import 'jest-styled-components';
import {
    addFieldToState,
    applyActionMocks,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';

import { RadioComponent, ConnectedRadioComponent } from '../radio-component';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import type { RadioComponentProps } from '../radio-types';
import React from 'react';
import { FieldKey } from '../../../types';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../../../../redux';
import * as i18nService from '../../../../service/i18n-service';
import { Provider } from 'react-redux';
import * as actions from '../../../../redux/actions';

import * as stateUtils from '../../../../utils/state-utils';

jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() => getMockPageDefinition('screen-id'));

describe('Radio component', () => {
    let radioComponentProps: RadioComponentProps;
    beforeEach(() => {
        radioComponentProps = {
            elementId: 'test-radio',
            screenId: 'screen-id',
            locale: 'en-US',
            fieldProperties: {
                title: 'title radio',
                options: ['option 1', 'option 2', 'option 3'],
            },
            onFocus: jest.fn(),
            setFieldValue: jest.fn().mockResolvedValue(undefined),
            validate: jest.fn(),
            removeNonNestedErrors: jest.fn(),
        };
    });

    it('should render 3 radio buttons with fieldProperties.options values', async () => {
        render(<RadioComponent {...radioComponentProps} />);

        screen.getByRole('radiogroup');
        const radioButtons = screen.getAllByRole('radio') as HTMLInputElement[];
        expect(radioButtons.length).toBe(3);
        expect(radioButtons[0].value).toBe('option 1');
        expect(radioButtons[1].value).toBe('option 2');
        expect(radioButtons[2].value).toBe('option 3');
    });

    it('set field value should be fired upon clicking a radio button', () => {
        const mockSetFieldValue = jest.fn().mockResolvedValue(undefined);
        radioComponentProps.setFieldValue = mockSetFieldValue;
        render(<RadioComponent {...radioComponentProps} />);

        const radioButtons = screen.getAllByRole('radio') as HTMLInputElement[];

        radioButtons[0].click();
        expect(mockSetFieldValue).toHaveBeenCalledWith('test-radio', 'option 1');

        radioButtons[1].click();
        expect(mockSetFieldValue).toHaveBeenCalledWith('test-radio', 'option 2');

        radioButtons[2].click();
        expect(mockSetFieldValue).toHaveBeenCalledWith('test-radio', 'option 3');
    });

    it('Should render with title radio', () => {
        render(<RadioComponent {...radioComponentProps} />);
        screen.getByText('title radio');
    });

    it('Snapshot', () => {
        const wrapper = render(<RadioComponent {...radioComponentProps} />);
        expect(wrapper.baseElement).toMatchSnapshot();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;
        let localizeSpy: jest.SpyInstance<string, [string, string]> | null = null;

        const screenId = 'TestPage';
        const fieldIdWithOptionType = 'myTestOptionTypeRadioButton';
        const fieldIdWithOptionTypeSorted = 'myTestOptionTypeRadioButtonSorted';
        const fieldIdWithCallbackOptions = 'myTestCallbackOptionsTypeRadioButton';
        const fieldIdWithOptions = 'myTestRadioButton';
        const fieldIdWithOptionsSorted = 'myTestRadioButtonSorted';
        const fieldIdWithOptionsMapped = 'fieldIdWithOptionsMapped';

        beforeEach(() => {
            const setFieldValueMockImpl = jest.fn(() => () => Promise.resolve());
            jest.spyOn(xtremRedux.actions, 'setFieldValue').mockImplementation(setFieldValueMockImpl);

            localizeSpy = jest
                .spyOn(i18nService, 'localizeEnumMember')
                .mockImplementation((_: any, enumName: string) => `${enumName} localized`);
            const state = getMockState();
            state.enumTypes.MyLocalizedEnum = ['option1', 'option2', 'option3'];
            state.enumTypes.MyLocalizedUnsortedEnum = ['option3', 'option2', 'option1'];
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);

            addFieldToState(
                FieldKey.Radio,
                state,
                screenId,
                fieldIdWithOptionType,
                {
                    title: 'Radio button with options from an enum',
                    optionType: '@sage/xtrem-test/MyLocalizedEnum',
                    isMandatory: true,
                },
                'item3',
            );

            addFieldToState(
                FieldKey.Radio,
                state,
                screenId,
                fieldIdWithOptionTypeSorted,
                {
                    title: 'Radio button with sorted options from an enum',
                    optionType: '@sage/xtrem-test/MyLocalizedUnsortedEnum',
                    isMandatory: true,
                    isSortedAlphabetically: true,
                },
                'item3',
            );

            addFieldToState(
                FieldKey.Radio,
                state,
                screenId,
                fieldIdWithOptions,
                {
                    title: 'Radio button with hardcoded options',
                    options: ['item1', 'item2', 'item3'],
                },
                'option3',
            );

            addFieldToState(
                FieldKey.Radio,
                state,
                screenId,
                fieldIdWithOptionsSorted,
                {
                    title: 'Radio button with sorted hardcoded options',
                    options: ['item3', 'item1', 'item2'],
                    isSortedAlphabetically: true,
                },
                'option3',
            );

            addFieldToState(
                FieldKey.Radio,
                state,
                screenId,
                fieldIdWithCallbackOptions,
                {
                    title: 'Radio button with hardcoded options',
                    options() {
                        return ['callback1', 'callback2', 'callback3'];
                    },
                },
                'option3',
            );

            addFieldToState(FieldKey.Toggle, state, screenId, fieldIdWithOptionsMapped, {
                title: 'Radio button with hardcoded and mapped value',
                options: ['item1', 'item2', 'item3'],
                map(value: string) {
                    if (value === 'item1') {
                        return 'mapped item 1';
                    }
                    return value;
                },
            });

            mockStore = getMockStore(state);
        });

        afterEach(() => {
            jest.clearAllMocks();
            applyActionMocks();
        });

        it('should render radio button with hardcoded options', () => {
            render(
                <Provider store={mockStore}>
                    <ConnectedRadioComponent screenId={screenId} elementId={fieldIdWithOptions} />
                </Provider>,
            );

            const radioButtonContainers = screen.getAllByRole('radio') as HTMLInputElement[];

            expect(radioButtonContainers).toHaveLength(3);
            expect(actions.setFieldValue).not.toHaveBeenCalled();

            expect(radioButtonContainers[0].value).toBe('item1');
            radioButtonContainers[0].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith('TestPage', 'myTestRadioButton', 'item1', true);

            expect(radioButtonContainers[1].value).toBe('item2');
            radioButtonContainers[1].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith('TestPage', 'myTestRadioButton', 'item2', true);

            expect(radioButtonContainers[2].value).toBe('item3');
            radioButtonContainers[2].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith('TestPage', 'myTestRadioButton', 'item3', true);
        });

        it('should render radio button with hardcoded, sorted options', () => {
            render(
                <Provider store={mockStore}>
                    <ConnectedRadioComponent screenId={screenId} elementId={fieldIdWithOptionsSorted} />
                </Provider>,
            );

            const radioButtonContainers = screen.getAllByRole('radio') as HTMLInputElement[];

            expect(radioButtonContainers).toHaveLength(3);
            expect(actions.setFieldValue).not.toHaveBeenCalled();

            expect(radioButtonContainers[0].value).toBe('item1');
            radioButtonContainers[0].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestRadioButtonSorted',
                'item1',
                true,
            );

            expect(radioButtonContainers[1].value).toBe('item2');
            radioButtonContainers[1].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestRadioButtonSorted',
                'item2',
                true,
            );

            expect(radioButtonContainers[2].value).toBe('item3');
            radioButtonContainers[2].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestRadioButtonSorted',
                'item3',
                true,
            );
        });

        it('should render radio button with title', () => {
            const element = render(
                <Provider store={mockStore}>
                    <ConnectedRadioComponent screenId={screenId} elementId={fieldIdWithOptions} />
                </Provider>,
            );

            const label = element.baseElement.querySelector('legend');
            expect(label).toHaveTextContent('Radio button with hardcoded options');
        });

        it('should render radio button with title if the field is mandatory', () => {
            const element = render(
                <Provider store={mockStore}>
                    <ConnectedRadioComponent screenId={screenId} elementId={fieldIdWithOptionType} />
                </Provider>,
            );

            const label = element.baseElement.querySelector('legend');
            expect(label).toHaveTextContent('Radio button with options from an enum *');
        });

        it('should render radio button with callback options', () => {
            render(
                <Provider store={mockStore}>
                    <ConnectedRadioComponent screenId={screenId} elementId={fieldIdWithCallbackOptions} />
                </Provider>,
            );

            const radioButtonContainers = screen.getAllByRole('radio') as HTMLInputElement[];

            expect(radioButtonContainers).toHaveLength(3);
            expect(actions.setFieldValue).not.toHaveBeenCalled();

            expect(radioButtonContainers[0].value).toBe('callback1');
            radioButtonContainers[0].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                screenId,
                fieldIdWithCallbackOptions,
                'callback1',
                true,
            );

            expect(radioButtonContainers[1].value).toBe('callback2');
            radioButtonContainers[1].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                screenId,
                fieldIdWithCallbackOptions,
                'callback2',
                true,
            );

            expect(radioButtonContainers[2].value).toBe('callback3');
            radioButtonContainers[2].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                screenId,
                fieldIdWithCallbackOptions,
                'callback3',
                true,
            );
        });

        it('should render radio button with option type and localize displayed labels', () => {
            expect(localizeSpy).not.toHaveBeenCalled();

            render(
                <Provider store={mockStore}>
                    <ConnectedRadioComponent screenId={screenId} elementId={fieldIdWithOptionType} />
                </Provider>,
            );

            expect(localizeSpy).toHaveBeenCalledTimes(3);

            const radioButtonContainers = screen.getAllByRole('radio') as HTMLInputElement[];
            const radioButtonLabels = screen.getByRole('group').getElementsByTagName('label');
            expect(radioButtonContainers).toHaveLength(3);
            expect(actions.setFieldValue).not.toHaveBeenCalled();

            expect(radioButtonLabels[0].textContent).toBe('option1 localized');
            radioButtonContainers[0].click();
            expect(xtremRedux.actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestOptionTypeRadioButton',
                'option1',
                true,
            );

            expect(radioButtonLabels[1].textContent).toBe('option2 localized');
            radioButtonContainers[1].click();
            expect(xtremRedux.actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestOptionTypeRadioButton',
                'option2',
                true,
            );

            expect(radioButtonLabels[2].textContent).toBe('option3 localized');
            radioButtonContainers[2].click();
            expect(xtremRedux.actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestOptionTypeRadioButton',
                'option3',
                true,
            );
        });
        it('should render radio button with option type and localize displayed labels when sorted', () => {
            expect(localizeSpy).not.toHaveBeenCalled();

            render(
                <Provider store={mockStore}>
                    <ConnectedRadioComponent screenId={screenId} elementId={fieldIdWithOptionTypeSorted} />
                </Provider>,
            );

            expect(localizeSpy).toHaveBeenCalledTimes(3);

            const radioButtonContainers = screen.getAllByRole('radio') as HTMLInputElement[];
            const radioButtonLabels = screen.getByRole('group').getElementsByTagName('label');
            expect(radioButtonContainers).toHaveLength(3);
            expect(actions.setFieldValue).not.toHaveBeenCalled();

            expect(radioButtonLabels[0].textContent).toBe('option1 localized');
            radioButtonContainers[0].click();
            expect(xtremRedux.actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestOptionTypeRadioButtonSorted',
                'option1',
                true,
            );

            expect(radioButtonLabels[1].textContent).toBe('option2 localized');
            radioButtonContainers[1].click();
            expect(xtremRedux.actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestOptionTypeRadioButtonSorted',
                'option2',
                true,
            );

            expect(radioButtonLabels[2].textContent).toBe('option3 localized');
            radioButtonContainers[2].click();
            expect(xtremRedux.actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestOptionTypeRadioButtonSorted',
                'option3',
                true,
            );
        });

        it('should render radio button with mapped values', () => {
            render(
                <Provider store={mockStore}>
                    <ConnectedRadioComponent screenId={screenId} elementId={fieldIdWithOptionsMapped} />
                </Provider>,
            );

            screen.getByRole('radiogroup');
            const radioButtons = screen.getAllByRole('radio') as HTMLInputElement[];
            const radioButtonMappedLabels = screen.getByRole('group').getElementsByTagName('label');

            expect(radioButtons.length).toBe(3);
            expect(radioButtons[0].value).toBe('item1');
            expect(radioButtonMappedLabels[0].textContent).toBe('mapped item 1');
            expect(radioButtons[1].value).toBe('item2');
            expect(radioButtons[2].value).toBe('item3');
        });

        describe('info and warning', () => {
            it('should render with an info message', async () => {
                radioComponentProps.fieldProperties.infoMessage = 'Info message!!';
                const { baseElement } = render(<RadioComponent {...radioComponentProps} />);

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an info message from a callback', async () => {
                radioComponentProps.fieldProperties.infoMessage = () => 'Info message!!';
                const { baseElement } = render(<RadioComponent {...radioComponentProps} />);

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an warning message', async () => {
                radioComponentProps.fieldProperties.warningMessage = 'Warning message!!';
                const { baseElement } = render(<RadioComponent {...radioComponentProps} />);

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with an warning message from a callback', async () => {
                radioComponentProps.fieldProperties.warningMessage = () => 'Warning message!!';
                const { baseElement } = render(<RadioComponent {...radioComponentProps} />);

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should prioritize warnings over info messages', () => {
                radioComponentProps.fieldProperties.warningMessage = () => 'Warning message!!';
                radioComponentProps.fieldProperties.infoMessage = () => 'Info message!!';
                const wrapper = render(<RadioComponent {...radioComponentProps} />);

                expect(wrapper.baseElement.querySelector('[data-element="info"]')).toBeNull();
                expect(wrapper.baseElement.querySelector('[data-element="warning"]')).not.toBeNull();
            });

            it('should prioritize validation errors over warnings and info messages', () => {
                radioComponentProps.fieldProperties.warningMessage = () => 'Warning message!!';
                radioComponentProps.fieldProperties.infoMessage = () => 'Info message!!';
                radioComponentProps.validationErrors = [
                    { elementId: fieldIdWithOptions, screenId, validationRule: 'isMandatory', message: 'Error' },
                ];
                const wrapper = render(<RadioComponent {...radioComponentProps} />);
                expect(wrapper.baseElement.querySelector('[data-element="info"]')).toBeNull();
                expect(wrapper.baseElement.querySelector('[data-element="warning"]')).toBeNull();
                expect(wrapper.baseElement.querySelector('[data-element="error"]')).not.toBeNull();
            });
        });
    });
});
