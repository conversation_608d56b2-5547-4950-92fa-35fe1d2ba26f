import type { Dict } from '@sage/xtrem-shared';
import { RadioButton, RadioButtonGroup } from 'carbon-react/esm/components/radio-button';
import React, { useRef } from 'react';
import { connect } from 'react-redux';
import type { XtremAppState } from '../../../redux';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { getDataTestIdAttribute } from '../../../utils/dom';
import { triggerFieldEvent } from '../../../utils/events';
import { useFocus } from '../../../utils/hooks/effects/use-focus';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { addOptionsAndLocalizationToProps } from '../../../utils/transformers';
import { getFieldIndicatorStatus, getLabelTitle, isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import { HelperText } from '../carbon-utility-components';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { CarbonRadioButtonGroupProps, CarbonRadioButtonProps, RadioComponentProps } from './radio-types';
import styled from 'styled-components';
import RadioButtonGroupStyle from 'carbon-react/esm/components/radio-button/radio-button-group/radio-button-group.style';
import CheckBoxStyle from 'carbon-react/esm/components/checkbox/checkbox.style';

type RadioAdditionalProps = RadioComponentProps & { localizedOptions?: Dict<string>; enumOptions?: string[] };

const StyledCarbonWrapper = styled(CarbonWrapper)`
    ${CheckBoxStyle} {
        &:not(:last-child) {
            margin-right: 72px;
        }
        display: inline-block;
    }
    ${RadioButtonGroupStyle} {
        display: block;
    }
`;

export function RadioComponent(props: RadioAdditionalProps): React.ReactElement {
    const componentRef = useRef(null);
    useFocus(componentRef, props.isInFocus, 'input');
    const title = getLabelTitle(props.screenId, props.fieldProperties, null) || '';
    const { error, warning, info } = getFieldIndicatorStatus(props);

    const radioButtonGroupProps: CarbonRadioButtonGroupProps = {
        groupName: props.elementId,
        label: title,
        name: props.elementId,
        legend: title,
        value: props.value,
        error,
        warning,
        info,
        onChange: evt =>
            handleChange(
                props.elementId,
                evt.currentTarget.value,
                props.setFieldValue,
                props.validate,
                triggerChangeListener(props.screenId, props.elementId),
            ),
    };

    let optionsToUse =
        props.enumOptions ||
        resolveByValue({
            propertyValue: props.fieldProperties.options,
            skipHexFormat: true,
            screenId: props.screenId,
            fieldValue: props.value,
            rowValue: null, // Nested radio buttons are not supported
        }) ||
        [];
    const isReadOnly =
        isFieldDisabled(props.screenId, props.fieldProperties, props.value, null) ||
        isFieldReadOnly(props.screenId, props.fieldProperties, props.value, null);

    if (props.fieldProperties.isSortedAlphabetically) {
        optionsToUse = [...optionsToUse].sort((a, b) => {
            const aLabel: string =
                props.localizedOptions && props.localizedOptions[a]
                    ? props.localizedOptions[a]
                    : props.fieldProperties.map?.apply({}, [a]) || a;
            const bLabel: string =
                props.localizedOptions && props.localizedOptions[b]
                    ? props.localizedOptions[b]
                    : props.fieldProperties.map?.apply({}, [b]) || b;
            return aLabel.localeCompare(bLabel);
        });
    }

    return (
        <StyledCarbonWrapper
            noReadOnlySupport={true}
            {...props}
            className="e-radio-field"
            componentName="radio"
            value={props.value}
            componentRef={componentRef}
        >
            <RadioButtonGroup
                data-testid={getDataTestIdAttribute('radio-component', 'radio-button-group', props.elementId)}
                {...radioButtonGroupProps}
            >
                {optionsToUse.map(option =>
                    renderRadioButton(
                        option,
                        props.elementId,
                        isReadOnly,
                        props.onFocus,
                        componentRef,
                        props.localizedOptions,
                        props.fieldProperties.map?.apply({}, [option]),
                    ),
                )}
            </RadioButtonGroup>
            <HelperText helperText={props.fieldProperties.helperText} />
        </StyledCarbonWrapper>
    );
}

const triggerChangeListener = (screenId: string, elementId: string) => (): void => {
    triggerFieldEvent(screenId, elementId, 'onChange');
};

const renderRadioButton = (
    value: string,
    elementId: string,
    disabled = false,
    onFocus: () => void,
    ref: React.MutableRefObject<null> | null,
    localizedOptions?: Dict<string>,
    mappedText?: string,
): React.ReactNode => {
    const label = localizedOptions && localizedOptions[value] ? localizedOptions[value] : mappedText || value;
    const carbonRadioProps: CarbonRadioButtonProps = {
        value,
        label,
        size: 'small',
        key: value,
        disabled,
    };
    return (
        <RadioButton
            ref={ref}
            onFocus={onFocus}
            className="e-radio-button-item"
            data-testid={getDataTestIdAttribute('radio-component', 'radio-button', elementId)}
            {...carbonRadioProps}
        />
    );
};

export const ConnectedRadioComponent = connect(
    (state: XtremAppState, externalProps: FieldComponentExternalProperties) =>
        addOptionsAndLocalizationToProps(state, mapStateToProps()(state, externalProps) as RadioComponentProps),
    mapDispatchToProps(),
)(RadioComponent);

export default ConnectedRadioComponent;
