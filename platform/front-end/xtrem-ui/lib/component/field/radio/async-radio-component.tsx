import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import type { RadioComponentProps } from './radio-types';

const ConnectedRadioComponent = React.lazy(() => import('./radio-component'));

export function AsyncConnectedRadioComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedRadioComponent {...props} />
        </React.Suspense>
    );
}

const RadioComponent = React.lazy(() => import('./radio-component').then(c => ({ default: c.RadioComponent })));

export function AsyncRadioComponent(props: RadioComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={true} bodyHeight="200px" />}>
            <RadioComponent {...props} />
        </React.Suspense>
    );
}
