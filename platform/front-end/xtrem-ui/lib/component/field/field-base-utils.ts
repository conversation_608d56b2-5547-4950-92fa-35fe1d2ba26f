import type { LocalizeLocale } from '@sage/xtrem-shared';
import { get, set } from 'lodash';
import * as xtremRedux from '../../redux';
import { runAndDispatchFieldValidation } from '../../service/dispatch-service';
import type { ScreenBase } from '../../service/screen-base';
import { getScreenElement } from '../../service/screen-base-definition';
import { xtremConsole } from '../../utils/console';
import type { UiComponentProperties } from '../abstract-ui-control-object';
import type { PageProperties } from '../control-objects';
import type { ReadonlyFieldProperties } from '../readonly-field-control-object';
import type {
    EditableFieldComponentProperties,
    FieldComponentExternalProperties,
    ReadonlyFieldComponentProperties,
} from './field-base-component-types';
import type { Clickable, Mappable } from './traits';

export const handleChange = (
    bind: string,
    value: any, // TODO Type this properly
    setFieldValue: (bind: string, value: any) => void,
    validate?: (bind: string, value: any) => Promise<string | undefined>,
    onChange?: () => void,
): void => {
    setFieldValue(bind, value);

    let validationPromise: Promise<string | undefined> = Promise.resolve(undefined);
    if (validate) {
        validationPromise = validate(bind, value);
    } else {
        xtremConsole.info(`Validation is being skipped on ${bind} since it does not provide a validate handler`);
    }

    if (onChange && validationPromise) {
        // eslint-disable-next-line no-console
        validationPromise.then(onChange).catch(xtremConsole.error);
    } else {
        xtremConsole.info(`Change event cannot be triggered on ${bind} since it does not provide a change handler`);
    }
};

const mapReadonlyStateToProps = () =>
    // eslint-disable-next-line func-names
    function (
        state: xtremRedux.XtremAppState,
        props: FieldComponentExternalProperties,
    ): ReadonlyFieldComponentProperties<UiComponentProperties, any> {
        const fieldProperties = state.screenDefinitions[props.screenId].metadata.uiComponentProperties[props.elementId];
        const screenElement = getScreenElement(state.screenDefinitions[props.screenId]);
        const fieldValue = state.screenDefinitions[props.screenId].values[props.elementId];
        const pageNode = (
            state.screenDefinitions[props.screenId].metadata.uiComponentProperties[props.screenId] as PageProperties
        ).node;

        const componentProperties: ReadonlyFieldComponentProperties<
            ReadonlyFieldProperties & Clickable<ScreenBase> & Mappable<ScreenBase>,
            any
        > = {
            value: fieldValue,
            browser: state.browser,
            nodeTypes: state.nodeTypes,
            pageNode,
            fieldProperties,
            locale: (state.applicationContext?.locale as LocalizeLocale) || 'base',
            isInFocus:
                !!state.focusPosition &&
                state.focusPosition.elementId === props.elementId &&
                state.focusPosition.screenId === props.screenId,
            onFocus: xtremRedux.actions.actionStub,
        };

        // TODO Define a Trait for headerActions
        /* eslint-disable */
        const headerActions = get<any, string>(fieldProperties, 'headerActions');
        if (typeof headerActions === 'function') {
            set(componentProperties.fieldProperties, 'headerActionsMap', headerActions.apply(screenElement));
        }
        /* eslint enable */

        return componentProperties;
    };

export const mapStateToProps = () =>
    // eslint-disable-next-line func-names
    function (
        state: xtremRedux.XtremAppState,
        props: FieldComponentExternalProperties,
    ): EditableFieldComponentProperties<UiComponentProperties, any> {
        return {
            ...mapReadonlyStateToProps()(state, props),
            validationErrors: state.screenDefinitions[props.screenId].errors[props.elementId],
            setFieldValue: xtremRedux.actions.actionStub,
            validate: xtremRedux.actions.actionStub,
            setFieldProperties: xtremRedux.actions.actionStub,
            removeNonNestedErrors: xtremRedux.actions.actionStub,
        };
    };

export const mapDispatchToProps = (
    callback?: (dispatch: xtremRedux.AppThunkDispatch, props: FieldComponentExternalProperties) => Partial<any>,
) =>
    // eslint-disable-next-line func-names
    function (
        dispatch: xtremRedux.AppThunkDispatch,
        props: FieldComponentExternalProperties,
    ): Partial<EditableFieldComponentProperties<UiComponentProperties, any>> {
        let dispatchToProps = {
            setFieldValue: (elementId: string, value: any) =>
                dispatch(xtremRedux.actions.setFieldValue(props.screenId, elementId, value, true)),
            onFocus: (row?: string, nestedField?: string) => {
                dispatch(xtremRedux.actions.setFocusPosition(props.screenId, props.elementId, row, nestedField));
            },
            validate: (elementId: string, value: any) =>
                runAndDispatchFieldValidation(props.screenId, elementId, value),
        };
        if (callback) {
            dispatchToProps = {
                ...dispatchToProps,
                ...callback(dispatch, props),
            };
        }
        return dispatchToProps;
    };
