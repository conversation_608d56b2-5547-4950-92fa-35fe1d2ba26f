import * as React from 'react';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { PodCollectionComponentProperties } from './pod-collection-types';

const ConnectedPodCollectionComponent = React.lazy(() => import('./pod-collection-component'));

export function AsyncConnectedPodCollectionComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="200px" />}>
            <ConnectedPodCollectionComponent {...props} />
        </React.Suspense>
    );
}

const PodCollectionComponent = React.lazy(() =>
    import('./pod-collection-component').then(c => ({ default: c.PodCollectionComponent })),
);

export function AsyncPodCollectionComponent(props: PodCollectionComponentProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={true} bodyHeight="200px" />}>
            <PodCollectionComponent {...props} />
        </React.Suspense>
    );
}
