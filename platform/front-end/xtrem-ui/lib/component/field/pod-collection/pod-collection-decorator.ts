/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import { addColumnsToProperties, addDisabledToProperties, addNodeToProperties } from '../../../utils/data-type-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { PodCollectionControlObject } from './pod-collection-control-object';
import type {
    PodCollectionDecoratorProperties,
    PodCollectionExtensionDecoratorProperties,
} from './pod-collection-types';

class PodCollectionDecorator extends AbstractFieldDecorator<FieldKey.PodCollection> {
    protected _controlObjectConstructor = PodCollectionControlObject;

    protected _layout = AbstractFieldLayoutBuilder;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<PodCollectionDecoratorProperties> {
        const properties: Partial<PodCollectionDecoratorProperties> = {};
        addNodeToProperties({ dataType, propertyDetails, properties });
        addColumnsToProperties({
            dataType,
            propertyDetails,
            properties,
            dataTypes: this.dataTypes,
            nodeTypes: this.nodeTypes,
        });
        addDisabledToProperties({
            propertyDetails,
            dataType,
            properties,
        });
        return properties;
    }
}

/**
 * Initializes the decorated member as a [PodCollection]{@link PodCollectionControlObject} field with the provided properties
 *
 * @param properties The properties that the [PodCollection]{@link PodCollectionControlObject} field will be initialized with
 */
export function podCollectionField<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: PodCollectionDecoratorProperties<Extend<T>, ReferencedItemType>,
): (target: T, name: string) => void {
    // Backwards support of the renamed rowAction property. In case of X3, they may deploy older platform with new app code. We should remove this around August 2023.
    if ((properties as any).rowActions) {
        // eslint-disable-next-line no-param-reassign
        properties = {
            ...properties,
            dropdownActions: (properties as any).rowActions,
        };
    }

    return standardDecoratorImplementation<T, FieldKey.PodCollection, ReferencedItemType>(
        properties,
        PodCollectionDecorator,
        FieldKey.PodCollection,
    );
}

export function podCollectionFieldOverride<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: PodCollectionExtensionDecoratorProperties<T, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.PodCollection, ReferencedItemType>(properties);
}
