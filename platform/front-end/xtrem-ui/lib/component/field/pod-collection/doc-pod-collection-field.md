PATH: XTREEM/UI+Field+Widgets/Pod+Collection

## Introduction

Pod collection fields are used to represent a data collection to the user in a non-tabular way using small block like components for each entry in the collection. It can be used and configured similarly to a pod collection.

## Example:

```ts
@ui.decorators.podCollectionField<PodCollection, ShowCaseInvoiceLine>({
    node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
    orderBy: {
        product: { _id: -1 },
    },
    parent() {
        return this.fieldBlock;
    },
    onClick() {

    },
    onChange() {

    },
    onRecordSelected(_id) {
        this.$.showToast(`Pod selected with ID ${_id}`, { type: 'success' });
    },
    onRecordUnselected(_id) {
        this.$.showToast(`Pod unselected with ID ${_id}`, { type: 'warning' });
    },
    onRecordAdded() {
        this.$.showToast(`A new pod was added`, { type: 'success' });
    },
    onRecordRemoved(_id) {
        this.$.showToast(`A pod was removed with ID: ${_id} :-(`, { type: 'maintenance' });
    },
    fetchesDefaults: true,
    recordTitle(value: any, rowValue: any) {
        return rowValue?.product?.product || 'Unknown';
    },
    dropdownActions: [
        {
            icon: 'add',
            title: 'Add',
            isDisabled() {
                return false;
            },
            onClick(recordId: any, data: any) {
                this.$.showToast(data.product, { type: 'info' });
            },
        }
    ],
    columns: [
        ui.nestedFields.reference<PodCollection>({
            bind: 'product',
            title: 'Product',
            isAutoSelectEnabled: true,
            node: '@sage/xtrem-show-case/ShowCaseProduct',
            valueField: 'product',
            fetchesDefaults: true,
            isFullWidth: true,
        }),
        ui.nestedFields.numeric({
            bind: 'orderQuantity', title: 'Ordered Quantity', scale: 0, fetchesDefaults: true,
            isFullWidth: true,
        }),
        ui.nestedFields.numeric({ bind: 'netPrice', title: 'Net Price', scale: 2, prefix: '€', width: 'small' }),
        ui.nestedFields.select({ bind: 'discountType', title: 'Discount Type', optionType: '@sage/xtrem-show-case/ShowCaseDiscountType' }),
    ],
})
lines: ui.fields.PodCollection;

```

### Display decorator properties

-   **columns**: A list of nested fields to be displayed as columns. The following types can be used as nested fields: 'checkbox', 'icon', 'image', 'label', 'link', 'numeric', 'progress', 'reference', 'text', 'date'.
-   **helperText**: The helper text that is displayed below the pods. It is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Whether all interactions (including field editing, selection, adding or removing pods) are disabled or not. Defaults to false.
-   **isReadonly**: Whether the pods are displayed in read-only mode. In this case no input fields are rendered
-   **isFullWidth**: Whether the pod collection spans all the parent width or not. It can also be defined as callback function that returns a boolean. Defaults to true.
-   **isHidden**: Whether the pod collection is hidden or not. Defaults to false.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
-   **orderBy**: The column or the set of columns which the pod collection should be sorted by.
-   **title**: The title that is displayed above the pod collection. It is automatically picked up by the i18n engine and externalized.
-   **recordTitle**: The title that is displayed in a single pod. It can be defined as a hardcoded string, a callback function that has access to the record data, or as a nested input-based field.
-   **recordWidth**: The size of individual pods using the block sizing convention
-   **canAddRecord**: Whether the add button is displayed. If set to true, an extra empty pod is rendered at the end of the list. Defaults to false
-   **canRemoveRecord**: Whether the remove icon is displayed on each pod on the top-right corner.
-   **canSelect**: Whether the rows of the pods can be selected or not. Defaults to false.
-   **dropdownActions**: A list of actions that are displayed in the top-right corner as a drop-down menu. See below under "Row action properties" how they can be configured.
-   **mapServerRecord(record)=>record**: Allows changing record values before those are rendered to the screen. This function is invoked whenever new entities are fetched from the server. The function is not allowed to modify the ID of the record or any other metadata property. The function may not be called when values are added by the functional code on runtime, only in case of server side communication.
- **headerLabel**: Label field rendered into the header of the pod in line with the title if defined.
-   **info**: Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function that returns a string.
-   **warning**: Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback function that returns a string.

### Binding decorator properties

-   **bind**: The GraphQL object's property that the pod collection value is bound to. If not provided, the pod collection name is used.
-   **filter**: Graphql filter that will restrict the records displayed in the pod collection. **Make sure that all properties used by filters have a corresponding column definition under the "*columns*" property.**
-   **isTransient**: If marked as true, the pod collection will be excluded from the automatic data binding process (i.e. no data will be fetched from the server). The default value is false.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **node**: The name of the node that the pod collection represents, it must be defined if you want to use the advanced filtering capabilities.

### Event handler decorator properties

-   **onClick**: Event triggered when any parts of the record is clicked, no arguments provided.
-   **onChange**: Event triggered when the record value changes.
-   **onRecordSelected**: Event triggered when a record row is selected.
-   **onRecordUnselected**: Event triggered when a record row is unselected.
-   **onRecordAdded**: Event triggered when the user adds a new record using the add button at the end of the pods.
-   **onRecordRemoved**: Event triggered when the user removes a pod using the cross icon.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

### Other decorator properties

-   **fetchesDefaults**: When set to true and when the pod collection value changes, a request to the server for default values for the whole page will be requested. False by default.

### Row action properties

-   **title**: The title of the action displayed in the a drop-down menu.
-   **icon**: An optional icon for the action, to be displayed alongside the action title.
-   **onClick**: Event triggered when an action is selected in the drop-down menu.
-   **isDisabled**: Whether the dropdown action is enabled (disabled = false) or not (disabled = true). It can also be defined as callback function that returns a boolean. It is enabled by default.
-   **isDestructive**: If set, the icon corresponding to the action is rendered in red.
-   If only one dropdown action is set, then only one button is displayed, otherwise a dropdown containing the available dropdown actions.

In addition to a dropdown action, a separator line can also added using `ui.menuSeparator({ id?: string, insertAfter?: string, insertBefore?: string })`.

## Runtime functions

-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
-   **refreshRecord(recordId, skipSet=false)**: Like refetch but applied only on a row level. If the skipSet flag is set to true, the value is returned but not applied to the table.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **redraw(columnBind?: string)**: Redraws the grid element on the screen and executes all property callback functions. If a `columnBind` property is passed, it only redraws that column.
-   **setRecordValue(rowValue: any)**: Updates a single record on the pod collection and keeps update history.
-   **getRecordByFieldValue(fieldName: keyof NestedRecordType, fieldValue: any)**: Find a records based on a property's value
-   **getRecordValue(recordId:string)**: Returns a single record. It can only return records that were previously loaded to the client side-cache by some other user interaction.
-   **addRecord(rowValue: any)**: Adds a single record to the pod collection and marks it as a new record.
-   **addOrUpdateRecordValue(rowValue: any)**: Add or update record in the pod collection depending of the existence of the ID field.
-   **removeRecord(recordId:string)**: Removes a single record from the displayed pod collection. It does NOT delete the corresponding server side entity.
-   **selectRecord(recordId:string)**: Selects a single record.
-   **generateRecordId()**: Returns an auto-generated ID that can be used for a new record.
-   **unselectRecord(recordId:string)**: Unselects a single record.
-   **unselectAllRecords()**: Unselects all selected records.
-   **validate(forceValidation:boolean)**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result. This validation will only trigger on dirty records. If the `forceValidation` argument is set to true, all records will be validated regardless of their dirty state.
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the record ID and colum ID. This validation will only trigger on dirty records. If the `forceValidation` argument is set to true, all records will be validated regardless of their dirty state.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

### Column properties

Please refer to the documentation of the corresponding nested field type.


## Adding columns in extension pages

Additional fields can be added to a pod collection in extension page artifacts using the `podCollectionFieldExtension` decorator. In this case, the `columns` decorator property accepts an `nestedFieldExtension` array. This set of nested fields comes with an additional decorator property which determines the position of the extension columns within the original pod collection. This property is called `insertBefore`.

### Positioning extension fields within the pod collection

The `insertBefore` decorator property determines the position of the extension field.
#### Not provided
If this decorator property is not provided, the extension field is added to the end of the columns.

#### Using the bind value
The position of the extension column can be set by providing the `bind` decorator property value of the base column that the field should be inserted before to the `insertBefore` extension column decorator property.

#### Base reference fields bound to the same property
When the base page contains reference field based columns that are bound to the same property, the position of the additional column can be set more precisely by including the `valueField` property value into the `insertBefore` extension property. This must be done by joining the two values by a double underscore in the following format: `bind__valueField`. For example if the field should be positioned before a reference field that is bound to `product` and the displayed value field is `description`, than the `insertBefore` extension property would be `product__description`.

### Example

```ts
import * as ui from '@sage/xtrem-ui';

...

export class PodCollectionExtension extends ui.PageExtension<PodCollection, GraphApi> {

    ...

    @ui.decorators.podCollectionFieldOverride<PodCollectionExtension, ShowCaseProduct>({
        title: 'Overridden Title',
        columns: [
            ui.nestedFieldExtensions.reference<PodCollectionExtension, ShowCaseProduct, BiodegradabilityCategory>({
                bind: 'biodegradabilityCategory',
                valueField: 'description',
                node: '@sage/xtrem-show-case-bundle/BiodegradabilityCategory',
                // This column is inserted before the `product` bound reference column that has `name` valueField configuration
                insertBefore: 'product__name',
                columns: [
                    ui.nestedFields.text({ bind: 'description', title: 'Description' }),
                    ui.nestedFields.text({ bind: 'energyToProduce', title: 'Energy to produce' })
                ],
                title: 'Biodegradability category',
                isAutoSelectEnabled: true,
            }),
            // This column goes to the end.
            ui.nestedFieldExtensions.text<PodCollectionExtension>({
                bind: 'someTransientFieldAtTheEnd',
                isTransient: true,
                title: 'Transient ext column',
            }),
            // This column goes to the end.
            ui.nestedFieldExtensions.text<PodCollectionExtension>({
                bind: 'someTransientFieldAtTheVeryEnd',
                isTransient: true,
                title: 'Transient ext column 2',
            }),
            // This column is inserted before the `provider` bound base column.
            ui.nestedFieldExtensions.numeric<PodCollectionExtension>({
                bind: 'someTransientFieldInTheMiddle',
                isTransient: true,
                title: 'Transient number',
                insertBefore: 'provider',
                scale: 5
            }),
        ]
    })
    field: ui.fields.PodCollection;
}
```
## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/PodCollection).
