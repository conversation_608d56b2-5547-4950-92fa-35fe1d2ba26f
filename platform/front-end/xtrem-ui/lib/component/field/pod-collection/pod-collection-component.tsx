import { CUSTOM_DATA_PROPERTY } from '@sage/xtrem-shared';
import { GridColumn, GridRow } from '@sage/xtrem-ui-components';
import Label from 'carbon-react/esm/__internal__/label';
import * as React from 'react';
import { connect } from 'react-redux';
import type { OnTelemetryEventFunction, XtremAppState } from '../../../redux/state';
import type { CollectionValue } from '../../../service/collection-data-service';
import { isCustomFieldReadOnly } from '../../../service/customization-service';
import { confirmationDialog } from '../../../service/dialog-service';
import { fetchNestedDefaultValues } from '../../../service/graphql-service';
import { localize } from '../../../service/i18n-service';
import type { AccessBindings } from '../../../service/page-definition';
import type { ScreenBase } from '../../../service/screen-base';
import type { NodePropertyType } from '../../../types';
import { calculateActionMenuWithSeparator } from '../../../utils/action-menu-utils';
import { triggerFieldEvent, triggerHandledEvent } from '../../../utils/events';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import { calculateContainerWidth } from '../../../utils/responsive-utils';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import { schemaTypeNameFromNodeName, splitValueToMergedValue } from '../../../utils/transformers';
import type { ValueOrCallbackWithFieldValue } from '../../../utils/types';
import type { UiComponentProperties } from '../../abstract-ui-control-object';
import { withCollectionValueAndOrderSubscription } from '../../connected-collection';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedField, NestedFieldTypes } from '../../nested-fields';
import { withoutNestedTechnical } from '../../nested-fields';
import type { CollectionItem, FieldKey } from '../../types';
import StatusIconWithPopover from '../../ui/icon/status-icon-with-popover';
import { ConnectedNestedBlock } from '../../ui/nested-block';
import { ConnectedNestedFieldErrors } from '../../ui/nested-field-errors-component';
import type {
    CollectionItemActionGroup,
    CollectionItemActionOrMenuSeparator,
} from '../../ui/table-shared/table-dropdown-actions/table-dropdown-action-types';
import type { XtremActionPopoverItemOrMenuSeparator } from '../../ui/xtrem-action-popover';
import { getFieldTitle, isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import { HelperText } from '../carbon-utility-components';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type {
    BaseEditableComponentProperties,
    EditableFieldComponentProperties,
    FieldComponentExternalProperties,
} from '../field-base-component-types';
import type { PodCollectionDecoratorProperties, PodCollectionProperties } from './pod-collection-types';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

export interface PodCollectionItemProps {
    _id: string;
    additionalTestId?: string;
    canRemoveRecord: boolean;
    canSelectRecord: boolean;
    dropdownActions?: Array<CollectionItemActionOrMenuSeparator<any> | CollectionItemActionGroup<any>>;
    elementId: string;
    getRowValue: () => CollectionItem;
    headerLabel?: NestedField<any, FieldKey.Label>;
    isDisabled: boolean;
    isReadOnly: boolean;
    isSelected: boolean;
    nestedFields: NestedField<ScreenBase, NestedFieldTypes>[];
    node?: NodePropertyType;
    onPodChange: (bind: string, value: any) => Promise<void>;
    onPodClick: () => void;
    onPodRemoved: () => Promise<void>;
    onPodSelectionChange: (isSelected: boolean) => void;
    onTelemetryEvent?: OnTelemetryEventFunction;
    podWidth: any;
    screenId: string;
    title?: NestedField<ScreenBase, NestedFieldTypes> | ValueOrCallbackWithFieldValue<any, string>;
}

export const PodCollectionItem: React.FC<PodCollectionItemProps> = React.memo((props: PodCollectionItemProps) => {
    const accessBindings = useDeepEqualSelector<XtremAppState, AccessBindings>(
        state => getPageDefinitionFromState(props.screenId, state)?.accessBindings || {},
    );

    const calculateDropdownActions = (): XtremActionPopoverItemOrMenuSeparator[] => {
        const rowValue = splitValueToMergedValue(props.getRowValue());

        return calculateActionMenuWithSeparator({
            accessBindings,
            actions: props.dropdownActions,
            actionType: 'pod-collection-action',
            onTriggerMenuItem: (context, onClick, onError) => {
                if (props.onTelemetryEvent) {
                    props.onTelemetryEvent(`podCollectionActionTriggered-${context.uniqueId}`, {
                        screenId: props.screenId,
                        elementId: props.elementId,
                        recordId: rowValue?._id,
                        id: context.id,
                        uniqueId: context.uniqueId,
                    });
                }

                triggerHandledEvent(
                    props.screenId,
                    props.elementId,
                    {
                        onClick,
                        onError,
                    },
                    rowValue?._id,
                    rowValue,
                );
            },
            rowValue,
            screenId: props.screenId,
        });
    };

    const [actionPopoverItems, setActionPopoverItems] =
        React.useState<XtremActionPopoverItemOrMenuSeparator[]>(calculateDropdownActions());

    const recalculateActionPopoverItems = (): void => {
        setActionPopoverItems(calculateDropdownActions());
    };

    return (
        <ConnectedNestedBlock
            actionPopoverItems={actionPopoverItems}
            additionalTestId={props.additionalTestId}
            availableColumns={props.podWidth}
            baseClassName="pod-collection"
            canSelect={props.canSelectRecord}
            contextNode={props.node}
            focusPosition={null}
            headerLabel={props.headerLabel}
            hideValidationSummary={true}
            isCloseIconDisplayed={props.canRemoveRecord}
            isDisabled={props.isDisabled}
            isReadOnly={props.isReadOnly}
            isSelected={props.isSelected}
            item={{ $containerId: props.elementId }}
            key={props._id}
            nestedFields={props.nestedFields}
            onActionPopoverOpen={recalculateActionPopoverItems}
            onBlockClick={props.onPodClick}
            onBlockRemoved={props.onPodRemoved}
            onBlockSelectionChange={props.onPodSelectionChange}
            onChange={props.onPodChange}
            parentElementId={props.elementId}
            recordId={props._id}
            screenId={props.screenId}
            title={props.title}
        />
    );
});

PodCollectionItem.displayName = 'PodCollectionItem';

interface PodCollectionState {
    invalidRecordIds: string[];
}
export class PodCollectionComponent extends EditableFieldBaseComponent<
    PodCollectionProperties,
    CollectionValue,
    {},
    PodCollectionState
> {
    constructor(props: BaseEditableComponentProperties<PodCollectionProperties, CollectionValue>) {
        super(props);
        this.state = {
            invalidRecordIds: [],
        };
    }

    shouldComponentUpdate(): boolean {
        // The component update is managed by the connected collection HOC, so we need to override the abstract method
        return true;
    }

    private onBlockChange = (recordId: string) => async (): Promise<void> => {
        const result = this.props.value?.getRawRecord({ id: recordId, cleanMetadata: true })!;

        triggerFieldEvent(
            this.props.screenId,
            this.props.elementId,
            'onChange',
            result._id,
            splitValueToMergedValue(result),
        );

        this.props.validate(this.props.elementId, this.props.value!);
    };

    private getRowValue = (recordId: string): CollectionItem =>
        this.props.value?.getRawRecord({ id: recordId, cleanMetadata: true })!;

    private getChildFields = (
        childFields?: NestedField<ScreenBase, NestedFieldTypes>[],
    ): NestedField<ScreenBase, NestedFieldTypes>[] => {
        const isDisabled = isFieldDisabled(
            this.props.screenId,
            this.props.fieldProperties,
            this.props.value || [],
            null,
        );
        const isReadOnly = isFieldReadOnly(
            this.props.screenId,
            this.props.fieldProperties,
            this.props.value || [],
            null,
        );

        return withoutNestedTechnical(childFields).map(f => {
            const bind = convertDeepBindToPathNotNull(f.properties.bind);
            const customReadOnly =
                this.props.nodeTypes &&
                bind.includes(CUSTOM_DATA_PROPERTY) &&
                isCustomFieldReadOnly(
                    this.props.nodeTypes,
                    schemaTypeNameFromNodeName(this.props.pageNode) || '',
                    this.props.fieldProperties.bind || this.props.elementId,
                );

            return {
                ...f,
                properties: {
                    ...f.properties,
                    isDisabled: isDisabled || f.properties.isDisabled,
                    isReadOnly: customReadOnly || isReadOnly || (f.properties as EditableFieldProperties).isReadOnly,
                },
            };
        });
    };

    private onPodAdded = async (): Promise<void> => {
        const defaultValues = await fetchNestedDefaultValues({
            screenId: this.props.screenId,
            isNewRow: true,
            elementId: this.props.elementId,
        });
        const result = this.props.value?.addRecord({ recordData: defaultValues.nestedDefaults || {} })!;

        await triggerFieldEvent(
            this.props.screenId,
            this.props.elementId,
            'onRecordAdded',
            result._id,
            splitValueToMergedValue(result),
        );

        this.props.validate(this.props.elementId, this.props.value!);
    };

    private onPodRemoved = (recordId: string) => async (): Promise<void> => {
        const fieldProperties = this.props.fieldProperties as PodCollectionDecoratorProperties;
        const removeDialogText =
            fieldProperties.removeDialogText ||
            localize('@sage/xtrem-ui/pod-collection-remove-text', 'You are about to remove this item.');
        const removeDialogTitle =
            fieldProperties.removeDialogTitle ||
            localize('@sage/xtrem-ui/pod-collection-remove-title', 'Removing Item');

        const result = this.getRowValue(recordId);
        try {
            await confirmationDialog(this.props.screenId, 'warn', removeDialogTitle, removeDialogText, {
                acceptButton: {
                    text: localize('@sage/xtrem-ui/pod-collection-remove-button', 'Remove'),
                },
                cancelButton: {
                    text: localize('@sage/xtrem-ui/pod-collection-cancel-button', 'Cancel'),
                },
            });
            this.props.value?.removeRecord({ recordId });
            await triggerFieldEvent(
                this.props.screenId,
                this.props.elementId,
                'onRecordRemoved',
                result._id,
                splitValueToMergedValue(result),
            );
            this.props.validate(this.props.elementId, this.props.value!);
        } catch {
            // Intentionally left empty
        }
    };

    private readonly onPodClick = (recordId: string) => (): void => {
        const result = this.getRowValue(recordId)!;

        triggerFieldEvent(
            this.props.screenId,
            this.props.elementId,
            'onRecordClick',
            result._id,
            splitValueToMergedValue(result),
        );
    };

    private readonly onPodSelectionChange = (recordId: string) => (): void => {
        const { setFieldProperties, value, screenId, elementId } = this.props;

        if (setFieldProperties && value) {
            const record = splitValueToMergedValue(this.getRowValue(recordId));

            const selectedRecords = [...(this.props.fieldProperties.selectedRecords || [])];
            const currentPosition = selectedRecords.indexOf(recordId);
            const selected = currentPosition >= 0;
            if (!selected) {
                selectedRecords.push(recordId);
                setFieldProperties(elementId, {
                    ...this.props.fieldProperties,
                    selectedRecords,
                });
                triggerFieldEvent(screenId, elementId, 'onRowSelected', recordId, record);
            } else {
                selectedRecords.splice(currentPosition, 1);
                setFieldProperties(elementId, {
                    ...this.props.fieldProperties,
                    selectedRecords,
                });
                triggerFieldEvent(screenId, elementId, 'onRowUnselected', recordId, record);
            }
        }
    };

    private readonly isItemSelected = (recordId: string): boolean =>
        this.props.fieldProperties.selectedRecords
            ? this.props.fieldProperties.selectedRecords.indexOf(recordId) !== -1
            : false;

    getTooltipContent = (): React.ReactNode => {
        if (!this.props.value) {
            return null;
        }
        return <ConnectedNestedFieldErrors screenId={this.props.screenId} elementId={this.props.elementId} />;
    };

    filterErrors = (): void => {
        if (this.props.value) {
            const invalidRecordIds = this.props.value.getAllInvalidRecords().map(r => r._id);
            this.setState({ invalidRecordIds });
        }
    };

    unFilterErrors = (): void => {
        this.setState({ invalidRecordIds: [] });
    };

    render(): React.ReactNode {
        const fieldProperties = this.props.fieldProperties as PodCollectionDecoratorProperties;
        const availableColumns = this.props.availableColumns || 12;

        const items = this.props.value
            ? this.state.invalidRecordIds.length > 0
                ? this.props.value?.getData({ where: { _id: { $in: this.state.invalidRecordIds } } })
                : this.props.value?.getData({ limit: fieldProperties.pageSize })
            : [];

        const podWidth = calculateContainerWidth(
            this.props.browser!.is,
            availableColumns,
            fieldProperties.recordWidth || 'small',
        );

        const needsTitleLine =
            (this.props.fieldProperties.title && !this.props.fieldProperties.isTitleHidden) ||
            (this.props.validationErrors && this.props.validationErrors.length > 0) ||
            this.state.invalidRecordIds.length > 0;

        return (
            <div
                {...this.getBaseAttributesDivWrapper(
                    'pod-collection',
                    'e-pod-collection-field',
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    this.props.isNested,
                )}
            >
                {needsTitleLine && (
                    <div className="e-field-title">
                        <Label htmlFor={undefined as unknown as string}>
                            {getFieldTitle(this.props.screenId, this.props.fieldProperties, null)}
                        </Label>
                        <StatusIconWithPopover
                            screenId={this.props.screenId}
                            validationErrors={this.props.validationErrors || []}
                            content={this.getTooltipContent()}
                            filterErrors={this.filterErrors}
                            unFilterErrors={this.unFilterErrors}
                            warningMessage={this.props.fieldProperties.warningMessage}
                            infoMessage={this.props.fieldProperties.infoMessage}
                        />
                    </div>
                )}
                <GridRow columns={availableColumns} margin={0} gutter={16} verticalMargin={0}>
                    {items.map(({ _id }) => (
                        <PodCollectionItem
                            _id={_id}
                            additionalTestId={`e-pod-collection-item-${_id}`}
                            canRemoveRecord={!!fieldProperties.canRemoveRecord}
                            canSelectRecord={!!fieldProperties.canSelect}
                            dropdownActions={fieldProperties.dropdownActions}
                            elementId={this.props.elementId}
                            getRowValue={(): CollectionItem => this.getRowValue(_id)}
                            headerLabel={fieldProperties.headerLabel}
                            isDisabled={this.isDisabled()}
                            isReadOnly={this.isReadOnly()}
                            isSelected={this.isItemSelected(_id)}
                            key={_id}
                            nestedFields={this.getChildFields(fieldProperties.columns)}
                            node={fieldProperties.node}
                            onPodChange={this.onBlockChange(_id)}
                            onPodClick={this.onPodClick(_id)}
                            onPodRemoved={this.onPodRemoved(_id)}
                            onPodSelectionChange={this.onPodSelectionChange(_id)}
                            onTelemetryEvent={this.props.fieldProperties.onTelemetryEvent}
                            podWidth={podWidth}
                            screenId={this.props.screenId}
                            title={fieldProperties.recordTitle}
                        />
                    ))}
                    {fieldProperties.canAddRecord && (
                        <GridColumn
                            key="_addnew"
                            className="e-pod-collection e-pod-collection-add-new-container"
                            columnSpan={podWidth}
                        >
                            <ButtonMinor
                                data-testid="e-pod-collection-add-new"
                                buttonType="secondary"
                                iconType="plus"
                                onClick={this.onPodAdded}
                                disabled={fieldProperties.isDisabled as boolean}
                            >
                                {fieldProperties.addButtonText ||
                                    localize('@sage/xtrem-ui/pod-collection-add-new', 'Add an item')}
                            </ButtonMinor>
                        </GridColumn>
                    )}
                </GridRow>
                {this.props.fieldProperties.helperText && (
                    <HelperText helperText={this.props.fieldProperties.helperText} />
                )}
            </div>
        );
    }
}

const mapStateWithTelemetryToProps = () =>
    // eslint-disable-next-line func-names
    function (
        state: XtremAppState,
        props: FieldComponentExternalProperties,
    ): EditableFieldComponentProperties<PodCollectionProperties, any> {
        const componentProps = mapStateToProps()(state, props) as EditableFieldComponentProperties<
            UiComponentProperties,
            any
        >;

        return {
            ...componentProps,
            fieldProperties: {
                ...componentProps.fieldProperties,
                onTelemetryEvent: state.applicationContext?.onTelemetryEvent,
            },
        };
    };

export const ConnectedPodCollectionComponent = connect(
    mapStateWithTelemetryToProps(),
    mapDispatchToProps(),
)(withCollectionValueAndOrderSubscription(PodCollectionComponent));

export default ConnectedPodCollectionComponent;
