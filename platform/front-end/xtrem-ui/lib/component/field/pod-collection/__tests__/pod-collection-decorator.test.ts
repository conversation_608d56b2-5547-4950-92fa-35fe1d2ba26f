import { fail } from 'assert';
import * as nestedFields from '../../../nested-fields';
import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata } from '../../../../__tests__/test-helpers';
import type { NestedField, NestedFieldTypes } from '../../../nested-fields';
import type { PodCollectionDecoratorProperties } from '../pod-collection-types';
import { podCollectionField } from '../pod-collection-decorator';

describe('Pod Collection Decorator', () => {
    const fieldId = 'tableField';
    let pageMetadata: pageMetaData.PageMetadata;

    describe('mapping values', () => {
        let mappedComponentProperties: PodCollectionDecoratorProperties<ScreenBase>;
        beforeEach(() => {
            pageMetadata = getMockPageMetadata();
            jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
            podCollectionField({ columns: [] })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            mappedComponentProperties = pageMetadata.uiComponentProperties[
                fieldId
            ] as PodCollectionDecoratorProperties<ScreenBase>;
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('PodCollectionProperties -> should set default values when no component properties provided', () => {
            expect(mappedComponentProperties.bind).toBeUndefined();
            expect(mappedComponentProperties.canSelect).toBeUndefined();
            expect(mappedComponentProperties.fetchesDefaults).toBeUndefined();
            expect(mappedComponentProperties.helperText).toBeUndefined();
            expect(mappedComponentProperties.isFullWidth).toBe(false);
            expect(mappedComponentProperties.isHidden).toBe(false);
            expect(mappedComponentProperties.isHiddenDesktop).toBe(false);
            expect(mappedComponentProperties.isHiddenMobile).toBe(false);
            expect(mappedComponentProperties.isTransient).toBe(false);
            expect(mappedComponentProperties.onRecordAdded).toBeUndefined();
            expect(mappedComponentProperties.onRecordRemoved).toBeUndefined();
            expect(mappedComponentProperties.onRowSelected).toBeUndefined();
            expect(mappedComponentProperties.onRowUnselected).toBeUndefined();
            expect(mappedComponentProperties.onChange).toBeUndefined();
            expect(mappedComponentProperties.onChange).toBeUndefined();
            expect(mappedComponentProperties.onClick).toBeUndefined();
            expect(mappedComponentProperties.orderBy).toBeUndefined();
            expect(mappedComponentProperties.parent).toBeUndefined();
            expect(mappedComponentProperties.selectedRecords).toBeUndefined();
            expect(mappedComponentProperties.title).toBeUndefined();
        });

        describe('mapping mandatory props for nested fields', () => {
            const keys = [
                'Aggregate',
                'Checkbox',
                'Count',
                'Date',
                'DropdownList',
                'FilterSelect',
                'Icon',
                'Image',
                'Label',
                'Link',
                'MultiDropdown',
                'MultiReference',
                'Numeric',
                'Progress',
                'Reference',
                'Select',
                'Switch',
                'Text',
                'TextArea',
                'Technical',
            ];

            const mandatoryProps = {
                Aggregate: { bind: 'Aggregate' },
                Checkbox: { bind: 'Checkbox' },
                Count: { bind: 'Count' },
                Date: { bind: 'Date' },
                DropdownList: { bind: 'DropdownList' },
                FilterSelect: { bind: 'FilterSelect' },
                Icon: { bind: 'Icon' },
                Image: { bind: 'Image' },
                Label: { bind: 'Label' },
                Link: { bind: 'Link', page: 'Test page' },
                MultiDropdown: { bind: 'MultiDropdown' },
                MultiReference: { bind: 'MultiReference' },
                Numeric: { bind: 'Numeric' },
                Progress: { bind: 'Progress' },
                Reference: { bind: 'Reference' },
                Select: { bind: 'Select' },
                Switch: { bind: 'Switch' },
                Text: { bind: 'Text' },
                TextArea: { bind: 'TextArea' },
                Technical: { bind: 'Technical' },
            };

            const childFields: NestedField<Page, NestedFieldTypes>[] = keys.map(key => {
                const lowerCaseKey = key[0].toLowerCase() + key.slice(1);
                return nestedFields[lowerCaseKey]({ ...mandatoryProps[key] });
            });

            it('should set values when component properties provided', () => {
                expect(childFields.length).toEqual(keys.length);
                childFields.forEach(column => {
                    if (column.properties) {
                        expect(keys).toContain(column.properties.bind);
                    } else {
                        fail('Test failed: no properties in Column object');
                    }
                });
            });
        });
    });
});
