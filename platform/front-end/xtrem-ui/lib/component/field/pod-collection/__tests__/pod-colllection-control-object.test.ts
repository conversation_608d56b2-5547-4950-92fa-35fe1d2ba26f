import { CollectionValue } from '../../../../service/collection-data-service';
import * as graphQlService from '../../../../service/graphql-service';
import type { GraphQLFilter } from '../../../../service/graphql-utils';
import type { Page } from '../../../../service/page';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import { PodCollectionControlObject } from '../../../control-objects';
import type { PodCollectionDecoratorProperties } from '../pod-collection-types';
import * as nestedFields from '../../../nested-fields';
import type { NestedField, NestedFieldTypes } from '../../../nested-fields';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';
import { CollectionFieldTypes } from '../../../../service/collection-data-types';

type NodeType = {
    _id: string;
    field1: string;
};

describe('Pod collection Field', () => {
    const screenId = 'TestPage';
    const elementId = 'podCollectionTestElement';
    const podCollectionValue = [{ _id: 'testId' }];
    let value: CollectionValue;
    let podCollectionFieldControlObject: PodCollectionControlObject;

    const properties: PodCollectionDecoratorProperties = {
        title: 'TEST_FIELD_TITLE',
        isHidden: true,
        isDisabled: true,
        columns: [],
    };

    describe('properties and values', () => {
        beforeEach(() => {
            value = new CollectionValue({
                screenId,
                elementId,
                isTransient: false,
                hasNextPage: false,
                orderBy: [{}],
                columnDefinitions: [
                    [
                        nestedFields.text<any, any>({ bind: '_id' }),
                        nestedFields.text<any, any>({ bind: 'anyField' }),
                        nestedFields.numeric({ bind: 'someOtherField' }),
                    ],
                ],
                nodeTypes: {},
                nodes: ['@sage/xtrem-test/AnyNode'],
                filter: [undefined],
                initialValues: podCollectionValue,
                fieldType: CollectionFieldTypes.POD,
            });

            podCollectionFieldControlObject = createFieldControlObject<FieldKey.PodCollection>(
                FieldKey.PodCollection,
                screenId,
                PodCollectionControlObject,
                elementId,
                value,
                properties,
            );
        });

        it('getting field value', () => {
            expect(podCollectionFieldControlObject.value).toEqual(podCollectionValue);
        });

        describe('setting and getting updated properties', () => {
            const testPodCollectionProps = (
                podCollectionProp: string,
                podCollectionPropValue: string | boolean | Array<NestedField<Page, NestedFieldTypes> | string>,
            ) => {
                expect(podCollectionFieldControlObject[podCollectionProp]).not.toEqual(podCollectionPropValue);
                podCollectionFieldControlObject[podCollectionProp] = podCollectionPropValue;
                expect(podCollectionFieldControlObject[podCollectionProp]).toEqual(podCollectionPropValue);
            };

            it('should set the title', () => {
                testPodCollectionProps('title', 'Test Title');
            });

            it('should set nested field and its properties', () => {
                const testFixture = [
                    nestedFields.text<any, NodeType>({
                        bind: 'field1',
                        isHiddenMobile: true,
                        isHiddenDesktop: true,
                    }),
                ];
                testPodCollectionProps('columns', testFixture);
            });

            it('should set the canFilter columns option', () => {
                testPodCollectionProps('canFilter', true);
            });

            it('should set the canResizeColumns columns option', () => {
                testPodCollectionProps('canResizeColumns', true);
            });

            it('should set the canSelect pods option', () => {
                testPodCollectionProps('canSelect', true);
            });

            it('should set the selected items', () => {
                testPodCollectionProps('selectedRecords', ['1', '2', '3']);
            });

            it('should set the canUserHideColumns columns option', () => {
                testPodCollectionProps('canUserHideColumns', false);
            });

            it('should select an item', () => {
                expect(podCollectionFieldControlObject.selectedRecords).toEqual([]);
                podCollectionFieldControlObject.selectRecord('4');
                expect(podCollectionFieldControlObject.selectedRecords).toEqual(['4']);
                podCollectionFieldControlObject.selectRecord('5');
                expect(podCollectionFieldControlObject.selectedRecords).toEqual(['4', '5']);
                podCollectionFieldControlObject.selectRecord('5');
                expect(podCollectionFieldControlObject.selectedRecords).toEqual(['4', '5']);
            });

            it('should unselect an item', () => {
                expect(podCollectionFieldControlObject.selectedRecords).toEqual([]);
                podCollectionFieldControlObject.unselectRecord('3');
                expect(podCollectionFieldControlObject.selectedRecords).toEqual([]);
                podCollectionFieldControlObject.selectedRecords = ['2', '3', '4'];
                expect(podCollectionFieldControlObject.selectedRecords).toEqual(['2', '3', '4']);
                podCollectionFieldControlObject.unselectRecord('3');
                expect(podCollectionFieldControlObject.selectedRecords).toEqual(['2', '4']);
                podCollectionFieldControlObject.unselectRecord('3');
                expect(podCollectionFieldControlObject.selectedRecords).toEqual(['2', '4']);
                podCollectionFieldControlObject.unselectRecord('2');
                expect(podCollectionFieldControlObject.selectedRecords).toEqual(['4']);
            });

            it('should unselect all items', () => {
                podCollectionFieldControlObject.selectedRecords = ['2', '3', '4'];
                podCollectionFieldControlObject.unselectAllRecords();
                expect(podCollectionFieldControlObject.selectedRecords).toEqual([]);
            });
        });

        describe('get and set value of the pod collection value', () => {
            beforeEach(() => {
                value = new CollectionValue({
                    screenId,
                    elementId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                            nestedFields.numeric({ bind: 'someOtherField' }),
                        ],
                    ],
                    nodeTypes: {},
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', anyField: 'test', someOtherField: 4 },
                        { _id: '2', anyField: 'test string 2', someOtherField: -5 },
                        { _id: '3', anyField: 'test aasd', someOtherField: 32432 },
                    ],
                });

                podCollectionFieldControlObject = createFieldControlObject<FieldKey.PodCollection>(
                    FieldKey.PodCollection,
                    screenId,
                    PodCollectionControlObject,
                    elementId,
                    value,
                    properties,
                );
            });

            afterEach(() => {
                jest.resetAllMocks();
            });

            it('get value() should return all records without metadata', () => {
                const result = podCollectionFieldControlObject.value;
                expect(result[0].__action).toBeUndefined();
                expect(result[1].__action).toBeUndefined();
                expect(result[2].__action).toBeUndefined();
            });

            it('get value() should exclude deleted rows', () => {
                expect(podCollectionFieldControlObject.value).toHaveLength(3);
                podCollectionFieldControlObject.removeRecord('1');
                expect(podCollectionFieldControlObject.value).toHaveLength(2);
            });

            it('should get a pod value when the getRecordValue() is called', () => {
                const result = podCollectionFieldControlObject.getRecordValue('2')!;
                expect(result._id).toEqual('2');
                expect(result.anyField).toEqual('test string 2');
                expect(result.someOtherField).toEqual(-5);
                expect(Object.keys(result)).toEqual(['_id', 'anyField', 'someOtherField']);
            });

            it('getRecordByFieldValue() should return an object without any metadata if found', () => {
                const result = podCollectionFieldControlObject.getRecordByFieldValue('someOtherField', -5);
                expect(result!._id).toEqual('2');
                expect(Object.keys(result!)).toEqual(['_id', 'anyField', 'someOtherField']);
            });

            it('addRecord should set the pod collection value if it is empty', () => {
                podCollectionFieldControlObject = createFieldControlObject<FieldKey.PodCollection>(
                    FieldKey.PodCollection,
                    screenId,
                    PodCollectionControlObject,
                    elementId,
                    null,
                    properties,
                );

                expect(podCollectionFieldControlObject.value).toEqual([]);
                expect(podCollectionFieldControlObject.value).toHaveLength(0);
                const result = podCollectionFieldControlObject.addRecord({
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(result).toStrictEqual({
                    _id: '-1',
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(podCollectionFieldControlObject.value).toHaveLength(1);
            });

            it('addRecord should add record to the DB', () => {
                expect(podCollectionFieldControlObject.value).toHaveLength(3);
                const result = podCollectionFieldControlObject.addRecord({
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(result).toStrictEqual({
                    _id: '-1',
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(podCollectionFieldControlObject.value).toHaveLength(4);
            });

            it('getRecordValue should return addRecord value without any internal property', () => {
                podCollectionFieldControlObject.addRecord({
                    anyField: 'new record',
                    someOtherField: 123,
                });

                expect(podCollectionFieldControlObject.getRecordValue('-1')).toEqual({
                    anyField: 'new record',
                    someOtherField: 123,
                    _id: '-1',
                });
            });

            describe('addRecordWithDefaults', () => {
                let fetchNestedDefaultValuesMock;

                beforeEach(() => {
                    fetchNestedDefaultValuesMock = jest
                        .spyOn(graphQlService, 'fetchNestedDefaultValues')
                        .mockReturnValue(
                            Promise.resolve({
                                nestedDefaults: {
                                    anyField: 'new record',
                                    someOtherField: 123,
                                },
                            }),
                        );
                });

                it('addRecordWithDefaults should set the pod collection value if it is empty', async () => {
                    podCollectionFieldControlObject = createFieldControlObject<FieldKey.PodCollection>(
                        FieldKey.PodCollection,
                        screenId,
                        PodCollectionControlObject,
                        elementId,
                        null,
                        properties,
                    );

                    expect(podCollectionFieldControlObject.value).toEqual([]);
                    expect(podCollectionFieldControlObject.value).toHaveLength(0);
                    const result = await podCollectionFieldControlObject.addRecordWithDefaults();

                    expect(podCollectionFieldControlObject.value).toHaveLength(1);
                    expect(result).toStrictEqual({
                        _id: '-1',
                        anyField: 'new record',
                        someOtherField: 123,
                    });
                    expect(podCollectionFieldControlObject.value).toHaveLength(1);

                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledTimes(1);
                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledWith({ screenId, elementId });
                });

                it('addRecordWithDefaults should add record to the DB', async () => {
                    expect(podCollectionFieldControlObject.value).toHaveLength(3);
                    const result = await podCollectionFieldControlObject.addRecordWithDefaults();
                    expect(result).toStrictEqual({
                        _id: '-1',
                        anyField: 'new record',
                        someOtherField: 123,
                    });
                    expect(podCollectionFieldControlObject.value).toHaveLength(4);

                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledTimes(1);
                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledWith({ screenId, elementId });
                });

                it('getRecordValue should return addRecordWithDefaults value without any internal property', async () => {
                    await podCollectionFieldControlObject.addRecordWithDefaults();

                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledTimes(1);
                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledWith({ screenId, elementId });

                    expect(podCollectionFieldControlObject.getRecordValue('-1')).toEqual({
                        anyField: 'new record',
                        someOtherField: 123,
                        _id: '-1',
                    });
                });
            });

            it('addOrUpdateValue should set the pod collection value if it is empty', () => {
                podCollectionFieldControlObject = createFieldControlObject<FieldKey.PodCollection>(
                    FieldKey.PodCollection,
                    screenId,
                    PodCollectionControlObject,
                    elementId,
                    null,
                    properties,
                );

                expect(podCollectionFieldControlObject.value).toEqual([]);
                expect(podCollectionFieldControlObject.value).toHaveLength(0);
                const result = podCollectionFieldControlObject.addOrUpdateRecordValue({
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(podCollectionFieldControlObject.value).toHaveLength(1);
                expect(result._id).toEqual('-1');
                expect(podCollectionFieldControlObject.value).toHaveLength(1);
            });

            it('addOrUpdateValue should add record to the DB if it does not have an ID', () => {
                expect(podCollectionFieldControlObject.value).toHaveLength(3);
                const result = podCollectionFieldControlObject.addOrUpdateRecordValue({
                    anyField: 'new record',
                    someOtherField: 123,
                });

                expect(result._id).toEqual('-1');
                expect(podCollectionFieldControlObject.value).toHaveLength(4);
            });

            it('addOrUpdateValue should add record to the DB if its ID does not exist', () => {
                expect(podCollectionFieldControlObject.value).toHaveLength(3);
                const result = podCollectionFieldControlObject.addOrUpdateRecordValue({
                    anyField: 'new record',
                    someOtherField: 123,
                    _id: '123',
                });

                expect(result._id).toEqual('123');
                expect(podCollectionFieldControlObject.value).toHaveLength(4);
            });

            it('should return updated value without any internal property', () => {
                podCollectionFieldControlObject.addOrUpdateRecordValue({
                    anyField: 'updated record',
                    someOtherField: 123,
                    _id: '2',
                })!;

                expect(podCollectionFieldControlObject.getRecordValue('2')).toEqual({
                    anyField: 'updated record',
                    someOtherField: 123,
                    _id: '2',
                });
            });
        });

        describe('refreshRecord', () => {
            beforeEach(() => {
                jest.spyOn(value, 'refreshRecord').mockResolvedValue({ _id: 3, someField: 4 });
            });

            it("should call collection value's refreshRecord function", async () => {
                expect(value.refreshRecord).not.toHaveBeenCalled();
                const result = await podCollectionFieldControlObject.refreshRecord('4');
                expect(result).toEqual({ _id: 3, someField: 4 });
                expect(value.refreshRecord).toHaveBeenCalledWith({ recordId: '4', skipUpdate: false });
            });
        });
    });

    describe('GraphQl filter property', () => {
        let setUiComponentProperties: jest.Mock<void, [string, string, PodCollectionDecoratorProperties]>;
        let refresh: jest.Mock<Promise<FieldInternalValue<FieldKey.PodCollection>>>;
        let newProperties: PodCollectionDecoratorProperties;
        const executeTest = async (filter: GraphQLFilter | (() => GraphQLFilter)) => {
            podCollectionFieldControlObject.filter = filter;
            expect(setUiComponentProperties).toHaveBeenCalled();
            expect(refresh).toHaveBeenCalled();
            expect(newProperties.filter).toEqual(filter);
        };

        beforeEach(() => {
            setUiComponentProperties = jest.fn(
                (_screenId: string, _elementId: string, _value: PodCollectionDecoratorProperties) => {
                    newProperties = { ..._value };
                },
            );

            refresh = jest.fn();
            podCollectionFieldControlObject = createFieldControlObject<FieldKey.PodCollection>(
                FieldKey.PodCollection,
                screenId,
                PodCollectionControlObject,
                elementId,
                value,
                properties,
                { setUiComponentProperties, refresh },
            );
        });

        it('should update filter with GraphQL filter object', () => {
            executeTest({ description: { _regex: 'policy', _options: 'i' } });
        });

        it('should update filter with function', () => {
            executeTest(() => ({ description: { _regex: 'policy', _options: 'i' } }));
        });
    });
});
