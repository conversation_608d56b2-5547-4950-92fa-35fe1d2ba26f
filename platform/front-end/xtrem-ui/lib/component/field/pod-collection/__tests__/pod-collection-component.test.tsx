jest.mock('../../text/async-text-component');
jest.mock('../../date/async-date-component');
jest.mock('../../numeric/async-numeric-component');

import {
    addFieldToState,
    applyActionMocks,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    userEvent,
} from '../../../../__tests__/test-helpers';

import * as graphqlService from '../../../../service/graphql-service';
import * as React from 'react';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux/state';
import { CollectionValue } from '../../../../service/collection-data-service';
import * as nestedFields from '../../../nested-fields';
import * as dialogs from '../../../../service/dialog-service';
import { <PERSON><PERSON><PERSON> } from '../../../types';
import type { ScreenBase } from '../../../../service/screen-base';
import { render, fireEvent, waitFor, cleanup } from '@testing-library/react';
import { Provider } from 'react-redux';
import { ConnectedPodCollectionComponent } from '../pod-collection-component';
import { ContextType } from '../../../../types';
import type { PodCollectionDecoratorProperties } from '../pod-collection-types';
import { triggerHandledEvent, triggerFieldEvent } from '../../../../utils/events';
import { setFieldProperties } from '../../../../redux/actions';
import { CollectionFieldTypes } from '../../../../service/collection-data-types';
import baseTheme from 'carbon-react/esm/style/themes/sage';
import { ThemeProvider } from 'styled-components';
import '@testing-library/jest-dom';

jest.useFakeTimers();

describe('pod collection', () => {
    const screenId = 'TestPage';
    const elementId = 'testField';

    let state: XtremAppState;
    let store: MockStoreEnhanced<XtremAppState>;
    let defaultProperties: PodCollectionDecoratorProperties;
    let columns: nestedFields.NestedField<ScreenBase, nestedFields.NestedFieldTypes, any>[];
    let defaultValue: any[];
    let collectionValue: CollectionValue;
    let fetchDefaultsMock: jest.MockInstance<any, any>;
    let confirmationDialogMock: jest.MockInstance<any, any>;

    const renderComponent = (order: any = { _id: 1 }) => {
        collectionValue = new CollectionValue<any>({
            screenId,
            elementId,
            isTransient: false,
            hasNextPage: false,
            orderBy: [order],
            columnDefinitions: [columns],
            nodeTypes: {
                AnyNode: {
                    properties: {},
                    mutations: {},
                    name: 'AnyNode',
                    title: 'AnyNode',
                    packageName: '@sage/xtrem-test',
                },
            },
            nodes: ['@sage/xtrem-test/AnyNode'],
            filter: [{}],
            initialValues: defaultValue,
            fieldType: CollectionFieldTypes.POD,
            locale: 'en-US',
        });

        addFieldToState(FieldKey.PodCollection, state, screenId, elementId, defaultProperties, collectionValue);

        store = getMockStore(state);
        return render(
            <ThemeProvider theme={baseTheme}>
                <Provider store={store!}>
                    <ConnectedPodCollectionComponent
                        elementId={elementId}
                        screenId={screenId}
                        contextType={ContextType.page}
                    />
                </Provider>
            </ThemeProvider>,
        );
    };
    beforeEach(() => {
        (setFieldProperties as unknown as jest.MockInstance<any, any>).mockReturnValue({
            type: 'AnyAction',
            value: null,
        });
        fetchDefaultsMock = jest
            .spyOn(graphqlService, 'fetchNestedDefaultValues')
            .mockResolvedValue({ nestedDefaults: {} });
        confirmationDialogMock = jest.spyOn(dialogs, 'confirmationDialog').mockResolvedValue({});
        columns = [
            nestedFields.text({
                bind: 'field1',
                title: 'Test text field',
            }),
            nestedFields.numeric({
                bind: 'field2',
                title: 'Test numeric field',
                scale: 2,
            }),
            nestedFields.date({
                bind: { nested: { binding: true } },
                title: 'Test date field',
            }),
        ];
        defaultProperties = {
            title: 'test title',
            node: '@sage/xtrem-test/AnyNode',
            columns,
            recordTitle: 'hardcoded title',
        } as PodCollectionDecoratorProperties;
        defaultValue = [
            {
                _id: '1',
                field1: 'Value 1',
                field2: '2.34',
                titleBoundField: 'Hi',
                nested: {
                    binding: '2023-03-03',
                },
            },
            {
                _id: '2',
                field1: 'Value 2',
                field2: '3.45',
                titleBoundField: 'Hola',
                nested: {
                    binding: '2022-02-02',
                },
            },
        ];
        state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
    });

    afterEach(async () => {
        applyActionMocks();
        (triggerFieldEvent as unknown as jest.MockInstance<any, any>).mockReset();
        (setFieldProperties as unknown as jest.MockInstance<any, any>).mockReset();
        (triggerHandledEvent as unknown as jest.MockInstance<any, any>).mockReset();
        confirmationDialogMock.mockReset();
        fetchDefaultsMock.mockReset();
        await cleanup();
    });

    describe('rendering', () => {
        it('should render with a couple nested fields and hardcoded pod title', () => {
            const element = renderComponent();
            const podCollectionTitles = element.queryAllByTestId('e-pod-collection-title');
            expect(podCollectionTitles).toHaveLength(2);
            expect(podCollectionTitles[0]).toHaveTextContent('hardcoded title');
            expect(podCollectionTitles[1]).toHaveTextContent('hardcoded title');
        });

        it('should render with callback style pod title', () => {
            defaultProperties.recordTitle = (value: any, rowValue: any) => `TEST ${rowValue._id}`;
            const element = renderComponent();
            const podCollectionTitles = element.queryAllByTestId('e-pod-collection-title');
            expect(podCollectionTitles).toHaveLength(2);
            expect(podCollectionTitles[0]).toHaveTextContent('TEST 1');
            expect(podCollectionTitles[1]).toHaveTextContent('TEST 2');
        });

        it('should render with nested field based title', () => {
            defaultProperties.recordTitle = nestedFields.text({
                bind: 'titleBoundField',
                title: 'Test text field',
            });

            const element = renderComponent();
            const podCollectionTitles = element.queryAllByTestId('e-pod-collection-title');
            expect(podCollectionTitles[0]).toHaveTextContent('Hi');
            expect(podCollectionTitles[1]).toHaveTextContent('Hola');
        });

        it('should render with helper text', () => {
            defaultProperties.helperText = 'DEMO HELPER TEXT';
            const element = renderComponent();
            const helperTextElement = element.queryByTestId('e-field-helper-text');
            expect(helperTextElement).toHaveTextContent('DEMO HELPER TEXT');
        });

        it('should render with fields and their values', () => {
            const element = renderComponent();
            const textFields = element.queryAllByTestId('e-field-label-testTextField', { exact: false });
            expect(textFields).toHaveLength(2);
            expect(textFields[0].querySelector('input')!.value).toEqual('Value 1');
            expect(textFields[1].querySelector('input')!.value).toEqual('Value 2');

            const numericFields = element.queryAllByTestId('e-field-label-testNumericField', { exact: false });
            expect(numericFields).toHaveLength(2);
            expect(numericFields[0].querySelector('input')!.value).toEqual('2.34');
            expect(numericFields[1].querySelector('input')!.value).toEqual('3.45');
        });

        it('should disable all child fields if the pod is disabled', () => {
            defaultProperties.isDisabled = true;
            const element = renderComponent();
            const textFields = element.queryAllByTestId('e-field-label-testTextField', { exact: false });
            expect(textFields).toHaveLength(2);
            expect(textFields[0].querySelector('input')!.disabled).toEqual(true);
            expect(textFields[1].querySelector('input')!.disabled).toEqual(true);

            const numericFields = element.queryAllByTestId('e-field-label-testNumericField', { exact: false });
            expect(numericFields).toHaveLength(2);
            expect(numericFields[0].querySelector('input')!.disabled).toEqual(true);
            expect(numericFields[1].querySelector('input')!.disabled).toEqual(true);
        });

        it('should not render a field if it is hidden', () => {
            (columns[1].defaultUiProperties as any).isHidden = true;
            (columns[1].properties as any).isHidden = true;
            const element = renderComponent();
            const textFields = element.queryAllByTestId('e-field-label-testTextField', { exact: false });
            expect(textFields).toHaveLength(2);

            const numericFields = element.queryAllByTestId('e-field-label-testNumericField', { exact: false });
            expect(numericFields).toHaveLength(2);
            expect(numericFields[0]).toHaveClass('e-hidden');
            expect(numericFields[1]).toHaveClass('e-hidden');
        });

        it('should render field with a nested bind', () => {
            const element = renderComponent();
            const nestedDateFields = element.queryAllByTestId('e-field-label-testDateField', { exact: false });
            expect(nestedDateFields).toHaveLength(2);

            expect(nestedDateFields[0].querySelector('input')!.value).toEqual('03/03/2023');
            expect(nestedDateFields[1].querySelector('input')!.value).toEqual('02/02/2022');
        });

        it('should render with an info message', async () => {
            defaultProperties.infoMessage = 'Hi there';
            const { baseElement } = renderComponent();
            expect(baseElement.querySelector('[data-element="info"]')).not.toBeNull();
        });

        it('should render with an info message from a callback', async () => {
            defaultProperties.infoMessage = () => 'Hi there';
            const { baseElement } = renderComponent();
            expect(baseElement.querySelector('[data-element="info"]')).not.toBeNull();
        });

        it('should render with an warning message', async () => {
            defaultProperties.warningMessage = 'Hi there';
            const { baseElement } = renderComponent();
            expect(baseElement.querySelector('[data-element="warning"]')).not.toBeNull();
        });

        it('should render with an info message from a callback', async () => {
            defaultProperties.warningMessage = () => 'Hi there';
            const { baseElement } = renderComponent();
            expect(baseElement.querySelector('[data-element="warning"]')).not.toBeNull();
        });

        it('should prioritize warnings over info messages', async () => {
            defaultProperties.warningMessage = () => 'Hi there';
            defaultProperties.infoMessage = () => 'Hi there';
            const { baseElement } = renderComponent();
            expect(baseElement.querySelector('[data-element="warning"]')).not.toBeNull();
            expect(baseElement.querySelector('[data-element="info"]')).toBeNull();
        });

        it('should render with add new button using the default button label', () => {
            defaultProperties.canAddRecord = true;
            const element = renderComponent();
            expect(element.queryByTestId('e-pod-collection-add-new')!.textContent!.trim()).toEqual('Add an item');
        });

        it('should render with add new button using a customized button label', () => {
            defaultProperties.canAddRecord = true;
            defaultProperties.addButtonText = 'Customized add button text';
            const element = renderComponent();
            expect(element.queryByTestId('e-pod-collection-add-new')!.textContent!.trim()).toEqual(
                'Customized add button text',
            );
        });

        it('should render checkboxes and mark them ticked when they are selected', () => {
            defaultProperties.canSelect = true;
            defaultProperties.selectedRecords = ['1'];
            const element = renderComponent();
            const selectionCheckboxes = element.queryAllByTestId('e-pod-collection-select');
            expect(selectionCheckboxes).toHaveLength(2);
            expect(selectionCheckboxes[0].querySelector('input')!.checked).toEqual(true);
            expect(selectionCheckboxes[1].querySelector('input')!.checked).toEqual(false);
        });

        it('should not render the remove button by default', () => {
            const element = renderComponent();
            expect(element.queryByTestId('e-pod-collection-close')).toEqual(null);
        });

        it('should not render the add button by default', () => {
            const element = renderComponent();
            expect(element.queryByTestId('e-pod-collection-add-new')).toEqual(null);
        });

        it('should not render the the selection checkboxes by default', () => {
            const element = renderComponent();
            expect(element.queryByTestId('e-pod-collection-select')).toEqual(null);
        });
    });

    describe('interactions', () => {
        it('should add a new element to the collection value when a add button is clicked', async () => {
            const onPodAddedMock = jest.fn();
            defaultProperties.canAddRecord = true;
            defaultProperties.onRecordAdded = onPodAddedMock;
            const element = renderComponent();

            expect(collectionValue.getData()).toHaveLength(2);
            expect(fetchDefaultsMock!).not.toHaveBeenCalled();
            expect(triggerFieldEvent).not.toHaveBeenCalled();

            fireEvent.click(element.queryByTestId('e-pod-collection-add-new')!);

            await waitFor(() => collectionValue.getData().length === 3, {});
            expect(fetchDefaultsMock!).toHaveBeenCalledWith({
                elementId: 'testField',
                isNewRow: true,
                screenId: 'TestPage',
            });
            expect(collectionValue.getData()).toHaveLength(3);
            expect(triggerFieldEvent).toHaveBeenCalledWith('TestPage', 'testField', 'onRecordAdded', '-1', {
                _id: '-1',
            });
        });

        it('should remove an item when the cross clicked', async () => {
            const onPodRemovedMock = jest.fn();
            defaultProperties.canRemoveRecord = true;
            defaultProperties.onRecordRemoved = onPodRemovedMock;
            const element = renderComponent();

            expect(collectionValue.getData()).toHaveLength(2);
            expect(triggerFieldEvent).not.toHaveBeenCalled();
            expect(confirmationDialogMock).not.toHaveBeenCalled();

            fireEvent.click(element.queryAllByTestId('e-pod-collection-close')[1]);
            expect(confirmationDialogMock).toHaveBeenCalledWith(
                'TestPage',
                'warn',
                'Removing Item',
                'You are about to remove this item.',
                { acceptButton: { text: 'Remove' }, cancelButton: { text: 'Cancel' } },
            );

            await waitFor(() => collectionValue.getData().length === 1, {});
            expect(collectionValue.getData()).toHaveLength(1);
            expect(triggerFieldEvent).toHaveBeenCalledWith('TestPage', 'testField', 'onRecordRemoved', '2', {
                _id: '2',
                field1: 'Value 2',
                field2: 3.45,
                titleBoundField: 'Hola',
                nested: {
                    binding: '2022-02-02',
                },
            });
        });

        it('should trigger onRecordClick when the pod is clicked', async () => {
            const onRecordClick = jest.fn();
            defaultProperties.onRecordClick = onRecordClick;
            const element = renderComponent();

            expect(triggerFieldEvent).not.toHaveBeenCalled();

            fireEvent.click(element.queryAllByTestId('e-field-label-hardcodedTitle', { exact: false })[0]);
            expect(triggerFieldEvent).toHaveBeenCalledTimes(1);
            fireEvent.click(element.queryAllByTestId('e-field-label-hardcodedTitle', { exact: false })[1]);
            expect(triggerFieldEvent).toHaveBeenCalledTimes(2);

            expect(triggerFieldEvent).toHaveBeenNthCalledWith(1, screenId, elementId, 'onRecordClick', '1', {
                _id: '1',
                field1: 'Value 1',
                field2: 2.34,
                titleBoundField: 'Hi',
                nested: {
                    binding: '2023-03-03',
                },
            });
            expect(triggerFieldEvent).toHaveBeenNthCalledWith(2, screenId, elementId, 'onRecordClick', '2', {
                _id: '2',
                field1: 'Value 2',
                field2: 3.45,
                titleBoundField: 'Hola',
                nested: {
                    binding: '2022-02-02',
                },
            });
        });

        it('should display the remove confirmation with custom content', async () => {
            defaultProperties.canRemoveRecord = true;
            defaultProperties.removeDialogText = 'Custom dialog content text';
            defaultProperties.removeDialogTitle = 'Custom Title';
            const element = renderComponent();

            expect(confirmationDialogMock).not.toHaveBeenCalled();

            fireEvent.click(element.queryAllByTestId('e-pod-collection-close')[1]);

            expect(confirmationDialogMock).toHaveBeenCalledWith(
                'TestPage',
                'warn',
                'Custom Title',
                'Custom dialog content text',
                { acceptButton: { text: 'Remove' }, cancelButton: { text: 'Cancel' } },
            );
        });

        it('should reorder if the collection value changes result in a different order', async () => {
            const element = renderComponent({ field1: 1 });

            let textFields = element.queryAllByTestId('e-field-label-testTextField', { exact: false });
            expect(textFields).toHaveLength(2);
            expect(textFields[0].querySelector('input')!.value).toEqual('Value 1');
            expect(textFields[1].querySelector('input')!.value).toEqual('Value 2');
            collectionValue.addOrUpdateRecordValue({
                recordData: {
                    _id: '2',
                    field1: 'A Value 2',
                    field2: '3.45',
                    titleBoundField: 'Hola',
                    nested: {
                        binding: '2022-02-02',
                    },
                },
                shouldNotifySubscribers: true,
            });

            await waitFor(() => {
                textFields = element.queryAllByTestId('e-field-label-testTextField', { exact: false });
                element.queryAllByTestId('e-field-label-testTextField', { exact: false });
                expect(textFields).toHaveLength(2);
                expect(textFields[0].querySelector('input')!.value).toEqual('A Value 2');
                expect(textFields[1].querySelector('input')!.value).toEqual('Value 1');
            });
        });

        it('should mark item selected when the user clicks the selection checkbox', () => {
            const onItemSelectedMock = jest.fn();
            const onItemUnselectedMock = jest.fn();
            defaultProperties.canSelect = true;
            defaultProperties.onRowSelected = onItemSelectedMock;
            defaultProperties.onRowUnselected = onItemUnselectedMock;
            defaultProperties.removeDialogTitle = 'Custom Title';

            expect(triggerFieldEvent).not.toHaveBeenCalled();
            expect(setFieldProperties).not.toHaveBeenCalled();

            const element = renderComponent();
            const selectionCheckboxes = element.queryAllByTestId('e-pod-collection-select');
            fireEvent.click(selectionCheckboxes[1].querySelector('input')!, {
                target: { checked: true, value: 'checked' },
            });

            expect(setFieldProperties).toHaveBeenCalledWith(
                'TestPage',
                'testField',
                expect.objectContaining({ selectedRecords: ['2'] }),
            );
            expect(triggerFieldEvent).toHaveBeenCalledWith('TestPage', 'testField', 'onRowSelected', '2', {
                _id: '2',
                field1: 'Value 2',
                field2: 3.45,
                titleBoundField: 'Hola',
                nested: {
                    binding: '2022-02-02',
                },
            });
        });

        it('should mark item unselected when the user clicks the selection checkbox of an already selected item', () => {
            const onItemSelectedMock = jest.fn();
            const onItemUnselectedMock = jest.fn();
            defaultProperties.canSelect = true;
            defaultProperties.selectedRecords = ['2'];
            defaultProperties.onRowSelected = onItemSelectedMock;
            defaultProperties.onRowUnselected = onItemUnselectedMock;
            defaultProperties.removeDialogTitle = 'Custom Title';

            expect(triggerFieldEvent).not.toHaveBeenCalled();
            expect(setFieldProperties).not.toHaveBeenCalled();

            const element = renderComponent();
            const selectionCheckboxes = element.queryAllByTestId('e-pod-collection-select');
            fireEvent.click(selectionCheckboxes[1].querySelector('input')!, {
                target: { checked: true, value: 'checked' },
            });

            expect(setFieldProperties).toHaveBeenCalledWith(
                'TestPage',
                'testField',
                expect.objectContaining({ selectedRecords: [] }),
            );
            expect(triggerFieldEvent).toHaveBeenCalledWith('TestPage', 'testField', 'onRowUnselected', '2', {
                _id: '2',
                field1: 'Value 2',
                field2: 3.45,
                titleBoundField: 'Hola',
                nested: {
                    binding: '2022-02-02',
                },
            });
        });

        it('should update the collection value when the user modifies the fields', () => {
            const onChangeMock = jest.fn();
            defaultProperties.onChange = onChangeMock;
            const element = renderComponent();

            expect(triggerFieldEvent).not.toHaveBeenCalled();

            const textFields = element.queryAllByTestId('e-field-label-testTextField', { exact: false });

            expect(textFields).toHaveLength(2);

            const target = textFields[0].querySelector('input')!;
            const eventData = { target: { value: 'My new test value' } };
            fireEvent.change(target, eventData);
            fireEvent.blur(target, eventData);

            expect(collectionValue.getRawRecord({ id: '1', cleanMetadata: true })).toEqual({
                _id: '1',
                field1: 'My new test value',
                field2: 2.34,
                titleBoundField: 'Hi',
                nested: {
                    binding: '2023-03-03',
                },
            });

            expect(triggerFieldEvent).toHaveBeenCalledWith('TestPage', 'testField', 'onChange', '1', {
                _id: '1',
                field1: 'My new test value',
                field2: 2.34,
                titleBoundField: 'Hi',
                nested: {
                    binding: '2023-03-03',
                },
            });
        });

        describe('dropdown actions', () => {
            let action1mock: jest.Mock<any>;
            let action2mock: jest.Mock<any>;
            let isDisabled2mock: jest.Mock<any>;
            let isHidden1Mock: jest.Mock<any>;
            let isHidden2Mock: jest.Mock<any>;

            beforeEach(() => {
                action1mock = jest.fn();
                action2mock = jest.fn();
                isDisabled2mock = jest.fn().mockReturnValue(true);
                isHidden1Mock = jest.fn().mockReturnValue(false);
                isHidden2Mock = jest.fn().mockReturnValue(true);

                defaultProperties.dropdownActions = [
                    { icon: 'add', title: 'Test title', onClick: action1mock },
                    {
                        icon: 'add',
                        title: 'Test title',
                        onClick: action2mock,
                        isHidden: isHidden1Mock,
                        isDisabled: isDisabled2mock,
                    },
                    {
                        icon: 'add',
                        title: 'Test hidden',
                        onClick: action2mock,
                        isHidden: isHidden2Mock,
                        isDisabled: jest.fn().mockReturnValue(false),
                    },
                ];
            });

            it('should open the menu when the user clicks on the icon', async () => {
                const wrapper = renderComponent();
                expect(
                    wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
                ).toHaveLength(0);
                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(
                    wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
                ).toHaveLength(2);
            });

            it('should call the isDisabled callback on menu rendering', async () => {
                const wrapper = renderComponent();

                expect(isDisabled2mock).toHaveBeenCalledTimes(2);

                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(isDisabled2mock).toHaveBeenCalledTimes(4);
            });

            it('should not render if an item is hidden', async () => {
                const wrapper = renderComponent();
                expect(isHidden2Mock).toHaveBeenCalledTimes(2);

                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(isHidden2Mock).toHaveBeenCalledTimes(4);

                expect(
                    document.querySelectorAll('[data-component="action-popover"] button[type="button"]').length,
                ).toEqual(2);
            });

            it('should render conditionally displayed items', async () => {
                isHidden2Mock.mockReturnValue(false);

                const wrapper = renderComponent();

                expect(isHidden2Mock).toHaveBeenCalledTimes(2);

                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(
                    document.querySelectorAll('[data-component="action-popover"] button[type="button"]').length,
                ).toEqual(3);
            });

            it('should trigger the event listener when the user clicks on an item', async () => {
                const wrapper = renderComponent();

                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(action1mock).not.toHaveBeenCalled();

                await userEvent.click(
                    document.querySelectorAll('[data-component="action-popover"] button[type="button"]')[0],
                );
                expect(triggerHandledEvent).toHaveBeenCalledTimes(1);
                expect(triggerHandledEvent).toHaveBeenCalledWith(
                    screenId,
                    elementId,
                    expect.objectContaining({ onClick: expect.any(Function) }),
                    '1',
                    {
                        _id: '1',
                        field1: 'Value 1',
                        field2: 2.34,
                        titleBoundField: 'Hi',
                        nested: {
                            binding: '2023-03-03',
                        },
                    },
                );
            });

            it('should trigger the right event listener even if an item is hidden in front of the item in the menu', async () => {
                isHidden1Mock.mockReturnValue(true);
                isHidden2Mock.mockReturnValue(false);
                const wrapper = renderComponent();

                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(triggerHandledEvent).not.toHaveBeenCalled();
                await userEvent.click(
                    document.querySelectorAll('[data-component="action-popover"] button[type="button"]')[1],
                );

                expect(triggerHandledEvent).toHaveBeenCalledTimes(1);
                expect(triggerHandledEvent).toHaveBeenCalledWith(
                    screenId,
                    elementId,
                    expect.objectContaining({ onClick: expect.any(Function) }),
                    '1',
                    {
                        _id: '1',
                        field1: 'Value 1',
                        field2: 2.34,
                        titleBoundField: 'Hi',
                        nested: {
                            binding: '2023-03-03',
                        },
                    },
                );
            });

            it('should not trigger the event listener when the user clicks on a disabled item', async () => {
                const wrapper = renderComponent();
                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(action1mock).not.toHaveBeenCalled();
                const actionButton = document.querySelectorAll(
                    '[data-component="action-popover"] button[type="button"]',
                )[1];
                expect(actionButton).toHaveTextContent('Test title');
                expect(actionButton).toHaveAttribute('aria-disabled', 'true');
                await userEvent.click(actionButton);
                expect(action2mock).not.toHaveBeenCalled();
            });
        });
    });
});
