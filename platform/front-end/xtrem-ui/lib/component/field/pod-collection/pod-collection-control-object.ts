/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { NestedFieldTypes } from '../../nested-fields';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import type { FieldKey } from '../../types';
import { CollectionValueControlObject } from '../collection-value-field';
import type { PodCollectionProperties } from './pod-collection-types';

/**
 * [Field]{@link ReadonlyFieldControlObject} that allows the user to explicitly trigger some action
 */
export class PodCollectionControlObject<
    NestedRecordType extends ClientNode = any,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends CollectionValueControlObject<
    FieldKey.PodCollection,
    NestedRecordType,
    CT,
    NestedFieldTypes,
    PodCollectionProperties<CT, NestedRecordType>
> {
    @ControlObjectProperty<
        PodCollectionProperties<CT, NestedRecordType>,
        PodCollectionControlObject<NestedRecordType, CT>
    >()
    canAddRecord?: boolean;

    @ControlObjectProperty<
        PodCollectionProperties<CT, NestedRecordType>,
        PodCollectionControlObject<NestedRecordType, CT>
    >()
    canRemoveRecord?: boolean;

    @ControlObjectProperty<
        PodCollectionProperties<CT, NestedRecordType>,
        PodCollectionControlObject<NestedRecordType, CT>
    >()
    addButtonText?: string;

    @ControlObjectProperty<
        PodCollectionProperties<CT, NestedRecordType>,
        PodCollectionControlObject<NestedRecordType, CT>
    >()
    removeDialogTitle?: string;

    @ControlObjectProperty<
        PodCollectionProperties<CT, NestedRecordType>,
        PodCollectionControlObject<NestedRecordType, CT>
    >()
    removeDialogText?: string;

    /**
     * Whether the field is editable (isReadOnly = false) or not (isReadOnly = true)
     *
     * The difference with disabled is that isReadOnly suggests that the field is never editable
     * (e.g. the id field of an object)
     */
    @FieldControlObjectResolvedProperty<
        PodCollectionProperties<CT, NestedRecordType>,
        PodCollectionControlObject<NestedRecordType, CT>
    >()
    isReadOnly: boolean;

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }

    get node(): string {
        return String(this.getUiComponentProperty('node'));
    }
}
