.e-pod-collection-field {
    >.e-grid-row {
        grid-auto-rows: 1fr;
    }

    & .e-field-title {
        display: flex;
        padding-bottom: 10px;
    }

    .e-pod-collection {
        border: 1px solid var(--colorsUtilityMajor075);
        padding: 16px 0;
        background: var(--colorsYang100);
        border-radius: var(--borderRadius100);
        height: 100%;
        box-sizing: border-box;

        .e-pod-collection-header {
            padding-bottom: 8px;
        }

        .e-field-nested-no-input {
            padding: 0;
            text-align: left;
        }

        .e-pod-collection-title {
            margin: 0;
            padding: 0 16px;
            display: flex;

            [data-component="action-popover-button"] {
                margin: 0;
                display: flex;
            }

            .e-field-read-only.e-field-nested-no-input {
                font-size: 20px;
            }

            .e-pod-collection-title-text {
                display: inline-block;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
            }

            .e-pod-collection-header-label {
                flex: 1;
                margin-right: 8px;
                margin-left: 8px;
                height: 28px;
                max-height: 28px;
                line-height: 24px;
            }

            .e-pod-collection-header-label span[data-component="pill"] {
                margin-top: 0;
            }

            .common-input__label,
            .common-input__help-text {
                display: none;
            }

            div[data-component="action-popover-wrapper"] {
                justify-content: flex-end;
                display: flex;
                text-align: right;
                margin-left: auto;
                margin-right: 0;
            }

            .e-pod-collection-close {
                padding: 0;
                height: 24px;

                span {
                    margin-right: 0;
                    color: var(--colorsUtilityMajor300);
                }
            }

            .e-pod-collection-select {
                padding: 4px 4px 4px 0;

                &>div {
                    padding-top: 0;
                }
            }
        }

        &.e-pod-collection-add-new-container {
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .e-block .e-pod-collection-field .e-pod-collection {
        box-shadow: none;
    }
}
