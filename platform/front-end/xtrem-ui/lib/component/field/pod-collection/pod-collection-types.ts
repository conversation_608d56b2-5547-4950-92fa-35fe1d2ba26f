import type { ClientNode } from '@sage/xtrem-client';
import type { CollectionValue } from '../../../service/collection-data-service';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { NestedRecordId, ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import type { ValueOrCallbackWithFieldValue } from '../../../utils/types';
import type { SectionControlObject } from '../../container/container-control-objects';
import type { BlockControlObject } from '../../control-objects';
import type { NestedField, NestedFieldTypes, PodNestedFieldTypes } from '../../nested-fields';
import type { NestedOverrideField } from '../../nested-fields-overrides';
import type { <PERSON>tainerWidth, FieldControlObjectInstance } from '../../types';
import type { CollectionValueFieldProperties } from '../collection-value-field';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type {
    CollectionItemActionGroup,
    CollectionItemActionOrMenuSeparator,
} from '../../ui/table-shared/table-dropdown-actions/table-dropdown-action-types';
import type {
    CanBeReadOnly,
    Changeable,
    Clickable,
    ExtensionField,
    HasCollectionSelectionEventHandlers,
    HasCollectionSelectionEventHandlersAfter,
    HasColumns,
    HasHeaderLabel,
    HasParent,
    Validatable,
    VoidPromise,
} from '../traits';
import type { NestedExtensionField } from '../../nested-fields-extensions';
import type { OnTelemetryEventFunction } from '../../../redux/state';

export interface PodCollectionProperties<CT extends ScreenBase = ScreenBase, NestedRecordType extends ClientNode = any>
    extends CollectionValueFieldProperties<CT, NestedRecordType>,
        HasColumns<CT, NestedRecordType, PodNestedFieldTypes>,
        HasHeaderLabel<CT, NestedRecordType>,
        CanBeReadOnly<CT, NestedRecordType> {
    /** Whether the rows of the pods can be selected or not. Defaults to false. */
    canSelect?: boolean;

    /** Whether the add button is displayed. If set to true, an extra empty pod is rendered at the end of the list. Defaults to false */
    canAddRecord?: boolean;

    /** Whether the remove icon is displayed on each pod on the top-right corner. */
    canRemoveRecord?: boolean;

    addButtonText?: string;

    removeDialogTitle?: string;

    removeDialogText?: string;

    /** Selected rows identifiers */
    selectedRecords?: NestedRecordId[];

    /** Actions that are rendered at the end of the table as a drop-down menu */
    dropdownActions?: Array<
        CollectionItemActionOrMenuSeparator<CT, NestedRecordType> | CollectionItemActionGroup<CT, NestedRecordType>
    >;

    onTelemetryEvent?: OnTelemetryEventFunction;
}

export interface PodCollectionDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    NestedRecordType extends ClientNode = any,
> extends Omit<PodCollectionProperties<CT, NestedRecordType>, '_controlObjectType'>,
        Clickable<CT>,
        Changeable<CT>,
        Validatable<CT, NestedRecordType[]>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasCollectionSelectionEventHandlers<CT, NestedRecordType>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>> {
    onRecordAdded?: (this: CT, _id: NestedRecordId, data: NestedRecordType) => VoidPromise;
    onRecordClick?: (this: CT, _id: NestedRecordId, data: NestedRecordType) => VoidPromise;
    onRecordRemoved?: (this: CT, _id: NestedRecordId, data: NestedRecordType) => VoidPromise;
    recordWidth?: ContainerWidth;

    /** The title of the HTML element */
    recordTitle?: ValueOrCallbackWithFieldValue<CT, string> | NestedField<CT, NestedFieldTypes, NestedRecordType>;
}

type BasePodCollectionExtensionDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    ReferencedItemType extends ClientNode = any,
> = HasCollectionSelectionEventHandlersAfter<CT, ReferencedItemType> & {
    onRecordAddedAfter?: (this: CT, _id: NestedRecordId, data: ReferencedItemType) => VoidPromise;
    onRecordClickAfter?: (this: CT, _id: NestedRecordId, data: ReferencedItemType) => VoidPromise;
    onRecordRemovedAfter?: (this: CT, _id: NestedRecordId, data: ReferencedItemType) => VoidPromise;
};

export interface PodCollectionExtensionDecoratorProperties<
    CT extends ScreenExtension<CT>,
    ReferencedItemType extends ClientNode = any,
> extends BasePodCollectionExtensionDecoratorProperties<Extend<CT>, ReferencedItemType>,
        ChangeableOverrideDecoratorProperties<PodCollectionDecoratorProperties<Extend<CT>, ReferencedItemType>, CT> {
    columns?: NestedExtensionField<CT, PodNestedFieldTypes, ReferencedItemType>[];
    /** Allows overriding existing column properties in the base page's columns */
    columnOverrides?: NestedOverrideField<CT, NestedFieldTypes, ReferencedItemType>[];
}

export type PodCollectionComponentProperties = BaseEditableComponentProperties<
    PodCollectionProperties,
    CollectionValue
>;
