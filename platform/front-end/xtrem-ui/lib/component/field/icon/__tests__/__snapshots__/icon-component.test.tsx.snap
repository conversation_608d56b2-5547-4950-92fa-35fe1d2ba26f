// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Icon component connected Snapshots should render helperText 1`] = `
.c0 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c0::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

<div>
  <div
    class="e-field e-icon-field"
    data-label="Test icon"
    data-testid="e-icon-field e-field-label-testIcon e-field-bind-test-icon-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test icon
    </label>
    <div>
      <span
        class="c0 e-field-value"
        data-component="icon"
        data-element="android"
        data-role="icon"
        font-size="small"
        type="android"
      />
    </div>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
      This is a helper text
    </span>
  </div>
</div>
`;

exports[`Icon component connected Snapshots should render hidden 1`] = `
.c0 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c0::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

<div>
  <div
    class="e-field e-icon-field e-hidden"
    data-label="Test icon"
    data-testid="e-icon-field e-field-label-testIcon e-field-bind-test-icon-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test icon
    </label>
    <div>
      <span
        class="c0 e-field-value"
        data-component="icon"
        data-element="android"
        data-role="icon"
        font-size="small"
        type="android"
      />
    </div>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Icon component connected Snapshots should render with default properties 1`] = `
.c0 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c0::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

<div>
  <div
    class="e-field e-icon-field"
    data-label="Test icon"
    data-testid="e-icon-field e-field-label-testIcon e-field-bind-test-icon-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test icon
    </label>
    <div>
      <span
        class="c0 e-field-value"
        data-component="icon"
        data-element="android"
        data-role="icon"
        font-size="small"
        type="android"
      />
    </div>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Icon component connected Snapshots should render with the mapped value 1`] = `
.c0 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c0::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

<div>
  <div
    class="e-field e-icon-field"
    data-label="Test icon"
    data-testid="e-icon-field e-field-label-testIcon e-field-bind-test-empty-icon-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test icon
    </label>
    <div>
      <span
        class="c0 e-field-value"
        data-component="icon"
        data-element="icon-value"
        data-role="icon"
        font-size="small"
        type="icon-value"
      />
    </div>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Icon component connected Snapshots should render without value 1`] = `
.c0 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c0::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

<div>
  <div
    class="e-field e-icon-field"
    data-label="Test icon"
    data-testid="e-icon-field e-field-label-testIcon e-field-bind-test-empty-icon-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test icon
    </label>
    <div>
      <span
        class="c0 e-field-value"
        data-component="icon"
        data-element="none"
        data-role="icon"
        font-size="small"
        type="none"
      />
    </div>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;
