import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type * as xtremRedux from '../../../../redux';
import type { ScreenBase } from '../../../../service/screen-base';
import * as events from '../../../../utils/events';
import { FieldKey } from '../../../types';
import { ConnectedIconComponent, IconComponent } from '../icon-component';
import type { IconDecoratorProperties } from '../icon-types';
import { fireEvent, render } from '@testing-library/react';
import '@testing-library/jest-dom';

describe('Icon component', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: IconDecoratorProperties<ScreenBase>;

    beforeEach(() => {
        mockFieldProperties = {
            title: 'Test icon',
        };
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Icon, state, screenId, 'test-icon-field', mockFieldProperties, 'android');
            addFieldToState(FieldKey.Icon, state, screenId, 'test-empty-icon-field', mockFieldProperties, null);
            mockStore = getMockStore(state);
        });

        afterEach(() => {
            jest.resetAllMocks();
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedIconComponent screenId={screenId} elementId="test-icon-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with the mapped value', () => {
                mockFieldProperties.map = () => 'icon-value';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedIconComponent screenId={screenId} elementId="test-empty-icon-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedIconComponent screenId={screenId} elementId="test-icon-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedIconComponent screenId={screenId} elementId="test-icon-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render without value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedIconComponent screenId={screenId} elementId="test-empty-icon-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });
        });

        describe('Interactions', () => {
            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedIconComponent screenId={screenId} elementId="test-icon-field" />
                    </Provider>,
                );
                expect(container.querySelector('[data-element="help"]')).not.toBeNull();
                expect(container.querySelector('[data-element="help"]')).toHaveTextContent('This is a helper text');
            });

            it('should render with a title', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedIconComponent screenId={screenId} elementId="test-icon-field" />
                    </Provider>,
                );
                expect(container.querySelector('[data-element="label"]')).not.toBeNull();
                expect(container.querySelector('[data-element="label"]')).toHaveTextContent('Test icon');
            });

            it('should render with a hidden title', () => {
                mockFieldProperties.isTitleHidden = true;

                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedIconComponent screenId={screenId} elementId="test-icon-field" />
                    </Provider>,
                );
                expect(container.querySelector('[data-element="label"]')).toBeNull();
            });

            it('Should render the helper text empty', () => {
                mockFieldProperties.helperText = '';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedIconComponent screenId={screenId} elementId="test-icon-field" />
                    </Provider>,
                );
                expect(container.querySelector('[data-element="help"]')).not.toBeNull();
                expect(container.querySelector('[data-element="help"]')!.textContent).toEqual(' ');
            });

            it('should trigger the triggerAction redux action on click', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedIconComponent screenId={screenId} elementId="test-icon-field" />
                    </Provider>,
                );

                expect(events.triggerFieldEvent).not.toHaveBeenCalled();

                fireEvent.click(container.querySelector('span[data-component="icon"]')!);

                expect(events.triggerFieldEvent).toHaveBeenCalledTimes(1);
                expect(events.triggerFieldEvent).toHaveBeenCalledWith(screenId, 'test-icon-field', 'onClick');
            });
        });
    });

    describe('unconnected', () => {
        describe('Interactions', () => {
            it('should call the on click callback when the button is clicked', () => {
                const onClickMock = jest.spyOn(events, 'triggerFieldEvent').mockImplementation(jest.fn());
                const { container } = render(
                    <IconComponent
                        elementId="test-icon-field"
                        fieldProperties={mockFieldProperties}
                        value="analysis"
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                expect(onClickMock).not.toHaveBeenCalled();

                fireEvent.click(container.querySelector('span[data-component="icon"]')!);

                expect(onClickMock).toHaveBeenCalledTimes(1);
                expect(onClickMock).toHaveBeenCalledWith(screenId, 'test-icon-field', 'onClick');
            });
        });
    });
});
