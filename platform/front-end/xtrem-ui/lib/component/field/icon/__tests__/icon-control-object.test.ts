import { <PERSON><PERSON><PERSON> } from '../../../types';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import { IconControlObject } from '../../../control-objects';

describe('Icon Field', () => {
    const screenId = 'TestPage';
    let iconField: IconControlObject;

    beforeEach(() => {
        iconField = createFieldControlObject<FieldKey.Icon>(
            FieldKey.Icon,
            screenId,
            IconControlObject,
            'test',
            'android',
            {
                title: 'TEST_FIELD_TITLE',
                isHidden: true,
                isDisabled: true,
            },
        );
    });

    it('getting field value', () => {
        expect(iconField.value).toEqual('android');
    });
});
