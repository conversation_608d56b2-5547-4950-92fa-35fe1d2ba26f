import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { IconDecoratorProperties } from '../icon-types';
import { iconField } from '../icon-decorator';

describe('Icon decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;
    let onClickMock: jest.Mock;
    let mapFunc: (value?: any) => string;

    beforeEach(() => {
        fieldId = 'iconField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
        mapFunc = jest.fn().mockImplementation(() => 'test map_return');
        onClickMock = jest.fn();
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        it('should set default values when no component properties provided', () => {
            iconField({})({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties: IconDecoratorProperties<Page> =
                pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.map).toBeUndefined();
            expect(mappedComponentProperties.onClick).toBeUndefined();
        });

        it('should set values when component properties provided', () => {
            iconField({
                map: mapFunc,
                onClick: onClickMock,
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties: IconDecoratorProperties<Page> =
                pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.map).not.toBeUndefined();
            expect(mappedComponentProperties.map).toBe(mapFunc);
            expect(mappedComponentProperties.onClick).toBe(onClickMock);
        });

        it('should assign onClick handler', () => {
            testOnClickHandler(iconField, pageMetadata, fieldId);
        });
    });
});
