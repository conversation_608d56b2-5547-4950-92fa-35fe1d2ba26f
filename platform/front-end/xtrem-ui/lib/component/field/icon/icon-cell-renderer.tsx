import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import * as React from 'react';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import type { NestedIconProperties } from '../../nested-fields-properties';
import { Icon } from '../../ui/icon/icon-component';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';

export const IconCellRenderer: React.FC<CellParams<NestedIconProperties, IconType>> = React.memo(props => {
    const { fieldProperties, data: rowValue } = props;
    if (rowValue && props.value) {
        const screenId = props.colDef.context.screenId;
        const originalValue = props.data[props.colDef.field];
        const mappedValue = props.value;
        const isHidden = resolveByValue({
            fieldValue: originalValue,
            propertyValue: fieldProperties.isHidden,
            skipHexFormat: true,
            screenId,
            rowValue: splitValueToMergedValue(rowValue),
        });

        if (isHidden) {
            return null;
        }

        return (
            <fieldProperties.wrapper {...props}>
                <Icon
                    className={`e-field-value${fieldProperties.onClick ? ' e-icon-clickable' : ''}`}
                    // TODO: Type "color" and "backgroundColor" based on the field type
                    // The Carbon library only supports some values here, the functional developer has to be aware
                    bg={resolveByValue({
                        fieldValue: originalValue,
                        propertyValue: fieldProperties.backgroundColor,
                        screenId,
                        rowValue: splitValueToMergedValue(rowValue),
                    })}
                    color={resolveByValue({
                        fieldValue: originalValue,
                        propertyValue: fieldProperties.color,
                        screenId,
                        rowValue: splitValueToMergedValue(rowValue),
                    })}
                    bgShape={resolveByValue({
                        fieldValue: originalValue,
                        propertyValue: fieldProperties.backgroundShape,
                        screenId,
                        rowValue: splitValueToMergedValue(rowValue),
                    })}
                    type={mappedValue}
                />
            </fieldProperties.wrapper>
        );
    }
    return null;
});

IconCellRenderer.displayName = 'IconCellRenderer';
