import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { IconComponentProps } from './icon-types';

const ConnectedIconComponent = React.lazy(() => import('./icon-component'));

export function AsyncConnectedIconComponent(props: FieldComponentExternalProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <ConnectedIconComponent {...props} />
        </React.Suspense>
    );
}

const IconComponent = React.lazy(() => import('./icon-component').then(c => ({ default: c.IconComponent })));

export function AsyncIconComponent(props: IconComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader size="small" />}>
            <IconComponent {...props} />
        </React.Suspense>
    );
}
