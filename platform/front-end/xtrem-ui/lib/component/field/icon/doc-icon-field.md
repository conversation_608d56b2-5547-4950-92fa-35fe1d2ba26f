PATH: XTREEM/UI+Field+Widgets/Icon+Count+Field

## Introduction

The icon field represents an icon in a container of pages within the user interface. The icon field is also available as a nested and extended field.

## Example

```ts
@ui.decorators.iconField({
    helperText: 'Helper text displayed below the field.',
    isDisabled: false,
    isHelperTextHidden: false,
    isHidden: false,
    isTransient: true,
    size: 'medium',
    title: 'Icon Field',
    onClick() {
        console.log(`Do something when field is clicked.`);
    },
    parent() {
        return this.block;
    },
})
field: ui.fields.Icon;
```

```ts
@ui.decorators.iconField({
    bind: 'categoryIcon',
    title: 'Category',
    insertBefore() {
        return this.field;
    },
    parent() {
        return this.block;
    },
})
extended: ui.fields.Icon;
```

```ts
@ui.decorators.tableField<Page>({
    ...,
    columns: [
        ...,
        @ui.nestedFields.icon({
            bind: 'categoryIcon',
            title: 'Category',
        }),
    ],
})
table: ui.fields.Icon<Node>;
```

## Decorator Properties

### Display Properties

-   **helperText**: The helper text displayed below the field. This property is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.
-   **isHelperTextHidden**: Determines whether the field's helper text is displayed or not.
-   **isHidden**: Determines whether the field is displayed or not.
-   **isTitleHidden**: Determines whether the field's title is displayed or not.
-   **size**: Size the field should be rendered in. The options are "small", "medium" and "large".
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **color**: Color code in HEX format that is used to set the color of the icon. It can be defined as a string, or conditionally by a callback that returns a string.
-   **backgroundColor**: Color code in HEX format that is used to set the color of the icon background. It can be defined as a string, or conditionally by a callback that returns a string.

### Binding Properties

-   **bind**: Determines the associated GraphQL node's property the field's value will be bound to.
-   **isTransient**: Determines whether the field will be excluded from the automatic data binding process or not.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **map()**: Custom callback to transform the field's value. The new value and grid's row data (if applicable) is provided as arguments. If implemented, the callbacks return value will be used as the field's value.

### Event Handler Properties

-   **onClick**: Handler triggered when the field is clicked.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

#### Runtime Functions

-   **refresh()**: Refetches the field's values from the server and updates the user interface.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Icon).
