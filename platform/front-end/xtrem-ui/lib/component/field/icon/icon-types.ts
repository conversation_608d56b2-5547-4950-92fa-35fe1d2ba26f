import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { BaseReadonlyComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    BackgroundColorable,
    Colorable,
    Clickable,
    ExtensionField,
    HasParent,
    Mappable,
    Nested,
    NestedClickable,
    Sizable,
} from '../traits';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValueOrCallbackWithFieldValue } from '../../../utils/types';
import type { FontSize } from 'carbon-react/esm/components/icon/icon.style';
import type { FieldControlObjectInstance } from '../../types';
import type { BlockControlObject } from '../../container/block/block-control-object';
import type { ClientNode } from '@sage/xtrem-client';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';

export interface IconDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<IconProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        Mappable<CT>,
        Sizable,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>> {
    iconSize?: ValueOrCallbackWithFieldValue<CT, FontSize>;

    style?: ValueOrCallbackWithFieldValue<CT, { iconColor?: string; backgroundColor?: string }>;
}

export interface NestedIconProperties<CT extends ScreenBase = ScreenBase, NodeType extends ClientNode = any>
    extends Omit<IconProperties<CT>, 'bind'>,
        Mappable<CT>,
        Nested<NodeType>,
        NestedClickable<CT, NodeType>,
        Sizable {
    /** Display style of the pill element */
    style?: ValueOrCallbackWithFieldValue<CT, { iconColor?: string; backgroundColor?: string }, string, NodeType>;
}

export type IconBackgroundShape = 'circle' | 'rounded-rect' | 'square';

export type IconBackgroundSize = 'small' | 'medium' | 'large';

export interface IconProperties<CT extends ScreenBase = ScreenBase>
    extends ReadonlyFieldProperties<CT>,
        BackgroundColorable<CT>,
        Colorable<CT> {
    /** The background shape of the icon */
    backgroundShape?: ValueOrCallbackWithFieldValue<CT, IconBackgroundShape>;
    /** The size of the icon background */
    backgroundSize?: ValueOrCallbackWithFieldValue<CT, IconBackgroundSize>;
    /** The size of the icon */
    iconSize?: ValueOrCallbackWithFieldValue<CT, FontSize>;
}

export type IconComponentProps = BaseReadonlyComponentProperties<
    IconProperties,
    IconType,
    NestedFieldsAdditionalProperties
>;
