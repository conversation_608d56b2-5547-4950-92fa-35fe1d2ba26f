import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import * as React from 'react';
import { connect } from 'react-redux';
import { ContextType } from '../../../types';
import { Icon } from '../../ui/icon/icon-component';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { mapReadonlyStateToProps, ReadonlyFieldBaseComponent } from '../field-base-component';
import type { NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { IconDecoratorProperties } from './icon-types';

export class IconComponent extends ReadonlyFieldBaseComponent<
    IconDecoratorProperties,
    IconType,
    NestedFieldsAdditionalProperties
> {
    getIconElement(): React.ReactNode {
        const classNames = ['e-field-value'];
        const title = this.getTitle() || '';

        if (this.props.fieldProperties.onClick) {
            classNames.push('e-icon-clickable');
        }

        const style = this.getResolvedProperty('style', true) as
            | { iconColor?: string; backgroundColor?: string }
            | undefined;

        return (
            <Icon
                className={classNames.join(' ')}
                // TODO: Type "color" and "backgroundColor" based on the field type
                // The Carbon library only supports some values here, the functional developer has to be aware
                bg={style?.backgroundColor ?? this.getResolvedProperty('backgroundColor', false)}
                bgShape={this.getResolvedProperty('backgroundShape', true)}
                bgSize={this.getResolvedProperty('backgroundSize', true)}
                color={style?.iconColor ?? this.getResolvedProperty('color', false)}
                fontSize={this.getResolvedProperty('iconSize', true) || 'small'}
                onClick={this.getClickHandler()}
                type={this.getValue() || 'none'}
                aria-label={title}
                tooltipMessage={this.props.fieldProperties.onClick ? title : undefined}
            />
        );
    }

    render(): React.ReactNode {
        const { isTitleHidden, isHelperTextHidden } = this.props.fieldProperties;
        const isFieldLabelHidden =
            !!isTitleHidden || (this.props.isNested && !this.props.shouldRenderLabelInNestedReadOnlyMode);

        const isFieldHelperTextHidden = isHelperTextHidden || this.props.nestedReadOnlyField;
        return (
            <div
                {...this.getBaseAttributesDivWrapper(
                    'icon',
                    'e-icon-field',
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    this.props.isNested,
                )}
            >
                {this.props.contextType !== ContextType.navigationPanel && !isFieldLabelHidden && (
                    <FieldLabel label={this.getTitle()} />
                )}
                {this.getIconElement()}
                {this.props.contextType !== ContextType.navigationPanel && !isFieldHelperTextHidden && (
                    <HelperText helperText={this.props.fieldProperties.helperText} />
                )}
            </div>
        );
    }
}

export const ConnectedIconComponent = connect(mapReadonlyStateToProps())(IconComponent);

export default ConnectedIconComponent;
