/**
 * @packageDocumentation
 * @module root
 * */

import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { ScreenExtension } from '../../../types';
import type { ScreenBase } from '../../../service/screen-base';
import type { IconBackgroundShape, IconBackgroundSize, IconProperties } from './icon-types';

/**
 * [Field]{@link ReadonlyFieldControlObject} that holds a graphic icon
 */
export class IconControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends ReadonlyFieldControlObject<
    CT,
    FieldKey.Icon,
    FieldComponentProps<FieldKey.Icon>
> {
    @FieldControlObjectResolvedProperty<IconProperties<CT>, IconControlObject<CT>>()
    /** The background color of the HTML field */
    backgroundColor?: string;

    @FieldControlObjectResolvedProperty<IconProperties<CT>, IconControlObject<CT>>()
    /** The font color of the HTML field */
    color?: string;

    @FieldControlObjectResolvedProperty<IconProperties<CT>, IconControlObject<CT>>()
    /** The background shape of the icon */
    backgroundShape?: IconBackgroundShape;

    @FieldControlObjectResolvedProperty<IconProperties<CT>, IconControlObject<CT>>()
    /** The background shape of the icon */
    backgroundSize?: IconBackgroundSize;
}
