/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ClickableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { IconControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { IconDecoratorProperties } from './icon-types';

class IconDecorator extends AbstractFieldDecorator<FieldKey.Icon> {
    protected _controlObjectConstructor = IconControlObject;

    protected _layout = AbstractFieldLayoutBuilder;
}

/**
 * Initializes the decorated member as a [Icon]{@link IconControlObject} field with the provided properties
 *
 * @param properties The properties that the [Icon]{@link IconControlObject} field will be initialized with
 */
export function iconField<T extends ScreenExtension<T>>(
    properties: IconDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Icon>(properties, IconDecorator, FieldKey.Icon);
}

export function iconFieldOverride<T extends ScreenExtension<T>>(
    properties: ClickableOverrideDecoratorProperties<IconDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Icon>(properties);
}
