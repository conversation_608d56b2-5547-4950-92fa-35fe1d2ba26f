PATH: XTREEM/UI+Field+Widgets/Preview+Field

## Introduction

The preview field can render rich documents such as PDF, DOCX or image files. It is a read-only field. Depending on the rendered file type, the user may be able to print, download or zoom the document.

Supported features:

| File type   | Printing | Thumbnails |
|-------------|----------|------------|
| TIFF        | N        | Y          |
| PDF         | Y        | Y          |
| DOCX        | N        | N          |
| Text files  | Y        | N          |
| Image files | Y        | N          |

## Example

```ts
@ui.decorators.previewField<Page>({
    height: '350px',
    helperText: 'Helper text displayed below the field.',
    isDisabled: false,
    isHelperTextHidden: false,
    isHidden: false,
    isTransient: true,
    size: 'medium',
    title: 'Preview Field',
    onChange() {
        ui.console.log(`Do something when the field is clicked.`);
    },
    onClick() {
        ui.console.log(`Do something when the field is changed.`);
    },
    parent() {
        return this.block;
    },
})
field: ui.fields.Preview;
```

## Decorator Properties

-   **hasThumbnailBar**:  Whether the thumbnail bar is enabled, please note this feature is not available for all file types
-   **canDownload**: Whether the document can be downloaded.;
-   **canPrint**: Whether printing is enabled, please note this feature is not available for all file types.
-   **canZoom**: Whether zooming is enabled;
-   **filename**: File name for downloaded files. If not set the component tries to figure out the extension and prefix it with `document`, e.g `document.pdf`;
-   **mimeType**: Explicit mime-type. If not set, the component would try to figure out the type from the content.

### Display Properties

-   **height**: The height attribute of the resulting HTML image (e.g. 100px, 50%, auto, etc.)
-   **helperText**: The helper text displayed below the field. This property is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.
-   **isFullWidth**: Determines whether the field takes up the full width of the grid or not.
-   **isHelperTextHidden**: Determines whether the field's helper text is displayed or not.
-   **isHidden**: Determines whether the field is displayed or not.
-   **isTitleHidden**: Determines whether the field's title is displayed or not.
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **isLoading**: Externally set loading state. If set to true, a loading bar is displayed even if the field has value.

### Binding Properties

-   **bind**: Determines the associated GraphQL node's property the field's value will be bound to.
-   **isTransient**: Determines whether the field will be excluded from the automatic data binding process or not.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.

### Event Handler Properties

-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

#### Runtime Functions

-   **refresh()**: Refetches the field's values from the server and updates the user interface.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).


## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Preview).
