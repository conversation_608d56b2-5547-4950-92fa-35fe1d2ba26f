/* eslint-disable react/no-array-index-key */
import * as React from 'react';
import type { PreviewRenderProps } from './preview-types';
import UTIF from 'utif';
import { localize } from '../../../service/i18n-service';
import { usePageIndex, useScrollToPage } from './preview-utils';

interface TiffPage {
    url: string;
    width: number;
    height: number;
}

const WIDTH_100_PERCENT = 592;
const PAGE_CLASS = 'e-preview-tiff-page';

function TiffThumbnail({
    page,
    index,
    onClick,
}: {
    page: TiffPage;
    index: number;
    onClick: (ev: React.MouseEvent) => void;
}): React.ReactElement {
    return (
        <button
            aria-label={localize('@sage/xtrem-ui/preview-go-to-page', 'Go to page {{pageNumber}}', {
                pageNumber: index + 1,
            })}
            className="e-preview-field-tiff-thumbnail"
            type="button"
            style={{ backgroundImage: `url(${page.url})` }}
            onClick={onClick}
        />
    );
}

export function TiffRenderer({
    height,
    isThumbnailBarVisible,
    scale,
    scrollPageIndex,
    setMetadataContent,
    setCurrentPage,
    value,
}: PreviewRenderProps): React.ReactElement {
    const bodyRef = React.useRef<HTMLDivElement>(null);

    const pages = React.useMemo((): TiffPage[] => {
        if (!value.arrayBuffer) {
            return [];
        }
        const ifds = UTIF.decode(value.arrayBuffer);
        return ifds.map((ifd): TiffPage => {
            UTIF.decodeImage(value.arrayBuffer, ifd);
            const rgba = UTIF.toRGBA8(ifd);
            const canvas = document.createElement('canvas');
            canvas.width = ifd.width;
            canvas.height = ifd.height;
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                throw new Error('No canvas context.');
            }
            const img = ctx.createImageData(ifd.width, ifd.height);
            img.data.set(rgba);
            ctx.putImageData(img, 0, 0);
            return {
                url: canvas.toDataURL(),
                width: ifd.width,
                height: ifd.height,
            };
        });
    }, [value]);

    React.useEffect(() => {
        setMetadataContent({ numberOfPages: pages.length || 1 });
    }, [pages, setMetadataContent]);

    const onThumbnailClick = useScrollToPage(bodyRef, `.${PAGE_CLASS}`);

    usePageIndex(bodyRef, `.${PAGE_CLASS}`, scrollPageIndex, setCurrentPage);

    return (
        <div className="e-preview-field-body" style={{ height: `${height}px` }}>
            {isThumbnailBarVisible && (
                <div className="e-preview-field-thumbnails">
                    {pages.map((page, index) => (
                        <TiffThumbnail key={index} index={index} page={page} onClick={onThumbnailClick(index)} />
                    ))}
                </div>
            )}

            <div
                ref={bodyRef}
                data-testid="e-preview-field-document-body"
                className="e-preview-field-document-body e-preview-field-document-body-tiff"
            >
                {pages.map((page, index) => (
                    <div
                        key={index}
                        className={PAGE_CLASS}
                        style={{
                            backgroundImage: `url(${page.url})`,
                            width: `${WIDTH_100_PERCENT * scale}px`,
                            height: `${(page.width / page.height) * WIDTH_100_PERCENT * scale}px`,
                        }}
                    />
                ))}
            </div>
        </div>
    );
}
