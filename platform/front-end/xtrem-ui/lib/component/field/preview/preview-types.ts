import type { BaseEditableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { ExtensionField, HasParent, Mappable, Sizable } from '../traits';
import type { BlockControlObject, SectionControlObject } from '../../control-objects';
import type { FieldControlObjectInstance } from '../../types';
import type { ScreenBase } from '../../../service/screen-base';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';

export interface PreviewDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<PreviewProperties<CT>, '_controlObjectType'>,
        Mappable<CT>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        Sizable {}

export interface PreviewProperties<CT extends ScreenBase = ScreenBase> extends ReadonlyFieldProperties<CT> {
    height?: number;
    /** Whether the thumbnail bar is enabled, please note this feature is not available for all file types. */
    hasThumbnailBar?: boolean;
    /** Whether the pagination controls are displayed in the header */
    hasPaginationControls?: boolean;
    /** Whether the document can be downloaded. */
    canDownload?: boolean;
    /** Whether printing is enabled, please note this feature is not available for all file types. */
    canPrint?: boolean;
    /** Whether zooming is enabled */
    canZoom?: boolean;
    /** File name for downloaded files. If not set the component tries to figure out the extension and prefix it with `document`, e.g `document.pdf` */
    filename?: string;
    /** Explicit mime-type. If not set, the component would try to figure out the type from the content. */
    mimeType?: string;
    /** Externally set loading state. If set to true, a loading bar is displayed even if the field has value. */
    isLoading?: boolean;
    /** Default zoom level */
    defaultZoomLevel?: number;
    /** Whether the filename in the header should be hidden */
    isFilenameHidden?: boolean;
}

export interface PreviewComponentProps
    extends BaseEditableComponentProperties<
        PreviewDecoratorProperties,
        string | { value: string },
        NestedFieldsAdditionalProperties
    > {
    onClose?: () => void;
}

export interface DocumentMetadata {
    updated?: string;
    pdfVersion?: string;
    producingSoftware?: string;
    resolution?: string;
    lineCount?: number;
    numberOfPages?: number;
}

export interface PreviewRenderProps {
    height: number;
    isThumbnailBarVisible: boolean;
    scale: number;
    setCurrentPage: (currentPageIndex: number) => void;
    setMetadataContent: (metadataContent: DocumentMetadata) => void;
    value: PreviewResolveValueResult;
    scrollPageIndex: number;
}
export interface PreviewRenderRouterProps extends Omit<PreviewRenderProps, 'value'> {
    isLoading: boolean;
    value: PreviewResolveValueResult | null;
}

export interface PreviewResolveValueResult {
    url: string;
    mimeType: string;
    arrayBuffer: ArrayBuffer;
    extension: string;
    size: number;
}
