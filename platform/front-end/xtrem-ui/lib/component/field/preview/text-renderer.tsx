import * as React from 'react';
import type { PreviewRenderProps } from './preview-types';

export function TextRenderer({ value, height, scale, setMetadataContent }: PreviewRenderProps): React.ReactElement {
    const containerRef = React.useRef<HTMLDivElement>(null);
    const [decodedText, setDecodedText] = React.useState<string>('');

    React.useEffect(() => {
        if (!value?.arrayBuffer) {
            setDecodedText('');
        }

        const text = new TextDecoder().decode(value?.arrayBuffer);
        setDecodedText(text);
        setMetadataContent({
            lineCount: text.split('\n').length,
            numberOfPages: 1,
        });
    }, [setMetadataContent, value]);

    return (
        <div className="e-preview-field-body" style={{ height: `${height}px` }}>
            <div
                className="e-preview-field-document-body e-preview-field-document-body-text"
                data-testid="e-preview-field-document-body"
            >
                <div className="e-preview-text-container" ref={containerRef}>
                    <pre
                        className="e-preview-text-content"
                        data-testid="e-preview-text-content"
                        style={{ fontSize: `${14 * scale}px` }}
                    >
                        {decodedText}
                    </pre>
                </div>
            </div>
        </div>
    );
}
