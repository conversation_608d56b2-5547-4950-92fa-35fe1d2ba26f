import * as React from 'react';
import {
    PREVIEW_SUPPORTED_MIME_TYPES_DOCX,
    PREVIEW_SUPPORTED_MIME_TYPES_IMAGE,
    PREVIEW_SUPPORTED_MIME_TYPES_PDF,
    PREVIEW_SUPPORTED_MIME_TYPES_TEXT,
    PREVIEW_SUPPORTED_MIME_TYPES_TIFF,
} from './preview-utils';
import type { PreviewRenderRouterProps } from './preview-types';
import LoaderBar from 'carbon-react/esm/components/loader-bar';
import { localize } from '../../../service/i18n-service';
import { AsyncPdfRenderer } from './async-pdf-renderer';
import { AsyncDocxRenderer } from './async-docx-renderer';
import { AsyncImageRenderer } from './async-image-renderer';
import { AsyncTextRenderer } from './async-text-renderer';
import { AsyncTiffRenderer } from './async-tiff-renderer';

export function PreviewRenderingRouter(props: PreviewRenderRouterProps): React.ReactNode {
    const { value, isLoading, height } = props;
    const mimeType = value?.mimeType;

    if (isLoading) {
        return <LoaderBar m="32px" data-testid="e-preview-field-loader" />;
    }

    if (mimeType && PREVIEW_SUPPORTED_MIME_TYPES_PDF.includes(mimeType)) {
        return <AsyncPdfRenderer {...props} value={value} />;
    }

    if (mimeType && PREVIEW_SUPPORTED_MIME_TYPES_DOCX.includes(mimeType)) {
        return <AsyncDocxRenderer {...props} value={value} />;
    }

    if (mimeType && PREVIEW_SUPPORTED_MIME_TYPES_IMAGE.includes(mimeType)) {
        return <AsyncImageRenderer {...props} value={value} />;
    }

    if (mimeType && PREVIEW_SUPPORTED_MIME_TYPES_TEXT.includes(mimeType)) {
        return <AsyncTextRenderer {...props} value={value} />;
    }

    if (mimeType && PREVIEW_SUPPORTED_MIME_TYPES_TIFF.includes(mimeType)) {
        return <AsyncTiffRenderer {...props} value={value} />;
    }

    return (
        <div
            className="e-preview-field-empty-state"
            data-testid="e-preview-field-document-body"
            style={{ height: `${height}px` }}
        >
            {value?.arrayBuffer
                ? localize('@sage/xtrem-ui/preview-no-preview-available', 'No preview available.')
                : localize('@sage/xtrem-ui/preview-no-file-selected', 'No file selected.')}
        </div>
    );
}
