import * as React from 'react';
import { connect } from 'react-redux';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { mapDispatchToProps, mapReadonlyStateToProps } from '../field-base-component';
import type { DocumentMetadata, PreviewComponentProps, PreviewResolveValueResult } from './preview-types';
import { PREVIEW_MIME_TYPES_PRINTING, PREVIEW_MIME_TYPES_THUMBNAIL, resolveValue } from './preview-utils';
import { getLabelTitle, isFieldDisabled } from '../carbon-helpers';
import { getComponentClass, getDataTestIdAttribute } from '../../../utils/dom';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import { messageDialog } from '../../../service/dialog-service';
import { localize } from '../../../service/i18n-service';
import { humanFileSize } from '../../../utils/formatters';
import { PreviewRenderingRouter } from './preview-rendering-router';
import styled from 'styled-components';
import { Option, Select } from 'carbon-react/esm/components/select';
import { ActionPopover, ActionPopoverItem } from 'carbon-react/esm/components/action-popover';
import { useResizeObserver } from 'usehooks-ts';
import { getMimeTypeUserFriendlyName } from '../../../utils/file-deposit-utils';
import { useFocus } from '../../../utils/hooks/effects/use-focus';
import NumberInput from 'carbon-react/esm/components/number';

const ButtonMinorDark = styled(ButtonMinor)`
    color: var(--colorsYang100);
    border-color: transparent;
    span[data-component='icon'] {
        color: var(--colorsYang100);
    }

    &[disabled] {
        color: var(--colorsYang030);
        span[data-component='icon'] {
            color: var(--colorsYang030);
        }
    }
`;

const ButtonMinorDarkWithBorder = styled(ButtonMinorDark)`
    border-radius: 16px;
    border: 2px solid var(--colorsYang100);
`;

export function PreviewComponent(props: PreviewComponentProps): React.ReactElement {
    const [isLoading, setLoading] = React.useState(false);
    const [isThumbnailBarVisible, setThumbnailBarVisible] = React.useState(false);
    const [resolvedValue, setResolvedValue] = React.useState<PreviewResolveValueResult | null>(null);
    const [metadataContent, setMetadataContent] = React.useState<DocumentMetadata | null>(null);
    const [scale, setScale] = React.useState<number>(props.fieldProperties.defaultZoomLevel || 1);
    const [pageIndex, setPageIndex] = React.useState<number>(0);
    const [pageIndexInputValue, setPageIndexInputValue] = React.useState<string>('1');
    const iframeRef = React.useRef<HTMLIFrameElement>(null);
    const containerRef = React.useRef<HTMLDivElement>(null);
    const { width } = useResizeObserver({ ref: containerRef });
    useFocus(containerRef, props.isInFocus, 'button');

    const filename = React.useMemo(() => {
        if (!resolvedValue) {
            return '';
        }

        const extension = resolvedValue?.extension || 'pdf';
        return props.fieldProperties.filename || `document.${extension}`;
    }, [props.fieldProperties.filename, resolvedValue]);

    const onPrint = React.useCallback(async (): Promise<void> => {
        if (!resolvedValue?.url || !iframeRef.current) {
            return;
        }
        iframeRef.current.addEventListener('load', () => {
            iframeRef.current?.contentWindow?.print();
        });
        iframeRef.current.src = resolvedValue.url;
    }, [resolvedValue?.url]);

    const onDownload = React.useCallback(
        async (ev: React.MouseEvent<HTMLButtonElement>): Promise<void> => {
            if (!resolvedValue?.url) {
                return;
            }
            ev.preventDefault();

            const linkElement = window.document.createElement('a');
            linkElement.href = resolvedValue.url;
            linkElement.setAttribute('download', filename);

            // Firefox requires the link to be in the body
            window.document.body.appendChild(linkElement);

            // simulate click
            linkElement.click();

            // remove the link when done
            window.document.body.removeChild(linkElement);
        },
        [resolvedValue?.url, filename],
    );

    const onThumbnailBarVisibleToggle = React.useCallback(() => {
        setThumbnailBarVisible(v => !v);
    }, []);

    const onViewInfo = React.useCallback(async () => {
        if (!metadataContent) {
            return;
        }

        let dialogContent = localize('@sage/xtrem-ui/pdf-metadata-file-name', '**File name:** {{name}}', {
            name: filename,
        });

        if (resolvedValue?.mimeType) {
            dialogContent += '\n\n';
            dialogContent += localize('@sage/xtrem-ui/pdf-metadata-file-type', '**File type:** {{fileType}}', {
                fileType: getMimeTypeUserFriendlyName(resolvedValue.mimeType),
            });
        }

        if (metadataContent.pdfVersion) {
            dialogContent += '\n\n';
            dialogContent += localize('@sage/xtrem-ui/pdf-metadata-pdf-version', '**PDF version:** {{version}}', {
                version: metadataContent.pdfVersion,
            });
        }

        if (metadataContent.producingSoftware) {
            dialogContent += '\n\n';
            dialogContent += localize(
                '@sage/xtrem-ui/pdf-metadata-producer',
                '**Producer application:** {{producer}}',
                {
                    producer: metadataContent.producingSoftware,
                },
            );
        }

        if (metadataContent?.resolution) {
            dialogContent += '\n\n';
            dialogContent += localize('@sage/xtrem-ui/pdf-metadata-resolution', '**Resolution:** {{resolution}}', {
                resolution: metadataContent.resolution,
            });
        }

        if (resolvedValue?.size) {
            dialogContent += '\n\n';
            dialogContent += localize('@sage/xtrem-ui/pdf-metadata-file-size', '**File size:** {{size}}', {
                size: humanFileSize(resolvedValue.size),
            });
        }

        if (metadataContent?.lineCount) {
            dialogContent += '\n\n';
            dialogContent += localize(
                '@sage/xtrem-ui/pdf-metadata-number-of-lines',
                '**Number of lines:** {{lineCount}}',
                {
                    lineCount: metadataContent.lineCount,
                },
            );
        }

        await messageDialog(
            props.screenId,
            'info',
            localize('@sage/xtrem-ui/document-metadata', 'Document metadata'),
            dialogContent,
            {
                mdContent: true,
                resolveOnCancel: true,
            },
        );
    }, [metadataContent, filename, resolvedValue?.mimeType, resolvedValue?.size, props.screenId]);

    const onZoomIn = React.useCallback(() => {
        setScale(s => (s < 4 ? s + 0.25 : s));
    }, []);

    const onZoomOut = React.useCallback(() => {
        setScale(s => (s > 0.25 ? s - 0.25 : s));
    }, []);

    const onStepToNextPage = React.useCallback(() => {
        setPageIndex(p => p + 1);
    }, []);

    const onStepToPreviousPage = React.useCallback(() => {
        setPageIndex(p => p - 1);
    }, []);

    const isDisabled = React.useMemo(
        () => isFieldDisabled(props.screenId, props.fieldProperties, props.value, null),
        [props.fieldProperties, props.screenId, props.value],
    );

    const isThumbnailsDisabled = React.useMemo(
        () =>
            isDisabled ||
            isLoading ||
            !props.fieldProperties.hasThumbnailBar ||
            !PREVIEW_MIME_TYPES_THUMBNAIL.includes(resolvedValue?.mimeType || ''),

        [isDisabled, isLoading, props.fieldProperties.hasThumbnailBar, resolvedValue?.mimeType],
    );

    const isDownloadDisabled = React.useMemo(
        () => isDisabled || isLoading || !resolvedValue?.url || !props.fieldProperties.canDownload,
        [isDisabled, isLoading, props.fieldProperties.canDownload, resolvedValue?.url],
    );
    const isPrintingDisabled = React.useMemo(
        () =>
            isDisabled ||
            isLoading ||
            !props.fieldProperties.canPrint ||
            !resolvedValue?.url ||
            !PREVIEW_MIME_TYPES_PRINTING.includes(resolvedValue?.mimeType || ''),
        [isDisabled, isLoading, props.fieldProperties.canPrint, resolvedValue?.url, resolvedValue?.mimeType],
    );

    React.useEffect(() => {
        if (!props.value) {
            setResolvedValue(null);
            return;
        }

        setLoading(true);

        resolveValue(props.value, props.fieldProperties.mimeType).then(result => {
            if (!result) {
                throw new Error('Unsupported value in the preview field.');
            }
            setResolvedValue(result);
            setLoading(false);
            setPageIndex(0);
            setPageIndexInputValue('1');
        });
    }, [props.value, props.fieldProperties.mimeType]);

    React.useEffect(() => {
        if (props.fieldProperties.defaultZoomLevel) {
            setScale(props.fieldProperties.defaultZoomLevel);
        }
    }, [props.fieldProperties.defaultZoomLevel]);

    React.useEffect(() => {
        setPageIndexInputValue((pageIndex + 1).toString());
    }, [pageIndex]);

    const title = getLabelTitle(props.screenId, props.fieldProperties, null); // Not available as a nested fields
    const hasTitle = !props.fieldProperties.isTitleHidden && title !== '' && title !== undefined;
    const hasHelperText =
        !props.fieldProperties.isHelperTextHidden &&
        props.fieldProperties.helperText !== '' &&
        props.fieldProperties.helperText !== undefined;

    const height = props.fieldProperties.height || props.fixedHeight || 400;
    const bodyHeight = height - 67 - (hasTitle ? 30 : 0);
    const shouldUseFullButtons = !!width && width > 650;

    return (
        <div
            data-testid={getDataTestIdAttribute('pdf', title, props.elementId)}
            className={getComponentClass(props, 'e-preview-field')}
            style={{ height }}
        >
            {hasTitle && <FieldLabel label={title} />}
            <div className="e-preview-field-document" ref={containerRef}>
                <div className="e-preview-field-toolbar">
                    {!props.fieldProperties.isFilenameHidden && (
                        <span
                            className="e-preview-field-toolbar-file-name"
                            data-testid="e-preview-field-toolbar-file-name"
                        >
                            {filename}
                        </span>
                    )}
                    <span className="e-preview-field-toolbar-spacer" />
                    {props.value &&
                        props.fieldProperties.hasPaginationControls &&
                        metadataContent?.numberOfPages &&
                        metadataContent.numberOfPages > 1 && (
                            <div className="e-preview-field-toolbar-main-pagination-controls">
                                <ButtonMinorDark
                                    data-testid="e-preview-field-page-prev"
                                    onClick={onStepToPreviousPage}
                                    aria-label={localize('@sage/xtrem-ui/preview-page-prev', 'Prev page')}
                                    marginRight="16px"
                                    iconType="chevron_up"
                                    disabled={pageIndex === 0}
                                    size="small"
                                    iconTooltipMessage={localize('@sage/xtrem-ui/preview-page-prev', 'Prev page')}
                                />
                                <ButtonMinorDark
                                    data-testid="e-preview-field-page-next"
                                    onClick={onStepToNextPage}
                                    aria-label={localize('@sage/xtrem-ui/preview-page-next', 'Next page')}
                                    marginRight="16px"
                                    iconType="chevron_down"
                                    size="small"
                                    disabled={pageIndex === (metadataContent?.numberOfPages || 1) - 1}
                                    iconTooltipMessage={localize('@sage/xtrem-ui/preview-page-next', 'Next page')}
                                />
                                <span className="e-preview-field-page-number">
                                    <NumberInput
                                        data-testid="e-preview-field-page-number"
                                        value={pageIndexInputValue}
                                        size="small"
                                        className="e-preview-field-page-number"
                                        max={metadataContent?.numberOfPages}
                                        min={1}
                                        onChange={(e): void => setPageIndexInputValue(e.target.value)}
                                        onBlur={(): void => setPageIndex(parseInt(pageIndexInputValue, 10) - 1)}
                                    />
                                </span>
                                <span className="e-preview-field-toolbar-main-pagination-separator">/</span>
                                <span
                                    data-testid="e-preview-field-toolbar-main-pagination-total-number-of-pages"
                                    className="e-preview-field-toolbar-main-pagination-total-number-of-pages"
                                >
                                    {metadataContent?.numberOfPages || 1}
                                </span>
                            </div>
                        )}
                    {props.value && props.fieldProperties.canZoom && (
                        <div className="e-preview-field-toolbar-main-zoom-controls">
                            <ButtonMinorDark
                                data-testid="e-preview-field-zoom-out"
                                onClick={onZoomOut}
                                aria-label={localize('@sage/xtrem-ui/preview-zoom-out', 'Zoom out')}
                                marginRight="16px"
                                iconType="minus"
                                size="small"
                                disabled={isDisabled || scale <= 0.25}
                                iconTooltipMessage={localize('@sage/xtrem-ui/preview-zoom-out', 'Zoom out')}
                            />
                            <ButtonMinorDark
                                data-testid="e-preview-field-zoom-in"
                                onClick={onZoomIn}
                                aria-label={localize('@sage/xtrem-ui/preview-zoom-in', 'Zoom in')}
                                marginRight="16px"
                                iconType="plus"
                                size="small"
                                disabled={isDisabled || scale >= 4}
                                iconTooltipMessage={localize('@sage/xtrem-ui/preview-zoom-in', 'Zoom in')}
                            />
                            <div className="e-preview-field-toolbar-main-zoom-level">
                                <Select
                                    data-testid="e-preview-field-zoom-level"
                                    aria-label={localize('@sage/xtrem-ui/preview-zoom-level', 'Zoom level')}
                                    listPlacement="bottom"
                                    value={scale.toString()}
                                    onChange={(e): void => setScale(parseFloat(e.target.value))}
                                    size="small"
                                    disabled={isDisabled}
                                >
                                    <Option text="25%" value="0.25" />
                                    <Option text="50%" value="0.5" />
                                    <Option text="75%" value="0.75" />
                                    <Option text="100%" value="1" />
                                    <Option text="125%" value="1.25" />
                                    <Option text="150%" value="1.5" />
                                    <Option text="175%" value="1.75" />
                                    <Option text="200%" value="2" />
                                    <Option text="225%" value="2.25" />
                                    <Option text="250%" value="2.5" />
                                    <Option text="275%" value="2.75" />
                                    <Option text="300%" value="3" />
                                    <Option text="325%" value="3.25" />
                                    <Option text="350%" value="3.5" />
                                    <Option text="375%" value="3.75" />
                                    <Option text="400%" value="4" />
                                </Select>
                            </div>
                        </div>
                    )}
                    {props.value && (
                        <div className="e-preview-field-toolbar-main-options">
                            {!isPrintingDisabled && (
                                <ButtonMinorDarkWithBorder
                                    data-testid="e-preview-field-print"
                                    size="small"
                                    buttonType="tertiary"
                                    iconType={shouldUseFullButtons ? undefined : 'print'}
                                    onClick={onPrint}
                                    marginRight="16px"
                                >
                                    {shouldUseFullButtons
                                        ? localize('@sage/xtrem-ui/preview-action-print', 'Print')
                                        : undefined}
                                </ButtonMinorDarkWithBorder>
                            )}
                            {!isDownloadDisabled && (
                                <ButtonMinorDarkWithBorder
                                    data-testid="e-preview-field-download"
                                    size="small"
                                    buttonType="tertiary"
                                    iconType={shouldUseFullButtons ? undefined : 'download'}
                                    onClick={onDownload}
                                    marginRight="16px"
                                >
                                    {shouldUseFullButtons
                                        ? localize('@sage/xtrem-ui/preview-action-download', 'Download')
                                        : undefined}
                                </ButtonMinorDarkWithBorder>
                            )}
                            <div className="e-preview-field-toolbar-more-options">
                                <ActionPopover
                                    renderButton={renderButtonProps => {
                                        return (
                                            <ButtonMinorDarkWithBorder
                                                {...renderButtonProps.ariaAttributes}
                                                {...renderButtonProps}
                                                disabled={isDisabled}
                                                data-testid="e-preview-field-more-options"
                                                aria-label={localize(
                                                    '@sage/xtrem-ui/preview-more-options',
                                                    'More options',
                                                )}
                                                size="small"
                                                iconTooltipMessage={localize(
                                                    '@sage/xtrem-ui/preview-more-options',
                                                    'More options',
                                                )}
                                                iconType="ellipsis_vertical"
                                            />
                                        );
                                    }}
                                >
                                    <ActionPopoverItem
                                        icon="box_arrow_right"
                                        onClick={onThumbnailBarVisibleToggle}
                                        disabled={isThumbnailsDisabled}
                                        data-testid="e-preview-field-thumbnails-toggle"
                                    >
                                        {localize('@sage/xtrem-ui/preview-more-thumbnails', 'Thumbnails')}
                                    </ActionPopoverItem>
                                    <ActionPopoverItem
                                        data-testid="e-preview-field-file-info"
                                        icon="info"
                                        onClick={onViewInfo}
                                    >
                                        {localize('@sage/xtrem-ui/preview-more-file-info', ' File information')}
                                    </ActionPopoverItem>
                                </ActionPopover>
                                {props.onClose && (
                                    <ButtonMinorDark
                                        disabled={isDisabled}
                                        data-testid="e-preview-field-close"
                                        aria-label={localize('@sage/xtrem-ui/preview-close', 'Close preview')}
                                        size="small"
                                        iconTooltipMessage={localize('@sage/xtrem-ui/preview-close', 'Close preview')}
                                        iconType="close"
                                        onClick={props.onClose}
                                        marginLeft="48px"
                                    />
                                )}
                            </div>
                        </div>
                    )}
                </div>
                <PreviewRenderingRouter
                    height={bodyHeight}
                    isLoading={isLoading || !!props.fieldProperties.isLoading}
                    isThumbnailBarVisible={isThumbnailBarVisible}
                    scale={scale}
                    setCurrentPage={setPageIndex}
                    setMetadataContent={setMetadataContent}
                    value={resolvedValue}
                    scrollPageIndex={pageIndex}
                />
                <iframe
                    ref={iframeRef}
                    title="print ref"
                    style={{ display: 'none' }}
                    data-testid="e-preview-component-print-iframe"
                />
            </div>
            {hasHelperText && <HelperText helperText={props.fieldProperties.helperText} />}
        </div>
    );
}

export const ConnectedPreviewComponent = connect(mapReadonlyStateToProps(), mapDispatchToProps())(PreviewComponent);

export default ConnectedPreviewComponent;
