import * as React from 'react';
import type { PreviewRenderProps } from './preview-types';
import { useResizeObserver } from 'usehooks-ts';

export function ImageRenderer({ value, height, scale, setMetadataContent }: PreviewRenderProps): React.ReactElement {
    const containerRef = React.useRef<HTMLDivElement>(null);
    const imageRef = React.useRef<HTMLImageElement>(null);
    const { width } = useResizeObserver({ ref: containerRef });

    const onImageLoad = React.useCallback(() => {
        if (!imageRef.current) {
            return;
        }
        setMetadataContent({
            resolution: `${imageRef.current.naturalWidth} x ${imageRef.current.naturalHeight}`,
            numberOfPages: 1,
        });
    }, [setMetadataContent]);

    const appliedWidth = React.useMemo(() => {
        return ((width || 64) - 64) * scale;
    }, [width, scale]);

    const appliedHeight = React.useMemo(() => {
        return ((height || 64) - 64) * scale;
    }, [height, scale]);

    return (
        <div className="e-preview-field-body" style={{ height: `${height}px` }}>
            <div className="e-preview-field-document-body" data-testid="e-preview-field-document-body">
                <div className="e-preview-image-container" ref={containerRef}>
                    <div
                        className="e-preview-image"
                        data-testid="e-preview-image"
                        style={{ width: appliedWidth, height: appliedHeight, backgroundImage: `url(${value.url})` }}
                    />
                    <img
                        aria-hidden={true}
                        style={{ display: 'none' }}
                        src={value.url}
                        onLoad={onImageLoad}
                        ref={imageRef}
                    />
                </div>
            </div>
        </div>
    );
}
