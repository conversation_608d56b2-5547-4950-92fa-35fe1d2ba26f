/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { PreviewControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { PreviewDecoratorProperties } from './preview-types';

class PreviewDecorator extends AbstractFieldDecorator<FieldKey.Preview> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = PreviewControlObject;
}

/**
 * Initializes the decorated member as a [Pdf]{@link PreviewControlObject} field with the provided properties
 *
 * @param properties The properties that the [Pdf]{@link PreviewControlObject} field will be initialized with
 */
export function previewField<T extends ScreenExtension<T>>(
    properties: PreviewDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Preview>(properties, PreviewDecorator, FieldKey.Preview);
}

export function previewFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<PreviewDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Preview>(properties);
}
