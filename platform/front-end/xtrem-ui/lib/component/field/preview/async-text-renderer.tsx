import * as React from 'react';
import LoaderBar from 'carbon-react/esm/components/loader-bar';
import type { PreviewRenderProps } from './preview-types';

const TextRenderer = React.lazy(() => import('./text-renderer').then(c => ({ default: c.TextRenderer })));

export function AsyncTextRenderer(props: PreviewRenderProps): React.ReactElement {
    return (
        <React.Suspense fallback={<LoaderBar m="32px" />}>
            <TextRenderer {...props} />
        </React.Suspense>
    );
}
