import * as React from 'react';
import type { PreviewRenderProps } from './preview-types';
import { renderAsync } from 'docx-preview';
import { localize } from '../../../service/i18n-service';
import { usePageIndex, useScrollToPage } from './preview-utils';

const PAGES_SELECTOR = '.docx-wrapper>section';

export function DocxRenderer({
    height,
    isThumbnailBarVisible,
    scale,
    scrollPageIndex,
    setCurrentPage,
    setMetadataContent,
    value,
}: PreviewRenderProps): React.ReactElement {
    const documentRef = React.useRef<HTMLDivElement>(null);
    const bodyRef = React.useRef<HTMLDivElement>(null);
    const [numberOfPages, setNumberOfPages] = React.useState<number>(0);

    usePageIndex(bodyRef, PAGES_SELECTOR, scrollPageIndex, setCurrentPage);

    const onThumbnailClick = useScrollToPage(bodyRef, PAGES_SELECTOR);

    React.useEffect(() => {
        if (documentRef.current) {
            renderAsync(value.arrayBuffer, documentRef.current, undefined, { breakPages: true }).then(() => {
                const pages = documentRef.current?.querySelectorAll(PAGES_SELECTOR);
                setNumberOfPages(pages?.length || 0);
                setMetadataContent({ numberOfPages: pages?.length || 1 });
            });
        }
    }, [value, documentRef, setMetadataContent]);

    React.useEffect(() => {
        documentRef.current?.querySelectorAll<HTMLDivElement>(PAGES_SELECTOR).forEach((page: HTMLDivElement) => {
            if (!page) {
                return;
            }
            const baseHeight = page.clientHeight;
            const additionalHeight = baseHeight * (scale - 1);
            page.style.marginTop = `${additionalHeight / 2}px`;
            page.style.marginBottom = `${additionalHeight / 2 + 30}px`;
            page.style.transform = `scale(${scale})`;
        });
    }, [scale]);

    const thumbnails = React.useMemo(() => {
        const collector: React.JSX.Element[] = [];
        if (document) {
            for (let i = 0; i < numberOfPages; i += 1) {
                collector.push(
                    <button
                        type="button"
                        key={i}
                        className="e-preview-field-docx-thumbnail"
                        onClick={onThumbnailClick(i)}
                        aria-label={localize('@sage/xtrem-ui/preview-go-to-page', 'Go to page {{pageNumber}}', {
                            pageNumber: i + 1,
                        })}
                    >
                        {i + 1}
                    </button>,
                );
            }
        }

        return collector;
    }, [numberOfPages, onThumbnailClick]);

    return (
        <div className="e-preview-field-body" style={{ height: `${height}px` }}>
            {isThumbnailBarVisible && <div className="e-preview-field-thumbnails">{thumbnails}</div>}
            <div data-testid="e-preview-field-document-body" className="e-preview-field-document-body" ref={bodyRef}>
                <div ref={documentRef} />
            </div>
        </div>
    );
}
