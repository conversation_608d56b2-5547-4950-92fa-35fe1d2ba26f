import { attachmentsMimeTypesByExtention, objectKeys } from '@sage/xtrem-shared';
import axios from 'axios';
import { fileTypeFromBuffer } from 'file-type';
import { isObject, isString, noop } from 'lodash';
import React from 'react';
import type { PreviewResolveValueResult } from './preview-types';

const base64regex = /[A-Za-z0-9+/=]/;
// eslint-disable-next-line @sage/redos/no-vulnerable
const urlRegex = /^https?:\/\/.+(:[0-9]{4,5})?\/.*$/;

function getExtensionFromMimeType(mimeType: string): string | null {
    return (
        objectKeys(attachmentsMimeTypesByExtention).find(ext =>
            attachmentsMimeTypesByExtention[ext].includes(mimeType),
        ) || null
    );
}

export async function resolveValue(
    value?: string | null | { value?: string },
    explicitMimeType?: string,
): Promise<PreviewResolveValueResult | null> {
    if (value) {
        if (isObject(value) && value?.value?.match(base64regex)) {
            const decodedPdfContent = atob(value.value);
            const byteArray = new Uint8Array(decodedPdfContent.length);
            for (let i = 0; i < decodedPdfContent.length; i += 1) {
                byteArray[i] = decodedPdfContent.charCodeAt(i);
            }
            const fileType = await fileTypeFromBuffer(byteArray);
            const mimeType = explicitMimeType || fileType?.mime || 'application/pdf';
            const blob = new Blob([byteArray.buffer], { type: mimeType });
            const url = URL.createObjectURL(blob);
            return {
                url,
                arrayBuffer: byteArray.buffer,
                mimeType,
                extension: fileType?.ext || getExtensionFromMimeType(mimeType) || 'pdf',
                size: byteArray.length,
            };
        }

        if (isString(value) && value.match(urlRegex)) {
            const response = await axios.get(value, { responseType: 'arraybuffer' });
            const byteArray = new Uint8Array(response.data);
            const fileType = await fileTypeFromBuffer(response.data);
            const mimeType =
                explicitMimeType || fileType?.mime || response.headers['Content-Type']?.toString() || 'application/pdf';
            const blob = new Blob([byteArray.buffer], { type: mimeType });
            const url = URL.createObjectURL(blob);
            return {
                url,
                mimeType,
                arrayBuffer: byteArray.buffer,
                extension: fileType?.ext || getExtensionFromMimeType(mimeType) || 'pdf',
                size: byteArray.length,
            };
        }

        throw new Error('Unsupported value in the preview field.');
    }

    return null;
}

export function calculatePageScrollTop(
    scrollableContainerRef: React.RefObject<HTMLDivElement>,
    pageSelector: string,
    index: number,
): number {
    const pages = scrollableContainerRef.current?.querySelectorAll<HTMLDivElement>(pageSelector);
    const page = pages?.[index];
    if (!page || !pages) {
        return 0;
    }
    return Array.from(pages)
        .slice(0, index)
        .reduce((acc, current) => {
            return acc + current.getBoundingClientRect().height;
        }, 0);
}

export function useScrollToPage(
    scrollableContainerRef: React.RefObject<HTMLDivElement>,
    pageSelector: string,
): (index: number) => (ev: React.MouseEvent) => void {
    return React.useCallback(
        (index: number) => (ev: React.MouseEvent) => {
            ev.preventDefault();
            if (!scrollableContainerRef.current) {
                return;
            }

            const top = calculatePageScrollTop(scrollableContainerRef, pageSelector, index);
            scrollableContainerRef.current.scrollTo({ top, behavior: 'smooth' });
        },
        [scrollableContainerRef, pageSelector],
    );
}
export function usePageIndex(
    scrollableContainerRef: React.RefObject<HTMLDivElement>,
    pageSelector: string,
    selectedIndex: number,
    setCurrentPage: (currentPageIndex: number) => void,
): void {
    const threshold = 100;

    // Update scroll position by updated page number effect
    React.useEffect((): void => {
        const scrollContainer = scrollableContainerRef.current;

        if (!scrollContainer) {
            return;
        }

        const numberOfPages = scrollContainer?.querySelectorAll<HTMLDivElement>(pageSelector).length;
        const targetPageScrollTop = calculatePageScrollTop(scrollableContainerRef, pageSelector, selectedIndex);
        const nextPageScrollTop =
            numberOfPages === selectedIndex + 1
                ? Number.MAX_SAFE_INTEGER
                : calculatePageScrollTop(scrollableContainerRef, pageSelector, selectedIndex + 1);
        const currentScrollTop = scrollableContainerRef.current?.scrollTop || 0;
        if (currentScrollTop < targetPageScrollTop - threshold || currentScrollTop > nextPageScrollTop + threshold) {
            scrollContainer.scrollTo({ top: targetPageScrollTop });
        }
    }, [selectedIndex, scrollableContainerRef, pageSelector]);

    // Update selected index by scrolling effect
    React.useEffect(() => {
        const scrollContainer = scrollableContainerRef.current;

        if (!scrollContainer) {
            return noop;
        }

        const scrollListener = (): void => {
            const currentScrollTop = scrollContainer.scrollTop;
            const numberOfPages = scrollContainer.querySelectorAll<HTMLDivElement>(pageSelector).length;
            for (let i = 0; i < numberOfPages; i += 1) {
                const pageScrollTop = calculatePageScrollTop(scrollableContainerRef, pageSelector, i);
                const nextPageScrollTop =
                    numberOfPages === i + 1
                        ? Number.MAX_SAFE_INTEGER
                        : calculatePageScrollTop(scrollableContainerRef, pageSelector, i + 1);
                if (currentScrollTop >= pageScrollTop - threshold && currentScrollTop < nextPageScrollTop + threshold) {
                    if (selectedIndex !== i) {
                        setCurrentPage(i);
                    }
                    break;
                }
            }
        };

        scrollContainer.addEventListener('scroll', scrollListener);

        return (): void => {
            scrollContainer.removeEventListener('scroll', scrollListener);
        };
    }, [pageSelector, scrollableContainerRef, selectedIndex, setCurrentPage]);
}

export const PREVIEW_SUPPORTED_MIME_TYPES_PDF = [
    'application/pdf',
    'application/x-pdf',
    'application/x-bzpdf',
    'application/x-gzpdf',
];

export const PREVIEW_SUPPORTED_MIME_TYPES_IMAGE = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/bmp',
    'image/webp',
    'image/svg+xml',
];

export const PREVIEW_SUPPORTED_MIME_TYPES_TIFF = ['image/tiff'];

export const PREVIEW_SUPPORTED_MIME_TYPES_DOCX = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
];
export const PREVIEW_SUPPORTED_MIME_TYPES_TEXT = [
    'text/plain',
    'text/css',
    'text/csv',
    'text/calendar',
    'text/markdown',
    'text/vcard',
    'application/json',
    'application/xml',
    'text/xml',
    'application/csv',
    'application/vnd.ms-excel',
];

export const PREVIEW_SUPPORTED_MEME_TYPES = [
    ...PREVIEW_SUPPORTED_MIME_TYPES_PDF,
    ...PREVIEW_SUPPORTED_MIME_TYPES_IMAGE,
    ...PREVIEW_SUPPORTED_MIME_TYPES_DOCX,
    ...PREVIEW_SUPPORTED_MIME_TYPES_TEXT,
    ...PREVIEW_SUPPORTED_MIME_TYPES_TIFF,
];

export const PREVIEW_MIME_TYPES_THUMBNAIL = [
    ...PREVIEW_SUPPORTED_MIME_TYPES_PDF,
    ...PREVIEW_SUPPORTED_MIME_TYPES_DOCX,
    ...PREVIEW_SUPPORTED_MIME_TYPES_TIFF,
];
export const PREVIEW_MIME_TYPES_PRINTING = [
    ...PREVIEW_SUPPORTED_MIME_TYPES_TEXT,
    ...PREVIEW_SUPPORTED_MIME_TYPES_PDF,
    ...PREVIEW_SUPPORTED_MIME_TYPES_IMAGE,
];
