import * as React from 'react';
import LoaderBar from 'carbon-react/esm/components/loader-bar';
import type { PreviewRenderProps } from './preview-types';

const TiffRenderer = React.lazy(() => import('./tiff-renderer').then(c => ({ default: c.TiffRenderer })));

export function AsyncTiffRenderer(props: PreviewRenderProps): React.ReactElement {
    return (
        <React.Suspense fallback={<LoaderBar m="32px" />}>
            <TiffRenderer {...props} />
        </React.Suspense>
    );
}
