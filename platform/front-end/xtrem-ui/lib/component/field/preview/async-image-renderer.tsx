import * as React from 'react';
import LoaderBar from 'carbon-react/esm/components/loader-bar';
import type { PreviewRenderProps } from './preview-types';

const ImageRenderer = React.lazy(() => import('./image-renderer').then(c => ({ default: c.ImageRenderer })));

export function AsyncImageRenderer(props: PreviewRenderProps): React.ReactElement {
    return (
        <React.Suspense fallback={<LoaderBar m="32px" />}>
            <ImageRenderer {...props} />
        </React.Suspense>
    );
}
