.e-preview-field {
    overflow: hidden;
    display: flex;

    .e-preview-field-toolbar {
        background: var(--colorsYin100);
        padding: 16px;
        border-bottom: 1px solid #474747;
        display: flex;
    }

    .e-preview-field-toolbar-file-name {
        color: var(--colorsUtilityYang100);
        font-size: var(--fontSizes100);
        font-weight: var(--fontWeights500);
        line-height: 32px;
        height: 32px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .e-preview-field-toolbar-main-options {
        display: inline-block;
        line-height: 32px;
        height: 32px;
        white-space: nowrap;
    }

    .e-preview-field-toolbar-more-options {
        display: inline-flex;
    }

    .e-preview-field-toolbar-main-zoom-controls {
        justify-content: center;
        display: flex;
        position: relative;
        white-space: nowrap;
        margin-right: 16px;
        height: 32px;

        .e-preview-field-toolbar-main-zoom-level {
            [data-element="select-list-wrapper"] {
                background: #1A1A1A;
                color: var(--colorsUtilityYang100);
            }

            [aria-selected="true"] {
                color: var(--colorsYin090);
            }

            [aria-selected="false"]:hover {
                color: var(--colorsYin090);
            }

            >div>div>div>div>div>div {
                background: #1A1A1A;
                border: 1px solid #474747;
                text-align: center;

                >span>span {
                    text-align: center;
                    color: var(--colorsUtilityYang100);
                }
            }
        }
    }

    .e-preview-field-toolbar-spacer {
        flex: 1;
    }

    .e-preview-field-toolbar-main-pagination-controls {
        justify-content: center;
        display: flex;
        position: relative;
        white-space: nowrap;
        margin-right: 16px;
        height: 32px;
    }

    .e-preview-field-toolbar-main-pagination-separator {
        color: var(--colorsUtilityYang100);
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: var(--fontWeights500);
        line-height: 32px;
        margin-right: 8px;
        margin-left: 8px;
    }

    .e-preview-field-toolbar-main-pagination-total-number-of-pages {
        color: var(--colorsUtilityYang100);
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: var(--fontWeights500);
        line-height: 32px;
    }

    .e-preview-field-page-number>div>div>div>div {
        background: #1A1A1A;
        border: 1px solid #474747;
        text-align: center;

        >input {
            text-align: center;
            color: var(--colorsUtilityYang100);
        }
    }


    .e-preview-field-toolbar-main-zoom-level {
        width: 80px;
    }

    .e-preview-field-document {
        height: 100%;
        overflow: hidden;
        border-top-right-radius: 4px;
        border-top-left-radius: 4px;
        border: 1px solid rgba(189, 195, 199, 0.58)
    }

    .react-pdf__Document {
        padding: 0;
        display: flex;
        overflow: hidden;
        height: 100%;
        box-sizing: border-box;
    }

    .react-pdf__Page {
        background: transparent !important;
        padding-bottom: 24px;
        padding-top: 24px;
    }

    .e-preview-field-body {
        overflow-x: auto;
        display: flex;
        flex: 1;
    }

    .e-preview-field-thumbnails {
        width: 200px;
        overflow-y: auto;
        background: var(--colorsGray700);
        padding: 16px 0;

        .react-pdf__Thumbnail__page__canvas {
            margin: 0 auto 16px auto;
            box-shadow: var(--boxShadow200);
            background: var(--colorsYang100);
        }

        .react-pdf__Thumbnail__page {
            background: var(--colorsGray700) !important;
        }
    }

    .e-preview-text-content {
        margin: 16px;
        transition: 0.2s ease-in-out;
    }

    .e-preview-field-document-body {
        flex: 1;
        overflow-y: auto;
        background: var(--colorsGray750);

        &.e-preview-field-document-body-text {
            overflow: auto;
            background: var(--colorsYang100);
        }


        .docx-wrapper {
            background: none;
            padding: 24px;

            .docx {
                transition: 0.2s ease-in-out;
            }
        }

        .react-pdf__Page__canvas {
            margin: 0 auto 16px auto;
            background: var(--colorsYang100);
            box-shadow: var(--boxShadow200);
        }
    }

    .e-preview-image {
        padding-left: 32px;
        padding-right: 32px;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: contain;
        transition: 0.2s ease-in-out;
    }

    .e-preview-field-docx-thumbnail {
        display: block;
        user-select: none;
        width: 150px;
        height: 212px;
        margin: 0 auto 16px auto;
        box-shadow: var(--boxShadow200);
        text-align: center;
        padding-top: 32px;
        font-size: 64px;
        box-sizing: border-box;
        background: var(--colorsYang100);
        cursor: pointer;

        &:focus {
            outline: 3px solid var(--colorsSemanticFocus500);
        }
    }

    .e-preview-image-container {
        display: flex;
        justify-content: center;
        padding-top: 32px;
        padding-bottom: 32px;
    }

    .e-preview-field-empty-state {
        background: var(--colorsGray750);
        font-size: 32px;
        color: var(--colorsActionMinorYang100);
        display: flex;
        text-align: center;
        justify-content: center;
        align-items: center;
    }

    .e-preview-field-tiff-thumbnail {
        display: block;
        user-select: none;
        width: 150px;
        height: 212px;
        margin: 0 auto 16px auto;
        box-shadow: var(--boxShadow200);
        text-align: center;
        padding-top: 32px;
        font-size: 64px;
        box-sizing: border-box;
        background: var(--colorsYang100);
        background-repeat: no-repeat;
        background-position: center center;
        background-size: contain;
        cursor: pointer;

        &:focus {
            outline: 3px solid var(--colorsSemanticFocus500);
        }
    }

    .e-preview-tiff-page {
        padding-left: 32px;
        padding-right: 32px;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: contain;
        transition: 0.2s ease-in-out;
        margin-bottom: 16px;
        margin-top: 16px;
    }

    .e-preview-field-document-body-tiff {
        justify-items: center;
    }
}
