import * as React from 'react';
import LoaderBar from 'carbon-react/esm/components/loader-bar';
import type { PreviewRenderProps } from './preview-types';

const DocxRenderer = React.lazy(() => import('./docx-renderer').then(c => ({ default: c.DocxRenderer })));

export function AsyncDocxRenderer(props: PreviewRenderProps): React.ReactElement {
    return (
        <React.Suspense fallback={<LoaderBar m="32px" />}>
            <DocxRenderer {...props} />
        </React.Suspense>
    );
}
