jest.mock('file-type', () => ({
    fileTypeFromBuffer: () => null,
}));

import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';

import { render, fireEvent, waitFor } from '@testing-library/react';
import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../../../../redux';
import type { PreviewProperties } from '../../../control-objects';
import { FieldKey } from '../../../types';
import { ConnectedPreviewComponent } from '../preview-component';
import fs from 'fs';
import path from 'path';
import '@testing-library/jest-dom';

describe('Preview component', () => {
    const createUrlMock = jest.fn();
    window.URL.createObjectURL = createUrlMock;

    const screenId = 'TestPage';
    const pdfFieldId = 'test-preview-field';
    const imageFieldId = 'test-image-preview-field';
    const docxFieldId = 'test-docx-preview-field';
    const emptyFieldId = 'test-empty-preview-field';
    const textFieldId = 'test-text-preview-field';
    let mockFieldProperties: PreviewProperties;
    let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;
    let mockState: xtremRedux.XtremAppState;

    const setPreviewFieldProperties = (fieldId: string, properties: Partial<PreviewProperties>) => {
        mockState.screenDefinitions[screenId].metadata.uiComponentProperties[fieldId] = {
            ...mockState.screenDefinitions[screenId].metadata.uiComponentProperties[fieldId],
            ...properties,
        };
    };

    beforeEach(() => {
        createUrlMock.mockReturnValue('blob:http://localhost:3000/1234');
        mockFieldProperties = {
            title: 'Test Field Title',
        };
        mockState = getMockState();
        mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        addFieldToState(
            FieldKey.Preview,
            mockState,
            screenId,
            pdfFieldId,
            { ...mockFieldProperties, mimeType: 'application/pdf' },
            {
                value: fs.readFileSync(path.resolve(__dirname, '../__fixtures__/pdf-fixture.pdf'), {
                    encoding: 'base64',
                }),
            },
        );
        addFieldToState(
            FieldKey.Preview,
            mockState,
            screenId,
            docxFieldId,
            {
                ...mockFieldProperties,
                mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            },
            {
                value: fs.readFileSync(path.resolve(__dirname, '../__fixtures__/docx-fixture.docx'), {
                    encoding: 'base64',
                }),
            },
        );
        addFieldToState(
            FieldKey.Preview,
            mockState,
            screenId,
            imageFieldId,
            {
                ...mockFieldProperties,
                mimeType: 'image/jpeg',
            },
            {
                value: fs.readFileSync(path.resolve(__dirname, '../__fixtures__/image-fixture.jpg'), {
                    encoding: 'base64',
                }),
            },
        );
        addFieldToState(
            FieldKey.Preview,
            mockState,
            screenId,
            textFieldId,
            {
                ...mockFieldProperties,
                mimeType: 'application/json',
            },
            {
                value: fs.readFileSync(path.resolve(__dirname, '../__fixtures__/text-fixture.json'), {
                    encoding: 'base64',
                }),
            },
        );
        addFieldToState(FieldKey.Preview, mockState, screenId, emptyFieldId, mockFieldProperties, null);

        mockStore = getMockStore(mockState);
        jest.spyOn(xtremRedux.actions, 'setFieldValue').mockReturnValue(
            () => Promise.resolve({ type: 'SetFieldValue' }) as any,
        );
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('connected', () => {
        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                setPreviewFieldProperties(imageFieldId, { isHidden: true });
                const { container } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render disabled', () => {
                setPreviewFieldProperties(imageFieldId, { isDisabled: true });
                const { container } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render without value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={emptyFieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });
        });

        describe('helper text', () => {
            it('should render helperText', () => {
                setPreviewFieldProperties(imageFieldId, { helperText: 'This is helper text' });
                const { queryByTestId } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );
                expect(queryByTestId('e-field-helper-text')).not.toBeNull();
            });

            it('should not render helperText', () => {
                setPreviewFieldProperties(imageFieldId, { helperText: '' });
                const { queryByTestId } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );
                expect(queryByTestId('e-field-helper-text')).toBeNull();
            });

            it('should not render helperText if it is set but hidden', () => {
                setPreviewFieldProperties(imageFieldId, { helperText: 'helper text', isHelperTextHidden: true });

                const { queryByTestId } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );
                expect(queryByTestId('e-field-helper-text')).toBeNull();
            });

            it('should not render helperText if it is set but hidden', () => {
                setPreviewFieldProperties(imageFieldId, { helperText: 'helper text', isHelperTextHidden: true });

                const { queryByTestId } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );
                expect(queryByTestId('e-field-helper-text')).toBeNull();
            });
        });

        describe('zoom controls', () => {
            it('should not render the zoom controls by default', () => {
                const { queryByTestId } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );
                expect(queryByTestId('e-preview-field-zoom-out')).toBeNull();
                expect(queryByTestId('e-preview-field-zoom-in')).toBeNull();
                expect(queryByTestId('e-preview-field-zoom-level')).toBeNull();
            });

            it('should render with zoom controls if zooming is enabled', () => {
                setPreviewFieldProperties(imageFieldId, { canZoom: true });

                const { baseElement, queryByTestId } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );

                const zoomIndicator = baseElement.querySelector('input[data-testid="e-preview-field-zoom-level"]');
                expect(queryByTestId('e-preview-field-zoom-out')).not.toBeNull();
                expect(queryByTestId('e-preview-field-zoom-in')).not.toBeNull();
                expect(zoomIndicator).not.toBeNull();
                expect(zoomIndicator).toHaveValue('100%');
            });

            it('should render with externally set zoom level', () => {
                setPreviewFieldProperties(imageFieldId, { canZoom: true, defaultZoomLevel: 0.5 });

                const { baseElement } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );

                const zoomIndicator = baseElement.querySelector('input[data-testid="e-preview-field-zoom-level"]');
                expect(zoomIndicator).toHaveValue('50%');
            });

            it('should zoom out when the zoom button is clicked until the limit is reached', () => {
                setPreviewFieldProperties(imageFieldId, { canZoom: true });

                const { queryByTestId, baseElement } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );

                const zoomOutButton = queryByTestId('e-preview-field-zoom-out')!;
                const zoomIndicator = baseElement.querySelector('input[data-testid="e-preview-field-zoom-level"]');
                expect(zoomIndicator).toHaveValue('100%');
                fireEvent.click(zoomOutButton);
                expect(zoomIndicator).toHaveValue('75%');
                expect(zoomOutButton).toBeEnabled();
                fireEvent.click(zoomOutButton);
                expect(zoomIndicator).toHaveValue('50%');
                expect(zoomOutButton).toBeEnabled();
                fireEvent.click(zoomOutButton);
                expect(zoomIndicator).toHaveValue('25%');
                expect(zoomOutButton).toBeDisabled();
            });

            it('should zoom in when the zoom button is clicked until the limit is reached', () => {
                setPreviewFieldProperties(imageFieldId, { canZoom: true });

                const { queryByTestId, baseElement } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );

                const zoomInButton = queryByTestId('e-preview-field-zoom-in')!;
                const zoomIndicator = baseElement.querySelector('input[data-testid="e-preview-field-zoom-level"]');
                expect(zoomIndicator).toHaveValue('100%');
                fireEvent.click(zoomInButton);
                expect(zoomIndicator).toHaveValue('125%');
                expect(zoomInButton).toBeEnabled();
                fireEvent.click(zoomInButton);
                expect(zoomIndicator).toHaveValue('150%');
                expect(zoomInButton).toBeEnabled();
                fireEvent.click(zoomInButton);
                expect(zoomIndicator).toHaveValue('175%');
                expect(zoomInButton).toBeEnabled();
                fireEvent.click(zoomInButton);
                expect(zoomIndicator).toHaveValue('200%');
                expect(zoomInButton).toBeEnabled();
                fireEvent.click(zoomInButton);
                expect(zoomIndicator).toHaveValue('225%');
                expect(zoomInButton).toBeEnabled();
                fireEvent.click(zoomInButton);
                expect(zoomIndicator).toHaveValue('250%');
                expect(zoomInButton).toBeEnabled();
                fireEvent.click(zoomInButton);
                expect(zoomIndicator).toHaveValue('275%');
                expect(zoomInButton).toBeEnabled();
                fireEvent.click(zoomInButton);
                expect(zoomIndicator).toHaveValue('300%');
                expect(zoomInButton).toBeEnabled();
                fireEvent.click(zoomInButton);
                expect(zoomIndicator).toHaveValue('325%');
                expect(zoomInButton).toBeEnabled();
                fireEvent.click(zoomInButton);
                expect(zoomIndicator).toHaveValue('350%');
                expect(zoomInButton).toBeEnabled();
                fireEvent.click(zoomInButton);
                expect(zoomIndicator).toHaveValue('375%');
                expect(zoomInButton).toBeEnabled();
                fireEvent.click(zoomInButton);
                expect(zoomIndicator).toHaveValue('400%');
                expect(zoomInButton).toBeDisabled();
            });
        });

        describe('download button', () => {
            it('should not render the download button by default', () => {
                const { queryByTestId } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );
                expect(queryByTestId('e-preview-field-download')).toBeNull();
            });

            it('should render with the download button if downloading is enabled', async () => {
                setPreviewFieldProperties(imageFieldId, { canDownload: true });

                const { queryByTestId } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );

                await waitFor(() => {
                    expect(queryByTestId('e-preview-field-download')).not.toBeNull();
                });
            });
        });

        describe('print button', () => {
            it('should not render the print button by default', () => {
                const { queryByTestId } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );
                expect(queryByTestId('e-preview-field-print')).toBeNull();
            });

            it('should render image document with the print button if downloading is enabled', async () => {
                setPreviewFieldProperties(imageFieldId, { canPrint: true });

                const { queryByTestId } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );

                await waitFor(() => {
                    expect(queryByTestId('e-preview-field-print')).not.toBeNull();
                });
            });

            it('should render pdf document with the print button if downloading is enabled', async () => {
                setPreviewFieldProperties(pdfFieldId, { canPrint: true });

                const { queryByTestId } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={pdfFieldId} />
                    </Provider>,
                );

                await waitFor(() => {
                    expect(queryByTestId('e-preview-field-print')).not.toBeNull();
                });
            });

            it('should not render docx document with the print button even if downloading is enabled', async () => {
                setPreviewFieldProperties(docxFieldId, { canPrint: true });

                const { queryByTestId } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={docxFieldId} />
                    </Provider>,
                );

                await waitFor(() => {
                    expect(queryByTestId('e-preview-field-print')).toBeNull();
                });
            });
        });

        describe('preview rendering', () => {
            it('should render docx document', async () => {
                const { queryByTestId, queryAllByText } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={docxFieldId} />
                    </Provider>,
                );
                await waitFor(() => {
                    expect(queryByTestId('e-preview-field-document-body')).not.toBeNull();

                    expect(queryAllByText('Lorem ipsum')).not.toHaveLength(0);
                    expect(
                        queryAllByText(
                            'Vivamus dapibus sodales ex, vitae malesuada ipsum cursus convallis. Maecenas sed egestas nulla, ac condimentum orci.',
                        ),
                    ).not.toHaveLength(0);
                });
            });

            it('should render image document', async () => {
                const { queryByTestId } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={imageFieldId} />
                    </Provider>,
                );
                await waitFor(() => {
                    expect(queryByTestId('e-preview-field-document-body')).not.toBeNull();
                    expect(queryByTestId('e-preview-image')).not.toBeNull();
                    expect(queryByTestId('e-preview-image')!).toHaveStyle(
                        'background-image: url(blob:http://localhost:3000/1234)',
                    );
                });
            });

            it('should render text document', async () => {
                const { queryByTestId } = render(
                    <Provider store={getMockStore(mockState)}>
                        <ConnectedPreviewComponent screenId={screenId} elementId={textFieldId} />
                    </Provider>,
                );
                await waitFor(() => {
                    expect(queryByTestId('e-preview-field-document-body')).not.toBeNull();
                    expect(queryByTestId('e-preview-text-content')).not.toBeNull();
                    expect(queryByTestId('e-preview-text-content')!.textContent).toContain(
                        'Which one is correct team name in NBA?',
                    );
                });
            });
        });
    });
});
