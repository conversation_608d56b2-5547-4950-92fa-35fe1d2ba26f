// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Preview component connected Snapshots should render disabled 1`] = `
.c6 {
  position: relative;
  color: var(--colorsYin030);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c6::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e961";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c1 {
  padding-left: var(--spacing200);
  padding-right: var(--spacing200);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: not-allowed;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  border-color: var(--colorsActionDisabled500);
  color: var(--colorsActionMajorYin030);
  font-size: var(--fontSizes100);
  min-height: 32px;
  padding: 0px;
  width: 32px;
  min-height: 32px;
}

.c1:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c1 .c5 {
  color: var(--colorsActionMajor500);
}

.c1:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c1:hover .c5 {
  color: var(--colorsActionMajorYang100);
}

.c1 .c5 {
  color: var(--colorsActionMajorYin030);
}

.c1:hover {
  background: transparent;
  border-color: var(--colorsActionDisabled500);
  color: var(--colorsActionMajorYin030);
}

.c1:hover .c5 {
  color: var(--colorsActionMajorYin030);
}

.c1 img,
.c1 svg {
  opacity: 0.3;
}

.c1 .c5 {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1px;
  height: 20px;
  width: 20px;
}

.c1 .c5 svg {
  margin-top: 0;
}

.c2 {
  border-radius: var(--borderRadius050);
  min-height: var(--sizing400);
  padding: var(--spacing000) var(--spacing100) var(--spacing000) var(--spacing100);
}

.c2 .c5 {
  position: absolute;
}

.c0 {
  position: relative;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: auto;
}

.c0.c0 .c5 {
  cursor: pointer;
}

.c7 {
  margin: 32px;
  text-align: center;
}

.c3 {
  color: var(--colorsYang100);
  border-color: transparent;
}

.c3 span[data-component='icon'] {
  color: var(--colorsYang100);
}

.c3[disabled] {
  color: var(--colorsYang030);
}

.c3[disabled] span[data-component='icon'] {
  color: var(--colorsYang030);
}

.c4 {
  border-radius: 16px;
  border: 2px solid var(--colorsYang100);
}

<div>
  <div
    class="e-field e-preview-field e-disabled"
    data-testid="e-pdf-field e-field-label-testFieldTitle e-field-bind-test-image-preview-field"
    style="height: 400px;"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <div
      class="e-preview-field-document"
    >
      <div
        class="e-preview-field-toolbar"
      >
        <span
          class="e-preview-field-toolbar-file-name"
          data-testid="e-preview-field-toolbar-file-name"
        />
        <span
          class="e-preview-field-toolbar-spacer"
        />
        <div
          class="e-preview-field-toolbar-main-options"
        >
          <div
            class="e-preview-field-toolbar-more-options"
          >
            <div
              class="c0"
              data-component="action-popover-wrapper"
              id="ActionPopoverButton_testcarb-onco-mpon-ents-uniqguidmock"
            >
              <button
                aria-controls="ActionPopoverMenu_testcarb-onco-mpon-ents-uniqguidmock"
                aria-expanded="false"
                aria-haspopup="true"
                aria-label="More options"
                class="c1 c2 c3 c4"
                data-component="button-minor"
                data-element="action-popover-button"
                data-testid="e-preview-field-more-options"
                disabled=""
                draggable="false"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="c5 c6"
                  color="--colorsActionMajor500"
                  data-component="icon"
                  data-element="ellipsis_vertical"
                  data-role="icon"
                  disabled=""
                  font-size="small"
                  type="ellipsis_vertical"
                />
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-label="Loading"
        class="c7"
        data-component="loader-bar"
        role="progressbar"
      >
        Loading
      </div>
      <iframe
        data-testid="e-preview-component-print-iframe"
        style="display: none;"
        title="print ref"
      />
    </div>
  </div>
</div>
`;

exports[`Preview component connected Snapshots should render hidden 1`] = `
.c6 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c6:hover {
  color: #006437;
  background-color: transparent;
}

.c6::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e961";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c1 {
  padding-left: var(--spacing200);
  padding-right: var(--spacing200);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 32px;
  padding: 0px;
  width: 32px;
  min-height: 32px;
}

.c1:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c1 .c5 {
  color: var(--colorsActionMajor500);
}

.c1:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c1:hover .c5 {
  color: var(--colorsActionMajorYang100);
}

.c1 .c5 {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1px;
  height: 20px;
  width: 20px;
}

.c1 .c5 svg {
  margin-top: 0;
}

.c2 {
  border-radius: var(--borderRadius050);
  background: transparent;
  padding: var(--spacing100);
  border-color: var(--colorsActionMinor500);
  color: var(--colorsActionMinor500);
  min-height: var(--sizing400);
  padding: var(--spacing000) var(--spacing100) var(--spacing000) var(--spacing100);
}

.c2 .c5 {
  position: absolute;
}

.c2 .c5 {
  color: var(--colorsActionMinor500);
}

.c2:hover {
  color: var(--colorsActionMinorYang100);
  background: var(--colorsActionMinor600);
}

.c0 {
  position: relative;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: auto;
}

.c0.c0 .c5 {
  cursor: pointer;
}

.c7 {
  margin: 32px;
  text-align: center;
}

.c3 {
  color: var(--colorsYang100);
  border-color: transparent;
}

.c3 span[data-component='icon'] {
  color: var(--colorsYang100);
}

.c3[disabled] {
  color: var(--colorsYang030);
}

.c3[disabled] span[data-component='icon'] {
  color: var(--colorsYang030);
}

.c4 {
  border-radius: 16px;
  border: 2px solid var(--colorsYang100);
}

<div>
  <div
    class="e-field e-preview-field e-hidden"
    data-testid="e-pdf-field e-field-label-testFieldTitle e-field-bind-test-image-preview-field"
    style="height: 400px;"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <div
      class="e-preview-field-document"
    >
      <div
        class="e-preview-field-toolbar"
      >
        <span
          class="e-preview-field-toolbar-file-name"
          data-testid="e-preview-field-toolbar-file-name"
        />
        <span
          class="e-preview-field-toolbar-spacer"
        />
        <div
          class="e-preview-field-toolbar-main-options"
        >
          <div
            class="e-preview-field-toolbar-more-options"
          >
            <div
              class="c0"
              data-component="action-popover-wrapper"
              id="ActionPopoverButton_testcarb-onco-mpon-ents-uniqguidmock"
            >
              <button
                aria-controls="ActionPopoverMenu_testcarb-onco-mpon-ents-uniqguidmock"
                aria-expanded="false"
                aria-haspopup="true"
                aria-label="More options"
                class="c1 c2 c3 c4"
                data-component="button-minor"
                data-element="action-popover-button"
                data-testid="e-preview-field-more-options"
                draggable="false"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="c5 c6"
                  color="--colorsActionMajor500"
                  data-component="icon"
                  data-element="ellipsis_vertical"
                  data-role="icon"
                  font-size="small"
                  type="ellipsis_vertical"
                />
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-label="Loading"
        class="c7"
        data-component="loader-bar"
        role="progressbar"
      >
        Loading
      </div>
      <iframe
        data-testid="e-preview-component-print-iframe"
        style="display: none;"
        title="print ref"
      />
    </div>
  </div>
</div>
`;

exports[`Preview component connected Snapshots should render with default properties 1`] = `
.c6 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c6:hover {
  color: #006437;
  background-color: transparent;
}

.c6::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e961";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c1 {
  padding-left: var(--spacing200);
  padding-right: var(--spacing200);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 32px;
  padding: 0px;
  width: 32px;
  min-height: 32px;
}

.c1:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c1 .c5 {
  color: var(--colorsActionMajor500);
}

.c1:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c1:hover .c5 {
  color: var(--colorsActionMajorYang100);
}

.c1 .c5 {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1px;
  height: 20px;
  width: 20px;
}

.c1 .c5 svg {
  margin-top: 0;
}

.c2 {
  border-radius: var(--borderRadius050);
  background: transparent;
  padding: var(--spacing100);
  border-color: var(--colorsActionMinor500);
  color: var(--colorsActionMinor500);
  min-height: var(--sizing400);
  padding: var(--spacing000) var(--spacing100) var(--spacing000) var(--spacing100);
}

.c2 .c5 {
  position: absolute;
}

.c2 .c5 {
  color: var(--colorsActionMinor500);
}

.c2:hover {
  color: var(--colorsActionMinorYang100);
  background: var(--colorsActionMinor600);
}

.c0 {
  position: relative;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: auto;
}

.c0.c0 .c5 {
  cursor: pointer;
}

.c7 {
  margin: 32px;
  text-align: center;
}

.c3 {
  color: var(--colorsYang100);
  border-color: transparent;
}

.c3 span[data-component='icon'] {
  color: var(--colorsYang100);
}

.c3[disabled] {
  color: var(--colorsYang030);
}

.c3[disabled] span[data-component='icon'] {
  color: var(--colorsYang030);
}

.c4 {
  border-radius: 16px;
  border: 2px solid var(--colorsYang100);
}

<div>
  <div
    class="e-field e-preview-field"
    data-testid="e-pdf-field e-field-label-testFieldTitle e-field-bind-test-image-preview-field"
    style="height: 400px;"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <div
      class="e-preview-field-document"
    >
      <div
        class="e-preview-field-toolbar"
      >
        <span
          class="e-preview-field-toolbar-file-name"
          data-testid="e-preview-field-toolbar-file-name"
        />
        <span
          class="e-preview-field-toolbar-spacer"
        />
        <div
          class="e-preview-field-toolbar-main-options"
        >
          <div
            class="e-preview-field-toolbar-more-options"
          >
            <div
              class="c0"
              data-component="action-popover-wrapper"
              id="ActionPopoverButton_testcarb-onco-mpon-ents-uniqguidmock"
            >
              <button
                aria-controls="ActionPopoverMenu_testcarb-onco-mpon-ents-uniqguidmock"
                aria-expanded="false"
                aria-haspopup="true"
                aria-label="More options"
                class="c1 c2 c3 c4"
                data-component="button-minor"
                data-element="action-popover-button"
                data-testid="e-preview-field-more-options"
                draggable="false"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="c5 c6"
                  color="--colorsActionMajor500"
                  data-component="icon"
                  data-element="ellipsis_vertical"
                  data-role="icon"
                  font-size="small"
                  type="ellipsis_vertical"
                />
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-label="Loading"
        class="c7"
        data-component="loader-bar"
        role="progressbar"
      >
        Loading
      </div>
      <iframe
        data-testid="e-preview-component-print-iframe"
        style="display: none;"
        title="print ref"
      />
    </div>
  </div>
</div>
`;

exports[`Preview component connected Snapshots should render without value 1`] = `
<div>
  <div
    class="e-field e-preview-field"
    data-testid="e-pdf-field e-field-label-testFieldTitle e-field-bind-test-empty-preview-field"
    style="height: 400px;"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <div
      class="e-preview-field-document"
    >
      <div
        class="e-preview-field-toolbar"
      >
        <span
          class="e-preview-field-toolbar-file-name"
          data-testid="e-preview-field-toolbar-file-name"
        />
        <span
          class="e-preview-field-toolbar-spacer"
        />
      </div>
      <div
        class="e-preview-field-empty-state"
        data-testid="e-preview-field-document-body"
        style="height: 303px;"
      >
        No file selected.
      </div>
      <iframe
        data-testid="e-preview-component-print-iframe"
        style="display: none;"
        title="print ref"
      />
    </div>
  </div>
</div>
`;
