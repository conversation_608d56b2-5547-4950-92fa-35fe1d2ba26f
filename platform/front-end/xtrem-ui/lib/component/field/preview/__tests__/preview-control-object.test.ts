import type { <PERSON><PERSON><PERSON> } from '../../../types';
import { PreviewControlObject } from '../../../control-objects';
import type { PreviewProperties } from '../preview-types';
import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';

describe('Preview control object', () => {
    let previewControlObject: PreviewControlObject;
    let previewProperties: PreviewProperties;

    beforeEach(() => {
        previewProperties = {
            title: 'TEST_FIELD_TITLE',
            canZoom: true,
        };
        previewControlObject = buildControlObject<FieldKey.Preview>(PreviewControlObject, {
            fieldValue: undefined,
            fieldProperties: previewProperties,
        });
    });

    it('should set the title', () => {
        const testFixture = 'Test Preview Field Title';
        expect(previewProperties.title).not.toEqual(testFixture);
        previewControlObject.title = testFixture;
        expect(previewProperties.title).toEqual(testFixture);
    });

    it('should set the default zoom level digits', () => {
        const testFixture = 0.5;
        expect(previewProperties.defaultZoomLevel).not.toEqual(testFixture);
        previewControlObject.defaultZoomLevel = testFixture;
        expect(previewProperties.defaultZoomLevel).toEqual(testFixture);
    });

    it('should set whether zooming is enabled', () => {
        const testFixture = false;
        expect(previewProperties.canZoom).not.toEqual(testFixture);
        previewControlObject.canZoom = testFixture;
        expect(previewProperties.canZoom).toEqual(testFixture);
    });

    it('should set whether downloading is enabled', () => {
        const testFixture = true;
        expect(previewProperties.canDownload).not.toEqual(testFixture);
        previewControlObject.canDownload = testFixture;
        expect(previewProperties.canDownload).toEqual(testFixture);
    });

    it('should set whether printing is enabled', () => {
        const testFixture = true;
        expect(previewProperties.canPrint).not.toEqual(testFixture);
        previewControlObject.canPrint = testFixture;
        expect(previewProperties.canPrint).toEqual(testFixture);
    });

    it('should set whether thumbnails are enabled', () => {
        const testFixture = true;
        expect(previewProperties.hasThumbnailBar).not.toEqual(testFixture);
        previewControlObject.hasThumbnailBar = testFixture;
        expect(previewProperties.hasThumbnailBar).toEqual(testFixture);
    });

    it('should set whether filename is hidden', () => {
        const testFixture = true;
        expect(previewProperties.isFilenameHidden).not.toEqual(testFixture);
        previewControlObject.isFilenameHidden = testFixture;
        expect(previewProperties.isFilenameHidden).toEqual(testFixture);
    });

    it('should set the filename', () => {
        const testFixture = 'test.pdf';
        expect(previewProperties.filename).not.toEqual(testFixture);
        previewControlObject.filename = testFixture;
        expect(previewProperties.filename).toEqual(testFixture);
    });

    it('should set the mime type', () => {
        const testFixture = 'text/csv';
        expect(previewProperties.mimeType).not.toEqual(testFixture);
        previewControlObject.mimeType = testFixture;
        expect(previewProperties.mimeType).toEqual(testFixture);
    });

    it('should set whether loading indicator is displayed', () => {
        const testFixture = true;
        expect(previewProperties.isLoading).not.toEqual(testFixture);
        previewControlObject.isLoading = testFixture;
        expect(previewProperties.isLoading).toEqual(testFixture);
    });
});
