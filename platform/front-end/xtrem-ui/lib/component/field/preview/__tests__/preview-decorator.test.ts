import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata } from '../../../../__tests__/test-helpers';
import type { PreviewDecoratorProperties } from '../preview-types';
import { previewField } from '../preview-decorator';

describe('Preview decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'preview';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        it('should set default values when no component properties provided', () => {
            previewField({})({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});

            const mappedComponentProperties: PreviewDecoratorProperties<Page> =
                pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.canDownload).toBeUndefined();
            expect(mappedComponentProperties.canPrint).toBeUndefined();
            expect(mappedComponentProperties.canZoom).toBeUndefined();
            expect(mappedComponentProperties.isFilenameHidden).toBeUndefined();
            expect(mappedComponentProperties.hasThumbnailBar).toBeUndefined();
            expect(mappedComponentProperties.defaultZoomLevel).toBeUndefined();
        });

        it('should set values when component properties provided', () => {
            previewField({
                canDownload: true,
                canZoom: false,
                canPrint: true,
                defaultZoomLevel: 1.5,
                hasThumbnailBar: true,
                isFilenameHidden: false,
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});

            const mappedComponentProperties: PreviewDecoratorProperties<Page> =
                pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.canDownload).toBe(true);
            expect(mappedComponentProperties.canZoom).toBe(false);
            expect(mappedComponentProperties.canPrint).toBe(true);
            expect(mappedComponentProperties.defaultZoomLevel).toBe(1.5);
            expect(mappedComponentProperties.hasThumbnailBar).toBe(true);
            expect(mappedComponentProperties.isFilenameHidden).toBe(false);
        });
    });
});
