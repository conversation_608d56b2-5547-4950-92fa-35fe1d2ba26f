import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { PreviewComponentProps } from './preview-types';

const ConnectedPreviewComponent = React.lazy(() => import('./preview-component'));

export function AsyncConnectedPreviewComponent(props: FieldComponentExternalProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <ConnectedPreviewComponent {...props} />
        </React.Suspense>
    );
}

const PreviewComponent = React.lazy(() => import('./preview-component').then(c => ({ default: c.PreviewComponent })));

export function AsyncPreviewComponent(props: PreviewComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader size="small" />}>
            <PreviewComponent {...props} />
        </React.Suspense>
    );
}
