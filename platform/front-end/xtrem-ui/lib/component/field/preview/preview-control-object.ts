/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { PreviewProperties } from './preview-types';

/**
 * [Field]{@link EditableFieldControlObject} that holds an image
 */
export class PreviewControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends ReadonlyFieldControlObject<
    CT,
    FieldKey.Preview,
    FieldComponentProps<FieldKey.Preview>
> {
    /** The value of the height attribute of the HTML image (e.g. 100px, 75%, auto, etc.)*/
    get height(): number | undefined {
        return this.getUiComponentProperty('height');
    }

    /** File name for downloaded files. If not set the component tries to figure out the extension and prefix it with `document`, e.g `document.pdf` */
    @FieldControlObjectResolvedProperty<PreviewProperties<CT>, PreviewControlObject<CT>>()
    filename?: string;

    /** Explicit mime-type. If not set, the component would try to figure out the type from the content. */
    @FieldControlObjectResolvedProperty<PreviewProperties<CT>, PreviewControlObject<CT>>()
    mimeType?: string;

    /** Whether the document can be downloaded. */
    @FieldControlObjectResolvedProperty<PreviewProperties<CT>, PreviewControlObject<CT>>()
    canDownload?: boolean;

    /** Whether printing is enabled, please note this feature is not available for all file types. */
    @FieldControlObjectResolvedProperty<PreviewProperties<CT>, PreviewControlObject<CT>>()
    canPrint?: boolean;

    /** Whether zooming is enabled */
    @FieldControlObjectResolvedProperty<PreviewProperties<CT>, PreviewControlObject<CT>>()
    canZoom?: boolean;

    /** Whether the thumbnail bar is enabled, please note this feature is not available for all file types. */
    @FieldControlObjectResolvedProperty<PreviewProperties<CT>, PreviewControlObject<CT>>()
    hasThumbnailBar?: boolean;

    /** Whether the pagination controls are displayed in the header. */
    @FieldControlObjectResolvedProperty<PreviewProperties<CT>, PreviewControlObject<CT>>()
    hasPaginationControls?: boolean;

    /** Externally set loading state. If set to true, a loading bar is displayed even if the field has value */
    @FieldControlObjectResolvedProperty<PreviewProperties<CT>, PreviewControlObject<CT>>()
    isLoading?: boolean;

    /** Default zoom level */
    @FieldControlObjectResolvedProperty<PreviewProperties<CT>, PreviewControlObject<CT>>()
    defaultZoomLevel?: number;

    /** Whether the filename in the header should be hidden  */
    @FieldControlObjectResolvedProperty<PreviewProperties<CT>, PreviewControlObject<CT>>()
    isFilenameHidden?: boolean;
}
