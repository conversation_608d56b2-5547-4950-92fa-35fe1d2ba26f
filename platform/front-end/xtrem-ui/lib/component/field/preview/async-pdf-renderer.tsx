import * as React from 'react';
import LoaderBar from 'carbon-react/esm/components/loader-bar';
import type { PreviewRenderProps } from './preview-types';

const PdfRenderer = React.lazy(() => import('./pdf-renderer').then(c => ({ default: c.PdfRenderer })));

export function AsyncPdfRenderer(props: PreviewRenderProps): React.ReactElement {
    return (
        <React.Suspense fallback={<LoaderBar m="32px" />}>
            <PdfRenderer {...props} />
        </React.Suspense>
    );
}
