import { set } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import type { XtremAppState } from '../../../redux';
import { getStore } from '../../../redux';
import type { ScreenBase } from '../../../service/screen-base';
import { getScreenElement } from '../../../service/screen-base-definition';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import { calculateContainerWidth } from '../../../utils/responsive-utils';
import type { PodProps } from '../../ui/pod/pod-component';
import { Pod } from '../../ui/pod/pod-component';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type {
    EditableFieldComponentProperties,
    FieldComponentExternalProperties,
    NestedFieldsAdditionalProperties,
} from '../field-base-component-types';
import type { VitalPodDecoratorProperties } from './vital-pod-types';

export class VitalPodComponent extends EditableFieldBaseComponent<
    VitalPodDecoratorProperties<ScreenBase, any>,
    any,
    NestedFieldsAdditionalProperties
> {
    onChange = async (bind: string, value: any): Promise<void> => {
        const newValue = { ...this.props.value };
        set(newValue, bind, value);
        handleChange(
            this.props.elementId,
            newValue,
            this.props.setFieldValue,
            this.props.validate,
            this.triggerChangeListener,
        );
    };

    onRemove = (): void =>
        handleChange(
            this.props.elementId,
            null,
            this.props.setFieldValue,
            this.props.validate,
            this.triggerChangeListener,
        );

    onNewPod = async (): Promise<void> => {
        const columns = this.props.fieldProperties.columns;

        let value = columns?.reduce((prev, curr) => {
            return set(prev, convertDeepBindToPathNotNull(curr.properties.bind), null);
        }, {});

        if (this.props.fieldProperties.onAddButtonClick) {
            const state = getStore().getState();
            const screenDefinition = state.screenDefinitions[this.props.screenId];
            const newValue = await this.props.fieldProperties.onAddButtonClick.apply(
                getScreenElement(screenDefinition),
            );
            value = { ...value, ...newValue };
        }

        handleChange(
            this.props.elementId,
            value,
            this.props.setFieldValue,
            this.props.validate,
            this.triggerChangeListener,
        );
    };

    render(): React.ReactNode {
        const { availableColumns, browser, elementId, fieldProperties, screenId, value } = this.props;
        const podWidth = browser && calculateContainerWidth(browser.is, availableColumns || 12, 'small');

        const podProps: PodProps = {
            availableColumns: podWidth,
            baseAttributesDivWrapper: this.getBaseAttributesDivWrapper(
                'vital-pod',
                'e-vital-pod-field',
                this.props.contextType,
                this.props.handlersArguments?.rowValue,
                this.props.isNested,
            ),
            browser,
            contextType: this.props.contextType,
            elementId,
            fieldProperties,
            isDisabled: this.isDisabled(),
            isReadOnly: this.isReadOnly(),
            onBlockClick: this.getClickHandler(),
            onChange: this.onChange,
            onNewPod: this.onNewPod,
            onRemove: this.onRemove,
            onTelemetryEvent: this.props.fieldProperties.onTelemetryEvent,
            screenId,
            validationErrors: this.props.validationErrors,
            value,
        };

        return <Pod {...podProps} />;
    }
}

const extendedMapStateToProps = (
    state: XtremAppState,
    props: FieldComponentExternalProperties,
): EditableFieldComponentProperties<VitalPodDecoratorProperties<ScreenBase, any>, any> => {
    const componentProperties = mapStateToProps()(state, props) as EditableFieldComponentProperties<
        VitalPodDecoratorProperties<ScreenBase, any>,
        any
    >;
    const screenDefinition = state.screenDefinitions[props.screenId];

    if (componentProperties.fieldProperties && componentProperties.fieldProperties.onAddButtonClick) {
        componentProperties.fieldProperties.onAddButtonClick =
            componentProperties.fieldProperties.onAddButtonClick.bind(getScreenElement(screenDefinition));
    }

    if (!componentProperties.fieldProperties.onTelemetryEvent) {
        componentProperties.fieldProperties.onTelemetryEvent = state.applicationContext?.onTelemetryEvent;
    }

    return componentProperties;
};

export const ConnectedVitalPodComponent = connect(extendedMapStateToProps, mapDispatchToProps())(VitalPodComponent);

export default ConnectedVitalPodComponent;
