import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { VitalPodControlObject } from './vital-pod-control-object';
import type { VitalPodDecoratorProperties } from './vital-pod-types';

class VitalPodDecorator extends AbstractFieldDecorator<FieldKey.VitalPod> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = VitalPodControlObject;
}

/**
 * Initializes the decorated member as a [VitalPod]{@link VitalPodControlObject} field with the provided properties.
 *
 * @param properties The properties that the [VitalPod]{@link VitalPodControlObject} field will be initialized with.
 */
export function vitalPodField<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode>(
    properties: VitalPodDecoratorProperties<Extend<T>, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.VitalPod, ReferencedItemType>(
        properties,
        VitalPodDecorator,
        FieldKey.VitalPod,
    );
}

export function vitalPodFieldOverride<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode>(
    properties: ChangeableOverrideDecoratorProperties<
        VitalPodDecoratorProperties<Extend<T>, ReferencedItemType>,
        Extend<T>
    >,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.VitalPod, ReferencedItemType>(properties);
}
