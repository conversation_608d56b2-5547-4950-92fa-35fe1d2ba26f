jest.mock('../../text/async-text-component');
jest.mock('../../text-area/async-text-area-component');
jest.mock('../../reference/async-reference-component');
jest.mock('../../label/async-label-component');
jest.mock('../../numeric/async-numeric-component');
jest.mock('../../date/async-date-component');
jest.mock('../../image/async-image-component');

import { cleanup, render, screen, waitFor, fireEvent } from '@testing-library/react';
import type { VitalPodProperties } from '../vital-pod-types';
import type { ScreenBase } from '../../../../service/screen-base';
import * as React from 'react';
import { Provider } from 'react-redux';
import type { Store } from 'redux';
import { nestedFields } from '../../../..';
import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getStore,
    userEvent,
} from '../../../../__tests__/test-helpers';
import { FieldKey } from '../../../types';
import * as abstractFieldUtils from '../../../../utils/abstract-fields-utils';
import type { AppAction, XtremAppState } from '../../../../redux';
import type { PageDefinition } from '../../../../service/page-definition';
import type * as pageMetadataImport from '../../../../service/page-metadata';
import baseTheme from 'carbon-react/esm/style/themes/sage';
import { ThemeProvider } from 'styled-components';
import type { DropdownActionItem } from '../../traits';
import ConnectedVitalPodComponent from '../vital-pod-component';

import '@testing-library/jest-dom';

let mockStore: Store<XtremAppState, AppAction> | null = null;
jest.mock('../../../../redux', () => {
    const actual = jest.requireActual('../../../../redux');
    return {
        getStore: () => mockStore,
        actions: actual.actions,
        ActionType: actual.ActionType,
    };
});
jest.mock('../../../../redux/store', () => {
    return {
        getStore: () => mockStore,
    };
});

jest.useFakeTimers();

describe('Vital Pod Component', () => {
    const screenId = 'testScreen';
    const elementId = 'testElement';

    const setup = (props: Omit<VitalPodProperties<ScreenBase, any>, 'columns'> = {}, fieldValue: any = null) => {
        const fieldProperties: VitalPodProperties<ScreenBase, any> = {
            ...props,
            ...{
                columns: [
                    nestedFields.text({ bind: 'name', title: 'Name' }),
                    nestedFields.text({ bind: 'addressLine1', title: 'Line1' }),
                    nestedFields.text({ bind: 'addressLine2', title: 'Line2' }),
                    nestedFields.reference({
                        bind: 'country',
                        title: 'Country',
                        node: '@sage/xtrem-show-case/ShowCaseCountry',
                        valueField: 'name',
                        helperTextField: 'code',
                    }),
                    nestedFields.textArea({ bind: 'addressCompressed', title: 'Address compressed' }),
                ],
                node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
                skipDirtyCheck: true,
            },
        };

        const store = () => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(
                screenId,
                {
                    page: { $: { isTransactionInProgress: () => false }, _pageMetadata: { screenId } },
                } as Partial<PageDefinition>,
                {
                    uiComponentProperties: { [screenId]: { skipDirtyCheck: true } },
                } as Partial<pageMetadataImport.PageMetadata>,
            );
            addFieldToState(FieldKey.VitalPod, state, screenId, elementId, fieldProperties, fieldValue);
            mockStore = getStore(state);
        };

        store();
        return render(
            <ThemeProvider theme={baseTheme}>
                <Provider store={mockStore!}>
                    <ConnectedVitalPodComponent elementId={elementId} screenId={screenId} />
                </Provider>
            </ThemeProvider>,
        );
    };

    let handleChangeSpy: jest.SpyInstance;

    beforeEach(() => {
        handleChangeSpy = jest.spyOn(abstractFieldUtils, 'handleChange');
    });

    afterEach(async () => {
        mockStore = null;
        handleChangeSpy.mockReset();
        await cleanup();
    });

    it('Should render with add new button and click creates a new pod', async () => {
        setup();
        const newButton = screen.getByTestId('e-pod-add-new');
        expect(newButton).toBeInTheDocument();
        expect(handleChangeSpy).not.toHaveBeenCalled();
        await userEvent.click(newButton);
        expect(handleChangeSpy).toHaveBeenCalled();
        await waitFor(() => {
            expect(newButton).not.toBeInTheDocument();
        });
        await waitFor(() => {
            const addressLineField = screen.getByTestId('e-field-bind-addressLine1', { exact: false });
            expect(addressLineField).toBeInTheDocument();
        });
    });

    it('Should render with title', () => {
        const wrapper = setup({ title: 'test pod' }, { _id: '124', vegetable: 'patata' });
        const title = wrapper.baseElement.querySelector('.e-pod-title');
        expect(title).toBeInTheDocument();
        expect(title!).toHaveTextContent('test pod');
    });

    it('Should render without title', () => {
        setup(undefined, { _id: '124', vegetable: 'patata' });
        expect(screen.queryByTestId('e-field-label'))!.toBeNull();
    });

    it('Should render with header label', () => {
        const wrapper = setup(
            {
                title: 'test pod',
                headerLabel: nestedFields.label({ bind: 'vegetable', title: 'Header label' }),
            },
            { _id: '124', vegetable: 'patata' },
        );
        const headerLabel = wrapper.baseElement.querySelector('.e-pod-header-label');
        expect(headerLabel).toBeInTheDocument();
        expect(headerLabel!).toHaveTextContent('patata');
    });

    it('Should render without header label', () => {
        const wrapper = setup({ title: 'test pod' }, { _id: '124', vegetable: 'patata' });
        const headerLabel = wrapper.baseElement.querySelector('.e-pod-collection-header-label');
        expect(headerLabel).toBeNull();
    });

    it('Should render with placeholder text when no value is provided', () => {
        setup();
        const placeholderText = screen.queryByText('No data to display');
        expect(placeholderText).toBeTruthy();
    });

    it('Should render with custom placeholder text when no value is provided', () => {
        setup({ placeholder: 'No Patatas to display' });
        const placeholderText = screen.queryByText('No Patatas to display');
        expect(placeholderText).toBeTruthy();
    });

    it('Should render with add new button because no value', () => {
        setup();
        const newButton = screen.queryByTestId('e-pod-add-new')!;
        expect(newButton!).toBeTruthy();
        expect(newButton.querySelector('span[data-element="main-text"]')!.innerHTML).toBe('Add an item');
    });

    it('Should render with info message', () => {
        const wrapper = setup({ title: 'test pod', infoMessage: 'Hi there!' }, { _id: '124', vegetable: 'patata' });
        const icon = wrapper.baseElement.querySelector('.e-icon-info-message');
        expect(icon).not.toBeNull();
    });

    it('Should render with info message callback', () => {
        const wrapper = setup(
            { title: 'test pod', infoMessage: () => 'Hi there!' },
            { _id: '124', vegetable: 'patata' },
        );
        const icon = wrapper.baseElement.querySelector('.e-icon-info-message');
        expect(icon).not.toBeNull();
    });

    it('Should render with warning message', () => {
        const wrapper = setup({ title: 'test pod', warningMessage: 'Hi there!' }, { _id: '124', vegetable: 'patata' });
        const icon = wrapper.baseElement.querySelector('.e-icon-warning-message');
        expect(icon).not.toBeNull();
    });

    it('Should render with warning message callback', () => {
        const wrapper = setup(
            { title: 'test pod', warningMessage: () => 'Hi there!' },
            { _id: '124', vegetable: 'patata' },
        );
        const icon = wrapper.baseElement.querySelector('.e-icon-warning-message');
        expect(icon).not.toBeNull();
    });

    it('Should prioritize warning over info messages', () => {
        const wrapper = setup(
            { title: 'test pod', warningMessage: () => 'Hi there!', infoMessage: 'Hi again.' },
            { _id: '124', vegetable: 'patata' },
        );
        const warningIcon = wrapper.baseElement.querySelector('.e-icon-warning-message');
        expect(warningIcon).not.toBeNull();
        const infoIcon = wrapper.baseElement.querySelector('.e-icon-info-message');
        expect(infoIcon).toBeNull();
    });

    it('Should render with custom text button for add new pod', () => {
        setup({ addButtonText: 'Add new patata' });
        const newButton = screen.queryByTestId('e-pod-add-new')!;
        expect(newButton).toBeTruthy();
        expect(newButton.querySelector('span[data-element="main-text"]')!.innerHTML).toBe('Add new patata');
    });

    it('Should call handle change when input is modified', async () => {
        setup({}, {});
        await waitFor(() => {
            const addressLineField = screen.getByTestId('e-field-bind-addressLine1', { exact: false });
            expect(addressLineField).toBeInTheDocument();
        });
        expect(handleChangeSpy).not.toHaveBeenCalled();

        const input = screen.getByTestId('e-field-bind-addressLine1', { exact: false })!.querySelector('input')!;
        await fireEvent.change(input, { target: { value: 'New value' } });
        await fireEvent.blur(input, { target: { value: 'New value' } });

        expect(handleChangeSpy).toHaveBeenCalledWith(
            'addressLine1',
            'New value',
            expect.anything(),
            expect.anything(),
            expect.anything(),
        );
    });

    it('Should call handle change when text area is modified', async () => {
        setup({}, {});
        await waitFor(() => {
            const addressLineField = screen.getByTestId('e-field-bind-addressCompressed', { exact: false });
            expect(addressLineField).toBeInTheDocument();
        });
        expect(handleChangeSpy).not.toHaveBeenCalled();

        const input = screen
            .getByTestId('e-field-bind-addressCompressed', { exact: false })!
            .querySelector('textarea')!;
        await fireEvent.change(input, { target: { value: 'New value\nWith two lines' } });
        await fireEvent.blur(input, { target: { value: 'New value\nWith two lines' } });

        expect(handleChangeSpy).toHaveBeenCalledWith(
            'addressCompressed',
            'New value\nWith two lines',
            expect.anything(),
            expect.anything(),
            expect.anything(),
        );
    });

    describe('dropdown actions', () => {
        let action1mock: jest.Mock<any>;
        let action2mock: jest.Mock<any>;
        let isDisabled2mock: jest.Mock<any>;
        let isHidden1Mock: jest.Mock<any>;
        let isHidden2Mock: jest.Mock<any>;
        let dropdownActions: DropdownActionItem<any>[] = [];

        beforeEach(() => {
            action1mock = jest.fn();
            action2mock = jest.fn();
            isDisabled2mock = jest.fn().mockReturnValue(true);
            isHidden1Mock = jest.fn().mockReturnValue(false);
            isHidden2Mock = jest.fn().mockReturnValue(true);

            dropdownActions = [
                { icon: 'add', title: 'Test title', onClick: action1mock },
                {
                    icon: 'add',
                    title: 'Test title',
                    onClick: action2mock,
                    isHidden: isHidden1Mock,
                    isDisabled: isDisabled2mock,
                },
                {
                    icon: 'add',
                    title: 'Test hidden',
                    onClick: action2mock,
                    isHidden: isHidden2Mock,
                    isDisabled: jest.fn().mockReturnValue(false),
                },
            ];
        });

        it('should open the menu when the user clicks on the icon', async () => {
            const wrapper = setup({ dropdownActions });
            expect(
                wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
            ).toHaveLength(0);

            await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

            expect(
                wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
            ).toHaveLength(2);
        });

        it('should call the isDisabled callback on menu rendering', async () => {
            const wrapper = setup({ dropdownActions });

            expect(isDisabled2mock).toHaveBeenCalledTimes(1);

            await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

            expect(isDisabled2mock).toHaveBeenCalledTimes(3);
        });

        it('should not render if an item is hidden', async () => {
            const wrapper = setup({ dropdownActions });
            expect(isHidden2Mock).toHaveBeenCalledTimes(1);

            await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

            expect(isHidden2Mock).toHaveBeenCalledTimes(3);

            expect(document.querySelectorAll('[data-component="action-popover"] button[type="button"]').length).toEqual(
                2,
            );
        });

        it('should render conditionally displayed items', async () => {
            isHidden2Mock.mockReturnValue(false);

            const wrapper = setup({ dropdownActions });

            expect(isHidden2Mock).toHaveBeenCalledTimes(1);

            await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

            expect(isHidden2Mock).toHaveBeenCalledTimes(3);

            expect(document.querySelectorAll('[data-component="action-popover"] button[type="button"]').length).toEqual(
                3,
            );
        });

        it('should trigger the event listener when the user clicks on an item', async () => {
            const wrapper = setup({ dropdownActions });

            await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

            expect(action1mock).not.toHaveBeenCalled();

            await userEvent.click(
                document.querySelectorAll('[data-component="action-popover"] button[type="button"]')[0],
            );
            expect(action1mock).toHaveBeenCalledTimes(1);
            expect(action1mock).toHaveBeenCalledWith();
        });

        it('should trigger the right event listener even if an item is hidden in front of the item in the menu', async () => {
            isHidden1Mock.mockReturnValue(true);
            isHidden2Mock.mockReturnValue(false);
            const wrapper = setup({ dropdownActions });

            await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

            expect(action2mock).not.toHaveBeenCalled();
            await userEvent.click(
                document.querySelectorAll('[data-component="action-popover"] button[type="button"]')[1],
            );

            expect(action1mock).not.toHaveBeenCalled();
            expect(action2mock).toHaveBeenCalledTimes(1);
            expect(action2mock).toHaveBeenCalledWith();
        });

        it('should not trigger the event listener when the user clicks on a disabled item', async () => {
            const wrapper = setup({ dropdownActions });
            await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

            expect(action1mock).not.toHaveBeenCalled();
            const actionButton = document.querySelectorAll(
                '[data-component="action-popover"] button[type="button"]',
            )[1];
            expect(actionButton).toHaveTextContent('Test title');
            expect(actionButton).toHaveAttribute('aria-disabled', 'true');
            await userEvent.click(actionButton);
            expect(action2mock).not.toHaveBeenCalled();
        });
    });
});
