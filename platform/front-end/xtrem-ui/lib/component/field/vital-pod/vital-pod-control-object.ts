/**
 * @packageDocumentation
 * @module root
 */
import type { ClientNode } from '@sage/xtrem-client';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import { showToast } from '../../../service/toast-service';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { VitalPodProperties } from './vital-pod-types';

/**
 * [Field]{@link EditableFieldControlObject} that holds a value from a set of given values.
 */
export class VitalPodControlObject<
    NodeType extends ClientNode = any,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.VitalPod, FieldComponentProps<FieldKey.VitalPod>> {
    static readonly defaultUiProperties: Partial<VitalPodProperties> = {
        ...EditableFieldControlObject.defaultUiProperties,
    };

    /** Graphql filter that will restrict the results of the reference field */
    get filter(): GraphQLFilter<NodeType> | undefined {
        return resolveByValue({
            fieldValue: undefined,
            propertyValue: this.getUiComponentProperty('filter'),
            rowValue: undefined,
            screenId: this.screenId,
            skipHexFormat: true,
        });
    }

    /** Graphql filter that will restrict the results of the reference field */
    set filter(filter: GraphQLFilter<NodeType> | undefined) {
        this.setUiComponentProperties('filter', filter);
        this.refresh().catch(() => {
            /* Intentional fire and forget */
        });
    }

    /** The GraphQL node that the field suggestions will be fetched from */
    get node(): string {
        return String(this.getUiComponentProperty('node'));
    }

    @ControlObjectProperty<VitalPodProperties<CT, NodeType>, VitalPodControlObject<NodeType, CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<VitalPodProperties<CT, NodeType>, VitalPodControlObject<NodeType, CT>>()
    /** Whether the value of the pod can be unset */
    canRemove?: boolean;

    @ControlObjectProperty<VitalPodProperties<CT, NodeType>, VitalPodControlObject<NodeType, CT>>()
    /** Placeholder to be displayed in the field body */
    placeholder?: string;

    async refresh(): Promise<void> {
        await this._refresh({ keepPageInfo: true }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }

    focus(): void {
        this._focus();
    }
}
