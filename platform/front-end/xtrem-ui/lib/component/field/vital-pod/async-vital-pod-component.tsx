import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import type { VitalPodComponentProperties } from './vital-pod-types';

const ConnectedVitalPodComponent = React.lazy(() => import('./vital-pod-component'));

export function AsyncConnectedVitalPodComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="200px" />}>
            <ConnectedVitalPodComponent {...props} />
        </React.Suspense>
    );
}

const VitalPodComponent = React.lazy(() =>
    import('./vital-pod-component').then(e => ({ default: e.VitalPodComponent })),
);

export function AsyncVitalPodComponent(props: VitalPodComponentProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={false} bodyHeight="200px" />}>
            <VitalPodComponent {...props} />
        </React.Suspense>
    );
}
