PATH: XTREEM/Client+Framework/UI+Field+Widgets

# Introduction

A page is defined with a set of component fields, each field type has its own decorator properties associated to it but everyone has the access binding decorator property in common.

# Access binding decorator properties:

-   **access**: An object with the properties **node** and **bind** to defined an explicit field access binding.

If the field does not provide an explicit **access** decorator property then:

- **node** value is resolved by looking at the **node** binding decorator property if defined (for example for fields decorated with **ui.decorators.tableField** or **ui.decorators.referenceField**), then the **node** of the page decorator properties.
- **bind** value is resolved by looking at the **bind** binding decorator property if defined (for example for **ui.decorators.treeField**), then if the field is not transient the field name is used.
