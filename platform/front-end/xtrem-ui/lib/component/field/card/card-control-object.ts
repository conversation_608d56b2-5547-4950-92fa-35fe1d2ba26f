/**
 * @packageDocumentation
 * @module root
 */
import type { ClientNode } from '@sage/xtrem-client';
import { showToast } from '../../../service/toast-service';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { CardProperties } from './card-types';

/**
 * [Field]{@link EditableFieldControlObject} that holds a value from a set of given values.
 */
export class CardControlObject<
    NodeType extends ClientNode = any,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends ReadonlyFieldControlObject<CT, FieldKey.Card, FieldComponentProps<FieldKey.Card, CT, NodeType>> {
    static readonly defaultUiProperties: Partial<CardProperties> = {
        ...ReadonlyFieldControlObject.defaultUiProperties,
    };

    /** The GraphQL node that the field suggestions will be fetched from */
    get node(): string {
        return String(this.getUiComponentProperty('node'));
    }

    @ControlObjectProperty<CardProperties<CT, NodeType>, CardControlObject<NodeType, CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    async refresh(): Promise<void> {
        await this._refresh({ keepPageInfo: true }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }

    focus(): void {
        this._focus();
    }
}
