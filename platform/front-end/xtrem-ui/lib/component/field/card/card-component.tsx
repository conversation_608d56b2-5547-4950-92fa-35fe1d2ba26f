import * as React from 'react';
import { connect } from 'react-redux';
import { ReadonlyFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import { getComponentClass, getDataTestIdAttribute } from '../../../utils/dom';
import type { CardComponentProps } from '../../ui/card/card-component';
import { CardComponent as Card } from '../../ui/card/card-component';
import type { CardProperties } from './card-types';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { getImagePlaceholderValue } from '../../../utils/nested-field-utils';
import { FieldKey } from '../../types';
import type { ImageDecoratorProperties } from '../../decorator-properties';
import * as tokens from '@sage/design-tokens/js/base/common';

export class CardComponent extends ReadonlyFieldBaseComponent<CardProperties, any> {
    render(): React.ReactElement {
        const title = this.getTitle();
        const isDisabled = this.getResolvedProperty('isDisabled');

        const { isTitleHidden, helperText, onClick } = this.props.fieldProperties;
        const cardDefinition = { ...this.props.fieldProperties.cardDefinition };
        if (
            cardDefinition.image &&
            cardDefinition.image.type === FieldKey.Image &&
            !(cardDefinition.image.properties as ImageDecoratorProperties).placeholderValue
        ) {
            cardDefinition.image.properties = {
                ...cardDefinition.image.properties,
                placeholderValue: (): string | undefined =>
                    getImagePlaceholderValue(this.props.value, cardDefinition.title),
            };
        }

        if (cardDefinition.image && cardDefinition.image.type === FieldKey.Icon) {
            cardDefinition.image.properties = {
                ...cardDefinition.image.properties,
                color: tokens.colorsUtilityMajor300,
                backgroundColor: tokens.colorsActionMinor050,
                backgroundShape: 'circle',
                backgroundSize: 'large',
            };
        }

        const cardProps: CardComponentProps = {
            cardDefinition,
            screenId: this.props.screenId,
            parentElementId: this.props.elementId,
            value: this.props.value,
            onCardClick: onClick && !isDisabled ? this.getClickHandler() : undefined,
            isClickIndicatorHidden: true,
        };

        return (
            <div
                data-testid={getDataTestIdAttribute('card', title, this.props.elementId)}
                className={getComponentClass(this.props, 'e-card-field')}
            >
                {!isTitleHidden && title && <FieldLabel label={title} />}
                {this.props.value && <Card {...cardProps} />}
                <HelperText helperText={helperText} />
            </div>
        );
    }
}

export const ConnectedCardComponent = connect(mapStateToProps(), mapDispatchToProps())(CardComponent);

export default ConnectedCardComponent;
