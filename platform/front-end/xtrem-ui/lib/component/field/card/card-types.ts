import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { BlockControlObject, SectionControlObject } from '../../container/container-control-objects';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type { CardDefinition } from '../../ui/card/card-component';
import type { BaseReadonlyComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { Clickable, ExtensionField, HasHelperText, HasNode, HasParent } from '../traits';

export interface CardProperties<CT extends ScreenExtension<CT> = ScreenBase, NodeType extends ClientNode = any>
    extends ReadonlyFieldProperties<CT>,
        Clickable<CT>,
        Extension<PERSON>ield<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>>,
        HasHelperText,
        HasNode<CT> {
    cardDefinition: CardDefinition<CT, NodeType>;
}

export interface CardDecoratorProperties<CT extends ScreenBase, NodeType extends ClientNode = any>
    extends Omit<CardProperties<CT, NodeType>, '_controlObjectType'> {}

export type CardComponentProps = BaseReadonlyComponentProperties<
    CardProperties,
    { query: { totalCard: number } },
    NestedFieldsAdditionalProperties
>;
