PATH: XTREEM/UI+Field+Widgets/Card

## Introduction

Card field are used to represent a reference data in a readonly non-tabular way using small block like components.

## Example:

```ts
    @ui.decorators.cardField<ShowCaseProvider, ShowCaseProduct>({
        isFullWidth: true,
        parent() {
            return this.detailPanelHeaderDetailSection;
        },
        isTransient: true,
        cardDefinition: {
            title: ui.nestedFields.text({ bind: 'description' }),
            titleRight: ui.nestedFields.label({ bind: 'category' }),
            line2: ui.nestedFields.numeric({ bind: 'amount' }),
            image: ui.nestedFields.image({ bind: 'imageField' }),
            progressBar: ui.nestedFields.progress({ bind: 'somePercentageValue' }),
        },
    })
    productCard: ui.fields.Card;

```

### Display decorator properties

- **cardDefinition**: See below
- **helperText**: The helper text that is displayed below the Cards. It is automatically picked up by the i18n engine and externalized.
- **isDisabled**: Whether all interactions (including field editing, selection, adding or removing Cards) are disabled or not. Defaults to false.
- **isFullWidth**: Whether the table spans all the parent width or not. It can also be defined as callback function that returns a boolean. Defaults to true.
- **isHidden**: Whether the table is hidden or not. Defaults to false.
- **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
- **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
- **title**: The title that is displayed above the table. It is automatically picked up by the i18n engine and externalized.
- **width**: Specifies the field's width. The width can be defined by using field size categories which are remapped to actually width values by the framework depending on the screen size and the container size that the field is in.

### Card definition
The following types can be used as nested fields for the card body (`title`, `titleRight`, `line2`, `line2Right`, `line3`, `line3Right`, `line4`, `line4Right`, `line5`, `line5Right` and etc): 'checkbox', 'icon', 'image', 'label', 'link', 'numeric', 'progress', 'reference', 'text', 'date'.

Card body properties:
-   **title**: Title of the item card.
-   **titleRight**: Support for title, being displayed at its right. Optional property.
-   **line2**: Text that is going to be placed under the `title` in the item card. Optional property.
-   **line2Right**: Text that is going to be placed under the `titleRight` property in the item card. Optional property.
-   **line3**: Text that is going to be placed under the `line2` in the item card. Optional property.
-   **line3Right**: Text that is going to be placed under the `line2Right` property in the item card. Optional property.
-   **line4**: Text that is going to be placed under the `line3` in the item card. Optional property.
-   **line4Right**: Text that is going to be placed under the `line3Right` property in the item card. Optional property.
-   **line5**: Text that is going to be placed under the `line4` in the item card. Optional property.
-   **line5Right**: Text that is going to be placed under the `line4Right` property in the item card. Optional property.

Special card properties:
-   **image**: An image or icon field is displayed on the left side of the card. It is positioned vertically to the center. Optional property.
-   **progressBar**: A progress field which is displayed below the card content. Optional property.

### Binding decorator properties

-   **bind**: The GraphQL object's property that the Card collection value is bound to. If not provided, the Card collection name is used.
-   **isTransient**: If marked as true, the Card collection will be excluded from the automatic data binding process (i.e. no data will be fetched from the server). The default value is false.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **node**: The name of the node that the Card collection represents, it must be defined if you want to use the advanced filtering capabilities.

### Event handler decorator properties

- **onClick**: Event triggered when any parts of the record is clicked, no arguments provided.
## Runtime functions

-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
### Column properties

Please refer to the documentation of the corresponding nested field type.


## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/CardField) and [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/CardBlock).
