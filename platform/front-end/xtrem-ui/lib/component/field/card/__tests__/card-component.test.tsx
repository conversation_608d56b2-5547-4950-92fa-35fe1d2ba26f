jest.mock('../../text/async-text-component');
jest.mock('../../text-area/async-text-area-component');
jest.mock('../../reference/async-reference-component');
jest.mock('../../label/async-label-component');
jest.mock('../../numeric/async-numeric-component');
jest.mock('../../date/async-date-component');
jest.mock('../../image/async-image-component');
jest.mock('../../icon/async-icon-component');

import { cleanup, render, screen } from '@testing-library/react';
import type { ScreenBase } from '../../../../service/screen-base';
import * as React from 'react';
import { Provider } from 'react-redux';
import type { Store } from 'redux';
import { nestedFields } from '../../../..';
import { addFieldToState, getMockPageDefinition, getMockState, getStore } from '../../../../__tests__/test-helpers';
import { FieldKey } from '../../../types';
import type { AppAction, XtremAppState } from '../../../../redux';
import type { PageDefinition } from '../../../../service/page-definition';
import type * as pageMetadataImport from '../../../../service/page-metadata';
import type { CardProperties } from '../card-types';
import { ConnectedCardComponent } from '../card-component';

let mockStore: Store<XtremAppState, AppAction> | null = null;

jest.mock('../../../../redux', () => {
    const actual = jest.requireActual('../../../../redux');
    return {
        getStore: () => mockStore,
        actions: actual.actions,
        ActionType: actual.ActionType,
    };
});
jest.mock('../../../../redux/store', () => {
    return {
        getStore: () => mockStore,
    };
});

describe('Card Component', () => {
    const screenId = 'testScreen';
    const elementId = 'testElement';
    const setup = (props?: CardProperties<ScreenBase, any>) => {
        const fieldProperties: CardProperties<ScreenBase, any> = {
            ...props,
            ...{
                cardDefinition: {
                    title: nestedFields.text({ bind: 'description' }),
                    titleRight: nestedFields.label({ bind: 'category' }),
                    line2: nestedFields.numeric({ bind: 'amount' }),
                    image: nestedFields.image({ bind: 'imageField' }),
                },
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                skipDirtyCheck: true,
            },
        };

        const store = () => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(
                screenId,
                {
                    page: { $: { isTransactionInProgress: () => false }, _pageMetadata: { screenId } },
                } as Partial<PageDefinition>,
                {
                    uiComponentProperties: { [screenId]: { skipDirtyCheck: true } },
                } as Partial<pageMetadataImport.PageMetadata>,
            );
            const fieldValue = {
                _id: '1',
                description: 'patata',
                category: 'fritas',
                amount: '2',
                image: 'fakeBase64',
            };
            addFieldToState(FieldKey.Card, state, screenId, elementId, fieldProperties, fieldValue);
            mockStore = getStore(state);
        };

        store();
        return render(
            <Provider store={mockStore!}>
                <ConnectedCardComponent elementId={elementId} screenId={screenId} />
            </Provider>,
        );
    };

    afterEach(async () => {
        mockStore = null;
        await cleanup();
    });

    it('should render a card component', async () => {
        setup();
        const card = screen.getByTestId('e-card-field e-field-bind-testElement');
        expect(card).toBeInTheDocument();
    });

    it('should render component with a reference', async () => {
        const fieldProperties: CardProperties<ScreenBase, any> = {
            cardDefinition: {
                title: nestedFields.reference({
                    bind: 'product',
                    valueField: 'title',
                    node: '@sage/xtrem-test/TestNode',
                }),
                titleRight: nestedFields.label({ bind: 'category' }),
                line2: nestedFields.numeric({ bind: 'amount' }),
                image: nestedFields.image({ bind: 'imageField' }),
            },
            node: '@sage/xtrem-show-case/ShowCaseProduct',
        };
        const state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(
            screenId,
            {
                page: { $: { isTransactionInProgress: () => false }, _pageMetadata: { screenId } },
            } as Partial<PageDefinition>,
            {
                uiComponentProperties: { [screenId]: { skipDirtyCheck: true } },
            } as Partial<pageMetadataImport.PageMetadata>,
        );
        const fieldValue = {
            _id: '1',
            product: {
                _id: '123',
                title: 'Product title',
            },
            category: 'fritas',
            amount: '2',
            image: 'fakeBase64',
        };
        addFieldToState(FieldKey.Card, state, screenId, elementId, fieldProperties, fieldValue);
        mockStore = getStore(state);
        const { getByTestId } = render(
            <Provider store={mockStore!}>
                <ConnectedCardComponent elementId={elementId} screenId={screenId} />
            </Provider>,
        );
        const referenceField = getByTestId('e-reference-field e-field-bind-product');
        expect(referenceField).toHaveTextContent('Product title');
    });

    it('should render with an icon image', () => {
        const fieldProperties: CardProperties<ScreenBase, any> = {
            cardDefinition: {
                title: nestedFields.reference({
                    bind: 'product',
                    valueField: 'title',
                    node: '@sage/xtrem-test/TestNode',
                }),
                titleRight: nestedFields.label({ bind: 'category' }),
                line2: nestedFields.numeric({ bind: 'amount' }),
                image: nestedFields.icon({ bind: 'icon' }),
            },
            node: '@sage/xtrem-show-case/ShowCaseProduct',
        };
        const state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(
            screenId,
            {
                page: { $: { isTransactionInProgress: () => false }, _pageMetadata: { screenId } },
            } as Partial<PageDefinition>,
            {
                uiComponentProperties: { [screenId]: { skipDirtyCheck: true } },
            } as Partial<pageMetadataImport.PageMetadata>,
        );
        const fieldValue = {
            _id: '1',
            product: {
                _id: '123',
                title: 'Product title',
            },
            category: 'fritas',
            amount: '2',
            icon: 'print',
        };

        addFieldToState(FieldKey.Card, state, screenId, elementId, fieldProperties, fieldValue);
        mockStore = getStore(state);
        const { getByTestId } = render(
            <Provider store={mockStore!}>
                <ConnectedCardComponent elementId={elementId} screenId={screenId} />
            </Provider>,
        );
        const iconField = getByTestId('e-field-bind-icon', { exact: false });
        expect(iconField.querySelector('[data-component="icon"]')).toHaveAttribute('type', 'print');
    });

    it('should render with a progress bar', () => {
        const fieldProperties: CardProperties<ScreenBase, any> = {
            cardDefinition: {
                title: nestedFields.reference({
                    bind: 'product',
                    valueField: 'title',
                    node: '@sage/xtrem-test/TestNode',
                }),
                titleRight: nestedFields.label({ bind: 'category' }),
                line2: nestedFields.numeric({ bind: 'amount' }),
                progressBar: nestedFields.progress({ bind: 'progressValue' }),
            },
            node: '@sage/xtrem-show-case/ShowCaseProduct',
        };
        const state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(
            screenId,
            {
                page: { $: { isTransactionInProgress: () => false }, _pageMetadata: { screenId } },
            } as Partial<PageDefinition>,
            {
                uiComponentProperties: { [screenId]: { skipDirtyCheck: true } },
            } as Partial<pageMetadataImport.PageMetadata>,
        );
        const fieldValue = {
            _id: '1',
            product: {
                _id: '123',
                title: 'Product title',
            },
            category: 'fritas',
            amount: '2',
            icon: 'print',
            progressValue: 67,
        };

        addFieldToState(FieldKey.Card, state, screenId, elementId, fieldProperties, fieldValue);
        mockStore = getStore(state);
        const { baseElement } = render(
            <Provider store={mockStore!}>
                <ConnectedCardComponent elementId={elementId} screenId={screenId} />
            </Provider>,
        );
        expect(baseElement.querySelector<HTMLElement>('[data-element="current-progress-label"]')!).toHaveTextContent(
            '67',
        );
    });
});
