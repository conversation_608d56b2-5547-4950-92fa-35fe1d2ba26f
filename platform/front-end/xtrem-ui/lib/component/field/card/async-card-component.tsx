import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { CardComponentProps } from './card-types';

const ConnectedCardComponent = React.lazy(() => import('./card-component'));

export function AsyncConnectedCardComponent(props: FieldComponentExternalProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <ConnectedCardComponent {...props} />
        </React.Suspense>
    );
}

const CardComponent = React.lazy(() => import('./card-component').then(c => ({ default: c.CardComponent })));

export function AsyncCardComponent(props: CardComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader size="small" />}>
            <CardComponent {...props} />
        </React.Suspense>
    );
}
