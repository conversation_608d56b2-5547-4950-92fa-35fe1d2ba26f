import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { CardControlObject } from './card-control-object';
import type { CardDecoratorProperties } from './card-types';

class CardDecorator extends AbstractFieldDecorator<FieldKey.Card> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = CardControlObject;
}

/**
 * Initializes the decorated member as a [Card]{@link CardControlObject} field with the provided properties.
 *
 * @param properties The properties that the [Card]{@link CardControlObject} field will be initialized with.
 */
export function cardField<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode>(
    properties: CardDecoratorProperties<Extend<T>, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Card, ReferencedItemType>(
        properties,
        CardDecorator,
        FieldKey.Card,
    );
}

export function cardFieldOverride<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode>(
    properties: ChangeableOverrideDecoratorProperties<
        CardDecoratorProperties<Extend<T>, ReferencedItemType>,
        Extend<T>
    >,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Card, ReferencedItemType>(properties);
}
