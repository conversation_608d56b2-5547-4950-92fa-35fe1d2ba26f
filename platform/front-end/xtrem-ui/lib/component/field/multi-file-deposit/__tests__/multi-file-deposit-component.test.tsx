import {
    renderWithRedux,
    applyActionMocks,
    getMockPageDefinition,
    userEvent,
} from '../../../../__tests__/test-helpers';
import * as xtremRedux from '../../../../redux';
import { fireEvent, waitFor } from '@testing-library/react';
import * as React from 'react';
import type { ScreenBase } from '../../../../service/screen-base';
import type { AttachmentAssociation, FileDepositValue } from '../../../types';
import { FieldKey } from '../../../types';
import axios from 'axios';
import { GraphQLApi } from '../../../../service/graphql-api';
import '@testing-library/jest-dom';
import ConnectedMultiFileDepositComponent from '../multi-file-deposit-component';
import type { MultiFileDepositDecoratorProperties } from '../multi-file-deposit-types';
import { getMultiFileDepositFieldColumns } from '../multi-file-deposit-utils';
import { CollectionValue } from '../../../../service/collection-data-service';

jest.mock('../../../../service/i18n-service', () => ({
    localize: (key: string, defaultValue: string) => {
        const mockTranslations = {
            '@sage/xtrem-ui/multi-file-deposit-uploaded': 'Uploaded',
            '@sage/xtrem-ui/multi-file-deposit-uploaded-by': 'Uploaded by',
            '@sage/xtrem-ui/multi-file-deposit-filename': 'File',
            '@sage/xtrem-ui/multi-file-deposit-size': 'Size',
            '@sage/xtrem-ui/multi-file-deposit-type': 'Type',
            '@sage/xtrem-ui/multi-file-deposit-status': 'Status',
            '@sage/xtrem-ui/multi-file-deposit-status-upload-failed': 'Upload failed',
            '@sage/xtrem-ui/multi-file-deposit-status-created': 'Uploading',
            '@sage/xtrem-ui/multi-file-deposit-status-uploaded': 'Uploaded',
        };
        return mockTranslations[key] || defaultValue;
    },
}));
import * as multiFileDepositUtils from '../multi-file-deposit-utils';
import { CollectionFieldTypes } from '../../../../service/collection-data-types';
import { AgGridHelpers } from '../../../../__tests__/test-helpers/table-helpers';

const screenId = 'TestPage';
const fieldId = 'test-multi-file-field';

const multiFileValue: FileDepositValue = {
    _id: '123',
    filename: 'some special data.csv',
    mimeType: 'text/csv',
    contentLength: 123456,
    downloadUrl: 'https://download.sage.com/my/special/file/123456',
    lastModified: '2022-09-20T13:58:46.485Z',
    status: 'uploaded',
    kind: 'attachment',
    uploadUrl: '',
    _createUser: {
        _id: '32',
        displayName: 'test user',
        email: '<EMAIL>',
    },
};

const multiFileValueUploading: FileDepositValue = {
    _id: '123',
    filename: 'some special data.csv',
    mimeType: 'text/csv',
    contentLength: 123456,
    downloadUrl: '',
    lastModified: '2022-09-20T13:58:46.485Z',
    status: 'uploading',
    uploadUrl: '',
    kind: 'attachment',
    _createUser: {
        _id: '32',
        displayName: 'test user',
        email: '<EMAIL>',
    },
};

const tableDefaultValue = (initialValues: { attachment: FileDepositValue }[] = []) =>
    new CollectionValue<AttachmentAssociation>({
        screenId,
        elementId: fieldId,
        isTransient: false,
        hasNextPage: false,
        orderBy: [{}],
        columnDefinitions: [getMultiFileDepositFieldColumns()],
        nodeTypes: {},
        nodes: ['@sage/xtrem-test/AnyNode'],
        filter: [],
        initialValues,
        locale: 'en-US',
        fieldType: CollectionFieldTypes.MULTI_FILE_DEPOSIT,
    });

describe('multi file deposit component', () => {
    const setup = (
        fileProps: Partial<MultiFileDepositDecoratorProperties<ScreenBase>>,
        value: { attachment: FileDepositValue }[] = [],
    ) => {
        const pageDefinition = getMockPageDefinition(
            screenId,
            {},
            {
                uiComponentProperties: {
                    [fieldId]: { ...fileProps },
                },
            },
        );
        pageDefinition.values = {
            ...(value && { [fieldId]: value }),
        };
        const initialState = {
            screenDefinitions: { [screenId]: pageDefinition },
        };
        const utils = renderWithRedux<FieldKey.MultiFileDeposit, any>(
            <ConnectedMultiFileDepositComponent screenId={screenId} elementId={fieldId} />,
            {
                initialState,
                fieldType: FieldKey.MultiFileDeposit,
                fieldValue: tableDefaultValue(value),
                fieldProperties: {
                    ...fileProps,
                    attachmentNode: '@sage/xtrem-test/TestNode',
                    node: '@sage/xtrem-test/TestNode',
                    kind: 'attachment',
                },
                elementId: fieldId,
                screenId,
                mockActions: true,
                partialPageDefinition: {
                    page: { $: { graph: new GraphQLApi({} as any) } } as any,
                },
            },
        );
        const helpers = new AgGridHelpers(utils);
        const getAllHeadersTextContent = () => helpers.getAllHeadersTextContent();
        const getCell = (rowIndex: number, columnIndex: number) => helpers.getCell(rowIndex, columnIndex);

        const multiFileDepositField = () => utils.findByTestId('e-field-bind-test-multi-file-field', { exact: false });

        const actionButton = async () => (await multiFileDepositField()).querySelector('button[type="button"]');
        const clickActionButton = async () => {
            fireEvent.click((await actionButton())!);
        };

        const getInput = () => utils.getByTestId('e-multi-file-deposit-field-upload-area', { exact: false });

        return {
            ...utils,
            utils,
            multiFileDepositField,
            actionButton,
            clickActionButton,
            getInput,
            getAllHeadersTextContent,
            getCell,
        };
    };

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    it('should render the upload area', async () => {
        const { actionButton, multiFileDepositField } = setup({});
        expect(await multiFileDepositField()).toHaveTextContent(
            'Browse to Fileor This list has no attached files yet. Drag and drop your file here.',
        );
        expect(await actionButton()).toBeInTheDocument();
    });

    it('should call depositFileToCollectionValue for the upload', async () => {
        const depositFileToCollectionValueCall = jest
            .spyOn(multiFileDepositUtils, 'depositFileToCollectionValue')
            .mockResolvedValue();

        const { getInput, clickActionButton } = setup({});

        const file = new File(['dummy content'], 'example.txt', { type: 'text/plain' });

        await clickActionButton();

        await userEvent.upload(getInput(), file);

        await waitFor(() => {
            expect((getInput() as HTMLInputElement).files?.[0]).toEqual(file);
            expect(depositFileToCollectionValueCall).toHaveBeenCalledTimes(1);
        });
    });

    it('should render the table when it has defaultvalue', async () => {
        const { utils } = setup({}, [{ attachment: multiFileValue }]);
        await utils.findByTestId('e-field-bind-test-multi-file-field', { exact: false });
    });

    it('should render the table with the correct columns', async () => {
        const { getAllHeadersTextContent, queryByTestId } = setup({}, [{ attachment: multiFileValue }]);
        await waitFor(
            () => {
                expect(queryByTestId('e-input-field-skeleton')).toBeNull();
            },
            { timeout: 6000 },
        );
        await waitFor(
            () => {
                expect(getAllHeadersTextContent()).toEqual([
                    'Uploaded',
                    'Uploaded by',
                    'File',
                    'Size',
                    'Type',
                    'Status',
                    '',
                ]);
            },
            { timeout: 6000 },
        );
    }, 15000);

    it('should render the table cell with value', async () => {
        const { getAllHeadersTextContent, getCell, queryByTestId } = setup({}, [{ attachment: multiFileValue }]);
        await waitFor(
            () => {
                expect(queryByTestId('e-input-field-skeleton')).toBeNull();
            },
            { timeout: 6000 },
        );
        await waitFor(
            () => {
                expect(getAllHeadersTextContent()).toEqual([
                    'Uploaded',
                    'Uploaded by',
                    'File',
                    'Size',
                    'Type',
                    'Status',
                    '',
                ]);
            },
            { timeout: 6000 },
        );

        await waitFor(() => {
            expect(getCell(0, 2)).not.toBeNull();
        });
    }, 15000);

    it('should render the table cell with the correct value', async () => {
        const { getCell } = setup({}, [{ attachment: multiFileValue }]);

        await waitFor(() => {
            expect(getCell(0, 3)?.textContent).toEqual('test user');
        });
    });

    describe('upload', () => {
        beforeEach(() => {
            jest.restoreAllMocks();
            jest.spyOn(xtremRedux.actions, 'setFieldValue').mockReturnValue({
                type: 'SetFieldValue',
                then: jest.fn(arg => {
                    arg();
                    return { catch: jest.fn() };
                }),
            } as any);
        });

        it('should not upload the file if the mime type is not allowed', async () => {
            const { getInput } = setup({ fileTypes: 'image/png' }, []);
            expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();

            const file = new File(['dummy content'], 'index.js', { type: 'text/javascript' });

            fireEvent.change(getInput(), {
                target: {
                    files: [file],
                },
            });

            await waitFor(() => {
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                expect(axios.post).not.toHaveBeenCalled();
            });
        });

        it('should create entry in the server when the user selects a file', async () => {
            const { store, getInput } = setup({});
            store.clearActions();
            (axios.post as unknown as jest.SpyInstance).mockResolvedValue({
                data: {
                    xtremTest: {
                        testNode: {
                            create: {
                                _id: '43',
                                uploadUrl: 'http://upload.here',
                            },
                        },
                    },
                },
            });

            const lastModified = new Date('2024-03-26T21:03:03.356Z').getTime();
            const file = new File(['dummy content'], 'some special data.csv', {
                type: 'text/csv',
                lastModified,
            });
            const input = getInput();
            expect(input).toBeDefined();
            await userEvent.upload(input, file);

            await waitFor(() => {
                expect(axios.post).toHaveBeenCalled();
            });

            expect(axios.post).toHaveBeenCalledWith(
                '/api',
                {
                    query: `mutation {
    xtremTest {
        testNode {
            create (data: {filename: "some special data.csv", mimeType: "text/csv", lastModified: "2024-03-26T21:03:03.356Z", contentLength: 13, status: "created", kind: "attachment"}) {
                _id
                uploadUrl
                _createStamp
                _createUser {
                    displayName
                }
            }
        }
    }
}`,
                },
                { headers: { 'Accept-Language': 'en-US' } },
            );
        });
    });

    describe('cancel upload', () => {
        beforeEach(() => {
            jest.restoreAllMocks();
        });

        it('should display the cancel button during the upload', async () => {
            const { utils } = setup({}, [{ attachment: multiFileValueUploading }]);

            await waitFor(() => {
                const cancelButton = utils.findByTestId('e-table-inline-action');
                expect(cancelButton).not.toBeNull();
            });
        });

        it('should click the cancel button then the dialog should appear', async () => {
            const { utils } = setup({}, [{ attachment: multiFileValueUploading }]);

            const cancelButton = async () => utils.findByTestId('e-table-inline-action');
            const clickCancelButton = async () => {
                fireEvent.click((await cancelButton())!);
            };
            clickCancelButton();
            await waitFor(() => {
                const dialog = utils.findByTestId('e-dialog-body');
                expect(dialog).not.toBeNull();
            });
        });
    });
});
