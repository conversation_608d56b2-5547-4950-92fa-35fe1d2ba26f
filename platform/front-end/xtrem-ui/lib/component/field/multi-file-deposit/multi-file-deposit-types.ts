import type { Dict } from '@sage/xtrem-shared';
import type { CancelTokenSource } from 'axios';
import type { MarkRequired } from 'ts-essentials';
import type { OnTelemetryEventFunction, ReduxResponsive } from '../../../redux/state';
import type { CollectionValue } from '../../../service/collection-data-service';
import type { GraphQLApi } from '../../../service/graphql-api';
import type { DataTypeDetails, FormattedNodeDetails } from '../../../service/metadata-types';
import type { AccessBindings } from '../../../service/page-definition';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { NestedRecordId, ScreenBaseGenericType, ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import type { SectionControlObject } from '../../container/container-control-objects';
import type { BlockControlObject } from '../../control-objects';
import type { NestedFieldTypes, PodNestedFieldTypes } from '../../nested-fields';
import type { NestedExtensionField } from '../../nested-fields-extensions';
import type { NestedOverrideField } from '../../nested-fields-overrides';
import type { AttachmentAssociation, FieldControlObjectInstance, FileDepositValue } from '../../types';
import type { CollectionValueFieldProperties } from '../collection-value-field';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type {
    CanBeReadOnly,
    Changeable,
    Clickable,
    ExtensionField,
    HasCollectionSelectionEventHandlers,
    HasCollectionSelectionEventHandlersAfter,
    HasOptionsMenu,
    HasParent,
    VoidPromise,
} from '../traits';
import type { SidebarDefinitionDecorator } from '../../table-sidebar/table-sidebar-types';

export interface MultiFileDepositProperties<CT extends ScreenBase = ScreenBase>
    extends MarkRequired<CollectionValueFieldProperties<CT, AttachmentAssociation>, 'node'>,
        CanBeReadOnly<CT, any> {
    /** Whether the add button is displayed. If set to true, an extra empty pod is rendered at the end of the list. Defaults to false */
    canAddRecord?: boolean;

    /** Whether the remove icon is displayed on each pod on the top-right corner. */
    canRemoveRecord?: boolean;

    /** Selected rows identifiers */
    selectedRecords?: NestedRecordId[];

    /** Function to be executed when any part of a row is clicked */
    onRowClick?: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: AttachmentAssociation,
        isModifierKeyPushed?: boolean,
    ) => VoidPromise;

    hasPreview?: boolean;
}

export interface MultiFileDepositDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    K extends FileDepositValue['kind'] = FileDepositValue['kind'],
> extends Omit<MultiFileDepositProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        Changeable<CT>,
        HasOptionsMenu<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasCollectionSelectionEventHandlers<CT, AttachmentAssociation>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>> {
    /** File types that can be uploaded. Can be either audio/*, video/*, image/*, an extension name starting with '.'
     *  or a valid media type. Look at [IANA Media Types](https://www.iana.org/assignments/media-types/media-types.xhtml). for a complete list of standard media types.
     * It is possible to set more than one file type, simply by defining them separated by a comma.
     */
    fileTypes?: string;

    /** The GraphQL node that the represents the attachment object */
    attachmentNode: keyof ScreenBaseGenericType<CT>;

    /** Whether the user can decide which nested grid columns to display. Defaults to true */
    canUserHideColumns?: boolean;
    kind: K;
    sidebar?: SidebarDefinitionDecorator<CT>;
}

type BaseMultiFileDepositExtensionDecoratorProperties<CT extends ScreenBase = ScreenBase> =
    HasCollectionSelectionEventHandlersAfter<CT, AttachmentAssociation>;

export interface MultiFileDepositExtensionDecoratorProperties<CT extends ScreenExtension<CT>>
    extends BaseMultiFileDepositExtensionDecoratorProperties<Extend<CT>>,
        ChangeableOverrideDecoratorProperties<MultiFileDepositDecoratorProperties<Extend<CT>>, CT> {
    columns?: NestedExtensionField<CT, PodNestedFieldTypes, AttachmentAssociation>[];
    /** Allows overriding existing column properties in the base page's columns */
    columnOverrides?: NestedOverrideField<CT, NestedFieldTypes, AttachmentAssociation>[];
}
export interface MultiFileDepositComponentAdditionalProperties {
    accessBindings: AccessBindings;
    nodeTypes: Dict<FormattedNodeDetails>;
    dataTypes: Dict<DataTypeDetails>;
    enumTypes: Dict<string[]>;
    graphApi: GraphQLApi<any>;
    browser: ReduxResponsive;
    onTelemetryEvent?: OnTelemetryEventFunction;
}

export type MultiFileDepositComponentProperties = BaseEditableComponentProperties<
    MultiFileDepositDecoratorProperties,
    CollectionValue<AttachmentAssociation>
> &
    MultiFileDepositComponentAdditionalProperties;

export type WithCancelToken<T> = T & {
    cancelTokenSource?: CancelTokenSource;
};
