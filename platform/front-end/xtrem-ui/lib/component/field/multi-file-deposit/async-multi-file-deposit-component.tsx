import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { MultiFileDepositComponentProperties } from './multi-file-deposit-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const ConnectedMultiFileDepositComponent = React.lazy(() => import('./multi-file-deposit-component'));

export function AsyncConnectedMultiFileDepositComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedMultiFileDepositComponent {...props} />
        </React.Suspense>
    );
}

const MultiFileDepositComponent = React.lazy(() =>
    import('./multi-file-deposit-component').then(c => ({ default: c.MultiFileDepositComponent })),
);

export function AsyncMultiFileDepositComponent(props: MultiFileDepositComponentProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <MultiFileDepositComponent {...props} />
        </React.Suspense>
    );
}
