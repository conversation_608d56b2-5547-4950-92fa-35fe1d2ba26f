import * as React from 'react';
import { connect } from 'react-redux';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type {
    MultiFileDepositComponentAdditionalProperties,
    MultiFileDepositComponentProperties,
    MultiFileDepositDecoratorProperties,
    WithCancelToken,
} from './multi-file-deposit-types';
import type { AttachmentAssociation } from '../../types';
import FileInput from 'carbon-react/esm/components/file-input';
import { localize } from '../../../service/i18n-service';
import { getDataTestIdAttribute, getComponentClass } from '../../../utils/dom';
import { confirmationDialog, errorDialog, internalDialog } from '../../../service/dialog-service';
import { ContextType } from '../../../types';
import { AsyncTableComponent } from '../table/async-table-component';
import { AsyncPreviewComponent } from '../preview/async-preview-component';
import type {
    BaseEditableComponentProperties,
    EditableFieldComponentProperties,
    FieldComponentExternalProperties,
} from '../field-base-component-types';
import { type AppThunkDispatch, getStore, type XtremAppState } from '../../../redux';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import { getScreenElement } from '../../../service/screen-base-definition';
import type { CollectionValue } from '../../../service/collection-data-service';
import { depositFileToCollectionValue, getMultiFileDepositFieldColumns } from './multi-file-deposit-utils';
import type { IconType } from 'carbon-react/esm/components/icon';
import type { InlineCollectionItemAction } from '../../ui/table-shared/table-dropdown-actions/table-dropdown-action-types';
import {
    STATUS_CREATED,
    STATUS_UPLOADING,
    getMimeTypeFromExtension,
    isDisallowedMimeType,
    showNotAllowedTypeToast,
} from '../../../utils/file-deposit-utils';
import { RecordActionType } from '../../../service/collection-data-types';
import type { Unsubscribe } from 'redux';
import { GridColumn, GridRow } from '@sage/xtrem-ui-components';
import { triggerFieldEvent } from '../../../utils/events';
import { openTableSidebar } from '../../../redux/actions/table-sidebar-actions';
import { ATTACHMENTS_ELEMENT_ID } from '../../../utils/constants';
import type { SidebarDefinitionDecorator } from '../../table-sidebar/table-sidebar-types';

interface MultiFileDepositComponentState {
    hasValue: boolean;
    isFileInputVisible: boolean;
    selectedItem: AttachmentAssociation | undefined;
}

export class MultiFileDepositComponent extends EditableFieldBaseComponent<
    MultiFileDepositDecoratorProperties,
    CollectionValue<WithCancelToken<AttachmentAssociation>>,
    MultiFileDepositComponentAdditionalProperties,
    MultiFileDepositComponentState
> {
    private changeSubscription: Unsubscribe | undefined;

    constructor(props: MultiFileDepositComponentProperties) {
        super(props);
        this.state = this.getDefaultStateFromProps(props);
    }

    getDefaultStateFromProps(props: MultiFileDepositComponentProperties): MultiFileDepositComponentState {
        return {
            hasValue: !!props.value && props.value.getRawRecords().length > 0,
            isFileInputVisible: !props.value || props.value.getRawRecords().length === 0,
            selectedItem: undefined,
        };
    }

    componentDidUpdate(
        prevProps: BaseEditableComponentProperties<
            MultiFileDepositDecoratorProperties,
            CollectionValue<WithCancelToken<AttachmentAssociation>>,
            MultiFileDepositComponentAdditionalProperties
        >,
    ): void {
        if (prevProps.value !== this.props.value) {
            this.changeSubscription?.();
            this.changeSubscription = this.props.value?.subscribeForValueChanges(type => {
                if (type === RecordActionType.ADDED && !this.state.hasValue) {
                    this.setState({ hasValue: true, isFileInputVisible: false });
                }
            });

            this.setState(this.getDefaultStateFromProps(this.props));
        }
    }

    componentDidMount(): void {
        this.changeSubscription = this.props.value?.subscribeForValueChanges(type => {
            if (type === RecordActionType.ADDED && !this.state.hasValue) {
                this.setState({ hasValue: true, isFileInputVisible: false });
            }
        });
    }

    componentWillUnmount(): void {
        this.changeSubscription?.();
    }

    isPreviewInPopup = (): boolean => {
        return this.props.browser.is.s || this.props.browser.is.xs;
    };

    onChange = async (fileList: FileList): Promise<void> => {
        if (!fileList.length) {
            return;
        }

        const targetNode = this.props.graphApi.node(this.props.fieldProperties.attachmentNode);
        await Promise.all(
            Array.from(fileList).map(async (file: File): Promise<void> => {
                if (!this.props.value) {
                    return;
                }

                const mimeType = file.type || getMimeTypeFromExtension(file.name);

                if (
                    this.props.fieldProperties.fileTypes &&
                    isDisallowedMimeType(this.props.fieldProperties.fileTypes, mimeType, file.name)
                ) {
                    this.props.onTelemetryEvent?.(
                        `multiFileDepositFileMimeTypeValidationFailed-${this.props.elementId}`,
                        {
                            elementId: this.props.elementId,
                            screenId: this.props.screenId,
                            mimeType,
                        },
                    );

                    showNotAllowedTypeToast(mimeType);
                    return;
                }

                this.props.onTelemetryEvent?.(`multiFileDepositFileAdded-${this.props.elementId}`, {
                    mimeType,
                    method: 'fileBrowserSelection',
                    elementId: this.props.elementId,
                    screenId: this.props.screenId,
                });

                try {
                    await depositFileToCollectionValue({
                        value: this.props.value,
                        file,
                        targetNodeMutations: targetNode.mutations,
                        kind: this.props.fieldProperties.kind,
                    });
                } catch (err) {
                    errorDialog(
                        this.props.screenId,
                        localize('@sage/xtrem-ui/file-upload-failed', 'Failed to upload file.'),
                        err,
                    );
                }
            }),
        );
        await triggerFieldEvent(this.props.screenId, this.props.elementId, 'onChange');
    };

    cancelFileUploading = (recordId: string): void => {
        const tokenSource = this.props.value?.getRawRecord({ id: recordId }).cancelTokenSource;
        if (tokenSource) {
            tokenSource.cancel(`Upload cancelled for recordId: ${recordId}`);
        }
    };

    getTableFixedHeight = (): number | undefined => {
        if (!this.props.fixedHeight) {
            return undefined;
        }

        if (this.state.isFileInputVisible) {
            return this.props.fixedHeight - 216;
        }

        return this.props.fixedHeight;
    };

    getPreviewFixedHeight = (): number | undefined => {
        if (this.props.browser.is.s || this.props.browser.is.xs) {
            return window.innerHeight - 80;
        }

        return this.getTableFixedHeight();
    };

    getUploadAreaHeight = (): string => {
        if (this.state.hasValue) {
            return '200px';
        }

        if (this.props.fixedHeight) {
            return `${this.props.fixedHeight}px`;
        }

        return '400px';
    };

    async displayCancelUploadDialog(recordId: string): Promise<void> {
        const confirmation = await confirmationDialog(
            this.props.screenId,
            'warn',
            localize('@sage/xtrem-ui/multi-file-upload-cancel-title', 'Cancel upload'),
            localize('@sage/xtrem-ui/multi-file-upload-cancel', 'Cancel file upload in progress.'),
            {
                acceptButton: {
                    text: localize('@sage/xtrem-ui/continue', 'Continue'),
                },
                cancelButton: {
                    text: localize('@sage/xtrem-ui/cancel', 'Cancel'),
                },
                size: 'small',
            },
        );
        if (confirmation) {
            this.props.onTelemetryEvent?.(`multiFileDepositFileUploadCancelled-${this.props.elementId}`, {
                elementId: this.props.elementId,
                screenId: this.props.screenId,
            });

            this.cancelFileUploading(recordId);
        }
    }

    async displayRemoveFileDialog(recordId: string): Promise<void> {
        const confirmation = await confirmationDialog(
            this.props.screenId,
            'warn',
            localize('@sage/xtrem-ui/multi-file-upload-remove-title', 'Remove file'),
            localize('@sage/xtrem-ui/multi-file-upload-remove', 'Remove uploaded file.'),
            {
                size: 'small',
            },
        );

        if (confirmation) {
            this.props.onTelemetryEvent?.(`multiFileDepositFileRemoved-${this.props.elementId}`, {
                elementId: this.props.elementId,
                screenId: this.props.screenId,
            });
            this.props.value?.removeRecord({ recordId, shouldNotifySubscribers: true });
            await triggerFieldEvent(this.props.screenId, this.props.elementId, 'onChange');
        }
    }

    onToggleDropzone = (): void => {
        this.setState(prevState => ({
            isFileInputVisible: !prevState.isFileInputVisible,
        }));
    };

    onClosePreview = (): void => {
        this.setState({ selectedItem: undefined });
    };

    onRowClick = (selectedItem: AttachmentAssociation): void => {
        if (this.isPreviewInPopup()) {
            return;
        }
        this.setState({ selectedItem });
    };

    onPreviewDialogOpen = (selectedItem: AttachmentAssociation): void => {
        this.setState({ selectedItem }, async () => {
            await internalDialog(this.props.screenId, 'info', this.renderPreviewComponentInDialog(), {
                dialogTitle: 'Preview',
                resolveOnCancel: true,
            });
            this.setState({ selectedItem: undefined });
        });
    };

    onEditDetails = (selectedItem: AttachmentAssociation): void => {
        const dispatch = getStore().dispatch as AppThunkDispatch;
        const sidebar: SidebarDefinitionDecorator = {
            title: (_id, item) => item.attachment.filename,
            async onRecordConfirmed(_id, recordValue) {
                const uploadedFileNode = this.$.graph.node('@sage/xtrem-upload/UploadedFile');
                await uploadedFileNode.mutations
                    .update(
                        { _tags: { _id: true, name: true }, _id: true },
                        {
                            data: {
                                _id: recordValue.attachment._id,
                                _tags: (recordValue.attachment._tags ?? []).map((tag: any) => tag._id),
                            },
                        },
                    )
                    .execute();
            },
            layout: {
                mainSection: {
                    title: localize('@sage/xtrem-ui/multi-file-deposit-edit-details', 'Edit file details'),
                    blocks: {
                        mainBlock: {
                            fields: [
                                'attachment.filename',
                                'attachment.humanFriendlyContentLength',
                                'attachment._createStamp',
                                'attachment.lastModified',
                                'attachment._createUser.displayName',
                                'title',
                                'description',
                                'attachment._tags',
                            ],
                        },
                    },
                },
            },
        };

        dispatch(
            openTableSidebar({
                screenId: this.props.screenId,
                elementId: ATTACHMENTS_ELEMENT_ID,
                recordId: selectedItem._id,
                sidebarDefinition: sidebar,
                columns: getMultiFileDepositFieldColumns(),
            }),
        );
    };

    renderUploadedFilesTable = (): React.ReactElement | null => {
        if (!this.props.value) {
            return null;
        }

        const inlineActions: Array<InlineCollectionItemAction<any>> = [
            {
                icon: 'cross_circle',
                title: localize('@sage/xtrem-ui/multi-file-deposit-cancel', 'Cancel'),
                isDestructive: true,
                isDisplayed: (_id, rowItem): boolean => {
                    return (
                        rowItem?.attachment.status === STATUS_CREATED || rowItem?.attachment.status === STATUS_UPLOADING
                    );
                },
                onClick: (_id, rowItem) => this.displayCancelUploadDialog(rowItem._id),
            },
            {
                icon: 'view',
                title: localize('@sage/xtrem-ui/multi-file-deposit-open-preview', 'Open preview'),
                isHidden: (): boolean => {
                    return !this.isPreviewInPopup();
                },
                onClick: (_id, rowItem) => this.onPreviewDialogOpen(rowItem as AttachmentAssociation),
            },
            {
                icon: 'box_arrow_left',
                title: localize('@sage/xtrem-ui/multi-file-deposit-edit-details', 'Edit file details'),
                onClick: (_id, rowItem) => this.onEditDetails(rowItem as AttachmentAssociation),
            },
            {
                icon: 'bin',
                title: localize('@sage/xtrem-ui/multi-file-deposit-remove', 'Remove'),
                isDestructive: true,
                isDisabled: (_id, rowItem): boolean => {
                    return (
                        rowItem?.attachment.status === STATUS_CREATED || rowItem?.attachment.status === STATUS_UPLOADING
                    );
                },
                onClick: (_id, rowItem) => this.displayRemoveFileDialog(rowItem._id),
            },
        ];

        const additionalFieldActions = [
            {
                id: 'toggle',
                icon: 'upload' as IconType,
                title: this.state.isFileInputVisible
                    ? localize('@sage/xtrem-ui/file-component-hide-upload-area', 'Hide upload area')
                    : localize('@sage/xtrem-ui/file-component-show-upload-area', 'Show upload area'),
                onClick: this.onToggleDropzone,
            },
        ];

        return (
            <AsyncTableComponent
                accessBindings={this.props.accessBindings}
                browser={this.props.browser}
                contextType={ContextType.table}
                dataTypes={this.props.dataTypes}
                elementId={this.props.elementId}
                enumTypes={this.props.enumTypes}
                selectionMode="single"
                fixedHeight={this.getTableFixedHeight()}
                fieldProperties={{
                    ...this.props.fieldProperties,
                    isChangeIndicatorDisabled: true,
                    columns: getMultiFileDepositFieldColumns(),
                    inlineActions,
                    additionalFieldActions,
                    canSelect: !this.isPreviewInPopup() && this.props.fieldProperties.hasPreview,
                    selectedRecords:
                        !this.isPreviewInPopup() && this.state.selectedItem ? [this.state.selectedItem._id] : [],
                }}
                isUsingInfiniteScroll={true}
                locale={this.props.locale}
                nodeTypes={this.props.nodeTypes}
                screenId={this.props.screenId}
                setFieldProperties={this.props.setFieldProperties}
                tableUserSettings={{}}
                validationErrors={[]}
                value={this.props.value}
                onRowClick={this.isPreviewInPopup() ? undefined : this.onRowClick}
            />
        );
    };

    renderPreviewComponentInDialog(): React.ReactElement {
        return <div className="e-multi-file-deposit-field-preview-dialog">{this.renderPreviewComponent()}</div>;
    }

    renderPreviewComponent(): React.ReactElement {
        return (
            <AsyncPreviewComponent
                {...this.props}
                fixedHeight={this.getPreviewFixedHeight()}
                fieldProperties={{
                    mimeType: this.state.selectedItem?.attachment.mimeType,
                    filename: this.state.selectedItem?.attachment.filename,
                    canZoom: true,
                    canPrint: true,
                    canDownload: true,
                    hasThumbnailBar: true,
                }}
                onClose={this.isPreviewInPopup() ? undefined : this.onClosePreview}
                value={this.state.selectedItem?.attachment.downloadUrl}
                setFieldValue={() => Promise.resolve()}
                validate={() => Promise.resolve(undefined)}
            />
        );
    }

    renderTableAndPreview = (): React.ReactElement => {
        return (
            <GridRow columns={12} gutter={16} margin={16} verticalMargin={0}>
                <GridColumn columnSpan={6}>{this.renderUploadedFilesTable()}</GridColumn>
                <GridColumn columnSpan={6}>{this.renderPreviewComponent()}</GridColumn>
            </GridRow>
        );
    };

    render(): React.ReactElement {
        const title = this.getTitle();
        const fieldProperties = this.props.fieldProperties;
        const dragAndDropText = !this.state.hasValue
            ? localize(
                  '@sage/xtrem-ui/file-component-drag-drop-empty',
                  'or \n This list has no attached files yet. \n Drag and drop your file here.',
              )
            : localize('@sage/xtrem-ui/file-component-drag-drop', 'or \n Drag and drop your file here');

        return (
            <div
                data-testid={getDataTestIdAttribute('multi-file-deposit', title, this.props.elementId)}
                className={getComponentClass(this.props, 'e-multi-file-deposit-field')}
            >
                {this.state.isFileInputVisible && (
                    <div className="e-multi-file-deposit-field-upload-area">
                        <FileInput
                            data-testid="e-multi-file-deposit-field-upload-area"
                            maxWidth="100%"
                            isVertical
                            onChange={this.onChange}
                            accept={fieldProperties.fileTypes}
                            buttonText={localize('@sage/xtrem-ui/file-component-browse-file', 'Browse to File')}
                            dragAndDropText={dragAndDropText}
                            minHeight={this.getUploadAreaHeight()}
                            mb={this.state.hasValue ? '16px' : undefined}
                        />
                    </div>
                )}
                {this.state.hasValue &&
                    (!this.state.selectedItem || this.isPreviewInPopup()) &&
                    this.renderUploadedFilesTable()}
                {this.state.hasValue &&
                    this.state.selectedItem &&
                    !this.isPreviewInPopup() &&
                    this.renderTableAndPreview()}
            </div>
        );
    }
}

export const ConnectedMultiFileDepositComponent = connect(
    (state: XtremAppState, props: FieldComponentExternalProperties): MultiFileDepositComponentProperties => {
        const screenDefinition = getPageDefinitionFromState(props.screenId, state);
        const screenElement = getScreenElement(screenDefinition);
        const basicProps = mapStateToProps()(state, props) as EditableFieldComponentProperties<
            MultiFileDepositDecoratorProperties,
            CollectionValue<AttachmentAssociation>
        >;

        return {
            ...props,
            ...basicProps,
            accessBindings: screenDefinition.accessBindings || {},
            enumTypes: state.enumTypes,
            nodeTypes: state.nodeTypes,
            dataTypes: state.dataTypes,
            graphApi: screenElement.$.graph,
            browser: state.browser,
            onTelemetryEvent: state.applicationContext?.onTelemetryEvent,
        };
    },
    mapDispatchToProps(),
)(MultiFileDepositComponent);

export default ConnectedMultiFileDepositComponent;
