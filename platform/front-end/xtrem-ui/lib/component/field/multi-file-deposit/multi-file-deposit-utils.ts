import * as tokens from '@sage/design-tokens/js/base/common';
import type { IconType } from 'carbon-react/esm/components/icon';
import type { CollectionValue } from '../../../service/collection-data-service';
import { localize } from '../../../service/i18n-service';
import type { DepositFileArguments } from '../../../utils/file-deposit-utils';
import {
    STATUS_CANCELLED,
    STATUS_CREATED,
    STATUS_REJECTED,
    STATUS_UPLOADED,
    STATUS_UPLOAD_FAILED,
    STATUS_VERIFIED,
    depositFile,
} from '../../../utils/file-deposit-utils';
import type { NestedField } from '../../nested-fields';
import { date, label, link, multiReference, relativeDate, technical, text, textArea } from '../../nested-fields';
import type { AttachmentAssociation } from '../../types';
import type { WithCancelToken } from './multi-file-deposit-types';
import { triggerFieldEvent } from '../../../utils/events';

function getStatusLabelColor(_id: string, rowValue: AttachmentAssociation): string {
    if (
        rowValue.attachment.status === STATUS_UPLOAD_FAILED ||
        rowValue.attachment.status === STATUS_REJECTED ||
        rowValue.attachment.status === STATUS_CANCELLED
    ) {
        return tokens.colorsSemanticNegative500;
    }

    if (rowValue.attachment.status === STATUS_CREATED) {
        return tokens.colorsSemanticInfo500;
    }

    if (rowValue.attachment.status === STATUS_UPLOADED) {
        return tokens.colorsSemanticCaution500;
    }

    if (rowValue.attachment.status === STATUS_VERIFIED) {
        return tokens.colorsSemanticPositive500;
    }

    return tokens.colorsYin090;
}

export function getMultiFileDepositFieldColumns(): Array<NestedField<any, any, AttachmentAssociation>> {
    return [
        text({
            bind: { attachment: { _id: true } },
            title: 'id',
            isHidden: true,
        }),
        date({
            bind: { attachment: { _createStamp: true } },
            title: localize('@sage/xtrem-ui/multi-file-deposit-uploaded', 'Uploaded'),
            isHiddenOnMainField: true,
            isReadOnly: true,
        }),
        relativeDate({
            bind: { attachment: { _createStamp: true } },
            title: localize('@sage/xtrem-ui/multi-file-deposit-uploaded', 'Uploaded'),
        }),
        text({
            bind: { attachment: { _createUser: { displayName: true } } } as any,
            title: localize('@sage/xtrem-ui/multi-file-deposit-uploaded-by', 'Uploaded by'),
            isReadOnly: true,
        }),
        text({
            bind: { attachment: { filename: true } },
            title: localize('@sage/xtrem-ui/multi-file-deposit-filename', 'File'),
            isHiddenOnMainField: true,
            isReadOnly: true,
        }),
        link({
            bind: { attachment: { filename: true } },
            title: localize('@sage/xtrem-ui/multi-file-deposit-filename', 'File'),
            page: (_id, rowValue) => {
                if (rowValue?.attachment?.status === STATUS_VERIFIED) {
                    return rowValue?.attachment?.downloadUrl;
                }
                return null;
            },
            isDisabled: (_id, rowValue): boolean => {
                return !rowValue?.attachment?.downloadUrl || rowValue.attachment.status !== STATUS_VERIFIED;
            },
            iconColor(_id, rowValue): string {
                if (rowValue?.attachment?.downloadUrl && rowValue?.attachment?.status === STATUS_VERIFIED) {
                    return tokens.colorsActionMajor500;
                }
                return tokens.colorsYin090;
            },
            icon(_id, rowValue: any): IconType {
                switch (rowValue.attachment?.mimeType) {
                    case 'application/pdf':
                        return 'file_pdf';
                    case 'application/msword':
                    case 'application/vnd.msword':
                    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                        return 'file_word';
                    case 'application/msexcel':
                    case 'application/vnd.ms-excel':
                    case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                        return 'file_excel';
                    case 'image/bmp':
                    case 'image/cgm':
                    case 'image/gif':
                    case 'image/jpeg':
                    case 'image/png':
                    case 'image/svg+xml':
                    case 'image/tiff':
                    case 'image/vnd.microsoft.icon':
                    case 'image/vnd.wap.wbmp':
                    case 'image/webp':
                    case 'image/x-cmu-raster':
                    case 'image/x-icon':
                    case 'image/x-jng':
                    case 'image/x-ms-bmp':
                    case 'image/x-portable-anymap':
                    case 'image/x-portable-bitmap':
                    case 'image/x-portable-graymap':
                    case 'image/x-portable-pixmap':
                    case 'image/x-rgb':
                    case 'image/x-xbitmap':
                    case 'image/x-xpixmap':
                    case 'image/x-xwindowdump':
                        return 'file_image';
                    case 'message/rfc822':
                    case 'application/vnd.ms-outlook':
                        return 'email';
                    default:
                        return 'file_generic';
                }
            },
        }),
        text({
            bind: { attachment: { humanFriendlyContentLength: true } } as any,
            isTransient: true,
            isReadOnly: true,
            title: localize('@sage/xtrem-ui/multi-file-deposit-size', 'Size'),
        }),

        text({
            bind: 'title',
            title: localize('@sage/xtrem-ui/multi-file-deposit-title', 'Title'),
            isHiddenOnMainField: true,
            isFullWidth: true,
        }),
        textArea({
            bind: 'description',
            title: localize('@sage/xtrem-ui/multi-file-deposit-description', 'Description'),
            isHiddenOnMainField: true,
            isFullWidth: true,
        }),
        text({
            bind: { attachment: { humanFriendlyFileType: true } } as any,
            title: localize('@sage/xtrem-ui/multi-file-deposit-type', 'Type'),
            isReadOnly: true,
            isTransient: true,
        }),
        label({
            bind: { attachment: { status: true } },
            title: localize('@sage/xtrem-ui/multi-file-deposit-status', 'Status'),
            backgroundColor: getStatusLabelColor,
            borderColor: getStatusLabelColor,
            color: tokens.colorsYang100,
            map(status) {
                if (status === STATUS_UPLOAD_FAILED) {
                    return localize('@sage/xtrem-ui/multi-file-deposit-status-upload-failed', 'Failed');
                }

                if (status === STATUS_REJECTED) {
                    return localize('@sage/xtrem-ui/multi-file-deposit-status-upload-rejected', 'Rejected');
                }

                if (status === STATUS_CANCELLED) {
                    return localize('@sage/xtrem-ui/multi-file-deposit-status-upload-cancelled', 'Cancelled');
                }

                if (status === STATUS_CREATED) {
                    return localize('@sage/xtrem-ui/multi-file-deposit-status-created', 'In progress');
                }

                if (status === STATUS_UPLOADED) {
                    return localize('@sage/xtrem-ui/multi-file-deposit-status-uploaded', 'Pending');
                }

                if (status === STATUS_VERIFIED) {
                    return localize('@sage/xtrem-ui/multi-file-deposit-status-verified', 'Uploaded');
                }

                return status;
            },
        }),

        date({
            bind: { attachment: { lastModified: true } } as any,
            isHidden: false,
            isReadOnly: true,
            isHiddenOnMainField: true,
            title: localize('@sage/xtrem-ui/multi-file-deposit-modified', 'Modified'),
        }),

        multiReference({
            isHiddenOnMainField: true,
            createTunnelLinkText: localize('@sage/xtrem-ui/multi-file-deposit-create-tag', 'Create tag'),
            tunnelPage: '@sage/xtrem-system/SysTag',
            isFullWidth: true,
            node: '@sage/xtrem-system/SysTag',
            valueField: 'name',
            bind: { attachment: { _tags: true } } as any,
            title: localize('@sage/xtrem-ui/multi-file-deposit-tags', 'Tags'),
            columns: [
                text({
                    bind: '_id',
                    title: localize('@sage/xtrem-ui/multi-file-deposit-tag-id', 'ID'),
                    isReadOnly: true,
                }),
                text({
                    bind: 'name',
                    title: localize('@sage/xtrem-ui/multi-file-deposit-tag-title', 'Title'),
                    isReadOnly: true,
                }),
                text({
                    bind: 'description',
                    title: localize('@sage/xtrem-ui/multi-file-deposit-tag-description', 'Description'),
                    isReadOnly: true,
                }),
            ],
        }),

        technical({
            bind: { attachment: { downloadUrl: true } },
        }),
        technical({
            bind: { attachment: { contentLength: true } },
        }),
        technical({
            bind: { attachment: { mimeType: true } },
        }),
    ];
}

export interface DepositFileToCollectionValueArguments extends DepositFileArguments {
    value: CollectionValue<WithCancelToken<AttachmentAssociation>>;
}

export async function depositFileToCollectionValue({
    file,
    targetNodeMutations,
    value,
    onUploadProgress,
    kind,
}: DepositFileToCollectionValueArguments): Promise<void> {
    let recordId: string | null = null;
    await depositFile({
        file,
        targetNodeMutations,
        onUploadProgress: ev => {
            onUploadProgress?.(ev);

            if (ev.progress === 0 && !recordId && ev.fieldValue) {
                const addedRecord = value.addRecord({
                    recordData: {
                        attachment: ev.fieldValue,
                        title: ev.fieldValue.filename,
                        cancelTokenSource: ev.cancelTokenSource,
                    },
                    shouldNotifySubscribers: true,
                });
                triggerFieldEvent(value.screenId, value.elementId, 'onChange');
                recordId = addedRecord._id;
            } else if (recordId && ev.cancelTokenSource && ev.fieldValue.status === STATUS_CANCELLED) {
                value.removeRecord({ recordId });
            } else if (recordId && !ev.isInProgress) {
                value.addOrUpdateRecordValue({
                    recordData: {
                        _id: recordId,
                        attachment: ev.fieldValue,
                        cancelTokenSource: ev.cancelTokenSource,
                    },
                });
            }
        },
        kind,
    });
}
