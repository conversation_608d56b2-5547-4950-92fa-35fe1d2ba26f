/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { NestedFieldTypes } from '../../nested-fields';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import type { FieldKey, AttachmentAssociation } from '../../types';
import { CollectionValueControlObject } from '../collection-value-field';
import type { MultiFileDepositProperties } from './multi-file-deposit-types';

/**
 * [Field]{@link ReadonlyFieldControlObject} that allows the user to explicitly trigger some action
 */
export class MultiFileDepositControlObject<
    CT extends ScreenExtension<CT> = ScreenBase,
> extends CollectionValueControlObject<
    FieldKey.MultiFileDeposit,
    AttachmentAssociation,
    CT,
    NestedFieldTypes,
    MultiFileDepositProperties<CT>
> {
    @ControlObjectProperty<MultiFileDepositProperties<CT>, MultiFileDepositControlObject<CT>>()
    canAddRecord?: boolean;

    @ControlObjectProperty<MultiFileDepositProperties<CT>, MultiFileDepositControlObject<CT>>()
    canRemoveRecord?: boolean;

    /**
     * Whether the field is editable (isReadOnly = false) or not (isReadOnly = true)
     *
     * The difference with disabled is that isReadOnly suggests that the field is never editable
     * (e.g. the id field of an object)
     */
    @FieldControlObjectResolvedProperty<MultiFileDepositProperties<CT>, MultiFileDepositControlObject<CT>>()
    isReadOnly: boolean;

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }

    get node(): string {
        return String(this.getUiComponentProperty('node'));
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async addFiles(_files: File[]): Promise<void> {
        // TODO: Implement this method so it can add files on file drop to the page body.
    }
}
