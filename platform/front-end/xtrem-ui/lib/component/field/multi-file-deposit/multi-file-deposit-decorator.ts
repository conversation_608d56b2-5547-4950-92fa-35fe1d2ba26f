import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { MultiFileDepositControlObject } from '../../control-objects';
import type {
    MultiFileDepositDecoratorProperties,
    MultiFileDepositExtensionDecoratorProperties,
} from './multi-file-deposit-types';

class MultiFileDepositDecorator extends AbstractFieldDecorator<FieldKey.MultiFileDeposit> {
    protected _controlObjectConstructor = MultiFileDepositControlObject;

    protected _layout = AbstractFieldLayoutBuilder;
}

/**
 * Initializes the decorated member as a [MultiFileDeposit]{@link MultiFileDepositControlObject} field with the provided properties
 *
 * @param properties The properties that the [MultiFileDeposit]{@link MultiFileDepositControlObject} field will be initialized with
 */
export function multiFileDepositField<T extends ScreenExtension<T>>(
    properties: MultiFileDepositDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.MultiFileDeposit>(
        properties,
        MultiFileDepositDecorator,
        FieldKey.MultiFileDeposit,
    );
}

export function multiFileDepositFieldOverride<T extends ScreenExtension<T>>(
    properties: MultiFileDepositExtensionDecoratorProperties<T>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.MultiFileDeposit>(properties);
}
