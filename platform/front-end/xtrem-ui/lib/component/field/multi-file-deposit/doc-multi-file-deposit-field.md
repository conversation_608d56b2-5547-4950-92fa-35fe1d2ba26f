PATH: XTREEM/UI+Field+Widgets/Multi+File+Deposit+Field

## Introduction

The multi file deposit handles uploading, storing and management of multiple files in a single field.

The field composed of two interactive parts:

### The file upload area
The file upload area is the area where the user can drag and drop files or click to select files to upload from the file system.
This behaves very similarly to the file deposit field.

### The file list
The file list is the nested grid where the user can see details and interact with the files related to the field.

It shows the progress of the upload to the storage services, and allows the user to cancel the process, preview, download, filter and remove files from the collection.

## Example:
```ts
@ui.decorators.multiFileDepositField<MultiFileDepositField>({
    parent() {
        return this.fieldBlock;
    },
    node: '@sage/xtrem-upload/AttachmentAssociation',
    attachmentNode: '@sage/xtrem-upload/UploadedFile',
    bind: '_attachments',
    kind: 'attachment',
})
field: ui.fields.MultiFileDeposit;
```
#### Data Binding Properties

- **node**: Use '@sage/xtrem-upload/AttachmentAssociation'. It describes the title, description and authorization schema for the attached file.
- **attachmentNode**: Use '@sage/xtrem-upload/UploadedFile'. It describes some content properties like the size or filetype, and current state of a file in the collection. Also contains the actual reference to the file in the storage service.
- **bind**: The GraphQL object's property that the field's value is bound to.
- **kind**: The kind of the uploaded file. e.g. `attachment` or `upload`.
- **fileTypes**: File types that can be uploaded. Can be either audio/*, video/*, image/*, an extension name starting with '.' or a valid media type. Look at [IANA Media Types](https://www.iana.org/assignments/media-types/media-types.xhtml). for a complete list of standard media types. It is possible to set more than one file type, simply by defining them separated by a comma.
- **canUserHideColumns**. Boolean. Defaults to false. If true, the user can hide columns in the file list.

## Sandbox

Check out this field type on our sandbox server by clicking [this link](https://showcase-ci-v2.eu.dev-sagextrem.com/@sage/xtrem-show-case/MultiFileDepositField/eyJfaWQiOiIzMTMifQ==).
