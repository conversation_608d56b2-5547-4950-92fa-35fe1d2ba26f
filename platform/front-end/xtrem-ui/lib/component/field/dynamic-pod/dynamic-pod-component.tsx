import { <PERSON><PERSON><PERSON> } from '@sage/xtrem-shared';
import { isEqual, set, uniq } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import { getScreenElement } from '../../../service/screen-base-definition';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import { calculateContainerWidth } from '../../../utils/responsive-utils';
import type { PodProps } from '../../ui/pod/pod-component';
import { Pod } from '../../ui/pod/pod-component';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { EditableFieldComponentProperties, FieldComponentExternalProperties } from '../field-base-component-types';
import type { HasOptionType } from '../traits';
import type { DynamicPodComponentProps, DynamicPodDecoratorProperties } from './dynamic-pod-types';
import { schemaTypeNameFromNodeName } from '../../../utils/transformers';
import type { ReferenceDecoratorProperties } from '../../decorator-properties';
import Loader from 'carbon-react/esm/components/loader';
import { createDynamicPodReferenceField } from '../../../utils/data-type-utils';
import type { NestedField } from '../../nested-fields';
import { getLocaleKey } from '../../../service/i18n-service';

export class DynamicPodComponent extends EditableFieldBaseComponent<
    DynamicPodDecoratorProperties,
    any,
    DynamicPodComponentProps,
    { fetchingReferenceTypes: boolean; fetchingOptionTypes: boolean }
> {
    constructor(props: DynamicPodComponentProps) {
        super(props);
        this.state = { fetchingReferenceTypes: true, fetchingOptionTypes: true };
    }

    getMissingOptionTypes(): string[] {
        return (this.props.fieldProperties.columns || [])
            .filter(c => !!(c.properties as HasOptionType).optionType)
            .map(c => (c.properties as HasOptionType).optionType as string);
    }

    getMissingReferenceTypes(): string[] {
        return uniq(
            (this.props.fieldProperties.columns || [])
                .filter(
                    c =>
                        c.type === FieldKey.Reference &&
                        (c.properties as ReferenceDecoratorProperties).node &&
                        !this.props.nodeTypes?.[
                            schemaTypeNameFromNodeName(String((c.properties as ReferenceDecoratorProperties).node))
                        ],
                )
                .map(c => String(schemaTypeNameFromNodeName((c.properties as ReferenceDecoratorProperties).node))),
        );
    }

    async checkDataModel(): Promise<void> {
        const missingOptionTypes = this.getMissingOptionTypes();
        const locale = this.props.locale || 'en-US';
        if (missingOptionTypes.length > 0) {
            await this.props.loadEnumTypes(missingOptionTypes, locale);
            // Check if the localization package is already loaded
            const { localizeKey, key } = getLocaleKey(missingOptionTypes[0]);
            if (!localizeKey) {
                // If we dont have the localization key we need to load from the package as it will not come from a page.
                await this.props.loadTranslationsEnum(`${missingOptionTypes[0].split('/', 2).join('/')}${key}`);
            }
            this.setState({ fetchingOptionTypes: false });
        } else {
            this.setState({ fetchingOptionTypes: false });
        }

        const missingReferenceTypes = this.getMissingReferenceTypes();
        if (missingReferenceTypes.length > 0) {
            await this.props.loadDataTypes(missingReferenceTypes, locale);
            this.setState({ fetchingReferenceTypes: false });
        } else {
            this.setState({ fetchingReferenceTypes: false });
        }
    }

    componentDidMount(): void {
        this.checkDataModel();
    }

    componentDidUpdate(prevProps: DynamicPodComponentProps): void {
        if (!isEqual(prevProps.fieldProperties.columns, this.props.fieldProperties.columns)) {
            this.checkDataModel();
        }
    }

    private readonly onChange = async (bind: string, value: any): Promise<void> => {
        const newValue = { ...this.props.value };
        set(newValue, bind, value);
        handleChange(
            this.props.elementId,
            newValue,
            this.props.setFieldValue,
            this.props.validate,
            this.triggerChangeListener,
        );
    };

    private readonly onRemove = (): void =>
        handleChange(
            this.props.elementId,
            null,
            this.props.setFieldValue,
            this.props.validate,
            this.triggerChangeListener,
        );

    private readonly onNewPod = async (): Promise<void> => {
        const columns = this.props.fieldProperties.columns;

        let value = columns?.reduce((prev, curr) => {
            return set(prev, convertDeepBindToPathNotNull(curr.properties.bind), null);
        }, {});
        if (this.props.fieldProperties.onAddButtonClick) {
            const state = xtremRedux.getStore().getState();
            const screenDefinition = state.screenDefinitions[this.props.screenId];
            const newValue = await this.props.fieldProperties.onAddButtonClick.apply(
                getScreenElement(screenDefinition),
            );
            value = { ...value, ...newValue };
        }
        handleChange(
            this.props.elementId,
            value,
            this.props.setFieldValue,
            this.props.validate,
            this.triggerChangeListener,
        );
    };

    private addReferenceDataTypeInfo(): NestedField<any, any>[] {
        return (this.props.fieldProperties.columns || []).map(c => {
            if (
                c.properties._controlObjectType === FieldKey.Reference &&
                this.props.nodeTypes &&
                this.props.dataTypes
            ) {
                return createDynamicPodReferenceField({
                    column: c,
                    nodeTypes: this.props.nodeTypes,
                    dataTypes: this.props.dataTypes,
                });
            }
            return c;
        });
    }

    render(): React.ReactNode {
        const { fieldProperties, browser, availableColumns, value, elementId, screenId } = this.props;
        const podWidth = browser && calculateContainerWidth(browser.is, availableColumns || 12, 'small');
        if (this.state.fetchingOptionTypes || this.state.fetchingReferenceTypes) {
            return <Loader />;
        }

        const podProps: PodProps = {
            value,
            fieldProperties: { ...fieldProperties, columns: this.addReferenceDataTypeInfo() },
            browser,
            elementId,
            screenId,
            availableColumns: podWidth,
            isDisabled: this.isDisabled(),
            isReadOnly: this.isReadOnly(),
            onBlockClick: this.getClickHandler(),
            onChange: this.onChange,
            baseAttributesDivWrapper: this.getBaseAttributesDivWrapper(
                'dynamic-pod',
                'e-dynamic-pod-field',
                this.props.contextType,
                this.props.handlersArguments?.rowValue,
                this.props.isNested,
            ),
            onNewPod: this.onNewPod,
            contextType: this.props.contextType,
            onRemove: this.onRemove,
            validationErrors: this.props.validationErrors,
        };

        return <Pod {...podProps} />;
    }
}

const extendedMapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: FieldComponentExternalProperties,
): DynamicPodComponentProps => {
    const componentProperties = mapStateToProps()(state, props) as EditableFieldComponentProperties<
        DynamicPodDecoratorProperties,
        any
    >;
    const screenDefinition = state.screenDefinitions[props.screenId];

    if (componentProperties.fieldProperties?.onAddButtonClick) {
        componentProperties.fieldProperties.onAddButtonClick =
            componentProperties.fieldProperties.onAddButtonClick.bind(getScreenElement(screenDefinition));
    }

    return {
        ...props,
        ...componentProperties,
        enumTypes: state.enumTypes || {},
        dataTypes: state.dataTypes || {},
        loadEnumTypes: xtremRedux.actions.actionStub,
        loadDataTypes: xtremRedux.actions.actionStub,
        loadTranslationsEnum: xtremRedux.actions.actionStub,
    };
};

const extendedMapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: FieldComponentExternalProperties,
): Partial<DynamicPodComponentProps> => {
    const defaultMapDispatchToProps = mapDispatchToProps<DynamicPodDecoratorProperties>()(dispatch, props);

    return {
        ...defaultMapDispatchToProps,
        loadEnumTypes: (optionTypes: string[], locale: string): Promise<void> =>
            dispatch(xtremRedux.actions.loadEnumType(optionTypes, locale)),
        loadDataTypes: (referenceTypes: string[], locale: string): Promise<void> =>
            dispatch(xtremRedux.actions.fetchNodeTypes(referenceTypes, locale)),
        loadTranslationsEnum: (enumName: string): Promise<void> =>
            dispatch(xtremRedux.actions.fetchTranslationByPackage(enumName)),
    };
};

export const ConnectedDynamicPodComponent = connect(
    extendedMapStateToProps,
    extendedMapDispatchToProps,
)(DynamicPodComponent);

export default ConnectedDynamicPodComponent;
