import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { DynamicPodControlObject } from './dynamic-pod-control-object';
import type { DynamicPodDecoratorProperties } from './dynamic-pod-types';

class DynamicPodDecorator extends AbstractFieldDecorator<FieldKey.DynamicPod> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = DynamicPodControlObject;
}

/**
 * Initializes the decorated member as a [DynamicPodDecorator]{@link DynamicPodDecoratorControlObject} field with the provided properties.
 *
 * @param properties The properties that the [DynamicPodDecorator]{@link DynamicPodDecoratorControlObject} field will be initialized with.
 */
export function dynamicPodField<T extends ScreenExtension<T>>(
    properties: DynamicPodDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.DynamicPod>(
        properties,
        DynamicPodDecorator,
        FieldKey.DynamicPod,
    );
}

export function dynamicPodFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<DynamicPodDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.DynamicPod>(properties);
}
