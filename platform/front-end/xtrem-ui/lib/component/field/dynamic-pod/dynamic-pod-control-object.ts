/**
 * @packageDocumentation
 * @module root
 */
import type { ClientNode } from '@sage/xtrem-client';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import { showToast } from '../../../service/toast-service';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { DynamicPodProperties } from './dynamic-pod-types';
import type { NestedField } from '../../nested-fields';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';

/**
 * [Field]{@link EditableFieldControlObject} that holds a value from a set of given values.
 */
export class DynamicPodControlObject<
    NodeType extends ClientNode = any,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.DynamicPod, FieldComponentProps<FieldKey.DynamicPod>> {
    static readonly defaultUiProperties: Partial<DynamicPodProperties> = {
        ...EditableFieldControlObject.defaultUiProperties,
    };

    /** Graphql filter that will restrict the results of the reference field */
    get filter(): GraphQLFilter<NodeType> | undefined {
        return resolveByValue({
            fieldValue: undefined,
            propertyValue: this.getUiComponentProperty('filter'),
            rowValue: undefined,
            screenId: this.screenId,
            skipHexFormat: true,
        });
    }

    /** Graphql filter that will restrict the results of the reference field */
    set filter(filter: GraphQLFilter<NodeType> | undefined) {
        this.setUiComponentProperties('filter', filter);
        this.refresh().catch(() => {
            /* Intentional fire and forget */
        });
    }

    /** The GraphQL node that the field suggestions will be fetched from */
    get node(): string {
        return String(this.getUiComponentProperty('node'));
    }

    @ControlObjectProperty<DynamicPodProperties<CT, NodeType>, DynamicPodControlObject<NodeType, CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<DynamicPodProperties<CT, NodeType>, DynamicPodControlObject<NodeType, CT>>()
    /** Whether the value of the pod can be unset */
    canRemove?: boolean;

    @ControlObjectProperty<DynamicPodProperties<CT, NodeType>, DynamicPodControlObject<NodeType, CT>>()
    /** Placeholder to be displayed in the field body */
    placeholder?: string;

    async refresh(): Promise<void> {
        await this._refresh({ keepPageInfo: true }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }

    focus(): void {
        this._focus();
    }

    /** Set a new list of nested fields to be show in the pod  */
    setNestedFields(nestedFields: NestedField<any, any>[]): void {
        this.setUiComponentProperties('columns', nestedFields);
    }

    /** Add a individual nested field to the end of pod */
    addNestedField(nestedField: NestedField<any, any>): void {
        const columns = this.getUiComponentProperty('columns') || [];
        this.setUiComponentProperties('columns', [...columns, nestedField]);
    }

    /** Remove the nested field by the bind */
    removeNestedField(bind: string | Object): void {
        const columns = [...(this.getUiComponentProperty('columns') || [])];
        columns.filter(c => {
            return convertDeepBindToPathNotNull(c.properties.bind) !== convertDeepBindToPathNotNull(bind);
        });
        this.setUiComponentProperties('columns', columns);
    }

    /** Remove all nested fields */
    removeAllNestedFields(): void {
        this.setUiComponentProperties('columns', []);
    }
}
