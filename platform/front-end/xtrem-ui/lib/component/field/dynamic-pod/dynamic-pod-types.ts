import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { BlockControlObject, SectionControlObject } from '../../container/container-control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { PodNestedFieldTypes } from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasColumns,
    HasDropdownActions,
    HasFilter,
    HasHeaderLabel,
    HasHelperText,
    HasNode,
    HasParent,
    HasPlaceholder,
    Sizable,
    Validatable,
} from '../traits';
import type { Dict } from '@sage/xtrem-shared';
import type { DataTypeDetails } from '../../../service/metadata-types';

export interface DynamicPodProperties<
    CT extends ScreenExtension<CT> = ScreenBase,
    NestedRecordType extends ClientNode = any,
> extends EditableFieldProperties<CT, NestedRecordType>,
        Changeable<CT>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>>,
        HasPlaceholder,
        HasHelperText,
        HasNode<CT>,
        HasColumns<CT, NestedRecordType, PodNestedFieldTypes>,
        Sizable,
        HasFilter<CT, NestedRecordType>,
        HasDropdownActions<CT>,
        HasHeaderLabel<CT, NestedRecordType>,
        Validatable<CT> {
    /** Label on the add button that is displayed when the field is empty. */
    addButtonText?: string;

    onAddButtonClick?: (this: CT) => Promise<Partial<NestedRecordType>> | Partial<NestedRecordType>;

    /** Pod field can be removed and set to null, default: false */
    canRemove?: boolean;
}

export interface DynamicPodDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<DynamicPodProperties<CT>, '_controlObjectType'> {}

export interface DynamicPodComponentProps extends BaseEditableComponentProperties<DynamicPodDecoratorProperties, any> {
    loadEnumTypes: (optionType: string[], locale: string) => Promise<void>;
    loadDataTypes: (referenceTypes: string[], locale: string) => Promise<void>;
    loadTranslationsEnum: (enumName: string) => Promise<void>;
    enumTypes: Dict<string[]>;
    dataTypes: Dict<DataTypeDetails>;
}
