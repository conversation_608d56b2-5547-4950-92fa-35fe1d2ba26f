jest.mock('../../text/async-text-component');
jest.mock('../../text-area/async-text-area-component');
jest.mock('../../reference/async-reference-component');
jest.mock('../../label/async-label-component');
jest.mock('../../numeric/async-numeric-component');
jest.mock('../../date/async-date-component');
jest.mock('../../image/async-image-component');

import { cleanup, render, screen, waitFor, fireEvent } from '@testing-library/react';
import type { DynamicPodProperties } from '../dynamic-pod-types';
import type { ScreenBase } from '../../../../service/screen-base';
import * as React from 'react';
import { Provider } from 'react-redux';
import type { Store } from 'redux';
import { nestedFields } from '../../../..';
import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getStore,
    userEvent,
} from '../../../../__tests__/test-helpers';
import { FieldKey } from '../../../types';
import * as abstractFieldUtils from '../../../../utils/abstract-fields-utils';
import type { AppAction, XtremAppState } from '../../../../redux';
import type { PageDefinition } from '../../../../service/page-definition';
import type * as pageMetadataImport from '../../../../service/page-metadata';
import baseTheme from 'carbon-react/esm/style/themes/sage';
import { ThemeProvider } from 'styled-components';
import ConnectedDynamicPodComponent from '../dynamic-pod-component';

let mockStore: Store<XtremAppState, AppAction> | null = null;
jest.mock('../../../../redux', () => {
    const actual = jest.requireActual('../../../../redux');
    return {
        getStore: () => mockStore,
        actions: actual.actions,
        ActionType: actual.ActionType,
    };
});
jest.mock('../../../../redux/store', () => {
    return {
        getStore: () => mockStore,
    };
});

jest.useFakeTimers();

describe('Dynamic Pod Component', () => {
    const screenId = 'testScreen';
    const elementId = 'testElement';

    const setup = (props: Omit<DynamicPodProperties<ScreenBase, any>, 'columns'> = {}, fieldValue: any = null) => {
        const fieldProperties: DynamicPodProperties<ScreenBase, any> = {
            ...props,
            ...{
                columns: [
                    nestedFields.text({ bind: 'name', title: 'Name' }),
                    nestedFields.text({ bind: { data: { value: true } }, title: 'Data Value' }),
                ],

                skipDirtyCheck: true,
            },
        };

        const store = () => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(
                screenId,
                {
                    page: { $: { isTransactionInProgress: () => false }, _pageMetadata: { screenId } },
                } as Partial<PageDefinition>,
                {
                    uiComponentProperties: { [screenId]: { skipDirtyCheck: true } },
                } as Partial<pageMetadataImport.PageMetadata>,
            );
            addFieldToState(FieldKey.DynamicPod, state, screenId, elementId, fieldProperties, fieldValue);
            mockStore = getStore(state);
        };

        store();
        return render(
            <ThemeProvider theme={baseTheme}>
                <Provider store={mockStore!}>
                    <ConnectedDynamicPodComponent elementId={elementId} screenId={screenId} />
                </Provider>
            </ThemeProvider>,
        );
    };

    let handleChangeSpy: jest.SpyInstance;

    beforeEach(() => {
        handleChangeSpy = jest.spyOn(abstractFieldUtils, 'handleChange');
    });

    afterEach(async () => {
        mockStore = null;
        handleChangeSpy.mockReset();
        await cleanup();
    });

    it('Should render with add new button and click creates a new pod', async () => {
        setup();
        const newButton = screen.getByTestId('e-pod-add-new');
        expect(newButton).toBeInTheDocument();
        expect(handleChangeSpy).not.toHaveBeenCalled();
        await userEvent.click(newButton);
        expect(handleChangeSpy).toHaveBeenCalled();
        await waitFor(() => {
            expect(newButton).not.toBeInTheDocument();
        });
        await waitFor(() => {
            const nameField = screen.getByTestId('e-field-bind-name', { exact: false });
            expect(nameField).toBeInTheDocument();
        });
    });

    it('Should render with title', () => {
        const wrapper = setup({ title: 'test pod' }, JSON.stringify({ name: 'test', data: { value: 'test' } }));
        const title = wrapper.baseElement.querySelector('.e-pod-title');
        expect(title).toBeInTheDocument();
        expect(title!).toHaveTextContent('test pod');
    });

    it('Should render without title', () => {
        setup(undefined, JSON.stringify({ name: 'test', data: { value: 'test' } }));
        expect(screen.queryByTestId('e-field-label'))!.toBeNull();
    });

    it('Should render without header label', () => {
        const wrapper = setup({ title: 'test pod' }, JSON.stringify({ name: 'test', data: { value: 'test' } }));
        const headerLabel = wrapper.baseElement.querySelector('.e-pod-collection-header-label');
        expect(headerLabel).toBeNull();
    });

    it('Should render with placeholder text when no value is provided', () => {
        setup();
        const placeholderText = screen.queryByText('No data to display');
        expect(placeholderText).toBeTruthy();
    });

    it('Should render with custom placeholder text when no value is provided', () => {
        setup({ placeholder: 'No Patatas to display' });
        const placeholderText = screen.queryByText('No Patatas to display');
        expect(placeholderText).toBeTruthy();
    });

    it('Should render with add new button because no value', () => {
        setup();
        const newButton = screen.queryByTestId('e-pod-add-new')!;
        expect(newButton!).toBeTruthy();
        expect(newButton.querySelector('span[data-element="main-text"]')!.innerHTML).toBe('Add an item');
    });

    it('Should render with info message', () => {
        const wrapper = setup(
            { title: 'test pod', infoMessage: 'Hi there!' },
            JSON.stringify({ name: 'test', data: { value: 'test' } }),
        );
        const icon = wrapper.baseElement.querySelector('.e-icon-info-message');
        expect(icon).not.toBeNull();
    });

    it('Should render with info message callback', () => {
        const wrapper = setup(
            { title: 'test pod', infoMessage: () => 'Hi there!' },
            JSON.stringify({ name: 'test', data: { value: 'test' } }),
        );
        const icon = wrapper.baseElement.querySelector('.e-icon-info-message');
        expect(icon).not.toBeNull();
    });

    it('Should render with warning message', () => {
        const wrapper = setup(
            { title: 'test pod', warningMessage: 'Hi there!' },
            JSON.stringify({ name: 'test', data: { value: 'test' } }),
        );
        const icon = wrapper.baseElement.querySelector('.e-icon-warning-message');
        expect(icon).not.toBeNull();
    });

    it('Should render with warning message callback', () => {
        const wrapper = setup(
            { title: 'test pod', warningMessage: () => 'Hi there!' },
            JSON.stringify({ name: 'test', data: { value: 'test' } }),
        );
        const icon = wrapper.baseElement.querySelector('.e-icon-warning-message');
        expect(icon).not.toBeNull();
    });

    it('Should prioritize warning over info messages', () => {
        const wrapper = setup(
            { title: 'test pod', warningMessage: () => 'Hi there!', infoMessage: 'Hi again.' },
            JSON.stringify({ name: 'test', data: { value: 'test' } }),
        );
        const warningIcon = wrapper.baseElement.querySelector('.e-icon-warning-message');
        expect(warningIcon).not.toBeNull();
        const infoIcon = wrapper.baseElement.querySelector('.e-icon-info-message');
        expect(infoIcon).toBeNull();
    });

    it('Should render with custom text button for add new pod', () => {
        setup({ addButtonText: 'Add new patata' });
        const newButton = screen.queryByTestId('e-pod-add-new')!;
        expect(newButton).toBeTruthy();
        expect(newButton.querySelector('span[data-element="main-text"]')!.innerHTML).toBe('Add new patata');
    });

    it('Should call handle change when input is modified', async () => {
        setup({}, {});
        await waitFor(() => {
            const nameField = screen.getByTestId('e-field-bind-name', { exact: false });
            expect(nameField).toBeInTheDocument();
        });
        expect(handleChangeSpy).not.toHaveBeenCalled();

        const input = screen.getByTestId('e-field-bind-name', { exact: false })!.querySelector('input')!;
        await fireEvent.change(input, { target: { value: 'New value' } });
        await fireEvent.blur(input, { target: { value: 'New value' } });

        expect(handleChangeSpy).toHaveBeenCalledWith(
            'name',
            'New value',
            expect.anything(),
            expect.anything(),
            expect.anything(),
        );
    });
});
