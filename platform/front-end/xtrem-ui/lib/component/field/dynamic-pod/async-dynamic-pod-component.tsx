import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import type { DynamicPodComponentProps } from './dynamic-pod-types';

const ConnectedDynamicPodComponent = React.lazy(() => import('./dynamic-pod-component'));

export function AsyncConnectedDynamicPodComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="200px" />}>
            <ConnectedDynamicPodComponent {...props} />
        </React.Suspense>
    );
}

const DynamicPodComponent = React.lazy(() =>
    import('./dynamic-pod-component').then(e => ({ default: e.DynamicPodComponent })),
);

export function AsyncdynamicPodComponent(props: DynamicPodComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={false} bodyHeight="200px" />}>
            <DynamicPodComponent {...props} />
        </React.Suspense>
    );
}
