import type { ValidationResult } from '../../service/screen-base-definition';
import * as wrapperService from '../../service/wrapper-service';
import { ContextType } from '../../types';
import { triggerFieldEvent, triggerNestedFieldEvent } from '../../utils/events';
import { getScalePrefixPostfixFromUnit } from '../../utils/formatters';
import { resolveByValue } from '../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../utils/transformers';
import type { ValueOrCallbackWithFieldValue } from '../../utils/types';
import type { UiComponentProperties } from '../abstract-ui-control-object';
import type { PageProperties } from '../container/container-properties';
import type { EditableFieldProperties } from '../editable-field-control-object';
import type { NestedFieldHandlersArguments } from '../nested-fields';
import type { ReadonlyFieldProperties } from '../readonly-field-control-object';
import { getFieldElement } from './carbon-utility-components';
import type {
    EditableFieldComponentProperties,
    FieldComponentExternalProperties,
    NestedFieldsAdditionalProperties,
} from './field-base-component-types';
import { handleChange } from './field-base-utils';
import type { NumericProperties } from './numeric/numeric-types';
import { getReferenceValueField } from './reference/reference-utils';

export type FieldProps = EditableFieldComponentProperties<any, any> &
    FieldComponentExternalProperties &
    NestedFieldsAdditionalProperties;

export const getLabelTitle = (
    screenId: string,
    fieldProperties: Pick<EditableFieldProperties, 'isTitleHidden' | 'isMandatory' | 'title'>,
    rowValue: any,
): string | undefined => {
    if (fieldProperties.isTitleHidden) {
        return undefined;
    }

    const resolvedTitle = getFieldTitle(screenId, fieldProperties, rowValue);
    const isMandatory = resolveByValue({
        propertyValue: fieldProperties.isMandatory,
        screenId,
        skipHexFormat: true,
        fieldValue: undefined,
        rowValue: undefined,
    });
    const isNotZero = resolveByValue({
        propertyValue: (fieldProperties as NumericProperties).isNotZero,
        screenId,
        skipHexFormat: true,
        fieldValue: undefined,
        rowValue: undefined,
    });

    if (resolvedTitle && (isMandatory || isNotZero)) {
        return `${resolvedTitle} *`;
    }

    if (resolvedTitle) {
        return `${resolvedTitle}`;
    }

    if (isMandatory || isNotZero) {
        return '*';
    }

    return undefined;
};

export const generateFieldId = ({
    contextType,
    elementId,
    fieldProperties,
    isNested,
    parentElementId,
    screenId,
}: {
    contextType?: ContextType;
    elementId: string;
    fieldProperties: any;
    isNested: boolean;
    parentElementId?: string;
    screenId: string;
}): string => {
    const nestedValueField =
        isNested && Object.prototype.hasOwnProperty.call(fieldProperties, 'valueField')
            ? getReferenceValueField(fieldProperties)?.split('.').join('-')
            : undefined;
    return [screenId, parentElementId, elementId, nestedValueField, contextType].filter(Boolean).join('-');
};

export const isFieldReadOnly = (
    screenId: string,
    fieldProperties: Pick<EditableFieldProperties, 'isReadOnly'>,
    fieldValue: any,
    rowValue: any,
    contextType?: ContextType,
): boolean =>
    contextType === ContextType.navigationPanel ||
    contextType === ContextType.header ||
    resolveByValue<boolean>({
        fieldValue,
        propertyValue: fieldProperties.isReadOnly,
        skipHexFormat: true,
        rowValue: rowValue ? splitValueToMergedValue(rowValue) : rowValue,
        screenId,
    });

interface GetFieldIndicatorStatusArgs {
    screenId: string;
    value?: any;
    validationErrors?: ValidationResult[];
    fieldProperties: {
        warningMessage?: ValueOrCallbackWithFieldValue<any, string>;
        infoMessage?: ValueOrCallbackWithFieldValue<any, string>;
    };
    handlersArguments?: NestedFieldHandlersArguments;
    isParentDisabled?: boolean;
    isParentHidden?: boolean;
}

interface CarbonProps {
    autoComplete: string;
    name: string;
    fieldHelp: any;
    label: string | undefined;
    size: any;
    placeholder: any;
    children: {} | null | undefined;
    leftChildren: any;
    tabindex: number | undefined;
    onFocus: any;
    onBlur: any;
    error: any;
    info: any;
    warning: any;
    validationOnLabel: any;
    id: any;
    disabled?: boolean;
    readOnly?: boolean;
    'aria-describedby'?: string;
    'data-label'?: string;
}

export const getFieldIndicatorStatus = ({
    validationErrors,
    screenId,
    value: fieldValue,
    fieldProperties,
    handlersArguments,
    isParentDisabled,
    isParentHidden,
}: GetFieldIndicatorStatusArgs): {
    error?: string;
    info?: string;
    warning?: string;
} => {
    const rowValue = handlersArguments?.rowValue ? splitValueToMergedValue(handlersArguments?.rowValue) : undefined;
    const error =
        !isParentDisabled &&
        !isParentHidden &&
        !isFieldDisabled(screenId, fieldProperties as EditableFieldProperties, fieldValue, handlersArguments?.rowValue)
            ? validationErrors?.[0]?.message || undefined
            : undefined;

    const warning =
        (!error &&
            resolveByValue({
                screenId,
                fieldValue,
                propertyValue: fieldProperties.warningMessage,
                rowValue,
                skipHexFormat: true,
            })) ||
        undefined;

    const info =
        (!error &&
            !warning &&
            resolveByValue({
                screenId,
                fieldValue,
                propertyValue: fieldProperties.infoMessage,
                rowValue,
                skipHexFormat: true,
            })) ||
        undefined;

    return { info, error, warning };
};

export const isFieldDisabled = (
    screenId: string,
    fieldProperties: Pick<EditableFieldProperties, 'isDisabled'>,
    fieldValue: any,
    rowValue: any,
): boolean =>
    resolveByValue<boolean>({
        fieldValue,
        propertyValue: fieldProperties.isDisabled,
        skipHexFormat: true,
        rowValue: rowValue ? splitValueToMergedValue(rowValue) : rowValue,
        screenId,
    });

export const getFieldTitle = (
    screenId: string,
    fieldProperties: Pick<UiComponentProperties, 'title'>,
    rowValue: any,
): string =>
    resolveByValue<string>({
        screenId,
        propertyValue: fieldProperties.title,
        skipHexFormat: true,
        rowValue: splitValueToMergedValue(rowValue),
    });

export const isFieldHidden = (
    screenId: string,
    fieldProperties: Pick<UiComponentProperties, 'isHidden'>,
    rowValue: any,
): boolean =>
    resolveByValue<boolean>({
        screenId,
        propertyValue: fieldProperties.isHidden,
        skipHexFormat: true,
        rowValue: splitValueToMergedValue(rowValue),
    });

export const isFieldTitleHidden = (
    screenId: string,
    fieldProperties: Pick<UiComponentProperties, 'isTitleHidden'>,
    rowValue: any,
): boolean =>
    resolveByValue<boolean>({
        screenId,
        propertyValue: fieldProperties.isTitleHidden,
        skipHexFormat: true,
        rowValue: splitValueToMergedValue(rowValue),
    });

export const getFieldHelperText = (
    screenId: string,
    fieldProperties: Pick<ReadonlyFieldProperties, 'helperText'>,
    rowValue: any,
): string =>
    resolveByValue<string>({
        screenId,
        propertyValue: fieldProperties.helperText,
        skipHexFormat: true,
        rowValue: splitValueToMergedValue(rowValue),
    });

export const isFieldHelperTextHidden = (
    screenId: string,
    fieldProperties: Pick<ReadonlyFieldProperties, 'isHelperTextHidden'>,
    rowValue: any,
): string =>
    resolveByValue<string>({
        screenId,
        propertyValue: fieldProperties.isHelperTextHidden,
        skipHexFormat: true,
        rowValue: splitValueToMergedValue(rowValue),
    });

export const getPageSubtitle = (
    screenId: string,
    fieldProperties: Pick<PageProperties, 'subtitle'>,
    rowValue: any,
): string | null =>
    resolveByValue<string>({
        screenId,
        propertyValue: fieldProperties.subtitle,
        skipHexFormat: true,
        rowValue: splitValueToMergedValue(rowValue),
    }) || null;

export const onBlurHandler = (props: FieldProps): void => {
    if (isFieldDisabled(props.screenId, props.fieldProperties, props.value, props.handlersArguments?.rowValue)) {
        return;
    }

    if (isFieldReadOnly(props.screenId, props.fieldProperties, props.value, props.handlersArguments?.rowValue)) {
        return;
    }

    wrapperService.onBlur({
        elementId: props.parentElementId || props.elementId,
        screenId: props.screenId,
        nestedElementId: props.parentElementId ? props.elementId : undefined,
        recordId: props.handlersArguments?.rowValue._id,
    });
};

export const onFocusHandler = (props: FieldProps): void => {
    if (isFieldDisabled(props.screenId, props.fieldProperties, props.value, props.handlersArguments?.rowValue)) {
        return;
    }

    if (isFieldReadOnly(props.screenId, props.fieldProperties, props.value, props.handlersArguments?.rowValue)) {
        return;
    }

    const callback = async (rawValue: string): Promise<void> => {
        await props.setFieldValue(props.elementId, rawValue);
        const triggerChangeListener = async (): Promise<void> => {
            const nestedFieldsAdditionalProperties = props as NestedFieldsAdditionalProperties;

            if (nestedFieldsAdditionalProperties.isNested) {
                await triggerNestedFieldEvent(
                    props.screenId,
                    nestedFieldsAdditionalProperties.parentElementId || props.elementId,
                    props.fieldProperties,
                    'onChange',
                    nestedFieldsAdditionalProperties.handlersArguments?.rowValue._id,
                    nestedFieldsAdditionalProperties.handlersArguments?.rowValue,
                );
            } else {
                await triggerFieldEvent(props.screenId, props.elementId, 'onChange');
            }
        };
        handleChange(props.elementId, rawValue, props.setFieldValue, props.validate as any, triggerChangeListener);
    };

    wrapperService.onFocus(
        {
            elementId: props.parentElementId || props.elementId,
            screenId: props.screenId,
            nestedElementId: props.parentElementId ? props.elementId : undefined,
            recordId: props.handlersArguments?.rowValue._id,
            title: getFieldTitle(props.screenId, props.fieldProperties, null),
        },
        callback,
    );
};

export const getCommonCarbonComponentProperties = (props: FieldProps, children?: JSX.Element): any => {
    const rowValue = props.handlersArguments?.rowValue
        ? splitValueToMergedValue(props.handlersArguments?.rowValue)
        : undefined;
    const {
        contextType,
        elementId,
        fieldProperties,
        isNested = false,
        parentElementId,
        screenId,
        isParentHidden,
        isParentDisabled,
        locale,
    } = props;
    const { error, warning, info } = getFieldIndicatorStatus(props);

    const computedUnitProperties = getScalePrefixPostfixFromUnit(screenId, locale, fieldProperties, rowValue);

    const carbonProps: CarbonProps = {
        autoComplete: 'off',
        name: props.elementId,
        disabled:
            isParentDisabled ||
            isParentHidden ||
            isFieldDisabled(props.screenId, props.fieldProperties, props.value, rowValue),
        readOnly: isFieldReadOnly(props.screenId, props.fieldProperties, props.value, rowValue, props.contextType),
        fieldHelp: props.fieldProperties.helperText,
        label:
            props.contextType === ContextType.navigationPanel || props.contextType === ContextType.header
                ? undefined
                : getLabelTitle(props.screenId, props.fieldProperties, rowValue),
        size: props.fieldProperties.size,
        placeholder: props.fieldProperties.placeholder,
        children:
            children ||
            getFieldElement(
                'e-field-postfix',
                computedUnitProperties?.postfix ??
                    resolveByValue({
                        screenId: props.screenId,
                        fieldValue: props.value,
                        propertyValue: props.fieldProperties.postfix,
                        rowValue,
                        skipHexFormat: true,
                    }),
            ),
        leftChildren: getFieldElement(
            'e-field-prefix',
            computedUnitProperties?.prefix ??
                resolveByValue({
                    screenId: props.screenId,
                    fieldValue: props.value,
                    propertyValue: props.fieldProperties.prefix,
                    rowValue,
                    skipHexFormat: true,
                }),
        ),
        tabindex: props.contextType === ContextType.navigationPanel ? -1 : undefined,
        onFocus: (): void => {
            onFocusHandler(props);
            props.onFocus();
        },
        onBlur: (): void => onBlurHandler(props),
        error,
        info,
        warning,
        validationOnLabel: true,
        id: generateFieldId({ screenId, elementId, contextType, fieldProperties, parentElementId, isNested }),
    };

    if (carbonProps.disabled) {
        // Carbon throws an error if we display any tooltip help on disabled fields
        delete carbonProps.error;
        delete carbonProps.info;
        delete carbonProps.warning;
    }

    if (!props.fieldProperties.helperText) {
        carbonProps['aria-describedby'] = undefined;
    }

    carbonProps['data-label'] = getDataLabel(props);

    return carbonProps;
};

/**
 * 'data-label' is used for alignment purposes only
 *
 * @param {(EditableFieldComponentProperties<any, any> & FieldComponentExternalProperties & NestedFieldsAdditionalProperties)} props
 * @returns
 */
function getDataLabel(
    props: EditableFieldComponentProperties<any, any> &
        FieldComponentExternalProperties &
        NestedFieldsAdditionalProperties,
): string | undefined {
    if (props.fieldProperties.isTitleHidden) {
        return undefined;
    }
    if (props.contextType === ContextType.navigationPanel || props.contextType === ContextType.header) {
        return getFieldTitle(props.screenId, props.fieldProperties, props.handlersArguments?.rowValue) ?? '';
    }
    return getLabelTitle(props.screenId, props.fieldProperties, props.handlersArguments?.rowValue);
}
