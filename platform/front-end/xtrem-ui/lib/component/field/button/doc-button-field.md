PATH: XTREEM/UI+Field+Widgets/Button+Field

## Introduction

The button field represents a button in a container of pages within the user interface.

## Example

```ts
@ui.decorators.buttonField<Page>({
    helperText: 'Click the button to launch the onClick handler.',
    isDisabled: false,
    isHelperTextHidden: false,
    isHidden: false,
    isTitleHidden: false,
    size: 'medium',
    title: 'Button Field',
    value: 'Button',
    shortcut:['shift', 'alt', 't']
    onClick() {
        console.log('Do something when the button is clicked.');
    },
    parent() {
        return this.block;
    },
})
field: ui.fields.Button;

@ui.decorators.buttonField<Page>({
    value: 'Focus',
    onClick() {
        this.field.focus();
    },
    parent() {
        return this.block;
    },
})
focus: ui.fields.Button;
```

## Decorator Properties

### Display Properties

-   **helperText**: The helper text displayed below the field. This property is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Determines whether the field is disabled or not.
-   **isHelperTextHidden**: Determines whether the field's helper text is displayed or not.
-   **isHidden**: Determines whether the field is displayed or not.
-   **shortcut**: key or key combination that triggers the onClick event, more about key combinations [here](./Key+Shortcuts+API).
-   **isTitleHidden**: Determines whether the field's title is displayed or not.
-   **size**: Size the field's button should be rendered in. The options are "small", "medium" and "large".
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **value**: The field's button text.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

### Event Handler Properties

-   **onClick**: Handler triggered when the field's button is clicked.

### Runtime Functions

-   **focus()**: Sets the browser's focus to the field.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Button).
