import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { FieldKey } from '../../../types';
import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    applyActionMocks,
} from '../../../../__tests__/test-helpers';
import type * as xtremRedux from '../../../../redux';
import type { ScreenBase } from '../../../../service/screen-base';
import * as events from '../../../../utils/events';
import { ButtonComponent, ConnectedButtonComponent } from '../button-component';
import type { ButtonDecoratorProperties } from '../button-types';
import { cleanup, fireEvent, render } from '@testing-library/react';

describe('Button component', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: ButtonDecoratorProperties<ScreenBase>;

    beforeEach(() => {
        mockFieldProperties = {
            title: 'Test Button',
        };
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
        applyActionMocks();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Button, state, screenId, 'test-button-field', mockFieldProperties, 'Button label');
            addFieldToState(FieldKey.Button, state, screenId, 'test-empty-button-field', mockFieldProperties, null);
            mockStore = getMockStore(state);
        });

        afterEach(() => {
            jest.resetAllMocks();
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedButtonComponent screenId={screenId} elementId="test-button-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with the mapped value', () => {
                mockFieldProperties.map = () => 'Button Mapped Value';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedButtonComponent screenId={screenId} elementId="test-button-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedButtonComponent screenId={screenId} elementId="test-button-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render disabled', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedButtonComponent screenId={screenId} elementId="test-button-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render without value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedButtonComponent screenId={screenId} elementId="test-empty-button-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with various field sizes', () => {
                mockFieldProperties.size = 'small';
                let wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedButtonComponent screenId={screenId} elementId="test-button-field" />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'medium';
                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedButtonComponent screenId={screenId} elementId="test-button-field" />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'large';
                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedButtonComponent screenId={screenId} elementId="test-button-field" />
                    </Provider>,
                    {},
                );
                expect(wrapper.container).toMatchSnapshot();
            });
        });

        describe('Interactions', () => {
            it('should trigger the custom click event handler', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedButtonComponent screenId={screenId} elementId="test-button-field" />
                    </Provider>,
                );

                expect(events.triggerFieldEvent).not.toHaveBeenCalled();

                fireEvent.click(container.querySelector('button')!);

                expect(events.triggerFieldEvent).toHaveBeenCalledTimes(1);
                expect(events.triggerFieldEvent).toHaveBeenCalledWith(screenId, 'test-button-field', 'onClick');
            });
        });
    });

    describe('unconnected', () => {
        describe('Interactions', () => {
            it('should call the on click callback when the button is clicked', () => {
                const onClickMock = jest.spyOn(events, 'triggerFieldEvent').mockImplementation(jest.fn());
                const { container } = render(
                    <ButtonComponent
                        elementId="test-button-field"
                        fieldProperties={mockFieldProperties}
                        screenId={screenId}
                        locale="en-US"
                        onFocus={jest.fn()}
                    />,
                );

                expect(onClickMock).not.toHaveBeenCalled();

                fireEvent.click(container.querySelector('button')!);

                expect(onClickMock).toHaveBeenCalledTimes(1);
                expect(onClickMock).toHaveBeenCalledWith(screenId, 'test-button-field', 'onClick');
            });
        });
    });
});
