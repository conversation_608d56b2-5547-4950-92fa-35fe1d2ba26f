// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Button component connected Snapshots should render disabled 1`] = `
.c0 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: not-allowed;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  border-color: var(--colorsActionDisabled500);
  color: var(--colorsActionMajorYin030);
  font-size: var(--fontSizes100);
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c0:hover {
  background: transparent;
  border-color: var(--colorsActionDisabled500);
  color: var(--colorsActionMajorYin030);
}

.c0 img,
.c0 svg {
  opacity: 0.3;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <div
    class="e-field e-button-field e-disabled"
    data-label="Test Button"
    data-testid="e-button-field e-field-label-testButton e-field-bind-test-button-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Button
    </label>
    <button
      class="c0"
      data-component="button"
      disabled=""
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="c1"
          data-element="main-text"
        >
          Button label
        </span>
      </span>
    </button>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Button component connected Snapshots should render hidden 1`] = `
.c0 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <div
    class="e-field e-button-field e-hidden"
    data-label="Test Button"
    data-testid="e-button-field e-field-label-testButton e-field-bind-test-button-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Button
    </label>
    <button
      class="c0"
      data-component="button"
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="c1"
          data-element="main-text"
        >
          Button label
        </span>
      </span>
    </button>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Button component connected Snapshots should render with default properties 1`] = `
.c0 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <div
    class="e-field e-button-field"
    data-label="Test Button"
    data-testid="e-button-field e-field-label-testButton e-field-bind-test-button-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Button
    </label>
    <button
      class="c0"
      data-component="button"
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="c1"
          data-element="main-text"
        >
          Button label
        </span>
      </span>
    </button>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Button component connected Snapshots should render with the mapped value 1`] = `
.c0 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <div
    class="e-field e-button-field"
    data-label="Test Button"
    data-testid="e-button-field e-field-label-testButton e-field-bind-test-button-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Button
    </label>
    <button
      class="c0"
      data-component="button"
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="c1"
          data-element="main-text"
        >
          Button Mapped Value
        </span>
      </span>
    </button>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Button component connected Snapshots should render with various field sizes 1`] = `
.c0 {
  padding-left: var(--spacing200);
  padding-right: var(--spacing200);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 32px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <div
    class="e-field e-button-field"
    data-label="Test Button"
    data-testid="e-button-field e-field-label-testButton e-field-bind-test-button-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Button
    </label>
    <button
      class="c0"
      data-component="button"
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="c1"
          data-element="main-text"
        >
          Button label
        </span>
      </span>
    </button>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Button component connected Snapshots should render with various field sizes 2`] = `
.c0 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <div
    class="e-field e-button-field"
    data-label="Test Button"
    data-testid="e-button-field e-field-label-testButton e-field-bind-test-button-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Button
    </label>
    <button
      class="c0"
      data-component="button"
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="c1"
          data-element="main-text"
        >
          Button label
        </span>
      </span>
    </button>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Button component connected Snapshots should render with various field sizes 3`] = `
.c0 {
  padding-left: var(--spacing400);
  padding-right: var(--spacing400);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes200);
  min-height: 48px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c2 {
  font-size: 14px;
  font-weight: 400;
  display: block;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <div
    class="e-field e-button-field"
    data-label="Test Button"
    data-testid="e-button-field e-field-label-testButton e-field-bind-test-button-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Button
    </label>
    <button
      class="c0"
      data-component="button"
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="c1"
          data-element="main-text"
        >
          Button label
        </span>
        <span
          class="c2"
          data-element="subtext"
          data-role="subtext"
        />
      </span>
    </button>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Button component connected Snapshots should render without value 1`] = `
.c0 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <div
    class="e-field e-button-field"
    data-label="Test Button"
    data-testid="e-button-field e-field-label-testButton e-field-bind-test-empty-button-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Button
    </label>
    <button
      class="c0"
      data-component="button"
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="c1"
          data-element="main-text"
        >
          &nbsp;
        </span>
      </span>
    </button>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;
