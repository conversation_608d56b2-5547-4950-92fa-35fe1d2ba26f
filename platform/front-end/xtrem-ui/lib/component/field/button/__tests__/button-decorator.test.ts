import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { ButtonDecoratorProperties } from '../button-types';
import { buttonField } from '../button-decorator';

describe('Button decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;
    let mapFunc: (value?: any) => string;

    beforeEach(() => {
        fieldId = 'buttonField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
        mapFunc = jest.fn().mockImplementation(() => 'test map_return');
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        it('should set default values when no component properties provided', () => {
            buttonField({})({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties: ButtonDecoratorProperties<ScreenBase> =
                pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.map).toBeUndefined();
            expect(mappedComponentProperties.onClick).toBeUndefined();
        });

        it('should map key shortcuts', () => {
            buttonField({ shortcut: ['control', 'l'] })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties: ButtonDecoratorProperties<ScreenBase> =
                pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.shortcut).toEqual(['control', 'l']);
        });

        it('should inherit false abstract-field booleans when no provided', () => {
            buttonField({})({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties: ButtonDecoratorProperties<ScreenBase> =
                pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.isHiddenMobile).toBe(false);
            expect(mappedComponentProperties.isHiddenDesktop).toBe(false);
            expect(mappedComponentProperties.isFullWidth).toBe(false);
            expect(mappedComponentProperties.isHidden).toBe(false);
            expect(mappedComponentProperties.isTransient).toBe(false);
        });

        it('should set values when component properties provided', () => {
            buttonField({
                map: mapFunc,
                isHiddenDesktop: true,
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties: ButtonDecoratorProperties<ScreenBase> =
                pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.map).not.toBeUndefined();
            expect(mappedComponentProperties.map).toBe(mapFunc);
            expect(mappedComponentProperties.isHiddenDesktop).toBe(true);
        });

        it('should assign onClick handler', () => {
            testOnClickHandler(buttonField, pageMetadata, fieldId);
        });
    });
});
