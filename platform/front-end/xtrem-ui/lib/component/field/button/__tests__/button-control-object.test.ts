import { <PERSON><PERSON><PERSON> } from '../../../types';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import { ButtonControlObject } from '../../../control-objects';

describe('Button Field', () => {
    const screenId = 'TestPage';
    let buttonField: ButtonControlObject;

    beforeEach(() => {
        buttonField = createFieldControlObject<FieldKey.Button>(
            FieldKey.Button,
            screenId,
            ButtonControlObject,
            'test',
            'TEST_VALUE',
            {
                title: 'TEST_FIELD_TITLE',
                isHidden: true,
                isDisabled: true,
            },
        );
    });

    it('getting field value', () => {
        expect(buttonField.value).toEqual('TEST_VALUE');
    });
});
