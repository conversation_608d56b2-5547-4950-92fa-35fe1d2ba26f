import type { ButtonProps } from 'carbon-react/esm/components/button';
import Button from 'carbon-react/esm/components/button';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import * as React from 'react';
import { connect } from 'react-redux';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { mapDispatchToProps, mapReadonlyStateToProps, ReadonlyFieldBaseComponent } from '../field-base-component';
import type { ButtonDecoratorProperties } from './button-types';

export class ButtonComponent extends ReadonlyFieldBaseComponent<
    ButtonDecoratorProperties,
    string,
    {
        icon?: IconType;
        buttonType?: ButtonProps['buttonType'];
        isDestructive?: boolean;
        onClick?: () => void;
    }
> {
    getFocusableElement(element: HTMLElement): HTMLButtonElement | null {
        return element.querySelector('button');
    }

    onClick = (): void => {
        // TODO: improve "getClickHandler"
        if (this.props.onClick) {
            this.props.onClick();
            return;
        }
        this.props.onFocus();
        this.getClickHandler()();
    };

    render(): React.ReactNode {
        const label = !this.props.fieldProperties.isTitleHidden && this.getTitle();
        return (
            <div
                {...this.getBaseAttributesDivWrapper(
                    'button',
                    'e-button-field',
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    this.props.isNested,
                )}
                ref={this.componentRef}
            >
                {label && <FieldLabel label={label} />}
                <Button
                    disabled={this.isDisabled()}
                    onClick={this.onClick}
                    size={this.props.fieldProperties.size}
                    iconType={this.props.icon}
                    buttonType={this.props.buttonType}
                    destructive={this.props.isDestructive}
                >
                    {this.getValue() || '&nbsp;'}
                </Button>
                <HelperText helperText={this.props.fieldProperties.helperText} />
            </div>
        );
    }
}

export const ConnectedButtonComponent = connect(mapReadonlyStateToProps(), mapDispatchToProps())(ButtonComponent);

export default ConnectedButtonComponent;
