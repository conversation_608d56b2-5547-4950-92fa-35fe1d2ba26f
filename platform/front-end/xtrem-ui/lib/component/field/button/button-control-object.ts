/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { FieldComponentProps, FieldKey } from '../../types';

/**
 * [Field]{@link ReadonlyFieldControlObject} that allows the user to explicitly trigger some action
 */
export class ButtonControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends ReadonlyFieldControlObject<
    CT,
    FieldKey.Button,
    FieldComponentProps<FieldKey.Button>
> {
    /** The border color of the button */
    get borderColor(): string | undefined {
        return this.getResolvedProperty('borderColor', false);
    }

    /** The border color of the button */
    set borderColor(newValue: string | undefined) {
        this.setUiComponentProperties('borderColor', newValue);
    }

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }
}
