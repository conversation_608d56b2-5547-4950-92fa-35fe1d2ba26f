## Documentation - Button
| name               | optional | description                                                                                                                                                                                                                                    | type                                                                                                              | default                                                                                                                                              |
| ------------------ | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------- |
| access             | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">AccessConfiguration</pre>                                                                  | <pre lang="javascript">{&nbsp;node:&nbsp;'<node>',&nbsp;bind:&nbsp;'<bind>'&nbsp;}</pre>                                                             |
| bind               | true     | The GraphQL node that the field’s value is bound to. If not provided, the field name is used instead */ // eslint-disable-next-line @typescript-eslint/no-unused-vars                                                                          | <pre lang="javascript">ReferenceValueType<any></pre>                                                              | <pre lang="javascript">undefined</pre>                                                                                                               |
| borderColor        | true     | The border color of the button                                                                                                                                                                                                                 | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;any,&nbsp;Dict<any>></pre>             | <pre lang="javascript">''</pre>                                                                                                                      |
| helperText         | true     | A text that will be displayed below the field                                                                                                                                                                                                  | <pre lang="javascript">string</pre>                                                                               | <pre lang="javascript">''</pre>                                                                                                                      |
| insertBefore       | true     | The field before this extension field is inserted                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;T</pre>                                                       | <pre lang="javascript">undefined</pre>                                                                                                               |
| isDisabled         | true     | Whether the HTML element is disabled or not. Defaults to false The difference with readOnly is that disabled suggests that the field is not editable for some validation reason (e.g. a button which can't be clicked due to validation errors | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>            | <pre lang="javascript">false</pre>                                                                                                                   |
| isFullWidth        | true     | Whether the field spans all the parent width or not. Defaults to false                                                                                                                                                                         | <pre lang="javascript">boolean</pre>                                                                              | <pre lang="javascript">false</pre>                                                                                                                   |
| isHelperTextHidden | true     | Whether the field helper text is hidden or not. Defaults to false                                                                                                                                                                              | <pre lang="javascript">boolean</pre>                                                                              | <pre lang="javascript">false</pre>                                                                                                                   |
| isHidden           | true     | Whether the HTML element is hidden or not. Defaults to false                                                                                                                                                                                   | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>            | <pre lang="javascript">false</pre>                                                                                                                   |
| isHiddenDesktop    | true     | Whether the element is hidden or not in desktop devices. Defaults to false                                                                                                                                                                     | <pre lang="javascript">boolean</pre>                                                                              | <pre lang="javascript">false</pre>                                                                                                                   |
| isHiddenMobile     | true     | Whether the element is hidden or not in mobile devices. Defaults to false                                                                                                                                                                      | <pre lang="javascript">boolean</pre>                                                                              | <pre lang="javascript">false</pre>                                                                                                                   |
| isTitleHidden      | true     | Whether the element title is hidden or not. Defaults to false                                                                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                              | <pre lang="javascript">false</pre>                                                                                                                   |
| isTransient        | true     | Whether the value is bound to a GraphQL node (transient = false) or not (transient = true). Defaults to false                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                              | <pre lang="javascript">false</pre>                                                                                                                   |
| isTransientInput   | true     | Whether the value is bound only to GraphQL mutations (isTransientInput = true) or not (isTransientInput = false). Defaults to false                                                                                                            | <pre lang="javascript">boolean</pre>                                                                              | <pre lang="javascript">false</pre>                                                                                                                   |
| map                | true     | Function that can be used to set/transform the component text                                                                                                                                                                                  | <pre lang="javascript">(this:&nbsp;CT,&nbsp;value?:&nbsp;any,&nbsp;rowValue?:&nbsp;any)&nbsp;=>&nbsp;string</pre> | <pre lang="javascript">function(value,&nbsp;rowValue<br>)&nbsp;{<br><br>}</pre>                                                                      |
| onClick            | true     | Function to be executed when the field is clicked                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                                    | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                                                                                          |
| onError            | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">ErrorHandlerFunction<CT></pre>                                                             | <pre lang="javascript">function(error,screenId,elementId<br>)&nbsp;{<br>console.error({&nbsp;error,&nbsp;screenId,&nbsp;elementId&nbsp;})<br>}</pre> |
| parent             | true     | The container in which this component will render                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;P</pre>                                                       | <pre lang="javascript">undefined</pre>                                                                                                               |
| shortcut           | true     | Key combination that triggers this action                                                                                                                                                                                                      | <pre lang="javascript">Key&nbsp;|&nbsp;Key[]</pre>                                                                | <pre lang="javascript">[]</pre>                                                                                                                      |
| size               | true     | The field's vertical size, default is medium                                                                                                                                                                                                   | <pre lang="javascript">FieldWidth</pre>                                                                           | <pre lang="javascript">'medium'</pre>                                                                                                                |
| title              | true     | The title of the HTML element                                                                                                                                                                                                                  | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;any,&nbsp;Dict<any>></pre>             | <pre lang="javascript">''</pre>                                                                                                                      |
| width              | true     | The width of the block relative to the section where it is place Must be a number between 1 and 12, both included                                                                                                                              | <pre lang="javascript">FieldWidth</pre>                                                                           | <pre lang="javascript">'medium'</pre>                                                                                                                |