/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ClickableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { ButtonControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { ButtonDecoratorProperties } from './button-types';

class ButtonDecorator extends AbstractFieldDecorator<FieldKey.Button> {
    protected _controlObjectConstructor = ButtonControlObject;

    protected _layout = AbstractFieldLayoutBuilder;
}

/**
 * Initializes the decorated member as a [Button]{@link ButtonControlObject} field with the provided properties
 *
 * @param properties The properties that the [Button]{@link ButtonControlObject} field will be initialized with
 */
export function buttonField<T extends ScreenExtension<T>>(
    properties: ButtonDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Button>(properties, ButtonDecorator, FieldKey.Button);
}

export function buttonFieldOverride<T extends ScreenExtension<T>>(
    properties: ClickableOverrideDecoratorProperties<ButtonDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Button>(properties);
}
