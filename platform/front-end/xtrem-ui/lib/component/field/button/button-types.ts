import type { ValueOrCallbackWithFieldValue } from '../../../utils/types';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type { Clickable, ExtensionField, HasParent, Mappable, Sizable, HasShortcuts } from '../traits';
import type { BlockControlObject } from '../../control-objects';
import type { ScreenBase } from '../../../service/screen-base';
import type { BaseReadonlyComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';

export interface ButtonProperties<CT extends ScreenBase = ScreenBase>
    extends ReadonlyFieldProperties<CT>,
        Sizable,
        HasShortcuts {
    /** The border color of the button */
    borderColor?: ValueOrCallbackWithFieldValue<CT, string>;
}

export interface ButtonDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<ButtonProperties<CT>, '_controlObjectType'>,
        Mappable<CT>,
        Clickable<CT>,
        Sizable,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>> {}

export type ButtonComponentProps = BaseReadonlyComponentProperties<
    ButtonProperties,
    string,
    NestedFieldsAdditionalProperties
>;
