import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { ButtonComponentProps } from './button-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const ConnectedButtonComponent = React.lazy(() => import('./button-component'));

export function AsyncConnectedButtonComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedButtonComponent {...props} />
        </React.Suspense>
    );
}

const ButtonComponent = React.lazy(() => import('./button-component').then(c => ({ default: c.ButtonComponent })));

export function AsyncButtonComponent(props: ButtonComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <ButtonComponent {...props} />
        </React.Suspense>
    );
}
