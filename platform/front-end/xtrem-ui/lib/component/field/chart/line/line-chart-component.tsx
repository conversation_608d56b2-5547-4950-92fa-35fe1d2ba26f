import React from 'react';
import { <PERSON><PERSON><PERSON>, Line, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import type { VerticalAlignmentType } from 'recharts/types/component/DefaultLegendContent';
import { formatChartLabel, type ChartOptions } from '../chart-utils';

export function LineChartComponent({
    series,
    screenId,
    xAxis,
    data,
    legend,
    height,
    labelNestedField,
    locale,
}: ChartOptions): React.JSX.Element {
    if (!series) {
        throw new Error('No line chart options provided.');
    }

    const getTickFormatter = React.useCallback(
        (value: any, index: number) => {
            const rowValue = data[index];
            return formatChartLabel({ value, nestedField: labelNestedField, locale, rowValue, screenId });
        },
        [data, labelNestedField, locale, screenId],
    );

    const getTooltipLabelFormatter = React.useCallback(
        (value: any) => {
            return formatChartLabel({ value, nestedField: labelNestedField, locale, rowValue: {}, screenId });
        },
        [labelNestedField, locale, screenId],
    );

    const getTooltipValueFormatter = React.useCallback(
        (value: number, _name: string, entry: any, seriesIndex: number) => {
            const rowValue = entry.payload;
            const nestedField = series[seriesIndex].seriesNestedField;
            return formatChartLabel({ value, nestedField, locale, rowValue, screenId });
        },
        [locale, screenId, series],
    );

    return (
        <ResponsiveContainer width="100%" height={height}>
            <LineChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey={xAxis.key} tickFormatter={getTickFormatter} />
                <YAxis />
                <Tooltip formatter={getTooltipValueFormatter} labelFormatter={getTooltipLabelFormatter} />
                {legend.enabled && <Legend verticalAlign={legend.position as VerticalAlignmentType} />}
                {series.map(serie => (
                    <Line
                        dataKey={serie.yKey}
                        name={serie.yName}
                        stroke={serie.stroke}
                        key={serie.yKey}
                        type="monotone"
                        label
                    />
                ))}
            </LineChart>
        </ResponsiveContainer>
    );
}
