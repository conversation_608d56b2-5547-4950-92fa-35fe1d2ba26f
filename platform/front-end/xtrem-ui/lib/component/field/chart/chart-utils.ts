import * as tokens from '@sage/design-tokens/js/base/common';
import { formatDateToCurrentLocale } from '@sage/xtrem-date-time';
import type { Dict, LocalizeLocale } from '@sage/xtrem-shared';
import { FieldKey, objectKeys } from '@sage/xtrem-shared';
import { formatNumericValue } from '../../../utils/formatters';
import { ChartTypes } from '../../chart-types';
import type { NestedField, NestedFieldTypes } from '../../nested-fields';
import type { GraphqlCollection } from '../../types';
import type { NestedNumericProperties } from '../numeric/numeric-types';
import type { ChartProperties } from './chart-types';

export const chartColors = [
    tokens.colorsActionMajor500,
    tokens.colorsSemanticCaution500,
    tokens.colorsSemanticPositive500,
    tokens.colorsSemanticNegative500,
    tokens.colorsSemanticInfo500,
    tokens.colorsUtilityMajor200,
    tokens.colorsUtilityMajor100,
];

export interface ChartOptions {
    screenId: string;
    locale: LocalizeLocale;
    type: ChartTypes;
    isLegendHidden: boolean;
    data: any[];
    height: number;
    series?: {
        stroke: string;
        xKey: string;
        yKey: string;
        yName: string;
        seriesNestedField: NestedField<any, FieldKey.Numeric>;
    }[];
    pie?: {
        labelKey: string;
        angleKey: string;
        stroke: string;
        fills: string[];
        valueNestedField: NestedField<any, FieldKey.Numeric>;
    };
    labelNestedField: NestedField<any, NestedFieldTypes>;
    legend: {
        enabled: boolean;
        position: string;
    };
    xAxis: {
        key: string;
    };
}

export function getChartOptions({
    screenId,
    properties,
    locale,
    collection,
}: {
    screenId: string;
    properties: ChartProperties;
    locale: LocalizeLocale;
    collection?: GraphqlCollection;
}): ChartOptions {
    const isPieChart = properties.chart.type === ChartTypes.Pie;
    let series;
    let pie;
    if (isPieChart) {
        pie = {
            labelKey: properties.chart.xAxis.properties.bind,
            angleKey: properties.chart.series[0].properties.bind,
            stroke: chartColors[0],
            fills: chartColors,
            valueNestedField: properties.chart.series[0],
        };
    } else {
        // Other chart types (line, bar)
        series = properties.chart.series.map((currentSeries, index: number) => {
            const currentSeriesProperties = currentSeries.properties as NestedNumericProperties;
            const color = chartColors.length < index + 1 ? chartColors[0] : chartColors[index];
            return {
                stroke: color,
                xKey: properties.chart.xAxis.properties.bind,
                yKey: currentSeriesProperties.bind,
                yName: currentSeriesProperties.title as string,
                seriesNestedField: currentSeries,
            };
        });
    }

    const yAxesProperties = isPieChart ? [pie?.angleKey] : series?.map(s => s.yKey);
    // Remapping Y Axes values to ensure that they are numbers
    const data =
        collection?.data?.map(d =>
            objectKeys(d).reduce((prevValue: Dict<number>, currentValue: string) => {
                if (yAxesProperties!.indexOf(currentValue) !== -1) {
                    prevValue[currentValue] = Number(d[currentValue]);
                } else {
                    prevValue[currentValue] = d[currentValue];
                }
                return prevValue;
            }, {} as Dict<number>),
        ) || [];

    const chartOptions: ChartOptions = {
        type: properties.chart.type,
        locale,
        screenId,
        data,
        pie,
        height: 300,
        series,
        labelNestedField: properties.chart.xAxis,
        isLegendHidden: !!properties.isLegendHidden,
        legend: {
            enabled: !properties.isLegendHidden,
            position: 'bottom',
        },
        xAxis: {
            key: properties.chart.xAxis.properties.bind,
        },
    };

    return chartOptions;
}

export function formatChartLabel({
    screenId,
    nestedField,
    locale,
    value,
    rowValue,
}: {
    screenId: string;
    nestedField: NestedField<any, NestedFieldTypes>;
    locale: LocalizeLocale;
    value: any;
    rowValue: any;
}): string {
    switch (nestedField.type) {
        case FieldKey.Date:
            return formatDateToCurrentLocale(value, locale);
        case FieldKey.Numeric:
            return formatNumericValue({
                screenId,
                value,
                locale,
                rowValue,
                scale: (nestedField.properties as NestedNumericProperties).scale,
            });
        default:
            return value;
    }
}
