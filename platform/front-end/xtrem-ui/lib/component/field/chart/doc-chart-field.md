PATH: XTREEM/UI+Field+Widgets/Chart+Field

## Introduction

The chart field represents data visualized in form of charts within the user interface. The field is also available as an extension field.

## Example

```ts
@ui.decorators.chartField<Page>({
    bind: 'products',
    chart: ui.charts.line({
        series: [
            ui.nestedFields.numeric({
                bind: 'quantity',
                canFilter: true,
                title: 'Quantity',
            }),
            ui.nestedFields.numeric({
                bind: 'msrp',
                canFilter: true,
                prefix: '€',
                title: 'MSRP',
            }),
        ],
        xAxis: ui.nestedFields.date({ bind: 'releaseDate', title: 'Release Date' },
    }),
    filter: { quantity: { _gt: 0 } },
    helperText: 'Helper text displayed below the field.',
    isDisabled: false,
    isFullWidth: true,
    isHelperTextHidden: false,
    isHidden: false,
    isTitleHidden: false,
    isTransient: false,
    node: '@sage/xtrem-docs/Product',
    title: 'Chart Field',
    fieldActions() {
        return [this.action];
    },
    onClick() {
        console.log(`Do something when the field is clicked.`);
    },
    parent() {
        return this.block;
    },
})
field: ui.fields.Chart;
```

#### Use of field in extended page:

```ts
@ui.decorators.chartField<PageExtension>({
    ...,
    title: 'Chart Field Extension',
    insertBefore() {
        return this.field;
    },
    parent() {
        return this.block;
    },
})
extension: ui.fields.Chart;
```

## Decorator Properties

#### Display Properties

-   **helperText**: The helper text displayed below the field. This property is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.
-   **isFullWidth**: Determines whether the field takes up the full width of the grid or not.
-   **isHelperTextHidden**: Determines whether the field's helper text is displayed or not.
-   **isHidden**: Determines whether the field is displayed or not.
-   **isTitleHidden**: Determines whether the field's title is displayed or not.
-   **isLegendHidden**: Determines whether the legend should be hidden.
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.

#### Binding Properties

-   **bind**: Determines the associated GraphQL node's property the field's value will be bound to.
-   **chart**: Describes the fields chart type, axis and data.
-   **filter**: GraphQL fliter that will restrict the records displayed in the field.
-   **isTransient**: Determines whether the field will be excluded from the automatic data binding process or not.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **node**: Name of the node that the field represents. This field must be defined, to use advanced filtering capabilities.

#### Event Handler Properties

-   **onClick()**: Handler triggered when the field is clicked.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

#### Runtime Functions

-   **refresh()**: Refetches the field's values from the server and updates the user interface.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **isDirty()**: Sets or gets the dirty state of the field.

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Chart).
