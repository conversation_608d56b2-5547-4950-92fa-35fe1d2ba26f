import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import type { VerticalAlignmentType } from 'recharts/types/component/DefaultLegendContent';
import type { ChartOptions } from '../chart-utils';

export function BarChartComponent(props: ChartOptions): React.JSX.Element {
    if (!props.series) {
        throw new Error('No bar chart options provided.');
    }

    const Bars = props.series.map(serie => (
        <Bar dataKey={serie.yKey} name={serie.yName} fill={serie.stroke} key={serie.yKey} />
    ));

    return (
        <ResponsiveContainer width="100%" height={props.height}>
            <BarChart data={props.data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey={props.xAxis.key} />
                <YAxis />
                <Tooltip />
                {props.legend.enabled && <Legend verticalAlign={props.legend.position as VerticalAlignmentType} />}
                {Bars}
            </BarChart>
        </ResponsiveContainer>
    );
}
