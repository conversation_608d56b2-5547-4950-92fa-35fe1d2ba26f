import type { Dict } from '@sage/xtrem-shared';
import type { Filter } from '../../../service/filter-service';
import type { BlockControlObject, InternalChartProperties } from '../../control-objects';
import type { FieldControlObjectInstance, GraphqlCollection } from '../../types';
import type { BaseReadonlyComponentProperties } from '../field-base-component-types';
import type { <PERSON>lickable, <PERSON><PERSON>ield, Has<PERSON>ieldActions, HasParent, HasFilter } from '../traits';
import type { ScreenBaseGenericType } from '../../../types';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import type { ScreenBase } from '../../../service/screen-base';
import type { ClientNode } from '@sage/xtrem-client';
import type { ChartDeclaration } from '../../chart-types';
import type { FormattedNodeDetails } from '../../../service/metadata-types';

export interface ChartProperties<CT extends ScreenBase = ScreenBase, NodeType extends ClientNode = any>
    extends ReadonlyFieldProperties<CT>,
        HasFilter<CT, NodeType> {
    /** Whether the series of the chart can be filtered or not. Defaults to true */
    canFilter?: boolean;
    /** Chart configuration properties */
    chart: ChartDeclaration<CT>;
    /** The GraphQL node that the table represents, needed for filtering */
    node?: keyof ScreenBaseGenericType<CT>;
    /** Whether the legend should be hidden */
    isLegendHidden?: boolean;
}
export interface ChartDecoratorProperties<CT extends ScreenBase = ScreenBase, NodeType extends ClientNode = any>
    extends HasFieldActions<CT>,
        ChartProperties<CT, NodeType>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>> {}

export interface ChartComponentAdditionalProps {
    setFieldProperties: (elementId: string, value: ChartDecoratorProperties & InternalChartProperties) => void;
    nodeTypes: Dict<FormattedNodeDetails>;
}
export type ChartComponentProps = BaseReadonlyComponentProperties<
    ChartDecoratorProperties,
    GraphqlCollection,
    ChartComponentAdditionalProps
>;

export interface ChartComponentState {
    filters: Filter[];
}
