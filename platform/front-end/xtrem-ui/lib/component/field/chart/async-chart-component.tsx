import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { ChartComponentProps } from './chart-types';

const ConnectedChartComponent = React.lazy(() => import('./chart-component'));

export function AsyncConnectedChartComponent(props: FieldComponentExternalProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <ConnectedChartComponent {...props} />
        </React.Suspense>
    );
}

const ChartComponent = React.lazy(() => import('./chart-component').then(c => ({ default: c.ChartComponent })));

export function AsyncChartComponent(props: ChartComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader size="small" />}>
            <ChartComponent {...props} />
        </React.Suspense>
    );
}
