import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ClickableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { ChartControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { ChartDecoratorProperties } from './chart-types';

class ChartDecorator extends AbstractFieldDecorator<FieldKey.Chart> {
    protected _controlObjectConstructor = ChartControlObject;

    protected _layout = AbstractFieldLayoutBuilder;
}

/**
 * Initializes the decorated member as a [Chart]{@link ChartControlObject} field with the provided properties
 *
 * @param properties The properties that the [Chart]{@link ChartControlObject} field will be initialized with
 */
export function chartField<T extends ScreenExtension<T>, NodeType extends ClientNode = any>(
    properties: ChartDecoratorProperties<Extend<T>, NodeType>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Chart>(properties, ChartDecorator, FieldKey.Chart);
}

export function chartFieldOverride<T extends ScreenExtension<T>, NodeType extends ClientNode = any>(
    properties: ClickableOverrideDecoratorProperties<ChartDecoratorProperties<Extend<T>, NodeType>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Chart>(properties);
}
