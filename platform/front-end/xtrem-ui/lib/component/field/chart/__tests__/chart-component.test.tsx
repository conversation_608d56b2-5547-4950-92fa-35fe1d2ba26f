import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { <PERSON>Key } from '../../../types';
import { nestedFields } from '../../../..';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import type { XtremAppState } from '../../../../redux';
import type { ScreenBase } from '../../../../service/screen-base';
import { line } from '../../../chart-types';
import type { ChartProperties } from '../../../control-objects';
import { ConnectedChartComponent } from '../chart-component';
import { render } from '@testing-library/react';

describe('Chart component', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: ChartProperties<ScreenBase>;

    beforeEach(() => {
        mockFieldProperties = {
            title: 'Test Chart Title',
            chart: line<any, any>({
                series: [
                    nestedFields.numeric({ bind: 'productA', prefix: '€', title: 'Product A' }),
                    nestedFields.numeric({ bind: 'productB', prefix: '€', title: 'Product B' }),
                ],
                xAxis: nestedFields.text<any, any>({ bind: 'date', title: 'Quantity' }),
            }),
        };
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });
    describe('connected', () => {
        let mockStore: MockStoreEnhanced<XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Chart, state, screenId, 'test-chart-field', mockFieldProperties, {
                data: [
                    {
                        _id: 'F0D04985-8F9B-BAAA-EED7-85F80D839FB6',
                        date: '2019-07-13',
                        productA: 4,
                        productB: 3,
                    },
                    {
                        _id: '2D1429EA-CF35-87D6-8478-BFD494AF80E7',
                        date: '2019-10-17',
                        productA: 4.2,
                        productB: 3.5,
                    },
                    {
                        _id: '1E9C324E-063E-1063-E784-7D03D26D4FDE',
                        date: '2019-02-14',
                        productA: 4.6,
                        productB: 3.3,
                    },
                ],
            });
            addFieldToState(FieldKey.Chart, state, screenId, 'test-empty-chart-field', mockFieldProperties, null);
            mockStore = getMockStore(state);
        });
        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedChartComponent screenId={screenId} elementId="test-chart-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedChartComponent screenId={screenId} elementId="test-chart-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with no value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedChartComponent screenId={screenId} elementId="test-empty-chart-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render the title ', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedChartComponent screenId={screenId} elementId="test-empty-chart-field" />
                    </Provider>,
                );
                const title = container.querySelector('[data-element="label"]');
                expect(title).not.toBeNull();
                expect(title!).toHaveTextContent('Test Chart Title');
            });

            it('should render with hidden title ', () => {
                mockFieldProperties.isTitleHidden = true;

                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedChartComponent screenId={screenId} elementId="test-empty-chart-field" />
                    </Provider>,
                );
                const title = container.querySelector('[data-element="label"]');
                expect(title).toBeNull();
            });
        });
    });
});
