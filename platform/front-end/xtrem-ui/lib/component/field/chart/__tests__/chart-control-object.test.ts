import type { Graph<PERSON>Filter } from '../../../../service/graphql-utils';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import { ChartControlObject } from '../../../control-objects';
import type { ChartDecoratorProperties } from '../../../decorators';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';
import { ChartTypes } from '../../../chart-types';

describe('Chart Field', () => {
    const screenId = 'TestPage';
    let chartField: ChartControlObject;
    const properties: ChartDecoratorProperties = {
        title: 'TEST_FIELD_TITLE',
        isHidden: true,
        isDisabled: true,
        chart: {
            type: ChartTypes.Bar,
            series: [
                {
                    type: FieldKey.Numeric,
                    properties: {
                        bind: 'test',
                        title: 'Char test',
                        canFilter: true,
                    },
                    defaultUiProperties: {
                        bind: 'test',
                    },
                },
            ],
            xAxis: {
                type: FieldKey.Text,
                properties: {
                    bind: 'xAxis',
                    title: 'Chart xAxis',
                },
                defaultUiProperties: { bind: 'xAxis' },
            },
        },
    };

    describe('value', () => {
        beforeEach(() => {
            chartField = createFieldControlObject<FieldKey.Chart>(
                FieldKey.Chart,
                screenId,
                ChartControlObject,
                'test',
                null,
                properties,
            );
        });

        it('getting field value', () => {
            const value = [{ chartTest: 'data' }];
            expect(chartField.value).not.toEqual(value);
            chartField.value = value;
            expect(chartField.value).toEqual(value);
        });
    });

    describe('Chart control object GraphQl filters', () => {
        let setUiComponentProperties: jest.Mock<void, [string, string, ChartDecoratorProperties]>;
        let refresh: jest.Mock<Promise<FieldInternalValue<FieldKey.Chart>>>;
        let newProperties: ChartDecoratorProperties;
        let chartField: ChartControlObject;
        const executeTest = (filter: GraphQLFilter | (() => GraphQLFilter)) => {
            chartField.filter = filter;
            expect(setUiComponentProperties).toHaveBeenCalled();
            expect(refresh).toHaveBeenCalled();
            expect(newProperties.filter).toEqual(filter);
        };

        beforeEach(() => {
            setUiComponentProperties = jest.fn(
                (_screenId: string, _elementId: string, _value: ChartDecoratorProperties) => {
                    newProperties = { ..._value };
                },
            );

            refresh = jest.fn();
            chartField = createFieldControlObject<FieldKey.Chart>(
                FieldKey.Chart,
                screenId,
                ChartControlObject,
                'test',
                null,
                properties,
                { setUiComponentProperties, refresh },
            );
        });

        it('should update filter with GraphQL filter object', () => {
            executeTest({ description: { _regex: 'policy', _options: 'i' } });
        });

        it('should update filter with function', () => {
            executeTest(() => ({ description: { _regex: 'policy', _options: 'i' } }));
        });
    });
});
