import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import { chartField } from '../chart-decorator';

describe('Button decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'chartField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(chartField, pageMetadata, fieldId);
        });
    });
});
