// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Chart component connected Snapshots should render hidden 1`] = `
<div>
  <div
    class="e-field e-chart-field e-hidden"
    data-label="Test Chart Title"
    data-testid="e-chart-field e-field-label-testChartTitle e-field-bind-test-chart-field"
  >
    <div
      class="e-field-header"
    >
      <div
        class="e-field-title"
      >
        <label
          class="common-input__label"
          data-element="label"
          data-testid="e-field-label"
        >
          Test Chart Title
        </label>
      </div>
      <div
        class="e-field-actions-wrapper"
      />
    </div>
    <div
      class="recharts-responsive-container"
      style="width: 100%; height: 300px; min-width: 0;"
    />
  </div>
</div>
`;

exports[`Chart component connected Snapshots should render with default properties 1`] = `
<div>
  <div
    class="e-field e-chart-field"
    data-label="Test Chart Title"
    data-testid="e-chart-field e-field-label-testChartTitle e-field-bind-test-chart-field"
  >
    <div
      class="e-field-header"
    >
      <div
        class="e-field-title"
      >
        <label
          class="common-input__label"
          data-element="label"
          data-testid="e-field-label"
        >
          Test Chart Title
        </label>
      </div>
      <div
        class="e-field-actions-wrapper"
      />
    </div>
    <div
      class="recharts-responsive-container"
      style="width: 100%; height: 300px; min-width: 0;"
    />
  </div>
</div>
`;

exports[`Chart component connected Snapshots should render with no value 1`] = `
<div>
  <div
    class="e-field e-chart-field"
    data-label="Test Chart Title"
    data-testid="e-chart-field e-field-label-testChartTitle e-field-bind-test-empty-chart-field"
  >
    <div
      class="e-field-header"
    >
      <div
        class="e-field-title"
      >
        <label
          class="common-input__label"
          data-element="label"
          data-testid="e-field-label"
        >
          Test Chart Title
        </label>
      </div>
      <div
        class="e-field-actions-wrapper"
      />
    </div>
    <p>
      No data found
    </p>
  </div>
</div>
`;
