import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import type { Filter } from '../../../service/filter-service';
import { getGraphQLFilter } from '../../../service/filter-service';
import { localize } from '../../../service/i18n-service';
import { showToast } from '../../../service/toast-service';
import { refreshField } from '../../../service/field-state-service';
import { getPropertyScalarType } from '../../../utils/abstract-fields-utils';
import { schemaTypeNameFromNodeName } from '../../../utils/transformers';
import { type InternalChartProperties } from '../../control-objects';
import type { GraphqlCollection } from '../../types';
import { FieldKey } from '../../types';
import type { FilterManagerField } from '../../ui/filter/filter-manager';
import { FiltersComponent, FiltersLabels } from '../../ui/filter/filters-component';
import { HelperText } from '../carbon-utility-components';
import { mapReadonlyStateToProps, ReadonlyFieldBaseComponent } from '../field-base-component';
import type { FieldComponentExternalProperties, ReadonlyFieldComponentProperties } from '../field-base-component-types';
import { FieldHeader } from '../field-header';
import type { ChartComponentProps, ChartComponentState, ChartDecoratorProperties } from './chart-types';
import { findDeepPropertyType } from '../../../utils/node-utils';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import type { OptionalNodePropertyType } from '../../../types';
import { ChartTypes } from '../../chart-types';
import { LineChartComponent } from './line/line-chart-component';
import { BarChartComponent } from './bar/bar-chart-component';
import { PieChartComponent } from './pie/pie-chart-component';
import { getChartOptions } from './chart-utils';

export class ChartComponent extends ReadonlyFieldBaseComponent<
    ChartDecoratorProperties,
    GraphqlCollection,
    ChartComponentProps,
    ChartComponentState
> {
    constructor(props: ChartComponentProps) {
        super(props);
        this.state = {
            filters: [],
        };
    }

    onFilterChange = (): Promise<void> => {
        const columns = this.getNestedFields();
        const filter = getGraphQLFilter(this.state.filters, columns);

        if (this.props.setFieldProperties) {
            this.props.setFieldProperties(this.props.elementId, {
                ...this.props.fieldProperties,
                activeUserFilter: filter,
            });
        }

        return refreshField({
            screenId: this.props.screenId,
            elementId: this.props.elementId,
            keepPageInfo: true,
        }).catch((e: any) => {
            showToast(e.message || e, { type: 'warning' });
        });
    };

    renderChart(): React.ReactNode {
        const options = getChartOptions({
            properties: this.props.fieldProperties,
            collection: this.props.value,
            screenId: this.props.screenId,
            locale: this.props.locale,
        });

        switch (this.props.fieldProperties.chart.type) {
            case ChartTypes.Line:
                return <LineChartComponent {...options} />;
            case ChartTypes.Bar:
                return <BarChartComponent {...options} />;
            case ChartTypes.Pie:
                return <PieChartComponent {...options} />;
            default:
                throw new Error(`Unsupported chart type: ${this.props.fieldProperties.chart.type}`);
        }
    }

    saveFilters = (filters: Filter[]): void => {
        this.setState({ filters }, () => this.onFilterChange());
    };

    getFilterableFields = (
        node: OptionalNodePropertyType,
        nestedFields: ChartDecoratorProperties['chart']['series'],
    ): FilterManagerField[] => {
        return nestedFields.map(nestedField => {
            const bind = convertDeepBindToPathNotNull(nestedField.properties.bind);

            const fieldType =
                findDeepPropertyType(
                    schemaTypeNameFromNodeName(node),
                    nestedField.properties.bind,
                    this.props.nodeTypes,
                    true,
                )?.type || FieldKey.Text;
            return {
                type: nestedField.type,
                properties: {
                    ...nestedField.properties,
                    bind,
                    title: this.getTitle(),
                    type: getPropertyScalarType(this.props.nodeTypes, fieldType, nestedField.type),
                },
            };
        });
    };

    getNestedFields = (): FilterManagerField[] => {
        const nestedFields = this.props.fieldProperties.chart.series;
        const node = this.props.fieldProperties.node;
        return this.getFilterableFields(node, nestedFields);
    };

    render(): React.ReactNode {
        return (
            <div
                {...this.getBaseAttributesDivWrapper(
                    'chart',
                    'e-chart-field',
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    this.props.isNested,
                )}
                onClick={this.getClickHandler()}
            >
                <FieldHeader
                    elementId={this.props.elementId}
                    screenId={this.props.screenId}
                    title={this.getTitle()}
                    isDisabled={this.isDisabled()}
                    isTitleHidden={this.props.fieldProperties.isTitleHidden}
                >
                    {this.props.fieldProperties.canFilter && this.props.fieldProperties.node && (
                        <FiltersComponent
                            screenId={this.props.screenId}
                            fields={this.getNestedFields()}
                            handleSave={this.saveFilters}
                            filters={this.state.filters}
                            isDisabled={this.isDisabled()}
                        />
                    )}
                </FieldHeader>
                {this.props.fieldProperties.canFilter && (
                    <FiltersLabels
                        screenId={this.props.screenId}
                        fields={this.getNestedFields()}
                        handleSave={this.saveFilters}
                        filters={this.state.filters}
                        isDisabled={this.isDisabled()}
                    />
                )}
                {this.props.value ? (
                    this.renderChart()
                ) : (
                    <p>{localize('@sage/xtrem-ui/chart-component-no-data', 'No data found')}</p>
                )}
                {this.props.fieldProperties.helperText && (
                    <HelperText helperText={this.props.fieldProperties.helperText} />
                )}
            </div>
        );
    }
}

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: ChartComponentProps,
): Partial<ChartComponentProps> => {
    return {
        setFieldProperties: (elementId: string, value: ChartDecoratorProperties & InternalChartProperties): void => {
            dispatch(xtremRedux.actions.setFieldProperties(props.screenId, elementId, value));
        },
    };
};

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: FieldComponentExternalProperties,
): ChartComponentProps => {
    const componentProperties = mapReadonlyStateToProps()(state, props) as ReadonlyFieldComponentProperties<
        ChartDecoratorProperties,
        GraphqlCollection
    >;

    return {
        ...props,
        ...componentProperties,
        nodeTypes: state.nodeTypes,
        setFieldProperties: xtremRedux.actions.actionStub,
    };
};

export const ConnectedChartComponent = connect(mapStateToProps, mapDispatchToProps)(ChartComponent);

export default ConnectedChartComponent;
