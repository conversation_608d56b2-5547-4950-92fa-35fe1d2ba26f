/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import { showToast } from '../../../service/toast-service';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { CollectionItem, FieldComponentProps, FieldKey } from '../../types';
import type { ChartProperties } from './chart-types';

export interface InternalChartProperties<CT extends ScreenBase = ScreenBase> extends ChartProperties<CT> {
    /** The GraphQL filter set by the user and applied to the records displayed on the chart */
    activeUserFilter?: GraphQLFilter;
}

/**
 * [Field]{@link ReadonlyFieldControlObject} that holds a set of read-only values and represents them as a chart
 */
export class ChartControlObject<
    CT extends ScreenExtension<CT> = ScreenBase,
    NodeType extends ClientNode = any,
> extends ReadonlyFieldControlObject<CT, FieldKey.Chart, FieldComponentProps<FieldKey.Chart, CT, NodeType>> {
    static readonly defaultUiProperties: Partial<ChartProperties> = {
        ...ReadonlyFieldControlObject.defaultUiProperties,
        canFilter: true,
    };

    /** Graphql filter that will restrict the records displayed in the chart */
    get filter(): GraphQLFilter<NodeType> | undefined {
        return resolveByValue({
            fieldValue: undefined,
            propertyValue: this.getUiComponentProperty('filter'),
            rowValue: undefined,
            screenId: this.screenId,
            skipHexFormat: true,
        });
    }

    /** Graphql filter that will restrict the records displayed in the chart */
    set filter(filter: GraphQLFilter<NodeType> | undefined) {
        this.setUiComponentProperties('filter', filter);
        this.refresh().catch(() => {
            /* Intentional fire and forget */
        });
    }

    /** Chart values */
    get value(): CollectionItem[] {
        const value = this._getValue();
        return value && value.data ? value.data : [];
    }

    /** Chart values */
    set value(newValue: CollectionItem[]) {
        const previousValue = this._getValue() || { data: [], pageInfo: {} };
        this._setValue({ ...previousValue, data: newValue });
    }

    @ControlObjectProperty<ChartProperties<CT>, ChartControlObject<CT>>()
    /** Whether the legend should be hidden */
    isLegendHidden?: boolean;

    async refresh(): Promise<void> {
        await this._refresh({ keepPageInfo: true }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }
}
