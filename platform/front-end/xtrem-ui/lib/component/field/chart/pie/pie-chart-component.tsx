import React from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import type { VerticalAlignmentType } from 'recharts/types/component/DefaultLegendContent';
import { get, isNil } from 'lodash';
import type { ChartOptions } from '../chart-utils';
import { formatChartLabel } from '../chart-utils';

export function PieChartComponent({
    pie,
    locale,
    data,
    xAxis,
    height,
    screenId,
    legend,
    isLegendHidden,
    labelNestedField,
}: ChartOptions): React.JSX.Element {
    if (!pie) {
        throw new Error('No pie chart options provided.');
    }

    const getLabel = React.useCallback(
        (p: any) => {
            const rowValue = p.payload?.payload;
            const label = get(rowValue, p.name, null);
            return isNil(label)
                ? formatChartLabel({ value: p.value, nestedField: pie.valueNestedField, locale, rowValue, screenId })
                : formatChartLabel({ value: label, nestedField: labelNestedField, locale, rowValue, screenId });
        },
        [locale, labelNestedField, pie.valueNestedField, screenId],
    );

    const getLegendLabel = React.useCallback(
        (_: any, entry: any) => {
            const bind = entry.payload.name;
            const rowValue = entry.payload.payload;
            const value = get(rowValue, bind, null);
            return formatChartLabel({ value, nestedField: labelNestedField, locale, rowValue, screenId });
        },
        [labelNestedField, locale, screenId],
    );

    const fills = pie.fills;

    return (
        <ResponsiveContainer width="100%" height={height}>
            <PieChart>
                <Pie
                    data={data}
                    dataKey={pie.angleKey}
                    name={pie.labelKey}
                    nameKey={xAxis.key}
                    stroke={pie.stroke}
                    label={isLegendHidden ? getLabel : undefined}
                >
                    {data.map((entry, index) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <Cell key={`cell-${index}`} fill={fills[index % fills.length]} />
                    ))}
                </Pie>
                <Tooltip />
                {legend.enabled && (
                    <Legend verticalAlign={legend.position as VerticalAlignmentType} formatter={getLegendLabel} />
                )}
            </PieChart>
        </ResponsiveContainer>
    );
}
