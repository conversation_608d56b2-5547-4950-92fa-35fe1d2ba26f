/**
 * @packageDocumentation
 * @module root
 * */

import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import { addOptionTypeToProperties } from '../../../utils/data-type-utils';
import type { ClickableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { LabelControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import { LabelDecoratorProperties, NestedLabelProperties } from './label-types';

class LabelDecorator extends AbstractFieldDecorator<FieldKey.Label> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = LabelControlObject;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<LabelDecoratorProperties> {
        const properties: Partial<LabelDecoratorProperties> = {};
        addOptionTypeToProperties({ propertyDetails, dataType, properties });
        return properties;
    }
}

/**
 * Initializes the decorated member as a [Label]{@link LabelControlObject} field with the provided properties
 *
 * @param properties The properties that the [Label]{@link LabelControlObject} field will be initialized with
 */
export function labelField<T extends ScreenExtension<T>>(
    properties: LabelDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Label>(properties, LabelDecorator, FieldKey.Label);
}

export function labelFieldOverride<T extends ScreenExtension<T>>(
    properties: ClickableOverrideDecoratorProperties<LabelDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Label>(properties);
}

export { LabelDecoratorProperties, NestedLabelProperties };
