import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { LabelComponentProps } from './label-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const ConnectedLabelComponent = React.lazy(() => import('./label-component'));

export function AsyncConnectedLabelComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedLabelComponent {...props} />
        </React.Suspense>
    );
}

const LabelComponent = React.lazy(() => import('./label-component').then(c => ({ default: c.LabelComponent })));

export function AsyncLabelComponent(props: LabelComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <LabelComponent {...props} />
        </React.Suspense>
    );
}
