/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { LabelDecoratorProperties } from './label-types';

/**
 * [Field]{@link ReadonlyFieldControlObject} that holds a read-only text value
 */
export class LabelControlObject<
    ReferencedEnumType extends string = string,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends ReadonlyFieldControlObject<CT, FieldKey.Label, FieldComponentProps<FieldKey.Label>> {
    @FieldControlObjectResolvedProperty<LabelDecoratorProperties<CT>, LabelControlObject<ReferencedEnumType, CT>>()
    /** The border color of the label */
    borderColor?: string;

    @FieldControlObjectResolvedProperty<LabelDecoratorProperties<CT>, LabelControlObject<ReferencedEnumType, CT>>()
    /** The background color of the HTML field */
    backgroundColor?: string;

    @FieldControlObjectResolvedProperty<LabelDecoratorProperties<CT>, LabelControlObject<ReferencedEnumType, CT>>()
    /** The font color of the HTML field */
    color?: string;

    @ControlObjectProperty<LabelDecoratorProperties<CT>, LabelControlObject<ReferencedEnumType, CT>>()
    /** Text to be displayed inline after the field value */
    prefix?: string;

    @ControlObjectProperty<LabelDecoratorProperties<CT>, LabelControlObject<ReferencedEnumType, CT>>()
    /** Text to be displayed inline before the field value */
    postfix?: string;

    /** Field's value, only valid options can be set as value. */
    set value(newValue: ReferencedEnumType | null) {
        this._setValue(newValue as string);
    }

    /** Field's value, only valid options can be set as value. */
    get value(): ReferencedEnumType | null {
        return (this._getValue() as ReferencedEnumType) || null;
    }
}
