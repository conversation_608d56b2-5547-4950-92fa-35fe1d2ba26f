import { get, isNil } from 'lodash';
import * as React from 'react';
import { localize, localizeEnumMember } from '../../../service/i18n-service';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import type { NestedLabelProperties } from '../../nested-fields-properties';
import { LabelComponent } from '../../ui/label/label-component';
import { isFieldDisabled } from '../carbon-helpers';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import { AUTO_COLUMN_ID } from '../../../utils/table-component-utils';

export const LabelCellRenderer: React.FC<CellParams<NestedLabelProperties>> = React.memo(props => {
    if (props.data && !props.data.__isGroup && props.colDef.colId === AUTO_COLUMN_ID) {
        return null;
    }

    const { screenId, fieldProperties } = props;
    const rowValue = splitValueToMergedValue(props.data);
    if (rowValue && props.value != null) {
        const fieldValueFromRowData = get(rowValue, convertDeepBindToPathNotNull(fieldProperties.bind));
        const fieldValue = !isNil(fieldValueFromRowData) ? fieldValueFromRowData : props.value;
        const isDisabled = isFieldDisabled(screenId, fieldProperties, fieldValue, rowValue);
        const mappedValue = fieldProperties.map
            ? resolveByValue({
                  propertyValue: fieldProperties.map,
                  fieldValue,
                  rowValue,
                  skipHexFormat: true,
                  screenId,
              })
            : fieldValue;

        const value =
            fieldProperties.optionType && fieldValue
                ? localizeEnumMember(fieldProperties.optionType, fieldValue || null)
                : mappedValue;

        return (
            <fieldProperties.wrapper {...props}>
                <LabelComponent
                    screenId={screenId}
                    value={
                        props.colDef.colId === AUTO_COLUMN_ID && !isNil(props.data.__groupCount)
                            ? `${value || localize('@sage/xtrem-ui/no-value', 'No value')} (${props.data.__groupCount})`
                            : value
                    }
                    rawValue={fieldValue}
                    rowValue={rowValue}
                    style={fieldProperties.style}
                    backgroundColor={fieldProperties.backgroundColor}
                    borderColor={fieldProperties.borderColor}
                    color={fieldProperties.color}
                    isDisabled={isDisabled}
                    prefix={fieldProperties.prefix}
                    postfix={fieldProperties.postfix}
                />
            </fieldProperties.wrapper>
        );
    }

    return null;
});

LabelCellRenderer.displayName = 'LabelCellRenderer';
