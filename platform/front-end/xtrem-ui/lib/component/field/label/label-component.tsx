import * as React from 'react';
import { connect } from 'react-redux';
import { localizeEnumMember } from '../../../service/i18n-service';
import { ContextType } from '../../../types';
import { LabelComponent as UiLabelComponent } from '../../ui/label/label-component';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { mapReadonlyStateToProps, ReadonlyFieldBaseComponent } from '../field-base-component';
import type { NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { LabelDecoratorProperties } from './label-types';

export class LabelComponent extends ReadonlyFieldBaseComponent<
    LabelDecoratorProperties,
    string,
    NestedFieldsAdditionalProperties
> {
    render(): React.ReactNode {
        const value =
            this.props.value && this.props.fieldProperties.optionType
                ? localizeEnumMember(this.props.fieldProperties.optionType, this.props.value)
                : this.getValue();

        const { isTitleHidden, onClick, isHelperTextHidden } = this.props.fieldProperties;

        const optionalProps: any = {};
        if (onClick) {
            optionalProps.onClick = this.getClickHandler();
        }

        const isFieldLabelHidden =
            !!isTitleHidden || (this.props.isNested && !this.props.shouldRenderLabelInNestedReadOnlyMode);

        const isFieldHelperTextHidden = isHelperTextHidden || this.props.nestedReadOnlyField;
        return (
            <div
                {...this.getBaseAttributesDivWrapper(
                    'label',
                    'e-label-field',
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    this.props.isNested,
                )}
            >
                {!isFieldLabelHidden && <FieldLabel label={this.getTitle()} />}
                <UiLabelComponent
                    screenId={this.props.screenId}
                    value={value}
                    rawValue={this.props.value}
                    isDisabled={this.isDisabled()}
                    {...optionalProps}
                    backgroundColor={this.props.fieldProperties.backgroundColor}
                    borderColor={this.props.fieldProperties.borderColor}
                    color={this.props.fieldProperties.color}
                    rowValue={this.props.handlersArguments?.rowValue}
                    prefix={this.props.fieldProperties.prefix}
                    postfix={this.props.fieldProperties.postfix}
                    style={this.props.fieldProperties.style}
                    size={
                        this.props.contextType === ContextType.navigationPanel ||
                        this.props.contextType === ContextType.pageHeader ||
                        this.props.contextType === ContextType.table
                            ? 'S'
                            : 'M'
                    }
                />
                {!isFieldHelperTextHidden && <HelperText helperText={this.props.fieldProperties.helperText} />}
            </div>
        );
    }
}

export const ConnectedLabelComponent = connect(mapReadonlyStateToProps())(LabelComponent);

export default ConnectedLabelComponent;
