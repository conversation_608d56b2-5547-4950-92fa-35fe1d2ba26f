import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValueOrCallbackWithFieldValue } from '../../../utils/types';
import type { BlockControlObject, TileControlObject } from '../../control-objects';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseReadonlyComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    BackgroundColorable,
    CanBeMandatory,
    Clickable,
    Colorable,
    ExtensionField,
    HasOptionType,
    HasParent,
    Mappable,
    Nested,
    NestedClickable,
    Postfixable,
    Prefixable,
    Sizable,
} from '../traits';

export interface LabelProperties<CT extends ScreenBase = ScreenBase, ContextNodeType = void>
    extends ReadonlyFieldProperties<CT>,
        Postfixable<CT, ContextNodeType>,
        Prefixable<CT, ContextNodeType>,
        BackgroundColorable<CT>,
        HasOptionType,
        Colorable<CT> {}
export interface LabelDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<LabelProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        Mappable<CT>,
        Sizable,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT> | TileControlObject<CT>> {
    /** Display style of the pill element */
    style?: ValueOrCallbackWithFieldValue<CT, LabelFieldStyle>;

    /**
     * @deprecated Use `style` instead
     * The border color of the label */
    borderColor?: ValueOrCallbackWithFieldValue<CT, string>;
}

export interface NestedLabelProperties<CT extends ScreenBase = ScreenBase, NodeType extends ClientNode = any>
    extends Omit<LabelProperties<CT, NodeType>, 'bind'>,
        Mappable<CT>,
        Nested<NodeType>,
        NestedClickable<CT, NodeType>,
        Sizable,
        CanBeMandatory<CT, NodeType> {
    /**
     * @deprecated Use `style` instead
     * The border color of the label */
    borderColor?: ValueOrCallbackWithFieldValue<CT, string>;
    /** Display style of the pill element */
    style?: ValueOrCallbackWithFieldValue<CT, LabelFieldStyle, string, NodeType>;
}

export interface LabelFieldStyle {
    borderColor?: string;
    backgroundColor?: string;
    textColor?: string;
}

export type LabelComponentProps = BaseReadonlyComponentProperties<
    LabelProperties,
    string,
    NestedFieldsAdditionalProperties
>;
