PATH: XTREEM/UI+Field+Widgets/Label+Field

## Introduction

A label field is a read-only field type that represents a value using a colorful pill. The look and feel fo this component can be highly customized. It can be bound to any non object field type.

## Example:

```ts
@ui.decorators.labelField<LabelFieldPage>({
    parent() {
        return this.anyBlock;
    },
    title: 'A label field',
    bind: 'anyProperty'
    onClick() {
        console.log('Doing something when the field is clicked');
    },
    map(fieldValue: any) {
        return fieldValue.toUpperCase();
    },
    optionType:'@sage/my-xtrem-package/MyVerySpecialEnum',
    prefix: '$',
    postfix: 'Kg',
    color: ui.tokens.colorsActionMajor500,
    helperText: 'This text goes underneath the field',
})
aSimpleLabelField: ui.fields.Label<MyVerySpecialEnumType>;
```

### Display decorator properties:

-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **helperText**: The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.
-   **prefix**: A string that is displayed inside the field before the value, aligned to the left.
-   **postfix**: A string that is displayed inside the field after the value, aligned to the right.
-   **isFullWidth**: Whether a field should take the full width of the screen.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
-   **color**: Color code in HEX format that is used to set the font color of the element. It can be defined as a string, or conditionally by a callback that returns a string.
-   **backgroundColor**: Color code in HEX format that is used to set the background color of the element. It can be defined as a string, or conditionally by a callback that returns a string.
-   **borderColor**: Color code in HEX format that is used to set the border color of the element. It can be defined as a string, or conditionally by a callback that returns a string.

### Binding decorator properties:

-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **map**: Function callback that allow remapping the field value to something else.
-   **optionType**: Enum option type, such as `@sage/xtrem-my-package/MySpecialEnum`. If this value is defined, the value is displayed in a localized form and the map() decorator property is not called.

### Event handler decorator properties:

-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

#### Runtime Functions

-   **refresh()**: Refetches the field's values from the server and updates the user interface.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **isDirty()**: Sets or gets the dirty state of the field.
