import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import { labelField } from '../label-decorator';
import type { LabelDecoratorProperties } from '../label-types';

describe('Label decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;
    let onClickMock: jest.Mock;
    let mapFunc: (value?: any) => string;

    beforeEach(() => {
        fieldId = 'labelField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
        mapFunc = jest.fn().mockImplementation(() => 'test map_return');
        onClickMock = jest.fn();
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        it('should set default values when no component properties provided', () => {
            labelField({})({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties: LabelDecoratorProperties<Page> =
                pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.map).toBeUndefined();
            expect(mappedComponentProperties.onClick).toBeUndefined();
        });

        it('should set values when component properties provided', () => {
            labelField({
                map: mapFunc,
                onClick: onClickMock,
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties: LabelDecoratorProperties<Page> =
                pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.map).not.toBeUndefined();
            expect(mappedComponentProperties.map).toBe(mapFunc);
            expect(mappedComponentProperties.onClick).toBe(onClickMock);
        });

        it('should able onClick handler event', () => {
            testOnClickHandler(labelField, pageMetadata, fieldId);
        });
    });
});
