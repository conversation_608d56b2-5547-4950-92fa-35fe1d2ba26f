import * as React from 'react';
import { render } from '@testing-library/react';
import { LabelCellRenderer } from '../label-cell-renderer';
import type { NestedLabelProperties } from '../label-types';
import * as i18nService from '../../../../service/i18n-service';
import * as stateUtils from '../../../../utils/state-utils';
import { getMockPageDefinition } from '../../../../__tests__/test-helpers';
import type { CellParams } from '../../../../utils/ag-grid/ag-grid-column-config';
import { CellWrapper } from '../../../ui/table-shared/cell/cell-wrapper';
import * as nestedFields from '../../../nested-fields';
import { CollectionValue } from '../../../../service/collection-data-service';
import { CollectionFieldTypes } from '../../../../service/collection-data-types';

jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() => getMockPageDefinition('test-screen'));

describe('select cell renderer', () => {
    let props: CellParams<NestedLabelProperties>;
    let localizeSpy: jest.SpyInstance<string, [string, string]> | null = null;

    beforeEach(() => {
        localizeSpy = jest
            .spyOn(i18nService, 'localizeEnumMember')
            .mockImplementation((_: any, enumName: string) => `${enumName} localized`);

        props = {
            locale: 'en-US',
            data: {},
            initialValue: '',
            value: '',
            onValueChange: jest.fn(),
            setTooltip: jest.fn(),
            valueFormatted: null,
            isParentFieldDisabled: false,
            collectionValue: () =>
                new CollectionValue({
                    screenId: 'test-screen',
                    elementId: 'testField',
                    isTransient: false,
                    hasNextPage: false,
                    orderBy: [{}],
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                        ],
                    ],
                    nodeTypes: {},
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                }),
            column: {
                colDef: {
                    field: 'test-column-id',
                },
                columnApi: {
                    getAllColumns: jest.fn(() => [
                        {
                            field: 'test-column-id',
                        },
                    ]),
                } as any,
            } as any,
            screenId: 'test-screen',
            elementId: 'test-table',
            tableElementId: 'test-table',
            columnId: 'test-column-id',
            fieldProperties: {
                bind: 'test-column-id',
                wrapper: CellWrapper,
            },
            isTree: false,
            colDef: {
                context: {
                    screenId: 'test-screen',
                    columnId: 'test-column-id',
                    isEditable: () => false,
                },
                field: 'test-column-id',
                cellRendererParams: {
                    locale: 'en-US',
                    screenId: 'test-screen',
                    tableElementId: 'test-table',
                    columnId: 'test-column-id',
                    elementId: 'elementId',
                    isParentFieldDisabled: false,
                    fieldProperties: {
                        bind: 'test-column-id',
                        wrapper: CellWrapper,
                    },
                    isTree: false,
                    collectionValue: () =>
                        new CollectionValue({
                            screenId: 'test-screen',
                            elementId: 'testField',
                            isTransient: false,
                            hasNextPage: false,
                            orderBy: [{}],
                            columnDefinitions: [
                                [
                                    nestedFields.text<any, any>({ bind: '_id' }),
                                    nestedFields.text<any, any>({ bind: 'anyField' }),
                                ],
                            ],
                            nodeTypes: {},
                            nodes: ['@sage/xtrem-test/AnyNode'],
                            filter: [undefined],
                            initialValues: [],
                            fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                        }),
                },
            },
            eventKey: null,
            stopEditing: jest.fn(),
            getValue: jest.fn(),
            setValue: jest.fn(),
            formatValue: jest.fn(),
            refreshCell: jest.fn(),
            api: {} as any,
            node: {
                rowIndex: 3,
            } as any,
            context: {} as any,
            eGridCell: {} as any,
            eParentOfValue: {} as any,
            registerRowDragger: () => {},
        };
    });

    it('should not render if no data is provided', () => {
        props.data = null;
        const component = render(<LabelCellRenderer {...props} />);
        expect(component.container.firstChild).toEqual(null);
    });

    it('should render a non-localized value', () => {
        props.value = 'anyValue';
        const component = render(<LabelCellRenderer {...props} />);
        expect(component.container.firstChild).toHaveTextContent('anyValue');
    });

    it('should render a mapped value', () => {
        props.value = 'anyValue';
        props.fieldProperties.map = (value: string) => {
            return `${value} mapped`;
        };
        const component = render(<LabelCellRenderer {...props} />);
        expect(component.container.firstChild).toHaveTextContent('anyValue mapped');
    });

    it('should render a localized value', () => {
        props.value = 'anyValue';
        props.fieldProperties.optionType = 'MyVerySpecialEnum';
        expect(localizeSpy).not.toHaveBeenCalled();
        const component = render(<LabelCellRenderer {...props} />);
        expect(localizeSpy).toHaveBeenCalledWith('MyVerySpecialEnum', 'anyValue');
        expect(component.container.firstChild).toHaveTextContent('anyValue localized');
    });

    it('should render a localized value even if there is a map function defined', () => {
        props.value = 'anyValue';
        props.fieldProperties.optionType = 'MyVerySpecialEnum';
        props.fieldProperties.map = (value: string) => {
            return `${value} mapped`;
        };

        const component = render(<LabelCellRenderer {...props} />);
        expect(component.container.firstChild).toHaveTextContent('anyValue localized');
    });
});
