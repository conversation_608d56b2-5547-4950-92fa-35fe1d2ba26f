// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Label component connected Snapshots should render disabled 1`] = `
.c0 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

<div>
  <div
    class="e-field e-label-field e-disabled"
    data-label="Test Field Title"
    data-testid="e-label-field e-field-label-testFieldTitle e-field-bind-test-label-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <span
      class="e-pill-wrapper"
    >
      <span
        class="c0"
        data-component="pill"
        style="background: rgb(230, 235, 237); border-color: rgb(102, 132, 148); color: rgb(102, 132, 148); cursor: default;"
      >
        Test Value
      </span>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Label component connected Snapshots should render disabled callback 1`] = `
.c0 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

<div>
  <div
    class="e-field e-label-field e-disabled"
    data-label="Test Field Title"
    data-testid="e-label-field e-field-label-testFieldTitle e-field-bind-test-label-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <span
      class="e-pill-wrapper"
    >
      <span
        class="c0"
        data-component="pill"
        style="background: rgb(230, 235, 237); border-color: rgb(102, 132, 148); color: rgb(102, 132, 148); cursor: default;"
      >
        Test Value
      </span>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Label component connected Snapshots should render helperText 1`] = `
.c0 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

<div>
  <div
    class="e-field e-label-field"
    data-label="Test Field Title"
    data-testid="e-label-field e-field-label-testFieldTitle e-field-bind-test-label-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <span
      class="e-pill-wrapper"
    >
      <span
        class="c0"
        data-component="pill"
        style="cursor: default;"
      >
        Test Value
      </span>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
      This is a helper text
    </span>
  </div>
</div>
`;

exports[`Label component connected Snapshots should render hidden 1`] = `
.c0 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

<div>
  <div
    class="e-field e-label-field e-hidden"
    data-label="Test Field Title"
    data-testid="e-label-field e-field-label-testFieldTitle e-field-bind-test-label-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <span
      class="e-pill-wrapper"
    >
      <span
        class="c0"
        data-component="pill"
        style="cursor: default;"
      >
        Test Value
      </span>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Label component connected Snapshots should render with a postfix 1`] = `
.c0 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

<div>
  <div
    class="e-field e-label-field"
    data-label="Test Field Title"
    data-testid="e-label-field e-field-label-testFieldTitle e-field-bind-test-label-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <span
      class="e-pill-wrapper"
    >
      <span
        class="c0"
        data-component="pill"
        style="cursor: default;"
      >
        Test Value €
      </span>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Label component connected Snapshots should render with a prefix 1`] = `
.c0 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

<div>
  <div
    class="e-field e-label-field"
    data-label="Test Field Title"
    data-testid="e-label-field e-field-label-testFieldTitle e-field-bind-test-label-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <span
      class="e-pill-wrapper"
    >
      <span
        class="c0"
        data-component="pill"
        style="cursor: default;"
      >
        € Test Value
      </span>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Label component connected Snapshots should render with a prefix callback 1`] = `
.c0 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

<div>
  <div
    class="e-field e-label-field"
    data-label="Test Field Title"
    data-testid="e-label-field e-field-label-testFieldTitle e-field-bind-test-label-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <span
      class="e-pill-wrapper"
    >
      <span
        class="c0"
        data-component="pill"
        style="cursor: default;"
      >
        & Test Value
      </span>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Label component connected Snapshots should render with default properties 1`] = `
.c0 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

<div>
  <div
    class="e-field e-label-field"
    data-label="Test Field Title"
    data-testid="e-label-field e-field-label-testFieldTitle e-field-bind-test-label-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <span
      class="e-pill-wrapper"
    >
      <span
        class="c0"
        data-component="pill"
        style="cursor: default;"
      >
        Test Value
      </span>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Label component connected Snapshots should render with the mapped value 1`] = `
.c0 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

<div>
  <div
    class="e-field e-label-field"
    data-label="Test Field Title"
    data-testid="e-label-field e-field-label-testFieldTitle e-field-bind-test-label-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <span
      class="e-pill-wrapper"
    >
      <span
        class="c0"
        data-component="pill"
        style="cursor: default;"
      >
        Label Mapped Value
      </span>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Label component connected Snapshots should render without value 1`] = `
.c0 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

<div>
  <div
    class="e-field e-label-field"
    data-label="Test Field Title"
    data-testid="e-label-field e-field-label-testFieldTitle e-field-bind-test-empty-label-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <span
      class="e-pill-wrapper"
    >
      <span
        class="c0"
        data-component="pill"
        style="cursor: default;"
      >
         
      </span>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`Label component unconnected Snapshots should render just fine 1`] = `
.c0 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

<div>
  <div
    class="e-field e-label-field"
    data-label="Test Field Title"
    data-testid="e-label-field e-field-label-testFieldTitle e-field-bind-test-label-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Field Title
    </label>
    <span
      class="e-pill-wrapper"
    >
      <span
        class="c0"
        data-component="pill"
        style="cursor: default;"
      >
        testValue
      </span>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;
