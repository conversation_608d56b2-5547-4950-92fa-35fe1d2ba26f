import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import type * as xtremRedux from '../../../../redux';
import type { ScreenBase } from '../../../../service/screen-base';
import { FieldKey } from '../../../types';
import { ConnectedLabelComponent, LabelComponent } from '../label-component';
import * as i18nService from '../../../../service/i18n-service';
import type { LabelDecoratorProperties } from '../label-types';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';

describe('Label component', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: LabelDecoratorProperties<ScreenBase>;
    let localizeSpy: jest.SpyInstance<string, [string, string]> | null = null;

    beforeEach(() => {
        mockFieldProperties = {
            title: 'Test Field Title',
        };
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            state.enumTypes = {
                MySpecialEnumType: ['good', 'bad'],
            };

            localizeSpy = jest
                .spyOn(i18nService, 'localizeEnumMember')
                .mockImplementation((_: any, enumName: string) => `${enumName} localized`);

            addFieldToState(FieldKey.Label, state, screenId, 'test-label-field', mockFieldProperties, 'Test Value');
            addFieldToState(FieldKey.Label, state, screenId, 'test-empty-label-field', mockFieldProperties, null);
            addFieldToState(
                FieldKey.Label,
                state,
                screenId,
                'localized-label-field',
                { ...mockFieldProperties, optionType: 'MySpecialEnumType' },
                'ok',
            );
            addFieldToState(
                FieldKey.Label,
                state,
                screenId,
                'localized-mapped-label-field',
                {
                    ...mockFieldProperties,
                    optionType: 'MySpecialEnumType',
                    map(v) {
                        return `${v}mapped`;
                    },
                },
                'ok',
            );
            mockStore = getMockStore(state);
        });

        afterEach(() => {
            jest.resetAllMocks();
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="test-label-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with the mapped value', () => {
                mockFieldProperties.map = () => 'Label Mapped Value';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="test-label-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="test-label-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render disabled', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="test-label-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render disabled callback', () => {
                const isDisabled = true;
                mockFieldProperties.isDisabled = () => {
                    return isDisabled;
                };
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="test-label-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with a prefix', () => {
                mockFieldProperties.prefix = '€';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="test-label-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with a prefix callback', () => {
                const prefix = '&';
                mockFieldProperties.prefix = () => prefix;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="test-label-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with a postfix', () => {
                mockFieldProperties.postfix = '€';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="test-label-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="test-label-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render without value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="test-empty-label-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with a localized value', () => {
                expect(localizeSpy).not.toHaveBeenCalled();
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="localized-label-field" />
                    </Provider>,
                );
                expect(localizeSpy).toHaveBeenCalledWith('MySpecialEnumType', 'ok');
                expect(container.querySelector('.e-pill-wrapper')).toHaveTextContent('ok localized');
            });

            it('should render with a localized value even if a `map()` property is defined', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="localized-mapped-label-field" />
                    </Provider>,
                );
                expect(container.querySelector('.e-pill-wrapper')).toHaveTextContent('ok localized');
            });

            it('should render with a title', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="test-label-field" />
                    </Provider>,
                );
                const title = container.querySelector('[data-element="label"]')!;
                expect(title).not.toBeNull();
                expect(title).toHaveTextContent('Test Field Title');
            });

            it('should render with a hidden title', () => {
                mockFieldProperties.isTitleHidden = true;

                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="test-label-field" />
                    </Provider>,
                );
                const title = container.querySelector('[data-element="label"]')!;
                expect(title).toBeNull();
            });
        });

        describe('Interactions', () => {
            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="test-label-field" />
                    </Provider>,
                );
                const helperText = container.querySelector('[data-element="help"]')!;
                expect(helperText).not.toBeNull();
                expect(helperText).toHaveTextContent('This is a helper text');
            });

            it('Should render the helper text empty', () => {
                mockFieldProperties.helperText = '';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLabelComponent screenId={screenId} elementId="test-label-field" />
                    </Provider>,
                );
                const helperText = container.querySelector('[data-element="help"]')!;
                expect(helperText).not.toBeNull();
                expect(helperText!.textContent).toEqual(' ');
            });
        });
    });

    describe('unconnected', () => {
        describe('Snapshots', () => {
            it('should render just fine', () => {
                const { container } = render(
                    <LabelComponent
                        elementId="test-label-field"
                        fieldProperties={mockFieldProperties}
                        value="testValue"
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with a background color', () => {
                mockFieldProperties.backgroundColor = '#444444';
                const { container } = render(
                    <LabelComponent
                        elementId="test-label-field"
                        fieldProperties={mockFieldProperties}
                        value="testValue"
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                const pill = container.querySelector('.e-pill-wrapper span')!;
                expect(pill).toHaveStyle({ 'background-color': '#444444' });
            });

            it('should render with a color', () => {
                mockFieldProperties.color = '#444444';
                const { container } = render(
                    <LabelComponent
                        elementId="test-label-field"
                        fieldProperties={mockFieldProperties}
                        value="testValue"
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                const pill = container.querySelector('.e-pill-wrapper span')!;
                expect(pill).toHaveStyle({ color: '#444444' });
            });

            it('should render with a color', () => {
                mockFieldProperties.borderColor = '#444444';
                const { container } = render(
                    <LabelComponent
                        elementId="test-label-field"
                        fieldProperties={mockFieldProperties}
                        value="testValue"
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                const pill = container.querySelector('.e-pill-wrapper span')!;
                expect(pill).toHaveStyle({ 'border-color': '#444444' });
            });
        });

        describe('Interactions', () => {
            it('should update the value on external update', () => {
                const { container, rerender } = render(
                    <LabelComponent
                        elementId="test-label-field"
                        fieldProperties={mockFieldProperties}
                        value="testValue"
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                expect(container.querySelector("span[data-component='pill']")).toHaveTextContent('testValue');

                rerender(
                    <LabelComponent
                        elementId="test-label-field"
                        fieldProperties={mockFieldProperties}
                        value="testValue"
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                expect(container.querySelector("span[data-component='pill']")).toHaveTextContent('testValue');

                rerender(
                    <LabelComponent
                        elementId="test-label-field"
                        fieldProperties={mockFieldProperties}
                        value="newTestValue"
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                expect(container.querySelector("span[data-component='pill']")).toHaveTextContent('newTestValue');
            });
        });
    });
});
