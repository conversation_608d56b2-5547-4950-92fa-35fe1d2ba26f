import type { <PERSON><PERSON><PERSON> } from '../../../types';
import { LabelControlObject } from '../../../control-objects';
import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import type { LabelProperties } from '../label-types';

describe('Label control object', () => {
    let labelControlObject: LabelControlObject;
    let labelProperties: LabelProperties;
    let labelValue: string;

    beforeEach(() => {
        labelProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
        };
        labelValue = 'LABEL TEST';
        labelControlObject = buildControlObject<FieldKey.Label>(LabelControlObject, {
            fieldValue: labelValue,
            fieldProperties: labelProperties,
        });
    });

    it('getting field value', () => {
        expect(labelControlObject.value).toEqual(labelValue);
    });

    it('should set the title', () => {
        const newValue = 'Test Numeric Field Title';
        expect(labelProperties.title).not.toEqual(newValue);
        labelControlObject.title = newValue;
        expect(labelProperties.title).toEqual(newValue);
    });

    it('should set the value prefix', () => {
        const testFixture = '$$';
        expect(labelProperties.prefix).not.toEqual(testFixture);
        labelControlObject.prefix = testFixture;
        expect(labelProperties.prefix).toEqual(testFixture);
    });

    it('should set the value postfix', () => {
        const testFixture = '££';
        expect(labelProperties.postfix).not.toEqual(testFixture);
        labelControlObject.postfix = testFixture;
        expect(labelProperties.postfix).toEqual(testFixture);
    });
});
