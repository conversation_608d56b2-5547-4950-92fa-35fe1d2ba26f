import type { ScreenBase } from '../../../service/screen-base';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type { Changeable, Clickable, ExtensionField, HasParent } from '../traits';

export interface FileValue {
    value: string;
}

export interface FileProperties<CT extends ScreenBase = ScreenBase> extends EditableFieldProperties<CT> {
    /** File types that can be uploaded. Can be either audio/*, video/*, image/*, an extension name starting with '.'
     *  or a valid media type. Look at [IANA Media Types](https://www.iana.org/assignments/media-types/media-types.xhtml). for a complete list of standard media types.
     * It is possible to set more than one file type, simply by defining them separated by a comma.
     */
    fileTypes?: string;
    /** Name of the uploaded file */
    text: string;
}

export interface FileDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<FileProperties<CT>, '_controlObjectType'>,
        Changeable<CT>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>> {
    onFileInfo?: (this: CT, mimeType: string, length: number, fileName: string) => void;
}

export type FileComponentProps = BaseEditableComponentProperties<FileDecoratorProperties, FileValue>;
