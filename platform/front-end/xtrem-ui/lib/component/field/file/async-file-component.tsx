import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { FileComponentProps } from './file-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';

const ConnectedFileComponent = React.lazy(() => import('./file-component'));

export function AsyncConnectedFileComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedFileComponent {...props} />
        </React.Suspense>
    );
}

const FileComponent = React.lazy(() => import('./file-component').then(c => ({ default: c.FileComponent })));

export function AsyncFileComponent(props: FileComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <FileComponent {...props} />
        </React.Suspense>
    );
}
