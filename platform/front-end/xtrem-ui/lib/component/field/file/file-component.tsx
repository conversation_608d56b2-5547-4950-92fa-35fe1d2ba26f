import Button from 'carbon-react/esm/components/button';
import * as React from 'react';
import { connect } from 'react-redux';
import * as tokens from '@sage/design-tokens/js/base/common';
import { localize } from '../../../service/i18n-service';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { getComponentClass, getDataTestIdAttribute, getNewFileReader } from '../../../utils/dom';
import { triggerFieldEvent } from '../../../utils/events';
import { useFocus } from '../../../utils/hooks/effects/use-focus';
import { Icon } from '../../ui/icon/icon-component';
import { getLabelTitle, isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import { mapDispatchToProps, mapStateToProps } from '../field-base-utils';
import type { FileComponentProps } from './file-types';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { resolveByValue } from '../../../utils/resolve-value-utils';

export function FileComponent(props: FileComponentProps): React.ReactElement {
    const hasValue = props.value && props.value.value;
    const [mimeType, setMimeType] = React.useState('data:application/octet-stream;base64');
    const inputFileRef = React.useRef<HTMLInputElement>(null);
    const fileComponentRef = React.useRef<HTMLDivElement>(null);
    useFocus(fileComponentRef, props.isInFocus, 'button');

    const isReadOnly = isFieldReadOnly(props.screenId, props.fieldProperties, props.value, null); // Not available as a nested fields
    const isDisabled = isFieldDisabled(props.screenId, props.fieldProperties, props.value, null); // Not available as a nested fields

    const changeEventHandler = React.useCallback(
        (): Promise<void> => triggerFieldEvent(props.screenId, props.elementId, 'onChange'),
        [props.screenId, props.elementId],
    );

    const clickEventHandler = React.useCallback(
        (): Promise<void> => triggerFieldEvent(props.screenId, props.elementId, 'onClick'),
        [props.screenId, props.elementId],
    );

    const onInputTypeFileChanged = async (event: React.ChangeEvent<HTMLInputElement>): Promise<void> => {
        if (event.target.files && event.target.files.length > 0) {
            const result = await readFile(event.target.files[0]);
            handleChange(
                props.elementId,
                result ? { value: result[1] } : null,
                props.setFieldValue,
                props.validate,
                changeEventHandler,
            );
            if (result) {
                setMimeType(result[0]);
            }
        }
    };

    const readFile = (file: File): Promise<string[] | null> =>
        new Promise<string[] | null>(resolve => {
            const reader = getNewFileReader();

            reader.onload = (): void => {
                let components: string[] | null = null;
                if (reader.result) {
                    const result = reader.result.toString().split(',');
                    if (result.length) {
                        components = result;
                    }
                }
                triggerFieldEvent.apply(null, [
                    props.screenId,
                    props.elementId,
                    'onFileInfo',
                    file.type,
                    file.size,
                    file.name,
                ]);
                resolve(components);
            };
            reader.readAsDataURL(file);
        });

    const onFileUploadAreaClick = (): void => {
        if (!isReadOnly && !isDisabled) {
            inputFileRef.current?.click();
        }
    };

    const onDeleteFile = (): void =>
        handleChange(props.elementId, null, props.setFieldValue, props.validate, changeEventHandler);

    const renderFileUploadedView = (): React.ReactNode => {
        const buildHref = isDisabled || !hasValue ? undefined : `${mimeType},${props.value?.value}`;
        const deleteLabel = localize('@sage/xtrem-ui/crud-delete', 'Delete');
        return (
            <div
                className={`e-file-field-file-uploaded${isDisabled ? ' e-file-field-file-uploaded-disabled' : ''}`}
                data-testid="e-file-field-file-uploaded"
            >
                <div className="e-file-field-details" data-testid="e-file-field-details">
                    <Icon type="pdf" my={1} color={tokens.colorsUtilityMajor300} />
                    <a
                        className="e-file-field-name"
                        data-testid="e-file-field-name"
                        href={buildHref}
                        target="_blank"
                        download={props.fieldProperties.text}
                        rel="noreferrer"
                    >
                        <span>{props.fieldProperties.text}</span>
                    </a>
                    {!isReadOnly && !isDisabled && (
                        <Button
                            iconType="delete"
                            aria-label={deleteLabel}
                            iconTooltipMessage={deleteLabel}
                            disabled={isDisabled}
                            onClick={isDisabled || isReadOnly ? undefined : onDeleteFile}
                        />
                    )}
                </div>
            </div>
        );
    };

    const renderUploadArea = (): React.ReactNode => (
        <div data-testid="e-file-field-upload-area" onClick={onFileUploadAreaClick}>
            <input
                accept={props.fieldProperties.fileTypes}
                className="e-hidden"
                data-testid="e-file-field-file-input"
                onChange={onInputTypeFileChanged}
                ref={inputFileRef}
                type="file"
            />
            <Button
                buttonType="secondary"
                iconType="upload"
                disabled={isDisabled || isReadOnly}
                onClick={clickEventHandler}
            >
                {localize('@sage/xtrem-ui/file-component-browse-files', 'Browse Files')}
            </Button>
        </div>
    );

    const title = getLabelTitle(props.screenId, props.fieldProperties, null); // Not available as a nested fields
    const { isTitleHidden } = props.fieldProperties;

    const infoMessage = resolveByValue({
        screenId: props.screenId,
        fieldValue: props.value,
        propertyValue: props.fieldProperties.infoMessage,
        rowValue: null,
        skipHexFormat: true,
    });

    const warningMessage = resolveByValue({
        screenId: props.screenId,
        fieldValue: props.value,
        propertyValue: props.fieldProperties.warningMessage,
        rowValue: null,
        skipHexFormat: true,
    });

    return (
        <div
            data-testid={getDataTestIdAttribute('file', title, props.elementId)}
            className={getComponentClass(props, 'e-file-field')}
            ref={fileComponentRef}
        >
            {!isTitleHidden && (
                <FieldLabel
                    label={title}
                    errorMessage={props.validationErrors?.[0].message}
                    infoMessage={infoMessage}
                    warningMessage={warningMessage}
                />
            )}
            <div className="e-file-field-content-wrapper" data-testid="e-file-field-content-wrapper">
                {hasValue ? renderFileUploadedView() : renderUploadArea()}
            </div>
            <HelperText helperText={props.fieldProperties.helperText} />
        </div>
    );
}

export const ConnectedFileComponent = connect(mapStateToProps(), mapDispatchToProps())(FileComponent);

export default ConnectedFileComponent;
