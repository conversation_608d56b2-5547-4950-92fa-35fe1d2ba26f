/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { FileControlObject } from './file-control-object';
import type { FileDecoratorProperties } from './file-types';

class FileDecorator extends AbstractFieldDecorator<FieldKey.File> {
    protected _controlObjectConstructor = FileControlObject;

    protected _layout = AbstractFieldLayoutBuilder;
}

/**
 * Initializes the decorated member as a [File]{@link FileControlObject} field with the provided properties
 *
 * @param properties The properties that the [File]{@link FileControlObject} field will be initialized with
 */
export function fileField<T extends ScreenExtension<T>>(
    properties: FileDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.File>(properties, FileDecorator, FieldKey.File);
}

export function fileFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<FileDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.File>(properties);
}
