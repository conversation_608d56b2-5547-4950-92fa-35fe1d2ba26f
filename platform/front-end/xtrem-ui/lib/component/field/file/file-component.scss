@import '../../../render/style/variables.scss';

.e-file-field .e-file-field-content-wrapper {

    .e-file-field-file-uploaded {
        background-color: var(--colorsUtilityMajor050);
        display: flex;  
        height: 100%;      
        justify-content: space-between;

        .e-file-field-details {
            display: flex;
            padding-left: 8px;
            box-sizing: border-box;
            width: 100%;

            .e-file-field-name {
                color: var(--colorsActionMajor500);
                font-family: var(--fontFamiliesDefault);
                font-weight: var(--fontWeights500);
                font-size: 14px;
                line-height: 21px;
                padding-left: 8px;
                text-align: left;
                padding-bottom: 8px;
                padding-top: 8px;
                min-width: 0;
                flex:1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        button {
            padding: 1px 0;
            margin: 0;
            border: 0;

            &:hover {
                background-color: var(--colorsUtilityMajor050);
            }

            span[data-component='icon'] {
                color: var(--colorsUtilityMajor300);
            }

        }

        &.e-file-field-file-uploaded-disabled .e-file-field-name {
            color: var(--colorsUtilityMajor200);
        }
    }
}
