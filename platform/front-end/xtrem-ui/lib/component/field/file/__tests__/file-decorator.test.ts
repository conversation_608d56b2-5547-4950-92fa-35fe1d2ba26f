import type { Page } from '../../../..';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import { fileField } from '../file-decorator';
import type { FileDecoratorProperties } from '../file-types';

describe('File decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'fileField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should set default values when no component properties provided', () => {
        fileField({ text: 'Text File Field' })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: FileDecoratorProperties<ScreenBase> = pageMetadata.uiComponentProperties[
            fieldId
        ] as FileDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onChange).toBeUndefined();
        expect(mappedComponentProperties.onClick).toBeUndefined();
    });

    it('should inherit false abstract-field booleans when no provided', () => {
        fileField({ text: 'Text File Field' })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: FileDecoratorProperties<ScreenBase> = pageMetadata.uiComponentProperties[
            fieldId
        ] as FileDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.isHiddenMobile).toBe(false);
        expect(mappedComponentProperties.isHiddenDesktop).toBe(false);
        expect(mappedComponentProperties.isFullWidth).toBe(false);
        expect(mappedComponentProperties.isHidden).toBe(false);
        expect(mappedComponentProperties.isTransient).toBe(false);
    });

    it('should set values when component properties provided', () => {
        const clickFunc: () => void = jest.fn().mockImplementation(() => {});
        const changeFunc: () => void = jest.fn().mockImplementation(() => {});

        fileField({
            onChange: changeFunc,
            onClick: clickFunc,
            helperText: 'helper text',
            text: 'text',
            fileTypes: 'image/*',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: FileDecoratorProperties<ScreenBase> = pageMetadata.uiComponentProperties[
            fieldId
        ] as FileDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onChange).not.toBeUndefined();
        expect(mappedComponentProperties.onChange).toBe(changeFunc);
        expect(mappedComponentProperties.onClick).not.toBeUndefined();
        expect(mappedComponentProperties.onClick).toBe(clickFunc);
        expect(mappedComponentProperties.helperText).toBe('helper text');
        expect(mappedComponentProperties.text).toBe('text');
        expect(mappedComponentProperties.fileTypes).toBe('image/*');
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(fileField, pageMetadata, fieldId);
        });
    });
});
