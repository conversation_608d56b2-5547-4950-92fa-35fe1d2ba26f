import { renderWithRedux, applyActionMocks } from '../../../../__tests__/test-helpers';
import * as xtremRedux from '../../../../redux';
import { fireEvent } from '@testing-library/react';
import * as React from 'react';
import type { FileDecoratorProperties, FileValue } from '../file-types';
import type { ScreenBase } from '../../../../service/screen-base';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';
import * as domUtils from '../../../../utils/dom';
import ConnectedFileComponent from '../file-component';

const fileValue: FileValue = {
    value:
        '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDADIiJSwlHzIsKSw4NTI7S31RS0VFS5ltc1p9tZ++u7Kfr6zI4f/zyNT/' +
        '16yv+v/9////////wfD/////////////2wBDATU4OEtCS5NRUZP/zq/O/////////////////////////////////////////////////////////////////' +
        '///wAARCAAYAEADAREAAhEBAxEB/8QAGQAAAgMBAAAAAAAAAAAAAAAAAQMAAgQF/8QAJRABAAIBBAEEAgMAAAAAAAAAAQIRAAMSITEEEyJBgTORUWFx/8QAFA' +
        'EBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AOgM52xQDrjvAV5Xv0vfKUALlTQfeBm0HThMNHXkL0Lw/swN5qg' +
        'A8yT4MCS1OEOJV8mBz9Z05yfW8iSx7p4j+jA1aD6Wj7ZMzstsfvAas4UyRHvjrAkC9KhpLMClQntlqFc2X1gUj4viwVObKrddH9YDoHvuujAEuNV+bLwFS8Xx' +
        'dSr+Cq3Vf+4F5RgQl6ZR2p1eAzU/HX80YBYyJLCuexwJCO2O1bwCRidAfWBSctswbI12GAJT3yiwFR7+MBjGK2g/WAJR3FdF84E2rK5VR0YH/9k=',
};

describe('file component', () => {
    const screenId = 'TestPage';
    const fieldId = 'test-file-field';

    const setup = (
        fileProps: Partial<FileDecoratorProperties<ScreenBase>>,
        value: FieldInternalValue<FieldKey.File> = { value: '' },
    ) => {
        const initialState = {
            screenDefinitions: {
                [screenId]: {
                    metadata: {
                        uiComponentProperties: {
                            [fieldId]: { ...fileProps, text: 'test-file-field' },
                        },
                    },
                    values: {
                        ...(value && { [fieldId]: value }),
                    },
                    errors: {},
                },
            },
        };
        const utils = renderWithRedux<FieldKey.File, any>(
            <ConnectedFileComponent screenId={screenId} elementId={fieldId} />,
            {
                initialState,
                fieldType: FieldKey.File,
                fieldValue: value,
                fieldProperties: { ...fileProps, text: 'text-test-file-field' },
                elementId: fieldId,
                screenId,
                mockActions: true,
            },
        );

        const fileField = utils.getByTestId('e-field-bind-test-file-field', { exact: false });

        const link = fileField.querySelector('a[data-testid="e-file-field-name"]');
        const getIcon = (iconType: 'pdf' | 'delete') =>
            fileField.querySelector(`span[data-component="icon"][data-element="${iconType}"]`);

        const browseFilesButton: any = () =>
            fileField.querySelector('div[data-testid="e-file-field-upload-area"] button');
        const clickBrowseFilesButton = () => {
            fireEvent.click(browseFilesButton);
        };

        jest.spyOn(domUtils, 'getNewFileReader').mockImplementation(() => {
            const value: any = {
                result: null,
                onload: null,
                readAsDataURL: () => {
                    if (value.onload) {
                        value.result = fileValue.value;
                        value.onload();
                    }
                },
            };
            return value;
        });

        return {
            ...utils,
            fileField,
            browseFilesButton,
            clickBrowseFilesButton,
            getIcon,
            link,
        };
    };

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    it('can render with redux with defaults', () => {
        const { browseFilesButton, fileField } = setup({ title: 'Test Field Title' });
        expect(fileField).toHaveTextContent('Test Field Title');
        expect(browseFilesButton()).toBeInTheDocument();
    });

    it('can render with redux with value', () => {
        const { getIcon, link } = setup({ title: 'Test Field Title' }, fileValue);
        expect(getIcon('pdf')).toBeInTheDocument();
        expect(getIcon('delete')).toBeInTheDocument();
        expect(link).toBeInTheDocument();
        expect(link).toHaveAttribute('href', `data:application/octet-stream;base64,${fileValue.value}`);
        expect(link).toHaveTextContent('text-test-file-field');
    });

    it('can render hidden', () => {
        const { fileField } = setup({ title: 'Test Field Title', isHidden: true });
        expect(fileField).toHaveClass('e-hidden');
    });

    it('can render disabled without value', () => {
        const { browseFilesButton } = setup({ title: 'Test Field Title', isDisabled: true });
        expect(browseFilesButton()).toHaveAttribute('disabled');
    });

    it('can render disabled without a title', () => {
        const { fileField } = setup({ title: 'Test Field Title', isTitleHidden: true });
        expect(fileField).not.toHaveTextContent('Test Field Title');
    });

    it('can render disabled with value', () => {
        const { getIcon, link } = setup({ title: 'Test Field Title', isDisabled: true }, fileValue);
        expect(link).toBeInTheDocument();
        expect(link).not.toHaveAttribute('href');
        expect(getIcon('delete') as any).not.toBeInTheDocument();
    });

    it('can render read only with value', () => {
        const { getIcon, link } = setup({ title: 'Test Field Title', isReadOnly: true }, fileValue);
        expect(link).toBeInTheDocument();
        expect(link).toHaveAttribute('href');
        expect(getIcon('delete') as any).not.toBeInTheDocument();
    });

    it('should render helperText', () => {
        const { fileField } = setup({ title: 'Test Field Title', helperText: 'This is a helper text' });
        expect(fileField).toHaveTextContent('This is a helper text');
    });

    it('browse file button appears when deleting previous uploaded file', async () => {
        const { getIcon, store } = setup({ title: 'Test Field Title' }, fileValue);
        store.clearActions();
        jest.spyOn(xtremRedux.actions, 'setFieldValue').mockReturnValue({
            type: 'SetFieldValue',
            then: jest.fn(arg => {
                arg();
                return { catch: jest.fn() };
            }),
        } as any);
        expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
        fireEvent.click(getIcon('delete') as any);
        expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(screenId, fieldId, null, true);
    });
});
