import type { <PERSON><PERSON><PERSON> } from '../../../types';
import { FileControlObject } from '../../../control-objects';
import type { FileProperties, FileValue } from '../file-types';
import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';

describe('File Field', () => {
    let fileField: FileControlObject;
    let fieldProperties: FileProperties;

    const fileValue: FileValue = {
        value:
            'data:image;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDADIiJSwlHzIsKSw4NTI7S31RS0VFS5ltc1p9tZ++u7Kfr6zI4f/zyNT/' +
            '16yv+v/9////////wfD/////////////2wBDATU4OEtCS5NRUZP/zq/O/////////////////////////////////////////////////////////////////' +
            '///wAARCAAYAEADAREAAhEBAxEB/8QAGQAAAgMBAAAAAAAAAAAAAAAAAQMAAgQF/8QAJRABAAIBBAEEAgMAAAAAAAAAAQIRAAMSITEEEyJBgTORUWFx/8QAFA' +
            'EBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AOgM52xQDrjvAV5Xv0vfKUALlTQfeBm0HThMNHXkL0Lw/swN5qg' +
            'A8yT4MCS1OEOJV8mBz9Z05yfW8iSx7p4j+jA1aD6Wj7ZMzstsfvAas4UyRHvjrAkC9KhpLMClQntlqFc2X1gUj4viwVObKrddH9YDoHvuujAEuNV+bLwFS8Xx' +
            'dSr+Cq3Vf+4F5RgQl6ZR2p1eAzU/HX80YBYyJLCuexwJCO2O1bwCRidAfWBSctswbI12GAJT3yiwFR7+MBjGK2g/WAJR3FdF84E2rK5VR0YH/9k=',
    };

    beforeEach(() => {
        fieldProperties = {
            title: 'TEST_FIELD_TITLE',
            text: 'TEST_FIELD_TEXT',
        };
    });

    describe('getters and setters', () => {
        beforeEach(() => {
            fileField = buildControlObject<FieldKey.File>(FileControlObject, {
                fieldValue: fileValue,
                fieldProperties,
            });
        });

        it('getting field value', () => {
            expect(fileField.value).toEqual(fileValue);
        });

        it('getting isReadOnly', () => {
            expect(fileField.isReadOnly).toBeFalsy();
        });

        it('should set the title', () => {
            const testFixture = 'Test File Field Title';
            expect(fieldProperties.title).not.toEqual(testFixture);
            fileField.title = testFixture;
            expect(fieldProperties.title).toEqual(testFixture);
        });

        it('should set the helper text', () => {
            const testFixture = 'Test File Field Helper Text';
            expect(fieldProperties.helperText).not.toEqual(testFixture);
            fileField.helperText = testFixture;
            expect(fieldProperties.helperText).toEqual(testFixture);
        });

        it('should set isDisabled property', () => {
            const testFixture = true;
            expect(fieldProperties.isDisabled).not.toEqual(testFixture);
            fileField.isDisabled = testFixture;
            expect(fieldProperties.isDisabled).toEqual(testFixture);
        });

        it('should set isHelperTextHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isHelperTextHidden).not.toEqual(testFixture);
            fileField.isHelperTextHidden = testFixture;
            expect(fieldProperties.isHelperTextHidden).toEqual(testFixture);
        });

        it('should set isHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isHidden).not.toEqual(testFixture);
            fileField.isHidden = testFixture;
            expect(fieldProperties.isHidden).toEqual(testFixture);
        });

        it('should set isReadOnly property', () => {
            const testFixture = true;
            expect(fieldProperties.isReadOnly).not.toEqual(testFixture);
            fileField.isReadOnly = testFixture;
            expect(fieldProperties.isReadOnly).toEqual(testFixture);
        });

        it('should set isTitleHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isTitleHidden).not.toEqual(testFixture);
            fileField.isTitleHidden = testFixture;
            expect(fieldProperties.isTitleHidden).toEqual(testFixture);
        });

        it('should set the text', () => {
            const testFixture = 'Test File Field Text';
            expect(fieldProperties.text).not.toEqual(testFixture);
            fileField.text = testFixture;
            expect(fieldProperties.text).toEqual(testFixture);
        });

        it('should set file types', () => {
            const testFixture = 'image/*';
            expect(fieldProperties.fileTypes).not.toEqual(testFixture);
            fileField.fileTypes = testFixture;
            expect(fieldProperties.fileTypes).toEqual(testFixture);
        });
    });

    describe('focus', () => {
        const focus = jest.fn();
        beforeEach(() => {
            fileField = buildControlObject<FieldKey.File>(FileControlObject, {
                fieldValue: fileValue,
                fieldProperties,
                focus,
            });
        });

        it('should execute focus', () => {
            jest.useFakeTimers();
            expect(focus).not.toHaveBeenCalled();
            fileField.focus();
            jest.runAllTimers();
            expect(focus).toHaveBeenCalled();
        });
    });
});
