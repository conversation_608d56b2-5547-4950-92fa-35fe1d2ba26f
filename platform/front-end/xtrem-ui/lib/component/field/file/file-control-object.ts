/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { FileProperties } from './file-types';
/**
 * [Field]{@link EditableFieldControlObject} that holds a file stream value
 */
export class FileControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.File,
    FieldComponentProps<FieldKey.File>
> {
    static readonly defaultUiProperties: Partial<FieldComponentProps<FieldKey.File>> = {
        ...EditableFieldControlObject.defaultUiProperties,
    };

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }

    @ControlObjectProperty<FileProperties<CT>, FileControlObject<CT>>()
    /** File types that can be uploaded. Can be either audio/*, video/*, image/*, an extension name starting with '.'
     *  or a valid media type. Look at [IANA Media Types](https://www.iana.org/assignments/media-types/media-types.xhtml). for a complete list of standard media types.
     * It is possible to set more than one file type, simply by defining them separated by a comma.
     */
    fileTypes?: string;

    @ControlObjectProperty<FileProperties<CT>, FileControlObject<CT>>()
    /**
     * Name of the uploaded file
     *
     */
    text: string;
}
