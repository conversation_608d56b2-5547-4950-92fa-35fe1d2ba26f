import React from 'react';
import { FieldLabel } from './carbon-utility-components';
import FieldActions from './field-actions-component';

interface FieldHeaderProps {
    children?: React.ReactNode;
    elementId: string;
    screenId: string;
    title?: string;
    isDisabled?: boolean;
    isTitleHidden?: boolean;
}

export function FieldHeader(props: FieldHeaderProps): React.ReactElement {
    return (
        <div className="e-field-header">
            <div className="e-field-title">
                {props.title && !props.isTitleHidden && <FieldLabel label={props.title} />}
            </div>
            <div className="e-field-actions-wrapper">
                {props.children && <div className="e-component-actions">{props.children}</div>}
                <FieldActions screenId={props.screenId} fieldId={props.elementId} isDisabled={props.isDisabled} />
            </div>
        </div>
    );
}
