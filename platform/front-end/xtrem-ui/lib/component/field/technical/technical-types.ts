import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenBaseGenericType } from '../../../types';
import type { NestedField, NestedFieldTypes } from '../../nested-fields';
import type { FieldKey } from '../../types';
import type { PropertyValueType } from '../reference/reference-types';

export interface NestedTechnicalProperties<
    CT extends ScreenBase = ScreenBase,
    NodeType extends ClientNode = any,
    ReferenceNode extends ClientNode = any,
> {
    bind: PropertyValueType<NodeType>;
    node?: keyof ScreenBaseGenericType<CT>;
    nestedFields?: NestedField<ScreenBase, NestedFieldTypes, ReferenceNode>[];
    isTransient?: boolean;
    isTransientInput?: boolean;
    _controlObjectType: FieldKey;
}
