PATH: XTREEM/UI+Field+Widgets/Technical+Nested+Field

## Introduction

Technical nested field is used in order to retrieve additional fields without display it in the UI.

## Example:

```ts
// In a Table or Pod collection declare the nested field technical.
ui.nestedFields.technical<TableWithDetailPanel, ShowCaseProduct, ShowCaseProvider>({
    bind: 'provider',
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    nestedFields: [
        ui.nestedFields.text({
            bind: 'integerField',
        }),
    ],
}),
```

### Binding decorator properties
-   **node**: type of the node that the technical property represents
-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
