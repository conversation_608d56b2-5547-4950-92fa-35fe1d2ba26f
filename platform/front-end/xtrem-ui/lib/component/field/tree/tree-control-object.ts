/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { NestedRecordId, ScreenExtension } from '../../../types';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { CollectionItem, FieldKey } from '../../types';
import type { HasIcon } from '../traits';
import type { TreeDecoratorProperties, TreeProperties } from './tree-types';
import { CollectionValueControlObject } from '../collection-value-field';
import type { GridNestedFieldTypes } from '../../nested-fields';

export interface TreeDropdownAction<CT extends ScreenExtension<CT>, NestedRecordType = CollectionItem> extends HasIcon {
    title: string;
    onClick: (this: CT, recordId: NestedRecordId, rowItem: NestedRecordType) => void;
    isDisabled?: (this: CT, recordId: NestedRecordId, rowItem: NestedRecordType) => boolean;
}

/**
 * [Field]{@link ReadonlyFieldControlObject} that holds a set of values of any type. It can contain nested fields
 */
export class TreeControlObject<
    NestedRecordType extends ClientNode = any,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends CollectionValueControlObject<
    FieldKey.Tree,
    NestedRecordType,
    CT,
    GridNestedFieldTypes,
    TreeProperties<CT, NestedRecordType>
> {
    static readonly defaultUiProperties: Partial<TreeDecoratorProperties> = {
        ...ReadonlyFieldControlObject.defaultUiProperties,
    };
}
