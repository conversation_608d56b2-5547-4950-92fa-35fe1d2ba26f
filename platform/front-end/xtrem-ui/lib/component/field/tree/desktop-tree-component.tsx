import type {
    CellFocusedEvent,
    ColDef,
    Column,
    ColumnEverythingChangedEvent,
    ColumnState,
    FirstDataRenderedEvent,
    GetMainMenuItems,
    GridApi,
    GridOptions,
    HeaderRowContainerCtrl,
    IServerSideDatasource,
    IServerSideGetRowsParams,
    Module,
    RowNode,
} from '@ag-grid-community/core';
import { HeaderRowCtrl, HeaderRowType } from '@ag-grid-community/core';
import { AgGridReact } from '@ag-grid-community/react';
import { EnterpriseCoreModule } from '@ag-grid-enterprise/core';
import { ExcelExportModule } from '@ag-grid-enterprise/excel-export';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { RichSelectModule } from '@ag-grid-enterprise/rich-select';
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping';
import { ServerSideRowModelModule } from '@ag-grid-enterprise/server-side-row-model';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import { objectKeys, type Dict, type FiltrableType } from '@sage/xtrem-shared';
import { difference, isEqual, isNil, last, sortBy } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import type { Unsubscribe } from 'redux';
import { ActionType } from '../../../redux';
import * as xtremRedux from '../../../redux';
import { subscribeToActions } from '../../../redux/middleware/action-subscription-middleware';
import { setAgGridLicence } from '../../../service/ag-grid-licence-service';
import type { CollectionGlobalValidationState, InternalValue } from '../../../service/collection-data-types';
import { RecordActionType } from '../../../service/collection-data-types';
import type { Filter } from '../../../service/filter-service';
import { getScreenElement } from '../../../service/screen-base-definition';
import { ContextType } from '../../../types';
import { getNestedFieldElementId } from '../../../utils/abstract-fields-utils';
import { xtremConsole } from '../../../utils/console';
import { triggerFieldEvent } from '../../../utils/events';
import { convertDeepBindToPath, convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import type { OptionsMenuItem } from '../../container/page/page-types';
import { isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import {
    COLUMN_ID_ROW_ACTIONS,
    COLUMN_ID_ROW_SELECTION,
    COLUMN_ID_VALIDATIONS,
    frameworkComponents,
} from '../../../utils/ag-grid/ag-grid-column-config';
import { DesktopTableHeaderComponent } from '../../ui/table-shared/desktop-table-header';
import type {
    InternalTableProperties,
    TableComponentExternalProps,
    TableInternalComponentProps,
    TableViewGrouping,
    TableViewSortedColumn,
} from '../table/table-component-types';
import {
    AUTO_COLUMN_ID,
    getOrderByFromSortModel,
    getTableContext,
    isColumnBindVariant,
    setTableContext,
} from '../../../utils/table-component-utils';
import type { TreeDecoratorProperties } from './tree-types';
import { TableLoadingCellRenderer } from '../../ui/table-shared/cell/table-loading-cell-renderer';
import type { AgGridColumnConfigWithScreenIdAndColDef } from '../../../utils/ag-grid/ag-grid-utility-types';
import type { FilterModel } from '../../../utils/ag-grid/ag-grid-table-utils';
import { callGridMethod, mapAgGridFilterToXtremFilters } from '../../../utils/ag-grid/ag-grid-table-utils';
import { getColumns } from '../../../utils/ag-grid/ag-grid-service';
import { getRowClassRules } from '../../../utils/ag-grid/ag-grid-cell-editor-utils';
import { onCellKeyDown, onRowClick } from '../../../utils/ag-grid/ag-grid-event-handlers';
import localeText from '../../../utils/ag-grid/ag-grid-strings';
import { hasAgGridLogging } from '../../../utils/window';
import type { TableSidebarDialogContent } from '../../../types/dialogs';

const activatedAgGridModules = [
    ServerSideRowModelModule,
    EnterpriseCoreModule,
    MenuModule,
    RichSelectModule,
    SetFilterModule,
    RowGroupingModule,
    ExcelExportModule,
] as Module[];

enum DataOperationMode {
    NONE,
    FETCH_PAGE,
    REMOVE_RECORD,
    RESET_TABLE,
    FILTER_ERRORS,
}

interface TreeDesktopState {
    columns: AgGridColumnConfigWithScreenIdAndColDef[];
    columnStates?: ColumnState[];
    hasFloatingFilters: boolean;
    isConfigurationDialogOpen?: boolean;
    phantomRows: any[];
    selectedOptionsMenuItem?: OptionsMenuItem;
    selectedRows: any[];
    tableHasData: boolean;
    autoGroupColumnDef?: AgGridColumnConfigWithScreenIdAndColDef;
}

const ROW_HEIGHT = 40;

export class DesktopTreeComponent extends React.Component<
    TableInternalComponentProps<any, TreeDecoratorProperties<any>>,
    TreeDesktopState
> {
    private gridApi: GridApi | null = null;

    private dataOperationMode = DataOperationMode.NONE;

    private actionSubscription: Unsubscribe;

    private collectionValueChangeSubscription: Unsubscribe;

    private collectionValidityChangeSubscription: Unsubscribe;

    protected containerRef = React.createRef<HTMLDivElement>();

    private readonly defaultColDef: GridOptions['defaultColDef'] = { flex: 1, resizable: false, singleClickEdit: true };

    private readonly loadingCellRendererParams: GridOptions['loadingCellRendererParams'] = {
        elementId: this.props.elementId,
    };

    constructor(props: TableInternalComponentProps<any, TreeDecoratorProperties<any>>) {
        super(props);
        setAgGridLicence();

        this.state = {
            columns: [],
            // Enable floating filters for lookups
            hasFloatingFilters: this.props.selectionMode !== undefined,
            // eslint-disable-next-line react/no-unused-state
            phantomRows: [],
            selectedRows: [],
            tableHasData:
                props.value &&
                props.value.getData &&
                props.value.getData({ temporaryRecords: this.props.additionalLookupRecords?.() }).length > 0,
        };
    }

    componentDidMount(): void {
        this.getColumnDefinitions({ keepVisibility: true });
        this.actionSubscription = subscribeToActions(action => {
            if (
                action.type === ActionType.RedrawComponent &&
                this.gridApi &&
                this.props.screenId === action.value.screenId &&
                this.props.elementId === action.value.elementId
            ) {
                const columns: string[] | undefined = action.value.columnBind
                    ? (callGridMethod(this.gridApi, 'getColumnDefs') ?? [])
                          ?.filter((c: ColDef) => c.field?.indexOf(action.value.columnBind!) === 0)
                          .map((c: ColDef) => c.colId as string)
                    : undefined;

                if (columns && columns.length > 0) {
                    callGridMethod(this.gridApi, 'refreshCells', { force: true, columns });
                } else {
                    this.getColumnDefinitions({ keepVisibility: true });
                }
            }
        });
    }

    componentDidUpdate(prevProps: TableInternalComponentProps): void {
        if (this.gridApi && !this.props.isInFocus && prevProps.isInFocus) {
            callGridMethod(this.gridApi, 'stopEditing', true);
        }

        // When the value changes (e.g the user switches between records), we should unset any filters that were applied to the table
        if (prevProps.value !== this.props.value && this.gridApi) {
            callGridMethod(this.gridApi, 'setFilterModel', {});
        }

        if (!this.state.columnStates) {
            this.initColumnState();
        }

        if (
            prevProps.value !== this.props.value ||
            prevProps.fieldProperties.isDisabled !== this.props.fieldProperties.isDisabled ||
            prevProps.isParentDisabled !== this.props.isParentDisabled ||
            prevProps.isReadOnly !== this.props.isReadOnly
        ) {
            this.dataOperationMode = DataOperationMode.NONE;
            this.getColumnDefinitions({ keepVisibility: true });
            // Revalidate field with current data
            this.props.value?.validateField();
            if (this.gridApi && (this.gridApi as any).context && (this.gridApi as any).ctrlsService) {
                // Reset headers
                setTableContext(this.gridApi, c => {
                    c.headerClasses = {};
                });

                /**
                 * "refreshHeader()" also clears any active floating filter so we mimic its
                 * internal implementation and avoid reloading the filters
                 */
                (this.gridApi as any).ctrlsService.getHeaderRowContainerCtrls().forEach(this.refreshHeader);
            }
        }

        if (this.props.openedRecordId !== prevProps.openedRecordId) {
            const rowNodes: RowNode[] = [];

            if (typeof prevProps.openedRecordId === 'string') {
                const node = this.getRowNode({ id: prevProps.openedRecordId, gridApi: this.gridApi });
                if (node) {
                    rowNodes.push(node);
                }
            }

            if (typeof this.props.openedRecordId === 'string') {
                const node = this.getRowNode({ id: this.props.openedRecordId, gridApi: this.gridApi });
                if (node) {
                    rowNodes.push(node);
                }
            }

            if (rowNodes.length) {
                setTimeout(() => callGridMethod(this.gridApi, 'redrawRows', { rowNodes }));
            }
        }

        if (
            this.gridApi &&
            !isEqual(
                sortBy(prevProps.fieldProperties.selectedRecords),
                sortBy(this.props.fieldProperties.selectedRecords),
            )
        ) {
            callGridMethod(this.gridApi, 'deselectAll');
            this.selectTableItems({ api: this.gridApi });
        }
    }

    componentWillUnmount(): void {
        if (this.actionSubscription) {
            this.actionSubscription();
        }
        if (this.collectionValueChangeSubscription) {
            this.collectionValueChangeSubscription();
        }

        if (this.collectionValidityChangeSubscription) {
            this.collectionValidityChangeSubscription();
        }
        window.removeEventListener('resize', this.resizeListener);
    }

    private readonly getRowNode = ({
        id,
        gridApi = this.gridApi,
    }: {
        id: string;
        gridApi?: GridApi | null;
    }): RowNode | undefined => {
        return (
            callGridMethod(gridApi, 'getRowNode', id) ??
            (gridApi as any)?.pinnedRowModel?.pinnedTopRows?.find((r: any) => r.data._id === id)
        );
    };

    private readonly onCollectionUpdated = (type: RecordActionType, rowValue: any): void => {
        if (this.gridApi) {
            if (type === RecordActionType.MODIFIED) {
                if (rowValue.__phantom && rowValue.__forceRowUpdate) {
                    delete rowValue.__forceRowUpdate;
                    // eslint-disable-next-line react/no-unused-state
                    this.setState({ phantomRows: [rowValue] });
                } else {
                    // In this case we target one specific row in order to reduce the amplitude of rendering
                    this.getRowNode({ id: rowValue._id })?.setData(rowValue);
                }
            } else if (type === RecordActionType.ADDED) {
                this.dataOperationMode = DataOperationMode.FETCH_PAGE;
                const nodes = this.props.value.getData({ noLimit: true });
                const addIndex = nodes.findIndex(n => n._id === rowValue._id);
                callGridMethod(this.gridApi, 'applyServerSideTransaction', {
                    add: [rowValue],
                    addIndex: addIndex === -1 ? 0 : addIndex,
                });
            } else if (type === RecordActionType.REMOVED) {
                this.dataOperationMode = DataOperationMode.REMOVE_RECORD;
                callGridMethod(this.gridApi, 'applyServerSideTransaction', {
                    remove: [rowValue],
                });
            }
            const tableHasData =
                this.props.value &&
                this.props.value.getData &&
                this.props.value.getData({ temporaryRecords: this.props.additionalLookupRecords?.() }).length > 0;
            this.setState({ tableHasData });
            this.showHideNoRowsOverlay(!tableHasData);
        }
    };

    private getCursor(api: GridApi, groupKey?: string): string | undefined {
        const keyComponents = groupKey?.split('.');
        const [parentId, parentLevel] = keyComponents || [];

        return groupKey
            ? last(this.props.value.getNestedGrid({ level: Number(parentLevel) + 1, parentId }))?.__cursor
            : callGridMethod(api, 'getDisplayedRowAtIndex', (callGridMethod(api, 'getLastDisplayedRow') ?? 1) - 1)?.data
                  ?.__cursor;
    }

    private getFilters(filterModel: FilterModel): Filter<FiltrableType>[] {
        return objectKeys(filterModel).map(mapAgGridFilterToXtremFilters(filterModel));
    }

    /** Marks items selected on the table, this function is triggered by changes initiated by a pagination or reset
     * event
     **/
    private readonly selectTableItems = ({ api, data }: { api: GridApi; data?: any[] }): void => {
        // Marking user selected rows, selected in the new dataset
        const selectedRecords = this.props.fieldProperties.selectedRecords || [];
        const selectedRows: any[] = [];
        selectedRecords.forEach(id => {
            const rowNode = this.getRowNode({ id, gridApi: api });
            if (rowNode) {
                rowNode.setSelected(true, false, 'checkboxSelected');
                selectedRows.push(rowNode.data);
                if (this.props.selectionMode === 'single' && this.gridApi) {
                    setTimeout(() => {
                        const columns = callGridMethod(this?.gridApi, 'getColumns');
                        if (columns) {
                            // Move focus to first visible cell
                            callGridMethod(
                                api,
                                'setFocusedCell',
                                rowNode.rowIndex!,
                                columns.filter(c => c.isVisible())[0],
                                rowNode.data.__phantom ? 'top' : undefined,
                            );
                        }
                    }, 500);
                }
            }
        });

        this.updateSelectedRows(selectedRows);

        // Enabling event row selection event listeners.
        setTimeout(() => {
            if (data !== undefined && getTableContext(api)?.isSelectAllEnabled) {
                data.forEach((row: any) => {
                    const node = callGridMethod(api, 'getRowNode', row._id);
                    if (node) {
                        node.setSelected(true, false, 'checkboxSelected');
                    }
                });
            }
        }, 10);
    };

    private readonly showHideNoRowsOverlay = (show: boolean): void => {
        setTimeout(() => {
            if (show) {
                callGridMethod(this.gridApi, 'showNoRowsOverlay');
            } else {
                callGridMethod(this.gridApi, 'hideOverlay');
            }
        }, 100);
    };

    private readonly insertDataSourceIntoTable = (
        getRowsParams: IServerSideGetRowsParams,
        data: any,
        lastRow: any,
    ): void => {
        getRowsParams.success({ rowData: data, rowCount: lastRow });
        callGridMethod(getRowsParams.api, 'hideOverlay');
        const isEmptyTable = getRowsParams.request.groupKeys?.length === 0 && lastRow === 0;
        this.showHideNoRowsOverlay(isEmptyTable);
        if (lastRow && lastRow !== -1) {
            triggerFieldEvent(this.props.screenId, this.props.elementId, 'onAllDataLoaded');
        } else {
            triggerFieldEvent(this.props.screenId, this.props.elementId, 'onDataLoaded');
        }
    };

    private readonly refreshHeader = (h: HeaderRowContainerCtrl): void => {
        const header = h as any;
        header.destroyBean(header.columnsRowCtrl);
        header.columnsRowCtrl = header.createBean(new HeaderRowCtrl(0, header.pinned, HeaderRowType.COLUMN));
        const allCtrls = header.getAllCtrls();
        header.comp.setCtrls(allCtrls);
    };

    private readonly updateColumnValidation = (globalValidationState: CollectionGlobalValidationState): void => {
        if (this.gridApi && (this.gridApi as any).context) {
            // Refresh column's header
            const invalidColumns = objectKeys(globalValidationState?.[0] || {});
            const headerClasses = invalidColumns.reduce((acc, column) => {
                acc[column] = false;
                return acc;
            }, {} as Dict<boolean>);
            setTableContext(this.gridApi, c => {
                c.headerClasses = headerClasses;
            });
            /**
             * "refreshHeader()" also clears any active floating filter so we mimic its
             * internal implementation and avoid reloading the filters
             */
            (this.gridApi as any).ctrlsService.getHeaderRowContainerCtrls().forEach(this.refreshHeader);

            // Display/hide validation column
            const isValidationColumnVisible = Boolean(
                callGridMethod(this.gridApi, 'getColumn', COLUMN_ID_VALIDATIONS)?.isVisible(),
            );
            const shouldValidationColumnBeVisible = invalidColumns.length > 0;
            if (isValidationColumnVisible !== shouldValidationColumnBeVisible) {
                callGridMethod(
                    this.gridApi,
                    'setColumnVisible',
                    COLUMN_ID_VALIDATIONS,
                    shouldValidationColumnBeVisible,
                );
                this.autoSizeAllColumns();
            }
        }
    };

    private readonly getUnknownLastRow = (): number | undefined => {
        return -1;
    };

    private readonly getRowCount = ({
        data,
        getRowsParams,
    }: {
        data: any[];
        getRowsParams: IServerSideGetRowsParams;
    }): number | undefined => {
        return data.length < (getRowsParams.request.endRow || 0) - (getRowsParams.request.startRow || 0)
            ? (getRowsParams.request.startRow || 0) + data.length
            : this.getUnknownLastRow();
    };

    /**
     * Ag-grid callback which is triggered whenever the table component needs new data from our framework
     * @param getRowsParams
     */
    private readonly getRows = async (getRowsParams: IServerSideGetRowsParams): Promise<void> => {
        const pageSize = this.props.fieldProperties.pageSize || 20;
        const pageNumber = Math.max(0, Math.ceil((getRowsParams.request.endRow || 0) / pageSize) - 1);
        callGridMethod(getRowsParams.api, 'hideOverlay');

        if (!this.props.value) {
            this.insertDataSourceIntoTable(getRowsParams, [], 0);
            return;
        }

        if (!this.props.isUsingInfiniteScroll) {
            callGridMethod(getRowsParams.api, 'showLoadingOverlay');
        }

        try {
            switch (this.dataOperationMode) {
                case DataOperationMode.NONE:
                    const data = this.props.value.getData({
                        cleanMetadata: false,
                        temporaryRecords: this.props.additionalLookupRecords?.(),
                        limit: this.props.fieldProperties.pageSize || 20,
                    });
                    this.insertDataSourceIntoTable(getRowsParams, data, this.getRowCount({ data, getRowsParams }));
                    this.collectionValueChangeSubscription = this.props.value.subscribeForValueChanges(
                        this.onCollectionUpdated,
                    );
                    this.collectionValidityChangeSubscription = this.props.value.subscribeForValidityChanges(
                        ({ globalValidationState, recordValidationState, recordId }) => {
                            if (this.gridApi) {
                                this.updateColumnValidation(globalValidationState);
                                const rowNode = this.getRowNode({ id: recordId });
                                if (rowNode) {
                                    const previousValidationState = objectKeys(rowNode.data.__validationState || {});
                                    rowNode.setData({
                                        ...rowNode.data,
                                        __validationState: recordValidationState,
                                    });

                                    // Refresh cell
                                    callGridMethod(this.gridApi, 'refreshCells', {
                                        rowNodes: [rowNode],
                                        columns: difference(previousValidationState, objectKeys(recordValidationState)),
                                        force: true,
                                    });
                                }
                            }
                        },
                    );
                    this.selectTableItems({ api: getRowsParams.api, data });
                    break;
                case DataOperationMode.FETCH_PAGE:
                    const groupCompositeKey = last(getRowsParams.request.groupKeys);
                    const keyComponents = groupCompositeKey?.split('.');
                    const [parentId, parentLevel] = keyComponents || [];

                    const newData = await this.props.value.getPageWithCurrentQueryArguments({
                        tableFieldProperties: this.props.fieldProperties,
                        pageSize,
                        pageNumber,
                        cleanMetadata: false,
                        cursor: this.getCursor(getRowsParams.api, groupCompositeKey),
                        parentId,
                        level: isNil(parentLevel) ? 0 : Number(parentLevel) + 1,
                    });

                    this.updateColumnValidation(this.props.value.getValidationStateByColumn());

                    this.insertDataSourceIntoTable(
                        getRowsParams,
                        newData,
                        this.getRowCount({ data: newData, getRowsParams }),
                    );
                    this.selectTableItems({ api: getRowsParams.api, data: newData });
                    break;
                case DataOperationMode.REMOVE_RECORD:
                    callGridMethod(getRowsParams.api, 'deselectAll');

                    // Populating values
                    const mergedData = await this.props.value.getRecordWithCurrentQueryArguments({
                        tableFieldProperties: this.props.fieldProperties,
                        pageSize,
                        pageNumber,
                        group: undefined,
                        cursor: this.getCursor(getRowsParams.api),
                        cleanMetadata: false,
                    });
                    this.insertDataSourceIntoTable(
                        getRowsParams,
                        mergedData,
                        this.getRowCount({ data: mergedData, getRowsParams }),
                    );
                    this.selectTableItems({ api: getRowsParams.api, data: mergedData });
                    this.updateColumnValidation(this.props.value.getValidationStateByColumn());
                    break;
                case DataOperationMode.RESET_TABLE:
                    callGridMethod(getRowsParams.api, 'deselectAll');

                    // Map Ag-grid sorting properties
                    const orderBy = getOrderByFromSortModel(
                        getRowsParams.request.sortModel,
                        this.props.fieldProperties.columns || [],
                    );

                    // Map Ag-grid filter objects into GraphQL/MongoDB friendly filter data structure
                    const userFilters = this.getFilters(this.getFilterModel(getRowsParams.api));
                    // Map Ag-grid page sizing properties
                    const gridPageSize = (getRowsParams.request.endRow || 0) - (getRowsParams.request.startRow || 0);

                    // Populating values
                    const pageData = await this.props.value.getPage({
                        tableFieldProperties: this.props.fieldProperties,
                        filters: userFilters,
                        orderBy,
                        pageSize: gridPageSize,
                        group: undefined,
                        cursor: this.getCursor(getRowsParams.api),
                        cleanMetadata: false,
                        pageNumber: !this.getCursor(getRowsParams.api) ? pageNumber : undefined,
                        selectedOptionsMenuItem: this.state.selectedOptionsMenuItem,
                    });
                    this.insertDataSourceIntoTable(
                        getRowsParams,
                        pageData,
                        this.getRowCount({ data: pageData, getRowsParams }),
                    );
                    this.selectTableItems({ api: getRowsParams.api, data: pageData });
                    callGridMethod(this.gridApi, 'paginationGoToFirstPage');
                    break;

                case DataOperationMode.FILTER_ERRORS:
                    if (this.props.value.getAllInvalidRecords().length === 0) {
                        this.insertDataSourceIntoTable(getRowsParams, [], 0);
                    } else if (this.props.fieldProperties.isTransient) {
                        this.insertDataSourceIntoTable(
                            getRowsParams,
                            this.props.value.getAllInvalidRecords(),
                            this.getRowCount({ data: this.props.value.getAllInvalidRecords(), getRowsParams }),
                        );
                    } else {
                        await this.props.value.fetchInvalidUnloadedRecords();
                        const errorPageData = await this.props.value.getPage({
                            tableFieldProperties: this.props.fieldProperties,
                            filters: this.getFilters(this.getFilterModel(getRowsParams.api)),
                            orderBy: getOrderByFromSortModel(
                                getRowsParams.request.sortModel,
                                this.props.fieldProperties.columns || [],
                            ),
                            pageSize: (getRowsParams.request.endRow || 0) - (getRowsParams.request.startRow || 0),
                            cursor: this.getCursor(getRowsParams.api),
                            cleanMetadata: false,
                            fetchPageSize: 500,
                        });
                        this.insertDataSourceIntoTable(
                            getRowsParams,
                            errorPageData,
                            this.getRowCount({ data: errorPageData, getRowsParams }),
                        );
                    }
                    break;
                default:
                    break;
            }

            if (
                this.props.value.getData({
                    cleanMetadata: false,
                    temporaryRecords: this.props.additionalLookupRecords?.(),
                }).length > 0
            ) {
                this.setState({ tableHasData: true });
            } else {
                this.setState({ tableHasData: false });
            }

            this.dataOperationMode = DataOperationMode.FETCH_PAGE;
        } catch (error) {
            xtremConsole.log(error);
            getRowsParams.fail();
        } finally {
            this.adjustTableHeight({ api: getRowsParams.api });
        }
    };

    private readonly serverSideDataSource: IServerSideDatasource = {
        getRows: this.getRows,
    };

    private readonly isDisabled = (): boolean =>
        this.props.isParentDisabled || isFieldDisabled(this.props.screenId, this.props.fieldProperties, null, null);

    private readonly isReadOnly = (): boolean =>
        resolveByValue<boolean>({
            fieldValue: null,
            propertyValue: this.props.isReadOnly,
            skipHexFormat: true,
            rowValue: null,
            screenId: this.props.screenId,
        }) || isFieldReadOnly(this.props.screenId, this.props.fieldProperties, null, undefined);

    private readonly resizeListener = (): void => {
        this.autoSizeAllColumns();
    };

    private readonly getTableViewColumnHidden = (): Dict<boolean> | undefined => {
        return this.props.tableUserSettings?.$current?.content[0].columnHidden;
    };

    private readonly getTableViewFilter = (): Dict<any> => {
        return this.props.tableUserSettings?.$current?.content[0].filter || {};
    };

    private readonly getTableViewSortOrder = (): TableViewSortedColumn[] | undefined => {
        return this.props.tableUserSettings?.$current?.content[0].sortOrder;
    };

    /**
     * Ag-grid lifecycle event listener. It is triggered when the table is prepared with the initial rendering of the
     * controller components and is ready to receive data.
     **/
    private readonly onGridReady: GridOptions['onGridReady'] = async params => {
        this.gridApi = params.api;

        callGridMethod(this.gridApi, 'setGridOption', 'serverSideDatasource', this.serverSideDataSource);

        if (this.props.contextType === ContextType.dialog) {
            (callGridMethod(this.gridApi, 'getRenderedNodes') ?? [])?.[0]?.setSelected?.(true);
        }

        if (this.gridApi) {
            const filter = this.getTableViewFilter();

            callGridMethod(this.gridApi, 'setFilterModel', filter);
        }

        window.addEventListener('resize', this.resizeListener);
    };

    private readonly updateSelectedRows = (selectedRows: any[]): void => {
        this.setState(prevState => ({
            selectedRows: [
                ...prevState.selectedRows,
                ...selectedRows.filter(sr => !prevState.selectedRows.find(psr => sr._id === psr._id)),
            ],
        }));
    };

    private readonly getFilterModel = (api?: GridApi | null): any | undefined => {
        return callGridMethod(api, 'getFilterModel');
    };

    private readonly handleFilterChanged: GridOptions['onFilterChanged'] = params => {
        const filterModel = this.getFilterModel(params?.api || this.gridApi);

        if (this.props.setTableViewFilter) {
            this.props.setTableViewFilter(0, filterModel);
        }

        this.setShouldResetTable();
    };

    private readonly setShouldResetTable = (): void => {
        this.dataOperationMode = DataOperationMode.RESET_TABLE;
    };

    private readonly getRowId: GridOptions<InternalValue<any>>['getRowId'] = ({ data }): string =>
        data.__path || data._id;

    private readonly getRowClass: GridOptions['getRowClass'] = params => {
        if (!params.data) return undefined;

        if (
            params.data.__phantom === undefined &&
            (params.data.__action === RecordActionType.ADDED || params.data.__action === RecordActionType.MODIFIED)
        ) {
            return 'ag-row-edited';
        }

        return undefined;
    };

    private readonly gridOptions: GridOptions = {
        getRowClass: this.getRowClass,
    };

    private readonly getColumnDefinitions = ({
        refreshDataSource = true,
        resetSelection = true,
        keepVisibility = false,
    }: { refreshDataSource?: boolean; resetSelection?: boolean; keepVisibility?: boolean } = {}): void => {
        const columnsData = [
            this.props.fieldProperties.masterColumn,
            ...(this.props.fieldProperties.columns || []),
        ].map(columnDefinition => ({
            columnDefinition,
            bind: convertDeepBindToPath(columnDefinition.properties.bind) as string,
            elementId: getNestedFieldElementId(columnDefinition),
        }));

        let columns = getColumns({
            accessBindings: this.props.accessBindings,
            columnsData,
            currentTableView: this.props.tableUserSettings?.$current?.content?.[0],
            customizedOrderBy: this.getTableViewSortOrder(),
            elementId: this.props.elementId,
            enumTypes: this.props.enumTypes,
            fieldProperties: () => this.props.fieldProperties,
            isDisabled: this.isDisabled(),
            isFilteringDisabled: true, // this.isDisabled()
            // eslint-disable-next-line react/no-access-state-in-setstate
            hasFloatingFilters: this.state.hasFloatingFilters,
            isParentDisabled: () => !!this.props.isParentDisabled,
            isReadOnly: this.isReadOnly(),
            level: 0,
            locale: this.props.locale,
            lookupSelectionMode: this.props.selectionMode,
            nodeTypes: this.props.nodeTypes,
            dataTypes: this.props.dataTypes,
            screenId: this.props.screenId,
            isSortingDisabled: true, // this.isDisabled()
            value: () => this.props.value,
        });

        if (keepVisibility) {
            columns = columns.map<AgGridColumnConfigWithScreenIdAndColDef>(k => {
                const colState = this.state.columnStates?.find(cs => cs.colId === k.field);
                if (colState) {
                    return { ...k, hide: !!colState.hide };
                }

                const columnHidden = this.getTableViewColumnHidden();
                if (columnHidden && k.field) {
                    return { ...k, hide: !!columnHidden[k.field] };
                }

                return k;
            });
        }

        const groupColumnIndex = columns.findIndex(
            c => c.field === convertDeepBindToPathNotNull(this.props.fieldProperties.masterColumn.properties.bind),
        );

        const colDef: AgGridColumnConfigWithScreenIdAndColDef | undefined = columns.splice(groupColumnIndex, 1)?.[0];

        const autoGroupColumnDef: AgGridColumnConfigWithScreenIdAndColDef | undefined =
            groupColumnIndex !== -1 && colDef
                ? {
                      ...colDef,
                      cellRenderer: 'agGroupCellRenderer',
                      flex: undefined,
                      width: 250,
                      minWidth: 250,
                      resizable: true,
                      editable: false,
                      ...(this.props.groupTitle && { headerName: this.props.groupTitle }),
                      // by default sort by grouping column in ascending order
                      sort: colDef.sort ?? 'asc',
                      sortable: false,
                      headerName: this.props.groupTitle ? this.props.groupTitle : colDef.headerName,
                      pinned: 'left',
                      cellRendererParams: {
                          ...colDef.cellRendererParams,
                          innerRenderer: colDef.cellRenderer,
                      },
                  }
                : undefined;

        this.setState({ columns, autoGroupColumnDef }, () => {
            if (this.gridApi) {
                callGridMethod(this.gridApi, 'stopEditing');
                if (resetSelection) {
                    callGridMethod(this.gridApi, 'deselectAll');
                }
                /**
                 * Column definitions have to be reset in order the new properties based on the new collection value
                 * object and event listeners to take effect
                 */
                const columns = this.state.columns.filter(
                    c =>
                        c.field !==
                        convertDeepBindToPathNotNull(this.props.fieldProperties.masterColumn.properties.bind),
                );

                callGridMethod(this.gridApi, 'setGridOption', 'columnDefs', columns);
                // AG-grid will not update the order of columns unless we explicitly tell it so
                this.sortColumns();
                if (refreshDataSource) {
                    /**
                     * Datasource has to be reset so the grid detects the new collection value and rerenders itself.
                     */
                    callGridMethod(this.gridApi, 'setGridOption', 'serverSideDatasource', this.serverSideDataSource);
                }
            }
        });
    };

    private readonly hasFilterableColumns = (): boolean => this.state.columns.filter(c => c.filter).length > 0;

    private readonly toggleFloatingFilters = (): void => {
        this.setState(
            state => ({
                hasFloatingFilters: !state.hasFloatingFilters,
            }),
            () => {
                this.getColumnDefinitions({ refreshDataSource: false, resetSelection: false, keepVisibility: true });
            },
        );
    };

    private readonly getServerSideGroupKey: GridOptions['getServerSideGroupKey'] = (dataItem: any): any => {
        return dataItem.__compositeKey;
    };

    private readonly getCacheBlockSize = (): number => this.props.fieldProperties.pageSize || 20;

    private readonly isServerSideGroup = (dataItem: any): boolean => Boolean(dataItem?.__isGroup);

    private readonly excludeInternalColumnsFilter = (column: Column): boolean => {
        const colId = column.getColId();
        return (
            colId !== undefined &&
            colId !== COLUMN_ID_ROW_SELECTION &&
            colId !== COLUMN_ID_ROW_ACTIONS &&
            colId !== COLUMN_ID_VALIDATIONS
        );
    };

    private readonly getAllColumnIds = (gridApi: GridApi | null = this.gridApi): string[] => {
        if (!gridApi) {
            return [];
        }
        const allColumns = (callGridMethod(gridApi, 'getColumns') ?? [])
            .filter(this.excludeInternalColumnsFilter)
            .map(column => column.getColId());
        return [...allColumns, AUTO_COLUMN_ID];
    };

    private readonly getAllDisplayedColumnIds = (gridApi: GridApi | null = this.gridApi): string[] => {
        if (!gridApi) {
            return [];
        }

        return (callGridMethod(gridApi, 'getAllDisplayedColumns') ?? []).map(column => {
            const colId = column.getColId();
            if (colId === AUTO_COLUMN_ID) {
                return convertDeepBindToPathNotNull(this.props.fieldProperties.masterColumn.properties.bind);
            }
            return colId;
        });
    };

    autoSizeAllColumns = (gridApi: GridApi | null = this.gridApi): void => {
        if (!gridApi) {
            return;
        }
        callGridMethod(gridApi, 'autoSizeColumns', this.getAllColumnIds(gridApi));
        callGridMethod(this.gridApi, 'sizeColumnsToFit');
    };

    private readonly onFirstDataRendered = (event: FirstDataRenderedEvent): void => {
        this.adjustTableHeight(event);
        setTimeout(() => this.autoSizeAllColumns(event.api), 100);
    };

    private readonly sortColumns = (): void => {
        (callGridMethod(this.gridApi, 'getColumnDefs') ?? []).forEach((c: any) => {
            if (this.gridApi && c.colId && c.context.xtremSortIndex) {
                callGridMethod(this.gridApi, 'moveColumns', [c.colId], c.context.xtremSortIndex);
            }
        });
    };

    private readonly getMainMenuItems: GetMainMenuItems = () => {
        /**
         * params.defaultItems are
        [
            "pinSubMenu",
            "separator",
            "autoSizeThis",
            "autoSizeAll",
            "separator",
            "separator",
            "resetColumns"
        ]
         */
        const defaultMenuItems = ['pinSubMenu', 'separator', 'autoSizeThis', 'autoSizeAll'];
        return defaultMenuItems;
    };

    onCellFocused = (params: CellFocusedEvent): void => {
        if (this.props.onFocus) {
            this.props.onFocus(
                this.props.screenId,
                this.props.elementId,
                String(params.rowIndex || ''),
                typeof params.column === 'object' && params.column ? params.column.getColId() : '',
            );
        }
        if (this.props.selectionMode === 'single') {
            callGridMethod(this.gridApi, 'forEachNode', node => {
                if (node.rowIndex === params.rowIndex) {
                    node.setSelected(true);
                }
            });
        }
    };

    getAllNotVisibleNotHiddenColumns = (tableProperties: InternalTableProperties): string[] => {
        return (tableProperties.columns || [])
            .filter(c => {
                return (c.properties as any).isHiddenOnMainField;
            })
            .map(c => {
                return convertDeepBindToPathNotNull(c.properties.bind);
            });
    };

    hideUnhideColumns = (event?: ColumnEverythingChangedEvent): void => {
        if (
            !this.gridApi ||
            (this.gridApi as any)?.destroyCalled ||
            !this.gridApi ||
            (event && ['toolPanelUi', 'api', 'gridOptionsUpdated'].includes(event.source))
        ) {
            return;
        }

        const hiddenColumns = this.state.columnStates
            ? this.state.columnStates.filter(c => c.colId && c.hide).map(c => c.colId as string)
            : [];
        const allColumns = this.getAllColumnIds();
        const visibleColumns = this.getAllDisplayedColumnIds();
        const notVisibleNotHiddenColumns = this.getAllNotVisibleNotHiddenColumns(this.props.fieldProperties);
        const currentlyHiddenColumns = difference(allColumns, visibleColumns);

        const columnsToHide = visibleColumns
            .filter(c => c !== COLUMN_ID_VALIDATIONS)
            .filter(colId => hiddenColumns.find(colBind => isColumnBindVariant(colBind, colId)));

        const columnsToUnhide = currentlyHiddenColumns
            .filter(c => c !== COLUMN_ID_VALIDATIONS)
            .filter(colId => !hiddenColumns.find(colBind => isColumnBindVariant(colBind, colId)))
            .filter(colId => !notVisibleNotHiddenColumns.find(colBind => isColumnBindVariant(colBind, colId)));

        if (!event || event.source !== 'toolPanelUi') {
            callGridMethod(this.gridApi, 'setColumnsVisible', columnsToHide, false);
            callGridMethod(this.gridApi, 'setColumnsVisible', columnsToUnhide, true);
            this.autoSizeAllColumns();
        }
    };

    getDefaultHeight = (): number => {
        return (
            this.props.fixedHeight ||
            (this.props.numberOfVisibleRows || this.props.fieldProperties.pageSize || 20) * ROW_HEIGHT + 85
        );
    };

    private readonly noRowsOverlayComponentParams: GridOptions['noRowsOverlayComponentParams'] = {
        ...this.props,
        isEditable: !this.isReadOnly() && !this.isDisabled(),
    };

    private readonly onRowClicked: GridOptions['onRowClicked'] = e => {
        return onRowClick(this.props.onRowClick)(e);
    };

    private readonly rowClassRules: GridOptions['rowClassRules'] = getRowClassRules({
        canSelect: this.props.fieldProperties.canSelect || false,
        openedRecordId: this.props.openedRecordId,
    });

    private readonly adjustTableHeight = ({ api = this.gridApi }: { api?: GridApi | null } = {}): void => {
        if (!this.containerRef.current) {
            return;
        }

        if (this.props.fixedHeight) {
            this.containerRef.current.style.height = `${this.props.fixedHeight}px`;
            return;
        }

        // A minimum row height must be set so we have some place to display the empty placeholders
        const renderedRowCount = Math.max(callGridMethod(api, 'getDisplayedRowCount') ?? 4, 4);

        const containerHeight = this.containerRef.current?.clientHeight || 0;
        const size = Math.min(renderedRowCount * ROW_HEIGHT + 164, this.getDefaultHeight());
        if (containerHeight === size) {
            return;
        }
        this.containerRef.current.style.height = `${size}px`;
    };

    async initColumnState(): Promise<void> {
        return new Promise(resolve => {
            if (this.gridApi) {
                const columnStates = callGridMethod(this.gridApi, 'getColumnState') ?? [];

                const columnHidden = this.getTableViewColumnHidden();
                if (columnHidden) {
                    columnStates.forEach(column => {
                        if (Boolean(column.hide) !== Boolean(columnHidden[column.colId as string])) {
                            column.hide = Boolean(columnHidden[column.colId as string]);
                        }
                    });
                }

                this.setState({ columnStates }, resolve);
            }
        });
    }

    render(): React.ReactNode {
        return (
            <>
                {process.env.NODE_ENV === 'test' && this.gridApi && <p data-testid="api" style={{ display: 'none' }} />}
                <DesktopTableHeaderComponent
                    canAddNewLine={false}
                    elementId={this.props.elementId}
                    fieldProperties={this.props.fieldProperties}
                    hasAddItemsButton={false}
                    hasData={this.state.tableHasData}
                    hasSidebar={false}
                    isDisabled={this.isDisabled()}
                    isReadOnly={this.isReadOnly()}
                    onToggleFilters={this.hasFilterableColumns() ? this.toggleFloatingFilters : undefined}
                    onTelemetryEvent={this.props.onTelemetryEvent}
                    screenId={this.props.screenId}
                    validationErrors={this.props.validationErrors}
                />
                <div
                    data-testid={`e-tree-field-${this.props.elementId}`}
                    style={{
                        height: '100%',
                        overflow: 'hidden',
                    }}
                    className="e-tree-field-desktop-wrapper"
                >
                    <div
                        ref={this.containerRef}
                        style={{
                            height: `${this.getDefaultHeight()}px`,
                            maxHeight: '100%',
                            width: '100%',
                        }}
                        className="ag-theme-balham"
                    >
                        {this.state.columns?.length > 0 && (
                            <AgGridReact
                                animateRows={true}
                                autoGroupColumnDef={this.state.autoGroupColumnDef}
                                cacheBlockSize={this.getCacheBlockSize()}
                                columnDefs={this.state.columns}
                                components={frameworkComponents}
                                debug={hasAgGridLogging()}
                                defaultColDef={this.defaultColDef}
                                domLayout="normal"
                                floatingFiltersHeight={ROW_HEIGHT}
                                getMainMenuItems={this.getMainMenuItems}
                                getRowId={this.getRowId}
                                getServerSideGroupKey={this.getServerSideGroupKey}
                                gridOptions={this.gridOptions}
                                groupDisplayType="singleColumn"
                                headerHeight={ROW_HEIGHT}
                                isServerSideGroup={this.isServerSideGroup}
                                loadingCellRenderer={TableLoadingCellRenderer}
                                loadingCellRendererParams={this.loadingCellRendererParams}
                                localeText={localeText()}
                                maxConcurrentDatasourceRequests={1}
                                modules={activatedAgGridModules}
                                noRowsOverlayComponent="DesktopTableEmptyComponent"
                                noRowsOverlayComponentParams={this.noRowsOverlayComponentParams}
                                onCellFocused={this.onCellFocused}
                                onCellKeyDown={onCellKeyDown}
                                onColumnEverythingChanged={this.hideUnhideColumns}
                                onFilterChanged={this.handleFilterChanged}
                                onFirstDataRendered={this.onFirstDataRendered}
                                onGridReady={this.onGridReady}
                                onGridSizeChanged={this.adjustTableHeight}
                                onRowClicked={this.onRowClicked}
                                pagination={false}
                                rowClassRules={this.rowClassRules}
                                rowHeight={ROW_HEIGHT}
                                rowModelType="serverSide"
                                serverSideSortAllLevels={true}
                                singleClickEdit={true}
                                suppressAggFuncInHeader={true}
                                suppressCellFocus={this.isDisabled()}
                                suppressColumnVirtualisation={process.env.NODE_ENV === 'test'}
                                suppressContextMenu={true}
                                suppressDragLeaveHidesColumns={true}
                                suppressExcelExport={false}
                                suppressRowClickSelection={true}
                                treeData={true}
                                undoRedoCellEditing={true}
                                valueCacheNeverExpires={true}
                            />
                        )}
                    </div>
                </div>
            </>
        );
    }
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: TableComponentExternalProps,
): Partial<TableInternalComponentProps> => {
    const screenDefinition = getPageDefinitionFromState(props.screenId, state);
    const screenElement = getScreenElement(screenDefinition);
    const selectedRecordId = screenDefinition.queryParameters?._id;
    const openSidebarDialogContent = Object.values(state.activeDialogs).find(
        dialog =>
            dialog.screenId === props.screenId &&
            dialog.type === 'table-sidebar' &&
            (dialog.content as TableSidebarDialogContent).elementId === props.elementId,
    )?.content as TableSidebarDialogContent;

    return {
        ...props,
        isInFocus:
            !!state.focusPosition &&
            state.focusPosition.elementId === props.elementId &&
            state.focusPosition.screenId === props.screenId &&
            !state.focusPosition.nestedField,
        graphApi: screenElement.$.graph,
        selectedRecordId,
        openedRecordId: openSidebarDialogContent?.recordId,
        tableUserSettings: screenDefinition.userSettings[props.elementId],
        accessBindings: screenDefinition.accessBindings,
        dataTypes: state.dataTypes,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: TableComponentExternalProps,
): Partial<TableInternalComponentProps> => ({
    setTableViewColumnHidden: (level: number, columnHidden?: Dict<boolean>): void => {
        dispatch(xtremRedux.actions.setTableViewColumnHidden(props.screenId, props.elementId, level, columnHidden));
    },
    setTableViewColumnOrder: (level: number, columnOrder?: string[]): void => {
        dispatch(xtremRedux.actions.setTableViewColumnOrder(props.screenId, props.elementId, level, columnOrder));
    },
    setTableViewFilter: (level: number, filter?: FilterModel): void => {
        dispatch(xtremRedux.actions.setTableViewFilter(props.screenId, props.elementId, level, filter));
    },
    setTableViewGrouping: (level: number, grouping?: TableViewGrouping): void => {
        dispatch(xtremRedux.actions.setTableViewGrouping(props.screenId, props.elementId, level, grouping));
    },
    setTableViewOptionsMenuItem: (level: number, optionsMenuItem?: OptionsMenuItem): void => {
        dispatch(
            xtremRedux.actions.setTableViewOptionMenuItem(props.screenId, props.elementId, level, optionsMenuItem),
        );
    },
    setTableViewSortOrder: (level: number, sortOrder?: TableViewSortedColumn[]): void => {
        dispatch(xtremRedux.actions.setTableViewSortOrder(props.screenId, props.elementId, level, sortOrder));
    },
});

export const ConnectedDesktopTableComponent = connect(mapStateToProps, mapDispatchToProps)(DesktopTreeComponent);

export default ConnectedDesktopTableComponent;
