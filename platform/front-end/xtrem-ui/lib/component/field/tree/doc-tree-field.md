PATH: XTREEM/UI+Field+Widgets/Tree+Field

## Introduction

Tree fields are used in order to represent hierarchical data of the same node type.

This component is fairly similar to a table field other than its `masterColumn` and `sublevelProperty` properties.

## Example:

```ts
@ui.decorators.treeField<TreeField>({
    parent() {
        return this.treeBlock;
    },
    canSelect: true,
    node: '@sage/xtrem-people/Employee',
    bind: 'products',
    masterColumn: ui.nestedFields.text({
        bind: 'email',
        title: 'Employee email address',
        optionType: '@sage/x3-products/ProductCategory',
    }),
    sublevelProperty: 'teamMembers',
    columns: [
        ui.nestedFields.text({
            bind: 'firstName',
            title: 'First name',
        }),
        ui.nestedFields.text({
            bind: 'lastName',
            title: 'Last Name',
        }),
        ui.nestedFields.text({
            bind: 'city',
            title: 'City',
        }),
        ui.nestedFields.reference({
            bind: 'country',
            node: '@sage/xtrem-show-case/ShowCaseCountry',
            title: 'Country',
            valueField: 'name',
        }),
    ],
    onChange(_id: string | number) {
        console.log('On tree change');
    },
    onRowSelected(_id: string | number) {
        console.log('On row selected');
    },
    onRowUnselected(_id: string | number) {
        console.log('On row unselected');
    }
})
products: ui.fields.Tree<SalesOrderItem>;
```

### Display decorator properties:

-   **sublevelProperty**: A child collection property. It must be a collection of the same type as the top node level of the tree
-   **canSelect**: Whether the rows of the tree can be selected or not. Defaults to true.
-   **columns**: A list of nested fields to be displayed as columns. The following types can be used as nested fields: 'checkbox', 'icon', 'image', 'label', 'link', 'numeric', 'progress', 'reference', 'text', 'date'.
-   **displayMode**: Determines how the tree rows are displayed. Options are "comfortree" (default) and "compact".
-   **helperText**: The helper text that is displayed above the tree. It is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Whether the tree is disabled or not. It can also be defined as callback function that returns a boolean. Defaults to false.
-   **isFullWidth**: Whether the tree spans all the parent width or not. Defaults to false.
-   **isHidden**: Whether the tree is hidden or not. Defaults to false.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
-   **masterColumn**: the nested field that is used to render the tree control at the beginning of each row. It is pinned to the left of the table
-   **orderBy**: The column or the set of columns which the tree should be sorted by.
-   **title**: The title that is displayed above the tree. It is automatically picked up by the i18n engine and externalized.

### Binding decorator properties:

-   **bind**: The GraphQL object's property that the tree value is bound to. If not provided, the tree name is used.
-   **filter**: Graphql filter that will restrict the records displayed in the tree.
-   **isTransient**: If marked as true, the tree will be excluded from the automatic data binding process (i.e. no data will be fetched from the server). The default value is false.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **node**: The name of the node that the tree represents, it must be defined if you want to use the advanced filtering capabilities.

### Event handler decorator properties:

-   **onClick**: Event triggered when any parts of the tree is clicked, no arguments provided.
-   **onChange**: Event triggered when the tree value changes.
-   **onRowSelected**: Event triggered when a tree row is selected.
-   **onRowUnselected**: Event triggered when a tree row is unselected.
-   **onRowClick**: Event triggered when any part of a tree row is clicked.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

## Runtime functions

-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
-   **redraw(columnBind?: string)**: Redraws the grid element on the screen and executes all property callback functions. If a `columnBind` property is passed, it only redraws that column.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **setRecordValue(rowValue: any)**: Updates a single row on the tree and keeps update history.
-   **getRecordByFieldValue(fieldName: keyof NestedRecordType, fieldValue: any)**: Find a row based on a property's value
-   **getRecordValue(recordId:string)**: Returns a single row. It can only return rows that were previously loaded to the client side-cache by some other user interaction.
-   **addRecord(rowValue: any)**: Adds a single row to the tree and marks it as a new row.
-   **addOrUpdateRecordValue(rowValue: any)**: Add or update row in the tree depending of the existence of the ID field.
-   **removeRecord(recordId:string)**: Removes a single row from the displayed tree. It does NOT delete the corresponding server side entity.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

### Column properties

Please refer to the documentation of the corresponding nested field type.

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Tree).
