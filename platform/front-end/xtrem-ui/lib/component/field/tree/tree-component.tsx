import type { LocalizeLocale } from '@sage/xtrem-shared';
import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import type { CollectionValue } from '../../../service/collection-data-service';
import { runAndDispatchFieldValidation } from '../../../service/dispatch-service';
import { convertFilterDecoratorToGraphQLFilter } from '../../../service/graphql-query-builder';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { getDataTestIdAttribute, isHidden } from '../../../utils/dom';
import { triggerFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import { getFieldTitle } from '../carbon-helpers';
import { HelperText } from '../carbon-utility-components';
import { AsyncDesktopTreeComponent } from './async-desktop-tree-component';
import type { TreeComponentExternalProps, TreeComponentProps, TreeDecoratorProperties } from './tree-types';
import type { NestedRecordId } from '../../../types';

export class TreeComponent extends React.Component<TreeComponentProps> {
    getComponentClass = (): string => {
        let className = 'e-field e-tree-field';
        if (
            isHidden(this.props.item, this.props.browser) ||
            resolveByValue({
                screenId: this.props.screenId,
                skipHexFormat: true,
                propertyValue: this.props.fieldProperties.isHidden,
                rowValue: null,
            })
        ) {
            className = `${className} e-hidden`;
        }

        if (this.props.fieldProperties.isFullWidth) {
            className = `${className} full-width`;
        }

        if (this.props.fieldProperties.isHelperTextHidden) {
            className = `${className} e-helper-text-hidden`;
        }

        if (this.props.fieldProperties.isTitleHidden) {
            className = `${className} e-title-hidden`;
        }

        return className;
    };

    onRowClick = (recordId: NestedRecordId) => (): void => {
        if (this.props.value) {
            const rowValue = this.props.value.getRawRecord({ id: recordId });
            if (this.props.onRowClick) {
                this.props.onRowClick(rowValue);
            } else {
                triggerFieldEvent(
                    this.props.screenId,
                    this.props.elementId,
                    'onRowClick',
                    recordId,
                    splitValueToMergedValue(rowValue),
                );
            }
        }
    };

    render(): React.ReactNode {
        const isMobileTree = !this.props.browser.greaterThan.s;
        const masterColumnProperties = this.props.fieldProperties.masterColumn.properties;

        const title = getFieldTitle(this.props.screenId, this.props.fieldProperties, null);

        return (
            <div
                className={this.getComponentClass()}
                data-testid={getDataTestIdAttribute('tree', title, this.props.elementId)}
            >
                {isMobileTree ? null : (
                    <AsyncDesktopTreeComponent
                        accessBindings={this.props.accessBindings}
                        elementId={this.props.elementId}
                        enumTypes={this.props.enumTypes}
                        fieldProperties={this.props.fieldProperties}
                        groupTitle={getFieldTitle(
                            this.props.screenId,
                            { title: 'title' in masterColumnProperties ? masterColumnProperties.title : undefined },
                            null,
                        )}
                        locale={this.props.locale}
                        nodeTypes={this.props.nodeTypes}
                        onRowClick={this.onRowClick}
                        screenId={this.props.screenId}
                        setFieldProperties={this.props.setFieldProperties}
                        tableUserSettings={this.props.tableUserSettings}
                        validationErrors={[]}
                        value={this.props.value}
                        dataTypes={this.props.dataTypes}
                        fixedHeight={this.props.fixedHeight ? this.props.fixedHeight - 24 : undefined}
                    />
                )}
                {this.props.fieldProperties.helperText && (
                    <HelperText helperText={this.props.fieldProperties.helperText} />
                )}
            </div>
        );
    }
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: TreeComponentExternalProps): TreeComponentProps => {
    const screenDefinition = getPageDefinitionFromState(props.screenId, state);
    const fieldProperties = screenDefinition.metadata.uiComponentProperties[
        props.elementId
    ] as TreeDecoratorProperties<ScreenBase>;
    const pageProperties = screenDefinition.metadata.uiComponentProperties[props.screenId];

    return {
        ...props,
        accessBindings: screenDefinition.accessBindings,
        browser: state.browser,
        dataTypes: state.dataTypes,
        enumTypes: state.enumTypes,
        fieldProperties: {
            ...fieldProperties,
            filter: convertFilterDecoratorToGraphQLFilter(screenDefinition, fieldProperties.filter),
            isTransient: pageProperties.isTransient || fieldProperties.isTransient,
        },
        locale: (state.applicationContext?.locale as LocalizeLocale) || 'base',
        nodeTypes: state.nodeTypes,
        setFieldProperties: xtremRedux.actions.actionStub,
        tableUserSettings: state.screenDefinitions[props.screenId].userSettings[props.elementId],
        validate: xtremRedux.actions.actionStub,
        value: screenDefinition.values[props.elementId],
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: TreeComponentExternalProps,
): Partial<TreeComponentProps> => {
    return {
        validate: (elementId: string, value: CollectionValue): Promise<ValidationResult[] | undefined> =>
            runAndDispatchFieldValidation(props.screenId, elementId, value.getNormalizedChangedRecords()),
        setFieldProperties: (elementId: string, value: TreeDecoratorProperties<ScreenBase>): any => {
            dispatch(xtremRedux.actions.setFieldProperties(props.screenId, elementId, value));
        },
    };
};

export const ConnectedTreeComponent = connect(mapStateToProps, mapDispatchToProps)(TreeComponent);

export default ConnectedTreeComponent;
