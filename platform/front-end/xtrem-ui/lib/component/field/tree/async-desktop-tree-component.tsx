import * as React from 'react';
import type { TableInternalComponentProps } from '../table/table-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const DesktopTreeComponent = React.lazy(() => import('./desktop-tree-component'));

export function AsyncDesktopTreeComponent(props: TableInternalComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={false} bodyHeight="200px" />}>
            <DesktopTreeComponent {...props} />
        </React.Suspense>
    );
}
