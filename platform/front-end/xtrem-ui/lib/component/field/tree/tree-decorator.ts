/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import { standardDecoratorImplementation } from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { TreeControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { TreeDecoratorProperties } from './tree-types';
import type { GridNestedFieldTypes, NestedField } from '../../nested-fields';
import { count } from '../../nested-fields';

class TreeDecorator extends AbstractFieldDecorator<FieldKey.Tree> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = TreeControlObject;
}

/**
 * Initializes the decorated member as a [Tree]{@link TreeControlObject} field with the provided properties
 *
 * @param properties The properties that the [Tree]{@link TreeControlObject} field will be initialized with
 */
export function treeField<CT extends ScreenExtension<CT>, ReferencedItemType extends ClientNode = any>(
    properties: TreeDecoratorProperties<Extend<CT>, ReferencedItemType>,
): (target: CT, name: string) => void {
    const propertiesCopy = {
        columns: [] as NestedField<any, GridNestedFieldTypes, ReferencedItemType>[],
        ...properties,
    };
    // Add master column as hidden column
    propertiesCopy.columns.push({
        ...properties.masterColumn,
        properties: { ...properties.masterColumn.properties, isHidden: true },
        defaultUiProperties: { ...properties.masterColumn.properties, isHidden: true },
    });

    // Add aggregate column for the total count so we can determine if a row can be extended or not
    propertiesCopy.columns.push(count<any>({ bind: properties.sublevelProperty, isHidden: true }));

    return standardDecoratorImplementation<CT, FieldKey.Tree, ReferencedItemType>(
        propertiesCopy,
        TreeDecorator,
        FieldKey.Tree,
        true,
    );
}
