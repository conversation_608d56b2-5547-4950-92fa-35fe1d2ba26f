import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import type { TreeComponentProps } from './tree-types';

const ConnectedTreeComponent = React.lazy(() => import('./tree-component'));

export function AsyncConnectedTreeComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="200px" />}>
            <ConnectedTreeComponent {...props} />
        </React.Suspense>
    );
}

const TreeComponent = React.lazy(() => import('./tree-component').then(e => ({ default: e.TreeComponent })));

export function AsyncTreeComponent(props: TreeComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={false} bodyHeight="200px" />}>
            <TreeComponent {...props} />
        </React.Suspense>
    );
}
