import type { ClientNode } from '@sage/xtrem-client';
import type { Dict, FilterTypeValue, LocalizeLocale } from '@sage/xtrem-shared';
import type { ReduxResponsive, UiComponentUserSettings } from '../../../redux/state';
import type { CollectionValue } from '../../../service/collection-data-service';
import type { PageArticleItem } from '../../../service/layout-types';
import type { DataTypeDetails, FormattedNodeDetails } from '../../../service/metadata-types';
import type { AccessBindings } from '../../../service/page-definition';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import type { ContextType, NestedRecordId, ScreenBaseGenericType, ScreenExtension } from '../../../types';
import type {
    BlockControlObject,
    CollectionValueFieldProperties,
    SectionControlObject,
    TreeDropdownAction,
} from '../../control-objects';
import type { GridNestedFieldTypes, NestedField } from '../../nested-fields';
import type { CollectionItem, FieldControlObjectInstance } from '../../types';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { ClientCollectionValueType } from '../reference/reference-types';
import type { TableViewGrouping, TableViewSortedColumn } from '../table/table-component-types';
import type {
    CanBeReadOnly,
    CanFetchDefaults,
    Clickable,
    ExtensionField,
    HasColumns,
    HasFieldActions,
    HasFilter,
    HasParent,
    VoidPromise,
} from '../traits';

export interface TreeComponentExternalProps extends FieldComponentExternalProperties {
    elementId: string;
    item?: PageArticleItem;
    mobilePerformInitialLoadTreeData?: boolean;
    screenId: string;
    searchText?: string;
}

export interface TreeInternalComponentProps {
    contextType?: ContextType;
    elementId: string;
    fieldProperties: TreeDecoratorProperties<ScreenBase>;
    onRowClick?: (recordId: NestedRecordId) => () => void;
    screenId: string;
    searchText?: string;
    setFieldProperties?: (elementId: string, properties: TreeDecoratorProperties<ScreenBase>) => void;
    setTableViewColumnHidden?: (level: number, columnHidden?: Dict<boolean>) => void;
    setTableViewColumnOrder?: (level: number, columnOrder?: string[]) => void;
    setTableViewFilter?: (level: number, filter?: any) => void;
    setTableViewGrouping?: (level: number, grouping?: TableViewGrouping) => void;
    setTableViewSearchText?: (level: number, searchText: string) => void;
    setTableViewSortOrder?: (level: number, sortOrder?: TableViewSortedColumn[]) => void;
    tableUserSettings?: Dict<UiComponentUserSettings>;
    value: CollectionValue;
    validationErrors: ValidationResult[];
    fixedHeight?: number;
}

export interface TreeComponentProps extends TreeComponentExternalProps {
    accessBindings: AccessBindings;
    browser: ReduxResponsive;
    contextType?: ContextType;
    dataTypes: Dict<DataTypeDetails>;
    enableMobileLoadMore?: boolean;
    enumTypes: Dict<string[]>;
    fieldProperties: TreeDecoratorProperties<ScreenBase>;
    isLoading?: boolean;
    locale: LocalizeLocale;
    nodeTypes: Dict<FormattedNodeDetails>;
    onRowClick?: (rowValue: CollectionItem) => void;
    setFieldProperties?: (elementId: string, properties: TreeDecoratorProperties<ScreenBase>) => void;
    tableUserSettings: Dict<UiComponentUserSettings>;
    validate?: (elementId: string, value: CollectionValue) => Promise<ValidationResult[] | undefined>;
    value: CollectionValue;
}

export interface FilterValue {
    filterType: {
        text: string;
        value: FilterTypeValue;
    };
    filterValue: any;
}

export interface TreeDecoratorProperties<CT extends ScreenBase = ScreenBase, NestedRecordType extends ClientNode = any>
    extends TreeProperties<CT, NestedRecordType>,
        Clickable<CT>,
        HasFieldActions<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>>,
        CanFetchDefaults {
    /** Function to be executed when a row is selected */
    onRowSelected?: (this: CT, recordId: NestedRecordId, rowItem: NestedRecordType) => VoidPromise;
    /** Function to be executed when a row is unselected */
    onRowUnselected?: (this: CT, recordId: NestedRecordId, rowItem: NestedRecordType) => VoidPromise;
    /** Function to be executed when the tree field's value changes */
    onChange?: (this: CT, recordId: NestedRecordId, column: string, rowItem: NestedRecordType) => VoidPromise;
    /** Function to be executed when any part of a row is clicked */
    onRowClick?: (this: CT, recordId: NestedRecordId, rowItem: NestedRecordType) => VoidPromise;
}

export interface TreeProperties<CT extends ScreenExtension<CT> = ScreenBase, NestedRecordType extends ClientNode = any>
    extends CollectionValueFieldProperties<CT, NestedRecordType>,
        HasFieldActions<CT>,
        HasFilter<CT, NestedRecordType>,
        HasColumns<CT, NestedRecordType>,
        CanBeReadOnly<CT, NestedRecordType> {
    /** Whether the rows of the tree can be selected or not. Defaults to true */
    canSelect?: boolean;
    /** Determines how the tree rows are displayed. Defaults to "comfortree" */
    sublevelProperty: ClientCollectionValueType<NestedRecordType>;
    /** The master column */
    masterColumn: NestedField<CT, GridNestedFieldTypes, NestedRecordType>;
    /** Actions that are rendered at the end of the tree as a drop-down menu */
    dropdownActions?: TreeDropdownAction<CT>[];
    /** Selected rows identifiers */
    selectedRecords?: NestedRecordId[];
    /** The GraphQL node that the tree represents, needed for filtering */
    node?: keyof ScreenBaseGenericType<CT>;
}
