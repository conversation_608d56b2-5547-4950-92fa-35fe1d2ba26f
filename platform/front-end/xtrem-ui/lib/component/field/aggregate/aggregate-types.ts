import type { BaseReadonlyComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    <PERSON>lick<PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Has<PERSON>elperT<PERSON>t,
    HasIcon,
    HasParent,
    HasPlaceholder,
    HasScale,
    HasUnit,
    Nested,
    NestedClickable,
    NestedHasUnit,
    Postfixable,
    Prefixable,
    Sizable,
} from '../traits';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import type { ClientCollection, ClientNode } from '@sage/xtrem-client';
import type { AggregationMethod, FieldControlObjectInstance } from '../../types';
import type { BlockControlObject, TileControlObject } from '../../control-objects';
import type { ScreenBase } from '../../../service/screen-base';

export type AggregateComponentProps = BaseReadonlyComponentProperties<
    AggregateProperties,
    number,
    NestedFieldsAdditionalProperties
>;
export type AggregateValuesSelector<T> =
    T extends ClientCollection<any>
        ? void
        : T extends object
          ? { [K in keyof T]?: T[K] extends (infer U)[] ? AggregateValuesSelector<U> : AggregateValuesSelector<T[K]> }
          : true;

export interface AggregateProperties<CT extends ScreenBase = ScreenBase, ReferencedItemType extends ClientNode = any>
    extends ReadonlyFieldProperties<CT>,
        Postfixable<CT, ReferencedItemType>,
        Prefixable<CT, ReferencedItemType>,
        Sizable,
        HasPlaceholder,
        HasIcon,
        HasScale<CT, ReferencedItemType>,
        HasFilter<CT, ReferencedItemType> {
    /** The child collection that is the subject of the aggregation query */
    aggregateOn: keyof ReferencedItemType | AggregateValuesSelector<ReferencedItemType>;
    /** The calculation method of the aggregation */
    aggregationMethod: AggregationMethod;
}

export interface AggregateDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    ReferencedItemType extends ClientNode = any,
> extends Omit<AggregateProperties<CT, ReferencedItemType>, '_controlObjectType'>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasHelperText,
        HasParent<CT, BlockControlObject<CT> | TileControlObject<CT>>,
        HasUnit<CT>,
        Sizable {}

export interface NestedAggregateProperties<CT extends ScreenBase = ScreenBase, NodeType extends ClientNode = any>
    extends Omit<AggregateProperties<CT, NodeType>, 'bind'>,
        Nested<NodeType>,
        NestedClickable<CT, NodeType>,
        NestedHasUnit<CT, NodeType>,
        Sizable {}
