/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ClickableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { AggregateControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { AggregateDecoratorProperties } from './aggregate-types';

class AggregateDecorator extends AbstractFieldDecorator<FieldKey.Aggregate> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = AggregateControlObject;
}

/**
 * Initializes the decorated member as a [Aggregate]{@link AggregateControlObject} field with the provided properties
 *
 * @param properties The properties that the [Aggregate]{@link AggregateControlObject} field will be initialized with
 */
export function aggregateField<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: AggregateDecoratorProperties<Extend<T>, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Aggregate, ReferencedItemType>(
        properties,
        AggregateDecorator,
        FieldKey.Aggregate,
    );
}

export function aggregateFieldOverride<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: ClickableOverrideDecoratorProperties<
        AggregateDecoratorProperties<Extend<T>, ReferencedItemType>,
        Extend<T>
    >,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Aggregate, ReferencedItemType>(properties);
}
