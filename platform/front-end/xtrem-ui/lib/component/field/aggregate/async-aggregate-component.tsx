import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { AggregateComponentProps } from './aggregate-types';

const ConnectedAggregateComponent = React.lazy(() => import('./aggregate-component'));

export function AsyncConnectedAggregateComponent(props: FieldComponentExternalProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <ConnectedAggregateComponent {...props} />
        </React.Suspense>
    );
}

const AggregateComponent = React.lazy(() =>
    import('./aggregate-component').then(c => ({ default: c.AggregateComponent })),
);

export function AsyncAggregateComponent(props: AggregateComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader size="small" />}>
            <AggregateComponent {...props} />
        </React.Suspense>
    );
}
