PATH: XTREEM/UI+Field+Widgets/Aggregate+Field

## Introduction

Aggregate fields can be used to represent a collection by aggregating it to a single value using various methods. This field type is read-only and changes to its value will never be sent when a server-side mutation is called.

It is available as a nested field so it can be used for example in tables and on the navigation panel.

## Example:

```ts
@ui.decorators.aggregateField<AggregateField, Product>({
    parent() {
        return this.aggregateBlock;
    },
    title: 'An aggregated value',
    isTransient: false,
    aggregateOn: 'listPrice',
    aggregationMethod: 'avg',
    scale: 2,
    onClick() {
        console.log('Doing something when the field is clicked');
    },
    placeholder: 'Type something...',
    postfix: 'pieces',
    helperText: 'This text goes underneath the field',
})
products: ui.fields.Aggregate;

@ui.decorators.aggregateField<AggregateField, Product>({
    parent() {
        return this.aggregateBlock;
    },
    title: 'Count of countries that this product has suppliers in',
    isTransient: false,
    aggregateOn: {
        supplier: {
            country: {
                code: true
            }
        }
    },
    aggregationMethod: 'distinctCount',
    scale: 2,
    onClick() {
        console.log('Doing something when the field is clicked');
    },
    placeholder: 'Type something...',
    postfix: 'pieces',
    helperText: 'This text goes underneath the field',
})
products: ui.fields.Aggregate;
```

### Display decorator properties:

-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **helperText**: The helper aggregate that is displayed above the field. It is automatically picked up by the i18n engine and externalized.
-   **prefix**: A string that is displayed inside the field before the value, aligned to the left.
-   **postfix**: A string that is displayed inside the field after the value, aligned to the right.
-   **placeholder**: Placeholder aggregate which is displayed inside the field body when the field is empty. It is automatically picked up by the i18n engine and externalized.
-   **scale**: Number of decimal digits to be displayed in the field.
-   **size**: Vertical size of the field, it can be `small`, `medium` or `large`. It is set to medium by default.
-   **isFullWidth**: Whether a field should take the full width of the screen.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **isHelperTextHidden**: Whether the helper aggregate underneath the field should be displayed and its vertical space preserved.
-   **icon**: Icon to be displayed on the right side of the input. The list of icons are defined by [DLS](https://brand.sage.com/d/NdbrveWvNheA/foundations#/icons/icons).
-   **iconColor**: Color of the input icon, only supported if the field is rendered within a tile container.
-   **unit**: Automatically sets the scale and the prefix or postfix of the displayed number based on the configured unit and the currency.
-   **unitMode**: Hint for the automatic unit formatting, it can be either `'currency'` or `'unitOfMeasure'`. Defaults to `'currency'`.

### Binding decorator properties:

-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **aggregateOn**: The name of the child collection node that is the subject of the aggregation operation. For nested property aggregation, this value can also be defined as an object that represents the nested query structure. In this case always only the first key of each level is considered. On the deepest level, the property name must be set with a `true` value.
-   **aggregationMethod**: The calculation method that is used to reduce the collection to a single value. The following methods can be used: `min`, `max`, `avg`, `sum`, `distinctCount`.

### Event handler decorator properties:

-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

## Runtime functions

-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
-   **focus()**: Moves the focus to the field.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
