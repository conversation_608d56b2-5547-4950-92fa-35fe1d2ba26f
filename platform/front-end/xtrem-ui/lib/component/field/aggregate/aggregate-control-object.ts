/**
 * @packageDocumentation
 * @module root
 * */
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { AggregateDecoratorProperties } from './aggregate-types';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import type { Unit } from '@sage/xtrem-shared';
import type { UnitMode } from '../traits';

/**
 * [Field]{@link AggregateControlObject} that holds a single-line aggregate value
 */
export class AggregateControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends ReadonlyFieldControlObject<
    CT,
    FieldKey.Aggregate,
    FieldComponentProps<FieldKey.Aggregate>
> {
    @ControlObjectProperty<AggregateDecoratorProperties<CT>, AggregateControlObject<CT>>()
    /** Icon of the input field. It will be placed on the right side. */
    icon?: IconType;

    @ControlObjectProperty<AggregateDecoratorProperties<CT>, AggregateControlObject<CT>>()
    /** Color of the icon, only supported in tile containers */
    iconColor?: string;

    @ControlObjectProperty<AggregateDecoratorProperties<CT>, AggregateControlObject<CT>>()
    /**
     * Number of digits after the numeric field value decimal point.
     * Must be in the range 0 - 20, inclusive.
     */
    scale?: number;

    @ControlObjectProperty<AggregateDecoratorProperties<CT>, AggregateControlObject<CT>>()
    /** Aggregate to be displayed inline after the field value */
    prefix?: string;

    @ControlObjectProperty<AggregateDecoratorProperties<CT>, AggregateControlObject<CT>>()
    /** Aggregate to be displayed inline before the field value */
    postfix?: string;

    @ControlObjectProperty<AggregateDecoratorProperties<CT>, AggregateControlObject<CT>>()
    /** Placeholder to be displayed in the field body */
    placeholder?: string;

    /** Unit of measure, if set the prefix/postfix and the scale is automatically set based on the user's locale */
    @FieldControlObjectResolvedProperty<AggregateDecoratorProperties<CT>, AggregateControlObject<CT>>()
    unit?: Unit | null;

    /** When the unit is set, this mode determines whether the unit should be handled as a unit of measure or a currency. Defaults to 'currency'. */
    @FieldControlObjectResolvedProperty<AggregateDecoratorProperties<CT>, AggregateControlObject<CT>>()
    unitMode?: UnitMode;

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }
}
