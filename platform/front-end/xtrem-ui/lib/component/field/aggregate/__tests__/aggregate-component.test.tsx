import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type * as xtremRedux from '../../../../redux';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import type { AggregateProperties } from '../../../control-objects';
import { FieldKey } from '../../../types';
import { ConnectedAggregateComponent } from '../aggregate-component';
import { render } from '@testing-library/react';

describe('Aggregate component', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: AggregateProperties<any>;
    const fieldValue = 1.2345;
    beforeEach(() => {
        mockFieldProperties = {
            title: 'Test Field Title',
            aggregateOn: 'anyCollectionField',
            aggregationMethod: 'max',
        };
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(
                FieldKey.Aggregate,
                state,
                screenId,
                'test-aggregate-field',
                mockFieldProperties,
                fieldValue,
            );
            addFieldToState(
                FieldKey.Aggregate,
                state,
                screenId,
                'test-empty-aggregate-field',
                mockFieldProperties,
                null,
            );
            mockStore = getMockStore(state);
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedAggregateComponent screenId={screenId} elementId="test-aggregate-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedAggregateComponent screenId={screenId} elementId="test-aggregate-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render disabled', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedAggregateComponent screenId={screenId} elementId="test-aggregate-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with a prefix', () => {
                mockFieldProperties.prefix = 'tel:';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedAggregateComponent screenId={screenId} elementId="test-aggregate-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with a postfix', () => {
                mockFieldProperties.postfix = 'x';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedAggregateComponent screenId={screenId} elementId="test-aggregate-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should with full-width', () => {
                mockFieldProperties.isFullWidth = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedAggregateComponent screenId={screenId} elementId="test-aggregate-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper aggregate';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedAggregateComponent screenId={screenId} elementId="test-aggregate-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render empty when no value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedAggregateComponent screenId={screenId} elementId="test-empty-aggregate-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with various field sizes', () => {
                mockFieldProperties.size = 'small';
                let wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedAggregateComponent screenId={screenId} elementId="test-aggregate-field" />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'medium';
                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedAggregateComponent screenId={screenId} elementId="test-aggregate-field" />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'large';
                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedAggregateComponent screenId={screenId} elementId="test-aggregate-field" />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();
            });
        });

        describe('Interactions', () => {
            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper aggregate';
                const { queryByTestId } = render(
                    <Provider store={mockStore}>
                        <ConnectedAggregateComponent screenId={screenId} elementId="test-aggregate-field" />
                    </Provider>,
                );

                expect(queryByTestId('e-field-helper-text')).not.toBeNull();
                expect(queryByTestId('e-field-helper-text')).toHaveTextContent('This is a helper aggregate');
            });

            it('Should not render helperText', () => {
                mockFieldProperties.helperText = undefined;
                const { queryByTestId } = render(
                    <Provider store={mockStore}>
                        <ConnectedAggregateComponent screenId={screenId} elementId="test-aggregate-field" />
                    </Provider>,
                );

                expect(queryByTestId('e-field-helper-text')).not.toBeNull();
                expect(queryByTestId('e-field-helper-text')!.textContent).toEqual(' ');
            });
        });
    });
});
