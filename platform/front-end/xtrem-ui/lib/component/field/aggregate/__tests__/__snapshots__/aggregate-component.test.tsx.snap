// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Aggregate component connected Snapshots should render disabled 1`] = `
<div>
  <div
    class="e-field e-aggregate-field e-read-only e-disabled"
    data-label="Test Field Title"
    data-testid="e-aggregate-field e-field-label-testFieldTitle e-field-bind-test-aggregate-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-aggregate-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          1.235
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Aggregate component connected Snapshots should render empty when no value 1`] = `
<div>
  <div
    class="e-field e-aggregate-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-aggregate-field e-field-label-testFieldTitle e-field-bind-test-empty-aggregate-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-empty-aggregate-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        />
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Aggregate component connected Snapshots should render helperText 1`] = `
<div>
  <div
    class="e-field e-aggregate-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-aggregate-field e-field-label-testFieldTitle e-field-bind-test-aggregate-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-aggregate-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          1.235
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
          This is a helper aggregate
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Aggregate component connected Snapshots should render hidden 1`] = `
<div>
  <div
    class="e-field e-aggregate-field e-hidden e-read-only"
    data-label="Test Field Title"
    data-testid="e-aggregate-field e-field-label-testFieldTitle e-field-bind-test-aggregate-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-aggregate-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          1.235
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Aggregate component connected Snapshots should render with a postfix 1`] = `
<div>
  <div
    class="e-field e-aggregate-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-aggregate-field e-field-label-testFieldTitle e-field-bind-test-aggregate-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-aggregate-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          1.235 x
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Aggregate component connected Snapshots should render with a prefix 1`] = `
<div>
  <div
    class="e-field e-aggregate-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-aggregate-field e-field-label-testFieldTitle e-field-bind-test-aggregate-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-aggregate-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          tel: 1.235
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Aggregate component connected Snapshots should render with default properties 1`] = `
<div>
  <div
    class="e-field e-aggregate-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-aggregate-field e-field-label-testFieldTitle e-field-bind-test-aggregate-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-aggregate-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          1.235
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Aggregate component connected Snapshots should render with various field sizes 1`] = `
<div>
  <div
    class="e-field e-aggregate-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-aggregate-field e-field-label-testFieldTitle e-field-bind-test-aggregate-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-aggregate-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          1.235
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Aggregate component connected Snapshots should render with various field sizes 2`] = `
<div>
  <div
    class="e-field e-aggregate-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-aggregate-field e-field-label-testFieldTitle e-field-bind-test-aggregate-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-aggregate-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          1.235
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Aggregate component connected Snapshots should render with various field sizes 3`] = `
<div>
  <div
    class="e-field e-aggregate-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-aggregate-field e-field-label-testFieldTitle e-field-bind-test-aggregate-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-aggregate-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          1.235
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Aggregate component connected Snapshots should with full-width 1`] = `
<div>
  <div
    class="e-field e-aggregate-field e-full-width e-read-only"
    data-label="Test Field Title"
    data-testid="e-aggregate-field e-field-label-testFieldTitle e-field-bind-test-aggregate-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-aggregate-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          1.235
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;
