import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import * as pageMetaData from '../../../../service/page-metadata';
import { aggregateField } from '../aggregate-decorator';

describe('Aggregate Decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'aggregateField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(aggregateField, pageMetadata, fieldId);
        });
    });
});
