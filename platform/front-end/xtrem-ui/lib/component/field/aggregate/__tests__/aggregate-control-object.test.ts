import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import { AggregateControlObject } from '../../../control-objects';
import type { <PERSON>Key } from '../../../types';
import type { AggregateProperties } from '../aggregate-types';

describe('Aggregate control object', () => {
    let aggregateControlObject: AggregateControlObject;
    let aggregateProperties: AggregateProperties;
    let aggregateValue: number;

    beforeEach(() => {
        aggregateProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
            aggregateOn: 'anyCollectionField',
            aggregationMethod: 'max',
        };
        aggregateValue = 5.4321;
        aggregateControlObject = buildControlObject<FieldKey.Aggregate>(AggregateControlObject, {
            fieldValue: 5.4321,
            fieldProperties: aggregateProperties,
        });
    });

    it('getting field value', () => {
        expect(aggregateControlObject.value).toEqual(aggregateValue);
    });

    it('setting field value', () => {
        const newValue = 12345;
        aggregateControlObject.value = newValue;
        expect(aggregateControlObject.value).toEqual(newValue);
    });

    it('should set the title', () => {
        const newValue = 'Test Aggregate Field Title';
        expect(aggregateProperties.title).not.toEqual(newValue);
        aggregateControlObject.title = newValue;
        expect(aggregateProperties.title).toEqual(newValue);
    });

    it('should set the value prefix', () => {
        const newValue = '$$';
        expect(aggregateProperties.prefix).not.toEqual(newValue);
        aggregateControlObject.prefix = newValue;
        expect(aggregateProperties.prefix).toEqual(newValue);
    });

    it('should set the value postfix', () => {
        const newValue = '££';
        expect(aggregateProperties.postfix).not.toEqual(newValue);
        aggregateControlObject.postfix = newValue;
        expect(aggregateProperties.postfix).toEqual(newValue);
    });

    it('should set the scale', () => {
        const newValue = 2;
        expect(aggregateProperties.scale).not.toEqual(newValue);
        aggregateControlObject.scale = newValue;
        expect(aggregateProperties.scale).toEqual(newValue);
    });
});
