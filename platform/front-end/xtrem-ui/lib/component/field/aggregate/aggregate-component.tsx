import * as React from 'react';
import { connect } from 'react-redux';
import { formatNumericValue, getScalePrefixPostfixFromUnit } from '../../../utils/formatters';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps, ReadonlyFieldBaseComponent } from '../field-base-component';
import type { AggregateDecoratorProperties } from './aggregate-types';
import type { NestedFieldsAdditionalProperties } from '../field-base-component-types';

export interface AggregateComponentState {
    value: string;
}

export class AggregateComponent extends ReadonlyFieldBaseComponent<
    AggregateDecoratorProperties,
    number,
    NestedFieldsAdditionalProperties
> {
    render(): React.ReactNode {
        const computedUnitProperties = getScalePrefixPostfixFromUnit(
            this.props.screenId,
            this.props.locale,
            this.props.fieldProperties,
            this.props.handlersArguments?.rowValue,
        );

        const value =
            this.props.value && Number.isNaN(Number(this.props.value))
                ? this.props.value
                : (computedUnitProperties?.scale ??
                  formatNumericValue({
                      screenId: this.props.screenId,
                      value: this.props.value,
                      scale: this.props.fieldProperties.scale,
                      rowValue: this.props.handlersArguments?.rowValue,
                  }));

        return (
            <CarbonWrapper
                {...this.props}
                className="e-aggregate-field"
                componentName="aggregate"
                componentRef={this.componentRef}
                fieldProperties={{
                    ...this.props.fieldProperties,
                    isReadOnly: true,
                }}
                handlersArguments={this.props.handlersArguments}
                value={value}
            >
                <noscript />
            </CarbonWrapper>
        );
    }
}

export const ConnectedAggregateComponent = connect(mapStateToProps(), mapDispatchToProps())(AggregateComponent);

export default ConnectedAggregateComponent;
