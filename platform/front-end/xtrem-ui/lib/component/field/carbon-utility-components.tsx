import type * as CarbonUtilityComponents from 'react';
import React from 'react';
import Icon from 'carbon-react/esm/components/icon';
import * as tokens from '@sage/design-tokens/js/base/common';

export function FieldLabel({
    label,
    children,
    htmlFor,
    className = '',
    errorMessage,
    infoMessage,
    warningMessage,
    id,
}: React.PropsWithChildren<{
    id?: string;
    label?: string;
    htmlFor?: string;
    className?: string;
    errorMessage?: string;
    infoMessage?: string;
    warningMessage?: string;
}>): React.ReactElement {
    return (
        <label
            id={id}
            data-element="label"
            className={`common-input__label ${className}`.trim()}
            data-testid="e-field-label"
            htmlFor={htmlFor}
        >
            {label}
            {children}
            {errorMessage && (
                <Icon
                    mr={8}
                    type="error"
                    tooltipMessage={errorMessage}
                    ariaLabel={errorMessage}
                    color={tokens.colorsSemanticNegative500}
                />
            )}

            {!errorMessage && !warningMessage && infoMessage && (
                <Icon
                    key="info"
                    className="e-icon-validation-errors"
                    ariaLabel={infoMessage}
                    fontSize="small"
                    color={tokens.colorsSemanticInfo500}
                    type="info"
                    role="tooltip"
                    tooltipMessage={infoMessage}
                />
            )}
            {!errorMessage && warningMessage && (
                <Icon
                    key="warning"
                    className="e-icon-validation-errors"
                    ariaLabel={warningMessage}
                    fontSize="small"
                    color={tokens.colorsSemanticCaution500}
                    type="warning"
                    role="tooltip"
                    tooltipMessage={warningMessage}
                />
            )}
        </label>
    );
}

export function HelperText({ helperText }: { helperText?: string }): React.ReactElement {
    return (
        <span data-element="help" className="common-input__help-text" data-testid="e-field-helper-text">
            {helperText || ' '}
        </span>
    );
}

export const getFieldElement = (
    className: string,
    element?: JSX.Element | string,
): CarbonUtilityComponents.ReactNode => {
    if (element) {
        return <span className={className}>{element}</span>;
    }
    return undefined;
};
