## Documentation - StepSequence
| name                   | optional | description                                                                                                                                                                                                                                    | type                                                                                                               | default                                                                                  |
| ---------------------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------- |
| access                 | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">AccessConfiguration</pre>                                                                   | <pre lang="javascript">{&nbsp;node:&nbsp;'<node>',&nbsp;bind:&nbsp;'<bind>'&nbsp;}</pre> |
| bind                   | true     | The GraphQL node that the field’s value is bound to. If not provided, the field name is used instead */ // eslint-disable-next-line @typescript-eslint/no-unused-vars                                                                          | <pre lang="javascript">ReferenceValueType<any></pre>                                                               | <pre lang="javascript">undefined</pre>                                                   |
| helperText             | true     | A text that will be displayed below the field                                                                                                                                                                                                  | <pre lang="javascript">string</pre>                                                                                | <pre lang="javascript">''</pre>                                                          |
| infoMessage            | true     | Indicate additional warning message, rendered as tooltip and blue border                                                                                                                                                                       | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;ContextNodeType,&nbsp;Dict<any>></pre>  | <pre lang="javascript">''</pre>                                                          |
| isDisabled             | true     | Whether the HTML element is disabled or not. Defaults to false The difference with readOnly is that disabled suggests that the field is not editable for some validation reason (e.g. a button which can't be clicked due to validation errors | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>             | <pre lang="javascript">false</pre>                                                       |
| isFullWidth            | true     | Whether the field spans all the parent width or not. Defaults to false                                                                                                                                                                         | <pre lang="javascript">boolean</pre>                                                                               | <pre lang="javascript">false</pre>                                                       |
| isHelperTextHidden     | true     | Whether the field helper text is hidden or not. Defaults to false                                                                                                                                                                              | <pre lang="javascript">boolean</pre>                                                                               | <pre lang="javascript">false</pre>                                                       |
| isHidden               | true     | Whether the HTML element is hidden or not. Defaults to false                                                                                                                                                                                   | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>             | <pre lang="javascript">false</pre>                                                       |
| isHiddenDesktop        | true     | Whether the element is hidden or not in desktop devices. Defaults to false                                                                                                                                                                     | <pre lang="javascript">boolean</pre>                                                                               | <pre lang="javascript">false</pre>                                                       |
| isHiddenMobile         | true     | Whether the element is hidden or not in mobile devices. Defaults to false                                                                                                                                                                      | <pre lang="javascript">boolean</pre>                                                                               | <pre lang="javascript">false</pre>                                                       |
| isMandatory            | true     | Whether is mandatory to provide a value for the field or not. Defaults to false                                                                                                                                                                | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;ContextNodeType,&nbsp;Dict<any>></pre> | <pre lang="javascript">false</pre>                                                       |
| isReadOnly             | true     | Whether the field value can only be read or not. Defaults to false The difference with disabled is that readOnly suggests that the field is never editable e.g. the id field of an object                                                      | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>             | <pre lang="javascript">false</pre>                                                       |
| isSortedAlphabetically | true     | Forces the options to be rendered in alphabetical order                                                                                                                                                                                        | <pre lang="javascript">boolean</pre>                                                                               | <pre lang="javascript">false</pre>                                                       |
| isTitleHidden          | true     | Whether the element title is hidden or not. Defaults to false                                                                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                               | <pre lang="javascript">false</pre>                                                       |
| isTransient            | true     | Whether the value is bound to a GraphQL node (transient = false) or not (transient = true). Defaults to false                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                               | <pre lang="javascript">false</pre>                                                       |
| isTransientInput       | true     | Whether the value is bound only to GraphQL mutations (isTransientInput = true) or not (isTransientInput = false). Defaults to false                                                                                                            | <pre lang="javascript">boolean</pre>                                                                               | <pre lang="javascript">false</pre>                                                       |
| isVertical             | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">boolean</pre>                                                                               | <pre lang="javascript">false</pre>                                                       |
| map                    | true     | Function that can be used to set/transform the component text                                                                                                                                                                                  | <pre lang="javascript">(this:&nbsp;CT,&nbsp;value?:&nbsp;any,&nbsp;rowValue?:&nbsp;any)&nbsp;=>&nbsp;string</pre>  | <pre lang="javascript">function(value,&nbsp;rowValue<br>)&nbsp;{<br><br>}</pre>          |
| optionType             | true     | The GraphQL node that the select options will be fetched from When using this property, the node must be an Enum                                                                                                                               | <pre lang="javascript">string</pre>                                                                                | <pre lang="javascript">''</pre>                                                          |
| options                | true     | The list of options available in the select field                                                                                                                                                                                              | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string[],&nbsp;any,&nbsp;Dict<any>></pre>            | <pre lang="javascript">[]</pre>                                                          |
| parent                 | true     | The container in which this component will render                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;P</pre>                                                        | <pre lang="javascript">undefined</pre>                                                   |
| size                   | true     | The field's vertical size, default is medium                                                                                                                                                                                                   | <pre lang="javascript">FieldWidth</pre>                                                                            | <pre lang="javascript">'medium'</pre>                                                    |
| title                  | true     | The title of the HTML element                                                                                                                                                                                                                  | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;any,&nbsp;Dict<any>></pre>              | <pre lang="javascript">''</pre>                                                          |
| warningMessage         | true     | Indicate additional information, rendered as tooltip and orange border                                                                                                                                                                         | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;ContextNodeType,&nbsp;Dict<any>></pre>  | <pre lang="javascript">''</pre>                                                          |
| width                  | true     | The width of the block relative to the section where it is place Must be a number between 1 and 12, both included                                                                                                                              | <pre lang="javascript">FieldWidth</pre>                                                                            | <pre lang="javascript">'medium'</pre>                                                    |