import type { Page } from '../../../..';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata } from '../../../../__tests__/test-helpers';
import { stepSequenceField } from '../step-sequence-decorator';
import type { StepSequenceDecoratorProperties } from '../step-sequence-types';

describe('File decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'stepSequenceField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should be able to set options', () => {
        stepSequenceField({ options: ['a', 'b', 'c'] })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: StepSequenceDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as StepSequenceDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.options).toEqual(['a', 'b', 'c']);
    });

    it('should be able to set statuses', () => {
        stepSequenceField({ statuses: { a: 'complete', b: 'current', c: 'incomplete' } })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: StepSequenceDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as StepSequenceDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.statuses).toEqual({ a: 'complete', b: 'current', c: 'incomplete' });
    });
});
