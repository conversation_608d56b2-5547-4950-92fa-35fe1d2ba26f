import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import type { StepSequenceProperties } from '../../../control-objects';
import { FieldKey } from '../../../types';
import { ConnectedStepSequenceComponent, StepSequenceComponent } from '../step-sequence-component';
import * as i18nService from '../../../../service/i18n-service';
import { render, waitFor } from '@testing-library/react';
import type { StepSequenceDecoratorProperties } from '../step-sequence-types';

describe('step sequence component', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: StepSequenceProperties;
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let localizeSpy: jest.SpyInstance<string, [string, string]> | null = null;

    beforeEach(() => {
        const state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        state.enumTypes.MyLocalizedEnum = ['Option1', 'Option2'];
        mockFieldProperties = {
            title: 'Test Step Sequence',
        };
        addFieldToState(FieldKey.StepSequence, state, screenId, 'test-step-sequence-field', mockFieldProperties, null);
        addFieldToState(
            FieldKey.StepSequence,
            state,
            screenId,
            'test-step-sequence-field-with-value',
            mockFieldProperties,
            'option2',
        );
        addFieldToState(
            FieldKey.StepSequence,
            state,
            screenId,
            'test-step-sequence-field-vertical-field',
            mockFieldProperties,
            null,
        );
        mockStore = getMockStore(state);
        localizeSpy = jest.spyOn(i18nService, 'localizeEnumMember').mockImplementation(() => 'Localized option');
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should render', async () => {
        mockFieldProperties.options = ['option1', 'option2'];
        const element = render(
            <Provider store={mockStore}>
                <ConnectedStepSequenceComponent screenId={screenId} elementId="test-step-sequence-field" />
            </Provider>,
        );
        await waitFor(() => {
            const label = element.queryByTestId('e-field-label');
            expect(label?.textContent).toBe('Test Step Sequence');
            const ol = element.baseElement.querySelector('ol');
            expect(ol?.querySelectorAll('li').length).toBe(2);
            expect(ol?.querySelectorAll('li')[0].textContent).toBe('1option1');
            expect(ol?.querySelectorAll('li')[1].textContent).toBe('2option2');
        });
    });

    it('should render with value', async () => {
        mockFieldProperties.options = ['option1', 'option2'];
        const element = render(
            <Provider store={mockStore}>
                <ConnectedStepSequenceComponent screenId={screenId} elementId="test-step-sequence-field-with-value" />
            </Provider>,
        );
        await waitFor(() => {
            const ol = element.baseElement.querySelector('ol');
            expect(ol?.querySelectorAll('li').length).toBe(2);
            expect(ol?.querySelectorAll('li')[0].textContent).toBe('Completeoption1');
            expect(ol?.querySelectorAll('li')[1].textContent).toBe('Current2option2');
        });
    });

    it('should be able to change satuses with options', async () => {
        const props: StepSequenceDecoratorProperties = {
            options: ['option1', 'option2'],
            title: 'Test Step Sequence',
        };
        const element = render(
            <StepSequenceComponent
                screenId={screenId}
                elementId="test-step-sequence-field-with-value"
                fieldProperties={props}
                locale="en-US"
                onFocus={() => {}}
                value="option2"
            />,
        );
        await waitFor(async () => {
            expect(
                (
                    (await element.findByTestId(
                        'e-step-sequence-field e-field-label-testStepSequence e-field-bind-test-step-sequence-field-with-value-option1-step',
                    )) as HTMLLIElement
                ).getAttribute('data-status'),
            ).toBe('complete');
            expect(
                (
                    (await element.findByTestId(
                        'e-step-sequence-field e-field-label-testStepSequence e-field-bind-test-step-sequence-field-with-value-option2-step',
                    )) as HTMLLIElement
                ).getAttribute('data-status'),
            ).toBe('current');
        });
        props.statuses = { option1: 'incomplete' };
        element.rerender(
            <StepSequenceComponent
                screenId={screenId}
                elementId="test-step-sequence-field-with-value"
                fieldProperties={props}
                locale="en-US"
                onFocus={() => {}}
                value="option2"
            />,
        );
        await waitFor(async () => {
            expect(
                (
                    (await element.findByTestId(
                        'e-step-sequence-field e-field-label-testStepSequence e-field-bind-test-step-sequence-field-with-value-option1-step',
                    )) as HTMLLIElement
                ).getAttribute('data-status'),
            ).toBe('incomplete');
            expect(
                (
                    (await element.findByTestId(
                        'e-step-sequence-field e-field-label-testStepSequence e-field-bind-test-step-sequence-field-with-value-option2-step',
                    )) as HTMLLIElement
                ).getAttribute('data-status'),
            ).toBe('current');
        });
    });

    it('should render with title hidden', async () => {
        mockFieldProperties.options = ['option1', 'option2'];
        mockFieldProperties.isTitleHidden = true;
        const element = render(
            <Provider store={mockStore}>
                <ConnectedStepSequenceComponent screenId={screenId} elementId="test-step-sequence-field" />
            </Provider>,
        );
        await waitFor(() => {
            const label = element.queryByTestId('e-field-label');
            expect(label).toBeNull();
        });
    });

    it('should render with helper text', async () => {
        mockFieldProperties.options = ['option1', 'option2'];
        mockFieldProperties.helperText = 'test helper text';
        const element = render(
            <Provider store={mockStore}>
                <ConnectedStepSequenceComponent screenId={screenId} elementId="test-step-sequence-field-with-value" />
            </Provider>,
        );
        await waitFor(() => {
            const helperText = element.queryByTestId('e-field-helper-text');
            expect(helperText?.textContent).toBe('test helper text');
        });
    });

    it('should render vertical', async () => {
        mockFieldProperties.options = ['option1', 'option2'];
        mockFieldProperties.isVertical = true;
        const element = render(
            <Provider store={mockStore}>
                <ConnectedStepSequenceComponent screenId={screenId} elementId="test-step-sequence-field-with-value" />
            </Provider>,
        );

        await waitFor(() => {
            const ol = element.baseElement.querySelector('ol');
            expect(ol?.getAttribute('orientation')).toBe('vertical');
        });
    });

    it('should render with callback options', async () => {
        mockFieldProperties.options = () => {
            return ['callback_option1', 'callback_option2'];
        };
        const element = render(
            <Provider store={mockStore}>
                <ConnectedStepSequenceComponent screenId={screenId} elementId="test-step-sequence-field" />
            </Provider>,
        );
        await waitFor(() => {
            const ol = element.baseElement.querySelector('ol');
            expect(ol?.querySelectorAll('li').length).toBe(2);
            expect(ol?.querySelectorAll('li')[0].textContent).toBe('1callback_option1');
            expect(ol?.querySelectorAll('li')[1].textContent).toBe('2callback_option2');
        });
    });

    it('should render with options types', async () => {
        mockFieldProperties.optionType = '@any/any-package-name/MyLocalizedEnum';
        expect(localizeSpy).not.toHaveBeenCalled();
        const element = render(
            <Provider store={mockStore}>
                <ConnectedStepSequenceComponent screenId={screenId} elementId="test-step-sequence-field" />
            </Provider>,
        );
        expect(localizeSpy).toHaveBeenCalled();
        expect(localizeSpy).toHaveBeenCalledWith('@any/any-package-name/MyLocalizedEnum', 'Option1');
        expect(localizeSpy).toHaveBeenCalledWith('@any/any-package-name/MyLocalizedEnum', 'Option2');
        await waitFor(() => {
            const ol = element.baseElement.querySelector('ol');
            expect(ol?.querySelectorAll('li').length).toBe(2);
            expect(ol?.querySelectorAll('li')[0].textContent).toBe('1Localized option');
            expect(ol?.querySelectorAll('li')[1].textContent).toBe('2Localized option');
        });
    });

    it('should be able to change satuses with option enums', async () => {
        const props: StepSequenceDecoratorProperties = {
            optionType: '@any/any-package-name/MyLocalizedEnum',
            title: 'Test Step Sequence',
        };
        const element = render(
            <StepSequenceComponent
                screenId={screenId}
                elementId="test-step-sequence-field-with-value"
                fieldProperties={props}
                locale="en-US"
                onFocus={() => {}}
                value="Option2"
            />,
        );
        await waitFor(async () => {
            expect(
                (
                    (await element.findByTestId(
                        'e-step-sequence-field e-field-label-testStepSequence e-field-bind-test-step-sequence-field-with-value-Option1-step',
                    )) as HTMLLIElement
                ).getAttribute('data-status'),
            ).toBe('complete');
            expect(
                (
                    (await element.findByTestId(
                        'e-step-sequence-field e-field-label-testStepSequence e-field-bind-test-step-sequence-field-with-value-Option2-step',
                    )) as HTMLLIElement
                ).getAttribute('data-status'),
            ).toBe('current');
        });
        props.statuses = { Option1: 'incomplete' };
        element.rerender(
            <StepSequenceComponent
                screenId={screenId}
                elementId="test-step-sequence-field-with-value"
                fieldProperties={props}
                locale="en-US"
                onFocus={() => {}}
                value="Option2"
            />,
        );
        await waitFor(async () => {
            expect(
                (
                    (await element.findByTestId(
                        'e-step-sequence-field e-field-label-testStepSequence e-field-bind-test-step-sequence-field-with-value-Option1-step',
                    )) as HTMLLIElement
                ).getAttribute('data-status'),
            ).toBe('incomplete');
            expect(
                (
                    (await element.findByTestId(
                        'e-step-sequence-field e-field-label-testStepSequence e-field-bind-test-step-sequence-field-with-value-Option2-step',
                    )) as HTMLLIElement
                ).getAttribute('data-status'),
            ).toBe('current');
        });
    });
});
