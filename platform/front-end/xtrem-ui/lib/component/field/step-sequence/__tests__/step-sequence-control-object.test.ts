import type { <PERSON><PERSON><PERSON> } from '../../../types';
import type { StepSequenceProperties } from '../../../control-objects';
import { StepSequenceControlObject } from '../../../control-objects';
import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';

describe('Icon Field', () => {
    let stepSequenceField: StepSequenceControlObject;
    const fieldProperties: StepSequenceProperties = {
        title: 'TEST_FIELD_TITLE',
        isHidden: true,
        isDisabled: true,
    };
    beforeEach(() => {
        stepSequenceField = buildControlObject<FieldKey.StepSequence>(StepSequenceControlObject, {
            fieldValue: 'step',
            fieldProperties,
        });
    });

    it('should be able to get value', () => {
        expect(stepSequenceField.value).toEqual('step');
    });

    it('should be able to get & set field statuses', () => {
        expect(stepSequenceField.statuses).toBe(undefined);
        stepSequenceField.statuses = { a: 'complete', b: 'current', c: 'incomplete' };
        expect(fieldProperties.statuses).toEqual({ a: 'complete', b: 'current', c: 'incomplete' });
    });
});
