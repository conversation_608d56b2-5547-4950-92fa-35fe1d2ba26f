import type { ScreenExtension } from '../../../types';
import type { ScreenBase } from '../../../service/screen-base';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import type { FieldComponentProps, FieldKey } from '../../types';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import type { StepSequenceStatus } from './step-sequence-types';
import { StepSequenceProperties } from './step-sequence-types';
import type { Dict } from '@sage/xtrem-shared';

export { StepSequenceProperties };

/**
 * [Field]{@link EditableFieldControlObject} that holds a step sequence
 */
export class StepSequenceControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.StepSequence,
    FieldComponentProps<FieldKey.StepSequence>
> {
    @FieldControlObjectResolvedProperty<StepSequenceProperties<CT>, StepSequenceControlObject<CT>>()
    /** Steps to be displayed in the select element */
    options?: string[];

    @FieldControlObjectResolvedProperty<StepSequenceProperties<CT>, StepSequenceControlObject<CT>>()
    /** An optional dicstionary of statuses. It takes strings as keys (which map to entries in either "options" or "optionType")
     * and 'current' | 'complete' | 'incomplete' as values */
    statuses?: Dict<StepSequenceStatus>;

    /**
     * The GraphQL node that the steps will be fetched from.
     * When using this property, the node must be an Enum
     */

    get optionType(): string | undefined {
        return this.getUiComponentProperty('optionType');
    }
}
