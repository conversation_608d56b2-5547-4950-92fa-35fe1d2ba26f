import * as React from 'react';
import { connect } from 'react-redux';
import { mapDispatchToProps, mapStateToProps, ReadonlyFieldBaseComponent } from '../field-base-component';
import { StepSequence, StepSequenceItem } from 'carbon-react/esm/components/step-sequence';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { localize, localizeEnumMember } from '../../../service/i18n-service';
import { createSelectItemFromOption } from '../select/select-utils';
import type { SelectItem } from '../../ui/select/select-component';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { getStore } from '../../../redux';
import { schemaTypeNameFromNodeName } from '../../../utils/transformers';
import type { StepSequenceDecoratorProperties } from './step-sequence-types';
import { getDataTestIdAttribute } from '../../../utils/dom';

export class StepSequenceComponent extends ReadonlyFieldBaseComponent<StepSequenceDecoratorProperties, any> {
    getStatus = ({
        index,
        steps,
        step,
    }: {
        index: number;
        steps: SelectItem[];
        step: SelectItem;
    }): 'current' | 'complete' | 'incomplete' => {
        const status = this.props.fieldProperties.statuses?.[String(step.value)];
        if (status) {
            return status;
        }
        const value = this.getValue();
        const activeStep = steps.findIndex(s => s.value === value);

        if (index === activeStep) {
            return 'current';
        }
        if (index < activeStep) {
            return 'complete';
        }
        return 'incomplete';
    };

    getSteps = (): SelectItem[] => {
        if (this.props.fieldProperties.optionType) {
            const enumOptions =
                getStore().getState().enumTypes[schemaTypeNameFromNodeName(this.props.fieldProperties.optionType)] ||
                [];
            return enumOptions.map((e: string) => ({
                id: e,
                value: e,
                displayedAs: localizeEnumMember(this.props.fieldProperties.optionType as string, e),
            }));
        }

        if (this.props.fieldProperties.options) {
            const options: string[] = resolveByValue({
                propertyValue: this.props.fieldProperties.options,
                screenId: this.props.screenId,
                skipHexFormat: true,
                fieldValue: this.props.value,
                rowValue: undefined,
            });

            return options.map(option => {
                const mapped = this.props.fieldProperties.map?.apply({}, [option]) || undefined;
                return createSelectItemFromOption(option, this.props, mapped);
            });
        }
        return [];
    };

    localizeCount = (stepNum: number, totalNum: number): string =>
        localize('@sage/xtrem-ui/step-sequence-item-aria-count', 'Step {{0}} of {{1}}', [stepNum, totalNum]);

    render(): React.ReactNode {
        const title = this.getTitle();
        const helperText = this.props.fieldProperties.helperText;
        const hasTitle = !this.props.fieldProperties.isTitleHidden && title !== '' && title !== undefined;
        const hasHelperText =
            !this.props.fieldProperties.isHelperTextHidden && helperText !== undefined && helperText !== '';
        const orientation = this.props.fieldProperties.isVertical ? 'vertical' : 'horizontal';
        const hiddenCompleteLabel = localize('@sage/xtrem-ui/step-sequence-item-aria-complete', 'Complete');
        const hiddenCurrentLabel = localize('@sage/xtrem-ui/step-sequence-item-aria-current', 'Current');
        const steps = this.getSteps();
        const testId = getDataTestIdAttribute('step-sequence', title, this.props.elementId);
        return (
            <div {...this.getBaseAttributesDivWrapper('step-sequence', 'e-step-sequence-field')}>
                {hasTitle && <FieldLabel label={title} />}
                <StepSequence orientation={orientation} data-testid={testId}>
                    {steps.map((step, index) => {
                        const status = this.getStatus({ index, steps, step });
                        return (
                            <StepSequenceItem
                                key={`stepsequenceitem_${step.id}`}
                                data-status={status}
                                data-testid={`${testId}-${step.id}-step`}
                                aria-label={this.localizeCount(index + 1, steps.length)}
                                hiddenCompleteLabel={hiddenCompleteLabel}
                                hiddenCurrentLabel={hiddenCurrentLabel}
                                status={status}
                                indicator={String(index + 1)}
                            >
                                {step.displayedAs}
                            </StepSequenceItem>
                        );
                    })}
                </StepSequence>
                {hasHelperText && <HelperText helperText={this.props.fieldProperties.helperText} />}
            </div>
        );
    }
}

export const ConnectedStepSequenceComponent = connect(mapStateToProps(), mapDispatchToProps())(StepSequenceComponent);

export default ConnectedStepSequenceComponent;
