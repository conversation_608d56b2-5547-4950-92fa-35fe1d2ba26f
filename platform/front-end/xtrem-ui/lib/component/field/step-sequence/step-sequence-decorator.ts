import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { StepSequenceControlObject } from './step-sequence-control-object';
import type { ScreenExtension } from '../../../types';
import type { Extend } from '../../../service/page-extension';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import type { StepSequenceDecoratorProperties } from './step-sequence-types';
import { addOptionTypeToProperties } from '../../../utils/data-type-utils';
import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';

export { StepSequenceDecoratorProperties };

class StepSequenceDecorator extends AbstractFieldDecorator<FieldKey.StepSequence> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = StepSequenceControlObject;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<StepSequenceDecoratorProperties> {
        const properties: Partial<StepSequenceDecoratorProperties> = {};
        addOptionTypeToProperties({ propertyDetails, dataType, properties });
        return properties;
    }
}

export function stepSequenceField<T extends ScreenExtension<T>>(
    properties: StepSequenceDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.StepSequence>(
        properties,
        StepSequenceDecorator,
        FieldKey.StepSequence,
    );
}

export function stepSequenceFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<StepSequenceDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.StepSequence>(properties);
}
