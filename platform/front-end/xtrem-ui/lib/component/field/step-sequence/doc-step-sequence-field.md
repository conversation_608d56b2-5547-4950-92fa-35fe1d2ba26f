PATH: XTREEM/UI+Field+Widgets/Step+Sequence+Field

## Introduction

The step sequence field represents the display of a step sequence indicator.

## Example

```ts
@ui.decorators.stepSequenceField<Page>({
    parent() {
        return this.block;
    },
    options: ['Create', 'Approve', 'Order', 'Receive', 'Invoice'],
})
field: ui.fields.StepSequence;
```

## Decorator Properties

### Display Properties

- **helperText**: The helper text displayed below the field. This property is automatically picked up by the i18n engine and externalized.
- **isFullWidth**: Determines whether the field takes up the full width of the grid or not.
- **isHelperTextHidden**: Determines whether the field's helper text is displayed or not.
- **isHidden**: Determines whether the field is displayed or not.
- **isTitleHidden**: Determines whether the field's title is displayed or not.
- **isVertical**: Sets a vertical orientation for the step sequence.
- **size**: Size the field should be rendered in. The options are "small", "medium" and "large". It uses all horizontal width by default.
- **statuses**: An optional dicstionary of statuses. It takes strings as keys (which map to entries in either `options` or `optionType`) and `'current' | 'complete' | 'incomplete'` as values.
- **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. It is automatically picked up by the i18n engine and externalized for translation.

### Binding Properties

-   **map()**: Custom callback to transform the field's list of steps.
-   **options**: List of steps available to select from. It can also be defined as callback function that returns a string array.
-   **optionType**: GraphQL enum type. The enum members are used as steps and they are displayed in a localized form.

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/StepSequence).
