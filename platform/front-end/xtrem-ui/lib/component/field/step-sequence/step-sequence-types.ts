import type { ScreenBase } from '../../../service/screen-base';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { HasOptions, HasOptionType, HasParent, Mappable, Sizable } from '../traits';
import type { BlockControlObject } from '../../container/block/block-control-object';
import type { Dict } from '@sage/xtrem-shared';
import type { StepSequenceItemProps } from 'carbon-react/esm/components/step-sequence';

export type StepSequenceStatus = Exclude<StepSequenceItemProps['status'], undefined>;

export interface StepSequenceProperties<CT extends ScreenBase = ScreenBase>
    extends EditableFieldProperties<CT>,
        HasOptions<CT>,
        HasOptionType,
        Mappable<CT> {
    isVertical?: boolean;
    statuses?: Dict<StepSequenceStatus>;
}

export interface StepSequenceDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<StepSequenceProperties<CT>, '_controlObjectType'>,
        HasParent<CT, BlockControlObject<CT>>,
        Sizable {}
