import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { FieldComponentExternalProperties } from '../field-base-component-types';

const ConnectedStepSequenceComponent = React.lazy(() => import('./step-sequence-component'));

export function AsyncConnectedStepSequenceComponent(props: FieldComponentExternalProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <ConnectedStepSequenceComponent {...props} />
        </React.Suspense>
    );
}

const StepSequenceComponent = React.lazy(() =>
    import('./step-sequence-component').then(c => ({ default: c.StepSequenceComponent })),
);

export function AsyncStepSequenceComponent(props: any): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader size="small" />}>
            <StepSequenceComponent {...props} />
        </React.Suspense>
    );
}
