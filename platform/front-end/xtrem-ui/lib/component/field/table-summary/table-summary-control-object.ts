/**
 * @packageDocumentation
 * @module root
 * */
import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { TableSummaryDecoratorProperties } from '../../decorator-properties';
import type { GridNestedFieldTypes } from '../../nested-fields';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { <PERSON>Key, PartialCollectionValueWithIds } from '../../types';
import { CollectionValueControlObject } from '../collection-value-field';
import type { InternalTableSummaryProperties } from './table-summary-types';

/**
 * [Field]{@link ReadonlyFieldControlObject} that holds a set of values of any type. It can contain nested fields
 */
export class TableSummaryControlObject<
    NestedRecordType extends ClientNode = any,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends CollectionValueControlObject<
    FieldKey.TableSummary,
    NestedRecordType,
    CT,
    GridNestedFieldTypes,
    InternalTableSummaryProperties<CT, NestedRecordType>
> {
    static readonly defaultUiProperties: Partial<TableSummaryDecoratorProperties> = {
        ...ReadonlyFieldControlObject.defaultUiProperties,
    };

    /** Return records that the user just added to this table and not yet known by the server. */
    getNewRecords(): PartialCollectionValueWithIds<NestedRecordType>[] {
        this.ensureFieldHasValue();
        const value = this._getValue();
        if (!value) {
            return [];
        }

        return value.getNewRecords();
    }

    get node(): string {
        return String(this.getUiComponentProperty('node'));
    }

    /** Redraws the current table view, this function can be useful when values are updated that are used in property callbacks. */
    redraw(columnBind?: string): Promise<void> {
        return this._redraw(columnBind);
    }
}
