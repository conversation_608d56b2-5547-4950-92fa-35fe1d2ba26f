import { getMockPageDefinition, getMockState, getMockStore, renderWithRedux } from '../../../../__tests__/test-helpers';

import React from 'react';
import {
    CheckboxControlObject,
    DateControlObject,
    ImageControlObject,
    LinkControlObject,
    NumericControlObject,
    ProgressControlObject,
    ReferenceControlObject,
    SelectControlObject,
    TextControlObject,
} from '../../../control-objects';
import { CollectionValue } from '../../../../service/collection-data-service';
import type { FieldDecoratorProps, FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';
import { cleanup } from '@testing-library/react';
import { ConnectedTableSummaryComponent } from '../table-summary-component';
import type * as xtremRedux from '../../../../redux';
import type { MockStoreEnhanced } from 'redux-mock-store';

const screenId = 'TestPage';
const tableSummaryElementId = 'tableSummaryElementId';
// const tableSummaryElementIdEmpty = 'tableSummaryElementIdEmpty';

const defaultFieldValueData = () => {
    return [
        {
            _id: 'id1',
            column1: 'Test Text Value 1',
            column2: 1,
            column3: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
            column4: 'option1' as 'option1',
            column5: 'product1',
            column6: '2020-07-14',
            column7: 50,
            column8: 'image',
            column9: true,
        },
        {
            _id: 'id2',
            column1: 'Test Text Value 2',
            column2: 2,
            column3: {
                _id: '_id2',
                code: 'Test Code 2',
                description: 'Test Description 2',
            },
            column4: 'option2' as 'option2',
            column5: 'product2',
            column6: '2020-07-15',
            column7: 60,
            column8: 'image2',
            column9: false,
        },
    ];
};

const defaultFieldProperties = (): FieldDecoratorProps<FieldKey.Table> => ({
    title: 'Table Test Title',
    pageSize: 20,
    node: 'Sample',
    isDisabled: false,
    columns: [
        {
            defaultUiProperties: { ...TextControlObject.defaultUiProperties, bind: 'column1' },
            properties: {
                bind: 'column1',
                title: 'Column 1',
            },
            type: FieldKey.Text,
        },
        {
            defaultUiProperties: { ...NumericControlObject.defaultUiProperties, bind: 'column2' },
            properties: {
                bind: 'column2',
                title: 'Column 2',
            },
            type: FieldKey.Numeric,
        },
        {
            defaultUiProperties: { ...ReferenceControlObject.defaultUiProperties, bind: 'column3' },
            properties: {
                bind: 'column3',
                title: 'Column 3',
                node: '@sage/xtrem-show-case/Reference',
                valueField: 'code',
                helperTextField: 'description',
                minLookupCharacters: 0,
            },
            type: FieldKey.Reference,
        },
        {
            defaultUiProperties: { ...SelectControlObject.defaultUiProperties, bind: 'column4' },
            properties: {
                bind: 'column4',
                title: 'Column 4',
                options: ['option1', 'option2'],
            },
            type: FieldKey.Select,
        },
        {
            defaultUiProperties: { ...LinkControlObject.defaultUiProperties, bind: 'column5' },
            properties: {
                bind: 'column5',
                title: 'Column 5',
                map(fieldValue) {
                    if (fieldValue) {
                        return `http://${(fieldValue as string).replace(/-|_|\s/g, '').toLowerCase()}.sage.com`;
                    }
                    return '';
                },
            },
            type: FieldKey.Link,
        },
        {
            defaultUiProperties: { ...DateControlObject.defaultUiProperties, bind: 'column6' },
            properties: {
                bind: 'column6',
                title: 'Column 6',
            },
            type: FieldKey.Date,
        },
        {
            defaultUiProperties: { ...ProgressControlObject.defaultUiProperties, bind: 'column7' },
            properties: {
                bind: 'column7',
                title: 'Column 7',
            },
            type: FieldKey.Progress,
        },
        {
            defaultUiProperties: { ...ImageControlObject.defaultUiProperties, bind: 'column8' },
            properties: {
                bind: 'column8',
                title: 'Column 8',
            },
            type: FieldKey.Image,
        },
        {
            defaultUiProperties: { ...CheckboxControlObject.defaultUiProperties, bind: 'column9' },
            properties: {
                bind: 'column9',
                title: 'Column 9',
            },
            type: FieldKey.Checkbox,
        },
    ],
});

const defaultFieldValue = (data = defaultFieldValueData()): FieldInternalValue<FieldKey.TableSummary> => {
    const result = {
        data,
        pageInfo: {
            startCursor: 'startCursor',
            endCursor: 'endCursor',
            hasPreviousPage: false,
            hasNextPage: false,
        },
    };

    return new CollectionValue<any>({
        screenId,
        elementId: tableSummaryElementId,
        isTransient: false,
        hasNextPage: result.pageInfo.hasNextPage,
        orderBy: [{}],
        columnDefinitions: [[]],
        nodeTypes: {},
        nodes: ['@sage/xtrem-test/AnyNode'],
        filter: [undefined],
        initialValues: result.data,
        locale: 'en-US',
    });
};

describe('table summary', () => {
    let value: CollectionValue;
    let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState, {}>;
    let mockState: xtremRedux.XtremAppState;

    beforeEach(() => {
        value = defaultFieldValue();
        mockState = getMockState();

        mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        mockStore = getMockStore(mockState);
    });

    afterEach(() => {
        cleanup();
    });

    it('should render with value', () => {
        renderWithRedux<FieldKey.TableSummary, any>(
            <ConnectedTableSummaryComponent
                screenId={screenId}
                elementId={tableSummaryElementId}
                item={{ $bind: tableSummaryElementId }}
            />,
            {
                mockStore,
                initialState: mockState,
                fieldType: FieldKey.TableSummary,
                fieldValue: value,
                fieldProperties: defaultFieldProperties(),
                elementId: tableSummaryElementId,
                screenId,
            },
        );
    });

    it('should render without a value', () => {
        renderWithRedux<FieldKey.TableSummary, any>(
            <ConnectedTableSummaryComponent
                screenId={screenId}
                elementId={tableSummaryElementId}
                item={{ $bind: tableSummaryElementId }}
            />,
            {
                mockStore,
                initialState: mockState,
                fieldType: FieldKey.TableSummary,
                fieldValue: null,
                fieldProperties: defaultFieldProperties(),
                elementId: tableSummaryElementId,
                screenId,
            },
        );
    });
});
