import { CollectionValue } from '../../../../service/collection-data-service';
import { CollectionFieldTypes } from '../../../../service/collection-data-types';
import * as graphQlService from '../../../../service/graphql-service';
import type { GraphQLFilter } from '../../../../service/graphql-utils';
import type { Page } from '../../../../service/page';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import { TableSummary as TableSummaryControlObject } from '../../../control-objects';
import type { TableSummaryDecoratorProperties } from '../../../decorators';
import * as nestedFields from '../../../nested-fields';
import type { NestedField, NestedFieldTypes } from '../../../nested-fields';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';

type NodeType = {
    _id: string;
    field1: string;
};

describe('Table Summary Field', () => {
    const screenId = 'TestPage';
    const elementId = 'test';
    const tableValue = [{ _id: 'testId' }];
    let value: CollectionValue;
    let tableSummaryFieldControlObject: TableSummaryControlObject;
    const properties: TableSummaryDecoratorProperties = {
        title: 'TEST_FIELD_TITLE',
        isHidden: true,
        isDisabled: true,
        columns: [],
    };

    let uiComponentProperties: any = {};
    describe('properties and values', () => {
        beforeEach(() => {
            uiComponentProperties = { ...properties };
            value = new CollectionValue({
                screenId,
                elementId,
                isTransient: false,
                hasNextPage: false,
                orderBy: [{}],
                columnDefinitions: [
                    [
                        nestedFields.text<any, any>({ bind: '_id' }),
                        nestedFields.text<any, any>({ bind: 'anyField' }),
                        nestedFields.numeric({ bind: 'someOtherField' }),
                    ],
                ],
                nodeTypes: {},
                nodes: ['@sage/xtrem-test/AnyNode'],
                filter: [undefined],
                initialValues: tableValue,
                fieldType: CollectionFieldTypes.DESKTOP_TABLE,
            });

            tableSummaryFieldControlObject = createFieldControlObject<FieldKey.TableSummary>(
                FieldKey.TableSummary,
                screenId,
                TableSummaryControlObject,
                elementId,
                value,
                properties,
                {
                    setUiComponentProperties: (_: string, __: string, props: any) => {
                        uiComponentProperties = { ...props };
                    },
                    getUiComponentProperties: () => uiComponentProperties,
                },
            );
        });

        it('getting field value', () => {
            expect(tableSummaryFieldControlObject.value).toEqual(tableValue);
        });

        describe('setting and getting updated properties', () => {
            const testTableSummaryProps = (
                tableSummaryProp: string,
                tableSummaryPropValue: string | boolean | number | Array<NestedField<Page, NestedFieldTypes> | string>,
            ) => {
                expect(tableSummaryFieldControlObject[tableSummaryProp]).not.toEqual(tableSummaryPropValue);
                tableSummaryFieldControlObject[tableSummaryProp] = tableSummaryPropValue;
                expect(tableSummaryFieldControlObject[tableSummaryProp]).toEqual(tableSummaryPropValue);
            };

            it('should set the title', () => {
                testTableSummaryProps('title', 'Test Title');
            });

            it('should set nested field and its properties', () => {
                const testFixture = [
                    nestedFields.text<any, NodeType>({
                        bind: 'field1',
                        isHiddenMobile: true,
                        isHiddenDesktop: true,
                    }),
                ];
                testTableSummaryProps('columns', testFixture);
            });

            it('should set the canFilter columns option', () => {
                testTableSummaryProps('canFilter', true);
            });

            it('should set the canResizeColumns columns option', () => {
                testTableSummaryProps('canResizeColumns', true);
            });

            it('should set sortColumns option', () => {
                testTableSummaryProps('sortColumns', 0);
            });

            it('should set the canSelect rows option', () => {
                testTableSummaryProps('canSelect', true);
            });

            it('should set the selected items', () => {
                testTableSummaryProps('selectedRecords', ['1', '2', '3']);
            });

            it('should set the canUserHideColumns columns option', () => {
                testTableSummaryProps('canUserHideColumns', false);
            });

            it('should select an item', () => {
                expect(tableSummaryFieldControlObject.selectedRecords).toEqual([]);
                tableSummaryFieldControlObject.selectRecord('4');
                expect(tableSummaryFieldControlObject.selectedRecords).toEqual(['4']);
                tableSummaryFieldControlObject.selectRecord('5');
                expect(tableSummaryFieldControlObject.selectedRecords).toEqual(['4', '5']);
                tableSummaryFieldControlObject.selectRecord('5');
                expect(tableSummaryFieldControlObject.selectedRecords).toEqual(['4', '5']);
            });

            it('should select an item and create a new array, not mutate the existing selection array', () => {
                const originalSelectedRecords = ['1', '2'];
                tableSummaryFieldControlObject = createFieldControlObject<FieldKey.TableSummary>(
                    FieldKey.TableSummary,
                    screenId,
                    TableSummaryControlObject,
                    elementId,
                    value,
                    { ...properties, selectedRecords: originalSelectedRecords },
                );

                expect(tableSummaryFieldControlObject.selectedRecords).toEqual(originalSelectedRecords);
                expect(tableSummaryFieldControlObject.selectedRecords).not.toBe(originalSelectedRecords);

                tableSummaryFieldControlObject.selectRecord('3');
                expect(tableSummaryFieldControlObject.selectedRecords).not.toEqual(originalSelectedRecords);
            });

            it('should unselect an item', () => {
                expect(tableSummaryFieldControlObject.selectedRecords).toEqual([]);
                tableSummaryFieldControlObject.unselectRecord('3');
                expect(tableSummaryFieldControlObject.selectedRecords).toEqual([]);
                tableSummaryFieldControlObject.selectedRecords = ['2', '3', '4'];
                expect(tableSummaryFieldControlObject.selectedRecords).toEqual(['2', '3', '4']);
                tableSummaryFieldControlObject.unselectRecord('3');
                expect(tableSummaryFieldControlObject.selectedRecords).toEqual(['2', '4']);
                tableSummaryFieldControlObject.unselectRecord('3');
                expect(tableSummaryFieldControlObject.selectedRecords).toEqual(['2', '4']);
                tableSummaryFieldControlObject.unselectRecord('2');
                expect(tableSummaryFieldControlObject.selectedRecords).toEqual(['4']);
            });

            it('should unselect an item and create a new array, not mutate the existing selection array', () => {
                const originalSelectedRecords = ['1', '2'];
                tableSummaryFieldControlObject = createFieldControlObject<FieldKey.TableSummary>(
                    FieldKey.TableSummary,
                    screenId,
                    TableSummaryControlObject,
                    elementId,
                    value,
                    { ...properties, selectedRecords: originalSelectedRecords },
                );

                expect(tableSummaryFieldControlObject.selectedRecords).toEqual(originalSelectedRecords);
                expect(tableSummaryFieldControlObject.selectedRecords).not.toBe(originalSelectedRecords);

                tableSummaryFieldControlObject.unselectRecord('2');
                expect(tableSummaryFieldControlObject.selectedRecords).not.toEqual(originalSelectedRecords);
            });

            it('should unselect all items', () => {
                tableSummaryFieldControlObject.selectedRecords = ['2', '3', '4'];
                tableSummaryFieldControlObject.unselectAllRecords();
                expect(tableSummaryFieldControlObject.selectedRecords).toEqual([]);
            });
        });

        describe('get and set value of the table', () => {
            beforeEach(() => {
                value = new CollectionValue({
                    screenId,
                    elementId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                            nestedFields.numeric({ bind: 'someOtherField' }),
                        ],
                    ],
                    nodeTypes: {},
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', anyField: 'test', someOtherField: 4 },
                        { _id: '2', anyField: 'test string 2', someOtherField: -5 },
                        { _id: '3', anyField: 'test aasd', someOtherField: 32432 },
                    ],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                });

                tableSummaryFieldControlObject = createFieldControlObject<FieldKey.TableSummary>(
                    FieldKey.TableSummary,
                    screenId,
                    TableSummaryControlObject,
                    elementId,
                    value,
                    properties,
                );
            });

            afterEach(() => {
                jest.resetAllMocks();
            });

            it('should get a row value when the getRecordValue() is called', () => {
                const result = tableSummaryFieldControlObject.getRecordValue('2')!;
                expect(result._id).toEqual('2');
                expect(result.anyField).toEqual('test string 2');
                expect(result.someOtherField).toEqual(-5);
                expect(Object.keys(result)).toEqual(['_id', 'anyField', 'someOtherField']);
            });

            it('getRecordByFieldValue() should return an object without any metadata if found', () => {
                const result = tableSummaryFieldControlObject.getRecordByFieldValue('someOtherField', -5);
                expect(result!._id).toEqual('2');
                expect(Object.keys(result!)).toEqual(['_id', 'anyField', 'someOtherField']);
            });

            it('addRecord should set the table value if it is empty', () => {
                tableSummaryFieldControlObject = createFieldControlObject<FieldKey.TableSummary>(
                    FieldKey.TableSummary,
                    screenId,
                    TableSummaryControlObject,
                    elementId,
                    null,
                    properties,
                );

                expect(tableSummaryFieldControlObject.value).toEqual([]);
                expect(tableSummaryFieldControlObject.value).toHaveLength(0);
                const result = tableSummaryFieldControlObject.addRecord({
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(result).toStrictEqual({
                    _id: '-1',
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(tableSummaryFieldControlObject.value).toHaveLength(1);
            });

            it('addRecord should add record to the DB', () => {
                expect(tableSummaryFieldControlObject.value).toHaveLength(3);
                const result = tableSummaryFieldControlObject.addRecord({
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(result).toStrictEqual({
                    _id: '-1',
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(tableSummaryFieldControlObject.value).toHaveLength(4);
            });

            it('getRecordValue should return addRecord value without any internal property', () => {
                tableSummaryFieldControlObject.addRecord({
                    anyField: 'new record',
                    someOtherField: 123,
                });

                expect(tableSummaryFieldControlObject.getRecordValue('-1')).toEqual({
                    anyField: 'new record',
                    someOtherField: 123,
                    _id: '-1',
                });
            });

            it('getRecordValue should return addRecord value with internal properties', () => {
                tableSummaryFieldControlObject.addRecord({
                    anyField: 'new record',
                    someOtherField: 123,
                });

                expect(tableSummaryFieldControlObject.getInternalRowValue('-1')).toEqual({
                    anyField: 'new record',
                    someOtherField: 123,
                    _id: '-1',
                    $loki: 4,
                    __path: '0,-1',
                    __action: 'create',
                    __dirtyColumns: new Set(),
                    __compositeKey: '-1.0',
                    __dirty: true,
                });
            });

            describe('addRecordWithDefaults', () => {
                let fetchNestedDefaultValuesMock;

                beforeEach(() => {
                    fetchNestedDefaultValuesMock = jest
                        .spyOn(graphQlService, 'fetchNestedDefaultValues')
                        .mockReturnValue(
                            Promise.resolve({
                                nestedDefaults: {
                                    anyField: 'new record',
                                    someOtherField: 123,
                                },
                            }),
                        );
                });

                it('addRecordWithDefaults should set the table value if it is empty', async () => {
                    tableSummaryFieldControlObject = createFieldControlObject<FieldKey.TableSummary>(
                        FieldKey.TableSummary,
                        screenId,
                        TableSummaryControlObject,
                        elementId,
                        null,
                        properties,
                    );

                    expect(tableSummaryFieldControlObject.value).toEqual([]);
                    expect(tableSummaryFieldControlObject.value).toHaveLength(0);
                    const result = await tableSummaryFieldControlObject.addRecordWithDefaults();

                    expect(tableSummaryFieldControlObject.value).toHaveLength(1);
                    expect(result).toStrictEqual({
                        _id: '-1',
                        anyField: 'new record',
                        someOtherField: 123,
                    });
                    expect(tableSummaryFieldControlObject.value).toHaveLength(1);

                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledTimes(1);
                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledWith({ screenId, elementId });
                });

                it('addRecordWithDefaults should add record to the DB', async () => {
                    expect(tableSummaryFieldControlObject.value).toHaveLength(3);
                    const result = await tableSummaryFieldControlObject.addRecordWithDefaults();
                    expect(result).toStrictEqual({
                        _id: '-1',
                        anyField: 'new record',
                        someOtherField: 123,
                    });
                    expect(tableSummaryFieldControlObject.value).toHaveLength(4);

                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledTimes(1);
                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledWith({ screenId, elementId });
                });

                it('getRecordValue should return addRecordWithDefaults value without any internal property', async () => {
                    await tableSummaryFieldControlObject.addRecordWithDefaults();

                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledTimes(1);
                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledWith({ screenId, elementId });

                    expect(tableSummaryFieldControlObject.getRecordValue('-1')).toEqual({
                        anyField: 'new record',
                        someOtherField: 123,
                        _id: '-1',
                    });
                });

                it('getRecordValue should return addRecordWithDefaults value with internal properties', async () => {
                    await tableSummaryFieldControlObject.addRecordWithDefaults();

                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledTimes(1);
                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledWith({ screenId, elementId });

                    expect(tableSummaryFieldControlObject.getInternalRowValue('-1')).toEqual({
                        anyField: 'new record',
                        someOtherField: 123,
                        _id: '-1',
                        $loki: 4,
                        __dirtyColumns: new Set(),
                        __action: 'create',
                        __compositeKey: '-1.0',
                        __dirty: true,
                        __path: '0,-1',
                    });
                });
            });

            it('addOrUpdateValue should set the table value if it is empty', () => {
                tableSummaryFieldControlObject = createFieldControlObject<FieldKey.TableSummary>(
                    FieldKey.TableSummary,
                    screenId,
                    TableSummaryControlObject,
                    elementId,
                    null,
                    properties,
                );

                expect(tableSummaryFieldControlObject.value).toEqual([]);
                expect(tableSummaryFieldControlObject.value).toHaveLength(0);
                const result = tableSummaryFieldControlObject.addOrUpdateRecordValue({
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(tableSummaryFieldControlObject.value).toHaveLength(1);
                expect(result._id).toEqual('-1');
                expect(tableSummaryFieldControlObject.value).toHaveLength(1);
            });

            it('addOrUpdateValue should add record to the DB if it does not have an ID', () => {
                expect(tableSummaryFieldControlObject.value).toHaveLength(3);
                const result = tableSummaryFieldControlObject.addOrUpdateRecordValue({
                    anyField: 'new record',
                    someOtherField: 123,
                });

                expect(result._id).toEqual('-1');
                expect(tableSummaryFieldControlObject.value).toHaveLength(4);
            });

            it('addOrUpdateValue should add record to the DB if its ID does not exist', () => {
                expect(tableSummaryFieldControlObject.value).toHaveLength(3);
                const result = tableSummaryFieldControlObject.addOrUpdateRecordValue({
                    anyField: 'new record',
                    someOtherField: 123,
                    _id: '123',
                });

                expect(result._id).toEqual('123');
                expect(tableSummaryFieldControlObject.value).toHaveLength(4);
            });

            it('should return updated value without any internal property', () => {
                tableSummaryFieldControlObject.addOrUpdateRecordValue({
                    anyField: 'updated record',
                    someOtherField: 123,
                    _id: '2',
                })!;

                expect(tableSummaryFieldControlObject.getRecordValue('2')).toEqual({
                    anyField: 'updated record',
                    someOtherField: 123,
                    _id: '2',
                });
            });

            it('should return updated value with internal properties', () => {
                tableSummaryFieldControlObject.addOrUpdateRecordValue({
                    anyField: 'updated record',
                    someOtherField: 123,
                    _id: '2',
                })!;

                expect(tableSummaryFieldControlObject.getInternalRowValue('2')).toEqual({
                    anyField: 'updated record',
                    someOtherField: 123,
                    _id: '2',
                    $loki: 2,
                    __action: 'update',
                    __compositeKey: '2.0',
                    __dirtyColumns: new Set(['anyField', 'someOtherField']),
                    __dirty: true,
                    __path: '0,2',
                });
            });
        });
    });

    describe('GraphQl filter property', () => {
        let setUiComponentProperties: jest.Mock<void, [string, string, TableSummaryDecoratorProperties]>;
        let refresh: jest.Mock<Promise<FieldInternalValue<FieldKey.TableSummary>>>;
        let newProperties: TableSummaryDecoratorProperties;
        const executeTest = async (filter: GraphQLFilter | (() => GraphQLFilter)) => {
            tableSummaryFieldControlObject.filter = filter;
            expect(setUiComponentProperties).toHaveBeenCalled();
            expect(refresh).toHaveBeenCalled();
            expect(newProperties.filter).toEqual(filter);
        };

        beforeEach(() => {
            setUiComponentProperties = jest.fn(
                (_screenId: string, _elementId: string, _value: TableSummaryDecoratorProperties) => {
                    newProperties = { ..._value };
                },
            );

            refresh = jest.fn();
            tableSummaryFieldControlObject = createFieldControlObject<FieldKey.TableSummary>(
                FieldKey.TableSummary,
                screenId,
                TableSummaryControlObject,
                elementId,
                value,
                properties,
                { setUiComponentProperties, refresh },
            );
        });

        it('should update filter with GraphQL filter object', () => {
            executeTest({ description: { _regex: 'policy', _options: 'i' } });
        });

        it('should update filter with function', () => {
            executeTest(() => ({ description: { _regex: 'policy', _options: 'i' } }));
        });
    });
});
