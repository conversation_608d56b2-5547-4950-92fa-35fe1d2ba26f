import { fail } from 'assert';
import * as nestedFields from '../../../nested-fields';
import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata } from '../../../../__tests__/test-helpers';
import type { NestedField } from '../../../nested-fields';
import { tableSummaryField } from '../table-summary-decorator';
import type { TableSummaryDecoratorProperties, TableSummaryProperties } from '../table-summary-types';

describe('Table Summary Decorator', () => {
    const fieldId = 'tableSummaryField';
    let pageMetadata: pageMetaData.PageMetadata;

    describe('mapping values', () => {
        let mappedComponentProperties: TableSummaryDecoratorProperties<ScreenBase>;
        beforeEach(() => {
            pageMetadata = getMockPageMetadata();
            jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
            tableSummaryField({ columns: [] })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            mappedComponentProperties = pageMetadata.uiComponentProperties[
                fieldId
            ] as TableSummaryProperties<ScreenBase>;
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('TableSummaryProperties -> should set default values when no component properties provided', () => {
            expect(mappedComponentProperties.bind).toBeUndefined();
            expect(mappedComponentProperties.columns).toEqual([]);
            expect(mappedComponentProperties.helperText).toBeUndefined();
            expect(mappedComponentProperties.isHidden).toBe(false);
            expect(mappedComponentProperties.isTransient).toBe(false);
            expect(mappedComponentProperties.onChange).toBeUndefined();
            expect(mappedComponentProperties.onClick).toBeUndefined();
            expect(mappedComponentProperties.onRowSelected).toBeUndefined();
            expect(mappedComponentProperties.onRowUnselected).toBeUndefined();
            expect(mappedComponentProperties.orderBy).toBeUndefined();
            expect(mappedComponentProperties.parent).toBeUndefined();
            expect(mappedComponentProperties.selectedRecords).toBeUndefined();
            expect(mappedComponentProperties.title).toBeUndefined();
            expect(mappedComponentProperties._controlObjectType).toEqual('TableSummary');
        });

        describe('mapping mandatory props for nested fields', () => {
            const keys = [
                'Aggregate',
                'Checkbox',
                'Count',
                'Date',
                'FilterSelect',
                'Icon',
                'Image',
                'Label',
                'Link',
                'MultiReference',
                'Numeric',
                'Progress',
                'Reference',
                'Select',
                'Switch',
                'Text',
            ];

            const mandatoryProps = {
                Aggregate: { bind: 'Aggregate' },
                Checkbox: { bind: 'Checkbox' },
                Count: { bind: 'Count' },
                Date: { bind: 'Date' },
                FilterSelect: { bind: 'FilterSelect' },
                Icon: { bind: 'Icon' },
                Image: { bind: 'Image' },
                Label: { bind: 'Label' },
                Link: { bind: 'Link', page: 'Test page' },
                MultiReference: { bind: 'MultiReference' },
                Numeric: { bind: 'Numeric' },
                Progress: { bind: 'Progress' },
                Reference: { bind: 'Reference' },
                Select: { bind: 'Select' },
                Switch: { bind: 'Switch' },
                Text: { bind: 'Text' },
            };

            const columns: NestedField<Page, nestedFields.GridNestedFieldTypes>[] = keys.map(key => {
                const lowerCaseKey = key[0].toLowerCase() + key.slice(1);
                return nestedFields[lowerCaseKey]({ ...mandatoryProps[key] });
            });

            it('should set values when component properties provided', () => {
                expect(columns.length).toEqual(keys.length);
                columns.forEach(column => {
                    if (column.properties) {
                        expect(keys).toContain(column.properties.bind);
                    } else {
                        fail('Test failed: no properties in Column object');
                    }
                });
            });
        });
    });
});
