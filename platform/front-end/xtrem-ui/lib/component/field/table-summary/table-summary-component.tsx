import * as React from 'react';
import { connect } from 'react-redux';
import type { NestedField, NestedFieldTypesWithoutTechnical } from '../../nested-fields';
import { withoutNestedTechnical } from '../../nested-fields';
import { getFieldTitle } from '../carbon-helpers';
import Label from 'carbon-react/esm/__internal__/label';
import { TableSummaryRowComponent } from './table-summary-row-component';
import { getNestedFieldElementId } from '../../../utils/abstract-fields-utils';
import { HelperText } from '../carbon-utility-components';
import type { TableSummaryDecoratorProperties } from './table-summary-types';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { CollectionValue } from '../../../service/collection-data-service';
import type { NestedFieldWrapperContextProps } from '../../../render/nested-field-wrapper';
import type { ScreenBase } from '../../../service/screen-base';
import { ContextType } from '../../../types';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { withCollectionValueSubscription } from '../../connected-collection';
import { localize } from '../../../service/i18n-service';
import { FieldKey } from '../../types';
import { splitValueToMergedValue } from '../../../utils/transformers';

export class TableSummaryComponent extends EditableFieldBaseComponent<
    Omit<TableSummaryDecoratorProperties, 'onChange'>,
    CollectionValue,
    Pick<TableSummaryDecoratorProperties, 'onChange'>
> {
    getColumnHeaders = (): React.ReactNode =>
        this.getVisibleColumns().map(col => {
            const title = resolveByValue({
                propertyValue: col.properties.title,
                screenId: this.props.screenId,
                skipHexFormat: true,
                rowValue: null,
                fieldValue: null,
            });
            const isRightAligned = [FieldKey.Numeric, FieldKey.Aggregate].includes(col.type);
            const elementId = getNestedFieldElementId(col);

            return (
                <th
                    className={isRightAligned ? 'e-table-summary-field-right-aligned-header' : undefined}
                    key={elementId}
                    data-testid={`e-table-summary-field-header-${elementId}`}
                >
                    <span>{title}</span>
                </th>
            );
        });

    getVisibleColumns = (): NestedField<ScreenBase, NestedFieldTypesWithoutTechnical>[] =>
        withoutNestedTechnical(this.props.fieldProperties.columns).filter(
            r =>
                !resolveByValue({
                    propertyValue: r.properties.isHidden,
                    screenId: this.props.screenId,
                    skipHexFormat: true,
                    rowValue: null,
                    fieldValue: null,
                }),
        );

    getRows = (): React.ReactNode => {
        const rowsData = this.props.value?.getData() || [];

        const visibleColumns = this.getVisibleColumns();

        const defaultEmptyStateText = localize(
            '@sage/xtrem-ui/table-summary-empty-default-text',
            'There are no data to display',
        );

        if (visibleColumns.length) {
            return rowsData.map(item => {
                const commonProperties: NestedFieldWrapperContextProps = {
                    _id: item._id,
                    contextType: ContextType.tableSummary,
                    screenId: this.props.screenId,
                    parentElementId: this.props.elementId,
                    setFieldValue: () => Promise.resolve(),
                    handlersArguments: { rowValue: splitValueToMergedValue(item) },
                };

                return (
                    <TableSummaryRowComponent
                        key={item._id}
                        value={this.props.value}
                        recordId={item._id}
                        columns={visibleColumns}
                        commonProperties={commonProperties}
                    />
                );
            });
        }

        return (
            <div className="e-table-summary-field-empty">
                <span className="e-table-summary-field-empty-message">
                    {localize('@sage/xtrem-ui/table-summary-empty-text', this.props.fieldProperties.emptyStateText!) ||
                        defaultEmptyStateText}
                </span>
            </div>
        );
    };

    render(): React.ReactNode {
        const title = getFieldTitle(this.props.screenId, this.props.fieldProperties, null);
        return (
            <div
                {...this.getBaseAttributesDivWrapper(
                    'table-summary',
                    'e-table-summary-field',
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    this.props.isNested,
                )}
            >
                {title && !this.props.fieldProperties.isTitleHidden && (
                    <div className="e-field-title">
                        <Label
                            error={this.props.validationErrors?.[0]?.message}
                            htmlFor={undefined as unknown as string}
                        >
                            {title}
                        </Label>
                    </div>
                )}
                <table className="e-table-summary-field-table">
                    <tbody className="e-table-summary-field-body">
                        <tr
                            key="header"
                            className="e-table-summary-field-header"
                            data-testid="e-table-summary-field-header"
                        >
                            {this.getColumnHeaders()}
                        </tr>
                        {this.getRows()}
                    </tbody>
                </table>
                {this.props.fieldProperties.helperText && (
                    <HelperText helperText={this.props.fieldProperties.helperText} />
                )}
            </div>
        );
    }
}

export const ConnectedTableSummaryComponent = connect(
    mapStateToProps(),
    mapDispatchToProps(),
)(withCollectionValueSubscription(TableSummaryComponent));

export default ConnectedTableSummaryComponent;
