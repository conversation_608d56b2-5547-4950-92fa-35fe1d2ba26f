/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { TableSummaryControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { TableSummaryDecoratorProperties, TableSummaryExtensionDecoratorProperties } from './table-summary-types';
import { addColumnsToProperties, addDisabledToProperties, addNodeToProperties } from '../../../utils/data-type-utils';
import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';

class TableSummaryDecorator extends AbstractFieldDecorator<FieldKey.TableSummary> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = TableSummaryControlObject;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<TableSummaryDecoratorProperties> {
        const properties: Partial<TableSummaryDecoratorProperties> = {};
        addNodeToProperties({ dataType, propertyDetails, properties });
        addColumnsToProperties({
            dataType,
            propertyDetails,
            properties,
            dataTypes: this.dataTypes,
            nodeTypes: this.nodeTypes,
        });
        addDisabledToProperties({
            propertyDetails,
            dataType,
            properties,
        });
        return properties;
    }
}

/**
 * Initializes the decorated member as a [TableSummary]{@link TableSummaryControlObject} field with the provided properties
 *
 * @param properties The properties that the [TableSummary]{@link TableSummaryControlObject} field will be initialized with
 */
export function tableSummaryField<CT extends ScreenExtension<CT>, ReferencedItemType extends ClientNode = any>(
    properties: TableSummaryDecoratorProperties<Extend<CT>, ReferencedItemType>,
): (target: CT, name: string) => void {
    return standardDecoratorImplementation<CT, FieldKey.TableSummary, ReferencedItemType>(
        properties,
        TableSummaryDecorator,
        FieldKey.TableSummary,
        true,
    );
}

export function tableSummaryFieldOverride<CT extends ScreenExtension<CT>, ReferencedItemType extends ClientNode = any>(
    properties: TableSummaryExtensionDecoratorProperties<CT, ReferencedItemType>,
): (target: CT, name: string) => void {
    return standardExtensionDecoratorImplementation<CT, FieldKey.TableSummary, ReferencedItemType>(properties);
}
