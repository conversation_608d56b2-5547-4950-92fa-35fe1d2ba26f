import type { ClientNode } from '@sage/xtrem-client';
import type { CollectionValue } from '../../../service/collection-data-service';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import type { ScreenBase } from '../../../service/screen-base';
import type { CollectionValueFieldProperties } from '../collection-value-field';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type {
    HasColumns,
    HasDynamicLookupSuggestions,
    HasRowChangeIndicators,
    CanFetchDefaults,
    Clickable,
    ExtensionField,
    HasCollectionSelectionEventHandlers,
    HasCollectionSelectionEventHandlersAfter,
    HasFieldActions,
    HasParent,
} from '../traits';
import type { NestedRecordId, ScreenExtension } from '../../../types';
import type { OverrideDecoratorProperties } from '../../../utils/decorator-utils';
import type { BlockControlObject, SectionControlObject } from '../../control-objects';
import type { GridNestedFieldTypes } from '../../nested-fields';
import type { NestedExtensionField } from '../../nested-fields-extensions';
import type { NestedOverrideField } from '../../nested-fields-overrides';
import type { FieldControlObjectInstance } from '../../types';
import type { Extend } from '../../../service/page-extension';

export interface TableSummaryDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    NestedRecordType extends ClientNode = any,
> extends TableSummaryProperties<CT, NestedRecordType>,
        Clickable<CT>,
        HasFieldActions<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>>,
        HasCollectionSelectionEventHandlers<CT, NestedRecordType>,
        CanFetchDefaults {
    /** Function to be executed when the table field's value changes */
    onChange?: (this: CT, recordId: NestedRecordId, column: string, rowItem: NestedRecordType) => void;
}

export type BaseTableSummaryExtensionDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    NestedRecordType extends ClientNode = any,
> = HasCollectionSelectionEventHandlersAfter<CT, NestedRecordType> & {
    /** Function to be executed when the table field's value changes after base onChange*/
    onChangeAfter?: (this: CT, recordId: NestedRecordId, column: string, rowItem: NestedRecordType) => void;
};

export interface TableSummaryExtensionDecoratorProperties<
    CT extends ScreenExtension<CT>,
    ReferencedItemType extends ClientNode = any,
> extends BaseTableSummaryExtensionDecoratorProperties<Extend<CT>>,
        OverrideDecoratorProperties<TableSummaryDecoratorProperties<Extend<CT>, ReferencedItemType>> {
    /** The definitions of the nested fields used to represent the table rows */
    columns?: NestedExtensionField<CT, GridNestedFieldTypes, ReferencedItemType>[];

    /** Allows overriding existing column properties in the base page's columns */
    columnOverrides?: NestedOverrideField<CT, GridNestedFieldTypes, ReferencedItemType>[];
}

export interface TableSummaryProperties<
    CT extends ScreenExtension<CT> = ScreenBase,
    NestedRecordType extends ClientNode = any,
> extends CollectionValueFieldProperties<CT, NestedRecordType>,
        HasColumns<CT, NestedRecordType>,
        HasRowChangeIndicators,
        HasFieldActions<CT> {
    /** Defines a message when the table is empty */
    emptyStateText?: string;
}

export interface InternalTableSummaryProperties<
    CT extends ScreenExtension<CT> = ScreenBase,
    NRT extends ClientNode = any,
> extends TableSummaryProperties<CT, NRT>,
        HasDynamicLookupSuggestions<CT, NRT> {
    activeUserFilter?: GraphQLFilter;
    isNewEnabled?: boolean;
    valueField?: string;
    hiddenColumns?: string[];
}

export type TableSummaryComponentProperties = BaseEditableComponentProperties<
    TableSummaryDecoratorProperties,
    CollectionValue
>;
