import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import type { TableSummaryComponentProperties } from './table-summary-types';

const ConnectedTableSummaryComponent = React.lazy(() => import('./table-summary-component'));

export function AsyncConnectedTableSummaryComponent(props: FieldComponentExternalProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <ConnectedTableSummaryComponent {...props} />
        </React.Suspense>
    );
}

const TableSummaryComponent = React.lazy(() =>
    import('./table-summary-component').then(c => ({ default: c.TableSummaryComponent })),
);

export function AsyncTableSummaryComponent(props: TableSummaryComponentProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={true} bodyHeight="200px" />}>
            <TableSummaryComponent {...props} />
        </React.Suspense>
    );
}
