import * as React from 'react';
import type { NestedFieldWrapperContextProps } from '../../../render/nested-field-wrapper';
import { ConnectedNestedFieldWrapper } from '../../../render/nested-field-wrapper';
import type { ScreenBase } from '../../../service/screen-base';
import type { NestedField, NestedFieldTypesWithoutTechnical } from '../../nested-fields';
import { getNestedFieldElementId, normalizeUnderscoreBind } from '../../../utils/abstract-fields-utils';
import type { ConnectedCollectionItemValueProps } from '../../connected-collection';
import { withCollectionValueItemSubscription } from '../../connected-collection';
import { FieldKey } from '../../types';
import { splitValueToMergedValue } from '../../../utils/transformers';
import { get } from 'lodash';

export interface TableRowComponentProps extends ConnectedCollectionItemValueProps {
    columns: NestedField<ScreenBase, NestedFieldTypesWithoutTechnical>[];
    commonProperties: NestedFieldWrapperContextProps;
}

export const TableSummaryRowComponent = withCollectionValueItemSubscription(
    ({ commonProperties, columns, recordValue }: TableRowComponentProps) => {
        const value = splitValueToMergedValue(recordValue || {});
        return (
            <tr className="e-table-summary-field-rows" data-testid={`e-table-summary-field-row-${recordValue?._id}`}>
                {columns.map(colDef => {
                    const elementId = getNestedFieldElementId(colDef);
                    const fieldValue = get(value, normalizeUnderscoreBind(elementId)) || null;
                    return (
                        <td key={elementId} className={colDef.type === FieldKey.Label ? 'label' : ''}>
                            <ConnectedNestedFieldWrapper
                                {...commonProperties}
                                columnDefinition={colDef}
                                columnName={elementId}
                                columnProperties={colDef.properties}
                                value={fieldValue}
                                nestedReadOnlyField={true}
                            />
                        </td>
                    );
                })}
            </tr>
        );
    },
);
