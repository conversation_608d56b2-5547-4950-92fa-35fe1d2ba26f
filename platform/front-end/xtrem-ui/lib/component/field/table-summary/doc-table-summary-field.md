PATH: XTREEM/UI+Field+Widgets/TableSummary+Field

## Introduction

TableSummary fields are used to represent tabular data in a simple non editable form.

## Example:

```ts
@ui.decorators.tableSummaryField<TableSummaryField>({
    parent() {
        return this.tableSummaryBlock;
    },
    node: '@sage/x3-products/Product',
    title: 'Summary Table Title',
    columns: [
        ui.nestedFields.text({
            bind: 'product',
            title: 'Product',
        }),
        ui.nestedFields.numeric({
            bind: 'qty',
            title: 'Quantity',
        }),
    ],
    helperText: 'Helper text here.',
    orderBy: {
        product: 1,
    },
    emptyStateText: 'Custom no data to display message'
})
products: ui.fields.TableSummary<SalesOrderItem>;
```

### Display decorator properties

-   **columns**: A list of nested fields to be displayed as columns. The following types can be used as nested fields: 'checkbox', 'icon', 'image', 'label', 'link', 'numeric', 'progress', 'reference', 'text', 'date'.
-   **helperText**: The helper text that is displayed above the table. It is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Whether the table is disabled or not. Defaults to false.
-   **isFullWidth**: Whether the table spans all the parent width or not. It can also be defined as callback function that returns a boolean. Defaults to false.
-   **isHidden**: Whether the table is hidden or not. Defaults to false.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
-   **orderBy**: The column or the set of columns which the table should be sorted by.
-   **title**: The title that is displayed above the table. It is automatically picked up by the i18n engine and externalized.
-   **emptyStateText**: The message to display when there are no data returned in the table. If it's not set, then the default empty state message is displayed.

### Binding decorator properties

-   **bind**: The GraphQL object's property that the table value is bound to. If not provided, the table name is used.
-   **node**: The name of the node that the table represents, it must be defined if you want to use the advanced filtering capabilities.

### Event handler decorator properties

-   **onClick**: Event triggered when any parts of the table is clicked, no arguments provided.
-   **onRowClick**: Event triggered when any part of a table row is clicked.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

## Runtime functions

-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
-   **redraw(columnBind?: string)**: Redraws the grid element on the screen and executes all property callback functions. If a `columnBind` property is passed, it only redraws that column.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **getNewRecords()**: Returns the new, unsaved records that were addded to the table.
-   **setRecordValue(rowValue: any)**: Updates a single row on the table and keeps update history.
-   **getRecordByFieldValue(fieldName: keyof NestedRecordType, fieldValue: any)**: Find a row based on a property's value
-   **getRecordValue(recordId:string)**: Returns a single row. It can only return rows that were previously loaded to the client side-cache by some other user interaction.
-   **addRecord(rowValue: any)**: Adds a single row to the table and marks it as a new row.
-   **addOrUpdateRecordValue(rowValue: any)**: Add or update row in the table depending of the existence of the ID field.
-   **removeRecord(recordId:string)**: Removes a single row from the displayed table. It does NOT delete the corresponding server side entity.
-   **selectRecord(recordId:string)**: Selects a single row.
-   **generateRecordId()**: Returns an auto-generated ID that can be used for a new row.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.

### Column properties

Please refer to the documentation of the corresponding nested field type.

## Adding columns in extension pages

Additional fields can be added to a table in extension page artifacts using the `tableSummaryFieldOverride` decorator. In this case, the `columns` decorator property accepts an `nestedFieldExtension` array. This set of nested fields comes with an additional decorator property which determines the position of the extension columns within the original table. This property is called `insertBefore`.

### Positioning extension fields within the table

The `insertBefore` decorator property determines the position of the extension field.
#### Not provided
If this decorator property is not provided, the extension field is added to the end of the columns.

#### Using the bind value
The position of the extension column can be set by providing the `bind` decorator property value of the base column that the field should be inserted before to the `insertBefore` extension column decorator property.

#### Base reference fields bound to the same property
When the base page contains reference field based columns that are bound to the same property, the position of the additional column can be set more precisely by including the `valueField` property value into the `insertBefore` extension property. This must be done by joining the two values by a double underscore in the following format: `bind__valueField`. For example if the field should be positioned before a reference field that is bound to `product` and the displayed value field is `description`, than the `insertBefore` extension property would be `product__description`.

### Example

```ts
import * as ui from '@sage/xtrem-ui';

...

export class TableSummaryExtension extends ui.PageExtension<TableSummary, GraphApi> {

    ...

    @ui.decorators.tableSummaryFieldOverride<TableSummaryExtension, ShowCaseProduct>({
        title: 'Overridden Title',
        columns: [
            ui.nestedFieldExtensions.reference<TableSummaryExtension, ShowCaseProduct, BiodegradabilityCategory>({
                bind: 'biodegradabilityCategory',
                valueField: 'description',
                node: '@sage/xtrem-show-case-bundle/BiodegradabilityCategory',
                // This column is inserted before the `product` bound reference column that has `name` valueField configuration
                insertBefore: 'product__name',
                columns: [
                    ui.nestedFields.text({ bind: 'description', title: 'Description' }),
                    ui.nestedFields.text({ bind: 'energyToProduce', title: 'Energy to produce' })
                ],
                title: 'Biodegradability category',
                isAutoSelectEnabled: true,
            }),
            // This column goes to the end.
            ui.nestedFieldExtensions.text<TableSummaryExtension>({
                bind: 'someTransientFieldAtTheEnd',
                isTransient: true,
                title: 'Transient ext column',
            }),
            // This column goes to the end.
            ui.nestedFieldExtensions.text<TableSummaryExtension>({
                bind: 'someTransientFieldAtTheVeryEnd',
                isTransient: true,
                title: 'Transient ext column 2',
            }),
            // This column is inserted before the `provider` bound base column.
            ui.nestedFieldExtensions.numeric<TableSummaryExtension>({
                bind: 'someTransientFieldInTheMiddle',
                isTransient: true,
                title: 'Transient number',
                insertBefore: 'provider',
                scale: 5
            }),
        ]
    })
    field: ui.fields.TableSummary;
}
```
## Overriding columns in extension pages

TableSummary's columns can be overridden in extension page artifacts using the `tableSummaryFieldOverride` decorator. In this case, the `columnOverrides` decorator property accepts an `nestedFieldOverrides` array. This set of nested fields have to match the bind values of the columns to extend.

### Example

```ts
import * as ui from '@sage/xtrem-ui';

...

export class TableSummaryExtension extends ui.PageExtension<TableSummary, GraphApi> {

    ...

    @ui.decorators.tableSummaryFieldOverride<TableSummaryExtension, ShowCaseProduct>({
        title: 'Overridden Title',
        columnOverrides: [
            ui.nestedFieldOverrides.numeric<TableSummaryExtension>({ // same type as the base column
                bind: 'qty', // same bind as the base column
                scale: 1, // property to override
                insertBefore: 'price', // insertBefore works the same as in nestedFieldExtensions
            }),
            ui.nestedFieldOverrides.reference<TableSummaryExtension, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                valueField: 'textField', // same valueField as the base column
                title: 'Provider Overriden title', // property to override
            }),
        ]
    })
    field: ui.fields.TableSummary;
}
```
## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/TableSummary).
