@import '../../../render/style/variables.scss';

.e-table-summary-field {
    box-sizing: border-box;

    @include extra_small {
        padding-top: 0;
        box-sizing: content-box;
        @include full_width_mobile_field;

        .e-field-title {
            padding-left: 16px;
            padding-right: 16px;
        }
    }

    .e-table-summary-field-table {
        border: 1px solid #BEC3C7;
        border-spacing: 0;
        border-radius: var(--borderRadius050);

        @include medium_and_below {
            .e-numeric-field {
                .e-field-read-only {
                    text-align: right;
                }
            }
        }

        .e-context-tableSummary {
            .e-field-read-only {
                font-size: 14px;
                padding: 0;
                height: 24px;
                line-height: 24px;
            }

            .common-input__label {
                display: none;
            }
        }

        .e-numeric-field {
            .e-field-read-only {
                padding-right: 14px;
            }
        }


        .e-table-summary-field-body {
            border: none;
            border-radius: var(--borderRadius050);
        }

        .e-table-summary-field-header {
            display: flex;
            background-color: var(--colorsUtilityMajor100);
            color: #000000E5;
            font-size: 14px;
            height: 40px;
            border-bottom: 1px solid #BEC3C7;
            border-top-left-radius: calc(var(--borderRadius050) - 1px);
            border-top-right-radius: calc(var(--borderRadius050) - 1px);

        }

        .e-table-summary-field-header th {
            width: 100%;
            padding: 6px 0 0 12px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .e-table-summary-field-header th span {
            display: flex;
            border-right: 1px solid;
            height: 24px;
            line-height: 24px;
            margin-top: 2px;
            font-family: var(--fontFamiliesDefault);
            font-weight: var(--fontWeights500);
            font-size: 14px;
        }

        .e-table-summary-field-header th.e-table-summary-field-right-aligned-header span {
            text-align: right;
            display: block;
            padding-right: 12px;
        }

        .e-table-summary-field-header th:last-child {
            span {
                border: none;
            }
        }

        .e-table-summary-field-rows {
            display: flex;
            height: 40px;
            border-bottom: 1px solid var(--colorsUtilityMajor075);

            &:last-child {
                border-bottom: none;
            }
        }

        .e-table-summary-field-rows:nth-of-type(even) {
            background-color: var(--colorsReadOnly400);
        }

        .e-table-summary-field-rows td {
            width: 100%;
            padding: 6px 0 0 12px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            .e-pill-wrapper {
                padding-top: 6px;

                span {
                    margin-top: 0 !important;
                }
            }
        }

        .e-table-summary-field-rows td.label {
            padding: 2px 0 0 2px;
        }

        .e-table-summary-field-empty {
            height: 100%;

            .e-table-summary-field-empty-message {
                display: flex;
                justify-content: center;
                width: 100%;
                padding: 50px 0;
                font-size: 16px;
                color: var(--colorsYin055)
            }
        }
    }
}