import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { ImageComponentProps } from './image-types';

const ConnectedImageComponent = React.lazy(() => import('./image-component'));

export function AsyncConnectedImageComponent(props: FieldComponentExternalProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <ConnectedImageComponent {...props} />
        </React.Suspense>
    );
}

const ImageComponent = React.lazy(() => import('./image-component').then(c => ({ default: c.ImageComponent })));

export function AsyncImageComponent(props: ImageComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader size="small" />}>
            <ImageComponent {...props} />
        </React.Suspense>
    );
}
