/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import type { FieldComponentProps, FieldKey } from '../../types';

/**
 * [Field]{@link EditableFieldControlObject} that holds an image
 */
export class ImageControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.Image,
    FieldComponentProps<FieldKey.Image>
> {
    /** The value of the height attribute of the HTML image (e.g. 100px, 75%, auto, etc.)*/
    get height(): string | undefined {
        return this.getUiComponentProperty('height');
    }
}
