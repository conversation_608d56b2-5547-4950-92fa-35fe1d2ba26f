PATH: XTREEM/UI+Field+Widgets/Image+Field

## Introduction

The image field represents the display and editor of image data in a container of pages within the user interface. The image field is also available as nested and extended field.

## Example

```ts
@ui.decorators.imageField<Page>({
    height: '100%',
    helperText: 'Helper text displayed below the field.',
    isDisabled: false,
    isHelperTextHidden: false,
    isHidden: false,
    isTransient: true,
    size: 'medium',
    title: 'Image Field',
    onChange() {
        console.log(`Do something when the field is clicked.`);
    },
    onClick() {
        console.log(`Do something when the field is changed.`);
    },
    parent() {
        return this.block;
    },
})
field: ui.fields.Image;
```

```ts
@ui.decorators.imageField<Page>({
    title: 'Extended Image Field',
    insertBefore() {
        return this.field;
    },
    parent() {
        return this.block;
    },
})
extended: ui.fields.Image;
```

```ts
@ui.decorators.tableField<Page>({
    ...,
    columns: [
        ...,
        ui.nestedFields.image({
            bind: 'productImage',
            title: 'Image',
        })
    ],
})
table: ui.fields.Table<Node>;
```

## Decorator Properties

-   **shape**: Adds a square or circle border to the image, only if it's a nested field. The options are "square" or "circle". If no shape is passed, it defaults to "square".

### Display Properties

-   **height**: The height attribute of the resulting HTML image (e.g. 100px, 50%, auto, etc.)
-   **helperText**: The helper text displayed below the field. This property is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.
-   **isFullWidth**: Determines whether the field takes up the full width of the grid or not.
-   **isHelperTextHidden**: Determines whether the field's helper text is displayed or not.
-   **isHidden**: Determines whether the field is displayed or not.
-   **isTitleHidden**: Determines whether the field's title is displayed or not.
-   **size**: Size the field should be rendered in. The options are "small", "medium" and "large".
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **placeholderValue**: In some places when the image is empty, a placeholder with up to 3 letters is rendered. This decorator property defines what value is used for displaying the place holder value.
-   **placeholderMode**: In some places when the image is empty, a placeholder with up to 3 letters is rendered. The placeholder either displays the `FirstThree` letters of the placeholder value or the `Initials` of the first three words.

### Binding Properties

-   **bind**: Determines the associated GraphQL node's property the field's value will be bound to.
-   **isTransient**: Determines whether the field will be excluded from the automatic data binding process or not.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.

### Event Handler Properties

-   **onChange**: Handler triggered when the field's value changes.
-   **onClick**: Handler triggered when the field is clicked.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

### Other Decorator Properties

-   **fetchesDefaults**: When set to true and when the image value changes, a request to the server for default values for the whole page will be requested. False by default.

#### Runtime Functions

-   **refresh()**: Refetches the field's values from the server and updates the user interface.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).


## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Image).
