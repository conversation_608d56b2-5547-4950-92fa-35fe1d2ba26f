import * as React from 'react';
import type { NestedImageProperties } from '../../nested-fields-properties';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import type { PortraitPlaceholderMode } from '../../ui/portrait-component';
import { Portrait } from '../../ui/portrait-component';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { getImageUrlFromValue } from './image-utils';

export const ImageCellRenderer: React.FC<CellParams<NestedImageProperties>> = React.memo(props => {
    const { fieldProperties } = props;

    if (!props.value) {
        const placeholderValue = resolveByValue({
            propertyValue: fieldProperties.placeholderValue,
            skipHexFormat: true,
            screenId: props.screenId,
            // Intentionally undefined, the validation rule should not depend on the value that is validated by that very rule
            rowValue: undefined,
            // Pass in the row value as the field value because the field value is passed in as the first argument to the functional code
            fieldValue: props.data,
        });

        const placeholderMode = resolveByValue<PortraitPlaceholderMode>({
            propertyValue: fieldProperties.placeholderMode,
            skipHexFormat: true,
            screenId: props.screenId,
            // Intentionally undefined, the validation rule should not depend on the value that is validated by that very rule
            rowValue: undefined,
            // Pass in the row value as the field value because the field value is passed in as the first argument to the functional code
            fieldValue: props.data,
        });
        return (
            <fieldProperties.wrapper {...props}>
                <Portrait size="S" placeholderMode={placeholderMode} placeholderValue={placeholderValue} icon="image" />
            </fieldProperties.wrapper>
        );
    }

    return (
        <fieldProperties.wrapper {...props}>
            <img src={getImageUrlFromValue(props.value)} alt="cell" width="26px" />
        </fieldProperties.wrapper>
    );
});

ImageCellRenderer.displayName = 'ImageCellRenderer';
