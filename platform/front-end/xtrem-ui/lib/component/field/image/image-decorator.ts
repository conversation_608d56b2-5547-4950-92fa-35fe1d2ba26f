/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { ImageControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { ImageDecoratorProperties } from './image-types';

class ImageDecorator extends AbstractFieldDecorator<FieldKey.Image> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = ImageControlObject;
}

/**
 * Initializes the decorated member as a [Image]{@link ImageControlObject} field with the provided properties
 *
 * @param properties The properties that the [Image]{@link ImageControlObject} field will be initialized with
 */
export function imageField<T extends ScreenExtension<T>>(
    properties: ImageDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Image>(properties, ImageDecorator, FieldKey.Image);
}

export function imageFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<ImageDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Image>(properties);
}
