import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { ImageProperties } from '../../../control-objects';
import { imageField } from '../image-decorator';

describe('Image decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'iconField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        it('should set default values when no component properties provided', () => {
            imageField({})({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties: ImageProperties = pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.height).toBeUndefined();
        });

        it('should set values when component properties provided', () => {
            imageField({
                height: '83',
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties: ImageProperties = pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.height).not.toBeUndefined();
            expect(mappedComponentProperties.height).toBe('83');
        });

        it('should assign onClick handler', () => {
            testOnClickHandler(imageField, pageMetadata, fieldId);
        });
    });
});
