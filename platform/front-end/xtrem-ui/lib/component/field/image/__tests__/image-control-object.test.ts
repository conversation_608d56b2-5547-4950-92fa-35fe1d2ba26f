import { <PERSON><PERSON><PERSON> } from '../../../types';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import { ImageControlObject } from '../../../control-objects';

const image = {
    value:
        'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDADIiJSwlHzIsKSw4NTI7S31RS0VFS5ltc1p9tZ++u7Kfr6zI4f/zyNT/' +
        '16yv+v/9////////wfD/////////////2wBDATU4OEtCS5NRUZP/zq/O/////////////////////////////////////////////////////////////////' +
        '///wAARCAAYAEADAREAAhEBAxEB/8QAGQAAAgMBAAAAAAAAAAAAAAAAAQMAAgQF/8QAJRABAAIBBAEEAgMAAAAAAAAAAQIRAAMSITEEEyJBgTORUWFx/8QAFA' +
        'EBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AOgM52xQDrjvAV5Xv0vfKUALlTQfeBm0HThMNHXkL0Lw/swN5qg' +
        'A8yT4MCS1OEOJV8mBz9Z05yfW8iSx7p4j+jA1aD6Wj7ZMzstsfvAas4UyRHvjrAkC9KhpLMClQntlqFc2X1gUj4viwVObKrddH9YDoHvuujAEuNV+bLwFS8Xx' +
        'dSr+Cq3Vf+4F5RgQl6ZR2p1eAzU/HX80YBYyJLCuexwJCO2O1bwCRidAfWBSctswbI12GAJT3yiwFR7+MBjGK2g/WAJR3FdF84E2rK5VR0YH/9k=',
};

describe('Image Field', () => {
    const screenId = 'TestPage';
    let imageFieldControlObject: ImageControlObject;

    beforeEach(() => {
        imageFieldControlObject = createFieldControlObject<FieldKey.Image>(
            FieldKey.Image,
            screenId,
            ImageControlObject,
            'test',
            image,
            {
                title: 'TEST_FIELD_TITLE',
                isHidden: true,
                isDisabled: true,
                height: '150px',
            },
        );
    });

    it('getting field value', () => {
        expect(imageFieldControlObject.value).toEqual(image);
    });

    describe('setting and getting updated values', () => {
        it('should set the title', () => {
            const testFixture = 'Test Title';
            expect(imageFieldControlObject.title).not.toEqual(testFixture);
            imageFieldControlObject.title = testFixture;
            expect(imageFieldControlObject.title).toEqual(testFixture);
        });

        it('should get the height', () => {
            expect(imageFieldControlObject.height).toEqual('150px');
        });
    });
});
