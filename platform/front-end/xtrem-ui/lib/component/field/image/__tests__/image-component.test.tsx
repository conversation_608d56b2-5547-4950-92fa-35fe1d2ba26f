import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import type { ImageProperties } from '../../../control-objects';
import { FieldKey } from '../../../types';
import type { ImageComponentProps, ImageValue } from '../image-types';
import { ConnectedImageComponent, ImageComponent } from '../image-component';
import { fireEvent, render } from '@testing-library/react';

const imageValue: ImageValue = {
    value:
        '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDADIiJSwlHzIsKSw4NTI7S31RS0VFS5ltc1p9tZ++u7Kfr6zI4f/zyNT/' +
        '16yv+v/9////////wfD/////////////2wBDATU4OEtCS5NRUZP/zq/O/////////////////////////////////////////////////////////////////' +
        '///wAARCAAYAEADAREAAhEBAxEB/8QAGQAAAgMBAAAAAAAAAAAAAAAAAQMAAgQF/8QAJRABAAIBBAEEAgMAAAAAAAAAAQIRAAMSITEEEyJBgTORUWFx/8QAFA' +
        'EBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AOgM52xQDrjvAV5Xv0vfKUALlTQfeBm0HThMNHXkL0Lw/swN5qg' +
        'A8yT4MCS1OEOJV8mBz9Z05yfW8iSx7p4j+jA1aD6Wj7ZMzstsfvAas4UyRHvjrAkC9KhpLMClQntlqFc2X1gUj4viwVObKrddH9YDoHvuujAEuNV+bLwFS8Xx' +
        'dSr+Cq3Vf+4F5RgQl6ZR2p1eAzU/HX80YBYyJLCuexwJCO2O1bwCRidAfWBSctswbI12GAJT3yiwFR7+MBjGK2g/WAJR3FdF84E2rK5VR0YH/9k=',
};

describe('XtremImageField', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: ImageProperties;

    beforeEach(() => {
        mockFieldProperties = {
            title: 'Test image',
        };
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Image, state, screenId, 'test-image-field', mockFieldProperties, imageValue);
            addFieldToState(FieldKey.Image, state, screenId, 'test-empty-image-field', mockFieldProperties, null);
            mockStore = getMockStore(state);
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedImageComponent screenId={screenId} elementId="test-image-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedImageComponent screenId={screenId} elementId="test-image-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render without value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedImageComponent screenId={screenId} elementId="test-empty-image-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with set height and width', () => {
                mockFieldProperties.height = '100px';

                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedImageComponent screenId={screenId} elementId="test-empty-image-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render in full-width mode', () => {
                mockFieldProperties.isFullWidth = true;

                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedImageComponent screenId={screenId} elementId="test-empty-image-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedImageComponent screenId={screenId} elementId="test-image-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render in readOnly mode', () => {
                mockFieldProperties.isReadOnly = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedImageComponent screenId={screenId} elementId="test-image-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render in disabled mode without a value', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedImageComponent screenId={screenId} elementId="test-empty-image-field" />
                    </Provider>,
                );
                expect(container.querySelector('button')).toBeDisabled();
            });

            it('should render the remove icon when the field is editable', () => {
                const { queryByTestId } = render(
                    <Provider store={mockStore}>
                        <ConnectedImageComponent screenId={screenId} elementId="test-image-field" />
                    </Provider>,
                );
                expect(queryByTestId('e-image-field-remove')).not.toBeNull();
            });

            it('should not render the remove icon in disabled mode with a value', () => {
                mockFieldProperties.isDisabled = true;
                const { queryByTestId } = render(
                    <Provider store={mockStore}>
                        <ConnectedImageComponent screenId={screenId} elementId="test-image-field" />
                    </Provider>,
                );
                expect(queryByTestId('e-image-field-remove')).toBeNull();
            });

            it('should not render the remove icon in read only mode with a value', () => {
                mockFieldProperties.isReadOnly = true;
                const { queryByTestId } = render(
                    <Provider store={mockStore}>
                        <ConnectedImageComponent screenId={screenId} elementId="test-image-field" />
                    </Provider>,
                );
                expect(queryByTestId('e-image-field-remove')).toBeNull();
            });

            it('should render in readOnly mode using a conditional declaration', () => {
                mockFieldProperties.isReadOnly = (value: any) => {
                    return value;
                };

                let wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedImageComponent screenId={screenId} elementId="test-image-field" />
                    </Provider>,
                );
                expect(wrapper.queryByTestId('e-image-field-remove')).toBeNull();

                mockFieldProperties.isReadOnly = (value: any) => {
                    return !value;
                };
                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedImageComponent screenId={screenId} elementId="test-image-field" />
                    </Provider>,
                );
                expect(wrapper.queryByTestId('e-image-field-remove')).not.toBeNull();
            });

            it('should render in readOnly mode without a value', () => {
                mockFieldProperties.isReadOnly = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedImageComponent screenId={screenId} elementId="test-empty-image-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });
        });

        describe('Interactions', () => {
            describe('connected', () => {
                it('should render helperText', () => {
                    mockFieldProperties.helperText = 'This is a helper text';
                    const { container } = render(
                        <Provider store={mockStore}>
                            <ConnectedImageComponent screenId={screenId} elementId="test-image-field" />
                        </Provider>,
                    );
                    expect(container.querySelector('[data-element="help"]')).not.toBeNull();
                    expect(container.querySelector('[data-element="help"]')).toHaveTextContent('This is a helper text');
                });

                it('should render the helperText empty', () => {
                    mockFieldProperties.helperText = '';
                    const { container } = render(
                        <Provider store={mockStore}>
                            <ConnectedImageComponent screenId={screenId} elementId="test-image-field" />
                        </Provider>,
                    );
                    expect(container.querySelector('[data-element="help"]')).not.toBeNull();
                    expect(container.querySelector('[data-element="help"]')!.textContent).toEqual(' ');
                });
            });

            describe('unconnected', () => {
                let props: ImageComponentProps;

                beforeEach(() => {
                    props = {
                        elementId: 'test-image-field',
                        setFieldValue: jest.fn().mockResolvedValue(undefined),
                        value: imageValue,
                        screenId: 'screen',
                        validate: jest.fn(),
                        removeNonNestedErrors: jest.fn(),
                        onFocus: jest.fn(),
                        locale: 'en-US',
                        fieldProperties: {
                            bind: 'test-image-field',
                            title: 'Field Title',
                        },
                    };
                });

                it('should unset value when the user clicks the cross', () => {
                    const { queryByTestId } = render(<ImageComponent {...props} />);

                    expect(props.setFieldValue).not.toHaveBeenCalled();

                    fireEvent.click(queryByTestId('e-image-field-remove')!);

                    expect(props.setFieldValue).toHaveBeenCalledTimes(1);
                    expect((props.setFieldValue as jest.Mock).mock.calls[0][0]).toEqual(props.elementId);
                    expect((props.setFieldValue as jest.Mock).mock.calls[0][1]).toEqual(null);
                });
                /* 

                BL: The best I could do here, I am not sure how we could test the drag events and file selection
                
                it('should prevent default on dragging over', () => {
                    const {container} =render(<ImageComponent {...props} />);

                    const fakeEvent = {
                        stopPropagation: jest.fn(),
                        preventDefault: jest.fn(),
                    };

                    wrapper.find('.e-image-field-remove').simulate('dragover', fakeEvent);

                    expect(fakeEvent.stopPropagation).toHaveBeenCalledTimes(1);
                    expect(fakeEvent.preventDefault).toHaveBeenCalledTimes(1);
                }); 
                */
            });
        });
    });
});
