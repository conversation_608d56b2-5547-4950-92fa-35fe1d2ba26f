// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`XtremImageField connected Snapshots should render helperText 1`] = `
.c3 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c3:hover {
  color: #006437;
  background-color: transparent;
}

.c3::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: transparent;
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
  padding: 0px;
  width: 40px;
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0 .c2 {
  color: var(--colorsActionMajor500);
}

.c0:hover {
  background: var(--colorsActionMajor600);
  color: var(--colorsActionMajorYang100);
}

.c0:hover .c2 {
  color: var(--colorsActionMajorYang100);
}

.c0 .c2 {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1px;
  height: 20px;
  width: 20px;
}

.c0 .c2 svg {
  margin-top: 0;
}

.c1 .c2 {
  color: #99adb7ff;
  font-size: 1.6667rem;
  height: 24px;
  min-height: 24px;
}

.c1:hover {
  color: #000000a6;
}

.c1:hover .c2 {
  color: #000000a6;
}

.c1:disabled .c2 {
  color: #ccd6dbff;
}

.c1:disabled:hover {
  background-color: transparent;
}

.c1:disabled:hover .c2 {
  color: #ccd6dbff;
}

<div>
  <div
    class="e-field e-image-field"
    data-label="Test image"
    data-testid="e-image-field e-field-label-testImage e-field-bind-test-image-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test image
    </label>
    <div
      class="e-image-field-content-wrapper"
    >
      <div>
        <img
          alt="Test image"
          height="auto"
          src="data:image;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDADIiJSwlHzIsKSw4NTI7S31RS0VFS5ltc1p9tZ++u7Kfr6zI4f/zyNT/16yv+v/9////////wfD/////////////2wBDATU4OEtCS5NRUZP/zq/O////////////////////////////////////////////////////////////////////wAARCAAYAEADAREAAhEBAxEB/8QAGQAAAgMBAAAAAAAAAAAAAAAAAQMAAgQF/8QAJRABAAIBBAEEAgMAAAAAAAAAAQIRAAMSITEEEyJBgTORUWFx/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AOgM52xQDrjvAV5Xv0vfKUALlTQfeBm0HThMNHXkL0Lw/swN5qgA8yT4MCS1OEOJV8mBz9Z05yfW8iSx7p4j+jA1aD6Wj7ZMzstsfvAas4UyRHvjrAkC9KhpLMClQntlqFc2X1gUj4viwVObKrddH9YDoHvuujAEuNV+bLwFS8XxdSr+Cq3Vf+4F5RgQl6ZR2p1eAzU/HX80YBYyJLCuexwJCO2O1bwCRidAfWBSctswbI12GAJT3yiwFR7+MBjGK2g/WAJR3FdF84E2rK5VR0YH/9k="
          width="auto"
        />
      </div>
      <div
        class="e-image-field-remove"
      >
        <button
          aria-label="Remove"
          class="c0 c1"
          data-component="button"
          data-testid="e-image-field-remove"
          draggable="false"
          type="button"
        >
          <span
            aria-hidden="true"
            class="c2 c3"
            color="--colorsActionMajor500"
            data-component="icon"
            data-element="cross"
            data-role="icon"
            font-size="small"
            type="cross"
          />
        </button>
      </div>
    </div>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
      This is a helper text
    </span>
  </div>
</div>
`;

exports[`XtremImageField connected Snapshots should render hidden 1`] = `
.c3 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c3:hover {
  color: #006437;
  background-color: transparent;
}

.c3::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: transparent;
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
  padding: 0px;
  width: 40px;
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0 .c2 {
  color: var(--colorsActionMajor500);
}

.c0:hover {
  background: var(--colorsActionMajor600);
  color: var(--colorsActionMajorYang100);
}

.c0:hover .c2 {
  color: var(--colorsActionMajorYang100);
}

.c0 .c2 {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1px;
  height: 20px;
  width: 20px;
}

.c0 .c2 svg {
  margin-top: 0;
}

.c1 .c2 {
  color: #99adb7ff;
  font-size: 1.6667rem;
  height: 24px;
  min-height: 24px;
}

.c1:hover {
  color: #000000a6;
}

.c1:hover .c2 {
  color: #000000a6;
}

.c1:disabled .c2 {
  color: #ccd6dbff;
}

.c1:disabled:hover {
  background-color: transparent;
}

.c1:disabled:hover .c2 {
  color: #ccd6dbff;
}

<div>
  <div
    class="e-field e-image-field e-hidden"
    data-label="Test image"
    data-testid="e-image-field e-field-label-testImage e-field-bind-test-image-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test image
    </label>
    <div
      class="e-image-field-content-wrapper"
    >
      <div>
        <img
          alt="Test image"
          height="auto"
          src="data:image;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDADIiJSwlHzIsKSw4NTI7S31RS0VFS5ltc1p9tZ++u7Kfr6zI4f/zyNT/16yv+v/9////////wfD/////////////2wBDATU4OEtCS5NRUZP/zq/O////////////////////////////////////////////////////////////////////wAARCAAYAEADAREAAhEBAxEB/8QAGQAAAgMBAAAAAAAAAAAAAAAAAQMAAgQF/8QAJRABAAIBBAEEAgMAAAAAAAAAAQIRAAMSITEEEyJBgTORUWFx/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AOgM52xQDrjvAV5Xv0vfKUALlTQfeBm0HThMNHXkL0Lw/swN5qgA8yT4MCS1OEOJV8mBz9Z05yfW8iSx7p4j+jA1aD6Wj7ZMzstsfvAas4UyRHvjrAkC9KhpLMClQntlqFc2X1gUj4viwVObKrddH9YDoHvuujAEuNV+bLwFS8XxdSr+Cq3Vf+4F5RgQl6ZR2p1eAzU/HX80YBYyJLCuexwJCO2O1bwCRidAfWBSctswbI12GAJT3yiwFR7+MBjGK2g/WAJR3FdF84E2rK5VR0YH/9k="
          width="auto"
        />
      </div>
      <div
        class="e-image-field-remove"
      >
        <button
          aria-label="Remove"
          class="c0 c1"
          data-component="button"
          data-testid="e-image-field-remove"
          draggable="false"
          type="button"
        >
          <span
            aria-hidden="true"
            class="c2 c3"
            color="--colorsActionMajor500"
            data-component="icon"
            data-element="cross"
            data-role="icon"
            font-size="small"
            type="cross"
          />
        </button>
      </div>
    </div>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`XtremImageField connected Snapshots should render in full-width mode 1`] = `
.c4 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c4::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e940";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
  width: 100%;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0 .c3 {
  color: var(--colorsActionMajor500);
}

.c0:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c0:hover .c3 {
  color: var(--colorsActionMajorYang100);
}

.c0 .c3 {
  margin-bottom: 0px;
  margin-left: var(--spacing100);
  margin-right: 0px;
  height: 20px;
  width: 20px;
}

.c0 .c3 svg {
  margin-top: 0;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c1 {
  border-radius: var(--borderRadius050);
  background: transparent;
  padding: var(--spacing100);
  border-color: var(--colorsActionMinor500);
  color: var(--colorsActionMinor500);
  padding-left: var(--spacing150);
  padding-right: var(--spacing150);
}

.c1 .c3 {
  color: var(--colorsActionMinor500);
}

.c1:hover {
  color: var(--colorsActionMinorYang100);
  background: var(--colorsActionMinor600);
}

<div>
  <div
    class="e-field e-image-field e-full-width"
    data-label="Test image"
    data-testid="e-image-field e-field-label-testImage e-field-bind-test-empty-image-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test image
    </label>
    <div
      class="e-image-field-content-wrapper"
    >
      <div
        style="width: 100%; height: 48px; line-height: 48px;"
      >
        <button
          class="c0 c1"
          data-component="button-minor"
          draggable="false"
          type="button"
        >
          <span>
            <span
              class="c2"
              data-element="main-text"
            >
              <label
                for="inputFile"
              >
                Add an image
              </label>
              <input
                accept="image/*"
                class="e-hidden"
                id="inputFile"
                type="file"
              />
            </span>
          </span>
          <span
            aria-hidden="true"
            class="c3 c4"
            color="--colorsActionMajor500"
            data-component="icon"
            data-element="plus"
            data-role="icon"
            font-size="small"
            type="plus"
          />
        </button>
      </div>
    </div>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`XtremImageField connected Snapshots should render in readOnly mode 1`] = `
<div>
  <div
    class="e-field e-image-field e-read-only"
    data-label="Test image"
    data-testid="e-image-field e-field-label-testImage e-field-bind-test-image-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test image
    </label>
    <div
      class="e-image-field-content-wrapper"
    >
      <div>
        <img
          alt="Test image"
          height="auto"
          src="data:image;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDADIiJSwlHzIsKSw4NTI7S31RS0VFS5ltc1p9tZ++u7Kfr6zI4f/zyNT/16yv+v/9////////wfD/////////////2wBDATU4OEtCS5NRUZP/zq/O////////////////////////////////////////////////////////////////////wAARCAAYAEADAREAAhEBAxEB/8QAGQAAAgMBAAAAAAAAAAAAAAAAAQMAAgQF/8QAJRABAAIBBAEEAgMAAAAAAAAAAQIRAAMSITEEEyJBgTORUWFx/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AOgM52xQDrjvAV5Xv0vfKUALlTQfeBm0HThMNHXkL0Lw/swN5qgA8yT4MCS1OEOJV8mBz9Z05yfW8iSx7p4j+jA1aD6Wj7ZMzstsfvAas4UyRHvjrAkC9KhpLMClQntlqFc2X1gUj4viwVObKrddH9YDoHvuujAEuNV+bLwFS8XxdSr+Cq3Vf+4F5RgQl6ZR2p1eAzU/HX80YBYyJLCuexwJCO2O1bwCRidAfWBSctswbI12GAJT3yiwFR7+MBjGK2g/WAJR3FdF84E2rK5VR0YH/9k="
          width="auto"
        />
      </div>
    </div>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`XtremImageField connected Snapshots should render in readOnly mode without a value 1`] = `
.c1 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c1::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e93e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c2.c2 {
  color: inherit;
  height: inherit;
  min-width: inherit;
}

.c2.c2::before {
  font-size: 24px;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  min-width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: var(--borderRadiusCircle);
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div>
  <div
    class="e-field e-image-field e-read-only"
    data-label="Test image"
    data-testid="e-image-field e-field-label-testImage e-field-bind-test-empty-image-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test image
    </label>
    <div
      class="e-image-field-content-wrapper"
    >
      <div
        class="e-image-field-placeholder"
        data-testid="e-image-field-placeholder"
      >
        <div
          class="e-portrait"
        >
          <div
            class="c0"
            data-component="portrait"
            shape="circle"
          >
            <span
              class="c1 c2"
              data-component="icon"
              data-element="image"
              data-role="icon"
              font-size="small"
              type="image"
            />
          </div>
        </div>
      </div>
    </div>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`XtremImageField connected Snapshots should render with default properties 1`] = `
.c3 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c3:hover {
  color: #006437;
  background-color: transparent;
}

.c3::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: transparent;
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
  padding: 0px;
  width: 40px;
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0 .c2 {
  color: var(--colorsActionMajor500);
}

.c0:hover {
  background: var(--colorsActionMajor600);
  color: var(--colorsActionMajorYang100);
}

.c0:hover .c2 {
  color: var(--colorsActionMajorYang100);
}

.c0 .c2 {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1px;
  height: 20px;
  width: 20px;
}

.c0 .c2 svg {
  margin-top: 0;
}

.c1 .c2 {
  color: #99adb7ff;
  font-size: 1.6667rem;
  height: 24px;
  min-height: 24px;
}

.c1:hover {
  color: #000000a6;
}

.c1:hover .c2 {
  color: #000000a6;
}

.c1:disabled .c2 {
  color: #ccd6dbff;
}

.c1:disabled:hover {
  background-color: transparent;
}

.c1:disabled:hover .c2 {
  color: #ccd6dbff;
}

<div>
  <div
    class="e-field e-image-field"
    data-label="Test image"
    data-testid="e-image-field e-field-label-testImage e-field-bind-test-image-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test image
    </label>
    <div
      class="e-image-field-content-wrapper"
    >
      <div>
        <img
          alt="Test image"
          height="auto"
          src="data:image;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDADIiJSwlHzIsKSw4NTI7S31RS0VFS5ltc1p9tZ++u7Kfr6zI4f/zyNT/16yv+v/9////////wfD/////////////2wBDATU4OEtCS5NRUZP/zq/O////////////////////////////////////////////////////////////////////wAARCAAYAEADAREAAhEBAxEB/8QAGQAAAgMBAAAAAAAAAAAAAAAAAQMAAgQF/8QAJRABAAIBBAEEAgMAAAAAAAAAAQIRAAMSITEEEyJBgTORUWFx/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AOgM52xQDrjvAV5Xv0vfKUALlTQfeBm0HThMNHXkL0Lw/swN5qgA8yT4MCS1OEOJV8mBz9Z05yfW8iSx7p4j+jA1aD6Wj7ZMzstsfvAas4UyRHvjrAkC9KhpLMClQntlqFc2X1gUj4viwVObKrddH9YDoHvuujAEuNV+bLwFS8XxdSr+Cq3Vf+4F5RgQl6ZR2p1eAzU/HX80YBYyJLCuexwJCO2O1bwCRidAfWBSctswbI12GAJT3yiwFR7+MBjGK2g/WAJR3FdF84E2rK5VR0YH/9k="
          width="auto"
        />
      </div>
      <div
        class="e-image-field-remove"
      >
        <button
          aria-label="Remove"
          class="c0 c1"
          data-component="button"
          data-testid="e-image-field-remove"
          draggable="false"
          type="button"
        >
          <span
            aria-hidden="true"
            class="c2 c3"
            color="--colorsActionMajor500"
            data-component="icon"
            data-element="cross"
            data-role="icon"
            font-size="small"
            type="cross"
          />
        </button>
      </div>
    </div>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`XtremImageField connected Snapshots should render with set height and width 1`] = `
.c4 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c4::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e940";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
  width: 100%;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0 .c3 {
  color: var(--colorsActionMajor500);
}

.c0:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c0:hover .c3 {
  color: var(--colorsActionMajorYang100);
}

.c0 .c3 {
  margin-bottom: 0px;
  margin-left: var(--spacing100);
  margin-right: 0px;
  height: 20px;
  width: 20px;
}

.c0 .c3 svg {
  margin-top: 0;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c1 {
  border-radius: var(--borderRadius050);
  background: transparent;
  padding: var(--spacing100);
  border-color: var(--colorsActionMinor500);
  color: var(--colorsActionMinor500);
  padding-left: var(--spacing150);
  padding-right: var(--spacing150);
}

.c1 .c3 {
  color: var(--colorsActionMinor500);
}

.c1:hover {
  color: var(--colorsActionMinorYang100);
  background: var(--colorsActionMinor600);
}

<div>
  <div
    class="e-field e-image-field"
    data-label="Test image"
    data-testid="e-image-field e-field-label-testImage e-field-bind-test-empty-image-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test image
    </label>
    <div
      class="e-image-field-content-wrapper"
    >
      <div
        style="width: auto; height: 100px; line-height: 100px;"
      >
        <button
          class="c0 c1"
          data-component="button-minor"
          draggable="false"
          type="button"
        >
          <span>
            <span
              class="c2"
              data-element="main-text"
            >
              <label
                for="inputFile"
              >
                Add an image
              </label>
              <input
                accept="image/*"
                class="e-hidden"
                id="inputFile"
                type="file"
              />
            </span>
          </span>
          <span
            aria-hidden="true"
            class="c3 c4"
            color="--colorsActionMajor500"
            data-component="icon"
            data-element="plus"
            data-role="icon"
            font-size="small"
            type="plus"
          />
        </button>
      </div>
    </div>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`XtremImageField connected Snapshots should render without value 1`] = `
.c4 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c4::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e940";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
  width: 100%;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0 .c3 {
  color: var(--colorsActionMajor500);
}

.c0:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c0:hover .c3 {
  color: var(--colorsActionMajorYang100);
}

.c0 .c3 {
  margin-bottom: 0px;
  margin-left: var(--spacing100);
  margin-right: 0px;
  height: 20px;
  width: 20px;
}

.c0 .c3 svg {
  margin-top: 0;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c1 {
  border-radius: var(--borderRadius050);
  background: transparent;
  padding: var(--spacing100);
  border-color: var(--colorsActionMinor500);
  color: var(--colorsActionMinor500);
  padding-left: var(--spacing150);
  padding-right: var(--spacing150);
}

.c1 .c3 {
  color: var(--colorsActionMinor500);
}

.c1:hover {
  color: var(--colorsActionMinorYang100);
  background: var(--colorsActionMinor600);
}

<div>
  <div
    class="e-field e-image-field"
    data-label="Test image"
    data-testid="e-image-field e-field-label-testImage e-field-bind-test-empty-image-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test image
    </label>
    <div
      class="e-image-field-content-wrapper"
    >
      <div
        style="width: auto; height: 48px; line-height: 48px;"
      >
        <button
          class="c0 c1"
          data-component="button-minor"
          draggable="false"
          type="button"
        >
          <span>
            <span
              class="c2"
              data-element="main-text"
            >
              <label
                for="inputFile"
              >
                Add an image
              </label>
              <input
                accept="image/*"
                class="e-hidden"
                id="inputFile"
                type="file"
              />
            </span>
          </span>
          <span
            aria-hidden="true"
            class="c3 c4"
            color="--colorsActionMajor500"
            data-component="icon"
            data-element="plus"
            data-role="icon"
            font-size="small"
            type="plus"
          />
        </button>
      </div>
    </div>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;
