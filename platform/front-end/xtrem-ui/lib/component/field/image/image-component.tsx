import * as React from 'react';
import { connect } from 'react-redux';
import { localize } from '../../../service/i18n-service';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import type { PortraitPlaceholderMode } from '../../ui/portrait-component';
import { Portrait } from '../../ui/portrait-component';
import { StyledIconButton } from '../../ui/styled-icon-button';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { EditableFieldBaseComponent, mapDispatchToProps, mapReadonlyStateToProps } from '../field-base-component';
import type { NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { ImageComponentProps, ImageComponentState, ImageValue, ImageDecoratorProperties } from './image-types';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { getImageUrlFromValue } from './image-utils';
import { ContextType } from '../../../types';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import { resizeImage } from '../../../service/image-resize-service';

export class ImageComponent extends EditableFieldBaseComponent<
    ImageDecoratorProperties,
    ImageValue,
    NestedFieldsAdditionalProperties,
    ImageComponentState
> {
    private readonly fileRef: React.RefObject<HTMLInputElement>;

    constructor(props: ImageComponentProps) {
        super(props);
        this.fileRef = React.createRef<HTMLInputElement>();
        this.state = { isZooming: false };
    }

    private async readFile(file?: File): Promise<void> {
        if (file && file.type.indexOf('image/') === 0) {
            const resizedImage = await resizeImage(file);
            const reader = new FileReader();
            reader.onload = (): void => {
                if (reader.result) {
                    const components = reader.result.toString().split(',');
                    if (components.length > 1) {
                        handleChange(
                            this.props.elementId,
                            { value: components[1] },
                            this.props.setFieldValue,
                            this.props.validate,
                            this.triggerChangeListener,
                        );
                    }
                }
            };
            reader.readAsDataURL(resizedImage);
        }
    }

    private readonly getPlaceholderValue = (): string | undefined =>
        resolveByValue({
            propertyValue: this.props.fieldProperties.placeholderValue,
            skipHexFormat: true,
            screenId: this.props.screenId,
            // Intentionally undefined, the validation rule should not depend on the value that is validated by that very rule
            rowValue: undefined,
            // Pass in the row value as the field value because the field value is passed in as the first argument to the functional code
            fieldValue: this.props.handlersArguments?.rowValue,
        });

    private readonly getPlaceholderMode = (): PortraitPlaceholderMode | undefined =>
        resolveByValue<PortraitPlaceholderMode>({
            propertyValue: this.props.fieldProperties.placeholderMode,
            skipHexFormat: true,
            screenId: this.props.screenId,
            // Intentionally undefined, the validation rule should not depend on the value that is validated by that very rule
            rowValue: undefined,
            // Pass in the row value as the field value because the field value is passed in as the first argument to the functional code
            fieldValue: this.props.handlersArguments?.rowValue,
        });

    private readonly onDragOver = (event: React.DragEvent<HTMLDivElement>): void => {
        event.stopPropagation();
        event.preventDefault();
    };

    private readonly onDrop = (event: React.DragEvent<HTMLDivElement>): void => {
        event.preventDefault();
        if (event.dataTransfer.items) {
            if (event.dataTransfer.items && event.dataTransfer.items.length > 0 && event.dataTransfer.items[0].kind) {
                const file = event.dataTransfer.items[0].getAsFile();
                if (file) {
                    this.readFile(file);
                }
            } else if (event.dataTransfer.files.length) {
                this.readFile(event.dataTransfer.files[0]);
            }
        }
    };

    private readonly onCreateElementClick = (): void => this.fileRef.current?.click();

    private readonly onFileChanged = (event: React.ChangeEvent<HTMLInputElement>): void => {
        if (event.target.files && event.target.files.length > 0) {
            this.readFile(event.target.files[0]);
        }
    };

    private readonly onRemoveImage = (): void =>
        handleChange(
            this.props.elementId,
            null,
            this.props.setFieldValue,
            this.props.validate,
            this.triggerChangeListener,
        );

    private readonly onFullScreenOpen = (): void => this.setState({ isZooming: true });

    private readonly onFullScreenClose = (): void => this.setState({ isZooming: false });

    private readonly renderRemoveIcon = (): React.ReactNode => (
        <div className="e-image-field-remove">
            <StyledIconButton
                data-testid="e-image-field-remove"
                onClick={this.onRemoveImage}
                iconType="cross"
                iconTooltipMessage={localize('@sage/xtrem-ui/image-field-remove-image', 'Remove')}
                aria-label={localize('@sage/xtrem-ui/image-field-remove-image', 'Remove')}
                buttonType="tertiary"
            />
        </div>
    );

    private readonly renderReadOnlyPlaceholder = (): React.ReactNode => (
        <div className="e-image-field-placeholder" data-testid="e-image-field-placeholder">
            <Portrait
                size="M"
                placeholderMode={this.getPlaceholderMode()}
                placeholderValue={this.getPlaceholderValue()}
                icon="image"
            />
        </div>
    );

    private readonly renderValueBody = (width: string): React.ReactNode => (
        <div onDoubleClick={!this.isDisabled() ? this.onFullScreenOpen : undefined}>
            {!this.props.isNested && (
                <img
                    src={getImageUrlFromValue(this.getValue()?.value)}
                    alt={this.getTitle() || this.getPlaceholderValue() || ''}
                    width={width}
                    height={this.props.fieldProperties.height ? this.props.fieldProperties.height : 'auto'}
                />
            )}
            {this.props.isNested && (
                <Portrait
                    image={{ value: `${this.getValue()?.value}` }}
                    placeholderValue={this.getTitle() || this.getPlaceholderValue() || ''}
                    shape={this.props.fieldProperties.shape}
                    size="M"
                />
            )}
        </div>
    );

    private readonly renderFullScreenView = (): React.ReactNode => (
        <div
            className="e-image-field-full-screen"
            onDoubleClick={!this.isDisabled() ? this.onFullScreenClose : undefined}
        >
            <img
                src={getImageUrlFromValue(this.getValue()?.value)}
                alt={this.getTitle() || this.getPlaceholderValue() || ''}
            />
        </div>
    );

    private renderUploadArea(width: string): React.ReactNode {
        const uploadBoxHeight = this.props.fieldProperties.height ? this.props.fieldProperties.height : '48px';
        return (
            <div
                style={{ width, height: uploadBoxHeight, lineHeight: uploadBoxHeight }}
                onDrop={this.onDrop}
                onDragOver={this.onDragOver}
            >
                <ButtonMinor
                    buttonType="secondary"
                    iconType="plus"
                    iconPosition="after"
                    fullWidth={true}
                    onClick={!this.isDisabled() ? this.onCreateElementClick : undefined}
                    disabled={this.isDisabled()}
                >
                    {/* eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions */}
                    <label htmlFor="inputFile" onClick={(e): void => e.preventDefault()}>
                        {localize('@sage/xtrem-ui/image-component-add-image', 'Add an image')}
                    </label>
                    <input
                        id="inputFile"
                        type="file"
                        className="e-hidden"
                        ref={this.fileRef}
                        accept="image/*"
                        onChange={this.onFileChanged}
                    />
                </ButtonMinor>
            </div>
        );
    }

    render(): React.ReactNode {
        let width;
        if (this.props.fieldProperties.width) {
            width = this.props.fieldProperties.width;
        } else if (this.props.fieldProperties.isFullWidth) {
            width = '100%';
        } else {
            width = 'auto';
        }
        const value = this.getValue();
        const hasValue = value && value.value;
        const isReadOnly = this.isReadOnly();
        const isDisabled = this.isDisabled();
        const title = this.getTitle();
        const hasTitle = !this.props.fieldProperties.isTitleHidden && title !== '' && title !== undefined;
        const isTable =
            this.props.contextType === ContextType.table || this.props.contextType === ContextType.tableSummary;
        return (
            <div
                {...this.getBaseAttributesDivWrapper(
                    'image',
                    'e-image-field',
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    this.props.isNested,
                )}
                onClick={!isDisabled ? this.getClickHandler() : undefined}
            >
                {hasTitle && <FieldLabel label={title} />}
                <div className="e-image-field-content-wrapper">
                    {hasValue && this.renderValueBody(width)}
                    {hasValue && this.state.isZooming && this.renderFullScreenView()}
                    {hasValue && !isReadOnly && !isDisabled && !isTable && this.renderRemoveIcon()}
                    {!hasValue && isReadOnly && this.renderReadOnlyPlaceholder()}
                    {!hasValue && !isReadOnly && this.renderUploadArea(width)}
                </div>
                <HelperText helperText={this.props.fieldProperties.helperText} />
            </div>
        );
    }
}

export const ConnectedImageComponent = connect(mapReadonlyStateToProps(), mapDispatchToProps())(ImageComponent);

export default ConnectedImageComponent;
