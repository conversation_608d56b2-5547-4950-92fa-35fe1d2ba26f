import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedPropertiesWrapper } from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import type { PortraitShape } from '../../ui/portrait-component';
import type { BaseEditableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasImagePlaceholder,
    HasNestedImagePlaceholder,
    HasParent,
    Mappable,
    Nested,
    NestedChangeable,
    NestedClickable,
    Sizable,
} from '../traits';

export interface ImageDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<ImageProperties<CT>, '_controlObjectType'>,
        Changeable<CT>,
        Clickable<CT>,
        Mappable<CT>,
        HasParent<CT, BlockControlObject<CT>>,
        HasImagePlaceholder<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        Sizable {}

export interface NestedImageProperties<CT extends ScreenBase = ScreenBase, NodeType extends ClientNode = any>
    extends NestedPropertiesWrapper<ImageProperties<CT>>,
        Nested<NodeType>,
        NestedChangeable<CT>,
        NestedClickable<CT, NodeType>,
        HasNestedImagePlaceholder<CT, NodeType>,
        Mappable<CT>,
        Sizable {}

export interface ImageProperties<CT extends ScreenBase = ScreenBase> extends EditableFieldProperties<CT> {
    /** The value of the height attribute of the HTML image (e.g. 100px, 75%, auto, etc.)*/
    height?: string;
    shape?: PortraitShape;
}

export interface ImageValue {
    value: string;
}

export interface ImageComponentState {
    isZooming: boolean;
}

export type ImageComponentProps = BaseEditableComponentProperties<
    ImageDecoratorProperties,
    ImageValue,
    NestedFieldsAdditionalProperties
>;
