import * as nestedFields from '../../../nested-fields';
import type { GraphQLFilter } from '../../../../service/graphql-utils';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import { DetailListControlObject } from '../../../control-objects';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';
import type { DetailListDecoratorProperties, DetailListProperties } from '../detail-list-types';

describe('Detail List Field', () => {
    let detailListField: DetailListControlObject;
    let fieldProperties: DetailListProperties;

    beforeEach(() => {
        fieldProperties = {
            title: 'TEST_FIELD_TITLE',
            fields: [
                nestedFields.text({
                    bind: 'product',
                    title: 'Product:',
                    canFilter: true,
                    isReadOnly: true,
                }),
                nestedFields.text({
                    bind: 'description',
                    title: 'Description',
                    canFilter: true,
                    isReadOnly: true,
                }),
                nestedFields.text({
                    bind: 'st',
                    title: 'St',
                    canFilter: true,
                    isReadOnly: true,
                }),
                nestedFields.numeric({
                    bind: 'qty',
                    title: 'Quantity',
                    scale: 0,
                    canFilter: true,
                    isReadOnly: true,
                }),
            ],
            orderBy: {
                product: 1,
                st: -1,
            },
        };
    });

    describe('getters and setters', () => {
        const detailListValue = [
            {
                product: 'product_0',
                description: 'description_0',
                st: '0',
                qty: 0,
            },
            {
                product: 'product_1',
                description: 'description_1',
                st: '1',
                qty: 1,
            },
        ];

        beforeEach(() => {
            detailListField = buildControlObject<FieldKey.DetailList>(DetailListControlObject, {
                fieldValue: {
                    data: detailListValue,
                    pageInfo: {},
                },
                fieldProperties,
            });
        });

        it('getting field value', () => {
            expect(detailListField.value).toEqual(detailListValue);
        });

        it('getting title', () => {
            expect(detailListField.title).toEqual('TEST_FIELD_TITLE');
        });

        it('should set the title', () => {
            const testFixture = 'Test Detail List Field Title';
            expect(fieldProperties.title).not.toEqual(testFixture);
            detailListField.title = testFixture;
            expect(fieldProperties.title).toEqual(testFixture);
        });

        it('should set the helper text', () => {
            const testFixture = 'Test Detail List Field Helper Text';
            expect(fieldProperties.helperText).not.toEqual(testFixture);
            detailListField.helperText = testFixture;
            expect(fieldProperties.helperText).toEqual(testFixture);
        });

        it('should set isDisabled property', () => {
            const testFixture = true;
            expect(fieldProperties.isDisabled).not.toEqual(testFixture);
            detailListField.isDisabled = testFixture;
            expect(fieldProperties.isDisabled).toEqual(testFixture);
        });

        it('should set isHelperTextHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isHelperTextHidden).not.toEqual(testFixture);
            detailListField.isHelperTextHidden = testFixture;
            expect(fieldProperties.isHelperTextHidden).toEqual(testFixture);
        });

        it('should set isHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isHidden).not.toEqual(testFixture);
            detailListField.isHidden = testFixture;
            expect(fieldProperties.isHidden).toEqual(testFixture);
        });

        it('should set isTitleHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isTitleHidden).not.toEqual(testFixture);
            detailListField.isTitleHidden = testFixture;
            expect(fieldProperties.isTitleHidden).toEqual(testFixture);
        });

        it('should set value', () => {
            const testFixture = [{ _id: '3', someField: 'asd' }] as Partial<any>[];
            detailListField.value = testFixture;
            expect(detailListField.value).toEqual(testFixture);
        });

        it('should set value to an empty array if falsy value is set', () => {
            const testFixture = null as any;
            detailListField.value = testFixture;
            expect(detailListField.value).toEqual([]);
        });

        describe('GraphQl filter property', () => {
            let setUiComponentProperties: jest.Mock<void, [string, string, DetailListDecoratorProperties]>;
            let refresh: jest.Mock<Promise<FieldInternalValue<FieldKey.DetailList>>>;
            let newProperties: DetailListDecoratorProperties;

            beforeEach(() => {
                setUiComponentProperties = jest.fn(
                    (_screenId: string, _elementId: string, _value: DetailListDecoratorProperties) => {
                        newProperties = { ..._value };
                    },
                );

                refresh = jest.fn();
                detailListField = createFieldControlObject<FieldKey.DetailList>(
                    FieldKey.DetailList,
                    'detailListTest',
                    DetailListControlObject,
                    'detailListFieldID',
                    {
                        data: detailListValue,
                        pageInfo: {},
                    },
                    fieldProperties,
                    { setUiComponentProperties, refresh },
                );
            });

            const executeTest = async (filter: GraphQLFilter | (() => GraphQLFilter)) => {
                expect(detailListField.filter).toBeUndefined();
                detailListField.filter = filter;
                expect(setUiComponentProperties).toHaveBeenCalled();
                expect(refresh).toHaveBeenCalled();
                expect(newProperties.filter).toEqual(filter);
            };

            it('should update filter with GraphQL filter object', () => {
                executeTest({ description: { _regex: 'policy', _options: 'i' } });
            });

            it('should update filter with function', () => {
                executeTest(() => ({ description: { _regex: 'policy', _options: 'i' } }));
            });
        });
    });
});
