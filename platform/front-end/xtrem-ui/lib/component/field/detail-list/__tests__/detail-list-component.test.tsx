import {
    getMockState,
    getMockStore,
    getMockPageDefinition,
    applyActionMocks,
} from '../../../../__tests__/test-helpers';

jest.mock('../../text/async-text-component');
jest.mock('../../reference/async-reference-component');
jest.mock('../../numeric/async-numeric-component');

import type { DetailListComponentProps } from '../detail-list-types';
import { nestedFields } from '../../../..';
import type { GraphqlCollection } from '../../../types';
import { DetailListComponent } from '../detail-list-component';
import React from 'react';
import * as events from '../../../../utils/events';
import { Provider } from 'react-redux';
import { cleanup, fireEvent, render } from '@testing-library/react';

describe('Detail List Component', () => {
    let detailListMockProperties: DetailListComponentProps;
    let stOnClickMock: jest.Mock<any, any>;
    let mockStore;
    const screenId = 'pageId';
    beforeEach(() => {
        stOnClickMock = jest.fn();
        detailListMockProperties = {
            elementId: 'elementId',
            isLoading: false,
            loadDetailListData: jest.fn(),
            screenId,
            setFieldValue: jest.fn().mockResolvedValue(undefined),
            validate: jest.fn(),
            removeNonNestedErrors: jest.fn(),
            onFocus: jest.fn(),
            locale: 'en-US',
            value: {
                pageInfo: {
                    endCursor: 'endCursor',
                    hasNextPage: false,
                },
                data: [
                    { product: 'product1', description: 'description1', st: 'st1', qty: 1, _id: '1' },
                    { product: 'product2', description: 'description2', st: 'st2', qty: 2, _id: '2' },
                    { product: 'product3', description: 'description3', st: 'st2', qty: 3, _id: '3' },
                ],
            } as GraphqlCollection,
            fieldProperties: {
                fields: [
                    nestedFields.text<any, any>({
                        bind: 'product',
                        title: 'Product:',
                        canFilter: true,
                        isReadOnly: true,
                    }),
                    nestedFields.text<any, any>({
                        bind: 'description',
                        title: 'Description',
                        canFilter: true,
                        isReadOnly: true,
                    }),
                    nestedFields.text<any, any>({
                        bind: 'st',
                        title: 'St',
                        canFilter: true,
                        onClick: stOnClickMock,
                    }),
                    nestedFields.numeric({
                        bind: 'qty',
                        title: 'Quantity',
                        scale: 0,
                        canFilter: true,
                        isReadOnly: true,
                    }),
                ],
            },
        };
        const mockState = getMockState();
        mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        mockStore = getMockStore(mockState);
    });

    const renderComponent = (props: DetailListComponentProps) =>
        render(
            <Provider store={mockStore}>
                <DetailListComponent {...props} />
            </Provider>,
        );

    afterEach(() => {
        applyActionMocks();
        cleanup();
        jest.resetAllMocks();
    });

    it('should render a list with 12 items (4 fields * 3 values = 12 items)', () => {
        const { container } = renderComponent({ ...detailListMockProperties });
        const items = container.querySelectorAll('.e-detail-list-item');
        expect(items).toHaveLength(12);
        const separators = container.querySelectorAll('.e-detail-list-first-item');
        expect(separators).toHaveLength(3);
    });

    it('should render with a title', () => {
        detailListMockProperties.fieldProperties.title = 'TEST TITLE';
        const { queryByTestId } = renderComponent({ ...detailListMockProperties });
        const title = queryByTestId('e-field-label');
        expect(title).toHaveTextContent('TEST TITLE');
    });

    it('should not render the title when it is hidden ', () => {
        detailListMockProperties.fieldProperties.title = 'TEST TITLE';
        detailListMockProperties.fieldProperties.isTitleHidden = true;
        const { queryByTestId } = renderComponent({ ...detailListMockProperties });
        const title = queryByTestId('e-field-label');
        expect(title).toBeNull();
    });

    it('should render with a helper text', () => {
        detailListMockProperties.fieldProperties.helperText = 'TEST HELPER TEXT';
        const { queryByTestId } = renderComponent({ ...detailListMockProperties });
        const helperText = queryByTestId('e-field-helper-text');
        expect(helperText).toHaveTextContent('TEST HELPER TEXT');
    });

    it('should not the helper text when it is hidden', () => {
        detailListMockProperties.fieldProperties.helperText = 'TEST HELPER TEXT';
        detailListMockProperties.fieldProperties.isHelperTextHidden = true;
        const { queryByTestId } = renderComponent({ ...detailListMockProperties });
        const helperText = queryByTestId('e-field-helper-text');
        expect(helperText).toBeNull();
    });

    it('should call loadDetailListData on click load more button', () => {
        const mockLoadDetailListData = jest.fn();
        detailListMockProperties.loadDetailListData = mockLoadDetailListData;
        detailListMockProperties.value!.pageInfo.hasNextPage = true;
        detailListMockProperties.fieldProperties.filter = {
            qty: { _gt: '15' },
        };
        const { queryByTestId } = renderComponent({ ...detailListMockProperties });
        fireEvent.click(queryByTestId('e-load-more-button-container')?.querySelector('button')!);

        expect(mockLoadDetailListData).toHaveBeenCalled();
        expect(mockLoadDetailListData).toHaveBeenCalledWith(
            detailListMockProperties.screenId,
            detailListMockProperties.elementId,
            detailListMockProperties.fieldProperties.fields,
            { after: 'endCursor', filter: '{"qty":{"_gt":"15"}}' },
            undefined,
        );
    });

    it('should call onRecordClick when clicking on a item', () => {
        const { container } = renderComponent({ ...detailListMockProperties });
        const items = container.querySelectorAll('.e-detail-list-item');
        fireEvent.click(items[0]);
        expect(events.triggerFieldEvent).toHaveBeenCalledTimes(1);
        expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
            1,
            'pageId',
            'elementId',
            'onRecordClick',
            detailListMockProperties.value!.data[0],
        );
        fireEvent.click(items[4]);
        expect(events.triggerFieldEvent).toHaveBeenCalledTimes(2);
        expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
            2,
            'pageId',
            'elementId',
            'onRecordClick',
            detailListMockProperties.value!.data[1],
        );
        fireEvent.click(items[8]);
        expect(events.triggerFieldEvent).toHaveBeenCalledTimes(3);
        expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
            3,
            'pageId',
            'elementId',
            'onRecordClick',
            detailListMockProperties.value!.data[2],
        );
    });

    it('should match the snapshot', () => {
        const { container } = renderComponent({ ...detailListMockProperties });
        expect(container).toMatchSnapshot();
    });

    it('should apply e-hidden className because isHidden is true', () => {
        detailListMockProperties.fieldProperties.isHidden = true;
        const { container } = renderComponent({ ...detailListMockProperties });
        expect(container.querySelector('.e-hidden')).not.toBeNull();
    });

    describe('hidden rows', () => {
        it('should hide a row if the nested items based on the isHidden property', () => {
            detailListMockProperties.fieldProperties.fields[0] = nestedFields.text<any, any>({
                bind: 'product',
                title: 'Product:',
                canFilter: true,
                isReadOnly: true,
            });
            let wrapper = renderComponent({ ...detailListMockProperties });
            expect(wrapper.container.querySelectorAll('[data-testid*="e-detail-list-item-bind-product"]')).toHaveLength(
                3,
            );
            detailListMockProperties.fieldProperties.fields[0] = nestedFields.text<any, any>({
                bind: 'product',
                title: 'Product:',
                canFilter: true,
                isReadOnly: true,
                isHidden: true,
            });
            wrapper = renderComponent({ ...detailListMockProperties });
            expect(wrapper.container.querySelectorAll('[data-testid*="e-detail-list-item-bind-product"]')).toHaveLength(
                0,
            );
        });

        it('should hide a row if the nested items based on isHidden callback', () => {
            detailListMockProperties.fieldProperties.fields[0] = nestedFields.text<any, any>({
                bind: 'product',
                title: 'Product:',
                canFilter: true,
                isReadOnly: true,
            });
            let wrapper = renderComponent({ ...detailListMockProperties });
            expect(wrapper.container.querySelectorAll('[data-testid*="e-detail-list-item-bind-product"]')).toHaveLength(
                3,
            );
            detailListMockProperties.fieldProperties.fields[0] = nestedFields.text<any, any>({
                bind: 'product',
                title: 'Product:',
                canFilter: true,
                isReadOnly: true,
                isHidden(value: any, rowValue: any) {
                    return Number(rowValue.qty) % 2 === 0;
                },
            });
            wrapper = renderComponent({ ...detailListMockProperties });
            expect(wrapper.container.querySelectorAll('[data-testid*="e-detail-list-item-bind-product"]')).toHaveLength(
                2,
            );
        });

        it('should merge split values for isHidden callback', () => {
            detailListMockProperties.value = {
                pageInfo: {
                    endCursor: 'endCursor',
                    hasNextPage: false,
                },
                data: [
                    {
                        product: 'product1',
                        description: 'description1',
                        st: 'st1',
                        qty: 1,
                        _id: '1',
                        fragmentedReference__value: { _id: 1, someProperty: 2 },
                        fragmentedReference__anotherValue: { _id: 1, anotherProperty: 3 },
                    },
                    {
                        product: 'product2',
                        description: 'description2',
                        st: 'st2',
                        qty: 2,
                        _id: '2',
                        fragmentedReference__value: { _id: 1, someProperty: 2 },
                        fragmentedReference__anotherValue: { _id: 1, anotherProperty: 3 },
                    },
                    {
                        product: 'product3',
                        description: 'description3',
                        st: 'st2',
                        qty: 3,
                        _id: '3',
                        fragmentedReference__value: { _id: 1, someProperty: 2 },
                        fragmentedReference__anotherValue: { _id: 1, anotherProperty: 3 },
                    },
                ],
            } as GraphqlCollection;

            const isHiddenMock = jest.fn(() => true);

            detailListMockProperties.fieldProperties.fields[0] = nestedFields.text<any, any>({
                bind: 'product',
                title: 'Product:',
                canFilter: true,
                isReadOnly: true,
                isHidden: isHiddenMock,
            });

            renderComponent({ ...detailListMockProperties });
            expect(isHiddenMock).toHaveBeenCalledTimes(3);
            expect(isHiddenMock).toHaveBeenNthCalledWith(1, 'product1', {
                _id: '1',
                description: 'description1',
                fragmentedReference: { _id: 1, anotherProperty: 3, someProperty: 2 },
                product: 'product1',
                qty: 1,
                st: 'st1',
            });
            expect(isHiddenMock).toHaveBeenNthCalledWith(2, 'product2', {
                _id: '2',
                description: 'description2',
                fragmentedReference: { _id: 1, anotherProperty: 3, someProperty: 2 },
                product: 'product2',
                qty: 2,
                st: 'st2',
            });
            expect(isHiddenMock).toHaveBeenNthCalledWith(3, 'product3', {
                _id: '3',
                description: 'description3',
                fragmentedReference: { _id: 1, anotherProperty: 3, someProperty: 2 },
                product: 'product3',
                qty: 3,
                st: 'st2',
            });
        });

        it('should merge split values for isHidden callback for reference field', () => {
            const mockState = getMockState();
            mockState.screenDefinitions.pageId = getMockPageDefinition('pageId');
            mockStore = getMockStore(mockState);
            detailListMockProperties.value = {
                pageInfo: {
                    endCursor: 'endCursor',
                    hasNextPage: false,
                },
                data: [
                    {
                        product: 'product1',
                        description: 'description1',
                        st: 'st1',
                        qty: 1,
                        _id: '1',
                        fragmentedReference__value: { _id: 1, someProperty: 2 },
                        fragmentedReference__anotherProperty: { _id: 1, anotherProperty: 3 },
                    },
                    {
                        product: 'product2',
                        description: 'description2',
                        st: 'st2',
                        qty: 2,
                        _id: '2',
                        fragmentedReference__value: { _id: 1, someProperty: 2 },
                        fragmentedReference__anotherProperty: { _id: 1, anotherProperty: 3 },
                    },
                    {
                        product: 'product3',
                        description: 'description3',
                        st: 'st2',
                        qty: 3,
                        _id: '3',
                        fragmentedReference__value: { _id: 1, someProperty: 2 },
                        fragmentedReference__anotherProperty: { _id: 1, anotherProperty: 3 },
                    },
                ],
            } as GraphqlCollection;

            const isHiddenMock = jest.fn(() => {
                return false;
            });

            detailListMockProperties.fieldProperties.fields.push(
                nestedFields.reference({
                    bind: 'fragmentedReference',
                    title: 'Fragmented Reference',
                    node: '@sage/xtrem-test/AnyNode',
                    valueField: 'anotherProperty',
                    isHidden: isHiddenMock,
                }),
            );

            renderComponent({ ...detailListMockProperties });
            expect(isHiddenMock).toHaveBeenCalledTimes(6);
            expect(isHiddenMock).toHaveBeenNthCalledWith(
                1,
                { _id: 1, anotherProperty: 3, someProperty: 2 },
                {
                    _id: '1',
                    description: 'description1',
                    fragmentedReference: { _id: 1, anotherProperty: 3, someProperty: 2 },
                    product: 'product1',
                    qty: 1,
                    st: 'st1',
                },
            );
            expect(isHiddenMock).toHaveBeenNthCalledWith(
                2,
                { _id: 1, anotherProperty: 3, someProperty: 2 },
                {
                    _id: '2',
                    description: 'description2',
                    fragmentedReference: { _id: 1, anotherProperty: 3, someProperty: 2 },
                    product: 'product2',
                    qty: 2,
                    st: 'st2',
                },
            );
            expect(isHiddenMock).toHaveBeenNthCalledWith(
                3,
                { _id: 1, anotherProperty: 3, someProperty: 2 },
                {
                    _id: '3',
                    description: 'description3',
                    fragmentedReference: { _id: 1, anotherProperty: 3, someProperty: 2 },
                    product: 'product3',
                    qty: 3,
                    st: 'st2',
                },
            );
            expect(isHiddenMock).toHaveBeenNthCalledWith(
                4,
                { _id: 1, anotherProperty: 3, someProperty: 2 },
                {
                    _id: '1',
                    description: 'description1',
                    fragmentedReference: { _id: 1, anotherProperty: 3, someProperty: 2 },
                    product: 'product1',
                    qty: 1,
                    st: 'st1',
                },
            );
            expect(isHiddenMock).toHaveBeenNthCalledWith(
                5,
                { _id: 1, anotherProperty: 3, someProperty: 2 },
                {
                    _id: '2',
                    description: 'description2',
                    fragmentedReference: { _id: 1, anotherProperty: 3, someProperty: 2 },
                    product: 'product2',
                    qty: 2,
                    st: 'st2',
                },
            );
            expect(isHiddenMock).toHaveBeenNthCalledWith(
                6,
                { _id: 1, anotherProperty: 3, someProperty: 2 },
                {
                    _id: '3',
                    description: 'description3',
                    fragmentedReference: { _id: 1, anotherProperty: 3, someProperty: 2 },
                    product: 'product3',
                    qty: 3,
                    st: 'st2',
                },
            );
        });

        it('should hide nested items with isHiddenDesktop flag on desktop viewport', () => {
            detailListMockProperties.browser = {
                greaterThan: { s: true, xs: true, m: false, l: false },
            } as any;

            detailListMockProperties.fieldProperties.fields[0] = nestedFields.text<any, any>({
                bind: 'product',
                title: 'Product:',
                canFilter: true,
                isReadOnly: true,
            });
            let wrapper = renderComponent({ ...detailListMockProperties });
            expect(wrapper.container.querySelectorAll('[data-testid*="e-detail-list-item-bind-product"]')).toHaveLength(
                3,
            );

            detailListMockProperties.fieldProperties.fields[0] = nestedFields.text<any, any>({
                bind: 'product',
                title: 'Product:',
                canFilter: true,
                isReadOnly: true,
                isHiddenDesktop: true,
            });
            wrapper = renderComponent({ ...detailListMockProperties });
            expect(wrapper.container.querySelectorAll('[data-testid*="e-detail-list-item-bind-product"]')).toHaveLength(
                0,
            );
        });

        it('should not hide nested items with isHiddenDesktop flag on mobile viewport', () => {
            detailListMockProperties.browser = {
                greaterThan: { s: false, xs: false, m: false, l: false },
            } as any;

            detailListMockProperties.fieldProperties.fields[0] = nestedFields.text<any, any>({
                bind: 'product',
                title: 'Product:',
                canFilter: true,
                isReadOnly: true,
            });
            let wrapper = renderComponent({ ...detailListMockProperties });
            expect(wrapper.container.querySelectorAll('[data-testid*="e-detail-list-item-bind-product"]')).toHaveLength(
                3,
            );
            detailListMockProperties.fieldProperties.fields[0] = nestedFields.text<any, any>({
                bind: 'product',
                title: 'Product:',
                canFilter: true,
                isReadOnly: true,
                isHiddenDesktop: true,
            });
            wrapper = renderComponent({ ...detailListMockProperties });
            expect(wrapper.container.querySelectorAll('[data-testid*="e-detail-list-item-bind-product"]')).toHaveLength(
                3,
            );
        });

        it('should hide nested items with isHiddenMobile flag on mobile viewport', () => {
            detailListMockProperties.browser = {
                greaterThan: { s: false, xs: false, m: false, l: false },
            } as any;

            detailListMockProperties.fieldProperties.fields[0] = nestedFields.text<any, any>({
                bind: 'product',
                title: 'Product:',
                canFilter: true,
                isReadOnly: true,
            });
            let wrapper = renderComponent({ ...detailListMockProperties });
            expect(wrapper.container.querySelectorAll('[data-testid*="e-detail-list-item-bind-product"]')).toHaveLength(
                3,
            );
            detailListMockProperties.fieldProperties.fields[0] = nestedFields.text<any, any>({
                bind: 'product',
                title: 'Product:',
                canFilter: true,
                isReadOnly: true,
                isHiddenMobile: true,
            });
            wrapper = renderComponent({ ...detailListMockProperties });
            expect(wrapper.container.querySelectorAll('[data-testid*="e-detail-list-item-bind-product"]')).toHaveLength(
                0,
            );
        });

        it('should node hide nested items with isHiddenMobile flag on desktop viewport', () => {
            detailListMockProperties.browser = {
                greaterThan: { s: true, xs: true, m: false, l: false },
            } as any;

            detailListMockProperties.fieldProperties.fields[0] = nestedFields.text<any, any>({
                bind: 'product',
                title: 'Product:',
                canFilter: true,
                isReadOnly: true,
            });
            let wrapper = renderComponent({ ...detailListMockProperties });
            expect(wrapper.container.querySelectorAll('[data-testid*="e-detail-list-item-bind-product"]')).toHaveLength(
                3,
            );
            detailListMockProperties.fieldProperties.fields[0] = nestedFields.text<any, any>({
                bind: 'product',
                title: 'Product:',
                canFilter: true,
                isReadOnly: true,
                isHiddenMobile: true,
            });
            wrapper = renderComponent({ ...detailListMockProperties });
            expect(wrapper.container.querySelectorAll('[data-testid*="e-detail-list-item-bind-product"]')).toHaveLength(
                3,
            );
        });

        it('should trigger the onRecordClick element with the right value', () => {
            detailListMockProperties.value = {
                pageInfo: {
                    endCursor: 'endCursor',
                    hasNextPage: false,
                },
                data: [
                    {
                        product: 'product1',
                        description: 'description1',
                        st: 'st1',
                        qty: 1,
                        nestedReference: {
                            _id: '1234',
                            reference__title: {
                                _id: '3',
                                title: 'The title',
                            },
                            reference__status: {
                                _id: '3',
                                status: 'good',
                            },
                        },
                    },
                ],
            } as GraphqlCollection;

            detailListMockProperties.fieldProperties.fields[0] = nestedFields.text<any, any>({
                bind: 'product',
                title: 'Product:',
                canFilter: true,
                isReadOnly: true,
            });
            const { container } = renderComponent({ ...detailListMockProperties });
            expect(events.triggerFieldEvent).not.toHaveBeenCalled();

            fireEvent.click(container.querySelector('.e-detail-list-item')!);

            expect(events.triggerFieldEvent).toHaveBeenCalledTimes(1);
            expect(events.triggerFieldEvent).toHaveBeenCalledWith('pageId', 'elementId', 'onRecordClick', {
                description: 'description1',
                product: 'product1',
                qty: 1,
                st: 'st1',
                nestedReference: { _id: '1234', reference: { _id: '3', status: 'good' } },
            });
        });

        it('should click nested field event handler when the user clicks on an element', () => {
            const { container } = renderComponent({ ...detailListMockProperties });
            expect(events.triggerNestedFieldEvent).not.toHaveBeenCalled();
            fireEvent.click(container.querySelector('[data-testid~="e-field-bind-st"] [data-testid="e-field-value"]')!);

            expect(events.triggerNestedFieldEvent).toHaveBeenCalledWith(
                'pageId',
                'elementId',
                {
                    bind: 'st',
                    canFilter: true,
                    isReadOnly: true,
                    onClick: stOnClickMock,
                    title: 'St',
                    _controlObjectType: 'Text',
                },
                'onClick',
                '1',
                {
                    _id: '1',
                    description: 'description1',
                    product: 'product1',
                    qty: 1,
                    st: 'st1',
                },
            );
        });
    });
});
