import type { Page } from '../../../../service/page';
import * as nestedFields from '../../../nested-fields';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import { detailListField } from '../../../decorators';
import type { NestedField } from '../../../nested-fields';
import type { DetailListDecoratorProperties, DetailListNestedTypes } from '../detail-list-types';

describe('Detail List decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    const fields: NestedField<any, DetailListNestedTypes, any>[] = [
        nestedFields.text({
            bind: 'product',
            title: 'Product:',
            canFilter: true,
            isReadOnly: true,
        }),
        nestedFields.text({
            bind: 'description',
            title: 'Description',
            canFilter: true,
            isReadOnly: true,
        }),
        nestedFields.text({
            bind: 'st',
            title: 'St',
            canFilter: true,
            isReadOnly: true,
        }),
        nestedFields.numeric({
            bind: 'qty',
            title: 'Quantity',
            scale: 0,
            canFilter: true,
            isReadOnly: true,
        }),
    ];

    beforeEach(() => {
        fieldId = 'detailListField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should inherit false abstract-field booleans when no provided', () => {
        detailListField({ fields })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties = pageMetadata.uiComponentProperties[
            fieldId
        ] as DetailListDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.isHiddenMobile).toBe(false);
        expect(mappedComponentProperties.isHiddenDesktop).toBe(false);
        expect(mappedComponentProperties.isFullWidth).toBe(false);
        expect(mappedComponentProperties.isHidden).toBe(false);
        expect(mappedComponentProperties.isTransient).toBe(false);
    });

    it('should set values when component properties provided', () => {
        const onItemClickFunc: () => void = jest.fn().mockImplementation(() => {});

        detailListField({
            onRecordClick: onItemClickFunc,
            orderBy: { product: -1 },
            helperText: 'helper text',
            fields,
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties = pageMetadata.uiComponentProperties[
            fieldId
        ] as DetailListDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onRecordClick).not.toBeUndefined();
        expect(mappedComponentProperties.onRecordClick).toBe(onItemClickFunc);
        expect(mappedComponentProperties.orderBy).not.toBeUndefined();
        expect(mappedComponentProperties.orderBy).toStrictEqual({ product: -1 });
        expect(mappedComponentProperties.helperText).toBe('helper text');
        expect(mappedComponentProperties.fields).toBe(fields);
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(detailListField, pageMetadata, fieldId);
        });
    });
});
