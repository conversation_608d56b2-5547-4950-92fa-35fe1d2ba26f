// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Detail List Component should match the snapshot 1`] = `
<div>
  <div
    class="e-field e-detail-list"
  >
    <div
      class="e-detail-list-container"
    >
      <div
        class="e-detail-list-first-item e-horizontal-separator e-detail-list-item"
        data-testid="e-detail-list-item-field e-detail-list-item-label-product e-detail-list-item-bind-product"
      >
        <div
          class="e-detail-list-item-title"
        >
          Product:
        </div>
        <div
          class="e-detail-list-item-value"
        >
          <div
            class="e-field-nested e-field-nested-product-product"
          >
            <div
              class="e-field e-text-field e-read-only"
              data-label="Product:"
              data-nested="true"
              data-testid="e-text-field e-field-label-product e-field-bind-product"
            >
              <div>
                <div>
                  <span
                    class="e-field-read-only e-field-nested-no-input"
                    data-testid="e-field-value"
                  >
                    product1
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="e-detail-list-item"
        data-testid="e-detail-list-item-field e-detail-list-item-label-description e-detail-list-item-bind-description"
      >
        <div
          class="e-detail-list-item-title"
        >
          Description
        </div>
        <div
          class="e-detail-list-item-value"
        >
          <div
            class="e-field-nested e-field-nested-description-description"
          >
            <div
              class="e-field e-text-field e-read-only"
              data-label="Description"
              data-nested="true"
              data-testid="e-text-field e-field-label-description e-field-bind-description"
            >
              <div>
                <div>
                  <span
                    class="e-field-read-only e-field-nested-no-input"
                    data-testid="e-field-value"
                  >
                    description1
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="e-detail-list-item"
        data-testid="e-detail-list-item-field e-detail-list-item-label-st e-detail-list-item-bind-st"
      >
        <div
          class="e-detail-list-item-title"
        >
          St
        </div>
        <div
          class="e-detail-list-item-value"
        >
          <div
            class="e-field-nested e-field-nested-st-st"
          >
            <div
              class="e-field e-text-field e-read-only"
              data-label="St"
              data-nested="true"
              data-testid="e-text-field e-field-label-st e-field-bind-st"
            >
              <div>
                <div>
                  <span
                    class="e-field-read-only e-field-nested-no-input"
                    data-testid="e-field-value"
                  >
                    st1
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="e-detail-list-item"
        data-testid="e-detail-list-item-field e-detail-list-item-label-quantity e-detail-list-item-bind-qty"
      >
        <div
          class="e-detail-list-item-title"
        >
          Quantity
        </div>
        <div
          class="e-detail-list-item-value"
        >
          <div
            class="e-field-nested e-field-nested-qty-qty"
          >
            <div
              class="e-field e-numeric-field e-read-only"
              data-label="Quantity"
              data-nested="true"
              data-testid="e-numeric-field e-field-label-quantity e-field-bind-qty"
            >
              <div>
                <div>
                  <span
                    class="e-field-read-only e-field-nested-no-input"
                    data-testid="e-field-value"
                  >
                    1
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="e-detail-list-first-item e-horizontal-separator e-detail-list-item"
        data-testid="e-detail-list-item-field e-detail-list-item-label-product e-detail-list-item-bind-product"
      >
        <div
          class="e-detail-list-item-title"
        >
          Product:
        </div>
        <div
          class="e-detail-list-item-value"
        >
          <div
            class="e-field-nested e-field-nested-product-product"
          >
            <div
              class="e-field e-text-field e-read-only"
              data-label="Product:"
              data-nested="true"
              data-testid="e-text-field e-field-label-product e-field-bind-product"
            >
              <div>
                <div>
                  <span
                    class="e-field-read-only e-field-nested-no-input"
                    data-testid="e-field-value"
                  >
                    product2
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="e-detail-list-item"
        data-testid="e-detail-list-item-field e-detail-list-item-label-description e-detail-list-item-bind-description"
      >
        <div
          class="e-detail-list-item-title"
        >
          Description
        </div>
        <div
          class="e-detail-list-item-value"
        >
          <div
            class="e-field-nested e-field-nested-description-description"
          >
            <div
              class="e-field e-text-field e-read-only"
              data-label="Description"
              data-nested="true"
              data-testid="e-text-field e-field-label-description e-field-bind-description"
            >
              <div>
                <div>
                  <span
                    class="e-field-read-only e-field-nested-no-input"
                    data-testid="e-field-value"
                  >
                    description2
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="e-detail-list-item"
        data-testid="e-detail-list-item-field e-detail-list-item-label-st e-detail-list-item-bind-st"
      >
        <div
          class="e-detail-list-item-title"
        >
          St
        </div>
        <div
          class="e-detail-list-item-value"
        >
          <div
            class="e-field-nested e-field-nested-st-st"
          >
            <div
              class="e-field e-text-field e-read-only"
              data-label="St"
              data-nested="true"
              data-testid="e-text-field e-field-label-st e-field-bind-st"
            >
              <div>
                <div>
                  <span
                    class="e-field-read-only e-field-nested-no-input"
                    data-testid="e-field-value"
                  >
                    st2
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="e-detail-list-item"
        data-testid="e-detail-list-item-field e-detail-list-item-label-quantity e-detail-list-item-bind-qty"
      >
        <div
          class="e-detail-list-item-title"
        >
          Quantity
        </div>
        <div
          class="e-detail-list-item-value"
        >
          <div
            class="e-field-nested e-field-nested-qty-qty"
          >
            <div
              class="e-field e-numeric-field e-read-only"
              data-label="Quantity"
              data-nested="true"
              data-testid="e-numeric-field e-field-label-quantity e-field-bind-qty"
            >
              <div>
                <div>
                  <span
                    class="e-field-read-only e-field-nested-no-input"
                    data-testid="e-field-value"
                  >
                    2
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="e-detail-list-first-item e-horizontal-separator e-detail-list-item"
        data-testid="e-detail-list-item-field e-detail-list-item-label-product e-detail-list-item-bind-product"
      >
        <div
          class="e-detail-list-item-title"
        >
          Product:
        </div>
        <div
          class="e-detail-list-item-value"
        >
          <div
            class="e-field-nested e-field-nested-product-product"
          >
            <div
              class="e-field e-text-field e-read-only"
              data-label="Product:"
              data-nested="true"
              data-testid="e-text-field e-field-label-product e-field-bind-product"
            >
              <div>
                <div>
                  <span
                    class="e-field-read-only e-field-nested-no-input"
                    data-testid="e-field-value"
                  >
                    product3
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="e-detail-list-item"
        data-testid="e-detail-list-item-field e-detail-list-item-label-description e-detail-list-item-bind-description"
      >
        <div
          class="e-detail-list-item-title"
        >
          Description
        </div>
        <div
          class="e-detail-list-item-value"
        >
          <div
            class="e-field-nested e-field-nested-description-description"
          >
            <div
              class="e-field e-text-field e-read-only"
              data-label="Description"
              data-nested="true"
              data-testid="e-text-field e-field-label-description e-field-bind-description"
            >
              <div>
                <div>
                  <span
                    class="e-field-read-only e-field-nested-no-input"
                    data-testid="e-field-value"
                  >
                    description3
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="e-detail-list-item"
        data-testid="e-detail-list-item-field e-detail-list-item-label-st e-detail-list-item-bind-st"
      >
        <div
          class="e-detail-list-item-title"
        >
          St
        </div>
        <div
          class="e-detail-list-item-value"
        >
          <div
            class="e-field-nested e-field-nested-st-st"
          >
            <div
              class="e-field e-text-field e-read-only"
              data-label="St"
              data-nested="true"
              data-testid="e-text-field e-field-label-st e-field-bind-st"
            >
              <div>
                <div>
                  <span
                    class="e-field-read-only e-field-nested-no-input"
                    data-testid="e-field-value"
                  >
                    st2
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="e-detail-list-item"
        data-testid="e-detail-list-item-field e-detail-list-item-label-quantity e-detail-list-item-bind-qty"
      >
        <div
          class="e-detail-list-item-title"
        >
          Quantity
        </div>
        <div
          class="e-detail-list-item-value"
        >
          <div
            class="e-field-nested e-field-nested-qty-qty"
          >
            <div
              class="e-field e-numeric-field e-read-only"
              data-label="Quantity"
              data-nested="true"
              data-testid="e-numeric-field e-field-label-quantity e-field-bind-qty"
            >
              <div>
                <div>
                  <span
                    class="e-field-read-only e-field-nested-no-input"
                    data-testid="e-field-value"
                  >
                    3
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
