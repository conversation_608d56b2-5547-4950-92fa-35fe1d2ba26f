PATH: XTREEM/UI+Field+Widgets/Detail+List+Field

## Introduction

Detail list field is a **read only component** that will display the collection as a list. This component supports text-based nested fields only:
- Text
- Reference
- Date
- Aggregate
- Link
- Numeric
- Label

_Currently the component is styled only for mobile responsive._

## Example:

```typescript
@ui.decorators.detailListField<DetailedList>({
        parent() {
            return this.block;
        },
        bind: 'products',
        filter: {
            qty: { _gt: '15' },
        },
        onRecordClick(item: any) {
            this.headerText1.value = item.product;
            this.headerLabel2.value = item.st;
            this.headerLabel3.value = item.qty;
        },
        fields: [
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product:',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            ui.nestedFields.text({
                bind: 'st',
                title: 'St',
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                scale: 0,
            }),
        ],
    })
    detailedList: ui.fields.DetailList;
```

### Display decorator properties:

-   **fields**: field definition to display the list. **The first field will be used as a separator**

### Binding decorator properties:

-   **node**: The GraphQL node that the field suggestions will be fetched from.
-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **filter**: The GraphQL filter applied to the values

#### Runtime Functions

-   **refresh()**: Refetches the field's values from the server and updates the user interface.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
### Event handler decorator properties:

-   **onRecordClick**: Triggered when a item is clicked. The clicked item will be received as an argument
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

## Adding nested fields in extension pages

Additional fields can be added to a table in extension page artifacts using the `detailListFieldOverride` decorator. In this case, the `fields` decorator property accepts an `nestedFieldExtension` array. This set of nested fields comes with an additional decorator property which determines the position of the extension fields within the original table. This property is called `insertBefore`.

### Positioning extension fields within the table

The `insertBefore` decorator property determines the position of the extension field.
#### Not provided
If this decorator property is not provided, the extension field is added to the end of the list of nested fields.

#### Using the bind value
The position of the extension nested field can be set by providing the `bind` decorator property value of the base nested field that the field should be inserted before to the `insertBefore` extension column decorator property.

#### Base reference fields bound to the same property
When the base page contains reference field based columns that are bound to the same property, the position of the additional column can be set more precisely by including the `valueField` property value into the `insertBefore` extension property. This must be done by joining the two values by a double underscore in the following format: `bind__valueField`. For example if the field should be positioned before a reference field that is bound to `product` and the displayed value field is `description`, than the `insertBefore` extension property would be `product__description`.