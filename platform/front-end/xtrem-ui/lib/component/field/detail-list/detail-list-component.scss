@import '../../../render/style/variables.scss';
@import '../../../render/style/mixins.scss';

.e-detail-list {
    width: 100%;
    margin: -8px;

    @include full_width_mobile_field;
    @include extra_small {
        margin: 0 -16px; 
    }

    .common-input__label {
        min-height: 21px;
    }

    .e-detail-list-container {
        display: flex;
        flex-flow: column wrap;
        width: 100%;

        @include extra_small {
            width: 100vw;
        }

        .e-detail-list-item {
            border-bottom: 1px solid var(--colorsUtilityMajor100);
            display: flex;
            width: 100%;
            flex-flow: row nowrap;
            background-color: var(--colorsYang100);
            padding-left: 16px;
            height: 32px;
            box-sizing: border-box;
            max-height: 32px;

            .e-field {
                padding-bottom: 0;
                padding-left: 0;
            }

            .e-label-field {
                .common-input__label {
                    display: none;
                }

                .e-pill-wrapper > span {
                    margin-top: 0;
                }
            }

            .e-field-read-only {
                padding: 0;
            }

            .e-detail-list-item-title {
                align-self: center;
                font-size: 14px;
                width: 158px;
                min-width: 158px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                font-family: var(--fontFamiliesDefault);
                font-weight: var(--fontWeights500);
            }

            .e-detail-list-item-value {
                width: 146px;
                align-self: center;

                @include e-field-label-hidden;

                .common-input__help-text {
                    display: none;
                }
            }
        }

        .e-detail-list-first-item:first-child {
            border-top: 1px solid var(--colorsUtilityMajor100);
        }

        .e-detail-list-first-item {
            background: var(--colorsUtilityMajor025);
            height: 32px;
            max-height: 32px;
            padding: 0 16px;

            .e-detail-list-item-title {
                font-family: $fontAdelle;
                width: 158px;
                white-space: unset;
                overflow: unset;
                text-overflow: unset;
                color: var(--colorsYin055);
                line-height: 18px;
                height: 18px;
            }

            .e-detail-list-item-value {
                width: 130px;
            }
        }
    }
}
