import type { ClientNode } from '@sage/xtrem-client';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import { showToast } from '../../../service/toast-service';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { mergedValueToSplitValue, splitValueToMergedValue } from '../../../utils/transformers';
import type { DetailListProperties } from '../../control-objects';
import type { DetailListDecoratorProperties } from '../../decorators';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { FieldKey } from '../../types';

export class DetailListControlObject<
    ItemType extends ClientNode = any,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends ReadonlyFieldControlObject<CT, FieldKey.DetailList, DetailListProperties<CT, ItemType>> {
    static readonly defaultUiProperties: Partial<DetailListDecoratorProperties> = {
        ...ReadonlyFieldControlObject.defaultUiProperties,
    };

    /** Graphql filter that will restrict the records displayed in the table */
    get filter(): GraphQLFilter<ItemType> | undefined {
        return resolveByValue({
            fieldValue: undefined,
            propertyValue: this.getUiComponentProperty('filter'),
            rowValue: undefined,
            screenId: this.screenId,
            skipHexFormat: true,
        });
    }

    /** Graphql filter that will restrict the records displayed in the table */
    set filter(filter: GraphQLFilter<ItemType> | undefined) {
        this.setUiComponentProperties('filter', filter);
        this.refresh().catch(() => {
            /* Intentional fire and forget */
        });
    }

    async refresh(): Promise<void> {
        await this._refresh({ keepPageInfo: true }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }

    /** Detail list items */
    get value(): Partial<ItemType>[] {
        const value = this._getValue();
        if (!value || !value.data) {
            return [];
        }
        return value.data.map(splitValueToMergedValue) as Partial<ItemType>[];
    }

    set value(newValue: Partial<ItemType>[]) {
        if (newValue) {
            const splitValue = newValue.map(mergedValueToSplitValue);
            const previousValue = this._getValue() || { pageInfo: {}, data: [] };
            this._setValue({ ...previousValue, data: [...splitValue] });
        } else {
            this._setValue(null);
        }
    }
}
