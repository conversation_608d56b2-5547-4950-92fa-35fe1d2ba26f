import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { DetailListComponentProps } from './detail-list-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';

const ConnectedDetailListComponent = React.lazy(() => import('./detail-list-component'));

export function AsyncConnectedDetailListComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="200px" />}>
            <ConnectedDetailListComponent {...props} />
        </React.Suspense>
    );
}

const DetailListComponent = React.lazy(() =>
    import('./detail-list-component').then(c => ({ default: c.DetailListComponent })),
);

export function AsyncDetailListComponent(props: DetailListComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={true} bodyHeight="200px" />}>
            <DetailListComponent {...props} />
        </React.Suspense>
    );
}
