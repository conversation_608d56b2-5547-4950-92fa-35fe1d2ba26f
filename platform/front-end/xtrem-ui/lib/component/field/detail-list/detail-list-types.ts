import type { ClientNode } from '@sage/xtrem-client';
import type { QueryArguments } from '../../../service/graphql-utils';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { OverrideDecoratorProperties } from '../../../utils/decorator-utils';
import type { BlockControlObject } from '../../control-objects';
import type { NestedField, NestedFieldTypes } from '../../nested-fields';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import type { FieldControlObjectInstance, FieldKey, GraphqlCollection, OrderByType } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type { PropertyValueType } from '../reference/reference-types';
import type { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HasNode, HasParent } from '../traits';
import type { NestedExtensionField } from '../../nested-fields-extensions';
import type { NestedOverrideField } from '../../nested-fields-overrides';
import type { Extend } from '../../../service/page-extension';

export type DetailListNestedTypes =
    | FieldKey.Aggregate
    | FieldKey.Date
    | FieldKey.Label
    | FieldKey.Link
    | FieldKey.Numeric
    | FieldKey.Reference
    | FieldKey.Text;
export interface DetailListProperties<CT extends ScreenExtension<CT> = ScreenBase, ItemType extends ClientNode = any>
    extends ReadonlyFieldProperties<CT>,
        HasFilter<CT, ItemType>,
        HasNode<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>> {
    /** The column or the set of columns which the table should be sorted by */
    orderBy?: OrderByType<ItemType>;
    /** The definitions of the nested fields used to represent the list */
    fields: NestedField<CT, DetailListNestedTypes, ItemType>[];
    /** Function to be executed when any part of an item is clicked */
    onRecordClick?: (this: CT, item: ItemType) => void;
}

export interface InternalDetailListProperties {
    loadDetailListData: (
        screenId: string,
        elementId: string,
        fields: NestedField<ScreenBase, NestedFieldTypes>[],
        queryArguments: QueryArguments,
        bind?: PropertyValueType,
    ) => void;
    isLoading: boolean;
}

export interface DetailListDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    ReferencedItemType extends ClientNode = any,
> extends DetailListProperties<CT, ReferencedItemType> {}

export interface DetailListExtensionDecoratorProperties<
    CT extends ScreenExtension<CT>,
    ReferencedItemType extends ClientNode = any,
> extends OverrideDecoratorProperties<DetailListDecoratorProperties<Extend<CT>, ReferencedItemType>> {
    onRecordClickAfter?: (this: CT, item: ReferencedItemType) => void;

    /** The definitions of the nested fields used to represent the table rows */
    fields?: NestedExtensionField<CT, DetailListNestedTypes, ReferencedItemType>[];

    /** Allows overriding existing column properties in the base page's columns */
    fieldOverrides?: NestedOverrideField<CT, DetailListNestedTypes, ReferencedItemType>[];
}

export type DetailListComponentProps = BaseEditableComponentProperties<
    DetailListProperties,
    GraphqlCollection,
    InternalDetailListProperties
>;
