/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { DetailListControlObject } from './detail-list-control-object';
import type { DetailListDecoratorProperties, DetailListExtensionDecoratorProperties } from './detail-list-types';

class DetailListDecorator extends AbstractFieldDecorator<FieldKey.DetailList> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = DetailListControlObject;
}

/**
 * Initializes the decorated member as a [Detail List]{@link DetailListControlObject} field with the provided properties
 *
 * @param properties The properties that the [Detail List]{@link DetailListControlObject} field will be initialized with
 */
export function detailListField<CT extends ScreenExtension<CT>, ReferencedItemType extends ClientNode = any>(
    properties: DetailListDecoratorProperties<Extend<CT>, ReferencedItemType>,
): (target: CT, name: string) => void {
    return standardDecoratorImplementation<CT, FieldKey.DetailList, ReferencedItemType>(
        properties,
        DetailListDecorator,
        FieldKey.DetailList,
    );
}

export function detailListFieldOverride<CT extends ScreenExtension<CT>, ReferencedItemType extends ClientNode = any>(
    properties: DetailListExtensionDecoratorProperties<CT, ReferencedItemType>,
): (target: CT, name: string) => void {
    return standardExtensionDecoratorImplementation<CT, FieldKey.DetailList, ReferencedItemType>(properties);
}
