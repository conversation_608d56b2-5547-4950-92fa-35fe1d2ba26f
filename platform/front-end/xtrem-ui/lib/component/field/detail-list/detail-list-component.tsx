import type { Dict } from '@sage/xtrem-shared';
import Button from 'carbon-react/esm/components/button';
import Loader from 'carbon-react/esm/components/loader';
import React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import { loadCollectionData } from '../../../redux/actions';
import type { ReduxResponsive } from '../../../redux/state';
import { ConnectedNestedFieldWrapper } from '../../../render/nested-field-wrapper';
import type { QueryArguments } from '../../../service/graphql-utils';
import { localize } from '../../../service/i18n-service';
import type { ScreenBase } from '../../../service/screen-base';
import { getNestedFieldElementId, normalizeUnderscoreBind } from '../../../utils/abstract-fields-utils';
import { getDataTestIdAttribute, getFieldClass, isHidden } from '../../../utils/dom';
import { triggerFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import type { NestedField, NestedFieldsProperties, NestedFieldTypes } from '../../nested-fields';
import type { GraphqlCollection } from '../../types';
import { FieldKey } from '../../types';
import { getFieldTitle } from '../carbon-helpers';
import { mapStateToProps } from '../field-base-component';
import type { BaseEditableComponentProperties, FieldComponentExternalProperties } from '../field-base-component-types';
import type { DetailListComponentProps, DetailListNestedTypes, DetailListProperties } from './detail-list-types';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { get } from 'lodash';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import type { PropertyValueType } from '../reference/reference-types';

export function DetailListComponent(props: DetailListComponentProps): React.ReactElement {
    const { browser, elementId, fieldProperties, isLoading, item, loadDetailListData, screenId, value } = props;
    const values = value?.data || [];
    const onClick = (): void => {
        const queryArguments: QueryArguments = {
            after: value?.pageInfo.endCursor,
        };

        if (fieldProperties.filter) {
            queryArguments.filter = JSON.stringify(fieldProperties.filter);
        }

        loadDetailListData(screenId, elementId, fieldProperties.fields, queryArguments, fieldProperties.bind);
    };

    const resolvedTitle = resolveByValue({
        fieldValue: value,
        propertyValue: fieldProperties.title,
        skipHexFormat: true,
        screenId,
        rowValue: null, // This is not a field context, so no row value can be present
    });

    return (
        <div
            className={getFieldClass(
                screenId,
                value,
                'e-field e-detail-list',
                fieldProperties,
                undefined,
                isHidden(item, browser) ||
                    resolveByValue({
                        fieldValue: value,
                        propertyValue: fieldProperties.isHidden,
                        skipHexFormat: true,
                        screenId,
                        rowValue: null, // This is not a field context, so no row value can be present
                    }),
            )}
        >
            {resolvedTitle && !fieldProperties.isTitleHidden && <FieldLabel label={resolvedTitle} />}
            {renderList({ nestedFields: fieldProperties.fields, values, screenId, elementId, browser })}
            {!fieldProperties.isTransient && value?.pageInfo?.hasNextPage && (
                <div className="e-load-more-button-container" data-testid="e-load-more-button-container">
                    <Button
                        buttonType="tertiary"
                        disabled={isLoading}
                        data-testid={isLoading ? 'e-is-loading-more-button' : 'e-load-more-button'}
                        onClick={onClick}
                    >
                        {isLoading ? (
                            <Loader size="large" />
                        ) : (
                            localize('@sage/xtrem-ui/mobile-table-load-more', 'Load more')
                        )}
                    </Button>
                </div>
            )}
            {fieldProperties.helperText && !fieldProperties.isHelperTextHidden && (
                <HelperText helperText={props.fieldProperties.helperText} />
            )}
        </div>
    );
}

const renderList = ({
    nestedFields,
    values,
    screenId,
    elementId,
    browser,
}: {
    nestedFields: DetailListComponentProps['fieldProperties']['fields'];
    values: any[];
    screenId: string;
    elementId: string;
    browser: ReduxResponsive | undefined;
}): React.ReactNode => {
    return (
        <div className="e-detail-list-container">
            {values.map((value: any, valueIndex: number) => {
                const mergedRowValue = splitValueToMergedValue(value);
                const onClick = (): void => {
                    triggerFieldEvent(screenId, elementId, 'onRecordClick', mergedRowValue);
                };

                return nestedFields
                    .filter(nestedField => {
                        const nestedProperties = nestedField.properties;
                        const isNestedFieldHidden = resolveByValue({
                            screenId,
                            fieldValue: get(mergedRowValue, convertDeepBindToPathNotNull(nestedField.properties.bind)),
                            propertyValue: nestedProperties.isHidden,
                            skipHexFormat: true,
                            rowValue: mergedRowValue,
                        });
                        return (
                            !isNestedFieldHidden &&
                            !(nestedProperties.isHiddenDesktop && browser?.greaterThan.s) &&
                            !(nestedProperties.isHiddenMobile && !browser?.greaterThan.s)
                        );
                    })
                    .map((nestedField, fieldIndex) =>
                        renderItem(
                            nestedField,
                            fieldIndex === 0,
                            value,
                            screenId,
                            elementId,
                            `${valueIndex}-${fieldIndex}`,
                            onClick,
                        ),
                    );
            })}
        </div>
    );
};

const renderItem = (
    nestedField: NestedField<ScreenBase, DetailListNestedTypes>,
    isFirstItem: boolean,
    value: any,
    screenId: string,
    elementId: string,
    key: string,
    onRecordClick?: (item: Dict<any>) => void,
): React.ReactNode => {
    const nestedFieldElementId = getNestedFieldElementId(nestedField);
    const rowValue = splitValueToMergedValue(value);
    const title = getFieldTitle(screenId, nestedField.properties, value);
    return (
        <div
            key={key}
            onClick={onRecordClick}
            className={
                isFirstItem
                    ? 'e-detail-list-first-item e-horizontal-separator e-detail-list-item'
                    : 'e-detail-list-item'
            }
            data-testid={getDataTestIdAttribute('detail-list-item', title, nestedFieldElementId, 'e-detail-list-item')}
        >
            <div className="e-detail-list-item-title">
                {title && !nestedField.properties.isTitleHidden ? title : ''}
            </div>
            <div className="e-detail-list-item-value">
                <ConnectedNestedFieldWrapper
                    _id={nestedFieldElementId}
                    columnDefinition={nestedField}
                    columnName={nestedFieldElementId}
                    columnProperties={getNestedFieldProperties(nestedField)}
                    handlersArguments={{
                        rowValue: rowValue ? splitValueToMergedValue(rowValue) : rowValue,
                        onClick: [value._id, splitValueToMergedValue(rowValue)],
                    }}
                    screenId={screenId}
                    setFieldValue={(): Promise<void> => Promise.resolve()}
                    value={rowValue[normalizeUnderscoreBind(nestedFieldElementId)]}
                    parentElementId={elementId}
                    nestedReadOnlyField={true}
                />
            </div>
        </div>
    );
};

const getNestedFieldProperties = (
    nestedField: NestedField<ScreenBase, DetailListNestedTypes>,
): NestedFieldsProperties<DetailListNestedTypes, ScreenBase> => {
    if (nestedField.type === FieldKey.Link) {
        return nestedField.properties as NestedFieldsProperties<FieldKey.Link, ScreenBase>;
    }
    return { ...nestedField.properties, isReadOnly: true } as NestedFieldsProperties<DetailListNestedTypes, ScreenBase>;
};

const mapDispatchToProps = (dispatch: xtremRedux.AppThunkDispatch): Partial<DetailListComponentProps> => {
    return {
        loadDetailListData: (
            screenId: string,
            elementId: string,
            fields: NestedField<ScreenBase, NestedFieldTypes>[],
            queryArguments: QueryArguments,
            bind?: PropertyValueType,
        ): Promise<void> => dispatch(loadCollectionData(screenId, elementId, fields, queryArguments, bind, true)),
    };
};

const customMapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: FieldComponentExternalProperties,
): Partial<DetailListComponentProps> => {
    const regularProps = mapStateToProps()(state, props) as BaseEditableComponentProperties<
        DetailListProperties,
        GraphqlCollection
    >;
    return {
        isLoading: state.loading.pages[props.screenId]?.[props.elementId],
        ...regularProps,
    };
};

export const ConnectedDetailListComponent = connect(customMapStateToProps, mapDispatchToProps)(DetailListComponent);

export default ConnectedDetailListComponent;
