/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { DateControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { DateDecoratorProperties } from './date-types';

class DateDecorator extends AbstractFieldDecorator<FieldKey.Date> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = DateControlObject;
}

/**
 * Initializes the decorated member as a [Date]{@link DateControlObject} field with the provided properties
 *
 * @param properties The properties that the [Date]{@link DateControlObject} field will be initialized with
 */
export function dateField<T extends ScreenExtension<T>>(
    properties: DateDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Date>(properties, DateDecorator, FieldKey.Date);
}

export function dateFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<DateDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Date>(properties);
}
