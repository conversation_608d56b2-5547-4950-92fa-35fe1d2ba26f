/**
 * @packageDocumentation
 * @module root
 * */

import type { DatePropertyValue } from '../../../utils/types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import { datePropertyValueToDateString } from '@sage/xtrem-date-time';
import type { ScreenExtension } from '../../../types';
import type { ScreenBase } from '../../../service/screen-base';
import type { DateProperties } from './date-types';

/**
 * [Field]{@link EditableFieldControlObject} that holds a date value in the YYYY-MM-DD format
 */
export class DateControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.Date,
    FieldComponentProps<FieldKey.Date>
> {
    /** The maximum date value allowed for the date field */
    get maxDate(): DatePropertyValue | undefined {
        return this.getResolvedProperty('maxDate');
    }

    /** The maximum date value allowed for the date field */
    set maxDate(newValue: DatePropertyValue | undefined) {
        const date = newValue ? datePropertyValueToDateString(newValue) : newValue;
        this.setUiComponentProperties('maxDate', date);
    }

    /** The minimum date value allowed for the date field */
    get minDate(): DatePropertyValue | undefined {
        return this.getResolvedProperty('minDate');
    }

    /** The minimum date value allowed for the date field */
    set minDate(newValue: DatePropertyValue | undefined) {
        const date = newValue ? datePropertyValueToDateString(newValue) : newValue;
        this.setUiComponentProperties('minDate', date);
    }

    @ControlObjectProperty<DateProperties<CT>, DateControlObject<CT>>()
    /** Placeholder to be displayed in the field body */
    placeholder?: string;

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }
}
