import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { DateComponentProps } from './date-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';

const ConnectedDateComponent = React.lazy(() => import('./date-component'));

export function AsyncConnectedDateComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedDateComponent {...props} />
        </React.Suspense>
    );
}

const DateComponent = React.lazy(() => import('./date-component').then(c => ({ default: c.DateComponent })));

export function AsyncDateComponent(props: DateComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <DateComponent {...props} />
        </React.Suspense>
    );
}
