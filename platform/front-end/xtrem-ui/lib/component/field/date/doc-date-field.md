PATH: XTREEM/UI+Field+Widgets/Date+Field

## Introduction

The date field represents the display and editor of a date in a container of pages within the user interface. The date field is also available as a nested field.

## Example

```ts
@ui.decorators.dateField<Page>({
    helperText: 'Click the fields icon to launch a date picker.',
    isDisabled: false,
    isHelperTextHidden: false,
    isHidden: false,
    isTitleHidden: false,
    isTransient: true,
    maxDate: '2020-12-31',
    minDate: '2020-01-01',
    placeholder: 'Select a date',
    title: 'Date Field',
    parent() {
        return this.block;
    },
    valdation(value: string) {
        return value < DateValue.today().toString()
            ? 'Please select a date in the future'
            : undefined;
    },
})
field: ui.fields.Date;
```

#### Use of field in extended page:

```ts
@ui.decorators.dateField<PageExtension>({
    title: 'Extended Date Field',
    insertBefore() {
        return this.field;
    },
    parent() {
        return this.block;
    }
})
extension: ui.fields.Date;
```

#### Use of field as nested field:

```ts
@ui.decorators.tableField<Page>({
    ...,
    columns: [
        ...,
        ui.nestedFields.date({
            bind: 'lastModified',
            title: 'Last Modified',
        }),
    ],
})
table: ui.fields.Table<Node>;
```

## Decorator Properties

#### Display Properties

-   **helperText**: The helper text displayed below the field. This property is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.
-   **isHelperTextHidden**: Determines whether the field's helper text is displayed or not.
-   **isHidden**: Determines whether the field is displayed or not.
-   **isTitleHidden**: Determines whether the field's title is displayed or not.
-   **size**: Size the field should be rendered in. The options are "small", "medium" and "large".
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **info**: Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function that returns a string.
-   **warning**: Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback function that returns a string.

#### Binding Properties

-   **bind**: Determines the associated GraphQL node's property the field's value will be bound to.
-   **isTransient**: Determines whether the field will be excluded from the automatic data binding process or not.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.

#### Validation Properties

-   **maxDate**: Determines the maximum date value allowed for this field.
-   **minDate**: Determines the minimum date value allowed for this field.
-   **validation()**: Custom validation callback. The new value is provided as argument. If the function returns a non-empty string or promise resolving into a non-empty string, the returned string will be used as the validation's error message. If the callback returns a falsy value, the field is considered to be valid.

#### Event Handler Properties

-   **onChange**: Handler triggered when the field's value changes.
-   **onClick**: Handler triggered when the field is clicked.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).
-   **onInputValueChange**: Triggered when the user stops typing into the input of the field. The execution is debounced by 50ms to cater for continuos typing.

### Other Decorator Properties

-   **fetchesDefaults**: When set to true and when the date value changes, a request to the server for default values for the whole page will be requested. False by default.
-   **groupAggregationMethod**: Set the grouping method for computing a value for the group totals row on the main list. The value is only displayed if the main list is grouped. Available methods for this field type: `min`, `max`, `distinctCount`.

#### Runtime Functions

-   **focus()**: Sets the browser's focus to the field.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Date).
