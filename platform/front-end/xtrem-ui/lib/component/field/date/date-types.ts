import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { DatePropertyValue, ValueOrCallback, ValueOrCallbackWitRecordValue } from '../../../utils/types';
import type { BlockControlObject, TileControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedPropertiesWrapper } from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseErrorableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasInputValueChangeListener,
    HasParent,
    Nested,
    NestedChangeable,
    NestedClickable,
    NestedGroupAggregations,
    NestedValidatable,
    Sizable,
    Validatable,
} from '../traits';

export interface DateProperties<CT extends ScreenBase = ScreenBase, ContextNodeType = void>
    extends EditableFieldProperties<CT, ContextNodeType>,
        Sizable {
    /** The maximum date value allowed for the date field */
    maxDate?: ValueOrCallback<CT, DatePropertyValue>;
    /** The minimum date value allowed for the date field */
    minDate?: ValueOrCallback<CT, DatePropertyValue>;
    /** Placeholder to be displayed in the field body */
    placeholder?: string;
}

export interface DateDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<DateProperties<CT>, '_controlObjectType'>,
        Changeable<CT>,
        Clickable<CT>,
        HasInputValueChangeListener<CT>,
        HasParent<CT, BlockControlObject<CT> | TileControlObject<CT>>,
        Sizable,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        Validatable<CT, string> {}

export interface NestedDateProperties<CT extends ScreenBase = ScreenBase, NodeType extends ClientNode = any>
    extends NestedPropertiesWrapper<DateProperties<CT, NodeType>>,
        Nested<NodeType>,
        NestedChangeable<CT>,
        NestedClickable<CT, NodeType>,
        NestedGroupAggregations<'min' | 'max' | 'distinctCount'>,
        Sizable,
        NestedValidatable<CT, string, NodeType> {
    /** The maximum date value allowed for the date field */
    maxDate?: ValueOrCallbackWitRecordValue<CT, DatePropertyValue, NodeType>;
    /** The minimum date value allowed for the date field */
    minDate?: ValueOrCallbackWitRecordValue<CT, DatePropertyValue, NodeType>;
}

export type DateComponentProps = BaseErrorableComponentProperties<
    DateProperties,
    string,
    NestedFieldsAdditionalProperties
>;
