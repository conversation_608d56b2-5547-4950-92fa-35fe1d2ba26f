import type { Page } from '../../../..';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { DateDecoratorProperties } from '../date-types';
import { dateField } from '../date-decorator';

describe('Date decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'dateField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should set minDate field when parameter type is string', () => {
        dateField({
            minDate: '2019-10-30',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: DateDecoratorProperties<ScreenBase> =
            pageMetadata.uiComponentProperties[fieldId];

        expect(mappedComponentProperties.minDate).toBe('2019-10-30');
    });

    it('should set maxDate field when parameter type is string', () => {
        dateField({
            maxDate: '2019-10-30',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: DateDecoratorProperties<ScreenBase> =
            pageMetadata.uiComponentProperties[fieldId];

        expect(mappedComponentProperties.maxDate).toBe('2019-10-30');
    });

    it('should set minDate field when parameter type is Date Object', () => {
        const minDate = new Date();
        dateField({
            minDate,
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: DateDecoratorProperties<ScreenBase> =
            pageMetadata.uiComponentProperties[fieldId];

        expect(mappedComponentProperties.minDate).toBe(minDate);
    });

    it('should set maxDate field when parameter type is is Date Object', () => {
        const maxDate = new Date();
        dateField({
            maxDate,
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: DateDecoratorProperties<ScreenBase> =
            pageMetadata.uiComponentProperties[fieldId];

        expect(mappedComponentProperties.maxDate).toBe(maxDate);
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(dateField, pageMetadata, fieldId);
        });
    });
});
