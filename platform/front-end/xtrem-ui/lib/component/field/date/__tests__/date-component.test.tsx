import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux';
import * as abstractUtils from '../../../../utils/abstract-fields-utils';
import type { DateProperties } from '../../../control-objects';
import { FieldKey } from '../../../types';
import { ConnectedDateComponent, DateComponent } from '../date-component';
import * as stateUtils from '../../../../utils/state-utils';
import { DateValue } from '@sage/xtrem-date-time';
import { fireEvent, render, waitFor } from '@testing-library/react';

describe('Date component', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: DateProperties;

    beforeEach(() => {
        mockFieldProperties = {
            title: 'Test Field Title',
        };
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Date, state, screenId, 'test-date-field', mockFieldProperties, '2018-03-25');
            addFieldToState(FieldKey.Date, state, screenId, 'test-empty-date-field', mockFieldProperties, null);
            mockStore = getMockStore(state);
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedDateComponent screenId={screenId} elementId="test-date-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedDateComponent screenId={screenId} elementId="test-date-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render disabled', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedDateComponent screenId={screenId} elementId="test-date-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render readOnly', () => {
                mockFieldProperties.isReadOnly = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedDateComponent screenId={screenId} elementId="test-date-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with minDate and maxDate sent as string', () => {
                mockFieldProperties.maxDate = '2019-12-31';
                mockFieldProperties.minDate = '2019-01-01';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedDateComponent screenId={screenId} elementId="test-date-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with minDate and maxDate sent as Date object', () => {
                mockFieldProperties.maxDate = '2019-12-31';
                mockFieldProperties.minDate = DateValue.parse('2019-01-01');
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedDateComponent screenId={screenId} elementId="test-date-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedDateComponent screenId={screenId} elementId="test-date-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
                expect(container.querySelector('[data-element="help"]')).not.toBeNull();
            });

            it('Should not render helperText', () => {
                mockFieldProperties.helperText = '';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedDateComponent screenId={screenId} elementId="test-date-field" />
                    </Provider>,
                );
                expect(container.querySelector('[data-element="help"]')).toBeNull();
            });

            it('should render without value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedDateComponent screenId={screenId} elementId="test-empty-date-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render read-only using a conditional declaration', () => {
                jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() =>
                    getMockPageDefinition('TestPage'),
                );
                mockFieldProperties.isReadOnly = () => {
                    return true;
                };

                let wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedDateComponent screenId={screenId} elementId="test-date-field" />
                    </Provider>,
                );

                expect(wrapper.container.querySelector('input[readonly]')).not.toBeNull();

                mockFieldProperties.isReadOnly = () => {
                    return false;
                };

                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedDateComponent screenId={screenId} elementId="test-date-field" />
                    </Provider>,
                );

                expect(wrapper.container.querySelector('input[readonly]')).toBeNull();
            });
        });
        describe('formatting', () => {
            it("should format the date in DD/MM/YYYY as this is Carbon's default format date", () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedDateComponent screenId={screenId} elementId="test-date-field" />
                    </Provider>,
                );
                expect(container.querySelector('input')).toHaveValue('25/03/2018');
            });
        });
    });

    describe('unconnected', () => {
        describe('Snapshots', () => {
            const value = '2017-08-05';
            it('should render just fine', () => {
                const { container } = render(
                    <DateComponent
                        elementId="test-date-field"
                        fieldProperties={mockFieldProperties}
                        value={value}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with an info message', async () => {
                mockFieldProperties.infoMessage = 'Info message!!';
                const { baseElement } = render(
                    <DateComponent
                        elementId="test-date-field"
                        fieldProperties={mockFieldProperties}
                        value={value}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an info message from a callback', async () => {
                jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() =>
                    getMockPageDefinition('TestPage'),
                );
                mockFieldProperties.infoMessage = () => 'Info message!!';
                const { baseElement } = render(
                    <DateComponent
                        elementId="test-date-field"
                        fieldProperties={mockFieldProperties}
                        value={value}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an warning message', async () => {
                mockFieldProperties.warningMessage = 'Warning message!!';
                const { baseElement } = render(
                    <DateComponent
                        elementId="test-date-field"
                        fieldProperties={mockFieldProperties}
                        value={value}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with an warning message from a callback', async () => {
                jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() =>
                    getMockPageDefinition('TestPage'),
                );
                mockFieldProperties.warningMessage = () => 'Warning message!!';
                const { baseElement } = render(
                    <DateComponent
                        elementId="test-date-field"
                        fieldProperties={mockFieldProperties}
                        value={value}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });
        });

        describe('Interactions', () => {
            const setFieldValueMock = jest.fn().mockResolvedValue(undefined);
            const validateMock = jest.fn().mockResolvedValue(undefined);

            it("should update the value '2017-12-21' on external update", () => {
                const value = '2017-08-06';
                const { container } = render(
                    <DateComponent
                        elementId="test-date-field"
                        fieldProperties={mockFieldProperties}
                        value={value}
                        setFieldValue={setFieldValueMock}
                        validate={validateMock}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                expect(container.querySelector('input[data-element="input"]')).toHaveValue('06/08/2017');

                const handleChangeSpy: jest.SpyInstance<any> = jest.spyOn(abstractUtils, 'handleChange');

                const input = container.querySelector('input[data-element="input"]')!;

                fireEvent.change(input, {
                    target: { value: '08/05/2017' },
                });

                fireEvent.blur(input);

                expect(handleChangeSpy).toHaveBeenCalledTimes(1);
                expect(handleChangeSpy).toHaveBeenCalledWith(
                    'test-date-field',
                    '2017-08-05',
                    setFieldValueMock,
                    validateMock,
                    expect.any(Function),
                );
            });

            it('should update the value to null on external update', () => {
                const value = '2017-08-06';
                const { container } = render(
                    <DateComponent
                        elementId="test-date-field"
                        fieldProperties={mockFieldProperties}
                        value={value}
                        setFieldValue={setFieldValueMock}
                        validate={validateMock}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                expect(container.querySelector('input[data-element="input"]')).toHaveValue('06/08/2017');

                const handleChangeSpy: jest.SpyInstance<any> = jest.spyOn(abstractUtils, 'handleChange');

                const input = container.querySelector('input[data-element="input"]')!;
                fireEvent.change(input, {
                    target: { value: '' },
                });

                fireEvent.blur(input);

                expect(handleChangeSpy).toHaveBeenCalledTimes(1);
                expect(handleChangeSpy).toHaveBeenCalledWith(
                    'test-date-field',
                    null,
                    setFieldValueMock,
                    validateMock,
                    expect.any(Function),
                );
            });
        });
    });
});
