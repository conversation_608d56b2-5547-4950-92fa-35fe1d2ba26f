// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Date component connected Snapshots should render disabled 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin: var(--spacing000);
}

.c3 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin030);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e90e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
  color: var(--colorsUtilityYin030);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c10 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
  color: var(--colorsUtilityYin030);
  cursor: not-allowed;
}

.c10:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c10::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c8 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background: var(--colorsUtilityDisabled400);
  border-color: var(--colorsUtilityDisabled600);
  cursor: not-allowed;
}

.c8 .c9 {
  padding: 0 var(--spacing150);
  padding-right: 0;
}

.c8 input::-ms-clear {
  display: none;
}

.c8 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c7 {
  -webkit-flex: none;
  -ms-flex: none;
  flex: none;
  width: 135px;
}

.c0 .c7 .c9 {
  margin-right: -8px;
}

.c11 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: var(--sizing500);
  cursor: not-allowed;
}

.c11:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

<div>
  <div
    class="e-field e-date-field e-disabled"
    data-label="Test Field Title"
    data-testid="e-date-field e-field-label-testFieldTitle e-field-bind-test-date-field"
  >
    <div
      class="c0"
      data-component="date"
      role="presentation"
    >
      <div
        class="c1 c2"
        data-component="date-input"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-date-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              disabled=""
              for="TestPage-test-date-field"
              id="TestPage-test-date-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7 c8"
              disabled=""
              role="presentation"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c9 c10"
                data-element="input"
                data-label="Test Field Title"
                disabled=""
                id="TestPage-test-date-field"
                name="test-date-field"
                placeholder=""
                type="text"
                value="25/03/2018"
              />
              <span
                aria-hidden="true"
                class="c11"
                data-element="input-icon-toggle"
                data-role="input-icon-toggle"
                disabled=""
                tabindex="-1"
              >
                <span
                  class="c12"
                  data-component="icon"
                  data-element="calendar"
                  data-role="icon"
                  disabled=""
                  font-size="small"
                  type="calendar"
                />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Date component connected Snapshots should render helperText 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin: var(--spacing000);
}

.c3 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e90e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c13 {
  display: block;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  margin-top: 8px;
  white-space: pre-wrap;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c10 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c10:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c10::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c8 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c8 .c9 {
  padding: 0 var(--spacing150);
  padding-right: 0;
}

.c8 input::-ms-clear {
  display: none;
}

.c8 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c7 {
  -webkit-flex: none;
  -ms-flex: none;
  flex: none;
  width: 135px;
}

.c0 .c7 .c9 {
  margin-right: -8px;
}

.c11 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: var(--sizing500);
}

.c11:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

<div>
  <div
    class="e-field e-date-field"
    data-label="Test Field Title"
    data-testid="e-date-field e-field-label-testFieldTitle e-field-bind-test-date-field"
  >
    <div
      class="c0"
      data-component="date"
      role="presentation"
    >
      <div
        class="c1 c2"
        data-component="date-input"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-date-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-date-field"
              id="TestPage-test-date-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7 c8"
              role="presentation"
            >
              <input
                aria-describedby="TestPage-test-date-field-field-help"
                aria-invalid="false"
                autocomplete="off"
                class="c9 c10"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-date-field"
                name="test-date-field"
                type="text"
                value="25/03/2018"
              />
              <span
                aria-hidden="true"
                class="c11"
                data-element="input-icon-toggle"
                data-role="input-icon-toggle"
                tabindex="-1"
              >
                <span
                  class="c12"
                  data-component="icon"
                  data-element="calendar"
                  data-role="icon"
                  font-size="small"
                  type="calendar"
                />
              </span>
            </div>
          </div>
        </div>
        <span
          class="c13"
          data-element="help"
          id="TestPage-test-date-field-field-help"
        >
          This is a helper text
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Date component connected Snapshots should render hidden 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin: var(--spacing000);
}

.c3 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e90e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c10 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c10:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c10::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c8 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c8 .c9 {
  padding: 0 var(--spacing150);
  padding-right: 0;
}

.c8 input::-ms-clear {
  display: none;
}

.c8 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c7 {
  -webkit-flex: none;
  -ms-flex: none;
  flex: none;
  width: 135px;
}

.c0 .c7 .c9 {
  margin-right: -8px;
}

.c11 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: var(--sizing500);
}

.c11:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

<div>
  <div
    class="e-field e-date-field e-hidden"
    data-label="Test Field Title"
    data-testid="e-date-field e-field-label-testFieldTitle e-field-bind-test-date-field"
  >
    <div
      class="c0"
      data-component="date"
      role="presentation"
    >
      <div
        class="c1 c2"
        data-component="date-input"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-date-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-date-field"
              id="TestPage-test-date-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7 c8"
              role="presentation"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c9 c10"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-date-field"
                name="test-date-field"
                type="text"
                value="25/03/2018"
              />
              <span
                aria-hidden="true"
                class="c11"
                data-element="input-icon-toggle"
                data-role="input-icon-toggle"
                tabindex="-1"
              >
                <span
                  class="c12"
                  data-component="icon"
                  data-element="calendar"
                  data-role="icon"
                  font-size="small"
                  type="calendar"
                />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Date component connected Snapshots should render readOnly 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin: var(--spacing000);
}

.c3 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin030);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e90e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c10 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c10:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c10::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c8 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background-color: var(--colorsUtilityReadOnly400);
  border-color: var(--colorsUtilityReadOnly600);
}

.c8 .c9 {
  padding: 0 var(--spacing150);
  padding-right: 0;
}

.c8 input::-ms-clear {
  display: none;
}

.c8 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c7 {
  -webkit-flex: none;
  -ms-flex: none;
  flex: none;
  width: 135px;
}

.c0 .c7 .c9 {
  margin-right: -8px;
}

.c11 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: var(--sizing500);
  cursor: default;
}

.c11:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

<div>
  <div
    class="e-field e-date-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-date-field e-field-label-testFieldTitle e-field-bind-test-date-field"
  >
    <div
      class="c0"
      data-component="date"
      role="presentation"
    >
      <div
        class="c1 c2"
        data-component="date-input"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-date-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-date-field"
              id="TestPage-test-date-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7 c8"
              readonly=""
              role="presentation"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c9 c10"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-date-field"
                name="test-date-field"
                placeholder=""
                readonly=""
                type="text"
                value="25/03/2018"
              />
              <span
                aria-hidden="true"
                class="c11"
                data-element="input-icon-toggle"
                data-role="input-icon-toggle"
                readonly=""
                tabindex="-1"
              >
                <span
                  class="c12"
                  data-component="icon"
                  data-element="calendar"
                  data-role="icon"
                  disabled=""
                  font-size="small"
                  type="calendar"
                />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Date component connected Snapshots should render with default properties 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin: var(--spacing000);
}

.c3 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e90e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c10 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c10:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c10::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c8 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c8 .c9 {
  padding: 0 var(--spacing150);
  padding-right: 0;
}

.c8 input::-ms-clear {
  display: none;
}

.c8 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c7 {
  -webkit-flex: none;
  -ms-flex: none;
  flex: none;
  width: 135px;
}

.c0 .c7 .c9 {
  margin-right: -8px;
}

.c11 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: var(--sizing500);
}

.c11:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

<div>
  <div
    class="e-field e-date-field"
    data-label="Test Field Title"
    data-testid="e-date-field e-field-label-testFieldTitle e-field-bind-test-date-field"
  >
    <div
      class="c0"
      data-component="date"
      role="presentation"
    >
      <div
        class="c1 c2"
        data-component="date-input"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-date-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-date-field"
              id="TestPage-test-date-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7 c8"
              role="presentation"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c9 c10"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-date-field"
                name="test-date-field"
                type="text"
                value="25/03/2018"
              />
              <span
                aria-hidden="true"
                class="c11"
                data-element="input-icon-toggle"
                data-role="input-icon-toggle"
                tabindex="-1"
              >
                <span
                  class="c12"
                  data-component="icon"
                  data-element="calendar"
                  data-role="icon"
                  font-size="small"
                  type="calendar"
                />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Date component connected Snapshots should render with minDate and maxDate sent as Date object 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin: var(--spacing000);
}

.c3 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e90e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c10 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c10:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c10::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c8 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c8 .c9 {
  padding: 0 var(--spacing150);
  padding-right: 0;
}

.c8 input::-ms-clear {
  display: none;
}

.c8 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c7 {
  -webkit-flex: none;
  -ms-flex: none;
  flex: none;
  width: 135px;
}

.c0 .c7 .c9 {
  margin-right: -8px;
}

.c11 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: var(--sizing500);
}

.c11:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

<div>
  <div
    class="e-field e-date-field"
    data-label="Test Field Title"
    data-testid="e-date-field e-field-label-testFieldTitle e-field-bind-test-date-field"
  >
    <div
      class="c0"
      data-component="date"
      role="presentation"
    >
      <div
        class="c1 c2"
        data-component="date-input"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-date-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-date-field"
              id="TestPage-test-date-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7 c8"
              role="presentation"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c9 c10"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-date-field"
                name="test-date-field"
                type="text"
                value="25/03/2018"
              />
              <span
                aria-hidden="true"
                class="c11"
                data-element="input-icon-toggle"
                data-role="input-icon-toggle"
                tabindex="-1"
              >
                <span
                  class="c12"
                  data-component="icon"
                  data-element="calendar"
                  data-role="icon"
                  font-size="small"
                  type="calendar"
                />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Date component connected Snapshots should render with minDate and maxDate sent as string 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin: var(--spacing000);
}

.c3 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e90e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c10 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c10:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c10::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c8 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c8 .c9 {
  padding: 0 var(--spacing150);
  padding-right: 0;
}

.c8 input::-ms-clear {
  display: none;
}

.c8 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c7 {
  -webkit-flex: none;
  -ms-flex: none;
  flex: none;
  width: 135px;
}

.c0 .c7 .c9 {
  margin-right: -8px;
}

.c11 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: var(--sizing500);
}

.c11:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

<div>
  <div
    class="e-field e-date-field"
    data-label="Test Field Title"
    data-testid="e-date-field e-field-label-testFieldTitle e-field-bind-test-date-field"
  >
    <div
      class="c0"
      data-component="date"
      role="presentation"
    >
      <div
        class="c1 c2"
        data-component="date-input"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-date-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-date-field"
              id="TestPage-test-date-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7 c8"
              role="presentation"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c9 c10"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-date-field"
                name="test-date-field"
                type="text"
                value="25/03/2018"
              />
              <span
                aria-hidden="true"
                class="c11"
                data-element="input-icon-toggle"
                data-role="input-icon-toggle"
                tabindex="-1"
              >
                <span
                  class="c12"
                  data-component="icon"
                  data-element="calendar"
                  data-role="icon"
                  font-size="small"
                  type="calendar"
                />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Date component connected Snapshots should render without value 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin: var(--spacing000);
}

.c3 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e90e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c10 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c10:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c10::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c8 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c8 .c9 {
  padding: 0 var(--spacing150);
  padding-right: 0;
}

.c8 input::-ms-clear {
  display: none;
}

.c8 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c7 {
  -webkit-flex: none;
  -ms-flex: none;
  flex: none;
  width: 135px;
}

.c0 .c7 .c9 {
  margin-right: -8px;
}

.c11 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: var(--sizing500);
}

.c11:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

<div>
  <div
    class="e-field e-date-field"
    data-label="Test Field Title"
    data-testid="e-date-field e-field-label-testFieldTitle e-field-bind-test-empty-date-field"
  >
    <div
      class="c0"
      data-component="date"
      role="presentation"
    >
      <div
        class="c1 c2"
        data-component="date-input"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-empty-date-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-empty-date-field"
              id="TestPage-test-empty-date-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7 c8"
              role="presentation"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c9 c10"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-empty-date-field"
                name="test-empty-date-field"
                type="text"
                value=""
              />
              <span
                aria-hidden="true"
                class="c11"
                data-element="input-icon-toggle"
                data-role="input-icon-toggle"
                tabindex="-1"
              >
                <span
                  class="c12"
                  data-component="icon"
                  data-element="calendar"
                  data-role="icon"
                  font-size="small"
                  type="calendar"
                />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Date component unconnected Snapshots should render just fine 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin: var(--spacing000);
}

.c3 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e90e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c10 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c10:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c10::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c8 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c8 .c9 {
  padding: 0 var(--spacing150);
  padding-right: 0;
}

.c8 input::-ms-clear {
  display: none;
}

.c8 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c7 {
  -webkit-flex: none;
  -ms-flex: none;
  flex: none;
  width: 135px;
}

.c0 .c7 .c9 {
  margin-right: -8px;
}

.c11 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: var(--sizing500);
}

.c11:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

<div>
  <div
    class="e-field e-date-field"
    data-label="Test Field Title"
    data-testid="e-date-field e-field-label-testFieldTitle e-field-bind-test-date-field"
  >
    <div
      class="c0"
      data-component="date"
      role="presentation"
    >
      <div
        class="c1 c2"
        data-component="date-input"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-date-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-date-field"
              id="TestPage-test-date-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7 c8"
              role="presentation"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c9 c10"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-date-field"
                name="test-date-field"
                type="text"
                value="05/08/2017"
              />
              <span
                aria-hidden="true"
                class="c11"
                data-element="input-icon-toggle"
                data-role="input-icon-toggle"
                tabindex="-1"
              >
                <span
                  class="c12"
                  data-component="icon"
                  data-element="calendar"
                  data-role="icon"
                  font-size="small"
                  type="calendar"
                />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
