import { DateValue } from '@sage/xtrem-date-time';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import { DateControlObject } from '../../../control-objects';
import { <PERSON>Key } from '../../../types';
import type { DateProperties } from '../date-types';

describe('Date Field', () => {
    const screenId = 'TestPage';
    let dateFieldControlObject: DateControlObject;
    const expectedDateValue = DateValue.today().toString();
    let getUiComponentProperties: (_screenId: string, _elementId: string) => any;
    let setUiComponentProperties: (_screenId: string, _elementId: string, newValue: any) => void;
    let dateFieldProperties: DateProperties;

    beforeEach(() => {
        dateFieldProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: false,
            isDisabled: true,
        };
        getUiComponentProperties = () => dateFieldProperties;
        setUiComponentProperties = (_screenId, _elementId, newValue) => {
            Object.keys(newValue).forEach(key => {
                dateFieldProperties[key] = newValue[key];
            });
        };
        dateFieldControlObject = createFieldControlObject<FieldKey.Date>(
            FieldKey.Date,
            screenId,
            DateControlObject,
            'test',
            '05/07/1924',
            dateFieldProperties,
            {
                setUiComponentProperties,
                getUiComponentProperties,
            },
        );
    });

    it('getting field value', () => {
        expect(dateFieldControlObject.value).toEqual('05/07/1924');
    });

    describe('setting and getting updated values', () => {
        it('should set the title', () => {
            const testFixture = 'Test Numeric Field Title';
            expect(dateFieldControlObject.title).not.toEqual(testFixture);
            dateFieldControlObject.title = testFixture;
            expect(dateFieldControlObject.title).toEqual(testFixture);
        });

        it('should set minDate field when parameter type is Date object', () => {
            const minDate = new Date();
            expect(dateFieldControlObject.minDate).not.toEqual(expectedDateValue);
            dateFieldControlObject.minDate = minDate;
            expect(dateFieldControlObject.minDate).toEqual(expectedDateValue);
        });

        it('should set maxDate field when parameter type is Date object', () => {
            const maxDate = new Date();
            expect(dateFieldControlObject.maxDate).not.toEqual(expectedDateValue);
            dateFieldControlObject.maxDate = maxDate;
            expect(dateFieldControlObject.maxDate).toEqual(expectedDateValue);
        });

        it('should set minDate field when parameter type is DateValue object', () => {
            const minDate = DateValue.today();
            expect(dateFieldControlObject.minDate).not.toEqual(expectedDateValue);
            dateFieldControlObject.minDate = minDate;
            expect(dateFieldControlObject.minDate).toEqual(expectedDateValue);
        });

        it('should set maxDate field when parameter type is DateValue object', () => {
            const maxDate = DateValue.today();
            expect(dateFieldControlObject.maxDate).not.toEqual(expectedDateValue);
            dateFieldControlObject.maxDate = maxDate;
            expect(dateFieldControlObject.maxDate).toEqual(expectedDateValue);
        });
        it('should set minDate field when parameter type is a string', () => {
            const minDate = DateValue.today().toString();
            expect(dateFieldControlObject.minDate).not.toEqual(expectedDateValue);
            dateFieldControlObject.minDate = minDate;
            expect(dateFieldControlObject.minDate).toEqual(expectedDateValue);
        });

        it('should set maxDate field when parameter type is a string', () => {
            const maxDate = DateValue.today().toString();
            expect(dateFieldControlObject.maxDate).not.toEqual(expectedDateValue);
            dateFieldControlObject.maxDate = maxDate;
            expect(dateFieldControlObject.maxDate).toEqual(expectedDateValue);
        });

        it('should set minDate field when parameter type is string', () => {
            const minDate = '2022-11-12';
            expect(dateFieldControlObject.minDate).not.toEqual(expectedDateValue);
            dateFieldControlObject.minDate = minDate;
            expect(dateFieldControlObject.minDate).toEqual(minDate);
        });

        it('should set maxDate field when parameter type is string', () => {
            const maxDate = '2022-12-31';
            expect(dateFieldControlObject.maxDate).not.toEqual(expectedDateValue);
            dateFieldControlObject.maxDate = maxDate;
            expect(dateFieldControlObject.maxDate).toEqual(maxDate);
        });

        it('should retrieve isHidden from properties', () => {
            expect(dateFieldControlObject.isHidden).toBe(dateFieldProperties.isHidden);
        });

        it('should set isHidden property in object properties', () => {
            const newValue = true;
            dateFieldControlObject.isHidden = newValue;
            expect(dateFieldControlObject.isHidden).toBe(newValue);
        });

        it('should set value object properties', () => {
            const newValue = '2020-04-29';
            expect(dateFieldControlObject.value).not.toBe(newValue);
            dateFieldControlObject.value = newValue;
            expect(dateFieldControlObject.value).toBe(newValue);
        });
    });
});
