import type { LocalizeLocale } from '@sage/xtrem-shared';
import Date from 'carbon-react/esm/components/date';
import I18nProvider from 'carbon-react/esm/components/i18n-provider';
import uniqueId from 'lodash/uniqueId';
import React from 'react';
import {
    getInitialCellEditorState,
    isFinishedEditingPressed,
    isKeyPressedDateComponent,
    isLeftOrRight,
    setDefaultAgGridInputStyles,
} from '../../../utils/ag-grid/ag-grid-cell-editor-utils';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import { carbonLocale } from '../../../utils/carbon-locale';
import { isDeleteOrBackspace } from '../../../utils/keyboard-event-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import type { NestedDateProperties } from './date-types';

type EventTargetOnDateChange = { value: { formattedValue: string; rawValue: string } };
interface DateEditorState {
    value: string;
    highlightOnFocus: boolean;
}

export default class DateCellEditor extends React.Component<CellParams<NestedDateProperties>, DateEditorState> {
    constructor(props: CellParams<NestedDateProperties>) {
        super(props);
        this.state = getInitialCellEditorState({ eventKey: props.eventKey, initialValue: props.initialValue });
        this.onKeyDown = this.onKeyDown.bind(this);
        this.handleChange = this.handleChange.bind(this);
    }

    componentDidMount(): void {
        const input = this.props.eGridCell.querySelector('input');
        if (!input) {
            return;
        }
        // try to match the styles of ag-grid
        setDefaultAgGridInputStyles(input);
        const inputDiv = input.closest('div');
        if (inputDiv) {
            inputDiv.style.width = '100%';
            inputDiv.style.height = '100%';
            inputDiv.style.border = 'none';
            inputDiv.style.display = 'flex';
            inputDiv.style.alignItems = 'center';
        }

        input.focus();
        if (this.state.highlightOnFocus) {
            input.select();
            this.setState({
                highlightOnFocus: false,
            });
        } else {
            // when we started editing, we want the caret at the end, not the start.
            // this comes into play in two scenarios: a) when user hits F2 and b)
            // when user hits a printable character, then on IE (and only IE) the caret
            // was placed after the first character, thus 'apply' would end up as 'pplea'
            const length = input.value ? input.value.length : 0;
            if (length > 0) {
                input.setSelectionRange(length, length);
            }
        }
    }

    handleChange(event: React.ChangeEvent<EventTargetOnDateChange>): void {
        if (!event.target?.value) {
            return;
        }
        this.setState({ value: event.target.value.formattedValue }, () => {
            if (!event.target.value.formattedValue && !event.target.value.rawValue) {
                this.props.onValueChange(null);
                return;
            }

            if (event.target.value.rawValue != null) {
                this.props.onValueChange(event.target.value.rawValue);
            }
        });
    }

    onKeyDown(event: React.KeyboardEvent): void {
        if (isLeftOrRight(event) || isDeleteOrBackspace(event)) {
            event.stopPropagation();
            return;
        }

        if (!isFinishedEditingPressed(event) && !isKeyPressedDateComponent(event) && event.preventDefault) {
            event.preventDefault();
        }
    }

    getCarbonProps = (): any => {
        return {
            autoComplete: 'off',
            name: this.props.column?.getColDef().field,
            className: '',
            size: 'small',
            id: uniqueId(),
        };
    };

    render(): React.ReactNode {
        const width = this.props.eGridCell?.style?.width || '200px';
        const { fieldProperties, tableElementId, screenId, data } = this.props;
        const rowValue = splitValueToMergedValue(data);
        const maxDate = resolveByValue({
            screenId,
            propertyValue: fieldProperties.maxDate,
            rowValue,
            skipHexFormat: true,
            fieldValue: rowValue,
        });
        const minDate = resolveByValue({
            screenId,
            propertyValue: fieldProperties.minDate,
            rowValue,
            skipHexFormat: true,
            fieldValue: rowValue,
        });

        const allColumns = this.props.api.getColumns() || [];

        return (
            <div
                className="ag-cell-edit-wrapper e-nested-cell-field-date"
                data-testid={`${tableElementId}-${this.props.node.rowIndex}-${allColumns.indexOf(this.props.column) + 1}`}
                style={{ width }}
            >
                <I18nProvider
                    locale={carbonLocale((this.props.colDef.cellEditorParams?.locale as LocalizeLocale) || 'en-US')}
                >
                    <Date
                        pickerProps={{
                            className: 'ag-custom-component-popup',
                        }}
                        allowEmptyValue={true}
                        onChange={this.handleChange}
                        value={this.state.value || ''}
                        maxDate={maxDate}
                        minDate={minDate}
                        {...this.getCarbonProps()}
                        onKeyDown={this.onKeyDown}
                    />
                </I18nProvider>
            </div>
        );
    }
}
