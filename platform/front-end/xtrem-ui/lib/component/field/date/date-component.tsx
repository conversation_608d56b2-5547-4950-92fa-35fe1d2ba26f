import { formatDateToCurrentLocale, isValidIsoDate, datePropertyValueToDateString } from '@sage/xtrem-date-time';
import CarbonDateComponent from 'carbon-react/esm/components/date';
import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { isUnmodifiedAlphabeticalChar } from '../../../utils/keyboard-event-utils';
import type { DateProperties } from '../../control-objects';
import { getCommonCarbonComponentProperties, isFieldDisabled } from '../carbon-helpers';
import { CarbonWrapper } from '../carbon-wrapper';
import { ErrorableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { DateComponentProps, DateDecoratorProperties } from './date-types';
import { debounce, throttle } from 'lodash';
import { triggerFieldEvent } from '../../../utils/events';
import I18nProvider from 'carbon-react/esm/components/i18n-provider';
import { carbonLocale } from '../../../utils/carbon-locale';

type EventTargetOnDateChange = { value: { formattedValue: string; rawValue: string } };

export interface DateComponentState {
    internalError?: string;
    formattedValue?: string;
    rawValue?: string;
}

export class DateComponent extends ErrorableFieldBaseComponent<
    DateProperties,
    string,
    NestedFieldsAdditionalProperties,
    DateComponentState
> {
    private readonly dateInputRef = React.createRef<any>();

    constructor(props: DateComponentProps) {
        super(props);
        this.state = { formattedValue: this.getFormattedDate(this.props.value), rawValue: this.props.value };
    }

    private readonly onInputValueChanged = debounce(async (searchText: string) => {
        await triggerFieldEvent(this.props.screenId, this.props.elementId, 'onInputValueChange', searchText);
    }, 150);

    private readonly dispatchChange = throttle((rawValue?: string) => {
        handleChange(
            this.props.elementId,
            rawValue || null,
            this.props.setFieldValue,
            this.validate(),
            this.triggerChangeListener,
        );
    }, 40);

    UNSAFE_componentWillReceiveProps(nextProps: DateComponentProps): void {
        if (this.props.isInFocus && !nextProps.isInFocus && this.state.rawValue !== this.props.value) {
            this.dispatchChange(this.state.rawValue);
        }

        const nextRawValue = nextProps.value;
        if (!this.state.rawValue || this.state.rawValue !== nextRawValue) {
            this.setState({ formattedValue: this.getFormattedDate(nextRawValue), rawValue: nextRawValue });
        }
    }

    private readonly onBlur = (event: React.ChangeEvent<EventTargetOnDateChange>): void => {
        const carbonProps = getCommonCarbonComponentProperties(this.props);
        carbonProps.onBlur();
        this.setState({ internalError: undefined });

        if (this.isReadOnly() || this.isDisabled()) {
            return;
        }

        if (
            datePropertyValueToDateString(event.target.value.rawValue) !==
            datePropertyValueToDateString(this.props.value)
        ) {
            this.dispatchChange(event.target.value.rawValue);
        }
    };

    private readonly onChange = async (event: React.ChangeEvent<EventTargetOnDateChange>): Promise<void> => {
        if (event.target?.value) {
            this.setState({ ...event.target.value });
            await this.onInputValueChanged(event.target.value.rawValue || event.target.value.formattedValue);
        }
    };

    private readonly onKeyDown = (event: React.KeyboardEvent<HTMLInputElement>): void => {
        if (isUnmodifiedAlphabeticalChar(event)) {
            event.preventDefault();
        }
    };

    private readonly getFormattedDate = (rawDateValue?: string): string => {
        return rawDateValue && isValidIsoDate(rawDateValue)
            ? formatDateToCurrentLocale(rawDateValue, this.props.locale, 'FullDate')
            : '';
    };

    private readonly validate = (): ((bind: string, value: string) => Promise<ValidationResult[] | undefined>) => {
        if (this.state.internalError) {
            this.props.addInternalError?.(this.props.elementId, {
                screenId: this.props.screenId,
                elementId: this.props.elementId,
                validationRule: 'dateValue',
                message: this.state.internalError,
            } as ValidationResult);
        } else {
            this.props.removeInternalError?.(this.props.elementId);
        }

        return this.props.validate;
    };

    render(): React.ReactNode {
        const maxDate = datePropertyValueToDateString(this.getResolvedProperty('maxDate')) || undefined;
        const minDate = datePropertyValueToDateString(this.getResolvedProperty('minDate')) || undefined;
        return (
            <CarbonWrapper
                {...this.props}
                className="e-date-field"
                componentName="date"
                componentRef={this.componentRef}
                handlersArguments={this.props.handlersArguments}
                noReadOnlySupport={true}
                value={this.state.formattedValue}
            >
                <I18nProvider locale={carbonLocale(this.props.locale || 'en-US')}>
                    <CarbonDateComponent
                        {...getCommonCarbonComponentProperties(this.props)}
                        disablePortal={false}
                        ref={this.dateInputRef}
                        allowEmptyValue={true}
                        onClick={this.getClickHandler()}
                        onChange={this.onChange}
                        onKeyDown={this.onKeyDown}
                        value={this.state.formattedValue || ''}
                        maxDate={maxDate}
                        minDate={minDate}
                        onBlur={this.onBlur}
                        error={
                            !this.props.isParentDisabled &&
                            !this.props.isParentHidden &&
                            !isFieldDisabled(
                                this.props.screenId,
                                this.props.fieldProperties as DateProperties,
                                this.props.value,
                                this.props.handlersArguments?.rowValue,
                            )
                                ? this.props.validationErrors?.[0]?.message || this.state.internalError
                                : undefined
                        }
                    />
                </I18nProvider>
            </CarbonWrapper>
        );
    }
}

const extendedMapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: DateComponentProps,
): Partial<DateComponentProps> => {
    const defaultMapDispatchToProps = mapDispatchToProps<DateDecoratorProperties>()(dispatch, props);
    return {
        ...defaultMapDispatchToProps,
        addInternalError: (elementId: string, errorMessage: ValidationResult): void => {
            dispatch(xtremRedux.actions.addInternalError(props.screenId, elementId, errorMessage));
        },
        removeInternalError: (elementId: string): void => {
            dispatch(xtremRedux.actions.removeInternalError(props.screenId, elementId));
        },
    };
};

export const ConnectedDateComponent = connect(mapStateToProps, extendedMapDispatchToProps)(DateComponent);

export default ConnectedDateComponent;
