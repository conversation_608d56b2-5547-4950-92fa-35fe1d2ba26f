import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { DropdownListComponentProps } from './dropdown-list-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const ConnectedDropdownListComponent = React.lazy(() => import('./dropdown-list-component'));

export function AsyncConnectedDropdownListComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedDropdownListComponent {...props} />
        </React.Suspense>
    );
}

const DropdownListComponent = React.lazy(() =>
    import('./dropdown-list-component').then(c => ({ default: c.DropdownListComponent })),
);

export function AsyncDropdownListComponent(props: DropdownListComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <DropdownListComponent {...props} />
        </React.Suspense>
    );
}
