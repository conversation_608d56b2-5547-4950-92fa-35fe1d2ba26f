import type { ClientNode } from '@sage/xtrem-client';
import type { Dict } from '@sage/xtrem-shared';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import type {
    ChangeableOverrideDecoratorProperties,
    ClickableOverrideDecoratorProperties,
} from '../../../utils/decorator-utils';
import type { BlockControlObject, TileControlObject } from '../../container/container-control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedPropertiesWrapper } from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasEmptyValue,
    HasIcon,
    HasOptions,
    HasOptionType,
    HasParent,
    HasPlaceholder,
    HasSound,
    Mappable,
    Nested,
    NestedChangeable,
    NestedClickable,
    NestedValidatable,
    Sizable,
    Validatable,
} from '../traits';

export interface DropdownListProperties<CT extends ScreenExtension<CT> = ScreenBase, ContextNodeType = void>
    extends EditableFieldProperties<CT, ContextNodeType>,
        HasEmptyValue,
        HasOptions<CT>,
        HasOptionType,
        HasParent<CT, BlockControlObject<CT> | TileControlObject<CT>>,
        HasPlaceholder,
        HasIcon,
        HasSound<CT>,
        Mappable<CT>,
        Sizable {}

export interface NestedDropdownListProperties<
    CT extends ScreenBase = ScreenBase,
    ContextNodeType extends ClientNode = any,
> extends NestedPropertiesWrapper<DropdownListProperties<CT, ContextNodeType>>,
        Nested<ContextNodeType>,
        NestedChangeable<CT>,
        NestedClickable<CT, ContextNodeType>,
        HasEmptyValue,
        NestedValidatable<CT, string, ContextNodeType> {}

export interface DropdownListDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<DropdownListProperties<CT>, '_controlObjectType'>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        Changeable<CT>,
        Clickable<CT>,
        Validatable<CT, string> {}

export type DropdownListExtensionDecoratorProperties<T extends ScreenExtension<T>> =
    ChangeableOverrideDecoratorProperties<DropdownListDecoratorProperties<Extend<T>>, T> &
        ClickableOverrideDecoratorProperties<DropdownListDecoratorProperties<Extend<T>>, T>;

export type DropdownListComponentProps = BaseEditableComponentProperties<DropdownListProperties, string> &
    NestedFieldsAdditionalProperties & {
        localizedOptions?: Dict<string>;
        enumOptions?: string[];
    };

export interface DropdownListInternalProps {
    enumOptions?: string[];
    localizedOptions: Dict<string>;
    screenId: string;
    value?: string;
}

export interface DropdownListCellEditorProperties extends CellParams<NestedDropdownListProperties> {
    enumOptions?: string[];
    localizedOptions?: Dict<string>;
}
