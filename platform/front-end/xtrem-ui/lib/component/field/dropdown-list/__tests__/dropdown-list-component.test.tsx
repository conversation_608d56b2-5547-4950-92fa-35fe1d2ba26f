import { cleanup, fireEvent, render, screen, waitFor, within } from '@testing-library/react';
import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import {
    addFieldToState,
    applyActionMocks,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    userEvent,
} from '../../../../__tests__/test-helpers';
import * as xtremRedux from '../../../../redux';
import type { ScreenBase } from '../../../../service/screen-base';
import type { DropdownListDecoratorProperties } from '../../../decorator-properties';
import { FieldKey } from '../../../types';
import { ConnectedDropdownListComponent } from '../dropdown-list-component';

const Test = {
    Screen: {
        Id: 'TestScreenId',
    },
    Field: {
        Id: 'TestFieldId',
        Title: 'TestFieldTitle',
        Options: ['rock', 'paper', 'scissors'],
        OptionType: '@sage/xtrem-test/RockPaperScissorsEnum',
        OptionTypeTranslated: '@sage/xtrem-test/RockPaperScissorsTranslatedEnum',
    },
};

const getConnectedField = store => (
    <Provider store={store}>
        <ConnectedDropdownListComponent screenId={Test.Screen.Id} elementId={Test.Field.Id} />
    </Provider>
);

jest.useFakeTimers();

describe('DropdownList Component', () => {
    let state: xtremRedux.XtremAppState;
    let store: MockStoreEnhanced<xtremRedux.XtremAppState>;

    let fieldProperties: DropdownListDecoratorProperties<ScreenBase>;

    beforeEach(() => {
        fieldProperties = {
            title: Test.Field.Title,
            onChange: jest.fn(),
            onClick: jest.fn(),
            onError: jest.fn(),
        };

        const setFieldValueMockImpl = jest.fn(() => () => Promise.resolve());
        jest.spyOn(xtremRedux.actions, 'setFieldValue').mockImplementation(setFieldValueMockImpl);

        state = getMockState({
            enumTypes: {
                RockPaperScissorsEnum: ['rock', 'paper', 'scissors', 'lizard', 'spock'],
                RockPaperScissorsTranslatedEnum: ['rock', 'paper', 'scissors', 'lizard', 'spock'],
            },
            translations: {
                'en-US': {
                    '@sage/xtrem-test/enums__rock_paper_scissors_translated_enum__rock': 'Translated Rock',
                    '@sage/xtrem-test/enums__rock_paper_scissors_translated_enum__paper': 'Translated Paper',
                    '@sage/xtrem-test/enums__rock_paper_scissors_translated_enum__scissors': 'Translated Scissors',
                    '@sage/xtrem-test/enums__rock_paper_scissors_translated_enum__lizard': 'Translated Lizard',
                    '@sage/xtrem-test/enums__rock_paper_scissors_translated_enum__spock': 'Translated Spock',
                },
            },
        });

        state.screenDefinitions[Test.Screen.Id] = getMockPageDefinition(Test.Screen.Id);
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
        cleanup();
    });

    describe('info and warning', () => {
        it('should render with an info message', async () => {
            fieldProperties.infoMessage = 'Info message!!';
            addFieldToState(FieldKey.DropdownList, state, Test.Screen.Id, Test.Field.Id, fieldProperties, null);
            store = getMockStore(state);
            const { baseElement } = render(getConnectedField(store));

            fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
            await waitFor(() => {
                expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
            });
        });

        it('should render with an info message from a callback', async () => {
            fieldProperties.infoMessage = 'Info message!!';
            addFieldToState(FieldKey.DropdownList, state, Test.Screen.Id, Test.Field.Id, fieldProperties, null);
            store = getMockStore(state);
            const { baseElement } = render(getConnectedField(store));
            fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
            await waitFor(() => {
                expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
            });
        });

        it('should render with an warning message', async () => {
            fieldProperties.warningMessage = 'Warning message!!';
            addFieldToState(FieldKey.DropdownList, state, Test.Screen.Id, Test.Field.Id, fieldProperties, null);
            store = getMockStore(state);
            const { baseElement } = render(getConnectedField(store));
            fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
            await waitFor(() => {
                expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Warning message!!');
            });
        });

        it('should render with an warning message from a callback', async () => {
            fieldProperties.warningMessage = () => 'Warning message!!';
            addFieldToState(FieldKey.DropdownList, state, Test.Screen.Id, Test.Field.Id, fieldProperties, null);
            store = getMockStore(state);
            const { baseElement } = render(getConnectedField(store));
            fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
            await waitFor(() => {
                expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Warning message!!');
            });
        });

        it('should prioritize warnings over info messages', () => {
            fieldProperties.warningMessage = () => 'Warning message!!';
            fieldProperties.infoMessage = () => 'Info message!!';
            addFieldToState(FieldKey.DropdownList, state, Test.Screen.Id, Test.Field.Id, fieldProperties, null);
            store = getMockStore(state);
            const wrapper = render(getConnectedField(store));
            expect(wrapper.baseElement.querySelector('[data-element="info"]')).toBeNull();
            expect(wrapper.baseElement.querySelector('[data-element="warning"]')).not.toBeNull();
        });

        it('should prioritize validation errors over warnings and info messages', () => {
            fieldProperties.warningMessage = () => 'Warning message!!';
            fieldProperties.infoMessage = () => 'Info message!!';
            state.screenDefinitions[Test.Screen.Id].errors[Test.Field.Id] = [
                { elementId: Test.Field.Id, screenId: Test.Screen.Id, validationRule: 'isMandatory', message: 'Error' },
            ];
            addFieldToState(FieldKey.DropdownList, state, Test.Screen.Id, Test.Field.Id, fieldProperties, null);
            store = getMockStore(state);
            const wrapper = render(getConnectedField(store));
            expect(wrapper.baseElement.querySelector('[data-element="info"]')).toBeNull();
            expect(wrapper.baseElement.querySelector('[data-element="warning"]')).toBeNull();
            expect(wrapper.baseElement.querySelector('[data-element="error"]')).not.toBeNull();
        });
    });

    describe('given options property', () => {
        beforeEach(() => {
            fieldProperties.options = Test.Field.Options;
            addFieldToState(
                FieldKey.DropdownList,
                state,
                Test.Screen.Id,

                Test.Field.Id,
                fieldProperties,
                null,
            );
            store = getMockStore(state);
        });

        it('should render component correctly', async () => {
            render(getConnectedField(store));
            const input = screen.getByLabelText(Test.Field.Title);
            await userEvent.click(input);

            const options = await screen.findByTestId('e-ui-select-dropdown');
            await waitFor(() => {
                expect(within(options).getAllByTestId('e-ui-select-suggestion').length).toEqual(3);
            });

            const selection = within(options).getByText('rock');
            within(options).getByText('paper');
            within(options).getByText('scissors');

            expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
            await userEvent.click(selection);
            expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(Test.Screen.Id, Test.Field.Id, 'rock', true);
        });
    });

    describe('given optionType property', () => {
        beforeEach(() => {
            fieldProperties.optionType = Test.Field.OptionType;
            addFieldToState(
                FieldKey.DropdownList,
                state,
                Test.Screen.Id,

                Test.Field.Id,
                fieldProperties,
                null,
            );
            store = getMockStore(state);
        });

        it('should render component correctly', async () => {
            render(getConnectedField(store));
            const input = screen.getByLabelText(Test.Field.Title);
            await userEvent.click(input);

            const options = await screen.findByTestId('e-ui-select-dropdown');
            await waitFor(() => {
                expect(within(options).getAllByTestId('e-ui-select-suggestion').length).toEqual(5);
            });

            within(options).getByText('rock');
            within(options).getByText('paper');
            within(options).getByText('scissors');
            within(options).getByText('lizard');
            within(options).getByText('spock');

            expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
            await userEvent.click(within(options).getByText('lizard'));
            expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(
                Test.Screen.Id,
                Test.Field.Id,
                'lizard',
                true,
            );
        });
    });

    describe('given optionType property translated', () => {
        beforeEach(() => {
            fieldProperties.optionType = Test.Field.OptionTypeTranslated;
            addFieldToState(
                FieldKey.DropdownList,
                state,
                Test.Screen.Id,

                Test.Field.Id,
                fieldProperties,
                null,
            );
            store = getMockStore(state);
        });

        it('should render component correctly', async () => {
            render(getConnectedField(store));
            const input = screen.getByLabelText(Test.Field.Title);
            await userEvent.click(input);

            const options = await screen.findByTestId('e-ui-select-dropdown');
            await waitFor(() => {
                expect(within(options).getAllByTestId('e-ui-select-suggestion').length).toEqual(5);
            });

            within(options).getByText('Translated Rock');
            within(options).getByText('Translated Paper');
            within(options).getByText('Translated Scissors');
            within(options).getByText('Translated Lizard');
            within(options).getByText('Translated Spock');

            expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
            await userEvent.click(within(options).getByText('Translated Lizard'));
            expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(
                Test.Screen.Id,
                Test.Field.Id,
                'lizard',
                true,
            );
        });
    });

    describe('empty value', () => {
        beforeEach(() => {
            fieldProperties.optionType = Test.Field.OptionType;
            fieldProperties.hasEmptyValue = true;
            addFieldToState(
                FieldKey.DropdownList,
                state,
                Test.Screen.Id,

                Test.Field.Id,
                fieldProperties,
                null,
            );
            store = getMockStore(state);
        });

        it('should have first item with null value', async () => {
            render(getConnectedField(store));
            const input = screen.getByLabelText(Test.Field.Title);
            await userEvent.click(input);

            const options = await screen.findByTestId('e-ui-select-dropdown');
            await waitFor(() => {
                expect(within(options).getAllByTestId('e-ui-select-suggestion').length).toEqual(6);
            });

            const option = within(options).getAllByTestId('e-ui-select-suggestion')[0];
            await userEvent.click(option);
            await waitFor(() => {
                expect(store.getState().screenDefinitions[Test.Screen.Id].values[Test.Field.Id]).toEqual(null);
            });
        });
    });

    describe('given map function', () => {
        beforeEach(() => {
            fieldProperties.options = Test.Field.Options;
            fieldProperties.map = jest.fn().mockImplementation((value: string) => {
                return `Mapped ${value[0].toUpperCase()}${value.substring(1)}`;
            });
            addFieldToState(
                FieldKey.DropdownList,
                state,
                Test.Screen.Id,

                Test.Field.Id,
                fieldProperties,
                null,
            );
            store = getMockStore(state);
        });

        it('should map display text correctly', async () => {
            render(getConnectedField(store));
            const input = screen.getByLabelText(Test.Field.Title);
            await userEvent.click(input);

            const options = await screen.findByTestId('e-ui-select-dropdown');
            await waitFor(() => {
                expect(within(options).getAllByTestId('e-ui-select-suggestion').length).toEqual(3);
            });

            within(options).getByText('Mapped Rock');
            within(options).getByText('Mapped Paper');
            within(options).getByText('Mapped Scissors');

            expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
            await userEvent.click(within(options).getByText('Mapped Rock'));
            expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(Test.Screen.Id, Test.Field.Id, 'rock', true);
        });

        it('should render with the mapped value', async () => {
            state.screenDefinitions[Test.Screen.Id].values[Test.Field.Id] = 'paper';
            render(getConnectedField(store));
            const input: HTMLInputElement = screen.getByLabelText(Test.Field.Title);

            await waitFor(() => {
                expect(input.value).toEqual('Mapped Paper');
            });
        });
    });
});
