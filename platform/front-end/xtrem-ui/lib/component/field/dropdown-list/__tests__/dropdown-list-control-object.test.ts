import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import type { DropdownListProperties } from '../../../control-objects';
import { DropdownListControlObject } from '../../../control-objects';
import type { FieldKey } from '../../../types';
import * as stateUtils from '../../../../utils/state-utils';
import { getMockPageDefinition } from '../../../../__tests__/test-helpers';

jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() => getMockPageDefinition('TestPage'));

describe('DropdownList Control Object', () => {
    let controlObject: DropdownListControlObject;
    let fieldProperties: DropdownListProperties;
    let fieldValue: string;

    beforeEach(() => {
        fieldProperties = {
            title: 'Field Title',
            options: ['rock', 'paper', 'scissors'],
        };
        fieldValue = 'paper';
        controlObject = buildControlObject<FieldKey.DropdownList>(DropdownListControlObject, {
            fieldProperties,
            fieldValue,
        });
    });

    it("should get field's value", () => {
        expect(controlObject.value).toEqual(fieldValue);
    });

    it("should get field's title", () => {
        expect(controlObject.title).toEqual(fieldProperties.title);
    });

    it("should set field's title", () => {
        const newTitle = 'New Field Title';
        expect(controlObject.title).toEqual(fieldProperties.title);
        fieldProperties.title = newTitle;
        expect(controlObject.title).toEqual(newTitle);
    });

    it("should get field's options", () => {
        expect(controlObject.properties.options).toEqual(fieldProperties.options);
    });

    it("should set field's options", () => {
        const newOptions = ['rock', 'paper', 'scissors', 'lizard', 'spock'];
        expect(controlObject.properties.options).toEqual(fieldProperties.options);
        fieldProperties.options = newOptions;
        expect(controlObject.properties.options).toEqual(newOptions);
    });

    it('should resolve callback style option list', () => {
        fieldProperties = {
            title: 'Field Title',
            options() {
                return ['Option12', 'Option32'];
            },
        };
        fieldValue = 'paper';
        controlObject = buildControlObject<FieldKey.DropdownList>(DropdownListControlObject, {
            fieldProperties,
            fieldValue,
        });

        expect(controlObject.options).toEqual(['Option12', 'Option32']);
    });

    describe('options as callback', () => {
        beforeEach(() => {
            fieldProperties = {
                title: 'TEST_FIELD_TITLE',
                isHidden: true,
                isDisabled: true,
                options() {
                    return ['Option12', 'Option32'];
                },
            };
            fieldValue = 'Option1';
            controlObject = buildControlObject<FieldKey.DropdownList>(DropdownListControlObject, {
                fieldValue,
                fieldProperties,
            });
        });

        it('should resolve callback style option list', () => {
            expect(controlObject.options).toEqual(['Option12', 'Option32']);
        });

        it('should set field value to a valid option', () => {
            expect(() => {
                controlObject.value = 'Option12';
            }).not.toThrow();
        });

        it('should not set field value to an invalid option', () => {
            expect(() => {
                controlObject.value = 'Option3';
            }).toThrow('Option3 is not a valid option of the fieldName field. Valid options: Option12, Option32');
        });
    });
});
