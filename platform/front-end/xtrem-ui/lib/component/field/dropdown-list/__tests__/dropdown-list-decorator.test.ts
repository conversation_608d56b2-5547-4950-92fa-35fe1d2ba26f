import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { Page } from '../../../../service/page';
import * as PageMetadata from '../../../../service/page-metadata';
import { dropdownListField } from '../../../decorators';
import type { DropdownListDecoratorProperties } from '../../../decorator-properties';

describe('DropdownList Decorator', () => {
    let fieldId: string;
    let pageMetadata: PageMetadata.PageMetadata;

    beforeEach(() => {
        fieldId = 'dropdownField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(PageMetadata, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('Value Mapping', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should map options property', () => {
            const options = ['rock', 'paper', 'scissors'];
            dropdownListField({ options })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const componentProperties = pageMetadata.uiComponentProperties[fieldId] as DropdownListDecoratorProperties;
            expect(componentProperties.options).toEqual(options);
        });

        it('should map optionType property', () => {
            const optionType = '@sage/xtrem-test/RockPaperScissorsEnum';
            dropdownListField({ optionType })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const componentProperties = pageMetadata.uiComponentProperties[fieldId] as DropdownListDecoratorProperties;
            expect(componentProperties.optionType).toEqual(optionType);
        });

        it('should assign onClick handler', () => {
            testOnClickHandler(dropdownListField, pageMetadata, fieldId);
        });
    });
});
