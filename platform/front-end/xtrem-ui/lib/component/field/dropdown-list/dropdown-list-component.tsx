import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { addOptionsAndLocalizationToProps, splitValueToMergedValue } from '../../../utils/transformers';
import type { SelectItem } from '../../ui/select/select-component';
import { Select } from '../../ui/select/select-component';
import { getCommonCarbonComponentProperties, getLabelTitle } from '../carbon-helpers';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { BaseEditableComponentProperties, FieldComponentExternalProperties } from '../field-base-component-types';
import { createSelectItemFromOption, getItemsFromProps, SELECT_EMPTY_VALUE } from '../select/select-utils';
import type {
    DropdownListComponentProps,
    DropdownListDecoratorProperties,
    DropdownListProperties,
} from './dropdown-list-types';

export const DropdownListComponent: React.FC<DropdownListComponentProps> = React.memo(
    (props: DropdownListComponentProps) => {
        const componentRef = useRef<HTMLDivElement>();
        const [selectedItem, setSelectedItem] = useState<SelectItem | null>(null);

        const displayReadOnlyValue = React.useMemo(() => {
            const selectItem = createSelectItemFromOption(props.value, props);
            return selectItem.displayedAs || selectItem.value;
        }, [props]);

        useEffect(() => {
            const value =
                props.fieldProperties.hasEmptyValue && props.value === null ? SELECT_EMPTY_VALUE : props.value;

            setSelectedItem(createSelectItemFromOption(value, props));
        }, [props, props.fieldProperties.hasEmptyValue, props.localizedOptions, props.value]);

        const getItems = React.useCallback((): Promise<SelectItem[]> => {
            if (props.fieldProperties.optionType) {
                const items =
                    getItemsFromProps(props, '', splitValueToMergedValue((props as any).handlersArguments?.rowValue)) ||
                    [];

                return Promise.resolve(items);
            }

            if (props.fieldProperties.options) {
                const options: string[] = resolveByValue({
                    propertyValue: props.fieldProperties.options,
                    screenId: props.screenId,
                    skipHexFormat: true,
                    fieldValue: props.value,
                    rowValue: undefined,
                });

                const items = options.map(option => {
                    return createSelectItemFromOption(option, props);
                });

                if (props.fieldProperties.hasEmptyValue) {
                    items.unshift({
                        id: SELECT_EMPTY_VALUE,
                        value: SELECT_EMPTY_VALUE,
                        displayedAs: ' ',
                    });
                }

                return Promise.resolve(items);
            }

            return Promise.resolve([]);
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [
            props.screenId,
            props.fieldProperties.optionType,
            props.fieldProperties.options,
            props.fieldProperties.hasEmptyValue,
            props.fieldProperties.map,
        ]);

        const isDisabled = React.useCallback((): boolean => {
            return typeof props.fieldProperties.isDisabled === 'function'
                ? resolveByValue({
                      propertyValue: props.fieldProperties.isDisabled,
                      screenId: props.screenId,
                      skipHexFormat: true,
                      fieldValue: props.value,
                      rowValue: undefined,
                  })
                : !!props.fieldProperties.isDisabled;
        }, [props.fieldProperties.isDisabled, props.screenId, props.value]);

        const isReadOnly = React.useCallback((): boolean => {
            return typeof props.fieldProperties.isReadOnly === 'function'
                ? resolveByValue({
                      propertyValue: props.fieldProperties.isReadOnly,
                      screenId: props.screenId,
                      skipHexFormat: true,
                      fieldValue: props.value,
                      rowValue: undefined,
                  })
                : !!props.fieldProperties.isReadOnly;
        }, [props.fieldProperties.isReadOnly, props.screenId, props.value]);

        const onChangeCallback = React.useCallback((): void => {
            triggerFieldEvent(props.screenId, props.elementId, 'onChange');
        }, [props.screenId, props.elementId]);

        const onChange = React.useCallback(
            (item: SelectItem | undefined, isOrganicChange: boolean): void => {
                if (isOrganicChange) {
                    const value = item?.value === SELECT_EMPTY_VALUE ? null : item?.id;
                    handleChange(props.elementId, value, props.setFieldValue, props.validate, onChangeCallback);
                }
            },
            [props.elementId, props.setFieldValue, props.validate, onChangeCallback],
        );

        const carbonProps = getCommonCarbonComponentProperties(props);
        const label = !props.fieldProperties.isTitleHidden
            ? getLabelTitle(props.screenId, props.fieldProperties, (props as any).handlersArguments?.rowValue)
            : undefined;

        return (
            <CarbonWrapper
                {...props}
                className="e-dropdown-list-field"
                componentName="dropdown-list"
                componentRef={componentRef}
                helperText={props.fieldProperties.helperText}
                noReadOnlySupport={true}
                value={props.value}
                readOnlyDisplayValue={displayReadOnlyValue}
            >
                <Select
                    {...carbonProps}
                    autoSelect={false}
                    disabled={isDisabled()}
                    elementId={props.elementId}
                    fullWidth={props.fieldProperties.isFullWidth}
                    getItems={getItems}
                    hasInputSearch={false}
                    helperText={props.fieldProperties.helperText}
                    initialInputValue=""
                    inputId={carbonProps.id}
                    isSortedAlphabetically={props.fieldProperties.isSortedAlphabetically}
                    label={label}
                    minLookupCharacters={0}
                    noHelperTextInItem={true}
                    onChange={onChange}
                    onInputFocus={props.onFocus}
                    placeholder={props.fieldProperties.placeholder}
                    readOnly={isReadOnly()}
                    screenId={props.screenId}
                    selectedItem={selectedItem}
                    icon={props.fieldProperties.icon}
                    size={props.fieldProperties.size}
                    testId="e-dropdown-list-field-input"
                />
            </CarbonWrapper>
        );
    },
);

DropdownListComponent.displayName = 'DropdownList';

const extendedMapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: FieldComponentExternalProperties,
): Partial<DropdownListComponentProps> =>
    addOptionsAndLocalizationToProps(
        state,
        mapStateToProps()(state, props) as BaseEditableComponentProperties<DropdownListProperties, string>,
    );

const extendedMapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: FieldComponentExternalProperties,
): Partial<DropdownListComponentProps> => {
    const defaultMapDispatchToProps = mapDispatchToProps<DropdownListDecoratorProperties>()(dispatch, props);
    return {
        ...defaultMapDispatchToProps,
        setFieldProperties: (elementId: string, value: any): void => {
            dispatch(xtremRedux.actions.setFieldProperties(props.screenId, elementId, value));
        },
    };
};

export const ConnectedDropdownListComponent = connect(
    extendedMapStateToProps,
    extendedMapDispatchToProps,
)(DropdownListComponent);

export default ConnectedDropdownListComponent;
