import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import { addDisabledToProperties, addOptionTypeToProperties } from '../../../utils/data-type-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { DropdownListControlObject } from './dropdown-list-control-object';
import type { DropdownListDecoratorProperties, DropdownListExtensionDecoratorProperties } from './dropdown-list-types';

class DropdownListDecorator extends AbstractFieldDecorator<FieldKey.DropdownList> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = DropdownListControlObject;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<DropdownListDecoratorProperties> {
        const properties: Partial<DropdownListDecoratorProperties> = {};
        addOptionTypeToProperties({ propertyDetails, dataType, properties });
        addDisabledToProperties({
            propertyDetails,
            dataType,
            properties,
        });
        return properties;
    }
}

/**
 * Initializes the decorated member as a [DropdownList]{@link DropdownListControlObject} field with the provided properties.
 *
 * @param properties The properties that the [DropdownList]{@link DropdownListControlObject} field will be initialized with.
 */
export function dropdownListField<T extends ScreenExtension<T>>(
    properties: DropdownListDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.DropdownList>(
        properties,
        DropdownListDecorator,
        FieldKey.DropdownList,
    );
}

export function dropdownListFieldOverride<T extends ScreenExtension<T>>(
    properties: DropdownListExtensionDecoratorProperties<T>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.DropdownList>(properties);
}
