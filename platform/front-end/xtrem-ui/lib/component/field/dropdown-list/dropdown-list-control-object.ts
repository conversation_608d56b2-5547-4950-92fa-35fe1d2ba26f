import type { IconType } from '@sage/xtrem-shared';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import { getValidOptionValuesForSelect } from '../select/select-utils';
import type { DropdownListProperties } from './dropdown-list-types';

/**
 * [Field]{@link EditableFieldControlObject} that holds a value from a set of given values.
 */
export class DropdownListControlObject<
    ReferencedEnumType extends string = string,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.DropdownList, FieldComponentProps<FieldKey.DropdownList>> {
    @ControlObjectProperty<DropdownListProperties<CT>, DropdownListControlObject<ReferencedEnumType, CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<DropdownListProperties<CT>, DropdownListControlObject<ReferencedEnumType, CT>>()
    /** Indicator, whether sounds play on successful/erroneous selection */
    isSoundDisabled?: boolean;

    @ControlObjectProperty<DropdownListProperties<CT>, DropdownListControlObject<ReferencedEnumType, CT>>()
    /** Placeholder to be displayed in the field body */
    placeholder?: string;

    @FieldControlObjectResolvedProperty<DropdownListProperties<CT>, DropdownListControlObject<ReferencedEnumType, CT>>()
    /** Options to be displayed in the select element */
    options?: string[];

    @FieldControlObjectResolvedProperty<DropdownListProperties<CT>, DropdownListControlObject<ReferencedEnumType, CT>>()
    /** -   **icon**: Icon to be displayed on the right side of the input. The list of icons are defined by [DLS](https://brand.sage.com/d/NdbrveWvNheA/foundations#/icons/icons). */
    icon?: IconType;

    /**
     * The GraphQL node that the select options will be fetched from.
     * When using this property, the node must be an Enum
     */
    get optionType(): string | undefined {
        return this.getUiComponentProperty('optionType');
    }

    focus(): void {
        this._focus();
    }

    /** Field's value, only valid options can be set as value. */
    set value(newValue: ReferencedEnumType | null) {
        const validOptions = getValidOptionValuesForSelect(this.options || [], this.optionType);
        if (newValue === null || validOptions.indexOf(newValue) !== -1) {
            this._setValue(newValue as string);
        } else {
            throw new Error(
                `${newValue} is not a valid option of the ${this.elementId} field. Valid options: ${validOptions.join(
                    ', ',
                )}`,
            );
        }
    }

    /** Field's value, only valid options can be set as value. */
    get value(): ReferencedEnumType | null {
        return (this._getValue() as ReferencedEnumType) || null;
    }
}
