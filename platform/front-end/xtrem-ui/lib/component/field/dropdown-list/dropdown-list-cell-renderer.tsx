import React from 'react';
import type { DropdownListCellEditorProperties } from './dropdown-list-types';

export const DropdownListCellRenderer: React.FC<DropdownListCellEditorProperties> = React.memo(props => {
    const {
        api,
        fieldProperties,
        localizedOptions,
        node: { rowIndex },
        tableElementId,
    } = props;

    const getTestId = (): string => {
        const columnIndex = (api.getColumns() ?? []).indexOf(props.column!) + 1;
        return `${tableElementId}-${rowIndex}-${columnIndex}`;
    };

    if (props.data && props.colDef.field) {
        return (
            <fieldProperties.wrapper {...props}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span data-testid={getTestId()}>{localizedOptions?.[props.value] ?? props.value}</span>
                </div>
            </fieldProperties.wrapper>
        );
    }

    return null;
});

DropdownListCellRenderer.displayName = 'DropdownListCellRenderer';
