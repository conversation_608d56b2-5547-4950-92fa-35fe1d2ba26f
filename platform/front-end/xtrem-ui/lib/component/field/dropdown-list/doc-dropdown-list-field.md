PATH: XTREEM/UI+Field+Widgets/Dropdown+List+Field

## Introduction
The Dropdown Field is used to represent a list of options to select a value from within the user interface. The field is also available as a nested and extended field.

## Decorator Interface
```typescript
{
    bind?: GraphQLNodeProperty;
    helperText?: string;
    isSortedAlphabetically?: boolean;
    isDisabled?: boolean;
    isFullWidth?: boolean;
    isHelperTextHidden?: boolean;
    isHidden?: boolean;
    isHiddenMobile?: boolean;
    isHiddenDesktop?: boolean;
    isMandatory?: boolean;
    isReadOnly?: boolean;
    isTitleHidden?: boolean;
    isTransient?: boolean;
    options: string[];
    optionTypes: GraqhQLEnumType;
    placeholder?: string;
    size?: FieldWidth;
    title?: string;
    width?: FieldWidth;
    insertBefore?: () => FieldComponent;
    map?: (value: string) => string;
    onChange?: () => void;
    onClick?: () => void;
    onError?: ErrorHandlerFunction;
    parent: () => ContainerComponent;
}
```

## Example
#### Usage with options:
```ts
@ui.decorators.dropdownListField<PageContext>({
    options: ['Rock', 'Paper', 'Scissors', 'Lizard', 'Spock'],
    placeholder: 'Choose an option...',
    title: 'Dropdown Field',
    onChange () {
        console.log(`Do something when the field's value has changed.`);
    },
    parent () {
        return this.block;
    },
})
field: ui.fields.DropdownList;
```

#### Usage with option type:
```ts
@ui.decorators.dropdownListField<PageContext>({
    optionType: '@sage/xtrem-docs/Janken',
    placeholder: 'Choose an option...',
    title: 'Dropdown Field',
    map (value: string) {
        switch (value) {
            case 'rock':
                return 'Rock';
            case 'paper':
                return 'Paper';
            case 'scissors':
                return 'Scissors';
            case 'lizard':
                return 'Lizard';
            case 'spock':
                return 'Spock';
        }
    },
    parent () {
        return this.block;
    },
})
field: ui.fields.DropdownList<Janken>;
```

#### Usage as an extension field:
```ts
@ui.decorators.dropdownListField<PageExtension>({
    ...,
    title: 'Extended Dropdown List Field',
    insertBefore () {
        return this.field;
    },
    parent () {
        return this.block;
    },
})
extension: ui.fields.DropdownList;
```

## Decorator Properties
#### Display Properties
-   **helperText**: Specifies the helper text displayed above the field. It is automatically picked up by the i18n engine and externalized.
-   **insertBefore**: Inserts the current field before the returned field, only for extension pages.
-   **isDisabled**: Specifies whether the field is editable or not. The difference between isDisabled and isReadOnly, is that isReadOnly suggests that the field is never editable, whereas isDisabled suggest that the field is currently not editable. It can also be defined as callback function that returns a boolean.
-   **isFullWidth**: Specifies whether the field should take the full width.
-   **isHelperTextHidden**: Specifies whether the field's helper text should be displayed or not.
-   **isHidden**: Specifies whether the component should be displayed or not.
-   **isHiddenDesktop**: Specifies whether the component should be displayed for desktop-size viewports.
-   **isHiddenMobile**: Specifies whether the component should be displayed for mobile-size viewports.
-   **hasEmptyValue**: Specifies whether the component should include a blank option.
-   **isReadOnly**: Specifies whether the field is editable or not. The difference between isDisabled and isReadOnly, is that isReadOnly suggests that the field is never editable, whereas isDisabled suggest that the field is currently not editable. It can also be defined as callback function that returns a boolean.
-   **isTitleHidden**: Specifies whether the field's title should be displayed or not.
-   **isSortedAlphabetically**: Forces the options to be rendered in alphabetical order.
-   **map(value: string)**: Function that iterates over all values provided in options/optionType and allows remapping/formatting of the options.
-   **placeholder**: Specifies the field's input placeholder. It is automatically picked up by the i18n engine and externalized.
-   **options**: List of options to be displayed in the dropdown's menu.
-   **optionType**: GraphQL enum field used to get the list of options.
-   **size**: Specifies the field's vertical size. Options are `small`, `medium` and `large`. By default, the size is set to `medium`.
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **width**: Specifies the field's width. The width can be defined by using field size categories which are remapped to actually width values by the framework depending on the screen size and the container size that the field is in.
-   **info**: Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function that returns a string.
-   **warning**: Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback function that returns a string.
-   **icon**: Icon to be displayed on the right side of the input. The list of icons are defined by [DLS](https://brand.sage.com/d/NdbrveWvNheA/foundations#/icons/icons).
-   **iconColor**: Color of the input icon, only supported if the field is rendered within a tile container.

#### Binding Properties

-   **bind**: Specifies the GraphQL object's property the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.

#### Event Handler Properties

-   **onChange()**: Triggered when the field's value changed and the focus is about to move away from the field. No arguments are provided.
-   **onClick()**: Triggered when the field is clicked. Nor arguments are provided.
-   **onError(error: any, screenId: string, elementId: string)**: Triggered whenever an unexpected event occurs during the execution of an event handler in the frontend framework. The function has access to the this scope of the page or sticker and can be synchronous or asynchronous. The function can be either void or return a string value. If it returns a non-empty string, the framework displays an error toast using the returned value as the toast message. When no or falsy value is returned, the framework silently swallows the error.

#### Validation Properties

-   **isMandatory**: Specifies whether the field is mandatory or not. When enabled, empty values will raise a validation error message.  It can also be defined as callback function that returns a boolean.
-   **validation(value: string)**: Custom validation callback with the new value provided as argument. If the function return a non-empty string (or promise resolving to a non-empty string), the return value will be used as the validation error message. If the function returns a falsy value, the field is considered to be valid.

### Other Decorator Properties

-   **fetchesDefaults**: When set to true and when the dropdown value changes, a request to the server for default values for the whole page will be requested. False by default.

#### Runtime Functions

-   **focus()**: Moves the browser's focus to the field.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **refresh()**: Refetches the field's value from the server and updates the user interface.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

## Sandbox
Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/DropdownList).
