import React from 'react';
import type { NestedFilterSelectProperties } from '../../nested-fields-properties';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';

export const FilterSelectRenderer: React.FC<CellParams<NestedFilterSelectProperties>> = React.memo(props => {
    if (props.data && props.colDef.field) {
        return <props.fieldProperties.wrapper {...props}>{props.value}</props.fieldProperties.wrapper>;
    }
    return null;
});

FilterSelectRenderer.displayName = 'FilterSelectRenderer';
