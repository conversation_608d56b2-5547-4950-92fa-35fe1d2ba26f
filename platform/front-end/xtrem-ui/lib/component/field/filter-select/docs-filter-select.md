## Documentation - FilterSelect
| name                      | optional | description                                                                                                                                                                                                                                    | type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | default                                                                                                                                              |
| ------------------------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------- |
| columns                   | false    | The definitions of the nested fields used to represent the table rows                                                                                                                                                                          | <pre lang="javascript">NestedField<CT,&nbsp;AvailableNestedFieldTypes,&nbsp;NodeType,&nbsp;any>[]</pre>                                                                                                                                                                                                                                                                                                                                                                                                             |                                                                                                                                                      |
| node                      | false    | The GraphQL node that the field suggestions will be fetched from                                                                                                                                                                               | <pre lang="javascript">NonNullable<keyof&nbsp;ScreenBaseGenericType<CT>></pre>                                                                                                                                                                                                                                                                                                                                                                                                                                      |                                                                                                                                                      |
| valueField                | false    | The GraphQL node property that will be used as the reference field value                                                                                                                                                                       | <pre lang="javascript">NonNullable<keyof&nbsp;ReferencedItemType&nbsp;&&nbsp;string>&nbsp;|&nbsp;NonNullable<{&nbsp;[P&nbsp;in&nbsp;keyof&nbsp;SingleKeyed<{&nbsp;[K&nbsp;in&nbsp;keyof&nbsp;ReferencedItemType]:&nbsp;ReferencedItemType[K]&nbsp;extends&nbsp;object&nbsp;?&nbsp;ReferencedItemType[K]&nbsp;extends&nbsp;ClientCollectionMatcher<...>&nbsp;?&nbsp;never&nbsp;:&nbsp;ReferencedItemType[K]&nbsp;extends&nbsp;EdgesMatcher<...>&nbsp;?&nbsp;SingleKeyed<...>[]&nbsp;:&nbsp;SingleKeyed<...>...</pre> |                                                                                                                                                      |
| access                    | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">AccessConfiguration</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | <pre lang="javascript">{&nbsp;node:&nbsp;'<node>',&nbsp;bind:&nbsp;'<bind>'&nbsp;}</pre>                                                             |
| additionalLookupRecords   | true     | These items added to the lookup dialog and dropdown in addition to the server provided records                                                                                                                                                 | <pre lang="javascript">PartialCollectionValueWithIds<NodeType>[]&nbsp;|&nbsp;((this:&nbsp;CT)&nbsp;=>&nbsp;PartialCollectionValueWithIds<NodeType>[])</pre>                                                                                                                                                                                                                                                                                                                                                         | <pre lang="javascript">[]</pre>                                                                                                                      |
| bind                      | true     | The GraphQL node that the field’s value is bound to. If not provided, the field name is used instead */ // eslint-disable-next-line @typescript-eslint/no-unused-vars                                                                          | <pre lang="javascript">ReferenceValueType<any></pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                | <pre lang="javascript">undefined</pre>                                                                                                               |
| canFilter                 | true     | Whether the rows of the filter select lookup can be filtered or not. Defaults to true                                                                                                                                                          | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | <pre lang="javascript">true</pre>                                                                                                                    |
| fetchesDefaults           | true     | Should fetch default values for other fields when the field's value is changed                                                                                                                                                                 | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | <pre lang="javascript">false</pre>                                                                                                                   |
| filter                    | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">((this:&nbsp;CT,&nbsp;value:&nbsp;any,&nbsp;rowValue?:&nbsp;Dict<any>&nbsp;|&nbsp;undefined)&nbsp;=>&nbsp;Filter<NodeType>)&nbsp;|&nbsp;NonNullable<Filter<NodeType>></pre>                                                                                                                                                                                                                                                                                                                  | <pre lang="javascript">function(value,&nbsp;rowValue<br>)&nbsp;{<br><br>}</pre>                                                                      |
| helperText                | true     | A text that will be displayed below the field                                                                                                                                                                                                  | <pre lang="javascript">string</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | <pre lang="javascript">''</pre>                                                                                                                      |
| icon                      | true     | Icon of the input field. It will be placed on the right side                                                                                                                                                                                   | <pre lang="javascript">IconType</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                               | <pre lang="javascript">undefined</pre>                                                                                                               |
| iconColor                 | true     | Color of the icon, only supported in tile containers                                                                                                                                                                                           | <pre lang="javascript">string</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | <pre lang="javascript">''</pre>                                                                                                                      |
| infoMessage               | true     | Indicate additional warning message, rendered as tooltip and blue border                                                                                                                                                                       | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;ContextNodeType,&nbsp;Dict<any>></pre>                                                                                                                                                                                                                                                                                                                                                                                                   | <pre lang="javascript">''</pre>                                                                                                                      |
| insertBefore              | true     | The field before this extension field is inserted                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;T</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                         | <pre lang="javascript">undefined</pre>                                                                                                               |
| isDisabled                | true     | Whether the HTML element is disabled or not. Defaults to false The difference with readOnly is that disabled suggests that the field is not editable for some validation reason (e.g. a button which can't be clicked due to validation errors | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>                                                                                                                                                                                                                                                                                                                                                                                                              | <pre lang="javascript">false</pre>                                                                                                                   |
| isFilterSelectDialogOpen  | true     | Indicator, whether field's lookup dialog is currently open                                                                                                                                                                                     | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | <pre lang="javascript">false</pre>                                                                                                                   |
| isFullWidth               | true     | Whether the field spans all the parent width or not. Defaults to false                                                                                                                                                                         | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | <pre lang="javascript">false</pre>                                                                                                                   |
| isHelperTextHidden        | true     | Whether the field helper text is hidden or not. Defaults to false                                                                                                                                                                              | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | <pre lang="javascript">false</pre>                                                                                                                   |
| isHidden                  | true     | Whether the HTML element is hidden or not. Defaults to false                                                                                                                                                                                   | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>                                                                                                                                                                                                                                                                                                                                                                                                              | <pre lang="javascript">false</pre>                                                                                                                   |
| isHiddenDesktop           | true     | Whether the element is hidden or not in desktop devices. Defaults to false                                                                                                                                                                     | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | <pre lang="javascript">false</pre>                                                                                                                   |
| isHiddenMobile            | true     | Whether the element is hidden or not in mobile devices. Defaults to false                                                                                                                                                                      | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | <pre lang="javascript">false</pre>                                                                                                                   |
| isMandatory               | true     | Whether is mandatory to provide a value for the field or not. Defaults to false                                                                                                                                                                | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;ContextNodeType,&nbsp;Dict<any>></pre>                                                                                                                                                                                                                                                                                                                                                                                                  | <pre lang="javascript">false</pre>                                                                                                                   |
| isNewEnabled              | true     | Indicator, whether field can generate value for to be created node                                                                                                                                                                             | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | <pre lang="javascript">true</pre>                                                                                                                    |
| isReadOnly                | true     | Whether the field value can only be read or not. Defaults to false The difference with disabled is that readOnly suggests that the field is never editable e.g. the id field of an object                                                      | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>                                                                                                                                                                                                                                                                                                                                                                                                              | <pre lang="javascript">false</pre>                                                                                                                   |
| isTitleHidden             | true     | Whether the element title is hidden or not. Defaults to false                                                                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | <pre lang="javascript">false</pre>                                                                                                                   |
| isTransient               | true     | Whether the value is bound to a GraphQL node (transient = false) or not (transient = true). Defaults to false                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | <pre lang="javascript">false</pre>                                                                                                                   |
| isTransientInput          | true     | Whether the value is bound only to GraphQL mutations (isTransientInput = true) or not (isTransientInput = false). Defaults to false                                                                                                            | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | <pre lang="javascript">false</pre>                                                                                                                   |
| lookupDialogTitle         | true     | Lookup Dialog title                                                                                                                                                                                                                            | <pre lang="javascript">string</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | <pre lang="javascript">''</pre>                                                                                                                      |
| maxLength                 | true     | The maximum length of the filter select field value                                                                                                                                                                                            | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;number,&nbsp;NodeType,&nbsp;Dict<any>></pre>                                                                                                                                                                                                                                                                                                                                                                                                          | <pre lang="javascript">0</pre>                                                                                                                       |
| minLength                 | true     | The minimum length of the filter select field value                                                                                                                                                                                            | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;number,&nbsp;NodeType,&nbsp;Dict<any>></pre>                                                                                                                                                                                                                                                                                                                                                                                                          | <pre lang="javascript">0</pre>                                                                                                                       |
| minLookupCharacters       | true     | Minimum number of characters to trigger server lookup. If `0` supplied, the first page of suggestions will be fetched on focus. Defaults to 3                                                                                                  | <pre lang="javascript">number</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | <pre lang="javascript">3</pre>                                                                                                                       |
| onChange                  | true     | Function to be executed when the field's value changes                                                                                                                                                                                         | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                      | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                                                                                          |
| onClick                   | true     | Function to be executed when the field is clicked                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                      | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                                                                                          |
| onCloseLookupDialog | true     | Function to be executed when the filter select's dialog is closed                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT,&nbsp;_id:&nbsp;any)&nbsp;=>&nbsp;void</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                  | <pre lang="javascript">function(_id<br>)&nbsp;{<br><br>}</pre>                                                                                       |
| onError                   | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">ErrorHandlerFunction<CT></pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                               | <pre lang="javascript">function(error,screenId,elementId<br>)&nbsp;{<br>console.error({&nbsp;error,&nbsp;screenId,&nbsp;elementId&nbsp;})<br>}</pre> |
| onInputValueChange        | true     | Function to be executed when the input of the field changes                                                                                                                                                                                    | <pre lang="javascript">(this:&nbsp;CT,&nbsp;inputValue:&nbsp;string)&nbsp;=>&nbsp;void</pre>                                                                                                                                                                                                                                                                                                                                                                                                                        | <pre lang="javascript">function(inputValue<br>)&nbsp;{<br><br>}</pre>                                                                                |
| onOpenLookupDialog  | true     | Function to be executed when the filter select's dialog is opened                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT,&nbsp;_id:&nbsp;any)&nbsp;=>&nbsp;void</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                  | <pre lang="javascript">function(_id<br>)&nbsp;{<br><br>}</pre>                                                                                       |
| orderBy                   | true     | The column or the set of columns which the table should be sorted by                                                                                                                                                                           | <pre lang="javascript">NonNullable<ReferenceRecursiveOrderBy<ReferencedItemType>></pre>                                                                                                                                                                                                                                                                                                                                                                                                                             | <pre lang="javascript">undefined</pre>                                                                                                               |
| parent                    | true     | The container in which this component will render                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;P</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                         | <pre lang="javascript">undefined</pre>                                                                                                               |
| placeholder               | true     | Placeholder to be displayed in the field body                                                                                                                                                                                                  | <pre lang="javascript">string</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | <pre lang="javascript">''</pre>                                                                                                                      |
| size                      | true     | The field's vertical size, default is medium                                                                                                                                                                                                   | <pre lang="javascript">FieldWidth</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | <pre lang="javascript">'medium'</pre>                                                                                                                |
| title                     | true     | The title of the HTML element                                                                                                                                                                                                                  | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;any,&nbsp;Dict<any>></pre>                                                                                                                                                                                                                                                                                                                                                                                                               | <pre lang="javascript">''</pre>                                                                                                                      |
| validation                | true     | Additional validation rules, can be a function or a regex (e.g. /^AZ]$/i                                                                                                                                                                       | <pre lang="javascript">RegExp&nbsp;|&nbsp;((this:&nbsp;CT,&nbsp;value:&nbsp;V)&nbsp;=>&nbsp;string&nbsp;|&nbsp;undefined)&nbsp;|&nbsp;((this:&nbsp;CT,&nbsp;value:&nbsp;V)&nbsp;=>&nbsp;Promise<string&nbsp;|&nbsp;undefined>)</pre>                                                                                                                                                                                                                                                                                | <pre lang="javascript">function(value,&nbsp;value<br>)&nbsp;{<br><br>}</pre>                                                                         |
| warningMessage            | true     | Indicate additional information, rendered as tooltip and orange border                                                                                                                                                                         | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;ContextNodeType,&nbsp;Dict<any>></pre>                                                                                                                                                                                                                                                                                                                                                                                                   | <pre lang="javascript">''</pre>                                                                                                                      |
| width                     | true     | The width of the block relative to the section where it is place Must be a number between 1 and 12, both included                                                                                                                              | <pre lang="javascript">FieldWidth</pre>                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | <pre lang="javascript">'medium'</pre>                                                                                                                |