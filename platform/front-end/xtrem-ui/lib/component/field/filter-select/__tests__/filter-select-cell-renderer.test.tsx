import * as React from 'react';
import { render } from '@testing-library/react';
import { FilterSelectRenderer } from '../filter-select-cell-renderer';
import { CellWrapper } from '../../../ui/table-shared/cell/cell-wrapper';
import '@testing-library/jest-dom';

const props = {
    column: {
        colDef: {
            field: 'test-column-id',
        },
        columnApi: {
            getAllColumns: jest.fn(() => [
                {
                    field: 'test-column-id',
                },
            ]),
        } as any,
    } as any,
    screenId: 'test-screen',
    elementId: 'test-table',
    tableElementId: 'test-table',
    fieldProperties: {
        bind: '',
        valueField: 'testvalueField',
        node: '',
        wrapper: CellWrapper,
    },
    localizedNew: 'localizedNew',
    columnId: 'test-column-id',
    isParentFieldDisabled: false,
    colDef: {
        field: 'test-column-id',
        screenId: 'test-screen',
        cellRendererParams: {
            tableElementId: 'test-table',
            columnId: 'test-column-id',
            elementId: 'test-table',
            isParentFieldDisabled: false,
            fieldProperties: {
                bind: '',
                valueField: 'testvalueField',
                node: '',
                wrapper: CellWrapper,
            },
            screenId: 'screenId',
            isTree: false,
        },
    },
    rowIndex: 3,
    setValue: jest.fn(),
    formatValue: jest.fn(),
    refreshCell: jest.fn(),
    node: {} as any,
    context: {} as any,
    eGridCell: {} as any,
    eParentOfValue: {} as any,
    stopEditing: jest.fn,
    api: {} as any,
    data: {},
    value: 'aa',
    valueFormatted: 'aa (New)',
    eventKey: null,
    getValue: jest.fn(),
    registerRowDragger: () => {},
} as any;

describe('select cell renderer', () => {
    it('should render preformatted value', () => {
        const { getByText } = render(<FilterSelectRenderer {...props} />);
        const div = getByText('aa');
        expect(div).toBeInTheDocument();
    });
});
