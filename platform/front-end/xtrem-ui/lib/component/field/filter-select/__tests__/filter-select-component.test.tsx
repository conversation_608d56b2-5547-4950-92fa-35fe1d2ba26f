import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    userEvent,
} from '../../../../__tests__/test-helpers';

import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../../../../redux';
import * as graphqlService from '../../../../service/graphql-service';
import type { ScreenBase } from '../../../../service/screen-base';
import { FieldKey } from '../../../types';
import ConnectedFilterSelectComponent from '../filter-select-component';
import type { FilterSelectDecoratorProperties } from '../filter-select-types';

type SuggestionFieldProperties = graphqlService.FetchReferenceFieldSuggestionsInput<any>['fieldProperties'];
type FieldSuggestionsType = { _id: string; code: string; description?: string; value?: string };

jest.useFakeTimers();
jest.setTimeout(10000);

describe('FilterSelect Component', () => {
    const screenId = 'TestPage';
    const fieldId = 'test-filter-select-field';
    const fieldTitle = 'Test Field Title';
    const fieldValueField = 'code';
    let fetchSuggestionsMock;
    let fieldProperties: FilterSelectDecoratorProperties<ScreenBase, any>;

    const fieldSuggestions: FieldSuggestionsType[] = [
        { _id: '1', code: 'Test code 1' },
        { _id: '2', code: 'Test code 2' },
        { _id: '3', code: 'Test code 3' },
        { _id: '4', code: 'Test code 4' },
    ];

    const getConnectedField = (mockStore, testFieldId = fieldId) => (
        <Provider store={mockStore}>
            <ConnectedFilterSelectComponent screenId={screenId} elementId={testFieldId} />
        </Provider>
    );

    jest.spyOn(xtremRedux.actions, 'setFieldValue').mockReturnValue(
        () => Promise.resolve({ type: 'SetFieldValue' }) as any,
    );

    const getMobileBrowserState = (): xtremRedux.XtremAppState['browser'] => {
        return {
            greaterThan: {
                l: false,
                m: false,
                s: false,
                xs: false,
            },
            is: {
                l: false,
                m: false,
                s: false,
                xs: true,
            },
            lessThan: {
                l: true,
                m: true,
                s: true,
                xs: false,
            },
            mediaType: 'xs',
            orientation: 'portrait',
        };
    };

    beforeEach(() => {
        fieldProperties = {
            node: '@sage/xtrem-test/TestNode',
            title: fieldTitle,
            valueField: fieldValueField,
        };

        fetchSuggestionsMock = jest
            .spyOn(graphqlService, 'fetchReferenceFieldSuggestions')
            .mockImplementation((input: any) => {
                if (input?.filterValue) {
                    const searchText = String(input.filterValue).toLowerCase();
                    const suggestions = fieldSuggestions.filter(suggestion => {
                        return suggestion.code.toLowerCase().includes(searchText);
                    });
                    return Promise.resolve(suggestions);
                }
                return Promise.resolve(fieldSuggestions);
            });
    });

    describe('Connected', () => {
        let state: xtremRedux.XtremAppState;
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        beforeEach(() => {
            state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.FilterSelect, state, screenId, fieldId, fieldProperties, null);
            mockStore = getMockStore(state);
        });

        afterEach(() => {
            fetchSuggestionsMock.mockClear();
        });

        it('should render with redux with defaults', () => {
            render(getConnectedField(mockStore, fieldId));
            expect(screen.getByTestId('e-field-bind-test-filter-select-field', { exact: false })).toBeInTheDocument();
        });

        it('should render component', () => {
            render(getConnectedField(mockStore, fieldId));
            screen.getByText(fieldTitle);
            screen.getByLabelText(fieldTitle);
        });

        it('should render the title', () => {
            render(getConnectedField(mockStore, fieldId));
            screen.getByLabelText(fieldTitle);
        });

        it('should render the title with an asterisk when the field is mandatory', () => {
            fieldProperties.isMandatory = true;
            render(getConnectedField(mockStore, fieldId));
            screen.getByLabelText(`${fieldTitle} *`);
        });

        it('should NOT fetch suggestions when the user focuses a field and "minLookupCharacters" NOT is set to "0"', () => {
            render(getConnectedField(mockStore, fieldId));
            const input = screen.getByLabelText(fieldTitle);

            fireEvent.focus(input);

            expect(fetchSuggestionsMock).not.toHaveBeenCalled();
        });

        it('should fetch suggestions when the user focuses a field and "minLookupCharacters" is set to "0"', async () => {
            fieldProperties.minLookupCharacters = 0;
            render(getConnectedField(mockStore, fieldId));
            const input = screen.getByLabelText(fieldTitle);

            fireEvent.focus(input);

            await waitFor(() => {
                expect(fetchSuggestionsMock).toHaveBeenCalledWith({
                    fieldId,
                    fieldProperties,
                    filterValue: '',
                    screenId,
                });
            });
        });

        it('should fetch suggestions as the user types into the input', async () => {
            render(getConnectedField(mockStore, fieldId));
            const input = screen.getByLabelText(fieldTitle);

            fireEvent.change(input, { target: { value: 'test search' } });

            await waitFor(() => {
                expect(fetchSuggestionsMock).toHaveBeenCalledWith({
                    fieldId,
                    fieldProperties: fieldProperties as SuggestionFieldProperties,
                    filterValue: 'test search',
                    screenId,
                });
            });
        });

        it('should open list of suggestions', async () => {
            fieldProperties.isNewEnabled = true;
            render(getConnectedField(mockStore, fieldId));
            const input = screen.getByLabelText(fieldTitle);

            fireEvent.focus(input);
            await userEvent.type(input, 'Test');

            const suggestionList = screen.getByTestId('e-ui-select-dropdown');
            expect(suggestionList).toBeInTheDocument();
            let suggestions: HTMLElement[] = [];
            await waitFor(() => {
                suggestions = screen.getAllByTestId('e-ui-select-suggestion-value');
                expect(suggestions).toHaveLength(5);
            });
            for (let i = 0; i < 5; i++) {
                const suggestion = i === 0 ? { code: 'Test (New)' } : fieldSuggestions[i - 1];
                expect(suggestions[i].textContent).toBe(suggestion.code);
            }
        });

        it('should filter list based on input', async () => {
            render(getConnectedField(mockStore, fieldId));
            const input = screen.getByLabelText(fieldTitle);

            fireEvent.focus(input);
            await userEvent.type(input, fieldSuggestions[0].code);
            await waitFor(() => {
                expect(screen.getAllByTestId('e-ui-select-suggestion-value')).toHaveLength(1);
            });
        });

        it('should not add suffix if existing value is added by clicking', async () => {
            render(getConnectedField(mockStore, fieldId));

            let input = screen.getByTestId('e-filter-select-field-input') as HTMLInputElement;
            expect(input).toBeTruthy();
            expect(input.value).toBe('');
            fireEvent.focus(input);
            await userEvent.type(input, 'Test code 1');
            expect(input.value).toBe('Test code 1');

            let suggestions: HTMLElement[] = [];
            await waitFor(() => {
                suggestions = screen.getAllByTestId('e-ui-select-suggestion-value');
                expect(suggestions).toHaveLength(1);
                expect(suggestions[0].textContent).toBe('Test code 1');
            });
            await userEvent.click(suggestions[0]);
            input = await (screen.findByTestId('e-filter-select-field-input') as Promise<HTMLInputElement>);
            expect(input.value).toBe('Test code 1');
        });

        it('should not add suffix if existing value is added by using the keyboard', async () => {
            render(getConnectedField(mockStore, fieldId));

            let input = screen.getByTestId('e-filter-select-field-input') as HTMLInputElement;
            expect(input).toBeTruthy();
            expect(input.value).toBe('');
            fireEvent.focus(input);
            await userEvent.type(input, 'Test code 1');
            expect(input.value).toBe('Test code 1');

            let suggestions: HTMLElement[] = [];
            await waitFor(() => {
                suggestions = screen.getAllByTestId('e-ui-select-suggestion-value');
                expect(suggestions).toHaveLength(1);
                expect(suggestions[0].textContent).toBe('Test code 1');
            });
            fireEvent.keyDown(input, { key: 'ArrowDown' });
            fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' });
            input = await (screen.findByTestId('e-filter-select-field-input') as Promise<HTMLInputElement>);
            expect(input.value).toBe('Test code 1');
        });

        it('should add suffix if non-existing value is added by clicking', async () => {
            fieldProperties.isNewEnabled = true;
            render(getConnectedField(mockStore, fieldId));

            let input = screen.getByTestId('e-filter-select-field-input') as HTMLInputElement;
            expect(input).toBeTruthy();
            expect(input.value).toBe('');
            fireEvent.focus(input);
            await userEvent.type(input, 'Patata');
            expect(input.value).toBe('Patata');

            let suggestions: HTMLElement[] = [];
            await waitFor(() => {
                suggestions = screen.getAllByTestId('e-ui-select-suggestion-value');
                expect(suggestions).toHaveLength(1);
                expect(suggestions[0].textContent).toBe('Patata (New)');
            });
            await userEvent.click(suggestions[0]);
            const inputDiv = await (screen.findByRole('presentation') as Promise<HTMLDivElement>);
            input = await (screen.findByTestId('e-filter-select-field-input') as Promise<HTMLInputElement>);
            await waitFor(() => {
                expect(input.value).toBe('Patata');
                expect(inputDiv.textContent).toBe('');
            });
            fireEvent.blur(input);
            await waitFor(() => {
                expect(input.value).toBe('Patata');
                expect(inputDiv.textContent).toBe('(New)');
            });
        });

        it('should add suffix if non-existing value is added by keyboard', async () => {
            fieldProperties.isNewEnabled = true;
            render(getConnectedField(mockStore, fieldId));

            let input = screen.getByTestId('e-filter-select-field-input') as HTMLInputElement;
            expect(input).toBeTruthy();
            expect(input.value).toBe('');
            fireEvent.focus(input);
            await userEvent.type(input, 'Patata');
            expect(input.value).toBe('Patata');

            let suggestions: HTMLElement[] = [];
            await waitFor(() => {
                suggestions = screen.getAllByTestId('e-ui-select-suggestion-value');
                expect(suggestions).toHaveLength(1);
                expect(suggestions[0].textContent).toBe('Patata (New)');
            });
            fireEvent.keyDown(input, { key: 'ArrowDown' });
            fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' });
            input = await (screen.findByTestId('e-filter-select-field-input') as Promise<HTMLInputElement>);
            expect(input.value).toBe('Patata');
            const inputDiv = await (screen.findByRole('presentation') as Promise<HTMLDivElement>);
            expect(inputDiv.textContent).toBe('');
            fireEvent.blur(input);
            expect(inputDiv.textContent).toBe('(New)');
        });

        describe('info and warning', () => {
            it('should render with an info message', async () => {
                fieldProperties.infoMessage = 'Info message!!';
                addFieldToState(FieldKey.FilterSelect, state, screenId, fieldId, fieldProperties, null);
                mockStore = getMockStore(state);
                const { baseElement } = render(getConnectedField(mockStore));
                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an info message from a callback', async () => {
                fieldProperties.infoMessage = () => 'Info message!!';
                addFieldToState(FieldKey.FilterSelect, state, screenId, fieldId, fieldProperties, null);
                mockStore = getMockStore(state);
                const { baseElement } = render(getConnectedField(mockStore));
                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an warning message', async () => {
                fieldProperties.warningMessage = 'Warning message!!';
                addFieldToState(FieldKey.FilterSelect, state, screenId, fieldId, fieldProperties, null);
                mockStore = getMockStore(state);
                const { baseElement } = render(getConnectedField(mockStore));
                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with an warning message from a callback', async () => {
                fieldProperties.warningMessage = () => 'Warning message!!';
                addFieldToState(FieldKey.FilterSelect, state, screenId, fieldId, fieldProperties, null);
                mockStore = getMockStore(state);
                const { baseElement } = render(getConnectedField(mockStore, fieldId));
                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should prioritize warnings over info messages', () => {
                fieldProperties.warningMessage = () => 'Warning message!!';
                fieldProperties.infoMessage = () => 'Info message!!';
                addFieldToState(FieldKey.FilterSelect, state, screenId, fieldId, fieldProperties, null);
                mockStore = getMockStore(state);
                const wrapper = render(getConnectedField(mockStore, fieldId));
                expect(wrapper.baseElement.querySelector('[data-element="info"]')).toBeNull();
                expect(wrapper.baseElement.querySelector('[data-element="warning"]')).not.toBeNull();
            });

            it('should prioritize validation errors over warnings and info messages', () => {
                fieldProperties.warningMessage = () => 'Warning message!!';
                fieldProperties.infoMessage = () => 'Info message!!';
                state.screenDefinitions[screenId].errors[fieldId] = [
                    {
                        elementId: fieldId,
                        screenId,
                        validationRule: 'isMandatory',
                        message: 'Error',
                    },
                ];
                addFieldToState(FieldKey.FilterSelect, state, screenId, fieldId, fieldProperties, null);
                mockStore = getMockStore(state);
                const wrapper = render(getConnectedField(mockStore, fieldId));
                expect(wrapper.baseElement.querySelector('[data-element="info"]')).toBeNull();
                expect(wrapper.baseElement.querySelector('[data-element="warning"]')).toBeNull();
                expect(wrapper.baseElement.querySelector('[data-element="error"]')).not.toBeNull();
            });
        });
    });

    describe('Mobile Component', () => {
        let state: xtremRedux.XtremAppState;
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        beforeEach(() => {
            state = getMockState({ browser: getMobileBrowserState() });
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.FilterSelect, state, screenId, fieldId, fieldProperties, null);
            mockStore = getMockStore(state);
            jest.spyOn(xtremRedux.actions, 'openDialog');
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should render mobile component', async () => {
            render(getConnectedField(mockStore, fieldId));

            screen.getByTestId('e-filter-select-field-mobile-wrapper');
            screen.getByText(fieldTitle);
            screen.getByLabelText(fieldTitle);
            await screen.findByRole('button');
        });

        it('should open lookup table upon clicking the search button', async () => {
            render(getConnectedField(mockStore, fieldId));

            const button = await screen.findByRole('button');
            expect(xtremRedux.actions.openDialog).toHaveBeenCalledTimes(0);
            await userEvent.click(button);
            await waitFor(() => {
                expect(xtremRedux.actions.openDialog).toHaveBeenCalledTimes(1);
                expect(xtremRedux.actions.openDialog).toHaveBeenLastCalledWith(
                    expect.any(Number),
                    expect.objectContaining({
                        title: 'Test Field Title',
                    }),
                );
            });
        });

        it('should open lookup table upon typing into the input', async () => {
            render(getConnectedField(mockStore, fieldId));

            const input = screen.getByLabelText(fieldTitle);
            expect(xtremRedux.actions.openDialog).toHaveBeenCalledTimes(0);
            await userEvent.type(input, 'T');
            await waitFor(() => {
                expect(xtremRedux.actions.openDialog).toHaveBeenCalledTimes(1);
                expect(xtremRedux.actions.openDialog).toHaveBeenLastCalledWith(
                    expect.any(Number),
                    expect.objectContaining({
                        content: expect.objectContaining({
                            searchText: 'T',
                        }),
                    }),
                );
            });
        });
    });
});
