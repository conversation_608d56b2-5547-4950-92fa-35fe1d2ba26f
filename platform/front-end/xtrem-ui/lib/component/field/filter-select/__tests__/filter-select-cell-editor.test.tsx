import * as React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react';
import FilterSelectCellEditor from '../filter-select-cell-editor';
import type { FilterSelectCellEditorProps } from '../../select/select-types';
import * as graphqlService from '../../../../service/graphql-service';
import { DELETE, BACKSPACE } from '../../../../utils/keyboard-event-utils';
import { CellWrapper } from '../../../ui/table-shared/cell/cell-wrapper';
import '@testing-library/jest-dom';
import { CollectionValue } from '../../../../service/collection-data-service';
import * as nestedFields from '../../../nested-fields';
import { CollectionFieldTypes } from '../../../../service/collection-data-types';

const fillerProps = {
    column: {
        getColId: () => 'test-column-id',
        colDef: {
            field: 'test-column-id',
        },
        columnApi: {
            getAllColumns: jest.fn(() => [
                {
                    field: 'test-column-id',
                },
            ]),
        } as any,
    } as any,
    elementId: 'test-table',
    tableElementId: 'test-table',
    setValue: jest.fn(),
    formatValue: jest.fn(),
    refreshCell: jest.fn(),
    setTooltip: jest.fn(),
    node: { rowIndex: 3 } as any,
    context: {
        screenId: 'test-screen',
    } as any,
    eGridCell: {} as any,
    eParentOfValue: {} as any,
    collectionValue: () =>
        new CollectionValue({
            screenId: 'test-screen',
            elementId: 'testField',
            isTransient: false,
            hasNextPage: false,
            orderBy: [{}],
            columnDefinitions: [
                [nestedFields.text<any, any>({ bind: '_id' }), nestedFields.text<any, any>({ bind: 'anyField' })],
            ],
            nodeTypes: {},
            nodes: ['@sage/xtrem-test/AnyNode'],
            filter: [undefined],
            initialValues: [],
            fieldType: CollectionFieldTypes.DESKTOP_TABLE,
        }),
};

jest.useFakeTimers();

describe('filter select cell editor', () => {
    let props: FilterSelectCellEditorProps;
    let ref: React.RefObject<any>;
    let stopEditing: any;
    let tabToNextCell: any;
    let tabToPreviousCell: any;
    let getFocusedCell: any;
    let setFocusedCell: any;
    let findNextCellToFocusOn: any;
    let startEditingCell: any;

    beforeEach(() => {
        ref = React.createRef();
        stopEditing = jest.fn();
        tabToNextCell = jest.fn();
        tabToPreviousCell = jest.fn();
        getFocusedCell = jest.fn();
        setFocusedCell = jest.fn();
        startEditingCell = jest.fn();
        findNextCellToFocusOn = jest.fn(() => {
            return {
                column: { getColId: () => {} },
                getRowNode: () => ({ rowIndex: 0 }),
            };
        });
        props = {
            ...fillerProps,
            locale: 'en-US',
            screenId: 'test-screen',
            columnId: 'test-column-id',
            localizedNew: 'localizedNew',
            isParentFieldDisabled: false,
            fieldProperties: {
                bind: '',
                valueField: 'testvalueField',
                node: '',
                minLookupCharacters: 0,
                wrapper: CellWrapper,
            },
            isTree: false,
            colDef: {
                context: {
                    screenId: 'test-screen',
                    columnId: 'test-column-id',
                    isEditable: (): boolean => true,
                },
                field: 'test-column-id',
                cellRendererParams: {
                    columnId: 'test-column-id',
                    tableElementId: 'test-table',
                    elementId: 'elementId',
                    isParentFieldDisabled: false,
                    locale: 'en-US',
                    collectionValue: () =>
                        new CollectionValue({
                            screenId: 'test-screen',
                            elementId: 'testField',
                            isTransient: false,
                            hasNextPage: false,
                            orderBy: [{}],
                            columnDefinitions: [
                                [
                                    nestedFields.text<any, any>({ bind: '_id' }),
                                    nestedFields.text<any, any>({ bind: 'anyField' }),
                                ],
                            ],
                            nodeTypes: {},
                            nodes: ['@sage/xtrem-test/AnyNode'],
                            filter: [undefined],
                            initialValues: [],
                            fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                        }),
                    fieldProperties: {
                        bind: '',
                        valueField: 'testvalueField',
                        node: '',
                        minLookupCharacters: 0,
                        wrapper: CellWrapper,
                    },
                    screenId: 'screenId',
                    isTree: false,
                },
            },
            stopEditing,
            node: {
                isRowPinned: () => false,
                rowIndex: 3,
            } as any,
            api: {
                paginationGetPageSize: () => 20,
                tabToNextCell,
                tabToPreviousCell,
                getFocusedCell,
                navigationService: {
                    findNextCellToFocusOn,
                },
                setFocusedCell,
                startEditingCell,
                getColumns: jest.fn(() => [
                    {
                        field: 'test-column-id',
                    },
                ]),
            } as any,
            data: {},
            value: '',
            initialValue: '',
            onValueChange: jest.fn(),
            valueFormatted: null,
            eventKey: null,
            getValue: jest.fn(),
            registerRowDragger: () => {},
        };
        jest.spyOn(graphqlService, 'fetchReferenceFieldSuggestions').mockResolvedValue([
            { _id: '1', testvalueField: 'aa', __cursor: '' },
            { _id: '2', testvalueField: 'ab', __cursor: '' },
            { _id: '3', testvalueField: 'zz', __cursor: '' },
        ] as any);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should render with empty value', async () => {
        const container = render(<FilterSelectCellEditor {...props} ref={ref} />);
        const input = container.getByTestId('test-table-3-0-input') as HTMLInputElement;
        expect(input.value).toBe('');
    });

    it('should render with original value', async () => {
        props.initialValue = 'testvalue';
        const container = render(<FilterSelectCellEditor {...props} ref={ref} />);
        const input = container.getByTestId('test-table-3-0-input') as HTMLInputElement;
        expect(input.value).toBe('testvalue');
    });

    it('should start with empty instead of original value when triggered with delete key', async () => {
        props.eventKey = DELETE;
        props.value = 'testvalue';
        const container = render(<FilterSelectCellEditor {...props} ref={ref} />);
        const input = container.getByTestId('test-table-3-0-input') as HTMLInputElement;
        expect(input.value).toBe('');
    });

    it('should start with empty instead of original value when triggered with backspace key', async () => {
        props.eventKey = BACKSPACE;
        props.value = 'testvalue';
        const container = render(<FilterSelectCellEditor {...props} ref={ref} />);
        const input = container.getByTestId('test-table-3-0-input') as HTMLInputElement;
        expect(input.value).toBe('');
    });

    it('should start with the pressed char instead of original value when triggered with any char', async () => {
        props.eventKey = 'u';
        props.value = 'testvalue';
        const container = render(<FilterSelectCellEditor {...props} ref={ref} />);
        const input = container.getByTestId('test-table-3-0-input') as HTMLInputElement;
        await waitFor(() => {
            expect(input.value).toBe('u');
        });
    });

    it('should use a style that matches ag-grid style', async () => {
        const container = render(<FilterSelectCellEditor {...props} ref={ref} />);
        const input = container.getByTestId('test-table-3-0-input') as HTMLInputElement;
        expect(input.style.getPropertyValue('font-family')).toBe('Sage UI');
        expect(input.style.getPropertyValue('font-size')).toBe('14px');
    });

    it('should consolidate on Enter', async () => {
        props.fieldProperties.isNewEnabled = true;
        const container = render(<FilterSelectCellEditor {...props} ref={ref} />);
        const input = container.getByTestId('test-table-3-0-input') as HTMLInputElement;
        await waitFor(() => {
            expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1);
        });
        fireEvent.change(input, { target: { value: 'abc' } });
        await waitFor(() => {
            expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(2);
        });
        fireEvent.keyDown(input, { key: 'Enter' });
        await waitFor(() => {
            expect(input.value).toEqual('abc');
        });
        expect(props.stopEditing).toHaveBeenCalled();
        await waitFor(() => {
            expect(input.value).toEqual('abc');
        });
    });

    it('should consolidate highlighted dropdown suggestion over input value on Enter', async () => {
        props.eventKey = 'a';
        props.fieldProperties.isNewEnabled = true;
        const container = render(<FilterSelectCellEditor {...props} ref={ref} />);
        const input = container.getByTestId('test-table-3-0-input') as HTMLInputElement;
        await waitFor(() => {
            expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1);
        });
        fireEvent.keyDown(input, { key: 'ArrowDown' });
        fireEvent.keyDown(input, { key: 'ArrowDown' });
        const suggestion = container.getAllByTestId('e-ui-select-suggestion-value')[1] as HTMLDivElement;
        await waitFor(() => {
            expect(suggestion).toBeInTheDocument();
            expect(suggestion).toHaveTextContent('aa');
        });
        fireEvent.keyDown(input, { key: 'Enter' });
        await waitFor(() => {
            expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(2);
        });
        await waitFor(() => {
            expect(props.stopEditing).toHaveBeenCalled();
        });
        expect(input.value).toEqual('aa');
        expect(input.value).toBe('aa');
    });

    it('should allow creation of new item isNewEnabled is true', async () => {
        props.fieldProperties.isNewEnabled = true;
        props.eventKey = 'a';

        const container = render(<FilterSelectCellEditor {...props} ref={ref} />);
        const input = container.getByTestId('test-table-3-0-input') as HTMLInputElement;

        await waitFor(() => {
            expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1);
        });

        fireEvent.change(input, { target: { value: 'a' } });

        const suggestions = container.getAllByTestId('e-ui-select-suggestion-value');

        await waitFor(() => {
            expect(suggestions[0]).toBeInTheDocument();
            expect(suggestions[0]).toHaveTextContent('a (New)');
        });
    });

    it('should not allowed new item created if isNewEnabled is false', async () => {
        props.fieldProperties.isNewEnabled = false;
        props.eventKey = 'a';

        const container = render(<FilterSelectCellEditor {...props} ref={ref} />);
        const input = container.getByTestId('test-table-3-0-input') as HTMLInputElement;

        await waitFor(() => {
            expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1);
        });

        fireEvent.change(input, { target: { value: 'a' } });

        const suggestions = container.getAllByTestId('e-ui-select-suggestion-value');

        await waitFor(() => {
            expect(suggestions[0]).not.toHaveTextContent('a (New)');
        });
    });
});
