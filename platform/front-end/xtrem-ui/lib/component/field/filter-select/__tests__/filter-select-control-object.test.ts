import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import { FilterSelectControlObject } from '../../../control-objects';
import type { FieldKey } from '../../../types';
import type { FilterSelectProperties } from '../filter-select-types';

describe('Filter Select control object', () => {
    let selectControlObject: FilterSelectControlObject;
    let filterSelectProperties: FilterSelectProperties<any>;
    let filterSelectValue: string;

    beforeEach(() => {
        filterSelectProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
            node: '@sage/xtrem-test/TestNode',
            valueField: 'title',
        };
        filterSelectValue = 'Any Value';
        selectControlObject = buildControlObject<FieldKey.FilterSelect>(FilterSelectControlObject, {
            fieldValue: filterSelectValue,
            fieldProperties: filterSelectProperties,
        });
    });

    it('getting field value', () => {
        expect(selectControlObject.value).toEqual(filterSelectValue);
    });

    it('should set the title', () => {
        const testFixture = 'Test Numeric Field Title';
        expect(filterSelectProperties.title).not.toEqual(testFixture);
        selectControlObject.title = testFixture;
        expect(filterSelectProperties.title).toEqual(testFixture);
    });

    it('should set the icon value', () => {
        const testFixture = 'add';
        expect(filterSelectProperties.icon).not.toEqual(testFixture);
        selectControlObject.icon = testFixture;
        expect(filterSelectProperties.icon).toEqual(testFixture);
    });
});
