import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import { filterSelectField } from '../filter-select-decorator';

describe('Filter Select Decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'selectField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(filterSelectField, pageMetadata, fieldId);
        });
    });
});
