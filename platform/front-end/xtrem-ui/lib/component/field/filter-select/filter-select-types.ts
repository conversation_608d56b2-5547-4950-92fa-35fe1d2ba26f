import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenBaseGenericType, ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedPropertiesWrapper } from '../../nested-fields';
import type { FieldControlObjectInstance, ReferenceRecursiveOrderBy } from '../../types';
import type { BaseEditableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    <PERSON>Field,
    HasColumns,
    HasDynamicLookupSuggestions,
    HasFilter,
    HasIcon,
    HasInputValueChangeListener,
    HasLookupDialogTitle,
    HasMandatoryValueField,
    HasMaxMinL<PERSON>th,
    HasParent,
    HasPlaceholder,
    HasSound,
    Nested,
    NestedChangeable,
    NestedHasLookupDialogTitle,
    NestedValidatable,
    Sizable,
    Validatable,
} from '../traits';
import type { FilterSelectDecoratorPropertiesKeys } from './filter-select-decorator';

export interface FilterSelectProperties<
    CT extends ScreenExtension<CT> = ScreenBase,
    ReferencedItemType extends ClientNode = any,
    NodeType = any,
> extends EditableFieldProperties<CT, NodeType>,
        Partial<HasColumns<CT, ReferencedItemType>>,
        HasFilter<CT, ReferencedItemType>,
        HasIcon,
        HasPlaceholder,
        HasMaxMinLength<CT>,
        HasSound<CT>,
        HasMandatoryValueField<ReferencedItemType>,
        Sizable {
    /** Whether the rows of the filter select lookup can be filtered or not. Defaults to true */
    canFilter?: boolean;
    /** Indicator, whether field's lookup dialog is currently open */
    isFilterSelectDialogOpen?: boolean;
    /** Indicator, whether field can generate value for to be created node */
    isNewEnabled?: boolean;
    /** Minimum number of characters to trigger server lookup. If `0` supplied, the first page of suggestions will be fetched on focus. Defaults to 3 */
    minLookupCharacters?: number;
    /** The GraphQL node that the field suggestions will be fetched from */
    node: keyof ScreenBaseGenericType<CT>;
    /** The column or the set of columns which the table should be sorted by */
    orderBy?: ReferenceRecursiveOrderBy<ReferencedItemType>;
}
export interface FilterSelectDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    ReferencedItemType extends ClientNode = any,
> extends Omit<FilterSelectProperties<CT, ReferencedItemType>, '_controlObjectType'>,
        Changeable<CT>,
        Clickable<CT>,
        HasInputValueChangeListener<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>>,
        HasDynamicLookupSuggestions<CT, ReferencedItemType>,
        HasLookupDialogTitle<CT>,
        Sizable,
        Validatable<CT, string>,
        HasSound<CT> {
    /** Function to be executed when the filter select's dialog is closed. */
    [FilterSelectDecoratorPropertiesKeys.onCloseLookupDialog]?: (this: CT, _id: any) => void;
    /** Function to be executed when the filter select's dialog is opened. */
    [FilterSelectDecoratorPropertiesKeys.onOpenLookupDialog]?: (this: CT, _id: any) => void;
}

export interface NestedFilterSelectProperties<
    CT extends ScreenBase = ScreenBase,
    ContextNodeType extends ClientNode = any,
    ReferencedNode extends ClientNode = any,
> extends NestedPropertiesWrapper<FilterSelectProperties<CT, ReferencedNode, ContextNodeType>>,
        NestedChangeable<CT>,
        Nested<ContextNodeType>,
        Sizable,
        NestedHasLookupDialogTitle<CT, ContextNodeType>,
        NestedValidatable<CT, string, ContextNodeType> {}

type BaseFilterSelectExtensionDecoratorProperties<CT extends ScreenBase = ScreenBase> = {
    onCloseLookupDialogAfter?: (this: CT, _id: any) => void;
    onOpenLookupDialogAfter?: (this: CT, _id: any) => void;
};

export type FilterSelectExtensionDecoratorProperties<
    CT extends ScreenExtension<CT>,
    ReferencedItemType extends ClientNode = any,
> = BaseFilterSelectExtensionDecoratorProperties<Extend<CT>> &
    ChangeableOverrideDecoratorProperties<FilterSelectDecoratorProperties<Extend<CT>, ReferencedItemType>, CT>;

export type FilterSelectComponentProps<T extends ClientNode = any> = BaseEditableComponentProperties<
    FilterSelectProperties<any, T>,
    string,
    NestedFieldsAdditionalProperties
>;

export interface FilterSelectState {
    id: string;
}
