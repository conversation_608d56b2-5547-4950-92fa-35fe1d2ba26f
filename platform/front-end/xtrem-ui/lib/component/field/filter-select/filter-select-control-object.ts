/**
 * @packageDocumentation
 * @module root
 */
import type { ClientNode } from '@sage/xtrem-client';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import { fetchReferenceFieldSuggestions } from '../../../service/graphql-service';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import { showToast } from '../../../service/toast-service';
import type { ScreenBase } from '../../../service/screen-base';
import type { FieldComponentProps, FieldKey } from '../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FilterSelectDecoratorProperties, FilterSelectProperties } from './filter-select-types';

/**
 * [Field]{@link EditableFieldControlObject} that holds a value from a set of given values or sets a new value to be
 * saved later. The type of GraphQL object must be specified through the 'node' property, while the 'valueField' and
 * 'helperTextField' properties define which properties of the GraphQL object will be displayed in the field and will
 * be matched against the user provided text.
 */
export class FilterSelectControlObject<
    ReferencedItemType extends ClientNode = any,
    CT extends ScreenBase = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.FilterSelect, FieldComponentProps<FieldKey.FilterSelect>> {
    static readonly defaultUiProperties: Partial<FilterSelectProperties> = {
        ...EditableFieldControlObject.defaultUiProperties,
        canFilter: true,
        isNewEnabled: true,
        minLookupCharacters: 3,
    };

    /** Graphql filter that will restrict the results of the reference field */
    get filter(): GraphQLFilter<ReferencedItemType> | ((this: CT) => GraphQLFilter<ReferencedItemType>) | undefined {
        return this.getUiComponentProperty('filter');
    }

    /** Graphql filter that will restrict the results of the reference field */
    set filter(
        filter: GraphQLFilter<ReferencedItemType> | ((this: CT) => GraphQLFilter<ReferencedItemType>) | undefined,
    ) {
        this.setUiComponentProperties('filter', filter);
        this.refresh().catch(() => {
            /* Intentional fire and forget */
        });
    }

    @ControlObjectProperty<FilterSelectDecoratorProperties<CT>, FilterSelectControlObject<ReferencedItemType, CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<FilterSelectDecoratorProperties<CT>, FilterSelectControlObject<ReferencedItemType, CT>>()
    /** Icon of the input field. It will be placed on the right side. */
    icon?: IconType;

    @ControlObjectProperty<FilterSelectDecoratorProperties<CT>, FilterSelectControlObject<ReferencedItemType, CT>>()
    /** Color of the icon, only supported in tile containers */
    iconColor?: string;

    @ControlObjectProperty<FilterSelectDecoratorProperties<CT>, FilterSelectControlObject<ReferencedItemType, CT>>()
    /** Indicator, whether field can generate value for to be created node */
    isNewEnabled?: boolean;

    @ControlObjectProperty<FilterSelectDecoratorProperties<CT>, FilterSelectControlObject<ReferencedItemType, CT>>()
    /** Indicator, whether sounds play on successful/erroneous selection */
    isSoundDisabled?: boolean;

    @ControlObjectProperty<FilterSelectDecoratorProperties<CT>, FilterSelectControlObject<ReferencedItemType, CT>>()
    /** The maximum length of the filter select field value */
    maxLength?: number;

    @ControlObjectProperty<FilterSelectDecoratorProperties<CT>, FilterSelectControlObject<ReferencedItemType, CT>>()
    /** The minimum length of the filter select field value */
    minLength?: number;

    @ControlObjectProperty<FilterSelectDecoratorProperties<CT>, FilterSelectControlObject<ReferencedItemType, CT>>()
    /** Placeholder to be displayed in the field body */
    placeholder?: string;

    @ControlObjectProperty<FilterSelectDecoratorProperties<CT>, FilterSelectControlObject<ReferencedItemType, CT>>()
    /** Lookup Dialog title **/
    lookupDialogTitle?: string;

    /** Refetches data from the server */
    async refresh(): Promise<void> {
        await this._refresh({ keepPageInfo: true }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }

    async fetchSuggestions(searchText: string): Promise<Partial<ReferencedItemType>[]> {
        const result = await fetchReferenceFieldSuggestions({
            fieldProperties: this.properties,
            screenId: this.screenId,
            fieldId: this.elementId,
            filterValue: searchText,
        });

        return result || [];
    }
}
