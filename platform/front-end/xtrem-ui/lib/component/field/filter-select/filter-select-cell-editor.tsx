import type { UseComboboxStateChangeTypes } from 'downshift';
import { omitBy } from 'lodash';
import React from 'react';
import uid from 'uid';
import * as xtremRedux from '../../../redux';
import { lookupDialog } from '../../../service/dialog-service';
import { fetchReferenceFieldSuggestions } from '../../../service/graphql-service';
import { localize } from '../../../service/i18n-service';
import {
    getInitialCellEditorState,
    setDefaultAgGridInputStyles,
} from '../../../utils/ag-grid/ag-grid-cell-editor-utils';
import { shouldRenderDropdownAbove } from '../../../utils/table-component-utils';
import { cleanMetadataAndNonPersistentIdFromRecord, splitValueToMergedValue } from '../../../utils/transformers';
import type { ReferenceDecoratorProperties } from '../../decorators';
import type { SelectItem } from '../../ui/select/select-component';
import { Select } from '../../ui/select/select-component';
import { nodeToSelectItem, nodesToSelectItems } from '../reference/reference-utils';
import type { FilterSelectCellEditorProps } from '../select/select-types';

interface FilterSelectEditorState {
    id: string;
    highlightOnFocus: boolean;
    isLookupPanelOpen: boolean;
    selectedRecord?: SelectItem;
    startValue: string;
}

export default class FilterSelectCellEditor extends React.Component<
    FilterSelectCellEditorProps,
    FilterSelectEditorState
> {
    private readonly selectRef: React.RefObject<HTMLInputElement>;

    constructor(props: FilterSelectCellEditorProps) {
        super(props);
        this.selectRef = React.createRef();
        this.state = this.createInitialState(props);
    }

    componentDidMount(): void {
        const input = this.selectRef.current;
        if (!input) {
            return;
        }
        // try to match the styles of ag-grid
        setDefaultAgGridInputStyles(input);
        const inputDiv = input.parentNode as HTMLInputElement;
        inputDiv.style.width = '100%';
        inputDiv.style.height = '100%';
        inputDiv.style.border = 'none';
        inputDiv.style.display = 'flex';
        inputDiv.style.alignItems = 'center';

        input.focus();
        if (this.state.highlightOnFocus) {
            input.select();
            this.setState({
                highlightOnFocus: false,
            });
        } else {
            // when we started editing, we want the caret at the end, not the start.
            // this comes into play in two scenarios: a) when user hits F2 and b)
            // when user hits a printable character, then on IE (and only IE) the caret
            // was placed after the first character, thus 'apply' would end up as 'pplea'
            const length = input.value ? input.value.length : 0;
            if (length > 0) {
                input.setSelectionRange(length, length);
            }
        }
    }

    getItems = async (filterValue: string): Promise<SelectItem[]> => {
        const level = this.props.node?.data?.__level;
        const nodes = await fetchReferenceFieldSuggestions({
            fieldProperties: this.props.fieldProperties,
            screenId: this.props.screenId,
            fieldId: this.props.elementId,
            filterValue,
            parentElementId: this.props.tableElementId,
            recordContext: splitValueToMergedValue(cleanMetadataAndNonPersistentIdFromRecord(this.props.data)),
            level,
            contextNode: String(this.props.contextNode),
        });

        return nodesToSelectItems({
            nodes,
            fieldProperties: this.props.fieldProperties,
        }).map(item => omitBy(item, (_v, k) => k === '__collectionItem') as Omit<SelectItem, '__collectionItem'>);
    };

    onSelected = (selectedRecord?: SelectItem | null, selectionType?: UseComboboxStateChangeTypes): void => {
        this.props.eGridCell.id = selectedRecord?.id ?? '';

        this.props.onValueChange(selectedRecord?.value ?? null);
        if (selectionType !== '__input_blur__') {
            this.props.stopEditing(true);
        }
    };

    isLookupDialogOpen = (): boolean => this.state.isLookupPanelOpen && !!this.props.fieldProperties.columns;

    openLookupDialog = async (event: React.MouseEvent<HTMLAnchorElement | HTMLButtonElement>): Promise<void> => {
        event.stopPropagation();
        event.preventDefault();

        try {
            const selection = await lookupDialog(this.props.screenId, 'info', {
                fieldProperties: this.props.fieldProperties as ReferenceDecoratorProperties,
                fieldId: this.props.elementId,
                onLookupDialogClose: async (): Promise<void> => {
                    this.setState({ isLookupPanelOpen: false });
                    setTimeout(() => {
                        this.props.api.setFocusedCell(
                            this.props.node.rowIndex ?? 0,
                            this.props.column,
                            this.props.node.isRowPinned() ? 'top' : null,
                        );
                    }, 50);
                    this.props.stopEditing(true);
                },
                parentElementId: this.props.tableElementId,
                contextNode: String(this.props.contextNode),
                recordContext: splitValueToMergedValue(cleanMetadataAndNonPersistentIdFromRecord(this.props.data)),
            });
            if (selection) {
                this.onSelected(
                    nodeToSelectItem({ collectionItem: selection, fieldProperties: this.props.fieldProperties }),
                );
            } else {
                this.onSelected();
            }
        } catch {
            /* intentionally left empty */
        }

        const dispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch; // TODO: Find out if needed.
        dispatch(xtremRedux.actions.removeNonNestedErrors(this.props.screenId, this.props.elementId));
    };

    getLocalizedSuffix = (): string => `(${localize('@sage/xtrem-ui/new', 'New')})`;

    getNewItem = (value: string): SelectItem | undefined => {
        if (!this.props.fieldProperties.isNewEnabled) {
            return undefined;
        }
        const displayedAs = `${value} ${this.getLocalizedSuffix()}`;
        return {
            id: '-1',
            value,
            displayedAs,
        };
    };

    createInitialState(props: FilterSelectCellEditorProps): FilterSelectEditorState {
        const { highlightOnFocus, value: startValue } = getInitialCellEditorState({
            eventKey: props.eventKey,
            initialValue: props.initialValue,
        });

        const selectedRecord = props.value
            ? {
                  id: this.props.eGridCell.id || '',
                  value: props.value,
                  displayedAs: props.value,
              }
            : undefined;

        return {
            highlightOnFocus,
            id: uid(16),
            isLookupPanelOpen: false,
            selectedRecord,
            startValue,
        };
    }

    render(): React.ReactNode {
        const dataTestId = `${this.props.tableElementId}-${this.props.node.rowIndex}-${
            (this.props.api.getColumns() ?? []).indexOf(this.props.column!) + 1
        }`;
        const width = this.props.eGridCell?.style?.width || '200px';

        return (
            <div data-testid={dataTestId} style={{ width }} className="e-filter-select-cell-editor">
                <Select
                    allowClearOnTab={true}
                    allowSelectOnTab={true}
                    autoSelect={true}
                    defaultIsOpen={true}
                    elementId={this.props.elementId}
                    escapeBehavior="revert"
                    getItems={this.getItems}
                    getNewItem={this.getNewItem}
                    hasLookupIcon={false}
                    initialInputValue={this.state.startValue}
                    isSortedAlphabetically={true}
                    isDropdownDisabled={false}
                    isInTable={true}
                    lookupIconId={`e-filter-select-cell-editor-lookup-icon-${this.state.id}`}
                    minLookupCharacters={this.props.fieldProperties.minLookupCharacters}
                    onLookupIconClick={this.openLookupDialog}
                    onSelected={this.onSelected}
                    placeholder={this.props.fieldProperties.placeholder}
                    preventSelectionOnBlur={this.isLookupDialogOpen()}
                    ref={this.selectRef}
                    screenId={this.props.screenId}
                    selectedItem={this.state.selectedRecord}
                    shouldRenderOptionsAbove={shouldRenderDropdownAbove({
                        isPhantomRow: !!this.props.data?.__phantom,
                        pageSize: this.props.api.paginationGetPageSize(),
                        rowIndex: this.props.node.rowIndex ?? 0,
                    })}
                    testId={`${dataTestId}-input`}
                    variant="plain"
                />
            </div>
        );
    }
}
