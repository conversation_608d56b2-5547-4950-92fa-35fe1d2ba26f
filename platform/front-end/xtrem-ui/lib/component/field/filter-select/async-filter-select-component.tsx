import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { FilterSelectComponentProps } from './filter-select-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const ConnectedFilterSelectComponent = React.lazy(() => import('./filter-select-component'));

export function AsyncConnectedFilterSelectComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedFilterSelectComponent {...props} />
        </React.Suspense>
    );
}

const FilterSelectComponent = React.lazy(() =>
    import('./filter-select-component').then(c => ({ default: c.FilterSelectComponent })),
);

export function AsyncFilterSelectComponent(props: FilterSelectComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <FilterSelectComponent {...props} />
        </React.Suspense>
    );
}
