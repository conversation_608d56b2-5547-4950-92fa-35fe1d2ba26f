import type { ClientNode } from '@sage/xtrem-client';
import type { UseComboboxStateChangeTypes } from 'downshift';
import { useCombobox } from 'downshift';
import { debounce, get } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import uid from 'uid';
import * as xtremRedux from '../../../redux';
import { lookupDialog } from '../../../service/dialog-service';
import { fetchReferenceFieldSuggestions } from '../../../service/graphql-service';
import { localize } from '../../../service/i18n-service';
import { SoundApi } from '../../../service/sound-api';
import type { LookupDialogContent } from '../../../types/dialogs';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import { isDevMode } from '../../../utils/window';
import type { ReferenceDecoratorProperties } from '../../decorator-properties';
import type { CollectionItem } from '../../types';
import type { SelectItem } from '../../ui/select/select-component';
import { Select } from '../../ui/select/select-component';
import { getCommonCarbonComponentProperties, getLabelTitle } from '../carbon-helpers';
import { CarbonWrapper } from '../carbon-wrapper';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import { getReferencePath } from '../reference/reference-utils';
import type {
    FilterSelectComponentProps,
    FilterSelectDecoratorProperties,
    FilterSelectState,
} from './filter-select-types';

export class FilterSelectComponent<T extends ClientNode = any> extends EditableFieldBaseComponent<
    FilterSelectDecoratorProperties<any, T>,
    string,
    {},
    FilterSelectState
> {
    private readonly lookupButtonRef: React.RefObject<HTMLButtonElement> = React.createRef();

    private searchText: string;

    private readonly selectInputRef: React.RefObject<HTMLInputElement>;

    constructor(props: FilterSelectComponentProps<T>) {
        super(props);
        this.searchText = this.getSearchTextFromValue();
        this.selectInputRef = React.createRef();
        this.state = {
            id: uid(16),
        };
    }

    private readonly onInputValueChanged = debounce(async (searchText: string, type?: UseComboboxStateChangeTypes) => {
        if (type !== useCombobox.stateChangeTypes.FunctionSelectItem) {
            await triggerFieldEvent(this.props.screenId, this.props.elementId, 'onInputValueChange', searchText);
        }
    }, 150);

    private readonly getSearchTextFromValue = (): string => {
        return this.props.value ? String(this.props.value) : '';
    };

    private readonly getSelectedItemFromValue = (): SelectItem | undefined => {
        return this.props.value
            ? {
                  id: '-1',
                  value: String(this.props.value),
                  displayedAs: String(this.props.value),
              }
            : undefined;
    };

    private readonly onChangeCallback = (newValue: any): void => {
        this.triggerChangeListener(newValue);
    };

    private readonly getItems = async (filterValue: string): Promise<SelectItem[]> => {
        const nodes = await fetchReferenceFieldSuggestions({
            fieldProperties: this.props.fieldProperties,
            screenId: this.props.screenId,
            fieldId: this.props.elementId,
            filterValue,
            recordContext: this.props.recordContext,
            contextNode: this.props.contextNode,
            level: this.props.level,
        });
        return this.getSelectItemsFromNodes(nodes);
    };

    private readonly getSelectItemsFromNodes = (data?: CollectionItem[]): SelectItem[] => {
        return (data ?? []).map(splitValueToMergedValue).map(this.getSelectItemFromCollectionItem);
    };

    private readonly getSelectItemFromCollectionItem = (collectionItem: CollectionItem): SelectItem => {
        const value = get(
            collectionItem,
            getReferencePath(this.props.fieldProperties.valueField as keyof typeof collectionItem),
        );
        return {
            displayedAs: value,
            id: collectionItem._id,
            value,
        };
    };

    private readonly getLocalizedSuffix = (): string => `(${localize('@sage/xtrem-ui/new', 'New')})`;

    private readonly getNewItem = (value: string): SelectItem | undefined => {
        if (!this.props.fieldProperties.isNewEnabled) {
            return undefined;
        }
        const displayedAs = `${value} ${this.getLocalizedSuffix()}`;
        return {
            id: '-1',
            value,
            displayedAs,
        };
    };

    private readonly isMobileLookupDialogOpen = (): boolean => {
        return !!this.props.fieldProperties.isFilterSelectDialogOpen && !this.isReadOnly() && !this.isDisabled();
    };

    private readonly isMobileSize = (): boolean => {
        return this.props.browser?.lessThan.m ?? false;
    };

    private readonly triggerChangeEvent = (value: string, isOrganicChange: boolean): void => {
        if (isOrganicChange) {
            handleChange(
                this.props.elementId,
                value,
                this.props.setFieldValue,
                this.props.validate,
                this.onChangeCallback,
            );
        }
    };

    getLookupContent = (searchText?: string): LookupDialogContent => {
        return {
            onLookupDialogClose: async (): Promise<void> => {
                // this.triggerChangeEvent('', true);
                this.lookupButtonRef.current?.focus();
                this.props.setFieldProperties?.(this.props.elementId, {
                    ...this.props.fieldProperties,
                    isFilterSelectDialogOpen: false,
                });
            },
            contextNode: this.props.contextNode,
            fieldId: this.props.elementId,
            fieldProperties: this.props.fieldProperties as ReferenceDecoratorProperties,
            recordContext: this.props.recordContext,
            searchText: searchText || this.searchText,
        };
    };

    private readonly onMobileButtonClick = async (event: React.MouseEvent<HTMLButtonElement>): Promise<void> => {
        event.stopPropagation();
        event.preventDefault();

        try {
            this.searchText = this.selectInputRef.current?.value ?? '';
            this.props.setFieldProperties?.(this.props.elementId, {
                ...this.props.fieldProperties,
                isFilterSelectDialogOpen: true,
            });
            const selection = await lookupDialog(
                this.props.screenId,
                'info',
                this.getLookupContent(this.selectInputRef.current?.value),
            );
            this.onLookupDialogSelectionFinished(selection);
        } catch {
            /* intentionally left empty */
        }
    };

    private readonly onLookupDialogSelectionFinished = ([collectionItem]: CollectionItem[]): void => {
        if (collectionItem) {
            const searchText = get(
                collectionItem,
                getReferencePath(this.props.fieldProperties.valueField as keyof typeof collectionItem),
                '',
            );
            this.triggerChangeEvent(searchText, true);
        }
        this.lookupButtonRef.current?.focus();
    };

    private readonly mobileAutoSelect = debounce((type?: UseComboboxStateChangeTypes) => {
        const isDifferentSearchText = this.props.value !== this.selectInputRef.current?.value;
        const isOrganicChange = type !== useCombobox.stateChangeTypes.FunctionSelectItem;
        const shouldOpenDialog = isDifferentSearchText && this.selectInputRef.current?.value !== '' && isOrganicChange;
        const soundApi = new SoundApi(
            resolveByValue({
                screenId: this.props.screenId,
                propertyValue: this.props.fieldProperties.isSoundDisabled,
                skipHexFormat: true,
                fieldValue: null,
                rowValue: null,
            }),
        );
        if (!this.selectInputRef.current?.value) {
            this.triggerChangeEvent('', isOrganicChange);
            return;
        }

        this.getItems(this.selectInputRef.current.value)
            .then(async (result: SelectItem[]) => {
                if (result.length === 1 && String(result[0].value) === this.selectInputRef.current?.value) {
                    this.triggerChangeEvent(String(result[0].value), isOrganicChange);
                    await soundApi.success();
                    return;
                }

                if (!result.length) {
                    await soundApi.error();
                }

                if (shouldOpenDialog && this.selectInputRef.current) {
                    this.searchText = this.selectInputRef.current.value;

                    try {
                        this.searchText = this.selectInputRef.current?.value ?? '';

                        this.props.setFieldProperties?.(this.props.elementId, {
                            ...this.props.fieldProperties,
                            isFilterSelectDialogOpen: true,
                        });

                        const selection = await lookupDialog(
                            this.props.screenId,
                            'info',
                            this.getLookupContent(this.selectInputRef.current?.value),
                        );
                        this.onLookupDialogSelectionFinished(selection);
                    } catch {
                        /* intentionally left empty */
                    }
                } else if (isDifferentSearchText) {
                    this.triggerChangeEvent('', isOrganicChange);
                }
            })
            .catch(reason => {
                if (isDevMode()) {
                    // eslint-disable-next-line no-console
                    console.error('Unable to get items:', reason);
                }
            });
    }, 200);

    private readonly onMobileInputChange = async (
        searchText: string,
        type?: UseComboboxStateChangeTypes,
    ): Promise<void> => {
        this.mobileAutoSelect(type);
        await this.onInputValueChanged(searchText, type);
    };

    private readonly onChange = (selectedRecord: SelectItem | undefined, isOrganicChange: boolean): void => {
        if (isOrganicChange) {
            this.triggerChangeEvent(selectedRecord?.value ? String(selectedRecord.value) : '', isOrganicChange);
        }
    };

    private renderMobile(): React.ReactNode {
        const carbonProps = getCommonCarbonComponentProperties(this.props);
        const initialInputValue = this.getSearchTextFromValue();
        const initialSelectedItem = this.getSelectedItemFromValue();

        return (
            <CarbonWrapper
                {...this.props}
                className="e-filter-select-field"
                componentName="filter-select"
                componentRef={this.componentRef}
                helperText={this.props.fieldProperties.helperText}
                noReadOnlySupport={true}
                value={this.props.value}
            >
                <div data-testid="e-filter-select-field-mobile-wrapper">
                    <Select
                        {...carbonProps}
                        autoSelect={false}
                        disabled={this.isDisabled()}
                        elementId={this.props.elementId}
                        fullWidth={this.props.fieldProperties.isFullWidth}
                        getItems={this.getItems}
                        getNewItem={this.getNewItem}
                        hasLookupIcon={true}
                        helperText={this.props.fieldProperties.helperText}
                        icon={this.props.fieldProperties.icon}
                        initialInputValue={initialInputValue}
                        inputId={carbonProps.id}
                        isSortedAlphabetically={true}
                        isDropdownDisabled={true}
                        label={getLabelTitle(
                            this.props.screenId,
                            this.props.fieldProperties,
                            this.props.handlersArguments?.rowValue,
                        )}
                        lookupButtonRef={this.lookupButtonRef}
                        lookupIconId={`e-filter-select-field-lookup-icon-${this.state.id}`}
                        minLookupCharacters={this.getMinLookupCharacters()}
                        onChange={this.onChange}
                        onInputChange={this.onMobileInputChange}
                        onInputFocus={this.props.onFocus}
                        onLookupIconClick={this.onMobileButtonClick}
                        placeholder={this.props.fieldProperties.placeholder}
                        preventSelectionOnBlur={this.isMobileLookupDialogOpen()}
                        readOnly={this.isReadOnly()}
                        ref={this.selectInputRef}
                        screenId={this.props.screenId}
                        selectedItem={initialSelectedItem}
                        testId="e-filter-select-field-input"
                        isSoundDisabled={resolveByValue({
                            screenId: this.props.screenId,
                            propertyValue: this.props.fieldProperties.isSoundDisabled,
                            skipHexFormat: true,
                            fieldValue: null,
                            rowValue: null,
                        })}
                    />
                </div>
            </CarbonWrapper>
        );
    }

    private readonly getMinLookupCharacters = (): number => this.props.fieldProperties.minLookupCharacters ?? 3;

    renderDesktop(): React.ReactNode {
        const carbonProps = getCommonCarbonComponentProperties(this.props);
        const initialInputValue = this.getSearchTextFromValue();
        const initialSelectedItem = this.getSelectedItemFromValue();
        return (
            <CarbonWrapper
                {...this.props}
                className="e-filter-select-field"
                componentName="filter-select"
                componentRef={this.componentRef}
                helperText={this.props.fieldProperties.helperText}
                noReadOnlySupport={true}
                value={this.props.value}
            >
                <div data-testid="e-filter-select-field-wrapper">
                    <Select
                        {...carbonProps}
                        autoSelect={true}
                        disabled={this.isDisabled()}
                        fullWidth={this.props.fieldProperties.isFullWidth}
                        getItems={this.getItems}
                        getNewItem={this.getNewItem}
                        helperText={this.props.fieldProperties.helperText}
                        icon={this.props.fieldProperties.icon}
                        initialInputValue={initialInputValue}
                        inputId={carbonProps.id}
                        isSortedAlphabetically={true}
                        label={getLabelTitle(
                            this.props.screenId,
                            this.props.fieldProperties,
                            this.props.handlersArguments?.rowValue,
                        )}
                        lookupButtonRef={this.lookupButtonRef}
                        minLookupCharacters={this.getMinLookupCharacters()}
                        noHelperTextInItem={true}
                        onChange={this.onChange}
                        onInputFocus={this.props.onFocus}
                        onInputChange={this.onInputValueChanged}
                        placeholder={this.props.fieldProperties.placeholder}
                        readOnly={this.isReadOnly()}
                        selectedItem={initialSelectedItem}
                        testId="e-filter-select-field-input"
                        screenId={this.props.screenId}
                        elementId={this.props.elementId}
                        isSoundDisabled={resolveByValue({
                            screenId: this.props.screenId,
                            propertyValue: this.props.fieldProperties.isSoundDisabled,
                            skipHexFormat: true,
                            fieldValue: null,
                            rowValue: null,
                        })}
                    />
                </div>
            </CarbonWrapper>
        );
    }

    render(): React.ReactNode {
        return this.isMobileSize() && !this.isReadOnly() ? this.renderMobile() : this.renderDesktop();
    }
}

const extendedMapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: FieldComponentExternalProperties,
): Partial<FilterSelectComponentProps> => {
    const defaultMapDispatchToProps = mapDispatchToProps<FilterSelectDecoratorProperties>()(dispatch, props);
    return {
        ...defaultMapDispatchToProps,
        setFieldProperties: (elementId: string, value: any): void => {
            dispatch(xtremRedux.actions.setFieldProperties(props.screenId, elementId, value));
        },
    };
};

export const ConnectedFilterSelectComponent = connect(
    mapStateToProps,
    extendedMapDispatchToProps,
)(FilterSelectComponent);

export default ConnectedFilterSelectComponent;
