.e-filter-select-field {
    display: flex;

    .e-portrait {
        padding-right: 8px;
        display: inline-block;
        vertical-align: top;
    }

    >div {
        flex: 1;
        max-width: 100%;
    }

    &.e-filter-select-inline-picture {
        display: flex;

        .e-portrait {
            flex: unset;
        }
    }

    &.e-filter-select-field-lookup {
        [type='dropdown'] {
            display: none;
        }
    }

    .e-filter-select-field-lookup-button-mobile {
        padding: 12px;
        margin-right: 0;
        margin-right: -10px;
        width: 40px;
        border-left: 1px solid var(--colorsUtilityMajor300);
        border-top: none;
        border-right: none;
        border-bottom: none;

        &[disabled] {
            border-left: 1px solid var(--colorsUtilityMajor100);
        }

        span {
            margin-right: 0;
            color: var(--colorsYin065);
        }

        &.e-filter-select-field-lookup-button-mobile-small {
            padding-top: 8px;
        }

        &.e-filter-select-field-lookup-button-mobile-large {
            padding-top: 16px;
        }
    }

    span > span[data-component='icon']:not([role="tooltip"]) {
        order: 1;
        margin-right: 16px;
        padding-top: 3px;
        padding-bottom: 3px;
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
    }
}
