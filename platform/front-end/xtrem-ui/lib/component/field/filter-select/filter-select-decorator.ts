/**
 * @packageDocumentation
 * @module root
 */
import type { ClientNode } from '@sage/xtrem-client';
import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import {
    addColumnsToProperties,
    addDisabledToProperties,
    addMaxLengthToProperties,
    addNodeToProperties,
    addValueFieldToProperties,
} from '../../../utils/data-type-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FilterSelectControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { FilterSelectDecoratorProperties, FilterSelectExtensionDecoratorProperties } from './filter-select-types';

export enum FilterSelectDecoratorPropertiesKeys {
    onCloseLookupDialog = 'onCloseLookupDialog',
    onOpenLookupDialog = 'onOpenLookupDialog',
}

class FilterSelectDecorator extends AbstractFieldDecorator<FieldKey.FilterSelect> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = FilterSelectControlObject;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<FilterSelectDecoratorProperties> {
        const properties: Partial<FilterSelectDecoratorProperties> = {};
        addNodeToProperties({ dataType, propertyDetails, properties });
        addColumnsToProperties({
            dataType,
            propertyDetails,
            properties,
            dataTypes: this.dataTypes,
            nodeTypes: this.nodeTypes,
        });
        addValueFieldToProperties({
            dataType,
            propertyDetails,
            properties,
        });
        addMaxLengthToProperties({
            dataType,
            propertyDetails,
            properties,
        });
        addDisabledToProperties({
            propertyDetails,
            dataType,
            properties,
        });
        return properties;
    }
}

/**
 * Initializes the decorated member as a [FilterSelect]{@link FilterSelectControlObject} field with the provided properties.
 *
 * @param properties The properties that the [FilterSelect]{@link FilterSelectControlObject} field will be initialized with.
 */
export function filterSelectField<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: FilterSelectDecoratorProperties<Extend<T>, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.FilterSelect>(
        properties as any,
        FilterSelectDecorator,
        FieldKey.FilterSelect,
        true,
    );
}

export function filterSelectFieldOverride<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: FilterSelectExtensionDecoratorProperties<T, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.FilterSelect>(properties);
}
