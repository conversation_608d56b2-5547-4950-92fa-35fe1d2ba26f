PATH: XTREEM/UI+Field+Widgets/Filter+Select+Field

## Introduction

Filter Select fields can be used to represent a string value from an existing or to be created node record. The field is also available as an extensions field and as a nested field. When the value doesn't match any record from the property's node, that value will be displayed with **`(New)`** appended to the text.

Filter Select contains a lookup dialog in mobile mode with an input to search for specific records for faster selection. It can be accessed either by clicking the lookup icon on the right side of the filter select input or by starting typing in the input. A list of records will be displayed as rows in the lookup dialog, each row containing the node property's value or a list of node properties values. Each row can be selected and the selected value will be set in the field's input and the dialog will be closed.

Filter Select component can be also used for scanning purposes.
## Example

```ts
@ui.decorators.filterSelectField<Page, Node>({
    bind: 'product',
    helperText: 'Helper text displayed below the field.',
    isDisabled: false,
    isFullWidth: false,
    isHelperTextHidden: true,
    isHidden: false,
    isMandatory: false,
    isNewEnabled: true,
    isReadOnly: false,
    isTransient: false,
    minLookupCharacters: 3,
    minLength: 0,
    maxLength: 255,
    node: '@sage/xtrem-test/TestNode',
    placeholder: 'Scan or search product...',
    size: 'medium',
    title: 'Filter Select Field',
    parent () {
        return this.block;
    },
    validation (value: string) {
        return value === 'INVALID_VALUE' ? 'This value is invalid' : '';
    },
})
field: ui.fields.FilterSelect<Node>;
```

#### Extension Field Example

```ts
@ui.decorators.filterSelectField<Page, Node>({
    ...,
    title: 'Extended Filter Select Field',
    insertBefore () {
        return this.field;
    },
    parent () {
        return this.block;
    },
})
extension: ui.fields.FilterSelect<Node>;
```

## Decorator Properties

#### Display Properties

-   **columns**: Column definitions for the lookup dialog. Columns are defined as a list of nested fields, which can be one of the following types: `checkbox`, `icon`, `image`, `label`, `link`, `numeric`, `progress`, `reference`, `text`, `date`. Up to 4 columns can be displayed. If no columns are defined, the lookup dialog will display information bound to the node property defined in the **`valueField`** property. It is highly recommended that one of the columns be the actual **`valueField`** property, as it is that field the one that is going to be sought and selected by the user. If there is no match between the search text and the values of the **`valueField`** property, an empty row will be displayed in the lookup dialog.
-   **helperText**: Specifies the helper text displayed above the field. It is automatically picked up by the i18n engine and externalized.
-   **icon**: Specifies the icon to be displayed in the right side of the field's input. The list of icons is defined by [DLS](https://brand.sage.com/d/NdbrveWvNheA/foundations#/icons/icons).
-   **isDisabled**: Specifies whether the field is editable or not. The difference between isDisabled and isReadOnly, is that isReadOnly suggests that the field is never editable, whereas isDisabled suggest that the field is currently not editable. It can also be defined as callback function that returns a boolean.
-   **isFullWidth**: Specifies whether the field should take the full width.
-   **isHelperTextHidden**: Specifies whether the field's helper text should be displayed or not.
-   **isHidden**: Specifies whether the component should be displayed or not.
-   **isReadOnly**: Specifies whether the field is editable or not. The difference between isDisabled and isReadOnly, is that isReadOnly suggests that the field is never editable, whereas isDisabled suggest that the field is currently not editable. It can also be defined as callback function that returns a boolean.
-   **isTitleHidden**: Specifies whether the field's title should be displayed or not.
-   **placeholder**: Specifies the field's input placeholder. It is automatically picked up by the i18n engine and externalized.
-   **size**: Specifies the field's vertical size. Options are `small`, `medium` and `large`. By default, the size is set to `medium`.
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **width**: Specifies the field's width. The width can be defined by using field size categories which are remapped to actually width values by the framework depending on the screen size and the container size that the field is in.
-   **additionalLookupRecords**: Records provided here are added to the lookup dialog and dropdown in addition to the server provided records.
-   **lookupDialogTitle**: The title that is displayed in the lookup dialog of the filter select field on mobile.
-   **info**: Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function that returns a string.
-   **warning**: Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback function that returns a string.
-   **icon**: Icon to be displayed on the right side of the input. The list of icons are defined by [DLS](https://brand.sage.com/d/NdbrveWvNheA/foundations#/icons/icons).
-   **iconColor**: Color of the input icon, only supported if the field is rendered within a tile container.
-   **isSoundDisabled**: Specifies whether the sound output of the field should be disabled/silenced. Defaults to false.

#### Data Binding Properties

-   **bind**: Specifies the GraphQL object's property the field's value is bound ot. If not provided, the field's name is used.
-   **canFilter**: Specified whether the field's lookup dialog result can be filtered or not.
-   **filter**: The GraphQL filter applied to the values. Nested logic operators such as `_or` within another `_or` filter object is not supported.
-   **isNewEnabled**: Specifies whether the component can accept new values, whose node should be created upon save, or only existing values for which a node already exists.
-   **isTransient**: If marked true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **minLookupCharacters**: Minimum number of characters required to trigger a server lookup. If set to `0` the first 20 suggestions will be fetched on focus. By default, the value is set to `3`.
-   **node**: The GraphQL node property that the field suggestions will be fetched from.
-   **orderBy**: The GraphQL sort order applied to the values.
-   **valueField**: The GraphQL node property that will be used as the field's value. It is a **`string`** referring to an existing property in the node.

#### Event Handler Properties

-   **onChange()**: Triggered when the field's value changed and the focus is about to move away from the field. No arguments are provided.
-   **onClick()**: Triggered when the field is clicked. No arguments are provided.
-   **onOpenLookupDialog()**: Triggered when the field's mobile lookup dialog is opened. No arguments are provided.
-   **onCloseLookupDialog()**: Triggered when the field's mobile lookup dialog is closed. No arguments are provided.
-   **onInputValueChange()**: Triggered when the user stops typing into the input of the field. The execution is debounced by 150ms to cater for continuous typing.

#### Validation Properties

-   **isMandatory**: Specifies whether the field is mandatory or not. When enabled, empty values will raise an validation error message. It can also be defined as callback function that returns a boolean.
-   **maxLength**: Sets the maximum number of allowed characters.
-   **minLength**: Sets the minimum number of required characters.
-   **validation()**: Custom validation callback with the new value provided as argument. If the function returns a non-empty string (or promise resolving to a non-empty string), the return value will be used as validation error message. If the function returns a falsy value, the field is considered to be valid.

#### Extension Properties

-   **insertBefore()**: Inserts the current field before the returned field, only for extension pages.

### Other Decorator Properties

-   **fetchesDefaults**: When set to true and when the filter select value changes, a request to the server for default values for the whole page will be requested. False by default.

#### Runtime Functions
-   **focus()**: Moves the browser's focus to the field.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

#### Component properties
The desktop render of filter-select-component has autoSelect={true} set, if there's a single match it will auto-select it and a success sound will play, if there's no single match then the error sound is played.
The mobile render instead, has autoSelect={false} set, therefore we have a callback where we check if there's a single match it will auto-select it and a success sound will play, if there's no single match then the error sound is played.

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/FilterSelect).
