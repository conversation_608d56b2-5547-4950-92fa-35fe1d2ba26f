import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { WorkflowComponentProperties } from './workflow-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';

const ConnectedFormDesignerComponent = React.lazy(() => import('./workflow-component'));

export function AsyncConnectedWorkflowComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedFormDesignerComponent {...props} />
        </React.Suspense>
    );
}

const WorkflowComponent = React.lazy(() =>
    import('./workflow-component').then(c => ({ default: c.WorkflowComponent })),
);

export function AsyncFormDesignerComponent(props: WorkflowComponentProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <WorkflowComponent {...props} />
        </React.Suspense>
    );
}
