import React from 'react';
import type { EdgeProps } from 'reactflow';
import { EdgeLabelRenderer, BaseEdge, getSmoothStepPath } from 'reactflow';
import * as tokens from '@sage/design-tokens/js/base/common';
import { AddNewNodeEdgeLabel } from './add-new-node-edge-label';
import { WorkflowContext } from './workflow-context-provider';

export function CustomEdge({
    id,
    sourceX,
    sourceY,
    targetX,
    targetY,
    sourcePosition,
    targetPosition,
    markerEnd,
    selected,
    style,
}: EdgeProps<{ screenId: string; elementId: string }>): React.ReactElement {
    const workflowContext = React.useContext(WorkflowContext);
    const isReadOnly = workflowContext?.isReadOnly ?? false;
    const [edgePath, centerX, centerY] = getSmoothStepPath({
        sourceX,
        sourceY,
        sourcePosition,
        targetX,
        targetY,
        targetPosition,
        borderRadius: 20,
    });

    return (
        <>
            <BaseEdge
                id={id}
                path={edgePath}
                markerEnd={markerEnd}
                style={{ ...style, stroke: selected ? tokens.colorsSemanticFocus500 : '#A6A6A6' }}
            />
            <EdgeLabelRenderer>
                {!isReadOnly && <AddNewNodeEdgeLabel edgeId={id} centerX={centerX} centerY={centerY} />}
            </EdgeLabelRenderer>
        </>
    );
}
