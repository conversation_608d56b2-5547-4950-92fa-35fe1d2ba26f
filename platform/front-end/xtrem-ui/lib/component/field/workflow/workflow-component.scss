@import '~reactflow/dist/style.css';

[data-element="workflow-dialog"] {
    [data-element="form-content"] {
        display: flex;
        flex-direction: column;
    }
}

.e-workflow-field {

    .react-flow__controls-button:focus {
        outline: solid 3px var(--colorsSemanticFocus500);

    }

    .react-flow__attribution {
        display: none;
    }

    .react-flow__pane {
        background: var(--colorsActionMinor050);
        border-radius: 3px;
    }

    .e-workflow-add-node-on-edge-button {
        background: var(--colorsYang100);

        &:hover {
            background: var(--colorsActionMinor500);
        }
    }

    .react-flow__handle {
        width: 8px;
        height: 8px;

        &.react-flow__handle-right {
            top: calc(50% + 10px);
            right: -4px;
        }

        &.react-flow__handle-left {
            top: calc(50% + 10px);
            left: -4px;
        }

        &.react-flow__handle-bottom {
            bottom: -4px;
        }

        &.react-flow__handle-top {
            background: none;
            width: auto;
            top: -25px;
            height: 25px;
            border: none;
            cursor: default;
        }
    }

    .e-workflow-wrapper,
    .e-workflow-empty {
        border: 1px solid var(--colorsUtilityMajor300);
        border-radius: var(--borderRadius050);
    }

    .e-workflow-empty {
        padding: 12px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        justify-items: center;

        .e-workflow-empty-content {
            text-align: center;
        }
    }

    .react-flow__node {
        border: none;
        width: unset;
        background: transparent;
        padding: 0;
        box-shadow: none;


        &.selected.selectable {
            box-shadow: none;

            &:focus {
                box-shadow: none;

                .e-workflow-node {
                    outline: solid 3px var(--colorsSemanticFocus500);
                }
            }
        }

        &.selectable:hover {
            box-shadow: none;
        }
    }

    .e-workflow-node-header-content {
        display: inline-block;
        border-top-right-radius: 4px;
        border-top-left-radius: 4px;
        padding: 2px 8px;
        font-size: var(--fontSizes100);
        font-style: normal;
        font-weight: var(--fontWeights500);
        line-height: var(--lineHeights500);
    }

    .e-workflow-node-wrapper {
        position: relative;
    }

    .e-workflow-node-add-button {
        position: absolute;
        bottom: -40px;
        width: 100%;
        display: flex;
        justify-content: center;
    }

    .e-workflow-node {
        border: none;
        background: var(--colorsActionMajorYang100);
        padding: 24px;
        box-shadow: var(--boxShadow050);
        width: 250px;
        display: flex;
        box-sizing: border-box;
        border-radius: 16px;

        .e-workflow-node-icon-wrapper {
            margin-right: 12px;
            padding: 4px;
            border-radius: 4px;
            display: inline-block;
            box-sizing: content-box;
            height: 24px;
            width: 24px;
        }

        .e-workflow-node-icon {
            width: 24px;
            height: 24px;
        }

        .e-workflow-node-body {
            display: inline-block;
            vertical-align: top;
            flex: 1;
        }

        .e-workflow-node-title {
            display: flex;

            .e-workflow-node-title-label {
                flex: 1;
                font-size: 16px;
                text-align: left;
            }

        }

        .e-workflow-node-subtitle {
            font-family: 12px;
            text-align: left;
        }
    }

    .e-workflow-node-event-running {
        border: 3px solid var(--colorsSemanticInfo500);
    }

    .e-workflow-node-event-success {
        border: 3px solid var(--colorsSemanticPositive500);
    }

    .e-workflow-node-event-error {
        border: 3px solid var(--colorsSemanticNegative500);
    }
    
    .e-workflow-node-event-suspended {
        border: 3px solid var(--colorsSemanticWarning500);
    }
    
    .e-workflow-node-event-shutDown {
        border: 3px solid var(--colorsActionMajorYin090);
    }

    .e-workflow-node-event-view-details {
        cursor: pointer;
    }
    

    .selection-card-container {
        display: grid;
        grid-template-columns: repeat(2, 50%);
        gap: 10px;

        .e-selection-card {
            min-height: 126px;
        }

        @include small_and_below {
            grid-template-columns: repeat(1, 100%);

        }
    }

}

.e-workflow-condition-path-label {
    border-radius: 4px;
    border: 2px solid var(--colorsUtilityMajor200);
    background: var(--colorsYang100);
    color: var(--colorsYin090);
    display: inline-flex;
    padding: 0px 6px;
    justify-content: center;
    align-items: center;
}

.e-workflow-node-event-sidebar {

    .common-input__label {
        @include e-field-field-label;
    }
}

.e-workflow-add-dialog {
    .selection-card-container {
        display: grid;
        grid-template-columns: repeat(2, 50%);
        gap: 10px;

        .e-selection-card {
            min-height: 126px;
        }

        @include small_and_below {
            grid-template-columns: repeat(1, 100%);

        }
    }
}

.e-dialog-custom-body .e-workflow-field .e-workflow-wrapper {
    border: none;
    border-radius: none;
}



.e-workflow-node-floating-buttons {
    position: relative;
    display: flex;
    justify-content: space-between;
    width: 100%;
    top: 30px; /* Adjust as needed */
}

.e-workflow-node-floating-button-left {
    position: absolute;
    left: -40px; /* Adjust as needed */
}

.e-workflow-node-floating-button-right {
    position: absolute;
    right: -40px; /* Adjust as needed */
}