import React from 'react';
import Tooltip from 'carbon-react/esm/components/tooltip';
import { Controls } from 'reactflow';
import type { LocalizeFunction } from '@sage/xtrem-shared';

function WorkflowControlButton({
    key,
    onClick,
    title,
    ariaLabel,
    testId,
    disabled = false,
    children,
    dataPendoid,
}: {
    key: string;
    onClick: () => void;
    title: string;
    ariaLabel: string;
    testId: string;
    disabled?: boolean;
    children: React.ReactNode;
    dataPendoid?: string;
}): React.ReactElement {
    return (
        <Tooltip message={title}>
            <button
                disabled={disabled}
                key={key}
                onClick={onClick}
                type="button"
                className="react-flow__controls-button"
                title={title}
                aria-label={ariaLabel}
                data-testid={testId}
                data-pendoid={dataPendoid}
            >
                {children}
            </button>
        </Tooltip>
    );
}

export function WorkflowControlButtons({
    zoomIn,
    zoomOut,
    fitView,
    onExpand,
    onMinimize,
    onUndo,
    onRedo,
    isUndoDisabled,
    isRedoDisabled,
    expansionDialogControl,
    localize,
}: {
    zoomIn: () => void;
    zoomOut: () => void;
    fitView: (options?: { padding: number }) => void;
    onExpand: () => void;
    onMinimize: () => void;
    onUndo: () => void;
    onRedo: () => void;
    isUndoDisabled: boolean;
    isRedoDisabled: boolean;
    expansionDialogControl: any;
    localize: LocalizeFunction;
}): React.ReactElement {
    const WORKFLOW_LOCALIZATION = React.useMemo(
        () => ({
            zoomIn: localize('@sage/xtrem-ui/workflow-zoom-in', 'Zoom in'),
            zoomOut: localize('@sage/xtrem-ui/workflow-zoom-out', 'Zoom out'),
            fitView: localize('@sage/xtrem-ui/workflow-fit-view', 'Fit view to screen'),
            expand: localize('@sage/xtrem-ui/workflow-expand', 'Expand'),
            collapse: localize('@sage/xtrem-ui/workflow-collapse', 'Collapse'),
            undo: localize('@sage/xtrem-ui/workflow-undo', 'Undo'),
            redo: localize('@sage/xtrem-ui/workflow-redo', 'Redo'),
        }),
        [localize],
    );

    return (
        <Controls showFitView={false} showZoom={false} showInteractive={false}>
            <WorkflowControlButton
                key="zoom-in"
                onClick={zoomIn}
                title={WORKFLOW_LOCALIZATION.zoomIn}
                ariaLabel={WORKFLOW_LOCALIZATION.zoomIn}
                testId="e-workflow-zoom-in-button"
                dataPendoid="workflow-zoom-in"
            >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
                    <path d="M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z" />
                </svg>
            </WorkflowControlButton>
            <WorkflowControlButton
                key="zoom-out"
                onClick={zoomOut}
                title={WORKFLOW_LOCALIZATION.zoomOut}
                ariaLabel={WORKFLOW_LOCALIZATION.zoomOut}
                testId="e-workflow-zoom-out-button"
                dataPendoid="workflow-zoom-out"
            >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 5">
                    <path d="M0 0h32v4.2H0z" />
                </svg>
            </WorkflowControlButton>
            <WorkflowControlButton
                key="fit-view"
                onClick={() => fitView({ padding: 0.3 })}
                title={WORKFLOW_LOCALIZATION.fitView}
                ariaLabel={WORKFLOW_LOCALIZATION.fitView}
                testId="e-workflow-fit-view-button"
                dataPendoid="workflow-fit-view"
            >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 30">
                    <path d="M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z" />
                </svg>
            </WorkflowControlButton>
            {!expansionDialogControl && (
                <WorkflowControlButton
                    key="expand"
                    onClick={onExpand}
                    title={WORKFLOW_LOCALIZATION.expand}
                    ariaLabel={WORKFLOW_LOCALIZATION.expand}
                    testId="e-workflow-expand-button"
                    dataPendoid="workflow-expand"
                >
                    <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="expand">
                            <path
                                id="Vector"
                                d="M2.85616 1.0004C2.56676 1.00071 2.29168 1.12636 2.10197 1.34491C1.91226 1.56346 1.82653 1.85348 1.86691 2.14005V6.01017C1.86181 6.3708 2.05128 6.70625 2.36278 6.88806C2.67428 7.06986 3.05954 7.06986 3.37103 6.88806C3.68253 6.70625 3.87201 6.3708 3.86691 6.01017V4.42423L7.65988 8.2172C7.91069 8.47844 8.28314 8.58367 8.63358 8.49232C8.98403 8.40096 9.2577 8.12729 9.34906 7.77685C9.44041 7.4264 9.33518 7.05395 9.07394 6.80314L5.28097 3.01017H6.86691C7.22754 3.01527 7.56299 2.82579 7.74479 2.51429C7.9266 2.2028 7.9266 1.81754 7.74479 1.50604C7.56299 1.19454 7.22754 1.00507 6.86691 1.01017H2.98995C2.94563 1.00392 2.90093 1.00066 2.85616 1.0004ZM18.8474 1.0004C18.8104 1.00162 18.7736 1.00488 18.737 1.01017H14.8669C14.5063 1.00507 14.1708 1.19454 13.989 1.50604C13.8072 1.81754 13.8072 2.2028 13.989 2.51429C14.1708 2.82579 14.5063 3.01527 14.8669 3.01017H16.4528L12.6599 6.80314C12.3986 7.05395 12.2934 7.4264 12.3848 7.77685C12.4761 8.12729 12.7498 8.40096 13.1002 8.49232C13.4507 8.58367 13.8231 8.47844 14.0739 8.2172L17.8669 4.42423V6.01017C17.8618 6.3708 18.0513 6.70625 18.3628 6.88806C18.6743 7.06986 19.0595 7.06986 19.371 6.88806C19.6825 6.70625 19.872 6.3708 19.8669 6.01017V2.13321C19.906 1.84242 19.8153 1.54915 19.6191 1.33107C19.4228 1.11299 19.1407 0.992091 18.8474 1.0004ZM8.34738 11.5004C8.08758 11.5081 7.841 11.6167 7.65988 11.8031L3.86691 15.5961V14.0102C3.87201 13.6495 3.68253 13.3141 3.37103 13.1323C3.05954 12.9505 2.67428 12.9505 2.36278 13.1323C2.05128 13.3141 1.86181 13.6495 1.86691 14.0102V17.8871C1.82544 18.1971 1.93135 18.5086 2.15315 18.729C2.37494 18.9495 2.68709 19.0535 2.99679 19.0102H6.86691C7.22754 19.0153 7.56299 18.8258 7.74479 18.5143C7.9266 18.2028 7.9266 17.8175 7.74479 17.506C7.56299 17.1945 7.22754 17.0051 6.86691 17.0102H5.28097L9.07394 13.2172C9.3697 12.9297 9.45863 12.4901 9.29787 12.1103C9.13712 11.7304 8.75966 11.4882 8.34738 11.5004ZM13.3562 11.5004C12.9494 11.5009 12.5835 11.7477 12.4306 12.1246C12.2778 12.5015 12.3684 12.9335 12.6599 13.2172L16.4528 17.0102H14.8669C14.5063 17.0051 14.1708 17.1945 13.989 17.506C13.8072 17.8175 13.8072 18.2028 13.989 18.5143C14.1708 18.8258 14.5063 19.0153 14.8669 19.0102H18.7439C19.0538 19.0516 19.3653 18.9457 19.5858 18.7239C19.8062 18.5021 19.9102 18.19 19.8669 17.8803V14.0102C19.872 13.6495 19.6825 13.3141 19.371 13.1323C19.0595 12.9505 18.6743 12.9505 18.3628 13.1323C18.0513 13.3141 17.8618 13.6495 17.8669 14.0102V15.5961L14.0739 11.8031C13.8854 11.6094 13.6265 11.5002 13.3562 11.5004Z"
                                fill="#335B70"
                            />
                        </g>
                    </svg>
                </WorkflowControlButton>
            )}
            {expansionDialogControl && (
                <WorkflowControlButton
                    key="collapse"
                    onClick={onMinimize}
                    title={WORKFLOW_LOCALIZATION.collapse}
                    ariaLabel={WORKFLOW_LOCALIZATION.collapse}
                    testId="e-workflow-collapse-button"
                    dataPendoid="workflow-collapse"
                >
                    <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M2.00213 1.00043C1.8036 1.00068 1.60965 1.06012 1.44506 1.17115C1.28046 1.28219 1.1527 1.43978 1.07808 1.6238C1.00346 1.80782 0.985387 2.00991 1.02616 2.20426C1.06692 2.3986 1.16469 2.57638 1.30696 2.71488L5.59303 7.00197H3.01125C2.87895 7.00009 2.7476 7.02454 2.62483 7.07389C2.50206 7.12323 2.39032 7.19649 2.2961 7.28941C2.20188 7.38233 2.12706 7.49305 2.076 7.61514C2.02493 7.73723 1.99864 7.86825 1.99864 8.0006C1.99864 8.13294 2.02493 8.26396 2.076 8.38605C2.12706 8.50814 2.20188 8.61886 2.2961 8.71178C2.39032 8.8047 2.50206 8.87796 2.62483 8.9273C2.7476 8.97665 2.87895 9.0011 3.01125 8.99923H7.88036C8.03302 9.01965 8.18833 9.00448 8.33416 8.95489C8.47998 8.9053 8.61235 8.82264 8.72093 8.71338C8.8295 8.60411 8.91134 8.47121 8.96003 8.32505C9.00872 8.1789 9.02295 8.02346 9.0016 7.87089V3.00745C9.00347 2.87512 8.97903 2.74373 8.9297 2.62093C8.88036 2.49813 8.80712 2.38637 8.71423 2.29213C8.62133 2.19789 8.51064 2.12305 8.38858 2.07197C8.26652 2.0209 8.13552 1.99459 8.00321 1.99459C7.8709 1.99459 7.73991 2.0209 7.61785 2.07197C7.49578 2.12305 7.38509 2.19789 7.2922 2.29213C7.1993 2.38637 7.12606 2.49813 7.07672 2.62093C7.02739 2.74373 7.00295 2.87512 7.00482 3.00745V5.58984L2.71875 1.30275C2.6256 1.20697 2.51417 1.13086 2.39108 1.07893C2.26799 1.02701 2.13573 1.00031 2.00213 1.00043ZM17.9676 1.00043C17.7083 1.00816 17.4621 1.1166 17.2812 1.30275L12.9952 5.58984V3.00745C12.997 2.87512 12.9726 2.74373 12.9233 2.62093C12.8739 2.49813 12.8007 2.38637 12.7078 2.29213C12.6149 2.19789 12.5042 2.12305 12.3821 2.07197C12.2601 2.0209 12.1291 1.99459 11.9968 1.99459C11.8645 1.99459 11.7335 2.0209 11.6114 2.07197C11.4894 2.12305 11.3787 2.19789 11.2858 2.29213C11.1929 2.38637 11.1196 2.49813 11.0703 2.62093C11.021 2.74373 10.9965 2.87512 10.9984 3.00745V7.87772C10.978 8.03041 10.9931 8.18576 11.0427 8.33162C11.0923 8.47748 11.1749 8.60988 11.2842 8.71848C11.3934 8.82709 11.5263 8.90894 11.6724 8.95764C11.8185 9.00635 11.9739 9.02058 12.1265 8.99923H16.9887C17.121 9.0011 17.2524 8.97665 17.3752 8.9273C17.4979 8.87796 17.6097 8.8047 17.7039 8.71178C17.7981 8.61886 17.8729 8.50814 17.924 8.38605C17.9751 8.26396 18.0014 8.13294 18.0014 8.0006C18.0014 7.86825 17.9751 7.73723 17.924 7.61514C17.8729 7.49305 17.7981 7.38233 17.7039 7.28941C17.6097 7.19649 17.4979 7.12323 17.3752 7.07389C17.2524 7.02454 17.121 7.00009 16.9887 7.00197H14.407L18.693 2.71488C18.8373 2.57461 18.9358 2.39402 18.9757 2.19677C19.0156 1.99952 18.995 1.79481 18.9166 1.60948C18.8382 1.42415 18.7056 1.26684 18.5363 1.15816C18.367 1.04948 18.1687 0.994502 17.9676 1.00043ZM7.98371 10.9867C7.94683 10.9879 7.91005 10.9912 7.87354 10.9965H3.01125C2.87895 10.9946 2.7476 11.0191 2.62483 11.0684C2.50206 11.1178 2.39032 11.191 2.2961 11.2839C2.20188 11.3768 2.12706 11.4876 2.076 11.6097C2.02493 11.7317 1.99864 11.8628 1.99864 11.9951C1.99864 12.1275 2.02493 12.2585 2.076 12.3806C2.12706 12.5027 2.20188 12.6134 2.2961 12.7063C2.39032 12.7992 2.50206 12.8725 2.62483 12.9218C2.7476 12.9712 2.87895 12.9956 3.01125 12.9937H5.59303L1.30696 17.2808C1.21114 17.3729 1.13464 17.4831 1.08194 17.605C1.02924 17.727 1.0014 17.8582 1.00005 17.9911C0.9987 18.124 1.02387 18.2558 1.07407 18.3788C1.12428 18.5018 1.19852 18.6135 1.29245 18.7075C1.38638 18.8014 1.4981 18.8757 1.62108 18.9259C1.74406 18.9761 1.87582 19.0013 2.00865 18.9999C2.14148 18.9986 2.2727 18.9707 2.39463 18.918C2.51656 18.8653 2.62675 18.7888 2.71875 18.693L7.00482 14.4059V16.9883C7.00295 17.1206 7.02739 17.252 7.07672 17.3748C7.12606 17.4976 7.1993 17.6093 7.2922 17.7036C7.38509 17.7978 7.49578 17.8727 7.61785 17.9237C7.73991 17.9748 7.8709 18.0011 8.00321 18.0011C8.13552 18.0011 8.26652 17.9748 8.38858 17.9237C8.51064 17.8727 8.62133 17.7978 8.71423 17.7036C8.80712 17.6093 8.88036 17.4976 8.9297 17.3748C8.97903 17.252 9.00347 17.1206 9.0016 16.9883V12.118C9.02087 11.9745 9.00872 11.8286 8.96599 11.6902C8.92327 11.5519 8.85099 11.4246 8.75417 11.3169C8.65735 11.2093 8.53829 11.1241 8.40526 11.0671C8.27222 11.01 8.12839 10.9826 7.98371 10.9867ZM11.9861 10.9867C11.8432 10.9869 11.7021 11.0177 11.5722 11.077C11.4423 11.1364 11.3267 11.2229 11.2331 11.3308C11.1395 11.4386 11.0701 11.5653 11.0296 11.7023C10.9891 11.8393 10.9785 11.9834 10.9984 12.1248V16.9883C10.9965 17.1206 11.021 17.252 11.0703 17.3748C11.1196 17.4976 11.1929 17.6093 11.2858 17.7036C11.3787 17.7978 11.4894 17.8727 11.6114 17.9237C11.7335 17.9748 11.8645 18.0011 11.9968 18.0011C12.1291 18.0011 12.2601 17.9748 12.3821 17.9237C12.5042 17.8727 12.6149 17.7978 12.7078 17.7036C12.8007 17.6093 12.8739 17.4976 12.9233 17.3748C12.9726 17.252 12.997 17.1206 12.9952 16.9883V14.4059L17.2812 18.693C17.3732 18.7888 17.4834 18.8653 17.6054 18.918C17.7273 18.9707 17.8585 18.9986 17.9913 18.9999C18.1242 19.0013 18.2559 18.9761 18.3789 18.9259C18.5019 18.8757 18.6136 18.8014 18.7075 18.7075C18.8015 18.6135 18.8757 18.5018 18.9259 18.3788C18.9761 18.2558 19.0013 18.124 18.9999 17.9911C18.9986 17.8582 18.9708 17.727 18.9181 17.605C18.8654 17.4831 18.7889 17.3729 18.693 17.2808L14.407 12.9937H16.9887C17.121 12.9956 17.2524 12.9712 17.3752 12.9218C17.4979 12.8725 17.6097 12.7992 17.7039 12.7063C17.7981 12.6134 17.8729 12.5027 17.924 12.3806C17.9751 12.2585 18.0014 12.1275 18.0014 11.9951C18.0014 11.8628 17.9751 11.7317 17.924 11.6097C17.8729 11.4876 17.7981 11.3768 17.7039 11.2839C17.6097 11.191 17.4979 11.1178 17.3752 11.0684C17.2524 11.0191 17.121 10.9946 16.9887 10.9965H12.1196C12.0754 10.9902 12.0307 10.987 11.9861 10.9867Z"
                            fill="#335B70"
                        />
                    </svg>
                </WorkflowControlButton>
            )}
            <WorkflowControlButton
                key="undo"
                onClick={onUndo}
                disabled={isUndoDisabled}
                title={WORKFLOW_LOCALIZATION.undo}
                ariaLabel={WORKFLOW_LOCALIZATION.undo}
                testId="e-workflow-undo-button"
                dataPendoid="workflow-undo"
            >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 30">
                    <g transform="scale(0.031 0.031)">
                        <path d="M312.435 641.319c9.706 9.706 22.391 14.547 35.101 14.547s25.395-4.841 35.101-14.547c19.388-19.388 19.388-50.815 0-70.203l-163.492-163.492h401.458c123.203 0 223.418 100.215 223.418 223.418s-100.215 223.418-223.418 223.418h-198.594c-27.406 0-49.648 22.243-49.648 49.648s22.243 49.648 49.648 49.648h198.594c177.94 0 322.715-144.775 322.715-322.715s-144.775-322.715-322.715-322.715h-401.458l163.492-163.492c19.388-19.388 19.388-50.815 0-70.203s-50.815-19.388-70.203 0l-248.242 248.242c-19.388 19.388-19.388 50.815 0 70.203l248.242 248.242z" />
                    </g>
                </svg>
            </WorkflowControlButton>
            <WorkflowControlButton
                key="redo"
                onClick={onRedo}
                disabled={isRedoDisabled}
                title={WORKFLOW_LOCALIZATION.redo}
                ariaLabel={WORKFLOW_LOCALIZATION.redo}
                testId="e-workflow-redo-button"
                dataPendoid="workflow-redo"
            >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 30">
                    <g transform="scale(0.031 0.031)">
                        <path d="M372.367 953.757h186.182c27.406 0 49.648-22.243 49.648-49.648s-22.243-49.648-49.648-49.648h-186.182c-123.203 0-223.418-100.215-223.418-223.418s100.215-223.418 223.418-223.418h401.458l-163.492 163.492c-19.388 19.388-19.388 50.815 0 70.203 9.706 9.706 22.391 14.547 35.101 14.547s25.395-4.841 35.101-14.547l248.242-248.242c19.388-19.388 19.388-50.815 0-70.203l-248.242-248.242c-19.388-19.388-50.815-19.388-70.203 0s-19.388 50.815 0 70.203l163.492 163.492h-401.458c-177.94 0-322.715 144.774-322.715 322.715s144.775 322.715 322.715 322.715z" />
                    </g>
                </svg>
            </WorkflowControlButton>
        </Controls>
    );
}
