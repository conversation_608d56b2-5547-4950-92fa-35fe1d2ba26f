import React from 'react';
import { localize } from '../../../service/i18n-service';

export function ConditionNodeLabel({
    sourceX,
    sourceY,
    sourceHandleId,
}: {
    sourceX: number;
    sourceY: number;
    sourceHandleId: string;
}): React.ReactElement {
    const isTruePath = sourceHandleId === 'out-true';
    return (
        <div
            style={{
                position: 'absolute',
                background: 'transparent',
                padding: 10,
                fontSize: 12,
                fontWeight: 700,
                transform: `translate(-50%, 0%) translate(${sourceX}px,${sourceY}px)`,
                zIndex: 1,
            }}
            className="nodrag nopan"
        >
            <span className="e-workflow-condition-path-label">
                {isTruePath
                    ? localize('@sage/xtrem-ui/workflow-component-edge-true-path', 'If true')
                    : localize('@sage/xtrem-ui/workflow-component-edge-false-path', 'else')}
            </span>
        </div>
    );
}
