import { <PERSON><PERSON>, Position } from 'reactflow';
import type { NodeProps } from 'reactflow';
import * as React from 'react';
import type { XtremAppState } from '../../../redux/state';
import { getTextForLocale, type WorkflowNode } from '@sage/xtrem-shared';
import Icon from 'carbon-react/esm/components/icon';
import IconButton from 'carbon-react/esm/components/icon-button';
import { openPageDialog } from '../../../service/dialog-service';
import {
    hasEdgeConnectedToNode,
    removeTransientNodeDataProperties,
    useSubsequentWorkflowNodes,
    useWorkflowNodeVariables,
} from './workflow-component-utils';
import type { DefaultDataType, WorkflowFieldValue, WorkflowLogEntry } from './workflow-types';
import { resolveDetailedIcon } from '../../../utils/detailed-icons-utils';
import { WorkflowContext } from './workflow-context-provider';
import Button from 'carbon-react/esm/components/button';
import SplitButton from 'carbon-react/esm/components/split-button';
import { localize } from '../../../service/i18n-service';
import { getStore } from '../../../redux';
import { ConditionNodeLabel } from './condition-node-label';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import { useFieldValue } from '../../../utils/hooks/effects/use-set-field-value';
import * as tokens from '@sage/design-tokens/js/base/common';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

interface CustomNodeProps extends NodeProps<DefaultDataType> {}

export function CustomNode({ data, isConnectable, id }: CustomNodeProps): React.ReactElement | null {
    const context = React.useContext(WorkflowContext);
    const subsequentNodes = useSubsequentWorkflowNodes(id, context?.screenId, context?.elementId);
    const locale = getStore().getState().applicationContext?.locale || 'base';
    const { inputVariables, oldRootPaths } = useWorkflowNodeVariables(id, context?.screenId, context?.elementId);

    const fieldValue = useFieldValue<WorkflowFieldValue>(context?.screenId, context?.elementId);
    const [hasOutgoingTrueEdge, hasOutgoingFalseEdge] = React.useMemo(() => {
        const trueBranch = hasEdgeConnectedToNode(id, fieldValue?.edges || [], 'out-true');
        const falseBranch = hasEdgeConnectedToNode(id, fieldValue?.edges || [], 'out-false');
        return [trueBranch, falseBranch];
    }, [id, fieldValue?.edges]);

    const workflowNodeType = useDeepEqualSelector<XtremAppState, WorkflowNode | null>(
        s => s.workflowNodes?.find(n => n.key === data.type) || null,
    );

    const eventLog: WorkflowLogEntry | undefined = React.useMemo(() => {
        return data.eventLog;
    }, [data.eventLog]);

    const errorMessage = React.useMemo(() => {
        if (data.message) {
            return data.message;
        }
        if (eventLog?.event === 'error') {
            return eventLog?.message;
        }
        return null;
    }, [data.message, eventLog?.event, eventLog?.message]);

    const headerLabel = React.useMemo((): string => {
        if (workflowNodeType?.type === 'condition') {
            return localize('@sage/xtrem-ui/workflow-component-header-label-condition', 'Condition');
        }

        if (workflowNodeType?.type === 'event') {
            return localize('@sage/xtrem-ui/workflow-component-header-label-start', 'Starts when');
        }

        return localize('@sage/xtrem-ui/workflow-component-header-label-action', 'Do');
    }, [workflowNodeType?.type]);

    const onConfigurationOpen = React.useCallback(
        async (isReadOnly: boolean) => {
            if (!workflowNodeType?.configurationPage || !context?.onNodeDataChange) {
                return;
            }

            try {
                const result = await openPageDialog<any>(
                    workflowNodeType.configurationPage,
                    {
                        inputVariables: inputVariables as any,
                        oldRootPaths: oldRootPaths as any,
                        isReadOnly,
                        errorMessage,
                    },
                    { values: removeTransientNodeDataProperties(data), rightAligned: true, size: 'extra-large' },
                );

                context.onNodeDataChange(id, { ...result, type: data.type });
            } catch (e) {
                // intentionally left empty
            }
        },
        [workflowNodeType?.configurationPage, inputVariables, oldRootPaths, data, context, id, errorMessage],
    );

    const onInsertBelowButtonClick = React.useCallback(() => {
        context?.onNodeBelowNode(id);
    }, [context, id]);

    const onInsertBelowConditionButtonClick = React.useCallback(() => {
        context?.onNodeBelowNode(id, 'condition');
    }, [context, id]);

    const onInsertBelowActionsButtonClick = React.useCallback(() => {
        context?.onNodeBelowNode(id, 'action');
    }, [context, id]);

    const onInsertTrueButtonClick = React.useCallback(() => {
        context?.onNodeBelowNode(id, undefined, 'out-true');
    }, [context, id]);

    const onInsertFalseButtonClick = React.useCallback(() => {
        context?.onNodeBelowNode(id, undefined, 'out-false');
    }, [context, id]);

    if (!workflowNodeType) {
        return null;
    }

    return (
        <div className="e-workflow-node-wrapper">
            {workflowNodeType.type === 'condition' && (
                <div className="e-workflow-node-floating-buttons">
                    {data.ifTrueBranch && !hasOutgoingTrueEdge && (
                        <ButtonMinor
                            data-pendoid="workflow-node-add-true-branch"
                            iconType="plus"
                            buttonType="secondary"
                            size="small"
                            onClick={onInsertTrueButtonClick}
                            iconTooltipMessage={localize('@sage/xtrem-ui/workflow-component-add-step', 'Add step')}
                            aria-label={localize('@sage/xtrem-ui/workflow-component-add-step', 'Add step')}
                            className="e-workflow-node-floating-button-left e-workflow-add-node-on-edge-button"
                        />
                    )}
                    {data.ifFalseBranch && !hasOutgoingFalseEdge && (
                        <ButtonMinor
                            data-pendoid="workflow-node-add-false-branch"
                            iconType="plus"
                            buttonType="secondary"
                            size="small"
                            onClick={onInsertFalseButtonClick}
                            iconTooltipMessage={localize('@sage/xtrem-ui/workflow-component-add-step', 'Add step')}
                            aria-label={localize('@sage/xtrem-ui/workflow-component-add-step', 'Add step')}
                            className="e-workflow-node-floating-button-right e-workflow-add-node-on-edge-button"
                        />
                    )}
                </div>
            )}
            <Handle type="target" position={Position.Top} id="in" isConnectable={isConnectable}>
                <div className="e-workflow-node-header">
                    <div
                        className="e-workflow-node-header-content"
                        style={{
                            backgroundColor: 'var(--colorsSemanticNeutral500)',
                            color: 'var(--colorsYang100)',
                        }}
                    >
                        {headerLabel}
                    </div>
                </div>
            </Handle>
            <div
                className={`e-workflow-node e-workflow-node-${workflowNodeType.type} e-workflow-node-${workflowNodeType.key} ${eventLog?.event ? `e-workflow-node-event-${eventLog.event}` : ''}`}
                data-testid={`e-workflow-node e-workflow-node-${workflowNodeType.type} e-workflow-node-${workflowNodeType.key}`}
                data-nodeid={id}
            >
                {workflowNodeType.type !== 'condition' && (
                    <div className="e-workflow-node-icon-wrapper" style={{ backgroundColor: workflowNodeType.color }}>
                        <div
                            className="e-workflow-node-icon"
                            style={{
                                mask: `url(${resolveDetailedIcon(workflowNodeType.icon)}) no-repeat left center`,
                                maskSize: '24px 24px',
                                backgroundColor: 'var(--colorsUtilityYang100)',
                            }}
                        />
                    </div>
                )}
                <div className={`e-workflow-node-body e-workflow-node-${workflowNodeType.type}-body`}>
                    <div className="e-workflow-node-title">
                        <div className="e-workflow-node-title-label">
                            {getTextForLocale(data.localizedTitle, locale) || data.title || workflowNodeType.title}
                        </div>
                        {workflowNodeType.configurationPage && !context?.isReadOnly && (
                            <IconButton
                                data-pendoid="workflow-node-open-configuration"
                                onClick={() => onConfigurationOpen(false)}
                            >
                                <Icon
                                    ml={workflowNodeType.type !== 'condition' ? '8px' : undefined}
                                    type="edit"
                                    color="--colorsUtilityYin090"
                                />
                            </IconButton>
                        )}
                    </div>
                    {data.subtitle && <div className="e-workflow-node-subtitle">{data.subtitle}</div>}
                    {data.details && <div className="e-workflow-node-details">{data.details}</div>}
                </div>

                {errorMessage && (
                    <div>
                        <Icon
                            type="error"
                            tooltipMessage={errorMessage}
                            ariaLabel={errorMessage}
                            color={tokens.colorsSemanticNegative500}
                            tooltipBgColor={tokens.colorsSemanticNegative500}
                            tooltipFontColor={tokens.colorsYang100}
                        />
                    </div>
                )}

                {(eventLog || context?.isReadOnly) && (
                    <div className="e-workflow-node-event-view-details" onClick={() => onConfigurationOpen(true)}>
                        <Icon type="view" />
                    </div>
                )}

                {workflowNodeType.type === 'condition' && (
                    <>
                        {(data.ifTrueBranch || hasOutgoingTrueEdge) && (
                            <ConditionNodeLabel sourceHandleId="out-true" sourceX={-55} sourceY={-40} />
                        )}
                        {(data.ifFalseBranch || hasOutgoingFalseEdge) && (
                            <ConditionNodeLabel sourceHandleId="out-false" sourceX={250} sourceY={-40} />
                        )}
                        <Handle type="source" position={Position.Right} id="out-false" isConnectable={isConnectable} />
                    </>
                )}
                {workflowNodeType.type === 'condition' ? (
                    // We need to always show Handle (dot) even if the branch is not selected, because if the user edits it it would be bugged.
                    <Handle type="source" position={Position.Left} id="out-true" isConnectable={isConnectable} />
                ) : (
                    <Handle type="source" position={Position.Bottom} id="out" isConnectable={isConnectable} />
                )}
            </div>
            {subsequentNodes.length === 0 && workflowNodeType?.type !== 'condition' && !context?.isReadOnly && (
                <div className="e-workflow-node-add-button">
                    <SplitButton
                        data-pendoid="workflow-node-add-step"
                        text={localize('@sage/xtrem-ui/workflow-component-add-step', 'Add step')}
                        size="small"
                        iconType="plus"
                        onClick={onInsertBelowButtonClick}
                    >
                        <Button
                            data-pendoid="workflow-node-add-action"
                            iconType="tick"
                            onClick={onInsertBelowActionsButtonClick}
                        >
                            {localize('@sage/xtrem-ui/workflow-component-add-action', 'Add action')}
                        </Button>
                        <Button
                            data-pendoid="workflow-node-add-condition"
                            iconType="split"
                            onClick={onInsertBelowConditionButtonClick}
                        >
                            {localize('@sage/xtrem-ui/workflow-component-add-condition', 'Add condition')}
                        </Button>
                    </SplitButton>
                </div>
            )}
        </div>
    );
}
