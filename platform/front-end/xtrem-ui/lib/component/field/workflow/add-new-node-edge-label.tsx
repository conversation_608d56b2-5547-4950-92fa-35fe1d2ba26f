import React from 'react';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import { localize } from '../../../service/i18n-service';
import { WorkflowContext } from './workflow-context-provider';

export function AddNewNodeEdgeLabel({
    edgeId,
    centerX,
    centerY,
}: {
    edgeId: string;
    centerX: number;
    centerY: number;
}): React.ReactElement {
    const context = React.useContext(WorkflowContext);

    const onAddClick = React.useCallback(
        (ev: React.MouseEvent) => {
            ev.stopPropagation();
            ev.preventDefault();
            context?.onNodeInsertToEdge(edgeId);
        },
        [context, edgeId],
    );

    const [isAddButtonDisplayed, setIsAddButtonDisplayed] = React.useState(false);
    const onEdgeMouseEnter = React.useCallback(() => {
        setIsAddButtonDisplayed(true);
    }, []);

    const onEdgeMouseLeave = React.useCallback(() => {
        setIsAddButtonDisplayed(false);
    }, []);

    return (
        <div
            style={{
                position: 'absolute',
                background: 'transparent',
                padding: 10,
                color: '#ff5050',
                fontSize: 12,
                fontWeight: 700,
                transform: `translate(-50%, -50%) translate(${centerX}px,${centerY}px)`,
                pointerEvents: 'all',
            }}
            className="nodrag nopan"
        >
            <div onMouseEnter={onEdgeMouseEnter} onMouseLeave={onEdgeMouseLeave} style={{ padding: '32px' }}>
                {isAddButtonDisplayed && (
                    <ButtonMinor
                        onClick={onAddClick}
                        className="e-workflow-add-node-on-edge-button"
                        buttonType="secondary"
                        size="small"
                        iconType="plus"
                        iconTooltipMessage={localize('@sage/xtrem-ui/workflow-component-add-step', 'Add step')}
                        aria-label={localize('@sage/xtrem-ui/workflow-component-add-step', 'Add step')}
                    />
                )}
            </div>
        </div>
    );
}
