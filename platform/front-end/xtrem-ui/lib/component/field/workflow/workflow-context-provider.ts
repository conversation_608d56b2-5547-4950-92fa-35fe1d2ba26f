import { createContext } from 'react';

export interface WorkflowContextValue {
    screenId: string;
    elementId: string;
    isReadOnly: boolean;
    onNodeDataChange: (nodeId: string, newData: any) => void;
    onNodeInsertToEdge: (edgeId: string) => void;
    onNodeBelowNode: (nodeId: string, type?: string, handleId?: string) => void;
}

export const WorkflowContext = createContext<WorkflowContextValue | null>(null);
