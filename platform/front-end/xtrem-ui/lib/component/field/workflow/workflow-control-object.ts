import type { FilterParameter } from '@sage/xtrem-ui-components';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { WorkflowDecoratorProperties, WorkflowLogEntry } from './workflow-types';

export class WorkflowControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.Workflow,
    FieldComponentProps<FieldKey.Workflow>
> {
    @ControlObjectProperty<WorkflowDecoratorProperties<CT>, WorkflowControlObject<CT>>()
    /** Document editor parameters */
    parameters: FilterParameter[];

    @ControlObjectProperty<WorkflowDecoratorProperties<CT>, WorkflowControlObject<CT>>()
    /** The logs of the workflow */
    eventLogs: WorkflowLogEntry[];
}
