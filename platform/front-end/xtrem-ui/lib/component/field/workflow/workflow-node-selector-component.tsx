import { SelectionCard } from '@sage/xtrem-ui-components';
import * as React from 'react';
import { resolveDetailedIcon } from '../../../utils/detailed-icons-utils';
import type { WorkflowNode } from '@sage/xtrem-shared';
import { localize } from '../../../service/i18n-service';
import Search from 'carbon-react/esm/components/search';

export interface WorkflowNodeSelectorComponentProps {
    value: string | null;
    onChange: (newValue: string) => void;
    workflowNodes: WorkflowNode[];
}

export function WorkflowNodeSelectorComponent({
    value,
    onChange,
    workflowNodes,
}: WorkflowNodeSelectorComponentProps): React.ReactElement | null {
    const [filterValue, setFilterValue] = React.useState<string>('');
    return (
        <div>
            <Search
                placeholder={localize('@sage/xtrem-ui/selection-card-filter-placeholder', 'Filter...')}
                value={filterValue}
                onChange={e => setFilterValue(e.target.value)}
                mb="8px"
                data-testid="e-selection-card-filter"
            />
            <div className="selection-card-container">
                {workflowNodes
                    .sort((a, b) => a.title.localeCompare(b.title))
                    .filter(n => !filterValue || n.title.toLowerCase().includes(filterValue.toLowerCase()))
                    .map(n => {
                        return (
                            <SelectionCard
                                key={n.key}
                                _id={n.key}
                                isSelected={value === n.key}
                                title={n.title}
                                icon={resolveDetailedIcon(n.icon)}
                                description={n.description}
                                onClick={(): void => onChange(n.key)}
                            />
                        );
                    })}
            </div>
        </div>
    );
}
