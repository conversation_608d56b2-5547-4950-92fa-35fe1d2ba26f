import type { Dict, WorkflowError, WorkflowVariable } from '@sage/xtrem-shared';
import type { FilterParameter } from '@sage/xtrem-ui-components';
import type { Edge, Node } from 'reactflow';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { OverrideDecoratorProperties } from '../../../utils/decorator-utils';
import type { ValueOrCallback } from '../../../utils/types';
import type { BlockControlObject, SectionControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type { Changeable, Clickable, ExtensionField, HasParent } from '../traits';

export interface DefaultDataType extends Dict<any> {
    type?: string;
    subtitle?: string;
    details?: string;
    variables?: WorkflowVariable[];
}

export interface WorkflowFieldValue {
    edges: Edge[];
    nodes: Node<{ screenId?: string; elementId?: string; [key: string]: any }>[];
}

export interface WorkflowDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends WorkflowProperties<CT>,
        Changeable<CT>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>> {
    /** The parameters of the form editor */
    parameters?: ValueOrCallback<CT, FilterParameter[]>;

    validation?:
        | ((this: CT, value: WorkflowFieldValue) => WorkflowError[] | undefined)
        | ((this: CT, value: WorkflowFieldValue) => Promise<WorkflowError[] | undefined>);
}

export interface WorkflowExtensionDecoratorProperties<CT extends ScreenExtension<CT>>
    extends OverrideDecoratorProperties<WorkflowDecoratorProperties<Extend<CT>>> {}

export interface WorkflowProperties<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldProperties<CT> {
    /** The logs of the workflow */
    eventLogs?: WorkflowLogEntry[];
}

export type WorkflowComponentProperties = BaseEditableComponentProperties<WorkflowProperties, WorkflowFieldValue>;

export type WorkflowLogEvent = 'running' | 'success' | 'error' | 'suspended' | 'shutDown';

export interface WorkflowLogEntry {
    timestamp: string;
    stepId: string;
    event: WorkflowLogEvent;
    message?: string;
}
