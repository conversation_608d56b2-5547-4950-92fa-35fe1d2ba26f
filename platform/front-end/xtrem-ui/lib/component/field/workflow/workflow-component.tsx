import * as React from 'react';
import { connect, useDispatch } from 'react-redux';
import { mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { WorkflowComponentProperties, WorkflowFieldValue, WorkflowLogEntry } from './workflow-types';
import type { WorkflowError, WorkflowNode } from '@sage/xtrem-shared';
import type {
    NodePositionChange,
    NodeRemoveChange,
    OnConnectEnd,
    OnConnectStartParams,
    OnEdgesChange,
    OnNodesChange,
} from 'reactflow';
import ReactFlow, { useReactFlow, ReactFlowProvider, Background, useNodesState, useEdgesState } from 'reactflow';
import { CustomNode } from './custom-node';
import { CarbonWrapper } from '../carbon-wrapper';
import { isEqual, isNil } from 'lodash';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { useCompare } from '../../../utils/hooks/effects/use-compare';
import {
    changeEventHandler,
    createNewEdge,
    createNewNode,
    edgeStyle,
    isSingleEdgeRemoval,
    removeChildNodesAndEdgesFromStartPoint,
    removeTransientEdgeDataPropertiesFromEdges,
    removeTransientNodeDataPropertiesFromNodes,
    roundToNearestTwenty,
} from './workflow-component-utils';
import { AddWorkflowNodeDialog } from './add-workflow-node-dialog';
import { loadWorkflowNodes } from '../../../redux/actions/workflow-nodes-actions';
import { CustomEdge } from './custom-edge';
import Typography from 'carbon-react/esm/components/typography';
import { isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import type { WorkflowContextValue } from './workflow-context-provider';
import { WorkflowContext } from './workflow-context-provider';
import { localize } from '../../../service/i18n-service';
import { findAncestorDatasetProperty } from '../../../utils/dom';
import type { DialogControl } from '../../../service/dialog-service';
import { confirmationDialog } from '../../../service/dialog-service';
import type * as xtremRedux from '../../../redux';
import Button from 'carbon-react/esm/components/button';
import { openFieldInDialog } from '../../../redux/actions';
import { WorkflowControlButtons } from './control-buttons';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';
import { type OnTelemetryEventFunction } from '../../../redux/state';

const edgeTypes = {
    default: CustomEdge,
};

export function WorkflowComponent(props: WorkflowComponentProperties): React.ReactElement {
    return (
        <ReactFlowProvider>
            <WorkflowInnerComponent {...props} />
        </ReactFlowProvider>
    );
}

export function WorkflowInnerComponent(props: WorkflowComponentProperties): React.ReactElement {
    const { elementId, fieldProperties, isParentDisabled, screenId, setFieldValue, fixedHeight, validate, value } =
        props;

    const isDragging = React.useRef(false);
    const [history, setHistory] = React.useState<Array<WorkflowFieldValue>>([]);
    const [historyIndex, setHistoryIndex] = React.useState<number>(0);
    const componentRef = React.useRef<HTMLDivElement>(null);
    const emptyStateElementRef = React.useRef<HTMLDivElement>(null);
    const dispatch = useDispatch();
    const workflowNodes = useDeepEqualSelector<xtremRedux.XtremAppState, WorkflowNode[] | null>(s => s.workflowNodes);
    const expansionDialogControl = useDeepEqualSelector<xtremRedux.XtremAppState, DialogControl | null>(s => {
        return (
            Object.values(s.activeDialogs).find(
                d => d.screenId === screenId && d.content instanceof Array && d.content[0]?.id === elementId,
            )?.dialogControl ?? null
        );
    });

    // From workflow nodes we set all to CustomNode type for ReactFlow
    const nodeTypes = React.useMemo(() => {
        return (
            workflowNodes?.reduce((types: Record<string, React.FC>, node) => {
                types[node.key] = CustomNode;
                return types;
            }, {}) || {}
        );
    }, [workflowNodes]);

    const hasExternalValueChanged = useCompare(value);
    const connectingStartNodeId = React.useRef<{ nodeId: string; handleId: string } | null>(null);
    const edgeSplittingId = React.useRef<string | null>(null);
    const nextNodePosition = React.useRef<{ clientX?: number; clientY?: number }>({});
    const [canRedo, setCanRedo] = React.useState(false);
    const [addDialogFilters, setAddDialogFilters] = React.useState<string[] | null>(null);
    const { screenToFlowPosition, flowToScreenPosition, zoomIn, zoomOut, fitView } = useReactFlow();
    const [nodes, setNodes, onNodesChange] = useNodesState([]);
    const [edges, setEdges, onEdgesChange] = useEdgesState([]);
    const [errors, setErrors] = React.useState<WorkflowError[]>([]);
    const onTelemetryEvent = useDeepEqualSelector<xtremRedux.XtremAppState, OnTelemetryEventFunction | undefined>(
        (s: xtremRedux.XtremAppState) => s.applicationContext?.onTelemetryEvent,
    );

    const [eventLogs, setEventLogs] = React.useState<WorkflowLogEntry[]>([]);

    const hasFitView = React.useRef(false);

    const selectedRecordId = useDeepEqualSelector((state: xtremRedux.XtremAppState) => {
        const screenDef = state.screenDefinitions?.[screenId];
        return screenDef?.selectedRecordId;
    });

    React.useEffect(() => {
        hasFitView.current = false;
    }, [selectedRecordId]);

    React.useEffect(() => {
        if (!hasFitView.current && (value?.nodes?.length ?? 0) > 0) {
            setTimeout(() => {
                fitView({ padding: 0.4 });
                hasFitView.current = true;
            }, 100);
        }
    }, [value?.nodes, fitView]);

    React.useEffect(() => {
        const validationErrors = props.validationErrors;
        if (validationErrors && validationErrors.length > 0) {
            setErrors(validationErrors.map(e => ({ stepId: e.recordId || '', message: e.message })));
        } else {
            setErrors([]);
        }
    }, [props.validationErrors, setNodes, value?.nodes, setErrors]);

    React.useEffect(() => {
        if (!isNil(props.fieldProperties.eventLogs) && !isEqual(eventLogs, props.fieldProperties.eventLogs)) {
            setEventLogs(props.fieldProperties.eventLogs || []);
        }
    }, [props.fieldProperties.eventLogs, eventLogs, setEventLogs]);

    React.useEffect(() => {
        setNodes(prevNodes =>
            prevNodes.map(node => {
                const error = errors.find(e => e.stepId === node.id);

                // It's possible stepID is repeated, if it's the case we will take the last one
                const lastLog = eventLogs.filter(l => l.stepId === node.id).pop();
                return {
                    ...node,
                    data: {
                        ...node.data,
                        message: error?.message,
                        eventLog: lastLog,
                    },
                };
            }),
        );
    }, [errors, eventLogs, setNodes]);

    const takeHistorySnapshot = React.useCallback(() => {
        const newHistory = history.slice(0, historyIndex);
        setHistory([...newHistory, { nodes, edges }]);
        setHistoryIndex(newHistory.length + 1);
    }, [edges, history, historyIndex, nodes]);

    const onExpand = React.useCallback(() => {
        dispatch(openFieldInDialog(screenId, elementId));
    }, [dispatch, elementId, screenId]);

    const onMinimize = React.useCallback(() => {
        expansionDialogControl?.cancel();
    }, [expansionDialogControl]);

    const isWorkflowDirty = React.useMemo(() => {
        return historyIndex !== 0 || (historyIndex === 0 && history.length > 0);
    }, [history, historyIndex]);

    const onUndo = React.useCallback(() => {
        // If the history index is at the end, we need to take a snapshot of the current state before going back
        if (historyIndex === history.length) {
            takeHistorySnapshot();
        }
        const historyItem = history[historyIndex - 1];
        if (!historyItem) return;
        setNodes(historyItem.nodes);
        setEdges(historyItem.edges);
        setHistoryIndex(historyIndex - 1);
        setCanRedo(true);
    }, [history, historyIndex, setEdges, setNodes, takeHistorySnapshot]);

    const onRedo = React.useCallback(() => {
        const historyItem = history[historyIndex + 1];
        if (!historyItem) return;

        setNodes(historyItem.nodes);
        setEdges(historyItem.edges);
        setHistoryIndex(historyIndex + 1);
        if (historyIndex + 1 === history.length - 1) {
            setCanRedo(false);
        }
    }, [history, historyIndex, setEdges, setNodes]);

    const isDisabled = React.useMemo(() => {
        return Boolean(isParentDisabled) || isFieldDisabled(screenId, fieldProperties, value, {});
    }, [fieldProperties, isParentDisabled, screenId, value]);

    const isReadOnly = React.useMemo(() => {
        return isFieldReadOnly(screenId, fieldProperties, value, {});
    }, [fieldProperties, screenId, value]);

    React.useEffect(() => {
        if (!workflowNodes) {
            dispatch(loadWorkflowNodes());
        }
    }, [dispatch, workflowNodes]);

    React.useEffect(() => {
        const areNodesDifferent = !isEqual(value?.nodes, removeTransientNodeDataPropertiesFromNodes(nodes));
        if (hasExternalValueChanged && areNodesDifferent) {
            setNodes(
                (value?.nodes || []).map(n => ({
                    ...n,
                    data: {
                        ...n.data,
                        type: n.type,
                    },
                })),
            );
        }

        const areEdgesDifferent = !isEqual(value?.edges, removeTransientEdgeDataPropertiesFromEdges(edges));
        if (hasExternalValueChanged && areEdgesDifferent) {
            setEdges(
                (value?.edges || []).map(e => ({
                    ...e,
                    ...edgeStyle,
                    data: { screenId, elementId },
                })),
            );
        }

        if (hasExternalValueChanged && (areEdgesDifferent || areNodesDifferent)) {
            setHistory([]);
            setHistoryIndex(0);
        }
    }, [value, setNodes, setEdges, elementId, screenId, nodes, edges, hasExternalValueChanged, isReadOnly, isDisabled]);

    const onAddNodeDialogClose = React.useCallback(() => {
        connectingStartNodeId.current = null;
        edgeSplittingId.current = null;
        setAddDialogFilters(null);
    }, []);

    const onChange = React.useCallback(
        (newValue: WorkflowFieldValue = { edges, nodes }): void => {
            if (hasExternalValueChanged) {
                return;
            }
            const cleanNodes = removeTransientNodeDataPropertiesFromNodes(newValue.nodes);
            const cleanEdges = removeTransientEdgeDataPropertiesFromEdges(newValue.edges);
            const needToValidate = (): boolean => {
                if (value?.nodes.length !== newValue.nodes.length) {
                    return true;
                }
                // If some of the nodes data has been changed we need to call the validate
                return cleanNodes.some((node, index) => {
                    const oldNode = value?.nodes[index];
                    if (oldNode && node.data && oldNode.data) {
                        return !isEqual(node.data, oldNode.data);
                    }
                    return false;
                });
            };

            if (
                isWorkflowDirty &&
                ((!newValue.nodes.find(n => n.dragging) && !isEqual(cleanNodes, value?.nodes ?? [])) ||
                    !isEqual(cleanEdges, value?.edges ?? []))
            ) {
                handleChange(
                    elementId,
                    { nodes: cleanNodes, edges: cleanEdges },
                    setFieldValue,
                    needToValidate() ? validate : undefined, // We only call validate function if data in nodes has changed or if the number of nodes has changed
                    changeEventHandler(screenId, elementId),
                );
            }
        },
        [
            edges,
            nodes,
            hasExternalValueChanged,
            isWorkflowDirty,
            value?.nodes,
            value?.edges,
            elementId,
            setFieldValue,
            validate,
            screenId,
        ],
    );

    const onNodeDataChange = React.useCallback(
        (nodeId: string, data: any) => {
            takeHistorySnapshot();
            const newNodes = [...nodes];
            const index = newNodes.findIndex(n => n.id === nodeId);
            newNodes[index] = {
                ...newNodes[index],
                data,
            };
            setNodes(newNodes);
        },
        [nodes, setNodes, takeHistorySnapshot],
    );

    const onNodeInsertToEdge = React.useCallback(
        (edgeId: string) => {
            edgeSplittingId.current = edgeId;
            const edge = edges.find(e => e.id === edgeId);
            const sourceNode = edge?.source;
            const sourceHandle = edge?.sourceHandle;
            if (sourceNode && sourceHandle) {
                connectingStartNodeId.current = { nodeId: sourceNode, handleId: sourceHandle };
                setAddDialogFilters(['condition', 'action']);
            }
        },
        [edges],
    );

    const onNodeBelowNode = React.useCallback(
        (nodeId: string, type: string, handleId?: string) => {
            const targetNode = nodes.find(n => n.id === nodeId);
            if (!targetNode) {
                return;
            }
            connectingStartNodeId.current = { nodeId, handleId: handleId || 'out' };
            const { x, y } = flowToScreenPosition(targetNode.position);
            if (handleId === 'out-true') {
                nextNodePosition.current = {
                    clientX: x - 100,
                    clientY: y + 200,
                };
            } else if (handleId === 'out-false') {
                nextNodePosition.current = {
                    clientX: x + 350,
                    clientY: y + 200,
                };
            } else {
                nextNodePosition.current = {
                    clientX: x + 125,
                    clientY: y + 200,
                };
            }
            setAddDialogFilters(type ? [type] : ['condition', 'action']);
        },
        [flowToScreenPosition, nodes],
    );

    const onClick = React.useCallback((): void => {
        if (!componentRef.current || componentRef.current.contains(document.activeElement)) {
            return;
        }
        onChange();
    }, [onChange]);

    const onConnectStart = React.useCallback((_: never, { nodeId, handleId }: OnConnectStartParams) => {
        if (nodeId && handleId) {
            connectingStartNodeId.current = { nodeId, handleId };
        }
    }, []);

    const onConnectEnd: OnConnectEnd = React.useCallback(
        (event: MouseEvent) => {
            if (!connectingStartNodeId.current) return;

            const target = event.target as HTMLElement;

            const targetIsPane = target?.classList?.contains('react-flow__pane');

            if (targetIsPane) {
                nextNodePosition.current = {
                    clientX: event.clientX,
                    clientY: event.clientY,
                };
                setAddDialogFilters(['condition', 'action']);
                return;
            }

            const nodeId = findAncestorDatasetProperty(target, 'nodeid');

            if (nodeId && connectingStartNodeId.current.nodeId !== nodeId) {
                takeHistorySnapshot();

                setEdges([
                    ...edges,
                    createNewEdge({
                        edges,
                        targetNodeId: nodeId,
                        sourceHandleId: connectingStartNodeId.current.handleId,
                        sourceNodeId: connectingStartNodeId.current.nodeId,
                        data: { screenId, elementId },
                    }),
                ]);
            }
        },
        [edges, elementId, screenId, setEdges, takeHistorySnapshot],
    );

    const onEdgesChangeInternal: OnEdgesChange = React.useCallback(
        change => {
            if (isDisabled || isReadOnly) {
                return;
            }

            if (isSingleEdgeRemoval(change, edges)) {
                return;
            }

            onEdgesChange(change);
        },
        [edges, isDisabled, isReadOnly, onEdgesChange],
    );

    const handleRemovalChange = React.useCallback(
        async (change: NodeRemoveChange) => {
            const connectingEdges = edges.filter(e => e.source === change.id);
            if (connectingEdges.length > 0) {
                try {
                    await confirmationDialog(
                        props.screenId,
                        'info',
                        localize('@sage/xtrem-ui/workflow-delete-node-chain-title', 'Delete step flow'),
                        localize(
                            '@sage/xtrem-ui/workflow-delete-node-chain-message',
                            'If you remove this step, any subsequent steps with no other links are also removed.',
                        ),
                    );
                } catch (e) {
                    // eslint-disable-next-line no-useless-return
                    return;
                }
            }

            takeHistorySnapshot();
            let newEdges = [...edges];
            const newNodes = [...nodes];
            removeChildNodesAndEdgesFromStartPoint(change.id, newEdges, newNodes);
            const remainingIds = newNodes.map(n => n.id);
            newEdges = newEdges.filter(e => remainingIds.includes(e.source) && remainingIds.includes(e.target));
            setNodes(newNodes);
            setEdges(newEdges);
        },
        [edges, nodes, props.screenId, setEdges, setNodes, takeHistorySnapshot],
    );

    const handlePositionChange = React.useCallback(
        (change: NodePositionChange) => {
            // We need to take a history snapshot only when the user starts dragging a node
            if (!isDragging.current && change.dragging) {
                isDragging.current = true;
                takeHistorySnapshot();
            } else if (isDragging.current && !change.dragging) {
                isDragging.current = false;
            }

            onNodesChange([change]);
        },
        [onNodesChange, takeHistorySnapshot],
    );

    const onNodesChangeInternal: OnNodesChange = React.useCallback(
        async change => {
            if (isDisabled || isReadOnly) {
                return;
            }

            const removalChange = change.find(c => c.type === 'remove') as NodeRemoveChange;
            if (removalChange) {
                await handleRemovalChange(removalChange);
                return;
            }

            const positionChange = change.find(c => c.type === 'position') as NodePositionChange;
            if (positionChange) {
                await handlePositionChange(positionChange);
                return;
            }

            onNodesChange(change);
        },
        [handlePositionChange, handleRemovalChange, isDisabled, isReadOnly, onNodesChange],
    );

    const onAddFirstElement = React.useCallback(() => {
        setAddDialogFilters(['event']);
    }, []);

    const addNewNodeByNextPosition = React.useCallback(
        (selectedNodeType: string, values: any) => {
            const position =
                nextNodePosition.current.clientX && nextNodePosition.current.clientY
                    ? screenToFlowPosition({
                          x: (nextNodePosition.current.clientX ?? 170) - 125,
                          y: nextNodePosition.current.clientY ?? 20,
                      })
                    : screenToFlowPosition({
                          x: roundToNearestTwenty((emptyStateElementRef.current?.offsetWidth || 40) / 2 - 125),
                          y: 40,
                      });
            const newNode = createNewNode({ selectedNodeType, values, nodes, position });
            const newNodes = [...nodes, newNode];
            const newEdges = [...edges];

            if (connectingStartNodeId.current) {
                newEdges.push(
                    createNewEdge({
                        edges,
                        targetNodeId: newNode.id,
                        sourceHandleId: connectingStartNodeId.current.handleId,
                        sourceNodeId: connectingStartNodeId.current.nodeId,
                        data: { screenId, elementId },
                    }),
                );
            }
            // If the new node comes from a condition node where the true/false branches were not enabled, we need to set the ifTrueBranch/ifFalseBranch in condition data
            if (
                connectingStartNodeId.current &&
                ['out-true', 'out-false'].includes(connectingStartNodeId.current.handleId)
            ) {
                const branchKey =
                    connectingStartNodeId.current.handleId === 'out-true' ? 'ifTrueBranch' : 'ifFalseBranch';
                const index = newNodes.findIndex(n => n.id === connectingStartNodeId.current?.nodeId);
                if (index) {
                    newNodes[index] = {
                        ...newNodes[index],
                        data: { ...newNodes[index].data, [branchKey]: true },
                    };
                }
            }

            setNodes(newNodes);
            setEdges(newEdges);
        },
        [edges, elementId, nodes, screenId, screenToFlowPosition, setEdges, setNodes, emptyStateElementRef],
    );

    const isUndoDisabled = React.useMemo(() => {
        return historyIndex === 0;
    }, [historyIndex]);

    const isRedoDisabled = React.useMemo(() => {
        return !canRedo;
    }, [canRedo]);

    const addNewNodeByEdgeSplitting = React.useCallback(
        (selectedNodeType: string, values: any) => {
            const targetEdgeIndex = edges.findIndex(e => e.id === edgeSplittingId.current);
            const newEdges = [...edges];
            const splitEdge = newEdges.splice(targetEdgeIndex, 1)[0];

            const previousNode = nodes.find(n => n.id === splitEdge.source);
            const nextNode = nodes.find(n => n.id === splitEdge.target);

            const currentNodes = [...nodes];

            if (!previousNode || !nextNode) {
                return;
            }

            const newNode = createNewNode({
                selectedNodeType,
                values,
                nodes,
                position: {
                    x: (previousNode.position.x + nextNode.position.x) / 2,
                    y: (previousNode.position.y + nextNode.position.y) / 2,
                },
            });

            const newPreviousEdge = createNewEdge({
                edges,
                targetNodeId: newNode.id,
                sourceHandleId: splitEdge.sourceHandle,
                sourceNodeId: previousNode.id,
                data: { screenId, elementId },
            });

            const newNextEdge = createNewEdge({
                edges,
                targetNodeId: nextNode.id,
                sourceHandleId: selectedNodeType === 'condition' ? 'out-true' : 'out',
                sourceNodeId: newNode.id,
                data: { screenId, elementId },
            });

            setNodes([...currentNodes, newNode]);
            setEdges([...newEdges, newPreviousEdge, newNextEdge]);
        },
        [edges, elementId, nodes, screenId, setEdges, setNodes],
    );

    const onNewNodeAdded = React.useCallback(
        ({ selectedNodeType, values }: { selectedNodeType: string; values: any }) => {
            if (nodes.length === 0 || (nextNodePosition.current.clientX && nextNodePosition.current.clientY)) {
                addNewNodeByNextPosition(selectedNodeType, values);
            }

            if (edgeSplittingId.current) {
                addNewNodeByEdgeSplitting(selectedNodeType, values);
            }

            setAddDialogFilters(null);
            connectingStartNodeId.current = null;
            edgeSplittingId.current = null;
            nextNodePosition.current = {};
            takeHistorySnapshot();

            if (nodes.length === 0) {
                onTelemetryEvent?.('workflowStartingPointAdded', {
                    nodeType: selectedNodeType,
                    entityName: values.entityName,
                    topic: values.topic,
                });
            } else {
                onTelemetryEvent?.('workflowNodeAdded', {
                    nodeType: selectedNodeType,
                });
            }
        },
        [addNewNodeByEdgeSplitting, addNewNodeByNextPosition, nodes, takeHistorySnapshot, onTelemetryEvent],
    );

    React.useEffect(() => {
        onChange();
    }, [onChange, nodes, edges]);

    React.useEffect(() => {
        window.addEventListener('click', onClick);
        return (): void => {
            window.removeEventListener('click', onClick);
        };
    }, [onClick]);

    const workflowContext = React.useMemo<WorkflowContextValue>(
        () => ({
            screenId,
            elementId,
            onNodeDataChange,
            onNodeInsertToEdge,
            onNodeBelowNode,
            isReadOnly: isReadOnly || isDisabled,
        }),
        [screenId, elementId, onNodeDataChange, onNodeInsertToEdge, onNodeBelowNode, isReadOnly, isDisabled],
    );

    const height = React.useMemo(() => {
        if (!fixedHeight) {
            return '400px';
        }

        // Use most of the available space, but leave some margin to prevent scroll
        // Subtract a small safety margin (40px) to account for borders, padding, etc.
        const safetyMargin = 40;
        const maxUsableHeight = fixedHeight - safetyMargin;

        // Minimum height should be reasonable for small workflows
        const minHeight = 300;
        const adjustedHeight = Math.max(maxUsableHeight, minHeight);

        return `${adjustedHeight}px`;
    }, [fixedHeight]);

    return (
        <>
            <CarbonWrapper
                {...props}
                className="e-workflow-field"
                componentName="workflow"
                noReadOnlySupport={true}
                value={!!value || false}
            >
                {nodes.length === 0 && (
                    <div className="e-workflow-empty" style={{ height }} ref={emptyStateElementRef}>
                        <div className="e-workflow-empty-content">
                            <Typography lineHeight="30px" variant="h3">
                                {localize('@sage/xtrem-ui/workflow-empty', 'This workflow is currently empty')}
                            </Typography>

                            {!isReadOnly && !isDisabled && (
                                <Button
                                    data-pendoid="workflow-empty-add-trigger-event"
                                    mt={1}
                                    iconType="add"
                                    onClick={onAddFirstElement}
                                    data-testid="add-item-button"
                                >
                                    {localize('@sage/xtrem-ui/workflow-add-trigger-event', 'Add a trigger event')}
                                </Button>
                            )}
                        </div>
                    </div>
                )}
                {nodes.length !== 0 && (
                    <div className="e-workflow-wrapper" style={{ height }} ref={componentRef}>
                        <WorkflowContext.Provider value={workflowContext}>
                            <ReactFlow
                                nodesDraggable={!isReadOnly && !isDisabled}
                                nodesConnectable={!isReadOnly && !isDisabled}
                                edgesFocusable={!isReadOnly && !isDisabled}
                                nodesFocusable={!isReadOnly && !isDisabled}
                                nodes={nodes}
                                edges={edges}
                                snapToGrid={true}
                                snapGrid={[20, 20]}
                                onConnectStart={onConnectStart}
                                onConnectEnd={onConnectEnd}
                                onNodesChange={onNodesChangeInternal}
                                onEdgesChange={onEdgesChangeInternal}
                                nodeTypes={nodeTypes}
                                edgeTypes={edgeTypes}
                                deleteKeyCode={['Backspace', 'Delete']}
                            >
                                <Background color="#004455" />
                                <WorkflowControlButtons
                                    zoomIn={zoomIn}
                                    zoomOut={zoomOut}
                                    fitView={fitView}
                                    onExpand={onExpand}
                                    onMinimize={onMinimize}
                                    onUndo={onUndo}
                                    onRedo={onRedo}
                                    isUndoDisabled={isUndoDisabled}
                                    isRedoDisabled={isRedoDisabled}
                                    expansionDialogControl={expansionDialogControl}
                                    localize={localize}
                                />
                            </ReactFlow>
                        </WorkflowContext.Provider>
                    </div>
                )}
            </CarbonWrapper>
            <AddWorkflowNodeDialog
                isOpen={!!addDialogFilters}
                onClose={onAddNodeDialogClose}
                onConfirm={onNewNodeAdded}
                elementId={props.elementId}
                screenId={props.screenId}
                previousNodeId={connectingStartNodeId.current?.nodeId}
                filterType={addDialogFilters || []}
            />
        </>
    );
}

export const ConnectedFormDesignerComponent = connect(mapStateToProps(), mapDispatchToProps())(WorkflowComponent);

export default ConnectedFormDesignerComponent;
