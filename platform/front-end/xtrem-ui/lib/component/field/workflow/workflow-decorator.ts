/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { WorkflowControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { WorkflowDecoratorProperties, WorkflowExtensionDecoratorProperties } from './workflow-types';

class WorkflowDecorator extends AbstractFieldDecorator<FieldKey.Workflow> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = WorkflowControlObject;
}

/**
 * Initializes the decorated member as a [FormDesigner]{@link WorkflowControlObject} field with the provided properties
 *
 * @param properties The properties that the [FormDesigner]{@link WorkflowControlObject} field will be initialized with
 */
export function workflowField<CT extends ScreenExtension<CT>>(
    properties: WorkflowDecoratorProperties<Extend<CT>>,
): (target: CT, name: string) => void {
    return standardDecoratorImplementation<CT, FieldKey.Workflow>(
        properties,
        WorkflowDecorator,
        FieldKey.Workflow,
        true,
    );
}

export function workflowFieldOverride<CT extends ScreenExtension<CT>>(
    properties: WorkflowExtensionDecoratorProperties<CT>,
): (target: CT, name: string) => void {
    return standardExtensionDecoratorImplementation<CT, FieldKey.Workflow>(properties);
}
