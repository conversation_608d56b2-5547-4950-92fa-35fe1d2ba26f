import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { AddWorkflowNodeDialog } from '../add-workflow-node-dialog';
import * as transformers from '../../../../utils/transformers';
import * as pageUtils from '../../../../utils/page-utils';
import { getMockStore } from '../../../../__tests__/test-helpers/mock-store-helpers';

jest.mock('../../../../utils/transformers', () => ({
    getArtifactDescription: jest.fn(),
}));

jest.mock('../../../../utils/page-utils', () => ({
    getPageTitlesFromPageDefinition: jest.fn(),
}));

jest.mock('../../../ui/screen-component.tsx', () => ({
    ScreenComponent: ({ onFinish }: any) => (
        <div data-testid="screen-component">
            Screen Component
            <button type="button" onClick={() => onFinish({ outputVariables: [] })} data-testid="finish-button">
                Finish
            </button>
        </div>
    ),
}));

jest.mock('../workflow-node-selector-component', () => ({
    WorkflowNodeSelectorComponent: ({ onChange, value }: any) => (
        <div>
            <select data-testid="workflow-node-selector" value={value || ''} onChange={e => onChange(e.target.value)}>
                <option value="node1">Node 1</option>
                <option value="node2">Node 2</option>
            </select>
        </div>
    ),
}));

jest.mock('../../../container/footer/business-actions.tsx', () => ({
    __esModule: true,
    default: () => <div data-testid="business-actions">Mocked BusinessActions</div>,
}));

const storeState: any = {
    workflowNodes: [
        {
            key: 'node1',
            type: 'action',
            defaultConfig: '{"outputVariables": []}',
            configurationPage: 'configPage1',
            title: 'Node 1',
            description: 'Action Node',
            color: 'red',
            icon: 'action-icon',
        },
        {
            key: 'node2',
            type: 'event',
            defaultConfig: '{"outputVariables": []}',
            configurationPage: 'configPage2',
            title: 'Node 2',
            description: 'Event Node',
            color: 'blue',
            icon: 'event-icon',
        },
    ],
    screenDefinitions: {
        configPage1: {
            metadata: {
                screenId: 'screen1',
                blockThunks: [],
                businessActionsExtensionsThunk: [],
                controlObjects: {},
                customizations: {},
                uiComponentProperties: {
                    screen1: {},
                },
            },
            page: { $: { businessActions: [] } },
        },
        configPage2: {
            metadata: {
                screenId: 'screen2',
                blockThunks: [],
                businessActionsExtensionsThunk: [],
                controlObjects: {},
                customizations: {},
                uiComponentProperties: {
                    screen2: {},
                },
            },
            page: { $: { businessActions: [] } },
        },
    },
};

const store = getMockStore(storeState);

describe('AddWorkflowNodeDialog', () => {
    const mockOnClose = jest.fn();
    const mockOnConfirm = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(transformers, 'getArtifactDescription').mockReturnValue({ name: 'configPage1' } as any);
        jest.spyOn(pageUtils, 'getPageTitlesFromPageDefinition').mockReturnValue({
            title: 'Configuration Page',
        } as any);
    });

    const defaultProps = {
        isOpen: true,
        onClose: mockOnClose,
        onConfirm: mockOnConfirm,
        screenId: 'test-screen',
        elementId: 'test-element',
    };

    it('should render correctly with initial step', () => {
        const { getByText } = render(
            <Provider store={store}>
                <AddWorkflowNodeDialog {...defaultProps} />
            </Provider>,
        );
        expect(getByText('Action gallery')).toBeInTheDocument();
    });

    it('should transition to the next step when "Next" is clicked', () => {
        const { getByText } = render(
            <Provider store={store}>
                <AddWorkflowNodeDialog {...defaultProps} />
            </Provider>,
        );
        fireEvent.click(getByText('Next'));
        expect(getByText('Configuration')).toBeInTheDocument();
    });

    it('should call onClose when "Cancel" is clicked on the first step', () => {
        const { getByText } = render(
            <Provider store={store}>
                <AddWorkflowNodeDialog {...defaultProps} />
            </Provider>,
        );
        fireEvent.click(getByText('Cancel'));
        expect(mockOnClose).toHaveBeenCalled();
    });

    it('should return to the first step when "Previous" is clicked on the second step', () => {
        const { getByText } = render(
            <Provider store={store}>
                <AddWorkflowNodeDialog {...defaultProps} />
            </Provider>,
        );
        fireEvent.click(getByText('Next'));
        expect(getByText('Action gallery')).toBeInTheDocument();
    });

    it('should call onConfirm with selected node type and values on configuration finish', () => {
        const { getByText, getByTestId } = render(
            <Provider store={store}>
                <AddWorkflowNodeDialog {...defaultProps} />
            </Provider>,
        );
        fireEvent.change(getByTestId('workflow-node-selector'), { target: { value: 'node1' } });
        fireEvent.click(getByText('Next'));
        fireEvent.click(getByTestId('finish-button')); // Simulate configuration finish
        expect(mockOnConfirm).toHaveBeenCalledWith({ selectedNodeType: 'node1', values: { outputVariables: [] } });
    });
});
