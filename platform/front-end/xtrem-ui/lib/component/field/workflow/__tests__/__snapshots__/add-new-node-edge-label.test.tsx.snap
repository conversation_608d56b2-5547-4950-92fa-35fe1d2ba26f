// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AddNewNodeEdgeLabel should render correctly 1`] = `
<div>
  <div
    class="nodrag nopan"
    style="position: absolute; background: transparent; padding: 10px; color: rgb(255, 80, 80); font-size: 12px; font-weight: 700; transform: translate(-50%, -50%) translate(100px,200px); pointer-events: all;"
  >
    <div
      style="padding: 32px;"
    />
  </div>
</div>
`;

exports[`AddNewNodeEdgeLabel should render correctly when isAddButtonDisplayed is true 1`] = `
.c3 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c3:hover {
  color: #006437;
  background-color: transparent;
}

.c3::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e940";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0 {
  padding-left: var(--spacing200);
  padding-right: var(--spacing200);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 32px;
  padding: 0px;
  width: 32px;
  min-height: 32px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0 .c2 {
  color: var(--colorsActionMajor500);
}

.c0:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c0:hover .c2 {
  color: var(--colorsActionMajorYang100);
}

.c0 .c2 {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1px;
  height: 20px;
  width: 20px;
}

.c0 .c2 svg {
  margin-top: 0;
}

.c1 {
  border-radius: var(--borderRadius050);
  background: transparent;
  padding: var(--spacing100);
  border-color: var(--colorsActionMinor500);
  color: var(--colorsActionMinor500);
  min-height: var(--sizing400);
  padding: var(--spacing000) var(--spacing100) var(--spacing000) var(--spacing100);
}

.c1 .c2 {
  position: absolute;
}

.c1 .c2 {
  color: var(--colorsActionMinor500);
}

.c1:hover {
  color: var(--colorsActionMinorYang100);
  background: var(--colorsActionMinor600);
}

<div>
  <div
    class="nodrag nopan"
    style="position: absolute; background: transparent; padding: 10px; color: rgb(255, 80, 80); font-size: 12px; font-weight: 700; transform: translate(-50%, -50%) translate(100px,200px); pointer-events: all;"
  >
    <div
      style="padding: 32px;"
    >
      <button
        aria-label="Add step"
        class="c0 c1 e-workflow-add-node-on-edge-button"
        data-component="button-minor"
        draggable="false"
        type="button"
      >
        <span
          aria-hidden="true"
          class="c2 c3"
          color="--colorsActionMajor500"
          data-component="icon"
          data-element="plus"
          data-role="icon"
          font-size="small"
          type="plus"
        />
      </button>
    </div>
  </div>
</div>
`;
