// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`WorkflowInnerComponent matches snapshot with nodes and edges 1`] = `
.c0 {
  display: none;
  height: 0;
  width: 0;
}

<div>
  <div
    class="e-field e-workflow-field"
    data-testid="e-workflow-field e-field-bind-element-1"
  >
    <div
      class="e-workflow-wrapper"
      style="height: 400px;"
    >
      <div
        class="react-flow"
        data-testid="rf__wrapper"
        style="width: 100%; height: 100%; overflow: hidden; position: relative; z-index: 0;"
      >
        <div
          class="react-flow__renderer"
          style="position: absolute; width: 100%; height: 100%; top: 0px; left: 0px; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
        >
          <div
            class="react-flow__pane"
            style="position: absolute; width: 100%; height: 100%; top: 0px; left: 0px;"
          >
            <div
              class="react-flow__viewport react-flow__container"
              style="transform: translate(0px,0px) scale(1);"
            >
              <svg
                class="react-flow__edges react-flow__container"
                height="500"
                style="z-index: 0;"
                width="500"
              >
                <defs>
                  <marker
                    class="react-flow__arrowhead"
                    id="1__color=#A6A6A6&height=12&type=arrow&width=12"
                    markerHeight="12"
                    markerUnits="strokeWidth"
                    markerWidth="12"
                    orient="auto-start-reverse"
                    refX="0"
                    refY="0"
                    viewBox="-10 -10 20 20"
                  >
                    <polyline
                      fill="none"
                      points="-5,-4 0,0 -5,4"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      style="stroke: #A6A6A6; stroke-width: 1;"
                    />
                  </marker>
                </defs>
                <g />
              </svg>
              <div
                class="react-flow__edgelabel-renderer"
              />
              <div
                class="react-flow__nodes"
                style="position: absolute; width: 100%; height: 100%; top: 0px; left: 0px;"
              >
                <div
                  aria-describedby="react-flow__node-desc-1"
                  class="react-flow__node react-flow__node-default nopan selectable"
                  data-id="node-1"
                  data-testid="rf__node-node-1"
                  role="button"
                  style="z-index: 0; transform: translate(100px,100px); pointer-events: all; visibility: hidden; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  tabindex="0"
                >
                  <div
                    class="react-flow__handle react-flow__handle-top nodrag nopan target connectable connectablestart connectableend connectionindicator"
                    data-handlepos="top"
                    data-id="node-1-null-target"
                    data-nodeid="node-1"
                  />
                  Node 1
                  <div
                    class="react-flow__handle react-flow__handle-bottom nodrag nopan source connectable connectablestart connectableend connectionindicator"
                    data-handlepos="bottom"
                    data-id="node-1-null-source"
                    data-nodeid="node-1"
                  />
                </div>
                <div
                  aria-describedby="react-flow__node-desc-1"
                  class="react-flow__node react-flow__node-default nopan selectable"
                  data-id="node-2"
                  data-testid="rf__node-node-2"
                  role="button"
                  style="z-index: 0; transform: translate(300px,100px); pointer-events: all; visibility: hidden; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);"
                  tabindex="0"
                >
                  <div
                    class="react-flow__handle react-flow__handle-top nodrag nopan target connectable connectablestart connectableend connectionindicator"
                    data-handlepos="top"
                    data-id="node-2-null-target"
                    data-nodeid="node-2"
                  />
                  Node 2
                  <div
                    class="react-flow__handle react-flow__handle-bottom nodrag nopan source connectable connectablestart connectableend connectionindicator"
                    data-handlepos="bottom"
                    data-id="node-2-null-source"
                    data-nodeid="node-2"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <svg
          class="react-flow__background"
          data-testid="rf__background"
          style="position: absolute; width: 100%; height: 100%; top: 0px; left: 0px;"
        >
          <pattern
            height="20"
            id="pattern-1undefined"
            patternTransform="translate(-0.5,-0.5)"
            patternUnits="userSpaceOnUse"
            width="20"
            x="0"
            y="0"
          >
            <circle
              cx="0.5"
              cy="0.5"
              fill="#004455"
              r="0.5"
            />
          </pattern>
          <rect
            fill="url(#pattern-1undefined)"
            height="100%"
            width="100%"
            x="0"
            y="0"
          />
        </svg>
        <div
          class="react-flow__panel react-flow__controls bottom left"
          data-testid="rf__controls"
          style="pointer-events: all;"
        >
          <button
            aria-label="Zoom in"
            class="react-flow__controls-button"
            data-pendoid="workflow-zoom-in"
            data-testid="e-workflow-zoom-in-button"
            title="Zoom in"
            type="button"
          >
            <svg
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"
              />
            </svg>
          </button>
          <button
            aria-label="Zoom out"
            class="react-flow__controls-button"
            data-pendoid="workflow-zoom-out"
            data-testid="e-workflow-zoom-out-button"
            title="Zoom out"
            type="button"
          >
            <svg
              viewBox="0 0 32 5"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0h32v4.2H0z"
              />
            </svg>
          </button>
          <button
            aria-label="Fit view to screen"
            class="react-flow__controls-button"
            data-pendoid="workflow-fit-view"
            data-testid="e-workflow-fit-view-button"
            title="Fit view to screen"
            type="button"
          >
            <svg
              viewBox="0 0 32 30"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"
              />
            </svg>
          </button>
          <button
            aria-label="Expand"
            class="react-flow__controls-button"
            data-pendoid="workflow-expand"
            data-testid="e-workflow-expand-button"
            title="Expand"
            type="button"
          >
            <svg
              fill="none"
              height="20"
              viewBox="0 0 21 20"
              width="21"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g
                id="expand"
              >
                <path
                  d="M2.85616 1.0004C2.56676 1.00071 2.29168 1.12636 2.10197 1.34491C1.91226 1.56346 1.82653 1.85348 1.86691 2.14005V6.01017C1.86181 6.3708 2.05128 6.70625 2.36278 6.88806C2.67428 7.06986 3.05954 7.06986 3.37103 6.88806C3.68253 6.70625 3.87201 6.3708 3.86691 6.01017V4.42423L7.65988 8.2172C7.91069 8.47844 8.28314 8.58367 8.63358 8.49232C8.98403 8.40096 9.2577 8.12729 9.34906 7.77685C9.44041 7.4264 9.33518 7.05395 9.07394 6.80314L5.28097 3.01017H6.86691C7.22754 3.01527 7.56299 2.82579 7.74479 2.51429C7.9266 2.2028 7.9266 1.81754 7.74479 1.50604C7.56299 1.19454 7.22754 1.00507 6.86691 1.01017H2.98995C2.94563 1.00392 2.90093 1.00066 2.85616 1.0004ZM18.8474 1.0004C18.8104 1.00162 18.7736 1.00488 18.737 1.01017H14.8669C14.5063 1.00507 14.1708 1.19454 13.989 1.50604C13.8072 1.81754 13.8072 2.2028 13.989 2.51429C14.1708 2.82579 14.5063 3.01527 14.8669 3.01017H16.4528L12.6599 6.80314C12.3986 7.05395 12.2934 7.4264 12.3848 7.77685C12.4761 8.12729 12.7498 8.40096 13.1002 8.49232C13.4507 8.58367 13.8231 8.47844 14.0739 8.2172L17.8669 4.42423V6.01017C17.8618 6.3708 18.0513 6.70625 18.3628 6.88806C18.6743 7.06986 19.0595 7.06986 19.371 6.88806C19.6825 6.70625 19.872 6.3708 19.8669 6.01017V2.13321C19.906 1.84242 19.8153 1.54915 19.6191 1.33107C19.4228 1.11299 19.1407 0.992091 18.8474 1.0004ZM8.34738 11.5004C8.08758 11.5081 7.841 11.6167 7.65988 11.8031L3.86691 15.5961V14.0102C3.87201 13.6495 3.68253 13.3141 3.37103 13.1323C3.05954 12.9505 2.67428 12.9505 2.36278 13.1323C2.05128 13.3141 1.86181 13.6495 1.86691 14.0102V17.8871C1.82544 18.1971 1.93135 18.5086 2.15315 18.729C2.37494 18.9495 2.68709 19.0535 2.99679 19.0102H6.86691C7.22754 19.0153 7.56299 18.8258 7.74479 18.5143C7.9266 18.2028 7.9266 17.8175 7.74479 17.506C7.56299 17.1945 7.22754 17.0051 6.86691 17.0102H5.28097L9.07394 13.2172C9.3697 12.9297 9.45863 12.4901 9.29787 12.1103C9.13712 11.7304 8.75966 11.4882 8.34738 11.5004ZM13.3562 11.5004C12.9494 11.5009 12.5835 11.7477 12.4306 12.1246C12.2778 12.5015 12.3684 12.9335 12.6599 13.2172L16.4528 17.0102H14.8669C14.5063 17.0051 14.1708 17.1945 13.989 17.506C13.8072 17.8175 13.8072 18.2028 13.989 18.5143C14.1708 18.8258 14.5063 19.0153 14.8669 19.0102H18.7439C19.0538 19.0516 19.3653 18.9457 19.5858 18.7239C19.8062 18.5021 19.9102 18.19 19.8669 17.8803V14.0102C19.872 13.6495 19.6825 13.3141 19.371 13.1323C19.0595 12.9505 18.6743 12.9505 18.3628 13.1323C18.0513 13.3141 17.8618 13.6495 17.8669 14.0102V15.5961L14.0739 11.8031C13.8854 11.6094 13.6265 11.5002 13.3562 11.5004Z"
                  fill="#335B70"
                  id="Vector"
                />
              </g>
            </svg>
          </button>
          <button
            aria-label="Undo"
            class="react-flow__controls-button"
            data-pendoid="workflow-undo"
            data-testid="e-workflow-undo-button"
            disabled=""
            title="Undo"
            type="button"
          >
            <svg
              viewBox="0 0 32 30"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g
                transform="scale(0.031 0.031)"
              >
                <path
                  d="M312.435 641.319c9.706 9.706 22.391 14.547 35.101 14.547s25.395-4.841 35.101-14.547c19.388-19.388 19.388-50.815 0-70.203l-163.492-163.492h401.458c123.203 0 223.418 100.215 223.418 223.418s-100.215 223.418-223.418 223.418h-198.594c-27.406 0-49.648 22.243-49.648 49.648s22.243 49.648 49.648 49.648h198.594c177.94 0 322.715-144.775 322.715-322.715s-144.775-322.715-322.715-322.715h-401.458l163.492-163.492c19.388-19.388 19.388-50.815 0-70.203s-50.815-19.388-70.203 0l-248.242 248.242c-19.388 19.388-19.388 50.815 0 70.203l248.242 248.242z"
                />
              </g>
            </svg>
          </button>
          <button
            aria-label="Redo"
            class="react-flow__controls-button"
            data-pendoid="workflow-redo"
            data-testid="e-workflow-redo-button"
            disabled=""
            title="Redo"
            type="button"
          >
            <svg
              viewBox="0 0 32 30"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g
                transform="scale(0.031 0.031)"
              >
                <path
                  d="M372.367 953.757h186.182c27.406 0 49.648-22.243 49.648-49.648s-22.243-49.648-49.648-49.648h-186.182c-123.203 0-223.418-100.215-223.418-223.418s100.215-223.418 223.418-223.418h401.458l-163.492 163.492c-19.388 19.388-19.388 50.815 0 70.203 9.706 9.706 22.391 14.547 35.101 14.547s25.395-4.841 35.101-14.547l248.242-248.242c19.388-19.388 19.388-50.815 0-70.203l-248.242-248.242c-19.388-19.388-50.815-19.388-70.203 0s-19.388 50.815 0 70.203l163.492 163.492h-401.458c-177.94 0-322.715 144.774-322.715 322.715s144.775 322.715 322.715 322.715z"
                />
              </g>
            </svg>
          </button>
        </div>
        <div
          class="react-flow__panel react-flow__attribution bottom right"
          data-message="Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"
          style="pointer-events: all;"
        >
          <a
            aria-label="React Flow attribution"
            href="https://reactflow.dev"
            rel="noopener noreferrer"
            target="_blank"
          >
            React Flow
          </a>
        </div>
        <div
          id="react-flow__node-desc-1"
          style="display: none;"
        >
          Press enter or space to select a node.
          You can then use the arrow keys to move the node around.
           Press delete to remove it and escape to cancel.
           
        </div>
        <div
          id="react-flow__edge-desc-1"
          style="display: none;"
        >
          Press enter or space to select an edge. You can then press delete to remove it or escape to cancel.
        </div>
        <div
          aria-atomic="true"
          aria-live="assertive"
          id="react-flow__aria-live-1"
          style="position: absolute; width: 1px; height: 1px; border: 0px; padding: 0px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); clip-path: inset(100%);"
        />
      </div>
    </div>
  </div>
  <span
    class="c0"
    data-portal-entrance="testcarb-onco-mpon-ents-uniqguidmock"
    data-role="data-portal-entrance"
  />
</div>
`;
