import { MarkerType } from 'reactflow';
import { triggerFieldEvent } from '../../../../utils/events';
import { useFieldValue } from '../../../../utils/hooks/effects/use-set-field-value';
import * as utils from '../workflow-component-utils';

jest.mock('../../../../utils/events', () => ({
    triggerFieldEvent: jest.fn(),
}));

jest.mock('../../../../utils/hooks/effects/use-set-field-value', () => ({
    useFieldValue: jest.fn(),
}));

describe('workflow-component-utils', () => {
    describe('edgeStyle', () => {
        it('should have the correct edge style properties', () => {
            expect(utils.edgeStyle).toEqual({
                style: {
                    strokeWidth: 1,
                    stroke: '#A6A6A6',
                },
                markerEnd: {
                    type: MarkerType.Arrow,
                    width: 12,
                    height: 12,
                    color: '#A6A6A6',
                },
            });
        });
    });

    describe('removeTransientNodeDataProperties', () => {
        it('should remove the "type" property from data', () => {
            const data = { type: 'test', other: 'value' };
            const result = utils.removeTransientNodeDataProperties(data);
            expect(result).toEqual({ other: 'value' });
        });
    });

    describe('removeTransientNodeDataPropertiesFromNodes', () => {
        it('should remove transient properties from nodes', () => {
            const nodes = [
                {
                    id: '1',
                    data: { type: 'test', other: 'value' },
                    selected: false,
                    dragging: false,
                    position: { x: 0, y: 0 },
                },
            ];
            const result = utils.removeTransientNodeDataPropertiesFromNodes(nodes);
            expect(result).toEqual([
                {
                    id: '1',
                    data: { other: 'value' },
                    position: { x: 0, y: 0 },
                },
            ]);
        });
    });

    describe('removeTransientEdgeDataPropertiesFromEdges', () => {
        it('should remove transient properties from edges', () => {
            const edges = [{ id: '1', markerEnd: {}, data: {}, style: {}, selected: true }] as any;
            const result = utils.removeTransientEdgeDataPropertiesFromEdges(edges);
            expect(result).toEqual([{ id: '1' }]);
        });
    });

    describe('changeEventHandler', () => {
        it('should trigger field event on change', async () => {
            const handler = utils.changeEventHandler('screen1', 'element1');
            await handler();
            expect(triggerFieldEvent).toHaveBeenCalledWith('screen1', 'element1', 'onChange');
        });
    });

    describe('usePreviousWorkflowNodes', () => {
        it('should return previous workflow nodes', () => {
            (useFieldValue as jest.Mock).mockReturnValue({
                nodes: [{ id: '1' }, { id: '2' }],
                edges: [{ source: '1', target: '2' }],
            });
            const result = utils.usePreviousWorkflowNodes('2', 'screen1', 'element1');
            expect(result).toEqual([{ id: '1' }]);
        });
    });

    describe('useSubsequentWorkflowNodes', () => {
        it('should return subsequent workflow nodes', () => {
            (useFieldValue as jest.Mock).mockReturnValue({
                nodes: [{ id: '1' }, { id: '2' }],
                edges: [{ source: '1', target: '2' }],
            });
            const result = utils.useSubsequentWorkflowNodes('1', 'screen1', 'element1');
            expect(result).toEqual([{ id: '2' }]);
        });
    });

    describe('useSourceNode', () => {
        it('should return the source node for an edge', () => {
            (useFieldValue as jest.Mock).mockReturnValue({
                nodes: [{ id: '1' }],
                edges: [{ id: 'edge1', source: '1' }],
            });
            const result = utils.useSourceNode('edge1', 'screen1', 'element1');
            expect(result).toEqual({ id: '1' });
        });
    });

    describe('hasEdgeConnectedToNode', () => {
        it('should check if an edge is connected to a node', () => {
            const edges = [{ source: '1', target: '2' }] as any;
            const result = utils.hasEdgeConnectedToNode('1', edges);
            expect(result).toBe(true);
        });
    });

    describe('useWorkflowNodeVariables', () => {
        it('should return workflow node variables', () => {
            jest.spyOn(utils, 'usePreviousWorkflowNodes').mockReturnValue([
                {
                    id: 'node1',
                    position: { x: 0, y: 0 },
                    data: { stepVariables: [{ path: 'var1' }], outputVariables: [{ path: 'var2' }] },
                },
            ]);
            const result = utils.useWorkflowNodeVariables('1', 'screen1', 'element1');
            expect(result).toEqual({
                inputVariables: [{ path: 'var1' }, { path: 'var2' }],
                oldRootPaths: [],
            });
        });
    });

    describe('removeChildNodesAndEdgesFromStartPoint', () => {
        it('should remove child nodes and edges from a start point', () => {
            const nodes = [
                { id: '1', position: { x: 0, y: 0 }, data: {} },
                { id: '2', position: { x: 0, y: 0 }, data: {} },
                { id: '3', position: { x: 0, y: 0 }, data: {} },
            ];
            const edges = [
                { source: '1', target: '2', position: { x: 0, y: 0 }, data: {} },
                { source: '2', target: '3', position: { x: 0, y: 0 }, data: {} },
            ] as any;
            utils.removeChildNodesAndEdgesFromStartPoint('1', edges, nodes);
            expect(nodes).toEqual([]);
            expect(edges).toEqual([]);
        });
        it('should only remove the last node and edge', () => {
            const nodes = [
                { id: '1', position: { x: 0, y: 0 }, data: {} },
                { id: '2', position: { x: 0, y: 0 }, data: {} },
                { id: '3', position: { x: 0, y: 0 }, data: {} },
            ];
            const edges = [
                { source: '1', target: '2', position: { x: 0, y: 0 }, data: {} },
                { source: '2', target: '3', position: { x: 0, y: 0 }, data: {} },
            ] as any;
            utils.removeChildNodesAndEdgesFromStartPoint('3', edges, nodes);
            expect(nodes).toEqual([
                { id: '1', position: { x: 0, y: 0 }, data: {} },
                { id: '2', position: { x: 0, y: 0 }, data: {} },
            ]);
            expect(edges).toEqual([{ source: '1', target: '2', position: { x: 0, y: 0 }, data: {} }]);
        });

        it('should remove all nodes behind id 3', () => {
            const nodes = [
                { id: '1', position: { x: 0, y: 0 }, data: {} },
                { id: '2', position: { x: 0, y: 0 }, data: {} },
                { id: '3', position: { x: 0, y: 0 }, data: {} },
                { id: '4', position: { x: 0, y: 0 }, data: {} },
                { id: '5', position: { x: 0, y: 0 }, data: {} },
                { id: '6', position: { x: 0, y: 0 }, data: {} },
                { id: '7', position: { x: 0, y: 0 }, data: {} },
                { id: '8', position: { x: 0, y: 0 }, data: {} },
            ];
            const edges = [
                { source: '1', target: '2', position: { x: 0, y: 0 }, data: {} },
                { source: '2', target: '3', position: { x: 0, y: 0 }, data: {} },
                { source: '3', target: '4', position: { x: 0, y: 0 }, data: {} },
                { source: '4', target: '5', position: { x: 0, y: 0 }, data: {} },
                { source: '5', target: '6', position: { x: 0, y: 0 }, data: {} },
                { source: '6', target: '7', position: { x: 0, y: 0 }, data: {} },
                { source: '7', target: '8', position: { x: 0, y: 0 }, data: {} },
            ] as any;

            const expectedNodes = [
                { id: '1', position: { x: 0, y: 0 }, data: {} },
                { id: '2', position: { x: 0, y: 0 }, data: {} },
                { id: '3', position: { x: 0, y: 0 }, data: {} },
            ];
            const expectedEdges = [
                { source: '1', target: '2', position: { x: 0, y: 0 }, data: {} },
                { source: '2', target: '3', position: { x: 0, y: 0 }, data: {} },
            ] as any;
            utils.removeChildNodesAndEdgesFromStartPoint('4', edges, nodes);
            expect(nodes).toEqual(expectedNodes);
            expect(edges).toEqual(expectedEdges);
        });
    });

    describe('createNewNode', () => {
        it('should create a new node', () => {
            const result = utils.createNewNode({
                selectedNodeType: 'type1',
                values: { key: 'value' },
                nodes: [],
            });
            expect(result).toMatchObject({
                id: expect.any(String),
                type: 'type1',
                data: { type: 'type1', key: 'value' },
            });
        });
    });

    describe('createNewEdge', () => {
        it('should create a new edge', () => {
            const result = utils.createNewEdge({
                sourceNodeId: '1',
                targetNodeId: '2',
                edges: [],
                data: { key: 'value' },
            });
            expect(result).toMatchObject({
                id: expect.any(String),
                source: '1',
                target: '2',
                data: { key: 'value' },
            });
        });
    });

    describe('allocateId', () => {
        it('should allocate a unique ID', () => {
            const result = utils.allocateId('prefix', []);
            expect(result).toBe('prefix-1');
        });
    });

    describe('isSingleEdgeRemoval', () => {
        it('should check if a single edge is being removed', () => {
            const edges = [{ id: '1', target: '2' }] as any;
            const changes = [{ type: 'remove', id: '1' }] as any;
            const result = utils.isSingleEdgeRemoval(changes, edges);
            expect(result).toBe(true);
        });
    });

    describe('roundToNearestTwenty', () => {
        it('should round a number to the nearest 20', () => {
            const result = utils.roundToNearestTwenty(25);
            expect(result).toBe(20);
        });
        it('should round a number to the nearest 20 (higher)', () => {
            const result = utils.roundToNearestTwenty(35);
            expect(result).toBe(40);
        });
        it('should round a number to the nearest 20 (lower)', () => {
            const result = utils.roundToNearestTwenty(15);
            expect(result).toBe(20);
        });
        it('should round a negative number to the nearest 20', () => {
            const result = utils.roundToNearestTwenty(-25);
            expect(result).toBe(-20);
        });
    });
});
