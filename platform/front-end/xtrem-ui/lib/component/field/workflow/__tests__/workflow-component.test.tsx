import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import { WorkflowComponent } from '../workflow-component';
import * as abstractUtils from '../../../../utils/abstract-fields-utils';
import * as workflowUtils from '../workflow-component-utils';

jest.mock('../.././../../redux/actions/workflow-nodes-actions.ts', () => ({
    loadWorkflowNodes: jest.fn(() => ({ type: 'LOAD_WORKFLOW_NODES' })),
}));

const mockStore = configureStore([]);

describe('WorkflowInnerComponent', () => {
    const defaultProps: any = {
        value: { nodes: [], edges: [] },
        setFieldValue: jest.fn(),
        isReadOnly: false,
        isDisabled: false,
        nodes: [],
        edges: [],
        screenId: 'screen-1',
        elementId: 'element-1',
        localize: jest.fn((key: string, defaultText: string) => defaultText),
        onAddFirstElement: jest.fn(),
        onChange: jest.fn(),
        addDialogFilters: null,
        onAddNodeDialogClose: jest.fn(),
        onNewNodeAdded: jest.fn(),
        fieldProperties: { isDisabled: false, isReadOnly: false }, // Mock fieldProperties
    };

    const renderWithProvider = (ui: React.ReactElement, storeState = {}) => {
        jest.spyOn(abstractUtils, 'handleChange').mockImplementation(() => {
            // Mock implementation of handleChange
            return jest.fn();
        });
        jest.spyOn(workflowUtils, 'useSubsequentWorkflowNodes').mockReturnValue([]);
        jest.spyOn(workflowUtils, 'usePreviousWorkflowNodes').mockReturnValue([]);

        const store = mockStore({
            activeDialogs: {}, // Add a default activeDialogs property
            screenDefinitions: {
                // Mock screenDefinitions
                'screen-1': {
                    values: {
                        'element-1': {},
                    },
                },
            },
            workflowNodes: [], // Mock workflowNodes state
            ...storeState,
        });
        store.dispatch = jest.fn(); // Mock dispatch function
        return render(<Provider store={store}>{ui}</Provider>);
    };

    afterEach(() => {
        jest.clearAllMocks(); // Clear mocks after each test
    });

    it('renders empty workflow state when no nodes are present', () => {
        renderWithProvider(<WorkflowComponent {...defaultProps} />);

        expect(screen.getByText('This workflow is currently empty')).toBeInTheDocument();
        expect(screen.getByText('Add a trigger event')).toBeInTheDocument();
    });

    it('renders workflow with nodes when nodes are present', () => {
        const propsWithNodes = {
            ...defaultProps,
            value: { nodes: [{ id: 'node-1', data: {}, position: { x: 0, y: 0 } }], edges: [] },
        };

        renderWithProvider(<WorkflowComponent {...propsWithNodes} />);

        expect(screen.queryByText('This workflow is currently empty')).not.toBeInTheDocument();
    });

    it('matches snapshot with nodes and edges', () => {
        const propsWithNodesAndEdges = {
            ...defaultProps,
            value: {
                nodes: [
                    { id: 'node-1', data: { label: 'Node 1' }, position: { x: 100, y: 100 } },
                    { id: 'node-2', data: { label: 'Node 2' }, position: { x: 300, y: 100 } },
                ],
                edges: [{ id: 'edge-1', source: 'node-1', target: 'node-2', type: 'default' }],
            },
        };

        const store = mockStore({
            activeDialogs: {},
            screenDefinitions: {
                'screen-1': {
                    values: {
                        'element-1': {},
                    },
                },
            },
            workflowNodes: [],
        });

        const { container } = render(
            <Provider store={store}>
                <WorkflowComponent {...propsWithNodesAndEdges} />
            </Provider>,
        );

        expect(container).toMatchSnapshot();
    });
});
