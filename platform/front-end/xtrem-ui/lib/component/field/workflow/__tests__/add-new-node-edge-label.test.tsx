import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { AddNewNodeEdgeLabel } from '../add-new-node-edge-label';
import { WorkflowContext } from '../workflow-context-provider';

describe('AddNewNodeEdgeLabel', () => {
    it('should render correctly', () => {
        const { container } = render(<AddNewNodeEdgeLabel edgeId="test-edge" centerX={100} centerY={200} />);
        expect(container).toMatchSnapshot();
    });

    it('should display the add button on mouse enter and hide it on mouse leave', () => {
        const { container, queryByRole } = render(
            <AddNewNodeEdgeLabel edgeId="test-edge" centerX={100} centerY={200} />,
        );

        // Ensure the button is not visible initially
        expect(queryByRole('button')).toBeNull();

        // Simulate mouse enter to display the button
        fireEvent.mouseEnter(container.querySelector('.nodrag.nopan > div')!);
        expect(queryByRole('button')).toBeInTheDocument();

        // Simulate mouse leave to hide the button
        fireEvent.mouseLeave(container.querySelector('.nodrag.nopan > div')!);
        expect(queryByRole('button')).toBeNull();
    });

    it('should call context.onNodeInsertToEdge when the add button is clicked', () => {
        const mockOnNodeInsertToEdge = jest.fn();
        const contextValue = {
            onNodeInsertToEdge: mockOnNodeInsertToEdge,
            screenId: 'test-screen',
            elementId: 'test-element',
            isReadOnly: false,
            onNodeDataChange: jest.fn(),
            onNodeBelowNode: jest.fn(),
        };

        const { container, getByRole } = render(
            <WorkflowContext.Provider value={contextValue}>
                <AddNewNodeEdgeLabel edgeId="test-edge" centerX={100} centerY={200} />
            </WorkflowContext.Provider>,
        );

        // Simulate mouse enter to display the button
        fireEvent.mouseEnter(container.querySelector('.nodrag.nopan > div')!);

        // Click the button
        fireEvent.click(getByRole('button'));

        // Ensure the context function is called with the correct edgeId
        expect(mockOnNodeInsertToEdge).toHaveBeenCalledWith('test-edge');
    });

    it('should not display the add button in read-only mode', () => {
        const contextValue = {
            onNodeInsertToEdge: jest.fn(),
            screenId: 'test-screen',
            elementId: 'test-element',
            isReadOnly: true,
            onNodeDataChange: jest.fn(),
            onNodeBelowNode: jest.fn(),
        };

        const { queryByRole } = render(
            <WorkflowContext.Provider value={contextValue}>
                <AddNewNodeEdgeLabel edgeId="test-edge" centerX={100} centerY={200} />
            </WorkflowContext.Provider>,
        );

        expect(queryByRole('button')).toBeNull();
    });

    it('should render without crashing when context is not provided', () => {
        const { container } = render(<AddNewNodeEdgeLabel edgeId="test-edge" centerX={100} centerY={200} />);
        expect(container).toBeInTheDocument();
    });

    it('should render correctly when isAddButtonDisplayed is true', () => {
        const { container } = render(<AddNewNodeEdgeLabel edgeId="test-edge" centerX={100} centerY={200} />);

        // Simulate mouse enter to set isAddButtonDisplayed to true
        fireEvent.mouseEnter(container.querySelector('.nodrag.nopan > div')!);

        expect(container).toMatchSnapshot();
    });
});
