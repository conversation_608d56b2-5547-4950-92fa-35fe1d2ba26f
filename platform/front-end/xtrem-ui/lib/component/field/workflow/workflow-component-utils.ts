/* eslint-disable @typescript-eslint/no-loop-func */
import type { WorkflowVariable } from '@sage/xtrem-shared';
import { remove, uniq, uniqBy } from 'lodash';
import type { Edge, EdgeChange, EdgeRemoveChange, Node, XYPosition } from 'reactflow';
import { MarkerType } from 'reactflow';
import { triggerFieldEvent } from '../../../utils/events';
import { useFieldValue } from '../../../utils/hooks/effects/use-set-field-value';
import type { DefaultDataType, WorkflowFieldValue } from './workflow-types';

export const edgeStyle: Partial<Edge> = {
    style: {
        strokeWidth: 1,
        stroke: '#A6A6A6',
    },
    markerEnd: {
        type: MarkerType.Arrow,
        width: 12,
        height: 12,
        color: '#A6A6A6',
    },
};

export const removeTransientNodeDataProperties = (data: DefaultDataType): DefaultDataType => {
    const cleanData = { ...data };
    delete cleanData.type;
    return cleanData;
};

export const removeTransientNodeDataPropertiesFromNodes = (nodes: Node<DefaultDataType>[]): Node[] =>
    nodes.map(n => ({
        data: removeTransientNodeDataProperties(n.data),
        height: n.height,
        id: n.id,
        position: n.position,
        type: n.type,
        width: n.width,
    }));

export const removeTransientEdgeDataPropertiesFromEdges = (nodes: Edge<DefaultDataType>[]): Edge[] =>
    nodes.map(e => {
        const edgeCopy = { ...e };
        delete edgeCopy.markerEnd;
        delete edgeCopy.data;
        delete edgeCopy.style;
        delete edgeCopy.selected;
        return edgeCopy;
    });

export const changeEventHandler = (screenId: string, elementId: string) => (): Promise<void> =>
    triggerFieldEvent(screenId, elementId, 'onChange');

export const usePreviousWorkflowNodes = (
    nodeId?: string,
    screenId?: string,
    elementId?: string,
    includeCurrent = false,
): Node<DefaultDataType>[] => {
    const fieldValue = useFieldValue<WorkflowFieldValue>(screenId, elementId);
    if (!fieldValue || !nodeId) {
        return [];
    }

    const currentNode = fieldValue.nodes.find(n => n.id === nodeId);
    let currentNodeId: string | undefined = nodeId;
    const previousNodes: Node[] = includeCurrent && currentNode ? [currentNode] : [];
    // eslint-disable-next-line no-constant-condition
    while (true) {
        currentNodeId = fieldValue.edges.find(e => e.target === currentNodeId)?.source;

        if (!currentNodeId) {
            break;
        }

        const node = fieldValue.nodes.find(n => n.id === currentNodeId);
        if (!node) {
            break;
        }

        // If the node is already in the list, we have a circular reference
        if (previousNodes.find(n => n.id === node.id)) {
            break;
        }

        previousNodes.push(node);
    }

    return previousNodes.reverse();
};

export const useSubsequentWorkflowNodes = (
    nodeId?: string,
    screenId?: string,
    elementId?: string,
    includeCurrent = false,
): Node<DefaultDataType>[] => {
    const fieldValue = useFieldValue<WorkflowFieldValue>(screenId, elementId);
    if (!fieldValue || !nodeId) {
        return [];
    }

    const currentNode = fieldValue.nodes.find(n => n.id === nodeId);
    let currentNodeId: string | undefined = nodeId;
    const subsequentNodes: Node[] = includeCurrent && currentNode ? [currentNode] : [];
    // eslint-disable-next-line no-constant-condition
    while (true) {
        currentNodeId = fieldValue.edges.find(e => e.source === currentNodeId)?.target;

        if (!currentNodeId) {
            break;
        }

        const node = fieldValue.nodes.find(n => n.id === currentNodeId);
        if (!node) {
            break;
        }

        // If the node is already in the list, we have a circular reference
        if (subsequentNodes.find(n => n.id === node.id)) {
            break;
        }

        subsequentNodes.push(node);
    }

    return subsequentNodes.reverse();
};

export const useSourceNode = (edgeId: string, screenId?: string, elementId?: string): Node<DefaultDataType> | null => {
    const fieldValue = useFieldValue<WorkflowFieldValue>(screenId, elementId);
    if (!fieldValue) {
        return null;
    }

    const edge = fieldValue?.edges.find(e => e.id === edgeId);
    if (!edge) {
        return null;
    }

    return fieldValue.nodes.find(n => n.id === edge.source) || null;
};

export const hasEdgeConnectedToNode = (nodeId: string, edges: Edge[], sourceHandle?: string): boolean => {
    const connectedEdges = edges.filter(e => e.source === nodeId || e.target === nodeId);
    if (sourceHandle) {
        return connectedEdges.some(e => e.sourceHandle === sourceHandle);
    }
    return connectedEdges.length > 0;
};

export const useWorkflowNodeVariables = (
    nodeId?: string,
    screenId?: string,
    elementId?: string,
    includeCurrent = false,
): { inputVariables: WorkflowVariable[]; oldRootPaths: string[] } => {
    const previousNodes = usePreviousWorkflowNodes(nodeId, screenId, elementId, includeCurrent);
    const collected = { inputVariables: [] as WorkflowVariable[], oldRootPaths: [] as string[] };
    removeTransientNodeDataPropertiesFromNodes(previousNodes).forEach((node: Node<DefaultDataType>) => {
        collected.inputVariables.push(...(node.data.stepVariables ?? []));
        const outputVariables = node.data.outputVariables;
        if (outputVariables && outputVariables.length > 0) {
            // Compatibility code: formerly the step could have an array of outputVariables
            collected.inputVariables.push(...outputVariables);
        }
        delete node.data.outputVariables;
        if (node.data.oldRootPaths) {
            collected.oldRootPaths.push(...node.data.oldRootPaths);
        }
    });

    return {
        inputVariables: uniqBy(collected.inputVariables, 'path'),
        oldRootPaths: uniq(collected.oldRootPaths),
    };
};

export function removeChildNodesAndEdgesFromStartPoint(nodeId: string, tempEdges: Edge[], tempNodes: Node[]): void {
    // Remove the node from tempNodes
    remove(tempNodes, n => n.id === nodeId);

    // Find all edges connected to the node (either as source or target)
    const connectedEdges = tempEdges.filter(e => e.source === nodeId || e.target === nodeId);
    // eslint-disable-next-line no-restricted-syntax
    for (const edge of connectedEdges) {
        // If the node is the source, recursively remove the target node
        if (edge.source === nodeId) {
            const targetNode = tempNodes.find(n => n.id === edge.target);
            const edgesToTarget = tempEdges.filter(e => e.target === edge.target);
            if (edgesToTarget.length === 1 && targetNode) {
                removeChildNodesAndEdgesFromStartPoint(edge.target, tempEdges, tempNodes);
            }
        }
    }

    // Remove edges where both source and target nodes are no longer in tempNodes
    remove(tempEdges, e => !tempNodes.find(n => n.id === e.source) || !tempNodes.find(n => n.id === e.target));
}

export function createNewNode({
    selectedNodeType,
    values,
    nodes,
    position = { x: 20, y: 20 },
}: {
    selectedNodeType: string;
    nodes: Node[];
    values: any;
    position?: XYPosition;
}): Node {
    return {
        id: allocateId(selectedNodeType, nodes),
        position,
        type: selectedNodeType,
        data: {
            type: selectedNodeType,
            ...values,
        },
    };
}

export function createNewEdge({
    sourceNodeId,
    sourceHandleId,
    edges,
    targetNodeId,
    data,
}: {
    sourceNodeId: string;
    sourceHandleId?: string | null;
    edges: Edge[];
    targetNodeId: string;
    data: any;
}): Edge {
    return {
        id: allocateId(`${sourceNodeId}--${sourceHandleId}`, edges),
        source: sourceNodeId,
        sourceHandle: sourceHandleId,
        target: targetNodeId,
        data,
        ...edgeStyle,
    };
}

export function allocateId(prefix: string, items: { id: string }[]): string {
    for (let i = 1; i < 1000; i += 1) {
        const id = `${prefix}-${i}`;
        if (!items.find(n => n.id === id)) return id;
    }
    throw new Error('Could not find a unique id');
}

export function isSingleEdgeRemoval(change: EdgeChange[], edges: Edge[]): boolean {
    const removalChange = change.find(c => c.type === 'remove') as EdgeRemoveChange;
    if (!removalChange) {
        return false;
    }
    const edgeToRemove = edges.find(e => e.id === removalChange.id);
    const edgesTargetingNode = edgeToRemove ? edges.filter(e => e.target === edgeToRemove.target) : [];
    return edgesTargetingNode.length < 2;
}

export function roundToNearestTwenty(num: number): number {
    return Math.round(num / 20) * 20;
}
