import * as React from 'react';
import Button from 'carbon-react/esm/components/button';
import Form from 'carbon-react/esm/components/form';
import { WorkflowNodeSelectorComponent } from './workflow-node-selector-component';
import { StepSequence, StepSequenceItem } from 'carbon-react/esm/components/step-sequence';
import Typography from 'carbon-react/esm/components/typography';
import type { XtremAppState } from '../../../redux/state';
import type { WorkflowNode } from '@sage/xtrem-shared';
import { ScreenComponent } from '../../ui/screen-component';
import { useWorkflowNodeVariables } from './workflow-component-utils';
import BusinessActions from '../../container/footer/business-actions';
import { ContextType } from '../../../types';
import type { PageDefinition } from '../../../service/page-definition';
import { getArtifactDescription } from '../../../utils/transformers';
import { getPageTitlesFromPageDefinition } from '../../../utils/page-utils';
import { localize } from '../../../service/i18n-service';
import Sidebar from 'carbon-react/esm/components/sidebar';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

export interface AddWorkflowNodeDialogProps {
    previousNodeId?: string;
    screenId: string;
    elementId: string;
    isOpen: boolean;
    onClose: () => void;
    onConfirm: (result: { selectedNodeType: string; values: any }) => void;
    filterType?: string[];
}

export function AddWorkflowNodeDialog({
    isOpen,
    onClose,
    onConfirm,
    previousNodeId,
    screenId,
    elementId,
    filterType,
}: AddWorkflowNodeDialogProps): React.ReactElement {
    const workflowNodes = useDeepEqualSelector<XtremAppState, WorkflowNode[] | null>(s => s.workflowNodes);
    const { inputVariables, oldRootPaths } = useWorkflowNodeVariables(previousNodeId, screenId, elementId, true);
    const [selectedNodeType, setSelectedNodeType] = React.useState<string | null>(null);
    const [step, setStep] = React.useState<number>(1);
    const selectedWorkflowType = workflowNodes?.find(n => n.key === selectedNodeType);
    const defaultConfig = JSON.parse(selectedWorkflowType?.defaultConfig || '{}');
    const configPageArtifactDescription = selectedWorkflowType
        ? getArtifactDescription(selectedWorkflowType.configurationPage)
        : null;

    const configPageDefinition = useDeepEqualSelector<XtremAppState, PageDefinition | null>(s => {
        if (!configPageArtifactDescription) {
            return null;
        }
        return (s.screenDefinitions[configPageArtifactDescription.name] as PageDefinition) || null;
    });

    const availableWorkflowNodes = React.useMemo(() => {
        return (workflowNodes || []).filter(n => !filterType || filterType.indexOf(n.type) !== -1);
    }, [filterType, workflowNodes]);

    const onWorkflowConfigPageFinish = React.useCallback(
        (values: any) => {
            if (selectedNodeType) {
                onConfirm({ selectedNodeType, values });
            }
        },
        [onConfirm, selectedNodeType],
    );

    const onPositiveButtonClick = React.useCallback(() => {
        if (step === 1) {
            setStep(2);
        }
    }, [step]);

    const onNegativeButtonClick = React.useCallback(() => {
        if (step === 1) {
            onClose();
        }
        if (step === 2) {
            setStep(1);
        }
    }, [onClose, step]);

    React.useEffect(() => {
        setStep(1);
        setSelectedNodeType(null);
    }, [isOpen]);

    React.useEffect(() => {
        if (availableWorkflowNodes?.length === 1 && step === 1) {
            setStep(2);
            setSelectedNodeType(availableWorkflowNodes[0].key);
        }
    }, [availableWorkflowNodes, step]);

    const dialogTitle = React.useMemo(() => {
        if (!configPageDefinition || step === 1) {
            if (filterType?.length === 1 && filterType[0] === 'event') {
                return localize('@sage/xtrem-ui/workflow-component-wizard-title-trigger', 'Trigger gallery');
            }
            return localize('@sage/xtrem-ui/workflow-component-wizard-title-action', 'Action gallery');
        }

        const { title } = getPageTitlesFromPageDefinition(configPageDefinition, 'en-US');
        return title;
    }, [configPageDefinition, filterType, step]);

    const hiddenCompleteLabel = localize('@sage/xtrem-ui/step-sequence-item-aria-complete', 'Complete');
    const hiddenCurrentLabel = localize('@sage/xtrem-ui/step-sequence-item-aria-current', 'Current');

    return (
        <Sidebar
            open={isOpen}
            onCancel={onClose}
            enableBackgroundUI={true}
            size="extra-large"
            data-element="workflow-dialog"
            header={
                <div>
                    <Typography variant="h1">{dialogTitle}</Typography>
                    {availableWorkflowNodes?.length !== 1 && (
                        <StepSequence mb="0" mt="16px">
                            <StepSequenceItem
                                aria-label={localize(
                                    '@sage/xtrem-ui/step-sequence-item-aria-count',
                                    'Step {{0}} of {{1}}',
                                    [1, 2],
                                )}
                                hiddenCompleteLabel={hiddenCompleteLabel}
                                hiddenCurrentLabel={hiddenCurrentLabel}
                                indicator="1"
                                status={step === 1 ? 'current' : 'complete'}
                            >
                                {filterType?.length === 1 && filterType[0] === 'event'
                                    ? localize(
                                          '@sage/xtrem-ui/workflow-component-wizard-trigger-selection',
                                          'Trigger selection',
                                      )
                                    : localize(
                                          '@sage/xtrem-ui/workflow-component-wizard-event-selection',
                                          'Event selection',
                                      )}
                            </StepSequenceItem>
                            <StepSequenceItem
                                aria-label={localize(
                                    '@sage/xtrem-ui/step-sequence-item-aria-count',
                                    'Step {{0}} of {{1}}',
                                    [2, 2],
                                )}
                                hiddenCompleteLabel={hiddenCompleteLabel}
                                hiddenCurrentLabel={hiddenCurrentLabel}
                                indicator="2"
                                status={step === 1 ? 'incomplete' : 'current'}
                            >
                                {localize(
                                    '@sage/xtrem-ui/workflow-component-wizard-step-configuration',
                                    'Configuration',
                                )}
                            </StepSequenceItem>
                        </StepSequence>
                    )}
                </div>
            }
        >
            <Form
                data-testid="e-workflow-node-dialog-form"
                stickyFooter={true}
                height="500px"
                leftSideButtons={
                    availableWorkflowNodes?.length > 1 && (
                        <Button onClick={onNegativeButtonClick}>
                            {step === 1
                                ? localize('@sage/xtrem-ui/cancel', 'Cancel')
                                : localize('@sage/xtrem-ui/wizard-previous', 'Previous')}
                        </Button>
                    )
                }
                saveButton={
                    configPageDefinition && step === 2 ? (
                        <BusinessActions
                            contextType={ContextType.dialog}
                            defaultButtonType="primary"
                            screenId={configPageDefinition.metadata.screenId}
                            businessActions={configPageDefinition.page.$.businessActions}
                        />
                    ) : (
                        <Button
                            buttonType="primary"
                            type="button"
                            onClick={onPositiveButtonClick}
                            disabled={!selectedNodeType || step === 2}
                        >
                            {localize('@sage/xtrem-ui/wizard-next', 'Next')}
                        </Button>
                    )
                }
            >
                {step === 1 && (
                    <div className="e-workflow-add-dialog">
                        <WorkflowNodeSelectorComponent
                            onChange={setSelectedNodeType}
                            value={selectedNodeType}
                            workflowNodes={availableWorkflowNodes}
                        />
                    </div>
                )}
                {step === 2 && selectedWorkflowType && (
                    <ScreenComponent
                        screenPath={selectedWorkflowType.configurationPage}
                        queryParameters={{ inputVariables, oldRootPaths }}
                        values={{ ...defaultConfig }}
                        onFinish={onWorkflowConfigPageFinish}
                    />
                )}
            </Form>
        </Sidebar>
    );
}
