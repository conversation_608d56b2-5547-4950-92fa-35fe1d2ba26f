import * as React from 'react';
import { connect } from 'react-redux';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { DecoupledEditor } from '@ckeditor/ckeditor5-editor-decoupled';
import { Essentials } from '@ckeditor/ckeditor5-essentials';
import { Bold, Code, Italic, Strikethrough, Subscript, Superscript, Underline } from '@ckeditor/ckeditor5-basic-styles';
import { Alignment } from '@ckeditor/ckeditor5-alignment';
import { Font } from '@ckeditor/ckeditor5-font';
import { Paragraph } from '@ckeditor/ckeditor5-paragraph';
import { List } from '@ckeditor/ckeditor5-list';
import { Table, TableCellProperties, TableProperties, TableToolbar } from '@ckeditor/ckeditor5-table';
import type {
    RichTextComponentProps,
    RichTextProperties,
    RichTextComponentState,
    RichTextEditorCapabilities,
} from './rich-text-types';
import sanitizeHtml from 'sanitize-html';
import { ContextType } from '../../../types';
import { handleChange } from '../../../utils/abstract-fields-utils';

export class RichTextComponent extends EditableFieldBaseComponent<
    RichTextProperties,
    { value: string },
    {},
    RichTextComponentState
> {
    private readonly toolbarContainer = React.createRef<HTMLDivElement>();

    constructor(props: RichTextComponentProps) {
        super(props);
        const value = this.props.value && this.props.value.value ? this.props.value.value : '';
        this.state = { value };
    }

    getToolbarItems(): string[] {
        if (!this.props.fieldProperties.capabilities) {
            return [];
        }

        return this.props.fieldProperties.capabilities
            .map((c: RichTextEditorCapabilities): string | string[] => {
                switch (c) {
                    case 'indentation':
                        return ['outdent', 'indent'];
                    case 'history':
                        return ['undo', 'redo'];
                    case 'lists':
                        return ['bulletedList', 'numberedList'];
                    case 'table':
                        return 'insertTable';
                    default:
                        return c;
                }
            })
            .flat();
    }

    onBlur = (): void => {
        if ((this.props.value?.value || null) !== (this.state.value || null)) {
            handleChange(
                this.props.elementId,
                this.state.value ? { value: this.state.value } : null,
                this.props.setFieldValue,
                this.props.validate,
                this.triggerChangeListener,
            );
        }
    };

    onChange = (value: string): void => {
        this.setState({ value });
    };

    onKeyDown = (data: any): void => {
        if (data.keyCode === 27) {
            this.setState({ value: this.props.value?.value || '' });
        }
    };

    UNSAFE_componentWillReceiveProps(nextProps: RichTextComponentProps): void {
        const value = nextProps.value?.value;
        if ((this.state.value || null) !== (value || null)) {
            const newValue = value || '';
            this.setState({ value: newValue });
        }
    }

    render(): React.ReactNode {
        const inlineStyle: React.CSSProperties = { minWidth: '100%', minHeight: '300px', width: '100%' };
        const { height, isTitleHidden } = this.props.fieldProperties;

        return (
            <div
                {...this.getBaseAttributesDivWrapper(
                    'rich-text',
                    `e-rich-text-field${this.isDisabled() ? ' e-rich-text-field-disabled' : ''}`,
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    this.props.isNested,
                )}
                style={inlineStyle}
            >
                {this.getTitle() && !isTitleHidden && (
                    <FieldLabel label={this.getTitle()} errorMessage={this.props.validationErrors?.[0]?.message} />
                )}
                {this.isReadOnly() ? (
                    <div
                        className="e-field-read-only ck-content"
                        style={{ height }}
                        // eslint-disable-next-line react/no-danger
                        dangerouslySetInnerHTML={{
                            __html: sanitizeHtml(this.state.value, {
                                allowedStyles: {},
                                allowedAttributes: {
                                    '*': ['style', 'class'],
                                },
                            }),
                        }}
                    />
                ) : (
                    <>
                        <div
                            className="e-rich-text-toolbar-container"
                            data-testid="e-rich-text-toolbar-container"
                            ref={this.toolbarContainer}
                        />
                        <div className="e-rich-text-editor-container">
                            <CKEditor
                                config={{
                                    language: this.props.locale.substring(0, 2),
                                    toolbar: this.getToolbarItems(),
                                    plugins: [
                                        Essentials,
                                        Bold,
                                        Code,
                                        Italic,
                                        Strikethrough,
                                        Subscript,
                                        Superscript,
                                        Underline,
                                        Alignment,
                                        Font,
                                        Paragraph,
                                        List,
                                        Table,
                                        TableCellProperties,
                                        TableProperties,
                                        TableToolbar,
                                    ],
                                    fontSize: {
                                        options: [
                                            {
                                                title: '8',
                                                model: '8pt',
                                            },
                                            {
                                                title: '10',
                                                model: '10pt',
                                            },
                                            {
                                                title: '12',
                                                model: '12pt',
                                            },
                                            {
                                                title: 'default',
                                                model: 'default',
                                            },
                                            {
                                                title: '18',
                                                model: '18pt',
                                            },
                                            {
                                                title: '24',
                                                model: '24pt',
                                            },
                                            {
                                                title: '32',
                                                model: '32pt',
                                            },
                                        ],
                                    },
                                    fontFamily: {
                                        options: [
                                            'Arial',
                                            'Verdana',
                                            'Helvetica',
                                            'Tahoma',
                                            'Trebuchet MS',
                                            'Times New Roman',
                                            'Georgia',
                                            'Garamond',
                                            'Courier New',
                                            'Brush Script MT',
                                        ],
                                    },
                                }}
                                onReady={(editor: DecoupledEditor): void => {
                                    if (this.toolbarContainer.current && editor.ui.view.toolbar.element) {
                                        this.toolbarContainer.current.appendChild(editor.ui.view.toolbar.element);
                                    }
                                    editor.editing.view.document.on('keydown', (_evt, data) => this.onKeyDown(data));
                                    editor.editing.view.change(writer => {
                                        const rootDocument = editor.editing.view.document?.getRoot();
                                        if (rootDocument && height) {
                                            writer.setStyle('height', height, rootDocument);
                                        }
                                    });
                                    if (this.props.contextType === ContextType.dialog) {
                                        const linkPlugin = editor.plugins.get('ContextualBalloon');
                                        if (linkPlugin.view.element) {
                                            linkPlugin.view.element.style.zIndex = '3000';
                                        }
                                    }
                                }}
                                editor={DecoupledEditor}
                                data={this.state.value}
                                disabled={this.isDisabled()}
                                onChange={(_: any, editor: DecoupledEditor): void => this.onChange(editor.getData())}
                                onBlur={this.onBlur}
                            />
                        </div>
                    </>
                )}
                {!this.props.validationErrors && this.props.fieldProperties.helperText && (
                    <HelperText helperText={this.props.fieldProperties.helperText} />
                )}
            </div>
        );
    }
}

export const ConnectedRichTextComponent = connect(mapStateToProps(), mapDispatchToProps())(RichTextComponent);

export default ConnectedRichTextComponent;
