import type { ScreenBase } from '../../../service/screen-base';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type { Changeable, Clickable, ExtensionField, HasParent } from '../traits';
import type { BlockControlObject, SectionControlObject } from '../../control-objects';
import type { FieldControlObjectInstance } from '../../types';

export interface RichTextDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<RichTextProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        Changeable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>> {}

export interface RichTextProperties<CT extends ScreenBase = ScreenBase> extends EditableFieldProperties<CT> {
    /** The value of the height attribute. Minimum height should be at least the height of the top edit panel and a line.  */
    height?: string;
    /** List of available text editing capabilities, e.g bold text, font size, font color. If not set all capabilities are enabled.  */
    capabilities?: RichTextEditorCapabilities[];
}

export type RichTextComponentProps = BaseEditableComponentProperties<RichTextProperties, { value: string }>;

export interface RichTextComponentState {
    value: string;
}

export type RichTextEditorCapabilities =
    | 'bold'
    | 'italic'
    | 'underline'
    | 'strikethrough'
    | 'history'
    | 'heading'
    | 'fontColor'
    | 'fontBackgroundColor'
    | 'fontSize'
    | 'fontFamily'
    | 'table'
    | 'lists'
    | 'alignment'
    | 'indentation'
    | 'link';
