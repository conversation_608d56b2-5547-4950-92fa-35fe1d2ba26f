/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { RichTextProperties } from './rich-text-types';

/**
 * [Field]{@link EditableFieldControlObject} that holds a rich, formatted text value
 */
export class RichTextControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.RichText,
    FieldComponentProps<FieldKey.RichText>
> {
    static readonly defaultUiProperties: Partial<RichTextProperties> = {
        ...EditableFieldControlObject.defaultUiProperties,
        capabilities: [
            'bold',
            'italic',
            'underline',
            'strikethrough',
            'history',
            'heading',
            'fontColor',
            'fontBackgroundColor',
            'fontSize',
            'fontFamily',
            'table',
            'lists',
            'alignment',
            'indentation',
            'link',
        ],
        height: '300px',
    };

    @ControlObjectProperty<RichTextProperties<CT>, RichTextControlObject<CT>>()
    /** The value of the height attribute of the HTML image (e.g. 100px, 75%, auto, etc.) */
    height?: string;

    /** Field's value */
    get value(): string | null {
        const value = this._getValue() as any;
        return value ? value.value : null;
    }

    /** Field's value */
    set value(newValue: string) {
        this._setValue({ value: newValue } as any);
    }
}
