/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { RichTextControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { RichTextDecoratorProperties } from './rich-text-types';

class RichTextDecorator extends AbstractFieldDecorator<FieldKey.RichText> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = RichTextControlObject;
}
/**
 * Initializes the decorated member as a [RichText]{@link RichTextControlObject} field with the provided properties
 *
 * @param properties The properties that the [RichText]{@link RichTextControlObject} field will be initialized with
 */
export function richTextField<T extends ScreenExtension<T>>(
    properties: RichTextDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.RichText>(properties, RichTextDecorator, FieldKey.RichText);
}

export function richTextFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<RichTextDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.RichText>(properties);
}
