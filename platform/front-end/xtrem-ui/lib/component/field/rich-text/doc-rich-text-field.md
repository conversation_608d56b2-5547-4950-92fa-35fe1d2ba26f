PATH: XTREEM/UI+Field+Widgets/RichText+Field

## Introduction

The rich text field represents the display and editor of formatted text in the user interface. The field is also available as an extended field.

## Example

```ts
@ui.decorators.richTextField<Page>({
    height: '300px',
    helperText: 'Click the fields icon to launch a date picker.',
    isDisabled: false,
    isFullWidth: false,
    isHelperTextHidden: false,
    isHidden: false,
    isMandatory: true,
    isReadOnly: false,
    isTitleHidden: false,
    isTransient: false,
    title: 'RichText Field',
    width: 'medium',
    onChange() {
        console.log(`Do something when field's value has changed.`);
    },
    onClick() {
        console.log(`Do something when field is clicked.`);
    },
    parent() {
        return this.block;
    },
})
field: ui.fields.RichtText;
```

```ts
@ui.decorators.richTextField<Page>({
    ...,
    title: 'Extended RichText Field',
    insertBefore() {
        return this.field;
    },
    parent() {
        return this.block;
    },
})
extensions: ui.fields.RichText;
```

## Decorator Properties

#### Display Properties

-   **height**: Determines the fields HTML height attributes (e.g. 500px, 50%, etc.).
-   **helperText**: The helper text displayed below the field. This property is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.
-   **isHelperTextHidden**: Determines whether the field's helper text is displayed or not.
-   **isHidden**: Determines whether the field is displayed or not.
-   **isTitleHidden**: Determines whether the field's title is displayed or not.
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **capabilities**: The list of formatting features, if not set all features are enabled by default. Please see the list for all capabilities below.

#### Binding Properties

-   **bind**: Determines the associated GraphQL node's property the field's value will be bound to.
-   **isTransient**: Determines whether the field will be excluded from the automatic data binding process or not.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.

#### Validation Properties

-   **isMandatory**: Determines whether the field triggers a validation error when left empty. It can also be defined as callback function that returns a boolean.
-   **isReadOnly**: Determines whether the field should be rendered as an editable field or as a formatted label. It can also be defined as callback function that returns a boolean.

#### Event Handler Properties

-   **onChange**: Handler triggered when the field's value changes.
-   **onClick**: Handler triggered when the field is clicked.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

#### Formatting capabilities
-   **bold**: Bold text
-   **italic**: Italic text
-   **underline**: Underlined text
-   **strikethrough**: Strike-through text
-   **history**: Undo/redo functionality
-   **heading**: Various heading styles such as `h1`, `h2`, `h3`...
-   **fontColor**: Font color
-   **fontBackgroundColor**: Font background color
-   **fontSize**: Font size
-   **fontFamily**: Font family
-   **table**: Insert tables
-   **lists**: Insert various lists such as ordered lists, unordered lists
-   **alignment**: Text alignment such as left or right
-   **indentation**: Indention for paragraph and lists
-   **link**: Links

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/RichText).

#### Runtime Functions

-   **refresh()**: Refetches the field's values from the server and updates the user interface.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).
