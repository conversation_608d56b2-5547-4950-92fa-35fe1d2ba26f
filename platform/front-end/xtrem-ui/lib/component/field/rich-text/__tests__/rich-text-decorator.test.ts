import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { RichTextProperties } from '../../field-properties';
import type { RichTextDecoratorProperties } from '../rich-text-types';
import { richTextField } from '../rich-text-decorator';

describe('RichText decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'richTextField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        let pageMetadataInstance: pageMetaData.PageMetadata;

        beforeEach(() => {
            pageMetadataInstance = getMockPageMetadata();
            jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadataInstance);
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should set default values when no component properties provided', () => {
            richTextField({})({} as Page, fieldId);
            pageMetadataInstance.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties = pageMetadataInstance.uiComponentProperties[
                fieldId
            ] as RichTextDecoratorProperties<Page>;
            expect(mappedComponentProperties.height).toEqual('300px');
            expect(mappedComponentProperties.onClick).toBeUndefined();
            expect(mappedComponentProperties.onChange).toBeUndefined();
            expect(mappedComponentProperties.capabilities).toEqual([
                'bold',
                'italic',
                'underline',
                'strikethrough',
                'history',
                'heading',
                'fontColor',
                'fontBackgroundColor',
                'fontSize',
                'fontFamily',
                'table',
                'lists',
                'alignment',
                'indentation',
                'link',
            ]);
        });

        it('should set values when component properties provided', () => {
            richTextField({ height: '520px' })({} as Page, fieldId);
            pageMetadataInstance.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties = pageMetadataInstance.uiComponentProperties[fieldId] as RichTextProperties;
            expect(mappedComponentProperties.height).toEqual('520px');
        });

        it('should assign onClick handler', () => {
            testOnClickHandler(richTextField, pageMetadataInstance, fieldId);
        });
    });
});
