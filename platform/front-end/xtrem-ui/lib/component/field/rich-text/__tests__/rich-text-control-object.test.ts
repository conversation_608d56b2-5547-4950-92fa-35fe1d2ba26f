import { <PERSON><PERSON>ey } from '../../../types';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import type { RichTextProperties } from '../../../control-objects';
import { RichTextControlObject } from '../../../control-objects';

describe('RichText Field Control Object', () => {
    const screenId = 'TestPage';
    let richTextValue: string;
    let richTextProperties: RichTextProperties;
    let richTextControlObject: RichTextControlObject;

    beforeEach(() => {
        richTextProperties = {
            title: 'TEST_FIELD_TITLE',
            height: '900px',
        };
        richTextValue = '<p style="text-align:center;"><span style="color: rgb(255, 0, 0)">hello</span></p>';
        richTextControlObject = createFieldControlObject<FieldKey.RichText>(
            FieldKey.RichText,
            screenId,
            RichTextControlObject,
            'test',
            { value: richTextValue } as any,
            richTextProperties,
        );
    });

    describe('getting default values', () => {
        it('should get field value', () => {
            expect(richTextControlObject.value).toEqual(richTextValue);
        });
        it('should get field title', () => {
            expect(richTextControlObject.title).toEqual(richTextProperties.title);
        });
    });

    describe('setting and getting updated values', () => {
        it('should set the title', () => {
            const testFixture = 'Test Title';
            expect(richTextControlObject.title).not.toEqual(testFixture);
            richTextControlObject.title = testFixture;
            expect(richTextControlObject.title).toEqual(testFixture);
        });

        it('should set the height', () => {
            const testFixture = '150px';
            expect(richTextControlObject.height).toEqual(richTextProperties.height);
            expect(richTextControlObject.height).not.toEqual(testFixture);
            richTextControlObject.height = testFixture;
            expect(richTextControlObject.height).toEqual(testFixture);
        });
    });
});
