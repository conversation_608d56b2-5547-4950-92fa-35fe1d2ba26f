import * as React from 'react';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { RichTextComponentProps } from './rich-text-types';

const ConnectedRichTextComponent = React.lazy(() => import('./rich-text-component'));

export function AsyncConnectedRichTextComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="200px" />}>
            <ConnectedRichTextComponent {...props} />
        </React.Suspense>
    );
}

const RichTextComponent = React.lazy(() =>
    import('./rich-text-component').then(c => ({ default: c.RichTextComponent })),
);

export function AsyncRichTextComponent(props: RichTextComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={true} bodyHeight="200px" />}>
            <RichTextComponent {...props} />
        </React.Suspense>
    );
}
