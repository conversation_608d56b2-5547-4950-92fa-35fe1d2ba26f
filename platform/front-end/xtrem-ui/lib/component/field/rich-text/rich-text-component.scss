@import './ck-editor.scss';

.e-rich-text-field {

    .e-rich-text-toolbar-container .ck.ck-toolbar {
        border-radius: 0;
        border-top: 1px solid var(--colorsUtilityMajor300);
        border-left: 1px solid var(--colorsUtilityMajor300);
        border-right: 1px solid var(--colorsUtilityMajor300);
        border-bottom: none;

        .ck-icon.ck-button__icon {
            color: var(--colorsYin090);
        }
    }

    .e-rich-text-editor-container {
        background: var(--colorsYang100);

        .ck.ck-content.ck-editor__editable_inline {
            border: 1px solid var(--colorsUtilityMajor300);
            border-radius: 0;

            &.ck-read-only {
                background: var(--colorsUtilityDisabled400);
                border: 1px solid var(--colorsUtilityDisabled600);
                pointer-events: none;
            }

            &.ck-focused {
                border: 1px solid var(--colorsUtilityMajor300);
                box-shadow: 0 0 0 2px var(--colorsSemanticFocus500);
            }
        }
    }

    a {
        color: var(--colorsActionMajor500);
    }

    &.e-disabled .e-rich-text-toolbar-container .ck.ck-toolbar {
        pointer-events: none;
        border-top: 1px solid var(--colorsUtilityDisabled600);
        border-left: 1px solid var(--colorsUtilityDisabled600);
        border-right: 1px solid var(--colorsUtilityDisabled600);
    }

    .e-field-read-only {
        border: 1px solid var(--colorsUtilityDisabled600);
        padding: 0 var(--ck-spacing-standard);
        overflow-y: auto;

        p {
            word-break: break-all;
            white-space: normal;
        }
    }

    .ck.ck-powered-by {
        display: none !important;
    }

}