import ButtonMinor from 'carbon-react/esm/components/button-minor';
import * as React from 'react';
import { localize } from '../../../service/i18n-service';
import Textbox from 'carbon-react/esm/components/textbox';
import type { IconType } from '@sage/xtrem-shared';
import type { SizeOptions } from 'carbon-react/esm/components/button/button.component';
import Icon from 'carbon-react/esm/components/icon';

export function DynamicSelectInput({
    disabled,
    error,
    helperText,
    icon,
    info,
    inputRef,
    label,
    lookupButtonEvents,
    mode = 'select',
    onBlur,
    onChange,
    onClick,
    onFocus,
    onKeyDown,
    readOnly,
    size,
    value,
    warning,
}: {
    disabled?: boolean;
    error?: string;
    helperText?: string;
    icon: IconType;
    info?: string;
    inputRef: React.RefObject<HTMLInputElement>;
    label: string;
    mode: 'select' | 'input';
    onBlur?: (ev: React.FocusEvent<HTMLInputElement>) => void;
    onChange?: (ev: React.ChangeEvent<HTMLInputElement>) => void;
    onClick?: (ev: React.MouseEvent<HTMLButtonElement>) => void;
    onFocus?: (ev: React.FocusEvent<HTMLInputElement>) => void;
    onKeyDown?: (ev: React.KeyboardEvent<HTMLInputElement>) => void;
    readOnly?: boolean;
    size?: SizeOptions;
    value: string | undefined;
    warning?: string;
    lookupButtonEvents: {
        onFocus: () => void;
        onBlur: () => void;
        onClick: () => void;
        lookupIconId: string;
        lookupButtonRef: React.RefObject<HTMLButtonElement>;
    };
}): React.ReactElement {
    const children = (
        <div className="e-ui-select-lookup-button">
            <ButtonMinor
                {...lookupButtonEvents}
                buttonType="tertiary"
                data-component-size={size}
                data-testid="e-ui-dynamic-select-open-button"
                iconType={icon}
                size="medium"
                aria-label={localize('@sage/xtrem-ui/open-dynamic-select', 'Open list')}
                iconTooltipMessage={localize('@sage/xtrem-ui/open-dynamic-select', 'Open list')}
            />
        </div>
    );

    const getWrapperClassNames = (): string => {
        const classes = ['e-ui-select-input-wrapper'];

        if (!readOnly && !disabled) {
            classes.push('e-ui-select-search-override');
        }

        return classes.join(' ');
    };

    const getClassNames = (): string => {
        const classes = ['e-field-select-input-text'];

        if (error) {
            classes.push('e-field-error-message-carbon');
        }

        return classes.join(' ');
    };

    return (
        <div className={getWrapperClassNames()}>
            <Textbox
                onChange={onChange}
                onBlur={onBlur}
                m={0}
                aria-label={label}
                className={getClassNames()}
                data-testid="e-ui-select-input"
                disabled={disabled}
                error={disabled ? undefined : error}
                warning={disabled ? undefined : warning}
                info={disabled ? undefined : info}
                onFocus={onFocus}
                onClick={onClick}
                onKeyDown={onKeyDown}
                fieldHelp={helperText}
                id="e-ui-select-input"
                ref={inputRef}
                readOnly={mode === 'select' ? true : readOnly}
                label={label}
                size={size}
                validationOnLabel={true}
                value={value}
            >
                {mode === 'select' && (
                    <span
                        data-testid="e-ui-dynamic-input-chevron"
                        className="e-ui-select-inline-dropdown"
                        {...lookupButtonEvents}
                    >
                        <Icon type="caret_down" />
                    </span>
                )}
                {mode === 'input' && children}
            </Textbox>
        </div>
    );
}
