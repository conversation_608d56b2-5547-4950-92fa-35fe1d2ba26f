import * as React from 'react';
import type { Store } from 'redux';
import type { AppAction, XtremAppState } from '../../../../redux';
import type { DynamicSelectProperties } from '../dynamic-select-types';
import type { ScreenBase } from '../../../../service/screen-base';
import { cleanup, render, screen, waitFor, fireEvent } from '@testing-library/react';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import type { PageDefinition } from '../../../../service/page-definition';
import { FieldKey } from '@sage/xtrem-shared';
import type { PageMetadata } from '../../../../service/page-metadata';
import { ThemeProvider } from 'styled-components';
import baseTheme from 'carbon-react/esm/style/themes/sage';
import { Provider } from 'react-redux';
import ConnectedDynamicSelectComponent from '../dynamic-select-component';
import * as abstractFieldUtils from '../../../../utils/abstract-fields-utils';

let mockStore: Store<XtremAppState, AppAction> | null = null;

jest.mock('../../../../service/i18n-service', () => {
    return {
        localize: (key: string, value: string) => value,
    };
});

jest.useFakeTimers();

describe('Dynamic Select Component', () => {
    const screenId = 'testScreen';
    const elementId = 'testElement';

    const setup = (props: DynamicSelectProperties<ScreenBase, any>, fieldValue: any = null) => {
        const fieldProperties: DynamicSelectProperties<ScreenBase, any> = {
            ...props,
            ...{
                skipDirtyCheck: true,
            },
        };

        const store = () => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(
                screenId,
                {
                    page: { $: { isTransactionInProgress: () => false }, _pageMetadata: { screenId } },
                } as Partial<PageDefinition>,
                {
                    uiComponentProperties: { [screenId]: { skipDirtyCheck: true } },
                } as Partial<PageMetadata>,
            );
            addFieldToState(FieldKey.DynamicSelect, state, screenId, elementId, fieldProperties, fieldValue);
            mockStore = getMockStore(state);
        };

        store();
        return render(
            <ThemeProvider theme={baseTheme}>
                <Provider store={mockStore!}>
                    <ConnectedDynamicSelectComponent elementId={elementId} screenId={screenId} />
                </Provider>
            </ThemeProvider>,
        );
    };

    let handleChangeSpy: jest.SpyInstance;

    beforeEach(() => {
        handleChangeSpy = jest.spyOn(abstractFieldUtils, 'handleChange');
    });

    afterEach(async () => {
        mockStore = null;
        handleChangeSpy.mockReset();
        await cleanup();
    });

    it('Should render the component with default props', async () => {
        setup({ bind: 'testBind', populateList: jest.fn(), mode: 'input' });
        expect(screen.getByTestId('e-dynamic-select-field', { exact: false })).toBeInTheDocument();
        expect(screen.getByTestId('e-ui-select-input')).toBeInTheDocument();
        expect(screen.getByTestId('e-ui-dynamic-select-open-button')).toBeInTheDocument();
        expect(screen.getByTestId('e-ui-select-dropdown')).toBeInTheDocument();
    });

    it('Should open the dropdown when the open button is clicked with a link', async () => {
        setup({ bind: 'testBind', populateList: jest.fn(), mode: 'input' });
        fireEvent.click(screen.getByTestId('e-ui-dynamic-select-open-button'));
        await waitFor(() => {
            expect(screen.getByTestId('e-ui-select-dropdown')).toBeInTheDocument();
            expect(screen.getByText('Add a new list')).toBeInTheDocument();
        });
    });

    it('Should populate the list when the link is clicked', async () => {
        const populateList = jest.fn().mockResolvedValue([
            { id: '1', value: '1', displayedAs: 'One' },
            { id: '2', value: '2', displayedAs: 'Two' },
            { id: '3', value: '3', displayedAs: 'Three' },
        ]);
        setup({ bind: 'testBind', populateList, mode: 'input' });
        fireEvent.click(screen.getByTestId('e-ui-dynamic-select-open-button'));
        await waitFor(() => {
            expect(screen.getByTestId('e-ui-select-dropdown')).toBeInTheDocument();
            expect(screen.getByText('Add a new list')).toBeInTheDocument();
        });

        fireEvent.click(screen.getByText('Add a new list'));
        expect(populateList).toHaveBeenCalled();
        await waitFor(() => {
            expect(screen.getByText('One')).toBeInTheDocument();
            expect(screen.getByText('Two')).toBeInTheDocument();
            expect(screen.getByText('Three')).toBeInTheDocument();
        });
    });

    it('Should not have the lookup button if is in select mode', async () => {
        setup({ bind: 'testBind', populateList: jest.fn(), mode: 'select' });
        expect(screen.queryByTestId('e-ui-dynamic-select-open-button')).not.toBeInTheDocument();
    });

    it('Should call the handleChange function when a value is selected', async () => {
        const populateList = jest.fn().mockResolvedValue([
            { id: '1', value: '1', displayedAs: 'One' },
            { id: '2', value: '2', displayedAs: 'Two' },
            { id: '3', value: '3', displayedAs: 'Three' },
        ]);
        setup({ bind: 'testBind', populateList, mode: 'select' });
        fireEvent.click(screen.getByTestId('e-ui-dynamic-input-chevron'));
        await waitFor(() => {
            expect(screen.getByTestId('e-ui-select-dropdown')).toBeInTheDocument();
            expect(screen.getByText('Add a new list')).toBeInTheDocument();
        });

        fireEvent.click(screen.getByText('Add a new list'));
        expect(populateList).toHaveBeenCalled();
        await waitFor(() => {
            expect(screen.getByText('One')).toBeInTheDocument();
            expect(screen.getByText('Two')).toBeInTheDocument();
            expect(screen.getByText('Three')).toBeInTheDocument();
        });

        fireEvent.click(screen.getByText('One'));
        fireEvent.blur(screen.getByTestId('e-ui-select-input'));
        expect(handleChangeSpy).toHaveBeenCalled();
    });
});
