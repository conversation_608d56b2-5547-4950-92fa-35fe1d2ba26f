import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import type { DynamicSelectComponentProps } from './dynamic-select-types';

const ConnectedDynamicSelectComponent = React.lazy(() => import('./dynamic-select-component'));

export function AsyncConnectedDynamicSelectComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="200px" />}>
            <ConnectedDynamicSelectComponent {...props} />
        </React.Suspense>
    );
}

const DynamicSelectComponent = React.lazy(() =>
    import('./dynamic-select-component').then(e => ({ default: e.DynamicSelectComponent })),
);

export function AsyncDynamicSelectComponent(props: DynamicSelectComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={false} bodyHeight="200px" />}>
            <DynamicSelectComponent {...props} />
        </React.Suspense>
    );
}
