import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { DynamicSelectControlObject } from './dynamic-select-control-object';
import type { DynamicSelectDecoratorProperties } from './dynamic-select-types';

class DynamicSelectDecorator extends AbstractFieldDecorator<FieldKey.DynamicSelect> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = DynamicSelectControlObject;
}

/**
 * Initializes the decorated member as a [DynamicSelectDecorator]{@link DynamicSelectDecoratorControlObject} field with the provided properties.
 *
 * @param properties The properties that the [DynamicSelectDecorator]{@link DynamicSelectDecoratorControlObject} field will be initialized with.
 */
export function dynamicSelectField<T extends ScreenExtension<T>>(
    properties: DynamicSelectDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.DynamicSelect>(
        properties,
        DynamicSelectDecorator,
        FieldKey.DynamicSelect,
    );
}

export function dynamicSelectFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<DynamicSelectDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.DynamicSelect>(properties);
}
