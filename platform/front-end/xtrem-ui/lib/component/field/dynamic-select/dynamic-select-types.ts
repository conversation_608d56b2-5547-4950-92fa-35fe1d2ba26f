import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { BlockControlObject, SectionControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasHelperText,
    HasParent,
    HasPlaceholder,
    Nested,
    NestedChangeable,
    NestedClickable,
    NestedValidatable,
    Sizable,
    Validatable,
} from '../traits';

import type { ValueOrCallback, ValueOrCallbackWithFieldValue } from '../../../utils/types';
import type { NestedPropertiesWrapper } from '../../nested-fields';
import type { SelectItem } from '../../ui/select/select-component';
import type { BaseEditableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';

export interface DynamicSelectProperties<CT extends ScreenExtension<CT> = ScreenBase, ContextNodeType = void>
    extends EditableFieldProperties<CT, ContextNodeType>,
        HasPlaceholder,
        HasHelperText {
    /** Function that will populate the list of items */
    populateList: (this: CT, currentList: SelectItem[], value: string, rowValue: any) => Promise<SelectItem[]>;
    /** Title of the button that will trigger the populate the list */
    populateListTitle?: ValueOrCallback<CT, string>;
    /** Function or value that will populate a initial list of items */
    options?: ValueOrCallbackWithFieldValue<CT, SelectItem[]>;
    /** The mode Dynamic Select will be set */
    mode?: ValueOrCallbackWithFieldValue<CT, 'select' | 'input'>;
}

export interface DynamicSelectDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<DynamicSelectProperties<CT>, '_controlObjectType'>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        Changeable<CT>,
        Clickable<CT>,
        Validatable<CT>,
        Sizable {}

export interface NestedDynamicSelectProperties<
    CT extends ScreenBase = ScreenBase,
    ContextNodeType extends ClientNode = any,
> extends NestedPropertiesWrapper<DynamicSelectProperties<CT, ContextNodeType>>,
        NestedChangeable<CT>,
        NestedClickable<CT, ContextNodeType>,
        Nested<ContextNodeType>,
        Sizable,
        NestedValidatable<CT, string, ContextNodeType> {}

export interface DynamicSelectComponentProps
    extends BaseEditableComponentProperties<
        DynamicSelectDecoratorProperties,
        string,
        NestedFieldsAdditionalProperties
    > {}
