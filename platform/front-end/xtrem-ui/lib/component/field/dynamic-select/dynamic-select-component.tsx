import * as React from 'react';
import { deepMerge } from '@sage/xtrem-shared';
import { connect } from 'react-redux';
import { useFocus } from '../../../utils/hooks/effects/use-focus';
import { CarbonWrapper } from '../carbon-wrapper';
import { noop, set } from 'lodash';
import { mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { DynamicSelectComponentProps } from './dynamic-select-types';
import { useSelect } from 'downshift';
import { DynamicSelectInput } from './dynamic-select-input';
import { SelectDropDown } from '../../ui/select/select-dropdown';
import type { SelectItem } from '../../ui/select/select-component';
import Link from 'carbon-react/esm/components/link';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import { triggerFieldEvent, triggerNestedFieldEvent } from '../../../utils/events';
import { getScreenElement } from '../../../service/screen-base-definition';
import * as xtremRedux from '../../../redux';
import { localize } from '../../../service/i18n-service';
import { getCommonCarbonComponentProperties } from '../carbon-helpers';
import { HelperText } from '../carbon-utility-components';

export function DynamicSelectComponent(props: DynamicSelectComponentProps): React.ReactElement {
    const {
        screenId,
        fieldProperties,
        handlersArguments,
        value,
        elementId,
        isNested,
        columnDefinition,
        parentElementId,
        isInFocus,
        setFieldValue,
        validate,
    } = props;
    const listRef = React.useRef<HTMLUListElement>(null);
    const inputRef = React.useRef<HTMLInputElement>(null);
    const lookupRef = React.useRef<HTMLButtonElement>(null);
    const linkRef = React.useRef<HTMLButtonElement | HTMLAnchorElement>(null);
    useFocus(inputRef, isInFocus);

    const [internalValue, setInternalValue] = React.useState<string | undefined>(value || '');
    const [displayAsValue, setDisplayAsValue] = React.useState<string | undefined>(value || '');
    const [currentList, setCurrentList] = React.useState<SelectItem[]>([]);
    const [lastCaretPosition, setLastCaretPosition] = React.useState<number>(0);

    // Calculate initial mode value based on fieldProperties to avoid initial render flicker
    const [mode, setMode] = React.useState<string>(() => {
        if (typeof fieldProperties.mode === 'function') {
            const state = xtremRedux.getStore().getState();
            const screenDefinition = state.screenDefinitions[screenId];
            return fieldProperties.mode.apply(getScreenElement(screenDefinition), [value, handlersArguments?.rowValue]);
        }
        return fieldProperties.mode || 'select';
    });

    React.useEffect(() => {
        if (typeof fieldProperties.options === 'function') {
            const state = xtremRedux.getStore().getState();
            const screenDefinition = state.screenDefinitions[screenId];
            const options = fieldProperties.options.apply(getScreenElement(screenDefinition), [
                undefined,
                handlersArguments?.rowValue,
            ]);
            setCurrentList(options);
        } else {
            setCurrentList(fieldProperties.options || []);
        }
    }, [fieldProperties.options, screenId, handlersArguments?.rowValue]);

    React.useEffect(() => {
        let newMode: string;

        if (typeof fieldProperties.mode === 'function') {
            const state = xtremRedux.getStore().getState();
            const screenDefinition = state.screenDefinitions[screenId];
            newMode = fieldProperties.mode.apply(getScreenElement(screenDefinition), [
                value,
                handlersArguments?.rowValue,
            ]);
        } else {
            newMode = fieldProperties.mode || 'select';
        }

        if (newMode !== mode) {
            setMode(newMode);
            // Clear value if mode has changed
            setInternalValue('');
        }
    }, [fieldProperties.mode, screenId, handlersArguments?.rowValue, value, mode]);

    React.useEffect(() => {
        if (mode === 'select') {
            const foundItem = currentList.find(item => item.value === value);
            if (foundItem && foundItem.displayedAs !== displayAsValue) {
                setDisplayAsValue(foundItem.displayedAs);
            }
        }
    }, [currentList, displayAsValue, mode, value]);

    React.useEffect(() => {
        setInternalValue(value);
    }, [value]);

    const { isOpen, inputValue, getToggleButtonProps, getMenuProps, getItemProps, highlightedIndex, openMenu } =
        useSelect({
            items: currentList,
            selectedItem: undefined,
            stateReducer(_state, actionAndChanges) {
                const changedSelectedItem = actionAndChanges.changes.selectedItem;
                if (
                    (actionAndChanges.type === useSelect.stateChangeTypes.ItemClick ||
                        actionAndChanges.type === useSelect.stateChangeTypes.MenuKeyDownEnter) &&
                    changedSelectedItem
                ) {
                    if (mode === 'select') {
                        setInternalValue(changedSelectedItem.value);
                        setDisplayAsValue(changedSelectedItem.displayedAs);
                        handleChange(elementId, changedSelectedItem.value, setFieldValue, validate, changeEventHandler);
                    } else {
                        inputRef.current?.focus();
                        setTimeout(() => {
                            const newValue =
                                (internalValue || '').slice(0, lastCaretPosition) +
                                changedSelectedItem.value +
                                (internalValue || '').slice(lastCaretPosition);
                            setInternalValue(newValue);
                            inputRef.current?.setSelectionRange(lastCaretPosition, lastCaretPosition);
                        }, 100);
                    }
                }
                if (actionAndChanges.type === useSelect.stateChangeTypes.MenuKeyDownArrowDown) {
                    if (isOpen && (currentList.length === 0 || highlightedIndex === currentList.length - 1)) {
                        setTimeout(() => {
                            linkRef.current?.focus();
                        }, 100);
                    }
                }
                if (
                    actionAndChanges.type === useSelect.stateChangeTypes.MenuBlur &&
                    linkRef.current === document.activeElement
                ) {
                    actionAndChanges.changes.isOpen = true;
                }
                return actionAndChanges.changes;
            },
        });

    const lookupButtonEvents = {
        ...getToggleButtonProps(),
        lookupIconId: 'lookup-icon',
        lookupButtonRef: lookupRef,
    };

    const onInputChange = React.useCallback((e: React.ChangeEvent): void => {
        const target = e.target as HTMLInputElement;
        setInternalValue(target.value);
    }, []);

    const onInputClick = React.useCallback((): void => {
        if (mode === 'select') {
            openMenu();
        }

        if (isNested && handlersArguments?.onClick && parentElementId) {
            triggerNestedFieldEvent(
                screenId,
                parentElementId,
                fieldProperties as any,
                'onClick',
                ...(handlersArguments?.onClick as [
                    rowId: string,
                    rowData: any,
                    level?: number,
                    ancestorIds?: string[],
                ]),
            );
        } else {
            triggerFieldEvent(screenId, elementId, 'onClick');
        }
    }, [openMenu, screenId, elementId, parentElementId, isNested, handlersArguments, fieldProperties, mode]);

    const changeEventHandler = React.useCallback(
        (newValue?: any): void => {
            if (isNested && handlersArguments?.onChange && parentElementId) {
                const rowValue =
                    newValue !== undefined
                        ? deepMerge(
                              handlersArguments.rowValue,
                              set(
                                  {},
                                  convertDeepBindToPathNotNull(
                                      columnDefinition?.properties?.bind || fieldProperties?.bind || elementId,
                                  ),
                                  newValue,
                              ),
                          )
                        : handlersArguments?.rowValue;
                triggerNestedFieldEvent(
                    screenId,
                    parentElementId || elementId,
                    fieldProperties as any,
                    'onChange',
                    rowValue?._id,
                    rowValue,
                );
            } else {
                triggerFieldEvent(screenId, elementId, 'onChange');
            }
        },
        [
            isNested,
            handlersArguments?.onChange,
            handlersArguments?.rowValue,
            parentElementId,
            columnDefinition?.properties?.bind,
            fieldProperties,
            elementId,
            screenId,
        ],
    );

    const onInputBlur = React.useCallback((): void => {
        if (mode === 'input') {
            setLastCaretPosition(inputRef.current?.selectionEnd || 0);
            handleChange(elementId, internalValue, setFieldValue, validate, changeEventHandler);
        }
    }, [elementId, internalValue, setFieldValue, validate, changeEventHandler, mode]);

    const onInputKeyDown = React.useCallback(
        (e: React.KeyboardEvent): void => {
            if (currentList.length === 0 && isOpen) {
                linkRef.current?.focus();
            }
            if (e.key === 'ArrowDown' && !isOpen && fieldProperties.mode === 'select') {
                openMenu();
            }
        },
        [currentList.length, isOpen, openMenu, fieldProperties.mode],
    );

    const onClickLink = React.useCallback(async (): Promise<void> => {
        const state = xtremRedux.getStore().getState();
        const screenDefinition = state.screenDefinitions[screenId];
        const list = await fieldProperties.populateList.apply(getScreenElement(screenDefinition), [
            currentList,
            value,
            handlersArguments?.rowValue,
        ]);
        setCurrentList(list);
        setTimeout(() => {
            openMenu();
        }, 100);
    }, [screenId, fieldProperties, openMenu, currentList, value, handlersArguments?.rowValue]);

    const onLinkKeyDown = React.useCallback(
        (e: React.KeyboardEvent<HTMLButtonElement>): void => {
            switch (e.key) {
                case 'ArrowDown':
                    inputRef.current?.focus();
                    break;
                case 'ArrowUp':
                    inputRef.current?.focus();
                    break;
                case 'Enter':
                    onClickLink();
                    break;
                default:
                    break;
            }

            e.preventDefault();
            e.stopPropagation();
        },
        [onClickLink],
    );

    const populatedListTitle = React.useCallback((): string | undefined => {
        if (typeof fieldProperties.populateListTitle === 'function') {
            const state = xtremRedux.getStore().getState();
            const screenDefinition = state.screenDefinitions[screenId];
            return fieldProperties.populateListTitle.apply(getScreenElement(screenDefinition));
        }
        return fieldProperties.populateListTitle;
    }, [fieldProperties.populateListTitle, screenId]);

    const linkElement = React.useMemo(
        () => (
            <Link
                data-testid="e-dynamic-select-link"
                icon="plus"
                ref={linkRef}
                onKeyDown={onLinkKeyDown}
                onClick={onClickLink}
            >
                {fieldProperties.populateListTitle
                    ? populatedListTitle()
                    : localize('@sage/xtrem-ui/populate-list-title-default', 'Add a new list')}
            </Link>
        ),
        [onClickLink, fieldProperties.populateListTitle, populatedListTitle, onLinkKeyDown],
    );

    return (
        <CarbonWrapper
            {...props}
            className="e-dynamic-select-field"
            componentName="dynamic-select"
            handlersArguments={handlersArguments}
            fieldProperties={fieldProperties}
            value={internalValue}
            helperText={fieldProperties.helperText}
        >
            <DynamicSelectInput
                {...getCommonCarbonComponentProperties(props)}
                icon="plus"
                lookupButtonEvents={lookupButtonEvents}
                onChange={onInputChange}
                onClick={onInputClick}
                onBlur={onInputBlur}
                onKeyDown={onInputKeyDown}
                value={mode === 'select' ? displayAsValue : internalValue}
                inputRef={inputRef}
                mode={mode}
            />
            <SelectDropDown
                {...getCommonCarbonComponentProperties(props)}
                screenId={screenId}
                ulRef={listRef}
                getItemProps={getItemProps}
                addSelectedItem={noop}
                isOpen={isOpen}
                inputValue={inputValue}
                menuProps={getMenuProps()}
                focusLookupButton={() => lookupRef.current?.focus()}
                hasHighlightMatchText={false}
                highlightedIndex={highlightedIndex}
                highlightFirstListItem={noop}
                highlightLastListItem={noop}
                items={currentList}
                loading={false}
                hasLookupIcon={true}
                linkElement={linkElement}
            />
            {fieldProperties.helperText && <HelperText helperText={fieldProperties.helperText} />}
        </CarbonWrapper>
    );
}

export const ConnectedDynamicSelectComponent = connect(mapStateToProps(), mapDispatchToProps())(DynamicSelectComponent);

export default ConnectedDynamicSelectComponent;
