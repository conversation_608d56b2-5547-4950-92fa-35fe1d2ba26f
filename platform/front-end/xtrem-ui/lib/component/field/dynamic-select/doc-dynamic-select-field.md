PATH: XTREEM/UI+Field+Widgets/Dynamic+Select+Field

## Introduction
The Dynamic Select Field is used to represent a list of options that can be dynamically populated or selected by the user. It supports both "select" and "input" modes, allowing users to either choose from a predefined list or type custom values. The field is also available as a nested and extended field.

## Decorator Interface
```typescript
{
    bind?: GraphQLNodeProperty;
    helperText?: string;
    isDisabled?: boolean;
    isFullWidth?: boolean;
    isHidden?: boolean;
    isHiddenMobile?: boolean;
    isHiddenDesktop?: boolean;
    isMandatory?: boolean;
    isReadOnly?: boolean;
    isTitleHidden?: boolean;
    isTransient?: boolean;
    placeholder?: string;
    size?: FieldWidth;
    title?: string;
    width?: FieldWidth;
    mode?: 'select' | 'input' | ((value: string, rowValue?: any) => 'select' | 'input');
    populateList: (currentList: SelectItem[]) => Promise<SelectItem[]>;
    populateListTitle?: string | (() => string);
    options?: SelectItem[] | (() => SelectItem[]);
    onChange?: () => void;
    onClick?: () => void;
    parent: () => ContainerComponent;
}
```

## Example
#### Usage with a dynamic list:
```ts
@ui.decorators.dynamicSelectField<PageContext>({
    populateList: async (currentList) => {
        return [
            { id: '1', value: 'Option 1', displayedAs: 'Option 1' },
            { id: '2', value: 'Option 2', displayedAs: 'Option 2' },
        ];
    },
    placeholder: 'Choose an option...',
    title: 'Dynamic Select Field',
    mode: 'select',
    onChange() {
        console.log(`Do something when the field's value has changed.`);
    },
    parent() {
        return this.block;
    },
})
field: ui.fields.DynamicSelect;
```

#### Usage with an initial list:
```ts
@ui.decorators.dynamicSelectField<PageContext>({
    options: [
        { id: '1', value: 'Option A', displayedAs: 'Option A' },
        { id: '2', value: 'Option B', displayedAs: 'Option B' },
    ],
    placeholder: 'Choose an option...',
    title: 'Dynamic Select Field',
    mode: 'input',
    onClick() {
        console.log(`Do something when the field is clicked.`);
    },
    parent() {
        return this.block;
    },
})
field: ui.fields.DynamicSelect;
```

#### Usage as an extension field:
```ts
@ui.decorators.dynamicSelectField<PageExtension>({
    ...,
    title: 'Extended Dynamic Select Field',
    populateListTitle: 'Load More Options',
    parent() {
        return this.block;
    },
})
extension: ui.fields.DynamicSelect;
```

## Decorator Properties
#### Display Properties
-   **helperText**: Specifies the helper text displayed above the field. It is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Specifies whether the field is editable or not. The difference between isDisabled and isReadOnly is that isReadOnly suggests that the field is never editable, whereas isDisabled suggests that the field is currently not editable.
-   **isFullWidth**: Specifies whether the field should take the full width.
-   **isHidden**: Specifies whether the component should be displayed or not.
-   **isHiddenDesktop**: Specifies whether the component should be displayed for desktop-size viewports.
-   **isHiddenMobile**: Specifies whether the component should be displayed for mobile-size viewports.
-   **isReadOnly**: Specifies whether the field is editable or not. The difference between isDisabled and isReadOnly is that isReadOnly suggests that the field is never editable, whereas isDisabled suggests that the field is currently not editable.
-   **isTitleHidden**: Specifies whether the field's title should be displayed or not.
-   **placeholder**: Specifies the field's input placeholder. It is automatically picked up by the i18n engine and externalized.
-   **size**: Specifies the field's vertical size. Options are `small`, `medium`, and `large`. By default, the size is set to `medium`.
-   **title**: The title that is displayed above the field. The title can be provided as a string or a callback function returning a string. It is automatically picked up by the i18n engine and externalized for translation.
-   **width**: Specifies the field's width. The width can be defined by using field size categories which are remapped to actual width values by the framework depending on the screen size and the container size that the field is in.

#### Dynamic Properties
-   **mode**: Specifies the mode of the field. It can be either `select` (dropdown) or `input` (free text). It can also be defined as a callback function that dynamically determines the mode based on the field's value or row data.
-   **populateList**: Function that dynamically populates the list of items displayed in the dropdown.
-   **populateListTitle**: Title of the button that triggers the population of the list. It can be a string or a callback function.
-   **options**: Specifies the initial list of items to be displayed in the dropdown. It can be a static array or a callback function.

#### Event Handler Properties
-   **onChange()**: Triggered when the field's value changes.
-   **onClick()**: Triggered when the field is clicked.

#### Validation Properties
-   **isMandatory**: Specifies whether the field is mandatory or not. When enabled, empty values will raise a validation error message.
-   **validation(value: string)**: Custom validation callback with the new value provided as an argument. If the function returns a non-empty string, the return value will be used as the validation error message.

### Other Decorator Properties
-   **fetchesDefaults**: When set to true and when the dropdown value changes, a request to the server for default values for the whole page will be requested. False by default.

#### Runtime Functions
-   **focus()**: Moves the browser's focus to the field.
-   **refresh()**: Refetches the field's value from the server and updates the user interface.
-   **validate()**: Triggers the field validation rules. Returns a promise that resolves to the validation result.
-   **fetchDefault(skipSet)**: Force re-fetches the default value for the field. If the `skipSet` flag is set to true, it returns the default values but does not apply them to the screen.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

## Sandbox
Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/DynamicSelectField).
