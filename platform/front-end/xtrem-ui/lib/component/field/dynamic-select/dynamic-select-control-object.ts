/**
 * @packageDocumentation
 * @module root
 */
import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { DynamicSelectProperties } from './dynamic-select-types';
import type { SelectItem } from '../../ui/select/select-component';

/**
 * [Field]{@link EditableFieldControlObject} that holds a value from a set of given values.
 */
export class DynamicSelectControlObject<
    NodeType extends ClientNode = any,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.DynamicSelect, FieldComponentProps<FieldKey.DynamicSelect>> {
    static readonly defaultUiProperties: Partial<DynamicSelectProperties> = {
        ...EditableFieldControlObject.defaultUiProperties,
        mode: 'select',
    };

    @ControlObjectProperty<DynamicSelectProperties<CT, NodeType>, DynamicSelectControlObject<NodeType, CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<DynamicSelectProperties<CT, NodeType>, DynamicSelectControlObject<NodeType, CT>>()
    /** The helper text underneath the field */
    placeholder?: string;

    @ControlObjectProperty<DynamicSelectProperties<CT, NodeType>, DynamicSelectControlObject<NodeType, CT>>()
    /** The mode Dynamic Select will be set */
    mode?: 'select' | 'input';

    @ControlObjectProperty<DynamicSelectProperties<CT, NodeType>, DynamicSelectControlObject<NodeType, CT>>()
    options?: SelectItem[];

    focus(): void {
        this._focus();
    }
}
