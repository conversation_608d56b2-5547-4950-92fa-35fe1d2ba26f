import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    userEvent,
} from '../../../../__tests__/test-helpers';
import { cleanup, render, screen, waitFor } from '@testing-library/react';
import type { PodProperties } from '../pod-types';
import type { ScreenBase } from '../../../../service/screen-base';
import * as React from 'react';
import { Provider } from 'react-redux';
import { nestedFields } from '../../../..';
import { FieldKey } from '../../../types';
import { ConnectedPodComponent } from '../pod-component';
import * as abstractFieldUtils from '../../../../utils/abstract-fields-utils';
import { actions, type AppAction, type XtremAppState } from '../../../../redux';
import type { PageDefinition } from '../../../../service/page-definition';
import type * as pageMetadataImport from '../../../../service/page-metadata';
import baseTheme from 'carbon-react/esm/style/themes/sage';
import { ThemeProvider } from 'styled-components';
import type { DropdownActionItem } from '../../traits';
import * as events from '../../../../utils/events';
import { ConnectedDialogRouter } from '../../../container/dialog/dialog-router';
import type { Store } from 'redux';

jest.useFakeTimers();

describe('Pod Component', () => {
    const screenId = 'testScreen';
    const elementId = 'testElement';
    let mockStore: Store<XtremAppState, AppAction> | null = null;
    let triggerHandledEventMock: jest.MockInstance<any, any>;
    let handleChangeSpy: jest.SpyInstance;

    const setup = (
        props: Omit<PodProperties<ScreenBase, any>, 'columns'> = {},
        fieldValue: any = null,
        shouldMockReduxActions = true,
    ) => {
        const fieldProperties: PodProperties<ScreenBase, any> = {
            ...props,
            ...{
                columns: [
                    nestedFields.text({ bind: 'name', title: 'Name' }),
                    nestedFields.text({ bind: 'addressLine1', title: 'Line1' }),
                    nestedFields.text({ bind: 'addressLine2', title: 'Line2' }),
                    nestedFields.reference({
                        bind: 'country',
                        title: 'Country',
                        node: '@sage/xtrem-show-case/ShowCaseCountry',
                        valueField: 'name',
                        helperTextField: 'code',
                    }),
                ],
                node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
                skipDirtyCheck: true,
            },
        };

        const state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(
            screenId,
            {
                page: { $: { isTransactionInProgress: () => false }, _pageMetadata: { screenId } },
            } as Partial<PageDefinition>,
            {
                uiComponentProperties: {
                    [screenId]: { skipDirtyCheck: true },
                },
            } as Partial<pageMetadataImport.PageMetadata>,
        );
        addFieldToState<FieldKey.Pod>(FieldKey.Pod, state, screenId, elementId, fieldProperties, fieldValue);
        mockStore = getMockStore(state, shouldMockReduxActions);
        const Children = (
            <ThemeProvider theme={baseTheme}>
                <Provider store={mockStore!}>
                    <ConnectedPodComponent elementId={elementId} screenId={screenId} />
                    <ConnectedDialogRouter />
                </Provider>
            </ThemeProvider>
        );
        return { ...render(Children), Children };
    };

    beforeEach(() => {
        triggerHandledEventMock = jest.spyOn(events, 'triggerHandledEvent').mockImplementation(jest.fn());
        handleChangeSpy = jest.spyOn(abstractFieldUtils, 'handleChange');
        jest.spyOn(actions, 'setFieldValue').mockReturnValue(() => Promise.resolve({ type: 'SetFieldValue' }) as any);
        jest.spyOn(actions, 'openDialog');
    });

    afterEach(async () => {
        jest.clearAllMocks();
        await cleanup();
    });

    it('should open the lookup dialog when the new button clicked', async () => {
        setup();
        const newButton = screen.getByTestId('e-pod-add-new');
        expect(newButton).toBeInTheDocument();
        expect(actions.openDialog).toHaveBeenCalledTimes(0);
        await userEvent.click(newButton);
        await waitFor(() => {
            expect(actions.openDialog).toHaveBeenCalledTimes(1);
            expect(actions.openDialog).toHaveBeenLastCalledWith(
                expect.any(Number),
                expect.objectContaining({
                    title: 'Selection',
                }),
            );
        });
    });

    it('should call on change handler when the user removes the values', async () => {
        const selectedItem = { _id: '124', vegetable: 'patata' };
        setup({ canRemove: true }, selectedItem);
        const removeButton = screen.getByTestId('e-pod-close');
        expect(handleChangeSpy).not.toHaveBeenCalled();
        await userEvent.click(removeButton);
        expect(handleChangeSpy).toHaveBeenCalledTimes(1);
        expect(handleChangeSpy).toHaveBeenCalledWith(
            'testElement',
            null,
            expect.anything(),
            expect.anything(),
            expect.anything(),
        );
    });

    it('Should render with title', () => {
        const wrapper = setup({ title: 'test pod' }, { _id: '124', vegetable: 'patata' });
        const title = wrapper.baseElement.querySelector('.e-pod-title');
        expect(title).toBeInTheDocument();
        expect(title!).toHaveTextContent('test pod');
    });

    it('Should render with info message', () => {
        const wrapper = setup({ title: 'test pod', infoMessage: 'Hi there!' }, { _id: '124', vegetable: 'patata' });
        const icon = wrapper.baseElement.querySelector('.e-icon-info-message');
        expect(icon).not.toBeNull();
    });

    it('Should render with info message callback', () => {
        const wrapper = setup(
            { title: 'test pod', infoMessage: () => 'Hi there!' },
            { _id: '124', vegetable: 'patata' },
        );
        const icon = wrapper.baseElement.querySelector('.e-icon-info-message');
        expect(icon).not.toBeNull();
    });

    it('Should render with warning message', () => {
        const wrapper = setup({ title: 'test pod', warningMessage: 'Hi there!' }, { _id: '124', vegetable: 'patata' });
        const icon = wrapper.baseElement.querySelector('.e-icon-warning-message');
        expect(icon).not.toBeNull();
    });

    it('Should render with warning message callback', () => {
        const wrapper = setup(
            { title: 'test pod', warningMessage: () => 'Hi there!' },
            { _id: '124', vegetable: 'patata' },
        );
        const icon = wrapper.baseElement.querySelector('.e-icon-warning-message');
        expect(icon).not.toBeNull();
    });

    it('Should prioritize warning over info messages', () => {
        const wrapper = setup(
            { title: 'test pod', warningMessage: () => 'Hi there!', infoMessage: 'Hi again.' },
            { _id: '124', vegetable: 'patata' },
        );
        const warningIcon = wrapper.baseElement.querySelector('.e-icon-warning-message');
        expect(warningIcon).not.toBeNull();
        const infoIcon = wrapper.baseElement.querySelector('.e-icon-info-message');
        expect(infoIcon).toBeNull();
    });

    it('Should render with placeholder text when no value is provided', () => {
        setup();
        const placeholderText = screen.queryByText('No data to display');
        expect(placeholderText).toBeTruthy();
    });

    it('Should render with custom placeholder text when no value is provided', () => {
        setup({ placeholder: 'No Patatas to display' });
        const placeholderText = screen.queryByText('No Patatas to display');
        expect(placeholderText).toBeTruthy();
    });

    it('Should render with add new button because no value', () => {
        setup();
        const newButton = screen.queryByTestId('e-pod-add-new')!;
        expect(newButton!).toBeTruthy();
        expect(newButton.querySelector('span[data-element="main-text"]')!.innerHTML).toBe('Add an item');
    });

    it('Should render with custom text button for add new pod', () => {
        setup({ addButtonText: 'Add new patata' });
        const newButton = screen.queryByTestId('e-pod-add-new')!;
        expect(newButton).toBeTruthy();
        expect(newButton.querySelector('span[data-element="main-text"]')!.innerHTML).toBe('Add new patata');
    });

    describe('dropdown actions', () => {
        let action1mock: jest.Mock<any>;
        let action2mock: jest.Mock<any>;
        let isDisabled2mock: jest.Mock<any>;
        let isHidden1Mock: jest.Mock<any>;
        let isHidden2Mock: jest.Mock<any>;
        let dropdownActions: DropdownActionItem<any>[] = [];

        beforeEach(() => {
            action1mock = jest.fn();
            action2mock = jest.fn();
            isDisabled2mock = jest.fn().mockReturnValue(true);
            isHidden1Mock = jest.fn().mockReturnValue(false);
            isHidden2Mock = jest.fn().mockReturnValue(true);

            dropdownActions = [
                { icon: 'add', title: 'Test title', onClick: action1mock },
                {
                    icon: 'add',
                    title: 'Test title',
                    onClick: action2mock,
                    isHidden: isHidden1Mock,
                    isDisabled: isDisabled2mock,
                },
                {
                    icon: 'add',
                    title: 'Test hidden',
                    onClick: action2mock,
                    isHidden: isHidden2Mock,
                    isDisabled: jest.fn().mockReturnValue(false),
                },
            ];
        });

        it('should open the menu when the user clicks on the icon', async () => {
            const wrapper = setup({ dropdownActions });
            expect(
                wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
            ).toHaveLength(0);

            await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

            expect(
                wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
            ).toHaveLength(2);
        });

        it('should call the isDisabled callback on menu rendering', async () => {
            const wrapper = setup({ dropdownActions });

            expect(isDisabled2mock).toHaveBeenCalledTimes(1);

            await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

            expect(isDisabled2mock).toHaveBeenCalledTimes(3);
        });

        it('should not render if an item is hidden', async () => {
            const wrapper = setup({ dropdownActions });
            expect(isHidden2Mock).toHaveBeenCalledTimes(1);

            await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

            expect(isHidden2Mock).toHaveBeenCalledTimes(3);

            expect(document.querySelectorAll('[data-component="action-popover"] button[type="button"]').length).toEqual(
                2,
            );
        });

        it('should render conditionally displayed items', async () => {
            isHidden2Mock.mockReturnValue(false);

            const wrapper = setup({ dropdownActions });

            expect(isHidden2Mock).toHaveBeenCalledTimes(1);

            await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

            expect(document.querySelectorAll('[data-component="action-popover"] button[type="button"]').length).toEqual(
                3,
            );
        });

        it('should trigger the event listener when the user clicks on an item', async () => {
            const wrapper = setup({ dropdownActions });

            await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

            expect(triggerHandledEventMock).not.toHaveBeenCalled();

            await userEvent.click(
                document.querySelectorAll('[data-component="action-popover"] button[type="button"]')[0],
            );
            expect(triggerHandledEventMock).toHaveBeenCalledTimes(1);
            expect(triggerHandledEventMock).toHaveBeenCalledWith(screenId, elementId, {
                onClick: action1mock,
                onError: undefined,
            });
        });

        it('should trigger the right event listener even if an item is hidden in front of the item in the menu', async () => {
            isHidden1Mock.mockReturnValue(true);
            isHidden2Mock.mockReturnValue(false);
            const wrapper = setup({ dropdownActions });

            await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

            expect(triggerHandledEventMock).not.toHaveBeenCalled();
            await userEvent.click(
                document.querySelectorAll('[data-component="action-popover"] button[type="button"]')[1],
            );

            expect(triggerHandledEventMock).toHaveBeenCalledTimes(1);
            expect(triggerHandledEventMock).toHaveBeenCalledWith(screenId, elementId, {
                onClick: action2mock,
                onError: undefined,
            });
        });

        it('should not trigger the event listener when the user clicks on a disabled item', async () => {
            const wrapper = setup({ dropdownActions });
            await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

            expect(action1mock).not.toHaveBeenCalled();
            const actionButton = document.querySelectorAll(
                '[data-component="action-popover"] button[type="button"]',
            )[1];
            expect(actionButton).toHaveTextContent('Test title');
            expect(actionButton).toHaveAttribute('aria-disabled', 'true');
            await userEvent.click(actionButton);
            expect(action2mock).not.toHaveBeenCalled();
        });
    });
});
