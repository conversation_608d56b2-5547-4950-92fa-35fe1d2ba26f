import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { PodControlObject } from './pod-control-object';
import type { PodDecoratorProperties } from './pod-types';

class PodDecorator extends AbstractFieldDecorator<FieldKey.Pod> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = PodControlObject;
}

/**
 * Initializes the decorated member as a [VitalPod]{@link VitalPodControlObject} field with the provided properties.
 *
 * @param properties The properties that the [VitalPod]{@link VitalPodControlObject} field will be initialized with.
 */
export function podField<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode>(
    properties: PodDecoratorProperties<Extend<T>, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Pod, ReferencedItemType>(properties, PodDecorator, FieldKey.Pod);
}

export function podFieldOverride<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode>(
    properties: ChangeableOverrideDecoratorProperties<PodDecoratorProperties<Extend<T>, ReferencedItemType>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Pod, ReferencedItemType>(properties);
}
