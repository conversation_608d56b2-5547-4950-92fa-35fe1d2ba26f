import * as React from 'react';
import { connect } from 'react-redux';
import type { XtremAppState } from '../../../redux';
import { lookupDialog } from '../../../service/dialog-service';
import type { ScreenBase } from '../../../service/screen-base';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { calculateContainerWidth } from '../../../utils/responsive-utils';
import type { UiComponentProperties } from '../../abstract-ui-control-object';
import type { ReferenceDecoratorProperties } from '../../decorator-properties';
import type { CollectionItem } from '../../types';
import type { PodProps } from '../../ui/pod/pod-component';
import { Pod } from '../../ui/pod/pod-component';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type {
    EditableFieldComponentProperties,
    FieldComponentExternalProperties,
    NestedFieldsAdditionalProperties,
} from '../field-base-component-types';
import type { PodDecoratorProperties, PodProperties } from './pod-types';

export class PodComponent extends EditableFieldBaseComponent<
    PodDecoratorProperties<ScreenBase, any>,
    any,
    NestedFieldsAdditionalProperties
> {
    onChange = (collectionItem: CollectionItem | undefined, isOrganicChange: boolean): void => {
        if (isOrganicChange) {
            handleChange(
                this.props.elementId,
                collectionItem ?? null,
                this.props.setFieldValue,
                this.props.validate,
                this.triggerChangeListener,
            );
        }
    };

    onRemove = (): void =>
        handleChange(
            this.props.elementId,
            null,
            this.props.setFieldValue,
            this.props.validate,
            this.triggerChangeListener,
        );

    onNewPod = async (): Promise<void> => {
        try {
            const [selectedRecord] = await lookupDialog(this.props.screenId, 'info', {
                contextNode: this.props.contextNode,
                createTunnelLinkText: undefined,
                fieldId: this.props.elementId,
                fieldProperties: this.props.fieldProperties as ReferenceDecoratorProperties,
                isLinkCreateNewText: false,
                isMultiSelect: false,
                level: this.props.level,
                onCreateNewItemLinkClick: undefined,
                recordContext: this.props.recordContext,
                selectedRecordId: this.props.value?._id,
                value: this.props.value,
            });
            this.onChange(selectedRecord, true);
        } catch {
            /* intentionally left empty */
        }

        if (this.props.setFieldProperties) {
            this.props.setFieldProperties(this.props.elementId, {
                ...this.props.fieldProperties,
                isReferenceDialogOpen: true,
            });
        }
    };

    isLookupDialogOpen = (): boolean =>
        !!this.props.fieldProperties.isReferenceDialogOpen && !!this.props.fieldProperties.columns;

    render(): React.ReactNode {
        const { fieldProperties, browser, availableColumns, value, elementId, screenId } = this.props;
        const podWidth = browser && calculateContainerWidth(browser.is, availableColumns || 12, 'small');

        const podProps: PodProps = {
            availableColumns: podWidth,
            baseAttributesDivWrapper: this.getBaseAttributesDivWrapper(
                'pod',
                'e-pod-field',
                this.props.contextType,
                this.props.handlersArguments?.rowValue,
                this.props.isNested,
            ),
            browser,
            contextType: this.props.contextType,
            elementId,
            fieldProperties,
            isDisabled: this.isDisabled(),
            isReadOnly: true,
            onBlockClick: this.getClickHandler(),
            onNewPod: this.onNewPod,
            onRemove: this.onRemove,
            onTelemetryEvent: this.props.fieldProperties.onTelemetryEvent,
            screenId,
            value,
        };

        return <Pod {...podProps} />;
    }
}

const mapStateWithTelemetryToProps =
    () =>
    (
        state: XtremAppState,
        props: FieldComponentExternalProperties,
    ): EditableFieldComponentProperties<PodProperties<ScreenBase>, any> => {
        const componentProps = mapStateToProps()(state, props) as EditableFieldComponentProperties<
            UiComponentProperties,
            any
        >;

        return {
            ...componentProps,
            fieldProperties: {
                ...componentProps.fieldProperties,
                onTelemetryEvent: state.applicationContext?.onTelemetryEvent,
            },
        };
    };

export const ConnectedPodComponent = connect(mapStateWithTelemetryToProps(), mapDispatchToProps())(PodComponent);
