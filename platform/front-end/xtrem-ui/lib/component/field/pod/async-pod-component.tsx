import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import type { PodComponentProperties } from './pod-types';

const ConnectedPodComponent = React.lazy(() =>
    import('./pod-component').then(e => ({ default: e.ConnectedPodComponent })),
);

export function AsyncConnectedPodComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="200px" />}>
            <ConnectedPodComponent {...props} />
        </React.Suspense>
    );
}

const PodComponent = React.lazy(() => import('./pod-component').then(e => ({ default: e.PodComponent })));

export function AsyncPodComponent(props: PodComponentProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={false} bodyHeight="200px" />}>
            <PodComponent {...props} />
        </React.Suspense>
    );
}
