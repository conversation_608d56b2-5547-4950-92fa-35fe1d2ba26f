PATH: XTREEM/UI+Field+Widgets/Pod

## Introduction

Pod field are used to represent a reference data in a non-tabular way using small block like components.

It can be used and configured similarly to a pod collection.

## Example:

```ts
    @ui.decorators.podField<PodField, ShowCaseProvider>({
        title: 'Provider',
        width: 'large',
        parent() {
            return this.block;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 5000);
        },
        onError() {
            console.error('just testing onError works.');
        },
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return this.anyOtherField.value === 4;
                },
                onClick() {
                    this.$.showToast(`Add action triggered, current value: ${this.provider.value.integerField}`, { type: 'info' });
                },
            }
        ],
        columns: [
            ui.nestedFields.text({ bind: 'textField', title: 'Text field' }),
            ui.nestedFields.text({ bind: 'integerField', title: 'Numeric field' }),
            ui.nestedFields.text({ bind: '_id', title: 'id' }),
            ui.nestedFields.text({ bind: 'dateField', title: 'date field' }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
    })
    provider: ui.fields.Pod;

```

### Display decorator properties

- **columns**: A list of nested fields to be displayed as columns. The following types can be used as nested fields: 'checkbox', 'icon', 'image', 'label', 'link', 'numeric', 'progress', 'reference', 'text', 'date'.
- **helperText**: The helper text that is displayed below the pods. It is automatically picked up by the i18n engine and externalized.
- **isDisabled**: Whether all interactions (including field editing, selection, adding or removing pods) are disabled or not. Defaults to false.
- **isReadonly**: Whether the pods are displayed in read-only mode. In this case no input fields are rendered
- **isFullWidth**: Whether the table spans all the parent width or not. It can also be defined as callback function that returns a boolean. Defaults to true.
- **isHidden**: Whether the table is hidden or not. Defaults to false.
- **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
- **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
- **title**: The title that is displayed above the table. It is automatically picked up by the i18n engine and externalized.
- **width**: Specifies the field's width. The width can be defined by using field size categories which are remapped to actually width values by the framework depending on the screen size and the container size that the field is in.
- **fetchesDefaults**: When set to true and when the table value changes, a request to the server for default values for the whole page will be requested. False by default.
- **addButtonText**: Defaults to "Add new item". The provided text is automatically picked up by the i18n engine and externalized.
- **placeholder**: Text displayed in the component's empty state. Defaults to "No data to display".
- **canRemove**: When true, the delete cross is displayed and it is sets the pod's value to null
- **dropdownActions**: A list of actions that are displayed in the top-right corner as a drop-down menu. See below under "Row action properties" how they can be configured.
- **additionalLookupRecords**: Records provided here are added to the lookup dialog and dropdown in addition to the server provided records.
- **lookupDialogTitle**: The title that is displayed in the lookup dialog when selecting a record to display in the pod field.
-   **info**: Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function that returns a string.
-   **warning**: Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback function that returns a string.

### Binding decorator properties

-   **bind**: The GraphQL object's property that the pod collection value is bound to. If not provided, the pod collection name is used.
-   **filter**: Graphql filter that will restrict the records displayed in the pod collection.
-   **isTransient**: If marked as true, the pod collection will be excluded from the automatic data binding process (i.e. no data will be fetched from the server). The default value is false.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **node**: The name of the node that the pod collection represents, it must be defined if you want to use the advanced filtering capabilities.

### Event handler decorator properties

- **onClick**: Event triggered when any parts of the record is clicked, no arguments provided.
- **onChange**: Event triggered when the record value changes.
- **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

### Other decorator properties

-   **fetchesDefaults**: When set to true and when the pod collection value changes, a request to the server for default values for the whole page will be requested. False by default.

### Dropdown action properties

-   **title**: The title of the action displayed in the drop-down menu.
-   **icon**: An optional icon for the action, to be displayed alongside the action title.
-   **onClick**: Event triggered when an action is selected in the drop-down menu.
-   **isDisabled**: Whether the action is enabled (disabled = false) or not (disabled = true). It can also be defined as callback function that returns a boolean. It is enabled by default.

## Runtime functions

-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the record ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

### Column properties

Please refer to the documentation of the corresponding nested field type.


## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/PodField) and [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/PodBlock).
