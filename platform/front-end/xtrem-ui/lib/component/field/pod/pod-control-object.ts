/**
 * @packageDocumentation
 * @module root
 */
import type { ClientNode } from '@sage/xtrem-client';
import { noop } from 'lodash';
import { lookupDialog } from '../../../service/dialog-service';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import type { ScreenBase } from '../../../service/screen-base';
import { showToast } from '../../../service/toast-service';
import type { ScreenExtension } from '../../../types';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { CollectionItem, FieldComponentProps, FieldKey } from '../../types';
import type { PodProperties } from './pod-types';
import { getStore } from '../../../redux';
import { setFieldValue } from '../../../redux/actions';

/**
 * [Field]{@link EditableFieldControlObject} that holds a value from a set of given values.
 */
export class PodControlObject<
    NodeType extends ClientNode = any,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.Pod, FieldComponentProps<FieldKey.Pod>> {
    static readonly defaultUiProperties: Partial<PodProperties<ScreenBase, any>> = {
        ...EditableFieldControlObject.defaultUiProperties,
    };

    /** Graphql filter that will restrict the results of the reference field */
    get filter(): GraphQLFilter<NodeType> | undefined {
        return resolveByValue({
            fieldValue: undefined,
            propertyValue: this.getUiComponentProperty('filter'),
            rowValue: undefined,
            screenId: this.screenId,
            skipHexFormat: true,
        });
    }

    /** Graphql filter that will restrict the results of the reference field */
    set filter(filter: GraphQLFilter<NodeType> | undefined) {
        this.setUiComponentProperties('filter', filter);
        this.refresh().catch(() => {
            /* Intentional fire and forget */
        });
    }

    /** The GraphQL node that the field suggestions will be fetched from */
    get node(): string {
        return String(this.getUiComponentProperty('node'));
    }

    async openLookupDialog(): Promise<void> {
        this.setUiComponentProperties('isReferenceDialogOpen', true);
        lookupDialog(this.screenId, 'info', {
            contextNode: this.getUiComponentProperty('node'),
            createTunnelLinkText: undefined,
            fieldId: this.elementId,
            fieldProperties: this._getUiComponentProperties(this.screenId, this.elementId),
            isLinkCreateNewText: undefined,
            isMultiSelect: false,
            level: undefined,
            onCreateNewItemLinkClick: undefined,
            parentElementId: undefined,
            recordContext: undefined,
            searchText: undefined,
            selectedRecordId: (this.value as ClientNode | undefined)?._id,
            value: this.value,
            valueField: undefined,
        })
            .catch(noop)
            .then(([selection]) => {
                const { dispatch, getState } = getStore();
                handleChange<Partial<CollectionItem> | null>(
                    this.elementId,
                    selection ?? null,
                    async () => {
                        await setFieldValue(this.screenId, this.elementId, selection, true)(dispatch, getState);
                    },
                    this.validateWithDetails.bind(this),
                    () => {
                        triggerFieldEvent(this.screenId, this.elementId, 'onChange');
                    },
                );
            });
    }

    @ControlObjectProperty<PodProperties<CT, NodeType>, PodControlObject<NodeType, CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<PodProperties<CT, NodeType>, PodControlObject<NodeType, CT>>()
    /** Whether the value of the pod can be unset */
    canRemove?: boolean;

    @ControlObjectProperty<PodProperties<CT, NodeType>, PodControlObject<NodeType, CT>>()
    /** Placeholder to be displayed in the field body */
    placeholder?: string;

    @ControlObjectProperty<PodProperties<CT, NodeType>, PodControlObject<NodeType, CT>>()
    /** Lookup Dialog title **/
    lookupDialogTitle?: string;

    async refresh(): Promise<void> {
        await this._refresh({ keepPageInfo: true }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }

    focus(): void {
        this._focus();
    }
}
