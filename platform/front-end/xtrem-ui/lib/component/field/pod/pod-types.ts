import type { ClientNode } from '@sage/xtrem-client';
import type { OnTelemetryEventFunction } from '../../../redux/state';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { BlockControlObject, SectionControlObject } from '../../container/container-control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { PodNestedFieldTypes } from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasColumns,
    HasDropdownActions,
    HasDynamicLookupSuggestions,
    HasFilter,
    HasHeaderLabel,
    HasHelperText,
    HasLookupDialogTitle,
    HasNode,
    HasParent,
    HasPlaceholder,
    Sizable,
    Validatable,
} from '../traits';

export interface PodProperties<CT extends ScreenExtension<CT>, NestedRecordType extends ClientNode = any>
    extends EditableFieldProperties<CT, NestedRecordType>,
        Changeable<CT>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasColumns<CT, NestedRecordType, PodNestedFieldTypes>,
        HasDropdownActions<CT>,
        HasDynamicLookupSuggestions<CT, NestedRecordType>,
        HasFilter<CT, NestedRecordType>,
        HasHeaderLabel<CT, NestedRecordType>,
        HasHelperText,
        HasLookupDialogTitle<CT>,
        HasNode<CT>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>>,
        HasPlaceholder,
        Sizable,
        Validatable<CT> {
    /** Label on the add button that is displayed when the field is empty. */
    addButtonText?: string;
    /** Pod field can be removed and set to null, default: false */
    canRemove?: boolean;
    isReferenceDialogOpen?: boolean;
    onAddButtonClick?: (this: CT) => Promise<Partial<NestedRecordType>> | Partial<NestedRecordType>;
    onTelemetryEvent?: OnTelemetryEventFunction;
}

export interface PodDecoratorProperties<CT extends ScreenBase, NodeType extends ClientNode>
    extends Omit<PodProperties<CT, NodeType>, '_controlObjectType'> {}

export type PodComponentProperties = BaseEditableComponentProperties<PodDecoratorProperties<ScreenBase, any>, any>;
