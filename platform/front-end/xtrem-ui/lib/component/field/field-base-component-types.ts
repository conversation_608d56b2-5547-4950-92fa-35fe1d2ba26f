import type { ClientNode } from '@sage/xtrem-client';
import type { Dict, LocalizeLocale } from '@sage/xtrem-shared';
import type { ReduxResponsive } from '../../redux/state';
import type { PageArticleItem } from '../../service/layout-types';
import type { FormattedNodeDetails } from '../../service/metadata-types';
import type { ValidationResult } from '../../service/screen-base-definition';
import type { ContextType, NodePropertyType } from '../../types';
import type { EditableFieldProperties } from '../editable-field-control-object';
import type { NestedFieldHandlersArguments } from '../nested-fields';
import type { ReadonlyFieldProperties } from '../readonly-field-control-object';
import type { PropertyValueType } from './reference/reference-types';

export interface FieldWrapperExternalProps {
    item?: PageArticleItem;
    screenId: string;
    contextType?: ContextType;
    nestedReadOnlyField?: boolean;
    shouldRenderLabelInNestedReadOnlyMode?: boolean;
    availableColumns?: number;
    isParentDisabled?: boolean;
    /**
     * Indicates if any of the parents in the layout structure is hidden, it is required so we can cascade
     * down the hidden status and mark the hidden inputs not focusable
     * */
    isParentHidden?: boolean;
    fixedHeight?: number;
    isUsingInfiniteScroll?: boolean;
}

export interface FieldComponentExternalProperties extends FieldWrapperExternalProps {
    // TODO Item should be mandatory. Find a way to make it compatible with NestedFields
    bind?: PropertyValueType<ClientNode | any>;
    elementId: string;
    isParentDisabled?: boolean;
    /**
     * Indicates if any of the parents in the layout structure is hidden, it is required so we can cascade
     * down the hidden status and mark the hidden inputs not focusable
     * */
    isParentHidden?: boolean;
    fixedHeight?: number;
}

export interface ReadonlyFieldComponentProperties<FieldUiProperties extends ReadonlyFieldProperties, FieldValue> {
    browser?: ReduxResponsive;
    fieldProperties: FieldUiProperties;
    nodeTypes?: Dict<FormattedNodeDetails>;
    pageNode?: NodePropertyType;
    value?: FieldValue;
    onFocus: (row?: string, nestedField?: string) => void;
    isInFocus?: boolean;
    locale: LocalizeLocale;
    isParentReadOnly?: boolean;
}

export type BaseReadonlyComponentProperties<
    FieldUiProperties extends ReadonlyFieldProperties,
    FieldValue,
    AdditionalProperties = {},
> = FieldComponentExternalProperties &
    ReadonlyFieldComponentProperties<FieldUiProperties, FieldValue> &
    AdditionalProperties;

export interface EditableFieldComponentProperties<FieldUiProperties extends ReadonlyFieldProperties, FieldValue>
    extends ReadonlyFieldComponentProperties<FieldUiProperties, FieldValue> {
    validationErrors?: ValidationResult[];
    setFieldValue: (bind: string, value: FieldValue) => Promise<void>;
    validate: (bind: string, value: FieldValue) => Promise<ValidationResult[] | undefined>;
    removeNonNestedErrors: (bind: string) => void;
    setFieldProperties?: (elementId: string, value: FieldUiProperties) => void;
    isParentReadOnly?: boolean;
}

export type BaseEditableComponentProperties<
    FieldUiProperties extends ReadonlyFieldProperties,
    FieldValue,
    AdditionalProperties = {},
> = FieldComponentExternalProperties &
    EditableFieldComponentProperties<FieldUiProperties, FieldValue> &
    AdditionalProperties;

export interface ErrorableFieldComponentProperties<FieldUiProperties extends ReadonlyFieldProperties, FieldValue>
    extends EditableFieldComponentProperties<FieldUiProperties, FieldValue> {
    addInternalError?: (elementId: string, errorMessage: ValidationResult) => void;
    removeInternalError?: (elementId: string) => void;
}

export type BaseErrorableComponentProperties<
    FieldUiProperties extends EditableFieldProperties,
    FieldValue,
    AdditionalProperties = {},
> = FieldComponentExternalProperties &
    ErrorableFieldComponentProperties<FieldUiProperties, FieldValue> &
    AdditionalProperties;

export interface NestedFieldsAdditionalProperties {
    columnDefinition?: any;
    contextNode?: NodePropertyType;
    handlersArguments?: NestedFieldHandlersArguments;
    isNested?: boolean;
    isParentReadOnly?: boolean;
    level?: number;
    parentElementId?: string;
    recordContext?: any;
}

export type FieldBaseComponentProperties<
    FieldUiProperties extends EditableFieldProperties,
    FieldValue,
    AdditionalProperties,
> = BaseReadonlyComponentProperties<FieldUiProperties, FieldValue, AdditionalProperties> &
    EditableFieldComponentProperties<FieldUiProperties, FieldValue> &
    NestedFieldsAdditionalProperties;
