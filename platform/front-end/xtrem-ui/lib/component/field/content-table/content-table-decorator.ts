import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { ContentTableControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { ContentTableDecoratorProperties } from './content-table-types';

class ContentTableDecorator extends AbstractFieldDecorator<FieldKey.ContentTable> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = ContentTableControlObject;
}

/**
 * Initializes the decorated member as a [ContentTable]{@link ContentTableControlObject} field with the provided properties
 *
 * @param properties The properties that the [ContentTable]{@link ContentTableControlObject} field will be initialized with
 */
export function contentTable<T extends ScreenExtension<T>>(
    properties: ContentTableDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.ContentTable>(
        properties,
        ContentTableDecorator,
        FieldKey.ContentTable,
    );
}
export function contentTableOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<ContentTableDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.ContentTable>(properties);
}
