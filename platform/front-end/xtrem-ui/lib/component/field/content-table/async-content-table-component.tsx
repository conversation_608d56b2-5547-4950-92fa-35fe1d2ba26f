import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { ContentTableComponentProps } from './content-table-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';

const ConnectedContentTableComponent = React.lazy(() => import('./content-table-component'));

export function AsyncConnectedContentTableComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="80px" />}>
            <ConnectedContentTableComponent {...props} />
        </React.Suspense>
    );
}

const ContentTableComponent = React.lazy(() =>
    import('./content-table-component').then(c => ({ default: c.ContentTableComponent })),
);

export function AsyncContentTableComponent(props: ContentTableComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} bodyHeight="80px" />}>
            <ContentTableComponent {...props} />
        </React.Suspense>
    );
}
