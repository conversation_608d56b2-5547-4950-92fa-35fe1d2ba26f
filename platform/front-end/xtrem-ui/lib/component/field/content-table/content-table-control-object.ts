/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { Dict } from '@sage/xtrem-shared';
import type { DefaultPropertyType } from '@sage/xtrem-ui-components';
import type { ScreenBase } from '../../../service/screen-base';
import { showToast } from '../../../service/toast-service';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { ContentTableProperties } from './content-table-types';

/**
 * [Field]{@link EditableFieldControlObject} that holds a multi-line text value
 */
export class ContentTableControlObject<
    ReferencedItemType extends ClientNode = any,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.ContentTable, FieldComponentProps<FieldKey.ContentTable>> {
    static readonly defaultUiProperties: Partial<ContentTableProperties> = {
        ...EditableFieldControlObject.defaultUiProperties,
    };

    @ControlObjectProperty<ContentTableProperties<CT>, ContentTableControlObject<ReferencedItemType, CT>>()
    selectedProperties?: Dict<DefaultPropertyType>;

    @ControlObjectProperty<ContentTableProperties<CT>, ContentTableControlObject<ReferencedItemType, CT>>()
    node?: string;

    @ControlObjectProperty<ContentTableProperties<CT>, ContentTableControlObject<ReferencedItemType, CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<ContentTableProperties<CT>, ContentTableControlObject<ReferencedItemType, CT>>()
    /** Placeholder to be displayed in the field body */
    placeholder?: string;

    /** Refetches data from the server */
    async refresh(): Promise<void> {
        await this._refresh({ keepPageInfo: true }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }
}
