import * as React from 'react';
import { applyActionMocks, renderWithRedux, userEvent } from '../../../../__tests__/test-helpers';
import type { ScreenBase } from '../../../../service/screen-base';
import type { FieldInternalValue } from '../../../types';
import { FieldKey, GraphQLTypes } from '../../../types';
import ConnectedContentTableComponent from '../content-table-component';
import type { ContentTableDecoratorProperties, ContentTableFieldValue } from '../content-table-types';
import type { XtremAppState } from '../../../../redux/state';
import type { DeepPartial } from 'ts-essentials';
import type { Dict } from '@sage/xtrem-shared';
import { fireEvent, waitFor } from '@testing-library/react';
import * as abstractFieldUtils from '../../../../utils/abstract-fields-utils';
import type { FormattedNodeDetails } from '../../../../service/metadata-types';
import '@testing-library/jest-dom';
import { GraphQLKind } from '../../../../types';

jest.useFakeTimers();

const nodeTypes: Dict<FormattedNodeDetails> = {
    ShowCaseEmployee: {
        name: 'ShowCaseEmployee',
        title: 'ShowCaseEmployee',
        packageName: '@sage/xtrem-test',
        properties: {
            _access: {
                type: '_OutputAccessBinding',
                kind: GraphQLKind.List,
                isCollection: false,
            },
            _customData: {
                type: 'Json',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _sourceId: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _id: {
                type: 'Id',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            firstName: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            lastName: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            city: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            email: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            country: {
                type: 'ShowCaseCountry',
                kind: 'OBJECT',
                isCollection: false,
            },
            manager: {
                type: 'ShowCaseEmployee',
                kind: 'OBJECT',
                isCollection: false,
            },
            teamMembers: {
                type: 'ShowCaseEmployee',
                kind: 'OBJECT',
                isCollection: true,
            },
            _etag: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _createStamp: {
                type: 'Datetime',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _updateStamp: {
                type: 'Datetime',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
        },
        mutations: {},
    },
    ShowCaseCountry: {
        name: 'ShowCaseCountry',
        title: 'ShowCaseCountry',
        packageName: '@sage/xtrem-test',
        properties: {
            _access: {
                type: '_OutputAccessBinding',
                kind: GraphQLKind.List,
                isCollection: false,
            },
            _customData: {
                type: 'Json',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _sourceId: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _id: {
                type: 'Id',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            code: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            phoneCountryCode: {
                type: GraphQLTypes.Int,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            name: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            employeesInCountry: {
                type: 'ShowCaseEmployee',
                kind: 'OBJECT',
                isCollection: true,
            },
            _etag: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _createStamp: {
                type: 'Datetime',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _updateStamp: {
                type: 'Datetime',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
        },
        mutations: {},
    },
};
const contentTableValue: ContentTableFieldValue = [];

const defaultContentTableProps = {
    node: 'ShowCaseEmployee',
    selectedProperties: {
        lastName: {
            label: 'Last name',
            data: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
                name: 'lastName',
                canFilter: true,
                canSort: true,
                label: 'Last name',
                dataType: '',
                enumType: '',
                isCustom: false,
                isStored: true,
                isOnInputType: true,
                isOnOutputType: true,
                targetNode: '',
                isMutable: false,
            },
            id: 'lastName',
            key: 'lastName',
            labelKey: 'Last name',
            labelPath: 'Last name',
        },
        firstName: {
            label: 'First name',
            data: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
                name: 'firstName',
                canFilter: true,
                canSort: true,
                label: 'First name',
                dataType: '',
                enumType: '',
                isCustom: false,
                isStored: true,
                isOnInputType: true,
                isOnOutputType: true,
                targetNode: '',
                isMutable: false,
            },
            id: 'firstName',
            key: 'firstName',
            labelKey: 'First name',
            labelPath: 'First name',
        },
        email: {
            label: 'Email',
            data: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
                name: 'email',
                canFilter: true,
                canSort: true,
                label: 'Email',
                dataType: '',
                enumType: '',
                isCustom: false,
                isStored: true,
                isOnInputType: true,
                isOnOutputType: true,
                targetNode: '',
                isMutable: false,
            },
            id: 'email',
            key: 'email',
            labelKey: 'Email',
            labelPath: 'Email',
        },
        city: {
            label: 'City',
            data: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
                name: 'city',
                canFilter: true,
                canSort: true,
                label: 'City',
                dataType: '',
                enumType: '',
                isCustom: false,
                isStored: true,
                isOnInputType: true,
                isOnOutputType: true,
                targetNode: '',
                isMutable: false,
            },
            id: 'city',
            key: 'city',
            labelKey: 'City',
            labelPath: 'City',
        },
    },
} satisfies Partial<ContentTableDecoratorProperties<ScreenBase>>;

describe('content-table component', () => {
    let handleChangeSpy: jest.SpyInstance;

    beforeEach(() => {
        handleChangeSpy = jest.spyOn(abstractFieldUtils, 'handleChange');
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.resetAllMocks();
        jest.useRealTimers();
        applyActionMocks();
    });

    const screenId = 'TestPage';
    const fieldId = 'test-content-table-field';

    const setup = (
        contentTableProps: Partial<ContentTableDecoratorProperties<ScreenBase>> = {},
        value: FieldInternalValue<FieldKey.ContentTable> = contentTableValue,
    ) => {
        const fieldProperties = { ...defaultContentTableProps, ...contentTableProps };

        const initialState: DeepPartial<XtremAppState> = {
            nodeTypes,
            screenDefinitions: {
                [screenId]: {
                    metadata: {
                        uiComponentProperties: {
                            [fieldId]: fieldProperties,
                        },
                    },
                    values: {
                        ...(value && { [fieldId]: value }),
                    },
                    errors: {},
                },
            },
        };
        const utils = renderWithRedux<FieldKey.ContentTable, any>(
            <ConnectedContentTableComponent screenId={screenId} elementId={fieldId} />,
            {
                initialState,
                fieldType: FieldKey.ContentTable,
                fieldValue: value,
                fieldProperties,
                elementId: fieldId,
                screenId,
                mockActions: true,
            },
        );

        const contentTableField = utils.getByTestId('e-field-bind-test-content-table-field', { exact: false });

        const getPropertyCell = async (rowId: number) => {
            return utils.findByTestId(`e-widget-editor-content-property-${rowId}`) as Promise<HTMLInputElement>;
        };

        const setPropertyCell = async ({ rowId, value }: { rowId: number; value: string }) => {
            await userEvent.type(await getPropertyCell(rowId), value);
            await waitFor(async () => {
                expect((await getPropertyCell(rowId)).value).toBe(value);
            });
        };

        const getTitleCell = async (rowId: number) => {
            return utils.findByTestId(`e-widget-editor-content-title-${rowId}`) as Promise<HTMLInputElement>;
        };

        const setTitleCell = async ({ rowId, value }: { rowId: number; value: string }) => {
            await userEvent.type(await getTitleCell(rowId), value);
            await waitFor(async () => {
                expect((await getTitleCell(rowId)).value).toBe(value);
            });
        };

        const getGroupCell = async (rowId: number) => {
            return utils.findByTestId(`e-widget-editor-content-group-${rowId}`) as Promise<HTMLInputElement>;
        };

        const setGroupCell = async ({
            rowId,
            value,
            expectedValue = value,
        }: {
            rowId: number;
            value: string;
            expectedValue?: string;
        }) => {
            const groupCell = await getGroupCell(rowId);
            fireEvent.change(groupCell, { target: { value } });
            await waitFor(async () => {
                expect((await getGroupCell(rowId)).value).toBe(expectedValue);
            });
        };

        const getOperationCell = async (rowId: number) => {
            return utils.findByTestId(`e-widget-editor-content-operation-${rowId}`) as Promise<HTMLInputElement>;
        };

        const setOperationCell = async ({ rowId, value }: { rowId: number; value: string }) => {
            await userEvent.type(await getOperationCell(rowId), value);
            await waitFor(async () => {
                expect((await getOperationCell(rowId)).value).toBe(value);
            });
        };

        const getSortingCell = async (rowId: number) => {
            return utils.findByTestId(`e-widget-editor-content-sorting-${rowId}`) as Promise<HTMLInputElement>;
        };

        const setSortingCell = async ({ rowId, value }: { rowId: number; value: string }) => {
            await userEvent.type(await getSortingCell(rowId), value);
            await waitFor(async () => {
                expect((await getSortingCell(rowId)).value).toBe(value);
            });
        };

        return {
            ...utils,
            contentTableField,
            getPropertyCell,
            setPropertyCell,
            getTitleCell,
            getGroupCell,
            setTitleCell,
            setGroupCell,
            getOperationCell,
            setOperationCell,
            getSortingCell,
            setSortingCell,
        };
    };

    it('can render with redux with defaults', async () => {
        const { contentTableField, findByTestId } = setup({ title: 'Test Field Title' });
        expect(contentTableField).toHaveTextContent('Test Field Title');
        expect(contentTableField).toHaveTextContent('No data to display');
        expect((await findByTestId('add-item-button')).textContent).toBe('Add column');
    });

    it('can render with redux without selected properties', async () => {
        const { contentTableField, queryByTestId } = setup({ title: 'Test Field Title', selectedProperties: {} });
        expect(contentTableField).toHaveTextContent('Test Field Title');
        expect(contentTableField).toHaveTextContent('Select a property to add a value');
        expect(queryByTestId('add-item-button')).toBe(null);
    });

    it('can add multiple rows and change their group', async () => {
        const {
            findByTestId,
            queryByTestId,
            findAllByTestId,
            setPropertyCell,
            getPropertyCell,
            getTitleCell,
            getGroupCell,
            getOperationCell,
            getSortingCell,
            setOperationCell,
            setGroupCell,
        } = setup();
        expect(queryByTestId('1')).toBe(null);
        await userEvent.click(await findByTestId('add-item-button'));
        await findByTestId('1');
        const addButtons = await findAllByTestId('flat-table-add-button');
        expect(addButtons.length).toBe(1);
        const removeButtons = await findAllByTestId('flat-table-remove-button');
        expect(removeButtons.length).toBe(1);

        const propertyCell = await getPropertyCell(1);
        expect(propertyCell.disabled).toBe(false);

        const titleCell = await getTitleCell(1);
        expect(titleCell.disabled).toBe(true);

        const groupCell = await getGroupCell(1);
        expect(groupCell.disabled).toBe(false);
        expect(groupCell.value).toBe('Group 1');

        expect(queryByTestId('e-widget-editor-content-operation-1')).toBe(null);
        expect(queryByTestId('e-widget-editor-content-sorting-1')).toBe(null);

        expect(handleChangeSpy).not.toHaveBeenCalled();

        await setPropertyCell({ rowId: 1, value: 'Last name' });
        expect((await getTitleCell(1)).value).toBe('Last name');

        expect((await getOperationCell(1)).value).toBe('');
        expect((await getSortingCell(1)).value).toBe('');

        await setOperationCell({ rowId: 1, value: 'Distinct count' });

        await waitFor(async () => {
            expect((await getSortingCell(1)).value).toBe('Ascending');
        });

        await waitFor(() => {
            expect(handleChangeSpy).toHaveBeenCalledTimes(2);
            // default title is set
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                1,
                'test-content-table-field',
                [
                    {
                        _id: '1',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'Last name',
                        path: 'lastName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Last name',
                                name: 'lastName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'lastName',
                            label: 'Last name',
                            labelPath: 'Last name',
                            path: undefined,
                        },
                        title: 'Last name',
                    },
                ],
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
            // default operation & sorting are set
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                2,
                'test-content-table-field',
                [
                    {
                        _id: '1',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'Last name',
                        operation: 'distinctCount',
                        path: 'lastName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Last name',
                                name: 'lastName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'lastName',
                            label: 'Last name',
                            labelPath: 'Last name',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'Last name',
                    },
                ],
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
        });

        // add second row
        expect(queryByTestId('2')).toBe(null);
        await userEvent.click(await findByTestId('flat-table-add-button'));
        await findByTestId('2');

        await setPropertyCell({ rowId: 2, value: 'First name' });
        expect((await getTitleCell(2)).value).toBe('First name');

        expect((await getGroupCell(2)).value).toBe('Group 1');

        expect((await getOperationCell(2)).value).toBe('');
        expect((await getSortingCell(2)).value).toBe('');

        await setOperationCell({ rowId: 2, value: 'Distinct count' });

        await waitFor(async () => {
            expect((await getSortingCell(2)).value).toBe('Ascending');
        });

        await waitFor(() => {
            expect(handleChangeSpy).toHaveBeenCalledTimes(4);
            // default title is set
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                3,
                'test-content-table-field',
                [
                    {
                        _id: '1',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'Last name',
                        operation: 'distinctCount',
                        path: 'lastName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Last name',
                                name: 'lastName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'lastName',
                            label: 'Last name',
                            labelPath: 'Last name',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'Last name',
                    },
                    {
                        _id: '2',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'First name',
                        path: 'firstName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'First name',
                                name: 'firstName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'firstName',
                            label: 'First name',
                            labelPath: 'First name',
                            path: undefined,
                        },
                        title: 'First name',
                    },
                ],
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
            // default operation & sorting are set
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                4,
                'test-content-table-field',
                [
                    {
                        _id: '1',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'Last name',
                        operation: 'distinctCount',
                        path: 'lastName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Last name',
                                name: 'lastName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'lastName',
                            label: 'Last name',
                            labelPath: 'Last name',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'Last name',
                    },
                    {
                        _id: '2',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'First name',
                        operation: 'distinctCount',
                        path: 'firstName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'First name',
                                name: 'firstName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'firstName',
                            label: 'First name',
                            labelPath: 'First name',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'First name',
                    },
                ],
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
        });

        // add third row
        expect(queryByTestId('3')).toBe(null);
        await userEvent.click(await findByTestId('flat-table-add-button'));
        await findByTestId('3');

        await setPropertyCell({ rowId: 3, value: 'Email' });
        expect((await getTitleCell(3)).value).toBe('Email');

        expect((await getGroupCell(3)).value).toBe('Group 1');

        expect((await getOperationCell(3)).value).toBe('');
        expect((await getSortingCell(3)).value).toBe('');

        await setOperationCell({ rowId: 3, value: 'Distinct count' });

        await waitFor(async () => {
            expect((await getSortingCell(3)).value).toBe('Ascending');
        });

        await waitFor(() => {
            expect(handleChangeSpy).toHaveBeenCalledTimes(6);
            // default title is set
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                5,
                'test-content-table-field',
                [
                    {
                        _id: '1',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'Last name',
                        operation: 'distinctCount',
                        path: 'lastName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Last name',
                                name: 'lastName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'lastName',
                            label: 'Last name',
                            labelPath: 'Last name',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'Last name',
                    },
                    {
                        _id: '2',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'First name',
                        operation: 'distinctCount',
                        path: 'firstName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'First name',
                                name: 'firstName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'firstName',
                            label: 'First name',
                            labelPath: 'First name',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'First name',
                    },
                    {
                        _id: '3',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'Email',
                        path: 'email',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Email',
                                name: 'email',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'email',
                            label: 'Email',
                            labelPath: 'Email',
                            path: undefined,
                        },
                        title: 'Email',
                    },
                ],
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
            // default operation & sorting are set
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                6,
                'test-content-table-field',
                [
                    {
                        _id: '1',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'Last name',
                        operation: 'distinctCount',
                        path: 'lastName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Last name',
                                name: 'lastName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'lastName',
                            label: 'Last name',
                            labelPath: 'Last name',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'Last name',
                    },
                    {
                        _id: '2',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'First name',
                        operation: 'distinctCount',
                        path: 'firstName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'First name',
                                name: 'firstName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'firstName',
                            label: 'First name',
                            labelPath: 'First name',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'First name',
                    },
                    {
                        _id: '3',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'Email',
                        operation: 'distinctCount',
                        path: 'email',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Email',
                                name: 'email',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'email',
                            label: 'Email',
                            labelPath: 'Email',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'Email',
                    },
                ],
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
        });

        // set group 2 for last name
        await setGroupCell({ rowId: 1, value: 'Group 2' });

        await waitFor(() => {
            expect(handleChangeSpy).toHaveBeenCalledTimes(7);
            // default title is set
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                7,
                'test-content-table-field',
                [
                    {
                        _id: '2',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'First name',
                        operation: 'distinctCount',
                        path: 'firstName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'First name',
                                name: 'firstName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'firstName',
                            label: 'First name',
                            labelPath: 'First name',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'First name',
                    },
                    {
                        _id: '3',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'Email',
                        operation: 'distinctCount',
                        path: 'email',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Email',
                                name: 'email',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'email',
                            label: 'Email',
                            labelPath: 'Email',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'Email',
                    },
                    {
                        _id: '1',
                        divisor: undefined,
                        formatting: undefined,
                        group: 1,
                        labelPath: 'Last name',
                        operation: 'distinctCount',
                        path: 'lastName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Last name',
                                name: 'lastName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'lastName',
                            label: 'Last name',
                            labelPath: 'Last name',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'Last name',
                    },
                ],
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
        });

        // set group 3 for first name
        await setGroupCell({ rowId: 2, value: 'Group 3' });

        await waitFor(() => {
            expect(handleChangeSpy).toHaveBeenCalledTimes(8);
            // default title is set
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                8,
                'test-content-table-field',
                [
                    {
                        _id: '3',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'Email',
                        operation: 'distinctCount',
                        path: 'email',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Email',
                                name: 'email',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'email',
                            label: 'Email',
                            labelPath: 'Email',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'Email',
                    },
                    {
                        _id: '1',
                        divisor: undefined,
                        formatting: undefined,
                        group: 1,
                        labelPath: 'Last name',
                        operation: 'distinctCount',
                        path: 'lastName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Last name',
                                name: 'lastName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'lastName',
                            label: 'Last name',
                            labelPath: 'Last name',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'Last name',
                    },
                    {
                        _id: '2',
                        divisor: undefined,
                        formatting: undefined,
                        group: 2,
                        labelPath: 'First name',
                        operation: 'distinctCount',
                        path: 'firstName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'First name',
                                name: 'firstName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'firstName',
                            label: 'First name',
                            labelPath: 'First name',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'First name',
                    },
                ],
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
        });

        // set group 4 for last name
        await setGroupCell({ rowId: 1, value: 'Group 4', expectedValue: 'Group 3' });

        await waitFor(() => {
            expect(handleChangeSpy).toHaveBeenCalledTimes(9);
            // default title is set
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                9,
                'test-content-table-field',
                [
                    {
                        _id: '3',
                        divisor: undefined,
                        formatting: undefined,
                        group: 0,
                        labelPath: 'Email',
                        operation: 'distinctCount',
                        path: 'email',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Email',
                                name: 'email',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'email',
                            label: 'Email',
                            labelPath: 'Email',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'Email',
                    },
                    {
                        _id: '2',
                        divisor: undefined,
                        formatting: undefined,
                        group: 1,
                        labelPath: 'First name',
                        operation: 'distinctCount',
                        path: 'firstName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'First name',
                                name: 'firstName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'firstName',
                            label: 'First name',
                            labelPath: 'First name',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'First name',
                    },
                    {
                        _id: '1',
                        divisor: undefined,
                        formatting: undefined,
                        group: 2,
                        labelPath: 'Last name',
                        operation: 'distinctCount',
                        path: 'lastName',
                        presentation: 'Text',
                        property: {
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Last name',
                                name: 'lastName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            id: 'lastName',
                            label: 'Last name',
                            labelPath: 'Last name',
                            path: undefined,
                        },
                        sorting: 'ascending',
                        title: 'Last name',
                    },
                ],
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
        });
    }, 90000);
});
