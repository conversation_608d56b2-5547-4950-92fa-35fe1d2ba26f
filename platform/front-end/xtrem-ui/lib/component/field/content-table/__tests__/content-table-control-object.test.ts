import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import { ContentTableControlObject } from '../../../control-objects';
import type { FieldKey } from '../../../types';
import type { ContentTableFieldValue, ContentTableProperties } from '../content-table-types';

describe('ContentTable Field', () => {
    let contentTableField: ContentTableControlObject;
    let fieldProperties: ContentTableProperties;

    const contentTableValue: ContentTableFieldValue = [];

    beforeEach(() => {
        fieldProperties = {
            title: 'TEST_FIELD_TITLE',
            node: 'ShowCaseEmployee',
        };
    });

    describe('getters and setters', () => {
        beforeEach(() => {
            contentTableField = buildControlObject<FieldKey.ContentTable>(ContentTableControlObject, {
                fieldValue: contentTableValue,
                fieldProperties,
            });
        });

        it('getting field value', () => {
            expect(contentTableField.value).toEqual(contentTableValue);
        });

        it('getting isReadOnly', () => {
            expect(contentTableField.isReadOnly).toBeFalsy();
        });

        it('should set the title', () => {
            const testFixture = 'Test ContentTable Field Title';
            expect(fieldProperties.title).not.toEqual(testFixture);
            contentTableField.title = testFixture;
            expect(fieldProperties.title).toEqual(testFixture);
        });

        it('should set the helper text', () => {
            const testFixture = 'Test ContentTable Field Helper Text';
            expect(fieldProperties.helperText).not.toEqual(testFixture);
            contentTableField.helperText = testFixture;
            expect(fieldProperties.helperText).toEqual(testFixture);
        });

        it('should set isDisabled property', () => {
            const testFixture = true;
            expect(fieldProperties.isDisabled).not.toEqual(testFixture);
            contentTableField.isDisabled = testFixture;
            expect(fieldProperties.isDisabled).toEqual(testFixture);
        });

        it('should set isHelperTextHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isHelperTextHidden).not.toEqual(testFixture);
            contentTableField.isHelperTextHidden = testFixture;
            expect(fieldProperties.isHelperTextHidden).toEqual(testFixture);
        });

        it('should set isHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isHidden).not.toEqual(testFixture);
            contentTableField.isHidden = testFixture;
            expect(fieldProperties.isHidden).toEqual(testFixture);
        });

        it('should set isReadOnly property', () => {
            const testFixture = true;
            expect(fieldProperties.isReadOnly).not.toEqual(testFixture);
            contentTableField.isReadOnly = testFixture;
            expect(fieldProperties.isReadOnly).toEqual(testFixture);
        });

        it('should set isTitleHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isTitleHidden).not.toEqual(testFixture);
            contentTableField.isTitleHidden = testFixture;
            expect(fieldProperties.isTitleHidden).toEqual(testFixture);
        });
    });

    describe('focus', () => {
        const focus = jest.fn();
        beforeEach(() => {
            contentTableField = buildControlObject<FieldKey.ContentTable>(ContentTableControlObject, {
                fieldValue: contentTableValue,
                fieldProperties,
                focus,
            });
        });

        it('should execute focus', () => {
            jest.useFakeTimers();
            expect(focus).not.toHaveBeenCalled();
            contentTableField.focus();
            jest.runAllTimers();
            expect(focus).toHaveBeenCalled();
        });
    });
});
