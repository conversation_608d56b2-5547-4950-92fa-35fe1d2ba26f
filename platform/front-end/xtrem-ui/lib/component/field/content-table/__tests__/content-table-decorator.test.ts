import type { Page } from '../../../..';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { contentTable } from '../content-table-decorator';
import type { ContentTableDecoratorProperties } from '../content-table-types';

describe('ContentTable decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'contentTable';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should set default values when no component properties provided', () => {
        contentTable({ node: 'ShowCaseEmployee' })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: ContentTableDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as ContentTableDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onChange).toBeUndefined();
        expect(mappedComponentProperties.onClick).toBeUndefined();
    });

    it('should inherit false abstract-field booleans when no provided', () => {
        contentTable({ node: 'ShowCaseEmployee' })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: ContentTableDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as ContentTableDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.isHiddenMobile).toBe(false);
        expect(mappedComponentProperties.isHiddenDesktop).toBe(false);
        expect(mappedComponentProperties.isFullWidth).toBe(false);
        expect(mappedComponentProperties.isHidden).toBe(false);
        expect(mappedComponentProperties.isTransient).toBe(false);
    });

    it('should set values when component properties provided', () => {
        const clickFunc: () => void = jest.fn().mockImplementation(() => {});
        const changeFunc: () => void = jest.fn().mockImplementation(() => {});

        contentTable({
            onChange: changeFunc,
            onClick: clickFunc,
            helperText: 'helper text',
            title: 'title',
            node: 'ShowCaseEmployee',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: ContentTableDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as ContentTableDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onChange).not.toBeUndefined();
        expect(mappedComponentProperties.onChange).toBe(changeFunc);
        expect(mappedComponentProperties.onClick).not.toBeUndefined();
        expect(mappedComponentProperties.onClick).toBe(clickFunc);
        expect(mappedComponentProperties.helperText).toBe('helper text');
        expect(mappedComponentProperties.title).toBe('title');
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(contentTable, pageMetadata, fieldId);
        });
    });
});
