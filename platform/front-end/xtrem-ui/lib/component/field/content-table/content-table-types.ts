import type { ClientNode } from '@sage/xtrem-client';
import type { Dict } from '@sage/xtrem-shared';
import type { DefaultPropertyType, TableContentWithGroupsProps } from '@sage/xtrem-ui-components';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type { Changeable, Clickable, ExtensionField, HasParent, HasPlaceholder, Sizable } from '../traits';

export interface ContentTableProperties<CT extends ScreenBase = ScreenBase, ContextNodeType = void>
    extends EditableFieldProperties<CT, ContextNodeType>,
        HasPlaceholder,
        Sizable {
    /** The GraphQL node that the field suggestions will be fetched from */
    node: string;
    selectedProperties?: Dict<DefaultPropertyType>;
}

export interface ContentTableDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<ContentTableProperties<CT>, '_controlObjectType'>,
        Changeable<CT>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>>,
        Sizable {}

export type ContentTableExtensionDecoratorProperties<CT extends ScreenExtension<CT>> =
    ChangeableOverrideDecoratorProperties<ContentTableDecoratorProperties<Extend<CT>>, CT>;

export type ContentTableComponentProps<T extends ClientNode = any> = BaseEditableComponentProperties<
    ContentTableProperties<any, T>,
    ContentTableFieldValue
>;

export type ContentTableFieldValue = TableContentWithGroupsProps['value'];
