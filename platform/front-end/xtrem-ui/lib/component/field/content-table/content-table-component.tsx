import { TableContentWithGroups, usePrevious } from '@sage/xtrem-ui-components';
import * as React from 'react';
import { connect } from 'react-redux';
import { localize } from '../../../service/i18n-service';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { ContentTableComponentProps } from './content-table-types';
import { isEqual } from 'lodash';
import {
    getFieldHelperText,
    getFieldTitle,
    isFieldDisabled,
    isFieldHelperTextHidden,
    isFieldTitleHidden,
} from '../carbon-helpers';
import Label from 'carbon-react/esm/__internal__/label';
import { HelperText } from '../carbon-utility-components';
import { objectKeys } from '@sage/xtrem-shared';

export const changeEventHandler = (screenId: string, elementId: string) => (): Promise<void> =>
    triggerFieldEvent(screenId, elementId, 'onChange');

export function ContentTableComponent(props: ContentTableComponentProps): React.ReactElement {
    const previousExternalValue = usePrevious(props.value ?? []);
    const [properties, setProperties] = React.useState<NonNullable<ContentTableComponentProps['value']>>(
        props.value ?? [],
    );

    const isAddButtonHidden = React.useMemo(
        () => objectKeys(props.fieldProperties.selectedProperties ?? {}).length === 0,
        [props.fieldProperties.selectedProperties],
    );

    const onChange = React.useCallback<React.ComponentProps<typeof TableContentWithGroups>['onChange']>(
        (newProperties): void => {
            if (!isEqual(newProperties ?? [], properties)) {
                setProperties(newProperties);
            }
        },
        [properties],
    );

    React.useEffect(() => {
        const propertiesChanged = !isEqual(props.value ?? [], properties);
        const isExternalValueUpdate = !isEqual(previousExternalValue, props.value ?? []);

        if (propertiesChanged && !isExternalValueUpdate) {
            handleChange(
                props.elementId,
                properties,
                props.setFieldValue,
                props.validate,
                changeEventHandler(props.screenId, props.elementId),
            );
        }
    }, [
        previousExternalValue,
        props.elementId,
        props.screenId,
        props.setFieldValue,
        props.validate,
        props.value,
        properties,
    ]);

    React.useEffect(() => {
        const newValue = props.value ?? [];
        const isExternalValueUpdate = !isEqual(previousExternalValue, props.value ?? []);
        if (!isEqual(newValue, properties) && isExternalValueUpdate) {
            setProperties(newValue);
        }
    }, [previousExternalValue, props.value, properties]);

    const isDisabled = isFieldDisabled(props.screenId, props.fieldProperties, props.value, null);

    const title = getFieldTitle(props.screenId, props.fieldProperties, null);
    const isTitleHidden = isFieldTitleHidden(props.screenId, props.fieldProperties, null);
    const helperText = getFieldHelperText(props.screenId, props.fieldProperties, null);
    const isHelperTextHidden = isFieldHelperTextHidden(props.screenId, props.fieldProperties, null);

    return (
        <CarbonWrapper
            {...props}
            className="e-content-table-field"
            componentName="content-table"
            noReadOnlySupport={true}
            value={props.value}
        >
            {title && !isTitleHidden && (
                <div className="e-field-title">
                    <Label error={props.validationErrors?.[0]?.message} htmlFor={undefined as unknown as string}>
                        {title}
                    </Label>
                </div>
            )}
            <div data-testid="e-content-table-field-wrapper">
                <TableContentWithGroups
                    isAddButtonHidden={isAddButtonHidden}
                    localize={localize}
                    isDisabled={isDisabled}
                    selectedItems={props.fieldProperties.selectedProperties}
                    onChange={onChange}
                    value={properties}
                />
            </div>
            {helperText && !isHelperTextHidden && <HelperText helperText={helperText} />}
        </CarbonWrapper>
    );
}

export const ConnectedContentTableComponent = connect(mapStateToProps(), mapDispatchToProps())(ContentTableComponent);

export default ConnectedContentTableComponent;
