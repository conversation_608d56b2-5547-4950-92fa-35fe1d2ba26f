import type { ButtonToggleGroup } from 'carbon-react/esm/components/button-toggle';
import I18nProvider from 'carbon-react/esm/components/i18n-provider';
import React from 'react';
import { connect } from 'react-redux';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { TimeComponentProps } from './time-types';
import { useTime } from '../../ui/time/use-time';
import {
    generateFieldId,
    getFieldHelperText,
    getFieldIndicatorStatus,
    getFieldTitle,
    isFieldDisabled,
    isFieldHelperTextHidden,
    isFieldReadOnly,
    isFieldTitleHidden,
} from '../carbon-helpers';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { localize } from '../../../service/i18n-service';
import { TimeComponent as Time } from '../../ui/time/time-component';

export function TimeComponent({
    locale,
    isInFocus,
    elementId,
    screenId,
    fieldProperties,
    validationErrors,
    value,
    isParentDisabled,
    isParentHidden,
    setFieldValue,
    validate,
    contextType,
    ...rest
}: TimeComponentProps): React.ReactElement {
    const triggerChangeListener = React.useCallback(
        () => (): void => {
            triggerFieldEvent(screenId, elementId, 'onChange');
        },
        [elementId, screenId],
    );

    const onChange = React.useCallback(
        (newValue: string) => {
            handleChange(elementId, newValue, setFieldValue, validate, triggerChangeListener());
        },
        [elementId, setFieldValue, triggerChangeListener, validate],
    );

    const {
        componentRef,
        dataTestId,
        hasAmPmToggle,
        hoursRef,
        maxHours,
        minHours,
        minutesRef,
        onHoursBlur,
        onHoursChange,
        onKeyDown,
        onMinutesBlur,
        onMinutesChange,
        providerLocale,
        state,
        toggleChange,
    } = useTime({ elementId, locale, isInFocus, onChange, value });

    const isValid = React.useMemo(() => (validationErrors?.length ?? 0) === 0, [validationErrors]);
    const isTitleHidden = React.useMemo(
        () => isFieldTitleHidden(screenId, fieldProperties, null),
        [fieldProperties, screenId],
    );
    const helperText = React.useMemo(
        () => getFieldHelperText(screenId, fieldProperties, null),
        [fieldProperties, screenId],
    );
    const isHelperTextHidden = React.useMemo(
        () => isFieldHelperTextHidden(screenId, fieldProperties, null),
        [fieldProperties, screenId],
    );
    const isDisabled = React.useMemo(
        () => isFieldDisabled(screenId, fieldProperties, state.value, null),
        [fieldProperties, screenId, state.value],
    );
    const isMandatory = React.useMemo(
        () =>
            resolveByValue<boolean>({
                propertyValue: fieldProperties.isMandatory,
                skipHexFormat: true,
                screenId,
                fieldValue: state.value,
                rowValue: null,
            }),
        [fieldProperties.isMandatory, screenId, state.value],
    );

    const title = React.useMemo(() => {
        const resolvedTitle = getFieldTitle(screenId, fieldProperties, null);
        return `${resolvedTitle || ''}${isMandatory ? ' *' : ''}`;
    }, [fieldProperties, screenId, isMandatory]);

    const isReadOnly = React.useMemo(
        () =>
            isFieldDisabled(screenId, fieldProperties, value, null) ||
            isFieldReadOnly(screenId, fieldProperties, value, null),
        [fieldProperties, screenId, value],
    );

    const { error, warning, info } = React.useMemo(
        () =>
            getFieldIndicatorStatus({
                validationErrors,
                screenId,
                value,
                fieldProperties,
                isParentDisabled,
                isParentHidden,
            }),
        [fieldProperties, isParentDisabled, isParentHidden, screenId, validationErrors, value],
    );

    const onToggleChange = React.useCallback<NonNullable<React.ComponentProps<typeof ButtonToggleGroup>['onChange']>>(
        (_, toggle) => {
            toggleChange(toggle as 'AM' | 'PM');
        },
        [toggleChange],
    );

    const fieldId = React.useMemo(
        () => generateFieldId({ screenId, elementId, contextType, fieldProperties, isNested: false }),
        [contextType, elementId, fieldProperties, screenId],
    );

    const onClick = React.useCallback(() => {
        triggerFieldEvent(screenId, elementId, 'onClick');
    }, [elementId, screenId]);

    return (
        <CarbonWrapper
            contextType={contextType}
            elementId={elementId}
            fieldProperties={fieldProperties}
            isInFocus={isInFocus}
            isParentDisabled={isParentDisabled}
            isParentHidden={isParentHidden}
            locale={locale}
            screenId={screenId}
            {...rest}
            noReadOnlySupport={true}
            className="e-time-field"
            componentName="time"
            componentRef={componentRef}
            value={state.value}
        >
            {((title && !isTitleHidden) || error || warning || info) && (
                <FieldLabel
                    id={fieldId}
                    label={title}
                    errorMessage={error}
                    warningMessage={warning}
                    infoMessage={info}
                />
            )}
            <I18nProvider locale={providerLocale}>
                <Time
                    screenId={screenId}
                    elementId={elementId}
                    dataTestId={dataTestId}
                    fieldId={fieldId}
                    hasAmPmToggle={hasAmPmToggle}
                    hoursRef={hoursRef}
                    isDisabled={isDisabled}
                    isFullWidth={fieldProperties.isFullWidth}
                    isReadOnly={isReadOnly}
                    isValid={isValid}
                    localize={localize}
                    maxHours={maxHours}
                    minHours={minHours}
                    minutesRef={minutesRef}
                    onClick={onClick}
                    onHoursBlur={onHoursBlur}
                    onHoursChange={onHoursChange}
                    onKeyDown={onKeyDown}
                    onMinutesBlur={onMinutesBlur}
                    onMinutesChange={onMinutesChange}
                    onToggleChange={onToggleChange}
                    state={state}
                />

                {helperText && !isHelperTextHidden && <HelperText helperText={helperText} />}
            </I18nProvider>
        </CarbonWrapper>
    );
}

export const ConnectedTimeComponent = connect(mapStateToProps(), mapDispatchToProps())(TimeComponent);

export default ConnectedTimeComponent;
