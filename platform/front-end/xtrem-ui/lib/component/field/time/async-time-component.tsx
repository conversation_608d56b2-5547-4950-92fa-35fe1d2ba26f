import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { TimeComponentProps } from './time-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const ConnectedTimeComponent = React.lazy(() => import('./time-component'));

export function AsyncConnectedTimeComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedTimeComponent {...props} />
        </React.Suspense>
    );
}

const TimeComponent = React.lazy(() => import('./time-component').then(c => ({ default: c.TimeComponent })));

export function AsyncTimeComponent(props: TimeComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <TimeComponent {...props} />
        </React.Suspense>
    );
}
