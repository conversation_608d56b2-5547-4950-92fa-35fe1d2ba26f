import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedPropertiesWrapper } from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasParent,
    Nested,
    NestedChangeable,
    NestedValidatable,
    Sizable,
    Validatable,
} from '../traits';

export interface TimeProperties<CT extends ScreenExtension<CT> = ScreenBase, NodeType = any>
    extends EditableFieldProperties<CT, NodeType>,
        Sizable {}

export interface TimeDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    ReferencedItemType extends ClientNode = any,
> extends Omit<TimeProperties<CT, ReferencedItemType>, '_controlObjectType'>,
        Changeable<CT>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>>,
        Sizable,
        Validatable<CT, string> {}

export interface NestedTimeProperties<CT extends ScreenBase = ScreenBase, ContextNodeType extends ClientNode = any>
    extends NestedPropertiesWrapper<TimeProperties<CT, ContextNodeType>>,
        NestedChangeable<CT>,
        Nested<ContextNodeType>,
        Sizable,
        NestedValidatable<CT, string, ContextNodeType> {}

export type TimeExtensionDecoratorProperties<
    CT extends ScreenExtension<CT>,
    ReferencedItemType extends ClientNode = any,
> = ChangeableOverrideDecoratorProperties<TimeDecoratorProperties<Extend<CT>, ReferencedItemType>, CT>;

export type TimeComponentProps<T extends ClientNode = any> = BaseEditableComponentProperties<
    TimeProperties<any, T>,
    string
> & {
    onChange?: (value: string) => void;
};
