PATH: XTREEM/UI+Field+Widgets/Time+Field

## Introduction

Time fields can be used to represent a time value, i.e. a string with the following format: HH:mm:ss.

## Example

```ts
@ui.decorators.timeField<Page>({
    bind: 'time',
    helperText: 'Helper text displayed below the field.',
    isDisabled: false,
    isFullWidth: false,
    isHelperTextHidden: true,
    isHidden: false,
    isMandatory: false,
    isNewEnabled: true,
    isReadOnly: false,
    isTransient: false,
    placeholder: 'Time',
    size: 'medium',
    title: 'Time Field',
    parent () {
        return this.block;
    },
    validation (value: string) {
        return value === '09:00:00' ? 'Invalid time' : '';
    },
})
field: ui.fields.Time<Page>;
```

### Extension Field Example

```ts
@ui.decorators.timeField<Page>({
    ...,
    title: 'Extended Time Field',
    insertBefore () {
        return this.field;
    },
    parent () {
        return this.block;
    },
})
extension: ui.fields.Time<Page>;
```

## Decorator Properties

### Display Properties

- **helperText**: Specifies the helper text displayed above the field. It is automatically picked up by the i18n engine and externalized.
- **isDisabled**: Specifies whether the field is editable or not. The difference between isDisabled and isReadOnly, is that isReadOnly suggests that the field is never editable, whereas isDisabled suggest that the field is currently not editable. It can also be defined as callback function that returns a boolean.
- **isFullWidth**: Specifies whether the field should take the full width.
- **isHelperTextHidden**: Specifies whether the field's helper text should be displayed or not.
- **isHidden**: Specifies whether the component should be displayed or not.
- **isReadOnly**: Specifies whether the field is editable or not. The difference between isDisabled and isReadOnly, is that isReadOnly suggests that the field is never editable, whereas isDisabled suggest that the field is currently not editable. It can also be defined as callback function that returns a boolean.
- **isTitleHidden**: Specifies whether the field's title should be displayed or not.
- **placeholder**: Specifies the field's input placeholder. It is automatically picked up by the i18n engine and externalized.
- **size**: Specifies the field's vertical size. Options are `small`, `medium` and `large`. By default, the size is set to `medium`.
- **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
- **width**: Specifies the field's width. The width can be defined by using field size categories which are remapped to actually width values by the framework depending on the screen size and the container size that the field is in.
- **info**: Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function that returns a string.
- **warning**: Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback function that returns a string.

#### Data Binding Properties

- **bind**: Specifies the GraphQL object's property the field's value is bound ot. If not provided, the field's name is used.

#### Event Handler Properties

- **onChange()**: Triggered when the field's value changed and the focus is about to move away from the field. No arguments are provided.
- **onClick()**: Triggered when the field is clicked. No arguments are provided.

#### Validation Properties

- **isMandatory**: Specifies whether the field is mandatory or not. When enabled, empty values will raise an validation error message. It can also be defined as callback function that returns a boolean.
- **validation()**: Custom validation callback with the new value provided as argument. If the function returns a non-empty string (or promise resolving to a non-empty string), the return value will be used as validation error message. If the function returns a falsy value, the field is considered to be valid.

#### Extension Properties

- **insertBefore()**: Inserts the current field before the returned field, only for extension pages.

### Other Decorator Properties

- **fetchesDefaults**: When set to true and when the time value changes, a request to the server for default values for the whole page will be requested. False by default.

#### Runtime Functions

- **hours()**: Gets/set the hours as an integer between 0 and 23.
- **minutes()**: Gets/set the minutes as an integer between 0 and 59.
- **seconds()**: Gets/set the seconds as an integer between 0 and 59.
- **focus()**: Moves the browser's focus to the field.
- **focus()**: Moves the browser's focus to the field.
- **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
- **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
- **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
- **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
- **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
- **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Time).
