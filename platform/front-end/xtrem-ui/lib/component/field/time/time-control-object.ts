/**
 * @packageDocumentation
 * @module root
 */
import type { ClientNode } from '@sage/xtrem-client';
import { isNil } from 'lodash';
import type { ScreenBase } from '../../../service/screen-base';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { TimeProperties } from './time-types';
import { TIME_REGEX, validateHours, validateMinutes } from '../../ui/time/time-utils';

/**
 * [Field]{@link EditableFieldControlObject} that holds a value from a set of given values or sets a new value to be
 * saved later. The type of GraphQL object must be specified through the 'node' property, while the 'valueField' and
 * 'helperTextField' properties define which properties of the GraphQL object will be displayed in the field and will
 * be matched against the user provided text.
 */
export class TimeControlObject<
    ReferencedItemType extends ClientNode = any,
    CT extends ScreenBase = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.Time, FieldComponentProps<FieldKey.Time>> {
    @ControlObjectProperty<TimeProperties<CT>, TimeControlObject<ReferencedItemType, CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }

    get hours(): number | null {
        const currentValue = this._getValue();
        if (isNil(currentValue)) {
            return null;
        }
        const [hours] = currentValue.split(':');

        return Number(hours);
    }

    set hours(value: number) {
        if (value < 0 || value > 23 || !Number.isInteger(value)) {
            throw new Error('Invalid hours');
        }
        const currentValue = this._getValue();

        const [, minutes, seconds] = (currentValue ?? '00:00:00').split(':');
        this._setValue(`${String(value).padStart(2, '0')}:${minutes}:${seconds}`);
    }

    get minutes(): number | null {
        const currentValue = this._getValue();
        if (isNil(currentValue)) {
            return null;
        }
        const [, minutes] = currentValue.split(':');

        return Number(minutes);
    }

    set minutes(value: number) {
        if (value < 0 || value > 59 || !Number.isInteger(value)) {
            throw new Error('Invalid minutes');
        }
        const currentValue = this._getValue();

        const [hours, , seconds] = (currentValue ?? '00:00:00').split(':');
        this._setValue(`${hours}:${String(value).padStart(2, '0')}:${seconds}`);
    }

    get seconds(): number | null {
        const currentValue = this._getValue();
        if (isNil(currentValue)) {
            return null;
        }
        const [, , seconds] = currentValue.split(':');

        return Number(seconds);
    }

    set seconds(value: number) {
        if (value < 0 || value > 59 || !Number.isInteger(value)) {
            throw new Error('Invalid seconds');
        }
        const currentValue = this._getValue();

        const [hours, minutes] = (currentValue ?? '00:00:00').split(':');
        this._setValue(`${hours}:${minutes}:${String(value).padStart(2, '0')}`);
    }

    get value(): string | null {
        return this._getValue();
    }

    set value(value: string | null) {
        if (isNil(value)) {
            this._setValue(null);
            return;
        }
        const match = value.match(TIME_REGEX);
        if (!match) {
            throw new Error(`Invalid time: ${value}`);
        }
        const hoursValidation = validateHours({ hours: match[1], hasAmPmToggle: false, toggle: 'unknown' });
        const minutesValidation = validateMinutes(match[2]);
        if (!hoursValidation.isValid || !minutesValidation.isValid) {
            throw new Error(`Invalid time: ${value}`);
        }
        this._setValue(value);
    }
}
