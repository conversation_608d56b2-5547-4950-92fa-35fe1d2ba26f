/**
 * @packageDocumentation
 * @module root
 */
import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { TimeControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { TimeDecoratorProperties, TimeExtensionDecoratorProperties } from './time-types';

class TimeDecorator extends AbstractFieldDecorator<FieldKey.Time> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = TimeControlObject;
}

/**
 * Initializes the decorated member as a [Time]{@link TimeControlObject} field with the provided properties.
 *
 * @param properties The properties that the [Time]{@link TimeControlObject} field will be initialized with.
 */
export function timeField<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: TimeDecoratorProperties<Extend<T>, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Time>(properties, TimeDecorator, FieldKey.Time, true);
}

export function timeFieldOverride<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: TimeExtensionDecoratorProperties<T, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Time>(properties);
}
