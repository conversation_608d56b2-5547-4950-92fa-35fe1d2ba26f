import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import * as pageMetaData from '../../../../service/page-metadata';
import { timeField } from '../time-decorator';

describe('Time Decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'timeField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(timeField, pageMetadata, fieldId);
        });
    });
});
