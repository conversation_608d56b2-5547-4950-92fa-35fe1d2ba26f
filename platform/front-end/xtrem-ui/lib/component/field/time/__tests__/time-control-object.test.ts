import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import { TimeControlObject } from '../../../control-objects';
import type { FieldKey } from '../../../types';
import type { TimeProperties } from '../time-types';

describe('Time control object', () => {
    let controlObject: TimeControlObject;
    let timeProperties: TimeProperties<any>;
    let timeValue: string;

    beforeEach(() => {
        timeProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
        };
        timeValue = '09:00:00';
        controlObject = buildControlObject<FieldKey.Time>(TimeControlObject, {
            fieldValue: timeValue,
            fieldProperties: timeProperties,
        });
    });

    it('should be able to get the value', () => {
        expect(controlObject.value).toEqual(timeValue);
    });

    it('should be able to set the value', () => {
        controlObject.value = '10:00:00';
        expect(controlObject.value).toEqual('10:00:00');
    });

    it('should be able to set the value to null', () => {
        controlObject.value = null;
        expect(controlObject.value).toEqual(null);
    });

    it('should be able to set the title', () => {
        const title = 'Test Title';
        expect(timeProperties.title).not.toEqual(title);
        controlObject.title = title;
        expect(timeProperties.title).toEqual(title);
    });

    it('should be able to set the helper text', () => {
        const helperText = 'Test Title';
        expect(timeProperties.title).not.toEqual(helperText);
        controlObject.helperText = helperText;
        expect(timeProperties.helperText).toEqual(helperText);
    });

    it('should be able to get/set the hours', () => {
        expect(controlObject.hours).toEqual(9);
        const hours = 10;
        controlObject.hours = hours;
        expect(controlObject.hours).toEqual(hours);
        expect(controlObject.value).toEqual(`${hours}:00:00`);
    });

    it('should NOT be able to set the hours', () => {
        expect(() => {
            controlObject.hours = 25;
        }).toThrow('Invalid hours');
        expect(() => {
            controlObject.hours = -1;
        }).toThrow('Invalid hours');
        expect(() => {
            controlObject.hours = 20.1;
        }).toThrow('Invalid hours');
    });

    it('should be able to get/set the minutes', () => {
        expect(controlObject.minutes).toEqual(0);
        const minutes = 10;
        controlObject.minutes = minutes;
        expect(controlObject.minutes).toEqual(minutes);
        expect(controlObject.value).toEqual(`09:${minutes}:00`);
    });

    it('should NOT be able to set the minutes', () => {
        expect(() => {
            controlObject.minutes = 60;
        }).toThrow('Invalid minutes');
        expect(() => {
            controlObject.minutes = -1;
        }).toThrow('Invalid minutes');
        expect(() => {
            controlObject.minutes = 20.1;
        }).toThrow('Invalid minutes');
    });

    it('should be able to get/set the seconds', () => {
        expect(controlObject.seconds).toEqual(0);
        const seconds = 10;
        controlObject.seconds = seconds;
        expect(controlObject.seconds).toEqual(seconds);
        expect(controlObject.value).toEqual(`09:00:${seconds}`);
    });

    it('should NOT be able to set the seconds', () => {
        expect(() => {
            controlObject.seconds = 60;
        }).toThrow('Invalid seconds');
        expect(() => {
            controlObject.seconds = -1;
        }).toThrow('Invalid seconds');
        expect(() => {
            controlObject.seconds = 20.1;
        }).toThrow('Invalid seconds');
    });
});
