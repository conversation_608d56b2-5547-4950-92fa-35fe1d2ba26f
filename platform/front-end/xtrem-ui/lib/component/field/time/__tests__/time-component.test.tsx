import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    userEvent,
} from '../../../../__tests__/test-helpers';

import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../../../../redux';
import type { ScreenBase } from '../../../../service/screen-base';
import { FieldKey } from '../../../types';
import ConnectedTimeComponent from '../time-component';
import type { TimeDecoratorProperties } from '../time-types';

jest.useFakeTimers();

describe('Time Component', () => {
    const screenId = 'TestPage';
    const fieldId = 'test-time-field';
    const fieldTitle = 'Test Field Title';

    let fieldProperties: TimeDecoratorProperties<ScreenBase, any>;

    let setValueMock;
    const getConnectedField = (mockStore: any, testFieldId = fieldId) => (
        <Provider store={mockStore}>
            <ConnectedTimeComponent screenId={screenId} elementId={testFieldId} />
        </Provider>
    );

    beforeEach(() => {
        fieldProperties = {
            title: fieldTitle,
        };
        setValueMock = jest
            .spyOn(xtremRedux.actions, 'setFieldValue')
            .mockReturnValue(() => Promise.resolve({ type: 'SetFieldValue' }) as any);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('Connected', () => {
        let state: xtremRedux.XtremAppState;
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        beforeEach(() => {
            state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Time, state, screenId, fieldId, fieldProperties, null);
            mockStore = getMockStore(state);
        });

        it('should render with redux with defaults', async () => {
            const wrapper = render(getConnectedField(mockStore, fieldId));
            expect(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false })).toBeInTheDocument();
            expect(
                (screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement).value,
            ).toBe('');
            expect(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false })).toBeInTheDocument();
            expect(
                (screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement)
                    .value,
            ).toBe('');
            expect(wrapper.container.querySelector(`#e-time-field-${screenId}-${fieldId}-toggle`)).toBeInTheDocument();
            expect(
                wrapper.container
                    .querySelector(`#e-time-field-${screenId}-${fieldId}-toggle [aria-label="AM"]`)
                    ?.getAttribute('aria-pressed'),
            ).toBe('true');
            expect(
                wrapper.container
                    .querySelector(`#e-time-field-${screenId}-${fieldId}-toggle [aria-label="PM"]`)
                    ?.getAttribute('aria-pressed'),
            ).toBe('false');
        });

        it('should render component', () => {
            render(getConnectedField(mockStore, fieldId));
            screen.getByText(fieldTitle);
            screen.getByLabelText(fieldTitle);
        });

        it('should render the title', () => {
            render(getConnectedField(mockStore, fieldId));
            screen.getByLabelText(fieldTitle);
        });

        it('should render the title with an asterisk when the field is mandatory', () => {
            fieldProperties.isMandatory = true;
            render(getConnectedField(mockStore, fieldId));
            screen.getByLabelText(`${fieldTitle} *`);
        });

        // JSDOM does not support related target on focus events so we can't test this
        it.skip('should trigger onChange on blur when hours and minutes are set', async () => {
            render(getConnectedField(mockStore, fieldId));
            fireEvent.change(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }), {
                target: { value: '1' },
            });
            await waitFor(() => {
                expect(
                    (
                        screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement
                    ).getAttribute('value'),
                ).toBe('1');
            });
            expect(setValueMock).not.toHaveBeenCalled();
            fireEvent.blur(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }));
            await waitFor(() => {
                expect(
                    (
                        screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement
                    ).getAttribute('value'),
                ).toBe('01');
            });
            expect(setValueMock).not.toHaveBeenCalled();
            fireEvent.change(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }), {
                target: { value: '1' },
            });
            await waitFor(() => {
                expect(
                    (
                        screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement
                    ).getAttribute('value'),
                ).toBe('1');
            });
            expect(setValueMock).not.toHaveBeenCalled();
            fireEvent.blur(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }));
            await waitFor(() => {
                expect(
                    (
                        screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement
                    ).getAttribute('value'),
                ).toBe('01');
            });
            await waitFor(() => {
                expect(setValueMock).toHaveBeenCalledTimes(1);
                expect(setValueMock).toHaveBeenLastCalledWith(screenId, fieldId, '01:01:00', true);
            });
        });

        // TODO: uncomment once https://github.com/testing-library/user-event/issues/1066 is fixed
        // it('should be able to set value with arrow up/down', async () => {
        //     render(getConnectedField(mockStore, fieldId));
        //     fireEvent.keyDown(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }), {
        //         key: 'ArrowUp',
        //     });
        //     await waitFor(() => {
        //         expect(
        //             (
        //                 screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement
        //             ).getAttribute('value'),
        //         ).toBe('1');
        //     });
        //     fireEvent.keyDown(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }), {
        //         key: 'ArrowUp',
        //     });
        //     await waitFor(() => {
        //         expect(
        //             (
        //                 screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement
        //             ).getAttribute('value'),
        //         ).toBe('2');
        //     });
        //     fireEvent.keyDown(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }), {
        //         key: 'ArrowDown',
        //     });
        //     await waitFor(() => {
        //         expect(
        //             (
        //                 screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement
        //             ).getAttribute('value'),
        //         ).toBe('1');
        //     });
        //     fireEvent.blur(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }));
        //     await waitFor(() => {
        //         expect(
        //             (
        //                 screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement
        //             ).getAttribute('value'),
        //         ).toBe('01');
        //     });
        //     fireEvent.keyDown(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }), {
        //         key: 'ArrowUp',
        //     });
        //     await waitFor(() => {
        //         expect(
        //             (
        //                 screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement
        //             ).getAttribute('value'),
        //         ).toBe('1');
        //     });
        //     fireEvent.keyDown(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }), {
        //         key: 'ArrowUp',
        //     });
        //     await waitFor(() => {
        //         expect(
        //             (
        //                 screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement
        //             ).getAttribute('value'),
        //         ).toBe('2');
        //     });
        //     fireEvent.keyDown(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }), {
        //         key: 'ArrowDown',
        //     });
        //     await waitFor(() => {
        //         expect(
        //             (
        //                 screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement
        //             ).getAttribute('value'),
        //         ).toBe('1');
        //     });
        //     fireEvent.blur(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }));
        //     await waitFor(() => {
        //         expect(
        //             (
        //                 screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement
        //             ).getAttribute('value'),
        //         ).toBe('01');
        //     });
        //     await waitFor(() => {
        //         expect(setValueMock).toHaveBeenCalledTimes(1);
        //         expect(setValueMock).toHaveBeenLastCalledWith(screenId, fieldId, '01:01:00', true);
        //     });
        // });

        it('should trigger onChange when am/pm toggle is clicked', async () => {
            const wrapper = render(getConnectedField(mockStore, fieldId));
            fireEvent.change(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }), {
                target: { value: '0' },
            });
            await userEvent.click(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }));
            fireEvent.change(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }), {
                target: { value: '0' },
            });
            await userEvent.click(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }));
            await waitFor(() => {
                expect(setValueMock).toHaveBeenCalledTimes(1);
                expect(setValueMock).toHaveBeenLastCalledWith(screenId, fieldId, '00:00:00', true);
            });
            fireEvent.click(
                wrapper.container.querySelector(`#e-time-field-${screenId}-${fieldId}-toggle [aria-label="PM"]`)!,
            );
            await waitFor(() => {
                expect(setValueMock).toHaveBeenCalledTimes(2);
                expect(setValueMock).toHaveBeenLastCalledWith(screenId, fieldId, '12:00:00', true);
            });
            fireEvent.click(
                wrapper.container.querySelector(`#e-time-field-${screenId}-${fieldId}-toggle [aria-label="AM"]`)!,
            );
            await waitFor(() => {
                expect(setValueMock).toHaveBeenCalledTimes(3);
                expect(setValueMock).toHaveBeenLastCalledWith(screenId, fieldId, '00:00:00', true);
            });
        });

        it('should show validation errors', async () => {
            state.screenDefinitions[screenId].errors[fieldId] = [
                {
                    elementId: fieldId,
                    screenId,
                    message: 'Validation error',
                    validationRule: 'validation',
                },
            ];
            const wrapper = render(getConnectedField(mockStore, fieldId));

            await waitFor(() => {
                expect(wrapper.baseElement.querySelector('[data-element="error"]')!).toBeInTheDocument();
            });
            fireEvent.mouseEnter(wrapper.baseElement.querySelector('[data-element="error"]')!);
            await waitFor(() => {
                expect(wrapper.baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                    'Validation error',
                );
            });
        });

        it('should render without toggle when locale is not en-US', () => {
            state.applicationContext!.locale = 'fr-FR';
            const wrapper = render(getConnectedField(mockStore, fieldId));
            expect(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false })).toBeInTheDocument();
            expect(
                (screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement).value,
            ).toBe('');
            expect(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false })).toBeInTheDocument();
            expect(
                (screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement)
                    .value,
            ).toBe('');
            expect(
                wrapper.container.querySelector(`#e-time-field-${screenId}-${fieldId}-toggle`),
            ).not.toBeInTheDocument();
        });

        it('should not allow non numeric characters - hours input', async () => {
            render(getConnectedField(mockStore, fieldId));
            await userEvent.type(
                screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }),
                '1{Backspace}abc2{Tab}',
            );
            await waitFor(() => {
                expect(
                    (
                        screen.getByTestId('e-field-bind-test-time-field-hours', {
                            exact: false,
                        }) as HTMLInputElement
                    ).getAttribute('value'),
                ).toBe('02');
                expect(setValueMock).not.toHaveBeenCalled();
            });
        });

        it('should not allow non numeric characters - minutes input', async () => {
            render(getConnectedField(mockStore, fieldId));
            await userEvent.type(
                screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }),
                '1{Backspace}abc2{Tab}',
            );
            await waitFor(() => {
                expect(
                    (
                        screen.getByTestId('e-field-bind-test-time-field-minutes', {
                            exact: false,
                        }) as HTMLInputElement
                    ).getAttribute('value'),
                ).toBe('02');
                expect(setValueMock).toHaveBeenCalledTimes(1);
                expect(setValueMock).toHaveBeenLastCalledWith(screenId, fieldId, '00:02:00', true);
            });
        });

        it('should not allow more than 2 digits - hours input', async () => {
            render(getConnectedField(mockStore, fieldId));
            await userEvent.type(
                screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }),
                '{Backspace}111{Tab}',
            );
            await waitFor(() => {
                expect(
                    (
                        screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement
                    ).getAttribute('value'),
                ).toBe('11');
                expect(setValueMock).not.toHaveBeenCalled();
            });
        });

        it('should not allow more than 2 digits - minutes input', async () => {
            render(getConnectedField(mockStore, fieldId));
            await userEvent.type(
                screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }),
                '{Backspace}111{Tab}',
            );
            await waitFor(() => {
                expect(
                    (
                        screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement
                    ).getAttribute('value'),
                ).toBe('11');
                expect(setValueMock).toHaveBeenCalledTimes(1);
                expect(setValueMock).toHaveBeenLastCalledWith(screenId, fieldId, '00:11:00', true);
            });
        });

        it('should not allow 24h format when using en-US', async () => {
            render(getConnectedField(mockStore, fieldId));
            await userEvent.type(
                screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }),
                '{Backspace}22{Tab}',
            );
            await waitFor(() => {
                expect(
                    (
                        screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement
                    ).getAttribute('value'),
                ).toBe('02');
                expect(setValueMock).not.toHaveBeenCalled();
            });
        });

        it('should set the value to null when both input are empty and field is blurred - en-US', async () => {
            state.screenDefinitions[screenId].values[fieldId] = '00:00:00';
            const wrapper = render(getConnectedField(mockStore, fieldId));
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('12');
            });
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('00');
            });
            fireEvent.change(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }), {
                target: { value: '' },
            });
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('');
            });
            await userEvent.click(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }));

            expect(setValueMock).toHaveBeenCalledTimes(0);
            fireEvent.change(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }), {
                target: { value: '' },
            });
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('');
            });
            await userEvent.click(wrapper.baseElement.closest('body')!);
            await waitFor(() => {
                expect(setValueMock).toHaveBeenCalledTimes(1);
                expect(setValueMock).toHaveBeenLastCalledWith(screenId, fieldId, null, true);
            });
        });

        it('should set input value to 00 when field is blurred - hours', async () => {
            state.screenDefinitions[screenId].values[fieldId] = '01:00:00';
            const wrapper = render(getConnectedField(mockStore, fieldId));
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('01');
            });
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('00');
            });
            fireEvent.change(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }), {
                target: { value: '' },
            });
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('');
            });
            await userEvent.click(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }));

            expect(setValueMock).toHaveBeenCalledTimes(0);

            await userEvent.click(wrapper.baseElement.closest('body')!);
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('12');
            });
            await waitFor(() => {
                expect(setValueMock).toHaveBeenCalledTimes(1);
                expect(setValueMock).toHaveBeenLastCalledWith(screenId, fieldId, '00:00:00', true);
            });
        });

        it('should set input value to 00 when field is blurred - minutes', async () => {
            state.screenDefinitions[screenId].values[fieldId] = '01:01:00';
            const wrapper = render(getConnectedField(mockStore, fieldId));
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('01');
            });
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('01');
            });
            fireEvent.change(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }), {
                target: { value: '' },
            });
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('');
            });
            await userEvent.click(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }));

            expect(setValueMock).toHaveBeenCalledTimes(0);

            await userEvent.click(wrapper.baseElement.closest('body')!);
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('00');
            });
            await waitFor(() => {
                expect(setValueMock).toHaveBeenCalledTimes(1);
                expect(setValueMock).toHaveBeenLastCalledWith(screenId, fieldId, '01:00:00', true);
            });
        });

        it('should set the value to null when both input are empty and field is blurred - fr-FR', async () => {
            state.screenDefinitions[screenId].values[fieldId] = '00:00:00';
            state.applicationContext!.locale = 'fr-FR';
            const wrapper = render(getConnectedField(mockStore, fieldId));
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('00');
            });
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('00');
            });
            fireEvent.change(screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }), {
                target: { value: '' },
            });
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('');
            });
            await userEvent.click(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }));

            expect(setValueMock).toHaveBeenCalledTimes(0);

            fireEvent.change(screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }), {
                target: { value: '' },
            });
            await waitFor(() => {
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-hours', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('');
            });
            await userEvent.click(wrapper.baseElement.closest('body')!);

            await waitFor(() => {
                expect(setValueMock).toHaveBeenCalledTimes(1);
                expect(
                    (screen.getByTestId('e-field-bind-test-time-field-minutes', { exact: false }) as HTMLInputElement)
                        .value,
                ).toBe('');
                expect(setValueMock).toHaveBeenLastCalledWith(screenId, fieldId, null, true);
            });
        });

        describe('info and warning', () => {
            it('should render with an info message', async () => {
                fieldProperties.infoMessage = 'Info message!!';
                addFieldToState(FieldKey.Time, state, screenId, fieldId, fieldProperties, null);
                mockStore = getMockStore(state);
                const { baseElement } = render(getConnectedField(mockStore));
                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an info message from a callback', async () => {
                fieldProperties.infoMessage = () => 'Info message!!';
                addFieldToState(FieldKey.Time, state, screenId, fieldId, fieldProperties, null);
                mockStore = getMockStore(state);
                const { baseElement } = render(getConnectedField(mockStore));
                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an warning message', async () => {
                fieldProperties.warningMessage = 'Warning message!!';
                addFieldToState(FieldKey.Time, state, screenId, fieldId, fieldProperties, null);
                mockStore = getMockStore(state);
                const { baseElement } = render(getConnectedField(mockStore));
                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with an warning message from a callback', async () => {
                fieldProperties.warningMessage = () => 'Warning message!!';
                addFieldToState(FieldKey.Time, state, screenId, fieldId, fieldProperties, null);
                mockStore = getMockStore(state);
                const { baseElement } = render(getConnectedField(mockStore, fieldId));
                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should prioritize warnings over info messages', () => {
                fieldProperties.warningMessage = () => 'Warning message!!';
                fieldProperties.infoMessage = () => 'Info message!!';
                addFieldToState(FieldKey.Time, state, screenId, fieldId, fieldProperties, null);
                mockStore = getMockStore(state);
                const wrapper = render(getConnectedField(mockStore, fieldId));
                expect(wrapper.baseElement.querySelector('[data-element="info"]')).toBeNull();
                expect(wrapper.baseElement.querySelector('[data-element="warning"]')).not.toBeNull();
            });

            it('should prioritize validation errors over warnings and info messages', () => {
                fieldProperties.warningMessage = () => 'Warning message!!';
                fieldProperties.infoMessage = () => 'Info message!!';
                state.screenDefinitions[screenId].errors[fieldId] = [
                    {
                        elementId: fieldId,
                        screenId,
                        validationRule: 'isMandatory',
                        message: 'Error',
                    },
                ];
                addFieldToState(FieldKey.Time, state, screenId, fieldId, fieldProperties, null);
                mockStore = getMockStore(state);
                const wrapper = render(getConnectedField(mockStore, fieldId));
                expect(wrapper.baseElement.querySelector('[data-element="info"]')).toBeNull();
                expect(wrapper.baseElement.querySelector('[data-element="warning"]')).toBeNull();
                expect(wrapper.baseElement.querySelector('[data-element="error"]')).not.toBeNull();
            });
        });
    });
});
