import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import { TextControlObject } from '../../../control-objects';
import type { FieldKey } from '../../../types';
import type { TextProperties } from '../text-types';

describe('Text control object', () => {
    let textControlObject: TextControlObject;
    let textProperties: TextProperties;
    let textValue: string;

    beforeEach(() => {
        textProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
        };
        textValue = 'TEST_VALUE';
        textControlObject = buildControlObject<FieldKey.Text>(TextControlObject, {
            fieldValue: textValue,
            fieldProperties: textProperties,
        });
    });

    it('getting field value', () => {
        expect(textControlObject.value).toEqual(textValue);
    });

    it('should set the title', () => {
        const newValue = 'Test Numeric Field Title';
        expect(textProperties.title).not.toEqual(newValue);
        textControlObject.title = newValue;
        expect(textProperties.title).toEqual(newValue);
    });

    it('should set the value prefix', () => {
        const newValue = '$$';
        expect(textProperties.prefix).not.toEqual(newValue);
        textControlObject.prefix = newValue;
        expect(textProperties.prefix).toEqual(newValue);
    });

    it('should set the value postfix', () => {
        const newValue = '££';
        expect(textProperties.postfix).not.toEqual(newValue);
        textControlObject.postfix = newValue;
        expect(textProperties.postfix).toEqual(newValue);
    });

    it('should set the min length', () => {
        const newValue = 2;
        expect(textProperties.minLength).not.toEqual(newValue);
        textControlObject.minLength = newValue;
        expect(textProperties.minLength).toEqual(newValue);
    });

    it('should set the max length', () => {
        const newValue = 32;
        expect(textProperties.maxLength).not.toEqual(newValue);
        textControlObject.maxLength = newValue;
        expect(textProperties.maxLength).toEqual(newValue);
    });
});
