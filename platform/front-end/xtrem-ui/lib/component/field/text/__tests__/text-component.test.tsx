import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    applyActionMocks,
    renderWithRedux,
} from '../../../../__tests__/test-helpers';

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../../../../redux';
import type { TextProperties } from '../../../control-objects';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';
import { ConnectedTextComponent, TextComponent } from '../text-component';
import { fireEvent, render, waitFor, cleanup } from '@testing-library/react';

describe('Text component', () => {
    const screenId = 'TestPage';
    const fieldId = 'test-text-field';
    let mockFieldProperties: TextProperties;

    beforeEach(() => {
        jest.spyOn(xtremRedux.actions, 'setFieldValue').mockReturnValue(
            () => Promise.resolve({ type: 'SetFieldValue' }) as any,
        );
        mockFieldProperties = {
            title: 'Test Field Title',
        };
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
        cleanup();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Text, state, screenId, fieldId, mockFieldProperties, 'Test Value');
            addFieldToState(FieldKey.Text, state, screenId, 'test-empty-text-field', mockFieldProperties, null);
            mockStore = getMockStore(state);
        });

        describe('integration', () => {
            const setup = (
                textProps: TextProperties = mockFieldProperties,
                value: FieldInternalValue<FieldKey.Text> = '',
            ) => {
                const initialState = {
                    screenDefinitions: {
                        [screenId]: {
                            metadata: {
                                uiComponentProperties: {
                                    [fieldId]: textProps,
                                },
                            },
                            ...(value && {
                                values: {
                                    [fieldId]: value,
                                },
                            }),
                        },
                    },
                };

                const utils = renderWithRedux(<ConnectedTextComponent screenId={screenId} elementId={fieldId} />, {
                    mockStore,
                    initialState,
                    fieldType: FieldKey.Text,
                    fieldValue: value,
                    fieldProperties: textProps,
                    elementId: fieldId,
                    screenId,
                });

                const text = utils.getByTestId(
                    'e-text-field e-field-label-testFieldTitle e-field-bind-test-text-field',
                ) as HTMLDivElement;

                const input = text.querySelector('input[data-testid="e-text-field-input"]');
                const getIcon = (iconType: string) =>
                    text.querySelector(`span[data-component="icon"][data-element="${iconType}"]`);

                return {
                    ...utils,
                    getIcon,
                    input,
                    text,
                };
            };

            it('can render with redux with defaults', () => {
                const { text, input } = setup();
                expect(text).toHaveTextContent('Test Field Title');
                expect(input).toBeInTheDocument();
            });

            it('can render with icon', () => {
                const { text, input, getIcon } = setup({ ...mockFieldProperties, icon: 'scan' });
                expect(text).toHaveTextContent('Test Field Title');
                expect(input).toBeInTheDocument();
                expect(getIcon('scan')).toBeInTheDocument();
            });
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render disabled', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render disabled 2', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container.querySelector('input')).toBeDisabled();
            });

            it('should render disabled if parent is disabled', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} isParentDisabled={true} />
                    </Provider>,
                );

                expect(container.querySelector('input')).toBeDisabled();
            });

            it('should render in read-only mode', () => {
                mockFieldProperties.isReadOnly = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with an info message', async () => {
                mockFieldProperties.infoMessage = 'Info message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an info message from a callback', async () => {
                mockFieldProperties.infoMessage = () => 'Info message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an warning message', async () => {
                mockFieldProperties.warningMessage = 'Warning message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with an warning message from a callback', async () => {
                mockFieldProperties.warningMessage = () => 'Warning message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with a prefix', () => {
                mockFieldProperties.prefix = 'tel:';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with a postfix', () => {
                mockFieldProperties.postfix = 'x';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render in read-only mode with a postfix and a prefix', () => {
                mockFieldProperties.isReadOnly = true;
                mockFieldProperties.postfix = 'x';
                mockFieldProperties.prefix = 'tel:';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should with full-width', () => {
                mockFieldProperties.isFullWidth = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render empty when no value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId="test-empty-text-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with various field sizes', () => {
                mockFieldProperties.size = 'small';
                let wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'medium';
                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'large';
                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();
            });

            it('should render read-only using a conditional declaration', () => {
                mockFieldProperties.isReadOnly = () => {
                    return true;
                };

                let wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(wrapper.container.querySelector('input[readonly]')).not.toBeNull();

                mockFieldProperties.isReadOnly = () => {
                    return false;
                };

                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(wrapper.container.querySelector('input[readonly]')).toBeNull();
            });
        });

        describe('Interactions', () => {
            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container.querySelector('[data-element="help"]')).not.toBeNull();
            });

            it('Should not render helperText', () => {
                mockFieldProperties.helperText = '';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container.querySelector('[data-element="help"]')).toBeNull();
            });

            it('should update parent on value change when it looses focus', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                const input = container.querySelector('input')!;
                fireEvent.change(input, { target: { value: 'A new input value' } });
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                fireEvent.blur(input);

                expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledTimes(1);
                expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(
                    screenId,
                    fieldId,
                    'A new input value',
                    true,
                );
            });

            it('should consolidate value to store on Enter keydown', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                const input = container.querySelector('input')!;
                fireEvent.change(input, { target: { value: 'A new input value' } });
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                fireEvent.keyDown(input, { key: 'Enter' });
                expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledTimes(1);
                expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(
                    screenId,
                    fieldId,
                    'A new input value',
                    true,
                );
            });
        });
    });

    describe('unconnected', () => {
        describe('Snapshots', () => {
            it('should render just fine', () => {
                const { container } = render(
                    <TextComponent
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        value="testValue"
                        locale="en-US"
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                    />,
                );

                expect(container).toMatchSnapshot();
            });
        });

        describe('Interactions', () => {
            it('should update the value on external update', () => {
                const { container, rerender } = render(
                    <TextComponent
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        value="testValue"
                        locale="en-US"
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                    />,
                );

                expect(container.querySelector('input')).toHaveValue('testValue');

                rerender(
                    <TextComponent
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        value="testValue"
                        locale="en-US"
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                    />,
                );

                expect(container.querySelector('input')).toHaveValue('testValue');
                rerender(
                    <TextComponent
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        value="newTestValue"
                        locale="en-US"
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                    />,
                );

                expect(container.querySelector('input')).toHaveValue('newTestValue');
            });

            it('should reset to original value on Escape keydown', () => {
                const { container } = render(
                    <TextComponent
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        value="testValue"
                        locale="en-US"
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                    />,
                );

                const input = container.querySelector('input')!;
                fireEvent.change(input, { target: { value: 'testValu' } });
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                expect(input).toHaveValue('testValu');
                fireEvent.keyDown(input, { key: 'Escape' });
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                expect(input).toHaveValue('testValue');
            });
        });
    });

    describe('password field', () => {
        it('renders a password input', () => {
            const { container } = render(
                <TextComponent
                    elementId={fieldId}
                    fieldProperties={{ ...mockFieldProperties, isPassword: true }}
                    value="testValue"
                    locale="en-US"
                    setFieldValue={jest.fn().mockResolvedValue(undefined)}
                    validate={jest.fn().mockResolvedValue(undefined)}
                    removeNonNestedErrors={jest.fn()}
                    screenId={screenId}
                    onFocus={jest.fn()}
                />,
            );

            expect(container.querySelector('input')).toHaveAttribute('type', 'password');
        });

        it('current text selected on focus when field non dirty', () => {
            const { container } = render(
                <TextComponent
                    elementId={fieldId}
                    fieldProperties={{ ...mockFieldProperties, isPassword: true }}
                    value="testValue"
                    locale="en-US"
                    setFieldValue={jest.fn().mockResolvedValue(undefined)}
                    validate={jest.fn().mockResolvedValue(undefined)}
                    removeNonNestedErrors={jest.fn()}
                    screenId={screenId}
                    onFocus={jest.fn()}
                />,
            );

            const input = container.querySelector('input')!;
            fireEvent.focus(input);
            expect(input.selectionStart).toEqual(0);
            expect(input.selectionEnd).toEqual(9);
        });

        it('current text selected on click when field non dirty', () => {
            const { container } = render(
                <TextComponent
                    elementId={fieldId}
                    fieldProperties={{ ...mockFieldProperties, isPassword: true }}
                    value="testValue"
                    locale="en-US"
                    setFieldValue={jest.fn().mockResolvedValue(undefined)}
                    validate={jest.fn().mockResolvedValue(undefined)}
                    removeNonNestedErrors={jest.fn()}
                    screenId={screenId}
                    onFocus={jest.fn()}
                />,
            );
            const input = container.querySelector('input')!;
            fireEvent.focus(input);
            expect(input.selectionStart).toEqual(0);
            expect(input.selectionEnd).toEqual(9);
        });

        it('current text selected on pressing arrow key when field non dirty', () => {
            const { container } = render(
                <TextComponent
                    elementId={fieldId}
                    fieldProperties={{ ...mockFieldProperties, isPassword: true }}
                    value="testValue"
                    locale="en-US"
                    setFieldValue={jest.fn().mockResolvedValue(undefined)}
                    validate={jest.fn().mockResolvedValue(undefined)}
                    removeNonNestedErrors={jest.fn()}
                    screenId={screenId}
                    onFocus={jest.fn()}
                />,
            );

            const input = container.querySelector('input')!;
            fireEvent.focus(input);
            fireEvent.keyPress(input, { key: 'Arrow' });
            expect(input.selectionStart).toEqual(0);
            expect(input.selectionEnd).toEqual(9);
        });

        it('current text unselected on focus when field dirty', () => {
            const { container } = render(
                <TextComponent
                    elementId={fieldId}
                    fieldProperties={{ ...mockFieldProperties, isPassword: true }}
                    value="testValue"
                    locale="en-US"
                    setFieldValue={jest.fn().mockResolvedValue(undefined)}
                    validate={jest.fn().mockResolvedValue(undefined)}
                    removeNonNestedErrors={jest.fn()}
                    screenId={screenId}
                    onFocus={jest.fn()}
                />,
            );
            const input = container.querySelector('input')!;
            fireEvent.change(input, { target: { value: 'testValu' } });
            fireEvent.focus(input);
            expect(input.selectionStart).toEqual(8);
            expect(input.selectionEnd).toEqual(8);
        });

        it('current text unselected on pressing arrow key when field dirty', () => {
            const { container } = render(
                <TextComponent
                    elementId={fieldId}
                    fieldProperties={{ ...mockFieldProperties, isPassword: true }}
                    locale="en-US"
                    value="testValue"
                    setFieldValue={jest.fn().mockResolvedValue(undefined)}
                    validate={jest.fn().mockResolvedValue(undefined)}
                    removeNonNestedErrors={jest.fn()}
                    screenId={screenId}
                    onFocus={jest.fn()}
                />,
            );
            const input = container.querySelector('input')!;

            fireEvent.focus(input);
            fireEvent.change(input, { target: { value: 'testValu' } });
            fireEvent.keyPress(input, { key: 'Arrow' });

            expect(input.selectionStart).toEqual(8);
            expect(input.selectionEnd).toEqual(8);
        });
    });
});
