import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import { addDisabledToProperties, addMaxLengthToProperties } from '../../../utils/data-type-utils';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { TextControlObject } from '../../control-objects';
import { <PERSON><PERSON>ey } from '../../types';
import type { TextDecoratorProperties } from './text-types';

class TextDecorator extends AbstractFieldDecorator<FieldKey.Text> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = TextControlObject;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<TextDecoratorProperties> {
        const properties: Partial<TextDecoratorProperties> = {};
        addMaxLengthToProperties({ dataType, propertyDetails, properties });
        addDisabledToProperties({
            propertyDetails,
            dataType,
            properties,
        });
        return properties;
    }
}

/**
 * Initializes the decorated member as a [Text]{@link TextControlObject} field with the provided properties
 *
 * @param properties The properties that the [Text]{@link TextControlObject} field will be initialized with
 */
export function textField<T extends ScreenExtension<T>>(
    properties: TextDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Text>(properties, TextDecorator, FieldKey.Text);
}

export function textFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<TextDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Text>(properties);
}
