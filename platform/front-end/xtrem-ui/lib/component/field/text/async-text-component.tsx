import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { TextComponentProps } from './text-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';

const ConnectedTextComponent = React.lazy(() => import('./text-component'));

export function AsyncConnectedTextComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedTextComponent {...props} />
        </React.Suspense>
    );
}

const TextComponent = React.lazy(() => import('./text-component').then(c => ({ default: c.TextComponent })));

export function AsyncTextComponent(props: TextComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <TextComponent {...props} />
        </React.Suspense>
    );
}
