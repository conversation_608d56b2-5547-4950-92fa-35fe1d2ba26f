import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValueOrCallbackWitRecordValue } from '../../../utils/types';
import type { BlockControlObject, TileControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedPropertiesWrapper } from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseErrorableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasIcon,
    HasInputValueChangeListener,
    HasMaxMinLength,
    HasParent,
    HasPlaceholder,
    Nested,
    NestedChangeable,
    NestedClickable,
    NestedGroupAggregations,
    NestedValidatable,
    Postfixable,
    Prefixable,
    Sizable,
    Validatable,
} from '../traits';

export interface TextProperties<CT extends ScreenBase = ScreenBase, ContextNodeType = void>
    extends EditableFieldProperties<CT>,
        HasIcon,
        HasPlaceholder,
        Postfixable<CT, ContextNodeType>,
        Prefixable<CT, ContextNodeType>,
        HasMaxMinLength<CT>,
        Sizable {
    /** Set text field to be password */
    isPassword?: boolean;
}
export interface TextDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<TextProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        Changeable<CT>,
        HasParent<CT, BlockControlObject<CT> | TileControlObject<CT>>,
        HasInputValueChangeListener<CT>,
        Sizable,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        Validatable<CT, string> {
    getAutocompleteText?: (this: CT, inputValue: string) => Promise<string | null> | string | null;
}

export interface NestedTextProperties<CT extends ScreenBase = ScreenBase, ContextNodeType extends ClientNode = any>
    extends NestedPropertiesWrapper<TextProperties<CT, ContextNodeType>>,
        NestedChangeable<CT>,
        NestedClickable<CT, ContextNodeType>,
        NestedGroupAggregations<'min' | 'max' | 'distinctCount'>,
        Nested<ContextNodeType>,
        Sizable,
        NestedValidatable<CT, string, ContextNodeType> {
    isTableReadOnly?: boolean;
    /** The maximum length of the text field value */
    maxLength?: ValueOrCallbackWitRecordValue<CT, number, ContextNodeType>;
    /** The minimum length of the text field value */
    minLength?: ValueOrCallbackWitRecordValue<CT, number, ContextNodeType>;

    getAutocompleteText?: (
        this: CT,
        inputValue: string,
        rowValue: ContextNodeType,
    ) => Promise<string | null> | string | null;
}

export type TextComponentProps = BaseErrorableComponentProperties<
    TextProperties,
    string,
    NestedFieldsAdditionalProperties
>;
