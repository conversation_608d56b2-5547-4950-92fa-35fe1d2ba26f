import Textbox from 'carbon-react/esm/components/textbox';
import { debounce } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { getCommonCarbonComponentProperties } from '../carbon-helpers';
import { CarbonWrapper } from '../carbon-wrapper';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { BaseEditableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { TextDecoratorProperties } from './text-types';
import { resolveByValue } from '../../../utils/resolve-value-utils';

type TextComponentProps = BaseEditableComponentProperties<
    TextDecoratorProperties,
    string,
    NestedFieldsAdditionalProperties & { isDirty?: boolean }
>;

export interface TextComponentState {
    value: string;
    isDirty: boolean;
    autocompleteText: string;
    autocompletePosition?: number | null;
}

export class TextComponent extends EditableFieldBaseComponent<
    TextDecoratorProperties,
    string,
    NestedFieldsAdditionalProperties,
    TextComponentState
> {
    constructor(props: TextComponentProps) {
        super(props);
        this.state = {
            value: this.props.value ? String(this.props.value) : '',
            isDirty: false,
            autocompleteText: '',
        };
    }

    private readonly onInputValueChanged = debounce(async (searchText: string) => {
        await triggerFieldEvent(this.props.screenId, this.props.elementId, 'onInputValueChange', searchText);
        if (this.props.fieldProperties.getAutocompleteText) {
            this.setState({ autocompleteText: '', autocompletePosition: this.getValueWidth(searchText) });
            const result = await resolveByValue<string | null>({
                propertyValue: this.props.fieldProperties.getAutocompleteText,
                rowValue: this.props.recordContext,
                fieldValue: searchText,
                screenId: this.props.screenId,
                skipHexFormat: true,
            });
            this.setState({ autocompleteText: result || '' });
        }
    }, 150);

    private readonly onChange = async (event: React.ChangeEvent<HTMLInputElement>): Promise<void> => {
        this.setState({ isDirty: true, value: event.target.value });
        await this.onInputValueChanged(event.target.value);
    };

    private readonly onBlur = (): void => {
        const carbonProps = getCommonCarbonComponentProperties(this.props);
        carbonProps.onBlur();
        const newValue = this.state.value || null;
        const propsValue = this.props.value || null;
        if (propsValue !== newValue) {
            handleChange(
                this.props.elementId,
                newValue,
                this.props.setFieldValue,
                this.props.validate,
                this.triggerChangeListener,
            );
        }
    };

    private readonly onKeyDown = (event: KeyboardEvent): void => {
        if (event.key === 'Tab' && this.state.autocompleteText) {
            event.preventDefault();
            this.setState({ value: this.state.value + this.state.autocompleteText, autocompleteText: '' });
        }

        if (event.key === 'Enter') {
            this.onBlur();
        }
        if (event.key === 'Escape') {
            this.setState({ value: this.props.value ? String(this.props.value) : '' });
        }
    };

    private readonly onKeyUp = (event: KeyboardEvent): void => {
        this.handleSelection(event);
    };

    private readonly onClick = (event: KeyboardEvent): void => {
        this.getClickHandler()();
        this.handleSelection(event);
    };

    UNSAFE_componentWillReceiveProps(nextProps: TextComponentProps): void {
        const value = nextProps.value;
        if (this.state.value !== value) {
            this.setState({ isDirty: false, value: value ? String(value) : '', autocompleteText: '' });
        }
    }

    private readonly handleFocus = (event: any): void => {
        this.handleSelection(event);
        if (!this.props.isInFocus) {
            const carbonProps = getCommonCarbonComponentProperties(this.props);
            carbonProps.onFocus();
        }
    };

    private readonly handleSelection = (event: any): void => {
        if (!this.state.isDirty && this.props.fieldProperties.isPassword) {
            event.target.setSelectionRange(0, 0);
            event.target.focus();
            event.target.select();
        }
    };

    private readonly getValueWidth = (value: string): number | null => {
        const inputElement = this.componentRef.current?.querySelector('input');
        if (!inputElement || !value) {
            return null;
        }

        const fontProperties = ['font-style', 'font-variant', 'font-weight', 'font-size', 'font-family'];
        let fontCssRule = '';
        fontProperties.forEach(propertyName => {
            fontCssRule += `${window.getComputedStyle(inputElement, null).getPropertyValue(propertyName)} `;
        });

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            return null;
        }
        ctx.font = fontCssRule;
        return ctx.measureText(value).width + 13;
    };

    render(): React.ReactNode {
        const commonProps = getCommonCarbonComponentProperties(this.props);

        return (
            <CarbonWrapper
                {...this.props}
                className="e-text-field"
                componentName="text"
                componentRef={this.componentRef}
                handlersArguments={this.props.handlersArguments}
                noReadOnlySupport={true}
                value={this.state.value}
            >
                <Textbox
                    {...commonProps}
                    data-testid="e-text-field-input"
                    inputIcon={this.props.fieldProperties.icon}
                    onBlur={this.onBlur}
                    onChange={this.onChange}
                    onClick={this.onClick}
                    onKeyDown={this.onKeyDown}
                    onKeyUp={this.onKeyUp}
                    value={this.state.value || ''}
                    type={this.props.fieldProperties.isPassword ? 'password' : undefined}
                    onFocus={this.handleFocus}
                >
                    <>
                        {this.state.autocompleteText && this.state.autocompletePosition && this.props.isInFocus && (
                            <span
                                style={{ left: this.state.autocompletePosition }}
                                className="e-text-field-autocomplete-value"
                                data-testid="e-text-field-autocomplete-value"
                            >
                                {this.state.autocompleteText}
                            </span>
                        )}
                        {commonProps.children}
                    </>
                </Textbox>
            </CarbonWrapper>
        );
    }
}

export const ConnectedTextComponent = connect(mapStateToProps(), mapDispatchToProps())(TextComponent);

export default ConnectedTextComponent;
