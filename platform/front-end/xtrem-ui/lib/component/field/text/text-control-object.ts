/**
 * @packageDocumentation
 * @module root
 * */
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { TextProperties } from './text-types';

/**
 * [Field]{@link EditableFieldControlObject} that holds a single-line text value
 */
export class TextControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.Text,
    FieldComponentProps<FieldKey.Text>
> {
    @ControlObjectProperty<TextProperties<CT>, TextControlObject<CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<TextProperties<CT>, TextControlObject<CT>>()
    /** Icon of the input field. It will be placed on the right side. */
    icon?: IconType;

    @ControlObjectProperty<TextProperties<CT>, TextControlObject<CT>>()
    /** Icon of the input field. It will be placed on the right side. */
    iconColor?: string;

    @FieldControlObjectResolvedProperty<TextProperties<CT>, TextControlObject<CT>>()
    /** The minimum length of the text field value */
    minLength?: number;

    @FieldControlObjectResolvedProperty<TextProperties<CT>, TextControlObject<CT>>()
    /**  The maximum length of the text field value  */
    maxLength?: number;

    @ControlObjectProperty<TextProperties<CT>, TextControlObject<CT>>()
    /** The helper text underneath the field */
    placeholder?: string;

    @FieldControlObjectResolvedProperty<TextProperties<CT>, TextControlObject<CT>>()
    /** Text to be displayed inline after the field value */
    prefix?: string;

    @FieldControlObjectResolvedProperty<TextProperties<CT>, TextControlObject<CT>>()
    /** Text to be displayed inline before the field value */
    postfix?: string;

    @ControlObjectProperty<TextProperties<CT>, TextControlObject<CT>>()
    /** The type of input field */
    isPassword?: boolean;
}
