import * as React from 'react';
import type { CarbonWrapperProps } from '../carbon-wrapper';
import { CarbonWrapper } from '../carbon-wrapper';
import { render } from '@testing-library/react';
import type { BaseReadonlyComponentProperties } from '../field-base-component-types';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { Prefixable, Postfixable } from '../traits';
import * as stateUtils from '../../../utils/state-utils';
import { getMockPageDefinition } from '../../../__tests__/test-helpers';

jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() => getMockPageDefinition('testScreen'));

describe('Carbon Wrapper', () => {
    let properties: BaseReadonlyComponentProperties<
        EditableFieldProperties & Prefixable<any> & Postfixable<any>,
        any,
        CarbonWrapperProps
    >;

    beforeEach(() => {
        const childrenElement = <div data-testid="fake-field">Fake field content</div>;
        properties = {
            screenId: 'testScreen',
            className: 'e-test-class-name',
            componentName: 'test',
            fieldProperties: { bind: 'testFieldId', title: 'Test field title' },
            elementId: 'testFieldId',
            children: childrenElement,
            locale: 'en-US',
            onFocus: () => {},
        };
    });

    describe('editable field', () => {
        it('should calculate the class name correctly', () => {
            const wrapper = render(<CarbonWrapper {...properties} />);

            expect(wrapper.getByTestId('e-test-field', { exact: false })).toHaveClass('e-test-class-name');
            expect(wrapper.getByTestId('e-test-field', { exact: false })).toHaveClass('e-field');
        });

        it('should encapsulate the field body', () => {
            const wrapper = render(<CarbonWrapper {...properties} />);
            expect(wrapper.getByTestId('fake-field', { exact: false })).toHaveTextContent('Fake field content');
        });
    });

    describe('read-only mode', () => {
        beforeEach(() => {
            properties.fieldProperties.isReadOnly = true;
        });

        it('should render the field label', () => {
            const wrapper = render(<CarbonWrapper {...properties} />);
            expect(wrapper.getByTestId('e-field-label')).toHaveTextContent('Test field title');
        });

        it('should render the field helper text', () => {
            properties.fieldProperties.helperText = 'Field helper text content';
            const wrapper = render(<CarbonWrapper {...properties} />);
            expect(wrapper.getByTestId('e-field-helper-text')).toHaveTextContent('Field helper text content');
        });

        it('should render the field value', () => {
            properties.value = 'Test field value';
            const wrapper = render(<CarbonWrapper {...properties} />);
            expect(wrapper.getByTestId('e-field-value')).toHaveTextContent('Test field value');
        });

        it('should remap value if `map` property is provided', () => {
            properties.value = 1.23;
            properties.map = (value: number) => value.toFixed(4);
            const wrapper = render(<CarbonWrapper {...properties} />);
            expect(wrapper.getByTestId('e-field-value')).toHaveTextContent('1.2300');
        });

        describe('prefixing', () => {
            it('should render the field value with a string prefix', () => {
                properties.value = 'Test field value';
                properties.fieldProperties.prefix = '$';
                const wrapper = render(<CarbonWrapper {...properties} />);
                expect(wrapper.getByTestId('e-field-value')).toHaveTextContent('$ Test field value');
            });

            it('should render the field value with a function prefix', () => {
                properties.value = 'Test field value';
                properties.handlersArguments = { rowValue: { currencySymbol: '£' } };
                properties.fieldProperties.prefix = (value: any, rowValue: any) => rowValue.currencySymbol;
                const wrapper = render(<CarbonWrapper {...properties} />);
                expect(wrapper.getByTestId('e-field-value')).toHaveTextContent('£ Test field value');
            });
        });

        describe('postfixing', () => {
            it('should render the field value with a string postfix', () => {
                properties.value = 'Test field value';
                properties.fieldProperties.postfix = '$';
                const wrapper = render(<CarbonWrapper {...properties} />);
                expect(wrapper.getByTestId('e-field-value')).toHaveTextContent('Test field value $');
            });

            it('should render the field value with a function postfix', () => {
                properties.value = 'Test field value';
                properties.handlersArguments = { rowValue: { currencySymbol: '£' } };
                properties.fieldProperties.postfix = (value: any, rowValue: any) => rowValue.currencySymbol;
                const wrapper = render(<CarbonWrapper {...properties} />);
                expect(wrapper.getByTestId('e-field-value')).toHaveTextContent('Test field value £');
            });
        });

        describe('nested read-only mode', () => {
            beforeEach(() => {
                properties.nestedReadOnlyField = true;
            });

            it('should render not the field label', () => {
                const wrapper = render(<CarbonWrapper {...properties} />);
                expect(wrapper.queryByTestId('e-field-label')).toBeNull();
            });

            it('should render the field helper text', () => {
                properties.fieldProperties.helperText = 'Field helper text content';
                const wrapper = render(<CarbonWrapper {...properties} />);
                expect(wrapper.queryByTestId('e-field-helper-text')).toBeNull();
            });
        });
    });
});
