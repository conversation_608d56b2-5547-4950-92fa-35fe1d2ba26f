import * as React from 'react';
import * as carbonUtilityComponents from '../carbon-utility-components';
import { render } from '@testing-library/react';

describe('Carbon Helpers', () => {
    describe('FieldLabel component', () => {
        it('Should render a FieldLabel with label test and a react children', () => {
            const label = 'label-string';
            const childrenElement = <div id="testChildElement">Simple div</div>;
            const { container } = render(
                <carbonUtilityComponents.FieldLabel label={label}>
                    {childrenElement}
                </carbonUtilityComponents.FieldLabel>,
            );
            expect(container.querySelector('#testChildElement')).not.toBeNull();
        });
        it('FieldLabel snapshot', () => {
            const label = 'label-string';
            const childrenElement = <div>Simple div</div>;
            const { container } = render(
                <carbonUtilityComponents.FieldLabel label={label}>
                    {childrenElement}
                </carbonUtilityComponents.FieldLabel>,
            );
            expect(container).toMatchSnapshot();
        });
    });
});
