import * as React from 'react';
import { ContextType } from '../../../types';
import * as carbonHelpers from '../carbon-helpers';
import type { EditableFieldComponentProperties, FieldComponentExternalProperties } from '../field-base-component-types';

describe('Carbon Helpers', () => {
    describe('getCommonCarbonComponentProperties', () => {
        let props = {
            elementId: 'fieldId',
            fieldProperties: {
                isDisabled: true,
                isReadOnly: true,
                helperText: 'helperText',
                title: 'titleField',
                size: 'M',
                placeholder: 'placeholder',
                postfix: 'postfix',
                prefix: 'prefix',
            },
        } as EditableFieldComponentProperties<any, any> & FieldComponentExternalProperties;
        beforeEach(() => {
            props = {
                elementId: 'fieldId',
                fieldProperties: {
                    isDisabled: true,
                    isReadOnly: true,
                    helperText: 'helperText',
                    title: 'titleField',
                    size: 'M',
                    placeholder: 'placeholder',
                    postfix: 'postfix',
                    prefix: 'prefix',
                },
            } as EditableFieldComponentProperties<any, any> & FieldComponentExternalProperties;
        });
        it('Should map props to Carbon Properties', () => {
            const expected = {
                autoComplete: 'off',
                name: 'fieldId',
                disabled: true,
                readOnly: true,
                fieldHelp: 'helperText',
                label: 'titleField',
                size: 'M',
                placeholder: 'placeholder',
                children: <span className="e-field-postfix">postfix</span>,
                'data-label': 'titleField',
                leftChildren: <span className="e-field-prefix">prefix</span>,
                tabindex: undefined,
                error: undefined,
                validationOnLabel: true,
                onFocus: expect.any(Function),
                id: expect.any(String),
                onBlur: expect.any(Function),
            };
            const carbonProps = carbonHelpers.getCommonCarbonComponentProperties(props);
            expect(carbonProps).toEqual(expected);
        });

        it('Carbon Property readOnly should be true because context is NavigationPanel', () => {
            props.fieldProperties.isReadOnly = false;
            let carbonProps = carbonHelpers.getCommonCarbonComponentProperties(props);
            expect(carbonProps.readOnly).toBeFalsy();
            props.contextType = ContextType.navigationPanel;
            carbonProps = carbonHelpers.getCommonCarbonComponentProperties(props);
            expect(carbonProps.readOnly).toBeTruthy();
        });

        it('Carbon Property label should be undefined because context is NavigationPanel', () => {
            props.contextType = ContextType.navigationPanel;
            const carbonProps = carbonHelpers.getCommonCarbonComponentProperties(props);
            expect(carbonProps.label).toBeUndefined();
        });

        it('Carbon Property label should be titleField * because has title and is mandatory', () => {
            props.fieldProperties.isMandatory = true;
            const carbonProps = carbonHelpers.getCommonCarbonComponentProperties(props);
            expect(carbonProps.label).toEqual('titleField *');
        });

        it('Carbon Property label should be titleField * because has title and a mandatory callback that returns true', () => {
            props.fieldProperties.isMandatory = () => true;
            const carbonProps = carbonHelpers.getCommonCarbonComponentProperties(props);
            expect(carbonProps.label).toEqual('titleField *');
        });

        it('Carbon Property label should be titleField because has title and a mandatory callback that returns false', () => {
            props.fieldProperties.isMandatory = () => false;
            const carbonProps = carbonHelpers.getCommonCarbonComponentProperties(props);
            expect(carbonProps.label).toEqual('titleField');
        });

        it('Carbon Property label should be titleField because has title', () => {
            props.fieldProperties.isMandatory = false;
            const carbonProps = carbonHelpers.getCommonCarbonComponentProperties(props);
            expect(carbonProps.label).toEqual('titleField');
        });

        it('Carbon Property label should be * because has no title and is mandatory', () => {
            props.fieldProperties.isMandatory = true;
            props.fieldProperties.title = '';
            const carbonProps = carbonHelpers.getCommonCarbonComponentProperties(props);
            expect(carbonProps.label).toEqual('*');
        });

        it('Carbon Property children should be undefined because no postfix', () => {
            props.fieldProperties.postfix = '';
            const carbonProps = carbonHelpers.getCommonCarbonComponentProperties(props);
            expect(carbonProps.children).toBeUndefined();
        });

        it('Carbon Property leftChildren should be undefined because no prefix', () => {
            props.fieldProperties.prefix = '';
            const carbonProps = carbonHelpers.getCommonCarbonComponentProperties(props);
            expect(carbonProps.leftChildren).toBeUndefined();
        });

        it('Carbon Property tabIndex should be -1 because context is navigationPanel', () => {
            props.contextType = ContextType.navigationPanel;
            const carbonProps = carbonHelpers.getCommonCarbonComponentProperties(props);
            expect(carbonProps.tabindex).toEqual(-1);
        });

        it('Carbon Property tabIndex should be undefined because context is NOT navigationPanel', () => {
            props.contextType = ContextType.page;
            const carbonProps = carbonHelpers.getCommonCarbonComponentProperties(props);
            expect(carbonProps.tabindex).toBeUndefined();
        });
    });
});
