import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import {
    addBusinessActionToState,
    addPageControlObject,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../__tests__/test-helpers';
import type { XtremAppState } from '../../../redux';
import * as events from '../../../utils/events';
import FieldActions from '../field-actions-component';
import { fireEvent, render } from '@testing-library/react';

describe('Field Actions', () => {
    const screenId = 'TestPage';
    const elementId = 'testField';
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let state: XtremAppState;
    let action1: any;
    let action2: any;
    let actionWithoutIcon: any;
    let disabledAction: any;
    let hiddenAction: any;
    let triggerEventListenerSpy: jest.SpyInstance<any>;

    beforeEach(() => {
        triggerEventListenerSpy = jest.spyOn(events, 'triggerFieldEvent').mockImplementation(jest.fn());
        state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);

        addPageControlObject(state, screenId, { isTransient: true });

        action1 = addBusinessActionToState(state, screenId, 'testHeaderAction1', {
            title: 'header-action-1',
            icon: 'refresh',
        });

        action2 = addBusinessActionToState(state, screenId, 'testHeaderAction2', {
            title: 'header-action-2',
            icon: 'calendar',
            onClick: () => {},
        });

        actionWithoutIcon = addBusinessActionToState(state, screenId, 'testHeaderActionWithoutIcon', {
            title: 'header-action-3',
        });

        disabledAction = addBusinessActionToState(state, screenId, 'testHeaderActionDisabled', {
            title: 'header-action-4',
            isDisabled: true,
            icon: 'call',
            onClick: () => {},
        });

        hiddenAction = addBusinessActionToState(state, screenId, 'testHeaderActionHidden', {
            title: 'header-action-5',
            isHidden: true,
            icon: 'marker',
        });

        state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] = {
            fieldActions() {
                return [action1, action2, actionWithoutIcon, disabledAction, hiddenAction];
            },
        } as any;

        mockStore = getMockStore(state);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should trigger on enabled actions', () => {
        const { container } = render(
            <Provider store={mockStore}>
                <FieldActions screenId={screenId} fieldId={elementId} />
            </Provider>,
        );

        expect(triggerEventListenerSpy).not.toHaveBeenCalled();

        fireEvent.click(container.querySelectorAll('button')[1]);

        expect(triggerEventListenerSpy).toHaveBeenCalledWith(screenId, 'testHeaderAction2', 'onClick');
    });

    it('should not trigger on disabled actions', () => {
        const { container } = render(
            <Provider store={mockStore}>
                <FieldActions screenId={screenId} fieldId={elementId} />
            </Provider>,
        );

        expect(triggerEventListenerSpy).not.toHaveBeenCalled();

        fireEvent.click(container.querySelectorAll('button')[2]);

        expect(triggerEventListenerSpy).not.toHaveBeenCalled();
    });
});
