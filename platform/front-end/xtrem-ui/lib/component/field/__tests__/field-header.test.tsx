import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { FieldKey } from '../../types';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../__tests__/test-helpers';
import type * as xtremRedux from '../../../redux';
import { FieldHeader } from '../field-header';
import { render } from '@testing-library/react';

const screenId = 'TestPage';
const elementId = 'fieldName';
let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

describe('Snapshots', () => {
    beforeEach(() => {
        const state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        addFieldToState(
            FieldKey.Table,
            state,
            screenId,
            elementId,
            {
                fieldActions: () => [],
                columns: [],
            },
            'Test Value',
        );
        mockStore = getMockStore(state);
    });

    it('should render with default properties', () => {
        const { container } = render(
            <Provider store={mockStore}>
                <FieldHeader screenId={screenId} elementId={elementId} />
            </Provider>,
        );
        expect(container).toMatchSnapshot();
    });
});
