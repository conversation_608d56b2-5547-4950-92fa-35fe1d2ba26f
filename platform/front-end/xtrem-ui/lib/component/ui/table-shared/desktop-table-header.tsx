import Label from 'carbon-react/esm/__internal__/label';
import ButtonBar from 'carbon-react/esm/components/button-bar';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import ButtonToggleGroup from 'carbon-react/esm/components/button-toggle/button-toggle-group';
import ButtonToggle from 'carbon-react/esm/components/button-toggle/button-toggle.component';
import * as React from 'react';
import { useDispatch } from 'react-redux';
import { refreshNavigationPanel } from '../../../redux/actions';
import type { Filter } from '../../../service/filter-service';
import { localize } from '../../../service/i18n-service';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { navigationPanelId } from '../../container/navigation-panel/navigation-panel-types';
import type { OptionsMenuItem } from '../../container/page/page-types';
import type { PageActionControlObject } from '../../control-objects';
import { getFieldTitle } from '../../field/carbon-helpers';
import FieldActions from '../../field/field-actions-component';
import type { InternalNestedGridProperties } from '../../field/nested-grid/nested-grid-control-object';
import type { InternalTableProperties, TableViewMode } from '../../field/table/table-component-types';
import type { CalendarView } from '../calendar-body/calendar-body-types';
import { CalendarViewSelector } from '../calendar-body/calendar-view-selector';
import type { FilterManagerField } from '../filter/filter-manager';
import { FiltersComponent } from '../filter/filters-component';
import StatusIconWithPopover from '../icon/status-icon-with-popover';
import { ConnectedNestedFieldErrors } from '../nested-field-errors-component';
import { DesktopTableAddNewRowButton } from './desktop-table-add-new-row-button';
import { TableCreateActions } from './table-create-actions/table-create-actions';
import { ConnectedTableOptionsMenu } from './table-options-menu';
import { TableSearchBox } from './table-search-box-component';
import ConnectedBusinessAction from '../../container/footer/business-action';
import { DesktopTableExportButton } from './desktop-table-export-button';
import { DesktopTablePrintButton } from './desktop-table-print-button';
import type { OnTelemetryEventFunction, XtremAppState } from '../../../redux/state';
import { TableViewSelector } from './table-view-selector';
import { getNestedFieldsFromProperties } from '../../../utils/nested-field-utils';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

export interface DesktopTableHeaderProps {
    canAddNewLine: boolean;
    columnPanelDisabled?: boolean;
    elementId: string;
    fieldProperties: InternalTableProperties | InternalNestedGridProperties;
    filterComponentFields?: FilterManagerField[];
    filtersComponentFilters?: Filter[];
    filterText?: string;
    hasAddItemsButton: boolean;
    hasCalendarView?: boolean;
    hasData?: boolean;
    hasFloatingFilters?: boolean;
    hasSearchBox?: boolean;
    hasSidebar: boolean;
    isDisabled: boolean;
    isExportDisabled?: boolean;
    isReadOnly: boolean;
    level?: number;
    onExport?: (target: 'csv' | 'excel') => void;
    onFilterByErrors?: () => void;
    onFiltersComponentChanged?: (filters: Filter[]) => Promise<void>;
    onFocusPhantomRow?: () => void;
    /** Callback that is called when the column settings button is clicked. If undefined, the column settings button is not rendered. */
    onOpenColumnPanel?: () => void;
    onOptionsMenuItemChange?: (newOptionItem: OptionsMenuItem) => void;
    onSearchBoxValueChange?: (textValue: string) => void;
    onSwitchCalendarView?: (calendarView: CalendarView) => void;
    onSwitchView?: (view: TableViewMode) => void;
    onTelemetryEvent?: OnTelemetryEventFunction;
    /** Callback that is called when the toggle filter button is clicked. If undefined, the toggle button is not rendered. */
    onToggleFilters?: () => void;
    onUnsetFilterByErrors?: () => void;
    screenId: string;
    selectedCalendarView?: CalendarView;
    selectedOptionsMenuItem?: OptionsMenuItem;
    tableViewMode?: TableViewMode;
    validationErrors: ValidationResult[];
}

/**
 * This component contains the header functionality for the desktop table and nested grids. The functionality of these two components is very similar.
 * It contains the field action buttons, export button, business header actions, options menu, validation errors and the field title label.
 */
export const DesktopTableHeaderComponent: React.FC<DesktopTableHeaderProps> = React.memo<DesktopTableHeaderProps>(
    ({
        canAddNewLine,
        columnPanelDisabled,
        elementId,
        fieldProperties,
        filterComponentFields,
        filtersComponentFilters,
        filterText,
        hasAddItemsButton,
        hasCalendarView,
        hasData,
        hasFloatingFilters,
        hasSidebar,
        isDisabled,
        isExportDisabled,
        isReadOnly,
        level,
        onExport,
        onFilterByErrors,
        onFiltersComponentChanged,
        onFocusPhantomRow,
        onOpenColumnPanel,
        onOptionsMenuItemChange,
        onSearchBoxValueChange,
        onSwitchCalendarView,
        onSwitchView,
        onTelemetryEvent,
        onToggleFilters,
        onUnsetFilterByErrors,
        screenId,
        selectedCalendarView,
        selectedOptionsMenuItem,
        tableViewMode,
        validationErrors,
    }) => {
        const dispatch = useDispatch();

        const optionsMenu = React.useMemo(
            () =>
                resolveByValue<OptionsMenuItem[]>({
                    propertyValue: fieldProperties.optionsMenu,
                    rowValue: null,
                    screenId,
                    fieldValue: null,
                    skipHexFormat: true,
                }) || [],
            [fieldProperties.optionsMenu, screenId],
        );

        const onRefreshMainList = React.useCallback(() => {
            dispatch(refreshNavigationPanel(screenId));
        }, [dispatch, screenId]);

        const { clientUserSettingsListPage, clientUserSettingsEditPage } = useDeepEqualSelector<
            XtremAppState,
            {
                clientUserSettingsListPage: string | null;
                clientUserSettingsEditPage: string | null;
            }
        >(state => ({
            clientUserSettingsListPage: state.clientUserSettingsListPage,
            clientUserSettingsEditPage: state.clientUserSettingsEditPage,
        }));

        const hasTableViewSelector = React.useMemo(() => {
            return elementId === navigationPanelId && clientUserSettingsListPage && clientUserSettingsEditPage;
        }, [clientUserSettingsEditPage, clientUserSettingsListPage, elementId]);

        const headerBusinessActions = React.useMemo(
            () =>
                resolveByValue<PageActionControlObject | PageActionControlObject[]>({
                    propertyValue: fieldProperties.headerBusinessActions,
                    rowValue: null,
                    screenId,
                    fieldValue: null,
                    skipHexFormat: true,
                }) || [],
            [fieldProperties.headerBusinessActions, screenId],
        );

        const hasHeaderBusinessActions = (): boolean => {
            if (headerBusinessActions === undefined) {
                return false;
            }

            if (Array.isArray(headerBusinessActions) && headerBusinessActions.length === 0) {
                return false;
            }

            return true;
        };

        const addItemActions = React.useMemo(
            () =>
                resolveByValue<PageActionControlObject[]>({
                    propertyValue: (fieldProperties as InternalTableProperties).addItemActions,
                    rowValue: null,
                    screenId,
                    fieldValue: null,
                    skipHexFormat: true,
                }) || [],
            // eslint-disable-next-line react-hooks/exhaustive-deps
            [(fieldProperties as InternalTableProperties).addItemActions, screenId],
        );

        const fieldActions = React.useMemo(
            () =>
                resolveByValue<PageActionControlObject[]>({
                    propertyValue: fieldProperties.fieldActions,
                    rowValue: null,
                    screenId,
                    fieldValue: null,
                    skipHexFormat: true,
                }) || [],
            [fieldProperties.fieldActions, screenId],
        );

        const getOptionsMenuType = (): string | undefined => {
            if (!fieldProperties.optionsMenu?.length) {
                return undefined;
            }

            return fieldProperties.optionsMenuType;
        };

        const renderSearchBox = (): React.ReactNode => {
            if (onSearchBoxValueChange && fieldProperties.hasSearchBoxMobile) {
                return (
                    <TableSearchBox
                        elementId={elementId}
                        fieldProperties={fieldProperties}
                        filterText={filterText}
                        isDisabled={isDisabled}
                        onSearchBoxValueChange={onSearchBoxValueChange}
                        screenId={screenId}
                    />
                );
            }

            return <div className="e-table-field-mobile-search" />;
        };

        const renderViewSelector = (): React.ReactNode => {
            if (!hasCalendarView || !onSwitchView || isDisabled) {
                return null;
            }

            const value: TableViewMode = tableViewMode || 'table';
            return (
                <ButtonToggleGroup
                    id={`table-view-selector-${screenId}-${elementId}`}
                    value={value}
                    onChange={(_evt: React.MouseEvent<HTMLButtonElement>, selectedView?: string): void =>
                        onSwitchView(selectedView as TableViewMode)
                    }
                    mr="8px"
                    ml="2px"
                >
                    <ButtonToggle
                        value="table"
                        buttonIcon="csv"
                        aria-label={localize('@sage/xtrem-ui/table-table-view', 'Switch to table view')}
                    />
                    <ButtonToggle
                        value="calendar"
                        buttonIcon="calendar"
                        aria-label={localize('@sage/xtrem-ui/table-calendar-view', 'Switch to calendar view')}
                    />
                </ButtonToggleGroup>
            );
        };

        const renderHeaderActions = (): React.ReactNode => {
            const filterButtonMessage = hasFloatingFilters
                ? localize('@sage/xtrem-ui/hide-floating-filters', 'Hide Table Filters')
                : localize('@sage/xtrem-ui/show-floating-filters', 'Show Table Filters');
            const refreshButtonMessage = localize('@sage/xtrem-ui/main-list-refresh', 'Refresh');
            const openColumnPanelMessage = localize('@sage/xtrem-ui/table-open-column-panel', 'Open column panel');

            return (
                <div className="e-field-actions-wrapper">
                    {hasAddItemsButton && (
                        <DesktopTableAddNewRowButton
                            addItemActions={addItemActions}
                            canAddNewLine={canAddNewLine}
                            cardDefinition={fieldProperties.mobileCard}
                            columns={getNestedFieldsFromProperties(fieldProperties)}
                            elementId={elementId}
                            hasSidebar={hasSidebar}
                            isDisabled={isDisabled || isReadOnly}
                            level={level}
                            onFocusPhantomRow={onFocusPhantomRow}
                            screenId={screenId}
                            sidebarDefinition={
                                (fieldProperties as InternalTableProperties).sidebar ||
                                (fieldProperties as InternalNestedGridProperties).levels?.[0]?.sidebar
                            }
                        />
                    )}
                    {elementId === navigationPanelId
                        ? hasHeaderBusinessActions() && (
                        <TableCreateActions
                            createActions={fieldProperties.headerBusinessActions}
                            multiActionType="multi-action-button"
                            screenId={screenId}
                            elementId={elementId}
                              />
                          )
                        : headerBusinessActions &&
                          Array.isArray(headerBusinessActions) &&
                          headerBusinessActions.map(hba => (
                              <ConnectedBusinessAction
                                  id={hba.id}
                                  defaultButtonType="tertiary"
                                  isSeparated={true}
                                  key={hba.id}
                                  screenId={screenId}
                              />
                          ))}
                    {(!tableViewMode || tableViewMode === 'table') && (
                        <DesktopTablePrintButton screenId={screenId} elementId={elementId} />
                    )}
                    {(fieldProperties as InternalTableProperties).canExport &&
                        (!tableViewMode || tableViewMode === 'table') &&
                        onExport && (
                            <DesktopTableExportButton
                                screenId={screenId}
                                elementId={elementId}
                                hasData={!!hasData}
                                isDisabled={isDisabled || !!isExportDisabled}
                                onExport={onExport}
                            />
                        )}
                    {hasCalendarView && onSwitchCalendarView && tableViewMode === 'calendar' && (
                        <CalendarViewSelector
                            onSelectView={onSwitchCalendarView}
                            selectedView={selectedCalendarView || 'dayGridMonth'}
                        />
                    )}
                    <div className="e-table-field-button-bar-container">
                        <ButtonBar>
                            {elementId === navigationPanelId && (
                                <ButtonMinor
                                    buttonType="tertiary"
                                    disabled={isDisabled}
                                    onClick={onRefreshMainList}
                                    iconType="refresh"
                                    iconTooltipMessage={refreshButtonMessage}
                                    aria-label={refreshButtonMessage}
                                    data-pendoid={`refreshTable-${elementId}`}
                                />
                            )}
                            <FieldActions
                                screenId={screenId}
                                fieldId={elementId}
                                isDisabled={isDisabled}
                                additionalFieldActions={fieldProperties.additionalFieldActions}
                            />
                            {onToggleFilters && (!tableViewMode || tableViewMode === 'table') && (
                                <ButtonMinor
                                    buttonType="tertiary"
                                    disabled={isDisabled}
                                    onClick={onToggleFilters}
                                    iconType="filter"
                                    iconTooltipMessage={filterButtonMessage}
                                    aria-label={filterButtonMessage}
                                    data-pendoid={`tableQuickFilters-${elementId}-${hasFloatingFilters ? 'close' : 'open'}`}
                                />
                            )}
                            {onOpenColumnPanel && (!tableViewMode || tableViewMode === 'table') && (
                                <ButtonMinor
                                    buttonType="tertiary"
                                    disabled={isDisabled || columnPanelDisabled}
                                    onClick={onOpenColumnPanel}
                                    iconType="settings"
                                    iconTooltipMessage={openColumnPanelMessage}
                                    aria-label={openColumnPanelMessage}
                                    data-pendoid={`openTableColumnPanel-${elementId}`}
                                />
                            )}
                            {onFiltersComponentChanged && filterComponentFields && filtersComponentFilters && (
                                <FiltersComponent
                                    screenId={screenId}
                                    fields={filterComponentFields}
                                    handleSave={onFiltersComponentChanged}
                                    filters={filtersComponentFilters}
                                    isDisabled={isDisabled}
                                />
                            )}
                        </ButtonBar>
                    </div>
                </div>
            );
        };

        const tableTitle = getFieldTitle(screenId, fieldProperties, null);
        const needsTitleLine =
            (tableTitle && !fieldProperties.isTitleHidden) ||
            validationErrors.length > 0 ||
            fieldProperties.warningMessage ||
            fieldProperties.infoMessage;

        const renderFieldTitle = (): React.ReactNode => {
            if (!needsTitleLine) {
                return null;
            }

            return (
                <div className="e-field-title e-table-field-title">
                    <Label htmlFor={undefined as unknown as string}>{tableTitle}</Label>
                    <StatusIconWithPopover
                        screenId={screenId}
                        validationErrors={validationErrors}
                        content={<ConnectedNestedFieldErrors screenId={screenId} elementId={elementId} />}
                        filterErrors={onFilterByErrors}
                        unFilterErrors={onUnsetFilterByErrors}
                        warningMessage={fieldProperties.warningMessage}
                        infoMessage={fieldProperties.infoMessage}
                    />
                </div>
            );
        };

        const renderOptionsMenu = (): React.ReactNode => {
            if (onOptionsMenuItemChange) {
                return (
                    <ConnectedTableOptionsMenu
                        elementId={elementId}
                        onSelectionChange={onOptionsMenuItemChange}
                        optionsMenuItems={optionsMenu}
                        optionsMenuType={fieldProperties.optionsMenuType}
                        screenId={screenId}
                        selectedOptionsMenuItem={selectedOptionsMenuItem}
                        onTelemetryEvent={onTelemetryEvent}
                    />
                );
            }
            return null;
        };

        // If none of the header functionality is used, the component is not rendered
        if (
            !needsTitleLine &&
            optionsMenu.length === 0 &&
            !hasHeaderBusinessActions() &&
            fieldActions.length === 0 &&
            !(fieldProperties as InternalTableProperties).canExport &&
            !onExport &&
            !(fieldProperties as InternalTableProperties).hasSearchBoxMobile
        ) {
            return null;
        }

        // If we have an options menu than the title displayed above the header line.
        if (optionsMenu.length !== 0) {
            return (
                <div className="e-field-header-wrapper">
                    <div className="e-field-title-wrapper">{renderFieldTitle()}</div>
                    <div className="e-field-header" data-options-menu-type={getOptionsMenuType()}>
                        {renderViewSelector()}
                        {fieldProperties.optionsMenuType !== 'tabs' && onOptionsMenuItemChange && renderOptionsMenu()}
                        {hasTableViewSelector && <TableViewSelector screenId={screenId} elementId={elementId} />}
                        <div className="e-field-mobile-search-and-actions">
                            {renderSearchBox()}
                            {renderHeaderActions()}
                        </div>
                        {fieldProperties.optionsMenuType === 'tabs' && renderOptionsMenu()}
                    </div>
                </div>
            );
        }

        return (
            <div className="e-field-header-wrapper">
                <div className="e-field-header">
                    {renderFieldTitle()}
                    {hasTableViewSelector && <TableViewSelector screenId={screenId} elementId={elementId} />}
                    <div className="e-field-mobile-search-and-actions">
                        {renderSearchBox()}
                        {renderHeaderActions()}
                    </div>
                </div>
            </div>
        );
    },
);

DesktopTableHeaderComponent.displayName = 'DesktopTableHeaderComponent';
