import type { ButtonProps } from 'carbon-react/esm/components/button';
import Button from 'carbon-react/esm/components/button';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import { camelCase, noop } from 'lodash';
import React from 'react';
import { connect } from 'react-redux';
import styled from 'styled-components';
import type * as xtremRedux from '../../../redux';
import { calculateActionMenu } from '../../../utils/action-menu-utils';
import { TABLE_INLINE_ACTION_CSS_CLASS } from '../../../utils/constants';
import { triggerHandledEvent } from '../../../utils/events';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import { navigationPanelId } from '../../container/navigation-panel/navigation-panel-types';
import type { GridRowActionType } from '../../types';
import type { TableDropdownActionsCellProps } from './cell/table-dropdown-action-cell';
import type {
    CollectionPropertiesWithActions,
    InlineCollectionItemAction,
} from './table-dropdown-actions/table-dropdown-action-types';
import type { TableDropdownActionExternalProps } from './table-dropdown-actions/table-dropdown-actions';

interface ResolvedInlineCollectionItemAction
    extends Omit<InlineCollectionItemAction<any, any>, 'isDisabled' | 'isHidden' | 'isDisplayed' | 'onClick'> {
    isDisabled?: boolean;
    isDisplayed?: boolean;
    isHidden?: boolean;
    key: string;
    onClick?: () => void;
}

type TableInlineActionsExternalProps = Omit<TableDropdownActionExternalProps, 'actions'> & {
    actions?: GridRowActionType;
};

interface TableInlineActionsProps {
    inlineActions: Array<ResolvedInlineCollectionItemAction>;
    column?: TableDropdownActionsCellProps['column'];
    api?: TableDropdownActionsCellProps['api'];
    rowIndex?: TableDropdownActionsCellProps['node']['rowIndex'];
    hasDropdownActions?: boolean;
    elementId: string;
    level: number;
}

const getTableInlineActionTestId = (title: string, id?: string): string => {
    const uniqueId = camelCase(id || title);
    return `${TABLE_INLINE_ACTION_CSS_CLASS} e-inline-action e-inline-action--${uniqueId} e-action e-action--${uniqueId}`;
};

type InlineButtonProps = ButtonProps & {
    tabIndex: string;
    className: string;
    'data-testid': string;
    'data-pendoid'?: string;
    isHidden: boolean;
    isDisplayed: boolean;
    height?: string;
    width?: string;
};

const StyledMinorButton = styled(ButtonMinor)`
    ${(props: InlineButtonProps): string => (props.isDisplayed ? '' : 'display: none;')}
    ${(props: InlineButtonProps): string =>
        props.height ? `height: ${props.height}; min-height: ${props.height};` : ''}
    ${(props: InlineButtonProps): string => (props.width ? `width: ${props.width}; min-width: ${props.width};` : '')}
    ${(props: InlineButtonProps): string =>
        props.isHidden ? '.e-card-content && { display: none; }; visibility: hidden;' : 'visibility: visible;'}
    & [data-component="icon"] {
        scale: 0.9;
    }
`;

const StyledButton = styled(Button)`
    ${(props: InlineButtonProps): string => (props.isDisplayed ? '' : 'display: none;')}
    ${(props: InlineButtonProps): string =>
        props.height ? `height: ${props.height}; min-height: ${props.height};` : ''}
    ${(props: InlineButtonProps): string => (props.width ? `width: ${props.width}; min-width: ${props.width};` : '')}
    ${(props: InlineButtonProps): string =>
        props.isHidden ? '.e-card-content && { display: none; }; visibility: hidden;' : 'visibility: visible;'}
    & [data-component="icon"] {
        scale: 0.9;
    }
`;

const TableInlineAction: React.FC<
    ResolvedInlineCollectionItemAction & {
        onKeyDown: React.ComponentProps<'button'>['onKeyDown'];
        pendoId?: string;
    }
> = React.memo(
    ({
        id,
        buttonType,
        icon,
        isDestructive,
        isDisabled,
        isDisplayed,
        isHidden,
        isMajor,
        key,
        onClick,
        onKeyDown,
        pendoId,
        title,
    }) => {
        const buttonRef = React.useRef<React.ElementRef<'button'> | null>(null);

        const buttonProps: InlineButtonProps = {
            tabIndex: isHidden || isDisabled ? '-1' : '0',
            className: TABLE_INLINE_ACTION_CSS_CLASS,
            height: '24px',
            width: '24px',
            'data-testid': getTableInlineActionTestId(title, id),
            'data-pendoid': pendoId,
            buttonType: buttonType ?? 'tertiary',
            size: 'small',
            iconType: icon,
            'aria-label': title ?? '',
            iconTooltipMessage: title ?? '',
            onClick,
            onKeyDown,
            disabled: isDisabled,
            destructive: isDestructive ?? false,
            isHidden: isHidden ?? false,
            isDisplayed: isDisplayed ?? true,
        } as const;

        if (isMajor) {
            return <StyledButton ref={buttonRef} key={key} {...buttonProps} />;
        }

        return (
            <StyledMinorButton
                ref={r => {
                    buttonRef.current = r;
                }}
                key={key}
                {...buttonProps}
            />
        );
    },
    (prev, next) => {
        return (
            prev.isHidden === next.isHidden &&
            prev.isDisabled === next.isDisabled &&
            prev.buttonType === next.buttonType &&
            prev.icon === next.icon &&
            prev.title === next.title &&
            prev.isDestructive === next.isDestructive &&
            prev.isDisplayed === next.isDisplayed
        );
    },
);

TableInlineAction.displayName = 'TableInlineAction';

function TableInlineActions({ inlineActions }: TableInlineActionsProps): React.ReactElement {
    return (
        <>
            {inlineActions.map(inlineAction => {
                return <TableInlineAction {...inlineAction} key={inlineAction.key} onKeyDown={noop} />;
            })}
        </>
    );
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: TableInlineActionsExternalProps,
): TableInlineActionsProps => {
    const pageDefinition = getPageDefinitionFromState(props.screenId, state);
    const fieldProperties = pageDefinition.metadata.uiComponentProperties[
        props.fieldId
    ] as CollectionPropertiesWithActions;
    const tableData =
        props.fieldId === navigationPanelId
            ? pageDefinition.navigationPanel?.value
            : pageDefinition.values[props.fieldId];
    const isNestedGrid = Object.prototype.hasOwnProperty.call(fieldProperties, 'levels');

    const inlineActions = calculateActionMenu({
        actions: props.actions,
        actionType: 'table-inline-action',
        screenId: props.screenId,
        rowValue: props.rowValue,
        pendoId: `tableInlineAction-${props.fieldId}-${props.level}`,
        accessBindings: pageDefinition.accessBindings,
        onTriggerMenuItem: (context, onClick, onError): void => {
            state.applicationContext?.onTelemetryEvent?.(`tableInlineActionTriggered-${context.uniqueId}`, {
                screenId: props.screenId,
                elementId: props.fieldId,
                recordId: props.recordId,
                id: context.id,
                uniqueId: context.uniqueId,
            });

            if (!onClick) {
                return;
            }

            const args = [
                ...[
                    props.screenId,
                    props.fieldId,
                    { onClick, onError: onError || fieldProperties.onError },
                    props.recordId,
                    splitValueToMergedValue(props.rowValue),
                ],
                ...(isNestedGrid
                    ? [
                          props.level,
                          tableData.getAncestorIds({
                              id: props.recordId,
                              level: props.level,
                          }),
                      ]
                    : []),
            ];
            triggerHandledEvent(...(args as [any, any, any, any, any, any, any]));
        },
    });

    return {
        elementId: props.fieldId,
        level: props.level,
        inlineActions,
        column: props.column,
        api: props.api,
        rowIndex: props.rowIndex,
        hasDropdownActions: props.hasDropdownActions,
    };
};

export default connect(mapStateToProps)(TableInlineActions);
