import React from 'react';
import Sidebar from 'carbon-react/esm/components/sidebar';
import { DraggableContainer, DraggableItem } from 'carbon-react/esm/components/draggable';
import { Checkbox } from 'carbon-react/esm/components/checkbox';
import Typography from 'carbon-react/esm/components/typography';
import Icon from 'carbon-react/esm/components/icon';
import { TableSearchBox } from '../table-search-box-component';
import { localize } from '../../../../service/i18n-service';
import type { ColumnPanelColumnState } from '../../../../utils/ag-grid/ag-grid-utility-types';

export interface TableConfigurationDialogProps {
    onDialogClose: () => void;
    isDialogOpen?: boolean;
    columnPanelColumnStates: ColumnPanelColumnState[];
    onChangeColumnHiddenState: (colId: string, isHidden: boolean) => void;
    onOrderChange: (colIds: string[]) => void;
}

export function TableConfigurationDialogColumnItem({
    c,
    onVisibilityChanged,
}: {
    c: ColumnPanelColumnState;
    onVisibilityChanged: (ev: React.ChangeEvent<HTMLInputElement>) => void;
}): React.ReactElement {
    return (
        <div
            className="e-table-configuration-dialog-line"
            data-testid={`e-table-configuration-dialog-line-col-${c.colId}`}
        >
            <Checkbox
                label={c.title}
                value={c.colId}
                mb={0}
                checked={!c.isHidden}
                disabled={c.isMandatory}
                onChange={onVisibilityChanged}
            />
            {c.isMandatory && <Icon type="locked" />}
        </div>
    );
}

export function TableConfigurationDialog({
    isDialogOpen,
    onDialogClose,
    columnPanelColumnStates,
    onChangeColumnHiddenState,
    onOrderChange,
}: TableConfigurationDialogProps): React.ReactElement {
    const [columnFilter, setColumnFilter] = React.useState<string>('');
    const sidebarRef = React.useRef<HTMLDivElement | null>(null);
    const canCloseRef = React.useRef<boolean>(true);

    const onVisibilityChanged = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>) => {
            const colId = ev.target.value;
            const isHidden = ev.target.checked;
            onChangeColumnHiddenState(colId, isHidden);
        },
        [onChangeColumnHiddenState],
    );

    const shouldDisplayDraggableItem = React.useCallback(
        (c: ColumnPanelColumnState) => {
            // The items are visible if no filter applied or the title contains the filter value
            return (!columnFilter || c.title.toLowerCase().includes(columnFilter.toLowerCase())) && !c.isSpecialColumn;
        },
        [columnFilter],
    );

    const shouldDisplaySpecialItem = React.useCallback(
        (c: ColumnPanelColumnState) => {
            // The items are visible if no filter applied or the title contains the filter value
            return (!columnFilter || c.title.toLowerCase().includes(columnFilter.toLowerCase())) && !!c.isSpecialColumn;
        },
        [columnFilter],
    );

    const onItemsReordered = React.useCallback(
        (ids: string[], itemWasJustMoved: string) => {
            if (ids.length === columnPanelColumnStates.length) {
                const newIdOrder = ids.map(String);
                return onOrderChange(newIdOrder);
            }
            return onOrderChange(getNewOrder(columnPanelColumnStates, ids, itemWasJustMoved));
        },
        [onOrderChange, columnPanelColumnStates],
    );

    const areElementsOverlapping = React.useCallback((element1: HTMLElement, element2: HTMLElement): boolean => {
        const rect1 = element1.getBoundingClientRect();
        const rect2 = element2.getBoundingClientRect();

        if (
            !(
                rect1.right < rect2.left ||
                rect1.left > rect2.right ||
                rect1.bottom < rect2.top ||
                rect1.top > rect2.bottom
            )
        ) {
            return true;
        }
        return false;
    }, []);

    React.useEffect(() => {
        if (isDialogOpen) {
            canCloseRef.current = false;
            // eslint-disable-next-line no-return-assign
            setTimeout(() => (canCloseRef.current = true), 500); // Prevents the dialog from closing immediately after opening
        }
    }, [isDialogOpen]);

    const onClickListener = React.useCallback(
        (ev: MouseEvent) => {
            // If the user clicks outside the dialog, we automatically close it
            if (
                sidebarRef.current &&
                ev.target &&
                (ev.target as HTMLElement).isConnected &&
                !sidebarRef.current.contains(ev.target as HTMLElement) &&
                ((ev.target as HTMLElement).contains(sidebarRef.current) ||
                    !areElementsOverlapping(sidebarRef.current, ev.target as HTMLElement)) &&
                canCloseRef.current
            ) {
                onDialogClose();
            }
        },
        [onDialogClose, areElementsOverlapping],
    );

    React.useEffect(() => {
        // Add the event listener when the component mounts or onClickListener changes
        window.document.addEventListener('click', onClickListener);

        return (): void => {
            // Remove the event listener when the component unmounts or before a new listener is added
            window.document.removeEventListener('click', onClickListener);
        };
    }, [onClickListener]);

    const specialColumns = columnPanelColumnStates.filter(shouldDisplaySpecialItem);

    return (
        <Sidebar
            ref={sidebarRef}
            aria-label="sidebar"
            open={!!isDialogOpen}
            onCancel={onDialogClose}
            enableBackgroundUI={true}
            width="464px"
            header={
                <Typography variant="h3" mb="16px">
                    {localize('@sage/xtrem-ui/table-column-settings', 'Column settings')}
                </Typography>
            }
        >
            <div className="e-table-configuration-dialog">
                <TableSearchBox disableAutoFocus={true} onSearchBoxValueChange={setColumnFilter} />
                {isDialogOpen && (
                    <div className="e-table-configuration-dialog-columns">
                        {specialColumns?.length > 0 && (
                            <div className="e-table-configuration-dialog-special-columns">
                                {specialColumns.map((c: ColumnPanelColumnState) => (
                                    <TableConfigurationDialogColumnItem
                                        key={c.colId}
                                        c={c}
                                        onVisibilityChanged={onVisibilityChanged}
                                    />
                                ))}
                            </div>
                        )}
                        <DraggableContainer getOrder={onItemsReordered}>
                            {columnPanelColumnStates
                                .filter(shouldDisplayDraggableItem)
                                .map((c: ColumnPanelColumnState) => (
                                    <DraggableItem key={c.colId} id={c.colId}>
                                        <TableConfigurationDialogColumnItem
                                            c={c}
                                            onVisibilityChanged={onVisibilityChanged}
                                        />
                                    </DraggableItem>
                                ))}
                        </DraggableContainer>
                    </div>
                )}
            </div>
        </Sidebar>
    );
}

// Function that passing one full list, one filtered list and one moved item can return the new order of the list
export const getNewOrder = (
    columnState: ColumnPanelColumnState[],
    filteredColumnState: string[],
    movedItem: string,
): string[] => {
    const mappedToIdsColumnState = columnState.map(c => c.colId);
    const indexOfMovedItemInState = mappedToIdsColumnState.findIndex(c => c === movedItem);
    const indexOfMovedItemInFiltered = filteredColumnState.findIndex(c => c === movedItem);

    let newOrder;
    // If the element is moved down
    if (indexOfMovedItemInState < indexOfMovedItemInFiltered) {
        const elementBeforeMovedItem = filteredColumnState[indexOfMovedItemInFiltered - 1];
        const indexOfElementBeforeMovedItem = mappedToIdsColumnState.findIndex(c => c === elementBeforeMovedItem);
        newOrder = mappedToIdsColumnState.filter(c => c !== movedItem);
        newOrder.splice(indexOfElementBeforeMovedItem, 0, movedItem);
        return newOrder;
    }
    // If the element is moved up
    const elementAfterMovedItem = filteredColumnState[indexOfMovedItemInFiltered + 1];
    const indexOfElementAfterMovedItem = mappedToIdsColumnState.findIndex(c => c === elementAfterMovedItem);
    newOrder = mappedToIdsColumnState.filter(c => c !== movedItem);

    newOrder.splice(indexOfElementAfterMovedItem, 0, movedItem);

    return newOrder;
};
