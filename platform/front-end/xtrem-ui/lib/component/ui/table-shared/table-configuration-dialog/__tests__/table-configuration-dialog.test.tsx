import * as React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react';
import type { TableConfigurationDialogProps } from '../table-configuration-dialog';
import { getNewOrder, TableConfigurationDialog } from '../table-configuration-dialog';

describe('Table configuration dialog', () => {
    let props: TableConfigurationDialogProps;

    beforeEach(() => {
        props = {
            isDialogOpen: true,
            onOrderChange: jest.fn(),
            onDialogClose: jest.fn(),
            onChangeColumnHiddenState: jest.fn(),
            columnPanelColumnStates: [
                {
                    colId: 'column1',
                    title: 'Column 1',
                    isHidden: false,
                    isMandatory: false,
                },
                {
                    colId: 'column2',
                    title: 'Column 2',
                    isHidden: true,
                    isMandatory: false,
                },
                {
                    colId: 'column3',
                    title: 'Column 3',
                    isHidden: false,
                    isMandatory: true,
                },
                {
                    colId: 'column4',
                    title: 'Column 4',
                    isHidden: false,
                    isMandatory: true,
                },
                {
                    colId: 'column5',
                    title: 'Column 5',
                    isHidden: false,
                    isMandatory: true,
                },
                {
                    colId: 'column6',
                    title: 'Column 6',
                    isHidden: false,
                    isMandatory: true,
                },
                {
                    colId: 'column7',
                    title: 'Column 7',
                    isHidden: false,
                    isMandatory: true,
                },
                {
                    colId: 'column8',
                    title: 'Column 8',
                    isHidden: false,
                    isMandatory: true,
                },
            ],
        };
    });

    it('should render a draggable item for each column', () => {
        const { baseElement } = render(<TableConfigurationDialog {...props} />);
        const elements = baseElement.querySelectorAll('label');
        expect(elements[0]).toHaveTextContent('Column 1');
        expect(elements[1]).toHaveTextContent('Column 2');
        expect(elements[2]).toHaveTextContent('Column 3');
    });

    it('should notify the parent if the user hides a column', async () => {
        const { queryByTestId } = render(<TableConfigurationDialog {...props} />);
        const checkbox = queryByTestId('e-table-configuration-dialog-line-col-column3')!.querySelector(
            'input[type="checkbox"]',
        )!;
        expect(props.onChangeColumnHiddenState).not.toHaveBeenCalled();
        fireEvent.click(checkbox);
        await waitFor(() => {
            expect(props.onChangeColumnHiddenState).toHaveBeenCalledWith('column3', false);
        });
    });

    it('should notify the parent if the user unhides a column', async () => {
        const { queryByTestId } = render(<TableConfigurationDialog {...props} />);
        const checkbox = queryByTestId('e-table-configuration-dialog-line-col-column2')!.querySelector(
            'input[type="checkbox"]',
        )!;
        expect(props.onChangeColumnHiddenState).not.toHaveBeenCalled();
        fireEvent.click(checkbox);
        await waitFor(() => {
            expect(props.onChangeColumnHiddenState).toHaveBeenCalledWith('column2', true);
        });
    });

    it('should close the dialog if the user clicks outside of it', async () => {
        const { baseElement } = render(<TableConfigurationDialog {...props} />);
        expect(props.onDialogClose).not.toHaveBeenCalled();
        setTimeout(() => {
            fireEvent.click(baseElement);
        }, 1000);

        await waitFor(
            () => {
                expect(props.onDialogClose).toHaveBeenCalled();
            },
            { timeout: 2000 },
        );
    });

    it('Should return a ordered list after moving column 1 down to between column 4 and 5 using filters', () => {
        const filteredList = ['column4', 'column1', 'column5', 'column6'];
        const expectedList = ['column2', 'column3', 'column4', 'column1', 'column5', 'column6', 'column7', 'column8'];

        const result = getNewOrder(props.columnPanelColumnStates, filteredList, 'column1');
        expect(result).toEqual(expectedList);
    });

    it('Should return a ordered list after moving column 8 up to between column 4 and 5 using filters', () => {
        const filteredList = ['column1', 'column4', 'column8', 'column5', 'column6'];
        const expectedList = ['column1', 'column2', 'column3', 'column4', 'column8', 'column5', 'column6', 'column7'];

        const result = getNewOrder(props.columnPanelColumnStates, filteredList, 'column8');
        expect(result).toEqual(expectedList);
    });
    it('Should return a ordered list after moving column 8 to the first place of the list using filters', () => {
        const filteredList = ['column8', 'column1', 'column4', 'column5', 'column6'];
        const expectedList = ['column8', 'column1', 'column2', 'column3', 'column4', 'column5', 'column6', 'column7'];

        const result = getNewOrder(props.columnPanelColumnStates, filteredList, 'column8');
        expect(result).toEqual(expectedList);
    });
    it('Should return a ordered list after moving column 1 to the bottom of the list using filters', () => {
        const filteredList = ['column4', 'column5', 'column6', 'column8', 'column1'];
        const expectedList = ['column2', 'column3', 'column4', 'column5', 'column6', 'column7', 'column8', 'column1'];

        const result = getNewOrder(props.columnPanelColumnStates, filteredList, 'column1');
        expect(result).toEqual(expectedList);
    });
});
