import Icon from 'carbon-react/esm/components/icon';
import MultiActionButton from 'carbon-react/esm/components/multi-action-button';
import SplitButton from 'carbon-react/esm/components/split-button';
import React from 'react';
import { localize } from '../../../../service/i18n-service';
import type { ScreenBase } from '../../../../service/screen-base';
import { resolveByValue } from '../../../../utils/resolve-value-utils';
import type { ValueOrCallback } from '../../../../utils/types';
import { ConnectedBusinessAction } from '../../../container/footer/business-action';
import type { PageActionControlObject } from '../../../control-objects';

export interface TableCreateActionsProps {
    createActions: ValueOrCallback<ScreenBase, PageActionControlObject | PageActionControlObject[]> | undefined;
    multiActionType: 'multi-action-button' | 'split-button';
    screenId: string;
    elementId: string;
}

export const TableCreateActions: React.FC<TableCreateActionsProps> = React.memo<TableCreateActionsProps>(
    ({ createActions, multiActionType, screenId, elementId }) => {
        const resolvedActions = resolveByValue<PageActionControlObject | PageActionControlObject[]>({
            propertyValue: createActions,
            screenId,
            rowValue: null,
            fieldValue: null,
            skipHexFormat: true,
        });

        const isMultiCreateAction = (): boolean => {
            return Array.isArray(resolvedActions) && resolvedActions.length > 1;
        };

        const isSingleCreateAction = (): boolean => {
            return Array.isArray(resolvedActions) ? resolvedActions.length === 1 : !!resolvedActions;
        };

        const getSingleCreateAction = (): PageActionControlObject => {
            return Array.isArray(resolvedActions) ? resolvedActions[0] : resolvedActions;
        };

        /**
         * INFO: Carbon's SplitButton accepts an Icon component as it's text prop, but is not typed
         *       accordingly. This function is merely to trick TypeScript ;).
         */
        const getAddIconAsString = (): string => {
            return (<Icon type="add" />) as any as string;
        };

        if (isMultiCreateAction() && multiActionType === 'multi-action-button') {
            const actions = resolvedActions as PageActionControlObject[];
            return (
                <MultiActionButton
                    data-testid="e-create-multi-action-button"
                    mr="16px"
                    text={localize('@sage/xtrem-ui/table-create', 'Create')}
                >
                    {actions.map(action => (
                        <ConnectedBusinessAction
                            id={action.id}
                            defaultButtonType="tertiary"
                            isSeparated={false}
                            key={action.id}
                            screenId={screenId}
                            skipWrapping={true}
                            pendoId={`createAction-${screenId}-${elementId}-${action.id}`}
                        />
                    ))}
                </MultiActionButton>
            );
        }

        if (isMultiCreateAction() && multiActionType === 'split-button') {
            const actions = resolvedActions as PageActionControlObject[];
            return (
                <SplitButton
                    buttonType="secondary"
                    data-testid="e-create-split-button"
                    size="small"
                    text={getAddIconAsString()}
                >
                    {actions.map(action => (
                        <ConnectedBusinessAction
                            id={action.id}
                            defaultButtonType="tertiary"
                            isSeparated={false}
                            key={action.id}
                            screenId={screenId}
                            skipWrapping={true}
                            pendoId={`createSplitAction-${screenId}-${elementId}`}
                        />
                    ))}
                </SplitButton>
            );
        }

        if (isSingleCreateAction() && multiActionType === 'multi-action-button') {
            const action: PageActionControlObject = getSingleCreateAction();
            return (
                <ConnectedBusinessAction
                    id={action.id}
                    defaultButtonType="tertiary"
                    isSeparated={true}
                    key={action.id}
                    screenId={screenId}
                    pendoId={`createAction-${screenId}-${elementId}`}
                />
            );
        }

        // INFO: Create action when rendered in split navigation view.
        if (isSingleCreateAction() && multiActionType === 'split-button') {
            const action: PageActionControlObject = getSingleCreateAction();
            return (
                <ConnectedBusinessAction
                    id={action.id}
                    buttonTypeOverride="secondary"
                    isIconOnly={true}
                    isSeparated={true}
                    key={action.id}
                    screenId={screenId}
                    size="small"
                    pendoId={`createAction-${screenId}-${elementId}`}
                />
            );
        }

        return null;
    },
);

TableCreateActions.displayName = 'TableCreateActions';
