import type { ResolveByValueParams } from '../../../../../utils/resolve-value-utils';
import type { PageActionControlObject } from '../../../../control-objects';
import type { TableCreateActionsProps } from '../table-create-actions';

jest.mock('../../../../../utils/resolve-value-utils', () => ({
    resolveByValue: jest.fn().mockImplementation((params: ResolveByValueParams) => {
        return params.propertyValue;
    }),
}));

jest.mock('../../../../container/footer/business-action', () => ({
    ConnectedBusinessAction: () => <div data-testid="e-business-action" />,
}));

import { render } from '@testing-library/react';
import React from 'react';
import { TableCreateActions } from '../table-create-actions';
import '@testing-library/jest-dom';

describe('TableCreateActions', () => {
    const SCREEN_ID = 'TableCreateActionsTest';
    let props: TableCreateActionsProps;

    it('should render null given undefined create actions', () => {
        props = {
            createActions: undefined,
            multiActionType: 'multi-action-button',
            screenId: SCREEN_ID,
            elementId: 'test',
        };
        const wrapper = render(<TableCreateActions {...props} />);
        expect(wrapper.container).toBeEmptyDOMElement();
    });

    it('should render null given empty create actions', () => {
        props = {
            createActions: [],
            multiActionType: 'multi-action-button',
            screenId: SCREEN_ID,
            elementId: 'test',
        };
        const wrapper = render(<TableCreateActions {...props} />);
        expect(wrapper.container).toBeEmptyDOMElement();
    });

    it('should render create button given create actions', () => {
        props = {
            createActions: { id: 'create-action-1', title: 'Action #1' } as PageActionControlObject,
            multiActionType: 'multi-action-button',
            screenId: SCREEN_ID,
            elementId: 'test',
        };
        const wrapper = render(<TableCreateActions {...props} />);
        expect(wrapper.queryByTestId('e-business-action')).toBeInTheDocument();
    });

    describe('given multi action type of "multi-create-button"', () => {
        beforeEach(() => {
            props = {
                createActions: [
                    { id: 'create-action-1', title: 'Action #1' },
                    { id: 'create-action-2', title: 'Action #2' },
                    { id: 'create-action-3', title: 'Action #3' },
                ] as PageActionControlObject[],
                multiActionType: 'multi-action-button',
                screenId: SCREEN_ID,
                elementId: 'test',
            };
        });

        it('should render multi-action button given multiple create actions', () => {
            const wrapper = render(<TableCreateActions {...props} />);
            expect(wrapper.queryByTestId('e-create-multi-action-button')).toBeInTheDocument();
        });
    });

    describe('given multi action type of "split-button"', () => {
        beforeEach(() => {
            props = {
                createActions: [
                    { id: 'create-action-1', title: 'Action #1' },
                    { id: 'create-action-2', title: 'Action #2' },
                    { id: 'create-action-3', title: 'Action #3' },
                ] as PageActionControlObject[],
                multiActionType: 'split-button',
                screenId: SCREEN_ID,
                elementId: 'test',
            };
        });

        it('should render split button given multiple create actions', () => {
            const wrapper = render(<TableCreateActions {...props} />);
            expect(wrapper.queryByTestId('e-create-split-button')).toBeInTheDocument();
        });
    });
});
