import * as React from 'react';
import { localize } from '../../../service/i18n-service';
import Button from 'carbon-react/esm/components/button';
import * as xtremRedux from '../../../redux';
import SplitButton from 'carbon-react/esm/components/split-button';
import type { PageActionControlObject } from '../../page-action/page-action-control-object';
import BusinessAction from '../../container/footer/business-action';
import type { SidebarDefinitionDecorator } from '../../table-sidebar/table-sidebar-types';
import type { CardDefinition } from '../card/card-component';
import { useDispatch } from 'react-redux';
import type { NestedField } from '../../nested-fields';
import MultiActionButton from 'carbon-react/esm/components/multi-action-button';

export interface DesktopTableAddNewRowButtonProps {
    addItemActions?: PageActionControlObject[];
    canAddNewLine: boolean;
    cardDefinition?: CardDefinition;
    columns: Array<NestedField<any, any>>;
    elementId: string;
    hasSidebar?: boolean;
    isDisabled?: boolean;
    level?: number;
    onFocusPhantomRow?: () => void;
    screenId: string;
    sidebarDefinition?: SidebarDefinitionDecorator;
}
export const DesktopTableAddNewRowButton = React.memo<DesktopTableAddNewRowButtonProps>(
    ({
        addItemActions = [],
        canAddNewLine,
        cardDefinition,
        columns,
        elementId,
        hasSidebar,
        isDisabled,
        level,
        onFocusPhantomRow,
        screenId,
        sidebarDefinition,
    }: DesktopTableAddNewRowButtonProps) => {
        const dispatch = useDispatch();

        const visibleAddItemActions = React.useMemo(
            () => addItemActions.filter(action => !action.isHidden),
            [addItemActions],
        );

        const onOpenSidebarClick = React.useCallback(() => {
            dispatch(
                xtremRedux.actions.openTableSidebar({
                    cardDefinition,
                    columns,
                    elementId,
                    level,
                    screenId,
                    sidebarDefinition,
                }),
            );
        }, [cardDefinition, columns, dispatch, elementId, level, screenId, sidebarDefinition]);

        const addItemPhantomRowLabel = localize('@sage/xtrem-ui/add-item-in-line', 'Add item');
        const addItemUsingSidebarLabel = localize(
            '@sage/xtrem-ui/add-item-in-line-using-sidebar',
            'Add item in sidebar',
        );

        if (hasSidebar || visibleAddItemActions.length > 0) {
            const businessActions = [
                ...(hasSidebar
                    ? [
                          // eslint-disable-next-line react/jsx-indent
                          <Button
                              key="e-table-button-add-new-row-sidebar"
                              data-testid="e-table-button-add-new-row-sidebar"
                              disabled={isDisabled}
                              onClick={onOpenSidebarClick}
                              aria-label={addItemUsingSidebarLabel}
                              data-pendoid={`addTableLineWithSidebarButton-${screenId}-${elementId}`}
                          >
                              {addItemUsingSidebarLabel}
                          </Button>,
                      ]
                    : []),
                ...visibleAddItemActions.map(a => (
                    <BusinessAction
                        key={a.id}
                        screenId={screenId}
                        id={a.id}
                        buttonTypeOverride="secondary"
                        skipWrapping={true}
                        alwaysTriggerFieldEvent={true}
                    />
                )),
            ];
            return !canAddNewLine ? (
                <MultiActionButton
                    data-testid="e-table-button-add-new-row-multi-action"
                    text={addItemPhantomRowLabel}
                    disabled={isDisabled}
                    mr="16px"
                    data-pendoid={`addTableLineWithMultiActionButton-${elementId}`}
                >
                    {businessActions}
                </MultiActionButton>
            ) : (
                <SplitButton
                    data-testid="e-table-button-add-new-row-phantom"
                    text={addItemPhantomRowLabel}
                    iconType="add"
                    onClick={onFocusPhantomRow}
                    disabled={isDisabled}
                    mr="16px"
                    data-pendoid={`addTableLineWithPhantomRowButton-${elementId}`}
                >
                    {businessActions}
                </SplitButton>
            );
        }

        return (
            <Button
                data-testid="e-table-button-add-new-row-phantom"
                disabled={isDisabled}
                iconType="add"
                onClick={onFocusPhantomRow}
                aria-label={addItemPhantomRowLabel}
                mr="16px"
            >
                {addItemPhantomRowLabel}
            </Button>
        );
    },
);

DesktopTableAddNewRowButton.displayName = 'DesktopTableAddNewRowButton';
