import Button from 'carbon-react/esm/components/button';
import MultiActionButton from 'carbon-react/esm/components/multi-action-button';
import * as React from 'react';
import { localize } from '../../../service/i18n-service';
import type * as xtremRedux from '../../../redux';
import type { PrintingSettings } from '../../../redux/state';
import * as dialogService from '../../../service/dialog-service';
import {
    QUERY_PARAM_PRINTING_NODE_TYPE,
    QUERY_PARAM_PRINTING_SOURCE_PAGE,
    QUERY_PARAM_PRINTING_SOURCE_TYPE,
} from '../../../utils/constants';
import { navigationPanelId } from '../../container/navigation-panel/navigation-panel-types';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';
import { getPagePropertiesFromState } from '../../../utils/state-utils';

export interface DesktopTablePrintingButtonProps {
    screenId: string;
    elementId: string;
}

export function DesktopTablePrintButton({
    screenId,
    elementId,
}: DesktopTablePrintingButtonProps): React.ReactElement | null {
    const printingSettings = useDeepEqualSelector<xtremRedux.XtremAppState, PrintingSettings | null>(
        state => state.printingSettings,
    );

    const pageNode = useDeepEqualSelector<xtremRedux.XtremAppState, string | null>(
        state => getPagePropertiesFromState(screenId, state)?.node?.toString() || null,
    );

    const openAssignmentDialog = React.useCallback(() => {
        if (!printingSettings?.printingAssignmentDialogUrl) {
            throw new Error('listPrintingAssignmentUrl is not defined');
        }
        dialogService.openPageDialog(
            printingSettings.printingAssignmentDialogUrl,
            {
                [QUERY_PARAM_PRINTING_SOURCE_PAGE]: screenId,
                [QUERY_PARAM_PRINTING_SOURCE_TYPE]: 'list',
                [QUERY_PARAM_PRINTING_NODE_TYPE]: pageNode || '',
            },
            { resolveOnCancel: true, size: 'large' },
        );
    }, [pageNode, printingSettings?.printingAssignmentDialogUrl, screenId]);

    const openPrintDialog = React.useCallback(() => {
        if (!printingSettings?.listPrintingWizardUrl) {
            throw new Error('listPrintingAssignmentDialogUrl is not defined');
        }
        dialogService.openPageDialog(
            printingSettings.listPrintingWizardUrl,
            {
                [QUERY_PARAM_PRINTING_SOURCE_PAGE]: screenId,
                [QUERY_PARAM_PRINTING_NODE_TYPE]: pageNode || '',
                [QUERY_PARAM_PRINTING_SOURCE_TYPE]: 'list',
            },
            { resolveOnCancel: true, size: 'medium' },
        );
    }, [pageNode, printingSettings?.listPrintingWizardUrl, screenId]);

    if (
        elementId !== navigationPanelId ||
        !printingSettings ||
        (!printingSettings.canAccessPrintingAssignmentDialog && !printingSettings.canAccessListPrintingWizard)
    ) {
        return null;
    }

    return (
        <MultiActionButton
            text={localize('@sage/xtrem-ui/table-print', 'Print')}
            data-testid="e-print-main-list-button"
            mr="16px"
        >
            <Button
                key="assignment"
                disabled={!printingSettings.canAccessPrintingAssignmentDialog}
                onClick={openAssignmentDialog}
                data-testid="e-print-list-assignment-button"
            >
                {localize('@sage/xtrem-ui/list-printing-assignment', 'Assign report')}
            </Button>
            <Button
                key="print"
                disabled={!printingSettings.canAccessListPrintingWizard}
                onClick={openPrintDialog}
                data-testid="e-print-list-wizard-button"
            >
                {localize('@sage/xtrem-ui/list-printing', 'Select report')}
            </Button>
        </MultiActionButton>
    );
}
