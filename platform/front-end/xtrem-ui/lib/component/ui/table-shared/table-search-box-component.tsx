import React from 'react';
import * as wrapperService from '../../../service/wrapper-service';
import { getFieldTitle } from '../../field/carbon-helpers';
import { localize } from '../../../service/i18n-service';
import { Icon } from '../icon/icon-component';
import IconButton from 'carbon-react/esm/components/icon-button';
import type { InternalNestedGridProperties } from '../../field/field-control-objects';
import type { InternalTableProperties } from '../../field/table/table-component-types';
import Textbox from 'carbon-react/esm/components/textbox';
import { debounce } from 'lodash';

export const getSearchTextboxId = (screenId?: string, elementId?: string): string | undefined =>
    screenId && elementId ? `e-table-field-mobile-search-input-${screenId}-${elementId}`.replace('$', '') : undefined;

export interface TableSearchBoxProps {
    disableAutoFocus?: boolean;
    elementId?: string;
    fieldProperties?: InternalTableProperties | InternalNestedGridProperties;
    filterText?: string;
    isDisabled?: boolean;
    onSearchBoxValueChange: (searchText: string) => void;
    screenId?: string;
}

export function TableSearchBox({
    disableAutoFocus,
    elementId,
    fieldProperties,
    filterText = '',
    isDisabled,
    onSearchBoxValueChange,
    screenId,
}: TableSearchBoxProps): React.ReactElement {
    const [searchText, setSearchText] = React.useState(filterText);
    const inputRef = React.useRef<HTMLInputElement>(null);

    React.useEffect(() => {
        setSearchText(filterText);
    }, [filterText]);

    const debouncedSearch = React.useMemo(
        () =>
            debounce((filter: string) => {
                onSearchBoxValueChange(filter);
            }, 500),
        [onSearchBoxValueChange],
    );

    // Debounce the actual filter value update so we don't refetch on every key stroke
    const debouncedTextFilterUpdate = React.useCallback(debouncedSearch, [onSearchBoxValueChange, debouncedSearch]);

    const onSearchTextChanged = React.useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            setSearchText(event.target.value);
            debouncedTextFilterUpdate(event.target.value);
        },
        [setSearchText, debouncedTextFilterUpdate],
    );

    const onSearchTextFocused = React.useCallback(
        (event: React.FocusEvent<HTMLInputElement>): void => {
            if (fieldProperties && elementId && screenId) {
                wrapperService.onFocus(
                    {
                        screenId,
                        elementId,
                        title: getFieldTitle(screenId, fieldProperties, null),
                    },
                    (updatedSearchText: string) => {
                        setSearchText(updatedSearchText);
                        onSearchBoxValueChange(updatedSearchText);
                    },
                );

                if (event.target) {
                    // Sets selection to the end of the current input.
                    setTimeout(() => {
                        event.target.setSelectionRange(event.target.value.length, event.target.value.length);
                    }, 10);
                }
            }
        },
        [setSearchText, onSearchBoxValueChange, elementId, screenId, fieldProperties],
    );

    const onClearButtonClicked = React.useCallback((): void => {
        setSearchText('');
        onSearchBoxValueChange('');
        if (inputRef?.current) {
            inputRef.current.focus();
        }
    }, [setSearchText, onSearchBoxValueChange, inputRef]);

    const onBlur = React.useCallback((): void => {
        if (screenId && elementId) {
            wrapperService.onBlur({
                screenId,
                elementId,
            });
        }
    }, [screenId, elementId]);

    return (
        <div className="e-table-field-mobile-search" data-testid="e-table-field-mobile-search">
            <Textbox
                id={getSearchTextboxId(screenId, elementId)}
                ref={inputRef}
                autoFocus={!disableAutoFocus}
                onKeyDown={(e: React.KeyboardEvent): void => {
                    // On hitting the enter key the component reloads the page for some reason
                    if (e.key === 'Enter') {
                        e.preventDefault();
                    }
                }}
                onChange={onSearchTextChanged}
                onFocus={onSearchTextFocused}
                placeholder={localize('@sage/xtrem-ui/reference-lookup-dialog-search-placeholder', 'Search...')}
                aria-label={localize('@sage/xtrem-ui/reference-lookup-dialog-search-placeholder', 'Search...')}
                leftChildren={<Icon type="search" bgSize="large" />}
                value={searchText}
                disabled={isDisabled}
                onBlur={onBlur}
            >
                {!!searchText && (
                    <IconButton
                        onClick={onClearButtonClicked}
                        aria-label={localize('@sage/xtrem-ui/clear-filter-text', 'Clear filter text')}
                    >
                        <Icon type="cross" />
                    </IconButton>
                )}
            </Textbox>
        </div>
    );
}
