import * as React from 'react';
import * as graphqlService from '../../../../../service/graphql-service';
import { render, waitFor, fireEvent } from '@testing-library/react';
import type { ReferenceFilterProps } from '../reference/reference-filter';
import ReferenceFilter from '../reference/reference-filter';
import '@testing-library/jest-dom';

describe('Reference filter', () => {
    let fetchSuggestionsMock: jest.MockInstance<any, any>;
    let props: ReferenceFilterProps;

    const onModelChange = jest.fn().mockImplementation(newValue => {
        props.model = newValue;
    });

    beforeEach(() => {
        fetchSuggestionsMock = jest.spyOn(graphqlService, 'fetchReferenceFieldSuggestions').mockResolvedValue([
            {
                _id: '1',
                name: 'First Item',
                __cursor: '1',
            },
            {
                _id: '2',
                name: 'Second Item',
                __cursor: '2',
            },
            {
                _id: '3',
                name: 'Third Item',
                __cursor: '3',
            },
        ] as any);

        props = {
            model: null,
            onModelChange,
            colDef: {
                context: {
                    screenId: 'TestPage',
                    columnId: 'testColumnId',
                    isEditable: () => true,
                },
                field: 'testColumnId',
                cellRendererParams: {
                    tableElementId: 'testTableElementId',
                    columnId: 'testColumnId',
                    contextNode: '@sage/xtrem-test/TestNode',
                    fieldProperties: {
                        bind: 'testColumnId',
                        valueField: 'name',
                        node: '@sage/xtrem-test/AnotherTestNode',
                    },
                },
            },
        };
    });

    afterEach(() => {
        fetchSuggestionsMock.mockClear();
    });

    it('should fetch suggestions on rendering', async () => {
        expect(fetchSuggestionsMock).not.toHaveBeenCalled();
        render(<ReferenceFilter {...props} />);
        await waitFor(() => {
            expect(fetchSuggestionsMock).toHaveBeenCalledWith({
                contextNode: '@sage/xtrem-test/TestNode',
                fieldId: 'testColumnId',
                fieldProperties: {
                    bind: 'testColumnId',
                    valueField: 'name',
                    node: '@sage/xtrem-test/AnotherTestNode',
                    isTransient: true,
                    minLookupCharacters: 0,
                    orderBy: {
                        name: 1,
                    },
                },
                parentElementId: 'testTableElementId',
                recordContext: {},
                screenId: 'TestPage',
            });
        });
    });

    it('should render the returned elements from the server', async () => {
        const wrapper = render(<ReferenceFilter {...props} />);
        await waitFor(() => {
            expect(wrapper.queryAllByTestId('e-reference-custom-filter-item')).toHaveLength(3);
        });

        const listItems = wrapper.queryAllByTestId('e-reference-custom-filter-item');

        expect(listItems[0]).toHaveTextContent('First Item');
        expect(listItems[1]).toHaveTextContent('Second Item');
        expect(listItems[2]).toHaveTextContent('Third Item');
    });

    it('should replace the existing items on the screen when the user filters the options', async () => {
        const wrapper = render(<ReferenceFilter {...props} />);
        await waitFor(() => {
            expect(wrapper.queryAllByTestId('e-reference-custom-filter-item')).toHaveLength(3);
        });

        fetchSuggestionsMock!.mockResolvedValue([
            {
                _id: '4',
                name: 'Fourth Item',
                __cursor: '4',
            },
        ]);

        fireEvent.change(wrapper.queryByTestId('e-reference-custom-filter-input')!, { target: { value: 'Fourth' } });

        await waitFor(() => {
            expect(fetchSuggestionsMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    filterValue: 'Fourth',
                }),
            );
        });

        await waitFor(() => {
            expect(wrapper.queryAllByTestId('e-reference-custom-filter-item')).toHaveLength(1);
        });

        const listItems = wrapper.queryAllByTestId('e-reference-custom-filter-item');

        expect(listItems[0]).toHaveTextContent('Fourth Item');
    });

    describe('clear button', () => {
        it('should disable the button if no items are selected', async () => {
            const wrapper = render(<ReferenceFilter {...props} />);
            await waitFor(() => {
                expect(wrapper.queryAllByTestId('e-reference-custom-filter-item')).toHaveLength(3);
            });

            await waitFor(() => {
                expect(
                    wrapper.baseElement
                        .querySelector('.e-reference-custom-filter-clear-container button')!
                        .hasAttribute('disabled'),
                ).toEqual(true);
            });
        });

        it('should enable the button any item is selected', async () => {
            const wrapper = render(<ReferenceFilter {...props} />);
            await waitFor(() => {
                expect(wrapper.queryAllByTestId('e-reference-custom-filter-item')).toHaveLength(3);
            });

            fireEvent.click(
                wrapper.queryByTestId('e-reference-custom-filter-checkbox-label-secondItem', { exact: false })!,
            );

            await waitFor(() => {
                expect(onModelChange).toHaveBeenLastCalledWith({
                    filterType: 'set',
                    values: ['2'],
                    filterById: true,
                    displayValues: ['Second Item'],
                });
            });
            wrapper.rerender(<ReferenceFilter {...props} />);
            await waitFor(() => {
                expect(
                    wrapper.baseElement
                        .querySelector('.e-reference-custom-filter-clear-container button')!
                        .hasAttribute('disabled'),
                ).toEqual(false);
            });
        });

        it('should should unselect all items', async () => {
            const wrapper = render(<ReferenceFilter {...props} />);
            await waitFor(() => {
                expect(wrapper.queryAllByTestId('e-reference-custom-filter-item')).toHaveLength(3);
            });

            fireEvent.click(
                wrapper.queryByTestId('e-reference-custom-filter-checkbox-label-secondItem', { exact: false })!,
            );
            await waitFor(() => {
                expect(onModelChange).toHaveBeenLastCalledWith({
                    filterType: 'set',
                    values: ['2'],
                    filterById: true,
                    displayValues: ['Second Item'],
                });
            });
            wrapper.rerender(<ReferenceFilter {...props} />);

            fireEvent.click(
                wrapper.queryByTestId('e-reference-custom-filter-checkbox-label-thirdItem', { exact: false })!,
            );

            await waitFor(() => {
                expect(onModelChange).toHaveBeenLastCalledWith({
                    displayValues: ['Second Item', 'Third Item'],
                    filterById: true,
                    filterType: 'set',
                    values: ['2', '3'],
                });
            });
            wrapper.rerender(<ReferenceFilter {...props} />);
            await waitFor(() => {
                expect(
                    (
                        wrapper.queryByTestId('e-reference-custom-filter-checkbox-label-thirdItem', {
                            exact: false,
                        }) as HTMLInputElement
                    ).checked,
                ).toBe(true);
                expect(
                    (
                        wrapper.queryByTestId('e-reference-custom-filter-checkbox-label-secondItem', {
                            exact: false,
                        }) as HTMLInputElement
                    ).checked,
                ).toBe(true);
            });

            fireEvent.click(wrapper.baseElement.querySelector('.e-reference-custom-filter-clear-container button')!);
            await waitFor(() => {
                expect(onModelChange).toHaveBeenLastCalledWith(null);
            });
            wrapper.rerender(<ReferenceFilter {...props} />);

            await waitFor(() => {
                expect(
                    (
                        wrapper.queryByTestId('e-reference-custom-filter-checkbox-label-thirdItem', {
                            exact: false,
                        }) as HTMLInputElement
                    ).checked,
                ).toBe(false);
                expect(
                    (
                        wrapper.queryByTestId('e-reference-custom-filter-checkbox-label-secondItem', {
                            exact: false,
                        }) as HTMLInputElement
                    ).checked,
                ).toBe(false);
            });
        });
    });
});
