.e-reference-custom-filter {
    height: 164px;

    .e-reference-custom-filter-clear-container {
        padding: 0 8px;
    }

    .e-reference-custom-filter-body {
        padding: 0 4px;
        max-height: 106px;
        overflow-y: auto;

        .e-reference-custom-filter-item {
            display: flex;
            align-items: center;
            padding: 4px;

            [data-component="checkbox"] {
                height: 16px;
                margin-bottom: 0;
            }

            .e-reference-custom-filter-item-value {
                padding-left: 4px;
                font-family: var(--fontFamiliesDefault);
            }

            .e-reference-custom-filter-item-value-highlighted {
                font-weight: var(--fontWeights500);
            }
        }
    }
}