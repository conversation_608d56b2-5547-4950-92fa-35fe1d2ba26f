import { Checkbox } from 'carbon-react/esm/components/checkbox';
import CarbonIcon from 'carbon-react/esm/components/icon';
import React from 'react';
import styled from 'styled-components';
import { localize } from '../../../../../service/i18n-service';
import { FieldKey, SET } from '../../../../types';

const Container = styled.div`
    padding: 16px;
    display: grid;
    grid-template-columns: 1fr 5fr;
    grid-template-rows: repeat(2, 1fr);
    grid-column-gap: 0px;
    grid-row-gap: 16px;
    align-items: center;
`;

const Icon = styled(CarbonIcon)`
    &:before {
        font-weight: bold;
        font-size: var(--sizing200);
        line-height: var(--sizing200);
    }
`;

export function BooleanFilter({
    controlObjectType,
    model,
    onModelChange,
}: {
    controlObjectType: FieldKey.Checkbox | FieldKey.Switch;
    model: { filterType: typeof SET; values: [boolean] | [] } | null;
    onModelChange: (model: { filterType: typeof SET; values: [boolean] | [] } | null) => void;
}): React.ReactNode {
    const isTrueChecked = React.useMemo(
        () => (model?.values ?? []).length === 1 && model?.values?.[0] === true,
        [model],
    );

    const isFalseChecked = React.useMemo(
        () => (model?.values ?? []).length === 1 && model?.values?.[0] === false,
        [model],
    );

    const isSwitch = React.useMemo(() => controlObjectType === FieldKey.Switch, [controlObjectType]);

    const onTrueChange = React.useCallback((): void => {
        onModelChange(isTrueChecked ? null : { filterType: SET, values: [true] });
    }, [onModelChange, isTrueChecked]);

    const onFalseChange = React.useCallback((): void => {
        onModelChange(isFalseChecked ? null : { filterType: SET, values: [false] });
    }, [isFalseChecked, onModelChange]);

    return (
        <Container className="e-boolean-custom-filter">
            <Checkbox
                checked={isTrueChecked}
                className="e-boolean-custom-filter-checkbox-true"
                onChange={onTrueChange}
            />
            {isSwitch ? <span>{localize('@sage/xtrem-ui/switch-on-caps', 'ON')}</span> : <Icon type="tick" />}
            <Checkbox
                checked={isFalseChecked}
                className="e-boolean-custom-filter-checkbox-false"
                onChange={onFalseChange}
            />
            {isSwitch ? <span>{localize('@sage/xtrem-ui/switch-off-caps', 'OFF')}</span> : <Icon type="close" />}
        </Container>
    );
}
