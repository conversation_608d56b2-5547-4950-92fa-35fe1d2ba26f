import { RANGE } from '@sage/xtrem-shared';
import React, { useEffect, useMemo, useRef } from 'react';
import { includes } from 'lodash';
import type { SelectItem } from '../../../select/select-component';
import type { DateFilterProps, DateFilterState } from './date-filter-types';
import { getModelFromDateState, getDateStateFromModel, dateRanges } from './date-filter-utils';
import { DateFilterSelect } from './date-filter-select';
import { isValidIsoDate, Time } from '@sage/xtrem-date-time';
import { DatetimeInputComponent } from '../../../datetime/datetime-input-component';
import { makeDatetime } from '../../../datetime/datetime-utils';
import { localize } from '../../../../../service/i18n-service';
import type { CalendarDate } from '@internationalized/date';

export default function DatetimeFilter({ model, onModelChange, locale }: DateFilterProps): React.ReactNode {
    const dateTimeRef = useRef<HTMLInputElement>(null);
    const dateTimeToRef = useRef<HTMLInputElement>(null);
    const [dateState, setDateState] = React.useState<DateFilterState>(() => getDateStateFromModel(model));
    const [time, setTime] = React.useState<string | null>(dateState.formattedValue || null);
    const [timeTo, setTimeTo] = React.useState<string | null>(dateState.formattedValueTo || null);
    const [date, setDate] = React.useState<CalendarDate | null>(null);
    const [dateTo, setDateTo] = React.useState<CalendarDate | null>(null);
    const [isPopoverOpen, setIsPopoverOpen] = React.useState<boolean>(false);
    const [isPopoverToOpen, setIsPopoverToOpen] = React.useState<boolean>(false);

    const isPredefinedRange = useMemo(() => includes(dateRanges, dateState.type), [dateState.type]);
    const isRange = useMemo(() => dateState.type === RANGE || isPredefinedRange, [dateState.type, isPredefinedRange]);

    const onChange = React.useCallback(
        (newValue: Partial<DateFilterState>) => {
            if (newValue.rawValue?.toString() === dateState.rawValue?.toString()) {
                return;
            }
            setDateState(current => ({ ...current, ...newValue }));
            const values = Object.values(newValue ?? {});
            if (values.filter(Boolean).length === 0) {
                onModelChange(null);
            }
            if (
                values.length > 0 &&
                values.some(v => isValidIsoDate(v) || (newValue.type && includes(dateRanges, newValue.type)))
            ) {
                onModelChange(
                    getModelFromDateState({ dateState: { ...dateState, ...newValue }, locale, isDatetime: true }),
                );
            }
        },
        [dateState, onModelChange, locale],
    );

    useEffect(() => {
        if (time && date && !isRange) {
            const newValue = makeDatetime(date, Time.parse(time));
            onChange({
                rawValue: newValue.toString(),
            });
        } else if (isRange && time && date && timeTo && dateTo) {
            const newValue = makeDatetime(date, Time.parse(time));
            const newValueTo = makeDatetime(dateTo, Time.parse(timeTo));
            onChange({
                rawValue: newValue.toString(),
                rawValueTo: newValueTo.toString(),
            });
        } else if ((!time || !date) && dateState.rawValue) {
            onChange({ rawValue: '' });
        }
    }, [time, date, onChange, timeTo, dateTo, isRange, dateState.rawValue]);

    const handleDateChange = (date: CalendarDate | null): void => {
        setDate(date);
    };

    const handleTimeChange = (time: string | null): void => {
        setTime(time);
    };

    const handleDateToChange = (date: CalendarDate | null): void => {
        setDateTo(date);
    };

    const handleTimeToChange = (time: string | null): void => {
        setTimeTo(time);
    };

    const onFilterTypeChanged = React.useCallback(
        (item: SelectItem): void => {
            const newValue = item.value as DateFilterState['type'];
            setDateState(current => ({ ...current, type: newValue }));
            onChange({ type: newValue });
            setTime(null);
            setTimeTo(null);
            setDate(null);
            setDateTo(null);
        },
        [onChange],
    );

    return (
        <div className="e-filter-date-container">
            <DateFilterSelect onChange={onFilterTypeChanged} value={dateState.type} />
            <div className="e-date-input-wrapper" ref={dateTimeRef}>
                <DatetimeInputComponent
                    aria-label={localize('@sage/xtrem-ui/date-time-component-aria-label', 'Select date and time')}
                    inputRef={dateTimeRef}
                    date={date}
                    time={time}
                    locale={locale}
                    onDateChange={handleDateChange}
                    onTimeChange={handleTimeChange}
                    isReadOnly={isPredefinedRange}
                    size="small"
                    isPopoverOpen={isPopoverOpen}
                    onPopperOpenChange={isOpen => setIsPopoverOpen(isOpen)}
                    rangeStartDate={null}
                    type="single"
                />
            </div>

            {isRange && (
                <div className="e-date-input-wrapper" ref={dateTimeToRef}>
                    <DatetimeInputComponent
                        aria-label={localize('@sage/xtrem-ui/date-time-component-aria-label', 'Select date and time')}
                        inputRef={dateTimeToRef}
                        date={dateTo}
                        time={timeTo}
                        locale={locale}
                        onDateChange={handleDateToChange}
                        onTimeChange={handleTimeToChange}
                        isReadOnly={isPredefinedRange}
                        size="small"
                        isPopoverOpen={isPopoverToOpen}
                        onPopperOpenChange={isOpen => setIsPopoverToOpen(isOpen)}
                        rangeStartDate={null}
                        type="single"
                    />
                </div>
            )}
        </div>
    );
}
