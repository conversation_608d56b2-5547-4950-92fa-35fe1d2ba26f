import React from 'react';
import {
    EQUALS,
    GREATER_THAN,
    GREATER_THAN_EQUAL,
    LESS_THAN,
    LESS_THAN_EQUAL,
    NOT_EQUALS,
    RANGE,
} from '../../../../types';
import { isEqual, isNil } from 'lodash';
import { localize } from '../../../../../service/i18n-service';
import { useDebouncedCallback } from 'use-debounce';
import { onNumericInputKeyDown } from '../../../../field/numeric/numeric-cell-utils';
import type { AgGridColumnConfigWithScreenIdAndColDef } from '../../../../../utils/ag-grid/ag-grid-utility-types';
import { formatNumericValue, parseLocalizedNumberStringToNumber } from '../../../../../utils/formatters';
import { isEnter, isEsc } from '../../../../../utils/keyboard-event-utils';
import type { NumericFilterModel, NumericFloatingFilterProps } from './numeric-filter-types';
import type { ValueOrCallbackWithFieldValue } from '../../../../../utils/types';
import type { ScreenBase } from '../../../../../service/screen-base';

export default function NumericFloatingFilter({
    model,
    onModelChange,
    column,
}: NumericFloatingFilterProps): React.ReactNode {
    const screenId = React.useMemo(
        () => (column.getColDef() as AgGridColumnConfigWithScreenIdAndColDef).context.screenId,
        [column],
    );
    const scale: ValueOrCallbackWithFieldValue<ScreenBase, number, string> = React.useMemo(
        () => column.getColDef().cellEditorParams?.fieldProperties?.scale,
        [column],
    );
    const isDisabled = React.useMemo(() => !!model && model.type !== EQUALS, [model]);

    const [inputValue, setInputValue] = React.useState<string>('');

    const setValueFromModel = React.useCallback(() => {
        if (isNil(model?.filter)) {
            setInputValue('');
            return;
        }

        const formattedNumber = formatNumericValue({
            screenId,
            value: model.filter,
            ignoreLocale: false,
            rowValue: null,
            scale,
        });

        switch (model?.type) {
            case EQUALS:
                setInputValue(formattedNumber);
                break;
            case NOT_EQUALS:
                setInputValue(
                    localize('@sage/xtrem-ui/table-numeric-filter-not-value', 'Not {{numericValue}}', {
                        numericValue: formattedNumber,
                    }),
                );
                break;
            case GREATER_THAN:
                setInputValue(
                    localize('@sage/xtrem-ui/table-numeric-filter-greater-than-value', '> {{numericValue}}', {
                        numericValue: formattedNumber,
                    }),
                );
                break;
            case GREATER_THAN_EQUAL:
                setInputValue(
                    localize('@sage/xtrem-ui/table-numeric-filter-greater-than-equals-value', '>= {{numericValue}}', {
                        numericValue: formattedNumber,
                    }),
                );
                break;
            case LESS_THAN:
                setInputValue(
                    localize('@sage/xtrem-ui/table-numeric-filter-less-than-value', '< {{numericValue}}', {
                        numericValue: formattedNumber,
                    }),
                );
                break;
            case LESS_THAN_EQUAL:
                setInputValue(
                    localize('@sage/xtrem-ui/table-numeric-filter-less-than-equals-value', '<= {{numericValue}}', {
                        numericValue: formattedNumber,
                    }),
                );
                break;
            case RANGE:
                const formattedNumberTo = formatNumericValue({
                    screenId,
                    value: model.filterTo,
                    ignoreLocale: false,
                    rowValue: null,
                    scale,
                });
                setInputValue(
                    localize(
                        '@sage/xtrem-ui/table-numeric-filter-range-value',
                        '{{numericValue}} - {{numericValueTo}}',
                        {
                            numericValue: formattedNumber,
                            numericValueTo: formattedNumberTo,
                        },
                    ),
                );
                break;
            default:
            // Intentionally left empty
        }
    }, [model, scale, screenId]);

    const setModelFromValue = React.useCallback((): void => {
        if (isDisabled) {
            return;
        }

        if (isNil(inputValue)) {
            if (model) {
                onModelChange(null);
            }
            return;
        }

        const updatedModel: NumericFilterModel = {
            filterType: 'number',
            type: EQUALS,
            filter: parseLocalizedNumberStringToNumber(
                inputValue,
                localize('@sage/xtrem-ui/number-format-separator', '.'),
            ),
        };

        if (isEqual(model, updatedModel)) {
            return;
        }

        onModelChange(updatedModel);
    }, [inputValue, isDisabled, model, onModelChange]);

    const debouncedSetModelFromValue = useDebouncedCallback(() => {
        setModelFromValue();
    }, 1000);

    const onKeyDown = React.useCallback(
        (event: React.KeyboardEvent<HTMLInputElement>): void => {
            if (isEsc(event)) {
                event.preventDefault();
                setValueFromModel();
                return;
            }

            if (isEnter(event)) {
                event.preventDefault();
                setModelFromValue();
                return;
            }

            onNumericInputKeyDown({ event, scale, screenId });
        },
        [scale, screenId, setModelFromValue, setValueFromModel],
    );

    const onChange = React.useCallback(
        (event: React.ChangeEvent<HTMLInputElement>): void => {
            setInputValue(event.currentTarget.value);
            debouncedSetModelFromValue();
        },
        [debouncedSetModelFromValue],
    );

    React.useEffect(() => {
        setValueFromModel();
    }, [model, setValueFromModel]);

    const className = React.useMemo(() => {
        const classes = ['ag-labeled', 'ag-label-align-left', 'ag-text-field', 'ag-input-field'];
        if (isDisabled) {
            classes.push('ag-disabled');
        }
        return classes.join(' ');
    }, [isDisabled]);

    return (
        <div className="ag-floating-filter-input" role="presentation">
            <div role="presentation" className={className}>
                <div className="ag-input-field-label ag-label ag-hidden ag-text-field-label" role="presentation" />
                <div className="ag-wrapper ag-input-wrapper ag-text-field-input-wrapper" role="presentation">
                    <input
                        className="ag-input-field-input ag-text-field-input e-filter-numeric-floating"
                        aria-label={localize('@sage/xtrem-ui/table-filter-aria-label', 'Filter')}
                        type="text"
                        disabled={isDisabled}
                        value={inputValue}
                        onKeyDown={onKeyDown}
                        onChange={onChange}
                        onBlur={setModelFromValue}
                    />
                </div>
            </div>
        </div>
    );
}
