import { getDateRange, type DateRangeType } from '@sage/xtrem-date-time';
import {
    EQUALS,
    GREATER_THAN,
    GREATER_THAN_EQUAL,
    LESS_THAN,
    LESS_THAN_EQUAL,
    NOT_EQUALS,
    RANGE,
    type LocalizeLocale,
} from '@sage/xtrem-shared';
import { includes, isNil } from 'lodash';
import type { DateFilterState, DateModel, DateState, SimpleDateModel } from './date-filter-types';

export const isSimpleDateFilter = (filter: DateModel): filter is SimpleDateModel => {
    return !Object.prototype.hasOwnProperty.call(filter, 'operator');
};

export const agGridDateFilterTypes = [
    EQUALS,
    NOT_EQUALS,
    LESS_THAN,
    GREATER_THAN,
    LESS_THAN_EQUAL,
    GREATER_THAN_EQUAL,
    RANGE,
];

export const defaultDateState = (): DateState => ({
    rawValue: '',
    formattedValue: '',
    rawValueTo: '',
    formattedValueTo: '',
    type: EQUALS,
});

export const dateRanges: DateRangeType[] = [
    'last-30-days',
    'last-7-days',
    'next-day',
    'next-month',
    'next-week',
    'next-year',
    'previous-day',
    'previous-month',
    'previous-week',
    'previous-year',
    'same-day',
    'same-month',
    'same-week',
    'same-year',
];
export const getModelFromDateState = ({
    dateState,
    locale,
    isDatetime = false,
}: {
    dateState: DateFilterState;
    locale: LocalizeLocale;
    isDatetime?: boolean;
}): SimpleDateModel => {
    if (includes(dateRanges, dateState.type)) {
        const dateRange = getDateRange({
            date: new Date().toISOString().slice(0, 10),
            range: dateState.type as DateRangeType,
            locale,
        });
        const dateFrom = isDatetime
            ? (dateRange.start?.toJsDate().toISOString() ?? '')
            : (dateRange.start?.format('YYYY-MM-DD') ?? '');
        const dateTo = isDatetime
            ? (dateRange.end?.toJsDate().toISOString() ?? '')
            : (dateRange.end?.format('YYYY-MM-DD') ?? '');
        return {
            filterType: 'date',
            dateFrom,
            dateTo,
            type: RANGE,
            formattedValue: dateRange.start?.toString() ?? '',
            formattedValueTo: dateRange.end?.toString() ?? '',
        };
    }
    return {
        filterType: 'date',
        dateFrom: dateState.rawValue,
        dateTo: dateState.rawValueTo,
        type: dateState.type as DateState['type'],
        formattedValue: dateState.formattedValue,
        formattedValueTo: dateState.formattedValueTo,
    };
};

export function getDateStateFromModel(model: DateModel | null): DateState {
    const value = defaultDateState();

    if (!model || !isSimpleDateFilter(model)) {
        return value;
    }
    if (!isNil(model.dateFrom)) {
        value.rawValue = model.dateFrom;
    }
    if (!isNil(model.dateTo)) {
        value.rawValueTo = model.dateTo;
    }
    if (!isNil(model.formattedValue)) {
        value.formattedValue = model.formattedValue;
    }
    if (!isNil(model.formattedValueTo)) {
        value.formattedValueTo = model.formattedValueTo;
    }
    if (!isNil(model.type)) {
        value.type = model.type;
    }
    return value;
}
