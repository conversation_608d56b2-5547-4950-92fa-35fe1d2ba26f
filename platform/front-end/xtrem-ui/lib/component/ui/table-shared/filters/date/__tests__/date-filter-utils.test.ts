import { getDateRange } from '@sage/xtrem-date-time';
import { EQUALS, RANGE } from '@sage/xtrem-shared';
import type { SimpleDateModel } from '../date-filter-types';
import {
    dateRanges,
    defaultDateState,
    getDateStateFromModel,
    getModelFromDateState,
    isSimpleDateFilter,
} from '../date-filter-utils';

describe('date-filter-utils', () => {
    const formattedValue = '01/01/2023';
    const formattedValueTo = '02/01/2023';

    describe('isSimpleDateFilter', () => {
        it('should return true for a simple date filter', () => {
            const filter: SimpleDateModel = {
                dateFrom: '2023-01-01',
                dateTo: '2023-01-02',
                filterType: 'date',
                type: 'equals',
                formattedValue: '01/01/2023',
                formattedValueTo: '02/01/2023',
            };
            expect(isSimpleDateFilter(filter)).toBe(true);
        });

        it('should return false for a complex date filter', () => {
            const filter = { operator: 'and' } as any;
            expect(isSimpleDateFilter(filter)).toBe(false);
        });
    });

    describe('defaultDateState', () => {
        it('should return the correct default date state', () => {
            const expectedState = {
                rawValue: '',
                formattedValue: '',
                rawValueTo: '',
                formattedValueTo: '',
                type: EQUALS,
            };
            expect(defaultDateState()).toEqual(expectedState);
        });
    });

    describe('getModelFromDateState', () => {
        it('should return the correct model for a given date state and locale', () => {
            const dateState = {
                rawValue: '2023-01-01',
                formattedValue,
                rawValueTo: '2023-01-02',
                formattedValueTo,
                type: EQUALS,
            } as const;
            const locale = 'en-US';
            const expectedModel = {
                filterType: 'date',
                dateFrom: '2023-01-01',
                dateTo: '2023-01-02',
                type: EQUALS,
                formattedValue,
                formattedValueTo,
            };
            expect(getModelFromDateState({ dateState, locale })).toEqual(expectedModel);
        });

        it('should handle date ranges correctly', () => {
            const dateState = {
                rawValue: '',
                formattedValue: '',
                rawValueTo: '',
                formattedValueTo: '',
            };
            const locale = 'en-US';
            dateRanges.forEach(range => {
                const dateRange = getDateRange({
                    date: new Date().toISOString().slice(0, 10),
                    range,
                    locale,
                });
                const expectedModel = {
                    filterType: 'date',
                    dateFrom: dateRange.start?.format('YYYY-MM-DD') ?? '',
                    dateTo: dateRange.end?.format('YYYY-MM-DD') ?? '',
                    type: RANGE,
                    formattedValue: dateRange.start?.toString() ?? '',
                    formattedValueTo: dateRange.end?.toString() ?? '',
                };
                expect(getModelFromDateState({ dateState: { ...dateState, type: range }, locale })).toEqual(
                    expectedModel,
                );
            });
        });
    });

    describe('getDateStateFromModel', () => {
        it('should return the correct date state for a given model', () => {
            const model = {
                dateFrom: '2023-01-01',
                dateTo: '2023-01-02',
                formattedValue,
                formattedValueTo,
                type: EQUALS,
                filterType: 'date',
            } as const;
            const expectedState = {
                rawValue: '2023-01-01',
                formattedValue,
                rawValueTo: '2023-01-02',
                formattedValueTo,
                type: EQUALS,
            };
            expect(getDateStateFromModel(model)).toEqual(expectedState);
        });

        it('should handle null and undefined models correctly', () => {
            const expectedState = defaultDateState();
            expect(getDateStateFromModel(null)).toEqual(expectedState);
            expect(getDateStateFromModel(undefined as any)).toEqual(expectedState);
        });
    });
});
