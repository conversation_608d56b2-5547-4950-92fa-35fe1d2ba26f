import type { Column } from '@ag-grid-community/core';
import type {
    EQUALS,
    GREATER_THAN,
    GREATER_THAN_EQUAL,
    LESS_THAN,
    LESS_THAN_EQUAL,
    NOT_EQUALS,
    RANGE,
} from '../../../../types';

export type NumericFilterTypes =
    | typeof LESS_THAN
    | typeof LESS_THAN_EQUAL
    | typeof EQUALS
    | typeof NOT_EQUALS
    | typeof GREATER_THAN
    | typeof GREATER_THAN_EQUAL
    | typeof RANGE;

export interface NumericFilterModel {
    filterType: 'number';
    type: NumericFilterTypes;
    filter: number | null;
    filterTo?: number | null;
}

export interface NumericFloatingFilterProps {
    model: NumericFilterModel | null;
    onModelChange: (model: NumericFilterModel | null) => void;
    column: Column;
}

export interface NumericFilterProps {
    model: NumericFilterModel | null;
    onModelChange: (model: NumericFilterModel | null) => void;
    column: Column;
}

export interface NumericFilterValueState {
    value?: number;
    valueTo?: number;
    type: NumericFilterTypes;
}

export interface NumericFilterInputProps {
    value: number | null;
    onChange: (value: number | null) => void;
    scale: any;
    screenId: string;
    'data-testid'?: string;
}
