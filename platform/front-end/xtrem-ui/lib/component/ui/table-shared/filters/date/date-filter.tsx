import { RANGE } from '@sage/xtrem-shared';
import CarbonDate from 'carbon-react/esm/components/date';
import React from 'react';
import { includes, isEqual } from 'lodash';
import type { SelectItem } from '../../../select/select-component';
import type { DateFilterProps, DateFilterState, EventTargetOnDateChange } from './date-filter-types';
import { getModelFromDateState, getDateStateFromModel, dateRanges, agGridDateFilterTypes } from './date-filter-utils';
import { isValidIsoDate } from '@sage/xtrem-date-time';
import { useDeepCompareEffect } from '@sage/xtrem-ui-components';
import { DateFilterSelect } from './date-filter-select';

export default function DateFilter({ model, onModelChange, locale }: DateFilterProps): React.ReactNode {
    const [dateState, setDateState] = React.useState<DateFilterState>(() => getDateStateFromModel(model));

    useDeepCompareEffect(() => {
        const newDateState = getDateStateFromModel(model);
        if (!isEqual(dateState, newDateState)) {
            setDateState(current =>
                agGridDateFilterTypes.includes(current.type) ? newDateState : { ...newDateState, type: current.type },
            );
        }
    }, [model]);

    const onChange = React.useCallback(
        (newValue: Partial<DateFilterState>) => {
            setDateState(current => ({ ...current, ...newValue }));
            const values = Object.values(newValue ?? {});
            if (values.filter(Boolean).length === 0) {
                onModelChange(null);
            }
            if (
                values.length > 0 &&
                values.some(v => isValidIsoDate(v) || (newValue.type && includes(dateRanges, newValue.type)))
            ) {
                onModelChange(getModelFromDateState({ dateState: { ...dateState, ...newValue }, locale }));
            }
        },
        [dateState, onModelChange, locale],
    );

    const handleChange = React.useCallback(
        (event: React.ChangeEvent<EventTargetOnDateChange>): void => {
            const newValue = event.target.value.rawValue;
            const newFormattedValue = event.target.value.formattedValue;
            onChange({ rawValue: newValue, formattedValue: newFormattedValue });
        },
        [onChange],
    );

    const handleChangeTo = React.useCallback(
        (event: React.ChangeEvent<EventTargetOnDateChange>): void => {
            const newValue = event.target.value.rawValue;
            const newFormattedValue = event.target.value.formattedValue;
            onChange({ rawValueTo: newValue, formattedValueTo: newFormattedValue });
        },
        [onChange],
    );

    const onFilterTypeChanged = React.useCallback(
        (item: SelectItem): void => {
            const newValue = item.value as DateFilterState['type'];
            onChange({ type: newValue });
        },
        [onChange],
    );

    const isPredefinedRange = React.useMemo(() => includes(dateRanges, dateState.type), [dateState.type]);

    const isRange = React.useMemo(
        () => dateState.type === RANGE || isPredefinedRange,
        [dateState.type, isPredefinedRange],
    );

    return (
        <div className="e-filter-date-container">
            <DateFilterSelect onChange={onFilterTypeChanged} value={dateState.type} />
            <CarbonDate
                readOnly={isPredefinedRange}
                mt="6px"
                data-testid="e-date-filter"
                allowEmptyValue={true}
                onChange={handleChange}
                value={dateState.formattedValue}
                size="small"
                autoComplete="off"
                disablePortal={true}
                inputWidth={100}
            />
            {isRange && (
                <CarbonDate
                    readOnly={isPredefinedRange}
                    mt="6px"
                    allowEmptyValue={true}
                    onChange={handleChangeTo}
                    value={dateState.formattedValueTo ?? ''}
                    size="small"
                    autoComplete="off"
                    disablePortal={true}
                    inputWidth={100}
                />
            )}
        </div>
    );
}
