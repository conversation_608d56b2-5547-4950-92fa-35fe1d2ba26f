import IconButton from 'carbon-react/esm/components/icon-button/icon-button.component';
import Icon from 'carbon-react/esm/components/icon';
import * as tokens from '@sage/design-tokens/js/base/common';
import * as React from 'react';
import { localize } from '../../../../../service/i18n-service';
import type { GridApi } from '@ag-grid-community/core';

/**
 * This floating filter component is used to clear all the currently selected filters.
 */
export function ClearFloatingFilter(props: { api: GridApi; elementId: string }): React.ReactElement {
    const clearAllFloatingFilters = (): void => {
        const api = props.api;

        api.setFilterModel({});
    };

    return (
        <IconButton
            data-testid="e-table-remove-all-filters"
            aria-label={localize('@sage/xtrem-ui/clear-floating-filter', 'Clear all filters')}
            onClick={clearAllFloatingFilters}
            margin="0 auto"
            data-pendoid={`clearFloatingFilterButton-${props.elementId}`}
        >
            <Icon
                type="cross_circle"
                color={tokens.colorsUtilityMajor300}
                tooltipMessage={localize('@sage/xtrem-ui/clear-floating-filter', 'Clear all filters')}
            />
        </IconButton>
    );
}

ClearFloatingFilter.displayName = 'ClearFloatingFilter';

export default ClearFloatingFilter;
