import CheckboxComponent from 'carbon-react/esm/components/checkbox/checkbox.component';
import Link from 'carbon-react/esm/components/link';
import Loader from 'carbon-react/esm/components/loader';
import Textbox from 'carbon-react/esm/components/textbox';
import { camelCase, debounce, uniqBy } from 'lodash';
import * as React from 'react';
import { localize } from '../../../../../service/i18n-service';
import type { AgGridColumnConfigWithScreenIdAndColDef } from '../../../../../utils/ag-grid/ag-grid-utility-types';
import { xtremConsole } from '../../../../../utils/console';
import type { CarbonLinkEvent } from '../../../../../utils/types';
import { fetchReferenceItems } from '../../../../field/reference/reference-utils';
import { SET } from '../../../../types';
import type { SelectItem } from '../../../select/select-component';

const ReferenceFilterItem: React.FC<{ itemValue: string; textFilter?: string }> = React.memo(
    ({ textFilter: searchCriteria, itemValue }) => {
        if (!itemValue) {
            return <span className="e-reference-custom-filter-item-value" />;
        }

        if (!searchCriteria) {
            return <span className="e-reference-custom-filter-item-value">{itemValue}</span>;
        }

        const pos = itemValue.toLowerCase().indexOf(searchCriteria.toLowerCase());
        const firstPart = itemValue.substr(0, pos);
        const highlightedPart = itemValue.substr(pos, searchCriteria.length);
        const lastPart = itemValue.substr(pos + searchCriteria.length);

        return (
            <span className="e-reference-custom-filter-item-value">
                {firstPart}
                <span className="e-reference-custom-filter-item-value-highlighted">{highlightedPart}</span>
                {lastPart}
            </span>
        );
    },
);

ReferenceFilterItem.displayName = 'ReferenceFilterItem';

type FilterModel = {
    filterType: string;
    // array of IDs
    values: string[];
    /**
     * By default, the filtering applied to the value field text value, so we need to pass this flag to indicate that
     * the records should be filtered by their ID values
     *  */
    filterById: boolean;
    // This value is used by the ReferenceFloatingFilter component to display the selection summary in the header
    displayValues: string[];
};

export interface ReferenceFilterProps {
    colDef: AgGridColumnConfigWithScreenIdAndColDef;
    onModelChange: (model: FilterModel | null) => void;
    model?: FilterModel | null;
}

/**
 * Custom filter component for AG-Grid, handling references.
 *
 * Not implemented:
 * - Support for nested grids
 * - "Select All" functionality
 */
export function ReferenceFilter({ colDef, onModelChange, model }: ReferenceFilterProps): React.ReactNode {
    const [textFilter, setTextFilter] = React.useState<string>('');
    const [textFilterDisplayValue, setTextFilterDisplayValue] = React.useState<string>('');
    const [hasNextPage, setHasNextPage] = React.useState<boolean>(true);
    const [isFetching, setFetching] = React.useState<boolean>(true);
    const [availableItems, setAvailableItems] = React.useState<SelectItem[]>([]);

    const debouncedSearch = React.useMemo(
        () =>
            debounce((filter: string) => {
                setTextFilter(filter);
            }, 500),
        [],
    );

    // Debounce the actual filter value update so we don't refetch on every key stroke
    const debouncedTextFilterUpdate = React.useCallback(debouncedSearch, [debouncedSearch]);

    React.useEffect(() => {
        fetchReferenceItems(textFilter, colDef)
            .then(items => {
                setAvailableItems(items);
            })
            .catch(e => {
                xtremConsole.error(e);
                setAvailableItems([]);
            })
            .finally(() => {
                setFetching(false);
            });
    }, [colDef, textFilter]);

    const onTextFilterChange = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>): void => {
            setHasNextPage(true);
            setTextFilterDisplayValue(ev.target.value);
            debouncedTextFilterUpdate(ev.target.value);
        },
        [debouncedTextFilterUpdate],
    );

    const onSelectionChange = React.useCallback(
        (isChecked: boolean, selectedItem: SelectItem): void => {
            const newFilter = [...(model?.values || [])];
            if (isChecked) {
                newFilter.push(selectedItem.id);
            } else {
                const index = newFilter.findIndex(i => i === selectedItem.id);
                if (index !== -1) {
                    newFilter.splice(index, 1);
                }
            }
            onModelChange(
                newFilter.length > 0
                    ? {
                          filterType: SET,
                          values: newFilter,
                          /**
                           * By default, the filtering applied to the value field text value, so we need to pass this flag to indicate that
                           * the records should be filtered by their ID values
                           *  */
                          filterById: true,
                          // This value is used by the ReferenceFloatingFilter component to display the selection summary in the header
                          displayValues: newFilter.map(r => {
                              const item = availableItems.find(i => i.id === r);
                              return item?.displayedAs || item?.value || r;
                          }),
                      }
                    : null,
            );
        },
        [availableItems, model?.values, onModelChange],
    );

    const onScroll = React.useCallback(
        async (event: React.SyntheticEvent<HTMLDivElement>): Promise<void> => {
            const listBody = event.target as HTMLDivElement;
            // If the scrollbar is 30 pixels away from the bottom, then we initiate the fetch of the next set of records
            const shouldLoadNextPage = listBody.scrollHeight - listBody.scrollTop <= listBody.clientHeight + 30;
            if (!isFetching && shouldLoadNextPage && hasNextPage) {
                const lastItem = availableItems[availableItems.length - 1];
                const newItems = await fetchReferenceItems(textFilter, colDef, lastItem.__collectionItem?.__cursor);
                setHasNextPage(newItems.length > 0);
                setAvailableItems(uniqBy([...availableItems, ...newItems], i => i.id));
                setFetching(false);
            }
        },
        [availableItems, colDef, hasNextPage, isFetching, textFilter],
    );

    const onClearSelection = React.useCallback(
        (ev: CarbonLinkEvent): void => {
            ev.preventDefault();
            ev.stopPropagation();
            onModelChange(null);
        },
        [onModelChange],
    );

    const debouncedOnScroll = React.useMemo(() => debounce(onScroll, 1000), [onScroll]);

    const isClearSelectedDisabled = React.useMemo(() => (model?.values ?? []).length === 0, [model?.values]);

    const onChange = React.useCallback<
        (item: SelectItem) => NonNullable<React.ComponentProps<typeof CheckboxComponent>['onChange']>
    >(
        item =>
            (event): void =>
                onSelectionChange(!!event.target.checked, item),
        [onSelectionChange],
    );
    return (
        <div className="e-reference-custom-filter">
            <div
                className="e-reference-custom-filter-clear-container"
                data-testid="e-reference-custom-filter-clear-button"
            >
                <Link onClick={onClearSelection} disabled={isClearSelectedDisabled}>
                    {localize('@sage/xtrem-ui/reference-filter-clear-selection', 'Clear selected items')}
                </Link>
            </div>
            <Textbox
                data-testid="e-reference-custom-filter-input"
                placeholder={localize('@sage/xtrem-ui/filter-search-placeholder', 'Search')}
                value={textFilterDisplayValue}
                onChange={onTextFilterChange}
                size="small"
                mx="8px"
                my="4px"
            />
            <div className="e-reference-custom-filter-body" onScroll={debouncedOnScroll}>
                {availableItems.map(item => (
                    <div
                        className="e-reference-custom-filter-item"
                        data-testid="e-reference-custom-filter-item"
                        key={item.id}
                    >
                        <CheckboxComponent
                            onChange={onChange(item)}
                            key={item.id}
                            value={item.id}
                            checked={(model?.values ?? []).includes(item.id)}
                            data-testid={`e-reference-custom-filter-checkbox-label-${camelCase(
                                String(item.value),
                            )} e-reference-custom-filter-checkbox-bind-${item.id}`}
                        />
                        <ReferenceFilterItem itemValue={String(item.value)} textFilter={textFilter} />
                    </div>
                ))}
                {isFetching && (
                    <div className="e-reference-custom-filter-loader">
                        <Loader />
                    </div>
                )}
            </div>
        </div>
    );
}

ReferenceFilter.displayName = 'ReferenceFilter';

export default ReferenceFilter;
