import type { NumericFloatingFilterProps } from '../numeric-filter-types';
import NumericFloatingFilter from '../numeric-floating-filter';
import { render, waitFor, fireEvent, cleanup } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import React from 'react';
import '@testing-library/jest-dom';

describe('numeric floating filter', () => {
    let props: NumericFloatingFilterProps;

    beforeEach(() => {
        props = {
            onModelChange: jest.fn(),
            model: null,
            column: {
                getColDef: jest.fn().mockReturnValue({
                    context: {
                        screenId: 'screenId',
                    },
                    cellEditorParams: {
                        fieldProperties: {
                            scale: 2,
                        },
                    },
                }),
            } as any,
        };
    });

    afterEach(() => {
        cleanup();
    });

    describe('render', () => {
        it('should render with empty value', () => {
            const { queryByLabelText } = render(<NumericFloatingFilter {...props} />);
            const input = queryByLabelText('Filter') as HTMLInputElement;
            expect(input).not.toBeDisabled();
            expect(input).toHaveValue('');
        });

        it('should render with a simple equals value', () => {
            props.model = {
                filterType: 'number',
                type: 'equals',
                filter: 123,
            };
            const { queryByLabelText } = render(<NumericFloatingFilter {...props} />);
            const input = queryByLabelText('Filter') as HTMLInputElement;
            expect(input).not.toBeDisabled();
            expect(input).toHaveValue('123.00');
        });

        it('should format the number according to the scale configuration', () => {
            props.model = {
                filterType: 'number',
                type: 'equals',
                filter: 123.4567,
            };
            const { queryByLabelText } = render(<NumericFloatingFilter {...props} />);
            const input = queryByLabelText('Filter') as HTMLInputElement;
            expect(input).not.toBeDisabled();
            expect(input).toHaveValue('123.46');
        });

        it('should render disabled with greater than value', () => {
            props.model = {
                filterType: 'number',
                type: 'greaterThan',
                filter: 123,
            };
            const { queryByLabelText } = render(<NumericFloatingFilter {...props} />);
            const input = queryByLabelText('Filter') as HTMLInputElement;
            expect(input).toBeDisabled();
            expect(input).toHaveValue('> 123.00');
        });

        it('should render disabled with greater than equal value', () => {
            props.model = {
                filterType: 'number',
                type: 'greaterThanOrEqual',
                filter: 123,
            };
            const { queryByLabelText } = render(<NumericFloatingFilter {...props} />);
            const input = queryByLabelText('Filter') as HTMLInputElement;
            expect(input).toBeDisabled();
            expect(input).toHaveValue('>= 123.00');
        });

        it('should render disabled with less than value', () => {
            props.model = {
                filterType: 'number',
                type: 'lessThan',
                filter: 123,
            };
            const { queryByLabelText } = render(<NumericFloatingFilter {...props} />);
            const input = queryByLabelText('Filter') as HTMLInputElement;
            expect(input).toBeDisabled();
            expect(input).toHaveValue('< 123.00');
        });

        it('should render disabled with less than equal value', () => {
            props.model = {
                filterType: 'number',
                type: 'lessThanOrEqual',
                filter: 123,
            };
            const { queryByLabelText } = render(<NumericFloatingFilter {...props} />);
            const input = queryByLabelText('Filter') as HTMLInputElement;
            expect(input).toBeDisabled();
            expect(input).toHaveValue('<= 123.00');
        });

        it('should render disabled with not equal value', () => {
            props.model = {
                filterType: 'number',
                type: 'notEqual',
                filter: 123,
            };
            const { queryByLabelText } = render(<NumericFloatingFilter {...props} />);
            const input = queryByLabelText('Filter') as HTMLInputElement;
            expect(input).toBeDisabled();
            expect(input).toHaveValue('Not 123.00');
        });

        it('should render disabled with range value', () => {
            props.model = {
                filterType: 'number',
                type: 'inRange',
                filter: 123,
                filterTo: 456,
            };
            const { queryByLabelText } = render(<NumericFloatingFilter {...props} />);
            const input = queryByLabelText('Filter') as HTMLInputElement;
            expect(input).toBeDisabled();
            expect(input).toHaveValue('123.00 - 456.00');
        });
    });

    describe('interactions', () => {
        it('should update the filter value only after some time', async () => {
            const { queryByLabelText } = render(<NumericFloatingFilter {...props} />);
            const input = queryByLabelText('Filter') as HTMLInputElement;
            expect(input).toHaveValue('');
            userEvent.type(input, '5');
            await waitFor(() => {
                expect(input).toHaveValue('5');
            });
            expect(props.onModelChange).not.toHaveBeenCalled();

            await waitFor(
                () => {
                    expect(props.onModelChange).toHaveBeenCalled();
                },
                { timeout: 1500 },
            );

            expect(props.onModelChange).toHaveBeenCalledWith({
                filterType: 'number',
                type: 'equals',
                filter: 5,
            });
        });

        it('should update the filter value on blur', async () => {
            const { queryByLabelText } = render(<NumericFloatingFilter {...props} />);
            const input = queryByLabelText('Filter') as HTMLInputElement;
            expect(input).toHaveValue('');
            userEvent.type(input, '567');
            await waitFor(() => {
                expect(input).toHaveValue('567');
            });
            expect(props.onModelChange).not.toHaveBeenCalled();
            fireEvent.blur(input);
            expect(props.onModelChange).toHaveBeenCalledWith({
                filterType: 'number',
                type: 'equals',
                filter: 567,
            });
        });
    });
});
