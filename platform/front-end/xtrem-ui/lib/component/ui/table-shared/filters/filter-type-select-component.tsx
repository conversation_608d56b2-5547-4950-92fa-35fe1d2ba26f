import localeText from '../../../../utils/ag-grid/ag-grid-strings';
import { Select } from '../../select/select-component';
import React from 'react';
import {
    type Dict,
    EQUALS,
    GREATER_THAN,
    GREATER_THAN_EQUAL,
    LESS_THAN,
    LESS_THAN_EQUAL,
    NOT_EQUALS,
    RANGE,
} from '@sage/xtrem-shared';
import type { SelectItem } from '../../select/select-component';
import { memoize } from 'lodash';

export type GetItems = () => Promise<SelectItem[]>;
export interface FilterBySelectorProps {
    onChange: (item: SelectItem) => void;
    value: string;
    options?: SelectItem[];
}

export const filterOptions: (locale: Dict<string>) => SelectItem[] = memoize(locale => [
    { id: EQUALS, value: EQUALS, displayedAs: locale.equals },
    { id: NOT_EQUALS, value: NOT_EQUALS, displayedAs: locale.notEqual },
    { id: LESS_THAN, value: LESS_THAN, displayedAs: locale.lessThan },
    { id: GREATER_THAN, value: GREATER_THAN, displayedAs: locale.greaterThan },
    { id: LESS_THAN_EQUAL, value: LESS_THAN_EQUAL, displayedAs: locale.lessThanOrEqual },
    { id: GREATER_THAN_EQUAL, value: GREATER_THAN_EQUAL, displayedAs: locale.greaterThanOrEqual },
    { id: RANGE, value: RANGE, displayedAs: locale.inRange },
]);

/**
 * Component that shows a selector with the options to filter by
 */
export function FilterTypeSelect({ value, onChange, options: controlledOptions }: FilterBySelectorProps): JSX.Element {
    const tableLocaleText = localeText();
    const options = React.useMemo<SelectItem[]>(
        () => controlledOptions ?? filterOptions(tableLocaleText),
        [controlledOptions, tableLocaleText],
    );
    const getItems = React.useMemo<GetItems>(() => async (): Promise<SelectItem[]> => options, [options]);
    const selectedItem = React.useMemo<SelectItem | undefined>(
        () => options.find(option => option.id === value),
        [options, value],
    );

    return (
        <Select
            disableOpenOnFocus={true}
            hasInputSearch={false}
            getItems={getItems}
            onChange={onChange}
            selectedItem={selectedItem}
            size="small"
            testId="e-ui-filter-type"
        />
    );
}
