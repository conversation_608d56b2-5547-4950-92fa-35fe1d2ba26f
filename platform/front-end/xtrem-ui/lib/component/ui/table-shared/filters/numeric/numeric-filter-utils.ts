import { isNil } from 'lodash';
import type { NumericFilterModel, NumericFilterValueState } from './numeric-filter-types';
import { EQUALS } from '@sage/xtrem-shared';

export const defaultNumericFilterState = (): NumericFilterValueState => ({
    type: EQUALS,
});

export const getModelFromNumericFilterState = (dateState: NumericFilterValueState): NumericFilterModel | null =>
    dateState.value
        ? {
              filterType: 'number',
              filter: dateState.value,
              filterTo: dateState.valueTo,
              type: dateState.type,
          }
        : null;

export function getNumericFilterStateFromModel(model: NumericFilterModel | null): NumericFilterValueState {
    const value: NumericFilterValueState = {
        type: EQUALS,
    };

    if (!isNil(model?.filter)) {
        value.value = model.filter;
    }
    if (!isNil(model?.filterTo)) {
        value.valueTo = model.filterTo;
    }

    if (!isNil(model?.type)) {
        value.type = model.type;
    }
    return value;
}
