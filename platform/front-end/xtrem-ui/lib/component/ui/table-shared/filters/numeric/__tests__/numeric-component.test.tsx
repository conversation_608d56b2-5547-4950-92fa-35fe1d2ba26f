import { render, cleanup, waitFor, fireEvent } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import React from 'react';
import '@testing-library/jest-dom';
import type { NumericFilterProps } from '../numeric-filter-types';
import NumericFilter from '../numeric-filter';

describe('numeric  filter', () => {
    let props: NumericFilterProps;

    beforeEach(() => {
        props = {
            onModelChange: jest.fn(),
            model: null,
            column: {
                getColDef: jest.fn().mockReturnValue({
                    context: {
                        screenId: 'screenId',
                    },
                    cellEditorParams: {
                        fieldProperties: {
                            scale: 2,
                        },
                    },
                }),
            } as any,
        };
    });

    afterEach(() => {
        cleanup();
    });

    describe('render', () => {
        it('should render with empty filter value', () => {
            const { queryByTestId } = render(<NumericFilter {...props} />);
            const filterInput = queryByTestId('e-ui-numeric-filter-input') as HTMLInputElement;
            const typeSelect = queryByTestId('e-ui-filter-type') as HTMLInputElement;
            expect(filterInput).not.toBeDisabled();
            expect(filterInput).toHaveValue('');
            expect(typeSelect).toHaveValue('Equals');
        });

        it('should render with equals filter value', () => {
            props.model = {
                filterType: 'number',
                type: 'equals',
                filter: 123,
            };
            const { queryByTestId } = render(<NumericFilter {...props} />);
            const filterInput = queryByTestId('e-ui-numeric-filter-input') as HTMLInputElement;
            const filterInputTo = queryByTestId('e-ui-numeric-filter-input-to') as HTMLInputElement;
            const typeSelect = queryByTestId('e-ui-filter-type');
            expect(filterInputTo).toBeNull();
            expect(filterInput).toHaveValue('123.00');
            expect(typeSelect).toHaveValue('Equals');
        });

        it('should render with equals filter value', () => {
            props.model = {
                filterType: 'number',
                type: 'equals',
                filter: 123,
            };
            const { queryByTestId } = render(<NumericFilter {...props} />);
            const filterInput = queryByTestId('e-ui-numeric-filter-input') as HTMLInputElement;
            const filterInputTo = queryByTestId('e-ui-numeric-filter-input-to') as HTMLInputElement;
            const typeSelect = queryByTestId('e-ui-filter-type') as HTMLInputElement;
            expect(filterInputTo).toBeNull();
            expect(filterInput).toHaveValue('123.00');
            expect(typeSelect).toHaveValue('Equals');
        });

        it('should render with greater than filter value', () => {
            props.model = {
                filterType: 'number',
                type: 'greaterThan',
                filter: 123,
            };
            const { queryByTestId } = render(<NumericFilter {...props} />);
            const filterInput = queryByTestId('e-ui-numeric-filter-input') as HTMLInputElement;
            const filterInputTo = queryByTestId('e-ui-numeric-filter-input-to') as HTMLInputElement;
            const typeSelect = queryByTestId('e-ui-filter-type') as HTMLInputElement;
            expect(filterInputTo).toBeNull();
            expect(filterInput).toHaveValue('123.00');
            expect(typeSelect).toHaveValue('Greater than');
        });

        it('should render with greater than equals filter value', () => {
            props.model = {
                filterType: 'number',
                type: 'greaterThanOrEqual',
                filter: 123,
            };
            const { queryByTestId } = render(<NumericFilter {...props} />);
            const filterInput = queryByTestId('e-ui-numeric-filter-input') as HTMLInputElement;
            const filterInputTo = queryByTestId('e-ui-numeric-filter-input-to') as HTMLInputElement;
            const typeSelect = queryByTestId('e-ui-filter-type') as HTMLInputElement;
            expect(filterInputTo).toBeNull();
            expect(filterInput).toHaveValue('123.00');
            expect(typeSelect).toHaveValue('Greater than or equal');
        });

        it('should render with less than filter value', () => {
            props.model = {
                filterType: 'number',
                type: 'lessThan',
                filter: 123,
            };
            const { queryByTestId } = render(<NumericFilter {...props} />);
            const filterInput = queryByTestId('e-ui-numeric-filter-input') as HTMLInputElement;
            const filterInputTo = queryByTestId('e-ui-numeric-filter-input-to') as HTMLInputElement;
            const typeSelect = queryByTestId('e-ui-filter-type') as HTMLInputElement;
            expect(filterInputTo).toBeNull();
            expect(filterInput).toHaveValue('123.00');
            expect(typeSelect).toHaveValue('Less than');
        });

        it('should render with greater than equals filter value', () => {
            props.model = {
                filterType: 'number',
                type: 'lessThanOrEqual',
                filter: 123,
            };
            const { queryByTestId } = render(<NumericFilter {...props} />);
            const filterInput = queryByTestId('e-ui-numeric-filter-input') as HTMLInputElement;
            const filterInputTo = queryByTestId('e-ui-numeric-filter-input-to') as HTMLInputElement;
            const typeSelect = queryByTestId('e-ui-filter-type') as HTMLInputElement;
            expect(filterInputTo).toBeNull();
            expect(filterInput).toHaveValue('123.00');
            expect(typeSelect).toHaveValue('Less than or equal');
        });

        it('should render with range filter value', () => {
            props.model = {
                filterType: 'number',
                type: 'inRange',
                filter: 123,
                filterTo: 456,
            };
            const { queryByTestId } = render(<NumericFilter {...props} />);
            const filterInput = queryByTestId('e-ui-numeric-filter-input') as HTMLInputElement;
            const filterInputTo = queryByTestId('e-ui-numeric-filter-input-to') as HTMLInputElement;
            const typeSelect = queryByTestId('e-ui-filter-type') as HTMLInputElement;
            expect(filterInputTo).not.toBeNull();
            expect(filterInput).toHaveValue('123.00');
            expect(filterInputTo).toHaveValue('456.00');
            expect(typeSelect).toHaveValue('In range');
        });
    });

    describe('interactions', () => {
        it('should update the filter value on blur', async () => {
            const { queryByTestId } = render(<NumericFilter {...props} />);
            const filterInput = queryByTestId('e-ui-numeric-filter-input') as HTMLInputElement;
            expect(filterInput).toHaveValue('');
            userEvent.type(filterInput, '567');
            await waitFor(() => {
                expect(filterInput).toHaveValue('567');
            });
            expect(props.onModelChange).not.toHaveBeenCalled();
            fireEvent.blur(filterInput);
            expect(props.onModelChange).toHaveBeenCalledWith({
                filterType: 'number',
                type: 'equals',
                filter: 567,
            });
        });

        it('should update the filter end range value on blur', async () => {
            props.model = {
                filterType: 'number',
                type: 'inRange',
                filter: 123,
                filterTo: 456,
            };
            const { queryByTestId } = render(<NumericFilter {...props} />);
            const filterInputTo = queryByTestId('e-ui-numeric-filter-input-to') as HTMLInputElement;
            expect(filterInputTo).toHaveValue('456.00');
            userEvent.type(filterInputTo, '987');
            await waitFor(() => {
                expect(filterInputTo).toHaveValue('987');
            });
            expect(props.onModelChange).not.toHaveBeenCalled();
            fireEvent.blur(filterInputTo);
            expect(props.onModelChange).toHaveBeenCalledWith({
                filterType: 'number',
                type: 'inRange',
                filter: 123,
                filterTo: 987,
            });
        });
    });
});
