import * as React from 'react';
import { localize } from '../../../../../service/i18n-service';
import type { ReferenceFilterProps } from './reference-filter';

/**
 * This floating filter component is used to display the currently selected items for reference fields in the header using a comma separated list.
 */
export function ReferenceFloatingFilter({ model }: ReferenceFilterProps): React.ReactElement {
    const displayValue = React.useMemo((): string => {
        const displayValues = model?.displayValues;
        if (Array.isArray(displayValues) && displayValues.length > 0) {
            return `(${displayValues.length}) ${displayValues.join(', ')}`;
        }
        return '';
    }, [model]);

    /**
     * This DOM structure was copied from AG-Grid's `SetFilter` to ensure it has the same look and feel.
     */
    return (
        <div className="ag-floating-filter-input" role="presentation">
            <div
                role="presentation"
                className="ag-labeled ag-label-align-left ag-text-field ag-input-field ag-disabled"
            >
                <div className="ag-input-field-label ag-label ag-hidden ag-text-field-label" role="presentation" />
                <div className="ag-wrapper ag-input-wrapper ag-text-field-input-wrapper" role="presentation">
                    <input
                        className="ag-input-field-input ag-text-field-input"
                        aria-label={localize('@sage/xtrem-ui/table-filter-aria-label', 'Filter')}
                        type="text"
                        disabled={true}
                        value={displayValue}
                    />
                </div>
            </div>
        </div>
    );
}

export default ReferenceFloatingFilter;
