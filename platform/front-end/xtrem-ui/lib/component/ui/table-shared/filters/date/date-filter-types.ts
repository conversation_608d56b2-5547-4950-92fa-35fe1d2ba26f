import type { Column } from '@ag-grid-community/core';
import type { DateRangeType } from '@sage/xtrem-date-time';
import type {
    EQUALS,
    GREATER_THAN,
    GREATER_THAN_EQUAL,
    LESS_THAN,
    LESS_THAN_EQUAL,
    LocalizeLocale,
    NOT_EQUALS,
    RANGE,
} from '@sage/xtrem-shared';

export type EventTargetOnDateChange = { value: { formattedValue: string; rawValue: string } };

type AgGridDateFilterType =
    | typeof EQUALS
    | typeof NOT_EQUALS
    | typeof LESS_THAN
    | typeof GREATER_THAN
    | typeof LESS_THAN_EQUAL
    | typeof GREATER_THAN_EQUAL
    | typeof RANGE;

export type DateFilterType = AgGridDateFilterType | DateRangeType;

export type DateFilterState = Omit<DateState, 'type'> & {
    type: DateFilterType;
};

export interface DateState {
    rawValue: string;
    formattedValue: string;
    rawValueTo?: string;
    formattedValueTo?: string;
    type: AgGridDateFilterType;
}

export interface SimpleDateModel {
    filterType: 'date';
    dateFrom: string;
    dateTo?: string;
    type: AgGridDateFilterType;
    formattedValue: string;
    formattedValueTo?: string;
}

export interface ComplexDateModel {
    condition1: SimpleDateModel;
    filterType: 'date';
}

export interface SimpleDateEqualModel {
    filterType: typeof EQUALS;
    rawValue: string;
    formattedValue: string;
}
export type DateModel = SimpleDateModel | ComplexDateModel | SimpleDateEqualModel;

export interface DateFilterProps {
    model: DateModel | null;
    onModelChange: (model: DateModel | null) => void;
    column: Column;
    locale: LocalizeLocale;
}
