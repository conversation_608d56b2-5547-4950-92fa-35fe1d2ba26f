import React from 'react';
import { isEqual, isNil } from 'lodash';
import { onNumericInputKeyDown } from '../../../../field/numeric/numeric-cell-utils';
import { isEnter, isEsc } from '../../../../../utils/keyboard-event-utils';
import { formatNumericValue, parseLocalizedNumberStringToNumber } from '../../../../../utils/formatters';
import { localize } from '../../../../../service/i18n-service';
import { useDebouncedCallback } from 'use-debounce';
import Textbox from 'carbon-react/esm/components/textbox';
import type { NumericFilterInputProps } from './numeric-filter-types';

export function NumericFilterInput({
    value: externalValue,
    onChange: setExternalValue,
    scale,
    screenId,
    'data-testid': dataTestId,
}: NumericFilterInputProps): React.ReactNode {
    const [inputValue, setInputValue] = React.useState<string>('');

    const setInternalValueFromExternal = React.useCallback(() => {
        if (isNil(externalValue)) {
            setInputValue('');
            return;
        }

        const formattedNumber = formatNumericValue({
            screenId,
            value: externalValue,
            ignoreLocale: false,
            rowValue: null,
            scale,
        });

        setInputValue(formattedNumber);
    }, [externalValue, scale, screenId]);

    const setExternalValueFromInput = React.useCallback((): void => {
        if (isNil(inputValue)) {
            if (!isNil(externalValue)) {
                setExternalValue(null);
            }
            return;
        }

        const parsedValue = parseLocalizedNumberStringToNumber(
            inputValue,
            localize('@sage/xtrem-ui/number-format-separator', '.'),
        );
        if (isEqual(externalValue, parsedValue)) {
            return;
        }

        setExternalValue(parsedValue);
    }, [externalValue, inputValue, setExternalValue]);

    const debouncedSetModelFromValue = useDebouncedCallback(() => {
        setExternalValueFromInput();
    }, 1000);

    const onKeyDown = React.useCallback(
        (event: React.KeyboardEvent<HTMLInputElement>): void => {
            if (isEsc(event)) {
                event.preventDefault();
                setInternalValueFromExternal();
                return;
            }

            if (isEnter(event)) {
                event.preventDefault();
                setExternalValueFromInput();
                return;
            }

            onNumericInputKeyDown({ event, scale, screenId });
        },
        [scale, screenId, setExternalValueFromInput, setInternalValueFromExternal],
    );

    const onChange = React.useCallback(
        (event: React.ChangeEvent<HTMLInputElement>): void => {
            setInputValue(event.currentTarget.value);
            debouncedSetModelFromValue();
        },
        [debouncedSetModelFromValue],
    );

    React.useEffect(() => {
        setInternalValueFromExternal();
    }, [externalValue, setInternalValueFromExternal]);

    return (
        <Textbox
            mt="6px"
            onKeyDown={onKeyDown}
            onBlur={setExternalValueFromInput}
            className="e-ui-numeric-filter-input"
            onChange={onChange}
            value={inputValue}
            data-testid={dataTestId}
            inputWidth={100}
            size="small"
            autoComplete="off"
        />
    );
}
