import { RANGE } from '@sage/xtrem-shared';
import React from 'react';
import { isEqual, isNil } from 'lodash';
import type { SelectItem } from '../../../select/select-component';
import { useDeepCompareEffect } from '@sage/xtrem-ui-components';
import { FilterTypeSelect } from '../filter-type-select-component';
import type { ValueOrCallbackWithFieldValue } from '../../../../../utils/types';
import type { ScreenBase } from '../../../../../service/screen-base';
import type { NumericFilterProps, NumericFilterTypes, NumericFilterValueState } from './numeric-filter-types';
import { getModelFromNumericFilterState, getNumericFilterStateFromModel } from './numeric-filter-utils';
import type { AgGridColumnConfigWithScreenIdAndColDef } from '../../../../../utils/ag-grid/ag-grid-utility-types';
import { NumericFilterInput } from './numeric-filter-input';

export default function NumericFilter({ model, onModelChange, column }: NumericFilterProps): React.ReactNode {
    const screenId = React.useMemo(
        () => (column.getColDef() as AgGridColumnConfigWithScreenIdAndColDef).context.screenId,
        [column],
    );
    const scale: ValueOrCallbackWithFieldValue<ScreenBase, number, number> = React.useMemo(
        () => column.getColDef().cellEditorParams?.fieldProperties?.scale,
        [column],
    );
    const [numericFilterState, setNumericFilterState] = React.useState<NumericFilterValueState>(() =>
        getNumericFilterStateFromModel(model),
    );

    useDeepCompareEffect(() => {
        const newDateState = getNumericFilterStateFromModel(model);
        if (!isEqual(numericFilterState, newDateState)) {
            setNumericFilterState(newDateState);
        }
    }, [model]);

    const onChange = React.useCallback(
        (newValue: Partial<NumericFilterValueState>) => {
            const nextState = { ...numericFilterState, ...newValue };
            setNumericFilterState(nextState);
            if (isNil(nextState.value) && model !== null) {
                onModelChange(null);
            }

            const updatedModel = getModelFromNumericFilterState(nextState);
            if (!isNil(nextState.value) && !isEqual(updatedModel, model)) {
                onModelChange(updatedModel);
            }
        },
        [model, numericFilterState, onModelChange],
    );

    const handleChange = React.useCallback(
        (updatedValue: number | null): void => {
            onChange({ value: updatedValue || undefined });
        },
        [onChange],
    );

    const handleChangeTo = React.useCallback(
        (updatedValue: number | null): void => {
            onChange({ valueTo: updatedValue || undefined });
        },
        [onChange],
    );

    const onFilterTypeChanged = React.useCallback(
        (item: SelectItem): void => {
            const newValue = item.value as NumericFilterTypes;
            onChange({ type: newValue });
        },
        [onChange],
    );

    return (
        <div className="e-filter-numeric-container">
            <FilterTypeSelect onChange={onFilterTypeChanged} value={numericFilterState.type} />
            <NumericFilterInput
                data-testid="e-ui-numeric-filter-input"
                scale={scale}
                onChange={handleChange}
                value={numericFilterState.value ?? null}
                screenId={screenId}
            />
            {numericFilterState.type === RANGE && (
                <NumericFilterInput
                    data-testid="e-ui-numeric-filter-input-to"
                    scale={scale}
                    onChange={handleChangeTo}
                    value={numericFilterState.valueTo ?? null}
                    screenId={screenId}
                />
            )}
        </div>
    );
}
