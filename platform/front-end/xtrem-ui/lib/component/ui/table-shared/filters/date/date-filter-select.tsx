import React from 'react';

import { FilterTypeSelect, filterOptions } from '../filter-type-select-component';
import type { SelectItem } from '../../../select/select-component';
import { localize } from '../../../../../service/i18n-service';
import localeText from '../../../../../utils/ag-grid/ag-grid-strings';
import { memoize } from 'lodash';

export const dateFilterOptions = memoize((): SelectItem[] => [
    {
        id: 'previous-year',
        value: 'previous-year',
        displayedAs: localize('@sage/xtrem-ui/previous-year', 'Previous year'),
    },
    {
        id: 'previous-month',
        value: 'previous-month',
        displayedAs: localize('@sage/xtrem-ui/previous-month', 'Previous month'),
    },
    {
        id: 'last-30-days',
        value: 'last-30-days',
        displayedAs: localize('@sage/xtrem-ui/last-30-days', 'Last 30 days'),
    },
    {
        id: 'previous-week',
        value: 'previous-week',
        displayedAs: localize('@sage/xtrem-ui/previous-week', 'Previous week'),
    },
    {
        id: 'last-7-days',
        value: 'last-7-days',
        displayedAs: localize('@sage/xtrem-ui/last-7-days', 'Last 7 days'),
    },
    {
        id: 'previous-day',
        value: 'previous-day',
        displayedAs: localize('@sage/xtrem-ui/previous-day', 'Previous day'),
    },
    { id: 'same-day', value: 'same-day', displayedAs: localize('@sage/xtrem-ui/same-day', 'Current day') },

    {
        id: 'same-week',
        value: 'same-week',
        displayedAs: localize('@sage/xtrem-ui/same-week', 'Current week'),
    },
    {
        id: 'same-month',
        value: 'same-month',
        displayedAs: localize('@sage/xtrem-ui/same-month', 'Current month'),
    },
    {
        id: 'same-year',
        value: 'same-year',
        displayedAs: localize('@sage/xtrem-ui/same-year', 'Current year'),
    },
    { id: 'next-day', value: 'next-day', displayedAs: localize('@sage/xtrem-ui/next-day', 'Next day') },
    { id: 'next-week', value: 'next-week', displayedAs: localize('@sage/xtrem-ui/next-week', 'Next week') },
    {
        id: 'next-month',
        value: 'next-month',
        displayedAs: localize('@sage/xtrem-ui/next-month', 'Next month'),
    },
    { id: 'next-year', value: 'next-year', displayedAs: localize('@sage/xtrem-ui/next-year', 'Next year') },
]);

export function DateFilterSelect({
    onChange,
    value,
}: Omit<React.ComponentProps<typeof FilterTypeSelect>, 'getItems'>): React.ReactNode {
    const tableLocaleText = localeText();

    const options = React.useMemo(() => [...filterOptions(tableLocaleText), ...dateFilterOptions()], [tableLocaleText]);

    return <FilterTypeSelect onChange={onChange} value={value} options={options} />;
}
