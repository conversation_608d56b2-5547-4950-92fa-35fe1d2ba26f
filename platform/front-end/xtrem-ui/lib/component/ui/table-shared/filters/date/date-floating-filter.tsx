import React from 'react';
import CarbonDate from 'carbon-react/esm/components/date';
import { RANGE } from '@sage/xtrem-shared';
import type { DateFilterProps, EventTargetOnDateChange } from './date-filter-types';
import { localize } from '../../../../../service/i18n-service';
import { isSimpleDateFilter, getModelFromDateState } from './date-filter-utils';
import { EQUALS } from '../../../../types';

function DateFloatingFilter({ column, onModelChange, model, locale }: DateFilterProps): React.ReactNode {
    // FormattedValue is the value that is displayed in the input field, rawValue is the value that is used for filtering
    const formattedValue = React.useMemo<string>(() => {
        if (model && isSimpleDateFilter(model) && model.type === RANGE) {
            return `${model.formattedValue} - ${model.formattedValueTo}`;
        }
        if (model && isSimpleDateFilter(model) && model.type !== RANGE) {
            return model.formattedValue;
        }
        return '';
    }, [model]);

    const disabled = React.useMemo<boolean>(() => {
        return Boolean(model && isSimpleDateFilter(model) && model.type === RANGE);
    }, [model]);

    const handleChange = React.useCallback(
        (event: React.ChangeEvent<EventTargetOnDateChange>): void => {
            const newValue = event.target.value.rawValue;
            const newFormattedValue = event.target.value.formattedValue;

            if (disabled) {
                return;
            }

            const newModel =
                newFormattedValue === ''
                    ? null
                    : getModelFromDateState({
                          dateState: {
                              type: EQUALS,
                              formattedValue: newFormattedValue,
                              rawValue: newValue,
                          },
                          locale,
                      });
            onModelChange(newModel);
        },
        [disabled, onModelChange, locale],
    );

    const filterName = React.useMemo(() => column.getColDef().headerName, [column]);

    const floatingFilterLabel = React.useMemo(
        () =>
            localize('@sage/xtrem-ui/floating-filter-label', '{{ filterName }} filter input', {
                filterName,
            }),
        [filterName],
    );

    return (
        <div className="e-filter-date-floating-container">
            <CarbonDate
                disabled={disabled}
                allowEmptyValue={true}
                onChange={handleChange}
                value={formattedValue}
                size="small"
                autoComplete="off"
                inputWidth={100}
                aria-label={floatingFilterLabel}
            />
        </div>
    );
}

export default DateFloatingFilter;
