import {
    addFieldToState,
    addPageControlObject,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    userEvent,
} from '../../../../../__tests__/test-helpers';

import * as React from 'react';
import { render, cleanup } from '@testing-library/react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { Page } from '../../../../../service/page';
import * as nestedFields from '../../../../nested-fields';
import type { XtremAppState } from '../../../../../redux';
import { CollectionValue } from '../../../../../service/collection-data-service';
import baseTheme from 'carbon-react/esm/style/themes/sage';
import { ThemeProvider } from 'styled-components';
import type { TableProperties } from '../../../../control-objects';
import { NumericControlObject, ReferenceControlObject, TextControlObject } from '../../../../control-objects';
import { FieldKey } from '../../../../types';
import ConnectedTableDropdownActions from '../table-dropdown-actions';
import type { PageProperties } from '../../../../container/container-properties';
import { CollectionFieldTypes } from '../../../../../service/collection-data-types';
import { destroyScreenCollections } from '../../../../../service/loki';
import * as events from '../../../../../utils/events';
import type { PageDefinition } from '../../../../../service/page-definition';
import '@testing-library/jest-dom';
import { GraphQLKind } from '../../../../../types';

jest.useFakeTimers();

describe('TableDropdownActions', () => {
    const originalTextColumn = 'Test Text Value 1';
    const textColumn: string = originalTextColumn;
    const row1Value = {
        _id: 'id1',
        column1: textColumn,
        column2: 1,
        column3__code: {
            code: 'Test Code 1',
            description: 'Test Description 1',
        },
    };

    const row2Value = {
        _id: 'id2',
        column1: 'Test Text Value 2',
        column2: 2,
        column3__code: { code: 'Test Code 2', description: 'Test Description 2' },
    };
    const screenId = 'TestPage';
    let mockFieldProperties: TableProperties<Page, any>;
    let action1mock: jest.Mock<any>;
    let action2mock: jest.Mock<any>;
    let isDisabled2mock: jest.Mock<any>;
    let isHidden1Mock: jest.Mock<any>;
    let isHidden2Mock: jest.Mock<any>;
    let triggerHandledEventMock: jest.MockInstance<any, any>;
    const tableTestFieldId = 'lines';

    beforeEach(() => {
        action1mock = jest.fn();
        action2mock = jest.fn();
        isDisabled2mock = jest.fn().mockReturnValue(true);
        isHidden1Mock = jest.fn().mockReturnValue(false);
        isHidden2Mock = jest.fn().mockReturnValue(false);
        triggerHandledEventMock = jest.spyOn(events, 'triggerHandledEvent').mockImplementation(jest.fn());

        mockFieldProperties = {
            _controlObjectType: FieldKey.Table,
            dropdownActions: [
                { icon: 'add', title: 'Test action 1', onClick: action1mock },
                {
                    icon: 'add',
                    title: 'Test action 2',
                    onClick: action2mock,
                    isHidden: isHidden1Mock,
                    isDisabled: isDisabled2mock,
                },
                {
                    icon: 'plus',
                    title: 'Test action 3',
                    onClick: action2mock,
                    isHidden: isHidden2Mock,
                    isDisabled: jest.fn().mockReturnValue(false),
                },
            ],
            title: 'Table Test Title',
            columns: [
                {
                    defaultUiProperties: {
                        ...TextControlObject.defaultUiProperties,
                        bind: 'column1',
                    },
                    properties: {
                        bind: 'column1',
                        title: 'Column 1',
                    },
                    type: FieldKey.Text,
                },
                {
                    defaultUiProperties: {
                        ...NumericControlObject.defaultUiProperties,
                        bind: 'column2',
                    },
                    properties: {
                        bind: 'column2',
                        title: 'Column 2',
                    },
                    type: FieldKey.Numeric,
                },
                {
                    defaultUiProperties: {
                        ...ReferenceControlObject.defaultUiProperties,
                        bind: 'column3',
                    },
                    properties: {
                        bind: 'column3',
                        title: 'Column 3',
                        node: 'column3',
                        valueField: 'code',
                        helperTextField: 'description',
                    },
                    type: FieldKey.Reference,
                },
            ],
        };
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        triggerHandledEventMock.mockReset();
        await cleanup();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<XtremAppState>;
        let state: XtremAppState;

        beforeEach(() => {
            destroyScreenCollections(screenId);
            state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(
                FieldKey.Table,
                state,
                screenId,
                tableTestFieldId,
                mockFieldProperties,
                new CollectionValue<any>({
                    screenId,
                    elementId: tableTestFieldId,
                    isTransient: false,
                    hasNextPage: false,
                    orderBy: [{}],
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'column1' }),
                            nestedFields.numeric({ bind: 'column2' }),
                            nestedFields.text<any, any>({ bind: 'column3' }),
                        ],
                    ],
                    nodeTypes: {},
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [row1Value, row2Value],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                    locale: 'en-US',
                }),
            );

            const pageProperties: PageProperties<Page> = {
                isTransient: false,
            };
            addPageControlObject(state, screenId, pageProperties);
            mockStore = getMockStore(state);
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const wrapper = render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={0}
                                rowValue={row2Value}
                            />
                        </Provider>
                    </ThemeProvider>,
                );
                expect(wrapper.baseElement).toMatchSnapshot();
            });
        });

        describe('Interactions', () => {
            it('should open the menu when the user clicks on the icon', async () => {
                isHidden1Mock.mockReturnValue(true);
                const wrapper = render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={0}
                                rowValue={row2Value}
                            />
                        </Provider>
                    </ThemeProvider>,
                );

                expect(
                    wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
                ).toHaveLength(0);
                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(
                    wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
                ).toHaveLength(2);
            });

            it('should not render dropdown action if the user has no rights', async () => {
                mockFieldProperties.dropdownActions![0] = {
                    icon: 'add',
                    title: 'Test action 1',
                    onClick: action1mock,
                    access: {
                        bind: 'someProperty',
                        node: '@sage/xtrem-test/SomeNode',
                    },
                };
                const wrapper = render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={0}
                                rowValue={row2Value}
                            />
                        </Provider>
                    </ThemeProvider>,
                );

                expect(
                    wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
                ).toHaveLength(0);
                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(
                    wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
                ).toHaveLength(2);
            });

            it('should render dropdown action has an access rule and it is authorized', async () => {
                (state.screenDefinitions[screenId] as PageDefinition).accessBindings = {
                    SomeNode: {
                        someProperty: 'authorized',
                    },
                };
                mockStore = getMockStore(state);

                mockFieldProperties.dropdownActions![0] = {
                    icon: 'add',
                    title: 'Test action 1',
                    onClick: action1mock,
                    access: {
                        bind: 'someProperty',
                        node: '@sage/xtrem-test/SomeNode',
                    },
                };
                const wrapper = render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={0}
                                rowValue={row2Value}
                            />
                        </Provider>
                    </ThemeProvider>,
                );

                expect(
                    wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
                ).toHaveLength(0);
                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(
                    wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
                ).toHaveLength(3);
            });

            it('should not render dropdown action has an access rule and it is unauthorized', async () => {
                (state.screenDefinitions[screenId] as PageDefinition).accessBindings = {
                    SomeNode: {
                        someProperty: 'unauthorized',
                    },
                };
                state.nodeTypes = {
                    SomeNode: {
                        name: 'SomeNode',
                        title: 'Some Node',
                        packageName: '@sage/xtrem-test',
                        properties: {
                            someProperty: {
                                name: 'someProperty',
                                type: 'String',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                        },
                        mutations: {},
                    },
                };
                mockStore = getMockStore(state);

                mockFieldProperties.dropdownActions![0] = {
                    icon: 'add',
                    title: 'Test action 1',
                    onClick: action1mock,
                    access: {
                        bind: 'someProperty',
                        node: '@sage/xtrem-test/SomeNode',
                    },
                };
                const wrapper = render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={0}
                                rowValue={row2Value}
                            />
                        </Provider>
                    </ThemeProvider>,
                );

                expect(
                    wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
                ).toHaveLength(0);
                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(
                    wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
                ).toHaveLength(2);
            });

            it('should not render dropdown action has an access rule and it is unavailable', async () => {
                (state.screenDefinitions[screenId] as PageDefinition).accessBindings = {
                    SomeNode: {
                        someProperty: 'unavailable',
                    },
                };
                mockStore = getMockStore(state);

                mockFieldProperties.dropdownActions![0] = {
                    icon: 'add',
                    title: 'Test action 1',
                    onClick: action1mock,
                    access: {
                        bind: 'someProperty',
                        node: '@sage/xtrem-test/SomeNode',
                    },
                };
                const wrapper = render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={0}
                                rowValue={row2Value}
                            />
                        </Provider>
                    </ThemeProvider>,
                );

                expect(
                    wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
                ).toHaveLength(0);
                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(
                    wrapper.baseElement.querySelectorAll('[data-component="action-popover"] button[type="button"]'),
                ).toHaveLength(2);
            });

            it('should call the isDisabled callback on component rendering', () => {
                render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={0}
                                rowValue={row2Value}
                            />
                        </Provider>
                    </ThemeProvider>,
                );

                expect(isDisabled2mock).toHaveBeenCalledTimes(1);
            });

            it('should not render if an item is hidden', async () => {
                isHidden1Mock.mockReturnValue(true);

                const wrapper = render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={0}
                                rowValue={row2Value}
                            />
                        </Provider>
                    </ThemeProvider>,
                );

                expect(isHidden2Mock).toHaveBeenCalledTimes(1);

                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(
                    document.querySelectorAll('[data-component="action-popover"] button[type="button"]').length,
                ).toEqual(2);
            });

            it('should render conditionally displayed items', async () => {
                const wrapper = render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={0}
                                rowValue={row2Value}
                            />
                        </Provider>
                    </ThemeProvider>,
                );

                isHidden2Mock.mockReturnValue(false);

                expect(isHidden2Mock).toHaveBeenCalledTimes(1);

                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(
                    document.querySelectorAll('[data-component="action-popover"] button[type="button"]').length,
                ).toEqual(3);
            });

            it('should only the icon if only one action is visible', () => {
                isHidden1Mock.mockReturnValue(true);
                isHidden2Mock.mockReturnValue(true);

                const wrapper = render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={0}
                                rowValue={row2Value}
                            />
                        </Provider>
                    </ThemeProvider>,
                );

                expect(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')).toBeNull();
                expect(wrapper.baseElement.querySelector('button[aria-label="Test action 1"]')).not.toBeNull();
            });

            it('should trigger the event listener when the user clicks on an item', async () => {
                const wrapper = render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={0}
                                rowValue={row2Value}
                            />
                        </Provider>
                    </ThemeProvider>,
                );

                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(triggerHandledEventMock).not.toHaveBeenCalled();

                await userEvent.click(
                    document.querySelectorAll('[data-component="action-popover"] button[type="button"]')[0],
                );
                expect(triggerHandledEventMock).toHaveBeenCalledWith(
                    screenId,
                    tableTestFieldId,
                    { onClick: action1mock, onError: undefined },
                    'id2',
                    expect.objectContaining({ _id: 'id2', column1: 'Test Text Value 2', column2: 2 }),
                );
            });

            it('should trigger the right event listener even if an item is hidden in front of the item in the menu', async () => {
                isHidden1Mock.mockReturnValue(true);
                isHidden2Mock.mockReturnValue(false);
                const wrapper = render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={0}
                                rowValue={row2Value}
                            />
                        </Provider>
                    </ThemeProvider>,
                );

                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(triggerHandledEventMock).not.toHaveBeenCalled();

                await userEvent.click(
                    document.querySelectorAll('[data-component="action-popover"] button[type="button"]')[1],
                );

                expect(triggerHandledEventMock).toHaveBeenCalledWith(
                    screenId,
                    tableTestFieldId,
                    { onClick: action2mock, onError: undefined },
                    'id2',
                    expect.objectContaining({ _id: 'id2', column1: 'Test Text Value 2', column2: 2 }),
                );
            });

            it('should not trigger the event listener when the user clicks on a disabled item', async () => {
                const wrapper = render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={0}
                                rowValue={row2Value}
                            />
                        </Provider>
                    </ThemeProvider>,
                );
                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(triggerHandledEventMock).not.toHaveBeenCalled();
                const actionButton = document.querySelectorAll(
                    '[data-component="action-popover"] button[type="button"]',
                )[1];
                expect(actionButton).toHaveTextContent('Test action 2');
                expect(actionButton).toHaveAttribute('aria-disabled', 'true');
                await userEvent.click(actionButton);
                expect(triggerHandledEventMock).not.toHaveBeenCalled();
            });

            it('should trigger event listener for nested records', async () => {
                addFieldToState(
                    FieldKey.Table,
                    state,
                    screenId,
                    tableTestFieldId,
                    mockFieldProperties,
                    new CollectionValue<any>({
                        screenId,
                        elementId: tableTestFieldId,
                        isTransient: false,
                        hasNextPage: false,
                        orderBy: [{}],
                        columnDefinitions: [
                            [
                                nestedFields.text<any, any>({ bind: '_id' }),
                                nestedFields.text<any, any>({ bind: 'column1' }),
                                nestedFields.numeric({ bind: 'column2' }),
                                nestedFields.reference<any, any>({
                                    bind: 'column3',
                                    valueField: 'code',
                                    helperText: 'description',
                                } as any),
                            ],
                            [
                                nestedFields.text<any, any>({ bind: '_id' }),
                                nestedFields.text<any, any>({ bind: 'column1' }),
                                nestedFields.numeric({ bind: 'column2' }),
                                nestedFields.reference<any, any>({
                                    bind: 'column3',
                                    valueField: 'code',
                                    helperText: 'description',
                                } as any),
                            ],
                        ],
                        nodeTypes: {
                            AnyNode: {
                                name: 'AnyNode',
                                title: 'Any Node',
                                packageName: '@sage/xtrem-test',
                                properties: {
                                    column1: {
                                        name: 'column1',
                                        type: 'String',
                                        kind: GraphQLKind.Scalar,
                                        isOnInputType: true,
                                        canFilter: true,
                                    },
                                    column2: {
                                        name: 'column2',
                                        type: 'Int',
                                        kind: GraphQLKind.Scalar,
                                        isOnInputType: true,
                                        canFilter: true,
                                    },
                                    column3: {
                                        name: 'column3',
                                        type: 'Product',
                                        kind: GraphQLKind.Object,
                                        isOnInputType: true,
                                        canFilter: true,
                                        isMutable: true,
                                    },
                                },
                                mutations: {},
                            },
                            Product: {
                                name: 'Product',
                                title: 'Product',
                                packageName: '@sage/xtrem-test',
                                properties: {
                                    _id: {
                                        name: '_id',
                                        type: 'IntOrString',
                                        kind: GraphQLKind.Scalar,
                                        isOnInputType: true,
                                        canFilter: true,
                                    },
                                    code: {
                                        name: 'code',
                                        type: 'String',
                                        kind: GraphQLKind.Scalar,
                                        isOnInputType: true,
                                        canFilter: true,
                                    },
                                    description: {
                                        name: 'description',
                                        type: 'String',
                                        kind: GraphQLKind.Scalar,
                                        isOnInputType: true,
                                        canFilter: true,
                                    },
                                },
                                mutations: {},
                            },
                        },
                        nodes: ['@sage/xtrem-test/AnyNode', '@sage/xtrem-test/AnyOtherNode'],
                        filter: [undefined],
                        initialValues: [
                            {
                                _id: 'id2',
                                __level: 0,
                                column1: 'Test Text Value 2',
                                column2: 2,
                                column3__code: { code: 'Test Code 2', description: 'Test Description 2' },
                            },
                            {
                                _id: 'id2',
                                __level: 1,
                                column1: 'This is what we are expecting',
                                column2: 2,
                                column3__code: { code: 'Test Code 2', description: 'Test Description 2' },
                            },
                        ],
                        fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                    }),
                );

                const pageProperties: PageProperties<Page> = {
                    isTransient: false,
                };
                addPageControlObject(state, screenId, pageProperties);
                mockStore = getMockStore(state);

                const wrapper = render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={1}
                                rowValue={{
                                    _id: 'id2',
                                    __level: 1,
                                    column1: 'This is what we are expecting',
                                    column2: 2,
                                    column3__code: { code: 'Test Code 2', description: 'Test Description 2' },
                                }}
                            />
                        </Provider>
                    </ThemeProvider>,
                );

                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                expect(triggerHandledEventMock).not.toHaveBeenCalled();

                await userEvent.click(
                    document.querySelectorAll('[data-component="action-popover"] button[type="button"]')[0],
                );
                expect(triggerHandledEventMock).toHaveBeenCalledTimes(1);

                expect(triggerHandledEventMock).toHaveBeenCalledWith(
                    screenId,
                    tableTestFieldId,
                    { onClick: action1mock, onError: undefined },
                    'id2',
                    {
                        _id: 'id2',
                        column1: 'This is what we are expecting',
                        column2: 2,
                        column3: { code: 'Test Code 2', description: 'Test Description 2' },
                    },
                );
            });

            it('should trigger callback properties with the rigth arguments', async () => {
                addFieldToState(
                    FieldKey.Table,
                    state,
                    screenId,
                    tableTestFieldId,
                    mockFieldProperties,
                    new CollectionValue<any>({
                        screenId,
                        elementId: tableTestFieldId,
                        isTransient: false,
                        hasNextPage: false,
                        orderBy: [{}],
                        columnDefinitions: [
                            [
                                nestedFields.text<any, any>({ bind: '_id' }),
                                nestedFields.text<any, any>({ bind: 'column1' }),
                                nestedFields.numeric({ bind: 'column2' }),
                                nestedFields.reference<any, any>({
                                    bind: 'column3',
                                    valueField: 'code',
                                    helperText: 'description',
                                } as any),
                            ],
                            [
                                nestedFields.text<any, any>({ bind: '_id' }),
                                nestedFields.text<any, any>({ bind: 'column1' }),
                                nestedFields.numeric({ bind: 'column2' }),
                                nestedFields.reference<any, any>({
                                    bind: 'column3',
                                    valueField: 'code',
                                    helperText: 'description',
                                } as any),
                            ],
                        ],
                        nodeTypes: {
                            AnyNode: {
                                name: 'AnyNode',
                                title: 'Any Node',
                                packageName: '@sage/xtrem-test',
                                properties: {
                                    column1: {
                                        name: 'column1',
                                        type: 'String',
                                        kind: GraphQLKind.Scalar,
                                        isOnInputType: true,
                                        canFilter: true,
                                    },
                                    column2: {
                                        name: 'column2',
                                        type: 'Int',
                                        kind: GraphQLKind.Scalar,
                                        isOnInputType: true,
                                        canFilter: true,
                                    },
                                    column3: {
                                        name: 'column3',
                                        type: 'Product',
                                        kind: GraphQLKind.Object,
                                        isOnInputType: true,
                                        canFilter: true,
                                        isMutable: true,
                                    },
                                },
                                mutations: {},
                            },
                            Product: {
                                name: 'Product',
                                title: 'Product',
                                packageName: '@sage/xtrem-test',
                                properties: {
                                    _id: {
                                        name: '_id',
                                        type: 'IntOrString',
                                        kind: GraphQLKind.Scalar,
                                        isOnInputType: true,
                                        canFilter: true,
                                    },
                                    code: {
                                        name: 'code',
                                        type: 'String',
                                        kind: GraphQLKind.Scalar,
                                        isOnInputType: true,
                                        canFilter: true,
                                    },
                                    description: {
                                        name: 'description',
                                        type: 'String',
                                        kind: GraphQLKind.Scalar,
                                        isOnInputType: true,
                                        canFilter: true,
                                    },
                                },
                                mutations: {},
                            },
                        },
                        nodes: ['@sage/xtrem-test/AnyNode', '@sage/xtrem-test/AnyOtherNode'],
                        filter: [undefined],
                        initialValues: [
                            {
                                _id: 'id2',
                                __level: 0,
                                column1: 'Test Text Value 2',
                                column2: 2,
                                column3__code: { code: 'Test Code 2', description: 'Test Description 2' },
                            },
                            {
                                _id: 'id2',
                                __level: 1,
                                column1: 'This is what we are expecting',
                                column2: 2,
                                column3__code: { code: 'Test Code 2', description: 'Test Description 2' },
                            },
                        ],
                        fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                    }),
                );

                const pageProperties: PageProperties<Page> = {
                    isTransient: false,
                };
                addPageControlObject(state, screenId, pageProperties);
                mockStore = getMockStore(state);

                const wrapper = render(
                    <ThemeProvider theme={baseTheme}>
                        <Provider store={mockStore}>
                            <ConnectedTableDropdownActions
                                recordId="id2"
                                screenId={screenId}
                                fieldId={tableTestFieldId}
                                level={1}
                                rowValue={row2Value}
                            />
                        </Provider>
                    </ThemeProvider>,
                );

                await userEvent.click(wrapper.baseElement.querySelector('[data-component="action-popover-button"]')!);

                await userEvent.click(
                    document.querySelectorAll('[data-component="action-popover"] button[type="button"]')[0],
                );
                expect(isHidden2Mock).toHaveBeenCalled();
                expect(isHidden2Mock).toHaveBeenCalledWith('id2', {
                    _id: 'id2',
                    column1: 'Test Text Value 2',
                    column2: 2,
                    column3: { code: 'Test Code 2', description: 'Test Description 2' },
                });
            });
        });
    });
});
