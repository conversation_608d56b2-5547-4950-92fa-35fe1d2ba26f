.e-table-field-dropdown-actions {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    .e-table-field-dropdown-actions-icon {
        color: var(--colorsUtilityMajor300);
        cursor: pointer;
    }
}

.e-table-field-dropdown-actions-menu {
    padding: 8px 0;
    position: fixed;
    background: var(--colorsYang100);
    box-shadow: 0 5px 5px 0 rgba(0, 20, 29, 0.2), 0 10px 10px 0 rgba(0, 20, 29, 0.1);
    width: 165px;
    z-index: 8;

    .e-table-field-dropdown-action {
        padding: 4px 16px;
        cursor: pointer;
        line-height: 24px;

        &:hover {
            background-color: var(--colorsUtilityMajor025);
        }

        &.e-table-field-dropdown-action-disabled {
            color: var(--colorsYin030);
            cursor: not-allowed;
            background-color: var(--colorsYang100);

            .e-table-field-dropdown-action-icon {
                color: var(--colorsYin030);
            }
        }

        .e-table-field-dropdown-action-label {
            padding-left: 8px;
            font-family: $fontAdelle;
            font-size: 14px;
            line-height: 24px;
            height: 24px;
            display: inline-block;
        }
    }
}