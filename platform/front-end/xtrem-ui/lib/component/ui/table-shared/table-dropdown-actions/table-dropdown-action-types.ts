import type { ClientNode } from '@sage/xtrem-client';
import type { ButtonProps } from 'carbon-react/esm/components/button';
import type { NestedRecordId, ScreenExtension } from '../../../../types';
import type {
    CollectionActionAccessConfiguration,
    HasGenericErrorHandler,
    HasIcon,
    VoidPromise,
} from '../../../field/traits';
import type { CollectionItem, PartialCollectionValueWithIds } from '../../../types';

export interface CollectionPropertiesWithActions extends HasGenericErrorHandler<any> {
    dropdownActions?: Array<CollectionItemActionOrMenuSeparator<any>>[];
    inlineActions?: Array<InlineCollectionItemAction<any>>[];
    levels?: { dropdownActions: any[]; inlineActions: any }[];
}

export interface CollectionItemAction<CT extends ScreenExtension<CT>, NestedRecordType = CollectionItem>
    extends HasIcon,
        HasGenericErrorHandler<CT> {
    access?: CollectionActionAccessConfiguration;
    id?: string;
    insertAfter?: string;
    insertBefore?: string;
    isDestructive?: boolean;
    isDisabled?: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: PartialCollectionValueWithIds<NestedRecordType>,
    ) => boolean;
    isDisplayed?: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: PartialCollectionValueWithIds<NestedRecordType>,
    ) => boolean;
    isHidden?: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: PartialCollectionValueWithIds<NestedRecordType>,
    ) => boolean;
    isMenuSeparator?: false;
    onClick: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: PartialCollectionValueWithIds<NestedRecordType>,
    ) => VoidPromise;
    title: string;
}

export interface CollectionItemActionMenuSeparator<CT extends ScreenExtension<CT>, NestedRecordType = CollectionItem> {
    id?: string;
    insertAfter?: string;
    insertBefore?: string;
    isDisabled?: false;
    isDisplayed?: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: PartialCollectionValueWithIds<NestedRecordType>,
    ) => boolean;
    isHidden?: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: PartialCollectionValueWithIds<NestedRecordType>,
    ) => boolean;
    isMenuSeparator: true;
}

export type CollectionItemActionOrMenuSeparator<CT extends ScreenExtension<CT>, NestedRecordType = CollectionItem> =
    | CollectionItemAction<CT, NestedRecordType>
    | CollectionItemActionMenuSeparator<CT, NestedRecordType>;

export interface CollectionItemActionGroup<CT extends ScreenExtension<CT>, NestedRecordType = CollectionItem>
    extends HasIcon,
        HasGenericErrorHandler<CT> {
    access?: CollectionActionAccessConfiguration;
    children?: Array<
        CollectionItemActionOrMenuSeparator<CT, NestedRecordType> | CollectionItemActionGroup<CT, NestedRecordType>
    >;
    isDisabled?: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: PartialCollectionValueWithIds<NestedRecordType>,
    ) => boolean;
    isHidden?: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: PartialCollectionValueWithIds<NestedRecordType>,
    ) => boolean;
    isMenuSeparator?: false;
    title: string;
}
export interface InlineCollectionItemAction<CT extends ScreenExtension<CT>, NestedRecordType = CollectionItem>
    extends CollectionItemAction<CT, NestedRecordType> {
    buttonType?: ButtonProps['buttonType'];
    isMajor?: boolean;
}

export interface NestedCollectionItemAction<CT extends ScreenExtension<CT>, NestedRecordType extends ClientNode = any>
    extends HasIcon,
        HasGenericErrorHandler<CT> {
    access?: CollectionActionAccessConfiguration;
    id?: string;
    isDestructive?: boolean;
    isDisabled?: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: NestedRecordType,
        level?: number,
        parentIds?: NestedRecordId[],
    ) => boolean;
    isHidden?: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: NestedRecordType,
        level?: number,
        parentIds?: NestedRecordId[],
    ) => boolean;
    isMenuSeparator?: false;
    onClick: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: NestedRecordType,
        level?: number,
        parentIds?: NestedRecordId[],
    ) => void;
    title: string;
}

export interface NestedCollectionItemMenuSeparator<
    CT extends ScreenExtension<CT>,
    NestedRecordType extends ClientNode = any,
> {
    id?: string;
    isDisabled?: false;
    isHidden?: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: NestedRecordType,
        level?: number,
        parentIds?: NestedRecordId[],
    ) => boolean;
    isMenuSeparator: true;
}

export type NestedCollectionItemActionOrMenuSeparator<
    CT extends ScreenExtension<CT>,
    NestedRecordType extends ClientNode = any,
> = NestedCollectionItemAction<CT, NestedRecordType> | NestedCollectionItemMenuSeparator<CT, NestedRecordType>;

export interface NestedCollectionItemActionGroup<
    CT extends ScreenExtension<CT>,
    NestedRecordType extends ClientNode = any,
> extends HasIcon,
        HasGenericErrorHandler<CT> {
    access?: CollectionActionAccessConfiguration;
    children?: Array<
        | NestedCollectionItemActionOrMenuSeparator<CT, NestedRecordType>
        | NestedCollectionItemActionGroup<CT, NestedRecordType>
    >;
    isDisabled?: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: NestedRecordType,
        level?: number,
        parentIds?: NestedRecordId[],
    ) => boolean;
    isHidden?: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: NestedRecordType,
        level?: number,
        parentIds?: NestedRecordId[],
    ) => boolean;
    isMenuSeparator?: false;
    title: string;
}
