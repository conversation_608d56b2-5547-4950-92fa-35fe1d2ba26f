// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TableDropdownActions connected Snapshots should render with default properties 1`] = `
.c3 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c3:hover {
  color: var(--colorsYin090);
  background-color: transparent;
}

.c3::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e961";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c1 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c1.c1 {
  padding: var(--spacing000);
}

.c1:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c1:hover {
  cursor: pointer;
}

.c1::-moz-focus-inner {
  border: none;
}

.c1 .c2 {
  position: relative;
}

.c1 .c2:focus {
  border: none;
}

.c0 {
  position: relative;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: auto;
}

.c0.c0 .c2 {
  cursor: pointer;
}

<body>
  <div>
    <div
      class="c0"
      data-component="action-popover-wrapper"
      data-pendoid="tableDropdownActionsButton-TestPage-lines-0"
      id="ActionPopoverButton_testcarb-onco-mpon-ents-uniqguidmock"
    >
      <button
        aria-label="ellipsis_vertical"
        class="c1"
        data-component="action-popover-button"
        data-element="action-popover-button"
        type="button"
      >
        <span
          class="c2 c3"
          data-component="icon"
          data-element="ellipsis_vertical"
          data-role="icon"
          font-size="small"
          type="ellipsis_vertical"
        />
      </button>
    </div>
  </div>
</body>
`;
