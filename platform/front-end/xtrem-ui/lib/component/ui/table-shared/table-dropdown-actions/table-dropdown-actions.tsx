import { useDeepCompareMemo } from '@sage/xtrem-ui-components';
import Icon from 'carbon-react/esm/components/icon';
import IconButton from 'carbon-react/esm/components/icon-button';
import { isNil } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../../redux';
import type { OnTelemetryEventFunction } from '../../../../redux/state';
import type { CollectionValue } from '../../../../service/collection-data-service';
import type { AccessBindings } from '../../../../service/page-definition';
import type { NestedRecordId } from '../../../../types';
import { calculateActionMenuWithSeparator } from '../../../../utils/action-menu-utils';
import type { ActionType } from '../../../../utils/action-menu-utils';
import { INTERNAL_COLUMN_IDS } from '../../../../utils/ag-grid/ag-grid-column-config';
import { callGridMethod, tryToCommitPhantomRow } from '../../../../utils/ag-grid/ag-grid-table-utils';
import { triggerHandledEvent } from '../../../../utils/events';
import { TAB } from '../../../../utils/keyboard-event-utils';
import { getPageDefinitionFromState } from '../../../../utils/state-utils';
import { splitValueToMergedValue } from '../../../../utils/transformers';
import { navigationPanelId } from '../../../container/navigation-panel/navigation-panel-types';
import type { ErrorHandlerFunction } from '../../../field/traits';
import type { GridRowActionOrMenuSeparatorType } from '../../../types';
import type { XtremActionPopoverItemOrMenuSeparator } from '../../xtrem-action-popover';
import XtremActionPopover from '../../xtrem-action-popover';
import type { TableDropdownActionsCellProps } from '../cell/table-dropdown-action-cell';
import type { CollectionPropertiesWithActions } from './table-dropdown-action-types';

export interface TableDropdownActionExternalProps {
    actions?: GridRowActionOrMenuSeparatorType;
    api?: TableDropdownActionsCellProps['api'];
    column?: TableDropdownActionsCellProps['column'];
    fieldId: string;
    hasDropdownActions?: boolean;
    hasInlineActions?: boolean;
    isDisabled?: boolean;
    isPhantomRow?: boolean;
    level: number;
    onTelemetryEvent?: OnTelemetryEventFunction;
    recordId: NestedRecordId;
    rowIndex?: TableDropdownActionsCellProps['node']['rowIndex'];
    rowValue: any;
    screenId: string;
}

export interface TableDropdownActionProps extends TableDropdownActionExternalProps {
    fieldProperties: CollectionPropertiesWithActions;
    tableData: CollectionValue;
    accessBindings: AccessBindings;
    rowValue: any;
}

export function TableDropdownActions({
    accessBindings,
    api,
    column,
    fieldId,
    fieldProperties,
    hasInlineActions,
    isDisabled,
    isPhantomRow,
    level,
    recordId,
    rowIndex,
    rowValue,
    screenId,
    tableData,
    onTelemetryEvent,
}: TableDropdownActionProps): React.ReactNode {
    const actionType: ActionType = 'table-dropdown-action';

    const isNestedGrid = React.useMemo<boolean>(
        () => Object.prototype.hasOwnProperty.call(fieldProperties, 'levels'),
        [fieldProperties],
    );

    // rowValue is an object so "useDeepCompareMemo" is needed
    const onClickMenu = useDeepCompareMemo(
        () =>
            (
                context: { id?: string; uniqueId: string },
                onClick?: (this: any, recordId: string, rowItem: any, level?: number, parentIds?: string[]) => void,
                onError?: ErrorHandlerFunction<any>,
            ): void => {
                if (onTelemetryEvent) {
                    onTelemetryEvent(`tableDropdownActionTriggered-${context.uniqueId}`, {
                        screenId,
                        elementId: fieldId,
                        recordId,
                        id: context.id,
                        uniqueId: context.uniqueId,
                    });
                }

                if (!onClick) {
                    return;
                }

                const ancestorIds = tableData.getAncestorIds({
                    id: recordId,
                    level,
                });

                if (isNestedGrid) {
                    triggerHandledEvent(
                        screenId,
                        fieldId,
                        { onClick, onError: onError || fieldProperties.onError },
                        recordId,
                        splitValueToMergedValue(rowValue),
                        level,
                        ancestorIds,
                    );
                } else {
                    triggerHandledEvent(
                        screenId,
                        fieldId,
                        { onClick, onError: onError || fieldProperties.onError },
                        recordId,
                        splitValueToMergedValue(rowValue),
                    );
                }
            },
        [fieldId, fieldProperties.onError, isNestedGrid, level, recordId, rowValue, screenId, tableData],
    );

    const menuItems = React.useMemo<Array<XtremActionPopoverItemOrMenuSeparator>>(() => {
        const actions =
            isNestedGrid && fieldProperties.levels
                ? fieldProperties.levels[level].dropdownActions
                : fieldProperties.dropdownActions;
        if (actions) {
            const rowData = splitValueToMergedValue(rowValue);

            if (!rowData) {
                return [];
            }

            return calculateActionMenuWithSeparator({
                accessBindings,
                actions,
                actionType,
                onTriggerMenuItem: onClickMenu,
                rowValue: rowData,
                screenId,
                pendoId: `tableDropdownActionsButton-${screenId}-${fieldId}-${level}`,
            });
        }

        return [];
    }, [
        isNestedGrid,
        fieldProperties.levels,
        fieldProperties.dropdownActions,
        level,
        rowValue,
        accessBindings,
        onClickMenu,
        screenId,
        fieldId,
    ]);

    const renderPhantomRowRemoveButton = React.useCallback(() => {
        const row = tableData.getRawRecord({
            id: recordId,
            level,
            cleanMetadata: false,
        });

        return (
            <IconButton
                onKeyDown={e => {
                    if (e.code !== TAB) {
                        return;
                    }
                    if (isNil(api) || isNil(column) || isNil(rowIndex)) {
                        return;
                    }
                    if (!e.shiftKey) {
                        const phantomRow = api.getPinnedTopRow(0);
                        if (phantomRow?.data?.__dirty) {
                            onTelemetryEvent?.(`tablePhantomRowCommittedByBlur-${fieldId}`, {
                                screenId,
                                elementId: fieldId,
                            });

                            tryToCommitPhantomRow({
                                api,
                                screenId,
                                elementId: fieldId,
                                value: xtremRedux.getStore().getState().screenDefinitions[screenId].values[fieldId],
                            }).then(() => {
                                // jump to first available column excluding selection column
                                const firstColumn = (callGridMethod(api, 'getAllDisplayedColumns') ?? []).find(
                                    c => !INTERNAL_COLUMN_IDS.includes(c.getColId()),
                                );
                                if (isNil(firstColumn)) {
                                    return;
                                }
                                setTimeout(() => {
                                    callGridMethod(api, 'startEditingCell', {
                                        colKey: firstColumn.getColId(),
                                        rowIndex: 0,
                                        key: undefined,
                                        rowPinned: 'top',
                                    });
                                }, 100);
                            });
                        }
                    }
                }}
                onClick={async (): Promise<void> => {
                    await tableData.resetRowToDefaults({
                        id: row._id,
                        level: row.__level,
                        parentId: row.__parentId,
                        isOrganicChange: true,
                        resetDirtiness: true,
                    });
                }}
                disabled={false}
                aria-label="Cancel"
            >
                <Icon tooltipMessage="Cancel" type="cross_circle" color="--colorsUtilityYin030" fontSize="small" />
            </IconButton>
        );
    }, [api, column, fieldId, level, onTelemetryEvent, recordId, rowIndex, screenId, tableData]);

    if (tableData) {
        if (isPhantomRow) {
            return renderPhantomRowRemoveButton();
        }

        return (
            <XtremActionPopover
                items={menuItems}
                isDisabled={isDisabled}
                api={api}
                column={column}
                rowIndex={rowIndex}
                hasInlineActions={hasInlineActions}
                pendoId={`tableDropdownActionsButton-${screenId}-${fieldId}-${level}`}
            />
        );
    }
    return null;
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: TableDropdownActionExternalProps,
): TableDropdownActionProps => {
    const screenDefinition = getPageDefinitionFromState(props.screenId, state);
    const fieldProperties = screenDefinition.metadata.uiComponentProperties[
        props.fieldId
    ] as CollectionPropertiesWithActions;

    const tableData =
        props.fieldId === navigationPanelId
            ? screenDefinition.navigationPanel?.value
            : screenDefinition.values[props.fieldId];

    return {
        ...props,
        accessBindings: screenDefinition.accessBindings,
        fieldProperties,
        onTelemetryEvent: state.applicationContext?.onTelemetryEvent,
        tableData,
    };
};

export default connect(mapStateToProps)(TableDropdownActions);
