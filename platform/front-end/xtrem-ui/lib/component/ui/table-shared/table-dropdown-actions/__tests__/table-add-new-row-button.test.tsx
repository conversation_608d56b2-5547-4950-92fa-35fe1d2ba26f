import React from 'react';
import { fireEvent, render, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { DesktopTableAddNewRowButton } from '../../desktop-table-add-new-row-button';
import { Provider } from 'react-redux';
import { getMockStore } from '../../../../../__tests__/test-helpers';

jest.mock('../../../../container/footer/business-action', () => ({
    __esModule: true,
    default: ({ id }: { id: string }) => <div data-testid={`add-item-${id}`}>{id}</div>,
}));

describe('DesktopTableAddNewRowButton', () => {
    it('renders MultiActionButton when canAddNewLine is false and there are businessActions', async () => {
        const { getByTestId, queryByTestId } = render(
            <Provider store={getMockStore()}>
                <DesktopTableAddNewRowButton
                    addItemActions={
                        [
                            { id: 'action-1', title: 'Action 1' },
                            { id: 'action-2', title: 'Action 2' },
                        ] as any
                    }
                    canAddNewLine={false}
                    cardDefinition={undefined as any}
                    columns={[]}
                    elementId="test-table"
                    hasSidebar={false}
                    isDisabled={false}
                    level={0}
                    onFocusPhantomRow={jest.fn()}
                    screenId="screen-1"
                    sidebarDefinition={undefined}
                />
            </Provider>,
        );
        // Should render MultiActionButton
        expect(getByTestId('e-table-button-add-new-row-multi-action')).toBeInTheDocument();
        // Click on multi action button
        fireEvent.click(getByTestId('e-table-button-add-new-row-multi-action'));
        await waitFor(() => {
            // Should render add item actions
            expect(getByTestId('add-item-action-1')).toBeInTheDocument();
            expect(getByTestId('add-item-action-2')).toBeInTheDocument();
            // Should not render SplitButton
            expect(queryByTestId('e-table-button-add-new-row-phantom')).not.toBeInTheDocument();
        });
    });
});
