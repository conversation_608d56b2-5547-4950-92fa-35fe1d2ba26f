// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Desktop table print button should render the component if print settings are available, enabled and the table is the navigation panel 1`] = `
.c5 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c5::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c2 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: var(--colorsActionMajor500);
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
}

.c2:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c2 .c4 {
  color: var(--colorsActionMajor500);
}

.c2:hover {
  background: var(--colorsActionMajor600);
  border-color: var(--colorsActionMajorTransparent);
  color: var(--colorsActionMajorYang100);
}

.c2:hover .c4 {
  color: var(--colorsActionMajorYang100);
}

.c2 .c4 {
  margin-bottom: 0px;
  margin-left: var(--spacing100);
  margin-right: 0px;
  height: 20px;
  width: 20px;
}

.c2 .c4 svg {
  margin-top: 0;
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c0 {
  margin-right: 16px;
  display: inline-block;
  position: relative;
}

.c0 > .c1 {
  margin: 0;
}

.c0 > .c1 .c4 {
  margin-left: 0;
  left: 8px;
}

.c0 > .c1:focus {
  background-color: var(--colorsActionMajor700);
  border: 3px solid var(--colorsActionMajor700);
  outline: none;
  margin: 0 -1px;
}

.c0 > .c1:focus,
.c0 > .c1:focus .c4 {
  color: var(--colorsActionMajorYang100);
}

<div>
  <div
    class="c0"
    data-component="multi-action-button"
  >
    <button
      aria-expanded="false"
      aria-haspopup="true"
      class="c1 c2"
      data-component="button"
      data-element="toggle-button"
      data-testid="e-print-main-list-button"
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="c3"
          data-element="main-text"
        >
          Print
        </span>
      </span>
      <span
        aria-hidden="true"
        class="c4 c5"
        color="--colorsActionMajor500"
        data-component="icon"
        data-element="dropdown"
        data-role="icon"
        font-size="small"
        type="dropdown"
      />
    </button>
  </div>
</div>
`;
