jest.unmock('carbon-react/esm/__internal__/utils/helpers/guid/index');

import { render, fireEvent, waitFor } from '@testing-library/react';
import * as React from 'react';
import type { TableOptionsMenuProps } from '../table-options-menu';
import { TableOptionsMenu } from '../table-options-menu';
import * as routerService from '../../../../service/router';
import { navigationPanelId } from '../../../container/navigation-panel/navigation-panel-types';

describe('table options menu', () => {
    let props: TableOptionsMenuProps;
    let mockRouterGoTo: jest.Mock;

    beforeEach(() => {
        mockRouterGoTo = jest.fn();
        jest.spyOn(routerService, 'getRouter').mockReturnValue({ goTo: mockRouterGoTo } as any);
        props = {
            onSelectionChange: jest.fn(),
            screenId: 'TestPage',
            elementId: 'someField',
            setTableViewOptionsMenuItem: jest.fn(),
            selectedOptionsMenuItem: { title: 'Option 1', graphQLFilter: { title: { _eq: test } } },
            optionsMenuItems: [
                { title: 'Option 1', graphQLFilter: { title: { _eq: test } } },
                { title: 'Option 2', graphQLFilter: { title: { _gt: 5 } } },
                { title: 'Option 3', page: '@sage/xtrem-test/SomePage' },
            ],
        };
    });

    it('should render in dropdown mode', () => {
        const { queryByTestId, baseElement } = render(<TableOptionsMenu {...props} optionsMenuType="dropdown" />);
        expect(queryByTestId('e-option-item-menu--dropdown')).not.toBeNull();
        expect(baseElement.querySelector('[role="combobox"]')).not.toBeNull();
    });

    it('should render in dropdown mode by default', () => {
        const { queryByTestId, baseElement } = render(<TableOptionsMenu {...props} />);
        expect(queryByTestId('e-option-item-menu--dropdown')).not.toBeNull();
        expect(baseElement.querySelector('[role="combobox"]')).not.toBeNull();
    });

    it('should render in toggle mode', () => {
        const { queryByTestId, baseElement } = render(<TableOptionsMenu {...props} optionsMenuType="toggle" />);
        expect(queryByTestId('e-option-item-menu--toggle')).not.toBeNull();
        expect(baseElement.querySelector('[data-component="button-toggle-group"]')).not.toBeNull();
    });

    it('should render in tabs mode', () => {
        const { queryByTestId, baseElement } = render(<TableOptionsMenu {...props} optionsMenuType="tabs" />);
        expect(queryByTestId('e-option-item-menu--tabs')).not.toBeNull();
        expect(baseElement.querySelector('[role="tablist"]')).not.toBeNull();
    });

    it('should trigger the onSelectionChange callback when the user clicks selects in the dropdown', async () => {
        const { baseElement, queryAllByTestId } = render(<TableOptionsMenu {...props} optionsMenuType="dropdown" />);
        expect(props.onSelectionChange).not.toHaveBeenCalled();
        fireEvent.click(baseElement.querySelector('input')!);
        await waitFor(() => {
            expect(queryAllByTestId('e-ui-select-suggestion-value-wrapper')).toHaveLength(3);
        });

        fireEvent.click(queryAllByTestId('e-ui-select-suggestion-value-wrapper')[1]);
        await waitFor(() => {
            expect(props.onSelectionChange).toHaveBeenCalledTimes(1);
            expect(props.onSelectionChange).toHaveBeenCalledWith({
                title: 'Option 2',
                graphQLFilter: { title: { _gt: 5 } },
            });
        });
    });

    it('should trigger the setNavigationPanelOptionItem callback when the user clicks selects in the dropdown using the navigation panel', async () => {
        props.elementId = navigationPanelId;
        const { baseElement, queryAllByTestId } = render(<TableOptionsMenu {...props} optionsMenuType="dropdown" />);
        fireEvent.click(baseElement.querySelector('input')!);
        await waitFor(() => {
            expect(queryAllByTestId('e-ui-select-suggestion-value-wrapper')).toHaveLength(3);
        });

        fireEvent.click(queryAllByTestId('e-ui-select-suggestion-value-wrapper')[1]);
        await waitFor(() => {
            expect(props.setTableViewOptionsMenuItem).toHaveBeenCalledWith({
                title: 'Option 2',
                graphQLFilter: { title: { _gt: 5 } },
            });
        });
    });

    it('should trigger the router "goTo" function if the user clicks selects an element with a page option using the dropdown', async () => {
        const { baseElement, queryAllByTestId } = render(<TableOptionsMenu {...props} optionsMenuType="dropdown" />);
        expect(props.onSelectionChange).not.toHaveBeenCalled();
        fireEvent.click(baseElement.querySelector('input')!);
        await waitFor(() => {
            expect(queryAllByTestId('e-ui-select-suggestion-value-wrapper')).toHaveLength(3);
        });

        fireEvent.click(queryAllByTestId('e-ui-select-suggestion-value-wrapper')[2]);
        await waitFor(() => {
            expect(props.onSelectionChange).not.toHaveBeenCalled();
            expect(mockRouterGoTo).toHaveBeenCalledTimes(1);
            expect(mockRouterGoTo).toHaveBeenCalledWith('@sage/xtrem-test/SomePage');
        });
    });

    it('should trigger the onSelectionChange callback when the user clicks the toggle', async () => {
        const { queryByText } = render(<TableOptionsMenu {...props} optionsMenuType="toggle" />);
        expect(props.onSelectionChange).not.toHaveBeenCalled();
        fireEvent.click(queryByText('Option 2')!);
        await waitFor(() => {
            expect(props.onSelectionChange).toHaveBeenCalledTimes(1);
            expect(props.onSelectionChange).toHaveBeenCalledWith({
                title: 'Option 2',
                graphQLFilter: { title: { _gt: 5 } },
            });
        });
    });

    it('should trigger the router "goTo" function if the user selects an element with a page option', async () => {
        const { queryByText } = render(<TableOptionsMenu {...props} optionsMenuType="toggle" />);
        expect(props.onSelectionChange).not.toHaveBeenCalled();
        fireEvent.click(queryByText('Option 3')!);
        await waitFor(() => {
            expect(props.onSelectionChange).not.toHaveBeenCalled();
            expect(mockRouterGoTo).toHaveBeenCalledTimes(1);
            expect(mockRouterGoTo).toHaveBeenCalledWith('@sage/xtrem-test/SomePage');
        });
    });

    it('should trigger the onSelectionChange callback when the user clicks a tab', async () => {
        const { queryByTestId } = render(<TableOptionsMenu {...props} optionsMenuType="tabs" />);
        expect(props.onSelectionChange).not.toHaveBeenCalled();
        fireEvent.click(queryByTestId('e-xtrem-tab-option2', { exact: false })!);
        await waitFor(() => {
            expect(props.onSelectionChange).toHaveBeenCalledTimes(1);
            expect(props.onSelectionChange).toHaveBeenCalledWith({
                title: 'Option 2',
                graphQLFilter: { title: { _gt: 5 } },
            });
        });
    });
});
