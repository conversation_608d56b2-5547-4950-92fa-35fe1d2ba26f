import type { <PERSON>rid<PERSON><PERSON> } from '@ag-grid-community/core';
import type { Dict, LocalizeLocale } from '@sage/xtrem-shared';
import { objectKeys } from '@sage/xtrem-shared';
import { camelCase, isEmpty, noop } from 'lodash';
import * as React from 'react';
import { useSelector, useStore } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import type { OnTelemetryEventFunction } from '../../../redux/state';
import { confirmationDialog, pageDialog } from '../../../service/dialog-service';
import type { Graph<PERSON><PERSON>pi } from '../../../service/graphql-api';
import { queryToGraphQuery } from '../../../service/graphql-utils';
import { localize } from '../../../service/i18n-service';
import { showToast } from '../../../service/toast-service';
import { getSelectionFilter } from '../../../utils/ag-grid/ag-grid-table-utils';
import type { AgGridColumnConfigWithScreenIdAndColDef } from '../../../utils/ag-grid/ag-grid-utility-types';
import {
    BULK_ACTION_ASYNC_EXPORT,
    QUERY_PARAM_PRINTING_NODE_TYPE,
    QUERY_PARAM_PRINTING_SOURCE_PAGE,
} from '../../../utils/constants';
import { getDataTestIdAttribute } from '../../../utils/dom';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';
import {
    getNavigationPanelState,
    getNavigationPanelTablePropertiesFromPageDefinition,
    getPageDefinitionFromState,
} from '../../../utils/state-utils';
import type { BulkAction } from '../../container/container-control-objects';
import type { OptionsMenuItem } from '../../control-objects';
import { ButtonComponent } from '../../field/button/button-component';
import type { TableDecoratorProperties } from '../../field/table/table-component-types';

export interface DesktopTableBulkActionBarProps {
    gridApi: GridApi;
    selectedRowCount: number;
    bulkActions?: BulkAction[];
    screenId: string;
    onClearSelection: () => void;
    isSelectAllChecked: boolean;
    groupByColumn?: AgGridColumnConfigWithScreenIdAndColDef;
    onTelemetryEvent?: OnTelemetryEventFunction;
}

export function DesktopTableBulkActionBar({
    bulkActions,
    gridApi,
    isSelectAllChecked,
    selectedRowCount,
    screenId,
    onClearSelection,
    groupByColumn,
    onTelemetryEvent,
}: DesktopTableBulkActionBarProps): React.ReactElement {
    const store = useStore();

    const contextPage = useSelector<xtremRedux.XtremAppState, string>(
        s => getPageDefinitionFromState(screenId, s).path,
    );

    const exportTemplatesByNode = useDeepEqualSelector<xtremRedux.XtremAppState, Dict<Dict<string>> | undefined>(
        s => getPageDefinitionFromState(screenId, s).metadata.exportTemplatesByNode,
    );

    const graphApi = useDeepEqualSelector<xtremRedux.XtremAppState, GraphQLApi<any>>(
        s => getPageDefinitionFromState(screenId, s).page.$.graph,
    );

    const locale = useDeepEqualSelector<xtremRedux.XtremAppState, LocalizeLocale>(
        s => s.applicationContext?.locale as LocalizeLocale,
    );

    const tableFieldProperties = useDeepEqualSelector<xtremRedux.XtremAppState, TableDecoratorProperties>(s =>
        getNavigationPanelTablePropertiesFromPageDefinition(getPageDefinitionFromState(screenId, s)),
    );

    const activeOptionsMenuItem = useDeepEqualSelector<xtremRedux.XtremAppState, OptionsMenuItem | undefined>(s =>
        getNavigationPanelState(screenId, s)?.value?.getActiveOptionsMenuItem(),
    );

    const node = React.useMemo(() => {
        const pageNode = tableFieldProperties.node?.toString();
        if (!pageNode) {
            throw new Error(`Page ${screenId} does not have a node defined.`);
        }
        return pageNode;
    }, [screenId, tableFieldProperties.node]);

    const getBulkActionParameters = React.useCallback(
        async (bulkAction: BulkAction): Promise<Dict<any> | false> => {
            if (
                exportTemplatesByNode &&
                bulkAction.mutation === BULK_ACTION_ASYNC_EXPORT &&
                exportTemplatesByNode[node] &&
                objectKeys(exportTemplatesByNode[node]).length === 1
            ) {
                // If the bulk action is an async export action and the page has exactly one template, then we skip the dialog
                return { id: objectKeys(exportTemplatesByNode[node])[0] };
            }

            if (bulkAction.configurationPage) {
                try {
                    const result = await pageDialog(
                        store,
                        bulkAction.configurationPage,
                        {
                            [QUERY_PARAM_PRINTING_SOURCE_PAGE]: screenId,
                            [QUERY_PARAM_PRINTING_NODE_TYPE]: node || '',
                            contextPage,
                            node,
                        },
                        { size: 'medium', height: 250 },
                    );

                    if (!isEmpty(result)) {
                        return result as Dict<any>;
                    }
                    return false;
                } catch {
                    // If the custom dialog is rejected we stop the mutation process gracefully
                    return false;
                }
            }

            return {};
        },
        [contextPage, exportTemplatesByNode, node, screenId, store],
    );

    const handleBulkAction = React.useCallback(
        async (bulkAction: BulkAction): Promise<void> => {
            try {
                if (selectedRowCount <= 0) {
                    return;
                }
                onTelemetryEvent?.(`tableBulkActionTriggered-${bulkAction.id || camelCase(bulkAction.title)}`, {
                    screenId,
                    id: bulkAction.id || bulkAction.title,
                });

                const parameters = await getBulkActionParameters(bulkAction);
                if (parameters === false) {
                    // The user rejected the dialog
                    return;
                }

                await confirmationDialog(
                    screenId,
                    'warn',
                    bulkAction.title,
                    localize(
                        '@sage/xtrem-ui/bulk-action-dialog-content',
                        'Perform this action on the selected items: {{itemCount}}',
                        { itemCount: selectedRowCount },
                    ),
                    {
                        acceptButton: {
                            text: localize('@sage/xtrem-ui/ok', 'OK'),
                        },
                        cancelButton: {
                            text: localize('@sage/xtrem-ui/cancel', 'Cancel'),
                        },
                        size: 'small',
                    },
                );

                const filter = getSelectionFilter({
                    groupByColumn,
                    gridApi,
                    screenId,
                    isSelectAllChecked,
                    tableFieldProperties,
                    activeOptionsMenuItem,
                });

                const targetNode = graphApi.node(node);
                try {
                    const mergeParameters = {
                        filter: JSON.stringify(filter),
                        ...parameters,
                    };
                    if (bulkAction.isGlobal) {
                        // TODO: Remove this when the global bulk actions are implemented in the Client Gen API
                        const stringQuery = queryToGraphQuery({
                            mutation: {
                                global: {
                                    [bulkAction.mutation]: {
                                        start: {
                                            __args: mergeParameters,
                                            trackingId: true,
                                        },
                                    },
                                },
                            },
                        });
                        await graphApi.raw(stringQuery);
                    } else {
                        await targetNode.asyncOperations?.[bulkAction.mutation]
                            ?.start({ trackingId: true }, mergeParameters)
                            .execute();
                    }
                    onClearSelection();
                    showToast(localize('@sage/xtrem-ui/bulk-action-started', 'Action started on the selected items.'), {
                        type: 'success',
                    });
                } catch (_err) {
                    showToast(
                        localize('@sage/xtrem-ui/bulk-action-error', 'Action could not be started, please try again.'),
                        { type: 'error' },
                    );
                }
            } catch (error) {
                // User dismissed dialog, intentionally left empty
            }
        },
        [
            selectedRowCount,
            getBulkActionParameters,
            screenId,
            graphApi,
            node,
            onClearSelection,
            onTelemetryEvent,
            activeOptionsMenuItem,
            gridApi,
            groupByColumn,
            isSelectAllChecked,
            tableFieldProperties,
        ],
    );

    return (
        <div className="e-page-navigation-panel-bulk-actions-bar-wrapper">
            <div className="e-page-navigation-panel-bulk-actions-bar">
                <span
                    className="e-page-navigation-panel-bulk-actions-bar-selected-items"
                    data-testid="e-table-field-bulk-actions-bar-selected-items"
                >
                    {localize('@sage/xtrem-ui/bulk-actions-bar-selected', 'Items selected: {{count}}', {
                        count: selectedRowCount,
                    })}
                </span>
                {bulkActions?.map(bulkAction => {
                    const handleButtonClick = (): void => {
                        handleBulkAction(bulkAction);
                    };

                    const { mutation, title, buttonType, icon, isDestructive } = bulkAction;

                    return (
                        <div
                            key={mutation}
                            data-testid={getDataTestIdAttribute('table', title, mutation, 'bulk-action')}
                        >
                            <ButtonComponent
                                screenId={screenId}
                                elementId={mutation}
                                locale={locale}
                                onFocus={noop}
                                fieldProperties={{
                                    title,
                                    isTitleHidden: true,
                                }}
                                isNested={true}
                                onClick={handleButtonClick}
                                buttonType={buttonType}
                                icon={icon}
                                isDestructive={isDestructive}
                                value={title}
                            />
                        </div>
                    );
                })}
                <button
                    className="e-page-navigation-panel-bulk-actions-bar-clear-selection"
                    data-testid="e-table-field-bulk-actions-bar-clear-selection"
                    type="button"
                    onClick={onClearSelection}
                >
                    {localize('@sage/xtrem-ui/clear-selection', 'Clear selection')}
                </button>
            </div>
        </div>
    );
}
