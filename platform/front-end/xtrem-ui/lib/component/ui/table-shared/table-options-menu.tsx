import ButtonToggle from 'carbon-react/esm/components/button-toggle/button-toggle.component';
import ButtonToggleGroup from 'carbon-react/esm/components/button-toggle/button-toggle-group';
import { isEqual, kebabCase } from 'lodash';
import React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import { getRouter } from '../../../service/router';
import { navigationPanelId } from '../../container/navigation-panel/navigation-panel-types';
import type { OptionsMenuItem, TableOptionsMenuType } from '../../container/page/page-types';
import { Select } from '../select/select-component';
import type { SelectItem } from '../select/select-component';
import { XtremTabs } from '../tabs/xtrem-tabs';
import type { XtremTabItem } from '../tabs/xtrem-tabs';
import type { TableUserSettings } from '../../field/table/table-component-types';
import type { OnTelemetryEventFunction } from '../../../redux/state';

export interface TableOptionsMenuExternalProps {
    elementId: string;
    onSelectionChange: (selectedItem: OptionsMenuItem) => void;
    optionsMenuItems: OptionsMenuItem[];
    optionsMenuType?: TableOptionsMenuType;
    screenId: string;
    selectedOptionsMenuItem?: OptionsMenuItem;
    onTelemetryEvent?: OnTelemetryEventFunction;
}

export interface TableOptionsMenuProps extends TableOptionsMenuExternalProps {
    setTableViewOptionsMenuItem: (optionsMenuItem: OptionsMenuItem) => void;
}

export function TableOptionsMenu({
    elementId,
    onSelectionChange,
    optionsMenuItems,
    optionsMenuType = 'dropdown',
    screenId,
    selectedOptionsMenuItem,
    setTableViewOptionsMenuItem,
    onTelemetryEvent,
}: TableOptionsMenuProps): React.ReactElement | null {
    const onChange = React.useCallback(
        (newOptionItem: OptionsMenuItem): void => {
            if (newOptionItem.graphQLFilter) {
                if (elementId === navigationPanelId) {
                    setTableViewOptionsMenuItem(newOptionItem);
                }
                onSelectionChange(newOptionItem);
            } else if (newOptionItem.page) {
                getRouter(screenId).goTo(newOptionItem.page);
            }
        },
        [onSelectionChange, screenId, setTableViewOptionsMenuItem, elementId],
    );

    const onDropDownSelected = React.useCallback(
        (selectedItem: SelectItem): void => {
            if (!selectedItem) {
                return;
            }

            onTelemetryEvent?.(`tableOptionsMenuSelected-${selectedItem.id}`, {
                screenId,
                id: selectedItem.id,
            });

            const selectedFilter = optionsMenuItems.find(filter => filter.title === selectedItem.id) as OptionsMenuItem;
            onChange(selectedFilter);
        },
        [screenId, optionsMenuItems, onChange, onTelemetryEvent],
    );

    const onToggleChanged = React.useCallback(
        (_evt: React.MouseEvent<HTMLButtonElement>, value?: string) => {
            const selectedFilter = optionsMenuItems.find(filter => filter.title === value) as OptionsMenuItem;
            onChange(selectedFilter);
        },
        [optionsMenuItems, onChange],
    );

    const onTabChanged = React.useCallback(
        (tabId: string) => {
            const selectedFilter = optionsMenuItems.find(
                filter => kebabCase(filter.title) === tabId,
            ) as OptionsMenuItem;
            onChange(selectedFilter);
        },
        [optionsMenuItems, onChange],
    );

    const getSelectItems = React.useCallback(
        (): Promise<SelectItem[]> =>
            Promise.resolve(
                optionsMenuItems.map(option => ({
                    id: option.title,
                    value: option.title,
                    pendoId: option.id ? `tableOptionsMenu-${elementId}-${option.id}` : undefined,
                })),
            ),
        [elementId, optionsMenuItems],
    );

    // We should only render the filter selected component if there are more than 1 options.
    if (!selectedOptionsMenuItem || !optionsMenuItems || optionsMenuItems.length < 2) {
        return null;
    }

    if (optionsMenuType === 'dropdown') {
        return (
            <div
                className="e-option-item-menu"
                data-testid="e-option-item-menu--dropdown"
                data-options-menu-type="dropdown"
            >
                <Select
                    getItems={getSelectItems}
                    hasInputSearch={false}
                    initialInputValue={selectedOptionsMenuItem.title}
                    minLookupCharacters={0}
                    onSelected={onDropDownSelected}
                    selectedItem={{ id: selectedOptionsMenuItem.title, value: selectedOptionsMenuItem.title }}
                />
            </div>
        );
    }

    if (optionsMenuType === 'tabs') {
        const tabs: XtremTabItem[] = optionsMenuItems.map(item => {
            return {
                id: kebabCase(item.title),
                title: item.title,
                icon: item.icon,
            };
        });

        const selectedTabId = kebabCase(selectedOptionsMenuItem?.title || optionsMenuItems[0]?.title);

        return (
            <div className="e-option-item-menu" data-testid="e-option-item-menu--tabs" data-options-menu-type="tabs">
                <XtremTabs onTabChange={onTabChanged} tabs={tabs} selectedTabId={selectedTabId} />
            </div>
        );
    }

    // In toggle mode, we only render the first 3 items.
    return (
        <div className="e-option-item-menu" data-testid="e-option-item-menu--toggle" data-options-menu-type="toggle">
            <ButtonToggleGroup
                id="table-options-toggle-group"
                value={selectedOptionsMenuItem.title}
                onChange={onToggleChanged}
            >
                {optionsMenuItems.slice(0, 3).map((value: OptionsMenuItem) => (
                    <ButtonToggle buttonIcon={value.icon} key={value.title} value={value.title}>
                        {value.title}
                    </ButtonToggle>
                ))}
            </ButtonToggleGroup>
        </div>
    );
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: TableOptionsMenuExternalProps,
): TableOptionsMenuProps => {
    const tableViews = state.screenDefinitions[props.screenId]?.userSettings?.[props.elementId];
    const savedTableViewMenuItem = (tableViews?.$current as TableUserSettings)?.content?.[0]?.optionsMenuItem;

    if (props.elementId === navigationPanelId && savedTableViewMenuItem) {
        const selectedItem = props.optionsMenuItems.find(item => isEqual(item, savedTableViewMenuItem));

        return {
            ...props,
            selectedOptionsMenuItem: selectedItem || props.optionsMenuItems?.[0],
            setTableViewOptionsMenuItem: xtremRedux.actions.actionStub,
        };
    }

    return {
        ...props,
        setTableViewOptionsMenuItem: xtremRedux.actions.actionStub,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: TableOptionsMenuExternalProps,
): Partial<TableOptionsMenuProps> => {
    return {
        setTableViewOptionsMenuItem: (optionsMenuItem: OptionsMenuItem): void => {
            dispatch(
                xtremRedux.actions.setTableViewOptionMenuItem(props.screenId, props.elementId, 0, optionsMenuItem),
            );
        },
    };
};

export const ConnectedTableOptionsMenu = connect(mapStateToProps, mapDispatchToProps)(TableOptionsMenu);
