@import "../../../render/style/variables.scss";
@import "../../../render/style/mixins.scss";
@import '~@ag-grid-community/styles/ag-grid.css';
@import '~@ag-grid-community/styles/ag-theme-balham.css';

$border-color: #ccd6da;
$focus-shadow: 0 0 0 2px var(--colorsSemanticFocus500);
$input-border-color: #547081;
$header-cell-label-color: #000000E5;

.e-field {
    --ag-header-background-color: #335c6d;
    --ag-header-cell-moving-background-color: #335c6d;
    --ag-header-foreground-color: var(--colorsYang100);
    --ag-header-column-separator-color: var(--colorsYang100);
    --ag-row-hover-color: var(--colorsUtilityMajor050);
    --ag-selected-row-background-color: var(--colorsActionMajor500);
    --ag-balham-active-color: var(--colorsSemanticFocus500);
    --ag-range-selection-border-color: #{$border-color};
    --ag-input-focus-border-color: var(--colorsSemanticFocus500);
    --ag-input-border-color: var(--colorsSemanticFocus500);
    --ag-border-color: #{$border-color};
    --ag-row-border-color: #{$border-color};
    // flash color
    --ag-value-change-value-highlight-background-color: #d9ece7;
}

.e-field.e-disabled .ag-theme-balham {
    --ag-header-background-color: var(--colorsUtilityMajor300);
    --ag-row-border-color: #dadcde;

    .e-pill-wrapper {
        opacity: 0.65;
    }
}

.e-field .ag-theme-balham {

    .ag-row-group-indent-0 {
        flex: 1;
    }

    .ag-row-group-indent-1 {
        flex: 1;
    }

    .ag-row-group-indent-2 {
        flex: 1;
    }

    .ag-row-group-indent-3 {
        flex: 1;
    }

    .ag-row-group-indent-4 {
        flex: 1;
    }

    .ag-row-group-indent-5 {
        flex: 1;
    }

    .ag-row-group-indent-6 {
        flex: 1;
    }

    .ag-row-group-indent-7 {
        flex: 1;
    }

    .ag-row-group-indent-8 {
        flex: 1;
    }

    .ag-row-group-indent-9 {
        flex: 1;
    }

    .ag-row-group-indent-10 {
        flex: 1;
    }

    .e-table-field-line-number {
        background-color: var(--colorsUtilityMajor050);
        font-family: var(--fontFamiliesDefault);
    }

    .e-table-field-line-number:hover {
        background-color: var(--colorsUtilityMajor100);
    }

    .ag-header {
        background-color: var(--colorsUtilityMajor100);
    }

    .ag-menu-header {
        background-color: var(--colorsUtilityMajor150);
        height: 35px;
    }

    .ag-menu-option {
        height: 40px;
        font-size: 14px;
        font-weight: 700;

        .ag-menu-option-popup-pointer {
            .ag-icon {
                font-weight: 700;
            }
        }
    }

    .ag-root.ag-layout-auto-height {
        min-height: 282px;
    }

    .ag-details-row-auto-height .ag-root.ag-layout-auto-height {
        // The min height rule from above should not be extended to child levels of the nested grid
        min-height: unset;
    }

    // Intentionally kept here, we are waiting for UX confirmation
    //.ag-side-buttons {
    //    border-left: 1px solid var(--colorsUtilityMajor400);
    //}


    [data-ref="btLast"],
    [data-ref="btFirst"] {
        display: none;
    }

    .ag-floating-filter {
        padding: 4px 9px;

        &:focus {
            background-color: var(--colorsUtilityMajor150);
        }

        .ag-floating-filter-button {
            margin-left: 4px;

            .ag-floating-filter-button-button {

                &:active,
                &:focus {
                    box-shadow: $focus-shadow;
                }

                .ag-icon-filter::before {
                    color: var(--colorsUtilityMajor400);
                }
            }
        }

        .ag-floating-filter-body {
            color: var(--colorsYin090);

            .ag-input-wrapper {
                height: 32px;

                input {
                    height: 100%;
                    font-size: 14px;
                    font-family: var(--fontFamiliesDefault);
                    border: 1px solid $input-border-color;
                    border-radius: var(--borderRadius050);

                    &:active,
                    &:focus {
                        box-shadow: $focus-shadow;
                    }
                }
            }
        }
    }

    // Center nested labels
    .e-pill-wrapper {
        line-height: 24px;
        display: block;
    }

    // Display horizontal scrollbar at the bottom when no rows are found
    & .ag-root>.ag-body-horizontal-scroll {
        order: 2;
    }

    & .ag-layout-auto-height .ag-center-cols-clipper,
    & .ag-layout-auto-height .ag-center-cols-container,
    & .ag-layout-print .ag-center-cols-clipper,
    & .ag-layout-print .ag-center-cols-container {
        min-height: unset;
    }

    .ag-overlay {
        // The order must be set so the "empty table" placeholder is displayed before the floating editable row
        order: 1;
        position: relative;
    }

    .ag-cell-wrapper.ag-row-group {
        align-items: center;
        flex: 1;
        max-width: 100%;
        overflow: hidden;
    }

    div[data-component="action-popover-wrapper"] {
        margin: auto 0;
        line-height: 24px;
    }

    .e-table-field-actions-container {
        &:focus {
            box-shadow: inset 0px 0px 0px 2px var(--colorsSemanticFocus500);
            outline: none;
        }

        text-align: center;
        align-items: center;
        justify-content: space-evenly;
        display: flex;
        flex: 1;


        &>* {
            flex-basis: 24px;
        }
    }

    .e-table-field-dropdown-actions-cell {
        text-align: center;
        justify-content: space-evenly !important;

    }

    .ag-ltr .ag-header-select-all:not(.ag-hidden) {
        &+.ag-header-cell-comp-wrapper {
            display: none;
        }
        margin: auto;
    }

    .ag-ltr .ag-side-bar-right {
        .ag-side-button-button {
            background: var(--colorsYang100);

            &:hover {
                background: var(--colorsUtilityMajor100);
                border-top-color: var(--colorsUtilityMajor100);
                border-bottom-color: var(--colorsUtilityMajor100);
            }
        }

        .ag-selected .ag-side-button-button {
            border-left-color: var(--colorsActionMajor500);
            border-left-width: 3px;
            box-sizing: border-box;
            border-top: none;
            border-bottom: none;
        }
    }

    .e-table-field-validation-summary-header::after {
        display: none;
    }

    .ag-column-select-virtual-list-viewport {
        padding: 12px 0;
    }

    .ag-column-select-virtual-list-item {
        margin: 0;
        padding: 0 0 0 16px;
    }

    .ag-side-button-icon-wrapper {
        margin-bottom: 8px;

        .ag-icon-columns::before {
            font-family: $fontCarbonIcons;
            content: "\e96d";
            font-size: 10px;
        }
    }

    .ag-header-cell-moving {
        border: 2px solid var(--colorsYang100);
        background-color: var(--colorsUtilityMajor150);

        .ag-header-cell-resize {
            display: none;
        }

        &::after {
            display: none;
        }
    }

    .ag-ltr .ag-cell {
        border: 0;
        border-right: none;
        border-left: none;
        border-top: 1px solid transparent;
        border-bottom: 1px solid transparent;
        line-height: 28px;
        font-size: 14px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .ag-ltr .ag-user-row-group .ag-cell {
        color: var(--utility-yin-090, rgba(0, 0, 0, 0.90));
        font-family: "Sage UI";
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
    }

    .ag-floating-top-container .ag-row-no-focus {
        opacity: 0.5;
    }

    .ag-floating-top-container .ag-row-no-focus.ag-row-hover {
        opacity: 1;
    }

    .ag-ltr .ag-cell.ag-cell-inline-editing {
        border: none;
        background: none;
    }

    .ag-ltr .ag-row-focus.ag-row-inline-editing .ag-cell.e-nested-cell-editable,
    .ag-has-focus .ag-floating-top .ag-cell-inline-editing,
    .ag-ltr .ag-has-focus .ag-cell-inline-editing {
        cursor: text;
        padding: 3px 8px !important;
        box-shadow: none !important;
        border: none;
        background: transparent;

        .e-grid-error-indicator,
        .ag-cell-edit-wrapper {
            border: 1px solid $input-border-color;
            height: 32px;
            background-color: var(--colorsYang100);
            border-radius: calc(var(--borderRadius050) + 1px);

            input {
                font-size: 14px;

                &:active,
                &:focus {
                    box-shadow: none;
                }
            }
        }

        &.ag-cell-focus {
            box-shadow: none;
            border: none;

            .e-grid-error-indicator {
                box-shadow: $focus-shadow;
            }
        }
    }

    .ag-ltr .ag-cell-inline-editing .ag-cell-edit-wrapper {
        box-shadow: $focus-shadow;
        border-radius: 0;
    }

    // Highlight the control element and not the cell borders
    .ag-ltr .ag-has-focus .ag-cell.ag-cell-focus.e-ag-cell-focus--no-border.e-table-field-dropdown-actions-cell {
        box-shadow: none;
        border-bottom: 1px solid transparent;
        border-top: 1px solid transparent;
        border-left: 1px solid $border-color;
        border-right: 1px solid transparent;
    }

    .ag-floating-top .ag-row-no-focus .ag-cell.e-table-field-dropdown-actions-cell button {
        display: none;
    }

    // fix alignment of svg which is absolutely positioned
    .e-nested-cell-field-checkbox {
        svg {
            left: 0;
        }
    }

    // center switch which is absolutely positioned
    .e-nested-cell-field-switch input[role="switch"]+span {
        right: 0;
        margin: auto;
    }

    .ag-ltr .ag-has-focus .ag-cell.ag-cell-focus.e-table-field-select-row,
    .ag-ltr .ag-has-focus .ag-cell.ag-cell-focus.e-nested-cell-field-checkbox,
    .ag-ltr .ag-has-focus .ag-cell.ag-cell-focus.e-nested-cell-field-link,
    .ag-ltr .ag-has-focus .ag-cell.ag-cell-focus.e-nested-cell-field-switch {
        box-shadow: none;

        // Checkbox and switch internal selectors
        input+div,
        input+span {
            box-shadow: $focus-shadow;
        }
    }

    .ag-ltr .ag-has-focus .ag-cell.ag-cell-focus.e-table-field-select-row .ag-cell-wrapper {
        box-shadow: $focus-shadow;
    }

    .ag-ltr .ag-cell.e-table-field-validation-summary {
        border-right: none;

        .ag-react-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 100%;
        }
    }

    .ag-ltr input.ag-input-field-input[class^="ag-"][type="text"] {
        padding-left: 11px;
        border-radius: var(--borderRadius050);
    }

    .ag-checkbox-input-wrapper:focus-within {
        box-shadow: none;
    }

    .ag-checkbox-input-wrapper {
        font-size: 12px;
        border: 1px solid var(--colorsUtilityMajor300);
        border-radius: var(--borderRadius025);

        &:hover,
        &:focus,
        &:active {
            outline: 3px solid var(--colorsSemanticFocus500);
        }

        &::after {
            font-family: $fontCarbonIcons;
            width: 100%;
            text-align: center;

        }

        &.ag-checked.ag-partial::after {
            top: -1px;
        }
    }

    .ag-center-cols-container {
        .e-row-is-edited-no-select.ag-row-not-inline-editing {
            border-left: 3px solid var(--colorsSemanticFocus500);
        }
    }

    .ag-pinned-left-cols-container {
        .ag-row-edited {
            border-left: 3px solid var(--colorsSemanticFocus500);

            // avoid cell pushed right:
            .ag-cell.e-table-field-select-row {
                width: 38px !important;
            }

            .ag-cell.e-table-field-validation-summary {
                width: 22px !important;
            }
        }
    }

    .ag-floating-top-container {

        .ag-cell.e-nested-cell-editable {

            .e-grid-error-indicator,
            .ag-cell-edit-wrapper {
                border: 1px solid rgba(102, 132, 148, 0.25);
                height: 32px;
                cursor: pointer;
                border-radius: var(--borderRadius100);
            }

        }
    }

    .ag-row-even {
        background-color: var(--colorsYang100);

        &.ag-row-hover {
            background: #D9ECE7;

            &>.ag-cell.e-nested-cell-editable {

                .e-grid-error-indicator,
                .ag-cell-edit-wrapper {
                    background: #e3f1ed;
                    border: 1px solid rgba(102, 132, 148, 0.25);
                    cursor: pointer;
                    border-radius: var(--borderRadius100);

                }
            }

            >.e-table-field-line-number {
                background-color: var(--colorsUtilityMajor100);
            }
        }

        &.ag-row-selected {
            background-color: var(--colorsUtilityMajor050);
            color: var(--colorsYin090);
        }

        &.ag-row-selected.ag-row-hover {
            .ag-cell.e-nested-cell-editable {

                .e-grid-error-indicator,
                .ag-cell-edit-wrapper {
                    background: transparent;
                    cursor: pointer;
                }
            }
        }

        &.ag-row-edited.ag-row-not-inline-editing {
            animation: blink-animation 1s infinite;
            -webkit-animation: blink-animation 1s infinite;
            animation-iteration-count: 3;

            @keyframes blink-animation {
                to {
                    background-color: var(--colorsSemanticInfo150);
                }
            }

            @-webkit-keyframes blink-animation {
                to {
                    background-color: var(--colorsSemanticInfo150);
                }
            }
        }
    }

    .ag-row-odd {
        background-color: var(--colorsYang100);

        &.ag-row-hover {
            background-color: #D9ECE7;

            &>.ag-cell.e-nested-cell-editable {

                .e-grid-error-indicator,
                .ag-cell-edit-wrapper {
                    background: #e3f1ed;
                    border: 1px solid rgba(102, 132, 148, 0.25);
                    cursor: pointer;
                    border-radius: var(--borderRadius100);
                }
            }

            >.e-table-field-line-number {
                background-color: var(--colorsUtilityMajor100);
            }
        }

        &.ag-row-selected {
            background-color: var(--colorsUtilityMajor050);
            color: var(--colorsYin090);
        }

        &.ag-row-selected.ag-row-hover {
            .ag-cell.e-nested-cell-editable {

                .e-grid-error-indicator,
                .ag-cell-edit-wrapper {
                    background: transparent;
                    cursor: pointer;
                }
            }
        }

        &.ag-row-edited.ag-row-not-inline-editing {
            animation: blink-animation 1s infinite;
            -webkit-animation: blink-animation 1s infinite;
            animation-iteration-count: 3;

            @keyframes blink-animation {
                to {
                    background-color: var(--colorsSemanticInfo150);
                }
            }

            @-webkit-keyframes blink-animation {
                to {
                    background-color: var(--colorsSemanticInfo150);
                }
            }
        }
    }

    .ag-header-cell {
        font-family: var(--fontFamiliesDefault);
        font-weight: var(--fontWeights500);

        &.hidden {
            display: none;
        }

        &::before {
            background-color: #{$border-color};
        }

        .ag-header-icon {
            color: var(--colorsUtilityMajor300);

            & .ag-icon-menu::before {
                font-family: $fontCarbonIcons;
                font-size: 16px;
                content: '\e98a';
                color: var(--colorsUtilityMajor300);
            }

            & .ag-icon-asc,
            & .ag-icon-desc {
                color: var(--ag-header-foreground-color, var(--ag-secondary-foreground-color, rgba(0, 0, 0, 0.54)));
            }
        }

        [class^="ag-"]:before {
            color: var(--colorsUtilityMajor300);
        }

        &.e-table-field-mandatory .ag-header-cell-label::after {
            content: '\00a0*';
        }
    }

    .ag-header-cell.ag-header-active {
        background-color: var(--colorsUtilityMajor150) !important;
    }

    .ag-header-cell::after {
        background: none;
    }

    .ag-header-cell:focus-visible::after {
        border-color: var(--colorsSemanticFocus500);
    }

    .ag-ltr .ag-has-focus .ag-cell.ag-cell-focus {
        box-shadow: inset 0px 0px 0px 2px var(--colorsSemanticFocus500);
        border: none;

        .ag-cell-editor .ag-text-field-input {
            border: transparent;
        }
    }

    &>.ag-popup-child {
        box-shadow: none !important;

        &.ag-menu {
            overflow: visible;
            border-radius: var(--borderRadius100);

            span[role="tab"] {
                border-top-right-radius: var(--borderRadius100);
                border-top-left-radius: var(--borderRadius100);
            }
        }
    }

    // Remove padding for action column in edit mode
    &>.ag-popup-editor:has(>.e-table-field-actions-container) {
        padding: 0;
    }

    &>.ag-popup-editor {
        background-color: var(--ag-background-color);
        background-clip: content-box;
        border-radius: calc(2 * var(--borderRadius100));
        border: none;
        height: 39px;


        display: flex;
        align-items: center;
        justify-content: center;

        .e-reference-cell-editor,
        .e-filter-select-cell-editor {
            border: 1px solid $input-border-color;
            height: 32px;
            box-sizing: border-box;
            outline: 2px solid var(--colorsSemanticFocus500);
            border-radius: var(--borderRadius100);

            .e-ui-select-input {
                border-radius: var(--borderRadius100);
            }
        }

        .e-ui-select-input-container {
            border-radius: var(--borderRadius100);
        }
    }

    .ag-tab:not(.ag-tab-selected) {
        color: var(--colorsYang100);
    }

    .e-grid-error-indicator {
        box-sizing: border-box;
        height: 100%;
        width: 100%;
        border-radius: var(--borderRadius100);

        span::before {
            cursor: default;
            font-size: 14px;
        }
    }

    .ag-center-cols-viewport {
        scrollbar-width: none;

        &::-webkit-scrollbar {
            width: 0;
            height: 0;
        }
    }

    .ag-header-cell-label {
        font-family: var(--fontFamiliesDefault);
        font-weight: var(--fontWeights500);
        font-size: 14px;
        color: $header-cell-label-color;
    }

    .ag-column-select-column {
        font-size: 14px;
    }

    .ag-ltr .ag-cell.e-nested-cell-field-progress {
        padding: 2px 8px !important;

        .e-grid-error-indicator {
            padding: 0 !important;
        }
    }

    .ag-cell-wrapper.ag-cell-expandable {
        height: 39px;

        .ag-group-contracted,
        .ag-group-expanded {
            margin-left: 6px;
            margin-right: 6px;
        }
    }

    .ag-header-row:not(:first-child) .ag-header-cell:not(.ag-header-span-height.ag-header-span-total) {
        border: none;
    }

    .ag-pinned-left-header {
        border: none;
    }
}