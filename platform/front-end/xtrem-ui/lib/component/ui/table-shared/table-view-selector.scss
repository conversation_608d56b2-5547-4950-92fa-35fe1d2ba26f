.e-table-view-selector {
    font-family: var(--fontFamiliesDefault);
    font-size: 14px;
    position: relative;
}

.e-table-view-selector-button {
    all: unset;

    &:focus {
        outline: none;
        box-shadow: inset 0px 0px 0px 2px var(--colorsSemanticFocus500);
    }

    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    padding: 0 2px;
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    cursor: pointer;
}

.e-table-view-selector-input {
    width: 200px;
    display: flex;
    border: 1px solid var(--colorsUtilityMajor300);
    border-radius: var(--borderRadius050);
    background-color: var(--colorsUtilityYang100);
    box-sizing: border-box;
    height: 40px;
    padding: 0 24px 0 12px;

    &:focus-visible {
        outline: none;
    }

    &:focus,
    [aria-expanded="true"] {
        box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500), 0px 0px 0px var(--borderWidth600);
    }
}

.e-table-view-selector-dropdown {
    max-height: 346px;
    top: 40px;
    margin: 0;
    position: absolute;
    cursor: pointer;
    padding: 0;
    scroll-snap-type: y mandatory;
    overflow: auto;
    overflow-y: auto;
    background-color: #fff;
    box-shadow: var(--boxShadow100);
    z-index: 98;
    width: 100%;
    display: flex;
    flex-direction: column;
    font-family: var(--fontFamiliesDefault);

    &:focus-visible {
        outline: none;
    }

    &.e-table-view-selector-dropdown-hidden {
        display: none;
    }
}

.e-table-view-selector-input-label {
    flex: 1;
}

.e-table-view-selector-item {
    height: 40px;
    padding: 0 16px;
    font-family: var(--fontFamiliesDefault);
    font-size: var(--fontSizes100);
    font-style: normal;
    list-style: none;
    line-height: 40px;
    box-sizing: border-box;
    white-space: nowrap;

    .e-table-view-selector-item-content {
        display: flex;
        align-items: center;
    }

    &.e-table-view-selector-item-selected {
        font-weight: var(--fontWeights700);
    }

    &.e-table-view-selector-item-highlighted {
        background-color: rgb(230, 235, 237);
    }
}

.e-table-view-selector-item-group {
    padding: 0;
    margin: 0;
    border-bottom: 1px solid var(--colorsUtilityMajor100);
    color: var(--colorsYin090);

    &:last-child {
        border-bottom: none;
    }

    &.e-table-view-selector-item-group-views {
        overflow-y: auto;

        .e-table-view-selector-item .e-table-view-selector-item-content {
            display: block;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }

    &.e-table-view-selector-item-group-actions {
        color: var(--colorsActionMajor500);
        font-weight: 500;

        .e-table-view-selector-item {
            height: 48px;
            line-height: 48px;
            border-bottom: 1px solid var(--colorsUtilityMajor100);

            &:last-child {
                border-bottom: none;
            }
        }
    }
}
