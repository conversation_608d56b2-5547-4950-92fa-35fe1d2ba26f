import * as React from 'react';
import { navigationPanelId } from '../../container/navigation-panel/navigation-panel-types';
import { useDispatch } from 'react-redux';
import * as xtremRedux from '../../../redux';
import { DEFAULT_VIEW_ID } from '../../../utils/constants';
import { localize } from '../../../service/i18n-service';
import { fetchUserClientSettingsForElement } from '../../../service/user-client-settings-service';
import type { OnTelemetryEventFunction, UiComponentUserSettings } from '../../../redux/state';
import { selectMainListView, storeUserClientSettings, unselectMainListView } from '../../../redux/actions';
import { xtremConsole } from '../../../utils/console';
import { showToast } from '../../../service/toast-service';
import { kebabCase } from 'lodash';
import type { UseSelectGetItemPropsOptions } from 'downshift';
import { useSelect } from 'downshift';
import Icon from 'carbon-react/esm/components/icon';
import type { Dict, IconType } from '@sage/xtrem-shared';
import * as tokens from '@sage/design-tokens/js/base/common';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

export interface TableViewSelectorProps {
    screenId: string;
    elementId: string;
}

interface ViewSelectorDropdownItem extends UiComponentUserSettings {
    isAction?: boolean;
    onClick?: () => void;
    icon?: IconType;
    pendoId?: string;
}

function TableViewSelectorDropdownItem({
    item,
    getItemProps,
    selectedItem,
    highlightedIndex,
    dropdownItems,
    'data-pendoid': dataPendoid,
}: {
    item: ViewSelectorDropdownItem;
    dropdownItems: ViewSelectorDropdownItem[];
    getItemProps: (options: UseSelectGetItemPropsOptions<ViewSelectorDropdownItem>) => any;
    selectedItem: ViewSelectorDropdownItem | null;
    highlightedIndex: number;
    'data-pendoid'?: string;
}): React.ReactElement {
    const index = dropdownItems.findIndex(i => i._id === item._id);
    const classNames = ['e-table-view-selector-item'];

    if (item._id === selectedItem?._id) {
        classNames.push('e-table-view-selector-item-selected');
    }

    if (highlightedIndex === index) {
        classNames.push('e-table-view-selector-item-highlighted');
    }

    return (
        <li
            key={item._id}
            data-pendoid={dataPendoid}
            data-testid={item.isAction ? item._id : `e-table-view-selector-item-${kebabCase(item.title)}`}
            className={classNames.join(' ')}
            {...getItemProps({ item, index })}
        >
            <span className="e-table-view-selector-item-content">
                {item.icon && <Icon type={item.icon} color={tokens.colorsActionMajor500} marginRight="8px" />}
                {item.title}
            </span>
        </li>
    );
}

export function TableViewSelector({ screenId, elementId }: TableViewSelectorProps): React.ReactElement | null {
    const defaultViewItem = React.useMemo(
        () => ({
            title: localize('@sage/xtrem-ui/table-views-default', 'Default view'),
            _id: DEFAULT_VIEW_ID,
            content: {},
        }),
        [],
    );
    const defaultViewLabel = React.useMemo(() => localize('@sage/xtrem-ui/table-views-default', 'Default view'), []);
    const saveButtonLabel = React.useMemo(() => localize('@sage/xtrem-ui/table-views-save', 'Save view'), []);
    const saveAsButtonLabel = React.useMemo(() => localize('@sage/xtrem-ui/table-views-save-as', 'Save view as'), []);
    const manageViewsButtonLabel = React.useMemo(
        () => localize('@sage/xtrem-ui/table-views-manage', 'Manage views'),
        [],
    );

    const dispatch = useDispatch<xtremRedux.AppThunkDispatch>();
    const selectedView = useDeepEqualSelector<xtremRedux.XtremAppState, UiComponentUserSettings>(
        state => state.screenDefinitions[screenId]?.userSettings[navigationPanelId]?.$current,
    );

    const onTelemetryEvent: OnTelemetryEventFunction | undefined = useDeepEqualSelector<
        xtremRedux.XtremAppState,
        OnTelemetryEventFunction | undefined
    >(state => state.applicationContext?.onTelemetryEvent);

    const [value, setValue] = React.useState(selectedView || defaultViewItem);
    const [availableViews, setAvailableViews] = React.useState<Array<UiComponentUserSettings>>([]);
    const [dropdownItems, setDropdownItems] = React.useState<Array<ViewSelectorDropdownItem>>([defaultViewItem]);
    const [isFetchingItems, setFetchingItems] = React.useState<boolean>(false);

    const fetchUserClientSettings = React.useCallback(
        async (force = false): Promise<UiComponentUserSettings[]> => {
            if (isFetchingItems || (!force && availableViews.length > 0)) {
                return availableViews || [];
            }

            setFetchingItems(true);
            const i = await fetchUserClientSettingsForElement(screenId, navigationPanelId);
            setAvailableViews(i);
            setFetchingItems(false);
            return i;
        },
        [isFetchingItems, availableViews, screenId],
    );

    const onSaveView = React.useCallback(async () => {
        try {
            if (selectedView) {
                setAvailableViews(
                    currentItems => currentItems?.map(i => (i._id === selectedView._id ? selectedView : i)) ?? null,
                );
                await dispatch(storeUserClientSettings(screenId, elementId));
                showToast(localize('@sage/xtrem-ui/table-views-saved', 'View saved.'), { type: 'success' });
                onTelemetryEvent?.('customTableView-saveView', { screenId, elementId });
            }
        } catch (e) {
            xtremConsole.error(e);
            showToast(localize('@sage/xtrem-ui/table-views-save-failed', 'Failed to save view.'), { type: 'error' });
        }
    }, [selectedView, dispatch, screenId, elementId, onTelemetryEvent]);

    const onSaveViewAs = React.useCallback(async () => {
        try {
            const newViewId = await dispatch(xtremRedux.actions.openCreateNewViewDialog(screenId, elementId));
            if (!newViewId) {
                return;
            }
            const updatedItems = await fetchUserClientSettings(true);
            const newView = updatedItems.find(({ _id }) => _id === newViewId);
            showToast(localize('@sage/xtrem-ui/table-views-saved', 'View saved.'), { type: 'success' });
            onTelemetryEvent?.('customTableView-saveViewAs', { screenId, elementId });

            if (!newView) {
                return;
            }
            setValue(newView);
        } catch (e) {
            xtremConsole.error(e);
            showToast(localize('@sage/xtrem-ui/table-views-save-failed', 'Failed to save view.'), { type: 'error' });
        }
    }, [dispatch, screenId, elementId, fetchUserClientSettings, onTelemetryEvent]);

    const onManageViewClick = React.useCallback(async () => {
        onTelemetryEvent?.('customTableView-openManageViewDialog', { screenId, elementId });
        const result = await dispatch(xtremRedux.actions.openManageViewsDialog(screenId, elementId));
        const resultIds = result.map(r => r._id);
        setAvailableViews(currentAvailableViews => currentAvailableViews.filter(r => resultIds.includes(r._id)));
        if (result.length === 0 || !result.find(r => r._id === value._id)) {
            setValue(defaultViewItem);
            await dispatch(unselectMainListView(screenId));
        } else {
            setFetchingItems(true);
            const i = await fetchUserClientSettingsForElement(screenId, navigationPanelId);
            setAvailableViews(i);
            setValue(result.find(r => r._id === value._id) || defaultViewItem);
            setFetchingItems(false);
        }
    }, [onTelemetryEvent, screenId, elementId, dispatch, value._id, defaultViewItem]);

    const { getItemProps, getMenuProps, getToggleButtonProps, highlightedIndex, isOpen, selectedItem } =
        useSelect<ViewSelectorDropdownItem>({
            items: dropdownItems,
            onSelectedItemChange: ({ selectedItem: newSelectedItem }) => {
                if (newSelectedItem?.isAction && newSelectedItem.onClick) {
                    newSelectedItem.onClick();
                } else if (newSelectedItem?._id === DEFAULT_VIEW_ID) {
                    onTelemetryEvent?.('customTableView-select-default-view', { screenId, elementId });
                    setValue(defaultViewItem);
                    dispatch(unselectMainListView(screenId));
                } else if (newSelectedItem?._id) {
                    onTelemetryEvent?.('customTableView-select-user-defined-view', { screenId, elementId });
                    setValue(newSelectedItem);
                    dispatch(selectMainListView(screenId, newSelectedItem));
                }
            },
            onIsOpenChange: isDropdownOpen => {
                if (isDropdownOpen) {
                    fetchUserClientSettings();
                }
            },
            itemToString: item => item?.title || '',
            selectedItem: value,
        });

    React.useEffect(() => {
        const actions: ViewSelectorDropdownItem[] = [
            {
                _id: 'e-table-view-save',
                content: {},
                icon: 'save',
                isAction: true,
                onClick: value._id === DEFAULT_VIEW_ID ? onSaveViewAs : onSaveView,
                pendoId: `${elementId}-view-save`,
                title: saveButtonLabel,
            },
        ];

        if (value._id !== DEFAULT_VIEW_ID) {
            actions.push({
                _id: 'e-table-view-save-as',
                content: {},
                icon: 'save',
                isAction: true,
                onClick: onSaveViewAs,
                pendoId: `${elementId}-view-save`,
                title: saveAsButtonLabel,
            });
        }

        actions.push({
            _id: 'e-table-view-manage',
            content: {},
            icon: 'settings',
            isAction: true,
            onClick: onManageViewClick,
            pendoId: `${elementId}-view-manage`,
            title: manageViewsButtonLabel,
        });

        setDropdownItems([defaultViewItem, ...availableViews, ...actions]);
    }, [
        availableViews,
        value._id,
        onSaveViewAs,
        onSaveView,
        saveButtonLabel,
        saveAsButtonLabel,
        defaultViewItem,
        manageViewsButtonLabel,
        onManageViewClick,
        elementId,
    ]);

    const toggleButtonProps = React.useMemo(getToggleButtonProps, [getToggleButtonProps]);

    const { buttonProps, toggleProps } = React.useMemo(
        () =>
            Object.keys(toggleButtonProps).reduce(
                (acc, key) => {
                    if (key.startsWith('aria-')) {
                        acc.buttonProps[key] = toggleButtonProps[key];
                    } else {
                        acc.toggleProps[key] = toggleButtonProps[key];
                    }
                    return acc;
                },
                { buttonProps: {} as Dict<any>, toggleProps: {} as Dict<any> },
            ),
        [toggleButtonProps],
    );

    return (
        <div className="e-table-view-selector">
            <div {...toggleProps}>
                <input
                    data-testid="e-table-view-selector"
                    className="e-table-view-selector-input"
                    aria-label={localize('@sage/xtrem-ui/table-views-select', 'Select view')}
                    readOnly={true}
                    value={selectedItem?.title || defaultViewLabel}
                    role="combobox"
                    aria-controls="e-table-view-selector-dropdown"
                    aria-expanded={isOpen}
                />
                <button
                    type="button"
                    className="e-table-view-selector-button"
                    aria-label={
                        isOpen
                            ? localize('@sage/xtrem-ui/table-views-close-button', 'Close view selector dropdown')
                            : localize('@sage/xtrem-ui/table-views-open-button', 'Open view selector dropdown')
                    }
                    {...buttonProps}
                >
                    <Icon type="caret_down" />
                </button>
            </div>
            <div
                {...getMenuProps()}
                className={`e-table-view-selector-dropdown ${!isOpen ? 'e-table-view-selector-dropdown-hidden' : ''}`}
            >
                <div className="e-table-view-selector-item-group">
                    {dropdownItems
                        .filter(i => i._id === DEFAULT_VIEW_ID)
                        .map(item => (
                            <TableViewSelectorDropdownItem
                                data-pendoid={`${elementId}-view-selector-default`}
                                dropdownItems={dropdownItems}
                                getItemProps={getItemProps}
                                highlightedIndex={highlightedIndex}
                                item={item}
                                key={item._id}
                                selectedItem={selectedItem}
                            />
                        ))}
                </div>
                <div className="e-table-view-selector-item-group e-table-view-selector-item-group-views">
                    {dropdownItems
                        .filter(i => i._id !== DEFAULT_VIEW_ID && !i.isAction)
                        .map(item => (
                            <TableViewSelectorDropdownItem
                                data-pendoid={`${elementId}-view-selector-user-defined`}
                                dropdownItems={dropdownItems}
                                getItemProps={getItemProps}
                                highlightedIndex={highlightedIndex}
                                item={item}
                                key={item._id}
                                selectedItem={selectedItem}
                            />
                        ))}
                </div>
                <div className="e-table-view-selector-item-group e-table-view-selector-item-group-actions">
                    {dropdownItems
                        .filter(i => i.isAction)
                        .map(item => (
                            <TableViewSelectorDropdownItem
                                data-pendoid={item.pendoId}
                                dropdownItems={dropdownItems}
                                getItemProps={getItemProps}
                                highlightedIndex={highlightedIndex}
                                item={item}
                                key={item._id}
                                selectedItem={selectedItem}
                            />
                        ))}
                </div>
            </div>
        </div>
    );
}
