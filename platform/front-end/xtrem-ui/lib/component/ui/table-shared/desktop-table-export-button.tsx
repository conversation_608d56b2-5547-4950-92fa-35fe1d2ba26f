import Button from 'carbon-react/esm/components/button';
import MultiActionButton from 'carbon-react/esm/components/multi-action-button';
import * as React from 'react';
import { localize } from '../../../service/i18n-service';

export interface DesktopTableExportButtonProps {
    onExport: (format: 'excel' | 'csv') => void;
    isDisabled: boolean;
    hasData: boolean;
    elementId: string;
    screenId: string;
}

export function DesktopTableExportButton({
    onExport,
    isDisabled,
    hasData,
    elementId,
    screenId,
}: DesktopTableExportButtonProps): React.ReactElement {
    const isButtonDisabled = isDisabled || !hasData;
    return (
        <MultiActionButton
            text={localize('@sage/xtrem-ui/table-export', 'Export')}
            mr="16px"
            data-testid="e-table-export"
            disabled={isDisabled}
        >
            <Button
                key="excel"
                disabled={isButtonDisabled}
                onClick={(): void => onExport('excel')}
                data-testid="e-table-export-excel"
                data-pendoid={`exportButton-excel-${screenId}-${elementId}`}
            >
                {localize('@sage/xtrem-ui/export-format-excel', 'Excel')}
            </Button>
            <Button
                key="csv"
                disabled={isButtonDisabled}
                data-testid="e-table-export-csv"
                onClick={(): void => onExport('csv')}
                data-pendoid={`exportButton-csv-${screenId}-${elementId}`}
            >
                {localize('@sage/xtrem-ui/export-format-csv', 'CSV')}
            </Button>
        </MultiActionButton>
    );
}
