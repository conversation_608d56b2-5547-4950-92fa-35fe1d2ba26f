import { objectKeys, type Dict } from '@sage/xtrem-shared';
import * as React from 'react';
import { localize } from '../../../../service/i18n-service';
import type { ValidationResult } from '../../../../service/screen-base-definition';
import { getNestedFieldElementId } from '../../../../utils/abstract-fields-utils';
import { withoutNestedTechnical } from '../../../nested-fields';
import { TableTooltip } from '../../tooltip/tooltip';
import type { CellParams } from '../../../../utils/ag-grid/ag-grid-column-config';
import type { AgGridColumnConfigWithAllColumns } from '../../../../utils/ag-grid/ag-grid-utility-types';

export function ValidationCellRenderer(props: CellParams<any>): React.ReactElement | null {
    const validationState: Dict<ValidationResult> = props.data?.__validationState || {};
    const numberOfErrors = objectKeys(validationState).length;

    const getTooltipContent = (): React.ReactNode => {
        const message = localize('@sage/xtrem-ui/validation-errors-number', 'Number of errors found in this line');
        return (
            <>
                <h6 style={{ margin: 0, padding: 0 }}>
                    {message}
                    :&nbsp;
                    <strong>{numberOfErrors}</strong>
                </h6>
                <ul className="error-list error-list--inside-tooltip" data-testid="error-list">
                    {Object.entries(validationState).map(([key, value]) => {
                        let columnName = props.api?.getColumn(key)?.getDefinition()?.headerName;
                        const validationColDef = props.colDef as AgGridColumnConfigWithAllColumns;
                        if (!columnName && validationColDef.context.getColumns) {
                            const columns = validationColDef.context.getColumns();
                            const columnDefinition = withoutNestedTechnical(columns).find(
                                c => getNestedFieldElementId(c) === key,
                            );
                            if (columnDefinition) {
                                columnName = columnDefinition.properties.title as string;
                            }
                        }
                        const idOrKey = key.includes('__rowError') ? value.elementId : key;
                        return (
                            <li key={key}>
                                <strong>{columnName || idOrKey}</strong>
                                :&nbsp;
                                {value.message}
                            </li>
                        );
                    })}
                </ul>
            </>
        );
    };

    if (numberOfErrors === 0) {
        return null;
    }

    return <TableTooltip content={getTooltipContent()} />;
}
