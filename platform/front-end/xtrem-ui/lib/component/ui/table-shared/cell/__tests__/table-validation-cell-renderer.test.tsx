import * as React from 'react';
import type { CellParams } from '../../../../../utils/ag-grid/ag-grid-column-config';
import { CellWrapper } from '../cell-wrapper';
import { screen, render } from '@testing-library/react';
import { ValidationCellRenderer } from '../table-validation-cell-renderer';
import { TooltipProvider } from 'carbon-react/esm/__internal__/tooltip-provider';
import type { AgGridColumnConfigWithAllColumns } from '../../../../../utils/ag-grid/ag-grid-utility-types';
import { CollectionValue } from '../../../../../service/collection-data-service';
import { CollectionFieldTypes } from '../../../../../service/collection-data-types';
import * as nestedFields from '../../../../nested-fields';

describe('select cell renderer', () => {
    let props: React.PropsWithChildren<CellParams<any, string>>;

    beforeEach(() => {
        props = {
            locale: 'en-US',
            data: {
                __validationState: {
                    'test-column-id': {
                        elementId: 'test-column-id',
                    },
                },
            },
            collectionValue: () =>
                new CollectionValue({
                    screenId: 'test-screen',
                    elementId: 'testField',
                    isTransient: false,
                    hasNextPage: false,
                    orderBy: [{}],
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                        ],
                    ],
                    nodeTypes: {},
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                }),
            initialValue: '',
            value: '',
            onValueChange: jest.fn(),
            setTooltip: jest.fn(),
            valueFormatted: null,
            isParentFieldDisabled: false,
            column: {
                colDef: {
                    field: 'test-column-id',
                },
                columnApi: {
                    getAllColumns: jest.fn(() => [
                        {
                            field: 'test-column-id',
                        },
                    ]),
                } as any,
            } as any,
            screenId: 'test-screen',
            elementId: 'test-table',
            tableElementId: 'test-table',
            columnId: 'test-column-id',
            fieldProperties: {
                bind: 'test-column-id',
                wrapper: CellWrapper,
            },
            isTree: false,
            colDef: {
                context: {
                    screenId: 'test-screen',
                    columnId: 'test-column-id',
                    isEditable: () => false,
                },
                field: 'test-column-id',
                cellRendererParams: {
                    locale: 'en-US',
                    screenId: 'test-screen',
                    tableElementId: 'test-table',
                    columnId: 'test-column-id',
                    elementId: 'elementId',
                    isParentFieldDisabled: false,
                    fieldProperties: {
                        bind: 'test-column-id',
                        wrapper: CellWrapper,
                    },
                    isTree: false,
                    collectionValue: () =>
                        new CollectionValue({
                            screenId: 'test-screen',
                            elementId: 'testField',
                            isTransient: false,
                            hasNextPage: false,
                            orderBy: [{}],
                            columnDefinitions: [
                                [
                                    nestedFields.text<any, any>({ bind: '_id' }),
                                    nestedFields.text<any, any>({ bind: 'anyField' }),
                                ],
                            ],
                            nodeTypes: {},
                            nodes: ['@sage/xtrem-test/AnyNode'],
                            filter: [undefined],
                            initialValues: [],
                            fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                        }),
                },
            },
            eventKey: null,
            stopEditing: jest.fn(),
            getValue: jest.fn(),
            setValue: jest.fn(),
            formatValue: jest.fn(),
            refreshCell: jest.fn(),
            api: {
                getColumns: jest.fn(() => [
                    {
                        field: 'test-column-id',
                    },
                ]),
                getColumn: jest.fn(),
            } as any,
            node: {
                rowIndex: 3,
            } as any,
            context: {} as any,
            eGridCell: {} as any,
            eParentOfValue: {} as any,
            registerRowDragger: () => {},
        };
    });

    it('should find the column header name among table column api', async () => {
        (props.api as any).getColumn = jest.fn(() => {
            return {
                getDefinition: jest.fn(() => {
                    return {
                        headerName: 'test-header-name',
                    };
                }),
            };
        });
        render(
            <TooltipProvider tooltipVisible={true}>
                <ValidationCellRenderer {...props} />
            </TooltipProvider>,
        );
        const errorTooltip = screen.getByTestId('error-list');
        expect(errorTooltip).toHaveTextContent('test-header-name');
    });

    it('should find the column header name among colDefs', async () => {
        (props.colDef as AgGridColumnConfigWithAllColumns) = {
            context: {
                screenId: '',
                columnId: 'test-column-id',
                isEditable: () => false,
                getColumns: jest.fn().mockReturnValue([
                    {
                        properties: {
                            bind: 'test-column-id',
                            title: 'test-header-name',
                        },
                    },
                ]),
            },
        };
        render(
            <TooltipProvider tooltipVisible={true}>
                <ValidationCellRenderer {...props} />
            </TooltipProvider>,
        );
        const errorTooltip = screen.getByTestId('error-list');
        expect(errorTooltip).toHaveTextContent('test-header-name');
    });
});
