import { isNil, noop } from 'lodash';
import * as React from 'react';
import type { CollectionValue } from '../../../../service/collection-data-service';
import { RecordActionType } from '../../../../service/collection-data-types';
import { type CellParams } from '../../../../utils/ag-grid/ag-grid-column-config';
import type { DropdownActionItem, DropdownActionItemOrMenuSeparator, Nested } from '../../../field/traits';
import type {
    NestedCollectionItemAction,
    NestedCollectionItemActionGroup,
    NestedCollectionItemActionOrMenuSeparator,
} from '../table-dropdown-actions/table-dropdown-action-types';
import ConnectedTableDropdownActions from '../table-dropdown-actions/table-dropdown-actions';
import ConnectedTableInlineActions from '../table-inline-actions';

type TableDropdownActionCellPropsData = { _id: string };
type NestedTableDropdownActionCellPropsData = TableDropdownActionCellPropsData & {
    __level: any;
    __phantom: boolean;
    __dirty: boolean;
};
type ActionItemType = DropdownActionItem<any> | NestedCollectionItemAction<any> | NestedCollectionItemActionGroup<any>;
type ActionItemOrMenuSeparatorType =
    | DropdownActionItemOrMenuSeparator<any>
    | NestedCollectionItemActionOrMenuSeparator<any>
    | NestedCollectionItemActionGroup<any>;

export interface TableDropdownActionsCellProps extends Omit<CellParams<Nested, any>, 'colDef'> {
    colDef: {
        cellRendererParams: {
            screenId: string;
            elementId: string;
            level: number;
            isParentFieldDisabled: boolean;
            isReadOnly: boolean;
            inlineActions: Array<ActionItemType>;
            dropdownActions: Array<ActionItemOrMenuSeparatorType>;
            collectionValue: () => CollectionValue | undefined;
        };
    };
    isEditing: boolean;
}

export function TableDropdownActionsCell(props: TableDropdownActionsCellProps): React.ReactElement | null {
    const containerRef = React.useRef<HTMLDivElement>(null);
    const [data, setData] = React.useState(props.data);

    const isPhantomRow = React.useMemo(
        () => (data as NestedTableDropdownActionCellPropsData)?.__phantom !== undefined,
        [data],
    );

    const onPhantomCellFocus = React.useCallback((e: FocusEvent) => {
        (
            (e.target as Element | null)?.querySelector?.('button[aria-label="Cancel"]') as HTMLButtonElement | null
        )?.focus?.();
    }, []);

    React.useEffect(() => {
        return props.colDef.cellRendererParams.collectionValue()?.subscribeForValueChanges((type, rowValue) => {
            if (type !== RecordActionType.MODIFIED || rowValue._id !== props.data._id) {
                return;
            }
            setData(rowValue);
        });
    }, [props.colDef.cellRendererParams, props.data._id]);

    // Focus on editing cell when in edit mode
    React.useEffect(() => {
        if (props.isEditing) {
            containerRef.current?.focus();
        }
    }, [props.isEditing]);

    // Focus phantom row's clear button in non edit mode
    React.useEffect(() => {
        if (!isPhantomRow || props.isEditing) {
            return noop;
        }
        props.eGridCell.addEventListener('focus', onPhantomCellFocus);
        return (): void => {
            props.eGridCell.removeEventListener('focus', onPhantomCellFocus);
        };
    }, [props.eGridCell, isPhantomRow, onPhantomCellFocus, props.isEditing]);

    if (data._id?.startsWith('__group-') || isNil(data?._id)) {
        return null;
    }

    const hasInlineActions = props.colDef.cellRendererParams.inlineActions.length > 0 && !isPhantomRow;
    const hasDropdownActions = props.colDef.cellRendererParams.dropdownActions.length > 0 || isPhantomRow;

    return (
        <div
            className="e-table-field-actions-container ag-cell-edit-wrapper ag-custom-component-popup"
            tabIndex={props.isEditing ? 0 : undefined}
            ref={containerRef}
            style={
                props.isEditing
                    ? {
                          width: props.eGridCell.clientWidth,
                          boxSizing: 'border-box',
                          height: '100%',
                      }
                    : undefined
            }
        >
            {hasInlineActions && (
                <ConnectedTableInlineActions
                    actions={props.colDef.cellRendererParams.inlineActions}
                    api={props.api}
                    column={props.column}
                    fieldId={props.colDef.cellRendererParams.elementId}
                    hasDropdownActions={hasDropdownActions}
                    hasInlineActions={hasInlineActions}
                    isDisabled={props.colDef.cellRendererParams.isParentFieldDisabled}
                    isPhantomRow={(data as NestedTableDropdownActionCellPropsData).__phantom !== undefined}
                    level={props.colDef.cellRendererParams.level}
                    recordId={data._id}
                    rowIndex={props.node.rowIndex}
                    rowValue={data}
                    screenId={props.colDef.cellRendererParams.screenId}
                />
            )}
            {hasDropdownActions && (
                <ConnectedTableDropdownActions
                    actions={props.colDef.cellRendererParams.dropdownActions}
                    api={props.api}
                    column={props.column}
                    fieldId={props.colDef.cellRendererParams.elementId}
                    hasDropdownActions={hasDropdownActions}
                    hasInlineActions={hasInlineActions}
                    isDisabled={props.colDef.cellRendererParams.isParentFieldDisabled}
                    isPhantomRow={(data as NestedTableDropdownActionCellPropsData).__phantom !== undefined}
                    level={props.colDef.cellRendererParams.level}
                    recordId={data._id}
                    rowIndex={props.node.rowIndex}
                    rowValue={data}
                    screenId={props.colDef.cellRendererParams.screenId}
                />
            )}
        </div>
    );
}
