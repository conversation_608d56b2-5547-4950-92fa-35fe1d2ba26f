import React from 'react';
import type { CellParams } from '../../../../utils/ag-grid/ag-grid-column-config';

export function CheckboxCell({ node, data }: CellParams): React.ReactNode {
    const inputRef = React.useRef<HTMLInputElement | null>(null);
    const [checked, setChecked] = React.useState<boolean>(<PERSON><PERSON><PERSON>(node.isSelected()));

    React.useEffect(() => {
        inputRef.current?.focus();
    }, []);

    const onChange = React.useCallback<NonNullable<React.ComponentProps<'input'>['onChange']>>(() => {
        const newValue = !node.isSelected();
        setChecked(newValue);
        node.setSelected(newValue);
    }, [node]);

    if (data?.__phantom) {
        return null;
    }

    return (
        <div className="ag-cell-wrapper" role="presentation">
            <div
                className="ag-selection-checkbox"
                role="presentation"
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '100%' }}
            >
                <div role="presentation" className="ag-labeled ag-label-align-right ag-checkbox ag-input-field">
                    <div
                        className={`ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper ${checked ? 'ag-checked' : ''}`}
                        role="presentation"
                    >
                        <input
                            ref={inputRef}
                            checked={checked}
                            onChange={onChange}
                            className="ag-input-field-input ag-checkbox-input"
                            type="checkbox"
                            tabIndex={-1}
                            aria-live="polite"
                            aria-relevant="text"
                            aria-label={`Press Space to toggle row selection (${checked ? 'checked' : 'unchecked'})`}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
}
