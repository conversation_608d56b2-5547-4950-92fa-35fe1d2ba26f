import React from 'react';
import Loader from 'carbon-react/esm/components/loader';

type LoadingCellRendererProps = {
    elementId: string;
};

export function TableLoadingCellRenderer({ elementId }: LoadingCellRendererProps): React.ReactNode {
    return (
        <div className="e-table-field-loading-row" data-testid={`e-table-field-loading-${elementId}`}>
            <Loader size="small" />
        </div>
    );
}
