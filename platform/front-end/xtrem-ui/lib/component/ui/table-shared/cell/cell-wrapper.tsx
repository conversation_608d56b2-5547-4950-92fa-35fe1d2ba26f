import React from 'react';
import type { ScreenBase } from '../../../../service/screen-base';
import type { NestedCheckboxProperties } from '../../../nested-fields-properties';
import { TableTooltip } from '../../tooltip/tooltip';
import type { CellParams } from '../../../../utils/ag-grid/ag-grid-column-config';
import {
    getCellErrorMessage,
    getCellInfoMessage,
    getCellWarningMessage,
} from '../../../../utils/ag-grid/ag-grid-column-config';
import { AUTO_COLUMN_ID } from '../../../../utils/table-component-utils';
import type { NestedGroupAggregations } from '../../../field/traits';
import { localize } from '../../../../service/i18n-service';
import { isNil } from 'lodash';
import * as tokens from '@sage/design-tokens/js/base/common';
import type { IconType } from '@sage/xtrem-shared';

export interface CellWrapperProps
    extends React.PropsWithChildren<CellParams<NestedCheckboxProperties<ScreenBase, any>, any>> {
    textAlign?: 'right' | 'left' | 'center';
}

export function CellWrapper({
    children,
    textAlign = 'left',
    ...props
}: React.PropsWithChildren<CellWrapperProps>): React.ReactElement | null {
    const errorMessage = React.useMemo(() => getCellErrorMessage(props), [props]);
    const warningMessage = React.useMemo(() => getCellWarningMessage(props), [props]);
    const infoMessage = React.useMemo(() => getCellInfoMessage(props), [props]);

    const messageColor = React.useMemo((): string | undefined => {
        if (errorMessage) {
            return tokens.colorsSemanticNegative500;
        }
        if (warningMessage) {
            return tokens.colorsSemanticCaution500;
        }
        if (infoMessage) {
            return tokens.colorsSemanticInfo500;
        }
        return undefined;
    }, [errorMessage, warningMessage, infoMessage]);

    const messageType = React.useMemo((): IconType | undefined => {
        if (errorMessage) {
            return 'error';
        }
        if (warningMessage) {
            return 'warning';
        }
        if (infoMessage) {
            return 'info';
        }
        return undefined;
    }, [errorMessage, warningMessage, infoMessage]);

    const messageToDisplay = errorMessage || warningMessage || infoMessage;

    if (
        !props.node?.footer &&
        !props.isTree &&
        props.data &&
        props.data.__isGroup &&
        props.colDef.colId !== AUTO_COLUMN_ID
    ) {
        // group row, but not group column => value should be empty
        return null;
    }

    if (
        !props.node?.footer &&
        !props.isTree &&
        props.data &&
        !props.data.__isGroup &&
        props.colDef.colId === AUTO_COLUMN_ID &&
        (!props.data.__aggFunc || props.data.__aggFunc === 'day')
    ) {
        // child rows should have an empty group column
        return null;
    }

    if (props.node?.footer && props.colDef.colId === AUTO_COLUMN_ID) {
        return (
            <div className="e-table-group-total-row-cell">
                {localize('@sage/xtrem-ui/table-group-total', 'Group total')}
            </div>
        );
    }

    if (props.node?.footer && !(props.fieldProperties as NestedGroupAggregations)?.groupAggregationMethod) {
        return null;
    }

    const hasTooltip = !isNil(messageToDisplay);

    return (
        <div
            className="e-grid-error-indicator"
            style={{
                ...(hasTooltip && {
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    border: `1px solid ${messageColor}`,
                }),
                padding: textAlign === 'left' ? '0 0 0 11px' : textAlign === 'right' ? '0 11px 0 0' : '0 11px',
                textAlign,
            }}
        >
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    height: '100%',
                    ...(textAlign === 'right' && { order: 1 }),
                }}
            >
                <div
                    style={{
                        minWidth: 0,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                    }}
                >
                    {children}
                </div>
            </div>
            {hasTooltip && <TableTooltip content={messageToDisplay} type={messageType} color={messageColor} />}
        </div>
    );
}
