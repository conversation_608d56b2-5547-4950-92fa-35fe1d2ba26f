import React from 'react';
import {
    defaultCellEditorStyles,
    getInitialCellEditorState,
} from '../../../../utils/ag-grid/ag-grid-cell-editor-utils';
import type { CellParams } from '../../../../utils/ag-grid/ag-grid-column-config';

interface DefaultCellEditorState {
    highlightOnFocus: boolean;
    value: string;
}

type DefaultCellEditorProps = CellParams<any>;
export class DefaultCellEditor extends React.Component<DefaultCellEditorProps, DefaultCellEditorState> {
    private inputRef: React.RefObject<HTMLInputElement>;

    constructor(props: DefaultCellEditorProps) {
        super(props);
        this.inputRef = React.createRef();

        this.state = getInitialCellEditorState({ eventKey: props.eventKey, initialValue: props.initialValue });
    }

    componentDidMount(): void {
        const input = this.inputRef.current;
        if (!input) {
            return;
        }
        input.focus();
        if (this.state.highlightOnFocus) {
            input.select();
            this.setState({
                highlightOnFocus: false,
            });
        } else {
            // when we started editing, we want the caret at the end, not the start.
            // this comes into play in two scenarios: a) when user hits F2 and b)
            // when user hits a printable character, then on IE (and only IE) the caret
            // was placed after the first character, thus 'apply' would end up as 'pplea'
            const length = input.value ? input.value.length : 0;
            if (length > 0) {
                input.setSelectionRange(length, length);
            }
        }
    }

    handleChange(event: React.ChangeEvent<HTMLInputElement>): void {
        this.setState({ value: event.target.value });
    }

    render(): React.ReactNode {
        return (
            <div className="ag-cell-edit-wrapper">
                <input
                    ref={this.inputRef}
                    type="text"
                    value={this.state.value}
                    onChange={this.handleChange}
                    style={defaultCellEditorStyles}
                />
            </div>
        );
    }
}
