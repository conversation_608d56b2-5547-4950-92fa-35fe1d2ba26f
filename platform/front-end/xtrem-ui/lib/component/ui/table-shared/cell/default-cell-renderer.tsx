import React from 'react';
import type { CellParams } from '../../../../utils/ag-grid/ag-grid-column-config';
import { getTextAlignment } from '../../../../utils/ag-grid/ag-grid-column-config';
import { AUTO_COLUMN_ID } from '../../../../utils/table-component-utils';
import { FieldKey } from '../../../types';
import { get } from 'lodash';
import { formatDateToCurrentLocale, localize } from '../../../../service/i18n-service';
import { date, isValidDatePropertyValue } from '@sage/xtrem-date-time';

export const DefaultCellRenderer: React.FC<CellParams> = React.memo(props => {
    const { fieldProperties } = props;
    let value = props.valueFormatted ?? props.value;
    if (
        props.colDef.colId === AUTO_COLUMN_ID &&
        [FieldKey.Date, FieldKey.RelativeDate].includes(props.colDef.type as FieldKey) &&
        props.data.__aggFunc !== undefined
    ) {
        if (value === '' && props.data.__isGroup) {
            value = `${localize('@sage/xtrem-ui/no-value', 'No value')} (${props.data.__groupCount})`;
        } else if (isValidDatePropertyValue(get(props.data, props.data.__groupKey))) {
            if (props.data.__aggFunc === 'year') {
                value = `${String(date.parse(get(props.data, props.data.__groupKey)).year)} (${
                    props.data.__groupCount
                })`;
            } else {
                value = `${formatDateToCurrentLocale(
                    get(props.data, props.data.__groupKey),
                    props.data.__aggFunc === 'day' ? 'FullDate' : 'LongMonthYear',
                )} (${props.data.__groupCount})`;
            }
        }
    }

    return (
        <fieldProperties.wrapper {...props} textAlign={getTextAlignment(props)}>
            {props.node?.footer ? <div className="e-table-group-total-row-cell">{value}</div> : value}
        </fieldProperties.wrapper>
    );
});

DefaultCellRenderer.displayName = 'DefaultCellRenderer';
