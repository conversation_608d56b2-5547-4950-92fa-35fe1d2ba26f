.e-table-empty {
    pointer-events: all;
    font-family: var(--fontFamiliesDefault);
    font-weight: var(--fontWeights700);
    font-size: 16px;
    justify-content: center;
    text-align: center;
    display: flex;
    flex-direction: column;

    &.e-table-empty-card {

        .e-table-empty-rows-image,
        .e-table-no-results-found-image {
            padding-top: 50px;
        }

        .e-table-empty-content-block {
            padding-left: 16px;
            padding-right: 16px;
        }
    }

    &.e-table-empty-filtering {
        flex-direction: row;

        &.e-table-empty-card {
            flex-direction: column;

            .e-table-empty-text {
                text-align: center;
            }

            .e-table-empty-content-block {
                margin-left: 0;
                text-align: center;
            }
        }

        .e-table-empty-text {
            text-align: left;
        }

        .e-table-empty-content-block {
            margin-left: 100px;
            text-align: left;
        }
    }

    .e-table-empty-rows-image {
        display: block;
        margin: 0 auto;
    }

    .e-no-rows-found-component-text {
        font-weight: var(--fontWeights400);
        font-size: 16px;
        margin-bottom: 24px;
    }

    & .e-table-empty-content-block {
        margin-top: 50px;
    }

    & .e-table-empty-text {
        text-align: center;
        font-family: var(--fontFamiliesDefault);
        font-style: normal;
        font-weight: var(--fontWeights700);
        font-size: 24px;
        line-height: 125%;
        margin-bottom: 24px;
    }

    p {
        margin: 8px 0;
    }

    h3 {
        margin: 0;
    }
}