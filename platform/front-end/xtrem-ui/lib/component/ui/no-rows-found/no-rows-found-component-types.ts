import type { Grid<PERSON><PERSON> } from '@ag-grid-community/core';
import type { NestedGridDecoratorProperties } from '../../field/nested-grid/nested-grid-decorator';
import type { TableDecoratorProperties } from '../../field/table/table-component-types';

export interface NoRowsFoundComponentProps {
    /** Sets a text when no data is available in the table */
    emptyStateText?: string;

    /** Sets a complementary text link when no data is available in the table  */
    emptyStateClickableText?: string;

    /** Function to be executed when the clickable text is clicked */
    onEmptyStateLinkClick?: (parentId?: string, level?: number) => void;

    onRemoveFiltersClick?: () => void;

    screenId: string;

    elementId: string;

    isTree: boolean;

    isFiltering: boolean;

    level?: number;

    parentId?: string;

    type?: 'link' | 'button';

    isCardView?: boolean;

    // If the table has a phantom row, the images are hidden in the overlay component, otherwise the phantom row would be pushed out of the view
    hasPhantomRow?: boolean;
}

export interface TableNoRowsFoundProps {
    api: GridApi | null;
    fieldProperties: TableDecoratorProperties;
    screenId: string;
    elementId: string;
    isEditable: boolean;
}

export interface NestedGridNoRowsFoundProps {
    api: GridApi;
    fieldProperties: NestedGridDecoratorProperties;
    screenId: string;
    elementId: string;
    isFiltering?: boolean;
}
