import * as React from 'react';
import { localize } from '../../../service/i18n-service';
import { getScreenElement } from '../../../service/screen-base-definition';
import type {
    NestedGridNoRowsFoundProps,
    NoRowsFoundComponentProps,
    TableNoRowsFoundProps,
} from './no-rows-found-component-types';
import * as xtremRedux from '../../../redux';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import Button from 'carbon-react/esm/components/button';
import { NoRows } from '../svg/no-rows';
import { NoDataFound } from '../svg/no-data-found';
import type { IconType } from 'carbon-react/esm/components/icon';
import { getNestedGridContext } from '../../../utils/table-component-utils';
import { objectKeys } from '@sage/xtrem-shared';

interface NoRowsProps {
    emptyStateText?: string;
    emptyStateClickableText?: string;
    onEmptyStateLinkClick?: () => void;
    onClick?: () => void;
    buttonIconType?: IconType;
}

function NoRowsWithButton({
    emptyStateText,
    emptyStateClickableText,
    onEmptyStateLinkClick,
    onClick,
    buttonIconType,
}: NoRowsProps): React.ReactElement {
    return (
        <>
            <div className="e-no-rows-found-component-text">
                {emptyStateText ||
                    localize('@sage/xtrem-ui/navigation-panel-no-results', 'There are no data to display')}
            </div>
            {emptyStateClickableText && (onClick || onEmptyStateLinkClick) && (
                <div className="e-no-rows-found-component-button">
                    <Button
                        data-testid="e-no-rows-found-component-button"
                        iconType={buttonIconType}
                        m={0}
                        buttonType="primary"
                        onClick={onClick || onEmptyStateLinkClick}
                        noWrap
                    >
                        {emptyStateClickableText}
                    </Button>
                </div>
            )}
        </>
    );
}

export function EmptyTableComponent({
    emptyStateText,
    emptyStateClickableText,
    onEmptyStateLinkClick,
    onRemoveFiltersClick,
    isTree,
    screenId = '',
    isFiltering,
    level,
    parentId,
    isCardView,
    hasPhantomRow = false,
}: Partial<NoRowsFoundComponentProps>): React.ReactElement | null {
    const onEmptyStateLinkButtonClick = React.useCallback((): void => {
        const state = xtremRedux.getStore().getState();
        const pageDefinition = getPageDefinitionFromState(screenId, state);
        if (onEmptyStateLinkClick) {
            onEmptyStateLinkClick.apply(getScreenElement(pageDefinition), [parentId, level]);
        }
    }, [onEmptyStateLinkClick, level, parentId, screenId]);

    if (isTree) {
        // Empty when group has no children
        return null;
    }

    const className = ['e-table-empty'];

    if (isCardView) {
        className.push('e-table-empty-card');
    }

    if (isFiltering) {
        className.push('e-table-empty-filtering');
        return (
            <div className={className.join(' ')} data-testid="e-no-rows-found-component">
                {!hasPhantomRow && (
                    <div className="e-table-no-results-found-image">
                        <NoDataFound width={isCardView ? 200 : undefined} height={isCardView ? 200 : undefined} />
                    </div>
                )}
                <div className="e-table-empty-content-block">
                    <div className="e-table-empty-text">
                        {localize('@sage/xtrem-ui/empty-state-filter-text-title', 'No results found.')}
                    </div>
                    <NoRowsWithButton
                        onClick={onRemoveFiltersClick}
                        emptyStateText={localize(
                            '@sage/xtrem-ui/empty-state-filter-text-no-results-description',
                            'Try different criteria or create a new record to get started.',
                        )}
                        emptyStateClickableText={localize(
                            '@sage/xtrem-ui/empty-state-filter-text-no-results-button',
                            'Clear filters',
                        )}
                        onEmptyStateLinkClick={onRemoveFiltersClick}
                    />
                </div>
            </div>
        );
    }

    return (
        <div className={className.join(' ')} data-testid="e-no-rows-found-component">
            {!hasPhantomRow && (
                <div className="e-table-empty-rows-image">
                    <NoRows />
                </div>
            )}
            <div className="e-table-empty-content-block">
                <div className="e-table-empty-text">
                    {localize('@sage/xtrem-ui/empty-state-text', 'This list has no items yet.')}
                </div>
                <NoRowsWithButton
                    onClick={onEmptyStateLinkButtonClick}
                    emptyStateText={emptyStateText}
                    emptyStateClickableText={emptyStateClickableText}
                    onEmptyStateLinkClick={onEmptyStateLinkClick}
                    buttonIconType="add"
                />
            </div>
        </div>
    );
}

/** To be used in ag-grid table */
export function DesktopTableEmptyComponent({
    fieldProperties,
    api,
    screenId,
    elementId,
    isEditable,
}: TableNoRowsFoundProps): React.ReactElement {
    const emptyStateText = fieldProperties?.emptyStateText;
    const emptyStateClickableText = fieldProperties?.emptyStateClickableText;
    const onEmptyStateLinkClick = fieldProperties?.onEmptyStateLinkClick;
    const filter = api?.getFilterModel();
    const isFiltering = filter && objectKeys(filter).length > 0;
    const hasPhantomRow = fieldProperties.canAddNewLine && isEditable;

    const onRemoveFiltersClick = React.useCallback((): void => {
        if (api) {
            api.setFilterModel(null);
        }
    }, [api]);

    return (
        <EmptyTableComponent
            elementId={elementId}
            emptyStateClickableText={emptyStateClickableText}
            emptyStateText={emptyStateText}
            isFiltering={isFiltering}
            onEmptyStateLinkClick={onEmptyStateLinkClick}
            onRemoveFiltersClick={onRemoveFiltersClick}
            screenId={screenId}
            hasPhantomRow={hasPhantomRow}
        />
    );
}

/** To be used in ag-grid nested grid */
export function DesktopNestedGridEmptyComponent({
    fieldProperties,
    api,
    screenId,
    elementId,
}: NestedGridNoRowsFoundProps): React.ReactElement {
    const { level = 0, parentId } = getNestedGridContext(api);
    const emptyStateText = fieldProperties.levels[level].emptyStateText;
    const emptyStateClickableText = fieldProperties.levels[level].emptyStateClickableText;
    const onEmptyStateLinkClick = fieldProperties.levels[level].onEmptyStateLinkClick;
    const filter = api?.getFilterModel();
    const isFiltering = filter && objectKeys(filter).length > 0;

    const onRemoveFiltersClick = React.useCallback((): void => {
        if (api) {
            api.setFilterModel(null);
        }
    }, [api]);

    return (
        <EmptyTableComponent
            elementId={elementId}
            emptyStateClickableText={emptyStateClickableText}
            emptyStateText={emptyStateText}
            isFiltering={isFiltering}
            level={level}
            onEmptyStateLinkClick={onEmptyStateLinkClick}
            onRemoveFiltersClick={onRemoveFiltersClick}
            parentId={parentId}
            screenId={screenId}
        />
    );
}
