const XtremActionMock = jest.fn<JSX.Element, any>(() => (
    <div id="mockXtremActionMock" data-testid="mockXtremActionMock" />
));
jest.mock('../../xtrem-action-popover', () => XtremActionMock);

import { render } from '@testing-library/react';
import * as React from 'react';
import { getDataTestIdAttribute } from '../../../../utils/dom';
import type { PodProps } from '../pod-component';
import { Pod } from '../pod-component';
import * as NestedBlock from '../../nested-block';
import * as CarbonUtilityComponents from '../../../field/carbon-utility-components';
import { Provider } from 'react-redux';
import { getMockStore } from '../../../../__tests__/test-helpers';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux';

describe('Pod UI component', () => {
    let props: PodProps;
    let mockStore: MockStoreEnhanced<XtremAppState>;

    beforeEach(() => {
        mockStore = getMockStore();

        props = {
            availableColumns: 12,
            baseAttributesDivWrapper: {
                'data-testid': getDataTestIdAttribute('pod', 'pod-component', 'podElementId'),
            },
            elementId: 'podElementId',
            fieldProperties: { columns: [] },
            isDisabled: false,
            isReadOnly: false,
            onBlockClick: jest.fn(),
            onNewPod: jest.fn(),
            onRemove: jest.fn(),
            screenId: 'screenId',
            value: null,
        };
        jest.clearAllMocks();
    });

    it('Should render a Nested block if the value exists', () => {
        const mockNestedBlock = jest.spyOn(NestedBlock, 'NestedBlock').mockImplementation(() => {
            return (<div id="mockNestedBlock" data-testid="mockNestedBlock" />) as any;
        });

        render(
            <Provider store={mockStore}>
                <Pod {...props} />
            </Provider>,
        );
        expect(mockNestedBlock).not.toHaveBeenCalled();
        props.value = {
            randomValue: 123,
        };
        render(
            <Provider store={mockStore}>
                <Pod {...props} />
            </Provider>,
        );
        expect(mockNestedBlock).toHaveBeenCalled();
    });

    it('Should render a add button if the value dont exists', () => {
        props.value = null;
        const component = render(
            <Provider store={mockStore}>
                <Pod {...props} />
            </Provider>,
        );
        const newButton = component.queryByTestId('e-pod-add-new')!;
        expect(newButton).toBeTruthy();
    });

    it('Should render a add button with custom add text if is provided', () => {
        props.value = null;
        props.fieldProperties.addButtonText = 'Click me!';
        const component = render(
            <Provider store={mockStore}>
                <Pod {...props} />
            </Provider>,
        );
        const newButton = component.queryByTestId('e-pod-add-new')!;
        expect(newButton).toBeTruthy();
        expect(newButton).toHaveTextContent('Click me!');
    });

    it('Should render a header with XtremActionPopover if there are dropdownActions', () => {
        props.fieldProperties.dropdownActions = [{ title: 'action 1', onClick: jest.fn() }];
        const component = render(
            <Provider store={mockStore}>
                <Pod {...props} />
            </Provider>,
        );
        const titleElement = component.queryByTestId('e-pod-title');
        expect(titleElement).toBeTruthy();
        expect(XtremActionMock).toHaveBeenCalledTimes(1);
    });

    it('Should render HelperText if fieldProperties helpertext provides it', () => {
        const helperTextMock = jest.spyOn(CarbonUtilityComponents, 'HelperText').mockImplementation(() => {
            return (<div id="mockHelperText" data-testid="mockHelperText" />) as any;
        });
        props.fieldProperties.helperText = 'test helper text';
        render(
            <Provider store={mockStore}>
                <Pod {...props} />
            </Provider>,
        );
        expect(helperTextMock).toHaveBeenCalled();
    });

    it('should render placeholder in empty state', () => {
        props.value = null;
        const component = render(
            <Provider store={mockStore}>
                <Pod {...props} />
            </Provider>,
        );
        const placeholderText = component.queryByText('No data to display');
        expect(placeholderText).toBeTruthy();
    });

    it('should render custom placeholder text in empty state given placeholder', () => {
        props.value = null;
        props.fieldProperties.placeholder = 'No Patatas to display';
        const component = render(
            <Provider store={mockStore}>
                <Pod {...props} />
            </Provider>,
        );
        const placeholderText = component.queryByText('No Patatas to display');
        expect(placeholderText).toBeTruthy();
    });
});
