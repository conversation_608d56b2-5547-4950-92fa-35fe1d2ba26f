.e-pod-add {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 8px 16px 16px 16px;
}

.e-pod-field,
.e-vital-pod-field,
.e-dynamic-pod-field
 {
    @include e-block-container-style;
    border: 1px solid var(--colorsUtilityMajor100);
    box-sizing: border-box;

    &.e-read-only>[data-component="heading"]>[data-element="header-container"] h1 {
        color: var(--colorsUtilityMajor100);
    }

    &.e-context-dialog {
        background: transparent;
    }

    .e-pod-title {
        @include e-pod-title-style;
        display: flex;

        [role="tooltip"] {
            order: unset;

            @include extra_small {
                height: 100%;
                line-height: 100%;
            }
        }

        .e-pod-header-spacer {
            flex: 1;
        }

        .e-pod-title-text {
            display: inline-block;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }

        .e-pod-header-label {
            margin-top: -4px;
            margin-left: 8px;
        }

        .e-pod-header-label span[data-component='pill'] {
            margin-top: 0px
        }

        div[data-component='action-popover-wrapper'] {
            flex: 1;
            justify-content: flex-end;
            display: flex;
        }
    }

    .e-field-read-only.e-field-nested-no-input {
        height: 40px;
        padding-left: 0;
        padding-right: 0
    }

    .e-text-area-field .e-field-read-only.e-field-nested-no-input {
        height: auto;
    }

    .e-nested-read-only-label:empty {
        display: none;
    }

    .e-pod-body {
        padding: 16px;
    }

    >[data-component="heading"]>[data-element="header-container"] h1 {
        padding: 10px 16px;
        text-align: center;
    }

    >[data-component="heading"]>[data-element="header-container"]>div {
        display: block !important;
    }

    .e-pod-add {
        align-self: center;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: center;
        margin: 32px 16px 48px 16px;
        text-align: left;
    }
}

.e-block .e-block-body .e-pod-field,
.e-block .e-block-body .e-vital-pod-field, .e-dynamic-pod-field {
    border: none;

    &.e-read-only [data-component="heading"]>[data-element="header-container"] h1 {
        color: var(--colorsUtilityMajor100);
    }

    .e-pod-body {
        padding: 0;
    }

    .e-pod-add {
        align-self: center;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: center;
        margin: 32px 16px 48px 16px;
        text-align: left;
    }

    .e-pod-title {
        @include e-field-field-label;
        line-height: 24px;
        height: 24px;
        margin-bottom: 0;
        display: flex;
    }
}