import type { Dict } from '@sage/xtrem-shared';
import { GridRow } from '@sage/xtrem-ui-components';
import Button from 'carbon-react/esm/components/button';
import Heading from 'carbon-react/esm/components/heading';
import { camelCase, noop } from 'lodash';
import * as React from 'react';
import type { OnTelemetryEventFunction, ReduxResponsive, XtremAppState } from '../../../redux/state';
import { localize } from '../../../service/i18n-service';
import type { FormattedNodeDetails } from '../../../service/metadata-types';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import type { ContextType } from '../../../types';
import { triggerHandledEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import type { PodDecoratorProperties } from '../../decorators';
import { getFieldIndicatorStatus, getFieldTitle } from '../../field/carbon-helpers';
import { HelperText } from '../../field/carbon-utility-components';
import type { NestedBlockProps } from '../nested-block';
import { NestedBlock } from '../nested-block';
import type { XtremActionPopoverItemOrMenuSeparator } from '../xtrem-action-popover';
import XtremActionPopover from '../xtrem-action-popover';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

export interface PodProps {
    availableColumns: number;
    baseAttributesDivWrapper: any;
    browser?: ReduxResponsive;
    contextType?: ContextType;
    elementId: string;
    fieldProperties: PodDecoratorProperties<ScreenBase, any>;
    isDisabled: boolean;
    isReadOnly: boolean;
    onBlockClick: () => void;
    onChange?: (bind: string, value: any) => Promise<void>;
    onNewPod: () => void;
    onRemove: () => void;
    onTelemetryEvent?: OnTelemetryEventFunction;
    screenId: string;
    validationErrors?: ValidationResult[];
    value: any;
}

export function Pod(props: PodProps): React.ReactElement {
    const nodeTypes = useDeepEqualSelector<XtremAppState, Dict<FormattedNodeDetails>>(s => s.nodeTypes);
    const { fieldProperties, browser, value, elementId, screenId } = props;
    const resolvedTitle = getFieldTitle(screenId, fieldProperties, props.value);

    const calculateDropdownActions = (): XtremActionPopoverItemOrMenuSeparator[] => {
        if (!props.fieldProperties.dropdownActions) {
            return [];
        }

        let idx = 0;
        const mapFn = (a: any): XtremActionPopoverItemOrMenuSeparator & { priority: number } => {
            const isDisabled =
                resolveByValue({
                    fieldValue: undefined,
                    propertyValue: a.isDisabled,
                    rowValue: undefined,
                    screenId: props.screenId,
                    skipHexFormat: true,
                }) || false;

            const isHidden =
                resolveByValue({
                    fieldValue: undefined,
                    propertyValue: a.isHidden,
                    rowValue: undefined,
                    screenId: props.screenId,
                    skipHexFormat: true,
                }) || false;

            const isMenuSeparator =
                resolveByValue({
                    fieldValue: undefined,
                    propertyValue: a.isMenuSeparator,
                    rowValue: undefined,
                    screenId: props.screenId,
                    skipHexFormat: true,
                }) || false;

            const children: any = a.children ? a.children.map(mapFn) : [];
            idx += 1;

            const uniqueId = a.id || camelCase(a.title);
            const context: { id?: string; uniqueId: string } = {
                id: a.id,
                uniqueId,
            };
            const testId = `e-pod-action e-pod-action--${uniqueId} e-action e-action--${uniqueId}`;

            return {
                childrenProp: children,
                icon: a.icon,
                isDisabled,
                isHidden,
                isMenuSeparator,
                key: `${idx}_${a.title}`,
                onClick: isMenuSeparator
                    ? noop
                    : (): void => {
                          if (isDisabled) {
                              return;
                          }

                          if (props.onTelemetryEvent) {
                              props.onTelemetryEvent(`podActionTriggered-${uniqueId}`, {
                                  screenId: props.screenId,
                                  elementId: props.elementId,
                                  id: context.id,
                                  uniqueId: context.uniqueId,
                              });
                          }

                          triggerHandledEvent(props.screenId, props.elementId, {
                              onClick: a.onClick,
                              onError: undefined,
                          });
                      },
                priority: idx,
                testId,
                title: a.title,
            };
        };

        return props.fieldProperties.dropdownActions.map<XtremActionPopoverItemOrMenuSeparator>(mapFn);
    };

    const [actionPopoverItems, setActionPopoverItems] =
        React.useState<XtremActionPopoverItemOrMenuSeparator[]>(calculateDropdownActions());
    const onActionPopoverOpen = (): void => {
        setActionPopoverItems(calculateDropdownActions());
    };

    const { info, warning } = getFieldIndicatorStatus(props);

    const nestedBlockProps: NestedBlockProps = {
        actionPopoverItems,
        availableColumns: props.availableColumns,
        baseClassName: 'pod',
        browser,
        contextNode: fieldProperties.node,
        contextType: props.contextType,
        focusPosition: null,
        headerLabel: fieldProperties.headerLabel,
        info,
        isCloseIconDisplayed: fieldProperties.canRemove,
        isDisabled: props.isDisabled,
        isReadOnly: props.isReadOnly,
        item: { $containerId: elementId },
        nestedFields: fieldProperties.columns || [],
        nodeTypes,
        noMargin: true,
        onActionPopoverOpen,
        onBlockClick: props.onBlockClick,
        onBlockRemoved: props.onRemove,
        onChange: props.onChange,
        parentElementId: elementId,
        recordValue: value,
        screenId,
        title: resolvedTitle && !fieldProperties.isTitleHidden ? resolvedTitle : undefined,
        validationErrors: props.validationErrors,
        warning,
    };

    return (
        <div {...props.baseAttributesDivWrapper}>
            {value ? (
                <GridRow columns={props.availableColumns} gutter={0} verticalMargin={0} margin={0}>
                    <NestedBlock {...nestedBlockProps} />
                </GridRow>
            ) : (
                addNewPod({
                    fieldProperties,
                    screenId,
                    value,
                    onNewPod: props.onNewPod,
                    isDisabled: props.isDisabled,
                    onActionPopoverOpen,
                    actionPopoverItems,
                })
            )}
            {props.fieldProperties.helperText && <HelperText helperText={props.fieldProperties.helperText} />}
        </div>
    );
}

const addNewPod = ({
    fieldProperties,
    screenId,
    value,
    onNewPod,
    isDisabled,
    onActionPopoverOpen,
    actionPopoverItems,
}: {
    fieldProperties: PodDecoratorProperties<ScreenBase, any>;
    screenId: string;
    value: any;
    onNewPod: () => void;
    isDisabled: boolean;
    onActionPopoverOpen?: () => void;
    actionPopoverItems?: XtremActionPopoverItemOrMenuSeparator[];
}): React.ReactNode => {
    const isEditable = !fieldProperties.isDisabled && !fieldProperties.isReadOnly;

    return (
        <>
            {((fieldProperties.title && !fieldProperties.isTitleHidden) ||
                (actionPopoverItems && actionPopoverItems.length > 0)) && (
                <div className="e-pod-header">
                    <h3 className="e-pod-title" data-testid="e-pod-title">
                        {!fieldProperties.isTitleHidden && (
                            <span className="e-pod-title-text">{getFieldTitle(screenId, fieldProperties, value)}</span>
                        )}
                        {actionPopoverItems && actionPopoverItems.length > 0 && (
                            <XtremActionPopover items={actionPopoverItems} onOpen={onActionPopoverOpen} />
                        )}
                    </h3>
                </div>
            )}
            <Heading
                data-testid="e-pod-placeholder-text"
                divider={false}
                margin="auto"
                title={
                    fieldProperties.placeholder || localize('@sage/xtrem-ui/pod-placeholder-text', 'No data to display')
                }
            />
            {isEditable && (
                <div className="e-pod-add">
                    <Button
                        buttonType="secondary"
                        data-testid="e-pod-add-new"
                        iconType="plus"
                        onClick={onNewPod}
                        disabled={isDisabled}
                        m="0"
                    >
                        {fieldProperties.addButtonText ||
                            localize('@sage/xtrem-ui/pod-collection-add-new', 'Add an item')}
                    </Button>
                </div>
            )}
        </>
    );
};
