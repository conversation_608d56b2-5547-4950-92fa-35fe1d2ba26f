import React from 'react';
import * as tokens from '@sage/design-tokens/js/base/common';
import { calculateHiddenColumns, getCardDefinitionFromColumns } from '../../../utils/table-component-utils';
import {
    convertDeepBindToPath,
    convertDeepBindToPathNotNull,
    getImagePlaceholderValue,
} from '../../../utils/nested-field-utils';
import { camelCase, get, includes } from 'lodash';
import type { NestedReferenceProperties, PropertyValueType } from '../../field/reference/reference-types';
import { CardComponent, type CardComponentProps, type CardDefinition } from '../card/card-component';
import { type Dict, FieldKey, objectKeys } from '@sage/xtrem-shared';
import type { ImageDecoratorProperties } from '../../decorator-properties';
import { getReferenceValueField } from '../../field/reference/reference-utils';
import { localize } from '../../../service/i18n-service';
import type { ScreenBase } from '../../../service/screen-base';
import type { NestedField, NestedFieldTypesWithoutTechnical } from '../../nested-fields';
import type { ClientNode } from '@sage/xtrem-client';
import type { MobileTableProps } from '../../field/table/mobile-table-component';
import { ContextType } from '../../../types';
import type { CollectionItem, ContainerWidth } from '../../types';
import { MobileTableSkeletonCard } from '../../field/table/mobile-table-skeleton-card';
import { EmptyTableComponent } from '../no-rows-found/no-rows-found-component';
import Loader from 'carbon-react/esm/components/loader';
import Button from 'carbon-react/esm/components/button';
import { GridColumn, GridRow } from '@sage/xtrem-ui-components';
import { calculateContainerWidth, getPageBodyColumnCount } from '../../../utils/responsive-utils';
import type { XtremAppState } from '../../../redux';
import type { ResponsiveTypes } from '../../../redux/state';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

const NAVIGATION_PANEL_ELEMENT_ID_PREFIX = 'navigation-panel-card-';

export interface UiMobileTableProps {
    areCardFieldTitlesDisplayed?: boolean;
    availableColumns?: number;
    canAddRecord?: boolean;
    canDragCard?: boolean;
    columns: MobileTableProps['fieldProperties']['columns'];
    dropdownActions?: MobileTableProps['fieldProperties']['dropdownActions'];
    elementId: string;
    fieldProperties: MobileTableProps['fieldProperties'];
    groupByField: PropertyValueType;
    hasMorePages: boolean;
    inlineActions?: MobileTableProps['fieldProperties']['inlineActions'];
    isDisabled: boolean;
    isFetchingNextPage?: boolean;
    isGreaterThanSmall: boolean;
    isLoading?: boolean;
    isNavigationPanel: boolean;
    isNestedGrid?: boolean;
    isUsingInfiniteScroll?: boolean;
    level?: number;
    onFetchNextPage: () => void;
    onKeyDown?: (event: React.KeyboardEvent<HTMLDivElement>) => void;
    onResetFilter?: () => void;
    onRowClick?: (item: any) => (_item: any, isModifierKeyPushed: boolean) => void;
    onRowSelected: (row: CollectionItem) => void;
    onScroll?: (event: React.SyntheticEvent<HTMLDivElement>) => void;
    recordWidth?: ContainerWidth;
    screenId: string;
    searchText?: string;
    valuesDisplayed: any[];
}

export function MobileTable(props: UiMobileTableProps): JSX.Element {
    const browserIs = useDeepEqualSelector<XtremAppState, ResponsiveTypes>(state => state.browser.is);

    const availableColumns = props.availableColumns ?? getPageBodyColumnCount(browserIs);

    const podWidth = calculateContainerWidth(browserIs, availableColumns, props.recordWidth || 'extra-large');

    const nestedFields = getCardDefinitionFromColumns({
        columns: props.columns,
        mobileCard: props.fieldProperties.mobileCard,
        screenId: props.screenId,
        isGreaterThanSmall: props.isGreaterThanSmall,
        hiddenColumns: props.fieldProperties.hiddenColumns,
        sortColumns: props.fieldProperties.sortColumns,
    });

    const currentFields = [
        convertDeepBindToPath(nestedFields.title?.properties?.bind),
        convertDeepBindToPath(nestedFields.titleRight?.properties?.bind),
        convertDeepBindToPath(nestedFields.line2?.properties?.bind),
        convertDeepBindToPath(nestedFields.line2Right?.properties?.bind),
        convertDeepBindToPath(nestedFields.line3?.properties?.bind),
        convertDeepBindToPath(nestedFields.line3Right?.properties?.bind),
        convertDeepBindToPath(nestedFields.line4?.properties?.bind),
        convertDeepBindToPath(nestedFields.line4Right?.properties?.bind),
        convertDeepBindToPath(nestedFields.line5?.properties?.bind),
        convertDeepBindToPath(nestedFields.line5Right?.properties?.bind),
    ];

    const hiddenFields = calculateHiddenColumns(props.fieldProperties.hiddenColumns ?? [], currentFields) as string[];

    const getCreateNewItem = (): any => ({
        _id: '-1',
        _isNewRecord: true,
        [`${props.fieldProperties.valueField}`]: props.searchText,
    });
    const getRows = (): any => {
        return props.canAddRecord ? [getCreateNewItem(), ...props.valuesDisplayed] : props.valuesDisplayed;
    };

    const getIsRowSelected = (item: CollectionItem): boolean => {
        if (props.isNestedGrid && props.level !== undefined) {
            return (props.fieldProperties?.selectedRecords?.[props.level] || ([] as never)).indexOf(item._id) !== -1;
        }
        return (props.fieldProperties?.selectedRecords || []).indexOf(item._id) !== -1;
    };

    const rows = getRows();

    const renderFooter = (): React.ReactNode => {
        if (props.isUsingInfiniteScroll && props.isFetchingNextPage) {
            return <Loader size="large" />;
        }
        if (!props.isUsingInfiniteScroll && (props.isFetchingNextPage || props.hasMorePages)) {
            return (
                <GridColumn
                    columnSpan={availableColumns}
                    className="e-load-more-button-container"
                    data-testid="e-load-more-button-container"
                >
                    <Button
                        buttonType="tertiary"
                        data-testid={props.isFetchingNextPage ? 'e-is-loading-more-button' : 'e-load-more-button'}
                        disabled={props.isDisabled || !props.hasMorePages || props.isFetchingNextPage}
                        onClick={props.onFetchNextPage}
                    >
                        {props.isFetchingNextPage ? (
                            <Loader size="large" />
                        ) : (
                            localize('@sage/xtrem-ui/mobile-table-load-more', 'Load more')
                        )}
                    </Button>
                </GridColumn>
            );
        }
        return null;
    };

    const hasOnlyOneColumnOnALine = availableColumns <= podWidth;

    const classNames = ['e-table-field-mobile-rows'];
    if (hasOnlyOneColumnOnALine) {
        classNames.push('e-table-field-mobile-rows-one-column');
    }

    return (
        <GridRow
            className={classNames.join(' ')}
            data-testid="e-table-field-mobile-rows"
            onScroll={props.isUsingInfiniteScroll ? props.onScroll : undefined}
            onKeyDown={props.onKeyDown}
            columns={availableColumns}
            gutter={hasOnlyOneColumnOnALine ? 0 : 16}
            margin={0}
            verticalMargin={0}
        >
            {!props.isLoading &&
                rows.map((item: any, index: number): JSX.Element => {
                    const isRowSelected = getIsRowSelected(item);
                    const cardDefinition = getCardDefinition(nestedFields, item, hiddenFields, props.fieldProperties);
                    const previousSeparatorValue = getSeparatorValue(
                        rows[index - 1],
                        props.fieldProperties.columns,
                        props.groupByField,
                    );
                    const currentSeparatorValue = getSeparatorValue(
                        item,
                        props.fieldProperties.columns,
                        props.groupByField,
                    );
                    const hasReferenceFieldLookup =
                        props.fieldProperties._controlObjectType === FieldKey.FilterSelect ||
                        props.fieldProperties._controlObjectType === FieldKey.Reference;

                    const cardComponentProps: CardComponentProps = {
                        areCardFieldTitlesDisplayed: props.areCardFieldTitlesDisplayed,
                        canDrag: !!props.canDragCard,
                        canSelect: props.fieldProperties.canSelect,
                        cardDefinition,
                        contextType: ContextType.table,
                        dropdownActions: props.dropdownActions,
                        id: props.isNavigationPanel ? `${NAVIGATION_PANEL_ELEMENT_ID_PREFIX}${index}` : undefined,
                        inlineActions: props.inlineActions,
                        isClickIndicatorHidden: hasReferenceFieldLookup,
                        isDisabled: props.isDisabled,
                        isNewItem: item._isNewRecord,
                        isSelected: isRowSelected,
                        onCardClick: props.onRowClick ? props.onRowClick(item) : undefined,
                        onSelect: props.onRowSelected,
                        parentElementId: props.elementId,
                        screenId: props.screenId,
                        value: item,
                    };
                    return (
                        <GridColumn columnSpan={podWidth} key={`${props.elementId}_${item._id}`}>
                            {props.groupByField &&
                                currentSeparatorValue &&
                                previousSeparatorValue !== currentSeparatorValue && (
                                    <div
                                        className="e-mobile-table-separator"
                                        data-testid={`e-mobile-table-separator-${camelCase(currentSeparatorValue)}`}
                                    >
                                        {currentSeparatorValue}
                                    </div>
                                )}
                            <CardComponent {...cardComponentProps} />
                        </GridColumn>
                    );
                })}
            {props.isLoading && (
                <>
                    <MobileTableSkeletonCard
                        key="1"
                        availableColumns={availableColumns}
                        cardDefinition={nestedFields}
                    />
                    <MobileTableSkeletonCard
                        key="2"
                        availableColumns={availableColumns}
                        cardDefinition={nestedFields}
                    />
                    <MobileTableSkeletonCard
                        key="3"
                        availableColumns={availableColumns}
                        cardDefinition={nestedFields}
                    />
                    <MobileTableSkeletonCard
                        key="4"
                        availableColumns={availableColumns}
                        cardDefinition={nestedFields}
                    />
                </>
            )}
            {!props.isLoading && !props.valuesDisplayed.length && (
                <GridColumn columnSpan={availableColumns} className="e-mobile-table-body-empty">
                    <EmptyTableComponent
                        elementId={props.elementId}
                        screenId={props.screenId}
                        emptyStateText={props.fieldProperties?.emptyStateText}
                        emptyStateClickableText={props.fieldProperties?.emptyStateClickableText}
                        onEmptyStateLinkClick={props.fieldProperties?.onEmptyStateLinkClick}
                        isFiltering={!!props.searchText}
                        isCardView={true}
                        onRemoveFiltersClick={props.onResetFilter}
                    />
                </GridColumn>
            )}
            {renderFooter()}
        </GridRow>
    );
}

function shouldDisplayElement(
    elementId: PropertyValueType | undefined,
    isNewRecord: boolean,
    hiddenFields: string[],
): boolean {
    return (
        !!elementId &&
        !(isNewRecord && elementId === '_id') &&
        !includes(hiddenFields, convertDeepBindToPathNotNull(elementId))
    );
}

function getNestedReferenceValue(
    definition: Omit<NestedReferenceProperties, 'onChange'>,
    data: any,
): string | undefined {
    const valueField = getReferenceValueField(definition);
    return valueField ? get(data, valueField) : undefined;
}

function getImagePlaceholderValueFromCardDefinition(item: any, nFields: CardDefinition): string {
    const titleElementId = convertDeepBindToPathNotNull(nFields.title?.properties?.bind);
    const value =
        nFields.title?.type === FieldKey.Reference
            ? getNestedReferenceValue(nFields.title.properties as NestedReferenceProperties, get(item, titleElementId))
            : get(item, titleElementId);
    return item._isNewRecord ? `${value} (${localize('@sage/xtrem-ui/new', 'New')})` : value;
}

function getSeparatorValue(
    record: any,
    columns: UiMobileTableProps['fieldProperties']['columns'],
    groupByField: UiMobileTableProps['groupByField'],
): string | null {
    if (!groupByField || !record) {
        return null;
    }

    const nestedField = (columns || []).find(
        c => convertDeepBindToPathNotNull(c.properties.bind) === convertDeepBindToPathNotNull(groupByField),
    );

    if (!nestedField) {
        return null;
    }

    const result = getImagePlaceholderValue(record, nestedField);
    return result || null;
}

function getCardDefinition(
    nestedFields: CardDefinition,
    item: Dict<any>,
    hiddenFields: string[],
    fieldProperties: any,
): CardDefinition {
    const titleRightElementId = nestedFields.titleRight?.properties?.bind;
    const shouldDisplayTitleRight = shouldDisplayElement(titleRightElementId, item._isNewRecord, hiddenFields);
    const line2ElementId = nestedFields.line2?.properties?.bind;
    const shouldDisplayLine2 = shouldDisplayElement(line2ElementId, item._isNewRecord, hiddenFields);

    const line2RightElementId = nestedFields.line2Right?.properties?.bind;
    const shouldDisplayLine2Right = shouldDisplayElement(line2RightElementId, item._isNewRecord, hiddenFields);

    const line3ElementId = nestedFields.line3?.properties?.bind;
    const shouldDisplayLine3 = shouldDisplayElement(line3ElementId, item._isNewRecord, hiddenFields);

    const line3RightElementId = nestedFields.line3Right?.properties?.bind;
    const shouldDisplayLine3Right = shouldDisplayElement(line3RightElementId, item._isNewRecord, hiddenFields);

    const line4ElementId = nestedFields.line4?.properties?.bind;
    const shouldDisplayLine4 = shouldDisplayElement(line4ElementId, item._isNewRecord, hiddenFields);

    const line4RightElementId = nestedFields.line4Right?.properties?.bind;
    const shouldDisplayLine4Right = shouldDisplayElement(line4RightElementId, item._isNewRecord, hiddenFields);

    const line5ElementId = nestedFields.line5?.properties?.bind;
    const shouldDisplayLine5 = shouldDisplayElement(line5ElementId, item._isNewRecord, hiddenFields);

    const line5RightElementId = nestedFields.line5Right?.properties?.bind;
    const shouldDisplayLine5Right = shouldDisplayElement(line5RightElementId, item._isNewRecord, hiddenFields);

    const cardDefinition: CardDefinition = {
        ...nestedFields,
        title: nestedFields.title,
        titleRight: shouldDisplayTitleRight ? nestedFields.titleRight : undefined,
        line2: shouldDisplayLine2 ? nestedFields.line2 : undefined,
        line2Right: shouldDisplayLine2Right ? nestedFields.line2Right : undefined,
        line3: shouldDisplayLine3 ? nestedFields.line3 : undefined,
        line3Right: shouldDisplayLine3Right ? nestedFields.line3Right : undefined,
        line4: shouldDisplayLine4 ? nestedFields.line4 : undefined,
        line4Right: shouldDisplayLine4Right ? nestedFields.line4Right : undefined,
        line5: shouldDisplayLine5 ? nestedFields.line5 : undefined,
        line5Right: shouldDisplayLine5Right ? nestedFields.line5Right : undefined,
    };

    if (
        nestedFields.image &&
        nestedFields.image.type === FieldKey.Image &&
        !(nestedFields.image.properties as ImageDecoratorProperties).placeholderValue
    ) {
        cardDefinition.image = {
            ...nestedFields.image,
            properties: {
                ...nestedFields.image.properties,
                placeholderValue: getImagePlaceholderValueFromCardDefinition(item, nestedFields),
            },
        };
    }

    if (cardDefinition.image && cardDefinition.image.type === FieldKey.Icon) {
        cardDefinition.image.properties = {
            ...cardDefinition.image.properties,
            color: tokens.colorsUtilityMajor300,
            backgroundColor: tokens.colorsActionMinor050,
            backgroundShape: 'circle',
            backgroundSize: 'large',
        };
    }

    if (item._isNewRecord) {
        const nestedValueFieldKey = objectKeys(nestedFields).find(
            key => nestedFields[key]?.properties.bind === fieldProperties.valueField,
        );
        if (nestedValueFieldKey) {
            cardDefinition.title = nestedFields[nestedValueFieldKey] as NestedField<
                ScreenBase,
                NestedFieldTypesWithoutTechnical,
                ClientNode
            >;
        }
    }

    return cardDefinition;
}
