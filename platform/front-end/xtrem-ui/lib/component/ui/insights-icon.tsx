import React from 'react';

export function InsightsIcon(): JSX.Element {
    return (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="Isolation_Mode" clipPath="url(#clip0_5783_8445)">
                <path
                    id="Vector"
                    d="M16.3784 9.79886L12.602 8.30955C12.1857 8.14556 11.8564 7.81586 11.6919 7.3996L10.2024 3.6237C9.66474 2.26053 7.73543 2.26053 7.19775 3.6237L5.70824 7.3996C5.54424 7.81586 5.21449 8.14513 4.79818 8.30955L1.02178 9.79886C-0.34157 10.3365 -0.34157 12.2655 1.02178 12.8031L4.79818 14.2925C5.21449 14.4564 5.5438 14.7861 5.70824 15.2024L7.19775 18.9783C7.73543 20.3415 9.66474 20.3415 10.2024 18.9783L11.6919 15.2024C11.8559 14.7861 12.1857 14.4569 12.602 14.2925L16.3784 12.8031C17.7417 12.2655 17.7417 10.3365 16.3784 9.79886Z"
                    fill="black"
                    fillOpacity="0.9"
                />
                <path
                    id="Vector_2"
                    d="M17.1724 5.65451C18.734 5.65451 20 4.3887 20 2.82725C20 1.26581 18.734 0 17.1724 0C15.6107 0 14.3447 1.26581 14.3447 2.82725C14.3447 4.3887 15.6107 5.65451 17.1724 5.65451Z"
                    fill="#00D639"
                />
            </g>
            <defs>
                <clipPath id="clip0_5783_8445">
                    <rect width="20" height="20" fill="white" />
                </clipPath>
            </defs>
        </svg>
    );
}
