import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
// eslint-disable-next-line import/no-named-default
import { default as CarbonPortrait } from 'carbon-react/esm/components/portrait';
import * as React from 'react';
import { getImageUrlFromValue } from '../field/image/image-utils';

export type PortraitShape = 'square' | 'circle';

export type PortraitSize = 'XS' | 'S' | 'M' | 'ML' | 'L' | 'XL' | 'XXL';

export type PortraitPlaceholderMode = 'Initials' | 'FirstThree';

export interface PortraitProps {
    shape?: PortraitShape;
    size: PortraitSize;
    placeholderValue?: string;
    placeholderMode?: PortraitPlaceholderMode;
    image?: { value: string };
    icon?: IconType;
}

export const getFirstThree = (value?: string): string | undefined => {
    if (typeof value !== 'string') {
        return undefined;
    }

    const length = value.length < 3 ? value.length : 3;
    return value.substring(0, length).toUpperCase();
};

export const getInitials = (value?: string): string | undefined => {
    if (typeof value !== 'string') {
        return undefined;
    }

    const match = value
        .replace(/[^0-9a-z\s]/gi, '')
        .split(' ')
        .filter(s => /\S/.test(s));

    return match && match.length > 0 ? match.map(s => s[0]).join('') : value;
};

export function Portrait({
    icon,
    image,
    shape,
    size,
    placeholderValue,
    placeholderMode,
}: PortraitProps): React.ReactElement {
    return (
        <div className="e-portrait">
            {image && image.value ? (
                <CarbonPortrait
                    shape={shape || 'square'}
                    size={size}
                    src={getImageUrlFromValue(image.value)}
                    alt={placeholderValue || ''}
                />
            ) : placeholderMode === 'FirstThree' ? (
                <CarbonPortrait
                    size={size}
                    alt={placeholderValue || ''}
                    iconType={icon}
                    initials={getFirstThree(placeholderValue)}
                />
            ) : (
                <CarbonPortrait
                    size={size}
                    alt={placeholderValue || ''}
                    iconType={icon}
                    initials={getInitials(placeholderValue)}
                />
            )}
        </div>
    );
}
