import * as React from 'react';
import { useDispatch } from 'react-redux';
import { loadPageDialogContent } from '../../redux/actions/dialog-actions';
import { removeScreenDefinition } from '../../redux/actions';
import { isEqual } from 'lodash';
import type { XtremAppState } from '../../redux';
import type { PageDefinition } from '../../service/page-definition';
import { getArtifactDescription } from '../../utils/transformers';
import { ContextType } from '../../types';
import PageComponent from '../container/page/page-component';
import Loader from 'carbon-react/esm/components/loader';
import { useDeepEqualSelector } from '../../utils/hooks/use-deep-equal-selector';

export interface ScreenComponentProps {
    screenPath: string;
    queryParameters: any;
    values?: any;
    onFinish?: (values: any) => void;
}

/**
 * This component allows us to enable a page to somewhere in the application
 */
export const ScreenComponent = React.memo(
    ({ screenPath, queryParameters, values, onFinish }: ScreenComponentProps): React.ReactElement => {
        const { name: screenId } = getArtifactDescription(screenPath);

        const dispatch = useDispatch();

        React.useEffect(() => {
            dispatch(loadPageDialogContent(screenPath, queryParameters, values, onFinish));
            return (): void => {
                dispatch(removeScreenDefinition(screenPath));
            };
        }, [dispatch, screenPath, queryParameters, values, onFinish]);

        const pageDefinition = useDeepEqualSelector<XtremAppState, PageDefinition | null>(
            s => (s.screenDefinitions[screenId] as PageDefinition) || null,
        );

        return (
            <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                {!pageDefinition?.isReady && <Loader size="large" />}
                {pageDefinition?.isReady && (
                    <PageComponent
                        key={screenId}
                        pageDefinition={pageDefinition}
                        contextType={ContextType.dialog}
                        availableColumns={8}
                    />
                )}
            </div>
        );
    },
    (prevProps: ScreenComponentProps, nextProps: ScreenComponentProps) => isEqual(prevProps, nextProps),
);

ScreenComponent.displayName = 'ScreenComponent';
