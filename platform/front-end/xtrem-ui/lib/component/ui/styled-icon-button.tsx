import Button from 'carbon-react/esm/components/button';
import IconStyle from 'carbon-react/esm/components/icon/icon.style';
import styled from 'styled-components';
import * as tokens from '@sage/design-tokens/js/base/common';

export const StyledIconButton = styled(Button)`
    ${IconStyle} {
        color: ${tokens.colorsUtilityMajor200};
        font-size: 1.6667rem;
        height: 24px;
        min-height: 24px;
    }

    &:hover {
        color: ${tokens.colorsYin065};
        ${IconStyle} {
            color: ${tokens.colorsYin065};
        }
    }

    &:disabled {
        ${IconStyle} {
            color: ${tokens.colorsUtilityMajor100};
        }
    }

    &:disabled:hover {
        background-color: transparent;
        ${IconStyle} {
            color: ${tokens.colorsUtilityMajor100};
        }
    }
`;
