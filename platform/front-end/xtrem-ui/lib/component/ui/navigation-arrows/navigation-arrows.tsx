import * as React from 'react';
import IconButton from 'carbon-react/esm/components/icon-button';
import Icon from 'carbon-react/esm/components/icon';
import * as tokens from '@sage/design-tokens/js/base/common';
import { localize } from '../../../service/i18n-service';

interface HeaderNavigationArrowsProps {
    hasNextRecord: boolean;
    hasPreviousRecord: boolean;
    onNextRecord: () => Promise<void>;
    onPreviousRecord: () => Promise<void>;
}

function NavigationArrows({
    hasNextRecord,
    hasPreviousRecord,
    onNextRecord,
    onPreviousRecord,
}: HeaderNavigationArrowsProps): React.ReactElement {
    return (
        <div className="e-header-navigation-arrow-container" data-testid="e-header-navigation-arrow-container">
            <IconButton
                aria-label={localize('@sage/xtrem-ui/previous-record', 'Previous record')}
                data-testid="e-header-navigation-arrow-previous"
                data-pendoid="headerPreviousRecord"
                onClick={(): Promise<void> => onPreviousRecord()}
                disabled={!hasPreviousRecord}
            >
                <Icon
                    type="arrow_up"
                    color={tokens.colorsYin090}
                    tooltipMessage={localize('@sage/xtrem-ui/previous-record', 'Previous record')}
                />
            </IconButton>
            <IconButton
                aria-label={localize('@sage/xtrem-ui/next-record', 'Next record')}
                data-testid="e-header-navigation-arrow-next"
                data-pendoid="headerNextRecord"
                onClick={(): Promise<void> => onNextRecord()}
                disabled={!hasNextRecord}
            >
                <Icon
                    type="arrow_down"
                    color={tokens.colorsYin090}
                    tooltipMessage={localize('@sage/xtrem-ui/next-record', 'Next record')}
                />
            </IconButton>
        </div>
    );
}

export default NavigationArrows;
