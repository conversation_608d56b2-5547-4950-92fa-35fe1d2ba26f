import * as React from 'react';
import Preview from 'carbon-react/esm/components/preview';

export interface InputFieldSkeletonProps {
    hasTitle?: boolean;
    bodyHeight?: string;
}

export const InputFieldSkeleton: React.FC<InputFieldSkeletonProps> = React.memo<InputFieldSkeletonProps>(
    ({ hasTitle, bodyHeight = '40px' }: InputFieldSkeletonProps) => (
        <div className="e-input-field-skeleton" data-testid="e-input-field-skeleton">
            {hasTitle && (
                <div className="e-input-field-skeleton">
                    <Preview loading width="60px" height="18px" />
                </div>
            )}
            <div className="e-input-field-skeleton-body">
                <Preview height={bodyHeight} />
            </div>
        </div>
    ),
);

InputFieldSkeleton.displayName = 'InputFieldSkeleton';
