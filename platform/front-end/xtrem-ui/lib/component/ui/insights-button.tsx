import Button from 'carbon-react/esm/components/button';
import React from 'react';
import { InsightsIcon } from './insights-icon';
import { useSelector } from 'react-redux';
import type * as xtremRedux from '../../redux';
import { getPageDefinitionFromState } from '../../utils/state-utils';
import { localize } from '../../service/i18n-service';

declare global {
    interface Window {
        GmsChatUi?: {
            setDisplayState: Function;
            navigateTo: Function;
        };
    }
}

export function PageHeaderInsightsButton({ screenId }: { screenId: string }): React.ReactElement | null {
    const insightCount = useSelector<xtremRedux.XtremAppState, number>(
        s => getPageDefinitionFromState(screenId, s)?.insightCount ?? 0,
    );

    const onButtonClick = React.useCallback(() => {
        window.GmsChatUi?.setDisplayState?.('popup-right');
        window.GmsChatUi?.navigateTo?.('insights');
    }, []);

    const singularInsightMessage = React.useMemo(() => localize('@sage/xtrem-ui/insight-singular', '1 insight'), []);
    const pluralInsightMessage = React.useMemo(
        () => localize('@sage/xtrem-ui/insight-plural', '{{insightCount}} insights', { insightCount }),
        [insightCount],
    );

    if (!insightCount) {
        return null;
    }

    return (
        <div className="e-header-insights">
            <Button buttonType="gradient-white" size="small" onClick={onButtonClick}>
                <span className="e-header-insights-icon">
                    <InsightsIcon />
                </span>
                {insightCount === 1 ? singularInsightMessage : pluralInsightMessage}
            </Button>
        </div>
    );
}
