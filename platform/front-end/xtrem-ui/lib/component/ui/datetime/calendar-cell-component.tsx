import React from 'react';
import { CalendarCell, CalendarStateContext } from 'react-aria-components';
import type { CalendarDate } from '@internationalized/date';

import { calendarDateToDatetime, isDateInRange } from './datetime-utils';

import { DateValue } from '@sage/xtrem-date-time';

export function CalendarCellComponent({
    day,
    rangeStartCalendarDate,
    selectedDate,
    timeZone,
}: {
    day: CalendarDate;
    rangeStartCalendarDate?: CalendarDate | null;
    selectedDate?: CalendarDate;
    timeZone: string;
}): JSX.Element {
    const state = React.useContext(CalendarStateContext);

    const isToday = DateValue.fromJsDate(day.toDate(timeZone)).equals(DateValue.today());
    const isCellUnavailable = state.isCellUnavailable(day) || state.isCellDisabled(day);
    const isRange = rangeStartCalendarDate && selectedDate && rangeStartCalendarDate.compare(selectedDate) !== 0;
    const isStartDateInRange =
        !isCellUnavailable && selectedDate && rangeStartCalendarDate && day.compare(rangeStartCalendarDate) === 0;
    const isEndDateInRange = !isCellUnavailable && selectedDate && day.compare(selectedDate) === 0;
    const isFirstSelectedInRange = !isCellUnavailable && isRange && isStartDateInRange;
    const isLastSelectedInRange = !isCellUnavailable && isRange && isEndDateInRange;
    const isInRangeSelected =
        !isCellUnavailable && isRange && !isFirstSelectedInRange && !isLastSelectedInRange
            ? isDateInRange(
                  calendarDateToDatetime(day),
                  calendarDateToDatetime(rangeStartCalendarDate),
                  calendarDateToDatetime(selectedDate),
              )
            : false;
    const isSelected = (!isCellUnavailable && isStartDateInRange) || isEndDateInRange;

    const classNames: string[] = ['react-aria-CalendarCell'];
    if (isSelected) classNames.push('is-selected');
    if (isInRangeSelected) classNames.push('is-inter-selected');
    if (isFirstSelectedInRange) classNames.push('is-first-selected-in-range');
    if (isLastSelectedInRange) classNames.push('is-last-selected-in-range');
    if (isToday) classNames.push('e-date-today');

    return (
        <CalendarCell date={day} className={classNames.join(' ')} key={day.toString()}>
            {({ formattedDate }) => <div className="e-calendar-date-cell">{formattedDate}</div>}
        </CalendarCell>
    );
}
