import { CalendarDate } from '@internationalized/date';
import { Datetime, Time, setLocalizeImplementation } from '@sage/xtrem-date-time';
import {
    toDatetime,
    calendarDateToDatetime,
    datetimeToCalendarDate,
    datetimeToTime,
    isDateInRange,
    isValidTimeZone,
    makeDatetime,
} from '../datetime-utils';
import { localize } from '../../../../service/i18n-service';

setLocalizeImplementation(localize);

describe('datetime-range-utils', () => {
    describe('toDatetime', () => {
        it('should convert Datetime object to Datetime object with correct timezone', () => {
            const datetime = Datetime.now();
            const timeZone = 'America/New_York';
            const result = toDatetime(datetime, timeZone);
            expect(result).toBeInstanceOf(Datetime);
            expect(result.timeZone).toBe(timeZone);
        });

        it('should convert Date object to Datetime object with correct timezone', () => {
            const date = new Date();
            const timeZone = 'Europe/London';
            const result = toDatetime(date, timeZone);
            expect(result).toBeInstanceOf(Datetime);
            expect(result.timeZone).toBe(timeZone);
        });

        it('should convert ISO string to Datetime object with correct timezone', () => {
            const isoString = '2023-07-22T12:00:00Z';
            const timeZone = 'Asia/Tokyo';
            const result = toDatetime(isoString, timeZone);
            expect(result).toBeInstanceOf(Datetime);
            expect(result.timeZone).toBe(timeZone);
        });

        it('should throw error for invalid input', () => {
            expect(() => toDatetime(123 as any, 'UTC')).toThrow('Invalid date parameter');
        });
    });

    describe('calendarDateToDatetime', () => {
        it('should convert CalendarDate to Datetime with correct timezone', () => {
            const calendarDate = new CalendarDate(2023, 7, 22);
            const timeZone = 'Australia/Sydney';
            const result = calendarDateToDatetime(calendarDate, timeZone);
            expect(result).toBeInstanceOf(Datetime);
            expect(result.year).toBe(2023);
            expect(result.month).toBe(7);
            expect(result.day).toBe(22);
            expect(result.timeZone).toBe(timeZone);
        });
    });

    describe('datetimeToCalendarDate', () => {
        it('should convert Datetime to CalendarDate with correct timezone', () => {
            const datetime = Datetime.make(2023, 7, 22, 12, 0, 0, 0, 'UTC');
            const timeZone = 'America/Los_Angeles';
            const result = datetimeToCalendarDate(datetime, timeZone);
            expect(result).toBeInstanceOf(CalendarDate);
            expect(result.year).toBe(2023);
            expect(result.month).toBe(7);
            expect(result.day).toBe(22);
        });
    });

    describe('datetimeToTime', () => {
        it('should convert Datetime to time string with correct timezone', () => {
            const datetime = Datetime.make(2023, 7, 22, 12, 30, 45, 0, 'UTC');
            const timeZone = 'Europe/Berlin';
            const result = datetimeToTime(datetime, timeZone);
            expect(result).toBe('14:30:45');
        });
    });

    describe('isDateInRange', () => {
        it('should return true for date within range', () => {
            const date = Datetime.make(2023, 7, 15);
            const startDate = Datetime.make(2023, 7, 1);
            const endDate = Datetime.make(2023, 7, 31);
            expect(isDateInRange(date, startDate, endDate)).toBe(true);
        });

        it('should return false for date outside range', () => {
            const date = Datetime.make(2023, 8, 1);
            const startDate = Datetime.make(2023, 7, 1);
            const endDate = Datetime.make(2023, 7, 31);
            expect(isDateInRange(date, startDate, endDate)).toBe(false);
        });
    });

    describe('isValidTimeZone', () => {
        it('should return true for valid timezone', () => {
            expect(isValidTimeZone('UTC')).toBe(true);
            expect(isValidTimeZone('America/New_York')).toBe(true);
        });

        it('should return false for invalid timezone', () => {
            expect(isValidTimeZone('Invalid/Timezone')).toBe(false);
        });
    });

    describe('makeDatetime', () => {
        it('should create Datetime from CalendarDate and Time with correct timezone', () => {
            const calendarDate = new CalendarDate(2023, 7, 22);
            const timeZone = 'Asia/Kolkata';
            const result = makeDatetime(calendarDate, Time.parse('11:00:00'), timeZone);
            expect(result).toBeInstanceOf(Datetime);
            expect(result.year).toBe(2023);
            expect(result.month).toBe(7);
            expect(result.day).toBe(22);
            expect(result.hour).toBe(11);
            expect(result.minute).toBe(0);
            expect(result.second).toBe(0);
            expect(result.timeZone).toBe(timeZone);
        });
    });
});
