import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import React from 'react';
import { DatetimeInputComponent } from '../datetime-input-component';
import '@testing-library/jest-dom';
import { datetimeToCalendarDate, toDatetime } from '../datetime-utils';
import type { LocalizeLocale } from '@sage/xtrem-shared';
import { datePropertyValueToCalendarDate } from '../../../../utils/date-utils';

describe('DatetimeInputComponent', () => {
    beforeAll(() => {
        HTMLElement.prototype.showPopover = jest.fn();
        HTMLElement.prototype.hidePopover = jest.fn();
    });

    const defaultProps = {
        'aria-label': 'Test Datetime Input',
        screenId: 'test-screen-id',
        elementId: 'test-element-id',
        date: null,
        fieldId: 'test-field-id',
        isDisabled: false,
        isPopoverOpen: false,
        isReadOnly: false,
        isTimeZoneHidden: false,
        locale: 'en-US' as LocalizeLocale,
        maxDate: datePropertyValueToCalendarDate(new Date(2100, 11, 31)),
        minDate: datePropertyValueToCalendarDate(new Date(1970, 0, 1)),
        onDateChange: jest.fn(),
        onPopperOpenChange: jest.fn(),
        onTimeChange: jest.fn(),
        rangeStartDate: null,
        time: null,
        timeZone: 'UTC',
        title: 'Test Title',
        type: 'start' as 'start' | 'end',
    };

    const getTodayLabel = (selected: boolean = false): string => {
        const today = new Date();
        const day = today.toLocaleDateString('en-US', { weekday: 'long' });
        const date = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
        return `Today, ${day}, ${date}${selected ? ' selected' : ''}`;
    };

    it('should render with default props', () => {
        render(<DatetimeInputComponent {...defaultProps} />);
        expect(screen.getByLabelText('Test Datetime Input')).toBeInTheDocument();
    });

    it('open timezone button should by default be disabled', async () => {
        const { findByTestId } = render(<DatetimeInputComponent {...defaultProps} isPopoverOpen />);
        expect(await findByTestId('e-time-component-open-timezone-start')).toBeDisabled();
    });

    it('should open popover on focus', () => {
        render(<DatetimeInputComponent {...defaultProps} />);
        fireEvent.focus(screen.getByLabelText('Test Datetime Input'));
        expect(defaultProps.onPopperOpenChange).toHaveBeenCalledWith(true, 'start');
    });

    it('should call onDateChange when a date is selected', () => {
        render(<DatetimeInputComponent {...defaultProps} isPopoverOpen />);
        const dateCell = screen.getByText('15');
        fireEvent.click(dateCell);
        expect(defaultProps.onDateChange).toHaveBeenCalled();
    });

    it('should call onTimeChange when time is changed', () => {
        render(<DatetimeInputComponent {...defaultProps} isPopoverOpen />);
        const hoursInput = screen.getByTestId('e-datetime-input-start-time-input-hours');
        const minutesInput = screen.getByTestId('e-datetime-input-start-time-input-minutes');
        fireEvent.click(hoursInput);
        fireEvent.change(hoursInput, { target: { value: '12' } });
        fireEvent.click(minutesInput);
        fireEvent.change(minutesInput, { target: { value: '30' } });
        fireEvent.blur(minutesInput);
        expect(defaultProps.onTimeChange).toHaveBeenCalledWith('00:30:00');
    });

    it('should hide timezone input when isTimeZoneHidden is true', () => {
        render(<DatetimeInputComponent {...defaultProps} isTimeZoneHidden />);
        expect(screen.queryByLabelText('Time zone:')).not.toBeInTheDocument();
    });

    it('should show today with the right class applied', async () => {
        const todayDate = datetimeToCalendarDate(toDatetime(new Date(), 'UTC'), 'UTC');
        render(<DatetimeInputComponent {...defaultProps} date={todayDate} isPopoverOpen />);
        await waitFor(() => {
            const today = document.body.querySelector('.e-date-today');
            expect(today).toBeInTheDocument();

            let ariaLabel = today?.getAttribute('aria-label');
            const expectedLabel = getTodayLabel(false);

            if (ariaLabel) {
                ariaLabel = ariaLabel.replace(' selected', '');
            }

            expect(ariaLabel).toBe(expectedLabel);
        });
    });
});
