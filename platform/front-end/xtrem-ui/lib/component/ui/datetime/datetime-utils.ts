import { CalendarDate } from '@internationalized/date';
import {
    Datetime,
    formatDateToCurrentLocale,
    formatTimeToLocale,
    isValidIsoDate,
    type Time,
} from '@sage/xtrem-date-time';
import { type LocalizeLocale } from '@sage/xtrem-shared';
import { isNil } from 'lodash';
import type { DatetimeRangeValue } from '../../field/datetime-range/datetime-range-types';

export function calendarDateToDatetime(calendarDate: CalendarDate, timeZone?: string): Datetime {
    const { year, month, day } = calendarDate;
    return Datetime.make(year, month, day, 0, 0, 0, 0, timeZone);
}

export function datetimeToCalendarDate(datetime: Datetime, timeZone: string): CalendarDate {
    const valueWithTz = datetime.inTimeZone(timeZone);
    return new CalendarDate(valueWithTz.year, valueWithTz.month, valueWithTz.day);
}

export function datetimeToTime(datetime: Datetime, timeZone: string): string {
    const valueWithTz = datetime.inTimeZone(timeZone);
    return valueWithTz.time.toString();
}

function areDateTimesEqual(a: Datetime | null | undefined, b: Datetime | null | undefined): boolean {
    if (isNil(a) || isNil(b)) {
        return (a ?? null) === (b ?? null);
    }
    return a.equals(b);
}

export function areDateRangesEqual(
    a: DatetimeRangeValue | null | undefined,
    b: DatetimeRangeValue | null | undefined,
): boolean {
    return areDateTimesEqual(a?.start, b?.start) && areDateTimesEqual(a?.end, b?.end);
}

export function toDatetime(
    input: Datetime | Date | string,
    timeZone: string,
    locale: LocalizeLocale = 'base',
): Datetime {
    if (Datetime.isDatetime(input)) {
        return (input as Datetime).inTimeZone(timeZone);
    }
    if (input instanceof Date) {
        return Datetime.fromJsDate(input, timeZone);
    }
    if (typeof input === 'string' && isValidIsoDate(input)) {
        return Datetime.parse(input, locale, undefined, timeZone);
    }

    throw new Error('Invalid date parameter');
}

export const formatDatetime = ({
    date,
    locale = 'base',
    separator = ' - ',
}: {
    date: Datetime | null | undefined;
    locale?: LocalizeLocale;
    separator?: string;
}): string => {
    if (!date) {
        return '';
    }
    const formattedDate = formatDateToCurrentLocale(date, locale, 'FullDate');
    return [formattedDate, formatTimeToLocale(date.time, locale)].join(separator);
};

export const isDateInRange = (date: Datetime, startDate: Datetime, endDate: Datetime): boolean => {
    return date.compare(startDate) >= 0 && date.compare(endDate) <= 0;
};

export function isValidTimeZone(timeZone: string): boolean {
    try {
        Intl.DateTimeFormat(undefined, { timeZone });
        return true;
    } catch (e) {
        return false;
    }
}

export const makeDatetime = (date: CalendarDate, time: Time, timeZone = 'UTC'): Datetime => {
    return Datetime.make(date.year, date.month, date.day, time.hour, time.minute, time.second, undefined, timeZone);
};
