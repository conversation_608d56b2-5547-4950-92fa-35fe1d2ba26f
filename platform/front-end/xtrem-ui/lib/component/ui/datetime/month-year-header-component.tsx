import React, { useContext, useMemo, useCallback } from 'react';
import { Button as CalendarButton, CalendarStateContext } from 'react-aria-components';
import Icon from 'carbon-react/esm/components/icon';
import { localize } from '../../../service/i18n-service';

interface MonthYearHeaderComponentProps {
    isDisabled?: boolean;
}

export function MonthYearHeaderComponent({ isDisabled }: MonthYearHeaderComponentProps): JSX.Element {
    const state = useContext(CalendarStateContext);
    const months = useMemo(
        () => [
            localize('@sage/xtrem-ui/dashboard-month_1', 'January'),
            localize('@sage/xtrem-ui/dashboard-month_2', 'February'),
            localize('@sage/xtrem-ui/dashboard-month_3', 'March'),
            localize('@sage/xtrem-ui/dashboard-month_4', 'April'),
            localize('@sage/xtrem-ui/dashboard-month_5', 'May'),
            localize('@sage/xtrem-ui/dashboard-month_6', 'June'),
            localize('@sage/xtrem-ui/dashboard-month_7', 'July'),
            localize('@sage/xtrem-ui/dashboard-month_8', 'August'),
            localize('@sage/xtrem-ui/dashboard-month_9', 'September'),
            localize('@sage/xtrem-ui/dashboard-month_10', 'October'),
            localize('@sage/xtrem-ui/dashboard-month_11', 'November'),
            localize('@sage/xtrem-ui/dashboard-month_12', 'December'),
        ],
        [],
    );

    const years = useMemo(
        () => Array.from({ length: 20 }, (_, index) => state.visibleRange.start.year - 10 + index),
        [state.visibleRange.start.year],
    );

    const handleMonthChange = useCallback(
        (ev: React.ChangeEvent<HTMLSelectElement>): void => {
            const newDate = state.focusedDate.set({ month: Number(ev.target.value) + 1 });
            state.setFocusedDate(newDate);
        },
        [state],
    );

    const handleYearChange = useCallback(
        (ev: React.ChangeEvent<HTMLSelectElement>): void => {
            const newDate = state.focusedDate.set({ year: Number(ev.target.value) });
            state.setFocusedDate(newDate);
        },
        [state],
    );

    return (
        <div className="e-calendar-header" data-disabled={String(isDisabled)}>
            <div className="e-month-selector-wrapper">
                <CalendarButton
                    className="navigate-month-button-left"
                    slot="previous"
                    onPress={() => state.focusPreviousPage()}
                >
                    <Icon
                        ariaLabel={localize('@sage/xtrem-ui/previous-month', 'Previous month')}
                        className="navigate-month-icon"
                        fontSize="small"
                        type="chevron_left"
                    />
                </CalendarButton>
                <select
                    disabled={isDisabled}
                    aria-label={localize('@sage/xtrem-ui/calendar-month', 'Month')}
                    className="e-month-selector"
                    onChange={handleMonthChange}
                    value={state.visibleRange.start.month - 1}
                >
                    {months.map((monthName, index) => (
                        <option key={`month-${monthName}`} value={index}>
                            {monthName}
                        </option>
                    ))}
                </select>
                <CalendarButton
                    className="navigate-month-button-right"
                    slot="next"
                    onPress={() => state.focusNextPage()}
                >
                    <Icon
                        ariaLabel={localize('@sage/xtrem-ui/next-month', 'Next month')}
                        className="navigate-month-icon"
                        fontSize="small"
                        type="chevron_right"
                    />
                </CalendarButton>
            </div>
            <select
                disabled={isDisabled}
                aria-label={localize('@sage/xtrem-ui/calendar-view-year', 'Year')}
                className="e-year-selector"
                onChange={handleYearChange}
                value={state.visibleRange.start.year}
            >
                {years.map(year => (
                    <option key={`year-${year}`} value={year}>
                        {year}
                    </option>
                ))}
            </select>
        </div>
    );
}
