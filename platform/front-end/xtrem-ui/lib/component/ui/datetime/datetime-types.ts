import type { CalendarDate } from '@internationalized/date';
import type { LocalizeLocale } from '@sage/xtrem-shared';
import type Textbox from 'carbon-react/esm/components/textbox';

export interface DatetimeComponentProps {
    'aria-label': string;
    screenId?: string;
    elementId?: string;
    date: CalendarDate | null;
    fieldId?: string;
    inputRef?: React.RefObject<HTMLInputElement>;
    isDisabled?: boolean;
    isPopoverOpen: boolean;
    isReadOnly?: boolean;
    isTimeZoneHidden?: boolean;
    locale: LocalizeLocale;
    maxDate?: CalendarDate;
    minDate?: CalendarDate;
    onDateChange: (date: CalendarDate | null) => void;
    onPopperOpenChange: (isOpen: boolean, inputName: 'start' | 'end' | 'single') => void;
    onTimeChange: (string: string | null) => void;
    rangeStartDate: CalendarDate | null;
    time: string | null;
    timeZone?: string;
    title?: string;
    type: 'start' | 'end' | 'single';
    validationError?: string;
    initialDate?: CalendarDate;
    size?: React.ComponentProps<typeof Textbox>['size'];
    nestedRowId?: string;
}
