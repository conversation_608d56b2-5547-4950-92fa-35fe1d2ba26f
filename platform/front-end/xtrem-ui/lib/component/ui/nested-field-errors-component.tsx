import React from 'react';
import { CollectionValue } from '../../service/collection-data-service';
import { localize } from '../../service/i18n-service';
import { resolveByValue } from '../../utils/resolve-value-utils';
import type { ValueOrCallbackWithFieldValue } from '../../utils/types';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../redux';
import type { ValidationResult } from '../../service/screen-base-definition';
import { messageDialog } from '../../service/dialog-service';

const ERRORS_TO_DISPLAY = 10;

export interface NestedFieldErrorsExternalProps {
    screenId: string;
    elementId: string;
}
export interface NestedFieldErrorsProps extends NestedFieldErrorsExternalProps {
    validationErrors: ValidationResult[];
    value: any;
    fieldType: string;
    fieldTitle?: ValueOrCallbackWithFieldValue<any, string>;
}
export interface TableValidationError {
    fieldName: string;
    mainFieldName?: string;
    mainFieldValue: string;
    columnId?: string;
    columnName?: string;
    message: string;
    recordId: string;
    validationRule: string;
    level?: number;
}

interface NestedFieldErrorsState {
    isDialogOpen: boolean;
}
class NestedFieldErrors extends React.Component<NestedFieldErrorsProps, NestedFieldErrorsState> {
    getTitle = (): string => {
        const totalErrors = this.props.validationErrors.length;
        const title = resolveByValue({
            screenId: this.props.screenId,
            propertyValue: this.props.fieldTitle,
            skipHexFormat: true,
            rowValue: null,
        })?.trim();
        const gridName = title != null && title !== '' ? title : this.props.elementId;
        const isPod = this.props.fieldType === 'PodCollection';
        return totalErrors > 1
            ? isPod
                ? localize(
                      '@sage/xtrem-ui/validation-errors-with-number-pod',
                      'You have {{0}} errors in the following collection: {{1}}.',
                      [totalErrors, gridName],
                  )
                : localize(
                      '@sage/xtrem-ui/validation-errors-with-number-grid',
                      'You have {{0}} errors in the following grid: {{1}}.',
                      [totalErrors, gridName],
                  )
            : isPod
              ? localize(
                    '@sage/xtrem-ui/validation-error-with-number-pod',
                    'You have 1 error in the following collection: {{0}}.',
                    [gridName],
                )
              : localize(
                    '@sage/xtrem-ui/validation-error-with-number-grid',
                    'You have 1 error in the following grid: {{0}}.',
                    [gridName],
                );
    };

    onRemainingErrorsClick = async (): Promise<void> => {
        if (this.props.value instanceof CollectionValue) {
            await this.props.value.fetchAllErrors();
        }
    };

    onClick = async (): Promise<void> => {
        await this.onRemainingErrorsClick();

        const dialogBody = this.props.validationErrors
            .map(({ message, messagePrefix }) => `- ${messagePrefix ? `**${messagePrefix}**:` : ''} ${message}`)
            .join('\n');
        await messageDialog(this.props.screenId, 'info', undefined, `######${this.getTitle()}\n${dialogBody}`, {
            acceptButton: { isHidden: true },
            mdContent: true,
        });
    };

    getErrorList = (limit?: number): React.ReactNode => {
        return (
            <ul>
                {this.props.validationErrors
                    .slice(0, limit ?? this.props.validationErrors.length)
                    .map(({ validationRule, columnId, recordId, message, messagePrefix }) => {
                        return (
                            <li key={`${validationRule}-${columnId}-${recordId}`}>
                                {messagePrefix && <strong>{`${messagePrefix}:`}</strong>}
                                &#32;
                                {message}
                            </li>
                        );
                    })}
            </ul>
        );
    };

    render(): React.ReactNode {
        const remainingErrors = this.props.validationErrors.length - ERRORS_TO_DISPLAY;
        const remainingErrorMessage =
            remainingErrors > 1
                ? localize('@sage/xtrem-ui/validation-errors-and-more', 'and {{0}} more errors', [remainingErrors])
                : localize('@sage/xtrem-ui/validation-error-and-more', 'and 1 more error');

        const title = this.getTitle();
        return (
            <div className="e-table-validation-global-errors">
                <h6>{title}</h6>
                <div className="e-table-validation-errors">
                    {this.getErrorList(ERRORS_TO_DISPLAY)}
                    {(remainingErrors ?? 0) > 0 && (
                        <button className="e-table-validation-errors-remaining" onClick={this.onClick} type="button">
                            {remainingErrorMessage}
                        </button>
                    )}
                </div>
            </div>
        );
    }
}

export const ConnectedNestedFieldErrors = connect(
    (state: xtremRedux.XtremAppState, props: NestedFieldErrorsExternalProps): NestedFieldErrorsProps => {
        const screenDefinition = state.screenDefinitions[props.screenId];

        return {
            ...props,
            validationErrors: screenDefinition?.errors?.[props.elementId] ?? [],
            value: screenDefinition?.values[props.elementId],
            fieldTitle: screenDefinition?.metadata.uiComponentProperties[props.elementId].title,
            fieldType: screenDefinition?.metadata.uiComponentProperties[props.elementId]._controlObjectType!,
        };
    },
)(NestedFieldErrors);
