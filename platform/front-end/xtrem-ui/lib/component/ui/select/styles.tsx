import * as tokens from '@sage/design-tokens/js/base/common';
import type { MutableRefObject } from 'react';

const calculateTopValue = (inputRef?: MutableRefObject<HTMLInputElement | null>): string => {
    if (!inputRef?.current) {
        return '-24px';
    }
    const boundRect = document.getElementById(`${inputRef.current.id}-field-help`)?.getBoundingClientRect();
    if (!boundRect) {
        return '-24px';
    }

    return `-${boundRect.height + 8}px`;
};

export const ulDivStyles = ({
    hasHelperTextField = false,
    inputRef,
    variant = 'plain',
}: {
    hasHelperTextField?: boolean;
    inputRef?: MutableRefObject<HTMLInputElement | null>;
    variant?: 'plain' | 'carbon';
}): React.CSSProperties => {
    let top: string | undefined;

    if (hasHelperTextField) {
        top = calculateTopValue(inputRef);
    }

    return {
        ...(hasHelperTextField && { top }),
        position: 'relative',
        width: '100%',
        minWidth: variant === 'plain' ? '220px' : undefined,
    };
};
export const ulStyles = ({
    shouldRenderOptionsAbove = false,
    hasLookupLinkLine = false,
    maxHeight = 180,
    hasCreateNewItemLink = false,
}: {
    shouldRenderOptionsAbove?: boolean;
    hasLookupLinkLine?: boolean;
    maxHeight?: number;
    hasCreateNewItemLink?: boolean;
}): React.CSSProperties => {
    let maxHeightUpdated = maxHeight;
    let offset = 40;
    if (hasLookupLinkLine) {
        offset += 33;
        maxHeightUpdated -= 33;
    }
    if (hasCreateNewItemLink) {
        offset += 33;
        maxHeightUpdated -= 33;
    }
    return {
        maxHeight: maxHeightUpdated,
        ...((hasLookupLinkLine || hasCreateNewItemLink) &&
            !shouldRenderOptionsAbove && {
                borderTopLeftRadius: tokens.borderRadius050,
                borderTopRightRadius: tokens.borderRadius050,
            }),
        ...((hasLookupLinkLine || hasCreateNewItemLink) &&
            shouldRenderOptionsAbove && {
                borderBottomLeftRadius: tokens.borderRadius050,
                borderBottomRightRadius: tokens.borderRadius050,
            }),
        ...(!hasLookupLinkLine && !hasCreateNewItemLink && { borderRadius: tokens.borderRadius050 }),
        ...(shouldRenderOptionsAbove && {
            borderRadius: '4px',
            bottom: `${offset}px`,
            boxShadow: '0 0px 5px 0 #00141e33, 0 10px 10px 0 #00141e1a',
        }),
    };
};

export const liStyles = ({ isActive = false, isSelected = false } = {}): React.CSSProperties => {
    return {
        ...(isActive && { backgroundColor: tokens.colorsUtilityMajor050 }),
        ...(isSelected && { fontWeight: 'bold' }),
        padding: '10px 20px 5',
        paddingLeft: '10px',
        paddingRight: '10px',
        whiteSpace: 'pre-wrap',
        flex: 1,
        boxSizing: 'border-box',
    };
};
