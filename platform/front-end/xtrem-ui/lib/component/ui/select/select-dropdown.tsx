import Link from 'carbon-react/esm/components/link';
import type { UseComboboxGetItemPropsOptions } from 'downshift';
import { noop } from 'lodash';
import { useMemo, type MutableRefObject } from 'react';
import React from 'react';
import { localize } from '../../../service/i18n-service';
import type { SelectItem } from './select-component';
import { SelectDropDownItem } from './select-dropdown-item';
import { liStyles, ulDivStyles, ulStyles } from './styles';
import { SelectCreateNewItem } from './select-create-new-item';
import { objectKeys } from '@sage/xtrem-shared';

interface SelectDropDownProps {
    addSelectedItem: (item: SelectItem) => void;
    createTunnelLinkRef?: React.RefObject<HTMLButtonElement | HTMLAnchorElement>;
    createTunnelLinkText?: string;
    focusLookupButton: () => void;
    fullWidth?: boolean;
    getItemProps?: (options: UseComboboxGetItemPropsOptions<SelectItem>) => any;
    hasHelperTextField?: boolean;
    hasHighlightMatchText: boolean;
    hasLookupIcon?: boolean;
    highlightedIndex?: number;
    highlightFirstListItem: () => void;
    highlightLastListItem: () => void;
    inputId?: string;
    inputRef?: MutableRefObject<HTMLInputElement | null>;
    inputValue: string;
    isItemSelected?: (args: { id: string; value: string }) => boolean;
    isLinkCreateNewText?: boolean;
    isMultiSelect?: boolean;
    isOpen: boolean;
    items: SelectItem[];
    linkElement?: React.ReactNode;
    loading: boolean;
    lookupLinkRef?: React.RefObject<HTMLButtonElement | HTMLAnchorElement>;
    maxHeight?: number;
    menuProps?: any;
    minLookupCharacters?: number;
    node?: any;
    onCreateNewItemLinkClick?: () => void;
    onLookupIconClick?: NonNullable<React.ComponentProps<typeof Link>['onClick']>;
    removeSelectedItem?: (item: SelectItem) => void;
    screenId?: string;
    shouldRenderOptionsAbove?: boolean;
    ulRef: React.RefObject<HTMLUListElement>;
    variant?: 'plain' | 'carbon';
}

function NoItemsFound({ fullWidth = false }: { fullWidth: boolean }): React.ReactElement {
    return (
        <li key="NOT_FOUND" style={liStyles()}>
            <div className="e-ui-select-suggestion" data-testid="e-ui-select-suggestion">
                <div
                    className="e-ui-select-suggestion-value-wrapper"
                    data-testid="e-ui-select-suggestion-value-wrapper"
                >
                    <div
                        className="e-ui-select-suggestion-value"
                        data-testid="e-ui-select-suggestion-value"
                        style={{ ...(!fullWidth && { flex: 1, textAlign: 'left' }) }}
                    >
                        {localize('@sage/xtrem-ui/select-component-no-results', 'No items found.')}
                    </div>
                </div>
            </div>
        </li>
    );
}

function Loading({ fullWidth }: { fullWidth: boolean }): React.ReactElement {
    return (
        <li key="LOADING" style={liStyles()}>
            <div className="e-ui-select-suggestion" data-testid="e-ui-select-suggestion">
                <div
                    className="e-ui-select-suggestion-value-wrapper"
                    data-testid="e-ui-select-suggestion-value-wrapper"
                >
                    <div
                        className="e-ui-select-suggestion-value"
                        data-testid="e-ui-select-suggestion-value"
                        style={{ ...(!fullWidth && { flex: 1, textAlign: 'left' }) }}
                    >
                        {localize('@sage/xtrem-ui/select-component-loading', 'Loading...')}
                    </div>
                </div>
            </div>
        </li>
    );
}

function TypeToSearch({ fullWidth }: { fullWidth: boolean }): React.ReactElement {
    return (
        <li key="EMPTY" style={liStyles()}>
            <div className="e-ui-select-suggestion" data-testid="e-ui-select-suggestion">
                <div
                    className="e-ui-select-suggestion-value-wrapper"
                    data-testid="e-ui-select-suggestion-value-wrapper"
                >
                    <div
                        className="e-ui-select-suggestion-value"
                        data-testid="e-ui-select-suggestion-value"
                        style={{ ...(!fullWidth && { flex: 1, textAlign: 'left' }) }}
                    >
                        {localize('@sage/xtrem-ui/select-component-type-to-search', 'Type to search...')}
                    </div>
                </div>
            </div>
        </li>
    );
}

function TypeMoreToSearch({
    missingCharacters,
    fullWidth,
}: {
    missingCharacters: number;
    fullWidth: boolean;
}): React.ReactElement {
    const emptyMessage =
        missingCharacters === 1
            ? localize('@sage/xtrem-ui/select-component-type-one-more-character', 'Type 1 more character to search')
            : localize('@sage/xtrem-ui/select-component-type-more-characters', 'Type {{0}} more characters to search', [
                  missingCharacters,
              ]);

    return (
        <div className="e-ui-select-suggestion" data-testid="e-ui-select-suggestion">
            <div className="e-ui-select-suggestion-value-wrapper" data-testid="e-ui-select-suggestion-value-wrapper">
                <div
                    className="e-ui-select-suggestion-value"
                    data-testid="e-ui-select-suggestion-value"
                    style={{ ...(!fullWidth && { flex: 1, textAlign: 'left' }) }}
                >
                    <li key="EMPTY" style={liStyles()}>
                        {emptyMessage}
                    </li>
                </div>
            </div>
        </div>
    );
}

export function SelectDropDown({
    addSelectedItem,
    node,
    createTunnelLinkText,
    focusLookupButton,
    fullWidth = false,
    getItemProps = (): any => {},
    hasHelperTextField = false,
    hasHighlightMatchText,
    hasLookupIcon,
    highlightedIndex,
    inputRef,
    highlightFirstListItem,
    highlightLastListItem,
    linkElement,
    inputValue,
    isItemSelected,
    isLinkCreateNewText,
    isMultiSelect = false,
    isOpen,
    items,
    loading,
    lookupLinkRef,
    createTunnelLinkRef,
    maxHeight = 180,
    menuProps = noop,
    minLookupCharacters = 3,
    onCreateNewItemLinkClick,
    onLookupIconClick,
    removeSelectedItem,
    screenId,
    shouldRenderOptionsAbove,
    ulRef: externalUlRef,
    variant = 'carbon',
    inputId,
}: SelectDropDownProps): React.ReactElement {
    const [listHeight, setListHeight] = React.useState(0);

    const ulRef = externalUlRef ?? React.createRef<HTMLUListElement>();

    // Optimized mobile detection and maxHeight calculation
    const finalMaxHeight = useMemo(() => {
        const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;

        if (!isMobile) {
            return maxHeight; // Desktop: use original maxHeight
        }

        // For small screens like ADC we need to recalculate the maxHeight based on the viewport height so that the dropdown is not cut off by the footer. https://jira.sage.com/browse/X3-338340
        const viewportHeight = typeof window !== 'undefined' ? window.innerHeight : 600;
        const safeMaxHeight = Math.min(viewportHeight * 0.4, 200); // 40% of viewport or 200px max

        return Math.max(safeMaxHeight, 100); // Minimum 100px for usability
    }, [maxHeight]); // Only recalculate if maxHeight prop changes

    React.useEffect(() => {
        let resizeObserverEntries: ResizeObserverEntry[] = [];

        const observer = new ResizeObserver(entries => {
            resizeObserverEntries = entries;
            setListHeight(entries[0].contentRect.height);
        });

        if (ulRef.current) {
            observer.observe(ulRef.current);
        }

        return (): void => {
            resizeObserverEntries.forEach(entry => entry.target.remove());
            observer.disconnect();
        };
    }, [ulRef]);

    const renderLinks = (): React.ReactNode => {
        return (
            <>
                {hasLookupLinkLine && (
                    <div
                        className={`e-ui-select-dropdown-lookup-link${
                            !hasCreateNewItemLink ? ' e-ui-select-dropdown-lookup-link-with-radius' : ''
                        }`}
                        style={
                            shouldRenderOptionsAbove
                                ? { bottom: hasCreateNewItemLink ? '73px' : '40px' }
                                : { top: listHeight }
                        }
                    >
                        {linkElement || (
                            <Link onClick={onLookupIconClick} ref={lookupLinkRef} onKeyDown={onLinkKeyDown}>
                                {localize('@sage/xtrem-ui/reference-open-lookup-link', 'View the list')}
                            </Link>
                        )}
                    </div>
                )}
                {hasCreateNewItemLink && (
                    <SelectCreateNewItem
                        ref={createTunnelLinkRef}
                        createTunnelLinkText={createTunnelLinkText}
                        onLinkKeyDown={onLinkKeyDown}
                        onCreateNewItemLinkClick={onInternalCreateNewLinkClick}
                        screenId={screenId}
                        node={node}
                        position={
                            shouldRenderOptionsAbove
                                ? { bottom: '40px' }
                                : {
                                      top: hasLookupLinkLine ? `${listHeight + 33}px` : `${listHeight}px`,
                                  }
                        }
                    />
                )}
            </>
        );
    };

    const renderList = (): React.ReactNode => {
        if (loading) {
            return <Loading fullWidth={fullWidth} />;
        }

        if (!inputValue && minLookupCharacters > 0 && items.length === 0 && !linkElement) {
            return <TypeToSearch fullWidth={fullWidth} />;
        }

        const missingCharacters = minLookupCharacters - (inputValue?.length ?? 0);
        if (missingCharacters > 0 && items.length === 0 && !linkElement) {
            return <TypeMoreToSearch missingCharacters={missingCharacters} fullWidth={fullWidth} />;
        }

        if (isOpen && (items?.length > 0 || linkElement)) {
            const list = items.map((item, index) => {
                const isSelected = isItemSelected ? isItemSelected({ id: item.id, value: String(item.value) }) : false;
                const itemProps = getItemProps({
                    index,
                    item,
                });
                return (
                    <SelectDropDownItem
                        addSelectedItem={addSelectedItem}
                        fullWidth={fullWidth}
                        isMultiSelect={isMultiSelect}
                        isSelected={isSelected}
                        item={item}
                        hasHighlightMatchText={hasHighlightMatchText}
                        itemProps={itemProps}
                        key={item.id}
                        removeSelectedItem={removeSelectedItem}
                        searchText={inputValue}
                        style={liStyles({
                            isActive: highlightedIndex === index,
                            isSelected,
                        })}
                        variant={variant}
                    />
                );
            });
            if (list.length > 0) {
                return list;
            }
            if (linkElement) {
                return null;
            }
            return <NoItemsFound fullWidth={fullWidth} />;
        }

        return <NoItemsFound fullWidth={fullWidth} />;
    };

    const onLinkKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>): void => {
        const linkRefs = {
            lookup: lookupLinkRef,
            createTunnel: createTunnelLinkRef,
        };

        const keys = objectKeys(linkRefs);
        const currentKey = keys.find(key => linkRefs[key]?.current === e.target);

        if (currentKey) {
            const currentIndex = keys.indexOf(currentKey);
            const nextRef = linkRefs[keys[currentIndex + 1]];
            const prevRef = linkRefs[keys[currentIndex - 1]];

            switch (e.key) {
                case 'Enter':
                    if (currentKey === 'lookup' && onLookupIconClick) {
                        onLookupIconClick(e);
                    } else if (currentKey === 'createTunnel') {
                        onInternalCreateNewLinkClick(e);
                    }
                    break;
                case 'ArrowDown':
                    if (nextRef?.current) {
                        nextRef.current.focus();
                    } else {
                        highlightFirstListItem();
                    }
                    break;
                case 'ArrowUp':
                    if (prevRef?.current) {
                        prevRef.current.focus();
                    } else {
                        highlightLastListItem();
                    }
                    break;
                case 'Tab':
                    focusLookupButton();
                    break;
                default:
                    break;
            }
        }

        e.preventDefault();
        e.stopPropagation();
    };

    const onInternalCreateNewLinkClick = React.useCallback<NonNullable<React.ComponentProps<typeof Link>['onClick']>>(
        ev => {
            if (ev.ctrlKey || ev.metaKey) {
                return;
            }
            ev.preventDefault();
            onCreateNewItemLinkClick?.();
        },
        [onCreateNewItemLinkClick],
    );

    const hasLookupLinkLine = hasLookupIcon && isOpen && !loading && (!!items?.length || !!linkElement);
    const hasCreateNewItemLink = isLinkCreateNewText && isOpen && !loading;

    return (
        <div
            {...menuProps}
            className="e-ui-select-dropdown"
            style={ulDivStyles({ hasHelperTextField, inputRef, variant })}
            aria-busy={!isOpen || loading ? true : undefined}
            role={undefined} // XT-70728 - Prevents accessibility violation
        >
            <ul
                aria-labelledby={inputId}
                data-testid="e-ui-select-dropdown"
                ref={ulRef}
                role={menuProps.role || 'listbox'}
                style={ulStyles({
                    shouldRenderOptionsAbove,
                    hasLookupLinkLine,
                    maxHeight: finalMaxHeight,
                    hasCreateNewItemLink,
                })}
            >
                {!isOpen ? null : renderList()}
            </ul>
            {renderLinks()}
        </div>
    );
}
