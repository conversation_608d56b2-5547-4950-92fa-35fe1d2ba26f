import * as React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react';
import type { SelectProps, SelectItem } from '../select-component';
import { Select } from '../select-component';
import { userEvent } from '../../../../__tests__/test-helpers';

jest.useFakeTimers();

describe('UI - Select component', () => {
    it('Should call the onChange event with organic flag after the user press enter keyDown', async () => {
        const mockedOnChange = jest.fn();
        const selectProps: SelectProps = {
            elementId: 'testId',
            testId: 'myTestId',
            getItems: () => {
                const selectItems: SelectItem[] = [
                    {
                        id: '1',
                        value: 'Amazon',
                    },
                    {
                        id: '2',
                        value: 'Ali Express',
                    },
                ];
                return Promise.resolve(selectItems);
            },
            screenId: 'screenId',
            onChange: mockedOnChange,
        };
        const selectComponent = render(<Select {...selectProps} />);
        const input = selectComponent.getByTestId('myTestId');
        fireEvent.focus(input);
        await userEvent.type(input, 'Amazon{Enter}');
        await waitFor(() => {
            expect(mockedOnChange).toHaveBeenCalledWith({ id: '1', value: 'Amazon' }, true);
        });
    });

    it('Should clean input value when close icon is clicked', async () => {
        const mockedOnChange = jest.fn();
        const selectInputProps: SelectProps = {
            elementId: 'testId',
            testId: 'myTestId',
            getItems: () => {
                const selectItems: SelectItem[] = [
                    {
                        id: '1',
                        value: 'Amazon',
                    },
                    {
                        id: '2',
                        value: 'Ali Express',
                    },
                ];
                return Promise.resolve(selectItems);
            },
            screenId: 'screenId',
            onChange: mockedOnChange,
        };
        const selectProps: SelectProps = {
            elementId: 'testId',
            testId: 'e-ui-select-close',
            getItems: () => {
                const selectItems: SelectItem[] = [
                    {
                        id: '1',
                        value: 'Amazon',
                    },
                    {
                        id: '2',
                        value: 'Ali Express',
                    },
                ];
                return Promise.resolve(selectItems);
            },
            screenId: 'screenId',
            onChange: mockedOnChange,
        };
        const selectComponent = render(<Select {...selectProps} />);
        const selectInputComponent = render(<Select {...selectInputProps} />);
        const input = selectInputComponent.getByTestId('myTestId') as HTMLInputElement;
        const closeButton = selectComponent.getByTestId('e-ui-select-close');
        fireEvent.focus(input);
        await userEvent.type(input, 'Amazon{Enter}');
        fireEvent.focus(closeButton);
        fireEvent.click(closeButton);

        expect(input.value).toBe('');
    });

    it('should call "onItemsFetched" with autoselected null when no match exists', async () => {
        const mockedOnChange = jest.fn();
        const mockedOnItemsFetched = jest.fn();
        const selectComponent = render(
            <Select
                elementId="testId"
                testId="testId"
                autoSelectPolicy="always"
                autoSelect={true}
                minLookupCharacters={1}
                getItems={() => {
                    return Promise.resolve([]);
                }}
                screenId="screenId"
                onChange={mockedOnChange}
                onItemsFetched={mockedOnItemsFetched}
            />,
        );
        const input = selectComponent.getByTestId('testId') as HTMLInputElement;
        fireEvent.focus(input);
        await userEvent.type(input, 'Amazon');
        await waitFor(() => {
            expect(mockedOnItemsFetched).toHaveBeenCalledTimes(1);
            expect(mockedOnItemsFetched).toHaveBeenNthCalledWith(1, { state: 'autoselect', item: null });
            expect(mockedOnChange).not.toHaveBeenCalled();
        });
    });

    it('should call "onItemsFetched" with autoselected value when only one match exists', async () => {
        const mockedOnChange = jest.fn();
        const mockedOnItemsFetched = jest.fn();
        const selectComponent = render(
            <Select
                elementId="testId"
                testId="testId"
                autoSelectPolicy="always"
                autoSelect={true}
                minLookupCharacters={1}
                getItems={() => {
                    return Promise.resolve([
                        {
                            id: '1',
                            value: 'Amazon',
                            __collectionItem: {
                                id: '1',
                                value: 'Amazon',
                            },
                        },
                        {
                            id: '2',
                            value: 'Ali Express',
                            __collectionItem: {
                                id: '2',
                                value: 'Ali Express',
                            },
                        },
                    ]);
                }}
                screenId="screenId"
                onChange={mockedOnChange}
                onItemsFetched={mockedOnItemsFetched}
            />,
        );
        const input = selectComponent.getByTestId('testId') as HTMLInputElement;
        fireEvent.focus(input);
        await userEvent.type(input, 'Amazon');
        await waitFor(() => {
            expect(mockedOnItemsFetched).toHaveBeenCalledTimes(1);
            expect(mockedOnItemsFetched).toHaveBeenNthCalledWith(1, {
                state: 'autoselect',
                item: { id: '1', value: 'Amazon' },
            });
            expect(mockedOnChange).toHaveBeenCalledTimes(1);
            expect(mockedOnChange).toHaveBeenNthCalledWith(1, { id: '1', value: 'Amazon' }, true);
        });
    });

    it('should call "onItemsFetched" with success when there are matches', async () => {
        const mockedOnChange = jest.fn();
        const mockedOnItemsFetched = jest.fn();
        const selectComponent = render(
            <Select
                elementId="testId"
                testId="testId"
                autoSelectPolicy="always"
                autoSelect={true}
                minLookupCharacters={1}
                getItems={() => {
                    return Promise.resolve([
                        {
                            id: '1',
                            value: 'Amazon',
                            __collectionItem: {
                                id: '1',
                                value: 'Amazon',
                            },
                        },
                        {
                            id: '2',
                            value: 'Ali Express',
                            __collectionItem: {
                                id: '2',
                                value: 'Ali Express',
                            },
                        },
                    ]);
                }}
                screenId="screenId"
                onChange={mockedOnChange}
                onItemsFetched={mockedOnItemsFetched}
            />,
        );
        const input = selectComponent.getByTestId('testId') as HTMLInputElement;
        fireEvent.focus(input);
        await userEvent.type(input, 'A');
        await waitFor(() => {
            expect(mockedOnItemsFetched).toHaveBeenCalledTimes(1);
            expect(mockedOnItemsFetched).toHaveBeenNthCalledWith(1, {
                state: 'success',
                items: [
                    {
                        id: '1',
                        value: 'Amazon',
                        __collectionItem: {
                            id: '1',
                            value: 'Amazon',
                        },
                    },
                    {
                        id: '2',
                        value: 'Ali Express',
                        __collectionItem: {
                            id: '2',
                            value: 'Ali Express',
                        },
                    },
                ],
            });
            expect(mockedOnChange).not.toHaveBeenCalled();
        });
    });

    it('should call "onItemsFetched" and "onChange" with null/undefined when no match exists - preselected item', async () => {
        const mockedOnChange = jest.fn();
        const mockedOnItemsFetched = jest.fn();
        const selectComponent = render(
            <Select
                elementId="testId"
                testId="testId"
                autoSelectPolicy="always"
                autoSelect={true}
                minLookupCharacters={1}
                getItems={() => {
                    return Promise.resolve([]);
                }}
                screenId="screenId"
                onChange={mockedOnChange}
                onItemsFetched={mockedOnItemsFetched}
                selectedItem={{ id: '1', value: 'Amazon' }}
            />,
        );
        const input = selectComponent.getByTestId('testId') as HTMLInputElement;
        fireEvent.focus(input);
        await userEvent.type(input, 'A');
        await waitFor(() => {
            expect(mockedOnItemsFetched).toHaveBeenCalledTimes(1);
            expect(mockedOnItemsFetched).toHaveBeenNthCalledWith(1, { state: 'autoselect', item: null });
            expect(mockedOnChange).toHaveBeenCalledTimes(1);
            expect(mockedOnChange).toHaveBeenNthCalledWith(1, undefined, true);
        });
    });
});
