import * as React from 'react';
import { getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import { fireEvent, render } from '@testing-library/react';
import type { SelectCreateNewItemProps } from '../select-create-new-item';
import { SelectCreateNewItem } from '../select-create-new-item';
import type * as xtremRedux from '../../../../redux';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { PageDefinition } from '../../../../service/page-definition';

describe('select create new item', () => {
    const screenId = 'TestPage';
    const nodeName = '@sage/xtrem-test/TestNode';
    let props: SelectCreateNewItemProps;
    let mockState: xtremRedux.XtremAppState;
    let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

    beforeEach(() => {
        mockState = getMockState();
        mockState.nodeTypes = {
            TestNode: {
                name: 'TestNode',
                title: 'TestNode',
                packageName: '@sage/xtrem-test',
                properties: {},
                mutations: {},
            },
        };
        mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        props = {
            screenId,
            node: nodeName,
            onCreateNewItemLinkClick: jest.fn(),
        };

        mockStore = getMockStore(mockState);
    });

    it('should not render if the user does not have access to the $create right on the bound node', () => {
        const { container } = render(
            <Provider store={mockStore}>
                <SelectCreateNewItem {...props} />
            </Provider>,
        );

        expect(container.childElementCount).toEqual(0);
    });

    it('should not render if the create right is not "authorized"', () => {
        (mockState.screenDefinitions[screenId] as PageDefinition).accessBindings = {
            TestNode: { $create: 'inactive' },
        };
        const { container: inactiveContainer } = render(
            <Provider store={getMockStore(mockState)}>
                <SelectCreateNewItem {...props} />
            </Provider>,
        );
        expect(inactiveContainer.childElementCount).toEqual(0);

        (mockState.screenDefinitions[screenId] as PageDefinition).accessBindings = {
            TestNode: { $create: 'readonly' },
        };
        const { container: readonlyContainer } = render(
            <Provider store={getMockStore(mockState)}>
                <SelectCreateNewItem {...props} />
            </Provider>,
        );
        expect(readonlyContainer.childElementCount).toEqual(0);

        (mockState.screenDefinitions[screenId] as PageDefinition).accessBindings = {
            TestNode: { $create: 'unauthorized' },
        };
        const { container: unauthorizedContainer } = render(
            <Provider store={getMockStore(mockState)}>
                <SelectCreateNewItem {...props} />
            </Provider>,
        );
        expect(unauthorizedContainer.childElementCount).toEqual(0);

        (mockState.screenDefinitions[screenId] as PageDefinition).accessBindings = {};
        const { container: missingRightsContainer } = render(
            <Provider store={getMockStore(mockState)}>
                <SelectCreateNewItem {...props} />
            </Provider>,
        );
        expect(missingRightsContainer.childElementCount).toEqual(0);
    });

    it('should render if the create right is "authorized"', () => {
        (mockState.screenDefinitions[screenId] as PageDefinition).accessBindings = {
            TestNode: { $create: 'authorized' },
        };
        const { container, queryByTestId } = render(
            <Provider store={getMockStore(mockState)}>
                <SelectCreateNewItem {...props} />
            </Provider>,
        );
        expect(container.childElementCount).toEqual(1);
        expect(queryByTestId('e-ui-select-dropdown-create-new-item-link')).not.toBeNull();
    });

    it('should trigger onCreateNewItemLinkClick when the link is clicked', () => {
        (mockState.screenDefinitions[screenId] as PageDefinition).accessBindings = {
            TestNode: { $create: 'authorized' },
        };
        const { queryByText } = render(
            <Provider store={getMockStore(mockState)}>
                <SelectCreateNewItem {...props} />
            </Provider>,
        );
        expect(props.onCreateNewItemLinkClick).not.toHaveBeenCalled();
        fireEvent.click(queryByText('Create new item')!);
        expect(props.onCreateNewItemLinkClick).toHaveBeenCalled();
    });

    it('should render with custom label if createTunnelLinkText is set', () => {
        (mockState.screenDefinitions[screenId] as PageDefinition).accessBindings = {
            TestNode: { $create: 'authorized' },
        };
        props.createTunnelLinkText = 'MY CUSTOM LABEL';
        const { queryByText } = render(
            <Provider store={getMockStore(mockState)}>
                <SelectCreateNewItem {...props} />
            </Provider>,
        );
        expect(props.onCreateNewItemLinkClick).not.toHaveBeenCalled();
        fireEvent.click(queryByText('MY CUSTOM LABEL')!);
        expect(props.onCreateNewItemLinkClick).toHaveBeenCalled();
    });
});
