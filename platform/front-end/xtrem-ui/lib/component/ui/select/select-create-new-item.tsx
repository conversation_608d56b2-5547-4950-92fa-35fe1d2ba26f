import React from 'react';
import Link from 'carbon-react/esm/components/link';
import type * as xtremRedux from '../../../redux';
import { localize } from '../../../service/i18n-service';
import type { NodePropertyType } from '../../../types';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import type { AccessStatus } from '@sage/xtrem-shared';
import { getElementAccessStatus } from '../../../utils/access-utils';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

export interface SelectCreateNewItemProps {
    node?: NodePropertyType;
    createTunnelLinkText?: string;
    onLinkKeyDown?: (e: React.KeyboardEvent<HTMLButtonElement>) => void;
    onCreateNewItemLinkClick?: NonNullable<React.ComponentProps<typeof Link>['onClick']>;
    screenId?: string;
    position?: { top?: string; bottom?: string };
}

export const SelectCreateNewItem = React.forwardRef<HTMLButtonElement | HTMLAnchorElement, SelectCreateNewItemProps>(
    (
        {
            node,
            createTunnelLinkText,
            onLinkKeyDown,
            onCreateNewItemLinkClick,
            screenId,
            position,
        }: SelectCreateNewItemProps,
        ref,
    ): React.ReactElement | null => {
        const createRight = useDeepEqualSelector<xtremRedux.XtremAppState, AccessStatus | null>(state => {
            if (!node || !screenId) {
                return null;
            }
            const pageDefinition = getPageDefinitionFromState(screenId, state);
            if (!pageDefinition) {
                return null;
            }

            return (
                getElementAccessStatus({
                    accessBindings: pageDefinition.accessBindings,
                    bind: '$create',
                    elementProperties: {},
                    contextNode: node,
                    nodeTypes: state.nodeTypes,
                    dataTypes: state.dataTypes,
                }) || null
            );
        });

        if (createRight !== 'authorized') {
            return null;
        }

        const style = {
            ...(position && position.top && { top: position.top }),
            ...(position && position.bottom && { bottom: position.bottom }),
        };

        return (
            <div
                className="e-ui-select-dropdown-create-new-item-link"
                data-testid="e-ui-select-dropdown-create-new-item-link"
                style={style}
            >
                <Link icon="add" onClick={onCreateNewItemLinkClick} ref={ref} onKeyDown={onLinkKeyDown}>
                    {createTunnelLinkText ||
                        localize('@sage/xtrem-ui/reference-create-new-item-link', 'Create new item')}
                </Link>
            </div>
        );
    },
);

SelectCreateNewItem.displayName = 'SelectCreateNewItem';
