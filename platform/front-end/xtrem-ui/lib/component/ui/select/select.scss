@import "../../../render/style/mixins.scss";
/* Rendered into a react portal */

.e-select-field {
    span>span[data-component="icon"]:not([role="tooltip"]) {
        padding-top: 3px;
        padding-bottom: 3px;
        vertical-align: middle;
        line-height: 100%;
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        cursor: pointer;
        margin-right: 8px;
        order: 1;
    }
}

.e-ui-select-suggestion {
    display: flex;
    align-items: center;

    // Ugly carbon hack
    &>div[data-component="checkbox"] {
        padding-right: 0.75rem;
        margin-bottom: 0;

        &>div>div {
            margin-bottom: 0;
        }
    }

    .e-ui-select-suggestion-image {
        min-width: 32px;
        padding-right: 0.25rem;
    }

    .e-ui-select-suggestion-value-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        overflow: hidden;
        font-family: var(--fontFamiliesDefault);
        height: 32px;
        gap: 4px;

        .e-ui-select-suggestion-helper {
            text-align: right;
            font-size: var(--fontSizes025);
            max-width: 50%;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .e-ui-select-suggestion-value {
            font-size: var(--fontSizes100);
            align-content: center;

            min-width: 0;

            & p {
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
}

.e-ui-select-highlight {
    font-weight: bold;
    text-decoration: underline;
}

[role="tooltip"] {
    order: 1;
}

.e-ui-select-dropdown {
    font-family: var(--fontFamiliesDefault);

    p {
        margin: 0;
    }

    ul {
        margin: 0;
        list-style: none;
        position: absolute;
        cursor: pointer;
        padding: 0;
        scroll-snap-type: y mandatory;
        //max-height: 180px;
        overflow: auto;
        overflow-y: auto;

        @include isFirefox {
            overflow-y: auto;
        }

        background-color: #fff;
        box-shadow: var(--boxShadow100);
        z-index: 98;
        width: 100%;
        display: flex;
        flex-direction: column;
        font-family: var(--fontFamiliesDefault);
    }

    .e-ui-select-dropdown-lookup-link {
        position: absolute;
        z-index: 99;
        background: var(--colorsYang100);
        box-shadow: var(--boxShadow100);
        width: 100%;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-top: var(--colorsUtilityMajor050) solid 1px;

        button {
            padding: 4px 0;
            width: 100%;
            font-size: var(--fontSizes025);

            &:focus {
                outline: 0;
            }
        }
    }

    .e-ui-select-dropdown-lookup-link-with-radius {
        border-bottom-right-radius: var(--borderRadius100);
        border-bottom-left-radius: var(--borderRadius100);
    }

    .e-ui-select-dropdown-create-new-item-link {
        position: absolute;
        z-index: 99;
        background: var(--colorsYang100);
        box-shadow: var(--boxShadow100);
        width: 100%;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-top: var(--colorsUtilityMajor050) solid 1px;
        border-bottom-right-radius: var(--borderRadius100);
        border-bottom-left-radius: var(--borderRadius100);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;

        >span {
            display: block;
            width: 100%;
            max-width: unset;

            >button {
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        button {
            font-size: var(--fontSizes025);
        }
    }
}

.e-ui-select-inline-image {
    padding-top: 3px;
    padding-bottom: 3px;
    padding-left: 4px;
    vertical-align: middle;
    line-height: 100%;
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
}

.e-ui-select-inline-image-plain {
    padding-left: 10px;
    vertical-align: middle;
    line-height: 100%;
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    width: 34px;
}

.e-ui-select-inline-dropdown {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    padding: 0 2px;
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    cursor: pointer;
}

.e-ui-select-input {
    flex: 1;
    border: none;
    outline-width: 0;
    padding-left: 10px;
    height: 2rem;
    border-radius: 4px;
    box-sizing: border-box;
}

.e-ui-select-input-container {
    display: flex;
    box-sizing: border-box;
    justify-content: space-around;
    align-items: center;
    background: var(--colorsYang100);
}

.e-ui-select-input-chevron {
    background: none;
    border: none;
    box-shadow: none;
    cursor: pointer;
    order: 3;
    transform: scale(1.2);
    padding-right: 5px;
    font-family: "agGridBalham", sans-serif;

    &:focus {
        outline: solid var(--colorsSemanticFocus500) 1px;
    }
}

.e-ui-select-input-chevron::before {
    box-sizing: border-box;
    outline: none;
    content: '\F132';
}

.e-ui-select-lookup-button-plain button {
    background-color: var(--colorsYang100);
    padding: 0;
    height: 32px;
    width: 32px;
    min-width: 32px;
    border-top: 1px solid var(--colorsUtilityMajor300);
    border-right: none;
    border-bottom: 1px solid var(--colorsUtilityMajor300);
    border-left: 1px solid var(--colorsUtilityMajor300);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: var(--borderRadius100);
    border-bottom-right-radius: var(--borderRadius100);

}

.e-ui-select-lookup-button button {
    padding: 0;
    width: 40px;
    height: 100%;
    min-height: unset !important;
    right: 0;
    top: 0;
    border: 1px solid var(--colorsUtilityMajor300);
    box-sizing: border-box;
    background: transparent;
    border-top-right-radius: calc(var(--borderRadius050) - 1px);
    border-bottom-right-radius: calc(var(--borderRadius050) - 1px);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top: 0;
    border-bottom: 0;
    border-right: 0;

    &[data-component-size="small"] {
        width: 30px;
    }

    &[data-component-size="medium"] {
        width: 40px;
    }

    &[data-component-size="large"] {
        width: 50px;
    }

    span[data-element="lookup"][data-component="icon"] {
        height: unset !important;

        border-top-left-radius: 0;
        border-bottom-left-radius: 0;

        &:before {
            line-height: 24px;
        }
    }

}

.e-ui-select-loading-plain {
    box-sizing: border-box;
    width: 26px;
    height: 26px;
    border: 1px solid var(--colorsUtilityMajor300);
    display: flex;
    justify-content: center;

    // another carbon hack, let's change carbon so it will not forward the class to the children
    >.e-ui-select-loading-plain {
        height: 16px;
        border: none;
        width: 6px;
        margin-top: 5px;
        margin-right: 0;
    }

    >div {
        height: 20px;
        border: none;
        width: 8px;
        position: relative;
        margin: auto 0;
    }
}

.e-ui-select-loading {
    box-sizing: border-box;
    height: 100%;
    width: 40px;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    justify-content: center;
    border-left: 1px solid var(--colorsUtilityMajor300);
    border-top: none;
    border-bottom: none;
    border-right: none;

    &[data-component-size="small"] {
        width: 30px;
    }

    &[data-component-size="medium"] {
        width: 40px;
    }

    &[data-component-size="large"] {
        width: 50px;
    }

    >div {
        height: 20px;
        border: none;
        width: 8px;
        position: relative;
        margin: auto 0;
    }
}

.e-ui-select-label {
    box-sizing: border-box;
    cursor: default;
    display: block;
    margin: auto 0;
    padding: 0.25rem;
    max-width: calc(min(100%, 200px));

    & [data-component="pill"] {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: block;
        max-width: 100%;
    }
}

.e-ui-select-input-wrapper.e-ui-select-search-override [role="presentation"][readonly] {
    border: 1px solid var(--colorsUtilityMajor300);
    background-color: var(--colorsUtilityYang100);
    box-sizing: border-box;
}

.e-dialog-content .e-dialog-body .e-ui-select-input-wrapper>div {
    margin-bottom: 0;
}

.e-ui-select-input-wrapper .e-ui-select-lookup-button {
    border-bottom: 0;
    border-top: 0;
    border-right: 0;
    border-top-right-radius: var(--borderRadius100);
    border-bottom-right-radius: var(--borderRadius100);
}

.e-ui-select-close {
    display: flex;
    padding: 0;
    margin-right: 8px;

    &.e-ui-select-close-add-icon {
        margin-right: 8px
    }

    [data-element="cross_circle"] {
        color: var(--colorsYin030);
    }
}

.e-ui-select-close-icon {
    display: flex;
}

.e-ui-select-input-left-children {
    display: flex;
    align-items: stretch;
    flex-wrap: wrap;
    overflow: hidden;
}

.e-ui-select-input-wrapper {
    [data-element="help"] {
        word-wrap: break-word;
    }
}