import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import Loader from 'carbon-react/esm/components/loader';
import Pill from 'carbon-react/esm/components/pill';
import type { DownshiftProps, UseMultipleSelectionGetSelectedItemPropsOptions } from 'downshift';
import { isNil, noop } from 'lodash';
import React from 'react';
import { ClearButton } from './clear-button';
import { localize } from '../../../service/i18n-service';
import { Icon } from '../icon/icon-component';
import type { PortraitSize } from '../portrait-component';
import { Portrait } from '../portrait-component';
import type { InputProps, SelectItem } from './select-component';
import Textbox from 'carbon-react/esm/components/textbox';
import type { StyledPillProps } from 'carbon-react/esm/components/pill/pill.style';
import type { SizeOptions } from 'carbon-react/esm/components/button/button.component';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import Link from 'carbon-react/esm/components/link';
import { SELECT_EMPTY_VALUE } from '../../field/select/select-utils';
import { TAB } from '../../../utils/keyboard-event-utils';
import { TUNNEL_LINK_CLASS } from '../../../utils/constants';

interface SelectInputProps extends DownshiftProps<SelectItem> {
    borderColor?: string;
    closeIconClassName?: string;
    closeMenu: () => void;
    disabled?: boolean;
    disablePills?: boolean;
    displayNew?: boolean;
    error?: string;
    getSelectedItemProps?: (args: UseMultipleSelectionGetSelectedItemPropsOptions<SelectItem>) => any;
    handleBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
    handleSelectedItemRemove?: (selectedItem: SelectItem) => void;
    hasInputSearch?: boolean;
    hasLookupIcon?: boolean;
    helperText?: string;
    helperTextLink?: string;
    icon?: IconType;
    image?: SelectItem['image'];
    info?: string;
    inputId?: string;
    inputProps: InputProps;
    isLinkHelperText?: boolean;
    isMultiSelect?: boolean;
    label?: string;
    loading?: boolean;
    lookupButtonRef?: React.RefObject<HTMLButtonElement>;
    lookupIconId?: string;
    onClearFieldButtonClick?: () => void;
    onHelperTextLinkClick?: () => void;
    onLookupIconClick?: (event: React.MouseEvent<HTMLAnchorElement | HTMLButtonElement, MouseEvent>) => void;
    selectedRecords?: SelectItem[];
    size?: SizeOptions;
    testId?: string;
    toggleButtonProps: any;
    variant?: 'plain' | 'carbon';
    warning?: string;
    width?: string;
    wrapperProps: any;
}

export const SelectInput = React.forwardRef<HTMLInputElement, SelectInputProps>(
    (
        {
            borderColor,
            closeIconClassName,
            closeMenu,
            disabled = false,
            disablePills = false,
            displayNew = false,
            error,
            getSelectedItemProps = (): null => null,
            handleBlur,
            handleSelectedItemRemove = noop,
            hasInputSearch = true,
            hasLookupIcon = false,
            helperText,
            helperTextLink,
            icon,
            image,
            info,
            inputId,
            inputProps,
            isLinkHelperText,
            isMultiSelect = false,
            label,
            loading = false,
            lookupButtonRef,
            lookupIconId,
            onClearFieldButtonClick,
            onHelperTextLinkClick,
            onLookupIconClick,
            selectedRecords = [],
            size = 'medium',
            testId,
            toggleButtonProps,
            variant = 'carbon',
            warning,
            width = '50px',
            wrapperProps = noop,
        },
        ref,
    ) => {
        const readOnly = inputProps.readOnly;

        const isTabPressed = React.useRef<boolean>(false);
        const isShiftTabPressed = React.useRef<boolean>(false);
        const keydownListener = React.useCallback((event: KeyboardEvent) => {
            const { key, code, shiftKey } = event;
            if (key === TAB || code === TAB) {
                isTabPressed.current = true;
                isShiftTabPressed.current = shiftKey;
            }
        }, []);
        const keyupListener = React.useCallback((event: KeyboardEvent) => {
            const { key, code, shiftKey } = event;
            if (key === TAB || code === TAB) {
                isTabPressed.current = false;
                isShiftTabPressed.current = shiftKey;
            }
        }, []);

        const onInternalHelperTextLinkClick = React.useCallback(
            (ev: React.MouseEvent<HTMLButtonElement>) => {
                if (ev.ctrlKey || ev.metaKey) {
                    return;
                }
                ev.preventDefault();
                onHelperTextLinkClick?.();
            },
            [onHelperTextLinkClick],
        );

        React.useEffect(() => {
            const lookupButtonRefValue = lookupButtonRef?.current;

            lookupButtonRefValue?.addEventListener('keydown', keydownListener);
            lookupButtonRefValue?.addEventListener('keyup', keyupListener);

            return (): void => {
                lookupButtonRefValue?.removeEventListener('keydown', keydownListener);
                lookupButtonRefValue?.removeEventListener('keyup', keyupListener);
            };
        }, [lookupButtonRef, keydownListener, keyupListener]);

        const onButtonBlur = (event: any): void => {
            if (!isNil(inputId) && event.relatedTarget?.id === inputId) {
                event.stopPropagation();
                return;
            }
            if (isTabPressed.current && !isShiftTabPressed.current) {
                handleBlur?.(event);
            }
        };

        const getPortraitSize = (): PortraitSize => {
            return variant !== 'carbon' || size === 'small' ? 'XS' : 'S';
        };

        const getButtonSize = (): SizeOptions => {
            return variant === 'carbon' ? 'medium' : 'small';
        };

        const getPillSize = (): StyledPillProps['size'] => {
            switch (size) {
                case 'large':
                    return 'L';
                case 'medium':
                    return 'M';
                case 'small':
                    return 'S';
                default:
                    return 'M';
            }
        };

        const leftChildren = (
            <div className="e-ui-select-input-left-children">
                {!isMultiSelect && image && inputProps.value && (
                    <div
                        className={
                            variant !== 'carbon' || size === 'small'
                                ? 'e-ui-select-inline-image-plain'
                                : 'e-ui-select-inline-image'
                        }
                    >
                        <Portrait image={image} placeholderValue={image.value ?? ''} size={getPortraitSize()} />
                    </div>
                )}
                {isMultiSelect &&
                    hasInputSearch &&
                    selectedRecords &&
                    selectedRecords.length > 0 &&
                    selectedRecords.map((selectedItem, index) => {
                        const onDelete =
                            disabled || readOnly ? undefined : (): void => handleSelectedItemRemove(selectedItem);
                        return (
                            <div
                                {...getSelectedItemProps({ selectedItem, index, tabIndex: -1 })}
                                className="e-ui-select-label"
                                data-testid={`e-ui-select-${selectedItem.value}-pill`}
                                key={`e-select-label-${selectedItem.id}`}
                            >
                                {disablePills ? (
                                    <p>{selectedItem.value}</p>
                                ) : (
                                    <Pill
                                        colorVariant="neutral"
                                        key={`${selectedItem.id}-label`}
                                        onDelete={onDelete}
                                        pillRole="status"
                                        size={getPillSize()}
                                    >
                                        {selectedItem.value}
                                    </Pill>
                                )}
                            </div>
                        );
                    })}
            </div>
        );

        const classNameSelectClose = icon ? 'e-ui-select-close e-ui-select-close-add-icon' : 'e-ui-select-close';
        const children = (
            <>
                {onClearFieldButtonClick && (
                    <ClearButton
                        hidden={!inputProps.value}
                        onClearFieldButtonClick={onClearFieldButtonClick}
                        closeIconClassName={closeIconClassName}
                        classNameSelectClose={classNameSelectClose}
                    />
                )}

                {!readOnly && !disabled && !loading && displayNew && (
                    <div style={{ fontWeight: 'bold', margin: 'auto', paddingRight: '1rem', order: 1 }}>
                        {`(${localize('@sage/xtrem-ui/new', 'New')})`}
                    </div>
                )}
                {!readOnly && !disabled && hasLookupIcon && onLookupIconClick && (
                    <div
                        className={
                            variant === 'carbon' ? 'e-ui-select-lookup-button' : 'e-ui-select-lookup-button-plain'
                        }
                    >
                        <ButtonMinor
                            onFocus={closeMenu}
                            buttonType="tertiary"
                            data-component-size={size}
                            data-testid="e-ui-select-lookup-button"
                            iconType={!loading ? 'lookup' : undefined}
                            id={lookupIconId}
                            ref={lookupButtonRef}
                            onBlur={onButtonBlur}
                            onClick={onLookupIconClick}
                            size={getButtonSize()}
                            arial-label={localize('@sage/xtrem-ui/open-lookup', 'Open lookup dialog')}
                            iconTooltipMessage={localize('@sage/xtrem-ui/open-lookup', 'Open lookup dialog')}
                        >
                            {loading && <Loader data-component-size={size} />}
                        </ButtonMinor>
                    </div>
                )}
                {!readOnly &&
                    !disabled &&
                    !hasLookupIcon &&
                    (variant === 'plain' ? (
                        <button
                            {...toggleButtonProps}
                            tabIndex={undefined}
                            onBlur={onButtonBlur}
                            onKeyDown={keydownListener}
                            onKeyUp={keyupListener}
                            type="button"
                            data-role="button"
                            className="e-ui-select-input-chevron"
                            data-testid={`${testId}-chevron`}
                        />
                    ) : (
                        <div {...toggleButtonProps} className="e-ui-select-inline-dropdown">
                            <Icon type="dropdown" bgSize="small" />
                        </div>
                    ))}
            </>
        );

        const renderInput = (): JSX.Element => {
            if (variant === 'plain') {
                return (
                    <div {...wrapperProps} className="e-ui-select-input-container">
                        {leftChildren}
                        <input
                            {...inputProps}
                            aria-controls={readOnly || disabled ? undefined : inputProps['aria-controls']}
                            aria-label={label}
                            className="e-ui-select-input"
                            data-testid={testId}
                            disabled={disabled}
                            id={inputId}
                            onBlur={handleBlur}
                            readOnly={readOnly || !hasInputSearch}
                            style={{ width }}
                        />
                        {!readOnly && !disabled && children}
                    </div>
                );
            }

            const getWrapperClassNames = (): string => {
                const classes = ['e-ui-select-input-wrapper'];

                if (wrapperProps.className) {
                    classes.push(wrapperProps.className);
                }

                if (!readOnly && !disabled && !hasInputSearch) {
                    classes.push('e-ui-select-search-override');
                }

                return classes.join(' ');
            };

            const getClassNames = (): string => {
                const classes = ['e-field-select-input-text'];

                if (error) {
                    classes.push('e-field-error-message-carbon');
                }

                return classes.join(' ');
            };

            const getDisplayValue = (): string => {
                return inputProps.value === SELECT_EMPTY_VALUE ? ' ' : String(inputProps.value || '');
            };

            const helperTextNode: React.ReactNode = isLinkHelperText ? (
                <Link onClick={onInternalHelperTextLinkClick} href={helperTextLink} className={TUNNEL_LINK_CLASS}>
                    {helperText || localize('@sage/xtrem-ui/tunnel-link-see-more', 'See details')}
                </Link>
            ) : (
                helperText
            );

            return (
                <div {...wrapperProps} className={getWrapperClassNames()}>
                    <Textbox
                        {...inputProps}
                        m={0}
                        aria-controls={readOnly || disabled ? undefined : inputProps['aria-controls']}
                        aria-label={label}
                        aria-labelledby={undefined}
                        className={getClassNames()}
                        data-testid={testId}
                        disabled={disabled}
                        error={disabled ? undefined : error}
                        warning={disabled ? undefined : warning}
                        info={disabled ? undefined : info}
                        fieldHelp={helperTextNode}
                        id={inputId}
                        inputIcon={icon}
                        ref={(input: HTMLInputElement | null): void => {
                            if (typeof inputProps.ref === 'function') {
                                if (input) {
                                    inputProps.ref(input);
                                    if (typeof ref === 'function' && input) {
                                        ref(input);
                                    }
                                }
                            }
                            if (input && !hasInputSearch) {
                                /**
                                 * Carbon removes the placeholder whenever the input is readonly.
                                 * This will always be the case for a multi-dropdown though
                                 * so we just restore the original placeholder for this usecase.
                                 */
                                input.placeholder = inputProps.placeholder || '';

                                // Prevent input text from being selected as this assumes the input is
                                // editable.
                                const end = input.value.length;
                                input.setSelectionRange(end, end);
                            }

                            const wrapper = input?.closest('[role="presentation"]') as HTMLDivElement | null;
                            if (!wrapper) {
                                return;
                            }

                            if (input) {
                                input.style.textOverflow = 'ellipsis';
                                input.style.minWidth = '0';
                            }

                            if (hasLookupIcon) {
                                const lookup = wrapper.getElementsByClassName(
                                    'e-ui-select-lookup-button',
                                )[0] as HTMLDivElement | null;
                                const closeIcon = wrapper.getElementsByClassName(
                                    'e-ui-select-close',
                                )[0] as HTMLDivElement | null;
                                if (input) {
                                    input.style.overflow = 'hidden';
                                    input.style.flex = '1 1';
                                }
                                if (lookup) {
                                    lookup.style.flex = size === 'small' ? '0 0 30px' : '0 0 40px';
                                }
                                if (closeIcon) {
                                    closeIcon.style.flex = '0 0 24px';
                                }
                                wrapper.style.paddingRight = '0';
                            } else {
                                wrapper.style.paddingRight = '20px';
                            }

                            wrapper.style.flexWrap = 'nowrap';

                            if (borderColor) {
                                wrapper.style.borderColor = borderColor;
                            }
                        }}
                        readOnly={!hasInputSearch || readOnly}
                        label={label}
                        leftChildren={leftChildren}
                        onBlur={handleBlur}
                        size={size}
                        validationOnLabel={true}
                        value={getDisplayValue()}
                    >
                        {children}
                    </Textbox>
                </div>
            );
        };
        return renderInput();
    },
);

SelectInput.displayName = 'SelectInput';
