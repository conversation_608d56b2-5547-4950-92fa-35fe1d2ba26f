import React from 'react';
import styled from 'styled-components';
import { localize } from '../../../service/i18n-service';
import { Icon } from '../icon/icon-component';
import IconButton from 'carbon-react/esm/components/icon-button';
import type { IconButtonProps } from 'carbon-react/esm/components/icon-button';

interface StyledIconButtonProps extends IconButtonProps {
    hidden: boolean;
}
interface ClearButtonProps {
    hidden: boolean;
    classNameSelectClose: string;
    closeIconClassName?: string;
    onClearFieldButtonClick: () => void;
}

const StyledIconButton = styled(IconButton).attrs((props: StyledIconButtonProps) => {
    return {
        tabIndex: props.hidden ? '-1' : '0',
    };
})`
    ${(props: StyledIconButtonProps): string => (props.hidden ? 'visibility: hidden;' : 'visibility: visible;')}
`;

export const ClearButton: React.FC<ClearButtonProps> = React.memo(props => {
    const { hidden } = props;
    return (
        <div className={props.classNameSelectClose}>
            <span className={props.closeIconClassName || ''}>
                <StyledIconButton
                    hidden={hidden}
                    data-testid="e-ui-select-close"
                    onClick={(): void => props.onClearFieldButtonClick()}
                    aria-label={localize('@sage/xtrem-ui/clear-input-text', 'Clear')}
                >
                    <Icon type="cross_circle" />
                </StyledIconButton>
            </span>
        </div>
    );
});
