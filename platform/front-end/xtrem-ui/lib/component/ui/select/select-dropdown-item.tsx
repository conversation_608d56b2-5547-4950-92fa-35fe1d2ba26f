import { Checkbox as CarbonCheckbox } from 'carbon-react/esm/components/checkbox';
import { escape as lodashEscape, noop } from 'lodash';
import React from 'react';
import uid from 'uid';
import { Portrait } from '../portrait-component';
import type { SelectItem } from './select-component';

interface SelectDropDownItemProps {
    addSelectedItem?: (item: SelectItem) => void;
    fullWidth?: boolean;
    hasHighlightMatchText: boolean;
    isMultiSelect?: boolean;
    isSelected: boolean;
    item: SelectItem;
    itemProps?: object;
    searchText?: string;
    removeSelectedItem?: (item: SelectItem) => void;
    style?: React.CSSProperties;
    variant?: 'plain' | 'carbon';
}

interface CheckboxProps {
    checked: boolean;
    name: string;
    onChange: (checked: boolean) => void;
    onClick?: (event: React.MouseEvent<HTMLInputElement>) => void;
    variant?: 'plain' | 'carbon';
}

/**
 * The whole purpose of this wrapper component is to set the input's tabIndex="-1".
 * If that's not the case the element will be focusable and the select's focus will misbehave upon tabbing through the various fields.
 */
function CarbonCheckboxWrapper({
    name,
    checked,
    onChange,
}: Pick<CheckboxProps, 'name' | 'checked' | 'onChange'>): React.ReactElement {
    const id = React.useMemo(() => uid(16), []);

    React.useEffect(() => {
        const checkboxInput = document.getElementById(id);
        if (checkboxInput) {
            checkboxInput.tabIndex = -1;
        }
    }, [id]);

    return (
        <CarbonCheckbox
            id={id}
            name={name}
            checked={checked}
            onChange={(e: React.ChangeEvent<HTMLInputElement>): void => onChange(e.target.checked)}
        />
    );
}

function Checkbox({ name, variant, checked, onChange, onClick = noop }: CheckboxProps): React.ReactElement {
    return variant === 'carbon' ? (
        <CarbonCheckboxWrapper name={name} checked={checked} onChange={onChange} />
    ) : (
        <input
            type="checkbox"
            name={name}
            checked={checked}
            onChange={(e: React.ChangeEvent<HTMLInputElement>): void => onChange(e.target.checked)}
            onClick={(e: React.MouseEvent<HTMLInputElement>): void => {
                e.stopPropagation();
                onClick(e);
            }}
            tabIndex={-1}
        />
    );
}

export function SelectDropDownItem({
    addSelectedItem,
    fullWidth = false,
    isMultiSelect = false,
    isSelected,
    hasHighlightMatchText,
    item,
    itemProps,
    searchText,
    removeSelectedItem,
    style = {},
    variant = 'carbon',
}: SelectDropDownItemProps): React.ReactElement {
    return (
        <li {...itemProps} style={style} data-pendoid={item.pendoId}>
            <div id={item.id} className="e-ui-select-suggestion" data-testid="e-ui-select-suggestion">
                {isMultiSelect && (
                    <Checkbox
                        name={String(item.value)}
                        checked={isSelected}
                        onChange={(): void => {
                            if (isSelected && removeSelectedItem) {
                                removeSelectedItem(item);
                            } else if (addSelectedItem) {
                                addSelectedItem(item);
                            }
                        }}
                        variant={variant}
                    />
                )}
                {item.image && (
                    <div className="e-ui-select-suggestion-image">
                        <Portrait
                            image={item.image}
                            placeholderValue={String(item.value)}
                            size={variant === 'carbon' ? 'S' : 'XS'}
                        />
                    </div>
                )}
                <div
                    className="e-ui-select-suggestion-value-wrapper"
                    data-testid="e-ui-select-suggestion-value-wrapper"
                >
                    <div
                        className="e-ui-select-suggestion-value"
                        data-testid="e-ui-select-suggestion-value"
                        style={{ ...(!fullWidth && { flex: 1, textAlign: 'left' }) }}
                    >
                        <p
                            dangerouslySetInnerHTML={{
                                __html: getHighlightMatchText(
                                    lodashEscape(item.displayedAs || String(item.value)),
                                    isSelected,
                                    hasHighlightMatchText,
                                    searchText,
                                ),
                            }}
                        />
                    </div>
                    {item.helperText && (
                        <div
                            className="e-ui-select-suggestion-helper"
                            data-testid="e-ui-select-suggestion-helper"
                            style={{ ...(!fullWidth && { textAlign: 'right' }), fontWeight: 'normal' }}
                        >
                            <span
                                dangerouslySetInnerHTML={{
                                    __html: getHighlightMatchText(
                                        lodashEscape(item.helperText),
                                        isSelected,
                                        hasHighlightMatchText,
                                        searchText,
                                    ),
                                }}
                            />
                        </div>
                    )}
                </div>
            </div>
        </li>
    );
}

function getHighlightMatchText(
    text: string,
    isSelected: boolean,
    hasHighlightMatchText: boolean,
    searchText = '',
): string {
    if (isSelected || !hasHighlightMatchText) {
        return text;
    }
    const regex = new RegExp(lodashEscape(searchText), 'gi');
    return text.replace(regex, (match: string): string => `<span class="e-ui-select-highlight">${match}</span>`);
}
