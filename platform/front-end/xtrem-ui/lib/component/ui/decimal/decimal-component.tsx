import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { DecimalProps, CustomEvent } from 'carbon-react/esm/components/decimal';
import CarbonDecimal from 'carbon-react/esm/components/decimal';
import React from 'react';
import { connect } from 'react-redux';
import type { CarbonStyleOverrides } from '../../types';
import type * as xtremRedux from '../../../redux';

interface Props {
    allowEmptyValue?: boolean;
    disabled: boolean;
    fieldHelp?: string;
    inputIcon?: IconType;
    onChange: (event: CustomEvent) => void;
    onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
    precision?: DecimalProps['precision'];
    styleOverride?: CarbonStyleOverrides;
    validations?: any; // Array<() => Promise<void>>;
    value?: string;
}

interface InternalProps extends Props {
    locale: string;
}

export interface DecimalValueObject {
    rawValue: string;
    formattedValue: string;
}

export const UnconnectedDecimal: React.FC<InternalProps> = React.forwardRef<HTMLInputElement, InternalProps>(
    ({ allowEmptyValue = true, fieldHelp = '', precision = 2, ...rest }, ref) => (
        <CarbonDecimal
            {...rest}
            ref={ref}
            allowEmptyValue={allowEmptyValue}
            data-testid="e-ui-decimal-input"
            fieldHelp={fieldHelp}
            precision={precision}
            inputIcon={rest.inputIcon}
            onKeyDown={rest.onKeyDown}
        />
    ),
);

UnconnectedDecimal.displayName = 'DecimalComponent';

export const Decimal = connect(
    (state: xtremRedux.XtremAppState, props: Props): InternalProps => {
        return { ...props, locale: state.applicationContext?.locale || 'en-US' };
    },
    null,
    null,
    { forwardRef: true },
)(UnconnectedDecimal);
