import type { NodePropertyType } from '../../../types';
import type { Dict, Property } from '@sage/xtrem-shared';
import type { NodeDetails } from '../../../service/node-information-service';

export interface NodeBrowserTreeProps {
    isDisabled?: boolean;
    isReadOnly?: boolean;
    checkedItems?: Dict<Property>;
    onCheckedItemsUpdated?: (checkedItems: Dict<Property>) => void;
    node?: NodePropertyType;
    filter?: (nodes: NodeDetails[]) => NodeDetails[];
    /** This is for platform use cases only. If you need to use this, please talk to the platform team first. */
    fetchItems?: (treeNode: Property) => Promise<Property[]>;
    locale: string;
}
