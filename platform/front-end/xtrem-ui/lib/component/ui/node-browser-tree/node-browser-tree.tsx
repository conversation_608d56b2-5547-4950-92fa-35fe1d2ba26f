import React from 'react';
import IconButton from 'carbon-react/esm/components/icon-button';
import Icon from 'carbon-react/esm/components/icon';
import { localize } from '../../../service/i18n-service';
import Textbox from 'carbon-react/esm/components/textbox';
import { fetchNodeDetails, mapNodeDetailsToTreeProperty } from '../../../service/node-information-service';
import { chain } from 'lodash';
import { getArtifactDescription } from '../../../utils/transformers';
import type { NodeBrowserTreeProps } from './node-browser-tree-type';
import { Tree } from '@sage/xtrem-ui-components';
import type { Property } from '@sage/xtrem-shared';
import { shouldIncludeDocumentEditorProperty } from '../../../utils/document-editor-utils';

export const NodeBrowserTree: React.FC<NodeBrowserTreeProps> = React.memo(props => {
    const {
        node,
        onCheckedItemsUpdated,
        filter,
        checkedItems,
        isDisabled,
        isReadOnly,
        fetchItems: externalFetchItems,
        locale,
    } = props;

    const rootElement = React.useMemo(
        (): Property | null =>
            node
                ? {
                      id: '',
                      labelPath: '',
                      key: '',
                      labelKey: '',
                      label: '',
                      canBeExpanded: true,
                      canBeSelected: false,
                      data: {
                          kind: 'OBJECT',
                          name: '',
                          label: '',
                          type: getArtifactDescription(node?.toString() || '').name as any,
                          canFilter: true,
                          canSort: true,
                          node: '',
                          enumType: null,
                          isCustom: false,
                          dataType: '',
                          targetNode: '',
                          isStored: false,
                          isOnInputType: false,
                          isOnOutputType: false,
                          isMutable: false,
                      },
                  }
                : null,
        [node],
    );

    const fetchItems = React.useCallback(
        async (treeNode: Property): Promise<Property[]> => {
            if (externalFetchItems) {
                return externalFetchItems(treeNode);
            }
            const allNodeInfo = await fetchNodeDetails({ nodeName: treeNode.data.type, locale });

            let filteredNodes = chain(allNodeInfo)
                .values()
                .filter(item => shouldIncludeDocumentEditorProperty(item.type as string));
            if (filter) {
                filteredNodes = filteredNodes.thru(filter);
            }

            return mapNodeDetailsToTreeProperty(treeNode, filteredNodes.value());
        },
        [externalFetchItems, filter, locale],
    );

    const [searchText, setSearchText] = React.useState<string>('');
    const inputRef = React.useRef<HTMLInputElement>(null);

    const [listOpenedItems, setListOpenedItems] = React.useState<{
        key: string;
        children: string[];
    }>({
        key: '',
        children: [],
    });
    const handleOpenChild = React.useCallback(
        (key: string, children: string[]) => {
            setListOpenedItems({ ...listOpenedItems, [key]: children });
        },
        [listOpenedItems],
    );
    const handleCloseChild = React.useCallback(
        (key: string) => {
            const updatedOpenedItems = { ...listOpenedItems } as any;
            delete updatedOpenedItems[key];
            setListOpenedItems(updatedOpenedItems);
        },
        [listOpenedItems],
    );
    const onClearButtonClicked = React.useCallback((): void => {
        setSearchText('');
        if (inputRef?.current) {
            inputRef.current.focus();
        }
    }, [setSearchText, inputRef]);

    const onSearchTextChanged = React.useCallback((ev: React.ChangeEvent<HTMLInputElement>) => {
        setSearchText(ev.target.value);
    }, []);

    return (
        rootElement && (
            <>
                <div data-testid="e-data-step-search">
                    <Textbox
                        data-testid="e-node-browser-filter"
                        ref={inputRef}
                        onKeyDown={(e: React.KeyboardEvent): void => {
                            // On hitting the enter key the component reloads the page for some reason
                            if (e.key === 'Enter') {
                                e.preventDefault();
                            }
                        }}
                        disabled={isDisabled}
                        onChange={onSearchTextChanged}
                        placeholder={localize('@sage/xtrem-ui/reference-lookup-dialog-search-placeholder', 'Search...')}
                        aria-label={localize('@sage/xtrem-ui/reference-lookup-dialog-search-placeholder', 'Search...')}
                        leftChildren={<Icon type="search" bgSize="large" />}
                        value={searchText}
                        mb={1}
                    >
                        {!!searchText && (
                            <IconButton
                                onClick={onClearButtonClicked}
                                aria-label={localize('@sage/xtrem-ui/clear-filter-text', 'Clear filter text')}
                            >
                                <Icon type="cross" />
                            </IconButton>
                        )}
                    </Textbox>
                </div>
                <div className="e-data-step-tree-container">
                    <div className="e-data-step-tree-wrapper">
                        {rootElement && (
                            <Tree
                                checkedItems={checkedItems || {}}
                                element={rootElement}
                                fetchItems={fetchItems}
                                isDisabled={isDisabled}
                                isReadOnly={isReadOnly}
                                level={0}
                                listOpenedItems={listOpenedItems}
                                localize={localize}
                                onCheckedItemsUpdated={onCheckedItemsUpdated}
                                onCloseChild={handleCloseChild}
                                onOpenChild={handleOpenChild}
                                searchText={searchText}
                            />
                        )}
                    </div>
                </div>
            </>
        )
    );
});

NodeBrowserTree.displayName = 'NodeBrowserTree';
