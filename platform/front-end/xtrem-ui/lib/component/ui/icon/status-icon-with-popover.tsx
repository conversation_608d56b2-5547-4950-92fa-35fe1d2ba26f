import CarbonIconButton from 'carbon-react/esm/components/icon-button';
import CarbonIcon from 'carbon-react/esm/components/icon';
import CarbonButton from 'carbon-react/esm/components/button';
import CarbonLink from 'carbon-react/esm/components/link';
import CarbonTooltip from 'carbon-react/esm/components/tooltip';
import type { ReactNode } from 'react';
import React from 'react';
import styled, { keyframes } from 'styled-components';
import { StyledToast, StyledToastContent } from 'carbon-react/esm/components/toast/toast.style';
import { TypeIconStyle as TypeIcon } from 'carbon-react/esm/components/message/message.style';
import { localize } from '../../../service/i18n-service';
import type { ValidationResult } from '../../../service/screen-base-definition';
import * as tokens from '@sage/design-tokens/js/base/common';
import type { CarbonLinkEvent, ValueOrCallbackWithFieldValue } from '../../../utils/types';
import type { ScreenBase } from '../../../service/screen-base';
import { resolveByValue } from '../../../utils/resolve-value-utils';

const fadeIn = keyframes`
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
`;

const TriggerButton = styled(CarbonIconButton)`
    &:focus {
        outline: 2px solid ${tokens.colorsSemanticFocus500};
    }
    margin-top: -2px;
    margin-left: 12px;
    margin-right: 4px;
`;

const CloseButton = styled(CarbonIconButton)`
    top: 24px !important;
    right: 12px !important;
`;

const LinkWithCaret = styled(CarbonLink)`
    & button {
        position: relative;
        color: black;
        text-decoration: none;
        padding-right: 6px;
        span {
            padding-left: 16px;
            text-decoration: underline;
            font-family: ${tokens.fontFamiliesDefault};
            font-weight: ${tokens.fontWeights500};
        }
    }
    & button::before {
        position: absolute;
        content: '\\E95F';
        font-family: 'CarbonIcons';
        top: 3px;
    }
`;

const Toast = styled(StyledToast)`
    animation: ${fadeIn} 0.2s linear;
    border: 1px solid var(--colorsSemanticNegative500);
    border-radius: 4px !important;
    max-width: 471px;
    padding-right: 8px;
    margin: 0 !important;
`;

const ToastContent = styled(StyledToastContent)`
    padding: 16px 16px 4px 24px;
`;

const StyledTypeIcon = styled(TypeIcon)`
    z-index: 6001;
`;

type StatusIconWithPopoverProps = {
    screenId: string;
    unFilterErrors?: Function;
    filterErrors?: Function;
    content: ReactNode;
    validationErrors: ValidationResult[];
    /** Indicate additional warning message, rendered as tooltip and blue border. */
    infoMessage?: ValueOrCallbackWithFieldValue<ScreenBase, string>;
    /** Indicate additional information, rendered as tooltip and orange border. */
    warningMessage?: ValueOrCallbackWithFieldValue<ScreenBase, string>;
};

type StatusIconWithPopoverState = {
    mode: Mode;
    isOpen: boolean;
};
type Mode = 'DEFAULT' | 'ERRORS';
class StatusIconWithPopover extends React.Component<StatusIconWithPopoverProps, StatusIconWithPopoverState> {
    private toolTipRef = React.createRef<HTMLDivElement>();

    private triggerButtonRef = React.createRef<HTMLButtonElement>();

    private closeButtonRef = React.createRef<HTMLButtonElement>();

    constructor(props: StatusIconWithPopoverProps) {
        super(props);
        this.state = { mode: 'DEFAULT', isOpen: false };
    }

    isVisible = (): boolean => {
        return false;
    };

    getDisplayErrorsButton = (): HTMLButtonElement | null | undefined => {
        return this.toolTipRef.current?.querySelector('button.e-button-filter-validation-errors');
    };

    close = (): void => {
        this.setState({ isOpen: false }, () => {
            this.triggerButtonRef.current?.focus();
        });
    };

    open = (ev?: CarbonLinkEvent): void => {
        ev?.preventDefault();
        this.setState({ isOpen: true }, () => {
            if (this.toolTipRef.current) {
                this.toolTipRef.current.style.padding = '0';
            }
            const errorButton = this.getDisplayErrorsButton();
            // We need this timeout because if you open the tooltip with the keyboard it will press the focus button too.
            setTimeout(() => {
                if (errorButton) {
                    errorButton.focus();
                } else if (this.closeButtonRef.current) {
                    this.closeButtonRef.current.focus();
                }
            }, 100);
        });
    };

    toggle = (): void => {
        if (!this.isVisible()) {
            this.open();
        } else {
            this.close();
        }
    };

    setDefaultMode = async (): Promise<void> => {
        await this.props.unFilterErrors?.();
        this.setState({ mode: 'DEFAULT' });
    };

    setErrorsMode = async (): Promise<void> => {
        await this.props.filterErrors?.();
        this.setState({ mode: 'ERRORS' });
        this.close();
    };

    getFilterButton = (): React.ReactNode => {
        if (this.props.filterErrors === undefined && this.props.unFilterErrors === undefined) {
            return null;
        }

        return this.state.mode === 'ERRORS' ? (
            <CarbonButton
                // className="e-button-filter-validation-errors"
                buttonType="secondary"
                size="small"
                onClick={this.setDefaultMode}
                // style={{ marginBottom: '1em' }}
            >
                {localize('@sage/xtrem-ui/display-errors-back-to-full-display', 'Back to full display')}
            </CarbonButton>
        ) : (
            <CarbonButton
                className="e-button-filter-validation-errors"
                buttonType="secondary"
                size="small"
                onClick={this.setErrorsMode}
                // style={{ marginBottom: '1em', marginTop: '1em' }}
            >
                {localize('@sage/xtrem-ui/display-errors-button', 'Display these errors')}
            </CarbonButton>
        );
    };

    render(): React.ReactNode {
        const numErrors = this.props.validationErrors?.length || 0;
        const warning =
            (!numErrors &&
                resolveByValue({
                    screenId: this.props.screenId,
                    fieldValue: null,
                    propertyValue: this.props.warningMessage,
                    rowValue: null,
                    skipHexFormat: true,
                })) ||
            undefined;

        const info =
            (!numErrors &&
                !warning &&
                resolveByValue({
                    screenId: this.props.screenId,
                    fieldValue: null,
                    propertyValue: this.props.infoMessage,
                    rowValue: null,
                    skipHexFormat: true,
                })) ||
            undefined;
        return (
            <>
                {(numErrors > 0 || info || warning) && (
                    <span>
                        {numErrors > 0 && (
                            <CarbonTooltip
                                ref={this.toolTipRef}
                                position="right"
                                fontColor={tokens.colorsUtilityYin090}
                                bgColor={tokens.colorsSemanticNegative500}
                                isVisible={this.state.isOpen}
                                message={
                                    <Toast variant="error">
                                        <StyledTypeIcon variant="error">
                                            <CarbonIcon type="error" />
                                        </StyledTypeIcon>
                                        <ToastContent>
                                            {this.props.content}
                                            {this.getFilterButton()}
                                        </ToastContent>
                                        <CloseButton
                                            ref={this.closeButtonRef}
                                            // @ts-expect-error className is a valid prop
                                            className="e-button-validation-errors-close"
                                            data-element="close"
                                            onClick={this.close}
                                        >
                                            <CarbonIcon type="close" />
                                        </CloseButton>
                                    </Toast>
                                }
                            >
                                <TriggerButton onClick={this.toggle} ref={this.triggerButtonRef}>
                                    <CarbonIcon
                                        key="validation-errors"
                                        className="e-icon-validation-errors"
                                        aria-label="validation-errors"
                                        fontSize="small"
                                        color="--colorsSemanticNegative500"
                                        type="error"
                                        role="tooltip"
                                    />
                                </TriggerButton>
                            </CarbonTooltip>
                        )}
                        {info && (
                            <CarbonIcon
                                key="info"
                                className="e-icon-validation-errors"
                                ariaLabel={info}
                                fontSize="small"
                                color={tokens.colorsSemanticInfo500}
                                type="info"
                                role="tooltip"
                                tooltipMessage={info}
                            />
                        )}
                        {warning && (
                            <CarbonIcon
                                key="warning"
                                className="e-icon-validation-errors"
                                ariaLabel={warning}
                                fontSize="small"
                                color={tokens.colorsSemanticCaution500}
                                type="warning"
                                role="tooltip"
                                tooltipMessage={warning}
                            />
                        )}
                    </span>
                )}
                {this.state.mode === 'ERRORS' && (
                    <CarbonTooltip
                        key="e-link-back-to-full-display-tooltip"
                        message={localize(
                            '@sage/xtrem-ui/display-errors-back-to-full-display-tooltip',
                            'For full display',
                        )}
                    >
                        <LinkWithCaret
                            key="e-link-back-to-full-display"
                            onClick={this.setDefaultMode}
                            className="e-link-back-to-full-display"
                        >
                            {localize('@sage/xtrem-ui/display-errors-back-to-full-display', 'Back to full display')}
                        </LinkWithCaret>
                    </CarbonTooltip>
                )}
                {this.state.mode === 'DEFAULT' && numErrors > 0 && (
                    <CarbonTooltip
                        key="e-link-error-numbers-tooltip"
                        message={localize('@sage/xtrem-ui/link-error-numbers-tooltip', 'Error information')}
                    >
                        <LinkWithCaret key="e-link-error-numbers" onClick={this.open} className="e-link-error-numbers">
                            {numErrors > 1
                                ? localize('@sage/xtrem-ui/link-errors-quantity', '{{0}} errors', [numErrors])
                                : localize('@sage/xtrem-ui/link-error-quantity', '1 error', [numErrors])}
                        </LinkWithCaret>
                    </CarbonTooltip>
                )}
            </>
        );
    }
}

export default StatusIconWithPopover;
