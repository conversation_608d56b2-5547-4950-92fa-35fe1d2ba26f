import CarbonIcon from 'carbon-react/esm/components/icon';
import { noop } from 'lodash';
import React from 'react';
import type { TooltipPosition } from '../tooltip/tooltip-types';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { FontSize } from 'carbon-react/esm/components/icon/icon.style';

interface CarbonIconProps {
    /** Add classes to this component */
    className?: string;
    /** Icon type */
    type: IconType;
    /** Background size */
    bgSize?: 'small' | 'medium' | 'large';
    /** Background shape */
    bgShape?: 'circle' | 'rounded-rect' | 'square';
    /** Background color theme */
    bg?: string;
    /** Icon font size */
    fontSize?: FontSize;
    /** Icon color */
    color?: string;
    /** Sets the icon in the disabled state */
    disabled?: boolean;
    /** The message string to be displayed in the tooltip */
    tooltipMessage?: string | React.ReactNode;
    /** The position to display the tooltip */
    tooltipPosition?: TooltipPosition;
    /** The tooltip background color */
    tooltipBgColor?: string;
    /** The tooltip font color */
    tooltipFontColor?: string;

    ariaLabel?: string;

    mx?: number;
    my?: number;
    m?: number;
    mt?: number;
    mr?: number;
    mb?: number;
    ml?: number;
}

export interface IconProps extends CarbonIconProps {
    onClick?: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
    style?: React.CSSProperties;
}

export const Icon = React.forwardRef<HTMLDivElement, IconProps>(({ style, onClick = noop, ...props }, ref) => {
    return (
        <div onClick={!props.disabled ? onClick : undefined} style={style}>
            <CarbonIcon {...props} ref={ref} />
        </div>
    );
});
