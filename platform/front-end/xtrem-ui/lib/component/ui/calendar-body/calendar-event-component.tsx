import React from 'react';
import type { XtremAppState } from '../../../redux';
import type { ScreenBaseDefinition } from '../../../service/screen-base-definition';
import type { CalendarEventCardProps } from './calendar-body-types';
import { CardComponent } from '../card/card-component';
import { getEventStyle } from './calendar-body-utils';
import { objectKeys } from '@sage/xtrem-shared';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

export function CalendarEventCardComponent({
    elementId,
    eventProps,
    selectedView,
    isMobileMonthlyView,
    value,
    screenId,
    hasClickEventListener,
    cardColor,
    eventCard,
}: CalendarEventCardProps): React.ReactElement | null {
    const screenDefinition = useDeepEqualSelector<XtremAppState, ScreenBaseDefinition>(
        state => state.screenDefinitions[screenId],
    );

    if (!screenDefinition || !value || !eventProps.event._def.extendedProps._id) {
        return null;
    }
    const _id = eventProps.event._def.extendedProps._id;
    const record = value.getRecordByIdAndLevel({ id: _id });

    const { eventClasses, style } = getEventStyle(screenId, eventProps, record, hasClickEventListener, cardColor);

    const commonProps: React.HTMLAttributes<HTMLElement> = {
        className: eventClasses,
        style,
    };

    if (isMobileMonthlyView) {
        return <button {...commonProps} type="button" />;
    }

    const cardDefinition = { ...eventCard };
    if (selectedView === 'dayGridMonth') {
        // In monthly view we only display the first line of the card
        objectKeys(cardDefinition).forEach(k => {
            if (k !== 'title' && k !== 'titleRight') {
                delete cardDefinition[k];
            }
        });
    }

    return (
        <div className="e-calendar-event-wrapper">
            <div {...commonProps}>
                <CardComponent
                    cardDefinition={cardDefinition}
                    parentElementId={elementId}
                    screenId={screenId}
                    value={record}
                />
            </div>
        </div>
    );
}
