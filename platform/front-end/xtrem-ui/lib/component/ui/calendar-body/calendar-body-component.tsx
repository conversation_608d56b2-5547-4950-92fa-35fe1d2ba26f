import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import timeGridPlugin from '@fullcalendar/timegrid';
import * as React from 'react';
import type { EventApi, CalendarOptions, EventContentArg, CalendarApi } from '@fullcalendar/core';
import { set, kebabCase } from 'lodash';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import { datePropertyValueToDateString } from '@sage/xtrem-date-time';
import { CalendarEventCardComponent } from './calendar-event-component';
import { triggerFieldEvent } from '../../../utils/events';
import type { CalendarBodyComponentProps, CalendarView } from './calendar-body-types';
import esLocale from '@fullcalendar/core/locales/es';
import enGbLocale from '@fullcalendar/core/locales/en-gb';
import frLocale from '@fullcalendar/core/locales/fr';
import zhCnLocale from '@fullcalendar/core/locales/zh-cn';
import deLocale from '@fullcalendar/core/locales/de';
import plLocale from '@fullcalendar/core/locales/pl';
import ptLocale from '@fullcalendar/core/locales/pt';
import arSaLocale from '@fullcalendar/core/locales/ar-sa';
import type { Locale } from '@sage/xtrem-shared';
import type { XtremAppState } from '../../../redux/state';
import { eventFormatter, getCalendarLocale } from './calendar-body-utils';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

const headerItems = ['back', 'today', 'prev', 'next', 'title'];

export const CalendarComponentBody = React.memo<CalendarBodyComponentProps>(
    ({
        cardColor,
        elementId,
        endDateField,
        eventCard,
        fieldProperties,
        fixedHeight,
        onEventClick,
        hasClickEventListener,
        isGreaterThanSmall,
        maxDate,
        minDate,
        screenId,
        selectedOptionsMenuItem,
        selectedView: externalSelectedView,
        startDateField,
        value,
        isEventMovable,
    }) => {
        const [selectedView, setSelectedView] = React.useState<CalendarView>(externalSelectedView || 'dayGridMonth');
        const calendarApiRef = React.useRef<CalendarApi | null>();

        const userLocale =
            useDeepEqualSelector<XtremAppState, Locale>(state => state.applicationContext?.locale as Locale) || 'en-US';

        const isMobileMonthlyView = React.useCallback(
            (): boolean => !isGreaterThanSmall && selectedView === 'dayGridMonth',
            [isGreaterThanSmall, selectedView],
        );

        const openInDayView = React.useCallback((date: Date): void => {
            setSelectedView('dayGridDay');
            calendarApiRef.current?.gotoDate(date);
        }, []);

        const triggerOnEventClick = React.useCallback(
            ({ event, jsEvent }: { event: EventApi; jsEvent: MouseEvent }) => {
                jsEvent.stopPropagation();
                jsEvent.preventDefault();
                if (isMobileMonthlyView() && event.start) {
                    openInDayView(event.start);
                } else if (value) {
                    // TS 5.2 added any cast
                    const _id = (event as any)._def.extendedProps._id;
                    const eventValue = value.getRecordByIdAndLevel({ id: _id });
                    const isModifierKeyPushed = jsEvent.ctrlKey || jsEvent.metaKey;

                    if (onEventClick) {
                        onEventClick(_id, eventValue, isModifierKeyPushed);
                    } else {
                        triggerFieldEvent(screenId, elementId, 'onEventClick', _id, eventValue, isModifierKeyPushed);
                    }
                }
            },
            [elementId, isMobileMonthlyView, onEventClick, openInDayView, screenId, value],
        );

        const triggerOnDateClick = React.useCallback(
            ({ date, jsEvent }: { date: Date; jsEvent: MouseEvent }) => {
                jsEvent.stopPropagation();
                jsEvent.preventDefault();
                if (isMobileMonthlyView()) {
                    openInDayView(date);
                } else {
                    triggerFieldEvent(screenId, elementId, 'onDayClick', date);
                }
            },
            [elementId, isMobileMonthlyView, openInDayView, screenId],
        );

        const fetchEvents = React.useCallback(
            async (info: { startStr: string; endStr: string }, successCb: Function): Promise<any> => {
                if (value) {
                    let filter: GraphQLFilter = {};
                    const startDateFilter = set({}, convertDeepBindToPathNotNull(startDateField), {
                        _gte: info.startStr,
                        _lte: info.endStr,
                    });

                    if (endDateField) {
                        const endDateFilter = set({}, convertDeepBindToPathNotNull(endDateField), {
                            _gte: info.startStr,
                            _lte: info.endStr,
                        });
                        const eventLongerThanViewSpanFilter = {
                            _and: [
                                set({}, convertDeepBindToPathNotNull(endDateField), { _gte: info.startStr }),
                                set({}, convertDeepBindToPathNotNull(startDateField), { _gte: info.endStr }),
                            ],
                        };
                        filter = {
                            _or: [startDateFilter, endDateFilter, eventLongerThanViewSpanFilter],
                        };
                    } else {
                        filter = startDateFilter;
                    }

                    const events = await value.getPage({
                        tableFieldProperties: fieldProperties,
                        pageSize: 500,
                        rawFilter: filter,
                        orderBy: set({}, convertDeepBindToPathNotNull(startDateField), 1),
                        selectedOptionsMenuItem,
                    });
                    successCb(
                        events.map(record =>
                            eventFormatter({
                                endDateField,
                                isEventMovable,
                                record,
                                screenId,
                                startDateField,
                            }),
                        ),
                    );
                }

                return [];
            },
            [endDateField, fieldProperties, isEventMovable, screenId, selectedOptionsMenuItem, startDateField, value],
        );

        React.useEffect(() => {
            calendarApiRef.current?.changeView(selectedView as string);
        }, [selectedView]);

        React.useEffect(() => {
            if (externalSelectedView) {
                setSelectedView(externalSelectedView);
            }
        }, [externalSelectedView]);

        React.useEffect(() => {
            calendarApiRef.current?.refetchEvents();
        }, [value, calendarApiRef, selectedOptionsMenuItem]);

        const eventContent = React.useCallback(
            (eventProps: EventContentArg) => (
                <CalendarEventCardComponent
                    elementId={elementId}
                    eventProps={eventProps}
                    value={value}
                    screenId={screenId}
                    isMobileMonthlyView={isMobileMonthlyView()}
                    cardColor={cardColor}
                    hasClickEventListener={hasClickEventListener}
                    selectedView={selectedView}
                    eventCard={eventCard}
                />
            ),
            [
                cardColor,
                elementId,
                eventCard,
                hasClickEventListener,
                isMobileMonthlyView,
                screenId,
                selectedView,
                value,
            ],
        );

        const end = React.useMemo(() => datePropertyValueToDateString(minDate) || undefined, [minDate]);
        const start = React.useMemo(() => datePropertyValueToDateString(maxDate) || undefined, [maxDate]);

        const onCustomButtonsClick = React.useCallback(() => {
            setSelectedView('dayGridMonth');
        }, []);

        const calendarProps = React.useMemo<CalendarOptions>(
            () => ({
                headerToolbar: {
                    left: headerItems.join(' '),
                    center: '',
                    right: '',
                },
                locales: [esLocale, enGbLocale, plLocale, deLocale, ptLocale, frLocale, zhCnLocale, arSaLocale],
                locale: getCalendarLocale(userLocale),
                weekends: true,
                initialView: 'dayGridMonth',
                views: {
                    dayGridMonth: {
                        dayMaxEvents: 4, // Three events + text show more = 4
                        moreLinkClick: 'dayGridThree',
                    },
                    dayGridThree: {
                        type: 'dayGrid',
                        duration: { days: 3 },
                        buttonText: '3 day',
                        eventLimit: false,
                    },
                },
                customButtons: {
                    back: {
                        text: 'back',
                        click: onCustomButtonsClick,
                    },
                },
                editable: !!isEventMovable,
                plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],
                events: fetchEvents,
                fixedWeekCount: false,
                eventContent,
                eventClick: triggerOnEventClick,
                dateClick: triggerOnDateClick,
                height: fixedHeight,
                validRange: {
                    start,
                    end,
                },
            }),
            [
                end,
                eventContent,
                fetchEvents,
                fixedHeight,
                isEventMovable,
                onCustomButtonsClick,
                start,
                triggerOnDateClick,
                triggerOnEventClick,
                userLocale,
            ],
        );

        return (
            <div className={`e-calendar-body e-calendar-body-type-${kebabCase(String(selectedView))}`}>
                <FullCalendar
                    ref={(c: FullCalendar | null): void => {
                        calendarApiRef.current = c?.getApi();
                    }}
                    {...calendarProps}
                />
            </div>
        );
    },
);

CalendarComponentBody.displayName = 'CalendarComponentBody';
