import type { EventContentArg, EventInput } from '@fullcalendar/core';
import type { CollectionValue, GridLodable } from '../../../service/collection-data-service';
import type { ValueOrCallbackWitRecordValue } from '../../../utils/types';
import type { CardDefinition } from '../card/card-component';
import type { Dict } from '@sage/xtrem-shared';
import type { OptionsMenuItem } from '../../container/page/page-types';
import type { HasCalendarConfigurationOptions } from '../../field/traits';
import { localize } from '../../../service/i18n-service';

export const calendarViews: Dict<() => string> = {
    dayGridDay: () => localize('@sage/xtrem-ui/calendar-view-day', 'Day'),
    dayGridMonth: () => localize('@sage/xtrem-ui/calendar-view-month', 'Month'),
    dayGridWeek: () => localize('@sage/xtrem-ui/calendar-view-week', 'Week'),
    dayGridThree: () => localize('@sage/xtrem-ui/calendar-view-3-day', '3 day'),
};

export type CalendarView = keyof typeof calendarViews;

export interface CalendarEvent extends EventInput {
    allDay?: boolean;
    end: Date | string;
    start: Date | string;
    _id: string;
}

export interface CalendarBodyComponentProps extends HasCalendarConfigurationOptions<any> {
    cardColor?: ValueOrCallbackWitRecordValue<any, string>;
    elementId: string;
    eventCard: CardDefinition;
    fieldProperties: GridLodable;
    fixedHeight?: number;
    hasClickEventListener?: boolean;
    isGreaterThanSmall: boolean;
    onEventClick?: (_id: string, eventDetails: any, isModifierKeyPushed?: boolean) => void;
    screenId: string;
    selectedOptionsMenuItem?: OptionsMenuItem;
    selectedView?: CalendarView;
    value?: CollectionValue;
    filterModel?: any;
}

export interface CalendarEventCardProps {
    elementId: string;
    eventProps: EventContentArg;
    isMobileMonthlyView: boolean;
    selectedView: CalendarView;
    value?: CollectionValue;
    screenId: string;
    hasClickEventListener?: boolean;
    cardColor?: ValueOrCallbackWitRecordValue<any, string>;
    eventCard: CardDefinition;
}
