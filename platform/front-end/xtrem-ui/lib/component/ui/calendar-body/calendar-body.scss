.e-calendar-body {
    --fc-event-text-color: var(--colorsYin090);
    --fc-button-text-color: var(--colorsYang100);
    --fc-button-hover-bg-color: var(--colorsActionMinor600);

    .fc .fc-button.fc-back-button {
        display: none;
    }

    @include small_and_below {

        &.e-calendar-body-type-day-grid-day .fc .fc-button.fc-back-button {
            display: inline-block;
        }

        // Overriding fullcalendars inline styles due to hacks in the grid system.
        .fc-day-grid-container {
            height: 240px !important;

            // Fix for Februaries starting on Sundays, which only have 4 rows.
            .fc-week:nth-child(4):last-child {
                height: 52px !important;
                border-bottom: 1px solid var(--colorsUtilityMajor100) !important;
            }
        }
    }


    &.e-calendar-body-type-day-grid-day {
        @include small_and_below {
            .fc-day-grid .fc-event-container {
                display: flex;
            }

            .fc-header-toolbar .fc-left {

                .fc-prev-button,
                .fc-next-button,
                .fc-today-button {
                    display: none;
                }

                .fc-back-button {
                    display: inline-block;
                }
            }
        }
    }

    .e-card {
        border: none;
    }

    .fc-icon {
        height: 100%;
    }

    .fc-daygrid-dot-event:hover {
        background: transparent;
    }

    .fc .fc-daygrid-day.fc-day-today {
        background: var(--colorsUtilityMajor040);
    }


    &.e-calendar-body-type-day-grid-month {
        .e-card-content {
            padding: 0;
        }

        .e-card {
            min-height: 28px;
        }

        .e-card-body {
            min-height: 28px;
        }

        .fc-day-number {
            width: 28px;
            text-align: center;
        }
    }

    .fc-day-grid-container {
        overflow: hidden !important;
    }

    .fc-content-skeleton {
        position: relative;
        overflow-y: auto;
        overflow-x: hidden;
        max-height: 100%;
    }

    .e-day-header {
        cursor: pointer;
    }

    .e-calendar-event-container {
        max-width: 100%;
        background: var(--colorsYang100);
        border: none;
        border-left: 4px solid;
        display: flex;
        flex: 1;
        font-family: var(--fontFamiliesDefault);
        font-weight: var(--fontWeights500);
        justify-content: space-between;
        min-height: 8px;
        border-radius: var(--borderRadius050);

        .e-card-image {
            padding-left: 8px;
        }

        .e-card.e-card-has-image .e-card-content,
        .e-card.e-card-has-image>.e-card-line-3 {
            width: calc(100% - 52px);
        }

        &>div {
            max-width: 100%;
            flex: 1;
            padding-left: 4px;
            padding-right: 4px;
        }

        * {
            cursor: pointer;
        }

        &:focus {
            outline: 3px solid var(--colorsSemanticFocus500);
        }

        .e-calendar-event-left,
        .e-calendar-event-right {
            flex-grow: 0;
        }

        .e-calendar-event-left {
            padding-right: 4px;
        }

        .e-calendar-event-right {
            padding-left: 4px;
        }

        .e-calendar-event-center {
            flex-grow: 1;
            text-align: left;
            width: 0;

            .e-field {
                width: 100%;
            }
        }

        .e-calendar-event-line2 {
            font-family: var(--fontFamiliesDefault);
        }

        .e-field-read-only {
            padding: 0;
            height: 18px;
            line-height: 18px;
        }

        .e-field {
            box-sizing: border-box;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            min-width: unset;
            padding: unset;

            @include e-field-label-hidden;

            .common-input__help-text {
                display: none;
            }

            div {
                min-height: unset;
            }

            .e-field-prefix,
            .e-field-postfix {
                display: none;
            }
        }
    }

    .fc-body>tr>.fc-widget-content {
        border-left: none;
        border-right: none;
    }

    .fc-event {
        background: none;
        border-radius: 0;
        border: none;
    }

    .fc-unthemed th,
    .fc-unthemed td,
    .fc-unthemed thead,
    .fc-unthemed tbody,
    .fc-unthemed .fc-divider,
    .fc-unthemed .fc-row,
    .fc-unthemed .fc-content,
    .fc-unthemed .fc-popover,
    .fc-unthemed .fc-list-view,
    .fc-unthemed .fc-list-heading td {
        border-color: var(--colorsUtilityMajor100);
    }

    .fc-left>h2 {
        margin-left: 6px;
    }

    .fc-toolbar.fc-header-toolbar {
        padding-top: 24px;
        padding-bottom: 24px;
    }

    .fc-more-cell {
        .fc-more {
            color: var(--colorsActionMajor500);
            font-size: 14px;
            text-decoration: underline;
        }
    }

    .fc-header-toolbar {
        .fc-left {
            font-family: $fontAdelle;
            padding-left: 18px;
        }

        h2 {
            display: inline-block;

            @include small_and_below {
                font-size: 16px;
                line-height: 28px;
            }
        }

        .fc-prev-button,
        .fc-next-button {
            border: none;
            line-height: 28px;
            height: 28px;
            width: 28px;
            margin: 0;
            font-size: 16px;
            vertical-align: top;
            padding: 0;
            color: var(--colorsActionMinor500);
            font-weight: bold;
            border-radius: var(--borderRadius050);
        }

        .fc-today-button,
        .fc-back-button {
            border: none;
            font-size: 0;
            height: 28px;
            width: 28px;
            margin: 0;
            padding: 0;
            vertical-align: top;
            border-radius: var(--borderRadius050);

            &:disabled {
                color: var(--colorsActionMajorYin030);
                background: transparent;
                border: 2px solid var(--colorsActionDisabled500);
            }

            &::before {
                @include icon;
                font-size: 16px;
                font-style: normal;
                font-weight: normal;
                line-height: 16px;
                display: block;
            }
        }

        .fc-today-button::before {
            content: '\E970';
        }

        .fc-back-button::before {
            content: '';
        }

    }

    .fc-day-number {
        float: left !important;
        font-family: $fontAdelle;
        padding: 4px;
        margin: 4px;
    }

    .fc-other-month {
        background-color: var(--colorsUtilityMajor025);
    }

    .fc-widget-header {
        border: none;

        th {
            border: none;
            text-transform: uppercase;
            font-family: $fontAdelle;
            text-align: left;
            padding-bottom: 8px;
            padding-left: 16px;
        }
    }

    .fc-button.fc-button-primary {
        background: transparent;
        color: var(--colorsYin090);
        border-color: var(--colorsUtilityMajor100);
        font-weight: bold;
        text-transform: capitalize;

        &.fc-button-active {
            background: var(--colorsSemanticInfo500);
            color: var(--colorsYang100);
            border-color: var(--colorsSemanticInfo500);
        }

        &:focus {
            outline: 3px solid var(--colorsSemanticFocus500);
            outline-offset: 0;
        }

        @include small_and_below {
            background: transparent;
        }
    }

    .fc-event-container {
        padding: 0 4px 4px 4px;
    }

    .fc-unthemed td.fc-today {
        &.fc-widget-content {
            padding: 0 !important;
            border-color: var(--colorsActionMajor500);
            border-top: 3px solid var(--colorsActionMajor500);
            box-shadow: inset 0px -2px 0px 0px var(--colorsActionMajor500);
            background: var(--colorsYang100);
        }
    }

    .fc-day-grid-event {
        display: flex;

        &.fc-h-event.fc-event.fc-start.fc-end {
            background-color: var(--colorsYang100);
            color: black;
            border: none;
            border-radius: 0;
        }
    }

    .e-calendar-event-description {
        display: none;
    }

    .e-calendar-event-wrapper {
        display: flex;
        width: 100%;
    }

    .e-calendar-event-container-no-background {
        border: 1px solid var(--colorsUtilityMajor100);
    }

    .e-calendar-event-container-not-in-range {
        opacity: 0.5;
    }

    .e-calendar-event-title-right input,
    .e-calendar-event-title input {
        font-weight: bold;
    }

    .rbc-time-header-cell-single-day+.rbc-allday-cell {
        .e-calendar-event-title {
            font-weight: bold;
        }
    }
}

.e-calendar-view-select-container {
    display: inline-block !important;
    padding-right: 4px;
}