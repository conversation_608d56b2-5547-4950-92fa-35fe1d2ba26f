import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { CalendarBodyComponentProps } from './calendar-body-types';

const CalendarComponentBodyComponent = React.lazy(() =>
    import('./calendar-body-component').then(c => ({ default: c.CalendarComponentBody })),
);

export function AsyncCalendarBodyComponent(props: CalendarBodyComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <CalendarComponentBodyComponent {...props} />
        </React.Suspense>
    );
}
