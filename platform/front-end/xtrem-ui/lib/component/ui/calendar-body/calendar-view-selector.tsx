import Button from 'carbon-react/esm/components/button';
import MultiActionButton from 'carbon-react/esm/components/multi-action-button';
import * as React from 'react';
import type { CalendarView } from './calendar-body-types';
import { calendarViews } from './calendar-body-types';
import { objectKeys } from '@sage/xtrem-shared';

export interface CalendarViewSelectorProps {
    selectedView: CalendarView;
    onSelectView: (view: CalendarView) => void;
}

export function CalendarViewSelector({ selectedView, onSelectView }: CalendarViewSelectorProps): React.ReactElement {
    return (
        <div className="e-calendar-view-select-container">
            <MultiActionButton
                className="e-calendar-view-select"
                data-testid="e-calendar-view-select"
                text={calendarViews[selectedView]()}
                subtext=""
            >
                {objectKeys(calendarViews)
                    .filter(k => k !== selectedView)
                    .map((k: CalendarView) => (
                        <Button
                            data-testid={`e-calendar-view-select-item-${k}`}
                            key={k}
                            onClick={(): void => onSelectView(k)}
                            size="small"
                        >
                            {calendarViews[k]()}
                        </Button>
                    ))}
            </MultiActionButton>
        </div>
    );
}
