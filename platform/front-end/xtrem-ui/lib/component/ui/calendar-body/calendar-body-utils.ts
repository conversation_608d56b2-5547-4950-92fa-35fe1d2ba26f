import type { EventContentArg } from '@fullcalendar/core';
import type { Locale } from '@sage/xtrem-shared';
import { get, memoize } from 'lodash';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { hexToRgb, splitValueToMergedValue } from '../../../utils/transformers';
import type { ValueOrCallbackWitRecordValue } from '../../../utils/types';
import type { PropertyValueType } from '../../field/reference/reference-types';
import type { CollectionItem } from '../../types';
import type { CalendarEvent } from './calendar-body-types';

export const getEventStyle = (
    screenId: string,
    eventProps: EventContentArg,
    record: any,
    hasClickEventListener = false,
    cardColor?: ValueOrCallbackWitRecordValue<any, string>,
): { eventClasses: string; style: React.CSSProperties } => {
    let eventClasses = 'e-calendar-event-container';

    if (record.start < eventProps.view.currentStart || record.start > eventProps.view.currentEnd) {
        eventClasses += ' e-calendar-event-container-not-in-range';
    }
    const style: React.CSSProperties = {};

    if (hasClickEventListener) {
        style.cursor = 'pointer';
    }

    const cardBackgroundColor = resolveByValue({
        fieldValue: record,
        propertyValue: cardColor,
        screenId,
        rowValue: splitValueToMergedValue(record),
    });

    if (cardBackgroundColor) {
        style.borderColor = cardBackgroundColor;
        style.borderTop = `1px solid ${cardBackgroundColor}`;
        style.borderRight = `1px solid ${cardBackgroundColor}`;
        style.borderBottom = `1px solid ${cardBackgroundColor}`;
        style.paddingLeft = 4;
        const rgb = hexToRgb(cardBackgroundColor);
        if (rgb) {
            style.background = `rgba(${rgb.r},${rgb.g},${rgb.b},0.1)`;
        }
    } else {
        eventClasses += ' e-calendar-event-container-no-background';
    }
    return { eventClasses, style };
};

export interface EventFormatterArgs {
    screenId: string;
    record: CollectionItem;
    startDateField: PropertyValueType;
    endDateField?: PropertyValueType;
    isEventMovable?: ValueOrCallbackWitRecordValue<any, boolean>;
}

export const eventFormatter = ({
    screenId,
    record,
    startDateField,
    endDateField,
    isEventMovable,
}: EventFormatterArgs): CalendarEvent => {
    const parsedStartDate = new Date(get(record, convertDeepBindToPathNotNull(startDateField)));

    const parsedEndDate = endDateField ? new Date(get(record, convertDeepBindToPathNotNull(endDateField))) : undefined;

    const _id = record._id;

    const eventStartEditable = !!resolveByValue<boolean>({
        fieldValue: record,
        propertyValue: isEventMovable,
        screenId,
        rowValue: record,
        skipHexFormat: true,
    });

    return {
        allDay: !parsedEndDate,
        end: parsedEndDate || parsedStartDate,
        start: parsedStartDate,
        eventStartEditable,
        editable: eventStartEditable,
        _id,
    };
};

export const getCalendarLocale = memoize((userLocale: Locale): string => {
    switch (userLocale) {
        case 'ar-SA':
        case 'zh-CN':
        case 'en-GB':
            return userLocale.toLowerCase();
        default:
            return userLocale.toLowerCase().substring(0, 2);
    }
});
