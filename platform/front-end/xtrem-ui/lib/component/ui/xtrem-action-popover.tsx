import * as tokens from '@sage/design-tokens/js/base/common';
import {
    ActionPopover,
    ActionPopoverDivider,
    ActionPopoverItem,
    ActionPopoverMenu,
} from 'carbon-react/esm/components/action-popover';
import Button from 'carbon-react/esm/components/button';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import Hr from 'carbon-react/esm/components/hr';
import Icon from 'carbon-react/esm/components/icon';
import IconButton from 'carbon-react/esm/components/icon-button';
import { noop } from 'lodash';
import * as React from 'react';
import * as ReactDom from 'react-dom';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../redux';
import { localize } from '../../service/i18n-service';
import { ACTION_POPOVER_SUBMENUS_COMPAT_CSS_CLASS, SINGLE_INLINE_ACTION_CSS_CLASS } from '../../utils/constants';
import type { Has<PERSON>enericError<PERSON><PERSON><PERSON>, HasIcon } from '../field/traits';
import type { TableDropdownActionExternalProps } from './table-shared/table-dropdown-actions/table-dropdown-actions';

export interface XtremActionPopoverItem extends HasIcon, HasGenericErrorHandler<any> {
    childrenProp?: Array<XtremActionPopoverItem>;
    key: string;
    isDestructive?: boolean;
    isDisabled?: boolean;
    isHidden?: boolean;
    isMenuSeparator?: false;
    onClick: () => void;
    pendoId?: string;
    testId?: string;
    title: string;
}

export interface XtremActionPopoverMenuSeparator {
    key: string;
    isHidden?: boolean;
    isMenuSeparator: true;
    pendoId?: string;
    testId?: string;
}

export type XtremActionPopoverItemOrMenuSeparator = XtremActionPopoverItem | XtremActionPopoverMenuSeparator;

export interface XtremActionPopoverExternalProps {
    api?: TableDropdownActionExternalProps['api'];
    color?: string;
    column?: TableDropdownActionExternalProps['column'];
    hasInlineActions?: boolean;
    isDisabled?: boolean;
    isOverSidebar?: boolean;
    items: Array<XtremActionPopoverItemOrMenuSeparator>;
    noIconSupport?: boolean;
    onOpen?: () => void;
    pendoId?: string;
    rowIndex?: TableDropdownActionExternalProps['rowIndex'];
}

export interface XtremActionPopoverProps extends XtremActionPopoverExternalProps {
    isDeviceLessThanSmall: boolean;
}

function XtremMobilePopoverMenuItem({
    item,
    level = 0,
    onSelect,
    isDisabled,
}: {
    item: XtremActionPopoverItemOrMenuSeparator;
    level: number;
    onSelect: (item: XtremActionPopoverItem) => (event: React.MouseEvent<HTMLButtonElement>) => void;
    isDisabled?: boolean;
}): React.ReactElement | null {
    const [isOpen, setOpen] = React.useState(false);

    if (item.isHidden) {
        return null;
    }

    if (item.isMenuSeparator) {
        return <Hr m={0} data-testid={item.testId} />;
    }

    const props = {
        fullWidth: true,
        buttonType: 'tertiary',
        mt: '8px',
        mb: '8px',
        ml: 0,
        mr: 0,
        iconType: item.icon || 'none',
        'data-testid': item.testId,
        'aria-label': item.title,
        disabled: isDisabled || item.isDisabled,
        isDestructive: item.isDestructive,
        key: `${level}_${item.key}`,
        className: item.isDestructive
            ? `e-action-popover-item e-action-popover-item-level-${level} e-action-popover-item-destructive`
            : `e-action-popover-item e-action-popover-item-level-${level}`,
    } as const;

    if (item.childrenProp && item.childrenProp.length > 0) {
        return (
            <>
                <ButtonMinor {...props} onClick={(): void => setOpen(!isOpen)}>
                    <span className="e-action-popover-item-mobile-label">{item.title}</span>
                    <Icon type={isOpen ? 'chevron_down' : 'chevron_up'} />
                </ButtonMinor>
                {isOpen &&
                    item.childrenProp.map(c => (
                        <XtremMobilePopoverMenuItem
                            key={`${level}_${item.key}`}
                            item={c}
                            level={level + 1}
                            isDisabled={isDisabled}
                            onSelect={onSelect}
                        />
                    ))}
            </>
        );
    }

    return (
        <ButtonMinor {...props} onClick={onSelect(item)}>
            <span className="e-action-popover-item-mobile-label">{item.title}</span>
        </ButtonMinor>
    );
}

export function XtremActionPopover({
    color,
    isDeviceLessThanSmall,
    isDisabled,
    isOverSidebar,
    items,
    noIconSupport,
    onOpen,
    pendoId,
}: XtremActionPopoverProps): React.ReactNode {
    const [isOpen, setIsOpen] = React.useState<boolean>(false);

    React.useEffect(() => {
        return (): void => {
            document.body.classList.remove(ACTION_POPOVER_SUBMENUS_COMPAT_CSS_CLASS);
        };
    }, []);

    const getVisibleMenuItemsAndMenuSeparators = React.useCallback((): Array<XtremActionPopoverItemOrMenuSeparator> => {
        return items.filter((item: XtremActionPopoverItemOrMenuSeparator) => !item.isHidden);
    }, [items]);

    const toggleMobileMenu = React.useCallback(
        (
            event: React.KeyboardEvent<HTMLButtonElement> | React.MouseEvent<HTMLButtonElement | HTMLDivElement>,
        ): void => {
            event.preventDefault();
            event.stopPropagation();

            if (onOpen) {
                onOpen();
            }
            setIsOpen(o => !o);
        },
        [onOpen],
    );

    const onRecordClick = React.useCallback(
        (item: XtremActionPopoverItem) =>
            (event: React.MouseEvent<HTMLButtonElement>): void => {
                toggleMobileMenu(event);
                if (item.onClick && !isDisabled) {
                    item.onClick();
                }
            },
        [isDisabled, toggleMobileMenu],
    );

    const getSingleActionIconButton = React.useCallback(
        (item: XtremActionPopoverItem): React.ReactNode => {
            const onClick = (e: React.MouseEvent<HTMLButtonElement>): void => {
                e.preventDefault();
                e.stopPropagation();
                if (item.onClick && !isDisabled) {
                    item.onClick();
                }
            };
            return (
                <IconButton
                    onClick={onClick}
                    disabled={isDisabled || item.isDisabled}
                    // @ts-expect-error tabIndex not included in the carbon IconButton typings
                    tabIndex={isDisabled ? -1 : 0}
                    aria-label={item.title}
                    data-testid={SINGLE_INLINE_ACTION_CSS_CLASS}
                    className={SINGLE_INLINE_ACTION_CSS_CLASS}
                >
                    <Icon
                        tooltipMessage={item.title}
                        type={item.icon as any}
                        color={item.isDestructive ? tokens.colorsSemanticNegative500 : tokens.colorsActionMinor500}
                    />
                </IconButton>
            );
        },
        [isDisabled],
    );

    const getMobileActionButton = React.useCallback((): React.ReactNode => {
        const visibleItems = getVisibleMenuItemsAndMenuSeparators();
        const hasChildren = (item: XtremActionPopoverItemOrMenuSeparator): boolean => {
            return (
                !item.isMenuSeparator &&
                !!item.childrenProp &&
                Array.isArray(item.childrenProp) &&
                item.childrenProp.length > 0
            );
        };
        if (noIconSupport || (visibleItems.length === 1 && hasChildren(visibleItems[0])) || visibleItems.length > 1) {
            return (
                <IconButton
                    onClick={toggleMobileMenu}
                    data-component="action-popover-button"
                    data-element="action-popover-button"
                >
                    <Icon type="ellipsis_vertical" color={color} />
                </IconButton>
            );
        }

        if (visibleItems.length === 1 && !visibleItems[0].isMenuSeparator) {
            return getSingleActionIconButton(visibleItems[0]);
        }

        return null;
    }, [color, getSingleActionIconButton, getVisibleMenuItemsAndMenuSeparators, noIconSupport, toggleMobileMenu]);

    const menuItemReducer = React.useCallback(
        (
            previousValue: JSX.Element[],
            item: XtremActionPopoverItemOrMenuSeparator,
            index: number,
            menuItems: Array<XtremActionPopoverItemOrMenuSeparator>,
        ): JSX.Element[] => {
            if (item.isMenuSeparator) {
                if (index === 0) {
                    // Do not render menu separator at the beginning of the list
                    return previousValue;
                }

                if (menuItems[index - 1].isMenuSeparator) {
                    // Do not render repeated menu separators
                    return previousValue;
                }

                if (menuItems.slice(index).every(i => i.isMenuSeparator)) {
                    // Do not render menu separator at the end of the list
                    return previousValue;
                }

                previousValue.push(
                    isDeviceLessThanSmall ? (
                        <div className="e-action-popover-divider" key={item.key}>
                            <ActionPopoverDivider />
                        </div>
                    ) : (
                        <ActionPopoverDivider key={item.key} />
                    ),
                );

                return previousValue;
            }

            const actionPopoverProps: any = {
                icon: item.icon,
                disabled: isDisabled || item.isDisabled,
                onClick: onRecordClick(item),
                children: <span data-testid={item.testId}>{item.title}</span>,
                key: item.key,
                'data-pendoid': item.pendoId,
            };

            if (item.childrenProp && item.childrenProp.length > 0) {
                const submenu = (
                    <ActionPopoverMenu key={item.key}>
                        {item.childrenProp.reduce(menuItemReducer, [] as JSX.Element[])}
                    </ActionPopoverMenu>
                );
                actionPopoverProps.submenu = submenu;
                actionPopoverProps.onClick = noop;
            }
            actionPopoverProps.className = item.isDestructive ? 'e-action-popover-item-destructive' : undefined;
            const actionPopoverItem = <ActionPopoverItem {...actionPopoverProps} />;
            previousValue.push(actionPopoverItem);
            return previousValue;
        },
        [isDeviceLessThanSmall, isDisabled, onRecordClick],
    );

    const createActionPopoverChildren = React.useMemo((): JSX.Element[] => {
        return getVisibleMenuItemsAndMenuSeparators().reduce(menuItemReducer, [] as JSX.Element[]);
    }, [getVisibleMenuItemsAndMenuSeparators, menuItemReducer]);

    const createActionPopoverChildrenMobile = React.useCallback((): JSX.Element => {
        return (
            <div className="e-action-popover-item-list-wrapper">
                {getVisibleMenuItemsAndMenuSeparators().map((item: XtremActionPopoverItem) => (
                    <XtremMobilePopoverMenuItem
                        key={item.key}
                        item={item}
                        isDisabled={isDisabled}
                        level={0}
                        onSelect={onRecordClick}
                    />
                ))}
            </div>
        );
    }, [getVisibleMenuItemsAndMenuSeparators, isDisabled, onRecordClick]);

    const itemHasNoIcon = React.useCallback((item: XtremActionPopoverItem): boolean => {
        return !item.icon || item.icon === 'none';
    }, []);

    const onActionPopoverClose = React.useCallback((): void => {
        setIsOpen(false);
    }, []);

    const onActionPopoverOpen = React.useCallback((): void => {
        /* This timeout is necessary as the carbon ActionPopover submenus are buggy and flickering in first render */
        document.body.classList.add(ACTION_POPOVER_SUBMENUS_COMPAT_CSS_CLASS);
        setIsOpen(true);
        document.body.classList.remove(ACTION_POPOVER_SUBMENUS_COMPAT_CSS_CLASS);
        onOpen?.();
    }, [onOpen]);

    const renderButton = React.useCallback<
        NonNullable<React.ComponentProps<typeof ActionPopover>['renderButton']>
    >(() => {
        const message = localize('@sage/xtrem-ui/action-button-more', 'More Actions');

        return (
            <IconButton data-component="action-popover-button" data-element="action-popover-button">
                <Icon aria-label={message} type="ellipsis_vertical" tooltipMessage={message} color={color} />
            </IconButton>
        );
    }, [color]);

    const renderDesktop = React.useCallback((): React.ReactNode => {
        const message = localize('@sage/xtrem-ui/action-button-more', 'More Actions');
        const visibleItemsAndMenuSeparators: XtremActionPopoverItemOrMenuSeparator[] =
            getVisibleMenuItemsAndMenuSeparators();
        const visibleItems: XtremActionPopoverItem[] = visibleItemsAndMenuSeparators.filter(
            i => !i.isMenuSeparator,
        ) as XtremActionPopoverItem[];

        if (
            noIconSupport ||
            (visibleItems.length === 1 && visibleItems[0].childrenProp && visibleItems[0].childrenProp.length > 0) ||
            visibleItems.length > 1 ||
            (visibleItems.length === 1 && itemHasNoIcon(visibleItems[0]))
        ) {
            if (isDisabled) {
                return (
                    <Button
                        iconType="ellipsis_vertical"
                        disabled={true}
                        aria-label={message}
                        buttonType="tertiary"
                        onClick={noop}
                        data-testid="e-popover-open"
                    />
                );
            }
            return (
                <ActionPopover
                    data-pendoid={pendoId}
                    renderButton={renderButton}
                    onOpen={onActionPopoverOpen}
                    onClose={onActionPopoverClose}
                >
                    {createActionPopoverChildren}
                </ActionPopover>
            );
        }

        if (visibleItems.length === 1) {
            return getSingleActionIconButton(visibleItems[0]);
        }

        return null;
    }, [
        createActionPopoverChildren,
        getSingleActionIconButton,
        getVisibleMenuItemsAndMenuSeparators,
        isDisabled,
        itemHasNoIcon,
        noIconSupport,
        onActionPopoverClose,
        onActionPopoverOpen,
        pendoId,
        renderButton,
    ]);

    const renderMobile = React.useCallback((): React.ReactNode => {
        const classes = ['e-action-popover-mobile'];
        if (isDeviceLessThanSmall && isOverSidebar) {
            classes.push('e-action-popover-mobile-over-sidebar');
        }
        return (
            <>
                {isOpen &&
                    ReactDom.createPortal(
                        <div className={classes.join(' ')}>
                            <div className="e-action-popover-mobile-background" onClick={toggleMobileMenu} />
                            <div className="e-action-popover-mobile-content" data-component="action-popover">
                                {createActionPopoverChildrenMobile()}
                            </div>
                        </div>,
                        window.document.body,
                    )}
                <div className="e-action-popover-mobile">
                    <div data-testid="e-action-popover-mobile-button" className="e-action-popover-mobile-button">
                        {getMobileActionButton()}
                    </div>
                </div>
            </>
        );
    }, [
        createActionPopoverChildrenMobile,
        getMobileActionButton,
        isDeviceLessThanSmall,
        isOpen,
        isOverSidebar,
        toggleMobileMenu,
    ]);

    if (getVisibleMenuItemsAndMenuSeparators().length === 0) {
        return null;
    }

    if (isDeviceLessThanSmall) {
        return renderMobile();
    }

    return renderDesktop();
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: XtremActionPopoverExternalProps,
): XtremActionPopoverProps => ({
    ...props,
    isDeviceLessThanSmall: state.browser.lessThan.s,
});

export default connect(mapStateToProps)(XtremActionPopover);
