import type { ClientNode } from '@sage/xtrem-client';
import * as React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../redux';
import type { FocusPosition, ReduxResponsive } from '../../redux/state';
import ValidationIcon from 'carbon-react/esm/__internal__/validations';
import { ConnectedNestedFieldWrapper } from '../../render/nested-field-wrapper';
import type { CollectionValue } from '../../service/collection-data-service';
import type { PageArticleItem } from '../../service/layout-types';
import type { ScreenBase } from '../../service/screen-base';
import type { ValidationResult } from '../../service/screen-base-definition';
import type { NodePropertyType } from '../../types';
import { ContextType } from '../../types';
import { getDataTestIdAttribute, isHidden as isElementHidden } from '../../utils/dom';
import { convertDeepBindToPathNotNull, isNestedField } from '../../utils/nested-field-utils';
import { resolveByValue } from '../../utils/resolve-value-utils';
import { calculateContainerWidth, calculateFieldWidth, getGutterSize } from '../../utils/responsive-utils';
import { splitValueToMergedValue } from '../../utils/transformers';
import type { ValueOrCallbackWithFieldValue } from '../../utils/types';
import { withCollectionValueItemSubscription } from '../connected-collection';
import { generateFieldId } from '../field/carbon-helpers';
import IconButton from 'carbon-react/esm/components/icon-button';
import type { NestedField, NestedFieldTypes, NestedFieldTypesWithoutTechnical } from '../nested-fields';
import { withoutNestedTechnical } from '../nested-fields';
import { GridColumn, GridRow } from '@sage/xtrem-ui-components';
import type { XtremActionPopoverItemOrMenuSeparator } from './xtrem-action-popover';
import XtremActionPopover from './xtrem-action-popover';
import Icon from 'carbon-react/esm/components/icon';
import { localize } from '../../service/i18n-service';
import type { FieldKey } from '../types';
import * as tokens from '@sage/design-tokens/js/base/common';
import { getNestedFieldElementId, normalizeUnderscoreBind } from '../../utils/abstract-fields-utils';
import { get, isNil, isObject, isString } from 'lodash';
import { Checkbox } from 'carbon-react/esm/components/checkbox';
import type { Dict } from '@sage/xtrem-shared';
import type { FormattedNodeDetails } from '../../service/metadata-types';
import { getBindAndSelectorFromDeepBinding } from '../../service/graphql-query-builder';

export interface NestedBlockProps {
    actionPopoverItems?: XtremActionPopoverItemOrMenuSeparator[];
    additionalTestId?: string;
    availableColumns: number;
    baseClassName?: string;
    browser?: ReduxResponsive;
    canSelect?: boolean;
    contextNode?: NodePropertyType;
    contextType?: ContextType;
    focusPosition: FocusPosition | null;
    headerLabel?: NestedField<any, FieldKey.Label>;
    hideValidationSummary?: boolean;
    info?: string;
    isCloseIconDisplayed?: boolean;
    isDisabled?: boolean;
    isHidden?: ValueOrCallbackWithFieldValue<any, boolean>;
    isReadOnly?: boolean;
    isSelected?: boolean;
    isTitleHidden?: ValueOrCallbackWithFieldValue<any, boolean>;
    item: Partial<PageArticleItem>;
    level?: number;
    nestedFields: NestedField<ScreenBase, NestedFieldTypes>[];
    nodeTypes?: Dict<FormattedNodeDetails>;
    noMargin?: boolean;
    onActionPopoverOpen?: () => void;
    onBlockClick?: () => void;
    onBlockRemoved?: () => void;
    onBlockSelectionChange?: (isSelected: boolean) => void;
    onChange?: (bind: string, value: any) => Promise<void>;
    parentElementId: string;
    readOnlyOverride?: (this: ScreenBase, elementId: string) => boolean | void;
    recordValue?: ClientNode;
    screenId: string;
    title?: NestedField<ScreenBase, NestedFieldTypes> | ValueOrCallbackWithFieldValue<any, string>;
    validate?: (columnName: string, value: any) => Promise<ValidationResult[]>;
    validationErrors?: ValidationResult[];
    warning?: string;
}

export interface ConnectedNestedBlockProps extends NestedBlockProps {
    recordId: string;
    value?: CollectionValue;
}

export interface NestedBlockExternalProps extends ConnectedNestedBlockProps {
    screenType: string;
}

export class NestedBlock extends React.Component<NestedBlockProps> {
    private readonly onChange = async (bind: string, value: any): Promise<void> => {
        if (this.props.onChange) {
            await this.props.onChange(bind, value);
        }
    };

    private readonly renderField = (
        nestedField: NestedField<ScreenBase, NestedFieldTypesWithoutTechnical>,
    ): React.ReactNode => {
        const { properties, type } = nestedField;
        const columnProperties = {
            isReadOnly: undefined,
            ...properties,
        };

        const columnSpan = this.props.browser
            ? calculateFieldWidth(
                  this.props.browser?.is,
                  type,
                  this.props.availableColumns,
                  columnProperties.isFullWidth,
                  columnProperties.width,
              )
            : 2;

        columnProperties.isDisabled = this.props.isDisabled || columnProperties.isDisabled;
        const readOnlyOverride = resolveByValue({
            screenId: this.props.screenId,
            propertyValue: this.props.readOnlyOverride,
            fieldValue: columnProperties.bind,
            skipHexFormat: true,
            rowValue: null,
        });

        columnProperties.isReadOnly = readOnlyOverride === undefined ? columnProperties.isReadOnly : readOnlyOverride;

        const gridColumnClasses = ['e-field-grid-column'];
        const mergedRowValue = splitValueToMergedValue(this.props.recordValue || {});
        const bind = normalizeUnderscoreBind(getNestedFieldElementId(nestedField));
        let value = get(mergedRowValue, convertDeepBindToPathNotNull(bind));
        if (isNil(value) && this.props.contextNode && this.props.nodeTypes) {
            const { valuePath, jsonSelector } = getBindAndSelectorFromDeepBinding(
                bind,
                String(this.props.contextNode),
                this.props.nodeTypes,
            );

            const rawFieldValue = get(mergedRowValue, valuePath, null);
            if (rawFieldValue && jsonSelector) {
                let parsedJsonField;
                if (isObject(rawFieldValue)) {
                    parsedJsonField = rawFieldValue;
                } else if (isString(rawFieldValue)) {
                    parsedJsonField = JSON.parse(rawFieldValue);
                } else {
                    parsedJsonField = null;
                }

                if (parsedJsonField) {
                    value = get(parsedJsonField, jsonSelector);
                }
            }
        }

        if (
            resolveByValue({
                propertyValue: columnProperties.isHidden,
                rowValue: mergedRowValue,
                fieldValue: value,
                skipHexFormat: true,
                screenId: this.props.screenId,
            })
        ) {
            gridColumnClasses.push('e-field-grid-column-hidden');
        }
        const { screenId, contextType, parentElementId } = this.props;
        const key = generateFieldId({
            screenId,
            elementId: convertDeepBindToPathNotNull(nestedField.properties.bind),
            contextType,
            fieldProperties: nestedField.properties,
            parentElementId,
            isNested: true,
        });

        const validationErrors = (this.props.validationErrors || []).filter(e => e.columnId === bind) || [];

        const getContextType = (): ContextType | undefined => {
            // INFO: Currently NestedBlock is only used for pod components. If in future the
            //       NestedBlock is also to be used for non-pod components, this function should be
            //       used to figure out the correct context type, based on the parent component.
            return ContextType.pod;
        };

        return (
            <GridColumn className={gridColumnClasses.join(' ')} columnSpan={columnSpan} key={key}>
                <ConnectedNestedFieldWrapper
                    _id={this.props.recordValue ? this.props.recordValue._id : ''}
                    columnDefinition={nestedField}
                    columnName={bind}
                    columnProperties={columnProperties}
                    contextNode={this.props.contextNode}
                    contextType={getContextType()}
                    focusPosition={this.props.focusPosition}
                    handlersArguments={{
                        rowValue: mergedRowValue,
                        onChange: [this.props.recordValue?._id, mergedRowValue],
                        onClick: [this.props.recordValue?._id, mergedRowValue],
                    }}
                    nestedReadOnlyField={this.props.isReadOnly}
                    parentElementId={this.props.parentElementId}
                    recordContext={mergedRowValue}
                    screenId={this.props.screenId}
                    setFieldValue={this.onChange}
                    shouldRenderLabelInNestedReadOnlyMode={this.props.isReadOnly}
                    value={value}
                    validationErrors={validationErrors}
                    validate={this.props.validate}
                    isParentReadOnly={this.props.isReadOnly}
                />
            </GridColumn>
        );
    };

    private readonly resolveTitle = (): string | null =>
        !isNestedField(this.props.title)
            ? resolveByValue<string>({
                  screenId: this.props.screenId,
                  propertyValue: this.props.title,
                  skipHexFormat: true,
                  rowValue: this.props.recordValue ? splitValueToMergedValue(this.props.recordValue) : undefined,
              })
            : null;

    private readonly getStatusIcon = (
        hideValidationSummary: boolean,
        errors: ValidationResult[],
        nestedFields: NestedBlockProps['nestedFields'],
        screenId: string,
        recordValue: NestedBlockProps['recordValue'],
        warningMessage?: string,
        infoMessage?: string,
    ): JSX.Element | null => {
        if (errors && errors.length > 0 && !hideValidationSummary) {
            const message = errors
                .map(e => {
                    const nestedField = withoutNestedTechnical(nestedFields).find(
                        n => getNestedFieldElementId(n) === e.columnId,
                    );
                    const title =
                        resolveByValue({
                            screenId,
                            propertyValue: nestedField?.properties.title,
                            skipHexFormat: true,
                            rowValue: recordValue ? splitValueToMergedValue(recordValue) : undefined,
                        }) || e.columnId;
                    return title ? `${title}: ${e.message}` : e.message;
                })
                .join('\n');
            const props: any = {
                error: message,
            };
            // CARBON-UPGRADE The context provider should be removed once carbon updates to React 17
            return (
                // <InputContext.Provider value={useInputBehaviour(true)}>
                <ValidationIcon {...props} />
                // </InputContext.Provider>
            );
        }

        if (warningMessage) {
            return (
                <Icon
                    key="warning"
                    className="e-icon-warning-message"
                    ariaLabel={warningMessage}
                    fontSize="small"
                    color={tokens.colorsSemanticCaution500}
                    type="warning"
                    role="tooltip"
                    tooltipMessage={warningMessage}
                />
            );
        }

        if (infoMessage) {
            return (
                <Icon
                    key="info"
                    className="e-icon-info-message"
                    ariaLabel={infoMessage}
                    fontSize="small"
                    color={tokens.colorsSemanticInfo500}
                    type="info"
                    role="tooltip"
                    tooltipMessage={infoMessage}
                />
            );
        }

        return null;
    };

    private renderTitle(): JSX.Element {
        const title = this.props.title;

        const rowValue = this.props.recordValue;
        if (isNestedField(this.props.title) && rowValue) {
            const nestedFieldTitle = title as NestedField<any, Exclude<NestedFieldTypes, FieldKey.Technical>>;
            return (
                <ConnectedNestedFieldWrapper
                    _id={rowValue._id}
                    columnName={convertDeepBindToPathNotNull(nestedFieldTitle.properties.bind)}
                    columnProperties={nestedFieldTitle.properties}
                    columnDefinition={nestedFieldTitle}
                    value={get(rowValue, getNestedFieldElementId(nestedFieldTitle))}
                    contextType={this.props.contextType}
                    contextNode={this.props.contextNode}
                    parentElementId={this.props.parentElementId}
                    screenId={this.props.screenId}
                    handlersArguments={{ rowValue: splitValueToMergedValue(rowValue) }}
                    setFieldValue={this.onChange}
                    nestedReadOnlyField={true}
                />
            );
        }

        return <span className={`e-${this.props.baseClassName}-title-text`}>{this.resolveTitle()}</span>;
    }

    private readonly renderHeader = (baseClassName: string): JSX.Element => (
        <div className={`e-${baseClassName}-header`}>
            <h3 className={`e-${baseClassName}-title`} data-testid={`e-${baseClassName}-title`}>
                {this.props.canSelect && (
                    <span className={`e-${baseClassName}-select`} data-testid={`e-${baseClassName}-select`}>
                        <Checkbox
                            checked={this.props.isSelected}
                            value="checked"
                            disabled={this.props.isDisabled}
                            onChange={(event: React.ChangeEvent<HTMLInputElement>): void => {
                                if (this.props.onBlockSelectionChange) {
                                    this.props.onBlockSelectionChange(event.target.checked);
                                }
                            }}
                        />
                    </span>
                )}
                {this.renderTitle()}
                {this.getStatusIcon(
                    !!this.props.hideValidationSummary,
                    this.props.validationErrors || [],
                    this.props.nestedFields,
                    this.props.screenId,
                    this.props.recordValue,
                    this.props.warning,
                    this.props.info,
                )}
                {this.props.headerLabel &&
                    this.props.recordValue &&
                    get(this.props.recordValue, getNestedFieldElementId(this.props.headerLabel)) && (
                        <div className={`e-${baseClassName}-header-label`}>
                            <ConnectedNestedFieldWrapper
                                _id={this.props.recordValue._id}
                                columnName={convertDeepBindToPathNotNull(this.props.headerLabel.properties.bind)}
                                columnProperties={this.props.headerLabel.properties}
                                columnDefinition={this.props.headerLabel}
                                value={get(this.props.recordValue, getNestedFieldElementId(this.props.headerLabel))}
                                contextType={this.props.contextType}
                                contextNode={this.props.contextNode}
                                parentElementId={this.props.parentElementId}
                                screenId={this.props.screenId}
                                handlersArguments={{ rowValue: splitValueToMergedValue(this.props.recordValue || {}) }}
                                setFieldValue={this.onChange}
                                nestedReadOnlyField={true}
                            />
                        </div>
                    )}
                <span className="e-pod-header-spacer" />
                {this.props.actionPopoverItems && (
                    <XtremActionPopover
                        items={this.props.actionPopoverItems}
                        onOpen={this.props.onActionPopoverOpen}
                        noIconSupport={true}
                    />
                )}
                {this.props.isCloseIconDisplayed && (
                    <IconButton
                        disabled={this.props.isDisabled}
                        onClick={this.props.onBlockRemoved}
                        data-testid={`e-${baseClassName}-close`}
                        aria-label={localize('@sage/xtrem-ui/pod-remove-item', 'Remove Item')}
                    >
                        <Icon type="cross" />
                    </IconButton>
                )}
            </h3>
        </div>
    );

    render(): React.ReactNode {
        const { screenId, browser, nestedFields, isCloseIconDisplayed, onBlockClick, canSelect } = this.props;
        const baseClassName = this.props.baseClassName || 'block';
        const isTitleHidden = resolveByValue({
            screenId,
            propertyValue: this.props.isTitleHidden,
            skipHexFormat: true,
            rowValue: splitValueToMergedValue(this.props.recordValue || {}),
        });
        const isHidden = resolveByValue({
            screenId,
            propertyValue: this.props.isHidden,
            skipHexFormat: true,
            rowValue: splitValueToMergedValue(this.props.recordValue || {}),
        });

        const shouldRenderHeader =
            (this.props.title && !isTitleHidden) ||
            isCloseIconDisplayed ||
            canSelect ||
            (this.props.actionPopoverItems && this.props.actionPopoverItems.length > 0);
        const shouldRenderBody = !!this.props.recordValue;
        const computedWidth = browser
            ? calculateContainerWidth(browser.is, this.props.availableColumns)
            : this.props.availableColumns;
        const gridGutter = browser ? getGutterSize(browser.is) : 24;
        const gridVerticalMargin = this.props.contextType === ContextType.page && !this.props.noMargin ? 16 : 0;
        const classes = ['e-container-parent', `e-${baseClassName}-parent`];

        if (isElementHidden(this.props.item, this.props.browser) || isHidden) {
            classes.push('e-hidden');
        }

        if (this.props.contextType) {
            classes.push(`e-${baseClassName}-context-${this.props.contextType}`);
        }

        const testIds: string[] = [
            getDataTestIdAttribute(baseClassName, this.resolveTitle() || 'unknown', this.props.item.$containerId),
        ];

        if (this.props.additionalTestId) {
            testIds.push(this.props.additionalTestId);
        }

        return (
            <GridColumn className={classes.join(' ')} columnSpan={computedWidth}>
                <div className={`e-${baseClassName}`} onClick={onBlockClick} data-testid={testIds.join(' ')}>
                    {shouldRenderHeader && this.renderHeader(baseClassName)}
                    <div className={`e-${baseClassName}-body`}>
                        <GridRow
                            columns={computedWidth}
                            gutter={gridGutter}
                            margin={this.props.noMargin ? 0 : 16}
                            verticalMargin={gridVerticalMargin}
                        >
                            {shouldRenderBody && withoutNestedTechnical(nestedFields).map(this.renderField)}
                        </GridRow>
                    </div>
                </div>
            </GridColumn>
        );
    }
}
const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: ConnectedNestedBlockProps,
): NestedBlockExternalProps => {
    const value = state.screenDefinitions[props.screenId].values[props.parentElementId];
    return {
        ...props,
        value,
        screenType: state.screenDefinitions[props.screenId].type,
        browser: state.browser,
        nodeTypes: state.nodeTypes,
        focusPosition: state.focusPosition,
    };
};

export const ConnectedNestedBlock = connect(mapStateToProps)(withCollectionValueItemSubscription(NestedBlock));
