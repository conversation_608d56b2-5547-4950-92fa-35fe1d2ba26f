// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Xtrem action popover snapshots connected should not render if no actions are provided 1`] = `<div />`;

exports[`Xtrem action popover snapshots connected should render on desktop 1`] = `
<div>
  <div
    data-testid="mockActionPopover"
  >
    <div
      aria-disabled="false"
      data-testid="mockActionPopoverItem"
    >
      <span>
        Action 1
      </span>
    </div>
    <div
      aria-disabled="true"
      data-testid="mockActionPopoverItem"
    >
      <span>
        Action 2
      </span>
    </div>
    <div
      data-testid="mockActionPopoverItem"
    >
      <span>
        Action 3
      </span>
    </div>
  </div>
</div>
`;

exports[`Xtrem action popover snapshots connected should render on mobile 1`] = `
.c2 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c2::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e961";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c0.c0 {
  padding: var(--spacing000);
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  cursor: pointer;
}

.c0::-moz-focus-inner {
  border: none;
}

.c0 .c1 {
  position: relative;
}

.c0 .c1:focus {
  border: none;
}

<div>
  <div
    class="e-action-popover-mobile"
  >
    <div
      class="e-action-popover-mobile-button"
      data-testid="e-action-popover-mobile-button"
    >
      <button
        aria-label="ellipsis_vertical"
        class="c0"
        data-component="action-popover-button"
        data-element="action-popover-button"
        type="button"
      >
        <span
          class="c1 c2"
          data-component="icon"
          data-element="ellipsis_vertical"
          data-role="icon"
          font-size="small"
          type="ellipsis_vertical"
        />
      </button>
    </div>
  </div>
</div>
`;

exports[`Xtrem action popover snapshots unconnected should not render if no actions are provided 1`] = `<div />`;

exports[`Xtrem action popover snapshots unconnected should render on desktop 1`] = `
<div>
  <div
    data-testid="mockActionPopover"
  >
    <div
      aria-disabled="false"
      data-testid="mockActionPopoverItem"
    >
      <span>
        Action 1
      </span>
    </div>
    <div
      aria-disabled="true"
      data-testid="mockActionPopoverItem"
    >
      <span>
        Action 2
      </span>
    </div>
    <div
      data-testid="mockActionPopoverItem"
    >
      <span>
        Action 3
      </span>
    </div>
  </div>
</div>
`;

exports[`Xtrem action popover snapshots unconnected should render on mobile 1`] = `
.c2 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c2::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e961";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c0.c0 {
  padding: var(--spacing000);
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  cursor: pointer;
}

.c0::-moz-focus-inner {
  border: none;
}

.c0 .c1 {
  position: relative;
}

.c0 .c1:focus {
  border: none;
}

<div>
  <div
    class="e-action-popover-mobile"
  >
    <div
      class="e-action-popover-mobile-button"
      data-testid="e-action-popover-mobile-button"
    >
      <button
        aria-label="ellipsis_vertical"
        class="c0"
        data-component="action-popover-button"
        data-element="action-popover-button"
        type="button"
      >
        <span
          class="c1 c2"
          data-component="icon"
          data-element="ellipsis_vertical"
          data-role="icon"
          font-size="small"
          type="ellipsis_vertical"
        />
      </button>
    </div>
  </div>
</div>
`;
