// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Portrait wrapper should render initials when invalid image value supplied 1`] = `
.c1 {
  font-weight: 500;
  font-size: var(--fontSizes100);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  white-space: nowrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: inherit;
  width: inherit;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  min-width: 32px;
  height: 32px;
  overflow: hidden;
  border-radius: var(--borderRadiusCircle);
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div>
  <div
    class="e-portrait"
  >
    <div
      class="c0"
      data-component="portrait"
      shape="circle"
    >
      <div
        class="c1"
        data-element="initials"
      >
        TIT
      </div>
    </div>
  </div>
</div>
`;

exports[`Portrait wrapper should render initials when no image supplied 1`] = `
.c1 {
  font-weight: 500;
  font-size: var(--fontSizes100);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  white-space: nowrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: inherit;
  width: inherit;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  min-width: 32px;
  height: 32px;
  overflow: hidden;
  border-radius: var(--borderRadiusCircle);
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div>
  <div
    class="e-portrait"
  >
    <div
      class="c0"
      data-component="portrait"
      shape="circle"
    >
      <div
        class="c1"
        data-element="initials"
      >
        TIT
      </div>
    </div>
  </div>
</div>
`;

exports[`Portrait wrapper should render placeholder icon when neither image nor initials are supplied 1`] = `
.c1 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c1::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e93c";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c2.c2 {
  color: inherit;
  height: inherit;
  min-width: inherit;
}

.c2.c2::before {
  font-size: 20px;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  min-width: 32px;
  height: 32px;
  overflow: hidden;
  border-radius: var(--borderRadiusCircle);
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div>
  <div
    class="e-portrait"
  >
    <div
      class="c0"
      data-component="portrait"
      shape="circle"
    >
      <span
        class="c1 c2"
        data-component="icon"
        data-element="individual"
        data-role="icon"
        font-size="small"
        type="individual"
      />
    </div>
  </div>
</div>
`;

exports[`Portrait wrapper should render with image value 1`] = `
.c1 {
  height: inherit;
  min-width: inherit;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  max-width: 32px;
  min-width: 32px;
  height: 32px;
  overflow: hidden;
  border-radius: 0px;
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div>
  <div
    class="e-portrait"
  >
    <div
      class="c0"
      data-component="portrait"
      shape="square"
    >
      <img
        alt="This is test image"
        class="c1"
        data-element="user-image"
        src="data:image;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg=="
      />
    </div>
  </div>
</div>
`;
