import * as React from 'react';
import type { XtremActionPopoverItem, XtremActionPopoverItemOrMenuSeparator } from '../xtrem-action-popover';
import ConnectedXtremActionPopover, { XtremActionPopover } from '../xtrem-action-popover';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../redux';
import { getMockState, getMockStore } from '../../../__tests__/test-helpers';
import { Provider } from 'react-redux';
import { fireEvent } from '@testing-library/dom';
import { render } from '@testing-library/react';

/* eslint-disable react/display-name, react/no-children-prop */
jest.mock('carbon-react/esm/components/action-popover', () => ({
    ActionPopover: (props: any) => (
        <div key="mockActionPopover" data-testid="mockActionPopover" onClick={props.onOpen} children={props.children} />
    ),
    ActionPopoverItem: (props: any) => (
        <div
            data-testid="mockActionPopoverItem"
            defaultValue={props.icon}
            key={props.key}
            onClick={props.onClick}
            aria-disabled={props.disabled}
        >
            {props.children}
        </div>
    ),
    ActionPopoverDivider: () => <div key="mockActionPopoverDivider" data-testid="mockActionPopoverDivider" />,
}));

/* eslint-enable react/display-name, react/no-children-prop */
describe('Xtrem action popover', () => {
    let actionItems: XtremActionPopoverItem[] = [];
    let actionItemsWithSeparator: Array<XtremActionPopoverItemOrMenuSeparator> = [];
    let singleActionItem: XtremActionPopoverItem[] = [];
    let singleActionItemWithNoIcon: XtremActionPopoverItem[] = [];
    let singleActionItemWithIconNone: XtremActionPopoverItem[] = [];
    let singleActionItemDisabled: XtremActionPopoverItem[] = [];
    let actionItemsWithSeparatorFirst: Array<XtremActionPopoverItemOrMenuSeparator> = [];
    let actionItemsWithSeparatorConditionallyFirst: Array<XtremActionPopoverItemOrMenuSeparator> = [];
    let actionItemsWithSeparatorLast: Array<XtremActionPopoverItemOrMenuSeparator> = [];
    let actionItemsWithSeparatorAndHiddenElements: Array<XtremActionPopoverItemOrMenuSeparator> = [];
    let actionItemsWithEmptyGroups: Array<XtremActionPopoverItemOrMenuSeparator> = [];
    let actionItemsWithMultipleSeperators: Array<XtremActionPopoverItemOrMenuSeparator> = [];

    beforeEach(() => {
        actionItems = [
            {
                onClick: jest.fn(),
                title: 'Action 1',
                icon: 'search',
                isDisabled: false,
                key: 'action1',
            },
            {
                onClick: jest.fn(),
                title: 'Action 2',
                icon: 'gift',
                isDisabled: true,
                key: 'action2',
            },
            {
                onClick: jest.fn(),
                title: 'Action 3',
                icon: 'gift',
                key: 'action3',
            },
        ];
        actionItemsWithSeparator = [
            {
                onClick: jest.fn(),
                title: 'Action 1',
                icon: 'search',
                isDisabled: false,
                key: 'action1',
            },
            {
                onClick: jest.fn(),
                title: 'Action 2',
                icon: 'gift',
                isDisabled: true,
                key: 'action2',
            },
            {
                isMenuSeparator: true,
                key: 'menu_separator_1',
            },
            {
                onClick: jest.fn(),
                title: 'Action 3',
                icon: 'gift',
                key: 'action3',
            },
        ];
        actionItemsWithSeparatorFirst = [
            {
                isMenuSeparator: true,
                key: 'menu_separator_1',
            },
            {
                onClick: jest.fn(),
                title: 'Action 1',
                icon: 'search',
                isDisabled: false,
                key: 'action1',
            },
            {
                onClick: jest.fn(),
                title: 'Action 2',
                icon: 'gift',
                isDisabled: true,
                key: 'action2',
            },
            {
                onClick: jest.fn(),
                title: 'Action 3',
                icon: 'gift',
                key: 'action3',
            },
        ];
        actionItemsWithSeparatorLast = [
            {
                onClick: jest.fn(),
                title: 'Action 1',
                icon: 'search',
                isDisabled: false,
                key: 'action1',
            },
            {
                onClick: jest.fn(),
                title: 'Action 2',
                icon: 'gift',
                isDisabled: true,
                key: 'action2',
            },
            {
                onClick: jest.fn(),
                title: 'Action 3',
                icon: 'gift',
                key: 'action3',
            },
            {
                isMenuSeparator: true,
                key: 'menu_separator_1',
            },
        ];
        actionItemsWithSeparatorConditionallyFirst = [
            {
                onClick: jest.fn(),
                title: 'Action 1',
                icon: 'search',
                isHidden: true,
                key: 'action1',
            },
            {
                isMenuSeparator: true,
                key: 'menu_separator_1',
            },
            {
                onClick: jest.fn(),
                title: 'Action 2',
                icon: 'gift',
                isDisabled: true,
                key: 'action2',
            },
            {
                onClick: jest.fn(),
                title: 'Action 3',
                icon: 'gift',
                key: 'action3',
            },
        ];
        actionItemsWithSeparatorAndHiddenElements = [
            {
                onClick: jest.fn(),
                title: 'Action 1',
                icon: 'search',
                isHidden: true,
                key: 'action1',
            },
            {
                isMenuSeparator: true,
                key: 'menu_separator_1',
            },
            {
                onClick: jest.fn(),
                title: 'Action 2',
                icon: 'gift',
                key: 'action2',
            },
        ];
        singleActionItem = [
            {
                onClick: jest.fn(),
                title: 'Action 1',
                icon: 'search',
                isDisabled: false,
                key: 'action1',
            },
        ];
        singleActionItemDisabled = [
            {
                onClick: jest.fn(),
                title: 'Action 1',
                icon: 'search',
                isDisabled: true,
                key: 'action1',
            },
        ];
        singleActionItemWithIconNone = [
            {
                onClick: jest.fn(),
                title: 'Action 1',
                icon: 'none',
                isDisabled: false,
                key: 'action1',
            },
        ];
        singleActionItemWithNoIcon = [
            {
                onClick: jest.fn(),
                title: 'Action 1',
                isDisabled: false,
                key: 'action1',
            },
        ];
        actionItemsWithEmptyGroups = [
            {
                key: 'action1',
                onClick: jest.fn(),
                title: 'Action 1',
            },
            {
                key: 'action2',
                onClick: jest.fn(),
                title: 'Action 2',
            },
            {
                isMenuSeparator: true,
                key: 'menu_separator_1',
            },
            {
                key: 'action3',
                onClick: jest.fn(),
                title: 'Action 3',
                isHidden: true,
            },
            {
                key: 'action4',
                onClick: jest.fn(),
                title: 'Action 4',
                isHidden: true,
            },
            {
                isMenuSeparator: true,
                key: 'menu_separator_2',
            },
            {
                key: 'action5',
                onClick: jest.fn(),
                title: 'Action 5',
            },
        ];
        actionItemsWithMultipleSeperators = [
            {
                key: 'action1',
                onClick: jest.fn(),
                title: 'Action 1',
            },
            {
                key: 'action2',
                onClick: jest.fn(),
                title: 'Action 2',
            },
            {
                isMenuSeparator: true,
                key: 'menu_separator_1',
            },
            {
                isMenuSeparator: true,
                key: 'menu_separator_2',
            },
        ];
    });

    describe('snapshots', () => {
        describe('unconnected', () => {
            it('should not render if no actions are provided', () => {
                const wrapper = render(<XtremActionPopover items={[]} isDeviceLessThanSmall={false} />);
                expect(wrapper.container).toMatchSnapshot();
            });

            it('should render on desktop', () => {
                const wrapper = render(<XtremActionPopover items={actionItems} isDeviceLessThanSmall={false} />);
                expect(wrapper.container).toMatchSnapshot();
            });

            it('should render on mobile', () => {
                const wrapper = render(<XtremActionPopover items={actionItems} isDeviceLessThanSmall={true} />);
                expect(wrapper.container).toMatchSnapshot();
            });
        });

        describe('connected', () => {
            let mockStore: MockStoreEnhanced<XtremAppState>;
            let state: XtremAppState;
            beforeEach(() => {
                state = getMockState();
                mockStore = getMockStore(state);
            });

            it('should not render if no actions are provided', () => {
                state.browser.lessThan.s = false;
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedXtremActionPopover items={[]} />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();
            });

            it('should render on desktop', () => {
                state.browser.lessThan.s = false;
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedXtremActionPopover items={actionItems} />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();
            });

            it('should render on desktop with a separator', () => {
                state.browser.lessThan.s = false;
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedXtremActionPopover items={actionItemsWithSeparator} />
                    </Provider>,
                );
                expect(wrapper.queryAllByTestId('mockActionPopoverItem')).toHaveLength(3);
                expect(wrapper.queryAllByTestId('mockActionPopoverDivider')).toHaveLength(1);
            });

            it('should render not render the separator element if it is the first in the list', () => {
                state.browser.lessThan.s = false;
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedXtremActionPopover items={actionItemsWithSeparatorFirst} />
                    </Provider>,
                );
                expect(wrapper.queryAllByTestId('mockActionPopoverItem')).toHaveLength(3);
                expect(wrapper.queryAllByTestId('mockActionPopoverDivider')).toHaveLength(0);
            });

            it('should render not render the separator element if it is the first in the list by having another one hidden in front of it', () => {
                state.browser.lessThan.s = false;
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedXtremActionPopover items={actionItemsWithSeparatorConditionallyFirst} />
                    </Provider>,
                );
                expect(wrapper.queryAllByTestId('mockActionPopoverItem')).toHaveLength(2);
                expect(wrapper.queryAllByTestId('mockActionPopoverDivider')).toHaveLength(0);
            });

            it('should render not render the separator element if it is the last in the list', () => {
                state.browser.lessThan.s = false;
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedXtremActionPopover items={actionItemsWithSeparatorLast} />
                    </Provider>,
                );
                expect(wrapper.queryAllByTestId('mockActionPopoverItem')).toHaveLength(3);
                expect(wrapper.queryAllByTestId('mockActionPopoverDivider')).toHaveLength(0);
            });

            it('should render on mobile', () => {
                state.browser.lessThan.s = true;
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedXtremActionPopover items={actionItems} />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();
            });
        });
    });

    describe('interactions', () => {
        it('should display only one dropdown action button if there is only one dropdown action item on desktop', () => {
            const wrapper = render(<XtremActionPopover items={singleActionItem} isDeviceLessThanSmall={false} />);
            expect(wrapper.container.querySelectorAll('button')).toHaveLength(1);
        });

        it('should display an icon button if the element list consists of separators, hidden items and one visible item', () => {
            const wrapper = render(
                <XtremActionPopover items={actionItemsWithSeparatorAndHiddenElements} isDeviceLessThanSmall={false} />,
            );
            expect(wrapper.container.querySelectorAll('button')).toHaveLength(1);
        });

        it('should display only one dropdown action button if there is only one dropdown action item on mobile', () => {
            const wrapper = render(<XtremActionPopover items={singleActionItem} isDeviceLessThanSmall={true} />);
            expect(wrapper.container.querySelectorAll('button')).toHaveLength(1);
        });

        it('should not display anything if the only single item is hidden on desktop', () => {
            singleActionItem[0].isHidden = true;
            const wrapper = render(<XtremActionPopover items={singleActionItem} isDeviceLessThanSmall={false} />);
            expect(wrapper.container.childElementCount).toEqual(0);
        });

        it('should not display anything if the only single item is hidden on mobile', () => {
            singleActionItem[0].isHidden = true;
            const wrapper = render(<XtremActionPopover items={singleActionItem} isDeviceLessThanSmall={true} />);
            expect(wrapper.container.childElementCount).toEqual(0);
        });

        it('should not display anything if the only single item is hidden on desktop', () => {
            actionItems[0].isHidden = true;
            actionItems[1].isHidden = true;
            actionItems[2].isHidden = true;
            const wrapper = render(<XtremActionPopover items={actionItems} isDeviceLessThanSmall={false} />);
            expect(wrapper.container.childElementCount).toEqual(0);
        });

        it('should not display anything if the only single item is hidden on mobile', () => {
            actionItems[0].isHidden = true;
            actionItems[1].isHidden = true;
            actionItems[2].isHidden = true;
            const wrapper = render(<XtremActionPopover items={actionItems} isDeviceLessThanSmall={true} />);
            expect(wrapper.container.childElementCount).toEqual(0);
        });

        it('should not trigger any action if single dropdown action button is disabled', () => {
            const wrapper = render(
                <XtremActionPopover items={singleActionItemDisabled} isDeviceLessThanSmall={false} />,
            );
            const action = wrapper.container.querySelectorAll('button')[0];

            fireEvent.click(wrapper.container.querySelector('button')!);

            expect(singleActionItemDisabled[0].onClick).not.toHaveBeenCalled();
            expect(action.attributes.getNamedItem('disabled')).not.toBeNull();
        });

        it('should not trigger any action if the popover is disabled', () => {
            const wrapper = render(
                <XtremActionPopover items={singleActionItem} isDeviceLessThanSmall={false} isDisabled={true} />,
            );
            const action = wrapper.container.querySelectorAll('button')[0];

            fireEvent.click(wrapper.container.querySelector('button')!);

            expect(singleActionItem[0].onClick).not.toHaveBeenCalled();
            expect(action.attributes.getNamedItem('disabled')).not.toBeNull();
        });

        it('should trigger an action if there is only one action with no icon', () => {
            const wrapper = render(
                <XtremActionPopover items={singleActionItemWithNoIcon} isDeviceLessThanSmall={false} />,
            );
            expect(wrapper.queryAllByTestId('mockActionPopover')).toHaveLength(1);
            expect(wrapper.queryAllByTestId('mockActionPopoverItem')).toHaveLength(1);
            const action = wrapper.queryAllByTestId('mockActionPopoverItem')[0];
            fireEvent.click(action);
            expect(singleActionItemWithNoIcon[0].onClick).toHaveBeenCalled();
        });

        it('should trigger an action if there is only one action with property icon set to none', () => {
            const wrapper = render(
                <XtremActionPopover items={singleActionItemWithIconNone} isDeviceLessThanSmall={false} />,
            );
            expect(wrapper.queryAllByTestId('mockActionPopover')).toHaveLength(1);
            expect(wrapper.queryAllByTestId('mockActionPopoverItem')).toHaveLength(1);
            const action = wrapper.queryAllByTestId('mockActionPopoverItem')[0];
            fireEvent.click(action);
            expect(singleActionItemWithIconNone[0].onClick).toHaveBeenCalled();
        });

        it('should open menu on click on desktop', () => {
            const wrapper = render(<XtremActionPopover items={actionItems} isDeviceLessThanSmall={false} />);
            expect(wrapper.queryAllByTestId('mockActionPopoverItem')).toHaveLength(3);

            const action0 = wrapper.queryAllByTestId('mockActionPopoverItem')[0];
            expect(actionItems[0].onClick).not.toHaveBeenCalled();
            expect(action0).toHaveTextContent('Action 1');
            fireEvent.click(action0);
            expect(actionItems[0].onClick).toHaveBeenCalled();

            const action1 = wrapper.queryAllByTestId('mockActionPopoverItem')[1];
            expect(actionItems[1].onClick).not.toHaveBeenCalled();
            expect(action1).toHaveTextContent('Action 2');
            fireEvent.click(action1);
            expect(actionItems[1].onClick).toHaveBeenCalled();
        });

        it('should set correctly disabled prop', () => {
            const wrapper = render(<XtremActionPopover items={actionItems} isDeviceLessThanSmall={false} />);
            expect(wrapper.queryAllByTestId('mockActionPopoverItem')).toHaveLength(3);

            const action2 = wrapper.queryAllByTestId('mockActionPopoverItem')[1];
            expect(actionItems[1].onClick).not.toHaveBeenCalled();
            expect(action2).toHaveTextContent('Action 2');
            expect(action2.attributes.getNamedItem('aria-disabled')).not.toBeNull();
        });

        it('should not render menu consecutive menu seperators ', () => {
            const wrapper = render(
                <XtremActionPopover items={actionItemsWithEmptyGroups} isDeviceLessThanSmall={false} />,
            );

            const children = wrapper.queryByTestId('mockActionPopover')?.children as HTMLCollection;
            expect(children).toHaveLength(4);

            const action1 = children[0];
            expect(action1.getAttribute('data-testid')).toEqual('mockActionPopoverItem');
            expect(action1).toHaveTextContent('Action 1');

            const action2 = children[1];
            expect(action2.getAttribute('data-testid')).toEqual('mockActionPopoverItem');
            expect(action2).toHaveTextContent('Action 2');

            const divider = children[2];
            expect(divider.getAttribute('data-testid')).toEqual('mockActionPopoverDivider');

            const action5 = children[3];
            expect(action5.getAttribute('data-testid')).toEqual('mockActionPopoverItem');
            expect(action5).toHaveTextContent('Action 5');
        });

        it('should not render any menu seperators at the end of the bottom', () => {
            const wrapper = render(
                <XtremActionPopover items={actionItemsWithMultipleSeperators} isDeviceLessThanSmall={false} />,
            );

            const children = wrapper.queryByTestId('mockActionPopover')?.children as HTMLCollection;
            expect(children).toHaveLength(2);

            const action1 = children[0];
            expect(action1.getAttribute('data-testid')).toEqual('mockActionPopoverItem');
            expect(action1).toHaveTextContent('Action 1');

            const action2 = children[1];
            expect(action2.getAttribute('data-testid')).toEqual('mockActionPopoverItem');
            expect(action2).toHaveTextContent('Action 2');
        });
    });
});
