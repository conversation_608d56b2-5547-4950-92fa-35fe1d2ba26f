import { render } from '@testing-library/react';
import 'jest-styled-components';
import * as React from 'react';
import { Portrait } from '../portrait-component';

const imageValue =
    'iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==';

/**
 * This component can only be tested with snapshots as the initials are rendered to a base64 image.
 *  */

describe('Portrait wrapper', () => {
    it('should render with image value', () => {
        const { container } = render(
            <Portrait
                image={{
                    value: imageValue,
                }}
                placeholderValue="This is test image"
                size="S"
            />,
        );

        expect(container).toMatchSnapshot();
    });

    it('should render initials when invalid image value supplied', () => {
        const { container } = render(
            <Portrait
                image={
                    {
                        malformatedObject: 'asdasd',
                    } as any
                }
                placeholderValue="This is test image"
                size="S"
            />,
        );
        expect(container).toMatchSnapshot();
    });

    it('should render initials when no image supplied', () => {
        const { container } = render(<Portrait placeholderValue="This is test image" size="S" />);
        expect(container).toMatchSnapshot();
    });

    it('should render placeholder icon when neither image nor initials are supplied', () => {
        const { container } = render(<Portrait size="S" />);
        expect(container).toMatchSnapshot();
    });
});
