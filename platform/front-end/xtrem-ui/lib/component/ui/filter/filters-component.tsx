import Pill from 'carbon-react/esm/components/pill';
import * as React from 'react';
import type { Filter } from '../../../service/filter-service';
import { formatDateToCurrentLocale, localize, localizeEnumMember } from '../../../service/i18n-service';
import type { FilterManagerField } from './filter-manager';
import { FilterManager, getFilterDisplayName } from './filter-manager';
import type { FilterModel } from '../../field/table/table-component-types';
import { RANGE, RANGE_DIVIDER, SET } from '../../types';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import { cloneDeep } from 'lodash';
import { objectKeys } from '@sage/xtrem-shared';

export interface FilterCommonProps {
    fields: FilterManagerField[];
    filters: Filter[];
    handleSave: (filters: Filter[]) => void;
    isDisabled?: boolean;
    screenId: string;
}

export function FiltersComponent({
    filters,
    isDisabled,
    fields,
    screenId,
    handleSave,
}: FilterCommonProps): React.ReactElement | null {
    const [isOpen, setIsOpen] = React.useState(false);
    const hasFilterableItems = fields.some(item => Boolean(item && item.properties.canFilter));
    if (!hasFilterableItems) {
        return null;
    }

    const togglePanelOpen = (): void => setIsOpen(!isOpen);
    const closePanel = (): void => setIsOpen(false);

    const transformedFilters =
        filters instanceof Array
            ? filters.map(filter => {
                  return { ...filter, value: { ...filter.value, filterValue: String(filter.value[0].filterValue) } };
              })
            : filters;

    const openFilterMessage = localize('@sage/xtrem-ui/openFilters', 'Open Filter');

    return (
        <>
            <ButtonMinor
                iconType="filter"
                onClick={togglePanelOpen}
                // className="e-filter-button"
                data-testid="e-filter-button"
                disabled={isDisabled}
                buttonType="tertiary"
                iconTooltipMessage={openFilterMessage}
                aria-label={openFilterMessage}
            />
            {isOpen && (
                <FilterManager
                    screenId={screenId}
                    initialFilters={transformedFilters}
                    handleCancel={closePanel}
                    nestedFields={fields}
                    handleSave={handleSave}
                />
            )}
        </>
    );
}

interface FilterLabelProps extends FilterCommonProps {
    isNavigationPanel?: boolean;
    filters: Filter[] | FilterModel;
}

interface IPill {
    labelKey: string;
    pillKey: string;
    removePill: Function;
    text: string;
}

export const dateFilterNormalization = (filter: FilterModel): string => {
    if (filter.type === RANGE) {
        return `${formatDateToCurrentLocale(filter.dateFrom)}${RANGE_DIVIDER}${formatDateToCurrentLocale(
            filter.dateTo,
        )}`;
    }
    return formatDateToCurrentLocale(filter.dateFrom);
};

export const filterModelToPills = (fields: FilterManagerField[], filters: FilterModel, removingFn: Function): IPill[] =>
    objectKeys(filters).reduce<IPill[]>((acc, key): IPill[] => {
        const column: FilterManagerField = fields.find(field => field.properties?.bind === key)!;
        const title = column?.properties?.title || key;

        if (filters[key].filterType === SET) {
            return [
                ...acc,
                ...filters[key].values.map((value: string, idx: number) => {
                    const displayValue = column?.properties?.type
                        ? localizeEnumMember(column.properties.type, value)
                        : filters?.[key]?.displayValues?.[idx] || 'N/A';
                    return {
                        labelKey: `e-filter-label-${key}-${value}-set`,
                        pillKey: `${key}-${value}-label`,
                        removePill: removingFn(key, SET, value),
                        text: getFilterDisplayName(key, [column], SET, displayValue, title),
                    };
                }),
            ];
        }
        if (filters[key].operator && filters[key].operator === 'AND') {
            const condition1Filter =
                filters[key].filterType === 'date'
                    ? dateFilterNormalization(filters[key].condition1)
                    : filters[key].condition1.filter;
            const condition2Filter =
                filters[key].filterType === 'date'
                    ? dateFilterNormalization(filters[key].condition2)
                    : filters[key].condition2.filter;
            return [
                ...acc,
                ...[
                    {
                        labelKey: `e-filter-label-${key}-${condition1Filter}-${filters[key].condition1.type}`,
                        pillKey: `${key}-${condition1Filter}-label`,
                        removePill: removingFn(key, filters[key].condition1.type, condition1Filter, 'condition1'),
                        text: getFilterDisplayName(
                            key,
                            [column],
                            filters[key].condition1.type,
                            condition1Filter,
                            title,
                        ),
                    },
                    {
                        labelKey: `e-filter-label-${key}-${condition2Filter}-${filters[key].condition2.type}`,
                        pillKey: `${key}-${condition2Filter}-label`,
                        removePill: removingFn(key, filters[key].condition2.type, condition2Filter, 'condition2'),
                        text: getFilterDisplayName(
                            key,
                            [column],
                            filters[key].condition2.type,
                            condition2Filter,
                            title,
                        ),
                    },
                ],
            ];
        }
        const filter = filters[key].filterType === 'date' ? dateFilterNormalization(filters[key]) : filters[key].filter;
        return [
            ...acc,
            {
                labelKey: `e-filter-label-${key}-${filter}-${filters[key].type}`,
                pillKey: `${key}-${filter}-label`,
                removePill: removingFn(key, filters[key].type, filter),
                text: getFilterDisplayName(key, [column], filters[key].type, filter, title),
            },
        ];
    }, []);

export function FiltersLabels({
    fields,
    filters,
    isNavigationPanel = false,
    handleSave,
}: FilterLabelProps): React.ReactElement | null {
    const removeFilter = React.useCallback(
        (id: string) => (): void => {
            const updatedFilters = cloneDeep(filters).filter((f: Filter) => {
                return f.id !== id;
            });
            handleSave(updatedFilters);
        },
        [filters, handleSave],
    );

    const removeMainListFilter = React.useCallback(
        (id: string, type: string, value: string, conditionIdx?: 'condition1' | 'condition2') => (): void => {
            const updatedFilters = cloneDeep(filters);
            if (type === SET) {
                const idx = updatedFilters[id].values.findIndex((val: string) => val === value);
                updatedFilters[id].values.splice(idx, 1);
                updatedFilters[id].displayValues?.splice(idx, 1);
                if (updatedFilters[id].values.length === 0) {
                    delete updatedFilters[id];
                }
            } else if (conditionIdx && updatedFilters[id].operator && updatedFilters[id].operator === 'AND') {
                updatedFilters[id] =
                    conditionIdx === 'condition1' ? updatedFilters[id].condition2 : updatedFilters[id].condition1;
            } else {
                delete updatedFilters[id];
            }
            handleSave(updatedFilters);
        },
        [filters, handleSave],
    );

    const pills: IPill[] = !isNavigationPanel
        ? filters.map(
              (f: Filter): IPill => ({
                  labelKey: `e-filter-label-${f.id}-${f.value[0].filterType.text}-${f.value[0].filterValue}`,
                  pillKey: `${f.id}-${f.value[0].filterType.value}-label`,
                  removePill: removeFilter(f.id),
                  text: `${f.value[0].filterType.text}`,
              }),
          )
        : filterModelToPills(fields, filters, removeMainListFilter);

    return pills.length > 0 ? (
        <div
            className={`e-filter-label-wrapper ${isNavigationPanel ? 'e-filter-label-wrapper--navigation-panel' : ''}`}
        >
            {pills.map((p: any) => {
                return (
                    <div key={p.labelKey} className="e-filter-label" data-testid={p.labelKey}>
                        <Pill
                            key={p.pillKey}
                            size={isNavigationPanel ? 'S' : 'M'}
                            showIcon={true}
                            onDelete={p.removePill}
                            colorVariant="neutral"
                            pillRole="status"
                        >
                            {p.text}
                        </Pill>
                    </div>
                );
            })}
        </div>
    ) : null;
}
