@import '../../../render/style/variables.scss';
@import '../../../render/style/mixins.scss';


.e-filters-manager {
    @include extra_small {
        height: calc(100dvh - 48px);
        overflow-y: auto;
    }
}

.e-filters-manager .e-filter-selector {
    font-family: var(--fontFamiliesDefault);
    padding: 0 8px;
    margin: 0;

    [name='switch'] {
        margin-bottom: 0;
    }

    .e-filter-input {
        margin: 4px 0;

        h4 {
            margin-bottom: 0;
            margin-top: 0;
        }
    }

    div {
        vertical-align: middle;
    }

    .e-section-icon {
        border: none;
        width: 24px;
        span {
            margin: 0;
        }
        span::before {
            color: var(--colorsYin090);
            opacity: 0.7;
        }
    }

    .e-section-icon:hover {
        background: none; 
        span::before {
            color: var(--colorsYin090);
        }
    }
}