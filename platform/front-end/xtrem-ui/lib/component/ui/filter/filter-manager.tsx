import Button from 'carbon-react/esm/components/button';
import DialogFullScreen from 'carbon-react/esm/components/dialog-full-screen';
import Heading from 'carbon-react/esm/components/heading';
import Sidebar from 'carbon-react/esm/components/sidebar';
import Switch from 'carbon-react/esm/components/switch';
import { capitalize, upperCase, zipObject } from 'lodash';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import type { ReduxResponsive } from '../../../redux/state';
import type { Filter, FilterValue } from '../../../service/filter-service';
import {
    CONTAINS,
    ENDS_WITH,
    GREATER_THAN_EQUAL,
    LESS_THAN_EQUAL,
    MATCHES,
    MULTIPLE_RANGE,
    NOT_EQUALS,
    RANGE,
    RANGE_DIVIDER,
    STARTS_WITH,
} from '../../types';
import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ilter, <PERSON>R<PERSON><PERSON><PERSON>ilt<PERSON>, <PERSON><PERSON><PERSON>ilter } from '../../types';
import { localize } from '../../../service/i18n-service';
import { showToast } from '../../../service/toast-service';
import { GraphQLTypes } from '../../../types';
import { getNestedFieldElementId } from '../../../utils/abstract-fields-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import type { NestedSelectProperties } from '../../field/select/select-types';
import type { ReferenceProperties } from '../../field/reference/reference-types';
import { getReferencePath } from '../../field/reference/reference-utils';
import type { NestedFieldTypes } from '../../nested-fields';
import type { DecimalValueObject } from '../decimal/decimal-component';
import { Decimal } from '../decimal/decimal-component';
import type { SelectItem } from '../select/select-component';
import { Select } from '../select/select-component';
import DateRange from 'carbon-react/esm/components/date-range';
import { DateValue } from '@sage/xtrem-date-time';
import { Checkbox } from 'carbon-react/esm/components/checkbox';
import Textbox from 'carbon-react/esm/components/textbox';
import type { CustomEvent } from 'carbon-react/esm/components/decimal';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import { objectKeys, type FilterType, type FiltrableType } from '@sage/xtrem-shared';
import { useDeepCompareEffect } from '@sage/xtrem-ui-components';

export interface FilterManagerField {
    type: NestedFieldTypes;
    properties: {
        bind?: string;
        canFilter?: boolean;
        title?: string;
        type: GraphQLTypes | null;
        valueField?: ReferenceProperties['valueField'];
        helperTextField?: ReferenceProperties['helperTextField'];
    };
}
interface Field {
    id: string;
    name: string;
    type: GraphQLTypes;
    values: string[];
}

interface Controls {
    [id: string]: {
        isOpen: boolean;
        isEnabled: boolean;
        hasErrors: boolean[];
    };
}

export interface FilterManagerExternalProps {
    screenId: string;
    nestedFields: FilterManagerField[];
    handleCancel: () => void;
    handleSave: (filters: Filter<FiltrableType>[]) => void;
    initialFilters?: Filter[];
}
export interface FilterManagerProps extends FilterManagerExternalProps {
    browser?: ReduxResponsive;
}

interface FilterManagerState {
    fields: Field[];
    filters: Filter<FiltrableType>[];
    controls: Controls;
}

const getFieldInitialErrors = (field: Field): boolean[] => (field.type === GraphQLTypes.Int ? [false, false] : [false]);

const getFilterHelperText = (text: StringFilter): string => {
    switch (text) {
        case CONTAINS:
            return localize('@sage/xtrem-ui/helper-text-contains', 'contains');
        case STARTS_WITH:
            return localize('@sage/xtrem-ui/helper-text-starts-with', 'starts with');
        case ENDS_WITH:
            return localize('@sage/xtrem-ui/helper-text-ends-with', 'ends with');
        case MATCHES:
            return localize('@sage/xtrem-ui/helper-text-matches', 'matches');
        default:
            return '';
    }
};

type FilterDisplayNameType = StringFilter | RangeFilter | MultipleRangeFilter | EnumFilter;

export const getFilterDisplayName = (
    fieldId: string,
    nestedFields: FilterManagerField[],
    text: FilterDisplayNameType,
    filterValue: string,
    title?: string,
): string => {
    const nestedField = nestedFields.find(c => getNestedFieldElementId(c) === fieldId);
    const fieldTitle = (nestedField && nestedField.properties.title) || title || fieldId;
    switch (text) {
        case CONTAINS:
            return localize('@sage/xtrem-ui/string-contains', '{{fieldTitle}} contains "{{filterValue}}"', {
                fieldTitle,
                filterValue,
            });
        case STARTS_WITH:
            return localize('@sage/xtrem-ui/string-starts-with', '{{fieldTitle}} starts with "{{filterValue}}"', {
                fieldTitle,
                filterValue,
            });
        case ENDS_WITH:
            return localize('@sage/xtrem-ui/string-ends-with', '{{fieldTitle}} ends with "{{filterValue}}"', {
                fieldTitle,
                filterValue,
            });
        case NOT_EQUALS:
            return localize('@sage/xtrem-ui/filter-value-not-equal', '{{fieldTitle}} != {{filterValue}}', {
                fieldTitle,
                filterValue,
            });
        case LESS_THAN_EQUAL:
            return localize('@sage/xtrem-ui/filter-value-less-than-equal', '{{fieldTitle}} <= {{filterValue}}', {
                fieldTitle,
                filterValue,
            });
        case GREATER_THAN_EQUAL:
            return localize('@sage/xtrem-ui/filter-value-greater-than-equal', '{{fieldTitle}} >= {{filterValue}}', {
                fieldTitle,
                filterValue,
            });
        case RANGE:
            const values = filterValue.split(RANGE_DIVIDER);
            return localize('@sage/xtrem-ui/filter-range', '{{fieldTitle}} is between {{value1}} and {{value2}}', {
                value1: values[0],
                value2: values[1],
                fieldTitle,
            });
        case MULTIPLE_RANGE:
            const rangeValues = filterValue.split(RANGE_DIVIDER);
            const valueKeys = rangeValues.map((_, i) => `value${i}`);
            const valueTranslationKeys = valueKeys.map(v => `{{${v}}}`);
            const localizationString = valueTranslationKeys.join(' | ');
            const localizationValue = zipObject(valueKeys, rangeValues);
            return localize('@sage/xtrem-ui/multiple-filter-range', `{{fieldTitle}} is ${localizationString}`, {
                ...localizationValue,
                fieldTitle,
            });
        default:
            return localize('@sage/xtrem-ui/filter-value-equals', '{{fieldTitle}} is {{filterValue}}', {
                filterValue,
                fieldTitle,
            });
    }
};

function UnconnectedFilterManager(props: FilterManagerProps): React.ReactElement {
    const initialFilters = React.useMemo(() => props.initialFilters || [], [props.initialFilters]);

    const [state, setState] = useState<FilterManagerState>({ fields: [], filters: initialFilters, controls: {} });

    // The order of the following two effects is IMPORTANT.
    useDeepCompareEffect(() => {
        // On mount this will be the case. The second effect is then run only once effectively populating "state.fields".
        if (state.fields.length === 0) {
            return;
        }
        const fieldControls = getInitialControls();

        const filterControls = state.filters.reduce<Controls>((prev, curr) => {
            const currentField = state.fields.find(f => f.id === curr.id);
            if (!currentField) {
                return prev;
            }
            const initialHasErrors = getFieldInitialErrors(currentField);
            return {
                ...prev,
                [curr.id]: {
                    isOpen: true,
                    isEnabled: true,
                    hasErrors: (state.controls[curr.id] && state.controls[curr.id].hasErrors) || initialHasErrors,
                },
            };
        }, {});
        setState({ ...state, controls: { ...fieldControls, ...filterControls } });
    }, [state.fields]);

    useEffect(() => {
        const newFields: Field[] = props.nestedFields
            .filter(
                item =>
                    item.properties &&
                    (item.properties.title || item.properties.valueField || item.properties.bind) &&
                    item.properties.canFilter &&
                    item.properties.type,
            )
            .map(item => {
                const name =
                    item.properties.title ||
                    (item.properties.valueField
                        ? `${item.properties.bind}.${getReferencePath(item.properties.valueField)}`
                        : item.properties.bind || '');
                return {
                    id: getNestedFieldElementId(item),
                    name,
                    type: item.properties.type || GraphQLTypes.String,
                    values:
                        item.properties.type === GraphQLTypes.Enum
                            ? resolveByValue({
                                  screenId: props.screenId,
                                  propertyValue: (item.properties as NestedSelectProperties).options,
                                  skipHexFormat: true,
                                  rowValue: null,
                              }) || []
                            : [],
                };
            });

        const initialControls =
            initialFilters instanceof Array && initialFilters.length > 0
                ? initialFilters.reduce<Controls>((prev, curr) => {
                      const currentField = newFields.find(f => f.id === curr.id);
                      if (!currentField) {
                          return prev;
                      }
                      return {
                          ...prev,
                          [curr.id]: {
                              isOpen: false,
                              isEnabled: true,
                              hasErrors: getFieldInitialErrors(currentField),
                          },
                      };
                  }, {})
                : {};

        setState({
            fields: newFields,
            filters: initialFilters instanceof Array ? initialFilters : [],
            controls: initialControls,
        });
    }, [initialFilters, props.nestedFields, props.screenId]);

    function changeFilter<T extends FiltrableType = string>(
        id: string,
        value: FilterType<T>,
        filterValue: FilterValue<T>,
    ): void {
        const text = getFilterDisplayName(id, props.nestedFields, value, filterValue.toString());
        const newFilter: Filter<T> = { id, value: [{ filterType: { text, value }, filterValue }] };
        setState({
            ...state,
            filters: [
                ...state.filters.filter(f => f.id !== id || !(f.id === id && f.value[0].filterType.value === value)),
                newFilter,
            ],
        });
    }

    const toggleVisibility = (id: string, isToShow: boolean): void => {
        setState({
            ...state,
            controls: {
                ...state.controls,
                [id]: {
                    ...state.controls[id],
                    isOpen: isToShow,
                },
            },
        });
    };

    const getInitialControls = (): Controls => {
        return state.fields.reduce<Controls>((prev, curr) => {
            const initialHasErrors = getFieldInitialErrors(curr);
            return {
                ...prev,
                [curr.id]: { isOpen: false, isEnabled: false, hasErrors: initialHasErrors },
            };
        }, {});
    };

    const showFilter = (id: string) => (): void => {
        toggleVisibility(id, true);
    };

    const hideFilter = (id: string) => (): void => {
        toggleVisibility(id, false);
    };

    const getFilterToggle = (fieldId: string) => (): void => {
        const wasEnabled = state.controls[fieldId].isEnabled;
        if (!wasEnabled) {
            // Open & enable
            setState({
                ...state,
                controls: {
                    ...state.controls,
                    [fieldId]: {
                        ...state.controls[fieldId],
                        isEnabled: true,
                        isOpen: true,
                    },
                },
            });
        } else {
            // Close & disable
            setState({
                ...state,
                controls: {
                    ...state.controls,
                    [fieldId]: {
                        ...state.controls[fieldId],
                        isEnabled: false,
                        isOpen: false,
                    },
                },
            });
        }
    };

    const handleDecimalChange =
        (id: string, updatedProperty: 'min' | 'max', otherValue: string) =>
        (event: CustomEvent): void => {
            const values: string[] = ['', ''];
            if (updatedProperty === 'min') {
                values[0] = (event.target.value as unknown as DecimalValueObject).rawValue;
                values[1] = otherValue;
            } else {
                values[0] = otherValue;
                values[1] = (event.target.value as unknown as DecimalValueObject).rawValue;
            }
            setRangeFiltersState(id, values);
        };

    function handleFilterChange(id: string, filterName: StringFilter, value: string): void {
        changeFilter(id, filterName, value);
    }

    const handleTextChange =
        (id: string, filterName: StringFilter) =>
        (event: React.ChangeEvent<HTMLInputElement>): void => {
            handleFilterChange(id, filterName, event.target.value);
        };

    const handleBooleanChange =
        (id: string, filterName: BooleanFilter) =>
        (event: React.ChangeEvent<HTMLInputElement>): void => {
            handleFilterChange(id, filterName, String(event.target.checked));
        };

    const getSwitches = (isOpen: boolean, field: Field, isEnabled = false): JSX.Element => (
        <div
            data-testid={`e-filter-${upperCase(field.name)}`}
            style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
            }}
        >
            <div style={{ display: 'flex', alignItems: 'center' }}>
                {isOpen && (
                    <ButtonMinor
                        onClick={hideFilter(field.id)}
                        iconType="chevron_up"
                        iconTooltipMessage={localize('@sage/xtrem-ui/filter-manager-close', 'Close')}
                        aria-label={localize('@sage/xtrem-ui/filter-manager-close', 'Close')}
                        buttonType="tertiary"
                        mr="8px"
                    />
                )}
                {!isOpen && (
                    <ButtonMinor
                        onClick={showFilter(field.id)}
                        iconType="chevron_down"
                        iconTooltipMessage={localize('@sage/xtrem-ui/filter-manager-open', 'Open')}
                        aria-label={localize('@sage/xtrem-ui/filter-manager-open', 'Open')}
                        buttonType="tertiary"
                        mr="8px"
                    />
                )}
                <h4>{capitalize(field.name)}</h4>
            </div>
            <Switch name="switch" value="switch" checked={isEnabled} onChange={getFilterToggle(field.id)} />
        </div>
    );

    const hasFieldErrors = (id: string): boolean => {
        if (!state.controls[id] || state.controls[id].hasErrors) {
            return false;
        }

        // eslint-disable-next-line no-restricted-syntax
        for (const v of state.controls[id].hasErrors as boolean[]) {
            if (v) {
                return true;
            }
        }

        return false;
    };

    const hasErrors = (): boolean => {
        // eslint-disable-next-line no-restricted-syntax
        for (const c of objectKeys(state.controls)) {
            if (state.controls[c].hasErrors && hasFieldErrors(c)) {
                return true;
            }
        }

        return false;
    };

    const handleDateChange =
        (id: string) =>
        (event: Parameters<React.ComponentProps<typeof DateRange>['onChange']>[0]): void => {
            const newDates = event.target.value.map(dateValue => dateValue.rawValue as string);
            setRangeFiltersState(id, newDates);
        };

    const setRangeFiltersState = (id: string, newValues: string[], isMultiRange = false): void => {
        const filterValue = newValues.join(RANGE_DIVIDER);
        const rangeType = isMultiRange ? MULTIPLE_RANGE : RANGE;
        const newRangeFilter: Filter<any> = {
            id,
            value: [
                {
                    filterType: {
                        text: getFilterDisplayName(id, props.nestedFields, rangeType, filterValue),
                        value: rangeType,
                    },
                    filterValue,
                },
            ],
        };

        setState({
            ...state,
            filters: [
                ...state.filters.filter(
                    f => f.id !== id || !(f.id === id && f.value[0].filterType.value === rangeType),
                ),
                newRangeFilter,
            ],
        });
    };

    const getTextFilter = ({ id }: Field, matchingCriteria: StringFilter): JSX.Element => {
        const isEnabled = state.controls[id] && state.controls[id].isEnabled;
        const filter = state.filters.find(f => f.id === id);
        const value = filter?.value[0].filterValue || '';
        return (
            <Textbox
                onChange={handleTextChange(id, matchingCriteria)}
                disabled={!isEnabled}
                fieldHelp={getFilterHelperText(matchingCriteria)}
                value={value.toString()}
            />
        );
    };

    const optionToSelectItem = (option: string): SelectItem => {
        return {
            id: option,
            value: option,
        };
    };

    const getEnumFilter = ({ id }: Field, matchingCriteria: StringFilter): JSX.Element => {
        const isEnabled = state.controls[id] && state.controls[id].isEnabled;
        const currentField = state.fields.find(f => f.id === id);
        const currentFilter = state.filters.find(f => f.id === id && f.value[0].filterType.value === MULTIPLE_RANGE);
        const { values } = getRangeValues(currentFilter);
        const initialSelectedItems = values.map(optionToSelectItem);
        const onSelectedItemsChange = (selectedRecords: SelectItem[]): void => {
            setRangeFiltersState(
                id,
                selectedRecords.map(i => String(i.value)),
                true,
            );
        };
        return (
            <Select
                minLookupCharacters={0}
                disabled={!isEnabled}
                helperText={getFilterHelperText(matchingCriteria)}
                isMultiSelect={true}
                getItems={(): Promise<SelectItem[]> => {
                    const items = currentField ? currentField.values.map(optionToSelectItem) : [];
                    return Promise.resolve(items);
                }}
                initialSelectedItems={initialSelectedItems}
                onSelectedItemsChange={onSelectedItemsChange}
                screenId={props.screenId}
                elementId="$filter"
            />
        );
    };

    const getRangeValues = (
        filter?: Filter<FiltrableType>,
    ): {
        minValue: string;
        maxValue: string;
        values: string[];
    } => {
        const values = filter ? filter.value[0].filterValue?.toString().split(RANGE_DIVIDER) : [];
        return { minValue: values[0] || '', maxValue: values[1] || '', values };
    };

    const getNumericFilter = ({ id, name, type }: Field): JSX.Element => {
        const isEnabled = state.controls[id] && state.controls[id].isEnabled;
        const currentFilter = state.filters.find(f => f.id === id && f.value[0].filterType.value === RANGE);
        const { minValue, maxValue } = getRangeValues(currentFilter);

        return (
            <div
                data-testid={`e-filter-${type}-${upperCase(name)}`}
                style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
            >
                <div data-testid={`e-filter-${type}-${upperCase(name)}-min-value`} style={{ flex: 1 }}>
                    <Decimal
                        onChange={handleDecimalChange(id, 'min', maxValue)}
                        disabled={!isEnabled}
                        fieldHelp={localize('@sage/xtrem-ui/filter-manager-min-value', 'Min value')}
                        value={minValue}
                    />
                </div>
                <div data-testid={`e-filter-${type}-${upperCase(name)}-max-value`} style={{ flex: 1 }}>
                    <Decimal
                        onChange={handleDecimalChange(id, 'max', minValue)}
                        disabled={!isEnabled}
                        fieldHelp={localize('@sage/xtrem-ui/filter-manager-max-value', 'Max value')}
                        value={maxValue}
                    />
                </div>
            </div>
        );
    };

    const getDateRangeFilter = ({ id }: Field): JSX.Element => {
        const isEnabled = state.controls[id] && state.controls[id].isEnabled;
        const startDate = DateValue.today().begOfMonth().toString();
        const endDate = DateValue.today().endOfMonth().toString();

        // In order to add the default dates as a filter, we need to simulate a change
        if (isEnabled && !state.filters.find(filter => filter.id === id)) {
            setRangeFiltersState(id, [startDate, endDate]);
        }

        return (
            <div style={{ flex: 1 }}>
                <DateRange
                    startLabel={localize('@sage/xtrem-ui/filter-manager-range-start', 'Start date')}
                    startDateProps={{ disabled: !isEnabled }}
                    endDateProps={{ disabled: !isEnabled }}
                    endLabel={localize('@sage/xtrem-ui/filter-manager-range-end', 'End date')}
                    value={[startDate, endDate]}
                    onChange={handleDateChange(id)}
                />
            </div>
        );
    };

    const getBooleanFilter = ({ id }: Field): JSX.Element => {
        const isEnabled = state.controls[id] && state.controls[id].isEnabled;
        const filter = state.filters.find(f => f.id === id);
        const value = filter?.value[0].filterValue === 'true' || filter?.value[0].filterValue === true;
        // In order to add the default value as a filter, we need to simulate a change
        if (isEnabled && !filter) {
            changeFilter(id, MATCHES, value.toString());
        }

        return (
            <div style={{ flex: 1 }}>
                <Checkbox
                    onChange={handleBooleanChange(id, MATCHES)}
                    label={localize('@sage/xtrem-ui/filter-manager-checkbox', 'Is active')}
                    disabled={!isEnabled}
                    value={String(value)}
                    checked={value}
                />
            </div>
        );
    };

    const filterItems: (JSX.Element | null)[] = state.fields.map(field => {
        const isOpen = state.controls[field.id] && state.controls[field.id].isOpen;
        const isEnabled = state.controls[field.id] && state.controls[field.id].isEnabled;
        let filterComponent;
        switch (field.type) {
            case GraphQLTypes.Enum:
                filterComponent = getEnumFilter(field, MATCHES);
                break;
            case GraphQLTypes.String:
                filterComponent = getTextFilter(field, CONTAINS);
                break;
            case GraphQLTypes.Int:
            case GraphQLTypes.Float:
            case GraphQLTypes.Decimal:
                filterComponent = getNumericFilter(field);
                break;
            case GraphQLTypes.Date:
            case GraphQLTypes.DateTime:
                filterComponent = getDateRangeFilter(field);
                break;
            case GraphQLTypes.Boolean:
                filterComponent = getBooleanFilter(field);
                break;
            default:
                break;
        }
        if (!filterComponent) {
            return null;
        }
        return (
            <div className="e-filter-selector" key={field.id}>
                {getSwitches(isOpen, field, isEnabled)}
                {isOpen && (
                    <div data-testid={`e-filter-input e-filter-input-${upperCase(field.name)}`}>{filterComponent}</div>
                )}
            </div>
        );
    });

    const validateActiveFilters = (activeFilters: Filter<FiltrableType>[]): boolean => {
        const getNewControls = (filter: Filter<FiltrableType>): Controls =>
            objectKeys(state.controls).reduce<Controls>((prev, curr) => {
                if (curr === filter.id) {
                    return { ...prev, [curr]: { ...state.controls[curr], isOpen: true } };
                }

                return { ...prev, [curr]: { ...state.controls[curr], isOpen: false } };
            }, {});

        // eslint-disable-next-line no-restricted-syntax
        for (const filter of activeFilters) {
            const currentField = state.fields.find(f => f.id === filter.id);
            if (
                currentField &&
                (currentField.type === GraphQLTypes.Int ||
                    currentField.type === GraphQLTypes.Float ||
                    currentField.type === GraphQLTypes.Decimal)
            ) {
                const { minValue, maxValue } = getRangeValues(filter);
                const numericMin = parseFloat(minValue);
                const numericMax = parseFloat(maxValue);
                if (Number.isNaN(Number(numericMin)) || Number.isNaN(Number(numericMax)) || numericMax < numericMin) {
                    const newControls = getNewControls(filter);
                    setState({ ...state, controls: newControls });
                    return false;
                }
            } else if (currentField && currentField.type === GraphQLTypes.Date) {
                const { minValue, maxValue } = getRangeValues(filter);
                if (DateValue.parse(minValue).compare(DateValue.parse(maxValue)) >= 0) {
                    const newControls = getNewControls(filter);
                    setState({ ...state, controls: newControls });
                    return false;
                }
            }
        }

        return true;
    };

    const transformFilters = (activeFilters: Filter<FiltrableType>[]): Filter<FiltrableType>[] => {
        return activeFilters.map(filter => {
            const currentField = state.fields.find(f => f.id === filter.id);
            if (
                filter.value[0].filterValue.toString().indexOf(RANGE_DIVIDER) === -1 &&
                currentField &&
                (currentField.type === GraphQLTypes.Int ||
                    currentField.type === GraphQLTypes.Float ||
                    currentField.type === GraphQLTypes.Decimal)
            ) {
                return {
                    ...filter,
                    value: [{ ...filter.value[0], filterValue: parseFloat(filter.value[0].filterValue.toString()) }],
                };
            }
            if (currentField && currentField.type === GraphQLTypes.Boolean) {
                return {
                    ...filter,
                    value: [
                        {
                            ...filter.value[0],
                            filterValue: filter.value[0].filterValue === 'true' || filter.value[0].filterValue === true,
                        },
                    ],
                };
            }
            return filter;
        });
    };

    const onSave = (): void => {
        const activeFilters = state.filters.filter(f => state.controls[f.id].isEnabled);
        const isValid = validateActiveFilters(activeFilters);
        if (isValid) {
            const transformedFilters = transformFilters(activeFilters);
            props.handleSave(transformedFilters);
            props.handleCancel();
        } else {
            showToast(localize('@sage/xtrem-ui/filter-manager-invalid-filters', 'Some filters are invalid'), {
                type: 'error',
            });
        }
    };

    const isClearAllDisabled = state.filters.length === 0;

    const clearAll = (): void => {
        const controls = getInitialControls();
        setState({ ...state, controls, filters: [] });
    };

    const dialogContent = (): React.ReactNode => (
        <div className="e-dialog-content e-filters-dialog" data-testid="e-filters-dialog">
            <div className="e-dialog-body" data-testid="e-dialog-body">
                <div className="e-filters-manager">{filterItems}</div>
            </div>
            <div className="e-dialog-sidebar-footer">
                <div className="e-dialog-sidebar-footer-content">
                    {props.browser && props.browser.greaterThan.xs && (
                        <div>
                            <Button buttonType="tertiary" onClick={props.handleCancel} data-testid="e-filters-cancel">
                                {localize('@sage/xtrem-ui/filter-manager-cancel-button', 'Cancel')}
                            </Button>
                        </div>
                    )}
                    <div>
                        <Button
                            onClick={clearAll}
                            buttonType="secondary"
                            disabled={isClearAllDisabled}
                            data-testid="e-filters-clear-all"
                        >
                            {localize('@sage/xtrem-ui/filter-manager-clear-all-button', 'Clear All')}
                        </Button>
                        <Button
                            // style={{ marginRight: 0 }}
                            disabled={hasErrors()}
                            onClick={onSave}
                            data-testid="e-filters-apply"
                        >
                            {localize('@sage/xtrem-ui/filter-manager-apply-button', 'Apply')}
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );

    return props.browser && props.browser.greaterThan.s ? (
        <Sidebar
            key="key"
            onCancel={props.handleCancel}
            open={true}
            header={
                <Heading title={localize('@sage/xtrem-ui/filter-manager-title-header', 'Filters')} divider={false} />
            }
        >
            {dialogContent()}
        </Sidebar>
    ) : (
        <DialogFullScreen
            key="key"
            data-testid="filter-manager-mobile"
            title={localize('@sage/xtrem-ui/filter-manager-title-header', 'Filters')}
            onCancel={props.handleCancel}
            open={true}
        >
            {dialogContent()}
        </DialogFullScreen>
    );
}

/** @internal */
const mapStateToProps = (state: xtremRedux.XtremAppState, props: FilterManagerExternalProps): FilterManagerProps => ({
    ...props,
    browser: state.browser,
});

/** @internal */
export const FilterManager = connect(mapStateToProps)(UnconnectedFilterManager);
