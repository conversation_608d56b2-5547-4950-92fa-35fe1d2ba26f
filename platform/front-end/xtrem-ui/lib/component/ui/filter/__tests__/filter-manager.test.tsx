import { getMockStore } from '../../../../__tests__/test-helpers';
import { fireEvent, render, waitFor, within } from '@testing-library/react';
import { upperCase } from 'lodash';
import * as React from 'react';
import { GraphQLTypes } from '../../../../types';
import type { FilterManagerField } from '../filter-manager';
import { FilterManager } from '../filter-manager';
import { Provider } from 'react-redux';
import { FieldKey } from '../../../types';

const setup = (filterManagerProps: React.ComponentProps<typeof FilterManager>) => {
    const mockStore = getMockStore();
    const utils = render(
        <Provider store={mockStore}>
            <FilterManager {...filterManagerProps} />
        </Provider>,
    );
    const getFilterManager = () => utils.getByTestId('e-filters-dialog');
    const getFilterByTitle = (title: string) => within(getFilterManager()).getByTestId(`e-filter-${upperCase(title)}`);
    const getTitle = (title: string) => within(getFilterManager()).getByText(title);
    const enableFilter = async (title: string) => {
        fireEvent.click(within(getFilterByTitle(title)).getByRole('switch'));
        await waitFor(() => expect(within(getFilterByTitle(title)).getByText('ON')).toBeInTheDocument());
    };
    const disableFilter = async (title: string) => {
        fireEvent.click(within(getFilterByTitle(title)).getByRole('switch'));
        await waitFor(() => expect(within(getFilterByTitle(title)).getByText('OFF')).toBeInTheDocument());
    };
    const getMinValue = (title: string, type: GraphQLTypes) => {
        return within(getFilterManager())
            .getByTestId(`e-filter-${type}-${upperCase(title)}-min-value`)
            .querySelector('input')!.value;
    };
    const getMaxValue = (title: string, type: GraphQLTypes) => {
        return within(getFilterManager())
            .getByTestId(`e-filter-${type}-${upperCase(title)}-max-value`)
            .querySelector('input')!.value;
    };
    const getMinValueSpan = (title: string, type: GraphQLTypes) => {
        return within(getFilterManager())
            .getByTestId(`e-filter-${type}-${upperCase(title)}-min-value`)
            .querySelector('span');
    };
    const getMinDateLabel = (title: string) => {
        return utils.getByTestId(`e-filter-input e-filter-input-${upperCase(title)}`).querySelector('label')!;
    };
    const getMinDateValue = (title: string) => {
        return utils.getByTestId(`e-filter-input e-filter-input-${upperCase(title)}`).querySelector('input')!.value;
    };
    const changeMinDateValue = (title: string, value: string) => {
        fireEvent.change(
            utils.getByTestId(`e-filter-input e-filter-input-${upperCase(title)}`).querySelector('input')!,
            { target: { value } },
        );
    };
    const getMaxDateLabel = (title: string) => {
        return utils.getByTestId(`e-filter-input e-filter-input-${upperCase(title)}`).querySelectorAll('label')[1]!;
    };
    const getMaxDateValue = (title: string) => {
        return (utils
            .getByTestId(`e-filter-input e-filter-input-${upperCase(title)}`)
            .querySelectorAll('input[data-element="input"]')[1] as HTMLInputElement)!.value;
    };
    const changeMaxDateValue = (title: string, value: string) => {
        fireEvent.change(
            utils
                .getByTestId(`e-filter-input e-filter-input-${upperCase(title)}`)
                .querySelectorAll('input[data-element="input"]')[1]!,
            { target: { value } },
        );
    };
    const getMaxValueSpan = (title: string, type: GraphQLTypes) => {
        return within(getFilterManager())
            .getByTestId(`e-filter-${type}-${upperCase(title)}-max-value`)
            .querySelector('span');
    };
    const getValueSpan = (title: string) => {
        return within(getFilterManager())
            .getByTestId(`e-filter-input e-filter-input-${upperCase(title)}`)
            .querySelector('span[data-element="help"]');
    };
    const getValueLabel = (title: string) => {
        return within(getFilterManager())
            .getByTestId(`e-filter-input e-filter-input-${upperCase(title)}`)
            .querySelector('label');
    };
    const getValue = (title: string) => {
        return within(getFilterManager())
            .getByTestId(`e-filter-input e-filter-input-${upperCase(title)}`)
            .querySelector('input')!.value;
    };
    const clickInput = (title: string) => {
        fireEvent.click(
            within(getFilterManager())
                .getByTestId(`e-filter-input e-filter-input-${upperCase(title)}`)
                .querySelector('input')!,
        );
    };
    const getOptions = (title: string) => {
        clickInput(title);
        const options = Array.from(
            (
                within(getFilterManager())
                    .getByTestId(`e-filter-input e-filter-input-${upperCase(title)}`)
                    .querySelectorAll('li') as any
            ).values(),
        ).map(li => {
            return (li as any).textContent;
        });
        fireEvent.keyDown(within(getFilterManager()).getByTestId(`e-filter-input e-filter-input-${upperCase(title)}`), {
            key: 'Escape',
            code: 27,
        });
        return options;
    };

    const changeMinValue = (title: string, type: GraphQLTypes, value: string) => {
        fireEvent.change(
            within(getFilterManager())
                .getByTestId(`e-filter-${type}-${upperCase(title)}-min-value`)
                .querySelector('input')!,
            { target: { value } },
        );
    };
    const changeMaxValue = (title: string, type: GraphQLTypes, value: string) => {
        fireEvent.change(
            within(getFilterManager())
                .getByTestId(`e-filter-${type}-${upperCase(title)}-max-value`)
                .querySelector('input')!,
            { target: { value } },
        );
    };
    const changeValue = (title: string, value: string) => {
        fireEvent.change(
            within(getFilterManager())
                .getByTestId(`e-filter-input e-filter-input-${upperCase(title)}`)
                .querySelector('input')!,
            { target: { value } },
        );
    };
    const clickOnInputField = (title: string) => {
        fireEvent.click(
            within(getFilterManager())
                .getByTestId(`e-filter-input e-filter-input-${upperCase(title)}`)
                .querySelector('input')!,
        );
    };
    const applyFilters = () => {
        fireEvent.click(within(getFilterManager()).getByText('Apply'));
    };

    return {
        ...utils,
        getFilterManager,
        getTitle,
        getFilterByTitle,
        enableFilter,
        disableFilter,
        getMinValue,
        getMinValueSpan,
        getMaxValue,
        getMaxValueSpan,
        changeMinValue,
        changeMaxValue,
        applyFilters,
        getValue,
        getValueSpan,
        changeValue,
        getMinDateLabel,
        getMaxDateLabel,
        getMinDateValue,
        getMaxDateValue,
        changeMinDateValue,
        changeMaxDateValue,
        getValueLabel,
        clickOnInputField,
        clickInput,
        getOptions,
    };
};

describe('Filter manager component', () => {
    describe('integration', () => {
        const handleCancel = () => {};
        const handleSave = jest.fn();
        it('can render Int field', async () => {
            const title = 'Int title';
            const type = GraphQLTypes.Int;
            const bind = 'bind';
            const fields: FilterManagerField[] = [
                {
                    type: FieldKey.Numeric,
                    properties: {
                        bind,
                        type,
                        title,
                        canFilter: true,
                    },
                },
            ];
            const {
                getFilterManager,
                getFilterByTitle,
                getTitle,
                enableFilter,
                getMinValueSpan,
                getMaxValueSpan,
                getMinValue,
                getMaxValue,
                changeMinValue,
                changeMaxValue,
                applyFilters,
            } = setup({
                nestedFields: fields,
                handleCancel,
                handleSave,
                screenId: 'TestPage',
            });
            expect(getFilterManager()).toBeInTheDocument();
            expect(getTitle(title)).toBeInTheDocument();
            expect(getFilterByTitle(title)).toBeInTheDocument();
            await enableFilter(title);
            expect(getMinValueSpan(title, type)).toHaveTextContent('Min value');
            expect(getMaxValueSpan(title, type)).toHaveTextContent('Max value');
            expect(getMinValue(title, type)).toBe('');
            expect(getMaxValue(title, type)).toBe('');
            changeMinValue(title, type, '1');
            expect(getMinValue(title, type)).toBe('1');
            changeMaxValue(title, type, '3');
            expect(getMaxValue(title, type)).toBe('3');
            applyFilters();
            const expected = [
                {
                    id: bind,
                    value: [
                        {
                            filterType: {
                                text: 'Int title is between 1 and 3',
                                value: 'inRange',
                            },
                            filterValue: '1~3',
                        },
                    ],
                },
            ];
            expect(handleSave).toHaveBeenCalledWith(expected);
        });
        it('can render String field', async () => {
            const title = 'String title';
            const type = GraphQLTypes.String;
            const bind = 'bind';
            const fields: FilterManagerField[] = [
                {
                    type: FieldKey.Text,
                    properties: {
                        bind,
                        type,
                        title,
                        canFilter: true,
                    },
                },
            ];
            const {
                getFilterManager,
                getFilterByTitle,
                getTitle,
                enableFilter,
                getValue,
                getValueSpan,
                applyFilters,
                changeValue,
            } = setup({
                nestedFields: fields,
                handleCancel,
                handleSave,
                screenId: 'TestPage',
            });
            expect(getFilterManager()).toBeInTheDocument();
            expect(getTitle(title)).toBeInTheDocument();
            expect(getFilterByTitle(title)).toBeInTheDocument();
            await enableFilter(title);
            expect(getValueSpan(title)).toHaveTextContent('contains');
            expect(getValue(title)).toBe('');
            changeValue(title, 'hello');
            expect(getValue(title)).toBe('hello');
            applyFilters();
            const expected = [
                {
                    id: bind,
                    value: [
                        {
                            filterType: {
                                text: 'String title contains "hello"',
                                value: 'contains',
                            },
                            filterValue: 'hello',
                        },
                    ],
                },
            ];
            expect(handleSave).toHaveBeenCalledWith(expected);
        });
        it('can render Date field', async () => {
            const title = 'Date title';
            const type = GraphQLTypes.Date;
            const bind = 'bind';
            const fields: FilterManagerField[] = [
                {
                    type: FieldKey.Date,
                    properties: {
                        bind,
                        type,
                        title,
                        canFilter: true,
                    },
                },
            ];
            const {
                getFilterManager,
                getFilterByTitle,
                getTitle,
                enableFilter,
                getMinDateLabel,
                getMaxDateLabel,
                getMinDateValue,
                getMaxDateValue,
                changeMinDateValue,
                changeMaxDateValue,
                applyFilters,
            } = setup({
                nestedFields: fields,
                handleCancel,
                handleSave,
                screenId: 'TestPage',
            });
            expect(getFilterManager()).toBeInTheDocument();
            expect(getTitle(title)).toBeInTheDocument();
            expect(getFilterByTitle(title)).toBeInTheDocument();
            await enableFilter(title);
            expect(getMinDateLabel(title)).toHaveTextContent('Start date');
            expect(getMaxDateLabel(title)).toHaveTextContent('End date');
            changeMinDateValue(title, '09/04/2020');
            expect(getMinDateValue(title)).toBe('09/04/2020');
            changeMaxDateValue(title, '10/04/2020');
            expect(getMaxDateValue(title)).toBe('10/04/2020');
            applyFilters();
            const expected = [
                {
                    id: bind,
                    value: [
                        {
                            filterType: {
                                text: 'Date title is between 2020-04-09 and 2020-04-10',
                                value: 'inRange',
                            },
                            filterValue: '2020-04-09~2020-04-10',
                        },
                    ],
                },
            ];
            expect(handleSave).toHaveBeenCalledWith(expected);
        });
        it('can render Checkbox field', async () => {
            const title = 'Checkbox title';
            const type = GraphQLTypes.Boolean;
            const bind = 'bind';
            const fields: FilterManagerField[] = [
                {
                    type: FieldKey.Checkbox,
                    properties: {
                        bind,
                        type,
                        title,
                        canFilter: true,
                    },
                },
            ];
            const {
                getFilterManager,
                getFilterByTitle,
                getTitle,
                enableFilter,
                getValueLabel,
                getValue,
                clickOnInputField,
                applyFilters,
            } = setup({
                nestedFields: fields,
                handleCancel,
                handleSave,
                screenId: 'TestPage',
            });
            expect(getFilterManager()).toBeInTheDocument();
            expect(getTitle(title)).toBeInTheDocument();
            expect(getFilterByTitle(title)).toBeInTheDocument();
            await enableFilter(title);
            expect(getValueLabel(title)).toHaveTextContent('Is active');
            expect(getValue(title)).toBe('false');
            clickOnInputField(title);
            expect(getValue(title)).toBe('true');
            applyFilters();
            const expected = [
                {
                    id: bind,
                    value: [
                        {
                            filterType: {
                                text: 'Checkbox title is true',
                                value: 'matches',
                            },
                            filterValue: true,
                        },
                    ],
                },
            ];
            expect(handleSave).toHaveBeenCalledWith(expected);
        });
    });
});
