.e-drop-file-container {
    display: flex;
    height: 100%;
    position: relative;

    .e-drop-file-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: calc(100% - 48px);
        height: calc(100% - 48px);
        background: var(--colorsYang080);
        display: flex;
        flex-flow: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        border: 2px dashed var(--colorsBaseTheme, var(--colorsSemanticPositive500));
        box-sizing: border-box;
        border-radius: 20px;
        margin: 24px;

        .e-drop-file-upload-icon {
            color: var(--colorsBaseTheme, var(--colorsSemanticPositive500));
            border: 4px solid;
            border-radius: 12px;
            padding: 6px;
        }

        p {
            color: var(--black-black-90-default-text, rgba(0, 0, 0, 0.90));
            font-size: 20px;
            font-style: normal;
            font-weight: 700;
            line-height: 125%;
            font-family: var(--fontFamiliesDefault);
        }
    }
}