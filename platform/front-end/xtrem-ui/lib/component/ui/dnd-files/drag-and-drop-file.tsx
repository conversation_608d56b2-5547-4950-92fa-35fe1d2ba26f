import React, { useState, type ReactNode } from 'react';
import { localize } from '../../../service/i18n-service';
import Icon from 'carbon-react/esm/components/icon';

interface DragAndDropFileProps {
    isDisabled?: boolean;
    children: ReactNode;
    onFileDrop: (files: FileList) => void;
}

function DragAndDropFile({ children, isDisabled, onFileDrop }: DragAndDropFileProps): React.ReactNode {
    const [isDragging, setIsDragging] = useState(false);
    const memoizedChildren = React.useMemo(() => children, [children]);

    const dragOverEnterHandler = React.useCallback<NonNullable<React.ComponentProps<'div'>['onDragEnter']>>(
        (e): void => {
            e.preventDefault();
            if (e.dataTransfer.items && e.dataTransfer.items[0].kind === 'file') {
                setIsDragging(true);
            }
        },
        [],
    );

    const dragLeaveHandler = React.useCallback<NonNullable<React.ComponentProps<'div'>['onDragLeave']>>((e): void => {
        e.preventDefault();
        // Check if the related target is a child of the parent div
        if (!e.currentTarget.contains(e.relatedTarget as Node | null)) {
            setIsDragging(false);
        }
    }, []);

    const dropHandler = React.useCallback<NonNullable<React.ComponentProps<'div'>['onDragLeave']>>(
        (e): void => {
            e.preventDefault();
            if (e.dataTransfer.items && e.dataTransfer.items[0].kind === 'file') {
                setIsDragging(false);
                const files = e.dataTransfer.files;
                onFileDrop(files);
            }
        },
        [onFileDrop],
    );

    if (isDisabled) {
        return memoizedChildren;
    }

    return (
        <div
            data-testid="e-drag-and-drop-component"
            id="e-drag-and-drop-component"
            className="e-drop-file-container"
            onDragOver={dragOverEnterHandler}
            onDragEnter={dragOverEnterHandler}
            onDragLeave={dragLeaveHandler}
            onDrop={dropHandler}
        >
            {isDragging && (
                <div className="e-drop-file-overlay">
                    <Icon className="e-drop-file-upload-icon" fontSize="extra-large" type="upload" />
                    <p>{localize('@sage/xtrem-ui/drag-drop-file', 'Drag and drop your files here.')}</p>
                </div>
            )}
            {children}
        </div>
    );
}

export default DragAndDropFile;
