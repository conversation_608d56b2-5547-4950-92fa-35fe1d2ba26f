import { render, fireEvent } from '@testing-library/react';
import React from 'react';
import DragAndDropFile from '../drag-and-drop-file';

describe('DragAndDropFile', () => {
    const mockFile = new File(['hello'], 'hello.png', { type: 'image/png' });
    const dataTransfer = {
        files: [mockFile],
        items: [
            {
                kind: 'file',
                type: 'image/png',
                getAsFile: () => mockFile,
            },
        ],
        types: ['Files'],
    };

    const onFileDropMock = jest.fn();

    it('renders children when not dragging', () => {
        const { getByText } = render(
            <DragAndDropFile onFileDrop={onFileDropMock}>
                <div>Test</div>
            </DragAndDropFile>,
        );
        expect(getByText('Test')).toBeInTheDocument();
    });

    it('shows overlay when dragging', () => {
        const { getByText, getByTestId } = render(
            <DragAndDropFile onFileDrop={onFileDropMock}>
                <div>Test</div>
            </DragAndDropFile>,
        );
        const dropzone = getByTestId('e-drag-and-drop-component');
        fireEvent.dragEnter(dropzone, { dataTransfer });
        expect(getByText('Drag and drop your files here.')).toBeInTheDocument();
    });

    it('hides overlay when drag leaves', () => {
        const { queryByText, getByTestId } = render(
            <DragAndDropFile onFileDrop={onFileDropMock}>
                <div>Test</div>
            </DragAndDropFile>,
        );
        const dropzone = getByTestId('e-drag-and-drop-component');
        fireEvent.dragEnter(dropzone, { dataTransfer });
        fireEvent.dragLeave(dropzone, { dataTransfer });
        expect(queryByText('Drag and drop your files here.')).not.toBeInTheDocument();
    });

    it('handles drop event', () => {
        const { getByTestId } = render(
            <DragAndDropFile onFileDrop={onFileDropMock}>
                <div>Test</div>
            </DragAndDropFile>,
        );
        const dropzone = getByTestId('e-drag-and-drop-component');
        fireEvent.drop(dropzone, { dataTransfer });
        expect(onFileDropMock).toHaveBeenCalledWith([mockFile]);
    });
});
