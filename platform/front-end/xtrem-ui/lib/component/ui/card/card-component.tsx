import * as tokens from '@sage/design-tokens/js/base/common';
import type { ClientNode } from '@sage/xtrem-client';
import { Checkbox } from 'carbon-react/esm/components/checkbox';
import Icon from 'carbon-react/esm/components/icon';
import IconButton from 'carbon-react/esm/components/icon-button';
import ProgressTracker from 'carbon-react/esm/components/progress-tracker';
import { get, isNil } from 'lodash';
import * as React from 'react';
import type { NestedFieldWrapperContextProps } from '../../../render/nested-field-wrapper';
import { ConnectedNestedFieldWrapper } from '../../../render/nested-field-wrapper';
import { localize } from '../../../service/i18n-service';
import type { ScreenBase } from '../../../service/screen-base';
import type { ContextType } from '../../../types';
import { getNestedFieldElementId, normalizeUnderscoreBind } from '../../../utils/abstract-fields-utils';
import { getImagePlaceholderValue } from '../../../utils/nested-field-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { cleanMetadataFromRecord, splitValueToMergedValue } from '../../../utils/transformers';
import type { ImageDecoratorProperties } from '../../field/image/image-types';
import type { NestedField, NestedFieldTypes, NestedFieldTypesWithoutTechnical } from '../../nested-fields';
import type { NestedExtensionField } from '../../nested-fields-extensions';
import type { FieldKey } from '../../types';
import type { PortraitPlaceholderMode } from '../portrait-component';
import type {
    CollectionItemActionGroup,
    CollectionItemActionOrMenuSeparator,
    InlineCollectionItemAction,
} from '../table-shared/table-dropdown-actions/table-dropdown-action-types';
import ConnectedTableDropdownActions from '../table-shared/table-dropdown-actions/table-dropdown-actions';
import ConnectedTableInlineActions from '../table-shared/table-inline-actions';
import type { EditableFieldProperties } from '../../editable-field-control-object';

export const getNestedValue = (value: any, bind: string): any => {
    if (!(value instanceof Object) || !bind) {
        return null;
    }

    const accessor = normalizeUnderscoreBind(bind);
    const result = get(value, accessor, null);
    if (isNil(result)) {
        return null;
    }
    return result;
};

export interface CardDefinition<CT extends ScreenBase = ScreenBase, NodeType extends ClientNode = any> {
    title: NestedField<CT, NestedFieldTypesWithoutTechnical, NodeType>;
    titleRight?: NestedField<CT, NestedFieldTypesWithoutTechnical, NodeType>;
    line2?: NestedField<CT, NestedFieldTypesWithoutTechnical, NodeType>;
    line2Right?: NestedField<CT, NestedFieldTypesWithoutTechnical, NodeType>;
    line3?: NestedField<CT, NestedFieldTypesWithoutTechnical, NodeType>;
    line3Right?: NestedField<CT, NestedFieldTypesWithoutTechnical, NodeType>;
    line4?: NestedField<CT, NestedFieldTypesWithoutTechnical, NodeType>;
    line4Right?: NestedField<CT, NestedFieldTypesWithoutTechnical, NodeType>;
    line5?: NestedField<CT, NestedFieldTypesWithoutTechnical, NodeType>;
    line5Right?: NestedField<CT, NestedFieldTypesWithoutTechnical, NodeType>;
    image?: NestedField<CT, FieldKey.Image | FieldKey.Icon, NodeType>;
    progressBar?: NestedField<CT, FieldKey.Progress, NodeType>;
    [key: string]: NestedField<CT, NestedFieldTypes, NodeType> | undefined;
}

export interface CardExtensionDefinition<CT extends ScreenBase = ScreenBase, NodeType extends ClientNode = any> {
    title?: NestedExtensionField<CT, NestedFieldTypesWithoutTechnical, NodeType>;
    titleRight?: NestedExtensionField<CT, NestedFieldTypesWithoutTechnical, NodeType> | null;
    line2?: NestedExtensionField<CT, NestedFieldTypesWithoutTechnical, NodeType> | null;
    line2Right?: NestedExtensionField<CT, NestedFieldTypesWithoutTechnical, NodeType> | null;
    line3?: NestedExtensionField<CT, NestedFieldTypesWithoutTechnical, NodeType> | null;
    line3Right?: NestedExtensionField<CT, NestedFieldTypesWithoutTechnical, NodeType> | null;
    line4?: NestedExtensionField<CT, NestedFieldTypesWithoutTechnical, NodeType> | null;
    line4Right?: NestedExtensionField<CT, NestedFieldTypesWithoutTechnical, NodeType> | null;
    line5?: NestedExtensionField<CT, NestedFieldTypesWithoutTechnical, NodeType> | null;
    line5Right?: NestedExtensionField<CT, NestedFieldTypesWithoutTechnical, NodeType> | null;
    image?: NestedExtensionField<CT, FieldKey.Image | FieldKey.Icon, NodeType> | null;
    [key: string]: NestedExtensionField<CT, NestedFieldTypes, NodeType> | undefined | null;
}

export interface CardComponentProps<CT extends ScreenBase = ScreenBase, NodeType extends ClientNode = any> {
    areCardFieldTitlesDisplayed?: boolean;
    buttonRef?: React.RefObject<HTMLButtonElement>;
    canDrag?: boolean;
    canSelect?: boolean;
    cardDefinition: CardDefinition<CT, any>;
    contextType?: ContextType;
    dropdownActions?: Array<CollectionItemActionOrMenuSeparator<ScreenBase> | CollectionItemActionGroup<ScreenBase>>;
    id?: string;
    imagePlaceholderMode?: PortraitPlaceholderMode;
    imagePlaceholderValue?: string;
    inlineActions?: Array<InlineCollectionItemAction<ScreenBase>>;
    isClickIndicatorHidden?: boolean;
    isDisabled?: boolean;
    isInFocus?: boolean;
    isNewItem?: boolean;
    isSelected?: boolean;
    /** If the element is clickable then it is rendered into a button element, if not then into a div element */
    onCardClick?: (recordValue: any, isModifierKeyPushed: boolean) => void;
    onSelect?: (recordValue: any) => void;
    parentElementId: string;
    screenId: string;
    shouldDisplayAlphabeticalDivider?: boolean;
    value: NodeType;
}

const isHidden = (
    screenId: string,
    value: CardComponentProps['value'],
    field?: NestedField<any, NestedFieldTypesWithoutTechnical>,
    fieldValue?: any,
): boolean =>
    !!field &&
    !!resolveByValue({
        screenId,
        propertyValue: field.properties.isHidden,
        rowValue: splitValueToMergedValue(value),
        fieldValue,
        skipHexFormat: true,
    });

const renderCard = ({
    areCardFieldTitlesDisplayed = false,
    canSelect,
    cardDefinition,
    contextType,
    dropdownActions,
    inlineActions,
    isClickIndicatorHidden,
    isDisabled,
    isNewItem,
    isSelected,
    onCardClick,
    onSelect,
    parentElementId,
    screenId,
    value,
}: {
    areCardFieldTitlesDisplayed?: boolean;
    canSelect: CardComponentProps['canSelect'];
    cardDefinition: CardComponentProps['cardDefinition'];
    contextType: CardComponentProps['contextType'];
    dropdownActions: CardComponentProps['dropdownActions'];
    inlineActions: CardComponentProps['inlineActions'];
    isClickIndicatorHidden: CardComponentProps['isClickIndicatorHidden'];
    isDisabled: CardComponentProps['isDisabled'];
    isNewItem: CardComponentProps['isNewItem'];
    isSelected: CardComponentProps['isSelected'];
    onCardClick: CardComponentProps['onCardClick'];
    onSelect: CardComponentProps['onSelect'];
    parentElementId: CardComponentProps['parentElementId'];
    screenId: CardComponentProps['screenId'];
    value: CardComponentProps['value'];
}): JSX.Element | null => {
    if (!value) {
        return null;
    }
    const _id = value._id;

    const level = value.__level || 0;
    const cleanValue = cleanMetadataFromRecord(value);

    const titleLineValueId = getNestedFieldElementId(cardDefinition.title);
    const titleRightValueId = getNestedFieldElementId(cardDefinition.titleRight);
    const line2ValueId = getNestedFieldElementId(cardDefinition.line2);
    const line2RightValueId = getNestedFieldElementId(cardDefinition.line2Right);
    const line3ValueId = getNestedFieldElementId(cardDefinition.line3);
    const line3RightValueId = getNestedFieldElementId(cardDefinition.line3Right);
    const line4ValueId = getNestedFieldElementId(cardDefinition.line4);
    const line4RightValueId = getNestedFieldElementId(cardDefinition.line4Right);
    const line5ValueId = getNestedFieldElementId(cardDefinition.line5);
    const line5RightValueId = getNestedFieldElementId(cardDefinition.line5Right);
    const imageValueId = getNestedFieldElementId(cardDefinition.image);
    const progressValueId = getNestedFieldElementId(cardDefinition.progressBar);

    const titleLineValue = getNestedValue(value, titleLineValueId);
    const titleRightValue = getNestedValue(value, titleRightValueId);
    const line2Value = getNestedValue(value, line2ValueId);
    const line2RightValue = getNestedValue(value, line2RightValueId);
    const line3Value = getNestedValue(value, line3ValueId);
    const line3RightValue = getNestedValue(value, line3RightValueId);
    const line4Value = getNestedValue(value, line4ValueId);
    const line4RightValue = getNestedValue(value, line4RightValueId);
    const line5Value = getNestedValue(value, line5ValueId);
    const line5RightValue = getNestedValue(value, line5RightValueId);
    const imageValue = getNestedValue(value, imageValueId);
    const progressValue = getNestedValue(value, progressValueId);

    const titleRightHidden = isHidden(screenId, value, cardDefinition.titleRight, titleRightValue);
    const line2Hidden = isHidden(screenId, value, cardDefinition.line2, line2Value);
    const line2RightHidden = isHidden(screenId, value, cardDefinition.line2Right, line2RightValue);
    const line3Hidden = isHidden(screenId, value, cardDefinition.line3, line3Value);
    const line3RightHidden = isHidden(screenId, value, cardDefinition.line3Right, line3RightValue);
    const line4Hidden = isHidden(screenId, value, cardDefinition.line4, line4Value);
    const line4RightHidden = isHidden(screenId, value, cardDefinition.line4Right, line4RightValue);
    const line5Hidden = isHidden(screenId, value, cardDefinition.line5, line5Value);
    const line5RightHidden = isHidden(screenId, value, cardDefinition.line5Right, line5RightValue);
    const imageHidden = isHidden(screenId, value, cardDefinition.image, imageValue);
    const progressHidden = isHidden(screenId, value, cardDefinition.progressBar, progressValue);

    const commonProperties: NestedFieldWrapperContextProps & { shouldRenderLabelInNestedReadOnlyMode: boolean } = {
        _id,
        contextType,
        handlersArguments: {
            onClick: [_id, cleanValue],
            rowValue: splitValueToMergedValue(cleanValue),
        },
        screenId,
        parentElementId,
        setFieldValue: () => Promise.resolve(),
        shouldRenderLabelInNestedReadOnlyMode: areCardFieldTitlesDisplayed,
    };

    const titleLineDisplayValue = isNewItem
        ? `${titleLineValue} (${localize('@sage/xtrem-ui/new', 'New')})`
        : titleLineValue;

    return (
        <>
            <div className="e-card-body">
                {cardDefinition.image && !imageHidden && (
                    <span className="e-card-image">
                        <ConnectedNestedFieldWrapper
                            {...commonProperties}
                            columnDefinition={cardDefinition.image}
                            columnName={imageValueId}
                            columnProperties={
                                {
                                    ...cardDefinition.image.properties,
                                    isReadOnly: true,
                                    size: 'medium',
                                    isTitleHidden: true,
                                } as ImageDecoratorProperties
                            }
                            value={imageValue}
                            nestedReadOnlyField={true}
                        />
                    </span>
                )}

                <div className="e-card-content">
                    {canSelect && (
                        <div className="e-card-check">
                            <Checkbox
                                checked={isSelected}
                                value="checked"
                                onChange={(): void => onSelect?.(value)}
                                disabled={isDisabled}
                            />
                        </div>
                    )}
                    <div className="e-card-row-container">
                        <div className="e-card-row e-card-row-line-title">
                            <span className="e-card-title">
                                <ConnectedNestedFieldWrapper
                                    {...commonProperties}
                                    nestedReadOnlyField={true}
                                    columnDefinition={cardDefinition.title}
                                    columnName={titleLineValueId}
                                    columnProperties={cardDefinition.title.properties}
                                    value={titleLineDisplayValue}
                                />
                            </span>
                            {cardDefinition.titleRight && !titleRightHidden && (
                                <span className="e-card-row-right">
                                    <ConnectedNestedFieldWrapper
                                        {...commonProperties}
                                        nestedReadOnlyField={true}
                                        columnDefinition={cardDefinition.titleRight}
                                        columnName={titleRightValueId}
                                        columnProperties={
                                            {
                                                ...cardDefinition.titleRight.properties,
                                                isReadOnly: true,
                                            } as EditableFieldProperties
                                        }
                                        value={titleRightValue}
                                    />
                                </span>
                            )}
                        </div>
                        <div className="e-card-row e-card-row-line-2">
                            {cardDefinition.line2 && !line2Hidden && (
                                <div className="e-card-line-2">
                                    <ConnectedNestedFieldWrapper
                                        {...commonProperties}
                                        nestedReadOnlyField={true}
                                        columnDefinition={cardDefinition.line2}
                                        columnName={line2ValueId}
                                        columnProperties={
                                            {
                                                ...cardDefinition.line2.properties,
                                                isReadOnly: true,
                                            } as EditableFieldProperties
                                        }
                                        value={line2Value}
                                    />
                                </div>
                            )}
                            {cardDefinition.line2Right && !line2RightHidden && (
                                <span className="e-card-row-right">
                                    <ConnectedNestedFieldWrapper
                                        {...commonProperties}
                                        nestedReadOnlyField={true}
                                        columnDefinition={cardDefinition.line2Right}
                                        columnName={line2RightValueId}
                                        columnProperties={
                                            {
                                                ...cardDefinition.line2Right.properties,
                                                isReadOnly: true,
                                            } as EditableFieldProperties
                                        }
                                        value={line2RightValue}
                                    />
                                </span>
                            )}
                        </div>
                        <div className="e-card-row e-card-row-line-3">
                            {cardDefinition.line3 && !line3Hidden && (
                                <div className="e-card-line-3">
                                    <ConnectedNestedFieldWrapper
                                        {...commonProperties}
                                        nestedReadOnlyField={true}
                                        columnDefinition={cardDefinition.line3}
                                        columnName={line3ValueId}
                                        columnProperties={
                                            {
                                                ...cardDefinition.line3.properties,
                                                isReadOnly: true,
                                            } as EditableFieldProperties
                                        }
                                        value={line3Value}
                                    />
                                </div>
                            )}
                            {cardDefinition.line3Right && !line3RightHidden && (
                                <span className="e-card-row-right">
                                    <ConnectedNestedFieldWrapper
                                        {...commonProperties}
                                        nestedReadOnlyField={true}
                                        columnDefinition={cardDefinition.line3Right}
                                        columnName={line3RightValueId}
                                        columnProperties={
                                            {
                                                ...cardDefinition.line3Right.properties,
                                                isReadOnly: true,
                                            } as EditableFieldProperties
                                        }
                                        value={line3RightValue}
                                    />
                                </span>
                            )}
                        </div>
                        <div className="e-card-row e-card-row-line-4">
                            {cardDefinition.line4 && !line4Hidden && (
                                <div className="e-card-line-4">
                                    <ConnectedNestedFieldWrapper
                                        {...commonProperties}
                                        nestedReadOnlyField={true}
                                        columnDefinition={cardDefinition.line4}
                                        columnName={line4ValueId}
                                        columnProperties={
                                            {
                                                ...cardDefinition.line4.properties,
                                                isReadOnly: true,
                                            } as EditableFieldProperties
                                        }
                                        value={line4Value}
                                    />
                                </div>
                            )}
                            {cardDefinition.line4Right && !line4RightHidden && (
                                <span className="e-card-row-right">
                                    <ConnectedNestedFieldWrapper
                                        {...commonProperties}
                                        nestedReadOnlyField={true}
                                        columnDefinition={cardDefinition.line4Right}
                                        columnName={line4RightValueId}
                                        columnProperties={
                                            {
                                                ...cardDefinition.line4Right.properties,
                                                isReadOnly: true,
                                            } as EditableFieldProperties
                                        }
                                        value={line4RightValue}
                                    />
                                </span>
                            )}
                        </div>
                        <div className="e-card-row e-card-row-line-5">
                            {cardDefinition.line5 && !line5Hidden && (
                                <div className="e-card-line-5">
                                    <ConnectedNestedFieldWrapper
                                        {...commonProperties}
                                        nestedReadOnlyField={true}
                                        columnDefinition={cardDefinition.line5}
                                        columnName={line5ValueId}
                                        columnProperties={
                                            {
                                                ...cardDefinition.line5.properties,
                                                isReadOnly: true,
                                            } as EditableFieldProperties
                                        }
                                        value={line5Value}
                                    />
                                </div>
                            )}
                            {cardDefinition.line5Right && !line5RightHidden && (
                                <span className="e-card-row-right">
                                    <ConnectedNestedFieldWrapper
                                        {...commonProperties}
                                        nestedReadOnlyField={true}
                                        columnDefinition={cardDefinition.line5Right}
                                        columnName={line5RightValueId}
                                        columnProperties={
                                            {
                                                ...cardDefinition.line5Right.properties,
                                                isReadOnly: true,
                                            } as EditableFieldProperties
                                        }
                                        value={line5RightValue}
                                    />
                                </span>
                            )}
                        </div>
                    </div>
                    {inlineActions && inlineActions.length > 0 && (
                        <ConnectedTableInlineActions
                            screenId={screenId}
                            fieldId={parentElementId}
                            recordId={value._id}
                            level={level}
                            rowValue={value}
                            actions={inlineActions}
                        />
                    )}
                    {dropdownActions && dropdownActions.length > 0 && (
                        <ConnectedTableDropdownActions
                            screenId={screenId}
                            fieldId={parentElementId}
                            recordId={value._id}
                            level={level}
                            rowValue={value}
                        />
                    )}
                    {onCardClick && parentElementId !== '$navigationPanel' && !isClickIndicatorHidden && (
                        <div className="e-card-row-chevron-container">
                            <IconButton
                                aria-label={localize('@sage/xtrem-ui/select-record', 'Select record')}
                                data-testid="e-card-row-chevron-right"
                                onClick={(): void => {}}
                            >
                                <Icon
                                    type="chevron_right"
                                    color={tokens.colorsYin090}
                                    tooltipMessage={localize('@sage/xtrem-ui/select-record', 'Select record')}
                                />
                            </IconButton>
                        </div>
                    )}
                </div>
            </div>
            {progressValueId && !progressHidden && cardDefinition.progressBar && (
                <div
                    className={
                        !cardDefinition.progressBar.properties.areProgressLabelsHidden
                            ? 'e-card-row-progress-bar-container'
                            : 'e-card-row-progress-bar-container e-progress-bar-label-hidden'
                    }
                >
                    <ProgressTracker
                        length="100%"
                        progress={progressValue || 0}
                        currentProgressLabel={
                            cardDefinition.progressBar.properties.areProgressLabelsHidden
                                ? undefined
                                : cardDefinition.progressBar.properties.currentProgressLabel
                        }
                        maxProgressLabel={
                            cardDefinition.progressBar.properties.areProgressLabelsHidden
                                ? undefined
                                : cardDefinition.progressBar.properties.maxProgressLabel
                        }
                    />
                </div>
            )}
        </>
    );
};

export const CardComponent = React.memo(
    <CT extends ScreenBase = ScreenBase, NodeType extends ClientNode = any>(
        props: CardComponentProps<CT, NodeType>,
    ) => {
        const {
            areCardFieldTitlesDisplayed,
            buttonRef,
            canSelect,
            cardDefinition,
            contextType,
            dropdownActions,
            id,
            inlineActions,
            isClickIndicatorHidden,
            isDisabled,
            isInFocus,
            isNewItem,
            isSelected,
            onCardClick,
            onSelect,
            parentElementId,
            screenId,
            value,
        } = props;

        const wrapperClassName = ['e-card'];

        if (isSelected) {
            wrapperClassName.push('e-card-selected');
        }

        if (cardDefinition.image) {
            wrapperClassName.push('e-card-has-image');
        }

        if (contextType) {
            wrapperClassName.push(`e-context-${contextType}`);
        }

        // We need to set the tab index to -1 to prevent some items from attracting focus.
        const focusTabIndex = isInFocus ? 0 : -1;
        const tabIndex = isInFocus === undefined ? undefined : focusTabIndex;

        const onClickHandler = (event: React.MouseEvent<HTMLButtonElement>): void => {
            if ((event.target as HTMLInputElement).type !== 'checkbox' && onCardClick && !isDisabled) {
                onCardClick(value, event.ctrlKey || event.metaKey);
            }
        };

        const sharedProps: React.HTMLAttributes<HTMLElement> = {
            className: wrapperClassName.join(' '),
            id,
        };

        if (props.canDrag) {
            sharedProps.draggable = true;
            sharedProps.unselectable = 'on';
            sharedProps.onDragStart = (e: React.DragEvent<HTMLElement>): void => {
                e.dataTransfer.setData('application/json', JSON.stringify(value));
            };
        }

        const wrapperElement = onCardClick ? (
            <button
                {...sharedProps}
                onClick={onClickHandler}
                type="button"
                ref={buttonRef}
                tabIndex={tabIndex}
                disabled={isDisabled}
            >
                {renderCard({
                    areCardFieldTitlesDisplayed,
                    canSelect,
                    cardDefinition,
                    contextType,
                    dropdownActions,
                    inlineActions,
                    isClickIndicatorHidden,
                    isDisabled,
                    isNewItem,
                    isSelected,
                    onCardClick,
                    onSelect,
                    parentElementId,
                    screenId,
                    value,
                })}
            </button>
        ) : (
            <div {...sharedProps}>
                {renderCard({
                    areCardFieldTitlesDisplayed,
                    canSelect,
                    cardDefinition,
                    contextType,
                    dropdownActions,
                    inlineActions,
                    isClickIndicatorHidden,
                    isDisabled,
                    isNewItem,
                    isSelected,
                    onCardClick,
                    onSelect,
                    parentElementId,
                    screenId,
                    value,
                })}
            </div>
        );

        const titleTextValue = getImagePlaceholderValue(value, cardDefinition.title);
        const firstChar = titleTextValue ? String(titleTextValue).charAt(0).toUpperCase() : undefined;
        const separatorContent = firstChar && firstChar !== ' ' ? firstChar : '_';

        return (
            <div data-label={`${titleTextValue}`} data-testid="e-card">
                {props.shouldDisplayAlphabeticalDivider ? (
                    <div className="e-horizontal-separator">{separatorContent}</div>
                ) : null}
                {wrapperElement}
            </div>
        );
    },
);

CardComponent.displayName = 'CardComponent';
