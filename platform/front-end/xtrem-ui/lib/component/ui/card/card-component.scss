.e-card {

    padding: 0;
    margin: 0;
    width: 100%;

    min-height: 78px;
    border: 1px solid var(--colorsUtilityMajor100);
    background-color: var(--colorsYang100);

    .e-card-body {
        position: relative;
        font-family: var(--fontFamiliesDefault);
        display: flex;
        box-sizing: border-box;
        padding: 0;
        margin: 0;
        width: 100%;
        min-height: 78px;
    }

    &.e-context-page-header {
        background-color: var(--colorsUtilityMajor025);

        .e-card-content {
            padding: 12px 14px 12px 14px;
        }
    }

    &:focus {
        box-shadow: inset 0 0 0 2px var(--colorsSemanticFocus500);
        outline: 0;
        z-index: 10;
    }

    &.e-card-has-image .e-card-content {
        width: calc(100% - 60px);
    }

    .e-card-image {
        padding-left: 16px;
        margin: 12px 0;

        .e-image-field-content-wrapper {
            width: 40px;

            img {
                max-height: 40px;
                max-width: 40px;
                border: 0;
            }
        }
    }

    .e-field {
        padding: 0;

        .common-input__help-text {
            display: none;
        }
    }

    .e-card-loading {
        padding: 4px 8px;
        border-bottom: 1px solid var(--colorsUtilityMajor100);
        display: flex;
        justify-content: center;
    }

    &.e-card-selected {
        background-color: var(--colorsUtilityMajor100);
        border-bottom: 1px solid var(--colorsUtilityMajor100);
    }

    .e-card-row {
        display: flex;
        min-height: 24px;

        &:empty {
            display: none;
        }

        .e-card-title,
        .e-card-line-2,
        .e-card-line-3 {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .e-card-title .e-field-nested-no-input {
            font-weight: var(--fontWeights500);
            font-family: var(--fontFamiliesDefault);
            font-size: 16px;
        }

        .e-card-subtitle-row-right {
            flex: 1;
            font-size: 1rem;
        }

        .e-card-row-right {
            flex: 1;
            font-size: 1rem;
            margin-left: 12px;

            .e-field,
            .e-field-nested-no-input {
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                text-align: right;
                width: 100%;

                >span {
                    display: block;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    box-sizing: border-box;
                    max-width: none;
                }
            }

            .e-field.e-label-field {
                overflow-y: hidden;

                span.e-pill-wrapper {
                    display: inline-block;
                }
            }
        }
    }

    .e-card-content {
        display: flex;
        width: 100%;
        align-items: center;
        box-sizing: border-box;
        padding: 12px 8px 12px 15px;
        margin: auto 0;
        position: relative;

        &.e-card-content-skeleton {
            display: block;

            .e-card-content-skeleton-row {
                padding: 4px 0;
            }
        }

        .e-field {
            width: 100%;
        }

        .e-field-read-only {
            width: 100%;
            min-height: 0;
            padding: 0;
            line-height: 18px;
            font-size: 12px;
            font-weight: 500;

            @include extra_small {
                font-size: 14px;
            }
        }

        .e-card-check {
            padding-right: 16px;
            padding-left: 8px;
            text-align: left;
            width: 24px;
        }

        .e-card-row-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 100%;
            overflow: hidden;

            .e-card-row {
                scrollbar-width: none;
                line-height: 24px;
                padding-left: 0;
                padding-right: 0;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;

                --webkit-scrollbar {
                    display: none;
                    width: 0;
                }

                &.e-card-row-line-title {
                    font-weight: var(--fontWeights700);

                    @include extra_small {
                        font-size: 16px;
                    }
                }

                .e-card-line-2 {
                    display: flex;

                    .e-field-nested {
                        overflow: hidden;
                    }
                }

                .e-card-new-text {
                    font-weight: var(--fontWeights700);
                    font-family: var(--fontFamiliesDefault);
                }

                [data-component="pill"] {
                    margin-top: 0;
                }
            }
        }

        .e-card-row-chevron-container {
            padding: 6px 4px 0 16px;

            button {
                span::before {
                    font-size: 20px;
                }
            }
        }

        .e-action-popover-mobile,
        [data-component="action-popover-wrapper"] {
            padding-left: 16px;
        }

        .e-table-inline-action {
            margin-left: 16px;
            min-width: 32px;
        }
    }
}

// The card is rendered as a button if it has an onClick listener
button.e-card:hover {
    background-color: #D9ECE7;
    cursor: pointer;
}