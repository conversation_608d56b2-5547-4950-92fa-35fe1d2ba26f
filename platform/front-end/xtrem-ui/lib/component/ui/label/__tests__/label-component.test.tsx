import React from 'react';
import { LabelComponent } from '../label-component';
import { render } from '@testing-library/react';
import * as stateUtils from '../../../../utils/state-utils';
import { getMockPageDefinition } from '../../../../__tests__/test-helpers';

jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() => getMockPageDefinition('TestScreenId'));

describe('Label UI component', () => {
    it('should always call resolveByValue function with the rawValue', () => {
        const color = jest.fn();
        const borderColor = jest.fn();
        const backgroundColor = jest.fn();
        const prefix = jest.fn();
        const postfix = jest.fn();

        expect(color).not.toHaveBeenCalled();
        expect(borderColor).not.toHaveBeenCalled();
        expect(backgroundColor).not.toHaveBeenCalled();
        expect(prefix).not.toHaveBeenCalled();
        expect(postfix).not.toHaveBeenCalled();

        const rowValue = { asd: 'anyValue' };
        const rawValue = 'testRawValue';
        render(
            <LabelComponent
                screenId="TestScreenId"
                rowValue={rowValue}
                rawValue={rawValue}
                value="testValue"
                color={color}
                backgroundColor={backgroundColor}
                borderColor={borderColor}
                prefix={prefix}
                postfix={postfix}
            />,
        );

        expect(color).toHaveBeenCalledTimes(1);
        expect(color).toHaveBeenCalledWith(rawValue, rowValue);

        expect(borderColor).toHaveBeenCalledTimes(1);
        expect(borderColor).toHaveBeenCalledWith(rawValue, rowValue);

        expect(backgroundColor).toHaveBeenCalledTimes(1);
        expect(backgroundColor).toHaveBeenCalledWith(rawValue, rowValue);

        expect(prefix).toHaveBeenCalledTimes(1);
        expect(prefix).toHaveBeenCalledWith(rawValue, rowValue);

        expect(postfix).toHaveBeenCalledTimes(1);
        expect(postfix).toHaveBeenCalledWith(rawValue, rowValue);
    });
});
