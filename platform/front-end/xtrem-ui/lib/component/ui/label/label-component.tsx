import Pill from 'carbon-react/esm/components/pill';
import React from 'react';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import type { ValueOrCallbackWithFieldValue } from '../../../utils/types';
import type { LabelProperties } from '../../control-objects';
import * as tokens from '@sage/design-tokens/js/base/common';
import { isNil } from 'lodash';
import { splitValueToMergedValue } from '../../../utils/transformers';

export interface LabelProps extends LabelProperties {
    value?: string;
    rawValue?: string | number;

    isDisabled?: boolean;
    onClick?: () => void;
    rowValue?: any;
    prefix?: ValueOrCallbackWithFieldValue<any, string, string, any>;
    postfix?: ValueOrCallbackWithFieldValue<any, string, string, any>;
    backgroundColor?: ValueOrCallbackWithFieldValue<any, string, string, any>;
    borderColor?: ValueOrCallbackWithFieldValue<any, string, string, any>;
    color?: ValueOrCallbackWithFieldValue<any, string, string, any>;
    size?: 'M' | 'S';
    screenId: string;
    style?: ValueOrCallbackWithFieldValue<
        any,
        { borderColor?: string; backgroundColor?: string; textColor?: string },
        string,
        any
    >;
}

export const LabelComponent: React.FC<LabelProps> = React.memo((props: LabelProps) => {
    // Value
    const preformattedValue = props.value || ' ';
    let formattedValue = preformattedValue;
    if (props.prefix) {
        const prefix = resolveByValue({
            fieldValue: isNil(props.rawValue) ? preformattedValue : props.rawValue,
            propertyValue: props.prefix,
            rowValue: props.rowValue ? splitValueToMergedValue(props.rowValue) : undefined,
            skipHexFormat: true,
            screenId: props.screenId,
        });

        formattedValue = `${prefix} ${formattedValue}`;
    }

    if (props.postfix) {
        const postfix = resolveByValue({
            fieldValue: isNil(props.rawValue) ? preformattedValue : props.rawValue,
            propertyValue: props.postfix,
            rowValue: props.rowValue ? splitValueToMergedValue(props.rowValue) : undefined,
            skipHexFormat: true,
            screenId: props.screenId,
        });

        formattedValue = `${formattedValue} ${postfix}`;
    }

    const resolvedStyleProp = resolveByValue<{ borderColor?: string; backgroundColor?: string; textColor?: string }>({
        fieldValue: props.rowValue?._id,
        propertyValue: props.style,
        rowValue: props.rowValue ? splitValueToMergedValue(props.rowValue) : undefined,
        screenId: props.screenId,
        skipHexFormat: true,
    });

    // Style
    const style: React.CSSProperties = props.isDisabled
        ? {
              background: tokens.colorsUtilityMajor050,
              borderColor: tokens.colorsUtilityMajor300,
              color: tokens.colorsUtilityMajor300,
              cursor: 'default',
          }
        : {
              backgroundColor:
                  resolvedStyleProp?.backgroundColor ??
                  resolveByValue({
                      fieldValue: isNil(props.rawValue) ? preformattedValue : props.rawValue,
                      propertyValue: props.backgroundColor,
                      rowValue: props.rowValue ? splitValueToMergedValue(props.rowValue) : undefined,
                      screenId: props.screenId,
                  }),
              borderColor:
                  resolvedStyleProp?.borderColor ??
                  resolveByValue({
                      fieldValue: isNil(props.rawValue) ? preformattedValue : props.rawValue,
                      propertyValue: props.borderColor,
                      rowValue: props.rowValue ? splitValueToMergedValue(props.rowValue) : undefined,
                      screenId: props.screenId,
                  }),
              color:
                  resolvedStyleProp?.textColor ??
                  resolveByValue({
                      fieldValue: isNil(props.rawValue) ? preformattedValue : props.rawValue,
                      propertyValue: props.color,
                      rowValue: props.rowValue ? splitValueToMergedValue(props.rowValue) : undefined,
                      screenId: props.screenId,
                  }),
              cursor: props.onClick ? 'pointer' : 'default',
          };

    return (
        <span className="e-pill-wrapper">
            <Pill fill={false} onClick={props.onClick} style={style} size={props.size || 'M'}>
                {formattedValue}
            </Pill>
        </span>
    );
});

LabelComponent.displayName = 'LabelComponent';
