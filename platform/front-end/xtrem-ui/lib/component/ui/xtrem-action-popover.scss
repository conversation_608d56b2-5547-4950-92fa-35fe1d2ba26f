@keyframes e-action-popover-mobile-background-fade-in {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes e-action-popover-mobile-content-move-in {
    0% {
        max-height: 0;
    }

    100% {
        max-height: 50%;
    }
}

.e-action-popover-icon {
    min-height: auto;

    &:disabled {
        opacity: 0.3;
    }
}

.e-action-popover-icon span {
    color: var(--colorsUtilityMajor500) !important;
    margin-right: 0px !important;
}

.e-action-popover-mobile {
    margin-top: 2px;
    margin-left: auto;

    .e-action-popover-mobile-button {
        padding-left: 8px;
        padding-right: 8px;
        height: 40px;
        line-height: 40px;
        padding-top: 2px;

        >div {

            >span {
                &::before {
                    font-size: 1.667rem;
                }
            }
        }
    }

    .e-action-popover-mobile-background {
        background: rgba(0, 20, 29, 0.6);
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 10;
        opacity: 1;
        animation-name: e-action-popover-mobile-background-fade-in;
        animation-iteration-count: 1;
        animation-timing-function: ease-in;
        animation-duration: 0.3s;
    }

    .e-action-popover-mobile-content {
        box-shadow: var(--boxShadow200);
        background: var(--colorsYang100);
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        max-height: 50%;
        z-index: 10;
        animation-name: e-action-popover-mobile-content-move-in;
        animation-iteration-count: 1;
        animation-timing-function: ease-in;
        animation-duration: 0.3s;
        overflow-y: auto;
    }

    &.e-action-popover-mobile-over-sidebar {
        .e-action-popover-mobile-background {
            z-index: 5001;
        }

        .e-action-popover-mobile-content {
            z-index: 5010;
        }
    }
}


ul[data-component="action-popover"][role="list"] {
    button {
        span {
            color: var(--colorsYin090);
        }
    }

    button[aria-disabled="true"] {
        span {
            color: var(--colorsUtilityYin030);
        }
    }

    .e-action-popover-item-destructive span[data-component="icon"] {
        color: var(--colorsSemanticNegative500);
    }

    span[data-element="chevron_left"] {
        color: var(--colorsYin090);
    }

    .e-action-popover-item-destructive span:first-of-type:not([data-component="icon"]) {
        color: var(--colorsSemanticNegative500);
    }

}

body.action-popover-submenus-compat {
    overflow: hidden;
}

div.e-action-popover-mobile-content {
    button {
        color: var(--colorsYin090);
        font-size: 18px;
        height: 4px;

        >:first-child {
            margin-right: 12px;
        }


        >:last-child {
            flex: 1;
            display: flex;

            >span {
                flex: 1;
                display: flex;
            }

            .e-action-popover-item-mobile-label {
                flex: 1;
                text-align: left;
            }
        }

        &.e-action-popover-item-level-0 {
            padding-left: 12px;
        }

        &.e-action-popover-item-level-1 {
            padding-left: 28px;
        }

        &.e-action-popover-item-level-2 {
            padding-left: 44px;
        }

        &.e-action-popover-item-level-3 {
            padding-left: 58px;
        }
    }

    .e-action-popover-item-list-wrapper {
        padding: 0 8px;
    }
}