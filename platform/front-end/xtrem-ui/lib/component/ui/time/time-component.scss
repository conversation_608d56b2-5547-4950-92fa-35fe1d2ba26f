.e-time-field {
    .e-time-field-container {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0.75rem;

        [data-component="button-toggle-group"] {
            flex-wrap: nowrap;

            button {
                padding: 0 var(--spacing050);
                font-size: revert;
            }
        }

        .e-time-field-input-container {
            > div {
                flex: 1;
                align-items: center;
                display: flex;
                justify-content: space-evenly;
            }
        
            // emulate Carbon's focus styles
            &:has(input:focus) {
                box-shadow:
                    0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),
                    0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
                outline: transparent solid 3px;
                appearance: none;
                z-index: 2;
            }

            background: var(--colorsUtilityYang100);
            border: 1px solid var(--colorsUtilityMajor300);
            box-sizing: border-box;
            cursor: text;
            border-radius: var(--borderRadius050);

            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0 var(--spacing075);
            font-size: var(--fontSizes100);
            min-height: var(--sizing500);

            &[data-full-width="true"] {
                flex: 1;
            }

            &[data-valid="false"] {
                box-shadow:
                    inset 1px 1px 0 var(--colorsSemanticNegative500),
                    inset -1px -1px 0 var(--colorsSemanticNegative500);
                border-color: var(--colorsSemanticNegative500);
                border-width: 1px;
            }

            &[data-read-only="true"] {
                background: transparent;
                outline: none;
                color: var(--colorsUtilityYin030);
                cursor: not-allowed;
            }

            input {
                appearance: none;
                min-width: 0;
                outline: none;
                background: transparent;
                border: none;
                margin: 0;
                padding: 0;
                text-align: center;
                flex: 1;

                // remove arrows from number input
                &[type="number"]::-webkit-inner-spin-button,
                &[type="number"]::-webkit-outer-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }
            }

            flex-basis: 120px;

            span {
                line-height: var(--fontSizes100);
            }

            &[data-disabled="true"] {
                background: unset;
                border-color: var(--colorsUtilityMajor200);
                cursor: not-allowed;

                input {
                    color: var(--colorsYin030);
                    cursor: not-allowed;
                }

                span {
                    color: var(--colorsYin030);
                }
            }
        }
    }
}
