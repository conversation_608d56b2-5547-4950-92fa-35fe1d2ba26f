import { formatString, getValueFromString, validateHours, validateMinutes } from '../time-utils';

describe('Time Utils', () => {
    it('formatString', () => {
        expect(formatString('   , , -   ')).toBe('');
    });

    it('validateHours', () => {
        // :XX AM
        expect(validateHours({ hours: '', toggle: 'AM', hasAmPmToggle: true })).toEqual({
            isValid: true,
            hours: 0,
            hoursString: '',
        });

        // 12:XX AM
        expect(validateHours({ hours: '12', toggle: 'AM', hasAmPmToggle: true })).toEqual({
            isValid: true,
            hours: 0,
            hoursString: '12',
        });

        // 12:XX PM
        expect(validateHours({ hours: '12', toggle: 'PM', hasAmPmToggle: true })).toEqual({
            isValid: true,
            hours: 12,
            hoursString: '12',
        });

        // :XX PM
        expect(validateHours({ hours: '', toggle: 'PM', hasAmPmToggle: true })).toEqual({
            isValid: true,
            hours: 12,
            hoursString: '',
        });

        // 14:XX PM
        expect(validateHours({ hours: '14', toggle: 'PM', hasAmPmToggle: false })).toEqual({
            isValid: true,
            hours: 14,
            hoursString: '14',
        });

        // 22:XX AM
        expect(validateHours({ hours: '22', toggle: 'AM', hasAmPmToggle: false })).toEqual({
            isValid: true,
            hours: 22,
            hoursString: '22',
        });

        // 10:XX AM
        expect(validateHours({ hours: '10', toggle: 'AM', hasAmPmToggle: false })).toEqual({
            isValid: true,
            hours: 10,
            hoursString: '10',
        });

        // 10:XX PM
        expect(validateHours({ hours: '10', toggle: 'AM', hasAmPmToggle: true })).toEqual({
            isValid: true,
            hours: 10,
            hoursString: '10',
        });

        // 0:XX PM
        expect(validateHours({ hours: '0', toggle: 'PM', hasAmPmToggle: true })).toEqual({
            isValid: true,
            hoursString: '0',
            hours: 12,
        });

        // 0:XX AM
        expect(validateHours({ hours: '0', toggle: 'AM', hasAmPmToggle: true })).toEqual({
            isValid: true,
            hoursString: '0',
            hours: 0,
        });

        // 0:XX
        expect(validateHours({ hours: '0', toggle: null, hasAmPmToggle: false })).toEqual({
            isValid: true,
            hoursString: '0',
            hours: 0,
        });

        // 00:XX
        expect(validateHours({ hours: '00', toggle: null, hasAmPmToggle: false })).toEqual({
            isValid: true,
            hoursString: '00',
            hours: 0,
        });

        // 000:XX
        expect(validateHours({ hours: '00', toggle: null, hasAmPmToggle: false })).toEqual({
            isValid: true,
            hoursString: '00',
            hours: 0,
        });

        // 13:XX AM
        expect(validateHours({ hours: '13', toggle: 'AM', hasAmPmToggle: true })).toEqual({
            isValid: false,
            hoursString: '13',
        });

        // 24:XX AM
        expect(validateHours({ hours: '24', toggle: 'AM', hasAmPmToggle: true })).toEqual({
            isValid: false,
            hoursString: '24',
        });

        // 24:XX PM
        expect(validateHours({ hours: '24', toggle: 'AM', hasAmPmToggle: true })).toEqual({
            isValid: false,
            hoursString: '24',
        });

        // 23:XX
        expect(validateHours({ hours: '23', toggle: 'unknown', hasAmPmToggle: true })).toEqual({
            isValid: true,
            hoursString: '11',
            hours: 23,
        });

        // 23:XX
        expect(validateHours({ hours: '23', toggle: 'unknown', hasAmPmToggle: false })).toEqual({
            isValid: true,
            hoursString: '23',
            hours: 23,
        });

        // 11:XX
        expect(validateHours({ hours: '11', toggle: 'unknown', hasAmPmToggle: true })).toEqual({
            isValid: true,
            hoursString: '11',
            hours: 11,
        });

        // 123:XX
        expect(validateHours({ hours: '123', toggle: 'unknown', hasAmPmToggle: true })).toEqual({
            isValid: false,
            hoursString: '12',
        });

        // 12:XX
        expect(validateHours({ hours: '12', toggle: 'unknown', hasAmPmToggle: true })).toEqual({
            isValid: true,
            hours: 12,
            hoursString: '12',
        });

        // 12:XX PM
        expect(validateHours({ hours: '12', toggle: 'PM', hasAmPmToggle: true })).toEqual({
            isValid: true,
            hours: 12,
            hoursString: '12',
        });
    });

    it('validateMinutes', () => {
        // XX:10
        expect(validateMinutes('10')).toEqual({
            isValid: true,
            minutes: 10,
            minutesString: '10',
        });

        // XX:1
        expect(validateMinutes('1')).toEqual({
            isValid: true,
            minutes: 1,
            minutesString: '1',
        });

        // XX:0
        expect(validateMinutes('0')).toEqual({
            isValid: true,
            minutes: 0,
            minutesString: '0',
        });

        // XX:00
        expect(validateMinutes('00')).toEqual({
            isValid: true,
            minutes: 0,
            minutesString: '00',
        });

        // XX:000
        expect(validateMinutes('000')).toEqual({
            isValid: true,
            minutesString: '00',
            minutes: 0,
        });
    });

    it('getValueFromString', () => {
        expect(getValueFromString({ time: '23:10:11', hasAmPmToggle: true })).toEqual({
            hours: 23,
            hoursString: '11',
            minutes: 10,
            minutesString: '10',
            seconds: 11,
            toggle: 'PM',
            value: '23:10:11',
        });

        expect(getValueFromString({ time: '23:10:11', hasAmPmToggle: false })).toEqual({
            hours: 23,
            hoursString: '23',
            minutes: 10,
            minutesString: '10',
            seconds: 11,
            toggle: 'PM',
            value: '23:10:11',
        });

        expect(getValueFromString({ time: '12:10:11', hasAmPmToggle: true })).toEqual({
            hours: 12,
            hoursString: '12',
            minutes: 10,
            minutesString: '10',
            seconds: 11,
            toggle: 'PM',
            value: '12:10:11',
        });

        expect(getValueFromString({ time: '12:10:11', hasAmPmToggle: false })).toEqual({
            hours: 12,
            hoursString: '12',
            minutes: 10,
            minutesString: '10',
            seconds: 11,
            toggle: 'PM',
            value: '12:10:11',
        });

        expect(getValueFromString({ time: '00:10:11', hasAmPmToggle: true })).toEqual({
            hours: 0,
            hoursString: '12',
            minutes: 10,
            minutesString: '10',
            seconds: 11,
            toggle: 'AM',
            value: '00:10:11',
        });

        expect(getValueFromString({ time: '00:10:11', hasAmPmToggle: false })).toEqual({
            hours: 0,
            hoursString: '00',
            minutes: 10,
            minutesString: '10',
            seconds: 11,
            toggle: 'AM',
            value: '00:10:11',
        });

        expect(getValueFromString({ time: null, hasAmPmToggle: false })).toEqual({
            hours: 0,
            hoursString: '',
            minutes: 0,
            minutesString: '',
            seconds: 0,
            toggle: null,
            value: null,
        });

        expect(() => {
            getValueFromString({ time: '24:00:00', hasAmPmToggle: false });
        }).toThrow('Invalid date time: 24:00:00');

        expect(() => {
            getValueFromString({ time: '-01:00:00', hasAmPmToggle: false });
        }).toThrow('Invalid date time: -01:00:00');

        expect(() => {
            getValueFromString({ time: '25:00:00', hasAmPmToggle: true });
        }).toThrow('Invalid date time: 25:00:00');

        expect(() => {
            getValueFromString({ time: '00:60:00', hasAmPmToggle: true });
        }).toThrow('Invalid date time: 00:60:00');

        expect(() => {
            getValueFromString({ time: '00:-01:00', hasAmPmToggle: true });
        }).toThrow('Invalid date time: 00:-01:00');

        expect(() => {
            getValueFromString({ time: '019:20:00', hasAmPmToggle: true });
        }).toThrow('Invalid date time: 019:20:00');

        expect(() => {
            getValueFromString({ time: '19:20:001', hasAmPmToggle: true });
        }).toThrow('Invalid date time: 19:20:001');
    });
});
