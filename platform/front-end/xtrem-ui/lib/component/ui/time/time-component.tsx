import { ButtonToggle, ButtonToggleGroup } from 'carbon-react/esm/components/button-toggle';
import Icon from 'carbon-react/esm/components/icon';
import React from 'react';
import type { TimeComponentProps } from './time-types';

export const TimeComponent: React.FC<TimeComponentProps> = React.memo(props => {
    const {
        screenId,
        elementId,
        dataTestId,
        fieldId,
        hasAmPmToggle,
        hoursRef,
        isDisabled,
        isFullWidth,
        isReadOnly,
        isValid,
        localize,
        maxHours,
        minHours,
        minutesRef,
        onClick,
        onHoursBlur,
        onHoursChange,
        onKeyDown,
        onMinutesBlur,
        onMinutesChange,
        onToggleChange,
        state,
    } = props;

    return (
        <div className="e-time-field-container" onClick={onClick}>
            <div
                aria-labelledby={fieldId}
                className="e-time-field-input-container"
                data-full-width={isFullWidth ? 'true' : 'false'}
                data-read-only={isReadOnly ? 'true' : 'false'}
                data-valid={String(isValid)}
                data-disabled={String(isDisabled)}
            >
                <div>
                    <input
                        ref={hoursRef}
                        aria-label={localize('@sage/xtrem-ui/hours', 'Hours')}
                        data-testid={`${dataTestId}-hours`}
                        disabled={isDisabled}
                        max={maxHours}
                        min={minHours}
                        onBlur={onHoursBlur}
                        onChange={onHoursChange}
                        onKeyDown={onKeyDown}
                        readOnly={isReadOnly}
                        step={1}
                        type="number"
                        value={state.hoursString}
                    />
                    <span>:</span>
                    <input
                        ref={minutesRef}
                        aria-label={localize('@sage/xtrem-ui/minutes', 'Minutes')}
                        data-testid={`${dataTestId}-minutes`}
                        disabled={isDisabled}
                        max={59}
                        min={0}
                        onBlur={onMinutesBlur}
                        onChange={onMinutesChange}
                        onKeyDown={onKeyDown}
                        readOnly={isReadOnly}
                        step={1}
                        type="number"
                        value={state.minutesString}
                    />
                </div>
                <Icon type="clock" color="--colorsUtilityYin065" fontSize="small" />
            </div>
            {hasAmPmToggle && (
                <ButtonToggleGroup
                    fullWidth={true}
                    id={`e-time-field-${screenId}-${elementId}-toggle`}
                    onChange={onToggleChange}
                    value={state.toggle ?? undefined}
                    disabled={isReadOnly || isDisabled}
                >
                    <ButtonToggle value="AM" aria-label="AM">
                        AM
                    </ButtonToggle>
                    <ButtonToggle data-testid={`${dataTestId}-toggle-pm`} value="PM" aria-label="PM">
                        PM
                    </ButtonToggle>
                </ButtonToggleGroup>
            )}
        </div>
    );
});

TimeComponent.displayName = 'TimeComponent';
