import type { Ref } from 'react';
import type { TimeComponentState } from './use-time';

export interface TimeComponentProps {
    screenId?: string;
    elementId?: string;
    dataTestId: string;
    fieldId?: string;
    hasAmPmToggle: boolean;
    hoursRef: Ref<HTMLInputElement>;
    isDisabled?: boolean;
    isFullWidth?: boolean;
    isReadOnly?: boolean;
    isValid?: boolean;
    localize: (key: string, defaultMessage: string) => string;
    maxHours: number;
    minHours: number;
    minutesRef: Ref<HTMLInputElement>;
    onClick?: () => void;
    onHoursBlur: NonNullable<React.ComponentProps<'input'>['onBlur']>;
    onHoursChange: NonNullable<React.ComponentProps<'input'>['onChange']>;
    onKeyDown: NonNullable<React.ComponentProps<'input'>['onKeyDown']>;
    onMinutesBlur: NonNullable<React.ComponentProps<'input'>['onBlur']>;
    onMinutesChange: NonNullable<React.ComponentProps<'input'>['onChange']>;
    onToggleChange?: (
        ev: React.MouseEvent<HTMLButtonElement, MouseEvent>,
        value?: string | undefined,
        name?: string | undefined,
    ) => void;
    state: TimeComponentState;
}

export type ToggleState = 'AM' | 'PM' | null;
