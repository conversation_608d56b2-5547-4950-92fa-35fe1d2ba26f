import { isNil } from 'lodash';
import type { ToggleState } from './time-types';
import type { TimeComponentState } from './use-time';

export const TIME_REGEX = /^(\d{2}):(\d{2}):(\d{2})$/;

export function formatString(value: string): string {
    return value.replaceAll(/[.,-]/g, '').trim().slice(0, 2);
}

const getHoursString = (
    toggle: ToggleState | 'unknown',
    hasAmPmToggle: boolean,
    hours24: number,
    hours: string,
): string => {
    if (toggle === 'unknown' && hasAmPmToggle) {
        if (hours24 > 12) {
            return formatString(String(hours24 - 12).padStart(2, '0'));
        }
        if (hours24 === 0) {
            return formatString(String(hours24 + 12).padStart(2, '0'));
        }
    }
    return formatString(hours);
};

export function validateHours({
    hours,
    toggle,
    hasAmPmToggle,
}: {
    hours: TimeComponentState['hoursString'];
    toggle: TimeComponentState['toggle'] | 'unknown';
    hasAmPmToggle: boolean;
}):
    | { isValid: true; hours: TimeComponentState['hours']; hoursString: TimeComponentState['hoursString'] }
    | { isValid: false; hoursString: TimeComponentState['hoursString'] } {
    if (hours === '') {
        return { isValid: true, hoursString: '', hours: hasAmPmToggle && toggle === 'PM' ? 12 : 0 };
    }
    const parsedNumber = parseInt(hours, 10);
    const maxHour = hasAmPmToggle && (toggle === 'AM' || toggle === 'PM') ? 12 : 23;
    const isValid =
        !Number.isNaN(parsedNumber) && Number.isInteger(parsedNumber) && parsedNumber >= 0 && parsedNumber <= maxHour;
    if (isValid) {
        let hours24 = parsedNumber;
        if (hasAmPmToggle && toggle === 'PM' && parsedNumber < 12) {
            hours24 = parsedNumber + 12;
        } else if (hasAmPmToggle && toggle === 'AM' && parsedNumber === 12) {
            hours24 = 0;
        }
        return {
            isValid,
            hours: hours24,
            hoursString: getHoursString(toggle, hasAmPmToggle, hours24, hours),
        };
    }
    return { isValid, hoursString: formatString(hours) };
}

export function validateMinutes(minutes: TimeComponentState['minutesString']):
    | {
          isValid: true;
          minutes: TimeComponentState['minutes'];
          minutesString: TimeComponentState['minutesString'];
      }
    | { isValid: false; minutesString: TimeComponentState['minutesString'] } {
    if (minutes === '') {
        return { isValid: true, minutesString: '', minutes: 0 };
    }
    const parsedNumber = parseInt(minutes, 10);
    const isValid =
        !Number.isNaN(parsedNumber) && Number.isInteger(parsedNumber) && parsedNumber >= 0 && parsedNumber <= 59;
    if (isValid) {
        return { isValid, minutes: parsedNumber, minutesString: formatString(minutes) };
    }
    return { isValid, minutesString: formatString(minutes) };
}

export function validateSeconds(
    seconds: string,
): { isValid: true; seconds: TimeComponentState['seconds'] } | { isValid: false } {
    const result = validateMinutes(seconds);
    if (result.isValid) {
        return { isValid: true, seconds: result.minutes };
    }
    return { isValid: false };
}

export function getValueFromString({ time, hasAmPmToggle }: { time?: string | null; hasAmPmToggle: boolean }): {
    hours: number;
    hoursString: string;
    minutes: number;
    minutesString: string;
    seconds: number;
    toggle: TimeComponentState['toggle'];
    value: string | null;
} {
    if (isNil(time)) {
        return {
            hours: 0,
            hoursString: '',
            minutes: 0,
            minutesString: '',
            seconds: 0,
            toggle: hasAmPmToggle ? 'AM' : null,
            value: null,
        };
    }
    const value = time;
    if (!value.match(TIME_REGEX)) {
        throw new Error(`Invalid date time: ${value}`);
    }
    const [hours, minutes, seconds] = value.split(':');

    const hoursValidation = validateHours({ hours, toggle: 'unknown', hasAmPmToggle });
    if (!hoursValidation.isValid) {
        throw new Error(`Invalid date time: ${value}`);
    }
    const minutesValidation = validateMinutes(minutes);
    if (!minutesValidation.isValid) {
        throw new Error(`Invalid date time: ${value}`);
    }
    const secondsValidation = validateSeconds(seconds);
    if (!secondsValidation.isValid) {
        throw new Error(`Invalid date time: ${value}`);
    }
    const toggle = hoursValidation.hours < 12 ? 'AM' : 'PM';
    return {
        hours: hoursValidation.hours,
        hoursString: hoursValidation.hoursString,
        minutes: minutesValidation.minutes,
        minutesString: minutesValidation.minutesString,
        seconds: secondsValidation.seconds,
        toggle,
        value: isNil(time)
            ? null
            : `${String(hoursValidation.hours).padStart(2, '0').slice(0, 2)}:${String(minutesValidation.minutes).padStart(2, '0').slice(0, 2)}:${String(secondsValidation.seconds).padStart(2, '0').slice(0, 2)}`,
    };
}
