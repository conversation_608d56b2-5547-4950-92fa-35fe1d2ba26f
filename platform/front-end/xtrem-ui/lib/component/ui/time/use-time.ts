import { usePrevious } from '@sage/xtrem-ui-components';
import type { I18nProviderProps } from 'carbon-react/esm/components/i18n-provider';
import { produce } from 'immer';
import { isNil, memoize } from 'lodash';
import React from 'react';
import { carbonLocale } from '../../../utils/carbon-locale';
import { getDataTestIdAttribute } from '../../../utils/dom';
import { useFocus } from '../../../utils/hooks/effects/use-focus';
import { BACKSPACE, ENTER, TAB } from '../../../utils/keyboard-event-utils';
import type { TimeComponentProps } from '../../field/time/time-types';
import type { ToggleState } from './time-types';
import { getValueFromString, validateHours, validateMinutes } from './time-utils';

export type TimeComponentState = {
    hours: number;
    hoursString: string;
    minutesString: string;
    minutes: number;
    seconds: number;
    toggle: ToggleState;
    value: string | null;
};

export type TimeComponentAction =
    | {
          type: 'CHANGE_HOURS';
          hours: TimeComponentState['hoursString'];
      }
    | {
          type: 'CHANGE_MINUTES';
          minutes: TimeComponentState['minutesString'];
      }
    | {
          type: 'CHANGE_VALUE';
          value?: string;
      }
    | {
          type: 'CHANGE_HAS_AM_PM_TOGGLE';
          hasAmPmToggle: boolean;
      }
    | {
          type: 'BLUR_HOURS';
      }
    | {
          type: 'BLUR_MINUTES';
      }
    | {
          type: 'BLUR_FIELD';
      }
    | {
          type: 'TOGGLE_CHANGE';
          toggle: TimeComponentState['toggle'];
      };

const handleHoursChange = (draft: TimeComponentState, action: TimeComponentAction, hasAmPmToggle: boolean): void => {
    if (action.type === 'CHANGE_HOURS') {
        const validation = validateHours({ hours: action.hours, toggle: draft.toggle, hasAmPmToggle });
        if (validation.isValid) {
            draft.hours = validation.hours;
            draft.hoursString = validation.hoursString;
        }
    }
};

const handleMinutesChange = (draft: TimeComponentState, action: TimeComponentAction): void => {
    if (action.type === 'CHANGE_MINUTES') {
        const validation = validateMinutes(action.minutes);
        if (validation.isValid) {
            draft.minutes = validation.minutes;
            draft.minutesString = validation.minutesString;
        }
    }
};

const handleBlurHours = (draft: TimeComponentState, hasAmPmToggle: boolean): void => {
    if (draft.hoursString) {
        if (hasAmPmToggle && draft.hours === 0) {
            draft.hoursString = '12';
        } else {
            draft.hoursString = String(hasAmPmToggle && draft.hours > 12 ? draft.hours - 12 : draft.hours).padStart(
                2,
                '0',
            );
        }
    }
    if (draft.hoursString !== '' && draft.minutesString !== '') {
        draft.value = `${String(draft.hours).padStart(2, '0').slice(0, 2)}:${String(draft.minutes).padStart(2, '0').slice(0, 2)}:${String(draft.seconds).padStart(2, '0').slice(0, 2)}`;
    }
};

const handleBlurMinutes = (draft: TimeComponentState): void => {
    if (draft.minutesString) {
        draft.minutesString = String(draft.minutes).padStart(2, '0');
    }
    if (draft.hoursString !== '' && draft.minutesString !== '') {
        draft.value = `${String(draft.hours).padStart(2, '0').slice(0, 2)}:${String(draft.minutes).padStart(2, '0').slice(0, 2)}:${String(draft.seconds).padStart(2, '0').slice(0, 2)}`;
    }
};

const handleBlurField = (draft: TimeComponentState, hasAmPmToggle: boolean): void => {
    if (draft.hoursString === '' && draft.minutesString === '') {
        draft.hours = 0;
        draft.minutes = 0;
        draft.value = null;
        return;
    }
    if (draft.hoursString === '') {
        draft.hours = 0;
        draft.hoursString = hasAmPmToggle ? '12' : '00';
        draft.value = `${String(draft.hours).padStart(2, '0').slice(0, 2)}:${String(draft.minutes).padStart(2, '0').slice(0, 2)}:${String(draft.seconds).padStart(2, '0').slice(0, 2)}`;
        return;
    }
    if (draft.minutesString === '') {
        draft.minutes = 0;
        draft.minutesString = '00';
        draft.value = `${String(draft.hours).padStart(2, '0').slice(0, 2)}:${String(draft.minutes).padStart(2, '0').slice(0, 2)}:${String(draft.seconds).padStart(2, '0').slice(0, 2)}`;
    }
};

const handleToggleChange = (draft: TimeComponentState, action: TimeComponentAction): void => {
    if (action.type === 'TOGGLE_CHANGE' && action.toggle !== draft.toggle) {
        if (draft.hoursString === '' || draft.minutesString === '') {
            draft.toggle = action.toggle;
            return;
        }
        if (action.toggle === 'AM' && draft.toggle) {
            draft.hours -= 12;
        } else if (action.toggle === 'PM' && draft.toggle && draft.hours !== 12) {
            draft.hours += 12;
        } else if (action.toggle === null) {
            draft.hoursString = String(draft.hours).padStart(2, '0');
        }
        draft.toggle = action.toggle;
        draft.value = `${String(draft.hours).padStart(2, '0').slice(0, 2)}:${String(draft.minutes).padStart(2, '0').slice(0, 2)}:${String(draft.seconds).padStart(2, '0').slice(0, 2)}`;
    }
};

const handleChangeValue = (draft: TimeComponentState, action: TimeComponentAction, hasAmPmToggle: boolean): void => {
    if (action.type === 'CHANGE_VALUE') {
        const { hours, hoursString, minutes, minutesString, seconds, toggle, value } = getValueFromString({
            time: action.value,
            hasAmPmToggle,
        });
        draft.hours = hours;
        draft.hoursString = hoursString;
        draft.minutes = minutes;
        draft.minutesString = minutesString;
        draft.seconds = seconds;
        draft.toggle = toggle;
        draft.value = value;
    }
};

const handleChangeHasAmPmToggle = (draft: TimeComponentState, action: TimeComponentAction): void => {
    if (action.type === 'CHANGE_HAS_AM_PM_TOGGLE') {
        const { hours, hoursString, minutes, minutesString, seconds, toggle, value } = getValueFromString({
            time: draft.value,
            hasAmPmToggle: action.hasAmPmToggle,
        });
        draft.hours = hours;
        draft.hoursString = hoursString;
        draft.minutes = minutes;
        draft.minutesString = minutesString;
        draft.seconds = seconds;
        draft.toggle = toggle;
        draft.value = value;
    }
};

const timeReducer = memoize(
    (hasAmPmToggle: boolean) =>
        (state: TimeComponentState, action: TimeComponentAction): TimeComponentState => {
            return produce(state, (draft: TimeComponentState) => {
                switch (action.type) {
                    case 'CHANGE_HOURS':
                        handleHoursChange(draft, action, hasAmPmToggle);
                        break;
                    case 'CHANGE_MINUTES':
                        handleMinutesChange(draft, action);
                        break;
                    case 'BLUR_HOURS':
                        handleBlurHours(draft, hasAmPmToggle);
                        break;
                    case 'BLUR_MINUTES':
                        handleBlurMinutes(draft);
                        break;
                    case 'BLUR_FIELD':
                        handleBlurField(draft, hasAmPmToggle);
                        break;
                    case 'TOGGLE_CHANGE':
                        handleToggleChange(draft, action);
                        break;
                    case 'CHANGE_VALUE':
                        handleChangeValue(draft, action, hasAmPmToggle);
                        break;
                    case 'CHANGE_HAS_AM_PM_TOGGLE':
                        handleChangeHasAmPmToggle(draft, action);
                        break;
                    default:
                        break;
                }
            });
        },
);

type UseTimeHook = {
    componentRef: React.RefObject<HTMLDivElement>;
    dataTestId: string;
    dispatch: React.Dispatch<TimeComponentAction>;
    hasAmPmToggle: boolean;
    hoursRef: React.RefObject<HTMLInputElement>;
    maxHours: number;
    minHours: number;
    minutesRef: React.RefObject<HTMLInputElement>;
    onHoursBlur: NonNullable<React.ComponentProps<'input'>['onBlur']>;
    onHoursChange: NonNullable<React.ComponentProps<'input'>['onChange']>;
    onBlurField: () => void;
    onKeyDown: NonNullable<React.ComponentProps<'input'>['onKeyDown']>;
    onMinutesBlur: NonNullable<React.ComponentProps<'input'>['onBlur']>;
    onMinutesChange: NonNullable<React.ComponentProps<'input'>['onChange']>;
    providerLocale?: I18nProviderProps['locale'];
    state: TimeComponentState;
    toggleChange: (toggle: 'AM' | 'PM') => void;
};

type UseTimeProps = Pick<TimeComponentProps, 'locale' | 'isInFocus' | 'elementId' | 'value'> & {
    onChange?: (v: string | null) => void;
};

export function useTime({ locale, isInFocus, elementId, value, onChange }: UseTimeProps): UseTimeHook {
    const componentRef = React.useRef<HTMLDivElement>(null);
    useFocus(componentRef, isInFocus, 'input');

    const hoursRef = React.useRef<HTMLInputElement>(null);
    const minutesRef = React.useRef<HTMLInputElement>(null);
    const previousExternalValue = usePrevious(value);
    const actualLocale = React.useMemo(() => locale || 'en-US', [locale]);
    const providerLocale = React.useMemo(() => carbonLocale(actualLocale), [actualLocale]);
    const hasAmPmToggle = React.useMemo(() => actualLocale === 'en-US', [actualLocale]);
    const minHours = React.useMemo(() => (hasAmPmToggle ? 1 : 0), [hasAmPmToggle]);
    const maxHours = React.useMemo(() => (hasAmPmToggle ? 12 : 23), [hasAmPmToggle]);

    const [state, dispatch] = React.useReducer(
        timeReducer(hasAmPmToggle),
        {
            hoursString: '',
            minutesString: '',
            toggle: hasAmPmToggle ? 'AM' : null,
            value: value ?? null,
            hours: 0,
            minutes: 0,
            seconds: 0,
        } satisfies TimeComponentState,
        initialState => {
            const { hours, minutes, seconds, hoursString, minutesString } = getValueFromString({
                time: value,
                hasAmPmToggle,
            });
            return { ...initialState, hours, minutes, seconds, hoursString, minutesString };
        },
    );

    React.useEffect(() => {
        dispatch({ type: 'CHANGE_HAS_AM_PM_TOGGLE', hasAmPmToggle });
    }, [hasAmPmToggle]);

    React.useEffect(() => {
        // internal change
        if (value !== state.value && previousExternalValue === value) {
            onChange?.(state.value);
        }
    }, [state.value, onChange, value, previousExternalValue]);

    React.useEffect(() => {
        // external change
        if (value !== state.value && previousExternalValue !== value) {
            dispatch({ type: 'CHANGE_VALUE', value });
        }
    }, [previousExternalValue, state.value, value]);

    const onHoursChange = React.useCallback<NonNullable<React.ComponentProps<'input'>['onChange']>>(
        ({ target: { value: hoursValue } }) => {
            dispatch({ type: 'CHANGE_HOURS', hours: hoursValue });
        },
        [],
    );

    const onMinutesChange = React.useCallback<NonNullable<React.ComponentProps<'input'>['onChange']>>(
        ({ target: { value: minutesValue } }) => {
            dispatch({ type: 'CHANGE_MINUTES', minutes: minutesValue });
        },
        [],
    );

    const onBlurField = React.useCallback(() => {
        dispatch({ type: 'BLUR_HOURS' });
        dispatch({ type: 'BLUR_MINUTES' });
        dispatch({ type: 'BLUR_FIELD' });
    }, []);

    const onHoursBlur = React.useCallback<NonNullable<React.ComponentProps<'input'>['onBlur']>>(e => {
        dispatch({ type: 'BLUR_HOURS' });
        if (
            isNil(e.relatedTarget) ||
            (!hoursRef.current?.contains(e.relatedTarget) && !minutesRef.current?.contains(e.relatedTarget))
        ) {
            dispatch({ type: 'BLUR_FIELD' });
        }
    }, []);

    const onMinutesBlur = React.useCallback<NonNullable<React.ComponentProps<'input'>['onBlur']>>(e => {
        dispatch({ type: 'BLUR_MINUTES' });
        if (
            isNil(e.relatedTarget) ||
            (!hoursRef.current?.contains(e.relatedTarget) && !minutesRef.current?.contains(e.relatedTarget))
        ) {
            dispatch({ type: 'BLUR_FIELD' });
        }
    }, []);

    const onKeyDown = React.useCallback<NonNullable<React.ComponentProps<'input'>['onKeyDown']>>(event => {
        const isNumeric = Number.isInteger(parseInt(event.key || '', 10));
        if (
            !isNumeric &&
            event.key !== BACKSPACE &&
            event.key !== TAB &&
            event.key !== ENTER &&
            event.key !== 'Home' &&
            event.key !== 'End' &&
            event.key !== 'ArrowLeft' &&
            event.key !== 'ArrowRight' &&
            event.key !== 'ArrowDown' &&
            event.key !== 'ArrowUp' &&
            event.key !== 'Delete' &&
            event.key !== 'Insert'
        ) {
            event.preventDefault();
        }
    }, []);

    const toggleChange = React.useCallback((toggle: 'AM' | 'PM') => {
        dispatch({
            type: 'TOGGLE_CHANGE',
            toggle,
        });
    }, []);

    const dataTestId = React.useMemo(() => getDataTestIdAttribute('time', 'icon', elementId), [elementId]);

    return {
        componentRef,
        dataTestId,
        dispatch,
        hasAmPmToggle,
        hoursRef,
        maxHours,
        minHours,
        minutesRef,
        onHoursBlur,
        onHoursChange,
        onKeyDown,
        onMinutesBlur,
        onBlurField,
        onMinutesChange,
        providerLocale,
        state,
        toggleChange,
    };
}
