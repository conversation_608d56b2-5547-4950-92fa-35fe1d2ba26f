import Button from 'carbon-react/esm/components/button';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import MultiActionButton from 'carbon-react/esm/components/multi-action-button';
import React, { useEffect } from 'react';
import { isScrolledToTheEnd } from '../../../utils/dom';
import styled from 'styled-components';
import * as tokens from '@sage/design-tokens/js/base/common';
import { camelCase, isNil } from 'lodash';
import Icon from 'carbon-react/esm/components/icon';
import { localize } from '../../../service/i18n-service';
import Pill from 'carbon-react/esm/components/pill';

export interface XtremTabItem {
    id: string;
    title: string;
    icon?: IconType;
    validationMessage?: string | null;
    indicatorContent?: string;
}

export interface XtremTabsProps {
    onTabChange: (tabId: string) => void;
    tabs: XtremTabItem[];
    selectedTabId: string;
    inSidebar?: boolean;
}

const StyledDropdownItemButton = styled(Button)`
    font-weight: ${tokens.fontWeights500} !important;
    width: 170px;
    display: flex !important;

    span:first-child {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: block;
    }
`;

const StyledMultiActionButton = styled(MultiActionButton)`
    border-top: 1px solid ${tokens.colorsUtilityMajor050};
    border-left: 1px solid ${tokens.colorsUtilityMajor050};
    border-bottom: 2px solid ${tokens.colorsUtilityMajor050};
    border-right: 1px solid ${tokens.colorsUtilityMajor050};
    background: ${tokens.colorsYang100};

    span[type='dropdown'] {
        left: unset !important;
        color: #668592;
    }

    span[type='dropdown']::before {
        content: '';
    }

    &:focus,
    &:hover {
        color: ${tokens.colorsYang100};
    }
`;

export function XtremTabs(props: XtremTabsProps): React.ReactElement {
    const tabContainerRef = React.createRef<HTMLDivElement>();
    const containerRef = React.createRef<HTMLDivElement>();
    const [isDropDownNeeded, setDropDownNeeded] = React.useState(false);
    const [isScrolledToRight, setScrolledRight] = React.useState(false);
    const [isScrolledToLeft, setScrolledLeft] = React.useState(true);

    const onScroll = (): void => {
        if (tabContainerRef.current) {
            setScrolledRight(isScrolledToTheEnd(tabContainerRef.current));
            setScrolledLeft(tabContainerRef.current.scrollLeft === 0);
        }
    };

    const onKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>): void => {
        const selectedItemIndex = props.tabs.findIndex(t => t.id === props.selectedTabId);

        if (event.key === 'ArrowLeft' && selectedItemIndex > 0) {
            event.preventDefault();
            const nextIndex = selectedItemIndex - 1;
            props.onTabChange(props.tabs[selectedItemIndex - 1].id);
            containerRef.current?.querySelectorAll<HTMLButtonElement>('.e-xtrem-tab-item').item(nextIndex)?.focus();
        }

        if (event.key === 'ArrowRight' && selectedItemIndex < props.tabs.length - 1) {
            event.preventDefault();
            const nextIndex = selectedItemIndex + 1;
            props.onTabChange(props.tabs[selectedItemIndex + 1].id);
            containerRef.current?.querySelectorAll<HTMLButtonElement>('.e-xtrem-tab-item').item(nextIndex)?.focus();
        }
    };

    const calculateOffset = React.useCallback(
        (index: number): number => {
            if (tabContainerRef.current) {
                let offset = 0;
                const buttons = tabContainerRef.current.querySelectorAll('button');
                // eslint-disable-next-line no-plusplus
                for (let i = 0; i < index; i++) {
                    offset += buttons[i].clientWidth;
                }

                return offset;
            }

            return 0;
        },
        [tabContainerRef],
    );

    // Scrolls the selected item in the middle based on user selection
    useEffect(() => {
        if (tabContainerRef.current) {
            const selectedItemIndex = props.tabs.findIndex(t => t.id === props.selectedTabId);
            const offset = selectedItemIndex > 1 ? calculateOffset(selectedItemIndex - 1) : 0;
            tabContainerRef.current.scroll({
                left: offset,
                top: 0,
                behavior: 'smooth',
            });
        }
    }, [props.tabs, props.selectedTabId, calculateOffset, tabContainerRef]);

    // Checks if a the tabs would overflow the container
    useEffect(() => {
        if (containerRef.current) {
            const totalWidth = calculateOffset(props.tabs.length);
            setDropDownNeeded(containerRef.current.clientWidth < totalWidth);
        }
    }, [calculateOffset, containerRef, props.tabs]);

    const tabContainerClasses: string[] = ['e-xtrem-tab-container'];
    if (isDropDownNeeded && !isScrolledToRight) {
        tabContainerClasses.push('e-xtrem-tab-container-right-indicator');
    }

    if (isDropDownNeeded && !isScrolledToLeft) {
        tabContainerClasses.push('e-xtrem-tab-container-left-indicator');
    }
    const tabClassName = props.inSidebar ? 'e-xtrem-tabs e-tabs-sidebar' : 'e-xtrem-tabs';

    return (
        <div className={tabClassName} ref={containerRef}>
            <div className={tabContainerClasses.join(' ')} ref={tabContainerRef} onScroll={onScroll} role="tablist">
                {props.tabs.map(t => {
                    const tabClasses = ['e-xtrem-tab-item'];
                    if (t.id === props.selectedTabId) {
                        tabClasses.push('e-xtrem-tab-item-active');
                    }
                    if (t.validationMessage) {
                        tabClasses.push('e-xtrem-tab-item-invalid');
                    }
                    return (
                        <button
                            aria-selected={t.id === props.selectedTabId}
                            className={tabClasses.join(' ')}
                            data-testid={`e-xtrem-tab-${camelCase(t.title)} e-xtrem-tab-bind-${camelCase(t.id)}`}
                            onClick={(): void => props.onTabChange(t.id)}
                            onKeyDown={onKeyDown}
                            role="tab"
                            tabIndex={t.id === props.selectedTabId ? 0 : -1}
                            type="button"
                            key={t.id}
                            data-pendoid={props.inSidebar ? `sidebarTab-${t.id}` : `sectionTab-${t.id}`}
                        >
                            <span className="e-xtrem-tab-item-text">{t.title}</span>
                            {!isNil(t.indicatorContent) && (
                                <Pill
                                    data-testid={`e-xtrem-tab-${camelCase(t.title)}-indicator-content e-xtrem-tab-bind-${camelCase(t.id)}-indicator-content`}
                                    ml="4px"
                                    size="S"
                                    pillRole="status"
                                    fill={true}
                                >
                                    {t.indicatorContent}
                                </Pill>
                            )}
                            {t.validationMessage && (
                                <Icon
                                    type="error"
                                    tooltipMessage={t.validationMessage}
                                    ariaLabel={t.validationMessage}
                                    color={tokens.colorsSemanticNegative500}
                                    tooltipBgColor={tokens.colorsSemanticNegative500}
                                    tooltipFontColor={tokens.colorsYang100}
                                />
                            )}
                        </button>
                    );
                })}
            </div>
            {isDropDownNeeded && (
                <StyledMultiActionButton
                    text=" "
                    align="left"
                    position="right"
                    aria-label={localize('@sage/xtrem-ui/see-more-items', 'See more items')}
                    style={{ marginTop: 'var(--spacing075)' }}
                >
                    {props.tabs.map(t => (
                        <StyledDropdownItemButton
                            key={t.id}
                            onClick={(): void => props.onTabChange(t.id)}
                            iconType={t.id === props.selectedTabId ? 'tick' : undefined}
                            iconPosition="after"
                        >
                            {t.title}
                        </StyledDropdownItemButton>
                    ))}
                </StyledMultiActionButton>
            )}
        </div>
    );
}
