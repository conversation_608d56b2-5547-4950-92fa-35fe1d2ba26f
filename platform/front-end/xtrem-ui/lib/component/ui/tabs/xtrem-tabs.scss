.e-xtrem-tabs {
    display: flex;
    align-items: flex-end;
    overflow: hidden;
    position: relative;

    [data-element="additional-buttons"] {
        max-height: 250px;
        overflow-y: auto;

        // Padding to account for the focused state border
        li {
            padding: 6px;
        }
    }

    .e-xtrem-tab-container {
        flex: 1;
        list-style-type: none;
        margin-block-start: 0;
        margin-block-end: 0;
        margin-inline-start: 0;
        margin-inline-end: 0;
        padding-inline-start: 0;
        white-space: nowrap;
        transition-property: margin-left;
        transition-duration: 0.3s;
        box-shadow: inset 0px -2px 0px 0 var(--colorsComponentsMenuSpringStandard500);
        -ms-overflow-style: none;
        /* for Internet Explorer, Edge */
        scrollbar-width: none;
        /* for Firefox */
        overflow-y: scroll;

        >::after {
            width: 0;
            height: 40px;
            position: absolute;
            right: 40px;
            top: 0;
            content: ' ';
            box-shadow: none;
            transition: box-shadow .1s ease-in;
        }

        >::before {
            width: 0;
            height: 38px;
            position: absolute;
            left: 0;
            top: 0;
            content: ' ';
            box-shadow: none;
            transition: box-shadow .1s ease-in;
            background: var(--colorsYang100);

        }

        &.e-xtrem-tab-container-right-indicator ::after {
            box-shadow: 8px 15px 15px 5px #00141e10;
        }

        &.e-xtrem-tab-container-left-indicator ::before {
            width: 0;
            box-shadow: -8px 15px 15px 5px #00141e10;
        }
    }

    [data-component="multi-action-button"]>button {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        padding-left: 12px;
        padding-right: 12px;
    }

    .e-xtrem-tab-container::-webkit-scrollbar {
        /* for Chrome, Safari, and Opera */
        display: none;
    }

    .e-xtrem-tab-item {
        border-top-left-radius: var(--borderRadius100);
        border-top-right-radius: var(--borderRadius100);
        margin: 0;
        min-width: 92px;
        height: 40px;
        padding: 4px 8px;
        font-family: var(--fontFamiliesDefault);
        font-weight: var(--fontWeights500);
        font-size: 14px;
        color: var(--colorsYin055);
        background-color: transparent;
        border-top: none;
        border-left: none;
        border-right: none;
        border-bottom: 2px solid var(--colorsUtilityMajor050);
        line-height: 32px;
        display: inline-block;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        .e-xtrem-tab-item-text {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        &:focus,
        &:active {
            box-shadow: inset 0px 0px 0px 3px var(--colorsSemanticFocus500);
            outline: none;
        }

        &.e-xtrem-tab-item-invalid {
            box-shadow: inset 0px 0px 0px 2px var(--colorsSemanticNegative500);
        }

        &.e-xtrem-tab-item-active {
            background: var(--colorsYang100);
            color: var(--colorsYin090);
            border-bottom: 2px solid var(--colorsActionMajor500);
        }
    }
}
