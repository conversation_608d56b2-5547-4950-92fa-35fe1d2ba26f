import * as React from 'react';
import type { IconProps } from '../icon/icon-component';
import { Icon } from '../icon/icon-component';
import * as tokens from '@sage/design-tokens/js/base/common';
import type { IconType } from '@sage/xtrem-shared';

export function TableTooltip(
    props: Omit<IconProps, 'color' | 'type' | 'tooltipMessage'> & {
        content: string | React.ReactNode;
        type?: IconType;
        color?: string;
        fontSize?: string;
    },
): React.ReactElement {
    return (
        <div
            style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                maxWidth: '24px',
                height: '32px',
            }}
        >
            <Icon
                {...props}
                tooltipMessage={<span className="e-ui-table-tooltip-content">{props.content}</span>}
                color={props.color ?? tokens.colorsSemanticNegative500}
                type={props.type ?? 'error'}
                fontSize={props.fontSize || 'small'}
            />
        </div>
    );
}
