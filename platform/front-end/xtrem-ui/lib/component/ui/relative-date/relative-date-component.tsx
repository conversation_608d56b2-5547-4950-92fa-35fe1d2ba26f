import React from 'react';
import { formatDateToCurrentLocale } from '../../../service/i18n-service';
import type { RelativeDateScope } from '../../field/relative-date/relative-date-utils';
import { getRelativeDateDisplayValue } from '../../field/relative-date/relative-date-utils';
import CarbonTooltip from 'carbon-react/esm/components/tooltip';
import type { DatePropertyValue } from '../../../utils/types';
import { isValidDatePropertyValue } from '@sage/xtrem-date-time';

type RelativeDateProps = {
    value: DatePropertyValue;
    scope?: RelativeDateScope; // should only be used when bound to a graphQL server datatype (Date / Datetime)
};

export function RelativeDate({ value, scope }: RelativeDateProps): React.ReactElement | null {
    const isValid = isValidDatePropertyValue(value);
    return isValid ? (
        <CarbonTooltip message={formatDateToCurrentLocale(value)} position="bottom">
            <span data-testid="e-relative-date-field-content">{getRelativeDateDisplayValue(value, scope)}</span>
        </CarbonTooltip>
    ) : null;
}
