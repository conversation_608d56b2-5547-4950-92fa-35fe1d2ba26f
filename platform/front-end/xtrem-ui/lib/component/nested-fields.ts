/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import { GridNestedFieldTypes } from '@sage/xtrem-shared';
import type { Extend } from '../service/page-extension';
import type { ScreenBase } from '../service/screen-base';
import type { ScreenExtension } from '../types';
import {
    AggregateControlObject,
    CheckboxControlObject,
    CountControlObject,
    DateControlObject,
    DatetimeControlObject,
    DatetimeRangeControlObject,
    DropdownListControlObject,
    DynamicSelectControlObject,
    FilterSelectControlObject,
    IconControlObject,
    ImageControlObject,
    LabelControlObject,
    LinkControlObject,
    MultiDropdownControlObject,
    MultiReferenceControlObject,
    NumericControlObject,
    ProgressControlObject,
    ReferenceControlObject,
    RelativeDateControlObject,
    SelectControlObject,
    SwitchControlObject,
    TextAreaControlObject,
    TextControlObject,
} from './control-objects';
import type {
    NestedAggregateProperties,
    NestedCheckboxProperties,
    NestedCountProperties,
    NestedDateProperties,
    NestedDatetimeProperties,
    NestedDatetimeRangeProperties,
    NestedDropdownListProperties,
    NestedDynamicSelectProperties,
    NestedFilterSelectProperties,
    NestedIconProperties,
    NestedImageProperties,
    NestedLabelProperties,
    NestedLinkProperties,
    NestedMultiDropdownProperties,
    NestedMultiReferenceProperties,
    NestedNumericProperties,
    NestedProgressProperties,
    NestedReferenceProperties,
    NestedRelativeDateProperties,
    NestedSelectProperties,
    NestedSwitchProperties,
    NestedTechnicalProperties,
    NestedTextAreaProperties,
    NestedTextProperties,
} from './nested-fields-properties';
import { FieldKey } from './types';

export { GridNestedFieldTypes };

export type NestedFieldTypes =
    | FieldKey.Aggregate
    | FieldKey.Checkbox
    | FieldKey.Count
    | FieldKey.Date
    | FieldKey.DropdownList
    | FieldKey.DynamicSelect
    | FieldKey.FilterSelect
    | FieldKey.Icon
    | FieldKey.Image
    | FieldKey.Label
    | FieldKey.Link
    | FieldKey.MultiDropdown
    | FieldKey.MultiReference
    | FieldKey.Numeric
    | FieldKey.Progress
    | FieldKey.Reference
    | FieldKey.RelativeDate
    | FieldKey.Select
    | FieldKey.Switch
    | FieldKey.Technical
    | FieldKey.Text
    | FieldKey.Datetime
    | FieldKey.DatetimeRange
    | FieldKey.TextArea;

export type PodNestedFieldTypes =
    | FieldKey.Aggregate
    | FieldKey.Checkbox
    | FieldKey.Count
    | FieldKey.Date
    | FieldKey.Datetime
    | FieldKey.DatetimeRange
    | FieldKey.DropdownList
    | FieldKey.DynamicSelect
    | FieldKey.FilterSelect
    | FieldKey.Icon
    | FieldKey.Image
    | FieldKey.Label
    | FieldKey.Link
    | FieldKey.MultiDropdown
    | FieldKey.MultiReference
    | FieldKey.Numeric
    | FieldKey.Progress
    | FieldKey.Reference
    | FieldKey.RelativeDate
    | FieldKey.Select
    | FieldKey.Switch
    | FieldKey.Technical
    | FieldKey.Text
    | FieldKey.TextArea;

export const allowedMainFieldTypes: GridNestedFieldTypes[] = [
    FieldKey.Checkbox,
    FieldKey.Count,
    FieldKey.Date,
    FieldKey.Datetime,
    FieldKey.DatetimeRange,
    FieldKey.DropdownList,
    FieldKey.Label,
    FieldKey.Link,
    FieldKey.Numeric,
    FieldKey.Progress,
    FieldKey.Select,
    FieldKey.Text,
];

export type NestedPropertiesWrapper<T> = Omit<T, 'bind' | 'onChange'>;

export type NestedFieldComponentProps<
    T extends NestedFieldTypes,
    CT extends ScreenBase = ScreenBase,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
> = T extends FieldKey.Aggregate
    ? NestedAggregateProperties<CT, NodeType>
    : T extends FieldKey.Checkbox
      ? NestedCheckboxProperties<CT, NodeType>
      : T extends FieldKey.Count
        ? NestedCountProperties<CT, NodeType>
        : T extends FieldKey.Date
          ? NestedDateProperties<CT, NodeType>
          : T extends FieldKey.Datetime
            ? NestedDatetimeProperties<CT, NodeType>
            : T extends FieldKey.DatetimeRange
              ? NestedDatetimeRangeProperties<CT, NodeType>
              : T extends FieldKey.DynamicSelect
                ? NestedDynamicSelectProperties<CT, NodeType>
                : T extends FieldKey.FilterSelect
                  ? NestedFilterSelectProperties<CT, NodeType, R>
                  : T extends FieldKey.Icon
                    ? NestedIconProperties<CT, NodeType>
                    : T extends FieldKey.Image
                      ? NestedImageProperties<CT, NodeType>
                      : T extends FieldKey.Label
                        ? NestedLabelProperties<CT, NodeType>
                        : T extends FieldKey.Link
                          ? NestedLinkProperties<CT, NodeType>
                          : T extends FieldKey.Numeric
                            ? NestedNumericProperties<CT, NodeType>
                            : T extends FieldKey.MultiDropdown
                              ? NestedMultiDropdownProperties<CT, NodeType>
                              : T extends FieldKey.DropdownList
                                ? NestedDropdownListProperties<CT, NodeType>
                                : T extends FieldKey.MultiReference
                                  ? NestedMultiReferenceProperties<CT, NodeType, R>
                                  : T extends FieldKey.Progress
                                    ? NestedProgressProperties<CT, NodeType>
                                    : T extends FieldKey.Reference
                                      ? NestedReferenceProperties<CT, NodeType, R>
                                      : T extends FieldKey.RelativeDate
                                        ? NestedRelativeDateProperties<CT, NodeType>
                                        : T extends FieldKey.Select
                                          ? NestedSelectProperties<CT, NodeType>
                                          : T extends FieldKey.Switch
                                            ? NestedSwitchProperties<CT, NodeType>
                                            : T extends FieldKey.Text
                                              ? NestedTextProperties<CT, NodeType>
                                              : T extends FieldKey.TextArea
                                                ? NestedTextAreaProperties<CT, NodeType>
                                                : NestedTextProperties<CT, NodeType>;

export type NestedFieldTypesWithoutTechnical = Exclude<NestedFieldTypes, FieldKey.Technical>;

export function isInstanceOf<T>(argument: any, C: new (...args: any[]) => T): argument is T {
    return argument instanceof C;
}

export function isNestedTechnical<NodeType extends ClientNode = any>(
    argument: NestedField<ScreenBase, NestedFieldTypes, NodeType>,
): argument is NestedField<any, FieldKey.Technical, NodeType> {
    return argument.type === FieldKey.Technical;
}

export function withoutNestedTechnical<NodeType extends ClientNode = any>(
    argument?: NestedField<ScreenBase, NestedFieldTypes, NodeType>[],
): NestedField<ScreenBase, Exclude<NestedFieldTypes, FieldKey.Technical>, NodeType>[] {
    if (!argument) {
        return [];
    }
    return argument.filter(i => !isNestedTechnical<NodeType>(i)) as NestedField<
        ScreenBase,
        Exclude<NestedFieldTypes, FieldKey.Technical>,
        NodeType
    >[];
}
export interface NestedField<
    CT extends ScreenExtension<CT>,
    T extends Partial<NestedFieldTypes>,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
> {
    defaultUiProperties: NestedFieldsProperties<T, Extend<CT>, NodeType, R>;
    properties: NestedFieldsProperties<T, Extend<CT>, NodeType, R>;
    type: T;
}
export interface NestedFieldTypeMap<
    CT extends ScreenExtension<CT> = any,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
> {
    [FieldKey.Aggregate]: NestedAggregateProperties<CT, NodeType>;
    [FieldKey.Checkbox]: NestedCheckboxProperties<CT, NodeType>;
    [FieldKey.Count]: NestedCountProperties<CT, NodeType>;
    [FieldKey.Date]: NestedDateProperties<CT, NodeType>;
    [FieldKey.Datetime]: NestedDatetimeProperties<CT, NodeType>;
    [FieldKey.DatetimeRange]: NestedDatetimeRangeProperties<CT, NodeType>;
    [FieldKey.DropdownList]: NestedDropdownListProperties<CT, NodeType>;
    [FieldKey.DynamicSelect]: NestedDynamicSelectProperties<CT, NodeType>;
    [FieldKey.FilterSelect]: NestedFilterSelectProperties<CT, NodeType, R>;
    [FieldKey.Icon]: NestedIconProperties<CT, NodeType>;
    [FieldKey.Image]: NestedImageProperties<CT, NodeType>;
    [FieldKey.Label]: NestedLabelProperties<CT, NodeType>;
    [FieldKey.Link]: NestedLinkProperties<CT, NodeType>;
    [FieldKey.MultiDropdown]: NestedMultiDropdownProperties<CT, NodeType>;
    [FieldKey.MultiReference]: NestedMultiReferenceProperties<CT, NodeType, R>;
    [FieldKey.Numeric]: NestedNumericProperties<CT, NodeType>;
    [FieldKey.Progress]: NestedProgressProperties<CT, NodeType>;
    [FieldKey.Reference]: NestedReferenceProperties<CT, NodeType, R>;
    [FieldKey.RelativeDate]: NestedRelativeDateProperties<CT, NodeType>;
    [FieldKey.Select]: NestedSelectProperties<CT, NodeType>;
    [FieldKey.Switch]: NestedSwitchProperties<CT, NodeType>;
    [FieldKey.Technical]: NestedTechnicalProperties<CT, NodeType, R>;
    [FieldKey.Text]: NestedTextProperties<CT, NodeType>;
    [FieldKey.TextArea]: NestedTextAreaProperties<CT, NodeType>;
}

export type NestedFieldsProperties<
    T extends NestedFieldTypes,
    CT extends ScreenBase = any,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
> = {
    [K in keyof NestedFieldTypeMap<CT, NodeType, R>]: K extends T ? NestedFieldTypeMap<CT, NodeType, R>[K] : never;
}[keyof {
    [K in keyof NestedFieldTypeMap<CT, NodeType, R>]: K extends T ? NestedFieldTypeMap<CT, NodeType, R>[K] : never;
}];

export interface NestedFieldHandlersArguments {
    onClick?: any[];
    onChange?: any[];
    rowValue?: any;
}

export const getNestedFieldDefaultProperties = <
    T extends NestedFieldTypes = FieldKey.Text,
    CT extends ScreenBase = ScreenBase,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
>(
    defaultProperties: any,
    controlObjectType?: T,
): NestedFieldComponentProps<T, Extend<CT>, NodeType, R> =>
    ({
        ...(defaultProperties as NestedFieldComponentProps<T>),
        canFilter: true,
        _controlObjectType: controlObjectType,
    }) as NestedFieldComponentProps<T, Extend<CT>, NodeType, R>;

export const aggregate = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedAggregateProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Aggregate, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Aggregate, CT, NodeType>(
        AggregateControlObject.defaultUiProperties,
        FieldKey.Aggregate,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Aggregate },
    type: FieldKey.Aggregate,
});

export const checkbox = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedCheckboxProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Checkbox, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Checkbox, CT, NodeType>(
        CheckboxControlObject.defaultUiProperties,
        FieldKey.Checkbox,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Checkbox },
    type: FieldKey.Checkbox,
});

export const count = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedCountProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Count, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Count, CT, NodeType>(
        CountControlObject.defaultUiProperties,
        FieldKey.Count,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Count },
    type: FieldKey.Count,
});

export const date = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedDateProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Date, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Date, CT, NodeType>(
        DateControlObject.defaultUiProperties,
        FieldKey.Date,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Date },
    type: FieldKey.Date,
});

export const datetime = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedDatetimeProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Datetime, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Datetime, CT, NodeType>(
        DatetimeControlObject.defaultUiProperties,
        FieldKey.Datetime,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Datetime },
    type: FieldKey.Datetime,
});

export const datetimeRange = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedDatetimeRangeProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.DatetimeRange, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.DatetimeRange, CT, NodeType>(
        DatetimeRangeControlObject.defaultUiProperties,
        FieldKey.DatetimeRange,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.DatetimeRange },
    type: FieldKey.DatetimeRange,
});

export const dynamicSelect = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedDynamicSelectProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.DynamicSelect, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.DynamicSelect, CT, NodeType>(
        DynamicSelectControlObject.defaultUiProperties,
        FieldKey.DynamicSelect,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.DynamicSelect },
    type: FieldKey.DynamicSelect,
});

export const dropdownList = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedDropdownListProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.DropdownList, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.DropdownList, CT, NodeType>(
        DropdownListControlObject.defaultUiProperties,
        FieldKey.DropdownList,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.DropdownList },
    type: FieldKey.DropdownList,
});

export const filterSelect = <
    CT extends ScreenExtension<CT> = any,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
>(
    properties: Omit<NestedFilterSelectProperties<Extend<CT>, NodeType, R>, '_controlObjectType'>,
): NestedField<CT, FieldKey.FilterSelect, NodeType, R> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.FilterSelect, CT, NodeType, R>(
        FilterSelectControlObject.defaultUiProperties,
        FieldKey.FilterSelect,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.FilterSelect },
    type: FieldKey.FilterSelect,
});

export const icon = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedIconProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Icon, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Icon, CT, NodeType>(
        IconControlObject.defaultUiProperties,
        FieldKey.Icon,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Icon },
    type: FieldKey.Icon,
});

export const image = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedImageProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Image, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Image, CT, NodeType>(
        ImageControlObject.defaultUiProperties,
        FieldKey.Image,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Image },
    type: FieldKey.Image,
});

export const label = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedLabelProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Label, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Label, CT, NodeType>(
        LabelControlObject.defaultUiProperties,
        FieldKey.Label,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Label },
    type: FieldKey.Label,
});

export const link = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedLinkProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Link, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Link, CT, NodeType>(
        LinkControlObject.defaultUiProperties,
        FieldKey.Link,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Link },
    type: FieldKey.Link,
});

export const numeric = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedNumericProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Numeric, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Numeric, CT, NodeType>(
        NumericControlObject.defaultUiProperties,
        FieldKey.Numeric,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Numeric },
    type: FieldKey.Numeric,
});

export const progress = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedProgressProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Progress, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Progress, CT, NodeType>(
        ProgressControlObject.defaultUiProperties,
        FieldKey.Progress,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Progress },
    type: FieldKey.Progress,
});

export const reference = <
    CT extends ScreenExtension<CT> = any,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
>(
    properties: Omit<NestedReferenceProperties<Extend<CT>, NodeType, R>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Reference, NodeType, R> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Reference, CT, NodeType, R>(
        ReferenceControlObject.defaultUiProperties,
        FieldKey.Reference,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Reference },
    type: FieldKey.Reference,
});

export const relativeDate = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedRelativeDateProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.RelativeDate, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.RelativeDate, CT, NodeType>(
        RelativeDateControlObject.defaultUiProperties,
        FieldKey.RelativeDate,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.RelativeDate },
    type: FieldKey.RelativeDate,
});

export const multiReference = <
    CT extends ScreenExtension<CT> = any,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
>(
    properties: Omit<NestedMultiReferenceProperties<Extend<CT>, NodeType, R>, '_controlObjectType'>,
): NestedField<CT, FieldKey.MultiReference, NodeType, R> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.MultiReference, CT, NodeType, R>(
        MultiReferenceControlObject.defaultUiProperties as NestedMultiReferenceProperties<CT, NodeType, R>,
        FieldKey.MultiReference,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.MultiReference },
    type: FieldKey.MultiReference,
});

export const multiDropdown = <
    CT extends ScreenExtension<CT> = any,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
>(
    properties: Omit<NestedMultiDropdownProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.MultiDropdown, NodeType, R> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.MultiDropdown, CT, NodeType>(
        MultiDropdownControlObject.defaultUiProperties,
        FieldKey.MultiDropdown,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.MultiDropdown },
    type: FieldKey.MultiDropdown,
});

export const select = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedSelectProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Select, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Select, CT, NodeType>(
        SelectControlObject.defaultUiProperties,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Select },
    type: FieldKey.Select,
});

const nestedSwitch = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedSwitchProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Switch, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Switch, CT, NodeType>(
        SwitchControlObject.defaultUiProperties,
        FieldKey.Switch,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Switch },
    type: FieldKey.Switch,
});

export { nestedSwitch as switch };

export const text = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedTextProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Text, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Text, CT, NodeType>(
        TextControlObject.defaultUiProperties,
        FieldKey.Text,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Text },
    type: FieldKey.Text,
});

export const textArea = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedTextAreaProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedField<CT, FieldKey.TextArea, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.TextArea, CT, NodeType>(
        TextAreaControlObject.defaultUiProperties,
        FieldKey.TextArea,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.TextArea },
    type: FieldKey.TextArea,
});

export const technical = <
    CT extends ScreenExtension<CT> = any,
    NodeType extends ClientNode = any,
    ReferenceNode extends ClientNode = any,
>(
    properties: Omit<NestedTechnicalProperties<Extend<CT>, NodeType, ReferenceNode>, '_controlObjectType'>,
): NestedField<CT, FieldKey.Technical, NodeType, ReferenceNode> => ({
    defaultUiProperties: { ...properties, _controlObjectType: FieldKey.Technical },
    properties: { ...properties, _controlObjectType: FieldKey.Technical },
    type: FieldKey.Technical,
});
