import type { DefaultFieldLayoutProps } from './abstract-layout-builder';
import { AbstractLayoutBuilder } from './abstract-layout-builder';
import type { FieldKey, LayoutContent } from './types';

export class AbstractFieldLayoutBuilder<T extends FieldKey> extends AbstractLayoutBuilder<T> {
    protected buildLayout = (): this => {
        const properties: Partial<DefaultFieldLayoutProps> = {
            elementId: this.elementId,
            isFullWidth: <PERSON>olean(this.metadata.properties.isFullWidth),
            width: (this.metadata.properties as any).width,
        };
        this.layout = this.buildFieldLayout(properties) as LayoutContent<T>;
        this.metadata.pageMetadata.layoutFields[this.elementId] = this.layout;
        return this;
    };
}
