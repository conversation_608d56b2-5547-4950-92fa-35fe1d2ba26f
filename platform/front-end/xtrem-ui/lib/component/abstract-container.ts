import type { ScreenBase } from '../service/screen-base';
import type { ValidationResult } from '../service/screen-base-definition';
import type { ValueOrCallbackWithFieldValue } from '../utils/types';
import type { UiComponentProperties } from './abstract-ui-control-object';
import { AbstractUiControlObject } from './abstract-ui-control-object';
import type { ContainerValidation } from './field/traits';
import type { ContainerKey, LayoutContent, ParentType } from './types';

export interface ContainerProperties<CT extends ScreenBase = ScreenBase>
    extends UiComponentProperties<CT>,
        ContainerValidation<CT> {
    /**
     * Whether the HTML element is disabled or not. Defaults to false
     *
     * The difference with readOnly is that disabled suggests that the field is not editable
     * for some validation reason (e.g. a button which can't be clicked due to validation errors)
     */
    isDisabled?: ValueOrCallbackWithFieldValue<CT, boolean>;
}

/**
 * Any container* than can be placed inside a page and can be interacted with (i.e. retrieving
 * and/or setting container's properties values).
 *
 * *A container is any element than can contain other elements and doesn't have a value itself
 * (e.g. a page, a section, a block, etc.)
 */
export abstract class AbstractContainer<
    CT extends ScreenBase,
    T extends ContainerKey,
    S extends ContainerProperties<CT>,
> extends AbstractUiControlObject<CT, S> {
    static readonly defaultUiProperties: Partial<ContainerProperties> = {
        ...AbstractUiControlObject.defaultUiProperties,
        isHidden: false,
    };

    constructor(
        screenId: string,
        elementId: string,
        _getUiComponentProperties: (screenId: string, elementId: string) => S,
        _setUiComponentProperties: (screenId: string, elementId: string, state: S) => void,
        protected _componentKey: T,
        private readonly _getValidationState: () => Promise<boolean>,
        public layout: LayoutContent<T>,
        public parent?: ParentType<T>,
    ) {
        super(screenId, elementId, _getUiComponentProperties, _setUiComponentProperties, _componentKey);
    }

    /** Whether the container has some validation errors or not */
    get isValid(): Promise<boolean> {
        return this._getValidationState();
    }

    /**
     * Triggers the validation rules of all the fields in the Fragment Page. Since the validation rules
     * might be asynchronous, this method returns a promise that must be awaited to get
     * the validation result
     */
    async validate(): Promise<string[]> {
        const result = await this.validateWithDetails();
        return result.map(r => r.message);
    }

    /**
     * Triggers the field validation rules. Since the validation rules might be asynchronous,
     * this method returns a promise that must be awaited to get the validation result. Compared to the `validate` method
     * it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
     */
    abstract validateWithDetails(
        partition: true,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
    abstract validateWithDetails(partition: false): Promise<ValidationResult[]>;
    abstract validateWithDetails(): Promise<ValidationResult[]>;
    abstract validateWithDetails(
        partition: false,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] } | ValidationResult[]>;
}
