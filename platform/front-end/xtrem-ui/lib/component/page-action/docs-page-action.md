## Documentation - PageAction
| name            | optional | description                                                                                                                                                                                                                                    | type                                                                                                   | default                                                                                                                                              |
| --------------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------- |
| access          | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">AccessConfiguration</pre>                                                       | <pre lang="javascript">{&nbsp;node:&nbsp;'<node>',&nbsp;bind:&nbsp;'<bind>'&nbsp;}</pre>                                                             |
| buttonType      | true     | Type of button, according with Carbon's API. Can be primary, secondary or tertiary                                                                                                                                                             | <pre lang="javascript">"primary"&nbsp;|&nbsp;"secondary"&nbsp;|&nbsp;"tertiary"</pre>                  | <pre lang="javascript">undefined</pre>                                                                                                               |
| helperText      | true     | The helper text underneath the field                                                                                                                                                                                                           | <pre lang="javascript">string</pre>                                                                    | <pre lang="javascript">''</pre>                                                                                                                      |
| icon            | true     | Icon of the input field. It will be placed on the right side                                                                                                                                                                                   | <pre lang="javascript">IconType</pre>                                                                  | <pre lang="javascript">undefined</pre>                                                                                                               |
| iconColor       | true     | Color of the icon, only supported in tile containers                                                                                                                                                                                           | <pre lang="javascript">string</pre>                                                                    | <pre lang="javascript">''</pre>                                                                                                                      |
| isDisabled      | true     | Whether the HTML element is disabled or not. Defaults to false The difference with readOnly is that disabled suggests that the field is not editable for some validation reason (e.g. a button which can't be clicked due to validation errors | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre> | <pre lang="javascript">false</pre>                                                                                                                   |
| isHidden        | true     | Whether the HTML element is hidden or not. Defaults to false                                                                                                                                                                                   | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre> | <pre lang="javascript">false</pre>                                                                                                                   |
| isHiddenDesktop | true     | Whether the element is hidden or not in desktop devices. Defaults to false                                                                                                                                                                     | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                                                                                   |
| isHiddenMobile  | true     | Whether the element is hidden or not in mobile devices. Defaults to false                                                                                                                                                                      | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                                                                                   |
| isIconButton    | true     | If set, only the icon is displayed, the title is used as a tooltip                                                                                                                                                                             | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                                                                                   |
| isTitleHidden   | true     | Whether the element title is hidden or not. Defaults to false                                                                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                                                                                   |
| isTransient     | true     | Whether the value is bound to a GraphQL node (transient = false) or not (transient = true). Defaults to false                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                                                                                   |
| onClick         | true     | Function to be executed when the field is clicked                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                         | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                                                                                          |
| onError         | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">ErrorHandlerFunction<CT></pre>                                                  | <pre lang="javascript">function(error,screenId,elementId<br>)&nbsp;{<br>console.error({&nbsp;error,&nbsp;screenId,&nbsp;elementId&nbsp;})<br>}</pre> |
| shortcut        | true     | Key combination that triggers this action                                                                                                                                                                                                      | <pre lang="javascript">Key&nbsp;|&nbsp;Key[]</pre>                                                     | <pre lang="javascript">[]</pre>                                                                                                                      |
| title           | true     | The title of the HTML element                                                                                                                                                                                                                  | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;any,&nbsp;Dict<any>></pre>  | <pre lang="javascript">''</pre>                                                                                                                      |