/**
 * @packageDocumentation
 * @module root
 * */

import type { Dict } from '@sage/xtrem-shared';
import type { DataTypeDetails, FormattedNodeDetails } from '../../service/metadata-types';
import type { Extend } from '../../service/page-extension';
import { getPageMetadata } from '../../service/page-metadata';
import type { ScreenBase } from '../../service/screen-base';
import type { ScreenExtension } from '../../types';
import type { ClickableOverrideDecoratorProperties } from '../../utils/decorator-utils';
import { getTargetPrototype, standardExtensionDecoratorImplementation } from '../../utils/decorator-utils';
import { AbstractDecorator } from '../abstract-decorator';
import type { Clickable, ExtensionField, HasShortcuts } from '../field/traits';
import { ActionKey } from '../types';
import type { PageActionProperties } from './page-action-control-object';
import { PageActionControlObject } from './page-action-control-object';

export interface PageActionDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<PageActionProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        ExtensionField<CT, PageActionControlObject<any>>,
        HasShortcuts {}

class PageActionDecorator extends AbstractDecorator<ActionKey.PageAction> {
    protected _controlObjectConstructor = PageActionControlObject;
}

/**
 * Initializes the decorated member as a [PageAction]{@link PageActionControlObject} with the provided properties
 *
 * @param properties The properties that the [PageAction]{@link PageActionControlObject} will be initialized with
 */
export function pageAction<CT extends ScreenExtension<CT>>(properties: PageActionDecoratorProperties<Extend<CT>>) {
    // eslint-disable-next-line func-names
    return function (target: CT, name: string): void {
        const targetPrototype = getTargetPrototype(target.constructor);
        const pageMetadata = getPageMetadata(targetPrototype, target);
        pageMetadata.pageActionThunks[name] = (
            nodeTypes: Dict<FormattedNodeDetails>,
            dataTypes: Dict<DataTypeDetails>,
        ): PageActionControlObject => {
            const controlObject = new PageActionDecorator(
                target.constructor.name.endsWith('Extension') ? targetPrototype.prototype : target,
                name,
                {
                    pageMetadata,
                    properties,
                },
                ActionKey.PageAction,
                nodeTypes,
                dataTypes,
                {},
            ).build().controlObject;
            return controlObject;
        };
    };
}

export function pageActionOverride<CT extends ScreenExtension<CT>>(
    properties: ClickableOverrideDecoratorProperties<PageActionDecoratorProperties<Extend<CT>>, Extend<CT>>,
): (target: CT, name: string) => void {
    return standardExtensionDecoratorImplementation<CT, ActionKey.PageAction>(properties);
}
