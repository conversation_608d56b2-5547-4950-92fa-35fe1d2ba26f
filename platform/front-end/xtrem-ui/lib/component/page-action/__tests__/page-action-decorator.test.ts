import { getMockPageDefinition } from '../../../__tests__/test-helpers/common-helpers';
import {
    addBusinessActionToState,
    getMockState,
    getMockStore,
} from '../../../__tests__/test-helpers/mock-store-helpers';
import type { XtremAppState } from '../../../redux';
import type { Page } from '../../../service/page';
import * as pageMetaData from '../../../service/page-metadata';
import * as eventUtils from '../../../utils/events';
import type { PageActionProperties } from '../../control-objects';
import { pageAction } from '../../decorators';

describe('Page Action decorator', () => {
    describe('mapping values', () => {
        let componentProperties: any;

        beforeEach(() => {
            componentProperties = {
                controlObjects: {},
                uiComponentProperties: {},
                extensionOverrideThunks: {},
                pageActions: {},
                defaultUiComponentProperties: {},
                pageActionThunks: {},
            };
            jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(componentProperties);
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should set default values when no component properties provided', () => {
            pageAction({})({} as Page, 'fixture');
            componentProperties.pageActionThunks.fixture();
            const mappedComponentProperties = componentProperties.uiComponentProperties.fixture as PageActionProperties;
            expect(mappedComponentProperties.isHidden).toBe(false);
        });

        it('should set values when component properties provided', () => {
            pageAction({
                title: 'Test title',
                isHidden: true,
                isDisabled: true,
                helperText: 'test',
                icon: 'shop',
                shortcut: ['alt', 'shift', 'r'],
            })({} as Page, 'fixture');

            componentProperties.pageActionThunks.fixture();

            const mappedComponentProperties = componentProperties.uiComponentProperties.fixture as PageActionProperties;

            expect(mappedComponentProperties.isHidden).toBe(true);
            expect(mappedComponentProperties.isDisabled).toBe(true);
            expect(mappedComponentProperties.helperText).toBe('test');
            expect(mappedComponentProperties.title).toBe('Test title');
            expect(mappedComponentProperties.shortcut).toEqual(['alt', 'shift', 'r']);
        });

        it('should set values when component duplicate and save page properties to the default component properties', () => {
            pageAction({
                title: 'Test title',
                isHidden: true,
                isDisabled: true,
                helperText: 'test',
                icon: 'shop',
            })({} as Page, 'fixture');

            componentProperties.pageActionThunks.fixture();

            const mappedComponentProperties = componentProperties.uiComponentProperties.fixture as PageActionProperties;
            const mappedDefaultComponentProperties = componentProperties.defaultUiComponentProperties
                .fixture as PageActionProperties;

            // Should not be the same but have equal properties
            expect(mappedComponentProperties).not.toBe(mappedDefaultComponentProperties);

            expect(mappedDefaultComponentProperties.isHidden).toBe(mappedComponentProperties.isHidden);
            expect(mappedDefaultComponentProperties.isDisabled).toBe(mappedComponentProperties.isDisabled);
            expect(mappedDefaultComponentProperties.helperText).toBe(mappedComponentProperties.helperText);
            expect(mappedDefaultComponentProperties.title).toBe(mappedComponentProperties.title);
        });
    });

    describe('execute method', () => {
        let state: XtremAppState;

        beforeEach(() => {
            state = getMockState();
            state.screenDefinitions.testId = getMockPageDefinition('testId');
            getMockStore(state, false);
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });
        it('should call onClick callback', async () => {
            const triggerEventListenerSpy = jest.spyOn(eventUtils, 'triggerHandledEvent');
            const onClickMock = jest.fn();
            const businessAction = addBusinessActionToState(state, 'testId', 'id', {
                title: 'Test title',
                isHidden: false,
                isDisabled: false,
                onClick: onClickMock,
                onError: jest.fn(() => {}),
            });

            await businessAction.execute();
            expect(triggerEventListenerSpy).toHaveBeenCalledTimes(1);
            expect(triggerEventListenerSpy).toHaveBeenCalledWith(
                'testId',
                'id',
                expect.objectContaining({ onClick: onClickMock, delegateErrorToCaller: true }),
            );
        });

        it('should call errorHandler if an error occurs and argument is true', async () => {
            const triggerEventListenerSpy = jest.spyOn(eventUtils, 'triggerHandledEvent');
            const onClickMock = jest.fn().mockImplementation(() => {
                throw Error('Error2');
            });
            const onErrorMock = jest.fn(() => {});
            const businessAction = addBusinessActionToState(state, 'testId', 'id', {
                title: 'Test title',
                isHidden: false,
                isDisabled: false,
                onClick: onClickMock,
                onError: onErrorMock,
            });
            await businessAction.execute(true);
            expect(triggerEventListenerSpy).toHaveBeenCalledWith(
                'testId',
                'id',
                expect.objectContaining({ onClick: onClickMock, onError: onErrorMock }),
            );
            expect(onErrorMock).toHaveBeenCalledTimes(1);
        });

        it('should throw an error if error occurs and argument is false or missing', async () => {
            const onClickMock = jest.fn(() => {
                throw Error('Error');
            });
            const onErrorMock = jest.fn(() => {});
            const businessAction = addBusinessActionToState(state, 'testId', 'id', {
                title: 'Test title',
                isHidden: false,
                isDisabled: false,
                onClick: onClickMock,
                onError: onErrorMock,
            });

            await expect(businessAction.execute()).rejects.toThrow('Error');
            expect(onErrorMock).toHaveBeenCalledTimes(0);
        });
    });
});
