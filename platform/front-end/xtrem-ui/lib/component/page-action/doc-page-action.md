PATH: XTREEM/Client+Framework/Page+Action

## Introduction

Page action is a decorator where a specific business action can be defined. They can be consumed by a Page decorator, in the `businessActions` and `detailPanel` page decorator methods. When defined, a page action is going to be placed at the bottom of the page or the detail panel, as a footer.

## Example:

```ts
@ui.decorators.page<PageAction>({
    authorizationCode: 'PageActionExample',
    businessActions() {
        return [this.pageAction];
    },
    detailPanel() {
        return {
            activeSection: 0,
            footerActions: [this.pageAction],
            header: this.detailPanelHeaderSection,
            sections: [this.detailPanelBodySection1, this.detailPanelBodySection2],
        };
    },
    title: 'Page Action Example',
})
export class DetailPanel extends ui.Page {
@ui.decorators.pageAction<PageAction>({
        title: 'Page Action',
        shortcut: 'f2',
        onClick() {
            this.$.showToast('You clicked on a page action ');
        },
    })
    pageAction: ui.PageAction;
}
```

### Display decorator properties:

-   **buttonType**: Type of button, according with Carbon's API. Can be `primary`, `secondary` or `tertiary`. By default, for a page, button is `secondary` and for the detail panel `primary`
-   **helperText**: Additional text that will be displayed when mousing over the page action.
-   **icon**: icon displayed in the button.
-   **shortcut**: key or key combination that triggers the onClick event, more about key combinations [here](./Key+Shortcuts+API).
-   **isDisabled**: Whether the HTML element is disabled or not. It can also be defined as callback function that returns a boolean. Defaults to false.
-   **isDestructive**: Makes the corresponding UI component to render the button or icon in red that indicates the nature of the action.
-   **isHidden**: Whether the HTML element is hidden or not. It can also be defined as callback function that returns a boolean. Defaults to false.
-   **isLoading**: Whether the page action is loading or not. If loading, the button is disabled and a loader is displayed. Only works if the action is used as a business action. Default to false Defaults to false.
-   **isTitleHidden**: Whether the element title is hidden or not. Defaults to false.
-   **title**: The title of the HTML element.
-   **insertBefore**: Callback function that returns an existing page action object instance before which the current action should be inserted. This property is only considered when extending the headerDropdownActions or the headerQuickActions by extending the page. If the specified page action's ID is not found, the action will be added at the end.
-   **insertAfter**: Callback function that returns an existing page action object instance before which the current action should be inserted. This property is only considered when extending the headerDropdownActions or the headerQuickActions by extending the page. If the specified page action's ID is not found, the action will be added at the end.

### Event handler decorator properties:

-   **onClick**: Function to be executed when the page action is clicked.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

### Runtime functions

-   **execute(executeErrorHandlers, ...args)**:  Executes the onClick callback of the page action. If the `executeErrorHandlers` flag is set to true, the content of the onClick function of the action will be executed within the error handler scope. The args are passed on to the onClick event of the page action.

## Sandbox

Check out element on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/DetailPanel).
