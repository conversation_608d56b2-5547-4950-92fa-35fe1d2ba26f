/**
 * @packageDocumentation
 * @module root
 * */
import type { ButtonProps } from 'carbon-react/esm/components/button';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { ScreenBase } from '../../service/screen-base';
import {
    getUiComponentProperties as defaultGetUiComponentProperties,
    setUiComponentProperties as defaultSetUiComponentProperties,
} from '../../service/transactions-service';
import type { ScreenExtension } from '../../types';
import { triggerHandledEvent } from '../../utils/events';
import type { ValueOrCallbackWithFieldValue } from '../../utils/types';
import type { UiComponentProperties } from '../abstract-ui-control-object';
import { AbstractUiControlObject } from '../abstract-ui-control-object';
import type { Clickable, HasHelperText, HasIcon, HasShortcuts } from '../field/traits';
import { ControlObjectProperty } from '../property-decorators/control-object-property-decorator';
import { FieldControlObjectResolvedProperty } from '../property-decorators/control-object-resolved-property-decorator';
import type { ControlObjectConstructorProps } from '../types';
import { ActionKey } from '../types';

export type PageActionButtonType = ButtonProps['buttonType'];

export interface PageActionProperties<CT extends ScreenBase = ScreenBase>
    extends UiComponentProperties<CT>,
        HasHelperText,
        Clickable<CT>,
        HasIcon,
        HasShortcuts {
    /** Type of button, according with Carbon's API. Can be primary, secondary or tertiary */
    buttonType?: PageActionButtonType;
    /** If set the action's icon and text are rendered in red. Optional, defaults to false. */
    isDestructive?: boolean;
    /** Whether the HTML element is hidden or not. Defaults to false */
    isHidden?: ValueOrCallbackWithFieldValue<CT, boolean>;
    /** If set, only the icon is displayed, the title is used as a tooltip */
    isIconButton?: boolean;
    /** Whether the page action is a menu separator or not. Default to false */
    isMenuSeparator?: boolean;
    /** Whether the page action is loading or not. If loading, the button is disabled and a loader is displayed.
     * Only works if the action is used as a business action. Default to false
     * */
    isLoading?: boolean;
}
export interface IPageAction<CT extends ScreenExtension<CT> = ScreenBase> {
    screenId: string;
    elementId: string;
    getUiComponentProperties?: (screenId: string, elementId: string) => PageActionProperties<CT>;
    setUiComponentProperties?: (screenId: string, elementId: string, state: PageActionProperties<CT>) => void;
    insertBefore?: (context: CT) => PageActionControlObject<CT>;
    insertAfter?: (context: CT) => PageActionControlObject<CT>;
}

/**
 * Action that will be placed in the page header (i.e. business action)
 */
export class PageActionControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends AbstractUiControlObject<
    CT,
    PageActionProperties<CT>
> {
    static readonly defaultUiProperties: Partial<PageActionProperties<any>> = {
        ...AbstractUiControlObject.defaultUiProperties,
        isHidden: false,
        isMenuSeparator: false,
    };

    public insertBefore?: ControlObjectConstructorProps<ActionKey.PageAction>['insertBefore'];

    public insertAfter?: ControlObjectConstructorProps<ActionKey.PageAction>['insertAfter'];

    constructor({
        screenId,
        elementId,
        getUiComponentProperties = defaultGetUiComponentProperties,
        setUiComponentProperties = defaultSetUiComponentProperties,
        insertBefore,
        insertAfter,
    }: IPageAction<CT>) {
        super(screenId, elementId, getUiComponentProperties, setUiComponentProperties, ActionKey.PageAction);
        this.insertBefore = insertBefore;
        this.insertAfter = insertAfter;
    }

    @ControlObjectProperty<PageActionProperties<CT>, PageActionControlObject<CT>>()
    /** Additional text that will be displayed when mousing over the page action */
    helperText?: string;

    @ControlObjectProperty<PageActionProperties<CT>, PageActionControlObject<CT>>()
    /** Icon of the action */
    icon?: IconType;

    @FieldControlObjectResolvedProperty<PageActionProperties<CT>, PageActionControlObject<CT>>()
    /** Whether the action is displayed */
    isHidden?: boolean;

    @FieldControlObjectResolvedProperty<PageActionProperties<CT>, PageActionControlObject<CT>>()
    /** Whether the page action is loading or not. If loading, the button is disabled and a loader is displayed.
     * Only works if the action is used as a business action. Default to false
     * */
    isLoading?: boolean;

    @FieldControlObjectResolvedProperty<PageActionProperties<CT>, PageActionControlObject<CT>>()
    /** Whether the action is a menu separator */
    isMenuSeparator?: boolean;

    @ControlObjectProperty<PageActionProperties<CT>, PageActionControlObject<CT>>()
    /** Whether the action is displayed */
    buttonType?: PageActionButtonType;

    async execute(executeErrorHandlers = false, ...args: any[]): Promise<void> {
        const onClick = this.getUiComponentProperty('onClick');
        if (onClick) {
            if (executeErrorHandlers) {
                await triggerHandledEvent(
                    this.screenId,
                    this.elementId,
                    {
                        onClick,
                        onError: this.getUiComponentProperty('onError') || undefined,
                    },
                    ...(args as any),
                );
            } else {
                await triggerHandledEvent(
                    this.screenId,
                    this.elementId,
                    {
                        onClick,
                        delegateErrorToCaller: true,
                    },
                    ...(args as any),
                );
            }
        }
    }
}
