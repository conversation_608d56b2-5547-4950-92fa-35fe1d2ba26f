/**
 * @packageDocumentation
 * @module root
 * */

export {
    AggregateDecoratorProperties,
    BlockDecoratorProperties,
    ButtonDecoratorProperties,
    CalendarDecoratorProperties,
    CardDecoratorProperties,
    ChartDecoratorProperties,
    CheckboxDecoratorProperties,
    ContentTableDecoratorProperties,
    CountDecoratorProperties,
    DateDecoratorProperties,
    DatetimeDecoratorProperties,
    DetailListDecoratorProperties,
    DetailPanelDecoratorProperties,
    DropdownListDecoratorProperties,
    DynamicPodDecoratorProperties,
    DynamicSelectDecoratorProperties,
    FileDecoratorProperties,
    FileDepositDecoratorProperties,
    FilterEditorDecoratorProperties,
    FilterSelectDecoratorProperties,
    FormDesignerDecoratorProperties,
    FragmentFieldsDecoratorProperties,
    GridRowBlockDecoratorProperties,
    IconDecoratorProperties,
    ImageDecoratorProperties,
    LabelDecoratorProperties,
    LinkDecoratorProperties,
    MessageDecoratorProperties,
    MultiDropdownDecoratorProperties,
    MultiFileDepositDecoratorProperties,
    MultiReferenceDecoratorProperties,
    NestedGridDecoratorProperties,
    NodeBrowserTreeDecoratorProperties,
    NumericDecoratorProperties,
    PageActionDecoratorProperties,
    PageDecoratorProperties,
    PageExtensionDecoratorProperties,
    PageFragmentDecoratorProperties,
    PdfDecoratorProperties,
    PluginDecoratorProperties,
    PodDecoratorProperties,
    ProgressDecoratorProperties,
    RadioDecoratorProperties,
    ReferenceDecoratorProperties,
    RelativeDateDecoratorProperties,
    RichTextDecoratorProperties,
    SectionDecoratorProperties,
    SelectDecoratorProperties,
    SelectionCardDecoratorProperties,
    SeparatorDecoratorProperties,
    StaticContentDecoratorProperties,
    StepSequenceDecoratorProperties,
    StickerDecoratorProperties,
    SwitchDecoratorProperties,
    TableDecoratorProperties,
    TableSummaryDecoratorProperties,
    TextAreaDecoratorProperties,
    TextDecoratorProperties,
    TileDecoratorProperties,
    TechnicalJsonDecoratorProperties,
    TimeDecoratorProperties,
    ToggleDecoratorProperties,
    TreeDecoratorProperties,
    VisualProcessDecoratorProperties,
    VitalPodDecoratorProperties,
    WorkflowDecoratorProperties,
} from './decorators';
