import type { BinaryStream, ClientCollection, ClientNode } from '@sage/xtrem-client';
import { strictTypeMatch } from '@sage/xtrem-shared';
import { expectError, expectType } from 'tsd';
import { Page } from '../service/page';
import type { ScreenBase } from '../service/screen-base';
import type { ScreenExtension } from '../types';
import type { ValueOrCallback, ValueOrCallbackKeysExtractor, ValueOrCallbackWithFieldValue } from '../utils/types';
import type {
    ClientCollectionValueType,
    PropertyValueType,
    ReferenceValueType,
} from './field/reference/reference-types';
import { text } from './nested-fields';
import type { OrderByType, PartialCollectionValue, ReferenceRecursiveOrderBy } from './types';

enum ShowCaseProductCategory {
    great = 0,
    good = 1,
    ok = 2,
    notBad = 3,
    awful = 4,
}

interface ShowCaseInvoiceLine extends ClientNode {
    invoice: ShowCaseInvoice;
    provider: ShowCaseProvider;
    details: string;
}
interface ShowCaseInvoice extends ClientNode {
    invoiceDate: string;
    lines: ClientCollection<ShowCaseInvoiceLine>;
}
interface ShowCaseProduct extends ClientNode {
    product: string;
    hotProduct: boolean;
    qty: number;
    st: number;
    progress: number;
    provider: ShowCaseProvider;
    category: keyof typeof ShowCaseProductCategory;
    image: BinaryStream;
    invoices: ClientCollection<ShowCaseInvoice>;
}

interface ShowCaseProviderAddress extends ClientNode {}
interface ShowCaseProvider extends ClientNode {
    textField: string;
    integerField: number;
    decimalField: string;
    booleanField: boolean;
    dateField: string;
    value: string;
    products: ClientCollection<ShowCaseProduct>;
    mainAddress: ShowCaseProviderAddress;
}

// Test 'PartialCollectionValue'
expectType<PartialCollectionValue<ShowCaseProvider>>({
    _id: '',
    _createStamp: '',
    _updateStamp: '',
    textField: '',
    integerField: 1,
    decimalField: '',
    booleanField: true,
    dateField: '',
    products: [
        {
            _id: '',
            _createStamp: '',
            _updateStamp: '',
            product: '',
            hotProduct: false,
            qty: 2,
            st: 3,
            progress: 4,
            provider: {
                _id: '',
            },
            category: 'good' as const,
            image: {
                value: '',
            },
        },
    ],
});
expectType<PartialCollectionValue<ShowCaseProvider>>({
    _id: '',
    _createStamp: '',
    _updateStamp: '',
    textField: '',
    integerField: 1,
    decimalField: '',
    booleanField: true,
    dateField: '',
    products: [
        {
            _id: '',
            _createStamp: '',
            _updateStamp: '',
            product: '',
            hotProduct: false,
            qty: 2,
            st: 3,
            progress: 4,
            category: 'good' as const,
            image: {
                value: '',
            },
        },
    ],
});
expectType<PartialCollectionValue<ShowCaseProvider>>({
    _id: '',
    _createStamp: '',
    _updateStamp: '',
    textField: '',
    integerField: 1,
    decimalField: '',
    booleanField: true,
    dateField: '',
    products: [],
});
expectType<PartialCollectionValue<ShowCaseProvider>>({
    _id: '',
    _createStamp: '',
    _updateStamp: '',
    textField: '',
    integerField: 1,
    decimalField: '',
    booleanField: true,
    dateField: '',
});
expectType<PartialCollectionValue<ShowCaseProduct>>({ invoices: [{ lines: [{ details: 'details', provider: {} }] }] });

// Test orderBy
expectType<OrderByType<any>>({});
expectType<OrderByType<ShowCaseProduct>>({ hotProduct: 1 });
// Test reference recursive orderBy
expectType<ReferenceRecursiveOrderBy<any>>({});
expectType<ReferenceRecursiveOrderBy<any>>({ provider: { dateField: 1 } });
expectType<ReferenceRecursiveOrderBy<ShowCaseProduct>>({ qty: 1 });
expectType<ReferenceRecursiveOrderBy<ShowCaseProduct>>({ provider: { dateField: 1 } });
// @ts-expect-error
expectType<ReferenceRecursiveOrderBy<ShowCaseProduct>>({ invoices: { invoiceDate: 1 } });

const p: PropertyValueType = {};
expectType<PropertyValueType<ShowCaseProduct>>(p);

// Test reference 'valueField'/'helperTextField'
expectType<PropertyValueType<ShowCaseProduct>>({ provider: { integerField: true } });
expectType<PropertyValueType<ShowCaseProduct>>({ _id: true });
// @ts-expect-error
expectError<PropertyValueType<ShowCaseProduct>>({ provider: true });
// @ts-expect-error
expectError<PropertyValueType<ShowCaseProduct>>({ invoices: true });
// @ts-expect-error
expectError<PropertyValueType<ShowCaseProduct>>({ provider: { integerField: true, booleanField: true } });
// @ts-expect-error
expectError<PropertyValueType<ShowCaseProduct>>({ provider: { products: true } });
// @ts-expect-error
expectError<PropertyValueType<ShowCaseProduct>>({ _id: true, integerField: true });
// @ts-expect-error
expectError<PropertyValueType<ShowCaseProduct>>({});

// @ts-expect-error
expectError<ReferenceValueType<ShowCaseProduct>>({});
// @ts-expect-error
expectType<ReferenceValueType<ShowCaseProduct>>({ _id: true });
expectError<ReferenceValueType<ShowCaseProduct>>({ provider: true });
// @ts-expect-error
expectError<ReferenceValueType<ShowCaseProduct>>({ invoices: true });
// @ts-expect-error
expectError<ReferenceValueType<ShowCaseProduct>>({ provider: { integerField: true, booleanField: true } });
// @ts-expect-error
expectError<ReferenceValueType<ShowCaseProduct>>({ provider: { products: true } });
// @ts-expect-error
expectError<ReferenceValueType<ShowCaseProduct>>({ _id: true, integerField: true });
// @ts-expect-error
expectError<ReferenceValueType<ShowCaseProduct>>({});
expectType<ReferenceValueType<ShowCaseInvoiceLine>>({ provider: { mainAddress: true } });
expectType<ClientCollectionValueType<ShowCaseInvoice>>({ lines: true });

interface FieldProps {
    p1: ValueOrCallback<any, string>;
    p2: ValueOrCallback<any, number>;
    p3: string;
    p4: ValueOrCallback<any, Date>;
    p5: string | boolean;
    p6: number;
    p7?: number;
    p8: Function;
    p9: () => boolean | (() => string);
    p10: ValueOrCallbackWithFieldValue<any, Date>;
}
strictTypeMatch<ValueOrCallbackKeysExtractor<FieldProps>, 'p1' | 'p2' | 'p4' | 'p10'>(true);

class Test extends Page {
    anyMethod(): void {
        this.$.dialog.lookup({
            node: 'test',
            columns: [
                text({
                    bind: '_id',
                    onChange(): void {
                        this.anyMethod();
                    },
                }),
            ],
        });
    }
}
expectType<ScreenBase<any, any>>(new Test());
expectType<ScreenExtension<Test>>(new Test());
declare const page: Page<any, any>;
expectType<ScreenExtension<Page<any, any>>>(page);
