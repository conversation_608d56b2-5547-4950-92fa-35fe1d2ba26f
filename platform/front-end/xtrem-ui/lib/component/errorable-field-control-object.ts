import type { ScreenBase } from '../service/screen-base';
import type { EditableFieldProperties } from './editable-field-control-object';
import { EditableFieldControlObject } from './editable-field-control-object';
import type { <PERSON>Key } from './types';

export interface ErrorableFieldProperties<CT extends ScreenBase = ScreenBase> extends EditableFieldProperties<CT> {}

// Represents editable elements that can be placed inside a page and can hold an internal error.
export abstract class ErrorableFieldControlObject<
    CT extends ScreenBase,
    T extends FieldKey,
    S extends ErrorableFieldProperties<CT>,
> extends EditableFieldControlObject<CT, T, S> {
    static readonly defaultUiProperties: Partial<ErrorableFieldProperties> = {
        ...EditableFieldControlObject.defaultUiProperties,
    };
}
