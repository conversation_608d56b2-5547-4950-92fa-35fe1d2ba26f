import type { ScreenBase } from '../service/screen-base';
import type { ValidationResult } from '../service/screen-base-definition';
import { triggerHandledEvent } from '../utils/events';
import type { ValueOrCallbackWithFieldValue } from '../utils/types';
import type { CanBeMandatory, CanBeReadOnly, Changeable } from './field/traits';
import { ControlObjectProperty } from './property-decorators/control-object-property-decorator';
import { FieldControlObjectResolvedProperty } from './property-decorators/control-object-resolved-property-decorator';
import type { ReadonlyFieldProperties } from './readonly-field-control-object';
import { ReadonlyFieldControlObject } from './readonly-field-control-object';
import type { FieldControlObjectConstructorProps, FieldKey } from './types';

export interface EditableFieldProperties<
    /** Type of the screen that the field is loaded in */
    CT extends ScreenBase = ScreenBase,
    /** Type of the nested context that the field is loaded into. For example, the type of the row that this nested field is rendered into */
    ContextNodeType = any,
> extends ReadonlyFieldProperties<CT>,
        CanBeReadOnly<CT, ContextNodeType>,
        CanBeMandatory<CT, ContextNodeType>,
        Changeable<CT> {
    /** Indicate additional warning message, rendered as tooltip and blue border. */
    infoMessage?: ValueOrCallbackWithFieldValue<CT, string, ContextNodeType>;

    /** Indicate additional information, rendered as tooltip and orange border. */
    warningMessage?: ValueOrCallbackWithFieldValue<CT, string, ContextNodeType>;
}

/**
 * Any element holding a value than can be placed inside a page and can be interacted with (i.e. retrieving
 * and/or setting field's ui properties values). The element value CAN be modified
 */
export abstract class EditableFieldControlObject<
    CT extends ScreenBase,
    T extends FieldKey,
    S extends EditableFieldProperties<CT>,
> extends ReadonlyFieldControlObject<CT, T, S> {
    static readonly defaultUiProperties: Partial<EditableFieldProperties> = {
        ...ReadonlyFieldControlObject.defaultUiProperties,
        isMandatory: false,
        isReadOnly: false,
    };

    protected readonly _dispatchFieldValidation: (
        screenId: string,
        elementId: string,
    ) => Promise<ValidationResult[] | undefined>;

    protected readonly _isFieldDirty: (screenId: string, elementId: string) => boolean;

    protected _setFieldDirty: (screenId: string, elementId: string) => void;

    protected _setFieldClean: (screenId: string, elementId: string) => void;

    constructor(properties: FieldControlObjectConstructorProps<T>) {
        super(properties);
        this._isFieldDirty = properties.isFieldDirty;
        this._setFieldDirty = properties.setFieldDirty;
        this._setFieldClean = properties.setFieldClean;
        this._dispatchFieldValidation = (_screenId: string): Promise<ValidationResult[] | undefined> => {
            return properties.dispatchValidation
                ? properties.dispatchValidation(_screenId, this.elementId)
                : Promise.reject();
        };
    }

    /** Whether is mandatory or not to provide a value for the field */
    @FieldControlObjectResolvedProperty<EditableFieldProperties<CT>, EditableFieldControlObject<CT, T, S>>()
    isMandatory?: boolean;

    /**
     * Whether the field is editable (isReadOnly = false) or not (isReadOnly = true)
     *
     * The difference with disabled is that isReadOnly suggests that the field is never editable
     * (e.g. the id field of an object)
     */
    @FieldControlObjectResolvedProperty<EditableFieldProperties<CT>, EditableFieldControlObject<CT, T, S>>()
    isReadOnly: boolean;

    /**
     * Indicate additional warning message, rendered as tooltip and blue border.
     */
    @ControlObjectProperty<EditableFieldProperties<CT>, EditableFieldControlObject<CT, T, S>>()
    infoMessage: string;

    /**
     * Indicate additional information, rendered as tooltip and orange border
     */
    @ControlObjectProperty<EditableFieldProperties<CT>, EditableFieldControlObject<CT, T, S>>()
    warningMessage: string;

    get isDirty(): boolean {
        return this._isFieldDirty(this.screenId, this.elementId);
    }

    set isDirty(newValue: boolean) {
        if (newValue) {
            this._setFieldDirty(this.screenId, this.elementId);
        } else {
            this._setFieldClean(this.screenId, this.elementId);
        }
    }

    /**
     * Triggers the field validation rules. Since the validation rules might be asynchronous,
     * this method returns a promise that must be awaited to get the validation result
     */
    async validate(): Promise<string | undefined> {
        const validationResult = await this._dispatchFieldValidation(this.screenId, this.elementId);
        return validationResult === undefined || validationResult.length === 0
            ? undefined
            : validationResult.map(v => v.message).join('\n');
    }

    /**
     * Triggers the field validation rules. Since the validation rules might be asynchronous,
     * this method returns a promise that must be awaited to get the validation result. Compared to the `validate` method
     * it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
     */
    async validateWithDetails(): Promise<ValidationResult[] | undefined> {
        return this._dispatchFieldValidation(this.screenId, this.elementId);
    }

    async executeOnChange(executeErrorHandlers = false): Promise<void> {
        const onChange = this.getUiComponentProperty('onChange');
        if (onChange) {
            if (executeErrorHandlers) {
                await triggerHandledEvent(this.screenId, this.elementId, {
                    onChange,
                    onError: this.getUiComponentProperty('onError') || undefined,
                });
            } else {
                await triggerHandledEvent(this.screenId, this.elementId, {
                    onChange,
                    delegateErrorToCaller: true,
                });
            }
        }
    }
}
