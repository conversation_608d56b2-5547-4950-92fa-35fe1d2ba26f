import type { Dict } from '@sage/xtrem-shared';
import type { DataTypeDetails, FormattedNodeDetails } from '../service/metadata-types';
import { getPageMetadata } from '../service/page-metadata';
import type { PageMetadata } from '../service/page-metadata';
import { setDefaultProperties } from '../utils/abstract-fields-utils';
import { overrideExtendedProperties } from './abstract-decorator-utils';
import type { UiComponentProperties } from './abstract-ui-control-object';
import type { TableProperties } from './control-objects';
import type { ExtensionField, HasParent } from './field/traits';
import { withoutNestedTechnical } from './nested-fields';
import type {
    ComponentKey,
    ControlObjectConstructor,
    ControlObjectConstructorProps,
    ControlObjectInstance,
    DecoratorProperties,
    DecoratorTarget,
    LayoutBuilder,
    LayoutContent,
    MetadataProps,
} from './types';
import { applyDefaultValuesOnNestedField } from '../utils/data-type-utils';
import { set } from 'lodash';

export class AbstractDecorator<T extends ComponentKey> {
    protected _layout: new (
        target: DecoratorTarget<T>,
        elementId: string,
        nodeTypes: Dict<FormattedNodeDetails>,
        dataTypes: Dict<DataTypeDetails>,
        props: MetadataProps<T>,
    ) => LayoutBuilder<T>;

    protected _controlObjectConstructor: ControlObjectConstructor<T>;

    protected layoutBuilder: LayoutBuilder<T>;

    protected controlObjectInstance: ControlObjectInstance<T>;

    constructor(
        protected target: DecoratorTarget<T>,
        protected elementId: string,
        protected _metadataProps: MetadataProps<T>,
        protected componentType: T,
        protected nodeTypes: Dict<FormattedNodeDetails>,
        protected dataTypes: Dict<DataTypeDetails>,

        protected _controlObjectConstructorProps: Partial<ControlObjectConstructorProps<T>>,
    ) {}

    public build = (): this => {
        return this.buildActions().buildLayout().buildControlObject().buildMetadata();
    };

    private getScreenId(): string {
        const metadata = getPageMetadata(this.target as Function);
        if (typeof metadata.screenId === 'string') {
            return metadata.screenId;
        }
        if (typeof this.target === 'function') {
            return (this.target as Function).name;
        }

        return this.target.constructor.name;
    }

    protected readonly buildLayout = (): this => {
        if (this._layout && this._metadataProps) {
            this.layoutBuilder = new this._layout(
                this.target,
                this.elementId,
                this.nodeTypes,
                this.dataTypes,
                this._metadataProps,
            ).build() as LayoutBuilder<T>;
        }
        return this;
    };

    protected readonly buildActions = (): this => {
        return this;
    };

    protected readonly buildControlObject = (): this => {
        if (!this._controlObjectConstructor || !this._controlObjectConstructorProps) {
            throw new Error('Did you forget to pass a control object to your new component?');
        }

        this.controlObjectInstance = new this._controlObjectConstructor({
            ...this._controlObjectConstructorProps,
            screenId: this.getScreenId(),
            elementId: this.elementId,
            layout: this.layoutBuilder ? this.layoutBuilder.layout : undefined,
            parent:
                'parent' in this._metadataProps.properties
                    ? (this._metadataProps.properties as HasParent<any, any>).parent
                    : undefined,
            insertBefore:
                'insertBefore' in this._metadataProps.properties
                    ? (this._metadataProps.properties as ExtensionField<any, UiComponentProperties>).insertBefore
                    : undefined,
            insertAfter:
                'insertAfter' in this._metadataProps.properties
                    ? (this._metadataProps.properties as ExtensionField<any, UiComponentProperties>).insertAfter
                    : undefined,
        } as ControlObjectConstructorProps<T>);

        return this;
    };

    protected readonly buildMetadata = (): this => {
        return this.setTarget().setControlObject().setUiComponentProperties();
    };

    private readonly setTarget = (): this => {
        if (this.target && this.elementId) {
            set(this.target, this.elementId, this.controlObjectInstance);
        }
        return this;
    };

    protected getPageMetadata(): PageMetadata {
        const actualPageMetadata: PageMetadata =
            this.layoutBuilder && this.layoutBuilder.metadata
                ? this.layoutBuilder.metadata.pageMetadata
                : this._metadataProps.pageMetadata;

        return actualPageMetadata;
    }

    private readonly setControlObject = (): this => {
        const actualPageMetadata = this.getPageMetadata();
        if (this.elementId) {
            actualPageMetadata.controlObjects[this.elementId] = this.controlObjectInstance;
        }
        return this;
    };

    protected setUiComponentProperties(dataTypeDefaults: Partial<DecoratorProperties<T>> = {}): this {
        const actualPageMetadata = this.getPageMetadata();

        if (this.elementId) {
            const properties = {
                ...dataTypeDefaults,
                ...setDefaultProperties(
                    overrideExtendedProperties<T>(
                        this.nodeTypes,
                        this.elementId,
                        actualPageMetadata,
                        this._metadataProps.properties,
                        this.componentType,
                    ),
                    this._controlObjectConstructor.defaultUiProperties,
                    this.componentType,
                    this._metadataProps.extensionPackageName,
                ),
            };
            actualPageMetadata.uiComponentProperties[this.elementId] = properties;
            actualPageMetadata.defaultUiComponentProperties[this.elementId] = { ...properties };

            const columns = (properties as TableProperties).columns;
            const mobileCard = (properties as TableProperties).mobileCard;
            const node = (properties as TableProperties).node;
            if (columns) {
                withoutNestedTechnical(columns).forEach(nestedField => {
                    nestedField.properties = setDefaultProperties(
                        nestedField.properties,
                        nestedField.defaultUiProperties,
                    );

                    if (node) {
                        applyDefaultValuesOnNestedField(this.nodeTypes, this.dataTypes, nestedField, String(node));
                    }
                });
            }

            if (mobileCard) {
                Object.values(mobileCard).forEach(nestedField => {
                    if (!nestedField) return;

                    nestedField.properties = setDefaultProperties(
                        nestedField.properties,
                        nestedField.defaultUiProperties,
                    );

                    if (node) {
                        applyDefaultValuesOnNestedField(this.nodeTypes, this.dataTypes, nestedField, String(node));
                    }
                });
            }

            // TODO Initialize nested fields properties with default values for Calendar and Chart
        }

        return this;
    }

    public get layout(): LayoutContent<T> {
        return this.layoutBuilder.layout as LayoutContent<T>;
    }

    public get controlObject(): AbstractDecorator<T>['controlObjectInstance'] {
        return this.controlObjectInstance;
    }
}
