// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Detail panel component should not render when set to be hidden 1`] = `<div />`;

exports[`Detail panel component should render with default properties 1`] = `
.c2 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c2::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: transparent;
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0 .c1 {
  color: var(--colorsActionMajor500);
}

.c0:hover {
  background: var(--colorsActionMajor600);
  color: var(--colorsActionMajorYang100);
}

.c0:hover .c1 {
  color: var(--colorsActionMajorYang100);
}

.c0 .c1 {
  margin-bottom: 0px;
  margin-left: 0px;
  margin-right: var(--spacing100);
  height: 20px;
  width: 20px;
}

.c0 .c1 svg {
  margin-top: 0;
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <aside
    class="e-detail-panel"
    data-testid="e-detail-panel"
  >
    <div
      class="e-detail-panel-close"
    >
      <button
        class="c0"
        data-component="button"
        data-testid="e-detail-panel-close"
        draggable="false"
        type="button"
      >
        <span
          aria-hidden="true"
          class="c1 c2"
          color="--colorsActionMajor500"
          data-component="icon"
          data-element="close"
          data-role="icon"
          font-size="small"
          type="close"
        />
        <span>
          <span
            class="c3"
            data-element="main-text"
          >
            <span
              class="e-screen-reader-only"
            >
              Close helper panel
            </span>
          </span>
        </span>
      </button>
    </div>
    <div
      class="e-detail-panel-header"
      data-testid="e-detail-panel-header"
    >
      <section
        class="e-section e-section-context-detail-panel"
        data-testid="e-section-field e-field-label-section1 e-field-bind-section1"
        id="section1"
      >
        <div
          class="e-section-header"
        >
          <h2
            class="e-section-title"
          >
            Section 1
          </h2>
        </div>
        <div
          class="e-grid-row e-grid-row-4 e-section-body"
          style="grid-template-columns: repeat(4, 1fr); padding: 0px 0px 0px 0px;"
        />
      </section>
    </div>
    <div
      class="e-detail-panel-tab-container"
      data-testid="e-detail-panel-tabs"
    >
      <div
        class="e-xtrem-tabs"
      >
        <div
          class="e-xtrem-tab-container"
          role="tablist"
        >
          <button
            aria-selected="true"
            class="e-xtrem-tab-item e-xtrem-tab-item-active"
            data-pendoid="sectionTab-section2"
            data-testid="e-xtrem-tab-section2 e-xtrem-tab-bind-section2"
            role="tab"
            tabindex="0"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 2
            </span>
          </button>
          <button
            aria-selected="false"
            class="e-xtrem-tab-item"
            data-pendoid="sectionTab-section3"
            data-testid="e-xtrem-tab-section3 e-xtrem-tab-bind-section3"
            role="tab"
            tabindex="-1"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 3
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="e-detail-panel-body"
      data-testid="e-detail-panel-body"
    >
      <div
        class="e-detail-panel-sections"
        data-testid="e-detail-panel-sections"
      >
        <section
          class="e-section e-section-context-detail-panel"
          data-testid="e-section-field e-field-label-section2 e-field-bind-section2"
          id="section2"
        >
          <div
            class="e-section-header"
          >
            <h2
              class="e-section-title"
            >
              Section 2
            </h2>
          </div>
          <div
            class="e-grid-row e-grid-row-4 e-section-body"
            style="grid-template-columns: repeat(4, 1fr); padding: 0px 0px 0px 0px;"
          />
        </section>
      </div>
    </div>
    <div
      id="mockBusinessActions"
    >
      <div
        id="mockDivBusinessAction"
      />
      <div
        id="mockDivBusinessAction"
      />
      <div
        id="mockDivBusinessAction"
      />
      <div
        id="mockDivBusinessAction"
      />
      <div
        id="mockDivContextType"
      >
        detail-panel
      </div>
      <div
        id="mockDivDefaultButtonType"
      >
        primary
      </div>
    </div>
  </aside>
</div>
`;

exports[`Detail panel component should render without a header section 1`] = `
.c2 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c2::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: transparent;
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0 .c1 {
  color: var(--colorsActionMajor500);
}

.c0:hover {
  background: var(--colorsActionMajor600);
  color: var(--colorsActionMajorYang100);
}

.c0:hover .c1 {
  color: var(--colorsActionMajorYang100);
}

.c0 .c1 {
  margin-bottom: 0px;
  margin-left: 0px;
  margin-right: var(--spacing100);
  height: 20px;
  width: 20px;
}

.c0 .c1 svg {
  margin-top: 0;
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <aside
    class="e-detail-panel"
    data-testid="e-detail-panel"
  >
    <div
      class="e-detail-panel-close"
    >
      <button
        class="c0"
        data-component="button"
        data-testid="e-detail-panel-close"
        draggable="false"
        type="button"
      >
        <span
          aria-hidden="true"
          class="c1 c2"
          color="--colorsActionMajor500"
          data-component="icon"
          data-element="close"
          data-role="icon"
          font-size="small"
          type="close"
        />
        <span>
          <span
            class="c3"
            data-element="main-text"
          >
            <span
              class="e-screen-reader-only"
            >
              Close helper panel
            </span>
          </span>
        </span>
      </button>
    </div>
    <div
      class="e-detail-panel-tab-container"
      data-testid="e-detail-panel-tabs"
    >
      <div
        class="e-xtrem-tabs"
      >
        <div
          class="e-xtrem-tab-container"
          role="tablist"
        >
          <button
            aria-selected="true"
            class="e-xtrem-tab-item e-xtrem-tab-item-active"
            data-pendoid="sectionTab-section2"
            data-testid="e-xtrem-tab-section2 e-xtrem-tab-bind-section2"
            role="tab"
            tabindex="0"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 2
            </span>
          </button>
          <button
            aria-selected="false"
            class="e-xtrem-tab-item"
            data-pendoid="sectionTab-section3"
            data-testid="e-xtrem-tab-section3 e-xtrem-tab-bind-section3"
            role="tab"
            tabindex="-1"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 3
            </span>
          </button>
        </div>
      </div>
    </div>
    <div
      class="e-detail-panel-body"
      data-testid="e-detail-panel-body"
    >
      <div
        class="e-detail-panel-sections"
        data-testid="e-detail-panel-sections"
      >
        <section
          class="e-section e-section-context-detail-panel"
          data-testid="e-section-field e-field-label-section2 e-field-bind-section2"
          id="section2"
        >
          <div
            class="e-section-header"
          >
            <h2
              class="e-section-title"
            >
              Section 2
            </h2>
          </div>
          <div
            class="e-grid-row e-grid-row-4 e-section-body"
            style="grid-template-columns: repeat(4, 1fr); padding: 0px 0px 0px 0px;"
          />
        </section>
      </div>
    </div>
    <div
      id="mockBusinessActions"
    >
      <div
        id="mockDivBusinessAction"
      />
      <div
        id="mockDivBusinessAction"
      />
      <div
        id="mockDivBusinessAction"
      />
      <div
        id="mockDivBusinessAction"
      />
      <div
        id="mockDivContextType"
      >
        detail-panel
      </div>
      <div
        id="mockDivDefaultButtonType"
      >
        primary
      </div>
    </div>
  </aside>
</div>
`;
