import { createNoValueControlObject } from '../../../../__tests__/test-helpers';
import { DetailPanelControlObject } from '../../../control-objects';
import { ContainerKey } from '../../../types';

describe('Detail Panel Control Object', () => {
    const screenId = 'TestPage';
    let detailPanelControlObject: DetailPanelControlObject;

    beforeEach(() => {
        detailPanelControlObject = createNoValueControlObject<ContainerKey.DetailPanel>(
            ContainerKey.DetailPanel,
            screenId,
            DetailPanelControlObject,
            'test',
            {
                title: 'TEST_FIELD_TITLE',
            } as any,
            {
                sections: [{ $containerId: 'testSection' }],
            },
        );
    });

    describe('getting default values', () => {
        it('should get field title', () => {
            expect(detailPanelControlObject.title).toEqual(detailPanelControlObject.title);
        });
    });

    describe('setting and getting updated values', () => {
        it('should set the title', () => {
            const testFixture = 'Test Title';
            expect(detailPanelControlObject.title).not.toEqual(testFixture);
            detailPanelControlObject.title = testFixture;
            expect(detailPanelControlObject.title).toEqual(testFixture);
        });

        it('should set the active section', () => {
            const testFixture = 'testSection';
            expect(detailPanelControlObject.activeSection).not.toEqual(testFixture);
            detailPanelControlObject.activeSection = testFixture;
            expect(detailPanelControlObject.activeSection).toEqual(testFixture);
        });

        it('should throw an exception if the active section is set to an invalid one', () => {
            const testFixture = 'anInvalidSection';
            expect(() => {
                detailPanelControlObject.activeSection = testFixture;
            }).toThrow('"anInvalidSection" is not a valid helper panel section element id.');
        });

        it('should set whether it has a close icon', () => {
            const testFixture = true;
            expect(detailPanelControlObject.isCloseButtonHidden).not.toEqual(testFixture);
            detailPanelControlObject.isCloseButtonHidden = testFixture;
            expect(detailPanelControlObject.isCloseButtonHidden).toEqual(testFixture);
        });

        it('should set whether it is extendable', () => {
            const testFixture = true;
            expect(detailPanelControlObject.isExtendable).not.toEqual(testFixture);
            detailPanelControlObject.isExtendable = testFixture;
            expect(detailPanelControlObject.isExtendable).toEqual(testFixture);
        });
    });
});
