import {
    addBusinessActionToState,
    addSectionToState,
    applyActionMocks,
    getMockPageDefinition,
    getMockPageMetadata,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux';
import { actions } from '../../../../redux';
import type { PageAction } from '../../../..';
import type { PageArticleItem } from '../../../../service/layout-types';
import type { Page } from '../../../../service/page';
import type { PageDefinition } from '../../../../service/page-definition';
import type { DetailPanelProperties, SectionProperties } from '../../../control-objects';
import { DetailPanelControlObject, detailPanelId } from '../../../control-objects';
import ConnectedDetailPanel from '../detail-panel-component';
import type { ContainerProperties } from '../../../abstract-container';
import type { BusinessActionsProps } from '../../footer/business-actions';
import { ContextType } from '../../../../types';
import type { IDetailPanelControlObject } from '../detail-panel-control-object';
import { cleanup, fireEvent, render } from '@testing-library/react';

jest.mock(
    '../../footer/business-actions',
    () =>
        function MockBusinessActions(props: BusinessActionsProps) {
            return (
                <div id="mockBusinessActions">
                    {props.businessActions &&
                        props.businessActions.map(value => (
                            <div id="mockDivBusinessAction" key={`mockDivBusinessAction-${value.id}`} />
                        ))}
                    <div id="mockDivContextType" key="mockDivContextType">
                        {props.contextType}
                    </div>
                    <div id="mockDivDefaultButtonType" key="mockDivDefaultButtonType">
                        {props.defaultButtonType}
                    </div>
                </div>
            );
        },
);

describe('Detail panel component', () => {
    const screenId = 'TestPage';
    let state: XtremAppState;
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let mockFieldProperties: SectionProperties;
    let detailPanel: DetailPanelControlObject;
    let section1Item: PageArticleItem;
    let section2Item: PageArticleItem;
    let section3Item: PageArticleItem;
    let pageAction1: PageAction;
    let pageAction2: PageAction;
    let pageAction3: PageAction;
    let pageAction4: PageAction;

    const detailPanelControlObjectProps: Partial<IDetailPanelControlObject> = {
        screenId,
        elementId: detailPanelId,
        getUiComponentProperties: jest.fn(),
        setUiComponentProperties: jest.fn(),
        layout: {
            detailPanelHeaderLayout: {} as Partial<PageArticleItem>,
            detailPanelSectionsLayout: [] as Partial<PageArticleItem>[],
        },
    };

    beforeEach(() => {
        mockFieldProperties = {
            title: 'Test Section Title',
        };

        state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);

        section1Item = addSectionToState(
            state,
            screenId,
            'section1',
            { ...mockFieldProperties, title: 'Section 1' },
            [],
        );
        section2Item = addSectionToState(
            state,
            screenId,
            'section2',
            { ...mockFieldProperties, title: 'Section 2' },
            [],
        );
        section3Item = addSectionToState(
            state,
            screenId,
            'section3',
            { ...mockFieldProperties, title: 'Section 3' },
            [],
        );

        pageAction1 = addBusinessActionToState(state, screenId, 'saveAction1', {
            title: 'Save 1 Title',
        });
        pageAction2 = addBusinessActionToState(state, screenId, 'saveAction2', {
            title: 'Save 2 Title',
        });
        pageAction3 = addBusinessActionToState(state, screenId, 'saveAction3', {
            title: 'Save 3 Title',
        });
        pageAction4 = addBusinessActionToState(state, screenId, 'delete', {
            title: 'Delete Title',
        });

        detailPanel = new DetailPanelControlObject({
            ...detailPanelControlObjectProps,
            headerSection: section1Item,
            sections: [section2Item, section3Item],
            footerActions: [pageAction1, pageAction2, pageAction3, pageAction4],
            layout: {
                detailPanelHeaderLayout: {} as Partial<PageArticleItem>,
                detailPanelSectionsLayout: [] as Partial<PageArticleItem>[],
            },
        } as IDetailPanelControlObject);

        const pageDefinition = state.screenDefinitions[screenId] as PageDefinition;
        pageDefinition.page = {
            ...pageDefinition.page,
            getSerializedValues: jest.fn(),

            $detailPanel: detailPanel,
        } as Page;

        pageDefinition.metadata.uiComponentProperties[detailPanelId] = {
            isHidden: false,
            activeSection: 'section2',
        } as DetailPanelProperties;

        mockStore = getMockStore(state);
    });

    afterEach(() => {
        cleanup();
        jest.restoreAllMocks();
        applyActionMocks();
    });

    it('should render with default properties', () => {
        const { container } = render(
            <Provider store={mockStore}>
                <ConnectedDetailPanel screenId={screenId} detailPanel={detailPanel} />
            </Provider>,
        );
        expect(container).toMatchSnapshot();
        expect(container.querySelector('.e-detail-panel-close')).not.toBeNull();
        expect(container.querySelector('.e-detail-panel-header')).not.toBeNull();
        const divContextType = container.querySelector('#mockDivContextType');
        expect(divContextType).not.toBeNull();
        expect(divContextType).toHaveTextContent(ContextType.detailPanel);

        const divDefaultButtonType = container.querySelector('#mockDivDefaultButtonType');
        expect(divDefaultButtonType).not.toBeNull();
        expect(divDefaultButtonType).toHaveTextContent('primary');
    });

    it('should close when the cross is clicked', () => {
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedDetailPanel screenId={screenId} detailPanel={detailPanel} />
            </Provider>,
        );
        expect(actions.setFieldProperties).not.toHaveBeenCalled();
        const closeButton = queryByTestId('e-detail-panel-close')!;
        fireEvent.click(closeButton);
        expect(actions.setFieldProperties).toHaveBeenCalledWith('TestPage', '$detailPanel', {
            activeSection: 'section2',
            isHidden: true,
        });
    });

    it('should change the active when the a tab is clicked', () => {
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedDetailPanel screenId={screenId} detailPanel={detailPanel} />
            </Provider>,
        );
        expect(actions.setFieldProperties).not.toHaveBeenCalled();
        const tab = queryByTestId('e-xtrem-tab-section3', { exact: false })!;
        fireEvent.click(tab);
        expect(actions.setFieldProperties).toHaveBeenCalledWith('TestPage', '$detailPanel', {
            activeSection: 'section3',
            isHidden: false,
        });
    });

    it('should render without a header section', () => {
        detailPanel = new DetailPanelControlObject({
            ...detailPanelControlObjectProps,
            sections: [section2Item, section3Item],
            footerActions: [pageAction1, pageAction2, pageAction3, pageAction4],
            layout: {
                detailPanelHeaderLayout: {} as Partial<PageArticleItem>,
                detailPanelSectionsLayout: [] as Partial<PageArticleItem>[],
            },
        } as IDetailPanelControlObject);

        const pageDefinition = state.screenDefinitions[screenId] as PageDefinition;
        pageDefinition.page = {
            ...pageDefinition.page,
            getSerializedValues: jest.fn(),
            $detailPanel: detailPanel,
        } as Page;

        pageDefinition.metadata.uiComponentProperties[detailPanelId] = {
            isHidden: false,
            activeSection: 'section2',
        } as DetailPanelProperties;

        mockStore = getMockStore(state);

        const { container } = render(
            <Provider store={mockStore}>
                <ConnectedDetailPanel screenId={screenId} detailPanel={detailPanel} />
            </Provider>,
        );
        expect(container).toMatchSnapshot();
        const divContextType = container.querySelector('#mockDivContextType');
        expect(divContextType).not.toBeNull();
        expect(divContextType).toHaveTextContent(ContextType.detailPanel);

        const divDefaultButtonType = container.querySelector('#mockDivDefaultButtonType');
        expect(divDefaultButtonType).not.toBeNull();
        expect(divDefaultButtonType).toHaveTextContent('primary');
    });

    it('should not render when set to be hidden', () => {
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
            metadata: getMockPageMetadata(screenId, {
                uiComponentProperties: {
                    [detailPanelId]: {
                        isHidden: true,
                    } as ContainerProperties,
                },
            }),
        });

        mockStore = getMockStore(state);
        const { container } = render(
            <Provider store={mockStore}>
                <ConnectedDetailPanel screenId={screenId} detailPanel={detailPanel} />
            </Provider>,
        );
        expect(container).toMatchSnapshot();
    });

    it('should render without a closing cross icon', () => {
        detailPanel = new DetailPanelControlObject({
            ...detailPanelControlObjectProps,
            headerSection: section1Item,
            sections: [section2Item, section3Item],
            footerActions: [pageAction1, pageAction2, pageAction3, pageAction4],
            layout: {
                detailPanelHeaderLayout: {} as Partial<PageArticleItem>,
                detailPanelSectionsLayout: [] as Partial<PageArticleItem>[],
            },
        } as IDetailPanelControlObject);

        const pageDefinition = state.screenDefinitions[screenId] as PageDefinition;
        pageDefinition.page = {
            ...pageDefinition.page,
            getSerializedValues: jest.fn(),
            $detailPanel: detailPanel,
        } as Page;

        pageDefinition.metadata.uiComponentProperties[detailPanelId] = {
            isCloseButtonHidden: true,
            activeSection: 'section2',
        } as DetailPanelProperties;

        mockStore = getMockStore(state);

        const { container } = render(
            <Provider store={mockStore}>
                <ConnectedDetailPanel screenId={screenId} detailPanel={detailPanel} />
            </Provider>,
        );
        expect(container.querySelector('.e-detail-panel-close')).toBeNull();
        expect(container.querySelector('.e-detail-panel-header')).not.toBeNull();
    });

    it('should render without a closing cross icon and a header section', () => {
        detailPanel = new DetailPanelControlObject({
            ...detailPanelControlObjectProps,
            sections: [section2Item, section3Item],
            footerActions: [pageAction1, pageAction2, pageAction3, pageAction4],
            layout: {
                detailPanelHeaderLayout: {} as Partial<PageArticleItem>,
                detailPanelSectionsLayout: [] as Partial<PageArticleItem>[],
            },
        } as IDetailPanelControlObject);

        const pageDefinition = state.screenDefinitions[screenId] as PageDefinition;
        pageDefinition.page = {
            ...pageDefinition.page,
            getSerializedValues: jest.fn(),
            $detailPanel: detailPanel,
        } as Page;

        pageDefinition.metadata.uiComponentProperties[detailPanelId] = {
            isCloseButtonHidden: true,
            activeSection: 'section2',
        } as DetailPanelProperties;

        mockStore = getMockStore(state);

        const { container } = render(
            <Provider store={mockStore}>
                <ConnectedDetailPanel screenId={screenId} detailPanel={detailPanel} />
            </Provider>,
        );
        expect(container.querySelector('.e-detail-panel-close')).toBeNull();
        expect(container.querySelector('.e-detail-panel-header')).toBeNull();
    });

    describe('footer', () => {
        it('should render footer actions', () => {
            const { container } = render(
                <Provider store={mockStore}>
                    <ConnectedDetailPanel screenId={screenId} detailPanel={detailPanel} />
                </Provider>,
            );
            expect(container.querySelectorAll('#mockBusinessActions')).toHaveLength(1);
            expect(container.querySelectorAll('#mockDivBusinessAction')).toHaveLength(4);
        });

        it('should render no footer actions', () => {
            detailPanel = new DetailPanelControlObject({
                ...detailPanelControlObjectProps,
                headerSection: section1Item,
                sections: [section2Item, section3Item],
            } as IDetailPanelControlObject);
            const { container } = render(
                <Provider store={mockStore}>
                    <ConnectedDetailPanel screenId={screenId} detailPanel={detailPanel} />
                </Provider>,
            );
            expect(container.querySelectorAll('#mockBusinessActions')).toHaveLength(1);
            expect(container.querySelectorAll('#mockBusinessAction')).toHaveLength(0);
        });
    });
});
