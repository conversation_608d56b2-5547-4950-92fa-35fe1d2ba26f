PATH: XTREEM/Page/Detail+Panel

## Introduction

Detail panel component is a container which is declared inside the page decorator that a provides detailed information of selected items, being displayed on the right side of the page. Detail panel's basic structure consists of a header, a list of sections and, in the bottom, a set of actions also known as footer actions.

## Example:

```ts
@ui.decorators.page<DetailPanelPage>({
    detailPanel() {
        return {
            activeSection: 'detailPanelBodySection1',
            footerActions: [this.action1, this.action2],
            header: this.detailPanelHeaderSection,
            sections: [this.detailPanelBodySection1, this.detailPanelBodySection2],
            isCloseButtonHidden: false,
        };
    },
})
```

### Display decorator properties:

-   **activeSection**: The element ID of the detail panel body section.
-   **footerActions**: business actions that are rendered in the detail panel's footer. Please refer to the documentation of the page actions for more information about it.
-   **header**: Section that is placed on top of the detail panel body.
-   **isExtendable**: Whether the detail panel can be extracted into a separate window or not. Defaults to false.
-   **isDisabled**: Whether the HTML element is disabled or not. It can also be defined as callback function that returns a boolean. Defaults to false.
-   **isHidden**: Whether the HTML element is hidden or not. Defaults to false.
-   **isTitleHidden**: Whether the element title is hidden or not. Defaults to false.
-   **isCloseButtonHidden**: Whether the element cross close icon should be hidden. If it is hidden, the user cannot manually close the detail panel. Defaults to false.
-   **sections**: list of page sections that will be rendered in the detail panel's body.
-   **title**: The title of the HTML element. The title can be provided as a string or callback function returning a string. It is automatically picked up by the i18n engine and externalized for translation.

### Observations:
It's strongly recommended to always use a title for the header section, otherwise the header content may overlap with the close icon located at the top right of the header panel.

## Sandbox

Check out element on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/DetailPanel).
