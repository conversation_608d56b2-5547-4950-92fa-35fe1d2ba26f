## Documentation - DetailPanel
| name                | optional | description                                                                                                                                                                                                                                    | type                                                                                                   | default                                                                                  |
| ------------------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------- |
| header              | false    | Section]{@link SectionControlObject} contained in the helper panel header                                                                                                                                                                      | <pre lang="javascript">SectionControlObject<CT></pre>                                                  |                                                                                          |
| sections            | false    | Sections]{@link SectionControlObject} contained in the helper panel body                                                                                                                                                                       | <pre lang="javascript">SectionControlObject<CT>[]</pre>                                                |                                                                                          |
| access              | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">AccessConfiguration</pre>                                                       | <pre lang="javascript">{&nbsp;node:&nbsp;'<node>',&nbsp;bind:&nbsp;'<bind>'&nbsp;}</pre> |
| activeSection       | true     | Which of the helper panel sections is being displayed (element ID of the section                                                                                                                                                               | <pre lang="javascript">string</pre>                                                                    | <pre lang="javascript">''</pre>                                                          |
| footerActions       | true     | Page Actions]{@link PageAction} rendered in the helper panel footer                                                                                                                                                                            | <pre lang="javascript">PageActionControlObject<CT>[]</pre>                                             | <pre lang="javascript">[]</pre>                                                          |
| isCloseButtonHidden | true     | Whether the a cross icon is displayed that enables the user from closing the dialog                                                                                                                                                            | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                       |
| isDisabled          | true     | Whether the HTML element is disabled or not. Defaults to false The difference with readOnly is that disabled suggests that the field is not editable for some validation reason (e.g. a button which can't be clicked due to validation errors | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre> | <pre lang="javascript">false</pre>                                                       |
| isExtendable        | true     | Whether the helper panel can be extracted into a separate window or not. Defaults to false                                                                                                                                                     | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                       |
| isHidden            | true     | Whether the HTML element is hidden or not. Defaults to false                                                                                                                                                                                   | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre> | <pre lang="javascript">false</pre>                                                       |
| isHiddenDesktop     | true     | Whether the element is hidden or not in desktop devices. Defaults to false                                                                                                                                                                     | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                       |
| isHiddenMobile      | true     | Whether the element is hidden or not in mobile devices. Defaults to false                                                                                                                                                                      | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                       |
| isTitleHidden       | true     | Whether the element title is hidden or not. Defaults to false                                                                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                       |
| isTransient         | true     | Whether the value is bound to a GraphQL node (transient = false) or not (transient = true). Defaults to false                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                       |
| title               | true     | The title of the HTML element                                                                                                                                                                                                                  | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;any,&nbsp;Dict<any>></pre>  | <pre lang="javascript">''</pre>                                                          |
| validation          | true     | Container validation rule                                                                                                                                                                                                                      | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                         | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                              |