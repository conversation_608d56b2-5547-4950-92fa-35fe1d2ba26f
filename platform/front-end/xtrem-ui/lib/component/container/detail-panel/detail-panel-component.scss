@import '../../../render/style/variables.scss';
@import '../../../render/style/mixins.scss';

@keyframes e-detail-panel-mobile-content-move-in {
    0% {
        left: 100%;
    }

    100% {
        left: 0;
    }
}

.e-detail-panel {
    margin: 0 0 0 24px;
    background: var(--colorsYang100);
    display: flex;
    overflow-y: auto;
    overflow-x: hidden;
    flex-direction: column;
    position: relative;
    width: 320px;

    @include medium_and_below {
        width: 256px;
    }

    @include extra_small {
        background: var(--colorsYang100);
        display: flex;
        overflow-y: auto;
        position: fixed;
        margin: 0;
        top: 48px;
        left: 0;
        width: 100vw;
        z-index: 12;
        height: calc(100dvh - 48px);
        animation-name: e-detail-panel-mobile-content-move-in;
        animation-iteration-count: 1;
        animation-timing-function: ease-in;
        animation-duration: 0.3s;
        box-shadow: var(--boxShadow200);
    }

    .e-detail-panel-body {
        flex: 1;
        display: flex;
        overflow-y: auto;
        flex-direction: column;
        position: relative;
    }

    .e-detail-panel-business-actions {
        box-shadow: var(--boxShadow200);
        display: flex;
        padding: 4px 8px;

        .e-business-action {
            flex: 1;
            margin: 8px;
            display: flex;

            >button {
                margin: 0;
                flex: 1;
                padding: 0 8px;
            }
        }

        .e-detail-panel-business-actions-more {
            >div {
                margin: 16px 0;

                &.e-action-popover-mobile {
                    margin: 8px 0;
                }

                >div {
                    bottom: 50px;
                }
            }
        }

        .e-action-popover-mobile>button {
            margin: 0;
        }
    }

    .e-card {
        border: unset;
        outline: unset;
        padding: 0 0 0 16px;
        color: var(--colorsYin090);

        .e-card-content {
            padding: 16px 0;
            color: var(--colorsYin090);
        }

        .e-card-image {
            padding-left: 0;
        }

        &.e-card-has-image .e-card-content {
            padding: 12px 8px 12px 15px;
        }

        .e-portrait {
            padding: 16px 0;
        }
    }

    .e-table-field-mobile .e-card {
        padding: 0;
    }


    .e-detail-panel-sections {
        flex: 1;

        .e-section .e-section-header {
            display: none;
        }


        // Additional padding if no title is defined
        .e-block-body:only-child {
            padding-top: 16px;
        }
    }

    .e-detail-panel-close {
        position: absolute;
        top: 4px;
        right: 4px;
        margin: 0;
        padding: 8px 8px;
        z-index: 1;

        span {
            margin-right: 0;
            color: var(--colorsUtilityMajor300);
        }
    }

    .e-detail-panel-header .e-section-header {
        border-bottom: unset;
    }

    .e-detail-panel-tab-container {
        margin-top: 0;
        box-sizing: content-box;

        [role="tablist"] {
            width: 100%;
            margin-bottom: 0;
            overflow-x: auto;

            button {
                flex: 1;
                text-align: center;
                font-size: 14px;
                font-family: $fontAdelle;

                div {
                    padding: 0;
                }
            }
        }
    }
}