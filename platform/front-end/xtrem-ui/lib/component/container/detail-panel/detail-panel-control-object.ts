/**
 * @packageDocumentation
 * @module root
 * */

import type { PageArticleItem } from '../../../service/layout-types';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { getUiComponentProperties, setUiComponentProperties } from '../../../service/transactions-service';
import type { ScreenExtension } from '../../../types';
import type { ContainerProperties } from '../../abstract-container';
import { AbstractContainer } from '../../abstract-container';
import type { PageActionControlObject } from '../../control-objects';
import type { ContainerComponentProps, ContainerControlObjectConstructorProps } from '../../types';
import { ContainerKey } from '../../types';

export const detailPanelId = '$detailPanel';

export interface DetailPanelProperties<CT extends ScreenBase = ScreenBase> extends ContainerProperties<CT> {
    /** Whether the helper panel can be extracted into a separate window or not. Defaults to false */
    isExtendable?: boolean;
    /** Which of the helper panel sections is being displayed (element ID of the section) */
    activeSection?: string;
    /** Whether the a cross icon is displayed that enables the user from closing the dialog */
    isCloseButtonHidden?: boolean;
}

export interface IDetailPanelControlObject<CT extends ScreenBase = ScreenBase>
    extends ContainerControlObjectConstructorProps<ContainerKey.DetailPanel> {
    headerSection: Partial<PageArticleItem>;
    sections: Partial<PageArticleItem>[];
    footerActions?: PageActionControlObject<CT>[];
}

/**
 * [Container]{@link AbstractContainer} that can be used to display additional information outside of the page layout
 */
export class DetailPanelControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends AbstractContainer<
    CT,
    ContainerKey.DetailPanel,
    ContainerComponentProps<ContainerKey.DetailPanel>
> {
    private readonly _headerSection: Partial<PageArticleItem>;

    private readonly _sections: Partial<PageArticleItem>[];

    private readonly _footerActions?: PageActionControlObject<CT>[];

    static readonly defaultUiProperties: Partial<DetailPanelProperties> = {
        ...AbstractContainer.defaultUiProperties,
        isExtendable: false,
    };

    constructor(properties: IDetailPanelControlObject<CT>) {
        // TODO: implement validation of the Detail Panel container
        super(
            properties.screenId,
            properties.elementId,
            properties.getUiComponentProperties || getUiComponentProperties,
            properties.setUiComponentProperties || setUiComponentProperties,
            ContainerKey.DetailPanel,
            properties.getValidationState || (async (): Promise<boolean> => true),
            properties.layout,
        );
        this._headerSection = Object.seal(properties.headerSection);
        this._sections = Object.seal(properties.sections);
        this._footerActions = Object.seal(properties.footerActions);
    }

    /** Which of the helper panel sections is being displayed */
    get activeSection(): string | undefined {
        return this.getUiComponentProperty('activeSection');
    }

    /** Which of the helper panel sections is being displayed */
    set activeSection(newValue: string | undefined) {
        if (newValue && !this.sections.find(s => s.$containerId === newValue)) {
            throw new Error(`"${newValue}" is not a valid helper panel section element id.`);
        }

        this.setUiComponentProperties('activeSection', newValue as any);
    }

    /** Whether the a cross icon is displayed that enables the user from closing the dialog */
    get isCloseButtonHidden(): boolean | undefined {
        return this.getUiComponentProperty('isCloseButtonHidden') || false;
    }

    /** Whether the a cross icon is displayed that enables the user from closing the dialog */
    set isCloseButtonHidden(newValue: boolean | undefined) {
        this.setUiComponentProperties('isCloseButtonHidden', newValue);
    }

    /**
     * Whether the helper panel can be extended into a separate window (extendable = true)
     * or not (extendable = false)
     */
    get isExtendable(): boolean {
        return !!this.getUiComponentProperty('isExtendable');
    }

    /**
     * Whether the helper panel can be extended into a separate window (extendable = true)
     * or not (extendable = false)
     */
    set isExtendable(newValue: boolean) {
        this.setUiComponentProperties('isExtendable', newValue);
    }

    /** [Section]{@link SectionControlObject} contained in the helper panel header */
    get headerSection(): Partial<PageArticleItem> {
        return this._headerSection;
    }

    /** [Sections]{@link SectionControlObject} contained in the helper panel body */
    get sections(): Partial<PageArticleItem>[] {
        return this._sections || [];
    }

    /** [Page Actions]{@link PageAction} rendered in the helper panel footer */
    get footerActions(): PageActionControlObject<CT>[] | undefined {
        return this._footerActions;
    }

    /**
     * Triggers the field validation rules. Since the validation rules might be asynchronous,
     * this method returns a promise that must be awaited to get the validation result. Compared to the `validate` method
     * it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
     */
    async validateWithDetails(
        partition: true,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
    async validateWithDetails(partition: false): Promise<ValidationResult[]>;
    async validateWithDetails(): Promise<ValidationResult[]>;
    async validateWithDetails(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _partition = false,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] } | ValidationResult[]> {
        // Todo: implement validation of the Detail Panel container
        return [];
    }
}
