import type { ScreenBase } from '../../../service/screen-base';
import type { PageActionControlObject, SectionControlObject } from '../../control-objects';
import type { DetailPanelProperties } from './detail-panel-control-object';

export interface DetailPanelDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<DetailPanelProperties<CT>, '_controlObjectType'> {
    /** [Section]{@link SectionControlObject} contained in the helper panel header */
    header: SectionControlObject<CT>;
    /** [Sections]{@link SectionControlObject} contained in the helper panel body */
    sections: SectionControlObject<CT>[];
    /** [Page Actions]{@link PageAction} rendered in the helper panel footer */
    footerActions?: PageActionControlObject<CT>[];
}
