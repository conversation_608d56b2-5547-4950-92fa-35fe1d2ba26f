import type { Dict } from '@sage/xtrem-shared';
import type { PageArticleItem } from '../../../service/layout-types';
import type { DataTypeDetails, FormattedNodeDetails } from '../../../service/metadata-types';
import type { DetailPanelDecoratorProperties } from '../../decorator-properties';
import type { ContainerKey, DecoratorTarget, MetadataProps } from '../../types';
import { AbstractLayoutBuilder } from '../layouts';

export interface IDetailPanelLayout {
    detailPanelHeader: DetailPanelDecoratorProperties['header'];
    detailPanelSections: DetailPanelDecoratorProperties['sections'];
}

export class DetailPanelLayout extends AbstractLayoutBuilder<ContainerKey.DetailPanel> implements IDetailPanelLayout {
    public detailPanelHeader: IDetailPanelLayout['detailPanelHeader'];

    public detailPanelSections: IDetailPanelLayout['detailPanelSections'];

    public detailPanelSectionsLayout: Partial<PageArticleItem>[] = [];

    public detailPanelHeaderLayout: Partial<PageArticleItem> = {};

    constructor(
        public target: DecoratorTarget<ContainerKey.DetailPanel>,
        public elementId: string,
        nodeTypes: Dict<FormattedNodeDetails>,
        dataTypes: Dict<DataTypeDetails>,
        props: IDetailPanelLayout & MetadataProps<ContainerKey.DetailPanel>,
    ) {
        super(target, elementId, nodeTypes, dataTypes, props);

        this.detailPanelHeader = props.detailPanelHeader;
        this.detailPanelSections = props.detailPanelSections;
    }

    buildLayout = (): this => {
        const headerElementId = this.detailPanelHeader.id;
        const bodySectionIds = this.detailPanelSections.map(s => s.id);

        this.metadata.pageMetadata.layout.$items = this.metadata.pageMetadata.layout.$items.filter(s => {
            if (headerElementId === s.$containerId) {
                this.detailPanelHeaderLayout = s;
                return false;
            }
            if (bodySectionIds.indexOf(s.$containerId!) !== -1) {
                this.detailPanelSectionsLayout.push(s);
                return false;
            }
            return true;
        });

        this.layout = {
            detailPanelHeaderLayout: this.detailPanelHeaderLayout,
            detailPanelSectionsLayout: this.detailPanelSectionsLayout,
        };

        return this;
    };
}
