import type { Dict } from '@sage/xtrem-shared';
import Button from 'carbon-react/esm/components/button';
import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import { RenderingRouter } from '../../../render/rendering-router';
import type { PageArticleItem } from '../../../service/layout-types';
import { ContextType } from '../../../types';
import { xtremConsole } from '../../../utils/console';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { COLUMN_COUNT_HELPER_PANEL } from '../../../utils/responsive-utils';
import type { DetailPanelControlObject, DetailPanelProperties } from '../../control-objects';
import { detailPanelId } from '../../control-objects';
import { getFieldTitle } from '../../field/carbon-helpers';
import { XtremTabs } from '../../ui/tabs/xtrem-tabs';
import type { SectionProperties } from '../container-properties';
import BusinessActions from '../footer/business-actions';

export interface DetailPanelExternalProps {
    detailPanel: DetailPanelControlObject;
    screenId: string;
}

export interface DetailPanelProps extends DetailPanelExternalProps {
    // The following property is necessary to trigger a render cycle after disabling actions
    enabledFooterActions: number;
    detailPanelProperties: DetailPanelProperties;
    setFieldProperties: (bind: string, properties: DetailPanelProperties) => void;
    // The following property is necessary to trigger a render cycle after hiding actions
    visibleFooterActions: number;
    sectionProperties: Dict<SectionProperties>;
}

export class DetailPanel extends React.Component<DetailPanelProps> {
    onTabSelect = (selectedId: any): void => {
        this.props.setFieldProperties(detailPanelId, {
            ...this.props.detailPanelProperties,
            activeSection: selectedId,
        });
    };

    onClose = (): void => {
        this.props.setFieldProperties(detailPanelId, {
            ...this.props.detailPanelProperties,
            isHidden: true,
        });
    };

    render(): React.ReactNode {
        const { detailPanelProperties, detailPanel, sectionProperties, screenId } = this.props;
        if (
            resolveByValue({
                propertyValue: detailPanelProperties.isHidden,
                screenId,
                skipHexFormat: true,
                rowValue: null, // This is not a field context, so no row value can be present
            })
        ) {
            return null;
        }
        // Filter out hidden sections, they are not rendered on the tab section selector
        const visibleSections = detailPanel.sections.filter(s =>
            resolveByValue({
                propertyValue: !sectionProperties[s.$containerId!].isHidden,
                screenId,
                skipHexFormat: true,
                rowValue: null, // This is not a field context, so no row value can be present
            }),
        );
        const headerSection = detailPanel.headerSection;
        const shouldRenderHeaderSection =
            headerSection &&
            sectionProperties[headerSection.$containerId!] &&
            !resolveByValue({
                propertyValue: sectionProperties[headerSection.$containerId!].isHidden,
                screenId,
                skipHexFormat: true,
                rowValue: null, // This is not a field context, so no row value can be present
            });
        if (shouldRenderHeaderSection && sectionProperties[headerSection.$containerId!].title === undefined) {
            xtremConsole.warn(`Detail panel header section ${headerSection.$containerId} has no title`);
        }

        const activeSectionElementId = detailPanelProperties.activeSection || visibleSections[0]?.$containerId || null;
        const activeSection = activeSectionElementId
            ? visibleSections.find(c => c.$containerId === activeSectionElementId)
            : null;

        return (
            <aside className="e-detail-panel" data-testid="e-detail-panel">
                {!detailPanelProperties.isCloseButtonHidden && (
                    <div className="e-detail-panel-close">
                        <Button
                            data-testid="e-detail-panel-close"
                            iconType="close"
                            onClick={this.onClose}
                            buttonType="tertiary"
                        >
                            <span className="e-screen-reader-only">Close helper panel</span>
                        </Button>
                    </div>
                )}
                {shouldRenderHeaderSection && (
                    <div className="e-detail-panel-header" data-testid="e-detail-panel-header">
                        <RenderingRouter
                            screenId={screenId}
                            item={headerSection}
                            availableColumns={COLUMN_COUNT_HELPER_PANEL}
                            contextType={ContextType.detailPanel}
                        />
                    </div>
                )}
                {visibleSections.length > 1 && (
                    <div className="e-detail-panel-tab-container" data-testid="e-detail-panel-tabs">
                        <XtremTabs
                            onTabChange={this.onTabSelect}
                            selectedTabId={activeSectionElementId!}
                            tabs={visibleSections.map(s => ({
                                id: s.$containerId!,
                                title: getFieldTitle(screenId, this.props.sectionProperties[s.$containerId!], null),
                                indicatorContent: sectionProperties[s.$containerId!].indicatorContent,
                            }))}
                        />
                    </div>
                )}
                <div className="e-detail-panel-body" data-testid="e-detail-panel-body">
                    <div className="e-detail-panel-sections" data-testid="e-detail-panel-sections">
                        {activeSection && (
                            <RenderingRouter
                                screenId={screenId}
                                contextType={ContextType.detailPanel}
                                availableColumns={COLUMN_COUNT_HELPER_PANEL}
                                item={activeSection}
                            />
                        )}
                    </div>
                </div>
                <BusinessActions
                    businessActions={detailPanel.footerActions}
                    defaultButtonType="primary"
                    contextType={ContextType.detailPanel}
                    key="e-detail-panel-business-actions"
                    screenId={screenId}
                />
            </aside>
        );
        /* eslint-enable react/no-array-index-key */
    }
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: DetailPanelExternalProps): DetailPanelProps => {
    const footerActions = props.detailPanel.footerActions || [];
    const sections = props.detailPanel.sections || [];
    const visibleFooterActions = footerActions.filter(action => !action.isHidden).length;
    const enabledFooterActions = footerActions.filter(action => !action.isDisabled).length;
    const detailPanelProperties = state.screenDefinitions[props.screenId].metadata.uiComponentProperties[
        detailPanelId
    ] as DetailPanelProperties;

    const sectionProperties = [...sections, props.detailPanel.headerSection].reduce(
        (prevValue: Dict<SectionProperties>, currentValue: Partial<PageArticleItem>) => {
            if (currentValue) {
                prevValue[currentValue.$containerId!] =
                    state.screenDefinitions[props.screenId].metadata.uiComponentProperties[currentValue.$containerId!];
            }
            return prevValue;
        },
        {} as Dict<SectionProperties>,
    );

    return {
        ...props,
        sectionProperties,
        enabledFooterActions,
        detailPanelProperties,
        visibleFooterActions,
        setFieldProperties: xtremRedux.actions.actionStub,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: DetailPanelExternalProps,
): Partial<DetailPanelProps> => ({
    setFieldProperties: (elementId: string, value: DetailPanelProperties): void => {
        dispatch(xtremRedux.actions.setFieldProperties(props.screenId, elementId, value));
    },
});

export default connect(mapStateToProps, mapDispatchToProps)(DetailPanel);
