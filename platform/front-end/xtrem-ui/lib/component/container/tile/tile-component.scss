.e-tile-parent {
    background-color: var(--colorsYang100);
    padding: 0;
    display: inline-block;
    border-radius: var(--borderRadius100);
    margin: 4px 16px;

    .e-tile-field {
        border-radius: 0;
        background: transparent;
        border: 0;
        text-align: left;
        width: 100%;
        display: block;

        .e-tile-field-title {
            line-height: 21px;
            font-size: var(--fontSizes100);
            color: var(--colorsUtilityYin055);
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            display: block;

            @include extra_small {
                text-align: center;
            }
        }

        .e-tile-field-value {
            font-weight: var(--fontWeights700);
            font-family: var(--fontFamiliesDefault);
            font-size: var(--fontSizes400);
            line-height: 25px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;

            @include extra_small {
                text-align: center;
            }

            .e-field-value-prefix {
                margin-right: 2px;
            }

            .e-field-value-postfix {
                margin-left: 2px;
            }
        }

        &.e-disabled {
            color: var(--colorsYin030);
        }

        &:focus {
            outline: none;
            box-shadow: inset 0px 0px 0px 2px var(--colorsSemanticFocus500);
        }
    }

    button.e-tile-field {
        cursor: pointer;
    }

    @include extra_small {
        margin: 0;
        padding: 0;
    }

    .e-grid-column {
        padding: 0 24px;
        margin: 16px 0;
        position: relative;

        ::after {
            content: '';
            position: absolute;
            top: 0;
            right: -12px;
            bottom: 0;
            width: 1px;
            background-color: var(--colorsActionMinor200);
        }

        @include extra_small {
            margin: 0;
            margin-top: 8px;
            margin-bottom: 8px;
        }

        &.e-grid-column-last-in-row {
            border-right: 0;

            ::after {
                display: none;
            }
        }
    }

    .e-tile-field-right-aligned .e-tile-field-title,
    .e-tile-field-right-aligned .e-tile-field-value {
        text-align: right;

        @include extra_small {
            text-align: center;
        }

    }
}

.e-header-section .e-tile-parent {
    margin: 0 16px;
}