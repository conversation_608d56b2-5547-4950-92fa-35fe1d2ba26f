PATH: XTREEM/Client+Framework/Tile

A container element that holds a number of fields which are rendered as read-only indicator values. Its parent must be a section. It can only be used with a limited number of fields:
- Aggregate
- Count
- Date
- Dropdown list
- Label
- Numeric
- Reference
- Select
- Text

In case of a tile, these fields are not rendered as they normally do. Their display value rendered on a read-only tile along with their main display properties such as `title`, `helperText`. The value is aligned to the right for numeric field types which are aggregate, count and numeric.

## Example

```ts
@ui.decorators.tile<SamplePage>({
    title: 'Sample Tile',
    parent() {
        return this.anySection;
    },
    isTitleHidden: true,
    isDisabled: false,
    width: 'medium',
})
sampleTile: ui.containers.Tile;
```

### Decorator properties:

-   **title**: The title that is displayed on the top of the page. The title can be provided as a string, or a callback function returning a string. It is automatically picked up by the i18n engine and externalized for translation.
-   **isTitleHidden**: Subtitle that is displayed on the top of the normal title pages. When the page is rendered into a dialog, the subtitle is displayed under the the title.
-   **isDisabled**: Determines whether the fields in the block are disabled or not. If set to true, all children field will be disabled.
-   **width**: Specifies the field's width. The width can be defined by using field size categories which are remapped to actually width values by the framework depending on the screen size.
