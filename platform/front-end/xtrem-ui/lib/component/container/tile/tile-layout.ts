import { AbstractLayoutBuilder } from '../../abstract-layout-builder';
import type { Container<PERSON>ey } from '../../types';

export class TileLayout extends AbstractLayoutBuilder<ContainerKey.Tile> {
    buildLayout = (): this => {
        this.layout = this.buildContainerLayout(
            this.elementId,
            'tile',
            this.metadata.properties.isHiddenMobile,
            this.metadata.properties.isHiddenDesktop,
            this.metadata.properties.width,
        );
        this.metadata.pageMetadata.layoutBlocks[this.elementId] = this.layout;
        return this;
    };
}
