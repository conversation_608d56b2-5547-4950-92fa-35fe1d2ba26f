import type { Dict } from '@sage/xtrem-shared';
import * as React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import type { PageArticleItem } from '../../../service/layout-types';
import { ContextType } from '../../../types';
import { getElementAccessStatus } from '../../../utils/access-utils';
import { getDataTestIdAttribute, isHidden } from '../../../utils/dom';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { calculateContainerWidth, calculateFieldWidth, getGutterSize } from '../../../utils/responsive-utils';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import type { PageProperties } from '../../control-objects';
import { getFieldTitle } from '../../field/carbon-helpers';
import type { ComponentKey, FieldWidth } from '../../types';
import { GridColumn, GridRow } from '@sage/xtrem-ui-components';
import { TileGridManager } from './tile-grid-manager';
import { ConnectedTileField } from './tile-field';
import type { GridColumnDefinition, TileComponentExternalProps, TileComponentProps } from './tile-types';

export function TileComponent(props: TileComponentProps): React.ReactElement {
    const computedWidth = calculateContainerWidth(
        props.browser.is,
        props.availableColumns,
        props.fieldProperties.width,
    );

    const gridGutter = getGutterSize(props.browser.is);
    const gridVerticalMargin = props.contextType === ContextType.page ? 16 : 0;

    const isTitleHidden = resolveByValue({
        screenId: props.screenId,
        fieldValue: null,
        rowValue: null,
        propertyValue: props.fieldProperties.isTitleHidden,
        skipHexFormat: true,
    });

    const isTileHidden =
        isHidden(props.item, props.browser) ||
        props.accessRule === 'unavailable' ||
        resolveByValue({
            screenId: props.screenId,
            propertyValue: props.fieldProperties.isHidden,
            skipHexFormat: true,
            rowValue: null,
        });

    const isDisabled =
        props.isParentDisabled ||
        resolveByValue<boolean>({
            screenId: props.screenId,
            propertyValue: props.fieldProperties.isDisabled,
            skipHexFormat: true,
            rowValue: null,
        });

    const title = getFieldTitle(props.screenId, props.fieldProperties, null);

    const classes = ['e-container-parent', 'e-tile-parent'];
    if (isTileHidden) {
        classes.push('e-hidden');
    }

    if (props.contextType) {
        classes.push(`e-tile-context-${props.contextType}`);
    }

    return (
        <GridColumn className={classes.join(' ')} columnSpan={computedWidth}>
            <div className="e-tile" data-testid={getDataTestIdAttribute('tile', title, props.item.$containerId)}>
                {!isTitleHidden && title && (
                    <div className="e-tile-header">
                        <h3 className="e-tile-title" data-testid="e-tile-title">
                            {title}
                        </h3>
                    </div>
                )}
                <GridRow columns={computedWidth} gutter={gridGutter} margin={0} verticalMargin={gridVerticalMargin}>
                    {props.item.$layout?.$items
                        .filter(i => !!i.$bind && props.columnInfo[i.$bind])
                        .map((i: PageArticleItem) => {
                            const columnInfo = props.columnInfo[i.$bind];
                            const gridColumnClasses = ['e-field-grid-column'];

                            if (columnInfo.isFirstInRow) {
                                gridColumnClasses.push('e-grid-column-first-in-row');
                            }

                            if (columnInfo.isLastInRow) {
                                gridColumnClasses.push('e-grid-column-last-in-row');
                            }

                            return (
                                <GridColumn
                                    key={i.$bind}
                                    className={gridColumnClasses.join(' ')}
                                    columnSpan={columnInfo.columnSpan}
                                    columnStart={columnInfo.columnStart}
                                >
                                    <ConnectedTileField
                                        elementId={i.$bind}
                                        screenId={props.screenId}
                                        availableColumns={computedWidth}
                                        item={i}
                                        isParentDisabled={isDisabled}
                                    />
                                </GridColumn>
                            );
                        })}
                </GridRow>
            </div>
        </GridColumn>
    );
}

TileComponent.displayName = 'TileComponent';

const mapStateToProps = (state: xtremRedux.XtremAppState, props: TileComponentExternalProps): TileComponentProps => {
    if (!props.item.$layout) {
        throw new Error(`No layout found for ${props.screenId} ${props.item.$containerId} tile.`);
    }
    const elementId = props.item.$containerId;
    if (!elementId) {
        throw new Error(
            `Cannot render tile container because layout definition does not have a container ID. Screen:${props.screenId}`,
        );
    }
    const pageDefinition = getPageDefinitionFromState(props.screenId);
    const pageProperties: PageProperties = pageDefinition.metadata.uiComponentProperties[props.screenId];
    const fieldProperties = pageDefinition.metadata.uiComponentProperties[elementId];
    const accessRule = getElementAccessStatus({
        accessBindings: pageDefinition.accessBindings || {},
        bind: elementId,
        elementProperties: fieldProperties,
        contextNode: pageProperties?.node,
        nodeTypes: state.nodeTypes,
        dataTypes: state.dataTypes,
    });

    const accessBindings = pageDefinition.accessBindings || {};
    const manager = new TileGridManager(props.availableColumns);

    // Precalculates all columns of the responsive grid to be able to anticipate the structure, such as which fields are going to be first or last in a row.
    props.item.$layout.$items.forEach(i => {
        const fieldElementId = String(i.$bind || i.$containerId);
        const elementProperties = pageDefinition.metadata.uiComponentProperties[fieldElementId];

        const fieldAccessRule = getElementAccessStatus({
            accessBindings,
            bind: fieldElementId,
            elementProperties,
            contextNode: pageProperties?.node,
            nodeTypes: state.nodeTypes,
            dataTypes: state.dataTypes,
        });

        const componentType = elementProperties._controlObjectType as ComponentKey;
        const columnSpan = calculateFieldWidth(
            state.browser.is,
            componentType,
            props.availableColumns,
            i.$isFullWidth,
            i.$columnWidth as FieldWidth,
        );

        const isColHidden =
            isHidden(i, state.browser) ||
            fieldAccessRule === 'unavailable' ||
            resolveByValue({
                propertyValue: elementProperties.isHidden,
                rowValue: null,
                screenId: props.screenId,
                fieldValue: null,
                skipHexFormat: true,
            });

        manager.appendItem({
            elementId: fieldElementId,
            columnSpan,
            isHidden: isColHidden,
        });
    });

    const columnInfo: Dict<GridColumnDefinition> = manager.getState();

    return {
        ...props,
        columnInfo,
        accessRule,
        fieldProperties,
        screenType: pageDefinition.type,
        browser: state.browser,
    };
};

export const ConnectedTileComponent = connect(mapStateToProps)(TileComponent);
