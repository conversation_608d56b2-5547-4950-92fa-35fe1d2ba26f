import type { Dict } from '@sage/xtrem-shared';
import type { GridColumnDefinition } from './tile-types';

export class TileGridManager {
    private readonly state: Array<Array<GridColumnDefinition>>;

    private readonly width: number;

    constructor(width: number) {
        this.state = [];
        this.width = width;
    }

    private getCurrentRow(): Array<GridColumnDefinition> {
        return this.state[this.state.length - 1];
    }

    private getRemainingColumns(): number {
        let result = this.width;

        this.getCurrentRow().forEach(col => {
            result -= col.columnSpan;
        });

        return result;
    }

    private isEmptyState(): boolean {
        return this.state.length === 0;
    }

    private wouldOverflow(item: GridColumnDefinition): boolean {
        return this.getRemainingColumns() < item.columnSpan;
    }

    public appendItem(item: GridColumnDefinition): void {
        if (item.isHidden) {
            return;
        }

        if (this.isEmptyState() || this.wouldOverflow(item)) {
            this.state.push([]);
        }

        const currentRow = this.getCurrentRow();

        if (currentRow.length === 0) {
            currentRow.push({
                ...item,
                columnStart: this.width - item.columnSpan + 1,
                isFirstInRow: true,
                isLastInRow: true,
            });
            return;
        }

        currentRow[0].columnStart = (currentRow[0].columnStart || 0) - item.columnSpan;
        currentRow[currentRow.length - 1].isLastInRow = false;
        currentRow.push({
            ...item,
            isFirstInRow: false,
            isLastInRow: true,
        });
    }

    public getState(): Dict<GridColumnDefinition> {
        const state: Dict<GridColumnDefinition> = {};

        this.state.forEach(row => {
            row.forEach(col => {
                state[col.elementId] = col;
            });
        });

        return state;
    }
}
