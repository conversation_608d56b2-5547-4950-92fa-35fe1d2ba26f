/**
 * @packageDocumentation
 * @module root
 * */

import type { Dict } from '@sage/xtrem-shared';
import { dispatchContainerValidation } from '../../../service/dispatch-service';
import type { PageArticleItem } from '../../../service/layout-types';
import type { DataTypeDetails, FormattedNodeDetails } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import { getPageMetadata } from '../../../service/page-metadata';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { getValidationState } from '../../../service/validation-service';
import type { ScreenExtension } from '../../../types';
import { getTargetPrototype } from '../../../utils/decorator-utils';
import { AbstractDecorator } from '../../abstract-decorator';
import { TileControlObject } from '../../control-objects';
import { ContainerKey } from '../../types';
import { TileLayout } from '../layouts';
import type { TileDecoratorProperties } from './tile-types';

class TileDecorator extends AbstractDecorator<ContainerKey.Tile> {
    protected _layout = TileLayout;

    protected _controlObjectConstructor = TileControlObject;
}

/**
 * Initializes the decorated member as a [Tile]{@link TileControlObject} container with the provided properties
 *
 * @param properties The properties that the [Tile]{@link TileControlObject} container will be initialized with
 */
export function tile<CT extends ScreenExtension<CT>>(
    properties: TileDecoratorProperties<Extend<CT>>,
): (target: CT, name: string) => void {
    return function tileDecorator(target: CT, name: string): void {
        const pageMetadata = getPageMetadata(getTargetPrototype(target.constructor), target);
        pageMetadata.definitionOrder.push(name);
        pageMetadata.blockThunks[name] = (
            nodeTypes: Dict<FormattedNodeDetails>,
            dataTypes: Dict<DataTypeDetails>,
        ): Partial<PageArticleItem> =>
            new TileDecorator(
                pageMetadata.target as any,
                name,
                { pageMetadata, properties },
                ContainerKey.Tile,
                nodeTypes,
                dataTypes,
                {
                    getValidationState: async (): Promise<boolean> => getValidationState(pageMetadata.screenId, name),
                    dispatchTileValidation: (): Promise<{
                        allErrors: ValidationResult[];
                        blockingErrors: ValidationResult[];
                    }> => dispatchContainerValidation(target.constructor.name, name),
                },
            ).build().layout;
    };
}
