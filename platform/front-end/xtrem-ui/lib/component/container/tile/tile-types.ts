import type { <PERSON>Status, Dict, LocalizeLocale } from '@sage/xtrem-shared';
import type { ReduxResponsive } from '../../../redux/state';
import type { PageArticleItem } from '../../../service/layout-types';
import type { PageDefinition } from '../../../service/page-definition';
import type { ScreenBase } from '../../../service/screen-base';
import type { ContextType } from '../../../types';
import type { ContainerProperties } from '../../abstract-container';
import type { BaseControlObject } from '../../base-control-object';
import type { SectionControlObject, TileControlObject } from '../../control-objects';
import type { ExtensionField, HasParent } from '../../field/traits';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import type { ContainerWidth } from '../../types';

export interface GridColumnDefinition {
    columnSpan: number;
    columnStart?: number;
    elementId: string;
    isFirstInRow?: boolean;
    isHidden?: boolean;
    isLastInRow?: boolean;
    row?: number;
}

export interface TileComponentExternalProps {
    accessRule?: AccessStatus;
    availableColumns: number;
    contextType?: ContextType;
    isParentDisabled?: boolean;
    /**
     * Indicates if any of the parents in the layout structure is hidden, it is required so we can cascade
     * down the hidden status and mark the hidden inputs not focusable
     * */
    isParentHidden?: boolean;
    item: Partial<PageArticleItem>;
    screenId: string;
}

export interface TileComponentProps extends TileComponentExternalProps {
    browser: ReduxResponsive;
    columnInfo: Dict<GridColumnDefinition>;
    fieldProperties: Omit<TileProperties, 'parent'>;
    screenType: string;
}

export interface TileFieldExternalProps {
    availableColumns: number;
    elementId: string;
    item: PageArticleItem;
    screenId: string;
    isParentDisabled?: boolean;
}

export interface TileFieldProps extends TileFieldExternalProps {
    browser: ReduxResponsive;
    fieldController: BaseControlObject<any, any>;
    fieldProperties: ReadonlyFieldProperties;
    locale: LocalizeLocale;
    pageDefinition: PageDefinition;
    value: any;
}

export interface TileDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<TileProperties<CT>, '_controlObjectType'>,
        HasParent<CT, SectionControlObject<CT>>,
        ExtensionField<CT, TileControlObject<CT>> {}

export interface TileProperties<CT extends ScreenBase = ScreenBase> extends ContainerProperties<CT> {
    /** The width of the tile relative to the section where it is place.
     * Must be a number between 1 and 12, both included*/
    width?: ContainerWidth;
}
