import {
    addFieldToState,
    addTileToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { FieldKey } from '../../../types';
import type { XtremAppState } from '../../../../redux/state';
import type { PageArticleItem } from '../../../../service/layout-types';
import type { TileProperties } from '../tile-types';
import { ConnectedTileComponent } from '../tile-component';
import type { PageDefinition } from '../../../../service/page-definition';
import { render, cleanup } from '@testing-library/react';

describe('tile component', () => {
    const screenId = 'TestPage';
    const elementId = 'test-ti;e';
    let store: MockStoreEnhanced<XtremAppState>;
    let item: Partial<PageArticleItem>;
    let state: XtremAppState;
    let pageDefinition: PageDefinition;
    let tileProperties: TileProperties = {};

    beforeEach(() => {
        tileProperties = {};
        tileProperties.title = 'Tile Title';
        tileProperties.isHidden = false;

        state = getMockState();
        pageDefinition = getMockPageDefinition(screenId);
        pageDefinition.metadata.uiComponentProperties[screenId] = { node: '@sage/xtrem-test/MyTestNode' } as any;
        state.screenDefinitions[screenId] = pageDefinition;
        addFieldToState(FieldKey.Text, state, screenId, 'fieldTextMedium', { width: 'medium' }, 'Hi there');
        addFieldToState(FieldKey.Select, state, screenId, 'fieldSelect', {}, 'option');
        addFieldToState(FieldKey.Date, state, screenId, 'fieldDate', {}, '2022-08-04');
        addFieldToState(FieldKey.Numeric, state, screenId, 'fieldTextLarge', { width: 'large' }, 3.45);
        addFieldToState(FieldKey.Text, state, screenId, 'fieldTextNormal1', {}, 'Some random value');
        addFieldToState(FieldKey.Text, state, screenId, 'fieldTextNormal2', {}, 'Some random value');
        addFieldToState(
            FieldKey.Text,
            state,
            screenId,
            'fieldTextFullWidth',
            { isFullWidth: true },
            'Some random value',
        );
        addFieldToState(FieldKey.Text, state, screenId, 'fieldTextNormal3', {}, 'Some random value');
        addFieldToState(FieldKey.Text, state, screenId, 'fieldTextHidden', { isHidden: true }, 'Some random value');

        state.browser = {
            is: {
                l: true,
                m: false,
                s: false,
                xs: false,
            },
            greaterThan: {
                l: false,
                m: false,
                s: false,
                xs: false,
            },
            lessThan: {
                l: false,
                m: true,
                s: true,
                xs: true,
            },
            mediaType: 'desktop',
            orientation: 'landscape',
        };
        store = getMockStore(state);
    });

    afterEach(() => {
        cleanup();
    });

    it('should render with a simple field', () => {
        item = addTileToState(state, screenId, elementId, tileProperties, [{ $bind: 'fieldSelect' }]);

        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileComponent screenId={screenId} item={item} availableColumns={8} />
            </Provider>,
        );

        const columns = wrapper.baseElement.querySelectorAll('.e-grid-row .e-grid-column');
        expect(columns).toHaveLength(1);
        expect(columns[0]).toHaveClass('e-grid-column-first-in-row');
        expect(columns[0]).toHaveClass('e-grid-column-last-in-row');
        expect(columns[0]).toHaveAttribute('style', 'grid-column: 7 / span 2;');
    });

    it('should render a full row', () => {
        item = addTileToState(state, screenId, elementId, tileProperties, [
            { $bind: 'fieldSelect' },
            { $bind: 'fieldTextNormal1' },
            { $bind: 'fieldTextNormal2' },
            { $bind: 'fieldTextNormal3' },
        ]);

        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileComponent screenId={screenId} item={item} availableColumns={8} />
            </Provider>,
        );

        const columns = wrapper.baseElement.querySelectorAll('.e-grid-row .e-grid-column');
        expect(columns).toHaveLength(4);
        expect(columns[0]).toHaveClass('e-grid-column-first-in-row');
        expect(columns[0]).not.toHaveClass('e-grid-column-last-in-row');
        expect(columns[0]).toHaveAttribute('style', 'grid-column: 1 / span 2;');

        expect(columns[1]).not.toHaveClass('e-grid-column-first-in-row');
        expect(columns[1]).not.toHaveClass('e-grid-column-last-in-row');
        expect(columns[1]).toHaveAttribute('style', 'grid-column: span 2;');

        expect(columns[2]).not.toHaveClass('e-grid-column-first-in-row');
        expect(columns[2]).not.toHaveClass('e-grid-column-last-in-row');
        expect(columns[2]).toHaveAttribute('style', 'grid-column: span 2;');

        expect(columns[3]).not.toHaveClass('e-grid-column-first-in-row');
        expect(columns[3]).toHaveClass('e-grid-column-last-in-row');
        expect(columns[3]).toHaveAttribute('style', 'grid-column: span 2;');
    });

    it('should render with a hidden field', () => {
        item = addTileToState(state, screenId, elementId, tileProperties, [
            { $bind: 'fieldSelect' },
            { $bind: 'fieldTextHidden' },
            { $bind: 'fieldTextNormal2' },
            { $bind: 'fieldTextNormal3' },
        ]);

        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileComponent screenId={screenId} item={item} availableColumns={8} />
            </Provider>,
        );

        const columns = wrapper.baseElement.querySelectorAll('.e-grid-row .e-grid-column');
        expect(columns).toHaveLength(3);
        expect(columns[0]).toHaveClass('e-grid-column-first-in-row');
        expect(columns[0]).not.toHaveClass('e-grid-column-last-in-row');
        expect(columns[0]).toHaveAttribute('style', 'grid-column: 3 / span 2;');

        expect(columns[1]).not.toHaveClass('e-grid-column-first-in-row');
        expect(columns[1]).not.toHaveClass('e-grid-column-last-in-row');
        expect(columns[1]).toHaveAttribute('style', 'grid-column: span 2;');

        expect(columns[2]).not.toHaveClass('e-grid-column-first-in-row');
        expect(columns[2]).toHaveClass('e-grid-column-last-in-row');
        expect(columns[2]).toHaveAttribute('style', 'grid-column: span 2;');
    });

    it('should render with a full-width column', () => {
        item = addTileToState(state, screenId, elementId, tileProperties, [
            { $bind: 'fieldSelect' },
            { $bind: 'fieldTextFullWidth', $isFullWidth: true },
            { $bind: 'fieldTextNormal2' },
            { $bind: 'fieldTextNormal3' },
        ]);

        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileComponent screenId={screenId} item={item} availableColumns={8} />
            </Provider>,
        );

        const columns = wrapper.baseElement.querySelectorAll('.e-grid-row .e-grid-column');
        expect(columns).toHaveLength(4);
        expect(columns[0]).toHaveClass('e-grid-column-first-in-row');
        expect(columns[0]).toHaveClass('e-grid-column-last-in-row');
        expect(columns[0]).toHaveAttribute('style', 'grid-column: 7 / span 2;');

        expect(columns[1]).toHaveClass('e-grid-column-first-in-row');
        expect(columns[1]).toHaveClass('e-grid-column-last-in-row');
        expect(columns[1]).toHaveAttribute('style', 'grid-column: 1 / span 8;');

        expect(columns[2]).toHaveClass('e-grid-column-first-in-row');
        expect(columns[2]).not.toHaveClass('e-grid-column-last-in-row');
        expect(columns[2]).toHaveAttribute('style', 'grid-column: 5 / span 2;');

        expect(columns[3]).not.toHaveClass('e-grid-column-first-in-row');
        expect(columns[3]).toHaveClass('e-grid-column-last-in-row');
        expect(columns[3]).toHaveAttribute('style', 'grid-column: span 2;');
    });

    it('should render with a medium sized field', () => {
        item = addTileToState(state, screenId, elementId, tileProperties, [
            { $bind: 'fieldTextMedium', $columnWidth: 'medium' },
            { $bind: 'fieldTextNormal1' },
            { $bind: 'fieldTextNormal2' },
            { $bind: 'fieldTextNormal3' },
        ]);

        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileComponent screenId={screenId} item={item} availableColumns={8} />
            </Provider>,
        );

        const columns = wrapper.baseElement.querySelectorAll('.e-grid-row .e-grid-column');
        expect(columns).toHaveLength(4);
        expect(columns[0]).toHaveClass('e-grid-column-first-in-row');
        expect(columns[0]).not.toHaveClass('e-grid-column-last-in-row');
        expect(columns[0]).toHaveAttribute('style', 'grid-column: 2 / span 3;');

        expect(columns[1]).not.toHaveClass('e-grid-column-first-in-row');
        expect(columns[1]).not.toHaveClass('e-grid-column-last-in-row');
        expect(columns[1]).toHaveAttribute('style', 'grid-column: span 2;');

        expect(columns[2]).not.toHaveClass('e-grid-column-first-in-row');
        expect(columns[2]).toHaveClass('e-grid-column-last-in-row');
        expect(columns[2]).toHaveAttribute('style', 'grid-column: span 2;');

        expect(columns[3]).toHaveClass('e-grid-column-first-in-row');
        expect(columns[3]).toHaveClass('e-grid-column-last-in-row');
        expect(columns[3]).toHaveAttribute('style', 'grid-column: 7 / span 2;');
    });

    it('should not render children disabled if the tile container is not disabled', () => {
        item = addTileToState(state, screenId, elementId, tileProperties, [
            { $bind: 'fieldTextMedium', $columnWidth: 'medium' },
            { $bind: 'fieldTextNormal1' },
            { $bind: 'fieldTextNormal2' },
            { $bind: 'fieldTextNormal3' },
        ]);

        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileComponent screenId={screenId} item={item} availableColumns={8} />
            </Provider>,
        );

        const tiles = wrapper.baseElement.querySelectorAll('.e-tile-field');
        expect(tiles).toHaveLength(4);
        tiles.forEach(t => {
            expect(t.classList.contains('e-disabled')).toBe(false);
        });
    });

    it('should render children disabled if the tile container is disabled', () => {
        tileProperties.isDisabled = true;
        item = addTileToState(state, screenId, elementId, tileProperties, [
            { $bind: 'fieldTextMedium', $columnWidth: 'medium' },
            { $bind: 'fieldTextNormal1' },
            { $bind: 'fieldTextNormal2' },
            { $bind: 'fieldTextNormal3' },
        ]);

        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileComponent screenId={screenId} item={item} availableColumns={8} />
            </Provider>,
        );

        const tiles = wrapper.baseElement.querySelectorAll('.e-tile-field');
        expect(tiles).toHaveLength(4);
        tiles.forEach(t => {
            expect(t.classList.contains('e-disabled')).toBe(true);
        });
    });

    it('should render children disabled if the tile container parent is disabled', () => {
        item = addTileToState(state, screenId, elementId, tileProperties, [
            { $bind: 'fieldTextMedium', $columnWidth: 'medium' },
            { $bind: 'fieldTextNormal1' },
            { $bind: 'fieldTextNormal2' },
            { $bind: 'fieldTextNormal3' },
        ]);

        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileComponent screenId={screenId} item={item} availableColumns={8} isParentDisabled={true} />
            </Provider>,
        );

        const tiles = wrapper.baseElement.querySelectorAll('.e-tile-field');
        expect(tiles).toHaveLength(4);
        tiles.forEach(t => {
            expect(t.classList.contains('e-disabled')).toBe(true);
        });
    });
});
