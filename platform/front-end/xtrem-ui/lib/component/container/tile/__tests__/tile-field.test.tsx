import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { FieldKey } from '../../../types';
import type { XtremAppState } from '../../../../redux/state';
import { ConnectedTileField } from '../tile-field';
import type { PageDefinition } from '../../../../service/page-definition';
import { render, cleanup, fireEvent } from '@testing-library/react';
import * as events from '../../../../utils/events';

describe('tile field', () => {
    const screenId = 'TestPage';
    let store: MockStoreEnhanced<XtremAppState>;
    let state: XtremAppState;
    let pageDefinition: PageDefinition;
    let triggerFieldEvent: jest.MockInstance<any, any>;

    beforeEach(() => {
        triggerFieldEvent = jest.spyOn(events, 'triggerFieldEvent').mockImplementation();

        state = getMockState();
        pageDefinition = getMockPageDefinition(screenId);
        pageDefinition.metadata.uiComponentProperties[screenId] = { node: '@sage/xtrem-test/MyTestNode' } as any;
        state.screenDefinitions[screenId] = pageDefinition;
        addFieldToState(
            FieldKey.Text,
            state,
            screenId,
            'textFieldWithTitle',
            { title: 'Test field', onClick: jest.fn() },
            'Hi there',
        );
        addFieldToState(
            FieldKey.Text,
            state,
            screenId,
            'textFieldWithTitleHidden',
            { title: 'Test field', isTitleHidden: true },
            'Hi there',
        );
        addFieldToState(FieldKey.Text, state, screenId, 'textFieldWithPrefix', { prefix: '&' }, 'Hi there');
        addFieldToState(FieldKey.Text, state, screenId, 'textFieldWithPostfix', { postfix: '&' }, 'Hi there');
        addFieldToState(
            FieldKey.Text,
            state,
            screenId,
            'textFieldWithHelperText',
            { helperText: 'some helper text' },
            'Hi there',
        );
        addFieldToState(
            FieldKey.Text,
            state,
            screenId,
            'textFieldWithHelperTextHidden',
            { helperText: 'some helper text', isHelperTextHidden: true },
            'Hi there',
        );
        addFieldToState(FieldKey.Numeric, state, screenId, 'numericField', {}, 3.45);
        addFieldToState(
            FieldKey.Text,
            state,
            screenId,
            'disabledTextField',
            { isDisabled: true, onClick: jest.fn() },
            'hi!',
        );

        state.browser = {
            is: {
                l: true,
                m: false,
                s: false,
                xs: false,
            },
            greaterThan: {
                l: false,
                m: false,
                s: false,
                xs: false,
            },
            lessThan: {
                l: false,
                m: true,
                s: true,
                xs: true,
            },
            mediaType: 'desktop',
            orientation: 'landscape',
        };
        store = getMockStore(state);
    });

    afterEach(() => {
        triggerFieldEvent.mockClear();
        cleanup();
    });

    it('should render field with value, uppercase title', () => {
        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileField
                    screenId={screenId}
                    availableColumns={8}
                    elementId="textFieldWithTitle"
                    item={{ $bind: 'textFieldWithTitle' }}
                />
            </Provider>,
        );

        const title = wrapper.queryByTestId('e-field-label')!;
        expect(title).not.toBeNull();
        expect(title).toHaveTextContent('TEST FIELD');
        expect(wrapper.queryByTestId('e-field-value')!).toHaveTextContent('Hi there');
    });

    it('should render a clickable button if the element has an onClick listener', () => {
        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileField
                    screenId={screenId}
                    availableColumns={8}
                    elementId="textFieldWithTitle"
                    item={{ $bind: 'textFieldWithTitle' }}
                />
            </Provider>,
        );

        const mainElement = wrapper.queryByTestId('e-field-bind-textFieldWithTitle', { exact: false })!;
        expect(mainElement.classList.contains('e-disabled')).toBe(false);

        expect(mainElement.nodeName).toEqual('BUTTON');
        expect(mainElement.getAttribute('disabled')).toBeNull();

        expect(triggerFieldEvent).not.toHaveBeenCalled();
        fireEvent.click(mainElement);
        expect(triggerFieldEvent).toHaveBeenCalled();
    });

    it('should render a disabled button if the element has an onClick listener and is disabled', () => {
        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileField
                    screenId={screenId}
                    availableColumns={8}
                    elementId="disabledTextField"
                    item={{ $bind: 'disabledTextField' }}
                />
            </Provider>,
        );

        const mainElement = wrapper.queryByTestId('e-field-bind-disabledTextField', { exact: false })!;

        expect(mainElement.nodeName).toEqual('BUTTON');
        expect(mainElement.getAttribute('disabled')).not.toBeNull();
        expect(mainElement.classList.contains('e-disabled')).toBe(true);

        expect(triggerFieldEvent).not.toHaveBeenCalled();
        fireEvent.click(mainElement);
        expect(triggerFieldEvent).not.toHaveBeenCalled();
    });

    it('should as a DIV if no onClick event declared', () => {
        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileField
                    screenId={screenId}
                    availableColumns={8}
                    elementId="textFieldWithPrefix"
                    item={{ $bind: 'textFieldWithPrefix' }}
                />
            </Provider>,
        );

        const mainElement = wrapper.queryByTestId('e-field-bind-textFieldWithPrefix', { exact: false })!;

        expect(mainElement.nodeName).toEqual('DIV');
    });

    it('should not render title if it is hidden', () => {
        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileField
                    screenId={screenId}
                    availableColumns={8}
                    elementId="textFieldWithTitleHidden"
                    item={{ $bind: 'textFieldWithTitleHidden' }}
                />
            </Provider>,
        );

        const title = wrapper.queryByTestId('e-field-label')!;
        expect(title).not.toBeNull();
        expect(title).toHaveTextContent('');
    });

    it('should render with helper text', () => {
        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileField
                    screenId={screenId}
                    availableColumns={8}
                    elementId="textFieldWithHelperText"
                    item={{ $bind: 'textFieldWithHelperText' }}
                />
            </Provider>,
        );

        const helperText = wrapper.queryByTestId('e-field-helper-text')!;
        expect(helperText).not.toBeNull();
        expect(helperText).toHaveTextContent('some helper text');
    });

    it('should not the helper text if it is hidden', () => {
        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileField
                    screenId={screenId}
                    availableColumns={8}
                    elementId="textFieldWithHelperTextHidden"
                    item={{ $bind: 'textFieldWithHelperTextHidden' }}
                />
            </Provider>,
        );
        const helperText = wrapper.queryByTestId('e-field-helper-text')!;
        expect(helperText).not.toBeNull();
        expect(helperText).toHaveTextContent('');
    });

    it('should render field value with a prefix', () => {
        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileField
                    screenId={screenId}
                    availableColumns={8}
                    elementId="textFieldWithPrefix"
                    item={{ $bind: 'textFieldWithPrefix' }}
                />
            </Provider>,
        );

        expect(wrapper.queryByTestId('e-field-value')!).toHaveTextContent('Hi there');
        expect(wrapper.queryByTestId('e-field-value-prefix')!).toHaveTextContent('&');
    });

    it('should render field value with a postfix', () => {
        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileField
                    screenId={screenId}
                    availableColumns={8}
                    elementId="textFieldWithPostfix"
                    item={{ $bind: 'textFieldWithPostfix' }}
                />
            </Provider>,
        );

        expect(wrapper.queryByTestId('e-field-value')!).toHaveTextContent('Hi there');
        expect(wrapper.queryByTestId('e-field-value-postfix')!).toHaveTextContent('&');
    });

    it('should render numeric field to the right', () => {
        const wrapper = render(
            <Provider store={store}>
                <ConnectedTileField
                    screenId={screenId}
                    availableColumns={8}
                    elementId="numericField"
                    item={{ $bind: 'numericField' }}
                />
            </Provider>,
        );

        expect(wrapper.queryByTestId('e-field-value')!).toHaveTextContent('3.45');
        expect(wrapper.queryByTestId('e-field-bind-numericField', { exact: false })!.className).toEqual(
            'e-tile-field e-tile-field-right-aligned',
        );
    });
});
