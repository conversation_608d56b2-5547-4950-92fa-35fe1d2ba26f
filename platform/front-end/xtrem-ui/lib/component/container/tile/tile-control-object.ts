/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { getUiComponentProperties, setUiComponentProperties } from '../../../service/transactions-service';
import type { ScreenExtension } from '../../../types';
import { AbstractContainer } from '../../abstract-container';
import type { ContainerComponentProps, ContainerControlObjectConstructorProps, ParentType } from '../../types';
import { ContainerKey } from '../../types';
import type { TileProperties } from './tile-types';

export interface ITileControlObject extends ContainerControlObjectConstructorProps<ContainerKey.Tile> {
    parent?: ParentType<ContainerKey.Tile>;
    dispatchTileValidation: () => Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
}

/**
 * [Container]{@link AbstractContainer} that holds any number of [fields]{@link AbstractField}
 */
export class TileControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends AbstractContainer<
    CT,
    ContainerKey.Tile,
    ContainerComponentProps<ContainerKey.Tile>
> {
    public insertBefore?: ContainerControlObjectConstructorProps<ContainerKey.Tile>['insertBefore'];

    private readonly _dispatchTileValidation: ITileControlObject['dispatchTileValidation'];

    static readonly defaultUiProperties: Partial<TileProperties> = {
        ...AbstractContainer.defaultUiProperties,
    };

    theParentOfThisFieldNeedsToBeABlockContainer: boolean;

    constructor(properties: ITileControlObject) {
        super(
            properties.screenId,
            properties.elementId,
            properties.getUiComponentProperties || getUiComponentProperties,
            properties.setUiComponentProperties || setUiComponentProperties,
            ContainerKey.Tile,
            properties.getValidationState || (async (): Promise<boolean> => true),
            properties.layout,
            properties.parent,
        );
        this.insertBefore = properties.insertBefore;
        this._dispatchTileValidation = properties.dispatchTileValidation;
    }

    /**
     * Triggers the field validation rules. Since the validation rules might be asynchronous,
     * this method returns a promise that must be awaited to get the validation result. Compared to the `validate` method
     * it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
     */
    async validateWithDetails(
        partition: true,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
    async validateWithDetails(partition: false): Promise<ValidationResult[]>;
    async validateWithDetails(): Promise<ValidationResult[]>;
    async validateWithDetails(
        partition = false,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] } | ValidationResult[]> {
        const errors = await this._dispatchTileValidation();
        return partition ? errors : errors.allErrors;
    }
}
