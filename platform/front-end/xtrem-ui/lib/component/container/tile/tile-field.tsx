import * as React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import type { BaseControlObject } from '../../base-control-object';
import {
    getFieldTitle,
    getFieldHelperText,
    isFieldHelperTextHidden,
    isFieldTitleHidden,
} from '../../field/carbon-helpers';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import { getFieldDisplayValue } from '../../../utils/id-field-utils';
import type { PageHeaderFields } from '../page/page-types';
import Icon from 'carbon-react/esm/components/icon';
import type { HasIcon, Prefixable, Postfixable, Clickable, HasUnit } from '../../field/traits';
import { getDataTestIdAttribute } from '../../../utils/dom';
import { kebabCase } from 'lodash';
import { FieldKey } from '../../types';
import type { TileFieldExternalProps, TileFieldProps } from './tile-types';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { getScalePrefixPostfixFromUnit } from '../../../utils/formatters';
import type { LocalizeLocale } from '@sage/xtrem-shared';

const rightAlignedFieldTypes = [FieldKey.Aggregate, FieldKey.Numeric, FieldKey.Count];

function TileField(props: TileFieldProps): React.ReactElement {
    const title = getFieldTitle(props.screenId, props.fieldProperties, null);
    const helperText = getFieldHelperText(props.screenId, props.fieldProperties, null);
    const isTitleHidden = isFieldTitleHidden(props.screenId, props.fieldProperties, null);
    const isHelperTextHidden = isFieldHelperTextHidden(props.screenId, props.fieldProperties, null);
    const fieldProperties = props.fieldProperties as ReadonlyFieldProperties<any> &
        HasIcon &
        Prefixable<any> &
        Postfixable<any> &
        Clickable<any>;
    const fieldType = props.fieldProperties._controlObjectType as FieldKey;
    const testIdType = kebabCase(String(fieldType));
    const isRightAligned = rightAlignedFieldTypes.includes(fieldType);

    const computedUnitProperties = getScalePrefixPostfixFromUnit(
        props.screenId,
        props.locale,
        props.fieldProperties as HasUnit<any>,
    );

    const prefix =
        computedUnitProperties?.prefix ??
        (resolveByValue<string>({
            fieldValue: props.value,
            propertyValue: fieldProperties.prefix,
            rowValue: null,
            skipHexFormat: true,
            screenId: props.screenId,
        }) ||
            '');

    const postfix =
        computedUnitProperties?.postfix ??
        (resolveByValue<string>({
            fieldValue: props.value,
            propertyValue: fieldProperties.postfix,
            rowValue: null,
            skipHexFormat: true,
            screenId: props.screenId,
        }) ||
            '');

    const isDisabled =
        props.isParentDisabled ||
        resolveByValue<boolean>({
            fieldValue: props.value,
            propertyValue: fieldProperties.isDisabled,
            rowValue: null,
            skipHexFormat: true,
            screenId: props.screenId,
        });

    const onClick = async (): Promise<void> => {
        if (!isDisabled) {
            await triggerFieldEvent(props.screenId, props.elementId, 'onClick');
        }
    };

    const fieldBody = (
        <>
            <label className="e-tile-field-title" data-element="label" data-testid="e-field-label">
                {!isTitleHidden && title && title.toUpperCase()}
            </label>
            <div className="e-tile-field-value">
                {prefix && (
                    <span className="e-field-value-prefix" data-testid="e-field-value-prefix">
                        {prefix}
                    </span>
                )}
                <span data-testid="e-field-value">
                    {getFieldDisplayValue(
                        props.pageDefinition,
                        props.fieldController as PageHeaderFields,
                        props.locale,
                    )}
                </span>
                {postfix && (
                    <span className="e-field-value-postfix" data-testid="e-field-value-postfix">
                        {postfix}
                    </span>
                )}
                {fieldProperties.icon && <Icon type={fieldProperties.icon} color={fieldProperties.iconColor} />}
            </div>
            <span className="e-tile-field-helper-text" data-testid="e-field-helper-text">
                {!isHelperTextHidden && helperText}
            </span>
        </>
    );

    const classes = ['e-tile-field'];
    if (isRightAligned) {
        classes.push('e-tile-field-right-aligned');
    }

    if (isDisabled) {
        classes.push('e-disabled');
    }

    return fieldProperties.onClick ? (
        <button
            data-testid={getDataTestIdAttribute(testIdType, title, props.elementId)}
            className={classes.join(' ')}
            onClick={onClick}
            type="button"
            disabled={isDisabled}
        >
            {fieldBody}
        </button>
    ) : (
        <div data-testid={getDataTestIdAttribute(testIdType, title, props.elementId)} className={classes.join(' ')}>
            {fieldBody}
        </div>
    );
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: TileFieldExternalProps): TileFieldProps => {
    if (!props.item) {
        throw new Error(`No item definition found for tile field ${props.screenId}`);
    }
    const elementId = props.item.$bind;
    const pageDefinition = getPageDefinitionFromState(props.screenId, state);
    const fieldProperties = pageDefinition.metadata.uiComponentProperties[elementId] as ReadonlyFieldProperties;

    return {
        ...props,
        browser: state.browser,
        fieldController: pageDefinition.metadata.controlObjects[elementId] as BaseControlObject<any, any>,
        fieldProperties,
        locale: (state.applicationContext?.locale as LocalizeLocale) || 'en-US',
        pageDefinition,
        value: pageDefinition.values[elementId],
    };
};

export const ConnectedTileField = connect(mapStateToProps)(TileField);
