import * as React from 'react';
import type { DialogDescription, PageDialogOptions, CustomDialogOptions } from '../../../types/dialogs';
import { collectSections } from './dialog-utils';
import { useSelector, useDispatch } from 'react-redux';
import * as xtremRedux from '../../../redux';
import type { Dict } from '@sage/xtrem-shared';
import type { ScreenBaseDefinition } from '../../../service/screen-base-definition';
import type { PageControlObject } from '../page/page-control-object';
import { XtremTabs } from '../../ui/tabs/xtrem-tabs';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { getPageDefinition } from '../../../utils/state-utils';
import { getSectionValidationMessage } from '../../../service/validation-service';
import type { PageDefinition } from '../../../service/page-definition';
import { WizardSteps } from '../page/wizard-steps';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

export interface DialogHeaderProps {
    dialog: DialogDescription<PageDialogOptions & CustomDialogOptions>;
    isMobileDialog?: boolean;
    isTabletDialog?: boolean;
    selectedSection?: string | null;
    setSelectedSection: (selectedSectionId: string) => void;
}

export function DialogHeader({
    dialog,
    isMobileDialog = false,
    isTabletDialog = false,
    setSelectedSection,
    selectedSection,
}: DialogHeaderProps): React.ReactElement | null {
    const dispatch = useDispatch();
    const screenDefinitions = useDeepEqualSelector<xtremRedux.XtremAppState, Dict<ScreenBaseDefinition>>(
        state => state.screenDefinitions,
    );

    const pageSelectedSection = useSelector<xtremRedux.XtremAppState, string | null>(state =>
        dialog.screenId && dialog.type === 'page'
            ? (state.screenDefinitions[dialog.screenId] as PageDefinition)?.activeSection || null
            : null,
    );

    const sectionValidationErrors = useDeepEqualSelector<xtremRedux.XtremAppState, Dict<string | null>>(state => {
        if (dialog.screenId && dialog.type === 'page') {
            const pageDefinition = getPageDefinition(dialog.screenId, state);
            if (!pageDefinition) {
                return {};
            }
            const sectionIds: string[] = pageDefinition.metadata.layout.$items
                .filter(section => !!section.$containerId)
                .map(section => section.$containerId as string);

            return sectionIds.reduce((result: Dict<string | null>, sectionId: string) => {
                if (dialog.screenId) {
                    return {
                        ...result,
                        [sectionId]: getSectionValidationMessage(dialog.screenId, sectionId, state),
                    };
                }
                return result;
            }, {});
        }

        return {};
    });

    const onTabChange = React.useCallback(
        (activeSection: string) => {
            if (dialog.type === 'page' && dialog.screenId) {
                dispatch(xtremRedux.actions.setActiveSection(dialog.screenId, activeSection));
            } else {
                setSelectedSection(activeSection);
            }
        },
        [dispatch, dialog, setSelectedSection],
    );

    // For custom dialogs, the selected section is tracked in the local state of the dialog component
    // For page dialogs, the selected section is tracked in the application state.
    const sections = collectSections(dialog, screenDefinitions);
    const selectedTabId = pageSelectedSection || selectedSection || sections[0]?.id || null;

    const isPageDialog = dialog.type === 'page';
    const isTabsMode = (dialog.content as PageControlObject)?.mode === 'tabs';
    const isWizardMode = (dialog.content as PageControlObject)?.mode === 'wizard';
    const isFullScreenPageDialog = isPageDialog && !!dialog.options.fullScreen;
    const isMobileOrTabletPageDialog = isPageDialog && (isMobileDialog || isTabletDialog);

    // The tabs should only be displayed if the content is non-page dialog or a page dialog in tabs
    // mode not in fullscreen. For fullscreen page dialogs, the page itself manages the display of
    // the header section and tabs.
    const useTabs =
        isFullScreenPageDialog || isMobileOrTabletPageDialog
            ? false
            : sections.length > 1 && (!isPageDialog || isTabsMode);

    // The steps should only be displayed if the content is a page that is set to wizard mode
    const useWizardSteps = sections.length > 1 && isPageDialog && isWizardMode;

    if (useTabs) {
        return (
            <XtremTabs
                inSidebar={true}
                onTabChange={onTabChange}
                selectedTabId={selectedTabId || ''}
                tabs={sections
                    .map(section => ({
                        id: section.id,
                        title: resolveByValue<string>({
                            propertyValue: section.title,
                            screenId: dialog.screenId || '',
                            fieldValue: null,
                            rowValue: null,
                            skipHexFormat: true,
                        }),
                        indicatorContent: section.indicatorContent,
                        validationMessage: sectionValidationErrors[section.id],
                    }))
                    .filter(t => !!t.title)}
            />
        );
    }

    if (useWizardSteps) {
        return <WizardSteps screenId={dialog.screenId || ''} sections={sections} selectedSection={selectedTabId} />;
    }

    if (sections.length > 1 && isFullScreenPageDialog && !isMobileDialog) {
        // Adds empty div to retain the spacing of the dialog header.
        return <div className="e-xtrem-tabs--blank" />;
    }

    return null;
}
