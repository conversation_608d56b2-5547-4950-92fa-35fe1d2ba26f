import * as React from 'react';
import type { CrumbProps } from 'carbon-react/esm/components/breadcrumbs';
import { Breadcrumbs, Crumb } from 'carbon-react/esm/components/breadcrumbs';
import { useDispatch, useSelector } from 'react-redux';
import * as xtremRedux from '../../../redux';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import { QUERY_PARAM_TUNNEL_SEGMENTS } from '../../../utils/constants';
import type { TunnelSegment } from '../../../redux/actions';
import { kebabCase } from 'lodash';

export interface DialogBreadcrumbsProps {
    screenId: string | null;
}

export function DialogBreadcrumbs({ screenId }: DialogBreadcrumbsProps): React.ReactElement | null {
    const dispatch = useDispatch();

    const breadcrumbs = useSelector<xtremRedux.XtremAppState, CrumbProps[]>(state => {
        if (!screenId) {
            return [];
        }
        const pageDefinition = getPageDefinitionFromState(screenId, state);

        const segments = pageDefinition.queryParameters[QUERY_PARAM_TUNNEL_SEGMENTS] as unknown as TunnelSegment[];

        if (!segments) {
            return [];
        }

        return segments.map<CrumbProps>(s => ({
            children: s.label,
            key: s.screenId,
            onClick: (): void => {
                dispatch(xtremRedux.actions.closeTunnelByBreadcrumbLink(s.screenId, screenId));
            },
        }));
    });

    if (!screenId || breadcrumbs.length === 0) {
        return null;
    }

    const updatedBreadcrumbs = breadcrumbs.map<CrumbProps & { key: string }>((b: CrumbProps, i: number) => {
        return { ...b, key: String(i), isCurrent: i === breadcrumbs.length - 1 };
    });

    return (
        <div data-testid={`e-breadcrumb-${kebabCase(screenId)}`}>
            <Breadcrumbs pt="20px">
                {updatedBreadcrumbs.map(c => (
                    // eslint-disable-next-line react/jsx-key
                    <Crumb {...c} />
                ))}
            </Breadcrumbs>
        </div>
    );
}
