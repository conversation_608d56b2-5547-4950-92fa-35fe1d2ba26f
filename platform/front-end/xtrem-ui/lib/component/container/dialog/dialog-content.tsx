import type { CustomDialogOptions, DialogDescription, PageDialogOptions } from '../../../types/dialogs';
import * as React from 'react';
import { getPageBodyColumnCount } from '../../../utils/responsive-utils';
import * as xtremRedux from '../../../redux';
import { useDispatch } from 'react-redux';
import type { ReduxResponsive } from '../../../redux/state';
import type { ScreenBaseDefinition } from '../../../service/screen-base-definition';
import type { Dict } from '@sage/xtrem-shared';
import { collectSections } from './dialog-utils';
import { DialogBodyContent } from './body/dialog-body-content';
import { useResizeObserver, useWindowSize } from 'usehooks-ts';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

export interface DialogContentProps {
    activeSection?: string | null;
    dialog: DialogDescription<PageDialogOptions & CustomDialogOptions>;
    defaultFocusRef?: React.RefObject<any>;
    noHeader?: boolean;
    isFullScreen?: boolean;
}

export function DialogContent({
    dialog,
    activeSection,
    defaultFocusRef,
    noHeader,
    isFullScreen,
}: DialogContentProps): React.ReactElement {
    const ref = React.useRef<HTMLDivElement>(null);
    const { height: windowHeight } = useWindowSize({ debounceDelay: 100 });
    const { height: dialogContainerHeight } = useResizeObserver({ ref });
    const browser = useDeepEqualSelector<xtremRedux.XtremAppState, ReduxResponsive>(s => s.browser);
    const screenDefinitions = useDeepEqualSelector<xtremRedux.XtremAppState, Dict<ScreenBaseDefinition>>(
        s => s.screenDefinitions,
    );

    const dialogBodyHeight = React.useMemo(() => {
        if (isFullScreen) {
            return dialogContainerHeight ? Math.max(dialogContainerHeight, 150) : undefined;
        }
        return windowHeight ? Math.max(windowHeight * 0.9 - 154, 150) : undefined;
    }, [dialogContainerHeight, isFullScreen, windowHeight]);

    const dispatch = useDispatch();

    const screenDefinition = dialog.screenId ? screenDefinitions[dialog.screenId] : undefined;

    const sections = collectSections(dialog, screenDefinitions);
    const currentSectionIndex = sections.findIndex(s => s?.id === activeSection);

    const gridColumnCount = React.useMemo((): number => {
        if (browser.is.xs) {
            return 4;
        }

        if (dialog.options.fullScreen) {
            return getPageBodyColumnCount(browser?.is);
        }

        if (dialog.options.size === 'extra-large') {
            return 8;
        }

        return 4;
    }, [browser.is, dialog.options.fullScreen, dialog.options.size]);

    const onStepOneSection = React.useCallback(
        (direction: 1 | -1): void => {
            if (dialog.content && dialog.type === 'page' && dialog.screenId) {
                dispatch(xtremRedux.actions.stepOneSection(dialog.screenId, direction));
            }
        },
        [dialog.content, dialog.screenId, dialog.type, dispatch],
    );

    const dialogContentClasses: string[] = ['e-dialog-content', `e-dialog-type-${dialog.type || 'unknown'}`];

    const isFirstSection = !activeSection || currentSectionIndex === 0;
    const isLastSection = sections.length === currentSectionIndex + 1;

    return (
        <div className={dialogContentClasses.join(' ')} ref={ref}>
            <div className="e-dialog-body" data-testid="e-dialog-body">
                <DialogBodyContent
                    dialog={dialog}
                    screenDefinition={screenDefinition}
                    availableColumns={gridColumnCount}
                    defaultFocusRef={defaultFocusRef}
                    selectedSection={activeSection}
                    noHeader={noHeader}
                    onStepOneSection={onStepOneSection}
                    isFirstSection={isFirstSection}
                    isLastSection={isLastSection}
                    dialogBodyHeight={dialogBodyHeight}
                />
            </div>
        </div>
    );
}
