import type { Dict } from '@sage/xtrem-shared';
import Dialog from 'carbon-react/esm/components/dialog';
import DialogFullScreen from 'carbon-react/esm/components/dialog-full-screen';
import Heading from 'carbon-react/esm/components/heading';
import Sidebar from 'carbon-react/esm/components/sidebar';
import { isNil, noop } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import { openDirtyPageConfirmationDialog } from '../../../service/dirty-state-service';
import type { ScreenBaseDefinition } from '../../../service/screen-base-definition';
import type { CustomDialogOptions, DialogDescription, PageDialogOptions } from '../../../types/dialogs';
import { focusTopPage } from '../../../utils/dom';
import { isScreenDefinitionDirty } from '../../../utils/state-utils';
import { TableControlObject } from '../../field/field-control-objects';
import { collectSections } from './dialog-utils';
import { DialogHeader } from './dialog-header';
import type { PageDefinition } from '../../../service/page-definition';
import type { SectionControlObject } from '../container-control-objects';
import { getHeaderSection, getVisibleSections } from '../../../utils/layout-utils';
import { DialogContent } from './dialog-content';
import { DialogTitle } from './dialog-title';

/**
 * This selector defines what elements are discovered by Carbon's dialog focus trap.
 */

const FOCUS_TRAP_SELECTOR = [
    'button.e-ui-confirmation-dialog-button',
    'button.e-ui-select-lookup-button',
    'div.e-ui-select-lookup-button button',
    'button.e-xtrem-tab-item',
    '.e-dialog-title-back-button-wrapper button',
    'button[data-element="close"]',
    '.e-file-deposit-field-content-wrapper button',
    'button.e-page-crud-button',
    '.e-business-action button',
    '[href]',
    'input:not([type="hidden"]):not([disabled]):not(.e-hidden)',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not(.DayPicker-wrapper):not(.rdp-day_button)',
    '[data-component="breadcrumbs"] button',
];

export interface DialogComponentExternalProps {
    dialogKey: string;
    isFullScreen: boolean;
}

export interface DialogComponentProps extends DialogComponentExternalProps {
    closeDialog: (dialogId: number) => Promise<void>;
    closeTableSidebar: (dialogId: number) => void;
    dialog: DialogDescription<PageDialogOptions & CustomDialogOptions>;
    isMobileDialog?: boolean;
    isTabletDialog?: boolean;
    screenDefinitions: Dict<ScreenBaseDefinition>;
    setActiveSection: (screenId: string, activeSection: string) => Promise<void>;
}

interface DialogComponentState {
    // We need to track the selected section locally for custom dialogs, but for page dialogs they are tracked in the global state
    activeSection: string | null;
}

export class DialogComponent extends React.Component<DialogComponentProps, DialogComponentState> {
    private normalDialogRef = React.createRef<any>();

    constructor(props: DialogComponentProps) {
        super(props);
        this.state = {
            activeSection:
                this.props.dialog.content && this.props.dialog.type === 'custom'
                    ? collectSections(props.dialog, props.screenDefinitions)[0]?.id || null
                    : null,
        };
    }

    componentDidUpdate(prevProps: DialogComponentProps): void {
        if (this.normalDialogRef.current) {
            this.normalDialogRef.current?.centerDialog();
        }

        if (!prevProps.dialog.content && this.props.dialog.content && this.props.dialog.type === 'custom') {
            this.setState({
                activeSection:
                    collectSections(this.props.dialog, xtremRedux.getStore().getState().screenDefinitions)[0]?.id ||
                    null,
            });
        }
    }

    closeDialog = async (): Promise<void> => {
        if (this.props.dialog.type === 'table-sidebar') {
            this.props.closeTableSidebar(this.props.dialog.dialogId);
        } else {
            const state = xtremRedux.getStore().getState();
            if (
                this.props.dialog.screenId &&
                this.props.dialog.type === 'page' &&
                !this.props.dialog.options.skipDirtyCheck &&
                state.screenDefinitions[this.props.dialog.screenId] &&
                isScreenDefinitionDirty(state.screenDefinitions[this.props.dialog.screenId])
            ) {
                try {
                    await openDirtyPageConfirmationDialog(this.props.dialog.screenId);
                } catch {
                    return;
                }
            }

            this.props.dialog.dialogControl?.onClose?.();
            if (this.props.dialog.options.resolveOnCancel) {
                this.props.dialog.dialogControl.resolve(null);
            } else {
                this.props.dialog.dialogControl.cancel();
            }

            await this.props.closeDialog(this.props.dialog.dialogId);
            focusTopPage();
        }
    };

    getActiveSection = (): string | null => {
        if (this.props.dialog.content && this.isPageDialog() && this.props.dialog.screenId) {
            const pageDefinition = xtremRedux.getStore().getState().screenDefinitions[
                this.props.dialog.screenId
            ] as PageDefinition;
            if (!pageDefinition) {
                return null;
            }

            const headerSection = getHeaderSection(pageDefinition);
            if (
                pageDefinition.activeSection &&
                (!this.isFullScreenPageDialog() || pageDefinition.activeSection !== headerSection?.id)
            ) {
                return pageDefinition.activeSection;
            }

            const visibleSections =
                !this.isFullScreenPageDialog() || isNil(headerSection)
                    ? getVisibleSections(pageDefinition, {})
                    : getVisibleSections(pageDefinition, {}).filter(
                          section => section.$containerId !== headerSection.id,
                      );

            return visibleSections[0]?.$containerId || null;
        }

        return this.state.activeSection;
    };

    /**
     *
     * @description Carbon Dialog focustrap and AgGrid internal state focus handling are incompatible.
     * They compete and overlap each other for the same browser events and imperative effects (.focus()).
     * Therefore, we disable the focus trap when the page dialog includes a table within.
     * For the same reasons we disable the 'Esc' key.
     *
     * `disableFocusTrap: boolean` does not currently work... so we use the hidden `bespokeFocusTrap`
     * callback property with a noop function, which does the same. We should consider a
     * replacement in the future, when `disableFocusTrap` is back in the Carbon library.
     *
     * Giving its current state, disabling by default the FocusTrap in all/most of the dialogs might
     * not be completely discarded. Browsers defaults may be enough for most cases.
     *
     */
    hasTableField = (): boolean => {
        const screenId = this.props.dialog.screenId;
        const ctrlObjs =
            (this.props.dialog.type === 'page' &&
                screenId &&
                xtremRedux.getStore().getState().screenDefinitions[screenId]?.metadata?.controlObjects) ||
            {};
        return Boolean(
            Object.values(ctrlObjs).find(ctrlObj => ctrlObj instanceof TableControlObject && !ctrlObj.isHidden),
        );
    };

    isPageDialog = (): boolean => {
        return this.props.dialog.type === 'page';
    };

    isFullScreenPageDialog = (): boolean => {
        return (
            this.isPageDialog() &&
            !!(this.props.dialog.options.fullScreen || this.props.isMobileDialog || this.props.isTabletDialog)
        );
    };

    setSelectedSection = (selectedSection: string): void => {
        if (this.props.dialog.content && this.props.dialog.type === 'custom') {
            this.setState({ activeSection: selectedSection });
        } else if (this.props.dialog.content && this.props.dialog.type === 'page' && this.props.dialog.screenId) {
            this.props.setActiveSection(this.props.dialog.screenId, selectedSection);
        }
    };

    renderSidebarDialog = (): React.ReactNode => {
        const forwardRef = React.createRef();
        const hasTableField = this.hasTableField();

        return (
            <Sidebar
                size={
                    this.props.dialog.type !== 'table-sidebar' ? this.props.dialog.options.size || 'medium' : undefined
                }
                width={this.props.dialog.type === 'table-sidebar' ? 714 : undefined}
                key={this.props.dialog.dialogId}
                onCancel={this.closeDialog}
                open={true}
                focusableSelectors={FOCUS_TRAP_SELECTOR.join(', ')}
                disableEscKey={hasTableField}
                bespokeFocusTrap={hasTableField ? noop : undefined}
                aria-label={this.props.dialog.title}
                header={
                    <div className="e-dialog-sidebar-heading">
                        <Heading
                            divider={false}
                            subheader={this.props.dialog.subtitle}
                            mb="12px !important"
                            title={<DialogTitle dialog={this.props.dialog} onCloseDialog={this.closeDialog} />}
                        />
                        <DialogHeader
                            dialog={this.props.dialog}
                            setSelectedSection={this.setSelectedSection}
                            selectedSection={this.getActiveSection()}
                        />
                    </div>
                }
            >
                <DialogContent
                    dialog={this.props.dialog}
                    activeSection={this.getActiveSection()}
                    defaultFocusRef={forwardRef}
                    noHeader={true}
                />
            </Sidebar>
        );
    };

    renderNormalDialog = (): React.ReactNode => {
        const forwardRef = React.createRef();
        const hasTableField = this.hasTableField();
        return (
            <Dialog
                key={this.props.dialog.dialogId}
                open={true}
                showCloseIcon={true}
                size={this.props.dialog.options.size || 'medium'}
                focusableSelectors={FOCUS_TRAP_SELECTOR.join(', ')}
                aria-label={this.props.dialog.title}
                greyBackground={this.props.dialog.options.hasGreyBackground}
                title={
                    <>
                        <Heading
                            divider={false}
                            subheader={this.props.dialog.subtitle}
                            mb="12px !important"
                            title={<DialogTitle dialog={this.props.dialog} onCloseDialog={this.closeDialog} />}
                        />
                        {!this.props.dialog.options.isDuplicate && (
                            <DialogHeader
                                dialog={this.props.dialog}
                                setSelectedSection={this.setSelectedSection}
                                selectedSection={this.getActiveSection()}
                            />
                        )}
                    </>
                }
                onCancel={this.closeDialog}
                className="e-dialog-normal"
                disableAutoFocus={true}
                disableEscKey={hasTableField}
                bespokeFocusTrap={hasTableField ? noop : undefined}
                contentPadding={this.props.dialog.type === 'page' ? { py: 2, px: 1 } : undefined}
            >
                <DialogContent
                    dialog={this.props.dialog}
                    activeSection={this.getActiveSection()}
                    defaultFocusRef={forwardRef}
                    noHeader={true}
                />
            </Dialog>
        );
    };

    renderStickerDialog = (): React.ReactNode => {
        const hasTableField = this.hasTableField();
        return (
            <Dialog
                className="e-dialog-sticker"
                key={this.props.dialog.dialogId}
                aria-label={this.props.dialog.title}
                open={true}
                showCloseIcon={true}
                size={this.props.dialog.options.size || 'small'}
                focusableSelectors={FOCUS_TRAP_SELECTOR.join(', ')}
                disableEscKey={hasTableField}
                bespokeFocusTrap={hasTableField ? noop : undefined}
                title={
                    <>
                        <Heading
                            divider={false}
                            subheader={this.props.dialog.subtitle}
                            mb="12px !important"
                            title={<DialogTitle dialog={this.props.dialog} onCloseDialog={this.closeDialog} />}
                        />
                        <DialogHeader
                            dialog={this.props.dialog}
                            setSelectedSection={this.setSelectedSection}
                            selectedSection={this.getActiveSection()}
                        />
                    </>
                }
                enableBackgroundUI={true}
                onCancel={this.closeDialog}
                disableAutoFocus={true}
            >
                <DialogContent dialog={this.props.dialog} activeSection={this.getActiveSection()} noHeader={true} />
            </Dialog>
        );
    };

    renderFullScreenStickerDialog = (): React.ReactNode => {
        const hasTableField = this.hasTableField();
        return (
            <DialogFullScreen
                className="e-dialog-sticker"
                disableAutoFocus={true}
                focusableSelectors={FOCUS_TRAP_SELECTOR.join(', ')}
                disableEscKey={hasTableField}
                bespokeFocusTrap={hasTableField ? noop : undefined}
                headerChildren={
                    <DialogHeader
                        dialog={this.props.dialog}
                        setSelectedSection={this.setSelectedSection}
                        selectedSection={this.getActiveSection()}
                    />
                }
                key={this.props.dialog.dialogId}
                onCancel={this.closeDialog}
                open={true}
                showCloseIcon={true}
                subtitle={this.props.dialog.subtitle}
                aria-label={this.props.dialog.title}
                title={<DialogTitle dialog={this.props.dialog} onCloseDialog={this.closeDialog} />}
            >
                <DialogContent
                    activeSection={this.getActiveSection()}
                    dialog={this.props.dialog}
                    isFullScreen={true}
                    noHeader={true}
                />
            </DialogFullScreen>
        );
    };

    renderFullScreenDialog = (): React.ReactNode => {
        const forwardRef = React.createRef();
        const hasTableField = this.hasTableField();
        const noHeader = !this.isFullScreenPageDialog();

        return (
            <DialogFullScreen
                disableAutoFocus={true}
                focusableSelectors={FOCUS_TRAP_SELECTOR.join(', ')}
                disableEscKey={hasTableField}
                bespokeFocusTrap={hasTableField ? noop : undefined}
                headerChildren={
                    <DialogHeader
                        dialog={this.props.dialog}
                        isMobileDialog={this.props.isMobileDialog}
                        isTabletDialog={this.props.isTabletDialog}
                        setSelectedSection={this.setSelectedSection}
                        selectedSection={this.getActiveSection()}
                    />
                }
                key={this.props.dialog.dialogId}
                onCancel={this.closeDialog}
                open={true}
                showCloseIcon={true}
                subtitle={this.props.dialog.subtitle}
                aria-label={this.props.dialog.title}
                disableContentPadding={this.props.dialog.options.isPaddingRemoved}
                title={<DialogTitle dialog={this.props.dialog} onCloseDialog={this.closeDialog} />}
            >
                <DialogContent
                    activeSection={this.getActiveSection()}
                    defaultFocusRef={forwardRef}
                    dialog={this.props.dialog}
                    isFullScreen={true}
                    noHeader={noHeader}
                />
            </DialogFullScreen>
        );
    };

    render(): React.ReactNode {
        if (
            this.props.dialog.type !== 'page' &&
            (
                (Array.isArray(this.props.dialog.content)
                    ? this.props.dialog.content
                    : [this.props.dialog.content]) as SectionControlObject[]
            )
                .filter(c => c !== undefined && c !== null)
                .filter(c => !c.isHidden).length === 0
        ) {
            return null;
        }
        if (this.props.isFullScreen || this.props.dialog.options.fullScreen) {
            return this.props.dialog.isSticker ? this.renderFullScreenStickerDialog() : this.renderFullScreenDialog();
        }
        if (this.props.dialog.isSticker) {
            return this.renderStickerDialog();
        }
        if (this.props.dialog.options.rightAligned) {
            return this.renderSidebarDialog();
        }

        return this.renderNormalDialog();
    }
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: DialogComponentExternalProps,
): DialogComponentProps => {
    const dialog = state.activeDialogs[props.dialogKey];

    return {
        ...props,
        closeDialog: xtremRedux.actions.actionStub,
        closeTableSidebar: xtremRedux.actions.actionStub,
        dialog,
        isMobileDialog: state.browser.lessThan.s,
        isTabletDialog: state.browser.greaterThan.xs && state.browser.lessThan.m,
        screenDefinitions: state.screenDefinitions,
        setActiveSection: xtremRedux.actions.actionStub,
    };
};

const mapDispatchToProps = (dispatch: xtremRedux.AppThunkDispatch): Partial<DialogComponentProps> => ({
    closeDialog: (dialogId: number): Promise<void> => dispatch(xtremRedux.actions.closeDialog(dialogId)),
    closeTableSidebar: (dialogId: number): void => {
        dispatch(xtremRedux.actions.closeTableSidebar(dialogId));
    },
    setActiveSection: (screenId: string, selectedSection: string) =>
        dispatch(xtremRedux.actions.setActiveSection(screenId, selectedSection)),
});

export const ConnectedDialogComponent = connect<DialogComponentProps, {}, DialogComponentExternalProps>(
    mapStateToProps,
    mapDispatchToProps,
)(DialogComponent);
