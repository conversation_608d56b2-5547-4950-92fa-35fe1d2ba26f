import {
    addBlockToState,
    addSectionToState,
    getMockPageDefinition,
    getMockState,
    getMockStickerDefinition,
    getMockStore,
} from '../../../../__tests__/test-helpers';

jest.mock('carbon-react/esm/components/textarea');
jest.mock('carbon-react/esm/components/date');
jest.mock('carbon-react/esm/components/date-range');
jest.mock('carbon-react/esm/components/checkbox');
jest.mock('carbon-react/esm/components/radio-button');
jest.mock('carbon-react/esm/components/form/form.component', () => (props: any) => props.children);
jest.mock('carbon-react/esm/components/action-popover');
jest.mock('carbon-react/esm/components/button-toggle/button-toggle-group');
jest.mock('carbon-react/esm/components/button-toggle/button-toggle.component');
jest.mock('carbon-react/esm/components/dialog');
jest.mock('carbon-react/esm/components/dialog-full-screen');
jest.mock('carbon-react/esm/components/form/form.style');
jest.mock('carbon-react/esm/components/heading');
jest.mock('carbon-react/esm/components/portrait');
jest.mock('carbon-react/esm/components/select');
jest.mock('carbon-react/esm/components/accordion');
jest.mock('carbon-react/esm/components/sidebar');
jest.mock('carbon-react/esm/components/toast');
jest.mock('carbon-react/esm/components/switch');
jest.mock(
    'carbon-react/esm/components/multi-action-button',
    () =>
        function MockMultiActionButton() {
            return <div id="mockMultiActionButton" />;
        },
);
jest.mock('../../../../component/ui/icon/status-icon-with-popover', () => () => null);
jest.mock(
    '../loading-dialog',
    () =>
        function MockLoadingDialog() {
            return <div id="mockLoadingDialog" />;
        },
);
jest.mock(
    '../../page/page-component',
    () =>
        function MockPageComponent() {
            return <div id="mockPageComponent" />;
        },
);
jest.mock('../../../../service/i18n-service');
jest.mock('../../../../service/dialog-service');
jest.mock('../../../../component/field/radio/radio-component', () => ({
    ConnectedRadioComponent: () => <div id="mockRadioButtons" />,
}));

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux';
import { DialogControl } from '../../../../service/dialog-service';
import type { CustomDialogOptions, DialogDescription, PageDialogOptions } from '../../../../types/dialogs';
import type { SectionControlObject } from '../../../control-objects';
import type { PageControlObject } from '../../page/page-control-object';
import { DialogBodyContent } from '../body/dialog-body-content';
import type { ScreenBaseDefinition } from '../../../../service/screen-base-definition';
import { render } from '@testing-library/react';

// Attention: We can not use snapshots in this test since Carbon Dialog is using React portals

interface TestContext {
    dialog: DialogDescription;
    dialogId: number;
    mockStore?: MockStoreEnhanced<XtremAppState>;
    screenDefinition: ScreenBaseDefinition;
    screenId: string;
    state?: XtremAppState;
}

const getTestPageContext = (useStore = false, options: PageDialogOptions | CustomDialogOptions = {}): TestContext => {
    const screenId = 'TestPage';
    const dialog: DialogDescription = {
        isSticker: false,
        dialogId: 1,
        buttons: {
            accept: {
                text: 'Ok',
                onClick: jest.fn(),
            },
            cancel: {
                text: 'Cancel',
                onClick: jest.fn(),
            },
        },
        content: 'Dialog text',
        level: 'info',
        title: 'Dialog title',
        screenId,
        dialogControl: new DialogControl({
            dialogId: 1,
            screenId: 'screenId',
            level: 'info',
            title: 'Title',
            content: 'content',
            getButtons: jest.fn(),
            isDirtyCheck: false,
        }),
        options,
    };

    const testContext: TestContext = {
        dialog,
        dialogId: 1,
        screenDefinition: getMockPageDefinition(screenId),
        screenId,
    };

    if (useStore) {
        testContext.state = getMockState({
            screenDefinitions: {
                [screenId]: testContext.screenDefinition,
            },
            activeDialogs: {
                [testContext.dialogId]: dialog,
            },
        });
        testContext.mockStore = getMockStore(testContext.state);
    }

    return testContext;
};

const getTestStickerContext = (useStore = false): TestContext => {
    const screenId = 'TestSticker';
    const dialog: DialogDescription = {
        isSticker: true,
        dialogId: 1,
        content: 'Dialog text',
        level: 'info',
        title: 'Dialog title',
        screenId,
        buttons: {},
        dialogControl: new DialogControl({
            dialogId: 1,
            screenId: 'screenId',
            level: 'info',
            title: 'Title',
            content: 'content',
            getButtons: jest.fn(),
            isDirtyCheck: false,
        }),
        options: {},
    };

    const testContext: TestContext = {
        dialog,
        dialogId: 1,
        screenDefinition: getMockStickerDefinition(screenId),
        screenId,
    };

    if (useStore) {
        testContext.state = getMockState({
            screenDefinitions: {
                [screenId]: testContext.screenDefinition,
            },
            activeDialogs: {
                [testContext.dialogId]: dialog,
            },
        });
        testContext.mockStore = getMockStore(testContext.state);
    }

    return testContext;
};

describe('Dialog component', () => {
    afterEach(() => jest.resetAllMocks());

    it('should render DialogErrorContent', () => {
        const testContext = getTestPageContext();
        testContext.dialog.content = new Error('Simulated error');

        const { queryByTestId } = render(
            <DialogBodyContent
                dialog={testContext.dialog}
                screenDefinition={testContext.screenDefinition}
                availableColumns={12}
                isFirstSection={false}
                isLastSection={false}
                onStepOneSection={jest.fn()}
            />,
        );
        expect(queryByTestId('e-dialog-error-content')).not.toBeNull();
    });

    it('should render PageCustomContent', () => {
        const testContext = getTestPageContext(true);

        testContext.dialog.content = testContext.screenDefinition.metadata.controlObjects[
            testContext.screenId
        ] as PageControlObject;
        testContext.dialog.type = 'page';

        const { container } = render(
            <Provider store={testContext.mockStore!}>
                <DialogBodyContent
                    dialog={testContext.dialog}
                    screenDefinition={testContext.screenDefinition}
                    availableColumns={12}
                    isFirstSection={false}
                    isLastSection={false}
                    onStepOneSection={jest.fn()}
                />
            </Provider>,
        );

        expect(container.querySelector('#mockPageComponent')).not.toBeNull();
    });

    it('should render DialogCustomContent', () => {
        const testContext = getTestPageContext(true);
        const block1 = addBlockToState(testContext.state!, testContext.screenId, 'block1', {
            title: 'Block 1',
            parent() {
                return testContext.state!.screenDefinitions[testContext.screenId].metadata.controlObjects
                    .testSection as SectionControlObject<any>;
            },
        });
        addSectionToState(
            testContext.state!,
            testContext.screenId,
            'testSection',
            {
                title: 'MySection',
            },
            [block1],
        );
        const screenDefinition = testContext.state!.screenDefinitions[testContext.screenId];
        testContext.dialog.content = screenDefinition.metadata.controlObjects.testSection as SectionControlObject<any>;

        const { container } = render(
            <Provider store={testContext.mockStore!}>
                <DialogBodyContent
                    dialog={testContext.dialog}
                    screenDefinition={testContext.screenDefinition}
                    availableColumns={12}
                    isFirstSection={false}
                    isLastSection={false}
                    onStepOneSection={jest.fn()}
                />
            </Provider>,
        );

        expect(container.querySelector('.e-dialog-custom-body')).not.toBeNull();
    });

    it('should render a sticker dialog', () => {
        const testContext = getTestStickerContext(true);
        const block1 = addBlockToState(testContext.state!, testContext.screenId, 'block1', {
            title: 'Block 1',
            parent() {
                return testContext.state!.screenDefinitions[testContext.screenId].metadata.controlObjects
                    .testSection as SectionControlObject<any>;
            },
        });
        addSectionToState(
            testContext.state!,
            testContext.screenId,
            'testSection',
            {
                title: 'MySection',
            },
            [block1],
        );
        const screenDefinition = testContext.state!.screenDefinitions[testContext.screenId];
        testContext.dialog.content = screenDefinition.metadata.controlObjects.testSection as SectionControlObject<any>;

        const { container } = render(
            <Provider store={testContext.mockStore!}>
                <DialogBodyContent
                    dialog={testContext.dialog}
                    screenDefinition={testContext.screenDefinition}
                    availableColumns={12}
                    isFirstSection={false}
                    isLastSection={false}
                    onStepOneSection={jest.fn()}
                />
            </Provider>,
        );
        expect(container.querySelector('.e-dialog-custom-body')).not.toBeNull();
    });
});
