// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Dialog alert component snapshots should render the error message 1`] = `
.c0 {
  max-width: -webkit-fit-content;
  max-width: -moz-fit-content;
  max-width: fit-content;
  box-shadow: 0 var(--spacing050) 0 0 var(--colorsUtilityYin090);
  border-bottom-left-radius: var(--borderRadius025);
  border-bottom-right-radius: var(--borderRadius025);
}

.c0 > a,
.c0 > button {
  font-size: var(--fontSizes100);
  color: var(--colorsActionMajor500);
}

.c0 > a:hover,
.c0 > button:hover {
  color: var(--colorsActionMajor600);
}

.c0 > a:focus,
.c0 > button:focus {
  background-color: var(--colorsSemanticFocus250);
  border-radius: var(--borderRadius025);
}

.c0 > a:any-link:hover,
.c0 > button:hover {
  cursor: pointer;
}

.c0 > a,
.c0 > button {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.c0 > a:focus,
.c0 > button:focus {
  color: var(--colorsActionMajorYin090);
  outline: none;
}

.c0 > a,
.c0 > button {
  outline: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-bottom-left-radius: var(--borderRadius000);
  border-bottom-right-radius: var(--borderRadius000);
}

<div>
  <div
    class="e-dialog-error-content"
    data-testid="e-dialog-error-content"
  >
    <p>
      Test error
    </p>
    <div
      class="e-dialog-error-header"
    >
      <span
        class="c0"
        data-component="link"
      >
        <button
          type="button"
        >
          <span
            class=""
          >
            Show technical details
          </span>
        </button>
      </span>
    </div>
  </div>
</div>
`;
