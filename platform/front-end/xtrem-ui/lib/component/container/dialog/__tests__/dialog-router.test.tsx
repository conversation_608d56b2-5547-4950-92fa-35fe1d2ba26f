import { DialogRouter } from '../dialog-router';

jest.mock('../dialog-component', () => ({
    ConnectedDialogComponent: () => <div className="mockLoadingDialog" role="dialog" />,
}));
import * as React from 'react';
import { render } from '@testing-library/react';
import type { ReduxResponsive } from '../../../../redux/state';
import type { DialogDescription } from '../../../../types/dialogs';
import type { Dict } from '@sage/xtrem-shared';
import { DialogControl } from '../../../../service/dialog-service';

describe('Dialog rooter', () => {
    let browser: ReduxResponsive;
    beforeEach(() => {
        browser = {
            greaterThan: {
                s: true,
            },
        } as ReduxResponsive;
    });

    it('should not render if no dialogs are open', () => {
        const rendered = render(<DialogRouter activeDialogs={{}} browser={browser} />);
        expect(rendered.container.firstChild).toEqual(null);
    });

    it('should render each dialogs', () => {
        const screenId = 'TestScreen';

        const dialogs: Dict<DialogDescription> = {
            dialog1: {
                isSticker: false,
                dialogId: 1,
                buttons: {
                    accept: {
                        text: 'Ok',
                        onClick: jest.fn(),
                    },
                    cancel: {
                        text: 'Cancel',
                        onClick: jest.fn(),
                    },
                },
                content: 'Dialog text 1 ',
                level: 'info',
                title: 'Dialog title 1',
                screenId,
                dialogControl: new DialogControl({
                    dialogId: 1,
                    screenId: 'screenId',
                    level: 'info',
                    title: 'Title 1 ',
                    content: 'content',
                    getButtons: jest.fn(),
                    isDirtyCheck: false,
                    isSticker: false,
                }),
                options: {},
            },
            dialog2: {
                isSticker: false,
                dialogId: 2,
                buttons: {
                    accept: {
                        text: 'Ok',
                        onClick: jest.fn(),
                    },
                    cancel: {
                        text: 'Cancel',
                        onClick: jest.fn(),
                    },
                },
                content: 'Dialog text 2 ',
                level: 'info',
                title: 'Dialog title 2',
                screenId,
                dialogControl: new DialogControl({
                    dialogId: 1,
                    screenId: 'screenId',
                    level: 'info',
                    title: 'Title 2',
                    content: 'content',
                    getButtons: jest.fn(),
                    isDirtyCheck: false,
                    isSticker: false,
                }),
                options: {},
            },
        };
        const rendered = render(<DialogRouter activeDialogs={dialogs} browser={browser} />);
        expect(rendered.getAllByRole('dialog').length).toEqual(2);
    });
});
