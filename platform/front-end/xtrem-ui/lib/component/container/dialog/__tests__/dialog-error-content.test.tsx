import * as React from 'react';
import * as domUtils from '../../../../utils/dom';
import { DialogErrorContent } from '../dialog-error-content';
import { fireEvent, render } from '@testing-library/react';

describe('Dialog alert component', () => {
    let content: Error;
    let copySpy: jest.SpyInstance<void, [string]>;

    beforeEach(() => {
        content = new Error('Test error');
        copySpy = jest.spyOn(domUtils, 'copyToClipboard').mockImplementation(() => {});
    });

    afterEach(() => {
        copySpy!.mockRestore();
    });

    describe('snapshots', () => {
        it('should render the error message', () => {
            const { container } = render(<DialogErrorContent content={content} />);
            expect(container).toMatchSnapshot();
        });
    });

    describe('interactions', () => {
        it('should show the error stack when click on the link', () => {
            const { container, queryByTestId } = render(<DialogErrorContent content={content} />);
            expect(container.querySelector('pre')).toBeNull();
            expect(queryByTestId('e-dialog-error-copy')).toBeNull();

            fireEvent.click(container.querySelector('button')!);

            expect(container.querySelector('pre')).not.toBeNull();
            expect(queryByTestId('e-dialog-error-copy')).not.toBeNull();
        });

        it('should copy the stack when clicking on the link', async () => {
            const { container, queryByTestId } = render(<DialogErrorContent content={content} />);

            fireEvent.click(container.querySelector('button')!);

            const copyButton = queryByTestId('e-dialog-error-copy')!;
            expect(copySpy).not.toHaveBeenCalled();
            fireEvent.click(copyButton);
            expect(copySpy).toHaveBeenCalledTimes(1);
            expect(copySpy!.mock.calls[0][0]).toContain('Error: Test error');
        });
    });
});
