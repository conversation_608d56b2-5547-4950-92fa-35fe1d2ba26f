import {
    addBlockToState,
    addPageControlObject,
    addSectionToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';

import { render, waitFor } from '@testing-library/react';
import * as React from 'react';
import { DialogHeader } from '../dialog-header';
import type * as xtremRedux from '../../../../redux';
import type { SectionControlObject } from '../../section/section-control-object';
import type { CustomDialogOptions, DialogDescription, PageDialogOptions } from '../../../../types/dialogs';
import { Provider } from 'react-redux';
import type { PageControlObject } from '../../container-control-objects';

describe('dialog header', () => {
    let state: xtremRedux.XtremAppState;
    let dialog: DialogDescription<PageDialogOptions & CustomDialogOptions>;
    const screenId = 'TestPage';

    beforeEach(() => {
        state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);

        const block1 = addBlockToState(state, screenId, 'block1', {
            title: 'Block 1',
            parent() {
                return state.screenDefinitions[screenId].metadata.controlObjects
                    .testSection1 as SectionControlObject<any>;
            },
        });

        addSectionToState(
            state,
            screenId,
            'testSection1',
            {
                title: 'My Section',
            },
            [block1],
        );

        const block2 = addBlockToState(state, screenId, 'block2', {
            title: 'Block 2',
            parent() {
                return state.screenDefinitions[screenId].metadata.controlObjects
                    .testSection2 as SectionControlObject<any>;
            },
        });

        addSectionToState(
            state,
            screenId,
            'testSection2',
            {
                title: 'My Other Section',
            },
            [block2],
        );

        dialog = {
            screenId,
            type: 'custom',
            level: 'info',
            isSticker: false,
            buttons: {},
            dialogId: 4,
            options: {},
            dialogControl: {} as any,
            content: null,
        };

        addPageControlObject(state, screenId, {});
        getMockStore(state);
    });

    describe('custom dialog', () => {
        beforeEach(() => {
            dialog.type = 'custom';
        });

        it('should not render if it is a custom dialog and has a single section content', () => {
            dialog.content = state.screenDefinitions[screenId].metadata.controlObjects
                .testSection1 as SectionControlObject;
            const { container } = render(
                <Provider store={getMockStore(state)}>
                    <DialogHeader dialog={dialog} setSelectedSection={jest.fn()} />
                </Provider>,
            );
            expect(container.childElementCount).toEqual(0);
        });

        it('should display the tabs if a custom dialog is used with two sections', async () => {
            dialog.content = [
                state.screenDefinitions[screenId].metadata.controlObjects.testSection1 as SectionControlObject,
                state.screenDefinitions[screenId].metadata.controlObjects.testSection2 as SectionControlObject,
            ];

            const { queryByTestId } = render(
                <Provider store={getMockStore(state)}>
                    <DialogHeader dialog={dialog} setSelectedSection={jest.fn()} />
                </Provider>,
            );

            await waitFor(() => {
                expect(queryByTestId('e-xtrem-tab-mySection', { exact: false })).not.toBeNull();
                expect(queryByTestId('e-xtrem-tab-myOtherSection', { exact: false })).not.toBeNull();
            });
        });
    });

    describe('page dialog', () => {
        beforeEach(() => {
            dialog.type = 'page';
        });

        it('should render the tabs if the page is in default mode', async () => {
            addPageControlObject(state, screenId, { mode: 'default' });
            dialog.content = state.screenDefinitions[screenId].metadata.controlObjects[screenId] as PageControlObject;
            const { queryByTestId } = render(
                <Provider store={getMockStore(state)}>
                    <DialogHeader dialog={dialog} setSelectedSection={jest.fn()} />
                </Provider>,
            );

            await waitFor(() => {
                expect(queryByTestId('e-xtrem-tab-mySection', { exact: false })).toBeNull();
                expect(queryByTestId('e-xtrem-tab-myOtherSection', { exact: false })).toBeNull();
            });
        });

        it('should render the tabs if there are more than two sections in the page', async () => {
            addPageControlObject(state, screenId, { mode: 'tabs' });
            dialog.content = state.screenDefinitions[screenId].metadata.controlObjects[screenId] as PageControlObject;
            const { queryByTestId } = render(
                <Provider store={getMockStore(state)}>
                    <DialogHeader dialog={dialog} setSelectedSection={jest.fn()} />
                </Provider>,
            );

            await waitFor(() => {
                expect(queryByTestId('e-xtrem-tab-mySection', { exact: false })).not.toBeNull();
                expect(queryByTestId('e-xtrem-tab-myOtherSection', { exact: false })).not.toBeNull();
            });
        });

        it('should render the wizards steps if the page in wizard mode', async () => {
            addPageControlObject(state, screenId, { mode: 'wizard' });
            dialog.content = state.screenDefinitions[screenId].metadata.controlObjects[screenId] as PageControlObject;
            const { container } = render(
                <Provider store={getMockStore(state)}>
                    <DialogHeader dialog={dialog} setSelectedSection={jest.fn()} />
                </Provider>,
            );

            await waitFor(() => {
                const tabs = container.querySelectorAll('[data-component="step-sequence-item"]');
                expect(tabs).toHaveLength(2);
                expect(tabs[0].textContent!.trim()).toEqual('1My Section');
                expect(tabs[1].textContent!.trim()).toEqual('2My Other Section');
            });
        });
    });
});
