import { getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import * as React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux';
import { DialogBreadcrumbs } from '../dialog-breadcrumbs';
import { QUERY_PARAM_TUNNEL_SEGMENTS } from '../../../../utils/constants';
import type { TunnelSegment } from '../../../../redux/actions';
import * as xtremRedux from '../../../../redux';

describe('Dialog breadcrumbs', () => {
    let mockStore: MockStoreEnhanced<XtremAppState>;
    const screenId: string = 'TestPage';
    const tunnelPageId: string = 'TunnelTestPage';

    beforeEach(() => {
        jest.useFakeTimers();
        const state = getMockState();
        const tunnelPageDefinition = getMockPageDefinition(tunnelPageId);
        tunnelPageDefinition.queryParameters[QUERY_PARAM_TUNNEL_SEGMENTS] = [
            { label: 'Root page', screenId },
            { label: 'Tunnel page', screenId: tunnelPageId },
        ] as TunnelSegment[] as any;
        state.screenDefinitions[tunnelPageId] = tunnelPageDefinition;
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        mockStore = getMockStore(state);
    });

    it('should trigger the close tunnel action with the right screen id', () => {
        const { getByText } = render(
            <Provider store={mockStore}>
                <DialogBreadcrumbs screenId={tunnelPageId} />
            </Provider>,
        );

        expect(xtremRedux.actions.closeTunnelByBreadcrumbLink).not.toHaveBeenCalled();

        fireEvent.click(getByText('Root page'));

        expect(xtremRedux.actions.closeTunnelByBreadcrumbLink).toHaveBeenCalledWith(screenId, tunnelPageId);
    });

    it('should not crash if it is opened with a page with no breadcrumbs', () => {
        render(
            <Provider store={mockStore}>
                <DialogBreadcrumbs screenId={screenId} />
            </Provider>,
        );
    });
});
