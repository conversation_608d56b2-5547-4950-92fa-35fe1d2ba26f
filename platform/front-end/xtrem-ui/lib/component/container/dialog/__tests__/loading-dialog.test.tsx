import { LoadingDialog } from '../loading-dialog';
import * as React from 'react';
import { render, waitFor } from '@testing-library/react';

describe('Loading Dialog', () => {
    beforeEach(() => {
        jest.useFakeTimers();
    });

    it('should not render by default', () => {
        const rendered = render(<LoadingDialog />);
        expect(rendered.container.firstChild).toEqual(null);
    });

    it('should render after 500ms', async () => {
        const rendered = render(<LoadingDialog />);
        expect(rendered.container.firstChild).toEqual(null);
        jest.advanceTimersByTime(600);
        await waitFor(() => {
            expect(rendered.container.firstChild).not.toEqual(null);
        });
    });
});
