import type { CustomDialogOptions, DialogDescription, PageDialogOptions } from '../../../types/dialogs';
import { useSelector } from 'react-redux';
import type { XtremAppState } from '../../../redux';
import { getPageDefinition, getPagePropertiesFromPageDefinition } from '../../../utils/state-utils';
import { getPageTitlesFromPageDefinition } from '../../../utils/page-utils';
import Typography from 'carbon-react/esm/components/typography';
import * as React from 'react';
import { DialogBreadcrumbs } from './dialog-breadcrumbs';
import { QUERY_PARAM_TUNNEL_SEGMENTS } from '../../../utils/constants';
import IconButton from 'carbon-react/esm/components/icon-button';
import Icon from 'carbon-react/esm/components/icon';
import { getScreenElement } from '../../../service/screen-base-definition';
import type { LabelControlObject } from '../../field/field-control-objects';
import FieldWrapper from '../../../render/field-wrapper';
import { ContextType } from '../../../types';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

export interface DialogTitleProps {
    dialog: DialogDescription<PageDialogOptions & CustomDialogOptions>;
    baseBottomMargin?: string;
    baseTopMargin?: string;
    onCloseDialog: () => Promise<void>;
}

// eslint-disable-next-line react/function-component-definition
export const PageDialogTitle: React.FC<DialogTitleProps> = ({
    dialog,
    baseBottomMargin = '0px',
    baseTopMargin = '12px',
    onCloseDialog,
}: DialogTitleProps) => {
    const screenTitle = useSelector<XtremAppState, string | null>(state => {
        if (dialog.type === 'page' && dialog.screenId) {
            const pageDefinition = getPageDefinition(dialog.screenId, state);
            if (!pageDefinition) {
                return null;
            }
            const locale = state.applicationContext?.locale || 'en-US';
            const { title } = getPageTitlesFromPageDefinition(pageDefinition, locale);
            return title || null;
        }
        return null;
    });

    const isTunnelDialog = useSelector<XtremAppState, boolean>(state => {
        if (dialog.type === 'page' && dialog.screenId) {
            const pageDefinition = getPageDefinition(dialog.screenId, state);
            if (!pageDefinition) {
                return false;
            }
            const breadcrumbs = pageDefinition.queryParameters[QUERY_PARAM_TUNNEL_SEGMENTS];
            if (breadcrumbs && Array.isArray(breadcrumbs)) {
                return true;
            }
        }
        return false;
    });

    const headerLabelField = useDeepEqualSelector<XtremAppState, LabelControlObject | null>(state => {
        if (dialog.type !== 'page' || !dialog.screenId) {
            return null;
        }
        const pageDefinition = getPageDefinition(dialog.screenId, state);
        if (!pageDefinition) {
            return null;
        }
        const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);
        return pageProperties.headerLabel?.apply(getScreenElement(pageDefinition)) || null;
    });

    return (
        <>
            {isTunnelDialog && <DialogBreadcrumbs screenId={dialog.screenId} />}
            <div data-component="heading">
                <div data-element="header-container">
                    <Typography
                        data-element="title"
                        variant="h1"
                        mt={isTunnelDialog ? '18px' : baseTopMargin}
                        mb={baseBottomMargin}
                    >
                        {isTunnelDialog && (
                            <span className="e-dialog-title-back-button-wrapper">
                                <IconButton aria-label="icon-button" mr="24px" onClick={onCloseDialog}>
                                    <Icon type="arrow_left" />
                                </IconButton>
                            </span>
                        )}
                        {dialog.title || screenTitle || null}
                        {headerLabelField && dialog.screenId && (
                            <div className="e-header-title-label">
                                <FieldWrapper
                                    item={{ $bind: headerLabelField.id }}
                                    screenId={dialog.screenId}
                                    contextType={ContextType.pageHeader}
                                />
                            </div>
                        )}
                    </Typography>
                </div>
            </div>
        </>
    );
};
