jest.mock('carbon-react/esm/components/action-popover', () => ({
    ActionPopover: (props: any) => (
        <div key="mockActionPopover" data-testid="mockActionPopover" onClick={props.onOpen}>
            {props.children}
        </div>
    ),
    ActionPopoverItem: (props: any) => (
        <button
            type="button"
            data-testid="mockActionPopoverItem"
            defaultValue={props.icon}
            key={props.key}
            onClick={props.onClick}
            aria-disabled={props.disabled}
            disabled={props.disabled}
        >
            {props.children}
        </button>
    ),
    ActionPopoverDivider: () => <div key="mockActionPopoverDivider" data-testid="mockActionPopoverDivider" />,
}));

import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    userEvent,
} from '../../../../../__tests__/test-helpers';

import React from 'react';
import { render, waitFor } from '@testing-library/react';
import { FieldKey } from '../../../../types';
import { CollectionFieldTypes } from '../../../../../service/collection-data-types';
import { ThemeProvider } from 'styled-components';
import baseTheme from 'carbon-react/esm/style/themes/base';
import { Provider } from 'react-redux';
import { TableSidebarDialogTitle } from '../table-sidebar-dialog-title';
import { CollectionValue } from '../../../../../service/collection-data-service';
import type { TableDecoratorProperties } from '../../../../decorators';
import { nestedFields } from '../../../../..';
import type { ScreenBase } from '../../../../../service/screen-base';
import type { XtremAppState } from '../../../../../redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as events from '../../../../../utils/events';
import type { CollectionItemAction } from '../../../../ui/table-shared/table-dropdown-actions/table-dropdown-action-types';
import '@testing-library/jest-dom';

jest.useFakeTimers();

describe('Table sidebar header', () => {
    const screenId = 'TestPage';
    const elementId = 'testField';

    let state: XtremAppState;
    let store: MockStoreEnhanced<XtremAppState>;
    let defaultProperties: TableDecoratorProperties;
    let columns: nestedFields.NestedField<ScreenBase, nestedFields.NestedFieldTypes, any>[];
    let defaultValue: any[];
    let collectionValue: CollectionValue;
    let triggerHandledEventMock: jest.MockInstance<any, any>;

    const renderComponent = () => {
        collectionValue = new CollectionValue<any>({
            screenId,
            elementId,
            isTransient: false,
            hasNextPage: false,
            orderBy: [],
            columnDefinitions: [columns],
            nodeTypes: {
                AnyNode: {
                    name: 'AnyNode',
                    title: 'AnyNode',
                    packageName: '@sage/xtrem-test',
                    properties: {},
                    mutations: {},
                },
            },
            nodes: ['@sage/xtrem-test/AnyNode'],
            filter: [{}],
            initialValues: defaultValue,
            fieldType: CollectionFieldTypes.DESKTOP_TABLE,
            locale: 'en-US',
        });
        collectionValue!.startRecordTransaction({ recordId: '1' });
        addFieldToState(FieldKey.Table, state, screenId, elementId, defaultProperties, collectionValue);

        store = getMockStore(state);
        return render(
            <ThemeProvider theme={baseTheme}>
                <Provider store={store!}>
                    <TableSidebarDialogTitle dialog={state.activeDialogs[1]} />
                </Provider>
            </ThemeProvider>,
        );
    };

    beforeEach(() => {
        triggerHandledEventMock = jest.spyOn(events, 'triggerHandledEvent').mockImplementation(jest.fn());
        const sidebarDefinition = {
            layout: {
                mainSection: {
                    title: 'Section title',
                    blocks: {
                        mainBlock: {
                            fields: ['field1, field2'],
                            title: 'Test block',
                        },
                    },
                },
            },
        };
        columns = [
            nestedFields.text({
                bind: 'field1',
                title: 'Test text field',
            }),
            nestedFields.numeric({
                bind: 'field2',
                title: 'Test numeric field',
                scale: 2,
            }),
            nestedFields.date({
                bind: { nested: { binding: true } },
                title: 'Test date field',
            }),
        ];
        defaultProperties = {
            title: 'test title',
            node: '@sage/xtrem-test/AnyNode',
            sidebar: sidebarDefinition,
            columns,
        } as TableDecoratorProperties;

        defaultValue = [
            {
                _id: '1',
                field1: 'Value 1',
                field2: '2.34',
                titleBoundField: 'Hi',
                nested: {
                    binding: '2023-03-03',
                },
            },
            {
                _id: '2',
                field1: 'Value 2',
                field2: '3.45',
                titleBoundField: 'Hola',
                nested: {
                    binding: '2022-02-02',
                },
            },
        ];
        state = getMockState();
        state.activeDialogs = {
            1: {
                buttons: {},
                dialogId: 1,
                level: 'info',
                options: {},
                screenId,
                isSticker: false,
                type: 'table-sidebar',
                dialogControl: {
                    resolve: jest.fn(),
                    reject: jest.fn(),
                    id: 1,
                } as any,
                content: {
                    recordId: '1',
                    isNewRecord: false,
                    nextRecordId: undefined,
                    prevRecordId: undefined,
                    columns,
                    sidebarDefinition,
                    screenId,
                    elementId,
                },
            },
        };
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
    });

    afterEach(() => {
        triggerHandledEventMock.mockReset();
    });

    describe('quick actions', () => {
        let quickActions: Array<CollectionItemAction<any>>;
        beforeEach(() => {
            quickActions = [
                {
                    onClick: jest.fn(),
                    title: 'Normal action',
                    icon: 'add',
                },
                {
                    onClick: jest.fn(),
                    title: 'Hidden action',
                    isHidden: () => true,
                    icon: 'add',
                },
                {
                    onClick: jest.fn(),
                    title: 'Disabled action',
                    isDisabled: () => true,
                    icon: 'add',
                },
            ];
            defaultProperties.sidebar!.headerQuickActions = quickActions;
        });

        it('should render the actions except the hidden one', () => {
            const { queryByTestId } = renderComponent();
            const quickActionContainer = queryByTestId('e-sidebar-quick-action-container');
            expect(quickActionContainer!.childElementCount).toEqual(2);
            expect(quickActionContainer!.children[0].attributes.getNamedItem('aria-label')).toHaveTextContent(
                'Normal action',
            );
            expect(quickActionContainer!.children[0].attributes.getNamedItem('disabled')).toBeNull();
            expect(quickActionContainer!.children[1].attributes.getNamedItem('aria-label')).toHaveTextContent(
                'Disabled action',
            );
            expect(quickActionContainer!.children[1].attributes.getNamedItem('disabled')).not.toBeNull();
        });

        it('should not render the container if no quick actions defined', () => {
            defaultProperties.sidebar!.headerQuickActions = undefined;

            const { queryByTestId } = renderComponent();
            const quickActionContainer = queryByTestId('e-sidebar-quick-action-container');
            expect(quickActionContainer).toBeNull();
        });

        it('should trigger the event listener when an enabled action is clicked', async () => {
            const { queryByTestId } = renderComponent();
            expect(triggerHandledEventMock).not.toHaveBeenCalled();
            const normalButton = queryByTestId('e-sidebar-quick-action-label-normalAction');

            await userEvent.click(normalButton!);

            await waitFor(() => {
                expect(triggerHandledEventMock).toHaveBeenCalledWith(
                    screenId,
                    elementId,
                    { onClick: quickActions[0].onClick, onError: undefined },
                    '1',
                    {
                        _id: '1',
                        field1: 'Value 1',
                        field2: 2.34,
                        nested: { binding: '2023-03-03' },
                        titleBoundField: 'Hi',
                    },
                );
            });
        });

        it('should not trigger the event listener when a disabled action is clicked', async () => {
            const { queryByTestId } = renderComponent();
            expect(triggerHandledEventMock).not.toHaveBeenCalled();
            const disabledButton = queryByTestId('e-sidebar-quick-action-label-disabledAction');

            await userEvent.click(disabledButton!);

            expect(triggerHandledEventMock).not.toHaveBeenCalled();
        });
    });

    describe('dropdown actions', () => {
        let dropdownActions: Array<CollectionItemAction<any>>;
        beforeEach(() => {
            dropdownActions = [
                {
                    onClick: jest.fn(),
                    title: 'Normal action',
                    icon: 'add',
                },
                {
                    onClick: jest.fn(),
                    title: 'Hidden action',
                    isHidden: () => true,
                    icon: 'add',
                },
                {
                    onClick: jest.fn(),
                    title: 'Disabled action',
                    isDisabled: () => true,
                    icon: 'add',
                },
            ];
            defaultProperties.sidebar!.headerDropdownActions = dropdownActions;
        });

        it('should render the actions except the hidden one', () => {
            const { queryAllByTestId } = renderComponent();
            const dropdownActionElements = queryAllByTestId('mockActionPopoverItem');
            expect(dropdownActionElements.length).toEqual(2);
            expect(dropdownActionElements[0]).toHaveTextContent('Normal action');
            expect(dropdownActionElements[0]).toHaveAttribute('aria-disabled', 'false');
            expect(dropdownActionElements[1]).toHaveTextContent('Disabled action');
            expect(dropdownActionElements[1]).toHaveAttribute('aria-disabled', 'true');
        });

        it('should not render the container if no quick actions defined', () => {
            defaultProperties.sidebar!.headerDropdownActions = undefined;

            const { queryByTestId } = renderComponent();
            const quickActionContainer = queryByTestId('e-sidebar-dropdown-action-container');
            expect(quickActionContainer).toBeNull();
        });

        it('should trigger the event listener when an enabled action is clicked', async () => {
            const { queryAllByTestId } = renderComponent();
            expect(triggerHandledEventMock).not.toHaveBeenCalled();
            const dropdownActionElements = queryAllByTestId('mockActionPopoverItem');

            await userEvent.click(dropdownActionElements[0]);

            await waitFor(() => {
                expect(triggerHandledEventMock).toHaveBeenCalledWith(
                    screenId,
                    elementId,
                    { onClick: dropdownActions[0].onClick, onError: undefined },
                    '1',
                    {
                        _id: '1',
                        field1: 'Value 1',
                        field2: 2.34,
                        nested: { binding: '2023-03-03' },
                        titleBoundField: 'Hi',
                    },
                );
            });
        });

        it('should not trigger the event listener when a disabled action is clicked', async () => {
            const { queryAllByTestId } = renderComponent();
            const dropdownActionElements = queryAllByTestId('mockActionPopoverItem');

            await userEvent.click(dropdownActionElements[1]);

            expect(triggerHandledEventMock).not.toHaveBeenCalled();
        });
    });
});
