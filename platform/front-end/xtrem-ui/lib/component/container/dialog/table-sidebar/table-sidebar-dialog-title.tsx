import * as React from 'react';
import type { DialogDescription, TableSidebarDialogContent } from '../../../../types/dialogs';
import NavigationArrows from '../../../ui/navigation-arrows/navigation-arrows';
import Typography from 'carbon-react/esm/components/typography';
import Icon from 'carbon-react/esm/components/icon';
import { camelCase } from 'lodash';
import XtremActionPopover from '../../../ui/xtrem-action-popover';
import * as tokens from '@sage/design-tokens/js/base/common';
import IconButton from 'carbon-react/esm/components/icon-button';
import { useDispatch } from 'react-redux';
import * as xtremRedux from '../../../../redux';
import { calculateActionMenu, calculateActionMenuWithSeparator } from '../../../../utils/action-menu-utils';
import type { AccessBindings } from '../../../../service/page-definition';
import type { OnTelemetryEventFunction } from '../../../../redux/state';
import { triggerHandledEvent } from '../../../../utils/events';
import { cleanMetadataFromRecord, splitValueToMergedValue } from '../../../../utils/transformers';
import { useCollectionItemRecord } from '../../../connected-collection';
import { useDeepEqualSelector } from '../../../../utils/hooks/use-deep-equal-selector';

export function TableSidebarDialogTitle({ dialog }: { dialog: DialogDescription }): React.ReactElement | string {
    const content = dialog.content as TableSidebarDialogContent;
    const dialogId = dialog.dialogId;
    const screenId = dialog.screenId;
    const isLoading = !content.recordId;

    if (!screenId) {
        throw new Error('Screen ID is missing from dialog description.');
    }

    const dispatch = useDispatch() as xtremRedux.AppThunkDispatch;

    const accessBindings = useDeepEqualSelector<xtremRedux.XtremAppState, AccessBindings>(
        (s: xtremRedux.XtremAppState) => s.screenDefinitions[screenId].accessBindings,
    );
    const onTelemetryEvent = useDeepEqualSelector<xtremRedux.XtremAppState, OnTelemetryEventFunction | undefined>(
        (s: xtremRedux.XtremAppState) => s.applicationContext?.onTelemetryEvent,
    );

    const onNextRecord = React.useCallback(() => {
        return dispatch(xtremRedux.actions.selectSidebarNextRecord(dialogId));
    }, [dialogId, dispatch]);

    const onPrevRecord = React.useCallback(() => {
        return dispatch(xtremRedux.actions.selectSidebarPreviousRecord(dialogId));
    }, [dialogId, dispatch]);

    const recordValue = useCollectionItemRecord({
        elementId: content.elementId,
        screenId,
        isUncommitted: true,
        recordId: content.recordId || '',
    });
    const rowValue = splitValueToMergedValue(recordValue);

    const resolvedPopoverItems = React.useMemo(
        () =>
            calculateActionMenuWithSeparator({
                accessBindings,
                actions: content.sidebarDefinition.headerDropdownActions,
                actionType: 'table-sidebar-header-dropdown-action',
                onTriggerMenuItem: (context, onClick, onActionError) => {
                    onTelemetryEvent?.(`tableSidebarHeaderDropdownActionTriggered-${context.uniqueId}`, {
                        screenId,
                        elementId: content.elementId,
                        id: context.id,
                        uniqueId: context.uniqueId,
                    });

                    triggerHandledEvent(
                        screenId,
                        content.elementId,
                        { onClick, onError: onActionError },
                        recordValue?._id || content.recordId,
                        cleanMetadataFromRecord(splitValueToMergedValue(recordValue)),
                    );
                },
                rowValue,
                screenId,
            }),
        [
            accessBindings,
            content.recordId,
            content.elementId,
            content.sidebarDefinition.headerDropdownActions,
            onTelemetryEvent,
            recordValue,
            rowValue,
            screenId,
        ],
    );

    const resolvedHeaderQuickActions = React.useMemo(
        () =>
            calculateActionMenu({
                accessBindings,
                actions: content.sidebarDefinition.headerQuickActions,
                actionType: 'table-sidebar-header-quick-action',
                onTriggerMenuItem: (context, onClick, onActionError) => {
                    if (onTelemetryEvent) {
                        onTelemetryEvent(`tableSidebarHeaderQuickActionTriggered-${context.uniqueId}`, {
                            screenId,
                            elementId: content.elementId,
                            id: context.id,
                            uniqueId: context.uniqueId,
                        });
                    }

                    triggerHandledEvent(
                        screenId,
                        content.elementId,
                        { onClick, onError: onActionError },
                        recordValue?._id || content.recordId,
                        cleanMetadataFromRecord(splitValueToMergedValue(recordValue)),
                    );
                },
                rowValue,
                screenId,
            }),
        [
            accessBindings,
            content.elementId,
            content.recordId,
            content.sidebarDefinition.headerQuickActions,
            onTelemetryEvent,
            recordValue,
            rowValue,
            screenId,
        ],
    );

    return (
        <Typography variant="h3" mb="16px" className="e-sidebar-header" data-testid="e-sidebar-header">
            <NavigationArrows
                hasNextRecord={!!content.nextRecordId}
                hasPreviousRecord={!!content.prevRecordId}
                onNextRecord={onNextRecord}
                onPreviousRecord={onPrevRecord}
            />
            <div className="e-sidebar-title">{dialog.title}</div>

            {!isLoading && resolvedHeaderQuickActions?.length > 0 && (
                <div className="e-sidebar-quick-action-container" data-testid="e-sidebar-quick-action-container">
                    {resolvedHeaderQuickActions.map(a => {
                        if (!a.icon) {
                            throw new Error('Icon is missing from sidebar quick action definition.');
                        }

                        if (a.isHidden) {
                            return null;
                        }

                        return (
                            <IconButton
                                key={a.icon}
                                onClick={(): void => a.onClick()}
                                disabled={a.isDisabled}
                                aria-label={a.title}
                                ml="16px"
                                data-testid={`e-sidebar-quick-action-label-${camelCase(a.title)}`}
                            >
                                <Icon
                                    type={a.icon}
                                    tooltipMessage={a.title}
                                    color={
                                        a.isDestructive
                                            ? tokens.colorsSemanticNegative500
                                            : tokens.colorsUtilityMajor400
                                    }
                                />
                            </IconButton>
                        );
                    })}
                </div>
            )}
            {!isLoading && resolvedPopoverItems?.length > 0 && (
                <div className="e-sidebar-dropdown-action-container" data-testid="e-sidebar-dropdown-action-container">
                    <XtremActionPopover
                        items={resolvedPopoverItems}
                        noIconSupport={false}
                        color={tokens.colorsUtilityMajor450}
                        isOverSidebar={true}
                    />
                </div>
            )}
        </Typography>
    );
}
