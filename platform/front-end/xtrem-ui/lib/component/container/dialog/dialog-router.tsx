import { objectKeys, type Dict } from '@sage/xtrem-shared';
import * as React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import type { ReduxResponsive } from '../../../redux/state';
import type { DialogDescription } from '../../../types/dialogs';
import { ConnectedDialogComponent } from './dialog-component';

interface DialogRouterComponentProps {
    activeDialogs: Dict<DialogDescription>;
    browser: ReduxResponsive;
}

export function DialogRouter(props: DialogRouterComponentProps): React.ReactElement {
    const isFullScreen = (props.browser && !props.browser.greaterThan.s) || false;
    return (
        <>
            {objectKeys(props.activeDialogs).map(dialogKey => (
                <ConnectedDialogComponent key={dialogKey} dialogKey={dialogKey} isFullScreen={isFullScreen} />
            ))}
        </>
    );
}

const mapStateToProps = (state: xtremRedux.XtremAppState): DialogRouterComponentProps => ({
    activeDialogs: state.activeDialogs,
    browser: state.browser,
});

export const ConnectedDialogRouter = connect<DialogRouterComponentProps>(mapStateToProps)(DialogRouter);
