import type { DialogDescription } from '../../../types/dialogs';
import * as React from 'react';
import { PageDialogTitle } from './page-dialog-title';
import { TableSidebarDialogTitle } from './table-sidebar/table-sidebar-dialog-title';
import Typography from 'carbon-react/esm/components/typography';

export function DialogTitle({
    dialog,
    onCloseDialog,
}: {
    dialog: DialogDescription;
    onCloseDialog: () => Promise<void>;
}): React.ReactElement | string {
    if (dialog.type === 'page') {
        return <PageDialogTitle dialog={dialog} onCloseDialog={onCloseDialog} />;
    }

    if (dialog.type === 'table-sidebar') {
        return <TableSidebarDialogTitle dialog={dialog} />;
    }

    const lineHeight = dialog.options.fullScreen ? '72px' : undefined;

    return (
        <div data-component="heading">
            <div className="e-header-container" data-element="header-container">
                <Typography data-element="title" variant="h1" mt="0px" mb="0px" lineHeight={lineHeight}>
                    {dialog.title || ''}
                </Typography>
            </div>
        </div>
    );
}
