import Loader from 'carbon-react/esm/components/loader';
import * as React from 'react';

export interface LoadingDialogState {
    isRendered: boolean;
}

export class LoadingDialog extends React.PureComponent<{}, LoadingDialogState> {
    private timeoutRef: number | null = null;

    constructor(props: {}) {
        super(props);
        this.state = { isRendered: false };
    }

    componentDidMount(): void {
        this.timeoutRef = setTimeout(() => {
            this.timeoutRef = null;
            this.setState({ isRendered: true });
        }, 100) as unknown as number;
    }

    componentWillUnmount(): void {
        if (this.timeoutRef !== null) {
            clearTimeout(this.timeoutRef);
        }
    }

    render(): React.ReactNode {
        if (this.state.isRendered) {
            return (
                <div className="e-loading-indicator" data-testid="e-loading-indicator">
                    <Loader size="large" />
                </div>
            );
        }
        return null;
    }
}

export default LoadingDialog;
