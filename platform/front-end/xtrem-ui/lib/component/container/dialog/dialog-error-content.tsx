import * as React from 'react';
import { showToast } from '../../../service/toast-service';
import { localize } from '../../../service/i18n-service';
import { copyToClipboard } from '../../../utils/dom';
import { StyledIconButton } from '../../ui/styled-icon-button';
import Link from 'carbon-react/esm/components/link';
import { focusFirstElement } from '../../../utils/layout-utils';

export interface DialogErrorContentProps {
    content: Error;
}

export function DialogErrorContent({ content }: DialogErrorContentProps): React.ReactElement {
    const [isDetailOpen, setDetailOpen] = React.useState(false);

    const onToggleClick = (): void => setDetailOpen(!isDetailOpen);

    const onCopy = (): void => {
        copyToClipboard(content.stack ? content.stack.toString() : '');
        showToast(localize('@sage/xtrem-ui/error-detail-copied-to-clipboard', 'Error detail copied to clipboard'), {
            type: 'info',
        });
    };

    const copyErrorDetailsLabel = localize('@sage/xtrem-ui/copy-error-details', 'Copy error details');

    React.useEffect(() => {
        focusFirstElement('.e-dialog-error-content');
    }, []);

    return (
        <div className="e-dialog-error-content" data-testid="e-dialog-error-content">
            <p>{content.message}</p>
            <div className="e-dialog-error-header">
                <Link data-testid="e-dialog-error-details-link" onClick={onToggleClick}>
                    {isDetailOpen
                        ? localize('@sage/xtrem-ui/hide-technical-details', 'Hide technical details')
                        : localize('@sage/xtrem-ui/show-technical-details', 'Show technical details')}
                </Link>
                {isDetailOpen && (
                    <StyledIconButton
                        data-testid="e-dialog-error-copy"
                        onClick={onCopy}
                        iconType="copy"
                        iconTooltipMessage={copyErrorDetailsLabel}
                        aria-label={copyErrorDetailsLabel}
                        buttonType="tertiary"
                    />
                )}
            </div>
            {isDetailOpen && <pre className="e-dialog-error-stack">{content.stack}</pre>}
        </div>
    );
}
