import type { Dict } from '@sage/xtrem-shared';
import type { DialogDescription, PageDialogOptions, CustomDialogOptions } from '../../../types/dialogs';
import { PageControlObject, SectionControlObject } from '../container-control-objects';
import type { ScreenBaseDefinition } from '../../../service/screen-base-definition';
import { getVisibleSectionsFromPage } from '../../../utils/state-utils';
import type { PageDefinition } from '../../../service/page-definition';

export const collectSections = (
    dialog: DialogDescription<PageDialogOptions & CustomDialogOptions>,
    screenDefinitions: Dict<ScreenBaseDefinition>,
): SectionControlObject[] => {
    const content = dialog.content;

    if (content instanceof PageControlObject && dialog.screenId && screenDefinitions[dialog.screenId]) {
        const pageDefinition = screenDefinitions[dialog.screenId] as PageDefinition;

        const sections = getVisibleSectionsFromPage(dialog.screenId, pageDefinition);
        const detailPanel = pageDefinition.page.$.detailPanel;
        const detailPanelSectionIds = detailPanel
            ? [
                  ...detailPanel.layout.detailPanelSectionsLayout.map(section => section.$containerId),
                  detailPanel.layout.detailPanelHeaderLayout?.$containerId,
              ]
            : [];
        return sections.filter(section => detailPanelSectionIds.indexOf(section.id) === -1);
    }
    const allContent = content instanceof Array ? content : [content];
    return allContent.filter(
        ele => (ele instanceof SectionControlObject && !ele.isHidden) || !(ele instanceof SectionControlObject),
    ) as SectionControlObject[];
};
