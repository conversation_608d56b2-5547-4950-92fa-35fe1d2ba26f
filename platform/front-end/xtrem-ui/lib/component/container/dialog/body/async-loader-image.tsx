import * as React from 'react';

export function AsyncLoaderImage(): React.ReactElement {
    return (
        <svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M96 192C149.019 192 192 149.019 192 96C192 42.9807 149.019 0 96 0C42.9807 0 0 42.9807 0 96C0 149.019 42.9807 192 96 192Z"
                fill="black"
            />
            <path
                d="M114.05 111.111C113.337 110.659 112.75 110.034 112.343 109.295C111.936 108.555 111.722 107.725 111.722 106.881C111.722 106.037 111.936 105.206 112.343 104.467C112.75 103.727 113.337 103.102 114.05 102.651C119.029 99.5308 123.134 95.1966 125.979 90.0549C128.823 84.9132 130.314 79.1328 130.311 73.2568C130.311 54.0941 114.709 49.6621 95.5468 49.6621C76.3842 49.6621 60.9175 54.0941 60.9175 73.2568C60.9139 79.1333 62.4042 84.9142 65.2484 90.0566C68.0926 95.1989 72.1974 99.5338 77.177 102.654C77.8905 103.106 78.4781 103.73 78.8853 104.47C79.2925 105.209 79.5061 106.04 79.5061 106.884C79.5061 107.729 79.2925 108.559 78.8853 109.299C78.4781 110.038 77.8905 110.663 77.177 111.115C72.1974 114.235 68.0926 118.57 65.2484 123.712C62.4042 128.854 60.9139 134.635 60.9175 140.512C60.9175 159.673 76.5175 164.105 95.6802 164.105C114.843 164.105 130.311 159.673 130.311 140.512C130.315 134.635 128.825 128.853 125.98 123.71C123.136 118.567 119.03 114.231 114.05 111.111Z"
                fill="#5E5E5E"
            />
            <path
                d="M131.357 161.184H59.8703C58.4358 161.184 57.2729 162.347 57.2729 163.781V164.427C57.2729 165.861 58.4358 167.024 59.8703 167.024H131.357C132.791 167.024 133.954 165.861 133.954 164.427V163.781C133.954 162.347 132.791 161.184 131.357 161.184Z"
                fill="#474747"
            />
            <path
                d="M131.357 48.1299H59.8703C58.4358 48.1299 57.2729 49.2927 57.2729 50.7272V51.3725C57.2729 52.807 58.4358 53.9699 59.8703 53.9699H131.357C132.791 53.9699 133.954 52.807 133.954 51.3725V50.7272C133.954 49.2927 132.791 48.1299 131.357 48.1299Z"
                fill="#474747"
            />
            <path
                d="M109.334 155.273H81.8989C78.3433 155.273 74.9334 153.861 72.4193 151.346C69.9051 148.832 68.4927 145.422 68.4927 141.867C68.4926 140.824 68.7371 139.796 69.2064 138.865C69.6757 137.933 70.3567 137.125 71.1949 136.505L91.4082 121.547C92.6254 120.646 94.0995 120.16 95.6136 120.16C97.1277 120.16 98.6018 120.646 99.8189 121.547L120.032 136.505C120.87 137.125 121.551 137.933 122.021 138.865C122.49 139.796 122.734 140.824 122.734 141.867C122.734 145.421 121.323 148.831 118.81 151.345C116.297 153.859 112.888 155.272 109.334 155.273Z"
                fill="#00D639"
            />
            <path
                d="M98.8784 17.9964C98.8788 18.4452 99.0453 18.878 99.3458 19.2114C99.6463 19.5447 100.059 19.7551 100.506 19.8019C100.952 19.8487 101.4 19.7286 101.763 19.4648C102.126 19.201 102.379 18.8122 102.472 18.3732C102.566 17.9343 102.494 17.4763 102.269 17.0874C102.045 16.6986 101.685 16.4065 101.258 16.2674C100.832 16.1283 100.369 16.1521 99.9584 16.3341C99.5482 16.5162 99.2198 16.8436 99.0366 17.2533C98.9321 17.4871 98.8781 17.7403 98.8784 17.9964Z"
                fill="#E84D4F"
            />
            <path
                d="M117.867 76.7875C117.015 76.7889 116.179 76.5549 115.452 76.1113C114.725 75.6677 114.135 75.0318 113.746 74.2738C113.534 73.8613 108.532 64.1013 106.102 57.8115C106.059 57.7013 105.616 56.6809 103.104 54.5955C101.278 53.0791 99.1114 51.616 97.3709 50.4409C96.386 49.7742 95.5345 49.1964 94.882 48.704C94.0465 48.0764 91.8278 46.4089 92.2011 43.5218C92.4761 41.3501 93.5832 39.3699 95.2891 37.9982C96.6065 36.9565 98.2508 36.4161 99.9293 36.4731C101.608 36.53 103.212 37.1807 104.455 38.3093C104.526 38.3662 105.017 38.7075 106.635 38.9138C108.224 39.1164 110.221 39.1058 112.146 39.0915C114.761 39.0773 117.465 39.0613 119.844 39.5431C123.91 40.3698 126.838 44.0213 128.555 50.4018C129.203 52.8795 129.644 55.4067 129.874 57.9573C129.968 59.1806 129.573 60.3913 128.774 61.323C127.976 62.2546 126.84 62.831 125.617 62.9253C124.394 63.0196 123.183 62.6241 122.252 61.8258C121.32 61.0275 120.743 59.8917 120.649 58.6684C120.488 56.9623 120.212 55.269 119.822 53.6C119.054 50.3733 118.16 48.9582 117.778 48.5707C116.34 48.3218 114.242 48.336 112.208 48.3467C111.497 48.3467 110.766 48.3555 110.032 48.3467C112.205 50.2791 113.899 52.2951 114.742 54.48C116.989 60.3147 121.927 69.9467 121.977 70.0444C122.341 70.7493 122.518 71.5359 122.49 72.3287C122.462 73.1215 122.23 73.8937 121.817 74.5711C121.404 75.2484 120.824 75.8081 120.132 76.1963C119.44 76.5845 118.66 76.7881 117.867 76.7875Z"
                fill="#303030"
            />
            <path
                d="M100.398 41.2516C102.912 41.2516 104.949 39.214 104.949 36.7005C104.949 34.187 102.912 32.1494 100.398 32.1494C97.8848 32.1494 95.8472 34.187 95.8472 36.7005C95.8472 39.214 97.8848 41.2516 100.398 41.2516Z"
                fill="white"
            />
            <path
                d="M105.568 27.1752C105.531 30.5192 103.767 32.2934 100.413 32.2543C98.8085 32.2342 97.278 31.5783 96.1571 30.4307C95.0361 29.2831 94.4164 27.7375 94.434 26.1334C94.4713 22.7877 97.202 23.561 100.548 23.5983C103.894 23.6357 105.6 23.8223 105.568 27.1752Z"
                fill="white"
            />
            <path d="M104.889 37.4452L95.8828 37.2621L94.457 26.7341L106.512 26.8639L104.889 37.4452Z" fill="white" />
            <path
                d="M98.2544 42.7804C97.4885 42.7828 96.7431 42.5329 96.1335 42.0693C93.7726 40.272 92.265 38.3449 91.5219 36.1778C90.8108 34.0764 90.8552 31.8204 91.6748 29.4756C92.7539 26.3929 95.9415 21.7084 100.269 21.4329C101.186 21.3786 102.089 21.687 102.781 22.2917C103.474 22.8964 103.901 23.7491 103.971 24.6658C104.041 25.5826 103.748 26.4902 103.155 27.193C102.563 27.8958 101.717 28.3376 100.802 28.4231C100.197 28.6613 98.8464 30.256 98.3006 31.7902C97.8312 33.1342 97.6641 34.416 100.389 36.4942C100.973 36.9394 101.402 37.5564 101.617 38.2585C101.831 38.9605 101.819 39.7121 101.584 40.4073C101.348 41.1024 100.9 41.7061 100.303 42.1331C99.7061 42.5602 98.9901 42.7891 98.2561 42.7876L98.2544 42.7804Z"
                fill="white"
            />
            <path
                d="M110.822 39.2373C110.591 39.2373 110.361 39.2249 110.132 39.2C106.311 38.7733 104.135 35.0667 102.222 31.7955C101.59 30.7164 100.566 28.9671 99.9611 28.3893C99.0859 28.2575 98.2931 27.7994 97.7418 27.1071C97.1906 26.4147 96.9217 25.5394 96.9892 24.657C97.0568 23.7746 97.4557 22.9504 98.1059 22.35C98.7561 21.7497 99.6094 21.4176 100.494 21.4204C104.269 21.4204 106.395 25.0489 108.27 28.2524C108.926 29.3707 110.077 31.3315 110.836 32.0587C110.979 31.9366 111.115 31.806 111.243 31.6675C111.882 31.0037 112.756 30.618 113.677 30.5938C114.598 30.5696 115.491 30.9088 116.164 31.5381C116.837 32.1674 117.235 33.0362 117.272 33.9567C117.309 34.8772 116.983 35.7753 116.363 36.4569C114.631 38.304 112.77 39.2373 110.822 39.2373Z"
                fill="white"
            />
            <path
                d="M122.256 78.839C122.14 78.8387 122.025 78.8158 121.918 78.7714L116.432 76.551C116.206 76.4699 116.023 76.3026 115.92 76.0859C115.818 75.8692 115.807 75.6208 115.888 75.3954C115.969 75.1701 116.136 74.9861 116.353 74.8841C116.57 74.7821 116.818 74.7703 117.043 74.8514C117.066 74.8588 117.088 74.8677 117.109 74.8781L122.595 77.0985C122.79 77.1771 122.951 77.3209 123.051 77.5052C123.151 77.6896 123.184 77.903 123.143 78.1088C123.103 78.3147 122.993 78.5001 122.831 78.6333C122.669 78.7666 122.466 78.8393 122.256 78.839Z"
                fill="#303030"
            />
            <path
                d="M129.753 64.464C129.664 64.464 129.576 64.4515 129.492 64.4266L123.668 62.6737C123.553 62.6414 123.447 62.5869 123.354 62.5133C123.261 62.4398 123.183 62.3486 123.125 62.245C123.067 62.1414 123.03 62.0275 123.016 61.9096C123.003 61.7918 123.012 61.6724 123.044 61.5582C123.077 61.444 123.131 61.3374 123.205 61.2443C123.278 61.1512 123.37 61.0735 123.473 61.0156C123.577 60.9577 123.691 60.9208 123.809 60.907C123.926 60.8932 124.046 60.9027 124.16 60.9351L124.187 60.944L130.012 62.6969C130.219 62.7598 130.397 62.8948 130.512 63.0775C130.628 63.2601 130.675 63.4782 130.643 63.6921C130.612 63.906 130.505 64.1016 130.341 64.2432C130.178 64.3848 129.969 64.4632 129.753 64.464Z"
                fill="#303030"
            />
            <path
                d="M115.266 34.7467C114.933 34.7462 114.607 34.651 114.327 34.4723C114.046 34.2936 113.822 34.0387 113.681 33.7374C113.54 33.4361 113.488 33.1009 113.53 32.7709C113.572 32.4409 113.708 32.1297 113.92 31.8738C116.064 29.2942 118.082 26.4125 118.101 26.384C118.37 26.0084 118.776 25.754 119.231 25.6759C119.686 25.5978 120.153 25.7024 120.532 25.967C120.91 26.2316 121.169 26.6348 121.252 27.089C121.335 27.5432 121.235 28.0118 120.974 28.3929C120.889 28.5156 118.852 31.4151 116.615 34.1138C116.451 34.3123 116.245 34.472 116.011 34.5815C115.778 34.6909 115.524 34.7473 115.266 34.7467Z"
                fill="#E84D4F"
            />
            <path
                d="M102.567 36.3253C102.48 36.3258 102.394 36.3034 102.318 36.2604C102.242 36.2173 102.178 36.1551 102.134 36.0799C102.089 36.0047 102.066 35.9192 102.064 35.8319C102.063 35.7445 102.085 35.6585 102.128 35.5822L103.195 33.6409C103.259 33.5244 103.367 33.4383 103.495 33.4014C103.623 33.3646 103.76 33.3801 103.876 33.4444C103.993 33.5088 104.079 33.6168 104.116 33.7446C104.153 33.8725 104.137 34.0097 104.073 34.1262L103.006 36.0675C102.963 36.1459 102.899 36.2112 102.822 36.2566C102.745 36.3019 102.657 36.3257 102.567 36.3253Z"
                fill="#E84D4F"
            />
            <path d="M113.799 43.2035L103.518 42.1689V27.3884L113.799 28.4213V43.2035Z" fill="#C6C6C6" />
            <path d="M119.538 36.7004L113.799 43.2035V28.4213L119.538 21.9182V36.7004Z" fill="#C6C6C6" />
            <path
                d="M99.7513 41.968C99.4013 41.9673 99.0595 41.8618 98.7699 41.6651C98.4804 41.4684 98.2563 41.1896 98.1267 40.8644C97.997 40.5393 97.9676 40.1828 98.0424 39.8409C98.1171 39.4989 98.2925 39.1871 98.546 38.9457L103.746 34.0266C104.076 33.6992 104.523 33.5162 104.988 33.5181C105.452 33.5199 105.898 33.7064 106.225 34.0364C106.553 34.3665 106.735 34.8131 106.734 35.278C106.732 35.7429 106.545 36.1881 106.215 36.5155L106.155 36.5724L100.955 41.4915C100.63 41.7992 100.199 41.9698 99.7513 41.968Z"
                fill="#E84D4F"
            />
            <path
                d="M119.536 29.1414C119.177 29.1419 118.826 29.0307 118.533 28.8232L118.274 28.6454C117.887 28.3934 117.616 27.9991 117.518 27.5482C117.421 27.0973 117.505 26.626 117.752 26.2367C118 25.8474 118.391 25.5713 118.841 25.4685C119.29 25.3656 119.763 25.4442 120.155 25.6872C120.199 25.7156 120.244 25.7458 120.284 25.7778L120.544 25.9556C120.85 26.1695 121.079 26.4754 121.199 26.8287C121.319 27.1821 121.323 27.5645 121.211 27.9203C121.098 28.2761 120.875 28.5868 120.574 28.807C120.273 29.0273 119.909 29.1457 119.536 29.145V29.1414Z"
                fill="#E84D4F"
            />
        </svg>
    );
}
