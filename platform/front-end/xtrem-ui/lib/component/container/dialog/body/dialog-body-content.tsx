import Loader from 'carbon-react/esm/components/loader';
import * as React from 'react';
import type { DialogBodyContentProps, TableSidebarDialogContent } from '../../../../types/dialogs';
import type { SectionControlObject } from '../../../control-objects';
import { DialogErrorContent } from '../dialog-error-content';
import { ConnectedDialogBodyCustomContent } from './custom-content';
import { LookupContent } from './lookup-content';
import { DialogBodyMessageContent } from './message-content';
import { DialogBodyPageContent } from './page-content';
import { ConnectedTableSidebarBody } from '../../../table-sidebar/table-sidebar-body';
import { AsyncLoaderContent } from './async-loader-content';

export function DialogBodyContent(props: DialogBodyContentProps): React.ReactElement {
    if (props.dialog.type === 'internal' && React.isValidElement(props.dialog.content)) {
        return props.dialog.content;
    }

    if (props.dialog.type === 'table-sidebar') {
        const sidebarTableContent = props.dialog.content as TableSidebarDialogContent;
        if (!props.dialog.screenId) {
            throw new Error('Dialog screenId is required for table-sidebar dialog');
        }

        return (
            <ConnectedTableSidebarBody
                cardDefinition={sidebarTableContent.cardDefinition}
                columns={sidebarTableContent.columns}
                dialogId={props.dialog.dialogId}
                elementId={sidebarTableContent.elementId}
                isNewRecord={sidebarTableContent.isNewRecord}
                level={sidebarTableContent.level}
                recordId={sidebarTableContent.recordId}
                screenId={props.dialog.screenId}
                sidebarDefinition={sidebarTableContent.sidebarDefinition}
            />
        );
    }

    if (props.dialog.content instanceof Error) {
        return <DialogErrorContent content={props.dialog.content} />;
    }

    if (props.dialog.type === 'async-loader') {
        return <AsyncLoaderContent {...props} />;
    }

    // Message dialog
    if (typeof props.dialog.content === 'string') {
        return <DialogBodyMessageContent {...props} />;
    }

    // Page dialog
    if (props.dialog.type === 'page' && props.screenDefinition) {
        return <DialogBodyPageContent {...props} />;
    }

    // Loading page definition
    if (props.dialog.type === 'page' && !props.screenDefinition) {
        return (
            <div className="e-dialog-content-loading">
                <Loader size="large" />
            </div>
        );
    }

    if (props.dialog.type === 'lookup') {
        return <LookupContent {...props} />;
    }

    // Custom dialog STUFF
    return (
        <ConnectedDialogBodyCustomContent
            {...props}
            content={props.dialog.content as unknown as SectionControlObject[]}
            screenId={props.dialog.screenId!}
        />
    );
}
