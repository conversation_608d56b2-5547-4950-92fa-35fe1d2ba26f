import Form from 'carbon-react/esm/components/form';
import * as React from 'react';
import type { AsyncLoaderDialogContent, DialogBodyContentProps } from '../../../../types/dialogs';
import LoaderBar from 'carbon-react/esm/components/loader-bar';
import { useEffect } from 'react';
import { localize } from '../../../../service/i18n-service';
import { AsyncLoaderImage } from './async-loader-image';
import Button from 'carbon-react/esm/components/button';

const KEEP_WAITING_TIMEOUT = 8000; // 8 seconds

export function AsyncLoaderContent(props: DialogBodyContentProps): React.ReactElement {
    const content = props.dialog.content as AsyncLoaderDialogContent;
    const [isWaiting, setIsWaiting] = React.useState<boolean>(true);

    const onStop = React.useCallback(
        (ev: React.MouseEvent<HTMLButtonElement>): void => {
            ev.preventDefault();
            ev.stopPropagation();
            content.onStop?.();
        },
        [content],
    );
    const onNotifyMe = React.useCallback(
        (ev: React.MouseEvent<HTMLButtonElement>): void => {
            ev.preventDefault();
            ev.stopPropagation();
            content.onNotifyMe?.();
        },
        [content],
    );

    const onKeepWaiting = React.useCallback((ev?: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>): void => {
        ev?.preventDefault();
        ev?.stopPropagation();
        setIsWaiting(true);
        setTimeout(() => {
            setIsWaiting(false);
        }, KEEP_WAITING_TIMEOUT);
    }, []);

    useEffect(() => {
        onKeepWaiting();
    }, [onKeepWaiting]);

    return (
        <>
            {isWaiting && (
                <div className="e-async-loading-indicator">
                    <div className="e-async-loading-indicator-loader">
                        <LoaderBar m={2} />
                    </div>
                    <div className="e-async-loading-indicator-animation-container">
                        <video className="e-async-loading-indicator-animation" autoPlay loop muted playsInline>
                            <source src="/async-loader-animation.webm" type="video/webm" />
                        </video>
                        <div className="e-async-loading-indicator-text">
                            {localize('@sage/xtrem-ui/dialogs-async-loader-waiting', 'This action may take a while...')}
                        </div>
                    </div>
                </div>
            )}
            <Form
                buttonAlignment="right"
                leftSideButtons={
                    content.isStopAvailable ? (
                        <Button buttonType="tertiary" destructive={true} onClick={onStop}>
                            {localize('@sage/xtrem-ui/dialogs-async-loader-button-stop', 'Stop')}
                        </Button>
                    ) : undefined
                }
                rightSideButtons={
                    <>
                        <Button key="keepWaiting" buttonType="secondary" onClick={onKeepWaiting}>
                            {localize('@sage/xtrem-ui/dialogs-async-loader-button-keep-waiting', 'Keep waiting')}
                        </Button>
                        <Button key="notifyMe" buttonType="secondary" onClick={onNotifyMe}>
                            {localize('@sage/xtrem-ui/dialogs-async-loader-button-notify-me', 'Notify me')}
                        </Button>
                    </>
                }
            >
                <div className="e-dialog-async-loader-container">
                    <div className="e-dialog-async-loader-image">
                        <AsyncLoaderImage />
                    </div>
                    <div className="e-dialog-async-loader-content">{content.dialogContent}</div>
                </div>
            </Form>
        </>
    );
}
