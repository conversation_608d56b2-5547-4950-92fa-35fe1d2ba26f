import { objectKeys, type Dict } from '@sage/xtrem-shared';
import Button from 'carbon-react/esm/components/button';
import * as React from 'react';
import type { DialogButton } from '../../../../types/dialogs';
import { focusFirstElement } from '../../../../utils/layout-utils';

export interface DialogButtonsProps {
    buttons: Dict<DialogButton>;
    reverseButtons: boolean;
    pickNegativeButtons: boolean;
    defaultFocusRef?: React.RefObject<any>;
}

const getButtonType = (button: DialogButton, reverseButtons: boolean): 'primary' | 'tertiary' =>
    reverseButtons ? (button.isNegative ? 'primary' : 'tertiary') : button.isNegative ? 'tertiary' : 'primary';

export function DialogActionButtons(props: DialogButtonsProps): React.ReactElement {
    const positiveButtons = objectKeys(props.buttons).filter(
        b => !!props.buttons[b].isNegative === !!props.pickNegativeButtons && !props.buttons[b].isHidden,
    );

    React.useEffect(() => {
        focusFirstElement('[data-element="form-footer"]', true);
    }, []);

    return (
        <>
            {positiveButtons.map((b: string) => {
                const buttonProps = {
                    className: props.buttons[b].className,
                    onClick: props.buttons[b].onClick,
                    disabled: props.buttons[b].isDisabled,
                    destructive: props.buttons[b].isDestructive,
                    buttonType: getButtonType(props.buttons[b], props.reverseButtons),
                };
                return (
                    <Button key={b} {...buttonProps}>
                        {props.buttons[b].text}
                    </Button>
                );
            })}
        </>
    );
}
