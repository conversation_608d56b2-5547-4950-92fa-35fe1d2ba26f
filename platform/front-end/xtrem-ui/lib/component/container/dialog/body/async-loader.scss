.e-dialog-async-loader-container {
    display: flex;

    .e-dialog-async-loader-content {
        font-size: 16px;
        padding-left: 24px;

        @include extra_small {
            padding: 24px;
        }
    }
}

.e-dialog-type-async-loader [data-element="form-footer"] {
    margin-top: 16px;
}

.e-async-loading-indicator {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 99999999;
    align-items: center;
    background: var(--colorsUtilityMajor025);
    animation-name: e-loader-animation;
    animation-iteration-count: 1;
    animation-timing-function: ease-in;
    animation-duration: 0.1s;
    display: flex;
    flex-direction: column;
}

.e-async-loading-indicator-animation-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.e-async-loading-indicator-animation {
    max-width: 400px;
    max-height: 400px;

    @include extra_small {
        max-width: 200px;
        max-height: 200px;
    }
}

.e-dialog-async-loader-image {
    @include extra_small {
        display: none;
    }
}

.e-async-loading-indicator-text {
    margin-top: 32px;
    font-weight: var(--fontWeights500);
    font-family: var(--fontFamiliesDefault);
    font-size: 16px;
}

.e-async-loading-indicator-loader {
    width: 100%;
}