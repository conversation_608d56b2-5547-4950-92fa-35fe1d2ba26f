import Form from 'carbon-react/esm/components/form';
import * as React from 'react';
import { connect } from 'react-redux';
import { RenderingRouter } from '../../../../render/rendering-router';
import type { ScreenBaseDefinition } from '../../../../service/screen-base-definition';
import type { StickerDefinition } from '../../../../service/sticker-definition';
import { ContextType } from '../../../../types';
import type { CustomDialogContentType, CustomDialogOptions, DialogBodyContentProps } from '../../../../types/dialogs';
import { focusFirstElement } from '../../../../utils/layout-utils';
import type {
    BlockControlObject,
    FormDesignerControlObject,
    SectionControlObject,
    TableControlObject,
    WorkflowControlObject,
} from '../../../control-objects';
import { ContainerKey } from '../../../types';
import { GridRow } from '@sage/xtrem-ui-components';
import BusinessActions from '../../footer/business-actions';
import { DialogActionButtons } from './dialog-action-buttons';
import { FieldKey, objectKeys } from '@sage/xtrem-shared';

export type CustomContentSupportedControlObjects =
    | SectionControlObject
    | BlockControlObject
    | WorkflowControlObject
    | TableControlObject
    | FormDesignerControlObject;

export interface DialogCustomContentProps extends DialogBodyContentProps<CustomDialogOptions> {
    content: CustomDialogContentType;
    screenId: string;
    availableColumns: number;
    screenDefinition?: ScreenBaseDefinition;
}

function DialogBodyBody({
    sections,
    screenId,
    availableColumns,
    dialogBodyDialogBodyHeight,
    selectedSection,
}: {
    selectedSection: string;
    dialogBodyDialogBodyHeight: number;
    availableColumns: number;
    screenId: string;
    sections: Array<CustomContentSupportedControlObjects>;
}): React.JSX.Element | React.JSX.Element[] {
    if (
        sections.length === 1 &&
        (sections[0].componentType === FieldKey.Table ||
            sections[0].componentType === FieldKey.Workflow ||
            sections[0].componentType === FieldKey.FormDesigner)
    ) {
        return (
            <GridRow margin={0} gutter={16} verticalMargin={0} columns={availableColumns}>
                <RenderingRouter
                    contextType={ContextType.dialog}
                    screenId={screenId}
                    availableColumns={availableColumns}
                    item={{ $bind: sections[0].id, $isFullWidth: true }}
                    fixedHeight={dialogBodyDialogBodyHeight}
                />
            </GridRow>
        );
    }

    const useTabs = sections.length > 1;

    // We only want to take selectedSection from external prop if is rightAligned because the tabs will be handled by dialog component as is rendered in the SidePanel Header
    const selectedSectionDetails = sections.find(section => section?.id === selectedSection);

    if (useTabs && selectedSectionDetails) {
        return (
            <RenderingRouter
                contextType={ContextType.dialog}
                screenId={screenId}
                availableColumns={availableColumns}
                item={selectedSectionDetails.layout as any}
                fixedHeight={dialogBodyDialogBodyHeight}
            />
        );
    }

    return sections
        .filter(section => section?.id)
        .map(section => (
            <RenderingRouter
                contextType={ContextType.dialog}
                screenId={screenId}
                availableColumns={availableColumns}
                key={section.id}
                item={section.layout as any}
                fixedHeight={dialogBodyDialogBodyHeight}
            />
        ));
}

export function DialogBodyCustomContent(props: DialogCustomContentProps): React.ReactElement {
    let sections: Array<CustomContentSupportedControlObjects> =
        props.content instanceof Array ? props.content : [props.content];
    sections = sections.filter(c => !!c && !c.isHidden);
    const isBlockContent = sections[0]?.componentType === ContainerKey.Block;
    const stickerDefinition = props.dialog.isSticker ? (props.screenDefinition as StickerDefinition) : null;

    const businessActions = stickerDefinition && stickerDefinition.sticker.$.businessActions;
    const hasBusinessActions = businessActions && businessActions.length > 0;

    React.useEffect(() => {
        focusFirstElement('.e-dialog-custom-content');
    }, []);

    const haveButtons = Object.values(props.dialog.buttons || {}).find(b => !b.isHidden);

    const rightSideButtons = ((): JSX.Element | undefined => {
        if (hasBusinessActions) {
            return (
                <div className="e-dialog-footer e-dialog-type-page" data-testid="e-dialog-footer">
                    <BusinessActions
                        businessActions={businessActions!}
                        contextType={ContextType.dialog}
                        defaultButtonType="primary"
                        key="e-sticker-business-actions"
                        screenId={props.dialog.screenId || ''}
                    />
                </div>
            );
        }

        if (haveButtons) {
            return (
                <DialogActionButtons
                    buttons={props.dialog.buttons}
                    reverseButtons={!!props.dialog.options.reverseButtons}
                    pickNegativeButtons={false}
                />
            );
        }

        return undefined;
    })();

    const leftSideButtons = haveButtons ? (
        <DialogActionButtons
            buttons={props.dialog.buttons}
            reverseButtons={!!props.dialog.options.reverseButtons}
            pickNegativeButtons={true}
        />
    ) : undefined;

    const dialogBody = (
        <DialogBodyBody
            availableColumns={props.availableColumns}
            dialogBodyDialogBodyHeight={props.dialogBodyHeight || 0}
            screenId={props.screenId}
            sections={sections}
            selectedSection={props.selectedSection || ''}
        />
    );

    return (
        <Form
            stickyFooter={objectKeys(props.dialog.buttons || {}).length > 0}
            buttonAlignment="right"
            height={props.dialog.options.height ? `${props.dialog.options.height}px` : undefined}
            rightSideButtons={rightSideButtons}
            leftSideButtons={leftSideButtons}
        >
            <div className="e-dialog-custom-body">
                <div className="e-dialog-custom-content">
                    {!isBlockContent && dialogBody}
                    {isBlockContent && (
                        <GridRow
                            className="e-section-body"
                            margin={0}
                            gutter={16}
                            verticalMargin={0}
                            columns={props.availableColumns}
                        >
                            {dialogBody}
                        </GridRow>
                    )}
                </div>
            </div>
        </Form>
    );
}

export const ConnectedDialogBodyCustomContent = connect()(DialogBodyCustomContent);
