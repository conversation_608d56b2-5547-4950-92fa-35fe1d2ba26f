import Form from 'carbon-react/esm/components/form';
import * as React from 'react';
import type { DialogBodyContentProps } from '../../../../types/dialogs';
import { DialogActionButtons } from './dialog-action-buttons';
import * as showdown from 'showdown';
import { escape as lodashEscape } from 'lodash';

const converter = new showdown.Converter();

export function DialogBodyMessageContent(props: DialogBodyContentProps): React.ReactElement {
    const body = props.dialog.options.mdContent ? (
        <span
            className="e-dialog-text-content"
            // eslint-disable-next-line react/no-danger
            dangerouslySetInnerHTML={{
                __html: converter.makeHtml(lodashEscape(props.dialog.content as string)),
            }}
        />
    ) : (
        <span className="e-dialog-text-content">{props.dialog.content as string}</span>
    );

    return (
        <Form
            buttonAlignment="right"
            rightSideButtons={
                <DialogActionButtons
                    buttons={props.dialog.buttons}
                    reverseButtons={!!props.dialog.options.reverseButtons}
                    pickNegativeButtons={!!props.dialog.options.reverseButtons}
                    defaultFocusRef={props.defaultFocusRef}
                />
            }
            leftSideButtons={
                <DialogActionButtons
                    buttons={props.dialog.buttons}
                    reverseButtons={!!props.dialog.options.reverseButtons}
                    pickNegativeButtons={!props.dialog.options.reverseButtons}
                />
            }
        >
            <span className="e-dialog-text-content">{body}</span>
        </Form>
    );
}
