import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    userEvent,
} from '../../../../../__tests__/test-helpers';

import { FieldKey, GraphQLTypes, type GridNestedFieldTypes } from '@sage/xtrem-shared';
import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import produce from 'immer';
import { omit } from 'lodash';
import * as React from 'react';
import { Provider } from 'react-redux';
import * as graphqlService from '../../../../../service/graphql-service';
import { GraphQLKind } from '../../../../../types';
import type { DialogDescription, LookupDialogContent } from '../../../../../types/dialogs';
import { TextControlObject } from '../../../../control-objects';
import type { FilterSelectDecoratorProperties } from '../../../../decorator-properties';
import type { NestedField } from '../../../../nested-fields';
import { LookupContent } from '../lookup-content';

type Node = { _id: string; code: string; description?: string; value?: string };
type FieldProperties = FilterSelectDecoratorProperties<any, { _id: string; code: string }> & {
    _controlObjectType: keyof typeof FieldKey;
};

describe('Lookup content', () => {
    const screenId = 'TestPage';
    const fieldId = 'test-filter-select-field';
    const level = 'info';
    const title = 'Title';

    const defaultDialog = {
        buttons: {
            accept: {
                className: 'e-ui-confirmation-dialog-button',
                isDisabled: undefined,
                isHidden: undefined,
                isNegative: undefined,
                onClick: expect.any(Function),
                text: 'OK',
            },
            cancel: {
                className: 'e-ui-confirmation-dialog-button',
                isDisabled: undefined,
                isHidden: undefined,
                isNegative: true,
                onClick: expect.any(Function),
                text: 'Cancel',
            },
        },
        content: {
            onLookupDialogClose: expect.any(Function),
            contextNode: undefined,
            fieldId,
            fieldProperties: {
                _controlObjectType: 'FilterSelect',
                node: '@sage/xtrem-test/TestNode',
                title,
                valueField: 'code',
                isNewEnabled: true,
            } as const satisfies FieldProperties,
            recordContext: undefined,
            searchText: '',
        } as LookupDialogContent,
        dialogControl: {
            resolve: jest.fn(),
            reject: jest.fn(),
        } as any,
        dialogId: expect.any(Number),
        isDirtyCheck: false,
        isSticker: false,
        options: {
            fullScreen: false,
            height: undefined,
            mdContent: false,
            resolveOnCancel: false,
            reverseButtons: false,
            rightAligned: false,
            size: undefined,
        },
        level,
        screenId,
        subtitle: undefined,
        title,
        type: 'lookup',
    } as const satisfies DialogDescription;

    let fieldSuggestions: Node[] = [
        { _id: '1', code: 'Test code 1' },
        { _id: '2', code: 'Test code 2' },
        { _id: '3', code: 'Test code 3' },
        { _id: '4', code: 'Test code 4' },
    ];

    const setup = (cb: (d: typeof defaultDialog) => typeof defaultDialog = () => defaultDialog) => {
        const dialog = produce(defaultDialog, cb);
        const state = getMockState({
            browser: {
                greaterThan: {
                    l: false,
                    m: false,
                    s: false,
                    xs: false,
                },
                is: {
                    l: false,
                    m: false,
                    s: false,
                    xs: true,
                },
                lessThan: {
                    l: true,
                    m: true,
                    s: true,
                    xs: false,
                },
                mediaType: 'xs',
                orientation: 'portrait',
            },
            nodeTypes: {
                TestNode: {
                    name: 'TestNode',
                    title: 'TestNode',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        _id: {
                            type: GraphQLTypes.String,
                            kind: GraphQLKind.Scalar,
                            isOnInputType: true,
                            isOnOutputType: true,
                            canFilter: true,
                            canSort: true,
                        },
                        code: {
                            type: GraphQLTypes.String,
                            kind: GraphQLKind.Scalar,
                            isOnInputType: true,
                            isOnOutputType: true,
                            canFilter: true,
                            canSort: true,
                        },
                    },
                    mutations: {},
                },
            },
        });
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        addFieldToState<FieldKey.FilterSelect, any, Node>(
            FieldKey.FilterSelect,
            state,
            screenId,
            fieldId,
            omit(dialog.content.fieldProperties as FieldProperties, ['_controlObjectType']),
            null,
        );
        const mockStore = getMockStore(state);
        return {
            ...render(
                <Provider store={mockStore}>
                    <LookupContent
                        dialog={dialog}
                        availableColumns={12}
                        isFirstSection={false}
                        isLastSection={false}
                        onStepOneSection={jest.fn()}
                    />
                </Provider>,
            ),
            dialog,
        };
    };

    beforeEach(() => {
        jest.useFakeTimers();
        jest.spyOn(graphqlService, 'fetchReferenceFieldData').mockResolvedValue({
            query: {
                edges: fieldSuggestions.map((suggestion, index) => {
                    return {
                        cursor: `${index}`,
                        node: suggestion,
                    };
                }),
                pageInfo: {
                    endCursor: '3',
                    hasNextPage: false,
                    hasPreviousPage: false,
                    startCursor: '0',
                },
            },
        });
        jest.spyOn(graphqlService, 'fetchReferenceFieldSuggestions').mockImplementation((input: any) => {
            if (input?.filterValue) {
                const searchText = String(input.filterValue).toLowerCase();
                const suggestions = fieldSuggestions.filter(suggestion => {
                    return suggestion.code.toLowerCase().includes(searchText);
                });
                return Promise.resolve(suggestions);
            }
            return Promise.resolve(fieldSuggestions);
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.useRealTimers();
    });

    it('should call "fetchReferenceFieldData"', async () => {
        setup();
        await screen.findByTestId('e-lookup-dialog');

        await waitFor(() => {
            expect(graphqlService.fetchReferenceFieldData).toHaveBeenCalledWith({
                contextNode: undefined,
                elementId: fieldId,
                fieldProperties: {
                    _controlObjectType: 'FilterSelect',
                    filter: undefined,
                    node: '@sage/xtrem-test/TestNode',
                    title,
                    valueField: 'code',
                    isNewEnabled: true,
                },
                filter: undefined,
                level: undefined,
                orderBy: {
                    _id: 1,
                    code: 1,
                },
                pageSize: 10,
                parentElementId: undefined,
                recordContext: undefined,
                screenId,
                valueField: 'code',
            });
        });
    });

    it('should call "fetchReferenceFieldData" with search text', async () => {
        setup(d => {
            if (d.content) {
                d.content.searchText = 'searchText';
            }
            return d;
        });
        await screen.findByTestId('e-lookup-dialog');

        await waitFor(() => {
            expect(graphqlService.fetchReferenceFieldData).toHaveBeenCalledWith({
                contextNode: undefined,
                elementId: fieldId,
                fieldProperties: {
                    _controlObjectType: 'FilterSelect',
                    filter: undefined,
                    node: '@sage/xtrem-test/TestNode',
                    title,
                    valueField: 'code',
                    isNewEnabled: true,
                },
                filter: {
                    _or: [
                        {
                            code: {
                                _options: 'i',
                                _regex: 'searchText',
                            },
                        },
                    ],
                },
                level: undefined,
                orderBy: {
                    _id: 1,
                    code: 1,
                },
                pageSize: 10,
                parentElementId: undefined,
                recordContext: undefined,
                screenId,
                valueField: 'code',
            });
        });
    });

    it('should resolve when existing option is selected', async () => {
        const { dialog } = setup();
        await screen.findByTestId('e-lookup-dialog');

        const option = await screen.findByText(fieldSuggestions[2].code);
        await userEvent.click(option);

        await waitFor(() => {
            expect(dialog.dialogControl.resolve).toHaveBeenCalled();
            expect(dialog.dialogControl.resolve).toHaveBeenCalledWith([{ _id: '3', code: 'Test code 3' }]);
        });
    });

    it('should resolve when new option is selected', async () => {
        const { dialog } = setup(d => {
            d.content.searchText = 'Lorem Ipsum';
            return d;
        });
        await screen.findByTestId('e-lookup-dialog');

        const search = await screen.findByPlaceholderText('Search...');
        fireEvent.focus(search);
        await userEvent.type(search, 'Lorem Ipsum');
        const option = await screen.findByText('Lorem Ipsum (New)');
        await userEvent.click(option);

        await waitFor(() => {
            expect(dialog.dialogControl.resolve).toHaveBeenCalled();
            expect(dialog.dialogControl.resolve).toHaveBeenCalledWith([
                { _id: '-1', code: 'Lorem Ipsum', _isNewRecord: true },
            ]);
        });
    });

    describe('Filter select - with open lookup dialog and mobile card columns', () => {
        const idNestedField: NestedField<any, GridNestedFieldTypes, any> = {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: '_id',
            },
            properties: {
                bind: '_id',
                title: 'ID',
            },
            type: FieldKey.Text,
        };
        const codeNestedField: NestedField<any, GridNestedFieldTypes, any> = {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: 'code',
            },
            properties: {
                bind: 'code',
                title: 'Code',
            },
            type: FieldKey.Text,
        };
        const descriptionNestedField: NestedField<any, GridNestedFieldTypes, any> = {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: 'description',
            },
            properties: {
                bind: 'description',
                title: 'Description',
            },
            type: FieldKey.Text,
        };
        const valueNestedField: NestedField<any, GridNestedFieldTypes, any> = {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: 'value',
            },
            properties: {
                bind: 'value',
                title: 'Value',
            },
            type: FieldKey.Text,
        };

        beforeEach(() => {
            fieldSuggestions = [
                { _id: '1', code: 'Test code 1', description: 'Test description 1', value: 'Test Value 1' },
                { _id: '2', code: 'Test code 2', description: 'Test description 2', value: 'Test Value 2' },
                { _id: '3', code: 'Test code 3', description: 'Test description 3', value: 'Test Value 3' },
                { _id: '4', code: 'Test code 4', description: 'Test description 4', value: 'Test Value 4' },
            ];
            jest.spyOn(graphqlService, 'fetchReferenceFieldData').mockResolvedValue({
                query: {
                    edges: fieldSuggestions.map((suggestion, index) => {
                        return {
                            cursor: `${index}`,
                            node: suggestion,
                        };
                    }),
                    pageInfo: {
                        endCursor: '3',
                        hasNextPage: false,
                        hasPreviousPage: false,
                        startCursor: '0',
                    },
                },
            });
            jest.spyOn(graphqlService, 'fetchReferenceFieldSuggestions').mockImplementation((input: any) => {
                if (input?.filterValue) {
                    const searchText = String(input.filterValue).toLowerCase();
                    const suggestions = fieldSuggestions.filter(suggestion => {
                        return suggestion.code.toLowerCase().includes(searchText);
                    });
                    return Promise.resolve(suggestions);
                }
                return Promise.resolve(fieldSuggestions);
            });
        });

        it('should display lookup dialog with mobile card columns', async () => {
            setup(d => {
                d.content.fieldProperties.columns = [
                    idNestedField,
                    codeNestedField,
                    descriptionNestedField,
                    valueNestedField,
                ];
                return d;
            });
            await screen.findByTestId('e-lookup-dialog');
            await waitFor(() => {
                expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[0]._id)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[1]._id)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[2]._id)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[3]._id)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[0].code)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[1].code)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[2].code)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[3].code)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[0].description!)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[1].description!)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[2].description!)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[3].description!)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[0].value!)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[1].value!)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[2].value!)).toBeInTheDocument();
                expect(screen.getByText(fieldSuggestions[3].value!)).toBeInTheDocument();
            });
        });

        it('should display only new value in the row card', async () => {
            setup(d => {
                d.content.fieldProperties.columns = [
                    idNestedField,
                    codeNestedField,
                    descriptionNestedField,
                    valueNestedField,
                ];
                return d;
            });
            await screen.findByTestId('e-lookup-dialog');
            const search = await screen.findByPlaceholderText('Search...');
            fireEvent.focus(search);
            await userEvent.type(search, 'Test');
            fireEvent.blur(search);
            await screen.findByText('Test');
            expect(screen.queryByText('-1')).not.toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[0]._id)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[1]._id)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[2]._id)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[3]._id)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[0].code)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[1].code)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[2].code)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[3].code)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[0].description!)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[1].description!)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[2].description!)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[3].description!)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[0].value!)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[1].value!)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[2].value!)).toBeInTheDocument();
            expect(screen.getByText(fieldSuggestions[3].value!)).toBeInTheDocument();
        });

        it('should display only new value in the row card', async () => {
            setup(d => {
                d.content.fieldProperties.columns = [
                    idNestedField,
                    codeNestedField,
                    descriptionNestedField,
                    valueNestedField,
                ];
                return d;
            });
            await screen.findByTestId('e-lookup-dialog');

            const search = await screen.findByPlaceholderText('Search...');
            fireEvent.focus(search);
            await userEvent.type(search, 'Test');
            fireEvent.blur(search);

            await screen.findByText('Test (New)');
            expect(screen.queryByText('-1')).not.toBeInTheDocument();
        });

        it('should display only new value in the row card', async () => {
            setup(d => {
                d.content.fieldProperties.columns = [
                    idNestedField,
                    codeNestedField,
                    descriptionNestedField,
                    valueNestedField,
                ];
                return d;
            });
            await screen.findByTestId('e-lookup-dialog');

            const search = await screen.findByPlaceholderText('Search...');
            fireEvent.focus(search);
            await userEvent.type(search, 'Test');
            fireEvent.blur(search);

            await screen.findByText('Test (New)');
            expect(screen.queryByText('-1')).not.toBeInTheDocument();
        });

        it('should display only new value in the row card', async () => {
            setup(d => {
                d.content.fieldProperties.columns = [
                    idNestedField,
                    codeNestedField,
                    descriptionNestedField,
                    valueNestedField,
                ];
                return d;
            });
            await screen.findByTestId('e-lookup-dialog');

            const search = await screen.findByPlaceholderText('Search...');
            fireEvent.focus(search);
            await userEvent.type(search, 'Test');
            fireEvent.blur(search);

            await screen.findByText('Test (New)');
            expect(screen.queryByText('-1')).not.toBeInTheDocument();
        });
    });
});
