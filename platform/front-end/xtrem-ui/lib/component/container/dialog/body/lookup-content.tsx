import type { Dict, GridNestedFieldTypes, LocalizeLocale } from '@sage/xtrem-shared';
import Button from 'carbon-react/esm/components/button';
import Form from 'carbon-react/esm/components/form';
import Loader from 'carbon-react/esm/components/loader';
import { isEmpty, isFunction, uniq } from 'lodash';
import * as React from 'react';
import type { ReduxResponsive } from '../../../../plugin';
import * as xtremRedux from '../../../../redux';
import { CollectionValue } from '../../../../service/collection-data-service';
import type { CollectionGlobalValidationState } from '../../../../service/collection-data-types';
import { CollectionFieldTypes, RecordActionType } from '../../../../service/collection-data-types';
import { mergeGraphQLFilters } from '../../../../service/filter-service';
import { buildSearchBoxFilter, convertFilterDecoratorToGraphQLFilter } from '../../../../service/graphql-query-builder';
import { fetchReferenceFieldData } from '../../../../service/graphql-service';
import type { GraphQLFilter } from '../../../../service/graphql-utils';
import { removeEdges } from '../../../../service/graphql-utils';
import { localize } from '../../../../service/i18n-service';
import type { DataTypeDetails, FormattedNodeDetails } from '../../../../service/metadata-types';
import type { PageDefinition } from '../../../../service/page-definition';
import type { ScreenBase } from '../../../../service/screen-base';
import type { ScreenBaseDefinition } from '../../../../service/screen-base-definition';
import { showToast } from '../../../../service/toast-service';
import { ContextType } from '../../../../types';
import type { DialogBodyContentProps, LookupDialogContent } from '../../../../types/dialogs';
import { xtremConsole } from '../../../../utils/console';
import { resolveByValue } from '../../../../utils/resolve-value-utils';
import type { EditableFieldProperties } from '../../../editable-field-control-object';
import { getReferenceOrderBy } from '../../../field/reference/reference-utils';
import { AsyncTableComponent } from '../../../field/table/async-table-component';
import type { TableDecoratorProperties, TableProperties } from '../../../field/table/table-component-types';
import type { Nested, Sizable } from '../../../field/traits';
import type { NestedField } from '../../../nested-fields';
import { text } from '../../../nested-fields';
import type { CollectionItem, PartialCollectionValueWithIds } from '../../../types';
import { LookupCreateNewItem } from './lookup-create-new-item';
import { CLASS_NAME_LOOKUP_DIALOG } from './lookup-utils';
import type { ClientNode } from '@sage/xtrem-client';
import { useDeepEqualSelector } from '../../../../utils/hooks/use-deep-equal-selector';
import { useDispatch } from 'react-redux';
import type { DesktopTableComponent } from '../../../field/table/desktop-table-component';
import { transformToLokiJsFilter } from '../../../../utils/lokijs-filter-transformer';

export function LookupContent({ dialog }: DialogBodyContentProps): React.ReactElement {
    const tableRef = React.useRef<DesktopTableComponent>(null);
    const screenId = React.useMemo(() => dialog.screenId, [dialog.screenId]);
    const dispatch = useDispatch();

    if (!screenId) {
        throw new Error('Dialog screenId is required to render lookup dialog.');
    }

    const [isValid, setIsValid] = React.useState<boolean>(true);
    const [isFetchingAllRecords, setIsFetchingAllRecords] = React.useState<boolean>(false);

    const content = React.useMemo(() => dialog.content as LookupDialogContent, [dialog.content]);

    const locale = useDeepEqualSelector<xtremRedux.XtremAppState, LocalizeLocale>(
        (state: xtremRedux.XtremAppState): LocalizeLocale =>
            (state.applicationContext?.locale || 'en-US') as LocalizeLocale,
    );
    const nodeTypes = useDeepEqualSelector<xtremRedux.XtremAppState, Dict<FormattedNodeDetails>>(
        (state: xtremRedux.XtremAppState) => state.nodeTypes,
    );
    const enumTypes = useDeepEqualSelector<xtremRedux.XtremAppState, Dict<string[]>>(
        (state: xtremRedux.XtremAppState) => state.enumTypes,
    );
    const dataTypes = useDeepEqualSelector<xtremRedux.XtremAppState, Dict<DataTypeDetails>>(
        (state: xtremRedux.XtremAppState) => state.dataTypes,
    );
    const browser = useDeepEqualSelector<xtremRedux.XtremAppState, ReduxResponsive>(
        (state: xtremRedux.XtremAppState) => state.browser,
    );

    const screenDefinition = useDeepEqualSelector<xtremRedux.XtremAppState, ScreenBaseDefinition>(
        state => state.screenDefinitions[screenId],
    );

    const selectedItems = React.useMemo<string[]>(
        () => content.fieldProperties.selectedRecords ?? [],
        [content.fieldProperties],
    );

    const isDeviceLessThanM = (browser && browser.lessThan.m) || false;

    const [value, setValue] = React.useState<CollectionValue | null>(null);

    const temporaryRecords = React.useMemo((): PartialCollectionValueWithIds<any>[] => {
        return resolveByValue({
            propertyValue: content.fieldProperties.additionalLookupRecords,
            screenId,
            rowValue: null,
            fieldValue: null,
            skipHexFormat: true,
        });
    }, [content.fieldProperties.additionalLookupRecords, screenId]);

    const onSelectionFinish = React.useCallback(() => {
        if (value) {
            dialog.dialogControl.resolve(
                uniq(selectedItems).map(r =>
                    value.getRawRecord({
                        id: r,
                        cleanMetadata: true,
                        temporaryRecords,
                    }),
                ),
            );
        }
    }, [dialog.dialogControl, selectedItems, temporaryRecords, value]);

    const onMultiSelectionFinish = React.useCallback(async () => {
        if (!value) {
            return;
        }
        if (tableRef.current) {
            if (tableRef.current.isSelectAllChecked()) {
                setIsFetchingAllRecords(true);
                const serverSelectionFilter = tableRef.current.getSelectionFilter();
                await value.getPagesIteratively({
                    rawFilter: serverSelectionFilter,
                    tableFieldProperties: content.fieldProperties as any,
                    pageSize: 1000,
                    cleanMetadata: false,
                });
            }
            const clientSelectionFilter = tableRef.current.getSelectionFilter('client');
            const where = transformToLokiJsFilter(clientSelectionFilter);
            const result = value.getData({ noLimit: true, where, temporaryRecords });
            setIsFetchingAllRecords(false);
            dialog.dialogControl.resolve(result);
        } else {
            dialog.dialogControl.resolve(
                uniq(selectedItems).map(r =>
                    value.getRawRecord({
                        id: r,
                        cleanMetadata: true,
                        temporaryRecords,
                    }),
                ),
            );
        }
    }, [content.fieldProperties, dialog.dialogControl, selectedItems, temporaryRecords, value]);

    const onDialogClose = React.useCallback(() => {
        dialog.dialogControl.cancel();
    }, [dialog.dialogControl]);

    const onKeyDown = React.useCallback(
        (event: React.KeyboardEvent): void => {
            if (isValid && content.isMultiSelect && value && event.key === 'Enter') {
                onSelectionFinish();
            }
        },
        [content.isMultiSelect, isValid, onSelectionFinish, value],
    );

    const onRowClick = React.useCallback(
        (selected: CollectionItem) => {
            if (!content.isMultiSelect) {
                dialog.dialogControl.resolve([selected]);
            }
        },
        [content.isMultiSelect, dialog.dialogControl],
    );

    const setFieldProperties = React.useCallback(
        (elementId: string, fieldProperties: TableProperties): void => {
            dispatch(xtremRedux.actions.setFieldProperties(screenId, elementId, fieldProperties));
        },
        [dispatch, screenId],
    );

    const onValidityChange = React.useCallback(
        ({ globalValidationState }: { globalValidationState: CollectionGlobalValidationState }) => {
            setIsValid(!globalValidationState.some(s => !isEmpty(s)));
        },
        [],
    );

    const onValueChange = React.useCallback(
        (type: RecordActionType, rowValue: ClientNode) => {
            if (type === RecordActionType.MODIFIED) {
                dispatch(
                    xtremRedux.actions.setFieldProperties(screenId, content.fieldId, {
                        ...content.fieldProperties,
                        selectedRecords: [...selectedItems, rowValue._id],
                    } as TableProperties),
                );
            }
        },
        [content.fieldProperties, content.fieldId, dispatch, screenId, selectedItems],
    );

    const columns = React.useMemo<NestedField<any, any>[]>(() => {
        return content.fieldProperties.columns
            ? [...content.fieldProperties.columns].map((column: NestedField<any, any>) => {
                  const properties = { ...column.properties } as EditableFieldProperties & Sizable & Nested;
                  properties.isReadOnly = content.isEditable ? properties.isReadOnly : true;
                  properties.size = 'small';
                  return { ...column, properties } as NestedField<any, any>;
              })
            : [text({ bind: content.fieldProperties.valueField })];
    }, [content.fieldProperties.columns, content.fieldProperties.valueField, content.isEditable]);

    const loadLookupDialogData = React.useCallback(async (): Promise<void> => {
        const fieldProperties = {
            ...content.fieldProperties,
            filter: convertFilterDecoratorToGraphQLFilter(
                screenDefinition,
                content.fieldProperties?.filter,
                content.recordContext,
            ),
        };
        let filter: GraphQLFilter;
        if (content.searchText) {
            filter = buildSearchBoxFilter(
                fieldProperties,
                nodeTypes,
                locale,
                CollectionFieldTypes.LOOKUP_DIALOG,
                content.searchText,
            );

            if (fieldProperties.filter && !isFunction(fieldProperties.filter)) {
                // apply app code filter first and then the search filter, same as in collection-data-service
                filter = mergeGraphQLFilters<GraphQLFilter>([fieldProperties.filter, filter]);
            }
        } else if (fieldProperties.filter && !isFunction(fieldProperties.filter)) {
            filter = fieldProperties.filter;
        }

        const orderBy = getReferenceOrderBy(fieldProperties);

        const result = await fetchReferenceFieldData({
            fieldProperties,
            screenId,
            elementId: content.fieldId,
            valueField: content.valueField || fieldProperties.valueField,
            filter,
            orderBy,
            parentElementId: content.parentElementId,
            recordContext: content.recordContext,
            contextNode: content.contextNode,
            pageSize: 10,
            level: content.level,
        });

        const columnDefinitions = (fieldProperties.columns as NestedField<ScreenBase, GridNestedFieldTypes>[]) || [];

        const tableValue = new CollectionValue({
            bind: fieldProperties.bind,
            columnDefinitions: [columnDefinitions],
            contextNode: content.contextNode,
            elementId: content.fieldId,
            fieldType: CollectionFieldTypes.LOOKUP_DIALOG,
            filter: [filter],
            hasNextPage: result.query?.pageInfo?.hasNextPage || false,
            initialValues: removeEdges(result, true, true)?.data ?? [],
            isTransient: !!fieldProperties.isTransient,
            mapServerRecordFunctions: fieldProperties.mapServerRecord ? [fieldProperties.mapServerRecord] : undefined,
            nodes: [fieldProperties.node as string],
            nodeTypes,
            orderBy: [orderBy],
            parentElementId: content.parentElementId,
            recordContext: content.recordContext,
            referenceLookupContextLevel: content.level,
            screenId,
        });

        tableValue.subscribeForValidityChanges(onValidityChange);
        tableValue.subscribeForValueChanges(onValueChange);

        setValue(tableValue);

        if (tableValue.getData({ cleanMetadata: false, temporaryRecords }).length === 0) {
            setTimeout(() => {
                const closeButton = document.querySelector('button[data-element="close"]') as HTMLButtonElement;
                closeButton?.focus();
            }, 500);
        }
    }, [
        content.contextNode,
        content.fieldId,
        content.fieldProperties,
        content.level,
        content.parentElementId,
        content.recordContext,
        content.searchText,
        content.valueField,
        locale,
        nodeTypes,
        onValidityChange,
        onValueChange,
        screenDefinition,
        screenId,
        temporaryRecords,
    ]);

    React.useEffect(
        () => {
            loadLookupDialogData().catch((error: Error) => {
                xtremConsole.warn(error);
                showToast(localize('@sage/xtrem-ui/lookup-dialog-failed-fetch', 'Failed to fetch options'), {
                    type: 'warning',
                });
                onSelectionFinish();
            });
        },
        // INFO: Removed dependency on `loadLookupDialogData` to prevent infinite loop
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [],
    );

    const data = value?.getData({ cleanMetadata: false, temporaryRecords });
    const hasData = !!data && data.length > 0;

    const fieldProps: TableProperties = React.useMemo((): TableProperties => {
        const tableProps: TableProperties = {
            ...(content.fieldProperties as TableDecoratorProperties),
            canSelect: false,
            columns: [...columns],
            hasSearchBoxMobile: true,
            helperText: undefined,
            isHelperTextHidden: true,
            isTransient: content.fieldProperties.isTransient,
            isHidden: false,
            isTitleHidden: true,
            pageSize: 10,
            cardView: isDeviceLessThanM,
            orderBy: getReferenceOrderBy(content.fieldProperties),
            ...(hasData && { selectedRecords: [content.selectedRecordId ?? data[0]._id] }),
        };

        delete tableProps.title;

        if (content.isMultiSelect) {
            tableProps.canSelect = true;
            tableProps.selectedRecords = hasData ? selectedItems : [];
        }

        return tableProps;
    }, [
        columns,
        content.fieldProperties,
        content.isMultiSelect,
        content.selectedRecordId,
        data,
        hasData,
        isDeviceLessThanM,
        selectedItems,
    ]);

    return (
        <Form
            stickyFooter={!!content.isMultiSelect}
            buttonAlignment="right"
            leftSideButtons={
                content.isMultiSelect && (
                    <Button onClick={onDialogClose} data-testid="e-lookup-dialog-button-cancel">
                        {localize('@sage/xtrem-ui/cancel', 'Cancel')}
                    </Button>
                )
            }
            rightSideButtons={
                content.isMultiSelect && (
                    <Button
                        onClick={onMultiSelectionFinish}
                        data-testid="e-lookup-dialog-button-select"
                        buttonType="primary"
                        disabled={!isValid}
                    >
                        {localize('@sage/xtrem-ui/lookup-dialog-confirm-select', 'Select')}
                    </Button>
                )
            }
        >
            <div className={`${CLASS_NAME_LOOKUP_DIALOG} ag-custom-component-popup`} data-testid="e-lookup-dialog">
                <div className="e-dialog-content">
                    <div className="e-dialog-body" data-testid="e-dialog-body" onKeyDown={onKeyDown}>
                        {value && (
                            <>
                                {content.isLinkCreateNewText && (
                                    <LookupCreateNewItem
                                        node={fieldProps.node}
                                        createTunnelLinkText={content.createTunnelLinkText}
                                        onCreateNewItemLinkClick={content.onCreateNewItemLinkClick}
                                        screenId={screenId}
                                        isDeviceLessThanM={isDeviceLessThanM}
                                    />
                                )}
                                <AsyncTableComponent
                                    ref={tableRef}
                                    accessBindings={(screenDefinition as PageDefinition).accessBindings || {}}
                                    additionalLookupRecords={() => temporaryRecords}
                                    browser={browser}
                                    contextType={ContextType.dialog}
                                    dataTypes={dataTypes}
                                    elementId={content.fieldId}
                                    enableMobileLoadMore={true}
                                    enumTypes={enumTypes}
                                    fieldProperties={fieldProps}
                                    isLookupDialog={true}
                                    isUsingInfiniteScroll={true}
                                    item={{ $bind: content.fieldId }}
                                    locale={locale}
                                    nodeTypes={nodeTypes}
                                    numberOfVisibleRows={10}
                                    onRowClick={onRowClick}
                                    recordContext={content.recordContext}
                                    screenId={screenId}
                                    searchText={content.searchText || ''}
                                    selectionMode={content.isMultiSelect ? 'multiple' : 'single'}
                                    setFieldProperties={setFieldProperties}
                                    tableUserSettings={{}}
                                    validationErrors={[]}
                                    value={value}
                                />
                            </>
                        )}
                        {(!value || isFetchingAllRecords) && (
                            <div className="e-lookup-dialog-loader">
                                <Loader size="large" />
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </Form>
    );
}
