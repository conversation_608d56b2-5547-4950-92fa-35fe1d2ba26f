import type { AccessStatus } from '@sage/xtrem-shared';
import Button from 'carbon-react/esm/components/button';
import React from 'react';
import type * as xtremRedux from '../../../../redux';
import { localize } from '../../../../service/i18n-service';
import { getElementAccessStatus } from '../../../../utils/access-utils';
import { getPageDefinitionFromState } from '../../../../utils/state-utils';
import { useDeepEqualSelector } from '../../../../utils/hooks/use-deep-equal-selector';

export interface LookupCreateNewItemProps {
    node?: any;
    createTunnelLinkText?: string;
    onCreateNewItemLinkClick?: (ev: React.MouseEvent<HTMLButtonElement>) => void;
    screenId?: string;
    isDeviceLessThanM?: boolean;
}

export function LookupCreateNewItem({
    node = '',
    createTunnelLinkText,
    onCreateNewItemLinkClick,
    screenId = '',
    isDeviceLessThanM,
}: LookupCreateNewItemProps): React.ReactElement | null {
    const createRight = useDeepEqualSelector<xtremRedux.XtremAppState, AccessStatus | null>(state => {
        if (!node) {
            return null;
        }

        const pageDefinition = getPageDefinitionFromState(screenId, state);
        if (!pageDefinition) {
            return null;
        }

        return (
            getElementAccessStatus({
                accessBindings: pageDefinition.accessBindings,
                bind: '$create',
                elementProperties: {},
                contextNode: node,
                nodeTypes: state.nodeTypes,
                dataTypes: state.dataTypes,
            }) || null
        );
    });

    if (createRight !== 'authorized') {
        return null;
    }

    return (
        <div
            className={
                isDeviceLessThanM
                    ? 'e-lookup-dialog-create-new-item-container-mobile'
                    : 'e-lookup-dialog-create-new-item-container'
            }
        >
            <Button
                buttonType="secondary"
                iconType="add"
                data-testid="e-lookup-dialog-create-new-item-button"
                onClick={onCreateNewItemLinkClick}
            >
                {createTunnelLinkText || localize('@sage/xtrem-ui/lookup-dialog-create-new-item', 'Create new item')}
            </Button>
        </div>
    );
}
