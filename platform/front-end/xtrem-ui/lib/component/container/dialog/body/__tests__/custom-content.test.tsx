// this MUST come BEFORE any import https://jestjs.io/docs/en/manual-mocks#using-with-es-module-imports
import * as React from 'react';
import type { PageArticleItem } from '../../../../../service/layout-types';
import { SectionControlObject } from '../../../../control-objects';
import type { DialogCustomContentProps } from '../custom-content';
import { DialogBodyCustomContent } from '../custom-content';
import { render } from '@testing-library/react';

jest.mock('../../../../../render/rendering-router');

describe('Dialog custom content', () => {
    const screenId = 'TestPage';
    let mockDialogProperties: DialogCustomContentProps;
    let section: SectionControlObject;

    beforeEach(() => {
        section = new SectionControlObject({
            screenId,
            elementId: 'testBlock',
            getUiComponentProperties: jest.fn().mockReturnValue({}),
            setUiComponentProperties: jest.fn(),
            getValidationState: jest.fn(),
            dispatchSectionValidation: jest.fn(),
            layout: {} as Partial<PageArticleItem>,
        });

        mockDialogProperties = {
            content: section,
            availableColumns: 8,
            dialog: {
                screenId,
                title: 'Test',
                buttons: {},
                content: section,
                dialogId: 123,
                isSticker: false,
                level: 'info',
                dialogControl: {} as any,
                options: {},
            },
            isFirstSection: false,
            isLastSection: false,
            onStepOneSection: jest.fn(),
            screenId,
        };
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('snapshots', () => {
        it('should render section elements', () => {
            const { container } = render(<DialogBodyCustomContent {...mockDialogProperties} />);
            expect(container).toMatchSnapshot();
        });
    });
});
