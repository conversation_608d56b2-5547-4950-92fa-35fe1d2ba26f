@import "../../../../render/style/mixins.scss";

.e-lookup-dialog {
    .e-dialog-body {
        display: grid;
        place-content: center;
        grid-template-columns: 1fr;
        align-items: center;
    }
}

.e-lookup-dialog .e-dialog-content {
    min-height: 500px;

    .e-table-field,
    .e-lookup-dialog-loader {
        grid-area: 2/1/2/1;
    }

    [data-element="form-footer"] {
        margin-top: 16px;
    }

    @include extra_small {
        margin-left: 0;
        margin-right: 0;
        min-height: unset;

        .e-table-field {
            padding: 0;
        }

        .e-table-field-mobile-search>div {
            margin-bottom: 0;
        }
    }

    .e-dialog-body .e-field.e-field.e-table-field {
        margin-top: 0 !important;
    }

    .e-table-field-mobile {
        margin: 0;
        height: calc(100vh - 52px);
        display: flex;
        flex-direction: column;

        .e-table-field-mobile-rows {
            overflow-y: auto;
        }
    }

    .e-dialog-body .e-lookup-dialog-create-new-item-container {
        display: flex;
        justify-content: flex-end;
        align-items: flex-start;
        margin-bottom: 16px;
    }

    .e-dialog-body .e-lookup-dialog-create-new-item-container-mobile {
        display: flex;
        justify-content: flex-end;
        align-items: flex-start;
        padding: 16px 16px 0px 16px;
    }
}
