import Form from 'carbon-react/esm/components/form';
import { isEmpty } from 'lodash';
import * as React from 'react';
import type { PageDefinition } from '../../../../service/page-definition';
import { ContextType } from '../../../../types';
import type { DialogBodyContentProps, PageDialogOptions } from '../../../../types/dialogs';
import { focusFirstElement } from '../../../../utils/layout-utils';
import BusinessActions from '../../footer/business-actions';
import PageComponent from '../../page/page-component';
import Button from 'carbon-react/esm/components/button';
import { localize } from '../../../../service/i18n-service';
import { BusinessActionButtonWrapper } from '../../footer/bussiness-action-button-wrapper';
import { dispatchContainerValidation } from '../../../../service/dispatch-service';
import { ContainerValidationErrorsComponent } from '../../page/container-validation-errors';
import { showToast } from '../../../../service/toast-service';
import type { SectionDecoratorProperties } from '../../section/section-types';

export function DialogBodyPageContent({
    availableColumns,
    dialog,
    dialogBodyHeight,
    isFirstSection,
    isLastSection,
    noHeader,
    onStepOneSection,
    screenDefinition,
    selectedSection,
}: DialogBodyContentProps<PageDialogOptions>): React.ReactNode {
    const pageDefinition = screenDefinition as PageDefinition | undefined;
    const businessActions = pageDefinition?.page.$.businessActions ?? [];
    const isWizardMode = pageDefinition ? pageDefinition.page.$.mode === 'wizard' : false;
    const hasBusinessActions = !isEmpty(businessActions);
    const dialogContentSelector = '.e-dialog-content';

    React.useEffect(() => {
        const selector = Array.from(document.querySelectorAll(dialogContentSelector))
            ? dialogContentSelector
            : '[data-element="sidebar"]';
        focusFirstElement(selector);
    }, []);

    React.useEffect(() => {
        if (isWizardMode) {
            const selector = Array.from(document.querySelectorAll(dialogContentSelector))
                ? dialogContentSelector
                : '[data-element="sidebar"]';
            focusFirstElement(selector);
        }
    }, [selectedSection, isWizardMode]);

    const sectionProperties = selectedSection
        ? (pageDefinition?.metadata.uiComponentProperties[selectedSection] as SectionDecoratorProperties)
        : null;

    const onNextClick = React.useCallback(async () => {
        if (selectedSection && pageDefinition) {
            const result = await dispatchContainerValidation(
                pageDefinition.page._pageMetadata.screenId,
                selectedSection,
            );

            if (result.blockingErrors.length > 0) {
                showToast(
                    <ContainerValidationErrorsComponent
                        validationResults={result.allErrors}
                        pageMetadata={pageDefinition.page._pageMetadata}
                        screenId={pageDefinition.page._pageMetadata.screenId}
                    />,
                    {
                        type: 'error',
                        language: 'jsx',
                    },
                );
                return;
            }
        }
        onStepOneSection(1);
    }, [onStepOneSection, pageDefinition, selectedSection]);

    const nextButtonLabel =
        sectionProperties?.wizardNextButtonLabel ||
        (isLastSection
            ? localize('@sage/xtrem-ui/wizard-finish', 'Finish')
            : localize('@sage/xtrem-ui/wizard-next', 'Next'));

    const previousButtonLabel =
        sectionProperties?.wizardPreviousButtonLabel || localize('@sage/xtrem-ui/wizard-previous', 'Previous');

    let dialogBodyDialogBodyHeight;
    if (dialogBodyHeight) {
        if (hasBusinessActions || isWizardMode) {
            dialogBodyDialogBodyHeight = dialogBodyHeight - 42;
        } else {
            dialogBodyDialogBodyHeight = dialogBodyHeight - 2;
        }
    } else {
        dialogBodyDialogBodyHeight = undefined;
    }

    if (!pageDefinition) {
        return null;
    }

    return (
        <Form
            onSubmit={(ev: React.FormEvent<HTMLFormElement>): void => {
                ev.preventDefault();
            }}
            data-pendo-page={dialog.screenId}
            data-pendo-page-mode="dialog"
            height={dialog.options.height ? `${dialog.options.height}px` : undefined}
            stickyFooter={hasBusinessActions || isWizardMode}
            rightSideButtons={
                (hasBusinessActions || isWizardMode) && (
                    <div className="e-dialog-footer e-dialog-type-page" data-testid="e-dialog-footer">
                        {isWizardMode && (
                            <BusinessActionButtonWrapper title={nextButtonLabel} id="next">
                                <Button
                                    ml="16px"
                                    onClick={onNextClick}
                                    buttonType={sectionProperties?.wizardNextButtonType}
                                >
                                    {nextButtonLabel}
                                </Button>
                            </BusinessActionButtonWrapper>
                        )}
                        {hasBusinessActions && dialog.screenId && (
                            <BusinessActions
                                businessActions={businessActions}
                                contextType={ContextType.dialog}
                                defaultButtonType="primary"
                                key="e-sticker-business-actions"
                                screenId={dialog.screenId}
                                pendoId="pageFooterBusinessActions"
                            />
                        )}
                    </div>
                )
            }
            leftSideButtons={
                isWizardMode && (
                    <div className="e-dialog-footer e-dialog-type-page" data-testid="e-dialog-footer">
                        <BusinessActionButtonWrapper title={previousButtonLabel} id="previous">
                            <Button
                                onClick={(): void => onStepOneSection(-1)}
                                disabled={isFirstSection}
                                buttonType={sectionProperties?.wizardPreviousButtonType || 'primary'}
                            >
                                {previousButtonLabel}
                            </Button>
                        </BusinessActionButtonWrapper>
                    </div>
                )
            }
        >
            <PageComponent
                key={dialog.screenId}
                pageDefinition={pageDefinition}
                contextType={ContextType.dialog}
                availableColumns={availableColumns}
                selectedSection={selectedSection}
                noHeader={noHeader}
                fixedHeight={dialogBodyDialogBodyHeight}
                isMainListDisplayedInDialog={dialog.options.isMainListDisplayedInDialog}
                isPageDisplayedInFullScreenDialog={!!dialog.options.fullScreen}
            />
        </Form>
    );
}
