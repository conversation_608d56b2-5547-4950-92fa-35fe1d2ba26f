// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Page footer Snapshots should render with default properties 1`] = `
<div>
  <span
    class="e-page-footer-container"
  >
    <div
      class="e-page-footer"
    >
      <div
        id="mockBusinessActions"
      >
        <div
          id="mockDivBusinessAction"
        />
        <div
          id="mockDivBusinessAction"
        />
        <div
          id="mockDivBusinessAction"
        />
        <div
          id="mockDivContextType"
        >
          page
        </div>
        <div
          id="mockDivDefaultButtonType"
        >
          secondary
        </div>
      </div>
    </div>
  </span>
</div>
`;

exports[`Page footer Snapshots should render with dialog context type 1`] = `
<div>
  <span
    class="e-page-footer-container e-page-footer-context-dialog"
  >
    <div
      class="e-page-footer"
    >
      <div
        id="mockBusinessActions"
      >
        <div
          id="mockDivBusinessAction"
        />
        <div
          id="mockDivBusinessAction"
        />
        <div
          id="mockDivBusinessAction"
        />
        <div
          id="mockDivContextType"
        >
          dialog
        </div>
        <div
          id="mockDivDefaultButtonType"
        >
          secondary
        </div>
      </div>
    </div>
  </span>
</div>
`;

exports[`Page footer Snapshots should render with neither crud actions nor page actions available 1`] = `<div />`;
