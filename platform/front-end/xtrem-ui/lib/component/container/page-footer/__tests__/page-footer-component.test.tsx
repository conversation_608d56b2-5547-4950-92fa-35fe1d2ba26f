import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { PageAction } from '../../../..';
import {
    addBusinessActionToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';
import type { XtremAppState } from '../../../../redux';
import type { PageFooterProps } from '../page-footer-component';
import { PageFooter } from '../page-footer-component';
import { ContextType } from '../../../../types';
import type { BusinessActionsProps } from '../../footer/business-actions';
import { render } from '@testing-library/react';

jest.mock(
    '../../footer/business-actions',
    () =>
        function MockComponent(props: BusinessActionsProps) {
            return (
                <div id="mockBusinessActions">
                    {props.businessActions &&
                        props.businessActions.map(() => <div id="mockDivBusinessAction" key="mockDivBusinessAction" />)}
                    <div id="mockDivContextType" key="mockDivContextType">
                        {props.contextType}
                    </div>
                    <div id="mockDivDefaultButtonType" key="mockDivDefaultButtonType">
                        {props.defaultButtonType}
                    </div>
                </div>
            );
        },
);

describe('Page footer', () => {
    const screenId = 'TestPage';
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let businessActions: PageAction[] = [];

    beforeEach(() => {
        const state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);

        businessActions = [
            addBusinessActionToState(state, screenId, 'businessAction1', {
                title: 'Business Action 1',
            }),
            addBusinessActionToState(state, screenId, 'businessAction2', {
                title: 'Business Action 2',
                isDisabled: true,
            }),
            addBusinessActionToState(state, screenId, 'businessAction2', {
                title: 'Business Action 3',
                icon: 'pdf',
            }),
        ];

        mockStore = getMockStore(state);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('Snapshots', () => {
        const getRenderedElement = (props: Omit<PageFooterProps, 'screenId'>) =>
            render(
                <Provider store={mockStore}>
                    <PageFooter {...props} screenId={screenId} />
                </Provider>,
            );

        it('should render with default properties', () => {
            const { container } = getRenderedElement({ businessActions });
            expect(container).toMatchSnapshot();
            expect(container.querySelectorAll('#mockBusinessActions')).toHaveLength(1);

            const divContextType = container.querySelector('#mockDivContextType');
            expect(divContextType).not.toBeNull();
            expect(divContextType).toHaveTextContent(ContextType.page);

            const divDefaultButtonType = container.querySelector('#mockDivDefaultButtonType');
            expect(divDefaultButtonType).not.toBeNull();
            expect(divDefaultButtonType).toHaveTextContent('secondary');
        });

        it('should render with dialog context type', () => {
            const { container } = getRenderedElement({ businessActions, contextType: ContextType.dialog });
            expect(container).toMatchSnapshot();
            expect(container.querySelector('#mockBusinessActions')).not.toBeNull();

            const divContextType = container.querySelector('#mockDivContextType');
            expect(divContextType).not.toBeNull();
            expect(divContextType).toHaveTextContent(ContextType.dialog);
        });

        it('should render with neither crud actions nor page actions available', () => {
            const { container } = getRenderedElement({ businessActions: [] });
            expect(container).toMatchSnapshot();
            expect(container.querySelector('#mockBusinessActions')).toBeNull();
            expect(container.querySelector('#mockCrudButton')).toBeNull();
        });
    });
});
