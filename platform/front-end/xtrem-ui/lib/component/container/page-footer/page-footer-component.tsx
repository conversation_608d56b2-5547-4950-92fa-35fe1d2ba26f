import * as React from 'react';
import { useDispatch } from 'react-redux';
import { ContextType } from '../../../types';
import type { PageActionControlObject, PageMode } from '../../control-objects';
import BusinessActions from '../footer/business-actions';
import { BusinessAction } from '../footer/business-action';
import * as xtremRedux from '../../../redux';
import { localize } from '../../../service/i18n-service';
import type { PageDefinition } from '../../../service/page-definition';
import type { SectionDecoratorProperties } from '../../decorator-properties';
import { getVisibleSections } from '../../../utils/layout-utils';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

export interface PageFooterProps {
    screenId: string;
    businessActions?: PageActionControlObject[];
    contextType?: ContextType;
    pageMode?: PageMode;
}

export function PageFooter({
    businessActions,
    screenId,
    contextType,
    pageMode,
}: PageFooterProps): React.ReactElement | null {
    const isExtraSmallScreen = useDeepEqualSelector((state: xtremRedux.XtremAppState) => state.browser.is.xs);
    const dispatch = useDispatch();

    const className = React.useMemo((): string => {
        const classes = ['e-page-footer-container'];
        if (contextType) {
            classes.push(`e-page-footer-context-${contextType}`);
        }

        return classes.join(' ');
    }, [contextType]);

    const visibleSections: string[] = useDeepEqualSelector((state: xtremRedux.XtremAppState) => {
        return getVisibleSections(state.screenDefinitions[screenId] as PageDefinition, {}).map(s =>
            String(s.$containerId),
        );
    });

    const activeSection = useDeepEqualSelector((state: xtremRedux.XtremAppState) => {
        const pageDefinition = state.screenDefinitions[screenId] as PageDefinition;
        if (!pageDefinition) {
            return null;
        }

        return pageDefinition.activeSection || visibleSections[0] || null;
    });

    const activeSectionProperties: SectionDecoratorProperties | null = useDeepEqualSelector(
        (state: xtremRedux.XtremAppState) => {
            const pageDefinition = state.screenDefinitions[screenId] as PageDefinition;
            if (!pageDefinition) {
                return null;
            }

            if (!activeSection) {
                return null;
            }

            return pageDefinition.metadata.uiComponentProperties[activeSection] as SectionDecoratorProperties;
        },
    );

    const onNextClick = React.useCallback(() => {
        dispatch(xtremRedux.actions.stepOneSection(screenId, 1));
    }, [dispatch, screenId]);

    const onPrevClick = React.useCallback(() => {
        dispatch(xtremRedux.actions.stepOneSection(screenId, 1));
    }, [dispatch, screenId]);

    if ((businessActions && businessActions?.length > 0) || pageMode === 'wizard') {
        return (
            // BL: This element has to be a SPAN for tricky spacing reasons
            <span className={className}>
                <div className="e-page-footer">
                    <BusinessActions
                        businessActions={businessActions}
                        contextType={contextType || ContextType.page}
                        defaultButtonType="secondary"
                        key="e-page-footer-business-actions"
                        screenId={screenId}
                        maxMainActions={isExtraSmallScreen ? 2 : 20}
                        pendoId="pageFooterBusinessActions"
                    />
                    {pageMode === 'wizard' && (
                        <div className="e-page-footer-wizard-button-wrapper">
                            {activeSection && visibleSections.indexOf(activeSection) !== 0 && (
                                <div className="e-page-footer-wizard-button">
                                    <BusinessAction
                                        onClick={onPrevClick}
                                        screenId={screenId}
                                        pendoId="wizard-footer-prev-button"
                                        title={
                                            activeSectionProperties?.wizardPreviousButtonLabel ||
                                            localize('@sage/xtrem-ui/wizard-previous', 'Previous')
                                        }
                                    />
                                </div>
                            )}
                            <div className="e-page-footer-wizard-button">
                                <BusinessAction
                                    onClick={onNextClick}
                                    screenId={screenId}
                                    pendoId="wizard-footer-next-button"
                                    title={
                                        activeSectionProperties?.wizardNextButtonLabel ||
                                        localize('@sage/xtrem-ui/wizard-next', 'Next')
                                    }
                                    buttonType={activeSectionProperties?.wizardNextButtonType || 'primary'}
                                />
                            </div>
                        </div>
                    )}
                </div>
            </span>
        );
    }
    return null;
}
