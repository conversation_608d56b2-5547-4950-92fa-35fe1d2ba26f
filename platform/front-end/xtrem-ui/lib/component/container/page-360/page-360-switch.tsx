import * as React from 'react';
import Switch from 'carbon-react/esm/components/switch';
import Icon from 'carbon-react/esm/components/icon';
import { localize } from '../../../service/i18n-service';
import { useDispatch, useSelector } from 'react-redux';
import { getPageDefinitionFromState, isScreenDefinitionDirty } from '../../../utils/state-utils';
import * as xtremRedux from '../../../redux';
import { update360ViewInPath } from '../../../redux/actions';

export interface Page360SwitchProps {
    screenId: string;
}

export function Page360Switch({ screenId }: Page360SwitchProps): React.ReactElement {
    const is360ViewOn = useSelector<xtremRedux.XtremAppState, boolean>(
        state => !!getPageDefinitionFromState(screenId, state)?.is360ViewOn,
    );

    const isDisabled = useSelector<xtremRedux.XtremAppState, boolean>(state =>
        isScreenDefinitionDirty(getPageDefinitionFromState(screenId, state)),
    );

    const dispatch = useDispatch();

    const onChange = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>) => {
            const isEnabled = ev.target.checked;
            dispatch(xtremRedux.actions.set360ViewState(screenId, isEnabled, true));
            dispatch(update360ViewInPath(isEnabled));
        },
        [dispatch, screenId],
    );

    return (
        <div
            className={`e-page-360-switch ${is360ViewOn ? 'active' : ''} ${isDisabled ? 'disabled' : ''}`}
            data-testid="e-page-360-switch"
        >
            <Icon
                type="dashboard"
                color={is360ViewOn ? 'var(--colorsActionMinor500)' : 'var(--colorsUtilityMajor300)'}
                marginRight="8px"
                disabled={isDisabled}
            />
            <div className="e-page-360-switch-input">
                <Switch
                    data-pendoid={`360View${is360ViewOn ? 'Enable' : 'Disable'}`}
                    checked={is360ViewOn}
                    disabled={isDisabled}
                    label={localize('@sage/xtrem-ui/360-view', '360 view')}
                    labelInline={true}
                    onChange={onChange}
                />
            </div>
        </div>
    );
}
