/**
 * @packageDocumentation
 * @module root
 * */

import { BlockControlObject as Block } from './block/block-control-object';
import { GridRowBlockControlObject as GridRowBlock } from './grid-row-block/grid-row-block-control-object';
import { DetailPanelControlObject as DetailPanel } from './detail-panel/detail-panel-control-object';
import { PageControlObject as Page } from './page/page-control-object';
import { PageFragmentControlObject as PageFragment } from './page/page-fragment-control-object';
import { FragmentFieldsControlObject as FragmentFields } from './fragment-fields/fragment-fields-control-object';
import { SectionControlObject as Section } from './section/section-control-object';
import { StickerControlObject as Sticker } from './sticker/sticker-control-object';
import { TileControlObject as Tile } from './tile/tile-control-object';

export * from './block/block-control-object';
export * from './detail-panel/detail-panel-control-object';
export * from './grid-row-block/grid-row-block-control-object';
export * from './grid-row-block/grid-row-block-types';
export * from './page/page-control-object';
export * from './page/page-fragment-control-object';
export * from './page/page-types';
export * from './section/section-types';
export * from './section/section-control-object';
export * from './sticker/sticker-control-object';
export * from './tile/tile-control-object';
export * from './fragment-fields/fragment-fields-control-object';
export { TileProperties } from './tile/tile-types';

/* Required for the .d.ts generated files */
export { Block, DetailPanel, GridRowBlock, Page, Section, Sticker, Tile, FragmentFields };

export const containers = {
    Block,
    DetailPanel,
    GridRowBlock,
    Page,
    PageFragment,
    Section,
    Sticker,
    Tile,
};
