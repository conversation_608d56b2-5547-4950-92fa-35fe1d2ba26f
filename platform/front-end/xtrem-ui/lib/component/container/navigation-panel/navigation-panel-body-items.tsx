import type { Dict, LocalizeLocale } from '@sage/xtrem-shared';
import * as React from 'react';
import type { ReduxResponsive } from '../../../redux/state';
import type { CollectionValue } from '../../../service/collection-data-service';
import type { TableDecoratorProperties } from '../../decorators';
import { AsyncTableComponent } from '../../field/table/async-table-component';
import { navigationPanelId } from './navigation-panel-types';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import {
    getNavigationPanelTablePropertiesFromPageDefinition,
    getPageDefinitionFromState,
    getPagePropertiesFromPageDefinition,
} from '../../../utils/state-utils';
import { ContextType } from '../../../types';
import type { ValueOrCallback } from '../../../utils/types';
import type { BulkAction } from '../../control-objects';
import type { InternalTableProperties, TableUserSettings } from '../../field/table/table-component-types';
import type { PropertyValueType } from '../../field/reference/reference-types';
import { DASHBOARD_SCREEN_ID } from '../../../utils/constants';
import { useParentElementSize } from '../../../utils/hooks/effects/use-parent-element-size';
import type { DataTypeDetails, FormattedNodeDetails } from '../../../service/metadata-types';
import type { AccessBindings } from '../../../service/page-definition';

export interface NavigationPanelBodyItemsExternalProps {
    isFullWidthGrid?: boolean;
    screenId: string;
    setFieldProperties?: (elementId: string, properties: InternalTableProperties) => void;
    fixedHeight?: number;
}

export interface NavigationPanelBodyItemsProps extends NavigationPanelBodyItemsExternalProps {
    accessBindings: AccessBindings;
    browser: ReduxResponsive;
    bulkActions?: BulkAction[];
    dataTypes: Dict<DataTypeDetails>;
    enumTypes: Dict<string[]>;
    groupByField?: PropertyValueType;
    isAutoSelectEnabled: boolean;
    isSoundDisabled?: ValueOrCallback<any, boolean>;
    locale?: LocalizeLocale;
    nodeTypes: Dict<FormattedNodeDetails>;
    selectedRecordId?: string | null;
    tableProperties?: TableDecoratorProperties;
    tableUserSettings: Dict<TableUserSettings>;
    username?: string;
    value?: CollectionValue;
}

function NavigationPanelBodyItems({
    accessBindings,
    browser,
    bulkActions,
    dataTypes,
    enumTypes,
    fixedHeight,
    groupByField,
    isAutoSelectEnabled,
    isFullWidthGrid,
    isSoundDisabled,
    locale,
    nodeTypes,
    screenId,
    selectedRecordId,
    setFieldProperties,
    tableProperties,
    tableUserSettings,
    username,
    value,
}: NavigationPanelBodyItemsProps): React.ReactElement | null {
    const [ref, { height }] = useParentElementSize();
    if (!tableProperties || !value || !locale) {
        return null;
    }
    const selectedRecords = selectedRecordId ? [selectedRecordId] : undefined;

    // The dropdown actions should be hidden on the split navigation panel, when not accessed on small screen
    const shouldShowCardActions = browser.is.xs || isFullWidthGrid;
    const dropdownActions = shouldShowCardActions ? tableProperties.dropdownActions : undefined;
    const inlineActions = shouldShowCardActions ? tableProperties.inlineActions : undefined;
    return (
        <div ref={ref} style={{ flex: 1, width: '100%' }}>
            <AsyncTableComponent
                accessBindings={accessBindings}
                browser={browser}
                contextType={ContextType.navigationPanel}
                dataTypes={dataTypes}
                elementId={navigationPanelId}
                enumTypes={enumTypes}
                canDragCard={screenId === DASHBOARD_SCREEN_ID}
                fieldProperties={{
                    ...tableProperties,
                    isSoundDisabled,
                    cardView: !isFullWidthGrid,
                    selectedRecords,
                    canSelect: tableProperties.canSelect && isFullWidthGrid && (bulkActions ?? []).length > 0,
                    dropdownActions,
                    inlineActions,
                    isTitleHidden: true,
                }}
                fixedHeight={isFullWidthGrid ? fixedHeight || Math.max(height, 50) : undefined}
                groupByField={groupByField}
                isAutoSelectEnabled={isAutoSelectEnabled}
                isUsingInfiniteScroll={true}
                bulkActions={bulkActions}
                locale={locale}
                selectionMode={(bulkActions ?? []).length > 0 ? 'multiple' : undefined}
                nodeTypes={nodeTypes}
                screenId={screenId}
                setFieldProperties={setFieldProperties}
                tableUserSettings={tableUserSettings}
                username={username}
                validationErrors={[]}
                value={value}
            />
        </div>
    );
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: NavigationPanelBodyItemsExternalProps,
): NavigationPanelBodyItemsProps => {
    const pageDefinition = getPageDefinitionFromState(props.screenId, state);
    if (!pageDefinition || !pageDefinition.navigationPanel) {
        throw new Error(`No page or page navigation panel found for ${props.screenId}.`);
    }
    const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);
    const navigationPanel = state.screenDefinitions[props.screenId].navigationPanel;
    return {
        ...props,
        accessBindings: pageDefinition.accessBindings,
        browser: state.browser,
        bulkActions: pageProperties?.navigationPanel?.bulkActions,
        dataTypes: state.dataTypes,
        enumTypes: state.enumTypes,
        groupByField: navigationPanel?.groupByField,
        isAutoSelectEnabled: pageProperties?.navigationPanel?.isAutoSelectEnabled || false,
        isSoundDisabled: pageProperties?.navigationPanel?.isSoundDisabled,
        locale: state.applicationContext?.locale as LocalizeLocale,
        nodeTypes: state.nodeTypes,
        selectedRecordId: pageDefinition.selectedRecordId,
        tableProperties: getNavigationPanelTablePropertiesFromPageDefinition(pageDefinition),
        tableUserSettings: state.screenDefinitions[props.screenId]?.userSettings?.[navigationPanelId] || {},
        username: state.applicationContext?.login,
        value: pageDefinition.navigationPanel.value,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: NavigationPanelBodyItemsExternalProps,
): Partial<NavigationPanelBodyItemsExternalProps> => {
    return {
        setFieldProperties: (elementId: string, value: InternalTableProperties): any => {
            dispatch(xtremRedux.actions.setFieldProperties(props.screenId, elementId, value));
        },
    };
};

export const ConnectedNavigationPanelBodyItems = connect(mapStateToProps, mapDispatchToProps)(NavigationPanelBodyItems);
