import ButtonMinor from 'carbon-react/esm/components/button-minor';
import * as React from 'react';
import { localize } from '../../../service/i18n-service';

interface ToggleNavigationPanelProps {
    isNavigationPanelOpened: boolean;
    setNavigationPanelIsOpened: (isOpened: boolean) => void;
    parentOnClick?: (parentRef: any) => any;
    parentRef?: any;
}

function ToggleNavigationPanelButton(props: ToggleNavigationPanelProps): React.ReactElement {
    const toggle = (): void => {
        props.setNavigationPanelIsOpened(!props.isNavigationPanelOpened);
        if (props.parentOnClick && props.parentRef) {
            props.parentOnClick(props.parentRef);
        }
    };

    const iconType = props.isNavigationPanelOpened ? 'chevron_left' : 'chevron_right';
    const label = localize('@sage/xtrem-ui/toggle-navigation-panel', 'Toggle Navigation Panel');
    return (
        <div className="e-navigation-panel-toggle">
            <ButtonMinor
                ref={props.parentRef}
                iconType={iconType}
                onClick={toggle}
                buttonType="tertiary"
                size="large"
                iconTooltipPosition="bottom"
                iconTooltipMessage={label}
                aria-label={label}
                data-testid={`nav-panel-toggle-button-${props.isNavigationPanelOpened ? 'close' : 'open'}`}
                data-pendoid="closeNavigationPanel"
            />
        </div>
    );
}

export default ToggleNavigationPanelButton;
