// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Toggle Navigation Panel Button snapshots should render collapse state 1`] = `
.c3 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c3:hover {
  color: #006437;
  background-color: transparent;
}

.c3::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e916";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0 {
  padding-left: var(--spacing400);
  padding-right: var(--spacing400);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: transparent;
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes200);
  min-height: 48px;
  padding: 0px;
  width: 48px;
  min-height: 48px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0 .c2 {
  color: var(--colorsActionMajor500);
}

.c0:hover {
  background: var(--colorsActionMajor600);
  color: var(--colorsActionMajorYang100);
}

.c0:hover .c2 {
  color: var(--colorsActionMajorYang100);
}

.c0 .c2 {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1px;
  height: 20px;
  width: 20px;
}

.c0 .c2 svg {
  margin-top: 0;
}

.c1 {
  border-radius: var(--borderRadius050);
  background: transparent;
  padding: var(--spacing100);
  color: var(--colorsActionMinor500);
  padding-left: var(--spacing200);
  padding-right: var(--spacing200);
}

.c1 .c2 {
  position: absolute;
}

.c1 .c2 {
  color: var(--colorsActionMinor500);
}

.c1:hover {
  color: var(--colorsActionMinorYang100);
  background: var(--colorsActionMinor600);
}

<div>
  <div
    class="e-navigation-panel-toggle"
  >
    <button
      aria-label="Toggle Navigation Panel"
      class="c0 c1"
      data-component="button-minor"
      data-pendoid="closeNavigationPanel"
      data-testid="nav-panel-toggle-button-close"
      draggable="false"
      type="button"
    >
      <span
        aria-hidden="true"
        class="c2 c3"
        color="--colorsActionMajor500"
        data-component="icon"
        data-element="chevron_left"
        data-role="icon"
        font-size="small"
        type="chevron_left"
      />
    </button>
  </div>
</div>
`;

exports[`Toggle Navigation Panel Button snapshots should render expand state 1`] = `
.c3 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c3:hover {
  color: #006437;
  background-color: transparent;
}

.c3::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e917";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0 {
  padding-left: var(--spacing400);
  padding-right: var(--spacing400);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: transparent;
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes200);
  min-height: 48px;
  padding: 0px;
  width: 48px;
  min-height: 48px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0 .c2 {
  color: var(--colorsActionMajor500);
}

.c0:hover {
  background: var(--colorsActionMajor600);
  color: var(--colorsActionMajorYang100);
}

.c0:hover .c2 {
  color: var(--colorsActionMajorYang100);
}

.c0 .c2 {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1px;
  height: 20px;
  width: 20px;
}

.c0 .c2 svg {
  margin-top: 0;
}

.c1 {
  border-radius: var(--borderRadius050);
  background: transparent;
  padding: var(--spacing100);
  color: var(--colorsActionMinor500);
  padding-left: var(--spacing200);
  padding-right: var(--spacing200);
}

.c1 .c2 {
  position: absolute;
}

.c1 .c2 {
  color: var(--colorsActionMinor500);
}

.c1:hover {
  color: var(--colorsActionMinorYang100);
  background: var(--colorsActionMinor600);
}

<div>
  <div
    class="e-navigation-panel-toggle"
  >
    <button
      aria-label="Toggle Navigation Panel"
      class="c0 c1"
      data-component="button-minor"
      data-pendoid="closeNavigationPanel"
      data-testid="nav-panel-toggle-button-open"
      draggable="false"
      type="button"
    >
      <span
        aria-hidden="true"
        class="c2 c3"
        color="--colorsActionMajor500"
        data-component="icon"
        data-element="chevron_right"
        data-role="icon"
        font-size="small"
        type="chevron_right"
      />
    </button>
  </div>
</div>
`;
