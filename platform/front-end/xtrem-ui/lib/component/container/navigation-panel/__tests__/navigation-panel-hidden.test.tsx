jest.mock('../../../field/text/async-text-component');
jest.mock('../../../field/numeric/async-numeric-component');
jest.mock('../../../field/label/async-label-component');
jest.mock('../../../field/image/async-image-component');
jest.mock('../../../field/reference/async-reference-component');

import type { Dict } from '@sage/xtrem-shared';
import { queryByTestId, render } from '@testing-library/react';
import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import {
    applyActionMocks,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';
import type { Page } from '../../../../service/page';
import type { XtremAppState } from '../../../../redux';
import type { PageProperties } from '../../container-properties';
import ConnectedNavigationPanel from '../navigation-panel-component';
import { FieldKey } from '../../../types';
import {
    createNavigationPanelValue,
    createNavigationTableProperties,
} from '../../../../service/navigation-panel-service';
import type { PageDefinition } from '../../../../service/page-definition';
import { navigationPanelId } from '../navigation-panel-types';

describe('NavigationPanelComponent (with hidden nested component)', () => {
    const screenId = 'TestPage';
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let mockState: XtremAppState;

    beforeEach(async () => {
        mockState = getMockState();
        mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        const pageProperties: Dict<PageProperties<Page>> = (mockState.screenDefinitions[
            screenId
        ].metadata.uiComponentProperties = {});
        pageProperties[screenId] = {
            node: '@sage/xtrem-sales/SalesOrder',
            navigationPanel: {
                isHeaderHidden: false,
                listItem: {
                    title: {
                        defaultUiProperties: { bind: 'field1' },
                        properties: { bind: 'field1' },
                        type: FieldKey.Text,
                    },
                    line2: {
                        defaultUiProperties: { bind: 'field2' },
                        properties: {
                            bind: 'field2',
                            isHidden(value, data) {
                                return data?._id === '2';
                            },
                        },
                        type: FieldKey.Text,
                    },
                    line3: {
                        defaultUiProperties: { bind: 'field3' },
                        properties: { bind: 'field3' },
                        type: FieldKey.Text,
                    },
                },
                menuType: 'dropdown',
            },
        };

        const tableProperties = await createNavigationTableProperties(
            screenId,
            '@sage/xtrem-sales/SalesOrder',
            mockState.screenDefinitions[screenId] as PageDefinition,
            {},
            {},
            mockState.path || undefined,
        );
        mockState.screenDefinitions[screenId].metadata.uiComponentProperties[navigationPanelId] = tableProperties;
        mockState.screenDefinitions[screenId].navigationPanel = {
            isHeaderHidden: false,
            isOpened: true,
            isHidden: false,
            isRefreshing: false,
            value: createNavigationPanelValue(
                screenId,
                tableProperties,
                [
                    {
                        _id: '1',
                        field1: 'TestField #1-1',
                        field2: 'TestField #1-2',
                        field3: 'TestField #1-3',
                    },
                    {
                        _id: '2',
                        field1: 'TestField #2-1',
                        field2: 'TestField #2-2',
                        field3: 'TestField #2-3',
                    },
                    {
                        _id: '3',
                        field1: 'TestField #3-1',
                        field2: 'TestField #3-2',
                        field3: 'TestField #3-3',
                    },
                ],
                {},
                'en-US',
            ),
        };

        mockStore = getMockStore(mockState);
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    describe('connected', () => {
        it('should render second item with hidden line2 property', () => {
            render(
                <Provider store={mockStore}>
                    <ConnectedNavigationPanel screenId={screenId} selectedRecordId={null} />
                </Provider>,
            );

            const containers: HTMLDivElement[] = [...(document.querySelectorAll('.e-card') as any)].map(
                element => element as HTMLDivElement,
            );
            containers.forEach((container, index) => {
                const element = queryByTestId(container, 'e-text-field e-field-bind-field2');
                if (index === 1) {
                    expect(element).not.toBeInTheDocument();
                } else {
                    expect(element).toBeInTheDocument();
                }
            });
        });
    });
});
