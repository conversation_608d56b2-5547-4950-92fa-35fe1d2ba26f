import { noop } from 'lodash';
import * as React from 'react';
import ToggleNavigationPanelButton from '../toggle-navigation-panel-button';
import { fireEvent, render } from '@testing-library/react';

describe('Toggle Navigation Panel Button', () => {
    let toggleSpy: any;
    let parentOnClickSpy: any;

    beforeEach(() => {
        toggleSpy = jest.fn();
        parentOnClickSpy = jest.fn();
    });
    afterEach(() => jest.resetAllMocks());
    describe('snapshots', () => {
        it('should render collapse state', () => {
            const { container } = render(
                <ToggleNavigationPanelButton isNavigationPanelOpened={true} setNavigationPanelIsOpened={toggleSpy} />,
            );
            expect(container).toMatchSnapshot();
        });
        it('should render expand state', () => {
            const { container } = render(
                <ToggleNavigationPanelButton isNavigationPanelOpened={false} setNavigationPanelIsOpened={toggleSpy} />,
            );
            expect(container).toMatchSnapshot();
        });
    });
    describe('interactions', () => {
        it('should toggle on button click', () => {
            const { container, rerender } = render(
                <ToggleNavigationPanelButton isNavigationPanelOpened={true} setNavigationPanelIsOpened={toggleSpy} />,
            );
            fireEvent.click(container.querySelector('button')!);
            rerender(
                <ToggleNavigationPanelButton isNavigationPanelOpened={false} setNavigationPanelIsOpened={toggleSpy} />,
            );
            fireEvent.click(container.querySelector('button')!);
            expect(toggleSpy.mock.calls[0][0]).toBe(false);
            expect(toggleSpy.mock.calls[1][0]).toBe(true);
            expect(toggleSpy).toHaveBeenCalledTimes(2);
        });
        it('should call parentOnClick callback on button click', () => {
            const { container } = render(
                <ToggleNavigationPanelButton
                    isNavigationPanelOpened={true}
                    setNavigationPanelIsOpened={toggleSpy}
                    parentOnClick={parentOnClickSpy}
                    parentRef={noop}
                />,
            );
            fireEvent.click(container.querySelector('button')!);

            expect(parentOnClickSpy).toHaveBeenCalled();
        });
    });
});
