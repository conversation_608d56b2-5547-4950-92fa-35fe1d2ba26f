import { getMockState, getMockPageDefinition, getMockStore, userEvent } from '../../../../__tests__/test-helpers';

import { cleanup, findByRole, fireEvent, render, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { ThemeProvider } from 'styled-components';
import ConnectedNavigationPanel from '../navigation-panel-component';
import type { NavigationPanelExternalProps } from '../navigation-panel-component';
import baseTheme from 'carbon-react/esm/style/themes/sage';
import * as React from 'react';
import { FieldKey } from '../../../types';
import { CollectionValue } from '../../../../service/collection-data-service';
import type { NestedField } from '../../../nested-fields';
import { navigationPanelId } from '../navigation-panel-types';
import type { InternalTableProperties, TableProperties } from '../../../field/table/table-component-types';
import * as stateUtils from '../../../../utils/state-utils';
import type { PageDecoratorProperties } from '../../page/page-decorator';
import type { Dict } from '@sage/xtrem-shared';
import type { ScreenBaseDefinition } from '../../../../service/screen-base-definition';
import type { PageDefinition } from '../../../../service/page-definition';
import type { PageNavigationPanel } from '../../../control-objects';
import type { ScreenBase } from '../../../../service/screen-base';
import * as dialogService from '../../../../service/dialog-service';
import * as graphqlService from '../../../../service/graphql-service';
import type { FormattedNodeDetails } from '../../../../service/metadata-types';
import '@testing-library/jest-dom';
import { GraphQLKind } from '../../../../types';
import { COLUMN_ID_ROW_SELECTION } from '../../../../utils/ag-grid/ag-grid-column-config';

jest.useFakeTimers();
jest.setTimeout(20000);

describe('NavigationPanelComponent', () => {
    let state;
    let mockStore;
    let partialPageDefinition: Partial<ScreenBaseDefinition>;
    let nodeApi: {
        asyncOperations: Dict<{ start: jest.MockInstance<any, any[]> }>;
    };
    let value: CollectionValue;

    const screenId = 'TestPage';
    const nodeTypes: Dict<FormattedNodeDetails> = {
        AnyNode: {
            name: 'AnyNode',
            title: 'Any Node',
            packageName: '@sage/xtrem-test',
            properties: {
                _id: { kind: GraphQLKind.Scalar, type: 'Id' },
                reference: { kind: 'OBJECT', type: 'AnotherType' },
                checkbox: { kind: GraphQLKind.Scalar, type: 'Boolean' },
                date: { kind: GraphQLKind.Scalar, type: 'Date' },
                label: { kind: GraphQLKind.Scalar, type: 'String' },
                select: { kind: GraphQLKind.Scalar, type: 'String' },
                text: { kind: GraphQLKind.Scalar, type: 'String' },
                link: { kind: GraphQLKind.Scalar, type: 'String' },
                numeric: { kind: GraphQLKind.Scalar, type: 'Float' },
                progress: { kind: GraphQLKind.Scalar, type: 'Int' },
            },
            mutations: {},
        },
        AnotherType: {
            name: 'AnotherType',
            title: 'AnotherType',
            packageName: '@sage/xtrem-test',
            properties: {
                _id: { kind: GraphQLKind.Scalar, type: 'Id' },
                status: { kind: GraphQLKind.Scalar, type: 'String' },
                title: { kind: GraphQLKind.Scalar, type: 'String' },
                code: { kind: GraphQLKind.Scalar, type: 'String' },
            },
            mutations: {},
        },
    };

    beforeEach(() => {
        value = new CollectionValue({
            screenId,
            elementId: '$navigationPanel',
            isTransient: true,
            hasNextPage: false,
            orderBy: [{ text: 1 }],
            columnDefinitions: [
                [
                    {
                        type: FieldKey.Text,
                        properties: { bind: 'field1' },
                        defaultUiProperties: {},
                    } as NestedField<any, any>,
                ],
            ],
            nodeTypes,
            nodes: ['@sage/xtrem-test/AnyNode'],
            filter: [undefined],
            initialValues: [
                {
                    field1: 'test1',
                    _id: '1',
                    __cursor: '#1',
                },
                {
                    field1: 'test2',
                    _id: '2',
                    __cursor: '#2',
                },
                {
                    field1: 'test3',
                    _id: '3',
                    __cursor: '#3',
                },
            ],
            locale: 'en-US',
        });
        partialPageDefinition = {
            navigationPanel: {
                isHeaderHidden: false,
                isHidden: false,
                isOpened: true,
                isRefreshing: false,
                value,
            },
            selectedRecordId: '1',
        };
    });
    afterEach(() => {
        jest.clearAllMocks();
        cleanup();
    });

    const renderNavPanel = (
        props: NavigationPanelExternalProps,
        pageProperties: Partial<PageDecoratorProperties<any>> = {},
        navigationPanelProperties: Partial<PageNavigationPanel<ScreenBase>> = {},
        pageDef: Partial<PageDefinition> = {},
        filter?: any,
        navigationTablePanelProperties: Partial<InternalTableProperties<ScreenBase>> = {},
    ) => {
        const bulkActions = navigationPanelProperties.bulkActions ?? [];
        nodeApi = {
            asyncOperations: bulkActions.reduce((acc, curr) => {
                acc[curr.mutation] = {
                    start: jest.fn(() => ({
                        execute: () => Promise.resolve(),
                    })),
                };
                return acc;
            }, {}),
        };
        if (filter) {
            (value as any).filter = filter;
        }
        const pageDefinition = getMockPageDefinition(
            screenId,
            { ...partialPageDefinition, ...pageDef },
            {
                uiComponentProperties: {
                    [navigationPanelId]: {
                        hasSearchBoxMobile: true,
                        node: '@sage/xtrem-test/AnyNode',
                        columns: [
                            {
                                type: FieldKey.Text,
                                properties: { bind: 'field1' },
                                defaultUiProperties: {},
                            } as NestedField<any, any>,
                        ],
                        canSelect: bulkActions.length > 0,
                        filter,
                        ...navigationTablePanelProperties,
                    } as TableProperties,
                    [screenId]: {
                        node: '@sage/xtrem-test/AnyNode',
                        navigationPanel: {
                            listItem: {
                                title: {
                                    type: FieldKey.Text,
                                    properties: { bind: 'field1' },
                                    defaultUiProperties: {},
                                },
                            },
                            ...navigationPanelProperties,
                        },
                        ...pageProperties,
                    } as any,
                },
            },
        );
        jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() =>
            getMockPageDefinition('TestPage', pageDefinition),
        );

        state = getMockState({
            nodeTypes,
            screenDefinitions: {
                [screenId]: pageDefinition,
            },
        });
        mockStore = getMockStore(state);

        const utils = render(
            <ThemeProvider theme={baseTheme}>
                <Provider store={mockStore}>
                    <ConnectedNavigationPanel {...props} />
                </Provider>
            </ThemeProvider>,
        );

        const getRowSelectionCheckbox = async (rowIndex: number): Promise<Element | null> => {
            const selector = `.ag-row[row-index="${rowIndex}"] [col-id^="${COLUMN_ID_ROW_SELECTION}"] .ag-selection-checkbox input`;
            await waitFor(() => {
                expect(document.querySelector(selector)).toBeInTheDocument();
            });
            return utils.container.querySelector(selector);
        };

        const toggleRowSelection = async (rowNumber: number): Promise<void> => {
            const checkbox = await getRowSelectionCheckbox(rowNumber);
            if (checkbox) {
                fireEvent.click(checkbox);
            }
        };

        const clickOnBulkAction = async (mutationName: string): Promise<void> => {
            const regexp = new RegExp(`bulk-action-label-${mutationName}`);
            const buttonContainer = await utils.findByTestId(regexp);
            const button = await findByRole(buttonContainer, 'button');
            fireEvent.click(button);
        };

        const toggleSelectAll = async () => {
            const selectAllCheckbox = document.querySelector('.ag-header-select-all input');
            if (selectAllCheckbox) {
                fireEvent.click(selectAllCheckbox);
            }
        };

        const waitForAllRowsToBeUnselected = async () => {
            await waitFor(() => {
                expect(Array.from(document.querySelectorAll('.ag-cell-wrapper .ag-checked')).length).toBe(0);
            });
        };

        return {
            ...utils,
            nodeApi,
            toggleRowSelection,
            clickOnBulkAction,
            toggleSelectAll,
            waitForAllRowsToBeUnselected,
        };
    };

    describe('Split view navigation panel', () => {
        afterEach(() => {
            cleanup();
        });
        it('Should render nav panel in split view with 3 cards', async () => {
            const { getAllByTestId, getByText, getByTestId } = renderNavPanel({ screenId, selectedRecordId: '1' });
            return waitFor(() => {
                expect(getByTestId('e-page-navigation-panel-split-view')).toBeInTheDocument();
                expect(getAllByTestId('e-card').length).toBe(3);
                expect(getByText('test1')).toBeDefined();
                expect(getByText('test2')).toBeDefined();
                expect(getByText('test3')).toBeDefined();
            });
        });
        it('Should render search in split view', () => {
            const { getByTestId } = renderNavPanel({ screenId, selectedRecordId: '1' });
            expect(getByTestId('e-table-field-mobile-search')).toBeInTheDocument();
        });
    });

    describe('Full width navigation panel', () => {
        beforeEach(() => {
            partialPageDefinition.selectedRecordId = null;
        });
        afterEach(() => {
            cleanup();
            jest.clearAllTimers();
        });

        it('should render nav panel with bulk actions', async () => {
            const wrapper = renderNavPanel(
                { screenId, selectedRecordId: null },
                {},
                { bulkActions: [{ title: 'Action', mutation: 'mutation' }] },
            );
            const bulkBarSelector = '.e-page-navigation-panel-bulk-actions-bar';
            const clearSelectionSelector = '.e-page-navigation-panel-bulk-actions-bar-clear-selection';
            const mutationButtonSelector = 'e-table-field bulk-action-label-action bulk-action-bind-mutation';
            const selectedItemsSelector = '.e-page-navigation-panel-bulk-actions-bar-selected-items';
            const rowCheckboxSelector = (rowIndex: number) =>
                `[row-index="${rowIndex - 1}"] [col-id^="${COLUMN_ID_ROW_SELECTION}"] input`;
            await waitFor(() => {
                expect(wrapper.baseElement.querySelector(rowCheckboxSelector(1))).toBeInTheDocument();
            });
            await userEvent.click(wrapper.baseElement.querySelector(rowCheckboxSelector(1))!);
            await waitFor(() => {
                expect(wrapper.baseElement.querySelector(bulkBarSelector)).toBeInTheDocument();
                expect(wrapper.baseElement.querySelector(selectedItemsSelector)).toHaveTextContent('Items selected: 1');
                expect(wrapper.getByTestId(mutationButtonSelector)).toBeInTheDocument();
                expect(wrapper.getByTestId(mutationButtonSelector)).toHaveTextContent('Action');
                expect(wrapper.baseElement.querySelector(clearSelectionSelector)).toBeInTheDocument();
            });
            await userEvent.click(wrapper.baseElement.querySelector(clearSelectionSelector)!);
            await waitFor(() => expect(wrapper.baseElement.querySelector(bulkBarSelector)).toBeNull());
        });

        it('should apply right filter to bulk actions when selecting all rows', async () => {
            jest.spyOn(dialogService, 'confirmationDialog').mockResolvedValue({});
            const mockTotalRowCount = jest
                .spyOn(graphqlService, 'fetchCollectionDataCount')
                .mockImplementation(async () => 1000);
            const filter = { field1: { _regex: 'test' } };
            const utils = renderNavPanel(
                {
                    screenId,
                    selectedRecordId: null,
                },
                {},
                {
                    bulkActions: [{ mutation: 'action', title: 'Action' }],
                },
                {
                    page: {
                        $: {
                            graph: {
                                node: function _(n: string) {
                                    return n === '@sage/xtrem-test/AnyNode' ? nodeApi : {};
                                },
                            },
                        },
                    } as any,
                },
                filter,
            );
            // Select first row
            await utils.toggleRowSelection(1);
            await utils.clickOnBulkAction('action');
            await waitFor(() => {
                expect(utils.nodeApi.asyncOperations.action.start).toHaveBeenCalledTimes(1);
            });
            // Only first row is selected => use "_in" clause
            expect(JSON.parse(utils.nodeApi.asyncOperations.action.start.mock.calls[0][1].filter)).toEqual({
                _id: { _in: ['2'] },
            });
            // Select all rows
            await utils.toggleSelectAll();
            await waitFor(() => {
                // Check that the count query has been fired
                expect(mockTotalRowCount).toHaveBeenCalledTimes(1);
                const firstCallParams = mockTotalRowCount.mock.calls[0][0];
                // Check that the count query contains the right filter
                expect(JSON.parse(firstCallParams.filter)).toEqual(filter);
                expect(firstCallParams.rootNode).toBe('@sage/xtrem-test/AnyNode');
            });
            await utils.clickOnBulkAction('action');
            await waitFor(() => {
                expect(utils.nodeApi.asyncOperations.action.start).toHaveBeenCalledTimes(2);
            });
            // Check that filter and "_nin" clause are used
            expect(JSON.parse(utils.nodeApi.asyncOperations.action.start.mock.calls[1][1].filter)).toEqual({
                ...filter,
                _id: { _nin: [] },
            });
            await utils.waitForAllRowsToBeUnselected();
            // Select all rows but first
            await utils.toggleSelectAll();
            await utils.toggleRowSelection(1);
            await utils.clickOnBulkAction('action');
            await waitFor(() => {
                expect(utils.nodeApi.asyncOperations.action.start).toHaveBeenCalledTimes(3);
            });
            // Check that filter and "_nin" clause are used
            expect(JSON.parse(utils.nodeApi.asyncOperations.action.start.mock.calls[2][1].filter)).toEqual({
                ...filter,
                _id: { _nin: ['2'] },
            });
        });

        it('should render with a title', () => {
            mockStore = getMockStore(state);
            const wrapper = renderNavPanel({ screenId, selectedRecordId: null }, { title: 'My test title' });
            expect(wrapper.baseElement.querySelector('.e-header-title')).toHaveTextContent('My test title');
        });
        it('should render with the pluralObjectType if both pluralObjectType and title are defined', () => {
            mockStore = getMockStore(state);
            const wrapper = renderNavPanel(
                { screenId, selectedRecordId: null },
                { title: 'My test title', objectTypePlural: 'Items' },
            );
            expect(wrapper.baseElement.querySelector('.e-header-title')).toHaveTextContent('Items');
        });

        it('Should NOT render search in full width', async () => {
            const { queryByTestId } = renderNavPanel({ screenId, selectedRecordId: null });

            await waitFor(() => {
                expect(queryByTestId('e-table-field-mobile-search')).not.toBeInTheDocument();
            });
        });

        it('Should NOT render navigation panel header', async () => {
            partialPageDefinition.navigationPanel!.isHeaderHidden = true;
            const { queryByTestId } = renderNavPanel({ screenId, selectedRecordId: null });
            await waitFor(() => {
                expect(queryByTestId('e-page-navigation-panel-full-width-header')).not.toBeInTheDocument();
            });
        });

        it('Should render nav panel in full width view with a table with 3 records because no record id is selected', () => {
            const { container, getByText, getByTestId } = renderNavPanel({ screenId, selectedRecordId: null });
            return waitFor(() => {
                expect(getByTestId('e-page-navigation-panel-full-width')).toBeInTheDocument();
                expect(container.getElementsByClassName('ag-center-cols-container')[0].childElementCount).toBe(3);
                expect(getByText('test1')).toBeDefined();
                expect(getByText('test2')).toBeDefined();
                expect(getByText('test3')).toBeDefined();
            });
        });
    });
});
