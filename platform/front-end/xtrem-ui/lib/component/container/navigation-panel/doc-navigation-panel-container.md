PATH: XTREEM/Page/Navigation+Panel

## Introduction

The navigation panel component is a container which is declared inside the page decorator that will display a list of item cards on the left side of the page. On top of the navigation panel, there is a search box where the user can query for a specific value in all the item card's fields. When filtering is enabled, it is also possible to filter for a value only in a specific item card's field. The items are order by `titleLine` and a second field. This second field will be `line2` or `titleLineRight` if present, taking `line2` precedence over `titleLineRight`. Moreover, an options menu can also be rendered, either visually represented by a dropdown or a toggle buttons menu, in order to also filter item cards or to redirect to another page.

## Example:

```ts
@ui.decorators.page<NavigationPanelPage>({
    navigationPanel: {
        canFilter: true,
        isFirstLetterSeparatorHidden: true,
        optionsMenu: [
            { title: 'FirstEntryTitle', page: '@sage/navigation-panel/AnotherNavigationPanelPage' },
            {
                title: 'SecondEntryTitle',
                graphQLFilter: (storage, queryParameters) => ({
                    _or: [
                        { title: { _regex: String(storage.get('navigationPanel')), _options: 'i' } },
                        { description: { _regex: String(queryParameters['navigationPanel']), _options: 'i' } },
                    ],
                }),
            },
            {
                title: 'ThirdEntryTitle',
                graphQLFilter: {
                    _or: [
                        { title: { _regex: 'navigationPanel', _options: 'i' } },
                        { description: { _regex: 'navigationPanel', _options: 'i' } },
                    ],
                },
            },
        ],
        listItem: {
            titleLine: ui.nestedFields.reference<any, any, any>({ bind: 'referenceNode', valueField: 'referenceField' }),
            titleLineRight: ui.nestedFields.text({
                bind: 'textNode',
                title: 'textTitle',
            }),
            line2: ui.nestedFields.image({ bind: 'imageNode', title: 'imageTitle'}),
            line2Right: ui.nestedFields.text({
                bind: 'textNode',
                title: 'textTitle',
            }),
            line3: ui.nestedFields.numeric({ bind: 'simpleNode', title: 'numericTitle' }),
            line4: ui.nestedFields.numeric({ bind: 'simpleNode', title: 'numericTitle' }),
            image: ui.nestedFields.image({ bind: 'logo', title: 'Logo', placeholderMode: 'Initials' }),
        },
        onOptionsMenuValueChange(mainFilterValue: string, selectedFilter?: string) {
            console.log(`Doing something when the dropdown value changed for ${mainFilterValue} and ${selectedFilter}`);
        },
        onSelect(item: any) {
            console.log(`Doing something when the item is selected for ${item}`);
            return true; // To stop the event propagation
        },
    },
})
```
## With Callback

```ts
@ui.decorators.page<NavigationPanelOptionsCallback, ShowCaseProduct>({
    authorizationCode: 'NAVPANEL',
    module: 'show-case',
    title: 'Navigation Panel with options callback',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            titleLine: ui.nestedFields.text({ bind: 'product' }),
            titleLineRight: ui.nestedFields.label({ bind: 'category' }),
            line2: ui.nestedFields.reference({
                bind: 'provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
            }),
            line2Right: ui.nestedFields.text({
                bind: 'textNode',
                title: 'textTitle',
            }),
            line3: ui.nestedFields.date({ bind: 'releaseDate' }),
            line3Right: ui.nestedFields.date({ bind: 'endingDate' }),
            line4: ui.nestedFields.numeric({ bind: 'amount' }),
            line4Right: ui.nestedFields.numeric({ bind: 'discount' }),
            line5: ui.nestedFields.numeric({ bind: 'listPrice' }),
            line5Right: ui.nestedFields.text({ bind: 'description' }),
        },
        orderBy: {
            releaseDate: -1,
        },
        async optionsMenu(graph, storage, queryParam, username, userCode, serviceOptions) {
            const result = await graph.node('@sage/xtrem-show-case/ShowCaseProvider').query({edges: {node: {textField: true}}}).execute();
            const options = result.edges.map((edge) => {
                return {
                    title: `Only from ${edge.node.textField}`,
                    graphQLFilter: { provider: { textField: { _eq: edge.node.textField} } },
                }
            });

            if (serviceOptions['myServiceOption']){
                options.push({
                    title: 'Some additional option',
                    graphQLFilter: {},
                });
            }

            return [
                {
                    title: 'All',
                    graphQLFilter: {},
                },
                ...options
            ]
        }
    },
})
```

### Display decorator properties:

-   **canFilter**: Whether the user can filter the navigation panel items. Defaults to true.
-   **isHeaderHidden**: Whether the navigation panel header is hidden. Defaults to false.
-   **bulkActions**: Actions that will be displayed as buttons on the main list's bulk action bar. The following decorator properties are available:
    - **buttonType**: Type of button, according with Carbon's API. Can be 'primary', 'secondary' or 'tertiary'. Optional, defaults to 'primary'.
    - **icon**: Optional icon displayed in the button.
    - **title**: The title of the button.
    - **isDestructive**: If set the action's icon and text are rendered in red. Optional, defaults to false.
    - **mutation**: Name of the mutation that will be triggered when the button is clicked.
-   **listItem**: Fields that will be rendered inside each navigation panel item card. See details below.
-   **optionsMenu**: Set of predefined graphql filters displayed above the the navigation panel. This list can be also be displayed as callback. Optional property. When defined with a callback, it is called with the following properties:
    - **graph**: A read-only GraphQL API wrapper object
    - **storage**: A read-only local storage wrapper
    - **queryParameters**: Object with the pages query parameters
    - **username**: The username of the current user.
    - **userCode**: The session username can be used it the application has a username that is different from the login user.
-   **optionsMenuType**: Defines how options menu should be displayed. The options menu can be displayed as a dropdown, toggle button or tabs menu. Default is dropdown.
-   **orderBy**: Ordered list of fields which the navigation panel items will be initially ordered/sorted by. Use 1 for ascending order and -1 for descending order. Properties of references are admitted. Optional property.
-   **isFirstLetterSeparatorHidden**: Whether the separator element which is displayed when the first letter of the title element in the list should be hidden. Optional property.
-   **isAutoSelectEnabled**: Automatically select a search results if this only yielded a single result and the search criteria matches any of the field values of the navigation panel item. Optional property, defaults to false. A success sound will play if there's a single match, or an error sound will play if there's no single match.
-   **emptyStateClickableText**: string; Sets a complementary text link when no data is available in the table
-   **emptyStateText**: Sets a text when no data is available in the table
-   **dropdownActions**: A list of actions that are displayed at the end of the table as a drop-down menu. The configuration is similar to the [table record actions](./Table+Field#TableField-Recordactionproperties). The navigation panel can be refreshed after the action successfully executed using the following action decorator property: `refreshesMainList?: 'record' | 'list';` The refresh can be limited to a record (e.g in case of a property change) or the entire table can be refreshed (e.g a record was deleted and no longer present in the dataset).
-   **inlineActions**: A list of actions that are displayed at the end of the table as a icon buttons. The configuration is similar to the [table record actions](./Table+Field#TableField-Recordactionproperties). The navigation panel can be refreshed after the action successfully executed using the following action decorator property: `refreshesMainList?: 'record' | 'list';` The refresh can be limited to a record (e.g in case of a property change) or the entire table can be refreshed (e.g a record was deleted and no longer present in the dataset).
-   **isSoundDisabled**: Specifies whether the sound output of the navigation panel should be disabled/silenced. Defaults to false.


### Event handler decorator properties:

-   **onOptionsMenuValueChange**: Triggered when the dropdown or the toggle buttons menu value is changed. Receives as arguments the text value inserted by the user and options menu new selected value. If the event is consumed, it should return true to stop propagation.

-   **onSelect**: Triggered when selecting a item from the list. Receives as argument an object with the properties in the item. If the event is consumed, it should return true to stop propagation.

### Options Menu decorator properties:

-   **title**: The title of the menu item. The title can be provided as a string, or a callback function returning a string. It is automatically picked up by the i18n engine and externalized for translation.
-   **page**: Redirects the user to the selected Page definition. E.G @sage/em-project-management/Employees. Optional property.
-   **graphQLFilter**: Applies a filter to the list. Can be either a graphQLFilter object or a method that receives session storage and query parameters objects and then returns the graphQLFilter object. Optional property.

-   **onEmptyStateLinkClick**: Function to be executed when the clickable text is clicked

-   **Note**: Either page or graphQLFilter property can be defined for a optionsMenu item.

### List item card definition
The following types can be used as nested fields for the card body (`title`, `titleRight`, `line2`, `line2Right` and etc): 'checkbox', 'icon', 'image', 'label', 'link', 'numeric', 'progress', 'reference', 'text', 'date'.

Card body properties:
-   **title**: Title of the item card.
-   **titleRight**: Support for title, being displayed at its right. Optional property.
-   **line2**: Text that is going to be placed under the `title` in the item card. Optional property.
-   **line2Right**: Text that is going to be placed under the `titleRight` property in the item card. Optional property.
-   **line3**: Text that is going to be placed under the `line2` in the item card. Optional property.
-   **line3Right**: Text that is going to be placed under the `line2Right` property in the item card. Optional property.
-   **line4**: Text that is going to be placed under the `line3` in the item card. Optional property.
-   **line4Right**: Text that is going to be placed under the `line3Right` property in the item card. Optional property.
-   **line5**: Text that is going to be placed under the `line4` in the item card. Optional property.
-   **line5Right**: Text that is going to be placed under the `line4Right` property in the item card. Optional property.


Special card properties:
-   **image**: An image or icon field is displayed on the left side of the card. Furthermore, this image will be displayed in the main section's header, next to the title. It is positioned vertically to the center. Optional property.
-   **progressBar**: A progress field which is displayed below the card content. Optional property.


#### List item definition for full-width navigation
To add additional columns to the full-width navigation panel, additional entries with custom keys can be added to the list item object. Nested fields defined with custom keys are rendered in combination with the standard keys following the definition order on the full with navigation panel. Nested fields defined with custom keys are not displayed in mobile or in split view mode.

#### Considerations with nestedFields link
When there is a nestedFields.link in the navigation panel, it will display as plain text instead of having the regular link style.

## Sandbox

Check out element on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/NavigationPanel).
