import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import type { ReduxResponsive } from '../../../redux/state';
import { localize } from '../../../service/i18n-service';
import type { Page } from '../../../service/page';
import type { PageDefinition } from '../../../service/page-definition';
import { getRouter } from '../../../service/router';
import type { ScreenBase } from '../../../service/screen-base';
import { NEW_PAGE, DASHBOARD_SCREEN_ID } from '../../../utils/constants';
import { getPageDefinitionFromState, getPagePropertiesFromState } from '../../../utils/state-utils';
import type { ValueOrCallback } from '../../../utils/types';
import { getFieldTitle } from '../../field/carbon-helpers';
import type { InternalTableProperties } from '../../field/table/table-component-types';
import type { PageActionControlObject } from '../../page-action/page-action-control-object';
import { PageTitle } from '../../ui/page-title';
import { TableCreateActions } from '../../ui/table-shared/table-create-actions/table-create-actions';
import { BusinessAction } from '../footer/business-action';
import type { PageNavigationPanel } from '../page/page-types';
import { ConnectedNavigationPanelBodyItems } from './navigation-panel-body-items';
import { navigationPanelId } from './navigation-panel-types';
import BackArrowButton from '../page/header-back-arrow';
import { PageHeaderInsightsButton } from '../../ui/insights-button';

export interface NavigationPanelExternalProps {
    screenId: string;
    selectedRecordId: string | null;
    fixedHeight?: number;
}

export interface NavigationPanelProps extends NavigationPanelExternalProps {
    browser?: ReduxResponsive;
    createAction?: ValueOrCallback<ScreenBase, PageActionControlObject | PageActionControlObject[]>;
    isNavigationPanelHidden: boolean;
    isNavigationPanelOpened: boolean;
    isNewPage: boolean;
    isRecordSelected: boolean;
    isSelectAllChecked: boolean;
    navigationPanelDefinition: PageNavigationPanel<Page>;
    selectedRecordIds: string[];
    setNavigationPanelIsHeaderHidden: (isHeaderHidden: boolean) => void;
    setNavigationPanelIsOpened: (isOpened: boolean) => void;
    title: string;
    shouldDisplayBackArrow: boolean;
}

interface MainListBodyStyle {
    height?: string;
}

export interface NavigationPanelState {
    mainListBodyStyle: MainListBodyStyle;
}

export class NavigationPanel extends React.Component<NavigationPanelProps, NavigationPanelState> {
    private togglePanelButtonRef = React.createRef<HTMLButtonElement>();

    private mainListPageRef = React.createRef<HTMLElement>();

    private mainListTitleRef = React.createRef<HTMLDivElement>();

    private mainListGuideRef = React.createRef<HTMLDivElement>();

    private guideObserver: MutationObserver | null = null;

    constructor(props: NavigationPanelProps) {
        super(props);
        this.state = { mainListBodyStyle: {} };
    }

    componentDidMount(): void {
        /*
        TODO: might be better dispatch this action (or similar)
        in an earlier phase (maybe at each xtrem page init, mount...)
        and let the navigation panel consume that value
        instead of accessing mutable navigationPanelDefinition prop
        */
        this.props.setNavigationPanelIsHeaderHidden(!!this.props.navigationPanelDefinition.isHeaderHidden);

        /**
         * Initializes height of the navigation panel body and adds a mutation observer to ensure the height is updated
         * when Pendo guide is added/removed as a child node within the navigation panel body.
         */
        this.setState({ mainListBodyStyle: this.getNavigationPanelBodyStyle() });
        if (this.mainListGuideRef.current) {
            this.guideObserver = new MutationObserver(() => {
                this.setState({ mainListBodyStyle: this.getNavigationPanelBodyStyle() });
            });
            this.guideObserver.observe(this.mainListGuideRef.current as HTMLDivElement, { childList: true });
        }
    }

    componentDidUpdate(prevProps: NavigationPanelProps): void {
        if (this.props.isNavigationPanelOpened !== prevProps.isNavigationPanelOpened) {
            this.setChevronFocus();
        }
    }

    componentWillUnmount(): void {
        this.guideObserver?.disconnect();
        this.guideObserver = null;
    }

    private readonly getNavigationPanelBodyStyle = (): { height?: string } => {
        const style: { height?: string } = {};

        if (this.mainListPageRef.current) {
            const pageHeight = this.mainListPageRef.current.clientHeight;
            const pagePadding =
                parseInt(getComputedStyle(this.mainListPageRef.current).paddingTop, 10) +
                parseInt(getComputedStyle(this.mainListPageRef.current).paddingBottom, 10);
            const titleHeight = this.mainListTitleRef.current?.offsetHeight || 0;
            const guideHeight = this.mainListGuideRef.current?.offsetHeight || 0;
            const result = pageHeight - pagePadding - titleHeight - guideHeight;

            if (!Number.isNaN(result) && result > 0) {
                style.height = `${result}px`;
            }
        }

        return style;
    };

    private readonly isFullWidth = (): boolean =>
        !this.props.isRecordSelected &&
        !this.props.isNewPage &&
        !this.props.isNavigationPanelHidden &&
        !this.props.browser?.is.xs;

    setChevronFocus = (): void => {
        if (this.togglePanelButtonRef.current) {
            this.togglePanelButtonRef.current.focus();
        }
    };

    private readonly setNavigationPanelIsOpened = (newState: boolean): void => {
        if (!newState && this.props.browser?.is.xs && !this.props.isRecordSelected) {
            getRouter(this.props.screenId).emptyPage();
        } else {
            this.props.setNavigationPanelIsOpened(newState);
        }
    };

    private readonly renderSplitHeaderActions = (): React.ReactNode => {
        const toggleLabel =
            this.props.screenId === DASHBOARD_SCREEN_ID
                ? localize('@sage/xtrem-ui/toggle-widget-list', 'Toggle widget list')
                : localize('@sage/xtrem-ui/toggle-navigation-panel', 'Toggle Navigation Panel');

        return (
            <div className="e-page-navigation-panel-header-actions">
                {this.props.createAction && (
                    <span className="e-create-actions" data-testid="e-create-actions">
                        <TableCreateActions
                            createActions={this.props.createAction}
                            multiActionType="split-button"
                            screenId={this.props.screenId}
                            elementId={navigationPanelId}
                        />
                    </span>
                )}
                {!this.props.browser?.greaterThan.l && !this.isFullWidth() && (
                    <BusinessAction
                        parentRef={this.togglePanelButtonRef}
                        onClick={(): void => this.setNavigationPanelIsOpened(false)}
                        id="$toggleNavigationPanel"
                        icon="chevron_left"
                        screenId={this.props.screenId}
                        isIconOnly={true}
                        title={toggleLabel}
                        buttonType="tertiary"
                        size="small"
                        pendoId="closeNavigationPanel"
                    />
                )}
            </div>
        );
    };

    private readonly renderTitle = (): React.ReactNode => {
        return (
            <div className="e-page-navigation-panel-header-title-container">
                {this.props.shouldDisplayBackArrow && this.props.browser?.is.xs && (
                    <BackArrowButton screenId={this.props.screenId} />
                )}
                <div className="e-page-navigation-panel-header-title">{this.props.title}</div>
                {this.renderSplitHeaderActions()}
            </div>
        );
    };

    private readonly renderHeaderTop = (): React.ReactNode => (
        <div data-testid="e-page-navigation-panel-full-width-header-top" className="e-page-navigation-panel-header-top">
            {!this.isFullWidth() && this.renderTitle()}
        </div>
    );

    private readonly isHeaderHidden = (): boolean => {
        return (
            this.props.navigationPanelDefinition.isHeaderHidden ||
            this.props.selectedRecordIds.length > 0 ||
            this.props.isSelectAllChecked
        );
    };

    renderFullWidth = (): React.ReactNode => {
        return (
            <main
                className="e-page-navigation-panel e-page-navigation-panel-full-width"
                data-testid="e-page-navigation-panel-full-width"
                ref={this.mainListPageRef}
            >
                {!this.props.fixedHeight && (
                    <>
                        <div className="e-page-navigation-panel-title" ref={this.mainListTitleRef}>
                            {this.props.shouldDisplayBackArrow && <BackArrowButton screenId={this.props.screenId} />}
                            <PageTitle title={this.props.title} />
                            <PageHeaderInsightsButton screenId={this.props.screenId} />
                        </div>
                        <div className="e-page-navigation-panel-guide" ref={this.mainListGuideRef} />
                    </>
                )}
                <div className="e-page-navigation-panel-body" style={this.state.mainListBodyStyle}>
                    <ConnectedNavigationPanelBodyItems
                        screenId={this.props.screenId}
                        isFullWidthGrid={this.isFullWidth()}
                        fixedHeight={this.props.fixedHeight ? this.props.fixedHeight - 48 : undefined}
                    />
                </div>
            </main>
        );
    };

    render(): React.ReactNode {
        const navPanelClassNames = this.props.isNavigationPanelHidden ? 'e-hidden' : 'e-page-navigation-panel';

        return this.isFullWidth() ? (
            this.renderFullWidth()
        ) : (
            <nav
                data-testid="e-page-navigation-panel-split-view"
                className={navPanelClassNames}
                style={this.props.fixedHeight ? { height: this.props.fixedHeight - 48 } : {}}
            >
                <div className="e-page-navigation-panel-header">{!this.isHeaderHidden() && this.renderHeaderTop()}</div>
                <div className="e-page-navigation-panel-body">
                    <ConnectedNavigationPanelBodyItems
                        screenId={this.props.screenId}
                        isFullWidthGrid={this.isFullWidth()}
                    />
                </div>
            </nav>
        );
    }
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: NavigationPanelExternalProps,
): NavigationPanelProps => {
    const pageProperties = getPagePropertiesFromState(props.screenId, state);
    const pageDefinition = getPageDefinitionFromState(props.screenId, state);

    const navigationPanelDefinition = pageProperties.navigationPanel;
    const navigationPanel = state.screenDefinitions[props.screenId].navigationPanel;
    if (!navigationPanel || !navigationPanelDefinition) {
        throw new Error(`No navigationPanel definition found for ${props.screenId}`);
    }

    const isRecordSelected = Boolean(props.selectedRecordId);
    const isNewPage = Boolean(
        (state.screenDefinitions[props.screenId] as PageDefinition).queryParameters?._id === NEW_PAGE,
    );

    const history = state.navigation.history;

    return {
        ...props,
        browser: state.browser,
        createAction: pageProperties.createAction,
        isNavigationPanelHidden: navigationPanel.isHidden,
        isNavigationPanelOpened: navigationPanel.isOpened,
        isNewPage,
        isRecordSelected,
        isSelectAllChecked:
            (pageDefinition.metadata.uiComponentProperties.$navigationPanel as InternalTableProperties)
                ?.isSelectAllChecked ?? false,
        navigationPanelDefinition,
        selectedRecordIds:
            (pageDefinition.metadata.uiComponentProperties.$navigationPanel as InternalTableProperties)
                ?.selectedRecords ?? [],
        setNavigationPanelIsHeaderHidden: xtremRedux.actions.actionStub,
        setNavigationPanelIsOpened: xtremRedux.actions.actionStub,
        title: pageProperties.objectTypePlural || getFieldTitle(props.screenId, pageProperties, null) || '',
        shouldDisplayBackArrow: history.length > 0,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: NavigationPanelExternalProps,
): Partial<NavigationPanelProps> => {
    return {
        setNavigationPanelIsHeaderHidden: (isHeaderHidden: boolean): void =>
            dispatch(xtremRedux.actions.setNavigationPanelIsHeaderHidden(isHeaderHidden, props.screenId)),
        setNavigationPanelIsOpened: (isOpened: boolean): void =>
            dispatch(xtremRedux.actions.setNavigationPanelIsOpened(isOpened, props.screenId)),
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(NavigationPanel);
