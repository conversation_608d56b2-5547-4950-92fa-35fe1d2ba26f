import type { Extend } from '../../../service/page-extension';
import { getPageMetadata } from '../../../service/page-metadata';
import type { ScreenExtension } from '../../../types';
import { getTargetPrototype } from '../../../utils/decorator-utils';
import { AbstractDecorator } from '../../abstract-decorator';
import { FragmentFieldsControlObject } from './fragment-fields-control-object';
import { ContainerKey } from '../../types';
import type { FragmentFieldsDecoratorProperties } from './fragment-fields-types';

class FragmentFieldsDecorator extends AbstractDecorator<ContainerKey.FragmentFields> {
    protected _controlObjectConstructor = FragmentFieldsControlObject;
}

export function fragmentFields<CT extends ScreenExtension<CT>>(
    properties: FragmentFieldsDecoratorProperties<Extend<CT>>,
): (target: CT, name: string) => void {
    return function fragmentFieldsDecorator(target: CT, name: string) {
        const pageMetadata = getPageMetadata(getTargetPrototype(target.constructor), target);

        pageMetadata.definitionOrder.push(name);
        pageMetadata.fragmentFieldsThunks[name] = (): FragmentFieldsControlObject => {
            return new FragmentFieldsDecorator(
                pageMetadata.target as any,
                name,
                { pageMetadata, properties },
                ContainerKey.FragmentFields,
                {},
                {},
                {},
            ).build().controlObject;
        };
    };
}
