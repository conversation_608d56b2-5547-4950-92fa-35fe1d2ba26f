/**
 * @packageDocumentation
 * @module root
 * */

import { getStore } from '../../../redux';
import { dispatchFieldsValidation } from '../../../service/dispatch-service';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { getUiComponentProperties, setUiComponentProperties } from '../../../service/transactions-service';
import type { ScreenExtension } from '../../../types';
import type { ContainerProperties } from '../../abstract-container';
import { AbstractContainer } from '../../abstract-container';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import type { ContainerComponentProps, ContainerControlObjectConstructorProps, ParentType } from '../../types';
import { ContainerKey } from '../../types';

export interface IFragmentFieldsControlObject
    extends ContainerControlObjectConstructorProps<ContainerKey.FragmentFields> {
    parent?: ParentType<ContainerKey.FragmentFields>;
}

/**
 * [Container]{@link AbstractContainer} that holds any number of [fields]{@link AbstractField}
 */
export class FragmentFieldsControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends AbstractContainer<
    CT,
    ContainerKey.FragmentFields,
    ContainerComponentProps<ContainerKey.FragmentFields>
> {
    static readonly defaultUiProperties: Partial<ContainerProperties> = {
        ...AbstractContainer.defaultUiProperties,
    };

    constructor(properties: IFragmentFieldsControlObject) {
        super(
            properties.screenId,
            properties.elementId,
            properties.getUiComponentProperties || getUiComponentProperties,
            properties.setUiComponentProperties || setUiComponentProperties,
            ContainerKey.FragmentFields,
            properties.getValidationState || (async (): Promise<boolean> => true),
            properties.layout,
            properties.parent,
        );
    }

    private getFragmentFieldsIds(): string[] {
        const fragment = this.uiComponentProperties.fragment.split('/')[2];
        return getStore().getState().screenDefinitions[this.screenId].metadata.fragmentFields[fragment];
    }

    private setFieldsFragmentProperty(propertyName: 'isHidden' | 'isDisabled', value: boolean): void {
        const listOfFields = this.getFragmentFieldsIds();
        listOfFields.forEach(fieldId => {
            const properties = this._getUiComponentProperties(this.screenId, fieldId);
            this._setUiComponentProperties(this.screenId, fieldId, { ...properties, [propertyName]: value });
        });
    }

    @FieldControlObjectResolvedProperty<ContainerProperties<CT>, FragmentFieldsControlObject>({
        onSet(_, __, value) {
            this.setFieldsFragmentProperty('isHidden', value);
        },
    })
    isHidden: boolean;

    @FieldControlObjectResolvedProperty<ContainerProperties<CT>, FragmentFieldsControlObject>({
        onSet(_, __, value) {
            this.setFieldsFragmentProperty('isDisabled', value);
        },
    })
    isDisabled: boolean;

    /**
     * Triggers the field validation rules. Since the validation rules might be asynchronous,
     * this method returns a promise that must be awaited to get the validation result. Compared to the `validate` method
     * it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
     */
    async validateWithDetails(
        partition: true,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
    async validateWithDetails(partition: false): Promise<ValidationResult[]>;
    async validateWithDetails(): Promise<ValidationResult[]>;
    async validateWithDetails(
        partition = false,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] } | ValidationResult[]> {
        const listOfFieldsIds = this.getFragmentFieldsIds();
        const errors = await dispatchFieldsValidation(listOfFieldsIds, this.screenId);
        return partition ? errors : errors.allErrors;
    }
}
