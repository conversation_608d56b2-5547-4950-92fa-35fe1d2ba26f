import type { ScreenBase } from '../../../service/screen-base';
import type { ContainerProperties, BlockControlObject } from '../../control-objects';
import type { HasParent } from '../../field/traits';

export interface FragmentFieldsDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<ContainerProperties<CT>, '_controlObjectType'>,
        HasParent<CT, BlockControlObject<CT>> {
    fragment: string;
}
