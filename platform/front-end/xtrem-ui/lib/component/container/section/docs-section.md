## Documentation - Section
| name            | optional | description                                                                                                                                                                                                                                    | type                                                                                                   | default                                                                                  |
| --------------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------- |
| access          | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">AccessConfiguration</pre>                                                       | <pre lang="javascript">{&nbsp;node:&nbsp;'<node>',&nbsp;bind:&nbsp;'<bind>'&nbsp;}</pre> |
| insertBefore    | true     | The field before this extension field is inserted                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;T</pre>                                            | <pre lang="javascript">undefined</pre>                                                   |
| isDisabled      | true     | Whether the HTML element is disabled or not. Defaults to false The difference with readOnly is that disabled suggests that the field is not editable for some validation reason (e.g. a button which can't be clicked due to validation errors | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre> | <pre lang="javascript">false</pre>                                                       |
| isExtendable    | true     | Whether the helper panel can be extracted into a separate window or not. Defaults to false                                                                                                                                                     | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                       |
| isHidden        | true     | Whether the HTML element is hidden or not. Defaults to false                                                                                                                                                                                   | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre> | <pre lang="javascript">false</pre>                                                       |
| isHiddenDesktop | true     | Whether the element is hidden or not in desktop devices. Defaults to false                                                                                                                                                                     | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                       |
| isHiddenMobile  | true     | Whether the element is hidden or not in mobile devices. Defaults to false                                                                                                                                                                      | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                       |
| isOpen          | true     | Whether the section is open (uncollapsed) or not. Defaults to true                                                                                                                                                                             | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">true</pre>                                                        |
| isTitleHidden   | true     | Whether the element title is hidden or not. Defaults to false                                                                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">undefined</pre>                                                   |
| isTransient     | true     | Whether the value is bound to a GraphQL node (transient = false) or not (transient = true). Defaults to false                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                       |
| mode            | true     | The display mode of the section. In accordion mode all blocks are displayed full width, regardless to their width configuration                                                                                                                | <pre lang="javascript">"normal"&nbsp;|&nbsp;"accordion"</pre>                                          | <pre lang="javascript">'normal'</pre>                                                    |
| onActive        | true     | Function that gets executed when the section becomes active                                                                                                                                                                                    | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                         | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                              |
| onInactive      | true     | Function that gets executed when the section stops being active                                                                                                                                                                                | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                         | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                              |
| title           | true     | The title of the HTML element                                                                                                                                                                                                                  | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;any,&nbsp;Dict<any>></pre>  | <pre lang="javascript">''</pre>                                                          |
| validation      | true     | Container validation rule                                                                                                                                                                                                                      | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                         | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                              |