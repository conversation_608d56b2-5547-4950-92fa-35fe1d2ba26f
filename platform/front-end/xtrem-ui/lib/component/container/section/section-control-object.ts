/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { getUiComponentProperties, setUiComponentProperties } from '../../../service/transactions-service';
import type { ScreenExtension } from '../../../types';
import { AbstractContainer } from '../../abstract-container';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { ContainerComponentProps, ContainerControlObjectConstructorProps } from '../../types';
import { ContainerKey } from '../../types';
import type { SectionProperties } from './section-types';

export interface ISectionControlObject extends ContainerControlObjectConstructorProps<ContainerKey.Section> {
    dispatchSectionValidation: () => Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
}

/**
 * [Container]{@link AbstractContainer} that holds any number of [blocks]{@link BlockControlObject}
 */
export class SectionControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends AbstractContainer<
    CT,
    ContainerKey.Section,
    ContainerComponentProps<ContainerKey.Section>
> {
    public insertBefore?: ContainerControlObjectConstructorProps<ContainerKey.Section>['insertBefore'];

    private readonly _dispatchSectionValidation: ISectionControlObject['dispatchSectionValidation'];

    static readonly defaultUiProperties: Partial<SectionProperties> = {
        ...AbstractContainer.defaultUiProperties,
        isExtendable: false,
        isOpen: true,
        isTitleHidden: undefined,
        mode: 'normal',
    };

    constructor(properties: ISectionControlObject) {
        super(
            properties.screenId,
            properties.elementId,
            properties.getUiComponentProperties || getUiComponentProperties,
            properties.setUiComponentProperties || setUiComponentProperties,
            ContainerKey.Section,
            properties.getValidationState || (async (): Promise<boolean> => true),
            properties.layout,
        );
        this._dispatchSectionValidation = properties.dispatchSectionValidation;
        this.insertBefore = properties.insertBefore;
    }

    @ControlObjectProperty<SectionProperties<CT>, SectionControlObject<CT>>()
    /** Whether the section is collapsed (isOpen = false) or not (isOpen = true) */
    isOpen?: boolean;

    @ControlObjectProperty<SectionProperties<CT>, SectionControlObject<CT>>()
    /**
     * Whether the section can be extended into a separate window (extendable = true)
     * or not (extendable = false)
     */
    isExtendable?: boolean;

    /** Short text that is displayed on the section anchor element next to its title */
    @ControlObjectProperty<SectionProperties<CT>, SectionControlObject<CT>>()
    indicatorContent?: string;

    /**
     * Triggers the validation rules of all the fields of the section. Since the validation rules
     * might be asynchronous, this method returns a promise that must be awaited to get
     * the validation result
     */
    async validate(): Promise<string[]> {
        const result = await this.validateWithDetails();
        return result.map(r => r!.message);
    }

    /**
     * Triggers the field validation rules. Since the validation rules might be asynchronous,
     * this method returns a promise that must be awaited to get the validation result. Compared to the `validate` method
     * it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
     */
    async validateWithDetails(
        partition: true,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
    async validateWithDetails(partition: false): Promise<ValidationResult[]>;
    async validateWithDetails(): Promise<ValidationResult[]>;
    async validateWithDetails(
        partition = false,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] } | ValidationResult[]> {
        const errors = await this._dispatchSectionValidation();
        return partition ? errors : errors.allErrors;
    }
}
