const tableComponentMock = jest.fn(() => <div data-testid="tableComponentMock" />);
jest.mock('../../../field/text/async-text-component');
jest.mock('../../../../utils/hooks/effects/use-parent-element-size', () => ({
    useParentElementSize: () => [React.createRef(), { height: 400, width: 600 }],
}));
jest.mock('../../../field/table/async-table-component', () => ({
    AsyncConnectedTableComponent: tableComponentMock,
}));

import {
    addBlockToState,
    addFieldToState,
    addSectionToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { FieldKey } from '../../../types';
import type { XtremAppState } from '../../../../redux';
import type { PageArticleItem } from '../../../../service/layout-types';
import { ContextType, GraphQLKind } from '../../../../types';
import type { SectionProperties } from '../../../control-objects';
import type { SectionComponentProps } from '../section-component';
import { ConnectedSectionComponent, SectionComponent } from '../section-component';
import { render } from '@testing-library/react';
import * as nestedFields from '../../../nested-fields';
import { GraphQLTypes } from '@sage/xtrem-shared';
import '@testing-library/jest-dom';

describe('SectionComponent', () => {
    const screenId = 'TestPage';
    const elementId = 'test-section';
    const nodeTypes = {
        MyTestNode: {
            name: 'MyTestNode',
            title: 'MyTestNode',

            packageName: '@sage/xtrem-test',
            properties: {
                field1: {
                    name: 'field1',
                    type: GraphQLTypes.String,
                    kind: GraphQLKind.Scalar,
                },
                field2: {
                    name: 'field1',
                    type: GraphQLTypes.String,
                    kind: GraphQLKind.Scalar,
                },
                'test-block': {
                    name: 'test-block',
                    type: GraphQLTypes.String,
                    kind: GraphQLKind.Scalar,
                },
                [elementId]: {
                    name: elementId,
                    type: GraphQLTypes.String,
                    kind: GraphQLKind.Scalar,
                },
            },
            mutations: {},
        },
        MyOtherNode: {
            title: 'MyOtherNode',
            name: 'MyOtherNode',
            packageName: '@sage/xtrem-test',
            properties: {
                someRandomBind: {
                    name: 'someRandomBind',
                    type: GraphQLTypes.String,
                    kind: GraphQLKind.Scalar,
                },
            },
            mutations: {},
        },
    };

    let store: MockStoreEnhanced<XtremAppState>;
    let sectionItem: Partial<PageArticleItem>;
    let sectionProperties: SectionProperties;
    let pageDefinition: any;
    let state: XtremAppState;

    beforeEach(() => {
        sectionProperties = {
            title: 'Test Section Title',
            isOpen: true,
        };

        const state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        state.nodeTypes = nodeTypes;
        addFieldToState(FieldKey.Text, state, screenId, 'field1', {});
        addFieldToState(FieldKey.Text, state, screenId, 'field2', {});
        const blockItem = addBlockToState(state, screenId, 'test-block', {}, [{ $bind: 'field1' }]);
        sectionItem = addSectionToState(state, screenId, elementId, sectionProperties, [blockItem]);
        state.screenDefinitions[screenId].selectedRecordId = '1';
        store = getMockStore(state);
    });

    describe('Snapshots', () => {
        it('should render with default properties', () => {
            const wrapper = render(
                <Provider store={store}>
                    <ConnectedSectionComponent
                        screenId={screenId}
                        item={sectionItem}
                        contextType={ContextType.page}
                        availableColumns={12}
                    />
                </Provider>,
            );

            expect(wrapper.queryByTestId('e-field-bind-test-section', { exact: false })).toMatchSnapshot();
        });

        it('should render in accordion mode', () => {
            state = store.getState();
            (state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] as SectionProperties).mode =
                'accordion';
            const wrapper = render(
                <Provider store={getMockStore(state)}>
                    <ConnectedSectionComponent
                        screenId={screenId}
                        item={sectionItem}
                        contextType={ContextType.page}
                        availableColumns={12}
                    />
                </Provider>,
            );

            expect(wrapper.queryByTestId('e-field-bind-test-section', { exact: false })).toMatchSnapshot();
        });

        it('should render in a non-page context', () => {
            const wrapper = render(
                <Provider store={store}>
                    <ConnectedSectionComponent screenId={screenId} item={sectionItem} availableColumns={12} />
                </Provider>,
            );

            expect(wrapper.queryByTestId('e-field-bind-test-section', { exact: false })).toMatchSnapshot();
        });

        it('should render without a title', () => {
            state = store.getState();

            (state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] as SectionProperties).title =
                undefined;
            const wrapper = render(
                <Provider store={getMockStore(state)}>
                    <ConnectedSectionComponent
                        screenId={screenId}
                        item={sectionItem}
                        contextType={ContextType.page}
                        availableColumns={12}
                    />
                </Provider>,
            );

            expect(wrapper.queryByTestId('e-field-bind-test-section', { exact: false })).toMatchSnapshot();
        });

        it('should render with closed', () => {
            state = store.getState();

            (state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] as SectionProperties).isOpen =
                false;
            const wrapper = render(
                <Provider store={getMockStore(state)}>
                    <ConnectedSectionComponent
                        screenId={screenId}
                        item={sectionItem}
                        contextType={ContextType.page}
                        availableColumns={12}
                    />
                </Provider>,
            );

            expect(wrapper.queryByTestId('e-field-bind-test-section', { exact: false })).toMatchSnapshot();
        });

        it('should render without title', () => {
            const state = store.getState();
            state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId].isTitleHidden = true;
            const wrapper = render(
                <Provider store={getMockStore(state)}>
                    <ConnectedSectionComponent
                        screenId={screenId}
                        item={sectionItem}
                        contextType={ContextType.page}
                        availableColumns={12}
                    />
                </Provider>,
            );
            expect(wrapper.queryByTestId('e-field-bind-test-section', { exact: false })).toMatchSnapshot();
        });

        it('should render with a fixed height if it contains only a single field and has no footer', async () => {
            state = getMockState();
            pageDefinition = getMockPageDefinition(screenId);
            pageDefinition.metadata.uiComponentProperties[screenId] = { node: '@sage/xtrem-test/MyTestNode' } as any;
            state.screenDefinitions[screenId] = pageDefinition;
            addFieldToState(FieldKey.Table, state, screenId, 'field1', {
                node: '@sage/xtrem-test/AnyNode',
                title: 'Test table',
                columns: [
                    nestedFields.text<any, any>({
                        bind: 'whatever',
                    }),
                ],
            });
            sectionItem = addSectionToState(state, screenId, elementId, sectionProperties, [{ $bind: 'field1' }]);
            store = getMockStore(state);

            render(
                <Provider store={store}>
                    <ConnectedSectionComponent
                        availableColumns={8}
                        contextType={ContextType.page}
                        item={sectionItem}
                        screenId={screenId}
                    />
                </Provider>,
            );
            expect(tableComponentMock).toHaveBeenLastCalledWith(
                expect.objectContaining({ fixedHeight: 380 }),
                expect.anything(),
            );
        });

        it('should not render with a fixed height container if the section is not rendered into the page body', async () => {
            state = getMockState();
            pageDefinition = getMockPageDefinition(screenId);
            pageDefinition.metadata.uiComponentProperties[screenId] = { node: '@sage/xtrem-test/MyTestNode' } as any;
            state.screenDefinitions[screenId] = pageDefinition;
            addFieldToState(FieldKey.Table, state, screenId, 'field1', {
                node: '@sage/xtrem-test/AnyNode',
                title: 'Test table',
                columns: [
                    nestedFields.text<any, any>({
                        bind: 'whatever',
                    }),
                ],
            });
            sectionItem = addSectionToState(state, screenId, elementId, sectionProperties, [{ $bind: 'field1' }]);
            store = getMockStore(state);

            render(
                <Provider store={store}>
                    <ConnectedSectionComponent availableColumns={8} item={sectionItem} screenId={screenId} />
                </Provider>,
            );
            expect(tableComponentMock).toHaveBeenLastCalledWith(
                expect.objectContaining({ fixedHeight: undefined }),
                expect.anything(),
            );
        });

        it('should render with a fixed height if it contains only a single field and should deduct the footer height', async () => {
            state = getMockState();
            pageDefinition = getMockPageDefinition(screenId);
            pageDefinition.metadata.uiComponentProperties[screenId] = { node: '@sage/xtrem-test/MyTestNode' } as any;
            state.screenDefinitions[screenId] = pageDefinition;
            addFieldToState(FieldKey.Table, state, screenId, 'field1', {
                node: '@sage/xtrem-test/AnyNode',
                title: 'Test table',
                columns: [
                    nestedFields.text<any, any>({
                        bind: 'whatever',
                    }),
                ],
            });
            sectionItem = addSectionToState(state, screenId, elementId, sectionProperties, [{ $bind: 'field1' }]);
            store = getMockStore(state);

            render(
                <Provider store={store}>
                    <ConnectedSectionComponent
                        availableColumns={8}
                        contextType={ContextType.page}
                        hasFooter={true}
                        item={sectionItem}
                        screenId={screenId}
                    />
                </Provider>,
            );
            expect(tableComponentMock).toHaveBeenLastCalledWith(
                expect.objectContaining({ fixedHeight: 310 }),
                expect.anything(),
            );
        });
    });

    describe('access rights', () => {
        beforeEach(() => {
            state = getMockState();
            state.nodeTypes = nodeTypes;
            pageDefinition = getMockPageDefinition(screenId);
            pageDefinition.metadata.uiComponentProperties[screenId] = { node: '@sage/xtrem-test/MyTestNode' } as any;
            state.screenDefinitions[screenId] = pageDefinition;
            addFieldToState(FieldKey.Text, state, screenId, 'field1', {});
            addFieldToState(FieldKey.Text, state, screenId, 'field2', {});
            const blockItem = addBlockToState(state, screenId, 'test-block', {}, [{ $bind: 'field1' }]);
            sectionItem = addSectionToState(state, screenId, elementId, sectionProperties, [blockItem]);
            store = getMockStore(state);
        });

        it('should not children fields if the block is does not have any associated access rules', () => {
            store = getMockStore(state);

            const wrapper = render(
                <Provider store={store}>
                    <ConnectedSectionComponent screenId={screenId} item={sectionItem} availableColumns={8} />
                </Provider>,
            );
            expect(wrapper.queryByTestId('e-text-field-input')).not.toHaveAttribute('disabled');
        });

        it('should not disable children fields if the block is authorized', () => {
            pageDefinition.accessBindings = {
                MyTestNode: {
                    [elementId]: 'authorized',
                },
            };
            store = getMockStore(state);

            const wrapper = render(
                <Provider store={store}>
                    <ConnectedSectionComponent screenId={screenId} item={sectionItem} availableColumns={8} />
                </Provider>,
            );
            expect(wrapper.queryByTestId('e-text-field-input')).not.toHaveAttribute('disabled');
        });

        it('should disable children fields if the block is unauthorized', () => {
            pageDefinition.accessBindings = {
                MyTestNode: {
                    [elementId]: 'unauthorized',
                },
            };
            store = getMockStore(state);

            const wrapper = render(
                <Provider store={store}>
                    <ConnectedSectionComponent screenId={screenId} item={sectionItem} availableColumns={8} />
                </Provider>,
            );
            expect(wrapper.queryByTestId('e-text-field-input')).toHaveAttribute('disabled');
        });

        it('should disable children fields if the block is an unauthorized access binding', () => {
            pageDefinition.accessBindings = {
                MyTestNode: {
                    [elementId]: 'authorized',
                },
                MyOtherNode: {
                    someRandomBind: 'unauthorized',
                },
            };
            store = getMockStore(state);
            pageDefinition.metadata.uiComponentProperties[elementId].access = {
                node: '@sage/xtrem-test/MyOtherNode',
                bind: 'unauthorized',
            };

            const wrapper = render(
                <Provider store={store}>
                    <ConnectedSectionComponent screenId={screenId} item={sectionItem} availableColumns={8} />
                </Provider>,
            );
            expect(wrapper.baseElement.querySelector('.e-section')!).not.toHaveClass('e-hidden');
        });

        it('should not be hidden if it is authorized', () => {
            pageDefinition.accessBindings = {
                MyTestNode: {
                    [elementId]: 'authorized',
                },
            };

            store = getMockStore(state);

            const wrapper = render(
                <Provider store={store}>
                    <ConnectedSectionComponent screenId={screenId} item={sectionItem} availableColumns={8} />
                </Provider>,
            );
            expect(wrapper.baseElement.querySelector('.e-section')!).not.toHaveClass('e-hidden');
        });

        it('should be hidden if it is unavailable', () => {
            pageDefinition.accessBindings = {
                MyTestNode: {
                    [elementId]: 'unavailable',
                },
            };

            store = getMockStore(state);

            const wrapper = render(
                <Provider store={store}>
                    <ConnectedSectionComponent screenId={screenId} item={sectionItem} availableColumns={8} />
                </Provider>,
            );
            expect(wrapper.baseElement.querySelector('.e-section')!).toHaveClass('e-hidden');
        });

        it('should be hidden if the block is an unavailable access binding', () => {
            pageDefinition.accessBindings = {
                MyTestNode: {
                    [elementId]: 'authorized',
                },
                MyOtherNode: {
                    someRandomBind: 'unavailable',
                },
            };
            store = getMockStore(state);
            pageDefinition.metadata.uiComponentProperties[elementId].access = {
                node: '@sage/xtrem-test/MyOtherNode',
                bind: 'someRandomBind',
            };

            const wrapper = render(
                <Provider store={store}>
                    <ConnectedSectionComponent screenId={screenId} item={sectionItem} availableColumns={8} />
                </Provider>,
            );
            expect(wrapper.baseElement.querySelector('.e-section')!).toHaveClass('e-hidden');
        });
    });

    describe('SectionComponent (Page Mode)', () => {
        let sectionComponentProps: SectionComponentProps;

        beforeEach(() => {
            sectionComponentProps = {
                availableColumns: 12,
                browser: {
                    greaterThan: { xs: true, s: true, m: true, l: true },
                    is: { xs: false, s: false, m: false, l: true },
                    lessThan: { xs: true, s: true, m: true, l: false },
                    mediaType: '?',
                    orientation: '?',
                },
                contextType: ContextType.page,
                fieldProperties: {
                    isExtendable: true,
                    isOpen: true,
                    title: 'Hello World!',
                },
                item: {
                    $layout: {
                        $items: [],
                    },
                },
                screenId: 'TabsTestPage',
                setFieldProperties: jest.fn(),
            };
        });

        describe('given "default" page mode', () => {
            it('should show section titles given undefined isTitleHidden', () => {
                sectionComponentProps.pageMode = 'default';
                sectionComponentProps.fieldProperties.isTitleHidden = undefined;
                const wrapper = render(<SectionComponent {...sectionComponentProps} />);
                expect(wrapper.baseElement.querySelectorAll('.e-section-header').length).toBe(1);
            });

            it('should hide section titles given true isTitleHidden', () => {
                sectionComponentProps.pageMode = 'default';
                sectionComponentProps.fieldProperties.isTitleHidden = true;
                const wrapper = render(<SectionComponent {...sectionComponentProps} />);
                expect(wrapper.baseElement.querySelectorAll('.e-section-header').length).toBe(0);
            });

            it('should show section titles given false isTitleHidden', () => {
                sectionComponentProps.pageMode = 'default';
                sectionComponentProps.fieldProperties.isTitleHidden = false;
                const wrapper = render(<SectionComponent {...sectionComponentProps} />);
                expect(wrapper.baseElement.querySelectorAll('.e-section-header').length).toBe(1);
            });
        });

        describe('given "tabs" page mode', () => {
            it('should hide section titles given undefined isTitleHidden', () => {
                sectionComponentProps.pageMode = 'tabs';
                sectionComponentProps.fieldProperties.isTitleHidden = undefined;
                const wrapper = render(<SectionComponent {...sectionComponentProps} />);
                expect(wrapper.baseElement.querySelectorAll('.e-section-header').length).toBe(0);
            });

            it('should hide section title given true isTitleHidden', () => {
                sectionComponentProps.pageMode = 'tabs';
                sectionComponentProps.fieldProperties.isTitleHidden = true;
                const wrapper = render(<SectionComponent {...sectionComponentProps} />);
                expect(wrapper.baseElement.querySelectorAll('.e-section-header').length).toBe(0);
            });

            it('should show section title given false isTitleHidden', () => {
                sectionComponentProps.pageMode = 'tabs';
                sectionComponentProps.fieldProperties.isTitleHidden = false;
                const wrapper = render(<SectionComponent {...sectionComponentProps} />);
                expect(wrapper.baseElement.querySelectorAll('.e-section-header').length).toBe(1);
            });
        });
    });
});
