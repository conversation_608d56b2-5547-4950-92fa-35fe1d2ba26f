// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SectionComponent Snapshots should render in a non-page context 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c6 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c6:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c6::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::placeholder {
  color: var(--colorsUtilityYin055);
}

.c3 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c4 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c4 .c5 {
  padding: 0 var(--spacing150);
}

.c4 input::-ms-clear {
  display: none;
}

.c4 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<section
  class="e-section"
  data-testid="e-section-field e-field-label-testSectionTitle e-field-bind-test-section"
  id="test-section"
>
  <div
    class="e-section-header"
  >
    <h2
      class="e-section-title"
    >
      Test Section Title
    </h2>
  </div>
  <div
    class="e-grid-row e-grid-row-12 e-section-body"
    style="grid-template-columns: repeat(12, 1fr); padding: 0px 0px 0px 0px;"
  >
    <div
      class="e-grid-column e-grid-column-8 e-container-parent e-block-parent"
      style="grid-column: span 8;"
    >
      <div
        class="e-block"
        data-testid="e-block-field e-field-bind-test-block"
      >
        <div
          class="e-block-body"
        >
          <div
            class="e-grid-row e-grid-row-8 "
            style="grid-template-columns: repeat(8, 1fr); padding: 0px 16px 0px 16px;"
          >
            <div
              class="e-grid-column e-grid-column-2 e-field-grid-column"
              style="grid-column: span 2;"
            >
              <div
                class="e-field e-text-field"
                data-testid="e-text-field e-field-bind-field1"
              >
                <div
                  class="c0 c1"
                >
                  <div
                    class="c2"
                    data-role="field-line"
                  >
                    <div
                      class="c3"
                      data-role="input-presentation-container"
                    >
                      <div
                        class="c4"
                        role="presentation"
                      >
                        <input
                          aria-invalid="false"
                          autocomplete="off"
                          class="c5 c6"
                          data-element="input"
                          data-testid="e-text-field-input"
                          id="TestPage-field1"
                          name="field1"
                          type="text"
                          value="Test value"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
`;

exports[`SectionComponent Snapshots should render in accordion mode 1`] = `
.c6 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c6::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e99c";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
  color: var(--colorsUtilityYin090);
  background-color: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor100);
  border-left: none;
  border-right: none;
}

.c1 + .c1 {
  margin-top: -1px;
  border-top: 1px solid var(--colorsUtilityMajor100);
  border-bottom: 1px solid var(--colorsUtilityMajor100);
}

.c7 {
  -webkit-transition: -webkit-transform 0.3s;
  -webkit-transition: transform 0.3s;
  transition: transform 0.3s;
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
  margin-right: var(--spacing200);
  color: var(--colorsActionMinor500);
}

.c5 {
  padding-right: var(--sizing300);
  display: grid;
  grid-template-rows: auto auto;
}

.c4 {
  padding: var(--spacing200);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -webkit-flex-direction: row-reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
  cursor: pointer;
  z-index: 1;
}

.c4:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c4:hover {
  background-color: var(--colorsUtilityMajor050);
}

.c9 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  box-sizing: border-box;
  overflow: hidden;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  max-height: 0px;
  height: 0px;
  visibility: hidden;
}

.c11 {
  padding: var(--spacing300);
  padding-top: var(--spacing100);
  overflow: hidden;
  padding: 0;
}

.c3 .c1 {
  overflow: unset;
}

.c3 .c10 {
  overflow: unset;
}

.c3 .c8 {
  overflow: unset;
}

.c0 {
  overflow: hidden;
}

<section
  class="e-section e-section-context-page"
  data-testid="e-section-field e-field-label-testSectionTitle e-field-bind-test-section"
  id="test-section"
>
  <div
    class="e-section-header"
  >
    <h2
      class="e-section-title"
    >
      Test Section Title
    </h2>
  </div>
  <div
    class="c0"
    data-component="accordion-group"
  >
    <div
      class="c1 c2 c3"
      data-component="accordion"
      id="Accordion_testcarb-onco-mpon-ents-uniqguidmock"
      width="100%"
    >
      <div
        aria-controls="AccordionContent_testcarb-onco-mpon-ents-uniqguidmock"
        aria-expanded="false"
        class="c4"
        data-element="accordion-title-container"
        id="AccordionHeader_testcarb-onco-mpon-ents-uniqguidmock"
        role="button"
        tabindex="0"
      >
        <div
          class="c5"
          data-element="accordion-headings-container"
        />
        <span
          class="c6 c7"
          data-component="icon"
          data-element="accordion-icon"
          data-role="icon"
          font-size="small"
          type="chevron_down_thick"
        />
      </div>
      <div
        class="c8 c9"
        data-role="accordion-content-container"
      >
        <div
          aria-labelledby="AccordionHeader_testcarb-onco-mpon-ents-uniqguidmock"
          class="c10 c11"
          data-element="accordion-content"
          data-role="accordion-content"
          id="AccordionContent_testcarb-onco-mpon-ents-uniqguidmock"
          role="region"
        />
      </div>
    </div>
  </div>
</section>
`;

exports[`SectionComponent Snapshots should render with closed 1`] = `
<section
  class="e-section e-section-context-page"
  data-testid="e-section-field e-field-label-testSectionTitle e-field-bind-test-section"
  id="test-section"
>
  <div
    class="e-section-header"
  >
    <h2
      class="e-section-title"
    >
      Test Section Title
    </h2>
  </div>
</section>
`;

exports[`SectionComponent Snapshots should render with default properties 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c6 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c6:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c6::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::placeholder {
  color: var(--colorsUtilityYin055);
}

.c3 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c4 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c4 .c5 {
  padding: 0 var(--spacing150);
}

.c4 input::-ms-clear {
  display: none;
}

.c4 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<section
  class="e-section e-section-context-page"
  data-testid="e-section-field e-field-label-testSectionTitle e-field-bind-test-section"
  id="test-section"
>
  <div
    class="e-section-header"
  >
    <h2
      class="e-section-title"
    >
      Test Section Title
    </h2>
  </div>
  <div
    class="e-grid-row e-grid-row-12 e-section-body"
    style="grid-template-columns: repeat(12, 1fr); padding: 0px 0px 0px 0px;"
  >
    <div
      class="e-grid-column e-grid-column-8 e-container-parent e-block-parent e-block-context-page"
      style="grid-column: span 8;"
    >
      <div
        class="e-block"
        data-testid="e-block-field e-field-bind-test-block"
      >
        <div
          class="e-block-body"
        >
          <div
            class="e-grid-row e-grid-row-8 "
            style="grid-template-columns: repeat(8, 1fr); padding: 16px 16px 16px 16px;"
          >
            <div
              class="e-grid-column e-grid-column-2 e-field-grid-column"
              style="grid-column: span 2;"
            >
              <div
                class="e-field e-text-field e-context-page"
                data-testid="e-text-field e-field-bind-field1"
              >
                <div
                  class="c0 c1"
                >
                  <div
                    class="c2"
                    data-role="field-line"
                  >
                    <div
                      class="c3"
                      data-role="input-presentation-container"
                    >
                      <div
                        class="c4"
                        role="presentation"
                      >
                        <input
                          aria-invalid="false"
                          autocomplete="off"
                          class="c5 c6"
                          data-element="input"
                          data-testid="e-text-field-input"
                          id="TestPage-field1-page"
                          name="field1"
                          type="text"
                          value="Test value"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
`;

exports[`SectionComponent Snapshots should render without a title 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c6 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c6:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c6::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::placeholder {
  color: var(--colorsUtilityYin055);
}

.c3 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c4 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c4 .c5 {
  padding: 0 var(--spacing150);
}

.c4 input::-ms-clear {
  display: none;
}

.c4 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<section
  class="e-section e-section-context-page"
  data-testid="e-section-field e-field-bind-test-section"
  id="test-section"
>
  <div
    class="e-section-header"
  >
    <span
      class="e-section-title"
    />
  </div>
  <div
    class="e-grid-row e-grid-row-12 e-section-body"
    style="grid-template-columns: repeat(12, 1fr); padding: 0px 0px 0px 0px;"
  >
    <div
      class="e-grid-column e-grid-column-8 e-container-parent e-block-parent e-block-context-page"
      style="grid-column: span 8;"
    >
      <div
        class="e-block"
        data-testid="e-block-field e-field-bind-test-block"
      >
        <div
          class="e-block-body"
        >
          <div
            class="e-grid-row e-grid-row-8 "
            style="grid-template-columns: repeat(8, 1fr); padding: 16px 16px 16px 16px;"
          >
            <div
              class="e-grid-column e-grid-column-2 e-field-grid-column"
              style="grid-column: span 2;"
            >
              <div
                class="e-field e-text-field e-context-page"
                data-testid="e-text-field e-field-bind-field1"
              >
                <div
                  class="c0 c1"
                >
                  <div
                    class="c2"
                    data-role="field-line"
                  >
                    <div
                      class="c3"
                      data-role="input-presentation-container"
                    >
                      <div
                        class="c4"
                        role="presentation"
                      >
                        <input
                          aria-invalid="false"
                          autocomplete="off"
                          class="c5 c6"
                          data-element="input"
                          data-testid="e-text-field-input"
                          id="TestPage-field1-page"
                          name="field1"
                          type="text"
                          value="Test value"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
`;

exports[`SectionComponent Snapshots should render without title 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c6 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c6:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c6::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::placeholder {
  color: var(--colorsUtilityYin055);
}

.c3 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c4 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c4 .c5 {
  padding: 0 var(--spacing150);
}

.c4 input::-ms-clear {
  display: none;
}

.c4 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<section
  class="e-section e-section-context-page"
  data-testid="e-section-field e-field-label-testSectionTitle e-field-bind-test-section"
  id="test-section"
>
  <div
    class="e-grid-row e-grid-row-12 e-section-body"
    style="grid-template-columns: repeat(12, 1fr); padding: 0px 0px 0px 0px;"
  >
    <div
      class="e-grid-column e-grid-column-8 e-container-parent e-block-parent e-block-context-page"
      style="grid-column: span 8;"
    >
      <div
        class="e-block"
        data-testid="e-block-field e-field-bind-test-block"
      >
        <div
          class="e-block-body"
        >
          <div
            class="e-grid-row e-grid-row-8 "
            style="grid-template-columns: repeat(8, 1fr); padding: 16px 16px 16px 16px;"
          >
            <div
              class="e-grid-column e-grid-column-2 e-field-grid-column"
              style="grid-column: span 2;"
            >
              <div
                class="e-field e-text-field e-context-page"
                data-testid="e-text-field e-field-bind-field1"
              >
                <div
                  class="c0 c1"
                >
                  <div
                    class="c2"
                    data-role="field-line"
                  >
                    <div
                      class="c3"
                      data-role="input-presentation-container"
                    >
                      <div
                        class="c4"
                        role="presentation"
                      >
                        <input
                          aria-invalid="false"
                          autocomplete="off"
                          class="c5 c6"
                          data-element="input"
                          data-testid="e-text-field-input"
                          id="TestPage-field1-page"
                          name="field1"
                          type="text"
                          value="Test value"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
`;
