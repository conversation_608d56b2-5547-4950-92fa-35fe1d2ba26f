@import '../../../render/style/variables.scss';
@import '../../../render/style/mixins.scss';

.e-section {
    width: 100%;
    margin-bottom: 16px;

    .e-section-fullscreen-icon {
        position: absolute;
        top: 4px;
        right: 4px;
    }

    position: relative;

    &.e-section-context-page {
        @include page_responsive_container;

        padding: 0 16px;

        [data-component="accordion"] {
            box-shadow: var(--boxShadow100);
        }

        @include extra_small {
            margin-bottom: 0;
            padding: 0;
        }
    }

    [data-element="accordion-title"] {
        color: var(--colorsYin090);
        font-weight: 400;
        font-size: 16px;
        font-family: $fontAdelle;
    }

    &.e-section-context-detail-panel {
        margin-bottom: 8px;

        .e-section-header {
            padding: 0 32px 0 16px;
            height: 48px;

            .e-section-title {
                color: var(--colorsYin090);
                font-size: 16px;
                line-height: 32px;
                margin: 0;
            }
        }
    }

    &.e-section-context-dialog .e-section-header {
        padding-left: 16px;
        padding-right: 16px;
    }

    @media print {
        margin-bottom: 0;
    }

    @include extra_small {
        margin-bottom: 0;
    }

    .e-section-header {
        padding: 16px 0;
        display: flex;
        color: var(--colorsActionMajor500);
        font-family: $fontAdelle;

        @include extra_small {
            padding: 16px 16px 0 16px;
        }

        .e-section-title {
            color: var(--colorsYin090);
            flex: 1;
            font-size: 20px;
            line-height: 2rem;
            display: flex;
            align-items: center;
            margin: 0;
        }

        &>button>span {
            // For some reason the minor icon buttons are absolutely positioned in Carbon
            position: static;
        }
    }

    &.e-section-fullscreen {
        background: var(--colorsUtilityMajor050);
        display: block;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 10000;
        width: 100%;
        height: 100%;

        @include extra_small {
            background: var(--colorsUtilityMajor025);
        }

        .e-section-header {
            padding-left: 16px;
            padding-right: 16px;
        }

        .e-section-body {
            overflow-y: auto;
            max-height: 100%;
            padding-left: 16px;
            padding-right: 16px;

            @include extra_small {
                padding-left: 0;
                padding-right: 0;
            }

            .e-grid-row {
                margin-bottom: 80px; // Offsets header on scrolling
            }
        }
    }

    &.e-single-section {
        margin-bottom: 0;

        .e-section-header {
            @include extra_small {
                display: none;
            }
        }
    }

    // Table, NestedGrid or TableSummary as section's children (no parent block):
    >.e-grid-row>.e-grid-column>.e-table-field,
    >.e-grid-row>.e-grid-column>.e-table-summary-field,
    >.e-grid-row>.e-grid-column>.e-nested-grid-field {
        flex: 1;
        padding: 16px;
        background: var(--colorsYang100);

        @include extra_small {
            padding-top: 0;
        }

        .e-field-title {
            @include extra_small {
                >div {
                    margin: 0
                }
            }

            @include e-block-title-container-style;

            label {
                @include e-block-title-style;
                padding: 0;
            }
        }
    }
}

.e-page-mode-default .e-section:nth-last-child(2) {
    padding-bottom: 64px;
}