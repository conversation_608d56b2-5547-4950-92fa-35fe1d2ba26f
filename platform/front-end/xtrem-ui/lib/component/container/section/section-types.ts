import type { ContainerProperties } from '../../control-objects';
import type { ExtensionField, VoidPromise } from '../../field/traits';
import type { ScreenBase } from '../../../service/screen-base';
import type { ButtonProps } from 'carbon-react/esm/components/button';
import type { SectionControlObject } from './section-control-object';

export interface SectionProperties<CT extends ScreenBase = ScreenBase> extends ContainerProperties<CT> {
    /** Whether the helper panel can be extracted into a separate window or not. Defaults to false */
    isExtendable?: boolean;
    /** Whether the section is open (uncollapsed) or not. Defaults to true */
    isOpen?: boolean;

    /** The display mode of the section. In accordion mode all blocks are displayed full width, regardless to their width configuration */
    mode?: 'normal' | 'accordion';

    /** Short text that is displayed on the section anchor element next to its title */
    indicatorContent?: string;
}

export interface SectionDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<SectionProperties<CT>, '_controlObjectType'>,
        Extension<PERSON>ield<CT, SectionControlObject<CT>> {
    /** Function that gets executed when the section becomes active */
    onActive?: (this: CT) => VoidPromise;
    /** Function that gets executed when the section stops being active */
    onInactive?: (this: CT) => VoidPromise;

    /** Button label for the next button if this section is used in a wizard */
    wizardNextButtonLabel?: string;

    /** Button type for the next button if this section is used in a wizard */
    wizardNextButtonType?: ButtonProps['buttonType'];

    /** Button label for the previous button if this section is used in a wizard */
    wizardPreviousButtonLabel?: string;

    /** Button type for the previous button if this section is used in a wizard */
    wizardPreviousButtonType?: ButtonProps['buttonType'];

    /** Determines whether the data of the section is needed when the screen is initially rendered */
    isLazyLoaded?: boolean;
}

export interface SectionOverrideDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends SectionDecoratorProperties<CT> {
    /** Function that gets executed when the section becomes active */
    onActiveAfter?: (this: CT) => VoidPromise;
    /** Function that gets executed when the section stops being active */
    onInactiveAfter?: (this: CT) => VoidPromise;
}

export interface InternalSectionProperties<CT extends ScreenBase = ScreenBase> extends SectionDecoratorProperties<CT> {
    /** Set true while data is being fetched */
    isLoading?: boolean;
    /** Set true when the section data is fully loaded */
    isLoaded?: boolean;
    /** Set true when the section is ready to be displayed, after the onActive callback is executed */
    isReady?: boolean;
}
