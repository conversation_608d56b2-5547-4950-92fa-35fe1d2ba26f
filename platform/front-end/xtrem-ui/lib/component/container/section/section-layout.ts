import { AbstractLayoutBuilder } from '../../abstract-layout-builder';
import type { ContainerKey } from '../../types';

export class SectionLayout extends AbstractLayoutBuilder<ContainerKey.Section> {
    buildLayout = (): this => {
        this.layout = this.buildContainerLayout(
            this.elementId,
            'section',
            this.metadata.properties.isHiddenMobile,
            this.metadata.properties.isHiddenDesktop,
        );
        this.metadata.pageMetadata.layout.$items.push(this.layout);
        return this;
    };
}
