PATH: XTREEM/Client+Framework/Section

The top level container element. Its parent is automatically the page. Sections can be used in various ways depending on the selected page mode.

## Example

```ts
@ui.decorators.section<SamplePage>({
    title: 'Sample section',
    isTitleHidden: true,
    mode: 'normal',
})
sampleSection: ui.containers.Section;
```

### Decorator properties:

-   **title**: The title that is displayed on the top of the page. The title can be provided as a string, or a callback function returning a string. It is automatically picked up by the i18n engine and externalized for translation.
-   **isTitleHidden**: Subtitle that is displayed on the top of the normal title pages. When the page is rendered into a dialog, the subtitle is displayed under the the title.
-   **mode**: It can either be `normal` or `accordion`. In normal mode, the section renders its content as individual items, with spacing in between them. In accordion mode, all child blocks are merged together into a single container, with no space between them, all blocks are forced to full width when the accordion mode is used.
-   **isDisabled**: Determines whether the fields in the section are disabled or not. If set to true, all children field will be disabled.
-   **wizardNextButtonLabel**: Button label for the next button if this section is used in a wizard
-   **wizardNextButtonType**: Button type for the next button if this section is used in a wizard
-   **wizardPrevButtonLabel**: Button label for the previous button if this section is used in a wizard
-   **wizardPrevButtonType**: Button type for the previous button if this section is used in a wizard
-   **indicatorContent**: Short text that is displayed on the section anchor element next to its title

### Event handler decorator properties:

-   **onActive**: Event triggered when the user selects the section in tab or wizard.
-   **onInactive**: Event triggered when the user leaves the section in tab or wizard.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).
