import type { Dict } from '@sage/xtrem-shared';
import { dispatchContainerValidation } from '../../../service/dispatch-service';
import type { PageArticleItem } from '../../../service/layout-types';
import type { DataTypeDetails, FormattedNodeDetails } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import { getPageMetadata } from '../../../service/page-metadata';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { getValidationState } from '../../../service/validation-service';
import type { ScreenExtension } from '../../../types';
import { getTargetPrototype, standardExtensionDecoratorImplementation } from '../../../utils/decorator-utils';
import { AbstractDecorator } from '../../abstract-decorator';
import { SectionControlObject } from '../../control-objects';
import { Container<PERSON>ey } from '../../types';
import { SectionLayout } from '../layouts';
import { getDeclarationPackage } from '../../../service/screen-loader-service';
import type { SectionDecoratorProperties, SectionOverrideDecoratorProperties } from './section-types';

class SectionDecorator extends AbstractDecorator<ContainerKey.Section> {
    protected _layout = SectionLayout;

    protected _controlObjectConstructor = SectionControlObject;

    public build = (): this => {
        return this.buildLayout().buildControlObject().buildMetadata();
    };
}

/**
 * Initializes the decorated member as a [Section]{@link SectionControlObject} container with the provided properties
 *
 * @param properties The properties that the [Section]{@link SectionControlObject} container will be initialized with
 */
export function section<CT extends ScreenExtension<CT>>(
    properties: SectionDecoratorProperties<Extend<CT>>,
): (target: CT, name: string) => void {
    return function sectionDecorator(target: CT, name: string): void {
        const extensionPackageName = getDeclarationPackage();
        const pageMetadata = getPageMetadata(getTargetPrototype(target.constructor), target);
        pageMetadata.definitionOrder.push(name);
        pageMetadata.sectionThunks[name] = (
            nodeTypes: Dict<FormattedNodeDetails>,
            dataTypes: Dict<DataTypeDetails>,
        ): Partial<PageArticleItem> => {
            return new SectionDecorator(
                pageMetadata.target as any,
                name,
                { pageMetadata, properties, extensionPackageName },
                ContainerKey.Section,
                nodeTypes,
                dataTypes,
                {
                    dispatchSectionValidation: (): Promise<{
                        allErrors: ValidationResult[];
                        blockingErrors: ValidationResult[];
                    }> => dispatchContainerValidation(target.constructor.name, name),
                    getValidationState: async (): Promise<boolean> => getValidationState(target.constructor.name, name),
                },
            ).build().layout;
        };
    };
}
export function sectionOverride<T extends ScreenExtension<T>>(
    properties: SectionOverrideDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, ContainerKey.Section>(properties);
}
