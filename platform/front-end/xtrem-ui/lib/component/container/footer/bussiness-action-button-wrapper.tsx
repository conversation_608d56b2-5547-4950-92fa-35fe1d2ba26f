import * as React from 'react';
import { getDataTestIdAttribute } from '../../../utils/dom';

export interface BusinessActionButtonWrapperProps {
    id?: string;
    title?: string;
    children: React.ReactNode;
}
/** Adds classes and test ids to buttons so business-action-like buttons have the same selectors and style */
export function BusinessActionButtonWrapper({
    children,
    title,
    id,
}: BusinessActionButtonWrapperProps): React.ReactElement {
    return (
        <span data-testid={getDataTestIdAttribute('business-action', title, id)} className="e-business-action">
            {children}
        </span>
    );
}
