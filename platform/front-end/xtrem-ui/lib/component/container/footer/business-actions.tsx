import * as React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import { ContextType } from '../../../types';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import type { PageActionButtonType, PageActionControlObject } from '../../control-objects';
import BusinessAction from './business-action';
import MultiActionButton from 'carbon-react/esm/components/multi-action-button';
import { localize } from '../../../service/i18n-service';
import { isNil } from 'lodash';
import styled from 'styled-components';
import { getPageDefinitionFromState, getPagePropertiesFromPageDefinition } from '../../../utils/state-utils';
import { getElementAccessStatus } from '../../../utils/access-utils';
import { objectKeys } from '@sage/xtrem-shared';

const HiddenButton = styled.button`
    width: 0;
    height: 0;
    overflow: hidden;
    border: none;
    padding: 0;
    margin: 0;
    opacity: 0;
`;
export interface BusinessActionsExternalProps {
    businessActions?: PageActionControlObject[];
    contextType: ContextType;
    defaultButtonType: PageActionButtonType;
    maxMainActions?: number;
    screenId: string;
    pendoId?: string;
}

export interface BusinessActionsProps extends BusinessActionsExternalProps {
    // The following property is necessary to trigger a render cycle after disabling actions
    // eslint-disable-next-line react/no-unused-prop-types
    enabledBusinessActions: string[];
    // The following property is necessary to trigger a render cycle after hiding actions
    visibleBusinessActions: string[];
}
interface CalculatedBusinessActions {
    additionalActions: PageActionControlObject[];
    hasActions: boolean;
    hasAdditionalActions: boolean;
    hasTertiaryActions: boolean;
    mainActions: PageActionControlObject[];
    tertiaryDialogActions: PageActionControlObject<any>[];
}

export function BusinessActions(props: BusinessActionsProps): React.ReactElement {
    const mainActionsNumber = !isNil(props.maxMainActions) ? props.maxMainActions : 3;

    const getBusinessActions = (): CalculatedBusinessActions => {
        const visibleActions = (props.businessActions || []).filter(
            action => props.visibleBusinessActions.indexOf(action.id) !== -1,
        );

        let mainActions =
            visibleActions.length === mainActionsNumber
                ? visibleActions
                : visibleActions.slice(0, mainActionsNumber - 1);

        const tertiaryDialogActions: PageActionControlObject[] = [];
        if (props.contextType === ContextType.dialog) {
            const isActionTertiary = (buttonType: PageActionButtonType = props.defaultButtonType): boolean =>
                buttonType === 'tertiary';
            tertiaryDialogActions.push(...mainActions.filter(action => isActionTertiary(action.properties.buttonType)));
            mainActions = mainActions.filter(action => !isActionTertiary(action.properties.buttonType));
        }

        const additionalActions: PageActionControlObject[] =
            visibleActions.length > mainActionsNumber ? visibleActions.slice(mainActionsNumber - 1) : [];

        return {
            additionalActions,
            hasActions: visibleActions.length > 0,
            hasAdditionalActions: additionalActions.length > 0,
            hasTertiaryActions: tertiaryDialogActions.length > 0,
            mainActions,
            tertiaryDialogActions,
        };
    };

    if (props.businessActions && props.businessActions.filter(ba => !ba.isHidden).length > 0) {
        const businessActions = getBusinessActions();
        const baseClassName = `e-${props.contextType}-business-actions`;
        return (
            <>
                {businessActions.hasActions && (
                    <>
                        <div className={baseClassName} data-testid={baseClassName}>
                            {businessActions.hasAdditionalActions && (
                                <MultiActionButton
                                    text={localize('@sage/xtrem-ui/footer-actions-more-button', 'More')}
                                    className={`${baseClassName}-more`}
                                    data-testid={`${baseClassName}-more`}
                                    buttonType="secondary"
                                >
                                    {businessActions.additionalActions.map(action => (
                                        <BusinessAction
                                            key={action.id}
                                            screenId={props.screenId}
                                            id={action.id}
                                            defaultButtonType={props.defaultButtonType}
                                            alwaysTriggerFieldEvent={true}
                                            buttonTypeOverride="secondary"
                                            pendoId={props.pendoId}
                                        />
                                    ))}
                                </MultiActionButton>
                            )}
                            {businessActions.mainActions.map(action => (
                                <BusinessAction
                                    key={action.id}
                                    screenId={props.screenId}
                                    id={action.id}
                                    defaultButtonType={props.defaultButtonType}
                                    pendoId={props.pendoId}
                                />
                            ))}
                        </div>
                        {businessActions.hasTertiaryActions && (
                            <div className={`${baseClassName}-tertiary`} data-testid={`${baseClassName}-tertiary`}>
                                {businessActions.tertiaryDialogActions.map(action => (
                                    <BusinessAction
                                        key={action.id}
                                        screenId={props.screenId}
                                        id={action.id}
                                        defaultButtonType={props.defaultButtonType}
                                        pendoId={props.pendoId}
                                    />
                                ))}
                            </div>
                        )}
                    </>
                )}
            </>
        );
    }
    // This element cannot be a <noscript> tag because that doesn't enforce the flex spacing
    // Must return a "valid" html element to avoid focus going to address bar from reference field in table.
    return <HiddenButton aria-hidden="true" />;
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: BusinessActionsExternalProps,
): BusinessActionsProps => {
    const pageDefinition = getPageDefinitionFromState(props.screenId, state);
    const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);

    const pageActions = pageDefinition.metadata.pageActions;
    const visibleBusinessActions = objectKeys(pageActions).filter(elementId => {
        const actionProperties = pageDefinition.metadata.uiComponentProperties[elementId];
        const accessRule = getElementAccessStatus({
            accessBindings: pageDefinition.accessBindings,
            bind: elementId,
            elementProperties: actionProperties,
            contextNode: pageProperties?.node,
            nodeTypes: state.nodeTypes,
            dataTypes: state.dataTypes,
        });

        return (
            !resolveByValue({
                screenId: props.screenId,
                rowValue: undefined,
                fieldValue: undefined,
                skipHexFormat: true,
                propertyValue: pageActions[elementId].isHidden,
            }) &&
            (!actionProperties.access || accessRule === 'authorized')
        );
    });

    const enabledBusinessActions = objectKeys(pageActions).filter(
        key =>
            !resolveByValue({
                screenId: props.screenId,
                rowValue: undefined,
                fieldValue: undefined,
                skipHexFormat: true,
                propertyValue: pageActions[key].isDisabled,
            }),
    );

    visibleBusinessActions.sort();
    enabledBusinessActions.sort();

    return {
        ...props,
        enabledBusinessActions,
        visibleBusinessActions,
    };
};

export default connect(mapStateToProps)(BusinessActions);
