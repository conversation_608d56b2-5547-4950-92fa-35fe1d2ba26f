// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Business actions Snapshots connected should render with default properties 1`] = `
<div>
  <div
    class="e-page-business-actions"
    data-testid="e-page-business-actions"
  >
    <div
      id="mockBusinessAction"
    >
      primary
    </div>
    <div
      id="mockBusinessAction"
    >
      primary
    </div>
    <div
      id="mockBusinessAction"
    >
      primary
    </div>
  </div>
</div>
`;

exports[`Business actions Snapshots connected should render with no page actions available 1`] = `
.c0 {
  width: 0;
  height: 0;
  overflow: hidden;
  border: none;
  padding: 0;
  margin: 0;
  opacity: 0;
}

<div>
  <button
    aria-hidden="true"
    class="c0"
  />
</div>
`;

exports[`Business actions Snapshots unconnected should render with default properties 1`] = `
<div>
  <div
    class="e-page-business-actions"
    data-testid="e-page-business-actions"
  >
    <div
      id="mockBusinessAction"
    >
      primary
    </div>
    <div
      id="mockBusinessAction"
    >
      primary
    </div>
    <div
      id="mockBusinessAction"
    >
      primary
    </div>
  </div>
</div>
`;

exports[`Business actions Snapshots unconnected should render with no page actions available 1`] = `
.c0 {
  width: 0;
  height: 0;
  overflow: hidden;
  border: none;
  padding: 0;
  margin: 0;
  opacity: 0;
}

<div>
  <button
    aria-hidden="true"
    class="c0"
  />
</div>
`;
