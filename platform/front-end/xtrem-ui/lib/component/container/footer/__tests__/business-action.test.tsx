import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import {
    addBusinessActionToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';
import type { XtremAppState } from '../../../../redux';
import * as events from '../../../../utils/events';
import type { PageActionProperties } from '../../../control-objects';
import ConnectedBusinessAction from '../business-action';
import { fireEvent, render } from '@testing-library/react';

describe('BusinessAction', () => {
    const screenId = 'TestPage';
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let actionProperties: PageActionProperties;
    const id = 'action';

    beforeEach(() => {
        actionProperties = { title: 'Business Action' };
        const state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        addBusinessActionToState(state, screenId, id, actionProperties);
        mockStore = getMockStore(state);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('connected', () => {
        describe('Snapshots', () => {
            it('should render', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedBusinessAction screenId={screenId} id={id} defaultButtonType="primary" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render disabled', () => {
                actionProperties.isDisabled = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedBusinessAction screenId={screenId} id={id} defaultButtonType="primary" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render loading', () => {
                actionProperties.isLoading = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedBusinessAction screenId={screenId} id={id} defaultButtonType="primary" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with a tooltip', () => {
                actionProperties.helperText = 'This is an action!';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedBusinessAction screenId={screenId} id={id} defaultButtonType="primary" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });
        });

        describe('interactions', () => {
            it('should trigger the corresponding action on clock', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedBusinessAction screenId={screenId} id={id} defaultButtonType="primary" />
                    </Provider>,
                );
                expect(events.triggerFieldEvent).not.toHaveBeenCalled();

                fireEvent.click(container.querySelector('button')!);

                expect(events.triggerFieldEvent).toHaveBeenCalledTimes(1);
                expect(events.triggerFieldEvent).toHaveBeenCalledWith(screenId, 'action', 'onClick');
            });
        });
    });
});
