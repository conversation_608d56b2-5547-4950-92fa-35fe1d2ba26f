// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`BusinessAction connected Snapshots should render 1`] = `
.c0 {
  padding-left: 24px;
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: var(--colorsActionMajor500);
  border-color: transparent;
  color: var(--colorsActionMajorYang100);
  font-size: var(--fontSizes100);
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  background: var(--colorsActionMajor600);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <span
    class="e-business-action"
    data-testid="e-business-action-field e-field-label-businessAction e-field-bind-action"
  >
    <button
      class="c0"
      data-component="button"
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="c1"
          data-element="main-text"
        >
          Business Action
        </span>
      </span>
    </button>
  </span>
</div>
`;

exports[`BusinessAction connected Snapshots should render disabled 1`] = `
.c0 {
  padding-left: 24px;
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: not-allowed;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: var(--colorsActionMajor500);
  border-color: transparent;
  color: var(--colorsActionMajorYang100);
  background: var(--colorsActionDisabled500);
  color: var(--colorsActionMajorYin030);
  font-size: var(--fontSizes100);
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  background: var(--colorsActionMajor600);
}

.c0:hover {
  background: var(--colorsActionDisabled500);
}

.c0 img,
.c0 svg {
  opacity: 0.3;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <span
    class="e-business-action"
    data-testid="e-business-action-field e-field-label-businessAction e-field-bind-action"
  >
    <button
      class="c0"
      data-component="button"
      disabled=""
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="c1"
          data-element="main-text"
        >
          Business Action
        </span>
      </span>
    </button>
  </span>
</div>
`;

exports[`BusinessAction connected Snapshots should render loading 1`] = `
.c2 {
  margin-right: 8px;
  text-align: center;
  white-space: nowrap;
}

.c0 {
  padding-left: 8px;
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: not-allowed;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: var(--colorsActionMajor500);
  border-color: transparent;
  color: var(--colorsActionMajorYang100);
  background: var(--colorsActionDisabled500);
  color: var(--colorsActionMajorYin030);
  font-size: var(--fontSizes100);
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  background: var(--colorsActionMajor600);
}

.c0:hover {
  background: var(--colorsActionDisabled500);
}

.c0 img,
.c0 svg {
  opacity: 0.3;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <span
    class="e-business-action"
    data-testid="e-business-action-field e-field-label-businessAction e-field-bind-action"
  >
    <button
      class="c0"
      data-component="button"
      disabled=""
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="c1"
          data-element="main-text"
        >
          <div
            class="c2"
            data-component="loader"
          >
            Loading
          </div>
          Business Action
        </span>
      </span>
    </button>
  </span>
</div>
`;

exports[`BusinessAction connected Snapshots should render with a tooltip 1`] = `
.c0 {
  padding-left: 24px;
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: var(--colorsActionMajor500);
  border-color: transparent;
  color: var(--colorsActionMajorYang100);
  font-size: var(--fontSizes100);
  min-height: 40px;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  background: var(--colorsActionMajor600);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <span
    class="e-business-action"
    data-testid="e-business-action-field e-field-label-businessAction e-field-bind-action"
  >
    <button
      class="c0"
      data-component="button"
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="c1"
          data-element="main-text"
        >
          Business Action
        </span>
      </span>
    </button>
  </span>
</div>
`;
