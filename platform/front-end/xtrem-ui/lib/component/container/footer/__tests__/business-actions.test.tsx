jest.mock(
    '../business-action',
    () =>
        function MockBusinessAction(props: BusinessActionProps) {
            return (
                <div id="mockBusinessAction" key={props.id}>
                    {props.defaultButtonType}
                </div>
            );
        },
);
jest.mock(
    '../../../ui/xtrem-action-popover',
    () =>
        function MockXtremActionPopover({ isDisabled }: { isDisabled: boolean }) {
            return <button type="submit" id="mockXtremActionPopover" disabled={isDisabled} />;
        },
);

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { PageAction } from '../../../..';
import {
    addBusinessActionToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';
import type { XtremAppState } from '../../../../redux';
import { ContextType } from '../../../../types';
import type { BusinessActionProps } from '../business-action';
import type { BusinessActionsProps, BusinessActionsExternalProps } from '../business-actions';
import ConnectedBusinessActions, { BusinessActions } from '../business-actions';
import type { PageActionProperties } from '../../../page-action/page-action-control-object';
import { render } from '@testing-library/react';

describe('Business actions', () => {
    const screenId = 'TestPage';
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let state: XtremAppState;
    let pageActions: PageAction[] = [];

    beforeEach(() => {
        state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);

        pageActions = [
            addBusinessActionToState(state, screenId, 'businessAction1', {
                title: 'Business Action 1',
            }),
            addBusinessActionToState(state, screenId, 'businessAction2', {
                title: 'Business Action 2',
                isDisabled: true,
            }),
            addBusinessActionToState(state, screenId, 'businessAction3', {
                title: 'Business Action 3',
                icon: 'pdf',
            }),
        ];

        mockStore = getMockStore(state);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('Snapshots', () => {
        const renderBusinessActionsComponent = (businessActionsComponent: JSX.Element) =>
            render(businessActionsComponent);

        describe('connected', () => {
            const getConnectedBusinessActions = (businessActions?: PageAction[]) => {
                const componentProps: BusinessActionsExternalProps = {
                    contextType: ContextType.page,
                    defaultButtonType: 'primary',
                    screenId,
                    ...(businessActions && { businessActions }),
                };
                return (
                    <Provider store={mockStore}>
                        <ConnectedBusinessActions {...componentProps} />
                    </Provider>
                );
            };

            it('should render with default properties', () => {
                const { container } = renderBusinessActionsComponent(getConnectedBusinessActions(pageActions));
                expect(container).toMatchSnapshot();
            });

            it('should render with no page actions available', () => {
                const { container } = renderBusinessActionsComponent(getConnectedBusinessActions());
                expect(container).toMatchSnapshot();
            });
        });

        describe('unconnected', () => {
            const getBusinessActions = (businessActions?: PageAction[]) => (
                <BusinessActions
                    businessActions={businessActions}
                    contextType={ContextType.page}
                    defaultButtonType="primary"
                    enabledBusinessActions={['businessAction1', 'businessAction2', 'businessAction3']}
                    screenId={screenId}
                    visibleBusinessActions={pageActions.filter(a => !a.isHidden).map(a => a.id)}
                />
            );
            it('should render with default properties', () => {
                const { container } = renderBusinessActionsComponent(getBusinessActions(pageActions));
                expect(container).toMatchSnapshot();
            });

            it('should render with no page actions available', () => {
                const { container } = renderBusinessActionsComponent(getBusinessActions([]));
                expect(container).toMatchSnapshot();
            });
        });
    });

    describe('rendering', () => {
        const getBusinessActions = (businessActionsProps?: Partial<BusinessActionsProps>) => {
            const componentProps: BusinessActionsProps = {
                businessActions: pageActions,
                contextType: ContextType.page,
                defaultButtonType: 'primary',
                enabledBusinessActions: ['businessAction1', 'businessAction2', 'businessAction3'],
                screenId,
                visibleBusinessActions: pageActions.filter(a => !a.isHidden).map(a => a.id),
                ...businessActionsProps,
            };

            return (
                <Provider store={mockStore}>
                    <BusinessActions {...componentProps} />
                </Provider>
            );
        };

        const mountBusinessActionsComponent = (businessActionsProps?: Partial<BusinessActionsProps>) =>
            render(getBusinessActions(businessActionsProps));

        const expectMainActions = (
            container: HTMLElement,
            contextType: ContextType,
            hasMainActions: boolean,
            totalMainActions?: number,
        ) => {
            const divMainActions = container.querySelectorAll(`div[data-testid="e-${contextType}-business-actions"]`);
            if (hasMainActions) {
                expect(divMainActions).toHaveLength(1);
                const businessActions = divMainActions[0].querySelectorAll('#mockBusinessAction');
                expect(businessActions).toHaveLength(totalMainActions || 0);
            } else {
                expect(divMainActions).toHaveLength(0);
            }
        };

        const expectMoreActions = (container: HTMLElement, contextType: ContextType, totalMoreActions: number) => {
            const spanMoreActions = container.querySelectorAll(
                `span[data-testid="e-${contextType}-business-actions-more"]`,
            );
            expect(spanMoreActions).toHaveLength(totalMoreActions);
        };

        const expectTertiaryActions = (
            container: HTMLElement,
            contextType: ContextType,
            totalTertiaryActions: number,
        ) => {
            const divTertiaryActions = container.querySelectorAll(
                `div[data-testid="e-${contextType}-business-actions-tertiary"]`,
            );
            if (totalTertiaryActions) {
                expect(divTertiaryActions).toHaveLength(1);
                const businessActions = divTertiaryActions[0].querySelectorAll('#mockBusinessAction');
                expect(businessActions).toHaveLength(totalTertiaryActions);
            } else {
                expect(divTertiaryActions).toHaveLength(totalTertiaryActions);
            }
        };

        /*
        it('should render only two business actions', () => {
            const contextType = ContextType.page;
            const wrapper = mountBusinessActionsComponent();

            expectMainActions(wrapper, contextType, true, 2);

            expectMoreActions(wrapper, contextType, 1);

            expectTertiaryActions(wrapper, contextType, 0);
        });

        it('should disable the popover button if all actions in the popover are disabled', () => {
            const contextType = ContextType.page;

            const wrapper = mountBusinessActionsComponent({
                enabledBusinessActions: ['businessAction1', 'businessAction2'],
            });

            expectMainActions(wrapper, contextType, true, 2);
            expectMoreActions(wrapper, contextType, 1);
            expect(wrapper.find('#mockXtremActionPopover')).toBeDisabled();
        });

        it('should not disable the popover button some actions in the popover are available', () => {
            const contextType = ContextType.page;

            const wrapper = mountBusinessActionsComponent({
                enabledBusinessActions: ['businessAction1', 'businessAction2', 'businessAction3'],
            });

            expectMainActions(wrapper, contextType, true, 2);
            expectMoreActions(wrapper, contextType, 1);
            expect(wrapper.find('#mockXtremActionPopover')).not.toBeDisabled();
        });
*/
        it('should render with no page actions available', () => {
            const { container } = mountBusinessActionsComponent({ businessActions: [], visibleBusinessActions: [] });
            expectMainActions(container, ContextType.page, false);
        });

        /*
        it('should render one main and one tertiary business actions in a dialog', () => {
            const contextType = ContextType.dialog;
            const actionProperties = state.screenDefinitions[screenId].metadata.uiComponentProperties
                .businessAction1 as PageActionProperties;
            actionProperties.buttonType = 'tertiary';
            const wrapper = mountBusinessActionsComponent({ contextType });

            expectMainActions(wrapper, contextType, true, 1);

            expectMoreActions(wrapper, contextType, 1);

            expectTertiaryActions(wrapper, contextType, 1);
        });
        */

        it('should render 3 main business actions in a dialog when maxMainActions is 3', () => {
            const contextType = ContextType.dialog;
            const actionProperties = state.screenDefinitions[screenId].metadata.uiComponentProperties
                .businessAction1 as PageActionProperties;
            actionProperties.buttonType = 'tertiary';
            const { container } = mountBusinessActionsComponent({ contextType, maxMainActions: 3 });

            expectMainActions(container, contextType, true, 2);

            expectMoreActions(container, contextType, 0);

            expectTertiaryActions(container, contextType, 1);
        });

        /*
        it('should render only main and more business actions in a page', () => {
            const contextType = ContextType.page;
            const actionProperties = state.screenDefinitions[screenId].metadata.uiComponentProperties
                .businessAction1 as PageActionProperties;
            actionProperties.buttonType = 'tertiary';
            const wrapper = mountBusinessActionsComponent();

            expectMainActions(wrapper, contextType, true, 2);

            expectMoreActions(wrapper, contextType, 1);

            expectTertiaryActions(wrapper, contextType, 0);
        });

        it('should render only main and more business actions in a page', () => {
            const contextType = ContextType.page;
            const wrapper = mountBusinessActionsComponent({ defaultButtonType: 'tertiary' });

            expectMainActions(wrapper, contextType, true, 2);

            expectMoreActions(wrapper, contextType, 1);

            expectTertiaryActions(wrapper, contextType, 0);
        });

        it('should render only main and tertiary business actions in a dialog', () => {
            const contextType = ContextType.dialog;
            const wrapper = mountBusinessActionsComponent({
                contextType,
                defaultButtonType: 'tertiary',
            });

            expectMainActions(wrapper, contextType, true);

            expectMoreActions(wrapper, contextType, 1);

            expectTertiaryActions(wrapper, contextType, 2);
        });
        */
    });
});
