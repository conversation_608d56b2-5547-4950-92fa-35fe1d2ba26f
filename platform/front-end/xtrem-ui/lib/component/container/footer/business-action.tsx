import Button from 'carbon-react/esm/components/button';
import Loader from 'carbon-react/esm/components/loader';
import * as React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import { triggerFieldEvent } from '../../../utils/events';
import type { PageActionButtonType, PageActionProperties } from '../../control-objects';
import { getFieldTitle, isFieldDisabled, isFieldHidden } from '../../field/carbon-helpers';
import type { IconType } from 'carbon-react/esm/components/icon';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import { BusinessActionButtonWrapper } from './bussiness-action-button-wrapper';
import { getDataTestIdAttribute } from '../../../utils/dom';
import type { OnTelemetryEventFunction } from '../../../redux/state';
import Tooltip from 'carbon-react/esm/components/tooltip';
import { localize } from '../../../service/i18n-service';

export interface BusinessActionExternalProps {
    id?: string;
    screenId: string;
    defaultButtonType?: PageActionButtonType;
    buttonTypeOverride?: PageActionButtonType;
    isIconOnly?: boolean;
    onClick?: () => void;
    parentRef?: React.Ref<HTMLButtonElement>;
    size?: 'small' | 'medium';
    isSeparated?: boolean;
    alwaysTriggerFieldEvent?: boolean;
    skipWrapping?: boolean;
    pendoId?: string;
    title?: string;
}

export interface BusinessActionProps extends BusinessActionExternalProps {
    buttonType?: PageActionButtonType;
    helperText?: string;
    icon?: IconType;
    isDestructive?: boolean;
    isDisabled?: boolean;
    isHidden?: boolean;
    isLoading?: boolean;
    onTelemetryEvent?: OnTelemetryEventFunction;
    title?: string;
}
export class BusinessAction extends React.Component<BusinessActionProps> {
    onClick = (): void => {
        if (this.props.onClick) {
            this.props.onClick();
            if (!this.props.alwaysTriggerFieldEvent) {
                // The multi action button registers an onClick prop even if we don't want it.
                return;
            }
        }

        if (this.props.id) {
            triggerFieldEvent(this.props.screenId, this.props.id, 'onClick');
            this.props.onTelemetryEvent?.(`businessActionTriggered-${this.props.id}`, {
                elementId: this.props.id,
                screen: this.props.screenId,
            });
        }
    };

    render(): React.ReactNode {
        if (this.props.isHidden) {
            return null;
        }

        const isIconOnly = this.props.isIconOnly && this.props.icon;

        let button = (
            <Button
                paddingLeft={this.props.isLoading ? '8px' : '24px'}
                ref={this.props.parentRef}
                buttonType={this.props.buttonType || this.props.defaultButtonType}
                disabled={this.props.isDisabled || this.props.isLoading}
                destructive={this.props.isDestructive}
                iconType={this.props.icon}
                mr={this.props.isSeparated ? '8px' : undefined}
                onClick={this.onClick}
                size={this.props.size || 'medium'}
                iconTooltipMessage={isIconOnly ? this.props.title : undefined}
                aria-label={isIconOnly ? this.props.title : undefined}
                data-testid={
                    this.props.skipWrapping
                        ? getDataTestIdAttribute('business-action', this.props.title, this.props.id)
                        : undefined
                }
                data-pendoid={
                    this.props.pendoId && this.props.id ? `${this.props.pendoId}-${this.props.id}` : undefined
                }
            >
                {this.props.isLoading && <Loader size="small" marginRight="8px" />}
                {isIconOnly ? undefined : this.props.title}
            </Button>
        );

        if (this.props.isLoading || this.props.helperText) {
            const tooltipMessage = this.props.isLoading
                ? localize('@sage/xtrem-ui/business-action-in-progress', 'This process is running in the background.')
                : this.props.helperText;

            button = <Tooltip message={tooltipMessage}>{button}</Tooltip>;
        }

        if (this.props.skipWrapping) {
            return button;
        }

        return (
            <BusinessActionButtonWrapper id={this.props.id} title={this.props.title}>
                {button}
            </BusinessActionButtonWrapper>
        );
    }
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: BusinessActionExternalProps): BusinessActionProps => {
    if (!props.id) {
        throw new Error('Action id must be supplied when the business action used in connected mode');
    }

    const pageDefinition = getPageDefinitionFromState(props.screenId, state);
    const actionProperties = pageDefinition.metadata.uiComponentProperties[props.id] as PageActionProperties;

    return {
        ...props,
        buttonType: props.buttonTypeOverride || actionProperties.buttonType,
        helperText: actionProperties.helperText,
        icon: actionProperties.icon,
        isDestructive: actionProperties.isDestructive,
        isLoading: actionProperties.isLoading,
        isHidden: isFieldHidden(props.screenId, actionProperties, null),
        isDisabled: isFieldDisabled(props.screenId, actionProperties, null, null),
        title: props.title || getFieldTitle(props.screenId, actionProperties, null),
        onTelemetryEvent: state.applicationContext?.onTelemetryEvent,
    };
};

export const ConnectedBusinessAction = connect(mapStateToProps)(BusinessAction);
ConnectedBusinessAction.displayName = 'Button';

export default ConnectedBusinessAction;
