## Documentation - GridRowBlock
| name             | optional | description                                                                                                                                                                                                                                    | type                                                                                                                                                                                                   | default                                                                                  |
| ---------------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------- |
| boundTo          | false    |                                                                                                                                                                                                                                                | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;TableControlObject<any,&nbsp;ScreenBase<any,&nbsp;any>>&nbsp;|&nbsp;NestedGridControlObject<[any,&nbsp;any],&nbsp;ScreenBase<any,&nbsp;any>></pre> |                                                                                          |
| access           | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">AccessConfiguration</pre>                                                                                                                                                       | <pre lang="javascript">{&nbsp;node:&nbsp;'<node>',&nbsp;bind:&nbsp;'<bind>'&nbsp;}</pre> |
| fieldFilter      | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">(this:&nbsp;CT,&nbsp;elementId:&nbsp;keyof&nbsp;CT)&nbsp;=>&nbsp;boolean</pre>                                                                                                  | <pre lang="javascript">function(elementId<br>)&nbsp;{<br><br>}</pre>                     |
| insertBefore     | true     | The field before this extension field is inserted                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;T</pre>                                                                                                                                            | <pre lang="javascript">undefined</pre>                                                   |
| isDisabled       | true     | Whether the HTML element is disabled or not. Defaults to false The difference with readOnly is that disabled suggests that the field is not editable for some validation reason (e.g. a button which can't be clicked due to validation errors | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>                                                                                                 | <pre lang="javascript">false</pre>                                                       |
| isHidden         | true     | Whether the HTML element is hidden or not. Defaults to false                                                                                                                                                                                   | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>                                                                                                 | <pre lang="javascript">false</pre>                                                       |
| isHiddenDesktop  | true     | Whether the element is hidden or not in desktop devices. Defaults to false                                                                                                                                                                     | <pre lang="javascript">boolean</pre>                                                                                                                                                                   | <pre lang="javascript">false</pre>                                                       |
| isHiddenMobile   | true     | Whether the element is hidden or not in mobile devices. Defaults to false                                                                                                                                                                      | <pre lang="javascript">boolean</pre>                                                                                                                                                                   | <pre lang="javascript">false</pre>                                                       |
| isTitleHidden    | true     | Whether the element title is hidden or not. Defaults to false                                                                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                                                                                                                   | <pre lang="javascript">false</pre>                                                       |
| isTransient      | true     | Whether the value is bound to a GraphQL node (transient = false) or not (transient = true). Defaults to false                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                                                                                                                   | <pre lang="javascript">false</pre>                                                       |
| parent           | true     | The container in which this component will render                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;P</pre>                                                                                                                                            | <pre lang="javascript">undefined</pre>                                                   |
| readOnlyOverride | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">(this:&nbsp;CT,&nbsp;elementId:&nbsp;keyof&nbsp;CT)&nbsp;=>&nbsp;boolean&nbsp;|&nbsp;void&nbsp;|&nbsp;undefined</pre>                                                           | <pre lang="javascript">function(elementId<br>)&nbsp;{<br><br>}</pre>                     |
| selectedLevel    | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">number</pre>                                                                                                                                                                    | <pre lang="javascript">0</pre>                                                           |
| selectedRecordId | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">string</pre>                                                                                                                                                                    | <pre lang="javascript">''</pre>                                                          |
| title            | true     | The title of the HTML element                                                                                                                                                                                                                  | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;any,&nbsp;Dict<any>></pre>                                                                                                  | <pre lang="javascript">''</pre>                                                          |
| validation       | true     | Container validation rule                                                                                                                                                                                                                      | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                                                                                                                         | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                              |