import * as React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import type { ReduxResponsive } from '../../../redux/state';
import type { PageArticleItem } from '../../../service/layout-types';
import type { ScreenBase } from '../../../service/screen-base';
import type { ContextType } from '../../../types';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { isFieldDisabled } from '../../field/carbon-helpers';
import type { NestedGridProperties, TableControlObject } from '../../field/field-control-objects';
import type { InternalTableProperties, TableProperties } from '../../field/table/table-component-types';
import type { NestedField, NestedFieldTypes } from '../../nested-fields';
import { ConnectedNestedBlock } from '../../ui/nested-block';
import type { GridRowBlockDecoratorProperties } from './grid-row-block-types';

const getColumns = (
    screenId: string,
    columns: NestedField<ScreenBase, NestedFieldTypes>[],
    fieldFilter?: (this: any, elementId: keyof any) => boolean,
): NestedField<ScreenBase, NestedFieldTypes>[] =>
    !fieldFilter
        ? columns
        : columns.filter(f =>
              resolveByValue({
                  screenId,
                  propertyValue: fieldFilter,
                  fieldValue: f.properties.bind,
                  skipHexFormat: true,
                  rowValue: null, // Here we don't have an individual context for each fields
              }),
          );

export interface GridRowBlockComponentExternalProps {
    item: Partial<PageArticleItem>;
    screenId: string;
    contextType?: ContextType;
    availableColumns: number;
    isParentDisabled?: boolean;
    /**
     * Indicates if any of the parents in the layout structure is hidden, it is required so we can cascade
     * down the hidden status and mark the hidden inputs not focusable
     * */
    isParentHidden?: boolean;
}

export interface GridRowBlockComponentProps extends GridRowBlockComponentExternalProps {
    screenType: string;
    fieldProperties: GridRowBlockDecoratorProperties;
    browser?: ReduxResponsive;
    referredTableProperties?: InternalTableProperties | NestedGridProperties;
    parentElementId: string;
}

export function GridRowBlockComponent(props: GridRowBlockComponentProps): React.ReactElement | null {
    if (!props.fieldProperties.selectedRecordId) {
        return null;
    }

    const columns = (props.referredTableProperties as NestedGridProperties).levels
        ? (props.referredTableProperties as NestedGridProperties).levels[props.fieldProperties.selectedLevel || 0]
              .columns
        : ((props.referredTableProperties as InternalTableProperties).columns || []).filter(
              column =>
                  !(props.referredTableProperties as InternalTableProperties).hiddenColumns?.find(
                      (c: string) => c === column.properties.bind,
                  ),
          );

    return (
        <ConnectedNestedBlock
            availableColumns={props.availableColumns}
            contextNode={props.referredTableProperties?.node ? String(props.referredTableProperties.node) : undefined}
            contextType={props.contextType}
            focusPosition={null}
            hideValidationSummary={true}
            isDisabled={props.isParentDisabled || isFieldDisabled(props.screenId, props.fieldProperties, null, null)}
            isHidden={props.fieldProperties.isHidden}
            isTitleHidden={props.fieldProperties.isTitleHidden}
            item={props.item}
            level={props.fieldProperties.selectedLevel}
            nestedFields={getColumns(props.screenId, columns, props.fieldProperties.fieldFilter)}
            parentElementId={props.parentElementId}
            readOnlyOverride={props.fieldProperties.readOnlyOverride}
            recordId={props.fieldProperties.selectedRecordId}
            screenId={props.screenId}
            title={props.fieldProperties.title}
        />
    );
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: GridRowBlockComponentExternalProps,
): GridRowBlockComponentProps => {
    const fieldProperties = state.screenDefinitions[props.screenId].metadata.uiComponentProperties[
        props.item.$containerId!
    ] as GridRowBlockDecoratorProperties;
    const referredTable = resolveByValue({
        propertyValue: fieldProperties.boundTo,
        screenId: props.screenId,
        skipHexFormat: true,
        rowValue: null, // Static value, it cannot change on run time.
    }) as TableControlObject;
    const parentElementId = referredTable.id;
    const referredTableProperties = state.screenDefinitions[props.screenId].metadata.uiComponentProperties[
        parentElementId
    ] as TableProperties | NestedGridProperties;

    return {
        ...props,
        fieldProperties,
        screenType: state.screenDefinitions[props.screenId].type,
        browser: state.browser,
        referredTableProperties,
        parentElementId,
    };
};

export const ConnectedGridRowBlockComponent = connect(mapStateToProps)(GridRowBlockComponent);

export default ConnectedGridRowBlockComponent;
