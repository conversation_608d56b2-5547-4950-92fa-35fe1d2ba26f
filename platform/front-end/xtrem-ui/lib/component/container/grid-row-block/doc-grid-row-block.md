PATH: XTREEM/UI+Field+Widgets/Grid+Row+Block

## Introduction

The Grid Row Block is a special UI container component. It represents a single nested record of the table that it is bound to. Changes are automatically reflected in each direction. It enables editing for table or nested grid rows in a convenient way for the user. The main use case for such element is the detail panel, but this block can be used in any section.

## Example:

```ts

@ui.decorators.tableField<TableWithDetailPanel, ShowCaseProduct>({
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    bind: 'products',
    title: 'Products',
    canExport: false,
    columns: [
        ui.nestedFields.text({
            bind: '_id',
            title: 'Id',
            isHiddenDesktop: true,
            isReadOnly: true,
        }),
        ui.nestedFields.text({
            bind: 'product',
            title: 'Product',
        }),
        ui.nestedFields.text({
            bind: 'description',
            isFullWidth: true,
            title: 'Description',
        }),
        ui.nestedFields.progress({
            bind: 'progress',
            color: (v: number) => {
                if (v < 30) {
                    return ui.tokens.colorsSemanticNegative500;
                } else if (v < 50) {
                    return ui.tokens.colorsSemanticCaution500;
                } else if (v > 90) {
                    return ui.tokens.colorsSemanticPositive500;
                } else {
                    return ui.tokens.colorsUtilityMajor200;
                }
            },
            title: 'Progress',
        }),
        ui.nestedFields.numeric({
            bind: 'tax',
            title: 'Tax',
            prefix: 'T',
            scale: 2,
            isExcludedFromMainField: true,
            isHiddenDesktop: true,
        }),
        ui.nestedFields.label<TableWithDetailPanel>({
            bind: 'amount',
            title: 'Amount',
            prefix: '$',
            isExcludedFromMainField: true,
            isHiddenDesktop: true,
        }),
        ui.nestedFields.reference<TableWithDetailPanel, ShowCaseProduct, ShowCaseProvider>({
            bind: 'provider',
            title: 'Provider',
            valueField: 'textField',
            node: '@sage/xtrem-show-case/ShowCaseProvider',
            minLookupCharacters: 0,
            imageField: 'logo',
            onClick(id: string, raw: any) {
                console.log('CLICKED NO LOOKUP', { id, raw });
            },
            onChange(id: string, raw: any) {
                console.log('CHANGED NO LOOKUP', { id, raw });
            },
        }),
        ui.nestedFields.select<TableWithDetailPanel>({
            bind: 'category',
            title: 'Category',
            optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
        }),
        ui.nestedFields.image({
            bind: 'imageField',
            title: 'Image',
            isExcludedFromMainField: true,
        }),
        ui.nestedFields.icon({
            bind: 'qty',
            title: 'Icon',
            isExcludedFromMainField: true,
        }),
    ],
    orderBy: {
        product: 1,
    },
    parent() {
        return this.fieldBlock;
    },
    onRowClick(_id: string) {
        this.$.detailPanel.isHidden = false;
        this.demoGridRowBlock.selectedRowId = _id;

    },
})
aTableField: ui.fields.Table<ShowCaseProduct>;

@ui.decorators.gridRowBlock<TableWithDetailPanel>({
    parent() {
        return this.anySection;
    },
    title: 'Demo Grid Row block',
    readOnlyOverride(columnId: string){
        if (columnId === 'category'){
            return true;
        }
    },
    boundTo() {
        return this.aTableField;
    },
    fieldFilter(columnId: string) {
        return columnId !== 'tax';
    }
})
demoGridRowBlock: ui.containers.GridRowBlock;
```

### Display decorator properties:

-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **size**: Vertical size of the field, it can be `small`, `medium` or `large`. It is set to medium by default.
-   **isFullWidth**: Whether a field should take the full width of the screen.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **selectedRecordId**: The record id that is currently displayed, if it is unset then the block becomes hidden.
-   **selectedLevel**: The level of the selected record, only applicable for nested grids
-   **filedFilter(columnId)**: Filter function that is expected to return a boolean value. It allows to filter out fields that should not be displayed on the grid records block.
-   **readOnlyOverride(columnId)**: Override the `readOnly` property of the column. If returns true, the column is forced to be read only, if returns false it will force it to be editable, if undefined (or nothing) is returned, the original `readOnly` property is used from the column definition.
### Binding decorator properties:

-   **boundTo**: Callback function that returns the control object of the table that the field is bound to.

## Runtime functions

-   **selectRowAndLevel()**: Sets the selected record id and level in a single call, practical for nested grid use cases.
