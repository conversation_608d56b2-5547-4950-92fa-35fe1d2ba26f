import type { ScreenBase } from '../../../service/screen-base';
import type { ContainerProperties } from '../../abstract-container';
import type { BlockControlObject, SectionControlObject } from '../../control-objects';
import type { NestedGridControlObject, TableControlObject } from '../../field/field-control-objects';
import type { ExtensionField, HasParent } from '../../field/traits';

export interface GridRowBlockProperties<CT extends ScreenBase = ScreenBase> extends ContainerProperties<CT> {
    selectedRecordId?: string;
    selectedLevel?: number;
}

export interface GridRowBlockDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<GridRowBlockProperties<CT>, '_controlObjectType'>,
        HasParent<CT, SectionControlObject<CT>>,
        ExtensionField<CT, BlockControlObject<CT>> {
    boundTo: (this: CT) => TableControlObject | NestedGridControlObject;
    fieldFilter?: (this: CT, elementId: keyof CT) => boolean;
    readOnlyOverride?: (this: CT, elementId: keyof CT) => boolean | undefined | void;
}
