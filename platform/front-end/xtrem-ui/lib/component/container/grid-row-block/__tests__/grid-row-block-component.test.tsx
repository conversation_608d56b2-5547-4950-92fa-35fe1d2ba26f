import {
    addFieldToState,
    addSectionElementToState,
    applyActionMocks,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';

jest.mock('../../../field/text/async-text-component');
jest.mock('../../../field/numeric/async-numeric-component');
jest.mock('../../../field/reference/async-reference-component');

import * as graphqlService from '../../../../service/graphql-service';
import * as React from 'react';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux/state';
import { CollectionValue } from '../../../../service/collection-data-service';
import * as nestedFields from '../../../nested-fields';
import * as dialogs from '../../../../service/dialog-service';
import type { Container<PERSON>ey } from '../../../types';
import { FieldKey } from '../../../types';
import type { ScreenBase } from '../../../../service/screen-base';
import { render, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { ContextType } from '../../../../types';
import { triggerNestedFieldEvent, triggerFieldEvent } from '../../../../utils/events';
import { setFieldProperties } from '../../../../redux/actions';
import { ConnectedGridRowBlockComponent } from '../grid-row-block-component';
import type { GridRowBlockDecoratorProperties, GridRowBlockProperties } from '../grid-row-block-types';
import { GridRowBlockControlObject } from '../grid-row-block-control-object';
import { CollectionFieldTypes } from '../../../../service/collection-data-types';

describe('grid row block', () => {
    const screenId = 'TestPage';
    const elementId = 'testField';
    const testTableId = 'testTableId';

    let state: XtremAppState;
    let store: MockStoreEnhanced<XtremAppState>;
    let defaultProperties: GridRowBlockDecoratorProperties;
    let columns: nestedFields.NestedField<ScreenBase, nestedFields.GridNestedFieldTypes, any>[];
    let defaultValue: any[];
    let collectionValue: CollectionValue;
    let fetchDefaultsMock: jest.MockInstance<any, any>;
    let confirmationDialogMock: jest.MockInstance<any, any>;
    let column1OnChangeMock: jest.MockInstance<any, any>;
    let column1OnClickMock: jest.MockInstance<any, any>;

    const renderComponent = (isParentDisabled?: boolean) => {
        collectionValue = new CollectionValue<any>({
            screenId,
            elementId,
            isTransient: false,
            hasNextPage: false,
            orderBy: [{}],
            columnDefinitions: [columns],
            nodeTypes: {},
            nodes: ['@sage/xtrem-test/AnyNode'],
            filter: [{}],
            initialValues: defaultValue,
            fieldType: CollectionFieldTypes.GRID_ROW,
            locale: 'en-US',
        });

        addFieldToState(
            FieldKey.Table,
            state,
            screenId,
            testTableId,
            {
                columns,
            },
            collectionValue,
        );

        addSectionElementToState<ContainerKey.GridRowBlock>(
            state,
            screenId,
            GridRowBlockControlObject,
            'block',
            elementId,
            {
                ...defaultProperties,
                boundTo: () => state.screenDefinitions[screenId].metadata.controlObjects[testTableId],
            } as GridRowBlockProperties,
            [],
            true,
        );

        store = getMockStore(state);
        return render(
            <Provider store={store!}>
                <ConnectedGridRowBlockComponent
                    item={{ $containerId: elementId }}
                    availableColumns={12}
                    screenId={screenId}
                    contextType={ContextType.page}
                    isParentDisabled={isParentDisabled}
                />
            </Provider>,
        );
    };

    beforeEach(() => {
        (setFieldProperties as unknown as jest.MockInstance<any, any>).mockReturnValue({
            type: 'AnyAction',
            value: null,
        });
        fetchDefaultsMock = jest
            .spyOn(graphqlService, 'fetchNestedDefaultValues')
            .mockResolvedValue({ nestedDefaults: {} });
        confirmationDialogMock = jest.spyOn(dialogs, 'confirmationDialog').mockResolvedValue({});

        column1OnChangeMock = jest.fn();
        column1OnClickMock = jest.fn();

        columns = [
            nestedFields.text({
                bind: 'field1',
                title: 'Test text field',
                onChange: column1OnChangeMock as any,
                onClick: column1OnClickMock as any,
            }),
            nestedFields.numeric({
                bind: 'field2',
                title: 'Test numeric field',
                scale: 2,
            }),
            nestedFields.text({
                bind: 'field3',
                title: 'Test readOnlyOverride',
                isReadOnly: true,
            }),
        ];
        defaultProperties = {
            title: 'test title',
        } as GridRowBlockDecoratorProperties;
        defaultValue = [
            {
                _id: '1',
                field1: 'Value 1',
                field2: '2.34',
                field3: 'test readOnlyOverride',
                titleBoundField: 'Hi',
            },
            {
                _id: '2',
                field1: 'Value 2',
                field2: '3.45',
                field3: 'test readOnlyOverride',
                titleBoundField: 'Hola',
            },
        ];
        state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
    });

    afterEach(() => {
        applyActionMocks();
        (triggerNestedFieldEvent as unknown as jest.MockInstance<any, any>).mockReset();
        (triggerFieldEvent as unknown as jest.MockInstance<any, any>).mockReset();
        (setFieldProperties as unknown as jest.MockInstance<any, any>).mockReset();
        confirmationDialogMock.mockReset();
        fetchDefaultsMock.mockReset();
    });

    it('should render an empty container if no fields are selected', () => {
        const element = renderComponent();
        expect(element.container.children).toHaveLength(0);
    });

    it('should render a field for each column when a row is selected', () => {
        defaultProperties.selectedRecordId = '1';
        const element = renderComponent();
        expect(element.queryByTestId('e-field-label-testNumericField', { exact: false })).not.toBeNull();
        expect(element.queryByTestId('e-field-label-testTextField', { exact: false })).not.toBeNull();
        expect(element.queryByLabelText('Test numeric field')).not.toHaveAttribute('disabled');
        expect(element.queryByLabelText('Test text field')).not.toHaveAttribute('disabled');
    });

    it('should render the fields disabled if the component is disabled', () => {
        defaultProperties.selectedRecordId = '1';
        defaultProperties.isDisabled = true;
        const element = renderComponent();
        expect(element.queryByLabelText('Test numeric field')).toHaveAttribute('disabled');
        expect(element.queryByLabelText('Test text field')).toHaveAttribute('disabled');
    });

    it('should render the fields disabled if the component is disabled by a callback', () => {
        defaultProperties.selectedRecordId = '1';
        defaultProperties.isDisabled = () => true;
        const element = renderComponent();
        expect(element.queryByLabelText('Test numeric field')).toHaveAttribute('disabled');
        expect(element.queryByLabelText('Test text field')).toHaveAttribute('disabled');
    });

    it('should render the fields enabled if the component has a disabled callback defined and returns false', () => {
        defaultProperties.selectedRecordId = '1';
        defaultProperties.isDisabled = () => false;
        const element = renderComponent();
        expect(element.queryByLabelText('Test numeric field')).not.toHaveAttribute('disabled');
        expect(element.queryByLabelText('Test text field')).not.toHaveAttribute('disabled');
    });

    it('should render the fields disabled if the parent container is disabled', () => {
        defaultProperties.selectedRecordId = '1';
        defaultProperties.isDisabled = false;
        const element = renderComponent(true);
        expect(element.queryByLabelText('Test numeric field')).toHaveAttribute('disabled');
        expect(element.queryByLabelText('Test text field')).toHaveAttribute('disabled');
    });

    it('should render a field for each column when a row is selected with a filter', () => {
        defaultProperties.selectedRecordId = '1';
        defaultProperties.fieldFilter = (elementId: string) => elementId === 'field1';
        const element = renderComponent();
        expect(element.queryByTestId('e-field-label-testNumericField', { exact: false })).toBeNull();
        expect(element.queryByTestId('e-field-label-testTextField', { exact: false })).not.toBeNull();
    });

    it("should bound the collection value's record to the fields", () => {
        defaultProperties.selectedRecordId = '1';
        const element = renderComponent();
        expect((element.queryByLabelText('Test numeric field')! as HTMLInputElement).value).toEqual('2.34');
        expect((element.queryByLabelText('Test text field')! as HTMLInputElement).value).toEqual('Value 1');
    });

    it('should automatically update as the collection value changes', async () => {
        defaultProperties.selectedRecordId = '1';
        const element = renderComponent();
        expect((element.queryByLabelText('Test text field')! as HTMLInputElement).value).toEqual('Value 1');
        collectionValue.setCellValue({ recordId: '1', value: 'New Value', columnId: 'field1' });
        await waitFor(() => {
            expect((element.queryByLabelText('Test text field')! as HTMLInputElement).value).toEqual('New Value');
        });
    });

    it('should update the collection value when the field is updated', () => {
        defaultProperties.selectedRecordId = '1';
        const element = renderComponent();
        const textInput = element.queryByLabelText('Test text field')! as HTMLInputElement;
        expect(collectionValue.getRecordByIdAndLevel({ id: '1' })!.field1).toEqual('Value 1');
        fireEvent.change(textInput, { target: { value: 'new value' } });
        fireEvent.blur(textInput, { target: { value: 'new value' } });
        expect(collectionValue.getRecordByIdAndLevel({ id: '1' })!.field1).toEqual('new value');
    });

    it('should trigger the change event with the right arguments', async () => {
        defaultProperties.selectedRecordId = '1';
        const element = renderComponent();
        const textInput = element.queryByLabelText('Test text field')! as HTMLInputElement;
        expect(triggerNestedFieldEvent).not.toHaveBeenCalled();
        fireEvent.change(textInput, { target: { value: 'new value' } });
        fireEvent.blur(textInput, { target: { value: 'new value' } });
        await waitFor(
            () => (triggerNestedFieldEvent as unknown as jest.MockInstance<any, any>).mock.calls.length === 1,
        );
        expect(triggerNestedFieldEvent).toHaveBeenCalledWith(
            screenId,
            testTableId,
            expect.objectContaining({ onChange: column1OnChangeMock }),
            'onChange',
            '1',
            {
                _id: '1',
                field1: 'new value',
                field2: 2.34,
                field3: 'test readOnlyOverride',
                titleBoundField: 'Hi',
            },
        );
    });

    it('should trigger the click event with the right arguments', async () => {
        defaultProperties.selectedRecordId = '1';
        const element = renderComponent();
        const textInput = element.queryByLabelText('Test text field')! as HTMLInputElement;
        expect(triggerNestedFieldEvent).not.toHaveBeenCalled();
        fireEvent.click(textInput);
        await waitFor(
            () => (triggerNestedFieldEvent as unknown as jest.MockInstance<any, any>).mock.calls.length === 1,
        );
        expect(triggerNestedFieldEvent).toHaveBeenCalledWith(
            screenId,
            testTableId,
            expect.objectContaining({ onClick: column1OnClickMock }),
            'onClick',
            '1',
            {
                _id: '1',
                field1: 'Value 1',
                field2: 2.34,
                field3: 'test readOnlyOverride',
                titleBoundField: 'Hi',
            },
        );
    });

    it('should override a read-only source field to be editable', () => {
        defaultProperties.selectedRecordId = '1';
        defaultProperties.readOnlyOverride = (elementId: string) => {
            if (elementId === 'field3') {
                return false;
            }

            return undefined;
        };
        const element = renderComponent();
        const boundField = element.queryByTestId('e-field-bind-field3', { exact: false });
        expect(boundField!.querySelector('input')).not.toBeNull();
        expect(boundField!.querySelector('span[data-testid="e-field-value"].e-field-read-only')).toBeNull();
    });

    it('should preserve original read only state if read-only value returns undefined', () => {
        defaultProperties.selectedRecordId = '1';
        defaultProperties.readOnlyOverride = (bind: string) => {
            if (bind === 'field1') {
                return true;
            }
            return undefined;
        };
        const element = renderComponent();
        // Read only by default
        const field3 = element.queryByTestId('e-field-bind-field3', { exact: false });
        expect(field3!.querySelector('input')).toHaveAttribute('readonly');
        // Read only property is not set
        const field2 = element.queryByTestId('e-field-bind-field2', { exact: false });
        expect(field2!.querySelector('input')).not.toHaveAttribute('readonly');
        // Read only by override callback
        const field1 = element.queryByTestId('e-field-bind-field1', { exact: false });
        expect(field1!.querySelector('input')).toHaveAttribute('readonly');
    });

    it('should override a editable field source to be read-only', () => {
        const overrideMock = jest.fn((elementId: string) => {
            if (elementId === 'field1') {
                return true;
            }
            return false;
        });
        defaultProperties.selectedRecordId = '1';
        defaultProperties.readOnlyOverride = overrideMock;
        expect(overrideMock).not.toHaveBeenCalled();
        const element = renderComponent();
        const boundField = element.queryByTestId('e-field-bind-field1', { exact: false });
        expect(overrideMock).toHaveBeenCalledTimes(3);
        expect(overrideMock).toHaveBeenCalledWith('field1', null);
        expect(overrideMock).toHaveBeenCalledWith('field2', null);
        expect(overrideMock).toHaveBeenCalledWith('field3', null);
        expect(boundField!.querySelector('input')).toHaveAttribute('readonly');
    });
});
