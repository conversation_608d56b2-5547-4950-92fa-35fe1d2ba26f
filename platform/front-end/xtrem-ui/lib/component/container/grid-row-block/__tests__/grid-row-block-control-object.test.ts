import type { GridRowBlockDecoratorProperties } from '../grid-row-block-types';
import { GridRowBlockControlObject } from '../grid-row-block-control-object';

describe('grid row block control object', () => {
    const screenId = 'TestPage';
    const elementId = 'podCollectionTestElement';
    let gridRowBlockControlObject: GridRowBlockControlObject;
    const setUiComponentProperties: jest.MockInstance<any, any> = jest.fn();

    const properties: GridRowBlockDecoratorProperties = {
        title: 'TEST_FIELD_TITLE',
        boundTo: () => {
            return null;
        },
    } as any;

    beforeEach(() => {
        gridRowBlockControlObject = new GridRowBlockControlObject({
            elementId,
            screenId,
            getUiComponentProperties: jest.fn(() => properties),
            layout: {},
            getValidationState: jest.fn(async () => true),
            setUiComponentProperties: setUiComponentProperties as any,
        });
    });

    afterEach(() => {
        setUiComponentProperties.mockReset();
    });

    it('selectedRecordId should record id prop', () => {
        expect(setUiComponentProperties).not.toHaveBeenCalled();
        gridRowBlockControlObject.selectedRecordId = '32';
        expect(setUiComponentProperties).toHaveBeenCalledWith('TestPage', 'podCollectionTestElement', {
            boundTo: expect.anything(),
            selectedRecordId: '32',
            title: 'TEST_FIELD_TITLE',
        });
    });
    it('selectedLevel should level prop', () => {
        expect(setUiComponentProperties).not.toHaveBeenCalled();
        gridRowBlockControlObject.selectedLevel = 2;
        expect(setUiComponentProperties).toHaveBeenCalledWith('TestPage', 'podCollectionTestElement', {
            boundTo: expect.anything(),
            selectedLevel: 2,
            title: 'TEST_FIELD_TITLE',
        });
    });

    it('selectRowAndLevel should update level and record id props', () => {
        expect(setUiComponentProperties).not.toHaveBeenCalled();
        gridRowBlockControlObject.selectRowAndLevel('1234', 5);
        expect(setUiComponentProperties).toHaveBeenCalledWith('TestPage', 'podCollectionTestElement', {
            boundTo: expect.anything(),
            selectedLevel: 5,
            selectedRecordId: '1234',
            title: 'TEST_FIELD_TITLE',
        });
    });
});
