import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata } from '../../../../__tests__/test-helpers';
import type { GridRowBlockDecoratorProperties } from '../grid-row-block-decorator';
import { gridRowBlock } from '../grid-row-block-decorator';

describe('Grid Row Block Decorator', () => {
    const containerId = 'gridRowBlock';
    let pageMetadata: pageMetaData.PageMetadata;

    describe('mapping values', () => {
        let mappedComponentProperties: GridRowBlockDecoratorProperties<ScreenBase>;
        beforeEach(() => {
            pageMetadata = getMockPageMetadata();
            jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
            gridRowBlock({
                boundTo: () => {
                    return {} as any;
                },
            })({} as Page, containerId);
            pageMetadata.blockThunks[containerId]({}, {});
            mappedComponentProperties = pageMetadata.uiComponentProperties[
                containerId
            ] as GridRowBlockDecoratorProperties<ScreenBase>;
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('Grid Row Block Decorator -> should set default values when no component properties provided', () => {
            expect(mappedComponentProperties.isHidden).toBe(false);
            expect(mappedComponentProperties.isHiddenDesktop).toBe(false);
            expect(mappedComponentProperties.isHiddenMobile).toBe(false);
            expect(mappedComponentProperties.isTransient).toBe(false);
            expect(mappedComponentProperties.title).toBeUndefined();
            expect(mappedComponentProperties.selectedLevel).toBeUndefined();
            expect(mappedComponentProperties.selectedRecordId).toBeUndefined();
        });
    });
});
