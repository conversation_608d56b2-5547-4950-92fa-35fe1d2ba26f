import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { GridRowBlockComponentExternalProps } from './grid-row-block-component';

const ConnectedGridRowBlockComponent = React.lazy(() => import('./grid-row-block-component'));

export function AsyncGridRowBlockComponent(props: GridRowBlockComponentExternalProps): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <ConnectedGridRowBlockComponent {...props} />
        </React.Suspense>
    );
}
