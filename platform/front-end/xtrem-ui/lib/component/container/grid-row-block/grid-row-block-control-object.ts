/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { getUiComponentProperties, setUiComponentProperties } from '../../../service/transactions-service';
import type { ScreenExtension } from '../../../types';
import { AbstractContainer } from '../../abstract-container';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import type { ContainerComponentProps, ContainerControlObjectConstructorProps, ParentType } from '../../types';
import { ContainerKey } from '../../types';
import type { GridRowBlockProperties } from './grid-row-block-types';

export interface IGridRowBlockControlObject extends ContainerControlObjectConstructorProps<ContainerKey.GridRowBlock> {
    parent?: ParentType<ContainerKey.GridRowBlock>;
}

/**
 * [Container]{@link AbstractContainer} that holds any number of [fields]{@link AbstractField}
 */
export class GridRowBlockControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends AbstractContainer<
    CT,
    ContainerKey.GridRowBlock,
    ContainerComponentProps<ContainerKey.GridRowBlock>
> {
    public insertBefore?: ContainerControlObjectConstructorProps<ContainerKey.GridRowBlock>['insertBefore'];

    static readonly defaultUiProperties: Partial<GridRowBlockProperties> = {
        ...AbstractContainer.defaultUiProperties,
    };

    theParentOfThisFieldNeedsToBeABlockContainer: boolean;

    constructor(properties: IGridRowBlockControlObject) {
        super(
            properties.screenId,
            properties.elementId,
            properties.getUiComponentProperties || getUiComponentProperties,
            properties.setUiComponentProperties || setUiComponentProperties,
            ContainerKey.GridRowBlock,
            properties.getValidationState || (async (): Promise<boolean> => true),
            properties.layout,
            properties.parent,
        );
        this.insertBefore = properties.insertBefore;
    }

    @FieldControlObjectResolvedProperty<GridRowBlockProperties<CT>, GridRowBlockControlObject<CT>>()
    selectedLevel?: number;

    @FieldControlObjectResolvedProperty<GridRowBlockProperties<CT>, GridRowBlockControlObject<CT>>()
    selectedRecordId?: string;

    selectRowAndLevel(selectedRecordId: string, selectedLevel?: number): void {
        const fieldProperties = { ...this.uiComponentProperties, selectedRecordId, selectedLevel };
        this._setUiComponentProperties(this.screenId, this.elementId, fieldProperties);
    }

    /**
     * Triggers the field validation rules. Since the validation rules might be asynchronous,
     * this method returns a promise that must be awaited to get the validation result. Compared to the `validate` method
     * it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
     */
    async validateWithDetails(
        partition: true,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
    async validateWithDetails(partition: false): Promise<ValidationResult[]>;
    async validateWithDetails(): Promise<ValidationResult[]>;
    async validateWithDetails(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _partition = false,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] } | ValidationResult[]> {
        // Never used, but required by the AbstractContainer
        return [];
    }
}
