/**
 * @packageDocumentation
 * @module root
 * */

import type { Dict } from '@sage/xtrem-shared';
import type { PageArticleItem } from '../../../service/layout-types';
import type { DataTypeDetails, FormattedNodeDetails } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import { getPageMetadata } from '../../../service/page-metadata';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import { getTargetPrototype, standardExtensionDecoratorImplementation } from '../../../utils/decorator-utils';
import { AbstractDecorator } from '../../abstract-decorator';
import { ContainerKey } from '../../types';
import { GridRowBlockControlObject } from './grid-row-block-control-object';
import { GridRowBlockLayout } from './grid-row-block-layout';
import { GridRowBlockDecoratorProperties } from './grid-row-block-types';

export { GridRowBlockDecoratorProperties };

class GridRowBlockDecorator extends AbstractDecorator<ContainerKey.GridRowBlock> {
    protected _layout = GridRowBlockLayout;

    protected _controlObjectConstructor = GridRowBlockControlObject;
}

/**
 * Initializes the decorated member as a [Block]{@link GridRowBlockControlObject} container with the provided properties
 *
 * @param properties The properties that the [Block]{@link GridRowBlockControlObject} container will be initialized with
 */
export function gridRowBlock<CT extends ScreenExtension<CT>>(properties: GridRowBlockDecoratorProperties<Extend<CT>>) {
    return function gridRowBlockDecorator(target: CT, name: string): void {
        const pageMetadata = getPageMetadata(getTargetPrototype(target.constructor), target);
        pageMetadata.blockThunks[name] = (
            nodeTypes: Dict<FormattedNodeDetails>,
            dataTypes: Dict<DataTypeDetails>,
        ): Partial<PageArticleItem> =>
            new GridRowBlockDecorator(
                pageMetadata.target as any,
                name,
                { pageMetadata, properties },
                ContainerKey.GridRowBlock,
                nodeTypes,
                dataTypes,
                {},
            ).build().layout;
    };
}

export function gridRowBlockOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<GridRowBlockDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, ContainerKey.GridRowBlock>(properties);
}
