import { AbstractLayoutBuilder } from '../../abstract-layout-builder';
import type { ContainerKey } from '../../types';

export class GridRowBlockLayout extends AbstractLayoutBuilder<ContainerKey.GridRowBlock> {
    buildLayout = (): this => {
        this.layout = this.buildContainerLayout(
            this.elementId,
            'grid-row-block',
            this.metadata.properties.isHiddenMobile,
            this.metadata.properties.isHiddenDesktop,
        );
        this.metadata.pageMetadata.layoutBlocks[this.elementId] = this.layout;
        return this;
    };
}
