PATH: XTREEM/Client+Framework/Block

A container element that holds a number of fields. Its parent must be a section. It can hold any kind of fields.

## Example

```ts
@ui.decorators.block<SamplePage>({
    title: 'Sample Block',
    parent() {
        return this.anySection;
    },
    isTitleHidden: true,
    isDisabled: false,
    width: 'medium',
})
sampleBlock: ui.containers.Block;
```

### Decorator properties:

-   **title**: The title that is displayed on the top of the page. The title can be provided as a string, or a callback function returning a string. It is automatically picked up by the i18n engine and externalized for translation.
-   **isTitleHidden**: Subtitle that is displayed on the top of the normal title pages. When the page is rendered into a dialog, the subtitle is displayed under the the title.
-   **isDisabled**: Determines whether the fields in the block are disabled or not. If set to true, all children field will be disabled.
-   **width**: Specifies the field's width. The width can be defined by using field size categories which are remapped to actually width values by the framework depending on the screen size.
