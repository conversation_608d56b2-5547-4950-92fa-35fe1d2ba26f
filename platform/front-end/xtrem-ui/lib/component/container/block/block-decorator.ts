/**
 * @packageDocumentation
 * @module root
 * */

import type { Dict } from '@sage/xtrem-shared';
import { dispatchContainerValidation } from '../../../service/dispatch-service';
import type { PageArticleItem } from '../../../service/layout-types';
import type { DataTypeDetails, FormattedNodeDetails } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import { getPageMetadata } from '../../../service/page-metadata';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { getValidationState } from '../../../service/validation-service';
import type { ScreenExtension } from '../../../types';
import { getTargetPrototype } from '../../../utils/decorator-utils';
import { AbstractDecorator } from '../../abstract-decorator';
import type { BlockProperties, SectionControlObject } from '../../control-objects';
import { BlockControlObject } from '../../control-objects';
import type { ExtensionField, HasParent } from '../../field/traits';
import { ContainerKey } from '../../types';
import { BlockLayout } from '../layouts';
import { getDeclarationPackage } from '../../../service/screen-loader-service';

export interface BlockDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<BlockProperties<CT>, '_controlObjectType'>,
        HasParent<CT, SectionControlObject<CT>>,
        ExtensionField<CT, BlockControlObject<CT>> {}

class BlockDecorator extends AbstractDecorator<ContainerKey.Block> {
    protected _layout = BlockLayout;

    protected _controlObjectConstructor = BlockControlObject;
}

/**
 * Initializes the decorated member as a [Block]{@link BlockControlObject} container with the provided properties
 *
 * @param properties The properties that the [Block]{@link BlockControlObject} container will be initialized with
 */
export function block<CT extends ScreenExtension<CT>>(
    properties: BlockDecoratorProperties<Extend<CT>>,
): (target: CT, name: string) => void {
    return function blockDecorator(target: CT, name: string): void {
        const pageMetadata = getPageMetadata(getTargetPrototype(target.constructor), target);
        const extensionPackageName = getDeclarationPackage();
        pageMetadata.definitionOrder.push(name);
        pageMetadata.blockThunks[name] = (
            nodeTypes: Dict<FormattedNodeDetails>,
            dataTypes: Dict<DataTypeDetails>,
        ): Partial<PageArticleItem> =>
            new BlockDecorator(
                pageMetadata.target as any,
                name,
                { pageMetadata, properties, extensionPackageName },
                ContainerKey.Block,
                nodeTypes,
                dataTypes,
                {
                    getValidationState: async (): Promise<boolean> => getValidationState(pageMetadata.screenId, name),
                    dispatchBlockValidation: (): Promise<{
                        allErrors: ValidationResult[];
                        blockingErrors: ValidationResult[];
                    }> => {
                        if (target.constructor.prototype.___isPageExtension) {
                            return dispatchContainerValidation((target.constructor as any).basePage, name);
                        }

                        return dispatchContainerValidation(target.constructor.name, name);
                    },
                },
            ).build().layout;
    };
}
