import { AbstractLayoutBuilder } from '../../abstract-layout-builder';
import type { ContainerKey } from '../../types';

export class BlockLayout extends AbstractLayoutBuilder<ContainerKey.Block> {
    buildLayout = (): this => {
        this.layout = this.buildContainerLayout(
            this.elementId,
            'block',
            this.metadata.properties.isHiddenMobile,
            this.metadata.properties.isHiddenDesktop,
            this.metadata.properties.width,
        );
        this.metadata.pageMetadata.layoutBlocks[this.elementId] = this.layout;
        return this;
    };
}
