import type { AccessStatus } from '@sage/xtrem-shared';
import { Accordion } from 'carbon-react/esm/components/accordion';
import {
    StyledAccordionContainer,
    StyledAccordionContent,
    StyledAccordionContentContainer,
} from 'carbon-react/esm/components/accordion/accordion.style';
import * as React from 'react';
import { connect } from 'react-redux';
import styled from 'styled-components';
import type * as xtremRedux from '../../../redux';
import type { ReduxResponsive } from '../../../redux/state';
import { RenderingRouter } from '../../../render/rendering-router';
import type { PageArticleItem } from '../../../service/layout-types';
import { ContextType } from '../../../types';
import { getElementAccessStatus } from '../../../utils/access-utils';
import { getDataTestIdAttribute, isHidden } from '../../../utils/dom';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { calculateContainerWidth, getGutterSize } from '../../../utils/responsive-utils';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import type { BlockProperties, PageProperties } from '../../control-objects';
import { getFieldTitle } from '../../field/carbon-helpers';
import { GridColumn, GridRow } from '@sage/xtrem-ui-components';

const StyledAccordion = styled(Accordion)`
    ${StyledAccordionContainer} {
        overflow: unset;
    }
    ${StyledAccordionContent} {
        overflow: unset;
    }
    ${StyledAccordionContentContainer} {
        overflow: unset;
    }
`;

export interface BlockComponentExternalProps {
    item: Partial<PageArticleItem>;
    screenId: string;
    contextType?: ContextType;
    availableColumns: number;
    isParentDisabled?: boolean;
    isParentHidden?: boolean;
    accessRule?: AccessStatus;
    fixedHeight?: number;
    isUsingInfiniteScroll?: boolean;
}

export interface BlockComponentProps extends BlockComponentExternalProps {
    screenType: string;
    fieldProperties: Omit<BlockProperties, 'parent'>;
    browser: ReduxResponsive;
}

const renderBlockBody = (props: BlockComponentProps, computedWidth: number, isBlockHidden: boolean): JSX.Element => {
    const gridGutter = props.contextType === ContextType.dialog ? 16 : getGutterSize(props.browser.is);
    const gridVerticalMargin = props.contextType === ContextType.page ? 16 : 0;

    const isDisabled = (): boolean =>
        props.accessRule === 'unauthorized' ||
        resolveByValue({
            screenId: props.screenId,
            fieldValue: null,
            rowValue: null,
            propertyValue: props.fieldProperties.isDisabled,
            skipHexFormat: true,
        });

    return (
        <div className="e-block-body">
            <GridRow
                columns={computedWidth}
                gutter={gridGutter}
                margin={props.contextType === ContextType.sidebar ? 0 : 16}
                verticalMargin={gridVerticalMargin}
            >
                {props.item.$layout?.$items.map(item => (
                    <RenderingRouter
                        key={item.$containerId || item.$bind}
                        item={item}
                        screenId={props.screenId}
                        availableColumns={computedWidth}
                        contextType={props.contextType}
                        isParentDisabled={props.isParentDisabled || isDisabled()}
                        isParentHidden={props.isParentHidden || isBlockHidden}
                        fixedHeight={props.fixedHeight}
                    />
                ))}
            </GridRow>
        </div>
    );
};

export function BlockComponent(props: BlockComponentProps): React.ReactElement | null {
    const [isExpanded, setExpanded] = React.useState(false);

    const computedWidth = calculateContainerWidth(
        props.browser.is,
        props.availableColumns,
        props.fieldProperties.width,
    );

    const isTitleHidden = resolveByValue({
        screenId: props.screenId,
        fieldValue: null,
        rowValue: null,
        propertyValue: props.fieldProperties.isTitleHidden,
        skipHexFormat: true,
    });

    const isBlockHidden =
        isHidden(props.item, props.browser) ||
        props.accessRule === 'unavailable' ||
        resolveByValue({
            screenId: props.screenId,
            propertyValue: props.fieldProperties.isHidden,
            skipHexFormat: true,
            rowValue: null,
        });

    const title = getFieldTitle(props.screenId, props.fieldProperties, null);

    if (props.contextType === ContextType.accordion) {
        if (isBlockHidden) {
            return null;
        }
        return (
            <StyledAccordion
                expanded={isExpanded}
                onChange={(): void => setExpanded(!isExpanded)}
                iconAlign="left"
                title={title}
                size="small"
                borders="default"
                disableContentPadding={true}
                error=""
                width="100%"
                warning=""
                info=""
            >
                {isExpanded && (
                    <div className="e-block e-accordion-block">
                        {renderBlockBody(props, computedWidth, isBlockHidden)}
                    </div>
                )}
            </StyledAccordion>
        );
    }

    const classes = ['e-container-parent', 'e-block-parent'];
    if (isBlockHidden) {
        classes.push('e-hidden');
    }

    if (props.contextType) {
        classes.push(`e-block-context-${props.contextType}`);
    }

    /* eslint-disable react/no-array-index-key */
    return (
        <GridColumn className={classes.join(' ')} columnSpan={computedWidth}>
            <div className="e-block" data-testid={getDataTestIdAttribute('block', title, props.item.$containerId)}>
                {!isTitleHidden && title && (
                    <div className="e-block-header">
                        <h3 className="e-block-title" data-testid="e-block-title">
                            {title}
                        </h3>
                    </div>
                )}
                {renderBlockBody(props, computedWidth, isBlockHidden)}
            </div>
        </GridColumn>
    );
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: BlockComponentExternalProps): BlockComponentProps => {
    const elementId = props.item.$containerId!;
    const pageDefinition = getPageDefinitionFromState(props.screenId);
    const pageProperties: PageProperties = pageDefinition.metadata.uiComponentProperties[props.screenId];
    const fieldProperties = pageDefinition.metadata.uiComponentProperties[elementId];
    const accessRule = getElementAccessStatus({
        accessBindings: pageDefinition.accessBindings || {},
        bind: elementId,
        elementProperties: fieldProperties,
        contextNode: pageProperties?.node,
        nodeTypes: state.nodeTypes,
        dataTypes: state.dataTypes,
    });

    return {
        ...props,
        accessRule,
        fieldProperties,
        screenType: pageDefinition.type,
        browser: state.browser,
    };
};

export const ConnectedBlockComponent = connect(mapStateToProps)(BlockComponent);
