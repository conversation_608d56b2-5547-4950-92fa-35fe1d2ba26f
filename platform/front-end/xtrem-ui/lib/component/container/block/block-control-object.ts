/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { getUiComponentProperties, setUiComponentProperties } from '../../../service/transactions-service';
import type { ScreenExtension } from '../../../types';
import type { ContainerProperties } from '../../abstract-container';
import { AbstractContainer } from '../../abstract-container';
import type {
    ContainerComponentProps,
    ContainerControlObjectConstructorProps,
    ContainerWidth,
    ParentType,
} from '../../types';
import { ContainerKey } from '../../types';

export interface BlockProperties<CT extends ScreenBase = ScreenBase> extends ContainerProperties<CT> {
    /** The width of the block relative to the section where it is place.
     * Must be a number between 1 and 12, both included*/
    width?: ContainerWidth;
}

export interface IBlockControlObject extends ContainerControlObjectConstructorProps<ContainerKey.Block> {
    parent?: ParentType<ContainerKey.Block>;
    dispatchBlockValidation: () => Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
}

/**
 * [Container]{@link AbstractContainer} that holds any number of [fields]{@link AbstractField}
 */
export class BlockControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends AbstractContainer<
    CT,
    ContainerKey.Block,
    ContainerComponentProps<ContainerKey.Block>
> {
    public insertBefore?: ContainerControlObjectConstructorProps<ContainerKey.Block>['insertBefore'];

    public insertAfter?: ContainerControlObjectConstructorProps<ContainerKey.Block>['insertAfter'];

    private readonly _dispatchBlockValidation: IBlockControlObject['dispatchBlockValidation'];

    static readonly defaultUiProperties: Partial<BlockProperties> = {
        ...AbstractContainer.defaultUiProperties,
    };

    theParentOfThisFieldNeedsToBeABlockContainer: boolean;

    constructor(properties: IBlockControlObject) {
        super(
            properties.screenId,
            properties.elementId,
            properties.getUiComponentProperties || getUiComponentProperties,
            properties.setUiComponentProperties || setUiComponentProperties,
            ContainerKey.Block,
            properties.getValidationState || (async (): Promise<boolean> => true),
            properties.layout,
            properties.parent,
        );
        this.insertBefore = properties.insertBefore;
        this.insertAfter = properties.insertAfter;
        this._dispatchBlockValidation = properties.dispatchBlockValidation;
    }

    /**
     * Triggers the validation rules of all the fields of the block. Since the validation rules
     * might be asynchronous, this method returns a promise that must be awaited to get
     * the validation result
     */
    async validate(): Promise<string[]> {
        const result = await this.validateWithDetails();
        return result.map(r => r!.message);
    }

    /**
     * Triggers the field validation rules. Since the validation rules might be asynchronous,
     * this method returns a promise that must be awaited to get the validation result. Compared to the `validate` method
     * it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
     */
    async validateWithDetails(
        partition: true,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
    async validateWithDetails(partition: false): Promise<ValidationResult[]>;
    async validateWithDetails(): Promise<ValidationResult[]>;
    async validateWithDetails(
        partition = false,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] } | ValidationResult[]> {
        const errors = await this._dispatchBlockValidation();
        return partition ? errors : errors.allErrors;
    }
}
