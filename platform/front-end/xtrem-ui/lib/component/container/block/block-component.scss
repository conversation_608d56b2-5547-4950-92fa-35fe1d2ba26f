@import '../../../render/style/variables.scss';
@import '../../../render/style/mixins.scss';

.e-block-parent {

    .e-block {
        @include e-block-container-style;

        @media print {
            page-break-inside: avoid;
        }

        .e-block-header {
            @include e-block-title-container-style;

            .e-block-title {
                @include e-block-title-style;
            }
        }
    }

    &.e-block-context-detail-panel .e-block,
    &.e-block-context-dialog .e-block {
        background: transparent;
    }

    &.e-block-context-header .e-block {
        .e-block-body {
            @include small_and_below {
                display: flex;
            }

            .e-field-read-only {
                border: none;
            }

            .e-field {
                border: none;

                .common-input__label {
                    @include e-field-label-hidden;

                    .common-input__help-text {
                        display: none;
                    }
                }
            }
        }
    }

    &.e-block-context-page .e-block {
        background: var(--colorsUtilityMajor010);

        @include extra_small {
            background: var(--colorsUtilityMajor025);
        }
    }

    &.e-block-context-header .e-block-body {
        margin: 0;
    }
}

.e-block.e-accordion-block {
    padding: 16px 0;
}