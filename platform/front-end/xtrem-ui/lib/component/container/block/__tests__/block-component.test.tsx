import {
    addBlockToState,
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';

jest.mock('../../../field/text/async-text-component');

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { FieldKey, GraphQLTypes } from '../../../types';
import type { XtremAppState } from '../../../../redux/state';
import type { PageArticleItem } from '../../../../service/layout-types';
import type { BlockProperties } from '../../../control-objects';
import { ConnectedBlockComponent } from '../block-component';
import type { PageDefinition } from '../../../../service/page-definition';
import { render, cleanup } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ContextType, GraphQLKind } from '../../../../types';

describe('BlockComponent', () => {
    const screenId = 'TestPage';
    const elementId = 'test-block';
    let store: MockStoreEnhanced<XtremAppState>;
    let item: Partial<PageArticleItem>;
    let state: XtremAppState;
    let pageDefinition: PageDefinition;
    let blockProperties: BlockProperties = {};

    beforeEach(() => {
        blockProperties = {};
        blockProperties.title = 'Test Block Title';
        blockProperties.isHidden = false;

        state = getMockState();
        state.nodeTypes = {
            MyTestNode: {
                name: 'MyTestNode',
                title: 'MyTestNode',
                packageName: '@sage/xtrem-test',
                properties: {
                    [elementId]: {
                        type: GraphQLTypes.String,
                        kind: GraphQLKind.Scalar,
                        isOnOutputType: true,
                        isOnInputType: true,
                        canFilter: true,
                        name: elementId,
                    },
                },
                mutations: {},
            },
            MyOtherNode: {
                name: 'MyOtherNode',
                title: 'MyOtherNode',
                packageName: '@sage/xtrem-test',
                properties: {
                    someRandomBind: {
                        type: GraphQLTypes.String,
                        kind: GraphQLKind.Scalar,
                        isOnOutputType: true,
                        isOnInputType: true,
                        canFilter: true,
                        name: 'someRandomBind',
                    },
                },
                mutations: {},
            },
        };
        pageDefinition = getMockPageDefinition(screenId);
        pageDefinition.metadata.uiComponentProperties[screenId] = { node: '@sage/xtrem-test/MyTestNode' } as any;
        state.screenDefinitions[screenId] = pageDefinition;
        addFieldToState(FieldKey.Text, state, screenId, 'field1', {});
        item = addBlockToState(state, screenId, elementId, blockProperties, [{ $bind: 'field1' }]);
        store = getMockStore(state);
    });

    afterEach(() => {
        jest.restoreAllMocks();
        cleanup();
    });

    const renderAndMatchSnapshot = (contextType: ContextType = ContextType.page) => {
        const { container } = render(
            <Provider store={store}>
                <ConnectedBlockComponent
                    screenId={screenId}
                    item={item}
                    contextType={contextType}
                    availableColumns={8}
                />
            </Provider>,
        );
        expect(container).toMatchSnapshot();
    };

    describe('Snapshots', () => {
        it('should render with default properties', () => {
            renderAndMatchSnapshot();
        });

        it('should render with a title', () => {
            state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId].title = 'TEST TITLE';
            const { queryByTestId } = render(
                <Provider store={getMockStore(state)}>
                    <ConnectedBlockComponent screenId={screenId} item={item} availableColumns={8} />
                </Provider>,
            );

            expect(queryByTestId('e-block-title')).toHaveTextContent('TEST TITLE');
        });

        it('should render with a callback title', () => {
            state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId].title = () =>
                'TEST TITLE callback';
            const { queryByTestId } = render(
                <Provider store={getMockStore(state)}>
                    <ConnectedBlockComponent screenId={screenId} item={item} availableColumns={8} />
                </Provider>,
            );

            expect(queryByTestId('e-block-title')).toHaveTextContent('TEST TITLE callback');
        });

        it('should render without a title', () => {
            state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId].title = undefined;
            const { queryByTestId } = render(
                <Provider store={getMockStore(state)}>
                    <ConnectedBlockComponent screenId={screenId} item={item} availableColumns={8} />
                </Provider>,
            );
            expect(queryByTestId('e-block-title')).toBeNull();
        });

        it('should render without a title if it is set to hidden', () => {
            state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId].title = 'SOME TITLE';
            state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId].isTitleHidden = true;
            const { queryByTestId } = render(
                <Provider store={getMockStore(state)}>
                    <ConnectedBlockComponent screenId={screenId} item={item} availableColumns={8} />
                </Provider>,
            );

            expect(queryByTestId('e-block-title')).toBeNull();
        });

        it('Should render the block with isHidden class due fieldProperty has isHidden', () => {
            state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId].isHidden = true;
            const { container } = render(
                <Provider store={getMockStore(state)}>
                    <ConnectedBlockComponent screenId={screenId} item={item} availableColumns={8} />
                </Provider>,
            );

            expect(container.querySelector('.e-container-parent.e-hidden')).not.toBeNull();
        });

        it('Should not render the block if it is hidden and rendered within an accordion', () => {
            state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId].isHidden = true;
            const { container } = render(
                <Provider store={getMockStore(state)}>
                    <ConnectedBlockComponent
                        screenId={screenId}
                        item={item}
                        availableColumns={8}
                        contextType={ContextType.accordion}
                    />
                </Provider>,
            );

            expect(container.querySelector('.e-container-parent')).toBeNull();
        });

        it('Should render the block with isHidden class due fieldProperty has isHidden callback returning true', () => {
            state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId].isHidden = () => true;
            const { container } = render(
                <Provider store={getMockStore(state)}>
                    <ConnectedBlockComponent screenId={screenId} item={item} availableColumns={8} />
                </Provider>,
            );

            expect(container.querySelector('.e-container-parent.e-hidden')).not.toBeNull();
        });

        it('Should render the block without isHidden class due fieldProperty has isHidden callback returning false', () => {
            state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId].isHidden = () => false;
            const { container } = render(
                <Provider store={getMockStore(state)}>
                    <ConnectedBlockComponent screenId={screenId} item={item} availableColumns={8} />
                </Provider>,
            );

            expect(container.querySelector('.e-container-parent.e-hidden')).toBeNull();
        });

        it('Should render the block with isHidden class due isHiddenDesktop is true and browser responsive is Desktop', () => {
            item.$isHiddenDesktop = true;
            renderAndMatchSnapshot();
        });

        it('Should render in other context than page', () => {
            renderAndMatchSnapshot(ContextType.detailPanel);
        });

        it('Should render block in mobile', () => {
            state.browser.greaterThan.s = false;
            renderAndMatchSnapshot();
        });

        describe('access rights', () => {
            it('should not children fields if the block is does not have any associated access rules', () => {
                const wrapper = render(
                    <Provider store={getMockStore(state)}>
                        <ConnectedBlockComponent screenId={screenId} item={item} availableColumns={8} />
                    </Provider>,
                );
                expect(wrapper.queryByTestId('e-text-field-input')).not.toHaveAttribute('disabled');
            });

            it('should not disable children fields if the block is authorized', () => {
                pageDefinition.accessBindings = {
                    MyTestNode: {
                        [elementId]: 'authorized',
                    },
                };
                const wrapper = render(
                    <Provider store={getMockStore(state)}>
                        <ConnectedBlockComponent screenId={screenId} item={item} availableColumns={8} />
                    </Provider>,
                );
                expect(wrapper.queryByTestId('e-text-field-input')).not.toHaveAttribute('disabled');
            });

            it('should disable children fields if the block is unauthorized', () => {
                pageDefinition.accessBindings = {
                    MyTestNode: {
                        [elementId]: 'unauthorized',
                    },
                };
                store = getMockStore(state);

                const wrapper = render(
                    <Provider store={store}>
                        <ConnectedBlockComponent screenId={screenId} item={item} availableColumns={8} />
                    </Provider>,
                );
                expect(wrapper.queryByTestId('e-text-field-input')).toHaveAttribute('disabled');
            });

            it('should disable children fields if the block is an unauthorized access binding', () => {
                pageDefinition.accessBindings = {
                    MyTestNode: {
                        [elementId]: 'authorized',
                    },
                    MyOtherNode: {
                        someRandomBind: 'unauthorized',
                    },
                };
                store = getMockStore(state);
                pageDefinition.metadata.uiComponentProperties[elementId].access = {
                    node: '@sage/xtrem-test/MyOtherNode',
                    bind: 'unauthorized',
                };

                const wrapper = render(
                    <Provider store={store}>
                        <ConnectedBlockComponent screenId={screenId} item={item} availableColumns={8} />
                    </Provider>,
                );
                expect(wrapper.queryByTestId('e-text-field-input')).not.toHaveAttribute('disabled');
            });

            it('shouldnot be hidden if it is authorized', () => {
                pageDefinition.accessBindings = {
                    MyTestNode: {
                        [elementId]: 'authorized',
                    },
                };

                const wrapper = render(
                    <Provider store={getMockStore(state)}>
                        <ConnectedBlockComponent screenId={screenId} item={item} availableColumns={8} />
                    </Provider>,
                );
                expect(wrapper.container.querySelector('.e-block-parent')!).not.toHaveClass('e-hidden');
            });

            it('should be hidden if it is unavailable', () => {
                pageDefinition.accessBindings = {
                    MyTestNode: {
                        [elementId]: 'unavailable',
                    },
                };

                const wrapper = render(
                    <Provider store={getMockStore(state)}>
                        <ConnectedBlockComponent screenId={screenId} item={item} availableColumns={8} />
                    </Provider>,
                );
                expect(wrapper.container.querySelector('.e-block-parent')!).toHaveClass('e-hidden');
            });

            it('should be hidden if the block is an unavailable access binding', () => {
                pageDefinition.accessBindings = {
                    MyTestNode: {
                        [elementId]: 'authorized',
                    },
                    MyOtherNode: {
                        someRandomBind: 'unavailable',
                    },
                };
                pageDefinition.metadata.uiComponentProperties[elementId].access = {
                    node: '@sage/xtrem-test/MyOtherNode',
                    bind: 'someRandomBind',
                };

                const wrapper = render(
                    <Provider store={getMockStore(state)}>
                        <ConnectedBlockComponent screenId={screenId} item={item} availableColumns={8} />
                    </Provider>,
                );
                expect(wrapper.container.querySelector('.e-block-parent')!).toHaveClass('e-hidden');
            });
        });
    });
});
