// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BlockComponent Snapshots Should render block in mobile 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c6 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c6:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c6::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::placeholder {
  color: var(--colorsUtilityYin055);
}

.c3 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c4 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c4 .c5 {
  padding: 0 var(--spacing150);
}

.c4 input::-ms-clear {
  display: none;
}

.c4 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-grid-column e-grid-column-8 e-container-parent e-block-parent e-block-context-page"
    style="grid-column: span 8;"
  >
    <div
      class="e-block"
      data-testid="e-block-field e-field-label-testBlockTitle e-field-bind-test-block"
    >
      <div
        class="e-block-header"
      >
        <h3
          class="e-block-title"
          data-testid="e-block-title"
        >
          Test Block Title
        </h3>
      </div>
      <div
        class="e-block-body"
      >
        <div
          class="e-grid-row e-grid-row-8 "
          style="grid-template-columns: repeat(8, 1fr); padding: 16px 16px 16px 16px;"
        >
          <div
            class="e-grid-column e-grid-column-2 e-field-grid-column"
            style="grid-column: span 2;"
          >
            <div
              class="e-field e-text-field e-context-page"
              data-testid="e-text-field e-field-bind-field1"
            >
              <div
                class="c0 c1"
              >
                <div
                  class="c2"
                  data-role="field-line"
                >
                  <div
                    class="c3"
                    data-role="input-presentation-container"
                  >
                    <div
                      class="c4"
                      role="presentation"
                    >
                      <input
                        aria-invalid="false"
                        autocomplete="off"
                        class="c5 c6"
                        data-element="input"
                        data-testid="e-text-field-input"
                        id="TestPage-field1-page"
                        name="field1"
                        type="text"
                        value="Test value"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`BlockComponent Snapshots Should render in other context than page 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c6 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c6:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c6::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::placeholder {
  color: var(--colorsUtilityYin055);
}

.c3 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c4 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c4 .c5 {
  padding: 0 var(--spacing150);
}

.c4 input::-ms-clear {
  display: none;
}

.c4 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-grid-column e-grid-column-8 e-container-parent e-block-parent e-block-context-detail-panel"
    style="grid-column: span 8;"
  >
    <div
      class="e-block"
      data-testid="e-block-field e-field-label-testBlockTitle e-field-bind-test-block"
    >
      <div
        class="e-block-header"
      >
        <h3
          class="e-block-title"
          data-testid="e-block-title"
        >
          Test Block Title
        </h3>
      </div>
      <div
        class="e-block-body"
      >
        <div
          class="e-grid-row e-grid-row-8 "
          style="grid-template-columns: repeat(8, 1fr); padding: 0px 16px 0px 16px;"
        >
          <div
            class="e-grid-column e-grid-column-2 e-field-grid-column"
            style="grid-column: span 2;"
          >
            <div
              class="e-field e-text-field e-context-detail-panel"
              data-testid="e-text-field e-field-bind-field1"
            >
              <div
                class="c0 c1"
              >
                <div
                  class="c2"
                  data-role="field-line"
                >
                  <div
                    class="c3"
                    data-role="input-presentation-container"
                  >
                    <div
                      class="c4"
                      role="presentation"
                    >
                      <input
                        aria-invalid="false"
                        autocomplete="off"
                        class="c5 c6"
                        data-element="input"
                        data-testid="e-text-field-input"
                        id="TestPage-field1-detail-panel"
                        name="field1"
                        type="text"
                        value="Test value"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`BlockComponent Snapshots Should render the block with isHidden class due isHiddenDesktop is true and browser responsive is Desktop 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c6 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
  color: var(--colorsUtilityYin030);
  cursor: not-allowed;
}

.c6:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c6::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::placeholder {
  color: var(--colorsUtilityYin055);
}

.c3 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c4 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background: var(--colorsUtilityDisabled400);
  border-color: var(--colorsUtilityDisabled600);
  cursor: not-allowed;
}

.c4 .c5 {
  padding: 0 var(--spacing150);
}

.c4 input::-ms-clear {
  display: none;
}

.c4 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-grid-column e-grid-column-8 e-container-parent e-block-parent e-hidden e-block-context-page"
    style="grid-column: span 8;"
  >
    <div
      class="e-block"
      data-testid="e-block-field e-field-label-testBlockTitle e-field-bind-test-block"
    >
      <div
        class="e-block-header"
      >
        <h3
          class="e-block-title"
          data-testid="e-block-title"
        >
          Test Block Title
        </h3>
      </div>
      <div
        class="e-block-body"
      >
        <div
          class="e-grid-row e-grid-row-8 "
          style="grid-template-columns: repeat(8, 1fr); padding: 16px 16px 16px 16px;"
        >
          <div
            class="e-grid-column e-grid-column-2 e-field-grid-column e-field-grid-column-hidden"
            style="grid-column: span 2;"
          >
            <div
              class="e-field e-text-field e-context-page"
              data-testid="e-text-field e-field-bind-field1"
            >
              <div
                class="c0 c1"
              >
                <div
                  class="c2"
                  data-role="field-line"
                >
                  <div
                    class="c3"
                    data-role="input-presentation-container"
                  >
                    <div
                      class="c4"
                      disabled=""
                      role="presentation"
                    >
                      <input
                        aria-invalid="false"
                        autocomplete="off"
                        class="c5 c6"
                        data-element="input"
                        data-testid="e-text-field-input"
                        disabled=""
                        id="TestPage-field1-page"
                        name="field1"
                        placeholder=""
                        type="text"
                        value="Test value"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`BlockComponent Snapshots should render with default properties 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c6 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c6:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c6::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c6::placeholder {
  color: var(--colorsUtilityYin055);
}

.c3 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c4 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c4 .c5 {
  padding: 0 var(--spacing150);
}

.c4 input::-ms-clear {
  display: none;
}

.c4 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-grid-column e-grid-column-8 e-container-parent e-block-parent e-block-context-page"
    style="grid-column: span 8;"
  >
    <div
      class="e-block"
      data-testid="e-block-field e-field-label-testBlockTitle e-field-bind-test-block"
    >
      <div
        class="e-block-header"
      >
        <h3
          class="e-block-title"
          data-testid="e-block-title"
        >
          Test Block Title
        </h3>
      </div>
      <div
        class="e-block-body"
      >
        <div
          class="e-grid-row e-grid-row-8 "
          style="grid-template-columns: repeat(8, 1fr); padding: 16px 16px 16px 16px;"
        >
          <div
            class="e-grid-column e-grid-column-2 e-field-grid-column"
            style="grid-column: span 2;"
          >
            <div
              class="e-field e-text-field e-context-page"
              data-testid="e-text-field e-field-bind-field1"
            >
              <div
                class="c0 c1"
              >
                <div
                  class="c2"
                  data-role="field-line"
                >
                  <div
                    class="c3"
                    data-role="input-presentation-container"
                  >
                    <div
                      class="c4"
                      role="presentation"
                    >
                      <input
                        aria-invalid="false"
                        autocomplete="off"
                        class="c5 c6"
                        data-element="input"
                        data-testid="e-text-field-input"
                        id="TestPage-field1-page"
                        name="field1"
                        type="text"
                        value="Test value"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
