@import '../../../render/style/variables.scss';

.e-sticker-dialog-footer {
    box-shadow: var(--boxShadow200);
    display: flex;
    margin: 0 -34px -30px -34px;
    padding: 0 34px;
    background-color: #ffffff;

    .e-dialog-business-actions {
        flex: 1;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        padding: 12px 8px;

        .e-business-action {
            flex: 1;
            display: block;
            margin: 0 8px;

            > button {
                display: block;
                width: 100%;
                margin: 0;
                padding: 0 8px;
            }
        }
    }

    .e-dialog-business-actions-more {
        margin-left: 8px;

        > div {
            > div {
                bottom: 50px;
            }
        }
    }
}

.e-sticker-dialog-footer.e-sticker-dialog-footer-fullscreen {
    margin-top: -64px;

    .e-action-popover-mobile-button > div {
        padding: 0;
    }
}
