import { objectKeys } from '@sage/xtrem-shared';
import { AbstractLayoutBuilder } from '../../abstract-layout-builder';
import type { Container<PERSON>ey, FieldControlObjectInstance } from '../../types';

// TODO: StickerLayout it's the exact same code as page-layout.
export class StickerLayout extends AbstractLayoutBuilder<ContainerKey.Sticker> {
    buildLayout = (): this => {
        return this.populateSections().populateBlocks().populateFields();
    };

    populateFields = (): this => {
        objectKeys(this.metadata.pageMetadata.fieldThunks).forEach(fieldId => {
            this.metadata.pageMetadata.fieldThunks[fieldId](this.nodeTypes, this.dataTypes);
            const fieldControlObject = this.metadata.pageMetadata.controlObjects[
                fieldId
            ] as FieldControlObjectInstance<any>;
            if (fieldControlObject && fieldControlObject.parent) {
                const parent = fieldControlObject.parent.apply(this.target.prototype).layout;
                objectKeys(this.metadata.pageMetadata.layoutBlocks).forEach((blockId: any) => {
                    if (blockId === parent.$containerId) {
                        fieldControlObject.layout.$containerType = parent.$category;
                        this.metadata.pageMetadata.layoutBlocks[blockId].$layout!.$items.push(
                            fieldControlObject.layout,
                        );
                    }
                });
            }
        });
        return this;
    };

    populateBlocks = (): this => {
        objectKeys(this.metadata.pageMetadata.blockThunks).forEach(blockId => {
            this.metadata.pageMetadata.blockThunks[blockId](this.nodeTypes, this.dataTypes);

            const blockControlObject = this.metadata.pageMetadata.controlObjects[
                blockId
            ] as FieldControlObjectInstance<any>;
            if (blockControlObject && blockControlObject.parent) {
                const parent = blockControlObject.parent.apply(this.target.prototype);
                const parentId = parent.layout.$containerId;
                Object.values(this.metadata.pageMetadata.layout.$items).forEach((section: any) => {
                    if (section.$containerId === parentId) {
                        section.$layout.$items.push(blockControlObject.layout);
                    }
                });
            }
        });

        return this;
    };

    populateSections = (): this => {
        objectKeys(this.metadata.pageMetadata.sectionThunks).forEach((sectionId: string) => {
            this.metadata.pageMetadata.sectionThunks[sectionId](this.nodeTypes, this.dataTypes);
        });

        return this;
    };
}
