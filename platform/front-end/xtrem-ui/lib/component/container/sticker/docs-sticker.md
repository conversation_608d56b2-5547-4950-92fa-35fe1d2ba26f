## Documentation - Sticker
| name                | optional | description                                                                                                                                                                                                                                    | type                                                                                                           | default                                                                                                                                              |
| ------------------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------- |
| icon                | false    | Sticker icon                                                                                                                                                                                                                                   | <pre lang="javascript">IconType</pre>                                                                          |                                                                                                                                                      |
| isActive            | false    | Expression that determines whether the sticker is currently active or not                                                                                                                                                                      | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;boolean&nbsp;|&nbsp;Promise<boolean></pre>                 |                                                                                                                                                      |
| _controlObjectType  | true     | The type of the corresponding control object, for internal use only                                                                                                                                                                            | <pre lang="javascript">ComponentKey</pre>                                                                      | <pre lang="javascript">undefined</pre>                                                                                                               |
| access              | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">AccessConfiguration</pre>                                                               | <pre lang="javascript">{&nbsp;node:&nbsp;'<node>',&nbsp;bind:&nbsp;'<bind>'&nbsp;}</pre>                                                             |
| businessActions     | true     | Business actions available in this page                                                                                                                                                                                                        | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;PageActionControlObject<ScreenBase<any,&nbsp;any>>[]</pre> | <pre lang="javascript">[]</pre>                                                                                                                      |
| category            | true     | Metadata field for organizing sticker                                                                                                                                                                                                          | <pre lang="javascript">string</pre>                                                                            | <pre lang="javascript">''</pre>                                                                                                                      |
| isDisabled          | true     | Whether the HTML element is disabled or not. Defaults to false The difference with readOnly is that disabled suggests that the field is not editable for some validation reason (e.g. a button which can't be clicked due to validation errors | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>         | <pre lang="javascript">false</pre>                                                                                                                   |
| isHidden            | true     | Whether the HTML element is hidden or not. Defaults to false                                                                                                                                                                                   | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>         | <pre lang="javascript">false</pre>                                                                                                                   |
| isHiddenDesktop     | true     | Whether the element is hidden or not in desktop devices. Defaults to false                                                                                                                                                                     | <pre lang="javascript">boolean</pre>                                                                           | <pre lang="javascript">false</pre>                                                                                                                   |
| isHiddenMobile      | true     | Whether the element is hidden or not in mobile devices. Defaults to false                                                                                                                                                                      | <pre lang="javascript">boolean</pre>                                                                           | <pre lang="javascript">false</pre>                                                                                                                   |
| isTitleHidden       | true     | Whether the element title is hidden or not. Defaults to false                                                                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                           | <pre lang="javascript">false</pre>                                                                                                                   |
| isTransient         | true     | Whether the value is bound to a GraphQL node (transient = false) or not (transient = true). Defaults to false                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                           | <pre lang="javascript">false</pre>                                                                                                                   |
| onClose             | true     | Function that will be executed immediately before the sticker closes                                                                                                                                                                           | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                                 | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                                                                                          |
| onDirtyStateUpdated | true     | Function that will be executed when the page becomes dirty or clean                                                                                                                                                                            | <pre lang="javascript">(this:&nbsp;CT,&nbsp;isDirty:&nbsp;boolean)&nbsp;=>&nbsp;void</pre>                     | <pre lang="javascript">function(isDirty<br>)&nbsp;{<br><br>}</pre>                                                                                   |
| onError             | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">ErrorHandlerFunction<CT></pre>                                                          | <pre lang="javascript">function(error,screenId,elementId<br>)&nbsp;{<br>console.error({&nbsp;error,&nbsp;screenId,&nbsp;elementId&nbsp;})<br>}</pre> |
| onLoad              | true     | Function that will be executed immediately after the sticker has been loaded                                                                                                                                                                   | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                                 | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                                                                                          |
| onOpen              | true     | Function that will be executed when the user opens the sticker                                                                                                                                                                                 | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                                 | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                                                                                          |
| title               | true     | The title of the HTML element                                                                                                                                                                                                                  | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;any,&nbsp;Dict<any>></pre>          | <pre lang="javascript">''</pre>                                                                                                                      |
| validation          | true     | Container validation rule                                                                                                                                                                                                                      | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                                 | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                                                                                          |