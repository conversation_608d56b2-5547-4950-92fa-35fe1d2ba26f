PATH: XTREEM/Client+Framework/Sticker+API

## Introduction

Stickers are package-level invokable **containers** that provide run-in-the-background capabilities.

They also:

-   persist across page navigation
-   able to update display information of their decorating menu item

## Example:

```ts
import { GraphApi } from '@sage/x3-show-case-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.sticker<ActiveSticker>({
    title: 'Active sticker',
    icon: 'home',
    category:'RANDOM_CATEGORY',
    onOpen(){
        this.$.showToast('This sticker is open!');
    }
    isActive() {
        return this.$.graph
            .node('@sage/x3-products/Product')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        code: true,
                        localizedDescription1: true,
                        productCategory: true,
                        productVolume: true,
                    },
                    {
                        first: 1,
                    },
                ),
            )
            .execute()
            .then(() => {
                return true;
            });
    },
})
export class ActiveSticker extends ui.Sticker<GraphApi> {
    @ui.decorators.section<ActiveSticker>({
        title: 'Section 1',
    })
    section1: ui.containers.Section;

    @ui.decorators.block<ActiveSticker>({
        title: 'Block 1',
        parent() {
            return this.section1;
        },
    })
    block1: ui.containers.Block;

    @ui.decorators.numericField<ActiveSticker>({
        title: 'Numeric badgeContent test',
        onChange() {
            this.updateLabel(this.field1.value);
        },
        parent() {
            return this.block1;
        },
    })
    field1: ui.fields.Numeric;

    @ui.decorators.block<ActiveSticker>({
        title: 'Block 2',
        parent() {
            return this.section1;
        },
    })
    block2: ui.containers.Block;

    @ui.decorators.textField<ActiveSticker>({
        title: 'Text badgeContent test',
        onChange() {
            this.updateLabel(this.field2.value);
        },
        parent() {
            return this.block2;
        },
    })
    field2: ui.fields.Text;

    @ui.decorators.block<ActiveSticker>({
        title: 'Block 3',
        parent() {
            return this.section1;
        },
    })
    block3: ui.containers.Block;

    @ui.decorators.buttonField<ActiveSticker>({
        map() {
            return 'Finish sticker';
        },
        onClick() {
            this.$.finish();
        },
        parent() {
            return this.block3;
        },
    })
    finishButton: ui.fields.Button;

    updateLabel(value: string | number) {
        this.$.sticker.updateMenuItem(value);
    }
}
```

### Decorator properties

-   **icon**: Sticker menu item icon (https://designsystem.sage.com/foundations/icons/)
-   **isActive**: Expression that determines whether the sticker is currently active or not
-   **onLoad**: Function that will be executed immediately after the sticker has been loaded
-   **onClose**: Function that will be executed immediately before the sticker closes
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

### API methods

-   **\$.sticker.updateMenuItem**: Updates menu item display information
-   **\$.sticker.validate**: Triggers the validation rules of all the fields of the sticker
-   **\$.finish**: Closes the sticker
