/**
 * @packageDocumentation
 * @module root
 * */

import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { getUiComponentProperties, setUiComponentProperties } from '../../../service/transactions-service';
import type { ScreenExtension } from '../../../types';
import type { ContainerProperties } from '../../abstract-container';
import { AbstractContainer } from '../../abstract-container';
import type { ContainerComponentProps, ContainerControlObjectConstructorProps } from '../../types';
import { ContainerKey } from '../../types';

export interface StickerProperties<CT extends ScreenBase = ScreenBase> extends ContainerProperties<CT> {
    /** Metadata field for organizing sticker */
    category?: string;
    /** Sticker icon */
    icon: IconType;
}

export interface IStickerControlObject
    extends Omit<ContainerControlObjectConstructorProps<ContainerKey.Sticker>, 'elementId'> {
    updateMenuItem: (badgeContent?: string | number) => void;
    dispatchPageValidation: () => Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
}

/**
 * Main [container]{@link AbstractContainer} of each screen. Holds any number of [sections]{@link SectionControlObject}
 * and might define additional parts (e.g. crud actions, business actions, a helper panel, etc.)
 */
export class StickerControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends AbstractContainer<
    CT,
    ContainerKey.Sticker,
    ContainerComponentProps<ContainerKey.Sticker>
> {
    private readonly _updateMenuItem: (badgeContent?: string | number) => void;

    private readonly _dispatchPageValidation: () => Promise<{
        allErrors: ValidationResult[];
        blockingErrors: ValidationResult[];
    }>;

    constructor(properties: IStickerControlObject) {
        // TODO: implement validation of the Sticker container. Send all fields
        super(
            properties.screenId,
            properties.screenId,
            properties.getUiComponentProperties || getUiComponentProperties,
            properties.setUiComponentProperties || setUiComponentProperties,
            ContainerKey.Sticker,
            properties.getValidationState || (async (): Promise<boolean> => true),
            properties.layout,
        );
        this._updateMenuItem = properties.updateMenuItem;
        this._dispatchPageValidation = properties.dispatchPageValidation;
    }

    // TODO Move this method to the abstract container
    /**
     * Triggers the validation rules of all the fields of the page. Since the validation rules
     * might be asynchronous, this method returns a promise that must be awaited to get
     * the validation result
     */
    async validate(): Promise<string[]> {
        const result = await this.validateWithDetails();
        return result.map(r => r!.message);
    }

    async validateWithDetails(
        partition: true,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
    async validateWithDetails(partition: false): Promise<ValidationResult[]>;
    async validateWithDetails(): Promise<ValidationResult[]>;
    async validateWithDetails(
        partition = false,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] } | ValidationResult[]> {
        const errors = await this._dispatchPageValidation();
        return partition ? errors : errors.allErrors;
    }

    updateMenuItem(badgeContent?: string | number): void {
        this._updateMenuItem(badgeContent);
    }
}
