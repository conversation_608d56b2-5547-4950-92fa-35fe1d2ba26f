/**
 * @packageDocumentation
 * @module root
 * */

import { actions, getStore } from '../../../redux';
import { dispatchContainerValidation } from '../../../service/dispatch-service';
import { getPageMetadata } from '../../../service/page-metadata';
import type { ValidationResult } from '../../../service/screen-base-definition';
import type { StickerExtension } from '../../../types';
import { AbstractDecorator } from '../../abstract-decorator';
import type { PageActionControlObject, StickerProperties } from '../../control-objects';
import { StickerControlObject } from '../../control-objects';
import type { HasGenericErrorHandler } from '../../field/traits';
import { ContainerKey } from '../../types';
import { StickerLayout } from '../layouts';
import type * as xtremRedux from '../../../redux';
import { objectKeys } from '@sage/xtrem-shared';

export interface StickerDecoratorProperties<CT extends StickerExtension<CT>>
    extends StickerProperties,
        HasGenericErrorHandler<CT> {
    /** Business actions available in this page */
    businessActions?: (this: CT) => PageActionControlObject[];
    /** Expression that determines whether the sticker is currently active or not */
    isActive: (this: CT) => boolean | Promise<boolean>;
    /** Function that will be executed immediately after the sticker has been loaded */
    onLoad?: (this: CT) => void;
    /** Function that will be executed immediately before the sticker closes */
    onClose?: (this: CT) => void;
    /** Function that will be executed when the user opens the sticker */
    onOpen?: (this: CT) => void;
    /** Function that will be executed when the page becomes dirty or clean */
    onDirtyStateUpdated?: (this: CT, isDirty: boolean) => void;
}

class StickerDecorator extends AbstractDecorator<ContainerKey.Sticker> {
    protected _layout = StickerLayout;

    protected _controlObjectConstructor = StickerControlObject;

    protected readonly buildActions = (): this => {
        if (this._metadataProps.pageMetadata) {
            objectKeys(this._metadataProps.pageMetadata.pageActionThunks).forEach(k => {
                this._metadataProps.pageMetadata.pageActions[k] = this._metadataProps.pageMetadata.pageActionThunks[k](
                    this.nodeTypes,
                    this.dataTypes,
                );
            });
        }
        return this;
    };
}

/**
 * Initializes the decorated member as a [Sticker]{@link StickerControlObject} container with the provided properties
 *
 * @param properties The properties that the [Sticker]{@link StickerControlObject} container will be initialized with
 */
export function sticker<CT extends StickerExtension<CT>>(
    properties: StickerDecoratorProperties<CT>,
): (ctor: Function) => void {
    return (ctor: Function): void => {
        const pageMetadata = getPageMetadata(ctor);
        const updateMenuItem = (badgeContent?: string): void => {
            const store = getStore();
            const thunkDispatch = store.dispatch as xtremRedux.AppThunkDispatch;
            thunkDispatch(actions.updateMenuItem(pageMetadata.screenId, badgeContent));
        };

        pageMetadata.businessActionsThunk = properties.businessActions;

        new StickerDecorator(
            ctor,
            pageMetadata.screenId,
            { pageMetadata, properties },
            ContainerKey.Sticker,
            {},
            {},
            {
                dispatchPageValidation: (): Promise<{
                    allErrors: ValidationResult[];
                    blockingErrors: ValidationResult[];
                }> => dispatchContainerValidation(pageMetadata.screenId, pageMetadata.screenId),
                updateMenuItem,
            },
        ).build();
    };
}
