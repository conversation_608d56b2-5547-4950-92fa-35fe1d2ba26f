import * as dispatchService from '../../../../service/dispatch-service';
import * as pageMetadataImport from '../../../../service/page-metadata';
import type { Sticker } from '../../../../service/sticker';
import * as transactionService from '../../../../service/transactions-service';
import { getMockPageMetadata } from '../../../../__tests__/test-helpers';
import { block, section, textField } from '../../../decorators';
import type { StickerDecoratorProperties } from '../sticker-decorator';
import { sticker } from '../sticker-decorator';

const populateBlockThunks = (stickerInstance: Sticker, sectionId: string, blockId: string) => {
    const blockDecorator = block({
        title: 'Test block',
        parent: () => stickerInstance[sectionId],
    });
    blockDecorator(stickerInstance, blockId);
};

const populateSectionThunks = (stickerInstance: Sticker, sectionId: string) => {
    const sectionDecorator = section({
        title: 'Test section',
    });
    sectionDecorator(stickerInstance, sectionId);
};

const populateFieldThunks = (stickerInstance: Sticker, fieldId: string, blockId: string) => {
    const textDecorator = textField({
        title: 'Test field',
        parent: () => stickerInstance[blockId],
    });
    textDecorator(stickerInstance, fieldId);
};

describe('Sticker decorator', () => {
    let decoratorProperties: StickerDecoratorProperties<Sticker>;
    let pageMetadata: pageMetadataImport.PageMetadata;
    const testFieldId = 'test-field';
    const testBlockId = 'test-block';
    const testSectionId = 'test-section';
    class TestSticker {}
    const stickerName = TestSticker.name;

    beforeEach(() => {
        const stickerInstance = new TestSticker() as Sticker;
        pageMetadata = getMockPageMetadata(stickerName, { target: stickerInstance });
        jest.spyOn(transactionService, 'getUiComponentProperties').mockImplementation(jest.fn());
        jest.spyOn(transactionService, 'setUiComponentProperties').mockImplementation(jest.fn());

        jest.spyOn(dispatchService, 'dispatchContainerValidation').mockImplementation(jest.fn());
        jest.spyOn(pageMetadataImport, 'getPageMetadata').mockImplementation(() => pageMetadata);

        populateSectionThunks(stickerInstance, testSectionId);
        populateBlockThunks(stickerInstance, testSectionId, testBlockId);
        populateFieldThunks(stickerInstance, testFieldId, testBlockId);

        decoratorProperties = {
            title: 'Sticker',
            isHidden: false,
            isDisabled: false,
            isTransient: true,
            icon: 'home',
            onLoad: () => {},
            onClose: () => {},
            isActive: () => true,
        };
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('Properties Mapping', () => {
        it('Should map all the decorator properties', () => {
            const decorator = sticker(decoratorProperties);

            expect(pageMetadata.controlObjects[stickerName]).toBeUndefined();

            decorator(TestSticker);

            expect(pageMetadata.controlObjects[stickerName]).toBeDefined();
            expect(pageMetadata.uiComponentProperties[stickerName]).toBeDefined();

            const containerUiProperties = pageMetadata.uiComponentProperties[stickerName];

            Object.keys(decoratorProperties).forEach(key =>
                expect(containerUiProperties[key]).toBe(decoratorProperties[key]),
            );

            expect(pageMetadata.layout.$items.length).toBe(1);
            const testSection = pageMetadata.layout.$items[0];
            expect(testSection.$containerId).toBe(testSectionId);
            expect(testSection.$layout!.$items.length).toBe(1);
            const testBlock = testSection.$layout!.$items[0];
            expect(testBlock.$containerId).toBe(testBlockId);
            expect(testBlock.$layout!.$items.length).toBe(1);
            const testField = testBlock.$layout!.$items[0];
            expect(testField.$bind).toBe(testFieldId);
        });
    });
});
