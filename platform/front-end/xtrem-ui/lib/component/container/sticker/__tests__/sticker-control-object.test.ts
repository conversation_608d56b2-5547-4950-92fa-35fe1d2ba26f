import type { StickerProperties } from '../../../control-objects';
import { StickerControlObject } from '../../../control-objects';

describe('Sticker control object', () => {
    const stickerId = 'TestSticker';
    let sticker: StickerControlObject;
    let stickerProperties: StickerProperties;
    let dispatchValidationMock: jest.Mock<Promise<any>>;
    let updateMenuItemMock: jest.Mock;

    beforeEach(() => {
        stickerProperties = {
            icon: 'home',
        };
        const getPropertiesMock = jest.fn(() => ({
            ...stickerProperties,
            updateMenuItem: () => {},
            dispatchPageValidation: () =>
                Promise.resolve({
                    allErrors: [
                        {
                            screenId: stickerId,
                            elementId: 'someElement',
                            message: 'Validation message',
                            validationRule: 'validation',
                        },
                    ],
                    blockingErrors: [
                        {
                            screenId: stickerId,
                            elementId: 'someElement',
                            message: 'Validation message',
                            validationRule: 'validation',
                        },
                    ],
                }),
            target: 'target',
            elementId: 'id',
            layout: {},
        }));
        const setPropertiesMock = jest.fn((_screenId: string, elementId: string, value: any) => {
            stickerProperties = { ...value };
        });
        dispatchValidationMock = jest.fn(() =>
            Promise.resolve({
                allErrors: [
                    {
                        screenId: stickerId,
                        elementId: 'someElement',
                        message: 'Validation message',
                        validationRule: 'validation',
                    },
                ],
                blockingErrors: [
                    {
                        screenId: stickerId,
                        elementId: 'someElement',
                        message: 'Validation message',
                        validationRule: 'validation',
                    },
                ],
            }),
        );
        updateMenuItemMock = jest.fn();
        sticker = new StickerControlObject({
            screenId: stickerId,
            getUiComponentProperties: getPropertiesMock,
            setUiComponentProperties: setPropertiesMock,
            dispatchPageValidation: dispatchValidationMock,
            updateMenuItem: updateMenuItemMock,
            layout: {},
        });
    });

    it('should call dispatchValidation function on validate', async () => {
        expect(dispatchValidationMock).not.toHaveBeenCalled();
        const result = await sticker.validate();
        expect(dispatchValidationMock).toHaveBeenCalledTimes(1);
        expect(result).toEqual(['Validation message']);
    });

    it('should call dispatchValidation function on validateWithDetails', async () => {
        expect(dispatchValidationMock).not.toHaveBeenCalled();
        const result = await sticker.validateWithDetails();
        expect(dispatchValidationMock).toHaveBeenCalledTimes(1);
        expect(result).toEqual([
            {
                screenId: stickerId,
                elementId: 'someElement',
                message: 'Validation message',
                validationRule: 'validation',
            },
        ]);
    });

    it('should call _updateMenuItem function on updateMenuItem', () => {
        const badgeContent = '!';
        expect(updateMenuItemMock).not.toHaveBeenCalled();
        sticker.updateMenuItem(badgeContent);
        expect(updateMenuItemMock).toHaveBeenCalledWith(badgeContent);
    });
});
