/**
 * @packageDocumentation
 * @module root
 * */
import type { ClientNode } from '@sage/xtrem-client';
import type { Dict } from '@sage/xtrem-shared';
import { set } from 'lodash';
import type { Page } from '../../../service/page';
import type { PageMetadata } from '../../../service/page-metadata';
import { getPageMetadata } from '../../../service/page-metadata';
import type { PageExtension, ScreenExtension } from '../../../types';
import type { MenuSeparatorResult } from '../../../utils/action-menu-utils';
import { getTargetPrototype } from '../../../utils/decorator-utils';
import { injectOnloadAfterIntoPageMetadata } from '../../../utils/page-utils';
import { insertExtendedActions, mergeTableActions } from '../../abstract-decorator-utils';
import type { UiComponentProperties } from '../../abstract-ui-control-object';
import type { PageActionControlObject } from '../../control-objects';
import type { AccessConfiguration } from '../../field/traits';
import type { PageDecoratorProperties } from './page-decorator';
import type {
    BasePageDecoratorProperties,
    PageNavigationPanelExtension,
    PageProperties,
    RuntimePageNavigationPanel,
} from './page-types';

export type HeaderDropDownActionsExtension<CT extends ScreenExtension<CT> = Page> = (
    this: CT,
) => Array<PageActionControlObject<CT> | MenuSeparatorResult>;

export type HeaderQuickActionsExtension<CT extends ScreenExtension<CT> = Page> = (
    this: CT,
) => Array<PageActionControlObject<CT>>;

export interface PageExtensionDecoratorProperties<CT extends PageExtension<CT>, NodeType extends ClientNode = any>
    extends BasePageDecoratorProperties<CT> {
    /** Page that this extension extends in the following format: @sage/package-name/PageName */
    extends: string;

    /** Access binding that determines whether the extension is loaded */
    extensionAccessBinding?: AccessConfiguration;

    /** Navigation panel property overrides */
    navigationPanel?: PageNavigationPanelExtension<CT, NodeType>;

    /** Navigation panel property overrides */
    businessActions?: (this: CT) => PageActionControlObject[];

    headerDropDownActions?: HeaderDropDownActionsExtension<CT>;

    headerQuickActions?: HeaderQuickActionsExtension<CT>;
}

export interface RuntimePageMetadata extends PageMetadata {
    uiComponentProperties: Dict<
        UiComponentProperties & {
            onLoadAfter: Array<PageDecoratorProperties<any>['onLoad']>;
            onCloseAfter: Array<PageDecoratorProperties<any>['onClose']>;
        }
    >;
    defaultUiComponentProperties: Dict<
        UiComponentProperties & {
            onLoadAfter: Array<PageDecoratorProperties<any>['onLoad']>;
            onCloseAfter: Array<PageDecoratorProperties<any>['onClose']>;
        }
    >;
}

const processNavigationPanelExtension = <CT extends PageExtension<CT>, NodeType extends ClientNode = any>(
    pageMetadata: RuntimePageMetadata,
    navigationPanel: PageNavigationPanelExtension<CT, NodeType>,
): void => {
    const pageDefaultProperties = pageMetadata.defaultUiComponentProperties[
        pageMetadata.screenId
    ] as PageDecoratorProperties<CT, NodeType>;

    const pageProperties = pageMetadata.uiComponentProperties[pageMetadata.screenId] as PageDecoratorProperties<
        CT,
        NodeType
    >;

    const navigationPanelDefaultProperties = pageDefaultProperties.navigationPanel as RuntimePageNavigationPanel<
        CT,
        NodeType
    >;

    const navigationPanelProperties = pageProperties.navigationPanel as RuntimePageNavigationPanel<CT, NodeType>;

    if (navigationPanel.listItem) {
        navigationPanelDefaultProperties.extensionListItem.push(navigationPanel.listItem);
        navigationPanelProperties.extensionListItem.push(navigationPanel.listItem);
    }

    if (navigationPanel.onEmptyStateLinkClick) {
        navigationPanelDefaultProperties.onEmptyStateLinkClick = navigationPanel.onEmptyStateLinkClick;
        navigationPanelProperties.onEmptyStateLinkClick = navigationPanel.onEmptyStateLinkClick;
    }

    if (navigationPanel.onEmptyStateLinkClickAfter) {
        navigationPanelDefaultProperties.onEmptyStateLinkClickAfter.push(navigationPanel.onEmptyStateLinkClickAfter);
        navigationPanelProperties.onEmptyStateLinkClickAfter.push(navigationPanel.onEmptyStateLinkClickAfter);
    }

    if (navigationPanel.onSelect) {
        navigationPanelDefaultProperties.onSelect = navigationPanel.onSelect;
        navigationPanelProperties.onSelect = navigationPanel.onSelect;
    }

    if (navigationPanel.onSelectAfter) {
        navigationPanelDefaultProperties.onSelectAfter.push(navigationPanel.onSelectAfter);
        navigationPanelProperties.onSelectAfter.push(navigationPanel.onSelectAfter);
    }

    if (navigationPanel.onOptionsMenuValueChange) {
        navigationPanelDefaultProperties.onOptionsMenuValueChange = navigationPanel.onOptionsMenuValueChange;
        navigationPanelProperties.onOptionsMenuValueChange = navigationPanel.onOptionsMenuValueChange;
    }

    if (navigationPanel.onOptionsMenuValueChangeAfter) {
        navigationPanelDefaultProperties.onOptionsMenuValueChangeAfter.push(
            navigationPanel.onOptionsMenuValueChangeAfter,
        );
        navigationPanelProperties.onOptionsMenuValueChangeAfter.push(navigationPanel.onOptionsMenuValueChangeAfter);
    }

    if (navigationPanel.onEmptyStateLinkClick) {
        navigationPanelProperties.onEmptyStateLinkClick = navigationPanel.onEmptyStateLinkClick;
    }

    if (navigationPanel.optionsMenu) {
        navigationPanelProperties.optionMenus.push(navigationPanel.optionsMenu);
    }

    if (navigationPanel.emptyStateText) {
        navigationPanelProperties.emptyStateText = navigationPanel.emptyStateText;
    }

    if (navigationPanel.emptyStateClickableText) {
        navigationPanelProperties.emptyStateClickableText = navigationPanel.emptyStateClickableText;
    }

    if (navigationPanel.dropdownActions) {
        navigationPanelProperties.dropdownActions = mergeTableActions(
            navigationPanelProperties.dropdownActions || [],
            navigationPanel.dropdownActions,
        );
    }

    if (navigationPanel.inlineActions) {
        navigationPanelProperties.inlineActions = mergeTableActions(
            navigationPanelProperties.inlineActions || [],
            navigationPanel.inlineActions,
        );
    }
};

export function pageExtension<CT extends PageExtension<CT>, NodeType extends ClientNode = any>(
    properties: PageExtensionDecoratorProperties<CT, NodeType>,
) {
    type HeaderDropDownActionsType = PageProperties<CT, NodeType>['headerDropDownActions'];
    type HeaderQuickActionsType = PageProperties<CT, NodeType>['headerQuickActions'];

    return (ctor: Function): void => {
        const targetPrototype = getTargetPrototype(ctor);
        (ctor as any).basePage = targetPrototype.name;
        Object.getOwnPropertyNames(ctor.prototype)
            .filter(k => k !== 'constructor')
            .forEach(memberName => {
                targetPrototype.prototype[memberName] = ctor.prototype[memberName];
            });
        const pageMetadata = getPageMetadata(targetPrototype) as RuntimePageMetadata;

        pageMetadata.pageExtensionThunks.push(() => {
            const pageProperties = pageMetadata.uiComponentProperties[pageMetadata.screenId] as PageDecoratorProperties<
                CT,
                NodeType
            >;
            if (properties.onLoad) {
                injectOnloadAfterIntoPageMetadata(pageMetadata, properties.onLoad);
            }

            if (properties.businessActions) {
                pageMetadata.businessActionsExtensionsThunk.push(properties.businessActions);
            }

            if (properties.onClose) {
                if (pageMetadata.uiComponentProperties?.[pageMetadata.screenId]?.onCloseAfter) {
                    pageMetadata.uiComponentProperties[pageMetadata.screenId].onCloseAfter.push(properties.onClose);
                    pageMetadata.defaultUiComponentProperties[pageMetadata.screenId].onCloseAfter.push(
                        properties.onClose,
                    );
                } else {
                    set(
                        pageMetadata,
                        ['uiComponentProperties', pageMetadata.screenId, 'onCloseAfter'],
                        [properties.onClose],
                    );
                    set(
                        pageMetadata,
                        ['defaultUiComponentProperties', pageMetadata.screenId, 'onCloseAfter'],
                        [properties.onClose],
                    );
                }
            }

            const pageDefaultProperties = pageMetadata.defaultUiComponentProperties[
                pageMetadata.screenId
            ] as PageDecoratorProperties<CT, NodeType>;

            if (properties.navigationPanel) {
                if (!pageDefaultProperties.navigationPanel) {
                    throw new Error(
                        `Cannot extend navigation panel on ${pageMetadata.screenId} because the base page does not have a navigation panel.`,
                    );
                }
                processNavigationPanelExtension<CT, NodeType>(pageMetadata, properties.navigationPanel);
            }

            // Handle header dropdown actions extension
            if (properties.headerDropDownActions) {
                const baseActionsThunk = pageProperties.headerDropDownActions;

                const headerDropDownActionsOverride: HeaderDropDownActionsType = function headerDropDownActionsOverride(
                    this: CT,
                ) {
                    const extendedActions = properties.headerDropDownActions?.call(this);
                    const baseActions = baseActionsThunk?.call(this) || [];

                    return insertExtendedActions(this, baseActions, extendedActions);
                };

                pageProperties.headerDropDownActions = headerDropDownActionsOverride;
                pageDefaultProperties.headerDropDownActions = headerDropDownActionsOverride;
            }

            if (properties.headerQuickActions) {
                const baseActionsThunk = pageProperties.headerQuickActions;

                const headerQuickActionsOverride: HeaderQuickActionsType = function headerQuickActionsOverride(
                    this: CT,
                ) {
                    const extendedActions = properties.headerQuickActions?.call(this);
                    const baseActions = baseActionsThunk?.call(this) || [];

                    return insertExtendedActions(this, baseActions, extendedActions);
                };

                pageProperties.headerQuickActions = headerQuickActionsOverride;
                pageDefaultProperties.headerQuickActions = headerQuickActionsOverride;
            }
        });
    };
}
