import * as React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import type { IconType } from 'carbon-react/esm/components/icon';
import Icon from 'carbon-react/esm/components/icon';
import IconButton from 'carbon-react/esm/components/icon-button';
import type { PageActionControlObject, PageActionProperties } from '../../page-action/page-action-control-object';
import { getPageDefinitionFromState, getPagePropertiesFromState } from '../../../utils/state-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { camelCase } from 'lodash';
import { triggerFieldEvent } from '../../../utils/events';
import * as tokens from '@sage/design-tokens/js/base/common';
import { getElementAccessStatus } from '../../../utils/access-utils';
import type { OnTelemetryEventFunction } from '../../../redux/state';

export interface HeaderQuickActionExternalProp {
    screenId: string;
    id: string;
}

export interface HeaderQuickActionProp extends HeaderQuickActionExternalProp {
    icon: IconType;
    isDisabled?: boolean;
    isHidden?: boolean;
    title?: string;
    onTelemetryEvent?: OnTelemetryEventFunction;
}

function HeaderQuickAction({
    icon,
    isDisabled,
    title,
    isHidden,
    screenId,
    id,
    onTelemetryEvent,
}: HeaderQuickActionProp): React.ReactElement | null {
    const onClick = (): void => {
        onTelemetryEvent?.(`headerQuickActionItemTriggered-${id}`, { screenId, elementId: id });
        triggerFieldEvent(screenId, id, 'onClick');
    };

    if (isHidden) {
        return null;
    }

    return (
        <span className="e-header-quick-action">
            <IconButton
                onClick={onClick}
                disabled={isDisabled}
                aria-label={title}
                data-testid={`e-header-quick-action-label-${camelCase(title)}`}
                data-pendoid={`e-header-quick-action-${id}`}
            >
                <Icon type={icon} tooltipMessage={title} color={tokens.colorsUtilityMajor400} />
            </IconButton>
        </span>
    );
}
const ConnectedHeaderQuickAction = connect(
    (state: xtremRedux.XtremAppState, { screenId, id }: HeaderQuickActionExternalProp): HeaderQuickActionProp => {
        const pageDefinition = getPageDefinitionFromState(screenId, state);
        const pageProperties = getPagePropertiesFromState(screenId, state);
        const actionProperties = pageDefinition.metadata.uiComponentProperties[id] as PageActionProperties;
        const accessRule = getElementAccessStatus({
            accessBindings: pageDefinition.accessBindings,
            bind: id,
            elementProperties: actionProperties,
            contextNode: pageProperties?.node,
            nodeTypes: state.nodeTypes,
            dataTypes: state.dataTypes,
        });
        const isDisabled = resolveByValue({
            screenId,
            propertyValue: actionProperties.isDisabled,
            skipHexFormat: true,
            rowValue: null,
            fieldValue: null,
        });
        const isHidden =
            resolveByValue({
                screenId,
                propertyValue: actionProperties.isHidden,
                skipHexFormat: true,
                rowValue: null,
                fieldValue: null,
            }) ||
            (actionProperties.access && accessRule !== 'authorized');

        const title = resolveByValue({
            screenId,
            propertyValue: actionProperties.title,
            skipHexFormat: true,
            rowValue: null,
            fieldValue: null,
        });

        const icon = actionProperties.icon;
        if (!icon) {
            throw new Error(`Quick actions must have a valid icon value. Screen Id: ${screenId}, Action Id: ${id}`);
        }

        return {
            id,
            icon,
            isDisabled,
            isHidden,
            screenId,
            title,
            onTelemetryEvent: state.applicationContext?.onTelemetryEvent,
        };
    },
)(HeaderQuickAction);

export interface HeaderQuickActionsExternalProps {
    screenId: string;
    actions?: Array<PageActionControlObject>;
}

export interface HeaderQuickActionsProps extends HeaderQuickActionsExternalProps {
    headerQuickActions?: Array<PageActionControlObject>;
}

export function HeaderQuickActions({
    screenId,
    headerQuickActions,
}: HeaderQuickActionsProps): React.ReactElement | null {
    if (!headerQuickActions) {
        return null;
    }

    return (
        <div className="e-header-quick-action-container">
            {headerQuickActions.map(a => (
                <ConnectedHeaderQuickAction key={a.id} screenId={screenId} id={a.id} />
            ))}
        </div>
    );
}

export const ConnectedHeaderQuickActions = connect(
    (state: xtremRedux.XtremAppState, props: HeaderQuickActionsExternalProps) => {
        const pageProperties = getPagePropertiesFromState(props.screenId, state);
        const rawQuickActions: PageActionControlObject[] =
            props.actions ??
            resolveByValue<Array<PageActionControlObject>>({
                skipHexFormat: true,
                screenId: props.screenId,
                propertyValue: pageProperties.headerQuickActions,
                rowValue: null,
                fieldValue: null,
            });

        return {
            ...props,
            headerQuickActions: rawQuickActions,
        };
    },
)(HeaderQuickActions);
