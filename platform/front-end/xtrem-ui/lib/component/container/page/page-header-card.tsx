import { objectKeys, type Dict } from '@sage/xtrem-shared';
import React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import type { PageDefinition } from '../../../service/page-definition';
import { ContextType } from '../../../types';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { getPageDefinitionFromState, getPagePropertiesFromPageDefinition } from '../../../utils/state-utils';
import type { ImageControlObject } from '../../control-objects';
import type { NestedField, NestedFieldTypesWithoutTechnical } from '../../nested-fields';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import type { FieldKey } from '../../types';
import type { CardDefinition } from '../../ui/card/card-component';
import { CardComponent } from '../../ui/card/card-component';
import type { PageHeaderCard, PageHeaderFields } from './page-types';

export interface PageHeaderCardExternalProps {
    screenId: string;
}

export interface PageHeaderCardProps extends PageHeaderCardExternalProps {
    cardDefinition: CardDefinition | null;
    screenId: string;
    value: Dict<any> | null;
}

const transformToNestedFieldDefinition = (
    pageDefinition: PageDefinition,
    field?: PageHeaderFields | ImageControlObject,
): NestedField<any, NestedFieldTypesWithoutTechnical, any> | undefined => {
    if (field) {
        const elementId = field.id;
        const properties = pageDefinition.metadata.uiComponentProperties[elementId] as ReadonlyFieldProperties;
        const defaultUiProperties = pageDefinition.metadata.defaultUiComponentProperties[elementId];
        return {
            properties: { ...properties, bind: properties.bind || elementId },
            defaultUiProperties: { ...defaultUiProperties, bind: properties.bind || elementId },
            type: defaultUiProperties._controlObjectType as NestedFieldTypesWithoutTechnical,
        };
    }

    return undefined;
};

export function PageHeaderCardComponent({
    screenId,
    cardDefinition,
    value,
}: PageHeaderCardProps): React.ReactElement | null {
    if (!cardDefinition || !value) {
        return null;
    }

    return (
        <CardComponent
            screenId={screenId}
            cardDefinition={cardDefinition}
            contextType={ContextType.pageHeader}
            parentElementId="$page"
            value={value as any}
        />
    );
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: PageHeaderCardExternalProps): PageHeaderCardProps => {
    const { screenId } = props;

    if (!state.browser.is.xs) {
        return { screenId, cardDefinition: null, value: null };
    }

    const pageDefinition = getPageDefinitionFromState(screenId, state);
    const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);
    const headerCard = resolveByValue<PageHeaderCard>({
        screenId,
        propertyValue: pageProperties.headerCard,
        rowValue: null,
        fieldValue: null,
        skipHexFormat: true,
    });

    if (!headerCard) {
        return { screenId, cardDefinition: null, value: null };
    }

    const title = transformToNestedFieldDefinition(pageDefinition, headerCard.title);
    if (!title) {
        throw new Error(`Title is not defined for page header card of ${screenId}`);
    }

    const cardDefinition: CardDefinition = {
        title,
        titleRight: transformToNestedFieldDefinition(pageDefinition, headerCard.titleRight),
        line2: transformToNestedFieldDefinition(pageDefinition, headerCard.line2),
        line2Right: transformToNestedFieldDefinition(pageDefinition, headerCard.line2Right),
        line3: transformToNestedFieldDefinition(pageDefinition, headerCard.line3),
        line3Right: transformToNestedFieldDefinition(pageDefinition, headerCard.line3Right),
        line4: transformToNestedFieldDefinition(pageDefinition, headerCard.line4),
        line4Right: transformToNestedFieldDefinition(pageDefinition, headerCard.line4Right),
        line5: transformToNestedFieldDefinition(pageDefinition, headerCard.line5),
        line5Right: transformToNestedFieldDefinition(pageDefinition, headerCard.line5Right),
        image: transformToNestedFieldDefinition(pageDefinition, headerCard.image) as
            | NestedField<any, FieldKey.Image>
            | undefined,
    };

    const usedKeys = objectKeys(cardDefinition)
        .map(k => cardDefinition[k]?.properties.bind as string)
        .filter(v => !!v);

    return {
        ...props,
        cardDefinition,
        value: objectKeys(pageDefinition.values).reduce((prevValue, key) => {
            if (usedKeys.includes(key)) {
                return { ...prevValue, [key]: pageDefinition.values[key] };
            }
            return prevValue;
        }, {} as Dict<any>),
    };
};

export const ConnectedPageHeaderCardComponent = connect(mapStateToProps)(PageHeaderCardComponent);
