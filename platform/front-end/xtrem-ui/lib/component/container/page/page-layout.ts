import { objectKeys } from '@sage/xtrem-shared';
import type { PageArticleItem } from '../../../service/layout-types';
import { xtremConsole } from '../../../utils/console';
import { AbstractLayoutBuilder } from '../../abstract-layout-builder';
import type { ContainerControlObjectInstance, ContainerKey, FieldControlObjectInstance } from '../../types';

export interface IPageLayout {
    populateBlocks: () => PageLayout;
    populateSections: () => PageLayout;
}

export class PageLayout extends AbstractLayoutBuilder<ContainerKey.Page> implements IPageLayout {
    buildLayout = (): this => {
        return this.populateSections().populateBlocks().populateFields();
    };

    populateFields = (): this => {
        objectKeys(this.metadata.pageMetadata.fieldThunks).forEach(fieldId => {
            this.metadata.pageMetadata.fieldThunks[fieldId](this.nodeTypes, this.dataTypes);
            const fieldControlObject = this.metadata.pageMetadata.controlObjects[
                fieldId
            ] as FieldControlObjectInstance<any>;

            const insertBeforeField = fieldControlObject?.insertBefore?.apply(this.target.prototype);
            const insertAfterField = fieldControlObject?.insertAfter?.apply(this.target.prototype);

            const insertBefore: string | null = insertBeforeField?.elementId || null;
            const insertAfter: string | null = insertAfterField?.elementId || null;

            const parent = fieldControlObject?.parent?.apply(this.target.prototype)?.layout;

            if (!parent) {
                // If no parent was found for a given field, it is not inserted to the layout structure.
                return;
            }

            [
                ...objectKeys(this.metadata.pageMetadata.layoutBlocks),
                ...this.metadata.pageMetadata.layout.$items.map(i => i.$containerId),
            ].forEach((blockId: any) => {
                if (blockId === parent.$containerId) {
                    fieldControlObject.layout.$containerType = parent.$category;
                    const layoutItems =
                        this.metadata.pageMetadata.layoutBlocks[blockId]?.$layout!.$items ||
                        this.metadata.pageMetadata.layout.$items.find(c => c.$containerId === blockId)!.$layout?.$items;

                    /**
                     * If it is an extension field and the `insertBefore` value is defined, first the index of
                     * insertion is identified and the field is put to the layout array.
                     */
                    if (insertBefore) {
                        const insertLocation = layoutItems.findIndex(f => f.$bind === insertBefore);
                        if (insertLocation !== -1) {
                            layoutItems.splice(insertLocation, 0, fieldControlObject.layout);
                            return;
                        }
                        /**
                         * If the insertBefore field was not found in the same parent as the extension field,
                         * we just display a warning and proceed to add the field to the end of its parent
                         * container.
                         */
                        xtremConsole.warn(
                            `${fieldId} was supposed to be inserted to the ${blockId} block before the ${insertBefore} field`,
                        );
                        xtremConsole.warn('These fields are not defined to be located in the same block.');
                    } else if (insertAfter) {
                        const insertLocation = layoutItems.findIndex(f => f.$bind === insertAfter);
                        if (insertLocation !== -1) {
                            layoutItems.splice(insertLocation + 1, 0, fieldControlObject.layout);
                            return;
                        }
                        /**
                         * If the insertAfter field was not found in the same parent as the extension field,
                         * we just display a warning and proceed to add the field to the end of its parent
                         * container.
                         */
                        xtremConsole.warn(
                            `${fieldId} was supposed to be inserted to the ${blockId} block after the ${insertAfter} field`,
                        );
                        xtremConsole.warn('These fields are not defined to be located in the same block.');
                    }

                    const currentLayoutElementIndex = this.metadata.pageMetadata.definitionOrder.indexOf(
                        fieldControlObject.layout.$bind || (fieldControlObject.layout.$containerId as string),
                    );
                    const existingLayoutMembersIndexes = layoutItems.map(l =>
                        this.metadata.pageMetadata.definitionOrder.indexOf(l.$bind || (l.$containerId as string)),
                    );
                    const positionToInsert = existingLayoutMembersIndexes.findIndex(e => e > currentLayoutElementIndex);
                    if (positionToInsert === -1) {
                        // Add the field to the end of the layout array.
                        layoutItems.push(fieldControlObject.layout);
                    } else {
                        layoutItems.splice(positionToInsert, 0, fieldControlObject.layout);
                    }
                }
            });
        });
        return this;
    };

    populateBlocks = (): this => {
        objectKeys(this.metadata.pageMetadata.blockThunks).forEach(blockId => {
            this.metadata.pageMetadata.blockThunks[blockId](this.nodeTypes, this.dataTypes);
            const blockControlObject = this.metadata.pageMetadata.controlObjects[
                blockId
            ] as FieldControlObjectInstance<any>;

            let insertBefore: string | null = null;
            let insertAfter: string | null = null;
            const insertBeforeBlock = blockControlObject?.insertBefore?.apply(this.target.prototype);
            const insertAfterBlock = blockControlObject?.insertAfter?.apply(this.target.prototype);

            if (insertBeforeBlock) {
                insertBefore = insertBeforeBlock.elementId;
            }
            if (insertAfterBlock) {
                insertAfter = insertAfterBlock.elementId;
            }

            const parent = blockControlObject?.parent?.apply(this.target.prototype);

            if (!parent) {
                // If no parent was found for a given block, it is not inserted to the layout structure.
                return;
            }

            const parentId = parent.layout?.$containerId;

            Object.values(this.metadata.pageMetadata.layout.$items).forEach((section: any) => {
                if (section.$containerId === parentId) {
                    // If dealing with an extension and the `insertBefore` value is defined, find the location where
                    // to insert the declared block's layout.
                    if (insertBefore) {
                        const insertLocation = (section.$layout.$items as Array<Partial<PageArticleItem>>).findIndex(
                            f => f.$containerId === insertBefore,
                        );
                        if (insertLocation > -1) {
                            section.$layout.$items.splice(insertLocation, 0, blockControlObject.layout);
                        } else {
                            xtremConsole.warn(
                                `${blockId} was suppose to be inserted to the ${parentId} parent section before the ${insertBefore} block.`,
                            );
                            xtremConsole.warn('These blocks are not defined to be located in the same parent.');
                        }
                    } else if (insertAfter) {
                        const insertLocation = (section.$layout.$items as Array<Partial<PageArticleItem>>).findIndex(
                            f => f.$containerId === insertAfter,
                        );
                        if (insertLocation !== -1) {
                            section.$layout.$items.splice(insertLocation + 1, 0, blockControlObject.layout);
                        } else {
                            /**
                             * If the insertAfter field was not found in the same parent as the extension field,
                             * we just display a warning and proceed to add the field to the end of its parent
                             * container.
                             */
                            xtremConsole.warn(
                                `${blockId} was suppose to be inserted to the ${parentId} parent section after the ${insertAfter} block.`,
                            );
                            xtremConsole.warn('These blocks are not defined to be located in the same parent.');
                        }
                    } else {
                        // Push declared block's layout to the end if dealing with a regular page.
                        section.$layout.$items.push(blockControlObject.layout);
                    }
                }
            });
        });

        return this;
    };

    populateSections = (): this => {
        objectKeys(this.metadata.pageMetadata.sectionThunks).forEach((sectionId: string) => {
            this.metadata.pageMetadata.sectionThunks[sectionId](this.nodeTypes, this.dataTypes);
        });

        const sectionControlObjects = Object.values(this.metadata.pageMetadata.controlObjects)
            .filter((controlOBject: ContainerControlObjectInstance<any> | FieldControlObjectInstance<any>) => {
                return (controlOBject.layout as any)?.$category === 'section';
            })
            .map(controlObject => {
                return controlObject as ContainerControlObjectInstance<ContainerKey.Section>;
            });

        if (this.metadata.pageMetadata.layout?.$items?.length === sectionControlObjects.length) {
            const layoutItems = (this.metadata.pageMetadata.layout as any)?.$items as Array<Partial<PageArticleItem>>;
            const sortedLayoutItems: Array<Partial<PageArticleItem>> = [];

            sectionControlObjects.forEach(controlObject => {
                const insertBeforeSection = controlObject.insertBefore?.apply(this.target.prototype);

                if (insertBeforeSection?.elementId) {
                    const insertLocation = sortedLayoutItems.findIndex(layoutItem => {
                        return layoutItem.$containerId === insertBeforeSection.elementId;
                    });

                    if (insertLocation > -1) {
                        const layoutItem = layoutItems.find(item => {
                            return item.$containerId === (controlObject as any).elementId;
                        })!;
                        sortedLayoutItems.splice(insertLocation, 0, layoutItem);
                    } else {
                        throw new Error(
                            `Couldn't identify insert location for ${controlObject.id}, it should have been insert before ${insertBeforeSection?.elementId}`,
                        );
                    }
                } else {
                    const layoutItem = layoutItems.find(item => {
                        return item.$containerId === controlObject.layout.$containerId;
                    })!;
                    sortedLayoutItems.push(layoutItem);
                }
            });

            (this.metadata.pageMetadata.layout as any).$items = sortedLayoutItems;
        }

        return this;
    };
}
