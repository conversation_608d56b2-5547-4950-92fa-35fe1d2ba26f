jest.mock('../../../field/text/async-text-component');
jest.mock('../../../field/label/async-label-component');
jest.mock('../../../field/image/async-image-component');

import React from 'react';
import { ConnectedPageHeaderCardComponent } from '../page-header-card';
import {
    createFieldControlObject,
    getMockStore,
    addFieldToState,
    getMockState,
} from '../../../../__tests__/test-helpers/mock-store-helpers';
import { ImageControlObject, LabelControlObject } from '../../../field/field-control-objects';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux';
import { getMockPageDefinition } from '../../../../__tests__/test-helpers';
import type { PageProperties } from '../page-types';
import { FieldKey } from '../../../types';
import * as stateUtils from '../../../../utils/state-utils';
import { render } from '@testing-library/react';

const screenId = 'TestPage';
const labelFieldId = 'labelField';
const labelFieldValue = 'Amazing';
const labelFieldProperties = {};

const imageFieldId = 'imageField';
const imageFieldValue = null;
const imageFieldProperties = {};

let labelField: LabelControlObject;
let imageField: ImageControlObject;
let mockState: XtremAppState;
let mockStore: MockStoreEnhanced<XtremAppState>;

describe('Page header card', () => {
    beforeEach(() => {
        mockState = getMockState();
        mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        const pageProperties: PageProperties = { headerCard: () => ({ title: labelField, image: imageField }) };
        mockState.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] = pageProperties as any;
        labelField = createFieldControlObject(
            FieldKey.Label,
            screenId,
            LabelControlObject,
            labelFieldId,
            labelFieldValue,
            labelFieldProperties,
        ) as any;

        imageField = createFieldControlObject(
            FieldKey.Image,
            screenId,
            ImageControlObject,
            imageFieldId,
            imageFieldValue,
            imageFieldProperties,
        ) as any;

        mockState.browser = { is: { xs: true } } as any;

        addFieldToState(FieldKey.Label, mockState, screenId, labelFieldId, labelFieldProperties, labelFieldValue);
        addFieldToState(FieldKey.Image, mockState, screenId, imageFieldId, imageFieldProperties, imageFieldValue);
        mockStore = getMockStore(mockState);
        jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() =>
            getMockPageDefinition(screenId, mockState.screenDefinitions[screenId]),
        );
    });
    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should render a header card with title containing the corresponding field value', () => {
        const { container } = render(
            <Provider store={mockStore}>
                <ConnectedPageHeaderCardComponent screenId={screenId} />
            </Provider>,
        );
        const titleContent = container.querySelector('.e-card-title')!.textContent;

        expect(titleContent).toContain(labelFieldValue);
    });

    it('should not render a header card on non-small screen', () => {
        mockState.browser = { is: { xs: false } } as any;
        mockStore = getMockStore(mockState);
        const { container } = render(
            <Provider store={mockStore}>
                <ConnectedPageHeaderCardComponent screenId={screenId} />
            </Provider>,
        );

        expect(container.childElementCount).toEqual(0);
    });

    it('should not render if no header card is defined', () => {
        mockState.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] = {};
        mockStore = getMockStore(mockState);
        const { container } = render(
            <Provider store={mockStore}>
                <ConnectedPageHeaderCardComponent screenId={screenId} />
            </Provider>,
        );

        expect(container.childElementCount).toEqual(0);
    });

    it('should render with image if image field provided', () => {
        mockStore = getMockStore(mockState);
        const { container } = render(
            <Provider store={mockStore}>
                <ConnectedPageHeaderCardComponent screenId={screenId} />
            </Provider>,
        );

        const image = container.querySelector('.e-card-image');

        expect(image).not.toBeNull();
    });
});
