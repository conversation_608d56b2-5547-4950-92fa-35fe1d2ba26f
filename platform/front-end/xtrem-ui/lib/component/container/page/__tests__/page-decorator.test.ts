import { getMockPageMetadata, getMockState, getMockStore } from '../../../../__tests__/test-helpers';

import * as dispatchService from '../../../../service/dispatch-service';
import type { Page } from '../../../../service/page';
import * as pageMetadataImport from '../../../../service/page-metadata';
import * as transactionService from '../../../../service/transactions-service';
import { detailPanelId, SectionControlObject } from '../../../control-objects';
import type { DetailPanelDecoratorProperties } from '../../../decorator-properties';
import { block, section, textField } from '../../../decorators';
import { buttonField } from '../../../field/button/button-decorator';
import { pageAction } from '../../../page-action/page-action-decorator';
import type { PageDecoratorProperties } from '../page-decorator';
import { page } from '../page-decorator';
import type { PageNavigationPanel, PageProperties } from '../page-types';
import { nestedFields } from '../../../..';
import type { XtremAppState } from '../../../../redux/state';

const populateBlockThunks = (pageInstance: Page, sectionId: string, blockId: string) => {
    const blockDecorator = block({
        title: 'Test block',
        parent() {
            return pageInstance[sectionId];
        },
    });
    blockDecorator(pageInstance, blockId);
};

const populateSectionThunks = (pageInstance: Page, sectionId: string) => {
    const sectionDecorator = section({
        title: 'Test section',
    });
    sectionDecorator(pageInstance, sectionId);
};

const populateFieldThunks = (
    pageInstance: Page,
    textFieldId: string,
    blockId: string,
    buttonFieldId: string,
    pageActionId: string,
) => {
    const textDecorator = textField({
        title: 'Test field',
        parent() {
            return pageInstance[blockId];
        },
    });
    textDecorator(pageInstance, textFieldId);

    const buttonDecorator = buttonField({
        title: 'Test button',
        parent() {
            return pageInstance[blockId];
        },
        shortcut: ['control', 'c'],
    });
    buttonDecorator(pageInstance, buttonFieldId);

    const pageActionDecorator = pageAction({
        title: 'Test page action',
        shortcut: 'shift',
    });
    pageActionDecorator(pageInstance, pageActionId);
};

describe('Page decorator', () => {
    let decoratorProperties: PageDecoratorProperties<Page>;
    let pageMetadata: pageMetadataImport.PageMetadata;
    const testFieldId = 'test-field';
    const testButtonId = 'test-button-field';
    const testPageActionId = 'test-page-action';
    const testBlockId = 'test-block';
    const testSectionId = 'test-section';
    class TestPage {}
    const pageName = TestPage.name;

    beforeEach(() => {
        getMockStore();
        const pageInstance = new TestPage() as Page;
        pageMetadata = getMockPageMetadata(pageName, { target: pageInstance });
        jest.spyOn(transactionService, 'getUiComponentProperties').mockImplementation(jest.fn());
        jest.spyOn(transactionService, 'setUiComponentProperties').mockImplementation(jest.fn());

        jest.spyOn(dispatchService, 'dispatchContainerValidation').mockImplementation(jest.fn());
        jest.spyOn(pageMetadataImport, 'getPageMetadata').mockImplementation(() => pageMetadata);

        populateSectionThunks(pageInstance, testSectionId);
        populateBlockThunks(pageInstance, testSectionId, testBlockId);
        populateFieldThunks(pageInstance, testFieldId, testBlockId, testButtonId, testPageActionId);

        decoratorProperties = {
            title: 'Page',
            mode: 'default',
            module: 'testModule',
            authorizationCode: 'TAC',
            isHidden: false,
            isDisabled: false,
            isTransient: true,
            node: 'testPage',
            businessActions: () => [],
            detailPanel: () => ({}) as DetailPanelDecoratorProperties,
            onLoad: () => Promise.resolve(),
            onClose: () => {},
            validation: () => {},
            skipDirtyCheck: true,
        };
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('Properties Mapping', () => {
        it('Should map all the decorator properties', () => {
            const decorator = page(decoratorProperties);

            expect(pageMetadata.controlObjects[pageName]).toBeUndefined();
            expect(pageMetadata.businessActionsThunk).toBeUndefined();
            expect(pageMetadata.detailPanelThunk).toBeUndefined();

            decorator(TestPage as any);
            pageMetadata.pageThunk!({}, {});

            expect(pageMetadata.controlObjects[pageName]).toBeDefined();
            expect(pageMetadata.uiComponentProperties[pageName]).toBeDefined();
            expect(pageMetadata.businessActionsThunk).toBeDefined();
            expect(pageMetadata.detailPanelThunk).toBeDefined();

            const decoratorPropertiesKeys = Object.keys(decoratorProperties);
            const decoratorUiPropertiesKey = decoratorPropertiesKeys.filter(
                p => p !== 'businessActions' && p !== 'detailPanel' && p !== 'module' && p !== 'authorizationCode',
            );
            const containerUiProperties = pageMetadata.uiComponentProperties[pageName];

            decoratorUiPropertiesKey.forEach(key => expect(containerUiProperties[key]).toBe(decoratorProperties[key]));

            expect(pageMetadata.layout.$items.length).toBe(1);

            const testSection = pageMetadata.layout.$items[0];
            expect(testSection.$containerId).toBe(testSectionId);
            expect(testSection.$layout!.$items.length).toBe(1);

            const testBlock = testSection.$layout!.$items[0];
            expect(testBlock.$containerId).toBe(testBlockId);
            expect(testBlock.$layout!.$items.length).toBe(2);

            const testField = testBlock.$layout!.$items[0];
            expect(testField.$bind).toBe(testFieldId);
            const testButtonField = testBlock.$layout!.$items[1];
            expect(testButtonField.$bind).toBe(testButtonId);
        });

        it('Should default menuType decorator property when navPanel is defined', () => {
            decoratorProperties.isTransient = false;
            decoratorProperties.node = '@sage/xtrem-test/TestNode';
            decoratorProperties.navigationPanel = {} as PageNavigationPanel<Page>;
            const decorator = page(decoratorProperties);

            decorator(TestPage as any);
            pageMetadata.pageThunk!({}, {});

            expect(
                (pageMetadata.uiComponentProperties[pageName] as PageProperties<Page>).navigationPanel?.menuType,
            ).toBe('dropdown');
        });

        it('Should not default menuType decorator property when it is defined navPanel', () => {
            decoratorProperties.isTransient = false;
            decoratorProperties.node = '@sage/xtrem-test/TestNode';
            decoratorProperties.navigationPanel = { menuType: 'toggle' } as PageNavigationPanel<Page>;
            const decorator = page(decoratorProperties);

            decorator(TestPage as any);
            pageMetadata.pageThunk!({}, {});

            expect(
                (pageMetadata.uiComponentProperties[pageName] as PageProperties<Page>).navigationPanel?.menuType,
            ).toBe('toggle');
        });

        it('should collect elements with short cuts assigned', () => {
            const decorator = page(decoratorProperties);
            expect(pageMetadata.elementsWithShortcut).toEqual([]);

            decorator(TestPage as any);
            pageMetadata.pageThunk!({}, {});

            expect(pageMetadata.elementsWithShortcut).toEqual([
                {
                    elementId: testPageActionId,
                    shortcut: 'shift',
                },
                {
                    elementId: testButtonId,
                    shortcut: ['control', 'c'],
                },
            ]);
        });

        it('should map helper panel properties', () => {
            function TestPage() {} // This function must be named with pageName value
            decoratorProperties.detailPanel = (): DetailPanelDecoratorProperties => {
                return {
                    isDisabled: true,
                    isHidden: true,
                    isTransient: true,
                    isExtendable: true,
                    title: 'Detail Panel Title',
                    header: new SectionControlObject({
                        screenId: pageName,
                        elementId: 'test',
                        getUiComponentProperties: jest.fn(),
                        setUiComponentProperties: jest.fn(),
                        getValidationState: jest.fn(),
                        dispatchSectionValidation: jest.fn(),
                        layout: {},
                    }),
                    sections: [
                        new SectionControlObject({
                            screenId: pageName,
                            elementId: 'test3',
                            getUiComponentProperties: jest.fn(),
                            setUiComponentProperties: jest.fn(),
                            getValidationState: jest.fn(),
                            dispatchSectionValidation: jest.fn(),
                            layout: {},
                        }),
                        new SectionControlObject({
                            screenId: pageName,
                            elementId: 'test2',
                            getUiComponentProperties: jest.fn(),
                            setUiComponentProperties: jest.fn(),
                            getValidationState: jest.fn(),
                            dispatchSectionValidation: jest.fn(),
                            layout: {},
                        }),
                    ],
                    activeSection: 'test3',
                };
            };

            delete decoratorProperties.mode;
            const decorator = page(decoratorProperties);

            expect(pageMetadata.controlObjects[pageName]).toBeUndefined();
            expect(pageMetadata.businessActionsThunk).toBeUndefined();
            expect(pageMetadata.detailPanelThunk).toBeUndefined();

            decorator(TestPage as any);
            pageMetadata.pageThunk!({}, {});

            expect(pageMetadata.controlObjects[pageName]).toBeDefined();
            expect(pageMetadata.uiComponentProperties[pageName]).toBeDefined();
            expect(pageMetadata.businessActionsThunk).toBeDefined();
            expect(pageMetadata.detailPanelThunk).toBeDefined();
            expect(pageMetadata.detailPanelThunk).toBeDefined();

            pageMetadata.detailPanelThunk!({} as Page);
            const detailPanelProperties = pageMetadata.uiComponentProperties[
                detailPanelId
            ] as DetailPanelDecoratorProperties;
            expect(detailPanelProperties.title).toBe('Detail Panel Title');
            expect(detailPanelProperties.isDisabled).toBe(true);
            expect(detailPanelProperties.isHidden).toBe(true);
            expect(detailPanelProperties.isTransient).toBe(true);
            expect(detailPanelProperties.isExtendable).toBe(true);
            expect(detailPanelProperties.activeSection).toEqual('test3');
        });

        describe('printing bulk action', () => {
            let state: XtremAppState;
            beforeEach(() => {
                jest.spyOn(pageMetadataImport, 'getPageMetadata').mockImplementation(() => pageMetadata);
                state = getMockState({
                    printingSettings: {
                        printingAssignmentDialogUrl: '@sage/xtrem-test/ReportAssignmentPage',
                        canAccessPrintingAssignmentDialog: true,
                        listPrintingWizardUrl: '@sage/xtrem-test/ListPrintingPage',
                        canAccessListPrintingWizard: true,
                        recordPrintingWizardUrl: '@sage/xtrem-test/RecordPrintingPage',
                        canAccessRecordPrintingWizard: true,
                        listPrintingGlobalMutationConfigPage: '@sage/xtrem-test/ListPrintingGlobalMutationConfigPage',
                        recordPrintingGlobalBulkMutationName: 'globalPrintRecords',
                    },
                });
                decoratorProperties.navigationPanel = {
                    listItem: {
                        title: nestedFields.text({ bind: 'name' }),
                    },
                    bulkActions: [],
                };
                decoratorProperties.node = '@sage/xtrem-test/TestNode';
                decoratorProperties.isTransient = false;
            });

            it('should add printing bulk action if the printing manager is active and has templates allocated', () => {
                pageMetadata = { ...pageMetadata, hasRecordPrintingTemplates: true };
                jest.spyOn(pageMetadataImport, 'getPageMetadata').mockImplementation(() => pageMetadata);

                getMockStore(state);

                const decorator = page(decoratorProperties);

                decorator(TestPage as any);
                pageMetadata.pageThunk!({}, {});
                expect(
                    (pageMetadata.uiComponentProperties[pageName] as PageProperties).navigationPanel?.bulkActions,
                ).toEqual([
                    {
                        title: 'Print',
                        buttonType: 'tertiary',
                        configurationPage: '@sage/xtrem-test/ListPrintingGlobalMutationConfigPage',
                        icon: 'print',
                        isGlobal: true,
                        mutation: 'globalPrintRecords',
                    },
                ]);
            });

            it('should not add printing bulk action if the printing manager is inactive', () => {
                pageMetadata = { ...pageMetadata, hasRecordPrintingTemplates: true };
                jest.spyOn(pageMetadataImport, 'getPageMetadata').mockImplementation(() => pageMetadata);
                state.printingSettings = null;
                getMockStore(state);

                const decorator = page(decoratorProperties);

                decorator(TestPage as any);
                pageMetadata.pageThunk!({}, {});
                expect(
                    (pageMetadata.uiComponentProperties[pageName] as PageProperties).navigationPanel?.bulkActions,
                ).toHaveLength(0);
            });

            it('should not add the print bulk action if no templates are allocated', () => {
                pageMetadata = { ...pageMetadata, hasRecordPrintingTemplates: false };
                jest.spyOn(pageMetadataImport, 'getPageMetadata').mockImplementation(() => pageMetadata);

                getMockStore(state);

                const decorator = page(decoratorProperties);

                decorator(TestPage as any);
                pageMetadata.pageThunk!({}, {});
                expect(
                    (pageMetadata.uiComponentProperties[pageName] as PageProperties).navigationPanel?.bulkActions,
                ).toHaveLength(0);
            });
        });
    });
});
