const hasNextRecordMock = jest.fn();
const hasPreviousRecordMock = jest.fn();
const nextRecordMock = jest.fn();
const previousRecordMock = jest.fn();

jest.mock('../../../../service/router', () => ({
    getRouter: () => ({
        hasNextRecord: hasNextRecordMock,
        hasPreviousRecord: hasPreviousRecordMock,
        nextRecord: nextRecordMock,
        previousRecord: previousRecordMock,
    }),
}));

import * as React from 'react';
import { render, waitFor, fireEvent } from '@testing-library/react';
import { HeaderNavigationArrows } from '../header-navigation-arrows';

describe('header navigation arrows', () => {
    const screenId = 'TestPage';

    beforeEach(() => {
        hasNextRecordMock.mockResolvedValue(false);
        hasPreviousRecordMock.mockResolvedValue(false);
        nextRecordMock.mockResolvedValue(false);
        previousRecordMock.mockResolvedValue(false);
    });

    describe('button state', () => {
        it("should render with disabled arrows if the page doesn't have next or previous record", async () => {
            const result = render(<HeaderNavigationArrows screenId={screenId} />);
            await waitFor(() => {
                expect(result.queryByTestId('e-header-navigation-arrow-previous')).toHaveAttribute('disabled');
                expect(result.queryByTestId('e-header-navigation-arrow-next')).toHaveAttribute('disabled');
            });
        });

        it('should render with enabled previous arrow if there is a previous record', async () => {
            hasPreviousRecordMock.mockResolvedValue(true);

            const result = render(<HeaderNavigationArrows screenId={screenId} />);
            await waitFor(() => {
                expect(result.queryByTestId('e-header-navigation-arrow-previous')).not.toHaveAttribute('disabled');
                expect(result.queryByTestId('e-header-navigation-arrow-next')).toHaveAttribute('disabled');
            });
        });

        it('should render with enabled next arrow if there is a next record', async () => {
            hasNextRecordMock.mockResolvedValue(true);

            const result = render(<HeaderNavigationArrows screenId={screenId} />);
            await waitFor(() => {
                expect(result.queryByTestId('e-header-navigation-arrow-previous')).toHaveAttribute('disabled');
                expect(result.queryByTestId('e-header-navigation-arrow-next')).not.toHaveAttribute('disabled');
            });
        });

        it('should enable both buttons if there are next and prev records', async () => {
            hasNextRecordMock.mockResolvedValue(true);
            hasPreviousRecordMock.mockResolvedValue(true);

            const result = render(<HeaderNavigationArrows screenId={screenId} />);
            await waitFor(() => {
                expect(result.queryByTestId('e-header-navigation-arrow-previous')).not.toHaveAttribute('disabled');
                expect(result.queryByTestId('e-header-navigation-arrow-next')).not.toHaveAttribute('disabled');
            });
        });
    });

    describe('interactions', () => {
        beforeEach(() => {
            hasNextRecordMock.mockResolvedValue(true);
            hasPreviousRecordMock.mockResolvedValue(true);

            nextRecordMock.mockClear();
            previousRecordMock.mockClear();
        });

        it('should navigate to the next record when the next arrow is clicked', async () => {
            const result = render(<HeaderNavigationArrows screenId={screenId} />);

            await waitFor(() => {
                expect(result.queryByTestId('e-header-navigation-arrow-previous')).not.toHaveAttribute('disabled');
                expect(result.queryByTestId('e-header-navigation-arrow-next')).not.toHaveAttribute('disabled');
            });

            expect(nextRecordMock).not.toHaveBeenCalled();
            expect(previousRecordMock).not.toHaveBeenCalled();
            fireEvent.click(result.queryByTestId('e-header-navigation-arrow-next')!);

            await waitFor(() => {
                expect(nextRecordMock).toHaveBeenCalled();
                expect(previousRecordMock).not.toHaveBeenCalled();
            });
        });

        it('should navigate to the previous record when the previous arrow is clicked', async () => {
            const result = render(<HeaderNavigationArrows screenId={screenId} />);

            await waitFor(() => {
                expect(result.queryByTestId('e-header-navigation-arrow-previous')).not.toHaveAttribute('disabled');
                expect(result.queryByTestId('e-header-navigation-arrow-next')).not.toHaveAttribute('disabled');
            });

            expect(nextRecordMock).not.toHaveBeenCalled();
            expect(previousRecordMock).not.toHaveBeenCalled();
            fireEvent.click(result.queryByTestId('e-header-navigation-arrow-previous')!);

            await waitFor(() => {
                expect(nextRecordMock).not.toHaveBeenCalled();
                expect(previousRecordMock).toHaveBeenCalled();
            });
        });
    });
});
