import { getMockPageMetadata } from '../../../../__tests__/test-helpers';
import type { Page } from '../../../../service/page';
import * as pageMetadataImport from '../../../../service/page-metadata';
import * as targetPrototypeImport from '../../../../utils/decorator-utils';
import type { PageFragmentDecoratorProperties } from '../../../decorators';
import { pageFragment } from '../page-fragment-decorator';
import type { PageEventType } from '../page-types';

describe('pageFragment decorator', () => {
    let decoratorProperties: PageFragmentDecoratorProperties<Page>;
    let pageMetadata;
    class TestPage {}

    const pageName = TestPage.name;
    const onLoad: PageEventType<TestPage> = () => {};

    beforeEach(() => {
        pageMetadata = getMockPageMetadata(pageName);
        jest.spyOn(pageMetadataImport, 'getPageMetadata').mockImplementation(() => pageMetadata);
        jest.spyOn(targetPrototypeImport, 'getTargetPrototype').mockImplementation(ctor => ctor);
        decoratorProperties = {
            onLoad,
        };
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('Should return a function', () => {
        const result = pageFragment(decoratorProperties);
        expect(typeof result).toBe('function');
    });

    it('Should push a function to pageMetadata.pageFragmentThunks', () => {
        pageFragment(decoratorProperties)(TestPage as any);
        expect(pageMetadata.pageFragmentThunks.length).toBe(1);
        expect(typeof pageMetadata.pageFragmentThunks[0]).toBe('function');
    });

    it('Should add onLoad to onLoadAfter in uiComponentProperties and defaultUiComponentProperties when onLoad is provided', () => {
        pageFragment(decoratorProperties)(TestPage as any);
        pageMetadata.pageFragmentThunks[0]();
        expect(pageMetadata.uiComponentProperties[pageMetadata.screenId].onLoadAfter).toContain(onLoad);
    });

    it('Should not add onLoad to onLoadAfter in uiComponentProperties and defaultUiComponentProperties when onLoad is not provided', () => {
        decoratorProperties.onLoad = undefined;
        pageFragment(decoratorProperties)(TestPage as any);
        pageMetadata.pageFragmentThunks[0]();
        expect(pageMetadata.uiComponentProperties[pageMetadata.screenId].onLoadAfter).toBeUndefined();
    });
});
