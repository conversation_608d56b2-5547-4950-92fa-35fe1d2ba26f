import * as React from 'react';
import { fireEvent, render, waitFor } from '@testing-library/react';
import {
    addPageActionToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';
import type { XtremAppState } from '../../../../redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { ConnectedHeaderQuickActions } from '../header-quick-actions';
import { Provider } from 'react-redux';
import { getPagePropertiesFromState } from '../../../../utils/state-utils';
import * as events from '../../../../utils/events';
import type { PageDefinition } from '../../../../service/page-definition';

describe('Header quick actions', () => {
    let state: XtremAppState;
    let mockStore: MockStoreEnhanced<XtremAppState>;
    const screenId = 'TestScreen';

    beforeEach(() => {
        state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId, {});
    });

    it('should render just fine with a single action', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', { title: 'Test', icon: 'add' });
        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerQuickActions = () => [testAction];
        mockStore = getMockStore(state);

        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderQuickActions screenId={screenId} />
            </Provider>,
        );

        expect(
            queryByTestId('e-header-quick-action-label-test')?.attributes.getNamedItem('aria-label')?.textContent,
        ).toEqual('Test');
    });

    it('should render empty if no actions defined', () => {
        mockStore = getMockStore(state);
        const { baseElement } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderQuickActions screenId={screenId} />
            </Provider>,
        );

        expect(baseElement.querySelector('.e-header-quick-action-container')).toBeNull();
    });

    it('should throw an error if no icon is supplied', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', { title: 'Test' });
        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerQuickActions = () => [testAction];
        mockStore = getMockStore(state);

        expect(() =>
            render(
                <Provider store={mockStore}>
                    <ConnectedHeaderQuickActions screenId={screenId} />
                </Provider>,
            ),
        ).toThrow('Quick actions must have a valid icon value. Screen Id: TestScreen, Action Id: testAction');
    });

    it('should render action button if its disabled by a callback', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', {
            title: 'Test',
            icon: 'add',
            isDisabled: () => true,
        });
        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerQuickActions = () => [testAction];
        mockStore = getMockStore(state);
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderQuickActions screenId={screenId} />
            </Provider>,
        );

        expect(queryByTestId('e-header-quick-action-label-test')?.attributes.getNamedItem('disabled')).not.toBeNull();
    });

    it('should render action button if its disabled', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', {
            title: 'Test',
            icon: 'add',
            isDisabled: true,
        });
        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerQuickActions = () => [testAction];
        mockStore = getMockStore(state);
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderQuickActions screenId={screenId} />
            </Provider>,
        );

        expect(queryByTestId('e-header-quick-action-label-test')?.attributes.getNamedItem('disabled')).not.toBeNull();
    });

    it('should not render the action button if it is hidden by a callback', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', {
            title: 'Test',
            icon: 'add',
            isHidden: () => true,
        });
        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerQuickActions = () => [testAction];
        mockStore = getMockStore(state);
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderQuickActions screenId={screenId} />
            </Provider>,
        );

        expect(queryByTestId('e-header-quick-action-label-test')!).toBeNull();
    });

    it('should not render the action button if it is hidden', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', {
            title: 'Test',
            icon: 'add',
            isHidden: true,
        });
        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerQuickActions = () => [testAction];
        mockStore = getMockStore(state);
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderQuickActions screenId={screenId} />
            </Provider>,
        );

        expect(queryByTestId('e-header-quick-action-label-test')!).toBeNull();
    });

    it('should not render the action button if it is the user does not have access rights', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', {
            title: 'Test',
            icon: 'add',
            access: {
                bind: 'someProperty',
                node: '@sage/xtrem-test/MyNode',
            },
        });
        (state.screenDefinitions[screenId] as PageDefinition).accessBindings = {
            MyNode: {
                someProperty: 'unauthorized',
            },
        };
        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerQuickActions = () => [testAction];
        mockStore = getMockStore(state);
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderQuickActions screenId={screenId} />
            </Provider>,
        );

        expect(queryByTestId('e-header-quick-action-label-test')!).toBeNull();
    });

    it('should not render the action button if the action has access definition but the corresponding rights are not available', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', {
            title: 'Test',
            icon: 'add',
            access: {
                bind: 'someProperty',
                node: '@sage/xtrem-test/MyNode',
            },
        });

        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerQuickActions = () => [testAction];
        mockStore = getMockStore(state);
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderQuickActions screenId={screenId} />
            </Provider>,
        );

        expect(queryByTestId('e-header-quick-action-label-test')!).toBeNull();
    });

    it('should trigger the action if the button is clicked', async () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', { title: 'Test', icon: 'add' });
        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerQuickActions = () => [testAction];
        mockStore = getMockStore(state);
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderQuickActions screenId={screenId} />
            </Provider>,
        );

        expect(events.triggerFieldEvent).not.toHaveBeenCalled();

        fireEvent.click(queryByTestId('e-header-quick-action-label-test')!);
        await waitFor(() => {
            expect(events.triggerFieldEvent).toHaveBeenCalled();
        });
    });
});
