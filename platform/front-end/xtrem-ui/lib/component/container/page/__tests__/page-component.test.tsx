import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import {
    addPageControlObject,
    addSectionToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';
import { ActionType, type XtremAppState } from '../../../../redux';
import type { Page } from '../../../../service/page';
import type { PageDefinition } from '../../../../service/page-definition';
import { ContextType } from '../../../../types';
import * as Dom from '../../../../utils/dom';
import PageComponent from '../page-component';
import * as shortcutService from '../../../../service/shortcut-service';
import type { DetailPanelProperties, PageProperties } from '../../container-properties';
import type { IDetailPanelControlObject } from '../../detail-panel/detail-panel-control-object';
import { DetailPanelControlObject, detailPanelId } from '../../detail-panel/detail-panel-control-object';
import type { PageArticleItem } from '../../../../service/layout-types';
import { createNavigationPanelValue } from '../../../../service/navigation-panel-service';
import { cleanup, render } from '@testing-library/react';

describe('Page component', () => {
    const screenId = 'TestPage';
    let state: XtremAppState;
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let pageProperties: PageProperties<Page>;
    let pageDefinition: PageDefinition;

    const getPageComponent = (contextType: ContextType = ContextType.page): React.ReactElement => {
        return (
            <Provider store={mockStore}>
                <PageComponent pageDefinition={pageDefinition} contextType={contextType} availableColumns={12} />
            </Provider>
        );
    };

    beforeEach(() => {
        jest.spyOn(shortcutService, 'subscribe').mockImplementation(jest.fn());
        jest.spyOn(shortcutService, 'unsubscribe').mockImplementation(jest.fn());

        state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(
            screenId,
            {},
            {
                elementsWithShortcut: [
                    {
                        elementId: 'aPageActionId',
                        shortcut: 'shift',
                    },
                    {
                        elementId: 'aButtonId',
                        shortcut: ['control', 'c'],
                    },
                ],
            },
        );

        pageDefinition = state.screenDefinitions[screenId] as PageDefinition;
        pageProperties = {
            title: 'Test page title',
            mode: 'default',
        };

        addSectionToState(state, screenId, 'section1');
        addSectionToState(state, screenId, 'section2');
        addPageControlObject(state, screenId, pageProperties);

        mockStore = getMockStore(state);
    });

    afterEach(() => {
        cleanup();
        jest.clearAllMocks();
    });

    describe('Snapshots', () => {
        const renderAndMatchSnapshot = (): any => {
            const result = render(getPageComponent());
            expect(result.container).toMatchSnapshot();
            return result;
        };

        it('should render when using wizard page mode', () => {
            pageProperties.mode = 'wizard';
            renderAndMatchSnapshot();
        });

        it('should render with 12 columns on large screen if the helper panel is closed', () => {
            const { container } = render(getPageComponent());
            expect(container.querySelector('.e-section-body')!).toHaveClass('e-grid-row-12');
        });

        it('should render with 8 columns on large screen if the helper panel is open', () => {
            state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(
                screenId,
                {},
                {
                    elementsWithShortcut: [
                        {
                            elementId: 'aPageActionId',
                            shortcut: 'shift',
                        },
                        {
                            elementId: 'aButtonId',
                            shortcut: ['control', 'c'],
                        },
                    ],
                },
            );

            pageProperties = {
                title: 'Test page title',
                mode: 'default',
            };

            const detailPanelControlObjectProps: Partial<IDetailPanelControlObject> = {
                screenId,
                elementId: detailPanelId,
                getUiComponentProperties: jest.fn(),
                setUiComponentProperties: jest.fn(),
                layout: {
                    detailPanelHeaderLayout: {} as Partial<PageArticleItem>,
                    detailPanelSectionsLayout: [] as Partial<PageArticleItem>[],
                },
            };

            const detailPanel = new DetailPanelControlObject({
                ...detailPanelControlObjectProps,

                layout: {
                    detailPanelHeaderLayout: {} as Partial<PageArticleItem>,
                    detailPanelSectionsLayout: [] as Partial<PageArticleItem>[],
                },
            } as IDetailPanelControlObject);

            pageDefinition = state.screenDefinitions[screenId] as PageDefinition;
            pageDefinition.page = {
                ...pageDefinition.page,
                getSerializedValues: jest.fn(),
                _pageMetadata: {
                    detailPanelThunk: () => detailPanel,
                } as any,
                $detailPanel: detailPanel,
            } as Page;

            pageDefinition.metadata.uiComponentProperties[detailPanelId] = {
                isHidden: false,
                activeSection: 'section2',
            } as DetailPanelProperties;

            addSectionToState(state, screenId, 'section1');
            addSectionToState(state, screenId, 'section2');
            addPageControlObject(state, screenId, pageProperties);

            mockStore = getMockStore(state);

            const { container } = render(getPageComponent());
            expect(container.querySelector('.e-section-body')!).toHaveClass('e-grid-row-8');
        });

        it('should not render the helper panel if opened as a dialog', () => {
            state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(
                screenId,
                {},
                {
                    elementsWithShortcut: [
                        {
                            elementId: 'aPageActionId',
                            shortcut: 'shift',
                        },
                        {
                            elementId: 'aButtonId',
                            shortcut: ['control', 'c'],
                        },
                    ],
                },
            );

            pageProperties = {
                title: 'Test page title',
                mode: 'default',
            };

            const detailPanelControlObjectProps: Partial<IDetailPanelControlObject> = {
                screenId,
                elementId: detailPanelId,
                getUiComponentProperties: jest.fn(),
                setUiComponentProperties: jest.fn(),
                layout: {
                    detailPanelHeaderLayout: {} as Partial<PageArticleItem>,
                    detailPanelSectionsLayout: [] as Partial<PageArticleItem>[],
                },
            };

            const detailPanel = new DetailPanelControlObject({
                ...detailPanelControlObjectProps,

                layout: {
                    detailPanelHeaderLayout: {} as Partial<PageArticleItem>,
                    detailPanelSectionsLayout: [] as Partial<PageArticleItem>[],
                },
            } as IDetailPanelControlObject);

            pageDefinition = state.screenDefinitions[screenId] as PageDefinition;
            pageDefinition.page = {
                ...pageDefinition.page,
                getSerializedValues: jest.fn(),
                _pageMetadata: {
                    detailPanelThunk: () => detailPanel,
                } as any,
                $detailPanel: detailPanel,
            } as Page;

            pageDefinition.metadata.uiComponentProperties[detailPanelId] = {
                isHidden: false,
                activeSection: 'section2',
            } as DetailPanelProperties;

            addSectionToState(state, screenId, 'section2');
            addPageControlObject(state, screenId, pageProperties);

            mockStore = getMockStore(state);

            const { container } = render(getPageComponent(ContextType.dialog));
            expect(container.querySelector('.e-detail-panel')).toBeNull();
        });

        it('should not render navigation panel it is rendered in a dialog even in a large screen', () => {
            state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(
                screenId,
                {},
                {
                    elementsWithShortcut: [
                        {
                            elementId: 'aPageActionId',
                            shortcut: 'shift',
                        },
                        {
                            elementId: 'aButtonId',
                            shortcut: ['control', 'c'],
                        },
                    ],
                },
            );

            pageProperties = {
                title: 'Test page title',
                mode: 'default',
                navigationPanel: {
                    listItem: {} as any,
                },
            };

            const detailPanelControlObjectProps: Partial<IDetailPanelControlObject> = {
                screenId,
                elementId: detailPanelId,
                getUiComponentProperties: jest.fn(),
                setUiComponentProperties: jest.fn(),
                layout: {
                    detailPanelHeaderLayout: {} as Partial<PageArticleItem>,
                    detailPanelSectionsLayout: [] as Partial<PageArticleItem>[],
                },
            };

            const detailPanel = new DetailPanelControlObject({
                ...detailPanelControlObjectProps,

                layout: {
                    detailPanelHeaderLayout: {} as Partial<PageArticleItem>,
                    detailPanelSectionsLayout: [] as Partial<PageArticleItem>[],
                },
            } as IDetailPanelControlObject);

            pageDefinition = state.screenDefinitions[screenId] as PageDefinition;
            pageDefinition.isInDialog = true;
            pageDefinition.navigationPanel = {
                isHidden: false,
                isOpened: true,
                isHeaderHidden: false,
                isRefreshing: false,
                value: createNavigationPanelValue(
                    screenId,
                    {
                        columns: [],
                    },
                    [],
                    {},
                ),
            };
            pageDefinition.page = {
                ...pageDefinition.page,
                getSerializedValues: jest.fn(),
                _pageMetadata: {
                    detailPanelThunk: () => detailPanel,
                } as any,
                $detailPanel: detailPanel,
            } as Page;

            pageDefinition.metadata.uiComponentProperties[detailPanelId] = {
                isHidden: false,
                activeSection: 'section2',
            } as DetailPanelProperties;

            addSectionToState(state, screenId, 'section1');
            addSectionToState(state, screenId, 'section2');
            addPageControlObject(state, screenId, pageProperties);
            state.browser.greaterThan.l = true;
            mockStore = getMockStore(state);

            const { container } = render(getPageComponent());
            expect(container.querySelector('.e-page-navigation-panel-open')).toBeNull();
        });

        it('should always render navigation panel it is rendered a large screen', () => {
            state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(
                screenId,
                {},
                {
                    elementsWithShortcut: [
                        {
                            elementId: 'aPageActionId',
                            shortcut: 'shift',
                        },
                        {
                            elementId: 'aButtonId',
                            shortcut: ['control', 'c'],
                        },
                    ],
                },
            );

            pageProperties = {
                title: 'Test page title',
                mode: 'default',
                navigationPanel: {
                    listItem: {} as any,
                },
            };

            const detailPanelControlObjectProps: Partial<IDetailPanelControlObject> = {
                screenId,
                elementId: detailPanelId,
                getUiComponentProperties: jest.fn(),
                setUiComponentProperties: jest.fn(),
                layout: {
                    detailPanelHeaderLayout: {} as Partial<PageArticleItem>,
                    detailPanelSectionsLayout: [] as Partial<PageArticleItem>[],
                },
            };

            const detailPanel = new DetailPanelControlObject({
                ...detailPanelControlObjectProps,

                layout: {
                    detailPanelHeaderLayout: {} as Partial<PageArticleItem>,
                    detailPanelSectionsLayout: [] as Partial<PageArticleItem>[],
                },
            } as IDetailPanelControlObject);

            pageDefinition = state.screenDefinitions[screenId] as PageDefinition;
            pageDefinition.isInDialog = false;
            pageDefinition.navigationPanel = {
                isHidden: false,
                isOpened: true,
                isHeaderHidden: false,
                isRefreshing: false,
                value: createNavigationPanelValue(
                    screenId,
                    {
                        columns: [],
                    },
                    [],
                    {},
                ),
            };
            pageDefinition.page = {
                ...pageDefinition.page,
                getSerializedValues: jest.fn(),
                _pageMetadata: {
                    detailPanelThunk: () => detailPanel,
                } as any,
                $detailPanel: detailPanel,
            } as Page;

            pageDefinition.metadata.uiComponentProperties[detailPanelId] = {
                isHidden: false,
                activeSection: 'section2',
            } as DetailPanelProperties;

            addSectionToState(state, screenId, 'section1');
            addSectionToState(state, screenId, 'section2');
            addPageControlObject(state, screenId, pageProperties);
            state.browser.greaterThan.l = true;
            mockStore = getMockStore(state);

            const { container } = render(getPageComponent());
            expect(container.querySelector('.e-page-navigation-panel-open')).not.toBeNull();
        });
    });

    describe('interactions', () => {
        it('should subscribe to key shortcut subscriptions when rendered', () => {
            expect(shortcutService.subscribe).not.toHaveBeenCalled();
            const wrapper = render(<Provider store={mockStore}>{getPageComponent()}</Provider>);
            expect(shortcutService.subscribe).toHaveBeenCalled();
            expect(shortcutService.unsubscribe).not.toHaveBeenCalled();
            wrapper.unmount();
            expect(shortcutService.unsubscribe).toHaveBeenCalled();
        });

        it('should focus on mount', () => {
            const focus = jest.spyOn(Dom, 'focusTopPage');
            expect(focus).toHaveBeenCalledTimes(0);

            render(<Provider store={mockStore}>{getPageComponent()}</Provider>);
            expect(focus).toHaveBeenCalledTimes(1);
        });
    });

    describe('PageComponent 360 view in Url', () => {
        const screenId = 'TestPage';
        let mockStore: MockStoreEnhanced<XtremAppState>;
        let state: XtremAppState;

        beforeEach(() => {
            state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            mockStore = getMockStore(state, false);
        });

        afterEach(() => {
            cleanup();
            jest.restoreAllMocks();
        });

        it('should activate 360 view if state.path contains view param', () => {
            pageDefinition = state.screenDefinitions[screenId] as PageDefinition;
            pageDefinition.is360ViewOn = false;
            state.path = `@sage/xtrem-master-data/Customer/${btoa(JSON.stringify({ _id: '1234', view: '360' }))}`;
            render(
                <Provider store={mockStore}>
                    <PageComponent
                        pageDefinition={pageDefinition}
                        contextType={ContextType.page}
                        availableColumns={12}
                    />
                </Provider>,
            );

            expect(mockStore.getActions()).toEqual([
                {
                    type: ActionType.Set360ViewState,
                    value: {
                        screenId: 'TestPage',
                        state: true,
                    },
                },
            ]);
        });

        it('should deactivate 360 view if state.path does not contain view param', () => {
            pageDefinition = state.screenDefinitions[screenId] as PageDefinition;
            state.path = `@sage/xtrem-master-data/Customer/${btoa(JSON.stringify({ _id: '1234' }))}`;
            pageDefinition.is360ViewOn = true;
            render(
                <Provider store={mockStore}>
                    <PageComponent
                        pageDefinition={pageDefinition}
                        contextType={ContextType.page}
                        availableColumns={12}
                    />
                </Provider>,
            );

            expect(mockStore.getActions()).toEqual([
                { type: ActionType.RemoveDashboardGroup, value: 'TestPage-360' },
                {
                    type: ActionType.Set360ViewState,
                    value: {
                        screenId: 'TestPage',
                        state: false,
                    },
                },
            ]);
        });
    });
});
