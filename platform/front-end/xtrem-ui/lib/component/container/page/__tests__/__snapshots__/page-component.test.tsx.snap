// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Page component Snapshots should render when using wizard page mode 1`] = `
.c1 {
  padding-left: 24px;
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: var(--colorsActionMajor500);
  border-color: transparent;
  color: var(--colorsActionMajorYang100);
  font-size: var(--fontSizes100);
  min-height: 40px;
}

.c1:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c1:hover {
  background: var(--colorsActionMajor600);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c0 {
  width: 0;
  height: 0;
  overflow: hidden;
  border: none;
  padding: 0;
  margin: 0;
  opacity: 0;
}

<div>
  <div
    class="e-page e-page-mode-wizard e-page-page e-page-has-footer"
    data-pendo-page="TestPage"
    data-pendo-page-mode="ML"
    data-testid="e-page"
  >
    <main
      class="e-page-main-section"
    >
      <header
        class="e-header e-header-context-page e-no-tabs"
        data-testid="e-header"
      >
        <div
          class="e-header-title-row"
        >
          <div
            class="e-header-title-col"
          >
            <div
              class="e-header-title-container"
            >
              <div>
                <h1
                  class="e-header-title"
                >
                  Test page title
                </h1>
              </div>
            </div>
          </div>
        </div>
      </header>
      <div
        class="e-page-body-container"
      >
        <div
          class="e-page-body e-page-body-context-page"
          data-testid="e-page-body"
        >
          <section
            class="e-section e-section-context-page"
            data-testid="e-section-field e-field-bind-section1"
            id="section1"
          >
            <div
              class="e-section-header"
            >
              <span
                class="e-section-title"
              />
            </div>
            <div
              class="e-grid-row e-grid-row-12 e-section-body"
              style="grid-template-columns: repeat(12, 1fr); padding: 0px 0px 0px 0px;"
            />
          </section>
        </div>
        <span
          class="e-page-footer-container e-page-footer-context-page"
        >
          <div
            class="e-page-footer"
          >
            <button
              aria-hidden="true"
              class="c0"
            />
            <div
              class="e-page-footer-wizard-button-wrapper"
            >
              <div
                class="e-page-footer-wizard-button"
              >
                <span
                  class="e-business-action"
                  data-testid="e-business-action-field e-field-label-next"
                >
                  <button
                    class="c1"
                    data-component="button"
                    draggable="false"
                    type="button"
                  >
                    <span>
                      <span
                        class="c2"
                        data-element="main-text"
                      >
                        Next
                      </span>
                    </span>
                  </button>
                </span>
              </div>
            </div>
          </div>
        </span>
      </div>
    </main>
  </div>
</div>
`;
