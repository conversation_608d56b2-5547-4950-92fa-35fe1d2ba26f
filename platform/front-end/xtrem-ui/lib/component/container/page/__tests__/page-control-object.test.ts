import type { Page } from '../../../../service/page';
import { PageControlObject } from '../../../control-objects';
import type { PageProperties } from '../../container-properties';

describe('Page control object', () => {
    const pageId = 'TestPage';
    let page: PageControlObject;
    let pageProperties: PageProperties<Page>;
    let dispatchValidationMock: jest.Mock<Promise<any>>;

    beforeEach(() => {
        pageProperties = {
            mode: 'wizard',
            node: 'GraphQlObject',
            navigationPanel: {} as any,
        };
        const getPropertiesMock = jest.fn(() => ({
            ...pageProperties,
            dispatchPageValidation: () => Promise.resolve([]),
            target: 'target',
            layout: null,
        }));
        const setPropertiesMock = jest.fn((_screenId: string, elementId: string, value: any) => {
            pageProperties = { ...value };
        });
        dispatchValidationMock = jest.fn(() =>
            Promise.resolve({
                allErrors: [
                    {
                        screenId: pageId,
                        elementId: 'someElement',
                        message: 'Validation message',
                        validationRule: 'validation',
                    },
                ],
                blockingErrors: [
                    {
                        screenId: pageId,
                        elementId: 'someElement',
                        message: 'Validation message',
                        validationRule: 'validation',
                    },
                ],
            }),
        );
        page = new PageControlObject({
            dispatchPageValidation: dispatchValidationMock,
            getUiComponentProperties: getPropertiesMock,
            layout: null,
            screenId: pageId,
            setUiComponentProperties: setPropertiesMock,
            getFocussedField: jest.fn(),
        });
    });

    it('should retrieve mode from properties', () => {
        expect(page.mode).toBe(pageProperties.mode);
    });

    it('should retrieve navigationPanel from properties', () => {
        expect(page.navigationPanel).toEqual(pageProperties.navigationPanel);
    });

    it('should retrieve node from properties', () => {
        expect(page.node).toBe(pageProperties.node);
    });

    it('should call dispatchValidation function on validate', async () => {
        expect(dispatchValidationMock).not.toHaveBeenCalled();
        const result = await page.validate();
        expect(dispatchValidationMock).toHaveBeenCalledTimes(1);
        expect(result).toEqual(['Validation message']);
    });
    it('should call dispatchValidation function on validateWithDetails', async () => {
        expect(dispatchValidationMock).not.toHaveBeenCalled();
        const result = await page.validateWithDetails();
        expect(dispatchValidationMock).toHaveBeenCalledTimes(1);
        expect(result).toEqual([
            {
                screenId: pageId,
                elementId: 'someElement',
                message: 'Validation message',
                validationRule: 'validation',
            },
        ]);
    });

    it('should set the title', () => {
        const testFixture = 'Test page title update value';
        expect(page.title).not.toEqual(testFixture);
        page.title = testFixture;
        expect(page.title).toEqual(testFixture);
    });

    it('should set the subtitle', () => {
        const testFixture = 'Test page subtitle update value';
        expect(page.subtitle).not.toEqual(testFixture);
        page.subtitle = testFixture;
        expect(page.subtitle).toEqual(testFixture);
    });
});
