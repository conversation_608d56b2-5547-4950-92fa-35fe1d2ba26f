import * as React from 'react';
import { render } from '@testing-library/react';
import {
    addPageActionToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';
import type { XtremAppState } from '../../../../redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { Provider } from 'react-redux';
import { getPagePropertiesFromState } from '../../../../utils/state-utils';
import type { PageDefinition } from '../../../../service/page-definition';
import { ConnectedHeaderDropdownActions } from '../header-dropdown-actions';
import '@testing-library/jest-dom';

jest.mock('carbon-react/esm/components/action-popover', () => {
    return {
        ...jest.requireActual('carbon-react/esm/components/action-popover'),
        ActionPopover: ({ children, ...props }: { children: any }) => (
            <div data-testid="e-action-popover-button-mock" data-component="action-popover-button" {...props}>
                {children}
            </div>
        ),
        ActionPopoverItem: ({ children, ...props }: { children: any }) => (
            <div data-testid="e-popover-action-single-button-mock" {...props}>
                {children}
            </div>
        ),
    };
});

describe('Header dropdown actions', () => {
    let state: XtremAppState;
    let mockStore: MockStoreEnhanced<XtremAppState>;
    const screenId = 'TestScreen';

    beforeEach(() => {
        state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId, {});
    });

    afterAll(() => {
        jest.clearAllMocks();
    });

    it('should render just fine with a single action', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', { title: 'Test', icon: 'add' });
        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerDropDownActions = (): any => [testAction];
        mockStore = getMockStore(state);

        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderDropdownActions screenId={screenId} />
            </Provider>,
        );

        expect(queryByTestId('e-popover-action-single-button')).toHaveAttribute('aria-label', 'Test');
    });
    it('should render just with two actions', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', { title: 'Test', icon: 'add' });
        const testAction2 = addPageActionToState(state, screenId, 'testAction2', {
            title: 'Test 2',
            icon: 'add',
        });
        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerDropDownActions = (): any => [testAction, testAction2];
        mockStore = getMockStore(state);

        const { queryAllByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderDropdownActions screenId={screenId} />
            </Provider>,
        );

        const menuItems = queryAllByTestId('e-popover-action-single-button-mock');

        expect(menuItems).toHaveLength(2);
        expect(menuItems[0]).not.toHaveAttribute('aria-disabled', 'true');
        expect(menuItems[0]).toHaveTextContent('Test');
        expect(menuItems[1]).not.toHaveAttribute('aria-disabled', 'true');
        expect(menuItems[1]).toHaveTextContent('Test 2');
    });

    it('should render empty if no actions defined', () => {
        mockStore = getMockStore(state);
        const { container } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderDropdownActions screenId={screenId} />
            </Provider>,
        );

        expect(container.childElementCount).toEqual(0);
    });

    it('should render action button if its disabled by a callback', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', {
            title: 'Test',
            icon: 'add',
            isDisabled: () => true,
        });
        const testAction2 = addPageActionToState(state, screenId, 'testAction2', {
            title: 'Test 2',
            icon: 'add',
            isDisabled: () => true,
        });

        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerDropDownActions = () => [testAction, testAction2];
        mockStore = getMockStore(state);
        const { queryAllByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderDropdownActions screenId={screenId} />
            </Provider>,
        );

        const menuItems = queryAllByTestId('e-popover-action-single-button-mock');
        expect(menuItems[0]).toHaveAttribute('disabled');
    });

    it('should render single action button disabled if its disabled', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', {
            title: 'Test',
            icon: 'add',
            isDisabled: true,
        });

        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerDropDownActions = () => [testAction];
        mockStore = getMockStore(state);
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderDropdownActions screenId={screenId} />
            </Provider>,
        );

        expect(queryByTestId('e-popover-action-single-button')).toBeDisabled();
    });

    it('should render action buttons disabled if they are disabled', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', {
            title: 'Test',
            icon: 'add',
            isDisabled: true,
        });
        const testAction2 = addPageActionToState(state, screenId, 'testAction2', {
            title: 'Test 2',
            icon: 'add',
            isDisabled: true,
        });

        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerDropDownActions = () => [testAction, testAction2];
        mockStore = getMockStore(state);
        const { queryAllByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderDropdownActions screenId={screenId} />
            </Provider>,
        );

        const menuItems = queryAllByTestId('e-popover-action-single-button-mock');
        expect(menuItems).toHaveLength(2);
        expect(menuItems[0]).toHaveAttribute('disabled');
        expect(menuItems[0]).toHaveTextContent('Test');
        expect(menuItems[1]).toHaveAttribute('disabled');
        expect(menuItems[1]).toHaveTextContent('Test 2');
    });

    it('should not render the action button if it is hidden by a callback', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', {
            title: 'Test',
            icon: 'add',
            isHidden: () => true,
        });
        const testAction2 = addPageActionToState(state, screenId, 'testAction2', {
            title: 'Test 2',
            icon: 'add',
        });

        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerDropDownActions = () => [testAction, testAction2];
        mockStore = getMockStore(state);
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderDropdownActions screenId={screenId} />
            </Provider>,
        );

        expect(queryByTestId('e-header-quick-action-label-test')!).toBeNull();
    });

    it('should not render the action button if it is hidden', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', {
            title: 'Test',
            icon: 'add',
            isHidden: true,
        });
        const testAction2 = addPageActionToState(state, screenId, 'testAction2', {
            title: 'Test 2',
            icon: 'add',
            isHidden: true,
        });

        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerDropDownActions = () => [testAction, testAction2];
        mockStore = getMockStore(state);
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderDropdownActions screenId={screenId} />
            </Provider>,
        );

        expect(queryByTestId('e-header-quick-action-label-test')!).toBeNull();
    });

    it('should not render the action button if it is the user does not have access rights', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', {
            title: 'Test',
            icon: 'add',
            access: {
                bind: 'someProperty',
                node: '@sage/xtrem-test/MyNode',
            },
        });
        const testAction2 = addPageActionToState(state, screenId, 'testAction2', {
            title: 'Test 2',
            icon: 'add',
            access: {
                bind: 'someProperty',
                node: '@sage/xtrem-test/MyNode',
            },
        });
        (state.screenDefinitions[screenId] as PageDefinition).accessBindings = {
            MyNode: {
                someProperty: 'unauthorized',
            },
        };
        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerDropDownActions = () => [testAction, testAction2];
        mockStore = getMockStore(state);
        const { container } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderDropdownActions screenId={screenId} />
            </Provider>,
        );

        expect(container.childElementCount).toEqual(1);
        expect(container.querySelector('[data-component="action-popover-button"]>span')).toBeNull();
    });

    it('should not render the action button if the action has access definition but the corresponding rights are not available', () => {
        const testAction = addPageActionToState(state, screenId, 'testAction', {
            title: 'Test',
            icon: 'add',
            isHidden: true,
            access: {
                bind: 'someProperty',
                node: '@sage/xtrem-test/MyNode',
            },
        });
        const testAction2 = addPageActionToState(state, screenId, 'testAction2', {
            title: 'Test 2',
            icon: 'add',
            access: {
                bind: 'someProperty',
                node: '@sage/xtrem-test/MyNode',
            },
        });

        const pageProperties = getPagePropertiesFromState(screenId, state);
        pageProperties.headerDropDownActions = () => [testAction, testAction2];
        mockStore = getMockStore(state);
        const { container } = render(
            <Provider store={mockStore}>
                <ConnectedHeaderDropdownActions screenId={screenId} />
            </Provider>,
        );

        expect(container.childElementCount).toEqual(1);
        expect(container.querySelector('[data-component="action-popover-button"]>span')).toBeNull();
    });
});
