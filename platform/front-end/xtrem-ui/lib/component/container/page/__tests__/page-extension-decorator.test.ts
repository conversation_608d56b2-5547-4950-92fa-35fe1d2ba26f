import { getMockPageMetadata } from '../../../../__tests__/test-helpers';
import type { Page } from '../../../../service/page';
import * as pageMetadataImport from '../../../../service/page-metadata';
import * as tagetPrototypeImport from '../../../../utils/decorator-utils';
import type { PageExtensionDecoratorProperties } from '../../../decorators';
import { pageExtension } from '../page-extension-decorator';
import type { PageEventType } from '../page-types';

describe('Page extension decorator', () => {
    let decoratorProperties: PageExtensionDecoratorProperties<Page>;
    let decoratorProperties2: PageExtensionDecoratorProperties<Page>;
    let pageMetadata: pageMetadataImport.PageMetadata;
    class TestPage {}
    const pageName = TestPage.name;
    const onLoad: PageEventType<TestPage> = () => {};
    const onClose: PageEventType<TestPage> = () => {};

    beforeEach(() => {
        pageMetadata = getMockPageMetadata(pageName);
        jest.spyOn(pageMetadataImport, 'getPageMetadata').mockImplementation(() => pageMetadata);
        jest.spyOn(tagetPrototypeImport, 'getTargetPrototype').mockImplementation(ctor => ctor);
        decoratorProperties = {
            extends: '@sage/module/page',
            authorizationCode: 'extensionMode',
            onLoad,
            onClose,
        };
        decoratorProperties2 = {
            extends: '@sage/module/second',
            authorizationCode: 'extensionMode',
            onLoad,
        };
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('Should add onLoadAfter and onCloseAfter array to page properties', () => {
        pageExtension({ ...decoratorProperties })(TestPage);
        pageExtension({ ...decoratorProperties2 })(TestPage);
        pageMetadataImport.getPageMetadata(TestPage).pageExtensionThunks.forEach(e => e());
        expect(pageMetadata.uiComponentProperties.TestPage).toMatchObject({
            onLoadAfter: [onLoad, onLoad],
            onCloseAfter: [onClose],
        });
    });
});
