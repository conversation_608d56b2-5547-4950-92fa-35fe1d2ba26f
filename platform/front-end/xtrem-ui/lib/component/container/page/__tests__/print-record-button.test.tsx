import React from 'react';
import { getMockState, getMockStore } from '../../../../__tests__/test-helpers/mock-store-helpers';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux';
import { getMockPageDefinition, getMockPageMetadata } from '../../../../__tests__/test-helpers';
import { render, waitFor } from '@testing-library/react';
import type { PageDefinition } from '../../../../service/page-definition';
import { PrintRecordButton } from '../print-record-button';
import * as dialogService from '../../../../service/dialog-service';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import type { PageDecoratorProperties } from '../page-decorator';

const screenId = 'TestPage';

let mockState: XtremAppState;
let mockStore: MockStoreEnhanced<XtremAppState>;
let openDialogMock: jest.SpyInstance;

describe('Print record button', () => {
    beforeEach(() => {
        openDialogMock = jest.spyOn(dialogService, 'openPageDialog').mockImplementation(() => Promise.resolve());

        mockState = getMockState();
        mockState.screenDefinitions[screenId] = getMockPageDefinition(
            screenId,
            {
                selectedRecordId: '2',
                metadata: getMockPageMetadata(screenId, {
                    uiComponentProperties: {
                        [screenId || 'AnyPage']: {
                            title: 'Test Page',
                            node: '@sage/xtrem-test/TestNode',
                        } as PageDecoratorProperties<any>,
                    },
                }),
            },
            { rootNode: '@sage/xtrem-test/TestNode' },
        );
        mockState.printingSettings = {
            printingAssignmentDialogUrl: '@sage/xtrem-test/ReportAssignmentPage',
            canAccessPrintingAssignmentDialog: true,
            listPrintingWizardUrl: '@sage/xtrem-test/ListPrintingPage',
            canAccessListPrintingWizard: true,
            recordPrintingWizardUrl: '@sage/xtrem-test/RecordPrintingPage',
            canAccessRecordPrintingWizard: true,
            listPrintingGlobalMutationConfigPage: '@sage/xtrem-test/ListPrintingGlobalMutationConfigPage',
            recordPrintingGlobalBulkMutationName: 'globalPrintRecords',
        };
        mockStore = getMockStore(mockState);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should render the component if print settings are available, enabled and there is a selected record', () => {
        const { container } = render(
            <Provider store={mockStore}>
                <PrintRecordButton screenId={screenId} />
            </Provider>,
        );

        expect(container.childElementCount).toEqual(1);
        expect(container).toMatchSnapshot();
    });

    it('should not render the component if print settings are available, enabled but there is no record selected', () => {
        (mockState.screenDefinitions[screenId] as PageDefinition).selectedRecordId = null;
        const { container } = render(
            <Provider store={getMockStore(mockState)}>
                <PrintRecordButton screenId={screenId} />
            </Provider>,
        );

        expect(container.childElementCount).toEqual(0);
    });

    it('should not render the component if a record is selected, print settings are available, but no access is granted', () => {
        mockState.printingSettings!.canAccessPrintingAssignmentDialog = false;
        mockState.printingSettings!.canAccessRecordPrintingWizard = false;
        const { container } = render(
            <Provider store={getMockStore(mockState)}>
                <PrintRecordButton screenId={screenId} />
            </Provider>,
        );

        expect(container.childElementCount).toEqual(0);
    });

    it('should not render the component if a record is selected, but no print settings available', () => {
        mockState.printingSettings = null;
        const { container } = render(
            <Provider store={getMockStore(mockState)}>
                <PrintRecordButton screenId={screenId} />
            </Provider>,
        );

        expect(container.childElementCount).toEqual(0);
    });

    it('should render the component if a record is selected, print settings are available, but only one of the actions is available', () => {
        mockState.printingSettings!.canAccessPrintingAssignmentDialog = false;
        mockState.printingSettings!.canAccessRecordPrintingWizard = true;
        const { container } = render(
            <Provider store={getMockStore(mockState)}>
                <PrintRecordButton screenId={screenId} />
            </Provider>,
        );

        expect(container.childElementCount).toEqual(1);
    });

    it('should render both actions enabled if they are enabled', async () => {
        const { queryByTestId, baseElement } = render(
            <Provider store={mockStore}>
                <PrintRecordButton screenId={screenId} />
            </Provider>,
        );

        const actionButton = queryByTestId('e-print-record-button')!;
        await userEvent.click(actionButton)!;
        await waitFor(() => {
            expect(baseElement.querySelector('[data-element="additional-buttons"]')).toBeTruthy();
        });
        expect(queryByTestId('e-print-record-assignment-button')!).not.toBeDisabled();
        expect(queryByTestId('e-print-record-wizard-button')!).not.toBeDisabled();
    });

    it("should disable the assignment button if it's disabled", async () => {
        mockState.printingSettings!.canAccessPrintingAssignmentDialog = false;
        const { queryByTestId, baseElement } = render(
            <Provider store={mockStore}>
                <PrintRecordButton screenId={screenId} />
            </Provider>,
        );

        const actionButton = queryByTestId('e-print-record-button')!;
        await userEvent.click(actionButton);
        await waitFor(() => {
            expect(baseElement.querySelector('[data-element="additional-buttons"]')).toBeTruthy();
        });
        expect(queryByTestId('e-print-record-assignment-button')!).toBeDisabled();
        expect(queryByTestId('e-print-record-wizard-button')!).not.toBeDisabled();
    });

    it("should disable the printing button if it's disabled", async () => {
        mockState.printingSettings!.canAccessRecordPrintingWizard = false;
        const { queryByTestId, baseElement } = render(
            <Provider store={mockStore}>
                <PrintRecordButton screenId={screenId} />
            </Provider>,
        );

        const actionButton = queryByTestId('e-print-record-button')!;
        await userEvent.click(actionButton);
        await waitFor(() => {
            expect(baseElement.querySelector('[data-element="additional-buttons"]')).toBeTruthy();
        });
        expect(queryByTestId('e-print-record-assignment-button')!).not.toBeDisabled();
        expect(queryByTestId('e-print-record-wizard-button')!).toBeDisabled();
    });

    it('should open the assignment dialog', async () => {
        const { queryByTestId, baseElement } = render(
            <Provider store={mockStore}>
                <PrintRecordButton screenId={screenId} />
            </Provider>,
        );

        const actionButton = queryByTestId('e-print-record-button')!;
        await userEvent.click(actionButton);
        await waitFor(() => {
            expect(baseElement.querySelector('[data-element="additional-buttons"]')).toBeTruthy();
        });
        expect(openDialogMock).not.toHaveBeenCalled();
        await userEvent.click(queryByTestId('e-print-record-assignment-button')!);
        expect(openDialogMock).toHaveBeenCalledWith(
            '@sage/xtrem-test/ReportAssignmentPage',
            {
                _PRINTING_SOURCE_PAGE: 'TestPage',
                _PRINTING_SOURCE_TYPE: 'record',
                _PRINTING_SOURCE_NODE_TYPE: '@sage/xtrem-test/TestNode',
            },
            { resolveOnCancel: true, size: 'large' },
        );
    });

    it('should open the wizard dialog', async () => {
        const { queryByTestId, baseElement } = render(
            <Provider store={mockStore}>
                <PrintRecordButton screenId={screenId} />
            </Provider>,
        );

        const actionButton = queryByTestId('e-print-record-button')!;
        await userEvent.click(actionButton);
        await waitFor(() => {
            expect(baseElement.querySelector('[data-element="additional-buttons"]')).toBeTruthy();
        });
        expect(openDialogMock).not.toHaveBeenCalled();
        await userEvent.click(queryByTestId('e-print-record-wizard-button')!);
        expect(openDialogMock).toHaveBeenCalledWith(
            '@sage/xtrem-test/RecordPrintingPage',
            {
                _PRINTING_SOURCE_PAGE: 'TestPage',
                _PRINTING_RECORD_ID: '2',
                _PRINTING_SOURCE_NODE_TYPE: '@sage/xtrem-test/TestNode',
            },
            { resolveOnCancel: true, size: 'medium' },
        );
    });
});
