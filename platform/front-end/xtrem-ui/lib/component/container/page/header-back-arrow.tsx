import * as React from 'react';
import { connect } from 'react-redux';
import IconButton from 'carbon-react/esm/components/icon-button';
import Icon from 'carbon-react/esm/components/icon';
import * as tokens from '@sage/design-tokens/js/base/common';
import { localize } from '../../../service/i18n-service';
import * as xtremRedux from '../../../redux';

interface BackArrowProps {
    history: { path: string; queryParams: any }[];
    onGoBack?: () => void;
}

export function BackArrowButton({ history, onGoBack }: BackArrowProps): React.ReactElement | null {
    const handleBack = (): void => {
        onGoBack?.();
    };

    if (history.length === 0) return null;

    return (
        <div
            className="e-header-navigation-return-arrow-parent-page-container"
            data-testid="e-header-navigation-arrow-return"
        >
            <IconButton
                aria-label={localize('@sage/xtrem-ui/return-arrow', 'Return')}
                data-testid="e-header-navigation-arrow-return"
                onClick={handleBack}
            >
                <Icon
                    type="arrow_left"
                    color={tokens.colorsYin090}
                    tooltipMessage={localize('@sage/xtrem-ui/return-arrow', 'Return')}
                />
            </IconButton>
        </div>
    );
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: { screenId: string }): BackArrowProps => ({
    ...props,
    history: state.navigation.history,
});

const mapDispatchToProps = (dispatch: xtremRedux.AppThunkDispatch): Partial<BackArrowProps> => {
    return { onGoBack: () => dispatch(xtremRedux.actions.goBack()) };
};

const ConnectedBackArrowButton = connect(mapStateToProps, mapDispatchToProps)(BackArrowButton);

export default ConnectedBackArrowButton;
