import type { ClientNode } from '@sage/xtrem-client';
import type { Dict, RequireOnlyOne } from '@sage/xtrem-shared';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { Page } from '../../..';
import type { OnTelemetryEventFunction } from '../../../redux/state';
import type { CollectionValue } from '../../../service/collection-data-service';
import type { GraphQLApi, ReadOnlyGraphQLApi } from '../../../service/graphql-api';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import type { PageArticleItem } from '../../../service/layout-types';
import type { AttachmentInformation } from '../../../service/node-information-service';
import type { PageDefinition } from '../../../service/page-definition';
import type { Extend } from '../../../service/page-extension';
import type { ElementWithShortcut } from '../../../service/page-metadata';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import type { Storage } from '../../../service/storage-service';
import type { ContextType, ScreenBaseGenericType, ScreenExtension } from '../../../types';
import type { MenuSeparatorResult } from '../../../utils/action-menu-utils';
import type { ContainerProperties } from '../../abstract-container';
import type {
    DateControlObject,
    DropdownListControlObject,
    ImageControlObject,
    LabelControlObject,
    NumericControlObject,
    PageActionButtonType,
    PageActionControlObject,
    ReferenceControlObject,
    SectionControlObject,
    SelectControlObject,
    TextControlObject,
} from '../../control-objects';
import type { DetailPanelDecoratorProperties } from '../../decorator-properties';
import type { HasCalendarConfigurationOptions, HasGenericErrorHandler, MappableIcon } from '../../field/traits';
import type { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { ContainerControlObjectConstructorProps, ContainerKey, OrderByType } from '../../types';
import type { CardDefinition, CardExtensionDefinition } from '../../ui/card/card-component';
import type {
    CollectionItemAction,
    CollectionItemActionMenuSeparator,
} from '../../ui/table-shared/table-dropdown-actions/table-dropdown-action-types';

export type PageEventType<CT> = (this: CT) => Promise<void> | void;
export type PageHeaderFields<CT extends ScreenExtension<CT> = Page> =
    | TextControlObject<CT>
    | LabelControlObject<any, CT>
    | ReferenceControlObject<any, CT>
    | NumericControlObject<CT>
    | DateControlObject<CT>
    | SelectControlObject<any, CT>
    | DropdownListControlObject<any, CT>;

export type IdFieldType<CT extends ScreenExtension<CT> = Page> =
    | string
    | PageHeaderFields<CT>
    | PageHeaderFields<CT>[]
    | null;

export interface PageHeaderCard<CT extends ScreenExtension<CT> = Page> {
    title: PageHeaderFields<CT>;
    titleRight?: PageHeaderFields<CT>;
    line2?: PageHeaderFields<CT>;
    line2Right?: PageHeaderFields<CT>;
    line3?: PageHeaderFields<CT>;
    line3Right?: PageHeaderFields<CT>;
    line4?: PageHeaderFields<CT>;
    line4Right?: PageHeaderFields<CT>;
    line5?: PageHeaderFields<CT>;
    line5Right?: PageHeaderFields<CT>;
    image?: ImageControlObject<CT>;
}

export interface BasePageDecoratorProperties<CT extends ScreenExtension<CT>> {
    /** Function code corresponding to the page */
    authorizationCode?: string;
    /** Function that will be executed immediately after the page has been loaded */
    onLoad?: PageEventType<Extend<CT> & CT>;
    /** Function that will be executed immediately before the page closes */
    onClose?: PageEventType<Extend<CT> & CT>;
}

export type TableOptionsMenuType = 'dropdown' | 'toggle' | 'tabs';

export type OptionsMenuType<CT extends ScreenBase, NodeType extends ClientNode = any> =
    | OptionsMenuItemType<NodeType>[]
    | ((
          graph: CT extends Page<infer G> ? ReadOnlyGraphQLApi<G> : never,
          storage: Storage,
          queryParameter: PageDefinition['queryParameters'],
          username: string,
          userCode: string,
          serviceOptions: Dict<boolean>,
      ) => Promise<OptionsMenuItemType<NodeType>[]>);

export interface BulkAction {
    // Type of button, according with Carbon's API. Can be 'primary', 'secondary' or 'tertiary'. Defaults to 'primary'.
    buttonType?: PageActionButtonType;
    // Icon displayed in the button.
    icon?: IconType;
    // The title of the button.
    title: string;
    // If set, the action's icon and text are rendered in red. Defaults to false.
    isDestructive?: boolean;
    // Name of the mutation that will be triggered when the button is clicked.
    mutation: string;
    // The id of the button for pendo.
    id?: string;

    // Set if a mutation is a global mutation, not one specific to the node of the page
    isGlobal?: boolean;

    /**
     * An application page that can be used to set parameters of the bulk action. If defined, it is opened when the user clicks the bulk action button. The
     * pages values are used as parameters for the mutation
     *  */
    configurationPage?: string;
}

export interface MainListAction<CT extends ScreenBase, NodeType extends ClientNode = any>
    extends CollectionItemAction<CT, NodeType> {
    refreshesMainList?: 'record' | 'list';
}

export interface MainListActionMenuSeparator<CT extends ScreenBase, NodeType extends ClientNode = any>
    extends CollectionItemActionMenuSeparator<CT, NodeType> {
    refreshesMainList?: 'record' | 'list';
}

export type MainListActionOrMenuSeparator<CT extends ScreenBase, NodeType extends ClientNode = any> =
    | MainListAction<CT, NodeType>
    | MainListActionMenuSeparator<CT, NodeType>;

export interface PageNavigationPanel<CT extends ScreenBase, NodeType extends ClientNode = any>
    extends MappableIcon<null>,
        HasCalendarConfigurationOptions<CT, NodeType> {
    /** Whether the navigation panel header is shown. Defaults to true */
    isHeaderHidden?: boolean;
    /** Whether the user can filter the navigation panel items. Defaults to true */
    canFilter?: boolean;
    /** Define how predefined graphql filters or page links should be displayed. Defaults to 'dropdown' */
    menuType?: TableOptionsMenuType;
    /** Set of predefined graphql filters or page links that will be displayed under the navigation panel search box */
    optionsMenu?: OptionsMenuType<CT, NodeType>;
    /** Fields that will be rendered inside each navigation panel item card */
    listItem: CardDefinition<CT, NodeType>;
    /**  If the event is consumed, it should return true to stop propagation. */
    onOptionsMenuValueChange?: (this: CT, mainFilterValue: string, selectedFilter?: string) => void | boolean;
    /** Event triggered on selecting a list item */
    onSelect?: (this: CT, listItemValue: Object) => void | boolean;
    /** Whether the separator element which is displayed when the first letter of the title element in the list should be hidden */
    isFirstLetterSeparatorHidden?: boolean;
    /** If the search results only yield a single result, then the platform code should automatically select it. */
    isAutoSelectEnabled?: boolean;
    /** Sets a text when no data is available in the table */
    emptyStateText?: string;
    /** Sets a complementary text link when no data is available in the table  */
    emptyStateClickableText?: string;
    /** Function to be executed when the clickable text is clicked */
    onEmptyStateLinkClick?: (this: CT) => void;
    /** A list of bulk actions */
    bulkActions?: BulkAction[];
    /**
     * Ordered list of fields which the navigation panel items are ordered/sorted by.
     * Use 1 for ascending order and -1 for descending/reverse order.
     *
     * E.g.
     * orderBy: {
     *   releaseDate: -1,
     * },
     *
     */
    orderBy?: OrderByType<NodeType>;

    /** Actions that are rendered at the end of the table as a drop-down menu */
    dropdownActions?: Array<MainListActionOrMenuSeparator<CT, NodeType>>;

    /** Actions that are rendered at the end of the table as a list of icon buttons */
    inlineActions?: Array<MainListAction<CT, NodeType>>;

    isSoundDisabled?: boolean;
}

export interface PageNavigationPanelExtension<CT extends ScreenBase, NodeType extends ClientNode = any>
    extends Omit<PageNavigationPanel<ScreenBase, NodeType>, 'listItem'> {
    listItem?: CardExtensionDefinition<CT, NodeType>;
    /**  If the event is consumed, it should return true to stop propagation. */
    onOptionsMenuValueChangeAfter?: (this: CT, mainFilterValue: string, selectedFilter?: string) => void | boolean;
    /** Event triggered on selecting a list item */
    onSelectAfter?: (this: CT, listItemValue: Object) => void | boolean;
    /** Function to be executed when the clickable text is clicked */
    onEmptyStateLinkClickAfter?: (this: CT) => void;
    /** Actions that are rendered at the end of the table as a drop-down menu */
    dropdownActions?: Array<MainListActionOrMenuSeparator<CT, NodeType>>;
    /** Actions that are rendered at the end of the table as a list of icon buttons */
    inlineActions?: Array<MainListAction<CT, NodeType>>;
}

export interface RuntimePageNavigationPanel<CT extends ScreenBase, NodeType extends ClientNode = any>
    extends PageNavigationPanel<ScreenBase, NodeType> {
    optionMenus: Array<PageNavigationPanelExtension<CT, NodeType>['optionsMenu']>;
    extensionListItem: Array<CardExtensionDefinition<CT, NodeType>>;
    onSelectAfter: Array<PageNavigationPanelExtension<CT, NodeType>['onSelectAfter']>;
    onOptionsMenuValueChangeAfter: Array<PageNavigationPanelExtension<CT, NodeType>['onOptionsMenuValueChangeAfter']>;
    onEmptyStateLinkClickAfter: Array<PageNavigationPanelExtension<CT, NodeType>['onEmptyStateLinkClickAfter']>;
}

export interface OptionsMenuItem<T extends ClientNode = any> {
    id?: string;
    /** The title of the menu item */
    title: string;
    /** Redirects the user to the selected Page definition. E.G @sage/em-project-management/Employees */
    page?: string;
    /** Icon in toggle mode */
    icon?: IconType;
    /** Applies a filter to the list */
    graphQLFilter?:
        | GraphQLFilter<T>
        | ((storage: Storage, queryParameter: PageDefinition['queryParameters']) => GraphQLFilter<T>);
}

export interface TableOptionsMenuItem<T extends ClientNode = any> {
    id?: string;
    /** The title of the menu item */
    title: string;
    /** Applies a filter to the list */
    graphQLFilter?: GraphQLFilter<T>;
}

// page OR graphQLFilter property in our object.
export type OptionsMenuItemType<NodeType extends ClientNode = any> = RequireOnlyOne<
    OptionsMenuItem<NodeType>,
    'page' | 'graphQLFilter'
>;

export type PageMode = 'default' | 'tabs' | 'wizard';

export interface IPageControlObject
    extends Omit<ContainerControlObjectConstructorProps<ContainerKey.Page>, 'elementId'> {
    dispatchPageValidation: (
        fieldsId?: string[],
    ) => Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
    getFocussedField: (screenId: string) => ReadonlyFieldControlObject<any, any, any, any> | null;
}

export interface IPageFragmentControlObject
    extends Omit<ContainerControlObjectConstructorProps<ContainerKey.PageFragment>, 'elementId'> {
    dispatchPageValidation: (
        fieldsId?: string[],
    ) => Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
    getFocussedField: (screenId: string) => ReadonlyFieldControlObject<any, any, any> | null;
}

export interface PageProperties<CT extends ScreenExtension<CT> = Page, NodeType extends ClientNode = any>
    extends ContainerProperties<CT>,
        HasGenericErrorHandler<CT> {
    /** Whether the navigation tabs of the page is hidden or not. Defaults to false */
    areNavigationTabsHidden?: boolean;
    /** Business actions available in this page */
    businessActions?: (this: CT) => PageActionControlObject<CT>[];
    /** List of actions that is represented as a dropdown menu in the top right side of the header */
    headerDropDownActions?: (this: CT) => Array<PageActionControlObject<CT> | MenuSeparatorResult>;
    /** Quick actions that are represented as icon buttons on the top right side of the header */
    headerQuickActions?: (this: CT) => Array<PageActionControlObject<CT>>;
    /** Actions used to create new entities */
    createAction?: (this: CT) => PageActionControlObject<CT> | PageActionControlObject<CT>[];
    /** Returns the identifier of the element that should be loaded by default */
    defaultEntry?: (this: CT) => null | string | Promise<null | string>;
    /** Set of fields that can be used to display global page information */
    headerCard?: (this: CT) => PageHeaderCard<CT>;
    /** Section that is displayed below the title but above the main content. Only works on medium and larger screens. */
    headerSection?: (this: CT) => SectionControlObject<CT>;
    /** Panel that provides detailed information of selected items */
    detailPanel?: (this: CT) => DetailPanelDecoratorProperties<CT>;
    /** Visualization mode */
    mode?: PageMode;
    /** Panel where to search, filter and navigate through current page related items */
    navigationPanel?: PageNavigationPanel<CT, NodeType>;
    /**
     * The GraphQL node that the page is bind to. This settings will be omitted
     * if the page is set to be transient
     */
    node?: keyof ScreenBaseGenericType<CT>;
    /** Whether the page title is hidden */
    isTitleHidden?: boolean;
    /** Subtitle displayed along with the page's title */
    subtitle?: string;
}

export interface PageComponentExternalProps {
    availableColumns: number;
    contextType: ContextType;
    fixedHeight?: number;
    isMainListDisplayedInDialog?: boolean;
    isMobileOrTabletScreen?: boolean;
    isPageDisplayedInFullScreenDialog?: boolean;
    noHeader?: boolean;
    pageDefinition: PageDefinition;
    selectedSection?: string | null;
}

export interface PageComponentProps extends PageComponentExternalProps {
    activeSection: string | null;
    areNavigationTabsHidden: boolean;
    attachmentInformation?: AttachmentInformation;
    attachmentListValue?: CollectionValue;
    elementsWithShortcut: ElementWithShortcut[];
    graphApi: GraphQLApi<any>;
    hasAttachmentPermission: boolean;
    hasNavigationPanel: boolean;
    headerCard?: PageHeaderCard;
    is360ViewOn: boolean;
    isNavigationPanelHidden: boolean;
    isNavigationPanelOpened: boolean;
    onTelemetryEvent?: OnTelemetryEventFunction;
    pageMode: PageMode;
    recordId: string | null;
    sections: Partial<PageArticleItem>[];
    setActiveSection: (activeSectionId: string | null) => void;
    set360ViewState: (screenId: string, is360ViewOn: boolean) => void;
    path: string | null;
}
