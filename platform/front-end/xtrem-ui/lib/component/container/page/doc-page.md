PATH: XTREEM/Client+Framework/Page

Artifact type representing a screen the user can interact with, either navigating to it or from inside a dialog.

## Introduction

A page artifact is defined by a TypeScript class that extends the `ui.Page` base class. A page file must contain one and only one page definition, and the file name must be the same as the class name but using `kebab-case` (instead of the `PascalCase` used in class names). A class decorator must be used to provide mandatory page properties (e.g. the module where it belongs) as well as describing additional behaviours, while the class body must be used to describe the content of the page.

Pages are meant to represent a record of a given entity type (specified through the `node` page decorator property) so, by default, the field properties of a page will be automatically bound to the corresponding entity property (if no matching property is found on the entity, the page will raise an error during the loading phase).

```ts
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<SamplePage>({
    authorizationCode: 'STRCTCDE',
    title: 'Sample page',
    isTransient: false,
    isTitleHidden: false,
    areNavigationTabsHidden: false,
    menuItem: applicationPages,
    mode: 'default',
    node: '@sage/x3-system/Site',
    objectTypeSingular: 'Site',
    objectTypePlural: 'Sites',
    idField(){
        return this.anotherSampleField;
    },
    headerSection(){
        return this.myHeaderSection;
    }
    priority: 1000,
    validation() {
        if (this.anotherSampleField.value !== 'The expected value'){
            return 'The page is invalid';
        }
    },
    headerLabel() {
        return this.headerLabel;
    },
    headerField() {
        return this.headerField;
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return [this.$standardSaveAction];
    },
    createAction() {
        return this.$standardNewAction;
    },
})
export class SamplePage extends ui.Page {
    @ui.decorators.section<SamplePage>({
        gravity: 'right',
        title: 'Blocks alignment',
    })
    sampleSection: ui.containers.Section;

    @ui.decorators.block<SamplePage>({
        parent() {
            return this.sampleSection;
        },
        title: 'Block title',
        gravity: 'left',
    })
    sampleBlock: ui.containers.Block;

    @ui.decorators.iconField<SamplePage>({
        parent() {
            return this.sampleBlock;
        },
        title: 'Sample field',
        map() {
            return 'home';
        },
    })
    sampleIconField: ui.fields.Icon;

    @ui.decorators.textField<SamplePage>({
        title: 'Sample text field 2',
        isFullWidth: true,
        parent() {
            return this.sampleBlock;
        },
    })
    anotherSampleField: ui.fields.Text;

    @ui.decorators.section<SamplePage>({
        title: 'Header Section',
    })
    myHeaderSection: ui.containers.Section;

    @ui.decorators.block<SamplePage>({
        parent() {
            return this.headerSection;
        },
        title: 'Some block title',
    })
    headerSectionBlock: ui.containers.Block;

    @ui.decorators.labelField<Table>({
        isTransient: true,
        map() {
            return 'Title label info';
        },
        onClick() {
            console.log('Doing something when the label field is clicked');
        },
    })
    headerLabel: ui.fields.Label;

    @ui.decorators.headerField<Table>({
        parent() {
            return this.block;
        },
        title: 'Title header',
    })
    headerField: ui.fields.Label;

    businessActions() {
        return [this.action1, this.action2, this.action3];
    },
    detailPanel() {
        return {
            activeSection: 0,
            footerActions: [this.action3, this.action4],
            header: this.detailPanelHeaderSection,
            sections: [this.detailPanelBodySection1, this.detailPanelBodySection2],
        };
    },
}

```

## Structure

The page body has a strict structure: it consists of sections, which consist of blocks which consists of fields:
`Page > Sections > Blocks > Fields`
All these components are represented by decorated class properties, as you can see it on the example above.

Moreover, there are other elements, such as detail panel or business actions, that are represented by methods. Please refer to the documentation of the corresponding element to know more about it.

## Design rules

Enforced minimum height on the page body using fixed height - 480px

## Decorator properties

This article summarizes simple decorator properties, but please note due to the complexity of some properties, there are additional articles which describe these in more details

### Display decorator properties:

-   **title**: The title that is displayed on the top of the page. The title can be provided as a string, or a callback function returning a string. It is also used on the navigation menu entry label. It is automatically picked up by the i18n engine and externalized for translation.
-   **subtitle**: Subtitle that is displayed on the top of the normal title pages. When the page is rendered into a dialog, the subtitle is displayed under the the title.
-   **isTitleHidden**: It hides the title row when set to true.
-   **areNavigationTabsHidden**: It hides the navigation tab control in the header when set to true.
-   **mode**: The page mode determines how the page is rendered, by `default` the sections are rendered underneath each other, in `tabs` mode the only one section is rendered and the users can switch between the sections using the tab control element in the header, in `wizard` mode the sections are rendered after each other in a standard wizard fashion and addition Next/Previous navigation controls provided.
-   **menuItem**: It determines the location of the page in the menu structure. The value is expected to be a `MenuItem` artifact object that can be imported from the `menu-items` folder of the current or any dependency packages. If not set, the page is not accessible from the menu structure, only by direct links or dialogs.
-   **priority**: Determines the order of the page in the menu structure. The smaller the value is, the higher the page is prioritized in the menu.
-   **skipDirtyCheck**: If it is set, the dirty checks are not run on the page when the user is about to leave it.
-   **onDirtyStateUpdated**: Callback which is triggered when the dirty status of the page changes.
-   **objectTypeSingular**: Singular name of the object that the page represents. It is displayed as the page title, followed by the idField (if applicable). If `objectTypeSingular` and `objectTypePlural` are defined, then `objectTypeSingular` takes precedence over the `title` on the page and full width navigation panel. It is automatically picked up by the i18n engine and externalized for translation.
-   **objectTypePlural**: Plural name of the object that the page represents. It is used in the navigation panel, full width navigation panel and in error messages. If `objectTypeSingular` and `objectTypePlural` are defined, then `objectTypePlural` takes precedence over the `title` on the page and full width navigation panel. It is automatically picked up by the i18n engine and externalized for translation.
-   **idField()**: Callback function that can return a field instance or a string. If a non-falsy value or field instance is returned, its value is displayed in place of the title. The field can return a string, a field instance or an array of field instances. In case of an array of field instances, the display value of the fields are joined together using a space character.
-   **headerSection()**: Callback function that can return a section instance. The returned section is placed between the title and the main page body. The section always visible above the main content, regardless to the selected tab and the scroll state of the page. It does not take affect on small and extra small sized screens.
-   **headerLabel()**: Callback function that can return a label field from the class body. The label field is placed next to the page or record title.
-   **headerField()**: Callback function that can return a non-block text-based field from the class body. The field is rendered inside the page header and is intended for displaying editable values such as a BOM's revision. Only field types listed in PageHeaderFieldTypes are supported.
-   **createAction()**: Callback function that can return a single page object instance or an array of page object instances from the class body. This action is placed on the header of the full with navigation panel as a labelled primary action button and as an icon button to the header of the split view navigation panel. If an array of actions is provided, the actions will render as a multi-action button in the main view navigation panel and as a split button in the split view navigation panel.
-   **businessActions()**: Callback function that can return an array of page object instances from the class body. These actions are rendered on the footer of the page in the form of labelled buttons.
-   **headerDropDownActions()**: Callback function that can return an array of page object instances from the class body. These actions are represented as a dropdown menu in the top right side of the header. The returned array can also contain `ui.menuSeparator({ id?: string, insertAfter?: string, insertBefore?: string })` in order to add a horizontal separator bar in between menu items.
-   **headerQuickActions()**: Callback function that can return an array of page object instances from the class body. These actions are  represented as icon buttons in the top-right corner of the header, next to the close icon.
-   **has360View()**: Optional boolean or callback returning boolean. If set to true, the user can switch from normal page representation to 360 view representation of the record.
-   **on360ViewSwitched()**: Callback which is triggered when the user switches between the normal page representation and the 360 view representation of the record.
-   **hasAttachmentsSection()**: Adds an additional section to the end of the page to manage attachments. It only works with non-transient pages that are bound to nodes with `hasAttachments` set to `true`

### Binding decorator properties:

-   **node**: The name of the node that the page represents, it must be defined if you wish to leverage the automated data binding capabilities
-   **bind**: The value provided as the bind property will be used instead of the class attribute name in the GraphQL queries. When using this property, any name is valid for the class attribute
-   **isTransient**: If marked as true, the page will not try to fetch data automatically from the server.
-   **validation**: Callback function, if returns a string the page is considered to be invalid.

### Access bindings decorator properties:

-   **access**: An object with the properties **node** and **bind** to defined an explicit page access binding. If not present the **node** and **bind** binding decorator properties described just above are used.

### Event handler decorator properties:

-   **onClose**: Event triggered when the user is about to navigate away from the page, or when the user closes the dialog that the page is loaded into.
-   **onLoad**: Event triggered when the page is loaded, or the selected record changed and the new data set is available.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

**Note:** The image that is displayed next to the title is been set by the navigationPanel's image property.
