import * as React from 'react';
import type { StepSequenceItemProps } from 'carbon-react/esm/components/step-sequence';
import { StepSequence, StepSequenceItem } from 'carbon-react/esm/components/step-sequence';
import type { StepSequenceStatus } from '../../field/step-sequence/step-sequence-types';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import type { ValueOrCallback } from '../../../utils/types';

export interface WizardStepsProps {
    screenId: string;
    selectedSection?: string | null;
    sections: { id: string; title?: ValueOrCallback<any, string> }[];
}

const getWizardStepStatus = (index: number, currentSectionIndex: number): StepSequenceStatus => {
    if (index === currentSectionIndex) {
        return 'current';
    }

    return index < currentSectionIndex ? 'complete' : 'incomplete';
};

export function WizardSteps({ sections, selectedSection, screenId }: WizardStepsProps): React.ReactNode {
    const currentSectionIndex = sections.findIndex(s => s.id === selectedSection);

    const steps = sections.map<StepSequenceItemProps & React.Attributes>((section, index) => ({
        key: section.id,
        id: section.id,
        indicator: String(index + 1),
        status: getWizardStepStatus(index, currentSectionIndex),
        children: resolveByValue<string>({
            propertyValue: section.title,
            screenId,
            fieldValue: null,
            rowValue: null,
            skipHexFormat: true,
        }),
    }));

    return (
        <div className="e-header-wizard-steps" data-testid="e-dialog-header-wizard-steps">
            <StepSequence orientation="horizontal">
                {steps.map(s => (
                    <StepSequenceItem key={s.key} {...s} />
                ))}
            </StepSequence>
        </div>
    );
}
