/**
 * @packageDocumentation
 * @module root
 **/
import type { ClientNode } from '@sage/xtrem-client';
import { objectKeys, type Dict, type RootMenuItem, type SubMenuItem } from '@sage/xtrem-shared';
import { isEmpty } from 'lodash';
import * as xtremRedux from '../../../redux';
import { addCustomFieldsToPageBody } from '../../../service/customization-service';
import { dispatchContainerValidation } from '../../../service/dispatch-service';
import { getFocussedField } from '../../../service/field-state-service';
import { localize } from '../../../service/i18n-service';
import type { DataTypeDetails, FormattedNodeDetails } from '../../../service/metadata-types';
import type { Page } from '../../../service/page';
import { getPageMetadata } from '../../../service/page-metadata';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { getUiComponentProperties, setUiComponentProperties } from '../../../service/transactions-service';
import type { Constructible, PageExtension } from '../../../types';
import { setDefaultProperties } from '../../../utils/abstract-fields-utils';
import { BULK_ACTION_ASYNC_EXPORT } from '../../../utils/constants';
import {
    applyDefaultValuesOnNestedField,
    getCardDefinitionFromReferenceDataType,
} from '../../../utils/data-type-utils';
import { schemaTypeNameFromNodeName } from '../../../utils/transformers';
import type { ValueOrCallback } from '../../../utils/types';
import { AbstractDecorator } from '../../abstract-decorator';
import type { LabelControlObject } from '../../control-objects';
import { DetailPanelControlObject, PageControlObject, detailPanelId } from '../../control-objects';
import type { DetailPanelDecoratorProperties } from '../../decorator-properties';
import { pageAction } from '../../decorators';
import type { HasShortcuts, VoidPromise } from '../../field/traits';
import type { NestedField, NestedFieldTypes } from '../../nested-fields';
import { withoutNestedTechnical } from '../../nested-fields';
import type {
    ContainerControlObjectConstructorProps,
    FieldControlObjectInstance,
    PageCategory,
    PageHeaderFieldTypes,
} from '../../types';
import { ContainerKey } from '../../types';
import { DetailPanelLayout, PageLayout } from '../layouts';
import type { IdFieldType, PageNavigationPanel, PageProperties, RuntimePageNavigationPanel } from './page-types';
import * as standardActions from './standard-page-actions';

export interface PageDecoratorProperties<CT extends PageExtension<CT>, NodeType extends ClientNode = any>
    extends PageProperties<CT, NodeType> {
    /** Function code corresponding to the page */
    authorizationCode?: string;

    /**
     * Metadata field for organizing pages. It should only be used for ADC, in Xtrem, the the `menuItem` should be used
     * instead.
     * */
    category?: PageCategory;

    /** Menu item that the page belongs to on the navigation menu */
    menuItem?: SubMenuItem | RootMenuItem;

    /** Priority that determines the order of the elements in its category on the navigation panel. */
    priority?: number;

    /**
     *  Module the page belongs to
     * @deprecated it used to be used in X3, no longer relevant
     * */
    module?: string;

    /** Function that will be executed immediately after the page has been loaded */
    onLoad?: (this: CT) => VoidPromise;

    /** Function that will be executed immediately before the page closes */
    onClose?: (this: CT, isWizardFinished?: boolean) => VoidPromise;

    /** Callback which is triggered when the dirty status of the page changes */
    onDirtyStateUpdated?: (this: CT, isDirty: boolean) => void;

    /** Subtitle displayed along with the page's title */
    subtitle?: string;

    /** If set, the confirmation dialog is not displayed when the user navigates away from the page */
    skipDirtyCheck?: boolean;

    /**
     * Singular name of the object that the page represents. It is displayed above the page title like the `subtitle`.
     * If both `objectTypeSingular` and `subtitle` is defined, then `objectTypeSingular` takes precedence over `the `subtitle` on the full width navigation panel.
     */
    objectTypeSingular?: string;

    /**
     * Plural name of the object that the page represents. It is used in the navigation panel and in error messages.
     * If both `objectTypePlural` and `title` is defined, then `objectTypePlural` takes precedence over `title` on the full width navigation panel.
     * */
    objectTypePlural?: string;

    /** Callback function that can return a field instance or a string. If a non-falsy value or field instance is returned, its value is displayed in place of the title */
    idField?: (this: CT) => IdFieldType<CT>;

    /** Callback function that can return a label field from the class body. The label field is placed next to the page or record title. */
    headerLabel?: (this: CT) => LabelControlObject<any, CT>;

    /**
     * Enables a object specific dashboard option on the page.
     */
    has360View?: ValueOrCallback<CT, boolean>;

    /** Function that will be executed when the user switches between page mode and 360 view mode */
    on360ViewSwitched?: (this: CT, is360ViewOn: boolean) => void;

    /** Adds an additional section to the end of the page to manage attachments. It only works with non-transient pages that are bound to nodes with `hasAttachments` set to `true` */
    hasAttachmentsSection?: boolean;

    /** Optional non-block text-based field to show in page header */
    headerField?: (this: CT) => FieldControlObjectInstance<PageHeaderFieldTypes>;
}

export class PageDecorator extends AbstractDecorator<ContainerKey.Page> {
    protected _layout = PageLayout;

    protected _controlObjectConstructor = PageControlObject;

    protected readonly buildActions = (): this => {
        if (this._metadataProps.pageMetadata) {
            objectKeys(this._metadataProps.pageMetadata.pageActionThunks).forEach(k => {
                this._metadataProps.pageMetadata.pageActions[k] = this._metadataProps.pageMetadata.pageActionThunks[k](
                    this.nodeTypes,
                    this.dataTypes,
                );
            });
        }
        return this;
    };

    protected setUiComponentProperties = (): this => {
        const applicableProperties: PageDecoratorProperties<Page> = {
            areNavigationTabsHidden: this._metadataProps.properties.areNavigationTabsHidden,
            createAction: this._metadataProps.properties.createAction,
            has360View: this._metadataProps.properties.has360View,
            hasAttachmentsSection: this._metadataProps.properties.hasAttachmentsSection,
            headerCard: this._metadataProps.properties.headerCard,
            headerDropDownActions: this._metadataProps.properties.headerDropDownActions,
            headerLabel: this._metadataProps.properties.headerLabel,
            headerField: this._metadataProps.properties.headerField,
            headerQuickActions: this._metadataProps.properties.headerQuickActions,
            headerSection: this._metadataProps.properties.headerSection,
            idField: this._metadataProps.properties.idField,
            isDisabled: this._metadataProps.properties.isDisabled,
            isHidden: this._metadataProps.properties.isHidden,
            isTitleHidden: this._metadataProps.properties.isTitleHidden,
            isTransient: this._metadataProps.properties.isTransient,
            mode: this._metadataProps.properties.mode,
            module: this._metadataProps.properties.module,
            node: this._metadataProps.properties.node,
            objectTypePlural: this._metadataProps.properties.objectTypePlural,
            objectTypeSingular: this._metadataProps.properties.objectTypeSingular,
            on360ViewSwitched: this._metadataProps.properties.on360ViewSwitched,
            onClose: this._metadataProps.properties.onClose,
            onDirtyStateUpdated: this._metadataProps.properties.onDirtyStateUpdated,
            onError: this._metadataProps.properties.onError,
            onLoad: this._metadataProps.properties.onLoad,
            priority: this._metadataProps.properties.priority,
            skipDirtyCheck: this._metadataProps.properties.skipDirtyCheck,
            subtitle: this._metadataProps.properties.subtitle,
            title: this._metadataProps.properties.title,
            validation: this._metadataProps.properties.validation,
        };
        const properties = setDefaultProperties(
            applicableProperties,
            this._controlObjectConstructor.defaultUiProperties,
            ContainerKey.Page,
        );
        if (this._metadataProps.properties.isTransient) {
            objectKeys(this._metadataProps.pageMetadata.uiComponentProperties).forEach(key => {
                this._metadataProps.pageMetadata.uiComponentProperties[key].isTransient = true;
            });
        }
        this._metadataProps.pageMetadata.uiComponentProperties[this.elementId] = Object.assign(
            this._metadataProps.pageMetadata.uiComponentProperties[this.elementId] || {},
            properties,
        );
        this._metadataProps.pageMetadata.defaultUiComponentProperties[this.elementId] = {
            ...this._metadataProps.pageMetadata.defaultUiComponentProperties[this.elementId],
            ...properties,
        };

        (
            this._metadataProps.pageMetadata.defaultUiComponentProperties[
                this.elementId
            ] as PageDecoratorProperties<Page>
        ).navigationPanel = this.getNavigationPanelProps();

        (
            this._metadataProps.pageMetadata.uiComponentProperties[this.elementId] as PageDecoratorProperties<Page>
        ).navigationPanel = this.getNavigationPanelProps();

        if (this._metadataProps.properties.navigationPanel) {
            const canFilter = this._metadataProps.properties.navigationPanel.canFilter;
            this._metadataProps.properties.navigationPanel.canFilter = canFilter === undefined ? true : canFilter;
            const nestedFields = this._metadataProps.properties.navigationPanel.listItem;
            if (nestedFields) {
                withoutNestedTechnical(
                    Object.values(nestedFields).filter(Boolean) as NestedField<Page, NestedFieldTypes>[],
                ).forEach(nestedField => {
                    nestedField.properties = setDefaultProperties(
                        nestedField.properties,
                        nestedField.defaultUiProperties,
                        nestedField.type,
                    );

                    if (this._metadataProps.properties.node) {
                        applyDefaultValuesOnNestedField(
                            this.nodeTypes,
                            this.dataTypes,
                            nestedField,
                            String(this._metadataProps.properties.node),
                        );
                    }
                });
            }
        }

        this._metadataProps.pageMetadata.businessActionsThunk = this._metadataProps.properties.businessActions;
        this._metadataProps.pageMetadata.defaultEntryThunk = this._metadataProps.properties.defaultEntry;

        if (this._metadataProps.properties.detailPanel) {
            this._metadataProps.pageMetadata.detailPanelThunk = (ctx: Page): DetailPanelControlObject => {
                const detailPanelProperties: DetailPanelDecoratorProperties =
                    this._metadataProps.properties.detailPanel!.apply(ctx);
                const layoutBuilder = new DetailPanelLayout(ctx, this.elementId, this.nodeTypes, this.dataTypes, {
                    pageMetadata: this._metadataProps.pageMetadata,
                    detailPanelHeader: detailPanelProperties.header,
                    detailPanelSections: detailPanelProperties.sections,
                } as any).build();
                const { detailPanelHeaderLayout, detailPanelSectionsLayout } = layoutBuilder.layout;

                const containerControlObject = new DetailPanelControlObject({
                    screenId: this.elementId,
                    elementId: detailPanelId,
                    getUiComponentProperties:
                        getUiComponentProperties as ContainerControlObjectConstructorProps<ContainerKey.DetailPanel>['getUiComponentProperties'],
                    headerSection: detailPanelHeaderLayout,
                    sections: detailPanelSectionsLayout,
                    setUiComponentProperties,
                    layout: {
                        detailPanelHeaderLayout: layoutBuilder.detailPanelHeaderLayout,
                        detailPanelSectionsLayout: layoutBuilder.detailPanelSectionsLayout,
                    },
                    footerActions: detailPanelProperties.footerActions,
                });

                this._metadataProps.pageMetadata.controlObjects[detailPanelId] = containerControlObject;

                const uiComponentProperties: Partial<DetailPanelDecoratorProperties> = {
                    isHidden: detailPanelProperties.isHidden,
                    title: detailPanelProperties.title,
                    isTransient: detailPanelProperties.isTransient,
                    isDisabled: detailPanelProperties.isDisabled,
                    isExtendable: detailPanelProperties.isExtendable,
                    activeSection: detailPanelProperties.activeSection,
                    isCloseButtonHidden: detailPanelProperties.isCloseButtonHidden,
                };
                this._metadataProps.pageMetadata.uiComponentProperties[detailPanelId] = uiComponentProperties;
                this._metadataProps.pageMetadata.defaultUiComponentProperties[detailPanelId] = {
                    ...uiComponentProperties,
                };

                return containerControlObject;
            };
        }

        // Collect hotkeys defined for various page elements
        objectKeys(this._metadataProps.pageMetadata.uiComponentProperties).forEach(elementId => {
            const element = this._metadataProps.pageMetadata.uiComponentProperties[elementId] as HasShortcuts;
            if (element.shortcut) {
                this._metadataProps.pageMetadata.elementsWithShortcut.push({
                    elementId,
                    shortcut: element.shortcut,
                });
            }
        });
        return this;
    };

    private readonly getNavigationPanelProps = (): RuntimePageNavigationPanel<Page> | undefined => {
        const pageProperties = this._metadataProps.properties;
        if (pageProperties.isTransient || !pageProperties.node) {
            return undefined;
        }

        const contextNode = String(pageProperties.node);
        let navigationPanel: PageNavigationPanel<Page> | null = pageProperties.navigationPanel || null;

        // The application developer may define a navigation panel with a `null` value to override the automatic generation of the navigation panel.
        const hasOwnNavPanelDefinition = !!navigationPanel || objectKeys(pageProperties).includes('navigationPanel');
        const nodeType = pageProperties.node
            ? this.nodeTypes[schemaTypeNameFromNodeName(pageProperties.node)]
            : undefined;
        const dataType = nodeType?.defaultDataType ? this.dataTypes[nodeType.defaultDataType] : undefined;
        if (!navigationPanel && !hasOwnNavPanelDefinition && dataType?.value) {
            navigationPanel = {
                listItem: getCardDefinitionFromReferenceDataType({
                    contextNode,
                    dataType,
                    dataTypes: this.dataTypes,
                    nodeTypes: this.nodeTypes,
                }),
            };
        }

        if (!navigationPanel) {
            return undefined;
        }

        const state = xtremRedux.getStore().getState();
        let bulkActions = navigationPanel.bulkActions || [];
        if (
            state.exportConfigurationPage &&
            !isEmpty(this._metadataProps.pageMetadata?.exportTemplatesByNode?.[String(pageProperties.node)])
        ) {
            /**
             * If the page is associated with a node that has at least 1 export template, then we add an additional bulk action.
             *  */
            bulkActions = [
                ...bulkActions,
                {
                    mutation: BULK_ACTION_ASYNC_EXPORT,
                    title: localize('@sage/xtrem-ui/bulk-action-async-export', 'Export'),
                    buttonType: 'tertiary',
                    icon: 'csv',
                    configurationPage: state.exportConfigurationPage,
                },
            ];
        }

        if (
            state.printingSettings?.recordPrintingGlobalBulkMutationName &&
            this._metadataProps.pageMetadata?.hasRecordPrintingTemplates
        ) {
            bulkActions = [
                ...bulkActions,
                {
                    mutation: state.printingSettings?.recordPrintingGlobalBulkMutationName,
                    isGlobal: true,
                    title: localize('@sage/xtrem-ui/bulk-action-print', 'Print'),
                    buttonType: 'tertiary',
                    icon: 'print',
                    configurationPage: state.printingSettings?.listPrintingGlobalMutationConfigPage ?? '',
                },
            ];
        }

        const navigationPanelProperties: RuntimePageNavigationPanel<Page> = {
            extensionListItem: [],
            onSelectAfter: [],
            optionMenus: [],
            onOptionsMenuValueChangeAfter: [],
            onEmptyStateLinkClickAfter: [],
            isHeaderHidden: false,
            ...navigationPanel,
            bulkActions,
        };

        if (!navigationPanelProperties.menuType) {
            navigationPanelProperties.menuType = 'dropdown';
        }

        return navigationPanelProperties;
    };
}

/**
 * Initializes the decorated member as a [Page]{@link PageControlObject} container with the provided properties
 *
 * @param properties The properties that the [Page]{@link PageControlObject} container will be initialized with
 */
export function page<CT extends PageExtension<CT>, NodeType extends ClientNode = any>(
    properties: PageDecoratorProperties<CT, NodeType>,
) {
    return (ctor: Constructible<Page>): void => {
        const pageMetadata = getPageMetadata(ctor);
        pageMetadata.rootNode = properties.node ? String(properties.node) : undefined;
        pageMetadata.isTransient = properties.isTransient || false;
        pageMetadata.hasAttachmentsSection = properties.hasAttachmentsSection || false;
        pageMetadata.pageThunk = (
            nodeTypes: Dict<FormattedNodeDetails>,
            dataTypes: Dict<DataTypeDetails>,
        ): PageControlObject<CT> => {
            pageAction<Page>(standardActions.remove())(ctor.prototype, '$standardDeleteAction');

            pageAction<Page>(standardActions.save())(ctor.prototype, '$standardSaveAction');

            pageAction<Page>(standardActions.create())(ctor.prototype, '$standardNewAction');

            pageAction<Page>(standardActions.cancel())(ctor.prototype, '$standardCancelAction');

            pageAction<Page>(standardActions.duplicate())(ctor.prototype, '$standardDuplicateAction');

            pageAction<Page>(standardActions.executeDuplication())(ctor.prototype, '$standardExecuteDuplicationAction');

            pageAction<Page>(standardActions.openCustomizationPageWizard())(
                ctor.prototype,
                '$standardOpenCustomizationPageWizardAction',
            );

            pageAction<Page>(standardActions.openRecordHistory())(ctor.prototype, '$standardOpenRecordHistoryAction');

            pageAction<Page>(standardActions.confirmDialog())(ctor.prototype, '$standardDialogConfirmationAction');

            if (properties.node && nodeTypes) {
                addCustomFieldsToPageBody(nodeTypes, pageMetadata, ctor);
            }

            return new PageDecorator(
                ctor,
                pageMetadata.screenId,
                {
                    pageMetadata,
                    properties,
                },
                ContainerKey.Page,
                nodeTypes,
                dataTypes,
                {
                    getFocussedField,
                    dispatchPageValidation: (): Promise<{
                        allErrors: ValidationResult[];
                        blockingErrors: ValidationResult[];
                    }> => dispatchContainerValidation(pageMetadata.screenId, pageMetadata.screenId),
                },
            ).build().controlObject;
        };
    };
}
