.e-print-record-button {
    color: var(--colorsActionMinor500);
    border-radius: 8px;
    padding-left: 4px;
    padding-right: 8px;
    margin-top: -4px;

    [data-component="icon"]::before {
        color: var(--colorsActionMinor500);
    }

    &[aria-expanded="true"],
    &:hover,
    &:focus {
        background: var(--colorsActionMinor500) !important;
        border-color: var(--colorsActionMinor500) !important;
        color: var(--colorsYang100);

        [data-component="icon"]::before {
            color: var(--colorsYang100);
        }
    }
}

.e-print-record-dropdown-button:not(:disabled) {
    color: var(--colorsActionMinor500);

    &[aria-expanded="true"],
    &:hover,
    &:focus {
        background: var(--colorsActionMinor500) !important;
        border-color: var(--colorsActionMinor500) !important;
        color: var(--colorsYang100);

        [data-component="icon"]::before {
            color: var(--colorsYang100);
        }
    }
}