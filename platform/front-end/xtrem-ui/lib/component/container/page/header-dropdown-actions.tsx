import * as tokens from '@sage/design-tokens/js/base/common';
import { noop } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import type { ContextType } from '../../../types';
import { getElementAccessStatus } from '../../../utils/access-utils';
import type { MenuSeparatorResult } from '../../../utils/action-menu-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { getPageDefinitionFromState, getPagePropertiesFromState } from '../../../utils/state-utils';
import { PageActionControlObject } from '../../page-action/page-action-control-object';
import type { PageActionProperties } from '../../page-action/page-action-control-object';
import type { XtremActionPopoverItemOrMenuSeparator } from '../../ui/xtrem-action-popover';
import XtremActionPopover from '../../ui/xtrem-action-popover';

export interface HeaderDropdownActionsExternalProps {
    contextType?: ContextType;
    screenId: string;
    actions?: Array<PageActionControlObject>;
}

export interface HeaderDropdownActionsProps extends HeaderDropdownActionsExternalProps {
    headerDropdownActions?: Array<XtremActionPopoverItemOrMenuSeparator>;
}

export function HeaderDropdownActions({
    contextType,
    headerDropdownActions,
}: HeaderDropdownActionsProps): React.ReactElement | null {
    if (!headerDropdownActions) {
        return null;
    }

    return (
        <span className="e-header-dropdown-action-container">
            <XtremActionPopover
                items={headerDropdownActions}
                isOverSidebar={contextType === 'dialog'}
                noIconSupport={false}
                color={tokens.colorsUtilityMajor450}
                pendoId="headerDropdownActions"
            />
        </span>
    );
}

export const ConnectedHeaderDropdownActions = connect(
    (state: xtremRedux.XtremAppState, props: HeaderDropdownActionsExternalProps) => {
        const pageProperties = getPagePropertiesFromState(props.screenId, state);
        const pageDefinition = getPageDefinitionFromState(props.screenId, state);

        const rawActions: Array<PageActionControlObject | MenuSeparatorResult> =
            props.actions ??
            resolveByValue<Array<PageActionControlObject | MenuSeparatorResult>>({
                skipHexFormat: true,
                screenId: props.screenId,
                propertyValue: pageProperties.headerDropDownActions,
                rowValue: null,
                fieldValue: null,
            });

        if (!rawActions) {
            return props;
        }

        const resolvedPopoverItems: Array<XtremActionPopoverItemOrMenuSeparator> = rawActions.map(e => {
            if (!(e instanceof PageActionControlObject) && e.isMenuSeparator) {
                // INFO: This is a menu separator injected via ui.menuSeparator()
                return {
                    key: e.id,
                    isMenuSeparator: true,
                } as XtremActionPopoverItemOrMenuSeparator;
            }

            const actionProperties = pageDefinition.metadata.uiComponentProperties[e.id] as PageActionProperties;
            const accessRule = getElementAccessStatus({
                accessBindings: pageDefinition.accessBindings,
                bind: e.id,
                elementProperties: actionProperties,
                contextNode: pageProperties?.node,
                nodeTypes: state.nodeTypes,
                dataTypes: state.dataTypes,
            });

            const isDisabled = resolveByValue({
                screenId: props.screenId,
                propertyValue: actionProperties.isDisabled,
                skipHexFormat: true,
                rowValue: null,
                fieldValue: null,
            });

            const isHidden =
                resolveByValue({
                    screenId: props.screenId,
                    propertyValue: actionProperties.isHidden,
                    skipHexFormat: true,
                    rowValue: null,
                    fieldValue: null,
                }) ||
                (actionProperties.access && accessRule !== 'authorized');

            const isMenuSeparator = resolveByValue({
                screenId: props.screenId,
                propertyValue: actionProperties.isMenuSeparator,
                skipHexFormat: true,
                rowValue: null,
                fieldValue: null,
            });

            const title = resolveByValue({
                screenId: props.screenId,
                propertyValue: actionProperties.title,
                skipHexFormat: true,
                rowValue: null,
                fieldValue: null,
            });

            return {
                icon: actionProperties.icon,
                isDestructive: actionProperties.isDestructive,
                isDisabled,
                isHidden,
                isMenuSeparator,
                key: e.id,
                onClick: e.isMenuSeparator
                    ? noop
                    : (): Promise<void> => {
                          state.applicationContext?.onTelemetryEvent?.(`headerDropdownMenuItemTriggered-${e.id}`, {
                              screenId: props.screenId,
                              elementId: e.id,
                          });
                          return triggerFieldEvent(props.screenId, e.id, 'onClick');
                      },
                pendoId: e.isMenuSeparator ? undefined : `${props.screenId}-headerDropdownAction-${e.id}`,
                title,
            };
        });

        return {
            ...props,
            headerDropdownActions: resolvedPopoverItems,
        };
    },
)(HeaderDropdownActions);
