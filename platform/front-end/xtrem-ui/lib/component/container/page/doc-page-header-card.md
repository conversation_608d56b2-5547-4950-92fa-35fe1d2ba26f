PATH: XTREEM/Client+Framework/Page+Header+card

The header card is a fixed header element that can contain up to 6 fields. The page header card is available for mobile screen sizes only.

## Introduction

The header card can be used to display key pieces of information. Fields in the header card are always read-only regardless to their configuration. Only the following field types can be used in the header card: text, numeric, reference and label. Due to space constraints, the label of the fields are not displayed, ony their value.

```ts
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<HeaderCardPage>({
    title: 'Three lines',
    headerCard() {
        return {
            title: this.headerText1,
            titleRight: this.headerText2,
            line2: this.headerText3,
            line2Right: this.headerText4,
            line3: this.headerText5,
            line3Right: this.headerText6,
            line4: this.headerText6,
            line4Right: this.headerText7,
            line5: this.headerText8,
            line5Right: this.headerText9,
        };
    },
})
export class HeaderCardPage extends ui.Page {
    @ui.decorators.textField<HeaderCardPage>({
        isReadOnly: true,
    })
    headerText1: ui.fields.Text;

    @ui.decorators.textField<HeaderCardPage>({})
    headerText2: ui.fields.Text;

    @ui.decorators.textField<HeaderCardPage>({})
    headerText3: ui.fields.Text;

    @ui.decorators.textField<HeaderCardPage>({
        isReadOnly: true,
    })
    headerText4: ui.fields.Text;

    @ui.decorators.textField<HeaderCardPage>({})
    headerText5: ui.fields.Text;

    @ui.decorators.textField<HeaderCardPage>({
        isReadOnly: true,
    })
    headerText6: ui.fields.Text;

    @ui.decorators.textField<HeaderCardPage>({})
    headerText7: ui.fields.Text;

    @ui.decorators.textField<HeaderCardPage>({
        isReadOnly: true,
    })
    headerText8: ui.fields.Text;

    @ui.decorators.textField<HeaderCardPage>({})
    headerText9: ui.fields.Text;
}

```

## Decorator properties

The header card can be defined by using the `headerCard()` callback property on the page decorator. The function has access to the page scope and expected to return an object with references to fields on the class as shown in the example above.

The object expected to contain the following properties:
- title
- titleRight
- line2
- line2Right
- line3
- line3Right
- line4
- line4Right
- line5
- line5Right
- image
