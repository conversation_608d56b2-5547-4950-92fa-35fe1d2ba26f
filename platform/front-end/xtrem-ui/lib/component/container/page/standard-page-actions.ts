import { datetime, formatDateToCurrentLocale, formatTimeToLocale } from '@sage/xtrem-date-time';
import { isEmpty } from 'lodash';
import { getStore } from '../../../redux/store';
import { localize } from '../../../service/i18n-service';
import type { Page } from '../../../service/page';
import { showContainerValidationToast } from '../../../service/toast-service';
import {
    DUPLICATE_INDICATOR_QUERY_EDITOR_PARAM,
    NEW_PAGE,
    SHOULD_REFRESH_DIALOG_RESULT,
} from '../../../utils/constants';
import { triggerScreenEvent } from '../../../utils/events';
import { getPageDefinitionFromState, getPagePropertiesFromPageDefinition } from '../../../utils/state-utils';
import type { PageActionDecoratorProperties } from '../../decorators';

const isInvalidRecordId = (_id: any): boolean =>
    _id === null || _id === undefined || _id === '' || _id === 0 || _id === '0';

export const remove = (): PageActionDecoratorProperties<any> => ({
    title: localize('@sage/xtrem-ui/crud-delete', 'Delete'),
    icon: 'delete',
    buttonType: 'tertiary',
    isDestructive: true,
    access: {
        bind: '$delete',
    },
    async onClick(this: Page, _id = this.$.recordId): Promise<void> {
        try {
            await this.$.dialog.confirmation(
                'warn',
                this.$standardDeletePromptTitle,
                this.$standardDeletePromptMessage,
                {
                    acceptButton: {
                        text: this.$standardDeleteButton,
                        isDestructive: true,
                    },
                    cancelButton: {
                        text: this.$standardCancelButton,
                    },
                },
            );
        } catch {
            // The error here means that the user closed the dialog and decided not to proceed.
            return;
        }

        this.$.loader.isHidden = false;
        await this.$.graph.delete({ _id });
        this.$.showToast(localize('@sage/xtrem-ui/crud-delete-successfully', 'Record has been deleted successfully.'), {
            type: 'success',
        });
        if (this.$.isInDialog) {
            this.$.finish({ _id }); // return ID
        } else {
            this.$.setPageClean();
            await this.$.router.closeRecord(true);
        }
        this.$.loader.isHidden = true;
    },
    onError(error: Error): string {
        this.$.loader.isHidden = true;
        return error.message;
    },
});

export const save = (): PageActionDecoratorProperties<any> => ({
    title: localize('@sage/xtrem-ui/crud-save', 'Save'),
    buttonType: 'primary',
    access: {
        bind: '$create',
    },
    async onClick(this: Page): Promise<void> {
        const developerApi = this.$;
        this.$.loader.isHidden = false;
        const validationResults = await developerApi.page.validateWithDetails(true);
        const isInTunnel = this.$.isInTunnel;
        if (validationResults.blockingErrors.length === 0) {
            const _id = this.$.recordId;
            if (_id !== null && _id !== undefined && _id !== '' && Number(_id) !== 0 && _id !== NEW_PAGE) {
                await this.$.graph.update({}, isInTunnel);
                this.$.showToast(this.$standardSaveSuccessMessage, { type: 'success' });
                if (isInTunnel) {
                    this.$.loader.isHidden = true;
                    this.$.finish({ _id: this.$.queryParameters._id, [SHOULD_REFRESH_DIALOG_RESULT]: true }); // return ID
                    return Promise.resolve();
                }
                this.$.setPageClean();
            } else {
                await this.$.graph.create();
                this.$.showToast(this.$standardCreateSuccessMessage, { type: 'success' });
                if (isInTunnel) {
                    this.$.loader.isHidden = true;
                    this.$.finish({ _id: this.$.recordId, [SHOULD_REFRESH_DIALOG_RESULT]: true }); // return ID
                    return Promise.resolve();
                }
                this.$standardDeleteAction.isDisabled = false;
                this.$standardDuplicateAction.isDisabled = false;
                this.$.setPageClean();
                await triggerScreenEvent(this.$.page.id, 'onLoad');
            }
            if (this.$.isInDialog) {
                this.$.finish({ _id: this.$.queryParameters._id }); // return ID
            }
        } else {
            showContainerValidationToast(this._pageMetadata, validationResults);

            this.$.loader.isHidden = true;
            return Promise.reject();
        }
        this.$.loader.isHidden = true;
        return Promise.resolve();
    },
    onError(error: Error): string | undefined {
        if (!error) {
            // The errors are already taken care of if the validation fails.
            return undefined;
        }
        this.$.loader.isHidden = true;
        return error.message || this.$standardSaveFailedMessage;
    },
});

export const confirmDialog = (): PageActionDecoratorProperties<any> => ({
    title: localize('@sage/xtrem-ui/crud-confirm', 'Confirm'),
    buttonType: 'primary',
    async onClick(this: Page): Promise<void> {
        const developerApi = this.$;
        this.$.loader.isHidden = false;
        const validationResults = await developerApi.page.validateWithDetails(true);
        if (validationResults.blockingErrors.length === 0) {
            this.$.finish(this.getSerializedValues());
        } else {
            showContainerValidationToast(this._pageMetadata, validationResults);
            this.$.loader.isHidden = true;
            return Promise.reject();
        }
        this.$.loader.isHidden = true;
        return Promise.resolve();
    },
    onError(error: Error): string | undefined {
        if (!error) {
            // The errors are already taken care of if the validation fails.
            return undefined;
        }
        this.$.loader.isHidden = true;
        return error.message || this.$standardSaveFailedMessage;
    },
});

export const duplicate = (): PageActionDecoratorProperties<any> => ({
    title: localize('@sage/xtrem-ui/crud-duplicate', 'Duplicate'),
    icon: 'duplicate',
    access: {
        bind: '$create',
    },

    async onClick(this: Page, _id = this.$.recordId): Promise<void> {
        if (isInvalidRecordId(_id)) {
            throw new Error(`Cannot duplicate without a valid record ID: ${_id}`);
        }

        const pageDefinition = getPageDefinitionFromState(this.$.page.id);
        const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);

        if (!pageProperties.node) {
            throw new Error(`Cannot duplicate without a valid node`);
        }

        const duplicateBindings = pageDefinition.metadata.duplicateBindings || [];
        let result;

        // We prompt the user to fill the fields if there are duplicate bindings provided by the server
        if (duplicateBindings.length > 0) {
            try {
                result = await this.$.dialog.page(
                    pageDefinition.path,
                    {
                        _id: _id as string,
                        [DUPLICATE_INDICATOR_QUERY_EDITOR_PARAM]: true,
                    },
                    {
                        title: localize('@sage/xtrem-ui/crud-duplicate-dialog-title', 'Duplicate record'),
                        subtitle: localize(
                            '@sage/xtrem-ui/crud-duplicate-dialog-subtitle',
                            'Complete the fields below to duplicate this record',
                        ),
                        isDuplicate: true,
                    },
                );
            } catch (error) {
                // Duplication cancel by the user
                return;
            }
        } else {
            this.$.loader.isHidden = false;
            // The empty object argument is important, because this way the duplication happens fully on the server
            result = await this.$.graph.duplicate({ values: {}, _id });
            this.$.loader.isHidden = true;
        }

        const duplicationId = result?._id || result;

        if (duplicationId && duplicationId > 0) {
            await this.$.router.selectRecord(duplicationId);
            this.$.showToast(
                localize('@sage/xtrem-ui/crud-duplicate-successfully', 'Record was duplicated successfully.'),
                {
                    type: 'success',
                },
            );
        }
    },
    onError(error: Error): string | undefined {
        this.$.loader.isHidden = true;
        if (!error) {
            // The errors are already taken care of if the validation fails.
            return undefined;
        }
        return error.message || this.$standardDuplicateFailedMessage;
    },
});

export const executeDuplication = (): PageActionDecoratorProperties<any> => ({
    title: localize('@sage/xtrem-ui/crud-duplicate', 'Duplicate'),
    access: {
        bind: '$create',
    },
    async onClick(this: Page, _id = this.$.queryParameters._id): Promise<void> {
        if (isInvalidRecordId(_id)) {
            throw new Error(`Cannot duplicate without a valid record ID: ${_id}`);
        }

        this.$standardExecuteDuplicationAction.isDisabled = true;
        const developerApi = this.$;
        this.$.loader.isHidden = false;
        const validationResults = await developerApi.page.validateWithDetails(true);
        if (validationResults.blockingErrors.length === 0) {
            const pageValues = this.$.values;
            const duplicateBindings = this._pageMetadata.duplicateBindings || [];
            const values = duplicateBindings.reduce((prevValue, key) => {
                return { ...prevValue, [key]: pageValues[key] };
            }, {});

            const duplicateRecordId = await this.$.graph.duplicate({ values });

            if (this.$.isInDialog) {
                this.$.finish({ _id: duplicateRecordId }); // return ID
            }
            return Promise.resolve();
        }
        showContainerValidationToast(this._pageMetadata, validationResults);

        this.$.loader.isHidden = true;
        this.$standardExecuteDuplicationAction.isDisabled = false;
        return Promise.reject();
    },
    onError(error: Error): string | undefined {
        if (!error) {
            // The errors are already taken care of if the validation fails.
            return undefined;
        }
        this.$.loader.isHidden = true;
        this.$standardExecuteDuplicationAction.isDisabled = false;

        return error.message || this.$standardDuplicateFailedMessage;
    },
});

export const create = (): PageActionDecoratorProperties<any> => ({
    title: localize('@sage/xtrem-ui/crud-create', 'Create'),
    icon: 'add',
    buttonType: 'primary',
    access: {
        bind: '$create',
    },
    async onClick(this: Page): Promise<void> {
        await this.$.router.emptyPage();
    },
});

export const cancel = (): PageActionDecoratorProperties<any> => ({
    title: localize('@sage/xtrem-ui/crud-cancel', 'Cancel'),
    buttonType: 'tertiary',
    async onClick(this: Page): Promise<void> {
        if (!this.$.isInDialog) {
            await this.$.router.refresh();
        }
        this.$.setPageClean();
        if (this.$.isInDialog) {
            if (this.$.queryParameters[DUPLICATE_INDICATOR_QUERY_EDITOR_PARAM]) {
                this.$.finish();
            } else {
                const _id = this.$.values._id || this.$.queryParameters._id;
                this.$.finish({ _id }); // return ID
            }
        }
    },
});

export const openCustomizationPageWizard = (): PageActionDecoratorProperties<any> => ({
    title: localize('@sage/xtrem-ui/open-custom-field-dialog', 'Create field'),
    icon: 'settings',
    isHidden(this: Page): boolean {
        return (
            this.$.isDirty ||
            isEmpty(this._pageMetadata.customizableNodesWizard) ||
            !getStore().getState().customizationWizardPage
        );
    },
    onError(): void {
        // Intentionally left empty. If the dialog is closed with no result we don't proceed.
    },
    async onClick(this: Page): Promise<void> {
        const { customizationWizardPage } = getStore().getState();
        if (customizationWizardPage) {
            try {
                await this.$.dialog.page(
                    customizationWizardPage,
                    {
                        customizableNodesWizard: JSON.stringify(this._pageMetadata.customizableNodesWizard),
                    },
                    {
                        height: 450,
                        size: 'large',
                    },
                );
            } catch {
                return;
            }
            await this.$.commitValueAndPropertyChanges();
            this.$.setPageClean();
            this.$.router.hardRefresh();
        }
    },
});

export const openRecordHistory = (): PageActionDecoratorProperties<any> => ({
    title: localize('@sage/xtrem-ui/open-record-history-dialog', 'Record history'),
    icon: 'refresh_clock',
    onError(): string {
        return localize('@sage/xtrem-ui/record-history-failed', 'Failed to get the record history. Try again later.');
    },
    isHidden(this: Page): boolean {
        return (
            !!this.$.page.properties.isTransient ||
            !this.$.page.properties.node ||
            !!this.$.isNewPage ||
            !this.$.recordId
        );
    },
    async onClick(this: Page, _id = this.$.recordId): Promise<void> {
        if (
            !this.$.locale ||
            !!this.$.page.properties.isTransient ||
            !this.$.page.properties.node ||
            !!this.$.isNewPage ||
            isInvalidRecordId(_id)
        ) {
            return;
        }

        const result = await this.$.graph
            .node(this.$.page.properties.node)
            .read(
                {
                    _createStamp: true,
                    _updateStamp: true,
                    _createUser: { displayName: true },
                    _updateUser: { displayName: true },
                },
                _id,
            )
            .execute();

        getStore()
            .getState()
            ?.applicationContext?.onTelemetryEvent?.('recordHistoryOpened', { screenId: this.$.page.id });

        const createStamp = datetime.parse(result._createStamp);
        const createdAt = formatTimeToLocale(createStamp, this.$.locale);
        const createdOn = formatDateToCurrentLocale(createStamp, this.$.locale);
        const createdBy = result._createUser.displayName;

        const updateStamp = datetime.parse(result._updateStamp);
        const updatedAt = formatTimeToLocale(updateStamp, this.$.locale);
        const updatedOn = formatDateToCurrentLocale(updateStamp, this.$.locale);
        const updatedBy = result._updateUser.displayName;

        const dialogHistory = localize('@sage/xtrem-ui/open-record-history-dialog', 'Record history');
        const creationHeader = localize('@sage/xtrem-ui/record-history-created-title', 'Created');
        const lastUpdateHeader = localize('@sage/xtrem-ui/record-history-last-update-title', 'Last update');

        const creationDetails = localize(
            '@sage/xtrem-ui/record-history-details',
            'By **{{name}}** on **{{date}}** at **{{time}}**',
            { name: createdBy, date: createdOn, time: createdAt },
        );

        const lastUpdateDetails = localize(
            '@sage/xtrem-ui/record-history-details',
            'By **{{name}}** on **{{date}}** at **{{time}}**',
            { name: updatedBy, date: updatedOn, time: updatedAt },
        );

        this.$.dialog.message(
            'info',
            dialogHistory,
            `
## ${creationHeader}
${creationDetails}

## ${lastUpdateHeader}
${lastUpdateDetails}
        `,
            {
                mdContent: true,
            },
        );
    },
});
