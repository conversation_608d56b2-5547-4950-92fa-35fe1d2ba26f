import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import type { ScreenExtension } from '../../../types';
import { AbstractContainer } from '../../abstract-container';
import type { ContainerComponentProps, ContainerKey } from '../../types';

/**
 * Page fragment that holds any number of fields. In order to use it you need to define a fragmentFields field.
 */
export class PageFragmentControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends AbstractContainer<
    CT,
    ContainerKey.PageFragment,
    ContainerComponentProps<ContainerKey.PageFragment>
> {
    /**
     * Triggers the field validation rules. Since the validation rules might be asynchronous,
     * this method returns a promise that must be awaited to get the validation result. Compared to the `validate` method
     * it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
     */
    async validateWithDetails(
        partition: true,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
    async validateWithDetails(partition: false): Promise<ValidationResult[]>;
    async validateWithDetails(): Promise<ValidationResult[]>;
    async validateWithDetails(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _partition = false,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] } | ValidationResult[]> {
        // Never used, but required by the AbstractContainer
        return [];
    }
}
