import * as React from 'react';
import { ConnectedDashboardComponent } from '../../../dashboard/dashboard-component';
import { useSelector } from 'react-redux';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import type * as xtremRedux from '../../../redux';

export interface Page360ViewProps {
    screenId: string;
}

export function Page360View({ screenId }: Page360ViewProps): React.ReactElement | null {
    const recordId = useSelector<xtremRedux.XtremAppState, string | null>(
        state => getPageDefinitionFromState(screenId, state)?.selectedRecordId,
    );

    if (!recordId) {
        return null;
    }
    return (
        <div className="e-page-360-view">
            <ConnectedDashboardComponent group={`${screenId}-360`} dashboardContextVariables={{ recordId }} />
        </div>
    );
}
