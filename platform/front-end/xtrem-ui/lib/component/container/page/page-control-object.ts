/**
 * @packageDocumentation
 * @module root
 * */

import { getStore } from '../../../redux';
import type { ContainerValidationResult } from '../../../service/dispatch-service';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { getUiComponentProperties, setUiComponentProperties } from '../../../service/transactions-service';
import type { ScreenExtension } from '../../../types';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { AbstractContainer } from '../../abstract-container';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { ContainerComponentProps } from '../../types';
import { ContainerKey } from '../../types';
import type { IPageControlObject, PageMode, PageNavigationPanel, PageProperties } from './page-types';

/**
 * Main [container]{@link AbstractContainer} of each screen. Holds any number of [sections]{@link SectionControlObject}
 * and might define additional parts (e.g. crud actions, business actions, a helper panel, etc.)
 */
export class PageControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends AbstractContainer<
    CT,
    ContainerKey.Page,
    ContainerComponentProps<ContainerKey.Page>
> {
    private readonly _dispatchPageValidation: IPageControlObject['dispatchPageValidation'];

    private readonly _getFocussedField: IPageControlObject['getFocussedField'];

    static readonly defaultUiProperties: Partial<PageProperties> = {
        ...AbstractContainer.defaultUiProperties,
        areNavigationTabsHidden: false,
        mode: 'default',
    };

    constructor(properties: IPageControlObject) {
        super(
            properties.screenId,
            properties.screenId,
            properties.getUiComponentProperties || getUiComponentProperties,
            properties.setUiComponentProperties || setUiComponentProperties,
            ContainerKey.Page,
            properties.getValidationState ||
                (async (): Promise<boolean> => {
                    const validationErrors = await this.validateWithDetails();
                    return validationErrors.length === 0;
                }),
            properties.layout,
        );

        this._dispatchPageValidation = properties.dispatchPageValidation;
        this._getFocussedField = properties.getFocussedField;
    }

    /** Visualization mode */
    get mode(): PageMode | undefined {
        return this.getUiComponentProperty('mode');
    }

    /** Panel where to search, filter and navigate through current page related items */
    get navigationPanel(): PageNavigationPanel<any, any> | undefined {
        return this.getUiComponentProperty('navigationPanel');
    }

    /**
     * The GraphQL node that the page is bind to. This settings will be omitted
     * if the page is set to be transient
     */
    get node(): string {
        return String(this.getUiComponentProperty('node'));
    }

    get focussedField(): ReadonlyFieldControlObject<CT, any, any, any> | null {
        return this._getFocussedField(this.screenId) as ReadonlyFieldControlObject<CT, any, any, any>;
    }

    @ControlObjectProperty<PageProperties, PageControlObject<CT>>()
    /** Subtitle displayed along with the page's title */
    subtitle?: string;

    // TODO Move this method to the abstract container
    /**
     * Triggers the validation rules of all the fields of the page. Since the validation rules
     * might be asynchronous, this method returns a promise that must be awaited to get
     * the validation result
     */
    async validate(): Promise<string[]> {
        const result = await this.validateWithDetails();
        return result.map(r => r!.message);
    }

    /**
     * Triggers the field validation rules. Since the validation rules might be asynchronous,
     * this method returns a promise that must be awaited to get the validation result. Compared to the `validate` method
     * it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
     */
    async validateWithDetails(partition: true): Promise<ContainerValidationResult>;
    async validateWithDetails(partition: false): Promise<ValidationResult[]>;
    async validateWithDetails(): Promise<ValidationResult[]>;
    async validateWithDetails(partition = false): Promise<ContainerValidationResult | ValidationResult[]> {
        const errors = await this._dispatchPageValidation();
        return partition ? errors : errors.allErrors;
    }
}

// TODO: TS 4.0 had to move the accessors outside of the class - there may be a better solution
Object.defineProperty(PageControlObject.prototype, 'title', {
    get(this: PageControlObject) {
        return (
            resolveByValue({
                propertyValue: this.getUiComponentProperty('title'),
                screenId: this.screenId,
                skipHexFormat: true,
                rowValue: null,
            }) || ''
        );
    },
    set(this: PageControlObject, newTitle: string) {
        this.setUiComponentProperties('title', newTitle);
        const state = getStore().getState();

        if (state.applicationContext?.onPageTitleChange) {
            const subtitle = resolveByValue({
                propertyValue: this.getUiComponentProperty('subtitle'),
                screenId: this.screenId,
                skipHexFormat: true,
                rowValue: null,
            });
            state.applicationContext?.onPageTitleChange(newTitle, subtitle || null);
        }
    },
});

Object.defineProperty(PageControlObject.prototype, 'subtitle', {
    get(this: PageControlObject) {
        return (
            resolveByValue({
                propertyValue: this.getUiComponentProperty('subtitle'),
                screenId: this.screenId,
                skipHexFormat: true,
                rowValue: null,
            }) || ''
        );
    },
    set(this: PageControlObject, newSubtitle: string) {
        this.setUiComponentProperties('subtitle', newSubtitle);
        const state = getStore().getState();

        if (state.applicationContext?.onPageTitleChange) {
            const title = resolveByValue({
                propertyValue: this.getUiComponentProperty('title'),
                screenId: this.screenId,
                skipHexFormat: true,
                rowValue: null,
            });
            state.applicationContext?.onPageTitleChange(title || null, newSubtitle);
        }
    },
});
