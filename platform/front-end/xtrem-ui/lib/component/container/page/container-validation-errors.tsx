import React from 'react';
import { localize } from '../../../service/i18n-service';
import type { PageMetadata } from '../../../service/page-metadata';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import { partition } from 'lodash';
import { ConnectedNestedFieldErrors } from '../../ui/nested-field-errors-component';

interface ContainerValidationErrorsComponentProps {
    screenId: string;
    validationResults: ValidationResult[];
    pageMetadata: PageMetadata;
    children?: React.ReactNode;
}

export function ContainerValidationErrorsComponent({
    validationResults,
    pageMetadata,
    screenId,
    children,
}: ContainerValidationErrorsComponentProps): React.ReactElement {
    const [nestedValidationErrors, nonNestedValidationErrors] = partition(validationResults, e => !!e.columnId);
    const nestedElementIds = Array.from(new Set(nestedValidationErrors.map(e => e.elementId)));
    return (
        <div className="e-page-validation-error-list">
            <h6>{localize('@sage/xtrem-ui/validation-errors', 'Validation Errors')}</h6>
            <ul>
                {nonNestedValidationErrors.map(({ message, elementId }) => {
                    const fieldProperties = pageMetadata.uiComponentProperties[elementId] as ReadonlyFieldProperties;
                    const title = resolveByValue({
                        screenId: pageMetadata.screenId,
                        skipHexFormat: true,
                        propertyValue: fieldProperties.title,
                        rowValue: undefined,
                    });
                    return (
                        <li key={elementId}>
                            <p>
                                <strong>{title || elementId}:</strong>
                                &#32;
                                {message}
                            </p>
                        </li>
                    );
                })}
                {nestedElementIds.map(elementId => {
                    return (
                        <li key={elementId}>
                            <ConnectedNestedFieldErrors screenId={screenId} elementId={elementId} />
                        </li>
                    );
                })}
                {children}
            </ul>
        </div>
    );
}
