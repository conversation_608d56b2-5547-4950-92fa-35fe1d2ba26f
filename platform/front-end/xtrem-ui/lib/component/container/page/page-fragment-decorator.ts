import type { Dict } from '@sage/xtrem-shared';
import type { Page } from '../../../service/page';
import { type PageMetadata, getPageMetadata } from '../../../service/page-metadata';
import type { Constructible, PageExtension } from '../../../types';
import { AbstractDecorator } from '../../abstract-decorator';
import { PageFragmentControlObject } from '../../control-objects';
import type { ContainerProperties, BlockControlObject, UiComponentProperties } from '../../control-objects';
import type { HasParent } from '../../field/traits';
import type { ContainerKey } from '../../types';
import { getTargetPrototype } from '../../../utils/decorator-utils';
import type { PageDecoratorProperties } from './page-decorator';
import { injectOnloadAfterIntoPageMetadata } from '../../../utils/page-utils';

export interface PageFragmentDecoratorProperties<CT extends PageExtension<CT> = PageExtension<any>>
    extends HasParent<CT, BlockControlObject<CT>>,
        ContainerProperties<CT> {
    /** Function that will be executed immediately after the page fragment has been loaded */
    onLoad?: (this: CT) => void;
}

interface RuntimePageMetadata extends PageMetadata {
    uiComponentProperties: Dict<
        UiComponentProperties & {
            onLoadAfter: Array<PageDecoratorProperties<any>['onLoad']>;
            onCloseAfter: Array<PageDecoratorProperties<any>['onClose']>;
        }
    >;
    defaultUiComponentProperties: Dict<
        UiComponentProperties & {
            onLoadAfter: Array<PageDecoratorProperties<any>['onLoad']>;
            onCloseAfter: Array<PageDecoratorProperties<any>['onClose']>;
        }
    >;
}

export class PageFragmentDecorator extends AbstractDecorator<ContainerKey.PageFragment> {
    protected _controlObjectConstructor = PageFragmentControlObject as any;
}

/**
 * Initializes the decorated member as a [Page]{@link PageFragmentControlObject} container with the provided properties
 *
 * @param properties The properties that the [Page]{@link PageFragmentControlObject} container will be initialized with
 */
export function pageFragment<CT extends PageExtension<CT>>(properties: PageFragmentDecoratorProperties<CT>) {
    return (ctor: Constructible<Page>): void => {
        // Get the main page metadata object
        const pageMetadata = getPageMetadata(getTargetPrototype(ctor)) as RuntimePageMetadata;

        pageMetadata.pageFragmentThunks.push((): void => {
            if (properties.onLoad) {
                injectOnloadAfterIntoPageMetadata(pageMetadata, properties.onLoad);
            }
        });
    };
}
