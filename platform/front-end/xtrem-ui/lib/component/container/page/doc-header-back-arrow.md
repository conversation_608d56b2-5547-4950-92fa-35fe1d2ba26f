PATH: XTREEM/Page/Detail+Panel

Modifier of the page header behavior that introduces a contextual **back navigation button**. It allows users to return to a previous page, based on internal in-app navigation history.

---

## Introduction

The **Back Navigation** functionality introduces a contextual arrow icon in the top-left of the page header. This feature maintains an internal navigation stack that is only updated when users navigate between pages via **in-application links**, such as buttons or anchor elements within a page, **and only when the navigation results in a change of scope** (for example, from a `Customer` context to a `Sales` context).

Navigations triggered via the **main navigation menu** do not populate the internal history stack and reset it entirely.

No decorator is required to activate this behavior. Pages will automatically participate in the back stack as long as they are navigated to using internal application routing and result in a scope change.

---

## Functional scope

- The back arrow icon is displayed to the left of the page title (or record title).
- The navigation history stack is updated **only** when navigating between **distinct scopes** via in-application links.
- A **scope change** typically occurs when transitioning from one node or entity type to another (e.g., from `Customer` to `Sales`).
- The back arrow is **not** shown when:
  - The user accesses a page via the main menu
  - The user navigates within the same scope (e.g., selecting different records or tabs)
- The back stack is reset every time a page is accessed from the menu
- If the current page is **dirty**, a confirmation dialog is displayed when the back arrow is clicked

---
