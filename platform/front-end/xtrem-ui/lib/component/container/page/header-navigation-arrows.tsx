import * as React from 'react';
import { getRouter } from '../../../service/router';
import type * as xtremRedux from '../../../redux';
import { connect } from 'react-redux';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import NavigationArrows from '../../ui/navigation-arrows/navigation-arrows';

interface HeaderNavigationArrowsExternalProps {
    screenId: string;
}

interface HeaderNavigationArrowsProps extends HeaderNavigationArrowsExternalProps {
    selectedRecordId?: string | null;
}

export function HeaderNavigationArrows({
    selectedRecordId,
    screenId,
}: HeaderNavigationArrowsProps): React.ReactElement {
    const router = getRouter(screenId);

    const [hasNextRecord, setHasNextRecord] = React.useState<boolean>(false);
    const [hasPreviousRecord, setPreviousRecord] = React.useState<boolean>(false);

    React.useEffect(() => {
        router.hasNextRecord().then(setHasNextRecord);
    }, [selectedRecordId, router]);

    React.useEffect(() => {
        router.hasPreviousRecord().then(setPreviousRecord);
    }, [selectedRecordId, router]);

    return (
        <NavigationArrows
            hasNextRecord={hasNextRecord}
            hasPreviousRecord={hasPreviousRecord}
            onNextRecord={(): Promise<void> => router.nextRecord()}
            onPreviousRecord={(): Promise<void> => router.previousRecord()}
        />
    );
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: HeaderNavigationArrowsExternalProps,
): HeaderNavigationArrowsProps => {
    const pageDefinition = getPageDefinitionFromState(props.screenId, state);

    return {
        ...props,
        selectedRecordId: pageDefinition.selectedRecordId,
    };
};

const ConnectedHeaderNavigationArrows = connect<HeaderNavigationArrowsProps, {}, HeaderNavigationArrowsExternalProps>(
    mapStateToProps,
)(HeaderNavigationArrows);

export default ConnectedHeaderNavigationArrows;
