PATH: XTREEM/Client+Framework/Standard+Page+Actions


The client framework provides standard page action implementations, which can be used in pages. This document lists these actions and summarizes their functionality.

## Introduction

The standard CRUD actions are `PageAction` instances that are declared on the abstract page object, therefore all page instances have access to them. They are available directly on the `this` scope of the page. The standard CRUD actions only work on non-transient pages with a valid `node` decorator property value.

## How standard CRUD actions can be used?

Like any other `PageAction`s, the standard CRUD actions can be used two ways:

1. **Assigning them to any decorator that can use page actions:**
The standard CRUD actions can be used in any place where `PageAction` instances are used.
```ts
@ui.decorators.page<MyPage>({
    ...
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return [this.refreshPage, this.emptyPage, this.$standardSaveAction];
    },
    createAction() {
        return this.$standardNewAction;
    },
    ...
})
export class MyPage extends ui.Page<GraphApi> {
    ...
}
```

2. **Invoking them directly from other event handlers:**
This can be achived by using the `execute()` function of the page actions. For example:
```ts
    async onChange() {
        ...
        await this.$standardSaveAction.execute();
        ...
    }
```

## $standardNewAction
The `$standardNewAction` clears any data from the page, unsets the query parameters and navigates to an empty page instance after displaying the "Dirty page" confirmation dialog when applicable. Its functionality is equal to `this.$.router.emptyPage()`.

## $standardSaveAction
This action executes an "upsert" procedure, which means if the record on the page already has an `_id` query parameter or and `_id` bound field with a non-falsy value, it calls `update` mutation of the node which is defined on the page decorator. If no `_id` is available, it calls the `create` mutation.
##### Validation
Before calling the server, the action executes all client side validation rules which are defined on the page, except disabled fields and children fields of disabled containers. If the validation failes, the invalid fields get marked on the screen and a validation summary toast is displayed.
##### Payload
By default, the action sends the content `this.$.values` variable to the server. This can be overridden by implementing the `getSerializedValues()` abstract function on the page class.
##### Updating page content
Upon a successful mutation execution, the client requests new values for all non-transient fields that are defined on the page in order to display any calculated or updated values that may be modified by server control rules. The navigation panel content is also refreshed.
#### Success notfication
Upon a successful mutation execution, a success toast is displayed. The content of this toast can be changed by overriding the following abstract class variables:
- `this.$standardSaveSuccessMessage`: Used in case of updating an existing record
- `this.$standardCreateSuccessMessage`: Used in case of creating a new record

##### Server side errors
When the mutation failes and the server returns errors with a `path` property, the client marks the corresponding fields on the screen. In addition to that, a global error summary toast is displayed.
##### Special behaviour in page dialogs
If it is invoked within a page dialog, upon successful execution, the `this.$.finish()` method is called with the `_id` of the record that was saved.

## $standardDeleteAction
This action executes the `delete` mutation of the node that is bound to the page. First a confirmation dialog is displayed to the user. If the user proceeds, the `delete` mutation is executed.

##### Emptying page content
Upon a successful mutation execution, the client empties all fields from the page and navigates to an empty page instance.

##### Content of the confirmation dialog
The title and the message of the confirmation dialog can be changed by overriding the following abstract class variables:
- `this.$standardDeletePromptTitle`: Used in the title of the confirmation dialog
- `this.$standardDeletePromptMessage`: Used in the body of the confirmation dialog

##### Special behaviour in page dialogs
If it is invoked within a page dialog, upon successful execution, the `this.$.finish()` method is called with the `_id` of the record that was deleted.

## $standardCancelAction
Resets the page content to its original state. When applicable, the "Dirty page" confirmation dialog is displayed.
##### Special behaviour in page dialogs
The `this.$.finish()` method is called with the `_id` of the record that was loaded into the dialog page.

## $standardDuplicateAction
It opens a dialog with with a reduced copy of the page. In this dialog, only the fields that are marked required for duplication by the server rendered. This allows the user to provide a value for such fields. These values then sent to the server which creates a copy of the record. This action takes the `_id` optional argument, which is used instead of the current record id when supplied.

#### Error toast
If an error occurs getting the record values a toast is shown. The content of this toast can be changed by overriding the following abstract class variables:
- `this.$standardDuplicateFailedMessage`

## $standardOpenCustomizationPageWizardAction

Open the customization dialog wizard if a customization manager is defined in the application. It is only visible if the page is bound to a customizable node and the page is clean.

## $standardOpenRecordHistoryAction

Open the record history dialog. This dialog displays details about the record creation and the last update including the timestamp of the event and the name of the author. The timestamps are displayed in local time of the user.
