import * as React from 'react';
import { useSelector } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import type { PrintingSettings } from '../../../redux/state';
import * as dialogService from '../../../service/dialog-service';
import {
    QUERY_PARAM_PRINTING_NODE_TYPE,
    QUERY_PARAM_PRINTING_RECORD_ID,
    QUERY_PARAM_PRINTING_SOURCE_PAGE,
    QUERY_PARAM_PRINTING_SOURCE_TYPE,
} from '../../../utils/constants';
import Button from 'carbon-react/esm/components/button';
import MultiActionButton from 'carbon-react/esm/components/multi-action-button';
import { localize } from '../../../service/i18n-service';
import Icon from 'carbon-react/esm/components/icon';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';
import { getPagePropertiesFromState } from '../../../utils/state-utils';

export interface PrintRecordButtonProps {
    screenId: string;
}

export function PrintRecordButton({ screenId }: PrintRecordButtonProps): React.ReactElement | null {
    const printingSettings = useDeepEqualSelector<xtremRedux.XtremAppState, PrintingSettings | null>(
        state => state.printingSettings,
    );

    const selectedRecordId = useSelector<xtremRedux.XtremAppState, string | null>(
        state => state.screenDefinitions[screenId]?.selectedRecordId || null,
    );

    const pageNode = useDeepEqualSelector<xtremRedux.XtremAppState, string | null>(
        state => getPagePropertiesFromState(screenId, state)?.node?.toString() || null,
    );

    const openAssignmentDialog = React.useCallback(() => {
        if (!printingSettings?.printingAssignmentDialogUrl) {
            throw new Error('printingAssignmentDialogUrl is not defined');
        }

        dialogService.openPageDialog(
            printingSettings?.printingAssignmentDialogUrl,
            {
                [QUERY_PARAM_PRINTING_SOURCE_PAGE]: screenId,
                [QUERY_PARAM_PRINTING_SOURCE_TYPE]: 'record',
                [QUERY_PARAM_PRINTING_NODE_TYPE]: pageNode || '',
            },
            { resolveOnCancel: true, size: 'large' },
        );
    }, [pageNode, printingSettings?.printingAssignmentDialogUrl, screenId]);

    const openPrintDialog = React.useCallback(() => {
        if (!printingSettings?.recordPrintingWizardUrl) {
            throw new Error('recordPrintingWizardUrl is not defined');
        }

        if (!selectedRecordId) {
            throw new Error('No record is selected');
        }

        dialogService.openPageDialog(
            printingSettings?.recordPrintingWizardUrl,
            {
                [QUERY_PARAM_PRINTING_SOURCE_PAGE]: screenId,
                [QUERY_PARAM_PRINTING_NODE_TYPE]: pageNode || '',
                [QUERY_PARAM_PRINTING_RECORD_ID]: selectedRecordId,
            },
            { resolveOnCancel: true, size: 'medium' },
        );
    }, [pageNode, printingSettings?.recordPrintingWizardUrl, screenId, selectedRecordId]);

    if (
        !selectedRecordId ||
        !printingSettings ||
        (!printingSettings.canAccessPrintingAssignmentDialog && !printingSettings.canAccessRecordPrintingWizard)
    ) {
        return null;
    }

    return (
        <MultiActionButton
            buttonType="tertiary"
            aria-label={localize('@sage/xtrem-ui/table-print', 'Print')}
            text={(<Icon type="print" color="var(--colorsActionMinor500)" />) as any}
            data-testid="e-print-record-button"
            mr="16px"
            mt="-4px"
            size="small"
            className="e-print-record-button"
        >
            <Button
                className="e-print-record-dropdown-button"
                data-testid="e-print-record-assignment-button"
                disabled={!printingSettings.canAccessPrintingAssignmentDialog}
                key="assignment"
                onClick={openAssignmentDialog}
            >
                {localize('@sage/xtrem-ui/list-printing-assignment', 'Assign report')}
            </Button>
            <Button
                className="e-print-record-dropdown-button"
                data-testid="e-print-record-wizard-button"
                disabled={!printingSettings.canAccessRecordPrintingWizard}
                key="print"
                onClick={openPrintDialog}
            >
                {localize('@sage/xtrem-ui/list-printing', 'Select report')}
            </Button>
        </MultiActionButton>
    );
}
