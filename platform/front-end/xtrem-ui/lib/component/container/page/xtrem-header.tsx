import { objectKeys, type Dict } from '@sage/xtrem-shared';
import { isNil } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import { RenderingRouter } from '../../../render/rendering-router';
import type { PageArticleItem } from '../../../service/layout-types';
import type { Key } from '../../../service/shortcut-service';
import { subscribe, unsubscribe } from '../../../service/shortcut-service';
import { getSectionValidationMessage } from '../../../service/validation-service';
import { ContextType } from '../../../types';
import { scrollWithAnimationTo } from '../../../utils/dom';
import { getHeaderSection, getVisibleSections } from '../../../utils/layout-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import {
    getNavigationPanelState,
    getPageDefinitionFromState,
    getPagePropertiesFromState,
} from '../../../utils/state-utils';
import type {
    LabelControlObject,
    PageActionControlObject,
    SectionProperties,
    TableProperties,
} from '../../control-objects';
import { getFieldTitle } from '../../field/carbon-helpers';
import type { XtremTabItem } from '../../ui/tabs/xtrem-tabs';
import { XtremTabs } from '../../ui/tabs/xtrem-tabs';
import ToggleNavigationPanelButton from '../navigation-panel/toggle-navigation-panel-button';
import { ConnectedPageHeaderCardComponent } from './page-header-card';
import type { PageMode } from './page-types';
import IconButton from 'carbon-react/esm/components/icon-button';
import Icon from 'carbon-react/esm/components/icon';
import { getRouter } from '../../../service/router';
import { localize } from '../../../service/i18n-service';
import * as tokens from '@sage/design-tokens/js/base/common';
import { getIdFieldValue } from '../../../utils/id-field-utils';
import { PageTitle } from '../../ui/page-title';
import FieldWrapper from '../../../render/field-wrapper';
import { getScreenElement } from '../../../service/screen-base-definition';
import HeaderNavigationArrows from './header-navigation-arrows';
import { ConnectedHeaderQuickActions } from './header-quick-actions';
import { ConnectedHeaderDropdownActions } from './header-dropdown-actions';
import type { CollectionValue } from '../../../service/collection-data-service';
import { getHeaderTitles } from '../../../utils/page-utils';
import { Page360Switch } from '../page-360/page-360-switch';
import { HEADER_IMAGE, HEADER_TITLE, NEW_PAGE } from '../../../utils/constants';
import { PrintRecordButton } from './print-record-button';
import { Portrait } from '../../ui/portrait-component';
import { getImageUrlFromValue } from '../../field/image/image-utils';
import { navigationPanelId } from '../navigation-panel/navigation-panel-types';
import { WizardSteps } from './wizard-steps';
import type { SectionDecoratorProperties } from '../section/section-types';
import BackArrowButton from './header-back-arrow';
import type { QueryParameters } from '../../../utils/types';
import { PageHeaderInsightsButton } from '../../ui/insights-button';
import type { FieldControlObjectInstance, PageHeaderFieldTypes } from '../../types';

export type StepStatus = 'complete' | 'current' | 'incomplete';

export interface XtremHeaderExternalProps {
    activeSection: string | null;
    areNavigationTabsHidden: boolean;
    availableColumns: number;
    contextType?: ContextType;
    hasNavigationPanel: boolean;
    headerLineBlock: Partial<PageArticleItem> | null;
    isRecordCreationPage?: boolean;
    navigationPanelValue?: CollectionValue;
    pageBodyRef: React.RefObject<HTMLDivElement>;
    screenId: string;
    setActiveSection: (sectionId: string) => void;
}

export interface XtremHeaderProps extends XtremHeaderExternalProps {
    canToggleNavigationPanel: boolean;
    has360View?: boolean;
    hasDropdownOrQuickActions: boolean;
    headerDropdownMenu?: Array<PageActionControlObject>;
    headerImage: string | null;
    headerLabelField?: LabelControlObject;
    headerField?: FieldControlObjectInstance<PageHeaderFieldTypes>;
    headerSection: Partial<PageArticleItem> | null;
    headerTitle: string | null;
    history: { path: string; queryParams: QueryParameters }[];
    idFieldValue?: string | null;
    insightCount?: number;
    is360ViewOn: boolean;
    isHeaderImageNeeded: boolean;
    isMobileScreen?: boolean;
    isNavigationPanelHeaderHidden: boolean;
    isNavigationPanelOpened: boolean;
    isNewPage: boolean;
    isTitleHidden?: boolean;
    objectTypePlural?: string | null;
    objectTypeSingular?: string | null;
    openSectionInDialog: (sectionId: string) => void;
    pageMode: PageMode;
    sections: Partial<PageArticleItem>[];
    sectionsProperties: Dict<SectionDecoratorProperties>;
    sectionValidationErrors: Dict<string | null>;
    setNavigationPanelIsOpened: (isOpened: boolean) => void;
    shouldDisplayBackArrow: boolean;
    shouldDisplayNavigationArrows: boolean;
    subtitle?: string;
    title: string;
    isLessThanL: boolean;
    headerQuickActions: Array<PageActionControlObject>;
    headerDropdownActions: Array<PageActionControlObject>;
}

export interface HeaderState {
    isHeaderSectionClosed?: boolean;
}

export class XtremHeader extends React.Component<XtremHeaderProps, HeaderState> {
    private readonly activeIndicator: HTMLDivElement | null;

    private readonly tabContainer: HTMLDivElement | null;

    private readonly tabDictionary: Dict<HTMLButtonElement | null> = {};

    private readonly hotkeySubscriptions: number[] = [];

    private readonly parentRef = React.createRef<HTMLButtonElement>();

    constructor(props: XtremHeaderProps) {
        super(props);
        this.state = {
            isHeaderSectionClosed: false,
        };
    }

    componentDidMount(): void {
        this.setIndicatorPosition();
        this.hotkeySubscriptions.push(
            subscribe(['escape', 'arrowup'], () => {
                const index = this.getActiveSectionIndex();
                this.setActiveSectionByIndex(index - 1);
            }),
        );

        this.hotkeySubscriptions.push(
            subscribe(['escape', 'arrowdown'], () => {
                const index = this.getActiveSectionIndex();
                this.setActiveSectionByIndex(index + 1);
            }),
        );

        // We subscribe to each button in order to prevent issues from overlapping screens
        for (let i = 1; i < 10; i += 1) {
            this.hotkeySubscriptions.push(
                subscribe(['escape', i.toString() as Key], () => {
                    this.setActiveSectionByIndex(i - 1);
                }),
            );
        }
    }

    componentDidUpdate(prevProps: XtremHeaderProps): void {
        this.setIndicatorPosition();

        if (this.props.isNavigationPanelOpened !== prevProps.isNavigationPanelOpened) {
            this.parentRef.current?.focus();
        }

        const visibleSections = this.getVisibleTabs();
        if (
            this.props.activeSection &&
            this.props.pageMode !== 'wizard' &&
            !this.props.areNavigationTabsHidden &&
            this.getVisibleTabs().indexOf(this.props.activeSection) === -1
        ) {
            this.props.setActiveSection(visibleSections[0]);
        }
    }

    componentWillUnmount(): void {
        this.hotkeySubscriptions.forEach(unsubscribe);
    }

    private setIndicatorPosition(): void {
        if (
            this.activeIndicator &&
            this.tabContainer &&
            this.props.activeSection &&
            this.tabDictionary[this.props.activeSection]
        ) {
            const activeTabRect = this.tabDictionary[this.props.activeSection]!.getBoundingClientRect();
            const containerX = this.tabContainer.getBoundingClientRect().x || 0;
            this.activeIndicator.style.left = `${(activeTabRect.x || 0) - containerX}px`;
            this.activeIndicator.style.width = `${activeTabRect.width - 16}px`;
        }
    }

    private readonly getActiveSectionIndex = (): number =>
        this.props.sections.findIndex(s => this.props.activeSection === s.$containerId);

    private readonly setActiveSectionByIndex = (index: number): void => {
        if (index < 0 || index >= this.props.sections.length) {
            return;
        }

        this.setActiveSection(this.props.sections[index].$containerId!);
    };

    private readonly setActiveSection = (containerId: string): void => {
        this.props.setActiveSection(containerId);
        if (this.props.pageBodyRef.current && this.props.pageMode !== 'tabs' && this.props.pageMode !== 'wizard') {
            scrollWithAnimationTo(
                this.props.pageBodyRef.current.querySelector(`#${containerId}`) as HTMLElement,
                this.props.pageBodyRef.current,
                80,
            );
        }
    };

    private readonly hasTabs = (): boolean => this.getVisibleTabs().length > 1;

    private readonly getClasses = (): string => {
        const classes = ['e-header'];

        if (this.props.contextType) {
            classes.push(`e-header-context-${this.props.contextType}`);
        }

        if (!this.hasTabs()) {
            classes.push('e-no-tabs');
        }

        return classes.join(' ');
    };

    private readonly getVisibleTabs = (skipHeaderSection: boolean = false): string[] => {
        if (this.props.areNavigationTabsHidden) {
            return [];
        }

        return this.props.sections
            .filter(item => getFieldTitle(this.props.screenId, this.props.sectionsProperties[item.$containerId!], null))
            .filter(
                item =>
                    !resolveByValue({
                        screenId: this.props.screenId,
                        skipHexFormat: true,
                        propertyValue: this.props.sectionsProperties[item.$containerId!].isHidden,
                        rowValue: null,
                    }),
            )
            .filter(item =>
                skipHeaderSection && this.props.headerSection?.$containerId
                    ? item.$containerId !== this.props.headerSection.$containerId
                    : true,
            )
            .map(c => c.$containerId!);
    };

    private readonly onRecordClose = (): void => {
        this.props.navigationPanelValue?.restoreFilterSnapshot();
        getRouter(this.props.screenId).closeRecord();
    };

    private readonly isDialogContext = (): boolean => {
        return this.props.contextType === ContextType.dialog;
    };

    private readonly hasHeaderSection = (): boolean => {
        return !isNil(this.props.headerSection);
    };

    private readonly filterByHeaderSection = (sections: XtremTabItem[]): XtremTabItem[] => {
        return this.hasHeaderSection()
            ? sections.filter(({ id }: XtremTabItem) => {
                  return id !== this.props.headerSection?.$containerId;
              })
            : sections;
    };

    private readonly renderTabs = (): React.ReactNode => {
        if (!this.hasTabs()) {
            return null;
        }

        const sections = this.getVisibleTabs().map(
            (id): XtremTabItem => ({
                id,
                title: getFieldTitle(this.props.screenId, this.props.sectionsProperties[id], null),
                validationMessage: this.props.sectionValidationErrors[id],
                indicatorContent: this.props.sectionsProperties[id].indicatorContent,
            }),
        );

        if (this.props.pageMode === 'wizard') {
            return (
                <div className="e-header-wizard-steps-container">
                    <WizardSteps
                        screenId={this.props.screenId}
                        sections={sections}
                        selectedSection={this.props.activeSection}
                    />
                </div>
            );
        }

        const skipHeaderSection = this.isDialogContext() || this.props.isMobileScreen;
        return (
            <div className="e-header-nav" data-testid="e-header-nav-tabs">
                <XtremTabs
                    selectedTabId={this.props.activeSection || this.getVisibleTabs(skipHeaderSection)[0]}
                    onTabChange={this.setActiveSection}
                    tabs={skipHeaderSection ? this.filterByHeaderSection(sections) : sections}
                />
            </div>
        );
    };

    private readonly onHeaderSectionToggle = (): void => {
        this.setState(
            state => ({ isHeaderSectionClosed: !state.isHeaderSectionClosed }),
            () => {
                if (this.props.pageBodyRef.current) {
                    /**
                     * Even if the parent element gets resized by hiding the top section,
                     * the resize event is only triggered automatically if the window is resized
                     *  */
                    const event = new UIEvent('resize', { view: window, bubbles: true, cancelable: false, detail: 0 });
                    this.props.pageBodyRef.current.dispatchEvent(event);
                }
            },
        );
    };

    renderCloseIcon = (): React.ReactNode => {
        if (this.props.hasNavigationPanel) {
            return (
                <div className="e-header-close-icon-col">
                    <IconButton
                        aria-label={localize('@sage/xtrem-ui/close-record', 'Close record')}
                        data-testid="e-page-close-button"
                        data-pendoid="closeRecord"
                        onClick={this.onRecordClose}
                    >
                        <Icon
                            type="close"
                            color={tokens.colorsUtilityMajor400}
                            tooltipMessage={localize('@sage/xtrem-ui/close-record', 'Close record')}
                        />
                    </IconButton>
                </div>
            );
        }

        return null;
    };

    renderHeaderToggleButton = (): React.ReactNode => {
        if (this.props.headerSection) {
            const label = this.state.isHeaderSectionClosed
                ? localize('@sage/xtrem-ui/open-header-section', 'Open header')
                : localize('@sage/xtrem-ui/close-header-section', 'Close header');
            return (
                <div className="e-header-toggle-icon-col">
                    <IconButton
                        aria-label={label}
                        data-testid="e-toggle-header-section"
                        onClick={this.onHeaderSectionToggle}
                        mr="24px"
                        data-pendoid={this.state.isHeaderSectionClosed ? 'headerOpen' : 'headerClose'}
                    >
                        <Icon
                            type={this.state.isHeaderSectionClosed ? 'chevron_up' : 'chevron_down'}
                            color={tokens.colorsUtilityMajor400}
                            tooltipMessage={label}
                        />
                    </IconButton>
                </div>
            );
        }

        return null;
    };

    private readonly renderTitleImage = (title: string): React.ReactNode => (
        <div className="e-header-image-container">
            {this.props.headerImage && (
                <div
                    className="e-header-image"
                    style={{ backgroundImage: `url('${getImageUrlFromValue(this.props.headerImage)}')` }}
                    aria-label={title}
                    role="img"
                />
            )}
            {!this.props.headerImage && this.props.headerTitle && (
                <Portrait size="M" placeholderMode="Initials" placeholderValue={this.props.headerTitle} icon="image" />
            )}
            {!this.props.headerImage && !this.props.headerTitle && this.props.title && (
                <Portrait
                    size="M"
                    placeholderMode="Initials"
                    placeholderValue={this.props.idFieldValue || this.props.title}
                    icon="image"
                />
            )}
            {!this.props.headerImage && !this.props.headerTitle && !this.props.title && (
                <img
                    className="e-header-image"
                    src="/images/no-available-image.svg"
                    alt={localize('@sage/xtrem-ui/no-available-image', 'No available image')}
                />
            )}
        </div>
    );

    private readonly getHeaderActions = (
        headerField?: FieldControlObjectInstance<PageHeaderFieldTypes>,
        headerQuickActions?: PageActionControlObject[] | null,
        headerDropdownActions?: PageActionControlObject[] | null,
        isLessThanL?: boolean,
    ): {
        finalQuickActions: PageActionControlObject[];
        finalDropdownActions: PageActionControlObject[];
    } => {
        const shouldMerge = isLessThanL && !!headerField;

        const quickActions = headerQuickActions ?? [];
        const dropdownActions = headerDropdownActions ?? [];

        return shouldMerge
            ? {
                  finalQuickActions: [],
                  finalDropdownActions: [...quickActions, ...dropdownActions],
              }
            : {
                  finalQuickActions: quickActions,
                  finalDropdownActions: dropdownActions,
              };
    };

    render(): React.ReactNode {
        const { isNavigationPanelOpened, isNavigationPanelHeaderHidden } = this.props;
        const { subtitle, title } = getHeaderTitles({
            title: this.props.title,
            subtitle: this.props.subtitle,
            idFieldValue: this.props.idFieldValue,
            isRecordCreationPage: this.props.isRecordCreationPage,
            objectTypePlural: this.props.objectTypePlural,
            objectTypeSingular: this.props.objectTypeSingular,
        });

        const headerTitleColumnClasses = ['e-header-title-col'];
        if (this.props.insightCount && this.props.insightCount > 0) {
            headerTitleColumnClasses.push('e-header-title-col-insight');
        }

        const { finalQuickActions, finalDropdownActions } = this.getHeaderActions(
            this.props.headerField,
            this.props.headerQuickActions,
            this.props.headerDropdownActions,
            this.props.isLessThanL,
        );

        return (
            <header className={this.getClasses()} data-testid="e-header">
                <div className="e-header-title-row">
                    {this.props.hasNavigationPanel &&
                        !this.props.canToggleNavigationPanel &&
                        !isNavigationPanelOpened &&
                        !isNavigationPanelHeaderHidden &&
                        !this.props.isNewPage && (
                            <ToggleNavigationPanelButton
                                setNavigationPanelIsOpened={this.props.setNavigationPanelIsOpened}
                                isNavigationPanelOpened={this.props.isNavigationPanelOpened}
                                parentRef={this.parentRef}
                            />
                        )}
                    {this.props.contextType === ContextType.page && !this.props.isTitleHidden && (
                        <div className={headerTitleColumnClasses.join(' ')}>
                            <div className="e-header-title-container">
                                {this.props.shouldDisplayBackArrow && (
                                    <BackArrowButton screenId={this.props.screenId} />
                                )}
                                {this.props.shouldDisplayNavigationArrows && (
                                    <HeaderNavigationArrows screenId={this.props.screenId} />
                                )}
                                {this.props.isHeaderImageNeeded && this.renderTitleImage(title)}
                                <div>
                                    <PageTitle title={title} />
                                    {!!subtitle && <p className="e-header-subtitle">{subtitle}</p>}
                                </div>
                                {this.props.headerField && (
                                    <div className="e-header-field">
                                        <FieldWrapper
                                            item={{ $bind: this.props.headerField.id }}
                                            screenId={this.props.screenId}
                                            contextType={ContextType.pageHeader}
                                        />
                                    </div>
                                )}
                                {this.props.headerLabelField && (
                                    <div className="e-header-title-label">
                                        <FieldWrapper
                                            item={{ $bind: this.props.headerLabelField.id }}
                                            screenId={this.props.screenId}
                                            contextType={ContextType.pageHeader}
                                        />
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                    <PageHeaderInsightsButton screenId={this.props.screenId} />
                    {this.props.hasDropdownOrQuickActions && (
                        <span className="e-header-line-icon-group">
                            <PrintRecordButton screenId={this.props.screenId} />
                            <ConnectedHeaderQuickActions screenId={this.props.screenId} actions={finalQuickActions} />
                            <ConnectedHeaderDropdownActions
                                contextType={this.props.contextType}
                                screenId={this.props.screenId}
                                actions={finalDropdownActions}
                            />
                        </span>
                    )}
                    {this.props.has360View && !this.props.isNewPage && <Page360Switch screenId={this.props.screenId} />}
                    {(this.props.hasNavigationPanel || this.props.headerSection) && (
                        <span className="e-header-line-icon-group e-header-line-icon-group-close">
                            {this.renderHeaderToggleButton()}
                            {this.renderCloseIcon()}
                        </span>
                    )}
                    {this.props.headerLineBlock && (
                        <div className="e-header-line-block">
                            <RenderingRouter
                                screenId={this.props.screenId}
                                item={this.props.headerLineBlock}
                                contextType={ContextType.header}
                                availableColumns={4}
                            />
                        </div>
                    )}
                </div>

                <ConnectedPageHeaderCardComponent screenId={this.props.screenId} />
                {this.props.headerSection && !this.props.is360ViewOn && !this.state.isHeaderSectionClosed && (
                    <div className="e-header-section" data-testid="e-header-section">
                        <RenderingRouter
                            screenId={this.props.screenId}
                            item={this.props.headerSection}
                            availableColumns={this.props.availableColumns}
                        />
                    </div>
                )}
                {!this.props.areNavigationTabsHidden && !this.props.is360ViewOn && this.renderTabs()}
            </header>
        );
    }
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: XtremHeaderExternalProps): XtremHeaderProps => {
    const pageDefinition = getPageDefinitionFromState(props.screenId, state);
    const sectionIds: string[] = pageDefinition.metadata.layout.$items
        .filter(section => !!section.$containerId)
        .map(section => section.$containerId as string);

    const sectionsProperties = objectKeys(pageDefinition.metadata.uiComponentProperties)
        .filter(property => sectionIds.indexOf(property) > -1)
        .reduce(
            (properties, nextKey) => ({
                ...properties,
                [nextKey]: pageDefinition.metadata.uiComponentProperties[nextKey],
            }),
            {} as Dict<SectionProperties>,
        );
    const navigationPanelState = getNavigationPanelState(props.screenId, state);
    const pageProperties = getPagePropertiesFromState(props.screenId, state);

    const navPanelDefinition = pageDefinition.metadata.uiComponentProperties[navigationPanelId] as TableProperties;
    const isHeaderImageNeeded = !!navPanelDefinition?.mobileCard?.image;
    const sectionValidationErrors = sectionIds.reduce(
        (prevValue, sectionId) => {
            return { ...prevValue, [sectionId]: getSectionValidationMessage(props.screenId, sectionId, state) };
        },
        {} as Dict<string | null>,
    );
    const locale = state.applicationContext?.locale || 'en-US';
    const idFieldValue = getIdFieldValue(pageDefinition, locale);
    const headerLabelField: LabelControlObject | undefined = pageProperties.headerLabel?.apply(
        getScreenElement(pageDefinition),
    );
    const headerField = pageProperties.headerField?.apply(getScreenElement(pageDefinition));
    const headerSection = getHeaderSection(pageDefinition, false);
    const headerQuickActions = resolveByValue<Array<PageActionControlObject>>({
        skipHexFormat: true,
        screenId: props.screenId,
        propertyValue: pageProperties.headerQuickActions,
        rowValue: null,
        fieldValue: null,
    });
    const has360View =
        resolveByValue<boolean>({
            skipHexFormat: true,
            screenId: props.screenId,
            propertyValue: pageProperties.has360View,
            rowValue: null,
            fieldValue: null,
        }) || false;

    const headerDropdownActions = resolveByValue<Array<PageActionControlObject>>({
        skipHexFormat: true,
        screenId: props.screenId,
        propertyValue: pageProperties.headerDropDownActions,
        rowValue: null,
        fieldValue: null,
    });

    const headerImage =
        state.screenDefinitions[props.screenId]?.values[HEADER_IMAGE] ||
        state.screenDefinitions[props.screenId]?.values.image;
    const headerTitle =
        state.screenDefinitions[props.screenId]?.values[HEADER_TITLE] ||
        state.screenDefinitions[props.screenId]?.values.name;

    const getLastValue = (obj: any): string => {
        if (typeof obj !== 'object' || obj === null) return obj;
        const values = Object.values(obj);
        if (values.length === 0) return '';
        return getLastValue(values[values.length - 1]);
    };

    const finalHeaderTitleValue =
        typeof headerTitle === 'object' && headerTitle !== null ? getLastValue(headerTitle) : headerTitle || '';
    const finalHeaderImageValue =
        typeof headerImage === 'object' && headerImage !== null
            ? getLastValue(headerImage)
            : headerImage?.value || undefined;

    const history = state.navigation.history;

    return {
        ...props,
        canToggleNavigationPanel: state.browser.greaterThan.l,
        has360View,
        hasDropdownOrQuickActions: headerQuickActions?.length > 0 || headerDropdownActions?.length > 0,
        headerLabelField,
        headerField,
        headerSection: headerSection?.layout || null,
        headerImage: finalHeaderImageValue,
        isHeaderImageNeeded,
        headerTitle: finalHeaderTitleValue,
        idFieldValue,
        is360ViewOn: !!pageDefinition.is360ViewOn,
        isMobileScreen: state.browser.lessThan.m,
        isNavigationPanelHeaderHidden: !!navigationPanelState?.isHeaderHidden,
        isNavigationPanelOpened: !!navigationPanelState?.isOpened,
        isNewPage: pageDefinition.queryParameters?._id === NEW_PAGE,
        insightCount: pageDefinition.insightCount,
        isTitleHidden: pageProperties.isTitleHidden,
        navigationPanelValue: pageDefinition.navigationPanel?.value,
        objectTypePlural: pageProperties.objectTypePlural,
        objectTypeSingular: pageProperties.objectTypeSingular,
        openSectionInDialog: xtremRedux.actions.actionStub,
        pageMode: pageProperties?.mode || 'default',
        sections: getVisibleSections(pageDefinition, state.activeDialogs, state.browser.lessThan.m),
        sectionsProperties,
        sectionValidationErrors,
        setNavigationPanelIsOpened: xtremRedux.actions.actionStub,
        shouldDisplayNavigationArrows:
            props.hasNavigationPanel && !state.browser.is.xs && !navigationPanelState?.isOpened,
        shouldDisplayBackArrow: history.length > 0,
        subtitle: pageProperties.subtitle,
        title: (pageProperties && getFieldTitle(props.screenId, pageProperties, null)) || '',
        history,
        isLessThanL: state.browser.lessThan.l,
        headerQuickActions,
        headerDropdownActions,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: XtremHeaderProps,
): Partial<XtremHeaderProps> => {
    return {
        setNavigationPanelIsOpened: (isOpened: boolean): void =>
            dispatch(xtremRedux.actions.setNavigationPanelIsOpened(isOpened, props.screenId)),
    };
};

export const ConnectedXtremHeader = connect<XtremHeaderProps, {}, XtremHeaderExternalProps>(
    mapStateToProps,
    mapDispatchToProps,
)(XtremHeader);
