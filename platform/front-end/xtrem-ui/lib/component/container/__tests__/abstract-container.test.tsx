import type { ValidationResult } from '../../..';
import type { UiComponentProperties } from '../../abstract-ui-control-object';
import { AbstractContainer } from '../../control-objects';
import type { ContainerProperties } from '../../types';
import { ContainerKey } from '../../types';

class AbstractContainerImplementation extends AbstractContainer<any, any, UiComponentProperties> {
    async validateWithDetails(
        partition: true,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }>;
    async validateWithDetails(partition: false): Promise<ValidationResult[]>;
    async validateWithDetails(): Promise<ValidationResult[]>;
    async validateWithDetails(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _partition = false,
    ): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] } | ValidationResult[]> {
        return [];
    }
}

describe('Abstract container', () => {
    const screenId = 'TestScreen';
    const elementId = 'abstract-container-id';
    let container: AbstractContainer<any, any, UiComponentProperties>;
    let containerProperties: ContainerProperties;
    let getValidationStateMock: jest.Mock<Promise<boolean>>;

    beforeEach(() => {
        containerProperties = {
            title: 'Sticker title',
            isHidden: true,
            isDisabled: true,
            isTransient: true,
        };
        const getPropertiesMock = jest.fn(() => containerProperties);
        const setPropertiesMock = jest.fn((_screenId: string, elementId: string, value: any) => {
            containerProperties = { ...value };
        });
        getValidationStateMock = jest.fn(async (): Promise<boolean> => true);
        container = new AbstractContainerImplementation(
            screenId,
            elementId,
            getPropertiesMock,
            setPropertiesMock,
            ContainerKey.Block,
            getValidationStateMock,
            null,
        );
    });

    it('should retrieve id from constructor parameter', () => {
        expect(container.id).toBe(elementId);
    });

    it('should call getValidationState function on isValid', async () => {
        expect(getValidationStateMock).not.toHaveBeenCalled();
        expect(await container.isValid).toEqual(true);
        expect(getValidationStateMock).toHaveBeenCalledTimes(1);
    });
});
