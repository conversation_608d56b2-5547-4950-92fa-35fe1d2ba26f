PATH: XTREEM/Client+Framework/Property+Callbacks

## Introduction

This document describes what property callbacks are, their benefits and drawbacks

## Property Callbacks

The Xtrem UI framework allows certain element properties (e.g `isDisabled`, `prefix`, `scale`...) to be defined as a function as well as a property assignment. This enables the application developers to dynamically set the value of these properties. In some cases, such as in a nested context, this is the only way of updating the property values on runtime.The property callbacks are executed in the scope of the page. They can access page value, but not allowed to make modifications to any field value or component property. In case this is attempted, an exception is thrown.

The property callbacks are not event handlers and should not be handled as such. They can be invoked by the framework a number of times in a row, for example in executing validation routines and rendering. In order to optimize performance, these functions should be kept as simple as possible without complex calculations.


## Limitations
Unlike traditional properties, there is no direct relationship between the callback function's body and the element they belong to. This means that the framework cannot automatically figure out when a value is changed that is used in the callback function in order to calculate the value of the property. The callbacks are only executed when the framework detects that it should re-render an element. That means that there is no guarantee that rendered screen is always up to date with property changes.

Some complex field control objects provide a `redraw()` function that can be used to re-render all nested fields and re-evaluate all property callbacks.
