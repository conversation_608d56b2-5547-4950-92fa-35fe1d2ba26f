{"@sage/xtrem-ui/360-view": "360 view", "@sage/xtrem-ui/action-button-more": "More Actions", "@sage/xtrem-ui/action-clone": "<PERSON><PERSON>", "@sage/xtrem-ui/action-close": "Close", "@sage/xtrem-ui/action-delete": "Delete", "@sage/xtrem-ui/action-edit": "Edit", "@sage/xtrem-ui/action-share": "Share", "@sage/xtrem-ui/action-tool-bar-items-selected": "Items selected: {{count}}", "@sage/xtrem-ui/actions": "Actions", "@sage/xtrem-ui/add-item-in-line": "Add item", "@sage/xtrem-ui/add-item-in-line-using-sidebar": "Add item in sidebar", "@sage/xtrem-ui/add-value": "Add value", "@sage/xtrem-ui/aggregation-method": "Aggregation method", "@sage/xtrem-ui/async-mutation-dialog-content": "The process is taking longer than expected. You can go back and try again later without losing your work. You can keep waiting or let the process run in the background.", "@sage/xtrem-ui/async-mutation-dialog-title": "Your operation is still in progress", "@sage/xtrem-ui/async-operation-error": "An error occurred while processing the operation.", "@sage/xtrem-ui/attachment-options-menu-all": "All file types", "@sage/xtrem-ui/attachment-options-menu-documents": "Documents", "@sage/xtrem-ui/attachment-options-menu-images": "Images", "@sage/xtrem-ui/attachment-options-menu-others": "Others", "@sage/xtrem-ui/attachments": "Attachments", "@sage/xtrem-ui/average": "Average", "@sage/xtrem-ui/axes": "Axes", "@sage/xtrem-ui/bulk-action-async-export": "Export", "@sage/xtrem-ui/bulk-action-dialog-content": "Perform this action on the selected items: {{itemCount}}", "@sage/xtrem-ui/bulk-action-error": "Action could not be started, please try again.", "@sage/xtrem-ui/bulk-action-print": "Print", "@sage/xtrem-ui/bulk-action-started": "Action started on the selected items.", "@sage/xtrem-ui/bulk-actions-bar-selected": "Items selected: {{count}}", "@sage/xtrem-ui/business-action-in-progress": "This process is running in the background.", "@sage/xtrem-ui/calendar-month": "Month", "@sage/xtrem-ui/calendar-view-3-day": "3 day", "@sage/xtrem-ui/calendar-view-day": "Day", "@sage/xtrem-ui/calendar-view-month": "Month", "@sage/xtrem-ui/calendar-view-week": "Week", "@sage/xtrem-ui/calendar-view-year": "Year", "@sage/xtrem-ui/cancel": "Cancel", "@sage/xtrem-ui/carbon-date-format": "MM/dd/yyyy", "@sage/xtrem-ui/chart-component-no-data": "No data found", "@sage/xtrem-ui/clear-filter-text": "Clear filter text", "@sage/xtrem-ui/clear-floating-filter": "Clear all filters", "@sage/xtrem-ui/clear-input-text": "Clear", "@sage/xtrem-ui/clear-selection": "Clear selection", "@sage/xtrem-ui/close-full-screen": "Close fullscreen", "@sage/xtrem-ui/close-header-section": "Close header", "@sage/xtrem-ui/close-record": "Close record", "@sage/xtrem-ui/collapse-section": "Collapse section", "@sage/xtrem-ui/consumer-mock-hide-test-ids": "Hide IDs", "@sage/xtrem-ui/consumer-mock-show-test-ids": "Show IDs", "@sage/xtrem-ui/continue": "Continue", "@sage/xtrem-ui/copy-error-details": "Copy error details", "@sage/xtrem-ui/create-a-new-view": "Create a new view", "@sage/xtrem-ui/create-a-view": "Create a view", "@sage/xtrem-ui/create-error": "Create error: {{0}}", "@sage/xtrem-ui/create-new-dashboard": "+ Create", "@sage/xtrem-ui/crud-cancel": "Cancel", "@sage/xtrem-ui/crud-confirm": "Confirm", "@sage/xtrem-ui/crud-create": "Create", "@sage/xtrem-ui/crud-create-success": "Record has been created successfully.", "@sage/xtrem-ui/crud-delete": "Delete", "@sage/xtrem-ui/crud-delete-record-button": "Delete", "@sage/xtrem-ui/crud-delete-record-warning-message": "You are about to delete this record.", "@sage/xtrem-ui/crud-delete-record-warning-title": "Delete record", "@sage/xtrem-ui/crud-delete-successfully": "Record has been deleted successfully.", "@sage/xtrem-ui/crud-duplicate": "Duplicate", "@sage/xtrem-ui/crud-duplicate-dialog-subtitle": "Complete the fields below to duplicate this record", "@sage/xtrem-ui/crud-duplicate-dialog-title": "Duplicate record", "@sage/xtrem-ui/crud-duplicate-failed": "Failed to duplicate record.", "@sage/xtrem-ui/crud-duplicate-successfully": "Record was duplicated successfully.", "@sage/xtrem-ui/crud-save": "Save", "@sage/xtrem-ui/crud-save-failed": "Failed to save record.", "@sage/xtrem-ui/crud-update-success": "Record has been updated successfully.", "@sage/xtrem-ui/dashboard-actions": "Actions", "@sage/xtrem-ui/dashboard-add_contact": "Add contact", "@sage/xtrem-ui/dashboard-add_note": "Add note", "@sage/xtrem-ui/dashboard-add_site": "Add address", "@sage/xtrem-ui/dashboard-add-widget": "Add widget", "@sage/xtrem-ui/dashboard-address": "Address", "@sage/xtrem-ui/dashboard-address_name": "Address name", "@sage/xtrem-ui/dashboard-addresses": "Addresses", "@sage/xtrem-ui/dashboard-cancel": "Cancel", "@sage/xtrem-ui/dashboard-choose_date": "Choose another date range", "@sage/xtrem-ui/dashboard-clear_filter": "Clear filter", "@sage/xtrem-ui/dashboard-contact": "Contact", "@sage/xtrem-ui/dashboard-contact_card_empty_text": "This widget has no contacts yet.", "@sage/xtrem-ui/dashboard-contact_image": "Contact image", "@sage/xtrem-ui/dashboard-contact_type": "Contact type", "@sage/xtrem-ui/dashboard-contacts": "Contacts", "@sage/xtrem-ui/dashboard-create": "Create", "@sage/xtrem-ui/dashboard-create-dashboard": "Create dashboard", "@sage/xtrem-ui/dashboard-current_date_filter": "Current date filter:", "@sage/xtrem-ui/dashboard-day": "Day", "@sage/xtrem-ui/dashboard-delete": "Delete", "@sage/xtrem-ui/dashboard-delete-dashboard-dialog-message": "Are you sure you want to delete this dashboard?", "@sage/xtrem-ui/dashboard-delete-dashboard-dialog-title": "Delete dashboard", "@sage/xtrem-ui/dashboard-deleted": "Dashboard deleted.", "@sage/xtrem-ui/dashboard-duplicate": "Duplicate", "@sage/xtrem-ui/dashboard-duplicated": "Dashboard duplicated.", "@sage/xtrem-ui/dashboard-edit": "Edit", "@sage/xtrem-ui/dashboard-editor-add-widget": "Add", "@sage/xtrem-ui/dashboard-editor-all-widgets": "All widgets", "@sage/xtrem-ui/dashboard-editor-cancel-edit": "Cancel", "@sage/xtrem-ui/dashboard-editor-create-dialog-blank-description": "Start with a blank template and add the widgets you need", "@sage/xtrem-ui/dashboard-editor-create-dialog-blank-title": "Blank template", "@sage/xtrem-ui/dashboard-editor-create-dialog-description": "Select a template to get started or build your own dashboard. You can customize any dashboard by adding or removing widgets.", "@sage/xtrem-ui/dashboard-editor-create-dialog-title": "Select a dashboard template.", "@sage/xtrem-ui/dashboard-editor-edit-title": "Edit title", "@sage/xtrem-ui/dashboard-editor-redo": "Redo", "@sage/xtrem-ui/dashboard-editor-save": "Save", "@sage/xtrem-ui/dashboard-editor-save-error": "Failed to save dashboard.", "@sage/xtrem-ui/dashboard-editor-saved-successfully": "Dash<PERSON> saved.", "@sage/xtrem-ui/dashboard-editor-title": "Dashboard editor", "@sage/xtrem-ui/dashboard-editor-undo": "Undo", "@sage/xtrem-ui/dashboard-editor-widget-edit": "Edit", "@sage/xtrem-ui/dashboard-editor-widget-list-add": "Create a widget", "@sage/xtrem-ui/dashboard-editor-widget-list-title": "Widgets", "@sage/xtrem-ui/dashboard-email": "Email", "@sage/xtrem-ui/dashboard-empty-heading": "This dashboard is empty.", "@sage/xtrem-ui/dashboard-empty-subtitle": "Add a widget to customize your dashboard.", "@sage/xtrem-ui/dashboard-expand_row": "Expand row", "@sage/xtrem-ui/dashboard-failed-to-create": "Failed to create a new dashboard", "@sage/xtrem-ui/dashboard-failed-to-delete": "The dashboard could not be deleted.", "@sage/xtrem-ui/dashboard-failed-to-duplicate": "The dashboard could not be duplicated.", "@sage/xtrem-ui/dashboard-month": "Month", "@sage/xtrem-ui/dashboard-month_1": "January", "@sage/xtrem-ui/dashboard-month_10": "October", "@sage/xtrem-ui/dashboard-month_11": "November", "@sage/xtrem-ui/dashboard-month_12": "December", "@sage/xtrem-ui/dashboard-month_2": "February", "@sage/xtrem-ui/dashboard-month_3": "March", "@sage/xtrem-ui/dashboard-month_4": "April", "@sage/xtrem-ui/dashboard-month_5": "May", "@sage/xtrem-ui/dashboard-month_6": "June", "@sage/xtrem-ui/dashboard-month_7": "July", "@sage/xtrem-ui/dashboard-month_8": "August", "@sage/xtrem-ui/dashboard-month_9": "September", "@sage/xtrem-ui/dashboard-more_options": "More options", "@sage/xtrem-ui/dashboard-next": "Next", "@sage/xtrem-ui/dashboard-next_period": "Select next period", "@sage/xtrem-ui/dashboard-no-change": "No change", "@sage/xtrem-ui/dashboard-note": "Note", "@sage/xtrem-ui/dashboard-notes": "Notes", "@sage/xtrem-ui/dashboard-phone_number": "Phone number", "@sage/xtrem-ui/dashboard-position": "Position", "@sage/xtrem-ui/dashboard-previous": "Previous", "@sage/xtrem-ui/dashboard-previous_period": "Select previous period", "@sage/xtrem-ui/dashboard-quarter": "Quarter", "@sage/xtrem-ui/dashboard-reload": "Reload", "@sage/xtrem-ui/dashboard-save": "Save", "@sage/xtrem-ui/dashboard-scroll_left": "<PERSON><PERSON> left", "@sage/xtrem-ui/dashboard-scroll_right": "<PERSON><PERSON> right", "@sage/xtrem-ui/dashboard-select_address": "Select address", "@sage/xtrem-ui/dashboard-select_contact": "Select contact", "@sage/xtrem-ui/dashboard-settings": "", "@sage/xtrem-ui/dashboard-site": "Site", "@sage/xtrem-ui/dashboard-site_card_empty_text": "This widget has no addresses yet.", "@sage/xtrem-ui/dashboard-table_filter_menu": "Filter menu", "@sage/xtrem-ui/dashboard-table_select": "Select row", "@sage/xtrem-ui/dashboard-view_switch": "View switch", "@sage/xtrem-ui/dashboard-week": "Week", "@sage/xtrem-ui/dashboard-widget-category-others": "Others", "@sage/xtrem-ui/dashboard-widget-close": "Close", "@sage/xtrem-ui/dashboard-widget-refresh": "Refresh", "@sage/xtrem-ui/dashboard-widget-settings": "Settings", "@sage/xtrem-ui/dashboard-year": "Year", "@sage/xtrem-ui/date-error-day-range": "Day should be a number within a 1-31 range.", "@sage/xtrem-ui/date-error-month-range": "Month should be a number within a 1-12 range.", "@sage/xtrem-ui/date-error-year-range": "Year should be a number within a 1800-2200 range.", "@sage/xtrem-ui/date-format": "DD/MM/YYYY", "@sage/xtrem-ui/date-format-separator": "/", "@sage/xtrem-ui/date-time-component-aria-label": "Select date and time", "@sage/xtrem-ui/date-time-range-end-date": "End", "@sage/xtrem-ui/date-time-range-start-date": "Start", "@sage/xtrem-ui/date-time-range-time": "Time", "@sage/xtrem-ui/date-time-range-time-zone": "Time zone:", "@sage/xtrem-ui/datetime-aria-label": "Date and time", "@sage/xtrem-ui/datetime-range-aria-label": "Date and time range", "@sage/xtrem-ui/datetime-range-end-date-error": "You need to enter an End date later than the Start date", "@sage/xtrem-ui/default-view-cannot-be-edited": "Default view cannot be updated.", "@sage/xtrem-ui/delete-error": "Delete error: {{0}}", "@sage/xtrem-ui/detailed-icon-name-accounting": "Accounting", "@sage/xtrem-ui/detailed-icon-name-addons": "Addons", "@sage/xtrem-ui/detailed-icon-name-animal": "Animal", "@sage/xtrem-ui/detailed-icon-name-apple": "Apple", "@sage/xtrem-ui/detailed-icon-name-asset_mgt": "Asset management", "@sage/xtrem-ui/detailed-icon-name-award": "Award", "@sage/xtrem-ui/detailed-icon-name-bag": "Bag", "@sage/xtrem-ui/detailed-icon-name-bakery": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-barcode": "Barcode", "@sage/xtrem-ui/detailed-icon-name-bicycle": "Bicycle", "@sage/xtrem-ui/detailed-icon-name-binocular": "Binoculars", "@sage/xtrem-ui/detailed-icon-name-book": "Book", "@sage/xtrem-ui/detailed-icon-name-bright": "<PERSON>", "@sage/xtrem-ui/detailed-icon-name-building": "Building", "@sage/xtrem-ui/detailed-icon-name-calculator": "Calculator", "@sage/xtrem-ui/detailed-icon-name-calendar": "Calendar", "@sage/xtrem-ui/detailed-icon-name-camera": "Camera", "@sage/xtrem-ui/detailed-icon-name-card": "Card", "@sage/xtrem-ui/detailed-icon-name-cart": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-certificate": "Certificate", "@sage/xtrem-ui/detailed-icon-name-check": "Check", "@sage/xtrem-ui/detailed-icon-name-checkbox": "Checkbox", "@sage/xtrem-ui/detailed-icon-name-checklist": "Checklist", "@sage/xtrem-ui/detailed-icon-name-chemical": "Chemical", "@sage/xtrem-ui/detailed-icon-name-chess": "Chess", "@sage/xtrem-ui/detailed-icon-name-click": "Click", "@sage/xtrem-ui/detailed-icon-name-clock": "Clock", "@sage/xtrem-ui/detailed-icon-name-close": "Close", "@sage/xtrem-ui/detailed-icon-name-clothes": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-cloud": "Cloud", "@sage/xtrem-ui/detailed-icon-name-coffee": "Coffee", "@sage/xtrem-ui/detailed-icon-name-compass": "<PERSON>mp<PERSON>", "@sage/xtrem-ui/detailed-icon-name-connected": "Connected", "@sage/xtrem-ui/detailed-icon-name-consultant": "Consultant", "@sage/xtrem-ui/detailed-icon-name-conversation": "Conversation", "@sage/xtrem-ui/detailed-icon-name-cooking": "Cooking", "@sage/xtrem-ui/detailed-icon-name-cpu": "CPU", "@sage/xtrem-ui/detailed-icon-name-crowd": "Crowd", "@sage/xtrem-ui/detailed-icon-name-crown": "Crown", "@sage/xtrem-ui/detailed-icon-name-data": "Data", "@sage/xtrem-ui/detailed-icon-name-database": "Database", "@sage/xtrem-ui/detailed-icon-name-decline": "Decline", "@sage/xtrem-ui/detailed-icon-name-desktop": "Desktop", "@sage/xtrem-ui/detailed-icon-name-devices": "Devices", "@sage/xtrem-ui/detailed-icon-name-dollar": "Dollar", "@sage/xtrem-ui/detailed-icon-name-download": "Download", "@sage/xtrem-ui/detailed-icon-name-ear": "Ear", "@sage/xtrem-ui/detailed-icon-name-ecomm": "E-commerce", "@sage/xtrem-ui/detailed-icon-name-euro": "Euro", "@sage/xtrem-ui/detailed-icon-name-excavator": "Excavator", "@sage/xtrem-ui/detailed-icon-name-eye": "Eye", "@sage/xtrem-ui/detailed-icon-name-factory": "Factory", "@sage/xtrem-ui/detailed-icon-name-favorite": "Favorite", "@sage/xtrem-ui/detailed-icon-name-filter": "Filter", "@sage/xtrem-ui/detailed-icon-name-financials": "Financials", "@sage/xtrem-ui/detailed-icon-name-flag": "Flag", "@sage/xtrem-ui/detailed-icon-name-folder": "Folder", "@sage/xtrem-ui/detailed-icon-name-food": "Food", "@sage/xtrem-ui/detailed-icon-name-form": "Form", "@sage/xtrem-ui/detailed-icon-name-gauge": "Gauge", "@sage/xtrem-ui/detailed-icon-name-gears": "Gears", "@sage/xtrem-ui/detailed-icon-name-glasses": "Glasses", "@sage/xtrem-ui/detailed-icon-name-globe": "Globe", "@sage/xtrem-ui/detailed-icon-name-green": "Green", "@sage/xtrem-ui/detailed-icon-name-handshake": "Handshake", "@sage/xtrem-ui/detailed-icon-name-happy": "Happy", "@sage/xtrem-ui/detailed-icon-name-heart": "Heart", "@sage/xtrem-ui/detailed-icon-name-hide": "<PERSON>de", "@sage/xtrem-ui/detailed-icon-name-holiday": "Holiday", "@sage/xtrem-ui/detailed-icon-name-home": "Home", "@sage/xtrem-ui/detailed-icon-name-hourglass": "Hourglass", "@sage/xtrem-ui/detailed-icon-name-hub": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-idea": "Idea", "@sage/xtrem-ui/detailed-icon-name-incline": "Incline", "@sage/xtrem-ui/detailed-icon-name-industry": "Industry", "@sage/xtrem-ui/detailed-icon-name-info": "Info", "@sage/xtrem-ui/detailed-icon-name-integration": "Integration", "@sage/xtrem-ui/detailed-icon-name-jewelry": "Jewelry", "@sage/xtrem-ui/detailed-icon-name-keys": "Keys", "@sage/xtrem-ui/detailed-icon-name-lab": "Lab", "@sage/xtrem-ui/detailed-icon-name-label": "Label", "@sage/xtrem-ui/detailed-icon-name-laptop": "Laptop", "@sage/xtrem-ui/detailed-icon-name-lightning": "Lightning", "@sage/xtrem-ui/detailed-icon-name-like": "Like", "@sage/xtrem-ui/detailed-icon-name-link": "Link", "@sage/xtrem-ui/detailed-icon-name-locations": "Locations", "@sage/xtrem-ui/detailed-icon-name-lock": "Lock", "@sage/xtrem-ui/detailed-icon-name-lock_unlocked": "Lock Unlocked", "@sage/xtrem-ui/detailed-icon-name-mail": "Mail", "@sage/xtrem-ui/detailed-icon-name-map": "Map", "@sage/xtrem-ui/detailed-icon-name-medical": "Medical", "@sage/xtrem-ui/detailed-icon-name-megaphone": "Megaphone", "@sage/xtrem-ui/detailed-icon-name-memo": "Memo", "@sage/xtrem-ui/detailed-icon-name-microphone": "Microphone", "@sage/xtrem-ui/detailed-icon-name-minus": "Minus", "@sage/xtrem-ui/detailed-icon-name-mouse": "Mouse", "@sage/xtrem-ui/detailed-icon-name-newspaper": "Newspaper", "@sage/xtrem-ui/detailed-icon-name-note": "Note", "@sage/xtrem-ui/detailed-icon-name-notebook": "Notebook", "@sage/xtrem-ui/detailed-icon-name-office": "Office", "@sage/xtrem-ui/detailed-icon-name-page": "Page", "@sage/xtrem-ui/detailed-icon-name-payment": "Payment", "@sage/xtrem-ui/detailed-icon-name-payroll": "Payroll", "@sage/xtrem-ui/detailed-icon-name-pen": "Pen", "@sage/xtrem-ui/detailed-icon-name-pencil": "Pencil", "@sage/xtrem-ui/detailed-icon-name-person": "Person", "@sage/xtrem-ui/detailed-icon-name-phone": "Phone", "@sage/xtrem-ui/detailed-icon-name-pin": "<PERSON>n", "@sage/xtrem-ui/detailed-icon-name-plus": "Plus", "@sage/xtrem-ui/detailed-icon-name-point": "Point", "@sage/xtrem-ui/detailed-icon-name-pound": "Pound", "@sage/xtrem-ui/detailed-icon-name-power": "Power", "@sage/xtrem-ui/detailed-icon-name-presentation": "Presentation", "@sage/xtrem-ui/detailed-icon-name-print": "Print", "@sage/xtrem-ui/detailed-icon-name-processing": "Processing", "@sage/xtrem-ui/detailed-icon-name-puzzle": "Puzzle", "@sage/xtrem-ui/detailed-icon-name-question": "Question", "@sage/xtrem-ui/detailed-icon-name-receipts": "Receipts", "@sage/xtrem-ui/detailed-icon-name-recycle": "Recycle", "@sage/xtrem-ui/detailed-icon-name-redo": "Redo", "@sage/xtrem-ui/detailed-icon-name-remote": "Remote", "@sage/xtrem-ui/detailed-icon-name-rocket": "Rocket", "@sage/xtrem-ui/detailed-icon-name-safe": "Safe", "@sage/xtrem-ui/detailed-icon-name-satelite": "Satellite", "@sage/xtrem-ui/detailed-icon-name-savings": "Savings", "@sage/xtrem-ui/detailed-icon-name-scissors": "Scissors", "@sage/xtrem-ui/detailed-icon-name-server": "Server", "@sage/xtrem-ui/detailed-icon-name-service": "Service", "@sage/xtrem-ui/detailed-icon-name-setting": "Setting", "@sage/xtrem-ui/detailed-icon-name-share": "Share", "@sage/xtrem-ui/detailed-icon-name-shoes": "Shoes", "@sage/xtrem-ui/detailed-icon-name-shuffle": "Shuffle", "@sage/xtrem-ui/detailed-icon-name-sign": "Sign", "@sage/xtrem-ui/detailed-icon-name-sim": "SIM Card", "@sage/xtrem-ui/detailed-icon-name-smartphone": "Smartphone", "@sage/xtrem-ui/detailed-icon-name-stationeries": "Stationeries", "@sage/xtrem-ui/detailed-icon-name-store": "Store", "@sage/xtrem-ui/detailed-icon-name-support": "Support", "@sage/xtrem-ui/detailed-icon-name-sync": "Sync", "@sage/xtrem-ui/detailed-icon-name-tab": "Tab", "@sage/xtrem-ui/detailed-icon-name-table": "Table", "@sage/xtrem-ui/detailed-icon-name-tablet": "Tablet", "@sage/xtrem-ui/detailed-icon-name-thermometer": "Thermometer", "@sage/xtrem-ui/detailed-icon-name-timer": "Timer", "@sage/xtrem-ui/detailed-icon-name-tools": "Tools", "@sage/xtrem-ui/detailed-icon-name-travel": "Travel", "@sage/xtrem-ui/detailed-icon-name-truck": "Truck", "@sage/xtrem-ui/detailed-icon-name-undo": "Undo", "@sage/xtrem-ui/detailed-icon-name-video": "Video", "@sage/xtrem-ui/detailed-icon-name-wallet": "Wallet", "@sage/xtrem-ui/detailed-icon-name-warehouse": "Warehouse", "@sage/xtrem-ui/detailed-icon-name-warning": "Warning", "@sage/xtrem-ui/detailed-icon-name-weather": "Weather", "@sage/xtrem-ui/detailed-icon-name-wireless": "Wireless", "@sage/xtrem-ui/detailed-icon-name-wrench": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-writing": "Writing", "@sage/xtrem-ui/dialog-actions-onload-unhandled-error": "An unhandled error ocurred", "@sage/xtrem-ui/dialog-loading": "Loading...", "@sage/xtrem-ui/dialogs-async-loader-button-keep-waiting": "Keep waiting", "@sage/xtrem-ui/dialogs-async-loader-button-notify-me": "Notify me", "@sage/xtrem-ui/dialogs-async-loader-button-stop": "Stop", "@sage/xtrem-ui/dialogs-async-loader-waiting": "This action may take a while...", "@sage/xtrem-ui/dirty-phantom-row-validation-error-message-mac": "You need to select the line and validate it by selecting command + Enter, so that you can save your document.", "@sage/xtrem-ui/dirty-phantom-row-validation-error-message-pc": "You need to select the line and validate it by selecting Ctrl + Enter, so that you can save your document.", "@sage/xtrem-ui/display-errors-back-to-full-display": "Back to full display", "@sage/xtrem-ui/display-errors-back-to-full-display-tooltip": "For full display", "@sage/xtrem-ui/display-errors-button": "Display these errors", "@sage/xtrem-ui/distinct-count": "Distinct count", "@sage/xtrem-ui/divisor": "Divisor", "@sage/xtrem-ui/divisor-helper-text": "Reduce large numbers by entering a divisor.", "@sage/xtrem-ui/document-metadata": "Document metadata", "@sage/xtrem-ui/drag-drop-file": "Drag and drop your files here.", "@sage/xtrem-ui/duplicate-error": "Duplication error: {{0}}", "@sage/xtrem-ui/empty-state-filter-text-no-results-button": "Clear filters", "@sage/xtrem-ui/empty-state-filter-text-no-results-description": "Try different criteria or create a new record to get started.", "@sage/xtrem-ui/empty-state-filter-text-title": "No results found.", "@sage/xtrem-ui/empty-state-text": "This list has no items yet.", "@sage/xtrem-ui/error": "Error", "@sage/xtrem-ui/error-detail-copied-to-clipboard": "Error detail copied to clipboard", "@sage/xtrem-ui/error-loading-stickers": "An error ocurred while loading the stickers", "@sage/xtrem-ui/export-format-csv": "CSV", "@sage/xtrem-ui/export-format-excel": "Excel", "@sage/xtrem-ui/failed-to-refresh-navigation-panel": "Failed to refresh navigation panel.", "@sage/xtrem-ui/false": "False", "@sage/xtrem-ui/field-mandatory": "You need to enter a value.", "@sage/xtrem-ui/field-max-items-value": "{{0}} the maximum number of selectable items is {{1}}", "@sage/xtrem-ui/field-maximum-date-value": "{{0}} maximum date is {{1}}", "@sage/xtrem-ui/field-maximum-length-value": "{{0}} maximum length is {{1}}", "@sage/xtrem-ui/field-maximum-value": "{{0}} maximum value is {{1}}", "@sage/xtrem-ui/field-min-items-value": "{{0}} the minimum number of selectable items is {{1}}", "@sage/xtrem-ui/field-minimum-date-value": "{{0}} minimum date is {{1}}", "@sage/xtrem-ui/field-minimum-length-value": "{{0}} minimum length is {{1}}", "@sage/xtrem-ui/field-minimum-value": "{{0}} minimum value is {{1}}", "@sage/xtrem-ui/field-non-zero": "{{0}} cannot be zero", "@sage/xtrem-ui/field-not-valid": "{{0}} is not valid", "@sage/xtrem-ui/field-select-mandatory": "You need to select a value.", "@sage/xtrem-ui/field-select-mandatory-checkbox": "You need to select this checkbox.", "@sage/xtrem-ui/field-select-mandatory-or-enter": "You need to select or enter a value.", "@sage/xtrem-ui/file-component-browse-file": "Browse to File", "@sage/xtrem-ui/file-component-browse-files": "Browse Files", "@sage/xtrem-ui/file-component-drag-drop": "or \n Drag and drop your file here", "@sage/xtrem-ui/file-component-drag-drop-empty": "or \n This list has no attached files yet. \n Drag and drop your file here.", "@sage/xtrem-ui/file-component-hide-upload-area": "Hide upload area", "@sage/xtrem-ui/file-component-show-upload-area": "Show upload area", "@sage/xtrem-ui/file-upload-failed": "Failed to upload file.", "@sage/xtrem-ui/filter-manager-apply-button": "Apply", "@sage/xtrem-ui/filter-manager-cancel-button": "Cancel", "@sage/xtrem-ui/filter-manager-checkbox": "Is active", "@sage/xtrem-ui/filter-manager-clear-all-button": "Clear All", "@sage/xtrem-ui/filter-manager-close": "Close", "@sage/xtrem-ui/filter-manager-invalid-filters": "Some filters are invalid", "@sage/xtrem-ui/filter-manager-max-value": "Max value", "@sage/xtrem-ui/filter-manager-min-value": "Min value", "@sage/xtrem-ui/filter-manager-open": "Open", "@sage/xtrem-ui/filter-manager-range-end": "End date", "@sage/xtrem-ui/filter-manager-range-start": "Start date", "@sage/xtrem-ui/filter-manager-title-header": "Filters", "@sage/xtrem-ui/filter-range": "{{fieldTitle}} is between {{value1}} and {{value2}}", "@sage/xtrem-ui/filter-search-placeholder": "Search", "@sage/xtrem-ui/filter-value-equals": "{{fieldTitle}} is {{filterValue}}", "@sage/xtrem-ui/filter-value-greater-than-equal": "{{fieldTitle}} >= {{filterValue}}", "@sage/xtrem-ui/filter-value-less-than-equal": "{{fieldTitle}} <= {{filterValue}}", "@sage/xtrem-ui/filter-value-not-equal": "{{fieldTitle}} != {{filterValue}}", "@sage/xtrem-ui/floating-filter-label": "{{ filterName }} filter input", "@sage/xtrem-ui/footer-actions-more-button": "More", "@sage/xtrem-ui/form-designer-var-current-date": "Current date", "@sage/xtrem-ui/form-designer-var-generation-by": "Generated by", "@sage/xtrem-ui/form-designer-var-page-number": "Page number", "@sage/xtrem-ui/form-designer-var-parameter": "Parameter - {{name}}", "@sage/xtrem-ui/form-designer-var-total-number-of-pages": "Total number of pages", "@sage/xtrem-ui/general_invalid_json": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/general-finish-editing": "Finish editing", "@sage/xtrem-ui/general-welcome-page": "Welcome page", "@sage/xtrem-ui/generic-no": "No", "@sage/xtrem-ui/generic-yes": "Yes", "@sage/xtrem-ui/greaterThanOrEqual": "Greater than or equal to", "@sage/xtrem-ui/group-by": "Group by", "@sage/xtrem-ui/group-by-day": "Day", "@sage/xtrem-ui/group-by-month": "Month", "@sage/xtrem-ui/group-by-this-column": "Group by this column", "@sage/xtrem-ui/group-by-year": "Year", "@sage/xtrem-ui/helper-text-contains": "contains", "@sage/xtrem-ui/helper-text-ends-with": "ends with", "@sage/xtrem-ui/helper-text-matches": "matches", "@sage/xtrem-ui/helper-text-starts-with": "starts with", "@sage/xtrem-ui/hide-floating-filters": "Hide Table Filters", "@sage/xtrem-ui/hide-technical-details": "Hide technical details", "@sage/xtrem-ui/hours": "Hours", "@sage/xtrem-ui/image-component-add-image": "Add an image", "@sage/xtrem-ui/image-field-remove-image": "Remove", "@sage/xtrem-ui/infinite-indicator-end": "No end date", "@sage/xtrem-ui/infinite-indicator-start": "No start date", "@sage/xtrem-ui/insight-plural": "{{insightCount}} insights", "@sage/xtrem-ui/insight-singular": "1 insight", "@sage/xtrem-ui/invalid-file-type-message": "{{0}} is not allowed for security reasons.", "@sage/xtrem-ui/invalid-number": "Invalid number", "@sage/xtrem-ui/invalid-response-no-data": "Invalid response, no data provided", "@sage/xtrem-ui/Label": "Label", "@sage/xtrem-ui/last-30-days": "Last 30 days", "@sage/xtrem-ui/last-7-days": "Last 7 days", "@sage/xtrem-ui/link-error-numbers-tooltip": "Error information", "@sage/xtrem-ui/link-error-quantity": "1 error", "@sage/xtrem-ui/link-errors-quantity": "{{0}} errors", "@sage/xtrem-ui/list-printing": "Select report", "@sage/xtrem-ui/list-printing-assignment": "Assign report", "@sage/xtrem-ui/lookup-dialog-confirm-select": "Select", "@sage/xtrem-ui/lookup-dialog-create-new-item": "Create new item", "@sage/xtrem-ui/lookup-dialog-dialog-title": "Selection", "@sage/xtrem-ui/lookup-dialog-failed-fetch": "Failed to fetch options", "@sage/xtrem-ui/main-list-refresh": "Refresh", "@sage/xtrem-ui/maximum": "Maximum", "@sage/xtrem-ui/message-type-ai": "AI", "@sage/xtrem-ui/message-type-error": "Error", "@sage/xtrem-ui/message-type-info": "Information", "@sage/xtrem-ui/message-type-information": "Information", "@sage/xtrem-ui/message-type-success": "Success", "@sage/xtrem-ui/message-type-warning": "Warning", "@sage/xtrem-ui/mime-type/application/atom+xml": "Atom XML Feed", "@sage/xtrem-ui/mime-type/application/ecmascript": "ECMAScript", "@sage/xtrem-ui/mime-type/application/font-woff": "Web Open Font Format", "@sage/xtrem-ui/mime-type/application/font-woff2": "Web Open Font Format 2", "@sage/xtrem-ui/mime-type/application/graphql": "GraphQL Query", "@sage/xtrem-ui/mime-type/application/java-archive": "Java Archive", "@sage/xtrem-ui/mime-type/application/javascript": "JavaScript", "@sage/xtrem-ui/mime-type/application/json": "JSON", "@sage/xtrem-ui/mime-type/application/ld+json": "JSON-LD Document", "@sage/xtrem-ui/mime-type/application/mac-binhex40": "BinHex Archive", "@sage/xtrem-ui/mime-type/application/mathml+xml": "MathML Document", "@sage/xtrem-ui/mime-type/application/ms-visio": "MS Visio document", "@sage/xtrem-ui/mime-type/application/ms-visio.stencil.macroEnabled.12": "MS Visio document with macros", "@sage/xtrem-ui/mime-type/application/ms-visio.template": "MS Visio template", "@sage/xtrem-ui/mime-type/application/msword": "MS Word Document", "@sage/xtrem-ui/mime-type/application/octet-stream": "Binary Data", "@sage/xtrem-ui/mime-type/application/pdf": "PDF Document", "@sage/xtrem-ui/mime-type/application/postscript": "PostScript", "@sage/xtrem-ui/mime-type/application/rdf+xml": "RDF Document", "@sage/xtrem-ui/mime-type/application/rss+xml": "RSS XML Feed", "@sage/xtrem-ui/mime-type/application/rtf": "Rich Text Format", "@sage/xtrem-ui/mime-type/application/sql": "SQL Database", "@sage/xtrem-ui/mime-type/application/vnd.amazon.ebook": "Amazon Kindle eBook", "@sage/xtrem-ui/mime-type/application/vnd.android.package-archive": "Android APK", "@sage/xtrem-ui/mime-type/application/vnd.apple.installer+xml": "Apple Installer Package", "@sage/xtrem-ui/mime-type/application/vnd.apple.keynote": "Apple Keynote", "@sage/xtrem-ui/mime-type/application/vnd.apple.numbers": "Apple Numbers", "@sage/xtrem-ui/mime-type/application/vnd.apple.pages": "Apple Pages", "@sage/xtrem-ui/mime-type/application/vnd.google-earth.kml+xml": "Google Earth KML", "@sage/xtrem-ui/mime-type/application/vnd.google-earth.kmz": "Google Earth KMZ", "@sage/xtrem-ui/mime-type/application/vnd.mozilla.xul+xml": "Mozilla XUL", "@sage/xtrem-ui/mime-type/application/vnd.ms-access": "Microsoft Access Database", "@sage/xtrem-ui/mime-type/application/vnd.ms-excel": "MS Excel Document", "@sage/xtrem-ui/mime-type/application/vnd.ms-fontobject": "Embedded OpenType Font", "@sage/xtrem-ui/mime-type/application/vnd.ms-powerpoint": "MS PowerPoint Document", "@sage/xtrem-ui/mime-type/application/vnd.ms-project": "Microsoft Project Document", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.graphics": "OpenDocument Graphics", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.presentation": "OpenDocument Presentation", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.spreadsheet": "OpenDocument Spreadsheet", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.text": "OpenDocument Text", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.presentationml.presentation": "MS PowerPoint Document", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "MS Excel Document", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.wordprocessingml.document": "MS Word Document", "@sage/xtrem-ui/mime-type/application/vnd.rn-realmedia": "RealMedia", "@sage/xtrem-ui/mime-type/application/vnd.wap.wmlc": "WMLC Document", "@sage/xtrem-ui/mime-type/application/x-7z-compressed": "7z Archive", "@sage/xtrem-ui/mime-type/application/x-abiword": "AbiWord Document", "@sage/xtrem-ui/mime-type/application/x-bzip": "Bzip Archive", "@sage/xtrem-ui/mime-type/application/x-bzip2": "Bzip2 Archive", "@sage/xtrem-ui/mime-type/application/x-cd-image": "CD Image", "@sage/xtrem-ui/mime-type/application/x-chrome-extension": "Chrome Extension", "@sage/xtrem-ui/mime-type/application/x-cocoa": "Cocoa Archive", "@sage/xtrem-ui/mime-type/application/x-csh": "C <PERSON>", "@sage/xtrem-ui/mime-type/application/x-deb": "Debian Package", "@sage/xtrem-ui/mime-type/application/x-dvi": "DVI Document", "@sage/xtrem-ui/mime-type/application/x-font-opentype": "OpenType Font", "@sage/xtrem-ui/mime-type/application/x-font-otf": "OpenType Font", "@sage/xtrem-ui/mime-type/application/x-font-ttf": "TrueType Font", "@sage/xtrem-ui/mime-type/application/x-font-woff": "Web Open Font Format", "@sage/xtrem-ui/mime-type/application/x-font-woff2": "Web Open Font Format 2", "@sage/xtrem-ui/mime-type/application/x-gtar": "GNU Tar Archive", "@sage/xtrem-ui/mime-type/application/x-gzip": "GZIP Archive", "@sage/xtrem-ui/mime-type/application/x-hdf": "Hierarchical Data Format (HDF)", "@sage/xtrem-ui/mime-type/application/x-httpd-php": "PHP Script", "@sage/xtrem-ui/mime-type/application/x-httpd-php-source": "PHP Source Code", "@sage/xtrem-ui/mime-type/application/x-java-applet": "Java Applet", "@sage/xtrem-ui/mime-type/application/x-java-archive": "Java Archive (JAR)", "@sage/xtrem-ui/mime-type/application/x-java-archive-diff": "Java Archive Diff", "@sage/xtrem-ui/mime-type/application/x-java-jnlp-file": "JNLP File", "@sage/xtrem-ui/mime-type/application/x-javascript": "Old JavaScript", "@sage/xtrem-ui/mime-type/application/x-latex": "LaTeX Document", "@sage/xtrem-ui/mime-type/application/x-lzh-compressed": "LZH Archive", "@sage/xtrem-ui/mime-type/application/x-makeself": "Makeself Archive", "@sage/xtrem-ui/mime-type/application/x-mif": "FrameMaker Interchange Format", "@sage/xtrem-ui/mime-type/application/x-msaccess": "MS Access Database", "@sage/xtrem-ui/mime-type/application/x-msdownload": "MS Download", "@sage/xtrem-ui/mime-type/application/x-msmetafile": "Windows Metafile", "@sage/xtrem-ui/mime-type/application/x-msmoney": "MS Money", "@sage/xtrem-ui/mime-type/application/x-perl": "<PERSON><PERSON>", "@sage/xtrem-ui/mime-type/application/x-pilot": "Pilot Archive", "@sage/xtrem-ui/mime-type/application/x-rar-compressed": "RAR Archive", "@sage/xtrem-ui/mime-type/application/x-redhat-package-manager": "RedHat Package", "@sage/xtrem-ui/mime-type/application/x-rpm": "RPM Package", "@sage/xtrem-ui/mime-type/application/x-sea": "Sea Archive", "@sage/xtrem-ui/mime-type/application/x-sh": "Bash Shell Script", "@sage/xtrem-ui/mime-type/application/x-shockwave-flash": "Flash Animation", "@sage/xtrem-ui/mime-type/application/x-sql": "SQL Database", "@sage/xtrem-ui/mime-type/application/x-stuffit": "StuffIt Archive", "@sage/xtrem-ui/mime-type/application/x-tar": "TAR Archive", "@sage/xtrem-ui/mime-type/application/x-tcl": "Tcl Script", "@sage/xtrem-ui/mime-type/application/x-tex": "TeX Document", "@sage/xtrem-ui/mime-type/application/x-texinfo": "Texinfo Document", "@sage/xtrem-ui/mime-type/application/x-troff": "T<PERSON>ff Document", "@sage/xtrem-ui/mime-type/application/x-vrml": "VRML", "@sage/xtrem-ui/mime-type/application/x-www-form-urlencoded": "Form URL Encoded Data", "@sage/xtrem-ui/mime-type/application/x-x509-ca-cert": "X.509 Certificate", "@sage/xtrem-ui/mime-type/application/x-xpinstall": "Mozilla XPI Install", "@sage/xtrem-ui/mime-type/application/xhtml+xml": "XHTML", "@sage/xtrem-ui/mime-type/application/xml": "XML", "@sage/xtrem-ui/mime-type/application/xslt+xml": "XSLT", "@sage/xtrem-ui/mime-type/application/zip": "ZIP Archive", "@sage/xtrem-ui/mime-type/audio/aac": "AAC Audio", "@sage/xtrem-ui/mime-type/audio/amr": "AMR Audio", "@sage/xtrem-ui/mime-type/audio/midi": "MIDI Audio", "@sage/xtrem-ui/mime-type/audio/mpeg": "MP3 Audio", "@sage/xtrem-ui/mime-type/audio/ogg": "Ogg Audio", "@sage/xtrem-ui/mime-type/audio/vnd.rn-realaudio": "RealAudio", "@sage/xtrem-ui/mime-type/audio/wav": "WAV Audio", "@sage/xtrem-ui/mime-type/audio/x-m4a": "M4A Audio", "@sage/xtrem-ui/mime-type/audio/x-matroska": "Matroska Audio", "@sage/xtrem-ui/mime-type/audio/x-mpegurl": "MPEG URL Stream", "@sage/xtrem-ui/mime-type/audio/x-ms-wax": "WMA Audio Playlist", "@sage/xtrem-ui/mime-type/audio/x-ms-wma": "Windows Media Audio", "@sage/xtrem-ui/mime-type/audio/x-pn-realaudio": "RealAudio", "@sage/xtrem-ui/mime-type/audio/x-pn-realaudio-plugin": "RealAudio Plugin", "@sage/xtrem-ui/mime-type/audio/x-realaudio": "RealAudio", "@sage/xtrem-ui/mime-type/audio/x-wav": "WAV Audio", "@sage/xtrem-ui/mime-type/chemical/x-pdb": "Protein Data Bank", "@sage/xtrem-ui/mime-type/image/bmp": "Bitmap Image", "@sage/xtrem-ui/mime-type/image/cgm": "Computer Graphics Metafile", "@sage/xtrem-ui/mime-type/image/gif": "GIF Image", "@sage/xtrem-ui/mime-type/image/jpeg": "JPEG Image", "@sage/xtrem-ui/mime-type/image/png": "PNG Image", "@sage/xtrem-ui/mime-type/image/svg+xml": "SVG Image", "@sage/xtrem-ui/mime-type/image/tiff": "TIFF Image", "@sage/xtrem-ui/mime-type/image/vnd.microsoft.icon": "ICO Image", "@sage/xtrem-ui/mime-type/image/vnd.wap.wbmp": "WBMP Image", "@sage/xtrem-ui/mime-type/image/webp": "WebP Image", "@sage/xtrem-ui/mime-type/image/x-cmu-raster": "CMU Image", "@sage/xtrem-ui/mime-type/image/x-icon": "Icon Image", "@sage/xtrem-ui/mime-type/image/x-jng": "JNG Image", "@sage/xtrem-ui/mime-type/image/x-ms-bmp": "Bitmap Image", "@sage/xtrem-ui/mime-type/image/x-portable-anymap": "Portable AnyMap Image", "@sage/xtrem-ui/mime-type/image/x-portable-bitmap": "Portable Bitmap Image", "@sage/xtrem-ui/mime-type/image/x-portable-graymap": "Portable Graymap Image", "@sage/xtrem-ui/mime-type/image/x-portable-pixmap": "Portable Pixmap Image", "@sage/xtrem-ui/mime-type/image/x-rgb": "RGB Image", "@sage/xtrem-ui/mime-type/image/x-xbitmap": "X Bitmap Image", "@sage/xtrem-ui/mime-type/image/x-xpixmap": "X Pixmap Image", "@sage/xtrem-ui/mime-type/image/x-xwindowdump": "X Window Dump Image", "@sage/xtrem-ui/mime-type/model/vrml": "VRML Model", "@sage/xtrem-ui/mime-type/multipart/form-data": "Multipart Form Data", "@sage/xtrem-ui/mime-type/text/cache-manifest": "<PERSON><PERSON>", "@sage/xtrem-ui/mime-type/text/calendar": "iCalendar", "@sage/xtrem-ui/mime-type/text/css": "CSS", "@sage/xtrem-ui/mime-type/text/csv": "CSV (Comma-Separated Values)", "@sage/xtrem-ui/mime-type/text/ecmascript": "ECMAScript", "@sage/xtrem-ui/mime-type/text/html": "HTML", "@sage/xtrem-ui/mime-type/text/javascript": "JavaScript", "@sage/xtrem-ui/mime-type/text/markdown": "Markdown Document", "@sage/xtrem-ui/mime-type/text/mathml": "MathML", "@sage/xtrem-ui/mime-type/text/plain": "Plain Text", "@sage/xtrem-ui/mime-type/text/richtext": "Rich Text", "@sage/xtrem-ui/mime-type/text/rtf": "Rich Text Format", "@sage/xtrem-ui/mime-type/text/tab-separated-values": "Tab-Separated Values", "@sage/xtrem-ui/mime-type/text/vnd.sun.j2me.app-descriptor": "J2ME App Descriptor", "@sage/xtrem-ui/mime-type/text/vnd.wap.wml": "WML", "@sage/xtrem-ui/mime-type/text/vnd.wap.wmlscript": "WMLScript", "@sage/xtrem-ui/mime-type/text/vtt": "WebVTT", "@sage/xtrem-ui/mime-type/text/x-component": "HTC Component", "@sage/xtrem-ui/mime-type/text/x-cross-domain-policy": "Cross-domain Policy", "@sage/xtrem-ui/mime-type/text/x-fortran": "Fortran Source Code", "@sage/xtrem-ui/mime-type/text/xml": "XML Document", "@sage/xtrem-ui/mime-type/video/3gpp": "3GPP Video", "@sage/xtrem-ui/mime-type/video/mp2t": "MPEG-2 TS Video", "@sage/xtrem-ui/mime-type/video/mp4": "MP4 Video", "@sage/xtrem-ui/mime-type/video/mpeg": "MPEG Video", "@sage/xtrem-ui/mime-type/video/ogg": "Ogg Video", "@sage/xtrem-ui/mime-type/video/quicktime": "QuickTime Video", "@sage/xtrem-ui/mime-type/video/vnd.rn-realvideo": "RealVideo", "@sage/xtrem-ui/mime-type/video/webm": "WebM Video", "@sage/xtrem-ui/mime-type/video/x-flv": "FLV Video", "@sage/xtrem-ui/mime-type/video/x-m4v": "M4V Video", "@sage/xtrem-ui/mime-type/video/x-matroska": "Matroska Video", "@sage/xtrem-ui/mime-type/video/x-mng": "MNG Video", "@sage/xtrem-ui/mime-type/video/x-ms-asf": "ASF Video", "@sage/xtrem-ui/mime-type/video/x-ms-wmv": "WMV Video", "@sage/xtrem-ui/mime-type/video/x-ms-wvx": "WMV Video Playlist", "@sage/xtrem-ui/mime-type/video/x-msvideo": "AVI Video", "@sage/xtrem-ui/minimum": "Minimum", "@sage/xtrem-ui/minutes": "Minutes", "@sage/xtrem-ui/mobile-table-load-more": "Load more", "@sage/xtrem-ui/multi-file-deposit-cancel": "Cancel", "@sage/xtrem-ui/multi-file-deposit-create-tag": "Create tag", "@sage/xtrem-ui/multi-file-deposit-description": "Description", "@sage/xtrem-ui/multi-file-deposit-edit-details": "Edit file details", "@sage/xtrem-ui/multi-file-deposit-filename": "File", "@sage/xtrem-ui/multi-file-deposit-modified": "Modified", "@sage/xtrem-ui/multi-file-deposit-open-preview": "Open preview", "@sage/xtrem-ui/multi-file-deposit-remove": "Remove", "@sage/xtrem-ui/multi-file-deposit-size": "Size", "@sage/xtrem-ui/multi-file-deposit-status": "Status", "@sage/xtrem-ui/multi-file-deposit-status-created": "In progress", "@sage/xtrem-ui/multi-file-deposit-status-upload-cancelled": "Cancelled", "@sage/xtrem-ui/multi-file-deposit-status-upload-failed": "Failed", "@sage/xtrem-ui/multi-file-deposit-status-upload-rejected": "Rejected", "@sage/xtrem-ui/multi-file-deposit-status-uploaded": "Pending", "@sage/xtrem-ui/multi-file-deposit-status-verified": "Uploaded", "@sage/xtrem-ui/multi-file-deposit-tag-description": "Description", "@sage/xtrem-ui/multi-file-deposit-tag-id": "ID", "@sage/xtrem-ui/multi-file-deposit-tag-title": "Title", "@sage/xtrem-ui/multi-file-deposit-tags": "Tags", "@sage/xtrem-ui/multi-file-deposit-title": "Title", "@sage/xtrem-ui/multi-file-deposit-type": "Type", "@sage/xtrem-ui/multi-file-deposit-uploaded": "Uploaded", "@sage/xtrem-ui/multi-file-deposit-uploaded-by": "Uploaded by", "@sage/xtrem-ui/multi-file-upload-cancel": "Cancel file upload in progress.", "@sage/xtrem-ui/multi-file-upload-cancel-title": "Cancel upload", "@sage/xtrem-ui/multi-file-upload-remove": "Remove uploaded file.", "@sage/xtrem-ui/multi-file-upload-remove-title": "Remove file", "@sage/xtrem-ui/must-be-a-number": "You need to enter a number", "@sage/xtrem-ui/must-be-between-1-and-365": "Enter a number between 1 and 365", "@sage/xtrem-ui/must-be-between-zero-and-four": "This value needs to be between 0 and 4.", "@sage/xtrem-ui/must-be-greater-than-zero": "This value needs to be greater than 0.", "@sage/xtrem-ui/name": "Name", "@sage/xtrem-ui/navigation-panel-failed": "An error ocurred loading the navigation panel items", "@sage/xtrem-ui/navigation-panel-my-view": "My selected data", "@sage/xtrem-ui/navigation-panel-no-filter": "All", "@sage/xtrem-ui/navigation-panel-no-results": "There are no data to display", "@sage/xtrem-ui/new": "New", "@sage/xtrem-ui/next-day": "Next day", "@sage/xtrem-ui/next-month": "Next month", "@sage/xtrem-ui/next-record": "Next record", "@sage/xtrem-ui/next-week": "Next week", "@sage/xtrem-ui/next-year": "Next year", "@sage/xtrem-ui/no-available-image": "No available image", "@sage/xtrem-ui/no-dashboard-heading": "This page has no dashboard.", "@sage/xtrem-ui/no-dashboard-subtitle": "Create a dashboard to get started.", "@sage/xtrem-ui/no-dashboard-title": "Drive your business", "@sage/xtrem-ui/no-data": "No data to display", "@sage/xtrem-ui/no-file-available": "No file available", "@sage/xtrem-ui/no-node": "No node was provided neither {{0}} specifies one", "@sage/xtrem-ui/no-page-content-found": "Could not find any content for page. Make sure the page exists and the format of the URL is /@<vendor name>/@<package name>/@<page name>", "@sage/xtrem-ui/no-pages-found": "Could not find any pages", "@sage/xtrem-ui/no-record-id-provided": "No record id was provided through query parameters", "@sage/xtrem-ui/no-sticker-content-found": "Could not find any content for this sticker", "@sage/xtrem-ui/no-stickers-found": "Could not find any stickers", "@sage/xtrem-ui/no-value": "No value", "@sage/xtrem-ui/no-widget-content-found": "Could not find any content for the {{widget}} widget", "@sage/xtrem-ui/number-format-separator": ".", "@sage/xtrem-ui/ok": "OK", "@sage/xtrem-ui/open-custom-field-dialog": "Create field", "@sage/xtrem-ui/open-dynamic-select": "Open list", "@sage/xtrem-ui/open-full-screen": "Open fullscreen", "@sage/xtrem-ui/open-header-section": "Open header", "@sage/xtrem-ui/open-lookup": "Open lookup dialog", "@sage/xtrem-ui/open-record-history-dialog": "Record history", "@sage/xtrem-ui/open-section": "Open section", "@sage/xtrem-ui/openFilters": "Open Filter", "@sage/xtrem-ui/page-failed-to-load": "Failed to load page", "@sage/xtrem-ui/parent": "Parent", "@sage/xtrem-ui/pdf-metadata-file-name": "**File name:** {{name}}", "@sage/xtrem-ui/pdf-metadata-file-size": "**File size:** {{size}}", "@sage/xtrem-ui/pdf-metadata-file-type": "**File type:** {{fileType}}", "@sage/xtrem-ui/pdf-metadata-number-of-lines": "**Number of lines:** {{lineCount}}", "@sage/xtrem-ui/pdf-metadata-pdf-version": "**PDF version:** {{version}}", "@sage/xtrem-ui/pdf-metadata-producer": "**Producer application:** {{producer}}", "@sage/xtrem-ui/pdf-metadata-resolution": "**Resolution:** {{resolution}}", "@sage/xtrem-ui/please-select-placeholder": "Please Select...", "@sage/xtrem-ui/pod-collection-add-new": "Add an item", "@sage/xtrem-ui/pod-collection-cancel-button": "Cancel", "@sage/xtrem-ui/pod-collection-remove-button": "Remove", "@sage/xtrem-ui/pod-collection-remove-text": "You are about to remove this item.", "@sage/xtrem-ui/pod-collection-remove-title": "Removing Item", "@sage/xtrem-ui/pod-placeholder-text": "No data to display", "@sage/xtrem-ui/pod-remove-item": "Remove Item", "@sage/xtrem-ui/populate-list-title-default": "Add a new list", "@sage/xtrem-ui/preview-action-download": "Download", "@sage/xtrem-ui/preview-action-print": "Print", "@sage/xtrem-ui/preview-close": "Close preview", "@sage/xtrem-ui/preview-go-to-page": "Go to page {{pageNumber}}", "@sage/xtrem-ui/preview-more-file-info": " File information", "@sage/xtrem-ui/preview-more-options": "More options", "@sage/xtrem-ui/preview-more-thumbnails": "Thumbnails", "@sage/xtrem-ui/preview-no-file-selected": "No file selected.", "@sage/xtrem-ui/preview-no-preview-available": "No preview available.", "@sage/xtrem-ui/preview-page-next": "Next page", "@sage/xtrem-ui/preview-page-prev": "Prev page", "@sage/xtrem-ui/preview-zoom-in": "Zoom in", "@sage/xtrem-ui/preview-zoom-level": "Zoom level", "@sage/xtrem-ui/preview-zoom-out": "Zoom out", "@sage/xtrem-ui/previous-day": "Previous day", "@sage/xtrem-ui/previous-month": "Previous month", "@sage/xtrem-ui/previous-record": "Previous record", "@sage/xtrem-ui/previous-week": "Previous week", "@sage/xtrem-ui/previous-year": "Previous year", "@sage/xtrem-ui/property": "Property", "@sage/xtrem-ui/record-history-created-title": "Created", "@sage/xtrem-ui/record-history-details": "By **{{name}}** on **{{date}}** at **{{time}}**", "@sage/xtrem-ui/record-history-failed": "Failed to get the record history. Try again later.", "@sage/xtrem-ui/record-history-last-update-title": "Last update", "@sage/xtrem-ui/reference-create-new-item-link": "Create new item", "@sage/xtrem-ui/reference-filter-clear-selection": "Clear selected items", "@sage/xtrem-ui/reference-lookup-dialog-search-placeholder": "Search...", "@sage/xtrem-ui/reference-open-lookup-link": "View the list", "@sage/xtrem-ui/return-arrow": "Return", "@sage/xtrem-ui/same-day": "Current day", "@sage/xtrem-ui/same-month": "Current month", "@sage/xtrem-ui/same-week": "Current week", "@sage/xtrem-ui/same-year": "Current year", "@sage/xtrem-ui/save-new-view-as": "Save view as", "@sage/xtrem-ui/see-more-items": "See more items", "@sage/xtrem-ui/select-component-loading": "Loading...", "@sage/xtrem-ui/select-component-no-results": "No items found.", "@sage/xtrem-ui/select-component-type-more-characters": "Type {{0}} more characters to search", "@sage/xtrem-ui/select-component-type-one-more-character": "Type 1 more character to search", "@sage/xtrem-ui/select-component-type-to-search": "Type to search...", "@sage/xtrem-ui/select-property": "Select property...", "@sage/xtrem-ui/select-record": "Select record", "@sage/xtrem-ui/selection-card-filter-placeholder": "Filter...", "@sage/xtrem-ui/series-options": "Series options", "@sage/xtrem-ui/show-floating-filters": "Show Table Filters", "@sage/xtrem-ui/show-less": "Show less", "@sage/xtrem-ui/show-more": "Show more", "@sage/xtrem-ui/show-technical-details": "Show technical details", "@sage/xtrem-ui/sidebar-apply-changes": "Apply", "@sage/xtrem-ui/sidebar-apply-changes-and-create-new": "Apply and add new", "@sage/xtrem-ui/sidebar-mobile-apply-changes-and-create-new": "Apply & new", "@sage/xtrem-ui/step-sequence-item-aria-complete": "Complete", "@sage/xtrem-ui/step-sequence-item-aria-count": "Step {{0}} of {{1}}", "@sage/xtrem-ui/step-sequence-item-aria-current": "Current", "@sage/xtrem-ui/sticker-actions-onload-unhandled-error": "An unhandled error ocurred", "@sage/xtrem-ui/string-contains": "{{fieldTitle}} contains \"{{filterValue}}\"", "@sage/xtrem-ui/string-ends-with": "{{fieldTitle}} ends with \"{{filterValue}}\"", "@sage/xtrem-ui/string-starts-with": "{{fieldTitle}} starts with \"{{filterValue}}\"", "@sage/xtrem-ui/sum": "Sum", "@sage/xtrem-ui/switch-off-caps": "OFF", "@sage/xtrem-ui/switch-on-caps": "ON", "@sage/xtrem-ui/table-addCurrentSelectionToFilter": "Add current selection to filter", "@sage/xtrem-ui/table-addToLabels": "Add **{{value}}** to labels", "@sage/xtrem-ui/table-addToValues": "Add **{{value}}** to values", "@sage/xtrem-ui/table-advancedFilterAnd": "AND", "@sage/xtrem-ui/table-advancedFilterApply": "Apply", "@sage/xtrem-ui/table-advancedFilterBlank": "is blank", "@sage/xtrem-ui/table-advancedFilterBuilder": "Builder", "@sage/xtrem-ui/table-advancedFilterBuilderAddButtonTooltip": "Add Filter or Group", "@sage/xtrem-ui/table-advancedFilterBuilderAddCondition": "Add Filter", "@sage/xtrem-ui/table-advancedFilterBuilderAddJoin": "Add Group", "@sage/xtrem-ui/table-advancedFilterBuilderApply": "Apply", "@sage/xtrem-ui/table-advancedFilterBuilderCancel": "Cancel", "@sage/xtrem-ui/table-advancedFilterBuilderEnterValue": "Enter a value...", "@sage/xtrem-ui/table-advancedFilterBuilderMoveDownButtonTooltip": "Move Down", "@sage/xtrem-ui/table-advancedFilterBuilderMoveUpButtonTooltip": "Move Up", "@sage/xtrem-ui/table-advancedFilterBuilderRemoveButtonTooltip": "Remove", "@sage/xtrem-ui/table-advancedFilterBuilderSelectColumn": "Select a column", "@sage/xtrem-ui/table-advancedFilterBuilderSelectOption": "Select an option", "@sage/xtrem-ui/table-advancedFilterBuilderTitle": "Advanced Filter", "@sage/xtrem-ui/table-advancedFilterBuilderValidationAlreadyApplied": "Current filter already applied.", "@sage/xtrem-ui/table-advancedFilterBuilderValidationEnterValue": "Must enter a value.", "@sage/xtrem-ui/table-advancedFilterBuilderValidationIncomplete": "Not all conditions are complete.", "@sage/xtrem-ui/table-advancedFilterBuilderValidationSelectColumn": "Must select a column.", "@sage/xtrem-ui/table-advancedFilterBuilderValidationSelectOption": "Must select an option.", "@sage/xtrem-ui/table-advancedFilterContains": "contains", "@sage/xtrem-ui/table-advancedFilterEndsWith": "ends with", "@sage/xtrem-ui/table-advancedFilterEquals": "=", "@sage/xtrem-ui/table-advancedFilterFalse": "is false", "@sage/xtrem-ui/table-advancedFilterGreaterThan": ">", "@sage/xtrem-ui/table-advancedFilterGreaterThanOrEqual": ">=", "@sage/xtrem-ui/table-advancedFilterLessThan": "<", "@sage/xtrem-ui/table-advancedFilterLessThanOrEqual": "<=", "@sage/xtrem-ui/table-advancedFilterNotBlank": "is not blank", "@sage/xtrem-ui/table-advancedFilterNotContains": "does not contain", "@sage/xtrem-ui/table-advancedFilterNotEqual": "!=", "@sage/xtrem-ui/table-advancedFilterOr": "OR", "@sage/xtrem-ui/table-advancedFilterStartsWith": "begins with", "@sage/xtrem-ui/table-advancedFilterTextEquals": "equals", "@sage/xtrem-ui/table-advancedFilterTextNotEqual": "does not equal", "@sage/xtrem-ui/table-advancedFilterTrue": "is true", "@sage/xtrem-ui/table-advancedFilterValidationExtraEndBracket": "Too many end brackets", "@sage/xtrem-ui/table-advancedFilterValidationInvalidColumn": "Column not found", "@sage/xtrem-ui/table-advancedFilterValidationInvalidDate": "Value is not a valid date", "@sage/xtrem-ui/table-advancedFilterValidationInvalidJoinOperator": "Join operator not found", "@sage/xtrem-ui/table-advancedFilterValidationInvalidOption": "Option not found", "@sage/xtrem-ui/table-advancedFilterValidationJoinOperatorMismatch": "Join operators within a condition must be the same", "@sage/xtrem-ui/table-advancedFilterValidationMessage": "Expression has an error. **{{error}}** - **{{validation}}**.", "@sage/xtrem-ui/table-advancedFilterValidationMessageAtEnd": "Expression has an error. **{{validation}}** at end of expression.", "@sage/xtrem-ui/table-advancedFilterValidationMissingColumn": "Column is missing", "@sage/xtrem-ui/table-advancedFilterValidationMissingCondition": "Condition is missing", "@sage/xtrem-ui/table-advancedFilterValidationMissingEndBracket": "Missing end bracket", "@sage/xtrem-ui/table-advancedFilterValidationMissingOption": "Option is missing", "@sage/xtrem-ui/table-advancedFilterValidationMissingQuote": "Value is missing an end quote", "@sage/xtrem-ui/table-advancedFilterValidationMissingValue": "Value is missing", "@sage/xtrem-ui/table-advancedFilterValidationNotANumber": "Value is not a number", "@sage/xtrem-ui/table-advancedSettings": "Advanced Settings", "@sage/xtrem-ui/table-after": "After", "@sage/xtrem-ui/table-aggregate": "Aggregate", "@sage/xtrem-ui/table-andCondition": "AND", "@sage/xtrem-ui/table-animation": "Animation", "@sage/xtrem-ui/table-applyFilter": "Apply", "@sage/xtrem-ui/table-april": "April", "@sage/xtrem-ui/table-area": "Area", "@sage/xtrem-ui/table-areaChart": "Area", "@sage/xtrem-ui/table-AreaColumnCombo": "Area & Column", "@sage/xtrem-ui/table-areaColumnComboTooltip": "Area & Column", "@sage/xtrem-ui/table-areaGroup": "Area", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderColumn": "Column", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderFilterItem": "Filter Condition", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderGroupItem": "Filter Group", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderItem": "**{{aria}}**. Level **{{level}}**. Press ENTER to edit", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderItemValidation": "**{{aria}}**. Level **{{level}}**. **{{subLevel}}** Press ENTER to edit", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderJoinOperator": "Join Operator", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderList": "Advanced Filter Builder List", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderOption": "Option", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderValueP": "Value", "@sage/xtrem-ui/table-ariaAdvancedFilterInput": "Advanced Filter Input", "@sage/xtrem-ui/table-ariaChartMenuClose": "Close Chart Edit Menu", "@sage/xtrem-ui/table-ariaChartSelected": "Selected", "@sage/xtrem-ui/table-ariaChecked": "checked", "@sage/xtrem-ui/table-ariaColumn": "Column", "@sage/xtrem-ui/table-ariaColumnFiltered": "Column Filtered", "@sage/xtrem-ui/table-ariaColumnGroup": "Column Group", "@sage/xtrem-ui/table-ariaColumnPanelList": "Column List", "@sage/xtrem-ui/table-ariaColumnSelectAll": "Toggle All Columns Visibility", "@sage/xtrem-ui/table-ariaDateFilterInput": "Date Filter Input", "@sage/xtrem-ui/table-ariaDefaultListName": "List", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentAggFuncSeparator": " of ", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentDescription": "Press DELETE to remove", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentSortAscending": "ascending", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentSortDescending": "descending", "@sage/xtrem-ui/table-ariaDropZoneColumnGroupItemDescription": "Press ENTER to sort", "@sage/xtrem-ui/table-ariaDropZoneColumnValueItemDescription": "Press ENTER to change the aggregation type", "@sage/xtrem-ui/table-ariaFilterColumn": "Press CTRL ENTER to open filter", "@sage/xtrem-ui/table-ariaFilterColumnsInput": "Filter Columns Input", "@sage/xtrem-ui/table-ariaFilterFromValue": "Filter from value", "@sage/xtrem-ui/table-ariaFilteringOperator": "Filtering Operator", "@sage/xtrem-ui/table-ariaFilterInput": "Filter Input", "@sage/xtrem-ui/table-ariaFilterList": "Filter List", "@sage/xtrem-ui/table-ariaFilterMenuOpen": "Open Filter Menu", "@sage/xtrem-ui/table-ariaFilterPanelList": "Filter List", "@sage/xtrem-ui/table-ariaFilterToValue": "Filter to value", "@sage/xtrem-ui/table-ariaFilterValue": "Filter Value", "@sage/xtrem-ui/table-ariaHeaderSelection": "Column with Header Selection", "@sage/xtrem-ui/table-ariaHidden": "hidden", "@sage/xtrem-ui/table-ariaIndeterminate": "indeterminate", "@sage/xtrem-ui/table-ariaInputEditor": "Input Editor", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterAutocomplete": "Advanced Filter Autocomplete", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderAddField": "Advanced Filter Builder Add Field", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderColumnSelectField": "Advanced Filter Builder Column Select Field", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderJoinSelectField": "Advanced Filter Builder Join Operator Select Field", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderOptionSelectField": "Advanced Filter Builder Option Select Field", "@sage/xtrem-ui/table-ariaLabelAggregationFunction": "Aggregation Function", "@sage/xtrem-ui/table-ariaLabelCellEditor": "Cell Editor", "@sage/xtrem-ui/table-ariaLabelColumnFilter": "<PERSON>umn <PERSON>", "@sage/xtrem-ui/table-ariaLabelColumnMenu": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-ariaLabelContextMenu": "Context Menu", "@sage/xtrem-ui/table-ariaLabelDialog": "Dialog", "@sage/xtrem-ui/table-ariaLabelRichSelectDeleteSelection": "Press DELETE to deselect item", "@sage/xtrem-ui/table-ariaLabelRichSelectDeselectAllItems": "Press DELETE to deselect all items", "@sage/xtrem-ui/table-ariaLabelRichSelectField": "Rich Select Field", "@sage/xtrem-ui/table-ariaLabelRichSelectToggleSelection": "Press SPACE to toggle selection", "@sage/xtrem-ui/table-ariaLabelSelectField": "Select Field", "@sage/xtrem-ui/table-ariaLabelSubMenu": "SubMenu", "@sage/xtrem-ui/table-ariaLabelTooltip": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-ariaMenuColumn": "Press ALT DOWN to open column menu", "@sage/xtrem-ui/table-ariaPageSizeSelectorLabel": "<PERSON>", "@sage/xtrem-ui/table-ariaPivotDropZonePanelLabel": "Column Labels", "@sage/xtrem-ui/table-ariaRowDeselect": "Press SPACE to deselect this row", "@sage/xtrem-ui/table-ariaRowGroupDropZonePanelLabel": "Row Groups", "@sage/xtrem-ui/table-ariaRowSelect": "Press SPACE to select this row", "@sage/xtrem-ui/table-ariaRowSelectAll": "Press Space to toggle all rows selection", "@sage/xtrem-ui/table-ariaRowSelectionDisabled": "Row Selection is disabled for this row", "@sage/xtrem-ui/table-ariaRowToggleSelection": "Press Space to toggle row selection", "@sage/xtrem-ui/table-ariaSearch": "Search", "@sage/xtrem-ui/table-ariaSearchFilterValues": "Search filter values", "@sage/xtrem-ui/table-ariaSkeletonCellLoading": "Row data is loading", "@sage/xtrem-ui/table-ariaSkeletonCellLoadingFailed": "Row failed to load", "@sage/xtrem-ui/table-ariaSortableColumn": "Press ENTER to sort", "@sage/xtrem-ui/table-ariaToggleCellValue": "Press SPACE to toggle cell value", "@sage/xtrem-ui/table-ariaToggleVisibility": "Press SPACE to toggle visibility", "@sage/xtrem-ui/table-ariaUnchecked": "unchecked", "@sage/xtrem-ui/table-ariaValuesDropZonePanelLabel": "Values", "@sage/xtrem-ui/table-ariaVisible": "visible", "@sage/xtrem-ui/table-august": "August", "@sage/xtrem-ui/table-automatic": "Automatic", "@sage/xtrem-ui/table-autoRotate": "Auto Rotate", "@sage/xtrem-ui/table-autosizeAllColumns": "Autosize All Columns", "@sage/xtrem-ui/table-autosizeThiscolumn": "Autosize This Column", "@sage/xtrem-ui/table-avg": "Average", "@sage/xtrem-ui/table-axis": "Axis", "@sage/xtrem-ui/table-axisType": "Axis Type", "@sage/xtrem-ui/table-background": "Background", "@sage/xtrem-ui/table-bar": "Bar", "@sage/xtrem-ui/table-barChart": "Bar", "@sage/xtrem-ui/table-barGroup": "Bar", "@sage/xtrem-ui/table-before": "Before", "@sage/xtrem-ui/table-blank": "Blank", "@sage/xtrem-ui/table-blanks": "Blanks", "@sage/xtrem-ui/table-blur": "Blur", "@sage/xtrem-ui/table-bold": "Bold", "@sage/xtrem-ui/table-boldItalic": "Bold Italic", "@sage/xtrem-ui/table-bottom": "Bottom", "@sage/xtrem-ui/table-boxPlot": "Box Plot", "@sage/xtrem-ui/table-boxPlotTooltip": "Box Plot", "@sage/xtrem-ui/table-bubble": "Bubble", "@sage/xtrem-ui/table-bubbleTooltip": "Bubble", "@sage/xtrem-ui/table-calendar-view": "Switch to calendar view", "@sage/xtrem-ui/table-callout": "Callout", "@sage/xtrem-ui/table-calloutLabels": "Callout Labels", "@sage/xtrem-ui/table-cancelFgroupFilterSelectilter": "Select field:", "@sage/xtrem-ui/table-cancelFilter": "Cancel", "@sage/xtrem-ui/table-cap": "Cap", "@sage/xtrem-ui/table-capLengthRatio": "Length Ratio", "@sage/xtrem-ui/table-categories": "Categories", "@sage/xtrem-ui/table-category": "Category", "@sage/xtrem-ui/table-categoryAdd": "Add a category", "@sage/xtrem-ui/table-categoryValues": "Category Values", "@sage/xtrem-ui/table-chart": "Chart", "@sage/xtrem-ui/table-chartAdvancedSettings": "Advanced Settings", "@sage/xtrem-ui/table-chartDownload": "Download Chart", "@sage/xtrem-ui/table-chartDownloadToolbarTooltip": "Download Chart", "@sage/xtrem-ui/table-chartEdit": "Edit Chart", "@sage/xtrem-ui/table-chartLink": "Link to Grid", "@sage/xtrem-ui/table-chartLinkToolbarTooltip": "Linked to Grid", "@sage/xtrem-ui/table-chartMenuToolbarTooltip": "<PERSON><PERSON>", "@sage/xtrem-ui/table-chartRange": "Chart Range", "@sage/xtrem-ui/table-chartSettingsToolbarTooltip": "<PERSON><PERSON>", "@sage/xtrem-ui/table-chartStyle": "Chart Style", "@sage/xtrem-ui/table-chartSubtitle": "Subtitle", "@sage/xtrem-ui/table-chartTitle": "Chart Title", "@sage/xtrem-ui/table-chartTitles": "Titles", "@sage/xtrem-ui/table-chartUnlink": "Unlink from Grid", "@sage/xtrem-ui/table-chartUnlinkToolbarTooltip": "Unlinked from Grid", "@sage/xtrem-ui/table-chooseColumns": "Choose Columns", "@sage/xtrem-ui/table-circle": "Circle", "@sage/xtrem-ui/table-clearFilter": "Clear", "@sage/xtrem-ui/table-collapseAll": "Close All", "@sage/xtrem-ui/table-color": "Color", "@sage/xtrem-ui/table-column": "Column", "@sage/xtrem-ui/table-column-settings": "Column settings", "@sage/xtrem-ui/table-columnChart": "Column", "@sage/xtrem-ui/table-columnChooser": "Choose Columns", "@sage/xtrem-ui/table-columnFilter": "<PERSON>umn <PERSON>", "@sage/xtrem-ui/table-columnGroup": "Column", "@sage/xtrem-ui/table-columnLineCombo": "Column & Line", "@sage/xtrem-ui/table-columnLineComboTooltip": "Column & Line", "@sage/xtrem-ui/table-columns": "Columns", "@sage/xtrem-ui/table-combinationChart": "Combination", "@sage/xtrem-ui/table-combinationGroup": "Combination", "@sage/xtrem-ui/table-compare-with-previous-period": "Compare with previous period", "@sage/xtrem-ui/table-connectorLine": "Connector Line", "@sage/xtrem-ui/table-contains": "Contains", "@sage/xtrem-ui/table-copy": "Copy", "@sage/xtrem-ui/table-copyWithGroupHeaders": "Copy with Group Headers", "@sage/xtrem-ui/table-copyWithHeaders": "<PERSON><PERSON> With Headers", "@sage/xtrem-ui/table-count": "Count", "@sage/xtrem-ui/table-create": "Create", "@sage/xtrem-ui/table-cross": "Cross", "@sage/xtrem-ui/table-crosshair": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-crosshairLabel": "Label", "@sage/xtrem-ui/table-crosshairSnap": "Snap to Node", "@sage/xtrem-ui/table-csvExport": "CSV Export", "@sage/xtrem-ui/table-ctrlC": "Ctrl+C", "@sage/xtrem-ui/table-ctrlV": "Ctrl+V", "@sage/xtrem-ui/table-ctrlX": "Ctrl+X", "@sage/xtrem-ui/table-customCombo": "Custom Combination", "@sage/xtrem-ui/table-customComboTooltip": "Custom Combination", "@sage/xtrem-ui/table-cut": "Cut", "@sage/xtrem-ui/table-data": "Data", "@sage/xtrem-ui/table-dateFilter": "Date Filter", "@sage/xtrem-ui/table-dateFormatOoo": "YYYY-MM-DD", "@sage/xtrem-ui/table-december": "December", "@sage/xtrem-ui/table-decimalSeparator": ".", "@sage/xtrem-ui/table-defaultCategory": "(None)", "@sage/xtrem-ui/table-diamond": "Diamond", "@sage/xtrem-ui/table-direction": "Direction", "@sage/xtrem-ui/table-donut": "Donut", "@sage/xtrem-ui/table-donutTooltip": "Donut", "@sage/xtrem-ui/table-doughnut": "Doughnut", "@sage/xtrem-ui/table-doughnutTooltip": "Doughnut", "@sage/xtrem-ui/table-durationMillis": "Duration (ms)", "@sage/xtrem-ui/table-empty": "Choose one", "@sage/xtrem-ui/table-enabled": "Enabled", "@sage/xtrem-ui/table-endAngle": "<PERSON>", "@sage/xtrem-ui/table-endsWith": "Ends with", "@sage/xtrem-ui/table-equals": "Equals", "@sage/xtrem-ui/table-excelExport": "Excel Export (.xlsx)", "@sage/xtrem-ui/table-excelXmlExport": "Excel Export (.xml)", "@sage/xtrem-ui/table-expandAll": "Expand All", "@sage/xtrem-ui/table-export": "Export", "@sage/xtrem-ui/table-export-failed": "Failed to export main list content", "@sage/xtrem-ui/table-export-service-no-columns": "No columns to export.", "@sage/xtrem-ui/table-export-started": "Export started.", "@sage/xtrem-ui/table-false": "False", "@sage/xtrem-ui/table-february": "February", "@sage/xtrem-ui/table-fillOpacity": "Fill Opacity", "@sage/xtrem-ui/table-filter-aria-label": "Filter", "@sage/xtrem-ui/table-filteredRows": "Filtered", "@sage/xtrem-ui/table-filterOoo": "Filter...", "@sage/xtrem-ui/table-filters": "Filters", "@sage/xtrem-ui/table-first": "First", "@sage/xtrem-ui/table-firstPage": "First Page", "@sage/xtrem-ui/table-fixed": "Fixed", "@sage/xtrem-ui/table-font": "Font", "@sage/xtrem-ui/table-footerTotal": "Total", "@sage/xtrem-ui/table-format": "Format", "@sage/xtrem-ui/table-greaterThan": "Greater than", "@sage/xtrem-ui/table-greaterThanOrEqual": "Greater than or equal", "@sage/xtrem-ui/table-gridLines": "Grid Lines", "@sage/xtrem-ui/table-group": "Group", "@sage/xtrem-ui/table-group-total": "Group total", "@sage/xtrem-ui/table-groupBy": "Group by", "@sage/xtrem-ui/table-groupedAreaTooltip": "Area", "@sage/xtrem-ui/table-groupedBar": "Grouped", "@sage/xtrem-ui/table-groupedBarFull": "Grouped Bar", "@sage/xtrem-ui/table-groupedBarTooltip": "Grouped", "@sage/xtrem-ui/table-groupedColumn": "Grouped", "@sage/xtrem-ui/table-groupedColumnFull": "Grouped Column", "@sage/xtrem-ui/table-groupedColumnTooltip": "Grouped", "@sage/xtrem-ui/table-groupedSeriesGroupType": "Grouped", "@sage/xtrem-ui/table-groupPadding": "Group Padding", "@sage/xtrem-ui/table-groups": "Row Groups", "@sage/xtrem-ui/table-heart": "Heart", "@sage/xtrem-ui/table-heatmap": "Heatmap", "@sage/xtrem-ui/table-heatmapTooltip": "Heatmap", "@sage/xtrem-ui/table-height": "Height", "@sage/xtrem-ui/table-hierarchicalChart": "Hierarchical", "@sage/xtrem-ui/table-hierarchicalGroup": "Hierarchical", "@sage/xtrem-ui/table-histogram": "Histogram", "@sage/xtrem-ui/table-histogramBinCount": "Bin count", "@sage/xtrem-ui/table-histogramChart": "Histogram", "@sage/xtrem-ui/table-histogramFrequency": "Frequency", "@sage/xtrem-ui/table-histogramGroup": "Histogram", "@sage/xtrem-ui/table-histogramTooltip": "Histogram", "@sage/xtrem-ui/table-horizontal": "Horizontal", "@sage/xtrem-ui/table-horizontalAxisTitle": "Horizontal Axis Title", "@sage/xtrem-ui/table-innerRadius": "Inner Radius", "@sage/xtrem-ui/table-inRange": "In range", "@sage/xtrem-ui/table-inRangeEnd": "To", "@sage/xtrem-ui/table-inRangeStart": "From", "@sage/xtrem-ui/table-inside": "Inside", "@sage/xtrem-ui/table-invalidColor": "Color value is invalid", "@sage/xtrem-ui/table-invalidDate": "Invalid Date", "@sage/xtrem-ui/table-invalidNumber": "Invalid Number", "@sage/xtrem-ui/table-italic": "Italic", "@sage/xtrem-ui/table-itemPaddingX": "<PERSON><PERSON>", "@sage/xtrem-ui/table-itemPaddingY": "<PERSON><PERSON>", "@sage/xtrem-ui/table-itemSpacing": "Item Spacing", "@sage/xtrem-ui/table-january": "January", "@sage/xtrem-ui/table-july": "July", "@sage/xtrem-ui/table-june": "June", "@sage/xtrem-ui/table-labelPlacement": "Placement", "@sage/xtrem-ui/table-labelRotation": "Rotation", "@sage/xtrem-ui/table-labels": "Labels", "@sage/xtrem-ui/table-last": "Last", "@sage/xtrem-ui/table-lastPage": "Last Page", "@sage/xtrem-ui/table-layoutHorizontalSpacing": "Horizontal Spacing", "@sage/xtrem-ui/table-layoutVerticalSpacing": "Vertical Spacing", "@sage/xtrem-ui/table-left": "Left", "@sage/xtrem-ui/table-legend": "Legend", "@sage/xtrem-ui/table-legendEnabled": "Enabled", "@sage/xtrem-ui/table-length": "Length", "@sage/xtrem-ui/table-lessThan": "Less than", "@sage/xtrem-ui/table-lessThanOrEqual": "Less than or equal", "@sage/xtrem-ui/table-line": "Line", "@sage/xtrem-ui/table-line-number": "Line number", "@sage/xtrem-ui/table-lineChart": "Line", "@sage/xtrem-ui/table-lineDash": "Line Dash", "@sage/xtrem-ui/table-lineDashOffset": "Dash Offset", "@sage/xtrem-ui/table-lineGroup": "Line", "@sage/xtrem-ui/table-lineTooltip": "Line", "@sage/xtrem-ui/table-lineWidth": "Line Width", "@sage/xtrem-ui/table-loadingError": "ERR", "@sage/xtrem-ui/table-loadingOoo": "Loading...", "@sage/xtrem-ui/table-march": "March", "@sage/xtrem-ui/table-markerPadding": "<PERSON><PERSON>", "@sage/xtrem-ui/table-markers": "Markers", "@sage/xtrem-ui/table-markerSize": "<PERSON><PERSON>", "@sage/xtrem-ui/table-markerStroke": "<PERSON><PERSON>", "@sage/xtrem-ui/table-max": "Max", "@sage/xtrem-ui/table-maxSize": "Maximum Size", "@sage/xtrem-ui/table-may": "May", "@sage/xtrem-ui/table-min": "Min", "@sage/xtrem-ui/table-miniChart": "Mini-Chart", "@sage/xtrem-ui/table-minSize": "Minimum Size", "@sage/xtrem-ui/table-more": "More", "@sage/xtrem-ui/table-navigator": "Navigator", "@sage/xtrem-ui/table-next": "Next", "@sage/xtrem-ui/table-nextPage": "Next Page", "@sage/xtrem-ui/table-nightingale": "<PERSON>", "@sage/xtrem-ui/table-nightingaleTooltip": "<PERSON>", "@sage/xtrem-ui/table-noAggregation": "None", "@sage/xtrem-ui/table-noDataToChart": "No data available to be charted.", "@sage/xtrem-ui/table-noMatches": "No matches.", "@sage/xtrem-ui/table-none": "None", "@sage/xtrem-ui/table-noPin": "No Pin", "@sage/xtrem-ui/table-normal": "Normal", "@sage/xtrem-ui/table-normalizedArea": "100% Stacked", "@sage/xtrem-ui/table-normalizedAreaFull": "100% Stacked Area", "@sage/xtrem-ui/table-normalizedAreaTooltip": "100% Stacked", "@sage/xtrem-ui/table-normalizedBar": "100% Stacked", "@sage/xtrem-ui/table-normalizedBarFull": "100% Stacked Bar", "@sage/xtrem-ui/table-normalizedBarTooltip": "100% Stacked", "@sage/xtrem-ui/table-normalizedColumn": "100% Stacked", "@sage/xtrem-ui/table-normalizedColumnFull": "100% Stacked Column", "@sage/xtrem-ui/table-normalizedColumnTooltip": "100% Stacked", "@sage/xtrem-ui/table-normalizedLine": "100% Stacked", "@sage/xtrem-ui/table-normalizedLineTooltip": "100% Stacked", "@sage/xtrem-ui/table-normalizedSeriesGroupType": "100% Stacked", "@sage/xtrem-ui/table-noRowsToShow": "No Rows To Show", "@sage/xtrem-ui/table-notBlank": "Not blank", "@sage/xtrem-ui/table-notContains": "Not contains", "@sage/xtrem-ui/table-notEqual": "Not equal", "@sage/xtrem-ui/table-november": "November", "@sage/xtrem-ui/table-number": "Number", "@sage/xtrem-ui/table-numberFilter": "Number Filter", "@sage/xtrem-ui/table-numeric-filter-greater-than-equals-value": ">= {{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-greater-than-value": "> {{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-less-than-equals-value": "<= {{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-less-than-value": "< {{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-not-value": "Not {{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-range-value": "{{numericValue}} - {{numericValueTo}}", "@sage/xtrem-ui/table-october": "October", "@sage/xtrem-ui/table-of": "Of", "@sage/xtrem-ui/table-offset": "Offset", "@sage/xtrem-ui/table-offsets": "Offsets", "@sage/xtrem-ui/table-open-column-panel": "Open column panel", "@sage/xtrem-ui/table-orCondition": "OR", "@sage/xtrem-ui/table-orientation": "Orientation", "@sage/xtrem-ui/table-outside": "Outside", "@sage/xtrem-ui/table-padding": "Padding", "@sage/xtrem-ui/table-page": "Page", "@sage/xtrem-ui/table-pageLastRowUnknown": "?", "@sage/xtrem-ui/table-pageSizeSelectorLabel": "Page Size:", "@sage/xtrem-ui/table-paired": "Paired Mode", "@sage/xtrem-ui/table-parallel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-paste": "Paste", "@sage/xtrem-ui/table-perpendicular": "Perpendicular", "@sage/xtrem-ui/table-pie": "Pie", "@sage/xtrem-ui/table-pieChart": "Pie", "@sage/xtrem-ui/table-pieGroup": "Pie", "@sage/xtrem-ui/table-pieTooltip": "Pie", "@sage/xtrem-ui/table-pinColumn": "<PERSON>n <PERSON>", "@sage/xtrem-ui/table-pinLeft": "<PERSON><PERSON>", "@sage/xtrem-ui/table-pinRight": "Pin <PERSON>", "@sage/xtrem-ui/table-pivotChart": "Pivot Chart", "@sage/xtrem-ui/table-pivotChartAndPivotMode": "Pivot Chart & Pivot Mode", "@sage/xtrem-ui/table-pivotChartRequiresPivotMode": "Pivot Chart requires Pivot Mode enabled.", "@sage/xtrem-ui/table-pivotChartTitle": "Pivot Chart", "@sage/xtrem-ui/table-pivotColumnGroupTotals": "Total", "@sage/xtrem-ui/table-pivotColumnsEmptyMessage": "Drag here to set column labels", "@sage/xtrem-ui/table-pivotMode": "Pivot Mode", "@sage/xtrem-ui/table-pivots": "Column Labels", "@sage/xtrem-ui/table-plus": "Plus", "@sage/xtrem-ui/table-polarAxis": "Polar Axis", "@sage/xtrem-ui/table-polarAxisTitle": "Polar Axis Title", "@sage/xtrem-ui/table-polarChart": "Polar", "@sage/xtrem-ui/table-polarGroup": "Polar", "@sage/xtrem-ui/table-polygon": "Polygon", "@sage/xtrem-ui/table-position": "Position", "@sage/xtrem-ui/table-positionRatio": "Position Ratio", "@sage/xtrem-ui/table-predefined": "Predefined", "@sage/xtrem-ui/table-preferredLength": "Preferred Length", "@sage/xtrem-ui/table-previous": "Previous", "@sage/xtrem-ui/table-previousPage": "Previous Page", "@sage/xtrem-ui/table-print": "Print", "@sage/xtrem-ui/table-radarArea": "Radar Area", "@sage/xtrem-ui/table-radarAreaTooltip": "Radar Area", "@sage/xtrem-ui/table-radarLine": "Radar Line", "@sage/xtrem-ui/table-radarLineTooltip": "Radar Line", "@sage/xtrem-ui/table-radialBar": "Radial Bar", "@sage/xtrem-ui/table-radialBarTooltip": "Radial Bar", "@sage/xtrem-ui/table-radialColumn": "Radial Column", "@sage/xtrem-ui/table-radialColumnTooltip": "Radial Column", "@sage/xtrem-ui/table-radiusAxis": "<PERSON><PERSON>", "@sage/xtrem-ui/table-radiusAxisPosition": "Position", "@sage/xtrem-ui/table-rangeArea": "Range Area", "@sage/xtrem-ui/table-rangeAreaTooltip": "Range Area", "@sage/xtrem-ui/table-rangeBar": "Range Bar", "@sage/xtrem-ui/table-rangeBarTooltip": "Range Bar", "@sage/xtrem-ui/table-rangeChartTitle": "Range Chart", "@sage/xtrem-ui/table-removeFromLabels": "Remove **{{value}}** from labels", "@sage/xtrem-ui/table-removeFromValues": "Remove **{{value}}** from values", "@sage/xtrem-ui/table-resetColumns": "Reset Columns", "@sage/xtrem-ui/table-resetFilter": "Reset", "@sage/xtrem-ui/table-reverseDirection": "Reverse Direction", "@sage/xtrem-ui/table-right": "Right", "@sage/xtrem-ui/table-rowDragRow": "row", "@sage/xtrem-ui/table-rowDragRows": "rows", "@sage/xtrem-ui/table-rowGroupColumnsEmptyMessage": "Drag here to set row groups", "@sage/xtrem-ui/table-scatter": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-scatterGroup": "X Y (<PERSON><PERSON><PERSON>)", "@sage/xtrem-ui/table-scatterTooltip": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-scrollingStep": "Scrolling Step", "@sage/xtrem-ui/table-scrollingZoom": "Scrolling", "@sage/xtrem-ui/table-searchOoo": "Search...", "@sage/xtrem-ui/table-secondaryAxis": "Secondary Axis", "@sage/xtrem-ui/table-sectorLabels": "Sector Labels", "@sage/xtrem-ui/table-select-all": "Select all", "@sage/xtrem-ui/table-selectAll": "Select All", "@sage/xtrem-ui/table-selectAllSearchResults": "Select All Search Results", "@sage/xtrem-ui/table-selectedRows": "Selected", "@sage/xtrem-ui/table-selectingZoom": "Selecting", "@sage/xtrem-ui/table-september": "September", "@sage/xtrem-ui/table-series": "Series", "@sage/xtrem-ui/table-seriesAdd": "Add a series", "@sage/xtrem-ui/table-seriesChartType": "Series Chart Type", "@sage/xtrem-ui/table-seriesGroupType": "Group Type", "@sage/xtrem-ui/table-seriesItemLabels": "Item Labels", "@sage/xtrem-ui/table-seriesItemNegative": "Negative", "@sage/xtrem-ui/table-seriesItemPositive": "Positive", "@sage/xtrem-ui/table-seriesItems": "Series Items", "@sage/xtrem-ui/table-seriesItemType": "Item Type", "@sage/xtrem-ui/table-seriesLabels": "Series Labels", "@sage/xtrem-ui/table-seriesPadding": "Series Padding", "@sage/xtrem-ui/table-seriesType": "Series Type", "@sage/xtrem-ui/table-setFilter": "Set Filter", "@sage/xtrem-ui/table-settings": "Settings", "@sage/xtrem-ui/table-shadow": "Shadow", "@sage/xtrem-ui/table-shape": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-show": "Show", "@sage/xtrem-ui/table-size": "Size", "@sage/xtrem-ui/table-sortAscending": "Sort Ascending", "@sage/xtrem-ui/table-sortDescending": "Sort Descending", "@sage/xtrem-ui/table-sortUnSort": "Clear Sort", "@sage/xtrem-ui/table-spacing": "Spacing", "@sage/xtrem-ui/table-specializedChart": "Specialized", "@sage/xtrem-ui/table-specializedGroup": "Specialized", "@sage/xtrem-ui/table-square": "Square", "@sage/xtrem-ui/table-stackedArea": "Stacked", "@sage/xtrem-ui/table-stackedAreaFull": "Stacked Area", "@sage/xtrem-ui/table-stackedAreaTooltip": "Stacked", "@sage/xtrem-ui/table-stackedBar": "Stacked", "@sage/xtrem-ui/table-stackedBarFull": "Stacked Bar", "@sage/xtrem-ui/table-stackedBarTooltip": "Stacked", "@sage/xtrem-ui/table-stackedColumn": "Stacked", "@sage/xtrem-ui/table-stackedColumnFull": "Stacked Column", "@sage/xtrem-ui/table-stackedColumnTooltip": "Stacked", "@sage/xtrem-ui/table-stackedLine": "Stacked", "@sage/xtrem-ui/table-stackedLineTooltip": "Stacked", "@sage/xtrem-ui/table-stackedSeriesGroupType": "Stacked", "@sage/xtrem-ui/table-startAngle": "Start Angle", "@sage/xtrem-ui/table-startsWith": "Starts with", "@sage/xtrem-ui/table-statisticalChart": "Statistical", "@sage/xtrem-ui/table-statisticalGroup": "Statistical", "@sage/xtrem-ui/table-strokeColor": "Line Color", "@sage/xtrem-ui/table-strokeOpacity": "Line Opacity", "@sage/xtrem-ui/table-strokeWidth": "Stroke Width", "@sage/xtrem-ui/table-sum": "Sum", "@sage/xtrem-ui/table-summary-empty-default-text": "There are no data to display", "@sage/xtrem-ui/table-sunburst": "Sunburst", "@sage/xtrem-ui/table-sunburstTooltip": "Sunburst", "@sage/xtrem-ui/table-switch-to-card-view": "Switch to card view", "@sage/xtrem-ui/table-switch-to-chart-view": "Switch to chart view", "@sage/xtrem-ui/table-switch-to-contact-view": "Switch to contact view", "@sage/xtrem-ui/table-switch-to-site-view": "Switch to site view", "@sage/xtrem-ui/table-switch-to-table-view": "Switch to table view", "@sage/xtrem-ui/table-switchCategorySeries": "Switch Category / Series", "@sage/xtrem-ui/table-table-view": "Switch to table view", "@sage/xtrem-ui/table-textFilter": "Text Filter", "@sage/xtrem-ui/table-thickness": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-thousandSeparator": ",", "@sage/xtrem-ui/table-ticks": "Ticks", "@sage/xtrem-ui/table-tile": "Tile", "@sage/xtrem-ui/table-time": "Time", "@sage/xtrem-ui/table-timeFormat": "Time Format", "@sage/xtrem-ui/table-timeFormatDashesYYYYMMDD": "YYYY-MM-DD", "@sage/xtrem-ui/table-timeFormatDotsDDMYY": "DD.M.YY", "@sage/xtrem-ui/table-timeFormatDotsMDDYY": "M.<PERSON>.YY", "@sage/xtrem-ui/table-timeFormatHHMMSS": "HH:MM:SS", "@sage/xtrem-ui/table-timeFormatHHMMSSAmPm": "HH:MM:SS AM/PM", "@sage/xtrem-ui/table-timeFormatSlashesDDMMYY": "DD/MM/YY", "@sage/xtrem-ui/table-timeFormatSlashesDDMMYYYY": "DD/MM/YYYY", "@sage/xtrem-ui/table-timeFormatSlashesMMDDYY": "MM/DD/YY", "@sage/xtrem-ui/table-timeFormatSlashesMMDDYYYY": "MM/DD/YYYY", "@sage/xtrem-ui/table-timeFormatSpacesDDMMMMYYYY": "DD MMMM YYYY", "@sage/xtrem-ui/table-title": "Title", "@sage/xtrem-ui/table-titlePlaceholder": "Chart title - double click to edit", "@sage/xtrem-ui/table-to": "To", "@sage/xtrem-ui/table-tooltips": "Tooltips", "@sage/xtrem-ui/table-top": "Top", "@sage/xtrem-ui/table-totalAndFilteredRows": "Rows", "@sage/xtrem-ui/table-totalRows": "Total Rows", "@sage/xtrem-ui/table-treemap": "Treemap", "@sage/xtrem-ui/table-treemapTooltip": "Treemap", "@sage/xtrem-ui/table-triangle": "Triangle", "@sage/xtrem-ui/table-true": "True", "@sage/xtrem-ui/table-ungroupAll": "Un-Group All", "@sage/xtrem-ui/table-ungroupBy": "Un-Group by", "@sage/xtrem-ui/table-valueAggregation": "Value Aggregation", "@sage/xtrem-ui/table-valueColumnsEmptyMessage": "Drag here to aggregate", "@sage/xtrem-ui/table-values": "Values", "@sage/xtrem-ui/table-variant_card": "Card View", "@sage/xtrem-ui/table-variant-area": "Area View", "@sage/xtrem-ui/table-variant-table": "Table View", "@sage/xtrem-ui/table-vertical": "Vertical", "@sage/xtrem-ui/table-verticalAxisTitle": "Vertical Axis Title", "@sage/xtrem-ui/table-views-close-button": "Close view selector dropdown", "@sage/xtrem-ui/table-views-default": "Default view", "@sage/xtrem-ui/table-views-manage": "Manage views", "@sage/xtrem-ui/table-views-open-button": "Open view selector dropdown", "@sage/xtrem-ui/table-views-save": "Save view", "@sage/xtrem-ui/table-views-save-as": "Save view as", "@sage/xtrem-ui/table-views-save-failed": "Failed to save view.", "@sage/xtrem-ui/table-views-saved": "View saved.", "@sage/xtrem-ui/table-views-select": "Select view", "@sage/xtrem-ui/table-waterfall": "Waterfall", "@sage/xtrem-ui/table-waterfallTooltip": "Waterfall", "@sage/xtrem-ui/table-weight": "Weight", "@sage/xtrem-ui/table-whisker": "<PERSON>his<PERSON>", "@sage/xtrem-ui/table-width": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-xAxis": "Horizontal Axis", "@sage/xtrem-ui/table-xOffset": "X Offset", "@sage/xtrem-ui/table-xRotation": "X Rotation", "@sage/xtrem-ui/table-xType": "X Type", "@sage/xtrem-ui/table-xyChart": "X Y (<PERSON><PERSON><PERSON>)", "@sage/xtrem-ui/table-xyValues": "X Y Values", "@sage/xtrem-ui/table-yAxis": "Vertical Axis", "@sage/xtrem-ui/table-yOffset": "Y Offset", "@sage/xtrem-ui/table-yRotation": "Y Rotation", "@sage/xtrem-ui/table-zoom": "Zoom", "@sage/xtrem-ui/text-editor-cancel": "Cancel", "@sage/xtrem-ui/text-editor-cancel-button": "Cancel", "@sage/xtrem-ui/text-editor-character-counter": "Character count", "@sage/xtrem-ui/text-editor-character-limit": "Character limit", "@sage/xtrem-ui/text-editor-content-editor": "Content editor", "@sage/xtrem-ui/text-editor-save": "Save", "@sage/xtrem-ui/text-editor-save-button": "Save", "@sage/xtrem-ui/text-editor-toolbar": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/text-format-capital-bold": "Bold", "@sage/xtrem-ui/text-format-capital-bullet-list": "Bullet List", "@sage/xtrem-ui/text-format-capital-italic": "Italic", "@sage/xtrem-ui/text-format-capital-number-list": "Number List", "@sage/xtrem-ui/toggle-navigation-panel": "Toggle Navigation Panel", "@sage/xtrem-ui/toggle-widget-list": "Toggle widget list", "@sage/xtrem-ui/true": "True", "@sage/xtrem-ui/tunnel-already-open-description": "This page is already used below, please close that one first to be able to open this link, or open it on a new tab.", "@sage/xtrem-ui/tunnel-already-open-title": "This page is already open.", "@sage/xtrem-ui/tunnel-link-see-more": "See details", "@sage/xtrem-ui/tunnel-record-not-suitable-description": "The record created cannot be used in this context.", "@sage/xtrem-ui/tunnel-record-not-suitable-title": "New record not available", "@sage/xtrem-ui/ungroup": "Ungroup", "@sage/xtrem-ui/unsaved-changes-content": "Leave and discard your changes?", "@sage/xtrem-ui/unsaved-changes-go-back": "Go back", "@sage/xtrem-ui/unsaved-changes-title": "Unsaved changes", "@sage/xtrem-ui/unsaved-changes-yes": "Yes", "@sage/xtrem-ui/update-error": "Update error: {{0}}", "@sage/xtrem-ui/upload-in-progress": "Uploading file...", "@sage/xtrem-ui/validation-error-and-more": "and 1 more error", "@sage/xtrem-ui/validation-error-of-children": "Validation error in children records", "@sage/xtrem-ui/validation-error-total": "1 error", "@sage/xtrem-ui/validation-error-with-number-grid": "You have 1 error in the following grid: {{0}}.", "@sage/xtrem-ui/validation-error-with-number-pod": "You have 1 error in the following collection: {{0}}.", "@sage/xtrem-ui/validation-errors": "Validation Errors", "@sage/xtrem-ui/validation-errors-and-more": "and {{0}} more errors", "@sage/xtrem-ui/validation-errors-number": "Number of errors found in this line", "@sage/xtrem-ui/validation-errors-total": "{{0}} errors", "@sage/xtrem-ui/validation-errors-unknown": "Unknown validation error.", "@sage/xtrem-ui/validation-errors-with-number-grid": "You have {{0}} errors in the following grid: {{1}}.", "@sage/xtrem-ui/validation-errors-with-number-pod": "You have {{0}} errors in the following collection: {{1}}.", "@sage/xtrem-ui/visual-process-lookup-page-dialog-title": "Choose a page", "@sage/xtrem-ui/visual-process-lookup-page-path-column": "Page path", "@sage/xtrem-ui/visual-process-lookup-page-title-column": "Page title", "@sage/xtrem-ui/visual-process-transactions-not-supported": "Page transactions are not supported", "@sage/xtrem-ui/widget-action-create": "Create", "@sage/xtrem-ui/widget-action-create-help": "Add a shortcut to create a new record", "@sage/xtrem-ui/widget-action-see-all": "See all", "@sage/xtrem-ui/widget-action-see-all-help": "View records based on your data selection", "@sage/xtrem-ui/widget-actions": "Actions", "@sage/xtrem-ui/widget-editor-action-decimal-digits-mandatory": "You need to enter a number.", "@sage/xtrem-ui/widget-editor-action-goes-to": "Goes to", "@sage/xtrem-ui/widget-editor-action-label-mandatory": "You need to enter a title.", "@sage/xtrem-ui/widget-editor-action-page-path": "Path", "@sage/xtrem-ui/widget-editor-action-page-title": "Title", "@sage/xtrem-ui/widget-editor-add": "Add", "@sage/xtrem-ui/widget-editor-basic-step-missing-node": "You need to add a node", "@sage/xtrem-ui/widget-editor-basic-step-missing-title": "You need to add a title", "@sage/xtrem-ui/widget-editor-basic-step-title": "{{stepIndex}}. Select a widget to get started", "@sage/xtrem-ui/widget-editor-cancel-edit": "Cancel", "@sage/xtrem-ui/widget-editor-content-formatting": "Decimal places", "@sage/xtrem-ui/widget-editor-content-step-subtitle": "Select a field for grouping and fields to use as filters.", "@sage/xtrem-ui/widget-editor-content-step-title": "{{stepIndex}}. Add your content", "@sage/xtrem-ui/widget-editor-content-step-vertical-axes": "Vertical axes", "@sage/xtrem-ui/widget-editor-data-step-title": "{{stepIndex}}. Select the data to add to your widget", "@sage/xtrem-ui/widget-editor-entry-node": "Entry module", "@sage/xtrem-ui/widget-editor-filter-step-title": "{{stepIndex}}. Add your filters", "@sage/xtrem-ui/widget-editor-grouping-method": "Grouping method", "@sage/xtrem-ui/widget-editor-grouping-property": "Grouping property", "@sage/xtrem-ui/widget-editor-horizontal-axis": "Horizontal axis", "@sage/xtrem-ui/widget-editor-horizontal-axis-label": "Horizontal axis label", "@sage/xtrem-ui/widget-editor-icon": "Icon", "@sage/xtrem-ui/widget-editor-layout-step-no-actions-in-preview": "Actions do not work in the preview. They work when you add the widget to your dashboard.", "@sage/xtrem-ui/widget-editor-layout-step-title": "{{stepIndex}}. Create your layout", "@sage/xtrem-ui/widget-editor-max-num-of-values": "Maximum number of values", "@sage/xtrem-ui/widget-editor-sorting-step-title": "{{stepIndex}}. Define sorting", "@sage/xtrem-ui/widget-editor-step-basic-title": "Widget", "@sage/xtrem-ui/widget-editor-step-content-title": "Content", "@sage/xtrem-ui/widget-editor-step-data-title": "Data", "@sage/xtrem-ui/widget-editor-step-filters-title": "Filters", "@sage/xtrem-ui/widget-editor-step-layout-title": "Layout", "@sage/xtrem-ui/widget-editor-step-sorting-title": "Sorting", "@sage/xtrem-ui/widget-editor-subtitle": "Subtitle", "@sage/xtrem-ui/widget-editor-title-edit": "Edit widget", "@sage/xtrem-ui/widget-editor-title-new": "New widget", "@sage/xtrem-ui/widget-editor-type-bar-chart": "Bar chart", "@sage/xtrem-ui/widget-editor-type-bar-chart-description": "Display data visually with bars.", "@sage/xtrem-ui/widget-editor-type-indicator-tile": "Indicator", "@sage/xtrem-ui/widget-editor-type-indicator-tile-description": "Display major record updates.", "@sage/xtrem-ui/widget-editor-type-line-chart": "Line chart", "@sage/xtrem-ui/widget-editor-type-line-chart-description": "Display data visually with lines.", "@sage/xtrem-ui/widget-editor-type-table": "Table", "@sage/xtrem-ui/widget-editor-type-table-description": "Display data in rows and columns.", "@sage/xtrem-ui/widget-editor-update": "Update", "@sage/xtrem-ui/widget-editor-vertical-axis-label": "Vertical axis label", "@sage/xtrem-ui/widget-editor-widget-category": "Widget category", "@sage/xtrem-ui/widget-editor-widget-title": "Widget title", "@sage/xtrem-ui/widget-preview-label": "Preview", "@sage/xtrem-ui/wizard-finish": "Finish", "@sage/xtrem-ui/wizard-next": "Next", "@sage/xtrem-ui/wizard-previous": "Previous", "@sage/xtrem-ui/workflow-add-trigger-event": "Add a trigger event", "@sage/xtrem-ui/workflow-collapse": "Collapse", "@sage/xtrem-ui/workflow-component-add-action": "Add action", "@sage/xtrem-ui/workflow-component-add-condition": "Add condition", "@sage/xtrem-ui/workflow-component-add-step": "Add step", "@sage/xtrem-ui/workflow-component-edge-false-path": "else", "@sage/xtrem-ui/workflow-component-edge-true-path": "If true", "@sage/xtrem-ui/workflow-component-header-label-action": "Do", "@sage/xtrem-ui/workflow-component-header-label-condition": "Condition", "@sage/xtrem-ui/workflow-component-header-label-start": "Starts when", "@sage/xtrem-ui/workflow-component-wizard-event-selection": "Event selection", "@sage/xtrem-ui/workflow-component-wizard-step-configuration": "Configuration", "@sage/xtrem-ui/workflow-component-wizard-title-action": "Action gallery", "@sage/xtrem-ui/workflow-component-wizard-title-trigger": "<PERSON><PERSON> gallery", "@sage/xtrem-ui/workflow-component-wizard-trigger-selection": "Trigger selection", "@sage/xtrem-ui/workflow-delete-node-chain-message": "If you remove this step, any subsequent steps with no other links are also removed.", "@sage/xtrem-ui/workflow-delete-node-chain-title": "Delete step flow", "@sage/xtrem-ui/workflow-empty": "This workflow is currently empty", "@sage/xtrem-ui/workflow-expand": "Expand", "@sage/xtrem-ui/workflow-fit-view": "Fit view to screen", "@sage/xtrem-ui/workflow-redo": "Redo", "@sage/xtrem-ui/workflow-undo": "Undo", "@sage/xtrem-ui/workflow-zoom-in": "Zoom in", "@sage/xtrem-ui/workflow-zoom-out": "Zoom out"}