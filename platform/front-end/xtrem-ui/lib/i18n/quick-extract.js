const path = require('path');
const fs = require('fs');
const enUsContent = require('./en-US.json');

const filesToTranslate = fs.readdirSync(__dirname).filter(f => ['target', 'quick-extract.js', 'base.json', 'en-US.json'].indexOf(f) === -1);

filesToTranslate.forEach(f => {
    const sourceFile = require(path.resolve(__dirname, f));
    const finalContent = Object.keys(sourceFile).reduce((prevValue, key) => {
        const value = sourceFile[key] ? sourceFile[key] : enUsContent[key];
        if (value) {
            prevValue[key] = value;
        }
        return prevValue;
    }, {});

    fs.writeFileSync(path.resolve(__dirname, 'target', f), JSON.stringify(finalContent, null, 4));
});