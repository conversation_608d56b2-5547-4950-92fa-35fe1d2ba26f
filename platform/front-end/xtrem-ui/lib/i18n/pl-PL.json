{"@sage/xtrem-ui/360-view": "", "@sage/xtrem-ui/action-button-more": "More actions", "@sage/xtrem-ui/action-clone": "Zamknij", "@sage/xtrem-ui/action-close": "Zamknij", "@sage/xtrem-ui/action-delete": "Usuń", "@sage/xtrem-ui/action-edit": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/action-share": "Udostępnij", "@sage/xtrem-ui/action-tool-bar-items-selected": "Items selected: {{count}}", "@sage/xtrem-ui/actions": "Actions", "@sage/xtrem-ui/add-item-in-line": "Add item", "@sage/xtrem-ui/add-item-in-line-using-sidebar": "Add item in panel", "@sage/xtrem-ui/add-value": "Add value", "@sage/xtrem-ui/aggregation-method": "Aggregation method", "@sage/xtrem-ui/async-mutation-dialog-content": "", "@sage/xtrem-ui/async-mutation-dialog-title": "", "@sage/xtrem-ui/async-operation-error": "", "@sage/xtrem-ui/attachment-options-menu-all": "", "@sage/xtrem-ui/attachment-options-menu-documents": "", "@sage/xtrem-ui/attachment-options-menu-images": "", "@sage/xtrem-ui/attachment-options-menu-others": "", "@sage/xtrem-ui/attachments": "", "@sage/xtrem-ui/average": "Average", "@sage/xtrem-ui/axes": "", "@sage/xtrem-ui/bar-code-component-not-available": "<PERSON><PERSON> kres<PERSON>wy jest niedostępny.", "@sage/xtrem-ui/bulk-action-async-export": "", "@sage/xtrem-ui/bulk-action-dialog-content": "Perform this action on the selected items: {{itemCount}}", "@sage/xtrem-ui/bulk-action-error": "The action could not be started. Try again.", "@sage/xtrem-ui/bulk-action-print": "", "@sage/xtrem-ui/bulk-action-started": "Action started on the selected items.", "@sage/xtrem-ui/bulk-actions-bar-selected": "Items selected: {{count}}", "@sage/xtrem-ui/business-action-in-progress": "", "@sage/xtrem-ui/calendar-month": "", "@sage/xtrem-ui/calendar-view-3-day": "", "@sage/xtrem-ui/calendar-view-day": "", "@sage/xtrem-ui/calendar-view-month": "", "@sage/xtrem-ui/calendar-view-week": "", "@sage/xtrem-ui/calendar-view-year": "", "@sage/xtrem-ui/cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/carbon-date-format": "dd.<PERSON><PERSON><PERSON>yyyy", "@sage/xtrem-ui/chart-component-no-data": "Nie znaleziono danych", "@sage/xtrem-ui/clear-filter-text": "<PERSON><PERSON><PERSON><PERSON><PERSON>ć tekst filtra", "@sage/xtrem-ui/clear-floating-filter": "", "@sage/xtrem-ui/clear-input-text": "Clear", "@sage/xtrem-ui/clear-selection": "Clear selection", "@sage/xtrem-ui/close-full-screen": "<PERSON><PERSON><PERSON>ć pełen ekran", "@sage/xtrem-ui/close-header-section": "Close header", "@sage/xtrem-ui/close-record": "Close record", "@sage/xtrem-ui/collapse-section": "<PERSON><PERSON><PERSON> sekcję", "@sage/xtrem-ui/collection-data-service-more-errors": "i {{0}} błędów więcej", "@sage/xtrem-ui/consumer-mock-hide-test-ids": "Ukryj <PERSON> testowe", "@sage/xtrem-ui/consumer-mock-show-test-ids": "Pokaż ID testowe", "@sage/xtrem-ui/copy-error-detail": "<PERSON><PERSON><PERSON><PERSON> szczegóły błędu", "@sage/xtrem-ui/copy-error-details": "<PERSON><PERSON><PERSON><PERSON> szczegóły błędu", "@sage/xtrem-ui/create-a-new-view": "", "@sage/xtrem-ui/create-a-view": "", "@sage/xtrem-ui/create-error": "Podczas tworzenia wystąpił błąd {{0}}.", "@sage/xtrem-ui/create-new-dashboard": "+ Create", "@sage/xtrem-ui/crud-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/crud-confirm": "", "@sage/xtrem-ui/crud-create": "Create", "@sage/xtrem-ui/crud-create-success": "Utworzono rekord", "@sage/xtrem-ui/crud-delete": "Usuń", "@sage/xtrem-ui/crud-delete-record-button": "Delete", "@sage/xtrem-ui/crud-delete-record-warning-message": "You are about to delete this record.", "@sage/xtrem-ui/crud-delete-record-warning-title": "Usuń rekord", "@sage/xtrem-ui/crud-delete-successfully": "Us<PERSON>ę<PERSON> rekord", "@sage/xtrem-ui/crud-duplicate": "Duplicate", "@sage/xtrem-ui/crud-duplicate-dialog-subtitle": "", "@sage/xtrem-ui/crud-duplicate-dialog-title": "", "@sage/xtrem-ui/crud-duplicate-failed": "The record duplication failed.", "@sage/xtrem-ui/crud-duplicate-successfully": "", "@sage/xtrem-ui/crud-save": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/crud-save-failed": "Zapis rekordu nie powiódł się", "@sage/xtrem-ui/crud-update-success": "Zaktualizowano rekord", "@sage/xtrem-ui/dashboard-actions": "", "@sage/xtrem-ui/dashboard-add_contact": "", "@sage/xtrem-ui/dashboard-add_note": "", "@sage/xtrem-ui/dashboard-add_site": "", "@sage/xtrem-ui/dashboard-add-widget": "Add widget", "@sage/xtrem-ui/dashboard-address": "", "@sage/xtrem-ui/dashboard-address_name": "", "@sage/xtrem-ui/dashboard-addresses": "", "@sage/xtrem-ui/dashboard-cancel": "", "@sage/xtrem-ui/dashboard-choose_date": "", "@sage/xtrem-ui/dashboard-clear_filter": "", "@sage/xtrem-ui/dashboard-contact": "", "@sage/xtrem-ui/dashboard-contact_card_empty_text": "", "@sage/xtrem-ui/dashboard-contact_image": "", "@sage/xtrem-ui/dashboard-contact_type": "", "@sage/xtrem-ui/dashboard-contacts": "", "@sage/xtrem-ui/dashboard-create": "", "@sage/xtrem-ui/dashboard-create-dashboard": "Create dashboard", "@sage/xtrem-ui/dashboard-current_date_filter": "", "@sage/xtrem-ui/dashboard-day": "", "@sage/xtrem-ui/dashboard-delete": "", "@sage/xtrem-ui/dashboard-delete-dashboard-dialog-message": "", "@sage/xtrem-ui/dashboard-delete-dashboard-dialog-title": "", "@sage/xtrem-ui/dashboard-deleted": "", "@sage/xtrem-ui/dashboard-duplicate": "", "@sage/xtrem-ui/dashboard-duplicated": "", "@sage/xtrem-ui/dashboard-edit": "", "@sage/xtrem-ui/dashboard-editor-add-widget": "Add", "@sage/xtrem-ui/dashboard-editor-all-widgets": "All widgets", "@sage/xtrem-ui/dashboard-editor-cancel-edit": "Cancel", "@sage/xtrem-ui/dashboard-editor-create-dialog-blank-description": "Start with a blank template and add the widgets you need.", "@sage/xtrem-ui/dashboard-editor-create-dialog-blank-title": "Blank template", "@sage/xtrem-ui/dashboard-editor-create-dialog-description": "Select a template to get started or build your own dashboard. You can customize any dashboard by adding or removing widgets.", "@sage/xtrem-ui/dashboard-editor-create-dialog-title": "Select dashboard template", "@sage/xtrem-ui/dashboard-editor-edit-title": "Edit title", "@sage/xtrem-ui/dashboard-editor-redo": "Redo", "@sage/xtrem-ui/dashboard-editor-save": "Save", "@sage/xtrem-ui/dashboard-editor-save-error": "The dashboard could not be saved.", "@sage/xtrem-ui/dashboard-editor-saved-successfully": "Dash<PERSON> saved.", "@sage/xtrem-ui/dashboard-editor-title": "Dashboard editor", "@sage/xtrem-ui/dashboard-editor-undo": "Undo", "@sage/xtrem-ui/dashboard-editor-widget-edit": "Edit", "@sage/xtrem-ui/dashboard-editor-widget-list-add": "Create a widget", "@sage/xtrem-ui/dashboard-editor-widget-list-title": "Widgets", "@sage/xtrem-ui/dashboard-email": "", "@sage/xtrem-ui/dashboard-empty-heading": "This dashboard is empty.", "@sage/xtrem-ui/dashboard-empty-subtitle": "Add a widget to customize your dashboard.", "@sage/xtrem-ui/dashboard-expand_row": "", "@sage/xtrem-ui/dashboard-failed-to-create": "The dashboard could not be created.", "@sage/xtrem-ui/dashboard-failed-to-delete": "", "@sage/xtrem-ui/dashboard-failed-to-duplicate": "", "@sage/xtrem-ui/dashboard-month": "", "@sage/xtrem-ui/dashboard-month_1": "", "@sage/xtrem-ui/dashboard-month_10": "", "@sage/xtrem-ui/dashboard-month_11": "", "@sage/xtrem-ui/dashboard-month_12": "", "@sage/xtrem-ui/dashboard-month_2": "", "@sage/xtrem-ui/dashboard-month_3": "", "@sage/xtrem-ui/dashboard-month_4": "", "@sage/xtrem-ui/dashboard-month_5": "", "@sage/xtrem-ui/dashboard-month_6": "", "@sage/xtrem-ui/dashboard-month_7": "", "@sage/xtrem-ui/dashboard-month_8": "", "@sage/xtrem-ui/dashboard-month_9": "", "@sage/xtrem-ui/dashboard-more_options": "", "@sage/xtrem-ui/dashboard-next": "", "@sage/xtrem-ui/dashboard-next_period": "", "@sage/xtrem-ui/dashboard-no-change": "No change", "@sage/xtrem-ui/dashboard-note": "", "@sage/xtrem-ui/dashboard-notes": "", "@sage/xtrem-ui/dashboard-phone_number": "", "@sage/xtrem-ui/dashboard-position": "", "@sage/xtrem-ui/dashboard-previous": "", "@sage/xtrem-ui/dashboard-previous_period": "", "@sage/xtrem-ui/dashboard-quarter": "", "@sage/xtrem-ui/dashboard-reload": "", "@sage/xtrem-ui/dashboard-save": "", "@sage/xtrem-ui/dashboard-scroll_left": "", "@sage/xtrem-ui/dashboard-scroll_right": "", "@sage/xtrem-ui/dashboard-select_address": "", "@sage/xtrem-ui/dashboard-select_contact": "", "@sage/xtrem-ui/dashboard-settings": "", "@sage/xtrem-ui/dashboard-site": "", "@sage/xtrem-ui/dashboard-site_card_empty_text": "", "@sage/xtrem-ui/dashboard-table_filter_menu": "", "@sage/xtrem-ui/dashboard-table_select": "", "@sage/xtrem-ui/dashboard-view_switch": "", "@sage/xtrem-ui/dashboard-week": "", "@sage/xtrem-ui/dashboard-widget-category-others": "Others", "@sage/xtrem-ui/dashboard-widget-close": "Zamknij", "@sage/xtrem-ui/dashboard-widget-refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-widget-settings": "Ustawienia", "@sage/xtrem-ui/dashboard-year": "", "@sage/xtrem-ui/date-error-day-range": "<PERSON>ak<PERSON> dni to od 1 do 31.", "@sage/xtrem-ui/date-error-month-range": "<PERSON><PERSON><PERSON> to od 1 do 12.", "@sage/xtrem-ui/date-error-year-range": "Zakres lat to od 1800 do 2200.", "@sage/xtrem-ui/date-format": "", "@sage/xtrem-ui/date-format-separator": "/", "@sage/xtrem-ui/date-time-component-aria-label": "", "@sage/xtrem-ui/date-time-range-end-date": "", "@sage/xtrem-ui/date-time-range-start-date": "", "@sage/xtrem-ui/date-time-range-time": "", "@sage/xtrem-ui/date-time-range-time-zone": "", "@sage/xtrem-ui/datetime-aria-label": "", "@sage/xtrem-ui/datetime-range-aria-label": "", "@sage/xtrem-ui/datetime-range-end-date-error": "", "@sage/xtrem-ui/default-view-cannot-be-edited": "", "@sage/xtrem-ui/delete-error": "Podczas usuwania wystąpił błąd {{0}}.", "@sage/xtrem-ui/delete-record-warning-message": "Usuniesz ten rekord. Kontynuować?", "@sage/xtrem-ui/delete-record-warning-title": "Potwierdź usuwanie", "@sage/xtrem-ui/detailed-icon-name-accounting": "Accounting", "@sage/xtrem-ui/detailed-icon-name-addons": "Addons", "@sage/xtrem-ui/detailed-icon-name-animal": "Animal", "@sage/xtrem-ui/detailed-icon-name-apple": "Apple", "@sage/xtrem-ui/detailed-icon-name-asset_mgt": "Asset management", "@sage/xtrem-ui/detailed-icon-name-award": "Award", "@sage/xtrem-ui/detailed-icon-name-bag": "Bag", "@sage/xtrem-ui/detailed-icon-name-bakery": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-barcode": "Barcode", "@sage/xtrem-ui/detailed-icon-name-bicycle": "Bicycle", "@sage/xtrem-ui/detailed-icon-name-binocular": "Binoculars", "@sage/xtrem-ui/detailed-icon-name-book": "Book", "@sage/xtrem-ui/detailed-icon-name-bright": "<PERSON>", "@sage/xtrem-ui/detailed-icon-name-building": "Building", "@sage/xtrem-ui/detailed-icon-name-calculator": "Calculator", "@sage/xtrem-ui/detailed-icon-name-calendar": "Calendar", "@sage/xtrem-ui/detailed-icon-name-camera": "Camera", "@sage/xtrem-ui/detailed-icon-name-card": "Card", "@sage/xtrem-ui/detailed-icon-name-cart": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-certificate": "Certificate", "@sage/xtrem-ui/detailed-icon-name-check": "Check", "@sage/xtrem-ui/detailed-icon-name-checkbox": "Checkbox", "@sage/xtrem-ui/detailed-icon-name-checklist": "Checklist", "@sage/xtrem-ui/detailed-icon-name-chemical": "Chemical", "@sage/xtrem-ui/detailed-icon-name-chess": "Chess", "@sage/xtrem-ui/detailed-icon-name-click": "Click", "@sage/xtrem-ui/detailed-icon-name-clock": "Clock", "@sage/xtrem-ui/detailed-icon-name-close": "Close", "@sage/xtrem-ui/detailed-icon-name-clothes": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-cloud": "Cloud", "@sage/xtrem-ui/detailed-icon-name-coffee": "Coffee", "@sage/xtrem-ui/detailed-icon-name-compass": "<PERSON>mp<PERSON>", "@sage/xtrem-ui/detailed-icon-name-connected": "Connected", "@sage/xtrem-ui/detailed-icon-name-consultant": "Consultant", "@sage/xtrem-ui/detailed-icon-name-conversation": "Conversation", "@sage/xtrem-ui/detailed-icon-name-cooking": "Cooking", "@sage/xtrem-ui/detailed-icon-name-cpu": "CPU", "@sage/xtrem-ui/detailed-icon-name-crowd": "Crowd", "@sage/xtrem-ui/detailed-icon-name-crown": "Crown", "@sage/xtrem-ui/detailed-icon-name-data": "Data", "@sage/xtrem-ui/detailed-icon-name-database": "Database", "@sage/xtrem-ui/detailed-icon-name-decline": "Decline", "@sage/xtrem-ui/detailed-icon-name-desktop": "Desktop", "@sage/xtrem-ui/detailed-icon-name-devices": "Devices", "@sage/xtrem-ui/detailed-icon-name-dollar": "Dollar", "@sage/xtrem-ui/detailed-icon-name-download": "Download", "@sage/xtrem-ui/detailed-icon-name-ear": "Ear", "@sage/xtrem-ui/detailed-icon-name-ecomm": "E-commerce", "@sage/xtrem-ui/detailed-icon-name-euro": "Euro", "@sage/xtrem-ui/detailed-icon-name-excavator": "Excavator", "@sage/xtrem-ui/detailed-icon-name-eye": "Eye", "@sage/xtrem-ui/detailed-icon-name-factory": "Factory", "@sage/xtrem-ui/detailed-icon-name-favorite": "Favorite", "@sage/xtrem-ui/detailed-icon-name-filter": "Filter", "@sage/xtrem-ui/detailed-icon-name-financials": "Financials", "@sage/xtrem-ui/detailed-icon-name-flag": "Flag", "@sage/xtrem-ui/detailed-icon-name-folder": "Folder", "@sage/xtrem-ui/detailed-icon-name-food": "Food", "@sage/xtrem-ui/detailed-icon-name-form": "Form", "@sage/xtrem-ui/detailed-icon-name-gauge": "Gauge", "@sage/xtrem-ui/detailed-icon-name-gears": "Gears", "@sage/xtrem-ui/detailed-icon-name-glasses": "Glasses", "@sage/xtrem-ui/detailed-icon-name-globe": "Globe", "@sage/xtrem-ui/detailed-icon-name-green": "Green", "@sage/xtrem-ui/detailed-icon-name-handshake": "Handshake", "@sage/xtrem-ui/detailed-icon-name-happy": "Happy", "@sage/xtrem-ui/detailed-icon-name-heart": "Heart", "@sage/xtrem-ui/detailed-icon-name-hide": "<PERSON>de", "@sage/xtrem-ui/detailed-icon-name-holiday": "Holiday", "@sage/xtrem-ui/detailed-icon-name-home": "Home", "@sage/xtrem-ui/detailed-icon-name-hourglass": "Hourglass", "@sage/xtrem-ui/detailed-icon-name-hub": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-idea": "Idea", "@sage/xtrem-ui/detailed-icon-name-incline": "Incline", "@sage/xtrem-ui/detailed-icon-name-industry": "Industry", "@sage/xtrem-ui/detailed-icon-name-info": "Info", "@sage/xtrem-ui/detailed-icon-name-integration": "Integration", "@sage/xtrem-ui/detailed-icon-name-jewelry": "Jewelry", "@sage/xtrem-ui/detailed-icon-name-keys": "Keys", "@sage/xtrem-ui/detailed-icon-name-lab": "Lab", "@sage/xtrem-ui/detailed-icon-name-label": "Label", "@sage/xtrem-ui/detailed-icon-name-laptop": "Laptop", "@sage/xtrem-ui/detailed-icon-name-lightning": "Lightning", "@sage/xtrem-ui/detailed-icon-name-like": "Like", "@sage/xtrem-ui/detailed-icon-name-link": "Link", "@sage/xtrem-ui/detailed-icon-name-locations": "Locations", "@sage/xtrem-ui/detailed-icon-name-lock": "Lock", "@sage/xtrem-ui/detailed-icon-name-lock_unlocked": "Lock Unlocked", "@sage/xtrem-ui/detailed-icon-name-mail": "Mail", "@sage/xtrem-ui/detailed-icon-name-map": "Map", "@sage/xtrem-ui/detailed-icon-name-medical": "Medical", "@sage/xtrem-ui/detailed-icon-name-megaphone": "Megaphone", "@sage/xtrem-ui/detailed-icon-name-memo": "Memo", "@sage/xtrem-ui/detailed-icon-name-microphone": "Microphone", "@sage/xtrem-ui/detailed-icon-name-minus": "Minus", "@sage/xtrem-ui/detailed-icon-name-mouse": "Mouse", "@sage/xtrem-ui/detailed-icon-name-newspaper": "Newspaper", "@sage/xtrem-ui/detailed-icon-name-note": "Note", "@sage/xtrem-ui/detailed-icon-name-notebook": "Notebook", "@sage/xtrem-ui/detailed-icon-name-office": "Office", "@sage/xtrem-ui/detailed-icon-name-page": "Page", "@sage/xtrem-ui/detailed-icon-name-payment": "Payment", "@sage/xtrem-ui/detailed-icon-name-payroll": "Payroll", "@sage/xtrem-ui/detailed-icon-name-pen": "Pen", "@sage/xtrem-ui/detailed-icon-name-pencil": "Pencil", "@sage/xtrem-ui/detailed-icon-name-person": "Person", "@sage/xtrem-ui/detailed-icon-name-phone": "Phone", "@sage/xtrem-ui/detailed-icon-name-pin": "<PERSON>n", "@sage/xtrem-ui/detailed-icon-name-plus": "Plus", "@sage/xtrem-ui/detailed-icon-name-point": "Point", "@sage/xtrem-ui/detailed-icon-name-pound": "Pound", "@sage/xtrem-ui/detailed-icon-name-power": "Power", "@sage/xtrem-ui/detailed-icon-name-presentation": "Presentation", "@sage/xtrem-ui/detailed-icon-name-print": "Print", "@sage/xtrem-ui/detailed-icon-name-processing": "Processing", "@sage/xtrem-ui/detailed-icon-name-puzzle": "Puzzle", "@sage/xtrem-ui/detailed-icon-name-question": "Question", "@sage/xtrem-ui/detailed-icon-name-receipts": "Receipts", "@sage/xtrem-ui/detailed-icon-name-recycle": "Recycle", "@sage/xtrem-ui/detailed-icon-name-redo": "Redo", "@sage/xtrem-ui/detailed-icon-name-remote": "Remote", "@sage/xtrem-ui/detailed-icon-name-rocket": "Rocket", "@sage/xtrem-ui/detailed-icon-name-safe": "Safe", "@sage/xtrem-ui/detailed-icon-name-satelite": "Satellite", "@sage/xtrem-ui/detailed-icon-name-savings": "Savings", "@sage/xtrem-ui/detailed-icon-name-scissors": "Scissors", "@sage/xtrem-ui/detailed-icon-name-server": "Server", "@sage/xtrem-ui/detailed-icon-name-service": "Service", "@sage/xtrem-ui/detailed-icon-name-setting": "Setting", "@sage/xtrem-ui/detailed-icon-name-share": "Share", "@sage/xtrem-ui/detailed-icon-name-shoes": "Shoes", "@sage/xtrem-ui/detailed-icon-name-shuffle": "Shuffle", "@sage/xtrem-ui/detailed-icon-name-sign": "Sign", "@sage/xtrem-ui/detailed-icon-name-sim": "SIM Card", "@sage/xtrem-ui/detailed-icon-name-smartphone": "Smartphone", "@sage/xtrem-ui/detailed-icon-name-stationeries": "Stationeries", "@sage/xtrem-ui/detailed-icon-name-store": "Store", "@sage/xtrem-ui/detailed-icon-name-support": "Support", "@sage/xtrem-ui/detailed-icon-name-sync": "Sync", "@sage/xtrem-ui/detailed-icon-name-tab": "Tab", "@sage/xtrem-ui/detailed-icon-name-table": "Table", "@sage/xtrem-ui/detailed-icon-name-tablet": "Tablet", "@sage/xtrem-ui/detailed-icon-name-thermometer": "Thermometer", "@sage/xtrem-ui/detailed-icon-name-timer": "Timer", "@sage/xtrem-ui/detailed-icon-name-tools": "Tools", "@sage/xtrem-ui/detailed-icon-name-travel": "Travel", "@sage/xtrem-ui/detailed-icon-name-truck": "Truck", "@sage/xtrem-ui/detailed-icon-name-undo": "Undo", "@sage/xtrem-ui/detailed-icon-name-video": "Video", "@sage/xtrem-ui/detailed-icon-name-wallet": "Wallet", "@sage/xtrem-ui/detailed-icon-name-warehouse": "Warehouse", "@sage/xtrem-ui/detailed-icon-name-warning": "Warning", "@sage/xtrem-ui/detailed-icon-name-weather": "Weather", "@sage/xtrem-ui/detailed-icon-name-wireless": "Wireless", "@sage/xtrem-ui/detailed-icon-name-wrench": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-writing": "Writing", "@sage/xtrem-ui/dialog-actions-onload-unhandled-error": "Wystąpił nieznany błąd. Spróbuj ponownie.", "@sage/xtrem-ui/dialog-loading": "Loading", "@sage/xtrem-ui/dialogs-async-loader-button-keep-waiting": "", "@sage/xtrem-ui/dialogs-async-loader-button-notify-me": "", "@sage/xtrem-ui/dialogs-async-loader-button-stop": "", "@sage/xtrem-ui/dialogs-async-loader-waiting": "", "@sage/xtrem-ui/dirty-phantom-row-validation-error-message-mac": "", "@sage/xtrem-ui/dirty-phantom-row-validation-error-message-pc": "", "@sage/xtrem-ui/display-errors-back-to-full-display": "<PERSON><PERSON>łen widok", "@sage/xtrem-ui/display-errors-back-to-full-display-tooltip": "Wróć do pełnego widoku", "@sage/xtrem-ui/display-errors-button": "Wyświetl błędy", "@sage/xtrem-ui/distinct-count": "Distinct count", "@sage/xtrem-ui/divisor": "", "@sage/xtrem-ui/divisor-helper-text": "", "@sage/xtrem-ui/document-metadata": "", "@sage/xtrem-ui/drag-drop-file": "", "@sage/xtrem-ui/duplicate-error": "", "@sage/xtrem-ui/empty-state-filter-text-no-results-button": "Clear filters", "@sage/xtrem-ui/empty-state-filter-text-no-results-description": "Try different criteria or create a new record to get started.", "@sage/xtrem-ui/empty-state-filter-text-title": "No results found.", "@sage/xtrem-ui/empty-state-text": "This list has no items yet.", "@sage/xtrem-ui/error": "Błąd", "@sage/xtrem-ui/error-detail-copied-to-clipboard": "Skopiowano szczegóły błędu do schowka", "@sage/xtrem-ui/error-loading-stickers": "Podczas ładowania naklejek wystąpił błąd. Spróbuj ponownie.", "@sage/xtrem-ui/export-format-csv": "CSV", "@sage/xtrem-ui/export-format-excel": "Excel", "@sage/xtrem-ui/failed-to-refresh-navigation-panel": "", "@sage/xtrem-ui/failed-to-save": "Zapis rekordu nie powiódł się", "@sage/xtrem-ui/false": "False", "@sage/xtrem-ui/field-mandatory": "", "@sage/xtrem-ui/field-max-items-value": "", "@sage/xtrem-ui/field-maximum-date-value": "Najpóźniejsza data to {{1}}", "@sage/xtrem-ui/field-maximum-length-value": "<PERSON><PERSON><PERSON><PERSON><PERSON> to {{1}}", "@sage/xtrem-ui/field-maximum-value": "<PERSON><PERSON><PERSON><PERSON><PERSON> to {{1}}", "@sage/xtrem-ui/field-min-items-value": "", "@sage/xtrem-ui/field-minimum-date-value": "Najwcześniejsza data to {{1}}", "@sage/xtrem-ui/field-minimum-length-value": "Mini<PERSON><PERSON> to {{1}}", "@sage/xtrem-ui/field-minimum-value": "<PERSON><PERSON><PERSON> to {{1}}", "@sage/xtrem-ui/field-non-zero": "{{0}} nie może r<PERSON> się zero.", "@sage/xtrem-ui/field-not-valid": "Nieprawidłowa wartość", "@sage/xtrem-ui/field-select-mandatory": "", "@sage/xtrem-ui/field-select-mandatory-checkbox": "", "@sage/xtrem-ui/field-select-mandatory-or-enter": "", "@sage/xtrem-ui/file-component-browse-file": "", "@sage/xtrem-ui/file-component-browse-files": "", "@sage/xtrem-ui/file-component-drag-drop": "", "@sage/xtrem-ui/file-component-drag-drop-empty": "", "@sage/xtrem-ui/file-component-hide-upload-area": "", "@sage/xtrem-ui/file-component-onchange-error": "Błąd podczas wgrywania pliku: {{0}}", "@sage/xtrem-ui/file-component-show-upload-area": "", "@sage/xtrem-ui/file-upload-failed": "The file upload failed.", "@sage/xtrem-ui/filter-manager-apply-button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/filter-manager-cancel-button": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/filter-manager-checkbox": "Aktywny", "@sage/xtrem-ui/filter-manager-clear-all-button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wszystko", "@sage/xtrem-ui/filter-manager-close": "Zamknij", "@sage/xtrem-ui/filter-manager-invalid-filters": "Nieprawidłowa wartość filtra", "@sage/xtrem-ui/filter-manager-max-value": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/filter-manager-min-value": "<PERSON><PERSON>", "@sage/xtrem-ui/filter-manager-open": "Otwórz", "@sage/xtrem-ui/filter-manager-range-end": "Data do", "@sage/xtrem-ui/filter-manager-range-start": "Data od", "@sage/xtrem-ui/filter-manager-title-header": "Filtry", "@sage/xtrem-ui/filter-range": "Pole {{fieldTitle}} jest mi<PERSON><PERSON> {{value1}} a {{value2}}.", "@sage/xtrem-ui/filter-search-placeholder": "Search", "@sage/xtrem-ui/filter-select-lookup-dialog-dialog-title": "Selection", "@sage/xtrem-ui/filter-select-lookup-dialog-failed-fetch": "Nie znaleziono operacji", "@sage/xtrem-ui/filter-value-equals": "Pole {{fieldTitle}} jest us<PERSON><PERSON><PERSON> jako {{filterValue}}.", "@sage/xtrem-ui/filter-value-greater-than-equal": "<PERSON><PERSON><PERSON><PERSON> pola {{fieldTitle}} mus<PERSON> w<PERSON><PERSON> przyna<PERSON>niej {{filterValue}}.", "@sage/xtrem-ui/filter-value-less-than-equal": "<PERSON><PERSON><PERSON><PERSON> pola {{fieldTitle}} nie może prz<PERSON> {{filterValue}}.", "@sage/xtrem-ui/filter-value-not-equal": "{{fieldTitle}} != {{filterValue}}", "@sage/xtrem-ui/floating-filter-label": "", "@sage/xtrem-ui/footer-actions-more-button": "More", "@sage/xtrem-ui/form-designer-var-current-date": "", "@sage/xtrem-ui/form-designer-var-generation-by": "", "@sage/xtrem-ui/form-designer-var-page-number": "", "@sage/xtrem-ui/form-designer-var-parameter": "", "@sage/xtrem-ui/form-designer-var-total-number-of-pages": "", "@sage/xtrem-ui/general_invalid_json": "Nieprawidłowy JSON", "@sage/xtrem-ui/general-finish-editing": "Zakończ edytowanie", "@sage/xtrem-ui/general-welcome-page": "Strona startowa", "@sage/xtrem-ui/generic-no": "<PERSON><PERSON>", "@sage/xtrem-ui/generic-yes": "Tak", "@sage/xtrem-ui/greaterThanOrEqual": "Greater than or equal to", "@sage/xtrem-ui/group-by": "Group by", "@sage/xtrem-ui/group-by-day": "Day", "@sage/xtrem-ui/group-by-month": "Month", "@sage/xtrem-ui/group-by-this-column": "Group by this column", "@sage/xtrem-ui/group-by-year": "Year", "@sage/xtrem-ui/helper-text-contains": "Zawiera", "@sage/xtrem-ui/helper-text-ends-with": "Kończy się na", "@sage/xtrem-ui/helper-text-matches": "Równa się", "@sage/xtrem-ui/helper-text-starts-with": "Zaczyna się od", "@sage/xtrem-ui/hide-floating-filters": "Hide table filters", "@sage/xtrem-ui/hide-technical-details": "Ukryj szczegóły techniczne", "@sage/xtrem-ui/hours": "", "@sage/xtrem-ui/image-component-add-image": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/image-component-not-available": "Obraz niedostępny", "@sage/xtrem-ui/image-field-remove-image": "Usuń", "@sage/xtrem-ui/infinite-indicator-end": "", "@sage/xtrem-ui/infinite-indicator-start": "", "@sage/xtrem-ui/insight-plural": "", "@sage/xtrem-ui/insight-singular": "", "@sage/xtrem-ui/invalid-file-type-message": "", "@sage/xtrem-ui/invalid-number": "Invalid number", "@sage/xtrem-ui/invalid-response-no-data": "Nieprawidłowa odpowiedź. Nie znaleziono danych", "@sage/xtrem-ui/Label": "Label", "@sage/xtrem-ui/last-30-days": "", "@sage/xtrem-ui/last-7-days": "", "@sage/xtrem-ui/link-error-numbers-tooltip": "Informacje o błędzie", "@sage/xtrem-ui/link-error-quantity": "1 błąd", "@sage/xtrem-ui/link-errors-quantity": "{{0}} błędów", "@sage/xtrem-ui/list-printing": "", "@sage/xtrem-ui/list-printing-assignment": "", "@sage/xtrem-ui/lookup-dialog-confirm-select": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/lookup-dialog-create-new-item": "", "@sage/xtrem-ui/lookup-dialog-dialog-title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/lookup-dialog-failed-fetch": "Ładowanie opcji nie powiodło się", "@sage/xtrem-ui/main-list-refresh": "", "@sage/xtrem-ui/maximum": "Maximum", "@sage/xtrem-ui/message-type-ai": "", "@sage/xtrem-ui/message-type-error": "", "@sage/xtrem-ui/message-type-info": "", "@sage/xtrem-ui/message-type-information": "", "@sage/xtrem-ui/message-type-success": "", "@sage/xtrem-ui/message-type-warning": "", "@sage/xtrem-ui/mime-type/application/atom+xml": "", "@sage/xtrem-ui/mime-type/application/ecmascript": "", "@sage/xtrem-ui/mime-type/application/font-woff": "", "@sage/xtrem-ui/mime-type/application/font-woff2": "", "@sage/xtrem-ui/mime-type/application/graphql": "", "@sage/xtrem-ui/mime-type/application/java-archive": "", "@sage/xtrem-ui/mime-type/application/javascript": "", "@sage/xtrem-ui/mime-type/application/json": "", "@sage/xtrem-ui/mime-type/application/ld+json": "", "@sage/xtrem-ui/mime-type/application/mac-binhex40": "", "@sage/xtrem-ui/mime-type/application/mathml+xml": "", "@sage/xtrem-ui/mime-type/application/ms-visio": "", "@sage/xtrem-ui/mime-type/application/ms-visio.stencil.macroEnabled.12": "", "@sage/xtrem-ui/mime-type/application/ms-visio.template": "", "@sage/xtrem-ui/mime-type/application/msword": "", "@sage/xtrem-ui/mime-type/application/octet-stream": "", "@sage/xtrem-ui/mime-type/application/pdf": "", "@sage/xtrem-ui/mime-type/application/postscript": "", "@sage/xtrem-ui/mime-type/application/rdf+xml": "", "@sage/xtrem-ui/mime-type/application/rss+xml": "", "@sage/xtrem-ui/mime-type/application/rtf": "", "@sage/xtrem-ui/mime-type/application/sql": "", "@sage/xtrem-ui/mime-type/application/vnd.amazon.ebook": "", "@sage/xtrem-ui/mime-type/application/vnd.android.package-archive": "", "@sage/xtrem-ui/mime-type/application/vnd.apple.installer+xml": "", "@sage/xtrem-ui/mime-type/application/vnd.apple.keynote": "", "@sage/xtrem-ui/mime-type/application/vnd.apple.numbers": "", "@sage/xtrem-ui/mime-type/application/vnd.apple.pages": "", "@sage/xtrem-ui/mime-type/application/vnd.google-earth.kml+xml": "", "@sage/xtrem-ui/mime-type/application/vnd.google-earth.kmz": "", "@sage/xtrem-ui/mime-type/application/vnd.mozilla.xul+xml": "", "@sage/xtrem-ui/mime-type/application/vnd.ms-access": "", "@sage/xtrem-ui/mime-type/application/vnd.ms-excel": "", "@sage/xtrem-ui/mime-type/application/vnd.ms-fontobject": "", "@sage/xtrem-ui/mime-type/application/vnd.ms-powerpoint": "", "@sage/xtrem-ui/mime-type/application/vnd.ms-project": "", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.graphics": "", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.presentation": "", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.spreadsheet": "", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.text": "", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.presentationml.presentation": "", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.wordprocessingml.document": "", "@sage/xtrem-ui/mime-type/application/vnd.rn-realmedia": "", "@sage/xtrem-ui/mime-type/application/vnd.wap.wmlc": "", "@sage/xtrem-ui/mime-type/application/x-7z-compressed": "", "@sage/xtrem-ui/mime-type/application/x-abiword": "", "@sage/xtrem-ui/mime-type/application/x-bzip": "", "@sage/xtrem-ui/mime-type/application/x-bzip2": "", "@sage/xtrem-ui/mime-type/application/x-cd-image": "", "@sage/xtrem-ui/mime-type/application/x-chrome-extension": "", "@sage/xtrem-ui/mime-type/application/x-cocoa": "", "@sage/xtrem-ui/mime-type/application/x-csh": "", "@sage/xtrem-ui/mime-type/application/x-deb": "", "@sage/xtrem-ui/mime-type/application/x-dvi": "", "@sage/xtrem-ui/mime-type/application/x-font-opentype": "", "@sage/xtrem-ui/mime-type/application/x-font-otf": "", "@sage/xtrem-ui/mime-type/application/x-font-ttf": "", "@sage/xtrem-ui/mime-type/application/x-font-woff": "", "@sage/xtrem-ui/mime-type/application/x-font-woff2": "", "@sage/xtrem-ui/mime-type/application/x-gtar": "", "@sage/xtrem-ui/mime-type/application/x-gzip": "", "@sage/xtrem-ui/mime-type/application/x-hdf": "", "@sage/xtrem-ui/mime-type/application/x-httpd-php": "", "@sage/xtrem-ui/mime-type/application/x-httpd-php-source": "", "@sage/xtrem-ui/mime-type/application/x-java-applet": "", "@sage/xtrem-ui/mime-type/application/x-java-archive": "", "@sage/xtrem-ui/mime-type/application/x-java-archive-diff": "", "@sage/xtrem-ui/mime-type/application/x-java-jnlp-file": "", "@sage/xtrem-ui/mime-type/application/x-javascript": "", "@sage/xtrem-ui/mime-type/application/x-latex": "", "@sage/xtrem-ui/mime-type/application/x-lzh-compressed": "", "@sage/xtrem-ui/mime-type/application/x-makeself": "", "@sage/xtrem-ui/mime-type/application/x-mif": "", "@sage/xtrem-ui/mime-type/application/x-msaccess": "", "@sage/xtrem-ui/mime-type/application/x-msdownload": "", "@sage/xtrem-ui/mime-type/application/x-msmetafile": "", "@sage/xtrem-ui/mime-type/application/x-msmoney": "", "@sage/xtrem-ui/mime-type/application/x-perl": "", "@sage/xtrem-ui/mime-type/application/x-pilot": "", "@sage/xtrem-ui/mime-type/application/x-rar-compressed": "", "@sage/xtrem-ui/mime-type/application/x-redhat-package-manager": "", "@sage/xtrem-ui/mime-type/application/x-rpm": "", "@sage/xtrem-ui/mime-type/application/x-sea": "", "@sage/xtrem-ui/mime-type/application/x-sh": "", "@sage/xtrem-ui/mime-type/application/x-shockwave-flash": "", "@sage/xtrem-ui/mime-type/application/x-sql": "", "@sage/xtrem-ui/mime-type/application/x-stuffit": "", "@sage/xtrem-ui/mime-type/application/x-tar": "", "@sage/xtrem-ui/mime-type/application/x-tcl": "", "@sage/xtrem-ui/mime-type/application/x-tex": "", "@sage/xtrem-ui/mime-type/application/x-texinfo": "", "@sage/xtrem-ui/mime-type/application/x-troff": "", "@sage/xtrem-ui/mime-type/application/x-vrml": "", "@sage/xtrem-ui/mime-type/application/x-www-form-urlencoded": "", "@sage/xtrem-ui/mime-type/application/x-x509-ca-cert": "", "@sage/xtrem-ui/mime-type/application/x-xpinstall": "", "@sage/xtrem-ui/mime-type/application/xhtml+xml": "", "@sage/xtrem-ui/mime-type/application/xml": "", "@sage/xtrem-ui/mime-type/application/xslt+xml": "", "@sage/xtrem-ui/mime-type/application/zip": "", "@sage/xtrem-ui/mime-type/audio/aac": "", "@sage/xtrem-ui/mime-type/audio/amr": "", "@sage/xtrem-ui/mime-type/audio/midi": "", "@sage/xtrem-ui/mime-type/audio/mpeg": "", "@sage/xtrem-ui/mime-type/audio/ogg": "", "@sage/xtrem-ui/mime-type/audio/vnd.rn-realaudio": "", "@sage/xtrem-ui/mime-type/audio/wav": "", "@sage/xtrem-ui/mime-type/audio/x-m4a": "", "@sage/xtrem-ui/mime-type/audio/x-matroska": "", "@sage/xtrem-ui/mime-type/audio/x-mpegurl": "", "@sage/xtrem-ui/mime-type/audio/x-ms-wax": "", "@sage/xtrem-ui/mime-type/audio/x-ms-wma": "", "@sage/xtrem-ui/mime-type/audio/x-pn-realaudio": "", "@sage/xtrem-ui/mime-type/audio/x-pn-realaudio-plugin": "", "@sage/xtrem-ui/mime-type/audio/x-realaudio": "", "@sage/xtrem-ui/mime-type/audio/x-wav": "", "@sage/xtrem-ui/mime-type/chemical/x-pdb": "", "@sage/xtrem-ui/mime-type/image/bmp": "", "@sage/xtrem-ui/mime-type/image/cgm": "", "@sage/xtrem-ui/mime-type/image/gif": "", "@sage/xtrem-ui/mime-type/image/jpeg": "", "@sage/xtrem-ui/mime-type/image/png": "", "@sage/xtrem-ui/mime-type/image/svg+xml": "", "@sage/xtrem-ui/mime-type/image/tiff": "", "@sage/xtrem-ui/mime-type/image/vnd.microsoft.icon": "", "@sage/xtrem-ui/mime-type/image/vnd.wap.wbmp": "", "@sage/xtrem-ui/mime-type/image/webp": "", "@sage/xtrem-ui/mime-type/image/x-cmu-raster": "", "@sage/xtrem-ui/mime-type/image/x-icon": "", "@sage/xtrem-ui/mime-type/image/x-jng": "", "@sage/xtrem-ui/mime-type/image/x-ms-bmp": "", "@sage/xtrem-ui/mime-type/image/x-portable-anymap": "", "@sage/xtrem-ui/mime-type/image/x-portable-bitmap": "", "@sage/xtrem-ui/mime-type/image/x-portable-graymap": "", "@sage/xtrem-ui/mime-type/image/x-portable-pixmap": "", "@sage/xtrem-ui/mime-type/image/x-rgb": "", "@sage/xtrem-ui/mime-type/image/x-xbitmap": "", "@sage/xtrem-ui/mime-type/image/x-xpixmap": "", "@sage/xtrem-ui/mime-type/image/x-xwindowdump": "", "@sage/xtrem-ui/mime-type/model/vrml": "", "@sage/xtrem-ui/mime-type/multipart/form-data": "", "@sage/xtrem-ui/mime-type/text/cache-manifest": "", "@sage/xtrem-ui/mime-type/text/calendar": "", "@sage/xtrem-ui/mime-type/text/css": "", "@sage/xtrem-ui/mime-type/text/csv": "", "@sage/xtrem-ui/mime-type/text/ecmascript": "", "@sage/xtrem-ui/mime-type/text/html": "", "@sage/xtrem-ui/mime-type/text/javascript": "", "@sage/xtrem-ui/mime-type/text/markdown": "", "@sage/xtrem-ui/mime-type/text/mathml": "", "@sage/xtrem-ui/mime-type/text/plain": "", "@sage/xtrem-ui/mime-type/text/richtext": "", "@sage/xtrem-ui/mime-type/text/rtf": "", "@sage/xtrem-ui/mime-type/text/tab-separated-values": "", "@sage/xtrem-ui/mime-type/text/vnd.sun.j2me.app-descriptor": "", "@sage/xtrem-ui/mime-type/text/vnd.wap.wml": "", "@sage/xtrem-ui/mime-type/text/vnd.wap.wmlscript": "", "@sage/xtrem-ui/mime-type/text/vtt": "", "@sage/xtrem-ui/mime-type/text/x-component": "", "@sage/xtrem-ui/mime-type/text/x-cross-domain-policy": "", "@sage/xtrem-ui/mime-type/text/x-fortran": "", "@sage/xtrem-ui/mime-type/text/xml": "", "@sage/xtrem-ui/mime-type/video/3gpp": "", "@sage/xtrem-ui/mime-type/video/mp2t": "", "@sage/xtrem-ui/mime-type/video/mp4": "", "@sage/xtrem-ui/mime-type/video/mpeg": "", "@sage/xtrem-ui/mime-type/video/ogg": "", "@sage/xtrem-ui/mime-type/video/quicktime": "", "@sage/xtrem-ui/mime-type/video/vnd.rn-realvideo": "", "@sage/xtrem-ui/mime-type/video/webm": "", "@sage/xtrem-ui/mime-type/video/x-flv": "", "@sage/xtrem-ui/mime-type/video/x-m4v": "", "@sage/xtrem-ui/mime-type/video/x-matroska": "", "@sage/xtrem-ui/mime-type/video/x-mng": "", "@sage/xtrem-ui/mime-type/video/x-ms-asf": "", "@sage/xtrem-ui/mime-type/video/x-ms-wmv": "", "@sage/xtrem-ui/mime-type/video/x-ms-wvx": "", "@sage/xtrem-ui/mime-type/video/x-msvideo": "", "@sage/xtrem-ui/minimum": "Minimum", "@sage/xtrem-ui/minutes": "", "@sage/xtrem-ui/mobile-table-load-more": "Załaduj więcej", "@sage/xtrem-ui/multi-file-deposit-cancel": "", "@sage/xtrem-ui/multi-file-deposit-create-tag": "", "@sage/xtrem-ui/multi-file-deposit-description": "", "@sage/xtrem-ui/multi-file-deposit-edit-details": "", "@sage/xtrem-ui/multi-file-deposit-filename": "", "@sage/xtrem-ui/multi-file-deposit-modified": "", "@sage/xtrem-ui/multi-file-deposit-open-preview": "", "@sage/xtrem-ui/multi-file-deposit-remove": "", "@sage/xtrem-ui/multi-file-deposit-size": "", "@sage/xtrem-ui/multi-file-deposit-status": "", "@sage/xtrem-ui/multi-file-deposit-status-created": "", "@sage/xtrem-ui/multi-file-deposit-status-upload-cancelled": "", "@sage/xtrem-ui/multi-file-deposit-status-upload-failed": "", "@sage/xtrem-ui/multi-file-deposit-status-upload-rejected": "", "@sage/xtrem-ui/multi-file-deposit-status-uploaded": "", "@sage/xtrem-ui/multi-file-deposit-status-verified": "", "@sage/xtrem-ui/multi-file-deposit-tag-description": "", "@sage/xtrem-ui/multi-file-deposit-tag-id": "", "@sage/xtrem-ui/multi-file-deposit-tag-title": "", "@sage/xtrem-ui/multi-file-deposit-tags": "", "@sage/xtrem-ui/multi-file-deposit-title": "", "@sage/xtrem-ui/multi-file-deposit-type": "", "@sage/xtrem-ui/multi-file-deposit-uploaded": "", "@sage/xtrem-ui/multi-file-deposit-uploaded-by": "", "@sage/xtrem-ui/multi-file-upload-cancel": "", "@sage/xtrem-ui/multi-file-upload-cancel-title": "", "@sage/xtrem-ui/multi-file-upload-remove": "", "@sage/xtrem-ui/multi-file-upload-remove-title": "", "@sage/xtrem-ui/must-be-a-number": "You need to enter a number", "@sage/xtrem-ui/must-be-between-1-and-365": "Enter a number between 1 and 365", "@sage/xtrem-ui/must-be-between-zero-and-four": "This value needs to be between 0 and 4.", "@sage/xtrem-ui/must-be-greater-than-zero": "", "@sage/xtrem-ui/name": "Name", "@sage/xtrem-ui/navigation-panel-failed": "An error occurred while loading the selection list items. Try again.", "@sage/xtrem-ui/navigation-panel-my-view": "My selected data", "@sage/xtrem-ui/navigation-panel-no-filter": "All", "@sage/xtrem-ui/navigation-panel-no-results": "Brak danych do wyświetlenia.", "@sage/xtrem-ui/nested-field-errors": "Błędy w zagnieżdżonych polach", "@sage/xtrem-ui/new": "Nowy", "@sage/xtrem-ui/next-day": "", "@sage/xtrem-ui/next-month": "", "@sage/xtrem-ui/next-record": "Next record", "@sage/xtrem-ui/next-week": "", "@sage/xtrem-ui/next-year": "", "@sage/xtrem-ui/no": "<PERSON><PERSON>", "@sage/xtrem-ui/no-dashboard-heading": "You do not have any dashboards yet.", "@sage/xtrem-ui/no-dashboard-subtitle": "Create a dashboard to get started.", "@sage/xtrem-ui/no-dashboard-title": "Drive your business", "@sage/xtrem-ui/no-data": "No data to display", "@sage/xtrem-ui/no-file-available": "", "@sage/xtrem-ui/no-node": "Nie podano w<PERSON>, a {{0}} go nie wskazuje.", "@sage/xtrem-ui/no-page-content-found": "Nie znaleziono zawartości strony. Upewnij się, że strona istnieje, a format adresu URL to /@<vendor name>/@<package name>/@<page name>.", "@sage/xtrem-ui/no-page-translations-found": "Nie znaleziono tłumaczenia dla tej strony", "@sage/xtrem-ui/no-pages-found": "Nie znaleziono strony. Spróbuj zmienić opcje wyszukiwania.", "@sage/xtrem-ui/no-record-id-provided": "W parametrach zapytania nie podano żadnego ID rekordu.", "@sage/xtrem-ui/no-sticker-content-found": "Nie znaleziono zawartości naklejki. Spróbuj zmienić opcje wyszukiwania.", "@sage/xtrem-ui/no-sticker-translations-found": "Nie znaleziono tłumaczenia dla tej <PERSON>jki", "@sage/xtrem-ui/no-stickers-found": "Nie znaleziono naklejki. Spróbuj zmienić opcje wyszukiwania.", "@sage/xtrem-ui/no-value": "", "@sage/xtrem-ui/no-widget-content-found": "Nie znaleziono zawartości dla widżetu {{widget}}.", "@sage/xtrem-ui/number-format-separator": ".", "@sage/xtrem-ui/ok": "OK", "@sage/xtrem-ui/open-custom-field-dialog": "", "@sage/xtrem-ui/open-dynamic-select": "", "@sage/xtrem-ui/open-full-screen": "Otwórz pełen ekran", "@sage/xtrem-ui/open-header-section": "Open header", "@sage/xtrem-ui/open-lookup": "Search", "@sage/xtrem-ui/open-record-history-dialog": "", "@sage/xtrem-ui/open-section": "<PERSON><PERSON><PERSON><PERSON><PERSON> sek<PERSON>", "@sage/xtrem-ui/openFilters": "Ot<PERSON><PERSON><PERSON> filtr", "@sage/xtrem-ui/page-failed-to-load": "Ładowanie strony nie powiodło się", "@sage/xtrem-ui/parent": "Parent", "@sage/xtrem-ui/pdf-metadata-file-name": "", "@sage/xtrem-ui/pdf-metadata-file-size": "", "@sage/xtrem-ui/pdf-metadata-file-type": "", "@sage/xtrem-ui/pdf-metadata-number-of-lines": "", "@sage/xtrem-ui/pdf-metadata-pdf-version": "", "@sage/xtrem-ui/pdf-metadata-producer": "", "@sage/xtrem-ui/pdf-metadata-resolution": "", "@sage/xtrem-ui/please-select-placeholder": "Wybierz...", "@sage/xtrem-ui/pod-collection-add-new": "Dodaj element", "@sage/xtrem-ui/pod-collection-cancel-button": "Cancel", "@sage/xtrem-ui/pod-collection-remove-button": "Remove", "@sage/xtrem-ui/pod-collection-remove-text": "You are about to remove this item.", "@sage/xtrem-ui/pod-collection-remove-title": "Potwierdź usuwanie", "@sage/xtrem-ui/pod-placeholder-text": "Brak danych do wyświetlenia.", "@sage/xtrem-ui/pod-remove-item": "Remove item", "@sage/xtrem-ui/populate-list-title-default": "", "@sage/xtrem-ui/preview-action-download": "", "@sage/xtrem-ui/preview-action-print": "", "@sage/xtrem-ui/preview-close": "", "@sage/xtrem-ui/preview-go-to-page": "", "@sage/xtrem-ui/preview-more-file-info": "", "@sage/xtrem-ui/preview-more-options": "", "@sage/xtrem-ui/preview-more-thumbnails": "", "@sage/xtrem-ui/preview-no-file-selected": "", "@sage/xtrem-ui/preview-no-preview-available": "", "@sage/xtrem-ui/preview-page-next": "", "@sage/xtrem-ui/preview-page-prev": "", "@sage/xtrem-ui/preview-zoom-in": "", "@sage/xtrem-ui/preview-zoom-level": "", "@sage/xtrem-ui/preview-zoom-out": "", "@sage/xtrem-ui/previous-day": "", "@sage/xtrem-ui/previous-month": "", "@sage/xtrem-ui/previous-record": "Previous record", "@sage/xtrem-ui/previous-week": "", "@sage/xtrem-ui/previous-year": "", "@sage/xtrem-ui/property": "Property", "@sage/xtrem-ui/qr-code-component-not-available": "Kod QR jest niedostępny.", "@sage/xtrem-ui/record-history-created-title": "", "@sage/xtrem-ui/record-history-details": "", "@sage/xtrem-ui/record-history-failed": "", "@sage/xtrem-ui/record-history-last-update-title": "", "@sage/xtrem-ui/reference-create-new-item-link": "", "@sage/xtrem-ui/reference-field-at-least-chars": "Minimalna wymagana liczba znaków: {{0}}.", "@sage/xtrem-ui/reference-field-fetching": "Ładowanie sugestii...", "@sage/xtrem-ui/reference-field-no-results": "Nie znaleziono wyników. Spróbuj zmienić opcje wyszukiwania.", "@sage/xtrem-ui/reference-filter-clear-selection": "Clear selected items", "@sage/xtrem-ui/reference-lookup-dialog-search-placeholder": "Wyszukaj...", "@sage/xtrem-ui/reference-open-lookup-link": "Wyświetl listę", "@sage/xtrem-ui/return-arrow": "", "@sage/xtrem-ui/router-actions-unhandled-error": "Wystąpił nieznany błąd. Spróbuj ponownie.", "@sage/xtrem-ui/same-day": "", "@sage/xtrem-ui/same-month": "", "@sage/xtrem-ui/same-week": "", "@sage/xtrem-ui/same-year": "", "@sage/xtrem-ui/save-new-view-as": "", "@sage/xtrem-ui/see-more-items": "", "@sage/xtrem-ui/select-component-loading": "Loading", "@sage/xtrem-ui/select-component-no-results": "Nie znaleziono żadnych elementów", "@sage/xtrem-ui/select-component-type-more": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>zcze: {{0}}", "@sage/xtrem-ui/select-component-type-more-characters": "<PERSON><PERSON> w<PERSON> wprowadź jeszcze {{0}} znaków.", "@sage/xtrem-ui/select-component-type-one-more-character": "Aby wyszukać wprowadź jeszcze 1 znak.", "@sage/xtrem-ui/select-component-type-to-search": "Wyszukaj...", "@sage/xtrem-ui/select-property": "Select property...", "@sage/xtrem-ui/select-record": "Select record", "@sage/xtrem-ui/selection-card-filter-placeholder": "", "@sage/xtrem-ui/series-options": "", "@sage/xtrem-ui/show-floating-filters": "Show table filters", "@sage/xtrem-ui/show-less": "", "@sage/xtrem-ui/show-more": "", "@sage/xtrem-ui/show-technical-details": "Pokaż szczegóły techniczne", "@sage/xtrem-ui/sidebar-apply-changes": "Apply", "@sage/xtrem-ui/sidebar-apply-changes-and-create-new": "Apply and add new", "@sage/xtrem-ui/sidebar-mobile-apply-changes-and-create-new": "", "@sage/xtrem-ui/step-sequence-item-aria-complete": "Complete", "@sage/xtrem-ui/step-sequence-item-aria-count": "Step {{0}} of {{1}}", "@sage/xtrem-ui/step-sequence-item-aria-current": "Current", "@sage/xtrem-ui/sticker-actions-onload-unhandled-error": "Wystąpił nieznany błąd. Spróbuj ponownie.", "@sage/xtrem-ui/string-contains": "{{fieldTitle}} contains {{filterValue}}.", "@sage/xtrem-ui/string-ends-with": "{{fieldTitle}} ends with \"{{filterValue}}\"", "@sage/xtrem-ui/string-starts-with": "{{fieldTitle}} starts with \"{{filterValue}}\"", "@sage/xtrem-ui/sum": "Sum", "@sage/xtrem-ui/switch-off-caps": "OFF", "@sage/xtrem-ui/switch-on-caps": "ON", "@sage/xtrem-ui/table-addCurrentSelectionToFilter": "", "@sage/xtrem-ui/table-addToLabels": "", "@sage/xtrem-ui/table-addToValues": "", "@sage/xtrem-ui/table-advancedFilterAnd": "", "@sage/xtrem-ui/table-advancedFilterApply": "", "@sage/xtrem-ui/table-advancedFilterBlank": "", "@sage/xtrem-ui/table-advancedFilterBuilder": "", "@sage/xtrem-ui/table-advancedFilterBuilderAddButtonTooltip": "", "@sage/xtrem-ui/table-advancedFilterBuilderAddCondition": "", "@sage/xtrem-ui/table-advancedFilterBuilderAddJoin": "", "@sage/xtrem-ui/table-advancedFilterBuilderApply": "", "@sage/xtrem-ui/table-advancedFilterBuilderCancel": "", "@sage/xtrem-ui/table-advancedFilterBuilderEnterValue": "", "@sage/xtrem-ui/table-advancedFilterBuilderMoveDownButtonTooltip": "", "@sage/xtrem-ui/table-advancedFilterBuilderMoveUpButtonTooltip": "", "@sage/xtrem-ui/table-advancedFilterBuilderRemoveButtonTooltip": "", "@sage/xtrem-ui/table-advancedFilterBuilderSelectColumn": "", "@sage/xtrem-ui/table-advancedFilterBuilderSelectOption": "", "@sage/xtrem-ui/table-advancedFilterBuilderTitle": "", "@sage/xtrem-ui/table-advancedFilterBuilderValidationAlreadyApplied": "", "@sage/xtrem-ui/table-advancedFilterBuilderValidationEnterValue": "", "@sage/xtrem-ui/table-advancedFilterBuilderValidationIncomplete": "", "@sage/xtrem-ui/table-advancedFilterBuilderValidationSelectColumn": "", "@sage/xtrem-ui/table-advancedFilterBuilderValidationSelectOption": "", "@sage/xtrem-ui/table-advancedFilterContains": "", "@sage/xtrem-ui/table-advancedFilterEndsWith": "", "@sage/xtrem-ui/table-advancedFilterEquals": "", "@sage/xtrem-ui/table-advancedFilterFalse": "", "@sage/xtrem-ui/table-advancedFilterGreaterThan": "", "@sage/xtrem-ui/table-advancedFilterGreaterThanOrEqual": "", "@sage/xtrem-ui/table-advancedFilterLessThan": "", "@sage/xtrem-ui/table-advancedFilterLessThanOrEqual": "", "@sage/xtrem-ui/table-advancedFilterNotBlank": "", "@sage/xtrem-ui/table-advancedFilterNotContains": "", "@sage/xtrem-ui/table-advancedFilterNotEqual": "", "@sage/xtrem-ui/table-advancedFilterOr": "", "@sage/xtrem-ui/table-advancedFilterStartsWith": "", "@sage/xtrem-ui/table-advancedFilterTextEquals": "", "@sage/xtrem-ui/table-advancedFilterTextNotEqual": "", "@sage/xtrem-ui/table-advancedFilterTrue": "", "@sage/xtrem-ui/table-advancedFilterValidationExtraEndBracket": "", "@sage/xtrem-ui/table-advancedFilterValidationInvalidColumn": "", "@sage/xtrem-ui/table-advancedFilterValidationInvalidDate": "", "@sage/xtrem-ui/table-advancedFilterValidationInvalidJoinOperator": "", "@sage/xtrem-ui/table-advancedFilterValidationInvalidOption": "", "@sage/xtrem-ui/table-advancedFilterValidationJoinOperatorMismatch": "", "@sage/xtrem-ui/table-advancedFilterValidationMessage": "", "@sage/xtrem-ui/table-advancedFilterValidationMessageAtEnd": "", "@sage/xtrem-ui/table-advancedFilterValidationMissingColumn": "", "@sage/xtrem-ui/table-advancedFilterValidationMissingCondition": "", "@sage/xtrem-ui/table-advancedFilterValidationMissingEndBracket": "", "@sage/xtrem-ui/table-advancedFilterValidationMissingOption": "", "@sage/xtrem-ui/table-advancedFilterValidationMissingQuote": "", "@sage/xtrem-ui/table-advancedFilterValidationMissingValue": "", "@sage/xtrem-ui/table-advancedFilterValidationNotANumber": "", "@sage/xtrem-ui/table-advancedSettings": "", "@sage/xtrem-ui/table-after": "", "@sage/xtrem-ui/table-aggregate": "", "@sage/xtrem-ui/table-andCondition": "ORAZ", "@sage/xtrem-ui/table-animation": "", "@sage/xtrem-ui/table-applyFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-april": "", "@sage/xtrem-ui/table-area": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-areaChart": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-AreaColumnCombo": "", "@sage/xtrem-ui/table-areaColumnComboTooltip": "", "@sage/xtrem-ui/table-areaGroup": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderColumn": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderFilterItem": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderGroupItem": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderItem": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderItemValidation": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderJoinOperator": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderList": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderOption": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderValueP": "", "@sage/xtrem-ui/table-ariaAdvancedFilterInput": "", "@sage/xtrem-ui/table-ariaChartMenuClose": "", "@sage/xtrem-ui/table-ariaChartSelected": "", "@sage/xtrem-ui/table-ariaChecked": "", "@sage/xtrem-ui/table-ariaColumn": "", "@sage/xtrem-ui/table-ariaColumnFiltered": "", "@sage/xtrem-ui/table-ariaColumnGroup": "", "@sage/xtrem-ui/table-ariaColumnPanelList": "", "@sage/xtrem-ui/table-ariaColumnSelectAll": "", "@sage/xtrem-ui/table-ariaDateFilterInput": "", "@sage/xtrem-ui/table-ariaDefaultListName": "", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentAggFuncSeparator": "", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentDescription": "", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentSortAscending": "", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentSortDescending": "", "@sage/xtrem-ui/table-ariaDropZoneColumnGroupItemDescription": "", "@sage/xtrem-ui/table-ariaDropZoneColumnValueItemDescription": "", "@sage/xtrem-ui/table-ariaFilterColumn": "", "@sage/xtrem-ui/table-ariaFilterColumnsInput": "", "@sage/xtrem-ui/table-ariaFilterFromValue": "", "@sage/xtrem-ui/table-ariaFilteringOperator": "", "@sage/xtrem-ui/table-ariaFilterInput": "", "@sage/xtrem-ui/table-ariaFilterList": "", "@sage/xtrem-ui/table-ariaFilterMenuOpen": "", "@sage/xtrem-ui/table-ariaFilterPanelList": "", "@sage/xtrem-ui/table-ariaFilterToValue": "", "@sage/xtrem-ui/table-ariaFilterValue": "", "@sage/xtrem-ui/table-ariaHeaderSelection": "", "@sage/xtrem-ui/table-ariaHidden": "", "@sage/xtrem-ui/table-ariaIndeterminate": "", "@sage/xtrem-ui/table-ariaInputEditor": "", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterAutocomplete": "", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderAddField": "", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderColumnSelectField": "", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderJoinSelectField": "", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderOptionSelectField": "", "@sage/xtrem-ui/table-ariaLabelAggregationFunction": "", "@sage/xtrem-ui/table-ariaLabelCellEditor": "", "@sage/xtrem-ui/table-ariaLabelColumnFilter": "", "@sage/xtrem-ui/table-ariaLabelColumnMenu": "", "@sage/xtrem-ui/table-ariaLabelContextMenu": "", "@sage/xtrem-ui/table-ariaLabelDialog": "", "@sage/xtrem-ui/table-ariaLabelRichSelectDeleteSelection": "", "@sage/xtrem-ui/table-ariaLabelRichSelectDeselectAllItems": "", "@sage/xtrem-ui/table-ariaLabelRichSelectField": "", "@sage/xtrem-ui/table-ariaLabelRichSelectToggleSelection": "", "@sage/xtrem-ui/table-ariaLabelSelectField": "", "@sage/xtrem-ui/table-ariaLabelSubMenu": "", "@sage/xtrem-ui/table-ariaLabelTooltip": "", "@sage/xtrem-ui/table-ariaMenuColumn": "", "@sage/xtrem-ui/table-ariaPageSizeSelectorLabel": "", "@sage/xtrem-ui/table-ariaPivotDropZonePanelLabel": "", "@sage/xtrem-ui/table-ariaRowDeselect": "", "@sage/xtrem-ui/table-ariaRowGroupDropZonePanelLabel": "", "@sage/xtrem-ui/table-ariaRowSelect": "", "@sage/xtrem-ui/table-ariaRowSelectAll": "", "@sage/xtrem-ui/table-ariaRowSelectionDisabled": "", "@sage/xtrem-ui/table-ariaRowToggleSelection": "", "@sage/xtrem-ui/table-ariaSearch": "", "@sage/xtrem-ui/table-ariaSearchFilterValues": "", "@sage/xtrem-ui/table-ariaSkeletonCellLoading": "", "@sage/xtrem-ui/table-ariaSkeletonCellLoadingFailed": "", "@sage/xtrem-ui/table-ariaSortableColumn": "", "@sage/xtrem-ui/table-ariaToggleCellValue": "", "@sage/xtrem-ui/table-ariaToggleVisibility": "", "@sage/xtrem-ui/table-ariaUnchecked": "", "@sage/xtrem-ui/table-ariaValuesDropZonePanelLabel": "", "@sage/xtrem-ui/table-ariaVisible": "", "@sage/xtrem-ui/table-august": "", "@sage/xtrem-ui/table-automatic": "Automatyczne", "@sage/xtrem-ui/table-autoRotate": "", "@sage/xtrem-ui/table-autosizeAllColumns": "Autodopasowanie wszystkich kolumn", "@sage/xtrem-ui/table-autosizeThiscolumn": "Autodopasowanie tej kolumny", "@sage/xtrem-ui/table-avg": "Średnia", "@sage/xtrem-ui/table-axis": "<PERSON><PERSON>", "@sage/xtrem-ui/table-axisType": "", "@sage/xtrem-ui/table-background": "Tło", "@sage/xtrem-ui/table-bar": "", "@sage/xtrem-ui/table-barChart": "Słupkowy", "@sage/xtrem-ui/table-barGroup": "Słupkowy", "@sage/xtrem-ui/table-before": "", "@sage/xtrem-ui/table-blank": "", "@sage/xtrem-ui/table-blanks": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-blur": "Roz<PERSON><PERSON>", "@sage/xtrem-ui/table-bold": "Pogrubienie", "@sage/xtrem-ui/table-boldItalic": "<PERSON>gru<PERSON><PERSON>", "@sage/xtrem-ui/table-bottom": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-boxPlot": "", "@sage/xtrem-ui/table-boxPlotTooltip": "", "@sage/xtrem-ui/table-bubble": "Bąbelkowy", "@sage/xtrem-ui/table-bubbleTooltip": "Bąbelkowy", "@sage/xtrem-ui/table-calendar-view": "", "@sage/xtrem-ui/table-callout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-calloutLabels": "", "@sage/xtrem-ui/table-cancelFgroupFilterSelectilter": "", "@sage/xtrem-ui/table-cancelFilter": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-cap": "", "@sage/xtrem-ui/table-capLengthRatio": "", "@sage/xtrem-ui/table-categories": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-category": "Kategoria", "@sage/xtrem-ui/table-categoryAdd": "", "@sage/xtrem-ui/table-categoryValues": "", "@sage/xtrem-ui/table-chart": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-chartAdvancedSettings": "", "@sage/xtrem-ui/table-chartDownload": "", "@sage/xtrem-ui/table-chartDownloadToolbarTooltip": "", "@sage/xtrem-ui/table-chartEdit": "", "@sage/xtrem-ui/table-chartLink": "", "@sage/xtrem-ui/table-chartLinkToolbarTooltip": "", "@sage/xtrem-ui/table-chartMenuToolbarTooltip": "", "@sage/xtrem-ui/table-chartRange": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-chartSettingsToolbarTooltip": "", "@sage/xtrem-ui/table-chartStyle": "", "@sage/xtrem-ui/table-chartSubtitle": "", "@sage/xtrem-ui/table-chartTitle": "", "@sage/xtrem-ui/table-chartTitles": "", "@sage/xtrem-ui/table-chartUnlink": "", "@sage/xtrem-ui/table-chartUnlinkToolbarTooltip": "", "@sage/xtrem-ui/table-chooseColumns": "", "@sage/xtrem-ui/table-circle": "", "@sage/xtrem-ui/table-clearFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-collapseAll": "Zamknij wszystko", "@sage/xtrem-ui/table-color": "<PERSON><PERSON>", "@sage/xtrem-ui/table-column": "", "@sage/xtrem-ui/table-column-settings": "", "@sage/xtrem-ui/table-columnChart": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-columnChooser": "", "@sage/xtrem-ui/table-columnFilter": "", "@sage/xtrem-ui/table-columnGroup": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-columnLineCombo": "", "@sage/xtrem-ui/table-columnLineComboTooltip": "", "@sage/xtrem-ui/table-columns": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-columns-display-controller-columns": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-combinationChart": "", "@sage/xtrem-ui/table-combinationGroup": "", "@sage/xtrem-ui/table-compare-with-previous-period": "Porównaj z poprzednim okresem", "@sage/xtrem-ui/table-connectorLine": "", "@sage/xtrem-ui/table-contains": "Zawiera", "@sage/xtrem-ui/table-copy": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-copyWithGroupHeaders": "", "@sage/xtrem-ui/table-copyWithHeaders": "Kopiuj z nagłówkami", "@sage/xtrem-ui/table-count": "Liczba", "@sage/xtrem-ui/table-create": "", "@sage/xtrem-ui/table-cross": "", "@sage/xtrem-ui/table-crosshair": "", "@sage/xtrem-ui/table-crosshairLabel": "", "@sage/xtrem-ui/table-crosshairSnap": "", "@sage/xtrem-ui/table-csvExport": "Eksport CSV", "@sage/xtrem-ui/table-ctrlC": "Ctrl+C", "@sage/xtrem-ui/table-ctrlV": "Ctrl+V", "@sage/xtrem-ui/table-ctrlX": "", "@sage/xtrem-ui/table-customCombo": "", "@sage/xtrem-ui/table-customComboTooltip": "", "@sage/xtrem-ui/table-cut": "", "@sage/xtrem-ui/table-data": "<PERSON>", "@sage/xtrem-ui/table-dateFilter": "", "@sage/xtrem-ui/table-dateFormatOoo": "DD.MM.YYYY", "@sage/xtrem-ui/table-december": "", "@sage/xtrem-ui/table-decimalSeparator": "", "@sage/xtrem-ui/table-defaultCategory": "(Brak)", "@sage/xtrem-ui/table-diamond": "", "@sage/xtrem-ui/table-direction": "", "@sage/xtrem-ui/table-donut": "", "@sage/xtrem-ui/table-donutTooltip": "", "@sage/xtrem-ui/table-doughnut": "Pierś<PERSON>nio<PERSON>", "@sage/xtrem-ui/table-doughnutTooltip": "Pierś<PERSON>nio<PERSON>", "@sage/xtrem-ui/table-durationMillis": "", "@sage/xtrem-ui/table-empty": "", "@sage/xtrem-ui/table-enabled": "Włączony", "@sage/xtrem-ui/table-endAngle": "", "@sage/xtrem-ui/table-endsWith": "Kończy się na", "@sage/xtrem-ui/table-equals": "Równa się", "@sage/xtrem-ui/table-excelExport": "Eksport Excel (.xlsx)", "@sage/xtrem-ui/table-excelXmlExport": "Eksport Excel (.xml)", "@sage/xtrem-ui/table-expandAll": "Rozwiń wszystko", "@sage/xtrem-ui/table-export": "Eksport", "@sage/xtrem-ui/table-export-failed": "", "@sage/xtrem-ui/table-export-service-no-columns": "", "@sage/xtrem-ui/table-export-started": "", "@sage/xtrem-ui/table-false": "", "@sage/xtrem-ui/table-february": "", "@sage/xtrem-ui/table-fillOpacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wypełnienia", "@sage/xtrem-ui/table-filter-aria-label": "", "@sage/xtrem-ui/table-filteredRows": "Odfiltrowane", "@sage/xtrem-ui/table-filterOoo": "Filtruj...", "@sage/xtrem-ui/table-filters": "Filtry", "@sage/xtrem-ui/table-first": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-firstPage": "", "@sage/xtrem-ui/table-fixed": "", "@sage/xtrem-ui/table-font": "Czcionka", "@sage/xtrem-ui/table-footerTotal": "", "@sage/xtrem-ui/table-format": "Format", "@sage/xtrem-ui/table-greaterThan": "<PERSON><PERSON><PERSON><PERSON><PERSON>ż", "@sage/xtrem-ui/table-greaterThanOrEqual": "<PERSON><PERSON><PERSON><PERSON><PERSON> niż lub równe", "@sage/xtrem-ui/table-gridLines": "", "@sage/xtrem-ui/table-group": "Grupa", "@sage/xtrem-ui/table-group-total": "", "@sage/xtrem-ui/table-groupBy": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-groupedAreaTooltip": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-groupedBar": "Zgrupowany", "@sage/xtrem-ui/table-groupedBarFull": "", "@sage/xtrem-ui/table-groupedBarTooltip": "Zgrupowany", "@sage/xtrem-ui/table-groupedColumn": "Zgrupowany", "@sage/xtrem-ui/table-groupedColumnFull": "", "@sage/xtrem-ui/table-groupedColumnTooltip": "Zgrupowany", "@sage/xtrem-ui/table-groupedSeriesGroupType": "", "@sage/xtrem-ui/table-groupPadding": "", "@sage/xtrem-ui/table-groups": "<PERSON><PERSON><PERSON> w<PERSON>", "@sage/xtrem-ui/table-heart": "", "@sage/xtrem-ui/table-heatmap": "", "@sage/xtrem-ui/table-heatmapTooltip": "", "@sage/xtrem-ui/table-height": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-hierarchicalChart": "", "@sage/xtrem-ui/table-hierarchicalGroup": "", "@sage/xtrem-ui/table-histogram": "", "@sage/xtrem-ui/table-histogramBinCount": "Liczba przedziałów", "@sage/xtrem-ui/table-histogramChart": "Histogram", "@sage/xtrem-ui/table-histogramFrequency": "", "@sage/xtrem-ui/table-histogramGroup": "Histogram", "@sage/xtrem-ui/table-histogramTooltip": "Histogram", "@sage/xtrem-ui/table-horizontal": "", "@sage/xtrem-ui/table-horizontalAxisTitle": "", "@sage/xtrem-ui/table-innerRadius": "", "@sage/xtrem-ui/table-inRange": "W zakresie", "@sage/xtrem-ui/table-inRangeEnd": "", "@sage/xtrem-ui/table-inRangeStart": "", "@sage/xtrem-ui/table-inside": "", "@sage/xtrem-ui/table-invalidColor": "", "@sage/xtrem-ui/table-invalidDate": "", "@sage/xtrem-ui/table-invalidNumber": "", "@sage/xtrem-ui/table-italic": "Ku<PERSON>ywa", "@sage/xtrem-ui/table-itemPaddingX": "Dopełnienie elementu X", "@sage/xtrem-ui/table-itemPaddingY": "Dopełnienie elementu Y", "@sage/xtrem-ui/table-itemSpacing": "Odstęp między elementami", "@sage/xtrem-ui/table-january": "", "@sage/xtrem-ui/table-july": "", "@sage/xtrem-ui/table-june": "", "@sage/xtrem-ui/table-labelPlacement": "", "@sage/xtrem-ui/table-labelRotation": "", "@sage/xtrem-ui/table-labels": "Etykiety", "@sage/xtrem-ui/table-last": "Ostatnia", "@sage/xtrem-ui/table-lastPage": "", "@sage/xtrem-ui/table-layoutHorizontalSpacing": "Odstępy w poziomie", "@sage/xtrem-ui/table-layoutVerticalSpacing": "Odstępy w pionie", "@sage/xtrem-ui/table-left": "<PERSON>", "@sage/xtrem-ui/table-legend": "Podpis", "@sage/xtrem-ui/table-legendEnabled": "", "@sage/xtrem-ui/table-length": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-lessThan": "Mniejsze niż", "@sage/xtrem-ui/table-lessThanOrEqual": "Mniejsze niż lub równe", "@sage/xtrem-ui/table-line": "<PERSON><PERSON>", "@sage/xtrem-ui/table-line-number": "Line number", "@sage/xtrem-ui/table-lineChart": "", "@sage/xtrem-ui/table-lineDash": "", "@sage/xtrem-ui/table-lineDashOffset": "", "@sage/xtrem-ui/table-lineGroup": "<PERSON><PERSON>", "@sage/xtrem-ui/table-lineTooltip": "<PERSON><PERSON>", "@sage/xtrem-ui/table-lineWidth": "S<PERSON>okość linii", "@sage/xtrem-ui/table-loadingError": "", "@sage/xtrem-ui/table-loadingOoo": "Loading", "@sage/xtrem-ui/table-march": "", "@sage/xtrem-ui/table-markerPadding": "Dopełnienie znacznika", "@sage/xtrem-ui/table-markers": "Znaczniki", "@sage/xtrem-ui/table-markerSize": "Rozmiar znacznika", "@sage/xtrem-ui/table-markerStroke": "Pociągnięcie znacznika", "@sage/xtrem-ui/table-max": "Ma<PERSON>.", "@sage/xtrem-ui/table-maxSize": "<PERSON><PERSON><PERSON><PERSON><PERSON> rozmiar", "@sage/xtrem-ui/table-may": "", "@sage/xtrem-ui/table-min": "<PERSON>.", "@sage/xtrem-ui/table-miniChart": "", "@sage/xtrem-ui/table-minSize": "Minimalny rozmiar", "@sage/xtrem-ui/table-more": "more", "@sage/xtrem-ui/table-navigator": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-next": "<PERSON><PERSON>", "@sage/xtrem-ui/table-nextPage": "", "@sage/xtrem-ui/table-nightingale": "", "@sage/xtrem-ui/table-nightingaleTooltip": "", "@sage/xtrem-ui/table-noAggregation": "", "@sage/xtrem-ui/table-noDataToChart": "<PERSON><PERSON> dostępnych danych dla wykresu.", "@sage/xtrem-ui/table-noMatches": "Brak pasujących elementów", "@sage/xtrem-ui/table-none": "Brak", "@sage/xtrem-ui/table-noPin": "Brak przypięcia", "@sage/xtrem-ui/table-normal": "Standardowa", "@sage/xtrem-ui/table-normalizedArea": "100% skumulowany", "@sage/xtrem-ui/table-normalizedAreaFull": "", "@sage/xtrem-ui/table-normalizedAreaTooltip": "100% skumulowany", "@sage/xtrem-ui/table-normalizedBar": "100% skumulowany", "@sage/xtrem-ui/table-normalizedBarFull": "", "@sage/xtrem-ui/table-normalizedBarTooltip": "100% skumulowany", "@sage/xtrem-ui/table-normalizedColumn": "100% skumulowany", "@sage/xtrem-ui/table-normalizedColumnFull": "", "@sage/xtrem-ui/table-normalizedColumnTooltip": "100% skumulowany", "@sage/xtrem-ui/table-normalizedLine": "", "@sage/xtrem-ui/table-normalizedLineTooltip": "", "@sage/xtrem-ui/table-normalizedSeriesGroupType": "", "@sage/xtrem-ui/table-noRowsToShow": "Brak wierszy do pokazania", "@sage/xtrem-ui/table-notBlank": "", "@sage/xtrem-ui/table-notContains": "<PERSON><PERSON>", "@sage/xtrem-ui/table-notEqual": "<PERSON><PERSON> równa się", "@sage/xtrem-ui/table-november": "", "@sage/xtrem-ui/table-number": "Liczba", "@sage/xtrem-ui/table-numberFilter": "", "@sage/xtrem-ui/table-numeric-filter-greater-than-equals-value": "", "@sage/xtrem-ui/table-numeric-filter-greater-than-value": "", "@sage/xtrem-ui/table-numeric-filter-less-than-equals-value": "", "@sage/xtrem-ui/table-numeric-filter-less-than-value": "", "@sage/xtrem-ui/table-numeric-filter-not-value": "", "@sage/xtrem-ui/table-numeric-filter-range-value": "", "@sage/xtrem-ui/table-october": "", "@sage/xtrem-ui/table-of": "z", "@sage/xtrem-ui/table-offset": "Przesunię<PERSON>", "@sage/xtrem-ui/table-offsets": "Przesunięcia", "@sage/xtrem-ui/table-open-column-panel": "Open column panel", "@sage/xtrem-ui/table-orCondition": "LUB", "@sage/xtrem-ui/table-orientation": "", "@sage/xtrem-ui/table-outside": "", "@sage/xtrem-ui/table-padding": "Dopełnienie", "@sage/xtrem-ui/table-page": "Strona", "@sage/xtrem-ui/table-pageLastRowUnknown": "", "@sage/xtrem-ui/table-pageSizeSelectorLabel": "", "@sage/xtrem-ui/table-paired": "<PERSON><PERSON> sparow<PERSON>", "@sage/xtrem-ui/table-parallel": "", "@sage/xtrem-ui/table-paste": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-perpendicular": "", "@sage/xtrem-ui/table-pie": "Kołowy", "@sage/xtrem-ui/table-pieChart": "Kołowy", "@sage/xtrem-ui/table-pieGroup": "Kołowy", "@sage/xtrem-ui/table-pieTooltip": "Kołowy", "@sage/xtrem-ui/table-pinColumn": "Przypnij kolumnę", "@sage/xtrem-ui/table-pinLeft": "Przypnij po lewej", "@sage/xtrem-ui/table-pinRight": "Przypnij po prawej", "@sage/xtrem-ui/table-pivotChart": "Wykres przestawny", "@sage/xtrem-ui/table-pivotChartAndPivotMode": "Wykres przestawny i tryb przestawny", "@sage/xtrem-ui/table-pivotChartRequiresPivotMode": "Wykres przestawny wymaga włączenia trybu przestawnego.", "@sage/xtrem-ui/table-pivotChartTitle": "Wykres przestawny", "@sage/xtrem-ui/table-pivotColumnGroupTotals": "", "@sage/xtrem-ui/table-pivotColumnsEmptyMessage": "<PERSON>rz<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>, aby us<PERSON><PERSON><PERSON> etykiety kolumn", "@sage/xtrem-ui/table-pivotMode": "<PERSON><PERSON> przestawny", "@sage/xtrem-ui/table-pivots": "Etykiety kolumn", "@sage/xtrem-ui/table-plus": "", "@sage/xtrem-ui/table-polarAxis": "", "@sage/xtrem-ui/table-polarAxisTitle": "", "@sage/xtrem-ui/table-polarChart": "", "@sage/xtrem-ui/table-polarGroup": "", "@sage/xtrem-ui/table-polygon": "", "@sage/xtrem-ui/table-position": "<PERSON><PERSON><PERSON>ja", "@sage/xtrem-ui/table-positionRatio": "", "@sage/xtrem-ui/table-predefined": "Wstępnie zdefiniowane", "@sage/xtrem-ui/table-preferredLength": "", "@sage/xtrem-ui/table-previous": "Poprzednia", "@sage/xtrem-ui/table-previousPage": "", "@sage/xtrem-ui/table-print": "", "@sage/xtrem-ui/table-radarArea": "", "@sage/xtrem-ui/table-radarAreaTooltip": "", "@sage/xtrem-ui/table-radarLine": "", "@sage/xtrem-ui/table-radarLineTooltip": "", "@sage/xtrem-ui/table-radialBar": "", "@sage/xtrem-ui/table-radialBarTooltip": "", "@sage/xtrem-ui/table-radialColumn": "", "@sage/xtrem-ui/table-radialColumnTooltip": "", "@sage/xtrem-ui/table-radiusAxis": "", "@sage/xtrem-ui/table-radiusAxisPosition": "", "@sage/xtrem-ui/table-rangeArea": "", "@sage/xtrem-ui/table-rangeAreaTooltip": "", "@sage/xtrem-ui/table-rangeBar": "", "@sage/xtrem-ui/table-rangeBarTooltip": "", "@sage/xtrem-ui/table-rangeChartTitle": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-removeFromLabels": "", "@sage/xtrem-ui/table-removeFromValues": "", "@sage/xtrem-ui/table-resetColumns": "<PERSON><PERSON><PERSON><PERSON> kol<PERSON>", "@sage/xtrem-ui/table-resetFilter": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-reverseDirection": "", "@sage/xtrem-ui/table-right": "Do prawej", "@sage/xtrem-ui/table-rowDragRow": "", "@sage/xtrem-ui/table-rowDragRows": "", "@sage/xtrem-ui/table-rowGroupColumnsEmptyMessage": "Przec<PERSON><PERSON><PERSON><PERSON> tuta<PERSON>, aby us<PERSON><PERSON><PERSON> grupy wierszy", "@sage/xtrem-ui/table-scatter": "Punktowy", "@sage/xtrem-ui/table-scatterGroup": "X Y (punktowy)", "@sage/xtrem-ui/table-scatterTooltip": "Punktowy", "@sage/xtrem-ui/table-scrollingStep": "", "@sage/xtrem-ui/table-scrollingZoom": "", "@sage/xtrem-ui/table-searchOoo": "Wyszukaj...", "@sage/xtrem-ui/table-secondaryAxis": "", "@sage/xtrem-ui/table-sectorLabels": "", "@sage/xtrem-ui/table-select-all": "Zaznacz wszystko", "@sage/xtrem-ui/table-selectAll": "Zaznacz wszystko", "@sage/xtrem-ui/table-selectAllSearchResults": "Zaznacz wszystkie wyniki", "@sage/xtrem-ui/table-selectedRows": "Wy<PERSON><PERSON>", "@sage/xtrem-ui/table-selectingZoom": "", "@sage/xtrem-ui/table-september": "", "@sage/xtrem-ui/table-series": "Serie", "@sage/xtrem-ui/table-seriesAdd": "", "@sage/xtrem-ui/table-seriesChartType": "", "@sage/xtrem-ui/table-seriesGroupType": "", "@sage/xtrem-ui/table-seriesItemLabels": "", "@sage/xtrem-ui/table-seriesItemNegative": "", "@sage/xtrem-ui/table-seriesItemPositive": "", "@sage/xtrem-ui/table-seriesItems": "", "@sage/xtrem-ui/table-seriesItemType": "", "@sage/xtrem-ui/table-seriesLabels": "", "@sage/xtrem-ui/table-seriesPadding": "", "@sage/xtrem-ui/table-seriesType": "", "@sage/xtrem-ui/table-setFilter": "", "@sage/xtrem-ui/table-settings": "Ustawienia", "@sage/xtrem-ui/table-shadow": "Cień", "@sage/xtrem-ui/table-shape": "", "@sage/xtrem-ui/table-show": "", "@sage/xtrem-ui/table-size": "Size", "@sage/xtrem-ui/table-sortAscending": "", "@sage/xtrem-ui/table-sortDescending": "", "@sage/xtrem-ui/table-sortUnSort": "", "@sage/xtrem-ui/table-spacing": "Odstępy", "@sage/xtrem-ui/table-specializedChart": "", "@sage/xtrem-ui/table-specializedGroup": "", "@sage/xtrem-ui/table-square": "", "@sage/xtrem-ui/table-stackedArea": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-stackedAreaFull": "", "@sage/xtrem-ui/table-stackedAreaTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-stackedBar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-stackedBarFull": "", "@sage/xtrem-ui/table-stackedBarTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-stackedColumn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-stackedColumnFull": "", "@sage/xtrem-ui/table-stackedColumnTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-stackedLine": "", "@sage/xtrem-ui/table-stackedLineTooltip": "", "@sage/xtrem-ui/table-stackedSeriesGroupType": "", "@sage/xtrem-ui/table-startAngle": "", "@sage/xtrem-ui/table-startsWith": "Zaczyna się od", "@sage/xtrem-ui/table-statisticalChart": "", "@sage/xtrem-ui/table-statisticalGroup": "", "@sage/xtrem-ui/table-strokeColor": "", "@sage/xtrem-ui/table-strokeOpacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> linii", "@sage/xtrem-ui/table-strokeWidth": "Szerokość pociągnięcia", "@sage/xtrem-ui/table-sum": "<PERSON><PERSON>", "@sage/xtrem-ui/table-summary-empty-default-text": "Brak danych do wyświetlenia.", "@sage/xtrem-ui/table-sunburst": "", "@sage/xtrem-ui/table-sunburstTooltip": "", "@sage/xtrem-ui/table-switch-to-card-view": "Przełącz na widok karty", "@sage/xtrem-ui/table-switch-to-table-view": "Przełącz na widok tabeli", "@sage/xtrem-ui/table-switchCategorySeries": "", "@sage/xtrem-ui/table-table-view": "", "@sage/xtrem-ui/table-textFilter": "", "@sage/xtrem-ui/table-thickness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-thousandSeparator": "", "@sage/xtrem-ui/table-ticks": "Znaczniki", "@sage/xtrem-ui/table-tile": "", "@sage/xtrem-ui/table-time": "Czas", "@sage/xtrem-ui/table-timeFormat": "", "@sage/xtrem-ui/table-timeFormatDashesYYYYMMDD": "", "@sage/xtrem-ui/table-timeFormatDotsDDMYY": "", "@sage/xtrem-ui/table-timeFormatDotsMDDYY": "", "@sage/xtrem-ui/table-timeFormatHHMMSS": "", "@sage/xtrem-ui/table-timeFormatHHMMSSAmPm": "", "@sage/xtrem-ui/table-timeFormatSlashesDDMMYY": "", "@sage/xtrem-ui/table-timeFormatSlashesDDMMYYYY": "", "@sage/xtrem-ui/table-timeFormatSlashesMMDDYY": "", "@sage/xtrem-ui/table-timeFormatSlashesMMDDYYYY": "", "@sage/xtrem-ui/table-timeFormatSpacesDDMMMMYYYY": "", "@sage/xtrem-ui/table-title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-titlePlaceholder": "Tytuł wykresu: k<PERSON><PERSON><PERSON>, a<PERSON>.", "@sage/xtrem-ui/table-to": "to", "@sage/xtrem-ui/table-tooltips": "Etykietki narzędzi", "@sage/xtrem-ui/table-top": "Góra", "@sage/xtrem-ui/table-totalAndFilteredRows": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-totalRows": "<PERSON><PERSON>", "@sage/xtrem-ui/table-treemap": "", "@sage/xtrem-ui/table-treemapTooltip": "", "@sage/xtrem-ui/table-triangle": "", "@sage/xtrem-ui/table-true": "", "@sage/xtrem-ui/table-ungroupAll": "", "@sage/xtrem-ui/table-ungroupBy": "Rozgrup<PERSON><PERSON>", "@sage/xtrem-ui/table-valueAggregation": "Agrega<PERSON><PERSON>", "@sage/xtrem-ui/table-valueColumnsEmptyMessage": "Przeci<PERSON><PERSON><PERSON> tutaj, aby zagregować", "@sage/xtrem-ui/table-values": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-variant_card": "Widok karty", "@sage/xtrem-ui/table-variant-area": "Widok obszaru", "@sage/xtrem-ui/table-variant-table": "Widok tabeli", "@sage/xtrem-ui/table-vertical": "", "@sage/xtrem-ui/table-verticalAxisTitle": "", "@sage/xtrem-ui/table-views-close-button": "", "@sage/xtrem-ui/table-views-default": "", "@sage/xtrem-ui/table-views-manage": "", "@sage/xtrem-ui/table-views-open-button": "", "@sage/xtrem-ui/table-views-save": "", "@sage/xtrem-ui/table-views-save-as": "", "@sage/xtrem-ui/table-views-save-failed": "", "@sage/xtrem-ui/table-views-saved": "", "@sage/xtrem-ui/table-views-select": "", "@sage/xtrem-ui/table-waterfall": "", "@sage/xtrem-ui/table-waterfallTooltip": "", "@sage/xtrem-ui/table-weight": "", "@sage/xtrem-ui/table-whisker": "", "@sage/xtrem-ui/table-width": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-xAxis": "", "@sage/xtrem-ui/table-xOffset": "Przesunięcie X", "@sage/xtrem-ui/table-xRotation": "Obrót X", "@sage/xtrem-ui/table-xType": "Typ X", "@sage/xtrem-ui/table-xyChart": "X Y (punktowy)", "@sage/xtrem-ui/table-xyValues": "Wartości X Y", "@sage/xtrem-ui/table-yAxis": "", "@sage/xtrem-ui/table-yOffset": "Przesunięcie Y", "@sage/xtrem-ui/table-yRotation": "Obrót Y", "@sage/xtrem-ui/table-zoom": "", "@sage/xtrem-ui/text-editor-cancel": "", "@sage/xtrem-ui/text-editor-cancel-button": "", "@sage/xtrem-ui/text-editor-character-counter": "", "@sage/xtrem-ui/text-editor-character-limit": "", "@sage/xtrem-ui/text-editor-content-editor": "", "@sage/xtrem-ui/text-editor-save": "", "@sage/xtrem-ui/text-editor-save-button": "", "@sage/xtrem-ui/text-editor-toolbar": "", "@sage/xtrem-ui/text-format-capital-bold": "Pogrubienie", "@sage/xtrem-ui/text-format-capital-bullet-list": "Lista punktowana", "@sage/xtrem-ui/text-format-capital-italic": "Ku<PERSON>ywa", "@sage/xtrem-ui/text-format-capital-number-list": "Lista numerowana", "@sage/xtrem-ui/toggle-navigation-panel": "Toggle navigation panel", "@sage/xtrem-ui/toggle-widget-list": "Toggle widget list", "@sage/xtrem-ui/true": "True", "@sage/xtrem-ui/tunnel-already-open-description": "", "@sage/xtrem-ui/tunnel-already-open-title": "", "@sage/xtrem-ui/tunnel-link-see-more": "", "@sage/xtrem-ui/tunnel-record-not-suitable-description": "", "@sage/xtrem-ui/tunnel-record-not-suitable-title": "", "@sage/xtrem-ui/unexpected-error-page-load": "Podczas ładowania strony wystąpił błąd. Spróbuj ponownie.", "@sage/xtrem-ui/ungroup": "Ungroup", "@sage/xtrem-ui/unhandled-event-handler-error": "Wystąpił nieznany błąd. Spróbuj ponownie.", "@sage/xtrem-ui/unsaved-changes-content": "Opuść i odrzuć zmiany?", "@sage/xtrem-ui/unsaved-changes-go-back": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/unsaved-changes-title": "Niezapisane zmiany", "@sage/xtrem-ui/unsaved-changes-yes": "Discard", "@sage/xtrem-ui/update-error": "Podczas aktualizacji wystąpił błąd {{0}}.", "@sage/xtrem-ui/upload-in-progress": "Uploading file", "@sage/xtrem-ui/validation-error-and-more": "i 1 błąd więcej", "@sage/xtrem-ui/validation-error-of-children": "Błąd zatwierdzania w podrekordach", "@sage/xtrem-ui/validation-error-total": "1 błąd", "@sage/xtrem-ui/validation-error-with-number-grid": "You have 1 error in the following grid: {{0}}.", "@sage/xtrem-ui/validation-error-with-number-pod": "You have 1 error in the following collection: {{0}}.", "@sage/xtrem-ui/validation-errors": "Błędy zatwierdzania", "@sage/xtrem-ui/validation-errors-and-more": "i {{0}} błędów więcej", "@sage/xtrem-ui/validation-errors-number": "Liczba błędów w tym wierszu", "@sage/xtrem-ui/validation-errors-total": "{{0}} błędów", "@sage/xtrem-ui/validation-errors-unknown": "Unknown validation error", "@sage/xtrem-ui/validation-errors-with-number-grid": "You have {{0}} errors in the following grid: {{1}}.", "@sage/xtrem-ui/validation-errors-with-number-pod": "You have {{0}} errors in the following collection: {{1}}.", "@sage/xtrem-ui/visual-process-lookup-page-dialog-title": "Select a page", "@sage/xtrem-ui/visual-process-lookup-page-path-column": "Page path", "@sage/xtrem-ui/visual-process-lookup-page-title-column": "Page title", "@sage/xtrem-ui/visual-process-transactions-not-supported": "Page transactions are not supported.", "@sage/xtrem-ui/widget-action-create": "Create", "@sage/xtrem-ui/widget-action-create-help": "Add a shortcut to create a new record.", "@sage/xtrem-ui/widget-action-see-all": "See all", "@sage/xtrem-ui/widget-action-see-all-help": "View records based on your data selection.", "@sage/xtrem-ui/widget-actions": "Actions", "@sage/xtrem-ui/widget-editor-action-decimal-digits-mandatory": "You need to enter a number.", "@sage/xtrem-ui/widget-editor-action-goes-to": "Goes to", "@sage/xtrem-ui/widget-editor-action-label-mandatory": "You need to enter a title.", "@sage/xtrem-ui/widget-editor-action-page-path": "Path", "@sage/xtrem-ui/widget-editor-action-page-title": "Title", "@sage/xtrem-ui/widget-editor-add": "Add", "@sage/xtrem-ui/widget-editor-basic-step-missing-node": "You need to add a node", "@sage/xtrem-ui/widget-editor-basic-step-missing-title": "You need to add a title", "@sage/xtrem-ui/widget-editor-basic-step-title": "{{stepIndex}}. Select a widget to get started.", "@sage/xtrem-ui/widget-editor-cancel-edit": "Cancel", "@sage/xtrem-ui/widget-editor-content-formatting": "", "@sage/xtrem-ui/widget-editor-content-step-subtitle": "Select a field for grouping and fields to use as filters.", "@sage/xtrem-ui/widget-editor-content-step-title": "{{stepIndex}}. Add your content.", "@sage/xtrem-ui/widget-editor-content-step-vertical-axes": "Vertical axes", "@sage/xtrem-ui/widget-editor-data-step-title": "{{stepIndex}}. Select the data to add to your widget.", "@sage/xtrem-ui/widget-editor-entry-node": "Entry module", "@sage/xtrem-ui/widget-editor-filter-step-title": "{{stepIndex}}. Add your filters.", "@sage/xtrem-ui/widget-editor-grouping-method": "Grouping method", "@sage/xtrem-ui/widget-editor-grouping-property": "Grouping property", "@sage/xtrem-ui/widget-editor-horizontal-axis": "Horizontal axis", "@sage/xtrem-ui/widget-editor-horizontal-axis-label": "", "@sage/xtrem-ui/widget-editor-icon": "Icon", "@sage/xtrem-ui/widget-editor-layout-step-no-actions-in-preview": "Actions do not work in the preview. They work when you add the widget to your dashboard.", "@sage/xtrem-ui/widget-editor-layout-step-title": "{{stepIndex}}. Create your layout.", "@sage/xtrem-ui/widget-editor-max-num-of-values": "Maximum number of values", "@sage/xtrem-ui/widget-editor-sorting-step-title": "{{stepIndex}}. Define sorting.", "@sage/xtrem-ui/widget-editor-step-basic-title": "Widget", "@sage/xtrem-ui/widget-editor-step-content-title": "Content", "@sage/xtrem-ui/widget-editor-step-data-title": "Data", "@sage/xtrem-ui/widget-editor-step-filters-title": "Filters", "@sage/xtrem-ui/widget-editor-step-layout-title": "Layout", "@sage/xtrem-ui/widget-editor-step-sorting-title": "Sorting", "@sage/xtrem-ui/widget-editor-subtitle": "Subtitle", "@sage/xtrem-ui/widget-editor-title-edit": "Edit widget", "@sage/xtrem-ui/widget-editor-title-new": "New widget", "@sage/xtrem-ui/widget-editor-type-bar-chart": "Bar chart", "@sage/xtrem-ui/widget-editor-type-bar-chart-description": "Display data visually with bars.", "@sage/xtrem-ui/widget-editor-type-indicator-tile": "Indicator", "@sage/xtrem-ui/widget-editor-type-indicator-tile-description": "Display major record updates.", "@sage/xtrem-ui/widget-editor-type-line-chart": "Line chart", "@sage/xtrem-ui/widget-editor-type-line-chart-description": "Display data visually with lines.", "@sage/xtrem-ui/widget-editor-type-table": "Table", "@sage/xtrem-ui/widget-editor-type-table-description": "Display data in rows and columns.", "@sage/xtrem-ui/widget-editor-update": "Update", "@sage/xtrem-ui/widget-editor-vertical-axis-label": "", "@sage/xtrem-ui/widget-editor-widget-category": "Widget category", "@sage/xtrem-ui/widget-editor-widget-title": "Widget title", "@sage/xtrem-ui/widget-preview-label": "", "@sage/xtrem-ui/wizard-finish": "Finish", "@sage/xtrem-ui/wizard-next": "Next", "@sage/xtrem-ui/wizard-previous": "Previous", "@sage/xtrem-ui/workflow-add-trigger-event": "", "@sage/xtrem-ui/workflow-collapse": "", "@sage/xtrem-ui/workflow-component-add-action": "", "@sage/xtrem-ui/workflow-component-add-condition": "", "@sage/xtrem-ui/workflow-component-add-step": "", "@sage/xtrem-ui/workflow-component-edge-false-path": "", "@sage/xtrem-ui/workflow-component-edge-true-path": "", "@sage/xtrem-ui/workflow-component-header-label-action": "", "@sage/xtrem-ui/workflow-component-header-label-condition": "", "@sage/xtrem-ui/workflow-component-header-label-start": "", "@sage/xtrem-ui/workflow-component-wizard-event-selection": "", "@sage/xtrem-ui/workflow-component-wizard-step-configuration": "", "@sage/xtrem-ui/workflow-component-wizard-title-action": "", "@sage/xtrem-ui/workflow-component-wizard-title-trigger": "", "@sage/xtrem-ui/workflow-component-wizard-trigger-selection": "", "@sage/xtrem-ui/workflow-delete-node-chain-message": "", "@sage/xtrem-ui/workflow-delete-node-chain-title": "", "@sage/xtrem-ui/workflow-empty": "", "@sage/xtrem-ui/workflow-expand": "", "@sage/xtrem-ui/workflow-fit-view": "", "@sage/xtrem-ui/workflow-redo": "", "@sage/xtrem-ui/workflow-undo": "", "@sage/xtrem-ui/workflow-zoom-in": "", "@sage/xtrem-ui/workflow-zoom-out": "", "@sage/xtrem-ui/yes": "Tak"}