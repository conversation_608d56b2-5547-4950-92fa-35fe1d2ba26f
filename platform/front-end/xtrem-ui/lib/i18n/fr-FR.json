{"@sage/xtrem-ui/360-view": "Vue 360", "@sage/xtrem-ui/action-button-more": "Plus d'options", "@sage/xtrem-ui/action-clone": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/action-close": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/action-delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/action-edit": "Modifier", "@sage/xtrem-ui/action-share": "Partager", "@sage/xtrem-ui/action-tool-bar-items-selected": "Enregistrements sélectionnés : {{count}}", "@sage/xtrem-ui/actions": "Actions", "@sage/xtrem-ui/add-column": "Ajouter colonne", "@sage/xtrem-ui/add-filter": "Ajouter filtre", "@sage/xtrem-ui/add-item": "<PERSON><PERSON><PERSON> ligne", "@sage/xtrem-ui/add-item-in-line": "<PERSON><PERSON><PERSON> ligne", "@sage/xtrem-ui/add-item-in-line-using-sidebar": "Ajouter ligne dans volet", "@sage/xtrem-ui/add-value": "Ajouter valeur", "@sage/xtrem-ui/aggregation-method": "Méthode de regroupement", "@sage/xtrem-ui/async-mutation-dialog-content": "Le processus est plus long que prévu. Vous pouvez revenir plus tard et réessayer sans pour autant perdre votre travail. Vous pouvez attendre et laisser le processus se dérouler en tâche de fond.", "@sage/xtrem-ui/async-mutation-dialog-title": "Votre opération est toujours en cours.", "@sage/xtrem-ui/async-operation-error": "Une erreur s'est produite lors du traitement de l'opération.", "@sage/xtrem-ui/attachment-options-menu-all": "Tous types de fichiers", "@sage/xtrem-ui/attachment-options-menu-documents": "Documents", "@sage/xtrem-ui/attachment-options-menu-images": "Images", "@sage/xtrem-ui/attachment-options-menu-others": "Autres", "@sage/xtrem-ui/attachments": "Pièces jointes", "@sage/xtrem-ui/average": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/axes": "Axes", "@sage/xtrem-ui/bar-code-component-not-available": "Le code-barres n'est pas disponible.", "@sage/xtrem-ui/between": "<PERSON><PERSON>", "@sage/xtrem-ui/bulk-action-async-export": "Exporter", "@sage/xtrem-ui/bulk-action-dialog-content": "<PERSON>uez cette action sur les enregistrements sélectionnés : {{itemCount}}", "@sage/xtrem-ui/bulk-action-error": "L'action n'a pas démarré. Essayez de nouveau.", "@sage/xtrem-ui/bulk-action-print": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/bulk-action-started": "Action démarrée sur les enregistrements sélectionnés.", "@sage/xtrem-ui/bulk-actions-bar-selected": "Enregistrements sélectionnés : {{count}}", "@sage/xtrem-ui/business-action-in-progress": "Ce processus se déroule en tâche de fond.", "@sage/xtrem-ui/calendar-month": "<PERSON><PERSON>", "@sage/xtrem-ui/calendar-view-3-day": "3 jours", "@sage/xtrem-ui/calendar-view-day": "Jour", "@sage/xtrem-ui/calendar-view-month": "<PERSON><PERSON>", "@sage/xtrem-ui/calendar-view-week": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/calendar-view-year": "<PERSON><PERSON>", "@sage/xtrem-ui/cancel": "Annuler", "@sage/xtrem-ui/carbon-date-format": "dd/MM/yyyy", "@sage/xtrem-ui/chart-component-no-data": "<PERSON><PERSON><PERSON> donn<PERSON> trouvée", "@sage/xtrem-ui/clear-filter-text": "Effacer le texte du filtre", "@sage/xtrem-ui/clear-floating-filter": "Supprimer tous les filtres", "@sage/xtrem-ui/clear-input-text": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/clear-selection": "Annuler la sélection", "@sage/xtrem-ui/close-full-screen": "<PERSON><PERSON><PERSON> le plein écran", "@sage/xtrem-ui/close-header-section": "<PERSON><PERSON><PERSON> l'en-tête", "@sage/xtrem-ui/close-record": "<PERSON><PERSON><PERSON> la fiche", "@sage/xtrem-ui/collapse-section": "Réduire la section", "@sage/xtrem-ui/collection-data-service-more-errors": "et {{0}} plus d'erreurs.", "@sage/xtrem-ui/consumer-mock-clear-path": "Nettoyer l'URL", "@sage/xtrem-ui/consumer-mock-hide-test-ids": "Masque les codes", "@sage/xtrem-ui/consumer-mock-show-test-ids": "Afficher les codes", "@sage/xtrem-ui/contains": "Contient", "@sage/xtrem-ui/continue": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/copy-error-detail": "<PERSON><PERSON><PERSON> les détails d'erreur", "@sage/xtrem-ui/copy-error-details": "<PERSON><PERSON><PERSON> les détails d'erreur", "@sage/xtrem-ui/create-a-new-view": "<PERSON><PERSON>er un nouvelle vue", "@sage/xtrem-ui/create-a-view": "<PERSON><PERSON><PERSON> une vue", "@sage/xtrem-ui/create-error": "L'erreur {{0}} s'est produite lors de la création.", "@sage/xtrem-ui/create-new-dashboard": "+ <PERSON><PERSON><PERSON>", "@sage/xtrem-ui/crud-cancel": "Annuler", "@sage/xtrem-ui/crud-confirm": "Confirmer", "@sage/xtrem-ui/crud-create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/crud-create-success": "Enregistrement créé", "@sage/xtrem-ui/crud-delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/crud-delete-record-button": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/crud-delete-record-warning-message": "Vous allez supprimer cet enregistrement.", "@sage/xtrem-ui/crud-delete-record-warning-title": "Confirmer la <PERSON>", "@sage/xtrem-ui/crud-delete-successfully": "Enregistrement supprimé", "@sage/xtrem-ui/crud-duplicate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/crud-duplicate-dialog-subtitle": "Remplissez les champs ci-dessous pour dupliquer cet enregistrement.", "@sage/xtrem-ui/crud-duplicate-dialog-title": "Dupliquez l'enregistrement.", "@sage/xtrem-ui/crud-duplicate-failed": "La fiche n'a pas été dupliquée.", "@sage/xtrem-ui/crud-duplicate-successfully": "L'enregistrement a été dupliqué avec succès.", "@sage/xtrem-ui/crud-save": "Enregistrer", "@sage/xtrem-ui/crud-save-failed": "L'enregistrement a échoué.", "@sage/xtrem-ui/crud-update-success": "Enregistrement mis à jour", "@sage/xtrem-ui/dashboard-actions": "Actions", "@sage/xtrem-ui/dashboard-add_contact": "Ajouter contact", "@sage/xtrem-ui/dashboard-add_note": "Ajouter note", "@sage/xtrem-ui/dashboard-add_site": "A<PERSON><PERSON> adresse", "@sage/xtrem-ui/dashboard-add-widget": "Ajouter widget", "@sage/xtrem-ui/dashboard-address": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-address_name": "Nom d'adresse", "@sage/xtrem-ui/dashboard-addresses": "Adresses", "@sage/xtrem-ui/dashboard-cancel": "Annuler", "@sage/xtrem-ui/dashboard-choose_date": "Choisir une autre borne de dates", "@sage/xtrem-ui/dashboard-clear_filter": "Supprimer filtre", "@sage/xtrem-ui/dashboard-contact": "Contact", "@sage/xtrem-ui/dashboard-contact_card_empty_text": "Ce widget ne contient pas encore de contacts.", "@sage/xtrem-ui/dashboard-contact_image": "Image du contact", "@sage/xtrem-ui/dashboard-contact_type": "Type de contact", "@sage/xtrem-ui/dashboard-contacts": "Contacts", "@sage/xtrem-ui/dashboard-create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-create-dashboard": "<PERSON><PERSON>er un tableau de bord", "@sage/xtrem-ui/dashboard-current_date_filter": "Filtre de date actuel :", "@sage/xtrem-ui/dashboard-day": "Jour", "@sage/xtrem-ui/dashboard-delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-delete-dashboard-dialog-message": "Confirmer la suppression du tableau de bord ?", "@sage/xtrem-ui/dashboard-delete-dashboard-dialog-title": "Supp<PERSON>er le tableau de bord", "@sage/xtrem-ui/dashboard-deleted": "Tableau de bord supprimé", "@sage/xtrem-ui/dashboard-duplicate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-duplicated": "Tableau de bord <PERSON><PERSON>", "@sage/xtrem-ui/dashboard-edit": "Modifier", "@sage/xtrem-ui/dashboard-editor-add-widget": "Ajouter", "@sage/xtrem-ui/dashboard-editor-all-widgets": "Tous les widgets", "@sage/xtrem-ui/dashboard-editor-cancel-edit": "Annuler", "@sage/xtrem-ui/dashboard-editor-create-dialog-blank-description": "Débutez avec un modèle vide et ajoutez les widgets dont vous avez besoin.", "@sage/xtrem-ui/dashboard-editor-create-dialog-blank-title": "<PERSON><PERSON><PERSON><PERSON> vierge", "@sage/xtrem-ui/dashboard-editor-create-dialog-description": "Sélectionnez un modèle pour débuter ou concevoir votre propre tableau de bord. Vous pouvez personnaliser un tableau de bord en ajoutant ou retirant des widgets.", "@sage/xtrem-ui/dashboard-editor-create-dialog-title": "Sélectionner un modèle de tableau de bord", "@sage/xtrem-ui/dashboard-editor-edit-title": "Modifier l'intitulé", "@sage/xtrem-ui/dashboard-editor-redo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-editor-save": "Enregistrer", "@sage/xtrem-ui/dashboard-editor-save-error": "Le tableau de bord n'a pas été enregistré.", "@sage/xtrem-ui/dashboard-editor-saved-successfully": "Tableau de bord enregistré.", "@sage/xtrem-ui/dashboard-editor-title": "Éditeur du tableau de bord", "@sage/xtrem-ui/dashboard-editor-undo": "Annuler", "@sage/xtrem-ui/dashboard-editor-widget-edit": "Modifier", "@sage/xtrem-ui/dashboard-editor-widget-list-add": "<PERSON><PERSON><PERSON> un widget", "@sage/xtrem-ui/dashboard-editor-widget-list-title": "Widgets", "@sage/xtrem-ui/dashboard-email": "E-mail", "@sage/xtrem-ui/dashboard-empty-heading": "Ce tableau de bord est vide.", "@sage/xtrem-ui/dashboard-empty-subtitle": "Ajouter un widget pour personnaliser votre tableau de bord.", "@sage/xtrem-ui/dashboard-expand_row": "<PERSON>é<PERSON><PERSON> ligne", "@sage/xtrem-ui/dashboard-failed-to-create": "Le tableau de bord n'a pas été créé.", "@sage/xtrem-ui/dashboard-failed-to-delete": "Le tableau de bord n'a pas été supprimé.", "@sage/xtrem-ui/dashboard-failed-to-duplicate": "Le tableau de bord n'a pas été dupliqué.", "@sage/xtrem-ui/dashboard-month": "<PERSON><PERSON>", "@sage/xtrem-ui/dashboard-month_1": "<PERSON><PERSON>", "@sage/xtrem-ui/dashboard-month_10": "Octobre", "@sage/xtrem-ui/dashboard-month_11": "Novembre", "@sage/xtrem-ui/dashboard-month_12": "Décembre", "@sage/xtrem-ui/dashboard-month_2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-month_3": "Mars", "@sage/xtrem-ui/dashboard-month_4": "Avril", "@sage/xtrem-ui/dashboard-month_5": "<PERSON>", "@sage/xtrem-ui/dashboard-month_6": "Juin", "@sage/xtrem-ui/dashboard-month_7": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-month_8": "Août", "@sage/xtrem-ui/dashboard-month_9": "Septembre", "@sage/xtrem-ui/dashboard-more_options": "Plus d'actions", "@sage/xtrem-ui/dashboard-next": "Suivant", "@sage/xtrem-ui/dashboard-next_period": "Sélectionner la période suivante", "@sage/xtrem-ui/dashboard-no-change": "Pas de modification", "@sage/xtrem-ui/dashboard-note": "Note", "@sage/xtrem-ui/dashboard-notes": "Notes", "@sage/xtrem-ui/dashboard-phone_number": "Numéro de téléphone", "@sage/xtrem-ui/dashboard-position": "Position", "@sage/xtrem-ui/dashboard-previous": "Précédent", "@sage/xtrem-ui/dashboard-previous_period": "Sélectionner la période précédente", "@sage/xtrem-ui/dashboard-quarter": "Trimestre", "@sage/xtrem-ui/dashboard-reload": "Actualiser", "@sage/xtrem-ui/dashboard-save": "Enregistrer", "@sage/xtrem-ui/dashboard-scroll_left": "Défiler vers la gauche", "@sage/xtrem-ui/dashboard-scroll_right": "Défiler vers la droite", "@sage/xtrem-ui/dashboard-select_address": "Sé<PERSON><PERSON><PERSON> adresse", "@sage/xtrem-ui/dashboard-select_contact": "Sélectionner contact", "@sage/xtrem-ui/dashboard-settings": "", "@sage/xtrem-ui/dashboard-site": "Site", "@sage/xtrem-ui/dashboard-site_card_empty_text": "Ce widget ne contient pas encore d'adresses.", "@sage/xtrem-ui/dashboard-table_filter_menu": "Menu filtre", "@sage/xtrem-ui/dashboard-table_select": "Sélectionner ligne", "@sage/xtrem-ui/dashboard-view_switch": "Changement vue", "@sage/xtrem-ui/dashboard-week": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-widget-category-others": "Autres", "@sage/xtrem-ui/dashboard-widget-close": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-widget-refresh": "Actualiser", "@sage/xtrem-ui/dashboard-widget-settings": "Paramétrage", "@sage/xtrem-ui/dashboard-year": "<PERSON><PERSON>", "@sage/xtrem-ui/date-error-day-range": "Borne de jours de 1 à 31", "@sage/xtrem-ui/date-error-month-range": "Borne de mois de 1 à 12", "@sage/xtrem-ui/date-error-year-range": "Borne d'années de 1800 à 2200", "@sage/xtrem-ui/date-format": "", "@sage/xtrem-ui/date-format-separator": "/", "@sage/xtrem-ui/date-time-component-aria-label": "Sélectionner date et heure", "@sage/xtrem-ui/date-time-range-end-date": "Fin", "@sage/xtrem-ui/date-time-range-start-date": "D<PERSON>but", "@sage/xtrem-ui/date-time-range-time": "<PERSON><PERSON>", "@sage/xtrem-ui/date-time-range-time-zone": "<PERSON><PERSON> horaire :", "@sage/xtrem-ui/datetime-aria-label": "Date et heure", "@sage/xtrem-ui/datetime-range-aria-label": "Borne de date et d'heure", "@sage/xtrem-ui/datetime-range-end-date-error": "<PERSON><PERSON> <PERSON><PERSON> renseigner une date de fin postérieure à la date de début.", "@sage/xtrem-ui/default-view-cannot-be-edited": "Impossible de mettre à jour la vue par défaut", "@sage/xtrem-ui/delete-error": "L'erreur {{0}} s'est produite lors de la <PERSON>.", "@sage/xtrem-ui/delete-record-warning-message": "Vous êtes sur le point de supprimer cet enregistrement. Confirmer ?", "@sage/xtrem-ui/delete-record-warning-title": "Confirmer la <PERSON>", "@sage/xtrem-ui/detailed-icon-name-accounting": "Comptabilité", "@sage/xtrem-ui/detailed-icon-name-addons": "Add-ons", "@sage/xtrem-ui/detailed-icon-name-animal": "Animal", "@sage/xtrem-ui/detailed-icon-name-apple": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-asset_mgt": "Gestion des biens", "@sage/xtrem-ui/detailed-icon-name-award": "Récompense", "@sage/xtrem-ui/detailed-icon-name-bag": "Sac", "@sage/xtrem-ui/detailed-icon-name-bakery": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-barcode": "Code-barres", "@sage/xtrem-ui/detailed-icon-name-bicycle": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-binocular": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-book": "Livre", "@sage/xtrem-ui/detailed-icon-name-bright": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-building": "Bâtiment", "@sage/xtrem-ui/detailed-icon-name-calculator": "Calculatrice", "@sage/xtrem-ui/detailed-icon-name-calendar": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-camera": "Appareil photo", "@sage/xtrem-ui/detailed-icon-name-card": "Fiche", "@sage/xtrem-ui/detailed-icon-name-cart": "Chariot", "@sage/xtrem-ui/detailed-icon-name-certificate": "Certificat", "@sage/xtrem-ui/detailed-icon-name-check": "Contr<PERSON>le", "@sage/xtrem-ui/detailed-icon-name-checkbox": "Case à cocher", "@sage/xtrem-ui/detailed-icon-name-checklist": "Liste de vérification", "@sage/xtrem-ui/detailed-icon-name-chemical": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-chess": "Échecs", "@sage/xtrem-ui/detailed-icon-name-click": "C<PERSON>r", "@sage/xtrem-ui/detailed-icon-name-clock": "Horlog<PERSON>", "@sage/xtrem-ui/detailed-icon-name-close": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-clothes": "Vêtements", "@sage/xtrem-ui/detailed-icon-name-cloud": "Nuage", "@sage/xtrem-ui/detailed-icon-name-coffee": "Café", "@sage/xtrem-ui/detailed-icon-name-compass": "Compas", "@sage/xtrem-ui/detailed-icon-name-connected": "Connection", "@sage/xtrem-ui/detailed-icon-name-consultant": "Consultant", "@sage/xtrem-ui/detailed-icon-name-conversation": "Conversation", "@sage/xtrem-ui/detailed-icon-name-cooking": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-cpu": "UC", "@sage/xtrem-ui/detailed-icon-name-crowd": "Groupe", "@sage/xtrem-ui/detailed-icon-name-crown": "Couronne", "@sage/xtrem-ui/detailed-icon-name-data": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-database": "Base de données", "@sage/xtrem-ui/detailed-icon-name-decline": "Baisse", "@sage/xtrem-ui/detailed-icon-name-desktop": "Bureau", "@sage/xtrem-ui/detailed-icon-name-devices": "Appareils", "@sage/xtrem-ui/detailed-icon-name-dollar": "Dollar", "@sage/xtrem-ui/detailed-icon-name-download": "Télécharger", "@sage/xtrem-ui/detailed-icon-name-ear": "Or<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-ecomm": "E-commerce", "@sage/xtrem-ui/detailed-icon-name-euro": "Euro", "@sage/xtrem-ui/detailed-icon-name-excavator": "Excavateur", "@sage/xtrem-ui/detailed-icon-name-eye": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-factory": "Livrée", "@sage/xtrem-ui/detailed-icon-name-favorite": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-filter": "Filtre", "@sage/xtrem-ui/detailed-icon-name-financials": "Comptabilité", "@sage/xtrem-ui/detailed-icon-name-flag": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-folder": "Dossier", "@sage/xtrem-ui/detailed-icon-name-food": "Alimentaire", "@sage/xtrem-ui/detailed-icon-name-form": "Fiche", "@sage/xtrem-ui/detailed-icon-name-gauge": "Jauge", "@sage/xtrem-ui/detailed-icon-name-gears": "Engrenages", "@sage/xtrem-ui/detailed-icon-name-glasses": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-globe": "Globe", "@sage/xtrem-ui/detailed-icon-name-green": "<PERSON>ert", "@sage/xtrem-ui/detailed-icon-name-handshake": "Poignée de main", "@sage/xtrem-ui/detailed-icon-name-happy": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-heart": "Coeur", "@sage/xtrem-ui/detailed-icon-name-hide": "Masquer", "@sage/xtrem-ui/detailed-icon-name-holiday": "Vacances", "@sage/xtrem-ui/detailed-icon-name-home": "Accueil", "@sage/xtrem-ui/detailed-icon-name-hourglass": "Sablier", "@sage/xtrem-ui/detailed-icon-name-hub": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-idea": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-incline": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-industry": "Industrie", "@sage/xtrem-ui/detailed-icon-name-info": "Infos", "@sage/xtrem-ui/detailed-icon-name-integration": "Intégration", "@sage/xtrem-ui/detailed-icon-name-jewelry": "Bijoux", "@sage/xtrem-ui/detailed-icon-name-keys": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-lab": "Lab", "@sage/xtrem-ui/detailed-icon-name-label": "Libellé", "@sage/xtrem-ui/detailed-icon-name-laptop": "PC portable", "@sage/xtrem-ui/detailed-icon-name-lightning": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-like": "Like", "@sage/xtrem-ui/detailed-icon-name-link": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-locations": "Emplacements", "@sage/xtrem-ui/detailed-icon-name-lock": "Verrouiller", "@sage/xtrem-ui/detailed-icon-name-lock_unlocked": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-mail": "E-mail", "@sage/xtrem-ui/detailed-icon-name-map": "Mapping", "@sage/xtrem-ui/detailed-icon-name-medical": "Médical", "@sage/xtrem-ui/detailed-icon-name-megaphone": "Mégaphone", "@sage/xtrem-ui/detailed-icon-name-memo": "Mémo", "@sage/xtrem-ui/detailed-icon-name-microphone": "Microphone", "@sage/xtrem-ui/detailed-icon-name-minus": "<PERSON>ins", "@sage/xtrem-ui/detailed-icon-name-mouse": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-newspaper": "Journal", "@sage/xtrem-ui/detailed-icon-name-note": "Note", "@sage/xtrem-ui/detailed-icon-name-notebook": "Carnet de notes", "@sage/xtrem-ui/detailed-icon-name-office": "Bureau", "@sage/xtrem-ui/detailed-icon-name-page": "Page", "@sage/xtrem-ui/detailed-icon-name-payment": "Règlement", "@sage/xtrem-ui/detailed-icon-name-payroll": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-pen": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-pencil": "Crayon", "@sage/xtrem-ui/detailed-icon-name-person": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-phone": "Téléphone", "@sage/xtrem-ui/detailed-icon-name-pin": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-plus": "Plus", "@sage/xtrem-ui/detailed-icon-name-point": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-pound": "Livre", "@sage/xtrem-ui/detailed-icon-name-power": "Allumer", "@sage/xtrem-ui/detailed-icon-name-presentation": "Présentation", "@sage/xtrem-ui/detailed-icon-name-print": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-processing": "Opérations", "@sage/xtrem-ui/detailed-icon-name-puzzle": "Puzzle", "@sage/xtrem-ui/detailed-icon-name-question": "Question", "@sage/xtrem-ui/detailed-icon-name-receipts": "Réceptions", "@sage/xtrem-ui/detailed-icon-name-recycle": "Recycler", "@sage/xtrem-ui/detailed-icon-name-redo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-remote": "A distance", "@sage/xtrem-ui/detailed-icon-name-rocket": "Fusée", "@sage/xtrem-ui/detailed-icon-name-safe": "Coffre-fort", "@sage/xtrem-ui/detailed-icon-name-satelite": "Satellite", "@sage/xtrem-ui/detailed-icon-name-savings": "Économies", "@sage/xtrem-ui/detailed-icon-name-scissors": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-server": "Ser<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-service": "Service", "@sage/xtrem-ui/detailed-icon-name-setting": "Paramétrage", "@sage/xtrem-ui/detailed-icon-name-share": "Partager", "@sage/xtrem-ui/detailed-icon-name-shoes": "Chaussures", "@sage/xtrem-ui/detailed-icon-name-shuffle": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-sign": "<PERSON>s", "@sage/xtrem-ui/detailed-icon-name-sim": "Carte SIM", "@sage/xtrem-ui/detailed-icon-name-smartphone": "Smartphone", "@sage/xtrem-ui/detailed-icon-name-stationeries": "Papeterie", "@sage/xtrem-ui/detailed-icon-name-store": "Ma<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-support": "Support", "@sage/xtrem-ui/detailed-icon-name-sync": "Synchroniser", "@sage/xtrem-ui/detailed-icon-name-tab": "Onglet", "@sage/xtrem-ui/detailed-icon-name-table": "Table", "@sage/xtrem-ui/detailed-icon-name-tablet": "Tablette", "@sage/xtrem-ui/detailed-icon-name-thermometer": "Thermomètre", "@sage/xtrem-ui/detailed-icon-name-timer": "Chronomètre", "@sage/xtrem-ui/detailed-icon-name-tools": "Outils", "@sage/xtrem-ui/detailed-icon-name-travel": "Voyage", "@sage/xtrem-ui/detailed-icon-name-truck": "Camion", "@sage/xtrem-ui/detailed-icon-name-undo": "Annuler", "@sage/xtrem-ui/detailed-icon-name-video": "Vidéo", "@sage/xtrem-ui/detailed-icon-name-wallet": "Portefeuille", "@sage/xtrem-ui/detailed-icon-name-warehouse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-warning": "Avertissement", "@sage/xtrem-ui/detailed-icon-name-weather": "Temps", "@sage/xtrem-ui/detailed-icon-name-wireless": "Sans fil", "@sage/xtrem-ui/detailed-icon-name-wrench": "Clé", "@sage/xtrem-ui/detailed-icon-name-writing": "Écriture", "@sage/xtrem-ui/dialog-actions-onload-unhandled-error": "Une erreur inconnue s'est produite. Réessayer.", "@sage/xtrem-ui/dialog-loading": "Chargement en cours", "@sage/xtrem-ui/dialogs-async-loader-button-keep-waiting": "Encore quelques instants", "@sage/xtrem-ui/dialogs-async-loader-button-notify-me": "M'informer", "@sage/xtrem-ui/dialogs-async-loader-button-stop": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dialogs-async-loader-waiting": "Cette action peut durer encore quelques instants ...", "@sage/xtrem-ui/dirty-phantom-row-validation-error-message-mac": "Sélectionnez la ligne et validez-la en utilisant command + Enter afin d'enregistrer le document.", "@sage/xtrem-ui/dirty-phantom-row-validation-error-message-pc": "Sélectionnez la ligne et validez-la en utilisant Ctrl + Enter afin d'enregistrer le document.", "@sage/xtrem-ui/display-errors-back-to-full-display": "Affichage complet", "@sage/xtrem-ui/display-errors-back-to-full-display-tooltip": "Revenir à l'affichage complet", "@sage/xtrem-ui/display-errors-button": "Afficher les erreurs", "@sage/xtrem-ui/distinct-count": "Comptage séparé", "@sage/xtrem-ui/divisor": "Diviseur", "@sage/xtrem-ui/divisor-helper-text": "<PERSON><PERSON><PERSON><PERSON>z les nombres volumineux en renseignant un diviseur.", "@sage/xtrem-ui/document-metadata": "Métadonnées document", "@sage/xtrem-ui/drag-drop-file": "Glissez-déplacez vos fichiers ici.", "@sage/xtrem-ui/duplicate-error": "Erreur de duplication : {{0}}", "@sage/xtrem-ui/empty-state-filter-text-no-results-button": "Supprimer filtres", "@sage/xtrem-ui/empty-state-filter-text-no-results-description": "Essayez de définir des critères différents ou créez un nouvel enregistrement pour débuter.", "@sage/xtrem-ui/empty-state-filter-text-title": "Aucun résultat trouvé", "@sage/xtrem-ui/empty-state-text": "Cette liste est vide.", "@sage/xtrem-ui/endsWith": "Se termine par", "@sage/xtrem-ui/equals": "Est égal à", "@sage/xtrem-ui/error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/error-detail-copied-to-clipboard": "Détails de l'erreur copiés dans le presse-papier", "@sage/xtrem-ui/error-loading-stickers": "Une erreur s'est produite lors du chargement des autocollants. Réessayer.", "@sage/xtrem-ui/export-format-csv": "CSV", "@sage/xtrem-ui/export-format-excel": "Excel", "@sage/xtrem-ui/failed-to-refresh-navigation-panel": "Échec de rafraîchissement du volet de navigation.", "@sage/xtrem-ui/failed-to-save": "L'enregistrement a échoué.", "@sage/xtrem-ui/false": "Faux", "@sage/xtrem-ui/field-mandatory": "Renseignez une valeur.", "@sage/xtrem-ui/field-max-items-value": "{{0}} : le nombre maximum d'éléments sélectionnables est {{1}}.", "@sage/xtrem-ui/field-maximum-date-value": "La date maximum est {{1}}.", "@sage/xtrem-ui/field-maximum-length-value": "La longueur maximum est {{1}}.", "@sage/xtrem-ui/field-maximum-value": "La valeur maximum est {{1}}.", "@sage/xtrem-ui/field-min-items-value": "{{0}} : le nombre minimum d'éléments sélectionnables est {{1}}.", "@sage/xtrem-ui/field-minimum-date-value": "La date minimum est {{1}}.", "@sage/xtrem-ui/field-minimum-length-value": "La longueur minimum est {{1}}.", "@sage/xtrem-ui/field-minimum-value": "La valeur minimum est {{1}}.", "@sage/xtrem-ui/field-non-zero": "{{0}} ne peut pas valoir z<PERSON>ro.", "@sage/xtrem-ui/field-not-valid": "<PERSON><PERSON> invalide", "@sage/xtrem-ui/field-select-mandatory": "Sélectionnez une valeur.", "@sage/xtrem-ui/field-select-mandatory-checkbox": "Sélectionnez cette case à cocher.", "@sage/xtrem-ui/field-select-mandatory-or-enter": "Sélectionnez ou renseignez une valeur.", "@sage/xtrem-ui/file-component-browse-file": "<PERSON><PERSON> au fi<PERSON>er", "@sage/xtrem-ui/file-component-browse-files": "Parcourir les fichiers", "@sage/xtrem-ui/file-component-drag-drop": "ou \n Glissez-dé<PERSON>z votre fichier ici.", "@sage/xtrem-ui/file-component-drag-drop-empty": "ou \n Cette liste n'a pas encore de fichiers joints. \n Glissez-déplacez votre fichier ici.", "@sage/xtrem-ui/file-component-hide-upload-area": "Masquer la zone de téléchargement", "@sage/xtrem-ui/file-component-onchange-error": "Erreur de chargement du fichier : {{0}}.", "@sage/xtrem-ui/file-component-show-upload-area": "Afficher la zone de téléchargement", "@sage/xtrem-ui/file-upload-failed": "Le téléchargement du fichier a échoué.", "@sage/xtrem-ui/filter-manager-apply-button": "Appliquer", "@sage/xtrem-ui/filter-manager-cancel-button": "Annuler", "@sage/xtrem-ui/filter-manager-checkbox": "Actif", "@sage/xtrem-ui/filter-manager-clear-all-button": "Tout effacer", "@sage/xtrem-ui/filter-manager-close": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/filter-manager-invalid-filters": "Valeur de filtre invalide", "@sage/xtrem-ui/filter-manager-max-value": "Valeur max", "@sage/xtrem-ui/filter-manager-min-value": "Valeur mini", "@sage/xtrem-ui/filter-manager-open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/filter-manager-range-end": "Date de fin", "@sage/xtrem-ui/filter-manager-range-start": "Date de début", "@sage/xtrem-ui/filter-manager-title-header": "Filtres", "@sage/xtrem-ui/filter-range": "{{fieldTitle}} se trouve entre {{value1}} et {{value2}}.", "@sage/xtrem-ui/filter-search-placeholder": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/filter-select-lookup-dialog-dialog-title": "Sélection", "@sage/xtrem-ui/filter-select-lookup-dialog-failed-fetch": "Options introuvables", "@sage/xtrem-ui/filter-type": "Type de filtre", "@sage/xtrem-ui/filter-value": "<PERSON>ur de filtre", "@sage/xtrem-ui/filter-value-equals": "{{fieldTitle}} a pour valeur {{filterValue}}.", "@sage/xtrem-ui/filter-value-greater-than-equal": "{{fieldTitle}} doit avoir au moins pour valeur {{filterValue}}.", "@sage/xtrem-ui/filter-value-less-than-equal": "{{fieldTitle}} ne doit pas dépasser {{filterValue}}.", "@sage/xtrem-ui/filter-value-not-equal": "{{fieldTitle}} != {{filterValue}}", "@sage/xtrem-ui/floating-filter-label": "<PERSON><PERSON> filtre {{ filterName }}", "@sage/xtrem-ui/footer-actions-more-button": "Plus", "@sage/xtrem-ui/form-designer-var-current-date": "Date courante", "@sage/xtrem-ui/form-designer-var-generation-by": "Généré par", "@sage/xtrem-ui/form-designer-var-page-number": "Numéro de <PERSON>", "@sage/xtrem-ui/form-designer-var-parameter": "Paramètre - {{name}}", "@sage/xtrem-ui/form-designer-var-total-number-of-pages": "Nombre total de pages", "@sage/xtrem-ui/general_invalid_json": "JSON invalide", "@sage/xtrem-ui/general-finish-editing": "Terminer <PERSON>", "@sage/xtrem-ui/general-welcome-page": "Page d'accueil", "@sage/xtrem-ui/generic-no": "Non", "@sage/xtrem-ui/generic-yes": "O<PERSON>", "@sage/xtrem-ui/greaterThan": "Sup<PERSON>ur à", "@sage/xtrem-ui/greaterThanOrEqual": "Su<PERSON><PERSON><PERSON> ou égal à", "@sage/xtrem-ui/group-by": "Regrouper par", "@sage/xtrem-ui/group-by-day": "Jour", "@sage/xtrem-ui/group-by-month": "<PERSON><PERSON>", "@sage/xtrem-ui/group-by-this-column": "Grouper selon cette colonne", "@sage/xtrem-ui/group-by-year": "<PERSON><PERSON>", "@sage/xtrem-ui/helper-text-contains": "Contient", "@sage/xtrem-ui/helper-text-ends-with": "Se termine par", "@sage/xtrem-ui/helper-text-matches": "Correspond à", "@sage/xtrem-ui/helper-text-starts-with": "Commence par", "@sage/xtrem-ui/hide-floating-filters": "Masquer les filtres tables", "@sage/xtrem-ui/hide-technical-details": "Masquer détails techniques", "@sage/xtrem-ui/hours": "<PERSON><PERSON>", "@sage/xtrem-ui/image-component-add-image": "Ajouter une image", "@sage/xtrem-ui/image-component-not-available": "Image indisponible", "@sage/xtrem-ui/image-field-remove-image": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/infinite-indicator-end": "Pas de date de fin", "@sage/xtrem-ui/infinite-indicator-start": "Pas de date de début", "@sage/xtrem-ui/insight-plural": "{{insightCount}} insights", "@sage/xtrem-ui/insight-singular": "1 insight", "@sage/xtrem-ui/invalid-boolean": "Bool<PERSON><PERSON> invalide", "@sage/xtrem-ui/invalid-date": "Date invalide", "@sage/xtrem-ui/invalid-file-type-message": "{{0}} n'est pas autorisé pour des raisons de sécurité.", "@sage/xtrem-ui/invalid-integer": "<PERSON>tier invalide", "@sage/xtrem-ui/invalid-number": "Numéro invalide", "@sage/xtrem-ui/invalid-range": "<PERSON><PERSON> invalide", "@sage/xtrem-ui/invalid-response-no-data": "Réponse invalide : aucune donn<PERSON>", "@sage/xtrem-ui/invalid-value": "<PERSON><PERSON> invalide", "@sage/xtrem-ui/Label": "Libellé", "@sage/xtrem-ui/last-30-days": "Les 30 derniers jours", "@sage/xtrem-ui/last-7-days": "Les 7 derniers jours", "@sage/xtrem-ui/last-thirty-days": "Les 30 derniers jours", "@sage/xtrem-ui/lessThan": "Inférieur à", "@sage/xtrem-ui/lessThanOrEqual": "Inférieur ou égal à", "@sage/xtrem-ui/link-error-numbers-tooltip": "Détail des erreurs", "@sage/xtrem-ui/link-error-quantity": "1 erreur", "@sage/xtrem-ui/link-errors-quantity": "{{0}} erreurs", "@sage/xtrem-ui/list-printing": "Sélectionner l'édition", "@sage/xtrem-ui/list-printing-assignment": "Attribuer l'édition", "@sage/xtrem-ui/lookup-dialog-confirm-select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/lookup-dialog-create-new-item": "Créer nouvel enregistrement", "@sage/xtrem-ui/lookup-dialog-dialog-title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/lookup-dialog-failed-fetch": "Échec de chargement des options", "@sage/xtrem-ui/main-list-refresh": "Actualiser", "@sage/xtrem-ui/matches": "Correspond à", "@sage/xtrem-ui/maximum": "Maximum", "@sage/xtrem-ui/message-type-ai": "IA", "@sage/xtrem-ui/message-type-error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/message-type-info": "Informations", "@sage/xtrem-ui/message-type-information": "Informations", "@sage/xtrem-ui/message-type-success": "Su<PERSON>ès", "@sage/xtrem-ui/message-type-warning": "Avertissement", "@sage/xtrem-ui/mime-type/application/atom+xml": "Fil XML Atom", "@sage/xtrem-ui/mime-type/application/ecmascript": "ECMAScript", "@sage/xtrem-ui/mime-type/application/font-woff": "Web Open Font Format", "@sage/xtrem-ui/mime-type/application/font-woff2": "Web Open Font Format 2", "@sage/xtrem-ui/mime-type/application/graphql": "Requête GraphQL", "@sage/xtrem-ui/mime-type/application/java-archive": "Archive Java", "@sage/xtrem-ui/mime-type/application/javascript": "JavaScript", "@sage/xtrem-ui/mime-type/application/json": "JSON", "@sage/xtrem-ui/mime-type/application/ld+json": "Document JSON-LD", "@sage/xtrem-ui/mime-type/application/mac-binhex40": "Archive BinHex", "@sage/xtrem-ui/mime-type/application/mathml+xml": "Document MathML", "@sage/xtrem-ui/mime-type/application/ms-visio": "Document MS Visio", "@sage/xtrem-ui/mime-type/application/ms-visio.stencil.macroEnabled.12": "Document MS Visio avec macros", "@sage/xtrem-ui/mime-type/application/ms-visio.template": "Modèle MS Visio", "@sage/xtrem-ui/mime-type/application/msword": "Document MS Word", "@sage/xtrem-ui/mime-type/application/octet-stream": "Données binaires", "@sage/xtrem-ui/mime-type/application/pdf": "Document PDF", "@sage/xtrem-ui/mime-type/application/postscript": "PostScript", "@sage/xtrem-ui/mime-type/application/rdf+xml": "Document RDF", "@sage/xtrem-ui/mime-type/application/rss+xml": "Fil RSS XML", "@sage/xtrem-ui/mime-type/application/rtf": "Format de texte enrichi", "@sage/xtrem-ui/mime-type/application/sql": "Base de données SQL", "@sage/xtrem-ui/mime-type/application/vnd.amazon.ebook": "eBook Kindle Amazon", "@sage/xtrem-ui/mime-type/application/vnd.android.package-archive": "APK Android", "@sage/xtrem-ui/mime-type/application/vnd.apple.installer+xml": "Package d'installation Apple", "@sage/xtrem-ui/mime-type/application/vnd.apple.keynote": "Apple Keynote", "@sage/xtrem-ui/mime-type/application/vnd.apple.numbers": "Apple Numbers", "@sage/xtrem-ui/mime-type/application/vnd.apple.pages": "Apple Pages", "@sage/xtrem-ui/mime-type/application/vnd.google-earth.kml+xml": "Google Earth KML", "@sage/xtrem-ui/mime-type/application/vnd.google-earth.kmz": "Google Earth KMZ", "@sage/xtrem-ui/mime-type/application/vnd.mozilla.xul+xml": "Mozilla XUL", "@sage/xtrem-ui/mime-type/application/vnd.ms-access": "Microsoft Access Database", "@sage/xtrem-ui/mime-type/application/vnd.ms-excel": "Document MS Excel", "@sage/xtrem-ui/mime-type/application/vnd.ms-fontobject": "Embedded OpenType Font", "@sage/xtrem-ui/mime-type/application/vnd.ms-powerpoint": "Document MS PowerPoint", "@sage/xtrem-ui/mime-type/application/vnd.ms-project": "Document Microsoft Project", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.graphics": "Graphiques OpenDocument", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.presentation": "Présentation OpenDocument", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.spreadsheet": "Tableir OpenDocument", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.text": "Texte OpenDocument", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.presentationml.presentation": "Document MS PowerPoint", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "Document MS Excel", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.wordprocessingml.document": "Document MS Word", "@sage/xtrem-ui/mime-type/application/vnd.rn-realmedia": "RealMedia", "@sage/xtrem-ui/mime-type/application/vnd.wap.wmlc": "Document WMLC", "@sage/xtrem-ui/mime-type/application/x-7z-compressed": "Archive 7z", "@sage/xtrem-ui/mime-type/application/x-abiword": "Document AbiWord", "@sage/xtrem-ui/mime-type/application/x-bzip": "Archive Bzip", "@sage/xtrem-ui/mime-type/application/x-bzip2": "Archive Bzip2", "@sage/xtrem-ui/mime-type/application/x-cd-image": "Image CD", "@sage/xtrem-ui/mime-type/application/x-chrome-extension": "Extension Chrome", "@sage/xtrem-ui/mime-type/application/x-cocoa": "Archive Cocoa", "@sage/xtrem-ui/mime-type/application/x-csh": "Script C Shell", "@sage/xtrem-ui/mime-type/application/x-deb": "<PERSON><PERSON>", "@sage/xtrem-ui/mime-type/application/x-dvi": "Document DVI", "@sage/xtrem-ui/mime-type/application/x-font-opentype": "OpenType Font", "@sage/xtrem-ui/mime-type/application/x-font-otf": "OpenType Font", "@sage/xtrem-ui/mime-type/application/x-font-ttf": "TrueType Font", "@sage/xtrem-ui/mime-type/application/x-font-woff": "Web Open Font Format", "@sage/xtrem-ui/mime-type/application/x-font-woff2": "Web Open Font Format 2", "@sage/xtrem-ui/mime-type/application/x-gtar": "Archive GNU Tar", "@sage/xtrem-ui/mime-type/application/x-gzip": "Archive GZIP", "@sage/xtrem-ui/mime-type/application/x-hdf": "Hierarchical Data Format (HDF)", "@sage/xtrem-ui/mime-type/application/x-httpd-php": "Script PHP", "@sage/xtrem-ui/mime-type/application/x-httpd-php-source": "Code source PHP", "@sage/xtrem-ui/mime-type/application/x-java-applet": "Java Applet", "@sage/xtrem-ui/mime-type/application/x-java-archive": "Archive Java (JAR)", "@sage/xtrem-ui/mime-type/application/x-java-archive-diff": "Diff Java Archive", "@sage/xtrem-ui/mime-type/application/x-java-jnlp-file": "Fichier JNLP", "@sage/xtrem-ui/mime-type/application/x-javascript": "Ancien JavaScript", "@sage/xtrem-ui/mime-type/application/x-latex": "Document LaTeX", "@sage/xtrem-ui/mime-type/application/x-lzh-compressed": "Archive LZH", "@sage/xtrem-ui/mime-type/application/x-makeself": "Archive Makeself", "@sage/xtrem-ui/mime-type/application/x-mif": "Format FrameMaker Interchange", "@sage/xtrem-ui/mime-type/application/x-msaccess": "Base de données MS Access", "@sage/xtrem-ui/mime-type/application/x-msdownload": "Téléchargement MS", "@sage/xtrem-ui/mime-type/application/x-msmetafile": "Windows Metafile", "@sage/xtrem-ui/mime-type/application/x-msmoney": "MS Money", "@sage/xtrem-ui/mime-type/application/x-perl": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/mime-type/application/x-pilot": "Archive Pilot", "@sage/xtrem-ui/mime-type/application/x-rar-compressed": "Archive RAR", "@sage/xtrem-ui/mime-type/application/x-redhat-package-manager": "Paquet RedHat", "@sage/xtrem-ui/mime-type/application/x-rpm": "Paquet RPM", "@sage/xtrem-ui/mime-type/application/x-sea": "Archive Sea", "@sage/xtrem-ui/mime-type/application/x-sh": "Script Bash Shell", "@sage/xtrem-ui/mime-type/application/x-shockwave-flash": "Animation Flash", "@sage/xtrem-ui/mime-type/application/x-sql": "Base de données SQL", "@sage/xtrem-ui/mime-type/application/x-stuffit": "Archive StuffIt", "@sage/xtrem-ui/mime-type/application/x-tar": "Archive TAR", "@sage/xtrem-ui/mime-type/application/x-tcl": "Script Tcl", "@sage/xtrem-ui/mime-type/application/x-tex": "Document TeX", "@sage/xtrem-ui/mime-type/application/x-texinfo": "Document Texinfo", "@sage/xtrem-ui/mime-type/application/x-troff": "Document Troff", "@sage/xtrem-ui/mime-type/application/x-vrml": "VRML", "@sage/xtrem-ui/mime-type/application/x-www-form-urlencoded": "Form URL Encoded Data", "@sage/xtrem-ui/mime-type/application/x-x509-ca-cert": "Certificat X.509", "@sage/xtrem-ui/mime-type/application/x-xpinstall": "Installation Mozilla XPI", "@sage/xtrem-ui/mime-type/application/xhtml+xml": "XHTML", "@sage/xtrem-ui/mime-type/application/xml": "XML", "@sage/xtrem-ui/mime-type/application/xslt+xml": "XSLT", "@sage/xtrem-ui/mime-type/application/zip": "Archive ZIP", "@sage/xtrem-ui/mime-type/audio/aac": "Audio AAC", "@sage/xtrem-ui/mime-type/audio/amr": "Audio AMR", "@sage/xtrem-ui/mime-type/audio/midi": "Audio MIDI", "@sage/xtrem-ui/mime-type/audio/mpeg": "Audio MP3", "@sage/xtrem-ui/mime-type/audio/ogg": "Audio Ogg", "@sage/xtrem-ui/mime-type/audio/vnd.rn-realaudio": "RealAudio", "@sage/xtrem-ui/mime-type/audio/wav": "Audio WAV", "@sage/xtrem-ui/mime-type/audio/x-m4a": "Audio M4A", "@sage/xtrem-ui/mime-type/audio/x-matroska": "Audio Matroska", "@sage/xtrem-ui/mime-type/audio/x-mpegurl": "MPEG URL Stream", "@sage/xtrem-ui/mime-type/audio/x-ms-wax": "Playlist Audio WMA", "@sage/xtrem-ui/mime-type/audio/x-ms-wma": "Windows Media Audio", "@sage/xtrem-ui/mime-type/audio/x-pn-realaudio": "RealAudio", "@sage/xtrem-ui/mime-type/audio/x-pn-realaudio-plugin": "Plugin Real<PERSON>udio", "@sage/xtrem-ui/mime-type/audio/x-realaudio": "RealAudio", "@sage/xtrem-ui/mime-type/audio/x-wav": "Audio WAV", "@sage/xtrem-ui/mime-type/chemical/x-pdb": "Protein Data Bank", "@sage/xtrem-ui/mime-type/image/bmp": "Image Bitmap", "@sage/xtrem-ui/mime-type/image/cgm": "Computer Graphics Metafile", "@sage/xtrem-ui/mime-type/image/gif": "Image GIF", "@sage/xtrem-ui/mime-type/image/jpeg": "Image JPEG", "@sage/xtrem-ui/mime-type/image/png": "Image PNG", "@sage/xtrem-ui/mime-type/image/svg+xml": "Image SVG", "@sage/xtrem-ui/mime-type/image/tiff": "Image TIFF", "@sage/xtrem-ui/mime-type/image/vnd.microsoft.icon": "Image ICO", "@sage/xtrem-ui/mime-type/image/vnd.wap.wbmp": "Image WBMP", "@sage/xtrem-ui/mime-type/image/webp": "Image WebP", "@sage/xtrem-ui/mime-type/image/x-cmu-raster": "Image CMU", "@sage/xtrem-ui/mime-type/image/x-icon": "Icône image", "@sage/xtrem-ui/mime-type/image/x-jng": "Image JNG", "@sage/xtrem-ui/mime-type/image/x-ms-bmp": "Image Bitmap", "@sage/xtrem-ui/mime-type/image/x-portable-anymap": "Image Portable AnyMap", "@sage/xtrem-ui/mime-type/image/x-portable-bitmap": "Image Portable Bitmap", "@sage/xtrem-ui/mime-type/image/x-portable-graymap": "Image Portable Graymap", "@sage/xtrem-ui/mime-type/image/x-portable-pixmap": "Image Portable Pixmap", "@sage/xtrem-ui/mime-type/image/x-rgb": "Image RGB", "@sage/xtrem-ui/mime-type/image/x-xbitmap": "Image X Bitmap", "@sage/xtrem-ui/mime-type/image/x-xpixmap": "Image X Pixmap", "@sage/xtrem-ui/mime-type/image/x-xwindowdump": "Image X Window Dump", "@sage/xtrem-ui/mime-type/model/vrml": "Modèle VRML", "@sage/xtrem-ui/mime-type/multipart/form-data": "Données de formulaire/multiple", "@sage/xtrem-ui/mime-type/text/cache-manifest": "Manifeste de cache", "@sage/xtrem-ui/mime-type/text/calendar": "iCalendrier", "@sage/xtrem-ui/mime-type/text/css": "CSS", "@sage/xtrem-ui/mime-type/text/csv": "CSV (Comma-Separated Values)", "@sage/xtrem-ui/mime-type/text/ecmascript": "ECMAScript", "@sage/xtrem-ui/mime-type/text/html": "HTML", "@sage/xtrem-ui/mime-type/text/javascript": "JavaScript", "@sage/xtrem-ui/mime-type/text/markdown": "Document Markdown", "@sage/xtrem-ui/mime-type/text/mathml": "MathML", "@sage/xtrem-ui/mime-type/text/plain": "Texte simple", "@sage/xtrem-ui/mime-type/text/richtext": "Texte enrichi", "@sage/xtrem-ui/mime-type/text/rtf": "Format de texte enrichi", "@sage/xtrem-ui/mime-type/text/tab-separated-values": "Tab-Separated Values", "@sage/xtrem-ui/mime-type/text/vnd.sun.j2me.app-descriptor": "J2ME App Descriptor", "@sage/xtrem-ui/mime-type/text/vnd.wap.wml": "WML", "@sage/xtrem-ui/mime-type/text/vnd.wap.wmlscript": "WMLScript", "@sage/xtrem-ui/mime-type/text/vtt": "WebVTT", "@sage/xtrem-ui/mime-type/text/x-component": "Composant HTC", "@sage/xtrem-ui/mime-type/text/x-cross-domain-policy": "Stratégie inter-domaines", "@sage/xtrem-ui/mime-type/text/x-fortran": "Code source Fortran", "@sage/xtrem-ui/mime-type/text/xml": "Document XML", "@sage/xtrem-ui/mime-type/video/3gpp": "Video 3GPP", "@sage/xtrem-ui/mime-type/video/mp2t": "Video MPEG-2 TS", "@sage/xtrem-ui/mime-type/video/mp4": "Video MP4", "@sage/xtrem-ui/mime-type/video/mpeg": "Video MPEG", "@sage/xtrem-ui/mime-type/video/ogg": "Video Ogg", "@sage/xtrem-ui/mime-type/video/quicktime": "Video QuickTime", "@sage/xtrem-ui/mime-type/video/vnd.rn-realvideo": "RealVideo", "@sage/xtrem-ui/mime-type/video/webm": "Video WebM", "@sage/xtrem-ui/mime-type/video/x-flv": "Video FLV", "@sage/xtrem-ui/mime-type/video/x-m4v": "Video M4V", "@sage/xtrem-ui/mime-type/video/x-matroska": "Video Matroska", "@sage/xtrem-ui/mime-type/video/x-mng": "Video MNG", "@sage/xtrem-ui/mime-type/video/x-ms-asf": "Video ASF", "@sage/xtrem-ui/mime-type/video/x-ms-wmv": "Video WMV", "@sage/xtrem-ui/mime-type/video/x-ms-wvx": "Playlist Video WMV", "@sage/xtrem-ui/mime-type/video/x-msvideo": "Video AVI", "@sage/xtrem-ui/minimum": "Minimum", "@sage/xtrem-ui/minutes": "Minutes", "@sage/xtrem-ui/mobile-table-load-more": "Charger plus", "@sage/xtrem-ui/multi-file-deposit-cancel": "Annuler", "@sage/xtrem-ui/multi-file-deposit-create-tag": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/multi-file-deposit-description": "Description", "@sage/xtrem-ui/multi-file-deposit-edit-details": "Modifier les détails des fichiers", "@sage/xtrem-ui/multi-file-deposit-filename": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/multi-file-deposit-modified": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/multi-file-deposit-open-preview": "Ouv<PERSON>r le prévisualisation", "@sage/xtrem-ui/multi-file-deposit-remove": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/multi-file-deposit-size": "<PERSON><PERSON>", "@sage/xtrem-ui/multi-file-deposit-status": "Statut", "@sage/xtrem-ui/multi-file-deposit-status-created": "En cours", "@sage/xtrem-ui/multi-file-deposit-status-upload-cancelled": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/multi-file-deposit-status-upload-failed": "Échec", "@sage/xtrem-ui/multi-file-deposit-status-upload-rejected": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/multi-file-deposit-status-uploaded": "En attente", "@sage/xtrem-ui/multi-file-deposit-status-verified": "Télécharg<PERSON>", "@sage/xtrem-ui/multi-file-deposit-tag-description": "Description", "@sage/xtrem-ui/multi-file-deposit-tag-id": "Code", "@sage/xtrem-ui/multi-file-deposit-tag-title": "Intitulé", "@sage/xtrem-ui/multi-file-deposit-tags": "Étiquettes", "@sage/xtrem-ui/multi-file-deposit-title": "Intitulé", "@sage/xtrem-ui/multi-file-deposit-type": "Type", "@sage/xtrem-ui/multi-file-deposit-uploaded": "Télécharg<PERSON>", "@sage/xtrem-ui/multi-file-deposit-uploaded-by": "Téléchargé par", "@sage/xtrem-ui/multi-file-upload-cancel": "Annuler téléchargement fichier en cours.", "@sage/xtrem-ui/multi-file-upload-cancel-title": "Annuler téléchargement.", "@sage/xtrem-ui/multi-file-upload-remove": "Su<PERSON><PERSON><PERSON> le fichier téléchargé.", "@sage/xtrem-ui/multi-file-upload-remove-title": "<PERSON><PERSON><PERSON><PERSON> fichier.", "@sage/xtrem-ui/multipleRange": "Est égal à", "@sage/xtrem-ui/must-be-a-number": "Renseignez un nombre.", "@sage/xtrem-ui/must-be-between-1-and-365": "Renseignez un nombre compris entre 1 et 365.", "@sage/xtrem-ui/must-be-between-zero-and-four": "Cette valeur doit se situer entre 0 et 4.", "@sage/xtrem-ui/must-be-greater-than-zero": "Cette valeur doit être supérieure à 0.", "@sage/xtrem-ui/name": "Nom", "@sage/xtrem-ui/navigation-panel-failed": "Une erreur s'est produite lors du chargement des éléments de la liste de sélection. Réessayer.", "@sage/xtrem-ui/navigation-panel-my-view": "Mes données sélect<PERSON>ées", "@sage/xtrem-ui/navigation-panel-no-filter": "<PERSON>ut", "@sage/xtrem-ui/navigation-panel-no-results": "Aucune donnée à afficher", "@sage/xtrem-ui/navigation-panel-type-to-search": "Rechercher...", "@sage/xtrem-ui/nested-field-errors": "Erreurs de champs imbriqués", "@sage/xtrem-ui/new": "Nouveau", "@sage/xtrem-ui/next-day": "<PERSON><PERSON> suivant", "@sage/xtrem-ui/next-month": "<PERSON><PERSON> prochain", "@sage/xtrem-ui/next-record": "Enregistrement suivant", "@sage/xtrem-ui/next-week": "Semaine prochaine", "@sage/xtrem-ui/next-year": "<PERSON><PERSON>", "@sage/xtrem-ui/no": "Non", "@sage/xtrem-ui/no-available-image": "Pas d'image disponible", "@sage/xtrem-ui/no-dashboard-heading": "Vous n'avez pas encore de tableaux de bord.", "@sage/xtrem-ui/no-dashboard-subtitle": "<PERSON>ur d<PERSON><PERSON>er, créer un tableau de bord.", "@sage/xtrem-ui/no-dashboard-title": "G<PERSON>rer votre activité", "@sage/xtrem-ui/no-data": "Aucune donnée à afficher", "@sage/xtrem-ui/no-file-available": "Aucun fichier disponible", "@sage/xtrem-ui/no-node": "Aucun node n'est fourni ou défini dans {{0}}.", "@sage/xtrem-ui/no-page-content-found": "Contenu de la page introuvable. Assurez-vous que la page existe et que l'URL suit le format /@<nom fournisseur>/@<nom package>/@<nom page>.", "@sage/xtrem-ui/no-page-translations-found": "Traduction introuvable pour cette page", "@sage/xtrem-ui/no-pages-found": "Pages introuvables. Vérifiez vos critères de recherche.", "@sage/xtrem-ui/no-record-id-provided": "Aucun ID d'enregistrement n'est fourni dans les paramètres de la requête.", "@sage/xtrem-ui/no-sticker-content-found": "Contenu d'autocollant introuvable. Vérifiez vos critères de recherche.", "@sage/xtrem-ui/no-sticker-translations-found": "Traduction introuvable pour cet autocollant", "@sage/xtrem-ui/no-stickers-found": "Autocollant introuvable. Vérifiez vos critères de recherche.", "@sage/xtrem-ui/no-value": "Pas de valeur", "@sage/xtrem-ui/no-widget-content-found": "Aucun contenu trouvé pour le widget {{widget}}.", "@sage/xtrem-ui/notEqual": "N'est pas égal à", "@sage/xtrem-ui/number-format-separator": ",", "@sage/xtrem-ui/ok": "OK", "@sage/xtrem-ui/open-custom-field-dialog": "<PERSON><PERSON><PERSON> le champ", "@sage/xtrem-ui/open-dynamic-select": "<PERSON><PERSON><PERSON><PERSON><PERSON> la liste", "@sage/xtrem-ui/open-full-screen": "Ou<PERSON><PERSON>r en plein écran", "@sage/xtrem-ui/open-header-section": "<PERSON><PERSON><PERSON><PERSON><PERSON> l'en-tête", "@sage/xtrem-ui/open-lookup": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/open-record-history-dialog": "Historique enregistrement", "@sage/xtrem-ui/open-section": "Ouvrir la section", "@sage/xtrem-ui/openFilters": "Charger le filtre", "@sage/xtrem-ui/page-failed-to-load": "Le chargement de la page a échoué.", "@sage/xtrem-ui/parent": "Parent", "@sage/xtrem-ui/pdf-metadata-file-name": "**Nom de <PERSON> :** {{name}}", "@sage/xtrem-ui/pdf-metadata-file-size": "**<PERSON><PERSON> :** {{size}}", "@sage/xtrem-ui/pdf-metadata-file-type": "**Type de fichier :** {{fileType}}", "@sage/xtrem-ui/pdf-metadata-number-of-lines": "**Nombre de lignes :** {{lineCount}}", "@sage/xtrem-ui/pdf-metadata-pdf-version": "**Version PDF :** {{version}}", "@sage/xtrem-ui/pdf-metadata-producer": "**Application Producteur:** {{producer}}", "@sage/xtrem-ui/pdf-metadata-resolution": "**Résolution :** {{resolution}}", "@sage/xtrem-ui/please-select-placeholder": "Sélectionner...", "@sage/xtrem-ui/pod-collection-add-new": "Ajouter un élément", "@sage/xtrem-ui/pod-collection-cancel-button": "Annuler", "@sage/xtrem-ui/pod-collection-remove-button": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/pod-collection-remove-text": "Vous allez supprimer cet élément.", "@sage/xtrem-ui/pod-collection-remove-title": "Confirmer la <PERSON>", "@sage/xtrem-ui/pod-placeholder-text": "Aucunes données à afficher.", "@sage/xtrem-ui/pod-remove-item": "Retirer l'élément", "@sage/xtrem-ui/populate-list-title-default": "Ajouter une liste", "@sage/xtrem-ui/presentation": "Présentation", "@sage/xtrem-ui/preview-action-download": "Télécharger", "@sage/xtrem-ui/preview-action-print": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/preview-close": "Fermer la prévisualisation", "@sage/xtrem-ui/preview-go-to-page": "<PERSON>er à la page {{pageNumber}}", "@sage/xtrem-ui/preview-more-file-info": " Information du fichier", "@sage/xtrem-ui/preview-more-options": "Plus d'actions", "@sage/xtrem-ui/preview-more-thumbnails": "Onglets", "@sage/xtrem-ui/preview-no-file-selected": "<PERSON><PERSON>n fichier sélectionn<PERSON>.", "@sage/xtrem-ui/preview-no-preview-available": "Aucune prévisualisation disponible.", "@sage/xtrem-ui/preview-page-next": "<PERSON> suivante", "@sage/xtrem-ui/preview-page-prev": "<PERSON> p<PERSON>", "@sage/xtrem-ui/preview-zoom-in": "Zoom avant", "@sage/xtrem-ui/preview-zoom-level": "Niveau de zoom", "@sage/xtrem-ui/preview-zoom-out": "Zoom arri<PERSON>", "@sage/xtrem-ui/previous-day": "<PERSON><PERSON>", "@sage/xtrem-ui/previous-month": "<PERSON><PERSON>", "@sage/xtrem-ui/previous-record": "Enregistrement précédent", "@sage/xtrem-ui/previous-week": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/previous-year": "<PERSON><PERSON>", "@sage/xtrem-ui/Progress": "Avancement", "@sage/xtrem-ui/property": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/qr-code-component-not-available": "Le code QR est indisponible.", "@sage/xtrem-ui/record-history-created-title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/record-history-details": "Par **{{name}}** le **{{date}}** à **{{time}}**", "@sage/xtrem-ui/record-history-failed": "Échec de récupération de l'historique de l'enregistrement. Réessayez plus tard.", "@sage/xtrem-ui/record-history-last-update-title": "Dernière mise à jour", "@sage/xtrem-ui/reference-create-new-item-link": "Créer nouvel enregistrement", "@sage/xtrem-ui/reference-field-at-least-chars": "Saisir au moins {{0}} caractères.", "@sage/xtrem-ui/reference-field-fetching": "Chargement des suggestions...", "@sage/xtrem-ui/reference-field-no-results": "Aucun résultat trouvé. Vérifiez vos critères de recherche.", "@sage/xtrem-ui/reference-filter-clear-selection": "Effacer les éléments sélectionnés", "@sage/xtrem-ui/reference-lookup-dialog-dialog-title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/reference-lookup-dialog-failed-fetch": "Le chargement des options a échoué.", "@sage/xtrem-ui/reference-lookup-dialog-search-placeholder": "Rechercher...", "@sage/xtrem-ui/reference-open-lookup-link": "Voir la liste", "@sage/xtrem-ui/return-arrow": "Retour", "@sage/xtrem-ui/router-actions-unhandled-error": "Une erreur inconnue s'est produite. Réessayer.", "@sage/xtrem-ui/same-day": "Jour en cours", "@sage/xtrem-ui/same-month": "Mois en cours", "@sage/xtrem-ui/same-week": "<PERSON><PERSON><PERSON> en cours", "@sage/xtrem-ui/same-year": "Année en cours", "@sage/xtrem-ui/save-new-view-as": "Enregistrer la vue sous", "@sage/xtrem-ui/see-more-items": "Voir plus d'articles", "@sage/xtrem-ui/select-component-loading": "Chargement en cours", "@sage/xtrem-ui/select-component-no-results": "Aucun élément trouvé", "@sage/xtrem-ui/select-component-type-more": "Encore {{0}} caractères", "@sage/xtrem-ui/select-component-type-more-characters": "Renseigner {{0}} caractères supplémentaires pour lancer la recherche.", "@sage/xtrem-ui/select-component-type-one-more-character": "Renseigner 1 caractère supplémentaire pour lancer la recherche.", "@sage/xtrem-ui/select-component-type-to-search": "Rechercher...", "@sage/xtrem-ui/select-filter-type": "Sélectionner le type de filtre", "@sage/xtrem-ui/select-filter-value-boolean": "Sélectionner valeur...", "@sage/xtrem-ui/select-filter-value-enum": "Sélectionner valeur...", "@sage/xtrem-ui/select-filter-value-max": "Valeur maximum", "@sage/xtrem-ui/select-filter-value-min": "Valeur minimum", "@sage/xtrem-ui/select-property": "Sélectionner la propriété", "@sage/xtrem-ui/select-record": "Sélectionner l'enregistrement", "@sage/xtrem-ui/selection-card-filter-placeholder": "Filtrer...", "@sage/xtrem-ui/series-options": "Options de série", "@sage/xtrem-ui/set": "Est égal à", "@sage/xtrem-ui/show-floating-filters": "Afficher les filtres tables", "@sage/xtrem-ui/show-less": "Affiche<PERSON> moins", "@sage/xtrem-ui/show-more": "Afficher plus", "@sage/xtrem-ui/show-technical-details": "Afficher détails techniques", "@sage/xtrem-ui/sidebar-apply-changes": "Appliquer", "@sage/xtrem-ui/sidebar-apply-changes-and-create-new": "Appliquer et ajouter nouveau", "@sage/xtrem-ui/sidebar-mobile-apply-changes-and-create-new": "Appliquer & nouveau", "@sage/xtrem-ui/startsWith": "Commence par", "@sage/xtrem-ui/step-sequence-item-aria-complete": "Finalisée", "@sage/xtrem-ui/step-sequence-item-aria-count": "Étape {{0}} de {{1}}.", "@sage/xtrem-ui/step-sequence-item-aria-current": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/sticker-actions-onload-unhandled-error": "Une erreur inconnue s'est produite. Réessayer.", "@sage/xtrem-ui/string-contains": "{{fieldTitle}} contient {{filterValue}}.", "@sage/xtrem-ui/string-ends-with": "{{fieldTitle}} finit par {{filterValue}}.", "@sage/xtrem-ui/string-starts-with": "{{fieldTitle}} commence par {{filterValue}}.", "@sage/xtrem-ui/sum": "Somme", "@sage/xtrem-ui/switch-off-caps": "OFF", "@sage/xtrem-ui/switch-on-caps": "ON", "@sage/xtrem-ui/table-addCurrentSelectionToFilter": "Ajouter la sélection actuelle au filtre", "@sage/xtrem-ui/table-addToLabels": "Ajouter {{value}}** aux étiquettes", "@sage/xtrem-ui/table-addToValues": "Ajouter **{{value}}** aux valeurs", "@sage/xtrem-ui/table-advancedFilterAnd": "ET", "@sage/xtrem-ui/table-advancedFilterApply": "Appliquer", "@sage/xtrem-ui/table-advancedFilterBlank": "est vide", "@sage/xtrem-ui/table-advancedFilterBuilder": "Con<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-advancedFilterBuilderAddButtonTooltip": "Ajouter un filtre ou un groupe", "@sage/xtrem-ui/table-advancedFilterBuilderAddCondition": "Ajouter filtre", "@sage/xtrem-ui/table-advancedFilterBuilderAddJoin": "Ajouter groupe", "@sage/xtrem-ui/table-advancedFilterBuilderApply": "Appliquer", "@sage/xtrem-ui/table-advancedFilterBuilderCancel": "Annuler", "@sage/xtrem-ui/table-advancedFilterBuilderEnterValue": "Ren<PERSON><PERSON>r une valeur...", "@sage/xtrem-ui/table-advancedFilterBuilderMoveDownButtonTooltip": "Descendre", "@sage/xtrem-ui/table-advancedFilterBuilderMoveUpButtonTooltip": "Remonter", "@sage/xtrem-ui/table-advancedFilterBuilderRemoveButtonTooltip": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-advancedFilterBuilderSelectColumn": "Sélectionner une colonne", "@sage/xtrem-ui/table-advancedFilterBuilderSelectOption": "Sélectionner option", "@sage/xtrem-ui/table-advancedFilterBuilderTitle": "Filtre avancé", "@sage/xtrem-ui/table-advancedFilterBuilderValidationAlreadyApplied": "Filtre courant d<PERSON><PERSON><PERSON> appliqué", "@sage/xtrem-ui/table-advancedFilterBuilderValidationEnterValue": "<PERSON><PERSON> de<PERSON> renseigner une valeur.", "@sage/xtrem-ui/table-advancedFilterBuilderValidationIncomplete": "Toutes les conditions ne sont pas remplies.", "@sage/xtrem-ui/table-advancedFilterBuilderValidationSelectColumn": "<PERSON><PERSON> de<PERSON> sélectionner une colonne.", "@sage/xtrem-ui/table-advancedFilterBuilderValidationSelectOption": "<PERSON><PERSON> <PERSON> sélectionner une option.", "@sage/xtrem-ui/table-advancedFilterContains": "contient", "@sage/xtrem-ui/table-advancedFilterEndsWith": "se termine par", "@sage/xtrem-ui/table-advancedFilterEquals": "=", "@sage/xtrem-ui/table-advancedFilterFalse": "est faux", "@sage/xtrem-ui/table-advancedFilterGreaterThan": ">", "@sage/xtrem-ui/table-advancedFilterGreaterThanOrEqual": ">=", "@sage/xtrem-ui/table-advancedFilterLessThan": "<", "@sage/xtrem-ui/table-advancedFilterLessThanOrEqual": "<=", "@sage/xtrem-ui/table-advancedFilterNotBlank": "n'est pas vide", "@sage/xtrem-ui/table-advancedFilterNotContains": "ne contient pas", "@sage/xtrem-ui/table-advancedFilterNotEqual": "!=", "@sage/xtrem-ui/table-advancedFilterOr": "OU", "@sage/xtrem-ui/table-advancedFilterStartsWith": "commence par", "@sage/xtrem-ui/table-advancedFilterTextEquals": "est égal à", "@sage/xtrem-ui/table-advancedFilterTextNotEqual": "n'est pas égal à", "@sage/xtrem-ui/table-advancedFilterTrue": "est vrai", "@sage/xtrem-ui/table-advancedFilterValidationExtraEndBracket": "Trop de crochets de fin", "@sage/xtrem-ui/table-advancedFilterValidationInvalidColumn": "Colonne introuvable", "@sage/xtrem-ui/table-advancedFilterValidationInvalidDate": "La valeur n'est pas une date valide.", "@sage/xtrem-ui/table-advancedFilterValidationInvalidJoinOperator": "Opérateur de jonction non trouvé", "@sage/xtrem-ui/table-advancedFilterValidationInvalidOption": "Option introuvable", "@sage/xtrem-ui/table-advancedFilterValidationJoinOperatorMismatch": "Les opérateurs de jonction au sein d'une condition doivent être identiques.", "@sage/xtrem-ui/table-advancedFilterValidationMessage": "L'expression contient une erreur. **{{error}}** - **{{validation}}**.", "@sage/xtrem-ui/table-advancedFilterValidationMessageAtEnd": "L'expression contient une erreur.**{{validation}}** en fin d'expression.", "@sage/xtrem-ui/table-advancedFilterValidationMissingColumn": "Il manque la colonne.", "@sage/xtrem-ui/table-advancedFilterValidationMissingCondition": "Il manque la condition.", "@sage/xtrem-ui/table-advancedFilterValidationMissingEndBracket": "Il manque le crochet de fin.", "@sage/xtrem-ui/table-advancedFilterValidationMissingOption": "Il manque l'option.", "@sage/xtrem-ui/table-advancedFilterValidationMissingQuote": "Il manque un guillemet de fin à la valeur.", "@sage/xtrem-ui/table-advancedFilterValidationMissingValue": "Il manque la valeur.", "@sage/xtrem-ui/table-advancedFilterValidationNotANumber": "La valeur n'est pas un nombre", "@sage/xtrem-ui/table-advancedSettings": "Paramétrage avancé", "@sage/xtrem-ui/table-after": "<PERSON><PERSON>", "@sage/xtrem-ui/table-aggregate": "Agrégat", "@sage/xtrem-ui/table-andCondition": "ET", "@sage/xtrem-ui/table-animation": "Animations", "@sage/xtrem-ui/table-applyFilter": "Appliquer", "@sage/xtrem-ui/table-april": "Avril", "@sage/xtrem-ui/table-area": "Aire", "@sage/xtrem-ui/table-areaChart": "Aire", "@sage/xtrem-ui/table-AreaColumnCombo": "Zone & Colonne", "@sage/xtrem-ui/table-areaColumnComboTooltip": "Zone & Colonne", "@sage/xtrem-ui/table-areaGroup": "Aire", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderColumn": "Colonne", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderFilterItem": "Condition de filtr", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderGroupItem": "Groupe de filtres", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderItem": "**{{aria}}**. <PERSON><PERSON><PERSON> **{{level}}**. <PERSON><PERSON><PERSON><PERSON> sur Entrée pour modifier.", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderItemValidation": "**{{aria}}**. <PERSON><PERSON><PERSON> **{{level}}**. **{{subLevel}}** Appuyer sur Entrée pour modifier.", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderJoinOperator": "Opérateur de jonction", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderList": "Liste de constructeur de filtre avancé", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderOption": "Option", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderValueP": "<PERSON><PERSON>", "@sage/xtrem-ui/table-ariaAdvancedFilterInput": "Entrée de filtre avancé", "@sage/xtrem-ui/table-ariaChartMenuClose": "<PERSON><PERSON><PERSON> le menu d'édition de graphique", "@sage/xtrem-ui/table-ariaChartSelected": "Sélectionné", "@sage/xtrem-ui/table-ariaChecked": "coché", "@sage/xtrem-ui/table-ariaColumn": "Colonne", "@sage/xtrem-ui/table-ariaColumnFiltered": "<PERSON>onne fi<PERSON>", "@sage/xtrem-ui/table-ariaColumnGroup": "Groupe de colonnes", "@sage/xtrem-ui/table-ariaColumnPanelList": "Liste de colonnes", "@sage/xtrem-ui/table-ariaColumnSelectAll": "Basculer la visibilité de toutes les colonnes", "@sage/xtrem-ui/table-ariaDateFilterInput": "Entrée de filtre de date", "@sage/xtrem-ui/table-ariaDefaultListName": "Liste", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentAggFuncSeparator": " de ", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentDescription": "Appuyer sur DELETE pour supprimer", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentSortAscending": "croissant", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentSortDescending": "décroissant", "@sage/xtrem-ui/table-ariaDropZoneColumnGroupItemDescription": "Appuyer sur Entrée pour trier", "@sage/xtrem-ui/table-ariaDropZoneColumnValueItemDescription": "Appuyer sur Entrée pour modifier le type d'agrégation", "@sage/xtrem-ui/table-ariaFilterColumn": "Appuyer sur Ctrl Entrée pour ouvrir le filtre", "@sage/xtrem-ui/table-ariaFilterColumnsInput": "Entrée de colonnes de filtre", "@sage/xtrem-ui/table-ariaFilterFromValue": "Filtrer depuis la valeur", "@sage/xtrem-ui/table-ariaFilteringOperator": "Opérateur de filtre", "@sage/xtrem-ui/table-ariaFilterInput": "Entrée de filtre", "@sage/xtrem-ui/table-ariaFilterList": "Liste des filtres", "@sage/xtrem-ui/table-ariaFilterMenuOpen": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu de filtre", "@sage/xtrem-ui/table-ariaFilterPanelList": "Liste des filtres", "@sage/xtrem-ui/table-ariaFilterToValue": "Filtrer jusqu'à la valeur", "@sage/xtrem-ui/table-ariaFilterValue": "<PERSON>ur de filtre", "@sage/xtrem-ui/table-ariaHeaderSelection": "Colonne avec sélection d'en-têtes", "@sage/xtrem-ui/table-ariaHidden": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-ariaIndeterminate": "indéterminé", "@sage/xtrem-ui/table-ariaInputEditor": "Éditeur d'entrée", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterAutocomplete": "Filtre avancé auto-complétion", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderAddField": "Filtre avancé constructeur Ajouter un champ", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderColumnSelectField": "Filtre avancé constructeur champ de sélection de colonne", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderJoinSelectField": "Filtre avancé constructeur champ de sélection d'opérateur de jointure", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderOptionSelectField": "Filtre avancé constructeur champ de sélection d'option", "@sage/xtrem-ui/table-ariaLabelAggregationFunction": "Fonction d'agrégation", "@sage/xtrem-ui/table-ariaLabelCellEditor": "Éditeur de cellule", "@sage/xtrem-ui/table-ariaLabelColumnFilter": "Filtre de colonne", "@sage/xtrem-ui/table-ariaLabelColumnMenu": "<PERSON>u de colonne", "@sage/xtrem-ui/table-ariaLabelContextMenu": "Menu contextuel", "@sage/xtrem-ui/table-ariaLabelDialog": "Dialogue", "@sage/xtrem-ui/table-ariaLabelRichSelectDeleteSelection": "Appuyer sur SUPPR pour désélectionner un article", "@sage/xtrem-ui/table-ariaLabelRichSelectDeselectAllItems": "Appuyer sur SUPPR pour désélectionner tous les articles", "@sage/xtrem-ui/table-ariaLabelRichSelectField": "Champ de sélection enrichi", "@sage/xtrem-ui/table-ariaLabelRichSelectToggleSelection": "Appuyer sur ESPACE pour basculer la sélection", "@sage/xtrem-ui/table-ariaLabelSelectField": "Champ de sélection", "@sage/xtrem-ui/table-ariaLabelSubMenu": "Sous-menu", "@sage/xtrem-ui/table-ariaLabelTooltip": "Info-bulle", "@sage/xtrem-ui/table-ariaMenuColumn": "Appuyez sur ALT BAS pour ouvrir le menu de colonne", "@sage/xtrem-ui/table-ariaPageSizeSelectorLabel": "<PERSON><PERSON>", "@sage/xtrem-ui/table-ariaPivotDropZonePanelLabel": "Intitulés de colonnes", "@sage/xtrem-ui/table-ariaRowDeselect": "Appuyer sur Espace pour désélectionner cette ligne", "@sage/xtrem-ui/table-ariaRowGroupDropZonePanelLabel": "Groupes de lignes", "@sage/xtrem-ui/table-ariaRowSelect": "Appuyer sur Espace pour sélectionner cette ligne", "@sage/xtrem-ui/table-ariaRowSelectAll": "Appuyer sur ESPACE pour basculer la sélection de toutes les lignes", "@sage/xtrem-ui/table-ariaRowSelectionDisabled": "Le sélection des lignes est désactivée pour cette ligne.", "@sage/xtrem-ui/table-ariaRowToggleSelection": "Appuyer sur ESPACE pour basculer la sélection des lignes", "@sage/xtrem-ui/table-ariaSearch": "Recherche", "@sage/xtrem-ui/table-ariaSearchFilterValues": "Rechercher les valeurs de filtre", "@sage/xtrem-ui/table-ariaSkeletonCellLoading": "Les données de la ligne sont en cours de chargement.", "@sage/xtrem-ui/table-ariaSkeletonCellLoadingFailed": "Échec du chargement de la ligne", "@sage/xtrem-ui/table-ariaSortableColumn": "Appuyer sur Entrée pour trier", "@sage/xtrem-ui/table-ariaToggleCellValue": "Appuyer sur ESPACE pour basculer la valeur de la cellule", "@sage/xtrem-ui/table-ariaToggleVisibility": "Appuyer sur ESPACE pour basculer la visibilité", "@sage/xtrem-ui/table-ariaUnchecked": "non coché", "@sage/xtrem-ui/table-ariaValuesDropZonePanelLabel": "Valeurs", "@sage/xtrem-ui/table-ariaVisible": "visible", "@sage/xtrem-ui/table-august": "Août", "@sage/xtrem-ui/table-automatic": "Automatique", "@sage/xtrem-ui/table-autoRotate": "Rotation automatique", "@sage/xtrem-ui/table-autosizeAllColumns": "Ajustement auto toutes colonnes", "@sage/xtrem-ui/table-autosizeThiscolumn": "Ajustement auto colonne", "@sage/xtrem-ui/table-avg": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-axis": "Axe", "@sage/xtrem-ui/table-axisType": "Type d'axe", "@sage/xtrem-ui/table-background": "Arrière-plan", "@sage/xtrem-ui/table-bar": "<PERSON><PERSON>", "@sage/xtrem-ui/table-barChart": "<PERSON><PERSON>", "@sage/xtrem-ui/table-barGroup": "<PERSON><PERSON>", "@sage/xtrem-ui/table-before": "Avant", "@sage/xtrem-ui/table-blank": "Vide", "@sage/xtrem-ui/table-blanks": "Espaces vides", "@sage/xtrem-ui/table-blur": "<PERSON><PERSON>", "@sage/xtrem-ui/table-bold": "Gras", "@sage/xtrem-ui/table-boldItalic": "Gras italique", "@sage/xtrem-ui/table-bottom": "Bas", "@sage/xtrem-ui/table-boxPlot": "Boîte à moustaches", "@sage/xtrem-ui/table-boxPlotTooltip": "Boîte à moustaches", "@sage/xtrem-ui/table-bubble": "Info-bulle", "@sage/xtrem-ui/table-bubbleTooltip": "Info-bulle", "@sage/xtrem-ui/table-calendar-view": "Passer en vue calendrier", "@sage/xtrem-ui/table-callout": "Trait de légende", "@sage/xtrem-ui/table-calloutLabels": "Étiquettes d'appel", "@sage/xtrem-ui/table-cancelFgroupFilterSelectilter": "Sélectionner champ :", "@sage/xtrem-ui/table-cancelFilter": "Annuler", "@sage/xtrem-ui/table-cap": "Capuchon", "@sage/xtrem-ui/table-capLengthRatio": "<PERSON><PERSON>", "@sage/xtrem-ui/table-categories": "Catégories", "@sage/xtrem-ui/table-category": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-categoryAdd": "Ajouter une catégorie", "@sage/xtrem-ui/table-categoryValues": "Valeurs de catégorie", "@sage/xtrem-ui/table-chart": "Graphique", "@sage/xtrem-ui/table-chartAdvancedSettings": "Paramétrage avancé", "@sage/xtrem-ui/table-chartDownload": "Télécharger le graphique", "@sage/xtrem-ui/table-chartDownloadToolbarTooltip": "Télécharger le graphique", "@sage/xtrem-ui/table-chartEdit": "Modifier le graphique", "@sage/xtrem-ui/table-chartLink": "<PERSON><PERSON> au <PERSON>au", "@sage/xtrem-ui/table-chartLinkToolbarTooltip": "<PERSON><PERSON> au <PERSON>au", "@sage/xtrem-ui/table-chartMenuToolbarTooltip": "<PERSON><PERSON>", "@sage/xtrem-ui/table-chartRange": "Intervalle graphique", "@sage/xtrem-ui/table-chartSettingsToolbarTooltip": "<PERSON><PERSON>", "@sage/xtrem-ui/table-chartStyle": "Style graphique", "@sage/xtrem-ui/table-chartSubtitle": "Sous-titre", "@sage/xtrem-ui/table-chartTitle": "Titre de graphique", "@sage/xtrem-ui/table-chartTitles": "Intitulés", "@sage/xtrem-ui/table-chartUnlink": "Détacher du tableau", "@sage/xtrem-ui/table-chartUnlinkToolbarTooltip": "Détaché du tableau", "@sage/xtrem-ui/table-chooseColumns": "Choisir les colonnes", "@sage/xtrem-ui/table-circle": "Cercle", "@sage/xtrem-ui/table-clearFilter": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-collapseAll": "Tout fermer", "@sage/xtrem-ui/table-color": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-column": "Colonne", "@sage/xtrem-ui/table-column-settings": "Affichage des colonnes", "@sage/xtrem-ui/table-columnChart": "Colonne", "@sage/xtrem-ui/table-columnChooser": "Choisir les colonnes", "@sage/xtrem-ui/table-columnFilter": "Filtre de colonne", "@sage/xtrem-ui/table-columnGroup": "Colonne", "@sage/xtrem-ui/table-columnLineCombo": "Colonne & Ligne", "@sage/xtrem-ui/table-columnLineComboTooltip": "Colonne & Ligne", "@sage/xtrem-ui/table-columns": "Colonnes", "@sage/xtrem-ui/table-columns-display-controller-columns": "Colonnes", "@sage/xtrem-ui/table-combinationChart": "Combinaison", "@sage/xtrem-ui/table-combinationGroup": "Combinaison", "@sage/xtrem-ui/table-compare-with-previous-period": "Comparer avec la période précédente", "@sage/xtrem-ui/table-connectorLine": "Ligne de connecteur", "@sage/xtrem-ui/table-contains": "Contient", "@sage/xtrem-ui/table-copy": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-copyWithGroupHeaders": "Copier avec en-têtes de groupe", "@sage/xtrem-ui/table-copyWithHeaders": "<PERSON><PERSON><PERSON> avec en<PERSON>tê<PERSON>", "@sage/xtrem-ui/table-count": "Éléments", "@sage/xtrem-ui/table-create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-cross": "Croix", "@sage/xtrem-ui/table-crosshair": "Réticule", "@sage/xtrem-ui/table-crosshairLabel": "Libellé", "@sage/xtrem-ui/table-crosshairSnap": "Accrocher au nœud", "@sage/xtrem-ui/table-csvExport": "Export CSV", "@sage/xtrem-ui/table-ctrlC": "Ctrl+C", "@sage/xtrem-ui/table-ctrlV": "Ctrl+V", "@sage/xtrem-ui/table-ctrlX": "Ctrl+X", "@sage/xtrem-ui/table-customCombo": "Combinaison personnalisée", "@sage/xtrem-ui/table-customComboTooltip": "Combinaison personnalisée", "@sage/xtrem-ui/table-cut": "Couper", "@sage/xtrem-ui/table-data": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-dateFilter": "Filtre de date", "@sage/xtrem-ui/table-dateFormatOoo": "DD/MM/YYYY", "@sage/xtrem-ui/table-december": "Décembre", "@sage/xtrem-ui/table-decimalSeparator": ".", "@sage/xtrem-ui/table-defaultCategory": "(Aucune)", "@sage/xtrem-ui/table-diamond": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-direction": "Direction", "@sage/xtrem-ui/table-donut": "Donut", "@sage/xtrem-ui/table-donutTooltip": "Donut", "@sage/xtrem-ui/table-doughnut": "<PERSON><PERSON>", "@sage/xtrem-ui/table-doughnutTooltip": "<PERSON><PERSON>", "@sage/xtrem-ui/table-durationMillis": "<PERSON><PERSON><PERSON> (ms)", "@sage/xtrem-ui/table-empty": "Choisissez-en un", "@sage/xtrem-ui/table-enabled": "Activée", "@sage/xtrem-ui/table-endAngle": "Angle de fin", "@sage/xtrem-ui/table-endsWith": "Se termine par", "@sage/xtrem-ui/table-equals": "Est égal à", "@sage/xtrem-ui/table-excelExport": "Export Excel (.xlsx)", "@sage/xtrem-ui/table-excelXmlExport": "Export Excel (.xml)", "@sage/xtrem-ui/table-expandAll": "<PERSON><PERSON> d<PERSON>vel<PERSON>per", "@sage/xtrem-ui/table-export": "Exporter", "@sage/xtrem-ui/table-export-failed": "Échec de l'export du contenu de la liste principal", "@sage/xtrem-ui/table-export-service-no-columns": "Aucune colonne à exporter.", "@sage/xtrem-ui/table-export-started": "Export dé<PERSON>.", "@sage/xtrem-ui/table-false": "Faux", "@sage/xtrem-ui/table-february": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-fillOpacity": "Re<PERSON><PERSON>r op<PERSON>", "@sage/xtrem-ui/table-filter-aria-label": "Filtre", "@sage/xtrem-ui/table-filteredRows": "Filtrées", "@sage/xtrem-ui/table-filterOoo": "Filtrer...", "@sage/xtrem-ui/table-filters": "Filtres", "@sage/xtrem-ui/table-first": "Premier", "@sage/xtrem-ui/table-firstPage": "Première page", "@sage/xtrem-ui/table-fixed": "Fixe", "@sage/xtrem-ui/table-font": "Police", "@sage/xtrem-ui/table-footerTotal": "Total", "@sage/xtrem-ui/table-format": "Format", "@sage/xtrem-ui/table-greaterThan": "Sup<PERSON>ur à", "@sage/xtrem-ui/table-greaterThanOrEqual": "Su<PERSON><PERSON><PERSON> ou égal à", "@sage/xtrem-ui/table-gridLines": "<PERSON>gne<PERSON>au", "@sage/xtrem-ui/table-group": "Regrouper", "@sage/xtrem-ui/table-group-total": "Total groupe", "@sage/xtrem-ui/table-groupBy": "Regrouper par", "@sage/xtrem-ui/table-groupedAreaTooltip": "Zone", "@sage/xtrem-ui/table-groupedBar": "Groupée", "@sage/xtrem-ui/table-groupedBarFull": "Barre groupée", "@sage/xtrem-ui/table-groupedBarTooltip": "Groupée", "@sage/xtrem-ui/table-groupedColumn": "Groupée", "@sage/xtrem-ui/table-groupedColumnFull": "Colonne groupée", "@sage/xtrem-ui/table-groupedColumnTooltip": "Groupée", "@sage/xtrem-ui/table-groupedSeriesGroupType": "Groupé", "@sage/xtrem-ui/table-groupPadding": "Rembourrage de groupe", "@sage/xtrem-ui/table-groups": "Groupes de lignes", "@sage/xtrem-ui/table-heart": "Coeur", "@sage/xtrem-ui/table-heatmap": "Carte thermique", "@sage/xtrem-ui/table-heatmapTooltip": "Carte thermique", "@sage/xtrem-ui/table-height": "<PERSON><PERSON>", "@sage/xtrem-ui/table-hierarchicalChart": "Hiérarchique", "@sage/xtrem-ui/table-hierarchicalGroup": "Hiérarchique", "@sage/xtrem-ui/table-histogram": "Histogramme", "@sage/xtrem-ui/table-histogramBinCount": "Comptage des regroupements", "@sage/xtrem-ui/table-histogramChart": "Histogramme", "@sage/xtrem-ui/table-histogramFrequency": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-histogramGroup": "Histogramme", "@sage/xtrem-ui/table-histogramTooltip": "Histogramme", "@sage/xtrem-ui/table-horizontal": "Horizontal", "@sage/xtrem-ui/table-horizontalAxisTitle": "Intitulé de l'axe horizontal", "@sage/xtrem-ui/table-innerRadius": "Rayon intérieur", "@sage/xtrem-ui/table-inRange": "Dans l'intervalle", "@sage/xtrem-ui/table-inRangeEnd": "À", "@sage/xtrem-ui/table-inRangeStart": "De", "@sage/xtrem-ui/table-inside": "À l'intérieur", "@sage/xtrem-ui/table-invalidColor": "La valeur de couleur est invalide.", "@sage/xtrem-ui/table-invalidDate": "Date invalide", "@sage/xtrem-ui/table-invalidNumber": "Numéro invalide", "@sage/xtrem-ui/table-italic": "Italique", "@sage/xtrem-ui/table-itemPaddingX": "Écart de remplissage élément X", "@sage/xtrem-ui/table-itemPaddingY": "Écart de remplissage élément Y", "@sage/xtrem-ui/table-itemSpacing": "Espacement élément", "@sage/xtrem-ui/table-january": "<PERSON><PERSON>", "@sage/xtrem-ui/table-july": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-june": "Juin", "@sage/xtrem-ui/table-labelPlacement": "Placement", "@sage/xtrem-ui/table-labelRotation": "Rotation", "@sage/xtrem-ui/table-labels": "Intitulés", "@sage/xtrem-ui/table-last": "<PERSON><PERSON>", "@sage/xtrem-ui/table-lastPage": "Dernière page", "@sage/xtrem-ui/table-layoutHorizontalSpacing": "Espacement horizontal", "@sage/xtrem-ui/table-layoutVerticalSpacing": "Espacement vertical", "@sage/xtrem-ui/table-left": "G<PERSON><PERSON>", "@sage/xtrem-ui/table-legend": "Légende", "@sage/xtrem-ui/table-legendEnabled": "Activé", "@sage/xtrem-ui/table-length": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-lessThan": "Inférieur à", "@sage/xtrem-ui/table-lessThanOrEqual": "Inférieur ou égal à", "@sage/xtrem-ui/table-line": "Ligne", "@sage/xtrem-ui/table-line-number": "Numéro de ligne", "@sage/xtrem-ui/table-lineChart": "Ligne", "@sage/xtrem-ui/table-lineDash": "Tirets de la ligne", "@sage/xtrem-ui/table-lineDashOffset": "Décalage des tirets", "@sage/xtrem-ui/table-lineGroup": "Ligne", "@sage/xtrem-ui/table-lineTooltip": "Ligne", "@sage/xtrem-ui/table-lineWidth": "<PERSON><PERSON> de ligne", "@sage/xtrem-ui/table-loadingError": "ERR", "@sage/xtrem-ui/table-loadingOoo": "Chargement en cours", "@sage/xtrem-ui/table-march": "Mars", "@sage/xtrem-ui/table-markerPadding": "<PERSON><PERSON><PERSON> de remplissage marqueur", "@sage/xtrem-ui/table-markers": "Mar<PERSON><PERSON>", "@sage/xtrem-ui/table-markerSize": "<PERSON><PERSON> marqueur", "@sage/xtrem-ui/table-markerStroke": "Contour marqueur", "@sage/xtrem-ui/table-max": "Max", "@sage/xtrem-ui/table-maxSize": "Taille maximum", "@sage/xtrem-ui/table-may": "<PERSON>", "@sage/xtrem-ui/table-min": "Mini", "@sage/xtrem-ui/table-miniChart": "Mini-graphique", "@sage/xtrem-ui/table-minSize": "<PERSON><PERSON> minimum", "@sage/xtrem-ui/table-more": "plus", "@sage/xtrem-ui/table-navigator": "Navigateur", "@sage/xtrem-ui/table-next": "Suivant", "@sage/xtrem-ui/table-nextPage": "<PERSON> suivante", "@sage/xtrem-ui/table-nightingale": "<PERSON>", "@sage/xtrem-ui/table-nightingaleTooltip": "<PERSON>", "@sage/xtrem-ui/table-noAggregation": "Aucune", "@sage/xtrem-ui/table-noDataToChart": "Aucune donnée disponible pour le graphique.", "@sage/xtrem-ui/table-noMatches": "Aucune concordance", "@sage/xtrem-ui/table-none": "Aucune", "@sage/xtrem-ui/table-noPin": "<PERSON><PERSON> <PERSON>", "@sage/xtrem-ui/table-normal": "Normale", "@sage/xtrem-ui/table-normalizedArea": "100% empilées", "@sage/xtrem-ui/table-normalizedAreaFull": "Zone 100% empilée", "@sage/xtrem-ui/table-normalizedAreaTooltip": "100% empilées", "@sage/xtrem-ui/table-normalizedBar": "100% empilées", "@sage/xtrem-ui/table-normalizedBarFull": "Barre 100% empilée", "@sage/xtrem-ui/table-normalizedBarTooltip": "100% empilées", "@sage/xtrem-ui/table-normalizedColumn": "100% empilées", "@sage/xtrem-ui/table-normalizedColumnFull": "Colonne 100% empilée", "@sage/xtrem-ui/table-normalizedColumnTooltip": "100% empilées", "@sage/xtrem-ui/table-normalizedLine": "100% empilé", "@sage/xtrem-ui/table-normalizedLineTooltip": "100% empilé", "@sage/xtrem-ui/table-normalizedSeriesGroupType": "100% empilé", "@sage/xtrem-ui/table-noRowsToShow": "Aucune ligne à afficher", "@sage/xtrem-ui/table-notBlank": "n'est pas vide", "@sage/xtrem-ui/table-notContains": "Ne contient pas", "@sage/xtrem-ui/table-notEqual": "N'est pas égal à", "@sage/xtrem-ui/table-november": "Novembre", "@sage/xtrem-ui/table-number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-numberFilter": "Filtre de nombre", "@sage/xtrem-ui/table-numeric-filter-greater-than-equals-value": ">= {{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-greater-than-value": "> {{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-less-than-equals-value": "<= {{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-less-than-value": "< {{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-not-value": "Pas {{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-range-value": "{{numericValue}} - {{numericValueTo}}", "@sage/xtrem-ui/table-october": "Octobre", "@sage/xtrem-ui/table-of": "de", "@sage/xtrem-ui/table-offset": "Décalage", "@sage/xtrem-ui/table-offsets": "Décalages", "@sage/xtrem-ui/table-open-column-panel": "<PERSON><PERSON><PERSON><PERSON><PERSON> le volet des colonnes", "@sage/xtrem-ui/table-orCondition": "OU", "@sage/xtrem-ui/table-orientation": "Orientation", "@sage/xtrem-ui/table-outside": "À l'extérieur", "@sage/xtrem-ui/table-padding": "<PERSON><PERSON>t de remplis<PERSON>ge", "@sage/xtrem-ui/table-page": "Page", "@sage/xtrem-ui/table-pageLastRowUnknown": "?", "@sage/xtrem-ui/table-pageSizeSelectorLabel": "<PERSON><PERSON> page :", "@sage/xtrem-ui/table-paired": "Mode connecté", "@sage/xtrem-ui/table-parallel": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-paste": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-perpendicular": "Perpendiculaire", "@sage/xtrem-ui/table-pie": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-pieChart": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-pieGroup": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-pieTooltip": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-pinColumn": "<PERSON><PERSON><PERSON> les colonnes", "@sage/xtrem-ui/table-pinLeft": "Épingler à gauche", "@sage/xtrem-ui/table-pinRight": "É<PERSON>ler à droite", "@sage/xtrem-ui/table-pivotChart": "Graphique croisé dynamique", "@sage/xtrem-ui/table-pivotChartAndPivotMode": "Graphique croisé dynamique et mode croisé dynamique", "@sage/xtrem-ui/table-pivotChartRequiresPivotMode": "Le graphique croisé dynamique nécessite que le mode croisé soit activé.", "@sage/xtrem-ui/table-pivotChartTitle": "Graphique croisé dynamique", "@sage/xtrem-ui/table-pivotColumnGroupTotals": "Total", "@sage/xtrem-ui/table-pivotColumnsEmptyMessage": "Glisser ici pour définir les intitulés de colonnes.", "@sage/xtrem-ui/table-pivotMode": "Mode pivot", "@sage/xtrem-ui/table-pivots": "Intitulés de colonnes", "@sage/xtrem-ui/table-plus": "Plus", "@sage/xtrem-ui/table-polarAxis": "Axe polaire", "@sage/xtrem-ui/table-polarAxisTitle": "Libellé de l'axe polaire", "@sage/xtrem-ui/table-polarChart": "Polaire", "@sage/xtrem-ui/table-polarGroup": "Polaire", "@sage/xtrem-ui/table-polygon": "Polygone", "@sage/xtrem-ui/table-position": "Position", "@sage/xtrem-ui/table-positionRatio": "Ration de position", "@sage/xtrem-ui/table-predefined": "Prédéfinie", "@sage/xtrem-ui/table-preferredLength": "<PERSON>ueur préf<PERSON><PERSON>e", "@sage/xtrem-ui/table-previous": "Précédent", "@sage/xtrem-ui/table-previousPage": "<PERSON> p<PERSON>", "@sage/xtrem-ui/table-print": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-radarArea": "Aire radar", "@sage/xtrem-ui/table-radarAreaTooltip": "Aire radar", "@sage/xtrem-ui/table-radarLine": "Ligne radar", "@sage/xtrem-ui/table-radarLineTooltip": "Ligne radar", "@sage/xtrem-ui/table-radialBar": "Barre radiale", "@sage/xtrem-ui/table-radialBarTooltip": "Barre radiale", "@sage/xtrem-ui/table-radialColumn": "Colonne radiale", "@sage/xtrem-ui/table-radialColumnTooltip": "Colonne radiale", "@sage/xtrem-ui/table-radiusAxis": "Axe de rayon", "@sage/xtrem-ui/table-radiusAxisPosition": "Position", "@sage/xtrem-ui/table-rangeArea": "Zone de plage", "@sage/xtrem-ui/table-rangeAreaTooltip": "Zone de plage", "@sage/xtrem-ui/table-rangeBar": "Barre de plage", "@sage/xtrem-ui/table-rangeBarTooltip": "Barre de plage", "@sage/xtrem-ui/table-rangeChartTitle": "Intervalle graphique", "@sage/xtrem-ui/table-removeFromLabels": "Supprimer **{{value}}** des étiquettes", "@sage/xtrem-ui/table-removeFromValues": "Supprimer **{{value}}** des valeurs", "@sage/xtrem-ui/table-resetColumns": "Réinitialiser colonnes", "@sage/xtrem-ui/table-resetFilter": "Réinitialiser", "@sage/xtrem-ui/table-reverseDirection": "Inverser la direction", "@sage/xtrem-ui/table-right": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-rowDragRow": "ligne", "@sage/xtrem-ui/table-rowDragRows": "lignes", "@sage/xtrem-ui/table-rowGroupColumnsEmptyMessage": "Glisser ici pour définir les groupes de lignes.", "@sage/xtrem-ui/table-scatter": "Répartir", "@sage/xtrem-ui/table-scatterGroup": "X Y (Répartition)", "@sage/xtrem-ui/table-scatterTooltip": "Répartir", "@sage/xtrem-ui/table-scrollingStep": "Étape de <PERSON>", "@sage/xtrem-ui/table-scrollingZoom": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-searchOoo": "Rechercher...", "@sage/xtrem-ui/table-secondaryAxis": "Axe secondaire", "@sage/xtrem-ui/table-sectorLabels": "Étiquettes de secteur", "@sage/xtrem-ui/table-select-all": "<PERSON><PERSON>", "@sage/xtrem-ui/table-selectAll": "<PERSON><PERSON>", "@sage/xtrem-ui/table-selectAllSearchResults": "Sélectionner tous les résultats de la recherche", "@sage/xtrem-ui/table-selectedRows": "Sélectionnées", "@sage/xtrem-ui/table-selectingZoom": "Sélection", "@sage/xtrem-ui/table-september": "Septembre", "@sage/xtrem-ui/table-series": "Séries", "@sage/xtrem-ui/table-seriesAdd": "Ajouter une série", "@sage/xtrem-ui/table-seriesChartType": "Type de graphique de séries", "@sage/xtrem-ui/table-seriesGroupType": "Type de groupe", "@sage/xtrem-ui/table-seriesItemLabels": "Étiquettes d'articles", "@sage/xtrem-ui/table-seriesItemNegative": "Négatif", "@sage/xtrem-ui/table-seriesItemPositive": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-seriesItems": "Articles de série", "@sage/xtrem-ui/table-seriesItemType": "Type d'article", "@sage/xtrem-ui/table-seriesLabels": "Étiquettes de séries", "@sage/xtrem-ui/table-seriesPadding": "Rembourrage de séries", "@sage/xtrem-ui/table-seriesType": "Type de séries", "@sage/xtrem-ui/table-setFilter": "<PERSON><PERSON>re de jeu", "@sage/xtrem-ui/table-settings": "Paramétrage", "@sage/xtrem-ui/table-shadow": "Ombre", "@sage/xtrem-ui/table-shape": "Forme", "@sage/xtrem-ui/table-show": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-size": "<PERSON><PERSON>", "@sage/xtrem-ui/table-sortAscending": "Trier par ordre croissant", "@sage/xtrem-ui/table-sortDescending": "Trier par ordre décroissant", "@sage/xtrem-ui/table-sortUnSort": "<PERSON>ff<PERSON><PERSON> le tri", "@sage/xtrem-ui/table-spacing": "Espacement", "@sage/xtrem-ui/table-specializedChart": "Spécialisé", "@sage/xtrem-ui/table-specializedGroup": "Spécialisé", "@sage/xtrem-ui/table-square": "Carré", "@sage/xtrem-ui/table-stackedArea": "Empilée", "@sage/xtrem-ui/table-stackedAreaFull": "Zone empilée", "@sage/xtrem-ui/table-stackedAreaTooltip": "Empilée", "@sage/xtrem-ui/table-stackedBar": "Empilée", "@sage/xtrem-ui/table-stackedBarFull": "Barre empilée", "@sage/xtrem-ui/table-stackedBarTooltip": "Empilée", "@sage/xtrem-ui/table-stackedColumn": "Empilée", "@sage/xtrem-ui/table-stackedColumnFull": "Colonne empi<PERSON>", "@sage/xtrem-ui/table-stackedColumnTooltip": "Empilée", "@sage/xtrem-ui/table-stackedLine": "Empilé", "@sage/xtrem-ui/table-stackedLineTooltip": "Empilé", "@sage/xtrem-ui/table-stackedSeriesGroupType": "Empilé", "@sage/xtrem-ui/table-startAngle": "<PERSON><PERSON>", "@sage/xtrem-ui/table-startsWith": "Commence par", "@sage/xtrem-ui/table-statisticalChart": "Statistique", "@sage/xtrem-ui/table-statisticalGroup": "Statistique", "@sage/xtrem-ui/table-strokeColor": "<PERSON><PERSON><PERSON> de ligne", "@sage/xtrem-ui/table-strokeOpacity": "Opacité ligne", "@sage/xtrem-ui/table-strokeWidth": "Largeur contour", "@sage/xtrem-ui/table-sum": "Somme", "@sage/xtrem-ui/table-summary-empty-default-text": "Aucune donnée à afficher", "@sage/xtrem-ui/table-sunburst": "Rayonnement", "@sage/xtrem-ui/table-sunburstTooltip": "Rayonnement", "@sage/xtrem-ui/table-switch-to-card-view": "Passer en vue fiche", "@sage/xtrem-ui/table-switch-to-chart-view": "Passer en vue graphique", "@sage/xtrem-ui/table-switch-to-contact-view": "Passer en vue contact", "@sage/xtrem-ui/table-switch-to-site-view": "Passer en vue site", "@sage/xtrem-ui/table-switch-to-table-view": "Passer en vue table", "@sage/xtrem-ui/table-switchCategorySeries": "Changer catégorie / séries", "@sage/xtrem-ui/table-table-view": "Passer en vue table", "@sage/xtrem-ui/table-textFilter": "Filtre de texte", "@sage/xtrem-ui/table-thickness": "Épaisseur", "@sage/xtrem-ui/table-thousandSeparator": ",", "@sage/xtrem-ui/table-ticks": "Coches", "@sage/xtrem-ui/table-tile": "<PERSON><PERSON>", "@sage/xtrem-ui/table-time": "<PERSON><PERSON>", "@sage/xtrem-ui/table-timeFormat": "Format heure", "@sage/xtrem-ui/table-timeFormatDashesYYYYMMDD": "YYYY-MM-DD", "@sage/xtrem-ui/table-timeFormatDotsDDMYY": "DD.M.YY", "@sage/xtrem-ui/table-timeFormatDotsMDDYY": "M.<PERSON>.YY", "@sage/xtrem-ui/table-timeFormatHHMMSS": "HH:MM:SS", "@sage/xtrem-ui/table-timeFormatHHMMSSAmPm": "HH:MM:SS AM/PM", "@sage/xtrem-ui/table-timeFormatSlashesDDMMYY": "DD/MM/YY", "@sage/xtrem-ui/table-timeFormatSlashesDDMMYYYY": "DD/MM/YYYY", "@sage/xtrem-ui/table-timeFormatSlashesMMDDYY": "MM/DD/YY", "@sage/xtrem-ui/table-timeFormatSlashesMMDDYYYY": "MM/DD/YYYY", "@sage/xtrem-ui/table-timeFormatSpacesDDMMMMYYYY": "DD MMMM YYYY", "@sage/xtrem-ui/table-title": "Titre", "@sage/xtrem-ui/table-titlePlaceholder": "Titre du graphique - Double-cliquer pour modifier.", "@sage/xtrem-ui/table-to": "à", "@sage/xtrem-ui/table-tooltips": "<PERSON><PERSON> d'aide", "@sage/xtrem-ui/table-top": "<PERSON><PERSON>", "@sage/xtrem-ui/table-totalAndFilteredRows": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-totalRows": "Total de lignes", "@sage/xtrem-ui/table-treemap": "Arborescence", "@sage/xtrem-ui/table-treemapTooltip": "Arborescence", "@sage/xtrem-ui/table-triangle": "Triangle", "@sage/xtrem-ui/table-true": "Vrai", "@sage/xtrem-ui/table-ungroupAll": "<PERSON><PERSON><PERSON><PERSON> tout", "@sage/xtrem-ui/table-ungroupBy": "Dissocier par", "@sage/xtrem-ui/table-valueAggregation": "Regroupement de valeurs", "@sage/xtrem-ui/table-valueColumnsEmptyMessage": "Glisser ici pour regrouper.", "@sage/xtrem-ui/table-values": "Valeurs", "@sage/xtrem-ui/table-variant_card": "<PERSON>ue fiche", "@sage/xtrem-ui/table-variant-area": "Vue aire", "@sage/xtrem-ui/table-variant-table": "Vue table", "@sage/xtrem-ui/table-vertical": "Vertical", "@sage/xtrem-ui/table-verticalAxisTitle": "Intitulé de l'axe vertical", "@sage/xtrem-ui/table-views-close-button": "<PERSON><PERSON><PERSON> le menu déroulant de sélecteur de vue", "@sage/xtrem-ui/table-views-default": "Vue par défaut", "@sage/xtrem-ui/table-views-manage": "<PERSON><PERSON><PERSON> les vues", "@sage/xtrem-ui/table-views-open-button": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu déroulant de sélecteur de vue", "@sage/xtrem-ui/table-views-save": "Enregistrer la vue", "@sage/xtrem-ui/table-views-save-as": "Enregistrer la vue", "@sage/xtrem-ui/table-views-save-failed": "Échec de l'enregistrement de la vue", "@sage/xtrem-ui/table-views-saved": "Vue enregistrée", "@sage/xtrem-ui/table-views-select": "Sélectionner la vue", "@sage/xtrem-ui/table-waterfall": "Cascade", "@sage/xtrem-ui/table-waterfallTooltip": "Cascade", "@sage/xtrem-ui/table-weight": "Poids", "@sage/xtrem-ui/table-whisker": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-width": "<PERSON><PERSON>", "@sage/xtrem-ui/table-xAxis": "Axe horizontal", "@sage/xtrem-ui/table-xOffset": "Décalage X", "@sage/xtrem-ui/table-xRotation": "Rotation X", "@sage/xtrem-ui/table-xType": "Type X", "@sage/xtrem-ui/table-xyChart": "X Y (Répartition)", "@sage/xtrem-ui/table-xyValues": "Valeurs X Y", "@sage/xtrem-ui/table-yAxis": "Axe vertical", "@sage/xtrem-ui/table-yOffset": "Décalage Y", "@sage/xtrem-ui/table-yRotation": "Rotation Y", "@sage/xtrem-ui/table-zoom": "Zoom", "@sage/xtrem-ui/text-editor-cancel": "Annuler", "@sage/xtrem-ui/text-editor-cancel-button": "Annuler", "@sage/xtrem-ui/text-editor-character-counter": "Nombre de caractères", "@sage/xtrem-ui/text-editor-character-limit": "Limite de caractères", "@sage/xtrem-ui/text-editor-content-editor": "É<PERSON><PERSON> de contenu", "@sage/xtrem-ui/text-editor-save": "Enregistrer", "@sage/xtrem-ui/text-editor-save-button": "Enregistrer", "@sage/xtrem-ui/text-editor-toolbar": "Barre d'outils", "@sage/xtrem-ui/text-format-capital-bold": "Gras", "@sage/xtrem-ui/text-format-capital-bullet-list": "Liste à puces", "@sage/xtrem-ui/text-format-capital-italic": "Italique", "@sage/xtrem-ui/text-format-capital-number-list": "Liste à chiffres", "@sage/xtrem-ui/timeframe": "Période de temps", "@sage/xtrem-ui/title": "Titre", "@sage/xtrem-ui/toggle-navigation-panel": "Afficher / Masquer le volet de navigation", "@sage/xtrem-ui/toggle-widget-list": "Afficher / Masquer la liste des widgets", "@sage/xtrem-ui/true": "Vrai", "@sage/xtrem-ui/tunnel-already-open-description": "Cette page est déjà utilisée ci-dessous. Veuillez d'abord la fermer pour pouvoir ouvrir ce lien, ou ouvrez-la dans un nouvel onglet.", "@sage/xtrem-ui/tunnel-already-open-title": "Cette page est déjà ouverte.", "@sage/xtrem-ui/tunnel-link-see-more": "Voir les détails", "@sage/xtrem-ui/tunnel-record-not-suitable-description": "L'enregistrement créé ne peut pas être utilisé dans ce contexte.", "@sage/xtrem-ui/tunnel-record-not-suitable-title": "Nouvel enregistrement indisponible", "@sage/xtrem-ui/unexpected-error-page-load": "Une erreur s'est produite lors du chargement de la page. Réessayer.", "@sage/xtrem-ui/ungroup": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/unhandled-event-handler-error": "Une erreur inconnue s'est produite. Réessayer.", "@sage/xtrem-ui/unsaved-changes-content": "<PERSON><PERSON>ter et abandonner vos modifications ?", "@sage/xtrem-ui/unsaved-changes-go-back": "Annuler", "@sage/xtrem-ui/unsaved-changes-title": "Modifications en cours", "@sage/xtrem-ui/unsaved-changes-yes": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/update-error": "L'erreur {{0}} s'est produite lors de la mise à jour.", "@sage/xtrem-ui/upload-in-progress": "Date de téléchargement", "@sage/xtrem-ui/validation-error-and-more": "et 1 erreur supplémentaire", "@sage/xtrem-ui/validation-error-of-children": "Erreur de validation dans les sous-fiches", "@sage/xtrem-ui/validation-error-total": "1 erreur", "@sage/xtrem-ui/validation-error-with-number-grid": "Il y a 1 erreur dans le tableau suivant : {{0}}.", "@sage/xtrem-ui/validation-error-with-number-pod": "Il y a 1 erreur dans la collection suivante : {{0}}.", "@sage/xtrem-ui/validation-errors": "Erreurs de validation", "@sage/xtrem-ui/validation-errors-and-more": "et {{0}} autre erreurs.", "@sage/xtrem-ui/validation-errors-number": "Nombre d'erreurs détectées sur cette ligne", "@sage/xtrem-ui/validation-errors-total": "{{0}} erreurs", "@sage/xtrem-ui/validation-errors-unknown": "Erreur de validation inconnue", "@sage/xtrem-ui/validation-errors-with-number-grid": "Il y a {{0}} erreurs dans le tableau suivant : {{1}}.", "@sage/xtrem-ui/validation-errors-with-number-pod": "Il y a {{0}} erreurs dans la collection suivante : {{1}}.", "@sage/xtrem-ui/visual-process-lookup-page-dialog-title": "Sé<PERSON><PERSON><PERSON> une page", "@sage/xtrem-ui/visual-process-lookup-page-path-column": "<PERSON><PERSON><PERSON> de <PERSON> page", "@sage/xtrem-ui/visual-process-lookup-page-title-column": "<PERSON><PERSON><PERSON> de la page", "@sage/xtrem-ui/visual-process-transactions-not-supported": "Les transactions de page ne sont pas prises en charge.", "@sage/xtrem-ui/widget-action-create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/widget-action-create-help": "A<PERSON><PERSON> raccourci pour créer une nouvelle fiche", "@sage/xtrem-ui/widget-action-see-all": "Tout voir", "@sage/xtrem-ui/widget-action-see-all-help": "Afficher les fiches en fonction des données sélectionnées", "@sage/xtrem-ui/widget-actions": "Actions", "@sage/xtrem-ui/widget-editor-action-decimal-digits-mandatory": "Renseignez un nombre.", "@sage/xtrem-ui/widget-editor-action-goes-to": "<PERSON><PERSON>", "@sage/xtrem-ui/widget-editor-action-label-mandatory": "V<PERSON> devez ajouter un titre.", "@sage/xtrem-ui/widget-editor-action-page-path": "Chemin", "@sage/xtrem-ui/widget-editor-action-page-title": "Titre", "@sage/xtrem-ui/widget-editor-add": "Ajouter", "@sage/xtrem-ui/widget-editor-basic-step-missing-node": "Ajoutez un node.", "@sage/xtrem-ui/widget-editor-basic-step-missing-title": "A<PERSON><PERSON>z un titre.", "@sage/xtrem-ui/widget-editor-basic-step-title": "{{stepIndex}}. Sélectionner un widget pour démarrer.", "@sage/xtrem-ui/widget-editor-cancel-edit": "Annuler", "@sage/xtrem-ui/widget-editor-content-formatting": "Décimales", "@sage/xtrem-ui/widget-editor-content-step-subtitle": "Sélectionner un champ pour le regroupement et des champs pour l'utilisation des filtres.", "@sage/xtrem-ui/widget-editor-content-step-title": "{{stepIndex}}. Ajouter votre contenu.", "@sage/xtrem-ui/widget-editor-content-step-vertical-axes": "Axes verticaux", "@sage/xtrem-ui/widget-editor-data-step-title": "{{stepIndex}}. Sélectionner les données à ajouter à votre widget.", "@sage/xtrem-ui/widget-editor-entry-node": "Source de données", "@sage/xtrem-ui/widget-editor-filter-step-title": "{{stepIndex}}. Ajouter vos filtres.", "@sage/xtrem-ui/widget-editor-grouping-method": "Méthode de regroupement", "@sage/xtrem-ui/widget-editor-grouping-property": "Propriété de regroupement", "@sage/xtrem-ui/widget-editor-horizontal-axis": "Axe horizontal", "@sage/xtrem-ui/widget-editor-horizontal-axis-label": "Libellé axe horizontal", "@sage/xtrem-ui/widget-editor-icon": "Icône", "@sage/xtrem-ui/widget-editor-layout-step-no-actions-in-preview": "Les actions ne fonctionnent pas dans la prévisualisation. Elles fonctionnent lorsque vous ajoutez le widget à votre tableau de bord.", "@sage/xtrem-ui/widget-editor-layout-step-title": "{{stepIndex}}. <PERSON><PERSON><PERSON> votre mise en page.", "@sage/xtrem-ui/widget-editor-max-num-of-values": "Nombre maximum de valeurs", "@sage/xtrem-ui/widget-editor-no-filterable-properties": "Vous ne pouvez pas filtrer les valeurs actuelles. Vous pouvez filtrer des données différentes ou continuer sans filtre.", "@sage/xtrem-ui/widget-editor-sorting-step-title": "{{stepIndex}}. Définir le tri.", "@sage/xtrem-ui/widget-editor-step-basic-title": "Widget", "@sage/xtrem-ui/widget-editor-step-content-title": "Contenu", "@sage/xtrem-ui/widget-editor-step-data-title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/widget-editor-step-filters-title": "Filtres", "@sage/xtrem-ui/widget-editor-step-layout-title": "Mise en page", "@sage/xtrem-ui/widget-editor-step-sorting-title": "Tri", "@sage/xtrem-ui/widget-editor-subtitle": "Sous-titre", "@sage/xtrem-ui/widget-editor-title-edit": "Modifier le widget", "@sage/xtrem-ui/widget-editor-title-new": "Nouveau widget", "@sage/xtrem-ui/widget-editor-type-bar-chart": "Graphique à barres", "@sage/xtrem-ui/widget-editor-type-bar-chart-description": "Affichage visuel des données sous forme de barres", "@sage/xtrem-ui/widget-editor-type-indicator-tile": "Indicateur", "@sage/xtrem-ui/widget-editor-type-indicator-tile-description": "Afficher les principales mises à jour des enregistrements", "@sage/xtrem-ui/widget-editor-type-line-chart": "Graphique linéaire", "@sage/xtrem-ui/widget-editor-type-line-chart-description": "Affichage visuel des données sous forme de lignes", "@sage/xtrem-ui/widget-editor-type-table": "Table", "@sage/xtrem-ui/widget-editor-type-table-description": "Affichage des données dans des lignes et de colonnes", "@sage/xtrem-ui/widget-editor-update": "Mettre à jour", "@sage/xtrem-ui/widget-editor-vertical-axis-label": "Libellé axe vertical", "@sage/xtrem-ui/widget-editor-widget-category": "<PERSON><PERSON><PERSON><PERSON> de widgets", "@sage/xtrem-ui/widget-editor-widget-title": "Intitulé du widget", "@sage/xtrem-ui/widget-preview-label": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/wizard-finish": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/wizard-next": "Suivant", "@sage/xtrem-ui/wizard-previous": "Précédent", "@sage/xtrem-ui/workflow-add-trigger-event": "Ajouter un événement déclenchant", "@sage/xtrem-ui/workflow-collapse": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/workflow-component-add-action": "Ajouter action", "@sage/xtrem-ui/workflow-component-add-condition": "Ajouter une condition", "@sage/xtrem-ui/workflow-component-add-step": "Ajouter une étape", "@sage/xtrem-ui/workflow-component-edge-false-path": "Else", "@sage/xtrem-ui/workflow-component-edge-true-path": "If true", "@sage/xtrem-ui/workflow-component-header-label-action": "Faire", "@sage/xtrem-ui/workflow-component-header-label-condition": "Condition", "@sage/xtrem-ui/workflow-component-header-label-start": "Commence par", "@sage/xtrem-ui/workflow-component-wizard-event-selection": "Sélection d'événements", "@sage/xtrem-ui/workflow-component-wizard-step-configuration": "Configuration", "@sage/xtrem-ui/workflow-component-wizard-title-action": "Galerie d'actions", "@sage/xtrem-ui/workflow-component-wizard-title-trigger": "Galerie de déclencheurs", "@sage/xtrem-ui/workflow-component-wizard-trigger-selection": "Sélection de déclencheurs", "@sage/xtrem-ui/workflow-delete-node-chain-message": "Si vous supprimez cette étape, les étapes suivantes n'ayant pas d'autres liens le sont également.", "@sage/xtrem-ui/workflow-delete-node-chain-title": "Supprimer l'étape", "@sage/xtrem-ui/workflow-empty": "Ce workflow est actuellement vide.", "@sage/xtrem-ui/workflow-expand": "Développer", "@sage/xtrem-ui/workflow-fit-view": "Adapter la vue à l'écran", "@sage/xtrem-ui/workflow-redo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/workflow-undo": "Annuler", "@sage/xtrem-ui/workflow-zoom-in": "Zoom avant", "@sage/xtrem-ui/workflow-zoom-out": "Zoom arri<PERSON>", "@sage/xtrem-ui/yes": "O<PERSON>"}