{"@sage/xtrem-ui/360-view": "Vista 360", "@sage/xtrem-ui/action-button-more": "Más acciones", "@sage/xtrem-ui/action-clone": "Clonar", "@sage/xtrem-ui/action-close": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/action-delete": "Eliminar", "@sage/xtrem-ui/action-edit": "<PERSON><PERSON>", "@sage/xtrem-ui/action-share": "Compartir", "@sage/xtrem-ui/action-tool-bar-items-selected": "Elementos seleccionados: {{count}}", "@sage/xtrem-ui/actions": "Acciones", "@sage/xtrem-ui/add-column": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/add-filter": "<PERSON><PERSON><PERSON> filt<PERSON>", "@sage/xtrem-ui/add-item": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/add-item-in-line": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/add-item-in-line-using-sidebar": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/add-value": "<PERSON><PERSON><PERSON> valor", "@sage/xtrem-ui/aggregation-method": "Método de agregación", "@sage/xtrem-ui/async-mutation-dialog-content": "Este proceso está llevando más tiempo de lo previsto. Puedes volver e intentarlo de nuevo más tarde sin perder tu trabajo, seguir esperando o dejar que se ejecute en segundo plano.", "@sage/xtrem-ui/async-mutation-dialog-title": "Operación en curso", "@sage/xtrem-ui/async-operation-error": "Ha habido un error al procesar la operación.", "@sage/xtrem-ui/attachment-options-menu-all": "Todos los tipos de archivo", "@sage/xtrem-ui/attachment-options-menu-documents": "Documentos", "@sage/xtrem-ui/attachment-options-menu-images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/attachment-options-menu-others": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/attachments": "Archivos adjuntos", "@sage/xtrem-ui/average": "Promedio", "@sage/xtrem-ui/axes": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/bar-code-component-not-available": "El código de barras no está disponible.", "@sage/xtrem-ui/between": "<PERSON><PERSON>", "@sage/xtrem-ui/bulk-action-async-export": "Exportar", "@sage/xtrem-ui/bulk-action-dialog-content": "Realizar esta acción en los elementos seleccionados: {{itemCount}}", "@sage/xtrem-ui/bulk-action-error": "La acción no se ha iniciado. Inténtalo de nuevo.", "@sage/xtrem-ui/bulk-action-print": "", "@sage/xtrem-ui/bulk-action-started": "La acción se ha iniciado en los elementos seleccionados.", "@sage/xtrem-ui/bulk-actions-bar-selected": "Elementos seleccionados: {{count}}", "@sage/xtrem-ui/business-action-in-progress": "El proceso se está ejecutando en segundo plano.", "@sage/xtrem-ui/calendar-month": "<PERSON><PERSON>", "@sage/xtrem-ui/calendar-view-3-day": "3 días", "@sage/xtrem-ui/calendar-view-day": "Día", "@sage/xtrem-ui/calendar-view-month": "<PERSON><PERSON>", "@sage/xtrem-ui/calendar-view-week": "Se<PERSON>", "@sage/xtrem-ui/calendar-view-year": "<PERSON><PERSON>", "@sage/xtrem-ui/cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/carbon-date-format": "dd/MM/yyyy", "@sage/xtrem-ui/chart-component-no-data": "Sin datos", "@sage/xtrem-ui/clear-filter-text": "Borrar texto de filtro", "@sage/xtrem-ui/clear-floating-filter": "<PERSON><PERSON><PERSON> filtros", "@sage/xtrem-ui/clear-input-text": "Bo<PERSON>r", "@sage/xtrem-ui/clear-selection": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/close-full-screen": "Salir de pantalla completa", "@sage/xtrem-ui/close-header-section": "<PERSON><PERSON><PERSON> cab<PERSON>", "@sage/xtrem-ui/close-record": "Cerrar registro", "@sage/xtrem-ui/collapse-section": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/collection-data-service-more-errors": "y {{0}} errores más", "@sage/xtrem-ui/consumer-mock-clear-path": "Acortar URL", "@sage/xtrem-ui/consumer-mock-hide-test-ids": "Ocultar ids.", "@sage/xtrem-ui/consumer-mock-show-test-ids": "Mostrar ids.", "@sage/xtrem-ui/contains": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/continue": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/copy-error-detail": "<PERSON><PERSON><PERSON> de<PERSON><PERSON> de error", "@sage/xtrem-ui/copy-error-details": "<PERSON><PERSON><PERSON> de<PERSON><PERSON> de error", "@sage/xtrem-ui/create-a-new-view": "Crear vista", "@sage/xtrem-ui/create-a-view": "Crear vista", "@sage/xtrem-ui/create-error": "Ha habido un error en la creación: {{0}}.", "@sage/xtrem-ui/create-new-dashboard": "Plantillas de cuadro de mando", "@sage/xtrem-ui/crud-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/crud-confirm": "Confirmar", "@sage/xtrem-ui/crud-create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/crud-create-success": "El registro se ha creado.", "@sage/xtrem-ui/crud-delete": "Eliminar", "@sage/xtrem-ui/crud-delete-record-button": "Eliminar", "@sage/xtrem-ui/crud-delete-record-warning-message": "¿Quieres eliminar este registro?", "@sage/xtrem-ui/crud-delete-record-warning-title": "Eliminar registro", "@sage/xtrem-ui/crud-delete-successfully": "El registro se ha eliminado.", "@sage/xtrem-ui/crud-duplicate": "Duplicar", "@sage/xtrem-ui/crud-duplicate-dialog-subtitle": "Completa los siguientes campos para duplicar el registro.", "@sage/xtrem-ui/crud-duplicate-dialog-title": "Duplicar registro", "@sage/xtrem-ui/crud-duplicate-failed": "Ha habido un error al duplicar el registro.", "@sage/xtrem-ui/crud-duplicate-successfully": "El registro se ha duplicado correctamente.", "@sage/xtrem-ui/crud-save": "Guardar", "@sage/xtrem-ui/crud-save-failed": "Ha habido un error al guardar el registro.", "@sage/xtrem-ui/crud-update-success": "El registro se ha actualizado.", "@sage/xtrem-ui/dashboard-actions": "Acciones", "@sage/xtrem-ui/dashboard-add_contact": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-add_note": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-add_site": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-add-widget": "<PERSON><PERSON><PERSON> widget", "@sage/xtrem-ui/dashboard-address": "Dirección", "@sage/xtrem-ui/dashboard-address_name": "Nombre de dirección", "@sage/xtrem-ui/dashboard-addresses": "Direcciones", "@sage/xtrem-ui/dashboard-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-choose_date": "Elegir otro rango de fechas", "@sage/xtrem-ui/dashboard-clear_filter": "<PERSON><PERSON><PERSON> filtro", "@sage/xtrem-ui/dashboard-contact": "Contacto", "@sage/xtrem-ui/dashboard-contact_card_empty_text": "Este widget no tiene contactos.", "@sage/xtrem-ui/dashboard-contact_image": "Imagen de contacto", "@sage/xtrem-ui/dashboard-contact_type": "Tipo de contacto", "@sage/xtrem-ui/dashboard-contacts": "Contactos", "@sage/xtrem-ui/dashboard-create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-create-dashboard": "<PERSON><PERSON><PERSON> cuadro de mando", "@sage/xtrem-ui/dashboard-current_date_filter": "Filtro de fecha actual:", "@sage/xtrem-ui/dashboard-day": "Día", "@sage/xtrem-ui/dashboard-delete": "Eliminar", "@sage/xtrem-ui/dashboard-delete-dashboard-dialog-message": "¿Quieres eliminar este cuadro de mando?", "@sage/xtrem-ui/dashboard-delete-dashboard-dialog-title": "Eliminar cuadro de mando", "@sage/xtrem-ui/dashboard-deleted": "El cuadro de mando se ha eliminado.", "@sage/xtrem-ui/dashboard-duplicate": "Duplicar", "@sage/xtrem-ui/dashboard-duplicated": "El cuadro de mando se ha duplicado.", "@sage/xtrem-ui/dashboard-edit": "<PERSON><PERSON>", "@sage/xtrem-ui/dashboard-editor-add-widget": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-editor-all-widgets": "Todos los widgets", "@sage/xtrem-ui/dashboard-editor-cancel-edit": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-editor-create-dialog-blank-description": "Utiliza una plantilla en blanco y añade los widgets que quieras.", "@sage/xtrem-ui/dashboard-editor-create-dialog-blank-title": "Plantilla en blanco", "@sage/xtrem-ui/dashboard-editor-create-dialog-description": "Selecciona una plantilla para empezar o crea tu propio cuadro de mando. Añade o quita los widgets que quieras para adaptar el cuadro de mando a tus necesidades.", "@sage/xtrem-ui/dashboard-editor-create-dialog-title": "Seleccionar plantilla de cuadro de mando", "@sage/xtrem-ui/dashboard-editor-edit-title": "<PERSON><PERSON>", "@sage/xtrem-ui/dashboard-editor-redo": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-editor-save": "Guardar", "@sage/xtrem-ui/dashboard-editor-save-error": "El cuadro de mando no se ha guardado.", "@sage/xtrem-ui/dashboard-editor-saved-successfully": "El cuadro de mando se ha guardado.", "@sage/xtrem-ui/dashboard-editor-title": "Editor de cuadro de mando", "@sage/xtrem-ui/dashboard-editor-undo": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-editor-widget-edit": "<PERSON><PERSON>", "@sage/xtrem-ui/dashboard-editor-widget-list-add": "Crear widget", "@sage/xtrem-ui/dashboard-editor-widget-list-title": "Widgets", "@sage/xtrem-ui/dashboard-email": "E-mail", "@sage/xtrem-ui/dashboard-empty-heading": "Este cuadro de mando está en blanco.", "@sage/xtrem-ui/dashboard-empty-subtitle": "Añade un widget para personalizarlo.", "@sage/xtrem-ui/dashboard-expand_row": "Expandir fila", "@sage/xtrem-ui/dashboard-failed-to-create": "El cuadro de mando no se ha creado.", "@sage/xtrem-ui/dashboard-failed-to-delete": "El cuadro de mando no se ha eliminado.", "@sage/xtrem-ui/dashboard-failed-to-duplicate": "El cuadro de mando no se ha duplicado.", "@sage/xtrem-ui/dashboard-month": "<PERSON><PERSON>", "@sage/xtrem-ui/dashboard-month_1": "<PERSON><PERSON>", "@sage/xtrem-ui/dashboard-month_10": "Octubre", "@sage/xtrem-ui/dashboard-month_11": "Noviembre", "@sage/xtrem-ui/dashboard-month_12": "Diciembre", "@sage/xtrem-ui/dashboard-month_2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-month_3": "<PERSON><PERSON>", "@sage/xtrem-ui/dashboard-month_4": "Abril", "@sage/xtrem-ui/dashboard-month_5": "Mayo", "@sage/xtrem-ui/dashboard-month_6": "<PERSON><PERSON>", "@sage/xtrem-ui/dashboard-month_7": "<PERSON>", "@sage/xtrem-ui/dashboard-month_8": "Agosto", "@sage/xtrem-ui/dashboard-month_9": "Septiembre", "@sage/xtrem-ui/dashboard-more_options": "Más opciones", "@sage/xtrem-ui/dashboard-next": "Siguient<PERSON>", "@sage/xtrem-ui/dashboard-next_period": "Seleccionar siguiente periodo", "@sage/xtrem-ui/dashboard-no-change": "Sin cambios", "@sage/xtrem-ui/dashboard-note": "<PERSON>a", "@sage/xtrem-ui/dashboard-notes": "Notas", "@sage/xtrem-ui/dashboard-phone_number": "Número de teléfono", "@sage/xtrem-ui/dashboard-position": "Puesto", "@sage/xtrem-ui/dashboard-previous": "Anterior", "@sage/xtrem-ui/dashboard-previous_period": "Seleccionar periodo anterior", "@sage/xtrem-ui/dashboard-quarter": "Trimestre", "@sage/xtrem-ui/dashboard-reload": "Recargar", "@sage/xtrem-ui/dashboard-save": "Guardar", "@sage/xtrem-ui/dashboard-scroll_left": "Desplazar a la izquierda", "@sage/xtrem-ui/dashboard-scroll_right": "Desplazar a la derecha", "@sage/xtrem-ui/dashboard-select_address": "Seleccionar <PERSON>", "@sage/xtrem-ui/dashboard-select_contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>o", "@sage/xtrem-ui/dashboard-settings": "", "@sage/xtrem-ui/dashboard-site": "Planta", "@sage/xtrem-ui/dashboard-site_card_empty_text": "Este widget no tiene direcciones.", "@sage/xtrem-ui/dashboard-table_filter_menu": "Menú de filtros", "@sage/xtrem-ui/dashboard-table_select": "Seleccionar fila", "@sage/xtrem-ui/dashboard-view_switch": "Conmutador de vista", "@sage/xtrem-ui/dashboard-week": "Se<PERSON>", "@sage/xtrem-ui/dashboard-widget-category-others": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-widget-close": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dashboard-widget-refresh": "Actualizar", "@sage/xtrem-ui/dashboard-widget-settings": "Configurar", "@sage/xtrem-ui/dashboard-year": "<PERSON><PERSON>", "@sage/xtrem-ui/date-error-day-range": "Los días van del 1 al 31.", "@sage/xtrem-ui/date-error-month-range": "Los meses van del 1 al 12.", "@sage/xtrem-ui/date-error-year-range": "Los años van de 1800 a 2200.", "@sage/xtrem-ui/date-format": "", "@sage/xtrem-ui/date-format-separator": "/", "@sage/xtrem-ui/date-time-component-aria-label": "Seleccionar fecha y hora", "@sage/xtrem-ui/date-time-range-end-date": "Fin", "@sage/xtrem-ui/date-time-range-start-date": "<PERSON><PERSON>o", "@sage/xtrem-ui/date-time-range-time": "<PERSON><PERSON>", "@sage/xtrem-ui/date-time-range-time-zone": "Zona horaria", "@sage/xtrem-ui/datetime-aria-label": "<PERSON><PERSON> y hora", "@sage/xtrem-ui/datetime-range-aria-label": "<PERSON>ngo de fechas y horas", "@sage/xtrem-ui/datetime-range-end-date-error": "La fecha de fin debe ser posterior a la fecha de inicio.", "@sage/xtrem-ui/default-view-cannot-be-edited": "La vista por defecto no se puede actualizar.", "@sage/xtrem-ui/delete-error": "Ha habido un error en la eliminación: {{0}}.", "@sage/xtrem-ui/delete-record-warning-message": "Vas a eliminar este registro. ¿Confirmar?", "@sage/xtrem-ui/delete-record-warning-title": "Confirmar eliminación", "@sage/xtrem-ui/detailed-icon-name-accounting": "Contabilidad", "@sage/xtrem-ui/detailed-icon-name-addons": "Add-ons", "@sage/xtrem-ui/detailed-icon-name-animal": "Animal", "@sage/xtrem-ui/detailed-icon-name-apple": "Man<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-asset_mgt": "Administración de activos", "@sage/xtrem-ui/detailed-icon-name-award": "Premio", "@sage/xtrem-ui/detailed-icon-name-bag": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-bakery": "Panadería", "@sage/xtrem-ui/detailed-icon-name-barcode": "Código de <PERSON>", "@sage/xtrem-ui/detailed-icon-name-bicycle": "Bicicleta", "@sage/xtrem-ui/detailed-icon-name-binocular": "Prismáticos", "@sage/xtrem-ui/detailed-icon-name-book": "Libro", "@sage/xtrem-ui/detailed-icon-name-bright": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-building": "Edificio", "@sage/xtrem-ui/detailed-icon-name-calculator": "Calculadora", "@sage/xtrem-ui/detailed-icon-name-calendar": "Calendario", "@sage/xtrem-ui/detailed-icon-name-camera": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-card": "Tarjeta", "@sage/xtrem-ui/detailed-icon-name-cart": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-certificate": "Certificado", "@sage/xtrem-ui/detailed-icon-name-check": "Marca de verificación", "@sage/xtrem-ui/detailed-icon-name-checkbox": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-checklist": "Lista de comprobación", "@sage/xtrem-ui/detailed-icon-name-chemical": "Químico", "@sage/xtrem-ui/detailed-icon-name-chess": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-click": "Clic", "@sage/xtrem-ui/detailed-icon-name-clock": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-close": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-clothes": "Ropa", "@sage/xtrem-ui/detailed-icon-name-cloud": "Nube", "@sage/xtrem-ui/detailed-icon-name-coffee": "Café", "@sage/xtrem-ui/detailed-icon-name-compass": "Brújula", "@sage/xtrem-ui/detailed-icon-name-connected": "Conexión establecida", "@sage/xtrem-ui/detailed-icon-name-consultant": "Consultoría", "@sage/xtrem-ui/detailed-icon-name-conversation": "Conversación", "@sage/xtrem-ui/detailed-icon-name-cooking": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-cpu": "CPU", "@sage/xtrem-ui/detailed-icon-name-crowd": "Multitud", "@sage/xtrem-ui/detailed-icon-name-crown": "Corona", "@sage/xtrem-ui/detailed-icon-name-data": "Datos", "@sage/xtrem-ui/detailed-icon-name-database": "Base de datos", "@sage/xtrem-ui/detailed-icon-name-decline": "Bajada", "@sage/xtrem-ui/detailed-icon-name-desktop": "Escritorio", "@sage/xtrem-ui/detailed-icon-name-devices": "Dispositivos", "@sage/xtrem-ui/detailed-icon-name-dollar": "D<PERSON>lar", "@sage/xtrem-ui/detailed-icon-name-download": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-ear": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-ecomm": "Comercio electrónico", "@sage/xtrem-ui/detailed-icon-name-euro": "Euro", "@sage/xtrem-ui/detailed-icon-name-excavator": "Excavadora", "@sage/xtrem-ui/detailed-icon-name-eye": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-factory": "Fábrica", "@sage/xtrem-ui/detailed-icon-name-favorite": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-filter": "Filtro", "@sage/xtrem-ui/detailed-icon-name-financials": "Finanzas", "@sage/xtrem-ui/detailed-icon-name-flag": "Bandera", "@sage/xtrem-ui/detailed-icon-name-folder": "Carpeta", "@sage/xtrem-ui/detailed-icon-name-food": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-form": "Formulario", "@sage/xtrem-ui/detailed-icon-name-gauge": "Medidor", "@sage/xtrem-ui/detailed-icon-name-gears": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-glasses": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-globe": "Globo", "@sage/xtrem-ui/detailed-icon-name-green": "Verde", "@sage/xtrem-ui/detailed-icon-name-handshake": "Apretón de manos", "@sage/xtrem-ui/detailed-icon-name-happy": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-heart": "Corazón", "@sage/xtrem-ui/detailed-icon-name-hide": "Esconder", "@sage/xtrem-ui/detailed-icon-name-holiday": "Vacaciones", "@sage/xtrem-ui/detailed-icon-name-home": "Casa", "@sage/xtrem-ui/detailed-icon-name-hourglass": "Reloj de arena", "@sage/xtrem-ui/detailed-icon-name-hub": "Nodo", "@sage/xtrem-ui/detailed-icon-name-idea": "Idea", "@sage/xtrem-ui/detailed-icon-name-incline": "Subida", "@sage/xtrem-ui/detailed-icon-name-industry": "Industria", "@sage/xtrem-ui/detailed-icon-name-info": "Información", "@sage/xtrem-ui/detailed-icon-name-integration": "Integración", "@sage/xtrem-ui/detailed-icon-name-jewelry": "Joyería", "@sage/xtrem-ui/detailed-icon-name-keys": "Llaves", "@sage/xtrem-ui/detailed-icon-name-lab": "Laboratorio", "@sage/xtrem-ui/detailed-icon-name-label": "Etiqueta", "@sage/xtrem-ui/detailed-icon-name-laptop": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-lightning": "Rayo", "@sage/xtrem-ui/detailed-icon-name-like": "Me gusta", "@sage/xtrem-ui/detailed-icon-name-link": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-locations": "Ubicaciones", "@sage/xtrem-ui/detailed-icon-name-lock": "Candado", "@sage/xtrem-ui/detailed-icon-name-lock_unlocked": "Candado abierto", "@sage/xtrem-ui/detailed-icon-name-mail": "E-mail", "@sage/xtrem-ui/detailed-icon-name-map": "Mapa", "@sage/xtrem-ui/detailed-icon-name-medical": "Medicina", "@sage/xtrem-ui/detailed-icon-name-megaphone": "Megáfono", "@sage/xtrem-ui/detailed-icon-name-memo": "<PERSON><PERSON>and<PERSON>", "@sage/xtrem-ui/detailed-icon-name-microphone": "Micrófono", "@sage/xtrem-ui/detailed-icon-name-minus": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-mouse": "Rat<PERSON>", "@sage/xtrem-ui/detailed-icon-name-newspaper": "Periódico", "@sage/xtrem-ui/detailed-icon-name-note": "<PERSON>a", "@sage/xtrem-ui/detailed-icon-name-notebook": "Libreta", "@sage/xtrem-ui/detailed-icon-name-office": "Oficina", "@sage/xtrem-ui/detailed-icon-name-page": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-payment": "Pago", "@sage/xtrem-ui/detailed-icon-name-payroll": "Nómina", "@sage/xtrem-ui/detailed-icon-name-pen": "Bolígra<PERSON>", "@sage/xtrem-ui/detailed-icon-name-pencil": "Lá<PERSON>z", "@sage/xtrem-ui/detailed-icon-name-person": "<PERSON>a", "@sage/xtrem-ui/detailed-icon-name-phone": "Teléfono", "@sage/xtrem-ui/detailed-icon-name-pin": "Chincheta", "@sage/xtrem-ui/detailed-icon-name-plus": "Más", "@sage/xtrem-ui/detailed-icon-name-point": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-pound": "Libra", "@sage/xtrem-ui/detailed-icon-name-power": "Inicio/apagado", "@sage/xtrem-ui/detailed-icon-name-presentation": "Presentación", "@sage/xtrem-ui/detailed-icon-name-print": "Imprimir", "@sage/xtrem-ui/detailed-icon-name-processing": "Procesando", "@sage/xtrem-ui/detailed-icon-name-puzzle": "Puzzle", "@sage/xtrem-ui/detailed-icon-name-question": "Pregunta", "@sage/xtrem-ui/detailed-icon-name-receipts": "Recibos", "@sage/xtrem-ui/detailed-icon-name-recycle": "Reciclar", "@sage/xtrem-ui/detailed-icon-name-redo": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-remote": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-rocket": "Cohete", "@sage/xtrem-ui/detailed-icon-name-safe": "Caja fuerte", "@sage/xtrem-ui/detailed-icon-name-satelite": "Satélite", "@sage/xtrem-ui/detailed-icon-name-savings": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-scissors": "Tijeras", "@sage/xtrem-ui/detailed-icon-name-server": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-service": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-setting": "Parámetros", "@sage/xtrem-ui/detailed-icon-name-share": "Compartir", "@sage/xtrem-ui/detailed-icon-name-shoes": "Zapatos", "@sage/xtrem-ui/detailed-icon-name-shuffle": "Aleat<PERSON>", "@sage/xtrem-ui/detailed-icon-name-sign": "Se<PERSON>l", "@sage/xtrem-ui/detailed-icon-name-sim": "Tarjeta SIM", "@sage/xtrem-ui/detailed-icon-name-smartphone": "Smartphone", "@sage/xtrem-ui/detailed-icon-name-stationeries": "Papelería", "@sage/xtrem-ui/detailed-icon-name-store": "Tienda", "@sage/xtrem-ui/detailed-icon-name-support": "Soporte", "@sage/xtrem-ui/detailed-icon-name-sync": "Sincronizar", "@sage/xtrem-ui/detailed-icon-name-tab": "Pestaña", "@sage/xtrem-ui/detailed-icon-name-table": "Tabla", "@sage/xtrem-ui/detailed-icon-name-tablet": "Tablet", "@sage/xtrem-ui/detailed-icon-name-thermometer": "Termómetro", "@sage/xtrem-ui/detailed-icon-name-timer": "Cronómetro", "@sage/xtrem-ui/detailed-icon-name-tools": "Herramientas", "@sage/xtrem-ui/detailed-icon-name-travel": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-truck": "Camión", "@sage/xtrem-ui/detailed-icon-name-undo": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-video": "Vídeo", "@sage/xtrem-ui/detailed-icon-name-wallet": "<PERSON><PERSON>", "@sage/xtrem-ui/detailed-icon-name-warehouse": "Almacén", "@sage/xtrem-ui/detailed-icon-name-warning": "Aviso", "@sage/xtrem-ui/detailed-icon-name-weather": "Meteorología", "@sage/xtrem-ui/detailed-icon-name-wireless": "Inalámbrico", "@sage/xtrem-ui/detailed-icon-name-wrench": "<PERSON><PERSON><PERSON> ing<PERSON>a", "@sage/xtrem-ui/detailed-icon-name-writing": "Escritura", "@sage/xtrem-ui/dialog-actions-onload-unhandled-error": "Ha habido un error desconocido. Inténtalo de nuevo.", "@sage/xtrem-ui/dialog-loading": "Cargando...", "@sage/xtrem-ui/dialogs-async-loader-button-keep-waiting": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/dialogs-async-loader-button-notify-me": "Notificar", "@sage/xtrem-ui/dialogs-async-loader-button-stop": "Detener", "@sage/xtrem-ui/dialogs-async-loader-waiting": "Esto puede llevar un tiempo...", "@sage/xtrem-ui/dirty-phantom-row-validation-error-message-mac": "Para poder guardar el documento, selecciona la línea y, luego, \"+ Entrar\" para validarla.", "@sage/xtrem-ui/dirty-phantom-row-validation-error-message-pc": "Para poder guardar el documento, selecciona la línea y, luego, \"Ctrl + Entrar\" para validarla.", "@sage/xtrem-ui/display-errors-back-to-full-display": "Tabla completa", "@sage/xtrem-ui/display-errors-back-to-full-display-tooltip": "Volver a tabla completa", "@sage/xtrem-ui/display-errors-button": "<PERSON><PERSON> errores", "@sage/xtrem-ui/distinct-count": "Recuento de valores únicos", "@sage/xtrem-ui/divisor": "Divisor", "@sage/xtrem-ui/divisor-helper-text": "Reduce los números grandes con un divisor.", "@sage/xtrem-ui/document-metadata": "Metadatos de documento", "@sage/xtrem-ui/drag-drop-file": "Arrastra y suelta los archivos aquí.", "@sage/xtrem-ui/duplicate-error": "Error de duplicación: {{0}}", "@sage/xtrem-ui/empty-state-filter-text-no-results-button": "<PERSON><PERSON><PERSON> filtros", "@sage/xtrem-ui/empty-state-filter-text-no-results-description": "Cambia los criterios o crea un registro para empezar.", "@sage/xtrem-ui/empty-state-filter-text-title": "Sin resultados", "@sage/xtrem-ui/empty-state-text": "Esta lista está en blanco.", "@sage/xtrem-ui/endsWith": "Acaba por", "@sage/xtrem-ui/equals": "Igual a", "@sage/xtrem-ui/error": "Error", "@sage/xtrem-ui/error-detail-copied-to-clipboard": "Los detalles del error se han copiado al portapapeles.", "@sage/xtrem-ui/error-loading-stickers": "Ha habido un error al cargar los adhesivos. Inténtalo de nuevo.", "@sage/xtrem-ui/export-format-csv": "CSV", "@sage/xtrem-ui/export-format-excel": "Excel", "@sage/xtrem-ui/failed-to-refresh-navigation-panel": "Ha habido un error al actualizar el panel de navegación.", "@sage/xtrem-ui/failed-to-save": "Ha habido un error al guardar el registro.", "@sage/xtrem-ui/false": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/field-mandatory": "Introduce un valor.", "@sage/xtrem-ui/field-max-items-value": "{{0}}: puedes seleccionar un máximo de {{1}} elementos.", "@sage/xtrem-ui/field-maximum-date-value": "<PERSON><PERSON> más tardía: {{1}}", "@sage/xtrem-ui/field-maximum-length-value": "<PERSON><PERSON><PERSON> máxima: {{1}}", "@sage/xtrem-ui/field-maximum-value": "Valor máximo: {{1}}", "@sage/xtrem-ui/field-min-items-value": "{{0}}: puedes seleccionar un mínimo de {{1}} elementos.", "@sage/xtrem-ui/field-minimum-date-value": "Primera fecha: {{1}}", "@sage/xtrem-ui/field-minimum-length-value": "<PERSON><PERSON><PERSON> m<PERSON>: {{1}}", "@sage/xtrem-ui/field-minimum-value": "<PERSON><PERSON> mínimo: {{1}}", "@sage/xtrem-ui/field-non-zero": "{{0}} no puede ser cero.", "@sage/xtrem-ui/field-not-valid": "El valor no es válido.", "@sage/xtrem-ui/field-select-mandatory": "Selecciona un valor.", "@sage/xtrem-ui/field-select-mandatory-checkbox": "Marca esta casilla.", "@sage/xtrem-ui/field-select-mandatory-or-enter": "Selecciona o introduce un valor.", "@sage/xtrem-ui/file-component-browse-file": "Examinar archivo", "@sage/xtrem-ui/file-component-browse-files": "Examinar archivos", "@sage/xtrem-ui/file-component-drag-drop": "o \n Arrastra y suelta el archivo aquí.", "@sage/xtrem-ui/file-component-drag-drop-empty": "o \n Esta lista está en blanco. \n Arrastra y suelta el archivo aquí.", "@sage/xtrem-ui/file-component-hide-upload-area": "Ocultar zona de subida", "@sage/xtrem-ui/file-component-onchange-error": "Error al subir el archivo: {{0}}", "@sage/xtrem-ui/file-component-show-upload-area": "Mostrar zona de subida", "@sage/xtrem-ui/file-upload-failed": "Ha habido un error al subir el archivo.", "@sage/xtrem-ui/filter-manager-apply-button": "Aplicar", "@sage/xtrem-ui/filter-manager-cancel-button": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/filter-manager-checkbox": "Activo", "@sage/xtrem-ui/filter-manager-clear-all-button": "<PERSON><PERSON><PERSON> todo", "@sage/xtrem-ui/filter-manager-close": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/filter-manager-invalid-filters": "El valor del filtro no es válido.", "@sage/xtrem-ui/filter-manager-max-value": "<PERSON>or máxi<PERSON>", "@sage/xtrem-ui/filter-manager-min-value": "<PERSON><PERSON> m<PERSON>", "@sage/xtrem-ui/filter-manager-open": "Abrir", "@sage/xtrem-ui/filter-manager-range-end": "<PERSON><PERSON> de fin", "@sage/xtrem-ui/filter-manager-range-start": "Fecha de inicio", "@sage/xtrem-ui/filter-manager-title-header": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/filter-range": "\"{{fieldTitle}}\" est<PERSON> entre \"{{value1}}\" y \"{{value2}}\".", "@sage/xtrem-ui/filter-search-placeholder": "Buscar", "@sage/xtrem-ui/filter-select-lookup-dialog-dialog-title": "Selección", "@sage/xtrem-ui/filter-select-lookup-dialog-failed-fetch": "No se han encontrado estas opciones.", "@sage/xtrem-ui/filter-type": "Tipo de filtro", "@sage/xtrem-ui/filter-value": "Valor de filtro", "@sage/xtrem-ui/filter-value-equals": "\"{{fieldTitle}}\" es \"{{filterValue}}\".", "@sage/xtrem-ui/filter-value-greater-than-equal": "\"{{fieldTitle}}\" debe ser superior a \"{{filterValue}}\".", "@sage/xtrem-ui/filter-value-less-than-equal": "\"{{fieldTitle}}\" debe ser inferior a \"{{filterValue}}\".", "@sage/xtrem-ui/filter-value-not-equal": "\"{{fieldTitle}}\" es diferente a \"{{filterValue}}\".", "@sage/xtrem-ui/floating-filter-label": "Entrada de filtro {{ filterName }}", "@sage/xtrem-ui/footer-actions-more-button": "Más", "@sage/xtrem-ui/form-designer-var-current-date": "<PERSON><PERSON>", "@sage/xtrem-ui/form-designer-var-generation-by": "<PERSON><PERSON> por", "@sage/xtrem-ui/form-designer-var-page-number": "Número de página", "@sage/xtrem-ui/form-designer-var-parameter": "Parámetro \"{{name}}\"", "@sage/xtrem-ui/form-designer-var-total-number-of-pages": "Número total de páginas", "@sage/xtrem-ui/general_invalid_json": "El JSON no es válido.", "@sage/xtrem-ui/general-finish-editing": "Finalizar edición", "@sage/xtrem-ui/general-welcome-page": "Página de inicio", "@sage/xtrem-ui/generic-no": "No", "@sage/xtrem-ui/generic-yes": "Sí", "@sage/xtrem-ui/greaterThan": "Superior a", "@sage/xtrem-ui/greaterThanOrEqual": "Superior o igual a", "@sage/xtrem-ui/group-by": "Agrupar por", "@sage/xtrem-ui/group-by-day": "Día", "@sage/xtrem-ui/group-by-month": "<PERSON><PERSON>", "@sage/xtrem-ui/group-by-this-column": "Agrupar por esta columna", "@sage/xtrem-ui/group-by-year": "<PERSON><PERSON>", "@sage/xtrem-ui/helper-text-contains": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/helper-text-ends-with": "Acaba por", "@sage/xtrem-ui/helper-text-matches": "Coincide con", "@sage/xtrem-ui/helper-text-starts-with": "Empieza por", "@sage/xtrem-ui/hide-floating-filters": "Ocultar filtros de tabla", "@sage/xtrem-ui/hide-technical-details": "Ocultar de<PERSON>les técnicos", "@sage/xtrem-ui/hours": "<PERSON><PERSON>", "@sage/xtrem-ui/image-component-add-image": "<PERSON><PERSON><PERSON>n", "@sage/xtrem-ui/image-component-not-available": "La imagen no está disponible.", "@sage/xtrem-ui/image-field-remove-image": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/infinite-indicator-end": "Sin fecha de fin", "@sage/xtrem-ui/infinite-indicator-start": "Sin fecha de inicio", "@sage/xtrem-ui/insight-plural": "{{insightCount}} resultados", "@sage/xtrem-ui/insight-singular": "1 resultado", "@sage/xtrem-ui/invalid-boolean": "El booleano no es válido.", "@sage/xtrem-ui/invalid-date": "La fecha no es válida.", "@sage/xtrem-ui/invalid-file-type-message": "{{0}} no está permitido por motivos de seguridad.", "@sage/xtrem-ui/invalid-integer": "El número entero no es válido.", "@sage/xtrem-ui/invalid-number": "El número no es válido.", "@sage/xtrem-ui/invalid-range": "El rango no es válido.", "@sage/xtrem-ui/invalid-response-no-data": "La respuesta no es válida. Sin datos", "@sage/xtrem-ui/invalid-value": "El valor no es válido.", "@sage/xtrem-ui/Label": "Etiqueta", "@sage/xtrem-ui/last-30-days": "Últimos 30 días", "@sage/xtrem-ui/last-7-days": "Últimos 7 días", "@sage/xtrem-ui/last-thirty-days": "Últimos 30 días", "@sage/xtrem-ui/lessThan": "Inferior a", "@sage/xtrem-ui/lessThanOrEqual": "Inferior o igual a", "@sage/xtrem-ui/link-error-numbers-tooltip": "Información de error", "@sage/xtrem-ui/link-error-quantity": "1 error", "@sage/xtrem-ui/link-errors-quantity": "{{0}} errores", "@sage/xtrem-ui/list-printing": "Seleccionar informe", "@sage/xtrem-ui/list-printing-assignment": "<PERSON><PERSON><PERSON> informe", "@sage/xtrem-ui/lookup-dialog-confirm-select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/lookup-dialog-create-new-item": "<PERSON><PERSON>r registro", "@sage/xtrem-ui/lookup-dialog-dialog-title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/lookup-dialog-failed-fetch": "Ha habido un error al cargar las opciones.", "@sage/xtrem-ui/main-list-refresh": "Actualizar", "@sage/xtrem-ui/matches": "Coincide con", "@sage/xtrem-ui/maximum": "Máximo", "@sage/xtrem-ui/message-type-ai": "IA", "@sage/xtrem-ui/message-type-error": "Error", "@sage/xtrem-ui/message-type-info": "Información", "@sage/xtrem-ui/message-type-information": "Información", "@sage/xtrem-ui/message-type-success": "Confirmación", "@sage/xtrem-ui/message-type-warning": "Aviso", "@sage/xtrem-ui/mime-type/application/atom+xml": "Atom XML Feed", "@sage/xtrem-ui/mime-type/application/ecmascript": "ECMAScript", "@sage/xtrem-ui/mime-type/application/font-woff": "Web Open Font Format", "@sage/xtrem-ui/mime-type/application/font-woff2": "Web Open Font Format 2", "@sage/xtrem-ui/mime-type/application/graphql": "GraphQL Query", "@sage/xtrem-ui/mime-type/application/java-archive": "Java Archive", "@sage/xtrem-ui/mime-type/application/javascript": "JavaScript", "@sage/xtrem-ui/mime-type/application/json": "JSON", "@sage/xtrem-ui/mime-type/application/ld+json": "JSON-LD Document", "@sage/xtrem-ui/mime-type/application/mac-binhex40": "BinHex Archive", "@sage/xtrem-ui/mime-type/application/mathml+xml": "MathML Document", "@sage/xtrem-ui/mime-type/application/ms-visio": "MS Visio document", "@sage/xtrem-ui/mime-type/application/ms-visio.stencil.macroEnabled.12": "MS Visio document with macros", "@sage/xtrem-ui/mime-type/application/ms-visio.template": "MS Visio template", "@sage/xtrem-ui/mime-type/application/msword": "MS Word Document", "@sage/xtrem-ui/mime-type/application/octet-stream": "Binary Data", "@sage/xtrem-ui/mime-type/application/pdf": "PDF Document", "@sage/xtrem-ui/mime-type/application/postscript": "PostScript", "@sage/xtrem-ui/mime-type/application/rdf+xml": "RDF Document", "@sage/xtrem-ui/mime-type/application/rss+xml": "RSS XML Feed", "@sage/xtrem-ui/mime-type/application/rtf": "Rich Text Format", "@sage/xtrem-ui/mime-type/application/sql": "SQL Database", "@sage/xtrem-ui/mime-type/application/vnd.amazon.ebook": "Amazon Kindle eBook", "@sage/xtrem-ui/mime-type/application/vnd.android.package-archive": "Android APK", "@sage/xtrem-ui/mime-type/application/vnd.apple.installer+xml": "Apple Installer Package", "@sage/xtrem-ui/mime-type/application/vnd.apple.keynote": "Apple Keynote", "@sage/xtrem-ui/mime-type/application/vnd.apple.numbers": "Apple Numbers", "@sage/xtrem-ui/mime-type/application/vnd.apple.pages": "Apple Pages", "@sage/xtrem-ui/mime-type/application/vnd.google-earth.kml+xml": "Google Earth KML", "@sage/xtrem-ui/mime-type/application/vnd.google-earth.kmz": "Google Earth KMZ", "@sage/xtrem-ui/mime-type/application/vnd.mozilla.xul+xml": "Mozilla XUL", "@sage/xtrem-ui/mime-type/application/vnd.ms-access": "Microsoft Access Database", "@sage/xtrem-ui/mime-type/application/vnd.ms-excel": "MS Excel Document", "@sage/xtrem-ui/mime-type/application/vnd.ms-fontobject": "Embedded OpenType Font", "@sage/xtrem-ui/mime-type/application/vnd.ms-powerpoint": "MS PowerPoint Document", "@sage/xtrem-ui/mime-type/application/vnd.ms-project": "Microsoft Project Document", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.graphics": "OpenDocument Graphics", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.presentation": "OpenDocument Presentation", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.spreadsheet": "OpenDocument Spreadsheet", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.text": "OpenDocument Text", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.presentationml.presentation": "MS PowerPoint Document", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "MS Excel Document", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.wordprocessingml.document": "MS Word Document", "@sage/xtrem-ui/mime-type/application/vnd.rn-realmedia": "RealMedia", "@sage/xtrem-ui/mime-type/application/vnd.wap.wmlc": "WMLC Document", "@sage/xtrem-ui/mime-type/application/x-7z-compressed": "7z Archive", "@sage/xtrem-ui/mime-type/application/x-abiword": "AbiWord Document", "@sage/xtrem-ui/mime-type/application/x-bzip": "Bzip Archive", "@sage/xtrem-ui/mime-type/application/x-bzip2": "Bzip2 Archive", "@sage/xtrem-ui/mime-type/application/x-cd-image": "CD Image", "@sage/xtrem-ui/mime-type/application/x-chrome-extension": "Chrome Extension", "@sage/xtrem-ui/mime-type/application/x-cocoa": "Cocoa Archive", "@sage/xtrem-ui/mime-type/application/x-csh": "C <PERSON>", "@sage/xtrem-ui/mime-type/application/x-deb": "Debian Package", "@sage/xtrem-ui/mime-type/application/x-dvi": "DVI Document", "@sage/xtrem-ui/mime-type/application/x-font-opentype": "OpenType Font", "@sage/xtrem-ui/mime-type/application/x-font-otf": "OpenType Font", "@sage/xtrem-ui/mime-type/application/x-font-ttf": "TrueType Font", "@sage/xtrem-ui/mime-type/application/x-font-woff": "Web Open Font Format", "@sage/xtrem-ui/mime-type/application/x-font-woff2": "Web Open Font Format 2", "@sage/xtrem-ui/mime-type/application/x-gtar": "GNU Tar Archive", "@sage/xtrem-ui/mime-type/application/x-gzip": "GZIP Archive", "@sage/xtrem-ui/mime-type/application/x-hdf": "Hierarchical Data Format (HDF)", "@sage/xtrem-ui/mime-type/application/x-httpd-php": "PHP Script", "@sage/xtrem-ui/mime-type/application/x-httpd-php-source": "PHP Source Code", "@sage/xtrem-ui/mime-type/application/x-java-applet": "Java Applet", "@sage/xtrem-ui/mime-type/application/x-java-archive": "Java Archive (JAR)", "@sage/xtrem-ui/mime-type/application/x-java-archive-diff": "Java Archive Diff", "@sage/xtrem-ui/mime-type/application/x-java-jnlp-file": "JNLP File", "@sage/xtrem-ui/mime-type/application/x-javascript": "Old JavaScript", "@sage/xtrem-ui/mime-type/application/x-latex": "LaTeX Document", "@sage/xtrem-ui/mime-type/application/x-lzh-compressed": "LZH Archive", "@sage/xtrem-ui/mime-type/application/x-makeself": "Makeself Archive", "@sage/xtrem-ui/mime-type/application/x-mif": "FrameMaker Interchange Format", "@sage/xtrem-ui/mime-type/application/x-msaccess": "MS Access Database", "@sage/xtrem-ui/mime-type/application/x-msdownload": "MS Download", "@sage/xtrem-ui/mime-type/application/x-msmetafile": "Windows Metafile", "@sage/xtrem-ui/mime-type/application/x-msmoney": "MS Money", "@sage/xtrem-ui/mime-type/application/x-perl": "<PERSON><PERSON>", "@sage/xtrem-ui/mime-type/application/x-pilot": "Pilot Archive", "@sage/xtrem-ui/mime-type/application/x-rar-compressed": "RAR Archive", "@sage/xtrem-ui/mime-type/application/x-redhat-package-manager": "RedHat Package", "@sage/xtrem-ui/mime-type/application/x-rpm": "RPM Package", "@sage/xtrem-ui/mime-type/application/x-sea": "Sea Archive", "@sage/xtrem-ui/mime-type/application/x-sh": "Bash Shell Script", "@sage/xtrem-ui/mime-type/application/x-shockwave-flash": "Flash Animation", "@sage/xtrem-ui/mime-type/application/x-sql": "SQL Database", "@sage/xtrem-ui/mime-type/application/x-stuffit": "StuffIt Archive", "@sage/xtrem-ui/mime-type/application/x-tar": "TAR Archive", "@sage/xtrem-ui/mime-type/application/x-tcl": "Tcl Script", "@sage/xtrem-ui/mime-type/application/x-tex": "TeX Document", "@sage/xtrem-ui/mime-type/application/x-texinfo": "Texinfo Document", "@sage/xtrem-ui/mime-type/application/x-troff": "T<PERSON>ff Document", "@sage/xtrem-ui/mime-type/application/x-vrml": "VRML", "@sage/xtrem-ui/mime-type/application/x-www-form-urlencoded": "Form URL Encoded Data", "@sage/xtrem-ui/mime-type/application/x-x509-ca-cert": "X.509 Certificate", "@sage/xtrem-ui/mime-type/application/x-xpinstall": "Mozilla XPI Install", "@sage/xtrem-ui/mime-type/application/xhtml+xml": "XHTML", "@sage/xtrem-ui/mime-type/application/xml": "XML", "@sage/xtrem-ui/mime-type/application/xslt+xml": "XSLT", "@sage/xtrem-ui/mime-type/application/zip": "ZIP Archive", "@sage/xtrem-ui/mime-type/audio/aac": "AAC Audio", "@sage/xtrem-ui/mime-type/audio/amr": "AMR Audio", "@sage/xtrem-ui/mime-type/audio/midi": "MIDI Audio", "@sage/xtrem-ui/mime-type/audio/mpeg": "MP3 Audio", "@sage/xtrem-ui/mime-type/audio/ogg": "Ogg Audio", "@sage/xtrem-ui/mime-type/audio/vnd.rn-realaudio": "RealAudio", "@sage/xtrem-ui/mime-type/audio/wav": "WAV Audio", "@sage/xtrem-ui/mime-type/audio/x-m4a": "M4A Audio", "@sage/xtrem-ui/mime-type/audio/x-matroska": "Matroska Audio", "@sage/xtrem-ui/mime-type/audio/x-mpegurl": "MPEG URL Stream", "@sage/xtrem-ui/mime-type/audio/x-ms-wax": "WMA Audio Playlist", "@sage/xtrem-ui/mime-type/audio/x-ms-wma": "Windows Media Audio", "@sage/xtrem-ui/mime-type/audio/x-pn-realaudio": "RealAudio", "@sage/xtrem-ui/mime-type/audio/x-pn-realaudio-plugin": "RealAudio Plugin", "@sage/xtrem-ui/mime-type/audio/x-realaudio": "RealAudio", "@sage/xtrem-ui/mime-type/audio/x-wav": "WAV Audio", "@sage/xtrem-ui/mime-type/chemical/x-pdb": "Protein Data Bank", "@sage/xtrem-ui/mime-type/image/bmp": "Bitmap Image", "@sage/xtrem-ui/mime-type/image/cgm": "Computer Graphics Metafile", "@sage/xtrem-ui/mime-type/image/gif": "GIF Image", "@sage/xtrem-ui/mime-type/image/jpeg": "JPEG Image", "@sage/xtrem-ui/mime-type/image/png": "PNG Image", "@sage/xtrem-ui/mime-type/image/svg+xml": "SVG Image", "@sage/xtrem-ui/mime-type/image/tiff": "TIFF Image", "@sage/xtrem-ui/mime-type/image/vnd.microsoft.icon": "ICO Image", "@sage/xtrem-ui/mime-type/image/vnd.wap.wbmp": "WBMP Image", "@sage/xtrem-ui/mime-type/image/webp": "WebP Image", "@sage/xtrem-ui/mime-type/image/x-cmu-raster": "CMU Image", "@sage/xtrem-ui/mime-type/image/x-icon": "Icon Image", "@sage/xtrem-ui/mime-type/image/x-jng": "JNG Image", "@sage/xtrem-ui/mime-type/image/x-ms-bmp": "Bitmap Image", "@sage/xtrem-ui/mime-type/image/x-portable-anymap": "Portable AnyMap Image", "@sage/xtrem-ui/mime-type/image/x-portable-bitmap": "Portable Bitmap Image", "@sage/xtrem-ui/mime-type/image/x-portable-graymap": "Portable Graymap Image", "@sage/xtrem-ui/mime-type/image/x-portable-pixmap": "Portable Pixmap Image", "@sage/xtrem-ui/mime-type/image/x-rgb": "RGB Image", "@sage/xtrem-ui/mime-type/image/x-xbitmap": "X Bitmap Image", "@sage/xtrem-ui/mime-type/image/x-xpixmap": "X Pixmap Image", "@sage/xtrem-ui/mime-type/image/x-xwindowdump": "X Window Dump Image", "@sage/xtrem-ui/mime-type/model/vrml": "VRML Model", "@sage/xtrem-ui/mime-type/multipart/form-data": "Multipart Form Data", "@sage/xtrem-ui/mime-type/text/cache-manifest": "<PERSON><PERSON>", "@sage/xtrem-ui/mime-type/text/calendar": "iCalendar", "@sage/xtrem-ui/mime-type/text/css": "CSS", "@sage/xtrem-ui/mime-type/text/csv": "CSV (Comma-Separated Values)", "@sage/xtrem-ui/mime-type/text/ecmascript": "ECMAScript", "@sage/xtrem-ui/mime-type/text/html": "HTML", "@sage/xtrem-ui/mime-type/text/javascript": "JavaScript", "@sage/xtrem-ui/mime-type/text/markdown": "Markdown Document", "@sage/xtrem-ui/mime-type/text/mathml": "MathML", "@sage/xtrem-ui/mime-type/text/plain": "Plain Text", "@sage/xtrem-ui/mime-type/text/richtext": "Texto enriquecido", "@sage/xtrem-ui/mime-type/text/rtf": "Rich Text Format", "@sage/xtrem-ui/mime-type/text/tab-separated-values": "Tab-Separated Values", "@sage/xtrem-ui/mime-type/text/vnd.sun.j2me.app-descriptor": "J2ME App Descriptor", "@sage/xtrem-ui/mime-type/text/vnd.wap.wml": "WML", "@sage/xtrem-ui/mime-type/text/vnd.wap.wmlscript": "WMLScript", "@sage/xtrem-ui/mime-type/text/vtt": "WebVTT", "@sage/xtrem-ui/mime-type/text/x-component": "HTC Component", "@sage/xtrem-ui/mime-type/text/x-cross-domain-policy": "Cross-domain Policy", "@sage/xtrem-ui/mime-type/text/x-fortran": "Fortran Source Code", "@sage/xtrem-ui/mime-type/text/xml": "XML Document", "@sage/xtrem-ui/mime-type/video/3gpp": "3GPP Video", "@sage/xtrem-ui/mime-type/video/mp2t": "MPEG-2 TS Video", "@sage/xtrem-ui/mime-type/video/mp4": "MP4 Video", "@sage/xtrem-ui/mime-type/video/mpeg": "MPEG Video", "@sage/xtrem-ui/mime-type/video/ogg": "Ogg Video", "@sage/xtrem-ui/mime-type/video/quicktime": "QuickTime Video", "@sage/xtrem-ui/mime-type/video/vnd.rn-realvideo": "RealVideo", "@sage/xtrem-ui/mime-type/video/webm": "WebM Video", "@sage/xtrem-ui/mime-type/video/x-flv": "FLV Video", "@sage/xtrem-ui/mime-type/video/x-m4v": "M4V Video", "@sage/xtrem-ui/mime-type/video/x-matroska": "Matroska Video", "@sage/xtrem-ui/mime-type/video/x-mng": "MNG Video", "@sage/xtrem-ui/mime-type/video/x-ms-asf": "ASF Video", "@sage/xtrem-ui/mime-type/video/x-ms-wmv": "WMV Video", "@sage/xtrem-ui/mime-type/video/x-ms-wvx": "WMV Video Playlist", "@sage/xtrem-ui/mime-type/video/x-msvideo": "AVI Video", "@sage/xtrem-ui/minimum": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/minutes": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/mobile-table-load-more": "<PERSON>gar más", "@sage/xtrem-ui/multi-file-deposit-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/multi-file-deposit-create-tag": "Crear etiqueta", "@sage/xtrem-ui/multi-file-deposit-description": "Descripción", "@sage/xtrem-ui/multi-file-deposit-edit-details": "Editar detalles de archivo", "@sage/xtrem-ui/multi-file-deposit-filename": "Archivo", "@sage/xtrem-ui/multi-file-deposit-modified": "Modificado", "@sage/xtrem-ui/multi-file-deposit-open-preview": "Abrir vista previa", "@sage/xtrem-ui/multi-file-deposit-remove": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/multi-file-deposit-size": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/multi-file-deposit-status": "Estado", "@sage/xtrem-ui/multi-file-deposit-status-created": "En curso", "@sage/xtrem-ui/multi-file-deposit-status-upload-cancelled": "Cancelado", "@sage/xtrem-ui/multi-file-deposit-status-upload-failed": "Error", "@sage/xtrem-ui/multi-file-deposit-status-upload-rejected": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/multi-file-deposit-status-uploaded": "Pendiente", "@sage/xtrem-ui/multi-file-deposit-status-verified": "Subido", "@sage/xtrem-ui/multi-file-deposit-tag-description": "Descripción", "@sage/xtrem-ui/multi-file-deposit-tag-id": "Id.", "@sage/xtrem-ui/multi-file-deposit-tag-title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/multi-file-deposit-tags": "Etiquetas", "@sage/xtrem-ui/multi-file-deposit-title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/multi-file-deposit-type": "Tipo", "@sage/xtrem-ui/multi-file-deposit-uploaded": "Subido", "@sage/xtrem-ui/multi-file-deposit-uploaded-by": "Usuario", "@sage/xtrem-ui/multi-file-upload-cancel": "La subida del archivo se va a cancelar.", "@sage/xtrem-ui/multi-file-upload-cancel-title": "Cancelar subida", "@sage/xtrem-ui/multi-file-upload-remove": "El archivo adjunto se va a quitar.", "@sage/xtrem-ui/multi-file-upload-remove-title": "Quitar archivo", "@sage/xtrem-ui/multipleRange": "Igual a", "@sage/xtrem-ui/must-be-a-number": "Introduce un número.", "@sage/xtrem-ui/must-be-between-1-and-365": "Introduce un número entre 1 y 365.", "@sage/xtrem-ui/must-be-between-zero-and-four": "El valor debe estar entre 0 y 4.", "@sage/xtrem-ui/must-be-greater-than-zero": "El valor debe ser superior o igual a 0.", "@sage/xtrem-ui/name": "Nombre", "@sage/xtrem-ui/navigation-panel-failed": "Ha habido un error al cargar los elementos de la lista de selección. Inténtalo de nuevo.", "@sage/xtrem-ui/navigation-panel-my-view": "Mis selecciones", "@sage/xtrem-ui/navigation-panel-no-filter": "Todo", "@sage/xtrem-ui/navigation-panel-no-results": "No hay datos que mostrar.", "@sage/xtrem-ui/navigation-panel-type-to-search": "Buscar...", "@sage/xtrem-ui/nested-field-errors": "Errores de campo anidado", "@sage/xtrem-ui/new": "Nuevo", "@sage/xtrem-ui/next-day": "<PERSON><PERSON> siguiente", "@sage/xtrem-ui/next-month": "<PERSON><PERSON> si<PERSON>", "@sage/xtrem-ui/next-record": "Siguiente registro", "@sage/xtrem-ui/next-week": "<PERSON><PERSON> sigu<PERSON>e", "@sage/xtrem-ui/next-year": "<PERSON><PERSON> si<PERSON>", "@sage/xtrem-ui/no": "No", "@sage/xtrem-ui/no-available-image": "Ninguna imagen disponible", "@sage/xtrem-ui/no-dashboard-heading": "No tienes ningún cuadro de mando todavía.", "@sage/xtrem-ui/no-dashboard-subtitle": "Crea el primero para empezar.", "@sage/xtrem-ui/no-dashboard-title": "Impulsa tu negocio", "@sage/xtrem-ui/no-data": "No hay datos que mostrar.", "@sage/xtrem-ui/no-file-available": "No hay archivos disponibles.", "@sage/xtrem-ui/no-node": "No se ha indicado ningún nodo y {{0}} no lo especifica.", "@sage/xtrem-ui/no-page-content-found": "No se ha encontrado el contenido de la página. Comprueba que la página existe y que el formato de la URL es /@<nombre de proveedor>/@<nombre de paquete>/@<nombre de página>.", "@sage/xtrem-ui/no-page-translations-found": "Esta página no está traducida.", "@sage/xtrem-ui/no-pages-found": "No se ha encontrado ninguna página. Cambia las opciones de búsqueda.", "@sage/xtrem-ui/no-record-id-provided": "No se ha indicado ningún identificador para el registro en la configuración de la consulta.", "@sage/xtrem-ui/no-sticker-content-found": "No se ha encontrado el contenido del adhesivo. Cambia las opciones de búsqueda.", "@sage/xtrem-ui/no-sticker-translations-found": "Este adhesivo no está traducido.", "@sage/xtrem-ui/no-stickers-found": "No se ha encontrado ningún adhesivo. Cambia las opciones de búsqueda.", "@sage/xtrem-ui/no-value": "<PERSON><PERSON><PERSON> valor", "@sage/xtrem-ui/no-widget-content-found": "No se ha encontrado ningún contenido para el widget {{widget}}.", "@sage/xtrem-ui/notEqual": "Diferente a", "@sage/xtrem-ui/number-format-separator": ",", "@sage/xtrem-ui/ok": "Aceptar", "@sage/xtrem-ui/open-custom-field-dialog": "<PERSON><PERSON><PERSON> campo", "@sage/xtrem-ui/open-dynamic-select": "<PERSON><PERSON><PERSON> lista", "@sage/xtrem-ui/open-full-screen": "<PERSON><PERSON><PERSON> pantalla completa", "@sage/xtrem-ui/open-header-section": "Abrir cabecera", "@sage/xtrem-ui/open-lookup": "Buscar", "@sage/xtrem-ui/open-record-history-dialog": "Historial de registros", "@sage/xtrem-ui/open-section": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/openFilters": "A<PERSON>r filtro", "@sage/xtrem-ui/page-failed-to-load": "Ha habido un error al cargar la página.", "@sage/xtrem-ui/parent": "Referencia primaria", "@sage/xtrem-ui/pdf-metadata-file-name": "**Nombre de archivo:** {{name}}", "@sage/xtrem-ui/pdf-metadata-file-size": "**Tamaño de archivo:** {{size}}", "@sage/xtrem-ui/pdf-metadata-file-type": "**Tipo de archivo:** {{fileType}}", "@sage/xtrem-ui/pdf-metadata-number-of-lines": "**Número de líneas:** {{lineCount}}", "@sage/xtrem-ui/pdf-metadata-pdf-version": "**Versión de PDF:** {{version}}", "@sage/xtrem-ui/pdf-metadata-producer": "**Aplicación de productor:** {{producer}}", "@sage/xtrem-ui/pdf-metadata-resolution": "**Resolución:** {{resolution}}", "@sage/xtrem-ui/please-select-placeholder": "Seleccionar...", "@sage/xtrem-ui/pod-collection-add-new": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/pod-collection-cancel-button": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/pod-collection-remove-button": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/pod-collection-remove-text": "¿Quieres quitar este elemento?", "@sage/xtrem-ui/pod-collection-remove-title": "Confirmar retirada", "@sage/xtrem-ui/pod-placeholder-text": "No hay datos que mostrar.", "@sage/xtrem-ui/pod-remove-item": "<PERSON><PERSON>ar elemento", "@sage/xtrem-ui/populate-list-title-default": "<PERSON><PERSON><PERSON>a", "@sage/xtrem-ui/presentation": "Presentación", "@sage/xtrem-ui/preview-action-download": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/preview-action-print": "Imprimir", "@sage/xtrem-ui/preview-close": "Cerrar vista previa", "@sage/xtrem-ui/preview-go-to-page": "Ir a p<PERSON>gina {{pageNumber}}", "@sage/xtrem-ui/preview-more-file-info": " Información de archivo", "@sage/xtrem-ui/preview-more-options": "Más acciones", "@sage/xtrem-ui/preview-more-thumbnails": "Miniaturas", "@sage/xtrem-ui/preview-no-file-selected": "Ningún archivo seleccionado", "@sage/xtrem-ui/preview-no-preview-available": "Ninguna vista previa disponible", "@sage/xtrem-ui/preview-page-next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "@sage/xtrem-ui/preview-page-prev": "Página anterior", "@sage/xtrem-ui/preview-zoom-in": "Ampliar", "@sage/xtrem-ui/preview-zoom-level": "<PERSON><PERSON> de zoom", "@sage/xtrem-ui/preview-zoom-out": "Reducir", "@sage/xtrem-ui/previous-day": "Día anterior", "@sage/xtrem-ui/previous-month": "Mes anterior", "@sage/xtrem-ui/previous-record": "Registro anterior", "@sage/xtrem-ui/previous-week": "Semana anterior", "@sage/xtrem-ui/previous-year": "<PERSON><PERSON> anterior", "@sage/xtrem-ui/Progress": "Progreso", "@sage/xtrem-ui/property": "Propiedad", "@sage/xtrem-ui/qr-code-component-not-available": "El código QR no está disponible.", "@sage/xtrem-ui/record-history-created-title": "Creación", "@sage/xtrem-ui/record-history-details": "**{{name}}**, **{{date}}** a las **{{time}}**", "@sage/xtrem-ui/record-history-failed": "Ha habido un error al obtener el historial de registros. Inténtalo de nuevo más tarde.", "@sage/xtrem-ui/record-history-last-update-title": "Última actualización", "@sage/xtrem-ui/reference-create-new-item-link": "<PERSON><PERSON>r registro", "@sage/xtrem-ui/reference-field-at-least-chars": "Introduce al menos {{0}} caracteres.", "@sage/xtrem-ui/reference-field-fetching": "Cargando sugerencias...", "@sage/xtrem-ui/reference-field-no-results": "No se ha encontrado ningún resultado. Cambia las opciones de búsqueda.", "@sage/xtrem-ui/reference-filter-clear-selection": "Borrar elementos seleccionados", "@sage/xtrem-ui/reference-lookup-dialog-dialog-title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/reference-lookup-dialog-failed-fetch": "Ha habido un error al cargar las opciones.", "@sage/xtrem-ui/reference-lookup-dialog-search-placeholder": "Buscar...", "@sage/xtrem-ui/reference-open-lookup-link": "Ver <PERSON>a", "@sage/xtrem-ui/return-arrow": "Volver", "@sage/xtrem-ui/router-actions-unhandled-error": "Ha habido un error desconocido. Inténtalo de nuevo.", "@sage/xtrem-ui/same-day": "Día actual", "@sage/xtrem-ui/same-month": "Mes actual", "@sage/xtrem-ui/same-week": "Semana actual", "@sage/xtrem-ui/same-year": "<PERSON><PERSON> actual", "@sage/xtrem-ui/save-new-view-as": "Guardar vista como", "@sage/xtrem-ui/see-more-items": "<PERSON>er más elementos", "@sage/xtrem-ui/select-component-loading": "Cargando...", "@sage/xtrem-ui/select-component-no-results": "Sin resultados", "@sage/xtrem-ui/select-component-type-more": "Introducir {{0}} más", "@sage/xtrem-ui/select-component-type-more-characters": "Introduce {{0}} caracteres más para lanzar la búsqueda.", "@sage/xtrem-ui/select-component-type-one-more-character": "Introduce 1 carácter más para lanzar la búsqueda.", "@sage/xtrem-ui/select-component-type-to-search": "Buscar...", "@sage/xtrem-ui/select-filter-type": "Seleccionar tipo de filtro...", "@sage/xtrem-ui/select-filter-value-boolean": "Seleccionar valor...", "@sage/xtrem-ui/select-filter-value-enum": "Seleccionar valor...", "@sage/xtrem-ui/select-filter-value-max": "<PERSON>or máxi<PERSON>", "@sage/xtrem-ui/select-filter-value-min": "<PERSON><PERSON> m<PERSON>", "@sage/xtrem-ui/select-property": "<PERSON><PERSON><PERSON><PERSON><PERSON> propiedad...", "@sage/xtrem-ui/select-record": "Seleccionar registro", "@sage/xtrem-ui/selection-card-filter-placeholder": "Filtrar...", "@sage/xtrem-ui/series-options": "Opciones de series", "@sage/xtrem-ui/set": "Igual a", "@sage/xtrem-ui/show-floating-filters": "Mostrar filtros de tabla", "@sage/xtrem-ui/show-less": "<PERSON><PERSON> menos", "@sage/xtrem-ui/show-more": "Mostrar más", "@sage/xtrem-ui/show-technical-details": "Mostrar detalles técnicos", "@sage/xtrem-ui/sidebar-apply-changes": "Aplicar", "@sage/xtrem-ui/sidebar-apply-changes-and-create-new": "Ap<PERSON>r y añadir nueva", "@sage/xtrem-ui/sidebar-mobile-apply-changes-and-create-new": "Aplicar y nueva", "@sage/xtrem-ui/startsWith": "Empieza por", "@sage/xtrem-ui/step-sequence-item-aria-complete": "Finalizar", "@sage/xtrem-ui/step-sequence-item-aria-count": "Fase {{0}} de {{1}}", "@sage/xtrem-ui/step-sequence-item-aria-current": "Actual", "@sage/xtrem-ui/sticker-actions-onload-unhandled-error": "Ha habido un error desconocido. Inténtalo de nuevo.", "@sage/xtrem-ui/string-contains": "\"{{fieldTitle}}\" contiene \"{{filterValue}}\".", "@sage/xtrem-ui/string-ends-with": "\"{{fieldTitle}}\" acaba por \"{{filterValue}}\".", "@sage/xtrem-ui/string-starts-with": "\"{{fieldTitle}}\" empieza por \"{{filterValue}}\".", "@sage/xtrem-ui/sum": "<PERSON><PERSON>", "@sage/xtrem-ui/switch-off-caps": "No", "@sage/xtrem-ui/switch-on-caps": "Sí", "@sage/xtrem-ui/table-addCurrentSelectionToFilter": "<PERSON><PERSON><PERSON> se<PERSON>cci<PERSON> actual al filtro", "@sage/xtrem-ui/table-addToLabels": "Añadir **{{value}}** a etiquetas", "@sage/xtrem-ui/table-addToValues": "Añadir **{{value}}** a valores", "@sage/xtrem-ui/table-advancedFilterAnd": "Y", "@sage/xtrem-ui/table-advancedFilterApply": "Aplicar", "@sage/xtrem-ui/table-advancedFilterBlank": "está en blanco", "@sage/xtrem-ui/table-advancedFilterBuilder": "Generador", "@sage/xtrem-ui/table-advancedFilterBuilderAddButtonTooltip": "Añadir filtro o grupo", "@sage/xtrem-ui/table-advancedFilterBuilderAddCondition": "<PERSON><PERSON><PERSON> filt<PERSON>", "@sage/xtrem-ui/table-advancedFilterBuilderAddJoin": "Añadir grupo", "@sage/xtrem-ui/table-advancedFilterBuilderApply": "Aplicar", "@sage/xtrem-ui/table-advancedFilterBuilderCancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-advancedFilterBuilderEnterValue": "Introduce un valor...", "@sage/xtrem-ui/table-advancedFilterBuilderMoveDownButtonTooltip": "Bajar", "@sage/xtrem-ui/table-advancedFilterBuilderMoveUpButtonTooltip": "Subir", "@sage/xtrem-ui/table-advancedFilterBuilderRemoveButtonTooltip": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-advancedFilterBuilderSelectColumn": "Selecciona una columna.", "@sage/xtrem-ui/table-advancedFilterBuilderSelectOption": "Selecciona una opción.", "@sage/xtrem-ui/table-advancedFilterBuilderTitle": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-advancedFilterBuilderValidationAlreadyApplied": "El filtro actual ya se ha aplicado.", "@sage/xtrem-ui/table-advancedFilterBuilderValidationEnterValue": "Introduce un valor.", "@sage/xtrem-ui/table-advancedFilterBuilderValidationIncomplete": "No todas las condiciones están completas.", "@sage/xtrem-ui/table-advancedFilterBuilderValidationSelectColumn": "Selecciona una columna.", "@sage/xtrem-ui/table-advancedFilterBuilderValidationSelectOption": "Selecciona una opción.", "@sage/xtrem-ui/table-advancedFilterContains": "contiene", "@sage/xtrem-ui/table-advancedFilterEndsWith": "acaba por", "@sage/xtrem-ui/table-advancedFilterEquals": "=", "@sage/xtrem-ui/table-advancedFilterFalse": "es falso", "@sage/xtrem-ui/table-advancedFilterGreaterThan": ">", "@sage/xtrem-ui/table-advancedFilterGreaterThanOrEqual": ">=", "@sage/xtrem-ui/table-advancedFilterLessThan": "<", "@sage/xtrem-ui/table-advancedFilterLessThanOrEqual": "<=", "@sage/xtrem-ui/table-advancedFilterNotBlank": "no está en blanco", "@sage/xtrem-ui/table-advancedFilterNotContains": "no contiene", "@sage/xtrem-ui/table-advancedFilterNotEqual": "!=", "@sage/xtrem-ui/table-advancedFilterOr": "O", "@sage/xtrem-ui/table-advancedFilterStartsWith": "empieza por", "@sage/xtrem-ui/table-advancedFilterTextEquals": "igual a", "@sage/xtrem-ui/table-advancedFilterTextNotEqual": "diferente a", "@sage/xtrem-ui/table-advancedFilterTrue": "es verdadero", "@sage/xtrem-ui/table-advancedFilterValidationExtraEndBracket": "Demas<PERSON><PERSON> parén<PERSON> c<PERSON>", "@sage/xtrem-ui/table-advancedFilterValidationInvalidColumn": "No se ha encontrado la columna.", "@sage/xtrem-ui/table-advancedFilterValidationInvalidDate": "El valor no es una fecha válida.", "@sage/xtrem-ui/table-advancedFilterValidationInvalidJoinOperator": "No se ha encontrado el operador de unión.", "@sage/xtrem-ui/table-advancedFilterValidationInvalidOption": "No se ha encontrado la opción.", "@sage/xtrem-ui/table-advancedFilterValidationJoinOperatorMismatch": "Los operadores de unión dentro de una condición deben ser los mismos.", "@sage/xtrem-ui/table-advancedFilterValidationMessage": "La expresión contiene un error. **{{error}}** - **{{validation}}**.", "@sage/xtrem-ui/table-advancedFilterValidationMessageAtEnd": "La expresión contiene un error. **{{validation}}** al final de la expresión.", "@sage/xtrem-ui/table-advancedFilterValidationMissingColumn": "Falta la columna.", "@sage/xtrem-ui/table-advancedFilterValidationMissingCondition": "Falta la condición.", "@sage/xtrem-ui/table-advancedFilterValidationMissingEndBracket": "Falta el paréntesis de cierre.", "@sage/xtrem-ui/table-advancedFilterValidationMissingOption": "Falta la opción.", "@sage/xtrem-ui/table-advancedFilterValidationMissingQuote": "Falta una comilla de cierre.", "@sage/xtrem-ui/table-advancedFilterValidationMissingValue": "Falta el valor.", "@sage/xtrem-ui/table-advancedFilterValidationNotANumber": "El valor no es un número.", "@sage/xtrem-ui/table-advancedSettings": "Configuración avanzada", "@sage/xtrem-ui/table-after": "Después", "@sage/xtrem-ui/table-aggregate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-andCondition": "Y", "@sage/xtrem-ui/table-animation": "Animación", "@sage/xtrem-ui/table-applyFilter": "Aplicar", "@sage/xtrem-ui/table-april": "Abril", "@sage/xtrem-ui/table-area": "Á<PERSON>", "@sage/xtrem-ui/table-areaChart": "Á<PERSON>", "@sage/xtrem-ui/table-AreaColumnCombo": "Área y columna", "@sage/xtrem-ui/table-areaColumnComboTooltip": "Área y columna", "@sage/xtrem-ui/table-areaGroup": "Gráfico de área", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderColumn": "Columna", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderFilterItem": "Condición de filtro", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderGroupItem": "Grupo de filtros", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderItem": "**{{aria}}**. <PERSON><PERSON> **{{level}}**. <PERSON><PERSON><PERSON> \"Entrar\" para editar.", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderItemValidation": "**{{aria}}**. <PERSON><PERSON> **{{level}}**. **{{subLevel}}** <PERSON><PERSON><PERSON> \"Entrar\" para editar.", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderJoinOperator": "Operador de unión", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderList": "Lista de generador de filtros avanzados", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderOption": "Opción", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderValueP": "Valor", "@sage/xtrem-ui/table-ariaAdvancedFilterInput": "Entrada de filtro avanzado", "@sage/xtrem-ui/table-ariaChartMenuClose": "Cerrar menú de edición de gráfico", "@sage/xtrem-ui/table-ariaChartSelected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-ariaChecked": "sele<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-ariaColumn": "Columna", "@sage/xtrem-ui/table-ariaColumnFiltered": "<PERSON><PERSON>na filtrada", "@sage/xtrem-ui/table-ariaColumnGroup": "Grupo de columnas", "@sage/xtrem-ui/table-ariaColumnPanelList": "Lista de columnas", "@sage/xtrem-ui/table-ariaColumnSelectAll": "Alternar visibilidad de todas las columnas", "@sage/xtrem-ui/table-ariaDateFilterInput": "Entrada de filtro de fecha", "@sage/xtrem-ui/table-ariaDefaultListName": "Lista", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentAggFuncSeparator": " de ", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentDescription": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" para eliminar.", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentSortAscending": "Ascendente", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentSortDescending": "Descendente", "@sage/xtrem-ui/table-ariaDropZoneColumnGroupItemDescription": "<PERSON><PERSON><PERSON> \"Entrar\" para ordenar.", "@sage/xtrem-ui/table-ariaDropZoneColumnValueItemDescription": "<PERSON><PERSON><PERSON> \"Entrar\" para cambiar el tipo de agregación.", "@sage/xtrem-ui/table-ariaFilterColumn": "Pulsa \"Ctrl\" + \"Entrar\" para abrir el filtro.", "@sage/xtrem-ui/table-ariaFilterColumnsInput": "Entrada de filtro de columnas", "@sage/xtrem-ui/table-ariaFilterFromValue": "Filtrar desde valor", "@sage/xtrem-ui/table-ariaFilteringOperator": "Operador de filtrado", "@sage/xtrem-ui/table-ariaFilterInput": "Entrada de filtro", "@sage/xtrem-ui/table-ariaFilterList": "Lista de filtros", "@sage/xtrem-ui/table-ariaFilterMenuOpen": "Abrir men<PERSON> de filtros", "@sage/xtrem-ui/table-ariaFilterPanelList": "Lista de filtros", "@sage/xtrem-ui/table-ariaFilterToValue": "Filtrar hasta valor", "@sage/xtrem-ui/table-ariaFilterValue": "Valor de filtro", "@sage/xtrem-ui/table-ariaHeaderSelection": "Selección de columna con cabecera", "@sage/xtrem-ui/table-ariaHidden": "oculto", "@sage/xtrem-ui/table-ariaIndeterminate": "indeterminado", "@sage/xtrem-ui/table-ariaInputEditor": "Editor de entrada", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterAutocomplete": "Autocompletar filtro avanzado", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderAddField": "Agregar campo al generador de filtro avanzado", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderColumnSelectField": "Campo de selección de columna del generador de filtro avanzado", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderJoinSelectField": "Campo de selección de operador de unión del generador de filtro avanzado", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderOptionSelectField": "Campo de selección de opción del generador de filtro avanzado", "@sage/xtrem-ui/table-ariaLabelAggregationFunction": "Función de agregación", "@sage/xtrem-ui/table-ariaLabelCellEditor": "<PERSON> <PERSON> c<PERSON>a", "@sage/xtrem-ui/table-ariaLabelColumnFilter": "Filtro de columna", "@sage/xtrem-ui/table-ariaLabelColumnMenu": "Menú de columna", "@sage/xtrem-ui/table-ariaLabelContextMenu": "Menú contextual", "@sage/xtrem-ui/table-ariaLabelDialog": "Diálogo", "@sage/xtrem-ui/table-ariaLabelRichSelectDeleteSelection": "<PERSON><PERSON><PERSON> \"<PERSON>pr\" para deseleccionar el elemento.", "@sage/xtrem-ui/table-ariaLabelRichSelectDeselectAllItems": "<PERSON><PERSON><PERSON> \"<PERSON>pr\" para deseleccionar todos los elementos.", "@sage/xtrem-ui/table-ariaLabelRichSelectField": "Campo de selección enriquecido", "@sage/xtrem-ui/table-ariaLabelRichSelectToggleSelection": "Pulsa la barra espaciadora para alternar la selección.", "@sage/xtrem-ui/table-ariaLabelSelectField": "Seleccionar campo", "@sage/xtrem-ui/table-ariaLabelSubMenu": "Submenú", "@sage/xtrem-ui/table-ariaLabelTooltip": "<PERSON>yuda contextual", "@sage/xtrem-ui/table-ariaMenuColumn": "<PERSON>ulsa \"Alt\" + flecha abajo para abrir el menú de la columna.", "@sage/xtrem-ui/table-ariaPageSizeSelectorLabel": "Tamaño de página", "@sage/xtrem-ui/table-ariaPivotDropZonePanelLabel": "Etiquetas de columnas", "@sage/xtrem-ui/table-ariaRowDeselect": "Pulsa la barra espaciadora para deseleccionar esta fila.", "@sage/xtrem-ui/table-ariaRowGroupDropZonePanelLabel": "Grupos de filas", "@sage/xtrem-ui/table-ariaRowSelect": "Pulsa la barra espaciadora para seleccionar esta fila.", "@sage/xtrem-ui/table-ariaRowSelectAll": "Pulsa la barra espaciadora para alternar la selección de todas las filas.", "@sage/xtrem-ui/table-ariaRowSelectionDisabled": "Esta fila no tiene la selección de filas habilitada.", "@sage/xtrem-ui/table-ariaRowToggleSelection": "Pulsa la barra espaciadora para alternar la selección de la fila.", "@sage/xtrem-ui/table-ariaSearch": "Buscar", "@sage/xtrem-ui/table-ariaSearchFilterValues": "Buscar valores de filtro", "@sage/xtrem-ui/table-ariaSkeletonCellLoading": "Los datos de la fila se están cargando.", "@sage/xtrem-ui/table-ariaSkeletonCellLoadingFailed": "Ha habido un error al cargar la fila.", "@sage/xtrem-ui/table-ariaSortableColumn": "<PERSON><PERSON><PERSON> \"Entrar\" para ordenar.", "@sage/xtrem-ui/table-ariaToggleCellValue": "Pulsa la barra espaciadora para alternar el valor de la celda.", "@sage/xtrem-ui/table-ariaToggleVisibility": "Pulsa la barra espaciadora para alternar la visibilidad.", "@sage/xtrem-ui/table-ariaUnchecked": "no seleccionado", "@sage/xtrem-ui/table-ariaValuesDropZonePanelLabel": "Valores", "@sage/xtrem-ui/table-ariaVisible": "visible", "@sage/xtrem-ui/table-august": "Agosto", "@sage/xtrem-ui/table-automatic": "Automático", "@sage/xtrem-ui/table-autoRotate": "Rotación automática", "@sage/xtrem-ui/table-autosizeAllColumns": "Reajustar todas las columnas", "@sage/xtrem-ui/table-autosizeThiscolumn": "Reajustar esta columna", "@sage/xtrem-ui/table-avg": "Promedio", "@sage/xtrem-ui/table-axis": "<PERSON><PERSON>", "@sage/xtrem-ui/table-axisType": "Tipo de eje", "@sage/xtrem-ui/table-background": "Fondo", "@sage/xtrem-ui/table-bar": "Barr<PERSON>", "@sage/xtrem-ui/table-barChart": "Barras", "@sage/xtrem-ui/table-barGroup": "Gráfico de barras", "@sage/xtrem-ui/table-before": "<PERSON><PERSON>", "@sage/xtrem-ui/table-blank": "En blanco", "@sage/xtrem-ui/table-blanks": "En blanco", "@sage/xtrem-ui/table-blur": "Desenfoque", "@sage/xtrem-ui/table-bold": "Negrita", "@sage/xtrem-ui/table-boldItalic": "Negrita cursiva", "@sage/xtrem-ui/table-bottom": "Abajo", "@sage/xtrem-ui/table-boxPlot": "Diagrama de caja", "@sage/xtrem-ui/table-boxPlotTooltip": "Diagrama de caja", "@sage/xtrem-ui/table-bubble": "Gráfico de burbujas", "@sage/xtrem-ui/table-bubbleTooltip": "Gráfico de burbujas", "@sage/xtrem-ui/table-calendar-view": "Cambiar a vista de calendario", "@sage/xtrem-ui/table-callout": "Llamada de línea", "@sage/xtrem-ui/table-calloutLabels": "Etiquetas de llamada", "@sage/xtrem-ui/table-cancelFgroupFilterSelectilter": "Seleccionar campo:", "@sage/xtrem-ui/table-cancelFilter": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-cap": "Máximo", "@sage/xtrem-ui/table-capLengthRatio": "Relación de longitud", "@sage/xtrem-ui/table-categories": "Categorías", "@sage/xtrem-ui/table-category": "Categoría", "@sage/xtrem-ui/table-categoryAdd": "Añadir categoría", "@sage/xtrem-ui/table-categoryValues": "Valores de categoría", "@sage/xtrem-ui/table-chart": "Gráfico", "@sage/xtrem-ui/table-chartAdvancedSettings": "Configuración avanzada", "@sage/xtrem-ui/table-chartDownload": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-chartDownloadToolbarTooltip": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-chartEdit": "<PERSON><PERSON>", "@sage/xtrem-ui/table-chartLink": "Vincular a tabla", "@sage/xtrem-ui/table-chartLinkToolbarTooltip": "Vinculado a tabla", "@sage/xtrem-ui/table-chartMenuToolbarTooltip": "Menú", "@sage/xtrem-ui/table-chartRange": "<PERSON><PERSON>", "@sage/xtrem-ui/table-chartSettingsToolbarTooltip": "Menú", "@sage/xtrem-ui/table-chartStyle": "Estilo de grá<PERSON>o", "@sage/xtrem-ui/table-chartSubtitle": "Subtítulo", "@sage/xtrem-ui/table-chartTitle": "T<PERSON><PERSON>lo de gráfico", "@sage/xtrem-ui/table-chartTitles": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-chartUnlink": "Desvincular de tabla", "@sage/xtrem-ui/table-chartUnlinkToolbarTooltip": "Desvinculado de tabla", "@sage/xtrem-ui/table-chooseColumns": "Elegir columnas", "@sage/xtrem-ui/table-circle": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-clearFilter": "Bo<PERSON>r", "@sage/xtrem-ui/table-collapseAll": "<PERSON><PERSON><PERSON> todo", "@sage/xtrem-ui/table-color": "Color", "@sage/xtrem-ui/table-column": "Columna", "@sage/xtrem-ui/table-column-settings": "Selección de columnas", "@sage/xtrem-ui/table-columnChart": "Columnas", "@sage/xtrem-ui/table-columnChooser": "Elegir columnas", "@sage/xtrem-ui/table-columnFilter": "Filtro de columna", "@sage/xtrem-ui/table-columnGroup": "Columna", "@sage/xtrem-ui/table-columnLineCombo": "Columna y línea", "@sage/xtrem-ui/table-columnLineComboTooltip": "Columna y línea", "@sage/xtrem-ui/table-columns": "Columnas", "@sage/xtrem-ui/table-columns-display-controller-columns": "Columnas", "@sage/xtrem-ui/table-combinationChart": "Combinación", "@sage/xtrem-ui/table-combinationGroup": "Combinación", "@sage/xtrem-ui/table-compare-with-previous-period": "Comparar con periodo anterior", "@sage/xtrem-ui/table-connectorLine": "<PERSON><PERSON><PERSON> conectora", "@sage/xtrem-ui/table-contains": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-copy": "Copiar", "@sage/xtrem-ui/table-copyWithGroupHeaders": "Copiar con cabeceras de grupo", "@sage/xtrem-ui/table-copyWithHeaders": "Copiar con cabecera", "@sage/xtrem-ui/table-count": "Recuento", "@sage/xtrem-ui/table-create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-cross": "<PERSON>", "@sage/xtrem-ui/table-crosshair": "Retí<PERSON>", "@sage/xtrem-ui/table-crosshairLabel": "Etiqueta", "@sage/xtrem-ui/table-crosshairSnap": "Ajustar al nodo", "@sage/xtrem-ui/table-csvExport": "Exportar a CSV", "@sage/xtrem-ui/table-ctrlC": "Ctrl+C", "@sage/xtrem-ui/table-ctrlV": "Ctrl+V", "@sage/xtrem-ui/table-ctrlX": "Ctrl+X", "@sage/xtrem-ui/table-customCombo": "Combinación personalizada", "@sage/xtrem-ui/table-customComboTooltip": "Combinación personalizada", "@sage/xtrem-ui/table-cut": "Cortar", "@sage/xtrem-ui/table-data": "Datos", "@sage/xtrem-ui/table-dateFilter": "Filtro de fecha", "@sage/xtrem-ui/table-dateFormatOoo": "DD/MM/YYYY", "@sage/xtrem-ui/table-december": "Diciembre", "@sage/xtrem-ui/table-decimalSeparator": ".", "@sage/xtrem-ui/table-defaultCategory": "(Ninguna)", "@sage/xtrem-ui/table-diamond": "Diamante", "@sage/xtrem-ui/table-direction": "Dirección", "@sage/xtrem-ui/table-donut": "<PERSON><PERSON>", "@sage/xtrem-ui/table-donutTooltip": "<PERSON><PERSON>", "@sage/xtrem-ui/table-doughnut": "Gráfico de anillos", "@sage/xtrem-ui/table-doughnutTooltip": "Gráfico de anillos", "@sage/xtrem-ui/table-durationMillis": "Duración (ms)", "@sage/xtrem-ui/table-empty": "Elige uno", "@sage/xtrem-ui/table-enabled": "Habilitado", "@sage/xtrem-ui/table-endAngle": "Ángulo final", "@sage/xtrem-ui/table-endsWith": "Acaba por", "@sage/xtrem-ui/table-equals": "Igual a", "@sage/xtrem-ui/table-excelExport": "Exportar a Excel (.xlsx)", "@sage/xtrem-ui/table-excelXmlExport": "Exportar a Excel (.xml)", "@sage/xtrem-ui/table-expandAll": "Expandir todo", "@sage/xtrem-ui/table-export": "Exportar", "@sage/xtrem-ui/table-export-failed": "Ha habido un error al exportar el contenido de la lista principal.", "@sage/xtrem-ui/table-export-service-no-columns": "Sin columnas que exportar", "@sage/xtrem-ui/table-export-started": "La exportación se ha iniciado.", "@sage/xtrem-ui/table-false": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-february": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-fillOpacity": "Opacidad de relleno", "@sage/xtrem-ui/table-filter-aria-label": "Filtrar", "@sage/xtrem-ui/table-filteredRows": "Filtradas", "@sage/xtrem-ui/table-filterOoo": "Filtrar...", "@sage/xtrem-ui/table-filters": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-first": "Primera", "@sage/xtrem-ui/table-firstPage": "Primera página", "@sage/xtrem-ui/table-fixed": "<PERSON><PERSON>", "@sage/xtrem-ui/table-font": "Fuente", "@sage/xtrem-ui/table-footerTotal": "Total", "@sage/xtrem-ui/table-format": "Formato", "@sage/xtrem-ui/table-greaterThan": "Superior a", "@sage/xtrem-ui/table-greaterThanOrEqual": "Superior o igual a", "@sage/xtrem-ui/table-gridLines": "Líneas de tabla", "@sage/xtrem-ui/table-group": "Grupo", "@sage/xtrem-ui/table-group-total": "Total de grupo", "@sage/xtrem-ui/table-groupBy": "Agrupar por", "@sage/xtrem-ui/table-groupedAreaTooltip": "Gráfico de área", "@sage/xtrem-ui/table-groupedBar": "Agrupadas", "@sage/xtrem-ui/table-groupedBarFull": "Barra agrupada", "@sage/xtrem-ui/table-groupedBarTooltip": "Gráfico de barras agrupadas", "@sage/xtrem-ui/table-groupedColumn": "Agrupadas", "@sage/xtrem-ui/table-groupedColumnFull": "<PERSON>umna a<PERSON>", "@sage/xtrem-ui/table-groupedColumnTooltip": "Gráfico de barras agrupadas", "@sage/xtrem-ui/table-groupedSeriesGroupType": "Agrupadas", "@sage/xtrem-ui/table-groupPadding": "Espaciado interno de grupo", "@sage/xtrem-ui/table-groups": "Grupos de filas", "@sage/xtrem-ui/table-heart": "Corazón", "@sage/xtrem-ui/table-heatmap": "Mapa térmico", "@sage/xtrem-ui/table-heatmapTooltip": "Mapa térmico", "@sage/xtrem-ui/table-height": "Alto", "@sage/xtrem-ui/table-hierarchicalChart": "Jerárqui<PERSON>", "@sage/xtrem-ui/table-hierarchicalGroup": "Jerárqui<PERSON>", "@sage/xtrem-ui/table-histogram": "Histograma", "@sage/xtrem-ui/table-histogramBinCount": "Recuento papelera", "@sage/xtrem-ui/table-histogramChart": "Histograma", "@sage/xtrem-ui/table-histogramFrequency": "Frecuencia", "@sage/xtrem-ui/table-histogramGroup": "Histograma", "@sage/xtrem-ui/table-histogramTooltip": "Histograma", "@sage/xtrem-ui/table-horizontal": "Horizontal", "@sage/xtrem-ui/table-horizontalAxisTitle": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-innerRadius": "Radio interno", "@sage/xtrem-ui/table-inRange": "En el rango", "@sage/xtrem-ui/table-inRangeEnd": "<PERSON><PERSON>", "@sage/xtrem-ui/table-inRangeStart": "<PERSON><PERSON>", "@sage/xtrem-ui/table-inside": "<PERSON><PERSON>", "@sage/xtrem-ui/table-invalidColor": "El valor del color no es válido.", "@sage/xtrem-ui/table-invalidDate": "La fecha no es válida.", "@sage/xtrem-ui/table-invalidNumber": "El número no es válido.", "@sage/xtrem-ui/table-italic": "Cursiva", "@sage/xtrem-ui/table-itemPaddingX": "Espaciado interno de elemento X", "@sage/xtrem-ui/table-itemPaddingY": "Espaciado interno de elemento Y", "@sage/xtrem-ui/table-itemSpacing": "Espaciado de elemento", "@sage/xtrem-ui/table-january": "<PERSON><PERSON>", "@sage/xtrem-ui/table-july": "<PERSON>", "@sage/xtrem-ui/table-june": "<PERSON><PERSON>", "@sage/xtrem-ui/table-labelPlacement": "Ubicación", "@sage/xtrem-ui/table-labelRotation": "Rotación", "@sage/xtrem-ui/table-labels": "Etiquetas", "@sage/xtrem-ui/table-last": "Última", "@sage/xtrem-ui/table-lastPage": "Última página", "@sage/xtrem-ui/table-layoutHorizontalSpacing": "Espaciado horizontal", "@sage/xtrem-ui/table-layoutVerticalSpacing": "Espaciado vertical", "@sage/xtrem-ui/table-left": "Iz<PERSON>erda", "@sage/xtrem-ui/table-legend": "Leyenda", "@sage/xtrem-ui/table-legendEnabled": "Habilitado", "@sage/xtrem-ui/table-length": "Largo", "@sage/xtrem-ui/table-lessThan": "Inferior a", "@sage/xtrem-ui/table-lessThanOrEqual": "Inferior o igual a", "@sage/xtrem-ui/table-line": "Líneas", "@sage/xtrem-ui/table-line-number": "Número de línea", "@sage/xtrem-ui/table-lineChart": "Lín<PERSON>", "@sage/xtrem-ui/table-lineDash": "Línea discontinua", "@sage/xtrem-ui/table-lineDashOffset": "Desplazamiento de discontinuidad", "@sage/xtrem-ui/table-lineGroup": "Gráfico de líneas", "@sage/xtrem-ui/table-lineTooltip": "Gráfico de líneas", "@sage/xtrem-ui/table-lineWidth": "<PERSON><PERSON>", "@sage/xtrem-ui/table-loadingError": "ERR", "@sage/xtrem-ui/table-loadingOoo": "Cargando...", "@sage/xtrem-ui/table-march": "<PERSON><PERSON>", "@sage/xtrem-ui/table-markerPadding": "Espaciado interno de marcador", "@sage/xtrem-ui/table-markers": "Marcadores", "@sage/xtrem-ui/table-markerSize": "Tamaño de marcador", "@sage/xtrem-ui/table-markerStroke": "Trazo de marcador", "@sage/xtrem-ui/table-max": "Máx.", "@sage/xtrem-ui/table-maxSize": "<PERSON><PERSON><PERSON> m<PERSON>", "@sage/xtrem-ui/table-may": "Mayo", "@sage/xtrem-ui/table-min": "<PERSON><PERSON>.", "@sage/xtrem-ui/table-miniChart": "Minigráfico", "@sage/xtrem-ui/table-minSize": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-more": "más", "@sage/xtrem-ui/table-navigator": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-next": "Siguient<PERSON>", "@sage/xtrem-ui/table-nextPage": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "@sage/xtrem-ui/table-nightingale": "<PERSON>", "@sage/xtrem-ui/table-nightingaleTooltip": "<PERSON>", "@sage/xtrem-ui/table-noAggregation": "Ninguna", "@sage/xtrem-ui/table-noDataToChart": "Sin datos para el gráfico", "@sage/xtrem-ui/table-noMatches": "Sin coincidencias", "@sage/xtrem-ui/table-none": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-noPin": "No anclar", "@sage/xtrem-ui/table-normal": "Normal", "@sage/xtrem-ui/table-normalizedArea": "100 % apiladas", "@sage/xtrem-ui/table-normalizedAreaFull": "Área 100 % apilada", "@sage/xtrem-ui/table-normalizedAreaTooltip": "100 % apiladas", "@sage/xtrem-ui/table-normalizedBar": "100 % apiladas", "@sage/xtrem-ui/table-normalizedBarFull": "Barra 100 % apilada", "@sage/xtrem-ui/table-normalizedBarTooltip": "100 % apiladas", "@sage/xtrem-ui/table-normalizedColumn": "100 % apiladas", "@sage/xtrem-ui/table-normalizedColumnFull": "Columna 100 % apilada", "@sage/xtrem-ui/table-normalizedColumnTooltip": "100 % apiladas", "@sage/xtrem-ui/table-normalizedLine": "100 % apilada", "@sage/xtrem-ui/table-normalizedLineTooltip": "100 % apilada", "@sage/xtrem-ui/table-normalizedSeriesGroupType": "100 % apiladas", "@sage/xtrem-ui/table-noRowsToShow": "<PERSON> filas", "@sage/xtrem-ui/table-notBlank": "Introducido", "@sage/xtrem-ui/table-notContains": "No contiene", "@sage/xtrem-ui/table-notEqual": "Diferente a", "@sage/xtrem-ui/table-november": "Noviembre", "@sage/xtrem-ui/table-number": "Número", "@sage/xtrem-ui/table-numberFilter": "Filtro de números", "@sage/xtrem-ui/table-numeric-filter-greater-than-equals-value": ">={{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-greater-than-value": ">{{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-less-than-equals-value": "<={{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-less-than-value": "<{{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-not-value": "Diferente a {{numericValue}}", "@sage/xtrem-ui/table-numeric-filter-range-value": "{{numericValue}}-{{numericValueTo}}", "@sage/xtrem-ui/table-october": "Octubre", "@sage/xtrem-ui/table-of": "de", "@sage/xtrem-ui/table-offset": "Desplazamiento", "@sage/xtrem-ui/table-offsets": "Desp<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-open-column-panel": "Abrir panel de columnas", "@sage/xtrem-ui/table-orCondition": "O", "@sage/xtrem-ui/table-orientation": "Orientación", "@sage/xtrem-ui/table-outside": "Fuera", "@sage/xtrem-ui/table-padding": "Espaciado interno", "@sage/xtrem-ui/table-page": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-pageLastRowUnknown": "?", "@sage/xtrem-ui/table-pageSizeSelectorLabel": "Tamaño de página:", "@sage/xtrem-ui/table-paired": "Modo en pares", "@sage/xtrem-ui/table-parallel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-paste": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-perpendicular": "Perpendicular", "@sage/xtrem-ui/table-pie": "Gráfico circular", "@sage/xtrem-ui/table-pieChart": "Circular", "@sage/xtrem-ui/table-pieGroup": "Gráfico circular", "@sage/xtrem-ui/table-pieTooltip": "Gráfico circular", "@sage/xtrem-ui/table-pinColumn": "Anclar columna", "@sage/xtrem-ui/table-pinLeft": "Anclar a izquierda", "@sage/xtrem-ui/table-pinRight": "Anclar a derecha", "@sage/xtrem-ui/table-pivotChart": "Grá<PERSON><PERSON> diná<PERSON>", "@sage/xtrem-ui/table-pivotChartAndPivotMode": "Gráfico y modo dinámicos", "@sage/xtrem-ui/table-pivotChartRequiresPivotMode": "Habilita el modo dinámico para poder utilizar el gráfico dinámico.", "@sage/xtrem-ui/table-pivotChartTitle": "Grá<PERSON><PERSON> diná<PERSON>", "@sage/xtrem-ui/table-pivotColumnGroupTotals": "Total", "@sage/xtrem-ui/table-pivotColumnsEmptyMessage": "<PERSON><PERSON><PERSON> hasta aquí para completar las etiquetas de las columnas.", "@sage/xtrem-ui/table-pivotMode": "<PERSON><PERSON>", "@sage/xtrem-ui/table-pivots": "Etiquetas de columnas", "@sage/xtrem-ui/table-plus": "Más", "@sage/xtrem-ui/table-polarAxis": "<PERSON><PERSON>", "@sage/xtrem-ui/table-polarAxisTitle": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-ui/table-polarChart": "Polar", "@sage/xtrem-ui/table-polarGroup": "Polar", "@sage/xtrem-ui/table-polygon": "Polígono", "@sage/xtrem-ui/table-position": "Posición", "@sage/xtrem-ui/table-positionRatio": "Relación de posición", "@sage/xtrem-ui/table-predefined": "Predefinido", "@sage/xtrem-ui/table-preferredLength": "<PERSON><PERSON><PERSON> preferida", "@sage/xtrem-ui/table-previous": "Anterior", "@sage/xtrem-ui/table-previousPage": "Página anterior", "@sage/xtrem-ui/table-print": "Imprimir", "@sage/xtrem-ui/table-radarArea": "Área de radar", "@sage/xtrem-ui/table-radarAreaTooltip": "Área de radar", "@sage/xtrem-ui/table-radarLine": "Línea de radar", "@sage/xtrem-ui/table-radarLineTooltip": "Línea de radar", "@sage/xtrem-ui/table-radialBar": "Barra radial", "@sage/xtrem-ui/table-radialBarTooltip": "Barra radial", "@sage/xtrem-ui/table-radialColumn": "Columna radial", "@sage/xtrem-ui/table-radialColumnTooltip": "Columna radial", "@sage/xtrem-ui/table-radiusAxis": "<PERSON><PERSON>", "@sage/xtrem-ui/table-radiusAxisPosition": "Posición", "@sage/xtrem-ui/table-rangeArea": "<PERSON><PERSON>", "@sage/xtrem-ui/table-rangeAreaTooltip": "<PERSON><PERSON>", "@sage/xtrem-ui/table-rangeBar": "Barra de rango", "@sage/xtrem-ui/table-rangeBarTooltip": "Barra de rango", "@sage/xtrem-ui/table-rangeChartTitle": "Gráfico de rango", "@sage/xtrem-ui/table-removeFromLabels": "Quitar **{{value}}** de etiquetas", "@sage/xtrem-ui/table-removeFromValues": "Quitar **{{value}}** de valores", "@sage/xtrem-ui/table-resetColumns": "Restablecer columnas", "@sage/xtrem-ui/table-resetFilter": "Restablecer", "@sage/xtrem-ui/table-reverseDirection": "Dirección inversa", "@sage/xtrem-ui/table-right": "Derecha", "@sage/xtrem-ui/table-rowDragRow": "fila", "@sage/xtrem-ui/table-rowDragRows": "filas", "@sage/xtrem-ui/table-rowGroupColumnsEmptyMessage": "<PERSON><PERSON><PERSON> hasta aquí para agrupar las filas.", "@sage/xtrem-ui/table-scatter": "Gráfico de dispersión", "@sage/xtrem-ui/table-scatterGroup": "XY (dispersión)", "@sage/xtrem-ui/table-scatterTooltip": "Gráfico de dispersión", "@sage/xtrem-ui/table-scrollingStep": "Paso de desplazamiento", "@sage/xtrem-ui/table-scrollingZoom": "Desplazamiento", "@sage/xtrem-ui/table-searchOoo": "Buscar...", "@sage/xtrem-ui/table-secondaryAxis": "<PERSON><PERSON> secundario", "@sage/xtrem-ui/table-sectorLabels": "Etiquetas de sector", "@sage/xtrem-ui/table-select-all": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "@sage/xtrem-ui/table-selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "@sage/xtrem-ui/table-selectAllSearchResults": "Seleccionar todos los resultados", "@sage/xtrem-ui/table-selectedRows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-selectingZoom": "Seleccionando", "@sage/xtrem-ui/table-september": "Septiembre", "@sage/xtrem-ui/table-series": "Series", "@sage/xtrem-ui/table-seriesAdd": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-seriesChartType": "Tipo de gráfico de series", "@sage/xtrem-ui/table-seriesGroupType": "Tipo de grupo", "@sage/xtrem-ui/table-seriesItemLabels": "Etiquetas de elementos", "@sage/xtrem-ui/table-seriesItemNegative": "Negativo", "@sage/xtrem-ui/table-seriesItemPositive": "Positivo", "@sage/xtrem-ui/table-seriesItems": "Elementos de series", "@sage/xtrem-ui/table-seriesItemType": "Tipo de elemento", "@sage/xtrem-ui/table-seriesLabels": "Etiquetas de series", "@sage/xtrem-ui/table-seriesPadding": "Espaciado interno de serie", "@sage/xtrem-ui/table-seriesType": "Tipo de serie", "@sage/xtrem-ui/table-setFilter": "Filtro de conjunto", "@sage/xtrem-ui/table-settings": "Parámetros", "@sage/xtrem-ui/table-shadow": "Sombreado", "@sage/xtrem-ui/table-shape": "Forma", "@sage/xtrem-ui/table-show": "Mostrar", "@sage/xtrem-ui/table-size": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-sortAscending": "Orden ascendente", "@sage/xtrem-ui/table-sortDescending": "<PERSON>den descendente", "@sage/xtrem-ui/table-sortUnSort": "<PERSON><PERSON><PERSON> orden", "@sage/xtrem-ui/table-spacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-specializedChart": "Especializado", "@sage/xtrem-ui/table-specializedGroup": "Especializado", "@sage/xtrem-ui/table-square": "Cuadrado", "@sage/xtrem-ui/table-stackedArea": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-stackedAreaFull": "<PERSON><PERSON> a<PERSON>a", "@sage/xtrem-ui/table-stackedAreaTooltip": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-stackedBar": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-stackedBarFull": "Barra apilada", "@sage/xtrem-ui/table-stackedBarTooltip": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-stackedColumn": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-stackedColumnFull": "<PERSON><PERSON><PERSON> apilada", "@sage/xtrem-ui/table-stackedColumnTooltip": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-stackedLine": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-stackedLineTooltip": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-stackedSeriesGroupType": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-startAngle": "<PERSON><PERSON><PERSON> inicial", "@sage/xtrem-ui/table-startsWith": "Empieza por", "@sage/xtrem-ui/table-statisticalChart": "Estadístico", "@sage/xtrem-ui/table-statisticalGroup": "Estadístico", "@sage/xtrem-ui/table-strokeColor": "Color de línea", "@sage/xtrem-ui/table-strokeOpacity": "Opacidad de relleno", "@sage/xtrem-ui/table-strokeWidth": "<PERSON><PERSON> de <PERSON>o", "@sage/xtrem-ui/table-sum": "<PERSON><PERSON>", "@sage/xtrem-ui/table-summary-empty-default-text": "No hay datos que mostrar.", "@sage/xtrem-ui/table-sunburst": "Proyección solar", "@sage/xtrem-ui/table-sunburstTooltip": "Proyección solar", "@sage/xtrem-ui/table-switch-to-card-view": "Cambiar a vista de tarjeta", "@sage/xtrem-ui/table-switch-to-chart-view": "Cambiar a vista de gráfico", "@sage/xtrem-ui/table-switch-to-contact-view": "Cambiar a vista de contacto", "@sage/xtrem-ui/table-switch-to-site-view": "Cambiar a vista de planta", "@sage/xtrem-ui/table-switch-to-table-view": "Cambiar a vista de tabla", "@sage/xtrem-ui/table-switchCategorySeries": "Cambiar categoría/serie", "@sage/xtrem-ui/table-table-view": "Cambiar a vista de tabla", "@sage/xtrem-ui/table-textFilter": "Filtro de texto", "@sage/xtrem-ui/table-thickness": "Grosor", "@sage/xtrem-ui/table-thousandSeparator": ",", "@sage/xtrem-ui/table-ticks": "Graduaciones", "@sage/xtrem-ui/table-tile": "Mosaico", "@sage/xtrem-ui/table-time": "Tiempo", "@sage/xtrem-ui/table-timeFormat": "Formato de hora", "@sage/xtrem-ui/table-timeFormatDashesYYYYMMDD": "YYYY-MM-DD", "@sage/xtrem-ui/table-timeFormatDotsDDMYY": "DD.M.YY", "@sage/xtrem-ui/table-timeFormatDotsMDDYY": "M.<PERSON>.YY", "@sage/xtrem-ui/table-timeFormatHHMMSS": "HH:MM:SS", "@sage/xtrem-ui/table-timeFormatHHMMSSAmPm": "HH:MM:SS AM/PM", "@sage/xtrem-ui/table-timeFormatSlashesDDMMYY": "DD/MM/YY", "@sage/xtrem-ui/table-timeFormatSlashesDDMMYYYY": "DD/MM/YYYY", "@sage/xtrem-ui/table-timeFormatSlashesMMDDYY": "MM/DD/YY", "@sage/xtrem-ui/table-timeFormatSlashesMMDDYYYY": "MM/DD/YYYY", "@sage/xtrem-ui/table-timeFormatSpacesDDMMMMYYYY": "DD MMMM YYYY", "@sage/xtrem-ui/table-title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-titlePlaceholder": "<PERSON><PERSON><PERSON><PERSON> de gráfico: haz doble clic para editar.", "@sage/xtrem-ui/table-to": "-", "@sage/xtrem-ui/table-tooltips": "<PERSON>yuda contextual", "@sage/xtrem-ui/table-top": "Arriba", "@sage/xtrem-ui/table-totalAndFilteredRows": "<PERSON><PERSON>", "@sage/xtrem-ui/table-totalRows": "Total filas", "@sage/xtrem-ui/table-treemap": "Mapa de árbol", "@sage/xtrem-ui/table-treemapTooltip": "Mapa de árbol", "@sage/xtrem-ui/table-triangle": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/table-true": "Verdadero", "@sage/xtrem-ui/table-ungroupAll": "<PERSON><PERSON><PERSON><PERSON> todo", "@sage/xtrem-ui/table-ungroupBy": "Desagrupar por", "@sage/xtrem-ui/table-valueAggregation": "Agregación de valores", "@sage/xtrem-ui/table-valueColumnsEmptyMessage": "<PERSON><PERSON><PERSON> hasta aquí para agregar.", "@sage/xtrem-ui/table-values": "Valores", "@sage/xtrem-ui/table-variant_card": "Vista de tarjeta", "@sage/xtrem-ui/table-variant-area": "Vista de área", "@sage/xtrem-ui/table-variant-table": "Vista de tabla", "@sage/xtrem-ui/table-vertical": "Vertical", "@sage/xtrem-ui/table-verticalAxisTitle": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-ui/table-views-close-button": "Cerrar selector de vistas", "@sage/xtrem-ui/table-views-default": "Vista por defecto", "@sage/xtrem-ui/table-views-manage": "Gestionar vistas", "@sage/xtrem-ui/table-views-open-button": "Abrir selector de vistas", "@sage/xtrem-ui/table-views-save": "Guardar vista", "@sage/xtrem-ui/table-views-save-as": "Guardar vista como", "@sage/xtrem-ui/table-views-save-failed": "Ha habido un error al guardar la vista.", "@sage/xtrem-ui/table-views-saved": "La vista se ha guardado.", "@sage/xtrem-ui/table-views-select": "Seleccionar vista", "@sage/xtrem-ui/table-waterfall": "Cascada", "@sage/xtrem-ui/table-waterfallTooltip": "Cascada", "@sage/xtrem-ui/table-weight": "Peso", "@sage/xtrem-ui/table-whisker": "<PERSON><PERSON>", "@sage/xtrem-ui/table-width": "<PERSON><PERSON>", "@sage/xtrem-ui/table-xAxis": "<PERSON><PERSON>", "@sage/xtrem-ui/table-xOffset": "Desplazamiento X", "@sage/xtrem-ui/table-xRotation": "Rotación X", "@sage/xtrem-ui/table-xType": "Tipo X", "@sage/xtrem-ui/table-xyChart": "XY (dispersión)", "@sage/xtrem-ui/table-xyValues": "Valores X Y", "@sage/xtrem-ui/table-yAxis": "<PERSON><PERSON> vertical", "@sage/xtrem-ui/table-yOffset": "Desplazamiento Y", "@sage/xtrem-ui/table-yRotation": "Rotación Y", "@sage/xtrem-ui/table-zoom": "Zoom", "@sage/xtrem-ui/text-editor-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/text-editor-cancel-button": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/text-editor-character-counter": "Número de caracteres", "@sage/xtrem-ui/text-editor-character-limit": "Límite de caracteres", "@sage/xtrem-ui/text-editor-content-editor": "Editor de contenido", "@sage/xtrem-ui/text-editor-save": "Guardar", "@sage/xtrem-ui/text-editor-save-button": "Guardar", "@sage/xtrem-ui/text-editor-toolbar": "Barra de herramientas", "@sage/xtrem-ui/text-format-capital-bold": "Negrita", "@sage/xtrem-ui/text-format-capital-bullet-list": "Lista con viñetas", "@sage/xtrem-ui/text-format-capital-italic": "Cursiva", "@sage/xtrem-ui/text-format-capital-number-list": "Lista numerada", "@sage/xtrem-ui/timeframe": "Periodo de tiempo", "@sage/xtrem-ui/title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/toggle-navigation-panel": "Alternar panel de navegación", "@sage/xtrem-ui/toggle-widget-list": "Alternar lista de widgets", "@sage/xtrem-ui/true": "Verdadero", "@sage/xtrem-ui/tunnel-already-open-description": "Esta página ya se está utilizando. Ciérrala para poder abrir este vínculo o ábrela en una pestaña nueva.", "@sage/xtrem-ui/tunnel-already-open-title": "Página abierta", "@sage/xtrem-ui/tunnel-link-see-more": "Ver registro", "@sage/xtrem-ui/tunnel-record-not-suitable-description": "El registro creado no se puede utilizar en este campo.", "@sage/xtrem-ui/tunnel-record-not-suitable-title": "Registro no disponible", "@sage/xtrem-ui/unexpected-error-page-load": "Ha habido un error al cargar la página. Inténtalo de nuevo.", "@sage/xtrem-ui/ungroup": "Desagrupar", "@sage/xtrem-ui/unhandled-event-handler-error": "Ha habido un error desconocido. Inténtalo de nuevo.", "@sage/xtrem-ui/unsaved-changes-content": "¿Quieres salir y descartar los cambios?", "@sage/xtrem-ui/unsaved-changes-go-back": "Volver", "@sage/xtrem-ui/unsaved-changes-title": "Cam<PERSON>s sin guardar", "@sage/xtrem-ui/unsaved-changes-yes": "Descar<PERSON>", "@sage/xtrem-ui/update-error": "Ha habido un error en la actualización: {{0}}.", "@sage/xtrem-ui/upload-in-progress": "Subiendo el archivo...", "@sage/xtrem-ui/validation-error-and-more": "y 1 error más", "@sage/xtrem-ui/validation-error-of-children": "Error de validación en los subregistros", "@sage/xtrem-ui/validation-error-total": "1 error", "@sage/xtrem-ui/validation-error-with-number-grid": "Hay 1 error en la tabla {{0}}.", "@sage/xtrem-ui/validation-error-with-number-pod": "Hay 1 error en la colección {{0}}.", "@sage/xtrem-ui/validation-errors": "Errores de validación", "@sage/xtrem-ui/validation-errors-and-more": "y {{0}} errores más", "@sage/xtrem-ui/validation-errors-number": "Número de errores en esta línea", "@sage/xtrem-ui/validation-errors-total": "{{0}} errores", "@sage/xtrem-ui/validation-errors-unknown": "Error de validación desconocido", "@sage/xtrem-ui/validation-errors-with-number-grid": "Errores en la tabla {{1}}: {{0}}", "@sage/xtrem-ui/validation-errors-with-number-pod": "Errores en la colección {{1}}: {{0}}", "@sage/xtrem-ui/visual-process-lookup-page-dialog-title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/visual-process-lookup-page-path-column": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-ui/visual-process-lookup-page-title-column": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/visual-process-transactions-not-supported": "Las transacciones de páginas no son compatibles.", "@sage/xtrem-ui/widget-action-create": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/widget-action-create-help": "Añadir acceso directo para crear un registro", "@sage/xtrem-ui/widget-action-see-all": "Ver todo", "@sage/xtrem-ui/widget-action-see-all-help": "Ver registros basados en tus selecciones", "@sage/xtrem-ui/widget-actions": "Acciones", "@sage/xtrem-ui/widget-editor-action-decimal-digits-mandatory": "Introduce un número.", "@sage/xtrem-ui/widget-editor-action-goes-to": "Vinculación", "@sage/xtrem-ui/widget-editor-action-label-mandatory": "Introduce un título.", "@sage/xtrem-ui/widget-editor-action-page-path": "<PERSON><PERSON>", "@sage/xtrem-ui/widget-editor-action-page-title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui/widget-editor-add": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/widget-editor-basic-step-missing-node": "Añade un nodo.", "@sage/xtrem-ui/widget-editor-basic-step-missing-title": "Añade un título.", "@sage/xtrem-ui/widget-editor-basic-step-title": "{{stepIndex}}. Selecciona un widget para empezar.", "@sage/xtrem-ui/widget-editor-cancel-edit": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/widget-editor-content-formatting": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/widget-editor-content-step-subtitle": "Selecciona un campo para la agrupación y los campos que quieras utilizar de filtros.", "@sage/xtrem-ui/widget-editor-content-step-title": "{{stepIndex}}. Añade el contenido.", "@sage/xtrem-ui/widget-editor-content-step-vertical-axes": "<PERSON><PERSON><PERSON> verticales", "@sage/xtrem-ui/widget-editor-data-step-title": "{{stepIndex}}. Selecciona los datos que añadir al widget.", "@sage/xtrem-ui/widget-editor-entry-node": "Origen de datos", "@sage/xtrem-ui/widget-editor-filter-step-title": "{{stepIndex}}. Añade tus filtros.", "@sage/xtrem-ui/widget-editor-grouping-method": "Método de agrupación", "@sage/xtrem-ui/widget-editor-grouping-property": "Propiedad de agrupación", "@sage/xtrem-ui/widget-editor-horizontal-axis": "<PERSON><PERSON>", "@sage/xtrem-ui/widget-editor-horizontal-axis-label": "Etiqueta de eje horizontal", "@sage/xtrem-ui/widget-editor-icon": "Icono", "@sage/xtrem-ui/widget-editor-layout-step-no-actions-in-preview": "Las acciones no funcionan en la vista previa. Tienes que añadir el widget al cuadro de mando.", "@sage/xtrem-ui/widget-editor-layout-step-title": "{{stepIndex}}. <PERSON>rea el diseño.", "@sage/xtrem-ui/widget-editor-max-num-of-values": "Número máximo de valores", "@sage/xtrem-ui/widget-editor-no-filterable-properties": "No puedes filtrar estos valores. Puedes seleccionar otros datos o continuar sin filtrarlos.", "@sage/xtrem-ui/widget-editor-sorting-step-title": "{{stepIndex}}. Define el orden que quieras.", "@sage/xtrem-ui/widget-editor-step-basic-title": "Widget", "@sage/xtrem-ui/widget-editor-step-content-title": "Contenido", "@sage/xtrem-ui/widget-editor-step-data-title": "Datos", "@sage/xtrem-ui/widget-editor-step-filters-title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/widget-editor-step-layout-title": "Diseño", "@sage/xtrem-ui/widget-editor-step-sorting-title": "Orden", "@sage/xtrem-ui/widget-editor-subtitle": "Subtítulo", "@sage/xtrem-ui/widget-editor-title-edit": "Editar widget", "@sage/xtrem-ui/widget-editor-title-new": "Nuevo widget", "@sage/xtrem-ui/widget-editor-type-bar-chart": "Gráfico de barras", "@sage/xtrem-ui/widget-editor-type-bar-chart-description": "Mostrar datos con barras", "@sage/xtrem-ui/widget-editor-type-indicator-tile": "Indicador", "@sage/xtrem-ui/widget-editor-type-indicator-tile-description": "Mostrar actualizaciones importantes", "@sage/xtrem-ui/widget-editor-type-line-chart": "Gráfico de líneas", "@sage/xtrem-ui/widget-editor-type-line-chart-description": "Mostrar datos con líneas", "@sage/xtrem-ui/widget-editor-type-table": "Tabla", "@sage/xtrem-ui/widget-editor-type-table-description": "Mostrar datos con filas y columnas", "@sage/xtrem-ui/widget-editor-update": "Actualizar", "@sage/xtrem-ui/widget-editor-vertical-axis-label": "Etiqueta de eje vertical", "@sage/xtrem-ui/widget-editor-widget-category": "Categoría de widget", "@sage/xtrem-ui/widget-editor-widget-title": "<PERSON><PERSON><PERSON><PERSON> de widget", "@sage/xtrem-ui/widget-preview-label": "Vista previa", "@sage/xtrem-ui/wizard-finish": "Finalizar", "@sage/xtrem-ui/wizard-next": "Siguient<PERSON>", "@sage/xtrem-ui/wizard-previous": "Anterior", "@sage/xtrem-ui/workflow-add-trigger-event": "Añadir efecto desencadenante", "@sage/xtrem-ui/workflow-collapse": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/workflow-component-add-action": "<PERSON><PERSON><PERSON> acci<PERSON>", "@sage/xtrem-ui/workflow-component-add-condition": "Añadir condición", "@sage/xtrem-ui/workflow-component-add-step": "<PERSON><PERSON><PERSON> p<PERSON>o", "@sage/xtrem-ui/workflow-component-edge-false-path": "Si no", "@sage/xtrem-ui/workflow-component-edge-true-path": "Si verdadero", "@sage/xtrem-ui/workflow-component-header-label-action": "Acción", "@sage/xtrem-ui/workflow-component-header-label-condition": "Condición", "@sage/xtrem-ui/workflow-component-header-label-start": "Inicia cuando", "@sage/xtrem-ui/workflow-component-wizard-event-selection": "Seleccionar acción", "@sage/xtrem-ui/workflow-component-wizard-step-configuration": "Configurar", "@sage/xtrem-ui/workflow-component-wizard-title-action": "Galería de acciones", "@sage/xtrem-ui/workflow-component-wizard-title-trigger": "Galería de efectos desencadenantes", "@sage/xtrem-ui/workflow-component-wizard-trigger-selection": "Seleccionar efecto desencadenante", "@sage/xtrem-ui/workflow-delete-node-chain-message": "Si quitas este paso, los pasos posteriores sin otros vínculos también se quitan.", "@sage/xtrem-ui/workflow-delete-node-chain-title": "Eliminar flujo de pasos", "@sage/xtrem-ui/workflow-empty": "Este flujo de trabajo está en blanco.", "@sage/xtrem-ui/workflow-expand": "Expandir", "@sage/xtrem-ui/workflow-fit-view": "Ajustar vista", "@sage/xtrem-ui/workflow-redo": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/workflow-undo": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui/workflow-zoom-in": "Ampliar", "@sage/xtrem-ui/workflow-zoom-out": "Reducir", "@sage/xtrem-ui/yes": "Sí"}