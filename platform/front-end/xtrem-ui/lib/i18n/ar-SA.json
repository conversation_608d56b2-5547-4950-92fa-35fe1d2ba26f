{"@sage/xtrem-ui/360-view": "", "@sage/xtrem-ui/action-button-more": "", "@sage/xtrem-ui/action-clone": "", "@sage/xtrem-ui/action-close": "", "@sage/xtrem-ui/action-delete": "", "@sage/xtrem-ui/action-edit": "", "@sage/xtrem-ui/action-share": "", "@sage/xtrem-ui/action-tool-bar-items-selected": "", "@sage/xtrem-ui/actions": "", "@sage/xtrem-ui/add-item-in-line": "", "@sage/xtrem-ui/add-item-in-line-using-sidebar": "", "@sage/xtrem-ui/add-value": "", "@sage/xtrem-ui/aggregation-method": "", "@sage/xtrem-ui/async-mutation-dialog-content": "", "@sage/xtrem-ui/async-mutation-dialog-title": "", "@sage/xtrem-ui/async-operation-error": "", "@sage/xtrem-ui/attachment-options-menu-all": "", "@sage/xtrem-ui/attachment-options-menu-documents": "", "@sage/xtrem-ui/attachment-options-menu-images": "", "@sage/xtrem-ui/attachment-options-menu-others": "", "@sage/xtrem-ui/attachments": "", "@sage/xtrem-ui/average": "", "@sage/xtrem-ui/axes": "", "@sage/xtrem-ui/bulk-action-async-export": "", "@sage/xtrem-ui/bulk-action-dialog-content": "", "@sage/xtrem-ui/bulk-action-error": "", "@sage/xtrem-ui/bulk-action-print": "", "@sage/xtrem-ui/bulk-action-started": "", "@sage/xtrem-ui/bulk-actions-bar-selected": "", "@sage/xtrem-ui/business-action-in-progress": "", "@sage/xtrem-ui/calendar-month": "", "@sage/xtrem-ui/calendar-view-3-day": "", "@sage/xtrem-ui/calendar-view-day": "", "@sage/xtrem-ui/calendar-view-month": "", "@sage/xtrem-ui/calendar-view-week": "", "@sage/xtrem-ui/calendar-view-year": "", "@sage/xtrem-ui/cancel": "", "@sage/xtrem-ui/carbon-date-format": "", "@sage/xtrem-ui/chart-component-no-data": "", "@sage/xtrem-ui/clear-filter-text": "", "@sage/xtrem-ui/clear-floating-filter": "", "@sage/xtrem-ui/clear-input-text": "", "@sage/xtrem-ui/clear-selection": "", "@sage/xtrem-ui/close-full-screen": "", "@sage/xtrem-ui/close-header-section": "", "@sage/xtrem-ui/close-record": "", "@sage/xtrem-ui/collapse-section": "", "@sage/xtrem-ui/consumer-mock-hide-test-ids": "", "@sage/xtrem-ui/consumer-mock-show-test-ids": "", "@sage/xtrem-ui/copy-error-details": "", "@sage/xtrem-ui/create-a-new-view": "", "@sage/xtrem-ui/create-a-view": "", "@sage/xtrem-ui/create-error": "", "@sage/xtrem-ui/create-new-dashboard": "", "@sage/xtrem-ui/crud-cancel": "", "@sage/xtrem-ui/crud-confirm": "", "@sage/xtrem-ui/crud-create": "", "@sage/xtrem-ui/crud-create-success": "", "@sage/xtrem-ui/crud-delete": "", "@sage/xtrem-ui/crud-delete-record-button": "", "@sage/xtrem-ui/crud-delete-record-warning-message": "", "@sage/xtrem-ui/crud-delete-record-warning-title": "", "@sage/xtrem-ui/crud-delete-successfully": "", "@sage/xtrem-ui/crud-duplicate": "", "@sage/xtrem-ui/crud-duplicate-dialog-subtitle": "", "@sage/xtrem-ui/crud-duplicate-dialog-title": "", "@sage/xtrem-ui/crud-duplicate-failed": "", "@sage/xtrem-ui/crud-duplicate-successfully": "", "@sage/xtrem-ui/crud-save": "", "@sage/xtrem-ui/crud-save-failed": "", "@sage/xtrem-ui/crud-update-success": "", "@sage/xtrem-ui/dashboard-actions": "", "@sage/xtrem-ui/dashboard-add_contact": "", "@sage/xtrem-ui/dashboard-add_note": "", "@sage/xtrem-ui/dashboard-add_site": "", "@sage/xtrem-ui/dashboard-add-widget": "", "@sage/xtrem-ui/dashboard-address": "", "@sage/xtrem-ui/dashboard-address_name": "", "@sage/xtrem-ui/dashboard-addresses": "", "@sage/xtrem-ui/dashboard-cancel": "", "@sage/xtrem-ui/dashboard-choose_date": "", "@sage/xtrem-ui/dashboard-clear_filter": "", "@sage/xtrem-ui/dashboard-contact": "", "@sage/xtrem-ui/dashboard-contact_card_empty_text": "", "@sage/xtrem-ui/dashboard-contact_image": "", "@sage/xtrem-ui/dashboard-contact_type": "", "@sage/xtrem-ui/dashboard-contacts": "", "@sage/xtrem-ui/dashboard-create": "", "@sage/xtrem-ui/dashboard-create-dashboard": "", "@sage/xtrem-ui/dashboard-current_date_filter": "", "@sage/xtrem-ui/dashboard-day": "", "@sage/xtrem-ui/dashboard-delete": "", "@sage/xtrem-ui/dashboard-delete-dashboard-dialog-message": "", "@sage/xtrem-ui/dashboard-delete-dashboard-dialog-title": "", "@sage/xtrem-ui/dashboard-deleted": "", "@sage/xtrem-ui/dashboard-duplicate": "", "@sage/xtrem-ui/dashboard-duplicated": "", "@sage/xtrem-ui/dashboard-edit": "", "@sage/xtrem-ui/dashboard-editor-add-widget": "", "@sage/xtrem-ui/dashboard-editor-all-widgets": "", "@sage/xtrem-ui/dashboard-editor-cancel-edit": "", "@sage/xtrem-ui/dashboard-editor-create-dialog-blank-description": "", "@sage/xtrem-ui/dashboard-editor-create-dialog-blank-title": "", "@sage/xtrem-ui/dashboard-editor-create-dialog-description": "", "@sage/xtrem-ui/dashboard-editor-create-dialog-title": "", "@sage/xtrem-ui/dashboard-editor-edit-title": "", "@sage/xtrem-ui/dashboard-editor-redo": "", "@sage/xtrem-ui/dashboard-editor-save": "", "@sage/xtrem-ui/dashboard-editor-save-error": "", "@sage/xtrem-ui/dashboard-editor-saved-successfully": "", "@sage/xtrem-ui/dashboard-editor-title": "", "@sage/xtrem-ui/dashboard-editor-undo": "", "@sage/xtrem-ui/dashboard-editor-widget-edit": "", "@sage/xtrem-ui/dashboard-editor-widget-list-add": "", "@sage/xtrem-ui/dashboard-editor-widget-list-title": "", "@sage/xtrem-ui/dashboard-email": "", "@sage/xtrem-ui/dashboard-empty-heading": "", "@sage/xtrem-ui/dashboard-empty-subtitle": "", "@sage/xtrem-ui/dashboard-expand_row": "", "@sage/xtrem-ui/dashboard-failed-to-create": "", "@sage/xtrem-ui/dashboard-failed-to-delete": "", "@sage/xtrem-ui/dashboard-failed-to-duplicate": "", "@sage/xtrem-ui/dashboard-month": "", "@sage/xtrem-ui/dashboard-month_1": "", "@sage/xtrem-ui/dashboard-month_10": "", "@sage/xtrem-ui/dashboard-month_11": "", "@sage/xtrem-ui/dashboard-month_12": "", "@sage/xtrem-ui/dashboard-month_2": "", "@sage/xtrem-ui/dashboard-month_3": "", "@sage/xtrem-ui/dashboard-month_4": "", "@sage/xtrem-ui/dashboard-month_5": "", "@sage/xtrem-ui/dashboard-month_6": "", "@sage/xtrem-ui/dashboard-month_7": "", "@sage/xtrem-ui/dashboard-month_8": "", "@sage/xtrem-ui/dashboard-month_9": "", "@sage/xtrem-ui/dashboard-more_options": "", "@sage/xtrem-ui/dashboard-next": "", "@sage/xtrem-ui/dashboard-next_period": "", "@sage/xtrem-ui/dashboard-no-change": "", "@sage/xtrem-ui/dashboard-note": "", "@sage/xtrem-ui/dashboard-notes": "", "@sage/xtrem-ui/dashboard-phone_number": "", "@sage/xtrem-ui/dashboard-position": "", "@sage/xtrem-ui/dashboard-previous": "", "@sage/xtrem-ui/dashboard-previous_period": "", "@sage/xtrem-ui/dashboard-quarter": "", "@sage/xtrem-ui/dashboard-reload": "", "@sage/xtrem-ui/dashboard-save": "", "@sage/xtrem-ui/dashboard-scroll_left": "", "@sage/xtrem-ui/dashboard-scroll_right": "", "@sage/xtrem-ui/dashboard-select_address": "", "@sage/xtrem-ui/dashboard-select_contact": "", "@sage/xtrem-ui/dashboard-settings": "", "@sage/xtrem-ui/dashboard-site": "", "@sage/xtrem-ui/dashboard-site_card_empty_text": "", "@sage/xtrem-ui/dashboard-table_filter_menu": "", "@sage/xtrem-ui/dashboard-table_select": "", "@sage/xtrem-ui/dashboard-view_switch": "", "@sage/xtrem-ui/dashboard-week": "", "@sage/xtrem-ui/dashboard-widget-category-others": "", "@sage/xtrem-ui/dashboard-widget-close": "", "@sage/xtrem-ui/dashboard-widget-refresh": "", "@sage/xtrem-ui/dashboard-widget-settings": "", "@sage/xtrem-ui/dashboard-year": "", "@sage/xtrem-ui/date-error-day-range": "", "@sage/xtrem-ui/date-error-month-range": "", "@sage/xtrem-ui/date-error-year-range": "", "@sage/xtrem-ui/date-format": "", "@sage/xtrem-ui/date-format-separator": "", "@sage/xtrem-ui/date-time-component-aria-label": "", "@sage/xtrem-ui/date-time-range-end-date": "", "@sage/xtrem-ui/date-time-range-start-date": "", "@sage/xtrem-ui/date-time-range-time": "", "@sage/xtrem-ui/date-time-range-time-zone": "", "@sage/xtrem-ui/datetime-aria-label": "", "@sage/xtrem-ui/datetime-range-aria-label": "", "@sage/xtrem-ui/datetime-range-end-date-error": "", "@sage/xtrem-ui/default-view-cannot-be-edited": "", "@sage/xtrem-ui/delete-error": "", "@sage/xtrem-ui/detailed-icon-name-accounting": "", "@sage/xtrem-ui/detailed-icon-name-addons": "", "@sage/xtrem-ui/detailed-icon-name-animal": "", "@sage/xtrem-ui/detailed-icon-name-apple": "", "@sage/xtrem-ui/detailed-icon-name-asset_mgt": "", "@sage/xtrem-ui/detailed-icon-name-award": "", "@sage/xtrem-ui/detailed-icon-name-bag": "", "@sage/xtrem-ui/detailed-icon-name-bakery": "", "@sage/xtrem-ui/detailed-icon-name-barcode": "", "@sage/xtrem-ui/detailed-icon-name-bicycle": "", "@sage/xtrem-ui/detailed-icon-name-binocular": "", "@sage/xtrem-ui/detailed-icon-name-book": "", "@sage/xtrem-ui/detailed-icon-name-bright": "", "@sage/xtrem-ui/detailed-icon-name-building": "", "@sage/xtrem-ui/detailed-icon-name-calculator": "", "@sage/xtrem-ui/detailed-icon-name-calendar": "", "@sage/xtrem-ui/detailed-icon-name-camera": "", "@sage/xtrem-ui/detailed-icon-name-card": "", "@sage/xtrem-ui/detailed-icon-name-cart": "", "@sage/xtrem-ui/detailed-icon-name-certificate": "", "@sage/xtrem-ui/detailed-icon-name-check": "", "@sage/xtrem-ui/detailed-icon-name-checkbox": "", "@sage/xtrem-ui/detailed-icon-name-checklist": "", "@sage/xtrem-ui/detailed-icon-name-chemical": "", "@sage/xtrem-ui/detailed-icon-name-chess": "", "@sage/xtrem-ui/detailed-icon-name-click": "", "@sage/xtrem-ui/detailed-icon-name-clock": "", "@sage/xtrem-ui/detailed-icon-name-close": "", "@sage/xtrem-ui/detailed-icon-name-clothes": "", "@sage/xtrem-ui/detailed-icon-name-cloud": "", "@sage/xtrem-ui/detailed-icon-name-coffee": "", "@sage/xtrem-ui/detailed-icon-name-compass": "", "@sage/xtrem-ui/detailed-icon-name-connected": "", "@sage/xtrem-ui/detailed-icon-name-consultant": "", "@sage/xtrem-ui/detailed-icon-name-conversation": "", "@sage/xtrem-ui/detailed-icon-name-cooking": "", "@sage/xtrem-ui/detailed-icon-name-cpu": "", "@sage/xtrem-ui/detailed-icon-name-crowd": "", "@sage/xtrem-ui/detailed-icon-name-crown": "", "@sage/xtrem-ui/detailed-icon-name-data": "", "@sage/xtrem-ui/detailed-icon-name-database": "", "@sage/xtrem-ui/detailed-icon-name-decline": "", "@sage/xtrem-ui/detailed-icon-name-desktop": "", "@sage/xtrem-ui/detailed-icon-name-devices": "", "@sage/xtrem-ui/detailed-icon-name-dollar": "", "@sage/xtrem-ui/detailed-icon-name-download": "", "@sage/xtrem-ui/detailed-icon-name-ear": "", "@sage/xtrem-ui/detailed-icon-name-ecomm": "", "@sage/xtrem-ui/detailed-icon-name-euro": "", "@sage/xtrem-ui/detailed-icon-name-excavator": "", "@sage/xtrem-ui/detailed-icon-name-eye": "", "@sage/xtrem-ui/detailed-icon-name-factory": "", "@sage/xtrem-ui/detailed-icon-name-favorite": "", "@sage/xtrem-ui/detailed-icon-name-filter": "", "@sage/xtrem-ui/detailed-icon-name-financials": "", "@sage/xtrem-ui/detailed-icon-name-flag": "", "@sage/xtrem-ui/detailed-icon-name-folder": "", "@sage/xtrem-ui/detailed-icon-name-food": "", "@sage/xtrem-ui/detailed-icon-name-form": "", "@sage/xtrem-ui/detailed-icon-name-gauge": "", "@sage/xtrem-ui/detailed-icon-name-gears": "", "@sage/xtrem-ui/detailed-icon-name-glasses": "", "@sage/xtrem-ui/detailed-icon-name-globe": "", "@sage/xtrem-ui/detailed-icon-name-green": "", "@sage/xtrem-ui/detailed-icon-name-handshake": "", "@sage/xtrem-ui/detailed-icon-name-happy": "", "@sage/xtrem-ui/detailed-icon-name-heart": "", "@sage/xtrem-ui/detailed-icon-name-hide": "", "@sage/xtrem-ui/detailed-icon-name-holiday": "", "@sage/xtrem-ui/detailed-icon-name-home": "", "@sage/xtrem-ui/detailed-icon-name-hourglass": "", "@sage/xtrem-ui/detailed-icon-name-hub": "", "@sage/xtrem-ui/detailed-icon-name-idea": "", "@sage/xtrem-ui/detailed-icon-name-incline": "", "@sage/xtrem-ui/detailed-icon-name-industry": "", "@sage/xtrem-ui/detailed-icon-name-info": "", "@sage/xtrem-ui/detailed-icon-name-integration": "", "@sage/xtrem-ui/detailed-icon-name-jewelry": "", "@sage/xtrem-ui/detailed-icon-name-keys": "", "@sage/xtrem-ui/detailed-icon-name-lab": "", "@sage/xtrem-ui/detailed-icon-name-label": "", "@sage/xtrem-ui/detailed-icon-name-laptop": "", "@sage/xtrem-ui/detailed-icon-name-lightning": "", "@sage/xtrem-ui/detailed-icon-name-like": "", "@sage/xtrem-ui/detailed-icon-name-link": "", "@sage/xtrem-ui/detailed-icon-name-locations": "", "@sage/xtrem-ui/detailed-icon-name-lock": "", "@sage/xtrem-ui/detailed-icon-name-lock_unlocked": "", "@sage/xtrem-ui/detailed-icon-name-mail": "", "@sage/xtrem-ui/detailed-icon-name-map": "", "@sage/xtrem-ui/detailed-icon-name-medical": "", "@sage/xtrem-ui/detailed-icon-name-megaphone": "", "@sage/xtrem-ui/detailed-icon-name-memo": "", "@sage/xtrem-ui/detailed-icon-name-microphone": "", "@sage/xtrem-ui/detailed-icon-name-minus": "", "@sage/xtrem-ui/detailed-icon-name-mouse": "", "@sage/xtrem-ui/detailed-icon-name-newspaper": "", "@sage/xtrem-ui/detailed-icon-name-note": "", "@sage/xtrem-ui/detailed-icon-name-notebook": "", "@sage/xtrem-ui/detailed-icon-name-office": "", "@sage/xtrem-ui/detailed-icon-name-page": "", "@sage/xtrem-ui/detailed-icon-name-payment": "", "@sage/xtrem-ui/detailed-icon-name-payroll": "", "@sage/xtrem-ui/detailed-icon-name-pen": "", "@sage/xtrem-ui/detailed-icon-name-pencil": "", "@sage/xtrem-ui/detailed-icon-name-person": "", "@sage/xtrem-ui/detailed-icon-name-phone": "", "@sage/xtrem-ui/detailed-icon-name-pin": "", "@sage/xtrem-ui/detailed-icon-name-plus": "", "@sage/xtrem-ui/detailed-icon-name-point": "", "@sage/xtrem-ui/detailed-icon-name-pound": "", "@sage/xtrem-ui/detailed-icon-name-power": "", "@sage/xtrem-ui/detailed-icon-name-presentation": "", "@sage/xtrem-ui/detailed-icon-name-print": "", "@sage/xtrem-ui/detailed-icon-name-processing": "", "@sage/xtrem-ui/detailed-icon-name-puzzle": "", "@sage/xtrem-ui/detailed-icon-name-question": "", "@sage/xtrem-ui/detailed-icon-name-receipts": "", "@sage/xtrem-ui/detailed-icon-name-recycle": "", "@sage/xtrem-ui/detailed-icon-name-redo": "", "@sage/xtrem-ui/detailed-icon-name-remote": "", "@sage/xtrem-ui/detailed-icon-name-rocket": "", "@sage/xtrem-ui/detailed-icon-name-safe": "", "@sage/xtrem-ui/detailed-icon-name-satelite": "", "@sage/xtrem-ui/detailed-icon-name-savings": "", "@sage/xtrem-ui/detailed-icon-name-scissors": "", "@sage/xtrem-ui/detailed-icon-name-server": "", "@sage/xtrem-ui/detailed-icon-name-service": "", "@sage/xtrem-ui/detailed-icon-name-setting": "", "@sage/xtrem-ui/detailed-icon-name-share": "", "@sage/xtrem-ui/detailed-icon-name-shoes": "", "@sage/xtrem-ui/detailed-icon-name-shuffle": "", "@sage/xtrem-ui/detailed-icon-name-sign": "", "@sage/xtrem-ui/detailed-icon-name-sim": "", "@sage/xtrem-ui/detailed-icon-name-smartphone": "", "@sage/xtrem-ui/detailed-icon-name-stationeries": "", "@sage/xtrem-ui/detailed-icon-name-store": "", "@sage/xtrem-ui/detailed-icon-name-support": "", "@sage/xtrem-ui/detailed-icon-name-sync": "", "@sage/xtrem-ui/detailed-icon-name-tab": "", "@sage/xtrem-ui/detailed-icon-name-table": "", "@sage/xtrem-ui/detailed-icon-name-tablet": "", "@sage/xtrem-ui/detailed-icon-name-thermometer": "", "@sage/xtrem-ui/detailed-icon-name-timer": "", "@sage/xtrem-ui/detailed-icon-name-tools": "", "@sage/xtrem-ui/detailed-icon-name-travel": "", "@sage/xtrem-ui/detailed-icon-name-truck": "", "@sage/xtrem-ui/detailed-icon-name-undo": "", "@sage/xtrem-ui/detailed-icon-name-video": "", "@sage/xtrem-ui/detailed-icon-name-wallet": "", "@sage/xtrem-ui/detailed-icon-name-warehouse": "", "@sage/xtrem-ui/detailed-icon-name-warning": "", "@sage/xtrem-ui/detailed-icon-name-weather": "", "@sage/xtrem-ui/detailed-icon-name-wireless": "", "@sage/xtrem-ui/detailed-icon-name-wrench": "", "@sage/xtrem-ui/detailed-icon-name-writing": "", "@sage/xtrem-ui/dialog-actions-onload-unhandled-error": "", "@sage/xtrem-ui/dialog-loading": "", "@sage/xtrem-ui/dialogs-async-loader-button-keep-waiting": "", "@sage/xtrem-ui/dialogs-async-loader-button-notify-me": "", "@sage/xtrem-ui/dialogs-async-loader-button-stop": "", "@sage/xtrem-ui/dialogs-async-loader-waiting": "", "@sage/xtrem-ui/dirty-phantom-row-validation-error-message-mac": "", "@sage/xtrem-ui/dirty-phantom-row-validation-error-message-pc": "", "@sage/xtrem-ui/display-errors-back-to-full-display": "", "@sage/xtrem-ui/display-errors-back-to-full-display-tooltip": "", "@sage/xtrem-ui/display-errors-button": "", "@sage/xtrem-ui/distinct-count": "", "@sage/xtrem-ui/divisor": "", "@sage/xtrem-ui/divisor-helper-text": "", "@sage/xtrem-ui/document-metadata": "", "@sage/xtrem-ui/drag-drop-file": "", "@sage/xtrem-ui/duplicate-error": "", "@sage/xtrem-ui/empty-state-filter-text-no-results-button": "", "@sage/xtrem-ui/empty-state-filter-text-no-results-description": "", "@sage/xtrem-ui/empty-state-filter-text-title": "", "@sage/xtrem-ui/empty-state-text": "", "@sage/xtrem-ui/error": "", "@sage/xtrem-ui/error-detail-copied-to-clipboard": "", "@sage/xtrem-ui/error-loading-stickers": "", "@sage/xtrem-ui/export-format-csv": "", "@sage/xtrem-ui/export-format-excel": "", "@sage/xtrem-ui/failed-to-refresh-navigation-panel": "", "@sage/xtrem-ui/false": "", "@sage/xtrem-ui/field-mandatory": "", "@sage/xtrem-ui/field-max-items-value": "", "@sage/xtrem-ui/field-maximum-date-value": "", "@sage/xtrem-ui/field-maximum-length-value": "", "@sage/xtrem-ui/field-maximum-value": "", "@sage/xtrem-ui/field-min-items-value": "", "@sage/xtrem-ui/field-minimum-date-value": "", "@sage/xtrem-ui/field-minimum-length-value": "", "@sage/xtrem-ui/field-minimum-value": "", "@sage/xtrem-ui/field-non-zero": "", "@sage/xtrem-ui/field-not-valid": "", "@sage/xtrem-ui/field-select-mandatory": "", "@sage/xtrem-ui/field-select-mandatory-checkbox": "", "@sage/xtrem-ui/field-select-mandatory-or-enter": "", "@sage/xtrem-ui/file-component-browse-file": "", "@sage/xtrem-ui/file-component-browse-files": "", "@sage/xtrem-ui/file-component-drag-drop": "", "@sage/xtrem-ui/file-component-drag-drop-empty": "", "@sage/xtrem-ui/file-component-hide-upload-area": "", "@sage/xtrem-ui/file-component-show-upload-area": "", "@sage/xtrem-ui/file-upload-failed": "", "@sage/xtrem-ui/filter-manager-apply-button": "", "@sage/xtrem-ui/filter-manager-cancel-button": "", "@sage/xtrem-ui/filter-manager-checkbox": "", "@sage/xtrem-ui/filter-manager-clear-all-button": "", "@sage/xtrem-ui/filter-manager-close": "", "@sage/xtrem-ui/filter-manager-invalid-filters": "", "@sage/xtrem-ui/filter-manager-max-value": "", "@sage/xtrem-ui/filter-manager-min-value": "", "@sage/xtrem-ui/filter-manager-open": "", "@sage/xtrem-ui/filter-manager-range-end": "", "@sage/xtrem-ui/filter-manager-range-start": "", "@sage/xtrem-ui/filter-manager-title-header": "", "@sage/xtrem-ui/filter-range": "", "@sage/xtrem-ui/filter-search-placeholder": "", "@sage/xtrem-ui/filter-value-equals": "", "@sage/xtrem-ui/filter-value-greater-than-equal": "", "@sage/xtrem-ui/filter-value-less-than-equal": "", "@sage/xtrem-ui/filter-value-not-equal": "", "@sage/xtrem-ui/floating-filter-label": "", "@sage/xtrem-ui/footer-actions-more-button": "", "@sage/xtrem-ui/form-designer-var-current-date": "", "@sage/xtrem-ui/form-designer-var-generation-by": "", "@sage/xtrem-ui/form-designer-var-page-number": "", "@sage/xtrem-ui/form-designer-var-parameter": "", "@sage/xtrem-ui/form-designer-var-total-number-of-pages": "", "@sage/xtrem-ui/general_invalid_json": "", "@sage/xtrem-ui/general-finish-editing": "", "@sage/xtrem-ui/general-welcome-page": "", "@sage/xtrem-ui/generic-no": "", "@sage/xtrem-ui/generic-yes": "", "@sage/xtrem-ui/greaterThanOrEqual": "", "@sage/xtrem-ui/group-by": "", "@sage/xtrem-ui/group-by-day": "", "@sage/xtrem-ui/group-by-month": "", "@sage/xtrem-ui/group-by-this-column": "", "@sage/xtrem-ui/group-by-year": "", "@sage/xtrem-ui/helper-text-contains": "", "@sage/xtrem-ui/helper-text-ends-with": "", "@sage/xtrem-ui/helper-text-matches": "", "@sage/xtrem-ui/helper-text-starts-with": "", "@sage/xtrem-ui/hide-floating-filters": "", "@sage/xtrem-ui/hide-technical-details": "", "@sage/xtrem-ui/hierarchy-parent-page": "", "@sage/xtrem-ui/hours": "", "@sage/xtrem-ui/image-component-add-image": "", "@sage/xtrem-ui/image-field-remove-image": "", "@sage/xtrem-ui/infinite-indicator-end": "", "@sage/xtrem-ui/infinite-indicator-start": "", "@sage/xtrem-ui/insight-plural": "", "@sage/xtrem-ui/insight-singular": "", "@sage/xtrem-ui/invalid-file-type-message": "", "@sage/xtrem-ui/invalid-number": "", "@sage/xtrem-ui/invalid-response-no-data": "", "@sage/xtrem-ui/Label": "", "@sage/xtrem-ui/last-30-days": "", "@sage/xtrem-ui/last-7-days": "", "@sage/xtrem-ui/link-error-numbers-tooltip": "", "@sage/xtrem-ui/link-error-quantity": "", "@sage/xtrem-ui/link-errors-quantity": "", "@sage/xtrem-ui/list-printing": "", "@sage/xtrem-ui/list-printing-assignment": "", "@sage/xtrem-ui/lookup-dialog-confirm-select": "", "@sage/xtrem-ui/lookup-dialog-create-new-item": "", "@sage/xtrem-ui/lookup-dialog-dialog-title": "", "@sage/xtrem-ui/lookup-dialog-failed-fetch": "", "@sage/xtrem-ui/main-list-refresh": "", "@sage/xtrem-ui/maximum": "", "@sage/xtrem-ui/message-type-ai": "", "@sage/xtrem-ui/message-type-error": "", "@sage/xtrem-ui/message-type-info": "", "@sage/xtrem-ui/message-type-information": "", "@sage/xtrem-ui/message-type-success": "", "@sage/xtrem-ui/message-type-warning": "", "@sage/xtrem-ui/mime-type/application/atom+xml": "", "@sage/xtrem-ui/mime-type/application/ecmascript": "", "@sage/xtrem-ui/mime-type/application/font-woff": "", "@sage/xtrem-ui/mime-type/application/font-woff2": "", "@sage/xtrem-ui/mime-type/application/graphql": "", "@sage/xtrem-ui/mime-type/application/java-archive": "", "@sage/xtrem-ui/mime-type/application/javascript": "", "@sage/xtrem-ui/mime-type/application/json": "", "@sage/xtrem-ui/mime-type/application/ld+json": "", "@sage/xtrem-ui/mime-type/application/mac-binhex40": "", "@sage/xtrem-ui/mime-type/application/mathml+xml": "", "@sage/xtrem-ui/mime-type/application/ms-visio": "", "@sage/xtrem-ui/mime-type/application/ms-visio.stencil.macroEnabled.12": "", "@sage/xtrem-ui/mime-type/application/ms-visio.template": "", "@sage/xtrem-ui/mime-type/application/msword": "", "@sage/xtrem-ui/mime-type/application/octet-stream": "", "@sage/xtrem-ui/mime-type/application/pdf": "", "@sage/xtrem-ui/mime-type/application/postscript": "", "@sage/xtrem-ui/mime-type/application/rdf+xml": "", "@sage/xtrem-ui/mime-type/application/rss+xml": "", "@sage/xtrem-ui/mime-type/application/rtf": "", "@sage/xtrem-ui/mime-type/application/sql": "", "@sage/xtrem-ui/mime-type/application/vnd.amazon.ebook": "", "@sage/xtrem-ui/mime-type/application/vnd.android.package-archive": "", "@sage/xtrem-ui/mime-type/application/vnd.apple.installer+xml": "", "@sage/xtrem-ui/mime-type/application/vnd.apple.keynote": "", "@sage/xtrem-ui/mime-type/application/vnd.apple.numbers": "", "@sage/xtrem-ui/mime-type/application/vnd.apple.pages": "", "@sage/xtrem-ui/mime-type/application/vnd.google-earth.kml+xml": "", "@sage/xtrem-ui/mime-type/application/vnd.google-earth.kmz": "", "@sage/xtrem-ui/mime-type/application/vnd.mozilla.xul+xml": "", "@sage/xtrem-ui/mime-type/application/vnd.ms-access": "", "@sage/xtrem-ui/mime-type/application/vnd.ms-excel": "", "@sage/xtrem-ui/mime-type/application/vnd.ms-fontobject": "", "@sage/xtrem-ui/mime-type/application/vnd.ms-powerpoint": "", "@sage/xtrem-ui/mime-type/application/vnd.ms-project": "", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.graphics": "", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.presentation": "", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.spreadsheet": "", "@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.text": "", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.presentationml.presentation": "", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "", "@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.wordprocessingml.document": "", "@sage/xtrem-ui/mime-type/application/vnd.rn-realmedia": "", "@sage/xtrem-ui/mime-type/application/vnd.wap.wmlc": "", "@sage/xtrem-ui/mime-type/application/x-7z-compressed": "", "@sage/xtrem-ui/mime-type/application/x-abiword": "", "@sage/xtrem-ui/mime-type/application/x-bzip": "", "@sage/xtrem-ui/mime-type/application/x-bzip2": "", "@sage/xtrem-ui/mime-type/application/x-cd-image": "", "@sage/xtrem-ui/mime-type/application/x-chrome-extension": "", "@sage/xtrem-ui/mime-type/application/x-cocoa": "", "@sage/xtrem-ui/mime-type/application/x-csh": "", "@sage/xtrem-ui/mime-type/application/x-deb": "", "@sage/xtrem-ui/mime-type/application/x-dvi": "", "@sage/xtrem-ui/mime-type/application/x-font-opentype": "", "@sage/xtrem-ui/mime-type/application/x-font-otf": "", "@sage/xtrem-ui/mime-type/application/x-font-ttf": "", "@sage/xtrem-ui/mime-type/application/x-font-woff": "", "@sage/xtrem-ui/mime-type/application/x-font-woff2": "", "@sage/xtrem-ui/mime-type/application/x-gtar": "", "@sage/xtrem-ui/mime-type/application/x-gzip": "", "@sage/xtrem-ui/mime-type/application/x-hdf": "", "@sage/xtrem-ui/mime-type/application/x-httpd-php": "", "@sage/xtrem-ui/mime-type/application/x-httpd-php-source": "", "@sage/xtrem-ui/mime-type/application/x-java-applet": "", "@sage/xtrem-ui/mime-type/application/x-java-archive": "", "@sage/xtrem-ui/mime-type/application/x-java-archive-diff": "", "@sage/xtrem-ui/mime-type/application/x-java-jnlp-file": "", "@sage/xtrem-ui/mime-type/application/x-javascript": "", "@sage/xtrem-ui/mime-type/application/x-latex": "", "@sage/xtrem-ui/mime-type/application/x-lzh-compressed": "", "@sage/xtrem-ui/mime-type/application/x-makeself": "", "@sage/xtrem-ui/mime-type/application/x-mif": "", "@sage/xtrem-ui/mime-type/application/x-msaccess": "", "@sage/xtrem-ui/mime-type/application/x-msdownload": "", "@sage/xtrem-ui/mime-type/application/x-msmetafile": "", "@sage/xtrem-ui/mime-type/application/x-msmoney": "", "@sage/xtrem-ui/mime-type/application/x-perl": "", "@sage/xtrem-ui/mime-type/application/x-pilot": "", "@sage/xtrem-ui/mime-type/application/x-rar-compressed": "", "@sage/xtrem-ui/mime-type/application/x-redhat-package-manager": "", "@sage/xtrem-ui/mime-type/application/x-rpm": "", "@sage/xtrem-ui/mime-type/application/x-sea": "", "@sage/xtrem-ui/mime-type/application/x-sh": "", "@sage/xtrem-ui/mime-type/application/x-shockwave-flash": "", "@sage/xtrem-ui/mime-type/application/x-sql": "", "@sage/xtrem-ui/mime-type/application/x-stuffit": "", "@sage/xtrem-ui/mime-type/application/x-tar": "", "@sage/xtrem-ui/mime-type/application/x-tcl": "", "@sage/xtrem-ui/mime-type/application/x-tex": "", "@sage/xtrem-ui/mime-type/application/x-texinfo": "", "@sage/xtrem-ui/mime-type/application/x-troff": "", "@sage/xtrem-ui/mime-type/application/x-vrml": "", "@sage/xtrem-ui/mime-type/application/x-www-form-urlencoded": "", "@sage/xtrem-ui/mime-type/application/x-x509-ca-cert": "", "@sage/xtrem-ui/mime-type/application/x-xpinstall": "", "@sage/xtrem-ui/mime-type/application/xhtml+xml": "", "@sage/xtrem-ui/mime-type/application/xml": "", "@sage/xtrem-ui/mime-type/application/xslt+xml": "", "@sage/xtrem-ui/mime-type/application/zip": "", "@sage/xtrem-ui/mime-type/audio/aac": "", "@sage/xtrem-ui/mime-type/audio/amr": "", "@sage/xtrem-ui/mime-type/audio/midi": "", "@sage/xtrem-ui/mime-type/audio/mpeg": "", "@sage/xtrem-ui/mime-type/audio/ogg": "", "@sage/xtrem-ui/mime-type/audio/vnd.rn-realaudio": "", "@sage/xtrem-ui/mime-type/audio/wav": "", "@sage/xtrem-ui/mime-type/audio/x-m4a": "", "@sage/xtrem-ui/mime-type/audio/x-matroska": "", "@sage/xtrem-ui/mime-type/audio/x-mpegurl": "", "@sage/xtrem-ui/mime-type/audio/x-ms-wax": "", "@sage/xtrem-ui/mime-type/audio/x-ms-wma": "", "@sage/xtrem-ui/mime-type/audio/x-pn-realaudio": "", "@sage/xtrem-ui/mime-type/audio/x-pn-realaudio-plugin": "", "@sage/xtrem-ui/mime-type/audio/x-realaudio": "", "@sage/xtrem-ui/mime-type/audio/x-wav": "", "@sage/xtrem-ui/mime-type/chemical/x-pdb": "", "@sage/xtrem-ui/mime-type/image/bmp": "", "@sage/xtrem-ui/mime-type/image/cgm": "", "@sage/xtrem-ui/mime-type/image/gif": "", "@sage/xtrem-ui/mime-type/image/jpeg": "", "@sage/xtrem-ui/mime-type/image/png": "", "@sage/xtrem-ui/mime-type/image/svg+xml": "", "@sage/xtrem-ui/mime-type/image/tiff": "", "@sage/xtrem-ui/mime-type/image/vnd.microsoft.icon": "", "@sage/xtrem-ui/mime-type/image/vnd.wap.wbmp": "", "@sage/xtrem-ui/mime-type/image/webp": "", "@sage/xtrem-ui/mime-type/image/x-cmu-raster": "", "@sage/xtrem-ui/mime-type/image/x-icon": "", "@sage/xtrem-ui/mime-type/image/x-jng": "", "@sage/xtrem-ui/mime-type/image/x-ms-bmp": "", "@sage/xtrem-ui/mime-type/image/x-portable-anymap": "", "@sage/xtrem-ui/mime-type/image/x-portable-bitmap": "", "@sage/xtrem-ui/mime-type/image/x-portable-graymap": "", "@sage/xtrem-ui/mime-type/image/x-portable-pixmap": "", "@sage/xtrem-ui/mime-type/image/x-rgb": "", "@sage/xtrem-ui/mime-type/image/x-xbitmap": "", "@sage/xtrem-ui/mime-type/image/x-xpixmap": "", "@sage/xtrem-ui/mime-type/image/x-xwindowdump": "", "@sage/xtrem-ui/mime-type/model/vrml": "", "@sage/xtrem-ui/mime-type/multipart/form-data": "", "@sage/xtrem-ui/mime-type/text/cache-manifest": "", "@sage/xtrem-ui/mime-type/text/calendar": "", "@sage/xtrem-ui/mime-type/text/css": "", "@sage/xtrem-ui/mime-type/text/csv": "", "@sage/xtrem-ui/mime-type/text/ecmascript": "", "@sage/xtrem-ui/mime-type/text/html": "", "@sage/xtrem-ui/mime-type/text/javascript": "", "@sage/xtrem-ui/mime-type/text/markdown": "", "@sage/xtrem-ui/mime-type/text/mathml": "", "@sage/xtrem-ui/mime-type/text/plain": "", "@sage/xtrem-ui/mime-type/text/richtext": "", "@sage/xtrem-ui/mime-type/text/rtf": "", "@sage/xtrem-ui/mime-type/text/tab-separated-values": "", "@sage/xtrem-ui/mime-type/text/vnd.sun.j2me.app-descriptor": "", "@sage/xtrem-ui/mime-type/text/vnd.wap.wml": "", "@sage/xtrem-ui/mime-type/text/vnd.wap.wmlscript": "", "@sage/xtrem-ui/mime-type/text/vtt": "", "@sage/xtrem-ui/mime-type/text/x-component": "", "@sage/xtrem-ui/mime-type/text/x-cross-domain-policy": "", "@sage/xtrem-ui/mime-type/text/x-fortran": "", "@sage/xtrem-ui/mime-type/text/xml": "", "@sage/xtrem-ui/mime-type/video/3gpp": "", "@sage/xtrem-ui/mime-type/video/mp2t": "", "@sage/xtrem-ui/mime-type/video/mp4": "", "@sage/xtrem-ui/mime-type/video/mpeg": "", "@sage/xtrem-ui/mime-type/video/ogg": "", "@sage/xtrem-ui/mime-type/video/quicktime": "", "@sage/xtrem-ui/mime-type/video/vnd.rn-realvideo": "", "@sage/xtrem-ui/mime-type/video/webm": "", "@sage/xtrem-ui/mime-type/video/x-flv": "", "@sage/xtrem-ui/mime-type/video/x-m4v": "", "@sage/xtrem-ui/mime-type/video/x-matroska": "", "@sage/xtrem-ui/mime-type/video/x-mng": "", "@sage/xtrem-ui/mime-type/video/x-ms-asf": "", "@sage/xtrem-ui/mime-type/video/x-ms-wmv": "", "@sage/xtrem-ui/mime-type/video/x-ms-wvx": "", "@sage/xtrem-ui/mime-type/video/x-msvideo": "", "@sage/xtrem-ui/minimum": "", "@sage/xtrem-ui/minutes": "", "@sage/xtrem-ui/mobile-table-load-more": "", "@sage/xtrem-ui/multi-file-deposit-cancel": "", "@sage/xtrem-ui/multi-file-deposit-create-tag": "", "@sage/xtrem-ui/multi-file-deposit-description": "", "@sage/xtrem-ui/multi-file-deposit-edit-details": "", "@sage/xtrem-ui/multi-file-deposit-filename": "", "@sage/xtrem-ui/multi-file-deposit-modified": "", "@sage/xtrem-ui/multi-file-deposit-open-preview": "", "@sage/xtrem-ui/multi-file-deposit-remove": "", "@sage/xtrem-ui/multi-file-deposit-size": "", "@sage/xtrem-ui/multi-file-deposit-status": "", "@sage/xtrem-ui/multi-file-deposit-status-created": "", "@sage/xtrem-ui/multi-file-deposit-status-upload-cancelled": "", "@sage/xtrem-ui/multi-file-deposit-status-upload-failed": "", "@sage/xtrem-ui/multi-file-deposit-status-upload-rejected": "", "@sage/xtrem-ui/multi-file-deposit-status-uploaded": "", "@sage/xtrem-ui/multi-file-deposit-status-verified": "", "@sage/xtrem-ui/multi-file-deposit-tag-description": "", "@sage/xtrem-ui/multi-file-deposit-tag-id": "", "@sage/xtrem-ui/multi-file-deposit-tag-title": "", "@sage/xtrem-ui/multi-file-deposit-tags": "", "@sage/xtrem-ui/multi-file-deposit-title": "", "@sage/xtrem-ui/multi-file-deposit-type": "", "@sage/xtrem-ui/multi-file-deposit-uploaded": "", "@sage/xtrem-ui/multi-file-deposit-uploaded-by": "", "@sage/xtrem-ui/multi-file-upload-cancel": "", "@sage/xtrem-ui/multi-file-upload-cancel-title": "", "@sage/xtrem-ui/multi-file-upload-remove": "", "@sage/xtrem-ui/multi-file-upload-remove-title": "", "@sage/xtrem-ui/multiple-filter-range": "", "@sage/xtrem-ui/must-be-a-number": "", "@sage/xtrem-ui/must-be-between-1-and-365": "", "@sage/xtrem-ui/must-be-between-zero-and-four": "", "@sage/xtrem-ui/must-be-greater-than-zero": "", "@sage/xtrem-ui/name": "", "@sage/xtrem-ui/navigation-panel-failed": "", "@sage/xtrem-ui/navigation-panel-my-view": "", "@sage/xtrem-ui/navigation-panel-no-filter": "", "@sage/xtrem-ui/navigation-panel-no-results": "", "@sage/xtrem-ui/new": "", "@sage/xtrem-ui/next-day": "", "@sage/xtrem-ui/next-month": "", "@sage/xtrem-ui/next-record": "", "@sage/xtrem-ui/next-week": "", "@sage/xtrem-ui/next-year": "", "@sage/xtrem-ui/no-dashboard-heading": "", "@sage/xtrem-ui/no-dashboard-subtitle": "", "@sage/xtrem-ui/no-dashboard-title": "", "@sage/xtrem-ui/no-data": "", "@sage/xtrem-ui/no-file-available": "", "@sage/xtrem-ui/no-node": "", "@sage/xtrem-ui/no-page-content-found": "", "@sage/xtrem-ui/no-pages-found": "", "@sage/xtrem-ui/no-record-id-provided": "", "@sage/xtrem-ui/no-sticker-content-found": "", "@sage/xtrem-ui/no-stickers-found": "", "@sage/xtrem-ui/no-value": "", "@sage/xtrem-ui/no-widget-content-found": "", "@sage/xtrem-ui/number-format-separator": "", "@sage/xtrem-ui/ok": "", "@sage/xtrem-ui/open-custom-field-dialog": "", "@sage/xtrem-ui/open-dynamic-select": "", "@sage/xtrem-ui/open-full-screen": "", "@sage/xtrem-ui/open-header-section": "", "@sage/xtrem-ui/open-lookup": "", "@sage/xtrem-ui/open-record-history-dialog": "", "@sage/xtrem-ui/open-section": "", "@sage/xtrem-ui/openFilters": "", "@sage/xtrem-ui/page-failed-to-load": "", "@sage/xtrem-ui/parent": "", "@sage/xtrem-ui/pdf-metadata-file-name": "", "@sage/xtrem-ui/pdf-metadata-file-size": "", "@sage/xtrem-ui/pdf-metadata-file-type": "", "@sage/xtrem-ui/pdf-metadata-number-of-lines": "", "@sage/xtrem-ui/pdf-metadata-pdf-version": "", "@sage/xtrem-ui/pdf-metadata-producer": "", "@sage/xtrem-ui/pdf-metadata-resolution": "", "@sage/xtrem-ui/please-select-placeholder": "", "@sage/xtrem-ui/pod-collection-add-new": "", "@sage/xtrem-ui/pod-collection-cancel-button": "", "@sage/xtrem-ui/pod-collection-remove-button": "", "@sage/xtrem-ui/pod-collection-remove-text": "", "@sage/xtrem-ui/pod-collection-remove-title": "", "@sage/xtrem-ui/pod-placeholder-text": "", "@sage/xtrem-ui/pod-remove-item": "", "@sage/xtrem-ui/populate-list-title-default": "", "@sage/xtrem-ui/preview-action-download": "", "@sage/xtrem-ui/preview-action-print": "", "@sage/xtrem-ui/preview-close": "", "@sage/xtrem-ui/preview-go-to-page": "", "@sage/xtrem-ui/preview-more-file-info": "", "@sage/xtrem-ui/preview-more-options": "", "@sage/xtrem-ui/preview-more-thumbnails": "", "@sage/xtrem-ui/preview-no-file-selected": "", "@sage/xtrem-ui/preview-no-preview-available": "", "@sage/xtrem-ui/preview-page-next": "", "@sage/xtrem-ui/preview-page-prev": "", "@sage/xtrem-ui/preview-zoom-in": "", "@sage/xtrem-ui/preview-zoom-level": "", "@sage/xtrem-ui/preview-zoom-out": "", "@sage/xtrem-ui/previous-day": "", "@sage/xtrem-ui/previous-month": "", "@sage/xtrem-ui/previous-record": "", "@sage/xtrem-ui/previous-week": "", "@sage/xtrem-ui/previous-year": "", "@sage/xtrem-ui/property": "", "@sage/xtrem-ui/record-history-created-title": "", "@sage/xtrem-ui/record-history-details": "", "@sage/xtrem-ui/record-history-failed": "", "@sage/xtrem-ui/record-history-last-update-title": "", "@sage/xtrem-ui/reference-create-new-item-link": "", "@sage/xtrem-ui/reference-filter-clear-selection": "", "@sage/xtrem-ui/reference-lookup-dialog-search-placeholder": "", "@sage/xtrem-ui/reference-open-lookup-link": "", "@sage/xtrem-ui/return-arrow": "", "@sage/xtrem-ui/same-day": "", "@sage/xtrem-ui/same-month": "", "@sage/xtrem-ui/same-week": "", "@sage/xtrem-ui/same-year": "", "@sage/xtrem-ui/save-new-view-as": "", "@sage/xtrem-ui/see-more-items": "", "@sage/xtrem-ui/select-component-character": "", "@sage/xtrem-ui/select-component-characters": "", "@sage/xtrem-ui/select-component-loading": "", "@sage/xtrem-ui/select-component-no-results": "", "@sage/xtrem-ui/select-component-to-search": "", "@sage/xtrem-ui/select-component-type-more": "", "@sage/xtrem-ui/select-component-type-more-characters": "", "@sage/xtrem-ui/select-component-type-one-more-character": "", "@sage/xtrem-ui/select-component-type-to-search": "", "@sage/xtrem-ui/select-property": "", "@sage/xtrem-ui/select-record": "", "@sage/xtrem-ui/selection-card-filter-placeholder": "", "@sage/xtrem-ui/series-options": "", "@sage/xtrem-ui/show-floating-filters": "", "@sage/xtrem-ui/show-less": "", "@sage/xtrem-ui/show-more": "", "@sage/xtrem-ui/show-technical-details": "", "@sage/xtrem-ui/sidebar-apply-changes": "", "@sage/xtrem-ui/sidebar-apply-changes-and-create-new": "", "@sage/xtrem-ui/sidebar-mobile-apply-changes-and-create-new": "", "@sage/xtrem-ui/step-sequence-item-aria-complete": "", "@sage/xtrem-ui/step-sequence-item-aria-count": "", "@sage/xtrem-ui/step-sequence-item-aria-current": "", "@sage/xtrem-ui/sticker-actions-onload-unhandled-error": "", "@sage/xtrem-ui/string-contains": "", "@sage/xtrem-ui/string-ends-with": "", "@sage/xtrem-ui/string-starts-with": "", "@sage/xtrem-ui/sum": "", "@sage/xtrem-ui/switch-off-caps": "", "@sage/xtrem-ui/switch-on-caps": "", "@sage/xtrem-ui/table-addCurrentSelectionToFilter": "", "@sage/xtrem-ui/table-addToLabels": "", "@sage/xtrem-ui/table-addToValues": "", "@sage/xtrem-ui/table-advancedFilterAnd": "", "@sage/xtrem-ui/table-advancedFilterApply": "", "@sage/xtrem-ui/table-advancedFilterBlank": "", "@sage/xtrem-ui/table-advancedFilterBuilder": "", "@sage/xtrem-ui/table-advancedFilterBuilderAddButtonTooltip": "", "@sage/xtrem-ui/table-advancedFilterBuilderAddCondition": "", "@sage/xtrem-ui/table-advancedFilterBuilderAddJoin": "", "@sage/xtrem-ui/table-advancedFilterBuilderApply": "", "@sage/xtrem-ui/table-advancedFilterBuilderCancel": "", "@sage/xtrem-ui/table-advancedFilterBuilderEnterValue": "", "@sage/xtrem-ui/table-advancedFilterBuilderMoveDownButtonTooltip": "", "@sage/xtrem-ui/table-advancedFilterBuilderMoveUpButtonTooltip": "", "@sage/xtrem-ui/table-advancedFilterBuilderRemoveButtonTooltip": "", "@sage/xtrem-ui/table-advancedFilterBuilderSelectColumn": "", "@sage/xtrem-ui/table-advancedFilterBuilderSelectOption": "", "@sage/xtrem-ui/table-advancedFilterBuilderTitle": "", "@sage/xtrem-ui/table-advancedFilterBuilderValidationAlreadyApplied": "", "@sage/xtrem-ui/table-advancedFilterBuilderValidationEnterValue": "", "@sage/xtrem-ui/table-advancedFilterBuilderValidationIncomplete": "", "@sage/xtrem-ui/table-advancedFilterBuilderValidationSelectColumn": "", "@sage/xtrem-ui/table-advancedFilterBuilderValidationSelectOption": "", "@sage/xtrem-ui/table-advancedFilterContains": "", "@sage/xtrem-ui/table-advancedFilterEndsWith": "", "@sage/xtrem-ui/table-advancedFilterEquals": "", "@sage/xtrem-ui/table-advancedFilterFalse": "", "@sage/xtrem-ui/table-advancedFilterGreaterThan": "", "@sage/xtrem-ui/table-advancedFilterGreaterThanOrEqual": "", "@sage/xtrem-ui/table-advancedFilterLessThan": "", "@sage/xtrem-ui/table-advancedFilterLessThanOrEqual": "", "@sage/xtrem-ui/table-advancedFilterNotBlank": "", "@sage/xtrem-ui/table-advancedFilterNotContains": "", "@sage/xtrem-ui/table-advancedFilterNotEqual": "", "@sage/xtrem-ui/table-advancedFilterOr": "", "@sage/xtrem-ui/table-advancedFilterStartsWith": "", "@sage/xtrem-ui/table-advancedFilterTextEquals": "", "@sage/xtrem-ui/table-advancedFilterTextNotEqual": "", "@sage/xtrem-ui/table-advancedFilterTrue": "", "@sage/xtrem-ui/table-advancedFilterValidationExtraEndBracket": "", "@sage/xtrem-ui/table-advancedFilterValidationInvalidColumn": "", "@sage/xtrem-ui/table-advancedFilterValidationInvalidDate": "", "@sage/xtrem-ui/table-advancedFilterValidationInvalidJoinOperator": "", "@sage/xtrem-ui/table-advancedFilterValidationInvalidOption": "", "@sage/xtrem-ui/table-advancedFilterValidationJoinOperatorMismatch": "", "@sage/xtrem-ui/table-advancedFilterValidationMessage": "", "@sage/xtrem-ui/table-advancedFilterValidationMessageAtEnd": "", "@sage/xtrem-ui/table-advancedFilterValidationMissingColumn": "", "@sage/xtrem-ui/table-advancedFilterValidationMissingCondition": "", "@sage/xtrem-ui/table-advancedFilterValidationMissingEndBracket": "", "@sage/xtrem-ui/table-advancedFilterValidationMissingOption": "", "@sage/xtrem-ui/table-advancedFilterValidationMissingQuote": "", "@sage/xtrem-ui/table-advancedFilterValidationMissingValue": "", "@sage/xtrem-ui/table-advancedFilterValidationNotANumber": "", "@sage/xtrem-ui/table-advancedSettings": "", "@sage/xtrem-ui/table-after": "", "@sage/xtrem-ui/table-aggregate": "", "@sage/xtrem-ui/table-andCondition": "", "@sage/xtrem-ui/table-animation": "", "@sage/xtrem-ui/table-applyFilter": "", "@sage/xtrem-ui/table-april": "", "@sage/xtrem-ui/table-area": "", "@sage/xtrem-ui/table-areaChart": "", "@sage/xtrem-ui/table-AreaColumnCombo": "", "@sage/xtrem-ui/table-areaColumnComboTooltip": "", "@sage/xtrem-ui/table-areaGroup": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderColumn": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderFilterItem": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderGroupItem": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderItem": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderItemValidation": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderJoinOperator": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderList": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderOption": "", "@sage/xtrem-ui/table-ariaAdvancedFilterBuilderValueP": "", "@sage/xtrem-ui/table-ariaAdvancedFilterInput": "", "@sage/xtrem-ui/table-ariaChartMenuClose": "", "@sage/xtrem-ui/table-ariaChartSelected": "", "@sage/xtrem-ui/table-ariaChecked": "", "@sage/xtrem-ui/table-ariaColumn": "", "@sage/xtrem-ui/table-ariaColumnFiltered": "", "@sage/xtrem-ui/table-ariaColumnGroup": "", "@sage/xtrem-ui/table-ariaColumnPanelList": "", "@sage/xtrem-ui/table-ariaColumnSelectAll": "", "@sage/xtrem-ui/table-ariaDateFilterInput": "", "@sage/xtrem-ui/table-ariaDefaultListName": "", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentAggFuncSeparator": "", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentDescription": "", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentSortAscending": "", "@sage/xtrem-ui/table-ariaDropZoneColumnComponentSortDescending": "", "@sage/xtrem-ui/table-ariaDropZoneColumnGroupItemDescription": "", "@sage/xtrem-ui/table-ariaDropZoneColumnValueItemDescription": "", "@sage/xtrem-ui/table-ariaFilterColumn": "", "@sage/xtrem-ui/table-ariaFilterColumnsInput": "", "@sage/xtrem-ui/table-ariaFilterFromValue": "", "@sage/xtrem-ui/table-ariaFilteringOperator": "", "@sage/xtrem-ui/table-ariaFilterInput": "", "@sage/xtrem-ui/table-ariaFilterList": "", "@sage/xtrem-ui/table-ariaFilterMenuOpen": "", "@sage/xtrem-ui/table-ariaFilterPanelList": "", "@sage/xtrem-ui/table-ariaFilterToValue": "", "@sage/xtrem-ui/table-ariaFilterValue": "", "@sage/xtrem-ui/table-ariaHeaderSelection": "", "@sage/xtrem-ui/table-ariaHidden": "", "@sage/xtrem-ui/table-ariaIndeterminate": "", "@sage/xtrem-ui/table-ariaInputEditor": "", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterAutocomplete": "", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderAddField": "", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderColumnSelectField": "", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderJoinSelectField": "", "@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderOptionSelectField": "", "@sage/xtrem-ui/table-ariaLabelAggregationFunction": "", "@sage/xtrem-ui/table-ariaLabelCellEditor": "", "@sage/xtrem-ui/table-ariaLabelColumnFilter": "", "@sage/xtrem-ui/table-ariaLabelColumnMenu": "", "@sage/xtrem-ui/table-ariaLabelContextMenu": "", "@sage/xtrem-ui/table-ariaLabelDialog": "", "@sage/xtrem-ui/table-ariaLabelRichSelectDeleteSelection": "", "@sage/xtrem-ui/table-ariaLabelRichSelectDeselectAllItems": "", "@sage/xtrem-ui/table-ariaLabelRichSelectField": "", "@sage/xtrem-ui/table-ariaLabelRichSelectToggleSelection": "", "@sage/xtrem-ui/table-ariaLabelSelectField": "", "@sage/xtrem-ui/table-ariaLabelSubMenu": "", "@sage/xtrem-ui/table-ariaLabelTooltip": "", "@sage/xtrem-ui/table-ariaMenuColumn": "", "@sage/xtrem-ui/table-ariaPageSizeSelectorLabel": "", "@sage/xtrem-ui/table-ariaPivotDropZonePanelLabel": "", "@sage/xtrem-ui/table-ariaRowDeselect": "", "@sage/xtrem-ui/table-ariaRowGroupDropZonePanelLabel": "", "@sage/xtrem-ui/table-ariaRowSelect": "", "@sage/xtrem-ui/table-ariaRowSelectAll": "", "@sage/xtrem-ui/table-ariaRowSelectionDisabled": "", "@sage/xtrem-ui/table-ariaRowToggleSelection": "", "@sage/xtrem-ui/table-ariaSearch": "", "@sage/xtrem-ui/table-ariaSearchFilterValues": "", "@sage/xtrem-ui/table-ariaSkeletonCellLoading": "", "@sage/xtrem-ui/table-ariaSkeletonCellLoadingFailed": "", "@sage/xtrem-ui/table-ariaSortableColumn": "", "@sage/xtrem-ui/table-ariaToggleCellValue": "", "@sage/xtrem-ui/table-ariaToggleVisibility": "", "@sage/xtrem-ui/table-ariaUnchecked": "", "@sage/xtrem-ui/table-ariaValuesDropZonePanelLabel": "", "@sage/xtrem-ui/table-ariaVisible": "", "@sage/xtrem-ui/table-august": "", "@sage/xtrem-ui/table-automatic": "", "@sage/xtrem-ui/table-autoRotate": "", "@sage/xtrem-ui/table-autosizeAllColumns": "", "@sage/xtrem-ui/table-autosizeThiscolumn": "", "@sage/xtrem-ui/table-avg": "", "@sage/xtrem-ui/table-axis": "", "@sage/xtrem-ui/table-axisType": "", "@sage/xtrem-ui/table-background": "", "@sage/xtrem-ui/table-bar": "", "@sage/xtrem-ui/table-barChart": "", "@sage/xtrem-ui/table-barGroup": "", "@sage/xtrem-ui/table-before": "", "@sage/xtrem-ui/table-blank": "", "@sage/xtrem-ui/table-blanks": "", "@sage/xtrem-ui/table-blur": "", "@sage/xtrem-ui/table-bold": "", "@sage/xtrem-ui/table-boldItalic": "", "@sage/xtrem-ui/table-bottom": "", "@sage/xtrem-ui/table-boxPlot": "", "@sage/xtrem-ui/table-boxPlotTooltip": "", "@sage/xtrem-ui/table-bubble": "", "@sage/xtrem-ui/table-bubbleTooltip": "", "@sage/xtrem-ui/table-calendar-view": "", "@sage/xtrem-ui/table-callout": "", "@sage/xtrem-ui/table-calloutLabels": "", "@sage/xtrem-ui/table-cancelFgroupFilterSelectilter": "", "@sage/xtrem-ui/table-cancelFilter": "", "@sage/xtrem-ui/table-cap": "", "@sage/xtrem-ui/table-capLengthRatio": "", "@sage/xtrem-ui/table-categories": "", "@sage/xtrem-ui/table-category": "", "@sage/xtrem-ui/table-categoryAdd": "", "@sage/xtrem-ui/table-categoryValues": "", "@sage/xtrem-ui/table-chart": "", "@sage/xtrem-ui/table-chartAdvancedSettings": "", "@sage/xtrem-ui/table-chartDownload": "", "@sage/xtrem-ui/table-chartDownloadToolbarTooltip": "", "@sage/xtrem-ui/table-chartEdit": "", "@sage/xtrem-ui/table-chartLink": "", "@sage/xtrem-ui/table-chartLinkToolbarTooltip": "", "@sage/xtrem-ui/table-chartMenuToolbarTooltip": "", "@sage/xtrem-ui/table-chartRange": "", "@sage/xtrem-ui/table-chartSettingsToolbarTooltip": "", "@sage/xtrem-ui/table-chartStyle": "", "@sage/xtrem-ui/table-chartSubtitle": "", "@sage/xtrem-ui/table-chartTitle": "", "@sage/xtrem-ui/table-chartTitles": "", "@sage/xtrem-ui/table-chartUnlink": "", "@sage/xtrem-ui/table-chartUnlinkToolbarTooltip": "", "@sage/xtrem-ui/table-chooseColumns": "", "@sage/xtrem-ui/table-circle": "", "@sage/xtrem-ui/table-clearFilter": "", "@sage/xtrem-ui/table-collapseAll": "", "@sage/xtrem-ui/table-color": "", "@sage/xtrem-ui/table-column": "", "@sage/xtrem-ui/table-column-settings": "", "@sage/xtrem-ui/table-columnChart": "", "@sage/xtrem-ui/table-columnChooser": "", "@sage/xtrem-ui/table-columnFilter": "", "@sage/xtrem-ui/table-columnGroup": "", "@sage/xtrem-ui/table-columnLineCombo": "", "@sage/xtrem-ui/table-columnLineComboTooltip": "", "@sage/xtrem-ui/table-columns": "", "@sage/xtrem-ui/table-combinationChart": "", "@sage/xtrem-ui/table-combinationGroup": "", "@sage/xtrem-ui/table-compare-with-previous-period": "", "@sage/xtrem-ui/table-connectorLine": "", "@sage/xtrem-ui/table-contains": "", "@sage/xtrem-ui/table-copy": "", "@sage/xtrem-ui/table-copyWithGroupHeaders": "", "@sage/xtrem-ui/table-copyWithHeaders": "", "@sage/xtrem-ui/table-count": "", "@sage/xtrem-ui/table-create": "", "@sage/xtrem-ui/table-cross": "", "@sage/xtrem-ui/table-crosshair": "", "@sage/xtrem-ui/table-crosshairLabel": "", "@sage/xtrem-ui/table-crosshairSnap": "", "@sage/xtrem-ui/table-csvExport": "", "@sage/xtrem-ui/table-ctrlC": "", "@sage/xtrem-ui/table-ctrlV": "", "@sage/xtrem-ui/table-ctrlX": "", "@sage/xtrem-ui/table-customCombo": "", "@sage/xtrem-ui/table-customComboTooltip": "", "@sage/xtrem-ui/table-cut": "", "@sage/xtrem-ui/table-data": "", "@sage/xtrem-ui/table-dateFilter": "", "@sage/xtrem-ui/table-dateFormatOoo": "", "@sage/xtrem-ui/table-december": "", "@sage/xtrem-ui/table-decimalSeparator": "", "@sage/xtrem-ui/table-defaultCategory": "", "@sage/xtrem-ui/table-diamond": "", "@sage/xtrem-ui/table-direction": "", "@sage/xtrem-ui/table-donut": "", "@sage/xtrem-ui/table-donutTooltip": "", "@sage/xtrem-ui/table-doughnut": "", "@sage/xtrem-ui/table-doughnutTooltip": "", "@sage/xtrem-ui/table-durationMillis": "", "@sage/xtrem-ui/table-empty": "", "@sage/xtrem-ui/table-enabled": "", "@sage/xtrem-ui/table-endAngle": "", "@sage/xtrem-ui/table-endsWith": "", "@sage/xtrem-ui/table-equals": "", "@sage/xtrem-ui/table-excelExport": "", "@sage/xtrem-ui/table-excelXmlExport": "", "@sage/xtrem-ui/table-expandAll": "", "@sage/xtrem-ui/table-export": "", "@sage/xtrem-ui/table-export-failed": "", "@sage/xtrem-ui/table-export-service-no-columns": "", "@sage/xtrem-ui/table-export-started": "", "@sage/xtrem-ui/table-false": "", "@sage/xtrem-ui/table-february": "", "@sage/xtrem-ui/table-fillOpacity": "", "@sage/xtrem-ui/table-filter-aria-label": "", "@sage/xtrem-ui/table-filteredRows": "", "@sage/xtrem-ui/table-filterOoo": "", "@sage/xtrem-ui/table-filters": "", "@sage/xtrem-ui/table-first": "", "@sage/xtrem-ui/table-firstPage": "", "@sage/xtrem-ui/table-fixed": "", "@sage/xtrem-ui/table-font": "", "@sage/xtrem-ui/table-footerTotal": "", "@sage/xtrem-ui/table-format": "", "@sage/xtrem-ui/table-greaterThan": "", "@sage/xtrem-ui/table-greaterThanOrEqual": "", "@sage/xtrem-ui/table-gridLines": "", "@sage/xtrem-ui/table-group": "", "@sage/xtrem-ui/table-group-total": "", "@sage/xtrem-ui/table-groupBy": "", "@sage/xtrem-ui/table-groupedAreaTooltip": "", "@sage/xtrem-ui/table-groupedBar": "", "@sage/xtrem-ui/table-groupedBarFull": "", "@sage/xtrem-ui/table-groupedBarTooltip": "", "@sage/xtrem-ui/table-groupedColumn": "", "@sage/xtrem-ui/table-groupedColumnFull": "", "@sage/xtrem-ui/table-groupedColumnTooltip": "", "@sage/xtrem-ui/table-groupedSeriesGroupType": "", "@sage/xtrem-ui/table-groupPadding": "", "@sage/xtrem-ui/table-groups": "", "@sage/xtrem-ui/table-heart": "", "@sage/xtrem-ui/table-heatmap": "", "@sage/xtrem-ui/table-heatmapTooltip": "", "@sage/xtrem-ui/table-height": "", "@sage/xtrem-ui/table-hierarchicalChart": "", "@sage/xtrem-ui/table-hierarchicalGroup": "", "@sage/xtrem-ui/table-histogram": "", "@sage/xtrem-ui/table-histogramBinCount": "", "@sage/xtrem-ui/table-histogramChart": "", "@sage/xtrem-ui/table-histogramFrequency": "", "@sage/xtrem-ui/table-histogramGroup": "", "@sage/xtrem-ui/table-histogramTooltip": "", "@sage/xtrem-ui/table-horizontal": "", "@sage/xtrem-ui/table-horizontalAxisTitle": "", "@sage/xtrem-ui/table-innerRadius": "", "@sage/xtrem-ui/table-inRange": "", "@sage/xtrem-ui/table-inRangeEnd": "", "@sage/xtrem-ui/table-inRangeStart": "", "@sage/xtrem-ui/table-inside": "", "@sage/xtrem-ui/table-invalidColor": "", "@sage/xtrem-ui/table-invalidDate": "", "@sage/xtrem-ui/table-invalidNumber": "", "@sage/xtrem-ui/table-italic": "", "@sage/xtrem-ui/table-itemPaddingX": "", "@sage/xtrem-ui/table-itemPaddingY": "", "@sage/xtrem-ui/table-itemSpacing": "", "@sage/xtrem-ui/table-january": "", "@sage/xtrem-ui/table-july": "", "@sage/xtrem-ui/table-june": "", "@sage/xtrem-ui/table-labelPlacement": "", "@sage/xtrem-ui/table-labelRotation": "", "@sage/xtrem-ui/table-labels": "", "@sage/xtrem-ui/table-last": "", "@sage/xtrem-ui/table-lastPage": "", "@sage/xtrem-ui/table-layoutHorizontalSpacing": "", "@sage/xtrem-ui/table-layoutVerticalSpacing": "", "@sage/xtrem-ui/table-left": "", "@sage/xtrem-ui/table-legend": "", "@sage/xtrem-ui/table-legendEnabled": "", "@sage/xtrem-ui/table-length": "", "@sage/xtrem-ui/table-lessThan": "", "@sage/xtrem-ui/table-lessThanOrEqual": "", "@sage/xtrem-ui/table-line": "", "@sage/xtrem-ui/table-line-number": "", "@sage/xtrem-ui/table-lineChart": "", "@sage/xtrem-ui/table-lineDash": "", "@sage/xtrem-ui/table-lineDashOffset": "", "@sage/xtrem-ui/table-lineGroup": "", "@sage/xtrem-ui/table-lineTooltip": "", "@sage/xtrem-ui/table-lineWidth": "", "@sage/xtrem-ui/table-loadingError": "", "@sage/xtrem-ui/table-loadingOoo": "", "@sage/xtrem-ui/table-march": "", "@sage/xtrem-ui/table-markerPadding": "", "@sage/xtrem-ui/table-markers": "", "@sage/xtrem-ui/table-markerSize": "", "@sage/xtrem-ui/table-markerStroke": "", "@sage/xtrem-ui/table-max": "", "@sage/xtrem-ui/table-maxSize": "", "@sage/xtrem-ui/table-may": "", "@sage/xtrem-ui/table-min": "", "@sage/xtrem-ui/table-miniChart": "", "@sage/xtrem-ui/table-minSize": "", "@sage/xtrem-ui/table-more": "", "@sage/xtrem-ui/table-navigator": "", "@sage/xtrem-ui/table-next": "", "@sage/xtrem-ui/table-nextPage": "", "@sage/xtrem-ui/table-nightingale": "", "@sage/xtrem-ui/table-nightingaleTooltip": "", "@sage/xtrem-ui/table-noAggregation": "", "@sage/xtrem-ui/table-noDataToChart": "", "@sage/xtrem-ui/table-noMatches": "", "@sage/xtrem-ui/table-none": "", "@sage/xtrem-ui/table-noPin": "", "@sage/xtrem-ui/table-normal": "", "@sage/xtrem-ui/table-normalizedArea": "", "@sage/xtrem-ui/table-normalizedAreaFull": "", "@sage/xtrem-ui/table-normalizedAreaTooltip": "", "@sage/xtrem-ui/table-normalizedBar": "", "@sage/xtrem-ui/table-normalizedBarFull": "", "@sage/xtrem-ui/table-normalizedBarTooltip": "", "@sage/xtrem-ui/table-normalizedColumn": "", "@sage/xtrem-ui/table-normalizedColumnFull": "", "@sage/xtrem-ui/table-normalizedColumnTooltip": "", "@sage/xtrem-ui/table-normalizedLine": "", "@sage/xtrem-ui/table-normalizedLineTooltip": "", "@sage/xtrem-ui/table-normalizedSeriesGroupType": "", "@sage/xtrem-ui/table-noRowsToShow": "", "@sage/xtrem-ui/table-notBlank": "", "@sage/xtrem-ui/table-notContains": "", "@sage/xtrem-ui/table-notEqual": "", "@sage/xtrem-ui/table-november": "", "@sage/xtrem-ui/table-number": "", "@sage/xtrem-ui/table-numberFilter": "", "@sage/xtrem-ui/table-numeric-filter-greater-than-equals-value": "", "@sage/xtrem-ui/table-numeric-filter-greater-than-value": "", "@sage/xtrem-ui/table-numeric-filter-less-than-equals-value": "", "@sage/xtrem-ui/table-numeric-filter-less-than-value": "", "@sage/xtrem-ui/table-numeric-filter-not-value": "", "@sage/xtrem-ui/table-numeric-filter-range-value": "", "@sage/xtrem-ui/table-october": "", "@sage/xtrem-ui/table-of": "", "@sage/xtrem-ui/table-offset": "", "@sage/xtrem-ui/table-offsets": "", "@sage/xtrem-ui/table-open-column-panel": "", "@sage/xtrem-ui/table-orCondition": "", "@sage/xtrem-ui/table-orientation": "", "@sage/xtrem-ui/table-outside": "", "@sage/xtrem-ui/table-padding": "", "@sage/xtrem-ui/table-page": "", "@sage/xtrem-ui/table-pageLastRowUnknown": "", "@sage/xtrem-ui/table-pageSizeSelectorLabel": "", "@sage/xtrem-ui/table-paired": "", "@sage/xtrem-ui/table-parallel": "", "@sage/xtrem-ui/table-paste": "", "@sage/xtrem-ui/table-perpendicular": "", "@sage/xtrem-ui/table-pie": "", "@sage/xtrem-ui/table-pieChart": "", "@sage/xtrem-ui/table-pieGroup": "", "@sage/xtrem-ui/table-pieTooltip": "", "@sage/xtrem-ui/table-pinColumn": "", "@sage/xtrem-ui/table-pinLeft": "", "@sage/xtrem-ui/table-pinRight": "", "@sage/xtrem-ui/table-pivotChart": "", "@sage/xtrem-ui/table-pivotChartAndPivotMode": "", "@sage/xtrem-ui/table-pivotChartRequiresPivotMode": "", "@sage/xtrem-ui/table-pivotChartTitle": "", "@sage/xtrem-ui/table-pivotColumnGroupTotals": "", "@sage/xtrem-ui/table-pivotColumnsEmptyMessage": "", "@sage/xtrem-ui/table-pivotMode": "", "@sage/xtrem-ui/table-pivots": "", "@sage/xtrem-ui/table-plus": "", "@sage/xtrem-ui/table-polarAxis": "", "@sage/xtrem-ui/table-polarAxisTitle": "", "@sage/xtrem-ui/table-polarChart": "", "@sage/xtrem-ui/table-polarGroup": "", "@sage/xtrem-ui/table-polygon": "", "@sage/xtrem-ui/table-position": "", "@sage/xtrem-ui/table-positionRatio": "", "@sage/xtrem-ui/table-predefined": "", "@sage/xtrem-ui/table-preferredLength": "", "@sage/xtrem-ui/table-previous": "", "@sage/xtrem-ui/table-previousPage": "", "@sage/xtrem-ui/table-print": "", "@sage/xtrem-ui/table-radarArea": "", "@sage/xtrem-ui/table-radarAreaTooltip": "", "@sage/xtrem-ui/table-radarLine": "", "@sage/xtrem-ui/table-radarLineTooltip": "", "@sage/xtrem-ui/table-radialBar": "", "@sage/xtrem-ui/table-radialBarTooltip": "", "@sage/xtrem-ui/table-radialColumn": "", "@sage/xtrem-ui/table-radialColumnTooltip": "", "@sage/xtrem-ui/table-radiusAxis": "", "@sage/xtrem-ui/table-radiusAxisPosition": "", "@sage/xtrem-ui/table-rangeArea": "", "@sage/xtrem-ui/table-rangeAreaTooltip": "", "@sage/xtrem-ui/table-rangeBar": "", "@sage/xtrem-ui/table-rangeBarTooltip": "", "@sage/xtrem-ui/table-rangeChartTitle": "", "@sage/xtrem-ui/table-removeFromLabels": "", "@sage/xtrem-ui/table-removeFromValues": "", "@sage/xtrem-ui/table-resetColumns": "", "@sage/xtrem-ui/table-resetFilter": "", "@sage/xtrem-ui/table-reverseDirection": "", "@sage/xtrem-ui/table-right": "", "@sage/xtrem-ui/table-rowDragRow": "", "@sage/xtrem-ui/table-rowDragRows": "", "@sage/xtrem-ui/table-rowGroupColumnsEmptyMessage": "", "@sage/xtrem-ui/table-scatter": "", "@sage/xtrem-ui/table-scatterGroup": "", "@sage/xtrem-ui/table-scatterTooltip": "", "@sage/xtrem-ui/table-scrollingStep": "", "@sage/xtrem-ui/table-scrollingZoom": "", "@sage/xtrem-ui/table-searchOoo": "", "@sage/xtrem-ui/table-secondaryAxis": "", "@sage/xtrem-ui/table-sectorLabels": "", "@sage/xtrem-ui/table-select-all": "", "@sage/xtrem-ui/table-selectAll": "", "@sage/xtrem-ui/table-selectAllSearchResults": "", "@sage/xtrem-ui/table-selectedRows": "", "@sage/xtrem-ui/table-selectingZoom": "", "@sage/xtrem-ui/table-september": "", "@sage/xtrem-ui/table-series": "", "@sage/xtrem-ui/table-seriesAdd": "", "@sage/xtrem-ui/table-seriesChartType": "", "@sage/xtrem-ui/table-seriesGroupType": "", "@sage/xtrem-ui/table-seriesItemLabels": "", "@sage/xtrem-ui/table-seriesItemNegative": "", "@sage/xtrem-ui/table-seriesItemPositive": "", "@sage/xtrem-ui/table-seriesItems": "", "@sage/xtrem-ui/table-seriesItemType": "", "@sage/xtrem-ui/table-seriesLabels": "", "@sage/xtrem-ui/table-seriesPadding": "", "@sage/xtrem-ui/table-seriesType": "", "@sage/xtrem-ui/table-setFilter": "", "@sage/xtrem-ui/table-settings": "", "@sage/xtrem-ui/table-shadow": "", "@sage/xtrem-ui/table-shape": "", "@sage/xtrem-ui/table-show": "", "@sage/xtrem-ui/table-size": "", "@sage/xtrem-ui/table-sortAscending": "", "@sage/xtrem-ui/table-sortDescending": "", "@sage/xtrem-ui/table-sortUnSort": "", "@sage/xtrem-ui/table-spacing": "", "@sage/xtrem-ui/table-specializedChart": "", "@sage/xtrem-ui/table-specializedGroup": "", "@sage/xtrem-ui/table-square": "", "@sage/xtrem-ui/table-stackedArea": "", "@sage/xtrem-ui/table-stackedAreaFull": "", "@sage/xtrem-ui/table-stackedAreaTooltip": "", "@sage/xtrem-ui/table-stackedBar": "", "@sage/xtrem-ui/table-stackedBarFull": "", "@sage/xtrem-ui/table-stackedBarTooltip": "", "@sage/xtrem-ui/table-stackedColumn": "", "@sage/xtrem-ui/table-stackedColumnFull": "", "@sage/xtrem-ui/table-stackedColumnTooltip": "", "@sage/xtrem-ui/table-stackedLine": "", "@sage/xtrem-ui/table-stackedLineTooltip": "", "@sage/xtrem-ui/table-stackedSeriesGroupType": "", "@sage/xtrem-ui/table-startAngle": "", "@sage/xtrem-ui/table-startsWith": "", "@sage/xtrem-ui/table-statisticalChart": "", "@sage/xtrem-ui/table-statisticalGroup": "", "@sage/xtrem-ui/table-strokeColor": "", "@sage/xtrem-ui/table-strokeOpacity": "", "@sage/xtrem-ui/table-strokeWidth": "", "@sage/xtrem-ui/table-sum": "", "@sage/xtrem-ui/table-summary-empty-default-text": "", "@sage/xtrem-ui/table-sunburst": "", "@sage/xtrem-ui/table-sunburstTooltip": "", "@sage/xtrem-ui/table-switch-to-card-view": "", "@sage/xtrem-ui/table-switch-to-table-view": "", "@sage/xtrem-ui/table-switchCategorySeries": "", "@sage/xtrem-ui/table-table-view": "", "@sage/xtrem-ui/table-textFilter": "", "@sage/xtrem-ui/table-thickness": "", "@sage/xtrem-ui/table-thousandSeparator": "", "@sage/xtrem-ui/table-ticks": "", "@sage/xtrem-ui/table-tile": "", "@sage/xtrem-ui/table-time": "", "@sage/xtrem-ui/table-timeFormat": "", "@sage/xtrem-ui/table-timeFormatDashesYYYYMMDD": "", "@sage/xtrem-ui/table-timeFormatDotsDDMYY": "", "@sage/xtrem-ui/table-timeFormatDotsMDDYY": "", "@sage/xtrem-ui/table-timeFormatHHMMSS": "", "@sage/xtrem-ui/table-timeFormatHHMMSSAmPm": "", "@sage/xtrem-ui/table-timeFormatSlashesDDMMYY": "", "@sage/xtrem-ui/table-timeFormatSlashesDDMMYYYY": "", "@sage/xtrem-ui/table-timeFormatSlashesMMDDYY": "", "@sage/xtrem-ui/table-timeFormatSlashesMMDDYYYY": "", "@sage/xtrem-ui/table-timeFormatSpacesDDMMMMYYYY": "", "@sage/xtrem-ui/table-title": "", "@sage/xtrem-ui/table-titlePlaceholder": "", "@sage/xtrem-ui/table-to": "", "@sage/xtrem-ui/table-tooltips": "", "@sage/xtrem-ui/table-top": "", "@sage/xtrem-ui/table-totalAndFilteredRows": "", "@sage/xtrem-ui/table-totalRows": "", "@sage/xtrem-ui/table-treemap": "", "@sage/xtrem-ui/table-treemapTooltip": "", "@sage/xtrem-ui/table-triangle": "", "@sage/xtrem-ui/table-true": "", "@sage/xtrem-ui/table-ungroupAll": "", "@sage/xtrem-ui/table-ungroupBy": "", "@sage/xtrem-ui/table-valueAggregation": "", "@sage/xtrem-ui/table-valueColumnsEmptyMessage": "", "@sage/xtrem-ui/table-values": "", "@sage/xtrem-ui/table-variant_card": "", "@sage/xtrem-ui/table-variant-area": "", "@sage/xtrem-ui/table-variant-table": "", "@sage/xtrem-ui/table-vertical": "", "@sage/xtrem-ui/table-verticalAxisTitle": "", "@sage/xtrem-ui/table-views-close-button": "", "@sage/xtrem-ui/table-views-default": "", "@sage/xtrem-ui/table-views-manage": "", "@sage/xtrem-ui/table-views-open-button": "", "@sage/xtrem-ui/table-views-save": "", "@sage/xtrem-ui/table-views-save-as": "", "@sage/xtrem-ui/table-views-save-failed": "", "@sage/xtrem-ui/table-views-saved": "", "@sage/xtrem-ui/table-views-select": "", "@sage/xtrem-ui/table-waterfall": "", "@sage/xtrem-ui/table-waterfallTooltip": "", "@sage/xtrem-ui/table-weight": "", "@sage/xtrem-ui/table-whisker": "", "@sage/xtrem-ui/table-width": "", "@sage/xtrem-ui/table-xAxis": "", "@sage/xtrem-ui/table-xOffset": "", "@sage/xtrem-ui/table-xRotation": "", "@sage/xtrem-ui/table-xType": "", "@sage/xtrem-ui/table-xyChart": "", "@sage/xtrem-ui/table-xyValues": "", "@sage/xtrem-ui/table-yAxis": "", "@sage/xtrem-ui/table-yOffset": "", "@sage/xtrem-ui/table-yRotation": "", "@sage/xtrem-ui/table-zoom": "", "@sage/xtrem-ui/text-editor-cancel": "", "@sage/xtrem-ui/text-editor-cancel-button": "", "@sage/xtrem-ui/text-editor-character-counter": "", "@sage/xtrem-ui/text-editor-character-limit": "", "@sage/xtrem-ui/text-editor-content-editor": "", "@sage/xtrem-ui/text-editor-save": "", "@sage/xtrem-ui/text-editor-save-button": "", "@sage/xtrem-ui/text-editor-toolbar": "", "@sage/xtrem-ui/text-format-capital-bold": "", "@sage/xtrem-ui/text-format-capital-bullet-list": "", "@sage/xtrem-ui/text-format-capital-italic": "", "@sage/xtrem-ui/text-format-capital-number-list": "", "@sage/xtrem-ui/toggle-navigation-panel": "", "@sage/xtrem-ui/toggle-widget-list": "", "@sage/xtrem-ui/true": "", "@sage/xtrem-ui/tunnel-already-open-description": "", "@sage/xtrem-ui/tunnel-already-open-title": "", "@sage/xtrem-ui/tunnel-link-see-more": "", "@sage/xtrem-ui/tunnel-record-not-suitable-description": "", "@sage/xtrem-ui/tunnel-record-not-suitable-title": "", "@sage/xtrem-ui/ungroup": "", "@sage/xtrem-ui/unsaved-changes-content": "", "@sage/xtrem-ui/unsaved-changes-go-back": "", "@sage/xtrem-ui/unsaved-changes-title": "", "@sage/xtrem-ui/unsaved-changes-yes": "", "@sage/xtrem-ui/update-error": "", "@sage/xtrem-ui/upload-in-progress": "", "@sage/xtrem-ui/validation-error-and-more": "", "@sage/xtrem-ui/validation-error-of-children": "", "@sage/xtrem-ui/validation-error-total": "", "@sage/xtrem-ui/validation-error-with-number-grid": "", "@sage/xtrem-ui/validation-error-with-number-pod": "", "@sage/xtrem-ui/validation-errors": "", "@sage/xtrem-ui/validation-errors-and-more": "", "@sage/xtrem-ui/validation-errors-number": "", "@sage/xtrem-ui/validation-errors-total": "", "@sage/xtrem-ui/validation-errors-unknown": "", "@sage/xtrem-ui/validation-errors-with-number-grid": "", "@sage/xtrem-ui/validation-errors-with-number-pod": "", "@sage/xtrem-ui/visual-process-lookup-page-dialog-title": "", "@sage/xtrem-ui/visual-process-lookup-page-path-column": "", "@sage/xtrem-ui/visual-process-lookup-page-title-column": "", "@sage/xtrem-ui/visual-process-transactions-not-supported": "", "@sage/xtrem-ui/widget-action-create": "", "@sage/xtrem-ui/widget-action-create-help": "", "@sage/xtrem-ui/widget-action-see-all": "", "@sage/xtrem-ui/widget-action-see-all-help": "", "@sage/xtrem-ui/widget-actions": "", "@sage/xtrem-ui/widget-editor-action-decimal-digits-mandatory": "", "@sage/xtrem-ui/widget-editor-action-goes-to": "", "@sage/xtrem-ui/widget-editor-action-label-mandatory": "", "@sage/xtrem-ui/widget-editor-action-page-path": "", "@sage/xtrem-ui/widget-editor-action-page-title": "", "@sage/xtrem-ui/widget-editor-add": "", "@sage/xtrem-ui/widget-editor-basic-step-missing-node": "", "@sage/xtrem-ui/widget-editor-basic-step-missing-title": "", "@sage/xtrem-ui/widget-editor-basic-step-title": "", "@sage/xtrem-ui/widget-editor-cancel-edit": "", "@sage/xtrem-ui/widget-editor-content-formatting": "", "@sage/xtrem-ui/widget-editor-content-step-subtitle": "", "@sage/xtrem-ui/widget-editor-content-step-title": "", "@sage/xtrem-ui/widget-editor-content-step-vertical-axes": "", "@sage/xtrem-ui/widget-editor-data-step-title": "", "@sage/xtrem-ui/widget-editor-entry-node": "", "@sage/xtrem-ui/widget-editor-filter-step-title": "", "@sage/xtrem-ui/widget-editor-grouping-method": "", "@sage/xtrem-ui/widget-editor-grouping-property": "", "@sage/xtrem-ui/widget-editor-horizontal-axis": "", "@sage/xtrem-ui/widget-editor-horizontal-axis-label": "", "@sage/xtrem-ui/widget-editor-icon": "", "@sage/xtrem-ui/widget-editor-layout-step-no-actions-in-preview": "", "@sage/xtrem-ui/widget-editor-layout-step-title": "", "@sage/xtrem-ui/widget-editor-max-num-of-values": "", "@sage/xtrem-ui/widget-editor-sorting-step-title": "", "@sage/xtrem-ui/widget-editor-step-basic-title": "", "@sage/xtrem-ui/widget-editor-step-content-title": "", "@sage/xtrem-ui/widget-editor-step-data-title": "", "@sage/xtrem-ui/widget-editor-step-filters-title": "", "@sage/xtrem-ui/widget-editor-step-layout-title": "", "@sage/xtrem-ui/widget-editor-step-sorting-title": "", "@sage/xtrem-ui/widget-editor-subtitle": "", "@sage/xtrem-ui/widget-editor-title-edit": "", "@sage/xtrem-ui/widget-editor-title-new": "", "@sage/xtrem-ui/widget-editor-type-bar-chart": "", "@sage/xtrem-ui/widget-editor-type-bar-chart-description": "", "@sage/xtrem-ui/widget-editor-type-indicator-tile": "", "@sage/xtrem-ui/widget-editor-type-indicator-tile-description": "", "@sage/xtrem-ui/widget-editor-type-line-chart": "", "@sage/xtrem-ui/widget-editor-type-line-chart-description": "", "@sage/xtrem-ui/widget-editor-type-table": "", "@sage/xtrem-ui/widget-editor-type-table-description": "", "@sage/xtrem-ui/widget-editor-update": "", "@sage/xtrem-ui/widget-editor-vertical-axis-label": "", "@sage/xtrem-ui/widget-editor-widget-category": "", "@sage/xtrem-ui/widget-editor-widget-title": "", "@sage/xtrem-ui/widget-preview-label": "", "@sage/xtrem-ui/wizard-finish": "", "@sage/xtrem-ui/wizard-next": "", "@sage/xtrem-ui/wizard-previous": "", "@sage/xtrem-ui/workflow-add-trigger-event": "", "@sage/xtrem-ui/workflow-collapse": "", "@sage/xtrem-ui/workflow-component-add-action": "", "@sage/xtrem-ui/workflow-component-add-condition": "", "@sage/xtrem-ui/workflow-component-add-step": "", "@sage/xtrem-ui/workflow-component-edge-false-path": "", "@sage/xtrem-ui/workflow-component-edge-true-path": "", "@sage/xtrem-ui/workflow-component-header-label-action": "", "@sage/xtrem-ui/workflow-component-header-label-condition": "", "@sage/xtrem-ui/workflow-component-header-label-start": "", "@sage/xtrem-ui/workflow-component-wizard-event-selection": "", "@sage/xtrem-ui/workflow-component-wizard-step-configuration": "", "@sage/xtrem-ui/workflow-component-wizard-title-action": "", "@sage/xtrem-ui/workflow-component-wizard-title-trigger": "", "@sage/xtrem-ui/workflow-component-wizard-trigger-selection": "", "@sage/xtrem-ui/workflow-delete-node-chain-message": "", "@sage/xtrem-ui/workflow-delete-node-chain-title": "", "@sage/xtrem-ui/workflow-empty": "", "@sage/xtrem-ui/workflow-expand": "", "@sage/xtrem-ui/workflow-fit-view": "", "@sage/xtrem-ui/workflow-redo": "", "@sage/xtrem-ui/workflow-undo": "", "@sage/xtrem-ui/workflow-zoom-in": "", "@sage/xtrem-ui/workflow-zoom-out": ""}