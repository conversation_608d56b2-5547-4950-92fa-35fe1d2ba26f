import type React from 'react';

import type { PillProps as OriginalPillProps } from 'carbon-react/esm/components/pill';

declare module 'carbon-react/esm/components/pill' {
    export interface PillProps extends OriginalPillProps {
        showIcon?: boolean;
        style?: React.CSSProperties;
    }
    class Pill extends React.FC<PillProps> {}
}

declare module 'i18n-js';
declare module 'sanitize-html';
declare module 'pdfjs-dist/build/pdf.worker.min.mjs' {
    const entry: string;
    export default entry;
}

interface CommonTextboxProps extends React.HTMLProps<HTMLInputElement> {}

declare module 'react' {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    export interface DOMAttributes<T> {
        popover?: 'auto';
        popovertarget?: string;
    }
}
