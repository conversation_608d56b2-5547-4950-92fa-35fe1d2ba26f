import { ClientNode } from '@sage/xtrem-client';
import { prettyDOM, render, RenderResult } from '@testing-library/react';
import React from 'react';
import { Provider } from 'react-redux';
import { MockStoreEnhanced } from 'redux-mock-store';
import { DeepPartial } from 'ts-essentials';
import { PageProperties } from '../../component/container/container-properties';
import { FieldDecoratorProps, FieldInternalValue, FieldKey } from '../../component/types';
import { XtremAppState } from '../../redux/state';
import { AccessBindings, PageDefinition } from '../../service/page-definition';
import { PageMetadata } from '../../service/page-metadata';
import { ScreenBase } from '../../service/screen-base';
import { xtremConsole } from '../../utils/console';
import { deepMerge } from '@sage/xtrem-shared';
import { getMockPageDefinition } from './common-helpers';
import { addFieldToState, addPageControlObject, getMockState, getMockStore } from './mock-store-helpers';
import UE from '@testing-library/user-event';

interface RenderWithReduxOptions {
    initialState?: DeepPartial<XtremAppState>;
    mockStore?: MockStoreEnhanced<XtremAppState, {}>;
}

interface RenderFieldWithReduxOptions<T extends FieldKey, CT extends ScreenBase = any, N extends ClientNode = any>
    extends RenderWithReduxOptions {
    screenId?: string;
    elementId?: string;
    fieldType: T;
    fieldProperties: FieldDecoratorProps<T, CT, N>;
    fieldValue: FieldInternalValue<T> | null;
    mockActions?: boolean;
    partialPageDefinition?: Partial<PageDefinition>;
    partialPageMetadata?: Partial<PageMetadata>;
    accessBindings?: AccessBindings;
}

export const userEvent = UE.setup({
    advanceTimers: jest.advanceTimersByTime,
});

export function renderWithRedux<T extends FieldKey, CT extends ScreenBase = any, N extends ClientNode = any>(
    ui: React.ReactElement,
    {
        fieldType,
        screenId = 'TestPage',
        elementId = 'elementId',
        fieldProperties,
        mockStore,
        initialState,
        fieldValue,
        mockActions,
        partialPageDefinition,
        partialPageMetadata,
        accessBindings = {},
    }: RenderFieldWithReduxOptions<T, CT, N>,
) {
    const state = getMockState(initialState as Partial<XtremAppState>);
    const pageDefinition = getMockPageDefinition(screenId, partialPageDefinition, partialPageMetadata);
    pageDefinition.accessBindings = accessBindings;
    state.screenDefinitions[screenId] = pageDefinition;
    addFieldToState<T>(fieldType, state, screenId, elementId, fieldProperties, fieldValue);
    const pageProperties: PageProperties = {
        isTransient: false,
    };
    addPageControlObject(state, screenId, pageProperties);
    const currentState = mockStore ? deepMerge(mockStore.getState(), initialState) : state;
    const store = getMockStore(currentState, !!mockActions);
    const component = (props?: any) => (
        <Provider store={store} {...props}>
            {ui}
        </Provider>
    );
    return {
        ...render(component()),
        component,
        store,
    } as RenderResult & {
        store: MockStoreEnhanced<XtremAppState, {}>;
        component: (
            props: any,
        ) => React.ReactElement<
            any,
            | string
            | ((props: any) => React.ReactElement<any, any>)
            | (new (props: any) => React.Component<any, any, any>)
        >;
    };
}

export const printBody = () => {
    xtremConsole.log(prettyDOM());
};
