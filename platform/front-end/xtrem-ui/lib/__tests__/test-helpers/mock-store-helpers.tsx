let mockStore: any = null;
let mockActions = false;
const actionsMock = {};

import { LicenseManager } from '@ag-grid-enterprise/core';
import * as path from 'path';
import * as fs from 'fs';
import * as React from 'react';
import { objectKeys } from '@sage/xtrem-shared';

const licencePath = path.resolve(__dirname, '..', '..', '..', 'ag-grid-license.txt');
if (fs.existsSync(licencePath)) {
    LicenseManager.setLicenseKey(fs.readFileSync(licencePath, 'utf-8'));
}

export const applyActionMocks = () => {
    const actions = jest.requireActual('../../redux/actions');
    objectKeys(actions)
        .filter(actionKey => typeof actions[actionKey] === 'function')
        .forEach(actionKey => {
            actionsMock[actionKey] = jest.fn((...args: any) => {
                if (mockActions) {
                    const action = { type: pascalCase(actionKey), value: args };
                    if (asyncActionsNames.includes(actionKey)) {
                        const promise = Promise.resolve(action) as any;
                        promise.type = action.type;
                        return promise;
                    } else {
                        return action;
                    }
                } else {
                    return actions[actionKey](...args);
                }
            });
        });
};

jest.mock('../../redux/actions', () => {
    applyActionMocks();
    return actionsMock;
});

jest.mock('../../redux', () => {
    const actual = jest.requireActual('../../redux');
    return {
        getStore: () => (mockStore ? mockStore : actual.getStore),
        actions: actionsMock,
        ActionType: actual.ActionType,
    };
});

jest.mock('../../redux/store', () => {
    const actual = jest.requireActual('../../redux/store');
    return {
        getStore: () => (mockStore ? mockStore : actual.getStore),
    };
});

jest.mock('axios', () => {
    return {
        post: jest.fn().mockResolvedValue({}),
        get: jest.fn(),
        CancelToken: { source: jest.fn().mockReturnValue({ token: undefined }) },
    };
});

import { applyMiddleware, createStore, Middleware } from 'redux';
import configureMockStore, { MockStoreEnhanced } from 'redux-mock-store';
import reduxThunk from 'redux-thunk';
import { UiComponentProperties } from '../../component/abstract-ui-control-object';
import * as controlObjects from '../../component/control-objects';
import { PageActionDecoratorProperties } from '../../component/page-action/page-action-decorator';
import {
    ComponentKey,
    ContainerControlObjectConstructorProps,
    ContainerKey,
    ControlObjectConstructorProps,
    ControlObjectInstance,
    ControlObjectProps,
    FieldComponentProps,
    FieldControlObjectConstructorProps,
    FieldDecoratorProps,
    FieldInternalValue,
    FieldKey,
    LayoutContent,
} from '../../component/types';
import { AppAction } from '../../redux/action-types';
import { actionSubscriptionMiddleware } from '../../redux/middleware/action-subscription-middleware';
import rootReducer from '../../redux/reducer';
import { XtremAppState } from '../../redux/state';
import { PageArticleItem } from '../../service/layout-types';
import { Page, PageDeveloperApi } from '../../service/page';
import { ScreenBase } from '../../service/screen-base';
import * as events from '../../utils/events';
import { pascalCase } from '../../utils/transformers';
import { XtremUiPluginComponentProps } from '../../service/plugin-service';
import { ClientNode } from '@sage/xtrem-client';
import { getPageDefinitionFromState } from '../../utils/state-utils';
import { ValidationResult } from '../../service/screen-base-definition';
import { PageProperties } from '../../component/container/container-properties';
import { xtremConsole } from '../../utils/console';
import { ReadonlyFieldProperties } from '../../component/readonly-field-control-object';

const asyncActionsNames = ['validate'];

// TODO Add the screenDefinition and change to addPageToState
export const addPageControlObject = (
    state: XtremAppState,
    pageId: string,
    pageProperties: PageProperties | Object = {},
) => {
    const getProperties = jest.fn().mockReturnValue(pageProperties);
    const setProperties = jest.fn();
    const dispatchPageValidation = jest.fn().mockReturnValue(Promise.resolve(undefined));

    state.screenDefinitions[pageId].metadata.controlObjects[pageId] = new controlObjects.PageControlObject({
        dispatchPageValidation,
        getUiComponentProperties: getProperties,
        layout: null,
        screenId: pageId,
        setUiComponentProperties: setProperties,
        getFocussedField: jest.fn(),
    });
    state.screenDefinitions[pageId].metadata.uiComponentProperties[pageId] = pageProperties as any;
};

// TODO Add the screenDefinition and change to addStickerToState
export const addStickerControlObject = (
    state: XtremAppState,
    stickerId: string,
    stickerProperties: controlObjects.StickerProperties | Object = {},
) => {
    const getProperties = jest.fn().mockReturnValue(stickerProperties);
    const setProperties = jest.fn();
    const dispatchStickerValidation = jest.fn().mockReturnValue(Promise.resolve(undefined));
    const updateMenuItem = jest.fn();

    state.screenDefinitions[stickerId].metadata.controlObjects[stickerId] = new controlObjects.StickerControlObject({
        dispatchPageValidation: dispatchStickerValidation,
        getUiComponentProperties: getProperties,
        layout: {},
        screenId: stickerId,
        setUiComponentProperties: setProperties,
        updateMenuItem,
    });
    state.screenDefinitions[stickerId].metadata.uiComponentProperties[stickerId] = stickerProperties;
};

export const addBlockToState = (
    state: XtremAppState,
    screenId: string,
    blockId: string,
    blockProperties: Partial<controlObjects.BlockProperties> & {
        parent?: () => controlObjects.SectionControlObject<any>;
    } = {},
    layoutItems: PageArticleItem[] = [],
    isBlockValid = true,
) => {
    return addSectionElementToState<ContainerKey.Block>(
        state,
        screenId,
        // TODO fix this cast - TS 5.4.5 complains about dispatchTileValidation being required in IBlockControlObject but missing in BlockControlObject
        controlObjects.BlockControlObject as any,
        'block',
        blockId,
        { ...blockProperties, _controlObjectType: ContainerKey.Block },
        layoutItems,
        isBlockValid,
    );
};
export const addTileToState = (
    state: XtremAppState,
    screenId: string,
    blockId: string,
    blockProperties: Partial<controlObjects.BlockProperties> & {
        parent?: () => controlObjects.SectionControlObject<any>;
    } = {},
    layoutItems: PageArticleItem[] = [],
    isBlockValid = true,
) => {
    return addSectionElementToState<ContainerKey.Tile>(
        state,
        screenId,
        // TODO fix this cast - TS 5.4.5 complains about dispatchTileValidation being required in ITileControlObject but missing in TileControlObject
        controlObjects.TileControlObject as any,
        'tile',
        blockId,
        blockProperties,
        layoutItems,
        isBlockValid,
    );
};

export const addSectionElementToState = <T extends ContainerKey>(
    state: XtremAppState,
    screenId: string,
    classConstructor: new (props: ContainerControlObjectConstructorProps<T>) => ControlObjectInstance<T>,
    containerCategory: string,
    elementId: string,
    containerProperties: Partial<UiComponentProperties> = {},
    layoutItems: PageArticleItem[] = [],
    isContainerValid = true,
) => {
    const getProperties = jest.fn().mockReturnValue(containerProperties);
    const setProperties = jest.fn();
    const getValidationState = jest.fn().mockReturnValue(isContainerValid);
    const layoutItem = {
        $category: containerCategory,
        $containerId: elementId,
        $layout: {
            $items: layoutItems,
        },
    } as LayoutContent<T>;
    const controlObject = new classConstructor({
        target: screenId,
        screenId,
        elementId,
        getUiComponentProperties: getProperties,
        setUiComponentProperties: setProperties,
        getValidationState,
        layout: layoutItem,
        parent: (containerProperties as any).parent,
    } as ContainerControlObjectConstructorProps<T>);

    state.screenDefinitions[screenId].metadata.controlObjects[elementId] = controlObject;
    state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] = containerProperties;
    state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId]._controlObjectType =
        controlObject.componentType;

    return layoutItem;
};

export const addSectionToState = (
    state: XtremAppState,
    screenId: string,
    sectionId: string,
    sectionProperties: Partial<controlObjects.SectionProperties> = {},
    layoutItems: Partial<PageArticleItem>[] = [],
    isSectionValid = true,
) => {
    const properties: Partial<controlObjects.SectionProperties> = {
        ...sectionProperties,
        _controlObjectType: ContainerKey.Section,
    };
    const getProperties = jest.fn().mockReturnValue(properties);
    const setProperties = jest.fn();
    const getValidationState = jest.fn().mockReturnValue(isSectionValid);
    const dispatchSectionValidation = jest.fn().mockReturnValue(Promise.resolve(undefined));
    const sectionItem = {
        $category: 'section',
        $containerId: sectionId,
        $layout: {
            $items: layoutItems,
        },
    } as PageArticleItem;

    const controlObject = new controlObjects.SectionControlObject({
        elementId: sectionId,
        dispatchSectionValidation,
        getUiComponentProperties: getProperties,
        getValidationState: getValidationState,
        layout: sectionItem,
        screenId,
        setUiComponentProperties: setProperties,
    });
    state.screenDefinitions[screenId].metadata.controlObjects[sectionId] = controlObject;
    state.screenDefinitions[screenId].metadata.uiComponentProperties[sectionId] = properties;

    state.screenDefinitions[screenId].metadata.layout.$items.push(sectionItem);

    return sectionItem;
};

export const getControlObjectFromFieldType = (fieldType: FieldKey): { new (...args: any[]): any } => {
    switch (fieldType) {
        case FieldKey.Aggregate:
            return controlObjects.AggregateControlObject;
        case FieldKey.Button:
            return controlObjects.ButtonControlObject;
        case FieldKey.Card:
            return controlObjects.Card;
        case FieldKey.Calendar:
            return controlObjects.CalendarControlObject;
        case FieldKey.Chart:
            return controlObjects.ChartControlObject;
        case FieldKey.Checkbox:
            return controlObjects.CheckboxControlObject;
        case FieldKey.ContentTable:
            return controlObjects.ContentTableControlObject;
        case FieldKey.Count:
            return controlObjects.CountControlObject;
        case FieldKey.Date:
            return controlObjects.DateControlObject;
        case FieldKey.DetailList:
            return controlObjects.DetailListControlObject;
        case FieldKey.DropdownList:
            return controlObjects.DropdownListControlObject;
        case FieldKey.File:
            return controlObjects.FileControlObject;
        case FieldKey.FileDeposit:
            return controlObjects.FileDepositControlObject;
        case FieldKey.FilterEditor:
            return controlObjects.FilterEditorControlObject;
        case FieldKey.FilterSelect:
            return controlObjects.FilterSelectControlObject;
        case FieldKey.FormDesigner:
            return controlObjects.FormDesignerControlObject;
        case FieldKey.Icon:
            return controlObjects.IconControlObject;
        case FieldKey.Image:
            return controlObjects.ImageControlObject;
        case FieldKey.Label:
            return controlObjects.LabelControlObject;
        case FieldKey.Link:
            return controlObjects.LinkControlObject;
        case FieldKey.Message:
            return controlObjects.MessageControlObject;
        case FieldKey.MultiDropdown:
            return controlObjects.MultiDropdownControlObject;
        case FieldKey.MultiFileDeposit:
            return controlObjects.MultiFileDepositControlObject;
        case FieldKey.MultiReference:
            return controlObjects.MultiReferenceControlObject;
        case FieldKey.NestedGrid:
            return controlObjects.NestedGridControlObject;
        case FieldKey.NodeBrowserTree:
            return controlObjects.NodeBrowserTreeControlObject;
        case FieldKey.Numeric:
            return controlObjects.NumericControlObject;
        case FieldKey.Preview:
            return controlObjects.PreviewControlObject;
        case FieldKey.Pod:
            return controlObjects.PodControlObject;
        case FieldKey.Progress:
            return controlObjects.ProgressControlObject;
        case FieldKey.Radio:
            return controlObjects.RadioControlObject;
        case FieldKey.Reference:
            return controlObjects.ReferenceControlObject;
        case FieldKey.RelativeDate:
            return controlObjects.RelativeDateControlObject;
        case FieldKey.RichText:
            return controlObjects.RichTextControlObject;
        case FieldKey.Plugin:
            return controlObjects.PluginControlObject;
        case FieldKey.PodCollection:
            return controlObjects.PodCollectionControlObject;
        case FieldKey.Select:
            return controlObjects.SelectControlObject;
        case FieldKey.SelectionCard:
            return controlObjects.SelectionCardControlObject;
        case FieldKey.Separator:
            return controlObjects.SeparatorControlObject;
        case FieldKey.StepSequence:
            return controlObjects.StepSequenceControlObject;
        case FieldKey.Switch:
            return controlObjects.SwitchControlObject;
        case FieldKey.StaticContent:
            return controlObjects.StaticContentControlObject;
        case FieldKey.Table:
            return controlObjects.TableControlObject;
        case FieldKey.TableSummary:
            return controlObjects.TableSummaryControlObject;
        case FieldKey.Text:
            return controlObjects.TextControlObject;
        case FieldKey.Time:
            return controlObjects.TimeControlObject;
        case FieldKey.DatetimeRange:
            return controlObjects.DatetimeRangeControlObject;
        case FieldKey.Datetime:
            return controlObjects.DatetimeControlObject;
        case FieldKey.TextArea:
            return controlObjects.TextAreaControlObject;
        case FieldKey.Tree:
            return controlObjects.TreeControlObject;
        case FieldKey.Toggle:
            return controlObjects.ToggleControlObject;
        case FieldKey.VisualProcess:
            return controlObjects.VisualProcessControlObject;
        case FieldKey.VitalPod:
            return controlObjects.VitalPodControlObject;
        case FieldKey.DynamicPod:
            return controlObjects.DynamicPodControlObject;
        case FieldKey.DynamicSelect:
            return controlObjects.DynamicSelectControlObject;
        default:
            throw new Error(`Unsupported field ${fieldType}.`);
    }
};

export const addFieldToState = <T extends FieldKey, CT extends ScreenBase = any, N extends ClientNode = any>(
    componentType: T,
    state: XtremAppState,
    screenId: string,
    fieldId: string,
    fieldProperties: FieldDecoratorProps<T, CT, N>,
    // IG: Had to put the any, because TS Jenkins is fucked up and is not taking the null values.
    fieldValue: any = 'Test value' as FieldInternalValue<T>, //FieldInternalValue<T> | null = 'Test value' as FieldInternalValue<T>,
    screenBase?: ScreenBase,
): PageArticleItem => {
    const fieldConstructor = getControlObjectFromFieldType(componentType);
    state.screenDefinitions[screenId].metadata.controlObjects[fieldId] = createFieldControlObject<T>(
        componentType,
        screenId,
        fieldConstructor,
        fieldId,
        fieldValue as FieldInternalValue<T>,
        fieldProperties,
        // TODO fix this cast - added for TS 5.4.5 upgrade
    ) as any;

    (fieldProperties as any)._controlObjectType = componentType;

    state.screenDefinitions[screenId].metadata.uiComponentProperties[fieldId] = fieldProperties as any;

    state.screenDefinitions[screenId].metadata.defaultUiComponentProperties[fieldId] = {
        ...fieldProperties,
    } as any;

    // Tables need data, pageInfo
    if (
        state.screenDefinitions[screenId].values[fieldId] &&
        state.screenDefinitions[screenId].values[fieldId].hasOwnProperty('data') &&
        state.screenDefinitions[screenId].values[fieldId].hasOwnProperty('pageInfo')
    ) {
        state.screenDefinitions[screenId].values[fieldId] = {
            ...state.screenDefinitions[screenId].values[fieldId],
            data: fieldValue as FieldInternalValue<T>,
        };
    } else {
        state.screenDefinitions[screenId].values[fieldId] = fieldValue as FieldInternalValue<T>;
    }

    if (screenBase) {
        screenBase._pageMetadata.fieldBindings[fieldId] = (fieldProperties as ReadonlyFieldProperties).bind || fieldId;
        screenBase._pageMetadata.uiComponentProperties = screenBase._pageMetadata.uiComponentProperties || {};
        screenBase._pageMetadata.controlObjects = screenBase._pageMetadata.controlObjects || {};
        screenBase._pageMetadata.controlObjects[fieldId] =
            state.screenDefinitions[screenId].metadata.controlObjects[fieldId];
        screenBase._pageMetadata.uiComponentProperties[fieldId] = fieldProperties as any;
    }
    return { $bind: fieldId };
};

export const addBusinessActionToState = (
    state: XtremAppState,
    screenId: string,
    id: string,
    actionProperties: PageActionDecoratorProperties<any> = {},
) => {
    const getProperties = jest.fn().mockReturnValue(actionProperties);
    const setProperties = jest.fn();

    const businessAction = new controlObjects.PageActionControlObject({
        screenId,
        elementId: id,
        getUiComponentProperties: getProperties,
        setUiComponentProperties: setProperties,
    });

    const pageDefinition = getPageDefinitionFromState(screenId, state);
    pageDefinition.page = pageDefinition.page || ({} as Page);

    (pageDefinition.page.$ as any) = {
        ...pageDefinition.page.$,
        businessActions: pageDefinition.page.$.businessActions || [],
    } as PageDeveloperApi<any, ScreenBase>;
    pageDefinition.page.$.businessActions.push(businessAction);

    (pageDefinition.metadata as any).pageActions = pageDefinition.metadata.pageActions || {};
    pageDefinition.metadata.pageActions[id] = businessAction;
    pageDefinition.metadata.uiComponentProperties[id] = actionProperties;

    return businessAction;
};

export const addPageActionToState = (
    state: XtremAppState,
    screenId: string,
    id: string,
    actionProperties: PageActionDecoratorProperties<any> = {},
): controlObjects.PageActionControlObject => {
    const getProperties = jest.fn().mockReturnValue(actionProperties);
    const setProperties = jest.fn();

    const pageAction = new controlObjects.PageActionControlObject({
        screenId,
        elementId: id,
        getUiComponentProperties: getProperties,
        setUiComponentProperties: setProperties,
    });

    const pageDefinition = getPageDefinitionFromState(screenId, state);
    pageDefinition.page = pageDefinition.page || ({} as Page);

    (pageDefinition.metadata as any).pageActions = pageDefinition.metadata.pageActions || {};
    pageDefinition.metadata.pageActions[id] = pageAction;
    pageDefinition.metadata.uiComponentProperties[id] = actionProperties;

    return pageAction;
};

export const createFieldControlObject = <T extends FieldKey, CT extends ScreenBase = any, C extends ClientNode = any>(
    componentType: T,
    screenId: string,
    fieldConstructor: new (props: FieldControlObjectConstructorProps<T>) => ControlObjectInstance<T>,
    fieldId: string,
    fieldValue: FieldInternalValue<T> | null = 'Test value' as FieldInternalValue<T>,
    fieldProperties: FieldDecoratorProps<T, CT, C>,
    builderProps?: Partial<FieldControlObjectConstructorProps<T>>,
): ControlObjectInstance<T> => {
    let properties = {
        ...fieldProperties,
        _controlObjectType: componentType,
    } as unknown as FieldComponentProps<T>;
    let value = fieldValue as FieldInternalValue<T>;

    const getValue =
        builderProps?.getValue ||
        jest.fn<ReturnType<Required<FieldControlObjectConstructorProps<T>>['getValue']>, any>(() => value);
    const setValue =
        builderProps?.setValue ||
        jest.fn<void, any>((_screenId: string, _elementId: string, _value: any) => {
            value = _value;
        });
    const getProperties =
        builderProps?.getUiComponentProperties ||
        jest.fn<ReturnType<Required<FieldControlObjectConstructorProps<T>>['getUiComponentProperties']>, any>(
            (..._args) => properties as unknown as FieldComponentProps<T>,
        );

    const setProperties =
        builderProps?.setUiComponentProperties ||
        jest.fn<void, any>((_screenId: string, _elementId: string, _value: any) => {
            properties = { ..._value };
        });
    const dispatchValidation =
        builderProps?.dispatchValidation || jest.fn<Promise<ValidationResult[] | undefined>, any>();
    const refresh = builderProps?.refresh || jest.fn().mockReturnValue(Promise.resolve('wasabi'));
    const isFieldDirty = builderProps?.isFieldDirty || jest.fn().mockReturnValue(false);
    const setFieldDirty = builderProps?.setFieldDirty || jest.fn().mockReturnValue(false);
    const setFieldClean = builderProps?.setFieldClean || jest.fn().mockReturnValue(false);

    return new fieldConstructor({
        screenId,
        elementId: fieldId,
        componentKey: componentType,
        getValue,
        setValue,
        getUiComponentProperties: getProperties,
        setUiComponentProperties: setProperties,
        dispatchValidation,
        isFieldInFocus: jest.fn(),
        isFieldDirty,
        setFieldDirty,
        setFieldClean,
        refresh,
        layout: {} as any,
        focus: jest.fn(),
        parent: (fieldProperties as any).parent,
    });
};

export const createNoValueControlObject = <T extends ComponentKey>(
    componentKey: T,
    screenId: string,
    fieldConstructor: new (props: ControlObjectConstructorProps<T>) => ControlObjectInstance<T>,
    fieldId: string,
    fieldProperties: ControlObjectProps<T>,
    additionalConstructorProps = {},
) => {
    let properties: any = {
        ...fieldProperties,
    };
    const getProperties = jest.fn(() => {
        return properties;
    });
    const setProperties = jest.fn((_screenId: string, elementId: string, value: any) => {
        properties = { ...value };
    });
    const getError = jest.fn();

    return new fieldConstructor({
        screenId: screenId,
        elementId: fieldId,
        getUiComponentProperties: getProperties,
        setUiComponentProperties: setProperties,
        componentKey,
        dispatchValidation: getError,
        ...additionalConstructorProps,
    } as any);
};

export const getMockState = (partialState: Partial<XtremAppState> = {}): XtremAppState => ({
    plugins: {
        '@sage/myPluginPackage': {
            name: 'myFakePlugin',
            component: (props: XtremUiPluginComponentProps) => (
                <span id="fake-plugin">
                    <span id="fake-plugin-value">{props.value}</span>
                </span>
            ),
            transformFromGraphValue: jest.fn(() => 'myMockedValue'),
        },
    },
    activeLookupDialog: null,
    customizationWizardPage: null,
    exportConfigurationPage: null,
    clientUserSettingsEditPage: null,
    clientUserSettingsListPage: null,
    activeDialogs: {},
    translations: {},
    serviceOptions: {},
    printingSettings: null,
    enumTypes: {},
    nodeTypes: {},
    dataTypes: {},
    dashboard: {
        widgetCategories: {},
        nodeNames: {},
        canEditDashboards: true,
        dashboardGroups: {
            home: {
                dashboards: {},
                widgets: {},
                widgetEditor: {
                    isOpen: false,
                    isDirty: false,

                    widgetDefinition: {},
                },
                dashboardEditor: {
                    isOpen: false,
                    isDirty: false,
                    history: [],
                    currentHistoryIndex: 0,
                    currentDashboardDefinition: {
                        _id: '-1',
                        children: [],
                        title: '',
                    },
                },
                availableDashboards: [],
            },
        },
    },
    focusPosition: null,
    applicationContext: {
        updateMenu: jest.fn(),
        handleNavigation: jest.fn(),
        onPageTitleChange: jest.fn(),
        locale: 'en-US',
    },
    browser: {
        greaterThan: {
            l: false,
            m: false,
            s: true,
            xs: true,
        },
        is: {
            l: false,
            m: true,
            s: false,
            xs: false,
        },
        lessThan: {
            l: true,
            m: false,
            s: false,
            xs: false,
        },
        mediaType: '',
        orientation: 'landscape',
    },
    loading: { globalLoading: false, pages: {}, loadingDashboards: false, widgets: {} },
    menuItems: [],
    toasts: [],
    path: '/test/path',
    screenDefinitions: {},
    workflowNodes: [],
    isKeyboardShortcutsEnabled: false,
    applicationPackages: {},
    navigation: { history: [], isBackNavigation: false },
    websocket: {
        subscriptions: {
            activeSubscriptions: new Map(),
        },
    },
    ...partialState,
});

export const getMockStore = (
    storeState: XtremAppState = getMockState(),
    shouldMockReduxActions = true,
): MockStoreEnhanced<XtremAppState> => {
    mockStore = null;
    mockActions = shouldMockReduxActions;
    if (shouldMockReduxActions) {
        jest.spyOn(events, 'triggerHandledEvent').mockImplementation(jest.fn());
        jest.spyOn(events, 'triggerFieldEvent').mockImplementation((_, __, eventName, ...___) => {
            xtremConsole.log('triggerFieldEvent', eventName);
            return Promise.resolve();
        });
        jest.spyOn(events, 'triggerScreenEvent').mockImplementation(jest.fn());
        jest.spyOn(events, 'triggerNestedFieldEvent').mockImplementation(jest.fn());
    }

    const mockStoreCreator = configureMockStore<XtremAppState>([reduxThunk]);
    mockStore = mockStoreCreator(() => storeState);
    return mockStore;
};

export const restoreActionHandlerMocks = () => {
    if (jest.isMockFunction(events.triggerHandledEvent)) {
        events.triggerHandledEvent.mockRestore();
    }
    if (jest.isMockFunction(events.triggerFieldEvent)) {
        events.triggerFieldEvent.mockRestore();
    }
    if (jest.isMockFunction(events.triggerScreenEvent)) {
        events.triggerScreenEvent.mockRestore();
    }
    if (jest.isMockFunction(events.triggerNestedFieldEvent)) {
        events.triggerNestedFieldEvent.mockRestore();
    }
};

export const getStore = (initialState?: XtremAppState) => {
    const middleWares: Middleware[] = [reduxThunk, actionSubscriptionMiddleware];
    return createStore<XtremAppState, AppAction, {}, {}>(
        rootReducer,
        initialState as any,
        applyMiddleware(...middleWares),
    );
};
