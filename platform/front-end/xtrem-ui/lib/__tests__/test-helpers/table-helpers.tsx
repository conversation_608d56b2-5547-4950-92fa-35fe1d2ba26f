import { waitFor } from '@testing-library/react';

import { RenderResult } from '@testing-library/react';
import { COLUMN_ID_ROW_SELECTION } from '../../utils/ag-grid/ag-grid-column-config';
interface RenderWithReduxUtils<TStore = any> extends RenderResult {
    store: TStore;
}

export class AgGridHelpers {
    utils: RenderWithReduxUtils;

    constructor(utils: RenderWithReduxUtils) {
        this.utils = utils;
    }

    getRow = async (rowIndex: number): Promise<Element | null> => {
        const selector = `.ag-row[row-index="${rowIndex}"]`;
        await waitFor(() => {
            expect(document.querySelector(selector)).toBeInTheDocument();
        });
        return this.utils.container.querySelector(selector);
    };

    getCellContent = (rowIndex: number, columnIndex: number): string | null | undefined =>
        this.getCell(rowIndex, columnIndex)?.textContent;

    getCell = (rowIndex: number, columnIndex: number): Element | null => {
        return this.utils.container.querySelector(
            `.ag-row[row-index="${rowIndex}"] > .ag-cell-value[aria-colindex="${columnIndex + 1}"]`,
        );
    };

    getPhantomRow = async (): Promise<Element | null> => {
        const selector = '.ag-floating-top-container .ag-row-pinned';
        await waitFor(() => {
            expect(document.querySelector(selector)).toBeInTheDocument();
        });
        return this.utils.container.querySelector(selector);
    };

    getAllHeaders = () => Array.from(this.utils.container.querySelectorAll<HTMLSpanElement>('.ag-header-cell-text'));
    getHeader = (columnIndex: number) => {
        return this.utils.container.querySelector(
            `.ag-header-cell[aria-colindex="${columnIndex + 1}"] .ag-header-cell-text`,
        );
    };
    getAllHeadersTextContent = () => this.getAllHeaders().map((h: HTMLSpanElement) => h.textContent);

    getColumnByIndex = (columnIndex: number) =>
        Array.from(
            this.utils.container.querySelectorAll<HTMLDivElement>(
                `.ag-cell-value[aria-colindex="${columnIndex + 1}"][role="gridcell"]`,
            ),
        ).map((c: HTMLDivElement) => c.textContent);
    getColumnById = (columnId: number) =>
        Array.from(
            this.utils.container.querySelectorAll<HTMLDivElement>(
                `.ag-cell-value[col-id="${columnId}"][role="gridcell"]`,
            ),
        ).map((c: HTMLDivElement) => c.textContent);

    getRowSelectionCheckbox = async (rowIndex: number): Promise<Element | null> => {
        const selector = `.ag-row[row-index="${rowIndex}"] [col-id="${COLUMN_ID_ROW_SELECTION}"] .ag-checkbox input`;
        await waitFor(() => {
            expect(document.querySelector(selector)).toBeInTheDocument();
        });
        return this.utils.container.querySelector(selector);
    };
}
