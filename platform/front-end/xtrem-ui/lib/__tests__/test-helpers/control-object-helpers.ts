import { objectKeys } from '@sage/xtrem-shared';
import {
    ControlObjectInstance,
    FieldControlObjectConstructorProps,
    FieldDecoratorProps,
    FieldInternalValue,
    FieldKey,
} from '../../component/types';
import { get, set } from 'lodash';

export interface ControlObjectProps<T extends FieldKey> {
    screenId?: string;
    elementId?: string;
    fieldProperties?: FieldDecoratorProps<T>;
    fieldValue?: FieldInternalValue<T>;
    focus?: (screenId: string, elementId: string, row?: number, nestedField?: string) => void;
}

export const buildControlObject = <T extends FieldKey>(
    fieldConstructor: new (props: FieldControlObjectConstructorProps<T>) => ControlObjectInstance<T>,
    options?: ControlObjectProps<T>,
): ControlObjectInstance<T> => {
    const screenId = options?.screenId || 'TestPage';
    const elementId = options?.elementId || 'fieldName';
    const fieldProperties = options?.fieldProperties || ({} as FieldDecoratorProps<T>);
    let fieldValue = options?.fieldValue;
    const focus = options?.focus || jest.fn();
    const getValue = jest.fn(() => fieldValue);
    const setValue = jest.fn((_screenId: string, _elementId: string, value: FieldInternalValue<T>) => {
        fieldValue = value;
    });
    const getProperties = jest.fn(() => fieldProperties);
    const setProperties = jest.fn((_screenId: string, _elementId: string, newProperties: FieldDecoratorProps<T>) => {
        objectKeys(newProperties).forEach(key => {
            set(fieldProperties, key, get(newProperties, key));
        });
    });
    const dispatchValidation = jest.fn();
    const refresh = jest.fn();
    const isFieldDirty = jest.fn();

    return new fieldConstructor({
        screenId,
        elementId,
        getValue,
        setValue,
        getUiComponentProperties: getProperties,
        setUiComponentProperties: setProperties,
        dispatchValidation,
        refresh,
        isFieldDirty,
        layout: {} as any,
        focus,
    } as unknown as FieldControlObjectConstructorProps<T>);
};
