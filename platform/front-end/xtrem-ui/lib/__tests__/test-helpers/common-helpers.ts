import { Clickable } from '../../component/field/traits';
import { Page } from '../../service/page';
import { PageDefinition } from '../../service/page-definition';
import * as pageMetadataImport from '../../service/page-metadata';
import { ScreenBaseDefinition } from '../../service/screen-base-definition';
import { Sticker } from '../../service/sticker';
import { StickerDefinition } from '../../service/sticker-definition';

export const getMockPageDefinition = (
    screenId?: string,
    partialPageDefinition: Partial<PageDefinition> = {},
    partialPageMetadata: Partial<pageMetadataImport.PageMetadata> = {},
): PageDefinition => {
    const metadata = getMockPageMetadata(screenId, partialPageMetadata);
    return {
        insightCount: 0,
        accessBindings: {},
        activeSection: null,
        dirtyStates: {},
        errors: {},
        path: `@sage/xtrem-test/${screenId || 'AnyPage'}`,
        internalErrors: {},
        isInDialog: false,
        isMainPage: true,
        isReady: true,
        metadata,
        page: {
            $: {},
            _pageMetadata: metadata,
        } as Page,
        queryParameters: {},
        selectedRecordId: null,
        userSettings: {},
        type: 'page',
        values: {},
        ...partialPageDefinition,
    };
};

export const testOnClickHandler = <T extends Clickable<Page>>(
    decorator: Function,
    pageMetadata: pageMetadataImport.PageMetadata,
    fieldId: string,
) => {
    const onClickMock: jest.Mock = jest.fn();
    decorator({
        onClick: onClickMock,
    })({} as Page, fieldId);
    pageMetadata.fieldThunks[fieldId]({}, {});
    const mappedComponentEvents = pageMetadata.uiComponentProperties[fieldId] as unknown as T;
    expect(mappedComponentEvents.onClick).toBe(onClickMock);
    expect(onClickMock).not.toHaveBeenCalled();
    mappedComponentEvents.onClick!.bind({})();
    expect(onClickMock).toHaveBeenCalledTimes(1);
};

export class TestClass {}

export const getMockPageMetadata = (
    screenId = 'TestPage',
    partialPageMetadata: Partial<pageMetadataImport.PageMetadata> = {},
): pageMetadataImport.PageMetadata => ({
    pageActions: {},
    layout: { $items: [] },
    controlObjects: {},
    uiComponentProperties: {
        [screenId]: {
            title: 'test',
        },
    },
    pageFragmentThunks: [],
    fragmentFields: {},
    fragmentFieldsThunks: {},
    defaultUiComponentProperties: {},
    extensionOverrideThunks: {},
    businessActionsExtensionsThunk: [],
    pageActionThunks: {},
    blockThunks: {},
    fieldThunks: {},
    sectionThunks: {},
    layoutBlocks: {},
    fieldBindings: {},
    pageExtensionThunks: [],
    layoutFields: {},
    target: new TestClass() as any,
    screenId,
    elementsWithShortcut: [],
    definitionOrder: [],
    customizations: {},
    isTransient: false,
    ...partialPageMetadata,
});

export const getMockScreenDefinition = (
    screenId: string,
    type: 'page' | 'sticker',
    partialPageMetadata: Partial<pageMetadataImport.PageMetadata> = {},
): ScreenBaseDefinition => {
    const metadata = getMockPageMetadata(screenId, partialPageMetadata);
    return {
        accessBindings: {},
        errors: {},
        internalErrors: {},
        dirtyStates: {},
        metadata,
        userSettings: {},
        type,
        values: {},
    };
};

export const getMockStickerDefinition = (
    screenId?: string,
    partialStickerDefinition: Partial<StickerDefinition> = {},
): StickerDefinition => ({
    accessBindings: {},
    errors: {},
    internalErrors: {},
    dirtyStates: {},
    metadata: getMockPageMetadata(screenId),
    sticker: {
        $: {},
    } as Sticker,
    userSettings: {},
    type: 'sticker',
    values: {},
    ...partialStickerDefinition,
});

export const dispatchKeyEvent = (key: any, type: 'keydown' | 'keyup') => {
    const ev: KeyboardEvent = new KeyboardEvent(type, { bubbles: true, cancelable: true, key, code: key });
    ev.initEvent(type, true, true);
    document.dispatchEvent(ev);
};

/**
 * Getting the deepest element that contain string / match regex even when it split between multiple elements
 *
 * @example
 * For:
 * <div>
 *   <span>Hello</span><span> World</span>
 * </div>
 *
 * screen.getByText('Hello World') // ❌ Fail
 * screen.getByText(textContentMatcher('Hello World')) // ✅ pass
 */
export const textContentMatcher = (textMatch: string | RegExp) => {
    const hasText =
        typeof textMatch === 'string'
            ? (node: Element) => node.textContent === textMatch
            : (node: Element) => textMatch.test(node.textContent as string);

    return (_content: string, node: Element) => {
        if (!hasText(node)) {
            return false;
        }

        const childrenDontHaveText = Array.from(node?.children || []).every(child => !hasText(child));

        return childrenDontHaveText;
    };
};
