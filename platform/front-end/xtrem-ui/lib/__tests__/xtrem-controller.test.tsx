import * as React from 'react';
import { Provider } from 'react-redux';
import type { Page } from '../service/page';
import {
    addPageControlObject,
    addStickerControlObject,
    getMockPageDefinition,
    getMockState,
    getMockStickerDefinition,
    getMockStore,
} from './test-helpers';
import type { PageDefinition } from '../service/page-definition';
import type { XtremAppState } from '../redux';
import { ConnectedXtremController } from '../xtrem-controller';
import { render } from '@testing-library/react';

describe('Xtrem controller component', () => {
    const pageId = 'TestPage';
    const stickerId = 'testSticker';

    const page = {
        $: {
            headerLineBlock: { $layout: { $items: [] } },
            footerBlock: { $layout: { $items: [] } },
            businessActions: [],
        },
    } as unknown as Partial<Page>;

    const addControlObjects = (mockState: XtremAppState): void => {
        addPageControlObject(mockState, pageId, {
            node: 'test-controller-page',
            title: 'test controller',
        });

        addStickerControlObject(mockState, stickerId, {
            icon: 'home',
        });
    };

    const renderAndMatchSnapshot = (mockState: XtremAppState): void => {
        const mockStore = getMockStore(mockState);

        const { container } = render(
            <Provider store={mockStore}>
                <ConnectedXtremController />
            </Provider>,
        );
        expect(container).toMatchSnapshot();
    };

    it('should render Xtrem controller with common properties', () => {
        const mockState = getMockState();
        mockState.screenDefinitions[pageId] = getMockPageDefinition(pageId, {
            page,
        } as Partial<PageDefinition>);
        mockState.screenDefinitions[stickerId] = getMockStickerDefinition(stickerId);

        addControlObjects(mockState);
        renderAndMatchSnapshot(mockState);
    });

    it('should render Xtrem controller without PageComponent in the body when isReady is false', () => {
        const mockState = getMockState();
        mockState.screenDefinitions[pageId] = getMockPageDefinition(pageId, {
            page,
            isReady: false,
        } as Partial<PageDefinition>);
        mockState.screenDefinitions[stickerId] = getMockStickerDefinition(stickerId);

        addControlObjects(mockState);
        renderAndMatchSnapshot(mockState);
    });

    it('should render Xtrem controller without PageComponent in the body when there is no page definition', () => {
        const mockState = getMockState();

        renderAndMatchSnapshot(mockState);
    });
});
