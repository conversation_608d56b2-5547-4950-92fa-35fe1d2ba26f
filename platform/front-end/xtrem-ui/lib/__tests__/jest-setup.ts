import { resetIdCounter } from 'downshift';
import '@testing-library/jest-dom';
import 'jest-styled-components';
import '../module-types.d';
import { setLocalizeImplementation } from '@sage/xtrem-date-time';
import { TextEncoder, TextDecoder } from 'util';

global.IS_REACT_ACT_ENVIRONMENT = true;

jest.mock('@ckeditor/ckeditor5-react');
jest.mock('@ckeditor/ckeditor5-editor-decoupled');
jest.mock('@ckeditor/ckeditor5-essentials');
jest.mock('@ckeditor/ckeditor5-basic-styles');
jest.mock('@ckeditor/ckeditor5-alignment');
jest.mock('@ckeditor/ckeditor5-font');
jest.mock('@ckeditor/ckeditor5-paragraph');
jest.mock('@ckeditor/ckeditor5-list');
jest.mock('@ckeditor/ckeditor5-table');
jest.mock('@sage/bms-dashboard', () => ({}));
jest.retryTimes(3, { waitBeforeRetry: 1000 });

(window as any).TextEncoder = TextEncoder;
(window as any).TextDecoder = TextDecoder;

(window as any).DEV_MODE = true;
(window as any).FEATURE_USER_CLIENT_CUSTOMIZATION = false;
window.HTMLElement.prototype.scroll = jest.fn();

process.on('unhandledRejection', (error: any) => {
    /* eslint-disable no-console, consistent-return */
    console.log(error.stack);
});

jest.mock('uid', () => (): string => 'uniqguidmock');

jest.mock('carbon-react/esm/__internal__/utils/helpers/guid/index', () => (): string => {
    return 'testcarb-onco-mpon-ents-uniqguidmock';
});

jest.mock('carbon-react/esm/hooks/useMediaQuery', () => ({ __esModule: true, default: (): boolean => false }));
setLocalizeImplementation(jest.fn((_, v) => v));

Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(), // Deprecated
        removeListener: jest.fn(), // Deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
    })),
});

class ResizeObserver {
    observe(): void {
        jest.fn();
    }

    unobserve(): void {
        jest.fn();
    }

    disconnect(): void {
        jest.fn();
    }
}

(window as any).ResizeObserver = ResizeObserver;

const hideContainsWarningOrErrorMessages = [
    'useLayoutEffect',
    'has been renamed',
    'Failed prop type',
    '[Deprecation] `styleOverride`',
    'does not correspond to defined gridOptions.columnTypes',
    'State after dispatch',
    'State before dispatch',
    'after dispatch',
    'before dispatch',
    "Cannot read property '__resizeListenerCallbacks__' of null",
    'You forgot to call the getMenuProps getter function on your component / element',
    'The ref prop "undefined" from getDropdownProps',
    'Supplying an `onEscape` prop to `DraftEditor` has been deprecated',
    '****************************************************************************************************************',
    '* If you want to hide the watermark, <NAME_EMAIL> for a trial license.                        *',
    '* This is an evaluation only version, it is not licensed for development projects intended for production.     *',
    '* All AG Grid Enterprise features are unlocked.                                                                *',
    '****************************************** License Key Not Found ***********************************************',
    '***************************************** AG Grid Enterprise License *******************************************',
    '* If you want to hide the watermark, <NAME_EMAIL> for a trial license.                        *',
    'No AG-Grid licence was loaded.',
    "ag-grid: invalid colDef property 'xtremSortIndex' did you mean any of these: sortIndex, initialSortIndex, sortingOrder, initialPivotIndex, pivotIndex, initialRowGroupIndex, initialSort, rowGroupIndex",
    'Warning: An update to %s inside a test was not wrapped in act(...)',
    'Unknown node type:',
    'Unknown property:',
];
const originalWarn = console.warn.bind(console.warn);
const originalError = console.error.bind(console.error);
const originalInfo = console.info.bind(console.info);

// This hook cleans the console from unwanted errors, normally coming from React.
beforeAll(() => {
    console.warn = (msg: string): void => {
        if (
            !process.env.NO_CONSOLE &&
            msg &&
            !hideContainsWarningOrErrorMessages.some(toHideMsg => msg.toString().includes(toHideMsg))
        ) {
            return originalWarn(msg);
        }
    };
    console.error = (msg: string): void => {
        if (
            !process.env.NO_CONSOLE &&
            msg &&
            !hideContainsWarningOrErrorMessages.some(toHideMsg => msg.toString().includes(toHideMsg))
        ) {
            return originalError(msg);
        }
    };
    console.info = (msg: string): void => {
        if (
            !process.env.NO_CONSOLE &&
            msg &&
            !hideContainsWarningOrErrorMessages.some(toHideMsg => msg.toString().includes(toHideMsg))
        ) {
            return originalInfo(msg);
        }
    };

    // This circunvents a bug in the internal carbon-react/esm/components/select/select-list
    if (!HTMLElement.prototype.scrollTo) {
        HTMLElement.prototype.scrollTo = jest.fn;
    }
});

beforeEach(() => {
    window.structuredClone = (val: any): any => JSON.parse(JSON.stringify(val));
    // eslint-disable-next-line global-require
    const FDBFactory = require('fake-indexeddb/lib/FDBFactory');
    (window.indexedDB as any) = new FDBFactory();
    (window as any).setImmediate = window.setTimeout;
    (window as any).clearImmediate = window.clearTimeout;
    global.gc?.();
});

afterEach(() => {
    resetIdCounter();
});

afterAll(() => {
    console.warn = originalWarn;
    console.error = originalError;
    console.info = originalInfo;
});
