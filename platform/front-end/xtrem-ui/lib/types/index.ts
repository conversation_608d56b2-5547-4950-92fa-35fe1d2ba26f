import type { Page, PageFragment } from '../service/page';
import type { PageMetadata } from '../service/page-metadata';
import type { ScreenBase } from '../service/screen-base';
import type { Sticker } from '../service/sticker';
import type { Simplify } from '../utils/types';

export enum ContextType {
    accordion = 'accordion',
    calendar = 'calendar',
    detailPanel = 'detail-panel',
    dialog = 'dialog',
    header = 'header',
    navigationPanel = 'navigation-panel',
    page = 'page',
    pageHeader = 'page-header',
    pod = 'pod',
    sidebar = 'sidebar',
    table = 'table',
    tableSummary = 'tableSummary',
}

export enum GraphQLTypes {
    Boolean = 'Boolean',
    Date = 'Date',
    DateTime = 'datetime',
    DatetimeRange = 'datetimeRange',
    Decimal = 'Decimal',
    Enum = 'Enum',
    ExternalReference = 'ExternalReference', // Non-vital references for ADC, same as IntReference
    Float = 'Float',
    ID = 'Id', // Yes, it is lowercase 'd'
    InputStream = '_InputStream',
    OutputTextStream = '_OutputTextStream',
    InputTextStream = '_InputTextStream',
    Int = 'Int',
    IntOrString = 'IntOrString',
    IntReference = 'IntReference', // Non-vital references
    Json = 'Json',
    String = 'String',
}

export const textStreams: ReadonlyArray<GraphQLTypes> = [GraphQLTypes.OutputTextStream, GraphQLTypes.InputTextStream];

export enum GraphQLKind {
    Object = 'OBJECT',
    Scalar = 'SCALAR',
    List = 'LIST',
}

export type Constructible<T> = { _pageMetadata?: PageMetadata; new (): T };

export type ScreenBaseGenericType<T> = T extends ScreenBase<infer U> ? U : any;
export type ScreenBaseApiNodeType<T> = T extends ScreenBase<infer U> ? keyof U : Object;

export type ScreenExtension<CT> = ScreenBase<ScreenBaseGenericType<CT>>;
export type PageExtension<CT> = Page<ScreenBaseGenericType<CT>>;
export type PageFragmentExtension<CT> = PageFragment<ScreenBaseGenericType<CT>>;
export type StickerExtension<CT> = Sticker<ScreenBaseGenericType<CT>>;

export type NestedRecordId = string;

export type PageWithAccessToNodes<
    NodeUnion extends (keyof G & `${'@sage/xtrem-'}${string}`)[],
    P extends Page = Page,
    G = P extends Page<infer GraphqlApi> ? GraphqlApi : any,
> = Simplify<
    Omit<P, '$'> & {
        $: Simplify<
            Omit<P['$'], 'graph'> & {
                graph: Simplify<
                    Omit<P['$']['graph'], 'node'> & {
                        node<K extends keyof Pick<G, NodeUnion[number]>>(nodeName: K): G[K];
                    }
                >;
            }
        >;
    }
>;

export type NodePropertyType = string | number | symbol;
export type OptionalNodePropertyType = NodePropertyType | undefined;

export type GroupAggregationMethods = 'min' | 'max' | 'sum' | 'avg' | 'distinctCount';
