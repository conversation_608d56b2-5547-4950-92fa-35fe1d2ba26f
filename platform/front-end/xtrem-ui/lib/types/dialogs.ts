import type { AsyncOperation, ClientNode, OnlySelected, Selector } from '@sage/xtrem-client';
import type { Dict, GridNestedFieldTypes } from '@sage/xtrem-shared';
import type * as React from 'react';
import type { NodePropertyType, ScreenExtension } from '.';
import type { CustomContentSupportedControlObjects } from '../component/container/dialog/body/custom-content';
import type {
    BlockControlObject,
    FormDesignerControlObject,
    PageControlObject,
    SectionControlObject,
    TableControlObject,
    WorkflowControlObject,
} from '../component/control-objects';
import type { ReferenceDecoratorProperties } from '../component/decorator-properties';
import type { HasFilter, HasServerRecordMapperFunction } from '../component/field/traits';
import type { NestedField } from '../component/nested-fields';
import type { SidebarDefinitionDecorator } from '../component/table-sidebar/table-sidebar-types';
import type { OrderByType } from '../component/types';
import type { CardDefinition } from '../component/ui/card/card-component';
import type { CollectionValue } from '../service/collection-data-service';
import type { DialogControl } from '../service/dialog-service';
import type { ScreenBase } from '../service/screen-base';
import type { ScreenBaseDefinition } from '../service/screen-base-definition';
import type { QueryParameters } from '../utils/types';

export type DialogLevel = 'info' | 'success' | 'error' | 'warn';

export type DialogContentType =
    | string
    | Array<CustomContentSupportedControlObjects>
    | SectionControlObject
    | Error
    | PageControlObject
    | LookupDialogContent
    | React.ReactElement
    | TableSidebarDialogContent
    | AsyncLoaderDialogContent
    | null;

export type DialogSize = 'extra-small' | 'small' | 'medium-small' | 'medium' | 'medium-large' | 'large' | 'extra-large';

export interface DialogBodyContentProps<T extends BaseDialogOptions = BaseDialogOptions> {
    availableColumns: number;
    defaultFocusRef?: React.RefObject<any>;
    dialog: DialogDescription<T>;
    dialogBodyHeight?: number;
    isFirstSection: boolean;
    isLastSection: boolean;
    noHeader?: boolean;
    onStepOneSection: (direction: 1 | -1) => void;
    screenDefinition?: ScreenBaseDefinition;
    selectedSection?: string | null;
}

export interface LookupDialogContent<ReferencedItemType extends ClientNode = any> {
    isMultiSelect?: boolean;
    /* Lookup context node type */
    contextNode?: NodePropertyType;
    fieldId: string;
    fieldProperties: Pick<
        ReferenceDecoratorProperties<ScreenBase, ReferencedItemType>,
        | 'additionalLookupRecords'
        | 'bind'
        | 'filter'
        | 'isTransient'
        | 'lookupDialogTitle'
        | 'mapServerRecord'
        | 'node'
        | 'orderBy'
        | 'title'
        | 'tunnelPage'
        | 'valueField'
    > & { columns?: NestedField<any, any>[]; selectedRecords?: string[] };
    /* There is a tunnel page to create a new item */
    isLinkCreateNewText?: boolean;
    /* The id of the parent field. If set lookup queries will be performed at the parent level. */
    parentElementId?: string;
    /* Lookup context, it is sent to the "lookups" Graphql endpoint if applicable" */
    recordContext?: Dict<any>;
    /* Sets default value for the search input in mobile mode */
    searchText?: string;
    /* Only for desktop purposes now. Send the id of the already selected record to be highlighted in the table when opening the lookup dialog. */
    selectedRecordId?: string | string[];
    valueField?: any;
    onLookupDialogClose?: (action: 'select' | 'cancel' | 'close') => Promise<void>;
    level?: number;
    /* Collection value that the suggestions are loaded from, if not provided automatically populated based on the element and screen IDs */
    value?: ReferencedItemType | CollectionValue<ReferencedItemType>;
    createTunnelLinkText?: string;
    onCreateNewItemLinkClick?: () => void;
    isEditable?: boolean;
    dialogTitle?: string;
}

export interface AsyncLoaderDialogContent {
    onNotifyMe?: () => void;
    onStop?: () => void;
    isStopAvailable: boolean;
    dialogTitle: string;
    dialogContent: string;
}

export interface DialogButtonOptions {
    className?: string;
    isDestructive?: boolean;
    isDisabled?: boolean;
    isHidden?: boolean;
    isNegative?: boolean;
    text?: string;
}

export interface BaseDialogOptions {
    rightAligned?: boolean;
    fullScreen?: boolean;
    size?: DialogSize;
    resolveOnCancel?: boolean;
    reverseButtons?: boolean;
    mdContent?: boolean;
    isDirtyCheck?: boolean;
    height?: number;
    hasGreyBackground?: boolean;
}

export interface PageDialogOptions extends BaseDialogOptions {
    /** When this option is set, no warning is displayed if the user closes the dialog with unsaved content. */
    skipDirtyCheck?: boolean;
    /** Initial value of the dialog. When the value is set, the field values of the page dialog are automatically populated. */
    values?: Dict<any>;
    /** Set to true to duplicate the page */
    isDuplicate?: boolean;
    /** Optional title for the dialog */
    title?: string;
    /** Optional subtitle for the dialog */
    subtitle?: string;
    /** Whether the main list should be displayed in the dialog (given the loaded page has one) */
    isMainListDisplayedInDialog?: boolean;

    isPaddingRemoved?: boolean;
}

export interface CustomDialogOptions extends DialogOptions {
    dialogTitle?: string;
    isPaddingRemoved?: boolean;
}

export interface DialogOptions extends BaseDialogOptions {
    acceptButton?: DialogButtonOptions;
    cancelButton?: DialogButtonOptions;
}

export interface DialogButton {
    className?: string;
    isDestructive?: boolean;
    isDisabled?: boolean;
    isHidden?: boolean;
    isNegative?: boolean;
    onClick: (value?: any) => void;
    text: string;
}
export type DialogType =
    | 'page'
    | 'custom'
    | 'message'
    | 'confirmation'
    | 'error'
    | 'lookup'
    | 'unknown'
    | 'internal'
    | 'table-sidebar'
    | 'async-loader';

export interface DialogDescription<T extends BaseDialogOptions = BaseDialogOptions> {
    dialogId: number;
    isSticker: boolean;
    isDirtyCheck?: boolean;
    buttons: Dict<DialogButton>;
    content: DialogContentType;
    level: DialogLevel;
    options: T;
    screenId: string | null;
    title?: string;
    subtitle?: string;
    dialogControl: DialogControl;
    type?: DialogType;
}

export interface IDialogControl extends Promise<any> {
    cancel: () => void;
    finally(onFinally?: (() => void) | undefined | null): Promise<any>;
    then<TResult1 = any, TResult2 = any>(
        onFulfilled?: ((value: any) => TResult1 | PromiseLike<TResult1>) | undefined | null,
        onRejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null,
    ): Promise<TResult1 | TResult2>;
    catch<TResult = any>(
        onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null,
    ): Promise<any | TResult>;
}

export type CustomDialogContentType =
    | SectionControlObject
    | BlockControlObject
    | Array<SectionControlObject>
    | Array<BlockControlObject>
    | TableControlObject
    | WorkflowControlObject
    | FormDesignerControlObject;

export interface TableSidebarDialogContent {
    cardDefinition?: CardDefinition;
    columns: Array<NestedField<any, any>>;
    elementId: string;
    isNewRecord?: boolean;
    level?: number;
    nextRecordId?: string;
    prevRecordId?: string;
    recordId?: string;
    sidebarDefinition: SidebarDefinitionDecorator;
}

export interface LookupDialogOptions<CT extends ScreenBase<CT>, NodeType extends ClientNode = any>
    extends HasFilter<CT, NodeType>,
        HasServerRecordMapperFunction<ScreenExtension<CT>, NodeType>,
        DialogOptions {
    columns?: NestedField<CT extends ScreenExtension<CT> ? CT : never, GridNestedFieldTypes, NodeType>[];
    dialogTitle?: string;
    isEditable?: boolean;
    isMultiSelect?: boolean;
    node: string;
    orderBy?: OrderByType<NodeType>;
    selectedRecordId?: string | string[];
    tunnelPage?: string;
    id?: string;
}

export type AsyncOperationGetter<G, N extends keyof G> = G[N] extends {
    asyncOperations: Record<string, any>;
}
    ? {
          [K in keyof G[N]['asyncOperations']]: G[N]['asyncOperations'][K] extends AsyncOperation<infer A, any>
              ? A
              : never;
      }
    : never;

export type AsyncOperationReturn<G, N extends keyof G> = G[N] extends {
    asyncOperations: Record<string, any>;
}
    ? {
          [K in keyof G[N]['asyncOperations']]: G[N]['asyncOperations'][K] extends AsyncOperation<any, infer A>
              ? A
              : never;
      }
    : never;
export type GraphqlApiGetterType<T extends ScreenBase> = T extends ScreenBase<infer G> ? G : never;

export interface AsyncLoaderDialogOptions {
    dialogTitle?: string;
    dialogContent?: string;
    isStopAvailable?: boolean;
}

export interface IDialogApi<CT extends ScreenBase> {
    confirmation: (level: DialogLevel, title: string, message: string, options?: DialogOptions) => IDialogControl;
    message: (level: DialogLevel, title: string, message: string, options?: DialogOptions) => IDialogControl;
    custom: (level: DialogLevel, content: CustomDialogContentType, options?: CustomDialogOptions) => IDialogControl;
    page: (path: string, queryParameters?: QueryParameters, options?: PageDialogOptions) => Promise<any>;
    lookup: <NodeType extends ClientNode = any>(options: LookupDialogOptions<any, NodeType>) => Promise<NodeType[]>;
    asyncLoader: <
        N extends keyof GraphqlApiGetterType<CT>,
        O extends keyof AsyncOperationGetter<GraphqlApiGetterType<CT>, N>,
        SelectorT extends Selector<
            AsyncOperationReturn<GraphqlApiGetterType<CT>, N>[O] extends (infer U)[]
                ? U
                : AsyncOperationReturn<GraphqlApiGetterType<CT>, N>[O]
        >,
    >(
        nodeName: N,
        operation: O,
        parameters: AsyncOperationGetter<GraphqlApiGetterType<CT>, N>[O],
        selector: SelectorT,
        dialogOptions?: AsyncLoaderDialogOptions,
    ) => Promise<OnlySelected<AsyncOperationReturn<GraphqlApiGetterType<CT>, N>[O], SelectorT> | null>;
}
