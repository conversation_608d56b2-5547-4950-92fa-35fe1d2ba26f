const path = require('path');
const fs = require('fs');
const rootDir = path.resolve(__dirname, '..');

const agGridLicencePath = path.resolve(path.join(rootDir, 'xtrem-ui'), 'ag-grid-license.txt');
const agGridLicence = fs.existsSync(agGridLicencePath)
    ? Buffer.from(fs.readFileSync(agGridLicencePath, 'utf8')).toString('base64')
    : null;

const esmOnlyPackages = [
    '@ag-grid-community',
    '@ag-grid-enterprise',
    '@react-dnd',
    'carbon-react',
    'core-dnd',
    'dnd-core',
    'react-dnd-html5-backend',
    'react-dnd',
];

if (process.env.SKIP_CLIENT === '1') {
    console.warn('Skipping tests on SKIP_CLIENT env variable.');
    process.exit(0);
}

module.exports = {
    roots: ['<rootDir>/lib'],
    globals: {
        AG_LICENCE: agGridLicence,
    },
    moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node', 'd.ts'],
    transform: {
        '\\.svg$': '<rootDir>/jest-svg-transformer.js',
        '^.+\\.(ts|tsx|js|jsx|mjs)$': [
            'ts-jest',
            {
                tsconfig: 'tsconfig.debug.json',
            },
        ],
    },
    testMatch: ['<rootDir>/**/__tests__/**/*test.ts?(x)'],
    testEnvironment: 'jsdom',
    runtime: '@side/jest-runtime',
    verbose: false,
    setupFilesAfterEnv: ['<rootDir>/lib/__tests__/jest-setup.ts'],
    setupFiles: ['jest-canvas-mock', 'fake-indexeddb/auto'],
    reporters: ['default', 'jest-junit'],
    coverageProvider: 'v8',
    collectCoverageFrom: [
        '<rootDir>/lib/**/*.{ts,tsx}',
        '!<rootDir>/lib/component/container/container-properties.ts',
        '!<rootDir>/lib/component/nested-fields-properties.ts',
    ],
    coverageReporters: ['json', 'lcov', 'text', 'clover', 'cobertura'],
    testPathIgnorePatterns: [
        '<rootDir>/node_modules/',
        '<rootDir>/coverage/',
        '<rootDir>/webpack/',
        '<rootDir>/lib/component/types-test.ts',
    ],
    moduleDirectories: ['node_modules', '<rootDir>/../xtrem-ui-components'],
    coveragePathIgnorePatterns: [
        '.*__tests__.*',
        '<rootDir>/node_modules/',
        '<rootDir>/coverage/',
        '<rootDir>/webpack/',
        '<rootDir>/lib/consumer-mock',
    ],
    transformIgnorePatterns: [
        '<rootDir>/node_modules/',
        '<rootDir>/coverage/',
        '<rootDir>/junit/',
        '<rootDir>/webpack/',
        `node_modules/(?!.pnpm|${esmOnlyPackages.join('|')})`,
    ],
    moduleNameMapper: {
        '\\.(css|scss|svg)$': 'identity-obj-proxy',
        'carbon-react/esm/(.*)': '<rootDir>/node_modules/carbon-react/lib/$1',
        'file-type': '<rootDir>/node_modules/file-type/index.js',
    },
    snapshotFormat: {
        escapeString: true,
        printBasicPrototype: true,
    },
};
