const istanbulCoverage = require('istanbul-lib-coverage');
const istanbulReport = require('istanbul-lib-report');
const reports = require('istanbul-reports');
const fs = require('fs');
const path = require('path');

const coverageMap = istanbulCoverage.createCoverageMap({});
const coverageFilePath = path.resolve(__dirname, 'coverage/coverage-final.json');
if (fs.existsSync(coverageFilePath)) {
    const content = JSON.parse(fs.readFileSync(coverageFilePath));
    coverageMap.merge(content);
}

const package = istanbulReport.summarizers.pkg(coverageMap);
const context = istanbulReport.createContext({
    dir: path.resolve(__dirname, 'test-results'),
});

package.visit(reports.create('lcov'), context);
package.visit(reports.create('clover'), context);

const coverageSummary = coverageMap.getCoverageSummary();

fs.writeFileSync(
    path.resolve(__dirname, 'coverage', 'latest.json'),
    JSON.stringify({
        lines: coverageSummary.statements.pct,
    }),
);

const previousCoveragePath = path.resolve(__dirname, 'coverage', 'previous.json');
const previousCoverage = fs.existsSync(previousCoveragePath)
    ? require(previousCoveragePath)
    : {
          lines: coverageSummary.statements.pct,
      };

let githubCoveragePost = `

**Coverage Summary**
- Functions: ${coverageSummary.functions.pct}%
- Lines: ${coverageSummary.statements.pct}%
- Branches: ${coverageSummary.branches.pct}%
[View coverage report](http://ptf-ci-master2.sagefr.adinternal.com:8080/job/etna-ui/job/PR-${process.argv[2]}/${
    process.argv[3]
}/clover-report/)`;

if (previousCoverage.lines - 0.5 < coverageSummary.statements.pct) {
    githubCoveragePost += `
    
**Merging this PR**
- For external API changes, new features etc:
[Merge as a new feature (minor)](http://ptf-ci-master2.sagefr.adinternal.com:8080/job/etna-ui-merge/buildWithParameters/?PR_ID=${
        process.argv[2]
    }&TYPE=minor)
- For bugfixes that don't come with external API changes:
[Merge as a bugfix (patch)](http://ptf-ci-master2.sagefr.adinternal.com:8080/job/etna-ui-merge/buildWithParameters/?PR_ID=${
        process.argv[2]
    }&TYPE=patch)
- For anything else such as resolving tech debt issues:
[Merge without a release (none)](http://ptf-ci-master2.sagefr.adinternal.com:8080/job/etna-ui-merge/buildWithParameters/?PR_ID=${
        process.argv[2]
    }&TYPE=none)`;
}

if (previousCoverage.lines > coverageSummary.statements.pct) {
    githubCoveragePost += `
    
Looks like the code coverage is decreasing in your branch :( You will burn in the developers hell if you don't test your code! Please add some tests before merging your changes

- Previous coverage: ${previousCoverage.lines}%
- Current coverage: ${coverageSummary.statements.pct}%`;
}

fs.writeFileSync(
    path.resolve(__dirname, 'test-results', 'github.json'),
    JSON.stringify({
        body: githubCoveragePost,
    }),
);
