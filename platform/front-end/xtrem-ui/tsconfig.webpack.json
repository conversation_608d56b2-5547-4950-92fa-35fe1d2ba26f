{"compilerOptions": {"allowSyntheticDefaultImports": true, "baseUrl": ".", "declaration": false, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "jsx": "react", "lib": ["es2022", "dom"], "module": "esnext", "moduleResolution": "node", "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "outDir": "build", "rootDir": ".", "skipLibCheck": true, "sourceMap": true, "strictNullChecks": true, "ignoreDeprecations": "5.0", "target": "es2022"}, "include": ["./lib"], "exclude": ["./lib/**/*.test.ts", "./lib/**/*.test.tsx"]}