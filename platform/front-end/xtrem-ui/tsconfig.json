{"extends": "../tsconfig", "compilerOptions": {"baseUrl": ".", "outDir": "build", "rootDir": ".", "paths": {"timers": ["node_modules/timers-browserify"], "stream": ["node_modules/stream-browserify"]}}, "include": ["index.ts", "./lib", "jest-svg-transformer.js"], "exclude": ["./lib/**/*.test.ts", "./lib/**/*.test.tsx", "./lib/**/__tests__/**/*", "./lib/__tests__/**/*", "./lib/**/__mocks__/**/*", "./lib/__mocks__/**/*"], "references": [{"path": "../../shared/xtrem-async-helper"}, {"path": "../xtrem-client"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-decimal"}, {"path": "../xtrem-document-editor"}, {"path": "../../shared/xtrem-filter-utils"}, {"path": "../../shared/xtrem-shared"}, {"path": "../xtrem-ui-components"}, {"path": "../../cli/xtrem-cli-transformers"}, {"path": "../xtrem-static-shared"}]}