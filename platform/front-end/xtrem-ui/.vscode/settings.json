{"editor.formatOnSave": true, "files.exclude": {"**/node_modules": true, "**/out": true, "**/certificatetest": true}, "typescript.tsdk": "./node_modules/typescript/lib", "cSpell.words": ["adelle", "Antialiasing", "Barcode", "clas", "CLASSNAME", "Constructible", "customizations", "Datetime", "debounced", "decapitalize", "<PERSON><PERSON>", "dropdown", "Errorable", "font", "fullcalendar", "grapqhl", "highchart", "immer", "jsbarcode", "junit", "Lodable", "lokijs", "neighbours", "orderable", "pendo", "pendoid", "Postfixable", "Prefixable", "qrious", "reorderable", "Requestable", "resizable", "Resultset", "sdata", "Snackbar", "sonarts", "tabindex", "testid", "textbox", "Tileable", "typedoc", "Unmount", "unserializable", "upsert", "Validatable", "Xtreem", "Xtrem"], "git.ignoreLimitWarning": true, "[typescript]": {"editor.insertSpaces": true}, "debug.node.autoAttach": "off", "typescript.preferences.importModuleSpecifier": "relative", "sonarlint.connectedMode.project": {"connectionId": "sage-erp-x3", "projectKey": "Sage-ERP-X3_xtrem"}}