{
    // Use IntelliSense to learn about possible Node.js debug attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch localhost",
            "type": "chrome",
            "request": "launch",
            "url": "http://localhost:3000",
            "webRoot": "${workspaceFolder}/lib",
            "sourceMaps": true,
            "smartStep": true
        },
        {
            "name": "Run unit tests with coverage",
            "type": "node",
            "request": "launch",
            "program": "${workspaceRoot}/../../../node_modules/jest/bin/jest.js",
            "runtimeArgs": ["--expose-gc"],
            "args": [
                "--coverage",
                "--config=jest.config.js",
                "--no-cache",
                "--runInBand",
                "--logHeapUsage",
                "--no-compilation-cache",
                "--workerIdleLimit=2GB"
            ],
            "sourceMaps": true,
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Jest Debug xtrem-ui",
            "runtimeExecutable": "sh", // <-- The important bit!
            "cwd": "${workspaceFolder}",
            "program": "${workspaceFolder}/node_modules/.bin/jest",
            "args": ["${relativeFile}"],
            "console": "integratedTerminal",
            "internalConsoleOptions": "openOnFirstSessionStart"
        }
    ]
}
