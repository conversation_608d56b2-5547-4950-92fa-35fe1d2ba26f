'use strict';

const Webpack = require('webpack');
const path = require('path');
const merge = require('webpack-merge');
const TerserPlugin = require('terser-webpack-plugin');
const base = require('./base');
const rootDir = path.resolve(__dirname, '..');
const xtremi18n = require('@sage/xtrem-cli-transformers');

module.exports = merge.merge(base(), {
    devtool: false,
    mode: 'production',
    devtool: false,
    plugins: [
        new Webpack.DefinePlugin({
            'process.env.NODE_ENV': JSON.stringify('production'),
            'process.env.NO_CONSOLE': false,
            DEV_MODE: false,
            AG_LICENCE: JSON.stringify('UNSET'),
            FEATURE_USER_CLIENT_CUSTOMIZATION: JSON.stringify(true),
        }),
        {
            apply: compiler => {
                compiler.hooks.afterEmit.tap('AfterEmitPlugin', () => {
                    xtremi18n.mergeTranslationFiles(rootDir);
                });
            },
        },
        new Webpack.BannerPlugin({
            banner: `Copyright (c) 2020-${new Date().getFullYear()} The Sage Group plc or its licensors. Sage, Sage logos, and Sage product and service names mentioned herein are the trademarks of Sage Global Services Limited or its licensors. All other trademarks are the property of their respective owners. */`,
        }),
    ],
    optimization: {
        minimize: true,
        minimizer: [
            new TerserPlugin({
                extractComments: false,
                terserOptions: {
                    keep_classnames: true,
                    keep_fnames: true,
                },
            }),
        ],
    },
});
