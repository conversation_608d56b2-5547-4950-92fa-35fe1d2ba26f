const express = require('express');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { createProxyMiddleware } = require('http-proxy-middleware');

const targetUrl = 'https://sdmo-ci-v2.eu.dev-sagextrem.com';
const renewUrl = 'https://login.eu.dev-sagextrem.com/renew?locale=undefined';
const userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36';

const cookiesFile = path.resolve(__dirname, 'prod-cookies.txt');
let cookies = fs.readFileSync(cookiesFile, 'utf8').trim();
let server = null;

const reload = async () => {
  console.log(`${new Date().toISOString()} - Renewing authentication tokens...`);
  const renewResponse = await axios.get(renewUrl, {
    headers: {
      'Cookie': cookies,
      'User-Agent': userAgent,
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    }
  });
  cookies = renewResponse.headers.get('set-cookie').map(c => {
    return c.split(';')[0];

  }).join(';');
  fs.writeFileSync(cookiesFile, cookies);
  console.log(`${new Date().toISOString()} - Auth tokens renewed!`);

  if (server) {
    console.log(`${new Date().toISOString()} - Stopping server...`);
    await new Promise((resolve) => server.close(resolve));
    console.log(`${new Date().toISOString()} - Server stopped.`);
  }

  const apiMiddleWare = createProxyMiddleware({
    target: `${targetUrl}/api`,
    changeOrigin: true,
    headers: {
      'Cookie': cookies,
      'User-Agent': userAgent,
      'Origin': targetUrl,
    }
  });
  const metadataMiddleWare = createProxyMiddleware({
    target: `${targetUrl}/metadata`,
    changeOrigin: true,
    headers: {
      'Cookie': cookies,
      'User-Agent': userAgent,
      'Origin': targetUrl,
    }
  });
  const explorerMiddleWare = createProxyMiddleware({
    target: `${targetUrl}/explorer`,
    changeOrigin: true,
    headers: {
      'Cookie': cookies,
      'User-Agent': userAgent,
      'Origin': targetUrl,
    }
  });
  const pluginsMiddleWare = createProxyMiddleware({
    target: `${targetUrl}/plugins`,
    changeOrigin: true,
    headers: {
      'Cookie': cookies,
      'User-Agent': userAgent,
      'Origin': targetUrl,
    }
  });
  const standaloneConfigMiddleWare = createProxyMiddleware({
    target: `${targetUrl}/standalone-config`,
    changeOrigin: true,
    headers: {
      'Cookie': cookies,
      'User-Agent': userAgent,
      'Origin': targetUrl,
    }
  });

  console.log(`${new Date().toISOString()} - Starting server...`);
  const app = express();
  app.use('/api', apiMiddleWare);
  app.use('/metadata', metadataMiddleWare);
  app.use('/explorer', explorerMiddleWare);
  app.use('/plugins', pluginsMiddleWare);
  app.use('/standalone-config', standaloneConfigMiddleWare);
  server = app.listen(8240);
  console.log(`${new Date().toISOString()} - Server started!`);
  setTimeout(reload, 1000 * 60 * 2);
};

console.log(`${new Date().toISOString()} - Booting...`);
reload().catch(e => {
  console.log(`${new Date().toISOString()} - CRASHED!!!`);
  console.error(e);
});