'use strict';

const Webpack = require('webpack');
const WebpackNotifierPlugin = require('webpack-notifier');
const merge = require('webpack-merge');
const base = require('./base');
const path = require('path');
const fs = require('fs');
const rootDir = path.resolve(__dirname, '..');
const buildDir = path.resolve(rootDir, 'build');
const agGridLicencePath = path.resolve(rootDir, 'ag-grid-license.txt');
const agGridLicence = fs.existsSync(agGridLicencePath)
    ? Buffer.from(fs.readFileSync(agGridLicencePath, 'utf8')).toString('base64')
    : '';

module.exports = merge.merge(base('esbuild'), {
    devtool: 'source-map',
    mode: 'development',
    target: 'web',
    devServer: {
        static: {
            directory: buildDir,
            watch: {
                ignored: /(i18n|node_modules)/,
            },
            publicPath: '/',
        },
        compress: true,
        port: 3000,
        host: '0.0.0.0',
        historyApiFallback: {
            index: '/',
        },
        headers: {
            "Access-Control-Allow-Origin": "http://localhost:3000, http://localhost:8240, http://127.0.0.1:3000, http://127.0.0.1:8240",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
            "Access-Control-Allow-Headers": "X-Requested-With, content-type, Authorization"
        },
        proxy: [
            {
                context: ['/api', '/metadata', '/explorer', '/plugins', '/dev', '/standalone-config'],
                target: 'http://localhost:8240',
            },
        ],
    },
    cache: {
        type: 'filesystem',
        cacheDirectory: path.resolve(process.cwd(), '.temp_cache'),
    },
    plugins: [
        new WebpackNotifierPlugin({
            skipFirstNotification: true,
            alwaysNotify: true,
            excludeWarnings: true,
            sound: false,
        }),
        new Webpack.DefinePlugin({
            'process.env.NODE_ENV': JSON.stringify('development'),
            'process.env.NO_CONSOLE': JSON.stringify(false),
            DEV_MODE: true,
            AG_LICENCE: JSON.stringify(agGridLicence),
            FEATURE_USER_CLIENT_CUSTOMIZATION: JSON.stringify(false),
        }),
    ],
    optimization: {
        minimize: false,
        moduleIds: 'deterministic',
    },
});
