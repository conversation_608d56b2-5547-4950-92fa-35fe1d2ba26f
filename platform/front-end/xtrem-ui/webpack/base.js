const path = require('path');
const rootDir = path.resolve(__dirname, '..');
const buildDir = path.resolve(rootDir, 'build');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const Webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const LastCallWebpackPlugin = require('last-call-webpack-plugin');
const templateFn = require('adjust-sourcemap-loader').moduleFilenameTemplate({
    format: 'projectRelative',
});
const processFn = require('adjust-sourcemap-loader/lib/process');
const timestamp = new Date().toISOString();
const WebpackBar = require('webpackbar');
const xtremi18n = require('@sage/xtrem-cli-transformers');
const { copyStaticResources } = require('@sage/xtrem-static-shared');
const cMapsDir = path.join(path.dirname(require.resolve('pdfjs-dist/package.json')), 'cmaps');
const standardFontsDir = path.join(
    path.dirname(require.resolve('pdfjs-dist/package.json')),
    'standard_fonts',
);

const pdfWorker = require.resolve('pdfjs-dist/build/pdf.worker.min.mjs');

const WEBPACK_NONCE = '{{nonce}}';

if (process.env.SKIP_CLIENT) {
    // eslint-disable-next-line no-console
    console.log('SKIP_CLIENT environment variable detected, stopping webpack bundle build.');
    process.exit(0);
}

// eslint-disable-next-line no-console
console.log('Build target dir: ', buildDir);

module.exports = (typescriptLoader = 'ts-loader') => {
    const loader =
        typescriptLoader === 'esbuild'
            ? {
                test: /\.tsx?$/,
                loader: 'esbuild-loader',
                options: {
                    loader: 'tsx',
                },
            }
            : {
                test: /\.tsx?$/,
                loader: 'ts-loader',
                options: {
                    transpileOnly: false,
                    getCustomTransformers: () => ({
                        before: [xtremi18n.messageTransformer],
                    }),
                },
            };
    return {
        entry: './lib/consumer-mock/consumer-mock.tsx',
        context: rootDir,
        target: 'web',
        // Check https://github.com/TypeStrong/ts-loader#transpileonly-boolean-defaultfalse
        ignoreWarnings: [/export .* was not found in|Can't resolve 'fs'/],
        output: {
            filename: '[name]-bundle.js',
            path: buildDir,
            pathinfo: true,
            publicPath: '/',
            devtoolModuleFilenameTemplate: templateFn,
            devtoolFallbackModuleFilenameTemplate: templateFn,
        },
        resolve: {
            fallback: {
                path: false,
                buffer: false,
                events: false,
            },
            symlinks: true,
            alias: {
                'carbon-react': path.resolve(rootDir, 'node_modules/carbon-react/'),
                '@sage/bms-dashboard/bms-dashboard.css': require.resolve('@sage/bms-dashboard/bms-dashboard.css'),
                react: path.resolve(rootDir, 'node_modules/react/'),
                timers: 'timers-browserify',
                stream: 'stream-browserify',
                'react-dom/server': path.resolve(rootDir, 'node_modules/react-dom/server.js'),
            },
            extensions: ['.ts', '.tsx', '.js', '.json', '.scss', '.css'],
        },
        plugins: [
            new Webpack.NormalModuleReplacementPlugin(
                /ckeditor5-[^/\\]+[/\\]theme[/\\].+\.css$/,
                path.join(rootDir, 'webpack', 'empty.css'),
            ),
            new Webpack.DefinePlugin({
                'global.__webpack_nonce__': JSON.stringify(WEBPACK_NONCE),
            }),
            new Webpack.NormalModuleReplacementPlugin(/@sage\/xtrem-i18n/, path.resolve(rootDir, 'empty-module.js')),
            // shows progress bar
            new WebpackBar(),
            new MiniCssExtractPlugin({
                filename: '[name]-xtrem-ui-style.css',
            }),
            new Webpack.BannerPlugin({
                banner: 'Built on ' + timestamp,
            }),
            new Webpack.BannerPlugin({
                banner: `window.XTREM_BUILD = '${process.env.BUILD_NUMBER ? 'b ' + process.env.BUILD_NUMBER : 'dev'}';`,
                raw: true,
                include: 'bundle.js',
            }),
            new CopyWebpackPlugin({
                patterns: [
                    {
                        from: cMapsDir,
                        to: path.resolve(buildDir, 'cmaps')
                    },
                    {
                        from: standardFontsDir,
                        to: path.resolve(buildDir, 'standard_fonts')
                    },
                    {
                        from: pdfWorker,
                        to: path.resolve(buildDir, 'pdf.worker.min.mjs')
                    },
                    {
                        from: path.resolve(rootDir + '/dev-resources/index.html'),
                        to: buildDir,
                    },
                    {
                        from: path.resolve(rootDir + '/dev-resources/sounds'),
                        to: path.resolve(buildDir),
                    },
                    {
                        from: path.resolve(rootDir + '/dev-resources/async-loader-animation.webm'),
                        to: path.resolve(buildDir),
                    },
                    {
                        from: path.resolve(rootDir + '/dev-resources'),
                        to: path.resolve(buildDir, 'images'),
                    },
                    {
                        from: path.resolve(rootDir, 'lib/i18n'),
                        to: path.resolve(buildDir, 'lib/i18n'),
                    },
                    {
                        from: path.resolve(rootDir + '/lib/render/style/breakpoints.json'),
                        to: path.resolve(buildDir + '/lib/render/style/breakpoints.json'),
                    },
                    {
                        from: require.resolve('axe-core/axe.min.js'),
                        to: path.resolve(buildDir + '/axe.min.js'),
                    },
                ],
            }),
            new MiniCssExtractPlugin({
                filename: '[name].[contenthash].css',
            }),
            copyStaticResources(buildDir),
            // currently devtoolModuleFilenameTemplate is not respected by OptimizeCSSAssetsPlugin so we must do it ourselves
            new LastCallWebpackPlugin({
                assetProcessors: [
                    {
                        regExp: /\.css\.map/,
                        processor: (_assetName, asset) =>
                            Promise.resolve(JSON.parse(asset.source()))
                                .then(obj =>
                                    processFn(
                                        {},
                                        {
                                            format: 'projectRelative',
                                        },
                                        obj,
                                    ),
                                )
                                .then(obj => JSON.stringify(obj)),
                    },
                ],
            }),
        ],
        module: {
            rules: [
                {
                    test: /\.svg$/,
                    use: [
                        {
                            loader: 'svg-inline-loader',
                            options: {
                                removeTags: true,
                                removingTags: ['title', 'desc'],
                                removeSVGTagAttrs: false,
                            },
                        },
                    ],
                },
                {
                    test: /\.scss$/,
                    exclude: [/ckeditor5-[^/\\]+[/\\]theme[/\\].+\.css$/],
                    use: [
                        {
                            loader: MiniCssExtractPlugin.loader,
                        },
                        {
                            loader: 'css-loader', // 3. Translates CSS into CommonJS
                            options: {
                                url: {
                                    filter: (url, resourcePath) => {
                                        // Exclude URLs starting with '/absolute/path'
                                        if (url.startsWith('/')) {
                                            return false;
                                        }
                                        return true;
                                    },
                                },
                            },
                        },
                        {
                            loader: 'resolve-url-loader', // 2. Resolves relative paths in url() statements based on the original source file
                        },
                        {
                            loader: 'sass-loader', // 1. compiles Sass to CSS
                            options: {
                                sourceMap: true, // needed for 'resolve-url-loader' to work
                                sassOptions: {
                                    exclude: [/node_modules/],
                                    sourceMapContents: false, // needed for 'resolve-url-loader' to work
                                },
                            },
                        },
                    ],
                },
                {
                    test: /\.(png|woff|woff2|eot|ttf)$/,
                    loader: 'url-loader',
                    options: {
                        limit: 100000,
                    },
                },

                loader,
                {
                    test: /\.css$/,
                    exclude: [/ckeditor5-[^/\\]+[/\\]theme[/\\].+\.css$/],
                    use: ['style-loader', 'css-loader'],
                },
                // All output '.js' files will have any sourcemaps re-processed by 'source-map-loader'.
                {
                    enforce: 'pre',
                    exclude: [/node_modules/],
                    test: /\.js$ /,
                    loader: 'source-map-loader',
                },
            ],
        },
        optimization: {
            runtimeChunk: 'single',
            splitChunks: {
                chunks: 'all',
                maxInitialRequests: Infinity,
                minSize: 0,
                cacheGroups: {
                    vendor: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendor',
                        chunks: 'all',
                    },
                },
            },
        },
    };
};
