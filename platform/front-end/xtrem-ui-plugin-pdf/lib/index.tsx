/* eslint-disable react/jsx-indent */
/* eslint-disable react/jsx-indent-props */

import * as ui from '@sage/xtrem-ui';
import * as React from 'react';

export interface PdfPluginProperties {
    /** Height of the editor in pixels. */
    height?: number;
}

type PdfQueryProps = ui.plugin.XtremUiPluginComponentProps<PdfPluginProperties, string | null>;

const base64regex = /[A-Za-z0-9+/=]/;
const urlRegex = /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)/;

const formatValue = (value: string | null) => {
    if (value) {
        if (value.match(base64regex)) {
            const decodedPdfContent = atob(value);
            const byteArray = new Uint8Array(decodedPdfContent.length);
            for (let i = 0; i < decodedPdfContent.length; i += 1) {
                byteArray[i] = decodedPdfContent.charCodeAt(i);
            }
            const blob = new Blob([byteArray.buffer], { type: 'application/pdf' });
            return URL.createObjectURL(blob);
        }
        if (value.match(urlRegex)) {
            return value;
        }

        throw new Error('Unsupported value in the PDF field.');
    }

    return null;
};

export function PdfQueryField(props: PdfQueryProps) {
    const { value, fieldProperties, fixedHeight } = props;
    const formattedValue = formatValue(value);
    const height = fieldProperties.height || fixedHeight || 400;

    if (formattedValue) {
        return (
            <div style={{ height }}>
                <object
                    data={formattedValue}
                    type="application/pdf"
                    width="100%"
                    height="100%"
                    aria-label={ui.localize('@sage/xtrem-ui-plugin-pdf/pdf-preview', 'PDF preview')}
                >
                    <iframe title="pdf" width="100%">
                        <p>
                            {ui.localize(
                                '@sage/xtrem-ui-plugin-pdf/no-browser-support',
                                'This browser does not support PDF display.',
                            )}
                        </p>
                    </iframe>
                </object>
            </div>
        );
    }
    return (
        <div style={{ height }}>
            <p>{ui.localize('@sage/xtrem-ui-plugin-pdf/no-pdf-to-display', 'No PDF to display.')}</p>
        </div>
    );
}

export default {
    name: 'pdf',
    component: PdfQueryField,
    createFieldQuery: (): ui.plugin.QueryProperty => {
        return { value: true };
    },
    transformFromGraphValue: (rawValueFromQueryResult: any) => {
        // eslint-disable-next-line react/destructuring-assignment
        if (rawValueFromQueryResult && rawValueFromQueryResult.value) {
            return rawValueFromQueryResult.value;
        }
        return null;
    },
    transformToGraphValue: (value: any) => {
        return value ? { value: btoa(value) } : null;
    },
} as ui.plugin.XtremUiPlugin<PdfPluginProperties, string>;
