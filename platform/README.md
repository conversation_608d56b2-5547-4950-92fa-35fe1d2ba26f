# Xtrem

This is the [monorepo](https://en.wikipedia.org/wiki/Monorepo) of the XTREM project.

This project uses [Lerna](https://github.com/lerna/lerna) to manage all its packages, which are found under the `@sage` folder.

<img src="https://dev.azure.com/sage-liveservices/X3 XTREM/_apis/build/status/868?api-version=6.0-preview.1&branchName=master" alt="Status" />

## 📟 &nbsp;Install and Usage

1. Clone the repository with

```sh
git clone https://"url": "git://github.com/Sage-ERP-X3/xtrem.git
```

2. Bootstrap all packages with

```sh
pnpm i
```

3. Copy configuration from the template

```sh
cp xtrem-config-template.yml xtrem-config.yml
# Remember to set your credentials :)
```

4. Create the schema and tables + init data

```sh
pnpm run xtrem layers --load setup,test
```

5. Refer to the `README.md` inside every package to get more details

## 💻 &nbsp;Development

### 🚰&nbsp;Clean Install

The following command will perform the following steps:

- run the `clean` command inside each package (`pnpm run clean:artifacts`)
- delete the `node_modules` folder inside each package (`pnpm run clean:packages`)
- delete the top-level `node_modules` folder (`pnpm run clean:root`)
- install all dependencies (`pnpm install`)

```sh
pnpm run clean:install
```

_**Note** that this command might fail on Windows. In case it does it is recommended to close all code editor's windows and kill all Explorer processes before re-running it._

### ➕&nbsp;Add a dependency

In order to add a new dependency to one or more Xtrem packages please use the following command from the project root:

```sh
pnpm run add
```

and you will be guided through the installation process.

### 🔗&nbsp;Link Xtrem packages

In order to link one or more Xtrem dependencies to the global scope you can issue the following command from the project root:

```sh
pnpm run link
```

then pick all the desired packages from the list and hit `Enter`.

Note that this is needed in order to test any Xtrem package against other projects like [xtrem-services](https://github.com/Sage-ERP-X3/xtrem-services).

## 🏁&nbsp;Test

### 🌈&nbsp;Run all tests in parallel

The following command will run all tests in parallel and stop upon first failure.

```sh
pnpm run test
```

### ⏳&nbsp;Run all tests sequentially

The following command will run all tests sequentially and stop upon first failure.

```sh
pnpm run test:sync
```

### ✅&nbsp;Run tests for a given set of packages

```sh
pnpm run script
```

- select `test` from the list
- pick one or more xtrem packages

Alternatively you can achieve the same result with the following command:

```sh
lerna run test --scope=@sage/<package1> --scope=@sage/<package2> --concurrency=1 --stream
```

### 👀&nbsp;Run a single test in **watch** mode

The following steps will allow you to run **the currently opened test file** in watch mode.

Open up any test file and then hit

```sh
Ctrl/Cmd + Shift + P
```

- Look for `Tasks: Run task`
- Select `run/watch current opened test file with mocha` from the dropdown list

### 🔴&nbsp;Run a single test in **debug** mode

The following steps will allow you to run **the currently opened test file** in debug mode.

```sh
Ctrl/Cmd + Shift + P
```

- Look for `Tasks: Run task`
- Select `debug current opened test file with mocha` from the dropdown list

## 📋&nbsp;Scripts

In order to run a single **pnpm script** across multiple packages you can run the following command from the project root:

```sh
pnpm run script
```

then pick any command from the dropdown list or type your own.

Alternatively you can achieve the same result with the following command:

```sh
lerna run <command> --scope=@sage/<package1> --scope=@sage/<package2> --concurrency=1 --stream
```

## 🚀&nbsp;Publish

### ⬆&nbsp;Release minor version of Xtrem

Navigate to [this link](http://ptf-ci-master2.sagefr.adinternal.com:8080/job/daily-jobs/job/create-image-patch-publish-latest) and run the `create-image-patch-publish-latest` job.

Although the above approach is recommended, in case you want to force a deployment without creating any Git tags you can issue the following command:

```sh
pnpm run lerna publish from-package --yes --registry="https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"
```

### ❎&nbsp;Release a patch for Xtrem V2

Navigate to [this link](http://ptf-ci-master2.sagefr.adinternal.com:8080/job/create-image-patch-publish-v2) and run the `create-image-patch-publish-v2` job.

## 💈&nbsp;Coding Style

This repository uses [Prettier](https://prettier.io/), it is recommended to download a plugin for your IDE.

### Commit to this repository

This repository uses [conventional-commits](https://www.conventionalcommits.org/en) hence changelog files are automatically generated.
In order to commit to this repository you have to use the following commit convention:

```
<type>[optional scope]: <description>

[optional body]

[optional footer]
```

Here are a couple of valid examples:

`feat(xtrem-core): added awesome feature`

`fix(xtrem-postgres): fixed critical bug`

Here are all the available types along with their meaning:

- **build**: changes that affect the build system or external dependencies (example scopes: gulp, broccoli, npm)
- **ci**: changes to our CI configuration files and scripts (example scopes: Jenkins, Travis)
- **chore**: other changes that don't modify src or test files
- **docs**: documentation only changes
- **feat**: a new feature
- **fix**: a bug fix
- **perf**: a code change that improves performance
- **refactor**: a code change that neither fixes a bug nor adds a feature
- **revert**: reverts a previous commit
- **style**: changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc)
- **test**: adding missing tests or correcting existing tests

You can read the full convention on [here](https://www.conventionalcommits.org/en/v1.0.0-beta.4/#specification)

Also by using `pnpm run commit` you will be guided through the creation of a valid commit.

## 💪🏻 &nbsp;Contributors

Here is a list of [all the people](https://github.com/Sage-ERP-X3/xtrem-platform/contributors) that contributed to this project.

## 📆 &nbsp;Versioning

We use [SemVer](http://semver.org/) for versioning.
You can see all the versions that have been released by clicking on [this link](https://github.com/Sage-ERP-X3/xtrem-platform/tags).
