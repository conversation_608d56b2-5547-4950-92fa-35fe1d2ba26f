{"@sage/xtrem-upgrade-test": [{"topic": "NewInV1ReleasedNode/asyncExport/start", "queue": "import-export", "sourceFileName": "new-in-v-1-released-node.ts"}, {"topic": "NodeForDataPatches/asyncExport/start", "queue": "import-export", "sourceFileName": "node-for-data-patches.ts"}, {"topic": "UpgradeAddColumns/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-add-columns.ts"}, {"topic": "UpgradeAlterColumns/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-alter-columns.ts"}, {"topic": "UpgradeChangeBaseNode/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-change-base-node.ts"}, {"topic": "UpgradeCustomSqlUpdated/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-custom-sql-updated.ts"}, {"topic": "UpgradeCustomSqlV1/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-custom-sql-v1.ts"}, {"topic": "UpgradeDataCustomSqlAction/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-data-custom-sql-action.ts"}, {"topic": "UpgradeDatatypes/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-datatypes.ts"}, {"topic": "UpgradeDeleteColumns/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-delete-columns.ts"}, {"topic": "UpgradeDeleteTable/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-delete-table.ts"}, {"topic": "UpgradeNodeToExtend/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-node-to-extend.ts"}, {"topic": "UpgradeNotifyUpdated/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-notify-updated.ts"}, {"topic": "UpgradePropertyToEncrypt/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-property-to-encrypt.ts"}, {"topic": "UpgradeReferenced/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-referenced.ts"}, {"topic": "UpgradeRefToCustomSqlUpdated/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-ref-to-custom-sql-updated.ts"}, {"topic": "UpgradeReloadCsvVendorChild/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-reload-csv-vendor-child.ts"}, {"topic": "UpgradeReloadCsvVendorParent/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-reload-csv-vendor-parent.ts"}, {"topic": "UpgradeReloadFromCsv/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-reload-from-csv.ts"}, {"topic": "UpgradeReloadFromVendorCsv/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-reload-from-vendor-csv.ts"}, {"topic": "UpgradeRenameColumns/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-rename-columns.ts"}, {"topic": "UpgradeRenameEnum/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-rename-enum.ts"}, {"topic": "UpgradeRenameEnumSecondLocation/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-rename-enum-second-location.ts"}, {"topic": "UpgradeRenameTableV1/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-rename-table.ts"}, {"topic": "UpgradeRenameTableWithNotifyV1/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-rename-table.ts"}, {"topic": "UpgradeSubNode1/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-sub-node-1.ts"}, {"topic": "UpgradeUpdateColumns/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-update-columns.ts"}, {"topic": "UpgradeVitalChild/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-vital-child.ts"}, {"topic": "UpgradeVitalParent/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-vital-parent.ts"}]}