import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { UpgradeReloadCsvVendorParent } from './upgrade-reload-csv-vendor-parent';

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.node<UpgradeReloadCsvVendorChild>({
    storage: 'sql',
    canDelete: true,
    canRead: true,
    canSearch: true,
    isPublished: true,
    isVitalCollectionChild: true,
})
export class UpgradeReloadCsvVendorChild extends Node {
    @decorators.referenceProperty<UpgradeReloadCsvVendorChild, 'parent'>({
        isStored: true,
        isPublished: true,
        node: () => UpgradeReloadCsvVendorParent,
        isVitalParent: true,
    })
    readonly parent: Reference<UpgradeReloadCsvVendorParent>;

    @decorators.stringProperty<UpgradeReloadCsvVendorChild, 'id'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<UpgradeReloadCsvVendorChild, 'childString'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly childString: Promise<string>;
}
