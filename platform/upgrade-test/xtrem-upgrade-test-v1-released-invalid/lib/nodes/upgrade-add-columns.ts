import { decorators, integer, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1 -> V1(released) differences :
 * - value2 : new column
 */
@decorators.node<UpgradeAddColumns>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeAddColumns extends Node {
    @decorators.stringProperty<UpgradeAddColumns, 'name1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name1: Promise<string>;

    @decorators.integerProperty<UpgradeAddColumns, 'value1'>({
        isStored: true,
        isPublished: true,
        defaultValue: 1,
    })
    readonly value1: Promise<integer>;

    @decorators.integerProperty<UpgradeAddColumns, 'value2'>({
        isStored: true,
        isPublished: true,
        defaultValue: 1,
    })
    readonly value2: Promise<integer>;
}
