import { decorators, integer, Node, Reference, StringDataType } from '@sage/xtrem-core';

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.node<UpgradeUpdateColumns>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [
        { orderBy: { name1: 1 }, isUnique: true },
        { orderBy: { name1: 1, value2: 1 }, isUnique: true },
        { orderBy: { name1: 1, value2: 1, value3: 1 }, isUnique: true },
        { orderBy: { name1: 1, value2: -1, value3: -1 }, isUnique: false },
    ],
})
export class UpgradeUpdateColumns extends Node {
    @decorators.stringProperty<UpgradeUpdateColumns, 'name1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly name1: Promise<string>;

    @decorators.integerProperty<UpgradeUpdateColumns, 'value2'>({
        isStored: true,
        isPublished: true,
    })
    readonly value2: Promise<integer>;

    @decorators.integerProperty<UpgradeUpdateColumns, 'value3'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        allowedInUniqueIndex: true,
    })
    readonly value3: Promise<integer | null>;

    @decorators.referenceProperty<UpgradeUpdateColumns, 'selfRefToNullable'>({
        isPublished: true,
        isStored: true,
        isNullable: false,
        node: () => UpgradeUpdateColumns,
        defaultValue() {
            return this;
        },
    })
    readonly selfRefToNullable: Reference<UpgradeUpdateColumns>;

    @decorators.referenceProperty<UpgradeUpdateColumns, 'selfRefToNonNullable'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => UpgradeUpdateColumns,
    })
    readonly selfRefToNonNullable: Reference<UpgradeUpdateColumns | null>;
}
