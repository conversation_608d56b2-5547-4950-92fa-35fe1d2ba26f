import { decorators, StringDataType } from '@sage/xtrem-core';
import { UpgradeBaseNode } from '.';
import { UpgradeAbstractNode1 } from './upgrade-abstract-node-1';

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.subNode<UpgradeChangeBaseNode>({
    extends: () => UpgradeBaseNode,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
})
export class UpgradeChangeBaseNode extends UpgradeAbstractNode1 {
    @decorators.stringProperty<UpgradeChangeBaseNode, 'strProp'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strProp: Promise<string>;
}
