import { decorators, StringDataType } from '@sage/xtrem-core';
import { UpgradeBaseNode } from '.';

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.subNode<UpgradeSubNode1>({
    extends: () => UpgradeBaseNode,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
})
export class UpgradeSubNode1 extends UpgradeBaseNode {
    @decorators.stringProperty<UpgradeSubNode1, 'strSub1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strSub1: Promise<string>;
}
