import { decorators, integer, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { UpgradeReferenced } from './upgrade-referenced';

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.node<UpgradeDeleteColumns>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [
        { orderBy: { name1: 1 }, isUnique: true },
        { orderBy: { name1: 1, name2: 1 }, isUnique: true },
        { orderBy: { name1: 1, name2: 1, name3: 1 }, isUnique: true },
        { orderBy: { name2: 1 }, isUnique: true },
        { orderBy: { name3: 1 }, isUnique: true },
    ],
})
export class UpgradeDeleteColumns extends Node {
    @decorators.stringProperty<UpgradeDeleteColumns, 'name1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name1: Promise<string>;

    @decorators.stringProperty<UpgradeDeleteColumns, 'name2'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name2: Promise<string>;

    @decorators.stringProperty<UpgradeDeleteColumns, 'name3'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name3: Promise<string>;

    @decorators.integerProperty<UpgradeDeleteColumns, 'storedToComputed'>({
        isStored: true,
        isPublished: true,
    })
    readonly storedToComputed: Promise<integer>;

    @decorators.referenceProperty<UpgradeDeleteColumns, 'ref1'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeReferenced,
    })
    readonly ref1: Reference<UpgradeReferenced>;
}
