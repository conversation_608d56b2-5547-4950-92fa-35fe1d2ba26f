import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.node<UpgradeCustomSqlV1>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeCustomSqlV1 extends Node {
    @decorators.stringProperty<UpgradeCustomSqlV1, 'string1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly string1: Promise<string>;

    @decorators.integerProperty<UpgradeCustomSqlV1, 'int1'>({
        isStored: true,
        isPublished: true,
    })
    readonly int1: Promise<string>;

    @decorators.booleanProperty<UpgradeCustomSqlV1, 'bool1'>({
        isStored: true,
        isPublished: true,
    })
    readonly bool1: Promise<string>;
}
