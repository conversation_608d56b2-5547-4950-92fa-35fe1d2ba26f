/**
 * This node will check that we are not allowed to create a new table on a released package
 */
import { decorators, integer, Node } from '@sage/xtrem-core';

@decorators.node<NewInV1ReleasedNode>({
    isPublished: true,
    storage: 'sql',
})
export class NewInV1ReleasedNode extends Node {
    @decorators.integerProperty<NewInV1ReleasedNode, 'val'>({
        isPublished: true,
        isStored: true,
    })
    readonly val: Promise<integer>;

}
