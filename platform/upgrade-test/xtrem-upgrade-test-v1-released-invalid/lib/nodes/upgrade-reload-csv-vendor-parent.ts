import { Collection, decorators, Node, StringDataType } from '@sage/xtrem-core';
import { UpgradeReloadCsvVendorChild } from './upgrade-reload-csv-vendor-child';

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.node<UpgradeReloadCsvVendorParent>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
export class UpgradeReloadCsvVendorParent extends Node {
    @decorators.stringProperty<UpgradeReloadCsvVendorParent, 'id'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<UpgradeReloadCsvVendorParent, 'someString'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly someString: Promise<string>;

    @decorators.collectionProperty<UpgradeReloadCsvVendorParent, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => UpgradeReloadCsvVendorChild,
        reverseReference: 'parent',
    })
    readonly lines: Collection<UpgradeReloadCsvVendorChild>;
}
