import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.node<UpgradeNodeToExtend>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeNodeToExtend extends Node {
    @decorators.stringProperty<UpgradeNodeToExtend, 'name1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name1: Promise<string>;
}
