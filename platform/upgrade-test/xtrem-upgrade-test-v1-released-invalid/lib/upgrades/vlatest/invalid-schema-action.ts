import { UnsafeCustomSqlAction } from '@sage/xtrem-system';

/**
 * Actions that will result in a schema change are forbidden on released packages.
 * A UnsafeCustomSqlAction is executed with a sys pool and could make this kind of changes.
 */
export const invalidSchemaAction = new UnsafeCustomSqlAction({
    description: 'Invalid Schema action',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    body: _options => {
        throw new Error('This action is invalid on a released package');
    },
});
