import { EnumDataType } from '@sage/xtrem-core';

/**
 * v1 -> v2 changes :
 * - enum is renamed (UpgradeRenamedEnumV1 -> UpgradeRenamedEnumV2)
 * - value1V1 -> value1V2 (renamed)
 * - value2V1 -> (removed)
 * - value3V1 -> value3V1 (untouched)
 * - -> value4V2 (added)
 */
enum UpgradeRenamedEnumV1 {
    value1V1,
    value2V1,
    value3V1,
}

export type UpgradeRenamedEnumV1Type = keyof typeof UpgradeRenamedEnumV1;

export const upgradeRenamedEnumV1DataType = new EnumDataType<UpgradeRenamedEnumV1Type>({
    enum: UpgradeRenamedEnumV1,
    filename: __filename,
});
