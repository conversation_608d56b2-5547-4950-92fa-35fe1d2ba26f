import { decorators, Node } from '@sage/xtrem-core';
import { testUpgradeEnumDataType, TestUpgradeEnumType } from '../enums/test-upgrade-enum-type';

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.node<UpgradeRenameEnumSecondLocation>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeRenameEnumSecondLocation extends Node {
    @decorators.enumProperty<UpgradeRenameEnumSecondLocation, 'enumTest'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => testUpgradeEnumDataType,
    })
    readonly enumTest: Promise<TestUpgradeEnumType | null>;
}
