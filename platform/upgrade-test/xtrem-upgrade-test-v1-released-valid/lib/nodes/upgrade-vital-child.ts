import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { UpgradeVitalParent } from './upgrade-vital-parent';

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.node<UpgradeVitalChild>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeVitalChild extends Node {
    @decorators.referenceProperty<UpgradeVitalChild, 'parent'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeVitalParent,
    })
    readonly parent: Reference<UpgradeVitalParent>;

    @decorators.stringProperty<UpgradeVitalChild, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly code: Promise<string>;
}
