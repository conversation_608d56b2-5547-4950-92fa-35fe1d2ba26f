import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.node<UpgradeAbstractNode1>({
    storage: 'sql',
    isAbstract: true,
})
export class UpgradeAbstractNode1 extends Node {
    @decorators.stringProperty<UpgradeAbstractNode1, 'strBase'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strBase: Promise<string>;
}
