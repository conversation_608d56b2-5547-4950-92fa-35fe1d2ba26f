import { decorators, Node, Reference } from '@sage/xtrem-core';
import { UpgradeCustomSqlUpdated } from './upgrade-custom-sql-updated';

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.node<UpgradeCustomSqlUpdated>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeRefToCustomSqlUpdated extends Node {
    @decorators.referenceProperty<UpgradeRefToCustomSqlUpdated, 'ref'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeCustomSqlUpdated,
    })
    readonly ref: Reference<UpgradeCustomSqlUpdated>;
}
