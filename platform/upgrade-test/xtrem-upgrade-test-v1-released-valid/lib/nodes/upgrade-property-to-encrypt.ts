import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.node<UpgradePropertyToEncrypt>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class UpgradePropertyToEncrypt extends Node {
    @decorators.stringProperty<UpgradePropertyToEncrypt, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<UpgradePropertyToEncrypt, 'encryptedValue'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly encryptedValue: Promise<string>;
}
