import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.node<UpgradeReloadFromCsv>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isSetupNode: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class UpgradeReloadFromCsv extends Node {
    @decorators.stringProperty<UpgradeReloadFromCsv, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
        isFrozen: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<UpgradeReloadFromCsv, 'val'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly val: Promise<string>;
}
