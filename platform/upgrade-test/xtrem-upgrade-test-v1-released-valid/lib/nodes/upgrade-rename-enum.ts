import { decorators, EnumDataType, Node } from '@sage/xtrem-core';
import { testUpgradeEnumDataType, TestUpgradeEnumType } from '../enums/test-upgrade-enum-type';

enum EnumV1Enum {
    value1V1,
    value2V1,
    value3V1,
    value4V1,
}

type EnumV1EnumType = keyof typeof EnumV1Enum;

const enumV1Datatype = new EnumDataType<EnumV1EnumType>({
    enum: EnumV1Enum,
    filename: `v1${__filename}`,
});

/**
 * V1 -> V1(released) differences :
 * - no difference
 */
@decorators.node<UpgradeRenameEnum>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeRenameEnum extends Node {
    @decorators.enumProperty<UpgradeRenameEnum, 'enumTest'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => testUpgradeEnumDataType,
    })
    readonly enumTest: Promise<TestUpgradeEnumType | null>;

    @decorators.enumProperty<UpgradeRenameEnum, 'enumTestBis'>({
        isStored: true,
        isPublished: true,
        dataType: () => enumV1Datatype,
    })
    readonly enumTestBis: Promise<EnumV1EnumType>;

    @decorators.enumProperty<UpgradeRenameEnum, 'enumTestDatatype'>({
        isStored: true,
        isPublished: true,
        dataType: () => enumV1Datatype,
    })
    readonly enumTestDatatype: Promise<EnumV1EnumType>;
}
