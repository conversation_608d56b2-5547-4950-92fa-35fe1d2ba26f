/**
 * V1 -> V1(released) differences :
 * - no difference
 */
import {
    BinaryStream,
    date,
    dateRange,
    datetime,
    datetimeRange,
    decimal,
    DecimalDataType,
    decimalRange,
    decorators,
    integer,
    integerRange,
    JsonDataType,
    Node,
    short,
    StringArrayDataType,
    StringDataType,
    TextStream,
    TextStreamDataType,
    Time,
    Uuid,
} from '@sage/xtrem-core';
import {
    upgradeDataTypesEnumForArrayDatatype,
    UpgradeDatatypesEnumForArrayType,
} from '../enums/upgrade-datatypes-enum-for-array-type';
import { upgradeDataTypesEnumDatatype, UpgradeDatatypesEnumType } from '../enums/upgrade-datatypes-enum-type';

export interface UpgradeDatatypesJson {
    a?: number;
    b?: string;
}

export const jsonDataType1 = new JsonDataType<unknown, UpgradeDatatypesJson>();

export const descriptionDataType = new StringDataType({ maxLength: 250 });

export const defaultDecimalDataType = new DecimalDataType({ precision: 9, scale: 3 });

export const descriptionArrayDataType = new StringArrayDataType({ maxLength: 250 });

const testTextStreamType = new TextStreamDataType({
    maxLength: 32768,
    allowedContentTypes: ['text/xml', 'text/html', 'text/plain'],
});

const mailTemplateType = new TextStreamDataType({
    allowedContentTypes: ['text/plain', 'text/html'],
    dangerouslyUnsafe: true,
});

@decorators.node<UpgradeDatatypes>({
    isPublished: true,
    storage: 'sql',
})
export class UpgradeDatatypes extends Node {
    @decorators.integerProperty<UpgradeDatatypes, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.booleanProperty<UpgradeDatatypes, 'booleanVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly booleanVal: Promise<boolean | null>;

    @decorators.shortProperty<UpgradeDatatypes, 'shortVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly shortVal: Promise<short | null>;

    @decorators.integerProperty<UpgradeDatatypes, 'integerVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly integerVal: Promise<integer | null>;

    @decorators.integerRangeProperty<UpgradeDatatypes, 'integerRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly integerRangeVal: Promise<integerRange | null>;

    @decorators.decimalRangeProperty<UpgradeDatatypes, 'decimalRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly decimalRangeVal: Promise<decimalRange | null>;

    @decorators.enumProperty<UpgradeDatatypes, 'enumVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => upgradeDataTypesEnumDatatype,
    })
    readonly enumVal: Promise<UpgradeDatatypesEnumType | null>;

    @decorators.stringProperty<UpgradeDatatypes, 'stringVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly stringVal: Promise<string>;

    @decorators.decimalProperty<UpgradeDatatypes, 'decimalVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly decimalVal: Promise<decimal>;

    @decorators.floatProperty<UpgradeDatatypes, 'floatVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly floatVal: Promise<number | null>;

    @decorators.doubleProperty<UpgradeDatatypes, 'doubleVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly doubleVal: Promise<number | null>;

    @decorators.dateProperty<UpgradeDatatypes, 'dateVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateVal: Promise<date | null>;

    @decorators.dateRangeProperty<UpgradeDatatypes, 'dateRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateRangeVal: Promise<dateRange | null>;

    @decorators.datetimeRangeProperty<UpgradeDatatypes, 'datetimeRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeRangeVal: Promise<datetimeRange | null>;

    @decorators.timeProperty<UpgradeDatatypes, 'timeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly timeVal: Promise<Time | null>;

    @decorators.datetimeProperty<UpgradeDatatypes, 'datetimeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeVal: Promise<datetime | null>;

    @decorators.binaryStreamProperty<UpgradeDatatypes, 'binaryStream'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly binaryStream: Promise<BinaryStream | null>;

    @decorators.textStreamProperty<UpgradeDatatypes, 'textStream'>({
        isPublished: true,
        isStored: true,
        dataType: () => testTextStreamType,
    })
    readonly textStream: Promise<TextStream>;

    @decorators.textStreamProperty<UpgradeDatatypes, 'mailTemplate'>({
        isPublished: true,
        isStored: true,
        dataType: () => mailTemplateType,
    })
    readonly mailTemplate: Promise<TextStream>;

    @decorators.uuidProperty<UpgradeDatatypes, 'uuidVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly uuidVal: Promise<Uuid | null>;

    @decorators.jsonProperty<UpgradeDatatypes, 'jsonVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => jsonDataType1,
    })
    readonly jsonVal: Promise<UpgradeDatatypesJson | null>;

    @decorators.integerArrayProperty<UpgradeDatatypes, 'integerArrayVal'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
    })
    readonly integerArrayVal: Promise<integer[] | null>;

    @decorators.enumArrayProperty<UpgradeDatatypes, 'enumArrayVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => upgradeDataTypesEnumForArrayDatatype,
    })
    readonly enumArrayVal: Promise<UpgradeDatatypesEnumForArrayType[] | null>;

    @decorators.stringArrayProperty<UpgradeDatatypes, 'stringArrayVal'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionArrayDataType,
    })
    readonly stringArrayVal: Promise<string[] | null>;
}
