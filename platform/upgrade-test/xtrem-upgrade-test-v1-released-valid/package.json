{"name": "@sage/xtrem-upgrade-test-v1-released-valid", "description": "Package to test HOT upgrades (v1 released - valid hot-upgrade)", "version": "1.1.0", "xtrem": {"packageName": "@sage/xtrem-upgrade-test", "isReleased": true, "queue": "upgrade-test"}, "keywords": ["xtrem-application-package"], "author": "Sage", "license": "UNLICENSED", "private": true, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "files": ["build", "data", "routing.json"], "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-authorization": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-interop": "workspace:*", "@sage/xtrem-metadata": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-upload": "workspace:*", "eslint": "^8.49.0"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@types/node": "^22.10.2", "eslint": "^8.49.0"}, "scripts": {"build": "xtrem compile --skip-api-client", "build:binary": "xtrem compile --binary --skip-api-client", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "lint": "eslint -c .eslintrc.js --ext .ts lib", "loadData": "xtrem layers --load setup,test", "start": "xtrem start", "test": "xtrem test --unit --layers=test", "test:ci": "xtrem test --unit --ci --layers=test", "upgrade:hot": "xtrem -- upgrade --hot", "upgrade:hot:ci": "xtrem -- upgrade --hot --metrics=local", "xtrem": "xtrem"}}