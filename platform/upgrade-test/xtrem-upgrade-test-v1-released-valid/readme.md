This package is used to unit-test the upgrade process

There are 6 packages involved in the unit-tests of the upgrade process:

-   xtrem-upgrade-test-v1-base: the package in version 0.
-   xtrem-upgrade-test-v1: node-extensions for xtrem-upgrade-test-v1.
-   xtrem-upgrade-test-v2-base: the package in version 2 (used to test the upgrade from v1 to v2).
-   xtrem-upgrade-test-v2: node-extensions for xtrem-upgrade-test-v2.
-   xtrem-upgrade-test-v1-released-valid: same as xtrem-upgrade-test-v1 but with the isReleased flag set in package.json
    and only upgrade actions that are allowed on a released package
-   xtrem-upgrade-test-v1-released-invalid: same as xtrem-upgrade-test-v1 but with the isReleased flag set in package.json
    and only upgrade actions that are forbidden on a released package
