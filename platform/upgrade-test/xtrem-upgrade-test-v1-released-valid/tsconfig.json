{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "lib/**/*", "test/**/*.ts", "test/**/*.json"], "exclude": ["**/*.feature"], "references": [{"path": "../../system/xtrem-authorization"}, {"path": "../../cli/xtrem-cli"}, {"path": "../../back-end/xtrem-core"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-decimal"}, {"path": "../../system/xtrem-interop"}, {"path": "../../system/xtrem-metadata"}, {"path": "../../shared/xtrem-shared"}, {"path": "../../system/xtrem-system"}, {"path": "../../system/xtrem-upload"}, {"path": "../../back-end/eslint-plugin-xtrem"}]}