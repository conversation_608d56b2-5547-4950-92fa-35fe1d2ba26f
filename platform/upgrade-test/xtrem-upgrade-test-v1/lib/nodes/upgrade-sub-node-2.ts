import { decorators, StringDataType } from '@sage/xtrem-core';
import * as v1Pckg from '@sage/xtrem-upgrade-test-v1-base';

@decorators.subNode<UpgradeSubNode2>({
    extends: () => v1Pckg.nodes.UpgradeBaseNode,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
})
export class UpgradeSubNode2 extends v1Pckg.nodes.UpgradeBaseNode {
    @decorators.stringProperty<UpgradeSubNode2, 'strSub2'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strSub2: Promise<string>;
}
