import { decorators, NodeExtension, StringDataType } from '@sage/xtrem-core';
import * as v1Pckg from '@sage/xtrem-upgrade-test-v1-base';

@decorators.nodeExtension<UpgradeNodeToExtendExtension>({
    extends: () => v1Pckg.nodes.UpgradeNodeToExtend,
})
export class UpgradeNodeToExtendExtension extends NodeExtension<v1Pckg.nodes.UpgradeNodeToExtend> {
    @decorators.stringProperty<UpgradeNodeToExtendExtension, 'name2'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name2: Promise<string>;
}
declare module '@sage/xtrem-upgrade-test-v1-base/lib/nodes/upgrade-node-to-extend' {
    export interface UpgradeNodeToExtend extends UpgradeNodeToExtendExtension {}
}
