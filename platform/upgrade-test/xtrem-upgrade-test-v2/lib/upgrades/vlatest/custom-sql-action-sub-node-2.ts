import { UnsafeCustomSqlAction } from '@sage/xtrem-system';
import { UpgradeSubNode2 } from '../../nodes/upgrade-sub-node-2';

export const customSqlActionSubNode2 = new UnsafeCustomSqlAction({
    description: 'Migrate upgrade_sub_node_2 (fill int_sub_2)',
    tableNamesToRenameAndDrop: ['upgrade_sub_node_2'],
    fixes: {
        tables: ['upgrade_sub_node_2'],
    },
    body: async helper => {
        const systemColumns = helper.getSystemColumnNames(UpgradeSubNode2);
        await helper.executeSql(
            `INSERT
                INTO
                ${helper.schemaName}.upgrade_sub_node_2 ( 
                ${systemColumns},
                str_sub_2,
                int_sub_2)
            SELECT
                ${systemColumns},
                str_sub_2,
                2
            FROM
                ${helper.schemaName}.temp_upgrade_sub_node_2`,
        );
    },
});
