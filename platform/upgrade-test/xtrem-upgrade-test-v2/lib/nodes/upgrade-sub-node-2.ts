import { decorators, integer, StringDataType } from '@sage/xtrem-core';
import * as v2Pckg from '@sage/xtrem-upgrade-test-v2-base';

@decorators.subNode<UpgradeSubNode2>({
    extends: () => v2Pckg.nodes.UpgradeBaseNode,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
})
export class UpgradeSubNode2 extends v2Pckg.nodes.UpgradeBaseNode {
    @decorators.stringProperty<UpgradeSubNode2, 'strSub2'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strSub2: Promise<string>;

    @decorators.integerProperty<UpgradeSubNode2, 'intSub2'>({
        isStored: true,
        isPublished: true,
    })
    readonly intSub2: Promise<integer>;
}
