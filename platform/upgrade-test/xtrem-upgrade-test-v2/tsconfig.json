{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "lib/**/*", "test/**/*.ts", "test/**/*.json"], "exclude": ["**/*.feature"], "references": [{"path": "../../system/xtrem-authorization"}, {"path": "../../cli/xtrem-cli"}, {"path": "../../back-end/xtrem-core"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-decimal"}, {"path": "../../system/xtrem-interop"}, {"path": "../../system/xtrem-metadata"}, {"path": "../../system/xtrem-system"}, {"path": "../xtrem-upgrade-test-v2-base"}, {"path": "../../back-end/eslint-plugin-xtrem"}]}