import { ServiceOption } from '@sage/xtrem-core';
import { upgradeServiceOption1 } from './upgrade-service-option-1';
import { upgradeServiceOption2 } from './upgrade-service-option-2';
import { upgradeServiceOption3 } from './upgrade-service-option-3';
import { upgradeServiceOptionNew1 } from './upgrade-service-option-new-1';

export const upgradeServiceOption4 = new ServiceOption({
    __filename,
    status: 'released',
    description: 'upgrade service option 4',
    isSubscribable: false,
    isHidden: false,
    activates: () => [upgradeServiceOption1, upgradeServiceOption2, upgradeServiceOption3, upgradeServiceOptionNew1],
});
