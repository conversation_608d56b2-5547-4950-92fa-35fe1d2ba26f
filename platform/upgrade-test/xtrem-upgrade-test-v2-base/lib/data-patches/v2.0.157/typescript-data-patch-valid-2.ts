import { Context } from '@sage/xtrem-core';
import { TypescriptDataPatch } from '@sage/xtrem-system';
import { NodeForDataPatches } from '../../nodes/node-for-data-patches';

class TypescriptDataPatchValid2 extends TypescriptDataPatch {
    constructor() {
        super('v2.0.157-ts-2');
    }

    // eslint-disable-next-line class-methods-use-this
    protected async execute(context: Context): Promise<void> {
        const nodes = context.query(NodeForDataPatches, {
            forUpdate: true,
        });
        await nodes.forEach(async node => {
            if ((await node.val2) !== node._id + 1)
                throw new Error(
                    `typescript-data-patch-valid-1 was not executed correctly on node ${
                        node._id
                    }. ${await node.val2} != ${node._id + 1}`,
                );
            await node.$.set({ val2: 3 });
            await node.$.save();
        });
    }
}

export const typescriptDataPatchValid2 = new TypescriptDataPatchValid2();
