import { SqlDataPatch, SqlRunner } from '@sage/xtrem-system';

class SqlDataPatchValid extends SqlDataPatch {
    constructor() {
        super('v2.0.157-sql');
    }

    // eslint-disable-next-line class-methods-use-this
    protected execute(sqlRunner: SqlRunner): Promise<void> {
        return sqlRunner.execute(`UPDATE ${sqlRunner.schemaName}.node_for_data_patches SET val_1=_id * 2`);
    }
}

export const sqlDataPatchValid = new SqlDataPatchValid();
