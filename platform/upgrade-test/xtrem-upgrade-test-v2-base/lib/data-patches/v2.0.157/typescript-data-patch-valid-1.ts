import { Context } from '@sage/xtrem-core';
import { TypescriptDataPatch } from '@sage/xtrem-system';
import { NodeForDataPatches } from '../../nodes/node-for-data-patches';

class TypescriptDataPatchValid1 extends TypescriptDataPatch {
    constructor() {
        super('v2.0.157-ts-1');
    }

    // eslint-disable-next-line class-methods-use-this
    protected async execute(context: Context): Promise<void> {
        const nodes = context.query(NodeForDataPatches, {
            forUpdate: true,
        });
        await nodes.forEach(async node => {
            await node.$.set({ val2: node._id + 1 });
            await node.$.save();
        });
    }
}

export const typescriptDataPatchValid1 = new TypescriptDataPatchValid1();
