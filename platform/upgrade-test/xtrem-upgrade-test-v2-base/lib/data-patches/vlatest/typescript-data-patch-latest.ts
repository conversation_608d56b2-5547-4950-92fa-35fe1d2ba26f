import { Context } from '@sage/xtrem-core';
import { TypescriptDataPatch } from '@sage/xtrem-system';
import { NodeForDataPatches } from '../../nodes/node-for-data-patches';

class TypescriptDataPatchLatest extends TypescriptDataPatch {
    constructor() {
        super('latest-ts');
    }

    // eslint-disable-next-line class-methods-use-this
    protected async execute(context: Context): Promise<void> {
        const nodes = context.query(NodeForDataPatches, {
            forUpdate: true,
        });
        await nodes.forEach(async node => {
            await node.$.set({ val4: node._id * 5 });
            await node.$.save();
        });
    }
}

export const typescriptDataPatchLatest = new TypescriptDataPatchLatest();
