import { SqlDataPatch, SqlRunner } from '@sage/xtrem-system';

class SqlDataPatchLatest extends SqlDataPatch {
    constructor() {
        super('latest-sql');
    }

    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    protected execute(sqlRunner: SqlRunner): Promise<void> {
        return sqlRunner.execute(`UPDATE ${sqlRunner.schemaName}.node_for_data_patches SET val_3=_id * 4`);
    }
}

export const sqlDataPatchLatest = new SqlDataPatchLatest();
