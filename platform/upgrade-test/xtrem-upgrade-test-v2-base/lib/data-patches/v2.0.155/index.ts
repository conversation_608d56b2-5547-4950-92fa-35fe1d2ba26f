import { Context } from '@sage/xtrem-core';
import { <PERSON><PERSON>ata<PERSON><PERSON>, SqlData<PERSON>atch, Sql<PERSON><PERSON><PERSON>, TypescriptDataPatch } from '@sage/xtrem-system';

/**
 * This module should not be loaded as the application's version is set to 1.5.0 before running the data-patches
 */

class InvalidSqlDataPatch extends SqlDataPatch {
    constructor() {
        super('invalid-sql');
    }

    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars, require-await
    protected async execute(sqlRunner: SqlRunner): Promise<void> {
        throw new Error('This data patch should not be invoked');
    }
}

class InvalidTypescriptDataPatch extends TypescriptDataPatch {
    constructor() {
        super('invalid-ts', false);
    }

    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars, require-await
    protected async execute(context: Context): Promise<void> {
        throw new Error('This data patch should not be invoked');
    }
}

export const dataPatches: BaseDataPatch[] = [new InvalidSqlDataPatch(), new InvalidTypescriptDataPatch()];

throw new Error('This module should not be loaded');
