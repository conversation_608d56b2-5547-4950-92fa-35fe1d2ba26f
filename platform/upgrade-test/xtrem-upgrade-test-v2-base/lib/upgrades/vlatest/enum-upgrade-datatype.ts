import { SchemaEnumPropertyDatatypeUpgradeAction } from '@sage/xtrem-system';
import { UpgradeRenameEnum } from '../../nodes/upgrade-rename-enum';

export const enumUpgradeDatatype = new SchemaEnumPropertyDatatypeUpgradeAction({
    description: 'Upgrade UpgradeRenameEnum.enumTestDatatype',
    node: () => UpgradeRenameEnum,
    propertyName: 'enumTestDatatype',
    valuesMapping: {
        value1V1: 'value1V2Datatype',
        value2V1: 'value2V2Datatype',
        value3V1: 'value3V2Datatype',
        value4V1: 'value4V2Datatype',
    },
});
