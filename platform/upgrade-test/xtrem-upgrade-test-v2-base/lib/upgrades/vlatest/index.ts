import {
    ReloadSetupCsvAction,
    SchemaAllowStringToIntegerAction,
    SchemaDropTableAction,
    SchemaRenameNodeAction,
    UpgradeSuite,
} from '@sage/xtrem-system';
import {
    UpgradeAbstractNode2,
    UpgradeAlterColumns,
    UpgradeReloadCsvVendorChild,
    UpgradeReloadCsvVendorParent,
    UpgradeReloadFromCsv,
    UpgradeReloadFromVendorCsv,
    UpgradeRenameVitalChildV2,
} from '../../nodes';
import { allowShorterString } from './allow-shorter-string';
import { alterColumnsIntegerToReference } from './alter-columns-integer-to-reference';
import { customSqlActionBaseNode } from './custom-sql-action-base-node';
import { customSqlActionSubNode1 } from './custom-sql-action-sub-node-1';
import { customSqlActionUpdated } from './custom-sql-action-updated';
import { customSqlActionV1ToV2 } from './custom-sql-action-v1-to-v2';
import { dropUpgradeBaseNode2 } from './drop-upgrade-base-node-2';
import { dropUpgradeBaseNodeToDelete } from './drop-upgrade-base-node-to-delete';
import { dropUpgradeDeleteTable } from './drop-upgrade-delete-table';
import { dropUpgradeDeleteTableWithAttachments } from './drop-upgrade-delete-table-with-attachments';
import { dropUpgradeDeleteTableWithBaseNode } from './drop-upgrade-delete-table-with-base-node';
import { enumUpgrade } from './enum-upgrade';
import { enumUpgradeDatatype } from './enum-upgrade-datatype';
import { manageAttachmentsOfDroppedTable } from './manage-attachments-of-dropped-table';
import { renameAbstractNodeAndUpdateConstructor } from './rename-abstract-node-update-constructor';
import { renameColumns1, renameColumns2, renameColumns3 } from './rename-columns-schema';
import { renameNodeAndUpdateConstructor } from './rename-node-update-constructor';
import { renameTable, renameTableWithNotify } from './rename-table-schema';
import { renameUpgradeRenameTableWithAttachments } from './rename-upgrade-rename-table-with-attachments';
import { upgradeRenamedEnumMembers, upgradeRenamedEnumV1ToV2 } from './upgrade-renamed-enum-v1-to-v2';

export const upgradeSuite = new UpgradeSuite({
    actions: [
        customSqlActionV1ToV2,
        customSqlActionUpdated,
        renameTable,
        renameAbstractNodeAndUpdateConstructor,
        renameNodeAndUpdateConstructor,
        renameTableWithNotify,
        renameUpgradeRenameTableWithAttachments,
        renameColumns1,
        renameColumns2,
        renameColumns3,
        enumUpgrade,
        enumUpgradeDatatype,
        customSqlActionBaseNode,
        customSqlActionSubNode1,
        alterColumnsIntegerToReference,
        dropUpgradeBaseNodeToDelete,
        dropUpgradeDeleteTable,
        dropUpgradeDeleteTableWithBaseNode,
        dropUpgradeBaseNode2,
        dropUpgradeDeleteTableWithAttachments,
        upgradeRenamedEnumV1ToV2,
        upgradeRenamedEnumMembers,
        allowShorterString,
        manageAttachmentsOfDroppedTable,
        new SchemaDropTableAction({ tableName: 'upgrade_custom_sql_v_1' }),
        // new EncryptPropertiesAction({
        //     node: () => UpgradePropertyToEncrypt,
        //     properties: ['encryptedValue'],
        // }),
        new SchemaRenameNodeAction({
            node: () => UpgradeRenameVitalChildV2,
            oldNodeName: 'UpgradeRenameVitalChildV1',
        }),
        new SchemaRenameNodeAction({
            node: () => UpgradeAbstractNode2,
            oldNodeName: 'UpgradeAbstractNode1',
        }),
        new ReloadSetupCsvAction({
            nodes: [
                () => UpgradeReloadFromCsv,
                () => UpgradeReloadFromVendorCsv,
                () => UpgradeReloadCsvVendorParent,
                () => UpgradeReloadCsvVendorChild,
            ],
        }),
        new SchemaAllowStringToIntegerAction({
            node: () => UpgradeAlterColumns,
            propertyName: 'stringToInteger',
        }),
    ],
});
