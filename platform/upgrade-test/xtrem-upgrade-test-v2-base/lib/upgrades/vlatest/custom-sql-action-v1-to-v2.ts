import { CustomSqlAction } from '@sage/xtrem-system';
import { UpgradeCustomSqlV2 } from '../../nodes/upgrade-custom-sql-v2';

export const customSqlActionV1ToV2 = new CustomSqlAction({
    description: 'Migrate from upgrade_custom_sql_v_1 to upgrade_custom_sql_v_2',
    fixes: {
        tables: ['upgrade_custom_sql_v_2'],
    },
    body: async helper => {
        // Table upgrade_custom_sql_v_1 was created in v1
        // Table upgrade_custom_sql_v_2 was created in v2. This action initializes the upgrade_custom_sql_v_2 table
        // from the data in table 'upgrade_custom_sql_v_1'

        // Remove the _id from the system columns as it will be specifically set in the query
        const systemColumns = helper.getSystemColumnNames(UpgradeCustomSqlV2).replace(/,_id,/, ',');
        await helper.executeSql(
            `INSERT
                INTO
                ${helper.schemaName}.upgrade_custom_sql_v_2 (
                ${systemColumns},
                _id,
                string_2,
                int_2,
                bool_2)
            SELECT
                ${systemColumns},
                _id + 5,
                'V2' || string_1,
                int_1 * 2,
                NOT(bool_1)
            FROM
                ${helper.schemaName}.upgrade_custom_sql_v_1`,
        );
    },
});
