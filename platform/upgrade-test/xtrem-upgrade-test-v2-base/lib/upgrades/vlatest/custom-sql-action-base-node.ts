import { UnsafeCustomSqlAction } from '@sage/xtrem-system';
import { UpgradeBaseNode } from '../../nodes/upgrade-base-node';

export const customSqlActionBaseNode = new UnsafeCustomSqlAction({
    description: 'Migrate upgrade_base_node (fill int_base)',
    tableNamesToRenameAndDrop: ['upgrade_base_node'],
    fixes: {
        tables: ['upgrade_base_node'],
    },
    body: async helper => {
        const systemColumns = helper.getSystemColumnNames(UpgradeBaseNode);
        await helper.executeSql(
            `INSERT
                INTO
                ${helper.schemaName}.upgrade_base_node ( 
                ${systemColumns},
                _constructor,
                str_base,
                int_base)
            SELECT
                ${systemColumns},
                _constructor,
                str_base,
                3
            FROM
                ${helper.schemaName}.temp_upgrade_base_node`,
        );
    },
});
