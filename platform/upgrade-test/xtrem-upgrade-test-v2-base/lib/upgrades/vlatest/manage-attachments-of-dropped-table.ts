import { CustomSqlAction } from '@sage/xtrem-system';

export const manageAttachmentsOfDroppedTable = new CustomSqlAction({
    description: 'Drop the attachements bound to obsolete table UpgradeDeleteTableWithAttachments',
    fixes: {
        attachments: [
            {
                table: 'upgrade_delete_table_with_attachments',
            },
        ],
    },
    body: async helper => {
        await helper.executeSql(`DELETE FROM ${helper.schemaName}.attachment_association WHERE source_node_name = $1`, [
            'UpgradeDeleteTableWithAttachments',
        ]);
    },
});
