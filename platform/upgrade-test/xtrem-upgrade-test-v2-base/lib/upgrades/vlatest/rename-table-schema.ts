import { SchemaRenameNodeAction } from '@sage/xtrem-system';
import { UpgradeRenameTableV2, UpgradeRenameTableWithNotifyV2 } from '../../nodes';

export const renameTable = new SchemaRenameNodeAction({
    node: () => UpgradeRenameTableV2,
    oldNodeName: 'UpgradeRenameTableV1',
});

export const renameTableWithNotify = new SchemaRenameNodeAction({
    node: () => UpgradeRenameTableWithNotifyV2,
    oldNodeName: 'UpgradeRenameTableWithNotifyV1',
});
