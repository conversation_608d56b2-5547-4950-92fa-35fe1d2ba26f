import { CustomSqlAction } from '@sage/xtrem-system';

export const fillNewEnumProperty = new CustomSqlAction({
    description: 'Custom action on new enum property',
    fixes: {
        notNullableColumns: [{ table: 'upgrade_rename_enum', column: 'new_enum_prop' }],
    },
    body: async helper => {
        await helper.executeSql(`UPDATE ${helper.schemaName}.upgrade_rename_enum SET new_enum_prop = 'value1'`);
    },
});
