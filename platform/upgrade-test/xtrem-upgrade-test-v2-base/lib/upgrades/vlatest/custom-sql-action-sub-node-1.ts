import { UnsafeCustomSqlAction } from '@sage/xtrem-system';
import { UpgradeSubNode1 } from '../../nodes/upgrade-sub-node-1';

export const customSqlActionSubNode1 = new UnsafeCustomSqlAction({
    description: 'Migrate upgrade_sub_node_1 (fill int_sub_1)',
    tableNamesToRenameAndDrop: ['upgrade_sub_node_1'],
    fixes: {
        tables: ['upgrade_sub_node_1'],
    },
    body: async helper => {
        const systemColumns = helper.getSystemColumnNames(UpgradeSubNode1);
        await helper.executeSql(
            `INSERT
                INTO
                ${helper.schemaName}.upgrade_sub_node_1 ( 
                ${systemColumns},
                str_sub_1,
                int_sub_1)
            SELECT
                ${systemColumns},
                str_sub_1,
                1
            FROM
                ${helper.schemaName}.temp_upgrade_sub_node_1`,
        );
    },
});
