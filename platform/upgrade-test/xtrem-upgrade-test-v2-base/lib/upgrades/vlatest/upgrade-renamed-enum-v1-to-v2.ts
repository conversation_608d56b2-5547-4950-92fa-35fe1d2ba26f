import { SchemaEnumRenameAction, SchemaEnumUpgradeAction } from '@sage/xtrem-system';
import { upgradeRenamedEnumV2DataType } from '../../enums/upgrade-renamed-enum-v-2-type';

export const upgradeRenamedEnumV1ToV2 = new SchemaEnumRenameAction({
    description: 'Rename enum upgradeRenamedEnumV1 to upgradeRenamedEnumV2',
    oldEnumName: 'upgrade_renamed_enum_v_1_type_enum',
    dataType: upgradeRenamedEnumV2DataType,
});

export const upgradeRenamedEnumMembers = new SchemaEnumUpgradeAction({
    description: 'Upgrade members of enum upgradeRenamedEnumV2',
    dataType: upgradeRenamedEnumV2DataType,
    // mappings:
    //   value1V1 -> value1V2
    //   value2V1 -> (removed)
    //   value3V1 -> value3V1 (untouched)
    //   value4V2 (added, managed automatically by the upgrade engine)

    valuesMapping: {
        value1V1: 'value1V2',
        value3V1: 'value3V1',
    },
    membersToDelete: {
        value2V1: 'value4V2', // replace value 'value2V1' with 'value4V2' for existing rows
    },
});
