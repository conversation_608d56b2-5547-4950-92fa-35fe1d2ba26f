import { CustomSqlAction } from '@sage/xtrem-system';

export const customSqlActionUpdated = new CustomSqlAction({
    description: 'Migrate within upgrade_custom_sql_updated',
    fixes: {
        tables: ['upgrade_custom_sql_updated'],
    },
    body: async helper => {
        await helper.executeSql(
            `UPDATE ${helper.schemaName}.upgrade_custom_sql_updated
                SET 
                    str_prop = 'updated_' || str_prop,
                    int_prop = int_prop * 3,
                    bool_prop = NOT(bool_prop),
                    ref_prop = ref_prop + 5`,
        );
    },
});
