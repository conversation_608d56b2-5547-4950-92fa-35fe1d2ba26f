import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import { UpgradeRenameColumns, UpgradeRenameTableV2 } from '../../nodes';

export const renameColumns1 = new SchemaRenamePropertyAction({
    node: () => UpgradeRenameColumns,
    oldPropertyName: 'nameTemp', // This property was already renamed from 'nameV1' to 'nameTemp' in upgrade 1.0.2
    newPropertyName: 'nameV2',
});

export const renameColumns2 = new SchemaRenamePropertyAction({
    node: () => UpgradeRenameTableV2,
    oldPropertyName: 'name',
    newPropertyName: 'name2',
});

export const renameColumns3 = new SchemaRenamePropertyAction({
    node: () => UpgradeRenameColumns,
    oldPropertyName: 'ref1',
    newPropertyName: 'ref2',
});
