import { SchemaEnumUpgradeAction } from '@sage/xtrem-system';
import { testUpgradeEnumDataType } from '../../enums/test-upgrade-enum-type';

export const enumUpgrade = new SchemaEnumUpgradeAction({
    description: 'Upgrade enum UpgradeEnum',
    dataType: testUpgradeEnumDataType,
    /**
     * V1 -> V2 changes
     *
     * value1V1 -> value1V2
     * value2V1 -> value2V2
     * value3V1 -> value3V2
     * value4V1 -> (deleted)
     * value5V1 -> (deleted)
     * value6V1 -> value6V1 (untouched)
     * value7V1 -> (deleted)
     *          -> value21V2 (added)
     *          -> value31V2 (added)
     */
    valuesMapping: {
        value1V1: 'value1V2',
        value2V1: 'value2V2',
        value3V1: 'value3V2',
        value6V1: 'value6V1',
    },
    membersToDelete: {
        value4V1: 'value1V2', // existing values in db equals to 'value4V1' will be replaced with 'value1V2'
        value5V1: null, // no replacement
        value7V1: 'value6V1', // existing values in db equals to 'value7V1' will be replaced with 'value6V1'
    },
});
