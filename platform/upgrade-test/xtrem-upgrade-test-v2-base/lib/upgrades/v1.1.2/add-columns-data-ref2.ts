import { CustomSqlAction } from '@sage/xtrem-system';

export const addColumnsDataRef2 = new CustomSqlAction({
    description: 'upgradeAddColumns.ref2 SqlAction: data upgrade (1.1.2->)',
    fixes: {
        notNullableColumns: [{ table: 'upgrade_add_columns', column: 'ref_2' }],
    },
    body: async helper => {
        await helper.executeSql(`UPDATE ${helper.schemaName}.upgrade_add_columns SET ref_2 = 2 WHERE ref_2 IS NULL`);
    },
});
