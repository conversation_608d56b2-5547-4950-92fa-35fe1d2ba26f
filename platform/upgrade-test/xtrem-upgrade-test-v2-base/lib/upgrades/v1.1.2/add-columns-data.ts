import { DataUpdateAction } from '@sage/xtrem-system';
import { UpgradeAddColumns } from '../../nodes';

export const addColumnsData = new DataUpdateAction({
    description: 'addColumnsData: data upgrade (1.1.2->)',
    node: () => UpgradeAddColumns,
    /* istanbul ignore next */
    async where() {
        return (await this.name1) !== 'ooo';
    },
    set: {
        // Note: no update for newMandatoryStringWithDefaultValue as it will be managed by an
        // automatic data action (a defaultValue() is provided)
        async newMandatoryString() {
            return `new ${await this.name1}`;
        },
        newNullableInteger: null,
    },
});
