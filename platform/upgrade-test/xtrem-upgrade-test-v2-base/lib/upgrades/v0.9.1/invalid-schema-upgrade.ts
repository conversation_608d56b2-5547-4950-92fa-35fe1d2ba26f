/* eslint-disable class-methods-use-this */
import { SchemaUpgradeAction } from '@sage/xtrem-system';
import { UpgradeAddColumns } from '../../nodes';

// This upgrade should not be used as we will only have to upgrade from v1.0.0 to v2.0.0
class InvalidSchemaUpgrade extends SchemaUpgradeAction<UpgradeAddColumns> {
    constructor() {
        super({ node: () => UpgradeAddColumns, description: 'Invalid upgrade' });
        throw new Error('ERROR: this schema upgrade should not be invoked');
    }

    override _preExecute(): Promise<void> {
        throw new Error('ERROR: this schema upgrade should not be invoked');
    }

    _execute(): Promise<void> {
        throw new Error('ERROR: this schema upgrade should not be invoked');
    }

    override _postExecute(): Promise<void> {
        throw new Error('ERROR: this schema upgrade should not be invoked');
    }
}

export const invalidSchemaUpgrade = new InvalidSchemaUpgrade();
