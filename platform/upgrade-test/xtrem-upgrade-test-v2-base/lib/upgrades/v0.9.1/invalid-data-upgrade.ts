/* eslint-disable class-methods-use-this */
import { DataUpdateAction } from '@sage/xtrem-system';
import { UpgradeAddColumns } from '../../nodes';

// This upgrade should not be used as we will only have to upgrade from v1.0.0 to v2.0.0
export const invalidDataUpgrade = new DataUpdateAction({
    description: 'invalidDataUpgrade: data upgrade (1.1.2->)',
    node: () => UpgradeAddColumns,
    /* istanbul ignore next */
    async where() {
        return (await this.name1) !== 'ooo';
    },
    set: {
        // Note: no update for newMandatoryStringWithDefaultValue as it will be managed by an
        // automatic data action (a defaultValue() is provided)
        async newMandatoryString() {
            return `new ${await this.name1}`;
        },
        newNullableInteger: null,
    },
});
