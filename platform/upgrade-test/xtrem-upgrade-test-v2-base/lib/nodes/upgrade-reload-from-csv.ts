import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * This node will be used to test the relead data from CSV files
 * There's no schema change between v1 and v2, only changes in data (see CSV file)
 */

@decorators.node<UpgradeReloadFromCsv>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
export class UpgradeReloadFromCsv extends Node {
    @decorators.stringProperty<UpgradeReloadFromCsv, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
        isFrozen: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<UpgradeReloadFromCsv, 'val'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly val: Promise<string>;
}
