import { Collection, decorators, Node, StringDataType } from '@sage/xtrem-core';
import { UpgradeRenameVitalChildV2 } from './upgrade-rename-vital-child';

/**
 * v1->v2 changes:
 * - Reference renamed child node: UpgradeRenameVitalChildV1 -> UpgradeRenameVitalChildV2
 */
@decorators.node<UpgradeRenameVitalParent>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeRenameVitalParent extends Node {
    @decorators.stringProperty<UpgradeRenameVitalParent, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly code: Promise<string>;

    @decorators.collectionProperty<UpgradeRenameVitalParent, 'children'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'parent',
        node: () => UpgradeRenameVitalChildV2,
    })
    readonly children: Collection<UpgradeRenameVitalChildV2>;
}
