import { decorators, Node } from '@sage/xtrem-core';
import {
    TestUpgradeEnumChangeDatatypeType,
    upgradeEnumChangeDatatypeDataType,
} from '../enums/test-upgrade-enum-change-datatype-type';
import { TestUpgradeEnumNewPropType, upgradeEnumNewPropDataType } from '../enums/test-upgrade-enum-new-prop-type';
import { testUpgradeEnumDataType, TestUpgradeEnumType } from '../enums/test-upgrade-enum-type';

/**
 * V1->V2 differences:
 * property enumTestBis: removed
 * property enumTestDatatype: enum(EnumV1EnumType) -> enum(EnumV2EnumType)
 */

@decorators.node<UpgradeRenameEnum>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeRenameEnum extends Node {
    @decorators.enumProperty<UpgradeRenameEnum, 'enumTest'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => testUpgradeEnumDataType,
    })
    readonly enumTest: Promise<TestUpgradeEnumType | null>;

    @decorators.enumProperty<UpgradeRenameEnum, 'enumTestDatatype'>({
        isStored: true,
        isPublished: true,
        dataType: () => upgradeEnumChangeDatatypeDataType,
    })
    readonly enumTestDatatype: Promise<TestUpgradeEnumChangeDatatypeType>;

    @decorators.enumProperty<UpgradeRenameEnum, 'newEnumProp'>({
        isStored: true,
        isPublished: true,
        dataType: () => upgradeEnumNewPropDataType,
    })
    readonly newEnumProp: Promise<TestUpgradeEnumNewPropType>;
}
