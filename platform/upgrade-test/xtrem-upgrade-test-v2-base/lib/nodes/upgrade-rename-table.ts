import { Node, Reference, StringDataType, decorators } from '@sage/xtrem-core';
import { UpgradeReferenced } from './upgrade-referenced';

/**
 * v1->v2 changes:
 * - node renamed: UpgradeRenameTableV1 -> UpgradeRenameTableV2
 * - property name renamed to name2
 */
@decorators.node<UpgradeRenameTableV2>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [{ orderBy: { name2: +1 }, isUnique: true }],
})
export class UpgradeRenameTableV2 extends Node {
    @decorators.stringProperty<UpgradeRenameTableV2, 'name2'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name2: Promise<string>;

    @decorators.referenceProperty<UpgradeRenameTableV2, 'ref'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeReferenced,
    })
    readonly ref: Reference<UpgradeReferenced>;
}

/**
 * v1->v2 changes:
 * - node renamed: UpgradeRenameTableWithNotifyV1 -> UpgradeRenameTableWithNotifyV2
 * - list of notifies changes ['created', 'updated'] -> ['updated', 'deleted']
 */
@decorators.node<UpgradeRenameTableWithNotifyV2>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    notifies: ['updated', 'deleted'],
})
export class UpgradeRenameTableWithNotifyV2 extends Node {
    @decorators.stringProperty<UpgradeRenameTableWithNotifyV2, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name: Promise<string>;
}
