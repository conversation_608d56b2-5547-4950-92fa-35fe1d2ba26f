import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1->V2 differences:
 *  the _constructor value is changed from old concrete node name - [UpdateConstructorWithSubNodeLevel2] (v1)
 *   to new concrete node name [UpdateConstructorWithSubNodeLevel2New] (v2)
 */
@decorators.node<UpdateConstructorWithBaseNode>({
    storage: 'sql',
    isAbstract: true,
})
export class UpdateConstructor<PERSON>ithBaseNode extends Node {
    @decorators.stringProperty<UpdateConstructorWithBaseNode, 'strBase'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strBase: Promise<string>;
}
