import { Node, Reference, StringDataType, decorators } from '@sage/xtrem-core';
import { UpgradeRenameVitalParent } from './upgrade-rename-vital-parent';

/**
 * v1->v2 changes:
 * - node renamed: UpgradeRenameVitalChildV1 -> UpgradeRenameVitalChildV2
 */
@decorators.node<UpgradeRenameVitalChildV2>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isVitalCollectionChild: true,
})
export class UpgradeRenameVitalChildV2 extends Node {
    @decorators.referenceProperty<UpgradeRenameVitalChildV2, 'parent'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => UpgradeRenameVitalParent,
    })
    readonly parent: Reference<UpgradeRenameVitalParent>;

    @decorators.stringProperty<UpgradeRenameVitalChildV2, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly code: Promise<string>;
}
