import { Reference, decorators, Node, StringDataType } from '@sage/xtrem-core';
import { UpgradeReferenced } from './upgrade-referenced';

/**
 * This node will be used to test the relead data from CSV files with factory
 */
@decorators.node<UpgradeReloadFromVendorCsv>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
export class UpgradeReloadFromVendorCsv extends Node {
    @decorators.stringProperty<UpgradeReloadFromVendorCsv, 'id'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<UpgradeReloadFromVendorCsv, 'country'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly country: Promise<string>;

    @decorators.stringProperty<UpgradeReloadFromVendorCsv, 'isOwnedByCustomerProperty'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
        isOwnedByCustomer: true,
    })
    readonly isOwnedByCustomerProperty: Promise<string>;

    @decorators.referenceProperty<UpgradeReloadFromVendorCsv, 'isOwnedByCustomerReference'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeReferenced,
        isOwnedByCustomer: true,
        isNullable: true,
    })
    readonly isOwnedByCustomerReference: Reference<UpgradeReferenced | null>;
}
