import { Node, StringDataType, decorators } from '@sage/xtrem-core';

/**
 * Purpose of the node:
 * - test renaming of a table with attachments (the records in the attachment_association table should be updated)
 * v1->v2 changes:
 * - node renamed: UpgradeRenameTableWithAttachments1 -> UpgradeRenameTableWithAttachments2
 */
@decorators.node<UpgradeRenameTableWithAttachments2>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    hasAttachments: true,
})
export class UpgradeRenameTableWithAttachments2 extends Node {
    @decorators.stringProperty<UpgradeRenameTableWithAttachments2, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name: Promise<string>;
}
