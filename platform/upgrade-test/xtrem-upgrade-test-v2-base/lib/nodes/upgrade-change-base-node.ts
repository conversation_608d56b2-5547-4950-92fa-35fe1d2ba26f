import { decorators, StringDataType } from '@sage/xtrem-core';
import { UpgradeBaseNode } from '.';
import { UpgradeAbstractNode2 } from './upgrade-abstract-node-2';

/**
 * V1->V2 differences:
 * the base node is changed from UpgradeAbstractNode1 (v1) to UpgradeAbstractNode2 (v2)
 */
@decorators.subNode<UpgradeChangeBaseNode>({
    extends: () => UpgradeBaseNode,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
})
export class UpgradeChangeBaseNode extends UpgradeAbstractNode2 {
    @decorators.stringProperty<UpgradeChangeBaseNode, 'strProp'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strProp: Promise<string>;
}
