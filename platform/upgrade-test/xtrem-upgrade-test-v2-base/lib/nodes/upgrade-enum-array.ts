import { decorators, Node } from '@sage/xtrem-core';
import { TestUpgradeEnumArrayType, upgradeEnumArrayDataType } from '../enums/test-upgrade-enum-array-type';

@decorators.node<UpgradeEnumArray>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeEnumArray extends Node {
    @decorators.enumArrayProperty<UpgradeEnumArray, 'enumTest'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => upgradeEnumArrayDataType,
    })
    readonly enumTest: Promise<TestUpgradeEnumArrayType[] | null>;
}
