import { decorators, Node, StringDataType, integer } from '@sage/xtrem-core';

/**
 * V1->V2 differences:
 * property name2: removed
 * property name3: removed
 * property storedToComputed: isStored : true -> false
 * property ref1: removed
 *
 * remove related indexes
 */
@decorators.node<UpgradeDeleteColumns>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [{ orderBy: { name1: 1 }, isUnique: true }],
})
export class UpgradeDeleteColumns extends Node {
    @decorators.stringProperty<UpgradeDeleteColumns, 'name1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name1: Promise<string>;

    @decorators.integerProperty<UpgradeDeleteColumns, 'storedToComputed'>({
        getValue(): integer {
            return 3;
        },
    })
    readonly storedToComputed: Promise<integer>;
}
