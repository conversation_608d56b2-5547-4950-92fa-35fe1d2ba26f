import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1->V2 differences:
 * the base node is changed from UpgradeBaseNode (v1) to Node (v2)
 */
@decorators.node<UpgradeRemoveBaseNode>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
})
export class UpgradeRemoveBaseNode extends Node {
    @decorators.stringProperty<UpgradeRemoveBaseNode, 'strProp'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strProp: Promise<string>;
}
