import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1->V2 differences:
 * property encryptedValue: string -> encrypted string
 */
@decorators.node<UpgradePropertyToEncrypt>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class UpgradePropertyToEncrypt extends Node {
    @decorators.stringProperty<UpgradePropertyToEncrypt, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<UpgradePropertyToEncrypt, 'encryptedValue'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
        isStoredEncrypted: true,
    })
    readonly encryptedValue: Promise<string>;
}
