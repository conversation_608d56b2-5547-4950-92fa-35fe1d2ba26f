import { date, datetime, decorators, integer, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { upgradeDataTypesEnumDatatype, UpgradeDatatypesEnumType } from '../enums/upgrade-datatypes-enum-type';
import { upgradeRenamedEnumV2DataType, UpgradeRenamedEnumV2Type } from '../enums/upgrade-renamed-enum-v-2-type';
import { UpgradeReferenced } from './upgrade-referenced';

const value2DefaultValue = (value: integer) => 2 * value;

/**
 * V1->V2 differences:
 * value2 : new column
 * newMandatoryBoolean : new column
 * newMandatoryBooleanWithTrueAsDefault : new column
 * newMandatoryString : new column
 * newNullableInteger : new column
 * ref1 : new column
 * ref2 : new column
 * newMandatoryStringWithDefaultValue : new column
 * newMandatoryDateWithDefault : new column
 * newMandatoryDatetimeWithDefault : new column
 * newEnumWithDefault : new column
 * newPropOnRenamedEnum: new column on an enum that has been renamed
 *
 * Note: no changes on name1, value1
 */
@decorators.node<UpgradeAddColumns>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [
        { orderBy: { name1: -1 }, isUnique: false },
        { orderBy: { name1: 1, newMandatoryString: 1 }, isUnique: true },
    ],
})
export class UpgradeAddColumns extends Node {
    @decorators.stringProperty<UpgradeAddColumns, 'name1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name1: Promise<string>;

    @decorators.integerProperty<UpgradeAddColumns, 'value1'>({
        isStored: true,
        isPublished: true,
        defaultValue: 1,
    })
    readonly value1: Promise<integer>;

    @decorators.integerProperty<UpgradeAddColumns, 'value2'>({
        isStored: true,
        isPublished: true,
        async defaultValue() {
            // eslint-disable-next-line @sage/xtrem/property-decorators-errors
            return value2DefaultValue(await this.value1);
        },
    })
    readonly value2: Promise<integer>;

    @decorators.booleanProperty<UpgradeAddColumns, 'newMandatoryBoolean'>({
        isStored: true,
        isPublished: true,
    })
    // Mandatory boolean without defaultValue: will check that the default of the type (false) is used for
    // the automatic dataAction.
    readonly newMandatoryBoolean: Promise<boolean>;

    @decorators.booleanProperty<UpgradeAddColumns, 'newMandatoryBooleanWithTrueAsDefault'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
    })
    readonly newMandatoryBooleanWithTrueAsDefault: Promise<boolean>;

    @decorators.stringProperty<UpgradeAddColumns, 'newMandatoryString'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly newMandatoryString: Promise<string>;

    @decorators.integerProperty<UpgradeAddColumns, 'newNullableInteger'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly newNullableInteger: Promise<integer | null>;

    @decorators.referenceProperty<UpgradeAddColumns, 'ref1'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeReferenced,
    })
    // Will be managed by action upgrades/v1.1.2/add-columns-data-ref1.ts
    readonly ref1: Reference<UpgradeReferenced>;

    @decorators.referenceProperty<UpgradeAddColumns, 'ref2'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeReferenced,
    })
    // Will be managed by action upgrades/v1.1.2/add-columns-data-ref2.ts
    readonly ref2: Reference<UpgradeReferenced>;

    @decorators.stringProperty<UpgradeAddColumns, 'newMandatoryStringWithDefaultValue'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
        async defaultValue() {
            return `defaultValue ${await this.name1}`;
        },
    })
    readonly newMandatoryStringWithDefaultValue: Promise<string>;

    @decorators.dateProperty<UpgradeAddColumns, 'newMandatoryDateWithDefault'>({
        isStored: true,
        isPublished: true,
        defaultValue: () => date.parse('2000-01-02'), // Use a constant: it will be easier for the the unit-test
    })
    readonly newMandatoryDateWithDefault: Promise<date>;

    @decorators.datetimeProperty<UpgradeAddColumns, 'newMandatoryDatetimeWithDefault'>({
        isStored: true,
        isPublished: true,
        // TODO: convert this to a function and remove the exclusion hack of this property in property.ts (xtrem-core)
        defaultValue: datetime.parse('2000-01-02T03:04:05Z'), // Use a constant: it will be easier for the the unit-test
    })
    readonly newMandatoryDatetimeWithDefault: Promise<datetime>;

    @decorators.enumProperty<UpgradeAddColumns, 'newEnumWithDefault'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => upgradeDataTypesEnumDatatype,
        async defaultValue() {
            return (await this.value1) > 4 ? 'value1' : null;
        },
    })
    readonly newEnumWithDefault: Promise<UpgradeDatatypesEnumType | null>;

    @decorators.enumProperty<UpgradeAddColumns, 'newPropOnRenamedEnum'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => upgradeRenamedEnumV2DataType,
    })
    readonly newPropOnRenamedEnum: Promise<UpgradeRenamedEnumV2Type | null>;
}
