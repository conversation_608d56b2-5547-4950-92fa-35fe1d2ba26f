import { Collection, decorators, Node, StringDataType } from '@sage/xtrem-core';
import { UpgradeVitalChild } from './upgrade-vital-child';

@decorators.node<UpgradeVitalParent>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeVitalParent extends Node {
    @decorators.stringProperty<UpgradeVitalParent, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly code: Promise<string>;

    @decorators.collectionProperty<UpgradeVitalParent, 'children'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'parent',
        node: () => UpgradeVitalChild,
    })
    readonly children: Collection<UpgradeVitalChild>;
}
