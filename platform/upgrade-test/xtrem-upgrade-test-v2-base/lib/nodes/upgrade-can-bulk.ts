import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { UpgradeReferenced } from './upgrade-referenced';

/**
 * v1->v2 no changes:
 */
@decorators.node<UpgradeCanBulk>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canBulkDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeCanBulk extends Node {
    @decorators.stringProperty<UpgradeCanBulk, 'nameV1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly nameV1: Promise<string>;

    @decorators.referenceProperty<UpgradeCanBulk, 'ref1'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeReferenced,
    })
    readonly ref1: Reference<UpgradeReferenced>;
}
