import { decorators, Node, StringDataType } from '@sage/xtrem-core';

@decorators.node<UpgradeCustomSqlV2>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeCustomSqlV2 extends Node {
    @decorators.stringProperty<UpgradeCustomSqlV2, 'string2'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly string2: Promise<string>;

    @decorators.integerProperty<UpgradeCustomSqlV2, 'int2'>({
        isStored: true,
        isPublished: true,
    })
    readonly int2: Promise<string>;

    @decorators.booleanProperty<UpgradeCustomSqlV2, 'bool2'>({
        isStored: true,
        isPublished: true,
    })
    readonly bool2: Promise<string>;
}
