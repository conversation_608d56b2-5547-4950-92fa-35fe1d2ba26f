import { StringDataType, decorators } from '@sage/xtrem-core';
import { UpdateConstructorWithSubNodeLevel1New } from './update-constructor-with-sub-node-level-1-new';

/**
 * v1->v2 changes:
 * - node renamed: UpdateConstructorWithSubNodeLevel2 -> UpdateConstructorWithSubNodeLevel2New
 */
@decorators.subNode<UpdateConstructorWithSubNodeLevel2New>({
    extends: () => UpdateConstructorWithSubNodeLevel1New,
})
export class UpdateConstructorWithSubNodeLevel2New extends UpdateConstructorWithSubNodeLevel1New {
    @decorators.stringProperty<UpdateConstructorWithSubNodeLevel2New, 'element'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly element: Promise<string>;
}
