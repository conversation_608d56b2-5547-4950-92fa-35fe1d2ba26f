/**
 * This node will be used to test data patches
 *
 * V1 -> V2 differences :
 * - no difference
 */

import { decorators, integer, Node } from '@sage/xtrem-core';

@decorators.node<NodeForDataPatches>({
    isPublished: true,
    storage: 'sql',
})
export class NodeForDataPatches extends Node {
    @decorators.integerProperty<NodeForDataPatches, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.integerProperty<NodeForDataPatches, 'val1'>({
        isPublished: true,
        isStored: true,
    })
    readonly val1: Promise<integer>;

    @decorators.integerProperty<NodeForDataPatches, 'val2'>({
        isPublished: true,
        isStored: true,
    })
    readonly val2: Promise<integer>;

    @decorators.integerProperty<NodeForDataPatches, 'val3'>({
        isPublished: true,
        isStored: true,
    })
    readonly val3: Promise<integer>;

    @decorators.integerProperty<NodeForDataPatches, 'val4'>({
        isPublished: true,
        isStored: true,
    })
    readonly val4: Promise<integer>;
}
