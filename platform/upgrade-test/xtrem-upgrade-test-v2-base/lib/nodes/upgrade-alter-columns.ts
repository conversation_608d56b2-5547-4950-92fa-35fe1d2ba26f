import {
    DecimalDataType,
    Node,
    Reference,
    StringDataType,
    TextStream,
    TextStreamDataType,
    date,
    datetime,
    decimal,
    decorators,
    integer,
} from '@sage/xtrem-core';
import { UpgradeRenamedEnumV2Type, upgradeRenamedEnumV2DataType } from '../enums/upgrade-renamed-enum-v-2-type';
import { UpgradeReferenced } from './upgrade-referenced';

/**
 * V1->V2 differences:
 * stringToRef : string[250] -> ref (integer)
 * stringToLongerString: string[50] -> string[250]
 * stringToShorterString: string[20] -> string[7]
 * stringToPassword: string[100] -> encrypted string[100] (column size = 233)
 * stringToTextStream : string[50] -> text stream
 * stringToLocalizedString: string -> json
 * localizedStringToString: json -> string
 * stringToInteger: string -> integer
 * integerToDecimal: integer -> decimal
 * integerToReference: integer -> reference
 * integerToString: integer -> string[50]
 * dateToDatetime: date -> datetime
 * datetimeToDate: datetime => date
 * renamedEnum: UpgradeRenamedEnumV1 -> UpgradeRenamedEnumV2
 *
 * Note: passwordToPassword is unchanged
 */

const testTextStreamType = new TextStreamDataType({
    maxLength: 2048,
    allowedContentTypes: ['application/json', 'text/plain'],
});

@decorators.node<UpgradeAlterColumns>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeAlterColumns extends Node {
    @decorators.referenceProperty<UpgradeAlterColumns, 'stringToRef'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeReferenced,
        defaultValue: 1 as any,
    })
    readonly stringToRef: Reference<UpgradeReferenced>;

    @decorators.stringProperty<UpgradeAlterColumns, 'stringToLongerString'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly stringToLongerString: Promise<string>;

    @decorators.stringProperty<UpgradeAlterColumns, 'stringToShorterString'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 7 }),
    })
    readonly stringToShorterString: Promise<string>;

    @decorators.stringProperty<UpgradeAlterColumns, 'stringToPassword'>({
        isStored: true,
        isPublished: true,
        isStoredEncrypted: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly stringToPassword: Promise<string>;

    @decorators.stringProperty<UpgradeAlterColumns, 'passwordToPassword'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
        isStoredEncrypted: true,
    })
    readonly passwordToPassword: Promise<string>;

    @decorators.textStreamProperty<UpgradeAlterColumns, 'stringToTextStream'>({
        isStored: true,
        isPublished: true,
        dataType: () => testTextStreamType,
    })
    readonly stringToTextStream: Promise<TextStream>;

    @decorators.stringProperty<UpgradeAlterColumns, 'stringToLocalizedString'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 50, isLocalized: true }),
    })
    readonly stringToLocalizedString: Promise<string>;

    @decorators.stringProperty<UpgradeAlterColumns, 'localizedStringToString'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly localizedStringToString: Promise<string>;

    @decorators.integerProperty<UpgradeAlterColumns, 'stringToInteger'>({
        isPublished: true,
        isStored: true,
    })
    readonly stringToInteger: Promise<integer>;

    @decorators.decimalProperty<UpgradeAlterColumns, 'integerToDecimal'>({
        isPublished: true,
        isStored: true,
        dataType: () => new DecimalDataType({ precision: 9, scale: 3 }),
    })
    readonly integerToDecimal: Promise<decimal>;

    @decorators.referenceProperty<UpgradeAlterColumns, 'integerToReference'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeReferenced,
        defaultValue: 1 as any,
    })
    readonly integerToReference: Reference<UpgradeReferenced>;

    @decorators.stringProperty<UpgradeAlterColumns, 'integerToString'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly integerToString: Promise<string>;

    @decorators.datetimeProperty<UpgradeAlterColumns, 'dateToDatetime'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateToDatetime: Promise<datetime | null>;

    @decorators.dateProperty<UpgradeAlterColumns, 'datetimeToDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeToDate: Promise<date | null>;

    @decorators.enumProperty<UpgradeAlterColumns, 'renamedEnum'>({
        isStored: true,
        isPublished: true,
        dataType: () => upgradeRenamedEnumV2DataType,
    })
    readonly renamedEnum: Promise<UpgradeRenamedEnumV2Type>;
}
