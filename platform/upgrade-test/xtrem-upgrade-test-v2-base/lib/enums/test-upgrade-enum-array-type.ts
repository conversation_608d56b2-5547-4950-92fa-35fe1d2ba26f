import { EnumDataType } from '@sage/xtrem-core';

/*
The only purpose of this enum is to make sur it isn't droppred if it is used in an enumArrayProperty
*/
export enum TestUpgradeEnumArray {
    enumArrayValue1,
    enumArrayValue2,
    enumArrayValue3,
    enumArrayValue4,
}

export type TestUpgradeEnumArrayType = keyof typeof TestUpgradeEnumArray;

export const upgradeEnumArrayDataType = new EnumDataType<TestUpgradeEnumArrayType>({
    enum: TestUpgradeEnumArray,
    filename: __filename,
});
