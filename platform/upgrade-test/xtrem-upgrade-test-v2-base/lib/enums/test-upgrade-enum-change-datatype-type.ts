import { EnumDataType } from '@sage/xtrem-core';

export enum TestUpgradeEnumChangeDatatype {
    value1V2Datatype,
    value2V2Datatype,
    value3V2Datatype,
    value4V2Datatype,
}

export type TestUpgradeEnumChangeDatatypeType = keyof typeof TestUpgradeEnumChangeDatatype;

export const upgradeEnumChangeDatatypeDataType = new EnumDataType<TestUpgradeEnumChangeDatatypeType>({
    enum: TestUpgradeEnumChangeDatatype,
    filename: __filename,
});
