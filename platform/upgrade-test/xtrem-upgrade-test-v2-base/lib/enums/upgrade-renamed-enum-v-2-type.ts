import { EnumDataType } from '@sage/xtrem-core';

/**
 * v1 -> v2 changes :
 * - enum is renamed (UpgradeRenamedEnumV1 -> UpgradeRenamedEnumV2)
 * - value1V1 -> value1V2 (renamed)
 * - value2V1 -> (removed)
 * - value3V1 -> value3V1 (untouched)
 * - -> value4V2 (added)
 */
export enum UpgradeRenamedEnumV2 {
    value1V2,
    value3V1,
    value4V2,
}
export type UpgradeRenamedEnumV2Type = keyof typeof UpgradeRenamedEnumV2;

export const upgradeRenamedEnumV2DataType = new EnumDataType<UpgradeRenamedEnumV2Type>({
    enum: UpgradeRenamedEnumV2,
    filename: __filename,
});
