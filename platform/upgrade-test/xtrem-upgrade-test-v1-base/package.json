{"name": "@sage/xtrem-upgrade-test-v1-base", "description": "Package to test upgrades (v1 without extensions)", "version": "1.0.211", "xtrem": {"isHidden": true, "packageName": "@sage/xtrem-upgrade-test-base", "queue": "upgrade-test"}, "keywords": ["xtrem-application-package"], "author": "Sage", "license": "UNLICENSED", "private": true, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "files": ["build", "data", "routing.json"], "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-authorization": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-upload": "workspace:*", "eslint": "^8.49.0"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "eslint": "^8.49.0", "mocha": "^10.8.2"}, "scripts": {"build": "xtrem compile --skip-api-client", "build:binary": "xtrem compile --binary --prod --skip-api-client", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "lint": "eslint -c .eslintrc.js --ext .ts lib", "start": "xtrem start", "xtrem": "xtrem"}}