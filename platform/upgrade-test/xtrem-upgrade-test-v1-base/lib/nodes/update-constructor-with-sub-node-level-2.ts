import { StringDataType, decorators } from '@sage/xtrem-core';
import { UpdateConstructorWithSubNodeLevel1 } from './update-constructor-with-sub-node-level-1';
/**
 * v1->v2 changes:
 * - node renamed: UpdateConstructorWithSubNodeLevel2 -> UpdateConstructorWithSubNodeLevel2New
 */
@decorators.subNode<UpdateConstructorWithSubNodeLevel2>({
    extends: () => UpdateConstructorWithSubNodeLevel1,
})
export class UpdateConstructorWithSubNodeLevel2 extends UpdateConstructorWithSubNodeLevel1 {
    @decorators.stringProperty<UpdateConstructorWithSubNodeLevel2, 'element'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly element: Promise<string>;
}
