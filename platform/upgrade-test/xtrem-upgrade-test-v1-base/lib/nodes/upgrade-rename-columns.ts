import { Reference, decorators, Node, StringDataType } from '@sage/xtrem-core';
import { UpgradeReferenced } from './upgrade-referenced';

/**
 * v1->v2 changes:
 * - property 'nameV1' renamed to 'nameV1'
 * - property 'ref1' renamed to 'ref2'
 */
@decorators.node<UpgradeRenameColumns>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeRenameColumns extends Node {
    @decorators.stringProperty<UpgradeRenameColumns, 'nameV1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly nameV1: Promise<string>;

    @decorators.referenceProperty<UpgradeRenameColumns, 'ref1'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeReferenced,
    })
    readonly ref1: Reference<UpgradeReferenced>;
}
