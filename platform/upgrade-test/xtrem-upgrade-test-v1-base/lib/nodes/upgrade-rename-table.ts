import { Node, Reference, StringDataType, decorators } from '@sage/xtrem-core';
import { UpgradeReferenced } from './upgrade-referenced';

/**
 * v1->v2 changes:
 * - node renamed: UpgradeRenameTableV1 -> UpgradeRenameTableV2
 * - property name renamed to name2
 */
@decorators.node<UpgradeRenameTableV1>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true }],
})
export class UpgradeRenameTableV1 extends Node {
    @decorators.stringProperty<UpgradeRenameTableV1, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<UpgradeRenameTableV1, 'ref'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeReferenced,
    })
    readonly ref: Reference<UpgradeReferenced>;
}

/**
 * v1->v2 changes:
 * - node renamed: UpgradeRenameTableWithNotifyV1 -> UpgradeRenameTableWithNotifyV2
 * - list of notifies changes ['created', 'updated'] -> ['updated', 'deleted']
 */
@decorators.node<UpgradeRenameTableWithNotifyV1>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    notifies: ['created', 'updated'],
})
export class UpgradeRenameTableWithNotifyV1 extends Node {
    @decorators.stringProperty<UpgradeRenameTableWithNotifyV1, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name: Promise<string>;
}
