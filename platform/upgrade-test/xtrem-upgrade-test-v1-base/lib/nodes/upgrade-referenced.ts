import { decorators, integer, Node, StringDataType } from '@sage/xtrem-core';

@decorators.node<UpgradeReferenced>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
})
export class UpgradeReferenced extends Node {
    @decorators.integerProperty<UpgradeReferenced, 'id'>({
        isStored: true,
        isPublished: true,
    })
    readonly id: Promise<integer>;

    @decorators.stringProperty<UpgradeReferenced, 'dummy'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly dummy: Promise<string>;
}
