import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1->V2 differences:
 * only exists in v1
 */
@decorators.node<UpgradeDeleteTableWithAttachments>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    hasAttachments: true,
})
export class UpgradeDeleteTableWithAttachments extends Node {
    @decorators.stringProperty<UpgradeDeleteTableWithAttachments, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name: Promise<string>;
}
