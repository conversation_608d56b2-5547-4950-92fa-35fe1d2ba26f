import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1->V2 differences:
 * UpgradeAbstractNode1 in v1 will be replaced by UpgradeAbstractNode2 in v2
 */
@decorators.node<UpgradeAbstractNode1>({
    storage: 'sql',
    isAbstract: true,
})
export class UpgradeAbstractNode1 extends Node {
    @decorators.stringProperty<UpgradeAbstractNode1, 'strBase'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strBase: Promise<string>;
}
