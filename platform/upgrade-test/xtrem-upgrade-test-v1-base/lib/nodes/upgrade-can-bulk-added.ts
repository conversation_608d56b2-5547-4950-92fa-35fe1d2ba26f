import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { UpgradeReferenced } from './upgrade-referenced';

/**
 * v1->v2 changes:
 * - add canBulkDelete decorator
 */
@decorators.node<UpgradeCanBulkAdded>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeCanBulkAdded extends Node {
    @decorators.stringProperty<UpgradeCanBulkAdded, 'nameV1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly nameV1: Promise<string>;

    @decorators.referenceProperty<UpgradeCanBulkAdded, 'ref1'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeReferenced,
    })
    readonly ref1: Reference<UpgradeReferenced>;
}
