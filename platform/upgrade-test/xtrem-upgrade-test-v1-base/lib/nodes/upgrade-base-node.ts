import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1->V2 differences:
 * intBase : new integer property
 */
@decorators.node<UpgradeBaseNode>({
    storage: 'sql',
    isAbstract: true,
})
export class UpgradeBaseNode extends Node {
    @decorators.stringProperty<UpgradeBaseNode, 'strBase'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strBase: Promise<string>;
}
