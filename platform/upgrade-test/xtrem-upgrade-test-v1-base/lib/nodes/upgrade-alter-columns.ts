import { date, datetime, decorators, integer, Node, StringDataType } from '@sage/xtrem-core';
import { upgradeRenamedEnumV1DataType, UpgradeRenamedEnumV1Type } from '../enums/upgrade-renamed-enum-v-1-type';

/**
 * V1->V2 differences:
 * stringToRef : string[250] -> ref (integer)
 * stringToLongerString: string[50] -> string[250]
 * stringToShorterString: string[20] -> string[7]
 * stringToPassword: string[100] -> encrypted string[100] (column size = 233)
 * stringToTextStream : string[50] -> text stream
 * stringToLocalizedString: string -> json
 * localizedStringToString: json -> string
 * stringToInteger: string -> integer
 * integerToDecimal: integer -> decimal
 * integerToReference: integer -> reference
 * integerToString: integer -> string[50]
 * dateToDatetime: date -> datetime
 * datetimeToDate: datetime => date
 * renamedEnum: UpgradeRenamedEnumV1 -> UpgradeRenamedEnumV2
 *
 * Note: passwordToPassword is unchanged
 */
@decorators.node<UpgradeAlterColumns>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeAlterColumns extends Node {
    @decorators.stringProperty<UpgradeAlterColumns, 'stringToRef'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly stringToRef: Promise<string>;

    @decorators.stringProperty<UpgradeAlterColumns, 'stringToLongerString'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly stringToLongerString: Promise<string>;

    @decorators.stringProperty<UpgradeAlterColumns, 'stringToShorterString'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 20 }),
    })
    readonly stringToShorterString: Promise<string>;

    @decorators.stringProperty<UpgradeAlterColumns, 'stringToPassword'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly stringToPassword: Promise<string>;

    @decorators.stringProperty<UpgradeAlterColumns, 'passwordToPassword'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
        isStoredEncrypted: true,
    })
    readonly passwordToPassword: Promise<string>;

    @decorators.stringProperty<UpgradeAlterColumns, 'stringToTextStream'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly stringToTextStream: Promise<string>;

    @decorators.stringProperty<UpgradeAlterColumns, 'stringToLocalizedString'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly stringToLocalizedString: Promise<string>;

    @decorators.stringProperty<UpgradeAlterColumns, 'localizedStringToString'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 50, isLocalized: true }),
    })
    readonly localizedStringToString: Promise<string>;

    @decorators.stringProperty<UpgradeAlterColumns, 'stringToInteger'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly stringToInteger: Promise<string>;

    @decorators.integerProperty<UpgradeAlterColumns, 'integerToDecimal'>({
        isPublished: true,
        isStored: true,
    })
    readonly integerToDecimal: Promise<integer>;

    @decorators.integerProperty<UpgradeAlterColumns, 'integerToReference'>({
        isPublished: true,
        isStored: true,
    })
    readonly integerToReference: Promise<integer>;

    @decorators.integerProperty<UpgradeAlterColumns, 'integerToString'>({
        isPublished: true,
        isStored: true,
    })
    readonly integerToString: Promise<integer>;

    @decorators.dateProperty<UpgradeAlterColumns, 'dateToDatetime'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateToDatetime: Promise<date | null>;

    @decorators.datetimeProperty<UpgradeAlterColumns, 'datetimeToDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeToDate: Promise<datetime | null>;

    @decorators.enumProperty<UpgradeAlterColumns, 'renamedEnum'>({
        isStored: true,
        isPublished: true,
        dataType: () => upgradeRenamedEnumV1DataType,
    })
    readonly renamedEnum: Promise<UpgradeRenamedEnumV1Type>;
}
