import { decorators, integer, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1->V2 differences:
 * value2 : new column
 * newMandatoryBoolean : new column
 * newMandatoryBooleanWithTrueAsDefault : new column
 * newMandatoryString : new column
 * newNullableInteger : new column
 * ref1 : new column
 * ref2 : new column
 * newMandatoryStringWithDefaultValue : new column
 * newMandatoryDateWithDefault : new column
 * newMandatoryDatetimeWithDefault : new column
 * newPropOnRenamedEnum: new column on an enum that will be renamed
 *
 * Note: no changes on name1, value1
 */
@decorators.node<UpgradeAddColumns>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeAddColumns extends Node {
    @decorators.stringProperty<UpgradeAddColumns, 'name1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name1: Promise<string>;

    @decorators.integerProperty<UpgradeAddColumns, 'value1'>({
        isStored: true,
        isPublished: true,
        defaultValue: 1,
    })
    readonly value1: Promise<integer>;
}
