import { Reference, decorators, Node } from '@sage/xtrem-core';
import { UpgradeCustomSqlUpdated } from './upgrade-custom-sql-updated';

/**
 * This node will not have any update, it only refers to a node with a CustomSqlAction to test
 * that foreignKeys are processed right.
 */
@decorators.node<UpgradeCustomSqlUpdated>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeRefToCustomSqlUpdated extends Node {
    @decorators.referenceProperty<UpgradeRefToCustomSqlUpdated, 'ref'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeCustomSqlUpdated,
    })
    readonly ref: Reference<UpgradeCustomSqlUpdated>;
}
