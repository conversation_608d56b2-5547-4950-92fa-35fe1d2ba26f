import { decorators, StringDataType } from '@sage/xtrem-core';
import { UpgradeBaseNode2 } from './upgrade-base-node-2';

/**
 * V1->V2 differences:
 * v1 : hierarchy change: UpgradeChangeHierarchyLevels -> UpgradeBaseNode2 -> UpgradeBaseNode -> Node
 * v2 : hierarchy change: UpgradeChangeHierarchyLevels -> UpgradeBaseNode -> Node
 */
@decorators.subNode<UpgradeChangeHierarchyLevels>({
    isAbstract: true,
    extends: () => UpgradeBaseNode2,
})
export class UpgradeChangeHierarchyLevels extends UpgradeBaseNode2 {
    @decorators.stringProperty<UpgradeChangeHierarchyLevels, 'str'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly str: Promise<string>;
}
