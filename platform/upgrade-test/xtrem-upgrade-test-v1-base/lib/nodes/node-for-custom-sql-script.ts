import { decorators, integer, Node, StringDataType } from '@sage/xtrem-core';

@decorators.node<NodeForCustomSqlScript>({
    isPublished: true,
    storage: 'sql',
})
export class NodeForCustomSqlScript extends Node {
    @decorators.stringProperty<NodeForCustomSqlScript, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name: Promise<string>;

    @decorators.integerProperty<NodeForCustomSqlScript, 'value'>({
        isPublished: true,
        isStored: true,
    })
    readonly value: Promise<integer>;
}
