import { decorators, StringDataType } from '@sage/xtrem-core';
import { UpgradeBaseNode } from '.';

/**
 * V1->V2 differences:
 * only exists in v1
 */
@decorators.subNode<UpgradeDeleteTableWithBaseNode>({
    extends: () => UpgradeBaseNode,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
})
export class UpgradeDeleteTableWithBaseNode extends UpgradeBaseNode {
    @decorators.stringProperty<UpgradeDeleteTableWithBaseNode, 'strSub1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strSub1: Promise<string>;
}
