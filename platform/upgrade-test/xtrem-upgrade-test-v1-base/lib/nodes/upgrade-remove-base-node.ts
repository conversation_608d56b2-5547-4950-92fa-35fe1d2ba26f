import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1->V2 differences:
 * this node will be dropped in v2
 */
@decorators.node<UpgradeBaseNodeToDelete>({
    storage: 'sql',
    isAbstract: true,
})
export class UpgradeBaseNodeToDelete extends Node {
    @decorators.stringProperty<UpgradeBaseNodeToDelete, 'strBase'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strBase: Promise<string>;
}

/**
 * V1->V2 differences:
 * the base node is changed from UpgradeBaseNode (v1) to Node (v2)
 */
@decorators.subNode<UpgradeRemoveBaseNode>({
    extends: () => UpgradeBaseNodeToDelete,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
})
export class UpgradeRemoveBaseNode extends UpgradeBaseNodeToDelete {
    @decorators.stringProperty<UpgradeRemoveBaseNode, 'strProp'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strProp: Promise<string>;
}
