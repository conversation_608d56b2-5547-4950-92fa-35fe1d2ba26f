import { StringDataType, decorators } from '@sage/xtrem-core';
import { UpdateConstructorWithBaseNode } from './update-constructor-with-base-node';

/**
 * v1->v2 changes:
 * - the _constructor value is changed from old concrete node name - [UpdateConstructorWithSubNodeLevel2] (v1)
 *   to new concrete node name [UpdateConstructorWithSubNodeLevel2New] (v2)
 * - node renamed: UpdateConstructorWithSubNodeLevel1 -> UpdateConstructorWithSubNodeLevel1New
 */
@decorators.subNode<UpdateConstructorWithSubNodeLevel1>({
    isAbstract: true,
    extends: () => UpdateConstructorWithBaseNode,
})
export class UpdateConstructorWithSubNodeLevel1 extends UpdateConstructorWithBaseNode {
    @decorators.stringProperty<UpdateConstructorWithSubNodeLevel1, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name: Promise<string>;
}
