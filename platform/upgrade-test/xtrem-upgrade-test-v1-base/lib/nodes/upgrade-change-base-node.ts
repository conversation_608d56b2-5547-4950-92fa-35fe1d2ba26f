import { decorators, StringDataType } from '@sage/xtrem-core';
import { UpgradeBaseNode } from '.';
import { UpgradeAbstractNode1 } from './upgrade-abstract-node-1';

/**
 * V1->V2 differences:
 * the base node is changed from UpgradeAbstractNode1 (v1) to UpgradeAbstractNode2 (v2)
 */
@decorators.subNode<UpgradeChangeBaseNode>({
    extends: () => UpgradeBaseNode,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
})
export class UpgradeChangeBaseNode extends UpgradeAbstractNode1 {
    @decorators.stringProperty<UpgradeChangeBaseNode, 'strProp'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strProp: Promise<string>;
}
