import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1->V2 differences:
 * only exists in v1
 */
@decorators.node<UpgradeDeleteTable>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeDeleteTable extends Node {
    @decorators.stringProperty<UpgradeDeleteTable, 'name1'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name1: Promise<string>;
}
