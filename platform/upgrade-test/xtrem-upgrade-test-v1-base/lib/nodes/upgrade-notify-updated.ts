import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * v1->v2 changes:
 * - list of notifies changes ['created', 'updated'] -> ['updated', 'deleted']
 */

@decorators.node<UpgradeNotifyUpdated>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    notifies: ['created', 'updated'],
})
export class UpgradeNotifyUpdated extends Node {
    @decorators.stringProperty<UpgradeNotifyUpdated, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly name: Promise<string>;
}
