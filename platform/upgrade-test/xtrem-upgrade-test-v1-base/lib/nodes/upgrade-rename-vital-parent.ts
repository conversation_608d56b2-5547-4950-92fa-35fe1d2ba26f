import { Collection, decorators, Node, StringDataType } from '@sage/xtrem-core';
import { UpgradeRenameVitalChildV1 } from './upgrade-rename-vital-child';

@decorators.node<UpgradeRenameVitalParent>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class UpgradeRenameVitalParent extends Node {
    @decorators.stringProperty<UpgradeRenameVitalParent, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly code: Promise<string>;

    @decorators.collectionProperty<UpgradeRenameVitalParent, 'children'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'parent',
        node: () => UpgradeRenameVitalChildV1,
    })
    readonly children: Collection<UpgradeRenameVitalChildV1>;
}
