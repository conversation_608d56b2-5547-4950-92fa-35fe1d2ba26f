import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * V1->V2 differences:
 * v1 : hierachy change: UpgradeChangeHierarchyLevels -> UpgradeBaseNode2 -> UpgradeBaseNode -> Node
 * v2 : hierachy change: UpgradeChangeHierarchyLevels -> UpgradeBaseNode2 -> Node
 *      UpgradeBaseNode2 node is dropped in v2
 */
@decorators.node<UpgradeBaseNode2>({
    storage: 'sql',
    isAbstract: true,
})
export class UpgradeBaseNode2 extends Node {
    @decorators.stringProperty<UpgradeBaseNode2, 'strIntermediate'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strIntermediate: Promise<string>;
}
