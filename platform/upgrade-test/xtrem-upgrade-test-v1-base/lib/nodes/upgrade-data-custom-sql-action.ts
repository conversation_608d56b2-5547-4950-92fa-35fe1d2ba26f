import { decorators, integer, Node } from '@sage/xtrem-core';

/**
 * This node is used to run some CustomSqlActions
 * V1->V2 differences:
 * no difference
 */
@decorators.node<UpgradeDataCustomSqlAction>({
    storage: 'sql',
})
export class UpgradeDataCustomSqlAction extends Node {
    @decorators.integerProperty<UpgradeDataCustomSqlAction, 'int1'>({
        isPublished: true,
        isStored: true,
    })
    readonly int1: Promise<integer>;

    @decorators.integerProperty<UpgradeDataCustomSqlAction, 'int2'>({
        isPublished: true,
        isStored: true,
    })
    readonly int2: Promise<integer>;
}
