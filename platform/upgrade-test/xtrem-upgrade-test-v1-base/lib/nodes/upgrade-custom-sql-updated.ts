import { decorators, integer, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { UpgradeCustomSqlV1 } from './upgrade-custom-sql-v1';

/**
 * V1->V2 differences:
 * refProp : reference(UpgradeCustomSqlV1) -> reference(UpgradeCustomSqlV2)
 *
 * remove unique index
 */

@decorators.node<UpgradeCustomSqlUpdated>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [
        {
            orderBy: {
                strProp: +1,
            },
            isUnique: true,
        },
    ],
})
export class UpgradeCustomSqlUpdated extends Node {
    @decorators.stringProperty<UpgradeCustomSqlUpdated, 'strProp'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly strProp: Promise<string>;

    @decorators.integerProperty<UpgradeCustomSqlUpdated, 'intProp'>({
        isStored: true,
        isPublished: true,
    })
    readonly intProp: Promise<integer>;

    @decorators.booleanProperty<UpgradeCustomSqlUpdated, 'boolProp'>({
        isStored: true,
        isPublished: true,
    })
    readonly boolProp: Promise<boolean>;

    @decorators.referenceProperty<UpgradeCustomSqlUpdated, 'refProp'>({
        isPublished: true,
        isStored: true,
        node: () => UpgradeCustomSqlV1,
    })
    readonly refProp: Reference<UpgradeCustomSqlV1>;
}
