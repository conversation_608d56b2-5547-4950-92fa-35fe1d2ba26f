import { EnumDataType } from '@sage/xtrem-core';

/**
 * V1 -> V2 changes
 *
 * value1V1 -> value1V2
 * value2V1 -> value2V2
 * value3V1 -> value3V2
 * value4V1 -> (deleted)
 * value5V1 -> (deleted)
 * value6V1 -> value6V1 (untouched)
 * value7V1 -> (deleted)
 *          -> value21V2 (added)
 *          -> value31V2 (added)
 */
export enum TestUpgradeEnum {
    value1V1,
    value2V1,
    value3V1,
    value4V1,
    value5V1,
    value6V1,
    value7V1,
}

export type TestUpgradeEnumType = keyof typeof TestUpgradeEnum;

export const testUpgradeEnumDataType = new EnumDataType<TestUpgradeEnumType>({
    enum: TestUpgradeEnum,
    filename: __filename,
});
