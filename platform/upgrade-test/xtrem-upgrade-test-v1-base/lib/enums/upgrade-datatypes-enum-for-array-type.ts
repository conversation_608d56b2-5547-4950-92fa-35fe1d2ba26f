import { EnumDataType } from '@sage/xtrem-core';

enum UpgradeDatatypesEnumForArray {
    arrayVal1 = 1,
    arrayVal2 = 2,
    arrayVal3 = 3,
}

export type UpgradeDatatypesEnumForArrayType = keyof typeof UpgradeDatatypesEnumForArray;

export const upgradeDataTypesEnumForArrayDatatype = new EnumDataType<UpgradeDatatypesEnumForArrayType>({
    enum: UpgradeDatatypesEnumForArray,
    filename: __filename,
});
