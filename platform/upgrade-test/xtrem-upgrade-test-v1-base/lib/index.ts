/* eslint-disable class-methods-use-this */
import * as xtremAuthorization from '@sage/xtrem-authorization';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import * as activities from './activities';
import * as enums from './enums/_index';
import * as nodes from './nodes/index';
import * as serviceOptions from './service-options/index';

export { activities, enums, nodes, serviceOptions, xtremUpload };

xtremSystem.updateContext();
xtremAuthorization.updateContext();
