{"@sage/xtrem-upgrade-test-v1-base/activity__delete_activity__name": "Delete activity", "@sage/xtrem-upgrade-test-v1-base/activity__update_activity__name": "Update activity", "@sage/xtrem-upgrade-test-v1-base/data_types__test_upgrade_enum__name": "Test upgrade enum", "@sage/xtrem-upgrade-test-v1-base/data_types__upgrade_datatypes_enum__name": "Upgrade datatypes enum", "@sage/xtrem-upgrade-test-v1-base/data_types__upgrade_datatypes_enum_for_array__name": "Upgrade datatypes enum for array", "@sage/xtrem-upgrade-test-v1-base/data_types__upgrade_renamed_enum_v_1__name": "Upgrade renamed enum v 1", "@sage/xtrem-upgrade-test-v1-base/enums__test_upgrade_enum_type__value1V1": "Value 1 v 1", "@sage/xtrem-upgrade-test-v1-base/enums__test_upgrade_enum_type__value2V1": "Value 2 v 1", "@sage/xtrem-upgrade-test-v1-base/enums__test_upgrade_enum_type__value3V1": "Value 3 v 1", "@sage/xtrem-upgrade-test-v1-base/enums__test_upgrade_enum_type__value4V1": "Value 4 v 1", "@sage/xtrem-upgrade-test-v1-base/enums__test_upgrade_enum_type__value5V1": "Value 5 v 1", "@sage/xtrem-upgrade-test-v1-base/enums__test_upgrade_enum_type__value6V1": "Value 6 v 1", "@sage/xtrem-upgrade-test-v1-base/enums__test_upgrade_enum_type__value7V1": "Value 7 v 1", "@sage/xtrem-upgrade-test-v1-base/enums__upgrade_datatypes_enum_for_array_type__arrayVal1": "Array val 1", "@sage/xtrem-upgrade-test-v1-base/enums__upgrade_datatypes_enum_for_array_type__arrayVal2": "Array val 2", "@sage/xtrem-upgrade-test-v1-base/enums__upgrade_datatypes_enum_for_array_type__arrayVal3": "Array val 3", "@sage/xtrem-upgrade-test-v1-base/enums__upgrade_datatypes_enum_type__value1": "Value 1", "@sage/xtrem-upgrade-test-v1-base/enums__upgrade_datatypes_enum_type__value2": "Value 2", "@sage/xtrem-upgrade-test-v1-base/enums__upgrade_datatypes_enum_type__value3": "Value 3", "@sage/xtrem-upgrade-test-v1-base/enums__upgrade_renamed_enum_v_1_type__value1V1": "Value 1 v 1", "@sage/xtrem-upgrade-test-v1-base/enums__upgrade_renamed_enum_v_1_type__value2V1": "Value 2 v 1", "@sage/xtrem-upgrade-test-v1-base/enums__upgrade_renamed_enum_v_1_type__value3V1": "Value 3 v 1", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_custom_sql_script__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_custom_sql_script__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_custom_sql_script__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_custom_sql_script__node_name": "Node for custom sql script", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_custom_sql_script__property__name": "Name", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_custom_sql_script__property__value": "Value", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_data_patches__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_data_patches__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_data_patches__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_data_patches__node_name": "Node for data patches", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_data_patches__property__id": "Id", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_data_patches__property__val1": "Val 1", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_data_patches__property__val2": "Val 2", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_data_patches__property__val3": "Val 3", "@sage/xtrem-upgrade-test-v1-base/nodes__node_for_data_patches__property__val4": "Val 4", "@sage/xtrem-upgrade-test-v1-base/nodes__update_constructor_with_base_node__node_name": "Update constructor with base node", "@sage/xtrem-upgrade-test-v1-base/nodes__update_constructor_with_base_node__property__strBase": "Str base", "@sage/xtrem-upgrade-test-v1-base/nodes__update_constructor_with_sub_node_level_1__node_name": "Update constructor with sub node level 1", "@sage/xtrem-upgrade-test-v1-base/nodes__update_constructor_with_sub_node_level_1__property__name": "Name", "@sage/xtrem-upgrade-test-v1-base/nodes__update_constructor_with_sub_node_level_2__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__update_constructor_with_sub_node_level_2__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__update_constructor_with_sub_node_level_2__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__update_constructor_with_sub_node_level_2__node_name": "Update constructor with sub node level 2", "@sage/xtrem-upgrade-test-v1-base/nodes__update_constructor_with_sub_node_level_2__property__element": "Element", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_abstract_node_1__node_name": "Upgrade abstract node 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_abstract_node_1__property__strBase": "Str base", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_add_columns__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_add_columns__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_add_columns__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_add_columns__node_name": "Upgrade add columns", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_add_columns__property__name1": "Name 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_add_columns__property__value1": "Value 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__node_name": "Upgrade alter columns", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__datetimeToDate": "Datetime to date", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__dateToDatetime": "Date to datetime", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__integerToDecimal": "Integer to decimal", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__integerToReference": "Integer to reference", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__integerToString": "Integer to string", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__localizedStringToString": "Localized string to string", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__passwordToPassword": "Password to password", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__renamedEnum": "<PERSON>amed enum", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__stringToInteger": "String to integer", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__stringToLocalizedString": "String to localized string", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__stringToLongerString": "String to longer string", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__stringToPassword": "String to password", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__stringToRef": "String to ref", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__stringToShorterString": "String to shorter string", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_alter_columns__property__stringToTextStream": "String to text stream", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_base_node__node_name": "Upgrade base node", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_base_node__property__strBase": "Str base", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_base_node_2__node_name": "Upgrade base node 2", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_base_node_2__property__strIntermediate": "Str intermediate", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_base_node_to_delete__node_name": "Upgrade base node to delete", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_base_node_to_delete__property__strBase": "Str base", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_can_bulk__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_can_bulk__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_can_bulk__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_can_bulk__bulkMutation__bulkDelete": "Bulk delete", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_can_bulk__node_name": "Upgrade can bulk", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_can_bulk__property__nameV1": "Name v 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_can_bulk__property__ref1": "Ref 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_can_bulk_added__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_can_bulk_added__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_can_bulk_added__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_can_bulk_added__node_name": "Upgrade can bulk added", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_can_bulk_added__property__nameV1": "Name v 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_can_bulk_added__property__ref1": "Ref 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_change_base_node__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_change_base_node__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_change_base_node__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_change_base_node__node_name": "Upgrade change base node", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_change_base_node__property__strProp": "Str prop", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_change_hierarchy_levels__node_name": "Upgrade change hierarchy levels", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_change_hierarchy_levels__property__str": "Str", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_updated__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_updated__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_updated__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_updated__node_name": "Upgrade custom sql updated", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_updated__property__boolProp": "Bool prop", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_updated__property__intProp": "Int prop", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_updated__property__refProp": "Ref prop", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_updated__property__strProp": "Str prop", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_v_1__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_v_1__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_v_1__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_v_1__node_name": "Upgrade custom sql v 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_v_1__property__bool1": "Bool 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_v_1__property__int1": "Int 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_custom_sql_v_1__property__string1": "String 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_data_custom_sql_action__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_data_custom_sql_action__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_data_custom_sql_action__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_data_custom_sql_action__node_name": "Upgrade data custom sql action", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_data_custom_sql_action__property__int1": "Int 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_data_custom_sql_action__property__int2": "Int 2", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__node_name": "Upgrade datatypes", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__binaryStream": "Binary stream", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__booleanVal": "Boolean val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__dateRangeVal": "Date range val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__datetimeRangeVal": "Datetime range val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__datetimeVal": "Datetime val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__dateVal": "Date val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__decimalRangeVal": "Decimal range val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__decimalVal": "Decimal val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__doubleVal": "Double val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__enumArrayVal": "Enum array val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__enumVal": "Enum val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__floatVal": "Float val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__id": "Id", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__integerArrayVal": "Integer array val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__integerRangeVal": "Integer range val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__integerVal": "Integer val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__jsonVal": "<PERSON><PERSON> val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__mailTemplate": "Mail template", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__shortVal": "Short val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__stringArrayVal": "String array val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__stringVal": "String val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__textStream": "Text stream", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__timeVal": "Time val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_datatypes__property__uuidVal": "<PERSON><PERSON> val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_columns__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_columns__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_columns__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_columns__node_name": "Upgrade delete columns", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_columns__property__name1": "Name 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_columns__property__name2": "Name 2", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_columns__property__name3": "Name 3", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_columns__property__ref1": "Ref 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_columns__property__storedToComputed": "Stored to computed", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table__node_name": "Upgrade delete table", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table__property__name1": "Name 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table_with_attachments__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table_with_attachments__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table_with_attachments__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table_with_attachments__node_name": "Upgrade delete table with attachments", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table_with_attachments__property__name": "Name", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table_with_base_node__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table_with_base_node__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table_with_base_node__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table_with_base_node__node_name": "Upgrade delete table with base node", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_delete_table_with_base_node__property__strSub1": "Str sub 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_node_to_extend__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_node_to_extend__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_node_to_extend__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_node_to_extend__node_name": "Upgrade node to extend", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_node_to_extend__property__name1": "Name 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_notify_updated__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_notify_updated__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_notify_updated__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_notify_updated__node_name": "Upgrade notify updated", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_notify_updated__property__name": "Name", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_property_to_encrypt__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_property_to_encrypt__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_property_to_encrypt__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_property_to_encrypt__node_name": "Upgrade property to encrypt", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_property_to_encrypt__property__encryptedValue": "Encrypted value", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_property_to_encrypt__property__id": "Id", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_ref_to_custom_sql_updated__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_ref_to_custom_sql_updated__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_ref_to_custom_sql_updated__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_ref_to_custom_sql_updated__node_name": "Upgrade ref to custom sql updated", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_ref_to_custom_sql_updated__property__ref": "Ref", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_referenced__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_referenced__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_referenced__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_referenced__node_name": "Upgrade referenced", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_referenced__property__dummy": "Dummy", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_referenced__property__id": "Id", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_csv_vendor_child__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_csv_vendor_child__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_csv_vendor_child__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_csv_vendor_child__node_name": "Upgrade reload csv vendor child", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_csv_vendor_child__property__childString": "Child string", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_csv_vendor_child__property__id": "Id", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_csv_vendor_child__property__parent": "Parent", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_csv_vendor_parent__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_csv_vendor_parent__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_csv_vendor_parent__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_csv_vendor_parent__node_name": "Upgrade reload csv vendor parent", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_csv_vendor_parent__property__id": "Id", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_csv_vendor_parent__property__lines": "Lines", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_csv_vendor_parent__property__someString": "Some string", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_from_csv__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_from_csv__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_from_csv__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_from_csv__node_name": "Upgrade reload from csv", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_from_csv__property__name": "Name", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_from_csv__property__val": "Val", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_from_vendor_csv__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_from_vendor_csv__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_from_vendor_csv__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_from_vendor_csv__node_name": "Upgrade reload from vendor csv", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_from_vendor_csv__property__country": "Country", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_from_vendor_csv__property__id": "Id", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_from_vendor_csv__property__isOwnedByCustomerProperty": "Is owned by customer property", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_reload_from_vendor_csv__property__isOwnedByCustomerReference": "Is owned by customer reference", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_remove_base_node__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_remove_base_node__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_remove_base_node__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_remove_base_node__node_name": "Upgrade remove base node", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_remove_base_node__property__strProp": "Str prop", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_columns__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_columns__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_columns__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_columns__node_name": "Upgrade rename columns", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_columns__property__nameV1": "Name v 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_columns__property__ref1": "Ref 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_enum__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_enum__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_enum__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_enum__node_name": "Upgrade rename enum", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_enum__property__enumTest": "Enum test", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_enum__property__enumTestBis": "Enum test bis", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_enum__property__enumTestDatatype": "Enum test datatype", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_enum_second_location__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_enum_second_location__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_enum_second_location__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_enum_second_location__node_name": "Upgrade rename enum second location", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_enum_second_location__property__enumTest": "Enum test", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_v_1__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_v_1__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_v_1__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_v_1__node_name": "Upgrade rename table v 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_v_1__property__name": "Name", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_v_1__property__ref": "Ref", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_with_attachments_1__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_with_attachments_1__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_with_attachments_1__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_with_attachments_1__node_name": "Upgrade rename table with attachments 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_with_attachments_1__property__name": "Name", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_with_notify_v_1__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_with_notify_v_1__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_with_notify_v_1__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_with_notify_v_1__node_name": "Upgrade rename table with notify v 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_table_with_notify_v_1__property__name": "Name", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_vital_child_v_1__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_vital_child_v_1__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_vital_child_v_1__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_vital_child_v_1__node_name": "Upgrade rename vital child v 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_vital_child_v_1__property__code": "Code", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_vital_child_v_1__property__parent": "Parent", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_vital_parent__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_vital_parent__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_vital_parent__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_vital_parent__node_name": "Upgrade rename vital parent", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_vital_parent__property__children": "Children", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_rename_vital_parent__property__code": "Code", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_sub_node_1__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_sub_node_1__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_sub_node_1__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_sub_node_1__node_name": "Upgrade sub node 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_sub_node_1__property__strSub1": "Str sub 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_update_columns__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_update_columns__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_update_columns__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_update_columns__node_name": "Upgrade update columns", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_update_columns__property__name1": "Name 1", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_update_columns__property__selfRefToNonNullable": "Self ref to non nullable", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_update_columns__property__selfRefToNullable": "Self ref to nullable", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_update_columns__property__value2": "Value 2", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_update_columns__property__value3": "Value 3", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_vital_child__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_vital_child__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_vital_child__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_vital_child__node_name": "Upgrade vital child", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_vital_child__property__code": "Code", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_vital_child__property__parent": "Parent", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_vital_parent__asyncMutation__asyncExport": "Export", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_vital_parent__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_vital_parent__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_vital_parent__node_name": "Upgrade vital parent", "@sage/xtrem-upgrade-test-v1-base/nodes__upgrade_vital_parent__property__code": "Code", "@sage/xtrem-upgrade-test-v1-base/package__name": "Sage xtrem upgrade test v 1 base", "@sage/xtrem-upgrade-test-v1-base/permission__create__name": "Create", "@sage/xtrem-upgrade-test-v1-base/permission__delete__name": "Delete", "@sage/xtrem-upgrade-test-v1-base/permission__read__name": "Read", "@sage/xtrem-upgrade-test-v1-base/permission__update__name": "Update", "@sage/xtrem-upgrade-test-v1-base/service_options__upgrade_service_option_1__name": "Upgrade service option 1", "@sage/xtrem-upgrade-test-v1-base/service_options__upgrade_service_option_2__name": "Upgrade service option 2", "@sage/xtrem-upgrade-test-v1-base/service_options__upgrade_service_option_3__name": "Upgrade service option 3", "@sage/xtrem-upgrade-test-v1-base/service_options__upgrade_service_option_4__name": "Upgrade service option 4", "@sage/xtrem-upgrade-test-v1-base/service_options__upgrade_service_option_to_delete__name": "Upgrade service option to delete"}