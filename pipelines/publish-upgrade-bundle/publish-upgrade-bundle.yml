# This pipeline is used to build and publish an upgrade bundle

# disable PR and branch triggering
pr: none
trigger: none

pool:
  vmImage: 'ubuntu-latest'

variables:
  - group: dockerXtrem
  - name: archiveFilename
    # the name of the archive that will be generated by the 'pnpm pack' command
    value: 'not set'
  - name: archiveS3Uri
    # the S3 Uri of the archive
    value: 'not set'
  - name: archiveS3UriBase
    value: s3://xtrem-dev-eu-global/bundles/upgrade/current/

parameters:
  - name: bundleName
    displayName: 'The name of the upgrade bundle to publish (must be located in the /services/upgrade-bundles folder)'
    type: 'string'

  - name: useNpmProxy
    displayName: 'Use nexus npm proxy'
    default: false
    type: boolean

steps:
  # this job can only be run from the master branch (the bundle must have been validated and merged)
  # - script: |
  #     echo "##vso[task.logissue type=error]This pipeline can only be run from the master branch"
  #     exit 1
  #   condition: ne(variables['Build.SourceBranchName'], 'master')
  #   displayName: Check branch

  - template: ../shared/install-node.yml

  - template: ../shared/npm-configure.yml
    parameters:
      useNpmProxy: ${{ parameters.useNpmProxy }}

    # Prepare all the configuration files that will be used later
    # - create xtrem-config.yml files by patching xtrem-config.template.yml
    # - the files are copied to the bundle folder and in xtrem-services-main (to be able to bootstrap it)
  - script: |
      echo "copy and patch configuration file (xtrem-services-main)"
      sed s:\\[S3_XTREM_DEVELOPERS_UTILITY_ACCESS_KEY\\]:$(s3-xtrem-developers-utility-access-key):g $(Build.SourcesDirectory)/pipelines/publish-upgrade-bundle/xtrem-config.template.yml > $(Build.SourcesDirectory)/services/main/xtrem-services-main/xtrem-config.yml
      sed -i s:\\[S3_XTREM_DEVELOPERS_UTILITY_SECRET\\]:$(s3-xtrem-developers-utility-secret):g $(Build.SourcesDirectory)/services/main/xtrem-services-main/xtrem-config.yml
      sed -i s:\\[S3_XTREM_DEV_EU_GLOBAL_ACCESS_KEY\\]:$(s3-xtrem-dev-eu-global-access-key):g $(Build.SourcesDirectory)/services/main/xtrem-services-main/xtrem-config.yml
      sed -i s:\\[S3_XTREM_DEV_EU_GLOBAL_SECRET\\]:$(s3-xtrem-dev-eu-global-secret):g $(Build.SourcesDirectory)/services/main/xtrem-services-main/xtrem-config.yml
      echo "copy patched configuration file to ${{ parameters.bundleName }}"
      cp $(Build.SourcesDirectory)/services/main/xtrem-services-main/xtrem-config.yml $(Build.SourcesDirectory)/services/upgrade-bundles/${{ parameters.bundleName }}/xtrem-config.yml

    displayName: 'Prepare configuration files'

  # Install the dependencies for the bundle
  - bash: |
      pnpm run install
    displayName: 'Bundle: pnpm install'
    workingDirectory: '$(Build.SourcesDirectory)/services/upgrade-bundles/${{ parameters.bundleName }}'

  # Npm Pack to build and create a .tgz file
  - script: |
      cd $(Build.SourcesDirectory)/services/upgrade-bundles/${{ parameters.bundleName }}
      pnpm pack &> output.txt
      cat output.txt
      # Note: the last output of the 'pnpm pack' command is the name of the generated archive
      filename="$(tail -n 1 output.txt)"
      echo "##vso[task.setvariable variable=archiveFilename]$(tail -n 1 output.txt)"
      echo "Generated archive $filename"
    displayName: 'Bundle: pnpm pack'

  # Install the dependencies for the xtrem-services-main
  - bash: |
      pnpm run install
    displayName: 'intact-gateway: pnpm install'
    workingDirectory: '$(Build.SourcesDirectory)/services/main/xtrem-services-main'

  # Build xtrem-services-main (no need to compile the client stuff)
  # WARNING !!! this build relies on packages published to npm. Might fail if the npm repo is not in sync with the repo
  - script: |
      cd $(Build.SourcesDirectory)/services/main/xtrem-services-main
      SKIP_CLIENT=1 npm run build
    displayName: 'intact-gateway: build'

  # Deploy a new database container and create the schema
  - bash: |
      scripts/docker/load-docker-images.sh
      scripts/postgres/deploy-postgres.sh --skip-client-check
    displayName: 'Deploy a database container and client tools'
    env:
      AWS_ACCESS_KEY_ID: $(s3-xtrem-developers-utility-access-key)
      AWS_SECRET_ACCESS_KEY: $(s3-xtrem-developers-utility-secret)
      AWS_REGION: 'eu-west-1'

  # Restore the last available db dump from S3
  - script: |
      cd $(Build.SourcesDirectory)/services/main/xtrem-services-main
      pnpm run xtrem schema --restore-from-s3
    displayName: 'intact-gateway: restore db'

  # the dumps in S3 do not contain any tenant.
  # Init the following tenant: (we are using --layers= to skip the loading of any layer- this would overwrite
  # the data loaded from the dump)
  #
  #   {
  #   "customer": {
  #     "id": "00000000000000000001",
  #     "name": "acme"
  #   },
  #   "tenant": {
  #     "id": "777777777777777777777",
  #     "name": "dev"
  #   },
  #   "adminUser": {
  #     "email": "<EMAIL>",
  #     "firstName": "John",
  #     "lastName": "Doe",
  #     "locale": "en-US"
  #   }
  # }
  - script: |
      cd $(Build.SourcesDirectory)/services/main/xtrem-services-main
      npm run xtrem -- tenant --layers= --init ********************************************************************************************************************************************************************************************************************************************************************************************************************
    displayName: 'intact-gateway: init tenant'

  # Upload the previously created archive to S3
  - script: |
      cd $(Build.SourcesDirectory)/services/main/xtrem-services-main
      npm run xtrem -- upgrade --upload-bundle-to-s3 $(Build.SourcesDirectory)/services/upgrade-bundles/${{ parameters.bundleName }}/$(archiveFilename)
      echo "##vso[task.logissue type=warning]The archive was uploaded to $(archiveS3UriBase)$(archiveFilename)"
      echo "##vso[task.setvariable variable=archiveS3Uri]$(archiveS3UriBase)$(archiveFilename)"
    displayName: 'Upload the archive to S3'

  # Try to install the upgrade bundle (from S3)
  - script: |
      cd $(Build.SourcesDirectory)/services/main/xtrem-services-main
      npm run xtrem -- upgrade --install-bundle-from-s3 $(archiveS3Uri) || (
        echo "The upgrade bundle could not be installed"
        echo "##vso[task.logissue type=error]The bundle could not be installed, the archive has been deleted from S3"
        # npm run xtrem -- upgrade --delete-bundle-from-s3 $(archiveS3Uri)
        exit 1
      )
    displayName: 'Install the bundle from S3'
