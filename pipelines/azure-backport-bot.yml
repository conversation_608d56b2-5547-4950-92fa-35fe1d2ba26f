pr: none
trigger: none

schedules:
  - cron: '*/30 0,8-23 * * 1-5'
    displayName: Run every 30 mins
    always: true
    branches:
      include:
        - master

parameters:
  - name: prNumber
    displayName: Single PR to backport (even if closed)
    type: string
    default: ''

jobs:
  - job: backport_bot
    pool:
      vmImage: 'ubuntu-latest'
    variables:
      - group: sagex3ci_github
    steps:
      - checkout: none
      - template: ./shared/manual-git-clone.yml
        parameters:
          # Blobless clone are good for dev and CI for fast and small sized clone
          # see https://github.blog/2020-12-21-get-up-to-speed-with-partial-clone-and-shallow-clone/
          blobless: true
      - bash: |
          set -e
          if [ "${PR_NUMBER}" != "" ]; then
            node scripts/releng/backport-prs.mjs --pr-number "${PR_NUMBER}"
          else
            node scripts/releng/backport-prs.mjs
          fi
        displayName: 'Creating PRs'
        env:
          GITHUB_PASSWORD: $(github_password)
          PR_NUMBER: ${{ parameters.prNumber }}
