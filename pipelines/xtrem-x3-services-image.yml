name: xtrem-x3-services-image

# disable PR and branch triggering
pr: none
trigger: none

# Build is no longer scheduled but it is now triggered by a dependency on xtrem-patch-release build
# see https://docs.microsoft.com/en-us/azure/devops/pipelines/process/pipeline-triggers?view=azure-devops&tabs=yaml
resources:
  repositories:
    - repository: huracan
      type: github
      endpoint: Sage-ERP-X3
      name: Sage-ERP-X3/huracan
      # branch name to use
      ref: master

  pipelines:
    - pipeline: xtremPatchRelease # Name of the pipeline resource
      source: xtrem-patch-release # Name of the pipeline referenced by the pipeline resource
      trigger:
        branches:
          - master

parameters:
  - name: dryRun
    type: boolean
    default: false

variables:
  - group: sagex3ci_github
  - group: globalSettings
  # see: https://sage-liveservices.visualstudio.com/X3%20XTREM/_library?itemType=VariableGroups&view=VariableGroupView&variableGroupId=149&path=dockerXtrem
  - group: ms_teams
  - group: dockerXtrem
  - group: aquasecurity
  - name: aquaScanEnabled
    value: $[eq(variables['ENABLE_AQUA_SCAN'], 'True')]
  # Do not prefix the image name with the repo name because the push will do it for you
  - name: imageName
    value: 'xtrem-x3-services'
  - name: productName
    value: 'x3-services'
  - name: subname
    value: 'x3-services'
  - name: dockerFolder
    value: 'docker/$(subname)-image'
  - name: defaultTag
    value: 'staging'
  - name: tagMajor
    value: ''
  - name: tagMinor
    value: ''
  - name: exitTitle
    value: 'Xtrem X3 Services docker image build'

jobs:
  # ========================================
  # == Linux build
  # ========================================
  - job: Linux_build

    variables:
      - name: exitTitle
        value: 'Xtrem X3 docker image'

    pool:
      vmImage: 'ubuntu-latest'

    steps:
      - checkout: none

      - template: pipelines/templates/git/manual-git-clone.yml@huracan
        parameters:
          depth: 100

      # Init xtremBranch variable
      - template: pipelines/templates/misc/branch-name.yml@huracan
        parameters:
          allowAnyBranch: ${{ parameters.dryRun }}

      # Init imageVersion variable
      - template: pipelines/templates/misc/get-image-version.yml@huracan
        parameters:
          packagePath: '$(dockerFolder)'
          branchName: '$(xtremBranch)'

      # get the node version of the alpine image
      - template: pipelines/templates/install-node.yml@huracan

      # set the tag name depending on the branch, the variable tagName has the new value only outside that task
      - bash: |
          echo "##vso[task.setvariable variable=nodeVersion;isOutput=true]$(nodeVersion)"
        displayName: Set global node version
        name: setNodeVerStep

      # set the tag name depending on the branch, the variable tagName has the new value only outside that task
      - bash: |
          DOCKER_IMAGE_TAG=$(defaultTag)
          MINOR_TAG=""
          MAJOR_TAG=""

          if [ "$(BUILD.SOURCEBRANCHNAME)" != "master" ]; then
            BRANCH_STREAM=$(echo $(BUILD.SOURCEBRANCH) | awk -F / '{print $3}')
            echo stream is ${BRANCH_STREAM}
            if [ "${BRANCH_STREAM}" == "release" ]; then
              DOCKER_IMAGE_TAG=$(imageVersion)
              MINOR_TAG=$(echo $DOCKER_IMAGE_TAG | awk -F. '{ print $1"."$2 }')
              MAJOR_TAG=$(echo $DOCKER_IMAGE_TAG | awk -F. '{ print $1 }')
            else
              if [ "${{ parameters.dryRun }}" == "True" ]; then
                echo "##vso[task.logissue type=warning]Running in dry mode, the image won't be pushed!"
              else
                echo "##vso[task.logissue type=error]bad branch name: only master and release/x.y can be used to generate images"
                exit 1
              fi
            fi
          fi
          echo "##vso[task.setvariable variable=tagName;isOutput=true]${DOCKER_IMAGE_TAG}"
          echo "##vso[task.setvariable variable=tagName]${DOCKER_IMAGE_TAG}"
          echo "##vso[task.setvariable variable=tagMinor;isOutput=true]${MINOR_TAG}"
          echo "##vso[task.setvariable variable=tagMinor]${MINOR_TAG}"
          echo "##vso[task.setvariable variable=tagMajor]${MAJOR_TAG}"
          echo "##vso[task.setvariable variable=tempContainer]$(imageName)_$(Build.BuildId)"
          echo "##vso[task.setvariable variable=pg_version]$(cat .pgdbrc)"
          echo "##vso[task.setvariable variable=imageVersion;isOutput=true]$(imageVersion)"
        displayName: set image tag name and postgres version
        name: setTagStep

      - script: echo image will be generated with $(imageName):$(tagName) name
        displayName: final image name:tag

      # Delete the staging index file to prevent devops from getting a docker image and zip file not in sync
      # For this task inputs, see https://github.com/aws/aws-toolkit-azure-devops/blob/master/src/tasks/AWSShellScript/task.json
      - task: AmazonWebServices.aws-vsts-tools.AWSShellScript.AWSShellScript@1
        condition: and(succeeded(), eq(variables.tagMinor, ''), not(${{ parameters.dryRun }}))
        inputs:
          awsCredentials: 'xtrem-s3-dev-bucket'
          regionName: 'eu-west-1'
          scriptType: 'filePath'
          filePath: pipelines/shared/scripts/aws-rm-x3-win-index.sh
          arguments: '$(productName) $(defaultTag)'
          logRequest: true
          logResponse: true
        displayName: Delete aws index file

      # Login on nexus private registry
      - task: Docker@2
        displayName: Login to repository
        inputs:
          command: login
          containerRegistry: ghcr

      # .env file required for building the image in the next step
      - task: DownloadSecureFile@1
        displayName: 'Get docker env for build'
        name: 'dockerBuildEnv'
        inputs:
          secureFile: '.env-xtrem-docker-build'

      - bash: |
          # amend secure file
          echo 'AZURE_DEVOPS_TOKEN='$AZURE_DEVOPS_TOKEN'' >> ${DOCKER_ENV_SOURCE}
        displayName: 'Add Azure DevOps token to docker env file'
        env:
          DOCKER_ENV_SOURCE: $(dockerBuildEnv.secureFilePath)
          AZURE_DEVOPS_TOKEN: $(System.AccessToken)

      # Build the multi-stage image
      - bash: $(Build.SourcesDirectory)/scripts/docker/docker-build-image.sh
        env:
          DOCKER_ENV_SOURCE: $(dockerBuildEnv.secureFilePath)
          IMAGE_NAME: $(imageName)
          TAG_NAME: $(tagName)
          TAG_MINOR: $(tagMinor)
          TAG_MAJOR: $(tagMajor)
        workingDirectory: $(dockerFolder)
        displayName: 'Build docker image'

      - script: |
          docker tag ghcr.io/sage-erp-x3/$(imageName):$(tagName) $(imageName):smoke-test
          cp xtrem-config-x3-sql.yml xtrem-config.yml
        workingDirectory: $(dockerFolder)
        displayName: setup smoke tests
        condition: succeeded()

      # Test the image
      - bash: |
          docker run --rm -i -v $(pwd)/scripts:/xtrem/app/scripts $(imageName):smoke-test node /xtrem/app/scripts/can-load-oracle-lib.js
        displayName: 'TEST: Can load oracle libs'
        workingDirectory: $(dockerFolder)

      - bash: |
          docker compose -f $(dockerFolder)/docker-compose-smoke-test.yml up --abort-on-container-exit --exit-code-from smoke_test
        displayName: 'TEST: Can start and listen'

      # Get pnpm-lock.yaml from it then publish it as an artifact
      - bash: |
          set -e
          mkdir -p $(Build.ArtifactStagingDirectory)/linux-build/
          docker create --name $(tempContainer) $(imageName):$(tagName)
          docker cp $(tempContainer):/xtrem/app/package.json $(Build.ArtifactStagingDirectory)/linux-build/package.json
          docker cp $(tempContainer):/xtrem/app/pnpm-lock.yaml $(Build.ArtifactStagingDirectory)/linux-build/pnpm-lock.yaml
          docker rm $(tempContainer)
          ls -l $(Build.ArtifactStagingDirectory)/linux-build
        displayName: Get pnpm-lock.yaml from localy created docker image
        condition: succeeded()

      - publish: $(Build.ArtifactStagingDirectory)/linux-build/
        artifact: linux-build
        displayName: Publish pnpm-lock.yaml artifact
        condition: succeeded()

      - bash: |
          docker images
        displayName: List docker images before scanning

      - template: pipelines/templates/npm-configure.yml@huracan

      - bash: |
          xdevVersion=$(grep -E "'@sage/xdev@\d+\.\d+\.\d+':" pnpm-lock.yaml 2> /dev/null | sed 's/[^0-9.]*//g')
          pnpm install -g @sage/xdev@"${xdevVersion}"
        displayName: Install xdev globally

      # AquaSec image scan
      # Use local image to avoid overriding of the nexus image
      - template: pipelines/templates/misc/image-scan.yml@huracan
        parameters:
          imageName: $(imageName)
          tag: $(tagName)

      # Login again on nexus private registry because the scan changed the repo
      - task: Docker@2
        displayName: Login to repository
        inputs:
          command: login
          containerRegistry: ghcr

      # Push the image on nexus private registry
      - task: Docker@2
        displayName: Push the image
        condition: and(succeeded(), not(${{ parameters.dryRun }}))
        inputs:
          repository: sage-erp-x3/$(imageName)
          command: push
          tags: |
            $(tagName)
            $(tagMinor)
            $(tagMajor)

      - task: Docker@2
        displayName: Logout from repository
        inputs:
          command: logout
          containerRegistry: ghcr

      # notify ci-support only if not a dry run build
      - ${{ if not(parameters.dryRun) }}:
          - template: pipelines/templates/misc/notify-steps.yml@huracan
            parameters:
              incomingWebhookUrl: '$(ci_support_channel)'

      # notify release-delivery only if not a dry run build
      - ${{ if and(not(parameters.dryRun), not(eq(variables['Build.SourceBranchName'], 'master'))) }}:
          - template: pipelines/templates/misc/notify-steps.yml@huracan
            parameters:
              incomingWebhookUrl: '$(release_delivery_channel)'

  # ========================================
  # == Windows build
  # ========================================
  - job: Windows_build
    dependsOn: Linux_build
    condition: succeeded()

    variables:
      - name: exitTitle
        value: 'Xtrem X3 zip delivery'
      - name: xtremBuildDir
        value: '$(Build.ArtifactStagingDirectory)\xtrem-x3-services-win'
      - name: xtremDistDir
        value: '$(Build.ArtifactStagingDirectory)\xtrem-x3-services'
      - name: nodeVersion
        value: $[ dependencies.Linux_build.outputs['setNodeVerStep.nodeVersion'] ]
      - name: tagName
        value: $[ dependencies.Linux_build.outputs['setTagStep.tagName'] ]
      - name: tagMinor
        value: $[ dependencies.Linux_build.outputs['setTagStep.tagMinor'] ]
      - name: imageVersion
        value: $[ dependencies.Linux_build.outputs['setTagStep.imageVersion'] ]

    pool:
      vmImage: 'windows-2022'

    steps:
      - checkout: none
      - template: pipelines/templates/git/manual-git-clone.yml@huracan
        parameters:
          depth: 1

      # use the version initialized in the linux build above
      - template: ./shared/install-node-win.yml

      - script: |
          mkdir $(xtremBuildDir)
          xcopy /I docker\x3-services-image\windows  $(xtremBuildDir)
          copy docker\x3-services-image\package.json $(xtremBuildDir)\
          copy docker\x3-services-image\pnpm-lock.yaml $(xtremBuildDir)\
          xcopy /I patches $(xtremBuildDir)\patches
          mkdir $(xtremBuildDir)\nodejs\win32-x64
          copy C:\hostedtoolcache\windows\node\$(nodeVersion)\x64\node.exe $(xtremBuildDir)\nodejs\win32-x64\
          dir $(xtremBuildDir)
          $(xtremBuildDir)\nodejs\win32-x64\node.exe -v
          mkdir $(xtremDistDir)
        displayName: Copy windows assets

      - task: npmAuthenticate@0
        inputs:
          workingFile: $(xtremBuildDir)\.npmrc
          feedUrl: 'https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/'
        displayName: 'NPM - Authenticate'

      # install to generate the Xtrem X3 delivery
      - bash: |
          # exit on non zero status code, we cannot use failOnStderr option because npm emits some warnings on stderr
          set -e
          echo "##[group]Install pnpm"
          corepack enable
          corepack prepare "$(jq -r '.packageManager' package.json | cut -d'+' -f1)" --activate
          corepack install
          echo "##[endgroup]"
          echo "##[group]Set registry"
          pnpm config set --global registry https://registry.npmjs.org
          pnpm config set --global @sage:registry https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/
          pnpm config set --global @sageai:registry https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/
          echo "##[endgroup]"
          # fixed node-windows version to 1.0.0-beta.6 see issue https://github.com/coreybutler/node-windows/issues/308
          echo "##[group]Install node-windows"
          pnpm install node-windows@1.0.0-beta.6 --lockfile-only
          echo "##[endgroup]"
          echo "##[group]Install xtrem application packages"
          pnpm install --prod --frozen-lockfile --config.node-linker=hoisted
          echo "##[endgroup]"
        workingDirectory: $(xtremBuildDir)
        displayName: 'NPM - Install'

      - bash: |
          cp node_modules/@sage/xtrem-cli/bin/xtrem node_modules/.bin/xtrem
        workingDirectory: $(xtremBuildDir)
        displayName: 'Override xtrem cli'

      - powershell: |
          mkdir $(Build.ArtifactStagingDirectory)\windows-build
          cp package.json $(Build.ArtifactStagingDirectory)\windows-build\
          cp pnpm-lock.yaml $(Build.ArtifactStagingDirectory)\windows-build\
        displayName: 'Make deployment folder'

      - bash: |
          # exit on non zero status code, we cannot use failOnStderr option because kiiling the process emits errors on stderr
          set -e
          # copy config for tests
          cp x3-services/xtrem-config-azure.yml ${XTREM_MAIN_APP_DIR}/xtrem-config.yml
          printf "    database: sagex3\n    hostname: localhost\n    user: REPOSX3\n    password: tiger\n  development:\n    folderName: REPOSX3\n    referenceFolder: X3\n    defaultLanguage: FRA\n    soap:\n        webServiceURL: http://scmx3-dev-dis.sagefr.adinternal.com:8124/soap-generic/syracuse/collaboration/syracuse/CAdxWebServiceXmlCC\n        userCredentials:\n            userName: admin\n            password: admin\n        codeLang: ENG\n        poolAlias: WSP\n        timeout: 50000\n" >> ${XTREM_MAIN_APP_DIR}/xtrem-config.yml
          cp x3-services/xtrem-security-template.yml ${XTREM_MAIN_APP_DIR}/xtrem-security.yml
          echo "##[group]Test application ready"
          pipelines/shared/scripts/application-ready-test.sh
          echo "##[endgroup]"
          # cleanup
          rm -rf ${XTREM_MAIN_APP_DIR}/logs
          rm -f ${XTREM_MAIN_APP_DIR}/newrelic_agent.log
          rm ${XTREM_MAIN_APP_DIR}/xtrem-config.yml
          rm ${XTREM_MAIN_APP_DIR}/xtrem-security.yml
        displayName: 'TEST: Can start and listen'
        condition: succeeded()
        env:
          XTREM_MAIN_APP_DIR: $(xtremBuildDir)

      - bash: |
          set -e
          # verify config
          cp x3-services/xtrem-config-prod-template.yml ${XTREM_MAIN_APP_DIR}/xtrem-config-template.yml
          cp x3-services/xtrem-security-prod-template.yml ${XTREM_MAIN_APP_DIR}/xtrem-security-template.yml
          grep -q "managedExternal: true" ${XTREM_MAIN_APP_DIR}/xtrem-config-template.yml
          grep -q "loginUrl: http://syracuse-server:8124" ${XTREM_MAIN_APP_DIR}/xtrem-security-template.yml
          grep -q "clientId: create-your-own-client-id-uuid" ${XTREM_MAIN_APP_DIR}/xtrem-security-template.yml
          grep -q "secret: change-to-use-a-strong-secret-for-your-client-id" ${XTREM_MAIN_APP_DIR}/xtrem-security-template.yml
          echo "List content of ${XTREM_MAIN_APP_DIR}"
          ls -l ${XTREM_MAIN_APP_DIR}
        displayName: 'Copy and verify config'
        condition: succeeded()
        env:
          XTREM_MAIN_APP_DIR: $(xtremBuildDir)

      - publish: $(Build.ArtifactStagingDirectory)\windows-build
        artifact: windows-build
        displayName: Publish pnpm-lock.yaml artifact
        condition: succeeded()

      - bash: |
          set -e
          rm ./pnpm-lock.yaml
          echo "List content of $PWD"
          ls -l ./
        displayName: 'Delete pnpm-lock.yaml'
        condition: succeeded()
        workingDirectory: $(xtremBuildDir)

      - powershell: |
          cd $(xtremBuildDir)
          $buildNumber = $(Build.BuildId)
          $curdate = Get-Date
          $subFolder= "$($curdate.Year)$($curdate.Month)"
          $zipSubPath= "$(productName)/$($subFolder)"
          $zipDir= "$(xtremDistDir)/$(productName)/$($subFolder)"
          $deliveryVersion = "$(tagMinor)"
          $zipVersion = "$(tagName)"
          if ([string]::IsNullOrEmpty($deliveryVersion)) {
            $deliveryVersion = "$(defaultTag)"
            $zipVersion = "$(tagName).$(imageVersion)"
          }
          mkdir $zipDir
          $zipName = "$(productName)-$zipVersion" + "." + $buildNumber + "-win.zip"
          Write-Host "##vso[task.setvariable variable=deliveryVersion]$deliveryVersion"
          Write-Host "##vso[task.setvariable variable=deliveryVersion;isOutput=true]$deliveryVersion"

          Write-Host "##vso[task.setvariable variable=zipDir]$zipDir"
          Write-Host "##vso[task.setvariable variable=zipDir;isOutput=true]$zipDir"

          Write-Host "##vso[task.setvariable variable=zipName]$zipName"
          Write-Host "##vso[task.setvariable variable=zipName;isOutput=true]$zipName"

          Write-Host "##vso[task.setvariable variable=zipSubPath]$zipSubPath"
          Write-Host "##vso[task.setvariable variable=zipSubPath;isOutput=true]$zipSubPath"

          Write-Host "##vso[task.setvariable variable=tagName;isOutput=true]$(tagName)"
        displayName: 'Extract version and set zip name'
        name: setZipStep

      - script: |
          del $(xtremBuildDir)\.npmrc
        displayName: NPM - Post cleanup

      - task: ArchiveFiles@2
        inputs:
          rootFolderOrFile: '$(xtremBuildDir)'
          includeRootFolder: false
          archiveType: 'zip'
          archiveFile: '$(zipDir)\$(zipName)'
          verbose: true
        displayName: Zip content

      - task: PublishPipelineArtifact@1
        displayName: 'Upload ZIP to Azure cache'
        condition: succeeded()
        inputs:
          targetPath: '$(zipDir)\$(zipName)'
          artifactType: 'pipeline'
          artifactName: 'cache_zip'

      # notify ci-support if not a dry run build
      - ${{ if and(not(parameters.dryRun), not(eq(variables.disableNotify, True))) }}:
          - template: pipelines/templates/misc/notify-steps.yml@huracan
            parameters:
              incomingWebhookUrl: '$(ci_support_channel)'

      - ${{ if and(not(parameters.dryRun), not(eq(variables.disableNotify, True))) }}:
          - template: shared/notify-adc-channel.yml
            parameters:
              pipelineName: 'x3-services-image'

      # notify release-delivery only if not a dry run build and if not the master branch
      - ${{ if and(not(parameters.dryRun), not(eq(variables.disableNotify, True)), not(eq(variables['Build.SourceBranchName'], 'master'))) }}:
          - template: pipelines/templates/misc/notify-steps.yml@huracan
            parameters:
              incomingWebhookUrl: '$(release_delivery_channel)'

  - job: x3_partner_dev_pack
    pool:
      name: 'x3-ubuntu'
    steps:
      - checkout: none
      - template: pipelines/templates/misc/setup-bash-env.yml@huracan
      - template: ./images/partner-developer-pack.yml
        parameters:
          dryRun: ${{ parameters.dryRun }}

  - job: x3_izpack
    dependsOn: Windows_build
    condition: succeeded()
    variables:
      - name: zipName
        value: $[ dependencies.Windows_build.outputs['setZipStep.zipName'] ]
      - name: deliveryVersion
        value: $[ dependencies.Windows_build.outputs['setZipStep.deliveryVersion'] ]
      - name: exitTitle
        value: 'Xtrem X3 JAR delivery'
      - name: tagName
        value: $[ dependencies.Windows_build.outputs['setZipStep.tagName'] ]
    pool:
      name: 'x3-ubuntu'
    steps:
      - checkout: none
      - template: ./images/izpack.yml
        parameters:
          dryRun: ${{ parameters.dryRun }}
          scope: 'x3-services'
          projectName: 'X3Services'
          zipName: $(zipName)
          deliveryVersion: $(deliveryVersion)
          tagName: $(tagName)

      # notify ci-support if not a dry run build
      - ${{ if and(not(parameters.dryRun), not(eq(variables.disableNotify, True))) }}:
          - template: pipelines/templates/misc/notify-steps.yml@huracan
            parameters:
              incomingWebhookUrl: '$(ci_support_channel)'

      - ${{ if and(not(parameters.dryRun), not(eq(variables.disableNotify, True))) }}:
          - template: shared/notify-adc-channel.yml
            parameters:
              pipelineName: 'x3-services-image'

      # notify release-delivery only if not a dry run build and if not the master branch
      - ${{ if and(not(parameters.dryRun), not(eq(variables.disableNotify, True)), not(eq(variables['Build.SourceBranchName'], 'master'))) }}:
          - template: pipelines/templates/misc/notify-steps.yml@huracan
            parameters:
              incomingWebhookUrl: '$(release_delivery_channel)'
