name: qa-pi-run-st-x3-services-x3rbtrun

resources:
  repositories:
    # huracan repository reference used by template-run-xtrem-services-v2.yml
    - repository: huracan
      type: github
      endpoint: Sage-ERP-X3
      name: Sage-ERP-X3/huracan
      # use stable branch v2.0 for huracan
      ref: v2.0


pr: none
trigger: none

pool: 'x3-ubuntu'

parameters:
  - name: scope
    displayName: test scope
    type: string
    default: 'x3-services'

  - name: x3Environment
    displayName: X3 environment
    type: string
    default: 'qa/pi'
    values:
      - qa/pi
      - qa/ci_latest
      - qa/ci_devopsvnext
      - qa/ci_release
      - none

  - name: clusterName
    displayName: Environment and cluster to use
    type: string
    default: 'none'
    values:
      - deveu/cluster-ci
      - deveu/cluster-cu
      - deveu/cluster-release
      - qana/cluster-release
      - ppeu1/cluster-release
      - ppeu1/pp-prd
      - pdeu/pilot
      - pdna/pilot
      - none

  - name: TENANT_PROVISIONNING_METHOD
    displayName: Tenant provisioning method
    type: string
    default: 'skipped'
    values:
      - skipped
      - static

  - name: SKIP_CUCUMBER_TESTS
    displayName: Skip tests execution
    type: string
    default: 'No'
    values:
      - No
      - Yes

  - name: authenticationType
    displayName: Authentication Type
    type: string
    default: 'sageId'
    values:
      - unsecure
      - sageId

  - name: TARGET_URL
    type: string
    displayName: 'TARGET_URL'
    default: 'http://aws-syrinteg.sagex3rd.local:8154/handheld'
  - name: tenantId
    displayName: tenantId
    type: string
    default: 'none'

  - name: tenantName
    displayName: tenantName
    type: string
    default: 'none'

  - name: endPointName1
    type: string
    displayName: 'endPointName1'
    default: 'X3SERV / X3RBTRUN'

  - name: tenantLayer
    displayName: Layer for provisioning the tenant
    type: string
    default: 'none'
    values:
      - none
      - setup,test
      - setup,qa
      - setup,demo

  - name: tenantApp
    displayName: Tenant application
    type: string
    default: 'none'
    values:
      - glossary
      - sdmo
      - shopfloor
      - showcase
      - none

  - name: tenantAppName
    displayName: Tenant application Name
    type: string
    default: 'none'
    values:
      - Glossary
      - Sage Distribution and Manufacturing
      - Shop Floor Control
      - Showcase
      - none

  - name: testType
    displayName: Type of tests to execute
    type: string
    default: 'Functional tests'
    values:
      - All smoke tests
      - Smoke tests static
      - Functional tests

  - name: XTREM_TEST_MAX_INSTANCES
    displayName: MAX INSTANCES
    type: number
    default: 1

  - name: CUCUMBER_TAGS
    type: string
    displayName: 'CUCUMBER_TAGS'
    default: '@smoketest_mandatory'

  - name: writeSQL
    displayName: Write data to mysql
    type: string
    default: 'No'
    values:
      - Yes
      - No

variables:
  - template: ../cucumber-test/templates/template-shared-variables.yml
    parameters:
      clusterName: '${{parameters.clusterName}}'
      endPointName1: '${{parameters.endPointName1}}'
      imageMajorVersion: 'none'
      imageVersion: 'none'
      scope: '${{parameters.scope}}'
      TARGET_URL: '${{parameters.TARGET_URL}}'
      testType: '${{parameters.testType}}'
      tenantName: '${{parameters.tenantName}}'
      tenantId: '${{parameters.tenantId}}'
      TENANT_PROVISIONNING_METHOD: '${{parameters.TENANT_PROVISIONNING_METHOD}}'
      x3Environment: '${{parameters.x3Environment}}'

stages:
  - stage: StartAgent
    displayName: StartAgent
    jobs:
      - job: StartAgentLinux
        displayName: StartAgentLinux
        pool:
          vmImage: 'windows-2022'
        workspace:
          clean: all
        steps:
          - checkout: none
          - task: AWSCLI@1
            inputs:
              awsCredentials: 'ConnectAWS'
              regionName: 'eu-west-1'
              awsCommand: 'ec2'
              awsSubCommand: 'start-instances'
              awsArguments: '--instance-ids "i-0aeb447755017126a'

  - stage: Run_template_st
    pool:
      name: X3DEVOPS
      demands: Agent.Name -equals agentlindevops1

    dependsOn:
      - StartAgent
    jobs:
      - template: ../cucumber-test/templates/template-run-xtrem-services-v2.yml
        parameters:
          authenticationType: '${{parameters.authenticationType}}'
          cluster: '$(cluster)'
          clusterId: '$(cluster)'
          clusterName: '${{parameters.clusterName}}'
          CUCUMBER_TAGS: '${{parameters.CUCUMBER_TAGS}}'
          endPointName1: '${{parameters.endPointName1}}'
          scope: '${{parameters.scope}}'
          SKIP_CUCUMBER_TESTS: '${{parameters.SKIP_CUCUMBER_TESTS}}'
          SKIP_STAGE_TENANT_PROVISIONNING: $[ replace('${{ parameters['TENANT_PROVISIONNING_METHOD'] }}', 'skipped', 'Yes') ]
          smokeTestType: '$(smoke_test_type)'
          TARGET_URL: '$(target_url)'
          TENANT_PROVISIONNING_METHOD: '${{parameters.TENANT_PROVISIONNING_METHOD}}'
          tenantApp: '${{parameters.tenantApp}}'
          tenantAppName: '${{parameters.tenantAppName}}'
          tenantId: '$(tenant_id)'
          tenantLayer: '${{parameters.tenantLayer}}'
          tenantName: '$(tenant_name)'
          testType: '${{parameters.testType}}'
          XTREM_TEST_MAX_INSTANCES: ${{parameters.XTREM_TEST_MAX_INSTANCES}}
