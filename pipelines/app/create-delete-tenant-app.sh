#!/usr/bin/env bash

# Ensure the tenant exists, check if the app exists, decommission the app if it exists, create the app and provision it if requested

# Env vars to set
# ACCESS_TOKEN => can be get from sageId endpoint (https://id-shadow.sage.com/oauth/token)
# API_URL => the dev or QA lambda api url, ex for dev "https://3m48vx3e0e.execute-api.eu-west-1.amazonaws.com/v1/"
# TENANT_ID => the id of the tenant we want to provision the app to
# USER_EMAIL => the email of the user to provision as dirst admin, need to be a master user of the customer of the tenant.
# APP_CODE => Application code we want to provision, ex "sdmo", "shopfloor", "x3_connector" (code should be with underscore, not dashes, lowercase)
# INDIVIDUAL (email who started the pipeline, used for audit only)
# CLUSTER => the cluster to provision the app to, ex "cls-release". Important if the tenant already an app must be the same as the existing app's cluster
# LAYERS => the layers to provision, ex "setup,test"
# DELETE_ONLY => if set to true, will only delete the app if it exists, will not create it

# Output none for now

# Exit code :
# 0 - Succeed
# 1 - Error on provision app tenant request call, no operation url, error on decommissioning api call.
# 2 - Multiple error after retries to fetch operation url
# 3 - Operation of ended in error
# 4 - Operation of timed out


function track_operation_completion {
  local OPERATION_URL=$1
  local ACCESS_TOKEN=$2
  local INDIVIDUAL=$3
  local OPERATION_ID=$4
  local OPERATION_DESCRIPTION=$5

  local ERROR_COUNT=0
  local SECONDS=0                   # Built into bash to track seconds
  local MAX_DURATION_IN_SECONDS=600 # 10 mins should be plenty
  local STATUS="none"
  local STATUS_SUCCESS="success"
  local STATUS_ERROR="error"
  local SUCCESS="false"

  until [[ "${SECONDS}" -gt "${MAX_DURATION_IN_SECONDS}" ]] || [[ "$SUCCESS" == "true" ]]; do
    sleep 5
    echo "Fetching operation status at ${OPERATION_URL}"
    local FETCH_OPERATION_RESPONSE=$(curl -sS -X GET \
      -H "Content-Type:application/json" \
      -H "x-version:v2" \
      -H "Authorization:Bearer ${ACCESS_TOKEN}" \
      -H "X_ROLE:OPS_ADMIN" \
      -H "X_INDIVIDUAL:${INDIVIDUAL}" \
      "${OPERATION_URL}")
    echo "API URL: ${OPERATION_URL}"

    local STATUS=$(echo "${FETCH_OPERATION_RESPONSE}" | jq -r .status)
    local STEP=$(echo "${FETCH_OPERATION_RESPONSE}" | jq -r .curStep)

    if [ "null" = "${STATUS}" ]; then
      echo "Error while fetching operation status, response from api :${FETCH_OPERATION_RESPONSE}"
      ERROR_COUNT=$((ERROR_COUNT + 1))

      if [[ $ERROR_COUNT -gt 5 ]]; then
        echo "Multiple errors after retries, assuming failure"
        echo "##vso[task.setvariable variable=exitMessage;]Multiple errors after retries, assuming failure"
        exit 3
      else
        echo "Retrying, error counter is ${ERROR_COUNT}"
      fi
    else
      # Status read ok
      if [[ "${STATUS_SUCCESS}" = "${STATUS}" ]]; then
        echo "End of ${OPERATION_DESCRIPTION} operation in success"
        SUCCESS="true"
      else
        echo "Status of workflow is ${STATUS}, step is ${STEP}"
      fi
      if [[ "${STATUS_ERROR}" = "${STATUS}" ]]; then
        echo "${OPERATION_DESCRIPTION} ended in error, check operation logs on swf via cloudmanager : https://cloudmanager-v2.dev-sagextrem.com/operation/${OPERATION_ID}"
        echo "##vso[task.setvariable variable=exitMessage;]${OPERATION_DESCRIPTION} ended in error"
        exit 4
      fi

    fi
  done

  if [[ "$SUCCESS" != "true" ]]; then
    echo "${OPERATION_DESCRIPTION} did not finish before the script timeout, check operation logs on swf via cloudmanager : https://cloudmanager-v2.dev-sagextrem.com/operation/${OPERATION_ID}"
    echo "##vso[task.setvariable variable=exitMessage;]${OPERATION_DESCRIPTION} did not finish before the script timeout"
    exit 5
  fi
}

# Check if tenant exists
TENANT_GET_API_URL="${API_URL}/tenant/${TENANT_ID}"
echo "Authorization:Bearer ${ACCESS_TOKEN}"
GET_TENANT_RESPONSE=$(curl -w "%{http_code}" -sS -X GET -H "Content-Type:application/json" -H "x-version:v2" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${TENANT_GET_API_URL}")
GET_TENANT_PAYLOAD=$(echo "$GET_TENANT_RESPONSE" | sed 's/\(.*\).\{3\}$/\1/') # remove last 3 characters
GET_TENANT_STATUS=$(echo "$GET_TENANT_RESPONSE" | sed 's/.*\(.\{3\}\)$/\1/')  # get last 3 characters
echo "API OPERATION_URL: ${TENANT_GET_API_URL}"
if ! [[ "$GET_TENANT_STATUS" =~ ^[0-9]+$ ]] || [ "$GET_TENANT_STATUS" -ne 200 ]; then
  echo "Error: Failed to get tenant details"
  echo "Response is:"
  echo "$GET_TENANT_RESPONSE"
  exit 1
fi

echo "Tenant ${TENANT_ID} found"

# check if tenant app exists
TENANT_GET_APP_API_URL="${API_URL}/tenant/${TENANT_ID}/app/${APP_CODE}"
GET_TENANT_APP_RESPONSE=$(curl -w "%{http_code}" -sS -X GET -H "Content-Type:application/json" -H "x-version:v2" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${TENANT_GET_APP_API_URL}")
GET_TENANT_APP_PAYLOAD=$(echo "$GET_TENANT_APP_RESPONSE" | sed 's/\(.*\).\{3\}$/\1/') # remove last 3 characters
GET_TENANT_APP_STATUS=$(echo "$GET_TENANT_APP_RESPONSE" | sed 's/.*\(.\{3\}\)$/\1/')  # get last 3 characters

APP_EXIST=true

if [[ "$GET_TENANT_APP_STATUS" == "404" ]]; then
  echo "App ${APP_CODE} does not exists on the tenant"
  APP_EXIST=false
else
  if [[ "$GET_TENANT_APP_STATUS" != "200" ]]; then
    echo "Error: Failed to get tenant app details"
    echo "Response is:"
    echo "$GET_TENANT_APP_RESPONSE"
    echo "##vso[task.setvariable variable=exitMessage;]Tenant not found"
    exit 1
  else
    echo "App ${APP_CODE} exists on the tenant"
  fi
fi

# decomission app if needed
if [[ "$APP_EXIST" == true ]]; then
  echo "Decomissioning app ${APP_CODE}"

  export DECOMISSION_BODY='{"xtremIgnoreErrors": true,"autoCleanTenant": false,"finalBackup": false,"decommissionOnly":false}'

  DECOMISSION_APP_API_URL="${API_URL}/tenant/${TENANT_ID}/app/${APP_CODE}/decommission"
  DECOMISSION_APP_RESPONSE=$(curl -w "%{http_code}" -d "${DECOMISSION_BODY}" -sS -X DELETE -H "Content-Type:application/json" -H "x-version:v2" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${DECOMISSION_APP_API_URL}")
  DECOMISSION_APP_PAYLOAD=$(echo "$DECOMISSION_APP_RESPONSE" | sed 's/\(.*\).\{3\}$/\1/') # remove last 3 characters
  DECOMISSION_APP_STATUS=$(echo "$DECOMISSION_APP_RESPONSE" | sed 's/.*\(.\{3\}\)$/\1/')  # get last 3 characters

  if ! [[ "$DECOMISSION_APP_STATUS" =~ ^[0-9]+$ ]] || [ "$DECOMISSION_APP_STATUS" -ne 201 ]; then
    echo "Error: Failed to decomission app"
    echo "##vso[task.setvariable variable=exitMessage;]Could not decommission app"
    echo "Response is:"
    echo "$DECOMISSION_APP_RESPONSE"
    exit 1
  fi

  OPERATION_URL=$(echo "${DECOMISSION_APP_PAYLOAD}" | jq -r '.url')
  OPERATION_ID=$(echo "${DECOMISSION_APP_PAYLOAD}" | jq -r '.id')
  echo "Decomission app started, operation id ${OPERATION_ID}"

  track_operation_completion "${OPERATION_URL}" "${ACCESS_TOKEN}" "${INDIVIDUAL}" "${OPERATION_ID}" "decommission app ${TENANT_ID}-${APP_CODE}"

  # End if we need to decomission app
fi

# Create tenant app part

if [[ "$DELETE_ONLY" == "true" ]]; then
  echo "Delete only mode, exiting"
  exit 0
fi

CREATE_APP_BODY=$(jq -n \
  --arg app "$APP_CODE" \
  --arg cluster "$CLUSTER" \
  --arg dataOptions "$LAYERS" \
  --arg mainAdmin "$TENANT_MAIN_ADMIN_EMAIL" \
  '{
                    app: $app,
                    cluster: $cluster,
                    enabled: false,
                    provisioning: {
                      dataType: "layers",
                      dataOptions: $dataOptions
                    },
                    mainAdmin: $mainAdmin
                  }')

echo "Creating app ${APP_CODE} on tenant ${TENANT_ID}"

CREATE_APP_API_URL="${API_URL}/tenant/${TENANT_ID}/app"
CREATE_APP_RESPONSE=$(curl -w "%{http_code}" -d "${CREATE_APP_BODY}" -sS -X POST -H "Content-Type:application/json" -H "x-version:v2" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${CREATE_APP_API_URL}")
CREATE_APP_PAYLOAD=$(echo "$CREATE_APP_RESPONSE" | sed 's/\(.*\).\{3\}$/\1/') # remove last 3 characters
CREATE_APP_STATUS=$(echo "$CREATE_APP_RESPONSE" | sed 's/.*\(.\{3\}\)$/\1/')  # get last 3 characters

if ! [[ "$CREATE_APP_STATUS" =~ ^[0-9]+$ ]] || [ "$CREATE_APP_STATUS" -ne 201 ]; then
  echo "Error: Failed to create app"
  echo "##vso[task.setvariable variable=exitMessage;]Could not create app"
  echo "Response is:"
  echo "$CREATE_APP_RESPONSE"
  exit 1
fi

echo "App ${APP_CODE} created on tenant ${TENANT_ID}"
echo "Starting app provisioning"
PROVISION_APP_API_URL="${API_URL}/tenant/${TENANT_ID}/app/${APP_CODE}/provision"
PROVISION_APP_RESPONSE=$(curl -w "%{http_code}" -sS -X POST -H "Content-Type:application/json" -H "x-version:v2" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${PROVISION_APP_API_URL}")
PROVISION_APP_PAYLOAD=$(echo "${PROVISION_APP_RESPONSE}" | sed 's/\(.*\).\{3\}$/\1/') # remove last 3 characters
PROVISION_APP_STATUS=$(echo "${PROVISION_APP_RESPONSE}" | sed 's/.*\(.\{3\}\)$/\1/')  # get last 3 characters
  echo "##vso[task.setvariable variable=tenantProvisioningState;isOutput=true]true"
if ! [[ "${PROVISION_APP_STATUS}" =~ ^[0-9]+$ ]] || [ "${PROVISION_APP_STATUS}" -ne 201 ]; then
  echo "Error: Failed to provision app"
  echo "##vso[task.setvariable variable=exitMessage;]Could not provision app"
  echo "##vso[task.setvariable variable=tenantProvisioningState;isOutput=true]false"
  echo "Response is:"
  echo "${PROVISION_APP_RESPONSE}"
  exit 1
fi

OPERATION_URL=$(echo "${PROVISION_APP_PAYLOAD}" | jq -r '.url')
OPERATION_ID=$(echo "${PROVISION_APP_PAYLOAD}" | jq -r '.id')

echo "App ${APP_CODE} provisioning started, operation id ${OPERATION_ID}, tracking url ${OPERATION_URL}"
track_operation_completion "${OPERATION_URL}" "${ACCESS_TOKEN}" "${INDIVIDUAL}" "${OPERATION_ID}" "provision app ${TENANT_ID}-${APP_CODE}"
