name: xtrem-showcase-image

# disable PR and branch triggering
pr: none
trigger: none

# Build is no longer scheduled but it is now triggered by a dependency on xtrem-platform-patch-release build
# see https://docs.microsoft.com/en-us/azure/devops/pipelines/process/pipeline-triggers?view=azure-devops&tabs=yaml
resources:
  pipelines:
    - pipeline: xtremPatchRelease # Name of the pipeline resource
      source: xtrem-patch-release # Name of the pipeline referenced by the pipeline resource
      trigger:
        branches:
          - master

parameters:
  - name: dryRun
    type: boolean
    default: false

jobs:
  - template: shared/image-build-job.yml
    parameters:
      imageName: 'xtrem'
      dockerFolder: 'docker/showcase-image'
      subname: 'showcase'
      dryRun: ${{ parameters.dryRun }}
