name: xtrem-translation-extraction

pr: none
trigger: none

resources:
  repositories:
    - repository: huracan
      type: github
      endpoint: Sage-ERP-X3
      name: Sage-ERP-X3/huracan
      # use stable branch v2.0 for huracan
      ref: v2.0

parameters:
  - name: dryRun
    type: boolean
    default: false
    displayName: 'Dry run'

pool:
  vmImage: 'ubuntu-latest'

schedules:
  - cron: '0 22 * * 1-5'
    displayName: Extracting content for translation
    branches:
      include:
        - master

variables:
  - group: dockerXtrem

steps:
  - checkout: self
    workspaceRepo: true
    path: xtrem
    fetchDepth: 1
    fetchTags: false
    submodules: false
  - checkout: huracan
    path: huracan
    fetchDepth: 1
    fetchTags: false
    submodules: false

  - template: pipelines/templates/install.yml@huracan
    parameters:
      skipNativeDependencies: true

  - bash: |
      mkdir -p tmp
      mv package.json pnpm-lock.yaml tmp/
      pnpm install --frozen-lockfile --prod typescript@4.6 glob ts-node
      mv tmp/package.json tmp/pnpm-lock.yaml ./
      pnpm run translation:extract
    displayName: 'Extracting new translations'

  - script: 'zip -r translations.zip translations && mv translations.zip translations/translations.zip'

  - task: AmazonWebServices.aws-vsts-tools.S3Upload.S3Upload@1
    displayName: 'Upload translations to S3'
    condition: and(succeeded(), not(${{ parameters.dryRun }}))
    inputs:
      awsCredentials: 'aws-s3'
      regionName: 'eu-west-1'
      bucketName: 'xtrem-developers-utility'
      sourceFolder: 'translations'
      globExpressions: '**'
      targetFolder: 'translations'
      logRequest: true
      logResponse: true
