# Template to execute the artillery scenarios on a cluster
parameters:
  - name: appId
    displayName: app to provision for the tests
    type: string

  - name: tenantIdToCreate
    displayName: id of the tenant to create for the tests
    type: string
    default: 'not_set_perf_test'

  - name: startFrom
    displayName: Start from
    type: string
    values:
      - export
      - new tenant
    default: export

  - name: scenarios
    displayName: Scenarios to execute
    type: object
    default:
      - 'customer'
      - 'sales-order/query'
      - 'sales-order/create'
      - 'supplier/create-supplier-with-no-business-entity'

  - name: scenarioTotalUsers
    displayName: Number of users
    # Note: this parameter should be typed as a number, unfortunately we are using group variables for the daily/monthly
    # pipelines and numbers are not supported
    type: string
    default: 1

  - name: scenarioDuration
    displayName: Duration of the scenario in second
    # Note: this parameter should be typed as a number, unfortunately we are using group variables for the daily/monthly
    # pipelines and numbers are not supported
    type: string
    default: 1

  - name: scenarioRampUp
    displayName: Users Ramp-up
    # Note: this parameter should be typed as a number, unfortunately we are using group variables for the daily/monthly
    # pipelines and numbers are not supported
    type: string
    default: 1

  - name: userThinkTime
    displayName: User Think Time
    # Note: this parameter should be typed as a number, unfortunately we are using group variables for the daily/monthly
    # pipelines and numbers are not supported
    type: string
    default: 0

  - name: importScenarios
    displayName: Import scenarios to execute
    type: object
    default:
      - name: 'small'
        timeoutSeconds: 900
        pollingMillis: 5000

  - name: cluster
    displayName: The cluster on which the tenant must be created
    type: string
    default: 'perf-test'

  - name: sourceExportId
    displayName: The exportId of the data to restore (when start from is 'export')
    type: string
    default: 'provide the same export id as in the "export" pipeline'

  - name: tenantLayer
    displayName: Layer for provisioning the tenant (when start from is 'new tenant')
    type: string
    default: 'test'
    values:
      - none
      - test
      - qa
      - demo

  - name: keepTenantsAlive
    displayName: Do not delete created tenants (for debugging purpose)
    type: boolean
    default: false

  - name: executionType
    displayName: The type of execution
    type: string
    default: 'manual'
    values:
      - manual
      - daily
      - monthly

  - name: variables
    displayName: Other variables passed to the scenario
    type: string
    default: '{}'

  - name: timeoutInMinutes
    displayName: timeout in minutes passed to the scenario
    type: number
    default: 100

jobs:
  - job: Provisioning_tenant_Execute_tests_Delete_tenant
    displayName: Tenant provisioning steps, test execution steps, delete tenant steps.
    timeoutInMinutes: ${{parameters.timeoutInMinutes}}
    variables:
      exitMessage: 'An error occurred, see build results'
      accessToken: ''
      s3UploadTargetFolder: performance-tests/services
      htmlReportsDir: $(Agent.TempDirectory)/artillery-html-reports/

    steps:
      - checkout: self
        # this makes this repo being the default working directory,
        # to reference it explicitly, use $(Build.Repository.LocalPath) instead of $(Build.SourcesDirectory)
        workspaceRepo: true
        path: xtrem
        fetchDepth: 1
        fetchTags: false
        submodules: false
      - checkout: huracan
        path: huracan
        fetchDepth: 1
        fetchTags: false
        submodules: false

      - template: pipelines/templates/install.yml@huracan
        parameters:
          skipNativeDependencies: true
          skipInstall: true

      - bash: |
          pipelines/shared/scripts/get-cloud-manager-access-token-from-azure.sh
        displayName: Get Access Token
        env:
          ENV_CLIENTID: $(secretCloudIdClientId)
          ENV_CLIENTSECRET: $(secretCloudIdClientSecret)
          ENV_AUDIENCE: $(cloudIdAudience)
          ENV_SAGEID_OAUTH_TOKEN_URL: $(sageIdOAuthTokenUrl)

      # Create the tenant in the cloud manager
      - bash: |
          pipelines/tenants/scripts/create-tenant-v2.sh
        displayName: Create tenant ${{ parameters.tenantIdToCreate }}
        env:
          IS_PERFORMANCE_TEST: 'true'
          ACCESS_TOKEN: $(accessToken)
          API_URL: $(apiUrl)
          BUILD_ID: $(Build.BuildId)
          USER_EMAIL: $(userEmail)
          CUSTOMER_ID: $(customerId)
          TENANT_ID: ${{ parameters.tenantIdToCreate }}
          TENANTS_STATUS: $(tenantsStatus)

      - ${{ each import in parameters.importScenarios }}:
          # create a tenant for every importScenarios
          - bash: |
              pipelines/tenants/scripts/create-tenant-v2.sh
            displayName: Create tenant import_csv_${{ import.name }}
            env:
              IS_PERFORMANCE_TEST: 'true'
              ACCESS_TOKEN: $(accessToken)
              API_URL: $(apiUrl)
              BUILD_ID: $(Build.BuildId)
              USER_EMAIL: $(userEmail)
              CUSTOMER_ID: $(customerId)
              TENANT_ID: import_csv_${{ import.name }}

          # Provision the newly created tenant
          - bash: |
              pipelines/tenants/scripts/provision-tenant-v2.sh
            displayName: Provision tenant import_csv_${{ import.name }}
            env:
              ACCESS_TOKEN: $(accessToken)
              API_URL: $(apiUrl)
              BUILD_ID: $(Build.BuildId)
              BUILD_DIR: $(Build.Repository.LocalPath)
              CLUSTER: ${{ parameters.cluster }}
              LAYER: ${{ parameters.tenantLayer }}
              URL_SUFFIX: $(urlSuffix)
              USER_EMAIL: $(userEmail)
              TENANT_ID: import_csv_${{ import.name }}
              APP_ID: ${{ parameters.appId }}

      - ${{ if eq(parameters.startFrom, 'export') }}:
          # import data set for the new tenant(s). They will be created in the cluster
          - bash: |
              echo "\n\n\n********************   Import data for tenant ${{ parameters.tenantIdToCreate }} \n\n\n"
              pipelines/tenants/scripts/import-tenant-v2.sh ${{ parameters.sourceExportId }} ${{ parameters.cluster }} ${{ parameters.tenantIdToCreate }}
              if [ $? -ne 0 ]; then
                  # Import failed
                  exit 1
              fi
            displayName: 'import tenant data'
            env:
              ACCESS_TOKEN: $(accessToken)
              API_URL: $(apiUrl)
              ENV_CLIENTID: $(secretCloudIdClientId)
              ENV_CLIENTSECRET: $(secretCloudIdClientSecret)
              ENV_AUDIENCE: $(cloudIdAudience)
              ENV_SAGEID_OAUTH_TOKEN_URL: $(sageIdOAuthTokenUrl)
              URL_SUFFIX: $(urlSuffix)
              AWS_ACCESS_KEY_ID: $(AWS_ACCESS_KEY_ID)
              AWS_SECRET_ACCESS_KEY: $(AWS_SECRET_ACCESS_KEY)
              AWS_DEFAULT_REGION: $(REGION)
              MAX_DURATION_IN_SECONDS: 1800

      - ${{ if eq(parameters.startFrom, 'new tenant') }}:
          # Provision the tenants (create them in the cluster)
          - bash: |
              pipelines/performance-test/scripts/provision-tenant-v2.sh
            displayName: Provision tenants
            env:
              IS_PERFORMANCE_TEST: 'true'
              ACCESS_TOKEN: $(accessToken)
              API_URL: $(apiUrl)
              BUILD_ID: $(Build.BuildId)
              BUILD_DIR: $(Build.Repository.LocalPath)
              CLUSTER: ${{ parameters.cluster }}
              LAYER: ${{parameters.tenantLayer}}
              URL_SUFFIX: $(urlSuffix)
              USER_EMAIL: $(userEmail)
              TENANT_ID: ${{ parameters.tenantIdToCreate }}
              APP_ID: ${{ parameters.appId }}
              TENANTS_STATUS: $(tenantsStatus)

      - ${{ if ne(length(parameters.scenarios), 0) }}:
          - bash: |
              scripts/artillery/install.sh
            displayName: 'Install Artillery & Plugins'

      - bash: |
          mkdir -p $(htmlReportsDir)
        displayName: Create HTML reports publish dir

      # Execute the GraphQL init scenario
      - ${{ if ne(length(parameters.scenarios), 0) }}:
          - bash: |
              set -e
              ../../scripts/artillery/scenarios-test-run.sh --execution-type ${{ parameters.executionType }} --cluster ${{ parameters.cluster }} \
                --scenario sales-order/create \
                --total-users 1 --ramp-up 1 --duration 1 --tenant ${{ parameters.tenantIdToCreate }} --user-think-time 0 \
                --variables ${{ parameters.variables }}
              rm -f reports/*
            displayName: 'Run init scenario'
            workingDirectory: services/performance-tests

      - ${{ each scenario in parameters.scenarios }}:
          # Execute the artillery scenarios
          - bash: |
              set -e
              echo "##[group]${{ scenario }}"
              ../../scripts/artillery/scenarios-test-run.sh --execution-type ${{ parameters.executionType }} --cluster ${{ parameters.cluster }} \
                --scenario ${{ scenario }} \
                --total-users ${{ parameters.scenarioTotalUsers }} --ramp-up ${{ parameters.scenarioRampUp }} \
                --duration ${{ parameters.scenarioDuration }} \
                --tenant ${{ parameters.tenantIdToCreate }} --user-think-time ${{ parameters.userThinkTime }} \
                --html-reports-dir $(htmlReportsDir) \
                --variables ${{ parameters.variables }}
              echo "##[endgroup]"
            displayName: 'Scenario: ${{ scenario }}'
            workingDirectory: services/performance-tests
            continueOnError: true

      - task: PublishPipelineArtifact@1
        # Publish the result to Azure, the reportName variable is set in scenarios-test-run.sh
        inputs:
          targetPath: '$(htmlReportsDir)'
          artifact: 'PerformanceResult'
          publishLocation: 'pipeline'
        displayName: 'Publish artillery reports'

      - ${{ each import in parameters.importScenarios }}:
          - task: AmazonWebServices.aws-vsts-tools.AWSShellScript.AWSShellScript@1
            inputs:
              awsCredentials: 'aws-s3'
              regionName: 'eu-west-1'
              scriptType: 'inline'
              inlineScript: |
                S3_FOLDER_URI=s3://xtrem-developers-utility/performance-tests/import-csv-artifacts
                S3_FILE_URI=${S3_FOLDER_URI}/$1.tgz
                TARGET_FOLDER=$2
                mkdir -p ${TARGET_FOLDER}
                echo "##[command]aws s3 ls ${S3_FOLDER_URI}/"
                aws s3 ls ${S3_FOLDER_URI}/
                echo "##[group]copy ${S3_FILE_URI} to ${TARGET_FOLDER}/"
                echo "##[command]aws s3 cp ${S3_FILE_URI} ${TARGET_FOLDER}/$1.tgz"
                aws s3 cp ${S3_FILE_URI} ${TARGET_FOLDER}/$1.tgz
                echo "##[endgroup]"
              arguments: import-csv-${{ import.name }} $(Agent.TempDirectory)
              logRequest: true
              logResponse: true
            displayName: Downloading import files (${{ import.name }})

          - bash: |
              set -e
              ls -l $(Agent.TempDirectory)
              TAR_FILE=${IMPORT_DIR}.tgz
              echo "##[group]extract CSV files of ${TAR_FILE}"
              mkdir -p ${IMPORT_DIR}
              echo "##[command]tar -xzf ${TAR_FILE} --directory ${IMPORT_DIR}"
              tar -xzf ${TAR_FILE} --directory ${IMPORT_DIR}
              ls -l ${IMPORT_DIR}
              echo "##"##vso[task.setvariable variable=importCsvDir]${IMPORT_DIR}""
              echo "##[endgroup]"
            displayName: 'Extract CSV files: ${{ import.name }}'
            env:
              TENANT_ID: $(tenantId)
              IMPORT_DIR: '$(Agent.TempDirectory)/import-csv-${{ import.name }}'

          # Execute the import scenarios: the tgz must have been uploaded on s3://xtrem-developers-utility/performance-tests/import-csv-artifacts/
          - bash: |
              set -e
              set -x

              mkdir -p services/performance-tests/reports

              echo "##[group]list files to import"
              ls -l ${IMPORT_DIR}
              echo "##[endgroup]"
              TIMEOUT_SECONDS=${TIMEOUT_SECONDS:-"900"}
              POLLING_MILLIS=${POLLING_MILLIS:-"10000"}
              platform/performance-tests/tools/import.sh --email ${USER_EMAIL} \
                --timeout-seconds ${TIMEOUT_SECONDS} --polling-millis ${POLLING_MILLIS} \
                --import-dir ${IMPORT_DIR} --cluster ${{ parameters.cluster }} --tenant ${TENANT_ID} \
                --report-dir $(pwd)/services/performance-tests/reports --artillery-report --prefix ${{ parameters.executionType }}
            displayName: 'Import CSV: ${{ import.name }}'
            continueOnError: true
            env:
              TENANT_ID: import_csv_${{ import.name }}
              IMPORT_DIR: '$(importCsvDir)'
              USER_EMAIL: $(userEmail)
              TIMEOUT_SECONDS: ${{ import.timeoutSeconds }}
              POLLING_MILLIS: ${{ import.pollingMillis }}

      - ${{ if ne(parameters.executionType, 'manual') }}:
          - task: AmazonWebServices.aws-vsts-tools.S3Upload.S3Upload@1
            displayName: 'Upload detailed report to S3'
            condition: and(succeeded(), eq(variables['Build.SourceBranchName'], 'master'))
            continueOnError: true
            inputs:
              awsCredentials: 'aws-s3'
              regionName: 'eu-west-1'
              bucketName: 'xtrem-developers-utility'
              sourceFolder: '$(Build.Repository.LocalPath)/services/performance-tests/reports'
              globExpressions: '**'
              targetFolder: $(s3UploadTargetFolder)
              logRequest: true
              logResponse: true

          - bash: echo Results are uploaded to s3://xtrem-developers-utility/$(s3UploadTargetFolder)
            condition: and(succeeded(), eq(variables['Build.SourceBranchName'], 'master'))
            displayName: 'S3 upload info'

      - ${{ if eq(parameters.keepTenantsAlive, false) }}:
          # Delete the tenant
          - bash: |
              echo Tenant Id ${{ parameters.tenantIdToCreate }}
              pipelines/tenants/scripts/decommission-and-delete-tenant-v2.sh "${{ parameters.tenantIdToCreate }}" "${{ parameters.appId }}"
            name: delete_tenant
            displayName: Decommission and delete tenant ${{ parameters.tenantIdToCreate }}
            # if we created some tenant, we MUST try to delete them, whatever happened before, EVEN if the job was cancelled (that's why we use 'always')
            condition: always()
            env:
              IS_PERFORMANCE_TEST: 'true'
              ACCESS_TOKEN: $(accessToken)
              API_URL: $(apiUrl)
              BUILD_ID: $(Build.BuildId)
              URL_SUFFIX: $(urlSuffix)
              USER_EMAIL: $(userEmail)

          - ${{ each import in parameters.importScenarios }}:
              # delete the tenants that were created for every importScenarios
              - bash: |
                  echo Tenant Id ${{ parameters.tenantIdToCreate }}
                  pipelines/tenants/scripts/decommission-and-delete-tenant-v2.sh "import_csv_${{ import.name }}" "${{ parameters.appId }}"
                displayName: Decommission and delete tenant import_csv_${{ import.name }}
                # if we created some tenant, we MUST try to delete them, whatever happened before, EVEN if the job was cancelled (that's why we use 'always')
                condition: always()
                env:
                  IS_PERFORMANCE_TEST: 'true'
                  ACCESS_TOKEN: $(accessToken)
                  API_URL: $(apiUrl)
                  BUILD_ID: $(Build.BuildId)
                  URL_SUFFIX: $(urlSuffix)
                  USER_EMAIL: $(userEmail)

      - bash: |
          echo "##vso[task.logissue type=warning]Pipeline succeeded with issues, failed scenario results were not uploaded"
        displayName: 'Succeeded with issues'
        condition: eq(variables['Agent.JobStatus'], 'SucceededWithIssues')
