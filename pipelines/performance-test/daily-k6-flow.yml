name: daily-k6-flow

pr: none
trigger: none

resources:
  repositories:
    - repository: huracan
      type: github
      endpoint: Sage-ERP-X3
      name: Sage-ERP-X3/huracan
      # use stable branch v2.0 for huracan
      ref: v2.0

pool:
  vmImage: 'ubuntu-latest'

parameters:
  - name: appId
    displayName: app to provision for the tests
    type: string
    default: 'sdmo'

  - name: tenantIdToCreate
    displayName: id of the tenant to create for the tests
    type: string
    default: 'DAILY_____K6_____FLOW' # Must be 21 characters long

  - name: scenarios
    displayName: Scenarios to execute
    type: object
    default:
      - 'flow' # Will create new data

  - name: scenarioTotalUsers
    displayName: Number of users
    # Note: this parameter should be typed as a number, unfortunately we are using group variables for the daily/monthly
    # pipelines and numbers are not supported
    type: string
    default: 5

  - name: scenarioDuration
    displayName: Duration of the scenario in second
    # Note: this parameter should be typed as a number, unfortunately we are using group variables for the daily/monthly
    # pipelines and numbers are not supported
    type: string
    default: 3600 # 1h

  - name: cluster
    displayName: The cluster on which the tenants must be created
    type: string
    default: 'cls-perf-test'

  - name: sourceExportId
    displayName: The exportId of the data to restore (when start from is 'export')
    type: string
    default: 'perf-export-small'

  - name: keepTenantsAlive
    displayName: The tenants will be kept alive after the end of tests
    type: boolean
    default: false

variables:
  - group: sagex3ci_github
  - group: loadPerformanceTestEnvironmentVarsForDev
  - group: dockerXtrem
  - group: AWS_readWriteToClustersBuckets

jobs:
  - template: ./run-k6-validation.yml
    parameters:
      appId: ${{ parameters.appId }}
      tenantIdToCreate: ${{ parameters.tenantIdToCreate }}
      startFrom: 'export'
      scenarios: ${{ parameters.scenarios }}
      scenarioTotalUsers: ${{ parameters.scenarioTotalUsers }}
      scenarioDuration: ${{ parameters.scenarioDuration }}
      cluster: ${{ parameters.cluster }}
      sourceExportId: ${{ parameters.sourceExportId }}
      tenantLayer: 'none'
      executionType: 'daily'
