#!/bin/sh

# This script is executed in the services image (see docker-compose-upgrade-from-s3-backup.yml)
# To test it locally, you can run: (don't forget to replace XXX with the right values)
# XTREM_DOCKER_IMAGE=ghcr.io/sage-erp-x3/xtrem:latest PG_VERSION=16.1 BACKUP_S3_URI=s3://xtrem-developers-utility/dump-anon/prod/anon-eu-prd-latest.zip AWS_ACCESS_KEY_ID=XXX AWS_SECRET_ACCESS_KEY=XXX docker compose -f ./docker-compose-upgrade-from-s3-backup.yml up

set -e

echo $#
# parameters
# - the S3 URI of the backup to restore
if [ $# -ne 1 ]; then
    echo "Usage: run-upgrade <S3 URI of the backup>"
    exit 2
fi

displaySection () {
    startsLine="**************************************************************************************************************"
    printf "\n\n\n\n%s\n" "$startsLine"
    printf "**\n**\n"
    printf "**              %s\n" "$1"
    printf "**\n**\n"
    printf "%s\n\n\n\n" "$startsLine"
}

# Note: here, we are using a configuration with a 'xtrem' user
# so that the user will be created when creating the schema
# but this user will not be used when upgrading
# 'postgres' user will be used instead (recorded SQL files will refer to the 'xtrem' user
# so we need this user to exist)
cp /xtrem/config/config-for-schema-creation.yml /xtrem/app/xtrem-config.yml
displaySection "CREATE SCHEMA"
xtrem schema --create --reset-database --skip-tables

cp -f /xtrem/config/config-for-upgrade.yml /xtrem/app/xtrem-config.yml
displaySection "RESTORE FROM S3"
xtrem schema --restore-from-s3 "$1"
displaySection "UPGRADE"
xtrem upgrade --run --prod --metrics=s3
if [ "${XTREM_CHECK_SCHEMA}" = "True" ]; then
    displaySection "CHECK SCHEMA"
    xtrem schema --check
fi;
echo "Done"
