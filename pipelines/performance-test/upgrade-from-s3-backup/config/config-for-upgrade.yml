deploymentMode: production

# We need to set to cluster-id so that the S3 file for metrics will have a meaningful name
clusterId: 'upgrade-from-s3-backup'

sql:
  # Note: we need to use the postgres user because we are upgrading from
  # a anonymized backup of the prod environment where the xtrem user does not exist
  user: postgres
  password: secret
  hostname: 'pgdb'
  port: 5432
  sysUser: postgres
  sysPassword: secret
  database: xtrem
  sysDatabase: postgres

s3Storage:
  # The S3 bucket where the metrics will be written
  s3ClusterBucket: xtrem-developers-utility
  # The folder (from the S3 bucket) where the metrics will be written
  s3UpgradeMetricsFolder: upgrade-from-s3-backup
