# This pipeline will restore a backup from S3 and run the upgrade
# At the end of the upgrade, upgrade metrics will be exported to s3
name: upgrade-from-s3-backup

pr: none
trigger: none

pool: 'x3-ubuntu'

schedules:
  # Every day, at 01:00 AM
  - cron: '0 1 * * *'
    displayName: Upgrade from prod backups
    branches:
      include:
        - master

variables:
  - group: sagex3ci_github
  - group: dockerXtrem

jobs:
  - job: init_and_upgrade
    timeoutInMinutes: 90 # The upgrade from prod-eu can we very long
    displayName: 'Init and upgrade - '
    strategy:
      matrix:
        'prod-EU on latest release image (xtrem:latest-1)':
          releaseName: 'release'
          backupS3Uri: 's3://xtrem-developers-utility/dump-anon/prod/anon-eu-prd-latest.zip'
          checkSchema: 'False'
        'prod-NA on latest release image (xtrem:latest-1)':
          releaseName: 'release'
          backupS3Uri: 's3://xtrem-developers-utility/dump-anon/prod/anon-na-prd-latest.zip'
          checkSchema: 'False'
        'prod-EU on master image (xtrem:latest)':
          releaseName: 'master'
          backupS3Uri: 's3://xtrem-developers-utility/dump-anon/prod/anon-eu-prd-latest.zip'
          checkSchema: 'True'
        'prod-NA on master image (xtrem:latest)':
          releaseName: 'master'
          backupS3Uri: 's3://xtrem-developers-utility/dump-anon/prod/anon-na-prd-latest.zip'
          checkSchema: 'True'
    steps:
      - checkout: none
      - template: ../../shared/manual-git-clone.yml
        parameters:
          depth: 1

      # Login on nexus private registry
      - task: Docker@2
        displayName: Login to repository
        inputs:
          command: login
          containerRegistry: ghcr-ro

      - bash: |
          set -e
          if [ "$(releaseName)" == "master" ]; then
            XTREM_DOCKER_IMAGE='ghcr.io/sage-erp-x3/xtrem:latest'
          else
            # extract the current version from the package.json
            CURRENT_VERSION=$(cat package.json | jq -r ".version" | cut -d. -f1)
            LAST_RELEASE_NUMBER=$(($CURRENT_VERSION - 1))
            XTREM_DOCKER_IMAGE="ghcr.io/sage-erp-x3/xtrem:$LAST_RELEASE_NUMBER"
          fi;
          echo "##vso[task.setvariable variable=XtremDockerImage;]$XTREM_DOCKER_IMAGE"
          echo "##vso[task.setvariable variable=pg_version]$(cat .pgdbrc)"
        displayName: "Compute name of Xtrem docker image for '$(releaseName)'"

      - bash: |
          set -e
          echo "Matrix release name=$(releaseName)"
          echo "Matrix backup=$(backupS3Uri)"
          echo "Will restore the following backup from S3"
          aws s3 ls $(backupS3Uri)
          echo "Will use docker image $(XtremDockerImage)"
        displayName: 'Display parameters'
        env:
          AWS_ACCESS_KEY_ID: $(s3-xtrem-developers-utility-access-key)
          AWS_SECRET_ACCESS_KEY: $(s3-xtrem-developers-utility-secret)
          AWS_REGION: 'eu-west-1'

      - bash: |
          docker compose -f ./pipelines/performance-test/upgrade-from-s3-backup/docker-compose-upgrade-from-s3-backup.yml up --abort-on-container-exit --exit-code-from xtrem
        displayName: Start docker stack
        env:
          AWS_ACCESS_KEY_ID: $(s3-xtrem-developers-utility-access-key)
          AWS_SECRET_ACCESS_KEY: $(s3-xtrem-developers-utility-secret)
          AWS_REGION: eu-west-1
          BACKUP_S3_URI: $(backupS3Uri)
          XTREM_DOCKER_IMAGE: $(XtremDockerImage)
          XTREM_CHECK_SCHEMA: $(checkSchema)
          XTREM_SCHEMA_NAME: 'sdmo'
