version: '3'
services:
  pgdb:
    image: postgres:${PG_VERSION}-alpine
    shm_size: 1g
    restart: always
    command: >
      -c max_locks_per_transaction=256
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=secret
  xtrem:
    image: ${XTREM_DOCKER_IMAGE}
    command: >
      /xtrem/scripts/run-upgrade.sh ${BACKUP_S3_URI}
    volumes:
      - ./config:/xtrem/config
      - ./scripts:/xtrem/scripts
    environment:
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_DEFAULT_REGION=${AWS_REGION}
      - BACKUP_S3_URI=${BACKUP_S3_URI}
      - XTREM_DOCKER_IMAGE=${XTREM_DOCKER_IMAGE}
      - XTREM_CHECK_SCHEMA=${XTREM_CHECK_SCHEMA}
      - XTREM_SCHEMA_NAME=${XTREM_SCHEMA_NAME}
    depends_on:
      - pgdb
