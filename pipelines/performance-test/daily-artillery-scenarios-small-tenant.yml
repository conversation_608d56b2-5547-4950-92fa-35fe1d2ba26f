name: daily-artillery-scenarios-small-tenant

pr: none
trigger: none

resources:
  repositories:
    - repository: huracan
      type: github
      endpoint: Sage-ERP-X3
      name: Sage-ERP-X3/huracan
      # use stable branch v2.0 for huracan
      ref: v2.0
  pipelines:
    - pipeline: daily-export-small-tenant # Name of the pipeline resource (local reference).
      source: daily-export-small-tenant # The name of the pipeline referenced by this pipeline resource.
      trigger:
        branches:
          - master

pool:
  vmImage: 'ubuntu-latest'

parameters:
  - name: appId
    displayName: app to provision for the tests
    type: string
    default: 'sdmo'

  - name: tenantIdToCreate
    displayName: id of the tenant to create for the tests
    type: string
    default: 'PERF_TEST__SMALL_DATA'

  - name: scenarios
    displayName: Scenarios to execute
    type: object
    default:
      - 'sales-order/create'
      - 'complete-flow/sales-order'
      - 'metadata/purchase-order-page'

  - name: keepTenantsAlive
    displayName: The tenants will be kept alive after the end of tests
    type: boolean
    default: false

  - name: variables
    displayName: Other variables passed to the scenario
    type: string
    default: '{}'

variables:
  - group: sagex3ci_github
  - group: loadPerformanceTestEnvironmentVarsForDev
  - group: dailyPerformanceTests
  - group: dockerXtrem
  - group: AWS_readWriteToClustersBuckets

jobs:
  - template: ./run-artillery-scenarios-template.yml
    parameters:
      appId: ${{ parameters.appId }}
      tenantIdToCreate: ${{ parameters.tenantIdToCreate }}
      startFrom: 'export'
      scenarios: ${{ parameters.scenarios }}
      scenarioTotalUsers: $(scenarioTotalUsers)
      scenarioDuration: $(scenarioDuration)
      scenarioRampUp: $(scenarioRampUp)
      userThinkTime: $(userThinkTime)
      importScenarios:
        - name: 'small'
          email: '<EMAIL>'
          timeoutSeconds: 1800
          pollingMillis: 10000
      cluster: $(cluster)
      sourceExportId: $(exportId)
      tenantLayer: 'none'
      keepTenantsAlive: ${{ parameters.keepTenantsAlive }}
      executionType: 'daily'
      variables: ${{ parameters.variables }}
