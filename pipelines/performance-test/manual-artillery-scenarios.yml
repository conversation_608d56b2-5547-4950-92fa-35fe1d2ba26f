name: manual-artillery-scenarios

pr: none
trigger: none

resources:
  repositories:
    - repository: huracan
      type: github
      endpoint: Sage-ERP-X3
      name: Sage-ERP-X3/huracan
      # use stable branch v2.0 for huracan
      ref: v2.0

pool:
  vmImage: 'ubuntu-latest'

parameters:
  - name: appId
    displayName: app to provision for the tests
    type: string
    default: 'sdmo'

  - name: tenantIdToCreate
    displayName: id of the tenant to create for the tests
    type: string
    default: 'PERF_TEST__SMALL_DATA'

  - name: scenarios
    displayName: Scenarios to execute
    type: object
    default:
      - 'sales-order/create'

  - name: scenarioTotalUsers
    displayName: Number of users
    type: number
    default: 20

  - name: scenarioDuration
    displayName: Duration of the scenario in second
    type: number
    default: 120

  - name: scenarioRampUp
    displayName: Users Ramp-up
    type: number
    default: 5

  - name: userThinkTime
    displayName: User Think Time
    type: string
    default: 0

  - name: cluster
    displayName: The cluster on which the tenants must be created
    type: string
    default: 'cls-perf-test'

  - name: sourceExportId
    displayName: The exportId of the data to restore (when start from is 'export')
    type: string
    default: 'perf-export-small'

  - name: importScenarios
    displayName: Import scenarios to execute
    type: object
    default: []

  - name: keepTenantsAlive
    displayName: The tenants will be kept alive after the end of tests
    type: boolean
    default: false

  - name: variables
    displayName: Other variables passed to the scenario
    type: string
    default: '{}'

variables:
  - group: sagex3ci_github
  - group: loadPerformanceTestEnvironmentVarsForDev
  - group: dockerXtrem
  - group: AWS_readWriteToClustersBuckets

jobs:
  - template: ./run-artillery-scenarios-template.yml
    parameters:
      appId: ${{ parameters.appId }}
      tenantIdToCreate: ${{ parameters.tenantIdToCreate }}
      startFrom: 'export'
      scenarios: ${{ parameters.scenarios }}
      scenarioTotalUsers: ${{ parameters.scenarioTotalUsers }}
      scenarioDuration: ${{ parameters.scenarioDuration }}
      scenarioRampUp: ${{ parameters.scenarioRampUp }}
      userThinkTime: ${{ parameters.userThinkTime }}
      importScenarios: ${{ parameters.importScenarios }}
      cluster: ${{ parameters.cluster }}
      sourceExportId: ${{ parameters.sourceExportId }}
      tenantLayer: 'none'
      keepTenantsAlive: ${{ parameters.keepTenantsAlive }}
      executionType: 'manual'
      variables: ${{ parameters.variables }}
