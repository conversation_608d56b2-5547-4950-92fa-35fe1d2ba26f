# Template to execute the artillery scenarios on a cluster
parameters:
  - name: appId
    displayName: app to provision for the tests
    type: string

  - name: tenantIdToCreate
    displayName: id of the tenant to create for the tests
    type: string
    default: 'not_set_perf_test'

  - name: startFrom
    displayName: Start from
    type: string
    values:
      - export
      - new tenant
    default: export

  - name: scenarios
    displayName: Scenarios to execute
    type: object
    default:
      - 'flow'
      - 'perf'

  - name: scenarioTotalUsers
    displayName: Number of users
    # Note: this parameter should be typed as a number, unfortunately we are using group variables for the daily/monthly
    # pipelines and numbers are not supported
    type: string
    default: 1

  - name: scenarioDuration
    displayName: Duration of the scenario in second
    # Note: this parameter should be typed as a number, unfortunately we are using group variables for the daily/monthly
    # pipelines and numbers are not supported
    type: string
    default: 1

  - name: cluster
    displayName: The cluster on which the tenant must be created
    type: string
    default: 'cls-perf-test'

  - name: sourceExportId
    displayName: The exportId of the data to restore (when start from is 'export')
    type: string
    default: 'provide the same export id as in the "export" pipeline'

  - name: tenantLayer
    displayName: Layer for provisioning the tenant (when start from is 'new tenant')
    type: string
    default: 'test'
    values:
      - none
      - test
      - qa
      - demo

  - name: executionType
    displayName: The type of execution
    type: string
    default: 'manual'
    values:
      - manual
      - daily
      - monthly

  - name: timeoutInMinutes
    displayName: timeout in minutes passed to the scenario
    type: number
    default: 100

jobs:
  - job: Provisioning_tenant_Execute_tests_Delete_tenant
    displayName: Tenant provisioning steps, test execution steps, delete tenant steps.
    timeoutInMinutes: ${{parameters.timeoutInMinutes}}
    variables:
      exitMessage: 'An error occurred, see build results'
      accessToken: ''
      s3UploadTargetFolder: performance-tests/k6-json-reports
      htmlReportsDir: $(Agent.TempDirectory)/k6-html-reports/
      jsonReportsDir: $(Agent.TempDirectory)/k6-json-reports/

    steps:
      - checkout: self
        # this makes this repo being the default working directory,
        # to reference it explicitly, use $(Build.Repository.LocalPath) instead of $(Build.SourcesDirectory)
        workspaceRepo: true
        path: xtrem
        fetchDepth: 1
        fetchTags: false
        submodules: false
      - checkout: huracan
        path: huracan
        fetchDepth: 1
        fetchTags: false
        submodules: false

      - template: pipelines/templates/install.yml@huracan
        parameters:
          skipNativeDependencies: true
          skipInstall: false

      - bash: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
        displayName: install k6

      - bash: |
          pipelines/shared/scripts/get-cloud-manager-access-token-from-azure.sh
        displayName: Get Access Token
        env:
          ENV_CLIENTID: $(secretCloudIdClientId)
          ENV_CLIENTSECRET: $(secretCloudIdClientSecret)
          ENV_AUDIENCE: $(cloudIdAudience)
          ENV_SAGEID_OAUTH_TOKEN_URL: $(sageIdOAuthTokenUrl)

      # Delete the tenant before starting the tests
      - bash: |
          echo Tenant Id ${{ parameters.tenantIdToCreate }}
          pipelines/tenants/scripts/decommission-and-delete-tenant-v2.sh "${{ parameters.tenantIdToCreate }}" "${{ parameters.appId }}"
        name: delete_tenant
        displayName: Decommission and delete tenant ${{ parameters.tenantIdToCreate }}
        # if we created some tenant, we MUST try to delete them, whatever happened before, EVEN if the job was cancelled (that's why we use 'always')
        condition: always()
        env:
          IS_PERFORMANCE_TEST: 'true'
          ACCESS_TOKEN: $(accessToken)
          API_URL: $(apiUrl)
          BUILD_ID: $(Build.BuildId)
          URL_SUFFIX: $(urlSuffix)
          USER_EMAIL: $(userEmail)

      # Create the tenant in the cloud manager
      # this shell script will set the following variables:
      #   - tenantId
      #   - tenantName
      #   - tenantcreateSuccess
      - bash: |
          pipelines/tenants/scripts/create-tenant-v2.sh
        displayName: Create tenant ${{ parameters.tenantIdToCreate }}
        env:
          IS_PERFORMANCE_TEST: 'true'
          ACCESS_TOKEN: $(accessToken)
          API_URL: $(apiUrl)
          BUILD_ID: $(Build.BuildId)
          USER_EMAIL: $(userEmail)
          CUSTOMER_ID: $(customerId)
          TENANT_ID: ${{ parameters.tenantIdToCreate }}
          TENANTS_STATUS: $(tenantsStatus)

      - ${{ if eq(parameters.startFrom, 'export') }}:
          # import data set for the new tenant(s). They will be created in the cluster
          - bash: |
              echo "\n\n\n********************   Import data for tenant ${{ parameters.tenantIdToCreate }} \n\n\n"
              pipelines/tenants/scripts/import-tenant-v2.sh ${{ parameters.sourceExportId }} ${{ parameters.cluster }} ${{ parameters.tenantIdToCreate }}
              if [ $? -ne 0 ]; then
                  # Import failed
                  exit 1
              fi
            displayName: 'import tenant data'
            env:
              ACCESS_TOKEN: $(accessToken)
              API_URL: $(apiUrl)
              ENV_CLIENTID: $(secretCloudIdClientId)
              ENV_CLIENTSECRET: $(secretCloudIdClientSecret)
              ENV_AUDIENCE: $(cloudIdAudience)
              ENV_SAGEID_OAUTH_TOKEN_URL: $(sageIdOAuthTokenUrl)
              URL_SUFFIX: $(urlSuffix)
              AWS_ACCESS_KEY_ID: $(AWS_ACCESS_KEY_ID)
              AWS_SECRET_ACCESS_KEY: $(AWS_SECRET_ACCESS_KEY)
              AWS_DEFAULT_REGION: $(REGION)
              MAX_DURATION_IN_SECONDS: 1800

      - ${{ if eq(parameters.startFrom, 'new tenant') }}:
          # Provision the tenants (create them in the cluster)
          - bash: |
              pipelines/performance-test/scripts/provision-tenant-v2.sh
            displayName: Provision tenants
            env:
              IS_PERFORMANCE_TEST: 'true'
              ACCESS_TOKEN: $(accessToken)
              API_URL: $(apiUrl)
              BUILD_ID: $(Build.BuildId)
              BUILD_DIR: $(Build.Repository.LocalPath)
              CLUSTER: ${{ parameters.cluster }}
              LAYER: ${{parameters.tenantLayer}}
              URL_SUFFIX: $(urlSuffix)
              USER_EMAIL: $(userEmail)
              TENANT_ID: ${{ parameters.tenantIdToCreate }}
              APP_ID: ${{ parameters.appId }}
              TENANTS_STATUS: $(tenantsStatus)

      - bash: |
          mkdir -p $(htmlReportsDir)
          mkdir -p $(jsonReportsDir)
        displayName: Create HTML reports publish dir

      - bash: |
          pnpm --dir services/performance-tests/k6/ build
        displayName: Build k6 project

      - ${{ each scenario in parameters.scenarios }}:
          # Execute the k6 scenarios
          - bash: |
              set -e
              echo "##[group]${{ scenario }}"
              pnpm --dir services/performance-tests/k6/ run:${{ scenario }} --quiet
              echo "##[endgroup]"
            displayName: 'Scenario: ${{ scenario }}'
            env:
              SERVER: 'eu.dev'
              CLUSTER: ${{ parameters.cluster }}
              TENANT_ID: $(tenantId)
              USER: $(userEmail)
              APP: ${{ parameters.appId }}
              K6_VUS: ${{ parameters.scenarioTotalUsers }}
              K6_DURATION: '${{ parameters.scenarioDuration }}s'
              K6_REPORT_DIR: '$(jsonReportsDir)'
              K6_REPORT_DIR_WEB: '$(htmlReportsDir)'
            continueOnError: true

      # =============================================
      # SEE LATER THE UPLOAD TO S3
      # =============================================
      - ${{ if eq(parameters.executionType, 'manual') }}:
          - task: PublishPipelineArtifact@1
            # Publish the result to Azure, the reportName variable is set in scenarios-test-run.sh
            inputs:
              targetPath: '$(htmlReportsDir)'
              artifact: 'PerformanceResult'
              publishLocation: 'pipeline'
            displayName: 'Publish k6 reports'

      - ${{ if ne(parameters.executionType, 'manual') }}:
          - task: AmazonWebServices.aws-vsts-tools.S3Upload.S3Upload@1
            displayName: 'Upload detailed report to S3'
            condition: and(succeeded(), eq(variables['Build.SourceBranchName'], 'master'))
            continueOnError: true
            inputs:
              awsCredentials: 'aws-s3'
              regionName: 'eu-west-1'
              bucketName: 'xtrem-developers-utility'
              sourceFolder: '$(jsonReportsDir)'
              globExpressions: '**'
              targetFolder: $(s3UploadTargetFolder)
              logRequest: true
              logResponse: true

          - bash: echo Results are uploaded to s3://xtrem-developers-utility/$(s3UploadTargetFolder)
            condition: and(succeeded(), eq(variables['Build.SourceBranchName'], 'master'))
            displayName: 'S3 upload info'

      - bash: |
          echo "##vso[task.logissue type=warning]Pipeline succeeded with issues, failed scenario results were not uploaded"
        displayName: 'Succeeded with issues'
        condition: eq(variables['Agent.JobStatus'], 'SucceededWithIssues')
