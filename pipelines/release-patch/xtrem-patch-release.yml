name: xtrem-patch-release

pr: none
trigger: none

schedules:
  # cron is UTC time based but devo<PERSON> and mindo use French local time with Daylight Saving Time
  # we need to start the job at a time in accordance with winter and summer time.
  # start at 20:00 UTC to help devops complete the full pipeline chain on time
  - cron: '0 20 * * *'
    displayName: Nightly build
    branches:
      include:
        - master

parameters:
  - name: dryRun
    displayName: 'DRY RUN: no publish, no push'
    type: boolean
    default: false

  - name: skipS3
    # This variable will skip all the 'S3' related tasks/scripts
    displayName: 'Skip publication to S3 bucket (WARNING: dry run !!)'
    type: boolean
    default: false

  - name: buildDevCli
    displayName: 'Build Dev CLI image (ignored on master)'
    type: boolean
    default: false

  - name: buildGlossary
    displayName: 'Build Glossary image (ignored on master)'
    type: boolean
    default: false

  - name: buildServices
    displayName: 'Build Services image (ignored on master)'
    type: boolean
    default: false

  - name: buildShopfloor
    displayName: 'Build Shopfloor image (ignored on master)'
    type: boolean
    default: false

  - name: buildShowcase
    displayName: 'Build Showcase image (ignored on master)'
    type: boolean
    default: false

  - name: buildX3Services
    displayName: 'Build X3 Services image (ignored on master)'
    type: boolean
    default: false

  - name: buildWHServices
    displayName: 'Build WH Services image (ignored on master)'
    type: boolean
    default: false

  - name: buildX3Connector
    displayName: 'Build X3 Connector image (ignored on master)'
    type: boolean
    default: false

  - name: poolName
    displayName: 'Pool to use'
    type: string
    values:
      - 'x3-ubuntu'
      - 'ubuntu-latest'
    default: 'x3-ubuntu'

jobs:
  - template: ../shared/release-job-template.yml
    parameters:
      poolName: ${{ parameters.poolName }}
      bumpKind: patch
      imagePipelinesDef:
        # WARNING: name MUST not contain space
        - name: cli-dev
          build: ${{ parameters.buildDevCli }}
          id: 3469
        - name: glossary
          build: ${{ parameters.buildGlossary }}
          id: 852
        - name: services
          build: ${{ parameters.buildServices }}
          id: 653
        - name: shopfloor
          build: ${{ parameters.buildShopfloor }}
          id: 3208
        - name: showcase
          build: ${{ parameters.buildShowcase }}
          id: 994
        - name: x3-services
          build: ${{ parameters.buildX3Services }}
          id: 4142
        - name: wh-services
          build: ${{ parameters.buildWHServices }}
          id: 4914
        - name: x3-connector
          build: ${{ parameters.buildX3Connector }}
          id: 4748
      dryRun: ${{ parameters.dryRun }}
      skipUpgrade: false
      skipS3: ${{ parameters.skipS3 }}
      pipelineId: 1286
      pipelineName: 'xtrem-patch-release'
