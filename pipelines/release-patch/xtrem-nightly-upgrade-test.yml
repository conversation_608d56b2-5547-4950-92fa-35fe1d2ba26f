name: xtrem-nightly-upgrade-test

pr: none
trigger: none

parameters:
  - name: backups
    displayName: 'List of backups type to restore'
    type: object
    default:
      - name: 'clusterDevRelease'
        backupType: '--s3ConfigType=clusterDevRelease'
      - name: 'clusterQaRelease'
        backupType: '--s3ConfigType=clusterQaRelease'
      - name: 'prod-eu'
        backupType: 's3://xtrem-developers-utility/dump-anon/prod/anon-eu-prd-latest.zip'
        timeoutInMinutes: 150
      - name: 'prod-na'
        backupType: 's3://xtrem-developers-utility/dump-anon/prod/anon-na-prd-latest.zip'
        timeoutInMinutes: 150

# This pipeline is triggered by a dependency on xtrem-services-patch-release build
# see https://docs.microsoft.com/en-us/azure/devops/pipelines/process/pipeline-triggers?view=azure-devops&tabs=yaml
resources:
  repositories:
    - repository: huracan
      type: github
      endpoint: Sage-ERP-X3
      name: Sage-ERP-X3/huracan
      # branch name to use
      ref: v4.2

  pipelines:
    - pipeline: xtremPatchRelease # Name of the pipeline resource
      source: xtrem-patch-release # Name of the pipeline referenced by the pipeline resource
      trigger:
        branches:
          - master

pool: 'x3-ubuntu'

variables:
  - group: sagex3ci_github
  - group: dockerXtrem
  - name: nodeVersion
    value: ''

stages:
  - stage: install_and_build
    displayName: 'Build and install'
    dependsOn: []
    jobs:
      - job: install_and_build
        workspace:
          clean: all
        displayName: 'Build and install'
        steps:
          - checkout: none

          - template: pipelines/templates/misc/setup-bash-env.yml@huracan

          - template: pipelines/templates/git/manual-git-clone.yml@huracan
            parameters:
              # DO NOT increase this value, if more history is required for upgrades, it is managed by using git fetch --deepen command
              # just make sure that git is correctly configured in the job that does the upgrade (see git-configure.yml template)
              depth: 100
          - template: pipelines/templates/install.yml@huracan
            parameters:
              skipNativeDependencies: true

          # Build the sources
          - script: pnpm run build
            displayName: 'Build'
            env:
              XTREM_CI: 1

          # Cache the result of the build
          - template: pipelines/templates/store-to-azure-cache.yml@huracan
            parameters:
              cacheKey: 'cache_build_normal'

  - stage: upgrade_clusterDev
    displayName: 'Upgrade clusters'
    dependsOn: install_and_build
    jobs:
      - ${{ each backup in parameters.backups }}:
          - job: upgrade_${{ replace(backup.name, '-', '_') }}
            timeoutInMinutes: ${{ coalesce(backup.timeoutInMinutes, 60) }} # Default timeout is 60 minutes, can be overridden by the backup definition
            workspace:
              clean: all
            displayName: 'Upgrade ${{ backup.name }}'
            steps:
              - checkout: none
              # Restore the sources + install + build from the cache
              - template: pipelines/templates/read-from-azure-cache.yml@huracan
                parameters:
                  cacheKey: 'cache_build_normal'
                  skipNativeDependencies: true
              - template: ../shared/xtrem-nightly-upgrade-test-single-cluster.yml
                parameters:
                  backupType: ${{ backup.backupType }}
                  applicationRootFolder: $(Build.SourcesDirectory)/services/main/xtrem-services-main
                  applicationDisplayName: 'sdmo'
                  appName: 'sdmo'
