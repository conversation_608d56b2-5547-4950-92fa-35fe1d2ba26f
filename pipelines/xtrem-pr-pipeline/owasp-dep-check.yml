parameters:
  - name: 'scan_path'
    type: string
    default: ''
  - name: 'scan_opts'
    type: string
    default: ''
  - name: 'tf_appkey'
    type: string
    default: ''
  - name: 'tf_url'
    type: string
    default: 'https://fix.gsecnet.com/rest/latest'

steps:
  - task: dependency-check-build-task@5
    displayName: 'OWASP Dependency Check'
    inputs:
      projectName: '$(Build.DefinitionName)'
      scanPath: ${{ parameters.scan_path }}
      format: 'XML'
      enableExperimental: true
      # to prevent error: "Could not perform Node Audit analysis. Invalid payload submitted to Node Audit API."
      additionalArguments: ${{ parameters.scan_opts }}
    continueOnError: true

  - powershell: |
      # Enable PS to work with TLS
      [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12

      $TF_APPKEY = "${{ parameters.tf_appkey }}"
      $TF_URL = "${{ parameters.tf_url}}"
      # Works for one file only
      $TF_APPID_URL = "$TF_URL/applications/allTeamLookup?uniqueId=$TF_APPKEY"
      $TF_APIKey = "$(TF_APIKey)"

      # Header
      $headers = New-Object "System.Collections.Generic.Dictionary[[String],[String]]"
      $headers.Add("Authorization", "APIKEY " + $TF_APIKey)
      $headers.Add("Accept", "application/json")

      # Get TF_APPID knowing unique key
      $_attempts = 1
      $_looperror = 0
      Write-Host "Trying to get TF_APPID from" $TF_APPID_URL
      do 
      { 
        $_results = try { Invoke-RestMethod -Uri $TF_APPID_URL -Headers $headers -Body $Body } catch { $_.Exception.Response }
          $_httpcode = $_results.StatusCode
          if (!$_httpcode)
          {
              $_looperror = 0
              $_attempts = 5
          } else {
              Write-Host "##vso[task.LogIssue type=warning;]Attempt $_attempts failed due" $_results.StatusCode
              $_attempts++
              $_looperror = 1
              Start-Sleep 4
          } 
      } while ( $_attempts -le 4 )
      if ( $_looperror -eq 1 )
      {
        Write-Host "##vso[task.LogIssue type=error;]Failed to get APP_ID. Cannot upload scans."
        Write-Host "##vso[task.complete result=SucceededWithIssues;]"
      } else {
        $TF_APPID = $_results.object.id
        Write-Host "##vso[task.setvariable variable=TF_APPID]$TF_APPID"
        Write-Host "Got TF_APPID $TF_APPID from unique key $TF_APPKEY"
      }

      $TF_UPLOAD_URL = "$TF_URL/applications/" + $TF_APPID + "/upload"

      # File
      $FileUp = "$(Agent.BuildDirectory)\TestResults\dependency-check\dependency-check-report.xml"
      $FileName = Split-path $FileUp -Leaf
      $FileBytes = [System.IO.File]::ReadAllBytes($FileUp)
      $FileEnc = [System.Text.Encoding]::GetEncoding('UTF-8').GetString($FileBytes)

      # Body
      $Boundary = [System.Guid]::NewGuid().ToString()
      $LF = "`r`n"
      $Body = @(
      "--$Boundary",
          "Content-Disposition: form-data; name=`"file`"; filename=`"$FileName`"",
          "Content-Type: application/octet-stream$LF",
          $FileEnc,
          "--$Boundary--$LF" 
      ) -join $LF

      # Upload Files in four attempts
      $_attempts = 1
      $_looperror = 0
      do 
      { 
          $_results = try { Invoke-RestMethod -Uri $TF_UPLOAD_URL -Headers $headers -Method Post -ContentType "multipart/form-data; boundary=`"$Boundary`"" -Body $Body } catch { $_.Exception.Response }
          $_httpcode = $_results.StatusCode
          if (!$_httpcode)
          {
              $_looperror = 0
              $_attempts = 5
          } else {
              Write-Host "##vso[task.LogIssue type=warning;]Attempt $_attempts failed due" $_results.StatusCode
              $_attempts++
              $_looperror = 1
              Start-Sleep 4
          } 
      } while ( $_attempts -le 4 )        
      if ( $_looperror -eq 1 )
      {
        Write-Host "##vso[task.LogIssue type=error;]Failed to upload the scan to Threafix! Do it manually via Threadfix Application page."
        Write-Host "##vso[task.complete result=SucceededWithIssues;]"
      } else {
        Write-Host "OWASP scan Uploaded to Threadfix for Application $TF_APPID"
      }

    displayName: 'TF Upload OWASP Dependency Check'
    condition: succeededOrFailed()
