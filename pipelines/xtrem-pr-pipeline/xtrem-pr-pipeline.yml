# this pipeline is applied to every PR to validate it

parameters:
  - name: resourcesBranch
    type: string
    # change the default, to debug the pipeline with a specific branch or stick th pipeline to a specific branch version
    # If you need to test an unpublished version of xdev, you should set the linkXdev parameter to true below
    # default: 'feat/sync-xdev-0523--xt-91267'
    # default: 'master'
    default: 'v4.2'

resources:
  repositories:
    - repository: huracan
      type: github
      endpoint: Sage-ERP-X3
      name: Sage-ERP-X3/huracan
      # branch name to use
      ref: ${{ parameters.resourcesBranch }}

# Display name
name: xtrem-mono

# disable branch triggering
trigger: none

schedules:
  - cron: '0 3,4,5 * * *'
    displayName: Daily code coverage update
    branches:
      include:
        - master

# Scanning settings
pr:
  autoCancel: true
  branches:
    include:
      - master
      - release/*

extends:
  template: pipelines/templates/pull-request-template.yml@huracan
  parameters:
    poolName: 'X3-Ubuntu'
    resources:
      ref: '${{ parameters.resourcesBranch }}'
      # use the resourcesBranch to develop xdev in parallel with the pipeline without having to publish it,
      # then set it to false to use the version published in the npm registry and referenced in the package.json
      linkXdev: false
    config:
      fetchDepth: 500
      # The simplified pipeline is a new feature that reduce the number of stages in the pipeline.
      # It will combine the checkout, install and build stages into a single stage.
      # This will reduce the time it takes to run the pipeline. (default: false)
      simplified: false
      # Should we build in binary mode (default: false, ignored if simplified is true).
      skipBuildBinary: false
      # We need to set the TURBO_* variables in the pipeline config to be able to build the turbo version.
      # See: https://sage-liveservices.visualstudio.com/X3%20XTREM/_apps/hub/ms.vss-build-web.ci-designer-hub?pipelineId=5411&branch=master
      # Should we build in turbo mode (default: false, ignored if simplified is true).
      skipBuildTurbo: false
      # Should we build in normal mode  (default: false, ignored if simplified is true).
      skipBuildNormal: false
      # Should we run the linting steps (default: false)
      skipLinting: false
      # Should we run the coverage steps (default: false)
      skipCoverage: false
      # Should we run tests on the sql files (default: false)
      skipTestSqlFiles: false
      # Should we run the unit tests (default: false)
      skipUnitTests: false
      # Should we run the integration tests (default: false)
      skipIntegrationTests: false
      # Should we run the cluster upgrade smoke tests (default: false)
      skipClusterSmokeTests: false
      # Should we run the CSV layers tests (default: false)
      skipCsvLayersTests: false
      # Path to the xtrem config file to use for the cluster smoke tests
      xtremReleaseConfig: 'pipelines/release-patch/xtrem-config-for-patch-release.yml'

    scopes:
      #========================================
      # scope - platform
      #========================================
      - name: 'platform-back-end'
        slug: 'platform'
        appName: 'showcase'
        applicationDisplayName: 'Platform: Back-end'
        applicationRootFolder: 'platform/show-case/xtrem-show-case'
        sqlFilesTest:
          enabled: false
          skipTenantTests: true
        integrationTests:
          - name: platform1_1
            displayName: 'Platform - 1.1'
            maxInstances: 2
            pattern: 'test/cucumber/1-1-*.feature'
            scopes: 'platform/show-case/xtrem-show-case$'
          - name: platform1_2
            displayName: 'Platform - 1.2'
            maxInstances: 2
            pattern: 'test/cucumber/1-2-*.feature'
            scopes: 'platform/show-case/xtrem-show-case$'
          - name: platform2_1
            displayName: 'Platform - 2.1'
            maxInstances: 2
            pattern: 'test/cucumber/2-1-*.feature'
            scopes: 'platform/show-case/xtrem-show-case$'
          - name: platform2_2
            displayName: 'Platform - 2.2'
            maxInstances: 2
            pattern: 'test/cucumber/2-2-*.feature'
            scopes: 'platform/show-case/xtrem-show-case$'
          - name: platform3_1
            displayName: 'Platform - 3.1'
            maxInstances: 2
            pattern: 'test/cucumber/3-1-*.feature'
            serviceOptions: 'showCaseWorkInProgressOption,showCaseDiscountOption'
            scopes: 'platform/show-case/xtrem-show-case$'
          - name: platform3_2
            displayName: 'Platform - 3.2'
            maxInstances: 2
            pattern: 'test/cucumber/3-2-*.feature'
            serviceOptions: 'showCaseWorkInProgressOption,showCaseDiscountOption'
            scopes: 'platform/show-case/xtrem-show-case$'
          - name: platform4_1
            displayName: 'Platform - 4.1'
            maxInstances: 2
            pattern: 'test/cucumber/4-1-*.feature'
            serviceOptions: 'showCaseWorkInProgressOption,showCaseDiscountOption'
            scopes: 'platform/show-case/xtrem-show-case$'
          - name: platform4_2
            displayName: 'Platform - 4.2'
            maxInstances: 2
            pattern: 'test/cucumber/4-2-*.feature'
            serviceOptions: 'showCaseWorkInProgressOption,showCaseDiscountOption'
            scopes: 'platform/show-case/xtrem-show-case$'
          - name: platform5
            displayName: 'Platform - 5 (Dashboard and single instance tests)'
            maxInstances: 1
            pattern: 'test/cucumber/5-y-*.feature'
            scopes: 'platform/show-case/xtrem-show-case$'
          - name: platformz
            displayName: 'Platform - z (Visual Regression)'
            maxInstances: 2
            pattern: 'test/cucumber/z-visual-regression-[0-9].feature'
            scopes: 'platform/show-case/xtrem-show-case$'
          - name: platform7
            displayName: 'Platform - 7 (Extensions)'
            maxInstances: 2
            pattern: 'test/cucumber/7-*.feature'
            scopes: 'platform/show-case/xtrem-show-case-bundle$'
          - name: platform8
            displayName: 'Platform - 8 (Prod mode)'
            maxInstances: 1
            pattern: 'test/cucumber/8-z-prod-*.feature'
            scopes: 'platform/show-case/xtrem-show-case$'
            productionMode: true
        clusterSmokeTests:
          enabled: true
          backups:
            - name: 'showcase_ci'
              displayName: 'Showcase (ci)'
              config: '--s3ConfigType=showcase'

      - name: 'platform-front-end'
        applicationDisplayName: 'Platform: Front-end'
        applicationRootFolder: 'platform/show-case/xtrem-show-case'
        sqlFilesTest:
          enabled: false
          skipTenantTests: true

      #========================================
      # scope - services
      #========================================
      - name: services
        appName: 'sdmo'
        applicationDisplayName: 'SDMO'
        applicationRootFolder: 'services/main/xtrem-services-main'
        sqlFilesTest:
          enabled: true
          skipTenantTests: false
          backupTypes:
            - name: '--s3ConfigType=sdmo_cu'
              appName: 'sdmo'
            - name: '--s3ConfigType=sdmo'
              skipTenantTests: true
              appName: 'sdmo'
        integrationTests:
          - name: services1_1
            displayName: 'Services - 1 Smoke test static - shared'
            maxInstances: 4
            pattern: 'smoke-test-static'
            scopes: 'services/shared/'
          - name: services1_2
            displayName: 'Services - 1 Smoke test static - applications'
            maxInstances: 4
            pattern: 'smoke-test-static'
            scopes: 'services/applications/'
          - name: services1_3
            displayName: 'Services - 1 Smoke test static - adapters'
            maxInstances: 4
            pattern: 'smoke-test-static'
            scopes: 'services/adapters/'
          - name: services2
            displayName: 'Services - 2 Smoke test data'
            maxInstances: 4
            pattern: 'smoke-test-data'
            scopes: 'services/'
          - name: services3
            displayName: 'Services - 3 (Shared CD)'
            maxInstances: 3
            pattern: 'smoke-test-pr-cd-*.feature'
            scopes: 'services/shared/'
          - name: services4
            displayName: 'Services - 4 (Applications CD)'
            maxInstances: 3
            pattern: 'smoke-test-pr-cd-*.feature'
            scopes: 'services/applications/'
          - name: services5
            displayName: 'Services - 5 (Adapters CD)'
            maxInstances: 3
            pattern: 'smoke-test-pr-cd-*.feature'
            scopes: 'services/adapters/'
        csvLayersTests:
          enabled: true
        clusterSmokeTests:
          enabled: true
          backups:
            - name: 'sdmo_ci'
              displayName: 'SDMO (ci)'
              config: '--s3ConfigType=sdmo'
              clusterName: 'cluster-ci'
            - name: 'sdmo_cu'
              displayName: 'SDMO (cu)'
              config: '--s3ConfigType=sdmo_cu'
              # run always even if hasLowRiskOnClusterTests is set to true
              runAlways: true
            - name: 'sdmo_prd_eu'
              displayName: 'SDMO (prd eu)'
              config: 's3://xtrem-developers-utility/dump-anon/prod/anon-eu-prd-latest-partial.zip'
              timeoutInMinutes: 90 # The upgrade from prod-eu can we very long
              clusterName: 'anon-eu-prd'
              skipTenantTests: true

      #========================================
      # scope - tools (glossary)
      # This is bit like an exception, it will not work if we add other application in that tool folder.
      # We will need to review this to follow the same pattern for all applications including glossary.
      #========================================
      - name: tools
        appName: 'glossary'
        slug: tools
        applicationDisplayName: 'Glossary'
        applicationRootFolder: 'tools/glossary/xtrem-glossary'
        sqlFilesTest:
          enabled: true
          skipTenantTests: true
          backupTypes:
            - name: '--s3ConfigType=glossary'
              appName: 'glossary'
        integrationTests:
          - name: glossary1
            displayName: 'Glossary - 1 Smoke test'
            maxInstances: 4
            pattern: 'smoke-test-{static,data}'
            scopes: 'tools/glossary/xtrem-glossary$'
        csvLayersTests:
          enabled: true
        clusterSmokeTests:
          enabled: true
          backups:
            - name: 'glossary_ci'
              displayName: 'Glossary (ci)'
              config: '--s3ConfigType=glossary'
              skipTenantTests: true

      #========================================
      # scope - shopfloor
      #========================================
      - name: shopfloor
        appName: 'shopfloor'
        applicationDisplayName: 'Shopfloor'
        applicationRootFolder: 'shopfloor/main/shopfloor-main'
        sqlFilesTest:
          enabled: true
          skipTenantTests: true
          backupTypes:
            - name: '--s3ConfigType=shopfloor'
              appName: 'shopfloor'
        integrationTests:
          - name: shopfloor1
            displayName: 'Shopfloor - 1 Smoke test'
            maxInstances: 4
            pattern: 'smoke-test-{static,data}'
            scopes: 'shopfloor/'
          - name: shopfloor2
            displayName: 'Shopfloor - 2 (Smoke test CD)'
            maxInstances: 3
            pattern: 'smoke-test-pr-cd-*.feature'
            scopes: 'shopfloor/'
        csvLayersTests:
          enabled: true
        clusterSmokeTests:
          enabled: true
          backups:
            - name: 'shopfloor_ci'
              displayName: 'Shopfloor (ci)'
              config: '--s3ConfigType=shopfloor'
              skipTenantTests: true
            - name: 'shopfloor_cu'
              displayName: 'Shopfloor (cu)'
              config: '--s3ConfigType=shopfloor_cu'
              skipTenantTests: true

      #========================================
      # scope - x3-connector
      #========================================
      - name: x3-connector
        appName: 'x3_connector'
        applicationDisplayName: 'X3 connector'
        applicationRootFolder: 'x3-connector/main/x3-connector-main'
        sqlFilesTest:
          enabled: true
          skipTenantTests: true
          backupTypes:
            - name: '--s3ConfigType=x3_connector'
              appName: 'x3_connector'
        csvLayersTests:
          enabled: true
        clusterSmokeTests:
          enabled: true
          backups:
            - name: 'x3_connector_ci'
              displayName: 'X3 Connector (ci)'
              config: '--s3ConfigType=x3_connector'
              skipTenantTests: true

      #========================================
      # scope - showcase-sales
      #========================================
      - name: showcase-sales
        appName: 'showcase_sales'
        applicationDisplayName: 'Showcase sales'
        applicationRootFolder: 'showcase-sales/main/showcase-sales-main'
        sqlFilesTest:
          enabled: false
          skipTenantTests: true
        integrationTests:
          - name: showcaseSales
            displayName: 'Showcase sales - Smoke test'
            maxInstances: 4
            pattern: 'smoke-test-{static,data,pr}'
            scopes: 'showcase-sales/'
        csvLayersTests:
          enabled: true
      #========================================
      # scope - showcase-stock
      #========================================
      - name: showcase-stock
        appName: 'showcase_stock'
        applicationDisplayName: 'Showcase stock'
        applicationRootFolder: 'showcase-stock/main/showcase-stock-main'
        sqlFilesTest:
          enabled: false
          skipTenantTests: true
        integrationTests:
          - name: showcaseStock
            displayName: 'Showcase stock - Smoke test'
            maxInstances: 4
            pattern: 'smoke-test-{static,data,pr}'
            scopes: 'showcase-stock/'
        csvLayersTests:
          enabled: true
      #========================================
      # scope - x3-services
      #========================================
      - name: x3-services
        applicationDisplayName: 'X3 services'
        applicationRootFolder: 'x3-services/main/x3-main'
        sqlFilesTest:
          enabled: false
          skipTenantTests: true
        unitTests:
          vmImage: 'ubuntu-latest'
          templates:
            validate: 'pipelines/xtrem-pr-pipeline/validate-custom-x3.yml@self'
          timeoutInMinutes: 30
      #========================================
      # scope - wh-services
      #========================================
      - name: wh-services
        applicationDisplayName: 'WH services'
        applicationRootFolder: 'wh-services/main/wh-main'
        sqlFilesTest:
          enabled: false
          skipTenantTests: true
        unitTests:
          vmImage: 'ubuntu-latest'
          templates:
            validate: 'pipelines/xtrem-pr-pipeline/validate-custom-x3.yml@self'
          timeoutInMinutes: 30

    customStages:
      # All this stage is valid for the 'services' scope only
      - stage: upgrade
        templateContext:
          dependsOnType: 'normal'
        displayName: 'Demo data smoke tests'
        variables:
          - name: stage_scopes
            value: $[ stageDependencies.checkout_and_install.checkout_and_install.outputs['changeSet.cs_stage_scopes'] ]
        condition: and(
          succeeded(),
          or(
          eq(dependencies.checkout_and_install.outputs['checkout_and_install.changeSet.cs_stage_scopes'], '*'),
          containsValue(split(dependencies.checkout_and_install.outputs['checkout_and_install.changeSet.cs_stage_scopes'], '|'), 'services')
          ))
        jobs:
          - job: upgrade
            displayName: 'Cluster upgrade test'
            steps:
              - checkout: none
              # Restore the sources + install + build from the cache
              - template: read-from-azure-cache.yml@huracan
                parameters:
                  cacheKey: $(cacheKeyNormal)
                  skipNativeDependencies: true

              - template: git/git-configure.yml@huracan

              # Configuration
              - bash: |
                  cp apps-template.yml apps.yml
                  xdev releng replace-config --source xtrem-config-azure.yml --target xtrem-config.yml
                displayName: NPM - Copy xtrem-config
                condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'))
                env:
                  XTREM_CACHE_READ_ONLY_KEY: $(xtrem_cache_read_only_key)
                  XTREM_CACHE_READ_ONLY_SECRET: $(xtrem_cache_read_only_secret)

              # Database
              - bash: |
                  xdev run postgres/deploy-postgres.sh --skip-client-check
                displayName: 'Deploy a database container and client tools'
                condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'))

              # Data
              - script: |
                  cd $(Build.SourcesDirectory)/services/main/xtrem-services-main
                  echo "##[group] reset database"
                      pnpm run xtrem schema --create --reset-database --skip-tables
                  echo "##[endgroup]"
                  echo "##[group] load setup and demo data"
                      pnpm run xtrem layers --load setup,demo --no-schema-reset
                  echo "##[endgroup]"
                displayName: 'Test demo data'
                condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'))
                env:
                  XTREM_SKIP_FACTORY_CHECKS: 1

              - bash: pipelines/shared/scripts/import-csv-test.sh
                env:
                  XTREM_MAIN_APP_DIR: '$(Build.SourcesDirectory)/services/main/xtrem-services-main'
                  XTREM_SCHEMA_NAME: 'xtrem'
                  AWS_ACCESS_KEY_ID: $(AWS_ACCESS_KEY_ID)
                  AWS_SECRET_ACCESS_KEY: $(AWS_SECRET_ACCESS_KEY)
                  AWS_DEFAULT_REGION: $(REGION)
                displayName: 'Can import csv files'
                condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'))

              - bash: pipelines/shared/scripts/https-server-test.sh
                env:
                  XTREM_MAIN_APP_DIR: '$(Build.SourcesDirectory)/services/main/xtrem-services-main'
                  XTREM_SCHEMA_NAME: 'xtrem'
                displayName: 'Can start server in HTTPS mode'
                condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'))

              - template: upgrade/tenant-management-tests.yml@huracan
                parameters:
                  scopeName: 'services'
                  applicationRootFolder: '$(Build.SourcesDirectory)/services/main/xtrem-services-main'
                  schemaName: 'xtrem'
                  applicationDisplayName: 'sdmo'

              # Ensure that the PR only contains changes that can be upgraded
              # Note: we are using a specific schema 'upgrade_test' to make sure nothing related to the schema
              # hard-coded in any custom SQL action
              - bash: |
                  set -e
                  echo "##[group]Reset database schema"
                  XTREM_SCHEMA_NAME="upgrade_test" pnpm run xtrem schema --create --reset-database --skip-tables
                  echo "##[endgroup]"
                  echo "##[group]Test upgrade"
                  XTREM_SCHEMA_NAME="upgrade_test" pnpm run xtrem upgrade --test --install-dependencies
                  echo "##[endgroup]"
                displayName: 'Test upgrade'
                condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'))
                workingDirectory: '$(Build.SourcesDirectory)/services/main/xtrem-services-main'
                env:
                  XTREM_SKIP_FACTORY_CHECKS: 1

      - stage: cli_smoke_tests
        displayName: 'CLI smoke tests'
        templateContext:
          dependsOnType: 'binary'
        # condition: false
        variables:
          - name: cs_stage_scopes
            value: $[ stageDependencies.checkout_and_install.checkout_and_install.outputs['changeSet.cs_stage_scopes'] ]
          - name: isCliTouched
            value: $[ stageDependencies.checkout_and_install.checkout_and_install.outputs['changeSet.isCliTouched'] ]
          - name: isX3CliTouched
            value: $[ stageDependencies.checkout_and_install.checkout_and_install.outputs['changeSet.isX3CliTouched']]
        jobs:
          # ***************************************************************************************************
          # *** DO NOT DISABLE THIS JOB ***
          # ***************************************************************************************************
          # This job test an application deployment based on pack file generated locally.
          # This mimics a deployement from a npm regsitry but with package not yet published.
          # This is to ensure that the application can start in this context.
          # If it fails there is a good chance that the installed applicaiton in the image will fail as well
          # ***************************************************************************************************
          - job: applicationDeployment
            displayName: 'Application deployment'
            condition: or(
              eq(variables.cs_stage_scopes, '*'),
              eq(variables.isCliTouched, 'true')
              )
            timeoutInMinutes: 30
            steps:
              - checkout: none
              # Restore the sources + install + build (BINARY) from the cache
              - template: read-from-azure-cache.yml@huracan
                parameters:
                  cacheKey: $(cacheKeyBinary)
              - bash: |
                  npm config list
                  cp apps-template.yml apps.yml
                  xdev releng replace-config --source xtrem-config-azure.yml --target xtrem-config.yml
                  xdev run postgres/deploy-postgres.sh --skip-client-check
                displayName: 'Deploy a database container and client tools'
                condition: and(succeeded(), eq(variables.runIntegrationTests, 'true'))
                env:
                  XTREM_CACHE_READ_ONLY_KEY: $(xtrem_cache_read_only_key)
                  XTREM_CACHE_READ_ONLY_SECRET: $(xtrem_cache_read_only_secret)

              # Deploy a test application (binary mode)
              - bash: |
                  set -e
                  xdev run pack-binary-build.sh \
                    '@sage/xtrem-show-case,@sage/xtrem-cli-main,@sage/xtrem-cli-cloud,@sage/xtrem-cli-dev,@sage/xtrem-cli-dev-data,@sage/xtrem-cli-atp' $XTREM_MAIN_APP_DIR xtrem-config.yml
                  # delete registry for @sage, @sageai scope to ensure we can install without it
                  npm config delete @sage:registry
                  npm config delete @sageai:registry
                  cd $XTREM_MAIN_APP_DIR
                  time npm ci --omit=dev --verbose
                displayName: 'Deploy a show-case application'
                condition: and(succeeded(), eq(variables.runIntegrationTests, 'true'))
                env:
                  XTREM_MAIN_APP_DIR: '$(Agent.TempDirectory)/xtrem-deploy-test'
              # Test deployed application (binary mode)
              - bash: pipelines/shared/scripts/application-test.sh
                displayName: 'Test deployed show-case application'
                condition: and(succeeded(), eq(variables.runIntegrationTests, 'true'))
                env:
                  XTREM_MAIN_APP_DIR: '$(Agent.TempDirectory)/xtrem-deploy-test'
          - job: x3ServicesDevPackDeployment
            displayName: 'X3 Services developer deployment'
            condition: or(
              eq(variables.cs_stage_scopes, '*'),
              eq(variables.isX3CliTouched, 'true')
              )
            timeoutInMinutes: 30
            steps:
              - checkout: none

              # Restore the sources + install + build (BINARY) from the cache
              - template: read-from-azure-cache.yml@huracan
                parameters:
                  cacheKey: $(cacheKeyBinary)

              - bash: |
                  cp x3-services/xtrem-config-dev.yml xtrem-config.yml
                displayName: 'Copy config'
                condition: succeeded()

              # Construct the X3 Services developer deployment pack
              - bash: |
                  set -e
                  xdev run pack-x3-dev-build.sh '@sage/x3-main,@sage/xtrem-cli-main,@sage/xtrem-x3-cli-dev,@sage/xtrem-cli-dev,@sage/eslint-plugin-xtrem' $XTREM_MAIN_APP_DIR xtrem-config.yml
                  # delete registry for @sage, @sageai scope to ensure we can install without it
                  npm config delete @sage:registry
                  npm config delete @sageai:registry
                  cd $XTREM_MAIN_APP_DIR
                  time npm ci --omit=dev
                displayName: 'Construct the X3 Services developer deployment pack'
                condition: succeeded()
                env:
                  XTREM_MAIN_APP_DIR: '$(Agent.TempDirectory)/xtrem-x3-services-dev-pack'

          - job: whServicesDevPackDeployment
            displayName: 'WH Services developer deployment'
            condition: or(
              eq(variables.cs_stage_scopes, '*'),
              eq(variables.isX3CliTouched, 'true')
              )
            timeoutInMinutes: 30
            steps:
              - checkout: none

              # Restore the sources + install + build (BINARY) from the cache
              - template: read-from-azure-cache.yml@huracan
                parameters:
                  cacheKey: $(cacheKeyBinary)

              - bash: |
                  cp wh-services/xtrem-config-dev.yml xtrem-config.yml
                displayName: 'Copy config'
                condition: succeeded()

              # Construct the X3 Services developer deployment pack
              - bash: |
                  set -e
                  xdev run pack-x3-dev-build.sh '@sage/wh-main,@sage/xtrem-cli-main,@sage/xtrem-x3-cli-dev,@sage/xtrem-cli-dev,@sage/eslint-plugin-xtrem' $XTREM_MAIN_APP_DIR xtrem-config.yml
                  # delete registry for @sage, @sageai scope to ensure we can install without it
                  npm config delete @sage:registry
                  npm config delete @sageai:registry
                  cd $XTREM_MAIN_APP_DIR
                  time npm ci --omit=dev
                displayName: 'Construct the X3 Services developer deployment pack'
                condition: succeeded()
                env:
                  XTREM_MAIN_APP_DIR: '$(Agent.TempDirectory)/xtrem-wh-services-dev-pack'

          - job: applicationStart
            displayName: 'Start Application'
            condition: or(
              eq(variables.cs_stage_scopes, '*'),
              eq(variables.isCliTouched, 'true'),
              eq(variables.isX3CliTouched, 'true')
              )
            timeoutInMinutes: 30
            steps:
              - checkout: none

              # Restore the sources + install + build (BINARY) from the cache
              - template: read-from-azure-cache.yml@huracan
                parameters:
                  cacheKey: $(cacheKeyBinary)

              # Can load plugins from cucumber test folder
              - bash: pipelines/shared/scripts/load-plugins-test.sh
                displayName: 'Can load plugins from cucumber test folder'
                condition: and(succeeded(), eq(variables.isCliTouched, 'true'))
                env:
                  XTREM_MAIN_APP_DIR: '$(Build.SourcesDirectory)/services/functional-tests/xtrem-0-prerequisites-test/test'

              # Start Services Application
              - bash: pipelines/shared/scripts/start-application-test.sh
                displayName: 'Start Services application'
                condition: and(succeeded(), eq(variables.isCliTouched, 'true'))
                env:
                  XTREM_MAIN_APP_DIR: '$(Build.SourcesDirectory)/services/main/xtrem-services-main'
                  XTREM_RESET_POSTGRES: 1

              # Start X3 Services Application
              - bash: pipelines/shared/scripts/start-application-test.sh
                displayName: 'Start X3 Services application'
                condition: and(succeeded(), eq(variables.isX3CliTouched, 'true'))
                env:
                  XTREM_MAIN_APP_DIR: '$(Build.SourcesDirectory)/x3-services/main/x3-main'
                  XTREM_X3_CONFIG: 1

              # Start WH Services Application
              - bash: pipelines/shared/scripts/start-application-test.sh
                displayName: 'Start WH Services application'
                condition: and(succeeded(), eq(variables.isX3CliTouched, 'true'))
                env:
                  XTREM_MAIN_APP_DIR: '$(Build.SourcesDirectory)/wh-services/main/wh-main'
                  XTREM_X3_CONFIG: 1

      - stage: misc
        displayName: 'Misc.'
        templateContext:
          dependsOnType: 'normal'
        variables:
          - name: stage_scopes
            value: $[ stageDependencies.checkout_and_install.checkout_and_install.outputs['changeSet.cs_stage_scopes'] ]
        jobs:
          - job: showcase_smoke_tests
            displayName: 'Showcase smoke tests'

            steps:
              - checkout: none
              # Restore the sources + install + build from the cache
              - template: read-from-azure-cache.yml@huracan
                parameters:
                  cacheKey: $(cacheKeyNormal)
                  skipNativeDependencies: true

              - template: git/git-configure.yml@huracan

              # Configuration
              - bash: |
                  cp apps-template.yml apps.yml
                  xdev releng replace-config --source xtrem-config-azure.yml --target xtrem-config.yml
                displayName: NPM - Copy xtrem-config
                condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'))
                env:
                  XTREM_CACHE_READ_ONLY_KEY: $(xtrem_cache_read_only_key)
                  XTREM_CACHE_READ_ONLY_SECRET: $(xtrem_cache_read_only_secret)

              # Database
              - bash: |
                  xdev run postgres/deploy-postgres.sh --skip-client-check
                displayName: 'Deploy a database container and client tools'
                condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'))

              - bash: |
                  ./show-case-test-specific-queue.sh
                workingDirectory: '$(Build.SourcesDirectory)/pipelines/show-case-smoke-tests'
                displayName: Test async mutation with specific queues
                condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'))
                env:
                  AWS_ACCESS_KEY_ID: $(AWS_ACCESS_KEY_ID)
                  AWS_SECRET_ACCESS_KEY: $(AWS_SECRET_ACCESS_KEY)
                  AWS_DEFAULT_REGION: $(REGION)
