parameters:
  - name: scope
    type: string
    displayName: 'The scope of the template: platform, services, tools, x3-services, ...'

steps:
  - script: |
      mkdir -p code-coverage
    displayName: Create code-coverage folder
    condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'))

  - bash: |
      if [ -f "$COV_TGZ" ]; then
        echo "##vso[task.setvariable variable=artifactExist]Yes"
      else
        echo "##vso[task.setvariable variable=artifactExist]No"
      fi;
    displayName: 'Check if the code-coverage artifact exists'
    env:
      COV_TGZ: '$(Build.StagingDirectory)/code-coverage-${{parameters.scope}}'

  - task: DownloadPipelineArtifact@2
    inputs:
      artifact: 'code-coverage-${{parameters.scope}}'
      path: $(Build.SourcesDirectory)
    displayName: 'Download cached code-coverage from Azure, if any'
    condition: eq(variables['artifactExist'], 'Yes')

  - bash: |
      tar -xf code-coverage.tgz
    displayName: Unpack code-coverage
    condition: eq(variables['artifactExist'], 'Yes')

  - script: |
      scope=${{parameters.scope}}
      target=code-coverage
      mkdir -p $target
      results=$(find . -name coverage-final.json | grep ./$scope)
      for result in $results; do
          fileKey=package_$(echo $result.json | sed "s/\/coverage\/coverage-final.json//" | sed "s/.\///" | sed "s/\//_/g")
          echo download s3://xtrem-developers-utility/code-coverage/$fileKey to $(Build.SourcesDirectory)/$target/$fileKey
          if xdev run lerna-cache/aws-s3.js --exists s3://xtrem-developers-utility/code-coverage/$fileKey; then
              xdev run lerna-cache/aws-s3.js --download s3://xtrem-developers-utility/code-coverage/$fileKey $(Build.SourcesDirectory)/$target/$fileKey
          fi
      done
    displayName: Create code-coverage folder
    condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'))

  - bash: |
      scope=${{parameters.scope}}
      mkdir final-coverage

      find "$scope" -type f -name "coverage-final.json" | while read file; do
        dir=$(dirname "$file")
        mkdir -p "final-coverage/$dir"
        cp "$file" "final-coverage/$dir"
      done

      tar -cf code-coverage.tgz final-coverage
    displayName: Create the final-coverage folder with it's entire subfolder structure
    condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'))

  - task: PublishPipelineArtifact@1
    displayName: 'Upload coverage archive to Azure cache'
    inputs:
      targetPath: '$(Build.SourcesDirectory)/code-coverage.tgz'
      artifactType: 'pipeline'
      artifactName: 'code-coverage-${{parameters.scope}}'
    condition: succeeded()

  # The code coverage reference is based on the master branch:
  - bash: pnpm run coverage:collect
    env:
      ${{ if eq(variables['Build.SourceBranchName'], 'master') }}:
        # a PR pipeline (master -> master) is scheduled every morning to compute the code coverage
        # that will be used during the day as a reference for all the PRs.
        # This specific PR must not fail (otherwise, the reference coverage will not be updated)
        FAILS_IF_CODE_COVERAGE_DROP: FALSE
      ${{ elseif eq(variables['system.pullRequest.targetBranch'],'master') }}:
        # PR on master : fail if code coverage drops
        FAILS_IF_CODE_COVERAGE_DROP: TRUE
      ${{ else }}:
        # Don't fail on code coverage when the target branch is not master (release/28.0 for instance)
        # There is only one reference code coverage (for master) so comparing this ref coverage with
        # the coverage of the PR would not make any sense but we still keep it to have an idea of
        # whether the coverage increases or decreases
        FAILS_IF_CODE_COVERAGE_DROP: FALSE
    displayName: 'Summarizing code coverage data'
    condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'))

  - task: AmazonWebServices.aws-vsts-tools.S3Upload.S3Upload@1
    displayName: Publishing code coverage data to S3
    condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'), eq(variables['Build.SourceBranch'], 'refs/heads/master'))
    inputs:
      awsCredentials: 'aws-s3'
      regionName: 'eu-west-1'
      bucketName: 'xtrem-developers-utility'
      sourceFolder: 'code-coverage'
      globExpressions: '**'
      targetFolder: 'code-coverage'
      logRequest: true
      logResponse: true
