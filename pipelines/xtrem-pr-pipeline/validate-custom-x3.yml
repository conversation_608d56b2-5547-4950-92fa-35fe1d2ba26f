# this template validates x3-services scope by running build/linting/unit-test/... steps
parameters:
  - name: scope
    type: object
    displayName: 'The scope object'
    default: {}

steps:
  - ${{ if eq(parameters.scope.name, 'x3-services') }}:
      - template: validate-x3.yml
        parameters:
          scope: '${{ parameters.scope.name }}'
          folderName: 'X3RBTREF'
          referenceFolderName: 'X3RBTREF'
          database: 'sagex3'
          sqlUser: 'READONLYUT'
          sqlPassword: $(SQLPASSWORD)
          instanceName: 'ADC-SQL-UNIT-TEST'
          securityGroupName: 'ADC-UNIT-TEST'
          applicationRootFolder: '${{ parameters.scope.applicationRootFolder }}'

  - ${{ elseif eq(parameters.scope.name, 'wh-services') }}:
      - template: validate-x3.yml
        parameters:
          scope: '${{ parameters.scope.name }}'
          folderName: 'WHRBTREF'
          referenceFolderName: 'WHRBTREF'
          database: 'whv12'
          sqlUser: 'READONLYUT_WH'
          sqlPassword: $(WH_SQLPASSWORD)
          instanceName: 'ADC-SQL-UNIT-TEST'
          securityGroupName: 'ADC-UNIT-TEST'
          applicationRootFolder: '${{ parameters.scope.applicationRootFolder }}'
