# this template validates x3-services scope by running build/linting/unit-test/... steps
parameters:
  - name: scope
    type: string
    displayName: 'The scope of the template: platform, services, tools, x3-services, ...'
  - name: applicationRootFolder
    type: string
    displayName: 'Application root folder'
  - name: folderName
    type: string
    displayName: 'The folder name'
    default:
  - name: referenceFolderName
    type: string
    displayName: 'The reference folder name'
  - name: database
    type: string
    displayName: 'database name'
    default: 'sagex3'
  - name: sqlUser
    type: string
    displayName: 'The sql user'
  - name: sqlPassword
    type: string
    displayName: 'The sql password'
  - name: instanceName
    type: string
    displayName: 'Database instance name'
  - name: securityGroupName
    type: string
    displayName: 'Security group name'
steps:
  - bash: |
      set -e
      cp ${{ parameters.scope }}/xtrem-config-azure.yml ${{ parameters.scope }}/xtrem-config.yml
      pipelines/shared/scripts/replace-config.sh ${{ parameters.scope }}/xtrem-config.yml $(xtrem_cache_read_only_key) $(xtrem_cache_read_only_secret)
      cp ${{ parameters.scope }}/xtrem-security-template.yml ${{ parameters.scope }}/xtrem-security.yml
    displayName: 'NPM - Copy xtrem-config - ${{ parameters.scope }}'

  - bash: |
      set -e
      IP_FILE="${TMP_DIR}/${SG_NAME}-sec-group-ip.txt"
      pipelines/shared/scripts/authorize-security-access.sh ${IP_FILE}
      publicIp=$(cat ${IP_FILE})

      echo -e "    database: ${{ parameters.database }}\n    hostname: ${publicIp}\n    user: ${{ parameters.sqlUser }}\n    password: ${{ parameters.sqlPassword }}\n  development:\n    folderName: ${{ parameters.folderName }}\n    referenceFolder: ${{ parameters.referenceFolderName }}\n    defaultLanguage: FRA\n    soap:\n        webServiceURL: http://scmx3-dev-dis.sagefr.adinternal.com:8124/soap-generic/syracuse/collaboration/syracuse/CAdxWebServiceXmlCC\n        userCredentials:\n            userName: admin\n            password: admin\n        codeLang: ENG\n        poolAlias: WSP\n        timeout: 50000\n" > "${TMP_DIR}"/xtrem-config-fragment.yml
      cat "${TMP_DIR}"/xtrem-config-fragment.yml >> ${{ parameters.scope }}/xtrem-config.yml

      rm "${TMP_DIR}"/xtrem-config-fragment.yml
    env:
      AWS_ACCESS_KEY_ID: $(AWS_ACCESS_KEY_ID)
      AWS_SECRET_ACCESS_KEY: $(AWS_SECRET_ACCESS_KEY)
      AWS_DEFAULT_REGION: $(REGION)
      INSTANCE_NAME: ${{ parameters.instanceName }}
      TMP_DIR: $(Agent.TempDirectory)
      SG_NAME: ${{ parameters.securityGroupName }}
      SEC_GROUP_DESC_FILE: $(Agent.TempDirectory)/${{ parameters.securityGroupName }}-sec-group.json
    displayName: 'Update config and authorize security access - ${{ parameters.scope }}'
    condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'))

  - bash: |
      set -e
      pipelines/shared/scripts/application-ready-test.sh
    displayName: 'Can be ready without db connection - ${{ parameters.scope }}'
    env:
      XTREM_MAIN_APP_DIR: '$(Build.SourcesDirectory)/${{ parameters.applicationRootFolder }}'

  - script: |
      XTREM_CI=1 pnpm run test:${{ parameters.scope }}
    displayName: 'Unit test execution'
    name: unit_test_execution
    condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'), ne(variables['Build.SourceBranch'], 'refs/heads/master'))

  - script: |
      XTREM_CI=1 XTREM_OFFLINE=1 pnpm run test:${{ parameters.scope }}
    displayName: 'Unit test execution (no cache)'
    name: unit_test_execution_no_cache
    condition: and(succeeded(), eq(variables.doesNotAffectBuild, 'False'), eq(variables['Build.SourceBranch'], 'refs/heads/master'))

  - script: |
      shopt -s globstar
      pnpm i -g copyfiles
      copyfiles -f ./*/*/*/junit-*.xml $(Agent.TempDirectory)/junit-xml/
    displayName: 'Prepare test result files for publishing'
    condition: succeededOrFailed()

  - task: PublishTestResults@2
    displayName: 'Publish integration test results'
    name: integration_test_results
    condition: succeededOrFailed()
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: '*.xml'
      searchFolder: '$(Agent.TempDirectory)/junit-xml/'
      mergeTestResults: true
      failTaskOnFailedTests: true
      testRunTitle: '${{ parameters.scope }} unit test'

  - ${{ if not(startsWith(variables['system.pullRequest.sourceBranch'], 'gh-renovate/')) }}:
      - template: coverage-summary.yml
        parameters:
          scope: '${{parameters.scope}}'

  - script: |
      sgId=$(aws ec2 describe-security-groups --filters Name=group-name,Values=${{ parameters.securityGroupName }} | jq -r .SecurityGroups[].GroupId)
      currentIp=${CURRENT_IP:=$(curl -s https://checkip.amazonaws.com)}

      aws ec2 revoke-security-group-ingress --group-id ${sgId} --protocol tcp --port 1433 --cidr ${currentIp}/32
    env:
      AWS_ACCESS_KEY_ID: $(AWS_ACCESS_KEY_ID)
      AWS_SECRET_ACCESS_KEY: $(AWS_SECRET_ACCESS_KEY)
      AWS_DEFAULT_REGION: $(REGION)
    displayName: Revoke security access
    condition: and(always(), eq(variables.doesNotAffectBuild, 'False'))
