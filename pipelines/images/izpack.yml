# Display name
parameters:
  - name: dryRun
    type: boolean
    default: false
  - name: scope
    type: string
    default: 'x3-services'
    displayName: 'The scope of the template: x3-services or wh-services'
  - name: projectName
    type: string
    default: 'X3Services'
    displayName: 'The scope of the template: X3Services or WHServices'
  - name: zipName
    type: string
    displayName: 'The name of the zip file'
  - name: deliveryVersion
    type: string
    displayName: 'The delivery version'
  - name: tagName
    type: string
    displayName: 'Tag name'
    default: 'staging'

steps:
  - script: |
      git config --global user.name 'Sage X3 CI'
      git config --global user.email '<EMAIL>'
    displayName: 'Git - Configuration'

  - template: ../shared/branch-name.yml
    parameters:
      allowAnyBranch: ${{ parameters.dryRun }}

  - template: ../shared/manual-git-clone.yml
    parameters:
      depth: 1

  - task: Docker@2
    displayName: Login to repository
    inputs:
      command: login
      containerRegistry: ghcr-ro

  - task: DownloadPipelineArtifact@2
    inputs:
      artifact: 'cache_zip'
      path: $(Build.SourcesDirectory)/scripts/x3-izpack/${{ parameters.scope }}/project/base/
    displayName: 'Download the archive from Azure cache'

  - bash: |
      set -e

      export XTREM_ROOT=$(pwd)
      echo xtrem root is $XTREM_ROOT
      echo scope is $SCOPE
      echo version is $DELIVERY_VERSION
      echo zip is $WIN_ARCHIVE
      echo project name is $PROJECT_NAME
      echo izpack version is $IZPACK_VERSION
      scripts/x3-izpack/run-izpack.sh

      ls -altr scripts/x3-izpack/$SCOPE/project
    displayName: Generate JAR file
    env:
      SCOPE: ${{ parameters.scope }}
      WIN_ARCHIVE: ${{ parameters.zipName }}
      PROJECT_NAME: ${{ parameters.projectName }}
      DELIVERY_VERSION: ${{ parameters.tagName }}
      IZPACK_VERSION: '*******'

  # The resulting tree in the S3 bucket is something like
  # $(productName)
  #   |- $(productName)-staging-win.txt
  #   |- $(productName)-1.4-win.txt
  #   \- yyyyMM
  #        |- $(productName)-staging.623430-win.jar
  #
  # Copy the jar delivery then the index file
  # For this task inputs, see https://github.com/aws/aws-toolkit-azure-devops/blob/master/src/tasks/AWSShellScript/task.json
  - task: AmazonWebServices.aws-vsts-tools.AWSShellScript.AWSShellScript@1
    condition: succeeded()
    inputs:
      awsCredentials: 'xtrem-s3-dev-bucket'
      regionName: 'eu-west-1'
      scriptType: 'filePath'
      filePath: scripts/x3-izpack/aws-cp-x3-izpack-delivery.sh
      arguments: '${{ parameters.scope }} ${{ parameters.zipName }} ${{ parameters.deliveryVersion }} ${{ parameters.tagName }} ${{ parameters.dryRun }}'
      disableAutoCwd: true
      workingDirectory: '.'
      logRequest: true
      logResponse: true
    displayName: Upload win delivery to aws

  - task: Docker@2
    displayName: Logout from repository
    inputs:
      command: logout
      containerRegistry: ghcr-ro
