# Display name
parameters:
  - name: dryRun
    type: boolean
    default: false

steps:
  - script: |
      git config --global user.name 'Sage X3 CI'
      git config --global user.email '<EMAIL>'
    displayName: 'Git - Configuration'

  - template: pipelines/templates/misc/branch-name.yml@huracan
    parameters:
      allowAnyBranch: ${{ parameters.dryRun }}

  - template: pipelines/templates/git/manual-git-clone.yml@huracan
    parameters:
      depth: 1

  - bash: |
      PACKAGE_VERSION=$(cat x3-services/package.json \
                      | grep version \
                      | head -1 \
                      | awk -F: '{ print $2 }' \
                      | sed 's/[",]//g' \
                      | sed 's/ //g')

      echo current package version ${PACKAGE_VERSION} on branch  $(xtremBranch)
      echo "##vso[task.setvariable variable=currentVersion]${PACKAGE_VERSION}"

      TAG="staging"
      MINOR_TAG="staging"

      if [ "$(BUILD.SOURCEBRANCHNAME)" != "master" ]; then
        BRANCH_STREAM=$(echo $(BUILD.SOURCEBRANCH) | awk -F / '{print $3}')
        echo stream is ${BRANCH_STREAM}
        if [ "${BRANCH_STREAM}" == "release" ]; then
          TAG=${PACKAGE_VERSION}
          MINOR_TAG=$(echo $TAG | awk -F. '{ print $1"."$2 }')
        else
          if [ "${{ parameters.dryRun }}" == "True" ]; then
            echo "##vso[task.logissue type=warning]Running in dry mode, the image won't be pushed!"
          else
            echo "##vso[task.logissue type=error]bad branch name: only master and release/x.y can be used to generate images"
            exit 1
          fi
        fi
      fi
      echo "##vso[task.setvariable variable=tagName;isOutput=true]${TAG}"
      echo "##vso[task.setvariable variable=tagName]${TAG}"
      echo "##vso[task.setvariable variable=tagMinor;isOutput=true]${MINOR_TAG}"
      echo "##vso[task.setvariable variable=tagMinor]${MINOR_TAG}"
    displayName: set image version

  - template: pipelines/templates/install.yml@huracan

  - script: pnpm run build:binary
    displayName: 'Build X3 Services(binary)'
    env:
      XTREM_CI: 1
      XTREM_SCOPES: 'platform|x3-services|[a-z0-9-]+/cli'

  - bash: |
      cp x3-services/xtrem-config-dev.yml xtrem-config.yml
    displayName: 'Copy config'
    condition: succeeded()

  # set the folder and zip variables
  - bash: |
      buildNumber=$(echo "$BUILD_BUILDID")

      currMonth=$(date --date="$bdate" +%-m)
      currYear=$(date --date="$bdate" +%Y)
      subFolder=$(echo "$currYear$currMonth")
      zipDir=$(echo "$AGENT_TEMPDIRECTORY/$PRODUCTNAME/$subFolder")

      deliveryVersion=$(echo "$CURRENTVERSION")
      tagName=$(echo "$TAGNAME")

      if [[ -z "$deliveryVersion" ]]; then
        deliveryVersion="staging"
      fi

      zipFolder=$(echo "$PRODUCTNAME-dev-studio-$deliveryVersion.$buildNumber")
      zipName=$(echo "$zipFolder.zip")

      echo "##vso[task.setvariable variable=zipDir;isOutput=true]$zipDir"
      echo "##vso[task.setvariable variable=zipDir]$zipDir"
      echo "zipDir is $zipDir"

      echo "##vso[task.setvariable variable=zipFolder;isOutput=true]$zipFolder"
      echo "##vso[task.setvariable variable=zipFolder]$zipFolder"
      echo "zipFolder is $zipFolder"

      echo "##vso[task.setvariable variable=subFolder;isOutput=true]$subFolder"
      echo "##vso[task.setvariable variable=subFolder]$subFolder"
      echo "subFolder is $subFolder"

      echo "##vso[task.setvariable variable=zipName;isOutput=true]$zipName"
      echo "##vso[task.setvariable variable=zipName]$zipName"
      echo "zipName is $zipName"

      echo "tagMinor is $TAGMINOR"
      txtFileName=$(echo "$PRODUCTNAME-dev-studio-$TAGMINOR.txt")
      echo "##vso[task.setvariable variable=txtFileName;isOutput=true]$txtFileName"
      echo "##vso[task.setvariable variable=txtFileName]$txtFileName"
      echo "txtFileName is $txtFileName"

      txtFilePath=$(echo "$AGENT_TEMPDIRECTORY/$PRODUCTNAME/$txtFileName")
      echo "##vso[task.setvariable variable=txtFilePath;isOutput=true]$txtFilePath"
      echo "##vso[task.setvariable variable=txtFilePath]$txtFilePath"
      echo "txtFilePath is $txtFilePath"
    displayName: set zip variables
    name: setTagStep

  # Construct the X3 developer deployment pack
  - bash: |
      xdev run pack-x3-dev-build.sh '@sage/x3-main,@sage/xtrem-cli-main,@sage/xtrem-x3-cli-dev,@sage/xtrem-cli-dev,@sage/eslint-plugin-xtrem' $XTREM_MAIN_APP_DIR xtrem-config.yml
    displayName: 'Construct the X3 developer deployment pack'
    condition: succeeded()
    env:
      XTREM_MAIN_APP_DIR: '$(zipDir)/$(zipFolder)'

  # Construct the X3 developer deployment pack text file
  - bash: |
      echo "$PRODUCTNAME/$(subFolder)/$(zipName)" > "$(txtFilePath)"
      echo "Contents of $(txtFilePath)"
      cat $(txtFilePath)
    displayName: 'Construct the text file'
    condition: succeeded()

  - task: ArchiveFiles@2
    inputs:
      rootFolderOrFile: '$(zipDir)/'
      includeRootFolder: false
      archiveType: 'zip'
      archiveFile: '$(zipDir)/$(zipName)'
      verbose: true
    displayName: Zip content

  # The resulting tree in the S3 bucket is something like
  # $(productName)
  #   |- $(productName)-${deliveryVersion}.${buildId}-dev.txt
  #   |- $(productName)-1.4-win.txt
  #   \- yyyyMM
  #        |- $(productName)-${deliveryVersion}.${buildId}-dev.zip
  #
  # Copy the zip delivery then the index file
  # For this task inputs, see https://github.com/aws/aws-toolkit-azure-devops/blob/master/src/tasks/AWSShellScript/task.json
  - task: AmazonWebServices.aws-vsts-tools.AWSShellScript.AWSShellScript@1
    condition: and(succeeded(), not(${{ parameters.dryRun }}))
    inputs:
      awsCredentials: 'xtrem-s3-dev-bucket'
      regionName: 'eu-west-1'
      scriptType: 'filePath'
      filePath: pipelines/shared/scripts/aws-cp-partner-dev-pack.sh
      arguments: '$(productName) $(subFolder) $(zipFolder) "s3://adc-dev-packaging" $(zipDir) $(txtFileName) $(txtFilePath)'
      disableAutoCwd: true
      workingDirectory: $(xtremDistDir)
      logRequest: true
      logResponse: true
    displayName: Upload developer pack delivery to aws
