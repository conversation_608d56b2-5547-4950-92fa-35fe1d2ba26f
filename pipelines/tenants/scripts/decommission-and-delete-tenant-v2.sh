#!/usr/bin/env bash

# FOR CLOUDMANAGER V2

set -e

# Decommission and delete a tenant (cloud-manager v2)

# Env vars
# ACCESS_TOKEN
# API_URL
# BUILD_ID
# TENANT_ID
# URL_SUFFIX

# arguments
# -$1 TENANT_ID
# -$2: APP_ID - the tenant's application to delete ("sdmo", "shopfloor", ...)

# Exit code :
# 0 - Succeed
# 1 - Error on delete tenant request call, no operation url.
# 2 - Multiple error after retries to fetch operation url
# 3 - Deletion of tenant ended in error
# 4 - Deletion of tenant timed out

if [ "true" = "${IS_PERFORMANCE_TEST}" ]; then
  INDIVIDUAL=${USER_EMAIL}
else
  INDIVIDUAL="cucumber-pipeline/${BUILD_ID}"
fi

TENANT_ID=$1
if [ -z "$TENANT_ID" ]; then
  echo "TenantId is missing"
  echo "##vso[task.setvariable variable=exitMessage;]TenantId is missing"
  exit 1
fi

APP_ID=$2
if [ -z "$APP_ID" ]; then
  echo "AppId is missing"
  echo "##vso[task.setvariable variable=exitMessage;]AppId is missing"
  exit 1
fi

echo "##[group]Decommission tenant ${TENANT_ID}/${APP_ID}"
# {
#   "xtremIgnoreErrors": false,
#   "autoCleanTenant": true,
#   "finalBackup": false,
#   "decommissionOnly":false
# }

PAYLOAD='{"xtremIgnoreErrors":false, "autoCleanTenant":true,"finalBackup":false,"decommissionOnly":false}'
echo "payload : ${PAYLOAD}"

REQUEST_URL="${API_URL}/tenant/${TENANT_ID}/app/${APP_ID}/decommission"

attempt=1

# It will often happen that the decommissioning query will return with a 409 error (conflict)
# thus, we have to retry many times (max=10)
until [ $attempt -gt 10 ]
do
  sleep 5
  echo "Attempt for decommissionning tenant ${TENANT_ID}: ${attempt}/10"
  RESPONSE_CODE=$(curl -so ./result.json -w "%{http_code}" -X DELETE -d "${PAYLOAD}" -H "Content-Type:application/json" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X-VERSION:v2" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${REQUEST_URL}")
  echo "Response code: ${RESPONSE_CODE}"
  if ! [ "409" = "${RESPONSE_CODE}" ] ; then
    break
  fi
  ((attempt++))
done

RESPONSE=$(cat ./result.json)
echo "Response: ${RESPONSE}"

if [ "409" = "${RESPONSE_CODE}" ] ; then
  # Even with 10 retries, the tenant could not be decommissionned
  echo "The tenant ${TENANT_ID} could not be decommissioned because of some conflicts (an operation is probably running on this tenant)"
elif [ "404" = "${RESPONSE_CODE}" ] ; then
  # Nothing more to do there, the tenant was not commissionned
  echo "The tenant ${TENANT_ID} was not decommissioned"
elif [ "204" = "${RESPONSE_CODE}" ] ; then
  echo "The tenant ${TENANT_ID} was successfully decommissioned"
else
  echo "The tenant ${TENANT_ID} is being decommissioned"
  OPERATION_URL=$(echo "${RESPONSE}" | jq -r '.url')
  OPERATION_ID=$(echo "${RESPONSE}" | jq -r '.id')
  # the request returned an operation url we have to follow until the end of the process
  if ! [ "201" = "${RESPONSE_CODE}" ] ; then
    errorMessage=$(echo "${RESPONSE}" | jq -r '.message')
    if [ "null" = "${errorMessage}" ] || [ "" = "${errorMessage}" ]; then
      errorMessage="${RESPONSE}"
    fi
      if [ -z "${OPERATION_URL}" ]; then
        echo "Error while fetching progress url (operation url), probably because the api call returned an error :"
      fi
    echo "Error while decommissioning tenant: ${errorMessage}"
    echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to decommission the tenant: ${errorMessage}"
    exit 1
  fi
  source pipelines/shared/scripts/fetch-cloud-manager-operation-result-v2.sh "${ACCESS_TOKEN}" "${OPERATION_URL}" "${INDIVIDUAL}"
  fetchOperationResult=$?
  case $fetchOperationResult in

    0)
      echo "Tenant ${TENANT_ID} has been successfully decommissioned"
      ;;

    *)
      echo "Tenant ${TENANT_ID} could not be decommissioned, error=${fetchOperationResult}"
      echo "Check operation logs on swf via cloudmanager : https://cloudmanager.${URL_SUFFIX}/operation/${OPERATION_ID}"
      echo "##vso[task.setvariable variable=exitMessage;]Tenant ${TENANT_ID} could not be decommissioned, error=${fetchOperationResult}"
      exit 1
      ;;
  esac

fi
echo "##[endgroup]"

echo "##[group]Delete tenant ${TENANT_ID}"
REQUEST_URL="${API_URL}/tenant/${TENANT_ID}"

RESPONSE_CODE=$(curl -so ./result.json -w "%{http_code}" -X DELETE -d "{}" -H "Content-Type:application/json" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X-VERSION:v2" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${REQUEST_URL}")
echo "Response code: ${RESPONSE_CODE}"

RESPONSE=$(cat ./result.json)
echo "Response: ${RESPONSE}"

if ! [ "204" = "${RESPONSE_CODE}" ] ; then
  errorMessage=$(echo "${RESPONSE}" | jq -r '.message')
  if [ "null" = "${errorMessage}" ] || [ "" = "${errorMessage}" ]; then
  	errorMessage="${RESPONSE}"
  fi
  echo "Error while deleting tenant: ${errorMessage}"
  echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to delete the tenant: ${errorMessage}"
  exit 1
fi
echo "##[endgroup]"
echo "Tenant ${TENANT_ID} has been successfully deleted"
