#!/usr/bin/env bash

# Provisions an xtrem tenant for xtreem release test
# Env vars
# ACCESS_TOKEN
# API_URL
# BUILD_ID
# CLUSTER
# LAYER
# TENANT_ID
# URL_SUFFIX

# Exit code :
# 0 - Succeed
# 1 - Error on provision tenant request call, no operation url.
# 2 - Multiple error after retries to fetch operation url
# 3 - Provision of tenant ended in error
# 4 - Provision of tenant timed out

if [ "true" = "${IS_PERFORMANCE_TEST}" ]; then
  INDIVIDUAL=${USER_EMAIL}
else
  INDIVIDUAL="performance-pipeline/${BUILD_ID}"
fi
PAYLOAD=$(printf '{"cluster":"%s","layers":["setup", "%s"]}' "${CLUSTER}" "${LAYER}")
API_URL="${API_URL}/tenant/${TENANT_ID}/provision"

PROVISION_RESPONSE=$(curl -sS -X POST -d "${PAYLOAD}" -H "Content-Type:application/json" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${API_URL}")

# Check the response for an operation id or an error
OPERATION_URL=$(echo "${PROVISION_RESPONSE}" | jq -r '.operation."$url"')
OPERATION_ID=$(echo "${PROVISION_RESPONSE}" | jq -r '.operation.id')

# Check the response for an operation id or an error
if [ "null" = "${OPERATION_URL}" ]; then
    errorMessage=$(echo "${PROVISION_RESPONSE}" | jq -r '.message')
    if [ "null" = "${errorMessage}" ]; then
        errorMessage="${PROVISION_RESPONSE}"
    fi
    echo "Error while fetching provision tenant result progress url (operation url), probably because the api call returned an error :${errorMessage}"
    echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to provision the tenant: ${errorMessage}"
    exit 2
fi

# shellcheck disable=SC1091
source "${BUILD_SOURCESDIRECTORY}/pipelines/shared/scripts/fetch-cloud-manager-operation-result.sh" "${ACCESS_TOKEN}" "${OPERATION_URL}" "${INDIVIDUAL}"
fetchOperationResult=$?

case $fetchOperationResult in

  0)
    echo "Tenant ${TENANT_ID} has been provisioned"
    echo "##vso[task.setvariable variable=exitMessage;]Tenant ${TENANT_ID} has been provisioned"
    echo "##vso[task.setvariable variable=tenantProvisioningState;isOutput=true]true"
    ;;

  1)
    errorMessage=$(echo "${PROVISION_RESPONSE}" | jq -r '.message')
    if [ "" = "${errorMessage}" ] || [ "null" = "${errorMessage}" ]; then
        errorMessage="${PROVISION_RESPONSE}"
    fi
    echo "Error while fetching provision tenant result progress url (operation url), probably because the api call return an error: ${errorMessage}"
    echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to provision the tenant: ${errorMessage}"
    echo "##vso[task.setvariable variable=tenantProvisioningState;isOutput=true]false"
    ;;

  2)
    echo "Multiple error after retries, assuming failure"
    echo "##vso[task.setvariable variable=exitMessage;]Multiple error after retries, assuming failure"
    echo "##vso[task.setvariable variable=tenantProvisioningState;isOutput=true]false"
    ;;

  3)
    echo "Provisioning of tenant ${TENANT_ID} ended in error, check operation logs on swf via cloudmanager : https://cloudmanager.${URL_SUFFIX}/operation/${OPERATION_ID}"
    echo "##vso[task.setvariable variable=exitMessage;]Provisioning of tenant ${TENANT_ID} ended in error"
    echo "##vso[task.setvariable variable=tenantProvisioningState;isOutput=true]false"
    ;;

  4)
    echo "Provisioning of tenant ${TENANT_ID} did not finish before the script timeout, check operation logs on swf via cloudmanager : https://cloudmanager.${URL_SUFFIX}/operation/${OPERATION_ID}"
    echo "##vso[task.setvariable variable=exitMessage;]Provisioning of tenant ${TENANT_ID} did not finish before the script timeout"
    echo "##vso[task.setvariable variable=tenantProvisioningState;isOutput=true]false"
    ;;

esac

exit $fetchOperationResult
