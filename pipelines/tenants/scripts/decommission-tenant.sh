#!/usr/bin/env bash

# Check the status of an xtrem tenant and decommission if required, for cucumber test
# Env vars
#
#          ACCESS_TOKEN: $(ACCESSTOKEN)
#          TENANT_ID: ${{parameters.tenantId}}
#          SKIP_DECOMMISSION: ${{parameters.SkipDecommission}}
#          API_URL: 'https://3m48vx3e0e.execute-api.eu-west-1.amazonaws.com/v1' #Make it as param if needed lated to do on multiple envs

if [ "false" = "${SKIP_DECOMMISSION}" ]; then

    if [ "true" = "${IS_PERFORMANCE_TEST}" ]; then
      INDIVIDUAL=${USER_EMAIL}
    else
      INDIVIDUAL="cucumber-pipeline/${BUILD_BUILDNUMBER}"
    fi

    TENANT_URL="${API_URL}/tenant/${TENANT_ID}"
    echo "Fetching tenant status at ${TENANT_URL}"
    TENANT_RESPONSE=$(curl -sS -X GET -H "Content-Type:application/json" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${TENANT_URL}")

    # Check the current status of the tenant
    TENANT_STATUS=$(echo "${TENANT_RESPONSE}" | jq -r '.provisioningStatus')

    if [ "null" = "${TENANT_STATUS}" ]; then
        echo "Error while fetching tenant satus, probably because the api call return an error :${errorMessage}"
        echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to check the tenant status: ${errorMessage}"
        exit 2
    fi

    if [ "none" = "${TENANT_STATUS}" ]; then
        echo "Tenant is not provisioned, decommission not required"
        exit 0
    fi

    DECOMMISSION_PAYLOAD="{\"decommissionOnly\": true}"
    DECOMMISSION_RESPONSE=$(curl -sS -X DELETE -d "${DECOMMISSION_PAYLOAD}" -H "Content-Type:application/json" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${TENANT_URL}")

    OPERATION_URL=$(echo "${DECOMMISSION_RESPONSE}" | jq -r '.operation."$url"')
    OPERATION_ID=$(echo "${DECOMMISSION_RESPONSE}" | jq -r '.operation.id')

    # Check the response for an operation id or an error
    if [ "null" = "${OPERATION_URL}" ]; then
        errorMessage=$(echo "${DECOMMISSION_RESPONSE}" | jq -r '.message')
        if [ "null" = "${errorMessage}" ]; then
            errorMessage="${DECOMMISSION_RESPONSE}"
        fi
        echo "Error while fetching decommission tenant result progress url (operation url), probably because the api call return an error :${errorMessage}"
        echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to decommission the tenant: ${errorMessage}"
        exit 2
    fi

    # Wait for the decommission tenant operation to complete
    ERROR_COUNT=0
    SECONDS=0                   # build int bash to track seconds
    MAX_DURATION_IN_SECONDS=600 # 10 mins to decommission a tenant should be plenty
    STATUS="none"
    STATUS_SUCCESS="success"
    STATUS_ERROR="error"

    until [[ "${SECONDS}" -gt "${MAX_DURATION_IN_SECONDS}" ]]; do
        sleep 5
        echo "Fetching operation status at ${OPERATION_URL}"
        FETCH_OPERATION_RESPONSE=$(curl -sS -X GET -H "Content-Type:application/json" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${OPERATION_URL}")

        # Example of response : {"id":"xuULStILDowWjrbQnqPNm","operationKind":"Cluster_upgrade_advanced","status":"success","createdOn":"2020-10-07T07:41:03.435Z","endedOn":"2020-10-07T07:44:33.791Z","createdBy":"OPS_ADMIN / OPS_ADMIN","curStep":"enableCluster","workflowId":"u8rNwOMRDpgGt19F8CVxH","relatedId":"cluster-a"}
        STATUS=$(echo "${FETCH_OPERATION_RESPONSE}" | jq -r .status)
        STEP=$(echo "${FETCH_OPERATION_RESPONSE}" | jq -r .curStep)

        if [ "null" = "${STATUS}" ]; then
            echo "Error while fetching operation status, response from api :${FETCH_OPERATION_RESPONSE}"
            ERROR_COUNT=$((ERROR_COUNT + 1))

            if [[ $ERROR_COUNT -gt 5 ]]; then
                echo "Multiple error after retries, assuming failure"
                echo "##vso[task.setvariable variable=exitMessage;]Multiple error after retries, assuming failure"
                exit 3
            else
                echo "Retrying, error counter is ${ERROR_COUNT}"
            fi

        else
            #Status read ok
            if [ "${STATUS_SUCCESS}" = "${STATUS}" ]; then
                echo "End of decommissioning workflow in success"
                echo "##vso[task.setvariable variable=exitMessage;]Tenant ${TENANT_ID} has been decommissioned"
                exit 0
            fi
            if [ "${STATUS_ERROR}" = "${STATUS}" ]; then
                echo "Decommissioning of tenant ${TENANT_ID} ended in error, check operation logs on swf via cloudmanager : https://cloudmanager.dev-sagextrem.com/operation/${OPERATION_ID}"
                echo "##vso[task.setvariable variable=exitMessage;]Decommissioning of tenant ${TENANT_ID} ended in error"
                exit 4
            fi

            echo "Status of workflow is ${STATUS}, step is ${STEP}"
        fi
    done

    echo "Decommissioning of tenant ${TENANT_ID} did not finish before the script timeout, check operation logs on swf via cloudmanager : https://cloudmanager.dev-sagextrem.com/operation/${OPERATION_ID}"
    echo "##vso[task.setvariable variable=exitMessage;]decommissioning of tenant ${TENANT_ID} did not finish before the script timeout"
    exit 5
fi
