#!/usr/bin/env bash

# FOR <PERSON>LOUDM<PERSON><PERSON>ER V2

# Create a tenant for xtreem release test
# Env vars
# ACCESS_TOKEN
# API_URL
# TENANT_ID (PERF_TEST__SMALL_DATA/PERF_TEST__LARGE_DATA)
# CUSTOMER_ID
# BUILD_ID
# USER_EMAIL

# Output
# tenantId
# tenantName

set -e

# manage multi-tenants creation
# @TODO will be removed when Cirrus implements tenant creation with tenantId
if [ "true" = "${IS_PERFORMANCE_TEST}" ]; then
  INDIVIDUAL=${USER_EMAIL}
  TENANT_NAME="xtreem release performance test for ${TENANT_ID} - ${BUILD_ID}"
else
  INDIVIDUAL="cucumber-pipeline/${BUILD_ID}"
  TENANT_NAME="xtreem release test ${BUILD_ID}"
fi

TENANT_DESCRIPTION="Tenant for xtreem release test ${BUILD_ID}"

# Example of payload:
# {
#     "id": "tenant-test",
#     "description": "New tenant test",
#     "customer": {
#         "id": "customer-one",
#         "ownerEmail": "<EMAIL>"
#     },
#     "name": "new tenant test",
#     "tenantType": "production",
#     "locale": "en-US",
#     "countryCode": "USA",
#     "tags": {
#         "atag": "avalue"
#     }
# }
echo "##[group]Create tenant ${TENANT_ID}"
echo "Create tenant ${TENANT_NAME} for xtreem release test on customer ${CUSTOMER_ID}"
PAYLOAD_FORMAT='{"id":"%s", "description":"%s","name":"%s","tenantType":"production","locale":"en-US","countryCode":"USA","customer":{"id":"%s","ownerEmail":"%s"}}'
# shellcheck disable=SC2059
PAYLOAD=$(printf "${PAYLOAD_FORMAT}" "${TENANT_ID}" "${TENANT_DESCRIPTION}" "${TENANT_NAME}" "${CUSTOMER_ID}" "${USER_EMAIL}")
echo "payload : ${PAYLOAD}"
REQUEST_URL="${API_URL}/tenant"

CREATE_TENANT_RESPONSE=$(curl -sS -X POST -d "${PAYLOAD}" -H "Content-Type:application/json" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X-VERSION:v2" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${REQUEST_URL}")

echo "response from create tenant : ${CREATE_TENANT_RESPONSE}"

# Check the response for a tenant id or an error
TENANT_ID=$(echo "${CREATE_TENANT_RESPONSE}" | jq -r '.id')

if [ "null" = "${TENANT_ID}" ]; then
    errorMessage=$(echo "${CREATE_TENANT_RESPONSE}" | jq -r '.message')
    if [ "null" = "${errorMessage}" ]; then
        errorMessage="${CREATE_TENANT_RESPONSE}"
    fi
    echo "Error while creating tenant, probably because the api call return an error : ${errorMessage}"
    echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to create the tenant: ${errorMessage}"
    exit 2
fi

echo "##[endgroup]"
echo "Tenant ${TENANT_ID} has been created. Name is ${TENANT_NAME}"
echo "##vso[task.setvariable variable=tenantId;]${TENANT_ID}"
echo "##vso[task.setvariable variable=tenantName;]${TENANT_NAME}"
echo "##vso[task.setvariable variable=tenantcreateSuccess]Yes"
