#!/usr/bin/env bash

# FOR <PERSON>LOUDM<PERSON>AGER V2

# enable for debug
# set -x

set -e

# TO BE RUN FROM THE ROOT OF THE REPO

# returns the cluster of a tenant

# The following variables must be set
# -ACCESS_TOKEN
# -API_URL

# arguments
# -$1 TENANT_ID
# -$2: APP_ID - the application("sdmo", "shopfloor", ...)

ENV_FILE=pipelines/tenants/scripts/.env
if [ -f $ENV_FILE ]; then
    source $ENV_FILE
fi

TENANT_ID=$1
APP_ID=$2

if [ -z "$TENANT_ID" ] || [ -z "$APP_ID" ]; then
  echo "Usage: get-tenant-cluster.sh tenant_id app_id"
  echo "    tenant_id : the id of tenant to export"
  echo "    app_id : the application ('sdmo', 'shopfloor', ...)"
  exit 1
fi

REQUEST_URL="${API_URL}/tenant/${TENANT_ID}/app/${APP_ID}"
INDIVIDUAL="get-tenant-info"

# Retrieve the cluster of the tenant
RESPONSE_CODE=$(curl -so ./result.json -w "%{http_code}" -X GET -H "Content-Type:application/json" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X-VERSION:v2" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${REQUEST_URL}")
if ! [ "200" = "${RESPONSE_CODE}" ]; then
  echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to get infos for tenant: ${TENANT_ID}"
  echo "Error ${RESPONSE_CODE} while getting infos for tenant: ${TENANT_ID}" >&2
  cat ./result.json >&2
  exit 1
fi

RESPONSE=$(cat ./result.json)
CLUSTER_ID=$(echo "${RESPONSE}" | jq -r '.cluster')
echo $CLUSTER_ID
