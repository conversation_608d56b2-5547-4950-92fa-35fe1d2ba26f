#!/usr/bin/env bash

# Create a tenant for xtreem release test
# Env vars
# ACCESS_TOKEN
# API_URL
# CUSTOMER
# BUILD_ID
# USER_EMAIL

# Output
# tenantId
# tenantName

# manage multi-tenants creation
# @TODO will be removed when Cirrus implements tenant creation with tenantId
if [ "true" = "${IS_PERFORMANCE_TEST}" ]; then
  INDIVIDUAL=${USER_EMAIL}
  TENANT_NAME="xtreem release performance test for ${TENANT_NAME_INPUT} - ${BUILD_ID}"
else
  INDIVIDUAL="cucumber-pipeline/${BUILD_ID}"
  TENANT_NAME="xtreem release test ${BUILD_ID}"
fi

TENANT_DESCRIPTION="Tenant for xtreem release test ${BUILD_ID}"

echo "Create tenant ${TENANT_NAME} for xtreem release test on customer ${CUSTOMER_ID}"

PAYLOAD_FORMAT='{"name":"%s","locale":"en-US","mainAdministrator":{"email":"%s","firstName":"test","lastName":"user"},"description":"%s","customer":{"id":"%s"}}'
PAYLOAD=$(printf "${PAYLOAD_FORMAT}" "${TENANT_NAME}" "${USER_EMAIL}" "${TENANT_DESCRIPTION}" "${CUSTOMER_ID}")
API_URL="${API_URL}/tenant"

CREATE_TENANT_RESPONSE=$(curl -sS -X POST -d "${PAYLOAD}" -H "Content-Type:application/json" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${API_URL}")

# Check the response for a tenant id or an error
TENANT_ID=$(echo "${CREATE_TENANT_RESPONSE}" | jq -r '.id')

if [ "null" = "${TENANT_ID}" ]; then
    errorMessage=$(echo "${CREATE_TENANT_RESPONSE}" | jq -r '.message')
    if [ "null" = "${errorMessage}" ]; then
        errorMessage="${CREATE_TENANT_RESPONSE}"
    fi
    echo "Error while creating tenant, probably because the api call return an error : ${errorMessage}"
    echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to create the tenant: ${errorMessage}"
    exit 2
fi
echo "Tenant ${TENANT_NAME} has been created. Tenant Id is ${TENANT_ID}"
echo "##vso[task.setvariable variable=tenantId;]${TENANT_ID}"
echo "##vso[task.setvariable variable=tenantName;]${TENANT_NAME}"
echo "##vso[task.setvariable variable=tenantcreateSuccess]Yes"

# manage multi-tenants creation
# @TODO will be removed when Cirrus implements tenant creation with tenantId
if [ "true" = "${IS_PERFORMANCE_TEST}" ]; then
  # Update the tenants/tenantsStatus variables
  if [ "" = "${TENANTS_IDS}" ]; then
    echo "##vso[task.setvariable variable=tenants;]${TENANT_ID}"
    echo "##vso[task.setvariable variable=tenantsStatus;]Yes"
  else
    echo "##vso[task.setvariable variable=tenants;]${TENANT_ID},$TENANTS_IDS"
    echo "##vso[task.setvariable variable=tenantsStatus;]Yes,$TENANTS_STATUS"
  fi
fi
