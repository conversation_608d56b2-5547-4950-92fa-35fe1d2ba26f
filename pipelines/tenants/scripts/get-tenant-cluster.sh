#!/usr/bin/env bash

# enable for debug
# set -x    

# TO BE RUN FROM THE ROOT OF THE REPO

# returns the cluster of a tenant

# The following variables must be set
# -ACCESS_TOKEN
# -API_URL

# arguments
# -$1 TENANT_ID

ENV_FILE=pipelines/tenants/scripts/.env
if [ -f $ENV_FILE ]; then
    source $ENV_FILE
fi

TENANT_ID="$1"

API_URL="${API_URL}/tenant/${TENANT_ID}"
INDIVIDUAL="get-tenant-info"

# Retrieve the cluster of the tenant
RESPONSE_CODE=$(curl -so ./result.json -w "%{http_code}" -X GET -H "Content-Type:application/json" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${API_URL}")
if ! [ "200" = "${RESPONSE_CODE}" ]; then
  echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to get infos for tenant: ${TENANT_ID}"
  echo "Error ${RESPONSE_CODE} while getting infos for tenant: ${TENANT_ID}" >&2
  cat ./result.json >&2    
  exit 1
fi

RESPONSE=$(cat ./result.json)
CLUSTER_ID=$(echo "${RESPONSE}" | jq -r '.cluster')
echo $CLUSTER_ID
