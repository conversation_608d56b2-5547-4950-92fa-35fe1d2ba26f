#!/usr/bin/env bash

# FOR CLOUDMANAGER V2

set -e

# Delete tenant
# Env vars
# ACCESS_TOKEN
# API_URL
# BUILD_ID
# TENANT_ID
# URL_SUFFIX

# arguments
# -$1 TENANT_ID

# Exit code :
# 0 - Succeed
# 1 - Error on delete tenant request call, no operation url.
# 2 - Multiple error after retries to fetch operation url
# 3 - Deletion of tenant ended in error
# 4 - Deletion of tenant timed out

if [ "true" = "${IS_PERFORMANCE_TEST}" ]; then
  INDIVIDUAL=${USER_EMAIL}
else
  INDIVIDUAL="cucumber-pipeline/${BUILD_ID}"
fi

TENANT_ID=$1
if [ -z "$TENANT_ID" ]; then
  echo "TenantId is missing"
  echo "##vso[task.setvariable variable=exitMessage;]TenantId is missing"
  exit 1
fi

echo "Delete tenant ${TENANT_ID}"

API_URL="${API_URL}/tenant/${TENANT_ID}"

DELETE_RESPONSE_CODE=$(curl -so ./result.json -w "%{http_code}" -X DELETE -d "{}" -H "Content-Type:application/json" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${API_URL}")

DELETE_RESPONSE=$(cat ./result.json)
OPERATION_URL=$(echo "${DELETE_RESPONSE}" | jq -r '.operation."$url"')
OPERATION_ID=$(echo "${DELETE_RESPONSE}" | jq -r '.operation.id')

if ! [ "200" = "${DELETE_RESPONSE_CODE}" ] ; then
  errorMessage=$(echo "${DELETE_RESPONSE}" | jq -r '.message')
  if [ "null" = "${errorMessage}" ] || [ "" = "${errorMessage}" ]; then
	errorMessage="${DELETE_RESPONSE}"
  fi
    if [ -z "${OPERATION_URL}" ]; then
      echo "Error while fetching delete tenant result progress url (operation url), probably because the api call returned an error :"
    fi
  echo "Error while deleting tenant: ${errorMessage}"
  echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to delete the tenant: ${errorMessage}"
  exit 1
fi

source "${BUILD_SOURCESDIRECTORY}/pipelines/shared/scripts/fetch-cloud-manager-operation-result.sh" "${ACCESS_TOKEN}" "${OPERATION_URL}" "${INDIVIDUAL}"
fetchOperationResult=$?

case $fetchOperationResult in

  0)
    echo "End of delete tenant in success"
    echo "##vso[task.setvariable variable=exitMessage;]Tenant ${TENANT_ID} has been deleted"
    exit 0
    ;;

  1)
    echo "Tenant ${TENANT_ID} has been deleted successfully"
    exit 0
    ;;

  2)
    echo "Multiple error after retries, assuming failure"
    echo "##vso[task.setvariable variable=exitMessage;]Multiple error after retries, assuming failure"
    ;;

  3)
    echo "Deletion of tenant ${TENANT_ID} ended in error, check operation logs on swf via cloudmanager : https://cloudmanager.${URL_SUFFIX}/operation/${OPERATION_ID}"
    echo "##vso[task.setvariable variable=exitMessage;]Deletion of tenant ${TENANT_ID} ended in error"
    ;;

  4)
    echo "Deletion of tenant ${TENANT_ID} did not finish before the script timeout, check operation logs on swf via cloudmanager : https://cloudmanager.${URL_SUFFIX}/operation/${OPERATION_ID}"
    echo "##vso[task.setvariable variable=exitMessage;]deletion of tenant ${TENANT_ID} did not finish before the script timeout"
    ;;

esac

exit $fetchOperationResult
