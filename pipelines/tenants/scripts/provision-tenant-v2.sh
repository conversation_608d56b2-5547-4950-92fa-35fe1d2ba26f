#!/usr/bin/env bash

# FOR CLOUDMANAGER V2

set -e

# Provisions an xtrem tenant for xtreem release test and bind it to the app
# Env vars
# ACCESS_TOKEN
# API_URL
# BUILD_ID
# CLUSTER
# LAYER
# TENANT_ID
# APP_ID (sdmo, shopfloor, ...)
# URL_SUFFIX
# USER_EMAIL

# Exit codes :
# 0 - Succeed
# 1 - Error on provision tenant request call, no operation url.
# 2 - Multiple error after retries to fetch operation url
# 3 - Provision of tenant ended in error
# 4 - Provision of tenant timed out
# 5 - at least one env variable is missing

if [ "true" = "${IS_PERFORMANCE_TEST}" ]; then
  INDIVIDUAL=${USER_EMAIL}
else
  INDIVIDUAL="performance-pipeline/${BUILD_ID}"
fi

if [ -z "$ACCESS_TOKEN" ]; then
  echo "ACCESS_TOKEN environment variable is not set"
  exit 5
fi
if [ -z "$API_URL" ]; then
  echo "API_URL environment variable is not set"
  exit 5
fi
if [ -z "$BUILD_ID" ]; then
  echo "BUILD_ID environment variable is not set"
  exit 5
fi
if [ -z "$CLUSTER" ]; then
  echo "CLUSTER environment variable is not set"
  exit 5
fi
if [ -z "$LAYER" ]; then
  echo "LAYER environment variable is not set"
  exit 5
fi
if [ -z "$TENANT_ID" ]; then
  echo "TENANT_ID environment variable is not set"
  exit 5
fi
if [ -z "$APP_ID" ]; then
  echo "APP_ID environment variable is not set"
  exit 5
fi
if [ -z "$URL_SUFFIX" ]; then
  echo "URL_SUFFIX environment variable is not set"
  exit 5
fi
if [ -z "$USER_EMAIL" ]; then
  echo "USER_EMAIL environment variable is not set"
  exit 5
fi

# Example of payload
# {
#   "app": "sdmo",
#   "cluster": "cls-perf-test",
#   "enabled": false,
#   "provisioning": {
#     "dataType": "layers",
#     "dataOptions": "setup,test"
#   },
#   "mainAdmin": "<EMAIL>"
# }

echo "##[group]Create ${APP_ID} app for tenant ${TENANT_ID}"
PAYLOAD_FORMAT='{"app":"%s", "cluster":"%s","enabled":false, "provisioning": {"dataType": "layers", "dataOptions":"setup,%s"},"mainAdmin": "%s"}'
# shellcheck disable=SC2059
PAYLOAD=$(printf "${PAYLOAD_FORMAT}" "${APP_ID}" "${CLUSTER}" "${LAYER}" "${USER_EMAIL}")
REQUEST_URL="${API_URL}/tenant/${TENANT_ID}/app"

RESPONSE_CODE=$(curl -so ./result.json  -w "%{http_code}" -X POST -d "${PAYLOAD}" -H "Content-Type:application/json" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X-VERSION:v2" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${REQUEST_URL}")
RESPONSE=$(cat ./result.json)

echo "Response code: $RESPONSE_CODE"
echo "Response: $RESPONSE"

if ! [ "201" = "$RESPONSE_CODE" ]; then
  errorMessage=$(echo "$RESPONSE" | jq -r '.message')
  if [ "null" = "$errorMessage" ] || [ "" = "$errorMessage" ]; then
    errorMessage="$RESPONSE"
  fi
  echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to provision the tenant: $errorMessage"
  echo "Error $RESPONSE_CODE while provisioning the tenant: $errorMessage"
  exit 1
fi
echo "##[endgroup]"
echo "The application ${APP_ID} was successfuly created for tenant ${TENANT_ID}"
echo "##[group]Provision ${APP_ID} app for tenant ${TENANT_ID}"
REQUEST_URL="${API_URL}/tenant/${TENANT_ID}/app/${APP_ID}/provision"
RESPONSE_CODE=$(curl -so ./result.json -w "%{http_code}" -X POST -H "Content-Type:application/json" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X-VERSION:v2" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${REQUEST_URL}")
echo "Response code: ${RESPONSE_CODE}"
RESPONSE=$(cat ./result.json)
echo "Response: ${RESPONSE}"
OPERATION_URL=$(echo "${RESPONSE}" | jq -r '.url')
OPERATION_ID=$(echo "${RESPONSE}" | jq -r '.id')
# the request returned an operation url we have to follow until the end of the process
if ! [ "201" = "${RESPONSE_CODE}" ] ; then
  errorMessage=$(echo "${RESPONSE}" | jq -r '.message')
  if [ "null" = "${errorMessage}" ] || [ "" = "${errorMessage}" ]; then
    errorMessage="${RESPONSE}"
  fi
    if [ -z "${OPERATION_URL}" ]; then
      echo "Error while fetching progress url (operation url), probably because the api call returned an error :"
    fi
  echo "Error while provisioning tenant: ${errorMessage}"
  echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to provision the tenant: ${errorMessage}"
  exit 1
fi
# shellcheck disable=SC1091
source "${BUILD_SOURCESDIRECTORY}/pipelines/shared/scripts/fetch-cloud-manager-operation-result-v2.sh" "${ACCESS_TOKEN}" "${OPERATION_URL}" "${INDIVIDUAL}"
fetchOperationResult=$?
case $fetchOperationResult in

  0)
    echo "Tenant ${TENANT_ID} has been successfully provisioned"
    ;;
  *)
    echo "Tenant ${TENANT_ID} could not be provisioned, error=${fetchOperationResult}"
    echo "Check operation logs on swf via cloudmanager : https://cloudmanager.${URL_SUFFIX}/operation/${OPERATION_ID}"
    echo "##vso[task.setvariable variable=exitMessage;]Tenant ${TENANT_ID} could not be provisioned, error=${fetchOperationResult}"
    exit 1
    ;;

esac

echo "##[endgroup]"
echo "The tenant ${TENANT_ID} was successfully provisioned on cluster ${CLUSTER}"
