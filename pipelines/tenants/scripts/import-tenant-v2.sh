#!/usr/bin/env bash

# TO BE RUN FROM THE ROOT OF THE REPO

# FOR CLOUDMANAGER V2

set -e

# imports a data set (the result of an export) to a cluster/tenant

# To run the script locally: copy the template from pipelines/tenants/scripts/tenants-template.env file to .env and adapt it

# The following variables must be set
# -API_URL
# -ACCESS_TOKEN
# -AWS_ACCESS_KEY_ID
# -AWS_SECRET_ACCESS_KEY
# -AWS_DEFAULT_REGION

# Arguments
# -$1: user export id   - the id (defined by the user) of the export (the one that was generated by export-tenant-v2.sh)
# -$2: target tenant id - the tenant id on which the data will be imported
# -$3: export_id        - the id of the export to import (this id was provided by the export)

# enable for debug
# set -x

USER_EXPORT_ID=$1
TARGET_CLUSTER=$2
TARGET_TENANT_ID=$3

if [ -z "$USER_EXPORT_ID" ] || [ -z "$TARGET_CLUSTER" ] || [ -z "$TARGET_TENANT_ID" ]; then
  echo "Usage: import-tenant-v2.sh export_id target_cluster target_tenant_id"
  echo "     export_id        : the id of the data to import (this id was generated by export-tenant-v2.sh"
  echo "     target_cluster   : the cluster on which the data will be imported"
  echo "     target_tenant_id : the tenant id on which the data will be imported (on the target cluster)"
  echo "##vso[task.setvariable variable=exitMessage;]Invalid arguments"
  exit 1
fi

ENV_FILE=pipelines/tenants/scripts/.env
if [ -f $ENV_FILE ]; then
    # shellcheck disable=SC1090
    source $ENV_FILE
fi

# When data were exported by export-tenant-v2.sh, a JSON file was written with some informations
EXPORT_INFOS_S3_URI="s3://xtrem-developers-utility/export-tenant-informations/$USER_EXPORT_ID.json"
echo "Retrieve informations file of the data to export ($EXPORT_INFOS_S3_URI)"
if ! aws s3 cp "$EXPORT_INFOS_S3_URI" "$USER_EXPORT_ID".json
then
    echo "AWS copy failed - could not retrieve the information file $EXPORT_INFOS_S3_URI"
    exit 1
fi

EXPORT_INFOS=$(cat "$USER_EXPORT_ID".json)
# retrieve the infos when the data were exported
EXPORT_ID=$(echo "$EXPORT_INFOS" | jq -r '.exportId')
SOURCE_TENANT_ID=$(echo "$EXPORT_INFOS" | jq -r '.tenandId')
SOURCE_CLUSTER=$(echo "$EXPORT_INFOS" | jq -r '.cluster')
EXPORT_DATE=$(echo "$EXPORT_INFOS" | jq -r '.date')
APP_ID=$(echo "$EXPORT_INFOS" | jq -r '.app')

rm "$USER_EXPORT_ID".json

echo "Importing data"
echo "   - user export id=$USER_EXPORT_ID"
echo "   - export id (cloudManager)=$EXPORT_ID"
echo "   - source tenant id=$SOURCE_TENANT_ID"
echo "   - source cluster=$SOURCE_CLUSTER"
echo "   - data were exported on $EXPORT_DATE"
echo "   - target app id=$APP_ID"

TENANT_API_URL="$API_URL/tenant/$TARGET_TENANT_ID/app/$APP_ID/import"
INDIVIDUAL="import-tenant"

# Payload looks like this:
# {
#   "id": "some-export-id",
#   "clusterId":"ci-v2",
#   "finalBackup": false,
#   "onlyMasterUsers":false,
#   "deleteImportFiles":false,
#   "disableInviteEmail":false
# }
PAYLOAD_FORMAT='{"id":"%s","clusterId":"%s","finalBackup":false,"deleteImportFiles":false,"onlyMasterUsers":false,"disableInviteEmail":true}'
# shellcheck disable=SC2059
PAYLOAD=$(printf "$PAYLOAD_FORMAT" "$EXPORT_ID" "$TARGET_CLUSTER")

echo "Payload $PAYLOAD"

# Send the 'import' command
echo "Sending import request"
RESPONSE_CODE=$(curl -so ./result.json -w "%{http_code}" -X POST -d "$PAYLOAD" -H "Content-Type:application/json" -H "Authorization:Bearer $ACCESS_TOKEN" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:$INDIVIDUAL" -H "X-VERSION:v2" "$TENANT_API_URL")

RESPONSE=$(cat ./result.json)
echo "Response: $RESPONSE"

if ! [ "201" = "$RESPONSE_CODE" ]; then
  errorMessage=$(echo "$RESPONSE" | jq -r '.message')
  if [ "null" = "$errorMessage" ] || [ "" = "$errorMessage" ]; then
  	errorMessage="$RESPONSE"
  fi
  echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to import $EXPORT_ID for the tenant $TARGET_TENANT_ID on cluster $TARGET_CLUSTER: $errorMessage"
  echo "Error $RESPONSE_CODE while importing $EXPORT_ID for the tenant $TARGET_TENANT_ID on cluster $TARGET_CLUSTER: $errorMessage"
  cat ./result.json
  exit 1
fi

OPERATION_URL=$(echo "$RESPONSE" | jq -r '.url')
OPERATION_ID=$(echo "$RESPONSE" | jq -r '.id')

echo "Operation.id=$OPERATION_ID"
echo "Operation.url=$OPERATION_URL"


# Wait for the import to complete
echo "Waiting for the import to complete"

# shellcheck disable=SC1091
source pipelines/shared/scripts/fetch-cloud-manager-operation-result-v2.sh "$ACCESS_TOKEN" "$OPERATION_URL" $INDIVIDUAL
fetchOperationResult=$?

case $fetchOperationResult in

  0)
    echo "##vso[task.setvariable variable=exitMessage;]Export $EXPORT_ID was imported on tenant $TARGET_TENANT_ID, cluster $TARGET_CLUSTER"
    echo "Export $EXPORT_ID was imported on tenant $TARGET_TENANT_ID, cluster $TARGET_CLUSTER"
    ;;

  1)
    echo "Arguments error"
    exit 1
    ;;

  2)
    echo "Multiple error after retries, assuming failure"
    echo "##vso[task.setvariable variable=exitMessage;]Multiple error after retries, assuming failure"
    exit 1
    ;;

  3)
    echo "Import of $EXPORT_ID for tenant $TARGET_TENANT_ID, cluster $TARGET_CLUSTER ended in error, check operation logs on swf via cloudmanager : https://cloudmanager.$URL_SUFFIX/operation/$OPERATION_ID"
    echo "##vso[task.setvariable variable=exitMessage;]Import of $EXPORT_ID for tenant $TARGET_TENANT_ID, cluster $TARGET_CLUSTER ended in error"
    exit 1
    ;;

  4)
    echo "Import of $EXPORT_ID for tenant $TARGET_TENANT_ID, cluster $TARGET_CLUSTER did not finish before the script timeout, check operation logs on swf via cloudmanager : https://cloudmanager.$URL_SUFFIX/operation/$OPERATION_ID"
    echo "##vso[task.setvariable variable=exitMessage;]Import of $EXPORT_ID for tenant $TARGET_TENANT_ID, cluster $TARGET_CLUSTER did not finish before the script timeout"
    exit 1
    ;;

esac
