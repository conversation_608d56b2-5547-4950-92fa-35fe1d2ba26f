#!/bin/sh

# This script is executed in the services image (see docker-compose-test-custom-sql-script.yml)
# To test it locally, you can run: (don't forget to replace XXX with the right values)
# XTREM_DOCKER_IMAGE=ghcr.io/sage-erp-x3/xtrem:latest PG_VERSION=XXX BACKUP_TO_RESTORE=XXX TENANT_ID=XXXX SCRIPT_LOCATION=XXX ROLLBACK_SCRIPT_LOCATION=XXX AWS_ACCESS_KEY_ID=XXX AWS_SECRET_ACCESS_KEY=XXX docker compose -f ./docker-compose-test-custom-sql-script.yml up

set -e

# parameters
# - the S3 URI of the backup to restore
if [ $# -ne 4 ]; then
    echo "Usage: test-custom-sql-script <S3 URI of the backup to restore> <tenantId> <Location of the script to execute> <Location of the rollback script to execute>"
    exit 2
fi

BACKUP_TO_RESTORE=$1
TENANT_ID=$2
SCRIPT_LOCATION=$3
ROLLBACK_SCRIPT_LOCATION=$4

displaySection () {
    startsLine="**************************************************************************************************************"
    printf "\n\n\n\n%s\n" "$startsLine"
    printf "**\n**\n"
    printf "**              %s\n" "$1"
    printf "**\n**\n"
    printf "%s\n\n\n\n" "$startsLine"
}

# Note: here, we are using a configuration with a 'xtrem' user
# so that the user will be created when creating the schema
# but this user will not be used when upgrading, 'postgres' user will be used instead
cp /xtrem/config/config-for-schema-creation.yml /xtrem/app/xtrem-config.yml
displaySection "CREATE SCHEMA"
xtrem schema --create --reset-database --skip-tables

cp -f /xtrem/config/config-for-upgrade.yml /xtrem/app/xtrem-config.yml
displaySection "Restore backup $BACKUP_TO_RESTORE"
xtrem schema  --check-single-schema --restore-from-s3 "$BACKUP_TO_RESTORE"

displaySection "Upgrade"
xtrem upgrade --run --prod

displaySection "Apply script"
xtrem tenant --execute-custom-sql --location="/monorepo/$SCRIPT_LOCATION" --tenants="$TENANT_ID"

displaySection "Apply rollback script"
xtrem tenant --execute-custom-sql --location="/monorepo/$ROLLBACK_SCRIPT_LOCATION" --tenants="$TENANT_ID"

echo "Done"
