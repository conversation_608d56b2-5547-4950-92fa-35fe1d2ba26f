# This pipeline will:
# - start a xtrem image
# - restore a backup of sdmo-cu and upgrade
# - try to apply a custom SQL script
# - upload the custom SQL script to S3 if it could be executed successfully

name: export-custom-sql-script

pr: none
trigger: none
pool: 'x3-ubuntu'

variables:
  - group: sagex3ci_github
  - group: dockerXtrem

parameters:
  - name: backupToRestore
    displayName: 'Test the scripts against backup of'
    type: string
    values:
      - 'prod-eu'
      - 'prod-na'
    default: 'prod-eu'

  - name: scriptLocation
    displayName: "Location of the script to upload - relative to monorepo's root, without leading /"
    type: string

  - name: rollbackScriptLocation
    displayName: "Location of the ROLLBACK script to upload - relative to monore<PERSON>'s root, without leading /"
    type: string

  - name: tenantIdToTest
    displayName: 'TenantId to test'
    type: string

jobs:
  - job: init_and_upgrade
    displayName: 'Init and upgrade'
    steps:
      - checkout: none
      - template: ../../shared/manual-git-clone.yml
        parameters:
          depth: 1

      # Login on nexus private registry
      - task: Docker@2
        displayName: Login to repository
        inputs:
          command: login
          containerRegistry: ghcr-ro

      - bash: |
          set -e
          RELEASE_BRANCH=$(BUILD.SOURCEBRANCH)
          if ! [[ -f ${{ parameters.scriptLocation }} ]]; then
            echo "##vso[task.logissue type=error]The script ${{ parameters.scriptLocation }} does not exist in the branch ${RELEASE_BRANCH}."
            exit 1
          fi
          if  ! [[ -s ${{ parameters.scriptLocation }} ]]; then
            echo "##vso[task.logissue type=error]The script ${{ parameters.scriptLocation }} exists but is empty."
            exit 1
          fi

          if ! [[ -f ${{ parameters.rollbackScriptLocation }} ]]; then
            echo "##vso[task.logissue type=error]The rollback script ${{ parameters.rollbackScriptLocation }} does not exist in the branch ${RELEASE_BRANCH}."
            exit 1
          fi
          if  ! [[ -s ${{ parameters.rollbackScriptLocation }} ]]; then
            echo "##vso[task.logissue type=error]The rollback script ${{ parameters.rollbackScriptLocation }} exists but is empty."
            exit 1
          fi

          if [[ "$RELEASE_BRANCH" == "refs/heads/master" ]]; then
            RELEASE_VERSION="master"
            XTREM_DOCKER_IMAGE="ghcr.io/sage-erp-x3/xtrem:latest"
          # Uncomment the following lines to allow testing this pipeline on a specific branch (replace the branch name with yours)
          # elif [[ "$RELEASE_BRANCH" == "refs/heads/XT-84601-Review-publication-of-custom-sql-scripts" ]]; then
          #   RELEASE_VERSION="master"
          #   XTREM_DOCKER_IMAGE="ghcr.io/sage-erp-x3/xtrem:latest"
          else
            echo "RELEASE_BRANCH=$RELEASE_BRANCH"
            # Extract the release number from the name of the release branch (should be release/xx.0)
            if ! [[ "$RELEASE_BRANCH" =~ ^refs/heads/release/([0-9]+)\.0$ ]]; then
              echo "##vso[task.logissue type=error]This pipeline can only be executed on a release branch."
              exit 1
            fi
            RELEASE_VERSION=${BASH_REMATCH[1]}
            XTREM_DOCKER_IMAGE="ghcr.io/sage-erp-x3/xtrem:$RELEASE_VERSION"
          fi

          echo "Release version=$RELEASE_VERSION"
          echo "##vso[task.setvariable variable=ReleaseVersion;]$RELEASE_VERSION"
          echo "Docker image=$XTREM_DOCKER_IMAGE"
          echo "##vso[task.setvariable variable=XtremDockerImage;]$XTREM_DOCKER_IMAGE"

          PG_VERSION=$(cat .pgdbrc)
          echo "pg_version=$PG_VERSION"
          echo "##vso[task.setvariable variable=pg_version]$PG_VERSION"
        displayName: 'Initialize variables'

      - bash: |
          set -e
          FILES=("${{ parameters.scriptLocation }}" "${{ parameters.rollbackScriptLocation }}")
          NB_FILES=${#FILES[@]}

          for (( i=0; i < NB_FILES; i++ )); do
              FILE=${FILES[$i]}
              if [ $i -eq 0 ];
              then
                  VAR_PREFIX="";
                  echo "##[group] process file $FILE"
              else
                  VAR_PREFIX="rollback_";
                  echo "##[group] process rollback file $FILE"
              fi
              FULLNAME=$FILE
              echo "script location is $FULLNAME"
              # get the filename of the script (without any path)
              FILENAME="$(basename -- "$FULLNAME")"
              FOLDER="$(dirname -- "$FULLNAME")"

              echo "full filename: $FILENAME"
              echo "folder: $FOLDER"
              EXTENSION="${FILENAME#*.}"
              echo "file extension: $EXTENSION"
              if [ "$EXTENSION" != "sql" ];
              then
                  echo "##vso[task.logissue type=error]Invalid SQL file: must have a '.sql' extension, got $EXTENSION"
                  exit 1;
              fi
              FILENAME="${FILENAME%.*}"
              echo "filename: $FILENAME"

              # get the version from the package.json at the root of the repo
              FULL_VERSION=$(jq -r ".version" package.json)

              # add the version to the file foo.sql -> foo-39.0.27.sql
              TARGET="s3://xtrem-developers-utility/custom-sql/${FULL_VERSION}/${FILENAME}-${FULL_VERSION}.${EXTENSION}"
              echo "S3 target: $TARGET"

              # build a metadata file that will contain informations about the upload
              echo "{
              \"timestamp\":\"$(date)\",
              \"step.url\":\"https//sage-liveservices.visualstudio.com/$(SYSTEM.TEAMPROJECT)/_build/results?buildId=$(BUILD.BUILDID)&view=results\",
              \"build.sourceBranch\":\"$(BUILD.SOURCEBRANCH)\",
              \"build.queuedBy\":\"$(BUILD.QUEUEDBY)\",
              \"version\":\"$FULL_VERSION\",
              \"originalPath\":\"$FULLNAME\"
              }" > "${FULLNAME}.metadata"

              echo "metadata file was generated to ${FULLNAME}.metadata"
              cat "${FULLNAME}.metadata"

              # rename the files so that they can be executed by the xtrem command
              # foo.sql -> foo-39.0.27.sql
              ORIGINAL_FILENAME="${FOLDER}/${FILENAME}.${EXTENSION}"
              RENAMED_FILENAME="${FOLDER}/${FILENAME}-${FULL_VERSION}.${EXTENSION}"
              mv "$ORIGINAL_FILENAME" "$RENAMED_FILENAME"
              mv "${ORIGINAL_FILENAME}.metadata" "${RENAMED_FILENAME}.metadata"
              echo "${VAR_PREFIX}localFile: $RENAMED_FILENAME"
              echo "${VAR_PREFIX}targetUri: $TARGET"
              echo "##vso[task.setvariable variable=${VAR_PREFIX}localFile;]$RENAMED_FILENAME"
              echo "##vso[task.setvariable variable=${VAR_PREFIX}targetUri;]$TARGET"
              echo "##[endgroup]"
          done

        displayName: 'Rename the SQL script files with version'

      # Start the docker stack
      - bash: |
          docker compose -f ./pipelines/tenants/export-custom-sql-script/docker-compose-test-custom-sql-script.yml up --abort-on-container-exit --exit-code-from xtrem
        displayName: Test the SQL script
        env:
          AWS_ACCESS_KEY_ID: $(s3-xtrem-developers-utility-access-key)
          AWS_SECRET_ACCESS_KEY: $(s3-xtrem-developers-utility-secret)
          AWS_REGION: 'eu-west-1'
          ${{ if eq(parameters.backupToRestore, 'prod-eu') }}:
            BACKUP_TO_RESTORE: 's3://xtrem-developers-utility/dump-anon/prod/anon-eu-prd-latest.zip'
          ${{ else }}:
            BACKUP_TO_RESTORE: 's3://xtrem-developers-utility/dump-anon/prod/anon-na-prd-latest.zip'
          TENANT_ID: ${{ parameters.tenantIdToTest }}
          SCRIPT_LOCATION: $(localFile)
          ROLLBACK_SCRIPT_LOCATION: $(rollback_localFile)
          XTREM_DOCKER_IMAGE: $(XtremDockerImage)

      - bash: |
          set -e
          # Upload the script file
          echo "upload $(localFile) to $(targetUri)"
          aws s3 cp "$(localFile)" "$(targetUri)"

          # Upload the metadata file
          echo "upload $(localFile).metadata to $(targetUri).metadata"
          aws s3 cp "$(localFile).metadata" "$(targetUri).metadata"

          # Upload the rollback script file
          echo "upload $(rollback_localFile) to $(rollback_targetUri)"
          aws s3 cp "$(rollback_localFile)" "$(rollback_targetUri)"

          # Upload the metadata file for the rollback
          echo "upload $(rollback_localFile).metadata to $(rollback_targetUri).metadata"
          aws s3 cp "$(rollback_localFile).metadata" "$(rollback_targetUri).metadata"

        displayName: 'Upload the SQL script to S3'
        env:
          AWS_ACCESS_KEY_ID: $(s3-xtrem-developers-utility-access-key)
          AWS_SECRET_ACCESS_KEY: $(s3-xtrem-developers-utility-secret)
          AWS_REGION: 'eu-west-1'
