version: '3'
services:
  pgdb:
    image: postgres:${PG_VERSION}-alpine
    shm_size: 1g
    restart: always
    command: >
      -c max_locks_per_transaction=256
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=secret
  xtrem:
    image: ${XTREM_DOCKER_IMAGE}
    command: >
      /xtrem/scripts/test-custom-sql-script.sh "${BACKUP_TO_RESTORE}" "${TENANT_ID}" "${SCRIPT_LOCATION}" "${ROLLBACK_SCRIPT_LOCATION}"
    volumes:
      - ./config:/xtrem/config
      - ./scripts:/xtrem/scripts
      - ../../..:/monorepo
    environment:
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_DEFAULT_REGION=${AWS_REGION}
      - BACKUP_TO_RESTORE=${BACKUP_TO_RESTORE}
      - TENANT_ID=${TENANT_ID}
      - SCRIPT_LOCATION=${SCRIPT_LOCATION}
      - ROLLBACK_SCRIPT_LOCATION=${ROLLBACK_SCRIPT_LOCATION}
      - XTREM_DOCKER_IMAGE=${XTREM_DOCKER_IMAGE}
    depends_on:
      - pgdb
