# Monthly Export Large Tenant Pipeline

name: monthly-export-large-tenant

pr: none
trigger: none

pool:
  vmImage: 'ubuntu-latest'

schedules:
  - cron: '30 5 1 * *' # Every 1st of month, at 5:30AM
    displayName: Monthly export large tenant
    branches:
      include:
        - master

variables:
  - group: dockerXtrem
  - group: loadPerformanceTestEnvironmentVarsForDev
  - group: AWS_readWriteToClustersBuckets
  - group: monthlyPerformanceTests

jobs:
  - job:
    steps:
      - template: ./export-tenant-template.yml
        parameters:
          appId: 'sdmo'
