# Daily Export Small Tenant Pipeline

name: daily-export-perf-tenant

pr: none
trigger: none

pool:
  vmImage: 'ubuntu-latest'

schedules:
  # Every day, at 5:00AM UTC from monday to friday after the upgrade that starts at 4:30AM - clusters are off on the week-end
  - cron: '00 5 * * 1-5'
    displayName: Daily export small tenant
    branches:
      include:
        - master

parameters:
  - name: tenantId
    displayName: 'Id of the tenant to export'
    type: string
    default: 'jOCeeEJipmUbWEul0PR1F' # ek-anon
  - name: exportId
    displayName: 'Id of the export to generate'
    type: string
    default: 'ek-anon'

variables:
  - group: dockerXtrem
  - group: loadPerformanceTestEnvironmentVarsForDev
  - group: AWS_readWriteToClustersBuckets

jobs:
  - job:
    steps:
      - template: ./export-tenant-template.yml
        parameters:
          appId: 'sdmo'
    variables:
      tenantId: ${{ parameters.tenantId }}
      exportId: ${{ parameters.exportId }}
