# A pipeline template for the export tenant pipelines (manual, daily and monthly)

parameters:
  - name: appId
    displayName: app to provision for the tests
    type: string

steps:
  - checkout: self
    fetchDepth: 1
    fetchTags: false
    submodules: false

  - bash: pipelines/tenants/export-tenant/scripts/export-tenant-v2.sh $(tenantId) ${{ parameters.appId }} $(exportId)
    displayName: 'Export tenant'
    env:
      API_URL: $(apiUrl)
      ENV_CLIENTID: $(secretCloudIdClientId)
      ENV_CLIENTSECRET: $(secretCloudIdClientSecret)
      ENV_AUDIENCE: $(cloudIdAudience)
      ENV_SAGEID_OAUTH_TOKEN_URL: $(sageIdOAuthTokenUrl)
      URL_SUFFIX: $(urlSuffix)
      AWS_ACCESS_KEY_ID: $(AWS_ACCESS_KEY_ID)
      AWS_SECRET_ACCESS_KEY: $(AWS_SECRET_ACCESS_KEY)
      AWS_DEFAULT_REGION: $(REGION)
      MAX_DURATION_IN_SECONDS: 1800
