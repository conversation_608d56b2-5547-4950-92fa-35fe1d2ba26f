# Manual Export Tenant Pipeline

name: manual-export-tenant

pr: none
trigger: none

pool:
  vmImage: 'ubuntu-latest'

parameters:
  - name: tenantId
    displayName: 'Id of the tenant to export'
    type: string
  - name: exportId
    displayName: 'Id of the export to generate'
    type: string

variables:
  - group: loadPerformanceTestEnvironmentVarsForDev
  - group: AWS_readWriteToClustersBuckets

jobs:
  - job: export_tenant
    steps:
      - bash: |
          echo "##vso[task.setvariable variable=tenantId;]${{ parameters.tenantId }}"
          echo "##vso[task.setvariable variable=exportId;]${{ parameters.exportId }}"

      - template: ./export-tenant-template.yml
        parameters:
          appId: 'sdmo'
