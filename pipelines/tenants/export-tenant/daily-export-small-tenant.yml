# Daily Export Small Tenant Pipeline

name: daily-export-small-tenant

pr: none
trigger: none

pool:
  vmImage: 'ubuntu-latest'

schedules:
  # Every day, at 5:00AM UTC from monday to friday after the upgrade that starts at 4:30AM - clusters are off on the week-end
  - cron: '00 5 * * 1-5'
    displayName: Daily export small tenant
    branches:
      include:
        - master

variables:
  - group: dockerXtrem
  - group: loadPerformanceTestEnvironmentVarsForDev
  - group: AWS_readWriteToClustersBuckets
  - group: dailyPerformanceTests

jobs:
  - job:
    steps:
      - template: ./export-tenant-template.yml
        parameters:
          appId: 'sdmo'
