#!/usr/bin/env bash

# enable for debug
# set -x    

# TO BE RUN FROM THE ROOT OF THE REPO

# To run the script locally: copy the template from pipelines/tenants/scripts/tenants-template.env file to .env and adapt it

# The following variables must be set
# -ACCESS_TOKEN
# -API_URL
# -ENV_CLIENTID
# -ENV_CLIENTSECRET
# -ENV_AUDIENCE
# -ENV_SAGEID_OAUTH_TOKEN_URL
# -AWS_ACCESS_KEY_ID
# -AWS_SECRET_ACCESS_KEY
# -AWS_DEFAULT_REGION

# Arguments
# -$1: tenant id - the id of the tenant to export
# -$2: user export id - the id (defined by the user) of the export (will be used to store informations in S3)

ENV_FILE=pipelines/tenants/export-tenant/scripts/.env
if [ -f $ENV_FILE ]; then
    echo "Loading environment variables from $ENV_FILE"
    source $ENV_FILE
fi

TENANT_ID=$1
USER_EXPORT_ID=$2
if [ -z "$TENANT_ID" ] || [ -z "$USER_EXPORT_ID" ]; then
  echo "Usage: export-tenant.sh tenant_id export_id"
  echo "    tenant_id : the id of tenant to export"
  echo "    export_id : the id of the export to generate"
  echo "##vso[task.setvariable variable=exitMessage;]Invalid arguments"
  exit 1
fi

USER_EXPORT_ID=$2
# The S3 URI of the json that will be generated to store some information about this export
EXPORT_INFOS_S3_URI="s3://xtrem-developers-utility/export-tenant-informations/$USER_EXPORT_ID.json"

echo "Retrieve informations about tenant $TENANT_ID"

export ACCESS_TOKEN=`pipelines/shared/scripts/get-cloud-manager-access-token.sh`
if [ $? -ne 0 ]; then 
  echo "Could not get access token" >&2
  exit 1
fi

TENANT_API_URL="$API_URL/tenant/$TENANT_ID"
INDIVIDUAL="export-tenant"

# Retrieve the cluster of the tenant
CLUSTER_ID=`pipelines/tenants/scripts/get-tenant-cluster.sh $TENANT_ID` 
if [ $? -ne 0 ]; then 
  exit 1
fi

echo "Exporting tenant $TENANT_ID from cluster '$CLUSTER_ID'"

# Send the 'export' command
echo "Sending export request"
RESPONSE_CODE=$(curl -so ./result.json -w "%{http_code}" -X POST -d "{}" -H "Content-Type:application/json" -H "Authorization:Bearer $ACCESS_TOKEN" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:$INDIVIDUAL" "$TENANT_API_URL/export")

RESPONSE=$(cat ./result.json)
if ! [ "200" = "$RESPONSE_CODE" ]; then
  errorMessage=$(echo "$RESPONSE" | jq -r '.message')
  if [ "null" = "${errorMessage}" ] || [ "" = "${errorMessage}" ]; then
  	errorMessage="$RESPONSE"
  fi
  echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to export the tenant $TENANT_ID from $CLUSTER_ID: ${errorMessage}"
  echo "Error $RESPONSE_CODE while exporting tenant $TENANT_ID from cluster $CLUSTER_ID: ${errorMessage}"
  cat ./result.json
  exit 1
fi

OPERATION_URL=$(echo "$RESPONSE" | jq -r '.operation."$url"')
OPERATION_ID=$(echo "$RESPONSE" | jq -r '.operation.id')
EXPORT_ID=$(echo "$RESPONSE" | jq -r '.export.id')

echo "Operation.id=$OPERATION_ID"
echo "Export.id=$EXPORT_ID"

# Wait for the export to complete
echo "Waiting for the export to complete"

source pipelines/shared/scripts/fetch-cloud-manager-operation-result.sh $ACCESS_TOKEN $OPERATION_URL $INDIVIDUAL
fetchOperationResult=$?

case $fetchOperationResult in

  0)
    S3_URL="s3://xtrem-dev-eu-$CLUSTER_ID/exports/$TENANT_ID/$EXPORT_ID/$EXPORT_ID.zip"
    echo "##vso[task.setvariable variable=exitMessage;]Tenant $TENANT_ID has been exported to $S3_URL"
    echo "Tenant $TENANT_ID has been exported to $S3_URL"    
    echo "{\"date\":\"$(date)\",\"exportId\":\"$EXPORT_ID\",\"tenandId\":\"$TENANT_ID\",\"cluster\":\"$CLUSTER_ID\"}">$USER_EXPORT_ID.json
    aws s3 cp $USER_EXPORT_ID.json $EXPORT_INFOS_S3_URI
    if [ $? -ne 0 ]; then 
      echo "Could not upload informations about the export to $EXPORT_INFOS_S3_URI"
      exit 1
    fi
    echo "Informations about the export were uploaded to $EXPORT_INFOS_S3_URI"
    ;;

  1)
    echo "Arguments error"
    exit 1
    ;;

  2)
    echo "Multiple error after retries, assuming failure"
    echo "##vso[task.setvariable variable=exitMessage;]Multiple error after retries, assuming failure"
    exit 1
    ;;

  3)
    echo "Export of tenant $TENANT_ID ended in error, check operation logs on swf via cloudmanager : https://cloudmanager.$URL_SUFFIX/operation/$OPERATION_ID"
    echo "##vso[task.setvariable variable=exitMessage;]Export of tenant $TENANT_ID ended in error"
    exit 1
    ;;

  4)
    echo "Export of tenant $TENANT_ID did not finish before the script timeout, check operation logs on swf via cloudmanager : https://cloudmanager.$URL_SUFFIX/operation/$OPERATION_ID"
    echo "##vso[task.setvariable variable=exitMessage;]Export of tenant $TENANT_ID did not finish before the script timeout"
    exit 1
    ;;

esac
