parameters:
  - name: powerAutomatePostUrl
    type: string
  - name: team
    type: string
  - name: teamChannel
    type: string
  - name: azureAppClientId
    type: string
  - name: azureAppClientSecret
    type: string
  - name: azureAppTenantId
    type: string
  - name: scope
    type: string
  - name: clusterName
    type: string
  - name: envName
    type: string
  - name: target_url
    type: string
  - name: tenantName
    type: string
  - name: tenantApp
    type: string
    default: 'none'
  - name: tenantAppName
    type: string
    default: 'none'
  - name: testType
    type: string
  - name: imageVersion
    type: string
  - name: DMXT_AT_RELEASE
    type: string
  - name: DMXT_AT_SUCCESS
    type: string
  - name: Run_template_st_status
    type: string

steps:
  - script: |
      response=$(curl -X POST -H "Content-Type: application/x-www-form-urlencoded" \
      -d "grant_type=client_credentials&client_id=${{ parameters.azureAppClientId }}&client_secret=${{ parameters.azureAppClientSecret }}&scope=https://service.flow.microsoft.com//.default" \
      "https://login.microsoftonline.com/${{ parameters.azureAppTenantId }}/oauth2/v2.0/token")
      token=$(echo $response | jq -r '.access_token')

      if [ -z "$token" ]; then
        echo "##vso[task.setvariable variable=accessTokenSuccess;]false"
        exit 1
      else
        echo "##vso[task.setvariable variable=accessTokenSuccess;]true"
        echo "##vso[task.setvariable variable=accessToken;]$token"
      fi
    displayName: 'Get Access Token for power automate'

  - script: |
      curl -X POST -H "Content-Type: application/json" \
      -H "Authorization: Bearer $(accessToken)" \
      -d '{
            "team": "${{ parameters.team}}",
            "teamChannel": "${{ parameters.teamChannel}}",
            "azurePipelineName": "$(Build.DefinitionName)",
            "scope": "${{ parameters.scope}}",
            "Status": "${{ parameters.Run_template_st_status }}",
            "environment": "${{ parameters.envName }}",
            "cluster": "${{ parameters.clusterName }}",
            "targetURL": "${{ parameters.target_url }}",
            "tenant": "${{ parameters.tenantName }}",
            "tenantApp": "${{ parameters.tenantApp }}",
            "tenantAppName": "${{ parameters.tenantAppName }}",
            "endpoint": "${{ parameters.tenantName }}",
            "testType": "${{ parameters.testType }}",
            "imageVersion": "${{ parameters.imageVersion }}",
            "branchVersion": "${{ parameters.DMXT_AT_RELEASE }}",
            "sourceBranch": "$(Build.SourceBranch)",
            "successRate": "${{ parameters.DMXT_AT_SUCCESS }} %",
            "build": "$(System.CollectionUri)X3%20XTREM/_build/results?buildId=$(Build.BuildId)&view=results"

      }' ${{ parameters.powerAutomatePostUrl }}
    displayName: 'Send teams notification'
    condition: eq(variables['accessTokenSuccess'], 'true')
