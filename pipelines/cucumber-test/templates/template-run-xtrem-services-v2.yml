# shellcheck disable=all
parameters:
  - name: cluster
    type: string

  - name: clusterId
    type: string

  - name: SKIP_STAGE_TENANT_PROVISIONNING
    type: string

  - name: SKIP_CUCUMBER_TESTS
    type: string

  - name: scope
    type: string

  - name: XTREM_TEST_MAX_INSTANCES
    type: number

  - name: TARGET_URL
    type: string

  - name: CUCUMBER_TAGS
    type: string

  - name: testType
    type: string

  - name: smokeTestType
    type: string

  - name: authenticationType
    type: string

  - name: clusterName
    type: string

  - name: simulateFailure
    displayName: Simulate a failure in the cucumber tests
    type: boolean
    default: false

  - name: WDIO_LOG_LEVEL
    displayName: WDIO LOG LEVEL
    type: string
    default: 'warn'

  - name: RUN_SMOKE_TEST_STATIC_ONLY
    displayName: Run smoke test Static only
    type: string
    default: 'Yes'
    values:
      - No
      - Yes

  - name: tenantApp
    type: string

  - name: tenantApp2
    type: string
    default: 'none'

  - name: tenantAppName
    type: string

  - name: tenantId
    type: string

  - name: tenantLayer
    type: string

  - name: tenantName
    type: string

  - name: TENANT_PROVISIONNING_METHOD
    type: string

  - name: endPointName1
    type: string
    default: ''

jobs:
  - job: check_parameters_values

    displayName: 'Control parameters values'
    steps:
      - checkout: none

      - bash: |
          echo "##vso[task.logissue type=error]Mandatory parameter: tenantId."
          exit 1
        displayName: 'Control tenantId parameter is mandatory'
        condition: and(
          and(ne('${{ parameters.scope }}','x3-services'), ne('${{ parameters.scope }}','wh-services')),
          eq('${{ parameters.TENANT_PROVISIONNING_METHOD }}', 'static'),
          or(eq(variables['tenant_id'],'none'),  eq(variables['tenant_id'],'')))

      - bash: |
          echo "##vso[task.logissue type=error]Unsecure authentication method is not allowed in prod environment."
          exit 1
        displayName: 'Control unsecure authentication is allowed'
        condition: and(
          and(ne('${{ parameters.scope }}','x3-services'), ne('${{ parameters.scope }}','wh-services')),
          eq('${{ parameters.authenticationType }}', 'unsecure'),
          or(startsWith('${{ parameters.clusterName }}','pdeu/'), startsWith('${{ parameters.clusterName }}','pdna/')))

      - bash: |
          echo "##vso[task.logissue type=error]Mandatory parameters: TARGET_URL and TenantName."
          exit 1
        displayName: 'Control TARGET_URL and TenantName parameters are mandatory'
        condition: and(
          and(ne('${{ parameters.scope }}','x3-services'), ne('${{ parameters.scope }}','wh-services')),
          eq('${{ parameters.authenticationType }}', 'sageId'),
          or(
          or(eq(variables['target_url'], 'none'), eq(variables['target_url'], '')),
          or(eq(variables['tenant_name'], 'none'), eq(variables['tenant_name'], ''))))

      - bash: |
          echo "##vso[task.logissue type=error]Mandatory parameter: application."
          exit 1
        displayName: 'Application parameter is mandatory'
        condition: and(
          and(ne('${{ parameters.scope }}','x3-services'), ne('${{ parameters.scope }}','wh-services')),
          or(eq('${{ parameters.tenantApp }}', 'none'), eq('${{ parameters.tenantApp }}', '')),
          or(eq('${{ parameters.tenantAppName }}', 'none'), eq('${{ parameters.tenantAppName }}', '')))

      - bash: |
          echo "##vso[task.logissue type=error]Mandatory parameters: TARGET_URL and endpointName1."
          exit 1
        displayName: 'Control TARGET_URL and endpointName1 parameters are mandatory'
        condition: and(
          or(eq('${{ parameters.scope }}','x3-services'), eq('${{ parameters.scope }}','wh-services')),
          eq('${{ parameters.authenticationType }}', 'sageId'),
          or(
          or(eq(variables['target_url'], 'none'), eq(variables['target_url'], '')),
          or(eq('${{ parameters.endPointName1 }}', 'none'), eq('${{ parameters.endPointName1 }}', ''))))

  - job: Provisioning_tenant_Execute_tests
    condition: succeeded()
    dependsOn: check_parameters_values
    displayName: Tenant provisioning steps, test execution steps
    timeoutInMinutes: $[ variables['pipelineTimeoutMins'] ]
    variables:
      exitMessage: 'An error occurred, see build results'
      accessToken: ''
    steps:
      - checkout: none

      - bash: |
          rm -r $HOME/.gitconfig
          rm -r .git
          rm -r .github
          rm -r .gitignore
          rm -r .husky
          rm -r .nvmrc
          rm -r .prettierrc
          rm -r .vscode
          rm -r .devcontainer
          rm -rf ./*
          rm -r .npmrc
          rm -r .pgdbrc
          rm -r .prettierignore
          rm -r .shellcheckrc
        displayName: 'Git - Remove artifacts'
        condition: or(eq(variables['Agent.Name'],'agentlindevops1'), eq(variables['Agent.Name'],'agentlindevops2'))
        continueOnError: true
        failOnStderr: false

      - template: ../../shared/manual-git-clone.yml
        parameters:
          depth: 100

      - task: Bash@3
        name: get_access_token
        condition: and(
          succeeded(),
          eq('${{parameters.TENANT_PROVISIONNING_METHOD}}','static'))
        inputs:
          targetType: 'filePath'
          filePath: '$(Build.SourcesDirectory)/pipelines/shared/scripts/get-cloud-manager-access-token-from-azure.sh'
          failOnStderr: true
        env:
          ENV_CLIENTID: $(secretCloudIdClientId)
          ENV_CLIENTSECRET: $(secretCloudIdClientSecret)
          ENV_AUDIENCE: $(cloudIdAudience)
          ENV_SAGEID_OAUTH_TOKEN_URL: $(sageIdOAuthTokenUrl)

      - task: Bash@3
        name: create_delete_tenant_app2
        condition: and(
          succeeded(),
          ne('${{parameters.tenantApp2}}','none'),
          eq('${{parameters.TENANT_PROVISIONNING_METHOD}}','static'))
        inputs:
          targetType: 'filePath'
          filePath: '$(Build.SourcesDirectory)/pipelines/app/create-delete-tenant-app.sh'
          failOnStderr: true
        env:
          ACCESS_TOKEN: $(accessToken)
          API_URL: $(apiUrl)
          TENANT_ID: $(tenant_id)
          USER_EMAIL: $(userEmail)
          APP_CODE: '${{ parameters.tenantApp2 }}'
          CLUSTER: $(clusterId)
          LAYERS: '${{ parameters.tenantLayer }}'
          INDIVIDUAL: 'atp'
          TENANT_MAIN_ADMIN_EMAIL: $(userEmail)

      - task: Bash@3
        name: decommission_tenant_if_provisioning_failed_app2
        condition: and(
          eq(variables['create_delete_tenant_app2.tenantProvisioningState'], 'false'),
          eq('${{parameters.TENANT_PROVISIONNING_METHOD}}','static'),
          ne('${{parameters.tenantApp2}}','none'))
        inputs:
          targetType: 'filePath'
          filePath: '$(Build.SourcesDirectory)/pipelines/app/create-delete-tenant-app.sh'
          failOnStderr: true
        env:
          ACCESS_TOKEN: $(accessToken)
          API_URL: $(apiUrl)
          TENANT_ID: $(tenant_id)
          USER_EMAIL: $(userEmail)
          APP_CODE: '${{ parameters.tenantApp2 }}'
          CLUSTER: $(clusterId)
          LAYERS: '${{ parameters.tenantLayer }}'
          INDIVIDUAL: 'atp'
          TENANT_MAIN_ADMIN_EMAIL: $(userEmail)
          DELETE_ONLY: 'true'

      - task: Bash@3
        name: create_delete_tenant_app
        condition: and(
          succeeded(),
          eq('${{parameters.TENANT_PROVISIONNING_METHOD}}','static'))
        inputs:
          targetType: 'filePath'
          filePath: '$(Build.SourcesDirectory)/pipelines/app/create-delete-tenant-app.sh'
          failOnStderr: true
        env:
          ACCESS_TOKEN: $(accessToken)
          API_URL: $(apiUrl)
          TENANT_ID: $(tenant_id)
          USER_EMAIL: $(userEmail)
          APP_CODE: '${{ parameters.tenantApp }}'
          CLUSTER: $(clusterId)
          LAYERS: '${{ parameters.tenantLayer }}'
          INDIVIDUAL: 'atp'
          TENANT_MAIN_ADMIN_EMAIL: $(userEmail)

      - ${{ if eq(parameters.SKIP_CUCUMBER_TESTS, 'No') }}:
          - bash: |
              pipelines/shared/scripts/install-chrome.sh
            displayName: 'Install latest stable chrome'
            condition: succeeded()
            continueOnError: true

      - ${{ if eq(parameters.SKIP_CUCUMBER_TESTS, 'No') }}:
          - template: pipelines/templates/install.yml@huracan
            parameters:
              installCliOnly: false

          - bash: |
              export NODE_OPTIONS=--max-old-space-size=8192
              pnpm run build
            displayName: 'PNPM - Build Xtrem'
      - ${{ if eq(parameters.SKIP_CUCUMBER_TESTS, 'No') }}:
          - template: ./cucumber-tests.yml
            parameters:
              authenticationType: '${{parameters.authenticationType}}'
              clusterId: '${{parameters.clusterId}}'
              clusterName: '${{parameters.clusterName}}'
              CUCUMBER_TAGS: '${{parameters.CUCUMBER_TAGS}}'
              endPointName1: '${{parameters.endPointName1}}'
              scope: '${{parameters.scope}}'
              SKIP_STAGE_TENANT_PROVISIONNING: '${{parameters.SKIP_STAGE_TENANT_PROVISIONNING}}'
              smokeTestType: '${{parameters.smokeTestType}}'
              tenant: '${{parameters.tenantId}}'
              TENANT_PROVISIONNING_METHOD: '${{parameters.TENANT_PROVISIONNING_METHOD}}'
              tenantLayer: '${{parameters.tenantLayer}}'
              tenantName: '${{parameters.tenantName}}'
              testType: '${{parameters.testType}}'
              TARGET_URL: '${{parameters.TARGET_URL}}'
              XTREM_TEST_MAX_INSTANCES: ${{parameters.XTREM_TEST_MAX_INSTANCES}}
              tenantApp: '${{parameters.tenantApp}}'
              tenantAppName: '${{parameters.tenantAppName}}'

      - task: Bash@3
        name: get_access_token_before_decomissioning
        condition: eq(variables['tenantcreateSuccess'],'Yes')

        inputs:
          targetType: 'filePath'
          filePath: '$(Build.SourcesDirectory)/pipelines/shared/scripts/get-cloud-manager-access-token-from-azure.sh'
          failOnStderr: true
        env:
          ENV_CLIENTID: $(secretCloudIdClientId)
          ENV_CLIENTSECRET: $(secretCloudIdClientSecret)
          ENV_AUDIENCE: $(cloudIdAudience)
          ENV_SAGEID_OAUTH_TOKEN_URL: $(sageIdOAuthTokenUrl)

      - task: Bash@3
        name: decommission_tenant_if_provisioning_failed
        condition: and(
          eq(variables['create_delete_tenant_app.tenantProvisioningState'], 'false'),
          eq('${{parameters.TENANT_PROVISIONNING_METHOD}}','static'))
        inputs:
          targetType: 'filePath'
          filePath: '$(Build.SourcesDirectory)/pipelines/app/create-delete-tenant-app.sh'
          failOnStderr: true
        env:
          ACCESS_TOKEN: $(accessToken)
          API_URL: $(apiUrl)
          TENANT_ID: $(tenant_id)
          USER_EMAIL: $(userEmail)
          APP_CODE: '${{ parameters.tenantApp }}'
          CLUSTER: $(clusterId)
          LAYERS: '${{ parameters.tenantLayer }}'
          INDIVIDUAL: 'atp'
          TENANT_MAIN_ADMIN_EMAIL: $(userEmail)
          DELETE_ONLY: 'true'

      - bash: |
          echo "##vso[task.setvariable variable=AT_STATUS;isOutput=true]$(Agent.JobStatus)"
          echo $(Agent.JobStatus)
        name: setStatus
