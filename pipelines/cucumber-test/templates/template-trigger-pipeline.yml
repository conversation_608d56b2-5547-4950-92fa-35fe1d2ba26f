parameters:
  - name: branch
    type: string
    default: ''
  - name: clusterName
    type: string
    default: ''
  - name: imageMajorVersion
    type: string
    default: 'none'
  - name: imageVersion
    type: string
    default: 'none'
  - name: CUCUMBER_TAGS
    type: string
    default: ''
  - name: pipelineToTrigger
    type: string
    default: ''

jobs:
  - job: TriggerPipelineJob
    displayName: 'Trigger ${{parameters.pipelineToTrigger}}'
    pool:
      vmImage: 'windows-2022'
    steps:
      - checkout: none
      - powershell: |
          $branch = '${{parameters.branch}}'
          $pat = '$(AzureDevOpsPAT)'
          $pipelineName = '${{parameters.pipelineToTrigger}}'
          $imageMajorVersion = '${{parameters.imageMajorVersion}}'
          $url = "https://dev.azure.com/Sage-LiveServices/X3%20XTREM/_apis/pipelines?api-version=6.0-preview.1"
          $headers = @{
              Authorization = "Basic " + [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(":$($pat)"))
          }
          try {
              $pipelinesInfo = Invoke-RestMethod -Uri $url -Headers $headers -Method Get
              if ($pipelinesInfo.value -ne $null) {
                  $pipeline = $pipelinesInfo.value | Where-Object { $_.name -eq $pipelineName }
                  if ($pipeline -ne $null) {
                      $pipelineId = $pipeline.id
                      Write-Host "Pipeline found with name : $pipelineName and id : $pipelineId"
                      $triggerUrl = "https://dev.azure.com/Sage-LiveServices/X3%20XTREM/_apis/pipelines/$pipelineId/runs?api-version=6.0-preview.1"
                      if ($imageMajorVersion -ne 'none') {
                          $requestBody = '
                            {
                                "resources": {
                                    "repositories": {
                                        "self": {
                                            "refName": "' + $branch + '"
                                        }
                                    }
                                },
                                "templateParameters": {
                                  "clusterName": "${{ parameters.clusterName }}",
                                  "imageMajorVersion": "${{ parameters.imageMajorVersion }}",
                                  "imageVersion": "${{ parameters.imageVersion }}",
                                  "CUCUMBER_TAGS": "${{ parameters.CUCUMBER_TAGS }}"
                                }
                            }
                          '
                      } else {
                          $requestBody = '
                            {
                                "resources": {
                                    "repositories": {
                                        "self": {
                                            "refName": "' + $branch + '"
                                        }
                                    }
                                },
                                "templateParameters": {
                                    "clusterName": "${{ parameters.clusterName }}",
                                    "CUCUMBER_TAGS": "${{ parameters.CUCUMBER_TAGS }}"
                                }
                            }
                          '
                      }
                      $requestBody = $requestBody | ConvertFrom-Json | ConvertTo-Json -Depth 12
                      $triggerResponse = Invoke-RestMethod -Uri $triggerUrl -Headers $headers -Method Post -Body $requestBody -ContentType "application/json"
                      Write-Host "Pipeline triggered with success. ID pipeline : $pipelineId"
                  } else {
                      Write-Host "No pipeline found with name : $pipelineName"
                  }
              } else {
                  Write-Host "No pipeline found"
              }
          } catch {
              Write-Host "Error when communicating with the Azure DevOps API : $_.Exception.Message"
          }
        displayName: 'Trigger Pipeline'
