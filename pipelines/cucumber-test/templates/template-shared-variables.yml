# File: template-shared-variables.yml
parameters:
  - name: clusterName
    type: string

  - name: endPointName1
    type: string

  - name: imageMajorVersion
    type: string

  - name: imageVersion
    type: string

  - name: scope
    type: string

  - name: TARGET_URL
    type: string

  - name: testType
    type: string

  - name: tenantName
    type: string

  - name: tenantId
    type: string

  - name: TENANT_PROVISIONNING_METHOD
    type: string

  - name: x3Environment
    type: string

variables:
  - group: sagex3ci_github
  - group: dockerXtrem
  - group: AWS_Mindo_QA_ADC_UT
  - group: ATP
  - ${{ if ne(parameters['x3Environment'],'none') }}:
      - group: SmokeTestEnvironmentVarsForADCQa
  - ${{ if startsWith(parameters['clusterName'],'deveu/') }}:
      - group: smokeTestEnvironmentVarsForDeveu
      - name: cluster
        value: $[ replace('${{ parameters['clusterName'] }}', 'deveu/', '') ]
      - name: envName
        value: 'Development'
  - ${{ if startsWith(parameters['clusterName'],'qana/') }}:
      - group: smokeTestEnvironmentVarsForQana
      - name: cluster
        value: $[ replace('${{ parameters['clusterName'] }}', 'qana/', '') ]
      - name: envName
        value: 'Quality'
  - ${{ if startsWith(parameters['clusterName'],'ppeu1/') }}:
      - group: smokeTestEnvironmentVarsForPPeu1
      - name: cluster
        value: $[ replace('${{ parameters['clusterName'] }}', 'ppeu1/', '') ]
      - name: envName
        value: 'Preprod'
  - ${{ if startsWith(parameters['clusterName'],'pdeu/') }}:
      - group: smokeTestEnvironmentVarsForPdeu
      - name: cluster
        value: $[ replace('${{ parameters['clusterName'] }}', 'pdeu/', '') ]
      - name: envName
        value: 'Production Europe'
  - ${{ if startsWith(parameters['clusterName'],'pdna/') }}:
      - group: smokeTestEnvironmentVarsForPdna
      - name: cluster
        value: $[ replace('${{ parameters['clusterName'] }}', 'pdna/', '') ]
      - name: envName
        value: 'Production North America'
  - name: runFunctionalTests
    value: $[eq('${{ parameters['testType']}}','Functional tests')]
  - name: runSmokeTests
    value: $[or(eq('${{ parameters['testType']}}','Smoke tests static'),eq('${{ parameters['testType']}}','All smoke tests'),eq('${{ parameters['testType']}}','Smoke tests minimum'))]
  - ${{ if startsWith(parameters['testType'],'All smoke tests') }}:
      - name: smoke_test_type
        value: 'all'
  - ${{ if startsWith(parameters['testType'],'Smoke tests static') }}:
      - name: smoke_test_type
        value: 'static'
  - ${{ if startsWith(parameters['testType'],'Smoke tests minimum') }}:
      - name: smoke_test_type
        value: 'minimum'
  - ${{ if startsWith(parameters['testType'],'Functional tests') }}:
      - name: smoke_test_type
        value: 'none'
  - name: runIntegrationTests
    value: $[eq('${{ parameters['testType']}}','Integration tests')]
  - name: target_url
    value: '${{parameters.TARGET_URL}}'
  - name: tenant_name
    value: '${{parameters.tenantName}}'
  - name: tenant_id
    value: '${{parameters.tenantId}}'
  - name: SG_NAME
    value: ADC-UNIT-TEST
  - name: INSTANCE_NAME
    value: ADC-SQL-UNIT-TEST
  - name: SQLUSER
    value: READONLYUT
  - name: X3_FOLDER
    value: X3RBTREF
  - name: tenant_MysqlName
    value: 'None'
  - name: clustername_MysqlName
    value: 'None'
  - ${{ if and(eq(parameters['TENANT_PROVISIONNING_METHOD'], 'static'), ne(parameters['scope'], 'x3-services'), ne(parameters['scope'], 'wh-services')) }}:
      - name: tenant_MysqlName
        value: '${{parameters.tenantName}}'
      - name: clustername_MysqlName
        value: '${{parameters.clusterName}}'
  - ${{ if and(eq(parameters['TENANT_PROVISIONNING_METHOD'], 'static'), or(eq(parameters['scope'], 'x3-services'), eq(parameters['scope'], 'wh-services'))) }}:
      - name: tenant_MysqlName
        value: '${{parameters.endPointName1}}'
      - name: clustername_MysqlName
        value: '${{parameters.x3Environment}}'
  - ${{ if and(eq(parameters['TENANT_PROVISIONNING_METHOD'], 'skipped'), ne(parameters['scope'], 'x3-services'), ne(parameters['scope'], 'wh-services')) }}:
      - name: tenant_MysqlName
        value: '${{parameters.tenantName}}'
      - name: clustername_MysqlName
        value: '${{parameters.clusterName}}'
  - ${{ if and(eq(parameters['TENANT_PROVISIONNING_METHOD'], 'skipped'), or(eq(parameters['scope'], 'x3-services'), eq(parameters['scope'], 'wh-services'))) }}:
      - name: tenant_MysqlName
        value: '${{parameters.endPointName1}}'
      - name: clustername_MysqlName
        value: '${{parameters.x3Environment}}'
  - ${{ if ne(parameters['imageVersion'], 'none') }}:
      - name: image_version
        value: '${{parameters.imageVersion}}'
  - ${{ if eq(parameters['imageVersion'], 'none') }}:
      - name: image_version
        value: 'N/A'
