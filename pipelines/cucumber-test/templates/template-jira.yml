parameters:
  - name: project
    type: string
  - name: summary
    type: string
  - name: DefinitionName
    type: string
  - name: RequestedFor
    type: string
  - name: SourceBranchName
    type: string
  - name: BuildId
    type: string
  - name: envName
    type: string
  - name: clusterName
    type: string
  - name: tenantName
    type: string
  - name: DMXT_AT_RELEASE
    type: string
  - name: DMXT_AT_SUCCESS
    type: string
  - name: qaLeader
    type: string
  - name: x3Applications
    type: string

steps:
  - task: DownloadBuildArtifacts@1
    inputs:
      buildType: 'current'
      downloadType: 'single'
      artifactName: 'ProgressTestResult'
      downloadPath: '$(Build.SourcesDirectory)/ProgressTestResult'
      checkDownloadedFiles: true

  - task: ksmazpipelinetask@1
    inputs:
      keepersecretconfig: $(ksmConfig)
      secrets: |
        fmn8W0gfynFDjjGdmkrS3A/field/password > var:var_password

  - powershell: |
      Write-Host "Creating Jira ticket because the test did not reach 100% of success."
      try{

        $FilePath = "$(Build.SourcesDirectory)\ProgressTestResult\merged-cucumber-progress-results.json"
        $data = Get-Content $FilePath -Raw | ConvertFrom-Json
        $FailedFiles += '{"name": "Unsuccesfull feature files", "isHeader": true} '

        foreach ($line in $data) {
          if ($line.status -ne "passed"){
            $FailedFiles += ', {"name": "' + $line.file + '"}'
          }
        }
          $headers = New-Object "System.Collections.Generic.Dictionary[[String],[String]]"
          $headers.Add("Authorization", "Bearer $(var_password)")
          $headers.Add("Content-Type", "application/json")
          $body = '
          {
              "fields": {
                  "project": {"key": "${{ parameters.project }}"},
                  "summary": "${{ parameters.summary }} Automation Pipeline Verification",
                  "issuetype": {"name": "Automated Test (Verification)"},
                  "reporter": {"name": "${{ parameters.qaLeader }}"},
                  "description": "The pipeline ${{ parameters.DefinitionName }} has been triggered by ${{ parameters.RequestedFor }} on the branch ${{ parameters.SourceBranchName }}.\nThe tests did not reach 100% of success.\nPlease check the logs for build with url: https://sage-liveservices.visualstudio.com/X3%20XTREM/_build/results?buildId=${{ parameters.BuildId }}",
                  "customfield_27409": "${{ parameters.DefinitionName }}",
                  "customfield_20704": "${{ parameters.envName }}",
                  "customfield_23811": "${{ parameters.clusterName }}",
                  "customfield_23812": "${{ parameters.tenantName }}",
                  "customfield_14200": "${{ parameters.DMXT_AT_RELEASE }}",
                  "customfield_30400": ${{ parameters.DMXT_AT_SUCCESS }},
                  "customfield_31600": [' + $FailedFiles + ']
              }
          }
          '

          if ("${{ parameters.x3Applications }}" -ne "none") {
            if ("${{ parameters.x3Applications }}" -eq "Sage Distribution and Manufacturing") {
              $x3Applications = "Sage Distribution and Manufacturing Operations"
            } else {
              $x3Applications = "${{ parameters.x3Applications }}"
            }
            $replacement = '"fields": { "customfield_18400": [{"value": "' + $x3Applications + '"}],'
            $body = $body -replace '"fields": {', $replacement

            Write-Host $body
          }

          $response = Invoke-RestMethod 'https://jira.sage.com/rest/api/2/issue' -Method 'POST' -Headers $headers -Body $body
          if($response){
              Write-Host "Jira ticket created successfully: https://jira.sage.com/browse/$($response.key)"
          }
          else{
              Write-Host "Error when creating the Jira ticket"
          }
        }
        catch{
            Write-Host "Error when communicating with the Jira API: $($_.Exception.Message)"
            $streamReader = [System.IO.StreamReader]::new($_.Exception.Response.GetResponseStream())
            $ErrResp = $streamReader.ReadToEnd() | ConvertFrom-Json
            $streamReader.Close()
        }
        $ErrResp
