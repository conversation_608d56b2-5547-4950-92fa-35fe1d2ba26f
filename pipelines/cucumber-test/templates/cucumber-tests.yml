parameters:
  - name: clusterId
    type: string
    displayName: 'Execution cluster'

  - name: clusterName
    type: string
    displayName: 'Name of cluster'

  - name: SKIP_STAGE_TENANT_PROVISIONNING
    displayName: SKIP_STAGE_TENANT_PROVISIONNING
    type: string
    default: ''

  - name: TENANT_PROVISIONNING_METHOD
    displayName: TENANT_PROVISIONNING_METHOD
    type: string
    default: ''

  - name: scope
    type: string
    displayName: 'The scope of the template: services, x3-services, ...'

  - name: tenant
    type: string
    displayName: 'Execution tenant'

  - name: endPointName1
    type: string
    displayName: 'endPointName1'
    default: 'X3RBTRUN'

  - name: XTREM_TEST_MAX_INSTANCES
    type: number
    displayName: 'XTREM_TEST_MAX_INSTANCES'
    default: 1

  - name: TARGET_URL
    type: string
    displayName: 'TARGET_URL'
    default: 'none'

  - name: CUCUMBER_TAGS
    type: string
    displayName: 'CUCUMBER_TAGS'
    default: 'none'

  - name: smokeTestType
    type: string
    displayName: 'smokeTestType'
    default: 'none'

  - name: testType
    type: string
    displayName: 'testType'

  - name: authenticationType
    displayName: Authentication Type
    type: string
    default: 'unsecure'

  - name: tenantName
    displayName: tenantName
    type: string
    default: 'none'

  - name: tenantApp
    type: string
    default: 'none'

  - name: tenantAppName
    type: string
    default: 'none'

  - name: tenantLayer
    displayName: tenantLayer
    type: string

steps:
  - task: Bash@3
    name: set_s3_upload_target_folder
    displayName: set s3 upload target folder
    env:
      SMOKE_TEST_TYPE: ${{ parameters.smokeTestType }}
    inputs:
      targetType: 'filePath'
      filePath: '$(Build.SourcesDirectory)/pipelines/cucumber-test/scripts/setS3UploadTargetFolder.sh'
      arguments: $(Build.DefinitionName) $(Build.BuildId)
      failOnStderr: true

  - script: |
      services=""
      if [ "${{ parameters.smokeTestType }}" != "none" ]; then
        services="|services"
      fi
      XTREM_CI=1 XTREM_SCOPES="platform$services"
    displayName: 'Set project variables'
    condition: succeeded()

  - task: Bash@3
    displayName: 'Display parameters'
    name: 'display_parameters'
    condition: succeeded()
    env:
      SMOKE_TEST_TYPE: ${{ parameters.smokeTestType }}
      TEST_TYPE: ${{ parameters.testType }}
      CLUSTER_ID: ${{ parameters.clusterId }}
      CLUSTER_NAME: ${{ parameters.clusterName }}
      SKIP_STAGE_TENANT_PROVISIONNING: ${{ parameters.SKIP_STAGE_TENANT_PROVISIONNING }}
      TENANT_PROVISIONNING_METHOD: '${{parameters.TENANT_PROVISIONNING_METHOD}}'
      AUTHENTICATION_TYPE: ${{ parameters.authenticationType }}
      TARGET_URL: ${{parameters.TARGET_URL}}
      SCOPE: ${{ parameters.scope }}
      ENDPOINT_NAME_1: ${{ parameters.endPointName1 }}
      TENANT: ${{ parameters.tenant }}
      TENANT_NAME: ${{ parameters.tenantName }}
      TENANT_APP: ${{ parameters.tenantApp }}
      TENANT_APP_NAME: ${{ parameters.tenantAppName }}
      TENANT_LAYER: ${{ parameters.tenantLayer }}
      XTREM_TEST_MAX_INSTANCES: ${{ parameters.XTREM_TEST_MAX_INSTANCES }}
      CUCUMBER_TAGS: ${{ parameters.CUCUMBER_TAGS }}
      testUser: $(testUser)
      timeout: $(timeout)
      timeoutLocks: $(timeoutLocks)
      timeoutCucumber: $(timeoutCucumber)
      timeoutWaitFor: $(timeoutWaitFor)
      timeoutWaitForLoading: $(timeoutWaitForLoading)
      LOGIN_USER_NAME: $(loginUserName)
      LOGIN_USER_NAME2: $(loginUserName2)
      LOGIN_USER_NAME3: $(loginUserName3)
      downloadFolder: $(downloadFolder)

    inputs:
      targetType: 'filePath'
      filePath: '$(Build.SourcesDirectory)/pipelines/cucumber-test/scripts/displayParameters.sh'
      failOnStderr: true

  - task: Bash@3
    displayName: 'Execute Cucumber tests'
    name: 'cucumberTests'
    condition: and(succeeded(),
      or(eq(variables.runFunctionalTests, 'true'), eq(variables.runSmokeTests, 'true'), eq(variables.runIntegrationTests, 'true'))
      )
    env:
      SMOKE_TEST_TYPE: ${{ parameters.smokeTestType }}
      CLUSTER_ID: ${{ parameters.clusterId }}
      CLUSTER_NAME: ${{ parameters.clusterName }}
      SKIP_STAGE_TENANT_PROVISIONNING: ${{ parameters.SKIP_STAGE_TENANT_PROVISIONNING }}
      AUTHENTICATION_TYPE: ${{ parameters.authenticationType }}
      TARGET_URL: ${{parameters.TARGET_URL}}
      SCOPE: ${{ parameters.scope }}
      ENDPOINT_NAME_1: ${{ parameters.endPointName1 }}
      TENANT: ${{ parameters.tenant }}
      TENANT_NAME: ${{ parameters.tenantName }}
      TENANT_APP: ${{ parameters.tenantApp }}
      TENANT_APP_NAME: ${{ parameters.tenantAppName }}
      XTREM_TEST_MAX_INSTANCES: ${{ parameters.XTREM_TEST_MAX_INSTANCES }}
      CUCUMBER_TAGS_INPUT: ${{ parameters.CUCUMBER_TAGS }}
      testUser: $(testUser)
      timeout: '$(timeout)'
      timeoutLocks: '$(timeoutLocks)'
      timeoutCucumber: '$(timeoutCucumber)'
      timeoutWaitFor: '$(timeoutWaitFor)'
      timeoutWaitForLoading: '$(timeoutWaitForLoading)'
      JAVA_HOME: $(JAVA_HOME_11_X64)
      PATH: $(JAVA_HOME_11_X64)/bin;$(PATH)
      LOGIN_USER_NAME: $(loginUserName)
      LOGIN_PASSWORD: $(loginPassword)
      LOGIN_USER_NAME2: $(loginUserName2)
      LOGIN_PASSWORD2: $(loginPassword2)
      LOGIN_USER_NAME3: $(loginUserName3)
      LOGIN_PASSWORD3: $(loginPassword3)
      SAGE_INTACTT_PASSWORD: $(sageIntacctPassword)
      SAGE_FRP_1000_PASSWORD: $(sageFrp1000Password)
      SMOKE_TEST_AUTH_SECRET: $(smokeTestsAuthSecret)
      SMOKE_TEST_AUTH_ENV_NAME: $(smokeTestAuthEnvName)
      downloadFolder: $(downloadFolder)

    inputs:
      targetType: 'filePath'
      filePath: '$(Build.SourcesDirectory)/pipelines/cucumber-test/scripts/executeCucumberTests.sh'
      arguments: $(Agent.TempDirectory) $(Build.SourcesDirectory)
    continueOnError: true

  - script: |
      find $(Build.SourcesDirectory)/${{ parameters.scope }}/*/*/test-report/cucumber/*.json
    displayName: 'Check smoke test reports'
    condition: and(succeeded(),eq(variables.runSmokeTests, 'true'))
    continueOnError: false

  #  - task: Bash@3
  #    name: check_test_reports
  #    displayName: 'Check functional test reports'
  #    condition: and(succeededOrFailed(), eq(variables.runFunctionalTests, 'true'))
  #    inputs:
  #      targetType: 'filePath'
  #     filePath: '$(Build.SourcesDirectory)/pipelines/cucumber-test/scripts/checkTestReports.sh'
  #      arguments: $(Build.SourcesDirectory) $(Agent.TempDirectory) ${{parameters.scope}}
  #      failOnStderr: true

  - task: Bash@3
    name: collect_test_results
    displayName: 'Collect functional / integration test results'
    condition: and(succeededOrFailed(),
      or(eq(variables.runFunctionalTests, 'true'), eq(variables.runIntegrationTests, 'true'))
      )
    inputs:
      targetType: 'filePath'
      filePath: '$(Build.SourcesDirectory)/pipelines/cucumber-test/scripts/collectTestResults.sh'
      arguments: $(Build.SourcesDirectory) $(Agent.TempDirectory) ${{parameters.scope}}
      failOnStderr: true

  - task: Bash@3
    name: collect_smoke_test_results
    displayName: 'Collect smoke test results'
    condition: and(succeeded(),eq(variables.runSmokeTests, 'true'))
    inputs:
      targetType: 'filePath'
      filePath: '$(Build.SourcesDirectory)/pipelines/cucumber-test/scripts/collectSmokeTestResults.sh'
      arguments: $(Build.SourcesDirectory) $(Agent.TempDirectory) ${{parameters.scope}}
      failOnStderr: true
    continueOnError: false

  - task: Bash@3
    name: collect_accessibility_test_results
    displayName: 'Collect accessibility test results'
    condition: and(succeededOrFailed(), eq(variables.runFunctionalTests, 'true'))
    inputs:
      targetType: 'filePath'
      filePath: '$(Build.SourcesDirectory)/pipelines/cucumber-test/scripts/collectAccessibilityTestResults.sh'
      arguments: $(Build.SourcesDirectory) $(Agent.TempDirectory) ${{parameters.scope}}
      failOnStderr: true

  - task: Bash@3
    name: collect_progress_results
    displayName: 'Collect progress test results'
    condition: and(succeededOrFailed(),
      or(eq(variables.runFunctionalTests, 'true'), eq(variables.runIntegrationTests, 'true'))
      )
    inputs:
      targetType: 'filePath'
      filePath: '$(Build.SourcesDirectory)/pipelines/cucumber-test/scripts/collectProgressResults.sh'
      arguments: $(Build.SourcesDirectory) $(Agent.TempDirectory) ${{parameters.scope}}
      failOnStderr: true

  - task: Bash@3
    name: collect_allure_test_results
    displayName: 'Collect allure test results'
    condition: and(succeededOrFailed(),
      or(eq(variables.runFunctionalTests, 'true'), eq(variables.runIntegrationTests, 'true'))
      )
    inputs:
      targetType: 'filePath'
      filePath: '$(Build.SourcesDirectory)/pipelines/cucumber-test/scripts/collectAllureResults.sh'
      arguments: $(Build.SourcesDirectory) $(Agent.TempDirectory) ${{parameters.scope}}
      failOnStderr: true

  - script: |
      cd $(Build.SourcesDirectory)
      pnpm i -g copyfiles
      shopt -s globstar
      if [ -z "$(ls -A $(Agent.TempDirectory)/allure-results)" ]; then
        echo "Folder Empty"
      else
        echo "allure-results folder content:"
        ls -lh '$(Agent.TempDirectory)/allure-results'
        cd $(Build.SourcesDirectory) && pnpm run merge-test-result-files $(Agent.TempDirectory)/allure-results
        mv $(Agent.TempDirectory)/allure-results/merged.json $(Agent.TempDirectory)/cucumber-json/
      fi
    displayName: 'Prepare allure test result files for publishing'
    condition: succeededOrFailed()

  - script: |
      cd $(Build.SourcesDirectory)
      pnpm i -g copyfiles
      shopt -s globstar
      copyfiles ./*/*/*/test-report/cucumber-progress-results/**/* $(Agent.TempDirectory)/cucumber-progress-results/

      if [ -z "$(ls -A $(Agent.TempDirectory)/cucumber-progress-results)" ]; then
        echo "Folder Empty"
        echo "##vso[task.setvariable variable=progressResultsExist;isOutput=true]false"
      else
        echo "##vso[task.setvariable variable=progressResultsExist;isOutput=true]true"
      fi
    displayName: 'Prepare Cucumber Progress test result'
    name: checkProgressResultsExist
    condition: succeededOrFailed()

  - script: |
      if [ -z "$(ls -A  $(Agent.TempDirectory)/allure-results)" ]; then
        echo "Folder Empty"
        echo "##vso[task.setvariable variable=allureTestResultsExist;isOutput=true]false"
      else
        echo "##vso[task.setvariable variable=allureTestResultsExist;isOutput=true]true"
      fi
    displayName: 'Check if Allure test result files exist'
    name: checkAllureTestResultsExist
    condition: succeededOrFailed()

  - script: |
      if [ -z "$(ls -A $(Agent.TempDirectory)/cucumber-accessibility)" ]; then
        echo "Folder Empty"
        echo "##vso[task.setvariable variable=accessibilityResultsExist;isOutput=true]false"
      else
        echo "##vso[task.setvariable variable=accessibilityResultsExist;isOutput=true]true"
      fi
    displayName: 'Check if Accessibility test result files exist'
    name: checkAccessibilityResultsExist
    condition: succeededOrFailed()

  - task: PublishAllureReport@1
    condition: and(succeededOrFailed(), eq(variables['checkAllureTestResultsExist.allureTestResultsExist'], 'true'))
    displayName: 'Publish allure report'
    inputs:
      allureVersion: '2.29.0'
      testResultsDir: '$(Agent.TempDirectory)/allure-results'
      reportName: '$(Build.DefinitionName)'

  - task: PublishTestResults@2
    displayName: 'Publish integration test results'
    name: integration_test_results
    condition: and(succeededOrFailed(),
      or(eq(variables.runFunctionalTests, 'true'),eq(variables.runSmokeTests, 'true'),eq(variables.runIntegrationTests, 'true'))
      )
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: '*.xml'
      searchFolder: '$(Agent.TempDirectory)/cucumber-junit/'
      mergeTestResults: true
      failTaskOnFailedTests: false
      testRunTitle: 'Integration tests'

  - task: PublishPipelineArtifact@1
    displayName: 'PublishPipelineArtifact AllureTestResult'
    inputs:
      targetPath: '$(Agent.TempDirectory)/cucumber-json/merged.json'
      artifact: 'AllureTestResult'
      publishLocation: 'pipeline'

  - task: PublishPipelineArtifact@1
    displayName: 'PublishPipelineArtifact packageJsonservices'
    inputs:
      targetPath: '$(Build.SourcesDirectory)/${{ parameters.scope }}/package.json'
      artifact: 'packageJsonservices'
      publishLocation: 'pipeline'

  - task: PublishPipelineArtifact@1
    displayName: 'PublishPipelineArtifact packageJsonx3'
    inputs:
      targetPath: '$(Build.SourcesDirectory)/${{ parameters.scope }}/package.json'
      artifact: 'packageJsonx3'
      publishLocation: 'pipeline'

  - task: PublishPipelineArtifact@1
    displayName: 'PublishPipelineArtifact AccessibilityTestResult'
    inputs:
      targetPath: '$(Agent.TempDirectory)/cucumber-accessibility/merged-accessibility-test.json'
      artifact: 'AccessibilityTestResult'
      publishLocation: 'pipeline'
    condition: eq(variables['checkAccessibilityResultsExist.accessibilityResultsExist'], 'true')

  - task: PublishPipelineArtifact@1
    displayName: 'PublishPipelineArtifact ProgressTestResult'
    inputs:
      targetPath: '$(Agent.TempDirectory)/cucumber-progress-results/merged-cucumber-progress-results.json'
      artifact: 'ProgressTestResult'
      publishLocation: 'pipeline'
    condition: and(eq(variables['checkprogressResultsExist.progressResultsExist'], 'true'),
      or(eq(variables.runFunctionalTests, 'true'), eq(variables.runIntegrationTests, 'true'))
      )
