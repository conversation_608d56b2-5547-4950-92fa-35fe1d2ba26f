parameters:
  - name: Branch
    type: string
    displayName: 'Branch'

  - name: Cluster
    type: string
    displayName: 'Cluster'

  - name: Tenant
    type: string
    displayName: 'Tenant'

  - name: Scope
    type: string
    displayName: 'Scope'

  - name: Type
    type: string
    displayName: 'Type'

  - name: Pipeline
    type: string
    displayName: 'Pipeline'

  - name: Job
    type: string
    displayName: 'Job'

steps:
  - task: DownloadBuildArtifacts@1
    displayName: 'Download AllureTestResult artifact'
    inputs:
      buildType: 'current'
      downloadType: 'single'
      artifactName: 'AllureTestResult'
      downloadPath: '$(Build.SourcesDirectory)'
      checkDownloadedFiles: true

  - task: DownloadBuildArtifacts@1
    displayName: 'Download packageJsonx3 artifact'
    inputs:
      buildType: 'current'
      downloadType: 'single'
      artifactName: 'packageJsonx3'
      downloadPath: '$(Build.SourcesDirectory)\packageJsonx3'
      checkDownloadedFiles: true

  - task: DownloadBuildArtifacts@1
    displayName: 'Download packageJsonservices artifact'
    inputs:
      buildType: 'current'
      downloadType: 'single'
      artifactName: 'packageJsonservices'
      downloadPath: '$(Build.SourcesDirectory)\packageJsonservices'
      checkDownloadedFiles: true

  - task: DownloadBuildArtifacts@1
    displayName: 'Download AccessibilityTestResult artifact'
    inputs:
      buildType: 'current'
      downloadType: 'single'
      artifactName: 'AccessibilityTestResult'
      downloadPath: '$(Build.SourcesDirectory)\AccessibilityTestResult'
      checkDownloadedFiles: true
    condition: eq(variables.cucumberAccessibilityResultsExists, 'true')

  - task: CmdLine@2
    inputs:
      script: 'dir'

  - task: PowerShell@2
    displayName: 'Data integration into mysql'
    name: 'WriteJsonFromTestXtrem'
    inputs:
      filePath: 'ScriptXtrem\WriteJsonFromTestXtrem.ps1'
      arguments: -fileToIntegre "$(Build.SourcesDirectory)\\merged.json" -PackagefileX3 "$(Build.SourcesDirectory)\packageJsonx3\package.json" -Packagefileservices "$(Build.SourcesDirectory)\packageJsonservices\package.json" -AT_BRANCH "${{ parameters.Branch }}" -AT_CLUSTER "${{ parameters.Cluster }}" -AT_TENANT "${{ parameters.Tenant }}" -AT_SCOPE "${{ parameters.Scope }}" -AT_TYPE "${{ parameters.Type }}" -AT_PIPELINE "${{ parameters.Pipeline }}" -AT_JOB ${{ parameters.Job }}

  - task: PowerShell@2
    displayName: 'Accessibility Data integration into mysql'
    name: 'WriteJsonFromAccessibilityTestXtrem'
    inputs:
      filePath: 'ScriptXtrem\WriteJsonFromAccessibilityTestXtrem.ps1'
      arguments: -fileToIntegre "$(Build.SourcesDirectory)\AccessibilityTestResult\merged-accessibility-test.json" -PackagefileX3 "$(Build.SourcesDirectory)\packageJsonx3\package.json" -Packagefileservices "$(Build.SourcesDirectory)\packageJsonservices\package.json" -AT_BRANCH "${{ parameters.Branch }}" -AT_CLUSTER "${{ parameters.Cluster }}" -AT_TENANT "${{ parameters.Tenant }}" -AT_SCOPE "${{ parameters.Scope }}" -AT_TYPE "${{ parameters.Type }}" -AT_PIPELINE "${{ parameters.Pipeline }}" -AT_JOB ${{ parameters.Job }}
    condition: eq(variables.cucumberAccessibilityResultsExists, 'true')
