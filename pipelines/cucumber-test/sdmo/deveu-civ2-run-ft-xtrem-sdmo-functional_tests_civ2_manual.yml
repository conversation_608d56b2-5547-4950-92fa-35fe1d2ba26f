name: deveu-civ2-run-ft-xtrem-sdmo-functional_tests_civ2_manual
#force Automatic trigger
resources:
  pipelines:
    - pipeline: generic-upgrade-cluster-app #internal ID for the script
      source: generic-upgrade-cluster-app #name of pipeline as it appears in Azure
      trigger:
        tags:
          - clsName-ci-v2-sdmo
        branches:
          include:
            - master
  repositories:
    # huracan repository reference used by template-run-xtrem-services-v2.yml
    - repository: huracan
      type: github
      endpoint: Sage-ERP-X3
      name: Sage-ERP-X3/huracan
      # use stable branch v2.0 for huracan
      ref: v2.0

    - repository: githubProject
      type: github
      name: 'Sage-ERP-X3/X3-QualityM'
      endpoint: Sage-ERP-X3
      ref: master

pr: none
trigger: none

pool: 'x3-ubuntu'

parameters:
  - name: scope
    displayName: test scope
    type: string
    default: 'services'

  - name: clusterName
    displayName: Environment and cluster to use
    type: string
    default: 'deveu/ci-v2'
    values:
      - deveu/ci-v2
      - deveu/cls-release
      - qana/cls-release
      - ppeu1/cls-release
      - ppeu1/pp-prd
      - pdeu/pilot
      - pdna/pilot

  - name: TENANT_PROVISIONNING_METHOD
    displayName: Tenant provisioning method
    type: string
    default: 'static'
    values:
      - skipped
      - static

  - name: SKIP_CUCUMBER_TESTS
    displayName: Skip tests execution
    type: string
    default: 'Yes'
    values:
      - No
      - Yes

  - name: authenticationType
    displayName: Authentication Type
    type: string
    default: 'unsecure'
    values:
      - unsecure
      - sageId

  - name: TARGET_URL
    type: string
    displayName: 'TARGET_URL'
    default: 'https://sdmo-ci-v2.eu.dev-sagextrem.com/'

  - name: tenantId
    displayName: tenantId
    type: string
    default: 'XQUHE4MaQkWZCwId6UZvp'

  - name: tenantName
    displayName: tenantName
    type: string
    default: 'functional_tests_civ2_manual'

  - name: endPointName1
    type: string
    displayName: 'endPointName1'
    default: 'none'

  - name: tenantLayer
    displayName: Layer for provisioning the tenant
    type: string
    default: 'setup,qa'
    values:
      - none
      - setup,test
      - setup,qa
      - setup,demo

  - name: tenantApp
    displayName: Tenant application
    type: string
    default: 'sdmo'
    values:
      - glossary
      - sdmo
      - shopfloor
      - showcase
      - none

  - name: tenantAppName
    displayName: Tenant application Name
    type: string
    default: 'Sage Distribution and Manufacturing'
    values:
      - Glossary
      - Sage Distribution and Manufacturing
      - Shop Floor Control
      - Showcase
      - none

  - name: testType
    displayName: Type of tests to execute
    type: string
    default: 'Functional tests'
    values:
      - All smoke tests
      - Smoke tests static
      - Functional tests

  - name: XTREM_TEST_MAX_INSTANCES
    displayName: MAX INSTANCES
    type: number
    default: 4

  - name: CUCUMBER_TAGS
    type: string
    displayName: 'CUCUMBER_TAGS'
    default: 'none'

  - name: writeSQL
    displayName: Write data to mysql
    type: string
    default: 'No'
    values:
      - Yes
      - No

variables:
  - template: ../templates/template-shared-variables.yml
    parameters:
      clusterName: '${{parameters.clusterName}}'
      endPointName1: '${{parameters.endPointName1}}'
      imageMajorVersion: 'none'
      imageVersion: 'none'
      scope: '${{parameters.scope}}'
      TARGET_URL: '${{parameters.TARGET_URL}}'
      testType: '${{parameters.testType}}'
      tenantName: '${{parameters.tenantName}}'
      tenantId: '${{parameters.tenantId}}'
      TENANT_PROVISIONNING_METHOD: '${{parameters.TENANT_PROVISIONNING_METHOD}}'
      x3Environment: 'none'

stages:
  - stage: StartAgent
    condition: ${{ eq(parameters.writeSQL, 'Yes') }}
    displayName: StartAgent
    jobs:
      - job: StartAgent
        displayName: StartAgent
        pool:
          vmImage: 'windows-2022'
        workspace:
          clean: all
        steps:
          - checkout: none
          - task: AWSCLI@1
            inputs:
              awsCredentials: 'ConnectAWS'
              regionName: 'eu-west-1'
              awsCommand: 'ec2'
              awsSubCommand: 'start-instances'
              awsArguments: '--instance-ids "i-03fb922f1fa8c84fe'

  - stage: Run_template_st
    condition: always()
    pool: 'x3-ubuntu'
    jobs:
      - template: ../templates/template-run-xtrem-services-v2.yml
        parameters:
          authenticationType: '${{parameters.authenticationType}}'
          cluster: '$(cluster)'
          clusterId: '$(cluster)'
          clusterName: '${{parameters.clusterName}}'
          CUCUMBER_TAGS: '${{parameters.CUCUMBER_TAGS}}'
          endPointName1: '${{parameters.endPointName1}}'
          scope: '${{parameters.scope}}'
          SKIP_CUCUMBER_TESTS: '${{parameters.SKIP_CUCUMBER_TESTS}}'
          SKIP_STAGE_TENANT_PROVISIONNING: $[ replace('${{ parameters['TENANT_PROVISIONNING_METHOD'] }}', 'skipped', 'Yes') ]
          smokeTestType: '$(smoke_test_type)'
          TARGET_URL: '$(target_url)'
          TENANT_PROVISIONNING_METHOD: '${{parameters.TENANT_PROVISIONNING_METHOD}}'
          tenantApp: '${{parameters.tenantApp}}'
          tenantAppName: '${{parameters.tenantAppName}}'
          tenantId: '$(tenant_id)'
          tenantLayer: '${{parameters.tenantLayer}}'
          tenantName: '$(tenant_name)'
          testType: '${{parameters.testType}}'
          XTREM_TEST_MAX_INSTANCES: ${{parameters.XTREM_TEST_MAX_INSTANCES}}

  - stage: WriteToMysql
    condition: and(${{ eq(parameters.writeSQL, 'Yes') }}, ${{ eq(parameters.SKIP_CUCUMBER_TESTS, 'No') }})
    dependsOn:
      - Run_template_st
    displayName: WriteToMysql
    jobs:
      - job: SaveData
        displayName: WriteToMysql
        pool:
          name: X3DEVOPS
          demands: Agent.Name -equals AGENTX3DEVOPS
        workspace:
          clean: all
        steps:
          - checkout: githubProject
          - template: ../templates/template-XtremWriteJSONToMysql.yml
            parameters:
              Branch: '$(Build.SourceBranchName)'
              Cluster: '$(clustername_MysqlName)'
              Tenant: '$(tenant_MysqlName)'
              Scope: '${{parameters.scope}}'
              Type: '${{parameters.testType}}'
              Pipeline: '$(Build.DefinitionName)'
              Job: '$(Build.BuildId)'
