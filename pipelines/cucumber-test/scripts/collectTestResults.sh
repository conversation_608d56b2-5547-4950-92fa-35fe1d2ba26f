#!/usr/bin/env bash
# Arguments:
# 1 $(Build.SourcesDirectory)
# 2 $(Agent.TempDirectory)
# 3 ${{parameters.scope}}


scope=$(echo $1/$3/functional-tests/*)
for dir in $scope; do
    if [ $dir/test-report/cucumber ] && [ $dir/test-report/junit ]; then
        if [ -n "$(ls -A $dir/test-report/cucumber)" ]; then
            cp $dir/test-report/cucumber/* $2/cucumber-json/
        fi
        if [ -n "$(ls -A $dir/test-report/junit)" ]; then
            cp $dir/test-report/junit/* $2/cucumber-junit/
        fi
    fi
done


find $2/cucumber-junit/ -size 0 -delete
cd $1 && pnpm run merge-test-result-files $2/cucumber-json
