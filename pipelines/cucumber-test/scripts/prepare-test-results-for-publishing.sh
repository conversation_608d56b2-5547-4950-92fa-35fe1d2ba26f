#!/usr/bin/env bash

# Prepare test results for publishing (after their execution in PR pipeline)

# 2 env variables must be set (because they will differ in Azure and GitHub Actions):
# TEMP_FOLDER: temp folder
# SOURCE_FOLDER: sources directory

set -e

shopt -s globstar
pnpm i -g copyfiles
copyfiles -f ./*/*/*/test-report/cucumber/* "$TEMP_FOLDER"/cucumber-json/
copyfiles -f ./*/*/*/test-report/junit/* "$TEMP_FOLDER"/cucumber-junit/
copyfiles -f ./*/*/*/test-report/allure-results/* "$TEMP_FOLDER"/allure-results/
find "$TEMP_FOLDER"/cucumber-junit/ -size 0 -delete

if [ -z "$(ls -A "$TEMP_FOLDER"/cucumber-json)" ]; then
    echo "No cucumber reports found"
else
    echo "Cucumber reports found. Merging them..."
    cd "$SOURCE_FOLDER" && pnpm run merge-test-result-files "$TEMP_FOLDER"/cucumber-json
fi
