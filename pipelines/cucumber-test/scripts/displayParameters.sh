#!/usr/bin/env bash

# Display pipeline parameters
# Env:
# SMOKE_TEST_TYPE: ${{ parameters.smokeTestType }}
# CLUSTER_ID: ${{ parameters.clusterId }}
# SKIP_STAGE_TENANT_PROVISIONNING: ${{ parameters.parameters.SKIP_STAGE_TENANT_PROVISIONNING }}
# AUTHENTICATION_TYPE: ${{ parameters.authenticationType }}
# TARGET_URL: ${{parameters.TARGET_URL}}
# SCOPE: ${{ parameters.scope }}
# ENDPOINT_NAME_1: ${{ parameters.parameters.endPointName1 }}
# TENANT: ${{ parameters.tenant }}
# TENANT_NAME: ${{ parameters.tenantName }}
# XTREM_TEST_MAX_INSTANCES: ${{ parameters.XTREM_TEST_MAX_INSTANCES }}
# CUCUMBER_TAGS: ${{ parameters.CUCUMBER_TAGS }}
# testUser:  $(testUser)
# timeout: $(timeout)
# timeoutLocks: $(timeoutLocks)
# timeoutCucumber: $(timeoutCucumber)
# timeoutWaitFor: $(timeoutWaitFor)
# timeoutWaitForLoading: $(timeoutWaitForLoading)
# downloadFolder: $(downloadFolder)

# shellcheck disable=SC2154

echo "Pipeline Parameters:"

echo "Test scope=${SCOPE}"

if [[ ${SCOPE} != "x3-services" &&  ${SCOPE} != "wh-services" ]]; then
    echo "clusterName=${CLUSTER_NAME}"
    echo "TENANT_PROVISIONNING_METHOD=${TENANT_PROVISIONNING_METHOD}"
fi

echo "authenticationType=${AUTHENTICATION_TYPE}"

if [[ ${AUTHENTICATION_TYPE} = "unsecure" ]]; then

    case "$CLUSTER_NAME" in
        deveu/ci-v2 | deveu/cls-release)
            export TARGET_URL="https://login.eu.dev-sagextrem.com/unsecuredevlogin?cluster=${CLUSTER_ID}&app=${TENANT_APP}&tenant=${TENANT}"
            echo URL defaulted to TARGET_URL="https://login.eu.dev-sagextrem.com/unsecuredevlogin?cluster=${CLUSTER_ID}&app=${TENANT_APP}&tenant=${TENANT}"
            ;;
        qana/cls-release)
            export TARGET_URL="https://login.na.qa-sagextrem.com/unsecuredevlogin?cluster=${CLUSTER_ID}&app=${TENANT_APP}&tenant=${TENANT}"
            echo URL defaulted to TARGET_URL="https://login.na.qa-sagextrem.com/unsecuredevlogin?cluster=${CLUSTER_ID}&app=${TENANT_APP}&tenant=${TENANT}"
            ;;
        ppeu1/cls-release | ppeu1/pp-prd)
            export TARGET_URL="https://login.eu1.pp-sagextrem.com/unsecuredevlogin?cluster=${CLUSTER_ID}&app=${TENANT_APP}&tenant=${TENANT}"
            echo URL defaulted to TARGET_URL="https://login.eu1.pp-sagextrem.com/unsecuredevlogin?cluster=${CLUSTER_ID}&app=${TENANT_APP}&tenant=${TENANT}"
            ;;
        *)
            echo "cluster not allowed for unsecure login"
        ;;
    esac
else
    echo "TARGET_URL=${TARGET_URL}"
fi

if [[  ${SCOPE} == "x3-services" ||  ${SCOPE} == "wh-services" ]]; then
    echo "endPointName1=${ENDPOINT_NAME_1}"
else
    echo "TenantId=${TENANT}"
    if [[ ${AUTHENTICATION_TYPE} != "unsecure" ]]; then
        echo "tenantName=${TENANT_NAME}"
    fi
    echo "tenantLayer=${TENANT_LAYER}"
    echo "tenantApp=${TENANT_APP}"
    echo "tenantAppName=${TENANT_APP_NAME}"
fi
echo "loginUserName=${LOGIN_USER_NAME}"
echo "loginUserName2=${LOGIN_USER_NAME2}"
echo "loginUserName3=${LOGIN_USER_NAME3}"
echo "testType=${TEST_TYPE}"
echo "smokeTestType=${SMOKE_TEST_TYPE}"
echo "XTREM_TEST_MAX_INSTANCES=${XTREM_TEST_MAX_INSTANCES}"
echo "CUCUMBER_TAGS=${CUCUMBER_TAGS}"
echo "testUser=${testUser}"
echo "timeout=${timeout}"
echo "timeoutLocks=${timeoutLocks}"
echo "timeoutCucumber=${timeoutCucumber}"
echo "timeoutWaitFor=${timeoutWaitFor}"
echo "timeoutWaitForLoading=${timeoutWaitForLoading}"
echo "downloadFolder=${downloadFolder}"
