#!/usr/bin/env bash
# Arguments:
# 1 $(Build.SourcesDirectory)
# 2 $(Agent.TempDirectory)
# 3 ${{parameters.scope}}

scope=$(echo $1/$3/functional-tests/*)

for dir in $scope; do
      if [ -d $dir/test-report/allure-results ]; then
         if [ -n "$(ls -A $dir/test-report/allure-results)" ]; then
            cp $dir/test-report/allure-results/* $2/allure-results/
         fi
     fi
   done

ls $2/allure-results
