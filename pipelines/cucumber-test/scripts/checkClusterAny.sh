#!/usr/bin/env bash

# Check the status of an xtrem cluster

# Env vars
# BUILD_BUILDNUMBER
# API_URL
# CLUSTER_RESPONSE
# CLUSTER_STATUS

INDIVIDUAL="cucumber-pipeline/${BUILD_BUILDNUMBER}"

CLUSTER_URL="${API_URL}/cluster/${2}"
CLUSTER_RESPONSE=$(curl -sS -X GET -H "Content-Type:application/json" -H "Authorization:Bearer ${1}" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${CLUSTER_URL}")

# Check the current status of the tenant
CLUSTER_STATUS=$(echo "${CLUSTER_RESPONSE}" | jq -r '.status')

if [ "null" = "${CLUSTER_STATUS}" ]; then
    echo "Error while fetching tenant satus, probably because the api call return an error :${errorMessage}"
    echo "##vso[task.setvariable variable=exitMessage;]An error occurred when trying to check the tenant status: ${errorMessage}"
    exit 2
fi

if [ "Ready" = "${CLUSTER_STATUS}" ]; then
    echo "Cluster is Ready provisioning can proceed"
    exit 0
else
    echo "##vso[task.setvariable variable=exitMessage;]Error, cluster status is [${CLUSTER_STATUS}]"
    exit 3
fi
