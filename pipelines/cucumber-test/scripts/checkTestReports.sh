#!/usr/bin/env bash

# Check test reports exist
# 1 $(Build.SourcesDirectory)
# 2 $(Agent.TempDirectory)
# 3 ${{parameters.scope}}


nbfolderFound=0
scope=$(echo $1/$3/functional-tests/*-test)
for dir in $scope; do
    if [ -d "$dir" ]; then
        ((nbfolderFound += 1))
    fi
done
if [ "$nbfolderFound" = 0 ]; then
    echo "No report will be produced for this run because caching skipped some tests"
    echo "##vso[task.setvariable variable=runFunctionalTests]false"
else
    find $1/$3/functional-tests/*/test-report/cucumber/*.json
fi

# fi
exit 0
