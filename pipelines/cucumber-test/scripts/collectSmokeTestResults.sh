#!/usr/bin/env bash

# Collect smoke tests results

# Arguments:
# 1 $(Build.SourcesDirectory)
# 2 $(Agent.TempDirectory)

ls

results=$(find ${3}/*/*/test-report/allure-results/*)

for result in $results; do
    cp $result ${2}/allure-results/
done

results=$(find ${3}/*/*/test-report/cucumber/*.json)

resultNo=0
for result in $results; do
    cp $result ${2}/cucumber-json/smoke-test-$resultNo.json
    resultNo=$((resultNo + 1))
done

results=$(find ${3}/*/*/test-report/junit/*.xml)

resultNo=0
for result in $results; do
    cp $result ${2}/cucumber-junit/smoke-test-$resultNo.xml
    resultNo=$((resultNo + 1))
done

pnpm run merge-test-result-files "${2}/cucumber-json"
