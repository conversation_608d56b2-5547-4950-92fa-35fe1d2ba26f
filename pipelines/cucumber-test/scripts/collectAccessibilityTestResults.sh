#!/usr/bin/env bash
# Arguments:
# 1 $(Build.SourcesDirectory)
# 2 $(Agent.TempDirectory)
# 3 ${{parameters.scope}}


scope=$(echo $1/$3/functional-tests/*)
for dir in $scope; do
    if [ -d $dir/test-report/accessibility ]; then
        if [ -n "$(ls -A $dir/test-report/accessibility)" ]; then
            cp $dir/test-report/accessibility/* $2/cucumber-accessibility/
        fi
    fi
done
ls $2/cucumber-accessibility

cd $1 && pnpm run merge-accessibility-result-files $2/cucumber-accessibility
