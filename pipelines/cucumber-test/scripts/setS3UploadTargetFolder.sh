#!/usr/bin/env bash

# Set S3 target folder

# Arguments:
# 1 $(Build.DefinitionName)
# 2 $(Build.BuildId)

# Env:
# SMOKE_TEST_TYPE

if [ "true" = "${IS_PERFORMANCE_TEST}" ]; then
    echo "##vso[task.setvariable variable=s3-upload-target-folder]performance-tests/${1}/${2}/$(date +%Y%m%d_%H%M%S)"
    exit 0
fi

if [ "${SMOKE_TEST_TYPE}" == "static" ] || [ "${SMOKE_TEST_TYPE}" == "all" ]; then
    echo "##vso[task.setvariable variable=s3-upload-target-folder]smoke-tests/${1}/${2}/$(date +%Y%m%d_%H%M%S)"
else
    echo "##vso[task.setvariable variable=s3-upload-target-folder]functional-tests/${1}/${2}/$(date +%Y%m%d_%H%M%S)"
fi
