#!/usr/bin/env bash

# Dummy test function for xtreem release test
# Env vars
# ACCESS_TOKEN
# CLUSTER
# CLUSTER_URL
# TENANT_ID
# TENANT_NAME
# RUN_SMOKE_TEST_STATIC_ONLY
# argument
# 1 - test result (failure / success) for test purpose, to be removed with real cucumber test

echo "Start dummy test function"
echo "Cluster url : ${CLUSTER_URL}"
echo "Tenant id : ${TENANT_ID}"
echo "Tenant name : ${TENANT_NAME}"
echo "Only static smke test :${RUN_SMOKE_TEST_STATIC_ONLY}"

sleep 5

if [ "${1}" = "failure" ]; then
  echo "Dummy test function failure"
  exit 1
fi
echo "Dummy test function success"
