#!/usr/bin/env bash
#
# Note: this script does not use the cache. It just iterates through the packages, using the convention
# that functional test packages end with -test.

pwd=`pwd`
scope=`echo $pwd/*/*-test`

failed=0
for dir in $scope; do
  if [ -f $dir/package.json ]; then
    cd $dir && pnpm run test:functional:ci;
    if [ $? -ne 0 ]; then failed=$((failed+1)); fi
  else
    echo "$dir is empty you should delete it"
    exit 1
  fi
done

if [ $failed -ne 0 ]; then
    echo "Tests FAILED in $failed packages !!!"
    exit 1
fi
