#!/usr/bin/env bash
# Arguments:
# 1 $(Build.SourcesDirectory)
# 2 $(Agent.TempDirectory)
# 3 ${{parameters.scope}}


scope=$(echo $1/$3/functional-tests/*)

for dir in $scope; do
    if [ -d $dir/test-report/cucumber-progress-results ]; then
        if [ -n "$(ls -A $dir/test-report/cucumber-progress-results)" ]; then
            cp $dir/test-report/cucumber-progress-results/* $2/cucumber-progress-results/cucumber-progress-results-$(date +%s%N).json
        fi
    fi
done
ls $2/cucumber-progress-results

cd $1 && pnpm run merge-progress-result-files $2/cucumber-progress-results
