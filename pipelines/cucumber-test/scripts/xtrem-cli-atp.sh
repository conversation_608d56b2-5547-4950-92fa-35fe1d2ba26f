#!/usr/bin/env bash


# shellcheck disable=SC2001
# shellcheck disable=SC2046
# shellcheck disable=SC2005
# shellcheck disable=SC2154
# shellcheck disable=SC2086


XTREM_ROOT=${XTREM_ROOT:="$BUILD_SOURCESDIRECTORY"}
XTREM_ROOT=${XTREM_ROOT:="$(git rev-parse --show-toplevel)"}
if [[ "${BUILD_SOURCEBRANCHNAME}" == "master" ]]; then
    major="latest";
else
    major=$(jq -r '.version|split(".")[0]' package.json)
fi
XTREM_CLI_TAG=${XTREM_CLI_TAG:="$major"}
TEST_WORKING_DIR=$(echo $(pwd) | sed 's|'$XTREM_ROOT'|/xtrem/test|g')
TMP_DIR=${TMP_DIR:="$AGENT_TEMPDIRECTORY"}
TMP_DIR=${TMP_DIR:="$(dirname "$(mktemp -u)")"}

echo -e "\ncurrent dir is $(pwd)\n"

echo -e ">>> running env\n"
echo "      XTREM_ROOT: $XTREM_ROOT"
echo "   XTREM_CLI_TAG: $XTREM_CLI_TAG"
echo "TEST_WORKING_DIR: $TEST_WORKING_DIR"
echo "         TMP_DIR: $TMP_DIR"

echo -e "\n>>> docker env var\n"
echo "TARGET_URL=${TARGET_URL}"
echo "tenantName=${tenantName}"
echo "tenantApp=${tenantApp}"
echo "tenantAppName=${tenantAppName}"
echo "endPointName1=${endPointName1}"
echo "SCOPE=${SCOPE}"
echo "XTREM_TEST_MAX_INSTANCES=${XTREM_TEST_MAX_INSTANCES}"
echo "CUCUMBER_TAGS=${CUCUMBER_TAGS}"
echo "testUser=${testUser}"
echo "timeout=${timeout}"
echo "timeoutCucumber=${timeoutCucumber}"
echo "timeoutWaitFor=${timeoutWaitFor}"
echo "timeoutWaitForLoading=${timeoutWaitForLoading}"

echo "timeoutLocks=${timeoutLocks}"
echo "stopTestOnError=${stopTestOnError}"
echo "toastFallback=${toastFallback}"
echo "downloadFolder=${downloadFolder}"

echo -e "\n>>> pulling image ghcr.io/sage-erp-x3/xtrem-cli-dev:$XTREM_CLI_TAG\n"

if ! docker pull ghcr.io/sage-erp-x3/xtrem-cli-dev:"$XTREM_CLI_TAG"; then
    echo "WARN: tag $XTREM_CLI_TAG does not exist, use 'latest' instead"
    XTREM_CLI_TAG="latest"
fi

# run xtrem cli docker container
#   -w $TEST_WORKING_DIR: change the current dir inside the docker container to /xtrem/test/<package-dir>
#   -v ...: map the volumes
#   -e ...: set all expected variables
#   -u ...: map user and group id to prevent from write permission issue in the container
docker run --rm --name xtrem-cli-atp \
    -w "$TEST_WORKING_DIR" \
    -v "$XTREM_ROOT":/xtrem/test \
    -v "$XTREM_ROOT":/xtrem/config \
    -v "$TMP_DIR":/tmp \
    -u "$(id -u)":"$(id -g)" \
    -e TARGET_URL="$TARGET_URL" \
    -e tenantName="$tenantName" \
    -e tenantApp="$tenantApp" \
    -e tenantAppName="$tenantAppName" \
    -e endPointName1="$endPointName1" \
    -e SCOPE="$SCOPE" \
    -e XTREM_TEST_MAX_INSTANCES="$XTREM_TEST_MAX_INSTANCES" \
    -e CUCUMBER_TAGS="$CUCUMBER_TAGS" \
    -e testUser="$testUser" \
    -e timeout="$timeout" \
    -e timeoutLocks="$timeoutLocks" \
    -e timeoutCucumber="$timeoutCucumber" \
    -e timeoutWaitFor="$timeoutWaitFor" \
    -e timeoutWaitForLoading ="$timeoutWaitForLoading" \
    -e TZ="CET" \
    -e loginUserName="$loginUserName" \
    -e loginPassword="$loginPassword" \
    -e loginUserName2="$loginUserName2" \
    -e loginPassword2="$loginPassword2" \
    -e loginUserName3="$loginUserName3" \
    -e loginPassword3="$loginPassword3" \
    -e stopTestOnError="$stopTestOnError" \
    -e toastFallback="$toastFallback" \
    -e downloadFolder="$downloadFolder" \

    ghcr.io/sage-erp-x3/xtrem-cli-dev:"$XTREM_CLI_TAG" xtrem "$@"
