name: xtrem-cli-dev-image

# disable PR and branch triggering
pr: none
trigger: none

# Build is no longer scheduled but it is now triggered by a dependency on xtrem-services-patch-release build
# see https://docs.microsoft.com/en-us/azure/devops/pipelines/process/pipeline-triggers?view=azure-devops&tabs=yaml
resources:
  pipelines:
    - pipeline: xtremPatchRelease # Name of the pipeline resource
      source: xtrem-patch-release # Name of the pipeline referenced by the pipeline resource
      trigger:
        branches:
          - master

parameters:
  - name: dryRun
    type: boolean
    default: false
  - name: skipTests
    type: boolean
    default: false

jobs:
  - template: shared/image-build-job.yml
    parameters:
      imageName: 'xtrem-cli-dev'
      dockerFolder: 'docker/cli-dev-image'
      skipAwsPush: true
      dryRun: ${{ parameters.dryRun }}
      skipTests: ${{ parameters.skipTests }}
      notifyReleaseDeliveryChannel: false
      isDevImage: true
