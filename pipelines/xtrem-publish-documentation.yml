name: xtrem-publish-documentation

pr: none
trigger: none

# Build is no longer scheduled but it is now triggered by a dependency on xtrem-platform-patch-release build
# see https://docs.microsoft.com/en-us/azure/devops/pipelines/process/pipeline-triggers?view=azure-devops&tabs=yaml
resources:
  pipelines:
    - pipeline: xtremPlatformPatchRelease # Name of the pipeline resource
      source: xtrem-patch-release # Name of the pipeline referenced by the pipeline resource
      trigger:
        branches:
          - master
pool:
  vmImage: 'ubuntu-latest'

parameters:
  - name: runPlatform
    displayName: 'Update platform doc'
    type: boolean
    default: true
  - name: runServices
    displayName: 'Update services doc'
    type: boolean
    default: true
  - name: useNpmProxy
    displayName: 'Use nexus npm proxy'
    default: false
    type: boolean

variables:
  - group: sagex3ci_github
  - group: CONFLUENCE_DOCUMENTATION_ACCESS_TOKEN
  - name: xtremBranch
    value: 'master'
stages:
  - stage: Release
    jobs:
      - job: Documents
        timeoutInMinutes: 90
        steps:
          - checkout: none
          - template: ./shared/manual-git-clone.yml
            parameters:
              depth: 1

          - template: ./shared/install-node.yml

          - template: ./shared/npm-configure.yml
            parameters:
              useNpmProxy: ${{ parameters.useNpmProxy }}

          - script: |
              git config --global user.name 'Sage X3 CI'
              git config --global user.email '<EMAIL>'
            displayName: 'Git - Configuration'

          - bash: |
              corepack enable
              corepack prepare "$(jq -r '.packageManager' package.json | cut -d'+' -f1)" --activate
              corepack install
              cd scripts/documentation
              pnpm install
            displayName: 'PNPM - Install dependencies'

          - bash: |
              scripts/documentation/documentation.sh ${CONFLUENCE_PAT}
            displayName: 'DOC - Update platform Confluence documentation'
            condition: and(succeeded(), eq(${{ parameters.runPlatform }}, true))
            env:
              CONFLUENCE_PAT: $(CONFLUENCE_DOCUMENTATION_ACCESS_TOKEN)
            continueOnError: true

          - bash: |
              scripts/documentation/documentation.sh ${CONFLUENCE_PAT} documentation/services/
            displayName: 'DOC - Update services Confluence documentation'
            condition: and(succeededOrFailed(), eq(${{ parameters.runServices }}, true))
            env:
              CONFLUENCE_PAT: $(CONFLUENCE_DOCUMENTATION_ACCESS_TOKEN)
            continueOnError: true
