#!/usr/bin/env bash

# This script is checking that the specific 'queue' decorator we can set on an asyncMutation is taken into account
# It also tests the --queue option we can set on the start command
# It will :
#   - start a show-case app with a specific queue enabled
#   - make sure an async mutation bound to this specific queue can be invoked
#   - re-start a show-case app WITHOU a specific queue enabled
#   - make sure the async mutation bound to this specific queue CANNOT be invoked

function banner() {
    echo "*******************************************"
    echo "**"
    echo "**"
    echo "**  $1"
    echo "**"
    echo "**"
    echo "*******************************************"
}

set -e

cd ../../platform/show-case/xtrem-show-case

banner "Init application (specificQueue active)"

echo "##[group]Init application (specificQueue active) - reset database"
echo "##[command]pnpm run xtrem schema --create --reset-database"
pnpm run xtrem schema --create --reset-database
echo "##[endgroup]"

echo "##[group]Init application (specificQueue active) - load:test:data"
echo "##[command]pnpm run load:test:data"
pnpm run load:test:data
echo "##[endgroup]"

echo "##[group]Init application (specificQueue active) - reset SQS"
echo "##[command]pnpm -sw run sqs:reset"
pnpm -sw run sqs:reset
echo "##[endgroup]"

echo "##[group]Start application (specificQueue active)"
echo "##[command]pnpm run xtrem start --queues=import-export,showcase-test-specific &"
# Start a show-case application (in background)
# We are only starting the 'showcase-test-specific' queue (and the import-export queue used by the processExit mutation)
banner "Start application (specificQueue active)"
pnpm run xtrem start --queues=import-export,showcase-test-specific &
# give a few seconds to the application to start
sleep 10
echo "##[endgroup]"

echo "##[group]Invoke async mutation (specificQueue active)"
# Try to invoke an async mutation
banner "Invoke async mutation (specificQueue active)"
RESULT_MUTATION1=$(../../../pipelines/shared/scripts/run-graphql-async-mutation.sh xtremShowCase testAsyncMutationOnSpecificQueue asyncMutationOnSpecificQueue "val: 5")
if [ "$RESULT_MUTATION1" != "{\"status\":\"success\",\"result\":10}" ]; then
    echo "Could not invoke the async mutation (with no --queues)"
    exit 1
fi
echo "##[endgroup]"

echo "##[group]Kill the application (specificQueue active)"
# Try to kill the show-case application (using an other async mutation)
RESULT_EXIT1=$(../../../pipelines/shared/scripts/run-graphql-async-mutation.sh xtremShowCase testAsyncMutationOnSpecificQueue asyncMutationToProcessExit)
if [ "$RESULT_EXIT1" != "{\"status\":\"success\",\"result\":\"done\"}" ]; then
    echo "Could not kill the application (1)"
    exit 1
fi
echo "##[endgroup]"

echo "##[group]Start application (specificQueue disabled)"
echo "##[command]pnpm run xtrem start --queues=import-export &"
# Start a show-case application (in background)
# this time, we only start the 'import-export' queue, so the 'showcase-test-specific' queue will not be started
banner "Start application (specificQueue disabled)"
# note the 'import-export' queue is used by the processExit mutation
pnpm run xtrem start --queues=import-export &
# give a few seconds to the application to start
sleep 10
echo "##[endgroup]"

# Try to invoke an async mutation - it should fail as its queue was not started
echo "##[group]Invoke async mutation (specificQueue disabled)"
banner "Invoke async mutation (specificQueue disabled)"
RESULT_MUTATION2=$(../../../pipelines/shared/scripts/run-graphql-async-mutation.sh xtremShowCase testAsyncMutationOnSpecificQueue asyncMutationOnSpecificQueue "val: 5")
if [ "$RESULT_MUTATION2" != "{\"status\":\"error\",\"result\":null}" ]; then
    echo "The async mutation could be invoked while its specific queue was supposed to be disabled"
    exit 1
fi
echo "##[endgroup]"

echo "##[group]Kill the application (specificQueue disabled)"
# Try to kill the show-case application (using an other async mutation)
RESULT_EXIT2=$(../../../pipelines/shared/scripts/run-graphql-async-mutation.sh xtremShowCase testAsyncMutationOnSpecificQueue asyncMutationToProcessExit)
if [ "$RESULT_EXIT2" != "{\"status\":\"success\",\"result\":\"done\"}" ]; then
    echo "Could not kill the application (2)"
    exit 1
fi
echo "##[endgroup]"
