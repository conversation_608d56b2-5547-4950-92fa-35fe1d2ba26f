name: xtrem-test-image

pr: none
trigger: none

# use ubuntu-latest even if it runs with low memory warnings
# because X3-ubuntu has an old version of docker that doesn't support multi-stage build
pool:
  vmImage: 'ubuntu-latest'

parameters:
  - name: targetApp
    displayName: Target application
    type: string
    default: 'services'
    values:
      - 'platform'
      - 'services'
      - 'shopfloor'
      - 'tools'
      - 'x3-services'
      - 'wh-services'
  - name: imageTag
    displayName: Image Tag (Dev option)
    type: string
    default: 'latest'
  - name: imageType
    displayName: Image to build (Dev option)
    type: string
    default: 'branch'
    values:
      - 'branch'
      - 'system-dependencies'
  - name: pushToAWSDevRegistry
    displayName: Push image to AWS dev registry
    type: boolean
    default: false

variables:
  - group: sagex3ci_github
  - group: sagex3ci_test_image
  - group: dockerXtrem
  - name: awsCredentials
    value: 'xtrem dev-eu'

stages:
  - stage: GatherInformation
    jobs:
      - job: retrieveExternalInfo
        displayName: 'Get Build information'
        steps:
          - checkout: none
          - script: |
              BRANCH_NAME=`echo $(Build.SourceBranch) | sed 's@refs/heads/@@'`
              echo "branchName: $BRANCH_NAME"
              echo "##vso[task.setvariable variable=branchName;]$BRANCH_NAME"
            name: IdentifyBranchName
          - script: |
              TICKET_NB=$( echo "$(branchName)" | grep -oEi '(X3-[0-9]+|XT-[0-9]+|X3TRADOC-[0-9]+)' | head -1 | tr 'a-z' 'A-Z' )
              echo "ticketNb: $TICKET_NB"
              echo "##vso[task.setvariable variable=ticketNumber;]$TICKET_NB"
            name: IdentifyTicketNumber
            condition: eq('${{ parameters.imageType }}', 'branch')
          - script: |
              curl --location --request GET "https://api.github.com/repos/Sage-ERP-X3/xtrem/pulls?state=open&head=Sage-ERP-X3:$(branchName)&per_page=1&page=1&sort=created&direction=desc" \
                --header 'Accept: application/vnd.github.v3+json' \
                -u $(github_username):$(github_password) \
                -o pr-search-result.json
              PR_NUMBER=$(cat pr-search-result.json | jq -r '.[0].number')
              if [ "$PR_NUMBER" != "null" ]; then
                echo "##vso[task.setvariable variable=prNumber;]$PR_NUMBER"
              else
                echo "##vso[task.setvariable variable=prNumber;]"
              fi;
            name: findPRAssociated
            condition: ne('$(branchName)', 'master')
          - script: |
              echo "ticketNb: $(ticketNumber)"
              echo "branchName: $(branchName)"
              echo "pnNb: $(prNumber)"
            name: RetrievedInformation
          - bash: |
              echo "##vso[task.setvariable variable=ticketNb;isOutput=true]$(ticketNumber)"
              echo "##vso[task.setvariable variable=branch;isOutput=true]$(branchName)"
              echo "##vso[task.setvariable variable=prNb;isOutput=true]$(prNumber)"
            name: outputVar

  - stage: BuildAndNotify
    jobs:
      - job: Build
        timeoutInMinutes: 120
        variables:
          - name: ticketNumber
            value: $[ stageDependencies.GatherInformation.retrieveExternalInfo.outputs['outputVar.ticketNb'] ]
          - name: branchName
            value: $[ stageDependencies.GatherInformation.retrieveExternalInfo.outputs['outputVar.branch'] ]
        displayName: 'Build branch Image'
        steps:
          - checkout: none

          - bash: |
              if [ "$(ticketNumber)" = "" ]; then
                echo "You must set an imageTag parameter for this pipeline which is different from 'latest'"
                echo "if your branch doesn't contain a jira ticket in the XT/X3/X3TRADOC-###### format"
                exit 1
              fi;
              echo $(ticketNumber)
            name: CheckIfTicketNbIsAvailable
            condition: and(eq('${{ parameters.imageTag }}', 'latest'), eq('${{ parameters.imageType }}', 'branch'))

          - bash: |
              BASE_URL=https://raw.githubusercontent.com/Sage-ERP-X3/xtrem/{branch}
              BRANCH_DOWNLOAD_URL=`echo $BASE_URL | sed 's@{branch}@'"$(branchName)"'@'`
              echo Downloading source code from $BRANCH_DOWNLOAD_URL
              echo "##vso[task.setvariable variable=BranchDownloadURL;]$BRANCH_DOWNLOAD_URL"
              GH_CRED=$(github_username):$(github_password)
              curl -u ${GH_CRED} ${BRANCH_DOWNLOAD_URL}/package.json -o package.json
              curl -u ${GH_CRED} ${BRANCH_DOWNLOAD_URL}/.nvmrc -o .nvmrc
              curl -u ${GH_CRED} ${BRANCH_DOWNLOAD_URL}/docker/test-image/Dockerfile -o Dockerfile
              curl -u ${GH_CRED} ${BRANCH_DOWNLOAD_URL}/docker/test-image/build-test-image.sh -o build-test-image.sh
              chmod u+x build-test-image.sh
              curl -u ${GH_CRED} ${BRANCH_DOWNLOAD_URL}/docker/test-image/manage-config-template.sh -o manage-config-template.sh
              chmod u+x manage-config-template.sh
              curl -u ${GH_CRED} ${BRANCH_DOWNLOAD_URL}/docker/test-image/waitlive.sh -o waitlive.sh
            displayName: Download required files

          - task: Docker@2
            displayName: Login to repository
            inputs:
              command: login
              containerRegistry: ghcr

          - bash: |
              SECONDARY_TAG="${{ parameters.imageTag }}"
              if [ "${{ parameters.imageTag }}" = "latest" ]; then
                SECONDARY_TAG=""
                if [ "$(branchName)" = "master" ]; then
                  VERSION_NUMBER=`cat package.json | grep "version" | sed 's/\(.*\)\([1-9][0-9]\{0,\}\.[0-9]\{1,\}\.[0-9]\{1,\}\)\(",\)/\2/'`
                  SECONDARY_TAG="$VERSION_NUMBER-${{ parameters.imageType }}"
                fi
              fi
              echo "##vso[task.setvariable variable=secondaryTag;]$SECONDARY_TAG"
            displayName: Manage secondary Image tag

          - bash: |
              AWS_ECR_TAG="${{ parameters.imageTag }}"
              if [ "${{ parameters.imageTag }}" = "latest" ]; then
                AWS_ECR_TAG=$(ticketNumber)
              fi
              echo $AWS_ECR_TAG
              echo "##vso[task.setvariable variable=awsEcrTag;]$AWS_ECR_TAG"
            displayName: Manage AWS ECR Image tag
            condition: and(eq(${{ parameters.pushToAWSDevRegistry }}, true), eq('${{ parameters.imageType }}', 'branch'))

          # env file required for building the image in the next step
          - task: DownloadSecureFile@1
            displayName: 'Get docker env for build'
            name: 'dockerBuildEnv'
            inputs:
              secureFile: '.env-xtrem-docker-build'

          - bash: |
              # amend secure file
              echo 'AZURE_DEVOPS_TOKEN='$AZURE_DEVOPS_TOKEN'' >> ${DOCKER_ENV_SOURCE}
            displayName: 'Add Azure DevOps token to docker env file'
            env:
              DOCKER_ENV_SOURCE: $(dockerBuildEnv.secureFilePath)
              AZURE_DEVOPS_TOKEN: $(System.AccessToken)

              # Build and push the multi-stage image
          - bash: |
              # amend secure file
              echo 'GIT_USERNAME="'$(github_username)'"' >> ${DOCKER_ENV_SOURCE}
              echo 'GIT_PASSWORD="'$(github_password)'"' >> ${DOCKER_ENV_SOURCE}
              echo 'export AWS_REGION="eu-west-1"' >> ${DOCKER_ENV_SOURCE}
              echo 'export AWS_ACCESS_KEY_ID="'$(xtrem_cache_read_only_key)'"' >> ${DOCKER_ENV_SOURCE}
              echo 'export AWS_SECRET_ACCESS_KEY="'$(xtrem_cache_read_only_secret)'"' >> ${DOCKER_ENV_SOURCE}
              NODE_VERSION=$(cat .nvmrc) ./build-test-image.sh
            env:
              REPO_ROOT_PATH: $(Build.SourcesDirectory)
              DOCKER_ENV_SOURCE: $(dockerBuildEnv.secureFilePath)
              IMAGE_NAME: 'ghcr.io/sage-erp-x3/xtrem-test-image-azure'
              TARGET_APP: '${{ parameters.targetApp }}'
              GIT_FEATURE_BRANCH: $(branchName)
              JIRA_TICKET: $(ticketNumber)
              SECONDARY_TAG: $(secondaryTag)
              DOCKER_BUILD_TARGET: '${{ parameters.imageType }}'
            displayName: 'Build image ${{ parameters.imageType }}'

          - bash: |
              rm -f .env
            condition: always()
            displayName: 'Clean env'

          - task: Docker@2
            displayName: Logout from repository
            inputs:
              command: logout
              containerRegistry: ghcr

          - task: ECRPushImage@1
            displayName: Push image to AWS ECR repository
            condition: and(succeeded(), and(eq(${{ parameters.pushToAWSDevRegistry }}, true), eq('${{ parameters.imageType }}', 'branch')))
            inputs:
              awsCredentials: $(awsCredentials)
              regionName: 'eu-west-1'
              sourceImageName: 'ghcr.io/sage-erp-x3/xtrem-test-image-azure'
              sourceImageTag: $(ticketNumber)
              repositoryName: 'xtrem'
              pushTag: $(awsEcrTag)

      - job: Workflows
        variables:
          - name: ticketNumber
            value: $[ stageDependencies.GatherInformation.retrieveExternalInfo.outputs['outputVar.ticketNb'] ]
          - name: prNumber
            value: $[ stageDependencies.GatherInformation.retrieveExternalInfo.outputs['outputVar.prNb'] ]
        displayName: 'Comment on the linked platforms'
        dependsOn:
          - Build
        condition: and(succeeded('Build'), eq('${{ parameters.imageType }}', 'branch'), variables.ticketNumber)
        steps:
          - checkout: none
          - bash: |
              curl --request POST \
              '$(jira_endpoint)/issue/$(ticketNumber)/comment' \
              --header "Content-Type: application/json" \
              --header "Authorization: Bearer $(sagex3ci_jira_token)" \
              --data-raw '{ "body": "This story can be tested using this command: \n\n XTREM_TESTER_EMAIL=\"<your-email>\" XTREM_TARGET_APP=\"${{ parameters.targetApp }}\" XTREM_RESET_DATA=\"true\" ./run-test-image.sh $(ticketNumber) \n\n making sure you replace this label *<your-email>* by your actual sage-id e-mail address" }'
            name: JIRAComment
            continueOnError: true # if the Jira rest call fail (user is locked, connection issue, ...) the build shouldn't fail
            displayName: 'Push comment on JIRA'
            condition: variables.ticketNumber
          - bash: |
              curl --location --request POST "https://api.github.com/repos/Sage-ERP-X3/xtrem/issues/$(prNumber)/comments" \
                --header 'Accept: application/vnd.github.v3+json' \
                -u $(github_username):$(github_password) \
                --data-raw '{ "body": "This story can be tested using this command: \n `XTREM_TESTER_EMAIL=\"<your-email>\" XTREM_TARGET_APP=\"${{ parameters.targetApp }}\" XTREM_RESET_DATA=\"true\" ./run-test-image.sh $(ticketNumber)` \n making sure you replace this label `<your-email>` by your actual sage-id e-mail address" }'
            name: GitHubComment
            displayName: 'Push comment on GitHub'
            condition: and(variables.ticketNumber, variables.prNumber)
