# Display name
name: xtrem-update-visual-snapshots

resources:
  repositories:
    - repository: huracan
      type: github
      endpoint: Sage-ERP-X3
      name: Sage-ERP-X3/huracan
      # branch name to use
      ref: v4.2

# disable branch triggering
pr: none
trigger: none
pool: 'x3-ubuntu'

variables:
  - group: sagex3ci_github
  - group: dockerXtrem
  - name: xtremBranch
  - group: AWS_Mindo_QA_ADC_UT
  - name: SG_NAME
    value: ADC-UNIT-TEST
  - name: INSTANCE_NAME
    value: ADC-SQL-UNIT-TEST
  - name: SQLUSER
    value: READONLYUT
  - name: X3_FOLDER
    value: X3RBTREF
  - name: runIntegrationTests
    value: 'true'

steps:
  - checkout: none
    persistCredentials: true
  - template: pipelines/templates/misc/setup-bash-env.yml@huracan
  - script: |
      git config --global user.name 'Sage X3 CI'
      git config --global user.email '<EMAIL>'
    displayName: 'Git - Configuration'
  - template: pipelines/templates/misc/branch-name.yml@huracan
    parameters:
      allowAnyBranch: true
  - template: pipelines/templates/git/manual-git-clone.yml@huracan
    parameters:
      depth: 1
  - template: pipelines/templates/install.yml@huracan

  - script: pnpm run build:binary
    displayName: 'Build (binary)'
    env:
      XTREM_CI: 1
      XTREM_SCOPES: 'platform'

  - template: pipelines/templates/tests/integration-tests.yml@huracan
    parameters:
      updateSnapshots: 'true'
      XTREM_TEST_MAX_INSTANCES: 2
      PATTERN: 'test/cucumber/z-visual-regression-[0-9].feature'
      XTREM_SCOPES: 'platform/show-case/xtrem-show-case$'
      applicationRootFolder: 'platform/show-case/xtrem-show-case'
      doNotUseS3Cache: 'true'

  - script: |
      git add platform/show-case/xtrem-show-case/test/cucumber/snapshots
      git commit -m "test: updated visual snapshots in showcase"
      git push
    displayName: 'Git - Pushing new snapshots'
