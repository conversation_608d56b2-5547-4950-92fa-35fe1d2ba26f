# Display name
name: xtrem-release-branch

# disable PR and branch triggering
pr: none
trigger: none

parameters:
  - name: confirmation
    displayName: 'Confirm: I understand that this pipeline will create a lot of things that CANNOT be reverted'
    type: boolean
    default: false
  - name: dryRun
    displayName: 'DRY RUN: no publish, no push'
    type: boolean
    default: false

jobs:
  # - job: main_release_job
  #   steps:
  #   - bash: |
  #       echo "##vso[task.logissue type=error]'Confirmation' was not checked"
  #       exit 1
  #     condition: not(${{ parameters.confirmation }})
  #     displayName: 'Check confirmation'

  #   - ${{ if eq(parameters['confirmation'], true) }}:
  - template: ../shared/release-job-template.yml
    parameters:
      bumpKind: major
      dryRun: ${{ parameters.dryRun }}
      skipUpgrade: true
      pipelineId: 1385
      pipelineName: 'xtrem-release-branch'
