name: '$(Date:yyyyMMdd)$(Rev:.r)'

pr: none
trigger: none

resources:
  repositories:
    - repository: huracan
      type: github
      endpoint: Sage-ERP-X3
      name: Sage-ERP-X3/huracan
      # use stable branch v2.0 for huracan
      ref: v2.0

parameters:
  - name: dryRun
    type: boolean
    default: false
    displayName: 'Dry run'

pool: 'x3-ubuntu'

schedules:
  - cron: '0 16 * * 1-5'
    displayName: Checking for new incoming translations
    branches:
      include:
        - master

variables:
  - group: dockerXtrem
  - group: sagex3ci_github

steps:
  - checkout: self
    persistCredentials: true
    workspaceRepo: true
    path: xtrem
    fetchDepth: 1
    fetchTags: false
    submodules: false
  - checkout: huracan
    path: huracan
    fetchDepth: 1
    fetchTags: false
    submodules: false

  - template: pipelines/templates/install.yml@huracan

  - task: AmazonWebServices.aws-vsts-tools.S3Download.S3Download@1
    displayName: Downloading translations
    inputs:
      awsCredentials: 'aws-s3'
      regionName: 'eu-west-1'
      bucketName: 'xtrem-developers-utility'
      sourceFolder: 'translations'
      globExpressions: '*.json'
      logRequest: true
      logResponse: true
      targetFolder: '$(Build.Repository.LocalPath)'
  - script: pnpm run translation:import
    displayName: 'Importing new translations'

  - script: XTREM_CI=1 pnpm run build
    displayName: 'Build project'
    env:
      XTREM_SKIP_CHANGED_CHECK: '1'

  - bash: |
      if [ -z "$(git status --porcelain)" ]; then
        # Working directory clean
        echo "No translations to import."
      else
        git config user.name "Sage Azure CI"
        git config user.email "<EMAIL>"
        git checkout -b translation-import-$(Build.BuildNumber)
        git diff --name-only | grep "\.json$" | xargs -r pnpm run prettier --write
        git add platform services tools x3-services wh-services shopfloor x3-connector
        git commit -m "chore: updated translation files $(Build.BuildNumber)"
        if [[ ${DRY_RUN,,} == "true" ]]; then
            echo "DRY_RUN is set to true. Not pushing changes."
            exit 0
        fi
        git push origin translation-import-$(Build.BuildNumber)
        prHead="translation-import-$(Build.BuildNumber)"
        prTitle="chore: updated translation files $(Build.BuildNumber)"
        prBody="Automatically generated pull request to import translations"
        node scripts/create-pr.js Sage-ERP-X3/xtrem "$prHead" "$prTitle" "$prBody" $(github_username) $(github_password)
      fi
    displayName: 'Committing and pushing changes'
    env:
      DRY_RUN: ${{ parameters.dryRun }}
