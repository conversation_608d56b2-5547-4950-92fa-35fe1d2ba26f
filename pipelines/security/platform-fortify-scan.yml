name: platform-fortify-scan
# disable PR and branch triggering
pr: none
trigger: none

schedules:
  # every saturday at 02:00 UTC time
  - cron: '0 2 * * 6'
    displayName: Weekly platform fortify scan
    branches:
      include:
        - master

pool:
  vmImage: 'windows-latest'

variables:
  - name: folderToScan
    value: "$(Build.SourcesDirectory)\\platform"
  - name: releaseId
    value: '70233'

steps:
  - task: fortifyvsts.hpe-security-fortify-vsts.build-task-fortify-on-demand-static.FortifyOnDemandStatic@6
    displayName: 'Run Fortify on Demand static assessment on $(folderToScan)'
    inputs:
      FortifyProjects: '$(folderToScan)'
      ReleaseId: $(releaseId)
      FodConnection: 'Fortify on Demand - MV'
      EntitlementPreference: 1
      InProgressScanActionType: 2
      RemediationScanPreference: 2
      PolicyFailAction: 0
