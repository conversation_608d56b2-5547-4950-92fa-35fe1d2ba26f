# auto fix some common false positive findings
name: threadfix-autofix
# disable PR and branch triggering
pr: none
trigger: none

schedules:
  # every monday at 02:30 UTC time
  - cron: '30 2 * * 1'
    displayName: Weekly threadFix autofix
    branches:
      include:
        - master

pool: 'x3-ubuntu'

variables:
  # To get TF3_API_URL and TF3_API_KEY variables
  - group: dockerXtrem

steps:
  # we don't need a any source code
  - checkout: none

  # Configure npm to be able to run the pnpm dlx commands below
  - template: ../shared/npm-configure.yml

  - bash: |
      pnpm dlx @sage/mxx tf vuln xtrem_platform --fix
    displayName: 'xtrem platform auto-fixing'
    env:
      MXX_TF_API_URL: $(TF3_API_URL)
      MXX_TF_API_KEY: $(TF3_API_KEY)

  - bash: |
      pnpm dlx @sage/mxx tf vuln xtrem_services --fix
    displayName: 'xtrem services auto-fixing'
    env:
      MXX_TF_API_URL: $(TF3_API_URL)
      MXX_TF_API_KEY: $(TF3_API_KEY)

  - bash: |
      pnpm dlx @sage/mxx tf vuln xtrem_x3 --fix
    displayName: 'xtrem x3 auto-fixing'
    env:
      MXX_TF_API_URL: $(TF3_API_URL)
      MXX_TF_API_KEY: $(TF3_API_KEY)

  - bash: |
      pnpm dlx @sage/mxx tf vuln xtrem_tools --fix
    displayName: 'xtrem tools auto-fixing'
    env:
      MXX_TF_API_URL: $(TF3_API_URL)
      MXX_TF_API_KEY: $(TF3_API_KEY)
