name: tools-fortify-scan
# disable PR and branch triggering
pr: none
trigger: none

schedules:
  # every saturday at 02:30 UTC time
  - cron: '30 3 * * 6'
    displayName: Weekly services fortify scan
    branches:
      include:
        - master

pool:
  vmImage: 'windows-latest'

variables:
  - name: folderToScan
    value: "$(Build.SourcesDirectory)\\tools"
  - name: releaseId
    value: '60825'

steps:
  - task: fortifyvsts.hpe-security-fortify-vsts.build-task-fortify-on-demand-static.FortifyOnDemandStatic@6
    displayName: 'Run Fortify on Demand static assessment on $(folderToScan)'
    inputs:
      FortifyProjects: '$(folderToScan)'
      ReleaseId: $(releaseId)
      FodConnection: 'Fortify on Demand - MV'
      EntitlementPreference: 1
      InProgressScanActionType: 2
      RemediationScanPreference: 2
      PolicyFailAction: 0
