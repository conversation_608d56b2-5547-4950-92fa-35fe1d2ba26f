# Display name
name: update-anon-partial-backup

resources:
  repositories:
    - repository: huracan
      type: github
      endpoint: Sage-ERP-X3
      name: Sage-ERP-X3/huracan
      # branch name to use
      ref: v4.2

# disable branch triggering
pr: none
trigger: none
pool: 'x3-ubuntu'

schedules:
  - cron: '0 2 * * *'
    displayName: Partial backup sync
    branches:
      include:
        - master

parameters:
  - name: dryRun
    type: boolean
    default: false

variables:
  - group: sagex3ci_github
  - group: dockerXtrem

jobs:
  - job: update_anon_partial_backup
    displayName: Update anon partial backup
    timeoutInMinutes: 60
    steps:
      - template: pipelines/templates/misc/job-cleanup.yml@huracan

      - checkout: none
        persistCredentials: true
      - template: pipelines/templates/misc/setup-bash-env.yml@huracan

      - template: pipelines/templates/misc/branch-name.yml@huracan
        parameters:
          allowAnyBranch: true
      - template: pipelines/templates/git/manual-git-clone.yml@huracan
        parameters:
          depth: 1
      - template: pipelines/templates/install.yml@huracan

      - bash: |
          pnpm run build:services
        displayName: 'Build (services)'
        env:
          XTREM_CI: 1
          XTREM_SCOPES: 'services'

      - bash: |
          pnpm -sw xdev releng replace-config --source xtrem-config-azure.yml --target xtrem-config.yml
          xdev run postgres/deploy-postgres.sh --skip-client-check
        displayName: 'Deploy Postgres'

      - bash: |
          scripts/anon-backup/update-partial-backup.sh $TMP_DIR $DRY_RUN
        displayName: 'Update partial backup'
        env:
          AWS_ACCESS_KEY_ID: $(s3-xtrem-developers-utility-access-key)
          AWS_SECRET_ACCESS_KEY: $(s3-xtrem-developers-utility-secret)
          AWS_REGION: 'eu-west-1'
          TMP_DIR: $(Agent.TempDirectory)/partial-backup
          DRY_RUN: ${{ parameters.dryRun }}

      - template: pipelines/templates/misc/job-cleanup.yml@huracan
