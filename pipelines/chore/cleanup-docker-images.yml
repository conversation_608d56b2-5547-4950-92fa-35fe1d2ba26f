# auto fix some common false positive findings
name: cleanup-docker-images
# disable PR and branch triggering
pr: none
trigger: none

schedules:
  # every monday at 02:30 UTC time
  - cron: '00 3 * * 1'
    displayName: Weekly docker images cleanup
    branches:
      include:
        - master

pool:
  vmImage: 'ubuntu-latest'

parameters:
  - name: dryRun
    type: boolean
    default: false
  - name: images
    type: object
    default:
      - name: 'xtrem-test-image-azure'
        age: '60d'
      - name: 'xtrem'
        age: '360d'
      - name: 'xtrem'
        version: 'showcase-*'
        age: '120d'
      - name: 'xtrem-glossary'
        age: '360d'
      - name: 'xtrem-shopfloor'
        age: '360d'
      - name: 'xtrem-cli-dev'
        age: '360d'

variables:
  # To get TF3_API_URL and TF3_API_KEY variables
  - group: dockerXtrem

steps:
  # we don't need a any source code
  - checkout: none

  # Configure npm to be able to run the pnpm dlx commands below
  - template: ../shared/npm-configure.yml

  - ${{ each image in parameters.images }}:
      - bash: |
          DELETE_OPT="--delete"
          if [[ ${DRY_RUN,,} == "true" ]]; then
            DELETE_OPT=""
          fi
          npx @sage/mxx nexus asset --name '${{ image.name }}' --version '${{ coalesce(image.version, '*')}}' --age ${{ image.age }} --age-filter last-modified --type docker $DELETE_OPT
        displayName: 'Delete ${{ image.age }} old ${{ image.name }} image'
        env:
          MXX_NEXUS_USER: $(npmUsername)
          MXX_NEXUS_PWD: $(npmPassword)
          DRY_RUN: ${{ parameters.dryRun }}
