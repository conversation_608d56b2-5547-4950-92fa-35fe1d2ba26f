# This template will generate the SQL files that will be replayed when upgrading a real cluster
# It will first restore a backup of a reference database (a database that was created from a fresh schema the day
# before - see dump-reference-database-to-s3.yml template)
# Then the upgrade engine will find differences between this ref schema and the current node definitions
# and generate a SQL file from these differences.
parameters:
  - name: scopeName
    displayName: 'Scope name aka. project name'
    type: string
    default: ''

  - name: deploymentMode
    displayName: 'Deployment mode'
    type: string
    default: 'development'

  - name: applicationDisplayName
    displayName: 'The displayName of the application'
    type: string

  - name: applicationRootFolder
    displayName: 'root folder of application'
    type: string

  - name: skipTenantTests
    displayName: 'Should the tenant tests be skipped ?'
    type: boolean
    default: false

steps:
  - bash: |
      set -e
      cd ${{ parameters.applicationRootFolder }}
      # Use a cryptic schema so that it will be easy to replace it with a tag in the
      # generated SQL files
      XTREM_SCHEMA_NAME="xxxxyyyyyzzzzz" pnpm run xtrem schema --create --reset-database
    displayName: '${{ parameters.applicationDisplayName }} - Initialize schema'
    condition: and(
      succeeded(),
      or(eq(variables.stage_scopes, '*'), containsValue(split(variables.stage_scopes, '|'), '${{ parameters.scopeName }}'))
      )
    env:
      XTREM_SKIP_FACTORY_CHECKS: 1

  - ${{ if not(parameters.skipTenantTests) }}:
      - template: ./tenant-management-tests.yml
        parameters:
          scopeName: ${{ parameters.scopeName }}
          applicationRootFolder: '${{ parameters.applicationRootFolder }}'
          schemaName: 'xxxxyyyyyzzzzz'
          deploymentMode: '${{ parameters.deploymentMode }}'
          applicationDisplayName: ${{ parameters.applicationDisplayName }}

  # Upgrade the schema from last database dump
  # This step will (if needed) generate SQL files (they will be committed later)
  - bash: |
      set -e
      cd ${{ parameters.applicationRootFolder }}
      # restore the last available database dump
      echo "##[group]Restore database"
      echo "##[command]XTREM_SCHEMA_NAME="xxxxyyyyyzzzzz" pnpm run xtrem schema --restore-from-s3"
      XTREM_SCHEMA_NAME="xxxxyyyyyzzzzz" pnpm run xtrem schema --restore-from-s3
      if [ "$?" != "0" ]; then
        exit $?
      fi
      echo "##[endgroup]"

      # run the upgrade (and record the SQL commands) to local files
      echo "##[group]Upgrade"
      echo "##[command]XTREM_SCHEMA_NAME="xxxxyyyyyzzzzz" pnpm run xtrem upgrade --run --install-dependencies --record"
      XTREM_SCHEMA_NAME="xxxxyyyyyzzzzz" pnpm run xtrem upgrade --run --install-dependencies --record
      echo "##[endgroup]"
    displayName: '${{ parameters.applicationDisplayName }} - Create upgrade SQL file'
    condition: or(eq(variables.stage_scopes, '*'), containsValue(split(variables.stage_scopes, '|'), '${{ parameters.scopeName }}'))
    env:
      XTREM_SKIP_FACTORY_CHECKS: 1
