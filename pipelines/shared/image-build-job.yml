parameters:
  - name: imageName
    type: string
    displayName: Image name
  - name: dockerFolder
    type: string
    displayName: Dockerfile folder
  - name: subname
    type: string
    displayName: 'Image subname like glossary, showcase, ...'
    default: ''
  - name: dryRun
    type: boolean
    default: false
  - name: skipTests
    type: boolean
    default: false
  - name: skipAwsPush
    type: boolean
    default: false
  - name: notifyReleaseDeliveryChannel
    type: boolean
    default: false
  - name: isDevImage
    type: boolean
    default: false
  - name: imageAppFolder
    type: string
    displayName: 'Image subname like glossary, showcase, ...'
    default: '/xtrem/app'

jobs:
  - job: image_build
    displayName: 'Build ${{ parameters.imageName }} ${{ parameters.subname }} image'
    timeoutInMinutes: 150

    pool:
      vmImage: 'ubuntu-latest'

    variables:
      - group: sagex3ci_github
      - group: globalSettings
      # variable group 'dockerXtrem' is defined at project level for all pipelines
      # see: https://sage-liveservices.visualstudio.com/X3%20XTREM/_library?itemType=VariableGroups&view=VariableGroupView&variableGroupId=149&path=dockerXtrem
      - group: dockerXtrem
      - group: ms_teams
      - group: aquasecurity
      - name: aquaScanEnabled
        value: $[eq(variables['ENABLE_AQUA_SCAN'], 'True')]
      # Do not prefix the image name with the repo name because the push will do it for you
      - name: imageName
        value: ${{ parameters.imageName }}
      - name: subname
        value: ${{ parameters.subname }}
      - name: dockerFolder
        value: ${{ parameters.dockerFolder }}
      - ${{ if not(eq(parameters.subname, '')) }}:
          - name: tagPrefix
            value: '$(subname)-'
          - name: exitTitle
            value: '$(subname) docker image build'
      - ${{ if eq(parameters.subname, '') }}:
          - name: tagPrefix
            value: ''
          - name: exitTitle
            value: 'Xtrem docker image build'
      - name: tagName
        value: '$(tagPrefix)latest'

    # ========================================
    # == Xtrem Docker image build
    # ========================================
    steps:
      - checkout: none

      # Safe guard to not disable tests if not a dry run
      - bash: |
          echo "##vso[task.logissue type=error]Skipping tests is allowed in dry run only!"
          echo "##vso[task.setvariable variable=disableNotify]True"
          exit 1
        condition: and(${{ parameters.skipTests }}, not(${{ parameters.dryRun }}))

      - template: ./manual-git-clone.yml
        parameters:
          depth: 100

      # Init xtremBranch variable
      - template: ./branch-name.yml
        parameters:
          allowAnyBranch: ${{ parameters.dryRun }}

      # Init imageVersion variable
      - template: ./get-image-version.yml
        parameters:
          packagePath: '$(dockerFolder)'
          branchName: '$(xtremBranch)'

      # set the tag name depending on the branch, the variable tagName has the new value only outside that task
      - bash: |
          MINOR_TAG=""
          MAJOR_TAG=""
          SOURCE_BRANCH_NAME=$(BUILD.SOURCEBRANCHNAME)
          if [ "$SOURCE_BRANCH_NAME" != "master" ]; then
            BRANCH_STREAM=$(echo $(BUILD.SOURCEBRANCH) | awk -F / '{print $3}')
            echo stream is ${BRANCH_STREAM}
            if [ "${BRANCH_STREAM}" == "release" ]; then
              DOCKER_IMAGE_TAG=$(tagPrefix)$(imageVersion)
              MINOR_TAG=$(echo $DOCKER_IMAGE_TAG | awk -F. '{ print $1"."$2 }')
              MAJOR_TAG=$(echo $DOCKER_IMAGE_TAG | awk -F. '{ print $1 }')
              echo "##vso[task.setvariable variable=tagName]${DOCKER_IMAGE_TAG}"
            else
              if [ "${{ parameters.dryRun }}" == "True" ]; then
                echo "##vso[task.logissue type=warning]Running in dry mode, the image won't be pushed!"
              else
                echo "##vso[task.logissue type=error]bad branch name: only master and release/x.y can be used to generate images"
                exit 1
              fi
            fi
          fi
          echo "##vso[task.setvariable variable=tagMinor]${MINOR_TAG}"
          echo "##vso[task.setvariable variable=tagMajor]${MAJOR_TAG}"
          echo "##vso[task.setvariable variable=tempContainer]$(imageName)_$(Build.BuildId)"
          echo "##vso[task.setvariable variable=pg_version]$(cat .pgdbrc)"
        displayName: set image tag name and postgres version

      # if the previous is ok, we just use that one to display the tag name
      - script: echo image will be generated with $(imageName):$(tagName) name, and [$(tagMinor), $(tagMajor)] additional tags
        displayName: final image name:tag

      # Login on nexus private registry
      - task: Docker@2
        displayName: Login to repository
        inputs:
          command: login
          containerRegistry: ghcr

      # .env file required for building the image in the next step
      - task: DownloadSecureFile@1
        displayName: 'Get docker env for build'
        name: 'dockerBuildEnv'
        inputs:
          secureFile: '.env-xtrem-docker-build'

      - bash: |
          # amend secure file
          echo 'AZURE_DEVOPS_TOKEN='$AZURE_DEVOPS_TOKEN'' >> ${DOCKER_ENV_SOURCE}
        displayName: 'Add Azure DevOps token to docker env file'
        env:
          DOCKER_ENV_SOURCE: $(dockerBuildEnv.secureFilePath)
          AZURE_DEVOPS_TOKEN: $(System.AccessToken)

      # Build the multi-stage default alpine image
      - bash: |
          $(Build.SourcesDirectory)/scripts/docker/docker-build-image.sh
        env:
          DOCKER_ENV_SOURCE: $(dockerBuildEnv.secureFilePath)
          IMAGE_NAME: $(imageName)
          TAG_NAME: $(tagName)
          TAG_MINOR: $(tagMinor)
          TAG_MAJOR: $(tagMajor)
        workingDirectory: $(dockerFolder)
        displayName: 'Build default image'

      # Build the multi-stage debian (puppeteer) image
      - bash: |
          echo "Running in $(pwd)"
          if [ ! -d ./puppeteer ]; then
            echo "No puppeteer folder found, skipping build"
            echo "##vso[task.setvariable variable=skipPuppeteerImage]true"
            exit 0
          fi
          if [ ! -f ./puppeteer/Dockerfile ]; then
            echo "Expecting puppeteer Dockerfile in $(pwd)/puppeteer"
            exit 1
          fi
          $(Build.SourcesDirectory)/scripts/docker/docker-build-image.sh \
            --image-name $(imageName)-puppeteer \
            --file ./puppeteer/Dockerfile
        env:
          DOCKER_ENV_SOURCE: $(dockerBuildEnv.secureFilePath)
          TAG_NAME: $(tagName)
          TAG_MINOR: $(tagMinor)
          TAG_MAJOR: $(tagMajor)
        workingDirectory: $(dockerFolder)
        displayName: 'Build puppeteer image'

      # Configuring test
      - bash: |
          ./replace-config.sh
        displayName: Replace variable in config files
        condition: and(succeeded(), not(${{ parameters.skipTests }}))
        workingDirectory: $(dockerFolder)
        env:
          PG_DBUSER: $(pg_dbuser)
          PG_DBPWD: $(pg_dbpwd)
          PG_SYSUSER: $(pg_sysuser)
          PG_SYSPWD: $(pg_syspwd)

      # Tag default images for smoke test
      - bash: |
          set -e
          docker tag ghcr.io/sage-erp-x3/$(imageName):$(tagName) $(imageName):$(tagPrefix)smoke-test
          echo "Image default version info:"
          docker run --rm ghcr.io/sage-erp-x3/$(imageName):$(tagName) --version
          docker images
        workingDirectory: $(dockerFolder)
        displayName: retag and show images (default)
        condition: and(succeeded(), not(${{ parameters.skipTests }}))

      # Tag puppeteer images for smoke test
      - bash: |
          set -e
          docker tag ghcr.io/sage-erp-x3/$(imageName)-puppeteer:$(tagName) $(imageName)-puppeteer:$(tagPrefix)smoke-test
          echo "Image puppeteer version info:"
          docker run --rm ghcr.io/sage-erp-x3/$(imageName)-puppeteer:$(tagName) --version
          docker images
        workingDirectory: $(dockerFolder)
        displayName: retag and show images (puppeteer)
        condition: and(succeeded(), not(eq(variables.skipPuppeteerImage, true)), not(${{ parameters.skipTests }}))

      # Test the image
      - bash: |
          docker compose -f $(dockerFolder)/docker-compose-smoke-test.yml up --abort-on-container-exit --exit-code-from xtrem
        displayName: Tenant smoke test (default)
        condition: and(succeeded(), not(${{ parameters.skipTests }}))
        env:
          PG_SYSUSER: $(pg_sysuser)
          PG_SYSPWD: $(pg_syspwd)

      - bash: |
          # replace the image name
          sed -i "s/image: ${IMAGE_NAME}:/image: ${IMAGE_NAME}-puppeteer:/g" $(dockerFolder)/docker-compose-smoke-test.yml
          cat $(dockerFolder)/docker-compose-smoke-test.yml
          docker compose -f $(dockerFolder)/docker-compose-smoke-test.yml up --abort-on-container-exit --exit-code-from xtrem
        displayName: Tenant smoke test (puppeteer)
        condition: and(succeeded(), not(eq(variables.skipPuppeteerImage, true)), not(${{ parameters.skipTests }}))
        env:
          IMAGE_NAME: $(imageName)
          PG_SYSUSER: $(pg_sysuser)
          PG_SYSPWD: $(pg_syspwd)

      # Get pnpm-lock.yaml from local tag then publish it as an artifact
      - bash: |
          set -e
          mkdir -p $(Build.ArtifactStagingDirectory)/image-build/
          docker create --name $(tempContainer) $(imageName):$(tagName)
          docker cp $(tempContainer):${X_APP_PATH}/package.json $(Build.ArtifactStagingDirectory)/image-build/package.json
          docker cp $(tempContainer):${X_APP_PATH}/pnpm-lock.yaml $(Build.ArtifactStagingDirectory)/image-build/pnpm-lock.yaml
          docker rm $(tempContainer)
          ls -l $(Build.ArtifactStagingDirectory)/image-build
        displayName: Get pnpm-lock.yaml from locally created docker image
        env:
          X_APP_PATH: ${{ parameters.imageAppFolder }}

      - publish: $(Build.ArtifactStagingDirectory)/image-build/
        artifact: image-build
        displayName: Publish pnpm-lock.yaml artifact

      - bash: |
          docker images
        displayName: List docker images before scanning

      # AquaSec image scan
      # Use local image to avoid overriding of the nexus image
      - template: ./image-scan.yml
        parameters:
          imageName: $(imageName)
          tag: $(tagName)
          isDevImage: ${{ parameters.isDevImage }}

      # AquaSec image scan
      # Use local image to avoid overriding of the nexus image
      - template: ./image-scan.yml
        parameters:
          imageName: $(imageName)-puppeteer
          tag: $(tagName)
          isDevImage: true
          # isDevImage: ${{ parameters.isDevImage }}

      # Login again on nexus private registry because the scan changed the repo
      - task: Docker@2
        displayName: Login to repository
        inputs:
          command: login
          containerRegistry: ghcr

      # Push the image on private registry
      - task: Docker@2
        displayName: Push the default image
        condition: and(succeeded(), not(${{ parameters.dryRun }}))
        inputs:
          repository: sage-erp-x3/$(imageName)
          command: push
          tags: |
            $(tagName)
            $(tagMinor)
            $(tagMajor)

      - task: Docker@2
        displayName: Push the puppeteer image
        condition: and(succeeded(), not(eq(variables.skipPuppeteerImage, true)), not(${{ parameters.dryRun }}))
        inputs:
          repository: sage-erp-x3/$(imageName)-puppeteer
          command: push
          tags: |
            $(tagName)
            $(tagMinor)
            $(tagMajor)

      - task: Docker@2
        displayName: Logout from repository
        inputs:
          command: logout
          containerRegistry: ghcr

      - ${{ if not(parameters.skipAwsPush) }}:
          - task: ECRPushImage@1
            displayName: Push default image to AWS
            condition: and(succeeded(), not(${{ parameters.dryRun }}))
            inputs:
              awsCredentials: 'xtrem dev-eu'
              regionName: 'eu-west-1'
              imageSource: 'imagename'
              sourceImageName: $(imageName)
              sourceImageTag: $(tagName)
              pushTag: $(tagName)
              repositoryName: $(imageName)

          - task: ECRPushImage@1
            displayName: Push puppeteer image to AWS
            condition: and(succeeded(), not(eq(variables.skipPuppeteerImage, true)), not(${{ parameters.dryRun }}))
            inputs:
              awsCredentials: 'xtrem dev-eu'
              regionName: 'eu-west-1'
              imageSource: 'imagename'
              sourceImageName: $(imageName)-puppeteer
              sourceImageTag: $(tagName)
              pushTag: $(tagName)
              repositoryName: $(imageName)-puppeteer

      # notify ci-support if not a dry run build
      - ${{ if and(not(parameters.dryRun), not(eq(variables.disableNotify, True))) }}:
          - template: ./notify-steps.yml
            parameters:
              incomingWebhookUrl: '$(ci_support_channel)'

      # notify release-delivery only if not a dry run build and if not the master branch
      - ${{ if and(not(parameters.dryRun), eq(parameters.notifyReleaseDeliveryChannel, true), not(eq(variables['Build.SourceBranchName'], 'master'))) }}:
          - template: ./notify-steps.yml
            parameters:
              incomingWebhookUrl: '$(release_delivery_channel)'
