parameters:
  - name: skipChangeSet
    displayName: 'deprecated use computeChangeSet if needed'
    default: true
    type: boolean

  - name: skipMdFilesCheck
    displayName: 'deprecated use computeChangeSet if needed'
    default: true
    type: boolean

  - name: computeChangeSet
    displayName: 'compute the changet set of a PR'
    default: false
    type: boolean

  - name: skipInstall
    default: false
    type: boolean

  - name: installCliOnly
    default: false
    type: boolean

  - name: skipNativeDependencies
    default: false
    type: boolean

  - name: useNpmProxy
    displayName: 'Use nexus npm proxy'
    default: false
    type: boolean

  - name: withPrLock
    displayName: 'Lock PR for the base branch'
    default: false
    type: boolean

  - name: updateS3Images
    displayName: 'Update S3 images'
    default: false
    type: boolean

steps:
  - template: ./install-node.yml

  - ${{ if eq(parameters.withPrLock, true) }}:
      # Lock the pull requests (prevent any PR to be merged while the pipeline is running)
      # then pull to be sure we are in sync with latest changes that may happen in between
      - bash: |
          set -e
          echo "##vso[task.setvariable variable=prStatus;]inProgress"
          node scripts/releng/release-guard.mjs lock $BRANCH_NAME
          echo "##[group] pull $BRANCH_NAME"
          git pull
          echo "##[endgroup]"
        displayName: 'Locking merging pull requests'
        env:
          GITHUB_USERNAME: $(github_username)
          GITHUB_PASSWORD: $(github_password)
          SYSTEM_ACCESSTOKEN: $(System.AccessToken)
          BRANCH_NAME: $(xtremBranch)

  - bash: |
      ps aux | grep -i apt | grep -v grep
      if [[ $? -eq 0 ]]; then
        echo "An apt command is in progress, waiting 30 seconds before installing..."
        sleep 30
      fi
      if ! sudo dpkg -l | grep -q graphicsmagick; then
        echo "graphicsmagick is not installed, installing..."
        sudo apt-get update
        sudo apt-get install -y graphicsmagick
      else
        echo "graphicsmagick is already installed"
      fi

    displayName: 'Install native dependencies'
    condition: eq('${{ parameters.skipNativeDependencies }}', false)

  - template: ./npm-configure.yml
    parameters:
      useNpmProxy: ${{ parameters.useNpmProxy }}

  # Compute the change set:
  #   The change set is computed as a json output, then every variable is set using jq to extract the value.
  #   See https://stedolan.github.io/jq/manual/ for more info on jq syntax
  # One of the purpose is to detect whether this PR only concerns files that does not affect the xtrem build.
  # If so, all the related steps will be skipped.
  # This notably sets the 'doesNotAffectBuild' variable to:
  # - 'true' : the PR does not affect the build related steps (build/lint/test/...)
  # - 'false': the PR may affect the build - run the full validation (build/lint/test/...)

  - ${{ if and(not(parameters.skipInstall), eq(parameters.computeChangeSet, true)) }}:
      - bash: |
          set -e
          echo "XDEV_LINKED: ${XDEV_LINKED}"
          echo "XDEV_BRANCH: ${XDEV_BRANCH}"
          echo "xdev command: $(command -v xdev || echo 'not found')"
          if ! xdev --version >/dev/null; then
            # xdevDepVersion=$(jq -r '.devDependencies["@sage/xdev"]' package.json | sed 's/^[^0-9]*//')
            # get version from pnpm-lock.yaml
            xdevDepVersion=$(grep -E "'@sage/xdev@\d+\.\d+\.\d+':" pnpm-lock.yaml | sed 's/[^0-9.]*//g')
            echo "xdev is not installed, installing version ${xdevDepVersion} globally using pnpm"
            pnpm install -g @sage/xdev@${xdevDepVersion}
          fi
          echo "xdev version: $(xdev --version || echo 'not found')"
          echo ""
          echo "Computing change set for PR: $SOURCEBRANCH -> $TARGETBRANCH"
          # compute the change set and create the shell script for later use in other stages
          xdev ci compute-change-set \
            --source-branch "$SOURCEBRANCH" \
            --target-branch "$TARGETBRANCH" \
            --scopes out/scopes.json \
            --script out/azure-var-from-change-set.js \
            --set-azure-var \
            --print

          echo -e "\ndoesNotAffectBuild: $doesNotAffectBuild\n"
          # we run the post-install command to link all required scripts with the xdev tool installed
          echo "Running post-install for xdev"
          xdev post-install
          XDEV_SCRIPTS_DIR="$(xdev --show-script-path)"
          echo "List of xdev scripts in ${XDEV_SCRIPTS_DIR}:"
          ls -Rl "${XDEV_SCRIPTS_DIR}"
        env:
          SOURCEBRANCH: 'origin/$(system.pullrequest.sourcebranch)'
          TARGETBRANCH: 'origin/$(system.pullrequest.targetbranch)'
        displayName: 'Compute change set'
        name: changeSet
        condition: and(succeeded(), ne(variables['Build.SourceBranch'], 'refs/heads/master'))

  - ${{ if not(parameters.skipInstall) }}:
      - bash: |
          echo "##vso[task.setvariable variable=doesNotAffectBuild]false"
        displayName: 'Set MD variable'
        name: skip_documentation_check
        condition: eq(variables['Build.SourceBranch'], 'refs/heads/master')
        continueOnError: true

  - bash: |
      # the build process needs S3 write credentials to s3://xtrem-developers-utility/builds/ to fetch (and write builds)
      echo "##vso[task.setvariable variable=S3_XTREM_DEVELOPERS_UTILITY_ACCESS_KEY;]$(s3-xtrem-developers-utility-access-key)"
      echo "##vso[task.setvariable variable=S3_XTREM_DEVELOPERS_UTILITY_SECRET;]$(s3-xtrem-developers-utility-secret)"
    displayName: Set S3 env variables

  - task: Cache@2
    inputs:
      key: 'pnpm | "$(Agent.OS)" | pnpm-lock.yaml | package.json | "azure-registry"'
      path: $(pnpm_home)
    displayName: Cache pnpm store
    condition: and(succeeded(), not(eq(variables.doesNotAffectBuild, true)))

  - ${{ if not(parameters.skipInstall) }}:
      - bash: |
          missing=()
          for pack in build-essential libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev; do
            if ! sudo dpkg -l | grep -q "$pack"; then
              missing+=($pack)
            fi
          done
          if [[ ${#missing[@]} -gt 0 ]]; then
            echo "The following packages were not installed: ${missing[@]}, installing..."
            sudo apt-get update
            sudo apt-get install -y "${missing[@]}"
          else
            echo "All required packages are already installed"
          fi
        displayName: 'canvas lib - prerequisites'
        condition: and(succeeded(), not(eq(variables.doesNotAffectBuild, true)))

      - bash: pnpm run install:ci
        displayName: 'PNPM - Install'
        condition: and(succeeded(), not(${{ parameters.installCliOnly }}), not(eq(variables.doesNotAffectBuild, true)))
        env:
          NPM_CONFIG_USERCONFIG: '$(agent.tempdirectory)/.npmrc'

  - ${{ if eq(parameters.installCliOnly, true) }}:
      - task: Docker@2
        displayName: Login to repository
        inputs:
          command: login
          containerRegistry: ghcr-ro

  - ${{ if eq(parameters.installCliOnly, true) }}:
      - bash: |
          pipelines/shared/scripts/install-cli-dev.sh
        displayName: 'NPM - Install CLI globally'
        env:
          NPM_USERNAME: $(npmUsername)
          NPM_PASSWORD: $(npmPassword)
