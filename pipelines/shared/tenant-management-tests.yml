# Performs a bunch of tests related to tenant management (initialization, export/import, deletion, ...)

parameters:
  - name: scopeName
    displayName: 'Scope name aka. project name'
    type: string
    default: ''
  - name: applicationRootFolder
    displayName: 'Application root folder'
    type: string
  - name: applicationDisplayName
    displayName: 'The displayName of the application'
    type: string
  - name: schemaName
    displayName: 'Schema name'
    type: string
    default: 'xtrem'
  - name: displayNameSuffix
    displayName: 'A suffix to add to all the display names of the pipeline steps'
    type: string
    default: ''
  - name: deploymentMode
    displayName: 'Deployment mode'
    type: string
    default: 'development'
  - name: backupType
    displayName: 'Backup type to restore'
    type: string
    default: ''

steps:
  # set a variable to ease the condition testing from the PR pipeline
  - ${{ if not(eq(parameters.scopeName,'')) }}:
      - bash: |
          echo "##[command]SKIP_TENANT_TEST=$SKIP_TENANT_TEST"
          echo "##vso[task.setvariable variable=skipTenantTests]$SKIP_TENANT_TEST"
        displayName: 'set skipTenantTests to true'
        env:
          SKIP_TENANT_TEST: $[ or(eq(variables.stage_scopes, '*'), containsValue(split(variables.stage_scopes, '|'), '${{ parameters.scopeName }}') ]

  - bash: |
      set -e
      echo "XTREM_SCHEMA_NAME=$XTREM_SCHEMA_NAME"
      pipelines/shared/scripts/tenant-export-import-test.sh
      pipelines/shared/scripts/tenant-export-import-with-pattern-test.sh
      pipelines/shared/scripts/tenant-delete-create-test.sh
    env:
      XTREM_MAIN_APP_DIR: ${{ parameters.applicationRootFolder }}
      XTREM_SCHEMA_NAME: ${{ parameters.schemaName }}
      XTREM_SKIP_FACTORY_CHECKS: 1
    displayName: '${{ parameters.applicationDisplayName }} - Tenant smoke tests${{ parameters.displayNameSuffix }}'
    condition: and(
      succeeded(),
      not(eq(variables.skipTenantTests, true)),
      not(eq(variables.doesNotAffectBuild, true))
      )

  - ${{ if and(eq(parameters.backupType, ''), eq(parameters.deploymentMode, 'development')) }}:
      - bash: |
          set -e
          pipelines/shared/scripts/tenant-create-reset-data-test.sh
          pipelines/shared/scripts/tenant-extract-data-test.sh
        env:
          XTREM_MAIN_APP_DIR: ${{ parameters.applicationRootFolder }}
          XTREM_SCHEMA_NAME: ${{ parameters.schemaName }}
          XTREM_SKIP_FACTORY_CHECKS: 1
        displayName: '${{ parameters.applicationDisplayName }} - Tenant data smoke tests'
        condition: and(
          succeeded(),
          not(eq(variables.skipTenantTests, true)),
          not(eq(variables.doesNotAffectBuild, true))
          )
