#!/usr/bin/env bash

set -e
cliVersion=$(jq -r '.version' docker/cli-dev-image/package.json)

echo "##[group] installing version ${cliVersion} using a docker container..."
xtremCliPath=$(dirname "$(which npm)")/xtrem
# remove global link if any and create a new link in the global npm bin
rm -f "$xtremCliPath"
ln -s "$(pwd)"/pipelines/cucumber-test/scripts/xtrem-cli-atp.sh "$xtremCliPath"
echo "##[endgroup]"
