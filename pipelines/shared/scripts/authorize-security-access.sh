#!/bin/env bash
set -e

IP_FILE=$1
if [ -z "${IP_FILE}" ]; then
    echo "Usage: $0 <ip_file>"
    exit 1
fi
aws ec2 describe-security-groups --filters Name=group-name,Values="${SG_NAME}" > "${SEC_GROUP_DESC_FILE}"
echo "Current registered AZ pipelines:" >&2
jq -r '.SecurityGroups[].IpPermissions[] | select (.ToPort == 1433) | .IpRanges[] | select (.Description // "-" | test("^SQL access from AZ pipeline"))' "${SEC_GROUP_DESC_FILE}"
sgId=$(jq -r .SecurityGroups[].GroupId "${SEC_GROUP_DESC_FILE}")
currentIp=$(curl -s https://checkip.amazonaws.com)
echo -e "\nCurrent IP: ${currentIp}"
echo "##vso[task.setvariable variable=CURRENT_IP]${currentIp}"

publicIp=$(aws ec2 describe-instances --filters "Name=tag:Name,Values=${INSTANCE_NAME}"|jq -r .Reservations[].Instances[].PublicIpAddress)
PERM_DESC="SQL access from AZ pipeline ${SYSTEM_DEFINITIONID} build ${BUILD_BUILDID}"
echo -n "$publicIp" > "${IP_FILE}"

if ! aws ec2 authorize-security-group-ingress --group-id "${sgId}" \
    --ip-permissions IpProtocol=tcp,FromPort=1433,ToPort=1433,IpRanges="[{CidrIp=${currentIp}/32,Description='""${PERM_DESC}""'}]" \
    2> "${TMP_DIR}"/authorize-security-group-ingress.err ; then
    cat "${TMP_DIR}"/authorize-security-group-ingress.err

    if grep -q "InvalidPermission.Duplicate" "${TMP_DIR}"/authorize-security-group-ingress.err; then
        echo "Permission already exists, skipping..."
    else
        echo "Error while adding ip to security group " && exit 1
    fi
fi

# try to connect with 5 retry
echo ""
for i in {1..5}; do
    [ $i -gt 1 ] && sleep 5
    nc -zvw5 "${publicIp}" 1433 && exit 0 || exitcode=$?;
done;

exit ${exitcode}
