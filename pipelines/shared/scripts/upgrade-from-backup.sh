#!/usr/bin/env bash

echo "XTREM_MAIN_APP_DIR=${XTREM_MAIN_APP_DIR}"
echo "XTREM_SCHEMA_NAME=${XTREM_SCHEMA_NAME}"
echo "XTREM_BACKUP_TYPE=${XTREM_BACKUP_TYPE}"
echo "XTREM_CHECK_SCHEMA=${XTREM_CHECK_SCHEMA}"
echo "XTREM_CLUSTER_NAME=${XTREM_CLUSTER_NAME}"


echo "##[group]Get deployment mode from config"
echo "##[command]yq .deploymentMode xtrem-config.yml"
xtremDeploymentMode=$(yq .deploymentMode xtrem-config.yml)
echo ">>> xtrem config deploymentMode is ${xtremDeploymentMode}"
echo "##[endgroup]"

cd "${XTREM_MAIN_APP_DIR}"

set -e
echo "##[group]Reset database schema"
echo "##[command]pnpm run xtrem schema --create --reset-database"
pnpm run xtrem schema --create --reset-database
echo "##[endgroup]"

if [ "${xtremDeploymentMode}" == "production" ]; then
    echo "##[group]Verify production schema"
    echo "select _id,tenant_id from ${XTREM_SCHEMA_NAME}.sys_tenant;" > "${XTREM_TMP_DIR}"/query-tenants.sql
    docker cp "${XTREM_TMP_DIR}"/query-tenants.sql xtrem_postgres:/tmp/query-tenants.sql
    set +e
    echo ">>> execute tenants sql query"
    docker exec -i xtrem_postgres psql -h localhost -d xtrem -U xtrem -p 5432 -a -q -f /tmp/query-tenants.sql | grep '^(0 rows)'
    # at least one tenant exits
    if [ $? -eq 1 ]; then
        echo "ERROR: A newly created schema must not contain any tenant in production mode"
        exit 1
    fi
    echo "##[endgroup]"
fi

set -e

echo "##[group]Restore backup ${XTREM_BACKUP_TYPE}"
echo "##[command]pnpm run xtrem schema --restore-from-s3 ${XTREM_BACKUP_TYPE} --check-single-schema"

pnpm run xtrem schema --restore-from-s3 "${XTREM_BACKUP_TYPE}" --check-single-schema

echo "##[endgroup]"

# compare lower-case
if [[ ${XTREM_SKIP_TENANT_TESTS,,} != "true" ]]; then
    echo "##[group]Test fixing of column order"
    echo "##[command]pnpm run xtrem schema --fix-column-order"
    pnpm run xtrem schema --fix-column-order
    echo "##[endgroup]"
fi

echo "##[group]Upgrade (${XTREM_UPGRADE_TYPE})"
if [ "${XTREM_UPGRADE_TYPE}" == "test" ]; then
    echo "##[command]pnpm run xtrem upgrade --test --skip-db-restore --cluster-name ${XTREM_CLUSTER_NAME}"
    pnpm run xtrem upgrade --test --skip-db-restore --cluster-name "${XTREM_CLUSTER_NAME}"
else
    echo "##[command]pnpm run xtrem upgrade --run --prod --cluster-name ${XTREM_CLUSTER_NAME}"
    pnpm run xtrem upgrade --run --prod --cluster-name "${XTREM_CLUSTER_NAME}"
fi
echo "##[endgroup]"

if [ "${XTREM_CHECK_SCHEMA}" == "true" ]; then
echo "##[group]Check SQL schema"
echo "##[command]pnpm run xtrem schema --check"
pnpm run xtrem schema --check
echo "##[endgroup]"
fi

echo "##[group]Full reload of setup data"
echo "##[command]pnpm run xtrem tenant --update-setup-data --force"
pnpm run xtrem tenant --update-setup-data --force --cluster-name "${XTREM_CLUSTER_NAME}"
echo "##[endgroup]"
