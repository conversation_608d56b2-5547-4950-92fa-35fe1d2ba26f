#!/bin/sh

# XTREM_CACHE_READ_ONLY_KEY and XTREM_CACHE_READ_ONLY_SECRET are initialized by using 
# the dockerXtrem variable group in azure pipelines

if [ ! -z "$2" ]; then
  XTREM_CACHE_READ_ONLY_KEY="$2"
fi
if [ ! -z "$3" ]; then
  XTREM_CACHE_READ_ONLY_SECRET="$3"
fi
if [ -z "$XTREM_CACHE_READ_ONLY_KEY" ] || [ -z "$XTREM_CACHE_READ_ONLY_SECRET" ]; then
  echo ""
  echo "*** WARNING ***"
  echo "  XTREM_CACHE_READ_ONLY_KEY and XTREM_CACHE_READ_ONLY_SECRET environment variable are not defined"
  echo "  Make sure dockerXtrem variable group is used in the pipeline"
  echo ""
  exit 1
fi

config_file=$1
sed -i "s/accessKey: '<xtremCacheReadOnlyKey>'/accessKey: '${XTREM_CACHE_READ_ONLY_KEY}'/g" "$config_file"
# Caution: the secret may contain some / but we are using this character as a separator for sed
# we first have to escape the / (-> \/)
FIXED_SECRET=$(echo ${XTREM_CACHE_READ_ONLY_SECRET} | sed 's/\//\\\//g')
# and now, we can use the secret with escaped slashes
sed -i "s/secret: '<xtremCacheReadOnlySecret>'/secret: '${FIXED_SECRET}'/g" "$config_file"
