#!/usr/bin/env bash

# Check operation result
# arguments
# 1 - ACCESS_TOKEN
# 2 - OPERATION_URL
# 3 - INDIVIDUAL

# return code
# 0 - success
# 1 - no operation url provided
# 2 - Multiple error after retries, assuming failure
# 3 - operation error
# 4 - timed out

ACCESS_TOKEN=${1}
OPERATION_URL=${2}
INDIVIDUAL=${3}

# Check the response for an operation id or an error
if [ "" = "${OPERATION_URL}" ] || [ "null" = "${OPERATION_URL}" ]; then
    return 1
fi

# Wait for the provision tenant operation to complete
ERROR_COUNT=0
SECONDS=0                    # build int bash to track seconds
MAX_DURATION_IN_SECONDS=${MAX_DURATION_IN_SECONDS:-"600"} # 10 minutes might not be enough depending on the operation
STATUS="none"
STATUS_SUCCESS="success"
STATUS_ERROR="error"

PREFIX="     "

until [[ "${SECONDS}" -gt "${MAX_DURATION_IN_SECONDS}" ]]; do
    sleep 5
    FETCH_OPERATION_RESPONSE=$(curl -sS -X GET -H "Content-Type:application/json" -H "Authorization:Bearer ${ACCESS_TOKEN}" -H "X_ROLE:OPS_ADMIN" -H "X_INDIVIDUAL:${INDIVIDUAL}" "${OPERATION_URL}")

    STATUS=$(echo "${FETCH_OPERATION_RESPONSE}" | jq -r .status)
    STEP=$(echo "${FETCH_OPERATION_RESPONSE}" | jq -r .curStep)

    if [ "null" = "${STATUS}" ]; then
        echo "${PREFIX}Error while fetching operation status, response from api :${FETCH_OPERATION_RESPONSE}"
        ERROR_COUNT=$((ERROR_COUNT + 1))

        if [[ $ERROR_COUNT -gt 5 ]]; then
            return 2
        else
            echo "${PREFIX}Retrying, error counter is ${ERROR_COUNT}"
        fi

    else
        #Status read ok
        if [ "${STATUS_SUCCESS}" = "${STATUS}" ]; then
            return 0
        fi
        if [ "${STATUS_ERROR}" = "${STATUS}" ]; then
            return 3
        fi

        echo "${PREFIX}Status of workflow is ${STATUS}, step is ${STEP}"
    fi
done

return 4
