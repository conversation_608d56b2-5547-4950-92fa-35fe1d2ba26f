#!/usr/bin/env bash

# Get an access token using the SageId client id and client secret passed as arguments
# If successful set the result into a variable named accessToken
# On error set the exitMessage to return an error to the pipeline

echo "Fetching cloudID token to authenticate to xtrem multi-tenant api"
ACCESS_TOKEN=$(pipelines/shared/scripts/get-cloud-manager-access-token.sh)
if [ "null" = "${ACCESS_TOKEN}" ] || [ "" = "${ACCESS_TOKEN}" ]; then
    echo "Error while fetching access token from cloudID :$TOKEN_HTTP_RESPONSE"
    echo "##vso[task.setvariable variable=exitMessage;]Error while fetching access token from cloudID"
    exit 2
else
    echo "##vso[task.setvariable variable=accessToken;]${ACCESS_TOKEN}"
fi

echo "Successfully fetched the API access token"
