#!/usr/bin/env bash

# You can run this script from the xtrem root source dir with:
# XTREM_SCHEMA_NAME=xtrem XTREM_MAIN_APP_DIR=services/main/xtrem-services-main ./pipelines/shared/scripts/tenant-full-test.sh

# exit as soon as we get an exit code different from 0
set -e

XTREM_SCHEMA_NAME=${XTREM_SCHEMA_NAME:-xtrem}
export XTREM_SCHEMA_NAME

XTREM_MAIN_APP_DIR=${XTREM_MAIN_APP_DIR:-services/main/xtrem-services-main}
export XTREM_MAIN_APP_DIR

if [[ "$1" ==  "reset" ]]; then
    cd "$XTREM_MAIN_APP_DIR"
    pnpm xtrem schema --create --reset-schema
    cd -
fi
"$(dirname "$0")"/tenant-export-import-test.sh
"$(dirname "$0")"/tenant-export-import-with-pattern-test.sh
"$(dirname "$0")"/tenant-delete-create-test.sh
"$(dirname "$0")"/tenant-create-reset-data-test.sh

echo ""
echo "All's good! Smoke tests passed successfully!"
