#!/usr/bin/env bash

# Exit immediately if a command exits with a non-zero status
set -e

isCI() {
    # azure || github actions || other azure detection || others
    if [[ "${TF_BUILD,,}" == "true"  ||  -n $BUILD_BUILDID || -n $BUILD_ID ]]; then
        return 0
    fi
    return 1
}

cd "$XTREM_MAIN_APP_DIR"
pwd

XTREM_ROOT_DIR=$(git rev-parse --show-toplevel)

XTREM=${XTREM_ROOT_DIR}/platform/cli/xtrem-cli/bin/xtrem

if [ ! -f "${XTREM}" ]; then
    echo -e "❌ invalid path to xtrem cli: ${XTREM}"
    exit 1
fi
echo "Use xtrem cli: $XTREM from ${XTREM_MAIN_APP_DIR}"

if isCI; then
    echo "Copy config $XTREM_ROOT_DIR/xtrem-config-azure.yml to $XTREM_SCOPE_ROOT_DIR/xtrem-config.yml"
    cp "$XTREM_ROOT_DIR"/xtrem-config-azure.yml "$XTREM_ROOT_DIR"/xtrem-config.yml
fi

# Check if we have the expected commands loaded
HELP_RESULT_FILE="${XTREM_ROOT_DIR}"/tmp/xtrem-help.txt
node "$XTREM" --help 2>&1 | grep -v '\----- |' > "$HELP_RESULT_FILE"

cat "$HELP_RESULT_FILE"
echo ""
error_count=0
if ! grep -q "Usage: xtrem" "${HELP_RESULT_FILE}"; then
    echo -e "❌ invalid xtrem cli: No usage found"
    error_count=$((error_count+1))
else
    echo -e "✅ Usage found in xtrem cli"
fi

if ! grep -q "Commands:" "${HELP_RESULT_FILE}"; then
    echo -e "❌ invalid xtrem cli: No commands found"
    error_count=$((error_count+1))
else
    echo -e "✅ Commands found in xtrem cli"
fi

COMMANDS=("pg-anonymizer" "data-patch" "schema" "tenant" "upgrade" "compile-plugin" "compile" "init" "lint" "test" "layers" "start")
for COMMAND in "${COMMANDS[@]}"; do
    if ! grep -q "${COMMAND}" "${HELP_RESULT_FILE}"; then
        echo -e "    ❌ invalid xtrem cli: No ${COMMAND} command found"
        error_count=$((error_count+1))
    else
        echo -e "    ✅ ${COMMAND} command found in xtrem cli"
    fi
done

if ! grep -q "Options:" "${HELP_RESULT_FILE}"; then
    echo -e "❌ invalid xtrem cli: No options found"
    error_count=$((error_count+1))
else
    echo -e "✅ Options found in xtrem cli"
fi

OPTIONS=("--version" "--help" "--verbose")
for OPTION in "${OPTIONS[@]}"; do
    if ! grep -q "\\${OPTION}" "${HELP_RESULT_FILE}"; then
        echo -e "    ❌ invalid xtrem cli: No ${OPTION} option found"
        error_count=$((error_count+1))
    else
        echo -e "    ✅ ${OPTION} option found in xtrem cli"
    fi
done

if [ $error_count -gt 0 ]; then
    echo -e "❌ invalid xtrem cli: Some commands or options are missing"
    error_count=$((error_count+1))
else
    echo "✅ All expected commands and options found in xtrem cli"
fi
