#!/usr/bin/env bash
productName=$1
subFolder=$2
fileName=$3
targetRoot=$4
sourceRoot=$5
txtFileName=$6
txtFilePath=$7

zipFileName=$(echo "$fileName.zip")
zipDeliveryFile=$(echo "$sourceRoot/$zipFileName")

echo "=== ${productName} content ==="
ls -l ${sourceRoot}
echo "=== zipDeliveryFile ==="
ls -l ${zipDeliveryFile}

echo "=== copy to S3 ==="
echo "copy ${zipDeliveryFile} to ${targetRoot}/${productName}/${subFolder}/${zipFileName}"
aws s3 cp ${zipDeliveryFile} ${targetRoot}/${productName}/${subFolder}/${zipFileName}

echo "copy ${txtFilePath} to ${targetRoot}/${productName}/${txtFileName}"
aws s3 cp ${txtFilePath} ${targetRoot}/${productName}/${txtFileName}
