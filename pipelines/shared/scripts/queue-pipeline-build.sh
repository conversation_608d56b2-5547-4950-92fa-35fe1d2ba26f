#!/usr/bin/env bash

set -e

if [[ -z $1 || -z $2 ]]; then
    echo "No pipelines to queue"
    exit 0
fi
DRY_RUN=${3,,}
if [[ "${DRY_RUN}" != "true" ]]; then
    DRY_RUN="false"
fi

# shellcheck disable=SC2206
DEF_NAMES=(${1//,/ })
# shellcheck disable=SC2206
DEF_IDS=(${2//,/ })

if [[ ${#DEF_NAMES[@]} -ne ${#DEF_IDS[@]} ]]; then
    echo "The list of names and ids must have the same number of elements"
    exit 1
fi
if [[ ${#DEF_NAMES[@]} -eq 0 ]]; then
    echo "No pipelines to queue"
    exit 0
fi

INDEX=0
for DEF_ID in "${DEF_IDS[@]}"; do
    echo "Queue build of ${DEF_NAMES[$INDEX]} on $BRANCH_NAME using pipeline id ${DEF_ID}..."

    BUILD_BODY=$(jq --null-input \
    --arg sourceBranch "$BRANCH_NAME" \
    --argjson id "${DEF_ID}" \
    --argjson dryRun "${DRY_RUN}" \
    '{ sourceBranch: $sourceBranch, definition: { id: $id }, templateParameters: {dryRun: $dryRun } }')
    echo "$BUILD_BODY"

    TOKEN=$(echo -n ".:${AZURE_PAT}" | base64 -w 0)
    # queue the build, we use a PAT because the SYSTEM_ACCESSTOKEN does not have the right to do it
    curl -H "Content-Type: application/json" \
        -H "Authorization: Basic ${TOKEN}" \
        -d "${BUILD_BODY}" \
        "${SYSTEM_COLLECTIONURI}${SYSTEM_TEAMPROJECTID}/_apis/build/builds?api-version=7.0"

    _=$((INDEX++))
done
