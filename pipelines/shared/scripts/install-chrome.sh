#!/usr/bin/env bash
CHROME_VERSION="133.0.6943.141-1"
CURENT_VERSION=$(google-chrome --version --no-sandbox)
echo "Current version is $CURENT_VERSION"

EXPECTED_MAJOR_VERSION=$(echo "$CHROME_VERSION" | cut -d '.' -f 1)
MAJOR_VERSION=$(echo "$CURENT_VERSION" | cut -d ' ' -f 3 | cut -d '.' -f 1)
if [ "$MAJOR_VERSION" -eq "$EXPECTED_MAJOR_VERSION" ]; then
  echo "Google Chrome is already at the expected major version"
  exit 0
fi
echo "Google Chrome is not at the expected version"
echo "Installing Google Chrome $CHROME_VERSION"

# cat /etc/apt/sources.list.d/google-chrome.list
wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
echo 'deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main' | sudo tee /etc/apt/sources.list.d/google-chrome.list
# cat /etc/apt/sources.list.d/google-chrome.list
sudo apt-get update

# temporary fix for chrome 134 : to uncomment 3 folowing lines and remove after to rollback

# sudo apt-get --only-upgrade install google-chrome-stable
# google-chrome --version --no-sandbox
# which google-chrome


# Specify the version you want to install

CHROME_VERSION=${CHROME_VERSION:-"133.0.6943.141-1"}

# Download the specific version of Google Chrome
wget --no-verbose https://dl.google.com/linux/chrome/deb/pool/main/g/google-chrome-stable/google-chrome-stable_"${CHROME_VERSION}"_amd64.deb

# Install the specific version
sudo dpkg -i google-chrome-stable_"${CHROME_VERSION}"_amd64.deb

# Fix any missing dependencies
sudo apt-get install -f

google-chrome --version --no-sandbox
which google-chrome
