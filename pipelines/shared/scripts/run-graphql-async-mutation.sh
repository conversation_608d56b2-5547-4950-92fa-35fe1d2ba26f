#!/usr/bin/env node
const axios = require('axios');

/**
 * This script will invoke an async mutation, track it's completion and return its status/result
 * The result will be written to stdout in JSON format
 */

const server = 'http://localhost:8240/api/';

async function runQuery(method, query) {
    try {
        const result = await axios({
            url: server,
            method,
            data: { query },
        });
        return result.data;
    } catch (err) {
        console.error(`FAILED ${err}`);
        process.exit(1);
    }
}

async function trackMutationCompletion(packageName, nodeName, mutationName, trackingId) {
    let count = 0;
    const getResult = async (resolve, reject) => {
        count++;
        if (count > 10) reject('Timeout');
        const result = await runQuery(
            'post',
            `query { ${packageName} { ${nodeName} { ${mutationName} {
                track(trackingId: "${trackingId}") {
                    status
                    result
                    errorMessage
                } 
             } } } }`,
        );
        const trackData = result.data[packageName][nodeName][mutationName].track;
        if (trackData.status === 'running') {
            console.warn(`\t\t${trackData.status} ... (${count} s)`);
            setTimeout(() => getResult(resolve, reject), 1000);
            return;
        }
        console.warn(`\t- exited with status '${trackData.status}', result=${trackData.result}`);
        resolve({ status: trackData.status, result: trackData.result });
    };

    return new Promise((resolve, reject) => {
        getResult(resolve, reject);
    });
}

async function invokeMutation(packageName, nodeName, mutationName, parameters) {
    console.warn(`Invoke mutation ${packageName}.${nodeName}.${mutationName} with parameters ${parameters}`);
    const parametersAsStr = parameters == null ? '' : `(${parameters})`;
    const result = await runQuery(
        'post',
        `mutation { ${packageName} { ${nodeName} { ${mutationName} {
            start${parametersAsStr} {
                trackingId
            }
         } } } }`,
    );
    const trackingId = result.data[packageName][nodeName][mutationName].start.trackingId;
    console.warn(
        `\t- mutation ${packageName}.${nodeName}.${mutationName} invoked, waiting for completion (trackingId:${trackingId})`,
    );
    try {
        return await trackMutationCompletion(packageName, nodeName, mutationName, trackingId);
    } catch (err) {
        console.log(err);
        process.exit(2);
    }
}

function showHelp() {
    console.error(`Invoke a graphQl async mutation on ${server}`);
    console.error('--------------------------');
    console.error('Usage: run-graphql-async-mutation <package> <node> <mutation> <parameters>');
    console.error('   - <package> : the name of the package that contains the node');
    console.error('   - <node> : the name of the node that contains the asyncMutation');
    console.error('   - <mutation> : the name of the async mutation to invoke');
    console.error('   - <parameters> : the (optional) parameters for the mutation');
    process.exit(1);
}

// Note: this script will use console.warn to log strings to the console but if we redirect the output to a file run.sh > out.log, they won't
// be written into the file.
// The only thing that will be written to stdout will be the result of a the execution of the async mutation (status + result)
(async () => {
    const [_, __, packageName, nodeName, mutationName, parameters] = process.argv;
    if (packageName === '--help' || packageName == null || nodeName == null || mutationName == null) showHelp();

    const mutationResult = await invokeMutation(packageName, nodeName, mutationName, parameters);
    console.log(JSON.stringify(mutationResult));
})();
