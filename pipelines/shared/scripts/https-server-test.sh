#!/usr/bin/env bash

EXIT_CODE=0

randomVal=$(uuidgen | cut -d'-' -f5)

XTREM_ROOT_DIR=${XTREM_ROOT_DIR:="$(git rev-parse --show-toplevel)"}
XTREM_MAIN_APP_DIR=${XTREM_MAIN_APP_DIR:="$XTREM_ROOT_DIR/services/main/xtrem-services-main"}

if [ "$XTREM_TEST_CA_PASS" == "" ]; then
    XTREM_TEST_CA_PASS="pass-$randomVal"
fi

# Exit immediately if a command exits with a non-zero status
set -e

sslDir=$XTREM_ROOT_DIR/ssl-$randomVal
mkdir -p "$sslDir"
echo ">>> Generate ssl certificates in $sslDir"
XTREM_TEST_CA_PASS=$XTREM_TEST_CA_PASS "$XTREM_ROOT_DIR"/scripts/ssl/gen-certs.sh "$sslDir"

backupFile=xtrem-config-$randomVal.yml.bak
cp "$XTREM_ROOT_DIR"/xtrem-config.yml "$XTREM_ROOT_DIR/$backupFile"

SED_OPT=(-i)
if [[ "$OSTYPE" == "darwin"* ]]; then
    SED_OPT=(-i '')
fi

sed s:{CERT_PATH}:"$sslDir"/server.crt:g "$XTREM_ROOT_DIR"/xtrem-config-https-server-fragment.yml >> "$XTREM_ROOT_DIR"/xtrem-config.yml
sed "${SED_OPT[@]}" s:{KEY_PATH}:"$sslDir"/server.key:g "$XTREM_ROOT_DIR"/xtrem-config.yml

# Start as background job
cd "${XTREM_MAIN_APP_DIR}"
./node_modules/.bin/xtrem start &

# Turn off exit immediately if a command exits with a non-zero status
set +e

# Wait the server to be ready
curl --cacert "$sslDir"/ca.crt --retry-connrefused --connect-timeout 5 --max-time 10 --retry 12 --retry-delay 0 \
    --retry-max-time 180 'https://localhost:8443/ready'

EXIT_CODE=$?

# Stop the xtrem service
kill -9 "$(jobs -p)"

# cleanup
cp "$XTREM_ROOT_DIR/$backupFile" "$XTREM_ROOT_DIR"/xtrem-config.yml
rm "$XTREM_ROOT_DIR/$backupFile"
rm -rf "$sslDir"

exit $EXIT_CODE
