#!/usr/bin/env bash

echo "##[command]$0"

set -e

cd "$XTREM_MAIN_APP_DIR"

tenantDemo='{"packages":["@sage/xtrem-intacct-gateway"],"customer":{"id":"00000000000000000001","name":"acme"},"tenant":{"id":"sH9h5OHURVu0yrpzp19Ur","name":"tenant_test_1"},"adminUsers":[{"email":"<EMAIL>","firstName":"<PERSON>","lastName":"Doe","locale":"en-US"},{"email":"<EMAIL>","firstName":"<PERSON>","lastName":"Star","locale":"en-US"}],"skipWelcomeEmail":true}'
tenantQa='{"customer":{"id":"00000000000000000001","name":"acme"}, "tenant":{"id":"OPmvzKZQX9N9kPIOivyrx","name":"tenant_qa_to_delete"},"adminUsers":[{"email":"<EMAIL>","firstName":"Joe","lastName":"Star","locale":"en-US"}],"skipWelcomeEmail":true}'
tenantTest='{"customer":{"id":"00000000000000000001","name":"acme"}, "tenant":{"id":"OPmvzKZQX9N9kPIOiTest","name":"tenant_test_to_delete"},"adminUsers":[{"email":"<EMAIL>","firstName":"Joe","lastName":"Star","locale":"en-US"},{"email":"<EMAIL>","firstName":"John","lastName":"Doe","locale":"en-US"}],"skipWelcomeEmail":true}'

echo "##[group]Init import/export tenants"
pnpm run xtrem tenant --layers=setup,demo --init "$(echo -n "$tenantDemo" | base64 -w 0)"
pnpm run xtrem tenant --layers=setup,qa --init "$(echo -n "$tenantQa" | base64 -w 0)"
pnpm run xtrem tenant --layers=setup,test --init "$(echo -n "$tenantTest" | base64 -w 0)"
echo "##[endgroup]"

tenantImport='{"adminUsers":[{"email":"<EMAIL>","firstName":"Test","lastName":"Test","locale":"en-US"},{"email":"<EMAIL>","firstName":"Joe","lastName":"Star","locale":"en-US"}],"customer":{"id":"aaaaaaaaaaaaaaaaaaaa","name":"SageCustomerTest"},"tenant":{"id":"111111111111111111111","name":"tenant-test-2"},"location":"./data/exports/tenant-test-1/test-export.zip","skipWelcomeEmail":true}'
tenantImportAnonymized='{"adminUsers":[{"email":"<EMAIL>","firstName":"Test","lastName":"Test","locale":"en-US"},{"email":"<EMAIL>","firstName":"Support","lastName":"Readonly","locale":"en-US"}],"customer":{"id":"aaaaaaaaaaaaaaaaaaaa","name":"SageCustomerTest"},"tenant":{"id":"222222222222222222222","name":"tenant-test-3"},"location":"./data/exports/tenant-test-to-delete/test-anonymized.zip","skipWelcomeEmail":true}'

echo "##[group]Export tenant sH9h5OHURVu0yrpzp19Ur"
if ! pnpm run xtrem tenant --export 'sH9h5OHURVu0yrpzp19Ur' --export-id 'test-export'; then
  echo 'Could not export tenant sH9h5OHURVu0yrpzp19Ur'
  exit 1
fi
echo "##[endgroup]"

echo "##[group]Export tenant OPmvzKZQX9N9kPIOiTest anonymized"
if ! pnpm run xtrem tenant --export 'OPmvzKZQX9N9kPIOiTest' --export-id 'test-anonymized' --anonymize; then
  echo 'Could not export tenant OPmvzKZQX9N9kPIOiTest anonymized'
  exit 1
fi
echo "##[endgroup]"

echo "##[group]Import anonymized tenant 222222222222222222222"
if ! pnpm run xtrem tenant --import "$(echo -n "$tenantImportAnonymized" | base64 -w 0)"; then
  echo "Could not import tenant $tenantImportAnonymized"
  exit 1
fi
echo "##[endgroup]"

# we should support the options --location ./data/exports/tenant-test-1/test-export.zip
echo "##[group]Import tenant 111111111111111111111"
if ! pnpm run xtrem tenant --import "$(echo -n "$tenantImport" | base64 -w 0)"; then
  echo "Could not import tenant $tenantImport"
  exit 1
fi
echo "##[endgroup]"

echo "##[group]Compute and check differences"

# the --diff-compliant option excludes _update_user, _create_user, _update_tick, _create_stamp _update_stamp and _vendor columns that may differ
if ! pnpm run xtrem tenant --export 'sH9h5OHURVu0yrpzp19Ur' --export-id 'test-export-cmp1' --diff-compliant; then
  echo 'Could not export tenant sH9h5OHURVu0yrpzp19Ur'
  exit 1
fi

if ! pnpm run xtrem tenant --export '111111111111111111111' --export-id 'test-export-cmp2' --diff-compliant; then
  echo 'Could not export tenant 111111111111111111111'
  exit 1
fi

echo "Expecting to have a line in user.csv for the user '<EMAIL>' "
grep "<EMAIL>;" data/exports/tenant-test-2/test-export-cmp2/user.csv

echo "Checking difference betwwen ...test-export-cmp1 and .../test-export-cmp2"
# excludes the metadata.json file from comparison
diff --exclude='*.json' --exclude='user*' --exclude='sys-job-schedule.csv' --exclude='sys-bundle-record*' data/exports/tenant-test-1/test-export-cmp1 data/exports/tenant-test-2/test-export-cmp2

# if we have a difference it might be expected because of the user id change
if ! diff data/exports/tenant-test-1/test-export-cmp1/sys-job-schedule* data/exports/tenant-test-2/test-export-cmp2/sys-job-schedule* > sys-job-schedule.diff ; then
  # get the 2 root user id that are created when we initialize the tenant
  rootUserId1=$(grep ";<EMAIL>;" data/exports/tenant-test-1/test-export-cmp1/user.csv | awk -F ";" '{print $1}')
  rootUserId2=$(grep ";<EMAIL>;" data/exports/tenant-test-2/test-export-cmp2/user.csv | awk -F ";" '{print $1}')

  grep "^<" sys-job-schedule.diff | cut -c 3- > sys-job-schedule-1.diff
  # replace the root user id in the difference, this is an expected difference
  grep "^>" sys-job-schedule.diff | cut -c 3- | awk -F";" '{OFS=FS} $3=='"$rootUserId2"' {$3="'"$rootUserId1"'"} {print}' > sys-job-schedule-2.diff

  diff sys-job-schedule-1.diff sys-job-schedule-2.diff
fi
rm -f sys-job-schedule*.diff

echo "##[endgroup]"

echo "##[group]Re-Import tenant 111111111111111111111 in an empty cluster"
pnpm run xtrem schema --create --reset-database

if ! pnpm run xtrem tenant --import "$(echo -n "$tenantImport" | base64 -w 0)"; then
  echo "Could not import in a empty cluster the tenant $tenantImport"
  exit 1
fi

echo "##[endgroup]"

tenantAddAdminUsers='{"adminUsers":[{"email":"<EMAIL>","firstName":"Talisha","lastName":"Taylor","locale":"en-US"},{"email":"<EMAIL>","firstName":"Sarah","lastName":"Scott","locale":"en-US"}],"customer":{"id":"aaaaaaaaaaaaaaaaaaaa","name":"SageCustomerTest"},"tenant":{"id":"111111111111111111111","name":"tenant-test-2"}}'

# we should support the options --update '{"adminUsers":"..."}'
echo "##[group]Update tenant 111111111111111111111 with new admin users"
if ! pnpm run xtrem tenant --update "$(echo -n "$tenantAddAdminUsers" | base64 -w 0)"; then
  echo "Could not update tenant $tenantImport"
  exit 1
fi


if ! pnpm run xtrem tenant --export '111111111111111111111' --export-id 'test-export-cmp3' --diff-compliant; then
  echo 'Could not export tenant 111111111111111111111'
  exit 1
fi

echo "Expecting to have a line in user.csv for the user '<EMAIL>' "
grep "<EMAIL>;" data/exports/tenant-test-2/test-export-cmp3/user.csv
echo "Expecting to have a line in user.csv for the user '<EMAIL>' "
grep "<EMAIL>;" data/exports/tenant-test-2/test-export-cmp3/user.csv

echo "##[endgroup]"
