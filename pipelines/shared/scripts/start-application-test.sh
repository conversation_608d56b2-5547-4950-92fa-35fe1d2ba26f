#!/usr/bin/env bash

EXIT_CODE=0

# Exit immediately if a command exits with a non-zero status
set -e

cd "$XTREM_MAIN_APP_DIR"
pwd

XTREM_ROOT_DIR=$(git rev-parse --show-toplevel)
XTREM_SCOPE_ROOT_DIR=$(dirname "$(dirname "$XTREM_MAIN_APP_DIR")")

if [ -f node_modules/.bin/xtrem ]; then
    XTREM=node_modules/.bin/xtrem
elif [ -f node_modules/@sage/xtrem-cli/bin/xtrem ]; then
    XTREM=node_modules/@sage/xtrem-cli/bin/xtrem
else
    echo -e "\033[31m❌\033[0m invalid package definition"
    echo "Please to use the default xtrem cli from the package, you may need to add in the dependencies:"
    echo "  \"@sage/xtrem-cli\": \"workspace:*\""
    echo "  \"@sage/xtrem-cli-cloud\": \"workspace:*\""
    echo "  \"@sage/xtrem-cli-main\": \"workspace:*\""
    exit 1
fi
echo "Use xtrem cli: $XTREM"

echo "##[group] Copy config $XTREM_SCOPE_ROOT_DIR/xtrem-config-azure.yml to $XTREM_SCOPE_ROOT_DIR/xtrem-config.yml"
cp "$XTREM_SCOPE_ROOT_DIR"/xtrem-config-azure.yml "$XTREM_SCOPE_ROOT_DIR"/xtrem-config.yml
echo "##[endgroup]"

if [ "$XTREM_RESET_POSTGRES" = 1 ]; then
    echo "##[group] Deploy database"
    "$XTREM_ROOT_DIR"/scripts/postgres/deploy-postgres.sh --skip-client-check
    echo "##[endgroup]"

    echo "##[group] Create schema"
    pnpm run xtrem schema --create --reset-database
    echo "##[endgroup]"

    echo "##[group] Load test data"
    pnpm run load:test:data
    echo "##[endgroup]"
fi

if [ "$XTREM_X3_CONFIG" = 1 ]; then
    echo "##[group] X3: Update config"
    # write x3-services/wh-services dummy SQL config
    printf "    database: sagex3\n    hostname: localhost\n    user: REPOSX3\n    password: tiger\n  development:\n    folderName: REPOSX3\n    referenceFolder: X3\n    defaultLanguage: FRA\n    soap:\n        webServiceURL: http://scmx3-dev-dis.sagefr.adinternal.com:8124/soap-generic/syracuse/collaboration/syracuse/CAdxWebServiceXmlCC\n        userCredentials:\n            userName: admin\n            password: admin\n        codeLang: ENG\n        poolAlias: WSP\n        timeout: 50000\n" >> $XTREM_SCOPE_ROOT_DIR/xtrem-config.yml
    echo "##[endgroup]"
fi

echo "##[group] Starting application from $XTREM_MAIN_APP_DIR"

# Start as background job. Do not use pnpm to ease killing job
$XTREM start &

# Turn off exit immediately if a command exits with a non-zero status
set +e

# Wait the server to be ready
curl --retry-connrefused --connect-timeout 5 --max-time 10 --retry 12 --retry-delay 0 \
    --retry-max-time 180 'http://localhost:8240/ready'

EXIT_CODE=$?

echo "##[endgroup]"


# Stop the xtrem service
echo "##[group] killing background job..."
kill -9 "$(jobs -p)"
echo "##[endgroup]"

echo "##[group] checking for running xtrem start process..."
ps -ax | grep "xtrem start" | grep -v "grep"
echo "##[endgroup]"

echo "##[group] delete config file from $XTREM_CONFIG_LOCATION ..."
rm $XTREM_SCOPE_ROOT_DIR/xtrem-config.yml
echo "##[endgroup]"

exit $EXIT_CODE
