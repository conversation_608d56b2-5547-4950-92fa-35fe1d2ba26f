#!/usr/bin/env bash

set -e

REPO_ROOT_PATH=$(git rev-parse --show-toplevel)
TOOLS_TMP_DIR="$REPO_ROOT_PATH"/tmp
mkdir -p "$TOOLS_TMP_DIR"

source ${REPO_ROOT_PATH}/scripts/docker/docker-image-helper.sh

# ${NODEVERSION} is the value of the $(nodeVersion) of azure variable
CURRENT_VERSION="$NODEVERSION"
if [ -n "$CURRENT_VERSION" ]; then
    NODE_VERSION=$CURRENT_VERSION
    echo "Set node version to '$NODE_VERSION' from current"
else
    if [ "$1" == "--s3-update" ]; then
        copyImageToS3 "${NODE_IMAGE}" --force
    else
        # to prevent rate limit on docker pull we use the image stored in s3
        LOCAL_NODE_IMAGE=$(docker images --quiet "$NODE_IMAGE")
        if [ -z "$LOCAL_NODE_IMAGE" ]; then
            copyImageToS3 "${NODE_IMAGE}"
            downloadAndLoadImage "${NODE_IMAGE}"
        fi
    fi

    echo "##[group]get node version from docker image $NODE_IMAGE"
    NODE_VERSION=$(docker run --rm "$NODE_IMAGE" node -v | sed 's/v//g')
    echo "##[endgroup]"
    echo "Set node version to '${NODE_VERSION}' from $NODE_IMAGE image"
fi
echo "##vso[task.setvariable variable=nodeVersion]$NODE_VERSION"

# convert to lower case
if [[ ${SKIP_INSTALL,,} == "true" ]]; then
    echo "skipping install"
    exit 0
fi

# create in the current source directory in a tmp folder so that it is excluded for git diff
NODE_NAME="node-v$NODE_VERSION-linux-x64"
NODE_TAR="$NODE_NAME.tar.gz"
NODE_BIN="$TOOLS_TMP_DIR/$NODE_NAME/bin"

if [[ -d $NODE_BIN ]]; then
    echo "##[group]list local node bin"
    ls -l "$NODE_BIN"
    echo "##[endgroup]"
fi

NODE_EXE=$NODE_BIN/node
# In CI on windows agent append ".exe" and replace the path separator on the bin for the env path
if [ "$AGENT_OS" == "Windows_NT" ]; then
    NODE_EXE=$NODE_EXE.exe
    NODE_BIN="$(echo "$NODE_BIN" | tr '\\' '/')"
fi

INSTALLED_VERSION=$(node -v | sed 's/v//g')
if [[ "$INSTALLED_VERSION" == "$NODE_VERSION" ]]; then
    echo "current global node version satisfies the expected version $NODE_VERSION"
    exit 0
fi

if [[ -f $NODE_EXE ]]; then
    INSTALLED_VERSION=$($NODE_EXE -v | sed 's/v//g')
    if [[ "$INSTALLED_VERSION" == "$NODE_VERSION" ]]; then
        echo "current local node version satisfies the expected version $NODE_VERSION"
        echo "Prepend node bin path: ${NODE_BIN}"
        echo "##vso[task.prependpath]${NODE_BIN}"
        exit 0
    fi
fi

echo "##[group]download and extract node version $NODE_VERSION"
echo "##[command]aws s3 cp s3://xtrem-developers-utility/tools/$NODE_TAR $TOOLS_TMP_DIR/"
set +e
aws s3 cp s3://xtrem-developers-utility/tools/"$NODE_TAR" $TOOLS_TMP_DIR/
EXITCODE=$?
set -e
if [ $EXITCODE -ne 0 ]; then
    echo "fallback to nodejs.org"

    if ! curl -s -o "$TOOLS_TMP_DIR/$NODE_TAR" https://nodejs.org/dist/v"$NODE_VERSION/$NODE_TAR"; then
        exit 1
    fi
    aws s3 cp "$TOOLS_TMP_DIR/$NODE_TAR" s3://xtrem-developers-utility/tools/"$NODE_TAR"
fi

echo "##[command]tar xzf $TOOLS_TMP_DIR/${NODE_TAR} --directory $TOOLS_TMP_DIR/"
tar xzf "$TOOLS_TMP_DIR/$NODE_TAR" --directory "$TOOLS_TMP_DIR"/
rm -f "$TOOLS_TMP_DIR/$NODE_TAR"
echo "##[endgroup]"

echo "Prepend node bin path: $NODE_BIN"
echo "##vso[task.prependpath]$NODE_BIN"
