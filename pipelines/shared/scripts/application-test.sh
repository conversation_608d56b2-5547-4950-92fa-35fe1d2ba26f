#!/usr/bin/env bash

EXIT_CODE=0
REPO_ROOT_PATH=$(git rev-parse --show-toplevel)

# Exit immediately if a command exits with a non-zero status
set -e
cd $XTREM_MAIN_APP_DIR

pwd
cat package.json

XTREM=./node_modules/.bin/xtrem

# should work with npm otherwise we are probably missing the xtrem script in package.json
npm run xtrem -- schema --create --reset-database
npm run xtrem -- layers --load=setup,test

# Start as background job. Do not use pnpm to ease killing job
"$XTREM" start &

# Turn off exit immediately if a command exits with a non-zero status
set +e

# Wait the server to be ready
curl --retry-connrefused --connect-timeout 5 --max-time 10 --retry 12 --retry-delay 0 \
    --retry-max-time 180 'http://localhost:8240/ready'

EXIT_CODE=$?

mkdir -p test/cucumber/
cp "${REPO_ROOT_PATH}"/platform/show-case/xtrem-show-case/test/cucumber/user.feature test/cucumber/

TARGET_URL=http://localhost:8240 "$XTREM" test --skip-server-compile --integration 'test/cucumber/user.feature'

# Stop the xtrem service
echo "killing background job..."
kill -9 "$(jobs -p)"

echo "checking for running xtrem start process..."
ps -ax | grep "xtrem start" | grep -v "grep"

exit $EXIT_CODE
