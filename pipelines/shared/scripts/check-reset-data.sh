#!/usr/bin/env bash

# Exit immediately if a command exits with a non-zero status
set -e
if [ -z "$1" ]; then
    echo "usage: $(basename "$0") <tenant-id>"
    exit 9
fi
sedi() {
    if [[ "$OSTYPE" == "darwin"* ]] && ! sed --version >/dev/null 2>&1; then
        sed -i '' "$@"
    else
        sed -i "$@"
    fi
}

XTREM_ROOT_DIR=${XTREM_ROOT_DIR:="$(git rev-parse --show-toplevel)"}

# Backup the xtrem-config.yml file
cp "$XTREM_ROOT_DIR/xtrem-config.yml" "$XTREM_ROOT_DIR/xtrem-config.yml.bak"

# replace the 'user: <EMAIL>' with 'user: <EMAIL>' to be allowed to reset the tenant data
sedi "s/user: <EMAIL>/user: <EMAIL>/" "$XTREM_ROOT_DIR/xtrem-config.yml"

tenantId=$1
jsonData='{"query":"mutation {xtremSystem {sysTenant {resetTenantDocuments(tenantId: \"'"$tenantId"'\")}}}","variables":null}'
echo -e "Sending GraphQL request to reset tenant data:\n$jsonData"
body=$(curl --silent 'http://localhost:8240/api' -H 'Content-Type: application/json' --data-raw "$jsonData")

echo -e "Response:\n$body"
result=$(echo "$body" | jq -r .data.xtremSystem.sysTenant.resetTenantDocuments)

# Restore the xtrem-config.yml file
mv "$XTREM_ROOT_DIR/xtrem-config.yml.bak" "$XTREM_ROOT_DIR/xtrem-config.yml"


if [ "$result" != "true" ]; then
    exit 1
fi
