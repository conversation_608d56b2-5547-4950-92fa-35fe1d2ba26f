#!/usr/bin/env bash

# Get an access token using the SageId client id and client secret passed as arguments
# If successful set the result into a variable named accessToken
# On error set the exitMessage to return an error to the pipeline

GET_TOKEN_PAYLOAD_FORMAT='{"grant_type":"client_credentials","client_id":"%s","client_secret":"%s","audience":"%s"}'
GET_TOKEN_PAYLOAD=$(printf "$GET_TOKEN_PAYLOAD_FORMAT" "$ENV_CLIENTID" "$ENV_CLIENTSECRET" "$ENV_AUDIENCE")

TOKEN_HTTP_RESPONSE=$(curl -sS -X POST -H "content-type:application/json" -d "${GET_TOKEN_PAYLOAD}" ${ENV_SAGEID_OAUTH_TOKEN_URL})
if [ $? -ne 0 ]; then 
  echo "Could not get an access token: result is $TOKEN_HTTP_RESPONSE" >&2
  exit 1
fi
echo $(echo "$TOKEN_HTTP_RESPONSE" | jq -r .access_token)
