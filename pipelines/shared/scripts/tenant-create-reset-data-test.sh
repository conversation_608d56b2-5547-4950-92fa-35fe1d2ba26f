#!/usr/bin/env bash

echo "##[command]$0"
echo "Can reset data of dev tenant?"
XTREM_ROOT_DIR=${XTREM_ROOT_DIR:="$(git rev-parse --show-toplevel)"}

# Exit immediately if a command exits with a non-zero status
set -e
cd "$XTREM_MAIN_APP_DIR"

# Creation is already done by the tenant-delete-create-test.sh script
echo "##[group]Load setup,demo layers"
pnpm run xtrem layers --load setup,demo --no-schema-reset
echo "##[endgroup]"

echo "##[group]Start server and wait to be ready"
# Start as background job without pnpm to be able to kill it later
./node_modules/.bin/xtrem start &


# Wait the server to be ready
if ! curl --retry-connrefused --connect-timeout 5 --max-time 10 --retry 12 --retry-delay 0 --retry-max-time 180 'http://localhost:8240/ready' ; then
    kill -9 "$(jobs -p)"
    exit 1
fi
echo "##[endgroup]"

# Wait the server to not be too busy
sleep 5

cd -

EXIT_CODE=0
echo "##[group]Request the tenant data reset"
"$(dirname "$0")"/check-reset-data.sh "777777777777777777777" || EXIT_CODE=$?
echo "##[endgroup]"

# Stop the xtrem service
kill -9 "$(jobs -p)"

exit $EXIT_CODE
