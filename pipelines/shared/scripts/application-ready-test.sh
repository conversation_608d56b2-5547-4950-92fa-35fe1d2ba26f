#!/usr/bin/env bash

EXIT_CODE=0

if [ "$(uname)" = "Darwin" ]; then
    PLATFORM="darwin"
elif [ "$(uname -s | cut -c 1-5)" = "Linux" ]; then
    PLATFORM="linux"
elif [ "$(uname -s | cut -c 1-5)" = "MINGW" ]; then
    PLATFORM="win32"
elif [ "$(uname -s | cut -c 1-10)" = "MINGW64_NT" ]; then
    PLATFORM="win32"
fi

echo "platform is $PLATFORM"
# This test is started from an artifact folder created by the windows zip delivery pipeline, we have to start from that folder
cd "$XTREM_MAIN_APP_DIR"

if [ -n "$XTREM" ]; then
    echo -e "\033[31m❌\033[0m XTREM env var is set to $XTREM"
    echo "This is probably not what you want."
    echo "Please unset XTREM to use the default xtrem cli from the package and fix the package dependencies if needed."
    echo "You may need to add in the dependencies:"
    echo "  \"@sage/xtrem-cli\": \"workspace:*\""
    echo "  \"@sage/xtrem-cli-cloud\": \"workspace:*\""
    echo "  \"@sage/xtrem-cli-main\": \"workspace:*\""
    exit 1
fi

if [ -f node_modules/.bin/xtrem ]; then
    XTREM=node_modules/.bin/xtrem
else
    XTREM=node_modules/@sage/xtrem-cli/bin/xtrem
fi

# Exit immediately if a command exits with a non-zero status
set -e

# Start as background job. Do not use pnpm to ease killing job
if [ "$PLATFORM" == "win32" ]; then
    nodejs/win32-x64/node "$XTREM" start &
else
    "$XTREM" start &
fi

# Turn off exit immediately if a command exits with a non-zero status
set +e

# Wait the server to be ready
curl --retry-connrefused --connect-timeout 5 --max-time 10 --retry 12 --retry-delay 0 \
    --retry-max-time 180 'http://localhost:8240/ready'

EXIT_CODE=$?

echo "list of running process..."
ps -a | grep -v "grep"

# Stop the xtrem service
echo "killing background job..."
if [ "$PLATFORM" == "win32" ]; then
    PID=$(ps -a | grep "/node" | grep -v "grep" | awk '{ print $1 }')
else
    PID=$(jobs -p)
fi
if [[ -z $PID ]]; then
    echo "server did not start correctly"
    exit 1
fi
echo "  Killing PID $PID"
kill -9 "$PID"
sleep 1

echo "checking for running xtrem start process..."
if [ "$PLATFORM" == "win32" ]; then
    ps -a | grep "/node" | grep -v "grep"
else
    ps -ax | grep "xtrem start" | grep -v "grep"
fi

exit $EXIT_CODE
