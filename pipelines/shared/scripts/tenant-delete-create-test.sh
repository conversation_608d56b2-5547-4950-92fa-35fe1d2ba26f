#!/usr/bin/env bash

echo "##[command]$0"

set -e
cd "$XTREM_MAIN_APP_DIR"

echo "##[group]Delete non existent tenant"
pnpm run xtrem tenant --delete "does-not-exist"
echo "##[endgroup]"

echo "##[group]Delete tenant OPmvzKZQX9N9kPIOivyrx"
pnpm run xtrem tenant --delete "OPmvzKZQX9N9kPIOivyrx"
echo "##[endgroup]"

echo "##[group]Checking that there are no rows left..."
pnpm run xtrem tenant --export 'OPmvzKZQX9N9kPIOivyrx' | grep "no tenants found to export."
echo "##[endgroup]"

# Note: even if 2 more tenants were created in the previous steps,
# sH9h5OHURVu0yrpzp19Ur + 111111111111111111111
# we assume that testing the deletion on only 1 tenant is enough

cd -
