#!/usr/bin/env bash

echo "##[command]$0"

set -e
echo "Schema='${XTREM_SCHEMA_NAME}'"
if [ "$XTREM_SCHEMA_NAME" = "xtrem" ]; then
  appPrefix="";
else
  appPrefix="${XTREM_SCHEMA_NAME}--";
  echo "Note: according to the schema, '${appPrefix}' will be used as a prefix"
fi

cd "$XTREM_MAIN_APP_DIR"

referenceDemo='{"customer":{"id":"00000000000000000001","name":"acme"}, "tenant":{"id":"6OtF7mkj8ca_NrCXj9eW9","name":"reference_demo"},"adminUsers":[{"email":"<EMAIL>","firstName":"Joe","lastName":"Star","locale":"en-US"}],"skipWelcomeEmail":true}'
referencePharma='{"customer":{"id":"00000000000000000001","name":"acme"}, "tenant":{"id":"N7mLPUT7RpI1NAasvupS2","name":"reference_pharma"},"adminUsers":[{"email":"<EMAIL>","firstName":"Joe","lastName":"Star","locale":"en-US"}],"skipWelcomeEmail":true}'

echo "##[group]Init reference tenants"
pnpm run xtrem tenant --layers=setup,demo --init "$(echo -n "$referenceDemo" | base64 -w 0)"
pnpm run xtrem tenant --layers=setup,demo --init "$(echo -n "$referencePharma" | base64 -w 0)"
echo "##[endgroup]"

declare -a tenants=("demo" "pharma")

index=0;
echo "##[group]Clean-up folders"
for name in "${tenants[@]}"; do
  echo "Clean-up $name"
  rm -rf  "./data/exports/reference-$name"
  rm -rf "./data/exports/reference-$name-cmp1/"
  for file in ./data/exports/reference-"$name"/reference-"$name"-*.zip; do
    tenantId="reference-$name-$index";
    rm -rf "./data/exports/$tenantId"
    index=$((index+1));
  done
done
echo "##[endgroup]"

echo "##[group]Export reference- and reference-pharma using the pattern reference-*"
# the --diff-compliant option excludes _update_user, _create_user, _update_tick, _create_stamp _update_stamp and _vendor columns that may differ
pnpm run xtrem tenant --export 'reference-*' --diff-compliant --no-timestamp
echo "##[endgroup]"

# ensure <EMAIL> is present in the user.csv
find ./data/exports -name 'user.csv' -exec grep -q '<EMAIL>;' {} \;

for name in "${tenants[@]}"; do
  mv ./data/exports/reference-"$name" ./data/exports/reference-"$name"-cmp1
done

echo "##[group]Export files to be used by the import (all columns are needed here: no --diff-compliant option! )"
pnpm run xtrem tenant --export 'reference-*' --no-timestamp
echo "##[endgroup]"

index=0;
# For reference-demo and reference-pharma:
for name in "${tenants[@]}"; do
  echo "Import the tenant using files generated by export 'reference-$name-*'"
  for file in "./data/exports/reference-$name/${appPrefix}reference-$name"-*.zip; do
    tenantId="reference-$name-$index";
    filename=${file##*/}
    dirname=${filename%.*}

    echo "Import file'$file' for tenant '$tenantId'"

    echo "##[group]Import the tenant using files generated by export 'reference-*'"
    tenantB64=$(echo -n '{"adminUsers":[{"email":"<EMAIL>","firstName":"Test","lastName":"Test","locale":"en-US"}],"customer":{"id":"aaaaaaaaaaaaaaaaaaaa","name":"SageCustomerTest"},"tenant":{"id":"'"$tenantId"'","name":"'"$tenantId"'"},"location":"'"$file"'","skipWelcomeEmail":true}' | base64 -w 0)
    pnpm run xtrem tenant --import "$tenantB64"
    echo "##[endgroup]"

    echo "##[group]Export tenant $tenantId"
    pnpm run xtrem tenant --export "$tenantId" --export-id "$name-pattern-cmp" --diff-compliant --no-timestamp
    echo "##[endgroup]"

    echo "##[group]Check tenant $tenantId"
    echo "Expecting to have a line in user.csv for the user '<EMAIL>' "
    grep "<EMAIL>;" "data/exports/$tenantId/$name-pattern-cmp/user.csv"
    echo "Expecting to have a line in user.csv for the user '<EMAIL>' "
    grep "<EMAIL>;" "data/exports/$tenantId/$name-pattern-cmp/user.csv"

    echo "Checking difference between .../reference-$name-cmp1/$dirname and .../$tenantId/$name-pattern-cmp"
    # excludes the metadata.json file from comparison
    cmp1Dir="data/exports/reference-$name-cmp1/$dirname"
    cmp2Dir="data/exports/$tenantId/$name-pattern-cmp"
    diff --exclude='*.json' --exclude='user*' --exclude='sys-job-schedule.csv' "$cmp1Dir" "$cmp2Dir"

    # if we have a difference it might be expected because of the user id change
    if ! diff "$cmp1Dir"/sys-job-schedule* "$cmp2Dir"/sys-job-schedule* > sys-job-schedule.diff ; then
      # get the 2 root user id that are created when we initialize the tenant
      rootUserId1=$(grep ";<EMAIL>;" "$cmp1Dir"/user.csv | awk -F ";" '{print $1}')
      rootUserId2=$(grep ";<EMAIL>;" "$cmp2Dir"/user.csv | awk -F ";" '{print $1}')
      grep "^<" sys-job-schedule.diff | cut -c 3- > sys-job-schedule-1.diff
      # replace the root user id in the difference, this is an expected difference
      grep "^>" sys-job-schedule.diff | cut -c 3- | awk -F";" '{OFS=FS} $3=='"$rootUserId2"' {$3="'"$rootUserId1"'"} {print}' > sys-job-schedule-2.diff

      diff sys-job-schedule-1.diff sys-job-schedule-2.diff
    fi
    rm -f sys-job-schedule*.diff

    echo "##[endgroup]"

    index=$((index+1));
  done
done

cd -
