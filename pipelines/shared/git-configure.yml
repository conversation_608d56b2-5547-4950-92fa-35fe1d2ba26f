# This template prepare git configuration for manual cloning

steps:
  # use the inline script because we haven't got the source code from git yet
  - bash: |
      git config --global user.name 'Sage X3 CI'
      git config --global user.email '<EMAIL>'
      b64Token=$(echo -n ${GITHUB_PASSWORD} | base64 -w 0)
      git config --global --add http.https://$(github_username)@github.com/Sage-ERP-X3/xtrem.git.extraheader \
        "AUTHORIZATION: basic ${b64Token}"
    env:
      GITHUB_PASSWORD: $(github_password)
    displayName: 'Git - Configuration'
