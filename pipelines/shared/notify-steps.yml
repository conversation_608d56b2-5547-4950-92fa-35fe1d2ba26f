parameters:
  - name: incomingWebhookUrl
    type: string
    displayName: 'incomingWebhookUrl'
    default: 'none'

steps:
  - bash: |
      echo '##vso[task.setvariable variable=exitMessage;]Build succeeded'
      echo '##vso[task.setvariable variable=themeColor;]00ff00'
    condition: succeeded()

  - bash: |
      echo '##vso[task.setvariable variable=exitMessage;]Build failed'
      echo '##vso[task.setvariable variable=themeColor;]ff0000'
    condition: failed()

  - task: PostToOffice365Connector@0
    condition: not(eq(variables.disableNotify, True))
    continueOnError: true
    inputs:
      url: '${{parameters.incomingWebhookUrl}}'
      title: '$(exitTitle) $(imageName):$(tagName)'
      msg: '
      $(exitMessage)

      * [View build]($(System.CollectionUri)$(System.TeamProjectId)/\_build/results?buildId=$(Build.BuildId)&view=results)'
      themeColor: $(themeColor)
