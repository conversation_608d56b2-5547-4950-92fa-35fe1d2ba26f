steps:
  # Pretiffy all modified package.json and amend last commit to not pollute logs
  # use xargs -r option to not run prettier if there no files to prettify
  - bash: |
      git log origin..HEAD --name-only --oneline | tail -n +2 \
        | grep "\.json$" \
        | grep -v "/empty-package/package\.json" \
        | xargs -r pnpm run prettier --write
      # exit code 0 means nothing to commit
      if git diff --exit-code; then
        echo "nothing to commit"
      else
        echo "amend last commit"
        git commit -a --amend --no-edit --no-verify
      fi
    displayName: 'Prettify package.json files'
