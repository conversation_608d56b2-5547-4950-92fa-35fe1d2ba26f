parameters:
  - name: skipInstall
    default: false
    type: boolean

  - name: updateS3Images
    displayName: 'Update S3 images'
    default: false
    type: boolean

steps:
  # install node using archives stored on aws to avoid issue with access to the nodejs.org server
  - task: AmazonWebServices.aws-vsts-tools.AWSShellScript.AWSShellScript@1
    inputs:
      awsCredentials: 'aws-s3'
      regionName: 'eu-west-1'
      scriptType: 'inline'
      inlineScript: |
        NODE_PARTIAL_VER=$(cat .nvmrc | awk -F '.' '{ print $1"."$2 }')

        export NODE_IMAGE="node:${NODE_PARTIAL_VER}-alpine"
        export SKIP_INSTALL="${{ parameters.skipInstall }}"

        if [ "${{ parameters.updateS3Images }}" == "True" ]; then
          INSTALL_OPT="--s3-update"
        fi
        pipelines/shared/scripts/install-node.sh $INSTALL_OPT
      logRequest: true
      logResponse: true
    displayName: 'Install node'
    name: installNode
    condition: and(succeeded(), not(eq(variables['doesNotAffectBuild'], true)))

  - ${{ if not(eq(parameters.skipInstall, true)) }}:
      - bash: |
          NODEPATH=$(which node)
          echo -e "\nnode $(node -v) npm $(npm -v) from $(dirname $NODEPATH)\n"

          echo "##[group] System CPU info"
          lscpu
          echo "##[endgroup]"

          echo "##[group] System Memory info"
          free -m
          echo "##[endgroup]"

          echo "##[group] node.js memory info"
          node -p "v8.getHeapStatistics()"
          echo "##[endgroup]"

          echo "##[group] linux version"
          echo "##[command]lsb_release -a"
          lsb_release -a
          echo "##[endgroup]"
        displayName: 'Node and sys info'
        name: showNodeAndSysInfo
        condition: and(succeeded(), not(eq(variables['doesNotAffectBuild'], true)))
