parameters:
  - name: pipelineName
    type: string
    default: 'xtrem-patch-release'

steps:
  - bash: |
      echo '##vso[task.setvariable variable=exitTitle;]Successfully generated'
      echo '##vso[task.setvariable variable=themeColor;]00ff00'
    condition: succeeded()

  - bash: |
      echo '##vso[task.setvariable variable=exitTitle;]Failed to generate'
      echo '##vso[task.setvariable variable=themeColor;]ff0000'
    condition: failed()


  - task: PostToOffice365Connector@0
    displayName: 'Notify ADC CI channel'
    continueOnError: true
    inputs:
      url: '$(adc_ci_support_channel)'
      title: '$(exitTitle) ${{parameters.pipelineName}}'
      msg: '
        * started for/by $(Build.Reason)/$(Build.QueuedBy)

        * [View build]($(System.CollectionUri)$(System.TeamProjectId)/_build/results?buildId=$(Build.BuildId)&view=logs)
      '
      themeColor: $(themeColor)
