parameters:
  - name: allowAnyBranch
    displayName: 'Should it allow any branch name. Default is to allow only "master" and "release/*"'
    type: boolean
    default: false

steps:
  - bash: |
      XTREM_BRANCH="$(BUILD.SOURCEBRANCHNAME)"
      echo "Source branch: $(BUILD.SOURCEBRANCH)" 
      if [ "$(BUILD.SOURCEBRANCHNAME)" != "master" ]; then
        BRANCH_STREAM=$(echo $(BUILD.SOURCEBRANCH) | awk -F / '{print $3}')
        echo stream is ${BRANCH_STREAM}             
        echo "allowAnyBranch: ${{ parameters.allowAnyBranch }}"
        if [ "${BRANCH_STREAM}" == "release" ] || [ "${{ parameters.allowAnyBranch }}" == "True" ]; then
          XTREM_BRANCH=$(echo $(BUILD.SOURCEBRANCH) | sed 's/refs\/heads\///')
        else
          echo "##vso[task.logissue type=error]bad branch name: only master and release/x.y can be used to generate images"
          exit 1
        fi
      fi
      echo branch is ${XTREM_BRANCH}
      echo "##vso[task.setvariable variable=xtremBranch]${XTREM_BRANCH}"
    displayName: Set Xtrem branch name
    name: setBranchName
