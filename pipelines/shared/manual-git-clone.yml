# This template performs a optimized manual git clone of the xtrem repo
# callers must have a variables section with:
# variables
#   - group: sagex3ci_github
# moreover, the caller should embed a checkout:none declaration

parameters:
  - name: depth
    type: number
    default: 100000
    displayName: 'How many commits to fetch ?'
  - name: blobless
    type: boolean
    default: false
    displayName: 'Clone with --filter=blob:none'

steps:
  # Prepare git configuration for manual cloning
  - template: ./git-configure.yml

  - ${{ if eq(parameters.blobless,true) }}:
      # Run a blobless clone to speed up perf and reduce size without loosing git history
      # see https://github.blog/2020-12-21-get-up-to-speed-with-partial-clone-and-shallow-clone/
      - bash: |
          PR_SOURCEBRANCH=${SYSTEM_PULLREQUEST_SOURCEBRANCH}
          PR_TARGETBRANCH=${SYSTEM_PULLREQUEST_TARGETBRANCH}
          PR_NUMBER=${SYSTEM_PULLREQUEST_PULLREQUESTNUMBER}

          git clone --filter=blob:none https://$(github_username)@github.com/Sage-ERP-X3/xtrem.git ./

          if [ -z "${PR_SOURCEBRANCH}" ]; then
            XTREM_BRANCH=$(echo ${BUILD_SOURCEBRANCH} | sed 's/refs\/heads\///')
            echo "Checkout ${XTREM_BRANCH}"
            git checkout ${XTREM_BRANCH}
          else
            echo "Checkout pull/${PR_NUMBER}/merge from ${PR_SOURCEBRANCH}"
            git fetch origin \
              +refs/pull/${PR_NUMBER}/merge:refs/remotes/pull/${PR_NUMBER}/merge \
              +"${PR_TARGETBRANCH}" +"${PR_SOURCEBRANCH}"
            git checkout pull/${PR_NUMBER}/merge
          fi
          echo "Last commit is : "
          git log -1
        displayName: 'Git - blobless clone'

  - ${{ else }}:
      # Run the same commands as the automatic Azure "Checkout" step
      # except that we don't checkout the tags and with a limited history of commits
      # use bash to be compatible with windows job (xtrem-x3-image.yml)
      - bash: |
          PR_SOURCEBRANCH=${SYSTEM_PULLREQUEST_SOURCEBRANCH}
          PR_TARGETBRANCH=${SYSTEM_PULLREQUEST_TARGETBRANCH}
          PR_NUMBER=${SYSTEM_PULLREQUEST_PULLREQUESTNUMBER}

          git init
          git remote add origin https://$(github_username)@github.com/Sage-ERP-X3/xtrem.git

          if [ -z "${PR_SOURCEBRANCH}" ]; then
            XTREM_BRANCH=$(echo ${BUILD_SOURCEBRANCH} | sed 's/refs\/heads\///')
            echo "Cloning repo on ${XTREM_BRANCH}"
            echo "##vso[task.logissue type=warning]Only ${XTREM_BRANCH} was cloned - this pipeline can only be used to compute the code coverage"
            git fetch --depth ${{ parameters.depth }} --no-tags --force --prune --progress --no-recurse-submodules origin +${XTREM_BRANCH}
            git checkout ${XTREM_BRANCH}
          else
            echo "Cloning repo for PR ${PR_NUMBER} on ${PR_SOURCEBRANCH}"
            git fetch --depth ${{ parameters.depth }} --no-tags --force --prune --progress --no-recurse-submodules origin \
              +refs/pull/${PR_NUMBER}/merge:refs/remotes/pull/${PR_NUMBER}/merge \
              +"${PR_TARGETBRANCH}" +"${PR_SOURCEBRANCH}"
            git checkout pull/${PR_NUMBER}/merge
          fi
          echo "Last commit is : "
          git log -1
        displayName: 'Git - Clone and checkout (depth=${{ parameters.depth }})'
