parameters:
  - name: nodeImage
    default: 'node:18.17-alpine'
    type: string
  - name: specificVersion
    default: ''
    type: string
  - name: skipInstall
    default: false
    type: boolean

steps:
  - bash: |
      # use the syntaxe ${NODEVERSION} and not $(nodeVersion) to avoid the error "nodeVersion: command not found"
      CURRENT_VERSION=${NODEVERSION}
      if [ -n "$SPECIFIC_VERSION" ]; then
        NODE_VERSION=$SPECIFIC_VERSION
        echo "Set node version to '${NODE_VERSION}' from parameters"
      elif [ -n "$CURRENT_VERSION" ]; then
        NODE_VERSION=$CURRENT_VERSION
        echo "Set node version to '${NODE_VERSION}' from current"
      else
        NODE_VERSION=$(docker run --rm --pull always ${{ parameters.nodeImage }} node -v | sed 's/v//g')
        echo "Set node version to '${NODE_VERSION}' from ${{ parameters.nodeImage }} image"
      fi
      echo "##vso[task.setvariable variable=nodeVersion]${NODE_VERSION}"
    displayName: 'Set node version'
    name: setNodeVersion
    condition: and(succeeded(), not(eq(variables['doesNotAffectBuild'], true)))
    env:
      SPECIFIC_VERSION: ${{ parameters.specificVersion }}

  - ${{ if not(eq(parameters.skipInstall, true)) }}:
      - task: NodeTool@0
        inputs:
          versionSpec: $(nodeVersion)
        displayName: 'Install Node.js'
        condition: and(succeeded(), not(eq(variables['doesNotAffectBuild'], true)))

      - bash: |
          echo "node $(node -v) npm $(npm -v) from $(dirname $(which node))"
          echo "##[group] npm version $(npm -v)"
          npm version
          echo "##[endgroup]"
        displayName: 'node and npm info'
        name: setNpmV6
        condition: and(succeeded(), not(eq(variables['doesNotAffectBuild'], true)))
