# Npm tasks are no longer maintaned and it is recommended to use the NpmAuthenticate task instead
# see https://docs.microsoft.com/en-us/azure/devops/pipelines/tasks/package/npm?view=azure-devops

parameters:
  - name: poolName
    displayName: 'pool to use'
    type: string
    values:
      - 'x3-ubuntu'
      - 'ubuntu-latest'
    default: 'x3-ubuntu'

  - name: imagePipelinesDef
    displayName: 'Docker Image pipelines definition'
    type: object
    default: []

  - name: bumpKind
    displayName: 'Version bump kind'
    type: string
    default: 'major'
    values:
      - patch
      - major

  - name: dryRun
    displayName: 'DRY RUN: no publish, no push'
    type: boolean
    default: false

  - name: skipUpgrade
    displayName: 'Skip upgrade'
    type: boolean
    default: false

  - name: skipS3
    # This variable will skip all the 'S3' related tasks/scripts
    displayName: 'Skip publication to S3 bucket (WARNING: dry run !!)'
    type: boolean
    default: false

  - name: pipelineId
    type: number
    default: 0
  - name: pipelineName
    type: string
    default: ''

  - name: useNpmProxy
    displayName: 'Use nexus npm proxy'
    default: false
    type: boolean

  - name: scopes
    type: object
    default:
      #========================================
      # scope - services
      #========================================
      - name: 'services'
        applicationDisplayName: 'sdmo'
        applicationRootFolder: 'services/main/xtrem-services-main'
        sqlFilesTest:
          enabled: true
          skipTenantTests: false
          backupTypes:
            - name: '--s3ConfigType=sdmo_cu'
              appName: 'sdmo'
            - name: '--s3ConfigType=sdmo'
              appName: 'sdmo'
              skipTenantTests: true
      #========================================
      # scope - platform/showcase
      #========================================
      - name: 'platform'
        applicationDisplayName: 'showcase'
        applicationRootFolder: 'platform/show-case/xtrem-show-case'
        sqlFilesTest:
          enabled: true
          skipTenantTests: true
          backupTypes:
            - name: '--s3ConfigType=showcase'
              appName: 'showcase'
      #========================================
      # scope - tools (glossary)
      #========================================
      - name: 'tools'
        applicationDisplayName: 'glossary'
        applicationRootFolder: 'tools/glossary/xtrem-glossary'
        sqlFilesTest:
          enabled: true
          skipTenantTests: true
          backupTypes:
            - name: '--s3ConfigType=glossary'
              appName: 'glossary'
      #========================================
      # scope - shopfloor
      #========================================
      - name: 'shopfloor'
        applicationDisplayName: 'shopfloor'
        applicationRootFolder: 'shopfloor/main/shopfloor-main'
        sqlFilesTest:
          enabled: true
          skipTenantTests: true
          backupTypes:
            - name: '--s3ConfigType=shopfloor'
              appName: 'shopfloor'
      #========================================
      # scope - x3-connector
      #========================================
      - name: 'x3-connector'
        applicationDisplayName: 'x3 connector'
        applicationRootFolder: 'x3-connector/main/x3-connector-main'
        sqlFilesTest:
          enabled: true
          skipTenantTests: true
          backupTypes:
            - name: '--s3ConfigType=x3_connector'
              appName: 'x3_connector'

jobs:
  - job: Release
    displayName: 'Release ${{ parameters.bumpKind }} version'
    timeoutInMinutes: 150

    pool:
      ${{ if eq(parameters.poolName, 'x3-ubuntu') }}:
        name: 'x3-ubuntu'
      ${{ else }}:
        vmImage: ${{ parameters.poolName }}

    variables:
      # uncomment this variable to temporarily enable pipeline diagnosis
      # - name: System.Debug
      #   value: true
      # variable group 'dockerXtrem' is defined at project level for all pipelines
      # see: https://sage-liveservices.visualstudio.com/X3%20XTREM/_library?itemType=VariableGroups&view=VariableGroupView&variableGroupId=149&path=dockerXtrem
      # contains:
      # - s3-xtrem-developers-utility-access-key
      # - s3-xtrem-developers-utility-secret
      - group: dockerXtrem
      - group: sagex3ci_github
      - group: ms_teams
      - group: azure_build
      - name: xtremBranch
        # Note: xtremBranch is set by the branch-name.yml step
      - name: pipelineId
        value: ${{ parameters.pipelineId }}
      - name: pipelineName
        value: ${{ parameters.pipelineName }}
      - name: pipelineContext
        value: xtrem-release-guard
      - name: prStatus
        value: 'none'
      - name: bumpKind
        value: ${{ parameters.bumpKind }}
      - name: versionExtraOpts
        ${{ if eq(parameters.bumpKind, 'major') }}:
          value: '--force-publish'
      # the project scopes for this stage: '*' means all projects are considered ('services', 'shopfloor', ...)
      - name: stage_scopes
        value: '*'

    steps:
      - checkout: none

      - bash: |
          # the build process needs S3 write credentials to s3://xtrem-developers-utility/builds/ to fetch (and write builds)
          echo "##vso[task.setvariable variable=S3_XTREM_DEVELOPERS_UTILITY_ACCESS_KEY;]$(s3-xtrem-developers-utility-access-key)"
          echo "##vso[task.setvariable variable=S3_XTREM_DEVELOPERS_UTILITY_SECRET;]$(s3-xtrem-developers-utility-secret)"

          if [ "${{ parameters.dryRun }}" == "True" ]; then
            echo "##vso[task.logissue type=warning]'dryRun' flag was set: nothing will be published or pushed"
            DRY_RUN_OPT="--dry-run"
          else
            DRY_RUN_OPT=""
          fi
          if [ "${{ parameters.skipUpgrade }}" == "True" ]; then
            echo "##vso[task.logissue type=warning]'skipUpgrade' flag was set: upgrade test will not be performed"
          fi
          if [ "${{ parameters.skipS3 }}" == "True" ]; then
            echo "##vso[task.logissue type=warning]'skipS3' flag was set: nothing will be published to the S3 bucket"
          fi

          echo "pipelineId: $(pipelineId)"
          echo "DRY_RUN_OPT: $DRY_RUN_OPT"
          echo "##vso[task.setvariable variable=DRY_RUN_OPT]$DRY_RUN_OPT"

          PIPELINE_NAME=$(az pipelines show --id $(pipelineId) \
            --organization $(System.CollectionUri) \
            --project "$(System.TeamProject)" \
            --query name \
            --output tsv)

          echo "pipelineName: $PIPELINE_NAME"
          echo "##vso[task.setvariable variable=pipelineName]$PIPELINE_NAME"

        displayName: Set env variables
        env:
          # the PAT to use with the az cli
          AZURE_DEVOPS_EXT_PAT: $(System.AccessToken)

      - template: ./branch-name.yml
        parameters:
          allowAnyBranch: ${{ parameters.dryRun }}

      - template: ./manual-git-clone.yml
        parameters:
          # Blobless clone are good for dev and CI for fast and small sized clone
          # see https://github.blog/2020-12-21-get-up-to-speed-with-partial-clone-and-shallow-clone/
          blobless: true

      - template: ./install.yml
        parameters:
          updateS3Images: true
          withPrLock: ${{ not(parameters.dryRun) }}
          useNpmProxy: ${{ parameters.useNpmProxy }}

      # Create release branch for major bump
      - bash: |
          set -e
          RELEASE_VERSION=$(grep version package.json \
                          | head -1 \
                          | awk -F: '{ print $2 }' \
                          | sed 's/[", ]//g' \
                          | awk -F. '{ print $1 }')
          RELEASE_BRANCH="release/${RELEASE_VERSION}.0"
          echo "Create branch ${RELEASE_BRANCH} from $(xtremBranch) at commit $(git rev-parse HEAD)"
          git checkout -b ${RELEASE_BRANCH}
          pnpm run release:packages
          pnpm run prettify:modified:json
          git add -A
          git commit -m "chore: update isReleased in package.json files" --no-verify
          if [[ -z $DRY_RUN_OPT ]]; then
            git push --set-upstream origin ${RELEASE_BRANCH}
          else
            echo "##vso[task.logissue type=warning]DRY_RUN_OPT is set, so the branch will not be pushed to remote"
          fi
          git checkout $(xtremBranch)
          git pull
        displayName: 'Create release branch'
        condition: and(succeeded(), eq(variables.bumpKind, 'major'), or(eq(variables.xtremBranch, 'master'), eq(${{ parameters.dryRun }}, true)))

      - bash: |
          set -e
          # Bump versions
          pnpm --verbose run prepare:release:version ${{ parameters.bumpKind }}
          # prettify modified files and amend commit to not pollute the git logs
          git diff HEAD^ --stat --name-only | grep "\.json$" | grep -v "empty-package" | xargs pnpm run prettier --write
          git commit -a --amend --no-edit --allow-empty
          # Create required SQL files (for upgrade - files will only be created on 'major' releases, not on 'patch' releases)
          pnpm prepare:release:sql:files ${{ parameters.bumpKind }} 'xtrem-services-main' 'services/main/xtrem-services-main/sql'
          pnpm prepare:release:sql:files ${{ parameters.bumpKind }} 'xtrem-glossary' 'tools/glossary/xtrem-glossary/sql'
          pnpm prepare:release:sql:files ${{ parameters.bumpKind }} 'xtrem-show-case' 'platform/show-case/xtrem-show-case/sql'
          pnpm prepare:release:sql:files ${{ parameters.bumpKind }} 'shopfloor-main' 'shopfloor/main/shopfloor-main/sql'
          pnpm prepare:release:sql:files ${{ parameters.bumpKind }} 'x3-connector-main' 'x3-connector/main/x3-connector-main/sql'
          git push origin $(xtremBranch)

        displayName: 'Bump version (${{ parameters.bumpKind }}) and push'

      - bash: |
          set -e
          # Generate changelog
          pnpm --verbose run generate:csv-changelog
        displayName: 'Generate updated sys-changelog.csv'

      - bash: |
          pnpm run build:binary
        displayName: 'PNPM - Build Binary xtrem'
        env:
          XTREM_OLD_BUILD_CACHE: 1

      - script: |
          git reset --hard
        displayName: Git - Clean modified files

        # Copy config file and replace tags to set the S3 configuration keys
      - script: |
          set -e
          sed s:\\[S3_XTREM_DEVELOPERS_UTILITY_ACCESS_KEY\\]:$(s3-xtrem-developers-utility-access-key):g pipelines/release-patch/xtrem-config-for-patch-release.yml > xtrem-config.yml
          sed -i s:\\[S3_XTREM_DEVELOPERS_UTILITY_SECRET\\]:$(s3-xtrem-developers-utility-secret):g xtrem-config.yml
        displayName: Initialize configuration file

      - ${{ if not(parameters.skipUpgrade) }}:
          - template: ./generate-all-upgrade-sql-files.yml
            parameters:
              scopes: ${{ parameters.scopes }}
      - ${{ if and( not(parameters.skipUpgrade), eq(variables['Build.SourceBranchName'], 'master') ) }}:
          - template: ./test-upgrades.yml
            parameters:
              scopes: ${{ parameters.scopes }}

      # Commit new SQL files
      # commit must be done before publishing to avoid "ERR! EUNCOMMIT" errors
      - bash: |
          COUNT=`git status --porcelain | wc -l`
          if [ $COUNT -gt 0 ]; then
            git add -A
            git commit -m "chore: commit upgrades" --no-verify
          else
            echo "nothing to commit"
          fi
        displayName: Git - Commit upgrades renamings
        condition: and(succeeded(), not(${{ parameters.skipUpgrade }}))

      # Use pnpm publish with recursive option to manage correctly the workspace:* replacement
      - bash: |
          pnpm -r publish ${DRY_RUN_OPT} --config.node-linker=hoisted --no-git-checks --filter=\!'@sage/xtrem-root' --filter=\!'@sage/xtrem-upgrade-test*' --filter=\!'@sage/*~*' --stream
        displayName: 'PNPM - Publish $(bumpKind) version (dry-run ${{ parameters.dryRun }})'
        env:
          TZ: UTC
          XTREM_BUILD_BINARY: 1 # enable build:binary, which part of prepack command

      - template: './prettify-package-json.yml'

      - script: |
          echo "head is $(git rev-parse HEAD)"
          echo "Latest commits from HEAD..."
          git log --date=short --pretty=format:'%h [%ci] <%ae> %d %s' --abbrev-commit -5
          echo ""
          git fetch origin
          echo "Latest commits from origin HEAD..."
          git log --date=short --pretty=format:'%h [%ci] <%ae> %d %s' --abbrev-commit -5 origin
          git push origin $(xtremBranch)
        displayName: Git - Push updated package and changelog files
        condition: and(succeeded(), not(${{ parameters.dryRun }}))

      - bash: |
          pnpm run generate:docker:pnpm:lock:files
        displayName: 'Generate pnpm-lock.yaml files for docker images'
        condition: and(succeeded(), not(${{ parameters.dryRun }}))

      - template: './prettify-package-json.yml'

      # git tags have to be explicitly pushed with git push origin --tags
      - script: |
          git push origin $(xtremBranch)
          git push origin --tags
        condition: and(succeeded(), not(${{ parameters.dryRun }}))
        displayName: Git - Push tags and docker pnpm-lock

      - ${{ if and(not(parameters.skipS3), not(parameters.dryRun)) }}:
          # Deploy a new database container and init schema
          - bash: |
              scripts/docker/load-docker-images.sh
              scripts/postgres/deploy-postgres.sh --skip-client-check
            displayName: 'Deploy a database container and client tools'
            env:
              AWS_ACCESS_KEY_ID: $(s3-xtrem-developers-utility-access-key)
              AWS_SECRET_ACCESS_KEY: $(s3-xtrem-developers-utility-secret)
              AWS_REGION: 'eu-west-1'

          - template: ./dump-reference-database-to-s3.yml
            parameters:
              applicationRootFolder: $(Build.SourcesDirectory)/services/main/xtrem-services-main
              applicationDisplayName: 'sdmo'

          - template: ./dump-reference-database-to-s3.yml
            parameters:
              applicationRootFolder: $(Build.SourcesDirectory)/tools/glossary/xtrem-glossary
              applicationDisplayName: 'glossary'

          - template: ./dump-reference-database-to-s3.yml
            parameters:
              applicationRootFolder: $(Build.SourcesDirectory)/platform/show-case/xtrem-show-case
              applicationDisplayName: 'showcase'

          - template: ./dump-reference-database-to-s3.yml
            parameters:
              applicationRootFolder: $(Build.SourcesDirectory)/shopfloor/main/shopfloor-main
              applicationDisplayName: 'shopfloor'

          - template: ./dump-reference-database-to-s3.yml
            parameters:
              applicationRootFolder: $(Build.SourcesDirectory)/x3-connector/main/x3-connector-main
              applicationDisplayName: 'x3 connector'

      # Unlock the pull requests even if the job failed
      - bash: |
          node scripts/releng/release-guard.mjs unlock $BRANCH_NAME
        condition: eq(variables.prStatus, 'inProgress')
        # this is not a critical step, so we can continue even if it fails
        continueOnError: true
        displayName: 'Unlocking merging pull requests'
        env:
          GITHUB_USERNAME: $(github_username)
          GITHUB_PASSWORD: $(github_password)
          SYSTEM_ACCESSTOKEN: $(System.AccessToken)
          BRANCH_NAME: $(xtremBranch)

      - bash: |
          echo "# Build Summary" > $SUMMARY_FILE
          echo "Release version: **$(jq -r '.version' package.json)**" >> $SUMMARY_FILE
          echo "##vso[task.uploadsummary]$SUMMARY_FILE"
        displayName: 'Upload Summary'
        env:
          SUMMARY_FILE: $(Agent.TempDirectory)/summary-$(Build.BuildId).md

      - ${{ if not(eq(variables['Build.SourceBranchName'], 'master')) }}:
          - bash: |
              set -e
              # convertToJson converts boolean to the strings "True" or "False"
              PIPELINES_DEF=$(echo "${PIPELINES_DEF}" | sed 's/"True"/true/g' | sed 's/"False"/false/g')
              echo "PIPELINES_DEF=${PIPELINES_DEF}"
              DEF_NAMES=$(echo ${PIPELINES_DEF} | jq -r '[.[] | select(.build == true) | .name] | join(",")')
              DEF_IDS=$(echo ${PIPELINES_DEF} | jq -r '[.[] | select(.build == true) | .id] | join(",")')
              echo "DEF_NAMES=${DEF_NAMES}"
              echo "  DEF_IDS=${DEF_IDS}"
              echo "  DRY_RUN=${DRY_RUN}"

              pipelines/shared/scripts/queue-pipeline-build.sh "${DEF_NAMES}" "${DEF_IDS}" "${DRY_RUN}"
            condition: succeeded()
            displayName: 'Queuing image builds'
            env:
              SYSTEM_ACCESSTOKEN: $(System.AccessToken)
              BRANCH_NAME: $(xtremBranch)
              PIPELINES_DEF: ${{ convertToJson(parameters.imagePipelinesDef) }}
              DRY_RUN: ${{ parameters.dryRun }}
              AZURE_PAT: $(az_build_pat)

      - task: PostToOffice365Connector@0
        name: notify_ci_support
        displayName: 'Notify CI-support channel'
        condition: and(failed(), not(${{ parameters.dryRun }}))
        inputs:
          # ci-support channel / connector 'Azure Pipeline - xtrem-patch-release'
          url: '$(ci_support_channel)'
          title: 'xtrem-patch-release pipeline failed'
          msg: '
            **The next generated images will not be relevant**

            * started for/by $(Build.Reason)/$(Build.QueuedBy)

            * [View build]($(System.CollectionUri)$(System.TeamProjectId)/_build/results?buildId=$(Build.BuildId)&view=logs)
            '
          themeColor: FF0000

      - task: PostToOffice365Connector@0
        name: notify_platform_support
        displayName: 'Notify platform-support channel'
        condition: and(failed(), not(${{ parameters.dryRun }}))
        inputs:
          # platform-support channel / connector 'Azure Pipeline - xtrem-patch-release'
          url: '$(platform_support_channel)'
          title: 'xtrem-patch-release pipeline failed'
          msg: '
            * started for/by $(Build.Reason)/$(Build.QueuedBy)

            * [View build]($(System.CollectionUri)$(System.TeamProjectId)/_build/results?buildId=$(Build.BuildId)&view=logs)
            '
          themeColor: FF0000

      - task: PostToOffice365Connector@0
        name: notify_adc_ci_support_failed
        displayName: 'Notify ADC CI-support channel'
        condition: and(failed(), not(${{ parameters.dryRun }}))
        inputs:
          # adc-ci-support channel / connector 'Azure Pipeline - xtrem-patch-release'
          url: '$(adc_ci_support_channel)'
          title: 'Failed xtrem-patch-release'
          msg: '
            **No new image will be generated**
            * started for/by $(Build.Reason)/$(Build.QueuedBy)
            * [View build]($(System.CollectionUri)$(System.TeamProjectId)/_build/results?buildId=$(Build.BuildId)&view=logs)
            '
          themeColor: FF0000

      - task: PostToOffice365Connector@0
        name: notify_adc_ci_support_successfully
        displayName: 'Notify ADC CI-support channel'
        condition: and(succeeded(), not(${{ parameters.dryRun }}))
        continueOnError: true
        inputs:
          # adc-ci-support channel / connector 'Azure Pipeline - xtrem-patch-release'
          url: '$(adc_ci_support_channel)'
          title: 'Successfully generated xtrem-patch-release'
          msg: '
            **Successfully generated the latest image**
            * started for/by $(Build.Reason)/$(Build.QueuedBy)
            * [View build]($(System.CollectionUri)$(System.TeamProjectId)/_build/results?buildId=$(Build.BuildId)&view=logs)
            '
          themeColor: 00FF00
