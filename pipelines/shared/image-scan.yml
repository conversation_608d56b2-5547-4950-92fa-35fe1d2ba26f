parameters:
  - name: imageName
    default: defaultName
  - name: tag
    default: latest
  - name: aquaResultDir
    default: $(Agent.TempDirectory)/aqua-scan-results
  - name: isDevImage
    type: boolean
    default: false

steps:
  - template: ./image-scan-core.yml
    parameters:
      imageName: ${{ parameters.imageName }}
      tag: $(tagName)
      isDevImage: ${{ parameters.isDevImage }}

  - bash: |
      SCAN_RESULT=$(node scripts/releng/gen-build-summary.mjs \
        --summary ${SUMMARY_FILE} \
        --docker-folder ${DOCKERFOLDER} \
        --aqua-scan-dir ${AQUA_SCAN_RESULT_DIR} \
        --image-name ${IMAGENAME}:${TAGNAME})
      echo "${SCAN_RESULT}"
      IS_ELEVATE_COMPLIANT=$(echo ${SCAN_RESULT} | jq -r '.isElevateCompliant')
      echo "isElevateCompliant=${IS_ELEVATE_COMPLIANT}"
      echo "##vso[task.uploadsummary]$SUMMARY_FILE"
      echo "##vso[task.setvariable variable=isElevateCompliant]${IS_ELEVATE_COMPLIANT}"
    displayName: 'Upload Summary ${{ parameters.imageName }}'
    condition: and(succeeded(),eq(variables.aquaScanEnabled,true), eq(variables.skipAquaScan,false))
    env:
      IMAGENAME: ${{ parameters.imageName}}
      TAGNAME: ${{ parameters.tag}}
      SUMMARY_FILE: $(Agent.TempDirectory)/summary-${{ parameters.imageName }}-$(Build.BuildId).md
      AQUA_SCAN_RESULT_DIR: $(Agent.TempDirectory)/aqua-scan-results

  - bash: |
      if [ "${IS_ELEVATE_COMPLIANT}" == "false" ]; then
      echo "##vso[task.logissue type=error]Not compliant with Sage Elevate security policy. See 'Extensions' tab for details"
      exit 1
      fi
    displayName: 'Compliant with Sage Elevate security policy'
    condition: and(succeeded(),eq(variables.aquaScanEnabled,true), eq(variables.skipAquaScan,false))
    continueOnError: ${{ parameters.isDevImage }}
    env:
      IS_ELEVATE_COMPLIANT: $(isElevateCompliant)

  - bash: |
      echo "##vso[task.setvariable variable=skipAquaScan]false"
    condition: and(always(),eq(variables.aquaScanEnabled,true), eq(variables.skipAquaScan,true))
    displayName: 'Reset skip Aqua Scan skip'
