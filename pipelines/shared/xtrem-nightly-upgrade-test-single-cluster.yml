# template mainly used by the xtrem-nightly-upgrade-test.yml pipeline
# will clone, install, build and test the upgrade from a specific cluster's backup
parameters:
  - name: backupType
    displayName: 'Backup type to restore'
    type: string
    default: ''
  - name: applicationRootFolder
    displayName: 'root folder of application'
    type: string
  - name: applicationDisplayName
    displayName: 'The displayName of the application'
    type: string
  - name: appName
    displayName: 'Application name'
    values:
      - 'glossary'
      - 'sdmo'
      - 'shopfloor'
      - 'showcase'
      - 'x3_connector'
      - 'xtrem'

steps:
  # Configuration
  - bash: |
      xdev releng replace-config --source xtrem-config-azure.yml --target xtrem-config.yml
    displayName: NPM - Copy xtrem-config
    env:
      XTREM_CACHE_READ_ONLY_KEY: $(xtrem_cache_read_only_key)
      XTREM_CACHE_READ_ONLY_SECRET: $(xtrem_cache_read_only_secret)

  # Postgres
  - bash: |
      xdev run postgres/deploy-postgres.sh --skip-client-check
    displayName: 'Deploy a database container and client tools'

  # Upgrade the application from a backup
  - template: pipelines/templates/upgrade/upgrade-from-backup.yml@huracan
    parameters:
      backupType: ${{ parameters.backupType }}
      skipTenantTests: true
      applicationRootFolder: '${{ parameters.applicationRootFolder }}'
      applicationDisplayName: ${{ parameters.applicationDisplayName }}
      upgradeType: 'test'
      appName: ${{ parameters.appName }}
      checkSchema: false
