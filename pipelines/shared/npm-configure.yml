# This template configure pnpm and initiate a login to the Azure NPM registry
# By default, it does not use the npm proxy because it is faster to get packages directly from the npm registry
parameters:
  - name: useNpmProxy
    displayName: 'Use nexus pnpm proxy'
    default: false
    type: boolean

steps:
  - bash: |
      set -e
      echo "Writing to path (AGENT) ${NPM_CONFIG_USERCONFIG}"
      npm config set "@sage:registry" https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/
      npm config set "@sageai:registry" https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/
      npm config set "//pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/:_authToken" "${AZURE_DEVOPS_TOKEN}"
      npm config set progress false
      # re-export the user configuration
      echo "##vso[task.setvariable variable=NPM_CONFIG_USERCONFIG;]${NPM_CONFIG_USERCONFIG}"
    displayName: 'PNPM - Configure private registry'
    env:
      NPM_CONFIG_USERCONFIG: '$(agent.tempdirectory)/.npmrc'
      AZURE_DEVOPS_TOKEN: $(System.AccessToken)

  - ${{ if eq(parameters.useNpmProxy, true) }}:
      - script: |
          echo "Writing to path (AGENT) ${NPM_CONFIG_USERCONFIG}"
          npm config set registry https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/
        displayName: 'PNPM - Configure proxy'

  - bash: |
      set -e

      PNPM_HOME=$(Pipeline.Workspace)/.pnpm-store
      echo "PNPM_HOME=$PNPM_HOME"
      echo "##vso[task.setvariable variable=PNPM_HOME;]$PNPM_HOME"

      corepack enable
      corepack prepare $(jq -r '.packageManager' package.json | cut -d'+' -f1) --activate
      corepack install
      pnpm config set store-dir $PNPM_HOME
      echo "##vso[task.setvariable variable=PUPPETEER_CACHE_DIR;]$PNPM_HOME/puppeteer"
      echo "##vso[task.prependpath]$PNPM_HOME"
    displayName: 'PNPM - Setup and config'
    condition: and(succeeded(), not(eq(variables.doesNotAffectBuild, true)))
