# Restore a backup from S3 (backup of sdmo/sdmo-cu/glossary/..., depending
# on the value of the parameter 'backupType') and run the upgrade engine in
# test mode (will first replay the SQL files and then execute the required upgrades)

parameters:
  - name: scopeName
    displayName: 'Scope name aka. project name'
    type: string
    default: ''
  - name: backupType
    displayName: 'Backup type to restore'
    type: string
    default: ''
  - name: deploymentMode
    displayName: 'Deployment mode'
    type: string
    default: 'development'
  - name: skipTenantTests
    displayName: 'Test backup only'
    type: boolean
    default: false
  - name: applicationRootFolder
    displayName: 'root folder of application'
    type: string
    default: ''
  - name: applicationDisplayName
    displayName: 'The displayName of the application'
    type: string
  - name: appName
    displayName: 'The application name'
    values:
      - 'glossary'
      - 'sdmo'
      - 'shopfloor'
      - 'showcase'
      - 'x3_connector'
      - 'xtrem'
  - name: upgradeType
    displayName: 'The type of upgrade'
    values:
      - prod
      - test
  - name: checkSchema
    displayName: 'Check for schema inconsitencies after upgrade'
    type: boolean
    default: false
  - name: clusterName
    displayName: 'The (optional) name of the cluster to upgrade'
    type: string
    default: ''

steps:
  - bash: |
      pipelines/shared/scripts/upgrade-from-backup.sh
    displayName: '${{ parameters.applicationDisplayName }} - upgrade (${{ parameters.backupType }})'
    condition: and(
      succeeded(),
      not(eq(variables.doesNotAffectBuild, true)),
      or(
      eq(variables.stage_scopes, '*'),
      containsValue(split(variables.stage_scopes, '|'), '${{ parameters.scopeName }}')
      )
      )
    env:
      XTREM_MAIN_APP_DIR: ${{ parameters.applicationRootFolder }}
      XTREM_SCHEMA_NAME: ${{ parameters.appName }}
      XTREM_TMP_DIR: $(Agent.TempDirectory)
      XTREM_SKIP_FACTORY_CHECKS: 1
      XTREM_BACKUP_TYPE: ${{ parameters.backupType }}
      XTREM_SKIP_TENANT_TESTS: ${{ parameters.skipTenantTests }}
      XTREM_UPGRADE_TYPE: ${{ parameters.upgradeType }}
      XTREM_CHECK_SCHEMA: ${{ parameters.checkSchema }}
      XTREM_CLUSTER_NAME: ${{ parameters.clusterName }}
      TMPDIR: $(Agent.TempDirectory)

  - ${{ if not(parameters.skipTenantTests) }}:
      - template: ./tenant-management-tests.yml
        parameters:
          scopeName: ${{ parameters.scopeName }}
          applicationRootFolder: '${{ parameters.applicationRootFolder }}'
          schemaName: ${{ parameters.appName }}
          displayNameSuffix: ' (${{ parameters.backupType }})'
          deploymentMode: '${{ parameters.deploymentMode }}'
          applicationDisplayName: ${{ parameters.applicationDisplayName }}
          backupType: ${{ parameters.backupType }}
