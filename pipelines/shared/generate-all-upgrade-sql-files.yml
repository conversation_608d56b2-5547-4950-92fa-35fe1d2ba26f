# Generation of ALL the SQL files, for all the services (sdmo, glossary, ...)

# see pipelines/xtrem-pr-pipeline/xtrem-pr-pipeline.yml to view the content of the scopes
parameters:
  - name: scopes
    type: object
    # just a template definition, this not an actual default object array value
    default:
      - name: 'project_scope'
        applicationDisplayName: 'project_name'
        applicationRootFolder: 'project_app_main_folder'
        sqlFilesTest:
          enabled: false
          skipTenantTests: true

  - name: skipTenantTests
    displayName: 'Should the tenant tests be skipped?'
    type: boolean
    default: false

steps:
  # Deploy a new database container and init schema
  - bash: |
      scripts/docker/load-docker-images.sh
      scripts/postgres/deploy-postgres.sh --skip-client-check
    displayName: 'Deploy a database container and client tools'
    env:
      AWS_ACCESS_KEY_ID: $(s3-xtrem-developers-utility-access-key)
      AWS_SECRET_ACCESS_KEY: $(s3-xtrem-developers-utility-secret)
      AWS_REGION: 'eu-west-1'

  - ${{ each scope in parameters.scopes }}:
      - ${{ if eq(scope.sqlFilesTest.enabled, true) }}:
          - template: ./generate-upgrade-sql-files.yml
            parameters:
              scopeName: ${{ scope.name }}
              applicationRootFolder: $(Build.SourcesDirectory)/${{ scope.applicationRootFolder }}
              deploymentMode: 'production'
              applicationDisplayName: ${{ scope.applicationDisplayName }}
              skipTenantTests: ${{ or(scope.sqlFilesTest.skipTenantTests, parameters.skipTenantTests) }}

  # Rename the 'vlatest' upgrades folders to 'vX.X.X' where X.X.X is the current version (before bump)
  - bash: |
      cd $(Build.SourcesDirectory)
      pnpm run ci-only:rename-latest-upgrades
    displayName: "Rename 'latest' upgrade folders"
