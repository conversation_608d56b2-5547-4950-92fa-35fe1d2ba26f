# This template will manage the dump to s3 of a newly created schema.
# On the day after, this dump will be used as a reference database by the upgrade engine
# to compute modifications (schema of the reference database vs nodes definitions)
# and generate the SQL file that will be applied when upgrading real clusters.

parameters:
  - name: applicationRootFolder
    displayName: 'root folder of application'
    type: string
  - name: applicationDisplayName
    displayName: 'The displayName of the application'
    type: string
steps:
  - bash: |
      cd ${{ parameters.applicationRootFolder }}
      # Create a new schema from scratch
      pnpm run xtrem schema --create --reset-database

      # Provision tenant 777777777777777777777
      tenantInfo='{"customer":{"id":"777777777777777777777","name":"test"},"tenant":{"id":"777777777777777777777","name":"test0_xtrem_latest"},"adminUsers":[{"email":"<EMAIL>","firstName":"<PERSON>","lastName":"Snow","locale":"en-US"}],"skipWelcomeEmail":true}'
      encodedInfo=$(echo -n $tenantInfo | base64 -w 0)
      pnpm run xtrem tenant --layers=setup,demo --init "$encodedInfo"

      # Dump the schema to a S3 bucket
      pnpm run xtrem schema --dump-to-s3
    env:
      XTREM_SCHEMA_NAME: xtrem_dump
    displayName: '${{ parameters.applicationDisplayName }} - Dump reference database to S3'
