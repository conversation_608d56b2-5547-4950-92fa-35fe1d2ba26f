parameters:
  - name: imageName
    default: defaultName
  - name: tag
    default: latest
  - name: aquaResultDir
    default: $(Agent.TempDirectory)/aqua-scan-results
  - name: isDevImage
    type: boolean
    default: false

steps:
  - bash: |
      echo "check if ${IMAGE_FULL_NAME} image exists"
      resultName=$(docker images "${IMAGE_FULL_NAME}" --format "{{.Repository}}:{{.Tag}}")
      if [ -z "$resultName" ]; then
        echo "##vso[task.setvariable variable=skipAquaScan]true"
        echo "Image does not exist, skipping Aqua scan."
        exit 0
      fi
      echo "Image exists, proceeding with Aqua scan."
      echo "##vso[task.setvariable variable=skipAquaScan]false"
    displayName: 'Check if ${{ parameters.imageName }} image exists'
    condition: and(succeeded(),eq(variables.aquaScanEnabled,true))
    env:
      IMAGE_FULL_NAME: '${{parameters.imageName}}:${{parameters.tag}}'

  - bash: |
      mkdir -p ${RESULT_DIR}
      docker login registry.aquasec.com -u "${AQUASEC_REGISTRY_USER}" -p "${AQUASEC_REGISTRY_PASS}"
    condition: and(succeeded(),eq(variables.aquaScanEnabled,true), eq(variables.skipAquaScan,false))
    displayName: Log in to aquasec registry
    env:
      AQUASEC_SCANNER_TAG: $(AQUASEC_SCANNER_TAG)
      AQUASEC_REGISTRY_USER: $(AQUASEC_REGISTRY_USER)
      AQUASEC_REGISTRY_PASS: $(AQUASEC_REGISTRY_PASS)
      RESULT_DIR: ${{parameters.aquaResultDir}}

  - task: aquasecScanner@4
    condition: and(succeeded(),eq(variables.aquaScanEnabled,true), eq(variables.skipAquaScan,false))
    continueOnError: ${{ parameters.isDevImage }}
    displayName: 'Scan ${{ parameters.imageName }} image'
    inputs:
      image: '${{parameters.imageName}}:${{parameters.tag}}'
      scanType: 'local'
      register: false
      hideBase: false
      showNegligible: false
      connection: 'aquasec-02lnkqaaq'
      caCertificates: true
      showWillNotFix: true
      scanner: 'registry.aquasec.com/scanner:$(AQUASEC_SCANNER_TAG)'
      customFlags: 'path=${{parameters.aquaResultDir}}'

  - bash: |
      docker logout registry.aquasec.com
    condition: and(always(),eq(variables.aquaScanEnabled,true), eq(variables.skipAquaScan,false))
    displayName: Log out from aquasec registry
