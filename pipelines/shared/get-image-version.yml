parameters:
  - name: packagePath
    type: string
  - name: branchName
    type: string

steps:
  # when the pipeline is triggered by the release path,
  # the checkout is done using the commit of the first pipeline in the dependency flow,
  # so we need to get the current package.json version of the given branch
  - script: |
      git checkout origin/${{ parameters.branchName }} -- ${{ parameters.packagePath }}/package.json
    displayName: Get image package version

  - bash: |
      PACKAGE_VERSION=$(cat ${{ parameters.packagePath }}/package.json \
                      | grep version \
                      | head -1 \
                      | awk -F: '{ print $2 }' \
                      | sed 's/[",]//g' \
                      | sed 's/ //g')

      echo current package version ${PACKAGE_VERSION} on branch ${{ parameters.branchName }}
      echo "##vso[task.setvariable variable=imageVersion]${PACKAGE_VERSION}"
    displayName: set image version
