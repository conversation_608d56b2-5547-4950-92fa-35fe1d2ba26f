steps:
  # cleanup git configuration
  - bash: |
      echo "> Cleaning up git configuration..."
      # show what will be removed
      git config --global --get-regexp --name-only '^user\.(name|email)' || echo "<none>: user name/email not set"
      git config --global --get-regexp --name-only '^url\.https://.+\.insteadof' || echo "<none>: insteadof not set"
      git config --global --get-regexp --name-only '^http\.https://.+\.extraheader' || echo "<none>: extraheader not set"

      # do actual removing
      git config --global --get-regexp --name-only '^user\.(name|email)' | xargs -r -n1 git config --global --unset
      git config --global --get-regexp --name-only '^url\.https://.+\.insteadof' | xargs -r -n1 git config --global --unset-all
      git config --global --get-regexp --name-only '^http\.https://.+\.extraheader' | xargs -r -n1 git config --global --unset-all

      echo "> After Clean up..."
      # show what will be removed
      git config --global --get-regexp --name-only '^user\.(name|email)' || echo "<none>: user name/email not set"
      git config --global --get-regexp --name-only '^url\.https://.+\.insteadof' || echo "<none>: insteadof not set"
      git config --global --get-regexp --name-only '^http\.https://.+\.extraheader' || echo "<none>: extraheader not set"
      echo "> Cleaning up git configuration done"
    displayName: 'Cleanup git configuration'
    condition: always()
    env:
      GITHUB_USERNAME: $(github_username)

  # cleanup npm configuration
  - bash: |
      echo "> Cleaning up npm configuration..."
      npm config list
      npm config delete "@sage:registry"
      npm config delete "//pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/:_authToken"
      npm config delete progress
      if [ -f "${NPM_CONFIG_USERCONFIG}" ]; then
        echo "> Removing ${NPM_CONFIG_USERCONFIG}..."
        rm -f "${NPM_CONFIG_USERCONFIG}"
      else
        echo "NPM_CONFIG_USERCONFIG var is not set or file does not exist"
      fi
      echo "> Cleaning up npm configuration done"
    displayName: 'Cleanup npm configuration'
    condition: always()
    env:
      NPM_CONFIG_USERCONFIG: '$(agent.tempdirectory)/.npmrc'

  # cleanup docker containers
  - bash: |
      docker ps -a
      echo ""
      echo "> Stopping $(docker ps -aq | wc -l) docker containers..."
      docker ps -aq | xargs -r docker inspect --format='{{.Config.Image}}'
      docker ps -aq | xargs -r docker stop

      # remove containers that are stopped and not removed by docker stop
      echo ""
      docker ps -a
      echo ""
      echo "> Removing $(docker ps -aq --filter "status=exited" | wc -l) docker containers..."
      docker ps -aq --filter "status=exited" | xargs -r docker inspect --format='{{.Config.Image}}'
      docker ps -aq --filter "status=exited" | xargs -r docker rm
      # if we've got errors removing containers, we don't want to fail the build
      echo "> Stopping docker containers done"
    displayName: 'Stop and remove all docker containers'
    condition: always()
