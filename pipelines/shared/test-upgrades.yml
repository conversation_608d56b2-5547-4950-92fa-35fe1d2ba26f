# Tests the upgrade on a bunch of clusters' backups

# see pipelines/xtrem-pr-pipeline/xtrem-pr-pipeline.yml to view the content of the scopes
parameters:
  - name: scopes
    type: object
    # just a template definition, this not an actual default object array value
    default:
      - name: 'project_scope'
        applicationDisplayName: 'project_name'
        applicationRootFolder: 'project_app_main_folder'
        sqlFilesTest:
          enabled: false
          skipTenantTests: true
          backupTypes:
            - name: '--s3ConfigType=sdmo_cu'
              appName: 'sdmo'
            - name: '--s3ConfigType=sdmo'
              appName: 'sdmo'
              skipTenantTests: true
            - name: '--s3ConfigType=glossary'
              skipTenantTests: true
              appName: 'glossary'

  - name: skipTenantTests
    displayName: 'Should the tenant tests be skipped?'
    type: boolean
    default: false

steps:
  - ${{ each scope in parameters.scopes }}:
      - ${{ if eq(scope.sqlFilesTest.enabled, true) }}:
          - ${{ each backupType in scope.sqlFilesTest.backupTypes }}:
              - template: ./upgrade-from-backup.yml
                parameters:
                  scopeName: ${{ scope.name }}
                  deploymentMode: 'production'
                  backupType: '${{ backupType.name }}'
                  applicationRootFolder: $(Build.SourcesDirectory)/${{ scope.applicationRootFolder }}
                  applicationDisplayName: ${{ scope.applicationDisplayName }}
                  skipTenantTests: ${{ or(scope.sqlFilesTest.skipTenantTests, backupType.skipTenantTests, parameters.skipTenantTests) }}
                  upgradeType: 'prod'
                  appName: ${{ backupType.appName }}
