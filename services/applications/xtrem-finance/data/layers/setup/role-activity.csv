"role";"activity";"_vendor";"_sort_value";"has_all_permissions";"permissions";"is_active"
"100";"accountsPayableInvoice";"sage";"15800";"Y";"[]";"Y"
"100";"accountsReceivableAdvance";"sage";"15900";"Y";"[]";"Y"
"100";"accountsReceivableInvoice";"sage";"16000";"Y";"[]";"Y"
"100";"accountsReceivablePayment";"sage";"16100";"Y";"[]";"Y"
"100";"datevExport";"sage";"16150";"Y";"[]";"Y"
"100";"generateJournalEntries";"sage";"16200";"Y";"[]";"Y"
"100";"journalEntry";"sage";"16300";"Y";"[]";"Y"
"100";"journalEntryInquiry";"sage";"16400";"Y";"[]";"Y"
"100";"payment";"sage";"16450";"Y";"[]";"Y"
"100";"receipt";"sage";"16500";"Y";"[]";"Y"
"100";"accountsPayableOpenItem";"sage";"3100";"Y";"[]";"Y"
"100";"accountsReceivableOpenItem";"sage";"2700";"Y";"[]";"Y"
"200";"accountsReceivableInvoice";"sage";"1400";"N";"[""read""]";"Y"
"200";"accountsReceivablePayment";"sage";"1500";"N";"[""read""]";"Y"
"200";"journalEntry";"sage";"1600";"N";"[""read""]";"Y"
"200";"journalEntryInquiry";"sage";"1700";"Y";"[]";"Y"
"400";"accountsPayableInvoice";"sage";"1800";"N";"[""read""]";"Y"
"400";"journalEntry";"sage";"1900";"N";"[""read""]";"Y"
"400";"journalEntryInquiry";"sage";"1700";"Y";"[]";"Y"
"500";"accountsPayableInvoice";"sage";"1900";"N";"[""read""]";"Y"
"700";"journalEntry";"sage";"5400";"N";"[""read""]";"Y"
"700";"journalEntryInquiry";"sage";"5500";"Y";"[]";"Y"
"900";"journalEntry";"sage";"2300";"N";"[""read""]";"Y"
"900";"journalEntryInquiry";"sage";"2400";"Y";"[]";"Y"
"Support User";"accountsPayableInvoice";"sage";"2400";"Y";"[]";"Y"
"Support User";"accountsReceivableInvoice";"sage";"2500";"Y";"[]";"Y"
"Support User";"journalEntry";"sage";"2600";"Y";"[]";"Y"
"Support User";"accountsReceivableAdvance";"sage";"2700";"Y";"[]";"Y"
"Support User";"accountsReceivablePayment";"sage";"2800";"Y";"[]";"Y"
"Support User";"datevExport";"sage";"2850";"Y";"[]";"Y"
"Support User";"generateJournalEntries";"sage";"2900";"Y";"[]";"Y"
"Support User";"journalEntryInquiry";"sage";"3000";"Y";"[]";"Y"
"Support User";"accountsPayableOpenItem";"sage";"3050";"Y";"[]";"Y"
"Support User";"accountsReceivableOpenItem";"sage";"3100";"Y";"[]";"Y"
"Support User";"payment";"sage";"3150";"Y";"[]";"Y"
"Support User";"receipt";"sage";"3200";"Y";"[]";"Y"
"Support User";"accountingInterfaceListener";"sage";"3300";"Y";"[]";"Y"
"Support User Read-only";"accountsPayableInvoice";"sage";"2400";"N";"[""read""]";"Y"
"Support User Read-only";"accountsReceivableInvoice";"sage";"2500";"N";"[""read""]";"Y"
"Support User Read-only";"datevExport";"sage";"2550";"N";"[""read""]";"Y"
"Support User Read-only";"journalEntry";"sage";"2600";"N";"[""read""]";"Y"
"Support User Read-only";"accountsPayableOpenItem";"sage";"2650";"N";"[""read""]";"Y"
"Support User Read-only";"accountsReceivableOpenItem";"sage";"2700";"N";"[""read""]";"Y"
"Support User Read-only";"payment";"sage";"2800";"N";"[""read""]";"Y"
"Support User Read-only";"receipt";"sage";"2900";"N";"[""read""]";"Y"
"Operational User";"accountsPayableInvoice";"sage";"1100";"Y";"[]";"Y"
"Operational User";"accountsReceivableAdvance";"sage";"1200";"Y";"[]";"Y"
"Operational User";"accountsReceivableInvoice";"sage";"1300";"Y";"[]";"Y"
"Operational User";"accountsReceivablePayment";"sage";"1400";"Y";"[]";"Y"
"Operational User";"journalEntry";"sage";"1500";"Y";"[]";"Y"
"Operational User";"journalEntryInquiry";"sage";"1600";"Y";"[]";"Y"
"Admin";"accountsPayableInvoice";"sage";"4000";"Y";"[]";"Y"
"Admin";"accountsReceivableAdvance";"sage";"4100";"Y";"[]";"Y"
"Admin";"accountsReceivableInvoice";"sage";"4200";"Y";"[]";"Y"
"Admin";"accountsReceivablePayment";"sage";"4300";"Y";"[]";"Y"
"Admin";"datevExport";"sage";"4350";"Y";"[]";"Y"
"Admin";"generateJournalEntries";"sage";"4400";"Y";"[]";"Y"
"Admin";"journalEntry";"sage";"4500";"Y";"[]";"Y"
"Admin";"journalEntryInquiry";"sage";"4600";"Y";"[]";"Y"
"Admin";"accountsPayableOpenItem";"sage";"4650";"Y";"[]";"Y"
"Admin";"accountsReceivableOpenItem";"sage";"4700";"Y";"[]";"Y"
"Admin";"payment";"sage";"4750";"Y";"[]";"Y"
"Admin";"receipt";"sage";"4800";"Y";"[]";"Y"
"Admin";"accountingInterfaceListener";"sage";"4900";"Y";"[]";"Y"
"Business User";"accountsPayableInvoice";"sage";"3500";"Y";"[]";"Y"
"Business User";"accountsReceivableAdvance";"sage";"3600";"Y";"[]";"Y"
"Business User";"accountsReceivableInvoice";"sage";"3700";"Y";"[]";"Y"
"Business User";"accountsReceivablePayment";"sage";"3800";"Y";"[]";"Y"
"Business User";"generateJournalEntries";"sage";"3900";"Y";"[]";"Y"
"Business User";"journalEntry";"sage";"4000";"Y";"[]";"Y"
"Business User";"journalEntryInquiry";"sage";"4100";"Y";"[]";"Y"
"1100";"accountsPayableInvoice";"sage";"60";"N";"[""read"",""post""]";"Y"
"1100";"accountsReceivableInvoice";"sage";"70";"N";"[""read"",""post"",""updateOpenItemFromIntacct""]";"Y"
"1100";"datevExport";"sage";"80";"N";"[""read"",""manage""]";"Y"
"1100";"generateJournalEntries";"sage";"90";"N";"[""accountingIntegration""]";"Y"
"1100";"journalEntry";"sage";"100";"N";"[""read"",""manage"",""post""]";"Y"
"1100";"journalEntryInquiry";"sage";"110";"N";"[""read""]";"Y"
"1100";"accountsPayableOpenItem";"sage";"111";"Y";"[]";"Y"
"1100";"accountsReceivableOpenItem";"sage";"112";"Y";"[]";"Y"
"1100";"payment";"sage";"120";"Y";"[]";"Y"
"1100";"receipt";"sage";"125";"Y";"[]";"Y"
"1100";"accountingInterfaceListener";"sage";"130";"Y";"[]";"Y"
"Admin - Technical";"accountsPayableOpenItem";"sage";"112";"Y";"[]";"Y"
"Admin - Technical";"accountsReceivableOpenItem";"sage";"115";"Y";"[]";"Y"
"Admin - Technical";"payment";"sage";"120";"Y";"[]";"Y"
"Admin - Technical";"receipt";"sage";"130";"Y";"[]";"Y"
