{
    xtremFinance {
        accountsReceivableOpenItem {
            query(filter: "{ documentNumber : 'SI1'}") {
                edges {
                    node {
                        dueDate
                        businessEntity {
                            id
                        }
                        businessEntityPayment {
                            id
                        }
                        type
                        currency {
                            id
                        }
                        transactionAmountDue
                        companyAmountDue
                        financialSiteAmountDue
                        status
                        documentType
                        documentNumber
                        documentSysId
                        accountsReceivableInvoice {
                            number
                        }
                        totalAmount
                        amountDue
                    }
                }
            }
        }
    }
}
