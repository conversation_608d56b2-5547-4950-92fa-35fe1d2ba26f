{"data": {"xtremFinance": {"accountsReceivableOpenItem": {"query": {"edges": [{"node": {"dueDate": "2022-12-31", "businessEntity": {"id": "US019"}, "businessEntityPayment": {"id": "US019"}, "type": "customer", "currency": {"id": "USD"}, "transactionAmountDue": "3000", "companyAmountDue": "3000", "financialSiteAmountDue": "3000", "status": "notPaid", "documentType": "salesInvoice", "documentNumber": "SI1", "documentSysId": 1, "accountsReceivableInvoice": {"number": "AR-**********"}, "totalAmount": "3000", "amountDue": "3000"}}, {"node": {"dueDate": "2022-12-31", "businessEntity": {"id": "US019"}, "businessEntityPayment": {"id": "US019"}, "type": "customer", "currency": {"id": "USD"}, "transactionAmountDue": "3000", "companyAmountDue": "3000", "financialSiteAmountDue": "3000", "status": "notPaid", "documentType": "salesInvoice", "documentNumber": "SI1", "documentSysId": 1, "accountsReceivableInvoice": {"number": "AR-**********"}, "totalAmount": "3000", "amountDue": "3000"}}]}}}}}