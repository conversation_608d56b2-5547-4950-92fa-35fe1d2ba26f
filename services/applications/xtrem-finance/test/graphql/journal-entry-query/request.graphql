{
    xtremFinance {
        journalEntry {
            query(filter: "{ _id : '2'}") {
                edges {
                    node {
                        number
                        financialSite {
                            id
                        }
                        postingStatus
                        postingDate
                        description
                        reference
                        origin
                        financeIntegrationApp
                        financeIntegrationStatus
                        financeIntegrationAppRecordId
                        financeIntegrationAppUrl
                        lines {
                            query {
                                edges {
                                    node {
                                        financialSite {
                                            id
                                        }
                                        chartOfAccount {
                                            name
                                        }
                                        transactionCurrency {
                                            id
                                        }
                                        inquiryTransactionCurrency {
                                            id
                                        }
                                        companyFxRate
                                        fxRateDate
                                        account {
                                            id
                                            composedDescription
                                        }
                                        businessEntity {
                                            id
                                        }
                                        sign
                                        numericSign
                                        transactionAmount
                                        signedTransactionAmount
                                        companyAmount
                                        description
                                        inquiryDescription
                                        commonReference
                                        blank
                                        financialSiteAttribute {
                                            id
                                            composedDescription
                                        }
                                        businessSiteAttribute {
                                            id
                                        }
                                        stockSiteAttribute {
                                            id
                                        }
                                        manufacturingSiteAttribute {
                                            id
                                        }
                                        supplierAttribute {
                                            businessEntity {
                                                name
                                                composedDescription
                                            }
                                        }
                                        customerAttribute {
                                            businessEntity {
                                                name
                                                composedDescription
                                            }
                                        }
                                        projectAttribute {
                                            name
                                            composedDescription
                                        }
                                        employeeAttribute {
                                            name
                                            composedDescription
                                        }
                                        itemAttribute {
                                            name
                                            composedDescription
                                        }
                                        dimension01 {
                                            name
                                            composedDescription
                                        }
                                        dimension02 {
                                            name
                                        }
                                        dimension03 {
                                            name
                                        }
                                        dimension04 {
                                            name
                                        }
                                        dimension05 {
                                            name
                                        }
                                        dimension06 {
                                            name
                                        }
                                        attributesAndDimensions {
                                            query {
                                                edges {
                                                    node {
                                                        storedAttributes
                                                        storedDimensions
                                                        transactionAmount
                                                        companyAmount
                                                        financialSite {
                                                            id
                                                            composedDescription
                                                        }
                                                        businessSite {
                                                            id
                                                        }
                                                        stockSite {
                                                            id
                                                        }
                                                        manufacturingSite {
                                                            id
                                                        }
                                                        supplier {
                                                            businessEntity {
                                                                name
                                                                composedDescription
                                                            }
                                                        }
                                                        customer {
                                                            businessEntity {
                                                                name
                                                                composedDescription
                                                            }
                                                        }
                                                        project {
                                                            name
                                                            composedDescription
                                                        }
                                                        employee {
                                                            name
                                                            composedDescription
                                                        }
                                                        item {
                                                            name
                                                            composedDescription
                                                        }
                                                        dimension01 {
                                                            name
                                                            composedDescription
                                                        }
                                                        dimension02 {
                                                            name
                                                        }
                                                        dimension03 {
                                                            name
                                                        }
                                                        dimension04 {
                                                            name
                                                        }
                                                        dimension05 {
                                                            name
                                                        }
                                                        dimension06 {
                                                            name
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
