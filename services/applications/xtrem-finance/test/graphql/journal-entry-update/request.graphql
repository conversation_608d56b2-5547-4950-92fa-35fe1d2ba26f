mutation {
    xtremFinance {
        journalEntry {
            update(
                data: {
                    _id: "5"
                    lines: [
                        {
                            _id: "15"
                            _action: "update"
                            attributesAndDimensions: [
                                {
                                    _id: "5"
                                    _action: "update"
                                    storedAttributes: "{\"item\":\"5467\",\"project\":\"AttPROJ2\",\"supplier\":\"US018\",\"stockSite\":\"US002\",\"financialSite\":\"US001\"}"
                                }
                            ]
                        }
                    ]
                }
            ) {
                _id
                description
                lines {
                    query {
                        edges {
                            node {
                                _id
                                attributesAndDimensions {
                                    query {
                                        edges {
                                            node {
                                                _id
                                                hasAttributesOrDimenionsChanged
                                                storedAttributes
                                                storedDimensions
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
