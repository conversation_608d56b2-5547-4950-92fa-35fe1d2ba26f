import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremFinance from '../../../lib/index';

describe('Accounting interface listener ', () => {
    before(() => {});
    it('Update isPrinted flag from a sales invoice', () =>
        Test.withContext(async context => {
            let arInvoice = await context.read(xtremFinance.nodes.AccountsReceivableInvoice, {
                number: 'AR-**********',
            });
            assert.strictEqual(await arInvoice.isPrinted, false);

            await xtremFinance.nodes.AccountingInterfaceListener.onPrintingStatusUpdate(context, {
                documentType: 'salesInvoice',
                documentSysId: 2,
            });

            arInvoice = await context.read(xtremFinance.nodes.AccountsReceivableInvoice, {
                number: 'AR-**********',
            });
            assert.strictEqual(await arInvoice.isPrinted, true);
        }));

    it('Update isPrinted flag from a sales credit memo', () =>
        Test.withContext(async context => {
            let arInvoice = await context.read(xtremFinance.nodes.AccountsReceivableInvoice, {
                number: 'AR-**********',
            });
            assert.strictEqual(await arInvoice.isPrinted, false);

            await xtremFinance.nodes.AccountingInterfaceListener.onPrintingStatusUpdate(context, {
                documentType: await arInvoice.type,
                documentSysId: arInvoice._id,
            });

            arInvoice = await context.read(xtremFinance.nodes.AccountsReceivableInvoice, {
                number: 'AR-**********',
            });
            assert.strictEqual(await arInvoice.isPrinted, true);
        }));

    // We do support this status now
    it.skip('Finance document retry - status not supported', () =>
        Test.withContext(async context => {
            const financeTransaction = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                batchId: 'cdf35002-6b75-4e4b-8a41-7ab11c18a53B',
                documentNumber: 'MISC_RECEIPT2',
                documentType: 'miscellaneousStockReceipt',
                targetDocumentType: 'journalEntry',
            });
            assert.strictEqual(await financeTransaction.status, 'error');

            const result = await xtremFinance.nodes.AccountingInterfaceListener.retryFinanceDocument(
                context,
                financeTransaction,
            );

            assert.strictEqual(result.message, 'Status not supported: error.');
        }));

    it('Finance document retry - records on staging nok', () =>
        Test.withContext(async context => {
            const financeTransaction = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                batchId: 'cdf35002-6b75-4e4b-8a41-7ab11c18a52A',
                documentNumber: 'MISC_RECEIPT1',
                documentType: 'miscellaneousStockReceipt',
                targetDocumentType: 'journalEntry',
            });

            const result = await xtremFinance.nodes.AccountingInterfaceListener.retryFinanceDocument(
                context,
                financeTransaction,
            );

            assert.deepEqual(result, {
                wasSuccessful: true,
                message: 'The document is being reprocessed.',
            });
        }));

    it('Finance document retry - Shipment created', () =>
        Test.withContext(async context => {
            const financeTransaction = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                batchId: '34d0eb2a-3ff5-43ad-be58-2d49e9dedbfe',
                documentNumber: 'SH240001',
                documentType: 'salesShipment',
                targetDocumentType: 'journalEntry',
            });

            const result = await xtremFinance.nodes.AccountingInterfaceListener.retryFinanceDocument(
                context,
                financeTransaction,
            );

            assert.deepEqual(result, {
                wasSuccessful: true,
                message: `Journals created: 1`,
            });
        }));

    it('Finance document retry - sales invoice resent', () =>
        Test.withContext(async context => {
            const financeTransaction = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                batchId: 'e547ac46-c325-4fee-8fdf-22e4c0abb40c',
                documentNumber: 'SIG1240002',
                documentType: 'salesInvoice',
                targetDocumentType: 'accountsReceivableInvoice',
            });

            const notifyRetrySpy = sinon.spy(context, 'notify');

            const result = await xtremFinance.nodes.AccountingInterfaceListener.retryFinanceDocument(
                context,
                financeTransaction,
            );

            assert.deepEqual(result, {
                wasSuccessful: true,
                message: `The document is being reprocessed.`,
            });
            assert.equal(notifyRetrySpy.getCalls().length, 1);
            assert.deepEqual(notifyRetrySpy.args.at(0), [
                'SalesInvoice/resendNotificationForFinance',
                { number: 'SIG1240002' },
            ]);
        }));

    it('Create journal entry from accounting staging using asyncMutation with journalsCreatedData flag set to false', () =>
        Test.withContext(
            async context => {
                const result =
                    await xtremFinance.nodes.AccountingInterfaceListener.createJournalsFromAccountingStagingJob(
                        context,
                        false,
                        JSON.stringify({ documentNumber: 'SR250008' }),
                    );
                assert.equal(result, 'Journals created: 1');
            },
            { today: '2025-06-23' },
        ));

    it('Create journal entry from accounting staging using asyncMutation with journalsCreatedData flag set to true', () =>
        Test.withContext(
            async context => {
                const result =
                    await xtremFinance.nodes.AccountingInterfaceListener.createJournalsFromAccountingStagingJob(
                        context,
                        true,
                        JSON.stringify({ documentNumber: 'SR250008' }),
                    );
                assert.equal(result, 'Journals created: 1 \n  \n IJ250001');
            },
            { today: '2025-06-23' },
        ));
});
