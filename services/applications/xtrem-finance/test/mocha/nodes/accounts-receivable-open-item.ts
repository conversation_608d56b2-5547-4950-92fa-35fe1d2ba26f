import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type { InitialNotification } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremFinance from '../../../lib/index';

describe('bulkOpenItemUpdate', () => {
    function notificationCheck(
        notifyUserSpy: sinon.SinonSpy<[notification: InitialNotification]>,
        expectedUserNotification: InitialNotification,
    ) {
        assert.equal(notifyUserSpy.getCalls().length, 1);
        const notifyUserPayload = notifyUserSpy.args.at(0);
        assert.equal(notifyUserPayload?.length, 1);
        const userNotification = notifyUserPayload?.at(0);

        assert.equal(userNotification?.title, expectedUserNotification.title);
        assert.equal(userNotification?.description, expectedUserNotification.description);
        assert.equal(userNotification?.icon, expectedUserNotification.icon);
        assert.equal(userNotification?.level, expectedUserNotification.level);
        assert.equal(userNotification?.shouldDisplayToast, expectedUserNotification.shouldDisplayToast);

        assert.equal(userNotification?.actions.length, 1);
        assert.equal(userNotification?.actions?.at(0)?.link, expectedUserNotification.actions.at(0)?.link);
        assert.equal(userNotification?.actions?.at(0)?.title, expectedUserNotification.actions.at(0)?.title);
        assert.equal(userNotification?.actions?.at(0)?.icon, expectedUserNotification.actions.at(0)?.icon);
        assert.equal(userNotification?.actions?.at(0)?.style, expectedUserNotification.actions.at(0)?.style);
    }

    it('Bulk open item update - notifications error', () =>
        Test.withContext(
            async context => {
                const documentsError: string[] = [];

                const notifyUserSpyError = sinon.spy(context, 'notifyUser');
                await xtremFinance.functions.notifyUserBulkOpenItemPayment(
                    context,
                    documentsError,
                    '@sage/xtrem-finance/AccountsReceivableOpenItem',
                );

                assert.equal(notifyUserSpyError.getCalls().length, 1);

                const expectedUserNotification: InitialNotification = {
                    title: 'Force open item payment',
                    description: `The forced payment for all open items failed. Review batch task logs for more information.`,
                    icon: 'cross',
                    level: 'error',
                    shouldDisplayToast: true,
                    actions: [
                        {
                            link: `@sage/xtrem-communication/SysNotificationState/`,
                            title: 'Batch task logs',
                            icon: 'link',
                            style: 'tertiary',
                        },
                    ],
                };

                // Notification check
                assert.equal(notifyUserSpyError.getCalls().length, 1);
                notificationCheck(notifyUserSpyError, expectedUserNotification);
            },
            {
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Bulk open item update - notifications success', () =>
        Test.withContext(
            async context => {
                const documentsSuccess: string[] = ['SIG1240001'];

                const notifyUserSpySuccess = sinon.spy(context, 'notifyUser');
                await xtremFinance.functions.notifyUserBulkOpenItemPayment(
                    context,
                    documentsSuccess,
                    '@sage/xtrem-finance/AccountsReceivableOpenItem',
                );

                assert.equal(notifyUserSpySuccess.getCalls().length, 1);

                const expectedUserNotificationSuccessInvoice: InitialNotification = {
                    title: 'Force open item payment',
                    description: `The open items were forced to be paid.`,
                    icon: 'tick',
                    level: 'success',
                    shouldDisplayToast: true,
                    actions: [
                        {
                            link: xtremSystem.functions.linkToFilteredMainList<xtremFinance.nodes.AccountsReceivableOpenItem>(
                                '@sage/xtrem-finance/AccountsReceivableOpenItem',
                                {
                                    documentNumber: { _in: [documentsSuccess[0]] },
                                },
                            ),
                            title: 'Open items',
                            icon: 'link',
                            style: 'tertiary',
                        },
                        {
                            link: `@sage/xtrem-communication/SysNotificationState/`,
                            title: 'Batch task logs',
                            icon: 'link',
                            style: 'tertiary',
                        },
                    ],
                };

                const notifyUserPayload = notifyUserSpySuccess.args.at(0);
                assert.equal(notifyUserPayload?.length, 1);
                const userNotification = notifyUserPayload?.at(0);

                assert.equal(userNotification?.title, expectedUserNotificationSuccessInvoice.title);
                assert.equal(userNotification?.description, expectedUserNotificationSuccessInvoice.description);
                assert.equal(userNotification?.icon, expectedUserNotificationSuccessInvoice.icon);
                assert.equal(userNotification?.level, expectedUserNotificationSuccessInvoice.level);
                assert.equal(
                    userNotification?.shouldDisplayToast,
                    expectedUserNotificationSuccessInvoice.shouldDisplayToast,
                );

                assert.equal(userNotification?.actions.length, 2);
                assert.equal(
                    userNotification?.actions?.at(0)?.link,
                    expectedUserNotificationSuccessInvoice.actions.at(0)?.link,
                );
                assert.equal(
                    userNotification?.actions?.at(0)?.title,
                    expectedUserNotificationSuccessInvoice.actions.at(0)?.title,
                );
                assert.equal(
                    userNotification?.actions?.at(0)?.icon,
                    expectedUserNotificationSuccessInvoice.actions.at(0)?.icon,
                );
                assert.equal(
                    userNotification?.actions?.at(0)?.style,
                    expectedUserNotificationSuccessInvoice.actions.at(0)?.style,
                );
            },
            {
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));
    it('forceOpenItemPayment', () =>
        Test.withContext(
            async context => {
                const openItem = await context
                    .query(xtremFinance.nodes.AccountsReceivableOpenItem, {
                        filter: { accountsReceivableInvoice: { _id: '#SIPZ2250001|salesInvoice' } },
                        first: 1,
                        forUpdate: true,
                    })
                    .elementAt(0);

                await xtremFinance.functions.forceOpenItemPayment(context, openItem);

                const paidOpenItem = await context
                    .query(xtremFinance.nodes.AccountsReceivableOpenItem, {
                        filter: { accountsReceivableInvoice: { _id: '#SIPZ2250001|salesInvoice' } },
                        first: 1,
                    })
                    .elementAt(0);
                assert.strictEqual((await paidOpenItem.forcedAmountPaid).toString(), '1150');
                assert.strictEqual((await paidOpenItem.transactionAmountPaid).toString(), '1150');
                assert.strictEqual((await paidOpenItem.companyAmountPaid).toString(), '72.46');
                assert.strictEqual((await paidOpenItem.financialSiteAmountPaid).toString(), '72.46');
                assert.strictEqual(await paidOpenItem.status, 'paid');
                assert.strictEqual(await (await paidOpenItem.closeReason)?.id, 'Forced close');

                const invoice = await context.read(xtremFinance.nodes.AccountsReceivableInvoice, {
                    _id: '#SIPZ2250001|salesInvoice',
                });
                assert.strictEqual(await invoice.paymentStatus, 'paid');
            },
            {
                today: '2025-01-24',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Partially forceOpenItemPayment', () =>
        Test.withContext(
            async context => {
                const openItem = await context
                    .query(xtremFinance.nodes.AccountsReceivableOpenItem, {
                        filter: { accountsReceivableInvoice: { _id: '#SIPZ2250001|salesInvoice' } },
                        first: 1,
                        forUpdate: true,
                    })
                    .elementAt(0);

                await openItem.$.set({
                    forcedAmountPaid: 575,
                    closeReason: '#Forced close',
                    closeText: 'Forced close test',
                });
                await openItem.$.save();

                const paidOpenItem = await context
                    .query(xtremFinance.nodes.AccountsReceivableOpenItem, {
                        filter: { accountsReceivableInvoice: { _id: '#SIPZ2250001|salesInvoice' } },
                        first: 1,
                    })
                    .elementAt(0);
                assert.strictEqual((await paidOpenItem.forcedAmountPaid).toString(), '575');
                assert.strictEqual((await paidOpenItem.transactionAmountPaid).toString(), '575');
                assert.strictEqual((await paidOpenItem.companyAmountPaid).toString(), '36.23');
                assert.strictEqual((await paidOpenItem.financialSiteAmountPaid).toString(), '36.23');
                assert.strictEqual(await paidOpenItem.status, 'partiallyPaid');
                assert.strictEqual(await (await paidOpenItem.closeReason)?.id, 'Forced close');
                assert.strictEqual(await paidOpenItem.closeText, 'Forced close test');

                const invoice = await context.read(xtremFinance.nodes.AccountsReceivableInvoice, {
                    _id: '#SIPZ2250001|salesInvoice',
                });
                assert.strictEqual(await invoice.paymentStatus, 'partiallyPaid');
            },
            {
                today: '2025-01-24',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));
});
