import { date, Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as Sinon from 'sinon';
import * as xtremFinance from '../../../lib/index';

describe('Accounts receivable invoice - test ', () => {
    it('Create the Accounts receivable invoice', () =>
        Test.withContext(
            async context => {
                const accountsReceivableInvoice = await context.create(xtremFinance.nodes.AccountsReceivableInvoice, {
                    financialSite: '#US006',
                    description: 'Accounts receivable invoice',
                    reference: 'Sales invoice',
                    origin: 'invoice',
                    type: 'salesInvoice',
                    number: 'INV-0001',
                    billToCustomer: '#US019',
                    dueDate: date.today(),
                    totalAmountExcludingTax: 2000,
                    totalAmountIncludingTax: 2000,

                    account: '#1|TEST_US_DEFAULT',

                    lines: [
                        {
                            // financialSite: 6,
                            taxDate: date.parse('2020-05-05'),
                            account: '#2|TEST_US_DEFAULT',
                            lineType: 'goods',
                            amountExcludingTax: 1000,
                            amountIncludingTax: 1000,
                            description: 'INV-0001',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US024',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 100,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US023',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 200,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US019',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 700,
                                },
                            ],
                        },
                        {
                            // financialSite: { id: 'US006' },
                            taxDate: date.parse('2020-10-05'),
                            account: '#2|TEST_US_DEFAULT',
                            lineType: 'services',
                            amountExcludingTax: 1500,
                            amountIncludingTax: 1500,
                            description: 'INV-0001-1',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US024',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 150,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US023',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 300,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US017',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE2',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 800,
                                },
                            ],
                        },
                    ],
                });

                await accountsReceivableInvoice.$.save();

                const savedAccountsReceivableInvoice = await context.read(
                    xtremFinance.nodes.AccountsReceivableInvoice,
                    {
                        number: 'INV-0001',
                        type: 'salesInvoice',
                    },
                );

                assert.strictEqual(await savedAccountsReceivableInvoice.number, 'INV-0001');
                assert.strictEqual(await savedAccountsReceivableInvoice.financialSiteName, 'Chem. Boston');
                assert.strictEqual(await savedAccountsReceivableInvoice.financialSiteTaxIdNumber, '2020-1234-020');
                assert.strictEqual(
                    await (
                        await (
                            await savedAccountsReceivableInvoice.billToCustomer
                        ).businessEntity
                    ).id,
                    'US019',
                );
                assert.strictEqual(
                    await savedAccountsReceivableInvoice.billToCustomerName,
                    'Dépot de TOULOUSE - Sud Ouest',
                );
                assert.strictEqual(await savedAccountsReceivableInvoice.billToCustomerTaxIdNumber, '***********');
                assert.strictEqual(await (await savedAccountsReceivableInvoice.currency).id, 'USD');
                assert.strictEqual(
                    await (
                        await savedAccountsReceivableInvoice.paymentTerm
                    ).name,
                    'Payment due upon receipt',
                );
                assert.strictEqual(await savedAccountsReceivableInvoice.postingStatus, 'draft');
                assert.strictEqual(await savedAccountsReceivableInvoice.isPrinted, false);
                assert.strictEqual(
                    (await savedAccountsReceivableInvoice.invoiceDate).value,
                    date.parse('2020-11-24', context.currentLocale as any).value,
                );
                assert.strictEqual(await savedAccountsReceivableInvoice.lines.length, 2);

                const arInvoiceLine =
                    (
                        await context
                            .query(xtremFinance.nodes.AccountsReceivableInvoiceLine, {
                                orderBy: { _id: -1 },
                                first: 1,
                            })
                            .toArray()
                    )[0] || null;

                assert.strictEqual(String(await (await arInvoiceLine.currency).id), 'USD');
                assert.strictEqual(String(await (await arInvoiceLine.providerSite).id), 'US006');
                assert.strictEqual(String(await (await arInvoiceLine.financialSite).id), 'US006');

                const arInvoiceLineDimension =
                    (
                        await context
                            .query(xtremFinance.nodes.AccountsReceivableInvoiceLineDimension, {
                                orderBy: { _id: -1 },
                                first: 1,
                            })
                            .toArray()
                    )[0] || null;

                assert.strictEqual(
                    String(await (await (await arInvoiceLineDimension.customer)?.businessEntity)?.id),
                    'US017',
                );
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension01, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension02, null);
                assert.strictEqual(String(await (await arInvoiceLineDimension.dimension03)?.id), 'DIMTYPE1VALUE2');
                assert.strictEqual(String(await (await arInvoiceLineDimension.dimension04)?.id), 'DIMTYPE2VALUE2');
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension05, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension06, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension07, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension08, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension09, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension10, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension11, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension12, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension13, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension14, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension15, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension16, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension17, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension18, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension19, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension20, null);
            },
            {
                today: '2020-11-24',
            },
        ));
    it('Posts a posted accounts receivable invoice - fail', () =>
        Test.withContext(async context => {
            const arInvoice = await context.read(xtremFinance.nodes.AccountsReceivableInvoice, {
                number: 'AR-**********',
            });
            await assert.isRejected(xtremFinance.nodes.AccountsReceivableInvoice.post(context, arInvoice), {
                message: 'Post failed.',
                diagnoses: [
                    {
                        message:
                            '~The posting status is not Draft nor Error. The accounts receivable invoice cannot be posted',

                        severity: 4,
                        path: [],
                    },
                ],
            });
        }));
    it('Posts an accounts receivable invoice', () =>
        Test.withContext(async context => {
            let arInvoice = await context.read(
                xtremFinance.nodes.AccountsReceivableInvoice,
                { number: 'AR-**********' },
                { forUpdate: true },
            );
            const postReturn = await xtremFinance.nodes.AccountsReceivableInvoice.post(context, arInvoice);
            arInvoice = await context.read(xtremFinance.nodes.AccountsReceivableInvoice, { number: 'AR-**********' });
            assert.strictEqual(postReturn, 'The accounts receivable invoice has been posted.');
            assert.strictEqual(await arInvoice.postingStatus, 'inProgress');
            assert.strictEqual(await arInvoice.openItems.length, 1);
            const openItem = await arInvoice.openItems.at(0);
            assert.strictEqual(await (await openItem?.businessEntity)?.id, 'US017');
            assert.strictEqual((await openItem?.businessEntityPayment)?._id, 4);
            assert.strictEqual(await (await openItem?.currency)?.id, 'EUR');
            assert.strictEqual((await openItem?.dueDate)?.toString(), '2021-10-05');
            assert.strictEqual(await openItem?.status, 'notPaid');
            assert.strictEqual((await openItem?.transactionAmountDue)?.toString(), '3300');
            assert.strictEqual((await openItem?.transactionAmountPaid)?.toString(), '0');
            assert.strictEqual(await openItem?.type, 'customer');
        }));
    it('Create Accounts receivable invoice in foreign currency use default values', () =>
        Test.withContext(
            async context => {
                const accountsReceivableInvoice = await context.create(xtremFinance.nodes.AccountsReceivableInvoice, {
                    financialSite: '#ETS1-S01',
                    description: 'Invoice in USD',
                    reference: 'Sales invoice',
                    origin: 'invoice',
                    type: 'salesInvoice',
                    number: 'INV-0001-USD',
                    billToCustomer: '#US017',
                    dueDate: date.today(),
                    totalAmountExcludingTax: 1000,
                    totalAmountIncludingTax: 1200,
                    totalTaxAmount: 200,
                    account: '#********|FR_DEFAULT',
                    currency: { id: 'USD' },

                    lines: [
                        {
                            taxDate: date.parse('2022-02-05'),
                            account: '#********|FR_DEFAULT',
                            lineType: 'goods',
                            amountExcludingTax: 1000,
                            amountIncludingTax: 1200,
                            taxAmount: 200,
                            description: 'INV-0001-USD',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'ETS1-S01',
                                        stockSite: 'ETS1-S01',
                                        customer: 'US017',
                                        supplier: '',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType01: '300',
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 1200,
                                },
                            ],
                        },
                    ],
                });

                await accountsReceivableInvoice.$.save();

                const savedAccountsReceivableInvoice = await context.read(
                    xtremFinance.nodes.AccountsReceivableInvoice,
                    {
                        number: 'INV-0001-USD',
                        type: 'salesInvoice',
                    },
                );

                assert.strictEqual(await savedAccountsReceivableInvoice.number, 'INV-0001-USD');
                assert.strictEqual(await (await savedAccountsReceivableInvoice.currency).id, 'USD');
                // check currency rate dependant values
                assert.strictEqual(
                    (await savedAccountsReceivableInvoice.fxRateDate).value,
                    date.parse('2020-11-24', context.currentLocale as any).value,
                );
                assert.strictEqual((await savedAccountsReceivableInvoice.companyFxRate).toString(), '1');
                assert.strictEqual((await savedAccountsReceivableInvoice.companyFxRateDivisor).toString(), '1.002');
                assert.strictEqual(
                    (await savedAccountsReceivableInvoice.totalCompanyAmountExcludingTax).toString(),
                    '998',
                );
                assert.strictEqual(
                    (await savedAccountsReceivableInvoice.totalCompanyAmountIncludingTax).toString(),
                    '1197.6',
                );
                assert.strictEqual((await savedAccountsReceivableInvoice.totalCompanyTaxAmount).toString(), '199.6');
                assert.strictEqual(await savedAccountsReceivableInvoice.rateDescription, '1 USD = 0.********* EUR');
            },
            {
                today: '2020-11-24',
            },
        ));
    it('Create Accounts receivable invoice in foreign currency with forced values', () =>
        Test.withContext(
            async context => {
                const accountsReceivableInvoice = await context.create(xtremFinance.nodes.AccountsReceivableInvoice, {
                    financialSite: '#ETS1-S01',
                    description: 'Invoice in USD',
                    reference: 'Sales invoice',
                    origin: 'invoice',
                    type: 'salesInvoice',
                    number: 'INV-0002-USD',
                    billToCustomer: '#US017',
                    dueDate: date.today(),
                    totalAmountExcludingTax: 1000,
                    totalAmountIncludingTax: 1200,
                    totalTaxAmount: 200,
                    account: '#********|FR_DEFAULT',
                    currency: { id: 'USD' },
                    fxRateDate: date.parse('2022-02-04'),
                    companyFxRate: 1.5,
                    companyFxRateDivisor: 1,

                    lines: [
                        {
                            taxDate: date.parse('2022-02-05'),
                            account: '#********|FR_DEFAULT',
                            lineType: 'goods',
                            amountExcludingTax: 1000,
                            amountIncludingTax: 1200,
                            taxAmount: 200,
                            description: 'INV-0002-USD',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'ETS1-S01',
                                        stockSite: 'ETS1-S01',
                                        customer: 'US017',
                                        supplier: '',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType01: '300',
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 1200,
                                },
                            ],
                        },
                    ],
                });

                await accountsReceivableInvoice.$.save();

                const savedAccountsReceivableInvoice = await context.read(
                    xtremFinance.nodes.AccountsReceivableInvoice,
                    {
                        number: 'INV-0002-USD',
                        type: 'salesInvoice',
                    },
                );

                assert.strictEqual(await savedAccountsReceivableInvoice.number, 'INV-0002-USD');
                assert.strictEqual(await (await savedAccountsReceivableInvoice.currency).id, 'USD');
                // check currency rate dependant values
                assert.strictEqual(
                    (await savedAccountsReceivableInvoice.fxRateDate).value,
                    date.parse('2022-02-04', context.currentLocale as any).value,
                );
                assert.strictEqual((await savedAccountsReceivableInvoice.companyFxRate).toString(), '1.5');
                assert.strictEqual((await savedAccountsReceivableInvoice.companyFxRateDivisor).toString(), '1');
                assert.strictEqual(
                    (await savedAccountsReceivableInvoice.totalCompanyAmountExcludingTax).toString(),
                    '1500',
                );
                assert.strictEqual(
                    (await savedAccountsReceivableInvoice.totalCompanyAmountIncludingTax).toString(),
                    '1800',
                );
                assert.strictEqual((await savedAccountsReceivableInvoice.totalCompanyTaxAmount).toString(), '300');
                assert.strictEqual(await savedAccountsReceivableInvoice.rateDescription, '1 USD = 1.5 EUR');
            },
            {
                today: '2020-11-24',
            },
        ));

    it('Create the Accounts receivable invoice attribute type restricted to', () =>
        Test.withContext(
            async context => {
                const accountsReceivableInvoice = await context.create(xtremFinance.nodes.AccountsReceivableInvoice, {
                    financialSite: '#US006',
                    description: 'Accounts receivable invoice',
                    reference: 'Sales invoice',
                    origin: 'invoice',
                    type: 'salesInvoice',
                    number: 'INV-0001',
                    billToCustomer: '#US019',
                    dueDate: date.today(),
                    totalAmountExcludingTax: 2000,
                    totalAmountIncludingTax: 2000,

                    account: '#1|TEST_US_DEFAULT',

                    lines: [
                        {
                            taxDate: date.parse('2020-05-05'),
                            account: '#2|TEST_US_DEFAULT',
                            lineType: 'goods',
                            amountExcludingTax: 1000,
                            amountIncludingTax: 1000,
                            description: 'INV-0001',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US024',
                                        supplier: 'US018',
                                        project: '',
                                        task: 'Task1',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 100,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US023',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 200,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US019',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 700,
                                },
                            ],
                        },
                        {
                            // financialSite: { id: 'US006' },
                            taxDate: date.parse('2020-10-05'),
                            account: '#2|TEST_US_DEFAULT',
                            lineType: 'services',
                            amountExcludingTax: 1500,
                            amountIncludingTax: 1500,
                            description: 'INV-0001-1',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US024',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 150,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US023',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 300,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US017',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE2',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 800,
                                },
                            ],
                        },
                    ],
                });

                await assert.isRejected(accountsReceivableInvoice.$.save());
                assert.deepEqual(accountsReceivableInvoice.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['lines', '-**********', 'attributesAndDimensions', '-**********'],
                        message: 'The Project attribute needs to be filled in.',
                    },
                ]);
            },
            {
                today: '2020-11-24',
            },
        ));
});

describe('Accounts payable invoice resend notification', () => {
    it('Resend notification for finance - success', () =>
        Test.withContext(
            async context => {
                const arInvoice: xtremFinance.nodes.AccountsReceivableInvoice = await context.read(
                    xtremFinance.nodes.AccountsReceivableInvoice,
                    { number: 'SIG1250001' },
                );

                const notifySpy = Sinon.spy(context, 'notify');
                await xtremFinance.nodes.AccountsReceivableInvoice.resendNotificationForFinance(context, arInvoice);
                assert.equal(notifySpy.getCalls().length, 1);
                const financeNotificationTopic = notifySpy.args[0][0];
                const financeNotificationPayload = notifySpy.args[0][1];

                const financeTransaction = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, {
                        filter: {
                            documentNumber: 'SIG1250001',
                            documentType: 'arInvoice',
                        },
                    })
                    .at(0);

                assert.deepEqual(
                    [financeNotificationTopic, financeNotificationPayload],
                    [
                        'AccountingInterfaceListener/createJournalsFromAccountingStagingJob/start',
                        {
                            batchTrackingId: undefined,
                            journalsCreatedData: false,
                            filter: `{"batchId":"${(await financeTransaction?.batchId) || ''}","isProcessed":false}`,
                        },
                    ],
                );
            },
            { today: '2025-02-05' },
        ));
});
