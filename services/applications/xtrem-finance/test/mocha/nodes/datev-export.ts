import { DateValue, Test } from '@sage/xtrem-core';
import { DateRange } from '@sage/xtrem-date-time';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremFinance from '../../../index';

describe('Test node DatevExport', () => {
    it('Check default values and date range', () =>
        Test.withContext(
            async context => {
                const datevExport = await context.create(xtremFinance.nodes.DatevExport, {
                    id: 'export01',
                    company: '#UT02',
                    datevConsultantNumber: 1,
                    datevCustomerNumber: 1,
                    dateRange: new DateRange(DateValue.make(2024, 7, 1), DateValue.make(2024, 7, 31)),
                    fiscalYearStart: DateValue.make(2024, 1, 1),
                    doAccounts: true,
                    doCustomersSuppliers: true,
                    doJournalEntries: true,
                });

                await datevExport.$.save();
                assert.deepEqual(datevExport.$.context.diagnoses, []);
                assert.deepEqual(await datevExport.datevConsultantNumber, 1);
                assert.deepEqual(await datevExport.datevCustomerNumber, 1);
                assert.deepEqual(await datevExport.skrCoa, 3);
                assert.deepEqual(await datevExport.isLocked, true);
                assert.deepEqual(await datevExport.timeStamp, null);
                assert.deepEqual((await datevExport.dateRange).start?.toString(), '2024-07-01');
                assert.deepEqual((await datevExport.dateRange).end?.toString(), '2024-07-31');
                assert.deepEqual(await datevExport.datevExportAccounts.length, 0);
            },
            {
                today: '2024-01-01',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption],
            },
        ));

    it('Check datevExport with datevExportAccounts', () =>
        Test.withContext(
            async context => {
                const datevExport = await context.create(xtremFinance.nodes.DatevExport, {
                    id: 'export01',
                    company: '#UT02',
                    datevConsultantNumber: 1,
                    datevCustomerNumber: 1,
                    dateRange: new DateRange(DateValue.make(2024, 7, 1), DateValue.make(2024, 7, 31)),
                    fiscalYearStart: DateValue.make(2024, 1, 1),
                    doAccounts: true,
                    doCustomersSuppliers: true,
                    doJournalEntries: true,
                    datevExportAccounts: [
                        {
                            account: '#3800|DE_DEFAULT',
                            datevId: 1001,
                            name: 'Bezugsnebenkosten',
                        },
                        {
                            account: '#3830|DE_DEFAULT',
                            datevId: 1002,
                            name: 'Leergut',
                        },
                    ],
                });
                await datevExport.$.save();
                assert.deepEqual(datevExport.$.context.diagnoses, []);
                const datevExport2 = await context.read(xtremFinance.nodes.DatevExport, {
                    _id: '#export01',
                });
                assert.deepEqual(await datevExport2.datevExportAccounts.length, 2);
                const account1 = await datevExport2.datevExportAccounts.elementAt(0);
                assert.deepEqual(await (await account1.account).id, '3800');
                assert.deepEqual(await account1.name, 'Bezugsnebenkosten');
                assert.deepEqual(await account1.datevId, 1001);
                const account2 = await datevExport2.datevExportAccounts.elementAt(1);
                assert.deepEqual(await (await account2.account).id, '3830');
                assert.deepEqual(await account2.name, 'Leergut');
                assert.deepEqual(await account2.datevId, 1002);
            },
            {
                today: '2024-01-01',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption],
            },
        ));

    it('Check mutation datevExtraction', () =>
        Test.withContext(
            async context => {
                const datevExport = await context.create(xtremFinance.nodes.DatevExport, {
                    id: 'export01',
                    company: '#UT02',
                    datevConsultantNumber: 1,
                    datevCustomerNumber: 1,
                    dateRange: new DateRange(DateValue.make(2024, 9, 1), DateValue.make(2024, 9, 30)),
                    fiscalYearStart: DateValue.make(2024, 1, 1),
                    dimensionType1: 'dimensionType03',
                    attributeType2: 'task',
                    doAccounts: true,
                    doCustomersSuppliers: true,
                    doJournalEntries: true,
                });
                await datevExport.$.save();
                assert.deepEqual(datevExport.$.context.diagnoses, []);
                await xtremFinance.nodes.DatevExport.datevExtraction(context, 'export01');
                const datevExport2 = await context.read(xtremFinance.nodes.DatevExport, {
                    _id: '#export01',
                });
                assert.deepEqual(await datevExport2.datevExportAccounts.length, 38);
                const account1 = await datevExport2.datevExportAccounts.elementAt(0);
                assert.deepEqual(await (await account1.account).id, '1400');
                assert.deepEqual(await account1.name, 'Forderungen');
                assert.deepEqual(await account1.datevId, 1400);
                const account2 = await datevExport2.datevExportAccounts.elementAt(1);
                assert.deepEqual(await (await account2.account).id, '1571');
                assert.deepEqual(await account2.name, 'Vorsteuer 7 %');
                assert.deepEqual(await account2.datevId, 1571);
                const account3 = await datevExport2.datevExportAccounts.elementAt(2);
                assert.deepEqual(await (await account3.account).id, '1572');
                assert.deepEqual(await account3.name, 'Abziehbare Vorsteuer aus innergemeinschaftlichem Erwerb 7 %');
                assert.deepEqual(await account3.datevId, 1572);
                const account4 = await datevExport2.datevExportAccounts.elementAt(3);
                assert.deepEqual(await (await account4.account).id, '1574');
                assert.deepEqual(await account4.name, 'Abziehbare Vorsteuer aus innergemeinschaftlichem Erwerb 19 %');
                assert.deepEqual(await account4.datevId, 1574);
                assert.deepEqual(await datevExport2.datevExportBusinessRelations.length, 36);

                const customer1 = await datevExport2.datevExportBusinessRelations.elementAt(14);
                assert.deepEqual(await (await (await customer1.businessRelation)?.businessEntity)?.id, 'US017');
                assert.deepEqual(await customer1.name, 'Siège social S01 PARIS');
                assert.deepEqual(await customer1.datevId, 70001);
                assert.deepEqual(await customer1.taxIdNumber, 'FR58483849894');
                assert.deepEqual(await (await customer1.country)?.id, 'FR');

                assert.deepEqual(await datevExport2.datevExportJournalEntryLines.length, 5);
                let journalEntryLine = await datevExport2.datevExportJournalEntryLines.elementAt(0);
                assert.deepEqual(await journalEntryLine.datevSign, 'S');
                assert.deepEqual(await journalEntryLine.datevAccountId, 3970);
                assert.deepEqual(await journalEntryLine.datevContraAccountId, 3960);
                assert.deepEqual(await journalEntryLine.description, 'Stock');
                assert.deepEqual(await journalEntryLine.number, 'IJ240001');
                assert.deepEqual(await journalEntryLine.siteTaxIdNumber, 'DE*********');
                assert.deepEqual(await journalEntryLine.dimension1, '');
                assert.deepEqual(await journalEntryLine.dimension2, '');
                assert.equal(await journalEntryLine.transactionValue, 400);
                journalEntryLine = await datevExport2.datevExportJournalEntryLines.elementAt(1);
                assert.deepEqual(await journalEntryLine.datevSign, 'S');
                assert.deepEqual(await journalEntryLine.datevAccountId, 3970);
                assert.deepEqual(await journalEntryLine.datevContraAccountId, 3960);
                assert.deepEqual(await journalEntryLine.description, 'Stock');
                assert.deepEqual(await journalEntryLine.number, 'IJ240002');
                assert.deepEqual(await journalEntryLine.siteTaxIdNumber, 'DE*********');
                assert.deepEqual(await journalEntryLine.dimension1, '');
                assert.deepEqual(await journalEntryLine.dimension2, '');
                assert.equal(await journalEntryLine.transactionValue, 400);
                journalEntryLine = await datevExport2.datevExportJournalEntryLines.elementAt(2);
                assert.deepEqual(await journalEntryLine.datevSign, 'S');
                assert.deepEqual(await journalEntryLine.datevAccountId, 3970);
                assert.deepEqual(await journalEntryLine.datevContraAccountId, 3960);
                assert.deepEqual(await journalEntryLine.description, 'Stock');
                assert.deepEqual(await journalEntryLine.number, 'IJ240003');
                assert.deepEqual(await journalEntryLine.siteTaxIdNumber, 'DE*********');
                assert.deepEqual(await journalEntryLine.dimension1, 'MANUFACTURING');
                assert.deepEqual(await journalEntryLine.dimension2, 'Task1');
                assert.equal(await journalEntryLine.transactionValue, 10);
                journalEntryLine = await datevExport2.datevExportJournalEntryLines.elementAt(3);
                assert.deepEqual(await journalEntryLine.datevSign, 'S');
                assert.deepEqual(await journalEntryLine.datevAccountId, 3800);
                assert.deepEqual(await journalEntryLine.datevContraAccountId, 70999);
                assert.deepEqual(await journalEntryLine.description, 'AP invoice');
                assert.deepEqual(await journalEntryLine.number, 'PI240004');
                assert.deepEqual(await journalEntryLine.siteTaxIdNumber, 'DE*********');
                assert.deepEqual(await journalEntryLine.businessEntityTaxIdNumber, 'DE121234345');
                assert.deepEqual(await journalEntryLine.dimension1, '');
                assert.deepEqual(await journalEntryLine.dimension2, '');
                assert.equal(await journalEntryLine.transactionValue, 2000);
                journalEntryLine = await datevExport2.datevExportJournalEntryLines.elementAt(4);
                assert.deepEqual(await journalEntryLine.datevSign, 'H');
                assert.deepEqual(await journalEntryLine.datevAccountId, 8400);
                assert.deepEqual(await journalEntryLine.datevContraAccountId, 10999);
                assert.deepEqual(await journalEntryLine.description, 'AR invoice');
                assert.deepEqual(await journalEntryLine.number, 'SIG1240001');
                assert.deepEqual(await journalEntryLine.siteTaxIdNumber, 'DE*********');
                assert.deepEqual(await journalEntryLine.businessEntityTaxIdNumber, 'DE987654321');
                assert.deepEqual(await journalEntryLine.dimension1, '');
                assert.deepEqual(await journalEntryLine.dimension2, '');
                assert.equal(await journalEntryLine.transactionValue, 10000);
            },
            {
                today: '2024-01-01',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption],
            },
        ));

    it('Check datevExport with datevExportBusinessRelations', () =>
        Test.withContext(
            async context => {
                const datevExport = await context.create(xtremFinance.nodes.DatevExport, {
                    id: 'export01',
                    company: '#UT02',
                    datevConsultantNumber: 1,
                    datevCustomerNumber: 1,
                    dateRange: new DateRange(DateValue.make(2024, 7, 1), DateValue.make(2024, 7, 31)),
                    fiscalYearStart: DateValue.make(2024, 1, 1),
                    doAccounts: true,
                    doCustomersSuppliers: true,
                    doJournalEntries: true,
                    datevExportBusinessRelations: [
                        {
                            businessRelation: '#Customer|US019',
                            datevId: 10001,
                            name: 'customer 01',
                            taxIdNumber: '*********',
                            country: '#DE',
                        },
                        {
                            businessRelation: '#Supplier|US017',
                            datevId: 70001,
                            name: 'supplier 01',
                            taxIdNumber: '*********',
                            country: '#FR',
                        },
                    ],
                });
                await datevExport.$.save();
                assert.deepEqual(datevExport.$.context.diagnoses, []);
                const datevExport2 = await context.read(xtremFinance.nodes.DatevExport, {
                    _id: '#export01',
                });
                assert.deepEqual(await datevExport2.datevExportBusinessRelations.length, 2);
                const customer1 = await datevExport2.datevExportBusinessRelations.elementAt(0);
                assert.deepEqual(await (await customer1.country).id, 'DE');
                assert.deepEqual(await (await customer1.businessRelation)?.id, 'US019');
                assert.deepEqual(await customer1.name, 'customer 01');
                assert.deepEqual(await customer1.taxIdNumber, '*********');
                assert.deepEqual(await customer1.datevId, 10001);
                assert.deepEqual((await customer1.businessRelation) instanceof xtremMasterData.nodes.Customer, true);
                assert.deepEqual(
                    await (
                        await ((await customer1.businessRelation) as xtremMasterData.nodes.Customer)?.billToCustomer
                    )?.id,
                    'US019',
                );
                const supplier1 = await datevExport2.datevExportBusinessRelations.elementAt(1);
                assert.deepEqual(await (await supplier1.country).id, 'FR');
                assert.deepEqual(await (await supplier1.businessRelation)?.id, 'US017');
                assert.deepEqual(await supplier1.name, 'supplier 01');
                assert.deepEqual(await supplier1.taxIdNumber, '*********');
                assert.deepEqual(await supplier1.datevId, 70001);
                assert.deepEqual((await supplier1.businessRelation) instanceof xtremMasterData.nodes.Supplier, true);
                assert.deepEqual(
                    await (
                        await ((await supplier1.businessRelation) as xtremMasterData.nodes.Supplier)?.billBySupplier
                    )?.id,
                    'US017',
                );
            },
            {
                today: '2024-01-01',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption],
            },
        ));

    it('Check mutation datevExport', () =>
        Test.withContext(
            async context => {
                const datevExport = await context.create(xtremFinance.nodes.DatevExport, {
                    id: 'export01',
                    company: '#UT02',
                    datevConsultantNumber: 1,
                    datevCustomerNumber: 1,
                    dateRange: new DateRange(DateValue.make(2024, 7, 1), DateValue.make(2024, 7, 31)),
                    fiscalYearStart: DateValue.make(2024, 1, 1),
                    doAccounts: true,
                    doCustomersSuppliers: true,
                    doJournalEntries: true,
                });
                await datevExport.$.save();
                assert.deepEqual(datevExport.$.context.diagnoses, []);
                await xtremFinance.nodes.DatevExport.datevExtraction(context, 'export01');
                const datevExport1 = await context.read(xtremFinance.nodes.DatevExport, {
                    _id: '#export01',
                });
                assert.deepEqual(await datevExport1.status, 'extracted');

                const expectedUserNotification = {
                    title: 'DATEV export files',
                    description: 'Files created: 3.\nWarnings: 33.',
                    icon: 'download',
                    level: 'success',
                    shouldDisplayToast: true,
                    actions: [
                        {
                            link: '@sage/xtrem-communication/SysNotificationState/eyJfaWQiOiIjIn0=',
                            title: 'History',
                            icon: 'link',
                            style: 'tertiary',
                        },
                        {
                            icon: 'link',
                            link: '', // it changes on each call
                            style: 'link',
                            title: 'Download accounts',
                        },
                        {
                            icon: 'link',
                            link: '', // it changes on each call
                            style: 'link',
                            title: 'Download customers and suppliers',
                        },
                        {
                            icon: 'link',
                            link: '', // it changes on each call
                            style: 'link',
                            title: 'Download journal entry lines',
                        },
                    ],
                };

                const notifyUserSpy = sinon.spy(context, 'notifyUser');

                await xtremFinance.nodes.DatevExport.datevExport(context, 'export01');

                assert.equal(notifyUserSpy.getCalls().length, 1);
                const notifyUserPayload = notifyUserSpy.args.at(0);
                assert.equal(notifyUserPayload?.length, 1);
                const userNotification = notifyUserPayload?.at(0);

                assert.equal(userNotification?.title, expectedUserNotification.title);
                assert.equal(userNotification?.description, expectedUserNotification.description);
                assert.equal(userNotification?.icon, expectedUserNotification.icon);
                assert.equal(userNotification?.level, expectedUserNotification.level);
                assert.equal(userNotification?.shouldDisplayToast, expectedUserNotification.shouldDisplayToast);

                assert.equal(userNotification?.actions.length, 4);
                assert.equal(userNotification?.actions?.at(0)?.link, expectedUserNotification.actions.at(0)?.link);
                assert.equal(userNotification?.actions?.at(0)?.title, expectedUserNotification.actions.at(0)?.title);
                assert.equal(userNotification?.actions?.at(0)?.icon, expectedUserNotification.actions.at(0)?.icon);
                assert.equal(userNotification?.actions?.at(0)?.style, expectedUserNotification.actions.at(0)?.style);

                assert.equal(userNotification?.actions.at(1)?.title, expectedUserNotification.actions.at(1)?.title);
                assert.equal(userNotification?.actions.at(1)?.icon, expectedUserNotification.actions.at(1)?.icon);
                assert.equal(userNotification?.actions.at(1)?.style, expectedUserNotification.actions.at(1)?.style);

                assert.equal(userNotification?.actions.at(2)?.title, expectedUserNotification.actions.at(2)?.title);
                assert.equal(userNotification?.actions.at(2)?.icon, expectedUserNotification.actions.at(2)?.icon);
                assert.equal(userNotification?.actions.at(2)?.style, expectedUserNotification.actions.at(2)?.style);

                assert.equal(userNotification?.actions.at(3)?.title, expectedUserNotification.actions.at(3)?.title);
                assert.equal(userNotification?.actions.at(3)?.icon, expectedUserNotification.actions.at(3)?.icon);
                assert.equal(userNotification?.actions.at(3)?.style, expectedUserNotification.actions.at(3)?.style);

                const datevExport2 = await context.read(xtremFinance.nodes.DatevExport, {
                    _id: '#export01',
                });
                assert.deepEqual(await datevExport2.status, 'exported');
            },
            {
                today: '2024-01-01',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption],
            },
        ));
});
