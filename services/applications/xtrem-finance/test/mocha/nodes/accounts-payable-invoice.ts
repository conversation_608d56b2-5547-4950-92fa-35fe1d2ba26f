import { date, Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as Sinon from 'sinon';
import * as xtremFinance from '../../../lib/index';

describe('Accounts payable invoice - test ', () => {
    before(() => {});
    it('Create the Accounts payable invoice', () =>
        Test.withContext(
            async context => {
                const accountsPayableInvoice = await context.create(xtremFinance.nodes.AccountsPayableInvoice, {
                    financialSite: '#US006',
                    description: 'Accounts payable invoice',
                    reference: 'Purchase invoice',
                    origin: 'invoice',
                    type: 'purchaseInvoice',
                    number: 'INV-0001',
                    billBySupplier: '#500',
                    totalAmountExcludingTax: 2000,
                    totalAmountIncludingTax: 2000,
                    dueDate: date.today(),
                    account: '#1|TEST_US_DEFAULT',

                    lines: [
                        {
                            // financialSite: 6,
                            taxDate: date.parse('2020-05-05'),
                            account: '#2|TEST_US_DEFAULT',
                            lineType: 'goods',
                            amountExcludingTax: 1000,
                            amountIncludingTax: 1000,
                            description: 'INV-0001',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 100,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US023',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 200,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US019',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 700,
                                },
                            ],
                        },
                        {
                            // financialSite: { id: 'US006' },
                            taxDate: date.parse('2020-10-05'),
                            account: '#2|TEST_US_DEFAULT',
                            lineType: 'services',
                            amountExcludingTax: 1500,
                            amountIncludingTax: 1500,
                            description: 'INV-0001-1',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 150,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 300,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE2',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 800,
                                },
                            ],
                        },
                    ],
                });

                await accountsPayableInvoice.$.save();

                const savedAccountsPayableInvoice = await context.read(xtremFinance.nodes.AccountsPayableInvoice, {
                    number: 'INV-0001',
                    type: 'purchaseInvoice',
                });

                assert.strictEqual(await savedAccountsPayableInvoice.number, 'INV-0001');
                assert.strictEqual(await savedAccountsPayableInvoice.financialSiteName, 'Chem. Boston');
                assert.strictEqual(await savedAccountsPayableInvoice.financialSiteTaxIdNumber, '2020-1234-020');
                assert.strictEqual(
                    await (
                        await (
                            await savedAccountsPayableInvoice.billBySupplier
                        ).businessEntity
                    ).id,
                    '500',
                );
                assert.strictEqual(await savedAccountsPayableInvoice.billBySupplierName, 'ZA 500');
                assert.strictEqual(await savedAccountsPayableInvoice.billBySupplierTaxIdNumber, '2016-4548-365');
                assert.strictEqual(await (await savedAccountsPayableInvoice.currency).id, 'ZAR');
                assert.strictEqual(await (await savedAccountsPayableInvoice.paymentTerm).name, 'Net 30');
                assert.strictEqual(await savedAccountsPayableInvoice.postingStatus, 'draft');
                assert.strictEqual(
                    (await savedAccountsPayableInvoice.invoiceDate).value,
                    date.parse('2020-11-24', context.currentLocale as any).value,
                );
                assert.strictEqual(await savedAccountsPayableInvoice.lines.length, 2);

                const arInvoiceLine =
                    (
                        await context
                            .query(xtremFinance.nodes.AccountsPayableInvoiceLine, {
                                orderBy: { _id: -1 },
                                first: 1,
                            })
                            .toArray()
                    )[0] || null;

                assert.strictEqual(String(await (await arInvoiceLine.currency).id), 'ZAR');
                assert.strictEqual(String(await (await arInvoiceLine.recipientSite).id), 'US006');
                assert.strictEqual(String(await (await arInvoiceLine.financialSite).id), 'US006');

                const arInvoiceLineDimension =
                    (
                        await context
                            .query(xtremFinance.nodes.AccountsPayableInvoiceLineDimension, {
                                orderBy: { _id: -1 },
                                first: 1,
                            })
                            .toArray()
                    )[0] || null;

                assert.strictEqual(
                    String(await (await (await arInvoiceLineDimension.supplier)?.businessEntity)?.id),
                    'US018',
                );
                assert.strictEqual(String(await (await arInvoiceLineDimension.financialSite)?.id), 'US001');
                assert.strictEqual(String(await (await arInvoiceLineDimension.stockSite)?.id), 'US001');
                assert.strictEqual(String(await (await arInvoiceLineDimension.project)?.id), 'AttPROJ');
                assert.deepStrictEqual(await arInvoiceLineDimension.customer, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.item, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.businessSite, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.manufacturingSite, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.employee, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension01, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension02, null);
                assert.strictEqual(String(await (await arInvoiceLineDimension.dimension03)?.id), 'DIMTYPE1VALUE2');
                assert.strictEqual(String(await (await arInvoiceLineDimension.dimension04)?.id), 'DIMTYPE2VALUE2');
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension05, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension06, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension07, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension08, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension09, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension10, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension11, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension12, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension13, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension14, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension15, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension16, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension17, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension18, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension19, null);
                assert.deepStrictEqual(await arInvoiceLineDimension.dimension20, null);
            },
            {
                today: '2020-11-24',
            },
        ));
    it('Create the Accounts payable invoice with mandatory attributes on accounts', () =>
        Test.withContext(
            async context => {
                const accountsPayableInvoice = await context.create(xtremFinance.nodes.AccountsPayableInvoice, {
                    financialSite: '#US006',
                    description: 'Accounts payable invoice',
                    reference: 'Purchase invoice',
                    origin: 'invoice',
                    type: 'purchaseInvoice',
                    number: 'INV-0001',
                    billBySupplier: '#500',
                    totalAmountExcludingTax: 2000,
                    totalAmountIncludingTax: 2000,
                    dueDate: date.today(),
                    account: '#1|TEST_US_DEFAULT',

                    lines: [
                        {
                            // financialSite: 6,
                            taxDate: date.parse('2020-05-05'),
                            account: '#2|TEST_US_DEFAULT',
                            lineType: 'goods',
                            amountExcludingTax: 1000,
                            amountIncludingTax: 1000,
                            description: 'INV-0001',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 100,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US023',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 200,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US019',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 700,
                                },
                            ],
                        },
                        {
                            // financialSite: { id: 'US006' },
                            taxDate: date.parse('2020-10-05'),
                            account: '#2|TEST_US_DEFAULT',
                            lineType: 'services',
                            amountExcludingTax: 1500,
                            amountIncludingTax: 1500,
                            description: 'INV-0001-1',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 150,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 300,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE2',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 800,
                                },
                            ],
                        },
                    ],
                });
                await assert.isRejected(accountsPayableInvoice.$.save(), 'The record was not created.');
                assert.deepEqual(context.diagnoses, [
                    {
                        severity: 3,
                        path: [],
                        message:
                            'Required account dimensions [Dimension type 1] have not been declared for account 2 on journal line 1.',
                    },
                ]);
            },
            {
                today: '2020-11-24',
            },
        ));
    it('Posts a posted payable receivable invoice - fail', () =>
        Test.withContext(async context => {
            const apInvoice = await context.read(
                xtremFinance.nodes.AccountsPayableInvoice,
                { number: 'AP-**********' },
                { forUpdate: true },
            );
            await assert.isRejected(
                xtremFinance.nodes.AccountsPayableInvoice.post(context, apInvoice),
                /The posting status is not Draft nor Error. The accounts payable invoice cannot be posted./,
            );
        }));
    it('Posts an accounts payable invoice', () =>
        Test.withContext(async context => {
            let apInvoice = await context.read(
                xtremFinance.nodes.AccountsPayableInvoice,
                { number: 'AP-**********' },
                { forUpdate: true },
            );
            const postReturn = await xtremFinance.nodes.AccountsPayableInvoice.post(context, apInvoice);
            apInvoice = await context.read(xtremFinance.nodes.AccountsPayableInvoice, { number: 'AP-**********' });
            assert.strictEqual(postReturn, 'The accounts payable invoice has been posted.');
            assert.strictEqual(await apInvoice.postingStatus, 'inProgress');
            assert.strictEqual(await apInvoice.openItems.length, 1);
            assert.strictEqual(await (await (await apInvoice.openItems.at(0))?.businessEntity)?.id, 'LECLERC');
            assert.strictEqual(await (await (await apInvoice.openItems.at(0))?.businessEntityPayment)?.id, 'LECLERC');
            assert.strictEqual(await (await (await apInvoice.openItems.at(0))?.currency)?.id, 'EUR');
            assert.strictEqual((await (await apInvoice.openItems.at(0))?.dueDate)?.toString(), '2021-09-04');
            assert.strictEqual(await (await apInvoice.openItems.at(0))?.status, 'notPaid');
            assert.strictEqual((await (await apInvoice.openItems.at(0))?.transactionAmountDue)?.toString(), '1100');
            assert.strictEqual((await (await apInvoice.openItems.at(0))?.transactionAmountPaid)?.toString(), '0');
            assert.strictEqual(await (await apInvoice.openItems.at(0))?.type, 'supplier');
        }));
    it('Create Accounts payable invoice in foreign currency use default values', () =>
        Test.withContext(
            async context => {
                const accountsPayableInvoice = await context.create(xtremFinance.nodes.AccountsPayableInvoice, {
                    financialSite: '#ETS1-S01',
                    description: 'Accounts payable invoice in USD',
                    reference: 'Purchase invoice',
                    origin: 'invoice',
                    type: 'purchaseInvoice',
                    number: 'INV-0001-USD',
                    billBySupplier: '#500',
                    totalAmountExcludingTax: 1000,
                    totalAmountIncludingTax: 1200,
                    totalTaxAmount: 200,
                    dueDate: date.today(),
                    account: '#********|FR_DEFAULT',
                    currency: { id: 'USD' },

                    lines: [
                        {
                            taxDate: date.parse('2022-02-05'),
                            account: '#********|FR_DEFAULT',
                            lineType: 'goods',
                            amountExcludingTax: 1000,
                            amountIncludingTax: 1200,
                            taxAmount: 200,
                            description: 'INV-0001-USD',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'ETS1-S01',
                                        stockSite: 'ETS1-S01',
                                        customer: '',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType01: '300',
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 2000,
                                },
                            ],
                        },
                    ],
                });

                await accountsPayableInvoice.$.save();

                const savedAccountsPayableInvoice = await context.read(xtremFinance.nodes.AccountsPayableInvoice, {
                    number: 'INV-0001-USD',
                    type: 'purchaseInvoice',
                });

                assert.strictEqual(await savedAccountsPayableInvoice.number, 'INV-0001-USD');
                assert.strictEqual(await (await savedAccountsPayableInvoice.currency).id, 'USD');
                // check currency rate dependant values
                assert.strictEqual(
                    (await savedAccountsPayableInvoice.fxRateDate).value,
                    date.parse('2020-11-24', context.currentLocale as any).value,
                );
                assert.strictEqual((await savedAccountsPayableInvoice.companyFxRate).toString(), '1');
                assert.strictEqual((await savedAccountsPayableInvoice.companyFxRateDivisor).toString(), '1.002');
                assert.strictEqual(
                    (await savedAccountsPayableInvoice.totalCompanyAmountExcludingTax).toString(),
                    '998',
                );
                assert.strictEqual(
                    (await savedAccountsPayableInvoice.totalCompanyAmountIncludingTax).toString(),
                    '1197.6',
                );
                assert.strictEqual((await savedAccountsPayableInvoice.totalCompanyTaxAmount).toString(), '199.6');
                assert.strictEqual(await savedAccountsPayableInvoice.rateDescription, '1 USD = 0.********* EUR');
            },
            {
                today: '2020-11-24',
            },
        ));
    it('Create Accounts payable invoice in foreign currency with forced values', () =>
        Test.withContext(
            async context => {
                const accountsPayableInvoice = await context.create(xtremFinance.nodes.AccountsPayableInvoice, {
                    financialSite: '#ETS1-S01',
                    description: 'Accounts payable invoice in USD',
                    reference: 'Purchase invoice',
                    origin: 'invoice',
                    type: 'purchaseInvoice',
                    number: 'INV-0002-USD',
                    billBySupplier: '#500',
                    totalAmountExcludingTax: 1000,
                    totalAmountIncludingTax: 1200,
                    totalTaxAmount: 200,
                    dueDate: date.today(),
                    account: '#********|FR_DEFAULT',
                    currency: { id: 'USD' },
                    fxRateDate: date.parse('2022-02-04'),
                    companyFxRate: 1.5,
                    companyFxRateDivisor: 1,

                    lines: [
                        {
                            taxDate: date.parse('2022-02-05'),
                            account: '#********|FR_DEFAULT',
                            lineType: 'goods',
                            amountExcludingTax: 1000,
                            amountIncludingTax: 1200,
                            taxAmount: 200,
                            description: 'INV-0002-USD',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'ETS1-S01',
                                        stockSite: 'ETS1-S01',
                                        customer: '',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType01: '300',
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 1200,
                                },
                            ],
                        },
                    ],
                });

                await accountsPayableInvoice.$.save();

                const savedAccountsPayableInvoice = await context.read(xtremFinance.nodes.AccountsPayableInvoice, {
                    number: 'INV-0002-USD',
                    type: 'purchaseInvoice',
                });

                assert.strictEqual(await savedAccountsPayableInvoice.number, 'INV-0002-USD');
                assert.strictEqual(await (await savedAccountsPayableInvoice.currency).id, 'USD');
                // check currency rate dependant values
                assert.strictEqual(
                    (await savedAccountsPayableInvoice.fxRateDate).value,
                    date.parse('2022-02-04', context.currentLocale as any).value,
                );
                assert.strictEqual((await savedAccountsPayableInvoice.companyFxRate).toString(), '1.5');
                assert.strictEqual((await savedAccountsPayableInvoice.companyFxRateDivisor).toString(), '1');
                assert.strictEqual(
                    (await savedAccountsPayableInvoice.totalCompanyAmountExcludingTax).toString(),
                    '1500',
                );
                assert.strictEqual(
                    (await savedAccountsPayableInvoice.totalCompanyAmountIncludingTax).toString(),
                    '1800',
                );
                assert.strictEqual((await savedAccountsPayableInvoice.totalCompanyTaxAmount).toString(), '300');
                assert.strictEqual(await savedAccountsPayableInvoice.rateDescription, '1 USD = 1.5 EUR');
            },
            {
                today: '2020-11-24',
            },
        ));

    it('Create the Accounts payable invoice attribute type restricted to', () =>
        Test.withContext(
            async context => {
                const accountsPayableInvoice = await context.create(xtremFinance.nodes.AccountsPayableInvoice, {
                    financialSite: '#US006',
                    description: 'Accounts payable invoice',
                    reference: 'Purchase invoice',
                    origin: 'invoice',
                    type: 'purchaseInvoice',
                    number: 'INV-0001',
                    billBySupplier: '#500',
                    totalAmountExcludingTax: 2000,
                    totalAmountIncludingTax: 2000,
                    dueDate: date.today(),
                    account: '#1|TEST_US_DEFAULT',

                    lines: [
                        {
                            // financialSite: 6,
                            taxDate: date.parse('2020-05-05'),
                            account: '#2|TEST_US_DEFAULT',
                            lineType: 'goods',
                            amountExcludingTax: 1000,
                            amountIncludingTax: 1000,
                            description: 'INV-0001',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        supplier: 'US018',
                                        project: '',
                                        task: 'Task1',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 100,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US023',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 200,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US019',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 700,
                                },
                            ],
                        },
                        {
                            // financialSite: { id: 'US006' },
                            taxDate: date.parse('2020-10-05'),
                            account: '#2|TEST_US_DEFAULT',
                            lineType: 'services',
                            amountExcludingTax: 1500,
                            amountIncludingTax: 1500,
                            description: 'INV-0001-1',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 150,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 300,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE2',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    amount: 800,
                                },
                            ],
                        },
                    ],
                });

                await assert.isRejected(accountsPayableInvoice.$.save());
                assert.deepEqual(accountsPayableInvoice.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['lines', '-**********', 'attributesAndDimensions', '-**********'],
                        message: 'The Project attribute needs to be filled in.',
                    },
                ]);
            },
            {
                today: '2020-11-24',
            },
        ));
});

describe('Accounts payable invoice resend notification', () => {
    it('Resend notification for finance - success', () =>
        Test.withContext(
            async context => {
                const apInvoice: xtremFinance.nodes.AccountsPayableInvoice = await context.read(
                    xtremFinance.nodes.AccountsPayableInvoice,
                    { number: 'PI250005' },
                );

                const notifySpy = Sinon.spy(context, 'notify');
                await xtremFinance.nodes.AccountsPayableInvoice.resendNotificationForFinance(context, apInvoice);
                assert.equal(notifySpy.getCalls().length, 1);
                const financeNotificationTopic = notifySpy.args[0][0];
                const financeNotificationPayload = notifySpy.args[0][1];

                const financeTransaction = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, {
                        filter: {
                            documentNumber: 'PI250005',
                            documentType: 'apInvoice',
                        },
                    })
                    .at(0);

                assert.deepEqual(
                    [financeNotificationTopic, financeNotificationPayload],
                    [
                        'AccountingInterfaceListener/createJournalsFromAccountingStagingJob/start',
                        {
                            batchTrackingId: undefined,
                            journalsCreatedData: false,
                            filter: `{"batchId":"${(await financeTransaction?.batchId) || ''}","isProcessed":false}`,
                        },
                    ],
                );
            },
            { today: '2025-02-03' },
        ));
});
