import type { NodeCreateData } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremFinance from '../../../lib/index';

describe('Journal Entry - issue ', () => {
    before(() => {});

    /**
     *  This creation provide an issue on the function financialSiteTransactionCurrencyUniqKey 2 uniq are returned
     *  The save is throw an error
     *  TODO : dig & create jira ticket
     */
    it('Create the journal entry', () =>
        Test.withContext(
            async context => {
                const journalEntryPayload: NodeCreateData<xtremFinance.nodes.JournalEntry> = {
                    financialSite: '#US001',
                    postingStatus: 'posted',
                    postingDate: date.today(),
                    description: 'Inventory receipt',
                    reference: 'Inventory receipt',
                    origin: 'stock',
                    journal: '#US|STK',
                    lines: [
                        {
                            financialSite: '#US001',
                            chartOfAccount: '#TEST_US_DEFAULT',
                            transactionCurrency: '#EUR',
                            companyFxRate: 1.0,
                            fxRateDate: date.parse('2020-05-05'),
                            account: {
                                id: '1',
                            },
                            sign: 'C',
                            transactionAmount: 1000,
                            companyAmount: 1000,
                            description: 'REC-**********',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US019',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    transactionAmount: 1000,
                                    companyAmount: 1000,
                                },
                            ],
                            journalEntryTypeLine: '#US|miscellaneousStockReceipt|journalEntry|100',
                        },
                        {
                            financialSite: '#US001',
                            chartOfAccount: '#TEST_US_DEFAULT',
                            transactionCurrency: '#EUR',
                            companyFxRate: 1.0,
                            fxRateDate: date.parse('2020-05-05'),
                            account: {
                                id: '2',
                            },
                            sign: 'D',
                            transactionAmount: 800,
                            companyAmount: 800,
                            description: 'REC-**********',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US019',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    transactionAmount: 500,
                                    companyAmount: 500,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US019',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    transactionAmount: 300,
                                    companyAmount: 300,
                                },
                            ],
                            journalEntryTypeLine: 'US|miscellaneousStockReceipt|journalEntry|200',
                        },
                        {
                            financialSite: { id: 'US001' },
                            chartOfAccount: '#TEST_US_DEFAULT',
                            transactionCurrency: '#EUR',
                            companyFxRate: 1.0,
                            fxRateDate: date.parse('2020-05-05'),
                            account: {
                                id: '3',
                            },
                            sign: 'D',
                            transactionAmount: 200,
                            companyAmount: 200,
                            description: 'REC-**********',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US019',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    transactionAmount: 200.0,
                                    companyAmount: 200.0,
                                },
                            ],
                        },
                    ],
                };
                const journalEntry1 = await context.create(xtremFinance.nodes.JournalEntry, journalEntryPayload);

                const uniqKey = await journalEntry1.financialSiteTransactionCurrencyUniqKey();

                assert.equal(uniqKey.length, 1);

                await assert.isRejected(journalEntry1.$.save());
                assert.equal(
                    JSON.stringify(journalEntry1.$.context.diagnoses),
                    '[{"severity":3,"path":["lines","-1000000010","journalEntryTypeLine"],"message":"You need to enter the journal entry type on the line."}]',
                );

                const correctedLine = {
                    ...journalEntryPayload.lines?.at(2),
                    journalEntryTypeLine: 'US|miscellaneousStockReceipt|journalEntry|200',
                };

                journalEntryPayload.lines?.splice(2, 1, correctedLine);

                const journalEntry2 = await context.create(xtremFinance.nodes.JournalEntry, journalEntryPayload);

                await journalEntry2.$.save();

                assert.equal(await (await journalEntry2.lines?.at(0))?.contraAccount, '2 -- Account 2 US');
                assert.equal(await (await journalEntry2.lines?.at(1))?.contraAccount, '1 -- Account 1 US');
                assert.equal(await (await journalEntry2.lines?.at(2))?.contraAccount, '1 -- Account 1 US');
            },
            {
                today: '2020-11-24',
            },
        ));

    it('Create the journal entry - fail because no contra journal entry line ', () =>
        Test.withContext(
            async context => {
                const journalEntryPayload: NodeCreateData<xtremFinance.nodes.JournalEntry> = {
                    financialSite: '#US001',
                    postingStatus: 'posted',
                    postingDate: date.today(),
                    description: 'Inventory receipt',
                    reference: 'Inventory receipt',
                    origin: 'stock',
                    journal: '#US|STK',
                    lines: [
                        {
                            financialSite: '#US001',
                            chartOfAccount: '#TEST_US_DEFAULT',
                            transactionCurrency: '#EUR',
                            companyFxRate: 1.0,
                            fxRateDate: date.parse('2020-05-05'),
                            account: {
                                id: '1',
                            },
                            sign: 'C',
                            transactionAmount: 1000,
                            companyAmount: 1000,
                            description: 'REC-**********',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US019',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    transactionAmount: 1000,
                                    companyAmount: 1000,
                                },
                            ],
                            journalEntryTypeLine: '#US|purchaseCreditMemo|accountsPayableInvoice|200',
                        },
                        {
                            financialSite: '#US001',
                            chartOfAccount: '#TEST_US_DEFAULT',
                            transactionCurrency: '#EUR',
                            companyFxRate: 1.0,
                            fxRateDate: date.parse('2020-05-05'),
                            account: {
                                id: '2',
                            },
                            sign: 'D',
                            transactionAmount: 800,
                            companyAmount: 800,
                            description: 'REC-**********',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US019',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    transactionAmount: 500,
                                    companyAmount: 500,
                                },
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US019',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    transactionAmount: 300,
                                    companyAmount: 300,
                                },
                            ],
                            journalEntryTypeLine: 'US|miscellaneousStockReceipt|journalEntry|200',
                        },
                        {
                            financialSite: { id: 'US001' },
                            chartOfAccount: '#TEST_US_DEFAULT',
                            transactionCurrency: '#EUR',
                            companyFxRate: 1.0,
                            fxRateDate: date.parse('2020-05-05'),
                            account: {
                                id: '3',
                            },
                            sign: 'D',
                            transactionAmount: 200,
                            companyAmount: 200,
                            description: 'REC-**********',
                            attributesAndDimensions: [
                                {
                                    storedAttributes: {
                                        financialSite: 'US001',
                                        stockSite: 'US001',
                                        customer: 'US019',
                                        supplier: 'US018',
                                        project: 'AttPROJ',
                                        task: '',
                                        employee: '',
                                    },
                                    storedDimensions: {
                                        dimensionType03: 'DIMTYPE1VALUE1',
                                        dimensionType04: 'DIMTYPE2VALUE2',
                                    },
                                    transactionAmount: 200.0,
                                    companyAmount: 200.0,
                                },
                            ],
                            journalEntryTypeLine: '#US|purchaseCreditMemo|accountsPayableInvoice|200',
                        },
                    ],
                };

                const journalEntry = await context.create(xtremFinance.nodes.JournalEntry, journalEntryPayload);

                await assert.isRejected(journalEntry.$.save(), 'You need to enter a contra journal entry on the line.');
            },
            {
                today: '2020-11-24',
            },
        ));

    it('Posts a journal entry', () =>
        Test.withContext(async context => {
            let journalEntry = await context.read(
                xtremFinance.nodes.JournalEntry,
                { number: 'STK-**********' },
                { forUpdate: true },
            );
            const postReturn = await xtremFinance.nodes.JournalEntry.post(context, journalEntry);
            journalEntry = await context.read(xtremFinance.nodes.JournalEntry, { number: 'STK-**********' });
            assert.strictEqual(postReturn, 'The journal entry has been posted.');
            assert.strictEqual(await journalEntry.postingStatus, 'posted');
        }));
});
