import type { Context } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremFinance from '../../../lib/index';

async function getApOpenItemSysId(context: Context, sysId: string): Promise<number> {
    return (
        await context
            .query(xtremFinance.nodes.AccountsPayableOpenItem, {
                filter: { accountsPayableInvoice: { _id: sysId } },
                first: 1,
            })
            .elementAt(0)
    )._id;
}

describe('Payment node - test ', () => {
    it('Create the payment without error', () =>
        Test.withContext(
            async context => {
                const notifyFinanceSpy = sinon.spy(context, 'notify');

                const payment = await context.create(xtremFinance.nodes.Payment, {
                    bankAccount: '#BUS01',
                    financialSite: '#US001',
                    businessRelation: '#Supplier|US017',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#USD',
                    amount: 80,
                    amountBankCurrency: 80,
                    lines: [
                        {
                            amount: 80,
                            apOpenItem: await getApOpenItemSysId(context, '#PI240003|purchaseInvoice'),
                            originalNodeFactory: '#AccountsPayableInvoice',
                        },
                    ],
                });
                await payment.$.save({ flushDeferredActions: true });

                assert.equal(notifyFinanceSpy.getCalls().length, 1);
                const notifyDocumentPayload = notifyFinanceSpy.args.at(0);
                assert.equal(notifyDocumentPayload?.length, 2);
                assert.equal(notifyDocumentPayload?.at(0), 'PurchaseInvoice/updatePaymentStatus');
                assert.deepEqual(notifyDocumentPayload?.at(1), { _id: 10026, paymentStatus: 'partiallyPaid' });

                const savedPayment = await context.read(xtremFinance.nodes.Payment, { _id: payment._id });
                const paymentLine = await savedPayment.lines.at(0);

                assert.strictEqual(await savedPayment.financialSiteName, 'Chem. Atlanta');
                assert.strictEqual(await savedPayment.postingStatus, 'posted');
                assert.strictEqual(
                    (await savedPayment.postingDate).value,
                    date.parse('2024-11-22', context.currentLocale as any).value,
                );
                assert.strictEqual(
                    (await savedPayment.exchangeRateDate).value,
                    date.parse('2024-11-22', context.currentLocale as any).value,
                );
                assert.strictEqual((await savedPayment.companyExchangeRate).toString(), '1');
                assert.strictEqual((await savedPayment.companyExchangeRateDivisor).toString(), '1');
                assert.strictEqual(await savedPayment.number, 'PAYE1240001');
                assert.strictEqual(await paymentLine?.origin, 'invoice');
                assert.strictEqual(await (await paymentLine?.originalOpenItem)?.documentNumber, 'PI240003');
                assert.strictEqual(await (await paymentLine?.originalNodeFactory)?.name, 'AccountsPayableInvoice');
                assert.strictEqual((await savedPayment.amount).toString(), '80');
                assert.strictEqual((await savedPayment.companyAmount).toString(), '80');

                const openItem = await context.read(xtremFinance.nodes.AccountsPayableOpenItem, {
                    _id: (await paymentLine?.apOpenItem)?._id,
                });
                assert.strictEqual(await openItem.status, 'partiallyPaid');
                assert.strictEqual((await openItem.transactionAmountPaid).toString(), '80');
                assert.strictEqual((await openItem.companyAmountPaid).toString(), '80');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the payment without error with different currencies', () =>
        Test.withContext(
            async context => {
                const payment = await context.create(xtremFinance.nodes.Payment, {
                    bankAccount: '#BUS01',
                    financialSite: '#US001',
                    businessRelation: '#Supplier|US017',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#EUR',
                    amount: 100,
                    amountBankCurrency: 100,
                    lines: [
                        {
                            amount: 100,
                            apOpenItem: await getApOpenItemSysId(context, '#PI240003|purchaseInvoice'),
                            originalNodeFactory: '#AccountsPayableInvoice',
                        },
                    ],
                });
                await payment.$.save({ flushDeferredActions: true });

                const savedPayment = await context.read(xtremFinance.nodes.Payment, { _id: payment._id });
                const paymentLine = await savedPayment.lines.at(0);

                assert.strictEqual(await savedPayment.financialSiteName, 'Chem. Atlanta');
                assert.strictEqual(await savedPayment.postingStatus, 'posted');
                assert.strictEqual(
                    (await savedPayment.postingDate).value,
                    date.parse('2024-11-22', context.currentLocale as any).value,
                );
                assert.strictEqual(
                    (await savedPayment.exchangeRateDate).value,
                    date.parse('2024-11-22', context.currentLocale as any).value,
                );
                assert.strictEqual((await savedPayment.companyExchangeRate).toString(), '1.002');
                assert.strictEqual((await savedPayment.companyExchangeRateDivisor).toString(), '1');
                assert.strictEqual(await savedPayment.number, 'PAYE1240001');
                assert.strictEqual(await paymentLine?.origin, 'invoice');
                assert.strictEqual(await (await paymentLine?.originalOpenItem)?.documentNumber, 'PI240003');
                assert.strictEqual(await (await paymentLine?.originalNodeFactory)?.name, 'AccountsPayableInvoice');
                assert.strictEqual((await paymentLine?.amount)?.toString(), '100');
                assert.strictEqual((await paymentLine?.companyAmount)?.toString(), '100.2');
                assert.strictEqual((await savedPayment.amount).toString(), '100');
                assert.strictEqual((await savedPayment.companyAmount).toString(), '100.2');

                const openItem = await context.read(xtremFinance.nodes.AccountsPayableOpenItem, {
                    _id: (await paymentLine?.apOpenItem)?._id,
                });
                assert.strictEqual(await openItem.status, 'paid');
                assert.strictEqual((await openItem.transactionAmountPaid).toString(), '100');
                assert.strictEqual((await openItem.companyAmountPaid).toString(), '100.2');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the payment with wrong financialSite', () =>
        Test.withContext(
            async context => {
                const payment = await context.create(xtremFinance.nodes.Payment, {
                    bankAccount: '#BUS01',
                    financialSite: '#US005',
                    businessRelation: '#Supplier|US017',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#USD',
                    amount: 100,
                    amountBankCurrency: 100,
                    lines: [
                        {
                            amount: 100,
                            apOpenItem: await getApOpenItemSysId(context, '#PI240003|purchaseInvoice'),
                            originalNodeFactory: '#AccountsPayableInvoice',
                        },
                    ],
                });

                await assert.isRejected(payment.$.save({ flushDeferredActions: true }), 'The record was not created.');
                assert.deepEqual(payment.$.context.diagnoses, [
                    {
                        message:
                            'The financial site of the document needs to be the same as the financial site of the bank account.',
                        path: [],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the payment with wrong amounts', () =>
        Test.withContext(
            async context => {
                const payment = await context.create(xtremFinance.nodes.Payment, {
                    bankAccount: '#BUS01',
                    financialSite: '#US001',
                    businessRelation: '#Supplier|US017',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#USD',
                    amount: 100,
                    amountBankCurrency: 100,
                    lines: [
                        {
                            amount: 100,
                            apOpenItem: await getApOpenItemSysId(context, '#PI240003|purchaseInvoice'),
                            origin: 'invoice',
                            originalNodeFactory: '#AccountsPayableInvoice',
                        },
                        {
                            amount: 30,
                            apOpenItem: await getApOpenItemSysId(context, '#PI240003|purchaseInvoice'),
                            origin: 'creditMemo',
                            originalNodeFactory: '#AccountsPayableInvoice',
                        },
                    ],
                });

                await assert.isRejected(payment.$.save({ flushDeferredActions: true }), 'The record was not created.');
                assert.deepEqual(payment.$.context.diagnoses, [
                    {
                        message:
                            'The payment amount of the document: 100, needs to be the same as the total of payment amounts of all lines: 70.',
                        path: [],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the payment with wrong apOpenItem reference', () =>
        Test.withContext(
            async context => {
                const payment = await context.create(xtremFinance.nodes.Payment, {
                    bankAccount: '#BFR01',
                    financialSite: '#ETS2-S02',
                    businessRelation: '#Customer|US019',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#EUR',
                    amount: 100,
                    amountBankCurrency: 100,
                    lines: [
                        {
                            amount: 100,
                            apOpenItem: await getApOpenItemSysId(context, '#PI240003|purchaseInvoice'),
                            origin: 'creditMemo',
                            originalNodeFactory: '#AccountsPayableInvoice',
                        },
                    ],
                });

                await assert.isRejected(payment.$.save({ flushDeferredActions: true }), 'The record was not created.');
                assert.deepEqual(payment.$.context.diagnoses, [
                    {
                        message: 'You need to add a reference to the accounts receivable invoice.',
                        path: ['lines', '-**********', 'arOpenItem'],
                        severity: 3,
                    },
                    {
                        message: 'You cannot reference the accounts payable invoice.',
                        path: ['lines', '-**********', 'apOpenItem'],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the payment with wrong AP invoice posting status', () =>
        Test.withContext(
            async context => {
                const payment = await context.create(xtremFinance.nodes.Payment, {
                    bankAccount: '#BUS01',
                    financialSite: '#US001',
                    businessRelation: '#Supplier|US016',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#EUR',
                    amount: 100,
                    amountBankCurrency: 100,
                    lines: [
                        {
                            amount: 100,
                            apOpenItem: await getApOpenItemSysId(context, '#PI240002|purchaseInvoice'),
                            origin: 'invoice',
                            originalNodeFactory: '#AccountsPayableInvoice',
                        },
                    ],
                });

                await assert.isRejected(payment.$.save({ flushDeferredActions: true }), 'The record was not created.');
                assert.deepEqual(payment.$.context.diagnoses, [
                    {
                        message: 'The posting status of the accounts payable invoice needs to be posted.',
                        path: ['lines', '-**********'],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Void a payment', () =>
        Test.withContext(
            async context => {
                const paymentPAYG1250001 = await context.read(
                    xtremFinance.nodes.Payment,
                    {
                        number: 'PAYE1250001',
                    },
                    { forUpdate: true },
                );

                const paymentPAYG1250002 = await context.read(
                    xtremFinance.nodes.Payment,
                    {
                        number: 'PAYE1250002',
                    },
                    { forUpdate: true },
                );

                let openItemPayedOnPAYG1250001 = await context
                    .query(xtremFinance.nodes.AccountsPayableOpenItem, {
                        filter: { accountsPayableInvoice: { _id: '#PI250010|purchaseInvoice' } },
                    })
                    .elementAt(0);
                let openItemPayedOnPAYG1250001AndPAYG1250002 = await context
                    .query(xtremFinance.nodes.AccountsPayableOpenItem, {
                        filter: { accountsPayableInvoice: { _id: '#PI250011|purchaseInvoice' } },
                    })
                    .elementAt(0);

                assert.equal(await openItemPayedOnPAYG1250001.status, 'partiallyPaid');
                assert.equal(await openItemPayedOnPAYG1250001AndPAYG1250002.status, 'partiallyPaid');

                await xtremFinance.nodes.Payment.voidPayment(
                    context,
                    paymentPAYG1250001,
                    date.today(),
                    'Voiding the payment',
                );

                openItemPayedOnPAYG1250001 = await context
                    .query(xtremFinance.nodes.AccountsPayableOpenItem, {
                        filter: { accountsPayableInvoice: { _id: '#PI250010|purchaseInvoice' } },
                    })
                    .elementAt(0);
                openItemPayedOnPAYG1250001AndPAYG1250002 = await context
                    .query(xtremFinance.nodes.AccountsPayableOpenItem, {
                        filter: { accountsPayableInvoice: { _id: '#PI250011|purchaseInvoice' } },
                    })
                    .elementAt(0);

                assert.equal(await openItemPayedOnPAYG1250001.status, 'partiallyPaid');
                assert.equal(await openItemPayedOnPAYG1250001AndPAYG1250002.status, 'partiallyPaid');

                await xtremFinance.nodes.Payment.voidPayment(
                    context,
                    paymentPAYG1250002,
                    date.today(),
                    'Voiding the payment',
                );

                openItemPayedOnPAYG1250001 = await context
                    .query(xtremFinance.nodes.AccountsPayableOpenItem, {
                        filter: { accountsPayableInvoice: { _id: '#PI250010|purchaseInvoice' } },
                    })
                    .elementAt(0);
                openItemPayedOnPAYG1250001AndPAYG1250002 = await context
                    .query(xtremFinance.nodes.AccountsPayableOpenItem, {
                        filter: { accountsPayableInvoice: { _id: '#PI250011|purchaseInvoice' } },
                    })
                    .elementAt(0);

                assert.equal(await openItemPayedOnPAYG1250001.status, 'notPaid');
                assert.equal(await openItemPayedOnPAYG1250001AndPAYG1250002.status, 'notPaid');
            },
            {
                today: '2025-03-06',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the payment with discount, penalty and adjustment amount with different currencies without error', () =>
        Test.withContext(
            async context => {
                const payment = await context.create(xtremFinance.nodes.Payment, {
                    bankAccount: '#BUS01',
                    financialSite: '#US001',
                    businessRelation: '#Supplier|LECLERC',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#EUR',
                    amount: 105,
                    amountBankCurrency: 105,
                    lines: [
                        {
                            amount: 100,
                            discountAmount: 10,
                            penaltyAmount: 5,
                            adjustmentAmount: -2,
                            apOpenItem: await getApOpenItemSysId(context, '#PI250010|purchaseInvoice'),
                            originalNodeFactory: '#AccountsPayableInvoice',
                        },
                    ],
                });
                await payment.$.save({ flushDeferredActions: true });

                const savedPayment = await context.read(xtremFinance.nodes.Payment, { _id: payment._id });
                const paymentLine = await savedPayment.lines.at(0);

                assert.strictEqual((await savedPayment.amount).toString(), '105');
                assert.strictEqual((await savedPayment.companyAmount).toString(), '108.22');
                assert.strictEqual((await paymentLine?.amount)?.toString(), '100');
                assert.strictEqual((await paymentLine?.discountAmount)?.toString(), '10');
                assert.strictEqual((await paymentLine?.penaltyAmount)?.toString(), '5');
                assert.strictEqual((await paymentLine?.adjustmentAmount)?.toString(), '-2');
                assert.strictEqual((await paymentLine?.companyAmount)?.toString(), '108.22');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the payment with wrong negative discount amount', () =>
        Test.withContext(
            async context => {
                const payment = await context.create(xtremFinance.nodes.Payment, {
                    bankAccount: '#BUS01',
                    financialSite: '#US001',
                    businessRelation: '#Supplier|LECLERC',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#EUR',
                    amount: 105,
                    amountBankCurrency: 105,
                    lines: [
                        {
                            amount: 100,
                            discountAmount: -10,
                            penaltyAmount: 5,
                            apOpenItem: await getApOpenItemSysId(context, '#PI250010|purchaseInvoice'),
                            originalNodeFactory: '#AccountsPayableInvoice',
                        },
                    ],
                });
                await assert.isRejected(payment.$.save({ flushDeferredActions: true }), 'The record was not created.');
                assert.deepEqual(payment.$.context.diagnoses, [
                    {
                        message: 'The discount amount needs to be greater than or equal to 0.',
                        path: ['lines', '-**********', 'discountAmount'],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the payment with wrong negative penalty amount', () =>
        Test.withContext(
            async context => {
                const payment = await context.create(xtremFinance.nodes.Payment, {
                    bankAccount: '#BUS01',
                    financialSite: '#US001',
                    businessRelation: '#Supplier|LECLERC',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#EUR',
                    amount: 95,
                    amountBankCurrency: 95,
                    lines: [
                        {
                            amount: 100,
                            discountAmount: 10,
                            penaltyAmount: -5,
                            apOpenItem: await getApOpenItemSysId(context, '#PI250010|purchaseInvoice'),
                            originalNodeFactory: '#AccountsPayableInvoice',
                        },
                    ],
                });
                await assert.isRejected(payment.$.save({ flushDeferredActions: true }), 'The record was not created.');
                assert.deepEqual(payment.$.context.diagnoses, [
                    {
                        message: 'The penalty amount needs to be greater than or equal to 0.',
                        path: ['lines', '-**********', 'penaltyAmount'],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));
});
