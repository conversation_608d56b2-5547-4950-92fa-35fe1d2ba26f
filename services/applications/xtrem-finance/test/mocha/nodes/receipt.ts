import type { Context } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremFinance from '../../../lib/index';

async function getArOpenItemSysId(context: Context, sysId: string): Promise<number> {
    return (
        await context
            .query(xtremFinance.nodes.AccountsReceivableOpenItem, {
                filter: { accountsReceivableInvoice: { _id: sysId } },
                first: 1,
            })
            .elementAt(0)
    )._id;
}

async function getApOpenItemSysId(context: Context, sysId: string): Promise<number> {
    return (
        await context
            .query(xtremFinance.nodes.AccountsPayableOpenItem, {
                filter: { accountsPayableInvoice: { _id: sysId } },
                first: 1,
            })
            .elementAt(0)
    )._id;
}

async function getArInvoiceSysId(context: Context, sysId: string): Promise<number> {
    return (await context.read(xtremFinance.nodes.AccountsReceivableInvoice, { _id: sysId }))._id;
}

describe('Receipt node - test ', () => {
    it('Create the receipt without error', () =>
        Test.withContext(
            async context => {
                const notifyFinanceSpy = sinon.spy(context, 'notify');

                const receipt = await context.create(xtremFinance.nodes.Receipt, {
                    bankAccount: '#BAN',
                    financialSite: '#US006',
                    businessRelation: '#Customer|US019',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#USD',
                    amount: 100,
                    amountBankCurrency: 100,
                    lines: [
                        {
                            amount: 100,
                            arOpenItem: await getArOpenItemSysId(context, '#AR-**********|salesInvoice'),
                            originalNodeFactory: '#AccountsReceivableInvoice',
                        },
                    ],
                });
                await receipt.$.save({ flushDeferredActions: true });

                const arInvoiceSysId = await getArInvoiceSysId(context, '#AR-**********|salesInvoice');
                assert.equal(notifyFinanceSpy.getCalls().length, 1);
                const notifyDocumentPayload = notifyFinanceSpy.args.at(0);

                assert.equal(notifyDocumentPayload?.length, 2);
                assert.equal(notifyDocumentPayload?.at(0), 'SalesInvoice/updatePaymentStatus');
                assert.deepEqual(notifyDocumentPayload?.at(1), { _id: arInvoiceSysId, paymentStatus: 'partiallyPaid' });

                const savedReceipt = await context.read(xtremFinance.nodes.Receipt, { _id: receipt._id });
                const receiptLine = await savedReceipt.lines.at(0);

                assert.strictEqual(await savedReceipt.financialSiteName, 'Chem. Boston');
                assert.strictEqual(await savedReceipt.postingStatus, 'posted');
                assert.strictEqual(
                    (await savedReceipt.postingDate).value,
                    date.parse('2024-11-22', context.currentLocale as any).value,
                );
                assert.strictEqual(
                    (await savedReceipt.exchangeRateDate).value,
                    date.parse('2024-11-22', context.currentLocale as any).value,
                );
                assert.strictEqual((await savedReceipt.companyExchangeRate).toString(), '1');
                assert.strictEqual((await savedReceipt.companyExchangeRateDivisor).toString(), '1');
                assert.strictEqual(await savedReceipt.number, 'RCTE6240001');
                assert.strictEqual(await receiptLine?.origin, 'invoice');
                assert.strictEqual(await (await receiptLine?.originalOpenItem)?.documentNumber, 'SI1');
                assert.strictEqual(await (await receiptLine?.originalNodeFactory)?.name, 'AccountsReceivableInvoice');
                assert.strictEqual((await savedReceipt.amount).toString(), '100');
                assert.strictEqual((await savedReceipt.companyAmount).toString(), '100');

                const openItem = await context.read(xtremFinance.nodes.AccountsReceivableOpenItem, {
                    _id: (await receiptLine?.arOpenItem)?._id,
                });
                assert.strictEqual(await openItem.status, 'partiallyPaid');
                assert.strictEqual((await openItem.transactionAmountPaid).toString(), '100');
                assert.strictEqual((await openItem.companyAmountPaid).toString(), '100');

                const arInvoice = await context.read(xtremFinance.nodes.AccountsReceivableInvoice, {
                    _id: '#AR-**********|salesInvoice',
                });
                assert.strictEqual(await arInvoice.paymentStatus, 'partiallyPaid');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the receipt without error with different currencies', () =>
        Test.withContext(
            async context => {
                const receipt = await context.create(xtremFinance.nodes.Receipt, {
                    bankAccount: '#700_CHK',
                    financialSite: '#700',
                    businessRelation: '#Customer|US019',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#USD',
                    amount: 100,
                    amountBankCurrency: 100,
                    lines: [
                        {
                            amount: 100,
                            arOpenItem: await getArOpenItemSysId(context, '#SIPZ2230001|salesInvoice'),
                            originalNodeFactory: '#AccountsReceivableInvoice',
                        },
                    ],
                });
                await receipt.$.save({ flushDeferredActions: true });

                const savedReceipt = await context.read(xtremFinance.nodes.Receipt, { _id: receipt._id });
                const receiptLine = await savedReceipt.lines.at(0);

                assert.strictEqual(await savedReceipt.financialSiteName, 'ZA 700');
                assert.strictEqual(await savedReceipt.postingStatus, 'posted');
                assert.strictEqual(
                    (await savedReceipt.postingDate).value,
                    date.parse('2024-11-22', context.currentLocale as any).value,
                );
                assert.strictEqual(
                    (await savedReceipt.exchangeRateDate).value,
                    date.parse('2024-11-22', context.currentLocale as any).value,
                );
                assert.strictEqual((await savedReceipt.companyExchangeRate).toString(), '1');
                assert.strictEqual((await savedReceipt.companyExchangeRateDivisor).toString(), '15.87');
                assert.strictEqual(await savedReceipt.number, 'RCTZ2240001');
                assert.strictEqual(await receiptLine?.origin, 'invoice');
                assert.strictEqual(await (await receiptLine?.originalOpenItem)?.documentNumber, 'SIPZ2230001');
                assert.strictEqual(await (await receiptLine?.originalNodeFactory)?.name, 'AccountsReceivableInvoice');
                assert.strictEqual((await receiptLine?.amount)?.toString(), '100');
                assert.strictEqual((await receiptLine?.companyAmount)?.toString(), '6.3');
                assert.strictEqual((await savedReceipt.amount).toString(), '100');
                assert.strictEqual((await savedReceipt.companyAmount).toString(), '6.3');

                const openItem = await context.read(xtremFinance.nodes.AccountsReceivableOpenItem, {
                    _id: (await receiptLine?.arOpenItem)?._id,
                });
                assert.strictEqual(await openItem.status, 'partiallyPaid');
                assert.strictEqual((await openItem.transactionAmountPaid).toString(), '100');
                assert.strictEqual((await openItem.companyAmountPaid).toString(), '6.3');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the receipt with wrong financialSite', () =>
        Test.withContext(
            async context => {
                const receipt = await context.create(xtremFinance.nodes.Receipt, {
                    bankAccount: '#BAN',
                    financialSite: '#US005',
                    businessRelation: '#Customer|US019',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#USD',
                    amount: 100,
                    amountBankCurrency: 100,
                    lines: [
                        {
                            amount: 100,
                            arOpenItem: await getArOpenItemSysId(context, '#AR-**********|salesInvoice'),
                            originalNodeFactory: '#AccountsReceivableInvoice',
                        },
                    ],
                });

                await assert.isRejected(receipt.$.save({ flushDeferredActions: true }), 'The record was not created.');
                assert.deepEqual(receipt.$.context.diagnoses, [
                    {
                        message:
                            'The financial site of the document needs to be the same as the financial site of the bank account.',
                        path: [],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the receipt with wrong amounts', () =>
        Test.withContext(
            async context => {
                const receipt = await context.create(xtremFinance.nodes.Receipt, {
                    bankAccount: '#BAN',
                    financialSite: '#US006',
                    businessRelation: '#Supplier|US018',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#USD',
                    amount: 100,
                    amountBankCurrency: 100,
                    lines: [
                        {
                            amount: 30,
                            apOpenItem: await getApOpenItemSysId(context, '#PIINTACCT01|purchaseInvoice'),
                            origin: 'invoice',
                            originalNodeFactory: '#AccountsPayableInvoice',
                        },
                        {
                            amount: 100,
                            apOpenItem: await getApOpenItemSysId(context, '#PI240003|purchaseInvoice'),
                            origin: 'creditMemo',
                            originalNodeFactory: '#AccountsPayableInvoice',
                        },
                    ],
                });

                await assert.isRejected(receipt.$.save({ flushDeferredActions: true }), 'The record was not created.');
                assert.deepEqual(receipt.$.context.diagnoses, [
                    {
                        message:
                            'The payment amount of the document: 100, needs to be the same as the total of payment amounts of all lines: 70.',
                        path: [],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the receipt with wrong arOpenItem reference', () =>
        Test.withContext(
            async context => {
                const receipt = await context.create(xtremFinance.nodes.Receipt, {
                    bankAccount: '#BAN',
                    financialSite: '#US006',
                    businessRelation: '#Supplier|US018',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#USD',
                    amount: 100,
                    amountBankCurrency: 100,
                    lines: [
                        {
                            amount: 100,
                            arOpenItem: await getArOpenItemSysId(context, '#AR-**********|salesInvoice'),
                            origin: 'creditMemo',
                            originalNodeFactory: '#AccountsReceivableInvoice',
                        },
                    ],
                });

                await assert.isRejected(receipt.$.save({ flushDeferredActions: true }), 'The record was not created.');
                assert.deepEqual(receipt.$.context.diagnoses, [
                    {
                        message: 'You cannot reference the accounts receivable invoice.',
                        path: ['lines', '-**********', 'arOpenItem'],
                        severity: 3,
                    },
                    {
                        message: 'You need to add a reference to the accounts payable invoice.',
                        path: ['lines', '-**********', 'apOpenItem'],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the receipt with wrong AR invoice posting status', () =>
        Test.withContext(
            async context => {
                const receipt = await context.create(xtremFinance.nodes.Receipt, {
                    bankAccount: '#BFR01',
                    financialSite: '#ETS2-S02',
                    businessRelation: '#Customer|US019',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#EUR',
                    amount: 100,
                    amountBankCurrency: 100,
                    lines: [
                        {
                            amount: 100,
                            arOpenItem: await getArOpenItemSysId(context, '#AR-**********|salesInvoice'),
                            origin: 'invoice',
                            originalNodeFactory: '#AccountsReceivableInvoice',
                        },
                    ],
                });

                await assert.isRejected(receipt.$.save({ flushDeferredActions: true }), 'The record was not created.');
                assert.deepEqual(receipt.$.context.diagnoses, [
                    {
                        message: 'The posting status of the accounts receivable invoice needs to be posted.',
                        path: ['lines', '-**********'],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Void a receipt', () =>
        Test.withContext(
            async context => {
                const receiptRCTG1250001 = await context.read(
                    xtremFinance.nodes.Receipt,
                    {
                        number: 'RCTG1250001',
                    },
                    { forUpdate: true },
                );

                const receiptRCTG1250002 = await context.read(
                    xtremFinance.nodes.Receipt,
                    {
                        number: 'RCTG1250002',
                    },
                    { forUpdate: true },
                );

                let openItemPayedOnRCTG1250001 = await context
                    .query(xtremFinance.nodes.AccountsReceivableOpenItem, {
                        filter: { accountsReceivableInvoice: { _id: '#SIG1250002|salesInvoice' } },
                    })
                    .elementAt(0);
                let openItemPayedOnRCTG1250001AndRCTG1250002 = await context
                    .query(xtremFinance.nodes.AccountsReceivableOpenItem, {
                        filter: { accountsReceivableInvoice: { _id: '#SIG1250003|salesInvoice' } },
                    })
                    .elementAt(0);

                assert.equal(await openItemPayedOnRCTG1250001.status, 'paid');
                assert.equal(await openItemPayedOnRCTG1250001AndRCTG1250002.status, 'paid');

                await xtremFinance.nodes.Receipt.voidPayment(
                    context,
                    receiptRCTG1250001,
                    date.today(),
                    'Voiding the payment',
                );

                openItemPayedOnRCTG1250001 = await context
                    .query(xtremFinance.nodes.AccountsReceivableOpenItem, {
                        filter: { accountsReceivableInvoice: { _id: '#SIG1250002|salesInvoice' } },
                    })
                    .elementAt(0);
                openItemPayedOnRCTG1250001AndRCTG1250002 = await context
                    .query(xtremFinance.nodes.AccountsReceivableOpenItem, {
                        filter: { accountsReceivableInvoice: { _id: '#SIG1250003|salesInvoice' } },
                    })
                    .elementAt(0);

                assert.equal(await openItemPayedOnRCTG1250001.status, 'notPaid');
                assert.equal(await openItemPayedOnRCTG1250001AndRCTG1250002.status, 'partiallyPaid');

                await xtremFinance.nodes.Receipt.voidPayment(
                    context,
                    receiptRCTG1250002,
                    date.today(),
                    'Voiding the payment',
                );

                openItemPayedOnRCTG1250001AndRCTG1250002 = await context
                    .query(xtremFinance.nodes.AccountsReceivableOpenItem, {
                        filter: { accountsReceivableInvoice: { _id: '#SIG1250003|salesInvoice' } },
                    })
                    .elementAt(0);

                assert.equal(await openItemPayedOnRCTG1250001AndRCTG1250002.status, 'notPaid');
            },
            {
                today: '2025-02-17',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the receipt with discount, penalty and adjustment amount without error', () =>
        Test.withContext(
            async context => {
                const receipt = await context.create(xtremFinance.nodes.Receipt, {
                    bankAccount: '#BAN',
                    financialSite: '#US006',
                    businessRelation: '#Customer|US019',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#USD',
                    amount: 105,
                    amountBankCurrency: 105,
                    lines: [
                        {
                            amount: 100,
                            discountAmount: 10,
                            penaltyAmount: 5,
                            adjustmentAmount: -2,
                            arOpenItem: await getArOpenItemSysId(context, '#AR-**********|salesInvoice'),
                            originalNodeFactory: '#AccountsReceivableInvoice',
                        },
                    ],
                });
                await receipt.$.save({ flushDeferredActions: true });

                const savedReceipt = await context.read(xtremFinance.nodes.Receipt, { _id: receipt._id });
                const receiptLine = await savedReceipt.lines.at(0);

                assert.strictEqual((await savedReceipt.amount).toString(), '105');
                assert.strictEqual((await savedReceipt.companyAmount).toString(), '108');
                assert.strictEqual((await receiptLine?.amount)?.toString(), '100');
                assert.strictEqual((await receiptLine?.discountAmount)?.toString(), '10');
                assert.strictEqual((await receiptLine?.penaltyAmount)?.toString(), '5');
                assert.strictEqual((await receiptLine?.adjustmentAmount)?.toString(), '-2');
                assert.strictEqual((await receiptLine?.companyAmount)?.toString(), '108');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the receipt with wrong negative discount amount', () =>
        Test.withContext(
            async context => {
                const receipt = await context.create(xtremFinance.nodes.Receipt, {
                    bankAccount: '#BAN',
                    financialSite: '#US006',
                    businessRelation: '#Customer|US019',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#USD',
                    amount: 105,
                    amountBankCurrency: 105,
                    lines: [
                        {
                            amount: 100,
                            discountAmount: -10,
                            penaltyAmount: 5,
                            arOpenItem: await getArOpenItemSysId(context, '#AR-**********|salesInvoice'),
                            originalNodeFactory: '#AccountsReceivableInvoice',
                        },
                    ],
                });
                await assert.isRejected(receipt.$.save({ flushDeferredActions: true }), 'The record was not created.');
                assert.deepEqual(receipt.$.context.diagnoses, [
                    {
                        message: 'The discount amount needs to be greater than or equal to 0.',
                        path: ['lines', '-**********', 'discountAmount'],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the receipt with wrong negative penalty amount', () =>
        Test.withContext(
            async context => {
                const receipt = await context.create(xtremFinance.nodes.Receipt, {
                    bankAccount: '#BAN',
                    financialSite: '#US006',
                    businessRelation: '#Customer|US019',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#USD',
                    amount: 95,
                    amountBankCurrency: 95,
                    lines: [
                        {
                            amount: 100,
                            discountAmount: 10,
                            penaltyAmount: -5,
                            arOpenItem: await getArOpenItemSysId(context, '#AR-**********|salesInvoice'),
                            originalNodeFactory: '#AccountsReceivableInvoice',
                        },
                    ],
                });
                await assert.isRejected(receipt.$.save({ flushDeferredActions: true }), 'The record was not created.');
                assert.deepEqual(receipt.$.context.diagnoses, [
                    {
                        message: 'The penalty amount needs to be greater than or equal to 0.',
                        path: ['lines', '-**********', 'penaltyAmount'],
                        severity: 3,
                    },
                ]);
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the receipt with discount, penalty and adjustment amount with different currencies without error', () =>
        Test.withContext(
            async context => {
                const receipt = await context.create(xtremFinance.nodes.Receipt, {
                    bankAccount: '#700_CHK',
                    financialSite: '#700',
                    businessRelation: '#Customer|US019',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#USD',
                    amount: 105,
                    amountBankCurrency: 105,
                    lines: [
                        {
                            amount: 100,
                            discountAmount: 10,
                            penaltyAmount: 5,
                            adjustmentAmount: -2,
                            arOpenItem: await getArOpenItemSysId(context, '#SIPZ2230001|salesInvoice'),
                            originalNodeFactory: '#AccountsReceivableInvoice',
                        },
                    ],
                });
                await receipt.$.save({ flushDeferredActions: true });

                const savedReceipt = await context.read(xtremFinance.nodes.Receipt, { _id: receipt._id });
                const receiptLine = await savedReceipt.lines.at(0);

                assert.strictEqual((await savedReceipt.companyExchangeRate).toString(), '1');
                assert.strictEqual((await savedReceipt.companyExchangeRateDivisor).toString(), '15.87');
                assert.strictEqual((await savedReceipt.amount).toString(), '105');
                assert.strictEqual((await savedReceipt.companyAmount).toString(), '6.8');
                assert.strictEqual((await receiptLine?.amount)?.toString(), '100');
                assert.strictEqual((await receiptLine?.discountAmount)?.toString(), '10');
                assert.strictEqual((await receiptLine?.penaltyAmount)?.toString(), '5');
                assert.strictEqual((await receiptLine?.adjustmentAmount)?.toString(), '-2');
                assert.strictEqual((await receiptLine?.companyAmount)?.toString(), '6.8');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));

    it('Create the receipt without error and rounding', () =>
        Test.withContext(
            async context => {
                const receipt = await context.create(xtremFinance.nodes.Receipt, {
                    bankAccount: '#BAN',
                    financialSite: '#US006',
                    businessRelation: '#Customer|US019',
                    paymentMethod: 'cash',
                    reference: 'Payment reference',
                    paymentDate: date.today(),
                    currency: '#USD',
                    amount: 102.2508,
                    amountBankCurrency: 100.1234,
                    lines: [
                        {
                            amount: 100.1274,
                            arOpenItem: await getArOpenItemSysId(context, '#AR-**********|salesInvoice'),
                            originalNodeFactory: '#AccountsReceivableInvoice',
                            discountAmount: 1.1234,
                            penaltyAmount: 2.1234,
                            adjustmentAmount: -3.1234,
                        },
                    ],
                });
                await receipt.$.save({ flushDeferredActions: true });

                const savedReceipt = await context.read(xtremFinance.nodes.Receipt, { _id: receipt._id });
                const receiptLine = await savedReceipt.lines.at(0);

                assert.strictEqual((await savedReceipt.amount).toString(), '102.25');
                assert.strictEqual((await savedReceipt.companyAmount).toString(), '98.13');
                assert.strictEqual((await receiptLine?.amount)?.toString() ?? '', '100.13');
                assert.strictEqual((await receiptLine?.discountAmount)?.toString() ?? '', '1.12');
                assert.strictEqual((await receiptLine?.penaltyAmount)?.toString() ?? '', '2.12');
                assert.strictEqual((await receiptLine?.adjustmentAmount)?.toString() ?? '', '-3.12');
            },
            {
                today: '2024-11-22',
                testActiveServiceOptions: [xtremFinanceData.serviceOptions.paymentTrackingOption],
            },
        ));
});
