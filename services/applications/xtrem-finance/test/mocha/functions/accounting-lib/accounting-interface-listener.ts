import { date, Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremFinance from '../../../../lib';

describe('Accounting staging listener', () => {
    before(() => {});

    it('Update accounting staging from the updateAccountingStaging function', () =>
        Test.withContext(
            async context => {
                const baseFinanceDocumentLine = await context.query(xtremMasterData.nodes.BaseDocumentLine).toArray();
                const financeDocumentToUpdate = await context.read(
                    xtremFinanceData.nodes.AccountingStaging,
                    { _id: 1 },
                    { forUpdate: true },
                );
                await financeDocumentToUpdate.$.set({
                    baseDocumentLine: baseFinanceDocumentLine[0]._id,
                });
                await financeDocumentToUpdate.$.save();

                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate = {
                    batchId: await financeDocumentToUpdate.batchId,
                    batchSize: 1,
                    documentType: await financeDocumentToUpdate.documentType,
                    documentSysId: await financeDocumentToUpdate.documentSysId,
                    financialSiteSysId: (await financeDocumentToUpdate.financialSite)._id,
                    documentNumber: await financeDocumentToUpdate.documentNumber,
                    targetDocumentType: await financeDocumentToUpdate.targetDocumentType,
                    supplierDocumentDate: '2021-08-22',
                    supplierDocumentNumber: 'SUP_NUMBER',
                    documentLines: [
                        {
                            baseDocumentLineSysId: (await financeDocumentToUpdate.baseDocumentLine)?._id || 0,
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                                stockSite: 'US002',
                                financialSite: 'US002',
                                item: 'Muesli',
                            },
                        },
                    ],
                };

                const numberOfUpdatedLines =
                    await xtremFinance.nodes.AccountingInterfaceListener.onAccountingDataUpdatePosting(
                        context,
                        financeDocument,
                    );

                const stagingLines = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, {
                        filter: {
                            batchId: financeDocument.batchId,
                            documentNumber: financeDocument.documentNumber,
                            documentType: financeDocument.documentType,
                            targetDocumentType: financeDocument.targetDocumentType,
                            baseDocumentLine: financeDocument.documentLines[0].baseDocumentLineSysId,
                        },
                    })
                    .toArray();
                assert.equal(stagingLines.length, 1);
                assert.equal(numberOfUpdatedLines, 1);
                assert.equal(await stagingLines[0].supplierDocumentNumber, financeDocument.supplierDocumentNumber);
                assert.equal(
                    (await stagingLines[0].supplierDocumentDate)?.toString(),
                    date.parse(financeDocument.supplierDocumentDate || '').toString(),
                );
                assert.equal(
                    (await stagingLines[0].storedAttributes)?.employee,
                    financeDocument.documentLines[0].storedAttributes.employee,
                );
                assert.equal(
                    (await stagingLines[0].storedAttributes)?.project,
                    financeDocument.documentLines[0].storedAttributes.project,
                );
                assert.equal(
                    JSON.stringify(await stagingLines[0].storedDimensions),
                    JSON.stringify(financeDocument.documentLines[0].storedDimensions),
                );
                assert.equal(await stagingLines[0].toBeReprocessed, true);
            },
            {
                today: '2021-08-23',
            },
        ));
});
