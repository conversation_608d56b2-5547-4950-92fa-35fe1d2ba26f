import type { Context } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import { assert } from 'chai';
import * as xtremFinance from '../../../../lib';

async function getCurrencyId(context: Context, naturalKey: string): Promise<number> {
    return (await context.tryRead(xtremMasterData.nodes.Currency, { _id: `#${naturalKey}` }))?._id ?? 0;
}

async function getAccountId(context: Context, naturalKey: string): Promise<number> {
    return (await context.tryRead(xtremFinanceData.nodes.Account, { _id: `#${naturalKey}` }))?._id ?? 0;
}

async function getItemId(context: Context, naturalKey: string): Promise<number> {
    return (await context.read(xtremMasterData.nodes.Item, { _id: `#${naturalKey}` }))?._id ?? 0;
}

async function getSupplierId(context: Context, naturalKey: string): Promise<number> {
    return (await context.read(xtremMasterData.nodes.Supplier, { _id: `#${naturalKey}` }))?._id ?? 0;
}

async function getSiteId(context: Context, naturalKey: string): Promise<number> {
    return (await context.read(xtremSystem.nodes.Site, { _id: `#${naturalKey}` }))?._id ?? 0;
}

async function getCustomerId(context: Context, naturalKey: string): Promise<number> {
    return (await context.read(xtremMasterData.nodes.Customer, { _id: `#${naturalKey}` }))?._id ?? 0;
}

async function getTaxValueId(context: Context, code: string): Promise<number> {
    return (await context.tryRead(xtremTax.nodes.Tax, { _id: `#${code}` }))?._id ?? 0;
}

async function createFinanceTransaction(
    context: Context,
    financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument,
): Promise<xtremFinanceData.nodes.FinanceTransaction> {
    // create a finance transaction record (hack since we don't use the notification)
    const financeIntegrationStatusRecord: xtremFinanceData.nodes.FinanceTransaction = await context.create(
        xtremFinanceData.nodes.FinanceTransaction,
        {
            batchId: financeDocument.batchId,
            documentSysId: financeDocument.documentSysId,
            documentNumber: financeDocument.documentNumber,
            documentType: financeDocument.documentType,
            targetDocumentType: financeDocument.targetDocumentType,
            sourceDocumentSysId: financeDocument.sourceDocumentSysId || undefined,
            sourceDocumentNumber: financeDocument.sourceDocumentNumber || undefined,
            sourceDocumentType: financeDocument.sourceDocumentType || undefined,
            status: 'pending',
            message: '',
            lines: financeDocument.sourceLines,
            financialSite: '#US001',
        },
    );
    await financeIntegrationStatusRecord.$.save();
    return financeIntegrationStatusRecord;
}

describe('Accounting staging', () => {
    before(() => {});

    // Skipping this test because onAccountingDataPosting needs a readOnly context
    it.skip('Creation accounting staging from the listener static onAccountingDataPosting function', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '9991',
                    batchSize: 1,
                    documentSysId: 14,
                    documentNumber: 'MISC_RECEIPT14',
                    documentDate: '2021-08-23',
                    documentType: 'miscellaneousStockReceipt',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2802,
                            movementType: 'stockJournal',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Muesli'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 144.0, documentLineType: 'documentLine' }],
                        },
                    ],
                };

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '1234';
                (context as any)._contextValues.replyTopic = 'StockReceipt/accountingInterface';
                await xtremFinance.nodes.AccountingInterfaceListener.onAccountingDataPosting(context, financeDocument);

                const stagingLine = (
                    await context
                        .query(xtremFinanceData.nodes.AccountingStaging, {
                            filter: { baseDocumentLine: financeDocument.documentLines[0].baseDocumentLineSysId },
                        })
                        .toArray()
                )[0];
                assert.instanceOf(stagingLine, xtremFinanceData.nodes.AccountingStaging);
                assert.equal(await stagingLine.documentNumber, 'MISC_RECEIPT14');
                assert.equal((await stagingLine.documentDate).toString(), '2021-08-23');
                assert.equal(await (await stagingLine.item)?.id, 'Muesli');
                assert.isNotNull(await stagingLine.storedDimensions);
                assert.isNotNull(await stagingLine.storedAttributes);

                const stagingAmountLine = await context.read(xtremFinanceData.nodes.AccountingStagingAmount, {
                    accountingStaging: stagingLine._id,
                });
                assert.instanceOf(stagingAmountLine, xtremFinanceData.nodes.AccountingStagingAmount);
                assert.equal(await stagingAmountLine.amountType, 'amount');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Creation accounting staging record that fails test', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '9991-A',
                    batchSize: 1,
                    documentSysId: 14,
                    documentNumber: 'MISC_RECEIPT14',
                    documentDate: '2021-08-23',
                    documentType: 'miscellaneousStockReceipt',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'AAA'),
                    financialSiteSysId: 999999,
                    documentLines: [
                        {
                            baseDocumentLineSysId: 999999,
                            movementType: 'stockJournal',
                            sourceDocumentNumber: '',
                            stockJournalSysId: 999999,
                            currencySysId: await getCurrencyId(context, 'AAA'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: 999999,
                            accountSysId: await getAccountId(context, 'IDONOTEXIST'),
                            customerSysId: 999999,
                            supplierSysId: 999999,
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [
                                {
                                    amountType: 'amount',
                                    amount: 144.0,
                                    baseTaxSysId: 1,
                                    taxSysId: await getTaxValueId(context, 'IDONOTEXIST'),
                                    documentLineType: 'documentLine',
                                },
                            ],
                        },
                    ],
                };

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '1234';
                (context as any)._contextValues.replyTopic = 'StockReceipt/accountingInterface';

                await assert.isRejected(
                    xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                        financeDocument,
                        notificationId: '1234',
                        replyTopic: 'StockReceipt/accountingInterface',
                        isProcessed: false,
                    }),
                );
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Creation of Journal Entry record test (miscellaneous stock receipt)', () =>
        Test.withContext(
            async context => {
                const createJournalEntry = await xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'miscellaneousStockReceipt',
                    'SR250002',
                    'f953f2db-4412-4a7d-800a-3ce23d43b120',
                    'StockReceipt/accountingInterface',
                );

                const journalEntry = await context.read(xtremFinance.nodes.JournalEntry, {
                    number: createJournalEntry.documentsCreated[0].documentNumber,
                });

                assert.instanceOf(journalEntry, xtremFinance.nodes.JournalEntry);
                assert.equal((await journalEntry.postingDate).toString(), '2025-01-28');
                assert.equal(await (await journalEntry.financialSite).id, 'US001');

                const firstJournalEntryLine = await journalEntry.lines.at(0);
                const secondJournalEntryLine = await journalEntry.lines.at(1);
                const journalEntryTypeLine = await firstJournalEntryLine?.journalEntryTypeLine;
                const journalEntryType = await journalEntryTypeLine?.journalEntryType;

                assert.equal(await journalEntryType?.documentType, 'miscellaneousStockReceipt');
                assert.equal(await journalEntryType?.targetDocumentType, 'journalEntry');
                assert.equal(await (await journalEntryType?.legislation)?.id, 'US');
                assert.equal(await (await firstJournalEntryLine?.journalEntryTypeLine)?._sortValue, 100);
                assert.equal(await (await secondJournalEntryLine?.journalEntryTypeLine)?._sortValue, 200);
                assert.equal(await firstJournalEntryLine?.contraAccount, '20680 -- Inventory - GRNI');
                assert.equal(await secondJournalEntryLine?.contraAccount, '13100 -- Inventory');

                assert.equal(await (await firstJournalEntryLine?.account)?.name, 'Inventory');
                assert.equal(await (await secondJournalEntryLine?.account)?.name, 'Inventory - GRNI');

                let journalEntryLinesDimension: xtremFinance.nodes.JournalEntryLineDimension[] | undefined;
                journalEntryLinesDimension = await firstJournalEntryLine?.attributesAndDimensions?.toArray();
                assert.isNotNull(await journalEntryLinesDimension?.at(0)?.storedAttributes);
                assert.isNotNull(await journalEntryLinesDimension?.at(0)?.storedDimensions);

                journalEntryLinesDimension = await secondJournalEntryLine?.attributesAndDimensions?.toArray();
                assert.isNotNull(await journalEntryLinesDimension?.at(0)?.storedAttributes);
                assert.isNotNull(await journalEntryLinesDimension?.at(0)?.storedDimensions);
            },
            {
                today: '2025-01-28',
            },
        ));

    it('Creation and update of Journal Entry test (miscellaneous stock receipt)', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '9992U',
                    batchSize: 2,
                    documentSysId: 15,
                    documentNumber: 'MISC_RECEIPT19',
                    documentDate: '2021-08-23',
                    documentType: 'miscellaneousStockReceipt',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2802,
                            movementType: 'stockJournal',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Muesli'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 144.0, documentLineType: 'documentLine' }],
                        },
                        {
                            baseDocumentLineSysId: 2802,
                            movementType: 'stockJournal',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Muesli'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 144.0, documentLineType: 'documentLine' }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '12340';
                (context as any)._contextValues.replyTopic = 'StockReceipt/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '12340',
                    replyTopic: 'StockReceipt/accountingInterface',
                    isProcessed: false,
                });
                // creation of the journal entry from the data on the accounting staging
                const createJournalEntry = await xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'miscellaneousStockReceipt',
                    'MISC_RECEIPT19',
                    '9992U',
                    'StockReceipt/accountingInterface',
                );

                let journalEntry = await context.read(
                    xtremFinance.nodes.JournalEntry,
                    {
                        number: createJournalEntry.documentsCreated[0].documentNumber,
                    },
                    { forUpdate: true },
                );

                // tests
                assert.instanceOf(journalEntry, xtremFinance.nodes.JournalEntry);
                const line1 = await journalEntry.lines.at(0);
                const line2 = await journalEntry.lines.at(1);
                const line1AttributesAndDimensions = await line1?.attributesAndDimensions.at(0);
                assert.equal(await (await line1AttributesAndDimensions?.project)?.id, undefined);
                assert.equal(await (await line1AttributesAndDimensions?.dimension01)?.id, undefined);
                assert.equal(await (await line1AttributesAndDimensions?.dimension02)?.id, undefined);
                assert.equal(await (await line1AttributesAndDimensions?.dimension03)?.id, 'DIMTYPE1VALUE1');
                assert.equal(await (await line1AttributesAndDimensions?.dimension04)?.id, 'DIMTYPE2VALUE2');
                assert.equal(await (await line1AttributesAndDimensions?.dimension05)?.id, undefined);
                assert.equal(await (await line1?.account)?.id, '13100');
                assert.equal(await (await line2?.account)?.id, '20680');
                assert.equal(await line1?.contraAccount, '20680 -- Inventory - GRNI');
                assert.equal(await line2?.contraAccount, '13100 -- Inventory');
                // set the journal entry posting status to error so we can update it
                journalEntry.pIsPosting = true;
                await journalEntry.$.set({ postingStatus: 'error' });
                await journalEntry.$.save();

                journalEntry = await context.read(xtremFinance.nodes.JournalEntry, {
                    number: createJournalEntry.documentsCreated[0].documentNumber,
                });

                // create an update payload
                const financeDocumentNewData: xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate = {
                    batchId: '9992U',
                    batchSize: 1,
                    documentType: 'miscellaneousStockReceipt',
                    documentSysId: 15,
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    documentNumber: 'MISC_RECEIPT19',
                    targetDocumentType: 'journalEntry',
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2802,
                            storedDimensions: {
                                dimensionType05: 'RETAIL',
                            },
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                                stockSite: 'US002',
                                financialSite: 'US002',
                                item: 'Muesli',
                            },
                        },
                    ],
                };

                // save the updated data on accounting staging. This will also update the journal entry
                const numberOfUpdatedLines =
                    await xtremFinance.nodes.AccountingInterfaceListener.onAccountingDataUpdatePosting(
                        context,
                        financeDocumentNewData,
                    );

                assert.equal(numberOfUpdatedLines, 2);

                const updatedJournalEntry = await context.read(xtremFinance.nodes.JournalEntry, {
                    number: createJournalEntry.documentsCreated[0].documentNumber,
                });

                const newLine1 = await updatedJournalEntry.lines.at(0);
                const newLine2 = await updatedJournalEntry.lines.at(1);
                const newLine1AttributesAndDimensions = await newLine1?.attributesAndDimensions.at(0);

                // tests the updated document
                assert.equal(await (await newLine1AttributesAndDimensions?.project)?.id, 'AttPROJ');
                assert.equal(await (await newLine1AttributesAndDimensions?.dimension01)?.id, undefined);
                assert.equal(await (await newLine1AttributesAndDimensions?.dimension02)?.id, undefined);
                assert.equal(await (await newLine1AttributesAndDimensions?.dimension03)?.id, undefined);
                assert.equal(await (await newLine1AttributesAndDimensions?.dimension04)?.id, undefined);
                assert.equal(await (await newLine1AttributesAndDimensions?.dimension05)?.id, 'RETAIL');
                assert.equal(await updatedJournalEntry.lines.length, await journalEntry.lines.length);
                assert.equal(await (await newLine1?.account)?.id, '13100');
                assert.equal(await (await newLine2?.account)?.id, '20680');
                assert.equal(await newLine1?.contraAccount, '20680 -- Inventory - GRNI');
                assert.equal(await newLine2?.contraAccount, '13100 -- Inventory');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Creation of journal entry from staging test (miscellaneous stock issue)', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '9993',
                    batchSize: 1,
                    documentSysId: 1,
                    documentNumber: 'MISC_ISSUE_99996',
                    documentDate: '2021-08-23',
                    documentType: 'miscellaneousStockIssue',
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2801,
                            movementType: 'stockJournal',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Muesli'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 100.0, documentLineType: 'documentLine' }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '2345';
                (context as any)._contextValues.replyTopic = 'StockIssue/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '2345',
                    replyTopic: 'StockIssue/accountingInterface',
                    isProcessed: false,
                });

                financeDocument.documentDate = '2021-08-24';
                financeDocument.documentLines[0].baseDocumentLineSysId = 2802;
                financeDocument.documentNumber = 'MISC_ISSUE_99997';
                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);
                (context as any)._contextValues.notificationId = '3456';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '3456',
                    replyTopic: 'StockIssue/accountingInterface',
                    isProcessed: false,
                });

                financeDocument.documentDate = '2021-08-25';
                financeDocument.documentLines[0].baseDocumentLineSysId = 2803;
                financeDocument.documentNumber = 'MISC_ISSUE_99998';
                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);
                (context as any)._contextValues.notificationId = '4567';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '4567',
                    replyTopic: 'StockIssue/accountingInterface',
                    isProcessed: false,
                });

                let createJournalEntry1 = await xtremFinance.functions.createJournalsFromAccountingStaging(context, {
                    _and: [{}, { isProcessed: false }, { financialSite: { id: { _eq: 'US002' } } }],
                });

                assert.deepEqual(createJournalEntry1, [
                    {
                        documentsCreated: [],
                        validationMessages: [{ type: 1, message: 'No documents to be processed.' }],
                    },
                ]);

                createJournalEntry1 = await xtremFinance.functions.createJournalsFromAccountingStaging(context, {
                    _and: [
                        {},
                        { isProcessed: false },
                        { financialSite: { id: { _eq: '500' } } },
                        { documentType: { _eq: 'purchaseReceipt' } },
                    ],
                });

                assert.deepEqual(createJournalEntry1, [
                    {
                        documentsCreated: [],
                        validationMessages: [{ type: 1, message: 'No documents to be processed.' }],
                    },
                ]);
                const createJournalEntryReturn = await xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'miscellaneousStockIssue',
                    'MISC_ISSUE_99998',
                    '9993',
                    'StockIssue/accountingInterface',
                );
                assert.equal(createJournalEntryReturn.documentsCreated.length, 1);
                assert.equal(createJournalEntryReturn.documentsCreated[0].documentNumber, 'IJ210005');

                const journalCreated = await context.read(xtremFinance.nodes.JournalEntry, {
                    _id: createJournalEntryReturn.documentsCreated[0].documentSysId,
                });
                assert.equal(await (await (await journalCreated.lines.at(0))?.account)?.id, '13100');
                assert.equal(await (await journalCreated.lines.at(0))?.sign, 'C');
                assert.equal(await (await journalCreated.lines.at(0))?.transactionAmount, 100);
                assert.equal(await (await (await journalCreated.lines.at(1))?.account)?.id, '50200');
                assert.equal(await (await journalCreated.lines.at(1))?.sign, 'D');
                assert.equal(await (await journalCreated.lines.at(1))?.transactionAmount, 100);

                // [RM] createJournalsFromAccountingStaging cannot be tested anymore because it needs to get a readOnly context
                // createJournalEntry1 = xtremFinance.functions.createJournalsFromAccountingStaging(context, false, {
                //     _and: [
                //         {},
                //         { isProcessed: false },
                //         { financialSite: { _id: { _eq: 1 } } },
                //         { documentType: { _eq: 'miscellaneousStockIssue' } },
                //         { documentDate: { _lte: '2021-08-25' } },
                //     ],
                // });
                // assert.equal(createJournalEntry1.trimEnd(), '2 journals created');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Creation of journal entry for stock adjustment', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '9994',
                    batchSize: 1,
                    documentSysId: 1,
                    documentNumber: 'STOCK_ADJUSTMENT_99996',
                    documentDate: '2021-08-23',
                    documentType: 'stockAdjustment',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2801,
                            movementType: 'stockJournal',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Muesli'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 1.0, documentLineType: 'documentLine' }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '5678';
                (context as any)._contextValues.replyTopic = 'StockIssue/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '5678',
                    replyTopic: 'StockAdjustmentOld/accountingInterface',
                    isProcessed: false,
                });

                financeDocument.documentDate = '2021-08-24';
                financeDocument.documentLines[0].baseDocumentLineSysId = 2802;
                financeDocument.documentNumber = 'STOCK_ADJUSTMENT_99997';
                await createFinanceTransaction(context, financeDocument);
                (context as any)._contextValues.notificationId = '6789';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '6789',
                    replyTopic: 'StockAdjustmentOld/accountingInterface',
                    isProcessed: false,
                });

                financeDocument.documentDate = '2021-08-25';
                financeDocument.documentLines[0].baseDocumentLineSysId = 2803;
                financeDocument.documentNumber = 'STOCK_ADJUSTMENT_99998';
                await createFinanceTransaction(context, financeDocument);
                (context as any)._contextValues.notificationId = '7890';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '7890',
                    replyTopic: 'StockAdjustmentOld/accountingInterface',
                    isProcessed: false,
                });

                // [RM] createJournalsFromAccountingStaging cannot be tested anymore because it needs to get a readOnly context
                // let createJournalEntry2 = xtremFinance.functions.createJournalsFromAccountingStaging(context, false, {
                //     _and: [{}, { isProcessed: false }, { financialSite: { _id: { _eq: 2 } } }],
                // });
                // assert.equal(createJournalEntry2.trimEnd(), '0 journals created');

                // createJournalEntry2 = xtremFinance.functions.createJournalsFromAccountingStaging(context, false, {
                //     _and: [
                //         {},
                //         { isProcessed: false },
                //         { financialSite: { _id: { _eq: 1 } } },
                //         { documentType: { _eq: 'purchaseReceipt' } },
                //     ],
                // });
                // assert.equal(createJournalEntry2.trimEnd(), '0 journals created');

                await assert.isRejected(
                    xtremFinance.functions.createOrUpdateJournal(
                        context,
                        'journalEntry',
                        'stockAdjustment',
                        'STOCK_ADJUSTMENT_99996',
                        '9995',
                        'StockAdjustmentOld/accountingInterface',
                    ),
                    `There are no documents to process for document type ${context.localizeEnumMember(
                        '@sage/xtrem-finance-data/FinanceDocumentType',
                        'stockAdjustment',
                    )}, target document type ${context.localizeEnumMember(
                        '@sage/xtrem-finance-data/TargetDocumentType',
                        'journalEntry',
                    )} and document number STOCK_ADJUSTMENT_99996.`,
                );
            },
            {
                today: '2021-08-23',
            },
        ));

    // [RM] createJournalsFromAccountingStaging cannot be tested anymore because it needs to get a readOnly context
    it.skip('Create journal only when we have all the records on accounting staging based (batchSize)', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '9995-AAA',
                    batchSize: 2,
                    documentSysId: 15,
                    documentNumber: 'MISC_RECEIPT15',
                    documentDate: '2021-08-23',
                    documentType: 'miscellaneousStockReceipt',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2802,
                            movementType: 'stockJournal',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Muesli'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 1.0, documentLineType: 'documentLine' }],
                        },
                        {
                            baseDocumentLineSysId: 2803,
                            movementType: 'stockJournal',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Muesli'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 1.0, documentLineType: 'documentLine' }],
                        },
                    ],
                };

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '12345A';
                (context as any)._contextValues.replyTopic = 'StockReceipt/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '12345A',
                    replyTopic: 'StockReceipt/accountingInterface',
                    isProcessed: false,
                });

                // const createJournalEntry1 = await xtremFinance.functions.createJournalsFromAccountingStaging(context);

                // assert.equal(createJournalEntry1.trimEnd(), '1 journal created');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Create journal for a Sales Shipment', () =>
        Test.withContext(
            async context => {
                const createJournalEntryReturn = await xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'salesShipment',
                    'SH250004',
                    'f5b9731b-5932-436e-bf90-99b70b3fea47',
                    '',
                );

                assert.equal(createJournalEntryReturn.documentsCreated.length, 1);
                assert.equal(createJournalEntryReturn.documentsCreated[0].type, 'journalEntry');
            },
            {
                today: '2025-04-03',
            },
        ));

    it('Creation of an accounts receivable invoice', () =>
        Test.withContext(
            async context => {
                const createJournalEntryReturn = await xtremFinance.functions.createJournalsFromAccountingStaging(
                    context,
                    {
                        documentType: 'salesInvoice',
                        documentNumber: 'SIT1R250003',
                        batchId: '11e9b8f8-72e2-484c-a236-0717c02a7441',
                    },
                );

                assert.equal(createJournalEntryReturn.length, 2);
                assert.equal(createJournalEntryReturn[0].documentsCreated.length, 1);
                assert.equal(createJournalEntryReturn[0].documentsCreated[0].type, 'journalEntry');
                assert.equal(createJournalEntryReturn[1].documentsCreated.length, 1);
                assert.equal(createJournalEntryReturn[1].documentsCreated[0].type, 'accountsReceivableInvoice');
            },
            {
                today: '2025-04-03',
            },
        ));

    it('Creation of an accounts receivable invoice and update it', () =>
        Test.withContext(
            async context => {
                const taxes = await context
                    .query(xtremTax.nodes.BaseTax, {
                        filter: { taxRate: 10 },
                    })
                    .toArray();

                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '9997',
                    batchSize: 1,
                    documentSysId: 6,
                    documentNumber: 'SIU6',
                    documentDate: '2021-11-29',
                    documentType: 'salesInvoice',
                    targetDocumentType: 'accountsReceivableInvoice',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    taxes: [{ baseTaxSysId: taxes[0]._id }, { baseTaxSysId: taxes[1]._id }],
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2901,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            customerSysId: await getCustomerId(context, 'US019'),
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-11-29',
                            itemSysId: await getItemId(context, 'Consulting01'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Consulting01',
                            },
                            amounts: [
                                { amountType: 'amountExcludingTax', amount: 4.0, documentLineType: 'documentLine' },
                            ],
                            taxes: [{ baseTaxSysId: taxes[1]._id }, { baseTaxSysId: taxes[2]._id }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '1234UB';
                (context as any)._contextValues.replyTopic = 'SalesInvoice/accountingInterface';

                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '1234UB',
                    replyTopic: 'SalesInvoice/accountingInterface',
                    isProcessed: false,
                });
                const createdAccountsReceivableInvoice = await xtremFinance.functions.createOrUpdateAPARInvoice(
                    context,
                    'accountsReceivableInvoice',
                    'salesInvoice',
                    'SIU6',
                    '9997',
                    'SalesInvoice/accountingInterface',
                );

                assert.equal(createdAccountsReceivableInvoice.documentsCreated.length, 1);
                let accountsReceivableInvoice = await context.read(
                    xtremFinance.nodes.AccountsReceivableInvoice,
                    {
                        number: createdAccountsReceivableInvoice.documentsCreated[0].documentNumber,
                    },
                    { forUpdate: true },
                );

                // set the journal entry posting status to error so we can update it
                accountsReceivableInvoice.pCanUpdateFromExternalIntegration = true;
                await accountsReceivableInvoice.$.set({ postingStatus: 'error' });
                await accountsReceivableInvoice.$.save();

                accountsReceivableInvoice = await context.read(xtremFinance.nodes.AccountsReceivableInvoice, {
                    number: createdAccountsReceivableInvoice.documentsCreated[0].documentNumber,
                });

                // create an update payload
                const financeDocumentNewData: xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate = {
                    batchId: '9997',
                    batchSize: 1,
                    documentType: 'salesInvoice',
                    documentSysId: 6,
                    financialSiteSysId: 3,
                    documentNumber: 'SIU6',
                    targetDocumentType: 'accountsReceivableInvoice',
                    paymentTerm: await accountsReceivableInvoice.paymentTerm,
                    dueDate: (await accountsReceivableInvoice.dueDate).toString(),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2901,
                            storedDimensions: {
                                dimensionType05: 'RETAIL',
                            },
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                                stockSite: 'US002',
                                financialSite: 'US002',
                                item: 'Muesli',
                            },
                        },
                    ],
                };

                // save the updated data on accounting staging. This will also update the journal entry
                const numberOfUpdatedLines =
                    await xtremFinanceData.functions.AccountingEngineCommon.updateAccountingStaging(
                        context,
                        financeDocumentNewData,
                    );

                assert.equal(numberOfUpdatedLines, 1);

                const updatedAccountsReceivableInvoiceResult = await xtremFinance.functions.createOrUpdateAPARInvoice(
                    context,
                    'accountsReceivableInvoice',
                    'salesInvoice',
                    'SIU6',
                    '9997',
                    'SalesInvoice/accountingInterface',
                    true,
                );

                assert.equal(updatedAccountsReceivableInvoiceResult.documentsCreated.length, 1);

                const updatedAccountsReceivableInvoice = await context.read(
                    xtremFinance.nodes.AccountsReceivableInvoice,
                    {
                        number: createdAccountsReceivableInvoice.documentsCreated[0].documentNumber,
                    },
                );

                // tests the updated document
                assert.equal(
                    await (
                        await (
                            await (await updatedAccountsReceivableInvoice.lines.at(0))?.attributesAndDimensions.at(0)
                        )?.project
                    )?.id,
                    'AttPROJ',
                );
                assert.equal(
                    await (
                        await (
                            await (await updatedAccountsReceivableInvoice.lines.at(0))?.attributesAndDimensions.at(0)
                        )?.dimension01
                    )?.id,
                    undefined,
                );
                assert.equal(
                    await (
                        await (
                            await (await updatedAccountsReceivableInvoice.lines.at(0))?.attributesAndDimensions.at(0)
                        )?.dimension02
                    )?.id,
                    undefined,
                );
                assert.equal(
                    await (
                        await (
                            await (await updatedAccountsReceivableInvoice.lines.at(0))?.attributesAndDimensions.at(0)
                        )?.dimension03
                    )?.id,
                    undefined,
                );
                assert.equal(
                    await (
                        await (
                            await (await updatedAccountsReceivableInvoice.lines.at(0))?.attributesAndDimensions.at(0)
                        )?.dimension04
                    )?.id,
                    undefined,
                );
                assert.equal(
                    await (
                        await (
                            await (await updatedAccountsReceivableInvoice.lines.at(0))?.attributesAndDimensions.at(0)
                        )?.dimension05
                    )?.id,
                    'RETAIL',
                );
                assert.equal(
                    await updatedAccountsReceivableInvoice.lines.length,
                    await accountsReceivableInvoice.lines.length,
                );
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Creation of an accounts receivable invoice that fails because it cannot find an account', () =>
        Test.withContext(
            async context => {
                const taxes = await context.query(xtremTax.nodes.BaseTax, { filter: { taxRate: 10 } }).toArray();
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '9997',
                    batchSize: 1,
                    documentSysId: 6,
                    documentNumber: 'SI6',
                    documentDate: '2021-11-29',
                    documentType: 'salesInvoice',
                    targetDocumentType: 'accountsReceivableInvoice',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    taxes: [{ baseTaxSysId: taxes[0]._id }, { baseTaxSysId: taxes[1]._id }],
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2901,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            customerSysId: await getCustomerId(context, 'US019'),
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-11-29',
                            itemSysId: await getItemId(context, 'Consulting01'),
                            storedDimensions: { dimensionType03: 'MANUFACTURING', dimensionType04: 'DIMTYPE2VALUE2' },
                            storedAttributes: {
                                project: 'AttPROJ2',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Consulting01',
                            },
                            amounts: [
                                { amountType: 'amountExcludingTax', amount: 4.0, documentLineType: 'documentLine' },
                            ],
                            taxes: [{ baseTaxSysId: taxes[1]._id }, { baseTaxSysId: taxes[2]._id }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '1234UB';
                (context as any)._contextValues.replyTopic = 'SalesInvoice/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '1234UB',
                    replyTopic: 'SalesInvoice/accountingInterface',
                    isProcessed: false,
                });

                await assert.isRejected(
                    xtremFinance.functions.createOrUpdateAPARInvoice(
                        context,
                        'accountsReceivableInvoice',
                        'salesInvoice',
                        'SI6',
                        'This_Batch_id_does_not_exists',
                        'SalesInvoice/accountingInterface',
                    ),
                    'There are no documents to process for document type Sales invoice, target document type Accounts receivable invoice and document number SI6.',
                );
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Creation of an accounts receivable invoice with tax lines', () =>
        Test.withContext(
            async context => {
                const batchId = '99970';
                const documentNumber = 'SI1';
                const documentType: xtremFinanceData.enums.FinanceDocumentType = 'salesInvoice';
                const targetDocumentType: xtremFinanceData.enums.TargetDocumentType = 'accountsReceivableInvoice';
                const replyTopic = 'SalesInvoice/accountingInterface';
                const notificationId = '1234UB';

                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId,
                    batchSize: 1,
                    documentSysId: 1,
                    documentNumber,
                    documentDate: '2021-04-22',
                    documentType,
                    targetDocumentType,
                    currencySysId: await getCurrencyId(context, 'ZAR'),
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    taxes: [{ baseTaxSysId: 1100 }],
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2902,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            customerSysId: await getCustomerId(context, 'US019'),
                            currencySysId: await getCurrencyId(context, 'ZAR'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-04-22',
                            itemSysId: await getItemId(context, 'Chemical D'),
                            storedDimensions: {},
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Consulting01',
                            },
                            amounts: [
                                { amountType: 'amountExcludingTax', amount: 4.0, documentLineType: 'documentLine' },
                            ],
                            taxes: [{ baseTaxSysId: 2901 }, { baseTaxSysId: 2901 }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = notificationId;
                (context as any)._contextValues.replyTopic = replyTopic;
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '1234UB',
                    replyTopic,
                    isProcessed: false,
                });
                const createdAccountsReceivableInvoice = await xtremFinance.functions.createOrUpdateAPARInvoice(
                    context,
                    targetDocumentType,
                    documentType,
                    documentNumber,
                    batchId,
                    replyTopic,
                );
                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                assert.equal(createdAccountsReceivableInvoice.documentsCreated.length, 1);
                const accountsReceivableInvoice = await context.read(
                    xtremFinance.nodes.AccountsReceivableInvoice,
                    {
                        number: createdAccountsReceivableInvoice.documentsCreated[0].documentNumber,
                    },
                    { forUpdate: true },
                );

                const financeTransactionData: xtremFinanceData.interfaces.FinanceTransactionData = {
                    batchId,
                    documentNumber,
                    documentType,
                    targetDocumentType,
                    targetDocumentNumber: await accountsReceivableInvoice.number,
                    targetDocumentSysId: accountsReceivableInvoice._id,
                    validationMessages: [],
                    status: 'recorded',
                };

                await xtremFinance.nodes.AccountsReceivableInvoice.onFinanceIntegrationReply(
                    context,
                    financeTransactionData,
                );

                assert.instanceOf(accountsReceivableInvoice, xtremFinance.nodes.AccountsReceivableInvoice);
                assert.equal((await accountsReceivableInvoice.postingDate).toString(), '2021-04-22');
                assert.equal(await (await accountsReceivableInvoice.financialSite).id, 'US001');
                assert.equal((await accountsReceivableInvoice.totalAmountIncludingTax).toString(), '404');
                assert.equal((await accountsReceivableInvoice.totalTaxAmount).toString(), '400');
                assert.equal((await accountsReceivableInvoice.totalTaxableAmount).toString(), '4000');
                assert.equal((await accountsReceivableInvoice.totalExemptAmount).toString(), '0');

                assert.equal(await accountsReceivableInvoice.financeIntegrationApp, null);
                assert.equal(await accountsReceivableInvoice.financeIntegrationStatus, 'posted');
                assert.equal(await accountsReceivableInvoice.internalFinanceIntegrationStatus, 'recorded');
                assert.equal(await accountsReceivableInvoice.financeIntegrationAppRecordId, '');
                assert.equal(await accountsReceivableInvoice.financeIntegrationAppUrl, '');

                const accountsReceivableInvoiceLines = await accountsReceivableInvoice.lines.toArray();
                assert.equal(accountsReceivableInvoiceLines.length, 1);
                assert.equal(await accountsReceivableInvoiceLines[0].amountIncludingTax, 404);
                assert.equal(await accountsReceivableInvoiceLines[0].taxAmount, 400);

                // TODO: [RM] review
                // const accountsReceivableInvoiceTaxes = await accountsReceivableInvoice.taxes.toArray();
                // assert.equal(accountsReceivableInvoiceTaxes.length, 1);
                // assert.equal(await accountsReceivableInvoiceTaxes[0].tax, 'Sales tax');
                // assert.equal(await accountsReceivableInvoiceTaxes[0].taxRate, 10);

                const accountsReceivableInvoiceLineTaxes = await accountsReceivableInvoiceLines[0].taxes.toArray();
                assert.equal(accountsReceivableInvoiceLineTaxes.length, 2);
                assert.equal(await accountsReceivableInvoiceLineTaxes[0].tax, 'Sales tax');
                assert.equal(await accountsReceivableInvoiceLineTaxes[0].taxRate, 10);
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Creation of an accounts receivable invoice with the wrong targetDocument - fail', () =>
        Test.withContext(async context => {
            await assert.isRejected(
                xtremFinance.functions.createOrUpdateAPARInvoice(
                    context,
                    'journalEntry',
                    'salesInvoice',
                    'SI6',
                    '9997',
                    'SalesInvoice/accountingInterface',
                ),
                `The target document type ${context.localizeEnumMember(
                    '@sage/xtrem-finance-data/TargetDocumentType',
                    'journalEntry',
                )} is not supported.`,
            );
        }));

    it('Creation of an accounts receivable invoice with the wrong document number - fail', () =>
        Test.withContext(async context => {
            await assert.isRejected(
                xtremFinance.functions.createOrUpdateAPARInvoice(
                    context,
                    'accountsReceivableInvoice',
                    'salesInvoice',
                    'NON_EXISTENT_DOC',
                    '9997',
                    'SalesInvoice/accountingInterface',
                ),
                `There are no documents to process for document type Sales invoice, target document type Accounts receivable invoice and document number NON_EXISTENT_DOC.`,
            );
        }));

    // skipped because we no longer read the original document since we don't have references to sales and purchase
    it.skip('Create journal for a APAR invoice fails - document does not exist (is a shipment)', () =>
        Test.withContext(async context => {
            const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                batchId: '9996',
                batchSize: 2,
                documentSysId: 1,
                documentNumber: 'SALES_SHIPMENT_999990',
                documentDate: '2021-08-23',
                documentType: 'salesInvoice',
                targetDocumentType: 'accountsReceivableInvoice',
                currencySysId: await getCurrencyId(context, 'USD'),
                financialSiteSysId: await getSiteId(context, 'US001'),
                documentLines: [
                    {
                        baseDocumentLineSysId: 2801,
                        movementType: 'document',
                        sourceDocumentNumber: '',
                        currencySysId: await getCurrencyId(context, 'USD'),
                        companyFxRate: 1,
                        companyFxRateDivisor: 1,
                        fxRateDate: '2021-08-23',
                        customerSysId: await getCustomerId(context, 'US019'),
                        itemSysId: await getItemId(context, 'Muesli'),
                        storedDimensions: { dimensionType03: 'DIMTYPE1VALUE1', dimensionType04: 'DIMTYPE2VALUE2' },
                        storedAttributes: {
                            project: '',
                            task: '',
                            employee: '',
                            stockSite: 'US001',
                            financialSite: 'US001',
                            item: 'Muesli',
                        },
                        amounts: [{ amountType: 'amount', amount: 100.0, documentLineType: 'documentLine' }],
                    },
                ],
            };

            // hack to make the state of the context when been triggered by a notification
            (context as any)._contextValues.notificationId = '1678';
            (context as any)._contextValues.replyTopic = 'SalesShipment/accountingInterface';
            await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                financeDocument,
                notificationId: '1678',
                replyTopic: 'SalesShipment/accountingInterface',
                isProcessed: false,
            });

            await assert.isRejected(
                xtremFinance.functions.createOrUpdateAPARInvoice(
                    context,
                    'accountsReceivableInvoice',
                    'salesInvoice',
                    'SALES_SHIPMENT_999990',
                    '9996',
                    'SalesShipment/accountingInterface',
                ),
                'The invoice number SALES_SHIPMENT_999990 could not be found.',
            );
        }));

    it('Create journal for an AR invoice fails - the journal entry to be created has no lines', () =>
        Test.withContext(async context => {
            const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                batchId: '99952',
                batchSize: 4,
                documentSysId: 2,
                documentNumber: 'AR-**********',
                documentDate: '2021-10-05',
                documentType: 'arInvoice',
                targetDocumentType: 'journalEntry',
                currencySysId: await getCurrencyId(context, 'EUR'),
                financialSiteSysId: await getSiteId(context, 'US001'),
                documentLines: [
                    {
                        baseDocumentLineSysId: 2905,
                        movementType: 'document',
                        sourceDocumentNumber: '',
                        currencySysId: await getCurrencyId(context, 'EUR'),
                        companyFxRate: 1,
                        companyFxRateDivisor: 1,
                        fxRateDate: '2021-10-05',
                        accountSysId: await getAccountId(context, '********|FR_DEFAULT'),
                        amounts: [
                            {
                                amountType: 'amountExcludingTax',
                                amount: 1000.0,
                                baseTaxSysId: 2900,
                                taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_DEDUCTIBLE_ON_FA'),
                                documentLineType: 'documentLine',
                            },
                            {
                                amountType: 'deductibleTaxAmount',
                                amount: 100.0,
                                baseTaxSysId: 2900,
                                taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_DEDUCTIBLE_ON_FA'),
                                documentLineType: 'documentLine',
                            },
                        ],
                        storedDimensions: { dimensionType03: 'DIMTYPE1VALUE1', dimensionType04: 'DIMTYPE2VALUE2' },
                        storedAttributes: {
                            project: '',
                            task: '',
                            employee: '',
                            stockSite: '',
                            financialSite: '',
                            item: '',
                        },
                    },
                ],
            };

            // create a finance transaction record (hack since we don't use the notification)
            await createFinanceTransaction(context, financeDocument);

            // hack to make the state of the context when been triggered by a notification
            (context as any)._contextValues.notificationId = '1678';
            (context as any)._contextValues.replyTopic = 'AccountsReceivableInvoice/accountingInterface';
            await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                financeDocument,
                notificationId: '1678',
                replyTopic: 'AccountsReceivableInvoice/accountingInterface',
                isProcessed: false,
            });

            await assert.isRejected(
                xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'arInvoice',
                    'AR-**********',
                    '99952',
                    'AccountsReceivableInvoice/accountingInterface',
                ),
                'The journal entry to be created has no lines.',
            );
        }));

    it('Creation of an accounts payable invoice', () =>
        Test.withContext(
            async context => {
                const taxes = await context
                    .query(xtremTax.nodes.BaseTax, {
                        filter: { taxRate: 10 },
                    })
                    .toArray();
                const supplierZa700 = await context.read(xtremMasterData.nodes.Supplier, { _id: '#700' });
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '999902',
                    batchSize: 1,
                    documentSysId: 3,
                    documentNumber: 'PI3',
                    documentDate: '2020-08-18',
                    documentType: 'purchaseInvoice',
                    targetDocumentType: 'accountsPayableInvoice',
                    currencySysId: await getCurrencyId(context, 'GBP'),
                    financialSiteSysId: await getSiteId(context, 'US005'),
                    taxes: [{ baseTaxSysId: taxes[0]._id }, { baseTaxSysId: taxes[1]._id }],
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2901,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            supplierSysId: supplierZa700._id,
                            currencySysId: await getCurrencyId(context, 'GBP'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2020-08-18',
                            itemSysId: await getItemId(context, 'SalesItem81'),
                            storedDimensions: { dimensionType03: 'DIMTYPE1VALUE2' },
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                            amounts: [
                                {
                                    amountType: 'amountExcludingTax',
                                    amount: 10.0,
                                    documentLineType: 'documentLine',
                                },
                            ],
                            taxes: [{ baseTaxSysId: taxes[1]._id }, { baseTaxSysId: taxes[2]._id }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '1234PI';
                (context as any)._contextValues.replyTopic = 'PurchaseInvoice/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '1234PI',
                    replyTopic: 'PurchaseInvoice/accountingInterface',
                    isProcessed: false,
                });

                const stagingLine = (
                    await context
                        .query(xtremFinanceData.nodes.AccountingStaging, {
                            filter: { baseDocumentLine: financeDocument.documentLines[0].baseDocumentLineSysId },
                        })
                        .toArray()
                )[0];
                assert.instanceOf(stagingLine, xtremFinanceData.nodes.AccountingStaging);
                assert.equal(await stagingLine.documentNumber, 'PI3');

                const stagingLineTaxes = await context
                    .query(xtremFinanceData.nodes.AccountingStagingLineTax, {
                        filter: { accountingStaging: stagingLine._id },
                    })
                    .toArray();
                assert.instanceOf(stagingLineTaxes[0], xtremFinanceData.nodes.AccountingStagingLineTax);
                assert.equal((await stagingLineTaxes[0].baseTax)._id, taxes[1]._id);
                assert.equal((await stagingLineTaxes[1].baseTax)._id, taxes[2]._id);

                const stagingDocumentTaxes = await context
                    .query(xtremFinanceData.nodes.AccountingStagingDocumentTax, {
                        filter: {
                            batchId: '999902',
                            documentNumber: 'PI3',
                            documentType: 'purchaseInvoice',
                            targetDocumentType: 'accountsPayableInvoice',
                        },
                    })
                    .toArray();
                assert.instanceOf(stagingDocumentTaxes[0], xtremFinanceData.nodes.AccountingStagingDocumentTax);
                assert.equal((await stagingDocumentTaxes[0].baseTax)._id, taxes[0]._id);
                assert.equal((await stagingDocumentTaxes[1].baseTax)._id, taxes[1]._id);

                const createdAccountsPayableInvoice = await xtremFinance.functions.createOrUpdateAPARInvoice(
                    context,
                    'accountsPayableInvoice',
                    'purchaseInvoice',
                    'PI3',
                    '999902',
                    'PurchaseInvoice/accountingInterface',
                );

                assert.equal(createdAccountsPayableInvoice.documentsCreated.length, 1);
                const accountsPayableInvoice = await context.read(xtremFinance.nodes.AccountsPayableInvoice, {
                    number: createdAccountsPayableInvoice.documentsCreated[0].documentNumber,
                });

                assert.instanceOf(accountsPayableInvoice, xtremFinance.nodes.AccountsPayableInvoice);
                assert.equal((await accountsPayableInvoice.postingDate).toString(), '2020-08-18');
                assert.equal(await (await accountsPayableInvoice.financialSite).id, 'US005');

                const accountsPayableInvoiceLines = await accountsPayableInvoice.lines.toArray();
                assert.equal(accountsPayableInvoiceLines.length, 1);

                const accountsPayableInvoiceLinesDimension =
                    await accountsPayableInvoiceLines[0].attributesAndDimensions.toArray();
                assert.equal(accountsPayableInvoiceLinesDimension.length, 1);
                assert.isNotNull(await accountsPayableInvoiceLinesDimension[0].storedAttributes);
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Creation and update of an accounts payable invoice', () =>
        Test.withContext(
            async context => {
                const taxes = await context
                    .query(xtremTax.nodes.BaseTax, {
                        filter: { taxRate: 10 },
                    })
                    .toArray();
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '999902',
                    batchSize: 1,
                    documentSysId: 3,
                    documentNumber: 'PIU3',
                    documentDate: '2020-08-18',
                    documentType: 'purchaseInvoice',
                    targetDocumentType: 'accountsPayableInvoice',
                    currencySysId: await getCurrencyId(context, 'GBP'),
                    financialSiteSysId: await getSiteId(context, 'US005'),
                    taxes: [{ baseTaxSysId: taxes[0]._id }, { baseTaxSysId: taxes[1]._id }],
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2901,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            supplierSysId: await getSupplierId(context, '700'),
                            currencySysId: await getCurrencyId(context, 'GBP'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2020-08-18',
                            itemSysId: await getItemId(context, 'SalesItem81'),
                            storedDimensions: { dimensionType03: 'DIMTYPE1VALUE2' },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                            amounts: [
                                {
                                    amountType: 'amountExcludingTax',
                                    amount: 10.0,
                                    documentLineType: 'documentLine',
                                },
                            ],
                            taxes: [{ baseTaxSysId: taxes[1]._id }, { baseTaxSysId: taxes[2]._id }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '1234PI';
                (context as any)._contextValues.replyTopic = 'PurchaseInvoice/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '1234PI',
                    replyTopic: 'PurchaseInvoice/accountingInterface',
                    isProcessed: false,
                });

                const createdAccountsPayableInvoice = await xtremFinance.functions.createOrUpdateAPARInvoice(
                    context,
                    'accountsPayableInvoice',
                    'purchaseInvoice',
                    'PIU3',
                    '999902',
                    'PurchaseInvoice/accountingInterface',
                );

                assert.equal(createdAccountsPayableInvoice.documentsCreated.length, 1);
                let accountsPayableInvoice = await context.read(
                    xtremFinance.nodes.AccountsPayableInvoice,
                    {
                        number: createdAccountsPayableInvoice.documentsCreated[0].documentNumber,
                    },
                    { forUpdate: true },
                );

                // set the ap invoice posting status to error so we can update it
                accountsPayableInvoice.pCanUpdateFromExternalIntegration = true;
                await accountsPayableInvoice.$.set({ postingStatus: 'error' });
                await accountsPayableInvoice.$.save();

                accountsPayableInvoice = await context.read(xtremFinance.nodes.AccountsPayableInvoice, {
                    number: createdAccountsPayableInvoice.documentsCreated[0].documentNumber,
                });

                // create an update payload
                const financeDocumentNewData: xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate = {
                    batchId: '999902',
                    batchSize: 1,
                    documentType: 'purchaseInvoice',
                    documentSysId: 3,
                    financialSiteSysId: 3,
                    documentNumber: 'PIU3',
                    targetDocumentType: 'accountsPayableInvoice',
                    paymentTerm: await accountsPayableInvoice.paymentTerm,
                    dueDate: (await accountsPayableInvoice.dueDate).toString(),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2901,
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType05: 'RETAIL',
                            },
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                                stockSite: 'US002',
                                financialSite: 'US002',
                                item: 'Muesli',
                            },
                        },
                    ],
                };

                // save the updated data on accounting staging. This will also update the journal entry
                const numberOfUpdatedLines =
                    await xtremFinanceData.functions.AccountingEngineCommon.updateAccountingStaging(
                        context,
                        financeDocumentNewData,
                    );

                assert.equal(numberOfUpdatedLines, 1);

                const updatedAccountsPayableInvoiceResult = await xtremFinance.functions.createOrUpdateAPARInvoice(
                    context,
                    'accountsPayableInvoice',
                    'purchaseInvoice',
                    'PIU3',
                    '999902',
                    'PurchaseInvoice/accountingInterface',
                    true,
                );

                assert.equal(updatedAccountsPayableInvoiceResult.documentsCreated.length, 1);

                const updatedAccountsPayableInvoice = await context.read(xtremFinance.nodes.AccountsPayableInvoice, {
                    number: createdAccountsPayableInvoice.documentsCreated[0].documentNumber,
                });

                // tests the updated document
                assert.equal(
                    await (
                        await (
                            await (await updatedAccountsPayableInvoice.lines.at(0))?.attributesAndDimensions.at(0)
                        )?.project
                    )?.id,
                    'AttPROJ',
                );
                assert.equal(
                    await (
                        await (
                            await (await updatedAccountsPayableInvoice.lines.at(0))?.attributesAndDimensions.at(0)
                        )?.dimension01
                    )?.id,
                    undefined,
                );
                assert.equal(
                    await (
                        await (
                            await (await updatedAccountsPayableInvoice.lines.at(0))?.attributesAndDimensions.at(0)
                        )?.dimension02
                    )?.id,
                    undefined,
                );
                assert.equal(
                    await (
                        await (
                            await (await updatedAccountsPayableInvoice.lines.at(0))?.attributesAndDimensions.at(0)
                        )?.dimension03
                    )?.id,
                    'DIMTYPE1VALUE1',
                );
                assert.equal(
                    await (
                        await (
                            await (await updatedAccountsPayableInvoice.lines.at(0))?.attributesAndDimensions.at(0)
                        )?.dimension04
                    )?.id,
                    undefined,
                );
                assert.equal(
                    await (
                        await (
                            await (await updatedAccountsPayableInvoice.lines.at(0))?.attributesAndDimensions.at(0)
                        )?.dimension05
                    )?.id,
                    'RETAIL',
                );
                assert.equal(
                    await updatedAccountsPayableInvoice.lines.length,
                    await accountsPayableInvoice.lines.length,
                );
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Creation of an accounts payable invoice with tax lines', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '9999021',
                    batchSize: 1,
                    documentSysId: 3,
                    documentNumber: 'PI3',
                    documentDate: '2020-08-18',
                    documentType: 'purchaseInvoice',
                    targetDocumentType: 'accountsPayableInvoice',
                    currencySysId: await getCurrencyId(context, 'GBP'),
                    financialSiteSysId: await getSiteId(context, 'US005'),
                    taxes: [{ baseTaxSysId: 2902 }],
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2902,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            supplierSysId: await getSupplierId(context, '700'),
                            currencySysId: await getCurrencyId(context, 'GBP'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2020-08-18',
                            itemSysId: await getItemId(context, 'SalesItem81'),
                            storedDimensions: { dimensionType03: 'DIMTYPE1VALUE2' },
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                            amounts: [
                                {
                                    amountType: 'amountExcludingTax',
                                    amount: 10.0,
                                    documentLineType: 'documentLine',
                                },
                            ],
                            taxes: [{ baseTaxSysId: 3100 }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '1234PI';
                (context as any)._contextValues.replyTopic = 'PurchaseInvoice/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '1234PI',
                    replyTopic: 'PurchaseInvoice/accountingInterface',
                    isProcessed: false,
                });
                const createdAccountsPayableInvoice = await xtremFinance.functions.createOrUpdateAPARInvoice(
                    context,
                    'accountsPayableInvoice',
                    'purchaseInvoice',
                    'PI3',
                    '9999021',
                    'PurchaseInvoice/accountingInterface',
                );

                assert.equal(createdAccountsPayableInvoice.documentsCreated.length, 1);
                const accountsPayableInvoice = await context.read(xtremFinance.nodes.AccountsPayableInvoice, {
                    number: createdAccountsPayableInvoice.documentsCreated[0].documentNumber,
                });

                assert.instanceOf(accountsPayableInvoice, xtremFinance.nodes.AccountsPayableInvoice);
                assert.equal((await accountsPayableInvoice.postingDate).toString(), '2020-08-18');
                assert.equal(await (await accountsPayableInvoice.financialSite).id, 'US005');

                assert.equal(await accountsPayableInvoice.totalAmountIncludingTax, 110);
                assert.equal(await accountsPayableInvoice.totalTaxAmount, 100);
                assert.equal(await accountsPayableInvoice.totalTaxableAmount, 1000);
                assert.equal(await accountsPayableInvoice.totalExemptAmount, 0);

                const accountsPayableInvoiceLines = await accountsPayableInvoice.lines.toArray();
                assert.equal(accountsPayableInvoiceLines.length, 1);

                assert.equal(await accountsPayableInvoiceLines[0].amountIncludingTax, 110);
                assert.equal(await accountsPayableInvoiceLines[0].taxAmount, 100);

                // const accountsPayableInvoiceTaxes = await accountsPayableInvoice.taxes.toArray();

                // TODO: [RM] review
                // assert.equal(accountsPayableInvoiceTaxes.length, 1);
                // assert.equal(await accountsPayableInvoiceTaxes[0].tax, 'Purchase tax');
                // assert.equal(await accountsPayableInvoiceTaxes[0].taxRate, 10);

                const accountsPayableInvoiceLineTaxes = await accountsPayableInvoiceLines[0].taxes.toArray();
                assert.equal(accountsPayableInvoiceLineTaxes.length, 1);
                assert.equal(await accountsPayableInvoiceLineTaxes[0].tax, 'Normal rate collected on debits');
                assert.equal(await accountsPayableInvoiceLineTaxes[0].taxRate, 10);

                const accountsPayableInvoiceLinesDimension =
                    await accountsPayableInvoiceLines[0].attributesAndDimensions.toArray();
                assert.equal(accountsPayableInvoiceLinesDimension.length, 1);
                assert.isNotNull(await accountsPayableInvoiceLinesDimension[0].storedAttributes);
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Create journal for a Production tracking', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '9998',
                    batchSize: 1,
                    documentSysId: 1,
                    documentNumber: 'PRODUCTION_TRACK_9998',
                    documentDate: '2021-08-23',
                    documentType: 'workInProgress',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2801,
                            movementType: 'productionTracking',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Chair'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 235.0, documentLineType: 'documentLine' }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '1666';
                (context as any)._contextValues.replyTopic = 'ProductionTracking/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '1666',
                    replyTopic: 'ProductionTracking/accountingInterface',
                    isProcessed: false,
                });

                // [RM] createJournalsFromAccountingStaging cannot be tested anymore because it needs to get a readOnly context
                // const createJournalEntry4 = xtremFinance.functions.createJournalsFromAccountingStaging(context, false, {
                //     _and: [
                //         {},
                //         { isProcessed: false },
                //         { financialSite: { _id: { _eq: 1 } } },
                //         { documentType: { _eq: 'workInProgress' } },
                //     ],
                // });
                // assert.equal(createJournalEntry4.trimEnd(), '1 journal created');

                const createJournalEntryReturn = await xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'workInProgress',
                    'PRODUCTION_TRACK_9998',
                    '9998',
                    'ProductionTracking/accountingInterface',
                );

                assert.equal(createJournalEntryReturn.documentsCreated.length, 1);
                assert.equal(createJournalEntryReturn.documentsCreated[0].type, 'journalEntry');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Create journal for a Material tracking', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '999901',
                    batchSize: 3,
                    documentSysId: 1,
                    documentNumber: 'Material_TRACK_9998',
                    documentDate: '2021-08-23',
                    documentType: 'workInProgress',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2801,
                            movementType: 'materialTracking',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Chair'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 211.32, documentLineType: 'documentLine' }],
                        },
                        {
                            baseDocumentLineSysId: 2801,
                            movementType: 'materialTracking',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Chair'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 412.87, documentLineType: 'documentLine' }],
                        },
                        {
                            baseDocumentLineSysId: 2801,
                            movementType: 'materialTracking',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Chair'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 165.16, documentLineType: 'documentLine' }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '6666';
                (context as any)._contextValues.replyTopic = 'MaterialTracking/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '666',
                    replyTopic: 'MaterialTracking/accountingInterface',
                    isProcessed: false,
                });

                // [RM] createJournalsFromAccountingStaging cannot be tested anymore because it needs to get a readOnly context
                // const createJournalEntry5 = xtremFinance.functions.createJournalsFromAccountingStaging(context, false, {
                //     _and: [
                //         {},
                //         { isProcessed: false },
                //         { financialSite: { _id: { _eq: 1 } } },
                //         { documentType: { _eq: 'workInProgress' } },
                //     ],
                // });
                // assert.equal(createJournalEntry5.trimEnd(), '1 journal created');

                const createJournalEntryReturn = await xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'workInProgress',
                    'Material_TRACK_9998',
                    '999901',
                    'MaterialTracking/accountingInterface',
                );

                assert.equal(createJournalEntryReturn.documentsCreated.length, 1);
                assert.equal(createJournalEntryReturn.documentsCreated[0].type, 'journalEntry');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Create journal for an Operation tracking', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '999801',
                    batchSize: 2,
                    documentSysId: 1,
                    documentNumber: 'Operation_TRACK_9998',
                    documentDate: '2021-08-23',
                    documentType: 'workInProgress',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2801,
                            movementType: 'laborRunTimeTracking',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Chair'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 211.32, documentLineType: 'documentLine' }],
                        },
                        {
                            baseDocumentLineSysId: 2801,
                            movementType: 'machineRunTimeTracking',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Chair'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 412.87, documentLineType: 'documentLine' }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '6667';
                (context as any)._contextValues.replyTopic = 'OperationTracking/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '6667',
                    replyTopic: 'OperationTracking/accountingInterface',
                    isProcessed: false,
                });

                const createJournalEntryReturn = await xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'workInProgress',
                    'Operation_TRACK_9998',
                    '999801',
                    'OperationTracking/accountingInterface',
                );

                assert.equal(createJournalEntryReturn.documentsCreated.length, 1);
                assert.equal(createJournalEntryReturn.documentsCreated[0].type, 'journalEntry');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Create journal for a work order closing', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '999701',
                    batchSize: 1,
                    documentSysId: 1,
                    documentNumber: 'WO_CLOSE_9999',
                    documentDate: '2021-08-23',
                    documentType: 'workInProgress',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2801,
                            movementType: 'workOrderVariance',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Chair'),
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 211.32, documentLineType: 'documentLine' }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '6668';
                (context as any)._contextValues.replyTopic = 'WorkOrderClosing/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '6668',
                    replyTopic: 'WorkOrderClosing/accountingInterface',
                    isProcessed: false,
                });

                const createJournalEntryReturn = await xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'workInProgress',
                    'WO_CLOSE_9999',
                    '999701',
                    'WorkOrderClosing/accountingInterface',
                );

                assert.equal(createJournalEntryReturn.documentsCreated.length, 1);
                assert.equal(createJournalEntryReturn.documentsCreated[0].type, 'journalEntry');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Creation of Journal Entry record that fails - document does not exist (miscellaneous stock receipt)', () =>
        Test.withContext(
            async context => {
                await assert.isRejected(
                    xtremFinance.functions.createOrUpdateJournal(
                        context,
                        'journalEntry',
                        'miscellaneousStockReceipt',
                        'NON_EXISTING_DOCUMENT',
                        '9992',
                        'StockReceipt/accountingInterface',
                    ),
                    `There are no documents to process for document type ${context.localizeEnumMember(
                        '@sage/xtrem-finance-data/FinanceDocumentType',
                        'miscellaneousStockReceipt',
                    )}, target document type ${context.localizeEnumMember(
                        '@sage/xtrem-finance-data/TargetDocumentType',
                        'journalEntry',
                    )} and document number NON_EXISTING_DOCUMENT.`,
                );
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Creation of Journal Entry record that fails - wrong target document type', () =>
        Test.withContext(
            async context => {
                await assert.isRejected(
                    xtremFinance.functions.createOrUpdateJournal(
                        context,
                        'accountsReceivableInvoice',
                        'miscellaneousStockReceipt',
                        'NON_EXISTING_DOCUMENT',
                        '9992',
                        'StockReceipt/accountingInterface',
                    ),
                    `The target document type ${context.localizeEnumMember(
                        '@sage/xtrem-finance-data/TargetDocumentType',
                        'accountsReceivableInvoice',
                    )} is not supported.`,
                );
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Create journal for purchase receipt', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '99941',
                    batchSize: 1,
                    documentSysId: 1,
                    documentNumber: 'PURCHASE_RECEIPT_99941',
                    documentDate: '2021-08-23',
                    documentType: 'purchaseReceipt',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2801,
                            movementType: 'stockJournal',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Muesli'),
                            amounts: [
                                { amountType: 'amount', amount: 15.0, documentLineType: 'documentLine' },
                                { amountType: 'varianceAmount', amount: 1.0, documentLineType: 'documentLine' },
                            ],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '2678';
                (context as any)._contextValues.replyTopic = 'PurchaseReceipt/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '2678',
                    replyTopic: 'PurchaseReceipt/accountingInterface',
                    isProcessed: false,
                });

                // [RM] createJournalsFromAccountingStaging cannot be tested anymore because it needs to get a readOnly context
                // const createJournalEntry2 = xtremFinance.functions.createJournalsFromAccountingStaging(context, false, {
                //     _and: [
                //         {},
                //         { isProcessed: false },
                //         { financialSite: { _id: { _eq: 1 } } },
                //         { documentType: { _eq: 'purchaseReceipt' } },
                //     ],
                // });
                // assert.equal(createJournalEntry2.trimEnd(), '1 journal created');

                const createJournalEntryReturn = await xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'purchaseReceipt',
                    'PURCHASE_RECEIPT_99941',
                    '99941',
                    'PurchaseReceipt/accountingInterface',
                );
                assert.equal(createJournalEntryReturn.documentsCreated.length, 1);
                assert.equal(createJournalEntryReturn.documentsCreated[0].type, 'journalEntry');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Create journal from an ap invoice', () =>
        Test.withContext(
            async context => {
                const supplierLeclerc = await context.read(xtremMasterData.nodes.Supplier, { _id: '#LECLERC' });
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '99951',
                    batchSize: 2,
                    documentSysId: 3,
                    documentNumber: 'AP-**********',
                    documentDate: '2021-10-05',
                    documentType: 'apInvoice',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'EUR'),
                    financialSiteSysId: await getSiteId(context, 'ETS2-S02'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2805,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'EUR'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-10-05',
                            accountSysId: await getAccountId(context, '********|FR_DEFAULT'),
                            amounts: [
                                {
                                    amountType: 'amountExcludingTax',
                                    amount: 1000.0,
                                    baseTaxSysId: 3100,
                                    taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_COLLECTED_ON_DEBITS'),
                                    documentLineType: 'documentLine',
                                },
                                {
                                    amountType: 'deductibleTaxAmount',
                                    amount: 100.0,
                                    baseTaxSysId: 3100,
                                    taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_COLLECTED_ON_DEBITS'),
                                    documentLineType: 'documentLine',
                                },
                            ],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                        },
                        {
                            baseDocumentLineSysId: 2805,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'EUR'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-10-05',
                            accountSysId: await getAccountId(context, '********|FR_DEFAULT'),
                            supplierSysId: supplierLeclerc._id,
                            amounts: [
                                {
                                    amountType: 'amountIncludingTax',
                                    amount: 1100,
                                    documentLineType: 'documentLine',
                                },
                            ],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '2678';
                (context as any)._contextValues.replyTopic = 'AccountsPayableInvoice/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '2678',
                    replyTopic: 'AccountsPayableInvoice/accountingInterface',
                    isProcessed: false,
                });

                const createJournalEntryReturn = await xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'apInvoice',
                    'AP-**********',
                    '99951',
                    'AccountsPayableInvoice/accountingInterface',
                );
                assert.equal(createJournalEntryReturn.documentsCreated.length, 1);
                assert.equal(createJournalEntryReturn.documentsCreated[0].type, 'journalEntry');

                const journalEntry = await context.read(xtremFinance.nodes.JournalEntry, {
                    number: createJournalEntryReturn.documentsCreated[0].documentNumber,
                });
                assert.equal(await journalEntry.lines.length, 3);
                assert.equal(await (await journalEntry.lines.at(0))!.taxExternalReference, '');
                assert.equal(String(await (await journalEntry.lines.at(0))!.transactionDebit), '1000');
                assert.equal(String(await (await journalEntry.lines.at(2))!.transactionCredit), '1100');
                assert.equal(String(await (await journalEntry.lines.at(0))!.companyDebit), '1000');
                assert.equal(String(await (await journalEntry.lines.at(2))!.companyCredit), '1100');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Create journal from an ap invoice that fails - no account', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '99951',
                    batchSize: 2,
                    documentSysId: 3,
                    documentNumber: 'AP-**********',
                    documentDate: '2021-10-05',
                    documentType: 'apInvoice',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'EUR'),
                    financialSiteSysId: await getSiteId(context, 'ETS2-S02'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2805,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'EUR'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-10-05',
                            amounts: [
                                {
                                    amountType: 'amountExcludingTax',
                                    amount: 1000.0,
                                    baseTaxSysId: 3100,
                                    taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_COLLECTED_ON_DEBITS'),
                                    documentLineType: 'documentLine',
                                },
                                {
                                    amountType: 'deductibleTaxAmount',
                                    amount: 100.0,
                                    baseTaxSysId: 3100,
                                    taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_COLLECTED_ON_DEBITS'),
                                    documentLineType: 'documentLine',
                                },
                            ],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                        },
                        {
                            baseDocumentLineSysId: 2805,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'EUR'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-10-05',
                            supplierSysId: await getSupplierId(context, 'LECLERC'),
                            amounts: [
                                {
                                    amountType: 'amountIncludingTax',
                                    amount: 1100,
                                    documentLineType: 'documentLine',
                                },
                            ],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '2678';
                (context as any)._contextValues.replyTopic = 'AccountsPayableInvoice/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '2678',
                    replyTopic: 'AccountsPayableInvoice/accountingInterface',
                    isProcessed: false,
                });

                await assert.isRejected(
                    xtremFinance.functions.createOrUpdateJournal(
                        context,
                        'journalEntry',
                        'apInvoice',
                        'AP-**********',
                        '99951',
                        'AccountsPayableInvoice/accountingInterface',
                    ),
                    'The account cannot be determined.',
                );
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Create journal from an ar invoice', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '99952',
                    batchSize: 4,
                    documentSysId: 2,
                    documentNumber: 'AR-**********',
                    documentDate: '2021-10-05',
                    documentType: 'arInvoice',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'EUR'),
                    financialSiteSysId: await getSiteId(context, 'ETS2-S02'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2905,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'EUR'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-10-05',
                            taxDate: '2021-10-05',
                            accountSysId: await getAccountId(context, '********|FR_DEFAULT'),
                            amounts: [
                                {
                                    amountType: 'amountExcludingTax',
                                    amount: 1000.0,
                                    baseTaxSysId: 2900,
                                    taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_DEDUCTIBLE_ON_FA'),
                                    documentLineType: 'documentLine',
                                },
                                {
                                    amountType: 'deductibleTaxAmount',
                                    amount: 100.0,
                                    baseTaxSysId: 2900,
                                    taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_DEDUCTIBLE_ON_FA'),
                                    documentLineType: 'documentLine',
                                },
                            ],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                        },
                        {
                            baseDocumentLineSysId: 2906,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'EUR'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-10-05',
                            taxDate: '2021-10-05',
                            accountSysId: await getAccountId(context, '********|FR_DEFAULT'),
                            amounts: [
                                {
                                    amountType: 'amountExcludingTax',
                                    amount: 2000.0,
                                    baseTaxSysId: 2900,
                                    taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_DEDUCTIBLE_ON_FA'),
                                    documentLineType: 'documentLine',
                                },
                                {
                                    amountType: 'deductibleTaxAmount',
                                    amount: 200.0,
                                    baseTaxSysId: 2900,
                                    taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_DEDUCTIBLE_ON_FA'),
                                    documentLineType: 'documentLine',
                                },
                            ],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                        },
                        {
                            baseDocumentLineSysId: 2905,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'EUR'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-10-05',
                            taxDate: '2021-10-05',
                            accountSysId: await getAccountId(context, '********|FR_DEFAULT'),
                            customerSysId: await getCustomerId(context, 'US019'),
                            amounts: [
                                {
                                    amountType: 'amountIncludingTax',
                                    amount: 1100,
                                    documentLineType: 'documentLine',
                                },
                            ],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                        },
                        {
                            baseDocumentLineSysId: 2906,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'EUR'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-10-05',
                            taxDate: '2021-10-05',
                            accountSysId: await getAccountId(context, '********|FR_DEFAULT'),
                            customerSysId: await getCustomerId(context, 'US019'),
                            amounts: [
                                {
                                    amountType: 'amountIncludingTax',
                                    amount: 2200,
                                    documentLineType: 'documentLine',
                                },
                            ],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '2678';
                (context as any)._contextValues.replyTopic = 'AccountsReceivableInvoice/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '2678',
                    replyTopic: 'AccountsReceivableInvoice/accountingInterface',
                    isProcessed: false,
                });

                const createJournalEntryReturn = await xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'arInvoice',
                    'AR-**********',
                    '99952',
                    'AccountsReceivableInvoice/accountingInterface',
                );
                assert.equal(createJournalEntryReturn.documentsCreated.length, 1);
                assert.equal(createJournalEntryReturn.documentsCreated[0].type, 'journalEntry');

                const journalEntry = await context.read(xtremFinance.nodes.JournalEntry, {
                    number: createJournalEntryReturn.documentsCreated[0].documentNumber,
                });
                assert.equal(await journalEntry.lines.length, 6);
                assert.equal(
                    await (await journalEntry.lines.at(0))!.taxExternalReference,
                    'Déductible immobilisations taux normal',
                );
                assert.equal(await (await journalEntry.lines.at(3))!.taxExternalReference, '');
                assert.equal(String(await (await journalEntry.lines.at(0))!.transactionCredit), '1000');
                assert.equal(String(await (await journalEntry.lines.at(5))!.transactionDebit), '2200');
                assert.equal(String(await (await journalEntry.lines.at(0))!.companyCredit), '1000');
                assert.equal(String(await (await journalEntry.lines.at(5))!.companyDebit), '2200');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Create journal from an ar invoice - fails - no amounts', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '99952',
                    batchSize: 4,
                    documentSysId: 3,
                    documentNumber: 'AR-**********',
                    documentDate: '2021-10-05',
                    documentType: 'arInvoice',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'EUR'),
                    financialSiteSysId: await getSiteId(context, 'ETS2-S02'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2905,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'EUR'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-10-05',
                            taxDate: '2021-10-05',
                            accountSysId: await getAccountId(context, '********|FR_DEFAULT'),
                            amounts: [
                                {
                                    amountType: 'amountExcludingTax',
                                    amount: 1000.0,
                                    baseTaxSysId: 2900,
                                    taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_DEDUCTIBLE_ON_FA'),
                                    documentLineType: 'documentLine',
                                },
                                {
                                    amountType: 'deductibleTaxAmount',
                                    amount: 100.0,
                                    baseTaxSysId: 2900,
                                    taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_DEDUCTIBLE_ON_FA'),
                                    documentLineType: 'documentLine',
                                },
                            ],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                        },
                        {
                            baseDocumentLineSysId: 2906,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'EUR'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-10-05',
                            taxDate: '2021-10-05',
                            accountSysId: await getAccountId(context, '********|FR_DEFAULT'),
                            amounts: [
                                {
                                    amountType: 'amountExcludingTax',
                                    amount: 2000.0,
                                    baseTaxSysId: 2900,
                                    taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_DEDUCTIBLE_ON_FA'),
                                    documentLineType: 'documentLine',
                                },
                                {
                                    amountType: 'deductibleTaxAmount',
                                    amount: 200.0,
                                    baseTaxSysId: 2900,
                                    taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_DEDUCTIBLE_ON_FA'),
                                    documentLineType: 'documentLine',
                                },
                            ],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                        },
                        {
                            baseDocumentLineSysId: 2905,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'EUR'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-10-05',
                            taxDate: '2021-10-05',
                            accountSysId: await getAccountId(context, '********|FR_DEFAULT'),
                            customerSysId: await getCustomerId(context, 'US019'),
                            amounts: [
                                {
                                    amountType: 'amountIncludingTax',
                                    amount: 1100,
                                    documentLineType: 'documentLine',
                                },
                            ],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                        },
                        {
                            baseDocumentLineSysId: 2906,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'EUR'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-10-05',
                            taxDate: '2021-10-05',
                            accountSysId: await getAccountId(context, '********|FR_DEFAULT'),
                            customerSysId: await getCustomerId(context, 'US019'),
                            amounts: [],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '2678';
                (context as any)._contextValues.replyTopic = 'AccountsReceivableInvoice/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '2678',
                    replyTopic: 'AccountsReceivableInvoice/accountingInterface',
                    isProcessed: false,
                });

                const lastAccountingStagingRecordCreated = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { last: 1 })
                    .at(0);

                await assert.isRejected(
                    xtremFinance.functions.createOrUpdateJournal(
                        context,
                        'journalEntry',
                        'arInvoice',
                        'AR-**********',
                        '99952',
                        'AccountsReceivableInvoice/accountingInterface',
                    ),
                    `The accounting staging amount for the document number AR-********** ${lastAccountingStagingRecordCreated?._id} could not be found.`,
                );
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Creation of Journal Entry record with currency variance (miscellaneous stock receipt)', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '19990',
                    batchSize: 2,
                    documentSysId: 1,
                    documentNumber: 'MISC_RECEIPT19990',
                    documentDate: '2021-08-23',
                    documentType: 'miscellaneousStockReceipt',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: await getSiteId(context, 'ETS2-S01'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2802,
                            movementType: 'stockJournal',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1.97,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Muesli'),
                            storedDimensions: {
                                dimensionType01: '300',
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 11111.29, documentLineType: 'documentLine' }],
                        },
                        {
                            baseDocumentLineSysId: 2802,
                            movementType: 'stockJournal',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1.97,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Milk'),
                            storedDimensions: {
                                dimensionType01: '300',
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: 'AttPROJ',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: 77777.39, documentLineType: 'documentLine' }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '19990';
                (context as any)._contextValues.replyTopic = 'StockReceipt/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '19990',
                    replyTopic: 'StockReceipt/accountingInterface',
                    isProcessed: false,
                });

                const createJournalEntry = await xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'miscellaneousStockReceipt',
                    'MISC_RECEIPT19990',
                    '19990',
                    'StockReceipt/accountingInterface',
                );

                const journalEntry = await context.read(xtremFinance.nodes.JournalEntry, {
                    number: createJournalEntry.documentsCreated[0].documentNumber,
                });

                assert.instanceOf(journalEntry, xtremFinance.nodes.JournalEntry);
                assert.equal((await journalEntry.postingDate).toString(), '2021-08-23');
                assert.equal(await (await journalEntry.financialSite).id, 'ETS2-S01');

                const journalEntryLines: xtremFinance.nodes.JournalEntryLine[] = await journalEntry.lines.toArray();
                assert.equal(await (await journalEntryLines[0].account).name, 'Stocks de matières premières');
                assert.equal(
                    await (
                        await journalEntryLines[1].account
                    ).name,
                    'Variations des stocks de matières premières',
                );
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Creation of journal entry with negative amounts (miscellaneous stock issue)', () =>
        Test.withContext(
            async context => {
                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '99931',
                    batchSize: 1,
                    documentSysId: 1,
                    documentNumber: 'MISC_ISSUE_99996',
                    documentDate: '2021-08-23',
                    documentType: 'miscellaneousStockIssue',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'USD'),
                    financialSiteSysId: await getSiteId(context, 'US001'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: 2801,
                            movementType: 'stockJournal',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 1,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-08-23',
                            itemSysId: await getItemId(context, 'Muesli'),
                            storedDimensions: {
                                dimensionType01: '300',
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: 'US001',
                                financialSite: 'US001',
                                item: 'Muesli',
                            },
                            amounts: [{ amountType: 'amount', amount: -100.0, documentLineType: 'documentLine' }],
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '2345';
                (context as any)._contextValues.replyTopic = 'StockIssue/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '2345',
                    replyTopic: 'StockIssue/accountingInterface',
                    isProcessed: false,
                });
                await context.flushDeferredActions();
                const createJournalEntryReturn = await xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'miscellaneousStockIssue',
                    'MISC_ISSUE_99996',
                    '99931',
                    'StockIssue/accountingInterface',
                );
                assert.equal(createJournalEntryReturn.documentsCreated.length, 1);
                const journalCreated = await context.read(xtremFinance.nodes.JournalEntry, {
                    _id: createJournalEntryReturn.documentsCreated[0].documentSysId,
                });
                assert.equal(await (await (await journalCreated.lines.at(0))?.account)?.id, '13100');
                assert.equal(await (await journalCreated.lines.at(0))?.sign, 'D');
                assert.equal(await (await journalCreated.lines.at(0))?.transactionAmount, 100);
                assert.equal(await (await (await journalCreated.lines.at(1))?.account)?.id, '50200');
                assert.equal(await (await journalCreated.lines.at(1))?.sign, 'C');
                assert.equal(await (await journalCreated.lines.at(1))?.transactionAmount, 100);
            },
            {
                today: '2021-08-23',
            },
        ));

    it('Create journal from an ar invoice with currency rate rounding differences', () =>
        Test.withContext(
            async context => {
                const arInvoice = await context.read(xtremFinance.nodes.AccountsReceivableInvoice, {
                    number: 'AR-**********',
                    type: 'salesInvoice',
                });

                const financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument = {
                    batchId: '99952',
                    batchSize: 4,
                    documentSysId: arInvoice._id,
                    documentNumber: 'AR-**********',
                    documentDate: '2021-10-05',
                    documentType: 'arInvoice',
                    targetDocumentType: 'journalEntry',
                    currencySysId: await getCurrencyId(context, 'EUR'),
                    financialSiteSysId: await getSiteId(context, 'ETS2-S02'),
                    documentLines: [
                        {
                            baseDocumentLineSysId: (await arInvoice.lines.at(0))?._id ?? 0,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 0.***************,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-10-05',
                            taxDate: '2021-10-05',
                            accountSysId: await getAccountId(context, '********|FR_DEFAULT'),
                            customerSysId: await getCustomerId(context, 'US019'),
                            amounts: [
                                {
                                    amountType: 'amountIncludingTax',
                                    amount: 120,
                                    documentLineType: 'documentLine',
                                },
                            ],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                        },
                        {
                            baseDocumentLineSysId: (await arInvoice.lines.at(0))?._id ?? 0,
                            movementType: 'document',
                            sourceDocumentNumber: '',
                            currencySysId: await getCurrencyId(context, 'USD'),
                            companyFxRate: 0.***************,
                            companyFxRateDivisor: 1,
                            fxRateDate: '2021-10-05',
                            taxDate: '2021-10-05',
                            accountSysId: await getAccountId(context, '********|FR_DEFAULT'),
                            amounts: [
                                {
                                    amountType: 'amountExcludingTax',
                                    amount: 100.0,
                                    baseTaxSysId: (await (await arInvoice.lines.at(0))?.taxes.at(0))?._id ?? 0,
                                    taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_DEDUCTIBLE_ON_FA'),
                                    documentLineType: 'documentLine',
                                },
                                {
                                    amountType: 'deductibleTaxAmount',
                                    amount: 20.0,
                                    baseTaxSysId: (await (await arInvoice.lines.at(0))?.taxes.at(0))?._id ?? 0,
                                    taxSysId: await getTaxValueId(context, 'FR_TVA_NORMAL_DEDUCTIBLE_ON_FA'),
                                    documentLineType: 'documentLine',
                                },
                            ],
                            storedDimensions: {
                                dimensionType03: 'DIMTYPE1VALUE1',
                                dimensionType04: 'DIMTYPE2VALUE2',
                            },
                            storedAttributes: {
                                project: '',
                                task: '',
                                employee: '',
                                stockSite: '',
                                financialSite: '',
                                item: '',
                            },
                        },
                    ],
                };

                // create a finance transaction record (hack since we don't use the notification)
                await createFinanceTransaction(context, financeDocument);

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '2678';
                (context as any)._contextValues.replyTopic = 'AccountsReceivableInvoice/accountingInterface';
                await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
                    financeDocument,
                    notificationId: '2678',
                    replyTopic: 'AccountsReceivableInvoice/accountingInterface',
                    isProcessed: false,
                });

                const createJournalEntryReturn = await xtremFinance.functions.createOrUpdateJournal(
                    context,
                    'journalEntry',
                    'arInvoice',
                    'AR-**********',
                    '99952',
                    'AccountsReceivableInvoice/accountingInterface',
                );
                assert.equal(createJournalEntryReturn.documentsCreated.length, 1);
                assert.equal(createJournalEntryReturn.documentsCreated[0].type, 'journalEntry');

                const journalEntry = await context.read(xtremFinance.nodes.JournalEntry, {
                    number: createJournalEntryReturn.documentsCreated[0].documentNumber,
                });

                assert.equal(await journalEntry.lines.length, 4);

                const baseLine = await journalEntry.lines.at(0);
                const balanceLine = await journalEntry.lines.at(3);

                assert.deepEqual(await baseLine?.isBalanceLine, false);
                assert.deepEqual(await balanceLine?.isBalanceLine, true);
                assert.deepEqual((await balanceLine?.contraJournalEntryLine)?._id, baseLine?._id);
                assert.deepEqual(await (await baseLine?.account)?.taxManagement, 'includingTax');
                assert.deepEqual(await balanceLine?.companyCredit, 0.01);
            },
            {
                today: '2021-08-23',
            },
        ));
});
