import type { Context, NodeCreateData } from '@sage/xtrem-core';
import { datetime, DateValue, Test } from '@sage/xtrem-core';
import { DateRange } from '@sage/xtrem-date-time';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremStructure from '@sage/xtrem-structure';
import { assert } from 'chai';
import * as xtremFinance from '../../../index';

export async function createDatevExport(parameters: {
    context: Context;
    doAccountsAndBusinessRelations: boolean;
}): Promise<xtremFinance.nodes.DatevExport> {
    const datevExport = await parameters.context.create(xtremFinance.nodes.DatevExport, {
        id: 'export01',
        company: '#UT02',
        datevConsultantNumber: 1,
        datevCustomerNumber: 1,
        dateRange: new DateRange(DateValue.make(2024, 9, 1), DateValue.make(2024, 10, 1)),
        fiscalYearStart: DateValue.make(2024, 1, 1),
        doAccounts: true,
        doCustomersSuppliers: true,
        doJournalEntries: true,
        datevExportAccounts: parameters.doAccountsAndBusinessRelations
            ? [
                  { account: '#3800|DE_DEFAULT', datevId: 1001, name: 'Bezugsnebenkosten' },
                  { account: '#3830|DE_DEFAULT', datevId: 1002, name: 'Leergut' },
              ]
            : [],
        datevExportBusinessRelations: parameters.doAccountsAndBusinessRelations
            ? [
                  {
                      businessRelation: '#Customer|US019',
                      datevId: 10001,
                      name: 'customer 01',
                      taxIdNumber: '*********',
                      country: '#DE',
                      postcode: '12345',
                      city: 'Munich',
                      street: 'Main street',
                  },
                  {
                      businessRelation: '#Supplier|US017',
                      datevId: 70001,
                      name: 'supplier 01',
                      taxIdNumber: '*********',
                      country: '#FR',
                      postcode: '56745',
                      city: 'Bordeaux',
                      street: 'Rue centrale',
                  },
              ]
            : [],
        datevExportJournalEntryLines: parameters.doAccountsAndBusinessRelations
            ? [
                  {
                      journalEntryLine: '#IJ240002|DE|IJ|10',
                      datevSign: 'S',
                      transactionCurrency: '#EUR',
                      companyFxRate: 1,
                      companyCurrency: '#EUR',
                      datevAccountId: 3970,
                      datevContraAccountId: 3960,
                      postingDate: DateValue.make(2024, 9, 10),
                      number: 'IJ240002',
                      description: 'Stock',
                      siteTaxIdNumber: 'DE*********0',
                      transactionValue: 1200,
                      companyValue: 1200,
                  },
                  {
                      journalEntryLine: '#PI240004|DE|EREC|10',
                      datevSign: 'H',
                      transactionCurrency: '#EUR',
                      companyFxRate: 1,
                      companyCurrency: '#EUR',
                      datevAccountId: 3400,
                      datevContraAccountId: 70001,
                      postingDate: DateValue.make(2024, 9, 10),
                      number: 'PI240004',
                      description: 'AP invoice',
                      siteTaxIdNumber: 'DE*********0',
                      transactionValue: 1200,
                      companyValue: 1200,
                  },
              ]
            : [],
    });
    await datevExport.$.save();
    const datevExport2 = await parameters.context.read(xtremFinance.nodes.DatevExport, {
        _id: '#export01',
    });
    return datevExport2;
}

describe('Test DATEV helper functions', () => {
    it('Check getDatevHeader', () =>
        Test.withContext(
            async context => {
                const datevExport = await createDatevExport({
                    context,
                    doAccountsAndBusinessRelations: true,
                });
                const timeStamp = datetime.now();
                const datevConfiguration = await xtremFinanceData.functions.datev.getDatevConfiguration(context);
                const datevHeader = await xtremFinance.functions.getDatevHeader({
                    formatCategory: 20,
                    formatName: 'Kontenbeschriftungen',
                    formatVersion: 3,
                    timeStamp,
                    datevExport,
                    datevConfiguration,
                });
                assert.deepEqual(
                    datevHeader,
                    '"EXTF";700;20;"Kontenbeschriftungen";3;*****************;;;;;1;1;********;4;;;;;;;;;;;;;"03";;;;',
                );
            },
            {
                now: '2024-01-01T17:23:07',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption],
            },
        ));

    it('Check getDatevHeader for journal entry lines', () =>
        Test.withContext(
            async context => {
                const datevExport = await createDatevExport({
                    context,
                    doAccountsAndBusinessRelations: true,
                });
                const timeStamp = datetime.now();
                const datevConfiguration = await xtremFinanceData.functions.datev.getDatevConfiguration(context);
                const datevHeader = await xtremFinance.functions.getDatevHeader({
                    formatCategory: 21,
                    formatName: 'Buchungsstapel',
                    formatVersion: 13,
                    timeStamp,
                    datevExport,
                    datevConfiguration,
                });
                assert.deepEqual(
                    datevHeader,
                    '"EXTF";700;21;"Buchungsstapel";13;*****************;;;;;1;1;********;4;********;********;Rechnungsausgang 09/2024;;;0;1;EUR;;;;;"03";;;;',
                );
            },
            {
                now: '2024-01-01T17:23:07',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption],
            },
        ));

    it('Check getDatevAccountLines', () =>
        Test.withContext(
            async context => {
                const datevExport = await createDatevExport({
                    context,
                    doAccountsAndBusinessRelations: true,
                });
                const datevAccounts = await xtremFinance.functions.getDatevAccountLines(context, await datevExport.id);
                assert.deepEqual(datevAccounts.datevAccountLines.length, 2);
                assert.deepEqual(
                    datevAccounts.datevAccountLines[0],
                    '1001;"Bezugsnebenkosten";"de-DE";"Bezugsnebenkosten"',
                );
                assert.deepEqual(datevAccounts.datevAccountLines[1], '1002;"Leergut";"de-DE";"Leergut"');
            },
            {
                today: '2024-01-01',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption],
            },
        ));

    it('Check getDatevBusinessRelationLines', () =>
        Test.withContext(
            async context => {
                const datevExport = await createDatevExport({
                    context,
                    doAccountsAndBusinessRelations: true,
                });
                const datevBusinessRelations = await xtremFinance.functions.getDatevBusinessRelationLines(
                    context,
                    await datevExport.id,
                );
                assert.deepEqual(datevBusinessRelations.datevBusinessRelationLines.length, 2);
                assert.deepEqual(
                    datevBusinessRelations.datevBusinessRelationLines[0],
                    '10001;"customer 01";"";"";"";"";"";"";"12";"3456789";"";"";"";"";"";"Main street";"";"12345";"Munich";"DE";"";"";"";"";"";;;;"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";;"";"";"";;;"";;;;;;;;;;;;;;;;;;;;;;;;;;"";"";;"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";;"";"";"";"";"";"";"";"";"";"";;;;;;;;;;;;;"";;;"";;"";;;;;',
                );
                assert.deepEqual(
                    datevBusinessRelations.datevBusinessRelationLines[1],
                    '70001;"supplier 01";"";"";"";"";"";"";"01";"2345678";"";"";"";"";"";"Rue centrale";"";"56745";"Bordeaux";"FR";"";"";"";"";"";;;;"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";;"";"";"";;;"";;;;;;;;;;;;;;;;;;;;;;;;;;"";"";;"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";"";"";"";"";"";"";"";"";;;"";;"";"";"";"";"";"";"";"";"";"";;;;;;;;;;;;;"";;;"";;"";;;;;',
                );
            },
            {
                today: '2024-01-01',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption],
            },
        ));

    it('Check getDatevJournalEntryLines', () =>
        Test.withContext(
            async context => {
                const datevExport = await createDatevExport({
                    context,
                    doAccountsAndBusinessRelations: true,
                });
                const datevJournalEntryLines = await xtremFinance.functions.getDatevJournalEntryLines(
                    context,
                    await datevExport.id,
                );
                assert.deepEqual(datevJournalEntryLines.datevJournalEntryLines.length, 2);
                assert.deepEqual(
                    datevJournalEntryLines.datevJournalEntryLines[0],
                    '1200;"S";"EUR";1,000000;1200;"EUR";3970;3960;"";1009;"IJ240002";"";;"Stock";0;"";;;;"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";;"";;"";;;;;;"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";;;;"";;;;"";"";;"";;;;"";"";;"";;"";;"";"";;"";;0;;;;"";;"";"";;"";;',
                );
                assert.deepEqual(
                    datevJournalEntryLines.datevJournalEntryLines[1],
                    '1200;"H";"EUR";1,000000;1200;"EUR";3400;70001;"";1009;"PI240004";"";;"AP invoice";0;"";;;;"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";;"";;"";;;;;;"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";"";;;;"";;;;"";"";;"";;;;"";"";;"";;"";;"";"";;"";;0;;;;"";;"";"";;"";;',
                );
            },
            {
                today: '2024-01-01',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption],
            },
        ));

    it('Check executeAccountExtraction', () =>
        Test.withContext(
            async context => {
                const datevExport = await createDatevExport({
                    context,
                    doAccountsAndBusinessRelations: false,
                });
                const datevAccounts = await xtremFinance.functions.executeAccountExtraction(context, datevExport);
                assert.deepEqual(datevAccounts.length, 38);
                assert.deepEqual(datevAccounts[0].datevId, 1400);
                assert.deepEqual(datevAccounts[0].name, 'Forderungen');
                assert.deepEqual(datevAccounts[1].datevId, 1571);
                assert.deepEqual(datevAccounts[1].name, 'Vorsteuer 7 %');
            },
            {
                today: '2024-01-01',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption],
            },
        ));

    it('Check executeBusinessRelationExtraction', () =>
        Test.withContext(
            async context => {
                const datevExport = await createDatevExport({
                    context,
                    doAccountsAndBusinessRelations: false,
                });
                const datevBusinessRelations = await xtremFinance.functions.executeBusinessRelationExtraction(
                    context,
                    datevExport,
                );
                assert.deepEqual(datevBusinessRelations.length, 36);

                assert.deepEqual(datevBusinessRelations[14].datevId, 70001);
                assert.deepEqual(datevBusinessRelations[14].name, 'Siège social S01 PARIS');
                assert.deepEqual(datevBusinessRelations[14].taxIdNumber, 'FR58483849894');
                assert.deepEqual(datevBusinessRelations[14].street, '960 street 44');
                assert.deepEqual(datevBusinessRelations[14].city, '');
                assert.deepEqual(datevBusinessRelations[14].postcode, '20746');
                assert.deepEqual(
                    await Promise.resolve(
                        (datevBusinessRelations[14].country as NodeCreateData<xtremStructure.nodes.Country>)?.id,
                    ),
                    'FR',
                );
                assert.deepEqual(datevBusinessRelations[15].datevId, 10001);
                assert.deepEqual(datevBusinessRelations[15].name, 'Dépot de TOULOUSE - Sud Ouest');
                assert.deepEqual(datevBusinessRelations[15].taxIdNumber, undefined);
                assert.deepEqual(datevBusinessRelations[15].street, '1st Avenue');
                assert.deepEqual(datevBusinessRelations[15].city, 'One');
                assert.deepEqual(datevBusinessRelations[15].postcode, '85034');
                assert.deepEqual(
                    await Promise.resolve(
                        (datevBusinessRelations[15].country as NodeCreateData<xtremStructure.nodes.Country>)?.id,
                    ),
                    'US',
                );
            },
            {
                today: '2024-01-01',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption],
            },
        ));

    it('Check executeJournalEntryLineExtraction', () =>
        Test.withContext(
            async context => {
                const datevExport = await createDatevExport({
                    context,
                    doAccountsAndBusinessRelations: false,
                });
                const datevJournalEntryLines = await xtremFinance.functions.executeJournalEntryLineExtraction(
                    context,
                    datevExport,
                );
                assert.deepEqual(datevJournalEntryLines.length, 5);
                assert.deepEqual(datevJournalEntryLines[0].datevAccountId, 3970);
                assert.deepEqual(datevJournalEntryLines[0].datevContraAccountId, 3960);
                assert.deepEqual(datevJournalEntryLines[0].description, 'Stock');
                assert.deepEqual(datevJournalEntryLines[0].number, 'IJ240001');
                assert.deepEqual(datevJournalEntryLines[0].siteTaxIdNumber, 'DE*********');
                assert.deepEqual(datevJournalEntryLines[0].datevSign, 'S');
                assert.deepEqual(datevJournalEntryLines[0].transactionValue?.toString(), '400');
                assert.deepEqual(datevJournalEntryLines[3].datevAccountId, 3800);
                assert.deepEqual(datevJournalEntryLines[3].datevContraAccountId, 70999);
                assert.deepEqual(datevJournalEntryLines[3].description, 'AP invoice');
                assert.deepEqual(datevJournalEntryLines[3].number, 'PI240004');
                assert.deepEqual(datevJournalEntryLines[3].siteTaxIdNumber, 'DE*********');
                assert.deepEqual(datevJournalEntryLines[3].datevSign, 'S');
                assert.deepEqual(datevJournalEntryLines[3].transactionValue?.toString(), '2000');
                assert.deepEqual(datevJournalEntryLines[4].datevAccountId, 8400);
                assert.deepEqual(datevJournalEntryLines[4].datevContraAccountId, 10999);
                assert.deepEqual(datevJournalEntryLines[4].description, 'AR invoice');
                assert.deepEqual(datevJournalEntryLines[4].number, 'SIG1240001');
                assert.deepEqual(datevJournalEntryLines[4].siteTaxIdNumber, 'DE*********');
                assert.deepEqual(datevJournalEntryLines[4].datevSign, 'H');
                assert.deepEqual(datevJournalEntryLines[4].transactionValue?.toString(), '10000');
            },
            {
                today: '2024-01-01',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.datevOption],
            },
        ));
});
