import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as xtremFinance from '../../../lib';

describe('Finance retry', () => {
    before(() => {});

    it('getFinanceDocument - Journal entry', () =>
        Test.withContext(
            async context => {
                const financeTransaction = await context.read(xtremFinanceData.nodes.FinanceTransaction, { _id: 1 });

                const journalEntry = await xtremFinance.functions.financeRetry.getFinanceDocument(
                    context,
                    financeTransaction,
                );

                assert.equal(await journalEntry?.number, 'IJ-001');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('getFinanceDocument - Journal entry from accounting staging', () =>
        Test.withContext(
            async context => {
                const financeTransaction = await context.read(xtremFinanceData.nodes.FinanceTransaction, { _id: 15 });

                const journalEntry = await xtremFinance.functions.financeRetry.getFinanceDocument(
                    context,
                    financeTransaction,
                );

                assert.equal(await journalEntry?.number, 'IJ-010');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('getFinanceDocument - AR Invoice', () =>
        Test.withContext(
            async context => {
                const financeTransaction = await context.read(xtremFinanceData.nodes.FinanceTransaction, { _id: 11 });

                const arInvoice = await xtremFinance.functions.financeRetry.getFinanceDocument(
                    context,
                    financeTransaction,
                );

                assert.equal(await arInvoice?.number, 'AR-2021090401');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('getFinanceDocument - AP Invoice', () =>
        Test.withContext(
            async context => {
                const financeTransaction = await context.read(xtremFinanceData.nodes.FinanceTransaction, { _id: 10 });

                const apInvoice = await xtremFinance.functions.financeRetry.getFinanceDocument(
                    context,
                    financeTransaction,
                );

                assert.equal(await apInvoice?.number, 'AP-2021090405');
            },
            {
                today: '2021-08-23',
            },
        ));

    it('getFinanceDocument - not found', () =>
        Test.withContext(
            async context => {
                const financeTransaction = await context.read(xtremFinanceData.nodes.FinanceTransaction, { _id: 8 });

                const financeDocument = await xtremFinance.functions.financeRetry.getFinanceDocument(
                    context,
                    financeTransaction,
                );
                assert.equal(financeDocument, null);
            },
            {
                today: '2021-08-23',
            },
        ));

    it('getFinanceDocument - invalid target document type', () =>
        Test.withContext(
            async context => {
                const financeTransaction = await context.read(xtremFinanceData.nodes.FinanceTransaction, { _id: 14 });

                const financeDocument = await xtremFinance.functions.financeRetry.getFinanceDocument(
                    context,
                    financeTransaction,
                );
                assert.equal(financeDocument, null);
            },
            {
                today: '2021-08-23',
            },
        ));
});
