@xtrem_finance
Feature: smoke-test-data

    #Case without navigation panel full width
    Scenario Outline: std \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed
        Examples:
            | Page                                                           | Title                                     |
            | @sage/xtrem-finance/JournalEntry/eyJfaWQiOiIyIn0=              | Journal entry IJ-**********               |
            | @sage/xtrem-finance/AccountsReceivableInvoice/eyJfaWQiOiIyIn0= | Accounts receivable invoice AR-********** |
            | @sage/xtrem-finance/AccountsPayableInvoice/eyJfaWQiOiIxIn0=    | Accounts payable invoice AP-**********    |
