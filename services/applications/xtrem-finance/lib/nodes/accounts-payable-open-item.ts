import type { Context, Reference, decimal } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { forceOpenItemPayment, notifyUserBulkOpenItemPayment } from '../functions/payment-tracking';
import { signedAmount } from '../functions/signed-amount';
import * as xtremFinance from '../index';

@decorators.subNode<AccountsPayableOpenItem>({
    extends: () => xtremFinanceData.nodes.BaseOpenItem,
    isPublished: true,
    canSearch: true,
    canRead: true,

    async saveBegin() {
        await xtremFinance.events.saveBegin.OpenItem.saveBegin(this);
    },
})
export class AccountsPayableOpenItem extends xtremFinanceData.nodes.BaseOpenItem {
    @decorators.referencePropertyOverride<AccountsPayableOpenItem, 'businessRelation'>({
        node: () => xtremMasterData.nodes.Supplier,
        async getValue() {
            return (await this.businessEntity).supplier;
        },
    })
    override readonly businessRelation: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.referenceProperty<AccountsPayableOpenItem, 'accountsPayableInvoice'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        node: () => xtremFinance.nodes.AccountsPayableInvoice,
    })
    readonly accountsPayableInvoice: Reference<xtremFinance.nodes.AccountsPayableInvoice>;

    @decorators.referenceProperty<AccountsPayableOpenItem, 'financialSite'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.Site,
        async getValue() {
            return (await this.accountsPayableInvoice).financialSite;
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.decimalProperty<AccountsPayableOpenItem, 'totalAmount'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            return signedAmount({
                isReceipt: false,
                isSupplier: true,
                origin: await (await this.accountsPayableInvoice).origin,
                amount: await this.transactionAmountDue,
            });
        },
    })
    readonly totalAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableOpenItem, 'amountDue'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            return signedAmount({
                isReceipt: false,
                isSupplier: true,
                origin: await (await this.accountsPayableInvoice).origin,
                amount: (await this.transactionAmountDue) - (await this.transactionAmountPaid),
            });
        },
    })
    readonly amountDue: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableOpenItem, 'totalCompanyAmount'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            return signedAmount({
                isReceipt: false,
                isSupplier: true,
                origin: await (await this.accountsPayableInvoice).origin,
                amount: await this.companyAmountDue,
            });
        },
    })
    readonly totalCompanyAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableOpenItem, 'totalCompanyAmountPaid'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            return signedAmount({
                isReceipt: false,
                isSupplier: true,
                origin: await (await this.accountsPayableInvoice).origin,
                amount: await this.companyAmountPaid,
            });
        },
    })
    readonly totalCompanyAmountPaid: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableOpenItem, 'remainingCompanyAmount'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            return signedAmount({
                isReceipt: false,
                isSupplier: true,
                origin: await (await this.accountsPayableInvoice).origin,
                amount: (await this.companyAmountDue) - (await this.companyAmountPaid),
            });
        },
    })
    readonly remainingCompanyAmount: Promise<decimal>;

    @decorators.referenceProperty<AccountsPayableOpenItem, 'paymentTracking'>({
        isPublished: true,
        node: () => xtremFinanceData.nodes.PaymentTracking,
        lookupAccess: true,
        isNullable: true,
        async getValue() {
            return (await this.accountsPayableInvoice).paymentTracking;
        },
    })
    readonly paymentTracking: Reference<xtremFinanceData.nodes.PaymentTracking | null>;

    // Bulk mutation to update the selected open items to 'paid' and also update the display status on the
    // related purchase invoice or credit memo to paid.
    // Finally notify the user that the open items were forced to be paid.
    // This bulk mutation is called from the bulkAction on the main list of the Open item edit page
    @decorators.bulkMutation<typeof AccountsPayableOpenItem, 'bulkOpenItemUpdate'>({
        isPublished: true,
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
        async onComplete(context, documents) {
            await notifyUserBulkOpenItemPayment(context, documents, '@sage/xtrem-finance/AccountsPayableOpenItem');
        },
    })
    static async bulkOpenItemUpdate(context: Context, document: AccountsPayableOpenItem) {
        if ((await document.status) !== 'paid') {
            await forceOpenItemPayment(context, document);
            return document.documentNumber;
        }
        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-finance/nodes__accounts_payable_open_item__skipped',
                'Document skipped as it is already fully paid: {{number}}.',
                { number: await document.documentNumber },
            ),
        );
        return '';
    }
}
