import type { Collection, Context, decimal, integer, NodeCreateData, Reference } from '@sage/xtrem-core';
import { BusinessRuleError, date, decorators, Logger, Node, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinance from '../index';

const logger = Logger.getLogger(__filename, 'accounts-payable-invoice');

@decorators.node<AccountsPayableInvoice>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    indexes: [{ orderBy: { number: 1, type: 1 }, isUnique: true, isNaturalKey: true }],
    notifies: ['created', 'updated', 'deleted'],
    async controlEnd(cx) {
        await xtremFinance.functions.controlMandatoryAttributesAndDimensions(this.$.context, cx, this);
    },
    async saveBegin() {
        if (
            this.$.status === NodeStatus.modified &&
            !this.pForceUpdateForFinance &&
            ['posted', 'inProgress', 'error'].includes(await (await this.$.old).postingStatus) &&
            !this.pCanUpdateFromExternalIntegration &&
            (await this.internalFinanceIntegrationStatus) !== 'failed'
        ) {
            throw new BusinessRuleError(
                this.$.context.localize(
                    '@sage/xtrem-finance/nodes__accounts_payable_invoice__update_not_allowed_status_posted',
                    'The accounts payable invoice is posted. You cannot update it.',
                ),
            );
        }
    },
})
export class AccountsPayableInvoice extends Node implements xtremFinanceData.interfaces.ApInvoice {
    // To allow update on intacct gateway properties
    public pCanUpdateFromExternalIntegration = false;

    // To inform that a reply to the original document was sent from the intacct integration so we don't sent it from the accounting engine and don't mix the document status
    public pHasReplyBeenSentFromExternalIntegration = false;

    // To temporarily store an error message from the intacct integration
    public pExternalIntegrationMessage = '';

    // To allow update on finance integration reply
    protected pForceUpdateForFinance = false;

    // To flag if we are on the posting action, used on the extensions and to decide about the correct ocument status
    public pIsPosting = false;

    @decorators.referenceProperty<AccountsPayableInvoice, 'financialSite'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        provides: ['site'],
        node: () => xtremSystem.nodes.Site,
        filters: { control: { isFinance: true } },
        isFrozen: true,
        lookupAccess: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.stringProperty<AccountsPayableInvoice, 'financialSiteName'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isRequired: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => xtremSystem.dataTypes.name,
        dependsOn: ['financialSite'],
        async defaultValue() {
            return (await this.financialSite).name;
        },
        isFrozen: true,
        lookupAccess: true,
    })
    readonly financialSiteName: Promise<string>;

    @decorators.stringProperty<AccountsPayableInvoice, 'financialSiteTaxIdNumber'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'custom',
        anonymizeValue(value) {
            return xtremStructure.functions.anonymizeTaxId(value);
        },
        dataType: () => xtremStructure.dataTypes.taxIdentificationDataType,
        dependsOn: ['financialSite'],
        async defaultValue() {
            return (await (await this.financialSite).businessEntity).taxIdNumber;
        },
        isFrozen: true,
        lookupAccess: true,
    })
    readonly financialSiteTaxIdNumber: Promise<string>;

    @decorators.enumProperty<AccountsPayableInvoice, 'type'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.financeDocumentTypeDataType,
        isFrozen: true,
        lookupAccess: true,
        async control(cx) {
            if ((await this.type) !== 'purchaseInvoice' && (await this.type) !== 'purchaseCreditMemo') {
                cx.error.addLocalized(
                    '@sage/xtrem-finance/nodes__accounts_payable_invoice__type_invalid',
                    "The accounts payable invoice type must be 'Purchasing invoice' or 'Purchasing credit memo'.",
                );
            }
        },
    })
    readonly type: Promise<xtremFinanceData.enums.FinanceDocumentType>;

    @decorators.stringProperty<AccountsPayableInvoice, 'number'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly number: Promise<string>;

    @decorators.dateProperty<AccountsPayableInvoice, 'invoiceDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isFrozen: true,
        lookupAccess: true,
        defaultValue: () => date.today(),
    })
    readonly invoiceDate: Promise<date>;

    @decorators.dateProperty<AccountsPayableInvoice, 'postingDate'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly postingDate: Promise<date>;

    @decorators.referenceProperty<AccountsPayableInvoice, 'billBySupplier'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Supplier,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly billBySupplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.stringProperty<AccountsPayableInvoice, 'billBySupplierName'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isRequired: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => xtremSystem.dataTypes.name,
        dependsOn: ['billBySupplier'],
        lookupAccess: true,
        async defaultValue() {
            return (await (await this.billBySupplier).businessEntity).name;
        },
        isFrozen: true,
    })
    readonly billBySupplierName: Promise<string>;

    @decorators.stringProperty<AccountsPayableInvoice, 'billBySupplierTaxIdNumber'>({
        isPublished: true,
        isStored: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'custom',
        lookupAccess: true,
        anonymizeValue(value) {
            return xtremStructure.functions.anonymizeTaxId(value);
        },
        dataType: () => xtremStructure.dataTypes.taxIdentificationDataType,
        dependsOn: ['billBySupplier'],
        async defaultValue() {
            return (await (await this.billBySupplier).businessEntity).taxIdNumber;
        },
        isFrozen: true,
    })
    readonly billBySupplierTaxIdNumber: Promise<string>;

    @decorators.referenceProperty<AccountsPayableInvoice, 'payToSupplier'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Supplier,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly payToSupplier: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.referenceProperty<AccountsPayableInvoice, 'payToSupplierLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly payToSupplierLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress | null>;

    @decorators.referenceProperty<AccountsPayableInvoice, 'returnLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly returnLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress | null>;

    @decorators.referenceProperty<AccountsPayableInvoice, 'currency'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['billBySupplier'],
        async defaultValue() {
            return (await (await this.billBySupplier)?.businessEntity)?.currency;
        },
        isFrozen: true,
        lookupAccess: true,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<AccountsPayableInvoice, 'paymentTerm'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.PaymentTerm,
        dependsOn: ['billBySupplier'],
        async defaultValue() {
            return (await this.billBySupplier)?.paymentTerm;
        },
        isFrozen() {
            return !this.pCanUpdateFromExternalIntegration;
        },
        lookupAccess: true,
    })
    readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm>;

    @decorators.decimalProperty<AccountsPayableInvoice, 'totalAmountExcludingTax'>({
        isPublished: true,
        isStoredOutput: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: [{ lines: ['amountExcludingTax'] }],
        defaultValue() {
            return this.lines.sum(line => line.amountExcludingTax);
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly totalAmountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableInvoice, 'totalAmountIncludingTax'>({
        isPublished: true,
        isStoredOutput: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['totalAmountExcludingTax', 'totalTaxAmountAdjusted'],
        async defaultValue() {
            return (await this.totalAmountExcludingTax) + (await this.totalTaxAmountAdjusted);
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly totalAmountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableInvoice, 'totalTaxAmount'>({
        isStoredOutput: true,
        isPublished: true,
        dependsOn: [{ lines: ['taxAmount'] }],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        defaultValue() {
            return this.lines.sum(line => line.taxAmount);
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly totalTaxAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableInvoice, 'totalTaxAmountAdjusted'>({
        isStoredOutput: true,
        isPublished: true,
        dependsOn: [{ lines: ['taxAmountAdjusted'] }],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        defaultValue() {
            return this.lines.sum(line => line.taxAmountAdjusted);
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly totalTaxAmountAdjusted: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableInvoice, 'totalTaxableAmount'>({
        isStoredOutput: true,
        isPublished: true,
        dependsOn: [{ lines: ['taxableAmount'] }],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        defaultValue() {
            return this.lines.sum(line => line.taxableAmount);
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly totalTaxableAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableInvoice, 'totalExemptAmount'>({
        isStoredOutput: true,
        isPublished: true,
        dependsOn: [{ lines: ['exemptAmount'] }],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        defaultValue() {
            return this.lines.sum(line => line.exemptAmount);
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly totalExemptAmount: Promise<decimal>;

    @decorators.enumProperty<AccountsPayableInvoice, 'postingStatus'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.journalStatusDataType,
        defaultValue() {
            return 'draft';
        },
        isFrozen() {
            return !this.pIsPosting && !this.pCanUpdateFromExternalIntegration && !this.pForceUpdateForFinance;
        },
        lookupAccess: true,
    })
    readonly postingStatus: Promise<xtremFinanceData.enums.JournalStatus>;

    @decorators.enumProperty<AccountsPayableInvoice, 'origin'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isFrozen: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.accountsPayableReceivableInvoiceOriginDataType,
    })
    readonly origin: Promise<xtremFinanceData.enums.AccountsPayableReceivableInvoiceOrigin>;

    @decorators.stringProperty<AccountsPayableInvoice, 'supplierDocumentNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        isFrozen() {
            return !this.pCanUpdateFromExternalIntegration;
        },
    })
    readonly supplierDocumentNumber: Promise<string>;

    @decorators.dateProperty<AccountsPayableInvoice, 'supplierDocumentDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen() {
            return !this.pCanUpdateFromExternalIntegration;
        },
    })
    readonly supplierDocumentDate: Promise<date>;

    @decorators.stringProperty<AccountsPayableInvoice, 'reference'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly reference: Promise<string>;

    @decorators.stringProperty<AccountsPayableInvoice, 'description'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<AccountsPayableInvoice, 'account'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isFrozen: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.Account,
    })
    readonly account: Reference<xtremFinanceData.nodes.Account>;

    @decorators.dateProperty<AccountsPayableInvoice, 'dueDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['paymentTerm', 'invoiceDate'],
        async defaultValue() {
            const paymentTerm = await this.paymentTerm;
            return xtremMasterData.sharedFunctions.getDueDate({
                dueDateType: await paymentTerm.dueDateType,
                days: (await paymentTerm.days) ?? 0,
                baseDate: await this.invoiceDate,
            });
        },
        isFrozen() {
            return !this.pCanUpdateFromExternalIntegration;
        },
        lookupAccess: true,
    })
    readonly dueDate: Promise<date>;

    @decorators.decimalProperty<AccountsPayableInvoice, 'companyFxRate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'financialSite', 'fxRateDate'],
        async defaultValue() {
            return (
                await xtremMasterData.functions.getRateOrReverseRate(
                    this.$.context,
                    await this.currency,
                    await (
                        await (
                            await this.financialSite
                        ).legalCompany
                    ).currency,
                    await this.fxRateDate,
                )
            ).rate;
        },
        isFrozen: true,
    })
    readonly companyFxRate: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableInvoice, 'companyFxRateDivisor'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'financialSite', 'fxRateDate'],
        async defaultValue() {
            return (
                await xtremMasterData.functions.getRateOrReverseRate(
                    this.$.context,
                    await this.currency,
                    await (
                        await (
                            await this.financialSite
                        ).legalCompany
                    ).currency,
                    await this.fxRateDate,
                )
            ).divisor;
        },
        isFrozen: true,
    })
    readonly companyFxRateDivisor: Promise<decimal>;

    @decorators.dateProperty<AccountsPayableInvoice, 'fxRateDate'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['invoiceDate'],
        defaultValue() {
            return this.invoiceDate;
        },
        isFrozen: true,
        lookupAccess: true,
    })
    readonly fxRateDate: Promise<date>;

    @decorators.decimalProperty<AccountsPayableInvoice, 'totalCompanyAmountExcludingTax'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            // convert totalAmountExcludingTax into company currency
            return (await this.companyFxRate) !== 1 || (await this.companyFxRateDivisor) !== 1
                ? xtremMasterData.sharedFunctions.convertAmount(
                      await this.totalAmountExcludingTax,
                      await this.companyFxRate,
                      await this.companyFxRateDivisor,
                      await (
                          await this.currency
                      ).decimalDigits,
                      await (
                          await (
                              await (
                                  await this.financialSite
                              ).legalCompany
                          ).currency
                      ).decimalDigits,
                  )
                : this.totalAmountExcludingTax;
        },
        lookupAccess: true,
    })
    readonly totalCompanyAmountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableInvoice, 'totalCompanyAmountIncludingTax'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return (await this.totalCompanyAmountExcludingTax) + (await this.totalCompanyTaxAmount);
        },
        lookupAccess: true,
    })
    readonly totalCompanyAmountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableInvoice, 'totalCompanyTaxAmount'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async getValue() {
            // convert totalTaxAmount into company currency
            return (await this.companyFxRate) !== 1 || (await this.companyFxRateDivisor) !== 1
                ? xtremMasterData.sharedFunctions.convertAmount(
                      await this.totalTaxAmount,
                      await this.companyFxRate,
                      await this.companyFxRateDivisor,
                      await (
                          await this.currency
                      ).decimalDigits,
                      await (
                          await (
                              await (
                                  await this.financialSite
                              ).legalCompany
                          ).currency
                      ).decimalDigits,
                  )
                : this.totalTaxAmount;
        },
        lookupAccess: true,
    })
    readonly totalCompanyTaxAmount: Promise<decimal>;

    @decorators.stringProperty<AccountsPayableInvoice, 'rateDescription'>({
        isPublished: true,
        async computeValue() {
            const financialCurrency = await (await (await this.financialSite).legalCompany).currency;
            const currentCurrency = await this.currency;
            const companyFxRate = await this.companyFxRate;
            const companyFxRateDivisor = await this.companyFxRateDivisor;
            /**  if rate between transaction and company currency doesn't exist, use reverse rate from
                rate between company currency to transaction currency and round to 10 decimals */
            return companyFxRate !== 1 && companyFxRateDivisor === 1
                ? `1 ${await currentCurrency.id} = ${companyFxRate} ${await financialCurrency.id}`
                : `1 ${await currentCurrency.id} = ${
                      Math.round((companyFxRate / companyFxRateDivisor) * 10 ** 10) / 10 ** 10
                  } ${await financialCurrency.id}`;
        },
        lookupAccess: true,
    })
    readonly rateDescription: Promise<string>;

    @decorators.enumProperty<AccountsPayableInvoice, 'taxCalculationStatus'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dataType: () => xtremMasterData.enums.taxCalculationStatusDataType,
        defaultValue: () => 'notDone',
        lookupAccess: true,
    })
    readonly taxCalculationStatus: Promise<xtremMasterData.enums.TaxCalculationStatus>;

    @decorators.stringProperty<AccountsPayableInvoice, 'purchaseDocumentNumber'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        lookupAccess: true,
    })
    readonly purchaseDocumentNumber: Promise<string>;

    @decorators.integerProperty<AccountsPayableInvoice, 'purchaseDocumentSysId'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly purchaseDocumentSysId: Promise<integer>;

    @decorators.enumProperty<AccountsPayableInvoice, 'financeIntegrationApp'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationAppDataType,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationApp(
                this.$.context,
                await this.number,
                'accountsPayableInvoice',
            );
        },
        lookupAccess: true,
    })
    readonly financeIntegrationApp: Promise<xtremFinanceData.enums.FinanceIntegrationApp | null>;

    @decorators.enumProperty<AccountsPayableInvoice, 'financeIntegrationStatus'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.postingStatusDataType,
        async computeValue() {
            return xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(this.$.context, {
                financeIntegrationStatus: await this.internalFinanceIntegrationStatus,
                externalIntegration: (await this.financeIntegrationApp) !== null,
                financialSite: await this.financialSite,
                documentType:
                    (await (
                        await xtremFinanceData.functions.getFinanceTransaction(
                            this.$.context,
                            await this.number,
                            'accountsPayableInvoice',
                        )
                    )?.documentType) || 'purchaseInvoice',
                targetDocumentType: 'accountsPayableInvoice',
            });
        },
        lookupAccess: true,
    })
    readonly financeIntegrationStatus: Promise<xtremFinanceData.enums.PostingStatus>;

    @decorators.enumProperty<AccountsPayableInvoice, 'internalFinanceIntegrationStatus'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
        async computeValue() {
            return (
                (await (
                    await xtremFinanceData.functions.getFinanceTransaction(
                        this.$.context,
                        await this.number,
                        'accountsPayableInvoice',
                    )
                )?.status) || 'recorded'
            );
        },
        lookupAccess: true,
    })
    readonly internalFinanceIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.stringProperty<AccountsPayableInvoice, 'financeIntegrationAppRecordId'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationAppRecordId(
                this.$.context,
                await this.number,
                'accountsPayableInvoice',
            );
        },
        lookupAccess: true,
    })
    readonly financeIntegrationAppRecordId: Promise<string>;

    @decorators.stringProperty<AccountsPayableInvoice, 'financeIntegrationAppUrl'>({
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.url,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationAppUrl(
                this.$.context,
                await this.number,
                'accountsPayableInvoice',
            );
        },
        lookupAccess: true,
    })
    readonly financeIntegrationAppUrl: Promise<string>;

    @decorators.collectionProperty<AccountsPayableInvoice, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => xtremFinance.nodes.AccountsPayableInvoiceLine,
        reverseReference: 'document',
        lookupAccess: true,
    })
    readonly lines: Collection<xtremFinance.nodes.AccountsPayableInvoiceLine>;

    @decorators.collectionProperty<AccountsPayableInvoice, 'openItems'>({
        node: () => xtremFinance.nodes.AccountsPayableOpenItem,
        reverseReference: 'accountsPayableInvoice',
        isPublished: true,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly openItems: Collection<xtremFinance.nodes.AccountsPayableOpenItem>;

    @decorators.collectionProperty<AccountsPayableInvoice, 'taxes'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        lookupAccess: true,
        node: () => xtremFinance.nodes.AccountsPayableInvoiceTax,
        isFrozen() {
            return !this.pCanUpdateFromExternalIntegration;
        },
    })
    readonly taxes: Collection<xtremFinance.nodes.AccountsPayableInvoiceTax>;

    // property needed for the accounting interface
    @decorators.referenceProperty<AccountsPayableInvoice, 'transactionCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        getValue() {
            return this.currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    // property needed for the accounting interface
    @decorators.dateProperty<AccountsPayableInvoice, 'documentDate'>({
        isPublished: true,
        lookupAccess: true,
        getValue() {
            return this.invoiceDate;
        },
    })
    readonly documentDate: Promise<date>;

    @decorators.collectionProperty<AccountsPayableInvoice, 'postingDetails'>({
        isPublished: true,
        join: {
            documentSysId() {
                return this._id;
            },
            documentType() {
                return 'apInvoice';
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    @decorators.referenceProperty<AccountsPayableInvoice, 'paymentTracking'>({
        isStored: true,
        isPublished: true,
        node: () => xtremFinanceData.nodes.PaymentTracking,
        lookupAccess: true,
        isNullable: true,
    })
    readonly paymentTracking: Reference<xtremFinanceData.nodes.PaymentTracking | null>;

    async sendFinanceNotification() {
        if (await (await (await this.financialSite).legalCompany).doApPosting) {
            // send notification in order to create a staging table entry for the accounting engine
            await xtremFinanceData.functions.accountsPayableInvoiceNotification(
                this.$.context,
                this as xtremFinanceData.interfaces.ApInvoice,
                (await this.lines.toArray()) as xtremFinanceData.interfaces.ApArInvoiceLine[],
            );
        }
    }

    @decorators.mutation<typeof AccountsPayableInvoice, 'post'>({
        isPublished: true,
        parameters: [
            {
                name: 'apInvoice',
                type: 'reference',
                isMandatory: true,
                node: () => AccountsPayableInvoice,
                isWritable: true,
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async post(context: Context, apInvoice: AccountsPayableInvoice): Promise<string> {
        if (!['draft', 'error'].includes(await apInvoice.postingStatus)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-finance/nodes__accounts_payable_invoice__cant_post_ap_invoice_when_status_is_not_draft_nor_error',
                    'The posting status is not {{draft}} nor {{error}}. The accounts payable invoice cannot be posted.',
                    {
                        draft: context.localizeEnumMember('@sage/xtrem-finance-data/JournalStatus', 'draft'),
                        error: context.localizeEnumMember('@sage/xtrem-finance-data/JournalStatus', 'error'),
                    },
                ),
            );
        }

        apInvoice.pIsPosting = true;
        if (
            (await context.isServiceOptionEnabled(xtremStructure.serviceOptions.intacctActivationOption)) ||
            (await (
                await (
                    await apInvoice.financialSite
                ).legalCompany
            ).doArPosting)
        ) {
            await apInvoice.$.set({ postingStatus: 'inProgress' });
        } else {
            await apInvoice.$.set({ postingStatus: 'posted' });
        }

        if (!(await context.isServiceOptionEnabled(xtremStructure.serviceOptions.intacctActivationOption))) {
            const openItemData = await AccountsPayableInvoice.newOpenItemPayload(apInvoice);
            const openItemExists =
                (await context.query(xtremFinance.nodes.AccountsPayableOpenItem, {
                    filter: { documentSysId: openItemData.documentSysId, documentType: openItemData.documentType },
                    first: 1,
                }).length) > 0;
            if (!openItemExists) {
                const openItem = await context.create(xtremFinance.nodes.AccountsPayableOpenItem, openItemData);
                await openItem.$.save();
            }

            await apInvoice.sendFinanceNotification();
        }

        apInvoice.pForceUpdateForFinance = true; // make sure the update of the ar invoice will not be refused by the control in saveBegin
        await apInvoice.$.save();
        apInvoice.pIsPosting = false;

        return context.localize(
            '@sage/xtrem-finance/nodes__ap_invoice__posted',
            'The accounts payable invoice has been posted.',
        );
    }

    @decorators.notificationListener<typeof AccountsPayableInvoice>({
        topic: 'AccountsPayableInvoice/accountingInterface',
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        // isJustForStatus indicates that the update of finance transaction node was already done
        const shouldUpdateAPInvoiceStatus =
            payload.isJustForStatus ??
            (await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload));

        if (shouldUpdateAPInvoiceStatus) {
            // we need to update the status of the original AP invoice depending on the reply from finance integration
            // We do not know on this response if we are actually dealing with an invoice or credit memo. Therefore we
            // first try to read the apInvoice with type "purchaseInvoice" and if not found we try type "purchaseCreditMemo"
            let apInvoice = await AccountsPayableInvoice.getApInvoice(
                context,
                payload.documentNumber,
                'purchaseInvoice',
            );
            if (!apInvoice)
                apInvoice = await AccountsPayableInvoice.getApInvoice(
                    context,
                    payload.documentNumber,
                    'purchaseCreditMemo',
                );
            if (!apInvoice) return;

            // the status of the invoice is updated depending on the new status from the paylod
            apInvoice.pForceUpdateForFinance = true; // to allow the update of the status
            await apInvoice.$.set({
                postingStatus: xtremFinanceData.functions.financeStatusMapping(
                    payload.status,
                    !!payload.financeExternalIntegration,
                    await apInvoice.postingStatus,
                ),
            });

            await apInvoice.$.save();

            // when we get the reply from the journal entry, we also have to reply to the Purchase invoice in order to
            // update the posting status correctly. For FRP1000 integration we have at least 2 records in the FinanceTransactions:
            // one for creating the AP invoice from the Purchase invoice and one for creating the Journal from the AP invoice
            // only the Journal is sent to FRP1000, the AP invoice is just an intermediate step and will never get a "posted"
            // status from FRP1000.
            // With Intacct integration we do not create a journal and therefore the FinanceTransaction record for the
            // targetDocumentType apInvoice will get status 'posted' when posted in Intacct
            if ((await apInvoice.origin) !== 'direct') {
                // only for invoices created from a Purchase invoice or credit memo
                // we read the FinanceTransaction record for the original creation of the AP invoice to get the original batchId
                const financeIntegrationRecord = (
                    await context
                        .query(xtremFinanceData.nodes.FinanceTransaction, {
                            filter: {
                                targetDocumentType: 'accountsPayableInvoice',
                                targetDocumentNumber: payload.documentNumber,
                            },
                        })
                        .toArray()
                )[0];

                if (financeIntegrationRecord) {
                    const originPayload: xtremFinanceData.interfaces.FinanceTransactionData = payload;
                    // before we reply to the Purchase document, we clear the recordId, url and message
                    // here we have a cascaded reply only in case of frp1000 where the AP invoice is not sent to frp1000
                    // and we therefore do not want a url, message or record link on the FinanceTransaction record for the Purchase invoice
                    if (originPayload.financeExternalIntegration) {
                        originPayload.financeExternalIntegration.recordId = '';
                        originPayload.financeExternalIntegration.url = '';
                        originPayload.validationMessages = [];
                    }
                    originPayload.batchId = await financeIntegrationRecord.batchId;
                    originPayload.targetDocumentNumber = payload.documentNumber;
                    originPayload.documentType =
                        (await apInvoice.origin) === 'invoice' ? 'purchaseInvoice' : 'purchaseCreditMemo';
                    originPayload.targetDocumentSysId = apInvoice._id;
                    originPayload.targetDocumentType = 'accountsPayableInvoice';
                    await context.reply(
                        (await apInvoice.origin) === 'invoice'
                            ? 'PurchaseInvoice/accountingInterface'
                            : 'PurchaseCreditMemo/accountingInterface',
                        originPayload,
                    );
                }
            }
        }
    }

    @decorators.mutation<typeof AccountsPayableInvoice, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [{ name: 'apInvoice', type: 'reference', isMandatory: true, node: () => AccountsPayableInvoice }],
        return: { type: 'boolean' },
    })
    static async resendNotificationForFinance(context: Context, apInvoice: AccountsPayableInvoice): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-finance/node__accounts_payable_invoice__resend_notification_for_finance',
                'Resending finance notification for accounts payable invoice: {{apInvoiceNumber}}.',
                { apInvoiceNumber: await apInvoice.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await apInvoice.number,
                documentType: 'apInvoice',
            })
        ) {
            await apInvoice.sendFinanceNotification();
        }

        return true;
    }

    @decorators.notificationListener<typeof AccountsPayableInvoice>({
        topic: 'AccountsPayableInvoice/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const apInvoice = await context.read(AccountsPayableInvoice, { number: document.number });

        await AccountsPayableInvoice.resendNotificationForFinance(context, apInvoice);
    }

    // Returns an accounts payable invoice or null.
    private static getApInvoice(
        context: Context,
        documentNumber: string,
        type: string,
    ): Promise<xtremFinance.nodes.AccountsPayableInvoice | null> {
        return context.tryRead(
            xtremFinance.nodes.AccountsPayableInvoice,
            {
                number: documentNumber,
                type,
            },
            { forUpdate: true },
        );
    }

    public static async newOpenItemPayload(
        apInvoice: xtremFinance.nodes.AccountsPayableInvoice,
    ): Promise<NodeCreateData<xtremFinance.nodes.AccountsPayableOpenItem>> {
        const billBySupplier = await apInvoice.billBySupplier;
        let businessEntityPayment = await billBySupplier.businessEntity;

        businessEntityPayment = (await billBySupplier?.businessEntity) ?? businessEntityPayment;
        let documentType: xtremFinanceData.enums.FinanceDocumentType = 'apInvoice';
        if ((await apInvoice.origin) === 'invoice') documentType = 'purchaseInvoice';
        if ((await apInvoice.origin) === 'creditMemo') documentType = 'purchaseCreditMemo';
        return {
            dueDate: await apInvoice.dueDate,
            businessEntity: await (await apInvoice.billBySupplier).businessEntity,
            businessEntityPayment,
            type: 'supplier',
            currency: await apInvoice.currency,
            transactionAmountDue: await apInvoice.totalAmountIncludingTax,
            transactionAmountPaid: 0,
            companyAmountDue: await apInvoice.totalCompanyAmountIncludingTax,
            companyAmountPaid: 0,
            // TODO: convert, when site currency might be different from company currency
            financialSiteAmountDue: await apInvoice.totalCompanyAmountIncludingTax,
            financialSiteAmountPaid: 0,
            status: 'notPaid',
            documentSysId: (await apInvoice.purchaseDocumentSysId)
                ? await apInvoice.purchaseDocumentSysId
                : apInvoice._id,
            documentNumber: (await apInvoice.purchaseDocumentNumber)
                ? await apInvoice.purchaseDocumentNumber
                : await apInvoice.number,
            documentType,
            accountsPayableInvoice: apInvoice._id,
        };
    }

    // Listener to add a reference to a given payment tracking to a given AP invoice.
    // It is called when the service option paymentTrackingOption is activated
    @decorators.notificationListener<typeof AccountsPayableInvoice>({
        topic: 'AccountsReceivableInvoice/initPaymentTracking',
    })
    static async initPaymentTracking(context: Context, payload: { _id: number; paymentTracking: number }) {
        const apInvoice = await context.read(AccountsPayableInvoice, { _id: payload._id }, { forUpdate: true });
        if (apInvoice) {
            apInvoice.pCanUpdateFromExternalIntegration = true; // make sure the update of the ap invoice will not be refused by the control in saveBegin
            await apInvoice.$.set({ paymentTracking: payload.paymentTracking });
            await apInvoice.$.save();
        }
    }
}
