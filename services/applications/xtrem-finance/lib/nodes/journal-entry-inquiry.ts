import type { Collection, Context } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremFinance from '../../index';

@decorators.node<JournalEntryInquiry>({
    package: 'xtrem-finance',
    storage: 'sql',
    isPublished: true,
    canRead: true,
})
export class JournalEntryInquiry extends Node {
    @decorators.collectionProperty<JournalEntryInquiry, 'journalEntryLines'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinance.nodes.JournalEntryLine,
        getFilter() {
            return {};
        },
    })
    readonly journalEntryLines: Collection<xtremFinance.nodes.JournalEntryLine>;

    @decorators.mutation<typeof JournalEntryInquiry, 'singleRecord'>({
        isPublished: true,
        parameters: [],
        return: 'string',
    })
    static async singleRecord(context: Context): Promise<string> {
        let journalEntryInquiry = await context.query(JournalEntryInquiry).at(0);

        if (journalEntryInquiry) {
            return journalEntryInquiry._id.toString();
        }

        journalEntryInquiry = await context.create(JournalEntryInquiry, {});
        await journalEntryInquiry.$.save();
        return journalEntryInquiry._id.toString();
    }
}
