import type { Collection, date, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinance from '../index';

@decorators.subNode<AccountsPayableInvoiceLine>({
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    isPublished: true,
    isVitalCollectionChild: true,
})
export class AccountsPayableInvoiceLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements xtremFinanceData.interfaces.ApArInvoiceLine
{
    @decorators.referenceProperty<AccountsPayableInvoiceLine, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremFinance.nodes.AccountsPayableInvoice,
        isFrozen: true,
        lookupAccess: true,
    })
    override readonly document: Reference<xtremFinance.nodes.AccountsPayableInvoice>;

    @decorators.stringPropertyOverride<AccountsPayableInvoiceLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<AccountsPayableInvoiceLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.referenceProperty<AccountsPayableInvoiceLine, 'financialSite'>({
        isStored: true,
        isPublished: true,
        dependsOn: [{ document: ['financialSite'] }],
        node: () => xtremSystem.nodes.Site,
        filters: {
            control: {
                isFinance: true,
            },
        },
        async defaultValue() {
            return (await this.document).financialSite;
        },
        isFrozen: true,
        lookupAccess: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.dateProperty<AccountsPayableInvoiceLine, 'taxDate'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly taxDate: Promise<date>;

    @decorators.referenceProperty<AccountsPayableInvoiceLine, 'recipientSite'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['financialSite'],
        node: () => xtremSystem.nodes.Site,
        defaultValue() {
            return this.financialSite;
        },
        isFrozen: true,
        lookupAccess: true,
    })
    readonly recipientSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<AccountsPayableInvoiceLine, 'account'>({
        isStored: true,
        isPublished: true,
        node: () => xtremFinanceData.nodes.Account,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly account: Reference<xtremFinanceData.nodes.Account>;

    @decorators.enumProperty<AccountsPayableInvoiceLine, 'documentLineType'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.accountsPayableReceivableInvoiceDocumentLineTypeDataType,
        defaultValue: 'documentLine',
        isFrozen: true,
        lookupAccess: true,
    })
    readonly documentLineType: Promise<xtremFinanceData.enums.AccountsPayableReceivableInvoiceDocumentLineType>;

    @decorators.enumProperty<AccountsPayableInvoiceLine, 'lineType'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.accountsPayableReceivableInvoiceLineTypeDataType,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly lineType: Promise<xtremFinanceData.enums.AccountsPayableReceivableInvoiceLineType>;

    @decorators.referenceProperty<AccountsPayableInvoiceLine, 'currency'>({
        isStored: true,
        isPublished: true,
        dependsOn: [{ document: ['currency'] }],
        node: () => xtremMasterData.nodes.Currency,
        async defaultValue() {
            return (await this.document).currency;
        },
        isFrozen: true,
        lookupAccess: true,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<AccountsPayableInvoiceLine, 'amountExcludingTax'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['documentLineType'],
        defaultValue() {
            return 0;
        },
        async updatedValue() {
            return (await this.documentLineType) === 'taxLine'
                ? 0 // force zero for US tax lines
                : this.amountExcludingTax;
        },
        isFrozen: true,
        lookupAccess: true,
    })
    readonly amountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableInvoiceLine, 'amountIncludingTax'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['amountExcludingTax', 'taxAmountAdjusted', 'documentLineType'],
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async defaultValue() {
            return (await this.documentLineType) === 'taxLine'
                ? 0 // force zero for US tax lines
                : (await this.amountExcludingTax) + (await this.taxAmountAdjusted);
        },
        updatedValue: useDefaultValue,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly amountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableInvoiceLine, 'taxableAmount'>({
        isStored: true,
        isPublished: true,
        dependsOn: [{ taxes: ['taxableAmount'] }, 'documentLineType'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async defaultValue() {
            return (await this.documentLineType) === 'taxLine'
                ? 0 // force zero for US tax lines
                : this.taxes.sum(taxLine => taxLine.taxableAmount);
        },
        updatedValue: useDefaultValue,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        isFrozen: true,
        lookupAccess: true,
    })
    readonly taxableAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableInvoiceLine, 'taxAmount'>({
        isStoredOutput: true,
        isPublished: true,
        dependsOn: [{ taxes: ['taxAmount'] }, 'documentLineType'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async defaultValue() {
            return (await this.documentLineType) === 'taxLine'
                ? 0 // force zero for US tax lines
                : this.taxes
                      .filter(async taxLine => !(await taxLine.isReverseCharge))
                      .sum(taxLine => taxLine.taxAmount);
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly taxAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsPayableInvoiceLine, 'taxLineTaxAmount'>({
        isStored: true,
        isPublished: true,
        async control(cx, val) {
            if ((await this.documentLineType) === 'taxLine') await cx.error.if(val).is.zero();
        },
        dependsOn: ['documentLineType'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly taxLineTaxAmount: Promise<decimal>;

    @decorators.stringProperty<AccountsPayableInvoiceLine, 'taxDetail'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly taxDetail: Promise<string>;

    @decorators.decimalProperty<AccountsPayableInvoiceLine, 'exemptAmount'>({
        isStored: true,
        isPublished: true,
        dependsOn: [{ taxes: ['exemptAmount'] }, 'documentLineType'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async defaultValue() {
            return (await this.documentLineType) === 'taxLine'
                ? 0 // force zero for US tax lines
                : this.taxes.sum(taxLine => taxLine.exemptAmount);
        },
        updatedValue: useDefaultValue,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        isFrozen: true,
        lookupAccess: true,
    })
    readonly exemptAmount: Promise<decimal>;

    // only for the tax detail panel
    @decorators.decimalProperty<AccountsPayableInvoiceLine, 'taxAmountAdjusted'>({
        isStored: true,
        isPublished: true,
        dependsOn: [{ taxes: ['taxAmountAdjusted'] }, 'documentLineType'],
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        async defaultValue() {
            return (await this.documentLineType) === 'taxLine'
                ? 0 // force zero for US tax lines
                : this.taxes
                      .filter(async taxLine => !(await taxLine.isReverseCharge))
                      .sum(taxLine => taxLine.taxAmountAdjusted);
        },
        updatedValue: useDefaultValue,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        isFrozen: true,
        lookupAccess: true,
    })
    readonly taxAmountAdjusted: Promise<decimal>;

    @decorators.stringProperty<AccountsPayableInvoiceLine, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<AccountsPayableInvoiceLine, 'sourceDocumentNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly sourceDocumentNumber: Promise<string>;

    @decorators.enumProperty<AccountsPayableInvoiceLine, 'sourceDocumentType'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.sourceDocumentTypeDataType,
        async getValue() {
            const origin = await (await this.document).origin;
            if (origin === 'invoice') {
                return 'purchaseInvoice';
            }
            if (origin === 'creditMemo') {
                return 'purchaseCreditMemo';
            }
            return null;
        },
    })
    readonly sourceDocumentType: Promise<xtremFinanceData.enums.SourceDocumentType | null>;

    @decorators.collectionProperty<AccountsPayableInvoiceLine, 'attributesAndDimensions'>({
        isPublished: true,
        isVital: true,
        node: () => xtremFinance.nodes.AccountsPayableInvoiceLineDimension,
        reverseReference: 'originLine',
        lookupAccess: true,
    })
    readonly attributesAndDimensions: Collection<xtremFinance.nodes.AccountsPayableInvoiceLineDimension>;

    @decorators.collectionProperty<AccountsPayableInvoiceLine, 'accountingStagingLines'>({
        isPublished: true,
        isVital: true,
        node: () => xtremFinanceData.nodes.AccountsPayableInvoiceLineStaging,
        reverseReference: 'accountsPayableInvoiceLine',
        isFrozen: true,
        lookupAccess: true,
    })
    readonly accountingStagingLines: Collection<xtremFinanceData.nodes.AccountsPayableInvoiceLineStaging>;

    @decorators.collectionProperty<AccountsPayableInvoiceLine, 'taxes'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        dependsOn: ['currency'],
        node: () => xtremFinance.nodes.AccountsPayableInvoiceLineTax,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly taxes: Collection<xtremFinance.nodes.AccountsPayableInvoiceLineTax>;

    @decorators.jsonProperty<AccountsPayableInvoiceLine, 'uiTaxes'>({
        excludedFromPayload: true,
        isPublished: true,
        dependsOn: ['taxes'],
        async computeValue() {
            return xtremTax.functions.prepareTaxesToUiTaxes<xtremFinance.nodes.AccountsPayableInvoiceLine>(
                this,
                String(await (await (await this.financialSite).legalCompany).taxEngine),
            );
        },
        async setValue(val: xtremTax.interfaces.UiTaxes | null) {
            if (this.$.status === NodeStatus.added) {
                await xtremTax.functions.updateTaxesFromUiTaxes(
                    val,
                    this,
                    String(await (await (await this.financialSite).legalCompany).taxEngine),
                );
            }
        },
        lookupAccess: true,
    })
    readonly uiTaxes: Promise<xtremTax.interfaces.UiTaxes | null>;

    // FIXME: [RM] review when we might have more then one attribute and dimension line
    @decorators.jsonProperty<AccountsPayableInvoiceLine, 'storedDimensions'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        async computeValue() {
            return (await this.attributesAndDimensions.length)
                ? (await (await this.attributesAndDimensions.elementAt(0)).storedDimensions) || {}
                : {};
        },
        lookupAccess: true,
    })
    readonly storedDimensions: Promise<object | null>;

    // FIXME: [RM] review when we might have more then one attribute and dimension line
    @decorators.jsonProperty<AccountsPayableInvoiceLine, 'storedAttributes'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        async computeValue() {
            return (await this.attributesAndDimensions.length)
                ? (await (await this.attributesAndDimensions.elementAt(0)).storedAttributes) ||
                      ({} as xtremMasterData.interfaces.StoredAttributes)
                : ({} as xtremMasterData.interfaces.StoredAttributes);
        },
        lookupAccess: true,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;
}
