import type { Reference, integer } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinance from '../index';

@decorators.node<DatevExportAccount>({
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
})
export class DatevExportAccount extends Node {
    @decorators.referenceProperty<DatevExportAccount, 'datevExport'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinance.nodes.DatevExport,
        isFrozen: true,
    })
    readonly datevExport: Reference<xtremFinance.nodes.DatevExport>;

    @decorators.referenceProperty<DatevExportAccount, 'account'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.Account,
    })
    readonly account: Reference<xtremFinanceData.nodes.Account>;

    @decorators.integerProperty<DatevExportAccount, 'datevId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly datevId: Promise<integer>;

    @decorators.stringProperty<DatevExportAccount, 'name'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
    })
    readonly name: Promise<string>;
}
