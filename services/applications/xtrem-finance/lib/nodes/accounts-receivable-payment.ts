import type { Collection, Context, date, decimal, Reference, ValidationContext } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, Node, NodeStatus } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinance from '../index';

@decorators.node<AccountsReceivablePayment>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    indexes: [
        {
            orderBy: { number: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    async controlEnd(cx: ValidationContext) {
        if (
            this.$.status === NodeStatus.modified &&
            !(this.pIsPosting || this.pCanUpdateFromExternalIntegration) &&
            ['posted', 'inProgress', 'error'].includes(await (await this.$.old).postingStatus)
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance/nodes__accounts_receivable_payment__update_not_allowed_status_posted',
                'The accounts receivable payment status is {{status}}. You cannot update it.',
                {
                    status: this.$.context.localizeEnumMember(
                        '@sage/xtrem-finance-data/JournalStatus',
                        await (
                            await this.$.old
                        ).postingStatus,
                    ),
                },
            );
        }
    },
    async saveBegin() {
        await xtremMasterData.functions.controlDocumentNumber(this);
    },
})
export class AccountsReceivablePayment extends Node {
    // To allow update on intacct gateway properties
    public pCanUpdateFromExternalIntegration = false;

    // To inform that a reply to the original document was sent from the intacct integration so we don't sent it from the accounting engine and don't mix the document status
    public pHasReplyBeenSentFromExternalIntegration = false;

    // To temporarily store an error message from the intacct integration
    public pExternalIntegrationMessage = '';

    // To allow update on finance integration reply
    protected pForceUpdateForFinance = false;

    // To flag if we are on the posting action, used on the extensions and to decide about the correct ocument status
    public pIsPosting = false;

    @decorators.stringProperty<AccountsReceivablePayment, 'number'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        provides: ['sequenceNumber'],
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        defaultValue() {
            return xtremMasterData.functions.getDocumentNumberPlaceholder();
        },
        deferredDefaultValue() {
            return xtremMasterData.functions.allocateDocumentNumber(this);
        },
    })
    readonly number: Promise<string>;

    @decorators.referenceProperty<AccountsReceivablePayment, 'bankAccount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.BankAccount,
        isNullable: true, // XT-80680 changed during bank account refactor since cash book management will be removed
    })
    readonly bankAccount: Reference<xtremFinanceData.nodes.BankAccount | null>;

    @decorators.referenceProperty<AccountsReceivablePayment, 'financialSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
        filters: {
            control: {
                isFinance: true,
            },
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.stringProperty<AccountsReceivablePayment, 'financialSiteName'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNotEmpty: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => xtremSystem.dataTypes.name,
        dependsOn: ['financialSite'],
        async defaultValue() {
            return (await this.financialSite).name;
        },
    })
    readonly financialSiteName: Promise<string>;

    @decorators.stringProperty<AccountsReceivablePayment, 'payToCustomerId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.id,
    })
    readonly payToCustomerId: Promise<string>;

    @decorators.stringProperty<AccountsReceivablePayment, 'payToCustomerName'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNotEmpty: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly payToCustomerName: Promise<string>;

    @decorators.stringProperty<AccountsReceivablePayment, 'reference'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly reference: Promise<string>;

    @decorators.stringProperty<AccountsReceivablePayment, 'description'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.dateProperty<AccountsReceivablePayment, 'postingDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
    })
    readonly postingDate: Promise<date>;

    @decorators.dateProperty<AccountsReceivablePayment, 'paymentDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        provides: ['documentDate'],
    })
    readonly paymentDate: Promise<date>;

    @decorators.referenceProperty<AccountsReceivablePayment, 'currency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<AccountsReceivablePayment, 'companyCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<AccountsReceivablePayment, 'transactionCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        getValue() {
            return this.currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<AccountsReceivablePayment, 'companyFxRate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
    })
    readonly companyFxRate: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivablePayment, 'companyFxRateDivisor'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
    })
    readonly companyFxRateDivisor: Promise<decimal>;

    @decorators.dateProperty<AccountsReceivablePayment, 'fxRateDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly fxRateDate: Promise<date>;

    @decorators.decimalProperty<AccountsReceivablePayment, 'paymentAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
    })
    readonly paymentAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivablePayment, 'paymentCompanyAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
    })
    readonly paymentCompanyAmount: Promise<decimal>;

    @decorators.enumProperty<AccountsReceivablePayment, 'postingStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.journalStatusDataType,
        defaultValue() {
            return 'draft';
        },
    })
    readonly postingStatus: Promise<xtremFinanceData.enums.JournalStatus>;

    @decorators.enumProperty<AccountsReceivablePayment, 'financeIntegrationApp'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationAppDataType,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationApp(
                this.$.context,
                await this.number,
                'accountsReceivablePayment',
            );
        },
    })
    readonly financeIntegrationApp: Promise<xtremFinanceData.enums.FinanceIntegrationApp | null>;

    @decorators.enumProperty<AccountsReceivablePayment, 'financeIntegrationStatus'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.postingStatusDataType,
        async computeValue() {
            return xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(this.$.context, {
                financeIntegrationStatus: await this.internalFinanceIntegrationStatus,
                externalIntegration: await this.$.context.isServiceOptionEnabled(
                    xtremStructure.serviceOptions.intacctActivationOption,
                ),
                financialSite: await this.financialSite,
                documentType: 'bankReconciliationDeposit',
                targetDocumentType: 'accountsReceivablePayment',
            });
        },
    })
    readonly financeIntegrationStatus: Promise<xtremFinanceData.enums.PostingStatus>;

    @decorators.enumProperty<AccountsReceivablePayment, 'internalFinanceIntegrationStatus'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
        async computeValue() {
            return (
                (await (
                    await xtremFinanceData.functions.getFinanceTransaction(
                        this.$.context,
                        await this.number,
                        'accountsReceivablePayment',
                    )
                )?.status) || 'recorded'
            );
        },
    })
    readonly internalFinanceIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.stringProperty<AccountsReceivablePayment, 'financeIntegrationAppRecordId'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationAppRecordId(
                this.$.context,
                await this.number,
                'accountsReceivablePayment',
            );
        },
    })
    readonly financeIntegrationAppRecordId: Promise<string>;

    @decorators.stringProperty<AccountsReceivablePayment, 'financeIntegrationAppUrl'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.url,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationAppUrl(
                this.$.context,
                await this.number,
                'accountsReceivablePayment',
            );
        },
    })
    readonly financeIntegrationAppUrl: Promise<string>;

    @decorators.collectionProperty<AccountsReceivablePayment, 'lines'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinance.nodes.AccountsReceivablePaymentLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<xtremFinance.nodes.AccountsReceivablePaymentLine>;

    @decorators.mutation<typeof AccountsReceivablePayment, 'post'>({
        isPublished: true,
        parameters: [
            {
                name: 'arPayment',
                type: 'reference',
                isMandatory: true,
                node: () => AccountsReceivablePayment,
                isWritable: true,
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async post(context: Context, arPayment: AccountsReceivablePayment): Promise<string> {
        if (!['draft', 'error'].includes(await arPayment.postingStatus)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-finance/nodes__accounts_receivable_payment__cant_post_ar_payment_when_status_is_not_draft_nor_error',
                    "The posting status needs to be 'Draft' or 'Error'. The accounts receivable payment cannot be posted.",
                ),
            );
        }

        if (await context.isServiceOptionEnabled(xtremStructure.serviceOptions.intacctActivationOption)) {
            await arPayment.$.set({ postingStatus: 'inProgress' });
        } else {
            await arPayment.$.set({ postingStatus: 'posted' });
        }

        arPayment.pIsPosting = true;

        await arPayment.$.save();
        arPayment.pIsPosting = false;

        return context.localize(
            '@sage/xtrem-finance/nodes__ar_payment__posted',
            'The accounts receivable payment has been posted.',
        );
    }
}
