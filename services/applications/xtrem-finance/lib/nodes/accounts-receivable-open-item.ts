import type { Collection, Context, Reference, decimal } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { upperFirst } from 'lodash';
import { forceOpenItemPayment, notifyUserBulkOpenItemPayment } from '../functions/payment-tracking';
import { signedAmount } from '../functions/signed-amount';
import * as xtremFinance from '../index';

@decorators.subNode<AccountsReceivableOpenItem>({
    extends: () => xtremFinanceData.nodes.BaseOpenItem,
    isPublished: true,
    canSearch: true,
    canRead: true,

    async saveBegin() {
        await xtremFinance.events.saveBegin.OpenItem.saveBegin(this);
    },
})
export class AccountsReceivableOpenItem extends xtremFinanceData.nodes.BaseOpenItem {
    @decorators.referencePropertyOverride<AccountsReceivableOpenItem, 'businessRelation'>({
        node: () => xtremMasterData.nodes.Customer,
        async getValue() {
            return (await this.businessEntity).customer;
        },
    })
    override readonly businessRelation: Reference<xtremMasterData.nodes.Customer | null>;

    @decorators.referenceProperty<AccountsReceivableOpenItem, 'accountsReceivableInvoice'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremFinance.nodes.AccountsReceivableInvoice,
    })
    readonly accountsReceivableInvoice: Reference<xtremFinance.nodes.AccountsReceivableInvoice>;

    @decorators.referenceProperty<AccountsReceivableOpenItem, 'financialSite'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.Site,
        async getValue() {
            return (await this.accountsReceivableInvoice).financialSite;
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.decimalProperty<AccountsReceivableOpenItem, 'totalAmount'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            return signedAmount({
                isReceipt: true,
                isSupplier: false,
                origin: await (await this.accountsReceivableInvoice).origin,
                amount: await this.transactionAmountDue,
            });
        },
    })
    readonly totalAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableOpenItem, 'amountDue'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            return signedAmount({
                isReceipt: true,
                isSupplier: false,
                origin: await (await this.accountsReceivableInvoice).origin,
                amount: (await this.transactionAmountDue) - (await this.transactionAmountPaid),
            });
        },
    })
    readonly amountDue: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableOpenItem, 'totalCompanyAmount'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            return signedAmount({
                isReceipt: true,
                isSupplier: false,
                origin: await (await this.accountsReceivableInvoice).origin,
                amount: await this.companyAmountDue,
            });
        },
    })
    readonly totalCompanyAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableOpenItem, 'totalCompanyAmountPaid'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            return signedAmount({
                isReceipt: true,
                isSupplier: false,
                origin: await (await this.accountsReceivableInvoice).origin,
                amount: await this.companyAmountPaid,
            });
        },
    })
    readonly totalCompanyAmountPaid: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableOpenItem, 'remainingCompanyAmount'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            return signedAmount({
                isReceipt: true,
                isSupplier: false,
                origin: await (await this.accountsReceivableInvoice).origin,
                amount: (await this.companyAmountDue) - (await this.companyAmountPaid),
            });
        },
    })
    readonly remainingCompanyAmount: Promise<decimal>;

    @decorators.collectionProperty<AccountsReceivableOpenItem, 'receipts'>({
        isPublished: true,
        node: () => xtremFinanceData.nodes.PaymentDocumentLine,
        join: {
            async originalNodeFactory() {
                return `#${upperFirst(await this.documentType)}`;
            },
            originalOpenItem() {
                return { _id: this._id };
            },
        },
    })
    readonly receipts: Collection<xtremFinanceData.nodes.PaymentDocumentLine>;

    // Bulk mutation to update the selected open items to 'paid' and also update the payment and display status on the
    // related sales invoice or credit memo and AR invoice to paid.
    // Finally notify the user that the open items were forced to be paid.
    // This bulk mutation is called from the bulkAction on the main list of the Open item edit page
    @decorators.bulkMutation<typeof AccountsReceivableOpenItem, 'bulkOpenItemUpdate'>({
        isPublished: true,
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
        async onComplete(context, documents) {
            await notifyUserBulkOpenItemPayment(context, documents, '@sage/xtrem-finance/AccountsReceivableOpenItem');
        },
    })
    static async bulkOpenItemUpdate(context: Context, document: AccountsReceivableOpenItem) {
        if ((await document.status) !== 'paid') {
            await forceOpenItemPayment(context, document);
            return document.documentNumber;
        }
        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-finance/nodes__accounts_receivable_open_item__skipped',
                'Document skipped as it is already fully paid: {{number}}.',
                { number: await document.documentNumber },
            ),
        );
        return '';
    }
}
