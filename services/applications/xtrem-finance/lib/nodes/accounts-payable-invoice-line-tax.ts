import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinance from '../index';

@decorators.subNode<AccountsPayableInvoiceLineTax>({
    extends: () => xtremTax.nodes.BaseLineTax,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
})
export class AccountsPayableInvoiceLineTax
    extends xtremTax.nodes.BaseLineTax
    implements xtremMasterData.interfaces.FinancialSubNode
{
    @decorators.referenceProperty<AccountsPayableInvoiceLineTax, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremFinance.nodes.AccountsPayableInvoiceLine,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly document: Reference<xtremFinance.nodes.AccountsPayableInvoiceLine>;

    @decorators.referencePropertyOverride<AccountsPayableInvoiceLineTax, 'currency'>({
        dependsOn: [{ document: ['currency'] }],
        async getValue() {
            return (await this.document).currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;
}
