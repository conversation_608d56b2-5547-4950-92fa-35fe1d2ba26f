import type { Context, date } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremFinance from '..';

@decorators.subNode<Payment>({
    extends: () => xtremFinanceData.nodes.BasePaymentDocument,
    isPublished: true,
    canSearch: true,
    canRead: true,
    serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],

    async controlBegin(cx) {
        await xtremFinance.events.controlBegin.PaymentDocument.controlBegin({
            basePaymentDocument: this,
            typeToCheck: 'supplier',
            cx,
        });
    },

    async saveBegin() {
        await xtremFinance.events.saveBegin.BasePaymentDocument.saveBegin(this, 'supplier');
    },
})
export class Payment extends xtremFinanceData.nodes.BasePaymentDocument {
    @decorators.mutation<typeof Payment, 'voidPayment'>({
        isPublished: true,
        parameters: [
            {
                name: 'payment',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => Payment,
            },
            { name: 'voidDate', type: 'date', isMandatory: true },
            { name: 'voidText', type: 'string' },
        ],
        return: {
            type: 'string',
        },
    })
    static voidPayment(context: Context, payment: Payment, voidDate: date, voidText: string): Promise<string> {
        return xtremFinance.functions.voidPayment({ context, payment, voidDate, voidText });
    }
}
