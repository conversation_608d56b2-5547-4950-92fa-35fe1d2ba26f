import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinance from '../../index';

@decorators.subNode<AccountsReceivableInvoiceTax>({
    extends: () => xtremTax.nodes.BaseTax,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
})
export class AccountsReceivableInvoiceTax
    extends xtremTax.nodes.BaseTax
    implements xtremMasterData.interfaces.FinancialSubNode
{
    @decorators.referenceProperty<AccountsReceivableInvoiceTax, 'document'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinance.nodes.AccountsReceivableInvoice,
        isFrozen: true,
    })
    readonly document: Reference<xtremFinance.nodes.AccountsReceivableInvoice>;

    @decorators.referencePropertyOverride<AccountsReceivableInvoiceTax, 'currency'>({
        dependsOn: [{ document: ['currency'] }],
        async getValue() {
            return (await this.document).currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;
}
