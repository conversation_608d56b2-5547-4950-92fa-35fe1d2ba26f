import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremFinance from '../index';

@decorators.node<JournalEntryLineStaging>({
    package: 'xtrem-finance',
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
})
export class JournalEntryLineStaging extends Node {
    @decorators.referenceProperty<JournalEntryLineStaging, 'journalEntryLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinance.nodes.JournalEntryLine,
        isFrozen: true,
    })
    readonly journalEntryLine: Reference<xtremFinance.nodes.JournalEntryLine>;

    @decorators.referenceProperty<JournalEntryLineStaging, 'accountingStaging'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        node: () => xtremFinanceData.nodes.AccountingStaging,
        isFrozen: true,
    })
    readonly accountingStaging: Reference<xtremFinanceData.nodes.AccountingStaging>;
}
