import type { decimal, Dict, integer, Reference } from '@sage/xtrem-core';
import { decorators, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremFinance from '../index';

@decorators.subNode<AccountsReceivableAdvanceLine>({
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    isPublished: true,
    isVitalCollectionChild: true,
    async saveBegin() {
        if (
            this.$.status === NodeStatus.modified &&
            ((await this.$.old).storedAttributes !== this.storedAttributes ||
                (await this.$.old).storedDimensions !== this.storedDimensions)
        ) {
            await this.$.set({ hasAttributesOrDimenionsChanged: true });
        }
    },
    async controlEnd(cx) {
        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
})
export class AccountsReceivableAdvanceLine extends xtremMasterData.nodes.BaseDocumentLine {
    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'document'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinance.nodes.AccountsReceivableAdvance,
        isFrozen: true,
    })
    override readonly document: Reference<xtremFinance.nodes.AccountsReceivableAdvance>;

    @decorators.stringPropertyOverride<AccountsReceivableAdvanceLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<AccountsReceivableAdvanceLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'financialSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ document: ['financialSite'] }],
        node: () => xtremSystem.nodes.Site,
        filters: {
            control: {
                isFinance: true,
                async legalCompany() {
                    return (await (await this.document).financialSite).legalCompany;
                },
            },
        },
        async defaultValue() {
            return (await this.document).financialSite;
        },
        isFrozen: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    /** Contra GL Account  */
    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'account'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.Account,
        isNullable: true,
        filters: {
            control: {
                taxManagement: { _in: ['excludingTax', 'other'] },
            },
        },
        async control(cx, val) {
            if (await (await val?.chartOfAccount)?.legislation) {
                // TODO - Tx implementation on AR Advance line
                // if (
                //     (await this.tax) &&
                //     (await (await val?.chartOfAccount)?.legislation)?._id !== (await (await this.tax)?.legislation)?._id
                // ) {
                //     cx.error.addLocalized(
                //         '@sage/xtrem-intacct-finance/nodes__intacct-bank-account-transaction_feed__tax_legislation_and_coa_legislation_dont_match',
                //         'The tax legislation must be the same as the chart of accounts legislation.',
                //     );
                // }
                if (
                    (await this.financialSite) &&
                    (await (await val?.chartOfAccount)?.legislation)?._id !==
                        (await (await (await this.financialSite)?.legalCompany)?.legislation)?._id
                ) {
                    cx.error.addLocalized(
                        '@sage/xtrem-finance/nodes__intacct-bank-account-transaction_feed__site_legislation_and_coa_legislation_dont_match',
                        'The site legislation must be the same as the chart of accounts legislation.',
                    );
                }
            }
        },
        isFrozen: true,
    })
    readonly account: Reference<xtremFinanceData.nodes.Account | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'currency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ document: ['currency'] }],
        node: () => xtremMasterData.nodes.Currency,
        async defaultValue() {
            return (await this.document).currency;
        },
        isFrozen: true,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'companyCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'transactionCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        getValue() {
            return this.currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<AccountsReceivableAdvanceLine, 'advanceAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        isFrozen: true,
    })
    readonly advanceAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableAdvanceLine, 'advanceCompanyAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        isFrozen: true,
    })
    readonly advanceCompanyAmount: Promise<decimal>;

    /** DESCRIPTION */
    @decorators.stringProperty<AccountsReceivableAdvanceLine, 'description'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => dataTypes.description,
        isFrozen: true,
    })
    readonly description: Promise<string>;

    @decorators.jsonProperty<AccountsReceivableAdvanceLine, 'storedAttributes'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        async isFrozen() {
            return (await (await this.document).internalFinanceIntegrationStatus) !== 'failed';
        },
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<AccountsReceivableAdvanceLine, 'computedAttributes'>({
        isPublished: true,
        lookupAccess: true,
        async computeValue() {
            return xtremFinance.functions.computeAttributes(this.$.context, await this.financialSite);
        },
    })
    readonly computedAttributes: Promise<object>;

    @decorators.jsonProperty<AccountsReceivableAdvanceLine, 'storedDimensions'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        async isFrozen() {
            return (await (await this.document).internalFinanceIntegrationStatus) !== 'failed';
        },
    })
    readonly storedDimensions: Promise<Dict<string> | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.booleanProperty<AccountsReceivableAdvanceLine, 'hasAttributesOrDimenionsChanged'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        async isFrozen() {
            return (await (await this.document).internalFinanceIntegrationStatus) !== 'failed';
        },
    })
    readonly hasAttributesOrDimenionsChanged: Promise<boolean>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension01'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType01 ?? null;
            },
            dimensionType() {
                return '#dimensionType01';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType01' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension01: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension02'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType02 ?? null;
            },
            dimensionType() {
                return '#dimensionType02';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType02' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension02: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension03'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType03 ?? null;
            },
            dimensionType() {
                return '#dimensionType03';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType03' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension03: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension04'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType04 ?? null;
            },
            dimensionType() {
                return '#dimensionType04';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType04' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension04: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension05'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType05 ?? null;
            },
            dimensionType() {
                return '#dimensionType05';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType05' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension05: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension06'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType06 ?? null;
            },
            dimensionType() {
                return '#dimensionType06';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType06' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension06: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension07'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType07 ?? null;
            },
            dimensionType() {
                return '#dimensionType07';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType07' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension07: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension08'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType08 ?? null;
            },
            dimensionType() {
                return '#dimensionType08';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType08' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension08: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension09'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType09 ?? null;
            },
            dimensionType() {
                return '#dimensionType09';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType09' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension09: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension10'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType10 ?? null;
            },
            dimensionType() {
                return '#dimensionType10';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType10' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension10: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension11'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType11 ?? null;
            },
            dimensionType() {
                return '#dimensionType11';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType11' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension11: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension12'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType12 ?? null;
            },
            dimensionType() {
                return '#dimensionType12';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType12' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension12: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension13'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType13 ?? null;
            },
            dimensionType() {
                return '#dimensionType13';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType13' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension13: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension14'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType14 ?? null;
            },
            dimensionType() {
                return '#dimensionType14';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType14' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension14: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension15'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType15 ?? null;
            },
            dimensionType() {
                return '#dimensionType15';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType15' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension15: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension16'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType16 ?? null;
            },
            dimensionType() {
                return '#dimensionType16';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType16' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension16: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension17'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType17 ?? null;
            },
            dimensionType() {
                return '#dimensionType17';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType17' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension17: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension18'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType18 ?? null;
            },
            dimensionType() {
                return '#dimensionType18';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType18' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension18: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension19'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType19 ?? null;
            },
            dimensionType() {
                return '#dimensionType19';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType19' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension19: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'dimension20'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType20 ?? null;
            },
            dimensionType() {
                return '#dimensionType20';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType20' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension20: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'businessSite'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        async computeValue() {
            return ((await this.computedAttributes) as xtremFinanceData.interfaces.ComputedAttributes)?.businessSite
                ? this.$.context.tryRead(xtremSystem.nodes.Site, {
                      id: ((await this.computedAttributes) as xtremFinanceData.interfaces.ComputedAttributes)
                          ?.businessSite,
                  })
                : null;
        },
    })
    readonly businessSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'stockSite'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        async computeValue() {
            return ((await this.computedAttributes) as xtremFinanceData.interfaces.ComputedAttributes)?.stockSite
                ? this.$.context.tryRead(xtremSystem.nodes.Site, {
                      id: ((await this.computedAttributes) as xtremFinanceData.interfaces.ComputedAttributes)
                          ?.stockSite,
                  })
                : null;
        },
    })
    readonly stockSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'manufacturingSite'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        async computeValue() {
            return ((await this.computedAttributes) as xtremFinanceData.interfaces.ComputedAttributes)
                ?.manufacturingSite
                ? this.$.context.tryRead(xtremSystem.nodes.Site, {
                      id: ((await this.computedAttributes) as xtremFinanceData.interfaces.ComputedAttributes)
                          ?.manufacturingSite,
                  })
                : null;
        },
    })
    readonly manufacturingSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.stringProperty<AccountsReceivableAdvanceLine, 'customer'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            return `${await (await this.document).payToCustomerId} -- ${await (await this.document).payToCustomerName}`;
        },
    })
    readonly customer: Promise<string>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'supplier'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Supplier,
        async computeValue() {
            return ((await this.computedAttributes) as xtremFinanceData.interfaces.ComputedAttributes)?.supplier
                ? (
                      await this.$.context.tryRead(xtremMasterData.nodes.BusinessEntity, {
                          id: ((await this.computedAttributes) as xtremFinanceData.interfaces.ComputedAttributes)
                              ?.supplier,
                      })
                  )?.supplier || null
                : null;
        },
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'project'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
        async computeValue() {
            return ((await this.computedAttributes) as xtremMasterData.interfaces.StoredAttributes)?.project
                ? this.$.context.tryRead(xtremFinanceData.nodes.Attribute, {
                      id: ((await this.computedAttributes) as xtremMasterData.interfaces.StoredAttributes)?.project,
                      attributeType: await this.$.context.read(xtremFinanceData.nodes.AttributeType, {
                          id: 'project',
                      }),
                  })
                : null;
        },
        filters: {
            lookup: {
                attributeType: { id: { _eq: 'project' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly project: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'employee'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
        async computeValue() {
            return ((await this.computedAttributes) as xtremMasterData.interfaces.StoredAttributes)?.employee
                ? this.$.context.tryRead(xtremFinanceData.nodes.Attribute, {
                      id: ((await this.computedAttributes) as xtremMasterData.interfaces.StoredAttributes)?.employee,
                      attributeType: await this.$.context.read(xtremFinanceData.nodes.AttributeType, {
                          id: 'employee',
                      }),
                  })
                : null;
        },
        filters: {
            lookup: {
                attributeType: { id: { _eq: 'employee' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly employee: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'task'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
        async computeValue() {
            return ((await this.computedAttributes) as xtremMasterData.interfaces.StoredAttributes)?.task
                ? this.$.context.tryRead(xtremFinanceData.nodes.Attribute, {
                      id: ((await this.computedAttributes) as xtremMasterData.interfaces.StoredAttributes)?.task,
                      attributeType: await this.$.context.read(xtremFinanceData.nodes.AttributeType, {
                          id: 'task',
                      }),
                  })
                : null;
        },
        filters: {
            lookup: {
                attributeType: { id: { _eq: 'task' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly task: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<AccountsReceivableAdvanceLine, 'item'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Item,
        async computeValue() {
            return ((await this.computedAttributes) as xtremFinanceData.interfaces.ComputedAttributes)?.item
                ? this.$.context.tryRead(xtremMasterData.nodes.Item, {
                      id: ((await this.computedAttributes) as xtremFinanceData.interfaces.ComputedAttributes)?.item,
                  })
                : null;
        },
    })
    readonly item: Reference<xtremMasterData.nodes.Item | null>;
}
