import type { Collection, Context, date, decimal, Reference } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, Node, NodeStatus } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as _ from 'lodash';
import * as xtremFinance from '../index';

@decorators.node<JournalEntry>({
    isClearedByReset: true,
    package: 'xtrem-finance',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    indexes: [
        {
            orderBy: { number: 1, journal: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    notifies: ['created', 'updated', 'deleted'],

    async controlEnd(cx) {
        await xtremFinance.events.controlEnd.JournalEntry.controlEnd(this, cx);
    },

    async saveEnd() {
        await xtremFinance.events.saveEnd.JournalEntry.saveEnd(this);
    },
})
export class JournalEntry extends Node {
    // To allow update on intacct gateway properties
    public pCanUpdateFromExternalIntegration = false;

    // To allow the update of the document by recreating the lines, we need to nullify the references to other lines
    pCanNullifyContraJournalEntryLine = false;

    // To temporarily store an error message from the external integration
    public pExternalIntegrationMessage = '';

    // To inform that a reply to the original document was sent from the intacct integration so we don't sent it from the accounting engine and don't mix the document status
    public pHasReplyBeenSentFromExternalIntegration = false;

    // To flag if we are on the posting action, used on the extensions and to decide about the correct ocument status
    public pIsPosting = false;

    @decorators.stringProperty<JournalEntry, 'number'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        // override data type's defaultValue (nanoid)
        defaultValue() {
            return xtremMasterData.functions.getDocumentNumberPlaceholder();
        },
        async deferredDefaultValue() {
            const sequenceId = await (await (await this.journal).sequence)?.id;
            if (!sequenceId) {
                throw new BusinessRuleError(
                    this.$.context.localize(
                        '@sage/xtrem-finance/nodes__journal_entry__no_sequence_number',
                        'No default sequence number. A journal entry sequence number cannot be generated.',
                    ),
                );
            }
            const generator = await xtremMasterData.classes.DocumentNumberGenerator.create(this.$.context, {
                sequenceNumber: sequenceId,
                currentDate: await this.postingDate,
                skipControls: true,
            });
            return generator.allocate();
        },
        async control(cx) {
            if (
                this.$.status !== NodeStatus.added &&
                this._id > 0 &&
                (await this.number) !== (await (await this.$.old).number)
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance/nodes__journal_entry__number_change_forbidden',
                    'The journal number is only editable for new records.',
                );
            }
        },
        isFrozen: true,
    })
    readonly number: Promise<string>;

    @decorators.referenceProperty<JournalEntry, 'financialSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        provides: ['site'],
        node: () => xtremSystem.nodes.Site,
        filters: {
            control: {
                isFinance: true,
            },
        },
        isFrozen: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<JournalEntry, 'arInvoice'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinance.nodes.AccountsReceivableInvoice,
        async control(cx, value) {
            if (value && (await this.origin) !== 'arInvoice') {
                cx.error.addLocalized(
                    '@sage/xtrem-finance/nodes__journal_entry__ar_invoice_reference',
                    'The accounts receivable invoice reference can only be used in journal entries originating from an accounts receivable invoice.',
                );
            }
        },
        isFrozen: true,
    })
    readonly arInvoice: Reference<xtremFinance.nodes.AccountsReceivableInvoice | null>;

    @decorators.referenceProperty<JournalEntry, 'apInvoice'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinance.nodes.AccountsPayableInvoice,
        async control(cx, value) {
            if (value && (await this.origin) !== 'apInvoice') {
                cx.error.addLocalized(
                    '@sage/xtrem-finance/nodes__journal_entry__ap_invoice_reference',
                    'The accounts payable invoice reference can only be used in journal entries originating from an accounts payable invoice.',
                );
            }
        },
        isFrozen: true,
    })
    readonly apInvoice: Reference<xtremFinance.nodes.AccountsPayableInvoice | null>;

    @decorators.stringProperty<JournalEntry, 'documentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.id,
        dependsOn: ['journal'],
        async defaultValue() {
            return (await this.journal).primaryDocumentType;
        },
        async control(cx, value) {
            if (value && (await (await (await (await this.financialSite).legalCompany).legislation).id) !== 'FR') {
                cx.error.addLocalized(
                    '@sage/xtrem-finance/nodes__journal_entry__document_type_not_allowed',
                    'The document type can only be set for the French legislation.',
                );
            }
        },
        isFrozen: true,
    })
    readonly documentType: Promise<string>;

    @decorators.enumProperty<JournalEntry, 'postingStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.journalStatusDataType,
        isFrozen() {
            return !this.pIsPosting && !this.pCanUpdateFromExternalIntegration;
        },
    })
    readonly postingStatus: Promise<xtremFinanceData.enums.JournalStatus>;

    @decorators.referenceProperty<JournalEntry, 'journal'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremFinanceData.nodes.Journal,
        filters: {
            control: {
                sequence() {
                    return { _ne: null };
                },
            },
        },
        isFrozen: true,
    })
    readonly journal: Reference<xtremFinanceData.nodes.Journal>;

    @decorators.dateProperty<JournalEntry, 'postingDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
    })
    readonly postingDate: Promise<date>;

    @decorators.stringProperty<JournalEntry, 'description'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: false,
        isNotEmpty: false,
        dataType: () => xtremSystem.dataTypes.description,
        isFrozen: true,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<JournalEntry, 'reference'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: false,
        isNotEmpty: false,
        dataType: () => xtremSystem.dataTypes.description,
        isFrozen: true,
    })
    readonly reference: Promise<string>;

    @decorators.enumProperty<JournalEntry, 'origin'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.journalOriginDataType,
        isFrozen: true,
    })
    readonly origin: Promise<xtremFinanceData.enums.JournalOrigin>;

    @decorators.enumProperty<JournalEntry, 'financeIntegrationApp'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationAppDataType,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationApp(
                this.$.context,
                await this.number,
                'journalEntry',
            );
        },
    })
    readonly financeIntegrationApp: Promise<xtremFinanceData.enums.FinanceIntegrationApp | null>;

    @decorators.enumProperty<JournalEntry, 'financeIntegrationStatus'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.postingStatusDataType,
        async computeValue() {
            return xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(this.$.context, {
                financeIntegrationStatus: await this.internalFinanceIntegrationStatus,
                externalIntegration: (await this.financeIntegrationApp) !== null,
                financialSite: await this.financialSite,
                documentType:
                    (await (
                        await xtremFinanceData.functions.getFinanceTransaction(
                            this.$.context,
                            await this.number,
                            'journalEntry',
                        )
                    )?.documentType) || 'salesInvoice',
                targetDocumentType: 'journalEntry',
            });
        },
    })
    readonly financeIntegrationStatus: Promise<xtremFinanceData.enums.PostingStatus>;

    @decorators.enumProperty<JournalEntry, 'internalFinanceIntegrationStatus'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
        async computeValue() {
            return (
                (await (
                    await xtremFinanceData.functions.getFinanceTransaction(
                        this.$.context,
                        await this.number,
                        'journalEntry',
                    )
                )?.status) || 'recorded'
            );
        },
    })
    readonly internalFinanceIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.stringProperty<JournalEntry, 'financeIntegrationAppRecordId'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationAppRecordId(
                this.$.context,
                await this.number,
                'journalEntry',
            );
        },
    })
    readonly financeIntegrationAppRecordId: Promise<string>;

    @decorators.stringProperty<JournalEntry, 'financeIntegrationAppUrl'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.url,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationAppUrl(
                this.$.context,
                await this.number,
                'journalEntry',
            );
        },
    })
    readonly financeIntegrationAppUrl: Promise<string>;

    @decorators.collectionProperty<JournalEntry, 'lines'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinance.nodes.JournalEntryLine,
        reverseReference: 'journalEntry',
    })
    readonly lines: Collection<xtremFinance.nodes.JournalEntryLine>;

    financialSiteTransactionCurrencyLineBalance(
        site: xtremSystem.nodes.Site,
        currency: xtremMasterData.nodes.Currency,
    ): Promise<decimal> {
        return this.lines
            .filter(async line => (await line.financialSite) === site && (await line.transactionCurrency) === currency)
            .sum(async line => {
                await this.$.context.logger.debugAsync(
                    async () =>
                        ` ${await line.financialSite} ${await line.transactionCurrency} ${await line.sign} ${
                            (await line.companyAmount) + (await line.transactionAmount)
                        }`,
                );
                return (
                    ((await line.sign) === 'D' ? (await line.companyAmount) + (await line.transactionAmount) : 0) -
                    ((await line.sign) === 'C' ? (await line.companyAmount) + (await line.transactionAmount) : 0)
                );
            });
    }

    async financialSiteTransactionCurrencyUniqKey(): Promise<
        {
            financialSite: xtremSystem.nodes.Site;
            transactionCurrency: xtremMasterData.nodes.Currency;
        }[]
    > {
        const financialSiteTransactionCurrencyUniqKey = Array.from(
            new Set(
                await this.lines
                    .map(async line => ({
                        financialSite: await line.financialSite,
                        transactionCurrency: await line.transactionCurrency,
                    }))
                    .toArray(),
            ),
        );

        return _.uniqWith(financialSiteTransactionCurrencyUniqKey, _.isEqual);
    }

    @decorators.query<typeof JournalEntry, 'areFinanceIntegrationPackagesActive'>({
        isPublished: true,
        parameters: [
            {
                name: 'dummy',
                type: 'string',
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    static async areFinanceIntegrationPackagesActive(context: Context, dummy?: string): Promise<boolean> {
        const activePackages = await context.getActivePackageNames();
        return activePackages.includes('@sage/xtrem-intacct-finance');
    }

    /**
     * set pIsPosting to true & launch the save action then pIsPosting false
     * @param context
     * @param journalEntry reference of the journalEntry to post
     * @returns 'The journal entry has been posted.' text
     */
    @decorators.mutation<typeof JournalEntry, 'post'>({
        isPublished: true,
        parameters: [
            {
                name: 'journalEntry',
                type: 'reference',
                isMandatory: true,
                node: () => JournalEntry,
                isWritable: true,
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async post(context: Context, journalEntry: JournalEntry): Promise<string> {
        if (!['draft', 'error'].includes(await journalEntry.postingStatus)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-finance/nodes__journal-entry__cant_post_journal_entry_when_status_is_not_draft_nor_error',
                    'The posting status is not {{draft}} nor {{error}}. The journal entry invoice cannot be posted.',
                    {
                        draft: context.localizeEnumMember('@sage/xtrem-finance-data/JournalStatus', 'draft'),
                        error: context.localizeEnumMember('@sage/xtrem-finance-data/JournalStatus', 'error'),
                    },
                ),
            );
        }

        journalEntry.pIsPosting = true;
        if (await context.isServiceOptionEnabled(xtremStructure.serviceOptions.intacctActivationOption)) {
            await journalEntry.$.set({ postingStatus: 'inProgress' });
        } else {
            await journalEntry.$.set({ postingStatus: 'posted' });
        }
        await journalEntry.$.save({ flushDeferredActions: true });
        journalEntry.pIsPosting = false;

        return context.localize(
            '@sage/xtrem-finance/nodes__journal-entry__posted',
            'The journal entry has been posted.',
        );
    }
}
