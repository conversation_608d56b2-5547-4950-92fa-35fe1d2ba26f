import type { decimal, Dict, Reference } from '@sage/xtrem-core';
import { decorators, Node, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinance from '../index';

@decorators.node<AccountsPayableInvoiceLineDimension>({
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    async saveBegin() {
        if (
            this.$.status === NodeStatus.modified &&
            ((await this.$.old).storedAttributes !== this.storedAttributes ||
                (await this.$.old).storedDimensions !== this.storedDimensions)
        ) {
            await this.$.set({ hasAttributesOrDimenionsChanged: true });
        }
    },
    async controlEnd(cx) {
        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
})
export class AccountsPayableInvoiceLineDimension extends Node {
    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'originLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinance.nodes.AccountsPayableInvoiceLine,
        isFrozen: true,
    })
    readonly originLine: Reference<xtremFinance.nodes.AccountsPayableInvoiceLine>;

    @decorators.jsonProperty<AccountsPayableInvoiceLineDimension, 'storedAttributes'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        async isFrozen() {
            return (await (await (await this.originLine).document).internalFinanceIntegrationStatus) !== 'failed';
        },
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<AccountsPayableInvoiceLineDimension, 'storedDimensions'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        async isFrozen() {
            return (await (await (await this.originLine).document).internalFinanceIntegrationStatus) !== 'failed';
        },
    })
    readonly storedDimensions: Promise<Dict<string> | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.booleanProperty<AccountsPayableInvoiceLineDimension, 'hasAttributesOrDimenionsChanged'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        async isFrozen() {
            return (await (await (await this.originLine).document).internalFinanceIntegrationStatus) !== 'failed';
        },
    })
    readonly hasAttributesOrDimenionsChanged: Promise<boolean>;

    @decorators.decimalProperty<AccountsPayableInvoiceLineDimension, 'amount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        isFrozen: true,
    })
    readonly amount: Promise<decimal>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension01'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType01 ?? null;
            },
            dimensionType() {
                return '#dimensionType01';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType01' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension01: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension02'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType02 ?? null;
            },
            dimensionType() {
                return '#dimensionType02';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType02' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension02: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension03'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType03 ?? null;
            },
            dimensionType() {
                return '#dimensionType03';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType03' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension03: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension04'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType04 ?? null;
            },
            dimensionType() {
                return '#dimensionType04';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType04' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension04: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension05'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType05 ?? null;
            },
            dimensionType() {
                return '#dimensionType05';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType05' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension05: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension06'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType06 ?? null;
            },
            dimensionType() {
                return '#dimensionType06';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType06' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension06: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension07'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType07 ?? null;
            },
            dimensionType() {
                return '#dimensionType07';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType07' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension07: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension08'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType08 ?? null;
            },
            dimensionType() {
                return '#dimensionType08';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType08' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension08: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension09'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType09 ?? null;
            },
            dimensionType() {
                return '#dimensionType09';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType09' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension09: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension10'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType10 ?? null;
            },
            dimensionType() {
                return '#dimensionType10';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType10' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension10: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension11'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType11 ?? null;
            },
            dimensionType() {
                return '#dimensionType11';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType11' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension11: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension12'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType12 ?? null;
            },
            dimensionType() {
                return '#dimensionType12';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType12' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension12: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension13'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType13 ?? null;
            },
            dimensionType() {
                return '#dimensionType13';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType13' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension13: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension14'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType14 ?? null;
            },
            dimensionType() {
                return '#dimensionType14';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType14' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension14: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension15'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType15 ?? null;
            },
            dimensionType() {
                return '#dimensionType15';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType15' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension15: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension16'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType16 ?? null;
            },
            dimensionType() {
                return '#dimensionType16';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType16' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension16: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension17'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType17 ?? null;
            },
            dimensionType() {
                return '#dimensionType17';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType17' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension17: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension18'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType18 ?? null;
            },
            dimensionType() {
                return '#dimensionType18';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType18' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension18: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension19'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType19 ?? null;
            },
            dimensionType() {
                return '#dimensionType19';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType19' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension19: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'dimension20'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['storedDimensions'],
        join: {
            async id() {
                return (await this.storedDimensions)?.dimensionType20 ?? null;
            },
            dimensionType() {
                return '#dimensionType20';
            },
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType20' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension20: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'financialSite'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.financialSite ?? null;
            },
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'businessSite'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.businessSite ?? null;
            },
        },
    })
    readonly businessSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'stockSite'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.stockSite ?? null;
            },
        },
    })
    readonly stockSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'manufacturingSite'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.manufacturingSite ?? null;
            },
        },
    })
    readonly manufacturingSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'customer'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Customer,
        dependsOn: ['storedAttributes'],
        join: {
            async businessEntity() {
                const id = (await this.storedAttributes)?.customer;
                return id ? `#${id}` : null;
            },
        },
    })
    readonly customer: Reference<xtremMasterData.nodes.Customer | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'supplier'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Supplier,
        dependsOn: ['storedAttributes'],
        join: {
            async businessEntity() {
                const id = (await this.storedAttributes)?.supplier;
                return id ? `#${id}` : null;
            },
        },
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'project'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.project ?? null;
            },
            attributeType() {
                return '#project';
            },
        },
        filters: {
            lookup: {
                attributeType: { id: { _eq: 'project' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly project: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'employee'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.employee ?? null;
            },
            attributeType() {
                return '#employee';
            },
        },
        filters: {
            lookup: {
                attributeType: { id: { _eq: 'employee' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly employee: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'task'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.task ?? null;
            },
            attributeType() {
                return '#task';
            },
            async attributeRestrictedToId() {
                return (await this.storedAttributes)?.project ?? null;
            },
        },
        filters: {
            lookup: {
                attributeType: { id: { _eq: 'task' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly task: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<AccountsPayableInvoiceLineDimension, 'item'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Item,
        dependsOn: ['storedAttributes'],
        join: {
            async id() {
                return (await this.storedAttributes)?.item ?? null;
            },
        },
    })
    readonly item: Reference<xtremMasterData.nodes.Item | null>;
}
