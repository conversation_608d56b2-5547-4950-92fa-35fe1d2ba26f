import type { AsyncArray, Context, NodeQueryFilter } from '@sage/xtrem-core';
import { Logger, Node, asyncArray, decorators } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinance from '../../index';

const logger = Logger.getLogger(__filename, 'accountingInterfaceListener');

@decorators.node<AccountingInterfaceListener>({
    package: 'xtrem-finance',
    isPublished: true,
})
export class AccountingInterfaceListener extends Node {
    @decorators.notificationListener({
        topic: 'accountingInterface',
        startsReadOnly: true,
    })
    static async onAccountingDataPosting(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceIntegrationDocument,
    ): Promise<void> {
        logger.debug(() => `Payload: ${JSON.stringify(payload)}`);

        try {
            await context.runInWritableContext<xtremFinanceData.interfaces.FinanceTransactionData>(writableContext =>
                xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(writableContext, {
                    financeDocument: payload,
                    notificationId: context.getContextValue('notificationId') || '',
                    replyTopic: context.getContextValue('replyTopic') || '',
                    isProcessed: false,
                }),
            );

            // init immediatePosting with hard coded fallback for the case we do not find the journal entry type
            let doImmediatePosting = (
                [
                    'salesInvoice',
                    'salesCreditMemo',
                    'purchaseInvoice',
                    'purchaseCreditMemo',
                    'arInvoice',
                    'apInvoice',
                    'bankReconciliationWithdrawal',
                    'bankReconciliationDeposit',
                ] as xtremFinanceData.enums.FinanceDocumentType[]
            ).includes(payload.documentType);
            if (payload.financialSiteSysId) {
                const financialSite = await context.read(xtremSystem.nodes.Site, {
                    _id: payload.financialSiteSysId,
                });
                const journalEntryType = await xtremFinanceData.functions.AccountingEngineCommon.getJournalEntryType(
                    context,
                    await (
                        await financialSite.legalCompany
                    ).legislation,
                    payload.documentType,
                    payload.targetDocumentType,
                );
                if (journalEntryType) doImmediatePosting = await journalEntryType.immediatePosting;
            }

            if (doImmediatePosting) {
                const paramFilter = {
                    isProcessed: false,
                    documentType: payload.documentType,
                    documentNumber: payload.documentNumber,
                };
                await xtremFinance.functions.createJournalsFromAccountingStaging(
                    context,
                    paramFilter,
                    false,
                    payload.batchTrackingId,
                );
            }
        } catch (error) {
            const financeTransactionData: xtremFinanceData.interfaces.FinanceTransactionData = {
                batchId: payload.batchId,
                documentNumber: payload.documentNumber,
                documentType: payload.documentType,
                targetDocumentType: payload.targetDocumentType,
                targetDocumentNumber: '',
                targetDocumentSysId: 0,
                validationMessages: error.message || [],
                status: 'error',
            };
            await context.runInWritableContext(writableContext =>
                writableContext.reply(context.getContextValue('replyTopic') || '', financeTransactionData, {
                    replyId: context.getContextValue('notificationId'),
                }),
            );
        }
    }

    // Listener dedicated to the update of accounting staging records
    @decorators.notificationListener({
        topic: 'accountingInterfaceUpdate',
        startsReadOnly: true,
    })
    static async onAccountingDataUpdatePosting(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate,
    ): Promise<number> {
        logger.debug(() => `Payload to update: ${JSON.stringify(payload)}`);

        let numberOfUpdatedStagingRecords = 0;

        try {
            // For version where we were not updating directly the finance transaction node
            // To be deleted on the next version
            if (!payload.isJustForPost) {
                numberOfUpdatedStagingRecords = await context.runInWritableContext<number>(writableContext => {
                    return xtremFinanceData.functions.AccountingEngineCommon.updateAccountingStaging(
                        writableContext,
                        payload,
                    );
                });
            }

            if (!payload.doNotPostOnUpdate) {
                let paramFilter: NodeQueryFilter<xtremFinanceData.nodes.AccountingStaging> & {
                    toBeReprocessed?: boolean;
                } = {
                    batchId: payload.batchId,
                    isProcessed: { _in: [true, false] }, // we don't use the toBeReprocessed because if just part of the lines are marked as to be reprocessed, the engine will delete all document lines and use only the acc staging lines marked as to be reprocessed. This is the case when the same target document is generated from different source documents has more then one document which is a source for dimensions
                    documentType: payload.documentType,
                    targetDocumentType: payload.targetDocumentType,
                    documentNumber: payload.documentNumber,
                };

                await xtremFinance.functions.createJournalsFromAccountingStaging(context, paramFilter, true);

                const financeTransactionRecordStatus = await (
                    await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                        batchId: payload.batchId,
                        documentNumber: payload.documentNumber,
                        documentType: payload.documentType,
                        targetDocumentType: payload.targetDocumentType,
                    })
                ).status;

                const otherFinanceTransactionRecords = await context.select(
                    xtremFinanceData.nodes.FinanceTransaction,
                    { status: true, targetDocumentType: true },
                    {
                        filter: {
                            batchId: payload.batchId,
                            documentNumber: payload.documentNumber,
                            documentType: payload.documentType,
                            targetDocumentType: { _ne: payload.targetDocumentType },
                        },
                    },
                );

                otherFinanceTransactionRecords.filter(otherFinanceTransactionRecord =>
                    [financeTransactionRecordStatus, 'pending'].includes(otherFinanceTransactionRecord.status),
                );
                await asyncArray(
                    otherFinanceTransactionRecords.filter(otherFinanceTransactionRecord =>
                        [financeTransactionRecordStatus, 'pending'].includes(otherFinanceTransactionRecord.status),
                    ),
                ).forEach(async otherFinanceTransactionRecord => {
                    paramFilter = {
                        batchId: payload.batchId,
                        isProcessed: false,
                        documentType: payload.documentType,
                        documentNumber: payload.documentNumber,
                        targetDocumentType: otherFinanceTransactionRecord.targetDocumentType,
                    };
                    await xtremFinance.functions.createJournalsFromAccountingStaging(context, paramFilter, true);
                });
            }
        } catch (error) {
            context.logger.error(error);
            const financeTransactionData: xtremFinanceData.interfaces.FinanceTransactionData = {
                batchId: payload.batchId,
                documentNumber: payload.documentNumber,
                documentType: payload.documentType,
                targetDocumentType: payload.targetDocumentType,
                targetDocumentNumber: '',
                targetDocumentSysId: 0,
                validationMessages: error.message || [],
                status: 'error',
                isJustForStatus: true, // this will tell the listener static function that update of finance transaction node was already done
            };
            await context.runInWritableContext(async writableContext => {
                // Finance reply:
                // 1 - we update the finance transaction node
                // 2 - if the originated we send a reply to the accounting staging node
                await xtremFinanceData.functions.reactToFinanceIntegrationReply(
                    writableContext,
                    financeTransactionData,
                );

                if (
                    financeTransactionData.targetDocumentType !== 'journalEntry' ||
                    ['apInvoice', 'arInvoice'].includes(financeTransactionData.documentType)
                ) {
                    context.logger.info(
                        `Finance reply on update error for document ${financeTransactionData.documentType} ${financeTransactionData.documentNumber}`,
                    );
                    await context.reply(context.getContextValue('replyTopic') || '', financeTransactionData, {
                        replyId: context.getContextValue('notificationId'),
                    });
                }
            });
        }

        return numberOfUpdatedStagingRecords;
    }

    // Listener dedicated to the update of the printing status of an ar invoice when the correspondent sales invoice or sales credit memo is printed.
    @decorators.notificationListener({
        topic: 'printingStatus/accountingInterface',
    })
    static async onPrintingStatusUpdate(
        context: Context,
        payload: xtremFinanceData.interfaces.PrintedDocument,
    ): Promise<void> {
        const filter = { salesDocumentSysId: payload.documentSysId, type: payload.documentType };

        // use of query because salesInvoice and salesCreditMemo are not unique indexes
        const document: AsyncArray<xtremFinance.nodes.AccountsReceivableInvoice> | null = context.query(
            xtremFinance.nodes.AccountsReceivableInvoice,
            { filter, forUpdate: true },
        );
        if (document && (await document.length) === 1) {
            const arInvoice: xtremFinance.nodes.AccountsReceivableInvoice = await document.elementAt(0);
            await arInvoice.setIsPrintedStatus();
        }
    }

    @decorators.mutation<typeof AccountingInterfaceListener, 'retryFinanceDocument'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'financeTransaction',
                type: 'reference',
                isNullable: false,
                node: () => xtremFinanceData.nodes.FinanceTransaction,
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async retryFinanceDocument(
        context: Context,
        financeTransaction: xtremFinanceData.nodes.FinanceTransaction,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        context.logger.info(`retryFinanceDocument: financeTransaction = ${JSON.stringify(financeTransaction)}`);
        let message = '';

        if (!['pending', 'submitted', 'failed', 'notRecorded', 'error'].includes(await financeTransaction.status)) {
            message = context.localize(
                '@sage/xtrem-finance/status_not_supported',
                'Status not supported: {{status}}.',
                { status: await financeTransaction.status },
            );
            context.logger.info(message);
            return {
                wasSuccessful: true,
                message,
            };
        }

        const financeDocument: xtremFinance.dataTypes.JournalEntryOrApArInvoiceType | null =
            await xtremFinance.functions.financeRetry.getFinanceDocument(context, financeTransaction);

        // If we have already a finance document created on SDMO
        if (financeDocument) {
            context.logger.info(`Finance document found on SDMO: ${financeDocument.number}`);
            // If we have intacct-finance we do the retry from there
            if ((await context.getActivePackageNames()).includes('@sage/xtrem-intacct-finance')) {
                const retryFinanceDocumentResult = (await context.executeGraphql(
                    `mutation {
            xtremIntacctFinance {
            financeListener {
            retryFinanceDocument(
                financeTransaction: ${financeTransaction._id},
                financeDocumentSysId: ${financeDocument._id})
                {
                    wasSuccessful
                    message
                  }}}}`,
                )) as xtremFinanceData.interfaces.RetryFinanceDocumentMutationReply;

                context.logger.info(JSON.stringify(retryFinanceDocumentResult));

                return (
                    (retryFinanceDocumentResult?.xtremIntacctFinance?.financeListener
                        ?.retryFinanceDocument as xtremFinanceData.interfaces.MutationResult) || {
                        wasSuccessful: false,
                        message: context.localize(
                            '@sage/xtrem-finance/unexpected_error',
                            'Unexpected error: {{retryFinanceDocumentResult}}',
                            {
                                retryFinanceDocumentResult: context.diagnoses.length
                                    ? context.diagnoses
                                          .map(diagnose => {
                                              return diagnose.message;
                                          })
                                          .join('\n')
                                    : JSON.stringify(retryFinanceDocumentResult),
                            },
                        ),
                    }
                );
            }
        }

        // If we don't have already a finance document created on SDMO, we'll try to create
        // However, the OneOfMany will only create documents when there's diferent targets
        // for the same batchId

        const documentType = await financeTransaction.documentType;
        const documentNumber = await financeTransaction.documentNumber;
        const sourceDocumentType = await financeTransaction.sourceDocumentType;
        const sourceDocumentNumber = await financeTransaction.sourceDocumentNumber;
        const targetDocumentType = await financeTransaction.targetDocumentType;
        const batchId = await financeTransaction.batchId;

        const createFinanceDocumentResult = (await context.query(xtremFinanceData.nodes.FinanceTransaction, {
            filter: { documentType, documentNumber, batchId, targetDocumentType: { _ne: targetDocumentType } },
            first: 1,
        }).length)
            ? await xtremFinance.functions.createOneOfManyFinanceDocumentsFromAccountingStaging(
                  context,
                  documentType,
                  documentNumber,
                  targetDocumentType,
                  batchId,
              )
            : await xtremFinance.functions.createJournalsFromAccountingStaging(context, {
                  documentType,
                  documentNumber,
                  targetDocumentType,
                  batchId,
              });
        let wasSuccessful = true;
        if (!createFinanceDocumentResult.reduce((count, element) => count + element.documentsCreated.length, 0)) {
            context.logger.info(`Retry did not generated any document.`);
            if (
                [
                    'purchaseCreditMemo',
                    'purchaseInvoice',
                    'salesInvoice',
                    'stockAdjustment',
                    'salesShipment',
                    'stockValueChange',
                    'stockCount',
                    'miscellaneousStockIssue',
                    'purchaseReceipt',
                    'miscellaneousStockReceipt',
                    'purchaseReturn',
                    'salesCreditMemo',
                    'salesReturnReceipt',
                    'apInvoice',
                    'arInvoice',
                    'workInProgress',
                    'stockTransferShipment',
                    'stockTransferReceipt',
                ].includes(documentType)
            ) {
                context.logger.info(`The finance notification will be resent.`);

                await context.runInWritableContext(writableContext =>
                    writableContext.notify(
                        xtremFinance.functions.financeRetry.getTopicByDocumentType(
                            writableContext,
                            documentType,
                            sourceDocumentType,
                        ), // Can use the constructor when we have a reference to Base Document on finance transaction
                        {
                            number:
                                documentType === 'workInProgress' &&
                                ['materialTracking', 'operationTracking', 'productionTracking'].includes(
                                    sourceDocumentType,
                                )
                                    ? sourceDocumentNumber
                                    : documentNumber,
                        },
                    ),
                );

                return {
                    wasSuccessful: true,
                    message: context.localize(
                        '@sage/xtrem-finance/node__accounting-interface-listener__finance_document_notification_resent',
                        'The document is being reprocessed.',
                    ),
                };
            }
            wasSuccessful = false;
        }

        message = xtremFinanceData.functions.AccountingEngineCommon.getInformationFromCreateFinanceDocumentsReturn(
            context,
            createFinanceDocumentResult,
        );
        context.logger.info(message);
        return {
            wasSuccessful,
            message,
        };
    }

    @decorators.mutation<typeof AccountingInterfaceListener, 'createJournalsFromAccountingStaging'>({
        isPublished: true,
        parameters: [
            {
                name: 'journalsCreatedData',
                type: 'boolean',
                isMandatory: true,
            },
            { name: 'filter', type: 'string', isMandatory: false },
        ],
        return: {
            type: 'string',
        },
        startsReadOnly: true,
    })
    static async createJournalsFromAccountingStaging(
        context: Context,
        journalsCreatedData: boolean,
        filter: string,
    ): Promise<string> {
        const oFilter = { ...JSON.parse(filter), isProcessed: false };
        return xtremFinanceData.functions.AccountingEngineCommon.getInformationFromCreateFinanceDocumentsReturn(
            context,
            await xtremFinance.functions.createJournalsFromAccountingStaging(context, oFilter),
            journalsCreatedData,
        );
    }

    @decorators.asyncMutation<typeof AccountingInterfaceListener, 'createJournalsFromAccountingStagingJob'>({
        isPublished: true,
        isSchedulable: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'journalsCreatedData',
                type: 'boolean',
                isMandatory: true,
            },
            { name: 'filter', type: 'string' },
            { name: 'batchTrackingId', type: 'string' },
        ],
        return: {
            type: 'string',
        },
    })
    static async createJournalsFromAccountingStagingJob(
        context: Context,
        journalsCreatedData: boolean,
        filter: string,
        batchTrackingId?: string,
    ): Promise<string> {
        const oFilter = filter ? JSON.parse(filter) : {};
        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-finance/node__accounting_staging__start_create_journal',
                'Accounting staging start ',
            ),
        );
        const messageJournalCreated =
            xtremFinanceData.functions.AccountingEngineCommon.getInformationFromCreateFinanceDocumentsReturn(
                context,
                await xtremFinance.functions.createJournalsFromAccountingStaging(
                    context,
                    {
                        ...oFilter,
                        isProcessed: false,
                    },
                    false,
                    batchTrackingId,
                ),
                journalsCreatedData,
            );

        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-finance/node__accounting_staging__finish_create_journal',
                'Accounting staging : finish {{messageJournalCreated}}',
                { messageJournalCreated },
            ),
        );

        return messageJournalCreated;
    }
}
