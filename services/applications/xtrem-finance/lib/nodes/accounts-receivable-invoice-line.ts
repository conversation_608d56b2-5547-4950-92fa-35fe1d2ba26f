import type { Collection, date, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinance from '../index';

@decorators.subNode<AccountsReceivableInvoiceLine>({
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    isPublished: true,
    isVitalCollectionChild: true,
})
export class AccountsReceivableInvoiceLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements xtremFinanceData.interfaces.ApArInvoiceLine
{
    @decorators.referenceProperty<AccountsReceivableInvoiceLine, 'document'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinance.nodes.AccountsReceivableInvoice,
        isFrozen: true,
    })
    override readonly document: Reference<xtremFinance.nodes.AccountsReceivableInvoice>;

    @decorators.stringPropertyOverride<AccountsReceivableInvoiceLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<AccountsReceivableInvoiceLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.referenceProperty<AccountsReceivableInvoiceLine, 'financialSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ document: ['financialSite'] }],
        node: () => xtremSystem.nodes.Site,
        filters: {
            control: {
                isFinance: true,
            },
        },
        async defaultValue() {
            return (await this.document).financialSite;
        },
        isFrozen: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.dateProperty<AccountsReceivableInvoiceLine, 'taxDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
    })
    readonly taxDate: Promise<date>;

    @decorators.referenceProperty<AccountsReceivableInvoiceLine, 'providerSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['financialSite'],
        node: () => xtremSystem.nodes.Site,
        defaultValue() {
            return this.financialSite;
        },
        isFrozen: true,
    })
    readonly providerSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<AccountsReceivableInvoiceLine, 'account'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.Account,
        isFrozen: true,
    })
    readonly account: Reference<xtremFinanceData.nodes.Account>;

    @decorators.enumProperty<AccountsReceivableInvoiceLine, 'documentLineType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.accountsPayableReceivableInvoiceDocumentLineTypeDataType,
        defaultValue: 'documentLine',
        isFrozen: true,
    })
    readonly documentLineType: Promise<xtremFinanceData.enums.AccountsPayableReceivableInvoiceDocumentLineType>;

    @decorators.enumProperty<AccountsReceivableInvoiceLine, 'lineType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremFinanceData.enums.accountsPayableReceivableInvoiceLineTypeDataType,
    })
    readonly lineType: Promise<xtremFinanceData.enums.AccountsPayableReceivableInvoiceLineType>;

    @decorators.referenceProperty<AccountsReceivableInvoiceLine, 'currency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ document: ['currency'] }],
        node: () => xtremMasterData.nodes.Currency,
        async defaultValue() {
            return (await this.document).currency;
        },
        isFrozen: true,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<AccountsReceivableInvoiceLine, 'amountExcludingTax'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dependsOn: ['documentLineType'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue() {
            return 0;
        },
        async updatedValue() {
            return (await this.documentLineType) === 'taxLine'
                ? 0 // force zero for US sales tax lines
                : this.amountExcludingTax;
        },
        isFrozen: true,
    })
    readonly amountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableInvoiceLine, 'amountIncludingTax'>({
        isStoredOutput: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dependsOn: ['taxAmount', 'amountExcludingTax', 'documentLineType'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async defaultValue() {
            return (await this.documentLineType) === 'taxLine'
                ? 0 // force zero for US sales tax lines
                : (await this.taxAmount) + (await this.amountExcludingTax);
        },
        updatedValue: useDefaultValue,
    })
    readonly amountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableInvoiceLine, 'taxableAmount'>({
        isStoredOutput: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ taxes: ['taxableAmount'] }, 'documentLineType'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async updatedValue() {
            return (await this.documentLineType) === 'taxLine'
                ? 0 // force zero for US sales tax lines
                : this.taxes.sum(taxLine => taxLine.taxableAmount);
        },
    })
    readonly taxableAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableInvoiceLine, 'taxAmount'>({
        isStoredOutput: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ taxes: ['taxAmount'] }, 'documentLineType'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async defaultValue() {
            return (await this.documentLineType) === 'taxLine'
                ? 0 // force zero for US sales tax lines
                : this.taxes
                      .filter(async taxLine => !(await taxLine.isReverseCharge))
                      .sum(taxLine => taxLine.taxAmount);
        },
        updatedValue: useDefaultValue,
    })
    readonly taxAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableInvoiceLine, 'taxLineTaxAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        async control(cx, val) {
            if ((await this.documentLineType) === 'taxLine') await cx.error.if(val).is.zero();
        },
        dependsOn: ['documentLineType'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        isFrozen: true,
    })
    readonly taxLineTaxAmount: Promise<decimal>;

    @decorators.stringProperty<AccountsReceivableInvoiceLine, 'taxDetail'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
        isFrozen: true,
    })
    readonly taxDetail: Promise<string>;

    @decorators.decimalProperty<AccountsReceivableInvoiceLine, 'exemptAmount'>({
        isStoredOutput: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ taxes: ['exemptAmount'] }, 'documentLineType'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async updatedValue() {
            return (await this.documentLineType) === 'taxLine'
                ? 0 // force zero for US sales tax lines
                : this.taxes.sum(taxLine => taxLine.exemptAmount);
        },
    })
    readonly exemptAmount: Promise<decimal>;

    // only for the tax detail panel
    @decorators.decimalProperty<AccountsReceivableInvoiceLine, 'taxAmountAdjusted'>({
        isStoredOutput: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ taxes: ['taxAmountAdjusted'] }, 'documentLineType'],
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        async defaultValue() {
            return (await this.documentLineType) === 'taxLine'
                ? 0 // force zero for US sales tax lines
                : this.taxes
                      .filter(async taxLine => !(await taxLine.isReverseCharge))
                      .sum(taxLine => taxLine.taxAmountAdjusted);
        },
        updatedValue: useDefaultValue,
    })
    readonly taxAmountAdjusted: Promise<decimal>;

    // only for the tax detail panel
    @decorators.decimalProperty<AccountsReceivableInvoiceLine, 'netPriceIncludingTax'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: ['amountIncludingTax'],
        defaultValue() {
            return this.amountIncludingTax;
        },
        updatedValue: useDefaultValue,
        isFrozen: true,
    })
    readonly netPriceIncludingTax: Promise<decimal>;

    // only for the tax detail panel
    @decorators.decimalProperty<AccountsReceivableInvoiceLine, 'quantity'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        defaultValue: 1,
        isFrozen: true,
    })
    readonly quantity: Promise<decimal>;

    // @TODO DELETE AFTER REFACTORING SALES
    @decorators.decimalProperty<AccountsReceivableInvoiceLine, 'quantityInSalesUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInSalesUnit,
        getValue() {
            return this.quantity;
        },
    })
    readonly quantityInSalesUnit: Promise<decimal>;

    @decorators.stringProperty<AccountsReceivableInvoiceLine, 'description'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
        isFrozen: true,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<AccountsReceivableInvoiceLine, 'sourceDocumentNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        isFrozen: true,
    })
    readonly sourceDocumentNumber: Promise<string>;

    @decorators.enumProperty<AccountsReceivableInvoiceLine, 'sourceDocumentType'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.sourceDocumentTypeDataType,
        async getValue() {
            const origin = await (await this.document).origin;
            if (origin === 'invoice') {
                return 'salesInvoice';
            }
            if (origin === 'creditMemo') {
                return 'salesCreditMemo';
            }
            return null;
        },
    })
    readonly sourceDocumentType: Promise<xtremFinanceData.enums.SourceDocumentType | null>;

    @decorators.collectionProperty<AccountsReceivableInvoiceLine, 'attributesAndDimensions'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinance.nodes.AccountsReceivableInvoiceLineDimension,
        reverseReference: 'originLine',
    })
    readonly attributesAndDimensions: Collection<xtremFinance.nodes.AccountsReceivableInvoiceLineDimension>;

    @decorators.collectionProperty<AccountsReceivableInvoiceLine, 'accountingStagingLines'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinanceData.nodes.AccountsReceivableInvoiceLineStaging,
        reverseReference: 'accountsReceivableInvoiceLine',
        isFrozen: true,
    })
    readonly accountingStagingLines: Collection<xtremFinanceData.nodes.AccountsReceivableInvoiceLineStaging>;

    @decorators.collectionProperty<AccountsReceivableInvoiceLine, 'taxes'>({
        isVital: true,
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'document',
        dependsOn: ['currency'],
        node: () => xtremFinance.nodes.AccountsReceivableInvoiceLineTax,
        isFrozen: true,
    })
    readonly taxes: Collection<xtremFinance.nodes.AccountsReceivableInvoiceLineTax>;

    @decorators.jsonProperty<AccountsReceivableInvoiceLine, 'uiTaxes'>({
        excludedFromPayload: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['taxes'],
        async computeValue() {
            return xtremTax.functions.prepareTaxesToUiTaxes<xtremFinance.nodes.AccountsReceivableInvoiceLine>(
                this,
                String(await (await (await this.financialSite).legalCompany).taxEngine),
            );
        },
        async setValue(val: xtremTax.interfaces.UiTaxes | null) {
            if (this.$.status === NodeStatus.added) {
                await xtremTax.functions.updateTaxesFromUiTaxes(
                    val,
                    this,
                    String(await (await (await this.financialSite).legalCompany).taxEngine),
                );
            }
        },
    })
    readonly uiTaxes: Promise<xtremTax.interfaces.UiTaxes | null>;

    // FIXME: [RM] review when we might have more then one attribute and dimension line
    @decorators.jsonProperty<AccountsReceivableInvoiceLine, 'storedDimensions'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        async computeValue() {
            return (await this.attributesAndDimensions.length)
                ? (await (await this.attributesAndDimensions.elementAt(0)).storedDimensions) || {}
                : {};
        },
    })
    readonly storedDimensions: Promise<object | null>;

    // FIXME: [RM] review when we might have more then one attribute and dimension line
    @decorators.jsonProperty<AccountsReceivableInvoiceLine, 'storedAttributes'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        async computeValue() {
            return (await this.attributesAndDimensions.length)
                ? (await (await this.attributesAndDimensions.elementAt(0)).storedAttributes) ||
                      ({} as xtremMasterData.interfaces.StoredAttributes)
                : ({} as xtremMasterData.interfaces.StoredAttributes);
        },
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;
}
