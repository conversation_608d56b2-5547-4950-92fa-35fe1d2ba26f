import type { Collection, date, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, Node, NodeStatus } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinance from '../index';

@decorators.node<JournalEntryLine>({
    package: 'xtrem-finance',
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
})
export class JournalEntryLine extends Node {
    @decorators.referenceProperty<JournalEntryLine, 'journalEntry'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinance.nodes.JournalEntry,
        isFrozen: true,
    })
    readonly journalEntry: Reference<xtremFinance.nodes.JournalEntry>;

    @decorators.referenceProperty<JournalEntryLine, 'financialSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        filters: {
            control: {
                isFinance: true,
            },
        },
        isFrozen: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<JournalEntryLine, 'chartOfAccount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremStructure.nodes.ChartOfAccount,
        isFrozen: true,
    })
    readonly chartOfAccount: Reference<xtremStructure.nodes.ChartOfAccount>;

    @decorators.referenceProperty<JournalEntryLine, 'tax'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremTax.nodes.Tax,
        isNullable: true,
        async control(cx, val) {
            await xtremFinance.events.controls.JournalEntryLine.taxReferenceControls({
                line: this,
                tax: val,
                cx,
            });
        },
        isFrozen: true,
    })
    readonly tax: Reference<xtremTax.nodes.Tax | null>;

    @decorators.dateProperty<JournalEntryLine, 'taxDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        async control(cx, val) {
            await xtremFinance.events.controls.JournalEntryLine.taxDateControls({
                line: this,
                taxDate: val,
                cx,
            });
        },
        isFrozen: true,
    })
    readonly taxDate: Promise<date | null>;

    @decorators.decimalProperty<JournalEntryLine, 'taxRate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        async control() {
            // TODO: type is invalid (should be void!) + use a localized message
            return ((await this?.taxRate) >= 0) as unknown as void;
        },
        isFrozen: true,
    })
    readonly taxRate: Promise<decimal>;

    @decorators.decimalProperty<JournalEntryLine, 'deductibleTaxRate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        async control() {
            // TODO: type is invalid (should be void!) + use a localized message
            return ((await this?.taxRate) >= 0) as unknown as void;
        },
        isFrozen: true,
    })
    readonly deductibleTaxRate: Promise<decimal>;

    @decorators.stringProperty<JournalEntryLine, 'taxExternalReference'>({
        dependsOn: ['tax'],
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            const tax = await this.tax;
            if (!tax) {
                return '';
            }
            if (
                (await (await this.account).taxManagement) === 'excludingTax' ||
                (await (await this.account).taxManagement) === 'tax'
            ) {
                return tax.primaryExternalReference;
            }
            if ((await (await this.account).taxManagement) === 'reverseCharge') {
                return tax.secondaryExternalReference;
            }
            return '';
        },
        isFrozen: true,
    })
    readonly taxExternalReference: Promise<string>;

    @decorators.referenceProperty<JournalEntryLine, 'transactionCurrency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Currency,
        isFrozen: true,
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<JournalEntryLine, 'inquiryTransactionCurrency'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await (await (await this.financialSite).legalCompany).currency)._id !==
                (await this.transactionCurrency)._id
                ? this.transactionCurrency
                : null;
        },
    })
    readonly inquiryTransactionCurrency: Reference<xtremMasterData.nodes.Currency | null>;

    @decorators.referenceProperty<JournalEntryLine, 'companyCurrency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['financialSite'],
        async defaultValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
        isFrozen: true,
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<JournalEntryLine, 'companyFxRate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        isFrozen: true,
    })
    readonly companyFxRate: Promise<decimal>;

    @decorators.decimalProperty<JournalEntryLine, 'companyFxRateDivisor'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        defaultValue: 1,
        isFrozen: true,
    })
    readonly companyFxRateDivisor: Promise<decimal>;

    @decorators.referenceProperty<JournalEntryLine, 'financialSiteCurrency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['financialSite'],
        async defaultValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
        isFrozen: true,
    })
    readonly financialSiteCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<JournalEntryLine, 'financialSiteFxRate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        defaultValue: 1,
        isFrozen: true,
    })
    readonly financialSiteFxRate: Promise<decimal>;

    @decorators.decimalProperty<JournalEntryLine, 'financialSiteFxRateDivisor'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        defaultValue: 1,
        isFrozen: true,
    })
    readonly financialSiteFxRateDivisor: Promise<decimal>;

    @decorators.dateProperty<JournalEntryLine, 'fxRateDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
    })
    readonly fxRateDate: Promise<date>;

    @decorators.stringProperty<JournalEntryLine, 'rateDescription'>({
        isPublished: true,
        lookupAccess: true,
        async computeValue() {
            return (await this.companyFxRate) !== 1 && (await this.companyFxRateDivisor) === 1
                ? `1 ${await (await this.transactionCurrency).id} = ${await this.companyFxRate} ${await (
                      await this.companyCurrency
                  ).id}`
                : // if rate between transaction and company currency doesn't exist, use reverse rate from
                  // rate between company currency to transaction currency and round to 10 decimals
                  `1 ${await (await this.transactionCurrency).id} = ${
                      Math.round(((await this.companyFxRate) / (await this.companyFxRateDivisor)) * 10 ** 10) / 10 ** 10
                  } ${await (await this.companyCurrency).id}`;
        },
    })
    readonly rateDescription: Promise<string>;

    @decorators.referenceProperty<JournalEntryLine, 'account'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremFinanceData.nodes.Account,
        isFrozen: true,
    })
    readonly account: Reference<xtremFinanceData.nodes.Account>;

    @decorators.referenceProperty<JournalEntryLine, 'businessEntity'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: false,
        isNullable: true,
        node: () => xtremMasterData.nodes.BusinessEntity,
        async control(cx, value) {
            await xtremFinance.events.controls.JournalEntryLine.businessEntityReferenceControls({
                line: this,
                businessEntity: value,
                cx,
            });
        },
        isFrozen: true,
    })
    readonly businessEntity: Reference<xtremMasterData.nodes.BusinessEntity | null>;

    @decorators.enumProperty<JournalEntryLine, 'sign'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.signDataType,
        isFrozen: true,
    })
    readonly sign: Promise<xtremFinanceData.enums.Sign>;

    @decorators.integerProperty<JournalEntryLine, 'numericSign'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            return (await this.sign) === 'C' ? -1 : 1;
        },
    })
    readonly numericSign: Promise<integer>;

    @decorators.stringProperty<JournalEntryLine, 'blank'>({
        isPublished: true,
        lookupAccess: true,
        getValue() {
            return '';
        },
    })
    readonly blank: Promise<string>;

    @decorators.decimalProperty<JournalEntryLine, 'transactionAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        isFrozen: true,
    })
    readonly transactionAmount: Promise<decimal>;

    @decorators.decimalProperty<JournalEntryLine, 'transactionCredit'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        async getValue() {
            return (await this.sign) === 'C' ? this.transactionAmount : 0;
        },
    })
    readonly transactionCredit: Promise<decimal>;

    @decorators.decimalProperty<JournalEntryLine, 'transactionDebit'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        async getValue() {
            return (await this.sign) === 'D' ? this.transactionAmount : 0;
        },
    })
    readonly transactionDebit: Promise<decimal>;

    @decorators.decimalProperty<JournalEntryLine, 'signedTransactionAmount'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        async getValue() {
            return (await (await (await this.financialSite).legalCompany).currency)._id !==
                (await this.transactionCurrency)._id
                ? (await this.transactionAmount) * (await this.numericSign)
                : 0;
        },
    })
    readonly signedTransactionAmount: Promise<decimal>;

    @decorators.decimalProperty<JournalEntryLine, 'companyAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        isFrozen: true,
    })
    readonly companyAmount: Promise<decimal>;

    @decorators.decimalProperty<JournalEntryLine, 'companyCredit'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        async getValue() {
            return (await this.sign) === 'C' ? this.companyAmount : 0;
        },
    })
    readonly companyCredit: Promise<decimal>;

    @decorators.decimalProperty<JournalEntryLine, 'companyDebit'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        async getValue() {
            return (await this.sign) === 'D' ? this.companyAmount : 0;
        },
    })
    readonly companyDebit: Promise<decimal>;

    @decorators.decimalProperty<JournalEntryLine, 'financialSiteAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountInFinancialSiteCurrency,
        dependsOn: ['companyAmount'],
        defaultValue() {
            return this.companyAmount;
        },
        isFrozen: true,
    })
    readonly financialSiteAmount: Promise<decimal>;

    @decorators.decimalProperty<JournalEntryLine, 'financialSiteCredit'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountInFinancialSiteCurrency,
        async getValue() {
            return (await this.sign) === 'C' ? this.financialSiteAmount : 0;
        },
    })
    readonly financialSiteCredit: Promise<decimal>;

    @decorators.decimalProperty<JournalEntryLine, 'financialSiteDebit'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountInFinancialSiteCurrency,
        async getValue() {
            return (await this.sign) === 'D' ? this.financialSiteAmount : 0;
        },
    })
    readonly financialSiteDebit: Promise<decimal>;

    @decorators.stringProperty<JournalEntryLine, 'description'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
        isFrozen: true,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<JournalEntryLine, 'inquiryDescription'>({
        isPublished: true,
        lookupAccess: true,
        async computeValue() {
            return (await this.description) !== '' ? (await this.description).replaceAll(';', '') : 'N/A';
        },
    })
    readonly inquiryDescription: Promise<string>;

    @decorators.stringProperty<JournalEntryLine, 'commonReference'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
        isFrozen: true,
    })
    readonly commonReference: Promise<string>;

    @decorators.collectionProperty<JournalEntryLine, 'attributesAndDimensions'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinance.nodes.JournalEntryLineDimension,
        reverseReference: 'journalEntryLine',
    })
    readonly attributesAndDimensions: Collection<xtremFinance.nodes.JournalEntryLineDimension>;

    @decorators.dateProperty<JournalEntryLine, 'dueDate'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        async computeValue() {
            if ((await (await this.journalEntry).arInvoice) && (await this.businessEntity)) {
                return (await (await (await (await this.journalEntry).arInvoice)?.openItems?.at(0))?.dueDate) || null;
            }
            if ((await (await this.journalEntry).apInvoice) && (await this.businessEntity)) {
                return (await (await (await (await this.journalEntry).apInvoice)?.openItems?.at(0))?.dueDate) || null;
            }
            return null;
        },
    })
    readonly dueDate: Promise<date | null>;

    @decorators.dateProperty<JournalEntryLine, 'validationDate'>({
        isPublished: true,
        lookupAccess: true,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await this.$.createStamp).date;
        },
    })
    readonly validationDate: Promise<date | null>;

    @decorators.collectionProperty<JournalEntryLine, 'accountingStagingLines'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinance.nodes.JournalEntryLineStaging,
        reverseReference: 'journalEntryLine',
    })
    readonly accountingStagingLines: Collection<xtremFinance.nodes.JournalEntryLineStaging>;

    @decorators.referenceProperty<JournalEntryLine, 'financialSiteAttribute'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).financialSite
                : null;
        },
    })
    readonly financialSiteAttribute: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<JournalEntryLine, 'businessSiteAttribute'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).businessSite
                : null;
        },
    })
    readonly businessSiteAttribute: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<JournalEntryLine, 'stockSiteAttribute'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).stockSite
                : null;
        },
    })
    readonly stockSiteAttribute: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<JournalEntryLine, 'manufacturingSiteAttribute'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).manufacturingSite
                : null;
        },
    })
    readonly manufacturingSiteAttribute: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<JournalEntryLine, 'customerAttribute'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Customer,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).customer
                : null;
        },
    })
    readonly customerAttribute: Reference<xtremMasterData.nodes.Customer | null>;

    @decorators.referenceProperty<JournalEntryLine, 'supplierAttribute'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Supplier,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).supplier
                : null;
        },
    })
    readonly supplierAttribute: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.referenceProperty<JournalEntryLine, 'projectAttribute'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).project
                : null;
        },
        filters: {
            lookup: {
                attributeType: { id: { _eq: 'project' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly projectAttribute: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<JournalEntryLine, 'employeeAttribute'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).employee
                : null;
        },
        filters: {
            lookup: {
                attributeType: { id: { _eq: 'employee' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly employeeAttribute: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<JournalEntryLine, 'taskAttribute'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Attribute,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).task
                : null;
        },
        filters: {
            lookup: {
                attributeType: { id: { _eq: 'task' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly taskAttribute: Reference<xtremFinanceData.nodes.Attribute | null>;

    @decorators.referenceProperty<JournalEntryLine, 'itemAttribute'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Item,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).item
                : null;
        },
    })
    readonly itemAttribute: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension01'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension01
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType01' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension01: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension02'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension02
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType02' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension02: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension03'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension03
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType03' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension03: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension04'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension04
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType04' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension04: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension05'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension05
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType05' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension05: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension06'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension06
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType06' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension06: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension07'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension07
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType07' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension07: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension08'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension08
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType08' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension08: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension09'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension09
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType09' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension09: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension10'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension10
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType10' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension10: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension11'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension11
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType11' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension11: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension12'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension12
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType12' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension12: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension13'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension13
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType13' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension13: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension14'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension14
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType14' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension14: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension15'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension15
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType15' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension15: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension16'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension16
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType16' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension16: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension17'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension17
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType17' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension17: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension18'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension18
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType18' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension18: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension19'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension19
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType19' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension19: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'dimension20'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.Dimension,
        dependsOn: ['attributesAndDimensions'],
        async computeValue() {
            return (await this.attributesAndDimensions.length) > 0
                ? (await this.attributesAndDimensions.elementAt(0)).dimension20
                : null;
        },
        filters: {
            lookup: {
                dimensionType: { docProperty: { _eq: 'dimensionType20' }, isActive: true },
                isActive: true,
            },
        },
    })
    readonly dimension20: Reference<xtremFinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<JournalEntryLine, 'journalEntryTypeLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.JournalEntryTypeLine,
        isFrozen: true,
        async control(cx, val) {
            await xtremFinance.events.controls.JournalEntryLine.journalEntryTypeLineMandatoryOnNewDocuments({
                line: this,
                entryTypeLine: val,
                cx,
            });
        },
    })
    readonly journalEntryTypeLine: Reference<xtremFinanceData.nodes.JournalEntryTypeLine | null>;

    @decorators.referenceProperty<JournalEntryLine, 'contraJournalEntryLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinance.nodes.JournalEntryLine,
        async isFrozen() {
            return (
                ![NodeStatus.added, NodeStatus.unchanged].includes(this.$.status) &&
                !(await this.journalEntry).pCanNullifyContraJournalEntryLine
            );
        },
    })
    readonly contraJournalEntryLine: Reference<xtremFinance.nodes.JournalEntryLine | null>;

    @decorators.stringProperty<JournalEntryLine, 'contraAccount'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            const contraAccountLine = await this.contraJournalEntryLine;
            const businessEntityComposedDescription = await (
                await contraAccountLine?.businessEntity
            )?.composedDescription;
            if (businessEntityComposedDescription) {
                return businessEntityComposedDescription;
            }
            return (await (await contraAccountLine?.account)?.composedDescription) ?? '';
        },
    })
    readonly contraAccount: Promise<string>;

    @decorators.referenceProperty<JournalEntryLine, 'baseTax'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremTax.nodes.BaseTax,
        isNullable: true,
    })
    readonly baseTax: Reference<xtremTax.nodes.BaseTax | null>;

    @decorators.integerProperty<JournalEntryLine, 'datevContraAccountId'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        async getValue() {
            if ((await (await this.journalEntry).origin) === 'apInvoice') {
                return (
                    (await (await (await (await this.contraJournalEntryLine)?.businessEntity)?.supplier)?.datevId) ??
                    null
                );
            }
            if ((await (await this.journalEntry).origin) === 'arInvoice') {
                return (
                    (await (await (await (await this.contraJournalEntryLine)?.businessEntity)?.customer)?.datevId) ??
                    null
                );
            }

            return (await (await this.contraJournalEntryLine)?.account)?.datevId ?? null;
        },
    })
    readonly datevContraAccountId: Promise<integer | null>;

    @decorators.integerProperty<JournalEntryLine, 'datevPostingKey'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        async getValue() {
            if (await (await this.account).isAutomaticAccount) {
                if (await (await (await this.contraJournalEntryLine)?.account)?.isAutomaticAccount) {
                    return 40;
                }
                return null;
            }

            return (await this.tax)?.postingKey ?? null;
        },
    })
    readonly datevPostingKey: Promise<integer | null>;

    @decorators.decimalProperty<JournalEntryLine, 'datevTransactionAmount'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        async computeValue() {
            let amount = (await this.sign) === 'D' ? await this.transactionAmount : (await this.transactionAmount) * -1;
            if ((await (await this.account).taxManagement) === 'excludingTax') {
                amount += await (await this.journalEntry).lines
                    .filter(
                        async line =>
                            line._id !== this._id &&
                            (await line.baseTax) === (await this.baseTax) &&
                            ['tax', 'reverseCharge'].includes(await (await line.account).taxManagement),
                    )
                    .sum(async line =>
                        (await line.sign) === 'D' ? line.transactionAmount : (await line.transactionAmount) * -1,
                    );
            }

            return Math.abs(amount);
        },
    })
    readonly datevTransactionAmount: Promise<decimal | null>;

    @decorators.decimalProperty<JournalEntryLine, 'datevCompanyAmount'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        async computeValue() {
            let amount = (await this.sign) === 'D' ? await this.companyAmount : (await this.companyAmount) * -1;
            if ((await (await this.account).taxManagement) === 'excludingTax') {
                amount += await (await this.journalEntry).lines
                    .filter(
                        async line =>
                            line._id !== this._id &&
                            (await line.baseTax) === (await this.baseTax) &&
                            ['tax', 'reverseCharge'].includes(await (await line.account).taxManagement),
                    )
                    .sum(async line =>
                        (await line.sign) === 'D' ? line.companyAmount : (await line.companyAmount) * -1,
                    );
            }

            return Math.abs(amount);
        },
    })
    readonly datevCompanyAmount: Promise<decimal | null>;

    @decorators.stringProperty<JournalEntryLine, 'supplierDocumentNumber'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        async computeValue() {
            return (await (await this.journalEntry).origin) === 'apInvoice'
                ? ((
                      await this.$.context.tryRead(xtremFinance.nodes.AccountsPayableInvoice, {
                          number: await (await this.journalEntry).number,
                      })
                  )?.supplierDocumentNumber ?? '')
                : '';
        },
    })
    readonly supplierDocumentNumber: Promise<string>;

    @decorators.stringProperty<JournalEntryLine, 'datevBusinessEntityTaxIdNumber'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        async computeValue() {
            const businessEntity = await (await this.contraJournalEntryLine)?.businessEntity;

            return ['apInvoice', 'arInvoice'].includes(await (await this.journalEntry).origin) &&
                (await (
                    await businessEntity?.country
                )?.isEuMember)
                ? (await businessEntity?.taxIdNumber) || ''
                : '';
        },
    })
    readonly datevBusinessEntityTaxIdNumber: Promise<string>;

    @decorators.booleanProperty<JournalEntryLine, 'isBalanceLine'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        lookupAccess: true,
    })
    readonly isBalanceLine: Promise<boolean>;

    async isMandatoryByTaxManagement(): Promise<boolean> {
        return (
            (await (await this.account).taxManagement) === 'excludingTax' ||
            (await (await this.account).taxManagement) === 'reverseCharge' ||
            (await (await this.account).taxManagement) === 'tax'
        );
    }
}
