import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinance from '../../index';

@decorators.subNode<AccountsPayableInvoiceTax>({
    extends: () => xtremTax.nodes.BaseTax,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
})
export class AccountsPayableInvoiceTax
    extends xtremTax.nodes.BaseTax
    implements xtremMasterData.interfaces.FinancialSubNode
{
    @decorators.referenceProperty<AccountsPayableInvoiceTax, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremFinance.nodes.AccountsPayableInvoice,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly document: Reference<xtremFinance.nodes.AccountsPayableInvoice>;

    @decorators.referencePropertyOverride<AccountsPayableInvoiceTax, 'currency'>({
        dependsOn: [{ document: ['currency'] }],
        async getValue() {
            return (await this.document).currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;
}
