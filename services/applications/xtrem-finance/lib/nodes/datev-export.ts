import type { Collection, Context, NodeCreateData, Reference, date, dateRange, integer } from '@sage/xtrem-core';
import { Logger, Node, datetime, decorators } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinance from '../index';

const logger = Logger.getLogger(__filename, 'datev-export');

@decorators.node<DatevExport>({
    storage: 'sql',
    isPublished: true,
    isClearedByReset: true,
    serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canCreate: true,
    canExport: true,
    indexes: [{ orderBy: { id: +1 }, isUnique: true, isNaturalKey: true }],
})
export class DatevExport extends Node {
    @decorators.stringProperty<DatevExport, 'id'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.id,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.referenceProperty<DatevExport, 'company'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.Company,
        filters: {
            control: {
                legislation: { id: 'DE' },
            },
        },
    })
    readonly company: Reference<xtremSystem.nodes.Company>;

    @decorators.enumProperty<DatevExport, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremFinance.enums.datevExportStatusDataType,
        defaultValue: 'draft',
    })
    readonly status: Promise<xtremFinance.enums.DatevExportStatus>;

    @decorators.datetimeProperty<DatevExport, 'timeStamp'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly timeStamp: Promise<datetime | null>;

    @decorators.integerProperty<DatevExport, 'datevConsultantNumber'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['company'],
        async defaultValue() {
            return (await this.company).datevConsultantNumber ?? 1;
        },
    })
    readonly datevConsultantNumber: Promise<integer>;

    @decorators.integerProperty<DatevExport, 'datevCustomerNumber'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['company'],
        async defaultValue() {
            return (await this.company).datevCustomerNumber ?? 1;
        },
    })
    readonly datevCustomerNumber: Promise<integer>;

    @decorators.integerProperty<DatevExport, 'skrCoa'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        async defaultValue() {
            return (await xtremFinanceData.functions.datev.getDatevConfiguration(this.$.context)).skrCoa ?? 3;
        },
    })
    readonly skrCoa: Promise<integer>;

    @decorators.dateRangeProperty<DatevExport, 'dateRange'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
    })
    readonly dateRange: Promise<dateRange>;

    @decorators.dateProperty<DatevExport, 'startDate'>({
        isPublished: true,
        dependsOn: ['dateRange'],
        isNullable: true,
        async computeValue() {
            return (await this.dateRange).excludesStart
                ? ((await this.dateRange).start?.addDays(1) ?? null)
                : (await this.dateRange).start;
        },
        lookupAccess: true,
    })
    readonly startDate: Promise<date | null>;

    @decorators.dateProperty<DatevExport, 'endDate'>({
        isPublished: true,
        dependsOn: ['dateRange'],
        isNullable: true,
        async computeValue() {
            return (await this.dateRange).excludesEnd
                ? ((await this.dateRange).end?.addDays(-1) ?? null)
                : (await this.dateRange).end;
        },
        lookupAccess: true,
    })
    readonly endDate: Promise<date | null>;

    @decorators.dateProperty<DatevExport, 'fiscalYearStart'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
    })
    readonly fiscalYearStart: Promise<date>;

    @decorators.booleanProperty<DatevExport, 'isLocked'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: true,
        lookupAccess: true,
    })
    readonly isLocked: Promise<boolean>;

    @decorators.booleanProperty<DatevExport, 'doAccounts'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly doAccounts: Promise<boolean>;

    @decorators.booleanProperty<DatevExport, 'doCustomersSuppliers'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly doCustomersSuppliers: Promise<boolean>;

    @decorators.booleanProperty<DatevExport, 'doJournalEntries'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        lookupAccess: true,
    })
    readonly doJournalEntries: Promise<boolean>;

    @decorators.referenceProperty<DatevExport, 'dimensionType1'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.DimensionType,
    })
    readonly dimensionType1: Promise<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<DatevExport, 'attributeType1'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.AttributeType,
    })
    readonly attributeType1: Promise<xtremFinanceData.nodes.AttributeType | null>;

    @decorators.referenceProperty<DatevExport, 'dimensionType2'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.DimensionType,
    })
    readonly dimensionType2: Promise<xtremFinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<DatevExport, 'attributeType2'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.AttributeType,
    })
    readonly attributeType2: Promise<xtremFinanceData.nodes.AttributeType | null>;

    @decorators.collectionProperty<DatevExport, 'datevExportAccounts'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinance.nodes.DatevExportAccount,
        reverseReference: 'datevExport',
    })
    readonly datevExportAccounts: Collection<xtremFinance.nodes.DatevExportAccount>;

    @decorators.collectionProperty<DatevExport, 'datevExportBusinessRelations'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinance.nodes.DatevExportBusinessRelation,
        reverseReference: 'datevExport',
    })
    readonly datevExportBusinessRelations: Collection<xtremFinance.nodes.DatevExportBusinessRelation>;

    @decorators.collectionProperty<DatevExport, 'datevExportJournalEntryLines'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinance.nodes.DatevExportJournalEntryLine,
        reverseReference: 'datevExport',
    })
    readonly datevExportJournalEntryLines: Collection<xtremFinance.nodes.DatevExportJournalEntryLine>;

    @decorators.collectionProperty<DatevExport, 'accountsWithoutDatevId'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremFinanceData.nodes.Account,
        getFilter() {
            return { chartOfAccount: { legislation: { id: 'DE' } }, isActive: true, datevId: null };
        },
    })
    readonly accountsWithoutDatevId: Collection<xtremFinanceData.nodes.Account>;

    @decorators.collectionProperty<DatevExport, 'customersWithoutDatevId'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Customer,
        getFilter() {
            return { isActive: true, datevId: null };
        },
    })
    readonly customersWithoutDatevId: Collection<xtremMasterData.nodes.Customer>;

    @decorators.collectionProperty<DatevExport, 'suppliersWithoutDatevId'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Supplier,
        getFilter() {
            return { isActive: true, datevId: null };
        },
    })
    readonly suppliersWithoutDatevId: Collection<xtremMasterData.nodes.Supplier>;

    // async mutation to do the DATEV extraction for accounts and/or customers and suppliers and/or journal entries
    @decorators.asyncMutation<typeof DatevExport, 'datevExtraction'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [{ name: 'id', type: 'string' }],
        return: 'boolean',
    })
    static async datevExtraction(context: Context, id: string): Promise<boolean> {
        await context.batch.logMessage(
            'info',
            context.localize('@sage/xtrem-finance/nodes__datev_export__start_extraction', 'DATEV extraction started.'),
        );
        logger.verbose(() => `datevExtraction - notification received \n ${id}`);

        if (await xtremFinance.functions.stopRequested(context, id)) {
            return false;
        }

        await context.runInIsolatedContext(async isolatedContext => {
            // Remove all existing extracted accounts, customers and suppliers and journal entries, set timeStamp
            await xtremFinance.functions.datevExportRecordUpdate({
                context: isolatedContext,
                datevExportId: id,
                data: {
                    datevExportAccounts: [],
                    datevExportBusinessRelations: [],
                    timeStamp: datetime.now(),
                    status: 'extractionInProgress',
                },
            });
        });

        if (await xtremFinance.functions.stopRequested(context, id)) {
            return false;
        }

        // Do extraction
        const datevExport = await context.read(xtremFinance.nodes.DatevExport, { id });
        try {
            const datevExportAccounts: NodeCreateData<xtremFinance.nodes.DatevExportAccount>[] =
                await xtremFinance.functions.executeAccountExtraction(context, datevExport);
            if (await xtremFinance.functions.stopRequested(context, id)) {
                return false;
            }
            const datevExportBusinessRelations: NodeCreateData<xtremFinance.nodes.DatevExportBusinessRelation>[] =
                await xtremFinance.functions.executeBusinessRelationExtraction(context, datevExport);
            if (await xtremFinance.functions.stopRequested(context, id)) {
                return false;
            }
            const datevExportJournalEntryLines: NodeCreateData<xtremFinance.nodes.DatevExportJournalEntryLine>[] =
                await xtremFinance.functions.executeJournalEntryLineExtraction(context, datevExport);
            if (await xtremFinance.functions.stopRequested(context, id)) {
                return false;
            }
            await context.runInIsolatedContext(async isolatedContext => {
                await xtremFinance.functions.datevExportRecordUpdate({
                    context: isolatedContext,
                    datevExportId: id,
                    data: {
                        datevExportAccounts,
                        datevExportBusinessRelations,
                        datevExportJournalEntryLines,
                        status: 'extracted',
                        timeStamp: datetime.now(),
                    },
                });
            });
        } catch (error) {
            await context.runInIsolatedContext(async newContext => {
                await xtremFinance.functions.datevExportRecordUpdate({
                    context: newContext,
                    datevExportId: id,
                    data: { status: 'error', timeStamp: datetime.now() },
                });
            });
            await context.batch.logMessage(
                'error',
                context.localize(
                    '@sage/xtrem-finance/nodes__datev_export__failure_description',
                    'DATEV extraction failed to complete successfully. Error: {{error}}',
                    { error: error.message },
                ),
            );
            logger.error(() => `DatevExtraction - error ${error.message} - ${error.stack}`);
            await xtremFinance.functions.sendDatevUserNotification({
                context,
                datevExportId: datevExport._id.toString(),
                success: false,
                msgDescription: context.localize(
                    '@sage/xtrem-finance/nodes__datev_export__failure_description',
                    'DATEV extraction failed to complete successfully. Error: {{error}}',
                    { error: error.message },
                ),
            });
            throw error;
        }

        if (await xtremFinance.functions.stopRequested(context, id)) {
            return false;
        }

        await xtremFinance.functions.sendDatevUserNotification({
            context,
            datevExportId: datevExport._id.toString(),
            success: true,
            msgDescription: context.localize(
                '@sage/xtrem-finance/nodes__datev_export__success_description',
                'DATEV extraction completed successfully.',
            ),
        });

        await context.batch.logMessage(
            'info',
            context.localize('@sage/xtrem-finance/nodes__datev_export__end_extraction', 'DATEV extraction completed.'),
        );
        logger.verbose(() => `datevExtraction - completed \n ${id}`);

        await context.batch.updateProgress({
            detail: 'complete',
            errorCount: 0,
            successCount: 1,
            totalCount: 1,
            phase: 'done',
        });

        return true;
    }

    // async mutation to do the DATEV export for accounts and/or customers and suppliers and/or journal entries
    @decorators.asyncMutation<typeof DatevExport, 'datevExport'>({
        isPublished: true,
        parameters: [{ name: 'id', type: 'string' }],
        return: { type: 'boolean' },
    })
    static async datevExport(context: Context, id: string): Promise<boolean> {
        await context.batch.logMessage(
            'info',
            context.localize('@sage/xtrem-finance/nodes__datev_export__start_export', 'DATEV export started.'),
        );
        logger.verbose(() => `datevExport - notification received \n ${id}`);

        if (await xtremFinance.functions.stopRequested(context, id)) {
            return false;
        }

        await context.runInIsolatedContext(async isolatedContext => {
            await xtremFinance.functions.datevExportRecordUpdate({
                context: isolatedContext,
                datevExportId: id,
                data: { status: 'exportInProgress' },
            });
        });

        const datevExport = await context.read(xtremFinance.nodes.DatevExport, { id });
        try {
            await xtremFinance.functions.executeDatevExport(context, datevExport);

            if (await xtremFinance.functions.stopRequested(context, id)) {
                return false;
            }
            await context.runInIsolatedContext(async isolatedContext => {
                await xtremFinance.functions.datevExportRecordUpdate({
                    context: isolatedContext,
                    datevExportId: id,
                    data: {
                        status: 'exported',
                        timeStamp: datetime.now(),
                    },
                });
            });
        } catch (error) {
            await context.runInIsolatedContext(async newContext => {
                await xtremFinance.functions.datevExportRecordUpdate({
                    context: newContext,
                    datevExportId: id,
                    data: { status: 'error', timeStamp: datetime.now() },
                });
            });
            await context.batch.logMessage(
                'error',
                context.localize(
                    '@sage/xtrem-finance/nodes__datev_export__failure_description_export',
                    'DATEV export failed. Error: {{error}}',
                    { error: error.message },
                ),
            );
            logger.error(() => `DatevExport - error ${error.message} - ${error.stack}`);
            await xtremFinance.functions.sendDatevUserNotification({
                context,
                datevExportId: datevExport._id.toString(),
                success: false,
                msgDescription: context.localize(
                    '@sage/xtrem-finance/nodes__datev_export__failure_description_export',
                    'DATEV export failed. Error: {{error}}',
                    { error: error.message },
                ),
            });
            throw error;
        }

        if (await xtremFinance.functions.stopRequested(context, id)) {
            return false;
        }
        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-finance/nodes__datev_export__complete_export',
                'DATEV export completed successfully.',
            ),
        );
        logger.verbose(() => 'DATEV export completed successfully.');

        await context.batch.updateProgress({
            detail: 'complete',
            errorCount: 0,
            successCount: 1,
            totalCount: 1,
            phase: 'done',
        });

        return true;
    }
}
