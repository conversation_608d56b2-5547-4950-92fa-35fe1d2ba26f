import type { Reference, integer } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinance from '../index';

@decorators.node<DatevExportBusinessRelation>({
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
})
export class DatevExportBusinessRelation extends Node {
    @decorators.referenceProperty<DatevExportBusinessRelation, 'datevExport'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinance.nodes.DatevExport,
        isFrozen: true,
    })
    readonly datevExport: Reference<xtremFinance.nodes.DatevExport>;

    @decorators.referenceProperty<DatevExportBusinessRelation, 'businessRelation'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.BaseBusinessRelation,
    })
    readonly businessRelation: Reference<xtremMasterData.nodes.BaseBusinessRelation>;

    @decorators.integerProperty<DatevExportBusinessRelation, 'datevId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly datevId: Promise<integer>;

    @decorators.stringProperty<DatevExportBusinessRelation, 'name'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<DatevExportBusinessRelation, 'taxIdNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremStructure.dataTypes.taxIdentificationDataType,
        lookupAccess: true,
    })
    readonly taxIdNumber: Promise<string>;

    @decorators.referenceProperty<DatevExportBusinessRelation, 'country'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremStructure.nodes.Country,
        lookupAccess: true,
    })
    readonly country: Reference<xtremStructure.nodes.Country>;

    @decorators.stringProperty<DatevExportBusinessRelation, 'street'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.addressLineDataType,
        lookupAccess: true,
    })
    readonly street: Promise<string>;

    @decorators.stringProperty<DatevExportBusinessRelation, 'postcode'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.postcodeDataType,
        lookupAccess: true,
    })
    readonly postcode: Promise<string>;

    @decorators.stringProperty<DatevExportBusinessRelation, 'city'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.cityDataType,
        lookupAccess: true,
    })
    readonly city: Promise<string>;
}
