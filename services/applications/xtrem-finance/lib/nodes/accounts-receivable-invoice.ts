import type {
    Collection,
    Context,
    decimal,
    integer,
    NodeCreateData,
    Reference,
    ValidationContext,
} from '@sage/xtrem-core';
import { BusinessRuleError, date, decorators, Logger, Node, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import { noop } from 'lodash';
import * as xtremFinance from '../index';

const logger = Logger.getLogger(__filename, 'accounts-receivable-invoice');

@decorators.node<AccountsReceivableInvoice>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    indexes: [
        {
            orderBy: { number: 1, type: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    notifies: ['created', 'updated', 'deleted'],
    async controlEnd(cx) {
        await xtremFinance.functions.controlMandatoryAttributesAndDimensions(this.$.context, cx, this);
    },
    async saveBegin() {
        if (
            this.$.status === NodeStatus.modified &&
            !this.pForceUpdateForFinance &&
            ['posted', 'inProgress', 'error'].includes(await (await this.$.old).postingStatus) &&
            !this.pCanUpdateFromExternalIntegration &&
            !this.canUpdateIsPrinted &&
            (await this.internalFinanceIntegrationStatus) !== 'failed'
        ) {
            throw new BusinessRuleError(
                this.$.context.localize(
                    '@sage/xtrem-finance/nodes__accounts_receivable_invoice__update_not_allowed_status_posted',
                    'The accounts receivable invoice is posted. You cannot update it.',
                ),
            );
        }
    },
})
export class AccountsReceivableInvoice extends Node implements xtremFinanceData.interfaces.ArInvoice {
    // To allow the update of the printing status, through the setIsPrintedStatus method, when the ar invoice is posted
    protected canUpdateIsPrinted = false;

    // To allow update on intacct gateway properties
    public pCanUpdateFromExternalIntegration = false;

    // To inform that a reply to the original document was sent from the intacct integration so we don't sent it from the accounting engine and don't mix the document status
    public pHasReplyBeenSentFromExternalIntegration = false;

    // To temporarily store an error message from the intacct integration
    public pExternalIntegrationMessage = '';

    // To allow update on finance integration reply
    protected pForceUpdateForFinance = false;

    // To flag if we are on the posting action, used on the extensions and to decide about the correct ocument status
    public pIsPosting = false;

    @decorators.referenceProperty<AccountsReceivableInvoice, 'financialSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
        filters: { control: { isFinance: true } },
        isFrozen: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.enumProperty<AccountsReceivableInvoice, 'taxEngine'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.taxEngineDataType,
        async getValue() {
            return (await (await this.financialSite).legalCompany).taxEngine;
        },
    })
    readonly taxEngine: Promise<xtremFinanceData.enums.TaxEngine | null>;

    @decorators.stringProperty<AccountsReceivableInvoice, 'financialSiteName'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNotEmpty: true,
        isRequired: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => xtremSystem.dataTypes.name,
        dependsOn: ['financialSite'],
        async defaultValue() {
            return (await this.financialSite).name;
        },
        isFrozen: true,
    })
    readonly financialSiteName: Promise<string>;

    @decorators.stringProperty<AccountsReceivableInvoice, 'financialSiteTaxIdNumber'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        isRequired: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'custom',
        anonymizeValue(value) {
            return xtremStructure.functions.anonymizeTaxId(value);
        },
        dataType: () => xtremStructure.dataTypes.taxIdentificationDataType,
        dependsOn: ['financialSite'],
        async defaultValue() {
            return (await (await this.financialSite).businessEntity).taxIdNumber;
        },
        isFrozen: true,
    })
    readonly financialSiteTaxIdNumber: Promise<string>;

    // @decorators.referenceProperty<AccountsReceivableInvoice, 'financialSiteAddress'>({
    //     isStored: true,
    //     isPublished: true,
    //     isNullable: true,
    //     node: () => xtremMasterData.nodes.BusinessEntityAddress,

    //     defaultValue() {
    //         return (
    //             // eslint-disable-next-line @sage/xtrem/property-decorators-errors
    //             this.financialSite?.addresses.find(
    //                 (address: xtremMasterData.nodes.BusinessEntityAddress) => address.isPrimary,
    //             ) || null!
    //         );
    //     },
    // })
    // financialSiteAddress: xtremMasterData.nodes.BusinessEntityAddress | null;

    // @decorators.referenceProperty<AccountsReceivableInvoice, 'financialSiteContact'>({
    //     isStored: true,
    //     isPublished: true,
    //     isNullable: true,
    //     filters: {
    //         control: {
    //             isPrimary: true,
    //         },
    //     },
    //     node: () => xtremMasterData.nodes.BusinessEntityContact,
    //     defaultValue() {
    //         return (
    //             this.financialSiteAddress?.contacts.find(
    //                 (contact: xtremMasterData.nodes.BusinessEntityContact) => contact.isPrimary,
    //             ) || null
    //         );
    //     },
    // })
    // financialSiteContact: xtremMasterData.nodes.BusinessEntityContact | null;

    @decorators.enumProperty<AccountsReceivableInvoice, 'type'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.financeDocumentTypeDataType,
        isFrozen: true,
        async control(cx) {
            if ((await this.type) !== 'salesInvoice' && (await this.type) !== 'salesCreditMemo') {
                cx.error.addLocalized(
                    '@sage/xtrem-finance/nodes__accounts_receivable_invoice__type_invalid',
                    "The accounts receivable invoice type must be 'Sales invoice' or 'Sales credit memo'.",
                );
            }
        },
    })
    readonly type: Promise<xtremFinanceData.enums.FinanceDocumentType>;

    @decorators.stringProperty<AccountsReceivableInvoice, 'number'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly number: Promise<string>;

    @decorators.dateProperty<AccountsReceivableInvoice, 'invoiceDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        defaultValue: () => date.today(),
        isFrozen: true,
    })
    readonly invoiceDate: Promise<date>;

    @decorators.dateProperty<AccountsReceivableInvoice, 'postingDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
    })
    readonly postingDate: Promise<date>;

    @decorators.referenceProperty<AccountsReceivableInvoice, 'billToCustomer'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Customer,
        isFrozen: true,
    })
    readonly billToCustomer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.stringProperty<AccountsReceivableInvoice, 'billToCustomerName'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNotEmpty: true,
        isRequired: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => xtremSystem.dataTypes.name,
        dependsOn: ['billToCustomer'],
        async defaultValue() {
            return (await (await this.billToCustomer).businessEntity).name;
        },
        isFrozen: true,
    })
    readonly billToCustomerName: Promise<string>;

    @decorators.stringProperty<AccountsReceivableInvoice, 'billToCustomerTaxIdNumber'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'custom',
        anonymizeValue(value) {
            return xtremStructure.functions.anonymizeTaxId(value);
        },
        dataType: () => xtremStructure.dataTypes.taxIdentificationDataType,
        dependsOn: ['billToCustomer'],
        async defaultValue() {
            return (await (await this.billToCustomer).businessEntity).taxIdNumber;
        },
        isFrozen: true,
    })
    readonly billToCustomerTaxIdNumber: Promise<string>;

    @decorators.referenceProperty<AccountsReceivableInvoice, 'currency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['billToCustomer'],
        async defaultValue() {
            return (await (await this.billToCustomer).businessEntity)?.currency;
        },
        isFrozen: true,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<AccountsReceivableInvoice, 'paymentTerm'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.PaymentTerm,
        dependsOn: ['billToCustomer'],
        async defaultValue() {
            return (await this.billToCustomer)?.paymentTerm;
        },
        isFrozen() {
            return !this.pCanUpdateFromExternalIntegration;
        },
    })
    readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm>;

    @decorators.decimalProperty<AccountsReceivableInvoice, 'totalAmountExcludingTax'>({
        isPublished: true,
        lookupAccess: true,
        isStoredOutput: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: [{ lines: ['amountExcludingTax'] }],
        defaultValue() {
            return this.lines.sum(line => line.amountExcludingTax);
        },
        updatedValue: useDefaultValue,
    })
    readonly totalAmountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableInvoice, 'totalAmountIncludingTax'>({
        isPublished: true,
        lookupAccess: true,
        isStoredOutput: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['totalAmountExcludingTax', 'totalTaxAmount'],
        async defaultValue() {
            return (await this.totalAmountExcludingTax) + (await this.totalTaxAmount);
        },
        updatedValue: useDefaultValue,
    })
    readonly totalAmountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableInvoice, 'totalTaxAmount'>({
        isStoredOutput: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ lines: ['taxAmount'] }],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        defaultValue() {
            return this.lines.sum(line => line.taxAmount);
        },
        updatedValue: useDefaultValue,
    })
    readonly totalTaxAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableInvoice, 'totalTaxableAmount'>({
        isStoredOutput: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ lines: ['taxableAmount'] }],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        defaultValue() {
            return this.lines.sum(line => line.taxableAmount);
        },
        updatedValue: useDefaultValue,
    })
    readonly totalTaxableAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableInvoice, 'totalExemptAmount'>({
        isStoredOutput: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ lines: ['exemptAmount'] }],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        defaultValue() {
            return this.lines.sum(line => line.exemptAmount);
        },
        updatedValue: useDefaultValue,
    })
    readonly totalExemptAmount: Promise<decimal>;

    @decorators.enumProperty<AccountsReceivableInvoice, 'postingStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.journalStatusDataType,
        defaultValue() {
            return 'draft';
        },
        isFrozen() {
            return !this.pIsPosting && !this.pCanUpdateFromExternalIntegration && !this.pForceUpdateForFinance;
        },
    })
    readonly postingStatus: Promise<xtremFinanceData.enums.JournalStatus>;

    @decorators.enumProperty<AccountsReceivableInvoice, 'paymentStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.openItemStatusDataType,
        defaultValue: () => 'notPaid',
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    })
    readonly paymentStatus: Promise<xtremFinanceData.enums.OpenItemStatus | null>;

    @decorators.booleanProperty<AccountsReceivableInvoice, 'isPrinted'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue() {
            return false;
        },
        isFrozen() {
            return !this.canUpdateIsPrinted;
        },
    })
    readonly isPrinted: Promise<boolean>;

    @decorators.enumProperty<AccountsReceivableInvoice, 'origin'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremFinanceData.enums.accountsPayableReceivableInvoiceOriginDataType,
    })
    readonly origin: Promise<xtremFinanceData.enums.AccountsPayableReceivableInvoiceOrigin>;

    @decorators.stringProperty<AccountsReceivableInvoice, 'reference'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly reference: Promise<string>;

    @decorators.stringProperty<AccountsReceivableInvoice, 'description'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<AccountsReceivableInvoice, 'account'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        node: () => xtremFinanceData.nodes.Account,
    })
    readonly account: Reference<xtremFinanceData.nodes.Account>;

    @decorators.dateProperty<AccountsReceivableInvoice, 'dueDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dependsOn: ['paymentTerm', 'invoiceDate'],
        async defaultValue() {
            const paymentTerm = await this.paymentTerm;
            return xtremMasterData.sharedFunctions.getDueDate({
                dueDateType: await paymentTerm.dueDateType,
                days: (await paymentTerm.days) ?? 0,
                baseDate: await this.invoiceDate,
            });
        },
        isFrozen() {
            return !this.pCanUpdateFromExternalIntegration;
        },
    })
    readonly dueDate: Promise<date>;

    // only for tax details panel
    @decorators.enumProperty<AccountsReceivableInvoice, 'taxCalculationStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.enums.taxCalculationStatusDataType,
        defaultValue: () => 'notDone',
        isFrozen: true,
    })
    readonly taxCalculationStatus: Promise<xtremMasterData.enums.TaxCalculationStatus>;

    // only for tax details panel
    @decorators.decimalProperty<AccountsReceivableInvoice, 'totalTaxAmountAdjusted'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        isFrozen: true,
    })
    readonly totalTaxAmountAdjusted: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableInvoice, 'companyFxRate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'financialSite', 'fxRateDate'],
        async defaultValue() {
            return (
                await xtremMasterData.functions.getRateOrReverseRate(
                    this.$.context,
                    await this.currency,
                    await (
                        await (
                            await this.financialSite
                        ).legalCompany
                    ).currency,
                    await this.fxRateDate,
                )
            ).rate;
        },
        isFrozen: true,
    })
    readonly companyFxRate: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableInvoice, 'companyFxRateDivisor'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'financialSite', 'fxRateDate'],
        async defaultValue() {
            return (
                await xtremMasterData.functions.getRateOrReverseRate(
                    this.$.context,
                    await this.currency,
                    await (
                        await (
                            await this.financialSite
                        ).legalCompany
                    ).currency,
                    await this.fxRateDate,
                )
            ).divisor;
        },
        isFrozen: true,
    })
    readonly companyFxRateDivisor: Promise<decimal>;

    @decorators.dateProperty<AccountsReceivableInvoice, 'fxRateDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['invoiceDate'],
        defaultValue() {
            return this.invoiceDate;
        },
        isFrozen: true,
    })
    readonly fxRateDate: Promise<date>;

    @decorators.decimalProperty<AccountsReceivableInvoice, 'totalCompanyAmountExcludingTax'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            // convert totalAmountExcludingTax into company currency
            return (await this.companyFxRate) !== 1 || (await this.companyFxRateDivisor) !== 1
                ? xtremMasterData.sharedFunctions.convertAmount(
                      await this.totalAmountExcludingTax,
                      await this.companyFxRate,
                      await this.companyFxRateDivisor,
                      await (
                          await this.currency
                      ).decimalDigits,
                      await (
                          await (
                              await (
                                  await this.financialSite
                              ).legalCompany
                          ).currency
                      ).decimalDigits,
                  )
                : this.totalAmountExcludingTax;
        },
    })
    readonly totalCompanyAmountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableInvoice, 'totalCompanyAmountIncludingTax'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return (await this.totalCompanyAmountExcludingTax) + (await this.totalCompanyTaxAmount);
        },
        isFrozen: true,
    })
    readonly totalCompanyAmountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableInvoice, 'totalCompanyTaxAmount'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async getValue() {
            // convert totalTaxAmount into company currency
            return (await this.companyFxRate) !== 1 || (await this.companyFxRateDivisor) !== 1
                ? xtremMasterData.sharedFunctions.convertAmount(
                      await this.totalTaxAmount,
                      await this.companyFxRate,
                      await this.companyFxRateDivisor,
                      await (
                          await this.currency
                      ).decimalDigits,
                      await (
                          await (
                              await (
                                  await this.financialSite
                              ).legalCompany
                          ).currency
                      ).decimalDigits,
                  )
                : this.totalTaxAmount;
        },
    })
    readonly totalCompanyTaxAmount: Promise<decimal>;

    @decorators.stringProperty<AccountsReceivableInvoice, 'rateDescription'>({
        isPublished: true,
        lookupAccess: true,
        async computeValue() {
            const financialCurrency = await (await (await this.financialSite).legalCompany).currency;
            const currentCurrency = await this.currency;
            const companyFxRate = await this.companyFxRate;
            const companyFxRateDivisor = await this.companyFxRateDivisor;
            /**  if rate between transaction and company currency doesn't exist, use reverse rate from
                rate between company currency to transaction currency and round to 10 decimals */
            return companyFxRate !== 1 && companyFxRateDivisor === 1
                ? `1 ${await currentCurrency.id} = ${companyFxRate} ${await financialCurrency.id}`
                : `1 ${await currentCurrency.id} = ${
                      Math.round((companyFxRate / companyFxRateDivisor) * 10 ** 10) / 10 ** 10
                  } ${await financialCurrency.id}`;
        },
    })
    readonly rateDescription: Promise<string>;

    @decorators.stringProperty<AccountsReceivableInvoice, 'salesDocumentNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly salesDocumentNumber: Promise<string>;

    @decorators.integerProperty<AccountsReceivableInvoice, 'salesDocumentSysId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
    })
    readonly salesDocumentSysId: Promise<integer>;

    @decorators.enumProperty<AccountsReceivableInvoice, 'financeIntegrationApp'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationAppDataType,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationApp(
                this.$.context,
                await this.number,
                'accountsReceivableInvoice',
            );
        },
    })
    readonly financeIntegrationApp: Promise<xtremFinanceData.enums.FinanceIntegrationApp | null>;

    @decorators.enumProperty<AccountsReceivableInvoice, 'financeIntegrationStatus'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.postingStatusDataType,
        async computeValue() {
            return xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(this.$.context, {
                financeIntegrationStatus: await this.internalFinanceIntegrationStatus,
                externalIntegration: (await this.financeIntegrationApp) !== null,
                financialSite: await this.financialSite,
                documentType:
                    (await (
                        await xtremFinanceData.functions.getFinanceTransaction(
                            this.$.context,
                            await this.number,
                            'accountsReceivableInvoice',
                        )
                    )?.documentType) || 'salesInvoice',
                targetDocumentType: 'accountsReceivableInvoice',
            });
        },
    })
    readonly financeIntegrationStatus: Promise<xtremFinanceData.enums.PostingStatus>;

    @decorators.enumProperty<AccountsReceivableInvoice, 'internalFinanceIntegrationStatus'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
        async computeValue() {
            return (
                (await (
                    await xtremFinanceData.functions.getFinanceTransaction(
                        this.$.context,
                        await this.number,
                        'accountsReceivableInvoice',
                    )
                )?.status) || 'recorded'
            );
        },
    })
    readonly internalFinanceIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.stringProperty<AccountsReceivableInvoice, 'financeIntegrationAppRecordId'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationAppRecordId(
                this.$.context,
                await this.number,
                'accountsReceivableInvoice',
            );
        },
    })
    readonly financeIntegrationAppRecordId: Promise<string>;

    @decorators.stringProperty<AccountsReceivableInvoice, 'financeIntegrationAppUrl'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.url,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationAppUrl(
                this.$.context,
                await this.number,
                'accountsReceivableInvoice',
            );
        },
    })
    readonly financeIntegrationAppUrl: Promise<string>;

    @decorators.collectionProperty<AccountsReceivableInvoice, 'lines'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinance.nodes.AccountsReceivableInvoiceLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<xtremFinance.nodes.AccountsReceivableInvoiceLine>;

    @decorators.collectionProperty<AccountsReceivableInvoice, 'openItems'>({
        node: () => xtremFinance.nodes.AccountsReceivableOpenItem,
        reverseReference: 'accountsReceivableInvoice',
        isPublished: true,
        lookupAccess: true,
    })
    readonly openItems: Collection<xtremFinance.nodes.AccountsReceivableOpenItem>;

    @decorators.collectionProperty<AccountsReceivableInvoice, 'taxes'>({
        isVital: true,
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'document',
        node: () => xtremFinance.nodes.AccountsReceivableInvoiceTax,
        isFrozen() {
            return !this.pCanUpdateFromExternalIntegration;
        },
    })
    readonly taxes: Collection<xtremFinance.nodes.AccountsReceivableInvoiceTax>;

    // property needed for the accounting interface
    @decorators.referenceProperty<AccountsReceivableInvoice, 'transactionCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        getValue() {
            return this.currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    // property needed for the accounting interface
    @decorators.dateProperty<AccountsReceivableInvoice, 'documentDate'>({
        isPublished: true,
        lookupAccess: true,
        getValue() {
            return this.invoiceDate;
        },
    })
    readonly documentDate: Promise<date>;

    @decorators.collectionProperty<AccountsReceivableInvoice, 'postingDetails'>({
        isPublished: true,
        join: {
            documentSysId() {
                return this._id;
            },
            documentType() {
                return 'arInvoice';
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    async sendFinanceNotification() {
        if (await (await (await this.financialSite).legalCompany).doArPosting) {
            // send notification in order to create a staging table entry for the accounting engine
            await xtremFinanceData.functions.accountsReceivableInvoiceNotification(
                this.$.context,
                this as xtremFinanceData.interfaces.ArInvoice,
                (await this.lines.toArray()) as xtremFinanceData.interfaces.ApArInvoiceLine[],
            );
        }
    }

    @decorators.mutation<typeof AccountsReceivableInvoice, 'post'>({
        isPublished: true,
        parameters: [
            {
                name: 'arInvoice',
                type: 'reference',
                isMandatory: true,
                node: () => AccountsReceivableInvoice,
                isWritable: true,
            },
        ],
        return: { type: 'string' },
    })
    static async post(context: Context, arInvoice: AccountsReceivableInvoice): Promise<string> {
        if (!['draft', 'error'].includes(await arInvoice.postingStatus)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-finance/nodes__accounts_receivable_invoice__cant_post_ar_invoice_when_status_is_not_draft_nor_error',
                    'The posting status is not {{draft}} nor {{error}}. The accounts receivable invoice cannot be posted.',
                    {
                        draft: context.localizeEnumMember('@sage/xtrem-finance-data/JournalStatus', 'draft'),
                        error: context.localizeEnumMember('@sage/xtrem-finance-data/JournalStatus', 'error'),
                    },
                ),
            );
        }

        arInvoice.pIsPosting = true;
        if (
            (await context.isServiceOptionEnabled(xtremStructure.serviceOptions.intacctActivationOption)) ||
            (await (
                await (
                    await arInvoice.financialSite
                ).legalCompany
            ).doArPosting)
        ) {
            await arInvoice.$.set({ postingStatus: 'inProgress' });
        } else {
            await arInvoice.$.set({ postingStatus: 'posted' });
        }

        if (!(await context.isServiceOptionEnabled(xtremStructure.serviceOptions.intacctActivationOption))) {
            const openItemData = await AccountsReceivableInvoice.newOpenItemPayload(arInvoice);
            const openItemExists =
                (await context.query(xtremFinance.nodes.AccountsReceivableOpenItem, {
                    filter: { documentSysId: openItemData.documentSysId, documentType: openItemData.documentType },
                    first: 1,
                }).length) > 0;
            if (!openItemExists) {
                const openItem = await context.create(xtremFinance.nodes.AccountsReceivableOpenItem, openItemData);
                await openItem.$.save();
            }

            await arInvoice.sendFinanceNotification();
        }

        arInvoice.pForceUpdateForFinance = true; // make sure the update of the ar invoice will not be refused by the control in saveBegin
        await arInvoice.$.save();
        arInvoice.pIsPosting = false;

        return context.localize(
            '@sage/xtrem-finance/nodes__ar_invoice__posted',
            'The accounts receivable invoice has been posted.',
        );
    }

    @decorators.notificationListener<typeof AccountsReceivableInvoice>({
        topic: 'AccountsReceivableInvoice/accountingInterface',
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        // isJustForStatus indicates that the update of finance transaction node was already done
        const shouldUpdateARInvoiceStatus =
            payload.isJustForStatus ??
            (await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload));

        if (shouldUpdateARInvoiceStatus) {
            // we need to update the status of the original AR invoice depending on the reply from finance integration
            // We do not know on this response if we are actually dealing with an invoice or credit memo. Therefore we
            // first try to read the arInvoice with type "salesInvoice" and if not found we try type "salesCreditMemo"
            let arInvoice = await AccountsReceivableInvoice.getArInvoice(
                context,
                payload.documentNumber,
                'salesInvoice',
            );
            if (!arInvoice)
                arInvoice = await AccountsReceivableInvoice.getArInvoice(
                    context,
                    payload.documentNumber,
                    'salesCreditMemo',
                );
            if (!arInvoice) return;

            // the status of the invoice is updated depending on the new status from the payload
            arInvoice.pForceUpdateForFinance = true; // make sure the update of the ar invoice will not be refused by the control in saveBegin
            await arInvoice.$.set({
                postingStatus: xtremFinanceData.functions.financeStatusMapping(
                    payload.status,
                    !!payload.financeExternalIntegration,
                    await arInvoice.postingStatus,
                ),
            });

            await arInvoice.$.save();

            // when we get the reply from the journal entry, we also have to reply to the Sales invoice in order to
            // update the posting status correctly. For FRP1000 integration we have at least 2 records in the FinanceTransactions:
            // one for creating the AR invoice from the Sales invoice and one for creating the Journal from the AR invoice
            // only the Journal is sent to FRP1000, the AR invoice is just an intermediate step and will never get a "posted"
            // status from FRP1000.
            // With Intacct integration we do not create a journal and therefore the FinanceTransaction record for the
            // targetDocumentType arInvoice will get status 'posted' when posted in Intacct
            if ((await arInvoice.origin) !== 'direct') {
                // only for invoices created from a Sales invoice or credit memo
                // we read the FinanceTransaction record for the original creation of the AR invoice to get the original batchId
                const financeIntegrationRecord = (
                    await context
                        .query(xtremFinanceData.nodes.FinanceTransaction, {
                            filter: {
                                targetDocumentType: 'accountsReceivableInvoice',
                                targetDocumentNumber: payload.documentNumber,
                            },
                        })
                        .toArray()
                )[0];

                if (financeIntegrationRecord) {
                    const originPayload: xtremFinanceData.interfaces.FinanceTransactionData = payload;
                    // before we reply to the Sales document, we clear the recordId, url and message
                    // here we have a cascaded reply only in case of frp1000 where the AR invoice is not sent to frp1000
                    // and we therefore do not want a url, message or record link on the FinanceTransaction record for the Sales invoice
                    if (originPayload.financeExternalIntegration) {
                        originPayload.financeExternalIntegration.recordId = '';
                        originPayload.financeExternalIntegration.url = '';
                        originPayload.validationMessages = [];
                    }
                    originPayload.batchId = await financeIntegrationRecord.batchId;
                    originPayload.targetDocumentNumber = payload.documentNumber;
                    originPayload.documentType =
                        (await arInvoice.origin) === 'invoice' ? 'salesInvoice' : 'salesCreditMemo';
                    originPayload.targetDocumentSysId = arInvoice._id;
                    originPayload.targetDocumentType = 'accountsReceivableInvoice';
                    await context.reply(
                        (await arInvoice.origin) === 'invoice'
                            ? 'SalesInvoice/accountingInterface'
                            : 'SalesCreditMemo/accountingInterface',
                        originPayload,
                    );
                }
            }
        }
    }

    @decorators.mutation<typeof AccountsReceivableInvoice, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [
            { name: 'arInvoice', type: 'reference', isMandatory: true, node: () => AccountsReceivableInvoice },
        ],
        return: { type: 'boolean' },
    })
    static async resendNotificationForFinance(
        context: Context,
        arInvoice: AccountsReceivableInvoice,
    ): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-finance/node__accounts_receivable_invoice__resend_notification_for_finance',
                'Resending finance notification for accounts receivable invoice: {{arInvoiceNumber}}.',
                { arInvoiceNumber: await arInvoice.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await arInvoice.number,
                documentType: 'arInvoice',
            })
        ) {
            await arInvoice.sendFinanceNotification();
        }

        return true;
    }

    @decorators.notificationListener<typeof AccountsReceivableInvoice>({
        topic: 'AccountsReceivableInvoice/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const arInvoice = await context.read(AccountsReceivableInvoice, { number: document.number });

        await AccountsReceivableInvoice.resendNotificationForFinance(context, arInvoice);
    }

    // Returns an accounts receivable invoice or null.
    private static getArInvoice(
        context: Context,
        documentNumber: string,
        type: string,
    ): Promise<xtremFinance.nodes.AccountsReceivableInvoice | null> {
        return context.tryRead(
            xtremFinance.nodes.AccountsReceivableInvoice,
            { number: documentNumber, type },
            { forUpdate: true },
        );
    }

    public static async newOpenItemPayload(
        arInvoice: xtremFinance.nodes.AccountsReceivableInvoice,
    ): Promise<NodeCreateData<xtremFinance.nodes.AccountsReceivableOpenItem>> {
        const billToCustomer = await arInvoice.billToCustomer;
        let businessEntityPayment = await billToCustomer.businessEntity;
        const payByCustomer = await billToCustomer.payByCustomer;
        businessEntityPayment = (await payByCustomer?.businessEntity) ?? businessEntityPayment;

        let documentType: xtremFinanceData.enums.FinanceDocumentType = 'arInvoice';
        if ((await arInvoice.origin) === 'invoice') documentType = 'salesInvoice';
        if ((await arInvoice.origin) === 'creditMemo') documentType = 'salesCreditMemo';
        return {
            dueDate: await arInvoice.dueDate,
            businessEntity: await (await arInvoice.billToCustomer).businessEntity,
            businessEntityPayment,
            type: 'customer',
            currency: await arInvoice.currency,
            transactionAmountDue: await arInvoice.totalAmountIncludingTax,
            transactionAmountPaid: 0,
            companyAmountDue: await arInvoice.totalCompanyAmountIncludingTax,
            companyAmountPaid: 0,
            // TODO: convert, when site currency might be different from company currency
            financialSiteAmountDue: await arInvoice.totalCompanyAmountIncludingTax,
            financialSiteAmountPaid: 0,
            status: 'notPaid',
            documentSysId: (await arInvoice.salesDocumentSysId) ? await arInvoice.salesDocumentSysId : arInvoice._id,
            documentNumber: (await arInvoice.salesDocumentNumber)
                ? await arInvoice.salesDocumentNumber
                : await arInvoice.number,
            documentType,
            accountsReceivableInvoice: arInvoice._id,
        };
    }

    // To allow the update of the printing status when the ar invoice is posted
    async setIsPrintedStatus(): Promise<void> {
        if (['posted', 'inProgress', 'error'].includes(await this.postingStatus)) {
            this.canUpdateIsPrinted = true;
            await this.$.set({ isPrinted: true as any });
            await this.$.save();
        }
    }

    // eslint-disable-next-line require-await, class-methods-use-this
    async attributTypeRestrictedToCheck(cx: ValidationContext) {
        noop(cx);
    }

    // Listener to initialize the payment status of an AR invoice to notPaid, when the service option paymentTrackingOption is activated
    // A notification is sent to the user to inform him that the payment tracking service option has been activated
    // and that he has to balance his already created open items in accordance with the AP/AR aging balance
    @decorators.notificationListener<typeof AccountsReceivableInvoice>({
        topic: 'SysServiceOptionState/paymentTrackingOption/activate',
    })
    static async paymentTrackingOptionActivate(context: Context): Promise<void> {
        await context.bulkUpdate(xtremFinance.nodes.AccountsReceivableInvoice, {
            set: { paymentStatus: 'notPaid' },
            where: { paymentStatus: null },
        });

        await context.notifyUser({
            title: context.localize(
                '@sage/xtrem-finance/nodes__accounts_receivable_invoice__payment_tracking',
                'Payment tracking service option activated',
            ),
            description: context.localize(
                '@sage/xtrem-finance/nodes__accounts_receivable_invoice__information_payment_tracking',
                'Update the open items previously created according to the AP/AR aging balance',
            ),
            icon: 'info',
            level: 'success',
            shouldDisplayToast: true,
            actions: [],
        });
    }
}
