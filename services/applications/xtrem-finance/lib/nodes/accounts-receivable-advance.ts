import type { Collection, Context, date, decimal, Reference, ValidationContext } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, Node, NodeStatus } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinance from '../index';

@decorators.node<AccountsReceivableAdvance>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    indexes: [
        {
            orderBy: { number: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    async controlEnd(cx: ValidationContext) {
        if (
            this.$.status === NodeStatus.modified &&
            !(this.pIsPosting || this.pCanUpdateFromExternalIntegration) &&
            ['posted', 'inProgress', 'error'].includes(await (await this.$.old).postingStatus) &&
            (await this.internalFinanceIntegrationStatus) !== 'failed'
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-finance/nodes__accounts_receivable_advance__update_not_allowed_status_posted',
                'You can only update an accounts receivable advance that is a draft',
            );
        }

        await xtremFinance.functions.controlMandatoryAttributesAndDimensions(this.$.context, cx, this);
    },
    async saveBegin() {
        await xtremMasterData.functions.controlDocumentNumber(this);
    },
})
export class AccountsReceivableAdvance extends Node {
    // To allow update on intacct gateway properties
    public pCanUpdateFromExternalIntegration = false;

    // To inform that a reply to the original document was sent from the intacct integration so we don't sent it from the accounting engine and don't mix the document status
    public pHasReplyBeenSentFromExternalIntegration = false;

    // To temporarily store an error message from the intacct integration
    public pExternalIntegrationMessage = '';

    // To allow update on finance integration reply
    protected pForceUpdateForFinance = false;

    // To flag if we are on the posting action, used on the extensions and to decide about the correct document status
    public pIsPosting = false;

    @decorators.stringProperty<AccountsReceivableAdvance, 'number'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        provides: ['sequenceNumber'],
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        defaultValue() {
            return xtremMasterData.functions.getDocumentNumberPlaceholder();
        },
        deferredDefaultValue() {
            return xtremMasterData.functions.allocateDocumentNumber(this);
        },
    })
    readonly number: Promise<string>;

    @decorators.referenceProperty<AccountsReceivableAdvance, 'bankAccount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true, // XT-80680 changed during bank account refactor since cash book management will be removed
        node: () => xtremFinanceData.nodes.BankAccount,
        isFrozen: true,
    })
    readonly bankAccount: Reference<xtremFinanceData.nodes.BankAccount | null>;

    @decorators.referenceProperty<AccountsReceivableAdvance, 'financialSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
        filters: {
            control: {
                isFinance: true,
            },
        },
        isFrozen: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.stringProperty<AccountsReceivableAdvance, 'financialSiteName'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNotEmpty: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => xtremSystem.dataTypes.name,
        dependsOn: ['financialSite'],
        async defaultValue() {
            return (await this.financialSite).name;
        },
        isFrozen: true,
    })
    readonly financialSiteName: Promise<string>;

    @decorators.stringProperty<AccountsReceivableAdvance, 'payToCustomerId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.id,
        isFrozen: true,
    })
    readonly payToCustomerId: Promise<string>;

    @decorators.stringProperty<AccountsReceivableAdvance, 'payToCustomerName'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNotEmpty: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => xtremSystem.dataTypes.name,
        isFrozen: true,
    })
    readonly payToCustomerName: Promise<string>;

    @decorators.stringProperty<AccountsReceivableAdvance, 'reference'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
        isFrozen: true,
    })
    readonly reference: Promise<string>;

    @decorators.stringProperty<AccountsReceivableAdvance, 'description'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
        isFrozen: true,
    })
    readonly description: Promise<string>;

    @decorators.dateProperty<AccountsReceivableAdvance, 'postingDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
    })
    readonly postingDate: Promise<date>;

    @decorators.dateProperty<AccountsReceivableAdvance, 'paymentDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        provides: ['documentDate'],
        isFrozen: true,
    })
    readonly paymentDate: Promise<date>;

    @decorators.referenceProperty<AccountsReceivableAdvance, 'currency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        isFrozen: true,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<AccountsReceivableAdvance, 'companyCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<AccountsReceivableAdvance, 'transactionCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        getValue() {
            return this.currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<AccountsReceivableAdvance, 'companyFxRate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        isFrozen: true,
    })
    readonly companyFxRate: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableAdvance, 'companyFxRateDivisor'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
    })
    readonly companyFxRateDivisor: Promise<decimal>;

    @decorators.dateProperty<AccountsReceivableAdvance, 'fxRateDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
    })
    readonly fxRateDate: Promise<date>;

    @decorators.decimalProperty<AccountsReceivableAdvance, 'advanceAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
    })
    readonly advanceAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivableAdvance, 'advanceCompanyAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
    })
    readonly advanceCompanyAmount: Promise<decimal>;

    @decorators.enumProperty<AccountsReceivableAdvance, 'postingStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.journalStatusDataType,
        defaultValue() {
            return 'draft';
        },
        isFrozen() {
            return !this.pIsPosting && !this.pCanUpdateFromExternalIntegration;
        },
    })
    readonly postingStatus: Promise<xtremFinanceData.enums.JournalStatus>;

    @decorators.enumProperty<AccountsReceivableAdvance, 'financeIntegrationStatus'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.postingStatusDataType,
        async computeValue() {
            return xtremFinanceData.functions.getPostingStatusFromFinanceIntegrationStatus(this.$.context, {
                financeIntegrationStatus: await this.internalFinanceIntegrationStatus,
                externalIntegration: await this.$.context.isServiceOptionEnabled(
                    xtremStructure.serviceOptions.intacctActivationOption,
                ),
                financialSite: await this.financialSite,
                documentType: 'bankReconciliationDeposit',
                targetDocumentType: 'accountsReceivableAdvance',
            });
        },
    })
    readonly financeIntegrationStatus: Promise<xtremFinanceData.enums.PostingStatus>;

    @decorators.enumProperty<AccountsReceivableAdvance, 'financeIntegrationApp'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationAppDataType,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationApp(
                this.$.context,
                await this.number,
                'accountsReceivableAdvance',
            );
        },
    })
    readonly financeIntegrationApp: Promise<xtremFinanceData.enums.FinanceIntegrationApp | null>;

    @decorators.enumProperty<AccountsReceivableAdvance, 'internalFinanceIntegrationStatus'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
        async computeValue() {
            return (
                (await (
                    await xtremFinanceData.functions.getFinanceTransaction(
                        this.$.context,
                        await this.number,
                        'accountsReceivableAdvance',
                    )
                )?.status) || 'recorded'
            );
        },
    })
    readonly internalFinanceIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.stringProperty<AccountsReceivableAdvance, 'financeIntegrationAppRecordId'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationAppRecordId(
                this.$.context,
                await this.number,
                'accountsReceivableAdvance',
            );
        },
    })
    readonly financeIntegrationAppRecordId: Promise<string>;

    @decorators.stringProperty<AccountsReceivableAdvance, 'financeIntegrationAppUrl'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.url,
        async computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationAppUrl(
                this.$.context,
                await this.number,
                'accountsReceivableAdvance',
            );
        },
    })
    readonly financeIntegrationAppUrl: Promise<string>;

    @decorators.collectionProperty<AccountsReceivableAdvance, 'lines'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremFinance.nodes.AccountsReceivableAdvanceLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<xtremFinance.nodes.AccountsReceivableAdvanceLine>;

    @decorators.mutation<typeof AccountsReceivableAdvance, 'post'>({
        isPublished: true,
        parameters: [
            {
                name: 'arAdvance',
                type: 'reference',
                isMandatory: true,
                node: () => AccountsReceivableAdvance,
                isWritable: true,
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async post(context: Context, arAdvance: AccountsReceivableAdvance): Promise<string> {
        if (!['draft', 'error'].includes(await arAdvance.postingStatus)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-finance/nodes__accounts_receivable_advance__cant_post_ar_advance_when_status_is_not_draft_nor_error',
                    "The posting status needs to be 'Draft' or 'Error'. The accounts receivable advance cannot be posted.",
                ),
            );
        }

        arAdvance.pIsPosting = true;

        if (await context.isServiceOptionEnabled(xtremStructure.serviceOptions.intacctActivationOption)) {
            await arAdvance.$.set({ postingStatus: 'inProgress' });
        } else {
            await arAdvance.$.set({ postingStatus: 'posted' });
        }

        await arAdvance.$.save();
        arAdvance.pIsPosting = false;

        return context.localize(
            '@sage/xtrem-finance/nodes__ar_advance__posted',
            'The accounts receivable advance has been posted.',
        );
    }
}
