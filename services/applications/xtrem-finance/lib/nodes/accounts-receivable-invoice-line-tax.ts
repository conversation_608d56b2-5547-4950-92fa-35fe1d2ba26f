import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinance from '../index';

@decorators.subNode<AccountsReceivableInvoiceLineTax>({
    extends: () => xtremTax.nodes.BaseLineTax,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
})
export class AccountsReceivableInvoiceLineTax
    extends xtremTax.nodes.BaseLineTax
    implements xtremMasterData.interfaces.FinancialSubNode
{
    @decorators.referenceProperty<AccountsReceivableInvoiceLineTax, 'document'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinance.nodes.AccountsReceivableInvoiceLine,
        isFrozen: true,
    })
    readonly document: Reference<xtremFinance.nodes.AccountsReceivableInvoiceLine>;

    @decorators.referencePropertyOverride<AccountsReceivableInvoiceLineTax, 'currency'>({
        dependsOn: [{ document: ['currency'] }],
        async getValue() {
            return (await this.document).currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;
}
