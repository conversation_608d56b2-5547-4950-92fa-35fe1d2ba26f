import type { decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinance from '../index';

@decorators.subNode<AccountsReceivablePaymentLine>({
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    isPublished: true,
    isVitalCollectionChild: true,
})
export class AccountsReceivablePaymentLine extends xtremMasterData.nodes.BaseDocumentLine {
    @decorators.referenceProperty<AccountsReceivablePaymentLine, 'document'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinance.nodes.AccountsReceivablePayment,
    })
    override readonly document: Reference<xtremFinance.nodes.AccountsReceivablePayment>;

    @decorators.stringPropertyOverride<AccountsReceivablePaymentLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<AccountsReceivablePaymentLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumProperty<AccountsReceivablePaymentLine, 'type'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremFinanceData.enums.financeDocumentTypeDataType,
        async control(cx) {
            if ((await this.type) !== 'salesInvoice' && (await this.type) !== 'salesCreditMemo') {
                cx.error.addLocalized(
                    '@sage/xtrem-finance/nodes__accounts_receivable_payment_line__type_invalid',
                    "The accounts receivable payment line type must be 'Sales invoice' or 'Sales credit memo'.",
                );
            }
        },
    })
    readonly type: Promise<xtremFinanceData.enums.FinanceDocumentType>;

    @decorators.referenceProperty<AccountsReceivablePaymentLine, 'financialSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ document: ['financialSite'] }],
        node: () => xtremSystem.nodes.Site,
        filters: {
            control: {
                isFinance: true,
                async legalCompany() {
                    return (await (await this.document).financialSite).legalCompany;
                },
            },
        },
        async defaultValue() {
            return (await this.document).financialSite;
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<AccountsReceivablePaymentLine, 'currency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ document: ['currency'] }],
        node: () => xtremMasterData.nodes.Currency,
        async defaultValue() {
            return (await this.document).currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<AccountsReceivablePaymentLine, 'companyCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<AccountsReceivablePaymentLine, 'transactionCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        getValue() {
            return this.currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<AccountsReceivablePaymentLine, 'paymentAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
    })
    readonly paymentAmount: Promise<decimal>;

    @decorators.decimalProperty<AccountsReceivablePaymentLine, 'paymentCompanyAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
    })
    readonly paymentCompanyAmount: Promise<decimal>;
}
