import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, Node } from '@sage/xtrem-core';
import * as xtremUpload from '@sage/xtrem-upload';
import type { UploadedFileResult } from '../interfaces/datev';

@decorators.node<DatevExportListener>({
    package: 'xtrem-finance',
    isPublished: true,
})
export class DatevExportListener extends Node {
    @decorators.notificationListener<typeof DatevExportListener>({
        topic: 'DatevUpload/InfrastructureComplete',
    })
    static async whenListenedByDatevUpload(context: Context, envelope: UploadedFileResult): Promise<void> {
        const fileId = envelope.uploadedFileId;

        const downloadedFile = await context.tryRead(xtremUpload.nodes.UploadedFile, {
            _id: fileId,
        });
        if (!downloadedFile) {
            throw new BusinessRuleError(`UploadedFile:downloadFileContent Request file ${fileId} does not exist`);
        }

        await context.notifyUser({
            title: context.localize('@sage/xtrem-finance/nodes__datev_export_listener__datev_file', 'DATEV file'),
            description: context.localize(
                '@sage/xtrem-finance/nodes__datev_export_listener__datev_file_ready',
                'File ready to download',
            ),
            icon: 'download',
            level: 'success',
            shouldDisplayToast: true,
            actions: [
                {
                    icon: 'link',
                    link: await downloadedFile.downloadUrl,
                    style: 'link',
                    title: 'Download file',
                },
            ],
        });
    }
}
