import type { date, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinance from '../index';

@decorators.node<DatevExportJournalEntryLine>({
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
})
export class DatevExportJournalEntryLine extends Node {
    @decorators.referenceProperty<DatevExportJournalEntryLine, 'datevExport'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremFinance.nodes.DatevExport,
    })
    readonly datevExport: Reference<xtremFinance.nodes.DatevExport>;

    @decorators.referenceProperty<DatevExportJournalEntryLine, 'journalEntryLine'>({
        isStored: true,
        isPublished: true,
        node: () => xtremFinance.nodes.JournalEntryLine,
    })
    readonly journalEntryLine: Reference<xtremFinance.nodes.JournalEntryLine>;

    @decorators.decimalProperty<DatevExportJournalEntryLine, 'transactionValue'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dependsOn: ['transactionCurrency'],
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
    })
    readonly transactionValue: Promise<decimal | null>;

    // TODO: To be renamed to sign after next release
    @decorators.stringProperty<DatevExportJournalEntryLine, 'datevSign'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.id,
    })
    readonly datevSign: Promise<string>;

    @decorators.referenceProperty<DatevExportJournalEntryLine, 'transactionCurrency'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Currency,
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<DatevExportJournalEntryLine, 'companyFxRate'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
    })
    readonly companyFxRate: Promise<decimal>;

    @decorators.decimalProperty<DatevExportJournalEntryLine, 'companyValue'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dependsOn: ['companyCurrency'],
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
    })
    readonly companyValue: Promise<decimal | null>;

    @decorators.referenceProperty<DatevExportJournalEntryLine, 'companyCurrency'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Currency,
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.integerProperty<DatevExportJournalEntryLine, 'datevAccountId'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
    })
    readonly datevAccountId: Promise<integer | null>;

    @decorators.integerProperty<DatevExportJournalEntryLine, 'datevContraAccountId'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly datevContraAccountId: Promise<integer | null>;

    @decorators.integerProperty<DatevExportJournalEntryLine, 'postingKey'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly postingKey: Promise<integer | null>;

    @decorators.dateProperty<DatevExportJournalEntryLine, 'postingDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
    })
    readonly postingDate: Promise<date>;

    @decorators.stringProperty<DatevExportJournalEntryLine, 'number'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly number: Promise<string>;

    @decorators.stringProperty<DatevExportJournalEntryLine, 'supplierDocumentNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly supplierDocumentNumber: Promise<string>;

    @decorators.stringProperty<DatevExportJournalEntryLine, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<DatevExportJournalEntryLine, 'dimension1'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.mediumString,
    })
    readonly dimension1: Promise<string>;

    @decorators.stringProperty<DatevExportJournalEntryLine, 'dimension2'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.mediumString,
    })
    readonly dimension2: Promise<string>;

    @decorators.stringProperty<DatevExportJournalEntryLine, 'businessEntityTaxIdNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremStructure.dataTypes.taxIdentificationDataType,
    })
    readonly businessEntityTaxIdNumber: Promise<string>;

    @decorators.integerProperty<DatevExportJournalEntryLine, 'locked'>({
        isStored: true,
        isPublished: true,
    })
    readonly locked: Promise<integer>;

    @decorators.stringProperty<DatevExportJournalEntryLine, 'siteTaxIdNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremStructure.dataTypes.taxIdentificationDataType,
    })
    readonly siteTaxIdNumber: Promise<string>;
}
