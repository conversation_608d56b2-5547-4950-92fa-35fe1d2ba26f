import { asyncArray } from '@sage/xtrem-async-helper';
import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type {
    AccountsPayableInvoiceLine,
    AccountsPayableOpenItem,
    AccountsReceivableInvoiceLine,
    AccountsReceivableOpenItem,
    JournalEntryLine,
} from '@sage/xtrem-finance-api';
import type {
    AccountsPayableReceivableInvoiceOrigin,
    FinanceTransaction,
    PaymentTracking,
} from '@sage/xtrem-finance-data-api';
import {
    getActiveAttributes,
    getActiveDimensions,
} from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type { FinanceOriginPostingStatusData } from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces';
import type { BusinessRelationType, Currency, PaymentMethod } from '@sage/xtrem-master-data-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import { displayBaseTax, displayTaxes } from '@sage/xtrem-tax/build/lib/client-functions/display-taxes';
import type { DocumentTypeEnum } from '@sage/xtrem-tax/build/lib/client-functions/interfaces';
import * as ui from '@sage/xtrem-ui';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
import type {
    FinanceTransactionTable,
    SelectFinanceTransactionLine,
} from '../page-extensions/sys-notification-history-extension';
import type { BasePayment as BasePaymentPageFragment } from '../page-fragments/base-payment';
import type {
    BasePaymentLine as BasePaymentLinePageFragment,
    PaymentDocumentLineType,
} from '../page-fragments/base-payment-line';
import type { OpenItemGeneral } from '../page-fragments/open-item-general';
import type { AccountsPayableInvoice as AccountsPayableInvoicePage } from '../pages/accounts-payable-invoice';
import type { AccountsPayableOpenItem as AccountsPayableOpenItemPage } from '../pages/accounts-payable-open-item';
import type { AccountsPayableOpenItemPay as AccountsPayableOpenItemPayPage } from '../pages/accounts-payable-open-item-pay';
import type { AccountsReceivableInvoice as AccountsReceivableInvoicePage } from '../pages/accounts-receivable-invoice';
import type { AccountsReceivableOpenItem as AccountsReceivableOpenItemPage } from '../pages/accounts-receivable-open-item';
import type { AccountsReceivableOpenItemPay as AccountsReceivableOpenItemPayPage } from '../pages/accounts-receivable-open-item-pay';
import type { JournalEntry as JournalEntryPage } from '../pages/journal-entry';
import { areFinanceIntegrationPackagesActive } from './finance-integration';
import type {
    AccountsPayableOpenItemRecord,
    AccountsReceivableOpenItemRecord,
    BasePaymentCreationData,
    RecordPages,
} from './interfaces/record';

export type AccountsInvoicePage = AccountsPayableInvoicePage | AccountsReceivableInvoicePage;
type AccountsOpenItemPage =
    | AccountsPayableOpenItemPage
    | AccountsPayableOpenItemPayPage
    | AccountsReceivableOpenItemPage
    | AccountsReceivableOpenItemPayPage;

type CommonPages = AccountsOpenItemPage | OpenItemGeneral | BasePaymentLinePageFragment;

function initPosting(page: AccountsInvoicePage) {
    page.messages.value = page.postingDetails.value.length === 1 ? page.postingDetails.value[0].message : '';
    page.postingMessageBlock.isHidden = page.messages.value === '';
}

function manageDisplayButtonGoToSysNotificationPageAction(page: AccountsInvoicePage) {
    page.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
        parameters: {
            financeIntegrationStatus: page.postingDetails.value.map(
                financeDocument => financeDocument.postingStatus || '',
            ),
        },
        recordId: page.$.recordId,
    });
}

function updatePrefixScaleOnCurrencyFields(page: AccountsInvoicePage) {
    page.totalAmountExcludingTax.prefix = page.currency.value?.symbol ?? '';
    page.totalAmountExcludingTax.scale = page.currency.value?.decimalDigits ?? 2;
    page.totalAmountIncludingTax.prefix = page.currency.value?.symbol ?? '';
    page.totalAmountIncludingTax.scale = page.currency.value?.decimalDigits ?? 2;
    page.totalTaxAmount.prefix = page.currency.value?.symbol ?? '';
    page.totalTaxAmount.scale = page.currency.value?.decimalDigits ?? 2;
}

async function getActiveDimensionsAndAttributes(page: AccountsInvoicePage) {
    page.activeDimensionTypes = await getActiveDimensions(page);
    page.activeAttributeTypes = await getActiveAttributes(page);
}

async function financeIntegrationManagement(page: AccountsInvoicePage) {
    if (await areFinanceIntegrationPackagesActive(page.$.graph)) {
        page.financeIntegrationAppRecordIdLink.page = page.financeIntegrationAppUrl.value || ''; // Assign the finance integration url to the finance integration id link field.
        page.financeIntegrationAppRecordIdLink.value = page.financeIntegrationAppRecordId.value;
        page.financeIntegrationAppRecordIdLink.isHidden = page.financeIntegrationApp.value
            ? page.financeIntegrationApp.value !== 'intacct'
            : true;
        page.financeIntegrationStatus.isHidden = page.postingStatus.value === page.financeIntegrationStatus.value;
        page.financeIntegrationAppRecordId.isHidden = page.financeIntegrationApp.value !== 'frp1000';
    }
}

export function setApplicativePageCrudActionsAPARInvoice(isDirty: boolean, page: AccountsInvoicePage) {
    setApplicativePageCrudActions({
        page,
        isDirty: isDirty && page.internalFinanceIntegrationStatus.value === 'failed' && page.fromNotificationHistory,
        save: page.$standardSaveAction,
    });
}

export async function onLoadAPARInvoice(page: AccountsInvoicePage) {
    updatePrefixScaleOnCurrencyFields(page);
    await getActiveDimensionsAndAttributes(page);

    page.fromNotificationHistory = page.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
    await financeIntegrationManagement(page);
    page.taxDetailLines.isHidden = page.financialSite?.value?.legalCompany?.legislation?.id !== 'US';
    page.$standardSaveAction.isDisabled =
        page.internalFinanceIntegrationStatus.value !== 'failed' || !page.fromNotificationHistory;
    page.$standardCancelAction.isDisabled =
        page.internalFinanceIntegrationStatus.value !== 'failed' || !page.fromNotificationHistory;

    initPosting(page);
    manageDisplayButtonGoToSysNotificationPageAction(page);

    setApplicativePageCrudActionsAPARInvoice(page.$.isDirty, page);
}

export async function taxDetailsAPAR(parameters: {
    page: AccountsInvoicePage;
    rowItem: AccountsPayableInvoiceLine | AccountsReceivableInvoiceLine;
    documentType: DocumentTypeEnum;
}) {
    if (Number(parameters.rowItem._id) > 0) {
        await displayTaxes(parameters.page, parameters.rowItem, {
            currency: parameters.page.currency.value as ExtractEdgesPartial<Currency>,
            taxAmount: +parameters.rowItem.taxAmount,
            amountExcludingTax: +parameters.rowItem.amountExcludingTax,
            amountIncludingTax: +parameters.rowItem.amountIncludingTax,
            documentType: parameters.documentType,
            taxDate: parameters.rowItem.taxDate,
            editable: !(parameters.page.postingStatus.value === 'posted' || parameters.page.origin.value !== 'direct'),
        });
    }
}

export async function taxDetailsJournalEntry(parameters: { page: JournalEntryPage; rowItem: JournalEntryLine }) {
    if (Number(parameters.rowItem._id) > 0) {
        await displayBaseTax({
            pageInstance: parameters.page,
            data: {
                currency: parameters.rowItem.baseTax.currency as unknown as ExtractEdgesPartial<Currency>,
                taxCategory: parameters.rowItem.baseTax.taxCategory,
                tax: parameters.rowItem.baseTax.tax,
                taxableAmount: Number(parameters.rowItem.baseTax.taxableAmount),
                nonTaxableAmount: Number(parameters.rowItem.baseTax.nonTaxableAmount),
                taxAmount: Number(parameters.rowItem.baseTax.taxAmount),
                taxAmountAdjusted: Number(parameters.rowItem.baseTax.taxAmountAdjusted),
                taxRate: Number(parameters.rowItem.baseTax.taxRate),
                exemptAmount: Number(parameters.rowItem.baseTax.exemptAmount),
                deductibleTaxAmount: Number(parameters.rowItem.baseTax.deductibleTaxAmount),
                deductibleTaxRate: Number(parameters.rowItem.baseTax.deductibleTaxRate),
                isReverseCharge: parameters.rowItem.baseTax.isReverseCharge,
            },
        });
    }
}

export function isAttributeHidden(page: AccountsInvoicePage, attributeTypeId: string) {
    return !page.activeAttributeTypes.some(attributeType => attributeType.id === attributeTypeId);
}

export function isDimensionHidden(page: AccountsInvoicePage, dimensionTypeId: string) {
    return !page.activeDimensionTypes.some(dimensionType => dimensionType.docProperty === dimensionTypeId);
}

export function isFinanceOriginPostingStatusData(row: any): row is FinanceOriginPostingStatusData {
    if (row.documentType && row.documentSysId) {
        return !!row;
    }
    return false;
}

export function isFinanceTransaction(row: any): row is FinanceTransaction {
    if (row.status && row.targetDocumentType) {
        return !!row;
    }
    return false;
}

export function isFinanceTransactionLine(row: any): row is SelectFinanceTransactionLine {
    if (row.sourceDocumentType && row.sourceDocumentSysId && row.financeTransactionSysId) {
        return !!row;
    }
    return false;
}

export function isFinanceTransactionTable(row: any): row is FinanceTransactionTable {
    if (row.documentType && row.documentSysId && row.status && row.targetDocumentType) {
        return !!row;
    }
    return false;
}

function openPage(parameters: {
    page: CommonPages;
    documentSysId: number;
    fromFinance: boolean;
    path: string;
}): Promise<void> | undefined {
    return parameters.page.$.dialog.page(
        parameters.path,
        { _id: parameters.documentSysId, fromFinance: parameters.fromFinance },
        { fullScreen: true, resolveOnCancel: true },
    );
}

export function openDocumentLinkPage(parameters: {
    page: CommonPages;
    documentSysId: number;
    documentType: string | null;
    fromFinance: boolean;
}): Promise<void> | undefined {
    switch (parameters.documentType) {
        case 'salesInvoice':
            return openPage({ ...parameters, path: `@sage/xtrem-sales/SalesInvoice` });
        case 'salesCreditMemo':
            return openPage({ ...parameters, path: `@sage/xtrem-sales/SalesCreditMemo` });
        case 'purchaseInvoice':
            return openPage({ ...parameters, path: `@sage/xtrem-purchasing/PurchaseInvoice` });
        case 'purchaseCreditMemo':
            return openPage({ ...parameters, path: `@sage/xtrem-purchasing/PurchaseCreditMemo` });
        default:
            return undefined;
    }
}

function getDocumentTypeAndSysId(
    line: PartialCollectionValueWithIds<PaymentDocumentLineType>,
    type: string,
): { documentSysId: number; documentType: string } {
    switch (type) {
        case 'supplier':
            return {
                documentSysId: line.apOpenItem?.accountsPayableInvoice?.purchaseDocumentSysId ?? 0,
                documentType: line.origin === 'invoice' ? 'purchaseInvoice' : 'purchaseCreditMemo',
            };
        case 'customer':
            return {
                documentSysId: line.arOpenItem?.accountsReceivableInvoice?.salesDocumentSysId ?? 0,
                documentType: line.origin === 'invoice' ? 'salesInvoice' : 'salesCreditMemo',
            };
        default:
            return { documentSysId: 0, documentType: '' };
    }
}

export async function openLinkPage(parameters: {
    page: BasePaymentLinePageFragment;
    line: PartialCollectionValueWithIds<PaymentDocumentLineType>;
}) {
    const { documentType, documentSysId } = getDocumentTypeAndSysId(parameters.line, parameters.page.type.value ?? '');
    await openDocumentLinkPage({ page: parameters.page, documentSysId, documentType, fromFinance: true });
}

// Inits the receipt/payment page in onLoad
export function initBasePayment(page: BasePaymentPageFragment) {
    if (page.$.queryParameters.createParams) {
        const createParams: BasePaymentCreationData = JSON.parse(page.$.queryParameters.createParams as string);
        page.type.value = createParams.header.type;
        page.bankAccount.value = createParams.header.bankAccount;
        page.financialSite.value = createParams.header.financialSite;
        page.customer.value = page.type.value === 'customer' ? createParams.header.businessRelation : null;
        page.supplier.value = page.type.value === 'supplier' ? createParams.header.businessRelation : null;
        page.businessRelation.value = createParams.header.businessRelation;
        page.paymentMethod.value = createParams.header.paymentMethod;
        page.reference.value = createParams.header.reference;
        page.paymentDate.value = createParams.header.paymentDate;
        page.currency.value = createParams.header.currency;
        page.amount.value = createParams.header.amount;
        page.amountBankCurrency.value = createParams.header.amountBankCurrency;
    }
    page.paymentDate.title =
        page.type.value === 'customer'
            ? ui.localize('@sage/xtrem-finance/page_fragments__base_payment__date_received', 'Date received')
            : ui.localize('@sage/xtrem-finance/page_fragments__base_payment__date_issued', 'Date issued');
}

type DataToCreatePaymentLines = {
    amountBankCurrency: number;
    decimalDigits: number;
    amount: number;
    isDifferentBankCurrency: boolean;
};

function getValuesToCreatePaymentLines(page: BasePaymentLinePageFragment): DataToCreatePaymentLines {
    return {
        amountBankCurrency: page.amountBankCurrency.value ?? 0,
        decimalDigits: page.bankAccount?.value?.currency?.decimalDigits ?? 2,
        amount: page.amount.value ?? 0,
        isDifferentBankCurrency: page.currency?.value?.id !== page.bankAccount?.value?.currency?.id,
    };
}

function updatePageLines(
    page: BasePaymentLinePageFragment,
    lineToBeUpdated: {
        origin: AccountsPayableReceivableInvoiceOrigin;
        paymentAmount?: number;
        creditAmount?: number;
        amountBankCurrency: number;
        arOpenItem?: PartialCollectionValueWithIds<AccountsReceivableOpenItem>;
        apOpenItem?: PartialCollectionValueWithIds<AccountsPayableOpenItem>;
        paymentTracking?: ExtractEdgesPartial<PaymentTracking> | undefined;
        discountAmount?: number;
        penaltyAmount?: number;
        adjustmentAmount?: number;
    },
) {
    page.lines.addOrUpdateRecordValue({
        arOpenItem: lineToBeUpdated.arOpenItem,
        apOpenItem: lineToBeUpdated.apOpenItem,
        paymentTracking: lineToBeUpdated.paymentTracking,
        origin: lineToBeUpdated.origin,
        currency: lineToBeUpdated.arOpenItem?.currency ?? lineToBeUpdated.apOpenItem?.currency,
        amount:
            lineToBeUpdated.origin === 'invoice'
                ? (lineToBeUpdated.paymentAmount ?? 0).toString()
                : (lineToBeUpdated.creditAmount ?? 0).toString(),
        discountAmount:
            lineToBeUpdated.discountAmount && lineToBeUpdated.origin === 'invoice'
                ? (lineToBeUpdated.discountAmount ?? 0).toString()
                : undefined,
        penaltyAmount:
            lineToBeUpdated.penaltyAmount && lineToBeUpdated.origin === 'invoice'
                ? (lineToBeUpdated.penaltyAmount ?? 0).toString()
                : undefined,
        adjustmentAmount: lineToBeUpdated.adjustmentAmount
            ? (lineToBeUpdated.adjustmentAmount ?? 0).toString()
            : undefined,
        signedAmount:
            lineToBeUpdated.origin === 'invoice'
                ? (lineToBeUpdated.paymentAmount ?? 0).toString()
                : ((lineToBeUpdated.creditAmount ?? 0) * -1).toString(),
        signedAmountBankCurrency:
            lineToBeUpdated.origin === 'invoice'
                ? lineToBeUpdated.amountBankCurrency.toString()
                : (lineToBeUpdated.amountBankCurrency * -1).toString(),
    });
}

function readAccountsPayableOpenItem(
    page: BasePaymentLinePageFragment,
    lineSysId: string,
): Promise<PartialCollectionValueWithIds<AccountsPayableOpenItem>> {
    return page.$.graph
        .node('@sage/xtrem-finance/AccountsPayableOpenItem')
        .read(
            {
                _id: true,
                dueDate: true,
                accountsPayableInvoice: {
                    origin: true,
                    purchaseDocumentNumber: true,
                    purchaseDocumentSysId: true,
                    reference: true,
                    postingDate: true,
                    dueDate: true,
                    paymentTracking: { _id: true },
                },
                currency: { id: true, symbol: true, decimalDigits: true },
            },
            `${lineSysId}`,
        )
        .execute();
}

function readAccountsReceivableOpenItem(
    page: BasePaymentLinePageFragment,
    lineSysId: string,
): Promise<PartialCollectionValueWithIds<AccountsPayableOpenItem>> {
    return page.$.graph
        .node('@sage/xtrem-finance/AccountsReceivableOpenItem')
        .read(
            {
                _id: true,
                dueDate: true,
                accountsReceivableInvoice: {
                    origin: true,
                    salesDocumentNumber: true,
                    salesDocumentSysId: true,
                    reference: true,
                    postingDate: true,
                    dueDate: true,
                },
                currency: { id: true, symbol: true, decimalDigits: true },
            },
            `${lineSysId}`,
        )
        .execute();
}
type CalculateAmountBankCurrencyHeaderParameters = {
    amount: number;
    amountBankCurrency: number;
    isDifferentBankCurrency: boolean;
    total: number;
    tmp: number;
    numberOfLines: number;
};
type CalculateAmountBankCurrencyLineParameters = {
    index: number;
    origin: AccountsPayableReceivableInvoiceOrigin;
    creditAmount?: number;
    paymentAmount?: number;
};

type CalculatedAmountBankCurrency = { lineAmountBankCurrency: number; totalApplied: number };

function calculateAmountBankCurrency(
    header: CalculateAmountBankCurrencyHeaderParameters,
    line: CalculateAmountBankCurrencyLineParameters,
): CalculatedAmountBankCurrency {
    let lineAmountBankCurrency = line.origin === 'invoice' ? (line.paymentAmount ?? 0) : (line.creditAmount ?? 0);
    let totalApplied = header.total;
    if (header.isDifferentBankCurrency) {
        lineAmountBankCurrency =
            line.index === header.numberOfLines - 1
                ? Math.abs(header.amountBankCurrency - totalApplied)
                : Math.round(((header.amountBankCurrency * lineAmountBankCurrency) / header.amount) * header.tmp) /
                  header.tmp;
        totalApplied =
            line.origin === 'invoice' ? totalApplied + lineAmountBankCurrency : totalApplied - lineAmountBankCurrency;
    }
    return { lineAmountBankCurrency, totalApplied };
}

async function setBasePaymentLinesForPayment(
    page: BasePaymentLinePageFragment,
    linesToBeUpdated: PartialCollectionValueWithIds<AccountsPayableOpenItemRecord>[],
) {
    const { amountBankCurrency, decimalDigits, amount, isDifferentBankCurrency } = getValuesToCreatePaymentLines(page);
    const numberOfLines = linesToBeUpdated.length;
    const tmp = 10 ** decimalDigits;
    let totalApplied = 0;

    await asyncArray(linesToBeUpdated).forEach(async (line, indexLine: number) => {
        const apOpenItem = await readAccountsPayableOpenItem(page, line._id);
        let lineAmountBankCurrency = 0;

        ({ lineAmountBankCurrency, totalApplied } = calculateAmountBankCurrency(
            { amountBankCurrency, amount, isDifferentBankCurrency, total: totalApplied, tmp, numberOfLines },
            {
                index: indexLine,
                origin: await line.accountsPayableInvoice?.origin,
                creditAmount: line.creditAmount,
                paymentAmount: line.paymentAmount,
            },
        ));
        updatePageLines(page, {
            apOpenItem,
            origin: line.accountsPayableInvoice?.origin,
            paymentAmount: line.paymentAmount,
            creditAmount: line.creditAmount,
            amountBankCurrency: lineAmountBankCurrency,
            paymentTracking: apOpenItem.accountsPayableInvoice?.paymentTracking,
            discountAmount: line.discountPaymentAmount,
            penaltyAmount: line.penaltyPaymentAmount,
            adjustmentAmount: line.adjustmentAmount,
        });
    });
}

async function setBasePaymentLinesForReceipt(
    page: BasePaymentLinePageFragment,
    linesToBeUpdated: PartialCollectionValueWithIds<AccountsReceivableOpenItemRecord>[],
) {
    const { amountBankCurrency, decimalDigits, amount, isDifferentBankCurrency } = getValuesToCreatePaymentLines(page);
    const numberOfLines = linesToBeUpdated.length;
    const tmp = 10 ** decimalDigits;
    let totalApplied = 0;

    await asyncArray(linesToBeUpdated).forEach(async (line, indexLine: number) => {
        const arOpenItem = await readAccountsReceivableOpenItem(page, line._id);
        let lineAmountBankCurrency = 0;

        ({ lineAmountBankCurrency, totalApplied } = calculateAmountBankCurrency(
            { amountBankCurrency, amount, isDifferentBankCurrency, total: totalApplied, tmp, numberOfLines },
            {
                index: indexLine,
                origin: await line.accountsReceivableInvoice?.origin,
                creditAmount: line.creditAmount,
                paymentAmount: line.paymentAmount,
            },
        ));
        updatePageLines(page, {
            arOpenItem,
            origin: line.accountsReceivableInvoice?.origin,
            paymentAmount: line.paymentAmount,
            creditAmount: line.creditAmount,
            amountBankCurrency: lineAmountBankCurrency,
            discountAmount: line.discountPaymentAmount,
            penaltyAmount: line.penaltyPaymentAmount,
            adjustmentAmount: line.adjustmentAmount,
        });
    });
}

// Inits the the receipt/payment page in onLoad
export async function initBasePaymentLine(page: BasePaymentLinePageFragment) {
    const createParams: BasePaymentCreationData = JSON.parse(page.$.queryParameters.createParams as string);
    switch (page.type.value) {
        case 'supplier':
            await setBasePaymentLinesForPayment(page, createParams.lines);
            break;
        case 'customer':
            await setBasePaymentLinesForReceipt(page, createParams.lines);
            break;
        default:
            break;
    }
}

export function openBasePaymentPage(page: RecordPages): Promise<string> {
    /*  page.$.page.node.toString() will return @sage/xtrem-finance/Payment or @sage/xtrem-finance/Receipt
        which is the same name of the pages. If page names change we need to change the way we assign this.
    */
    return page.$.dialog.page(
        page.$.page.node.toString(),
        {
            createParams: JSON.stringify({
                header: {
                    bankAccount: page.bankAccount.value,
                    financialSite: page.financialSite.value,
                    businessRelation: page.type.value === 'customer' ? page.customer.value : page.supplier.value,
                    type: page.type.value as BusinessRelationType,
                    paymentMethod: page.paymentMethod.value as PaymentMethod,
                    reference: page.reference.value ?? '',
                    paymentDate: page.date.value ?? '',
                    currency: page.currency.value,
                    amount: page.amount.value ?? 0,
                    amountBankCurrency: page.amountBankCurrency.value ?? 0,
                },
                lines: page.lines.value.filter(line => page.lines.selectedRecords.includes(line._id)),
            }),
        },
        { size: 'extra-large', resolveOnCancel: true },
    );
}
