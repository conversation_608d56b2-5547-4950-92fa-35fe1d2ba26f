import { DateValue, date } from '@sage/xtrem-date-time';
import type { DatevExportStatus } from '@sage/xtrem-finance-api';
import {
    getActiveAttributes,
    getActiveDimensions,
} from '@sage/xtrem-finance-data/lib/client-functions/attributes-and-dimensions';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import * as ui from '@sage/xtrem-ui';
import type { DatevExport } from '../pages/datev-export';
import type { DatevExportSettings } from '../pages/datev-export-settings';

export interface ExtractionExportParameters {
    pageInstance: DatevExport;
    doAccounts: boolean;
    doCustomersSuppliers: boolean;
    doJournalEntries: boolean;
    id: string;
    recordId: string;
}

export function validateFiscalYearStart(options: {
    pageInstance: DatevExportSettings;
    startDate: string | undefined;
    endDate: string | undefined;
    fiscalYearStart: string | undefined;
}): string {
    if (options.startDate && options.fiscalYearStart && options.startDate < options.fiscalYearStart) {
        return ui.localize(
            '@sage/xtrem-finance/pages__datev_export_parameters__wrong_start_for_begin_of_fiscal_year',
            'The start date needs to be after the start of the fiscal year.',
        );
    }
    if (options.endDate && options.fiscalYearStart && options.endDate < options.fiscalYearStart) {
        return ui.localize(
            '@sage/xtrem-finance/pages__datev_export_parameters__wrong_end_for_begin_of_fiscal_year',
            'The end date needs to be after the start of the fiscal year.',
        );
    }
    if (
        options.endDate &&
        options.fiscalYearStart &&
        options.endDate >= date.parse(options.fiscalYearStart).addYears(1).toString()
    ) {
        options.pageInstance.$.showToast(
            ui.localize(
                '@sage/xtrem-finance/pages__datev_export_parameters__wrong_end_date',
                'The end date needs to be less than one year after the start of the fiscal year.',
            ),
            { timeout: 5000, type: 'warning' },
        );
    }
    return '';
}

export function validateDates(options: {
    pageInstance: DatevExportSettings;
    startDate: string | undefined;
    endDate: string | undefined;
    fiscalYearStart: string | undefined;
}): string {
    if (options.startDate && options.endDate && options.startDate > options.endDate) {
        return ui.localize(
            '@sage/xtrem-finance/pages__datev_export_parameters__wrong_date_range',
            'The end date needs to be after the start date.',
        );
    }
    return validateFiscalYearStart(options);
}

export interface DimensionTypeForDropDown {
    _id: string;
    name: string;
    isAttributeType: boolean;
}

export function getAttributeType(
    attributeAndDimensionTypes: DimensionTypeForDropDown[],
    attributeName: string,
): DimensionTypeForDropDown | undefined {
    return attributeAndDimensionTypes.find(
        attributeAndDimensionType =>
            attributeAndDimensionType.name === attributeName && attributeAndDimensionType.isAttributeType,
    );
}

export function getDimensionType(
    attributeAndDimensionTypes: DimensionTypeForDropDown[],
    dimensionName: string,
): DimensionTypeForDropDown | undefined {
    return attributeAndDimensionTypes.find(
        attributeAndDimensionType =>
            attributeAndDimensionType.name === dimensionName && !attributeAndDimensionType.isAttributeType,
    );
}

export async function getAttributeAndDimensionTypes(
    pageInstance: DatevExportSettings,
): Promise<DimensionTypeForDropDown[]> {
    // read all attribute types and dimension types and order by name
    const attributeAndDimensionTypes = (await getActiveAttributes(pageInstance, { onlyNodeLinkAttribute: true }))
        .map(element => ({ _id: element._id, name: element.name, isAttributeType: true }))
        .concat(
            (await getActiveDimensions(pageInstance)).map(element => ({
                _id: element._id,
                name: element.name,
                isAttributeType: false,
            })),
        )
        .sort((e1, e2) => {
            if ((e1.name || '') > (e2.name || '')) {
                return 1;
            }
            if ((e1.name || '') < (e2.name || '')) {
                return -1;
            }
            return 0;
        });

    if (attributeAndDimensionTypes.length > 0) {
        return attributeAndDimensionTypes.map(element => ({
            _id: element._id,
            name: element.name,
            isAttributeType: element.isAttributeType,
        }));
    }
    return [];
}

export function getAttributesAndDimensionsOptions(pageInstance: DatevExportSettings): string[] {
    if (pageInstance.attributeAndDimensionTypes && pageInstance.attributeAndDimensionTypes.length > 0) {
        return pageInstance.attributeAndDimensionTypes.map(element => element.name ?? '');
    }
    return [];
}

export function validateDimensionTypes(pageInstance: DatevExportSettings): string {
    if (
        pageInstance.dimensionTypeOrAttribute1.value &&
        pageInstance.dimensionTypeOrAttribute1.value === pageInstance.dimensionTypeOrAttribute2.value
    ) {
        return ui.localize(
            '@sage/xtrem-finance/pages__datev_export_parameters__wrong_dimension_types',
            'The dimension type 2 needs to be different from the dimension type 1.',
        );
    }
    return '';
}

export async function initDatevExportSettings(pageInstance: DatevExportSettings): Promise<void> {
    pageInstance.isLocked.value = true;
    pageInstance.doJournalEntries.value = true;
    pageInstance.startDate.value = DateValue.today().addMonths(-1).begOfMonth().toString();
    pageInstance.endDate.value = DateValue.today().addMonths(-1).endOfMonth().toString();
    pageInstance.fiscalYearStart.value = date.parse(pageInstance.startDate.value).begOfYear().toString();
    await setReferenceIfSingleValue([pageInstance.company]);
    pageInstance.attributeAndDimensionTypes = await getAttributeAndDimensionTypes(pageInstance);
    pageInstance.dimensionTypeOrAttribute1.options = getAttributesAndDimensionsOptions(pageInstance);
    pageInstance.dimensionTypeOrAttribute2.options = getAttributesAndDimensionsOptions(pageInstance);
}

export function initDatevExportDimensionTypes(pageInstance: DatevExport): void {
    if (pageInstance.dimensionType1.value) {
        pageInstance.dimensionTypeText1.value = pageInstance.dimensionType1.value.name ?? '';
    }
    if (pageInstance.attributeType1.value) {
        pageInstance.dimensionTypeText1.value = pageInstance.attributeType1.value.name ?? '';
    }
    if (pageInstance.dimensionType2.value) {
        pageInstance.dimensionTypeText2.value = pageInstance.dimensionType2.value.name ?? '';
    }
    if (pageInstance.attributeType2.value) {
        pageInstance.dimensionTypeText2.value = pageInstance.attributeType2.value.name ?? '';
    }
}

async function checkStatusAfterAction(parameters: {
    pageInstance: DatevExport;
    statusToCheck: DatevExportStatus[];
    recordId: string;
}) {
    let success = false;
    let nbTries = 0;
    while (!success && nbTries <= 3) {
        nbTries += 1;
        await new Promise(resolve => {
            setTimeout(resolve, 1000);
        });
        const datevExport = await parameters.pageInstance.$.graph
            .node('@sage/xtrem-finance/DatevExport')
            .read({ _id: true, status: true }, parameters.recordId)
            .execute();

        if (parameters.statusToCheck.includes(datevExport.status)) {
            success = true;
        }
    }
}

export async function doExtraction(parameters: ExtractionExportParameters) {
    if (parameters.doAccounts || parameters.doCustomersSuppliers || parameters.doJournalEntries) {
        // Request extraction in an asynchronous mutation.
        await parameters.pageInstance.$.graph
            .node('@sage/xtrem-finance/DatevExport')
            .asyncOperations.datevExtraction.start({ trackingId: true }, { id: parameters.id ?? undefined })
            .execute();

        parameters.pageInstance.$.showToast(
            ui.localize(
                '@sage/xtrem-finance/pages__datev_export__notification_success',
                'DATEV extraction request sent.',
            ),
            { type: 'success' },
        );

        await checkStatusAfterAction({
            pageInstance: parameters.pageInstance,
            statusToCheck: ['extractionInProgress', 'extracted'],
            recordId: parameters.recordId ?? '',
        });
    }
}

export async function doExport(parameters: ExtractionExportParameters) {
    if (parameters.doAccounts || parameters.doCustomersSuppliers || parameters.doJournalEntries) {
        // Request export in an asynchronous mutation.
        await parameters.pageInstance.$.graph
            .node('@sage/xtrem-finance/DatevExport')
            .asyncOperations.datevExport.start({ trackingId: true }, { id: parameters.id ?? undefined })
            .execute();

        parameters.pageInstance.$.showToast(
            ui.localize(
                '@sage/xtrem-finance/pages__datev_export__notification_export_sent',
                'DATEV export request sent.',
            ),
            { type: 'success' },
        );

        await checkStatusAfterAction({
            pageInstance: parameters.pageInstance,
            statusToCheck: ['exportInProgress', 'exported'],
            recordId: parameters.recordId ?? '',
        });
    }
}
