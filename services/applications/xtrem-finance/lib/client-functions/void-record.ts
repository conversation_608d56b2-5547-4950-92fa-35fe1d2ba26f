import { DateValue } from '@sage/xtrem-date-time';
import * as ui from '@sage/xtrem-ui';

export function voidDateValidation(voidDate: string, paymentDate: string | null) {
    const voidDateParsed = DateValue.parse(voidDate);
    const paymentDateParsed = paymentDate ? DateValue.parse(paymentDate) : null;
    if (paymentDateParsed && voidDateParsed.compare(paymentDateParsed) < 0) {
        return ui.localize(
            '@sage/xtrem-finance/client_functions__void_record__void_date_should_be_after_payment_date',
            'The void date needs to be after the payment date.',
        );
    }
    return undefined;
}
