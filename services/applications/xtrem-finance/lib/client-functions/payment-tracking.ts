import { DateValue } from '@sage/xtrem-date-time';
import * as ui from '@sage/xtrem-ui';
import type { OpenItemPageFragments, PaymentPages } from './interfaces/payment-tracking';

export async function openPaymentPage(page: OpenItemPageFragments, documentSysId: string): Promise<void> {
    const paymentPage =
        page.$.page.node.toString() === '@sage/xtrem-finance/AccountsPayableOpenItem'
            ? '@sage/xtrem-finance/Payment'
            : '@sage/xtrem-finance/Receipt';
    await page.$.dialog.page(
        paymentPage,
        { _id: documentSysId ?? '', fromSales: false },
        { fullScreen: true, resolveOnCancel: true },
    );
    await page.$.router.refresh();
}

export async function voidPaymentOrReceipt(page: PaymentPages): Promise<void> {
    page.voidPaymentDate.value = DateValue.today().toString();
    page.voidSection.isHidden = false;
    await page.$.dialog
        .custom('info', page.voidSection, {
            acceptButton: {
                text: ui.localize('@sage/xtrem-finance/pages__payment__confirm_void_button', 'Confirm'),
            },
        })
        .then(async () => {
            if (page.voidPaymentDate.value) {
                page.$.loader.isHidden = false;
                const paymentPage =
                    page.$.page.node.toString() === '@sage/xtrem-finance/Payment'
                        ? '@sage/xtrem-finance/Payment'
                        : '@sage/xtrem-finance/Receipt';
                await page.$.graph
                    .node(paymentPage)
                    .mutations.voidPayment(true, {
                        payment: page.$.recordId || '',
                        voidDate: page.voidPaymentDate.value,
                        voidText: page.voidPaymentText.value || '',
                    })
                    .execute();

                page.$.loader.isHidden = true;
                page.$.setPageClean();
                await page.$.refreshNavigationPanel();
                await page.$.router.refresh();
            } else {
                page.$.showToast(
                    ui.localize('@sage/xtrem-finance/page__void_date__mandatory', 'The date is mandatory.'),
                    { type: 'error' },
                );
            }
        });
    page.voidSection.isHidden = true;
}
