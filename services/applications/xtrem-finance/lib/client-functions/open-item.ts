import * as ui from '@sage/xtrem-ui';
import type { AccountsPayableOpenItemPay as AccountsPayableOpenItemPayPage } from '../pages/accounts-payable-open-item-pay';
import type { AccountsReceivableOpenItemPay as AccountsReceivableOpenItemPayPage } from '../pages/accounts-receivable-open-item-pay';

type AccountsOpenItemPayPage = AccountsPayableOpenItemPayPage | AccountsReceivableOpenItemPayPage;

export function validateForcedAmountPaid(options: {
    pageInstance: AccountsOpenItemPayPage;
    forcedAmountPaid: number | null;
    originalForcedAmountPaid: number | null;
    remainingAmount: number | null;
}): string {
    const isInvoice = ['salesInvoice', 'purchaseInvoice'].includes(options.pageInstance.documentType.value ?? '');
    if (isInvoice && (options.forcedAmountPaid ?? 0) < 0) {
        return ui.localize(
            '@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__forced_amount_paid_wrongly_negative',
            'The forced amount paid needs to be positive.',
        );
    }
    if (!isInvoice && (options.forcedAmountPaid ?? 0) > 0) {
        return ui.localize(
            '@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__forced_amount_paid_wrongly_positive',
            'The forced amount paid needs to be negative.',
        );
    }
    const originalForcedAmountPaid = Math.abs(options.originalForcedAmountPaid ?? 0);
    const forcedBalance = Math.abs(options.forcedAmountPaid ?? 0) - originalForcedAmountPaid;
    if (forcedBalance > Math.abs(options.remainingAmount ?? 0)) {
        const maxForcedAmount =
            (options.pageInstance.transactionAmountDue.value ?? 0) -
            (Math.abs(options.pageInstance.transactionAmountPaidSigned.value ?? 0) - originalForcedAmountPaid);
        return ui.localize(
            '@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__wrong_forced_amount_paid',
            'The forced amount paid needs to be between 0 and {{maxForcedAmount}}.',
            { maxForcedAmount: isInvoice ? maxForcedAmount : -maxForcedAmount },
        );
    }
    return '';
}

export function onChangeForcedAmountPaid(pageInstance: AccountsOpenItemPayPage) {
    pageInstance.forcedAmountPaid.value = Math.abs(pageInstance.forcedAmountPaidSigned.value ?? 0);
    const fullyPaid =
        (pageInstance.transactionAmountDue.value ?? 0) -
            (Math.abs(pageInstance.transactionAmountPaidSigned.value ?? 0) -
                Math.abs(pageInstance.originalForcedAmountPaid.value ?? 0)) ===
        Math.abs(pageInstance.forcedAmountPaidSigned.value ?? 0);
    pageInstance.closeReason.isHidden = !fullyPaid;
    pageInstance.closeText.isHidden = !fullyPaid;
    if (!fullyPaid) {
        pageInstance.closeReason.value = null;
        pageInstance.closeText.value = '';
    }
}

export function onLoadOpenItemPay(pageInstance: AccountsOpenItemPayPage) {
    const isDisabled =
        pageInstance.status.value === 'paid' &&
        (!pageInstance.forcedAmountPaid.value || pageInstance.forcedAmountPaid.value === 0);
    pageInstance.closeText.isDisabled = isDisabled;
    pageInstance.closeText.isHidden = pageInstance.status.value !== 'paid';
    pageInstance.closeReason.isDisabled = isDisabled;
    pageInstance.closeReason.isHidden = pageInstance.status.value !== 'paid';
    pageInstance.forcedAmountPaidSigned.isDisabled = isDisabled;
    pageInstance.remainingTransactionAmount.value = pageInstance.remainingTransactionAmountSigned.value ?? 0;
    pageInstance.originalForcedAmountPaid.value = pageInstance.forcedAmountPaidSigned.value ?? 0;
}

export function getNotFullyPaidFilter() {
    return {
        title: 'Not fully paid',
        graphQLFilter: { status: { _ne: 'paid' }, accountsReceivableInvoice: { postingStatus: 'posted' } },
    };
}

export function getPaidFilter() {
    return {
        title: 'Paid',
        graphQLFilter: { status: { _eq: 'paid' }, accountsReceivableInvoice: { postingStatus: 'posted' } },
    };
}

export function getNotPaidFilter() {
    return {
        title: 'Not paid',
        graphQLFilter: { status: { _eq: 'notPaid' }, accountsReceivableInvoice: { postingStatus: 'posted' } },
    };
}

export function getPartiallyPaidFilter() {
    return {
        title: 'Partially paid',
        graphQLFilter: { status: { _eq: 'partiallyPaid' }, accountsReceivableInvoice: { postingStatus: 'posted' } },
    };
}
