import type { Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { AccountsReceivableOpenItem as AccountsReceivableOpenItemNode } from '@sage/xtrem-finance-api';
import * as ui from '@sage/xtrem-ui';
import type { RecordReceipt } from '../pages/record-receipt';
import type { BaseRecordCreationData } from './interfaces/record';
import {
    getCommonDataForCreation,
    getCommonFilter,
    getCommonLineDataForCreation,
    initRecordCommon,
    onOpenItemRowSelected,
} from './record-common';

function getFilter(page: RecordReceipt, number?: string): Filter<AccountsReceivableOpenItemNode> {
    return {
        ...getCommonFilter(page),
        accountsReceivableInvoice: { postingStatus: 'posted' },
        ...(page.$.queryParameters.openParams ? { accountsReceivableInvoice: { number } } : {}),
    };
}

export async function getResults(page: RecordReceipt, number?: string) {
    page.lines.unselectAllRecords();

    return extractEdges(
        await page.$.graph
            .node('@sage/xtrem-finance/AccountsReceivableOpenItem')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        accountsReceivableInvoice: {
                            _id: true,
                            number: true,
                            origin: true,
                            reference: true,
                            postingDate: true,
                        },
                        penaltyPaymentType: true,
                        discountType: true,
                        discountPaymentBeforeDate: true,
                        dueDate: true,
                        currency: { id: true, symbol: true },
                        transactionAmountPaidSigned: true,
                        penaltyAmount: true,
                        discountAmount: true,
                        amountDue: true,
                        totalAmount: true,
                        remainingCompanyAmount: true,
                        totalCompanyAmount: true,
                        totalCompanyAmountPaid: true,
                    },
                    { filter: getFilter(page, number), first: 500 },
                ),
            )
            .execute(),
    );
}

// Inits the lines fragment of the recordReceipt page in onLoad
export async function initRecordLines(page: RecordReceipt, number: string) {
    if (page.$.queryParameters.openParams) {
        page.lines.value = await getResults(page, number);
    } else {
        page.lines.value = [];
    }
}

// Inits the record receipt page in onLoad
export async function initRecordReceipt(page: RecordReceipt) {
    initRecordCommon(page);
    if (page.$.queryParameters.openParams) {
        const openParams: BaseRecordCreationData = JSON.parse(page.$.queryParameters.openParams as string);
        await initRecordLines(page, openParams.number);
        const lineSysId = page.lines.value[0]?._id;
        onOpenItemRowSelected({ page, lineSysId });
        page.lines.selectRecord(lineSysId);
        page.amount.value = Number(page.totalPaymentApplied.value);
        page.amountAvailableToApply.value = 0.0;
        if (page.currency?.value?.id === page.bankAccount?.value?.currency?.id) {
            page.amountBankCurrency.value = page.amount.value;
        }
    } else {
        page.type.value = 'customer';
    }
}

export async function createReceipt(page: RecordReceipt): Promise<string> {
    const line = page.lines.value[0];
    const payment = await page.$.graph
        .node('@sage/xtrem-finance/Receipt')
        .create(
            { _id: true, number: true },
            {
                data: {
                    ...getCommonDataForCreation(page),
                    businessRelation: page.customer.value?._id ?? '',
                    lines: [
                        {
                            ...getCommonLineDataForCreation(line, page),
                            arOpenItem: line._id,
                            origin: line.accountsReceivableInvoice?.origin,
                        },
                    ],
                },
            },
        )
        .execute();
    return payment.number;
}
