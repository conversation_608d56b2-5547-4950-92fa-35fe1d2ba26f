import type { ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import type { AccountsPayableOpenItem, AccountsReceivableOpenItem } from '@sage/xtrem-finance-api';
import type { AccountsPayableReceivableInvoiceOrigin } from '@sage/xtrem-finance-data-api';
import type { Currency } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
import { merge } from 'lodash';
import type { PaymentInformation as PaymentInformationPageFragment } from '../page-fragments/payment-information';
import type { RecordLines as RecordLinesPageFragment } from '../page-fragments/record-lines';
import type {
    AccountsPayableOpenItemRecord,
    AccountsPayableReceivableOpenItemRecord,
    AccountsReceivableOpenItemRecord,
    BaseRecordCreationData,
    RecordPages,
    RecordPagesOrFragments,
} from './interfaces/record';

export async function calculateAmountInBankCurrency(page: PaymentInformationPageFragment): Promise<number> {
    const rateExists =
        extractEdges(
            await page.$.graph
                .node('@sage/xtrem-master-data/ExchangeRate')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true },
                        {
                            filter: {
                                _or: [
                                    {
                                        base: { _id: page.currency.value?._id },
                                        destination: { _id: page.bankAccount.value?.currency?._id },
                                    },
                                    {
                                        base: { _id: page.bankAccount.value?.currency?._id },
                                        destination: { _id: page.currency.value?._id },
                                    },
                                ],
                            },
                            first: 1,
                        },
                    ),
                )
                .execute(),
        ).length > 0;
    if (rateExists) {
        return (
            (await page.$.graph
                .node('@sage/xtrem-master-data/ExchangeRate')
                .queries.convertRate(true, {
                    base: page.currency.value?.id,
                    destination: page.bankAccount.value?.currency?.id,
                    amount: page.amount.value ?? 0,
                    rateDate: page.date.value,
                })
                .execute()) ?? 0
        );
    }
    return 0;
}

export async function controlAmountInBankCurrency(page: PaymentInformationPageFragment): Promise<void> {
    if (
        page.bankAccount.value &&
        page.currency.value !== page.bankAccount.value?.currency &&
        (page.amount.value ?? 0) !== 0.0 &&
        (page.amountBankCurrency.value ?? 0) !== 0
    ) {
        const calculatedAmountInBankCurrency = await calculateAmountInBankCurrency(page);
        if (
            calculatedAmountInBankCurrency !== 0 &&
            page.amountBankCurrency.value?.toString() !== calculatedAmountInBankCurrency.toString()
        ) {
            page.$.showToast(
                ui.localize(
                    '@sage/xtrem-finance/client_functions__record_common__wrong_amount_in_bank_currency',
                    'The bank currency and calculated amounts are different. Bank amount: {{currencySymbol}}{{bankAmount}}, calculated amount: ({{currencySymbol}}{{amount}}).',
                    {
                        bankAmount: page.amountBankCurrency.value,
                        amount: calculatedAmountInBankCurrency,
                        currencySymbol: page.bankAccount.value?.currency?.symbol,
                    },
                ),
                { timeout: 10000 },
            );
        }
    }
}

export async function checkCurrencies(parameters: {
    page: PaymentInformationPageFragment;
    currency: ExtractEdgesPartial<Currency> | undefined;
    bankAccountCurrency: ExtractEdgesPartial<Currency> | undefined;
}) {
    const { currency, bankAccountCurrency, page } = parameters;
    if (!currency || !bankAccountCurrency) {
        return;
    }
    if (currency?.id !== bankAccountCurrency?.id) {
        page.$.showToast(
            ui.localize(
                '@sage/xtrem-finance/client_functions__record_common__different_currencies',
                'The chosen currency is different to the currency associated with the bank account.',
            ),
            { type: 'warning' },
        );
    }
    await controlAmountInBankCurrency(page);
    page.amount.prefix = currency?.symbol ?? '';
    page.amountBankCurrency.prefix = bankAccountCurrency?.symbol ?? '';
    if (currency?.id === bankAccountCurrency?.id) {
        page.amountBankCurrency.value = page.amount.value;
    }
}

export function checkIfFutureDate(date: string) {
    const chosenDate = new Date(date);
    const now = new Date();
    return chosenDate > now
        ? ui.localize(
              '@sage/xtrem-finance/client_functions__record_common__invalid_date',
              'You need to select a date in the past.',
          )
        : undefined;
}

export function resetLines(page: RecordPages | PaymentInformationPageFragment): void {
    page.lines.value = [];
    page.totalPaymentApplied.value = 0.0;
    page.amountAvailableToApply.value = page.amount.value ?? 0.0;
}

// Validates the paymentAmount field of a line in the 'Record receipt' and 'Record payment' pages:
// If the field is empty, a warning toast is shown.
// If the field is not empty and the amount is less than 0, an error toast is shown.
export function validatePaymentAmount(parameters: {
    page: RecordLinesPageFragment;
    val: number;
    rowData: AccountsPayableReceivableOpenItemRecord;
}): string | undefined {
    // Prevent a paymentAmount less than zero
    if (parameters.val < 0)
        return ui.localize(
            '@sage/xtrem-finance/client_functions__record_common__negative_payment_amount_error',
            'Enter a payment amount greater than 0.',
        );
    // Prevent a paymentAmount + discountAmount + adjustmentAmount greater than amount due
    const adjustmentAmount = parameters.rowData.adjustmentAmount ?? 0;
    const discountAmount = Math.abs(Number(parameters.rowData.discountPaymentAmount ?? 0));
    const amountDue = Math.abs(Number(parameters.rowData?.amountDue ?? 0));
    if (parameters.val + discountAmount + adjustmentAmount > amountDue)
        return ui.localize(
            '@sage/xtrem-finance/client_functions__record_common__wrong_payment_amount_error',
            `The invoice payment needs to be less than the amount that's due.`,
        );
    return undefined;
}

// Validates the creditAmount field of a line in the 'Record receipt' and 'Record payment' pages:
// If the field is empty, a warning toast is shown.
// If the field is not empty and the amount is less than 0, an error toast is shown.
export function validateCreditAmount(parameters: {
    page: RecordLinesPageFragment;
    val: number;
    rowData: AccountsPayableReceivableOpenItemRecord;
}): string | undefined {
    // Prevent a creditAmount less than zero
    if (parameters.val < 0)
        return ui.localize(
            '@sage/xtrem-finance/client_functions__record_common__negative_credit_amount_error',
            'Enter a credit amount greater than 0.',
        );
    // Prevent a creditAmount + adjustmentAmount greater than amount due
    const adjustmentAmount = parameters.rowData.adjustmentAmount ?? 0;
    const amountDue = Math.abs(Number(parameters.rowData?.amountDue ?? 0));
    if (parameters.val + adjustmentAmount > amountDue)
        return ui.localize(
            '@sage/xtrem-finance/client_functions__record_common__wrong_credit_amount_error',
            `The credited amount needs to be less than the amount that's due.`,
        );
    return undefined;
}

export function onFinancialSiteChange(page: PaymentInformationPageFragment) {
    page.bankAccount.value =
        page.financialSite.value?.legalCompany?.bankAccount?.financialSite?.id === page.financialSite.value?.id
            ? (page.financialSite.value?.legalCompany?.bankAccount ?? null)
            : null;
    if (page.currency?.value?.id === page.bankAccount?.value?.currency?.id) {
        page.amountBankCurrency.value = page.amount.value;
    }
    resetLines(page);
}

export function isAmountInBankCurrencyHidden(page: PaymentInformationPageFragment): boolean {
    const isCustomerOrSupplierUnselected = page.type.value === 'customer' ? !page.customer.value : !page.supplier.value;
    return (
        !page.financialSite.value ||
        isCustomerOrSupplierUnselected ||
        !page.bankAccount.value ||
        page.currency.value?.id === page.bankAccount.value?.currency?.id
    );
}

// Selects or unselects a line depending on if an amount was entered into the fieldName of the line or not.
// If the line is unselected, the payment, credit, discount, penalty and adjustment amount fields are set to undefined
export function selectUnselectLine(parameters: {
    page: RecordLinesPageFragment;
    tableField: ui.fields.Table<AccountsPayableReceivableOpenItemRecord>;
    line: AccountsPayableReceivableOpenItemRecord;
    fieldName: keyof AccountsReceivableOpenItemRecord | keyof AccountsPayableOpenItemRecord;
}) {
    if (
        !parameters.tableField.selectedRecords.includes(parameters.line._id) &&
        parameters.line[parameters.fieldName] !== 0
    ) {
        parameters.tableField.selectRecord(parameters.line._id);
    }
    if (
        parameters.fieldName !== 'penaltyPaymentAmount' &&
        (!parameters.line[parameters.fieldName] || parameters.line[parameters.fieldName] === 0)
    ) {
        parameters.tableField.unselectRecord(parameters.line._id);
        const record = parameters.page.lines.getRecordValue(parameters.line._id);
        if (record) {
            record.paymentAmount = undefined;
            record.creditAmount = undefined;
            record.discountPaymentAmount = undefined;
            record.penaltyPaymentAmount = undefined;
            record.adjustmentAmount = undefined;
            parameters.page.lines.setRecordValue(record);
        }
    }
}

// Calculates the balance of payment and credit amount for all lines and updates the total payment applied and amount available to apply
export function calculateTotalAppliedAndAvailableToApply(page: RecordLinesPageFragment): void {
    const totalAmountApplied = page.lines.value.reduce(
        (totalAmount, line) =>
            totalAmount +
            Number(line.paymentAmount ?? (line.creditAmount ?? 0) * -1) +
            Number(line.penaltyPaymentAmount ?? 0),
        0,
    );
    page.totalPaymentApplied.value = totalAmountApplied;
    page.amountAvailableToApply.value = (page.amount.value ?? 0) - totalAmountApplied;
}

// Common function to handle the change of an amount field in a line of the 'Record payment' page:
// if the new amount is greater than the amountToApply and it is the 'paymentAmount' or 'creditAmount' or
// 'penaltyPaymentAmount', it is set to the amountToApply.
// If the payment amount is set to zero, discount, penalty and adjustment amounts are set to zero.
// The line is selected or unselected depending on if the new amount is 0 or not.
// The total payment applied and amount available to apply are recalculated.
function onLineAmountChange(parameters: {
    page: RecordLinesPageFragment;
    tableField: ui.fields.Table<AccountsPayableReceivableOpenItemRecord>;
    rowData: AccountsPayableReceivableOpenItemRecord;
    fieldName: keyof AccountsReceivableOpenItemRecord | keyof AccountsPayableOpenItemRecord;
    newAmount: number;
    amountToCompare: number;
}) {
    const record = parameters.page.lines.getRecordValue(parameters.rowData._id);
    if (!record) {
        return;
    }
    if (parameters.fieldName === 'paymentAmount' && (parameters.rowData.paymentAmount ?? 0) === 0) {
        record.discountPaymentAmount = 0;
        record.adjustmentAmount = 0;
        record.penaltyPaymentAmount = 0;
    } else if (parameters.newAmount > parameters.amountToCompare) {
        if (parameters.fieldName === 'paymentAmount') {
            record.paymentAmount = parameters.amountToCompare;
        }
        if (parameters.fieldName === 'creditAmount') {
            record.creditAmount = parameters.amountToCompare;
        }
        if (parameters.fieldName === 'penaltyPaymentAmount') {
            record.penaltyPaymentAmount = parameters.amountToCompare;
        }
    }
    parameters.page.lines.setRecordValue(record);
    parameters.rowData = merge(parameters.rowData, record);
    selectUnselectLine({
        page: parameters.page as RecordLinesPageFragment,
        tableField: parameters.tableField,
        line: parameters.rowData,
        fieldName: parameters.fieldName,
    });
    calculateTotalAppliedAndAvailableToApply(parameters.page);
}

// Calculates the balance of payment and credit amount for all lines except the one with the given _id
function calculateTotalAmountApplied(page: RecordLinesPageFragment, sysId: string): number {
    return (
        page.lines.value
            .filter(line => line._id !== sysId && page.lines.selectedRecords.includes(line._id))
            .reduce(
                (totalAmount, line) =>
                    totalAmount +
                    Number(line.paymentAmount ?? (line.creditAmount ?? 0) * -1) +
                    Number(line.penaltyPaymentAmount ?? 0),
                0,
            ) ?? 0.0
    );
}

// Function to handle the change of the payment amount field in a line of the 'Record payment' page:
// calls common function onLineAmountChange
export function onPaymentAmountChange(page: RecordLinesPageFragment, rowData: AccountsPayableReceivableOpenItemRecord) {
    let availableToApply = (rowData.paymentAmount ?? 0) + (rowData.penaltyPaymentAmount ?? 0);
    if (page.amount.value !== 0) {
        const appliedExludingRowData = calculateTotalAmountApplied(page, rowData._id);
        availableToApply = (page.amount.value ?? 0) - appliedExludingRowData - (rowData.penaltyPaymentAmount ?? 0);
    }
    onLineAmountChange({
        page,
        tableField: page.lines as unknown as ui.fields.Table<AccountsPayableReceivableOpenItemRecord>,
        rowData,
        fieldName: 'paymentAmount',
        newAmount: rowData.paymentAmount ?? 0,
        amountToCompare: availableToApply,
    });
}

// Function to handle the change of the credit amount field in a line of the 'Record receipt' page:
// calls common function onLineAmountChange
export function onCreditAmountChange(page: RecordLinesPageFragment, rowData: AccountsPayableReceivableOpenItemRecord) {
    let availableToApply = rowData.creditAmount ?? 0;
    if (page.amount.value !== 0) {
        const appliedExludingRowData = calculateTotalAmountApplied(page, rowData._id);
        availableToApply = Math.abs((page.amount.value ?? 0) - appliedExludingRowData);
    }
    onLineAmountChange({
        page,
        tableField: page.lines as unknown as ui.fields.Table<AccountsPayableReceivableOpenItemRecord>,
        rowData,
        fieldName: 'creditAmount',
        newAmount: rowData.creditAmount ?? 0,
        amountToCompare: availableToApply,
    });
}

function getAmounts(parameters: {
    page: RecordPagesOrFragments;
    record: PartialCollectionValueWithIds<AccountsPayableReceivableOpenItemRecord>;
}): {
    amount: number;
    amountAvailableToApply: number;
    amountDue: number;
    decimalDigits: number;
} {
    return {
        decimalDigits: parameters.page.currency.value?.decimalDigits ?? 2,
        amount: parameters.page.amount.value ?? 0.0,
        amountAvailableToApply: parameters.page.amountAvailableToApply.value ?? 0.0,
        amountDue: Math.abs(Number(parameters.record.amountDue)),
    };
}

function calculatePercentagePenaltyAmounts(parameters: {
    page: RecordPagesOrFragments;
    origin: AccountsPayableReceivableInvoiceOrigin;
    record: PartialCollectionValueWithIds<AccountsPayableReceivableOpenItemRecord>;
    paymentAmount: number;
    discountPaymentAmount: number;
}): {
    finalPaymentAmount: number;
    penaltyPaymentAmount: number;
    finalDiscountPaymentAmount: number;
} {
    let { paymentAmount, discountPaymentAmount } = parameters;
    let penaltyPaymentAmount = 0.0;

    const { discountType } = parameters.record;
    const discountAmount = Number(parameters.record.discountAmount);
    const { decimalDigits, amount, amountAvailableToApply, amountDue } = getAmounts({
        page: parameters.page,
        record: parameters.record,
    });
    const penaltyAmount = Number(parameters.record.penaltyAmount);

    const roundedPenaltyAmount = Decimal.roundAt(amountDue * (penaltyAmount / 100), decimalDigits) ?? 0.0;
    const roundedDiscountAmount = Decimal.roundAt((paymentAmount * discountAmount) / 100, decimalDigits) ?? 0.0;

    if (amount === 0 || amountAvailableToApply >= amountDue + roundedPenaltyAmount) {
        // if a discount was already applied, we use the already calculated payment amount
        paymentAmount = paymentAmount !== 0.0 ? paymentAmount : amountDue;
        penaltyPaymentAmount = roundedPenaltyAmount;
    } else {
        paymentAmount = Decimal.roundAt((amountAvailableToApply * 100) / (100 + penaltyAmount), decimalDigits) ?? 0.0;
        penaltyPaymentAmount = amountAvailableToApply - paymentAmount;
        if (discountPaymentAmount !== 0.0 && discountType === 'percentage') {
            discountPaymentAmount = roundedDiscountAmount;
        }
    }

    return {
        finalPaymentAmount: paymentAmount ?? 0,
        penaltyPaymentAmount: penaltyPaymentAmount ?? 0,
        finalDiscountPaymentAmount: discountPaymentAmount,
    };
}

function calculateAmountPenaltyAmounts(parameters: {
    page: RecordPagesOrFragments;
    origin: AccountsPayableReceivableInvoiceOrigin;
    record: PartialCollectionValueWithIds<AccountsPayableReceivableOpenItemRecord>;
    paymentAmount: number;
}): {
    finalPaymentAmount: number;
    penaltyPaymentAmount: number;
} {
    let { paymentAmount } = parameters;
    let penaltyPaymentAmount = 0.0;

    const { decimalDigits, amount, amountAvailableToApply, amountDue } = getAmounts({
        page: parameters.page,
        record: parameters.record,
    });
    const penaltyAmount = Decimal.roundAt(Number(parameters.record.penaltyAmount), decimalDigits) ?? 0.0;
    const transactionAmountPaid = Number(parameters.record.transactionAmountPaidSigned);

    // we apply penalty of type 'amount' only if there was no payment so far
    if (transactionAmountPaid === 0) {
        // enough amount to apply left?
        if (amount === 0 || amountAvailableToApply >= amountDue + penaltyAmount) {
            // if a discount was already applied, we use the already calculated payment amount
            paymentAmount = paymentAmount !== 0.0 ? paymentAmount : amountDue;
        } else {
            paymentAmount = amountAvailableToApply !== 0 ? amountAvailableToApply - penaltyAmount : 0;
        }
        penaltyPaymentAmount = paymentAmount !== 0 ? penaltyAmount : 0;
    } else if (amount === 0 || amountAvailableToApply >= amountDue) {
        paymentAmount = amountDue;
    } else {
        paymentAmount = amountAvailableToApply;
    }
    return {
        finalPaymentAmount: paymentAmount ?? 0,
        penaltyPaymentAmount: penaltyPaymentAmount ?? 0,
    };
}

function calculatePenaltyAmountsForInvoice(parameters: {
    page: RecordPagesOrFragments;
    origin: AccountsPayableReceivableInvoiceOrigin;
    record: PartialCollectionValueWithIds<AccountsPayableReceivableOpenItemRecord>;
    paymentAmount: number;
    discountPaymentAmount: number;
}): {
    finalPaymentAmount: number;
    penaltyPaymentAmount: number;
    finalDiscountPaymentAmount: number;
} {
    const penaltyType = parameters.record.penaltyPaymentType;
    const date = parameters.page.date.value ? DateValue.parse(parameters.page.date.value) : null;
    const dueDate = parameters.record.dueDate ? DateValue.parse(parameters.record.dueDate) : null;
    const { paymentAmount, discountPaymentAmount } = parameters;
    const penaltyPaymentAmount = 0.0;

    // if payment/receipt date before dueDate of the line, no penalty
    if (date && dueDate && date.compare(dueDate) <= 0) {
        return {
            finalPaymentAmount: paymentAmount,
            penaltyPaymentAmount,
            finalDiscountPaymentAmount: discountPaymentAmount,
        };
    }

    // if payment/receipt date after dueDate of the line and a penalty is defined for the open item apply penalty
    if (penaltyType === 'percentage') {
        return calculatePercentagePenaltyAmounts(parameters);
    }
    if (penaltyType === 'amount') {
        return { ...calculateAmountPenaltyAmounts(parameters), finalDiscountPaymentAmount: discountPaymentAmount };
    }

    return {
        finalPaymentAmount: paymentAmount,
        penaltyPaymentAmount,
        finalDiscountPaymentAmount: discountPaymentAmount,
    };
}

function calculateAmountDiscountAmounts(parameters: {
    page: RecordPagesOrFragments;
    origin: AccountsPayableReceivableInvoiceOrigin;
    record: PartialCollectionValueWithIds<AccountsPayableReceivableOpenItemRecord>;
}): {
    paymentAmount: number;
    discountPaymentAmount: number;
} {
    const { decimalDigits, amount, amountAvailableToApply, amountDue } = getAmounts({
        page: parameters.page,
        record: parameters.record,
    });
    const discountAmount = Decimal.roundAt(Number(parameters.record.discountAmount), decimalDigits) ?? 0.0;
    const transactionAmountPaid = Number(parameters.record.transactionAmountPaidSigned);

    let paymentAmount = 0.0;
    let discountPaymentAmount = 0.0;

    // we apply discount of type 'amount' only if there was no payment so far
    if (transactionAmountPaid === 0) {
        // enough amount to apply left? If yes apply discount
        if (amount === 0 || amountAvailableToApply >= amountDue - discountAmount) {
            paymentAmount = amountDue - discountAmount;
        } else {
            paymentAmount = amountAvailableToApply;
        }
        discountPaymentAmount = paymentAmount !== 0 ? discountAmount : 0;
    } else if (amount === 0 || amountAvailableToApply >= amountDue) {
        paymentAmount = amountDue;
    } else {
        paymentAmount = amountAvailableToApply;
    }
    return {
        paymentAmount,
        discountPaymentAmount,
    };
}

function calculatePercentageDiscountAmounts(parameters: {
    page: RecordPagesOrFragments;
    origin: AccountsPayableReceivableInvoiceOrigin;
    record: PartialCollectionValueWithIds<AccountsPayableReceivableOpenItemRecord>;
}): {
    paymentAmount: number;
    discountPaymentAmount: number;
} {
    const { decimalDigits, amount, amountAvailableToApply, amountDue } = getAmounts({
        page: parameters.page,
        record: parameters.record,
    });
    const discountAmount = Number(parameters.record.discountAmount);

    let paymentAmount = 0.0;
    let discountPaymentAmount = 0.0;

    const roundedAmount = Decimal.roundAt(amountDue * (1 - discountAmount / 100), decimalDigits) ?? 0.0;

    if (amount === 0 || amountAvailableToApply >= roundedAmount) {
        paymentAmount = roundedAmount;
        discountPaymentAmount = amountDue - paymentAmount;
    } else {
        paymentAmount = amountAvailableToApply;
        discountPaymentAmount =
            Decimal.roundAt((paymentAmount * 100) / (100 - discountAmount) - paymentAmount, decimalDigits) ?? 0.0;
    }
    return {
        paymentAmount,
        discountPaymentAmount,
    };
}

function calculateDiscountAmountsForInvoice(parameters: {
    page: RecordPagesOrFragments;
    origin: AccountsPayableReceivableInvoiceOrigin;
    record: PartialCollectionValueWithIds<AccountsPayableReceivableOpenItemRecord>;
}): {
    paymentAmount: number;
    discountPaymentAmount: number;
} {
    const amountAvailableToApply = parameters.page.amountAvailableToApply.value ?? 0.0;
    const amount = parameters.page.amount.value ?? 0.0;
    const amountDue = Math.abs(Number(parameters.record.amountDue));
    const discountType = parameters.record.discountType ?? '';
    const discountPaymentBeforeDate = parameters.record.discountPaymentBeforeDate
        ? DateValue.parse(parameters.record.discountPaymentBeforeDate)
        : null;
    const date = parameters.page.date.value ? DateValue.parse(parameters.page.date.value) : null;

    const paymentAmount = amount === 0 ? amountDue : Math.min(amountDue, amountAvailableToApply);
    const discountPaymentAmount = 0.0;

    // if payment/receipt date after discountPaymentBeforeDate, no discount
    if (date && discountPaymentBeforeDate && date.compare(discountPaymentBeforeDate) > 0) {
        return {
            paymentAmount,
            discountPaymentAmount,
        };
    }

    // if payment/receipt date before discountPaymentBeforeDate, apply discount
    if (discountType === 'percentage') {
        return calculatePercentageDiscountAmounts(parameters);
    }
    if (discountType === 'amount') {
        return calculateAmountDiscountAmounts(parameters);
    }

    return {
        paymentAmount,
        discountPaymentAmount,
    };
}

function calculateAmountsForInvoice(parameters: {
    page: RecordPagesOrFragments;
    origin: AccountsPayableReceivableInvoiceOrigin;
    record: PartialCollectionValueWithIds<AccountsPayableReceivableOpenItemRecord>;
}): {
    amountToApply: number;
    paymentAmount?: number;
    discountPaymentAmount?: number;
    penaltyPaymentAmount?: number;
} {
    const { paymentAmount, discountPaymentAmount } = calculateDiscountAmountsForInvoice(parameters);
    const { finalPaymentAmount, penaltyPaymentAmount, finalDiscountPaymentAmount } = calculatePenaltyAmountsForInvoice({
        ...parameters,
        paymentAmount,
        discountPaymentAmount,
    });
    return {
        amountToApply: finalPaymentAmount + penaltyPaymentAmount,
        paymentAmount: finalPaymentAmount,
        discountPaymentAmount: finalDiscountPaymentAmount,
        penaltyPaymentAmount,
    };
}

function calculateAmountsForCreditMemo(parameters: {
    amount: number;
    amountDue: number;
    totalPaymentApplied: number;
}): {
    amountToApply: number;
    creditAmount?: number;
} {
    let amountToApply =
        parameters.amount === 0 || parameters.totalPaymentApplied === 0
            ? Math.abs(parameters.amountDue)
            : Math.min(Math.abs(parameters.amountDue), parameters.totalPaymentApplied);
    const creditAmount = amountToApply > 0 ? amountToApply : undefined;
    amountToApply *= -1;

    return { amountToApply, creditAmount };
}

function calculateAmounts(parameters: {
    page: RecordPagesOrFragments;
    origin: AccountsPayableReceivableInvoiceOrigin;
    record: PartialCollectionValueWithIds<AccountsPayableReceivableOpenItemRecord>;
}): {
    amountToApply: number;
    paymentAmount?: number;
    creditAmount?: number;
    discountPaymentAmount?: number;
    penaltyPaymentAmount?: number;
} {
    if (parameters.origin === 'invoice') {
        return calculateAmountsForInvoice(parameters);
    }

    if (parameters.origin === 'creditMemo') {
        return calculateAmountsForCreditMemo({
            amount: parameters.page.amount.value ?? 0.0,
            amountDue: Number(parameters.record.amountDue),
            totalPaymentApplied: parameters.page.totalPaymentApplied.value ?? 0.0,
        });
    }

    return { amountToApply: 0 };
}

function setPaymentAmountCreditAmount(parameters: {
    page: RecordPagesOrFragments;
    origin: AccountsPayableReceivableInvoiceOrigin;
    record: PartialCollectionValueWithIds<AccountsPayableReceivableOpenItemRecord>;
    lineSysId: string;
    amountToApply: number;
    paymentAmount?: number;
    creditAmount?: number;
    discountPaymentAmount?: number;
    penaltyPaymentAmount?: number;
}) {
    const { paymentAmount, creditAmount, discountPaymentAmount, penaltyPaymentAmount } = parameters;
    if (parameters.origin === 'invoice') {
        parameters.record.paymentAmount = paymentAmount && paymentAmount > 0 ? paymentAmount : undefined;
    }
    if (parameters.origin === 'creditMemo') {
        parameters.record.creditAmount = creditAmount && creditAmount > 0 ? creditAmount : undefined;
    }
    parameters.record.discountPaymentAmount =
        discountPaymentAmount && discountPaymentAmount > 0 ? discountPaymentAmount : undefined;
    parameters.record.penaltyPaymentAmount =
        penaltyPaymentAmount && penaltyPaymentAmount > 0 ? penaltyPaymentAmount : undefined;
    parameters.page.lines.setRecordValue(parameters.record);

    parameters.page.amountAvailableToApply.value = parameters.page.amountAvailableToApply.value ?? 0.0;
    parameters.page.totalPaymentApplied.value = parameters.page.totalPaymentApplied.value ?? 0.0;
    parameters.page.amountAvailableToApply.value -= parameters.amountToApply;
    parameters.page.totalPaymentApplied.value += parameters.amountToApply;

    if (parameters.amountToApply === 0) {
        parameters.page.lines.unselectRecord(parameters.lineSysId);
    }
}

// Handles the selection of a line in the 'Record receipt' and 'Record payment' page:
// If the line is selected, the payment or credit amount field is set to the amount due of the line if the amount
// due isn't greater than the remaining available amount to apply otherwise it is set to the remaining available amount to apply.
// The total payment applied and amount available to apply are recalculated.
export function onOpenItemRowSelected(parameters: { page: RecordPagesOrFragments; lineSysId: string }) {
    const record = parameters.page.lines.getRecordValue(parameters.lineSysId);
    if (!record) {
        return;
    }
    const origin =
        parameters.page.type.value === 'supplier'
            ? 'accountsPayableInvoice' in record && record.accountsPayableInvoice?.origin
            : 'accountsReceivableInvoice' in record && record.accountsReceivableInvoice?.origin;

    const { amountToApply, paymentAmount, creditAmount, discountPaymentAmount, penaltyPaymentAmount } =
        calculateAmounts({
            page: parameters.page,
            origin,
            record,
        });

    setPaymentAmountCreditAmount({
        ...parameters,
        origin,
        record,
        amountToApply,
        paymentAmount,
        creditAmount,
        discountPaymentAmount,
        penaltyPaymentAmount,
    });
}

// Handles the unselection of a line in the 'Record payment' page:
// The total payment applied and amount available to apply are recalculated.
// The payment and credit amount fields of the line are set to undefined.
export function onOpenItemRowUnSelected(parameters: { page: RecordLinesPageFragment; lineSysId: string }) {
    const record = parameters.page.lines.getRecordValue(parameters.lineSysId);
    if (!record) {
        return;
    }
    parameters.page.amountAvailableToApply.value = parameters.page.amountAvailableToApply.value ?? 0.0;
    parameters.page.totalPaymentApplied.value = parameters.page.totalPaymentApplied.value ?? 0.0;

    const paymentAmount = Number(record.paymentAmount ?? 0.0) + Number(record.penaltyPaymentAmount ?? 0.0);
    const creditAmount = Number(record.creditAmount ?? 0.0);

    if (record.accountsReceivableInvoice?.origin === 'invoice') {
        parameters.page.amountAvailableToApply.value += paymentAmount;
        parameters.page.totalPaymentApplied.value -= paymentAmount;
    }
    if (record.accountsReceivableInvoice?.origin === 'creditMemo') {
        parameters.page.amountAvailableToApply.value -= creditAmount;
        parameters.page.totalPaymentApplied.value += creditAmount;
    }

    if (record.accountsPayableInvoice?.origin === 'invoice') {
        parameters.page.amountAvailableToApply.value += paymentAmount;
        parameters.page.totalPaymentApplied.value -= paymentAmount;
    }
    if (record.accountsPayableInvoice?.origin === 'creditMemo') {
        parameters.page.amountAvailableToApply.value -= creditAmount;
        parameters.page.totalPaymentApplied.value += creditAmount;
    }
    record.paymentAmount = undefined;
    record.creditAmount = undefined;
    record.discountPaymentAmount = undefined;
    record.penaltyPaymentAmount = undefined;
    record.adjustmentAmount = undefined;
    parameters.page.lines.setRecordValue(record);
}

function isPaymentAmountGreaterThanZero(page: RecordPages): boolean {
    if (page.amount.value && page.amount.value <= 0) {
        page.$.showToast(
            ui.localize(
                '@sage/xtrem-finance/pages__record_receipt__payment_amount_must_be_positive',
                'Enter a payment amount greater than 0.',
            ),
            { type: 'error', timeout: 10000 },
        );
        return false;
    }
    return true;
}

function isAmountInBankGreaterThanZero(page: RecordPages): boolean {
    if (!page.amountBankCurrency.value || (page.amountBankCurrency.value && page.amountBankCurrency.value < 0)) {
        page.$.showToast(
            ui.localize(
                '@sage/xtrem-finance/pages__record_receipt__bank_amount_must_be_positive',
                'Enter an amount in bank currency greater than 0.',
            ),
            { type: 'error', timeout: 10000 },
        );
        return false;
    }
    return true;
}

function isPaymentAmountSameAsTotalPayment(page: RecordPages): boolean {
    if (page.amount.value && page.amount.value !== page.totalPaymentApplied.value) {
        page.$.showToast(
            ui.localize(
                '@sage/xtrem-finance/pages__record_receipt__payment_amount_must_be_total_payment_applied',
                'The payment amount needs to be the same as the total payment applied.',
            ),
            { type: 'error', timeout: 10000 },
        );
        return false;
    }
    return true;
}

function setAmountBankCurrency(header: {
    amountBankCurrency: number | null;
    amount: number | null;
    currency: ExtractEdgesPartial<Currency> | null;
    bankAccountCurrency?: ExtractEdgesPartial<Currency>;
}) {
    if (
        header.amountBankCurrency &&
        header.amountBankCurrency <= 0 &&
        header.currency?.id === header.bankAccountCurrency?.id
    ) {
        return header.amount;
    }
    return header.amountBankCurrency;
}

export function validateAmounts(page: RecordPages): boolean {
    if (!isPaymentAmountGreaterThanZero(page) || !isAmountInBankGreaterThanZero(page)) {
        return false;
    }
    page.amountBankCurrency.value = setAmountBankCurrency({
        amountBankCurrency: page.amountBankCurrency.value,
        amount: page.amount.value,
        currency: page.currency.value,
        bankAccountCurrency: page.bankAccount.value?.currency,
    });
    if (!isPaymentAmountSameAsTotalPayment(page)) {
        return false;
    }
    return true;
}

export function getCommonFilter(
    page: RecordPages,
): Filter<AccountsPayableOpenItem> | Filter<AccountsReceivableOpenItem> {
    const filter = { status: { _ne: 'paid' } };
    if (page.financialSite.value && (page.customer.value || page.supplier.value) && page.currency.value) {
        merge(filter, {
            financialSite: { _id: page.financialSite.value._id },
            businessEntity: {
                _id:
                    page.type.value === 'customer'
                        ? (page.customer.value?.businessEntity?._id ?? '')
                        : (page.supplier.value?.businessEntity?._id ?? ''),
            },
            currency: { _id: page.currency.value._id },
        });
    }
    return filter;
}

// Inits the recordReceipt/recordPayment page in onLoad
export function initPaymentInformation(page: PaymentInformationPageFragment): void {
    page.date.title =
        page.type.value === 'customer'
            ? ui.localize('@sage/xtrem-finance/page_fragments__payment_information__date_received', 'Date received')
            : ui.localize('@sage/xtrem-finance/page_fragments__payment_information__date_issued', 'Date issued');
}

export function isAdjustmentAmountReadOnly(
    page: RecordLinesPageFragment,
    rowData: AccountsPayableReceivableOpenItemRecord,
) {
    const origin =
        page.type.value === 'supplier'
            ? rowData.accountsPayableInvoice?.origin
            : rowData.accountsReceivableInvoice?.origin;

    if (origin === 'invoice') {
        return !rowData.paymentAmount || rowData.paymentAmount === 0.0;
    }

    return !rowData.creditAmount || rowData.creditAmount === 0.0;
}

// Function to handle the change of the penalty payment amount field in a line of the 'Record payment' or 'Record receipt' page:
export function onPenaltyAmountChange(page: RecordLinesPageFragment, rowData: AccountsPayableReceivableOpenItemRecord) {
    let availableToApply = (rowData.paymentAmount ?? 0) + (rowData.penaltyPaymentAmount ?? 0);
    if (page.amount.value !== 0) {
        const appliedExludingRowData = calculateTotalAmountApplied(page, rowData._id);
        availableToApply = (page.amount.value ?? 0) - appliedExludingRowData - (rowData.paymentAmount ?? 0);
    }
    onLineAmountChange({
        page,
        tableField: page.lines as unknown as ui.fields.Table<AccountsPayableReceivableOpenItemRecord>,
        rowData,
        fieldName: 'penaltyPaymentAmount',
        newAmount: rowData.penaltyPaymentAmount ?? 0,
        amountToCompare: availableToApply,
    });
}

// Handles the change of the payment amount field in the header of the 'Record receipt' and 'Record payment' pages:
// If the amount is less than or equal to 0 or less than the total payment applied, the total payment applied is set to 0,
// the amount available to apply is set to the new amount and the payment and credit amount fields of all lines are set to undefined
// and all lines are unselected.
// Otherwise, the amount available to apply is recalculated
export function onAmountChange(page: PaymentInformationPageFragment | RecordPages) {
    const amount = page.amount.value ?? 0;
    if (!page.$.queryParameters.openParams) {
        if (amount <= 0 || amount < (page.totalPaymentApplied.value ?? 0.0)) {
            page.totalPaymentApplied.value = 0.0;
            page.amountAvailableToApply.value = amount > 0 ? amount : 0.0;
            page.lines.value.forEach(line => {
                const record = page.lines.getRecordValue(line._id);
                if (record) {
                    record.paymentAmount = undefined;
                    record.creditAmount = undefined;
                    record.discountPaymentAmount = undefined;
                    record.penaltyPaymentAmount = undefined;
                    record.adjustmentAmount = undefined;
                    page.lines.setRecordValue(record);
                }
            });
            page.lines.unselectAllRecords();
            return;
        }
        const availableToApply = page.amountAvailableToApply.value ?? 0;
        const difference = amount - (availableToApply + (page.totalPaymentApplied.value ?? 0.0));
        page.amountAvailableToApply.value = availableToApply + difference;
    } else {
        const lineSysId = page.lines.value[0]?._id;
        page.amountAvailableToApply.value = page.amount.value;
        page.totalPaymentApplied.value = 0.0;
        onOpenItemRowSelected({ page, lineSysId });
        page.lines.selectRecord(lineSysId);
    }
    if (page.currency?.value?.id === page.bankAccount.value?.currency?.id) {
        page.amountBankCurrency.value = amount;
    }
}

// Common part of init of the record receipt or record payment page in onLoad
export function initRecordCommon(page: RecordPages) {
    if (page.$.queryParameters.openParams) {
        const openParams: BaseRecordCreationData = JSON.parse(page.$.queryParameters.openParams as string);
        page.type.value = openParams.type;
        page.bankAccount.value = openParams.bankAccount;
        page.financialSite.value = openParams.financialSite;
        page.customer.value = page.type.value === 'customer' ? openParams.baseBusinessRelation : null;
        page.supplier.value = page.type.value === 'supplier' ? openParams.baseBusinessRelation : null;
        page.date.value = openParams.date;
        page.currency.value = openParams.currency;
        page.amountAvailableToApply.value = 0.0;
        page.totalPaymentApplied.value = 0.0;
    } else {
        page.date.value = DateValue.today().toString();
        page.amount.value = 0.0;
        page.amountAvailableToApply.value = 0.0;
        page.totalPaymentApplied.value = 0.0;
    }
}

// Common data payload for payment/receipt creation
export function getCommonDataForCreation(page: RecordPages) {
    return {
        type: page.type.value,
        bankAccount: page.bankAccount.value?._id,
        financialSite: page.financialSite.value?._id,
        paymentDate: page.date.value ?? undefined,
        paymentMethod: page.paymentMethod.value,
        reference: page.reference.value ?? '',
        currency: page.currency.value?._id,
        amount: page.amount.value ?? 0,
        amountBankCurrency: page.amountBankCurrency.value ?? 0,
    };
}

// Common data payload for payment/receipt line creation
export function getCommonLineDataForCreation(
    line: PartialCollectionValueWithIds<AccountsPayableOpenItemRecord | AccountsReceivableOpenItemRecord>,
    page: RecordPages,
) {
    return {
        amount: line.paymentAmount ?? 0,
        discountAmount: line.discountPaymentAmount ?? 0,
        penaltyAmount: line.penaltyPaymentAmount ?? 0,
        adjustmentAmount: line.adjustmentAmount ?? 0,
        amountBankCurrency: page.amountBankCurrency.value ?? 0,
    };
}
