import type { Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { AccountsPayableOpenItem as AccountsPayableOpenItemNode } from '@sage/xtrem-finance-api';
import * as ui from '@sage/xtrem-ui';
import type { RecordPayment } from '../pages/record-payment';
import type { BaseRecordCreationData } from './interfaces/record';
import {
    getCommonDataForCreation,
    getCommonFilter,
    getCommonLineDataForCreation,
    initRecordCommon,
    onOpenItemRowSelected,
} from './record-common';

function getFilter(page: RecordPayment, number?: string): Filter<AccountsPayableOpenItemNode> {
    return {
        ...getCommonFilter(page),
        accountsPayableInvoice: { postingStatus: 'posted' },
        ...(page.$.queryParameters.openParams ? { accountsPayableInvoice: { number } } : {}),
    };
}

export async function getResults(page: RecordPayment, number?: string) {
    page.lines.unselectAllRecords();

    return extractEdges(
        await page.$.graph
            .node('@sage/xtrem-finance/AccountsPayableOpenItem')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        accountsPayableInvoice: {
                            _id: true,
                            number: true,
                            origin: true,
                            reference: true,
                            postingDate: true,
                            paymentTracking: {
                                _id: true,
                                document: { _id: true, number: true },
                            },
                        },
                        penaltyPaymentType: true,
                        discountType: true,
                        discountPaymentBeforeDate: true,
                        dueDate: true,
                        currency: { id: true, symbol: true },
                        transactionAmountPaidSigned: true,
                        penaltyAmount: true,
                        discountAmount: true,
                        amountDue: true,
                        totalAmount: true,
                        remainingCompanyAmount: true,
                        totalCompanyAmount: true,
                        totalCompanyAmountPaid: true,
                    },
                    { filter: getFilter(page, number), first: 500 },
                ),
            )
            .execute(),
    );
}

// Inits the lines fragment of the recordReceipt page in onLoad
export async function initRecordLines(page: RecordPayment, number: string) {
    if (page.$.queryParameters.openParams) {
        page.lines.value = await getResults(page, number);
    } else {
        page.lines.value = [];
    }
}

// Inits the record receipt page in onLoad
export async function initRecordPayment(page: RecordPayment) {
    initRecordCommon(page);
    if (page.$.queryParameters.openParams) {
        const openParams: BaseRecordCreationData = JSON.parse(page.$.queryParameters.openParams as string);
        await initRecordLines(page, openParams.number);
        const lineSysId = page.lines.value[0]?._id;
        onOpenItemRowSelected({ page, lineSysId });
        page.lines.selectRecord(lineSysId);
        page.amount.value = Number(page.totalPaymentApplied.value);
        page.amountAvailableToApply.value = 0.0;
        if (page.currency?.value?.id === page.bankAccount?.value?.currency?.id) {
            page.amountBankCurrency.value = page.amount.value;
        }
    } else {
        page.type.value = 'supplier';
    }
}

export async function createPayment(page: RecordPayment): Promise<string> {
    const line = page.lines.value[0];
    const payment = await page.$.graph
        .node('@sage/xtrem-finance/Payment')
        .create(
            { _id: true, number: true },
            {
                data: {
                    ...getCommonDataForCreation(page),
                    businessRelation: page.supplier.value?._id ?? '',
                    lines: [
                        {
                            ...getCommonLineDataForCreation(line, page),
                            apOpenItem: line._id,
                            origin: line.accountsPayableInvoice?.origin,
                            paymentTracking: line.accountsPayableInvoice?.paymentTracking?._id,
                        },
                    ],
                },
            },
        )
        .execute();
    return payment.number;
}
