import type { AccountsPayableOpenItem as AccountsPayableOpenItemPage } from '../pages/accounts-payable-open-item';
import type { AccountsReceivableOpenItem as AccountsReceivableOpenItemPage } from '../pages/accounts-receivable-open-item';

type AccountsOpenItemPage = AccountsReceivableOpenItemPage | AccountsPayableOpenItemPage;

type Status = {
    _eq?: string;
    _ne?: string;
};

enum BlockType {
    PENALTY = 'penalty',
    DISCOUNT = 'discount',
}

function getGraphQlFilter(isAccountsReceivable: boolean, status: Status) {
    return isAccountsReceivable
        ? { status, accountsReceivableInvoice: { postingStatus: 'posted' } }
        : { status, accountsPayableInvoice: { postingStatus: 'posted' } };
}

export function getNotFullyPaidFilter(isAccountsReceivable: boolean) {
    return {
        title: 'Not fully paid',
        graphQLFilter: getGraphQlFilter(isAccountsReceivable, { _ne: 'paid' }),
    };
}

export function getPaidFilter(isAccountsReceivable: boolean) {
    return {
        title: 'Paid',
        graphQLFilter: getGraphQlFilter(isAccountsReceivable, { _eq: 'paid' }),
    };
}

export function getNotPaidFilter(isAccountsReceivable: boolean) {
    return {
        title: 'Not paid',
        graphQLFilter: getGraphQlFilter(isAccountsReceivable, { _eq: 'notPaid' }),
    };
}

export function getPartiallyPaidFilter(isAccountsReceivable: boolean) {
    return {
        title: 'Partially paid',
        graphQLFilter: getGraphQlFilter(isAccountsReceivable, { _eq: 'partiallyPaid' }),
    };
}

function hideBlock(page: AccountsOpenItemPage, blockType: BlockType) {
    const block = blockType === 'penalty' ? page.penaltyBlock : page.discountBlock;
    const amount =
        blockType === 'penalty' ? Number(page.penaltyAmount.value ?? 0) : Number(page.discountAmount.value ?? 0);

    if (amount === 0) block.isHidden = true;
}

export function hideDiscountPenaltyBlocks(page: AccountsOpenItemPage) {
    hideBlock(page, BlockType.PENALTY);
    hideBlock(page, BlockType.DISCOUNT);
}
