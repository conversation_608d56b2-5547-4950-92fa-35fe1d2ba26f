import type { ExtractEdgesPartial, decimal } from '@sage/xtrem-client';
import type { AccountsPayableOpenItem, AccountsReceivableOpenItem } from '@sage/xtrem-finance-api';
import type { BankAccount } from '@sage/xtrem-finance-data-api';
import type { BaseBusinessRelation, BusinessRelationType, Currency, PaymentMethod } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
import type { PaymentInformation as PaymentInformationPageFragment } from '../../page-fragments/payment-information';
import type { RecordLines as RecordLinesPageFragment } from '../../page-fragments/record-lines';
import type { RecordPayment as RecordPaymentPage } from '../../pages/record-payment';
import type { RecordReceipt as RecordReceiptPage } from '../../pages/record-receipt';

export interface AccountsOpenItemRecord {
    paymentAmount?: decimal;
    creditAmount?: decimal;
    adjustmentAmount?: decimal;
    discountPaymentAmount?: decimal;
    penaltyPaymentAmount?: decimal;
}

export interface AccountsReceivableOpenItemRecord extends AccountsReceivableOpenItem, AccountsOpenItemRecord {}
export interface AccountsPayableOpenItemRecord extends AccountsPayableOpenItem, AccountsOpenItemRecord {}
export type AccountsPayableReceivableOpenItemRecord = AccountsReceivableOpenItemRecord & AccountsPayableOpenItemRecord;

interface BasePaymentHeader {
    bankAccount: ExtractEdgesPartial<BankAccount>;
    financialSite: ExtractEdgesPartial<Site>;
    businessRelation: ExtractEdgesPartial<BaseBusinessRelation>;
    type: BusinessRelationType;
    paymentMethod: PaymentMethod;
    reference: string;
    paymentDate: string;
    currency: ExtractEdgesPartial<Currency>;
    amount: decimal;
    amountBankCurrency: decimal;
}

export interface BasePaymentCreationData {
    header: BasePaymentHeader;
    lines: PartialCollectionValueWithIds<AccountsReceivableOpenItemRecord | AccountsPayableOpenItemRecord>[];
}

export interface BaseRecordCreationData {
    bankAccount: ExtractEdgesPartial<BankAccount>;
    financialSite: ExtractEdgesPartial<Site>;
    baseBusinessRelation: ExtractEdgesPartial<BaseBusinessRelation>;
    type: BusinessRelationType;
    date: string;
    currency: ExtractEdgesPartial<Currency>;
    amount: decimal;
    number: string;
}

export type RecordPages = RecordPaymentPage | RecordReceiptPage;

export type RecordPagesOrFragments = RecordPages | RecordLinesPageFragment | PaymentInformationPageFragment;
