import type { AccountsPayableOpenItemPayment } from '../../page-fragments/accounts-payable-open-item-payment';
import type { AccountsReceivableOpenItemPayment } from '../../page-fragments/accounts-receivable-open-item-payment';
import type { Payment as PaymentPage } from '../../pages/payment';
import type { Receipt as ReceiptPage } from '../../pages/receipt';

export type OpenItemPageFragments = AccountsPayableOpenItemPayment | AccountsReceivableOpenItemPayment;
export type PaymentPages = PaymentPage | ReceiptPage;
