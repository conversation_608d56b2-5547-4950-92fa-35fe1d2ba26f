import type { AccountsPayableOpenItem, AccountsReceivableOpenItem, GraphApi } from '@sage/xtrem-finance-api';
import type { BankAccount, PaymentDocumentLine } from '@sage/xtrem-finance-data-api';
import type { Currency } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import { initBasePaymentLine, openLinkPage } from '../client-functions/common';

export type PaymentDocumentLineType = PaymentDocumentLine & {
    arOpenItem: AccountsReceivableOpenItem;
    apOpenItem: AccountsPayableOpenItem;
};

@ui.decorators.pageFragment<BasePaymentLine>({
    // eslint-disable-next-line
    async onLoad() {
        if (this.$.queryParameters.createParams) {
            await initBasePaymentLine(this);
        }
    },
})
export class BasePaymentLine extends ui.PageFragment<GraphApi> {
    currency: ui.fields.Reference<Currency>;

    bankAccount: ui.fields.Reference<BankAccount>;

    @ui.decorators.radioField<BasePaymentLine>({
        optionType: '@sage/xtrem-master-data/BusinessRelationType',
        isHidden: true,
    })
    type: ui.fields.Radio;

    amountBankCurrency: ui.fields.Numeric;

    amount: ui.fields.Numeric;

    @ui.decorators.tableField<BasePaymentLine, PaymentDocumentLineType>({
        title: 'Lines',
        bind: 'lines',
        canSelect: false,
        isReadOnly: true,
        isFullWidth: true,
        columns: [
            ui.nestedFields.technical({
                bind: 'arOpenItem',
                node: '@sage/xtrem-finance/AccountsReceivableOpenItem',
                nestedFields: [
                    ui.nestedFields.reference({
                        bind: 'accountsReceivableInvoice',
                        node: '@sage/xtrem-finance/AccountsReceivableInvoice',
                        valueField: 'salesDocumentNumber',
                        columns: [
                            ui.nestedFields.text({ bind: 'salesDocumentNumber' }),
                            ui.nestedFields.numeric({ bind: 'salesDocumentSysId' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({
                bind: 'apOpenItem',
                node: '@sage/xtrem-finance/AccountsPayableOpenItem',
                nestedFields: [
                    ui.nestedFields.reference({
                        bind: 'accountsPayableInvoice',
                        node: '@sage/xtrem-finance/AccountsPayableInvoice',
                        valueField: 'purchaseDocumentNumber',
                        columns: [
                            ui.nestedFields.text({ bind: 'purchaseDocumentNumber' }),
                            ui.nestedFields.numeric({ bind: 'purchaseDocumentSysId' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.link({
                title: 'Document number',
                width: 'small',
                bind: { arOpenItem: { accountsReceivableInvoice: { salesDocumentNumber: true } } },
                isHidden() {
                    return this.type.value === 'supplier';
                },
                isDisabled() {
                    return this.$.queryParameters.fromSales?.toString() === 'true';
                },
                async onClick(_rowId, rowData) {
                    await openLinkPage({ page: this, line: rowData });
                },
            }),
            ui.nestedFields.link({
                title: 'Document number',
                width: 'small',
                bind: { apOpenItem: { accountsPayableInvoice: { purchaseDocumentNumber: true } } },
                isHidden() {
                    return this.type.value === 'customer';
                },
                isDisabled() {
                    return this.$.queryParameters.fromSales?.toString() === 'true';
                },
                async onClick(_rowId, rowData) {
                    await openLinkPage({ page: this, line: rowData });
                },
            }),
            ui.nestedFields.dropdownList({
                title: 'Origin',
                bind: 'origin',
                optionType: '@sage/xtrem-finance-data/accountsPayableReceivableInvoiceOrigin',
            }),
            ui.nestedFields.date({
                title: 'Posting date',
                bind: { arOpenItem: { accountsReceivableInvoice: { postingDate: true } } },
                isHidden() {
                    return this.type.value === 'supplier';
                },
            }),
            ui.nestedFields.date({
                title: 'Posting date',
                bind: { apOpenItem: { accountsPayableInvoice: { postingDate: true } } },
                isHidden() {
                    return this.type.value === 'customer';
                },
            }),
            ui.nestedFields.date({
                title: 'Due date',
                bind: { arOpenItem: { accountsReceivableInvoice: { dueDate: true } } },
                isHidden() {
                    return this.type.value === 'supplier';
                },
            }),
            ui.nestedFields.date({
                title: 'Due date',
                bind: { apOpenItem: { accountsPayableInvoice: { dueDate: true } } },
                isHidden() {
                    return this.type.value === 'customer';
                },
            }),
            ui.nestedFields.reference({
                title: 'Currency',
                bind: 'currency',
                valueField: 'id',
                node: '@sage/xtrem-master-data/Currency',
            }),
            ui.nestedFields.numeric({
                title: 'Amount',
                bind: 'signedAmount',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Discount amount',
                bind: 'discountAmount',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Penalty amount',
                bind: 'penaltyAmount',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Adjustment amount',
                bind: 'adjustmentAmount',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Amount in bank currency',
                bind: 'signedAmountBankCurrency',
                isHidden() {
                    return this.currency.value?.id === this.bankAccount.value?.currency?.id;
                },
                unit() {
                    return this.bankAccount.value?.currency;
                },
                scale: null,
            }),
            ui.nestedFields.technical({ bind: 'amount' }),
            ui.nestedFields.text({
                title: 'Reference',
                bind: { arOpenItem: { accountsReceivableInvoice: { reference: true } } },
                isHidden() {
                    return this.type.value === 'supplier';
                },
            }),
            ui.nestedFields.text({
                title: 'Reference',
                bind: { apOpenItem: { accountsPayableInvoice: { reference: true } } },
                isHidden() {
                    return this.type.value === 'customer';
                },
            }),
            ui.nestedFields.technical({
                bind: 'paymentTracking',
                node: '@sage/xtrem-finance-data/PaymentTracking',
                nestedFields: [
                    ui.nestedFields.reference({
                        bind: 'document',
                        node: '@sage/xtrem-master-data/BaseDocument',
                        valueField: 'number',
                    }),
                ],
            }),
        ],
    })
    lines: ui.fields.Table<
        PaymentDocumentLine & {
            arOpenItem: AccountsReceivableOpenItem;
            apOpenItem: AccountsPayableOpenItem;
        }
    >;
}
