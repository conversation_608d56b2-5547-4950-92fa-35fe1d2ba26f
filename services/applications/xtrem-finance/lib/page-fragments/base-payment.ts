import type { GraphApi } from '@sage/xtrem-finance-api';
import type { BankAccount } from '@sage/xtrem-finance-data-api';
import type { BaseBusinessRelation, Currency, Customer, Supplier } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { initBasePayment } from '../client-functions/common';

@ui.decorators.pageFragment<BasePayment>({
    onLoad() {
        initBasePayment(this);
    },
})
export class BasePayment extends ui.PageFragment<GraphApi> {
    @ui.decorators.radioField<BasePayment>({
        optionType: '@sage/xtrem-master-data/BusinessRelationType',
        isHidden: true,
    })
    type: ui.fields.Radio;

    @ui.decorators.referenceField<BasePayment, Site>({
        title: 'Financial site',
        isReadOnly: true,
    })
    financialSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<BasePayment, BaseBusinessRelation>({ isHidden: true })
    businessRelation: ui.fields.Reference<BaseBusinessRelation>;

    @ui.decorators.referenceField<BasePayment, Customer>({
        node: '@sage/xtrem-master-data/Customer',
        tunnelPage: '@sage/xtrem-master-data/Customer',
        isHidden() {
            return this.type.value === 'supplier';
        },
        bind: { customer: true },
        title: 'Customer',
        valueField: 'name',
        helperTextField: 'id',
        isReadOnly: true,
    })
    customer: ui.fields.Reference<Customer>;

    @ui.decorators.referenceField<BasePayment, Supplier>({
        node: '@sage/xtrem-master-data/Supplier',
        tunnelPage: '@sage/xtrem-master-data/Supplier',
        isHidden() {
            return this.type.value === 'customer';
        },
        bind: { supplier: true },
        title: 'Supplier',
        valueField: 'name',
        helperTextField: 'id',
        isReadOnly: true,
    })
    supplier: ui.fields.Reference<Supplier>;

    @ui.decorators.textField<BasePayment>({
        title: 'Number',
        isReadOnly: true,
        width: 'small',
    })
    number: ui.fields.Text;

    @ui.decorators.referenceField<BasePayment, BankAccount>({
        title: 'Bank account',
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
    })
    bankAccount: ui.fields.Reference<BankAccount>;

    @ui.decorators.dateField<BasePayment>({
        title: 'Date received',
        isReadOnly: true,
    })
    paymentDate: ui.fields.Date;

    @ui.decorators.checkboxField<BasePayment>({
        title: 'Voided',
        isHidden() {
            return !this.isVoided.value;
        },
        isReadOnly: true,
        width: 'small',
    })
    isVoided: ui.fields.Checkbox;

    @ui.decorators.separatorField<BasePayment>({
        isFullWidth: true,
        isInvisible: true,
    })
    methodSeparator: ui.fields.Separator;

    @ui.decorators.dropdownListField<BasePayment>({
        title: 'Payment method',
        optionType: '@sage/xtrem-master-data/PaymentMethod',
        isReadOnly: true,
    })
    paymentMethod: ui.fields.DropdownList;

    @ui.decorators.textField<BasePayment>({
        title: 'Transaction information',
        isReadOnly: true,
    })
    reference: ui.fields.Text;

    @ui.decorators.referenceField<BasePayment, Currency>({
        node: '@sage/xtrem-master-data/Currency',
        title: 'Currency',
        valueField: 'name',
        isReadOnly: true,
        helperTextField: 'id',
        lookupDialogTitle: 'Select currency',
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.numericField<BasePayment>({
        title: 'Payment amount',
        isReadOnly: true,
        unit() {
            return this.currency.value;
        },
        scale: null,
    })
    amount: ui.fields.Numeric;

    @ui.decorators.numericField<BasePayment>({
        isHidden() {
            return this.currency.value?.id === this.bankAccount.value?.currency?.id;
        },
        title: 'Amount in bank currency',
        isReadOnly: true,
        unit() {
            return this.bankAccount.value?.currency;
        },
        scale: null,
    })
    amountBankCurrency: ui.fields.Numeric;

    @ui.decorators.dateField<BasePayment>({
        title: 'Voided on',
        isHidden() {
            return !this.isVoided.value;
        },
        isReadOnly: true,
        width: 'small',
    })
    voidDate: ui.fields.Date;

    @ui.decorators.textField<BasePayment>({
        title: 'Void text',
        isHidden() {
            return !this.isVoided.value;
        },
        isReadOnly: true,
    })
    voidText: ui.fields.Text;
}
