import type { AccountsPayableInvoice, AccountsReceivableInvoice, GraphApi } from '@sage/xtrem-finance-api';
import type { Currency } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type {
    AccountsPayableOpenItemRecord,
    AccountsPayableReceivableOpenItemRecord,
    AccountsReceivableOpenItemRecord,
} from '../client-functions/interfaces/record';
import {
    isAdjustmentAmountReadOnly,
    onCreditAmountChange,
    onOpenItemRowSelected,
    onOpenItemRowUnSelected,
    onPaymentAmountChange,
    onPenaltyAmountChange,
    validateCreditAmount,
    validatePaymentAmount,
} from '../client-functions/record-common';

@ui.decorators.pageFragment<RecordLines>({})
export class RecordLines extends ui.PageFragment<GraphApi> {
    totalPaymentApplied: ui.fields.Numeric;

    amountAvailableToApply: ui.fields.Numeric;

    type: ui.fields.Radio;

    amount: ui.fields.Numeric;

    financialSite: ui.fields.Reference<Site>;

    currency: ui.fields.Reference<Currency>;

    date: ui.fields.Date;

    @ui.decorators.tableField<RecordLines, AccountsPayableReceivableOpenItemRecord>({
        isTransient: true,
        orderBy: { dueDate: +1 },
        columns: [
            ui.nestedFields.reference<RecordLines, AccountsReceivableOpenItemRecord, AccountsReceivableInvoice>({
                title: 'Invoice number',
                bind: 'accountsReceivableInvoice',
                valueField: 'number',
                node: '@sage/xtrem-finance/AccountsReceivableInvoice',
                tunnelPage: '@sage/xtrem-finance/AccountsReceivableInvoice',
                isReadOnly: true,
                columns: [ui.nestedFields.text({ bind: 'origin' })],
                isHidden() {
                    return this.type.value === 'supplier';
                },
            }),
            ui.nestedFields.reference<RecordLines, AccountsPayableOpenItemRecord, AccountsPayableInvoice>({
                title: 'Invoice number',
                bind: 'accountsPayableInvoice',
                valueField: 'number',
                node: '@sage/xtrem-finance/AccountsPayableInvoice',
                tunnelPage: '@sage/xtrem-finance/AccountsPayableInvoice',
                isReadOnly: true,
                columns: [ui.nestedFields.text({ bind: 'origin' })],
                isHidden() {
                    return this.type.value !== 'supplier' || !!this.$.queryParameters.openParams;
                },
            }),
            ui.nestedFields.dropdownList({
                title: 'Origin',
                bind: { accountsReceivableInvoice: { origin: true } },
                optionType: '@sage/xtrem-finance-data/accountsPayableReceivableInvoiceOrigin',
                isReadOnly: true,
                isHidden() {
                    return this.type.value === 'supplier' || !!this.$.queryParameters.openParams;
                },
            }),
            ui.nestedFields.dropdownList({
                title: 'Origin',
                bind: { accountsPayableInvoice: { origin: true } },
                optionType: '@sage/xtrem-finance-data/accountsPayableReceivableInvoiceOrigin',
                isReadOnly: true,
                isHidden() {
                    return this.type.value !== 'supplier';
                },
            }),
            ui.nestedFields.text({
                title: 'Reference',
                bind: { accountsReceivableInvoice: { reference: true } },
                isReadOnly: true,
                isHidden() {
                    return this.type.value === 'supplier' || !!this.$.queryParameters.openParams;
                },
            }),
            ui.nestedFields.text({
                title: 'Reference',
                bind: { accountsPayableInvoice: { reference: true } },
                isReadOnly: true,
                isHidden() {
                    return this.type.value !== 'supplier' || !!this.$.queryParameters.openParams;
                },
            }),
            ui.nestedFields.date({
                title: 'Posting date',
                bind: { accountsReceivableInvoice: { postingDate: true } },
                isReadOnly: true,
                isHidden() {
                    return this.type.value === 'supplier' || !!this.$.queryParameters.openParams;
                },
            }),
            ui.nestedFields.date({
                title: 'Posting date',
                bind: { accountsPayableInvoice: { postingDate: true } },
                isReadOnly: true,
                isHidden() {
                    return this.type.value !== 'supplier' || !!this.$.queryParameters.openParams;
                },
            }),
            ui.nestedFields.date({ title: 'Due date', bind: 'dueDate', isReadOnly: true }),
            ui.nestedFields.reference({
                title: 'Currency',
                bind: 'currency',
                valueField: 'id',
                node: '@sage/xtrem-master-data/Currency',
                isReadOnly: true,
                isHidden() {
                    return !!this.$.queryParameters.openParams;
                },
            }),
            ui.nestedFields.numeric({
                title: 'Total amount',
                bind: 'totalAmount',
                unit: (_id, rowData) => rowData?.currency,
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Amount due',
                bind: 'amountDue',
                unit: (_id, rowData) => rowData?.currency,
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Payment amount',
                bind: 'paymentAmount',
                unit: (_id, rowData) => rowData?.currency,
                isReadOnly(_id: string, rowValue) {
                    return (
                        rowValue?.accountsReceivableInvoice?.origin === 'creditMemo' ||
                        rowValue?.accountsPayableInvoice?.origin === 'creditMemo'
                    );
                },
                validation(val: number, rowData) {
                    return validatePaymentAmount({
                        page: this,
                        val,
                        rowData,
                    });
                },
                onChange(_id: string, rowData) {
                    onPaymentAmountChange(this, rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Credit amount',
                bind: 'creditAmount',
                unit: (_id, rowData) => rowData?.currency,
                isReadOnly(_id: string, rowValue) {
                    return (
                        rowValue?.accountsReceivableInvoice?.origin === 'invoice' ||
                        rowValue?.accountsPayableInvoice?.origin === 'invoice'
                    );
                },
                validation(val: number, rowData) {
                    return validateCreditAmount({
                        page: this,
                        val,
                        rowData,
                    });
                },
                onChange(_id: string, rowData) {
                    onCreditAmountChange(this, rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Discount amount',
                bind: 'discountPaymentAmount',
                isReadOnly(_id: string, rowData) {
                    return !rowData?.paymentAmount;
                },
                unit: (_id, rowData) => rowData?.currency,
            }),
            ui.nestedFields.numeric({
                title: 'Penalty amount',
                bind: 'penaltyPaymentAmount',
                isReadOnly(_id: string, rowData) {
                    return !rowData?.paymentAmount;
                },
                unit: (_id, rowData) => rowData?.currency,
                scale: null,
                onChange(_id: string, rowData: AccountsPayableReceivableOpenItemRecord) {
                    onPenaltyAmountChange(this, rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Adjustment amount',
                bind: 'adjustmentAmount',
                isReadOnly(_id: string, rowData: AccountsPayableReceivableOpenItemRecord) {
                    return isAdjustmentAmountReadOnly(this, rowData);
                },
                unit: (_id, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Total company amount',
                bind: 'totalCompanyAmount',
                unit() {
                    return this.financialSite.value?.legalCompany?.currency;
                },
                scale: null,
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                title: 'Company amount due',
                bind: 'remainingCompanyAmount',
                unit() {
                    return this.financialSite.value?.legalCompany?.currency;
                },
                scale: null,
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                title: 'Company amount paid',
                bind: 'totalCompanyAmountPaid',
                unit() {
                    return this.financialSite.value?.legalCompany?.currency;
                },
                scale: null,
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
        ],
        onRowSelected(_id: string) {
            onOpenItemRowSelected({ page: this, lineSysId: _id });
        },
        onRowUnselected(_id: string) {
            onOpenItemRowUnSelected({ page: this, lineSysId: _id });
        },
    })
    lines: ui.fields.Table<AccountsReceivableOpenItemRecord & AccountsPayableOpenItemRecord>;
}
