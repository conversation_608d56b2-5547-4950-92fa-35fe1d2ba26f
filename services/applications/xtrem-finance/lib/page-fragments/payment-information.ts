import type { Graph<PERSON>pi } from '@sage/xtrem-finance-api';
import type { BankAccount } from '@sage/xtrem-finance-data-api';
import type { BaseBusinessRelation, Currency } from '@sage/xtrem-master-data-api';
import type { Legislation } from '@sage/xtrem-structure-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type { AccountsReceivableOpenItemRecord } from '../client-functions/interfaces/record';
import {
    checkCurrencies,
    checkIfFutureDate,
    controlAmountInBankCurrency,
    initPaymentInformation,
    isAmountInBankCurrencyHidden,
    onAmountChange,
    onFinancialSiteChange,
    resetLines,
} from '../client-functions/record-common';

@ui.decorators.pageFragment<PaymentInformation>({
    onLoad() {
        initPaymentInformation(this);
    },
})
export class PaymentInformation extends ui.PageFragment<GraphApi> {
    lines: ui.fields.Table<AccountsReceivableOpenItemRecord>;

    totalPaymentApplied: ui.fields.Numeric;

    amountAvailableToApply: ui.fields.Numeric;

    @ui.decorators.radioField<PaymentInformation>({
        title: 'Type',
        isReadOnly: true,
        optionType: '@sage/xtrem-master-data/BusinessRelationType',
        isHidden() {
            return !!this.$.queryParameters.openParams;
        },
    })
    type: ui.fields.Radio;

    @ui.decorators.referenceField<PaymentInformation, Site>({
        title: 'Financial site',
        node: '@sage/xtrem-system/Site',
        tunnelPage: '@sage/xtrem-master-data/Site',
        lookupDialogTitle: 'Select site',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        isHidden() {
            return !!this.$.queryParameters.openParams;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference<PaymentInformation, Site, Company>({
                node: '@sage/xtrem-system/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical<PaymentInformation, Company, Legislation>({
                        node: '@sage/xtrem-structure/Legislation',
                        bind: 'legislation',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical<PaymentInformation, Company, BankAccount>({
                        node: '@sage/xtrem-finance-data/BankAccount',
                        bind: 'bankAccount',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical<PaymentInformation>({
                                node: '@sage/xtrem-system/Site',
                                bind: 'financialSite',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                ],
                            }),
                            ui.nestedFields.technical({
                                bind: 'currency',
                                node: '@sage/xtrem-master-data/Currency',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'symbol' }),
                                ],
                            }),
                        ],
                    }),
                    ui.nestedFields.technical<PaymentInformation, Company, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
        ],
        filter: { isActive: true, isFinance: true },
        onChange() {
            onFinancialSiteChange(this);
        },
        placeholder: 'Financial site',
        width: 'small',
    })
    financialSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<PaymentInformation, BaseBusinessRelation>({
        node: '@sage/xtrem-master-data/BaseBusinessRelation',
        tunnelPage: '@sage/xtrem-master-data/Customer',
        isHidden() {
            return this.type.value === 'supplier';
        },
        bind: { businessRelation: true },
        title: 'Customer',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        isReadOnly() {
            return !!this.$.queryParameters.openParams;
        },
        filter: { isActive: true, _constructor: 'Customer' },
        columns: [
            ui.nestedFields.text({ bind: { name: true }, title: 'Name' }),
            ui.nestedFields.text({ bind: { id: true }, title: 'ID' }),
            ui.nestedFields.text({ bind: { taxIdNumber: true }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { country: { name: true } }, title: 'Country' }),
            ui.nestedFields.reference({ bind: 'currency', node: '@sage/xtrem-master-data/Currency', isHidden: true }),
            ui.nestedFields.technical({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
        ],
        async onChange() {
            this.currency.value = this.customer.value?.currency ?? null;
            this.supplier.value = this.customer.value;
            await checkCurrencies({
                page: this,
                currency: this.currency.value ?? undefined,
                bankAccountCurrency: this.bankAccount.value?.currency,
            });
            resetLines(this);
        },
    })
    customer: ui.fields.Reference<BaseBusinessRelation>;

    @ui.decorators.referenceField<PaymentInformation, BaseBusinessRelation>({
        node: '@sage/xtrem-master-data/BaseBusinessRelation',
        tunnelPage: '@sage/xtrem-master-data/Supplier',
        isHidden() {
            return this.type.value === 'customer';
        },
        bind: { businessRelation: true },
        title: 'Supplier',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        isReadOnly() {
            return !!this.$.queryParameters.openParams;
        },
        filter: { isActive: true, _constructor: 'Supplier' },
        columns: [
            ui.nestedFields.text({ bind: { name: true }, title: 'Name' }),
            ui.nestedFields.text({ bind: { id: true }, title: 'ID' }),
            ui.nestedFields.text({ bind: { taxIdNumber: true }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { country: { name: true } }, title: 'Country' }),
            ui.nestedFields.reference({ bind: 'currency', node: '@sage/xtrem-master-data/Currency', isHidden: true }),
            ui.nestedFields.technical({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
        ],
        async onChange() {
            this.currency.value = this.supplier.value?.currency ?? null;
            this.customer.value = this.supplier.value;
            await checkCurrencies({
                page: this,
                currency: this.currency.value ?? undefined,
                bankAccountCurrency: this.bankAccount.value?.currency,
            });
            resetLines(this);
        },
    })
    supplier: ui.fields.Reference<BaseBusinessRelation>;

    @ui.decorators.referenceField<PaymentInformation, BankAccount>({
        node: '@sage/xtrem-finance-data/BankAccount',
        tunnelPage: '@sage/xtrem-finance-data/BankAccount',
        title: 'Bank account',
        isMandatory: true,
        filter() {
            if (this.financialSite.value) {
                return { financialSite: { _id: this.financialSite.value._id } };
            }

            return {};
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
        async onChange() {
            await checkCurrencies({
                page: this,
                currency: this.currency.value ?? undefined,
                bankAccountCurrency: this.bankAccount.value?.currency,
            });
        },
    })
    bankAccount: ui.fields.Reference<BankAccount>;

    @ui.decorators.dateField<PaymentInformation>({
        title: 'Date received',
        isMandatory: true,
        validation: value => checkIfFutureDate(value),
        async onChange() {
            await checkCurrencies({
                page: this,
                currency: this.currency.value ?? undefined,
                bankAccountCurrency: this.bankAccount.value?.currency,
            });
        },
    })
    date: ui.fields.Date;

    @ui.decorators.dropdownListField<PaymentInformation>({
        title: 'Payment method',
        optionType: '@sage/xtrem-master-data/PaymentMethod',
        isMandatory: true,
    })
    paymentMethod: ui.fields.DropdownList;

    @ui.decorators.referenceField<PaymentInformation, Currency>({
        node: '@sage/xtrem-master-data/Currency',
        title: 'Currency',
        valueField: 'name',
        isMandatory: true,
        helperTextField: 'id',
        lookupDialogTitle: 'Select currency',
        isHidden() {
            return !!this.$.queryParameters.openParams;
        },
        async onChange() {
            await checkCurrencies({
                page: this,
                currency: this.currency.value ?? undefined,
                bankAccountCurrency: this.bankAccount.value?.currency,
            });
            resetLines(this);
        },
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.numericField<PaymentInformation>({
        title: 'Payment amount',
        isMandatory: true,
        unit() {
            return this.currency.value;
        },
        scale: null,
        validation(amount) {
            if (amount < 0) {
                return ui.localize(
                    '@sage/xtrem-finance/page_fragments__payment_information__payment_amount_can_not_be_negative',
                    'The payment amount needs to be greater than or equal to 0.',
                );
            }
            return '';
        },
        async onChange() {
            if (!this.amount.value) {
                this.amount.value = 0.0;
            }
            await controlAmountInBankCurrency(this);
            onAmountChange(this);
        },
    })
    amount: ui.fields.Numeric;

    @ui.decorators.numericField<PaymentInformation>({
        isHidden() {
            return isAmountInBankCurrencyHidden(this);
        },
        title: 'Amount in bank currency',
        validation(amount) {
            if (amount < 0 || (amount === 0 && this.amount.value !== 0)) {
                return ui.localize(
                    '@sage/xtrem-finance/page_fragments__payment_information__amount_in_bank_currency_must_be_positive',
                    'The amount in bank currency needs to be greater than 0.',
                );
            }
            return '';
        },
        async onChange() {
            await controlAmountInBankCurrency(this);
        },
        unit() {
            return this.bankAccount.value?.currency;
        },
        scale: null,
    })
    amountBankCurrency: ui.fields.Numeric;

    @ui.decorators.textField<PaymentInformation>({
        title: 'Transaction information',
        validation(tranInfo) {
            if (tranInfo.length > 100) {
                return ui.localize(
                    '@sage/xtrem-finance/page_fragments__payment_information__transaction_information_can_not_exceed_100_characters',
                    'Transaction information cannot exceed 100 characters.',
                );
            }
            return '';
        },
    })
    reference: ui.fields.Text;
}
