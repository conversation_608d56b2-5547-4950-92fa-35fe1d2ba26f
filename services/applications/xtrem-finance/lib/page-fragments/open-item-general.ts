import type { Graph<PERSON><PERSON> } from '@sage/xtrem-finance-api';
import type { BaseBusinessRelation, Currency } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { openDocumentLinkPage } from '../client-functions/common';

@ui.decorators.pageFragment<OpenItemGeneral>({
    onLoad() {
        this.documentNumberLink.value = this.documentNumber.value ?? '';
        this.businessRelation.title = ['salesInvoice', 'salesCreditMemo'].includes(this.documentType.value ?? '')
            ? 'Customer'
            : 'Supplier';
    },
})
export class OpenItemGeneral extends ui.PageFragment<GraphApi> {
    @ui.decorators.textField<OpenItemGeneral>({ isHidden: true })
    documentNumber: ui.fields.Text;

    @ui.decorators.textField<OpenItemGeneral>({ isHidden: true })
    documentSysId: ui.fields.Text;

    @ui.decorators.referenceField<OpenItemGeneral, Currency>({ isHidden: true })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.referenceField<OpenItemGeneral, Site>({
        title: 'Financial site',
        width: 'medium',
        columns: [
            ui.nestedFields.reference({
                bind: { legalCompany: true },
                node: '@sage/xtrem-system/Company',
                isHidden: true,
                columns: [
                    ui.nestedFields.reference({
                        bind: { currency: true },
                        node: '@sage/xtrem-master-data/Currency',
                    }),
                ],
            }),
        ],
    })
    financialSite: ui.fields.Reference<Site>;

    @ui.decorators.linkField<OpenItemGeneral>({
        title: 'Document number',
        width: 'small',
        isTransient: true,
        async onClick() {
            await openDocumentLinkPage({
                page: this,
                documentSysId: Number(this.documentSysId.value),
                documentType: this.documentType.value,
                fromFinance: true,
            });
        },
        map() {
            return this.documentNumber.value ?? '';
        },
        isDisabled() {
            return this.$.queryParameters.fromSales?.toString() === 'true';
        },
    })
    documentNumberLink: ui.fields.Link;

    @ui.decorators.dropdownListField<OpenItemGeneral>({
        title: 'Document type',
        width: 'small',
        isReadOnly: true,
        optionType: '@sage/xtrem-finance-data/FinanceDocumentType',
    })
    documentType: ui.fields.DropdownList;

    @ui.decorators.referenceField<OpenItemGeneral, BaseBusinessRelation>({
        title: 'Business relation',
        width: 'medium',
    })
    businessRelation: ui.fields.Reference<BaseBusinessRelation>;

    @ui.decorators.separatorField<OpenItemGeneral>({
        isFullWidth: true,
        isInvisible: true,
    })
    dueSeparator: ui.fields.Separator;

    @ui.decorators.dateField<OpenItemGeneral>({
        title: 'Due date',
        width: 'small',
        isReadOnly: true,
    })
    dueDate: ui.fields.Date;

    @ui.decorators.numericField<OpenItemGeneral>({
        title: 'Transaction amount due',
        width: 'medium',
        isReadOnly: true,
        unit() {
            return this.currency?.value;
        },
        scale: null,
    })
    transactionAmountDueSigned: ui.fields.Numeric;

    @ui.decorators.numericField<OpenItemGeneral>({
        title: 'Company amount due',
        width: 'medium',
        isReadOnly: true,
        unit() {
            return this.financialSite?.value?.legalCompany?.currency;
        },
        scale: null,
    })
    companyAmountDueSigned: ui.fields.Numeric;
}
