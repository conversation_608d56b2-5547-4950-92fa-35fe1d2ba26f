import type { <PERSON>raph<PERSON><PERSON> } from '@sage/xtrem-finance-api';
import type { BankAccount, BasePaymentDocument, PaymentDocumentLine } from '@sage/xtrem-finance-data-api';
import type { Currency } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import { openPaymentPage } from '../client-functions/payment-tracking';

@ui.decorators.pageFragment<AccountsReceivableOpenItemPayment>({})
export class AccountsReceivableOpenItemPayment extends ui.PageFragment<GraphApi> {
    @ui.decorators.tableField<AccountsReceivableOpenItemPayment, PaymentDocumentLine>({
        node: '@sage/xtrem-finance-data/PaymentDocumentLine',
        title: 'Receipts',
        isTitleHidden: true,
        canSelect: false,
        canExport: true,
        isReadOnly: true,
        isChangeIndicatorDisabled: true,
        pageSize: 10,
        bind: 'receipts',
        isHidden() {
            return !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
        orderBy: { document: { paymentDate: -1 } },
        columns: [
            ui.nestedFields.technical({
                bind: 'document',
                node: '@sage/xtrem-finance-data/BasePaymentDocument',
                nestedFields: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical<AccountsReceivableOpenItemPayment, BasePaymentDocument, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical<AccountsReceivableOpenItemPayment, BasePaymentDocument, BankAccount>({
                        bind: 'bankAccount',
                        node: '@sage/xtrem-finance-data/BankAccount',
                        nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
                    }),
                    ui.nestedFields.technical({ bind: 'paymentMethod' }),
                    ui.nestedFields.technical({ bind: 'reference' }),
                ],
            }),
            ui.nestedFields.date({ title: 'Receipt date', bind: { document: { paymentDate: true } } }),
            ui.nestedFields.link({
                title: 'Receipt number',
                width: 'small',
                bind: { document: { number: true } },
                isDisabled() {
                    return this.$.queryParameters.fromFinance?.toString() === 'true';
                },
                async onClick(_rowId, rowData: ui.PartialCollectionValue<PaymentDocumentLine>) {
                    await openPaymentPage(this, rowData.document?._id ?? '');
                },
            }),
            ui.nestedFields.numeric({
                title: 'Amount',
                bind: 'amount',
                unit: (_rowId, rowData) => rowData?.document?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Discount amount',
                bind: 'discountAmount',
                unit: (_value, rowData) => rowData?.document?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Penalty amount',
                bind: 'penaltyAmount',
                unit: (_value, rowData) => rowData?.document?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Adjustment amount',
                bind: 'adjustmentAmount',
                unit: (_value, rowData) => rowData?.document?.currency,
                scale: null,
            }),
            ui.nestedFields.text({ title: 'Bank account', bind: { document: { bankAccount: { name: true } } } }),
            ui.nestedFields.dropdownList({
                bind: { document: { paymentMethod: true } },
                title: 'Payment method',
                optionType: '@sage/xtrem-master-data/PaymentMethod',
            }),
            ui.nestedFields.text({ title: 'Transaction information', bind: { document: { reference: true } } }),
            ui.nestedFields.checkbox({ title: 'Voided', bind: { document: { isVoided: true } } }),
        ],
    })
    receipts: ui.fields.Table<PaymentDocumentLine>;
}
