import type { <PERSON>raph<PERSON><PERSON> } from '@sage/xtrem-finance-api';
import type { Currency } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageFragment<OpenItemPenalty>({})
export class OpenItemPenalty extends ui.PageFragment<GraphApi> {
    @ui.decorators.referenceField<OpenItemPenalty, Currency>({ isHidden: true })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.dropdownListField<OpenItemPenalty>({
        title: 'Penalty type',
        width: 'small',
        isReadOnly: true,
        optionType: '@sage/xtrem-master-data/PaymentTermDiscountOrPenaltyType',
    })
    penaltyPaymentType: ui.fields.DropdownList;

    @ui.decorators.numericField<OpenItemPenalty>({
        title() {
            return this.penaltyPaymentType.value === 'percentage'
                ? ui.localize(
                      '@sage/xtrem-finance/pages__accounts_receivable_open_item__penalty_percentage',
                      'Penalty percent',
                  )
                : ui.localize(
                      '@sage/xtrem-finance/pages__accounts_receivable_open_item__penalty_amount',
                      'Penalty amount',
                  );
        },
        width: 'small',
        isReadOnly: true,
        scale() {
            return this.penaltyPaymentType.value === 'percentage' ? 3 : (this.currency?.value?.decimalDigits ?? 2);
        },
        prefix() {
            return this.penaltyPaymentType.value === 'amount' ? (this.currency?.value?.symbol ?? '') : '';
        },
        postfix() {
            return this.penaltyPaymentType.value === 'percentage' ? '%' : '';
        },
    })
    penaltyAmount: ui.fields.Numeric;
}
