import type { Graph<PERSON><PERSON> } from '@sage/xtrem-finance-api';
import type { Currency } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageFragment<OpenItemDiscount>({})
export class OpenItemDiscount extends ui.PageFragment<GraphApi> {
    @ui.decorators.referenceField<OpenItemDiscount, Currency>({ isHidden: true })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.dateField<OpenItemDiscount>({
        title: 'Discount payment before date',
        width: 'small',
        isReadOnly: true,
        isHidden() {
            return this.discountDate.value === 0;
        },
    })
    displayDiscountPaymentDate: ui.fields.Date;

    @ui.decorators.numericField<OpenItemDiscount>({
        title: 'Discount date',
        width: 'small',
        isReadOnly: true,
    })
    discountDate: ui.fields.Numeric;

    @ui.decorators.dropdownListField<OpenItemDiscount>({
        title: 'Discount type',
        width: 'small',
        isReadOnly: true,
        optionType: '@sage/xtrem-master-data/PaymentTermDiscountOrPenaltyType',
    })
    discountType: ui.fields.DropdownList;

    @ui.decorators.numericField<OpenItemDiscount>({
        title() {
            return this.discountType.value === 'percentage'
                ? ui.localize(
                      '@sage/xtrem-finance/pages__accounts_receivable_open_item__discount_percentage',
                      'Discount percent',
                  )
                : ui.localize(
                      '@sage/xtrem-finance/pages__accounts_receivable_open_item__discount_amount',
                      'Discount amount',
                  );
        },
        width: 'small',
        isReadOnly: true,
        scale() {
            return this.discountType.value === 'percentage' ? 3 : (this.currency?.value?.decimalDigits ?? 2);
        },
        prefix() {
            return this.discountType.value === 'amount' ? (this.currency?.value?.symbol ?? '') : '';
        },
        postfix() {
            return this.discountType.value === 'percentage' ? '%' : '';
        },
    })
    discountAmount: ui.fields.Numeric;
}
