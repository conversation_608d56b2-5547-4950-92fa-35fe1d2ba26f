import type { Context, decimal, Dict, integer, NodeCreateData } from '@sage/xtrem-core';
import { asyncArray, Logger, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { DataInputError } from '@sage/xtrem-shared';
import type * as xtremSystem from '@sage/xtrem-system';
import * as _ from 'lodash';
import * as xtremFinance from '../index';
import { BaseFinanceDocument } from './base-finance-document';

const logger = Logger.getLogger(__filename, 'accounting-engine');

export class JournalEntry extends BaseFinanceDocument {
    protected override targetDocumentType: xtremFinanceData.enums.TargetDocumentType;

    protected override get documentType(): xtremFinanceData.enums.FinanceDocumentType {
        return this.financeIntegrationRecord.documentType;
    }

    protected override get documentNumber(): string {
        return this.financeIntegrationRecord.documentNumber;
    }

    protected override get batchId(): string {
        return this.financeIntegrationRecord.batchId;
    }

    protected override createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn = {
        documentsCreated: [],
        validationMessages: [] as xtremFinanceData.interfaces.ValidationMessage[],
    };

    private apArInvoice:
        | xtremFinance.nodes.AccountsPayableInvoice
        | xtremFinance.nodes.AccountsReceivableInvoice
        | null;

    private journalEntry: NodeCreateData<xtremFinance.nodes.JournalEntry>;

    /**
     * Controls that the target document type on the constructor is a journalEntry
     * @param targetDocumentType: the target document type
     */
    private setTargetDocumentType(targetDocumentType: xtremFinanceData.enums.TargetDocumentType): void {
        if (targetDocumentType !== 'journalEntry') {
            throw new DataInputError(
                xtremFinance.classes.LocalizedMessages.targetDocumentTypeNotSupported(this.context, targetDocumentType),
            );
        } else {
            this.targetDocumentType = targetDocumentType;
        }
    }

    constructor(
        protected override context: Context,
        protected override financeIntegrationRecord: xtremFinanceData.interfaces.FinanceTransactionRecord,
        private readonly replyTopic: string,
    ) {
        super(context, financeIntegrationRecord, logger);
    }

    /**
     * Returns the journal entry origin based on the document type
     */
    private getJournalEntryOrigin(): xtremFinanceData.enums.JournalOrigin {
        switch (this.documentType) {
            case 'salesCreditMemo':
            case 'salesInvoice':
            case 'salesShipment':
                return 'sales';
            case 'arInvoice':
                return 'arInvoice';
            case 'purchaseCreditMemo':
            case 'purchaseInvoice':
            case 'purchaseReceipt':
                return 'purchase';
            case 'apInvoice':
                return 'apInvoice';
            case 'workInProgress':
                return 'manufacturing';
            case 'miscellaneousStockIssue':
            case 'miscellaneousStockReceipt':
            case 'stockAdjustment':
            default:
                return 'stock';
        }
    }

    // Disabling grouping for the moment at the scope of XT-38458
    // /**
    //  * After getting a NodeCreateData<xtremFinance.nodes.JournalEntryLineDimension> (dimension line),
    //  * this function will add the line dimension to the journal entry line or, if it already exists a
    //  * line dimension with the same characteristics, update the line dimension values
    //  * @param journalEntryLine: the journal entry line where the dimension line will be added or updated
    //  * @param newAttributesAndDimensionsLines: the dimension line
    //  */
    // private static addOrUpdateAttributeAndDimensionLine(
    //     journalEntryLine: NodeCreateData<xtremFinance.nodes.JournalEntryLine>,
    //     newAttributesAndDimensionsLines: NodeCreateData<xtremFinance.nodes.JournalEntryLineDimension>[],
    // ): void {
    //     newAttributesAndDimensionsLines.forEach(newAttributesAndDimensionsLine => {
    //         const existingLine = journalEntryLine.attributesAndDimensions?.find(journalLineDimension => {
    //             return (
    //                 _.isEqual(newAttributesAndDimensionsLine.storedAttributes, journalLineDimension.storedAttributes) &&
    //                 _.isEqual(newAttributesAndDimensionsLine.storedDimensions, journalLineDimension.storedDimensions)
    //             );
    //         });

    //         if (existingLine && journalEntryLine && journalEntryLine.attributesAndDimensions) {
    //             journalEntryLine.attributesAndDimensions = journalEntryLine.attributesAndDimensions.map(
    //                 attributesAndDimensionsLine => {
    //                     if (
    //                         attributesAndDimensionsLine === existingLine &&
    //                         attributesAndDimensionsLine.companyAmount &&
    //                         attributesAndDimensionsLine.transactionAmount &&
    //                         attributesAndDimensionsLine.financialSiteAmount &&
    //                         newAttributesAndDimensionsLine.companyAmount &&
    //                         newAttributesAndDimensionsLine.transactionAmount &&
    //                         newAttributesAndDimensionsLine.financialSiteAmount
    //                     ) {
    //                         attributesAndDimensionsLine.companyAmount += newAttributesAndDimensionsLine.companyAmount;
    //                         attributesAndDimensionsLine.transactionAmount +=
    //                             newAttributesAndDimensionsLine.transactionAmount;
    //                         attributesAndDimensionsLine.financialSiteAmount +=
    //                             newAttributesAndDimensionsLine.financialSiteAmount;
    //                     }
    //                     return attributesAndDimensionsLine;
    //                 },
    //             );
    //         } else {
    //             journalEntryLine.attributesAndDimensions?.push(newAttributesAndDimensionsLine as object);
    //         }
    //     });
    // }

    /**
     * After getting a NodeCreateData<xtremFinance.nodes.JournalEntryLine> (journal line),
     * this function will add the journal line to the journal entry or, if it already exists a
     * journal line with the same characteristics, update the journal line values
     * @param newJournalEntryLine: the journal line line
     */
    public addOrUpdateLine(newJournalEntryLine: NodeCreateData<xtremFinance.nodes.JournalEntryLine>) {
        // Disabling grouping for the moment at the scope of XT-38458
        // const existingLine = this.journalEntry.lines?.find(journalLine => {
        //     return (
        //         journalLine.account === newJournalEntryLine.account &&
        //         journalLine.businessEntity === newJournalEntryLine.businessEntity &&
        //         journalLine.chartOfAccount === newJournalEntryLine.chartOfAccount &&
        //         journalLine.commonReference === newJournalEntryLine.commonReference &&
        //         journalLine.description === newJournalEntryLine.description &&
        //         journalLine.financialSite === newJournalEntryLine.financialSite &&
        //         journalLine.sign === newJournalEntryLine.sign &&
        //         journalLine.transactionCurrency === newJournalEntryLine.transactionCurrency &&
        //         journalLine.companyFxRate === newJournalEntryLine.companyFxRate &&
        //         journalLine.companyFxRateDivisor === newJournalEntryLine.companyFxRateDivisor &&
        //         journalLine.fxRateDate === newJournalEntryLine.fxRateDate &&
        //         journalLine.tax === newJournalEntryLine.tax &&
        //         journalLine.taxDate === newJournalEntryLine.taxDate &&
        //         journalLine.taxRate === newJournalEntryLine.taxRate &&
        //         journalLine.deductibleTaxRate === newJournalEntryLine.deductibleTaxRate &&
        //         journalLine.companyCurrency === newJournalEntryLine.companyCurrency &&
        //         journalLine.financialSiteCurrency === newJournalEntryLine.financialSiteCurrency &&
        //         journalLine.financialSiteFxRate === newJournalEntryLine.financialSiteFxRate &&
        //         journalLine.financialSiteFxRateDivisor === newJournalEntryLine.financialSiteFxRateDivisor
        //     );
        // });

        // if (
        //     existingLine &&
        //     this.journalEntry &&
        //     this.journalEntry.lines &&
        //     !(await this.journalEntryType.headerJournal)?.taxImpact &&
        //     ['excludingTax', 'reverseCharge', 'tax'].includes(
        //         await (existingLine.account as xtremFinanceData.nodes.Account).taxManagement,
        //     )
        // ) {
        //     this.journalEntry.lines = this.journalEntry.lines.map(journalLine => {
        //         if (
        //             journalLine === existingLine &&
        //             journalLine.companyAmount &&
        //             newJournalEntryLine.companyAmount &&
        //             journalLine.financialSiteAmount &&
        //             newJournalEntryLine.financialSiteAmount &&
        //             journalLine.transactionAmount &&
        //             newJournalEntryLine.transactionAmount
        //         ) {
        //             journalLine.companyAmount += newJournalEntryLine.companyAmount;
        //             journalLine.financialSiteAmount += newJournalEntryLine.financialSiteAmount;
        //             journalLine.transactionAmount += newJournalEntryLine.transactionAmount;
        //             if (newJournalEntryLine.attributesAndDimensions?.length) {
        //                 xtremFinance.classes.JournalEntry.addOrUpdateAttributeAndDimensionLine(
        //                     journalLine,
        //                     newJournalEntryLine.attributesAndDimensions,
        //                 );
        //             }
        //         }
        //         return journalLine;
        //     });
        // } else {
        this.journalEntry.lines?.push(newJournalEntryLine as object);
        // }
    }

    /**
     * From an accounting staging line, create an object that can be used to create a
     * journal entry line dimension record
     * @param documentLine: the accounting staging line
     * @param lineAmount: the already calculated amount in transaction currency for the journal entry line dimension record
     * @param companyAmount: the already calculated amount in company currency for the journal entry line dimension record
     *                       for the moment the amount in financialSiteCurrency is equal to the companyAmount,
     *                       later we maybe will need a FinancialSiteAmount parameter
     * @returns an array of line dimensions if there are any attributes or dimensions and an empty array if not
     */
    private static async getNewJournalEntryLineDimension(
        documentLine: xtremFinanceData.nodes.AccountingStaging,
        lineAmount: number,
        companyAmount: number,
    ): Promise<NodeCreateData<xtremFinance.nodes.JournalEntryLineDimension>[]> {
        const storedAttributes = await documentLine.storedAttributes;
        const storedDimensions = await documentLine.storedDimensions;
        return (
            storedAttributes || storedDimensions
                ? [
                      {
                          transactionAmount: lineAmount,
                          companyAmount,
                          financialSiteAmount: companyAmount,
                          storedAttributes,
                          storedDimensions,
                      },
                  ]
                : [{}]
        ) as Dict<string>[];
    }

    /**
     * Returns tax related data for a journal entry line
     * @param accountingStagingLine: the accounting staging line
     * @param accountingStagingLineAmount: the accounting staging amount line
     * @param account: the journal entry line account
     * @returns an object with tax related properties for a journal entry line
     */
    private async getTaxData(
        accountingStagingLine: xtremFinanceData.nodes.AccountingStaging,
        accountingStagingLineAmount: xtremFinanceData.nodes.AccountingStagingAmount,
        account: xtremFinanceData.nodes.Account,
    ): Promise<xtremFinanceData.interfaces.JournalEntryLineTaxData> {
        if (
            (await (await this.journalEntryType.headerJournal)?.taxImpact) &&
            (await (await (await accountingStagingLine.financialSite).legalCompany).taxEngine) !== 'avalaraAvaTax' &&
            ['excludingTax', 'tax', 'reverseCharge'].includes(await account.taxManagement)
        ) {
            return {
                tax: await accountingStagingLineAmount.tax,
                taxDate: (await accountingStagingLine.taxDate) || undefined,
                taxRate: await (await accountingStagingLineAmount.baseTax)?.taxRate,
                deductibleTaxRate: (await (await accountingStagingLineAmount.baseTax)?.deductibleTaxRate) || 0,
            };
        }
        return {};
    }

    /**
     * Gets the description to use on a journal entry header based on the setup
     * @returns The description to use on a journal entry header based on the setup
     */
    private async getJournalEntryDescription(): Promise<string> {
        switch (await this.journalEntryType.headerDescription) {
            case 'documentNumber':
                return this.documentNumber;
            case 'documentType':
                return xtremFinanceData.enums.financeDocumentTypeDataType.getLocalizedValue(
                    this.context,
                    await this.journalEntryType.documentType,
                );
            case 'transactionDescription':
                return this.accountingStagingRecords[0].description;
            default:
                return 'stock';
        }
    }

    /**
     * From an accounting stating line, the corresponding journal entry type line and account, returns an object that corresponds to a new NodeCreateData<xtremFinance.nodes.JournalEntryLine>
     * @param account: the account to use on this finance document line
     * @param accountingStagingLine: An accounting stanging record
     * @param journalEntryTypeLine: The journal entry type line to get setup from. It can be null when we are generating a journal entry from an ap\ar invoice
     * @param accountingStagingLineAmount: An accounting stanging amount record
     */
    public async getNewFinanceDocumentLine(
        account: xtremFinanceData.nodes.Account,
        accountingStagingLine: xtremFinanceData.nodes.AccountingStaging,
        journalEntryTypeLine: xtremFinanceData.nodes.JournalEntryTypeLine,
        accountingStagingLineAmount: xtremFinanceData.nodes.AccountingStagingAmount,
    ): Promise<NodeCreateData<xtremFinance.nodes.JournalEntryLine>> {
        // convert transaction amount into company currency
        const companyAmount =
            (await accountingStagingLine.transactionCurrency) !==
            (await (
                await (
                    await accountingStagingLine.financialSite
                ).legalCompany
            ).currency)
                ? xtremMasterData.sharedFunctions.convertAmount(
                      await accountingStagingLineAmount.amount,
                      await accountingStagingLine.companyFxRate,
                      await accountingStagingLine.companyFxRateDivisor,
                      await (
                          await accountingStagingLine.transactionCurrency
                      ).decimalDigits,
                      await (
                          await (
                              await (
                                  await accountingStagingLine.financialSite
                              ).legalCompany
                          ).currency
                      ).decimalDigits,
                  )
                : await accountingStagingLineAmount.amount;

        let sign: xtremFinanceData.enums.Sign = (await journalEntryTypeLine.sign)!;
        const stagingLineDocumentType = await accountingStagingLine.documentType;
        const apArInvoiceType = await this.apArInvoice?.type;
        if (
            sign &&
            ((stagingLineDocumentType === 'arInvoice' && apArInvoiceType === 'salesCreditMemo') ||
                (stagingLineDocumentType === 'apInvoice' && apArInvoiceType === 'purchaseCreditMemo'))
        ) {
            sign = sign === 'C' ? 'D' : 'C';
        }

        return {
            ...(await this.getTaxData(accountingStagingLine, accountingStagingLineAmount, account)),
            financialSite: await accountingStagingLine.financialSite,
            transactionCurrency: await accountingStagingLine.transactionCurrency,
            companyCurrency: await (await (await accountingStagingLine.financialSite).legalCompany).currency,
            financialSiteCurrency: await (await (await accountingStagingLine.financialSite).legalCompany).currency,
            companyFxRate: await accountingStagingLine.companyFxRate,
            companyFxRateDivisor: await accountingStagingLine.companyFxRateDivisor,
            fxRateDate: this.journalEntry.postingDate,
            account,
            businessEntity:
                ['apInvoice', 'arInvoice'].includes(await accountingStagingLine.documentType) &&
                (await account.isControl)
                    ? (await (await accountingStagingLine.customer)?.businessEntity) ||
                      (await (
                          await accountingStagingLine.supplier
                      )?.businessEntity) ||
                      null
                    : null,
            sign,
            transactionAmount: await accountingStagingLineAmount.amount,
            companyAmount,
            financialSiteAmount: companyAmount, // for the moment the financialSiteAmount is equal to the companyAmount
            description: (await accountingStagingLine.account)
                ? this.journalEntry.description
                : await (
                      await journalEntryTypeLine.accountType
                  )?.accountTypeName,
            commonReference:
                (await journalEntryTypeLine?.commonReference) === 'sourceDocumentNumber'
                    ? await accountingStagingLine.sourceDocumentNumber
                    : await accountingStagingLine.documentNumber,
            journalEntry: this.journalEntry?._id,
            chartOfAccount: (await account.chartOfAccount)._id,
            accountingStagingLines: [{ accountingStaging: accountingStagingLine }],
            attributesAndDimensions: await xtremFinance.classes.JournalEntry.getNewJournalEntryLineDimension(
                accountingStagingLine,
                await accountingStagingLineAmount.amount,
                companyAmount,
            ),
            journalEntryTypeLine: journalEntryTypeLine._id,
            ...(['excludingTax', 'includingTax', 'reverseCharge', 'tax'].includes(await account.taxManagement)
                ? {
                      baseTax: await accountingStagingLineAmount.baseTax,
                  }
                : {}),
        };
    }

    /**
     * From an accounting stating line and the corresponding journal entry type, returns an object that corresponds to a new NodeCreateData<xtremFinance.nodes.JournalEntry>
     */
    private async setJournalHeader(): Promise<void> {
        let documentType: string | undefined;
        if (this.apArInvoice && (await this.apArInvoice.origin) === 'invoice') {
            documentType = await (await this.journalEntryType.headerJournal)!.primaryDocumentType;
        }
        if (this.apArInvoice && (await this.apArInvoice.origin) === 'creditMemo') {
            documentType = await (await this.journalEntryType.headerJournal)!.secondaryDocumentType;
        }
        this.journalEntry = {
            number: this.apArInvoice ? await this.apArInvoice.number : undefined,
            arInvoice: this.documentType === 'arInvoice' ? this.apArInvoice?._id : null,
            apInvoice: this.documentType === 'apInvoice' ? this.apArInvoice?._id : null,
            financialSite: this.apArInvoice
                ? await this.apArInvoice.financialSite
                : await this.accountingStagingRecords[0].financialSite,
            postingStatus: 'draft',
            journal: (await this.journalEntryType.headerJournal)!,
            documentType,
            postingDate:
                (await this.journalEntryType.headerPostingDate) === 'endOfMonth'
                    ? (await this.accountingStagingRecords[0].documentDate).endOfMonth()
                    : await this.accountingStagingRecords[0].documentDate,
            description: await this.getJournalEntryDescription(),
            reference: this.apArInvoice ? await this.apArInvoice.reference : await this.journalEntryType.name,
            origin: this.getJournalEntryOrigin(),
            lines: [],
        };
    }

    /**
     * Updates ligns sign if we have negative amounts
     */
    private updateLignSign(): void {
        if (!this.journalEntry.lines?.length) {
            return;
        }
        this.journalEntry.lines.forEach(line => {
            if (line.transactionAmount && line.transactionAmount < 0) {
                line.sign = line.sign === 'C' ? 'D' : 'C';
                line.transactionAmount *= -1;
                line.companyAmount = -1 * (line.companyAmount || 0);
                line.financialSiteAmount = -1 * (line.financialSiteAmount || 0);
                if (line.attributesAndDimensions) {
                    line.attributesAndDimensions.forEach(attributesAndDimensionsLine => {
                        attributesAndDimensionsLine.transactionAmount =
                            -1 * (attributesAndDimensionsLine.transactionAmount || 0);
                        attributesAndDimensionsLine.companyAmount =
                            -1 * (attributesAndDimensionsLine.companyAmount || 0);
                        attributesAndDimensionsLine.financialSiteAmount =
                            -1 * (attributesAndDimensionsLine.financialSiteAmount || 0);
                    });
                }
            }
        });
    }

    /**
     * Creates an array of unique sites used in a given journal entrys
     */
    private getFinancialSiteUniqKey(): xtremSystem.nodes.Site[] {
        const financialSiteUniqKeyArray = Array.from(
            new Set(
                this.journalEntry.lines!.map(line => ({
                    financialSite: line.financialSite,
                })),
            ),
        );

        return _.uniqWith(financialSiteUniqKeyArray, _.isEqual);
    }

    /**
     * Calculates the balance of the amount in company currency for a given site of a journal entry
     * @param site: the site for which the balance should be calculated
     */
    private financialSiteLineBalance(site: xtremSystem.nodes.Site): Promise<decimal> {
        return asyncArray(this.journalEntry.lines!)
            .filter(async line => line.financialSite === (await site.financialSite))
            .sum(line => {
                logger.debug(() => ` ${line.financialSite} ${line.companyCurrency} ${line.sign} ${line.companyAmount}`);
                return (
                    ((line.sign as string) === 'D' ? (line.companyAmount as number) : 0) -
                    ((line.sign as string) === 'C' ? (line.companyAmount as number) : 0)
                );
            });
    }

    /**
     * Returns an object that corresponds to a new NodeCreateData<xtremFinance.nodes.JournalEntryLine>
     * A line for balancing rounding issues is created
     * @param balance: the balance amount in company currency
     * @param site: the site for which a balance line has to be created
     * @param accountingStagingLine: accounting staging line from which to take some values
     * @param postingClassLine: the posting class line to get the account from
     */
    private async getNewJournalEntryBalanceLine(
        balance: decimal,
        site: xtremSystem.nodes.Site,
        accountingStagingLine: xtremFinanceData.nodes.AccountingStaging,
        postingClassLine: xtremFinanceData.nodes.PostingClassLine,
    ): Promise<NodeCreateData<xtremFinance.nodes.JournalEntryLine>> {
        return {
            financialSite: site,
            transactionCurrency: await accountingStagingLine.transactionCurrency,
            companyFxRate: await accountingStagingLine.companyFxRate,
            companyFxRateDivisor: await accountingStagingLine.companyFxRateDivisor,
            fxRateDate: this.journalEntry.postingDate,
            account: await postingClassLine.account,
            businessEntity: null,
            sign: balance > 0 ? 'C' : 'D',
            transactionAmount: 0,
            companyAmount: balance > 0 ? balance : -balance,
            financialSiteAmount: balance > 0 ? balance : -balance, // for the moment same as companyAmount
            description: await (await postingClassLine.definition).accountTypeName,
            commonReference: '',
            journalEntry: this.journalEntry?._id,
            chartOfAccount: await postingClassLine.chartOfAccount,
            isBalanceLine: true,
            journalEntryTypeLine: null,
        };
    }

    /**
     * Checks for currency rounding issues on dimension level and correct them:
     * The total of companyAmount from journal entry line dimension must be equal to companyAmount previously calculated for the given journalEntryLine.
     * If ∑journalEntryLineDimension.companyAmount <> journalEntryLine.companyAmount, then we'll allocate the difference
     * (∑journalEntryLineDimension.companyAmount - journalEntryLine.companyAmount) to one of the journal entry dimension line.
     * How to determine which line to use? Read the journal entry dimension line for the given journal entry line from the 1st to last:
     *     if the sign (>0 or <0) of the journal entry dimension line is the same as the sign (>0 or <0) of the difference
     *     then update companyAmount for this journal entry line dimension : companyAmount = companyAmount + difference
     *     (meaning this line is impacted by the difference and so the loop can stop there)
     *       If the sign (>0 or <0) of the journal entry dimension line is NOT the same as the sign (>0 or <0) of the
     *       difference then update companyAmount for this journal entry line dimension
     *          if journalEntryLineDimension.companyAmount >= difference then updatejournalEntryLineDimension.companyAmount with the difference
     *          if journalEntryLineDimension.companyAmount < difference then read the next journal entry line dimension
     *       If there is no dimension line to update without changing its sign, we spread the difference to several lines
     *       until it is zero.
     */
    private handleDimensionLineCurrencyBalance(): void {
        this.journalEntry?.lines?.forEach(journalLine => {
            if (journalLine.transactionCurrency !== journalLine.companyCurrency) {
                //  Calculate the balance of the total amount in company currency of all linked dimension lines compared to
                //  the company amount of the actual journal entry line
                let balance =
                    journalLine && journalLine.companyAmount && journalLine.attributesAndDimensions
                        ? journalLine.companyAmount - // subtract total of dimension lines from line amount
                          journalLine.attributesAndDimensions.reduce((previousValue, line) => {
                              return previousValue + (line.companyAmount as number);
                          }, 0)
                        : 0;
                let done = false;
                if (balance !== 0 && journalLine && journalLine.attributesAndDimensions) {
                    // loop over all dimension lines and try to find a line where to add the balance without changing
                    // the sign of the line (should not change from positive (+) to negative (-) or vice versa)
                    journalLine?.attributesAndDimensions?.map(dimensionLine => {
                        if (!done && dimensionLine.companyAmount) {
                            // check for same sign
                            if (
                                (balance > 0 && dimensionLine.companyAmount > 0) ||
                                (balance < 0 && dimensionLine.companyAmount < 0)
                            ) {
                                // if same sign, add balance to companyAmount and set done to true
                                dimensionLine.companyAmount += balance;
                                done = true;
                            }
                            // signs differ
                            else if (Math.abs(dimensionLine.companyAmount) - Math.abs(balance) >= 0) {
                                // if the sign would not change add the balance to the companyAmount and set done to true
                                // if the sign would change, go ahead for the next dimension line
                                dimensionLine.companyAmount += balance;
                                done = true;
                            }
                        }
                        return dimensionLine;
                    });
                    if (!done) {
                        // if no dimension line was found where the balance could be added, spread it over several lines
                        journalLine?.attributesAndDimensions?.map(dimensionLine => {
                            if (!done && dimensionLine.companyAmount) {
                                // check for different sign
                                if (
                                    (balance > 0 && dimensionLine.companyAmount < 0) ||
                                    (balance < 0 && dimensionLine.companyAmount > 0)
                                ) {
                                    // if different sign, add balance to companyAmount but take care that the sign doesn't change
                                    if (Math.abs(dimensionLine.companyAmount) - Math.abs(balance) >= 0) {
                                        // last iteration
                                        // company amount won't change the sign any longer
                                        dimensionLine.companyAmount += balance;
                                        done = true;
                                        balance = 0;
                                    } else {
                                        // company amount would still change sign, set it to zero and reduce balance
                                        dimensionLine.companyAmount = 0; //
                                        balance += dimensionLine.companyAmount; // as signs differ it will reduce balance
                                    }
                                }
                            }
                            return dimensionLine;
                        });
                    }
                }
            }
        });
    }

    /**
     * Balances the journal in case of currency variances
     */
    private async balanceJournal(): Promise<void> {
        // Create an additional journal entry line if the journal entry is not balanced for a site to fix rounding issues
        const siteArray: xtremSystem.nodes.Site[] = this.getFinancialSiteUniqKey(); // get list of sites used in the journal
        await asyncArray(siteArray).forEach(async site => {
            // check balance for each site
            const balance = await this.financialSiteLineBalance(site);
            if (balance !== 0) {
                logger.debug(() => ` balance: ${balance}}`);
                // filter the array of accounting staging entries by the given site to get some properties from the first one
                const accountingStagingLine = (
                    await asyncArray(this.accountingStagingRecords)
                        .filter(
                            async stagingDocument =>
                                (await stagingDocument.financialSite) === (await site.financialSite),
                        )
                        .toArray()
                )[0];

                const companyPostingClassDefinition =
                    balance > 0
                        ? await this.context.read(xtremFinanceData.nodes.PostingClassDefinition, {
                              postingClassType: 'company',
                              id: 'CreditorRoundingVariance',
                              legislation: await (
                                  await (
                                      await accountingStagingLine.financialSite
                                  ).legalCompany
                              ).legislation,
                          })
                        : await this.context.read(xtremFinanceData.nodes.PostingClassDefinition, {
                              postingClassType: 'company',
                              id: 'DebtorRoundingVariance',
                              legislation: await (
                                  await (
                                      await accountingStagingLine.financialSite
                                  ).legalCompany
                              ).legislation,
                          });
                const companyPostingClass = await xtremFinanceData.functions.AccountingEngineCommon.getPostingClass(
                    this.context,
                    companyPostingClassDefinition,
                    await (
                        await (
                            await accountingStagingLine.financialSite
                        ).legalCompany
                    ).postingClass,
                );
                await companyPostingClass?.lines
                    .filter(
                        async postingClassLine =>
                            (await postingClassLine.definition) === companyPostingClassDefinition &&
                            (await postingClassLine.chartOfAccount) ===
                                (await (
                                    await (
                                        await accountingStagingLine.financialSite
                                    ).legalCompany
                                ).chartOfAccount),
                    )
                    .forEach(async postingClassLine => {
                        this.addOrUpdateLine(
                            await this.getNewJournalEntryBalanceLine(
                                balance,
                                (await site.financialSite)
                                    ? (await site.financialSite)!
                                    : await this.accountingStagingRecords[0].financialSite,
                                accountingStagingLine,
                                postingClassLine,
                            ),
                        );
                    });
            }
        });

        // check for currency rounding issues on dimension level
        this.handleDimensionLineCurrencyBalance();
    }

    protected async init(
        context: Context,
        financeIntegrationRecord: xtremFinanceData.interfaces.FinanceTransactionRecord,
    ): Promise<this> {
        this.setTargetDocumentType(financeIntegrationRecord.targetDocumentType);

        if (financeIntegrationRecord.documentType === 'apInvoice') {
            this.apArInvoice = await context.read(xtremFinance.nodes.AccountsPayableInvoice, {
                number: financeIntegrationRecord.documentNumber,
            });
        }
        if (financeIntegrationRecord.documentType === 'arInvoice') {
            this.apArInvoice = await context.read(xtremFinance.nodes.AccountsReceivableInvoice, {
                number: financeIntegrationRecord.documentNumber,
            });
        }
        return this;
    }

    /**
     * Creates a journal entry. Related with the update function. If create changes, review update.
     * @param context context
     * @param financeIntegrationRecord data that corresponds to the properties of a finance transaction main index
     * @param replyTopic the topic that it will be used to reply to
     */
    public async create(
        classContext: Context,
        financeIntegrationRecord: xtremFinanceData.interfaces.FinanceTransactionRecord,
        replyTopic: string,
        isUpdate: boolean,
        batchTrackingId?: string,
    ): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn> {
        const journalEntry = await new JournalEntry(classContext, financeIntegrationRecord, replyTopic).init(
            classContext,
            financeIntegrationRecord,
        );
        await journalEntry.setAccountingStagingRecords(isUpdate);
        if (journalEntry.createFinanceDocumentsReturn.validationMessages?.length) {
            return journalEntry.createFinanceDocumentsReturn;
        }
        await journalEntry.setJournalEntryType();
        if (journalEntry.createFinanceDocumentsReturn.validationMessages?.length) {
            return journalEntry.createFinanceDocumentsReturn;
        }

        await journalEntry.setJournalHeader();

        await journalEntry.setFinanceDocumentLines();

        if (!journalEntry.journalEntry.lines?.length) {
            journalEntry.createFinanceDocumentsReturn.validationMessages.push({
                type: ValidationSeverity.error,
                message: xtremFinance.classes.LocalizedMessages.journalToBeCreatedHasNoLines(journalEntry.context),
            });
            return journalEntry.createFinanceDocumentsReturn;
        }

        journalEntry.updateLignSign();

        await journalEntry.balanceJournal();

        logger.debug(
            () =>
                `Journal entry (creation) payload for document ${journalEntry.documentNumber} = ${JSON.stringify(
                    journalEntry.journalEntry,
                )}`,
        );

        if (!journalEntry.createFinanceDocumentsReturn.validationMessages?.length) {
            try {
                const createdJournal = await journalEntry.context.create(
                    xtremFinance.nodes.JournalEntry,
                    journalEntry.journalEntry,
                );
                await createdJournal.$.save();
                await xtremFinance.nodes.JournalEntry.post(this.context, createdJournal);

                journalEntry.createFinanceDocumentsReturn.documentsCreated.push({
                    type: journalEntry.targetDocumentType,
                    documentNumber: await createdJournal.number,
                    documentSysId: createdJournal._id,
                });

                if (!createdJournal.pHasReplyBeenSentFromExternalIntegration) {
                    await xtremFinance.functions.replyToOriginalDocument(
                        journalEntry.context,
                        journalEntry.financeIntegrationRecord,
                        'recorded',
                        journalEntry.createFinanceDocumentsReturn,
                        journalEntry.replyTopic,
                        '',
                        batchTrackingId,
                    );
                }
            } catch (error) {
                logger.error(error);
                journalEntry.createFinanceDocumentsReturn.validationMessages.push({
                    type: ValidationSeverity.error,
                    message:
                        error.extensions?.diagnoses?.length > 0 && error.extensions?.diagnoses[0].message
                            ? error.extensions.diagnoses[0].message
                            : error,
                });
            }
        }
        return journalEntry.createFinanceDocumentsReturn;
    }

    /**
     * Updates a journal entry. Related with the create function. If update changes, review create.
     * @param context context
     * @param financeIntegrationRecord data that corresponds to the properties of a finance transaction main index
     * @param targetDocumentSysId the _id of the existing journal entry
     * @param replyTopic the topic that it will be used to reply to
     */
    public async update(
        classContext: Context,
        financeIntegrationRecord: xtremFinanceData.interfaces.FinanceTransactionRecord,
        targetDocumentSysId: integer,
        replyTopic: string,
    ): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn> {
        const journalEntryToUpdate = await classContext.read(
            xtremFinance.nodes.JournalEntry,
            { _id: targetDocumentSysId },
            { forUpdate: true },
        );

        const journalEntry = await new JournalEntry(classContext, financeIntegrationRecord, replyTopic).init(
            classContext,
            financeIntegrationRecord,
        );

        await journalEntry.setAccountingStagingRecords(true);
        if (journalEntry.createFinanceDocumentsReturn.validationMessages?.length) {
            return journalEntry.createFinanceDocumentsReturn;
        }

        await journalEntry.setJournalEntryType();
        if (journalEntry.createFinanceDocumentsReturn.validationMessages?.length) {
            return journalEntry.createFinanceDocumentsReturn;
        }

        // In order to be able to delete the lines, we need first to delete
        // the references to other lines saved in contraJournalEntryLine
        journalEntryToUpdate.pCanNullifyContraJournalEntryLine = true;
        await journalEntryToUpdate.lines.forEach(async line => {
            await line.$.set({ contraJournalEntryLine: null });
        });
        await journalEntryToUpdate.$.save();

        const existingJournalEntryData = await journalEntryToUpdate.$.payload();
        existingJournalEntryData.lines = [];
        journalEntry.journalEntry = existingJournalEntryData;

        await journalEntry.setFinanceDocumentLines();

        if (!journalEntry.journalEntry.lines?.length) {
            journalEntry.createFinanceDocumentsReturn.validationMessages.push({
                type: ValidationSeverity.error,
                message: xtremFinance.classes.LocalizedMessages.journalToBeCreatedHasNoLines(journalEntry.context),
            });
            return journalEntry.createFinanceDocumentsReturn;
        }

        journalEntry.updateLignSign();

        await journalEntry.balanceJournal();

        // prepare an array for the new lines
        const documentLines: NodeCreateData<
            xtremFinance.nodes.JournalEntryLine & {
                _sortValue?: number;
                _action: 'delete' | 'create' | 'update';
            }
        >[] = [];

        // delete the existing lines on the original document
        await journalEntryToUpdate.lines.forEach(line => {
            documentLines.push({
                _action: 'delete',
                _id: line._id,
            });
        });

        // set the new lines
        journalEntry.journalEntry.lines.forEach(line => documentLines.push({ ...line, _action: 'create' }));
        await journalEntryToUpdate.$.set({
            lines: documentLines,
        });
        logger.debug(
            () =>
                `Journal entry (update) payload for document ${journalEntry.documentNumber} = ${JSON.stringify(
                    journalEntry.journalEntry,
                )}`,
        );

        if (!journalEntry.createFinanceDocumentsReturn.validationMessages?.length) {
            try {
                await journalEntryToUpdate.$.save();
                await xtremFinance.nodes.JournalEntry.post(this.context, journalEntryToUpdate);

                journalEntry.createFinanceDocumentsReturn.documentsCreated.push({
                    type: journalEntry.targetDocumentType,
                    documentNumber: await journalEntryToUpdate.number,
                    documentSysId: journalEntryToUpdate._id,
                });
            } catch (error) {
                logger.error(error);
                journalEntry.createFinanceDocumentsReturn.validationMessages.push({
                    type: ValidationSeverity.error,
                    message:
                        error.extensions?.diagnoses?.length > 0 && error.extensions?.diagnoses[0].message
                            ? error.extensions.diagnoses[0].message
                            : error,
                });
            }
        }
        return journalEntry.createFinanceDocumentsReturn;
    }
}
