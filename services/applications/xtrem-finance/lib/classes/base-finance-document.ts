import type { Context, Logger, NodeCreateData, NodeQueryFilter } from '@sage/xtrem-core';
import { asyncArray, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremFinance from '..';

export abstract class BaseFinanceDocument {
    protected targetDocumentType: xtremFinanceData.enums.TargetDocumentType;

    protected get documentType(): xtremFinanceData.enums.FinanceDocumentType {
        return this.financeIntegrationRecord.documentType;
    }

    protected get documentNumber(): string {
        return this.financeIntegrationRecord.documentNumber;
    }

    protected get batchId(): string {
        return this.financeIntegrationRecord.batchId;
    }

    protected createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn = {
        documentsCreated: [],
        validationMessages: [] as xtremFinanceData.interfaces.ValidationMessage[],
    };

    protected accountingStagingRecords: xtremFinanceData.nodes.AccountingStaging[];

    protected journalEntryType: xtremFinanceData.nodes.JournalEntryType;

    public currentAccountingStagingAmount: xtremFinanceData.nodes.AccountingStagingAmount;

    protected get stagingLineData(): Promise<xtremFinanceData.interfaces.StagingLineData> {
        return (async () => {
            const accountingStaging = await this.currentAccountingStagingAmount.accountingStaging;
            return {
                item: await accountingStaging.item,
                customer: await accountingStaging.customer,
                supplier: await accountingStaging.supplier,
                tax: await xtremFinanceData.functions.FinanceDocumentGeneration.getTax(this.context, {
                    tax: await this.currentAccountingStagingAmount.tax,
                    documentLineType: await this.currentAccountingStagingAmount.documentLineType,
                    createFinanceDocumentsReturn: this.createFinanceDocumentsReturn,
                    amount: await this.currentAccountingStagingAmount.amount,
                    baseTaxId: (await this.currentAccountingStagingAmount.baseTax)?._id,
                    taxId: (await this.currentAccountingStagingAmount.tax)?._id,
                }),
                resource: await accountingStaging.resource,
                account: await accountingStaging.account,
                documentNumber: await accountingStaging.documentNumber,
                documentType: await accountingStaging.documentType,
                movementType: await accountingStaging.movementType,
                amountType: await this.currentAccountingStagingAmount.amountType,
                financialSite: await accountingStaging.financialSite,
            };
        })();
    }

    /**
     * Sets the accounting staging records to process
     */
    protected async setAccountingStagingRecords(isUpdate = false): Promise<void> {
        const filter: NodeQueryFilter<xtremFinanceData.nodes.AccountingStaging> = {
            batchId: this.batchId,
            documentNumber: this.documentNumber,
            documentType: this.documentType,
            targetDocumentType: this.targetDocumentType,
            isProcessed: isUpdate, // we don't use the toBeReprocessed because if just part of the lines are marked as to be reprocessed, the engine will delete all document lines and use only the acc staging lines marked as to be reprocessed. This is the case when the same target document is generated from different source documents has more then one document which is a source for dimensions
        };

        const accountingStagingRecords = await this.context
            .query(xtremFinanceData.nodes.AccountingStaging, {
                filter,
                forUpdate: true,
            })
            .toArray();

        if (accountingStagingRecords.length === 0) {
            this.createFinanceDocumentsReturn.validationMessages.push({
                type: ValidationSeverity.info,
                message: xtremFinance.classes.LocalizedMessages.noDocumentsToProcess(
                    this.context,
                    this.targetDocumentType,
                    this.documentType,
                    this.documentNumber,
                ),
            });
            this.logger.warn(() => JSON.stringify(this.createFinanceDocumentsReturn.validationMessages));
        } else {
            this.accountingStagingRecords = accountingStagingRecords;
        }
    }

    protected getSupplierDocumentNumber(): Promise<string> {
        return this.accountingStagingRecords[0].supplierDocumentNumber;
    }

    /**
     * Sets the journal entry type to be used to process the accounting staging records
     */
    protected async setJournalEntryType(): Promise<void> {
        const journalEntryType = await xtremFinanceData.functions.AccountingEngineCommon.getJournalEntryType(
            this.context,
            await (
                await (
                    await this.accountingStagingRecords[0].financialSite
                ).legalCompany
            ).legislation,
            this.documentType,
            this.targetDocumentType,
        );

        if (!journalEntryType) {
            this.createFinanceDocumentsReturn.validationMessages.push({
                type: ValidationSeverity.info,
                message: xtremFinance.classes.LocalizedMessages.cantReadJournalEntryType(
                    this.context,
                    this.documentType,
                    this.documentNumber,
                    this.accountingStagingRecords[0]._id,
                ),
            });
            this.logger.warn(() => JSON.stringify(this.createFinanceDocumentsReturn.validationMessages));
        } else {
            this.journalEntryType = journalEntryType;
        }
    }

    /**
     * From an accounting stating line, the corresponding journal entry type line and account, returns an object that corresponds to a
     * new NodeCreateData<xtremFinance.nodes.JournalEntryLine>,
     * NodeCreateData<xtremFinance.nodes.AccountsPayableInvoiceLine> or NodeCreateData<xtremFinance.nodes.AccountsReceivableInvoiceLine>
     * @param accountingStagingLine: An accounting stanging record
     * @param journalEntryTypeLine: The journal entry type line to get setup from. It can be null when we are generating a journal entry from an ap\ar invoice
     * @param accountingStagingLineAmount: An accounting stanging amount record
     */
    abstract getNewFinanceDocumentLine(
        account: xtremFinanceData.nodes.Account,
        accountingStagingLine: xtremFinanceData.nodes.AccountingStaging,
        journalEntryTypeLine: xtremFinanceData.nodes.JournalEntryTypeLine,
        accountingStagingLineAmount?: xtremFinanceData.nodes.AccountingStagingAmount,
    ): Promise<
        | NodeCreateData<xtremFinance.nodes.JournalEntryLine>
        | NodeCreateData<xtremFinance.nodes.AccountsReceivableInvoiceLine>
        | NodeCreateData<xtremFinance.nodes.AccountsPayableInvoiceLine>
    >;

    abstract addOrUpdateLine(
        financeDocumentLine:
            | NodeCreateData<xtremFinance.nodes.JournalEntryLine>
            | NodeCreateData<xtremFinance.nodes.AccountsReceivableInvoiceLine>
            | NodeCreateData<xtremFinance.nodes.AccountsPayableInvoiceLine>,
    ): void;

    /**
     * From the accounting staging records, add all the finance document lines to the finance document to be created
     */
    protected async setFinanceDocumentLines(): Promise<void> {
        // iterate through all staging table lines related with this document
        await asyncArray(this.accountingStagingRecords).forEach(async stagingLine => {
            // update item posting class on staging
            const stagingLineItem = await stagingLine.item;
            await stagingLine.$.set({
                itemPostingClass: stagingLineItem ? await stagingLineItem.postingClass : null,
            });
            // update customer posting class on staging
            const stagingLineCustomer = await stagingLine.customer;
            await stagingLine.$.set({
                customerPostingClass: stagingLineCustomer ? await stagingLineCustomer.postingClass : null,
            });
            // update supplier posting class on staging
            const stagingLineSupplier = await stagingLine.supplier;
            await stagingLine.$.set({
                supplierPostingClass: stagingLineSupplier ? await stagingLineSupplier.postingClass : null,
            });

            // control if we have at least one amount for this staging line
            if ((await stagingLine.amounts.length) === 0) {
                this.createFinanceDocumentsReturn.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinance.classes.LocalizedMessages.cantReadAccountingStagingAmount(
                        this.context,
                        await stagingLine.documentNumber,
                        stagingLine._id,
                    ),
                });
            }

            await stagingLine.amounts.forEach(async stagingLineAmount => {
                this.currentAccountingStagingAmount = stagingLineAmount;
                const stagingLineAmountTax = await stagingLineAmount.tax;
                if (stagingLineAmountTax) {
                    await stagingLineAmount.$.set({
                        taxPostingClass: await stagingLineAmountTax.postingClass,
                    });
                }
                await xtremFinanceData.functions.FinanceDocumentGeneration.processFinanceIntegrationDocumentLineAmount(
                    this.context,
                    this.journalEntryType,
                    await this.stagingLineData,
                    this.createFinanceDocumentsReturn,
                    xtremFinance.functions.addOrUpdateFinanceDocumentLineCallback,
                    this,
                );
            });
            if (!this.createFinanceDocumentsReturn.validationMessages?.length) {
                await stagingLine.$.set({ isProcessed: true });
                await stagingLine.$.save();
            }
        });
    }

    constructor(
        protected context: Context,
        protected financeIntegrationRecord: xtremFinanceData.interfaces.FinanceTransactionRecord,
        protected logger: Logger,
    ) {}
}
