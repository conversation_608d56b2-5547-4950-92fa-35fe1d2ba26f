import type { Context } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';

export class LocalizedMessages {
    public static targetDocumentTypeNotSupported(
        context: Context,
        targetDocumentType: xtremFinanceData.enums.TargetDocumentType,
    ): string {
        return context.localize(
            '@sage/xtrem-finance/classes__localized-messages__target_document_type_not_supported',
            'The target document type {{targetDocumentType}} is not supported.',
            {
                targetDocumentType: context.localizeEnumMember(
                    '@sage/xtrem-finance-data/TargetDocumentType',
                    targetDocumentType,
                ),
            },
        );
    }

    public static noDocumentsToProcess(
        context: Context,
        targetDocumentType: xtremFinanceData.enums.TargetDocumentType,
        documentType: xtremFinanceData.enums.FinanceDocumentType,
        documentNumber: string,
    ): string {
        return context.localize(
            '@sage/xtrem-finance/classes__localized-messages__no_documents_to_process',
            'There are no documents to process for document type {{documentType}}, target document type {{targetDocumentType}} and document number {{documentNumber}}.',
            {
                documentType: context.localizeEnumMember('@sage/xtrem-finance-data/FinanceDocumentType', documentType),
                targetDocumentType: context.localizeEnumMember(
                    '@sage/xtrem-finance-data/TargetDocumentType',
                    targetDocumentType,
                ),
                documentNumber,
            },
        );
    }

    public static cantReadJournalEntryType(
        context: Context,
        documentType: xtremFinanceData.enums.FinanceDocumentType,
        documentNumber: string,
        documentId: number,
    ): string {
        return context.localize(
            '@sage/xtrem-finance/classes__localized-messages__cant_read_journal_entry_type',
            'The journal entry type for the {{documentType}} with the document number {{documentNumber}} {{documentId}} could not be found.',
            {
                documentType: context.localizeEnumMember('@sage/xtrem-finance-data/FinanceDocumentType', documentType),
                documentNumber,
                documentId,
            },
        );
    }

    public static cantReadAccountingStagingAmount(
        context: Context,
        documentNumber: string,
        documentNumberId: number,
    ): string {
        return context.localize(
            '@sage/xtrem-finance/classes__localized-messages__cant_read_accounting_staging_amount',
            'The accounting staging amount for the document number {{documentNumber}} {{documentNumberId}} could not be found.',
            {
                documentNumber,
                documentNumberId,
            },
        );
    }

    public static journalToBeCreatedHasNoLines(context: Context): string {
        return context.localize(
            '@sage/xtrem-finance/classes__localized-messages__journal_to_be_created_has_no_lines',
            'The journal entry to be created has no lines.',
        );
    }

    public static invoiceNotFound(context: Context, documentNumber: string) {
        return context.localize(
            '@sage/xtrem-finance/classes__localized-messages__no_invoice',
            'The invoice number {{documentNumber}} could not be found.',
            {
                documentNumber,
            },
        );
    }

    public static unableToGetAccount(context: Context): string {
        return context.localize(
            '@sage/xtrem-finance/classes__localized-messages__unable_to_get_account',
            'The account cannot be determined.',
        );
    }
}
