import type { Context, integer, NodeCreateData } from '@sage/xtrem-core';
import { Logger, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import { DataInputError } from '@sage/xtrem-shared';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinance from '../index';
import { BaseFinanceDocument } from './base-finance-document';

const logger = Logger.getLogger(__filename, 'accounting-engine');

export class AccountsPayableReceivableInvoice extends BaseFinanceDocument {
    private account: xtremFinanceData.nodes.Account;

    private paymentTracking: xtremFinanceData.nodes.PaymentTracking | null;

    public accountsPayableReceivableInvoice:
        | NodeCreateData<xtremFinance.nodes.AccountsReceivableInvoice>
        | NodeCreateData<xtremFinance.nodes.AccountsPayableInvoice>;

    /**
     * Controls that the target document type on the constructor is accountsReceivableInvoice or accountsPayableInvoice
     * @param targetDocumentType: the target document type
     */
    private setTargetDocumentType(targetDocumentType: xtremFinanceData.enums.TargetDocumentType) {
        if (!['accountsReceivableInvoice', 'accountsPayableInvoice'].includes(targetDocumentType)) {
            throw new DataInputError(
                xtremFinance.classes.LocalizedMessages.targetDocumentTypeNotSupported(this.context, targetDocumentType),
            );
        }
        this.targetDocumentType = targetDocumentType;
    }

    /**
     * Sets the payment tracking record for the back link to the purchase invoice or credit memo
     */
    private setPaymentTracking(financeTransaction: xtremFinanceData.interfaces.FinanceTransactionRecord) {
        this.paymentTracking = financeTransaction.paymentTracking ?? null;
    }

    constructor(
        protected override context: Context,
        protected override financeIntegrationRecord: xtremFinanceData.interfaces.FinanceTransactionRecord,
        private readonly replyTopic: string,
    ) {
        super(context, financeIntegrationRecord, logger);
    }

    /**
     * Sets the account to be used as the account for the business partner on an ap\ar invoice header
     */
    private async setApArInvoiceAccount(): Promise<void> {
        const account = await xtremFinanceData.functions.AccountingEngineCommon.getApArInvoiceAccount(
            this.context,
            this.journalEntryType,
            this.targetDocumentType,
            this.documentType,
            this.documentNumber,
            await this.accountingStagingRecords[0].customer,
            await this.accountingStagingRecords[0].supplier,
            await (
                await (
                    await this.accountingStagingRecords[0].financialSite
                ).legalCompany
            ).chartOfAccount,
            this.createFinanceDocumentsReturn,
        );

        if (account) {
            this.account = account;
        }
    }

    /**
     * Returns the accounts receivable invoice origin based on the document type
     */
    private getAPARInvoiceOrigin(): xtremFinanceData.enums.AccountsPayableReceivableInvoiceOrigin {
        switch (this.documentType) {
            case 'salesCreditMemo':
            case 'purchaseCreditMemo':
                return 'creditMemo';
            case 'salesInvoice':
            case 'purchaseInvoice':
                return 'invoice';
            default:
                return 'invoice';
        }
    }

    private getNewAPARInvoiceTaxes(): Promise<object[]> {
        return this.context
            .query(xtremFinanceData.nodes.AccountingStagingDocumentTax, {
                filter: {
                    batchId: this.batchId,
                    documentNumber: this.documentNumber,
                    documentType: this.documentType,
                    targetDocumentType: this.targetDocumentType,
                },
            })
            .map(async (accountingStagingDocumentTax: xtremFinanceData.nodes.AccountingStagingDocumentTax) => {
                const baseTax = await (
                    await this.context.read(xtremTax.nodes.BaseTax, {
                        _id: (await accountingStagingDocumentTax.baseTax)._id,
                    })
                ).$.payload({ withIds: true });
                delete baseTax._id;
                return baseTax;
            })
            .toArray();
    }

    /**
     * From an accounting stating line and the corresponding journal entry type, returns an object that corresponds to a new NodeCreateData<xtremFinance.nodes.AccountsReceivableInvoice>
     */
    private async getNewARInvoice(): Promise<NodeCreateData<xtremFinance.nodes.AccountsReceivableInvoice>> {
        const accountingStagingRecord = this.accountingStagingRecords[0];
        const financialSite = await accountingStagingRecord.financialSite;
        const businessEntity = await (await accountingStagingRecord.customer)?.businessEntity;

        return {
            financialSite,
            financialSiteName: await financialSite.name,
            financialSiteTaxIdNumber: await (await financialSite.businessEntity).taxIdNumber,
            dueDate: (await accountingStagingRecord.dueDate) || undefined,
            taxCalculationStatus: (await accountingStagingRecord.taxCalculationStatus) || undefined,
            type: await accountingStagingRecord.documentType,
            number: await accountingStagingRecord.documentNumber,
            invoiceDate: await accountingStagingRecord.documentDate,
            postingDate: await accountingStagingRecord.documentDate,
            billToCustomer: (await accountingStagingRecord.customer) || undefined,
            billToCustomerName: await businessEntity?.name,
            billToCustomerTaxIdNumber: await businessEntity?.taxIdNumber,
            companyFxRate: await accountingStagingRecord.companyFxRate,
            companyFxRateDivisor: await accountingStagingRecord.companyFxRateDivisor,
            fxRateDate: await accountingStagingRecord.fxRateDate,
            currency: await accountingStagingRecord.transactionCurrency,
            paymentTerm: (await accountingStagingRecord.paymentTerm) || undefined,
            postingStatus: 'draft',
            isPrinted: await accountingStagingRecord.isPrinted,
            origin: this.getAPARInvoiceOrigin(),
            account: this.account!,
            salesDocumentNumber: await accountingStagingRecord.documentNumber,
            salesDocumentSysId: await accountingStagingRecord.documentSysId,
            lines: [],
            taxes: await this.getNewAPARInvoiceTaxes(),
        };
    }

    /**
     * From an accounting stating line and the corresponding journal entry type, returns an object that corresponds to a new NodeCreateData<xtremFinance.nodes.AccountsPayableInvoice>
     */
    private async getNewAPInvoice(): Promise<NodeCreateData<xtremFinance.nodes.AccountsPayableInvoice>> {
        const accountingStagingRecord = this.accountingStagingRecords[0];
        const financialSite = await accountingStagingRecord.financialSite;
        const businessEntity = await (await accountingStagingRecord.supplier)?.businessEntity;

        return {
            financialSite,
            financialSiteName: await financialSite.name,
            financialSiteTaxIdNumber: await (await financialSite.businessEntity).taxIdNumber,
            dueDate: (await accountingStagingRecord.dueDate) || undefined,
            type: await accountingStagingRecord.documentType,
            number: await accountingStagingRecord.documentNumber,
            invoiceDate: await accountingStagingRecord.documentDate,
            postingDate: await accountingStagingRecord.documentDate,
            payToSupplier: (await accountingStagingRecord.payToSupplier) || undefined,
            payToSupplierLinkedAddress: (await accountingStagingRecord.payToSupplierLinkedAddress) || undefined,
            returnLinkedAddress: (await accountingStagingRecord.returnLinkedAddress) || undefined,
            billBySupplier: (await accountingStagingRecord.supplier) || undefined,
            billBySupplierName: await businessEntity?.name,
            billBySupplierTaxIdNumber: await businessEntity?.taxIdNumber,
            companyFxRate: await accountingStagingRecord.companyFxRate,
            companyFxRateDivisor: await accountingStagingRecord.companyFxRateDivisor,
            fxRateDate: await accountingStagingRecord.fxRateDate,
            currency: await accountingStagingRecord.transactionCurrency,
            paymentTerm: (await accountingStagingRecord.paymentTerm) || undefined,
            postingStatus: 'draft',
            supplierDocumentDate: (await accountingStagingRecord.supplierDocumentDate) || undefined,
            supplierDocumentNumber: await accountingStagingRecord.supplierDocumentNumber,
            origin: this.getAPARInvoiceOrigin(),
            account: this.account!,
            purchaseDocumentNumber: await accountingStagingRecord.documentNumber,
            purchaseDocumentSysId: await accountingStagingRecord.documentSysId,
            lines: [],
            taxes: await this.getNewAPARInvoiceTaxes(),
            paymentTracking: this.paymentTracking,
        };
    }

    /**
     * Sets the ap\ar invoice header
     */
    private async setAccountsPayableReceivableInvoiceHeader(): Promise<void> {
        // create an Header for the AP or AR Invoice
        this.accountsPayableReceivableInvoice =
            this.targetDocumentType === 'accountsReceivableInvoice'
                ? await this.getNewARInvoice()
                : await this.getNewAPInvoice();
    }

    /**
     * From an accounting staging line, created an object that can be used to create an
     * ap\ar invoice line dimension record
     * @param documentLine: the accounting staging line
     * @param lineAmount: the already calculated amount for the ap\ar invoice line dimension record
     * @returns an array of line dimensions if there are any attributes or dimensions and an empty array if not
     */
    private static async getNewAPARInvoiceLineDimension(
        documentLine: xtremFinanceData.nodes.AccountingStaging,
        lineAmount: number,
    ): Promise<object[]> {
        const storedAttributes = await documentLine.storedAttributes;
        const storedDimensions = await documentLine.storedDimensions;
        return storedAttributes || storedDimensions
            ? [
                  {
                      amount: lineAmount,
                      storedAttributes,
                      storedDimensions,
                  },
              ]
            : [{}];
    }

    /**
     * Returns the accounts receivable invoice line type based on the item type
     * @param itemType: the item type
     */
    private static getAPARInvoiceLineType(
        itemType: xtremMasterData.enums.ItemType,
    ): xtremFinanceData.enums.AccountsPayableReceivableInvoiceLineType {
        switch (itemType) {
            case 'service':
            case 'landedCost':
                return 'services';
            case 'good':
            default:
                return 'goods';
        }
    }

    /**
     * From an accounting staging line, the corresponding journal entry type line and postingClasse, returns an object that corresponds to a
     * new NodeCreateData<xtremFinance.nodes.AccountsPayableInvoiceLine> or NodeCreateData<xtremFinance.nodes.AccountsReceivableInvoiceLine>
     * @param account: the account to use on this finance document line
     * @param accountingStagingLine: the accounting staging line to data from
     * @param journalEntryTypeLine: the journal entry type line to get setup from
     */
    public async getNewFinanceDocumentLine(
        account: xtremFinanceData.nodes.Account,
        accountingStagingLine: xtremFinanceData.nodes.AccountingStaging,
        journalEntryTypeLine: xtremFinanceData.nodes.JournalEntryTypeLine,
        accountingStagingLineAmount: xtremFinanceData.nodes.AccountingStagingAmount,
    ): Promise<
        | NodeCreateData<xtremFinance.nodes.JournalEntryLine>
        | NodeCreateData<xtremFinance.nodes.AccountsReceivableInvoiceLine>
        | NodeCreateData<xtremFinance.nodes.AccountsPayableInvoiceLine>
    > {
        const lineAmount =
            (await accountingStagingLineAmount.documentLineType) === 'documentLine'
                ? await accountingStagingLine.amounts
                      .filter(
                          async documentLineAmount =>
                              (await documentLineAmount.amountType) === (await journalEntryTypeLine.amountType),
                      )
                      .sum(accountStagingAmountLine => accountStagingAmountLine.amount)
                : 0; // force zero for US tax (amount stored in taxLineTaxAmount property)

        const jurisdictionName = await (await accountingStagingLineAmount.baseTax)?.jurisdictionName;
        const stagingLineTaxDate = await accountingStagingLine.taxDate;
        const taxDate = stagingLineTaxDate ?? (await accountingStagingLine.documentDate);
        return {
            financialSite: await accountingStagingLine.financialSite,
            taxDate,
            providerSite: (await accountingStagingLine.providerSite) || undefined,
            recipientSite: (await accountingStagingLine.recipientSite) || undefined,
            account,
            lineType: xtremFinance.classes.AccountsPayableReceivableInvoice.getAPARInvoiceLineType(
                await (await accountingStagingLine.item)!.type,
            ),
            currency: this.accountsPayableReceivableInvoice.currency,
            sourceDocumentNumber:
                (await journalEntryTypeLine.commonReference) === 'sourceDocumentNumber'
                    ? await accountingStagingLine.sourceDocumentNumber
                    : await accountingStagingLine.documentNumber,
            amountExcludingTax: lineAmount,
            accountingStagingLines: [{ accountingStaging: accountingStagingLine }],
            attributesAndDimensions:
                await xtremFinance.classes.AccountsPayableReceivableInvoice.getNewAPARInvoiceLineDimension(
                    accountingStagingLine,
                    (await accountingStagingLineAmount.documentLineType) === 'documentLine'
                        ? lineAmount
                        : await accountingStagingLineAmount.amount,
                ),
            taxes: await accountingStagingLine.taxes
                .map(async accountingStagingLineTax => {
                    const baseTax = await (await accountingStagingLineTax.baseTax).$.payload({ withIds: true });
                    delete baseTax._id;
                    return baseTax;
                })
                .toArray(),
            documentLineType: await accountingStagingLineAmount.documentLineType,
            // US tax: populate amount to taxLineTaxAmount instead of amountExcludingTax
            taxLineTaxAmount:
                (await accountingStagingLineAmount.documentLineType) === 'taxLine'
                    ? await accountingStagingLineAmount.amount
                    : 0,
            taxDetail:
                (await accountingStagingLineAmount.documentLineType) === 'taxLine'
                    ? `${jurisdictionName} ${await (await accountingStagingLineAmount.baseTax)?.tax} (${await (
                          await accountingStagingLineAmount.baseTax
                      )?.taxRate})`
                    : '',
        };
    }

    /**
     * After getting a NodeCreateData<xtremFinance.nodes.AccountsPayableInvoiceLine> or NodeCreateData<xtremFinance.nodes.AccountsPayableInvoiceLine> (ap or ar line),
     * this function will add the invoice line to the invoice. On ap and ar invoices we do not group lines
     * @param newAPARInvoiceLineLine: the ap or ar invoice line
     */
    public addOrUpdateLine(
        newAPARInvoiceLineLine:
            | NodeCreateData<xtremFinance.nodes.AccountsPayableInvoiceLine>
            | NodeCreateData<xtremFinance.nodes.AccountsReceivableInvoiceLine>,
    ) {
        this.accountsPayableReceivableInvoice.lines?.push(newAPARInvoiceLineLine as Object);
    }

    protected init(financeIntegrationRecord: xtremFinanceData.interfaces.FinanceTransactionRecord): this {
        this.setTargetDocumentType(financeIntegrationRecord.targetDocumentType);
        return this;
    }

    public static async create(
        classContext: Context,
        financeIntegrationRecord: xtremFinanceData.interfaces.FinanceTransactionRecord,
        replyTopic: string,
        isUpdate: boolean,
        batchTrackingId?: string,
    ): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn> {
        const accountsPayableReceivableInvoice = new AccountsPayableReceivableInvoice(
            classContext,
            financeIntegrationRecord,
            replyTopic,
        ).init(financeIntegrationRecord);
        accountsPayableReceivableInvoice.setPaymentTracking(financeIntegrationRecord);
        await accountsPayableReceivableInvoice.setAccountingStagingRecords(false);
        if (accountsPayableReceivableInvoice.createFinanceDocumentsReturn.validationMessages?.length) {
            return accountsPayableReceivableInvoice.createFinanceDocumentsReturn;
        }
        await accountsPayableReceivableInvoice.setJournalEntryType();
        if (accountsPayableReceivableInvoice.createFinanceDocumentsReturn.validationMessages?.length) {
            return accountsPayableReceivableInvoice.createFinanceDocumentsReturn;
        }
        await accountsPayableReceivableInvoice.setApArInvoiceAccount();
        if (accountsPayableReceivableInvoice.createFinanceDocumentsReturn.validationMessages?.length) {
            return accountsPayableReceivableInvoice.createFinanceDocumentsReturn;
        }
        await accountsPayableReceivableInvoice.setAccountsPayableReceivableInvoiceHeader();
        await accountsPayableReceivableInvoice.setFinanceDocumentLines();

        logger.debug(
            () =>
                `AP-AR invoice for document ${accountsPayableReceivableInvoice.documentNumber} =${JSON.stringify(
                    accountsPayableReceivableInvoice.accountsPayableReceivableInvoice,
                )}`,
        );

        if (!accountsPayableReceivableInvoice.createFinanceDocumentsReturn.validationMessages?.length) {
            try {
                const createdAPARInvoice =
                    accountsPayableReceivableInvoice.targetDocumentType === 'accountsReceivableInvoice'
                        ? await accountsPayableReceivableInvoice.context.create(
                              xtremFinance.nodes.AccountsReceivableInvoice,
                              accountsPayableReceivableInvoice.accountsPayableReceivableInvoice as NodeCreateData<xtremFinance.nodes.AccountsReceivableInvoice>,
                          )
                        : await accountsPayableReceivableInvoice.context.create(
                              xtremFinance.nodes.AccountsPayableInvoice,
                              accountsPayableReceivableInvoice.accountsPayableReceivableInvoice as NodeCreateData<xtremFinance.nodes.AccountsPayableInvoice>,
                          );
                await createdAPARInvoice.$.save();
                if (accountsPayableReceivableInvoice.targetDocumentType === 'accountsReceivableInvoice') {
                    await xtremFinance.nodes.AccountsReceivableInvoice.post(
                        accountsPayableReceivableInvoice.context,
                        createdAPARInvoice as xtremFinance.nodes.AccountsReceivableInvoice,
                    );
                } else {
                    await xtremFinance.nodes.AccountsPayableInvoice.post(
                        accountsPayableReceivableInvoice.context,
                        createdAPARInvoice as xtremFinance.nodes.AccountsPayableInvoice,
                    );
                }

                accountsPayableReceivableInvoice.createFinanceDocumentsReturn.documentsCreated.push({
                    type: accountsPayableReceivableInvoice.targetDocumentType,
                    documentNumber: await createdAPARInvoice.number,
                    documentSysId: createdAPARInvoice._id,
                });

                if (!createdAPARInvoice.pHasReplyBeenSentFromExternalIntegration) {
                    await xtremFinance.functions.replyToOriginalDocument(
                        accountsPayableReceivableInvoice.context,
                        accountsPayableReceivableInvoice.financeIntegrationRecord,
                        'recorded',
                        accountsPayableReceivableInvoice.createFinanceDocumentsReturn,
                        accountsPayableReceivableInvoice.replyTopic,
                        '',
                        batchTrackingId,
                    );
                }
            } catch (error) {
                accountsPayableReceivableInvoice.createFinanceDocumentsReturn.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: JSON.stringify(classContext.diagnoses).concat(error),
                });
            }
        }

        return accountsPayableReceivableInvoice.createFinanceDocumentsReturn;
    }

    public static async update(
        classContext: Context,
        financeIntegrationRecord: xtremFinanceData.interfaces.FinanceTransactionRecord,
        targetDocumentSysId: integer,
        replyTopic: string,
    ): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn> {
        const accountsPayableReceivableInvoiceToUpdate =
            financeIntegrationRecord.targetDocumentType === 'accountsPayableInvoice'
                ? await classContext.read(
                      xtremFinance.nodes.AccountsPayableInvoice,
                      { _id: targetDocumentSysId },
                      { forUpdate: true },
                  )
                : await classContext.read(
                      xtremFinance.nodes.AccountsReceivableInvoice,
                      { _id: targetDocumentSysId },
                      { forUpdate: true },
                  );

        const accountsPayableReceivableInvoice = new AccountsPayableReceivableInvoice(
            classContext,
            financeIntegrationRecord,
            replyTopic,
        ).init(financeIntegrationRecord);

        await accountsPayableReceivableInvoice.setAccountingStagingRecords(true);
        if (accountsPayableReceivableInvoice.createFinanceDocumentsReturn.validationMessages?.length) {
            return accountsPayableReceivableInvoice.createFinanceDocumentsReturn;
        }
        await accountsPayableReceivableInvoice.setJournalEntryType();
        if (accountsPayableReceivableInvoice.createFinanceDocumentsReturn.validationMessages?.length) {
            return accountsPayableReceivableInvoice.createFinanceDocumentsReturn;
        }

        // with accountsPayableReceivableInvoiceToUpdate.$.payload(), the currency property was empty!
        const existingAccountsPayableReceivableInvoiceData = JSON.parse(
            JSON.stringify(accountsPayableReceivableInvoiceToUpdate),
        );
        existingAccountsPayableReceivableInvoiceData.lines = [];
        accountsPayableReceivableInvoice.accountsPayableReceivableInvoice =
            existingAccountsPayableReceivableInvoiceData;
        await accountsPayableReceivableInvoice.setFinanceDocumentLines();

        // prepare an array for the new lines
        const documentLines: NodeCreateData<
            (xtremFinance.nodes.AccountsPayableInvoiceLine | xtremFinance.nodes.AccountsReceivableInvoiceLine) & {
                _sortValue?: number;
                _action: 'delete' | 'create' | 'update';
            }
        >[] = [];

        // delete the existing lines on the original document
        await accountsPayableReceivableInvoiceToUpdate.lines.forEach(line => {
            documentLines.push({
                _action: 'delete',
                _id: line._id,
            });
        });

        // set the new lines
        accountsPayableReceivableInvoice.accountsPayableReceivableInvoice.lines?.forEach(line =>
            documentLines.push({ ...line, _action: 'create' }),
        );

        const documentData = accountsPayableReceivableInvoice.accountingStagingRecords[0];
        accountsPayableReceivableInvoiceToUpdate.pCanUpdateFromExternalIntegration = true;
        await accountsPayableReceivableInvoiceToUpdate.$.set(
            financeIntegrationRecord.targetDocumentType === 'accountsPayableInvoice'
                ? {
                      supplierDocumentNumber: await accountsPayableReceivableInvoice.getSupplierDocumentNumber(),
                      supplierDocumentDate: (await documentData.supplierDocumentDate) || undefined,
                      paymentTerm: await documentData.paymentTerm,
                      dueDate: (await documentData.dueDate) || undefined,
                      taxes: await accountsPayableReceivableInvoice.getNewAPARInvoiceTaxes(),
                      lines: documentLines as any, // since it can be an ap or ar invoice line array, i did not find any other way than using any
                  }
                : {
                      paymentTerm: await documentData.paymentTerm,
                      dueDate: (await documentData.dueDate) || undefined,
                      taxes: await accountsPayableReceivableInvoice.getNewAPARInvoiceTaxes(),
                      lines: documentLines as any, // since it can be an ap or ar invoice line array, i did not find any other way than using any} )
                  },
        );

        logger.debug(
            () =>
                `AP-AR invoice update for document ${accountsPayableReceivableInvoice.documentNumber} =${JSON.stringify(
                    accountsPayableReceivableInvoice.accountsPayableReceivableInvoice,
                )}`,
        );

        if (!accountsPayableReceivableInvoice.createFinanceDocumentsReturn.validationMessages?.length) {
            try {
                await accountsPayableReceivableInvoiceToUpdate.$.save();
                if (accountsPayableReceivableInvoice.targetDocumentType === 'accountsReceivableInvoice') {
                    await xtremFinance.nodes.AccountsReceivableInvoice.post(
                        classContext,
                        accountsPayableReceivableInvoiceToUpdate as xtremFinance.nodes.AccountsReceivableInvoice,
                    );
                } else {
                    await xtremFinance.nodes.AccountsPayableInvoice.post(
                        classContext,
                        accountsPayableReceivableInvoiceToUpdate as xtremFinance.nodes.AccountsPayableInvoice,
                    );
                }

                accountsPayableReceivableInvoice.createFinanceDocumentsReturn.documentsCreated.push({
                    type: accountsPayableReceivableInvoice.targetDocumentType,
                    documentNumber: await accountsPayableReceivableInvoiceToUpdate.number,
                    documentSysId: accountsPayableReceivableInvoiceToUpdate._id,
                });

                if (!accountsPayableReceivableInvoiceToUpdate.pHasReplyBeenSentFromExternalIntegration) {
                    await xtremFinance.functions.replyToOriginalDocument(
                        classContext,
                        accountsPayableReceivableInvoice.financeIntegrationRecord,
                        'recorded',
                        accountsPayableReceivableInvoice.createFinanceDocumentsReturn,
                        accountsPayableReceivableInvoice.replyTopic,
                        '',
                    );
                }
            } catch (error) {
                accountsPayableReceivableInvoice.createFinanceDocumentsReturn.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: error,
                });
            }
        }

        return accountsPayableReceivableInvoice.createFinanceDocumentsReturn;
    }
}
