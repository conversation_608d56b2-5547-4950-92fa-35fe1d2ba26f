import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import { JournalEntry } from '../nodes/journal-entry';
import { JournalEntryInquiry } from '../nodes/journal-entry-inquiry';

export const journalEntryInquiry = new Activity({
    description: 'Journal entry inquiry',
    node: () => JournalEntryInquiry,
    __filename,
    permissions: ['read'],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [
                    () => JournalEntryInquiry,
                    () => xtremFinanceData.nodes.Journal,
                    () => xtremFinanceData.nodes.Account,
                    () => xtremSystem.nodes.Company,
                    () => xtremSystem.nodes.Site,
                    () => xtremStructure.nodes.Legislation,
                    () => xtremMasterData.nodes.Item,
                    () => xtremMasterData.nodes.Customer,
                    () => xtremMasterData.nodes.Supplier,
                    () => xtremMasterData.nodes.Currency,
                    () => xtremTax.nodes.Tax,
                    () => JournalEntry,
                ],
            },
            {
                operations: ['singleRecord'],
                on: [() => JournalEntryInquiry],
            },
            ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
        ],
    },
});
