import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import { AccountsReceivableInvoice } from '../nodes/accounts-receivable-invoice';
import { JournalEntry } from '../nodes/journal-entry';

export const accountsReceivableInvoice = new Activity({
    description: 'Accounts receivable Invoice',
    node: () => AccountsReceivableInvoice,
    __filename,
    permissions: ['read', 'post'],
    operationGrants: {
        read: [
            {
                operations: ['areFinanceIntegrationPackagesActive'],
                on: [() => JournalEntry],
            },
            {
                operations: ['lookup'],
                on: [
                    () => xtremSystem.nodes.Company,
                    () => xtremSystem.nodes.Site,
                    () => xtremStructure.nodes.Legislation,
                ],
            },
            ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
        ],
        post: [
            ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
            {
                operations: ['resendNotificationForFinance'],
                on: [() => AccountsReceivableInvoice],
            },
        ],
    },
});
