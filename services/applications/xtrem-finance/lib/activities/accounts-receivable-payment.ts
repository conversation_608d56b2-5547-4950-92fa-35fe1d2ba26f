import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { AccountsReceivablePayment } from '../nodes/accounts-receivable-payment';

export const accountsReceivablePayment = new Activity({
    description: 'Accounts receivable payment',
    node: () => AccountsReceivablePayment,
    __filename,
    permissions: ['read', 'post'],
    operationGrants: {
        read: [...xtremFinanceData.functions.dimensionsAndAttributesOperations],
    },
});
