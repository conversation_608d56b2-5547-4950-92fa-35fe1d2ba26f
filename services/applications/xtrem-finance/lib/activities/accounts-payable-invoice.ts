import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import { AccountsPayableInvoice } from '../nodes/accounts-payable-invoice';
import { JournalEntry } from '../nodes/journal-entry';

export const accountsPayableInvoice = new Activity({
    description: 'Accounts payable invoice',
    node: () => AccountsPayableInvoice,
    __filename,
    permissions: ['read', 'post'],
    operationGrants: {
        read: [
            {
                operations: ['areFinanceIntegrationPackagesActive'],
                on: [() => JournalEntry],
            },
            {
                operations: ['lookup'],
                on: [
                    () => xtremSystem.nodes.Company,
                    () => xtremSystem.nodes.Site,
                    () => xtremStructure.nodes.Legislation,
                ],
            },
            ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
        ],
        post: [
            {
                operations: ['resendNotificationForFinance'],
                on: [() => AccountsPayableInvoice],
            },
        ],
    },
});
