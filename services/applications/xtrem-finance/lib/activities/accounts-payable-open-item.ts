import { Activity } from '@sage/xtrem-core';
import { AccountsOpenItem } from '../nodes/accounts-open-item';
import { AccountsPayableOpenItem } from '../nodes/accounts-payable-open-item';
import { InitializeOpenItem } from '../nodes/initialize-open-item';

export const accountsPayableOpenItem = new Activity({
    description: 'Accounts payable open item',
    node: () => AccountsPayableOpenItem,
    __filename,
    permissions: ['read', 'initializePaidAmount'],
    operationGrants: {
        read: [{ operations: ['create'], on: [() => AccountsOpenItem] }],
        initializePaidAmount: [
            { operations: ['create'], on: [() => InitializeOpenItem] },
            { operations: ['update', 'bulkOpenItemUpdate'], on: [() => AccountsPayableOpenItem] },
        ],
    },
});
