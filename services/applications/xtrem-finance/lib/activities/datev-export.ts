import type { OperationGrant } from '@sage/xtrem-core';
import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremFinance from '../index';

const readGrants: OperationGrant[] = [
    { operations: ['read', 'areFinanceIntegrationPackagesActive'], on: [() => xtremFinance.nodes.JournalEntry] },
    { operations: ['read'], on: [() => xtremFinanceData.nodes.Account] },
    { operations: ['read'], on: [() => xtremFinanceData.nodes.DatevConfiguration] },
    { operations: ['read'], on: [() => xtremFinanceData.nodes.FinanceTransaction] },
    { operations: ['read'], on: [() => xtremMasterData.nodes.BusinessEntity] },
    { operations: ['lookup'], on: [() => xtremStructure.nodes.Legislation] },
];

export const datevExport = new Activity({
    description: 'DATEV export',
    node: () => xtremFinance.nodes.DatevExport,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [...readGrants],
        manage: [
            {
                operations: ['create', 'update', 'delete', 'datevExtraction', 'datevExport'],
                on: [() => xtremFinance.nodes.DatevExport],
            },
            ...readGrants,
        ],
    },
});
