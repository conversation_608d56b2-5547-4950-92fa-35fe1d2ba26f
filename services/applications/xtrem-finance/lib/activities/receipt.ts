import type { OperationGrant } from '@sage/xtrem-core';
import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinance from '../index';

const readGrants: OperationGrant[] = [
    { operations: ['lookup'], on: [() => xtremFinance.nodes.AccountsReceivableOpenItem] },
    { operations: ['lookup'], on: [() => xtremFinanceData.nodes.BankAccount] },
    { operations: ['lookup'], on: [() => xtremFinanceData.nodes.BasePaymentDocument] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.BaseDocumentLine] },
    { operations: ['lookup'], on: [() => xtremSystem.nodes.Site] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Supplier] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Customer] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Currency] },
];

export const receipt = new Activity({
    description: 'Receipt',
    node: () => xtremFinance.nodes.Receipt,
    __filename,
    permissions: ['read', 'manage', 'void'],
    operationGrants: {
        read: [...readGrants],
        manage: [
            {
                operations: ['create'],
                on: [() => xtremFinance.nodes.Receipt],
            },
            ...readGrants,
        ],
        void: [
            {
                operations: ['voidPayment'],
                on: [() => xtremFinance.nodes.Receipt],
            },
            ...readGrants,
        ],
    },
});
