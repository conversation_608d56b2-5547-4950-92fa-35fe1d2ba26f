import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremSystem from '@sage/xtrem-system';
import { AccountingInterfaceListener } from '../nodes/accounting-interface-listener';

export const generateJournalEntries = new Activity({
    description: 'Generate journal entries',
    node: () => AccountingInterfaceListener,
    __filename,
    permissions: ['accountingIntegration'],
    operationGrants: {
        accountingIntegration: [
            {
                operations: ['createJournalsFromAccountingStaging', 'createJournalsFromAccountingStagingJob'],
                on: [() => AccountingInterfaceListener],
                ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
            },
            { operations: ['lookup'], on: [() => xtremSystem.nodes.Company] },
            { operations: ['lookup'], on: [() => xtremSystem.nodes.Site] },
        ],
    },
});
