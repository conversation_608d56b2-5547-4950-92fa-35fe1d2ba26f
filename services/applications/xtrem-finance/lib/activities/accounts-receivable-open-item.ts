import { Activity } from '@sage/xtrem-core';
import { AccountsOpenItem } from '../nodes/accounts-open-item';
import { AccountsReceivableOpenItem } from '../nodes/accounts-receivable-open-item';
import { InitializeOpenItem } from '../nodes/initialize-open-item';

export const accountsReceivableOpenItem = new Activity({
    description: 'Accounts receivable open item',
    node: () => AccountsReceivableOpenItem,
    __filename,
    permissions: ['read', 'initializePaidAmount'],
    operationGrants: {
        read: [{ operations: ['delete'], on: [() => AccountsOpenItem] }],
        initializePaidAmount: [
            { operations: ['update'], on: [() => InitializeOpenItem] },
            { operations: ['update', 'bulkOpenItemUpdate'], on: [() => AccountsReceivableOpenItem] },
        ],
    },
});
