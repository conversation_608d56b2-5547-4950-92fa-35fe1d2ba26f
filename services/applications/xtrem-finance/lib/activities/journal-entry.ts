import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import { JournalEntry } from '../nodes/journal-entry';
import { JournalEntryInquiry } from '../nodes/journal-entry-inquiry';

export const journalEntry = new Activity({
    description: 'Journal entry',
    node: () => JournalEntry,
    __filename,
    permissions: ['read', 'manage', 'post'],
    operationGrants: {
        read: [
            {
                operations: ['lookup', 'singleRecord'],
                on: [() => JournalEntryInquiry],
            },

            {
                operations: ['lookup'],
                on: [() => xtremStructure.nodes.Legislation, () => xtremSystem.nodes.Site],
            },
            {
                operations: ['areFinanceIntegrationPackagesActive'],
                on: [() => JournalEntry],
            },
            ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
        ],
        manage: [
            {
                operations: ['update', 'post', 'areFinanceIntegrationPackagesActive'],
                on: [() => JournalEntry],
            },
            ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
        ],
    },
});
