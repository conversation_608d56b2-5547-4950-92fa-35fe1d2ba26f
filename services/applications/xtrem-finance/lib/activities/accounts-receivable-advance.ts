import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import { JournalEntry } from '../nodes/journal-entry';
import { AccountsReceivableAdvance } from '../nodes/accounts-receivable-advance';

export const accountsReceivableAdvance = new Activity({
    description: 'Accounts receivable advance',
    node: () => AccountsReceivableAdvance,
    __filename,
    permissions: ['read', 'post'],
    operationGrants: {
        read: [
            {
                operations: ['areFinanceIntegrationPackagesActive'],
                on: [() => JournalEntry],
            },
            {
                operations: ['lookup'],
                on: [
                    () => xtremSystem.nodes.Company,
                    () => xtremSystem.nodes.Site,
                    () => xtremStructure.nodes.Legislation,
                ],
            },
            ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
        ],
    },
});
