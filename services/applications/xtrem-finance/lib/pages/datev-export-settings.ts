import { date } from '@sage/xtrem-date-time';
import type { DatevExport as DatevExportNode, GraphApi } from '@sage/xtrem-finance-api';
import type { AttributeType, DimensionType } from '@sage/xtrem-finance-data-api';
import type { Company } from '@sage/xtrem-system-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import type { DimensionTypeForDropDown } from '../client-functions/datev';
import {
    getAttributeType,
    getDimensionType,
    initDatevExportSettings,
    validateDates,
    validateDimensionTypes,
    validateFiscalYearStart,
} from '../client-functions/datev';

@ui.decorators.page<DatevExportSettings, DatevExportNode>({
    title: 'DATEV export settings',
    module: 'finance',
    mode: 'default',
    access: { node: '@sage/xtrem-finance/DatevExport' },
    node: '@sage/xtrem-finance/DatevExport',
    navigationPanel: undefined,
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
        await initDatevExportSettings(this);
    },
})
export class DatevExportSettings extends ui.Page<GraphApi, DatevExportNode> {
    attributeAndDimensionTypes: DimensionTypeForDropDown[];

    getSerializedValues() {
        const { values } = this.$;

        /** Temporary hack to have date range on ui waiting for https://jira.sage.com/browse/XT-50736 */
        values.dateRange = `[${this.startDate.value ?? ''},${this.endDate.value ?? ''}]`;

        return values;
    }

    @ui.decorators.section<DatevExportSettings>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<DatevExportSettings>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
        isTitleHidden: true,
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.textField<DatevExportSettings>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'ID',
        width: 'small',
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.linkField<DatevExportSettings>({
        isTransient: true,
        parent() {
            return this.criteriaBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-finance/pages__datev_export_parameters__datev_configuration',
                'DATEV configuration',
            );
        },
        async onClick() {
            if (this.$.isServiceOptionEnabled('datevOption')) {
                await this.$.dialog.page(
                    '@sage/xtrem-finance-data/DatevConfiguration',
                    { _id: '#DATEV' },
                    {
                        resolveOnCancel: true,
                    },
                );
            }
        },
    })
    datevConfigurationLink: ui.fields.Link;

    @ui.decorators.separatorField<DatevExportSettings>({
        parent() {
            return this.criteriaBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    configurationSeparator: ui.fields.Separator;

    @ui.decorators.referenceField<DatevExportSettings>({
        parent() {
            return this.criteriaBlock;
        },
        minLookupCharacters: 0,
        valueField: 'name',
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
        ],
        width: 'small',
    })
    company: ui.fields.Reference<Company>;

    @ui.decorators.checkboxField<DatevExportSettings>({
        title: 'Locked',
        parent() {
            return this.criteriaBlock;
        },
    })
    isLocked: ui.fields.Checkbox;

    @ui.decorators.separatorField<DatevExportSettings>({
        parent() {
            return this.criteriaBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    companySeparator: ui.fields.Separator;

    @ui.decorators.dateField<DatevExportSettings>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Start date',
        width: 'small',
        isMandatory: true,
        validation() {
            return validateDates({
                pageInstance: this,
                startDate: this.startDate.value ? this.startDate.value : undefined,
                endDate: this.endDate.value ? this.endDate.value : undefined,
                fiscalYearStart: this.fiscalYearStart.value ? this.fiscalYearStart.value : undefined,
            });
        },
        onChange() {
            if (this.startDate.value && !this.fiscalYearStart.value) {
                this.fiscalYearStart.value = date.parse(this.startDate.value).begOfYear().toString();
            }
        },
    })
    startDate: ui.fields.Date;

    @ui.decorators.dateField<DatevExportSettings>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'End date',
        width: 'small',
        isMandatory: true,
        validation() {
            return validateDates({
                pageInstance: this,
                startDate: this.startDate.value ? this.startDate.value : undefined,
                endDate: this.endDate.value ? this.endDate.value : undefined,
                fiscalYearStart: this.fiscalYearStart.value ? this.fiscalYearStart.value : undefined,
            });
        },
    })
    endDate: ui.fields.Date;

    @ui.decorators.textField<DatevExportSettings>({ title: 'Date range' }) dateRange: ui.fields.Text;

    @ui.decorators.dateField<DatevExportSettings>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Fiscal year start',
        width: 'small',
        isMandatory: true,
        validation() {
            return validateFiscalYearStart({
                pageInstance: this,
                startDate: this.startDate.value ? this.startDate.value : undefined,
                endDate: this.endDate.value ? this.endDate.value : undefined,
                fiscalYearStart: this.fiscalYearStart.value ? this.fiscalYearStart.value : undefined,
            });
        },
    })
    fiscalYearStart: ui.fields.Date;

    @ui.decorators.separatorField<DatevExportSettings>({
        parent() {
            return this.criteriaBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    dateSeparator: ui.fields.Separator;

    @ui.decorators.dropdownListField<DatevExportSettings>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Dimension type 1',
        isTransient: true,
        hasEmptyValue: true,
        validation() {
            return validateDimensionTypes(this);
        },
        onChange() {
            if (this.dimensionTypeOrAttribute1.value && this.dimensionTypeOrAttribute1.value !== ' ') {
                this.dimensionTypeOrAttribute2.isDisabled = false;
                const attributeType = getAttributeType(
                    this.attributeAndDimensionTypes,
                    this.dimensionTypeOrAttribute1.value,
                );
                if (attributeType) {
                    this.attributeType1.value = { ...attributeType };
                } else {
                    this.dimensionType1.value = {
                        ...getDimensionType(this.attributeAndDimensionTypes, this.dimensionTypeOrAttribute1.value),
                    };
                }
            } else {
                this.dimensionTypeOrAttribute2.isDisabled = true;
                this.dimensionTypeOrAttribute2.value = null;
                this.dimensionType1.value = null;
                this.attributeType1.value = null;
                this.dimensionType2.value = null;
                this.attributeType2.value = null;
            }
        },
    })
    dimensionTypeOrAttribute1: ui.fields.DropdownList;

    @ui.decorators.referenceField<DatevExportSettings, DimensionType>({
        parent() {
            return this.criteriaBlock;
        },
        isHidden: true,
    })
    dimensionType1: ui.fields.Reference<DimensionType>;

    @ui.decorators.referenceField<DatevExportSettings, AttributeType>({
        parent() {
            return this.criteriaBlock;
        },
        isHidden: true,
    })
    attributeType1: ui.fields.Reference<AttributeType>;

    @ui.decorators.dropdownListField<DatevExportSettings>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Dimension type 2',
        isTransient: true,
        hasEmptyValue: true,
        isDisabled() {
            return !this.dimensionTypeOrAttribute1.value;
        },
        validation() {
            return validateDimensionTypes(this);
        },
        onChange() {
            if (this.dimensionTypeOrAttribute2.value && this.dimensionTypeOrAttribute2.value !== ' ') {
                const attributeType = getAttributeType(
                    this.attributeAndDimensionTypes,
                    this.dimensionTypeOrAttribute2.value,
                );
                if (attributeType) {
                    this.attributeType2.value = { ...attributeType };
                } else {
                    this.dimensionType2.value = {
                        ...getDimensionType(this.attributeAndDimensionTypes, this.dimensionTypeOrAttribute2.value),
                    };
                }
            } else {
                this.attributeType2.value = null;
                this.dimensionType2.value = null;
            }
        },
    })
    dimensionTypeOrAttribute2: ui.fields.DropdownList;

    @ui.decorators.referenceField<DatevExportSettings, DimensionType>({
        parent() {
            return this.criteriaBlock;
        },
        isHidden: true,
    })
    dimensionType2: ui.fields.Reference<DimensionType>;

    @ui.decorators.referenceField<DatevExportSettings, AttributeType>({
        parent() {
            return this.criteriaBlock;
        },
        isHidden: true,
    })
    attributeType2: ui.fields.Reference<AttributeType>;

    @ui.decorators.separatorField<DatevExportSettings>({
        parent() {
            return this.criteriaBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    dimensionSeparator: ui.fields.Separator;

    @ui.decorators.checkboxField<DatevExportSettings>({
        title: 'Export journal entries',
        parent() {
            return this.criteriaBlock;
        },
    })
    doJournalEntries: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DatevExportSettings>({
        title: 'Export accounts',
        parent() {
            return this.criteriaBlock;
        },
    })
    doAccounts: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DatevExportSettings>({
        title: 'Export customers and suppliers',
        parent() {
            return this.criteriaBlock;
        },
    })
    doCustomersSuppliers: ui.fields.Checkbox;
}
