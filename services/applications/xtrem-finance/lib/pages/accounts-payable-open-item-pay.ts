import type { AccountsPayableOpenItem as AccountsPayableOpenItemNode, GraphApi } from '@sage/xtrem-finance-api';
import type { CloseReason } from '@sage/xtrem-finance-data-api';
import { paymentTracking } from '@sage/xtrem-finance-data/build/lib/menu-items/payment-tracking';
import type { Currency } from '@sage/xtrem-master-data-api';
import * as Pill<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { openDocumentLinkPage } from '../client-functions/common';
import { onChangeForcedAmountPaid, onLoadOpenItemPay, validateForcedAmountPaid } from '../client-functions/open-item';

@ui.decorators.page<AccountsPayableOpenItemPay, AccountsPayableOpenItemNode>({
    title: 'Initialize accounts payable payment',
    objectTypeSingular: 'Initialize accounts payable payment',
    objectTypePlural: 'Initialize accounts payable payments',
    idField() {
        return this.documentNumber;
    },
    headerLabel() {
        return this.status;
    },
    menuItem: paymentTracking,
    priority: 120,
    node: '@sage/xtrem-finance/AccountsPayableOpenItem',
    module: 'finance',
    mode: 'tabs',
    navigationPanel: {
        orderBy: { dueDate: -1 },
        bulkActions: [
            {
                mutation: 'bulkOpenItemUpdate',
                title: 'Close open items',
                icon: 'document_tick',
                buttonType: 'primary',
            },
        ],
        listItem: {
            title: ui.nestedFields.link({
                title: 'Document number',
                bind: { documentNumber: true },
                width: 'medium',
                async onClick(_id, rowItem) {
                    await openDocumentLinkPage({
                        page: this,
                        documentSysId: rowItem.documentSysId ?? 0,
                        documentType: rowItem.documentType,
                        fromFinance: true,
                    });
                },
            }),
            documentType: ui.nestedFields.dropdownList({
                bind: { documentType: true },
                title: 'Document type',
                optionType: '@sage/xtrem-finance-data/FinanceDocumentType',
            }),
            documentSysId: ui.nestedFields.technical({ bind: { documentSysId: true } }),
            line2: ui.nestedFields.reference({
                bind: { businessRelation: true },
                node: '@sage/xtrem-master-data/BaseBusinessRelation',
                title: 'Supplier name',
                valueField: 'name',
            }),
            businessEntityId: ui.nestedFields.text({
                bind: { businessRelation: { id: true } },
                title: 'Supplier ID',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({ bind: { dueDate: true }, title: 'Due date' }),
            financialSite: ui.nestedFields.reference({
                bind: { financialSite: true },
                node: '@sage/xtrem-system/Site',
                title: 'Financial site',
                valueField: 'name',
            }),
            transactionAmountDueSigned: ui.nestedFields.numeric({
                bind: { transactionAmountDueSigned: true },
                title: 'Transaction amount due',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            transactionAmountPaid: ui.nestedFields.numeric({
                bind: { transactionAmountPaidSigned: true },
                title: 'Transaction amount paid',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            remainingTransactionAmount: ui.nestedFields.numeric({
                bind: { remainingTransactionAmountSigned: true },
                title: 'Remaining transaction amount',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            currency: ui.nestedFields.reference({
                bind: { currency: true },
                node: '@sage/xtrem-master-data/Currency',
                title: 'Currency',
                valueField: 'name',
                isHiddenOnMainField: true,
            }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-finance-data/OpenItemStatus',
                style: (_id, rowValue) => PillColorCommon.getDisplayStatusPillFeatures(rowValue?.status),
            }),
            companyAmountDueSigned: ui.nestedFields.numeric({
                bind: { companyAmountDueSigned: true },
                title: 'Company amount due',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.financialSite?.legalCompany?.currency,
            }),
            companyAmountPaid: ui.nestedFields.numeric({
                bind: { companyAmountPaidSigned: true },
                title: 'Company amount paid',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.financialSite?.legalCompany?.currency,
                isHiddenOnMainField: true,
            }),
            remainingCompanyAmount: ui.nestedFields.numeric({
                bind: { remainingCompanyAmountSigned: true },
                title: 'Remaining company amount',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.financialSite?.legalCompany?.currency,
                isHiddenOnMainField: true,
            }),
            companyCurrency: ui.nestedFields.reference({
                bind: { financialSite: { legalCompany: { currency: true } } },
                node: '@sage/xtrem-master-data/Currency',
                title: 'Company currency',
                valueField: 'name',
                isHiddenOnMainField: true,
            }),
            displayDiscountPaymentDate: ui.nestedFields.date({
                bind: { displayDiscountPaymentDate: true },
                title: 'Discount payment before date',
            }),
            closeReason: ui.nestedFields.text({
                bind: { closeReason: { id: true } },
                title: 'Close reason',
                isHiddenOnMainField: true,
            }),
            closeText: ui.nestedFields.text({
                bind: { closeText: true },
                title: 'Close text',
                isHiddenOnMainField: true,
            }),
            forcedAmountPaid: ui.nestedFields.numeric({
                bind: { forcedAmountPaidSigned: true },
                title: 'Forced amount paid',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.currency,
                isHiddenOnMainField: true,
            }),
        },
        optionsMenu: [
            {
                title: 'Not fully paid',
                graphQLFilter: { status: { _ne: 'paid' }, accountsPayableInvoice: { postingStatus: 'posted' } },
            },
            { title: 'All statuses', graphQLFilter: { accountsPayableInvoice: { postingStatus: 'posted' } } },
            {
                title: 'Not paid',
                graphQLFilter: { status: { _eq: 'notPaid' }, accountsPayableInvoice: { postingStatus: 'posted' } },
            },
            {
                title: 'Partially paid',
                graphQLFilter: {
                    status: { _eq: 'partiallyPaid' },
                    accountsPayableInvoice: { postingStatus: 'posted' },
                },
            },
            {
                title: 'Paid',
                graphQLFilter: { status: { _eq: 'paid' }, accountsPayableInvoice: { postingStatus: 'posted' } },
            },
        ],
    },
    access: { node: '@sage/xtrem-finance/InitializeOpenItem', bind: '$create' },
    businessActions() {
        return [this.$standardCancelAction, this.save];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            dropDownBusinessActions: [this.$standardOpenCustomizationPageWizardAction],
        });
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.save,
            cancel: this.$standardCancelAction,
        });
        onLoadOpenItemPay(this);
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
        });
    },
})
export class AccountsPayableOpenItemPay extends ui.Page<GraphApi, AccountsPayableOpenItemNode> {
    @ui.decorators.pageAction<AccountsPayableOpenItemPay>({
        title: 'Save',
        access: { bind: '$update' },
        buttonType: 'primary',
        async onClick() {
            await this.$standardSaveAction.execute(true);
            await this.$.router.refresh();
        },
    })
    save: ui.PageAction;

    @ui.decorators.section<AccountsPayableOpenItemPay>({ title: 'General', isTitleHidden: true })
    generalSection: ui.containers.Section;

    @ui.decorators.textField<AccountsPayableOpenItemPay>({ isHidden: true })
    documentNumber: ui.fields.Text;

    @ui.decorators.dropdownListField<AccountsPayableOpenItemPay>({
        optionType: '@sage/xtrem-finance-data/FinanceDocumentType',
    })
    documentType: ui.fields.DropdownList;

    @ui.decorators.numericField<AccountsPayableOpenItemPay>({ isHidden: true })
    remainingTransactionAmountSigned: ui.fields.Numeric;

    @ui.decorators.numericField<AccountsPayableOpenItemPay>({ isHidden: true, isTransient: true })
    remainingTransactionAmount: ui.fields.Numeric;

    @ui.decorators.numericField<AccountsPayableOpenItemPay>({ isHidden: true, isTransient: true })
    originalForcedAmountPaid: ui.fields.Numeric;

    @ui.decorators.numericField<AccountsPayableOpenItemPay>({ isHidden: true })
    transactionAmountDue: ui.fields.Numeric;

    @ui.decorators.labelField<AccountsPayableOpenItemPay>({
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.status.value);
        },
    })
    status: ui.fields.Label;

    @ui.decorators.referenceField<AccountsPayableOpenItemPay, Currency>({ isHidden: true })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.block<AccountsPayableOpenItemPay>({
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.fragmentFields<AccountsPayableOpenItemPay>({
        parent() {
            return this.generalBlock;
        },
        fragment: '@sage/xtrem-finance/OpenItemGeneral',
    })
    generalFields: ui.containers.FragmentFields;

    @ui.decorators.block<AccountsPayableOpenItemPay>({
        parent() {
            return this.generalSection;
        },
    })
    closeBlock: ui.containers.Block;

    @ui.decorators.numericField<AccountsPayableOpenItemPay>({})
    forcedAmountPaid: ui.fields.Numeric;

    @ui.decorators.numericField<AccountsPayableOpenItemPay>({
        title: 'Forced amount paid',
        width: 'medium',
        parent() {
            return this.closeBlock;
        },
        unit() {
            return this.currency?.value;
        },
        validation(value: number | null) {
            return validateForcedAmountPaid({
                pageInstance: this,
                forcedAmountPaid: value,
                originalForcedAmountPaid: this.originalForcedAmountPaid.value,
                remainingAmount: this.remainingTransactionAmount.value,
            });
        },
        onChange() {
            onChangeForcedAmountPaid(this);
        },
    })
    forcedAmountPaidSigned: ui.fields.Numeric;

    @ui.decorators.referenceField<AccountsPayableOpenItemPay, CloseReason>({
        title: 'Close reason',
        width: 'medium',
        minLookupCharacters: 0,
        parent() {
            return this.closeBlock;
        },
    })
    closeReason: ui.fields.Reference<CloseReason>;

    @ui.decorators.textField<AccountsPayableOpenItemPay>({
        title: 'Close text',
        parent() {
            return this.closeBlock;
        },
    })
    closeText: ui.fields.Text;

    @ui.decorators.numericField<AccountsPayableOpenItemPay>({
        title: 'Total amount paid',
        parent() {
            return this.closeBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isDisabled: true,
    })
    transactionAmountPaidSigned: ui.fields.Numeric;

    @ui.decorators.fragmentFields<AccountsPayableOpenItemPay>({
        parent() {
            return this.closeBlock;
        },
        fragment: '@sage/xtrem-finance/AccountsPayableOpenItemPayment',
    })
    paymentTable: ui.containers.FragmentFields;
}
