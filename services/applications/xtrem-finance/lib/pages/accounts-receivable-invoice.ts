import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type {
    AccountsReceivableInvoiceLine,
    AccountsReceivableInvoiceLineDimension,
    AccountsReceivableInvoice as AccountsReceivableInvoiceNode,
    AccountsReceivableInvoiceTax,
    GraphApi,
} from '@sage/xtrem-finance-api';
import type {
    Account,
    Attribute,
    Dimension,
    FinanceTransactionBinding,
    OpenItemStatus,
} from '@sage/xtrem-finance-data-api';
import type {
    ActiveAttributeType,
    ActiveDimensionType,
} from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import {
    attributeIDs,
    dimensionDocProperties,
    getAttributeTypeName,
    getDimensionTypeName,
} from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type { Cur<PERSON>cy, Customer, Item, PaymentTerm, Supplier } from '@sage/xtrem-master-data-api';
import * as Pill<PERSON><PERSON><PERSON><PERSON><PERSON>mon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import { finance } from '@sage/xtrem-master-data/build/lib/menu-items/finance';
import type { Legislation } from '@sage/xtrem-structure-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import { DocumentTypeEnum } from '@sage/xtrem-tax/build/lib/client-functions/interfaces';
import * as ui from '@sage/xtrem-ui';
import {
    isAttributeHidden,
    isDimensionHidden,
    onLoadAPARInvoice,
    setApplicativePageCrudActionsAPARInvoice,
    taxDetailsAPAR,
} from '../client-functions/common';

@ui.decorators.page<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
    title: 'Accounts receivable invoice',
    objectTypeSingular: 'Accounts receivable invoice',
    objectTypePlural: 'Accounts receivable invoices',
    idField() {
        return this.number;
    },
    menuItem: finance,
    priority: 200,
    node: '@sage/xtrem-finance/AccountsReceivableInvoice',
    module: 'finance',
    mode: 'tabs',
    navigationPanel: {
        orderBy: { invoiceDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.text<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
                bind: 'number',
                title: 'Number',
            }),
            line_4: ui.nestedFields.dropdownList<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
                bind: 'type',
                title: 'Type',
                optionType: '@sage/xtrem-finance-data/FinanceDocumentType',
                width: 'small',
            }),
            line_5: ui.nestedFields.date<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
                bind: 'invoiceDate',
                title: 'Invoice date',
            }),
            line6: ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
                bind: 'financialSite',
                node: '@sage/xtrem-system/Site',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Financial site',
            }),
            line7: ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceNode, Customer>({
                bind: 'billToCustomer',
                title: 'Bill-to customer',
                node: '@sage/xtrem-master-data/Customer',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
            }),
            billToCustomerId: ui.nestedFields.text({
                bind: { billToCustomer: { id: true } },
                title: 'Bill-to customer ID',
                isHiddenOnMainField: true,
            }),
            line2: ui.nestedFields.text<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
                bind: 'description',
                title: 'Description',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
                bind: 'postingDate',
                title: 'Posting date',
            }),
            line8: ui.nestedFields.numeric<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
                bind: 'totalAmountIncludingTax',
                title: 'Total including tax',
            }),
            titleRight: ui.nestedFields.label<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
                bind: 'postingStatus',
                title: 'Posting status',
                optionType: '@sage/xtrem-finance-data/JournalStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            line9: ui.nestedFields.text<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
                bind: 'reference',
                title: 'Reference',
                isHiddenOnMainField: true,
            }),
            line10: ui.nestedFields.dropdownList<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
                bind: 'origin',
                title: 'Origin',
                optionType: '@sage/xtrem-finance-data/AccountsPayableReceivableInvoiceOrigin',
                isHiddenOnMainField: true,
            }),
            line11: ui.nestedFields.numeric<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
                bind: 'totalAmountExcludingTax',
                title: 'Total excluding tax',
                isHiddenOnMainField: true,
            }),
            line12: ui.nestedFields.numeric<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
                bind: 'totalTaxAmountAdjusted',
                title: 'Adjusted total vat',
                isHiddenOnMainField: true,
            }),
            line13: ui.nestedFields.date<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
                bind: 'dueDate',
                title: 'Due date',
                isHiddenOnMainField: true,
            }),
            line14: ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceNode>({
                bind: 'paymentTerm',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
        },
        optionsMenu: [
            { title: 'All', graphQLFilter: {} },
            { title: 'Draft', graphQLFilter: { postingStatus: { _eq: 'draft' } } },
            { title: 'Posted', graphQLFilter: { postingStatus: { _eq: 'posted' } } },
            { title: 'In progress', graphQLFilter: { postingStatus: { _eq: 'inProgress' } } },
            { title: 'Error', graphQLFilter: { postingStatus: { _eq: 'error' } } },
        ],
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    async onLoad() {
        await onLoadAPARInvoice(this);
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActionsAPARInvoice(isDirty, this);
    },
})
export class AccountsReceivableInvoice extends ui.Page<GraphApi> {
    activeDimensionTypes: ActiveDimensionType[];

    activeAttributeTypes: ActiveAttributeType[];

    fromNotificationHistory: boolean;

    @ui.decorators.section<AccountsReceivableInvoice>({ title: 'General' }) generalSection: ui.containers.Section;

    @ui.decorators.block<AccountsReceivableInvoice>({
        parent() {
            return this.generalSection;
        },
        width: 'large',
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.textField<AccountsReceivableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Number',
        isReadOnly: true,
        width: 'small',
    })
    number: ui.fields.Text;

    @ui.decorators.dropdownListField<AccountsReceivableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Type',
        optionType: '@sage/xtrem-finance-data/FinanceDocumentType',
        isReadOnly: true,
        width: 'small',
    })
    type: ui.fields.DropdownList;

    @ui.decorators.referenceField<AccountsReceivableInvoice, Site>({
        parent() {
            return this.generalBlock;
        },
        title: 'Financial site',
        width: 'small',
        isReadOnly: true,
        columns: [
            ui.nestedFields.technical<AccountsReceivableInvoice, Site, Company>({
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<AccountsReceivableInvoice, Company, Legislation>({
                        bind: 'legislation',
                        node: '@sage/xtrem-structure/Legislation',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'taxEngine' }),
                ],
            }),
        ],
    })
    financialSite: ui.fields.Reference<Site>;

    @ui.decorators.dateField<AccountsReceivableInvoice>({
        title: 'Invoice date',
        parent() {
            return this.generalBlock;
        },
        width: 'small',
        isReadOnly: true,
    })
    invoiceDate: ui.fields.Date;

    @ui.decorators.separatorField<AccountsReceivableInvoice>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isHidden: true,
    })
    fieldSeparator01: ui.fields.Separator;

    @ui.decorators.textField<AccountsReceivableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Reference',
        isReadOnly: true,
    })
    reference: ui.fields.Text;

    @ui.decorators.dropdownListField<AccountsReceivableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Origin',
        optionType: '@sage/xtrem-finance-data/AccountsPayableReceivableInvoiceOrigin',
        isReadOnly: true,
    })
    origin: ui.fields.DropdownList;

    @ui.decorators.dateField<AccountsReceivableInvoice>({
        title: 'Posting date',
        parent() {
            return this.generalBlock;
        },
        width: 'small',
        isReadOnly: true,
    })
    postingDate: ui.fields.Date;

    @ui.decorators.labelField<AccountsReceivableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Posting status',
        optionType: '@sage/xtrem-finance-data/JournalStatus',
        style() {
            return PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', this.postingStatus.value);
        },
        width: 'small',
    })
    postingStatus: ui.fields.Label;

    // Starts hidden but it can be visible in some conditions
    @ui.decorators.labelField<AccountsReceivableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Accounting integration status',
        isHidden: true,
        width: 'large',
        optionType: '@sage/xtrem-finance-data/PostingStatus',
        style() {
            return PillColorFinance.getLabelColorByStatus(
                'FinanceDocumentPostingStatus',
                this.financeIntegrationStatus.value,
            );
        },
    })
    financeIntegrationStatus: ui.fields.Label;

    // Starts hidden but it can be visible in some conditions
    @ui.decorators.textField<AccountsReceivableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Accounting integration reference',
        isHidden: true,
        isReadOnly: true,
        width: 'large',
    })
    financeIntegrationAppRecordId: ui.fields.Text;

    // Starts hidden but it can be visible in some conditions
    @ui.decorators.linkField<AccountsReceivableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Accounting integration reference',
        width: 'large',
        isHidden: true,
        isTransient: true,
    })
    financeIntegrationAppRecordIdLink: ui.fields.Link;

    @ui.decorators.labelField<AccountsReceivableInvoice>({})
    financeIntegrationApp: ui.fields.Label;

    // Note: We need this hidden field to retrieve the URL from the node. It is assigned to the reference link field
    // in the onLoad() action.
    @ui.decorators.textField<AccountsReceivableInvoice>({})
    financeIntegrationAppUrl: ui.fields.Text;

    @ui.decorators.labelField<AccountsReceivableInvoice>({})
    internalFinanceIntegrationStatus: ui.fields.Label;

    @ui.decorators.checkboxField<AccountsReceivableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Printed',
        width: 'small',
        size: 'small',
        isReadOnly: true,
    })
    isPrinted: ui.fields.Checkbox;

    @ui.decorators.labelField<AccountsReceivableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Payment status',
        optionType: '@sage/xtrem-finance-data/OpenItemStatus',
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.paymentStatus.value);
        },
    })
    paymentStatus: ui.fields.Label<OpenItemStatus>;

    @ui.decorators.block<AccountsReceivableInvoice>({
        parent() {
            return this.generalSection;
        },
        width: 'small',
    })
    customerBlock: ui.containers.Block;

    @ui.decorators.referenceField<AccountsReceivableInvoice, Customer>({
        parent() {
            return this.customerBlock;
        },
        title: 'Bill-to customer',
        node: '@sage/xtrem-master-data/Customer',
        valueField: { businessEntity: { name: true } },
        width: 'medium',
        isReadOnly: true,
        helperTextField: { businessEntity: { id: true } },
    })
    billToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.separatorField<AccountsReceivableInvoice>({
        parent() {
            return this.customerBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    fieldSeparator03: ui.fields.Separator;

    @ui.decorators.dateField<AccountsReceivableInvoice>({
        title: 'Due date',
        parent() {
            return this.customerBlock;
        },
        width: 'small',
        isReadOnly: true,
    })
    dueDate: ui.fields.Date;

    @ui.decorators.referenceField<AccountsReceivableInvoice, PaymentTerm>({
        parent() {
            return this.customerBlock;
        },
        valueField: 'name',
        width: 'medium',
        isReadOnly: true,
    })
    paymentTerm: ui.fields.Reference<PaymentTerm>;

    @ui.decorators.separatorField<AccountsReceivableInvoice>({
        parent() {
            return this.customerBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    fieldSeparator04: ui.fields.Separator;

    @ui.decorators.referenceField<AccountsReceivableInvoice, Currency>({
        node: '@sage/xtrem-master-data/Currency',
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.textField<AccountsReceivableInvoice>({
        parent() {
            return this.customerBlock;
        },
        title: 'Rate',
        isReadOnly: true,
    })
    rateDescription: ui.fields.Text;

    @ui.decorators.nestedGridField<
        AccountsReceivableInvoice,
        [AccountsReceivableInvoiceLine, AccountsReceivableInvoiceLineDimension]
    >({
        parent() {
            return this.generalSection;
        },
        bind: 'lines',
        title: 'Lines',
        canSelect: false,
        levels: [
            {
                filter: { documentLineType: { _eq: 'documentLine' } },
                node: '@sage/xtrem-finance/AccountsReceivableInvoiceLine',
                childProperty: 'attributesAndDimensions',
                columns: [
                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLine, Account>({
                        node: '@sage/xtrem-finance-data/Account',
                        title: 'Account',
                        bind: 'account',
                        isReadOnly: true,
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'composedDescription' }),
                        ],
                    }),
                    ui.nestedFields.numeric<AccountsReceivableInvoice, AccountsReceivableInvoiceLine>({
                        title: 'Amount excluding tax',
                        bind: 'amountExcludingTax',
                        isReadOnly: true,
                        unit: (_id, rowData) => rowData?.currency,
                        scale: null,
                    }),
                    ui.nestedFields.numeric<AccountsReceivableInvoice, AccountsReceivableInvoiceLine>({
                        title: 'Tax amount',
                        bind: 'taxAmount',
                        isReadOnly: true,
                        unit: (_id, rowData) => rowData?.currency,
                        scale: null,
                    }),
                    ui.nestedFields.numeric<AccountsReceivableInvoice, AccountsReceivableInvoiceLine>({
                        title: 'Amount including tax',
                        isReadOnly: true,
                        bind: 'amountIncludingTax',
                        unit: (_id, rowData) => rowData?.currency,
                        scale: null,
                    }),
                    ui.nestedFields.dropdownList<AccountsReceivableInvoice, AccountsReceivableInvoiceLine>({
                        title: 'Line type',
                        width: 'small',
                        bind: 'lineType',
                        isReadOnly: true,
                        optionType: '@sage/xtrem-finance-data/AccountsPayableReceivableInvoiceLineType',
                    }),
                    ui.nestedFields.text<AccountsReceivableInvoice, AccountsReceivableInvoiceLine>({
                        title: 'Description',
                        bind: 'description',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLine, Site>({
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        title: 'Financial site',
                        bind: 'financialSite',
                        isReadOnly: true,
                        valueField: 'name',
                    }),
                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLine, Site>({
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        title: 'Provider site',
                        bind: 'providerSite',
                        isReadOnly: true,
                        valueField: 'name',
                    }),
                    ui.nestedFields.date<AccountsReceivableInvoice, AccountsReceivableInvoiceLine>({
                        title: 'Tax date',
                        bind: 'taxDate',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.technical<AccountsReceivableInvoice, AccountsReceivableInvoiceLine>({
                        bind: 'uiTaxes',
                    }),
                    ui.nestedFields.technical<AccountsReceivableInvoice, AccountsReceivableInvoiceLine, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'money_bag',
                        title: 'Tax details',
                        async onClick(_recordId: string, rowItem: AccountsReceivableInvoiceLine) {
                            await taxDetailsAPAR({
                                page: this,
                                documentType:
                                    this.type.value === 'salesInvoice'
                                        ? DocumentTypeEnum.salesInvoiceLine
                                        : DocumentTypeEnum.salesCreditMemoLine,
                                rowItem,
                            });
                        },
                    },
                ],
            },
            {
                node: '@sage/xtrem-finance/AccountsReceivableInvoiceLineDimension',
                columns: [
                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension, Site>({
                        bind: 'financialSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().financialSite);
                        },
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        isHidden() {
                            return isAttributeHidden(this, 'financialSite');
                        },
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),

                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension, Site>({
                        bind: 'businessSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().businessSite);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'businessSite');
                        },
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),

                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension, Site>({
                        bind: 'stockSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().stockSite);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'stockSite');
                        },
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),

                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension, Site>({
                        bind: 'manufacturingSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().manufacturingSite);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'manufacturingSite');
                        },
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Customer
                    >({
                        bind: 'customer',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().customer);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'customer');
                        },
                        node: '@sage/xtrem-master-data/Customer',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Supplier
                    >({
                        bind: 'supplier',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().supplier);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'supplier');
                        },
                        node: '@sage/xtrem-master-data/Supplier',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Attribute
                    >({
                        bind: 'employee',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().employee);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'employee');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Attribute
                    >({
                        bind: 'project',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().project);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'project');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Attribute
                    >({
                        bind: 'task',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().task);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'task');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension, Item>({
                        bind: 'item',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().item);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'item');
                        },
                        node: '@sage/xtrem-master-data/Item',
                        tunnelPage: '@sage/xtrem-master-data/Item',
                        isReadOnly: true,
                        valueField: 'composedDescription',
                    }),

                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension01',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty01,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType01');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension02',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty02,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType02');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),

                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension03',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty03,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType03');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),

                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension04',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty04,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType04');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension05',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty05,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType05');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension06',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty06,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType06');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension07',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty07,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType07');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension08',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty08,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType08');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension09',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty09,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType09');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension10',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty10,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType10');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension11',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty11,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType11');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension12',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty12,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType12');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension13',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty13,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType13');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension14',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty14,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType14');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension15',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty15,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType15');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension16',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty16,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType16');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension17',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty17,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType17');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension18',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty18,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType18');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension19',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty19,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType19');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension20',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty20,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType20');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension>({
                        title: 'Amount',
                        bind: 'amount',
                        isReadOnly: true,
                        unit() {
                            return this.currency?.value;
                        },
                        scale: null,
                    }),
                    ui.nestedFields.technical<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension>({
                        bind: 'storedAttributes',
                    }),
                    ui.nestedFields.technical<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension>({
                        bind: 'storedDimensions',
                    }),
                ],
            },
        ],
    })
    lines: ui.fields.NestedGrid<[AccountsReceivableInvoiceLine, AccountsReceivableInvoiceLineDimension]>;

    @ui.decorators.nestedGridField<
        AccountsReceivableInvoice,
        [AccountsReceivableInvoiceLine, AccountsReceivableInvoiceLineDimension]
    >({
        parent() {
            return this.generalSection;
        },
        bind: 'lines',
        title: 'Tax detail lines',
        canSelect: false,
        levels: [
            {
                node: '@sage/xtrem-finance/AccountsReceivableInvoiceLine',
                filter: { documentLineType: { _eq: 'taxLine' } },
                childProperty: 'attributesAndDimensions',
                columns: [
                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLine, Account>({
                        node: '@sage/xtrem-finance-data/Account',
                        title: 'Account',
                        bind: 'account',
                        isReadOnly: true,
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'composedDescription' }),
                        ],
                    }),
                    ui.nestedFields.numeric<AccountsReceivableInvoice, AccountsReceivableInvoiceLine>({
                        title: 'Tax amount',
                        bind: 'taxLineTaxAmount',
                        isReadOnly: true,
                        unit: (_id, rowData) => rowData?.currency,
                    }),
                    ui.nestedFields.text<AccountsReceivableInvoice, AccountsReceivableInvoiceLine>({
                        title: 'Tax detail',
                        bind: 'taxDetail',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLine, Site>({
                        title: 'Financial site',
                        bind: 'financialSite',
                        isReadOnly: true,
                        valueField: 'name',
                    }),
                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLine, Site>({
                        title: 'Provider site',
                        bind: 'providerSite',
                        isReadOnly: true,
                        valueField: 'name',
                    }),
                    ui.nestedFields.technical<AccountsReceivableInvoice, AccountsReceivableInvoiceLine, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            },
            {
                node: '@sage/xtrem-finance/AccountsReceivableInvoiceLineDimension',
                columns: [
                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension, Site>({
                        bind: 'financialSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().financialSite);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(
                                attributeType => attributeType.id === 'financialSite',
                            );
                        },
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),

                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension, Site>({
                        bind: 'businessSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().businessSite);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(
                                attributeType => attributeType.id === 'businessSite',
                            );
                        },
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),

                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension, Site>({
                        bind: 'stockSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().stockSite);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'stockSite');
                        },
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),

                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension, Site>({
                        bind: 'manufacturingSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().manufacturingSite);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(
                                attributeType => attributeType.id === 'manufacturingSite',
                            );
                        },
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Customer
                    >({
                        bind: 'customer',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().customer);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'customer');
                        },
                        node: '@sage/xtrem-master-data/Customer',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Supplier
                    >({
                        bind: 'supplier',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().supplier);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'supplier');
                        },
                        node: '@sage/xtrem-master-data/Supplier',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Attribute
                    >({
                        bind: 'employee',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().employee);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'employee');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Attribute
                    >({
                        bind: 'project',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().project);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'project');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Attribute
                    >({
                        bind: 'task',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().task);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'task');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension, Item>({
                        bind: 'item',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().item);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'item');
                        },
                        node: '@sage/xtrem-master-data/Item',
                        tunnelPage: '@sage/xtrem-master-data/Item',
                        isReadOnly: true,
                        valueField: 'composedDescription',
                    }),

                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension01',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty01,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType01',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension02',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty02,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType02',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),

                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension03',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty03,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType03',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),

                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension04',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty04,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType04',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension05',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty05,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType05',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension06',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty06,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType06',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension07',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty07,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType07',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension08',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty08,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType08',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension09',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty09,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType09',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension10',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty10,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType10',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension11',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty11,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType11',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension12',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty12,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType12',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension13',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty13,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType13',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension14',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty14,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType14',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension15',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty15,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType15',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension16',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty16,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType16',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension17',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty17,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType17',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension18',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty18,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType18',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension19',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty19,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType19',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<
                        AccountsReceivableInvoice,
                        AccountsReceivableInvoiceLineDimension,
                        Dimension
                    >({
                        bind: 'dimension20',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty20,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType20',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension>({
                        title: 'Amount',
                        bind: 'amount',
                        isReadOnly: true,
                        unit() {
                            return this.currency?.value;
                        },
                        scale: null,
                    }),
                    ui.nestedFields.technical<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension>({
                        bind: 'storedAttributes',
                    }),
                    ui.nestedFields.technical<AccountsReceivableInvoice, AccountsReceivableInvoiceLineDimension>({
                        bind: 'storedDimensions',
                    }),
                ],
            },
        ],
    })
    taxDetailLines: ui.fields.NestedGrid<[AccountsReceivableInvoiceLine, AccountsReceivableInvoiceLineDimension]>;

    @ui.decorators.section<AccountsReceivableInvoice>({ title: 'Totals' })
    totalsSection: ui.containers.Section;

    @ui.decorators.block<AccountsReceivableInvoice>({
        parent() {
            return this.totalsSection;
        },
        title: 'Totals',
        width: 'large',
    })
    totalsSectionTaxTotalsBlock: ui.containers.Block;

    @ui.decorators.numericField<AccountsReceivableInvoice>({
        title: 'Total excluding tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<AccountsReceivableInvoice>({
        title: 'Total tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericField<AccountsReceivableInvoice>({
        title: 'Total including tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountIncludingTax: ui.fields.Numeric;

    @ui.decorators.tableField<AccountsReceivableInvoice, AccountsReceivableInvoiceTax>({
        bind: 'taxes',
        title: 'Totals',
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-finance/AccountsReceivableInvoiceTax',
        isReadOnly: true,
        parent() {
            return this.totalsSection;
        },
        columns: [
            ui.nestedFields.text({ title: 'Category', bind: 'taxCategory' }),
            ui.nestedFields.text({ title: 'Tax', bind: 'tax' }),
            ui.nestedFields.numeric({
                title: 'Taxable base',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxableAmount',
            }),
            ui.nestedFields.numeric({ title: 'Rate', postfix: '%', scale: 2, bind: 'taxRate' }),
            ui.nestedFields.numeric({
                title: 'Amount',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxAmount',
            }),
        ],
    })
    taxDetails: ui.fields.Table<AccountsReceivableInvoiceTax>;

    @ui.decorators.section<AccountsReceivableInvoice>({
        title: 'Posting',
        isHidden() {
            return !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<AccountsReceivableInvoice>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.tableField<AccountsReceivableInvoice, FinanceTransactionBinding>({
        title: 'Posting',
        isTitleHidden: true,
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        isReadOnly: true,
        pageSize: 10,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page: '@sage/xtrem-finance/JournalEntry',
                queryParameters(_value, rowData: FinanceTransactionBinding) {
                    return {
                        _id: rowData.targetDocumentSysId,
                        number: rowData.targetDocumentNumber,
                    };
                },
            }),
            ui.nestedFields.link({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                onClick(_value, rowData) {
                    if (rowData.financeIntegrationAppUrl) {
                        this.$.router.goToExternal(rowData.financeIntegrationAppUrl);
                    }
                },
                isHidden() {
                    return this.postingDetails.value.at(0)?.financeIntegrationApp !== 'intacct';
                },
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table;

    @ui.decorators.textAreaField<AccountsReceivableInvoice>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<AccountsReceivableInvoice>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'arInvoice',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.postingStatus ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    getSerializedValues() {
        if (this.$.recordId) {
            const lines: (ui.PartialNodeWithId<AccountsReceivableInvoiceLine> & { _action: string })[] = [];
            let idx = 0;
            this.lines.value.forEach((line: ui.PartialNodeWithId<AccountsReceivableInvoiceLine>) => {
                if (line.attributesAndDimensions && line.attributesAndDimensions?.length > 0) {
                    lines.push({ _id: line._id, _action: 'update', attributesAndDimensions: [] });
                    line.attributesAndDimensions?.forEach(
                        (attributesAndDimension: ExtractEdgesPartial<AccountsReceivableInvoiceLineDimension>) => {
                            lines[idx].attributesAndDimensions?.push({
                                _id: attributesAndDimension._id,
                                _action: 'update',
                                storedAttributes: attributesAndDimension.storedAttributes,
                                storedDimensions: attributesAndDimension.storedDimensions,
                            } as any);
                        },
                    );
                    idx += 1;
                }
            });
            this.taxDetailLines.value.forEach((taxDetailLine: ui.PartialNodeWithId<AccountsReceivableInvoiceLine>) => {
                if (taxDetailLine.attributesAndDimensions && taxDetailLine.attributesAndDimensions?.length > 0) {
                    lines.push({ _id: taxDetailLine._id, _action: 'update', attributesAndDimensions: [] });
                    taxDetailLine.attributesAndDimensions?.forEach(
                        (attributesAndDimension: ExtractEdgesPartial<AccountsReceivableInvoiceLineDimension>) => {
                            lines[idx].attributesAndDimensions?.push({
                                _id: attributesAndDimension._id,
                                _action: 'update',
                                storedAttributes: attributesAndDimension.storedAttributes,
                                storedDimensions: attributesAndDimension.storedDimensions,
                            } as any);
                        },
                    );
                    idx += 1;
                }
            });
            if (lines.length) {
                return { _etag: this.$.values._etag, lines };
            }
        }
        return undefined;
    }
}
