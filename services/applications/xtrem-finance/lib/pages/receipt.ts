import type { GraphApi, Receipt as ReceiptNode } from '@sage/xtrem-finance-api';
import { paymentTracking } from '@sage/xtrem-finance-data/build/lib/menu-items/payment-tracking';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib//client-functions/utils';
import * as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as ui from '@sage/xtrem-ui';
import { voidPaymentOrReceipt } from '../client-functions/payment-tracking';
import { voidDateValidation } from '../client-functions/void-record';

@ui.decorators.page<Receipt>({
    title: 'Receipt',
    objectTypeSingular: 'Receipt',
    objectTypePlural: 'Receipts',
    idField() {
        return this.number;
    },
    module: 'finance',
    menuItem: paymentTracking,
    priority: 110,
    mode: 'default',
    node: '@sage/xtrem-finance/Receipt',
    skipDirtyCheck: true,
    navigationPanel: {
        orderBy: { paymentDate: -1 },
        listItem: {
            title: ui.nestedFields.link({ title: 'Number', bind: 'number', width: 'medium' }),
            line2Right: ui.nestedFields.date({
                bind: 'paymentDate',
                title: 'Payment date',
                groupAggregationMethod: 'distinctCount',
            }),
            bankAccount: ui.nestedFields.reference({
                bind: 'bankAccount',
                node: '@sage/xtrem-finance-data/BankAccount',
                title: 'Bank account',
                valueField: 'name',
                groupAggregationMethod: 'distinctCount',
            }),
            type: ui.nestedFields.dropdownList({
                bind: 'type',
                title: 'Type',
                optionType: '@sage/xtrem-master-data/BusinessRelationType',
                isHiddenOnMainField: true,
            }),
            financialSite: ui.nestedFields.reference({
                bind: 'financialSite',
                node: '@sage/xtrem-system/Site',
                title: 'Financial site',
                valueField: 'name',
                groupAggregationMethod: 'distinctCount',
            }),
            line2: ui.nestedFields.text({
                bind: 'businessRelationName',
                title: 'Business entity name',
                groupAggregationMethod: 'distinctCount',
            }),
            businessRelationId: ui.nestedFields.text({
                bind: { businessRelation: { id: true } },
                title: 'Business entity ID',
                isHiddenOnMainField: true,
            }),
            paymentMethod: ui.nestedFields.dropdownList({
                bind: 'paymentMethod',
                title: 'Payment method',
                optionType: '@sage/xtrem-master-data/PaymentMethod',
            }),
            amount: ui.nestedFields.numeric({
                bind: 'amount',
                title: 'Amount',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            currency: ui.nestedFields.reference({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                title: 'Currency',
                valueField: 'name',
                groupAggregationMethod: 'distinctCount',
            }),
            amountBankCurrency: ui.nestedFields.numeric({
                bind: 'amountBankCurrency',
                title: 'Amount in bank currency',
                groupAggregationMethod: 'sum',
                scale: null,
                unit: (_rowId, rowData) => rowData?.bankAccount?.currency,
            }),
            bankCurrency: ui.nestedFields.reference({
                bind: { bankAccount: { currency: true } },
                node: '@sage/xtrem-master-data/Currency',
                title: 'Bank account currency',
                valueField: 'name',
                groupAggregationMethod: 'distinctCount',
            }),
            amountCompanyCurrency: ui.nestedFields.numeric({
                bind: 'companyAmount',
                title: 'Amount in company currency',
                groupAggregationMethod: 'sum',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.financialSite?.legalCompany?.currency,
            }),
            companyCurrency: ui.nestedFields.reference({
                bind: { financialSite: { legalCompany: { currency: true } } },
                node: '@sage/xtrem-master-data/Currency',
                title: 'Company currency',
                valueField: 'name',
                isHiddenOnMainField: true,
            }),
            reference: ui.nestedFields.text({
                bind: 'reference',
                title: 'Reference',
                isHiddenOnMainField: true,
            }),
            titleRight: ui.nestedFields.label({
                bind: 'postingStatus',
                title: 'Status',
                optionType: '@sage/xtrem-finance-data/JournalStatus',
                style: (_id, rowValue) => PillColorCommon.getDisplayStatusPillFeatures(rowValue?.postingStatus),
                isHiddenOnMainField: true,
            }),
            isVoided: ui.nestedFields.checkbox({
                bind: 'isVoided',
                title: 'Voided',
                isHiddenOnMainField: true,
            }),
            voidDate: ui.nestedFields.date({
                bind: 'voidDate',
                title: 'Voided on',
                isHiddenOnMainField: true,
            }),
            voidText: ui.nestedFields.text({
                bind: 'voidText',
                title: 'Void text',
                isHiddenOnMainField: true,
            }),
        },
        optionsMenu: [
            {
                title: 'Posted receipts',
                id: 'postedReceipts',
                graphQLFilter: { isVoided: { _ne: true } },
            },
            {
                title: 'Voided receipts',
                id: 'voidedReceipts',
                graphQLFilter: { isVoided: true },
            },
            { title: 'All receipts', id: 'allReceipts', graphQLFilter: {} },
        ],
    },
    headerSection() {
        return this.mainSection;
    },
    businessActions() {
        return [this.cancelReceipt, this.createReceipt, this.voidReceipt];
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
    onClose() {
        if (this.$.queryParameters.createParams && this.isSaved) {
            this.$.finish(this.number.value);
        }
    },
})
export class Receipt extends ui.Page<GraphApi, ReceiptNode> {
    isSaved = false;

    number: ui.fields.Text;

    isVoided: ui.fields.Checkbox;

    paymentDate: ui.fields.Date;

    @ui.decorators.section<Receipt>({ isHidden: true, title: 'Void receipt' })
    voidSection: ui.containers.Section;

    @ui.decorators.block<Receipt>({
        parent() {
            return this.voidSection;
        },
    })
    voidBlock: ui.containers.Block;

    @ui.decorators.dateField<Receipt>({
        parent() {
            return this.voidBlock;
        },
        validation(val) {
            return voidDateValidation(val, this.paymentDate.value);
        },
        title: 'Date',
        helperText: 'Void the payment on date',
        isTransient: true,
        isMandatory: true,
    })
    voidPaymentDate: ui.fields.Date;

    @ui.decorators.textField<Receipt>({
        parent() {
            return this.voidBlock;
        },
        title: 'Text',
        isTransient: true,
        isFullWidth: true,
    })
    voidPaymentText: ui.fields.Text;

    @ui.decorators.section<Receipt>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<Receipt>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Payment information',
    })
    paymentInformationBlock: ui.containers.Block;

    @ui.decorators.fragmentFields<Receipt>({
        isTitleHidden: true,
        parent() {
            return this.paymentInformationBlock;
        },
        fragment: '@sage/xtrem-finance/BasePayment',
    })
    basePaymentFields: ui.containers.FragmentFields;

    @ui.decorators.section<Receipt>({ title: 'Lines', isTitleHidden: true }) linesSection: ui.containers.Section;

    @ui.decorators.block<Receipt>({
        parent() {
            return this.linesSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    linesBlock: ui.containers.Block;

    @ui.decorators.fragmentFields<Receipt>({
        isTitleHidden: true,
        parent() {
            return this.linesBlock;
        },
        fragment: '@sage/xtrem-finance/BasePaymentLine',
    })
    basePaymentLineFields: ui.containers.FragmentFields;

    @ui.decorators.pageAction<Receipt>({
        title: 'Confirm',
        isHidden() {
            return this.$.queryParameters.createParams === undefined;
        },
        async onClick() {
            const validation = await this.$.page.validate();
            if (!validation.length) {
                this.isSaved = true;
                await this.$standardSaveAction.execute(true);
            }
        },
    })
    createReceipt: ui.PageAction;

    @ui.decorators.pageAction<Receipt>({
        title: 'Cancel',
        isHidden() {
            return this.$.queryParameters.createParams === undefined;
        },
        buttonType: 'tertiary',
        onClick() {
            this.$.finish('');
        },
    })
    cancelReceipt: ui.PageAction;

    @ui.decorators.pageAction<Receipt>({
        title: 'Void',
        isHidden() {
            return !!this.isVoided.value || !!this.$.queryParameters.createParams;
        },
        async onClick() {
            await voidPaymentOrReceipt(this);
        },
    })
    voidReceipt: ui.PageAction;
}
