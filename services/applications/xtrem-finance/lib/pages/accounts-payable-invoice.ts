import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type {
    AccountsPayableInvoiceLine,
    AccountsPayableInvoiceLineDimension,
    AccountsPayableInvoice as AccountsPayableInvoiceNode,
    AccountsPayableInvoiceTax,
    GraphApi,
} from '@sage/xtrem-finance-api';
import type {
    Account,
    Attribute,
    Dimension,
    FinanceTransactionBinding,
    OpenItemStatus,
} from '@sage/xtrem-finance-data-api';
import type {
    ActiveAttributeType,
    ActiveDimensionType,
} from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import {
    attributeIDs,
    dimensionDocProperties,
    getAttributeTypeName,
    getDimensionTypeName,
} from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type { Cur<PERSON>cy, Customer, Item, PaymentTerm, Supplier } from '@sage/xtrem-master-data-api';
import * as Pill<PERSON><PERSON><PERSON><PERSON><PERSON>mon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import { finance } from '@sage/xtrem-master-data/build/lib/menu-items/finance';
import type { Country, Legislation } from '@sage/xtrem-structure-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import { DocumentTypeEnum } from '@sage/xtrem-tax/build/lib/client-functions/interfaces';
import * as ui from '@sage/xtrem-ui';
import {
    isAttributeHidden,
    isDimensionHidden,
    onLoadAPARInvoice,
    setApplicativePageCrudActionsAPARInvoice,
    taxDetailsAPAR,
} from '../client-functions/common';

@ui.decorators.page<AccountsPayableInvoice, AccountsPayableInvoiceNode>({
    title: 'Accounts payable invoice',
    objectTypeSingular: 'Accounts payable invoice',
    objectTypePlural: 'Accounts payable invoices',
    idField() {
        return this.number;
    },
    menuItem: finance,
    priority: 100,
    node: '@sage/xtrem-finance/AccountsPayableInvoice',
    module: 'finance',
    mode: 'tabs',
    navigationPanel: {
        orderBy: { invoiceDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            line_4: ui.nestedFields.dropdownList<AccountsPayableInvoice, AccountsPayableInvoiceNode>({
                bind: 'type',
                title: 'Type',
                optionType: '@sage/xtrem-finance-data/FinanceDocumentType',
                width: 'small',
            }),
            line_5: ui.nestedFields.date({ bind: 'invoiceDate', title: 'Invoice date' }),
            line6: ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceNode>({
                bind: 'financialSite',
                node: '@sage/xtrem-system/Site',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Financial site',
            }),
            line2: ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceNode, Supplier>({
                bind: 'billBySupplier',
                title: 'Bill-by supplier',
                node: '@sage/xtrem-master-data/Supplier',
                valueField: { businessEntity: { name: true } },
                tunnelPage: undefined,
            }),
            id: ui.nestedFields.text({
                bind: { billBySupplier: { id: true } },
                title: 'Bill-by supplier ID',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({ bind: 'postingDate' }),
            line7: ui.nestedFields.numeric<AccountsPayableInvoice, AccountsPayableInvoiceNode>({
                bind: 'totalAmountIncludingTax',
                title: 'Total including tax',
            }),
            titleRight: ui.nestedFields.label<AccountsPayableInvoice, AccountsPayableInvoiceNode>({
                bind: 'postingStatus',
                title: 'Posting status',
                optionType: '@sage/xtrem-finance-data/JournalStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            line8: ui.nestedFields.text({ bind: 'reference', title: 'Reference', isHiddenOnMainField: true }),
            line9: ui.nestedFields.dropdownList<AccountsPayableInvoice, AccountsPayableInvoiceNode>({
                bind: 'origin',
                title: 'Origin',
                optionType: '@sage/xtrem-finance-data/AccountsPayableReceivableInvoiceOrigin',
                isHiddenOnMainField: true,
            }),
            line10: ui.nestedFields.numeric<AccountsPayableInvoice, AccountsPayableInvoiceNode>({
                bind: 'totalAmountExcludingTax',
                title: 'Total excluding tax',
                isHiddenOnMainField: true,
            }),
            line11: ui.nestedFields.numeric<AccountsPayableInvoice, AccountsPayableInvoiceNode>({
                bind: 'totalTaxAmountAdjusted',
                title: 'Adjusted total vat',
                isHiddenOnMainField: true,
            }),
            line12: ui.nestedFields.numeric<AccountsPayableInvoice, AccountsPayableInvoiceNode>({
                bind: 'totalTaxAmount',
                title: 'Total vat',
                isHiddenOnMainField: true,
            }),
            line13: ui.nestedFields.date({
                bind: 'supplierDocumentDate',
                title: 'Supplier invoice date',
                isHiddenOnMainField: true,
            }),
            line14: ui.nestedFields.text({
                bind: 'supplierDocumentNumber',
                title: 'Supplier document number',
                isHiddenOnMainField: true,
            }),
            line15: ui.nestedFields.date({ bind: 'dueDate', title: 'Due date', isHiddenOnMainField: true }),
            line16: ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceNode>({
                bind: 'paymentTerm',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
        },
        optionsMenu: [
            { title: 'All', graphQLFilter: {} },
            { title: 'Draft', graphQLFilter: { postingStatus: { _eq: 'draft' } } },
            { title: 'Posted', graphQLFilter: { postingStatus: { _eq: 'posted' } } },
            { title: 'In progress', graphQLFilter: { postingStatus: { _eq: 'inProgress' } } },
            { title: 'Error', graphQLFilter: { postingStatus: { _eq: 'error' } } },
        ],
    },
    businessActions() {
        return [this.$standardSaveAction];
    },
    async onLoad() {
        await onLoadAPARInvoice(this);
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActionsAPARInvoice(isDirty, this);
    },
})
export class AccountsPayableInvoice extends ui.Page<GraphApi, AccountsPayableInvoiceNode> {
    activeDimensionTypes: ActiveDimensionType[];

    activeAttributeTypes: ActiveAttributeType[];

    fromNotificationHistory: boolean;

    @ui.decorators.section<AccountsPayableInvoice>({ title: 'General', isTitleHidden: true })
    generalSection: ui.containers.Section;

    @ui.decorators.block<AccountsPayableInvoice>({
        parent() {
            return this.generalSection;
        },
        width: 'large',
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.textField<AccountsPayableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Number',
        isReadOnly: true,
        width: 'small',
    })
    number: ui.fields.Text;

    @ui.decorators.dropdownListField<AccountsPayableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Type',
        optionType: '@sage/xtrem-finance-data/FinanceDocumentType',
        isReadOnly: true,
        width: 'small',
    })
    type: ui.fields.DropdownList;

    @ui.decorators.referenceField<AccountsPayableInvoice, Site>({
        parent() {
            return this.generalBlock;
        },
        title: 'Financial site',
        width: 'small',
        isReadOnly: true,
        columns: [
            ui.nestedFields.technical<AccountsPayableInvoice, Site, Company>({
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<AccountsPayableInvoice, Company, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                    }),
                    ui.nestedFields.technical<AccountsPayableInvoice, Company, Legislation>({
                        bind: 'legislation',
                        node: '@sage/xtrem-structure/Legislation',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'taxEngine' }),
                    ui.nestedFields.technical<AccountsPayableInvoice, Company, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
        ],
    })
    financialSite: ui.fields.Reference<Site>;

    @ui.decorators.dateField<AccountsPayableInvoice>({
        title: 'Invoice date',
        parent() {
            return this.generalBlock;
        },
        width: 'small',
        isReadOnly: true,
    })
    invoiceDate: ui.fields.Date;

    @ui.decorators.separatorField<AccountsPayableInvoice>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isHidden: true,
    })
    fieldSeparator01: ui.fields.Separator;

    @ui.decorators.textField<AccountsPayableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Reference',
        isReadOnly: true,
    })
    reference: ui.fields.Text;

    @ui.decorators.dropdownListField<AccountsPayableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Origin',
        optionType: '@sage/xtrem-finance-data/AccountsPayableReceivableInvoiceOrigin',
        isReadOnly: true,
    })
    origin: ui.fields.DropdownList;

    @ui.decorators.dateField<AccountsPayableInvoice>({
        title: 'Posting date',
        parent() {
            return this.generalBlock;
        },
        width: 'small',
        isReadOnly: true,
    })
    postingDate: ui.fields.Date;

    @ui.decorators.labelField<AccountsPayableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Posting status',
        optionType: '@sage/xtrem-finance-data/JournalStatus',
        style() {
            return PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', this.postingStatus.value);
        },
        width: 'small',
    })
    postingStatus: ui.fields.Label;

    // Starts hidden but it can be visible in some conditions
    @ui.decorators.labelField<AccountsPayableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Accounting integration status',
        isHidden: true,
        width: 'large',
        optionType: '@sage/xtrem-finance-data/PostingStatus',
        style() {
            return PillColorFinance.getLabelColorByStatus(
                'FinanceDocumentPostingStatus',
                this.financeIntegrationStatus.value,
            );
        },
    })
    financeIntegrationStatus: ui.fields.Label;

    @ui.decorators.labelField<AccountsPayableInvoice>({})
    financeIntegrationApp: ui.fields.Label;

    // Note: We need this field to retrieve the URL from the node. It is assigned to the reference link field
    // in the onLoad() action.
    @ui.decorators.textField<AccountsPayableInvoice>({})
    financeIntegrationAppUrl: ui.fields.Text;

    @ui.decorators.labelField<AccountsPayableInvoice>({})
    internalFinanceIntegrationStatus: ui.fields.Label;

    // Starts hidden but it can be visible in some conditions
    @ui.decorators.textField<AccountsPayableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Accounting integration reference',
        isHidden: true,
        isReadOnly: true,
        width: 'large',
    })
    financeIntegrationAppRecordId: ui.fields.Text;

    // Starts hidden but it can be visible in some conditions
    @ui.decorators.linkField<AccountsPayableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Accounting integration reference',
        width: 'large',
        isHidden: true,
        isTransient: true,
    })
    financeIntegrationAppRecordIdLink: ui.fields.Link;

    @ui.decorators.labelField<AccountsPayableInvoice>({
        parent() {
            return this.generalBlock;
        },
        title: 'Payment status',
        bind: { paymentTracking: { status: true } },
        optionType: '@sage/xtrem-finance-data/OpenItemStatus',
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.paymentStatus.value);
        },
        isHidden() {
            return !this.paymentStatus.value || !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
    })
    paymentStatus: ui.fields.Label<OpenItemStatus>;

    @ui.decorators.block<AccountsPayableInvoice>({
        parent() {
            return this.generalSection;
        },
        width: 'small',
    })
    supplierBlock: ui.containers.Block;

    @ui.decorators.referenceField<AccountsPayableInvoice, Supplier>({
        parent() {
            return this.supplierBlock;
        },
        title: 'Bill-by supplier',
        node: '@sage/xtrem-master-data/Supplier',
        valueField: { businessEntity: { name: true } },
        width: 'medium',
        isReadOnly: true,
        helperTextField: { businessEntity: { id: true } },
    })
    billBySupplier: ui.fields.Reference<Supplier>;

    @ui.decorators.separatorField<AccountsPayableInvoice>({
        parent() {
            return this.supplierBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    fieldSeparator03: ui.fields.Separator;

    @ui.decorators.dateField<AccountsPayableInvoice>({
        title: 'Supplier invoice date',
        parent() {
            return this.supplierBlock;
        },
        width: 'small',
        isReadOnly() {
            return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
        },
    })
    supplierDocumentDate: ui.fields.Date;

    @ui.decorators.textField<AccountsPayableInvoice>({
        parent() {
            return this.supplierBlock;
        },
        title: 'Supplier invoice number',
        width: 'small',
        isReadOnly() {
            return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
        },
    })
    supplierDocumentNumber: ui.fields.Text;

    @ui.decorators.dateField<AccountsPayableInvoice>({
        title: 'Due date',
        parent() {
            return this.supplierBlock;
        },
        width: 'small',
        isReadOnly: true,
    })
    dueDate: ui.fields.Date;

    @ui.decorators.referenceField<AccountsPayableInvoice, PaymentTerm>({
        parent() {
            return this.supplierBlock;
        },
        valueField: 'name',
        width: 'medium',
        isReadOnly: true,
    })
    paymentTerm: ui.fields.Reference<PaymentTerm>;

    @ui.decorators.separatorField<AccountsPayableInvoice>({
        parent() {
            return this.supplierBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    fieldSeparator04: ui.fields.Separator;

    @ui.decorators.referenceField<AccountsPayableInvoice, Currency>({
        node: '@sage/xtrem-master-data/Currency',
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.textField<AccountsPayableInvoice>({
        parent() {
            return this.supplierBlock;
        },
        title: 'Rate',
        isReadOnly: true,
    })
    rateDescription: ui.fields.Text;

    @ui.decorators.nestedGridField<
        AccountsPayableInvoice,
        [AccountsPayableInvoiceLine, AccountsPayableInvoiceLineDimension]
    >({
        parent() {
            return this.generalSection;
        },
        bind: 'lines',
        title: 'Lines',
        canSelect: false,
        levels: [
            {
                filter: { documentLineType: { _eq: 'documentLine' } },
                node: '@sage/xtrem-finance/AccountsPayableInvoiceLine',
                childProperty: 'attributesAndDimensions',
                columns: [
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLine, Account>({
                        node: '@sage/xtrem-finance-data/Account',
                        title: 'Account',
                        bind: 'account',
                        isReadOnly: true,
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'composedDescription' }),
                        ],
                    }),
                    ui.nestedFields.numeric<AccountsPayableInvoice, AccountsPayableInvoiceLine>({
                        title: 'Amount excluding tax',
                        bind: 'amountExcludingTax',
                        unit() {
                            return this.currency?.value;
                        },
                        scale: null,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.technical<AccountsPayableInvoice, AccountsPayableInvoiceLine>({
                        bind: 'taxAmount',
                    }),
                    ui.nestedFields.numeric<AccountsPayableInvoice, AccountsPayableInvoiceLine>({
                        title: 'Tax amount',
                        bind: 'taxAmountAdjusted',
                        isReadOnly: true,
                        unit: (_id, rowData) => rowData?.currency,
                        scale: null,
                    }),
                    ui.nestedFields.numeric<AccountsPayableInvoice, AccountsPayableInvoiceLine>({
                        title: 'Amount including tax',
                        isReadOnly: true,
                        bind: 'amountIncludingTax',
                        unit() {
                            return this.currency?.value;
                        },
                        scale: null,
                    }),
                    ui.nestedFields.dropdownList<AccountsPayableInvoice, AccountsPayableInvoiceLine>({
                        title: 'Line type',
                        width: 'small',
                        bind: 'lineType',
                        isReadOnly: true,
                        optionType: '@sage/xtrem-finance-data/AccountsPayableReceivableInvoiceLineType',
                    }),
                    ui.nestedFields.text<AccountsPayableInvoice, AccountsPayableInvoiceLine>({
                        title: 'Description',
                        bind: 'description',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLine, Site>({
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        title: 'Financial site',
                        bind: 'financialSite',
                        isReadOnly: true,
                        valueField: 'name',
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLine, Site>({
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        title: 'Receipt site',
                        bind: 'recipientSite',
                        isReadOnly: true,
                        valueField: 'name',
                    }),
                    ui.nestedFields.date<AccountsPayableInvoice, AccountsPayableInvoiceLine>({
                        title: 'Tax date',
                        bind: 'taxDate',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.technical<AccountsPayableInvoice, AccountsPayableInvoiceLine>({ bind: 'uiTaxes' }),
                    ui.nestedFields.technical<AccountsPayableInvoice, AccountsPayableInvoiceLine, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'money_bag',
                        title: 'Tax details',
                        async onClick(_recordId: string, rowItem: AccountsPayableInvoiceLine) {
                            await taxDetailsAPAR({
                                page: this,
                                documentType: DocumentTypeEnum.purchaseInvoiceLine,
                                rowItem,
                            });
                        },
                    },
                ],
            },
            {
                node: '@sage/xtrem-finance/AccountsPayableInvoiceLineDimension',
                columns: [
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Site>({
                        bind: 'financialSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().financialSite);
                        },
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        isHidden() {
                            return !this.activeAttributeTypes.some(
                                attributeType => attributeType.id === 'financialSite',
                            );
                        },
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Site>({
                        bind: 'businessSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().businessSite);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(
                                attributeType => attributeType.id === 'businessSite',
                            );
                        },
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Site>({
                        bind: 'stockSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().stockSite);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'stockSite');
                        },
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Site>({
                        bind: 'manufacturingSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().manufacturingSite);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(
                                attributeType => attributeType.id === 'manufacturingSite',
                            );
                        },
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Customer>({
                        bind: 'customer',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().customer);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'customer');
                        },
                        node: '@sage/xtrem-master-data/Customer',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Supplier>({
                        bind: 'supplier',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().supplier);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'supplier');
                        },
                        node: '@sage/xtrem-master-data/Supplier',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Attribute>({
                        bind: 'employee',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().employee);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'employee');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Attribute>({
                        bind: 'project',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().project);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'project');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Attribute>({
                        bind: 'task',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().task);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'task');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Item>({
                        bind: 'item',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().item);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'item');
                        },
                        node: '@sage/xtrem-master-data/Item',
                        tunnelPage: '@sage/xtrem-master-data/Item',
                        isReadOnly: true,
                        valueField: 'composedDescription',
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension01',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty01,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType01',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension02',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty02,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType02',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension03',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty03,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType03',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension04',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty04,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType04',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension05',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty05,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType05',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension06',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty06,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType06',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension07',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty07,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType07',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension08',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty08,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType08',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension09',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty09,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType09',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension10',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty10,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType10',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension11',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty11,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType11',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension12',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty12,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType12',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension13',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty13,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType13',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension14',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty14,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType14',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension15',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty15,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType15',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension16',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty16,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType16',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension17',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty17,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType17',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension18',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty18,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType18',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension19',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty19,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType19',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension20',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty20,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes.some(
                                dimensionType => dimensionType.docProperty === 'dimensionType20',
                            );
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension>({
                        title: 'Amount',
                        bind: 'amount',
                        isReadOnly: true,
                        unit() {
                            return this.currency?.value;
                        },
                        scale: null,
                    }),
                    ui.nestedFields.technical<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension>({
                        bind: 'storedAttributes',
                    }),
                    ui.nestedFields.technical<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension>({
                        bind: 'storedDimensions',
                    }),
                ],
            },
        ],
    })
    lines: ui.fields.NestedGrid<[AccountsPayableInvoiceLine, AccountsPayableInvoiceLineDimension]>;

    @ui.decorators.nestedGridField<
        AccountsPayableInvoice,
        [AccountsPayableInvoiceLine, AccountsPayableInvoiceLineDimension]
    >({
        parent() {
            return this.generalSection;
        },
        bind: 'lines',
        title: 'Tax detail lines',
        canSelect: false,
        levels: [
            {
                node: '@sage/xtrem-finance/AccountsPayableInvoiceLine',
                filter: { documentLineType: { _eq: 'taxLine' } },
                childProperty: 'attributesAndDimensions',
                columns: [
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLine, Account>({
                        node: '@sage/xtrem-finance-data/Account',
                        title: 'Account',
                        bind: 'account',
                        isReadOnly: true,
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'composedDescription' }),
                        ],
                    }),
                    ui.nestedFields.numeric<AccountsPayableInvoice, AccountsPayableInvoiceLine>({
                        title: 'Tax amount',
                        bind: 'taxLineTaxAmount',
                        isReadOnly: true,
                        unit: (_id, rowData) => rowData?.currency,
                    }),
                    ui.nestedFields.text<AccountsPayableInvoice, AccountsPayableInvoiceLine>({
                        title: 'Tax detail',
                        bind: 'taxDetail',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLine, Site>({
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        title: 'Financial site',
                        bind: 'financialSite',
                        isReadOnly: true,
                        valueField: 'name',
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLine, Site>({
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        title: 'Recipient site',
                        bind: 'recipientSite',
                        isReadOnly: true,
                        valueField: 'name',
                    }),
                    ui.nestedFields.technical<AccountsPayableInvoice, AccountsPayableInvoiceLine, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            },
            {
                node: '@sage/xtrem-finance/AccountsPayableInvoiceLineDimension',
                columns: [
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Site>({
                        bind: 'financialSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().financialSite);
                        },
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        isHidden() {
                            return isAttributeHidden(this, 'financialSite');
                        },
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Site>({
                        bind: 'businessSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().businessSite);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'businessSite');
                        },
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Site>({
                        bind: 'stockSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().stockSite);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'stockSite');
                        },
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Site>({
                        bind: 'manufacturingSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().manufacturingSite);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'manufacturingSite');
                        },
                        tunnelPage: '@sage/xtrem-master-data/Site',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Customer>({
                        bind: 'customer',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().customer);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'customer');
                        },
                        node: '@sage/xtrem-master-data/Customer',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Supplier>({
                        bind: 'supplier',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().supplier);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'supplier');
                        },
                        node: '@sage/xtrem-master-data/Supplier',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Attribute>({
                        bind: 'employee',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().employee);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'employee');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Attribute>({
                        bind: 'project',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().project);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'project');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Attribute>({
                        bind: 'task',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().task);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'task');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Item>({
                        bind: 'item',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().item);
                        },
                        isHidden() {
                            return isAttributeHidden(this, 'item');
                        },
                        node: '@sage/xtrem-master-data/Item',
                        tunnelPage: '@sage/xtrem-master-data/Item',
                        isReadOnly: true,
                        valueField: 'composedDescription',
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension01',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty01,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType01');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension02',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty02,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType02');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension03',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty03,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType03');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension04',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty04,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType04');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension05',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty05,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType05');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension06',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty06,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType06');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension07',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty07,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType07');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension08',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty08,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType08');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension09',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty09,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType09');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension10',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty10,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType10');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension11',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty11,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType11');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension12',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty12,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType12');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension13',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty13,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType13');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension14',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty14,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType14');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension15',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty15,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType15');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension16',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty16,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType16');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension17',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty17,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType17');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension18',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty18,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType18');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension19',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty19,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType19');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension, Dimension>({
                        bind: 'dimension20',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty20,
                            );
                        },
                        isHidden() {
                            return isDimensionHidden(this, 'dimensionType20');
                        },
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension>({
                        title: 'Amount',
                        bind: 'amount',
                        isReadOnly: true,
                        unit() {
                            return this.currency?.value;
                        },
                        scale: null,
                    }),
                    ui.nestedFields.technical<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension>({
                        bind: 'storedAttributes',
                    }),
                    ui.nestedFields.technical<AccountsPayableInvoice, AccountsPayableInvoiceLineDimension>({
                        bind: 'storedDimensions',
                    }),
                ],
            },
        ],
    })
    taxDetailLines: ui.fields.NestedGrid<[AccountsPayableInvoiceLine, AccountsPayableInvoiceLineDimension]>;

    @ui.decorators.section<AccountsPayableInvoice>({ title: 'Totals' })
    totalsSection: ui.containers.Section;

    @ui.decorators.block<AccountsPayableInvoice>({
        parent() {
            return this.totalsSection;
        },
        title: '',
        width: 'large',
    })
    totalsSectionTaxTotalsBlock: ui.containers.Block;

    @ui.decorators.numericField<AccountsPayableInvoice>({
        title: 'Calculated total excluding tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<AccountsPayableInvoice>({
        title: 'Calculated total tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericField<AccountsPayableInvoice>({
        title: 'Adjusted calculated total tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalTaxAmountAdjusted: ui.fields.Numeric;

    @ui.decorators.numericField<AccountsPayableInvoice>({
        title: 'Calculated total including tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountIncludingTax: ui.fields.Numeric;

    @ui.decorators.tableField<AccountsPayableInvoice, AccountsPayableInvoiceTax>({
        bind: 'taxes',
        title: 'Totals',
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-finance/AccountsPayableInvoiceTax',
        isReadOnly: true,
        parent() {
            return this.totalsSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.text({ title: 'Category', bind: 'taxCategory' }),
            ui.nestedFields.text({ title: 'Tax', bind: 'tax' }),
            ui.nestedFields.numeric({
                title: 'Taxable base',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxableAmount',
            }),
            ui.nestedFields.numeric({ title: 'Rate', postfix: '%', scale: 2, bind: 'taxRate' }),
            ui.nestedFields.numeric({
                title: 'Amount',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxAmount',
            }),
            ui.nestedFields.numeric({
                title: 'Adjusted amount',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxAmountAdjusted',
            }),
        ],
    })
    taxDetails: ui.fields.Table<AccountsPayableInvoiceTax>;

    @ui.decorators.section<AccountsPayableInvoice>({
        title: 'Posting',
        isHidden() {
            return !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<AccountsPayableInvoice>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.tableField<AccountsPayableInvoice, FinanceTransactionBinding>({
        title: 'Posting',
        isTitleHidden: true,
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        isReadOnly: true,
        pageSize: 10,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page: '@sage/xtrem-finance/JournalEntry',
                queryParameters(_value, rowData: FinanceTransactionBinding) {
                    return {
                        _id: rowData.targetDocumentSysId,
                        number: rowData.targetDocumentNumber,
                    };
                },
            }),
            ui.nestedFields.link({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                onClick(_value, rowData) {
                    if (rowData.financeIntegrationAppUrl) {
                        this.$.router.goToExternal(rowData.financeIntegrationAppUrl);
                    }
                },
                isHidden() {
                    return this.postingDetails.value.at(0)?.financeIntegrationApp !== 'intacct';
                },
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table;

    @ui.decorators.textAreaField<AccountsPayableInvoice>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<AccountsPayableInvoice>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'apInvoice',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.postingStatus ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    getSerializedValues() {
        if (this.$.recordId) {
            const lines: (ui.PartialNodeWithId<AccountsPayableInvoiceLine> & { _action: string })[] = [];
            let idx = 0;
            this.lines.value.forEach((line: ui.PartialNodeWithId<AccountsPayableInvoiceLine>) => {
                if (line.attributesAndDimensions && line.attributesAndDimensions?.length > 0) {
                    lines.push({ _id: line._id, _action: 'update', attributesAndDimensions: [] });
                    line.attributesAndDimensions?.forEach(
                        (attributesAndDimension: ExtractEdgesPartial<AccountsPayableInvoiceLineDimension>) => {
                            lines[idx].attributesAndDimensions?.push({
                                _id: attributesAndDimension._id,
                                _action: 'update',
                                storedAttributes: attributesAndDimension.storedAttributes,
                                storedDimensions: attributesAndDimension.storedDimensions,
                            } as any);
                        },
                    );
                    idx += 1;
                }
            });
            this.taxDetailLines.value.forEach((taxDetailLine: ui.PartialNodeWithId<AccountsPayableInvoiceLine>) => {
                if (taxDetailLine.attributesAndDimensions && taxDetailLine.attributesAndDimensions?.length > 0) {
                    lines.push({ _id: taxDetailLine._id, _action: 'update', attributesAndDimensions: [] });
                    taxDetailLine.attributesAndDimensions?.forEach(
                        (attributesAndDimension: ExtractEdgesPartial<AccountsPayableInvoiceLineDimension>) => {
                            lines[idx].attributesAndDimensions?.push({
                                _id: attributesAndDimension._id,
                                _action: 'update',
                                storedAttributes: attributesAndDimension.storedAttributes,
                                storedDimensions: attributesAndDimension.storedDimensions,
                            } as any);
                        },
                    );
                    idx += 1;
                }
            });
            if (lines.length) {
                return {
                    _etag: this.$.values._etag,
                    supplierDocumentDate: this.supplierDocumentDate.value,
                    supplierDocumentNumber: this.supplierDocumentNumber.value,
                    lines,
                };
            }
            return {
                _etag: this.$.values._etag,
                supplierDocumentDate: this.supplierDocumentDate.value,
                supplierDocumentNumber: this.supplierDocumentNumber.value,
            };
        }
        return undefined;
    }
}
