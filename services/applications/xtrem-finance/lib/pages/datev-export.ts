import type {
    DatevExportAccount as DatevExportAccountNode,
    DatevExportBusinessRelation as DatevExportBusinessRelationNode,
    DatevExportJournalEntryLine,
    DatevExport as DatevExportNode,
    GraphApi,
} from '@sage/xtrem-finance-api';
import type { Account as AccountNode, AttributeType, DimensionType } from '@sage/xtrem-finance-data-api';
import { formatStringWithLeadingZeros } from '@sage/xtrem-finance-data/build/lib/client-functions/datev';
import type { Customer as CustomerNode, Supplier as SupplierNode } from '@sage/xtrem-master-data-api';
import * as masterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { finance } from '@sage/xtrem-master-data/build/lib/menu-items/finance';
import type { Company as CompanyNode } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import { colorfulPillPattern } from '@sage/xtrem-system/build/lib/client-functions/color-pattern';
import * as ui from '@sage/xtrem-ui';
import { isEmpty } from 'lodash';
import { doExport, doExtraction, initDatevExportDimensionTypes } from '../client-functions/datev';

@ui.decorators.page<DatevExport, DatevExportNode>({
    module: 'finance',
    title: 'DATEV export',
    objectTypeSingular: 'DATEV export',
    objectTypePlural: 'DATEV exports',
    idField() {
        return this.id;
    },
    mode: 'tabs',
    menuItem: finance,
    priority: 1010,
    node: '@sage/xtrem-finance/DatevExport',
    navigationPanel: {
        orderBy: { _createStamp: -1 },
        listItem: {
            id: ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            title: ui.nestedFields.reference<DatevExport, DatevExportNode, CompanyNode>({
                bind: 'company',
                valueField: 'name',
                title: 'Company',
                node: '@sage/xtrem-system/Company',
                tunnelPage: undefined,
            }),
            timeStamp: ui.nestedFields.date({ bind: 'timeStamp', title: 'Extracted on' }),
            datevConsultantNumber: ui.nestedFields.text({ bind: 'datevConsultantNumber', title: 'Consultant number' }),
            datevcustomerNumber: ui.nestedFields.text({ bind: 'datevCustomerNumber', title: 'Customer number' }),
            fiscalYearStart: ui.nestedFields.date({ bind: 'fiscalYearStart', title: 'Fiscal year start' }),
            line2: ui.nestedFields.date({ bind: 'startDate', title: 'Start date' }),
            line2Right: ui.nestedFields.date({ bind: 'endDate', title: 'End date' }),
            isLocked: ui.nestedFields.checkbox({ bind: 'isLocked', title: 'Locked' }),
            doAccounts: ui.nestedFields.checkbox({ bind: 'doAccounts', title: 'Accounts' }),
            doCustomersSuppliers: ui.nestedFields.checkbox({
                bind: 'doCustomersSuppliers',
                title: 'Customers and suppliers',
            }),
            doJournalEntries: ui.nestedFields.checkbox({
                bind: 'doJournalEntries',
                title: 'Journal entries',
            }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-finance/DatevExportStatus',
            }),
            _createStamp: ui.nestedFields.date({ bind: '_createStamp', isHiddenOnMainField: true }),
        },
        dropdownActions: [
            {
                title: 'Extract',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<DatevExportNode>) {
                    await doExtraction({
                        pageInstance: this,
                        doAccounts: rowItem.doAccounts || false,
                        doCustomersSuppliers: rowItem.doCustomersSuppliers || false,
                        doJournalEntries: rowItem.doJournalEntries || false,
                        id: rowItem.id ?? '',
                        recordId,
                    });
                },
                isHidden(_recordId, rowItem: ui.PartialNodeWithId<DatevExportNode>) {
                    return ['extractionInProgress', 'exportInProgress', 'exported'].includes(rowItem.status || '');
                },
            },
            {
                title: 'Export',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<DatevExportNode>) {
                    await doExport({
                        pageInstance: this,
                        doAccounts: rowItem.doAccounts || false,
                        doCustomersSuppliers: rowItem.doCustomersSuppliers || false,
                        doJournalEntries: rowItem.doJournalEntries || false,
                        id: rowItem.id ?? '',
                        recordId,
                    });
                },
                isHidden(_recordId, rowItem: ui.PartialNodeWithId<DatevExportNode>) {
                    return ['draft', 'extractionInProgress', 'exportInProgress'].includes(rowItem.status || '');
                },
            },
        ],
    },
    createAction() {
        return this.createDatevExport;
    },
    headerSection() {
        return this.headerSection;
    },
    headerLabel() {
        return this.status;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction, this.extract, this.export];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
        if (this.$.recordId) {
            this.skrCoaString.value = this.skrCoa.value
                ? formatStringWithLeadingZeros(this.skrCoa.value.toString(), 2)
                : '';
            initDatevExportDimensionTypes(this);
        }
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class DatevExport extends ui.Page<GraphApi, DatevExportNode> {
    @ui.decorators.pageAction<DatevExport>({
        title: 'Create',
        icon: 'add',
        buttonType: 'primary',
        async onClick() {
            const datevExportSettingsReturn = await this.$.dialog.page(
                '@sage/xtrem-finance/DatevExportSettings',
                {},
                { size: 'large', resolveOnCancel: true },
            );
            if (datevExportSettingsReturn && !isEmpty(datevExportSettingsReturn)) {
                await this.$.router.selectRecord(datevExportSettingsReturn._id);
                await this.$.refreshNavigationPanel();
                await this.$.router.refresh();
            }
        },
    })
    createDatevExport: ui.PageAction;

    @ui.decorators.pageAction<DatevExport>({
        title: 'Extract',
        buttonType: 'primary',
        isHidden() {
            return ['extractionInProgress', 'exportInProgress', 'exported'].includes(this.status.value || '');
        },
        onError(error: string | (Error & { errors: Array<any> })) {
            return masterDataUtils.formatError(this, error);
        },
        async onClick() {
            const validation = await this.$.page.validate();

            if (validation.length > 0) {
                this.$.showToast(validation.join('\n'), { type: 'error' });
                return;
            }

            this.$.loader.isHidden = false;
            await doExtraction({
                pageInstance: this,
                doAccounts: this.doAccounts.value ?? false,
                doCustomersSuppliers: this.doCustomersSuppliers.value ?? false,
                doJournalEntries: this.doJournalEntries.value ?? false,
                id: this.id.value ?? '',
                recordId: this.$.recordId ?? '',
            });
            this.$.loader.isHidden = true;

            await this.$.router.refresh();
            await this.$.refreshNavigationPanel();
        },
    })
    extract: ui.PageAction;

    @ui.decorators.pageAction<DatevExport>({
        title: 'Export',
        buttonType: 'primary',
        isHidden() {
            return ['draft', 'extractionInProgress', 'exportInProgress'].includes(this.status.value || '');
        },
        onError(error: string | (Error & { errors: Array<any> })) {
            return masterDataUtils.formatError(this, error);
        },
        async onClick() {
            const validation = await this.$.page.validate();

            if (validation.length > 0) {
                this.$.showToast(validation.join('\n'), { type: 'error' });
                return;
            }

            this.$.loader.isHidden = false;
            await doExport({
                pageInstance: this,
                doAccounts: this.doAccounts.value ?? false,
                doCustomersSuppliers: this.doCustomersSuppliers.value ?? false,
                doJournalEntries: this.doJournalEntries.value ?? false,
                id: this.id.value ?? '',
                recordId: this.$.recordId ?? '',
            });
            this.$.loader.isHidden = true;

            await this.$.router.refresh();
            await this.$.refreshNavigationPanel();
        },
    })
    export: ui.PageAction;

    @ui.decorators.section<DatevExport>({ title: 'Header section', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.section<DatevExport>({
        title: 'Accounts without DATEV ID',
        isHidden() {
            return this.status.value !== 'draft';
        },
    })
    acountsWithoutDatevIdSection: ui.containers.Section;

    @ui.decorators.section<DatevExport>({
        title: 'Customers without DATEV ID',
        isHidden() {
            return this.status.value !== 'draft';
        },
    })
    customersWithoutDatevIdSection: ui.containers.Section;

    @ui.decorators.section<DatevExport>({
        title: 'Suppliers without DATEV ID',
        isHidden() {
            return this.status.value !== 'draft';
        },
    })
    suppliersWithoutDatevIdSection: ui.containers.Section;

    @ui.decorators.section<DatevExport>({
        title: 'Extracted accounts',
        isHidden() {
            return (
                ['draft', 'extractionInProgress'].includes(this.status.value || '') || this.doAccounts.value === false
            );
        },
    })
    accountsSection: ui.containers.Section;

    @ui.decorators.section<DatevExport>({
        title: 'Extracted customers and suppliers',
        isHidden() {
            return (
                ['draft', 'extractionInProgress'].includes(this.status.value || '') ||
                this.doCustomersSuppliers.value === false
            );
        },
    })
    businessRelationsSection: ui.containers.Section;

    @ui.decorators.section<DatevExport>({
        title: 'Extracted journal entry lines',
        isHidden() {
            return (
                ['draft', 'extractionInProgress'].includes(this.status.value || '') ||
                this.doJournalEntries.value === false
            );
        },
    })
    journalEntryLineSection: ui.containers.Section;

    @ui.decorators.block<DatevExport>({
        parent() {
            return this.headerSection;
        },
        width: 'extra-large',
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.labelField<DatevExport>({
        title: 'Status',
        optionType: '@sage/xtrem-finance/DatevExportStatus',
        style() {
            return colorfulPillPattern.filledNeutral;
        },
    })
    status: ui.fields.Label;

    @ui.decorators.textField<DatevExport>({
        parent() {
            return this.headerBlock;
        },
        title: 'ID',
        isDisabled: true,
    })
    id: ui.fields.Text;

    @ui.decorators.referenceField<DatevExport, CompanyNode>({
        parent() {
            return this.headerBlock;
        },
        valueField: 'name',
        isDisabled: true,
        width: 'small',
    })
    company: ui.fields.Reference<CompanyNode>;

    @ui.decorators.checkboxField<DatevExport>({
        title: 'Locked',
        parent() {
            return this.headerBlock;
        },
        isDisabled: true,
    })
    isLocked: ui.fields.Checkbox;

    @ui.decorators.dateField<DatevExport>({
        parent() {
            return this.headerBlock;
        },
        title: 'Start date',
        width: 'small',
        isDisabled: true,
    })
    startDate: ui.fields.Date;

    @ui.decorators.dateField<DatevExport>({
        parent() {
            return this.headerBlock;
        },
        title: 'End date',
        width: 'small',
        isDisabled: true,
    })
    endDate: ui.fields.Date;

    @ui.decorators.dateField<DatevExport>({
        parent() {
            return this.headerBlock;
        },
        title: 'Fiscal year start',
        width: 'small',
        isMandatory: true,
        isDisabled: true,
    })
    fiscalYearStart: ui.fields.Date;

    @ui.decorators.textField<DatevExport>({
        parent() {
            return this.headerBlock;
        },
        title: 'Dimension type 1',
        isTransient: true,
        isDisabled: true,
    })
    dimensionTypeText1: ui.fields.Text;

    @ui.decorators.textField<DatevExport>({
        parent() {
            return this.headerBlock;
        },
        title: 'Dimension type 2',
        isTransient: true,
        isDisabled: true,
    })
    dimensionTypeText2: ui.fields.Text;

    @ui.decorators.referenceField<DatevExport, DimensionType>({
        isHidden: true,
    })
    dimensionType1: ui.fields.Reference<DimensionType>;

    @ui.decorators.referenceField<DatevExport, AttributeType>({
        isHidden: true,
    })
    attributeType1: ui.fields.Reference<AttributeType>;

    @ui.decorators.referenceField<DatevExport, DimensionType>({
        isHidden: true,
    })
    dimensionType2: ui.fields.Reference<DimensionType>;

    @ui.decorators.referenceField<DatevExport, AttributeType>({
        isHidden: true,
    })
    attributeType2: ui.fields.Reference<AttributeType>;

    @ui.decorators.checkboxField<DatevExport>({
        title: 'Export journal entries',
        parent() {
            return this.headerBlock;
        },
        isDisabled: true,
    })
    doJournalEntries: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DatevExport>({
        title: 'Export accounts',
        parent() {
            return this.headerBlock;
        },
        isDisabled: true,
    })
    doAccounts: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DatevExport>({
        title: 'Export customers and suppliers',
        parent() {
            return this.headerBlock;
        },
        isDisabled: true,
    })
    doCustomersSuppliers: ui.fields.Checkbox;

    @ui.decorators.separatorField<DatevExport>({
        parent() {
            return this.headerBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    companyFieldsSeparator: ui.fields.Separator;

    @ui.decorators.textField<DatevExport>({
        parent() {
            return this.headerBlock;
        },
        title: 'DATEV consultant number',
        isDisabled: true,
    })
    datevConsultantNumber: ui.fields.Text;

    @ui.decorators.textField<DatevExport>({
        parent() {
            return this.headerBlock;
        },
        title: 'DATEV customer number',
        isDisabled: true,
    })
    datevCustomerNumber: ui.fields.Text;

    @ui.decorators.numericField<DatevExport>({
        isHidden: true,
    })
    skrCoa: ui.fields.Numeric;

    @ui.decorators.textField<DatevExport>({
        parent() {
            return this.headerBlock;
        },
        isDisabled: true,
        title: 'SKR',
        isTransient: true,
    })
    skrCoaString: ui.fields.Text;

    @ui.decorators.relativeDateField<DatevExport>({
        parent() {
            return this.headerBlock;
        },
        title: 'Last extraction date',
        isDisabled: true,
    })
    timeStamp: ui.fields.RelativeDate;

    @ui.decorators.tableField<DatevExport, AccountNode>({
        parent() {
            return this.acountsWithoutDatevIdSection;
        },
        title: 'Accounts without DATEV ID',
        canSelect: false,
        isChangeIndicatorDisabled: true,
        node: '@sage/xtrem-finance-data/Account',
        orderBy: { id: +1 },
        columns: [
            ui.nestedFields.link({
                title: 'ID',
                bind: 'id',
                async onClick(rowId) {
                    await this.$.dialog.page(
                        '@sage/xtrem-finance-data/Account',
                        { _id: rowId },
                        { resolveOnCancel: true },
                    );
                    await this.accountsWithoutDatevId.refresh();
                },
            }),
            ui.nestedFields.text({ title: 'Name', bind: 'name', isDisabled: true }),
        ],
    })
    accountsWithoutDatevId: ui.fields.Table<AccountNode>;

    @ui.decorators.tableField<DatevExport, CustomerNode>({
        parent() {
            return this.customersWithoutDatevIdSection;
        },
        title: 'Customers without DATEV ID',
        canSelect: false,
        isChangeIndicatorDisabled: true,
        node: '@sage/xtrem-master-data/Customer',
        orderBy: { id: +1 },
        columns: [
            ui.nestedFields.technical({ bind: { businessEntity: { _id: true } } }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-master-data/BusinessEntity',
                bind: 'businessEntity',
                valueField: 'id',
                isHidden: true,
                columns: [ui.nestedFields.technical({ bind: '_id' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.link({
                title: 'ID',
                bind: 'id',
                async onClick(_rowId, rowData) {
                    await this.$.dialog.page(
                        '@sage/xtrem-master-data/Customer',
                        { _id: rowData._id ?? '' },
                        { resolveOnCancel: true },
                    );
                    await this.customersWithoutDatevId.refresh();
                },
            }),
            ui.nestedFields.text({ title: 'Name', bind: 'name', isDisabled: true }),
        ],
    })
    customersWithoutDatevId: ui.fields.Table<CustomerNode>;

    @ui.decorators.tableField<DatevExport, SupplierNode>({
        parent() {
            return this.suppliersWithoutDatevIdSection;
        },
        title: 'Suppliers without DATEV ID',
        canSelect: false,
        isChangeIndicatorDisabled: true,
        node: '@sage/xtrem-master-data/Supplier',
        orderBy: { id: +1 },
        columns: [
            ui.nestedFields.technical({ bind: { businessEntity: { _id: true } } }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-master-data/BusinessEntity',
                bind: 'businessEntity',
                valueField: 'id',
                isHidden: true,
                columns: [ui.nestedFields.technical({ bind: '_id' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.link({
                title: 'ID',
                bind: 'id',
                async onClick(_rowId, rowData) {
                    await this.$.dialog.page(
                        '@sage/xtrem-master-data/Supplier',
                        { _id: rowData._id ?? '' },
                        { resolveOnCancel: true },
                    );
                    await this.suppliersWithoutDatevId.refresh();
                },
            }),
            ui.nestedFields.text({ title: 'Name', bind: 'name', isDisabled: true }),
        ],
    })
    suppliersWithoutDatevId: ui.fields.Table<SupplierNode>;

    @ui.decorators.tableField<DatevExport, DatevExportAccountNode>({
        parent() {
            return this.accountsSection;
        },
        title: 'Extracted accounts',
        canSelect: false,
        isChangeIndicatorDisabled: true,
        isReadOnly: true,
        node: '@sage/xtrem-finance/DatevExportAccount',
        orderBy: { account: { id: +1 } },
        columns: [
            ui.nestedFields.reference({
                bind: 'account',
                node: '@sage/xtrem-finance-data/Account',
                valueField: 'id',
                isHidden: true,
                columns: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.link({
                title: 'ID',
                bind: { account: { id: true } },
                async onClick(_rowId, rowData) {
                    await this.$.dialog.page(
                        '@sage/xtrem-finance-data/Account',
                        { _id: rowData.account?._id ?? '' },
                        { resolveOnCancel: true },
                    );
                    await this.datevExportJournalEntryLines.refresh();
                },
            }),
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'DATEV ID', bind: 'datevId' }),
        ],
    })
    datevExportAccounts: ui.fields.Table<DatevExportAccountNode>;

    @ui.decorators.tableField<DatevExport, DatevExportBusinessRelationNode>({
        parent() {
            return this.businessRelationsSection;
        },
        title: 'Extracted customers and suppliers',
        canSelect: false,
        isChangeIndicatorDisabled: true,
        isReadOnly: true,
        node: '@sage/xtrem-finance/DatevExportBusinessRelation',
        orderBy: { datevId: +1 },
        columns: [
            ui.nestedFields.reference({
                bind: 'businessRelation',
                node: '@sage/xtrem-master-data/BaseBusinessRelation',
                valueField: '_id',
                isHidden: true,
                columns: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical({ bind: '_constructor' }),
                ],
            }),
            ui.nestedFields.link({
                title: 'ID',
                bind: { businessRelation: { id: true } },
                async onClick(_rowId, rowData) {
                    if (rowData.businessRelation?._constructor === 'Supplier') {
                        await this.$.dialog.page(
                            '@sage/xtrem-master-data/Supplier',
                            {
                                _id: rowData.businessRelation?._id ?? '',
                            },
                            { fullScreen: true, resolveOnCancel: true },
                        );
                    }
                    if (rowData.businessRelation?._constructor === 'Customer') {
                        await this.$.dialog.page(
                            '@sage/xtrem-master-data/Customer',
                            {
                                _id: rowData.businessRelation?._id ?? '',
                            },
                            { fullScreen: true, resolveOnCancel: true },
                        );
                    }
                    await this.datevExportBusinessRelations.refresh();
                },
            }),
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'DATEV ID', bind: 'datevId' }),
            ui.nestedFields.text({ title: 'Tax ID', bind: 'taxIdNumber' }),
            ui.nestedFields.text({ title: 'Country', bind: { country: { name: true } } }),
            ui.nestedFields.text({ title: 'City', bind: 'city' }),
            ui.nestedFields.text({ title: 'Street', bind: 'street' }),
            ui.nestedFields.text({ title: 'Postal code', bind: 'postcode' }),
        ],
    })
    datevExportBusinessRelations: ui.fields.Table<DatevExportBusinessRelationNode>;

    @ui.decorators.tableField<DatevExport, DatevExportJournalEntryLine>({
        parent() {
            return this.journalEntryLineSection;
        },
        title: 'Extracted journal entry lines',
        canSelect: false,
        isChangeIndicatorDisabled: true,
        isReadOnly: true,
        node: '@sage/xtrem-finance/DatevExportJournalEntryLine',
        orderBy: { postingDate: +1, number: +1 },
        columns: [
            ui.nestedFields.date({ title: 'Posting date', bind: 'postingDate' }),
            ui.nestedFields.link({
                title: 'Number',
                bind: 'number',
                async onClick(_rowId, rowData) {
                    await this.$.dialog.page(
                        '@sage/xtrem-finance/JournalEntry',
                        { _id: rowData.journalEntryLine?.journalEntry?._id ?? '' },
                        { fullScreen: true, resolveOnCancel: true },
                    );
                    await this.datevExportJournalEntryLines.refresh();
                },
            }),
            ui.nestedFields.reference({
                bind: 'journalEntryLine',
                node: '@sage/xtrem-finance/JournalEntryLine',
                valueField: '_id',
                isHidden: true,
                columns: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.reference({
                        bind: 'journalEntry',
                        node: '@sage/xtrem-finance/JournalEntry',
                        valueField: 'number',
                        columns: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'origin' }),
                        ],
                    }),
                    ui.nestedFields.reference({
                        bind: 'account',
                        node: '@sage/xtrem-finance-data/Account',
                        valueField: 'id',
                        columns: [ui.nestedFields.technical({ bind: '_id' })],
                    }),
                    ui.nestedFields.reference({
                        bind: 'contraJournalEntryLine',
                        node: '@sage/xtrem-finance/JournalEntryLine',
                        valueField: '_id',
                        columns: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.reference({
                                bind: 'account',
                                node: '@sage/xtrem-finance-data/Account',
                                valueField: 'id',
                                columns: [ui.nestedFields.technical({ bind: '_id' })],
                            }),
                            ui.nestedFields.reference({
                                bind: 'businessEntity',
                                node: '@sage/xtrem-master-data/BusinessEntity',
                                valueField: 'id',
                                columns: [
                                    ui.nestedFields.technical({ bind: '_id' }),
                                    ui.nestedFields.reference({
                                        bind: 'customer',
                                        node: '@sage/xtrem-master-data/Customer',
                                        valueField: 'id',
                                        columns: [ui.nestedFields.technical({ bind: '_id' })],
                                    }),
                                    ui.nestedFields.reference({
                                        bind: 'supplier',
                                        node: '@sage/xtrem-master-data/Supplier',
                                        valueField: 'id',
                                        columns: [ui.nestedFields.technical({ bind: '_id' })],
                                    }),
                                ],
                            }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference({
                bind: 'transactionCurrency',
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                isHidden: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Transaction amount',
                bind: 'transactionValue',
                prefix(_value, rowData) {
                    return rowData?.transactionCurrency?.symbol ?? '';
                },
                scale(_value, rowData) {
                    return rowData?.transactionCurrency?.decimalDigits ?? 2;
                },
            }),
            ui.nestedFields.text({ title: 'Sign', bind: 'datevSign' }),
            ui.nestedFields.reference({
                bind: 'companyCurrency',
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                isHidden: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Amount in company currency',
                bind: 'companyValue',
                unit: (_id, rowData) => rowData?.companyCurrency,
            }),
            ui.nestedFields.numeric({ title: 'Rate', bind: 'companyFxRate' }),
            ui.nestedFields.link({
                title: 'DATEV account ID',
                bind: 'datevAccountId',
                async onClick(_rowId, rowData) {
                    await this.$.dialog.page(
                        '@sage/xtrem-finance-data/Account',
                        { _id: rowData.journalEntryLine?.account?._id ?? '' },
                        { resolveOnCancel: true },
                    );
                    await this.datevExportJournalEntryLines.refresh();
                },
            }),
            ui.nestedFields.link({
                title: 'DATEV contra account ID',
                bind: 'datevContraAccountId',
                async onClick(_rowId, rowData) {
                    if (rowData.journalEntryLine?.journalEntry?.origin === 'apInvoice') {
                        await this.$.dialog.page(
                            '@sage/xtrem-master-data/Supplier',
                            {
                                _id:
                                    rowData.journalEntryLine?.contraJournalEntryLine?.businessEntity?.supplier?._id ??
                                    '',
                            },
                            { fullScreen: true, resolveOnCancel: true },
                        );
                    }
                    if (rowData.journalEntryLine?.journalEntry?.origin === 'arInvoice') {
                        await this.$.dialog.page(
                            '@sage/xtrem-master-data/Customer',
                            {
                                _id:
                                    rowData.journalEntryLine?.contraJournalEntryLine?.businessEntity?.customer?._id ??
                                    '',
                            },
                            { fullScreen: true, resolveOnCancel: true },
                        );
                    }
                    if (!['apInvoice', 'arInvoice'].includes(rowData.journalEntryLine?.journalEntry?.origin)) {
                        await this.$.dialog.page(
                            '@sage/xtrem-finance-data/Account',
                            { _id: rowData.journalEntryLine?.contraJournalEntryLine?.account?._id ?? '' },
                            { resolveOnCancel: true },
                        );
                    }
                    await this.datevExportJournalEntryLines.refresh();
                },
            }),
            ui.nestedFields.numeric({ title: 'Posting key', bind: 'postingKey' }),
            ui.nestedFields.text({ title: 'Business entity tax ID', bind: 'businessEntityTaxIdNumber' }),
            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
            ui.nestedFields.text({ title: 'Supplier document number', bind: 'supplierDocumentNumber' }),
            ui.nestedFields.text({ title: 'Dimension 1', bind: 'dimension1' }),
            ui.nestedFields.text({ title: 'Dimension 2', bind: 'dimension2' }),
            ui.nestedFields.text({ title: 'Site tax ID', bind: 'siteTaxIdNumber' }),
            ui.nestedFields.numeric({ title: 'Locked', bind: 'locked' }),
        ],
    })
    datevExportJournalEntryLines: ui.fields.Table<DatevExportJournalEntryLine>;
}
