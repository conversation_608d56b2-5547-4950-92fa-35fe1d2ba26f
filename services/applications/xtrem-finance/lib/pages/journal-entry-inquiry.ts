import type { Filter } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type {
    GraphApi,
    JournalEntry,
    JournalEntryInquiry as JournalEntryInquiryNode,
    JournalEntryLine,
} from '@sage/xtrem-finance-api';
import type { Account, Attribute, Dimension, Journal } from '@sage/xtrem-finance-data-api';
import type {
    ActiveAttributeType,
    ActiveDimensionType,
} from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import {
    attributeIDs,
    dimensionDocProperties,
    getActiveAttributes,
    getActiveDimensions,
    getAttributeTypeName,
    getDimensionTypeName,
} from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import type { BusinessEntity, Currency, Customer, Item, Supplier } from '@sage/xtrem-master-data-api';
import { finance } from '@sage/xtrem-master-data/build/lib/menu-items/finance';
import { setMultiReferenceIfSingleValue } from '@sage/xtrem-master-data/lib/client-functions/get-default';
import type { Company, Site } from '@sage/xtrem-system-api';
import type { Tax } from '@sage/xtrem-tax-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<JournalEntryInquiry, JournalEntryInquiryNode>({
    title: 'Journal entry inquiry',
    mode: 'tabs',
    menuItem: finance,
    priority: 400,
    node: '@sage/xtrem-finance/JournalEntryInquiry',
    module: 'xtrem-finance',
    navigationPanel: undefined,
    async defaultEntry() {
        const inquiry = this.$.graph
            .node('@sage/xtrem-finance/JournalEntryInquiry')
            .mutations.singleRecord(true, {})
            .execute();
        this.activeDimensionTypes = await getActiveDimensions(this);
        this.activeAttributeTypes = await getActiveAttributes(this);
        return inquiry;
    },
    async onLoad() {
        this.dateFrom.value = DateValue.today().addDays(-1).toString();
        await setMultiReferenceIfSingleValue([this.companyFilter]);
    },
    skipDirtyCheck: true,
    headerSection() {
        return this.generalSection;
    },
})
export class JournalEntryInquiry extends ui.Page<GraphApi, JournalEntryInquiryNode> {
    activeDimensionTypes: ActiveDimensionType[];

    activeAttributeTypes: ActiveAttributeType[];

    @ui.decorators.section<JournalEntryInquiry>({ isTitleHidden: true })
    generalSection: ui.containers.Section;

    @ui.decorators.block<JournalEntryInquiry>({
        parent() {
            return this.generalSection;
        },
        title: 'Criteria',
    })
    filtersBlock: ui.containers.Block;

    @ui.decorators.multiReferenceField<JournalEntryInquiry, Company>({
        node: '@sage/xtrem-system/Company',
        parent() {
            return this.filtersBlock;
        },
        isTransient: true,
        title: 'Companies',
        lookupDialogTitle: 'Select companies',
        minLookupCharacters: 0,
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
        valueField: 'name',
        helperTextField: 'id',
    })
    companyFilter: ui.fields.MultiReference<Company>;

    @ui.decorators.dateField<JournalEntryInquiry>({
        parent() {
            return this.filtersBlock;
        },
        isTransient: true,
        title: 'Start date',
        width: 'small',
        validation() {
            return this.checkDateRange();
        },
    })
    dateFrom: ui.fields.Date;

    @ui.decorators.dateField<JournalEntryInquiry>({
        parent() {
            return this.filtersBlock;
        },
        isTransient: true,
        title: 'End date',
        width: 'small',
        validation() {
            return this.checkDateRange();
        },
    })
    dateTo: ui.fields.Date;

    @ui.decorators.multiReferenceField<JournalEntryInquiry, Journal>({
        node: '@sage/xtrem-finance-data/Journal',
        parent() {
            return this.filtersBlock;
        },
        isTransient: true,
        title: 'Journals',
        lookupDialogTitle: 'Select journals',
        minLookupCharacters: 0,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference({ bind: 'legislation' }),
        ],

        valueField: 'name',
        helperTextField: 'id',
    })
    journalFilter: ui.fields.MultiReference<Journal>;

    @ui.decorators.buttonField<JournalEntryInquiry>({
        parent() {
            return this.filtersBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-finance/search', 'Search');
        },
        width: 'small',
        isTransient: true,
        onClick() {
            this.journalEntryLines.filter = this.getFilter();
        },
    })
    searchButton: ui.fields.Button;

    @ui.decorators.section({ title: 'Lines' }) linesSection: ui.containers.Section;

    @ui.decorators.tableField<JournalEntryInquiry, JournalEntryLine>({
        parent() {
            return this.linesSection;
        },
        orderBy: { financialSite: { legalCompany: { name: 1 } }, journalEntry: { number: 1 } },
        canExport: true,
        canSelect: false,
        isReadOnly: true,
        title: 'Lines',
        filter: { journalEntry: { postingDate: { _gte: DateValue.today().addDays(-1).toString() } } },
        node: '@sage/xtrem-finance/JournalEntryLine',
        columns: [
            ui.nestedFields.text<JournalEntryInquiry, JournalEntryLine>({
                title: 'Journal code',
                bind: { journalEntry: { journal: { id: true } } },
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Journal>({
                node: '@sage/xtrem-finance-data/Journal',
                title: 'Journal name',
                bind: { journalEntry: { journal: true } },
                valueField: 'name',
            }),
            ui.nestedFields.text<JournalEntryInquiry, JournalEntryLine>({
                title: 'Number',
                bind: { journalEntry: { number: true } },
            }),
            ui.nestedFields.date<JournalEntryInquiry, JournalEntryLine>({
                title: 'Posting date',
                bind: { journalEntry: { postingDate: true } },
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Account>({
                node: '@sage/xtrem-finance-data/Account',
                title: 'Account code',
                bind: 'account',
                valueField: 'id',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Account>({
                node: '@sage/xtrem-finance-data/Account',
                title: 'Account name',
                bind: 'account',
                valueField: 'name',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, BusinessEntity>({
                node: '@sage/xtrem-master-data/BusinessEntity',
                tunnelPage: '@sage/xtrem-master-data/BusinessEntity',
                title: 'Business entity code',
                bind: 'businessEntity',
                valueField: 'id',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, BusinessEntity>({
                node: '@sage/xtrem-master-data/BusinessEntity',
                tunnelPage: '@sage/xtrem-master-data/BusinessEntity',
                title: 'Business entity name',
                bind: 'businessEntity',
                valueField: 'name',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, JournalEntry>({
                node: '@sage/xtrem-finance/JournalEntry',
                title: 'Reference',
                bind: 'journalEntry',
                valueField: 'reference',
            }),
            ui.nestedFields.date<JournalEntryInquiry, JournalEntryLine>({
                title: 'Reference document date',
                bind: { journalEntry: { postingDate: true } },
            }),
            ui.nestedFields.text({ title: 'Description', bind: 'inquiryDescription' }),
            ui.nestedFields.numeric({ title: 'Amount', bind: 'companyAmount' }),
            ui.nestedFields.numeric({ title: 'Sign', bind: 'numericSign' }),
            ui.nestedFields.text({ title: 'Matching', bind: 'blank' }),
            ui.nestedFields.text({ title: 'Matching date', bind: 'blank' }),
            ui.nestedFields.date({ title: 'Validation date', bind: 'validationDate' }),
            ui.nestedFields.numeric({ title: 'Transaction amount', bind: 'signedTransactionAmount' }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Currency>({
                node: '@sage/xtrem-master-data/Currency',
                title: 'Transaction currency',
                bind: 'inquiryTransactionCurrency',
                valueField: 'id',
            }),
            ui.nestedFields.text({ title: 'Company name', bind: { financialSite: { legalCompany: { name: true } } } }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Site>({
                title: 'Site code',
                bind: 'financialSite',
                valueField: 'id',
            }),
            ui.nestedFields.date<JournalEntryInquiry, JournalEntryLine>({ title: 'Due date', bind: 'dueDate' }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Tax>({
                node: '@sage/xtrem-tax/Tax',
                title: 'Tax',
                bind: 'tax',
                valueField: 'name',
            }),
            ui.nestedFields.date<JournalEntryInquiry, JournalEntryLine>({ title: 'Tax date', bind: 'taxDate' }),
            ui.nestedFields.text<JournalEntryInquiry, JournalEntryLine>({
                title: 'Common reference',
                bind: 'commonReference',
            }),
            ui.nestedFields.text<JournalEntryInquiry>({ title: 'Contra account', bind: 'contraAccount' }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Site>({
                bind: 'financialSiteAttribute',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().financialSite);
                },
                isHidden() {
                    return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'financialSite');
                },
                valueField: { businessEntity: { composedDescription: true } },
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Supplier>({
                bind: 'supplierAttribute',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().supplier);
                },
                isHidden() {
                    return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'supplier');
                },
                node: '@sage/xtrem-master-data/Supplier',
                valueField: { businessEntity: { composedDescription: true } },
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Customer>({
                bind: 'customerAttribute',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().customer);
                },
                isHidden() {
                    return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'customer');
                },
                node: '@sage/xtrem-master-data/Customer',
                valueField: { businessEntity: { composedDescription: true } },
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Item>({
                bind: 'itemAttribute',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().item);
                },
                isHidden() {
                    return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'item');
                },
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Attribute>({
                bind: 'projectAttribute',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().project);
                },
                isHidden() {
                    return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'project');
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Attribute>({
                bind: 'taskAttribute',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().task);
                },
                isHidden() {
                    return !this.activeAttributeTypes.map(attributeType => attributeType.id).includes('task');
                },
                isReadOnly: true,
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Site>({
                bind: 'businessSiteAttribute',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().businessSite);
                },
                isHidden() {
                    return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'businessSite');
                },
                valueField: { businessEntity: { composedDescription: true } },
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Site>({
                bind: 'stockSiteAttribute',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().stockSite);
                },
                isHidden() {
                    return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'stockSite');
                },
                valueField: { businessEntity: { composedDescription: true } },
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Site>({
                bind: 'manufacturingSiteAttribute',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().manufacturingSite);
                },
                isHidden() {
                    return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'manufacturingSite');
                },
                valueField: { businessEntity: { composedDescription: true } },
            }),
            // Other attributes
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Attribute>({
                bind: 'employeeAttribute',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().employee);
                },
                isHidden() {
                    return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'employee');
                },
                valueField: 'composedDescription',
            }),

            // Other dimensions
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension01',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty01);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType01',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension02',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty02);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType02',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension03',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty03);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType03',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension04',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty04);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType04',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension05',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty05);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType05',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension06',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty06);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType06',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension07',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty07);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType07',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension08',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty08);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType08',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension09',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty09);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType09',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension10',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty10);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType10',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension11',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty11);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType11',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension12',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty12);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType12',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension13',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty13);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType13',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension14',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty14);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType14',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension15',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty15);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType15',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension16',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty16);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType16',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension17',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty17);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType17',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension18',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty18);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType18',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension19',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty19);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType19',
                    );
                },
                valueField: 'composedDescription',
            }),
            ui.nestedFields.reference<JournalEntryInquiry, JournalEntryLine, Dimension>({
                bind: 'dimension20',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty20);
                },
                isHidden() {
                    return !this.activeDimensionTypes.some(
                        dimensionType => dimensionType.docProperty === 'dimensionType20',
                    );
                },
                valueField: 'composedDescription',
            }),
        ],
    })
    journalEntryLines: ui.fields.Table<JournalEntryLine>;

    checkDateRange(): string {
        return this.dateTo &&
            this.dateFrom &&
            Date.parse(this.dateTo.value || '') < Date.parse(this.dateFrom.value || '')
            ? ui.localize('@sage/xtrem-finance/check-date-range', 'The start date cannot be later than the end date.')
            : '';
    }

    getFilter(): Filter<JournalEntryLine> {
        const postingDate = {
            ...(this.dateFrom.value && { _gte: this.dateFrom.value }),
            ...(this.dateTo.value && { _lte: this.dateTo.value }),
        };
        const inCompany = this.companyFilter.value.map(company => company._id || '');
        const inJournal = this.journalFilter.value.map(journal => journal._id || '');

        return {
            journalEntry: { postingDate, ...(inJournal.length && { journal: { _id: { _in: inJournal } } }) },
            ...(inCompany.length && { financialSite: { legalCompany: { _id: { _in: inCompany } } } }),
        };
    }
}
