import type { AccountsPayableOpenItem as AccountsPayableOpenItemNode, GraphApi } from '@sage/xtrem-finance-api';
import type { OpenItemStatus } from '@sage/xtrem-finance-data-api';
import { paymentTracking } from '@sage/xtrem-finance-data/build/lib/menu-items/payment-tracking';
import type { Currency } from '@sage/xtrem-master-data-api';
import * as Pill<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { openDocumentLinkPage } from '../client-functions/common';
import {
    getNotFullyPaidFilter,
    getNotPaidFilter,
    getPaidFilter,
    getPartiallyPaidFilter,
    hideDiscountPenaltyBlocks,
} from '../client-functions/open-item-common';

@ui.decorators.page<AccountsPayableOpenItem, AccountsPayableOpenItemNode>({
    title: 'Accounts payable open item',
    objectTypeSingular: 'Accounts payable open item',
    objectTypePlural: 'Accounts payable open items',
    idField() {
        return this.documentNumber;
    },
    headerLabel() {
        return this.status;
    },
    menuItem: paymentTracking,
    priority: 100,
    node: '@sage/xtrem-finance/AccountsPayableOpenItem',
    module: 'finance',
    mode: 'tabs',
    navigationPanel: {
        orderBy: { dueDate: -1 },
        listItem: {
            title: ui.nestedFields.link({
                title: 'Document number',
                bind: { documentNumber: true },
                width: 'medium',
                async onClick(_id, rowItem) {
                    await openDocumentLinkPage({
                        page: this,
                        documentSysId: rowItem.documentSysId ?? 0,
                        documentType: rowItem.documentType,
                        fromFinance: true,
                    });
                },
            }),
            documentType: ui.nestedFields.dropdownList({
                bind: { documentType: true },
                title: 'Document type',
                optionType: '@sage/xtrem-finance-data/FinanceDocumentType',
            }),
            documentSysId: ui.nestedFields.technical({ bind: { documentSysId: true } }),
            line2: ui.nestedFields.reference({
                bind: { businessRelation: true },
                node: '@sage/xtrem-master-data/BaseBusinessRelation',
                title: 'Supplier name',
                valueField: 'name',
            }),
            businessEntityId: ui.nestedFields.text({
                bind: { businessRelation: { id: true } },
                title: 'Supplier ID',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({ bind: { dueDate: true }, title: 'Due date' }),
            financialSite: ui.nestedFields.reference({
                bind: { financialSite: true },
                node: '@sage/xtrem-system/Site',
                title: 'Financial site',
                valueField: 'name',
            }),
            transactionAmountDueSigned: ui.nestedFields.numeric({
                bind: { transactionAmountDueSigned: true },
                title: 'Transaction amount due',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            transactionAmountPaid: ui.nestedFields.numeric({
                bind: { transactionAmountPaidSigned: true },
                title: 'Transaction amount paid',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            remainingTransactionAmount: ui.nestedFields.numeric({
                bind: { remainingTransactionAmountSigned: true },
                title: 'Remaining transaction amount',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            currency: ui.nestedFields.reference({
                bind: { currency: true },
                node: '@sage/xtrem-master-data/Currency',
                title: 'Currency',
                valueField: 'name',
                isHiddenOnMainField: true,
            }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-finance-data/OpenItemStatus',
                style: (_id, rowValue) => PillColorCommon.getDisplayStatusPillFeatures(rowValue?.status),
            }),
            companyAmountDueSigned: ui.nestedFields.numeric({
                bind: { companyAmountDueSigned: true },
                title: 'Company amount due',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.financialSite?.legalCompany?.currency,
            }),
            companyAmountPaid: ui.nestedFields.numeric({
                bind: { companyAmountPaidSigned: true },
                title: 'Company amount paid',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.financialSite?.legalCompany?.currency,
                isHiddenOnMainField: true,
            }),
            remainingCompanyAmount: ui.nestedFields.numeric({
                bind: { remainingCompanyAmountSigned: true },
                title: 'Remaining company amount',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.financialSite?.legalCompany?.currency,
                isHiddenOnMainField: true,
            }),
            companyCurrency: ui.nestedFields.reference({
                bind: { financialSite: { legalCompany: { currency: true } } },
                node: '@sage/xtrem-master-data/Currency',
                title: 'Company currency',
                valueField: 'name',
                isHiddenOnMainField: true,
            }),
            closeReason: ui.nestedFields.text({
                bind: { closeReason: { id: true } },
                title: 'Close reason',
                isHiddenOnMainField: true,
            }),
            closeText: ui.nestedFields.text({
                bind: { closeText: true },
                title: 'Close text',
                isHiddenOnMainField: true,
            }),
            forcedAmountPaid: ui.nestedFields.numeric({
                bind: { forcedAmountPaidSigned: true },
                title: 'Forced amount paid',
                groupAggregationMethod: 'sum',
                unit: (_rowId, rowData) => rowData?.currency,
                isHiddenOnMainField: true,
            }),
        },
        optionsMenu(_graph, _storage, _queryParam, _username, _userCode, serviceOptions) {
            return Promise.resolve([
                serviceOptions.paymentTrackingOption ? getNotFullyPaidFilter(false) : undefined,
                { title: 'All statuses', graphQLFilter: { accountsPayableInvoice: { postingStatus: 'posted' } } },
                serviceOptions.paymentTrackingOption ? getNotPaidFilter(false) : undefined,
                serviceOptions.paymentTrackingOption ? getPartiallyPaidFilter(false) : undefined,
                serviceOptions.paymentTrackingOption ? getPaidFilter(false) : undefined,
            ] as ui.containers.OptionsMenuItemType<AccountsPayableOpenItemNode>[]);
        },
    },
    access: { node: '@sage/xtrem-finance/AccountsOpenItem', bind: '$create' },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            dropDownBusinessActions: [this.$standardOpenCustomizationPageWizardAction],
        });
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
        hideDiscountPenaltyBlocks(this);
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
    },
})
export class AccountsPayableOpenItem extends ui.Page<GraphApi, AccountsPayableOpenItemNode> {
    discountAmount: ui.fields.Numeric;

    penaltyAmount: ui.fields.Numeric;

    @ui.decorators.labelField<AccountsPayableOpenItem>({
        title: 'Status',
        bind: 'status',
        optionType: '@sage/xtrem-finance-data/OpenItemStatus',
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.status.value);
        },
    })
    status: ui.fields.Label<OpenItemStatus>;

    @ui.decorators.section<AccountsPayableOpenItem>({ title: 'General', isTitleHidden: true })
    generalSection: ui.containers.Section;

    @ui.decorators.textField<AccountsPayableOpenItem>({ isHidden: true })
    documentNumber: ui.fields.Text;

    @ui.decorators.textField<AccountsPayableOpenItem>({ isHidden: true })
    documentSysId: ui.fields.Text;

    @ui.decorators.referenceField<AccountsPayableOpenItem, Currency>({ isHidden: true })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.block<AccountsPayableOpenItem>({
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.fragmentFields<AccountsPayableOpenItem>({
        parent() {
            return this.generalBlock;
        },
        fragment: '@sage/xtrem-finance/OpenItemGeneral',
    })
    generalFields: ui.containers.FragmentFields;

    @ui.decorators.block<AccountsPayableOpenItem>({
        parent() {
            return this.generalSection;
        },
    })
    discountBlock: ui.containers.Block;

    @ui.decorators.fragmentFields<AccountsPayableOpenItem>({
        parent() {
            return this.discountBlock;
        },
        fragment: '@sage/xtrem-finance/OpenItemDiscount',
    })
    discountFields: ui.containers.FragmentFields;

    @ui.decorators.block<AccountsPayableOpenItem>({
        parent() {
            return this.generalSection;
        },
    })
    penaltyBlock: ui.containers.Block;

    @ui.decorators.fragmentFields<AccountsPayableOpenItem>({
        parent() {
            return this.penaltyBlock;
        },
        fragment: '@sage/xtrem-finance/OpenItemPenalty',
    })
    penaltyFields: ui.containers.FragmentFields;

    @ui.decorators.block<AccountsPayableOpenItem>({
        parent() {
            return this.generalSection;
        },
    })
    closeBlock: ui.containers.Block;

    @ui.decorators.numericField<AccountsPayableOpenItem>({
        title: 'Forced amount paid',
        width: 'medium',
        parent() {
            return this.closeBlock;
        },
        unit() {
            return this.currency?.value;
        },
        isDisabled: true,
        isHidden() {
            return this.forcedAmountPaidSigned.value === 0 || !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
    })
    forcedAmountPaidSigned: ui.fields.Numeric;

    @ui.decorators.numericField<AccountsPayableOpenItem>({
        title: 'Total amount paid',
        parent() {
            return this.closeBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isDisabled: true,
    })
    transactionAmountPaidSigned: ui.fields.Numeric;

    @ui.decorators.fragmentFields<AccountsPayableOpenItem>({
        parent() {
            return this.closeBlock;
        },
        fragment: '@sage/xtrem-finance/AccountsPayableOpenItemPayment',
    })
    paymentTable: ui.containers.FragmentFields;
}
