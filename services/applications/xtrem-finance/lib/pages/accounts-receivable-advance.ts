import type {
    AccountsReceivableAdvanceLine,
    AccountsReceivableAdvance as AccountsReceivableAdvanceNode,
    GraphApi,
} from '@sage/xtrem-finance-api';
import type { Account, Attribute, BankAccount, Dimension, DocProperty } from '@sage/xtrem-finance-data-api';
import type {
    ActiveAttributeType,
    ActiveDimensionType,
} from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import {
    attributeIDs,
    dimensionDocProperties,
    getActiveAttributes,
    getActiveDimensions,
    getAttributeTypeName,
    getDimensionTypeName,
} from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type { Currency, Item, Supplier } from '@sage/xtrem-master-data-api';
import type { Country } from '@sage/xtrem-structure-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { areFinanceIntegrationPackagesActive } from '../client-functions/finance-integration';

@ui.decorators.page<AccountsReceivableAdvance, AccountsReceivableAdvanceNode>({
    title: 'Accounts receivable advance',
    objectTypeSingular: 'Accounts receivable advance',
    objectTypePlural: 'Accounts receivable advances',
    idField() {
        return this.number;
    },
    node: '@sage/xtrem-finance/AccountsReceivableAdvance',
    module: 'finance',
    areNavigationTabsHidden: false,
    mode: 'tabs',
    navigationPanel: {
        orderBy: { postingDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.text<AccountsReceivableAdvance, AccountsReceivableAdvanceNode>({
                bind: 'number',
                title: 'Number',
            }),
            line_4: ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceNode>({
                bind: 'financialSite',
                node: '@sage/xtrem-system/Site',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Financial site',
            }),
            line7: ui.nestedFields.text<AccountsReceivableAdvance, AccountsReceivableAdvanceNode>({
                bind: 'payToCustomerName',
                title: 'Pay-to customer',
                isHiddenOnMainField: true,
            }),
            line2: ui.nestedFields.text<AccountsReceivableAdvance, AccountsReceivableAdvanceNode>({
                bind: 'description',
                title: 'Description',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date<AccountsReceivableAdvance, AccountsReceivableAdvanceNode>({
                bind: 'postingDate',
                title: 'Posting date',
            }),
            line8: ui.nestedFields.numeric<AccountsReceivableAdvance, AccountsReceivableAdvanceNode>({
                bind: 'advanceAmount',
                title: 'Advance amount',
            }),
            titleRight: ui.nestedFields.label<AccountsReceivableAdvance, AccountsReceivableAdvanceNode>({
                bind: 'postingStatus',
                title: 'Posting status',
                optionType: '@sage/xtrem-finance-data/JournalStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData.postingStatus),
            }),
            line9: ui.nestedFields.text<AccountsReceivableAdvance, AccountsReceivableAdvanceNode>({
                bind: 'reference',
                title: 'Reference',
                isHiddenOnMainField: true,
            }),
            line13: ui.nestedFields.date<AccountsReceivableAdvance, AccountsReceivableAdvanceNode>({
                bind: 'paymentDate',
                title: 'Payment date',
                isHiddenOnMainField: true,
            }),
        },
        optionsMenu: [
            { title: 'All', graphQLFilter: {} },
            { title: 'Draft', graphQLFilter: { postingStatus: { _eq: 'draft' } } },
            { title: 'Posted', graphQLFilter: { postingStatus: { _eq: 'posted' } } },
            { title: 'In progress', graphQLFilter: { postingStatus: { _eq: 'inProgress' } } },
            { title: 'Error', graphQLFilter: { postingStatus: { _eq: 'error' } } },
        ],
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    async onLoad() {
        this.activeDimensionTypes = await getActiveDimensions(this);
        this.activeAttributeTypes = await getActiveAttributes(this);
        this.fromNotificationHistory = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        if (await areFinanceIntegrationPackagesActive(this.$.graph)) {
            this.financeIntegrationAppRecordIdLink.page = this.financeIntegrationAppUrl.value || undefined; // Assign the finance integration url to the finance integration id link field.
            this.financeIntegrationAppRecordIdLink.value = this.financeIntegrationAppRecordId.value;
            this.financeIntegrationStatus.isHidden = this.postingStatus.value === this.financeIntegrationStatus.value;
            this.financeIntegrationAppRecordId.isHidden = this.financeIntegrationApp.value !== 'frp1000';
            this.financeIntegrationAppRecordIdLink.isHidden =
                !!this.financeIntegrationApp.value && this.financeIntegrationApp.value !== 'intacct';
        }

        this.$standardSaveAction.isDisabled =
            this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
        this.$standardCancelAction.isDisabled =
            this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;

        setApplicativePageCrudActions({
            page: this,
            isDirty:
                this.$.isDirty &&
                this.internalFinanceIntegrationStatus.value === 'failed' &&
                this.fromNotificationHistory,
            save: this.$standardSaveAction,
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty:
                isDirty && this.internalFinanceIntegrationStatus.value === 'failed' && this.fromNotificationHistory,
            save: this.$standardSaveAction,
        });
    },
})
export class AccountsReceivableAdvance extends ui.Page<GraphApi> {
    activeDimensionTypes: ActiveDimensionType[];

    activeAttributeTypes: ActiveAttributeType[];

    fromNotificationHistory: boolean;

    @ui.decorators.section<AccountsReceivableAdvance>({ title: 'General' }) generalSection: ui.containers.Section;

    @ui.decorators.block<AccountsReceivableAdvance>({
        parent() {
            return this.generalSection;
        },
        width: 'large',
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.textField<AccountsReceivableAdvance>({
        parent() {
            return this.generalBlock;
        },
        title: 'Number',
        isReadOnly: true,
        width: 'small',
    })
    number: ui.fields.Text;

    @ui.decorators.referenceField<AccountsReceivableAdvance, BankAccount>({
        parent() {
            return this.generalBlock;
        },
        title: 'Bank',
        valueField: 'id',
        width: 'small',
        isReadOnly: true,
        helperTextField: 'id',
        node: '@sage/xtrem-finance-data/BankAccount',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-system/Site',
                valueField: '_id',
                bind: 'financialSite',
                isHidden: true,
                columns: [ui.nestedFields.text({ bind: 'id' })],
            }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-master-data/Currency',
                valueField: '_id',
                bind: 'currency',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.text({ bind: 'decimalDigits' }),
                ],
            }),
        ],
    })
    bankAccount: ui.fields.Reference<BankAccount>;

    @ui.decorators.referenceField<AccountsReceivableAdvance, Site>({
        parent() {
            return this.generalBlock;
        },
        title: 'Financial site',
        width: 'small',
        isReadOnly: true,
        columns: [
            ui.nestedFields.reference<AccountsReceivableAdvance, Site, Company>({
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.reference<AccountsReceivableAdvance, Company, Country>({ bind: 'country' }),
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical({ bind: 'taxEngine' }),
                    ui.nestedFields.technical<AccountsReceivableAdvance, Company, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
        ],
    })
    financialSite: ui.fields.Reference<Site>;

    @ui.decorators.textField<AccountsReceivableAdvance>({
        parent() {
            return this.generalBlock;
        },
        title: 'Pay-to customer',
        isReadOnly: true,
    })
    payToCustomerName: ui.fields.Text;

    @ui.decorators.separatorField<AccountsReceivableAdvance>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isHidden: true,
    })
    fieldSeparator01: ui.fields.Separator;

    @ui.decorators.textField<AccountsReceivableAdvance>({
        parent() {
            return this.generalBlock;
        },
        title: 'Reference',
        isReadOnly: true,
    })
    reference: ui.fields.Text;

    @ui.decorators.textField<AccountsReceivableAdvance>({
        parent() {
            return this.generalBlock;
        },
        title: 'Description',
        isReadOnly: true,
    })
    description: ui.fields.Text;

    @ui.decorators.dateField<AccountsReceivableAdvance>({
        title: 'Receipt date',
        parent() {
            return this.generalBlock;
        },
        width: 'small',
        isReadOnly: true,
    })
    postingDate: ui.fields.Date;

    @ui.decorators.dateField<AccountsReceivableAdvance>({
        title: 'Payment date',
        parent() {
            return this.generalBlock;
        },
        width: 'small',
        isReadOnly: true,
    })
    paymentDate: ui.fields.Date;

    @ui.decorators.separatorField<AccountsReceivableAdvance>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isHidden: true,
    })
    fieldSeparator02: ui.fields.Separator;

    @ui.decorators.numericField<AccountsReceivableAdvance>({
        title: 'Advance amount',
        parent() {
            return this.generalBlock;
        },
        prefix() {
            return this.bankAccount?.value?.currency?.symbol ?? '';
        },
        scale() {
            return this.bankAccount?.value?.currency?.decimalDigits ?? 2;
        },
        isReadOnly: true,
    })
    advanceAmount: ui.fields.Numeric;

    @ui.decorators.labelField<AccountsReceivableAdvance>({
        parent() {
            return this.generalBlock;
        },
        title: 'Posting status',
        optionType: '@sage/xtrem-finance-data/JournalStatus',
        style() {
            return PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', this.postingStatus.value);
        },
        width: 'small',
    })
    postingStatus: ui.fields.Label;

    // Starts hidden but it can be visible in some conditions
    @ui.decorators.labelField<AccountsReceivableAdvance>({
        parent() {
            return this.generalBlock;
        },
        title: 'Accounting integration status',
        isHidden: true,
        width: 'large',
        optionType: '@sage/xtrem-finance-data/PostingStatus',
        style() {
            return PillColorFinance.getLabelColorByStatus(
                'FinanceDocumentPostingStatus',
                this.financeIntegrationStatus.value,
            );
        },
    })
    financeIntegrationStatus: ui.fields.Label;

    // Starts hidden but it can be visible in some conditions
    @ui.decorators.textField<AccountsReceivableAdvance>({
        parent() {
            return this.generalBlock;
        },
        title: 'Accounting integration reference',
        isHidden: true,
        isReadOnly: true,
        width: 'large',
    })
    financeIntegrationAppRecordId: ui.fields.Text;

    // Starts hidden but it can be visible in some conditions
    @ui.decorators.linkField<AccountsReceivableAdvance>({
        parent() {
            return this.generalBlock;
        },
        title: 'Accounting integration reference',
        width: 'large',
        isHidden: true,
        isTransient: true,
    })
    financeIntegrationAppRecordIdLink: ui.fields.Link;

    @ui.decorators.labelField<AccountsReceivableAdvance>({})
    financeIntegrationApp: ui.fields.Label;

    // Note: We need this hidden field to retrieve the URL from the node. It is assigned to the reference link field
    // in the onLoad() action.
    @ui.decorators.textField<AccountsReceivableAdvance>({})
    financeIntegrationAppUrl: ui.fields.Text;

    @ui.decorators.labelField<AccountsReceivableAdvance>({})
    internalFinanceIntegrationStatus: ui.fields.Label;

    @ui.decorators.tableField<AccountsReceivableAdvance, AccountsReceivableAdvanceLine>({
        parent() {
            return this.generalSection;
        },
        bind: 'lines',
        title: 'Lines',
        canSelect: false,
        node: '@sage/xtrem-finance/AccountsReceivableAdvanceLine',
        columns: [
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Account>({
                node: '@sage/xtrem-finance-data/Account',
                title: 'Account',
                bind: 'account',
                isReadOnly: true,
                valueField: 'composedDescription',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'composedDescription' }),
                ],
            }),
            ui.nestedFields.numeric<AccountsReceivableAdvance, AccountsReceivableAdvanceLine>({
                title: 'Amount',
                bind: 'advanceAmount',
                isReadOnly: true,
                unit: (_id, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.text<AccountsReceivableAdvance, AccountsReceivableAdvanceLine>({
                title: 'Description',
                bind: 'description',
                isReadOnly: true,
            }),
            ui.nestedFields.technical<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Currency>({
                node: '@sage/xtrem-master-data/Currency',
                bind: 'currency',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Site>({
                bind: 'financialSite',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().financialSite);
                },
                tunnelPage: '@sage/xtrem-master-data/Site',
                isHidden() {
                    return this.isAttributeHidden('financialSite');
                },
                isReadOnly: true,
                valueField: { businessEntity: { composedDescription: true } },
            }),

            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Site>({
                bind: 'businessSite',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().businessSite);
                },
                isHidden() {
                    return this.isAttributeHidden('businessSite');
                },
                tunnelPage: '@sage/xtrem-master-data/Site',
                isReadOnly: true,
                valueField: { businessEntity: { composedDescription: true } },
            }),

            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Site>({
                bind: 'stockSite',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().stockSite);
                },
                isHidden() {
                    return this.isAttributeHidden('stockSite');
                },
                tunnelPage: '@sage/xtrem-master-data/Site',
                isReadOnly: true,
                valueField: { businessEntity: { composedDescription: true } },
            }),

            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Site>({
                bind: 'manufacturingSite',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().manufacturingSite);
                },
                isHidden() {
                    return this.isAttributeHidden('manufacturingSite');
                },
                tunnelPage: '@sage/xtrem-master-data/Site',
                isReadOnly: true,
                valueField: { businessEntity: { composedDescription: true } },
            }),
            ui.nestedFields.text<AccountsReceivableAdvance, AccountsReceivableAdvanceLine>({
                bind: 'customer',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().customer);
                },
                isHidden() {
                    return this.isAttributeHidden('customer');
                },
                isReadOnly: true,
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Supplier>({
                bind: 'supplier',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().supplier);
                },
                isHidden() {
                    return this.isAttributeHidden('supplier');
                },
                node: '@sage/xtrem-master-data/Supplier',
                isReadOnly: true,
                valueField: { businessEntity: { composedDescription: true } },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Attribute>({
                bind: 'employee',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().employee);
                },
                isHidden() {
                    return this.isAttributeHidden('employee');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions('storedAttributes', 'employee', 'employee', rowValue);
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Attribute>({
                bind: 'project',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().project);
                },
                isHidden() {
                    return this.isAttributeHidden('project');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions('storedAttributes', 'project', 'project', rowValue);
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Attribute>({
                bind: 'task',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().task);
                },
                isHidden() {
                    return this.isAttributeHidden('task');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                onChange(rowValue) {
                    this.updateStoredAttributesOrDimensions('storedAttributes', 'task', 'task', rowValue);
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Item>({
                bind: 'item',
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().item);
                },
                isHidden() {
                    return this.isAttributeHidden('item');
                },
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                isReadOnly: true,
                valueField: 'composedDescription',
            }),

            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension01',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty01);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType01');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension01',
                        'dimensionType01',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension02',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty02);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType02');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension02',
                        'dimensionType02',
                        rowValue,
                    );
                },
            }),

            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension03',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty03);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType03');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension03',
                        'dimensionType03',
                        rowValue,
                    );
                },
            }),

            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension04',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty04);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType04');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension04',
                        'dimensionType04',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension05',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty05);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType05');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension05',
                        'dimensionType05',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension06',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty06);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType06');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension06',
                        'dimensionType06',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension07',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty07);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType07');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension07',
                        'dimensionType07',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension08',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty08);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType08');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension08',
                        'dimensionType08',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension09',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty09);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType09');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension09',
                        'dimensionType09',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension10',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty10);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType10');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension10',
                        'dimensionType10',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension11',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty11);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType11');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension11',
                        'dimensionType11',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension12',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty12);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType12');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension12',
                        'dimensionType12',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension13',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty13);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType13');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension13',
                        'dimensionType13',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension14',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty14);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType14');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension14',
                        'dimensionType14',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension15',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty15);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType15');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension15',
                        'dimensionType15',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension16',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty16);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType16');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension16',
                        'dimensionType16',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension17',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty17);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType17');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension17',
                        'dimensionType17',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension18',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty18);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType18');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension18',
                        'dimensionType18',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension19',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty19);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType19');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension19',
                        'dimensionType19',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.reference<AccountsReceivableAdvance, AccountsReceivableAdvanceLine, Dimension>({
                bind: 'dimension20',
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty20);
                },
                isHidden() {
                    return this.isDimensionHidden('dimensionType20');
                },
                valueField: 'composedDescription',
                isReadOnly() {
                    return this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                onChange(_rowId, rowValue) {
                    this.updateStoredAttributesOrDimensions(
                        'storedDimensions',
                        'dimension20',
                        'dimensionType20',
                        rowValue,
                    );
                },
            }),
            ui.nestedFields.technical<AccountsReceivableAdvance, AccountsReceivableAdvanceLine>({
                bind: 'storedAttributes',
            }),
            ui.nestedFields.technical<AccountsReceivableAdvance, AccountsReceivableAdvanceLine>({
                bind: 'storedDimensions',
            }),
        ],
    })
    lines: ui.fields.Table<AccountsReceivableAdvanceLine>;

    updateStoredAttributesOrDimensions(
        attributeOrDimension: string,
        attributeOrDimensionName: string,
        attributeOrDimensionType: string,
        rowValue: any,
    ) {
        const storedAttributeOrDimensions = JSON.parse(rowValue[attributeOrDimension]);
        if (rowValue[attributeOrDimensionName]) {
            storedAttributeOrDimensions[attributeOrDimensionType] = rowValue[attributeOrDimensionName].id;
        } else {
            delete storedAttributeOrDimensions[attributeOrDimensionType];
        }
        rowValue[attributeOrDimension] = JSON.stringify(storedAttributeOrDimensions);
        this.lines.addOrUpdateRecordValue(rowValue);
    }

    isAttributeHidden(attribute: string): boolean {
        return !this.activeAttributeTypes.some(attributeType => attributeType.id === attribute);
    }

    isDimensionHidden(dimensionType: DocProperty): boolean {
        return !this.activeDimensionTypes.some(
            activeDimensionType => activeDimensionType.docProperty === dimensionType,
        );
    }
}
