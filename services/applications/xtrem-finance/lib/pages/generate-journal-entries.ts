import type { Filter } from '@sage/xtrem-client';
import type { GraphApi } from '@sage/xtrem-finance-api';
import type { AccountingStaging } from '@sage/xtrem-finance-data-api';
import { finance } from '@sage/xtrem-master-data/build/lib/menu-items/finance';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/lib/client-functions/get-default';
import type { GraphApi as SchedulerGraphApi } from '@sage/xtrem-scheduler-api';
import { scheduleWizard } from '@sage/xtrem-scheduler/build/lib/client-functions/job-schedule';
import type { Company, Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

const once = ui.localize('@sage/xtrem-finance/pages__generate_journal_entries__run_once', 'Run once');
const recurring = ui.localize('@sage/xtrem-finance/pages__generate_journal_entries__recurring', 'Recurring');

@ui.decorators.page<GenerateJournalEntries>({
    title: 'Generate journal entries',
    menuItem: finance,
    priority: 300,
    isTransient: true,
    module: 'finance',
    skipDirtyCheck: true,
    access: { node: '@sage/xtrem-finance/AccountingInterfaceListener', bind: 'createJournalsFromAccountingStagingJob' },
    async onLoad() {
        await setReferenceIfSingleValue([this.financialSite]);
    },
})
export class GenerateJournalEntries extends ui.Page<GraphApi> {
    @ui.decorators.section<GenerateJournalEntries>({ title: 'General', isTitleHidden: true })
    generalSection: ui.containers.Section;

    @ui.decorators.block<GenerateJournalEntries>({
        parent() {
            return this.generalSection;
        },
        width: 'large',
        title: 'Criteria',
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.referenceField<GenerateJournalEntries, Site>({
        parent() {
            return this.generalBlock;
        },
        title: 'Financial site',
        node: '@sage/xtrem-system/Site',
        valueField: 'name',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.technical({ bind: 'isFinance' }),
            ui.nestedFields.reference<GenerateJournalEntries, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id', isHidden: true })],
            }),
        ],
        filter: { isActive: { _eq: true }, isFinance: { _eq: true } },
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select financial site',
        helperTextField: 'id',
        placeholder: 'Select site',
        width: 'small',
        isMandatory: false,
    })
    financialSite: ui.fields.Reference;

    @ui.decorators.dateField<GenerateJournalEntries>({
        parent() {
            return this.generalBlock;
        },
        title: 'Start date',
        width: 'small',
        validation() {
            return this.checkDateRange();
        },
    })
    dateFrom: ui.fields.Date;

    @ui.decorators.dateField<GenerateJournalEntries>({
        parent() {
            return this.generalBlock;
        },
        title: 'End date',
        width: 'small',
        validation() {
            return this.checkDateRange();
        },
    })
    dateTo: ui.fields.Date;

    @ui.decorators.multiDropdownField<GenerateJournalEntries>({
        parent() {
            return this.generalBlock;
        },
        title: 'Document type',
        width: 'small',
        optionType: '@sage/xtrem-finance-data/FinanceDocumentType',
    })
    documentType: ui.fields.MultiDropdown;

    @ui.decorators.buttonField<GenerateJournalEntries>({
        parent() {
            return this.generalBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-finance/pages__generate_journal_entries__create_button_text', 'Generate');
        },
        width: 'small',
        async onClick() {
            this.$.loader.isHidden = false;
            const filter: string = JSON.stringify(this.getFilter());
            const result = await this.$.graph
                .node('@sage/xtrem-finance/AccountingInterfaceListener')
                .asyncOperations.createJournalsFromAccountingStagingJob.runToCompletion(true, {
                    journalsCreatedData: false,
                    filter,
                })
                .execute();
            this.$.loader.isHidden = true;
            this.$.showToast(result);
        },
    })
    create: ui.fields.Button;

    @ui.decorators.buttonField<GenerateJournalEntries>({
        parent() {
            return this.generalBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-finance/pages__generate_journal_entries__schedule_button_text', 'Schedule');
        },
        width: 'small',
        async onClick() {
            const filter = JSON.stringify(this.getFilter());
            /** as unknown as graphApi ... because actualy we are not able to import api of the package only  */
            await scheduleWizard(this as unknown as ui.Page<SchedulerGraphApi>, {
                jobSchedule: [once, recurring],
                filter,
                operationKey: 'AccountingInterfaceListener|createJournalsFromAccountingStagingJob|start',
                additionalParameters: { journalsCreatedData: false },
                isOperationHidden: true,
                isParametersHidden: true,
            });
        },
    })
    schedule: ui.fields.Button;

    checkDateRange(): string {
        return this.dateTo.value &&
            this.dateFrom.value &&
            Date.parse(this.dateTo.value) < Date.parse(this.dateFrom.value)
            ? ui.localize(
                  '@sage/xtrem-finance/generate-journal-entry-date',
                  'The start date cannot be later than the end date.',
              )
            : '';
    }

    getFilter(): Filter<AccountingStaging> {
        const _and: Filter<AccountingStaging>[] = [];

        if (this.dateFrom.value) {
            _and.push({ documentDate: { _gte: this.dateFrom.value } });
        }

        if (this.dateTo.value) {
            _and.push({ documentDate: { _lte: this.dateTo.value } });
        }

        if (this.documentType.value && this.documentType.value.length > 0) {
            const orFilter = { _or: [{}] };
            this.documentType.value.map(value => orFilter._or.push({ documentType: { _eq: value } }));
            orFilter._or.splice(0, 1);
            _and.push(orFilter);
        }

        if (this.financialSite.value) {
            _and.push({ financialSite: { id: { _eq: this.financialSite.value.id.valueOf() } } });
        }
        if (_and.length === 0) {
            return {};
        }
        if (_and.length === 1) {
            return _and[0];
        }
        return { _and };
    }
}
