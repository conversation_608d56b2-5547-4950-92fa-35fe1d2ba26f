import type { GraphApi, Payment as PaymentNode } from '@sage/xtrem-finance-api';
import type { BankAccount } from '@sage/xtrem-finance-data-api';
import { paymentTracking } from '@sage/xtrem-finance-data/build/lib/menu-items/payment-tracking';
import type { BaseBusinessRelation, Currency } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { openBasePaymentPage } from '../client-functions/common';
import type { AccountsPayableOpenItemRecord } from '../client-functions/interfaces/record';
import { resetLines, validateAmounts } from '../client-functions/record-common';
import { createPayment, getResults, initRecordPayment } from '../client-functions/record-payment';

@ui.decorators.page<RecordPayment, PaymentNode>({
    menuItem: paymentTracking,
    priority: 100,
    title: 'Record payment',
    module: 'finance',
    mode: 'default',
    node: '@sage/xtrem-finance/Payment',
    navigationPanel: undefined,
    skipDirtyCheck: true,
    headerSection() {
        return this.mainSection;
    },
    businessActions() {
        return [this.generateButton];
    },
    async onLoad() {
        await initRecordPayment(this);
    },
})
export class RecordPayment extends ui.Page<GraphApi> {
    @ui.decorators.section<RecordPayment>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<RecordPayment>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Payment information',
    })
    paymentInformationBlock: ui.containers.Block;

    @ui.decorators.fragmentFields<RecordPayment>({
        isTitleHidden: true,
        parent() {
            return this.paymentInformationBlock;
        },
        fragment: '@sage/xtrem-finance/PaymentInformation',
    })
    paymentFields: ui.containers.FragmentFields;

    financialSite: ui.fields.Reference<Site>;

    currency: ui.fields.Reference<Currency>;

    amount: ui.fields.Numeric;

    type: ui.fields.Radio;

    date: ui.fields.Date;

    customer: ui.fields.Reference<BaseBusinessRelation>;

    supplier: ui.fields.Reference<BaseBusinessRelation>;

    amountBankCurrency: ui.fields.Numeric;

    bankAccount: ui.fields.Reference<BankAccount>;

    paymentMethod: ui.fields.DropdownList;

    reference: ui.fields.Text;

    @ui.decorators.section<RecordPayment>({ title: 'Lines', isTitleHidden: true })
    linesSection: ui.containers.Section;

    @ui.decorators.buttonField<RecordPayment>({
        parent() {
            return this.paymentInformationBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-finance/search', 'Search');
        },
        isHidden() {
            return !!this.$.queryParameters.openParams;
        },
        width: 'small',
        async onClick() {
            await this.paymentInformationBlock.validate();

            if (await this.paymentInformationBlock.isValid) {
                this.$.loader.isHidden = false;
                await this.search();
                this.$.loader.isHidden = true;
            }
        },
    })
    searchButton: ui.fields.Button;

    @ui.decorators.tile<RecordPayment>({
        parent() {
            return this.mainSection;
        },
        isHidden() {
            return !!this.$.queryParameters.openParams;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.numericField<RecordPayment>({
        parent() {
            return this.tileContainer;
        },
        title: 'Total payment applied',
        unit() {
            return this.currency.value;
        },
    })
    totalPaymentApplied: ui.fields.Numeric;

    @ui.decorators.numericField<RecordPayment>({
        parent() {
            return this.tileContainer;
        },
        title: 'Amount available to apply',
        unit() {
            return this.currency.value;
        },
    })
    amountAvailableToApply: ui.fields.Numeric;

    @ui.decorators.block<RecordPayment>({
        parent() {
            return this.linesSection;
        },
        width: 'extra-large',
        title: 'Results',
    })
    linesBlock: ui.containers.Block;

    @ui.decorators.fragmentFields<RecordPayment>({
        isTitleHidden: true,
        parent() {
            return this.linesBlock;
        },
        fragment: '@sage/xtrem-finance/RecordLines',
    })
    linesField: ui.containers.FragmentFields;

    lines: ui.fields.Table<AccountsPayableOpenItemRecord>;

    @ui.decorators.pageAction<RecordPayment>({
        title: 'Generate',
        async onClick() {
            await this.paymentInformationBlock.validate();

            if (await this.paymentInformationBlock.isValid) {
                if (!validateAmounts(this)) {
                    return;
                }
                let paymentNumber: string | null = null;
                if (!this.$.queryParameters.openParams) {
                    paymentNumber = await openBasePaymentPage(this);
                    if (typeof paymentNumber === 'string') {
                        this.$.showToast(
                            ui.localize(
                                '@sage/xtrem-finance/pages__record_payment__payment_created',
                                'Payment created: {{paymentNumber}}.',
                                { paymentNumber },
                            ),
                            { timeout: 10000 },
                        );
                        resetLines(this);
                        this.generateButton.isHidden = true;
                    }
                } else {
                    paymentNumber = await createPayment(this);
                    this.$.finish(paymentNumber);
                }
            }
        },
    })
    generateButton: ui.PageAction;

    async search() {
        resetLines(this);
        this.lines.value = await getResults(this);
        this.generateButton.isHidden = false;
    }
}
