import { extractEdges } from '@sage/xtrem-client';
import type {
    GraphApi,
    JournalEntryLine,
    JournalEntryLineBinding,
    JournalEntryLineDimension,
    JournalEntry as JournalEntryNode,
} from '@sage/xtrem-finance-api';
import type { Account, Attribute, Dimension, Journal } from '@sage/xtrem-finance-data-api';
import type {
    ActiveAttributeType,
    ActiveDimensionType,
} from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import {
    attributeIDs,
    dimensionDocProperties,
    getActiveAttributes,
    getActiveDimensions,
    getAttributeTypeName,
    getDimensionTypeName,
} from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as FinanceDataCommon from '@sage/xtrem-finance-data/build/lib/client-functions/common';
// Had to do this \(override from /lib/interfaces in /client-functions/interfaces\) to avoid using server entities (from /lib/nodes or enums etc) from some of the interface properties
import type { FinanceOriginPostingStatusData } from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/accounting-integration';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type { BusinessEntity, Currency, Customer, Item, Supplier } from '@sage/xtrem-master-data-api';
import { finance } from '@sage/xtrem-master-data/build/lib/menu-items/finance';
import { getLegislationOptionsMenu } from '@sage/xtrem-structure/build/lib/client-functions/options-menu';
import type { Site } from '@sage/xtrem-system-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import type { Tax } from '@sage/xtrem-tax-api';
import * as ui from '@sage/xtrem-ui';
import { isFinanceOriginPostingStatusData, taxDetailsJournalEntry } from '../client-functions/common';
import { areFinanceIntegrationPackagesActive } from '../client-functions/finance-integration';

@ui.decorators.page<JournalEntry, JournalEntryNode>({
    title: 'Journal entry',
    objectTypeSingular: 'Journal entry',
    objectTypePlural: 'Journal entries',
    idField() {
        return this.number;
    },
    menuItem: finance,
    priority: 400,
    node: '@sage/xtrem-finance/JournalEntry',
    module: 'finance',
    mode: 'tabs',
    areNavigationTabsHidden: false,
    navigationPanel: {
        orderBy: { postingDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            line2: ui.nestedFields.text({ bind: 'description' }),
            line2Right: ui.nestedFields.date({ bind: 'postingDate', title: 'Posting date' }),
            line3: ui.nestedFields.reference({
                bind: 'financialSite',
                node: '@sage/xtrem-system/Site',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Financial site',
            }),
            line_4: ui.nestedFields.reference({
                bind: 'journal',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Journal',
            }),
            titleRight: ui.nestedFields.label({
                bind: 'postingStatus',
                title: 'Posting status',
                optionType: '@sage/xtrem-finance-data/JournalStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            line_5: ui.nestedFields.text({ bind: 'documentType', title: 'Document type', isHiddenOnMainField: true }),
            line6: ui.nestedFields.text({
                bind: 'reference',
                title: 'Reference',
                isHiddenOnMainField: true,
            }),
            line7: ui.nestedFields.dropdownList({
                bind: 'origin',
                title: 'Origin',
                optionType: '@sage/xtrem-finance-data/AccountsPayableReceivableInvoiceOrigin',
                isHiddenOnMainField: true,
            }),
        },
        optionsMenu(graph) {
            return getLegislationOptionsMenu(graph, {
                openFilter: '{ "financialSite": { "legalCompany": { "legislation": { "_id": { "_eq": "',
                closeFilter: '" } } } } }',
            });
        },
    },
    businessActions() {
        return [this.$standardSaveAction];
    },
    async onLoad() {
        this.activeDimensionTypes = await getActiveDimensions(this);
        this.activeAttributeTypes = await getActiveAttributes(this);
        this.fromNotificationHistory = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        if (await areFinanceIntegrationPackagesActive(this.$.graph)) {
            this.financeIntegrationAppRecordIdLink.page = this.financeIntegrationAppUrl.value ?? undefined; // Assign the finance integration url to the finance integration id link field.
            this.financeIntegrationAppRecordIdLink.value = this.financeIntegrationAppRecordId.value;
            this.financeIntegrationStatus.isHidden = this.postingStatus.value === this.financeIntegrationStatus.value;
            this.financeIntegrationAppRecordId.isHidden = this.financeIntegrationApp.value !== 'frp1000';
            this.financeIntegrationAppRecordIdLink.isHidden =
                this.financeIntegrationApp.value !== null && this.financeIntegrationApp.value !== 'intacct';
        }
        this.$standardSaveAction.isDisabled =
            this.internalFinanceIntegrationStatus.value !== 'failed' || !this.fromNotificationHistory;
        await this.initPosting();
        setApplicativePageCrudActions({
            page: this,
            isDirty:
                this.$.isDirty &&
                this.internalFinanceIntegrationStatus.value === 'failed' &&
                this.fromNotificationHistory,
            save: this.$standardSaveAction,
        });
    },

    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty:
                isDirty && this.internalFinanceIntegrationStatus.value === 'failed' && this.fromNotificationHistory,
            save: this.$standardSaveAction,
        });
    },
})
export class JournalEntry extends ui.Page<GraphApi> {
    activeDimensionTypes: ActiveDimensionType[];

    activeAttributeTypes: ActiveAttributeType[];

    fromNotificationHistory: boolean;

    @ui.decorators.section<JournalEntry>({
        title: 'General',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<JournalEntry>({
        parent() {
            return this.generalSection;
        },
        width: 'extra-large',
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.textField<JournalEntry>({ title: 'ID', isReadOnly: true }) _id: ui.fields.Text;

    @ui.decorators.textField<JournalEntry>({
        parent() {
            return this.generalBlock;
        },
        title: 'Number',
        isReadOnly: true,
    })
    number: ui.fields.Text;

    @ui.decorators.dateField<JournalEntry>({
        title: 'Posting date',
        parent() {
            return this.generalBlock;
        },
        width: 'medium',
        isReadOnly: true,
    })
    postingDate: ui.fields.Date;

    @ui.decorators.referenceField<JournalEntry, Journal>({
        parent() {
            return this.generalBlock;
        },
        title: 'Journal',
        node: '@sage/xtrem-finance-data/Journal',
        tunnelPage: '@sage/xtrem-finance-data/Journal',
        valueField: 'name',
        isReadOnly: true,
        helperTextField: 'id',
    })
    journal: ui.fields.Reference<Journal>;

    @ui.decorators.textField<JournalEntry>({
        parent() {
            return this.generalBlock;
        },
        title: 'Document type',
        width: 'large',
        isMandatory() {
            return this.financialSite.value ? this.financialSite.value.legalCompany?.legislation?.id === 'FR' : false;
        },
        isReadOnly: true,
    })
    documentType: ui.fields.Text;

    @ui.decorators.separatorField<JournalEntry>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    fieldSeparator01: ui.fields.Separator;

    @ui.decorators.referenceField<JournalEntry, Site>({
        parent() {
            return this.generalBlock;
        },
        title: 'Financial site',
        width: 'medium',
        isReadOnly: true,
        columns: [
            ui.nestedFields.reference({
                node: '@sage/xtrem-system/Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                minLookupCharacters: 0,
                lookupDialogTitle: 'Select company',
                columns: [ui.nestedFields.reference({ bind: 'legislation' })],
            }),
        ],
    })
    financialSite: ui.fields.Reference<Site>;

    @ui.decorators.textField<JournalEntry>({
        parent() {
            return this.generalBlock;
        },
        title: 'Description',
        isReadOnly: true,
    })
    description: ui.fields.Text;

    @ui.decorators.textField<JournalEntry>({
        parent() {
            return this.generalBlock;
        },
        title: 'Reference',
        isReadOnly: true,
    })
    reference: ui.fields.Text;

    @ui.decorators.dropdownListField<JournalEntry>({
        parent() {
            return this.generalBlock;
        },
        title: 'Origin',
        optionType: '@sage/xtrem-finance-data/JournalOrigin',
        isReadOnly: true,
    })
    origin: ui.fields.DropdownList;

    @ui.decorators.separatorField<JournalEntry>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    fieldSeparator02: ui.fields.Separator;

    @ui.decorators.labelField<JournalEntry>({})
    internalFinanceIntegrationStatus: ui.fields.Label;

    @ui.decorators.labelField<JournalEntry>({
        parent() {
            return this.generalBlock;
        },
        title: 'Posting status',
        optionType: '@sage/xtrem-finance-data/JournalStatus',
        style() {
            return PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', this.postingStatus.value);
        },
        width: 'small',
    })
    postingStatus: ui.fields.Label;

    // Starts hidden but it can be visible in some conditions
    @ui.decorators.labelField<JournalEntry>({
        parent() {
            return this.generalBlock;
        },
        title: 'Accounting integration status',
        isHidden: true,
        width: 'large',
        optionType: '@sage/xtrem-finance-data/PostingStatus',
        style() {
            return PillColorFinance.getLabelColorByStatus(
                'FinanceDocumentPostingStatus',
                this.financeIntegrationStatus.value,
            );
        },
    })
    financeIntegrationStatus: ui.fields.Label;

    // Starts hidden but it can be visible in some conditions
    @ui.decorators.textField<JournalEntry>({
        parent() {
            return this.generalBlock;
        },
        title: 'Accounting integration reference',
        isHidden: true,
        isReadOnly: true,
        width: 'large',
    })
    financeIntegrationAppRecordId: ui.fields.Text;

    // Starts hidden but it can be visible in some conditions
    @ui.decorators.linkField<JournalEntry>({
        parent() {
            return this.generalBlock;
        },
        title: 'Accounting integration reference',
        width: 'large',
        isHidden: true,
        isTransient: true,
    })
    financeIntegrationAppRecordIdLink: ui.fields.Link;

    @ui.decorators.labelField<JournalEntry>({})
    financeIntegrationApp: ui.fields.Label;

    // Note: We need this hidden field to retrieve the URL from the node. It is assigned to the reference link field
    // in the onLoad() action.
    @ui.decorators.textField<JournalEntry>({})
    financeIntegrationAppUrl: ui.fields.Text;

    @ui.decorators.nestedGridField<JournalEntry, [JournalEntryLine, JournalEntryLineDimension]>({
        parent() {
            return this.generalSection;
        },
        bind: 'lines',
        title: 'Lines',
        canSelect: false,
        levels: [
            {
                node: '@sage/xtrem-finance/JournalEntryLine',
                orderBy: { _sortValue: +1 },
                childProperty: 'attributesAndDimensions',
                columns: [
                    ui.nestedFields.reference<JournalEntry, JournalEntryLine, Site>({
                        title: 'Financial site',
                        bind: 'financialSite',
                        isReadOnly: true,
                        valueField: 'name',
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLine, Account>({
                        node: '@sage/xtrem-finance-data/Account',
                        title: 'Account',
                        bind: 'account',
                        isReadOnly: true,
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                            ui.nestedFields.text({ bind: 'composedDescription', isHidden: true }),
                        ],
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLine, BusinessEntity>({
                        node: '@sage/xtrem-master-data/BusinessEntity',
                        tunnelPage: '@sage/xtrem-master-data/BusinessEntity',
                        title: 'Business entity',
                        bind: 'businessEntity',
                        isReadOnly: true,
                        valueField: 'name',
                    }),
                    ui.nestedFields.technical<JournalEntry, JournalEntryLine, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'transactionCurrency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.numeric<JournalEntry, JournalEntryLineBinding>({
                        title: 'Transaction debit',
                        isReadOnly: true,
                        bind: 'transactionDebit',
                        unit: (_id, rowData) => rowData?.transactionCurrency,
                    }),
                    ui.nestedFields.numeric<JournalEntry, JournalEntryLineBinding>({
                        title: 'Transaction credit',
                        isReadOnly: true,
                        bind: 'transactionCredit',
                        unit: (_id, rowData) => rowData?.transactionCurrency,
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLine, Tax>({
                        node: '@sage/xtrem-tax/Tax',
                        title: 'Tax',
                        bind: 'tax',
                        isReadOnly: true,
                        valueField: 'name',
                    }),
                    ui.nestedFields.date<JournalEntry, JournalEntryLineBinding>({
                        title: 'Tax date',
                        isReadOnly: true,
                        bind: 'taxDate',
                    }),
                    ui.nestedFields.date<JournalEntry, JournalEntryLine>({
                        title: 'Due date',
                        isReadOnly: true,
                        bind: 'dueDate',
                    }),
                    ui.nestedFields.text<JournalEntry, JournalEntryLine>({
                        title: 'Contra account',
                        isReadOnly: true,
                        bind: 'contraAccount',
                    }),
                    ui.nestedFields.text<JournalEntry, JournalEntryLine>({
                        title: 'Description',
                        bind: 'description',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text<JournalEntry, JournalEntryLine>({
                        title: 'Common reference',
                        bind: 'commonReference',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric<JournalEntry, JournalEntryLineBinding>({
                        title: 'Company debit',
                        isReadOnly: true,
                        bind: 'companyDebit',
                        prefix(_value, rowData) {
                            return rowData?.companyCurrency?.symbol ?? '';
                        },
                        scale(_value, rowData) {
                            return rowData?.companyCurrency?.decimalDigits ?? 2;
                        },
                    }),
                    ui.nestedFields.numeric<JournalEntry, JournalEntryLineBinding>({
                        title: 'Company credit',
                        isReadOnly: true,
                        bind: 'companyCredit',
                        prefix(_value, rowData) {
                            return rowData?.companyCurrency?.symbol ?? '';
                        },
                        scale(_value, rowData) {
                            return rowData?.companyCurrency?.decimalDigits ?? 2;
                        },
                    }),
                    ui.nestedFields.text<JournalEntry, JournalEntryLine>({
                        title: 'Exchange rate',
                        bind: 'rateDescription',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.date<JournalEntry, JournalEntryLine>({
                        title: 'Exchange rate date',
                        bind: 'fxRateDate',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.technical<JournalEntry, JournalEntryLine, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'companyCurrency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.reference<JournalEntry>({
                        bind: 'baseTax',
                        node: '@sage/xtrem-tax/BaseTax',
                        valueField: 'tax',
                        isHidden: true,
                        columns: [
                            ui.nestedFields.reference({
                                node: '@sage/xtrem-master-data/Currency',
                                bind: 'currency',
                                valueField: 'id',
                                isHidden: true,
                                columns: [
                                    ui.nestedFields.text({ bind: 'name' }),
                                    ui.nestedFields.text({ bind: 'id' }),
                                    ui.nestedFields.text({ bind: 'symbol' }),
                                    ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                                ],
                            }),
                            ui.nestedFields.text({ bind: 'taxCategory' }),
                            ui.nestedFields.text({ bind: 'tax' }),
                            ui.nestedFields.numeric({ bind: 'taxableAmount' }),
                            ui.nestedFields.numeric({ bind: 'nonTaxableAmount' }),
                            ui.nestedFields.numeric({ bind: 'taxAmount' }),
                            ui.nestedFields.numeric({ bind: 'taxAmountAdjusted' }),
                            ui.nestedFields.numeric({ bind: 'taxRate' }),
                            ui.nestedFields.numeric({ bind: 'exemptAmount' }),
                            ui.nestedFields.numeric({ bind: 'deductibleTaxAmount' }),
                            ui.nestedFields.numeric({ bind: 'deductibleTaxRate' }),
                            ui.nestedFields.checkbox({ bind: 'isReverseCharge' }),
                        ],
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'money_bag',
                        title: 'Tax details',
                        isHidden(_rowId, rowItem) {
                            return !rowItem.baseTax;
                        },
                        async onClick(_recordId: string, rowItem: JournalEntryLine) {
                            await taxDetailsJournalEntry({ page: this, rowItem });
                        },
                    },
                ],
            },
            {
                node: '@sage/xtrem-finance/JournalEntryLineDimension',
                columns: [
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Site>({
                        bind: 'financialSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().financialSite);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes
                                .map(attributeType => attributeType.id)
                                .includes('financialSite');
                        },
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.numeric<JournalEntry, JournalEntryLineDimension>({
                        title: 'Transaction amount',
                        bind: 'transactionAmount',
                        isReadOnly: true,
                        unit: (_id, rowData) => rowData?.transactionCurrency,
                    }),
                    ui.nestedFields.technical<JournalEntry, JournalEntryLineDimension, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'transactionCurrency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Supplier>({
                        bind: 'supplier',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().supplier);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes
                                .map(attributeType => attributeType.id)
                                .includes('supplier');
                        },
                        node: '@sage/xtrem-master-data/Supplier',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Customer>({
                        bind: 'customer',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().customer);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes
                                .map(attributeType => attributeType.id)
                                .includes('customer');
                        },
                        node: '@sage/xtrem-master-data/Customer',
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Item>({
                        bind: 'item',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().item);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.map(attributeType => attributeType.id).includes('item');
                        },
                        node: '@sage/xtrem-master-data/Item',
                        tunnelPage: '@sage/xtrem-master-data/Item',
                        isReadOnly: true,
                        valueField: 'composedDescription',
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Attribute>({
                        bind: 'project',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().project);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes
                                .map(attributeType => attributeType.id)
                                .includes('project');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions('storedAttributes', 'project', 'project', rowValue);
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Attribute>({
                        bind: 'task',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().task);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes.map(attributeType => attributeType.id).includes('task');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions('storedAttributes', 'task', 'task', rowValue);
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Site>({
                        bind: 'businessSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().businessSite);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes
                                .map(attributeType => attributeType.id)
                                .includes('businessSite');
                        },
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Site>({
                        bind: 'stockSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().stockSite);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes
                                .map(attributeType => attributeType.id)
                                .includes('stockSite');
                        },
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Site>({
                        bind: 'manufacturingSite',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().manufacturingSite);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes
                                .map(attributeType => attributeType.id)
                                .includes('manufacturingSite');
                        },
                        isReadOnly: true,
                        valueField: { businessEntity: { composedDescription: true } },
                    }),

                    // Other attributes
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Attribute>({
                        bind: 'employee',
                        title() {
                            return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().employee);
                        },
                        isHidden() {
                            return !this.activeAttributeTypes
                                .map(attributeType => attributeType.id)
                                .includes('employee');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedAttributes',
                                'employee',
                                'employee',
                                rowValue,
                            );
                        },
                    }),

                    // Other dimensions
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension01',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty01,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType01');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension01',
                                'dimensionType01',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension02',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty02,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType02');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension02',
                                'dimensionType02',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension03',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty03,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType03');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension03',
                                'dimensionType03',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension04',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty04,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType04');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension04',
                                'dimensionType04',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension05',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty05,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType05');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension05',
                                'dimensionType05',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension06',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty06,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType06');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension06',
                                'dimensionType06',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension07',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty07,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType07');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension07',
                                'dimensionType07',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension08',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty08,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType08');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension08',
                                'dimensionType08',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension09',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty09,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType09');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension09',
                                'dimensionType09',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension10',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty10,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType10');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension10',
                                'dimensionType10',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension11',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty11,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType11');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension11',
                                'dimensionType11',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension12',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty12,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType12');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension12',
                                'dimensionType12',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension13',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty13,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType13');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension13',
                                'dimensionType13',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension14',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty14,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType14');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension14',
                                'dimensionType14',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension15',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty15,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType15');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension15',
                                'dimensionType15',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension16',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty16,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType16');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension16',
                                'dimensionType16',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension17',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty17,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType17');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension17',
                                'dimensionType17',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension18',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty18,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType18');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension18',
                                'dimensionType18',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension19',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty19,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType19');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension19',
                                'dimensionType19',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.reference<JournalEntry, JournalEntryLineDimension, Dimension>({
                        bind: 'dimension20',
                        title() {
                            return getDimensionTypeName(
                                this.activeDimensionTypes,
                                dimensionDocProperties().docProperty20,
                            );
                        },
                        isHidden() {
                            return !this.activeDimensionTypes
                                .map(dimensionType => dimensionType.docProperty)
                                .includes('dimensionType20');
                        },
                        isReadOnly() {
                            return (
                                this.internalFinanceIntegrationStatus.value !== 'failed' ||
                                !this.fromNotificationHistory
                            );
                        },
                        valueField: 'composedDescription',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                        ],
                        onChange(_rowId, rowValue) {
                            this.updateStoredAttributesOrDimensions(
                                'storedDimensions',
                                'dimension20',
                                'dimensionType20',
                                rowValue,
                            );
                        },
                    }),
                    ui.nestedFields.numeric<JournalEntry, JournalEntryLineDimension>({
                        title: 'Company amount',
                        isReadOnly: true,
                        bind: 'companyAmount',
                        prefix(_value, rowData) {
                            return rowData?.companyCurrency?.symbol ?? '';
                        },
                        scale(_value, rowData) {
                            return rowData?.companyCurrency?.decimalDigits ?? 2;
                        },
                    }),
                    ui.nestedFields.technical<JournalEntry, JournalEntryLineDimension, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'companyCurrency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical<JournalEntry, JournalEntryLineDimension>({
                        bind: 'storedAttributes',
                    }),
                    ui.nestedFields.technical<JournalEntry, JournalEntryLineDimension>({
                        bind: 'storedDimensions',
                    }),
                    ui.nestedFields.technical<JournalEntry, JournalEntryLineDimension, JournalEntryLine>({
                        bind: 'journalEntryLine',
                        nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                    }),
                ],
            },
        ],
    })
    lines: ui.fields.NestedGrid;

    // Tab for posting information (accounting integration)
    @ui.decorators.section<JournalEntry>({
        title: 'Posting',
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<JournalEntry>({
        parent() {
            return this.postingSection;
        },
        width: 'large',
        title: 'Posting',
        isTitleHidden: true,
    })
    postingSectionBlock: ui.containers.Block;

    @ui.decorators.block<JournalEntry>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.tableField<JournalEntry, FinanceOriginPostingStatusData>({
        title: 'Posting',
        isTitleHidden: true,
        canSelect: false,
        isTransient: true,
        canUserHideColumns: false,
        isHelperTextHidden: true,
        pageSize: 10,
        parent() {
            return this.postingSectionBlock;
        },
        columns: [
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'documentType',
                optionType: '@sage/xtrem-finance-data/FinanceDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'documentNumber',
                onClick(_rowId, rowData) {
                    if (isFinanceOriginPostingStatusData(rowData)) {
                        const link = FinanceDataCommon.getPage(rowData.documentType);
                        if (link) {
                            this.$.router.goTo(link, { _id: rowData.documentSysId }, true);
                        }
                    }
                },
            }),
            ui.nestedFields.text({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
            ui.nestedFields.technical({ bind: 'financeIntegrationAppUrl' }),
            ui.nestedFields.technical({ bind: 'documentSysId' }),
        ],
        onRowClick(_id: string, rowData: FinanceOriginPostingStatusData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
    })
    postingDetails: ui.fields.Table<FinanceOriginPostingStatusData>;

    @ui.decorators.textAreaField<JournalEntry>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    updateStoredAttributesOrDimensions(
        attributeOrDimension: string,
        attributeOrDimensionName: string,
        attributeOrDimensionType: string,
        rowValue: any,
    ) {
        const storedAttributeOrDimensions = JSON.parse(rowValue[attributeOrDimension]);
        if (rowValue[attributeOrDimensionName]) {
            storedAttributeOrDimensions[attributeOrDimensionType] = rowValue[attributeOrDimensionName].id;
        } else {
            delete storedAttributeOrDimensions[attributeOrDimensionType];
        }
        rowValue[attributeOrDimension] = JSON.stringify(storedAttributeOrDimensions);
        this.lines.addOrUpdateRecordValue(rowValue, 1, rowValue.journalEntryLine._id);
    }

    async initPosting() {
        this.postingDetails.value = [];
        if (this._id.value) {
            this.postingDetails.value = extractEdges(
                await this.$.graph
                    .node('@sage/xtrem-finance-data/FinanceTransaction')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                _id: true,
                                documentType: true,
                                documentNumber: true,
                                documentSysId: true,
                                postingStatus: true,
                                message: true,
                                financeIntegrationAppRecordId: true,
                                financeIntegrationAppUrl: true,
                            },
                            { filter: { targetDocumentSysId: this._id.value, targetDocumentType: 'journalEntry' } },
                        ),
                    )
                    .execute(),
            ) as FinanceOriginPostingStatusData[];
        }
        this.postingMessageBlock.isHidden = true;
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value[0].message ?? null;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
    }
}
