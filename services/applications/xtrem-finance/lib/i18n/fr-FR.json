{"@sage/xtrem-finance/accounting_engine__no_records_to_process_on_accounting_staging_table": "Il n'existe aucun enregistrement à traiter pour le type {{documentType}} {{documentNumber}} à destination de {{targetDocumentType}}.", "@sage/xtrem-finance/accounting_engine__there_are_records_already_processed_on_accounting_staging_table": "Il existe déjà des enregistrements traités pour le type {{documentType}} {{documentNumber}} à destination de {{targetDocumentType}}.", "@sage/xtrem-finance/accounting_engine__there_are_records_to_be_processed_on_accounting_staging_table_to_documents_that_are_not_target_document_type": "Il existe des enregistrements à traiter pour le type {{documentType}} {{documentNumber}} qui ne sont pas de type {{targetDocumentType}}.", "@sage/xtrem-finance/accounting_engine__wrong_number_of_records": "Nombre d'enregistrements de préparation comptable : {{recordCount}}; valeur attendue : {{batchSize}}", "@sage/xtrem-finance/activity__accounting_interface_listener__name": "Listener de l'interface comptable", "@sage/xtrem-finance/activity__accounting_staging__name": "Préparation comptable", "@sage/xtrem-finance/activity__accounts_payable_invoice__name": "Facture comptable fournisseur", "@sage/xtrem-finance/activity__accounts_payable_open_item__name": "Échéance comptable fournisseur", "@sage/xtrem-finance/activity__accounts_receivable_advance__name": "Avance client", "@sage/xtrem-finance/activity__accounts_receivable_invoice__name": "Facture client", "@sage/xtrem-finance/activity__accounts_receivable_open_item__name": "Échéance comptable client", "@sage/xtrem-finance/activity__accounts_receivable_payment__name": "Règlement client", "@sage/xtrem-finance/activity__datev_export__name": "Export DATEV", "@sage/xtrem-finance/activity__generate_journal_entries__name": "G<PERSON><PERSON>rer écritures", "@sage/xtrem-finance/activity__journal_entry__name": "Écriture", "@sage/xtrem-finance/activity__journal_entry_inquiry__name": "Consultation d'écriture", "@sage/xtrem-finance/activity__payment__name": "Règlement", "@sage/xtrem-finance/activity__receipt__name": "Ré<PERSON>", "@sage/xtrem-finance/cant_post_ap_invoice_when_status_is_not_pending": "Le statut n'est pas {{pending}}. La facture auxiliaire fournisseur ne peut pas être comptabilisée.", "@sage/xtrem-finance/cant_post_ar_invoice_when_status_is_not_pending": "Le statut n'est pas {{pending}}. La facture auxiliaire client ne peut pas être comptabilisée.", "@sage/xtrem-finance/check-date-range": "La date de début ne peut pas être postérieure à la date de fin.", "@sage/xtrem-finance/classes__journal__cant_read_accounting_staging_amount": "Le montant de comptabilité pour le document numéro {{documentNumber}} {{documentNumberId}} est introuvable.", "@sage/xtrem-finance/classes__journal__cant_read_journal_entry_type": "Le type d'écriture pour le document {{documentType}}, numéro {{documentNumber}} {{documentId}} est introuvable.", "@sage/xtrem-finance/classes__journal__cant_read_posting_class_pre_journal": "La classe de comptabilisation pour le document {{documentType}}, numéro {{documentNumber}}, est introuvable. Le type de classe de comptabilisation est : {{postingClassType}}.", "@sage/xtrem-finance/classes__journal__journal_to_be_created_has_no_lines": "Le journal à créer n'a pas de lignes.", "@sage/xtrem-finance/classes__journal__no_documents_to_process": "Il n'existe aucun document à traiter pour le document {{documentType}}, le type de document cible {{targetDocumentType}} et le numéro de document {{documentNumber}}.", "@sage/xtrem-finance/classes__journal__target_document_type_not_supported": "Le type de document cible {{targetDocumentType}} n'est pas pris en charge.", "@sage/xtrem-finance/classes__localized-messages__cant_read_account": "Le compte {{account}} est introuvable.", "@sage/xtrem-finance/classes__localized-messages__cant_read_accounting_staging_amount": "Le montant de préparation de comptabilité pour le document n° {{documentNumber}} {{documentNumberId}} est introuvable.", "@sage/xtrem-finance/classes__localized-messages__cant_read_base_document_line": "La ligne de document de base {{baseDocumentLine}} est introuvable.", "@sage/xtrem-finance/classes__localized-messages__cant_read_base_tax": "La ligne de taxe {{baseTax}} est introuvable.", "@sage/xtrem-finance/classes__localized-messages__cant_read_journal_entry_type": "Le type d'écriture pour le document {{documentType}}, numéro {{documentNumber}} {{documentId}} est introuvable.", "@sage/xtrem-finance/classes__localized-messages__cant_read_stock_journal": "Le journal de stock {{stockJournal}} est introuvable.", "@sage/xtrem-finance/classes__localized-messages__cant_read_taxPostingClass": "La classe de comptabilisation d'imposition {{taxPostingClass}} est introuvable.", "@sage/xtrem-finance/classes__localized-messages__journal_to_be_created_has_no_lines": "L'écriture à créer n'a pas de lignes.", "@sage/xtrem-finance/classes__localized-messages__no_documents_to_process": "Il n'existe aucun document à traiter pour le type de document {{documentType}}, le type de document cible {{targetDocumentType}} et le numéro de document {{documentNumber}}.", "@sage/xtrem-finance/classes__localized-messages__no_invoice": "Le numéro de facture {{documentNumber}} est introuvable.", "@sage/xtrem-finance/classes__localized-messages__target_document_type_not_supported": "Le type de document cible {{targetDocumentType}} n'est pas pris en charge.", "@sage/xtrem-finance/classes__localized-messages__unable_to_get_account": "Le compte ne peut pas être déterminé.", "@sage/xtrem-finance/client_functions__record_common__different_currencies": "La devise choisie est différente de la devise associée au compte bancaire.", "@sage/xtrem-finance/client_functions__record_common__invalid_date": "<PERSON><PERSON> sélectionner une date passée.", "@sage/xtrem-finance/client_functions__record_common__negative_credit_amount_error": "Renseignez un montant d'avoir supérieur à 0.", "@sage/xtrem-finance/client_functions__record_common__negative_payment_amount_error": "Renseignez un montant de règlement supérieur à 0.", "@sage/xtrem-finance/client_functions__record_common__wrong_amount_in_bank_currency": "La devise de banque et les montants calculés sont différents. Montant de banque : {{currencySymbol}}{{bankAmount}}, montant calculé : ({{currencySymbol}}{{amount}}).", "@sage/xtrem-finance/client_functions__record_common__wrong_credit_amount_error": "Le montant crédité doit être inférieur au montant dû.", "@sage/xtrem-finance/client_functions__record_common__wrong_payment_amount_error": "Le règlement de la facture doit être inférieur au montant dû.", "@sage/xtrem-finance/client_functions__void_record__void_date_should_be_after_payment_date": "La date d'annulation doit être ultérieure à la date de commande.", "@sage/xtrem-finance/data_types__datev_export_status_enum__name": "Enum statut export Datev", "@sage/xtrem-finance/document_type_not_supported": "Ce type de document n'est pas pris en charge : {{documentType}}", "@sage/xtrem-finance/enums__datev_export_status__draft": "Brouillon", "@sage/xtrem-finance/enums__datev_export_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/enums__datev_export_status__exported": "Exporté", "@sage/xtrem-finance/enums__datev_export_status__exportInProgress": "Export en cours", "@sage/xtrem-finance/enums__datev_export_status__extracted": "Extrait", "@sage/xtrem-finance/enums__datev_export_status__extractionInProgress": "Extraction en cours", "@sage/xtrem-finance/fail__bulk_open_item_payment_notification_description__view_link": "Traces de tâches batch", "@sage/xtrem-finance/functions__accounting_engine__cant_read_account": "Le compte {{account}} est introuvable.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_base_document_line": "La ligne de document de base {{baseDocumentLine}} est introuvable.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_base_tax": "Ligne de taxe {{baseTax}} introuvable.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_customer": "Le client {{customer}} est introuvable.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_financial_site": "Le site financier {{financialSite}} est introuvable.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_item": "L'article {{item}} est introuvable.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_journal_entry_type": "Il n'existe pas de type de pièce pour le document {{documentType}}, numéro {{documentNumber}} {{documentId}}.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_stock_journal": "Le journal de stock {{stockJournal}} est introuvable.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_supplier": "Le fournisseur {{supplier}} est introuvable.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_tax": "Taxe {{tax}} introuvable.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_taxPostingClass": "Classe de comptabilisation de taxe {{taxPostingClass}} introuvable.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_transaction_currency": "La devise de transaction {{transactionCurrency}} est introuvable.", "@sage/xtrem-finance/functions__accounting_engine__customer_posting_class_missing_on_customer": "Saisissez une classe de comptabilisation pour le client {{customer}}.", "@sage/xtrem-finance/functions__accounting_engine__item_posting_class_missing_on_item": "Classe de comptabilisation introuvable pour l'article {{item}}.", "@sage/xtrem-finance/functions__accounting_engine__no_documents_to_be_processed": "Aucun document à traiter", "@sage/xtrem-finance/functions__accounting_engine__no_documents_to_process": "Il n'existe aucun document à traiter pour le type de document {{documentType}}, le type de document cible {{targetDocumentType}} et le numéro de document {{documentNumber}}.", "@sage/xtrem-finance/functions__accounting_engine__no_invoice": "Facture numéro {{documentNumber}} introuvable.", "@sage/xtrem-finance/functions__accounting_engine__processing": "{{i}}/{{numberToBeProcess}} traitant {{documents}}", "@sage/xtrem-finance/functions__accounting_engine__supplier_posting_class_missing_on_supplier": "Saisissez une classe de comptabilisation pour le fournisseur {{supplier}}.", "@sage/xtrem-finance/functions__accounting_engine__target_document_type_not_supported": "Le type de document cible {{targetDocumentType}} n'est pas pris en charge.", "@sage/xtrem-finance/functions__accounting_engine__tax_posting_class_missing_on_tax": "Saisissez une classe de comptabilisation pour la taxe {{tax}}.", "@sage/xtrem-finance/functions__accounting_engine__to_be_process": "Nombre de documents à traiter : {{numberToBeProcess}}", "@sage/xtrem-finance/generate-journal-entry-date": "Renseignez une date de début inférieure à la date de fin.", "@sage/xtrem-finance/generate-pre-journal-date": "La date de début ne peut pas être postérieure à la date de fin.", "@sage/xtrem-finance/menu_item__datev-interface": "Interface DATEV", "@sage/xtrem-finance/menu_item__payment-tracking": "Suivi des règlements", "@sage/xtrem-finance/node__accounting_staging__finish_create_journal": "Préparation comptable : fin {{messageJournalCreated}}", "@sage/xtrem-finance/node__accounting_staging__start_create_journal": "Début de préparation comptable ", "@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_description": " Écritures générées : {{journalsCreated}}", "@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_title": "Tâche batch de génération d'écriture exécutée avec succès", "@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_title_fail": "Erreur de la tâche batch de génération d'écriture", "@sage/xtrem-finance/node__accounting-interface-listener__finance_document_notification_resent": "Le document est en traitement.", "@sage/xtrem-finance/node__accounts_payable_invoice__resend_notification_for_finance": "Renvoi de la notification de finance pour la facture fournisseur : {{apInvoiceNumber}}.", "@sage/xtrem-finance/node__accounts_receivable_invoice__resend_notification_for_finance": "Renvoi de la notification de finance pour la facture client : {{arInvoiceNumber}}.", "@sage/xtrem-finance/node-extensions__base-payment-document-extension__voided": "Le règlement a été annulé.", "@sage/xtrem-finance/node-extensions__business_entity_extension__property__composedDescription": "Description composée", "@sage/xtrem-finance/node-extensions__item_extension__property__composedDescription": "Description composée", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__apOpenItem": "Éch<PERSON><PERSON> fournis<PERSON>ur", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__arOpenItem": "Échéance client", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__companyAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__origin": "Origine", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__originalNodeFactory": "Node livré d'origine", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__originalOpenItem": "Échéance d'origine", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__signedAmount": "<PERSON><PERSON> sign<PERSON>", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__signedAmountBankCurrency": "Montant signé devise de banque", "@sage/xtrem-finance/node-extensions__site_extension__property__composedDescription": "Description composée", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob": "Créer des journaux à partir de la tâche de préparation comptable", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__failed": "Échec de création des journaux à partir de la tâche de préparation comptable.", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__parameter__batchTrackingId": "Code de suivi des temps", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__parameter__journalsCreatedData": "<PERSON><PERSON><PERSON> j<PERSON> c<PERSON>", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging": "<PERSON><PERSON>er des journaux à partir de la préparation comptable", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging__failed": "Échec de création des journaux à partir de la tâche de préparation comptable.", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging__parameter__journalsCreatedData": "<PERSON><PERSON><PERSON> j<PERSON> c<PERSON>", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__retryFinanceDocument": "Renvoyer le document finance", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__retryFinanceDocument__failed": "Échec de renvoi du document financier.", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__retryFinanceDocument__parameter__financeTransaction": "Transaction finance", "@sage/xtrem-finance/nodes__accounting_interface_listener__node_name": "Listener de l'interface comptable", "@sage/xtrem-finance/nodes__accounts_open_item__node_name": "Échéance comptes", "@sage/xtrem-finance/nodes__accounts_payable_invoice__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_payable_invoice__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_payable_invoice__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_payable_invoice__cant_post_ap_invoice_when_status_is_not_draft": "Le statut n'est pas {{draft}}. La facture comptable fournisseur ne peut pas être comptabilisée.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__cant_post_ap_invoice_when_status_is_not_draft_nor_error": "Le statut de comptabilisation n'est pas {{draft}} ou {{error}}. La facture comptable fournisseur ne peut pas être comptabilisée.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__post": "Comptabi<PERSON>er", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__post__failed": "Échec de la comptabilisation.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__post__parameter__apInvoice": "Facture comptable fournisseur", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__resendNotificationForFinance": "Renvoyer la notification pour la finance", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__resendNotificationForFinance__failed": "Échec du renvoi de la notification pour la finance.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__resendNotificationForFinance__parameter__apInvoice": "Facture comptable fournisseur", "@sage/xtrem-finance/nodes__accounts_payable_invoice__node_name": "Facture comptable fournisseur", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__billBySupplier": "Fournisseur facturant", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__billBySupplierName": "Nom du fournisseur facturant", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__billBySupplierTaxIdNumber": "N° de TVA du fournisseur facturant", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__companyFxRate": "Cours de change société", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__companyFxRateDivisor": "Diviseur du cours de change société", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__description": "Description", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__documentDate": "Date document", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__dueDate": "Date d'échéance", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationApp": "Application d'intégration financière", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationAppRecordId": "Code de l'enregistrement d'application d'intégration financière", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationAppUrl": "URL d'application d'intégration finance", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationStatus": "Statut intégration financière", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financialSiteName": "Nom du site financier", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financialSiteTaxIdNumber": "N° de TVA du site financier", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__fxRateDate": "Date du cours de change", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__internalFinanceIntegrationStatus": "Statut intégration financière interne", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__invoiceDate": "Date de facture", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__openItems": "Échéances", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__origin": "Origine", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__paymentTerm": "Condition de paiement", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__paymentTracking": "Suivi des règlements", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__payToSupplier": "Fournisseur payé", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__payToSupplierLinkedAddress": "Adresse liée du fournisseur payé", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__postingDate": "Date de comptabilisation", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__postingDetails": "Détails de comptabilisation", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__postingStatus": "Statut de comptabilisation", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__purchaseDocumentNumber": "Numéro du document d'achat", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__purchaseDocumentSysId": "Code système documents d'achat", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__rateDescription": "Description taux", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__reference": "Référence", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__returnLinkedAddress": "<PERSON><PERSON><PERSON> de retour liée", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__supplierDocumentDate": "Date du document fournisseur", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__supplierDocumentNumber": "Numéro du document fournisseur", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__taxCalculationStatus": "Statut du calcul de taxe", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__taxes": "Taxes", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalAmountExcludingTax": "Montant total HT", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalAmountIncludingTax": "Montant total TTC", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalCompanyAmountExcludingTax": "Montant total société HT", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalCompanyAmountIncludingTax": "Montant total société TTC", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalCompanyTaxAmount": "Montant total de taxe société", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalExemptAmount": "Montant total exonéré", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalTaxableAmount": "Montant total imposable", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalTaxAmount": "Montant total taxes", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalTaxAmountAdjusted": "Montant total de taxe régularisé", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__type": "Type", "@sage/xtrem-finance/nodes__accounts_payable_invoice__type_invalid": "Sélectionner une facture d'achat ou un avoir d'achat comme type de facture comptable fournisseur", "@sage/xtrem-finance/nodes__accounts_payable_invoice__update_not_allowed_status_posted": "La facture comptable fournisseur est comptabilisée. Vous ne pouvez pas la mettre à jour.", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__node_name": "Ligne de facture comptable fournisseur", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__accountingStagingLines": "Lignes de préparation comptable", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__amountExcludingTax": "Montant HT", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__amountIncludingTax": "Montant TTC", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__attributesAndDimensions": "Attributs et sections", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__description": "Description", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__documentId": "Code de document", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__documentLineType": "Type de ligne de document", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__documentNumber": "Numéro de document", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__exemptAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__lineAmountExcludingTax": "Montant ligne HT", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__lineAmountIncludingTax": "Montant ligne TTC", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__lineType": "Type de ligne", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__recipientSite": "Site de consommation", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__sourceDocumentNumber": "Numéro de document d'origine", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__sourceDocumentType": "Type de document d'origine", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__storedAttributes": "Attributs stockés", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__storedDimensions": "Sections stockées", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxableAmount": "Montant imposable", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxAmount": "Montant de taxe", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxAmountAdjusted": "Montant de taxe régularisé", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxDate": "Date de taxe", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxDetail": "Détails de taxe", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxes": "Taxes", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxLineTaxAmount": "Montant de taxe de la ligne de taxe", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__uiTaxes": "Taxes UI", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__node_name": "Section de ligne de facture comptable fournisseur", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__amount": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__analyticalData": "Données analytiques", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__businessSite": "Site commercial", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__customer": "Client", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension01": "Section 01", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension02": "Section 02", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension03": "Section 03", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension04": "Section 04", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension05": "Section 05", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension06": "Section 06", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension07": "Section 07", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension08": "Section 08", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension09": "Section 09", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension10": "Section 10", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension11": "Section 11", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension12": "Section 12", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension13": "Section 13", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension14": "Section 14", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension15": "Section 15", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension16": "Section 16", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension17": "Section 17", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension18": "Section 18", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension19": "Section 19", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension20": "Section 20", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__employee": "Collaborateur", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__hasAttributesOrDimenionsChanged": "Attributs ou sections modifiés", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__item": "Article", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__manufacturingSite": "Site de production", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__originLine": "Ligne d'origine", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__project": "<PERSON>e", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__stockSite": "Site de stock", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__storedAttributes": "Attributs stockés", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__storedDimensions": "Sections stockées", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__supplier": "Fournisseur", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__task": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__node_name": "Taxe de ligne de facture comptable fournisseur", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__node_name": "Taxe de facture comptable fournisseur", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_payable_open_item__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_payable_open_item__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_payable_open_item__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_payable_open_item__bulkMutation__bulkOpenItemUpdate": "Mise à jour de masse des échéances", "@sage/xtrem-finance/nodes__accounts_payable_open_item__bulkMutation__bulkOpenItemUpdate__failed": "Échec de mise à jour de masse des échéances.", "@sage/xtrem-finance/nodes__accounts_payable_open_item__node_name": "Échéance comptable fournisseur", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__accountsPayableInvoice": "Facture comptable fournisseur", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__amountDue": "<PERSON><PERSON> dû", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__businessRelation": "Relation commerciale", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__paymentTracking": "Suivi des règlements", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__remainingCompanyAmount": "<PERSON><PERSON> soci<PERSON> restant", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__totalAmount": "Montant total", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__totalCompanyAmount": "<PERSON><PERSON> so<PERSON> total", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__totalCompanyAmountPaid": "Montant société total réglé", "@sage/xtrem-finance/nodes__accounts_payable_open_item__skipped": "Document ignoré puisqu'il est déjà entièrement réglé : {{number}}.", "@sage/xtrem-finance/nodes__accounts_receivable_advance__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_receivable_advance__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_receivable_advance__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_receivable_advance__cant_post_ar_advance_when_status_is_not_draft_nor_error": "Le statut de comptabilisation doit être 'Brouillon' ou 'Erreur'. L'avance client ne peut pas être comptabilisée.", "@sage/xtrem-finance/nodes__accounts_receivable_advance__mutation__post": "Comptabi<PERSON>er", "@sage/xtrem-finance/nodes__accounts_receivable_advance__mutation__post__failed": "Échec de comptabilisation.", "@sage/xtrem-finance/nodes__accounts_receivable_advance__mutation__post__parameter__arAdvance": "Avance client", "@sage/xtrem-finance/nodes__accounts_receivable_advance__node_name": "Avance client", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__advanceAmount": "<PERSON><PERSON> d'a<PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__advanceCompanyAmount": "Montant d'avance soci<PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__bankAccount": "Compte bancaire", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__companyCurrency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__companyFxRate": "Cours de change société", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__companyFxRateDivisor": "Diviseur du cours de change société", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__description": "Description", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationApp": "Application d'intégration financière", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationAppRecordId": "Code de l'enregistrement d'application d'intégration financière", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationAppUrl": "URL d'application d'intégration finance", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationStatus": "Statut intégration financière", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financialSiteName": "Nom du site financier", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__fxRateDate": "Date du cours de change", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__internalFinanceIntegrationStatus": "Statut intégration financière interne", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__paymentDate": "Date de règlement", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__payToCustomerId": "Code du fournisseur payé", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__payToCustomerName": "Nom du fournisseur payé", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__postingDate": "Date de comptabilisation", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__postingStatus": "Statut de comptabilisation", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__reference": "Référence", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-finance/nodes__accounts_receivable_advance__update_not_allowed_status_posted": "Vous pouvez uniquement mettre à jour une avance client de type brouillon.", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__node_name": "Ligne d'avance client", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__advanceAmount": "<PERSON><PERSON> d'a<PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__advanceCompanyAmount": "Montant d'avance soci<PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__analyticalData": "Données analytiques", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__businessSite": "Site commercial", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__companyCurrency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__computedAttributes": "Attributs calculés", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__customer": "Client", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__description": "Description", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension01": "Section 01", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension02": "Section 02", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension03": "Section 03", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension04": "Section 04", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension05": "Section 05", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension06": "Section 06", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension07": "Section 07", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension08": "Section 08", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension09": "Section 09", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension10": "Section 10", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension11": "Section 11", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension12": "Section 12", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension13": "Section 13", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension14": "Section 14", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension15": "Section 15", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension16": "Section 16", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension17": "Section 17", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension18": "Section 18", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension19": "Section 19", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension20": "Section 20", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__documentId": "Code de document", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__documentNumber": "Numéro de document", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__employee": "Collaborateur", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__hasAttributesOrDimenionsChanged": "Attributs ou sections modifiés", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__item": "Article", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__manufacturingSite": "Site de production", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__project": "<PERSON>e", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__stockSite": "Site de stock", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__storedAttributes": "Attributs stockés", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__storedDimensions": "Sections stockées", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__supplier": "Fournisseur", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__task": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__cant_post_ar_invoice_when_status_is_not_draft": "Le statut n'est pas {{draft}}. La facture comptable client ne peut pas être comptabilisée.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__cant_post_ar_invoice_when_status_is_not_draft_nor_error": "Le statut de comptabilisation n'est pas {{draft}} ou {{error}}. La facture client ne peut pas être comptabilisée.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__information_payment_tracking": "Actualiser les échéances créées précédemment en fonction de la balance âgée des tiers", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__post": "Comptabi<PERSON>er", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__post__failed": "Échec de comptabilisation.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__post__parameter__arInvoice": "Facture client", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__resendNotificationForFinance": "Renvoyer la notification pour la finance", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__resendNotificationForFinance__failed": "Échec du renvoi de la notification pour la finance.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__resendNotificationForFinance__parameter__arInvoice": "Facture comptable client", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__node_name": "Facture client", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__payment_tracking": "Option de service Suivi des règlements activée", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__billToCustomer": "Client facturé", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__billToCustomerName": "Nom du client facturé", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__billToCustomerTaxIdNumber": "N° de TVA du client facturé", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__companyFxRate": "Cours de change société", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__companyFxRateDivisor": "Diviseur du cours de change société", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__description": "Description", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__documentDate": "Date document", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__dueDate": "Date d'échéance", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationApp": "Application d'intégration financière", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationAppRecordId": "Code de l'enregistrement d'application d'intégration financière", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationAppUrl": "URL d'application d'intégration finance", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationStatus": "Statut intégration financière", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financialSiteName": "Nom du site financier", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financialSiteTaxIdNumber": "N° de TVA du site financier", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__fxRateDate": "Date du cours de change", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__internalFinanceIntegrationStatus": "Statut intégration financière interne", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__invoiceDate": "Date de facture", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__isPrinted": "Imprimée", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__openItems": "Échéances", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__origin": "Origine", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__paymentStatus": "Statut de règlement", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__paymentTerm": "Condition de paiement", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__postingDate": "Date de comptabilisation", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__postingDetails": "Détails de comptabilisation", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__postingStatus": "Statut de comptabilisation", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__rateDescription": "Description taux", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__reference": "Référence", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__salesDocumentNumber": "Numéro du document fournisseur", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__salesDocumentSysId": "Code système du document de vente", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__taxCalculationStatus": "Statut du calcul de taxe", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__taxEngine": "Moteur de taxe", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__taxes": "Taxes", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalAmountExcludingTax": "Montant total HT", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalAmountIncludingTax": "Montant total TTC", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalCompanyAmountExcludingTax": "Montant total société HT", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalCompanyAmountIncludingTax": "Montant total société TTC", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalCompanyTaxAmount": "Montant total de taxe société", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalExemptAmount": "Montant total exonéré", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalTaxableAmount": "Montant total imposable", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalTaxAmount": "Montant total taxes", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalTaxAmountAdjusted": "Montant total de taxes régularisé", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__type": "Type", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__type_invalid": "Sélectionner une facture ou un avoir de vente comme type de facture client", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__update_not_allowed_status_posted": "La facture client est comptabilisée. Vous ne pouvez pas la mettre à jour.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__node_name": "Ligne de facture client", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__accountingStagingLines": "Lignes de préparation comptable", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__amountExcludingTax": "Montant HT", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__amountIncludingTax": "Montant TTC", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__attributesAndDimensions": "Attributs et sections", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__description": "Description", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__documentId": "Code de document", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__documentLineType": "Type de ligne de document", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__documentNumber": "Numéro de document", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__exemptAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__lineAmountExcludingTax": "Montant ligne HT", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__lineAmountIncludingTax": "Montant ligne TTC", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__lineType": "Type de ligne", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__netPriceIncludingTax": "Prix net TTC", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__providerSite": "Site de fourniture", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__quantity": "Quantité", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__quantityInSalesUnit": "Quantité en unité de vente", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__sourceDocumentNumber": "Numéro de document d'origine", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__sourceDocumentType": "Type de document d'origine", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__storedAttributes": "Attributs stockés", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__storedDimensions": "Sections stockées", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxableAmount": "Montant imposable", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxAmount": "Montant de taxe", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxAmountAdjusted": "Montant taxe régularisé", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxDate": "Date de taxe", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxDetail": "Détails de taxe", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxes": "Taxes", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxLineTaxAmount": "Montant de taxe de la ligne de taxe", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__uiTaxes": "Taxes UI", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__node_name": "Section de ligne de facture client", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__amount": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__analyticalData": "Données analytiques", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__businessSite": "Site commercial", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__customer": "Client", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension01": "Section 01", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension02": "Section 02", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension03": "Section 03", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension04": "Section 04", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension05": "Section 05", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension06": "Section 06", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension07": "Section 07", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension08": "Section 08", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension09": "Section 09", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension10": "Section 10", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension11": "Section 11", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension12": "Section 12", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension13": "Section 13", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension14": "Section 14", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension15": "Section 15", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension16": "Section 16", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension17": "Section 17", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension18": "Section 18", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension19": "Section 19", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension20": "Section 20", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__employee": "Collaborateur", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__hasAttributesOrDimenionsChanged": "Attributs ou sections modifiés", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__item": "Article", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__manufacturingSite": "Site de production", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__originLine": "Ligne d'origine", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__project": "<PERSON>e", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__stockSite": "Site de stock", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__storedAttributes": "Attributs stockés", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__storedDimensions": "Sections stockées", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__supplier": "Fournisseur", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__task": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__node_name": "Taxe de ligne de facture client", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__node_name": "Taxe de facture client", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__bulkMutation__bulkOpenItemUpdate": "Mise à jour de masse des échéances", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__bulkMutation__bulkOpenItemUpdate__failed": "Échec de mise à jour de masse des échéances.", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__node_name": "Échéance client", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__accountsReceivableInvoice": "Facture client", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__amountDue": "<PERSON><PERSON> dû", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__businessRelation": "Relation commerciale", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__receipts": "Règlements reçus", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__remainingCompanyAmount": "<PERSON><PERSON> soci<PERSON> restant", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__totalAmount": "Montant total", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__totalCompanyAmount": "Total montant taxe société", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__totalCompanyAmountPaid": "Total montant société réglé", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__skipped": "Document ignoré puisqu'il est déjà entièrement réglé : {{number}}.", "@sage/xtrem-finance/nodes__accounts_receivable_payment__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_receivable_payment__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_receivable_payment__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_receivable_payment__cant_post_ar_payment_when_status_is_not_draft_nor_error": "Le statut de comptabilisation doit être 'Brouillon' ou 'Erreur'. Le règlement client ne peut pas être comptabilisé.", "@sage/xtrem-finance/nodes__accounts_receivable_payment__mutation__post": "Comptabi<PERSON>er", "@sage/xtrem-finance/nodes__accounts_receivable_payment__mutation__post__failed": "Échec de comptabilisation.", "@sage/xtrem-finance/nodes__accounts_receivable_payment__mutation__post__parameter__arPayment": "Règlement client", "@sage/xtrem-finance/nodes__accounts_receivable_payment__node_name": "Règlement client", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__bankAccount": "Compte bancaire", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__companyCurrency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__companyFxRate": "Cours de change société", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__companyFxRateDivisor": "Diviseur du cours de change société", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__description": "Description", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationApp": "Application d'intégration financière", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationAppRecordId": "Code de l'enregistrement d'application d'intégration financière", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationAppUrl": "URL d'application d'intégration finance", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationStatus": "Statut intégration financière", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financialSiteName": "Nom du site financier", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__fxRateDate": "Date du cours de change", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__internalFinanceIntegrationStatus": "Statut intégration financière interne", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__paymentAmount": "Montant de règlement", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__paymentCompanyAmount": "Montant règlement société", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__paymentDate": "Date de règlement", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__payToCustomerId": "Code du fournisseur payé", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__payToCustomerName": "Nom du fournisseur payé", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__postingDate": "Date de comptabilisation", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__postingStatus": "Statut de comptabilisation", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__reference": "Référence", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-finance/nodes__accounts_receivable_payment__update_not_allowed_status_posted": "Le statut du règlement client est {{status}}. Vous ne pouvez pas le mettre à jour.", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__node_name": "Ligne de règlement client", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__companyCurrency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__currency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__documentId": "Code de document", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__documentNumber": "Numéro de document", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__paymentAmount": "Montant de règlement", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__paymentCompanyAmount": "Montant règlement société", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__type": "Type", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__type_invalid": "Le type de la ligne du règlement client doit être 'Facture de vente' ou 'Avoir de vente'.", "@sage/xtrem-finance/nodes__ap_invoice__posted": "La facture comptable fournisseur a été comptabilisée.", "@sage/xtrem-finance/nodes__ar_advance__posted": "L'avance client a été comptabilisée.", "@sage/xtrem-finance/nodes__ar_invoice__posted": "La facture client a été comptabilisée.", "@sage/xtrem-finance/nodes__ar_payment__posted": "Le règlement client a été comptabilisé.", "@sage/xtrem-finance/nodes__base_open_item__text_forced_close": "Clôture forcée le {{date}} par {{user}}", "@sage/xtrem-finance/nodes__base_payment_document_payment_already_voided": "Le règlement était déjà annulé.", "@sage/xtrem-finance/nodes__datev_export__account_without_datev_id": "Compte sans code DATEV : {{account}}", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExport": "Export DATEV", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExport__failed": "Échec de l'export DATEV.", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExtraction": "Extraction DATEV", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExtraction__failed": "Échec de l'extraction DATEV.", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExtraction__parameter__id": "Code", "@sage/xtrem-finance/nodes__datev_export__business_relation_without_datev_id": "Entité commerciale sans code DATEV : {{businessEntity}}", "@sage/xtrem-finance/nodes__datev_export__complete_account_export": "Export compte DATEV achevé.", "@sage/xtrem-finance/nodes__datev_export__complete_business_relation_export": "Export relation commerciale DATEV achevé.", "@sage/xtrem-finance/nodes__datev_export__complete_export": "L'export DATEV est achevée.", "@sage/xtrem-finance/nodes__datev_export__complete_journal_entry_lines_export": "Export des lignes d'écriture DATEV effectué.", "@sage/xtrem-finance/nodes__datev_export__datev_file": "Fichiers export DATEV", "@sage/xtrem-finance/nodes__datev_export__datev_file_ready": "Fichiers créés : {{nbOfFilesCreated}}.\n", "@sage/xtrem-finance/nodes__datev_export__datev_number_of_warnings": "Avertissement : {{nbOfWarnings}}", "@sage/xtrem-finance/nodes__datev_export__end_extraction": "Extraction DATEV finalisée.", "@sage/xtrem-finance/nodes__datev_export__failure_description": "L'extraction DATEV ne s'est pas achevé avec succès. Erreur : {{error}}", "@sage/xtrem-finance/nodes__datev_export__failure_description_export": "Échec de l'export DATEV. Erreur : {{error}}", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_account_without_datev_id": "Ligne d'écriture pour un compte sans code DATEV. Écriture {{journalEntryNumber}}, compte {{account}}", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_contra_account_without_datev_id": "Ligne d'écriture pour un compte de contrepartie sans code DATEV. Écriture {{journalEntryNumber}}, compte de contrepartie {{account}}", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_no_tax_line_for_taxable_amount": "Impossible de trouver une ligne de taxe pour le montant imposable. Écriture {{journalEntryNumber}}.", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_with_different_tax": "Ligne d'écriture pour un compte automatique utilisant un code taxe différent. Écriture {{journalEntryNumber}}, compte {{account}}", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_without_posting_key": "Ligne d'écriture pour un compte non automatique et sans clé de comptabilisation. Écriture {{journalEntryNumber}}, compte {{account}}", "@sage/xtrem-finance/nodes__datev_export__node_name": "Export DATEV", "@sage/xtrem-finance/nodes__datev_export__number_extracted_accounts": "Comptes DATEV extraits : {{numberExtractedAccounts}}.", "@sage/xtrem-finance/nodes__datev_export__number_extracted_customers_suppliers": "Clients et fournisseurs DATEV extraits : {{numberExtractedBusinessRelations}}.", "@sage/xtrem-finance/nodes__datev_export__number_extracted_journal_entry_lines": "Lignes d'écriture DATEV extraites : {{numberExtractedJournalEntryLines}}.", "@sage/xtrem-finance/nodes__datev_export__property__accountsWithoutDatevId": "Comptes sans code DATEV", "@sage/xtrem-finance/nodes__datev_export__property__attributeType1": "Attribut type 1", "@sage/xtrem-finance/nodes__datev_export__property__attributeType2": "Attribut type 2", "@sage/xtrem-finance/nodes__datev_export__property__company": "Société", "@sage/xtrem-finance/nodes__datev_export__property__customersWithoutDatevId": "Clients sans code DATEV", "@sage/xtrem-finance/nodes__datev_export__property__dateRange": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-finance/nodes__datev_export__property__datevConsultantNumber": "Numéro du consultant DATEV", "@sage/xtrem-finance/nodes__datev_export__property__datevCustomerNumber": "Numéro client DATEV", "@sage/xtrem-finance/nodes__datev_export__property__datevExportAccounts": "Comptes export DATEV", "@sage/xtrem-finance/nodes__datev_export__property__datevExportBusinessRelations": "Relations commerciales export DATEV", "@sage/xtrem-finance/nodes__datev_export__property__datevExportJournalEntryLines": "Lignes d'écriture d'export DATEV", "@sage/xtrem-finance/nodes__datev_export__property__dimensionType1": "Axe 1", "@sage/xtrem-finance/nodes__datev_export__property__dimensionType2": "Axe 2", "@sage/xtrem-finance/nodes__datev_export__property__doAccounts": "<PERSON><PERSON> comptes", "@sage/xtrem-finance/nodes__datev_export__property__doCustomersSuppliers": "Clients fournisseurs", "@sage/xtrem-finance/nodes__datev_export__property__doJournalEntries": "Écritures", "@sage/xtrem-finance/nodes__datev_export__property__endDate": "Date de fin", "@sage/xtrem-finance/nodes__datev_export__property__fiscalYearStart": "Début année fiscale", "@sage/xtrem-finance/nodes__datev_export__property__id": "Code", "@sage/xtrem-finance/nodes__datev_export__property__isLocked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__datev_export__property__skrCoa": "SKR COA", "@sage/xtrem-finance/nodes__datev_export__property__startDate": "Date de début", "@sage/xtrem-finance/nodes__datev_export__property__status": "Statut", "@sage/xtrem-finance/nodes__datev_export__property__suppliersWithoutDatevId": "Fournisseurs sans code DATEV", "@sage/xtrem-finance/nodes__datev_export__property__timeStamp": "Horodatage", "@sage/xtrem-finance/nodes__datev_export__start_export": "Export DATEV débuté.", "@sage/xtrem-finance/nodes__datev_export__start_extract_accounts": "Début extraction compte DATEV", "@sage/xtrem-finance/nodes__datev_export__start_extract_customers_suppliers": "Début extraction clients et fournisseurs DATEV", "@sage/xtrem-finance/nodes__datev_export__start_extract_journal_entry_lines": "Début d'extraction des lignes d'écriture DATEV.", "@sage/xtrem-finance/nodes__datev_export__start_extraction": "Extraction DATEV débutée", "@sage/xtrem-finance/nodes__datev_export__stop_extraction": "Arrêt de l'extraction DATEV le {{stopDate}}.", "@sage/xtrem-finance/nodes__datev_export__success_description": "L'extraction DATEV est achevée.", "@sage/xtrem-finance/nodes__datev_export__success_notification_title": "Extraction DATEV achevée", "@sage/xtrem-finance/nodes__datev_export__user_download_accounts": "Télécharger comptes", "@sage/xtrem-finance/nodes__datev_export__user_download_journal_entry_lines": "Télécharger les lignes d'écriture", "@sage/xtrem-finance/nodes__datev_export__user_notifications_download_customers_and_suppliers": "Télécharger clients et fournisseurs", "@sage/xtrem-finance/nodes__datev_export__user_notifications_history": "Historique", "@sage/xtrem-finance/nodes__datev_export_account__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__datev_export_account__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__datev_export_account__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__datev_export_account__node_name": "Compte export DATEV", "@sage/xtrem-finance/nodes__datev_export_account__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__datev_export_account__property__datevExport": "DATEV export", "@sage/xtrem-finance/nodes__datev_export_account__property__datevId": "Code DATEV", "@sage/xtrem-finance/nodes__datev_export_account__property__name": "Nom", "@sage/xtrem-finance/nodes__datev_export_business_relation__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__datev_export_business_relation__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__datev_export_business_relation__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__datev_export_business_relation__node_name": "Relation commerciale export DATEV", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__businessRelation": "Relation commerciale", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__city": "Ville", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__country": "Pays", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__datevExport": "Export DATEV", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__datevId": "Code DATEV", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__name": "Nom", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__postcode": "Code postal", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__street": "Rue", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__taxIdNumber": "N° de TVA", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__node_name": "Ligne d'écriture d'export DATEV", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__businessEntityTaxIdNumber": "N° de TVA de l'entité commerciale", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__companyCurrency": "<PERSON><PERSON> socié<PERSON>", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__companyFxRate": "Cours de change de la société", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__companyValue": "<PERSON><PERSON> de soci<PERSON>", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevAccountId": "ID compte DATEV", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevContraAccountId": "Code de compte de contrepartie DATEV", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevExport": "Export DATEV", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevSign": "Signe DATEV", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__description": "Description", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__dimension1": "Section 1", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__dimension2": "Section 2", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__journalEntryLine": "Ligne d'écriture", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__locked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__postingDate": "Date comptabilisation", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__postingKey": "Clé de comptabilisation", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__siteTaxIdNumber": "SIRET", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__supplierDocumentNumber": "Numéro du document fournisseur", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__transactionValue": "Valeur de transaction", "@sage/xtrem-finance/nodes__datev_export_listener__datev_file": "Fichier DATEV", "@sage/xtrem-finance/nodes__datev_export_listener__datev_file_ready": "Fichier prêt pour téléchargement", "@sage/xtrem-finance/nodes__datev_export_listener__node_name": "Listenenr d'export DATEV", "@sage/xtrem-finance/nodes__initialize_open_item__node_name": "Initialiser l'échéance", "@sage/xtrem-finance/nodes__intacct-bank-account-transaction_feed__site_legislation_and_coa_legislation_dont_match": "La législation du site doit être la même que celle du plan comptable.", "@sage/xtrem-finance/nodes__journal_entry__ap_invoice_reference": "La référence de la facture auxiliaire fournisseur peut uniquement être utilisée sur des pré-écritures issues d'une facture auxiliaire fournisseur.", "@sage/xtrem-finance/nodes__journal_entry__ar_invoice_reference": "La référence de la facture client peut uniquement être utilisée sur des pré-écritures issues d'une facture client.", "@sage/xtrem-finance/nodes__journal_entry__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__journal_entry__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__journal_entry__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__journal_entry__contra_journal_entry_line_is_mandatory_on_new_documents": "<PERSON><PERSON> de<PERSON> renseigner une écriture de contrepartie sur la ligne.", "@sage/xtrem-finance/nodes__journal_entry__document_type_not_allowed": "Le type de document est paramétrable uniquement pour la législation française.", "@sage/xtrem-finance/nodes__journal_entry__journal_entry_dimension_line_not_equal_to_journal_line": "Le montant total d'allocation attribut / section n'est pas égal au montant de la ligne.", "@sage/xtrem-finance/nodes__journal_entry__mutation__post": "Comptabi<PERSON>er", "@sage/xtrem-finance/nodes__journal_entry__mutation__post__failed": "Échec de comptabilisation.", "@sage/xtrem-finance/nodes__journal_entry__mutation__post__parameter__journalEntry": "Écriture", "@sage/xtrem-finance/nodes__journal_entry__no_attribute_value_account": "Les sections de compte demandées {{dimensions}} n'ont pas été déclarées pour le compte {{lineAccount}} sur la ligne de journal {{lineCount}}.", "@sage/xtrem-finance/nodes__journal_entry__no_attribute_value_company": "Les sections société demandées {{dimensions}} n'ont pas été déclarées pour le compte {{lineAccount}} sur la ligne de journal {{lineCount}}.", "@sage/xtrem-finance/nodes__journal_entry__no_sequence_number": "Le numéro de compteur de l'écriture ne peut pas être généré. Renseignez d'abord un compteur par défaut.", "@sage/xtrem-finance/nodes__journal_entry__node_name": "Écriture", "@sage/xtrem-finance/nodes__journal_entry__number_change_forbidden": "Vous pouvez uniquement modifier le numéro d'écriture des nouveaux enregistrements.", "@sage/xtrem-finance/nodes__journal_entry__property__apInvoice": "Facture comptable fournisseur", "@sage/xtrem-finance/nodes__journal_entry__property__arInvoice": "Facture comptable client", "@sage/xtrem-finance/nodes__journal_entry__property__description": "Description", "@sage/xtrem-finance/nodes__journal_entry__property__documentType": "Type de document", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationApp": "Application d'intégration financière", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationAppRecordId": "Code de l'enregistrement d'application d'intégration financière", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationAppUrl": "URL d'application d'intégration finance", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationStatus": "Statut intégration financière", "@sage/xtrem-finance/nodes__journal_entry__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__journal_entry__property__internalFinanceIntegrationStatus": "Statut intégration financière interne", "@sage/xtrem-finance/nodes__journal_entry__property__journal": "Journal", "@sage/xtrem-finance/nodes__journal_entry__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry__property__origin": "Origine", "@sage/xtrem-finance/nodes__journal_entry__property__postingDate": "Date de comptabilisation", "@sage/xtrem-finance/nodes__journal_entry__property__postingStatus": "Statut de comptabilisation", "@sage/xtrem-finance/nodes__journal_entry__property__reference": "Référence", "@sage/xtrem-finance/nodes__journal_entry__query__areFinanceIntegrationPackagesActive": "Packages intégration Finance actifs", "@sage/xtrem-finance/nodes__journal_entry__query__areFinanceIntegrationPackagesActive__failed": "Échec des packages intégration Finance actifs.", "@sage/xtrem-finance/nodes__journal_entry__query__areFinanceIntegrationPackagesActive__parameter__dummy": "Factice", "@sage/xtrem-finance/nodes__journal_entry__reconcile_journal_entry_by_key": "L'écriture est déséquilibrée pour le site financier {{financialSite}}.", "@sage/xtrem-finance/nodes__journal_entry__two_journal_entry_lines_mandatory": "Renseignez au moins deux lignes pour l'écriture.", "@sage/xtrem-finance/nodes__journal_entry_inquiry__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__journal_entry_inquiry__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__journal_entry_inquiry__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__journal_entry_inquiry__mutation__singleRecord": "Enregistrement unique", "@sage/xtrem-finance/nodes__journal_entry_inquiry__mutation__singleRecord__failed": "Échec d'enregistrement unique.", "@sage/xtrem-finance/nodes__journal_entry_inquiry__node_name": "Consultation d'écriture", "@sage/xtrem-finance/nodes__journal_entry_inquiry__property__journalEntryLines": "Lignes d'écritures", "@sage/xtrem-finance/nodes__journal_entry_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__journal_entry_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__journal_entry_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__journal_entry_line__business_entity_reference_mandatory": "Le compte {{account}} est un compte collectif. Renseignez une entité commerciale.", "@sage/xtrem-finance/nodes__journal_entry_line__business_entity_reference_not_possible": "Le compte {{account}} n'est pas un compte collectif. Vous ne pouvez pas renseigner une entité commerciale.", "@sage/xtrem-finance/nodes__journal_entry_line__journal_entry_type_line_is_mandatory_on_new_documents": "<PERSON><PERSON> de<PERSON> renseigner le type d'écriture sur la ligne.", "@sage/xtrem-finance/nodes__journal_entry_line__node_name": "Ligne d'écriture", "@sage/xtrem-finance/nodes__journal_entry_line__property__account": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__accountingStagingLines": "Lignes de préparation comptable", "@sage/xtrem-finance/nodes__journal_entry_line__property__attributesAndDimensions": "Attributs et sections", "@sage/xtrem-finance/nodes__journal_entry_line__property__baseTax": "Taxe de base", "@sage/xtrem-finance/nodes__journal_entry_line__property__blank": "Vide", "@sage/xtrem-finance/nodes__journal_entry_line__property__businessEntity": "Entité commerciale", "@sage/xtrem-finance/nodes__journal_entry_line__property__businessSiteAttribute": "Attribut site commercial", "@sage/xtrem-finance/nodes__journal_entry_line__property__chartOfAccount": "Plan comptable", "@sage/xtrem-finance/nodes__journal_entry_line__property__commonReference": "Référence commune", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyCredit": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyCurrency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyDebit": "<PERSON><PERSON><PERSON> soci<PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyFxRate": "Cours de change société", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyFxRateDivisor": "Diviseur du cours de change société", "@sage/xtrem-finance/nodes__journal_entry_line__property__contraAccount": "Compte de contrepartie", "@sage/xtrem-finance/nodes__journal_entry_line__property__contraJournalEntryLine": "Ligne d'écriture de compte de contrepartie", "@sage/xtrem-finance/nodes__journal_entry_line__property__customerAttribute": "Attribut client", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevBusinessEntityTaxIdNumber": "N° de TVA de l'entité commerciale DATEV", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevCompanyAmount": "Montant société DATEV", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevContraAccountId": "Code de compte de contrepartie DATEV", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevPostingKey": "Clé de comptabilisation DATEV", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevTransactionAmount": "Montant de transaction DATEV", "@sage/xtrem-finance/nodes__journal_entry_line__property__deductibleTaxRate": "Taux de taxe déductible", "@sage/xtrem-finance/nodes__journal_entry_line__property__description": "Description", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension01": "Section 01", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension02": "Section 02", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension03": "Section 03", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension04": "Section 04", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension05": "Section 05", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension06": "Section 06", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension07": "Section 07", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension08": "Section 08", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension09": "Section 09", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension10": "Section 10", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension11": "Section 11", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension12": "Section 12", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension13": "Section 13", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension14": "Section 14", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension15": "Section 15", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension16": "Section 16", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension17": "Section 17", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension18": "Section 18", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension19": "Section 19", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension20": "Section 20", "@sage/xtrem-finance/nodes__journal_entry_line__property__dueDate": "Date d'échéance", "@sage/xtrem-finance/nodes__journal_entry_line__property__employeeAttribute": "Attribut collaborateur", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteAmount": "Montant du site financier", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteAttribute": "Attribut du site financier", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteCredit": "Crédit du site financier", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteCurrency": "Devise du site financier", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteDebit": "Débit du site financier", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteFxRate": "Taux de change du site financier", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteFxRateDivisor": "Diviseur du taux de change du site financier", "@sage/xtrem-finance/nodes__journal_entry_line__property__fxRateDate": "Date du cours de change", "@sage/xtrem-finance/nodes__journal_entry_line__property__inquiryDescription": "Description de consultation", "@sage/xtrem-finance/nodes__journal_entry_line__property__inquiryTransactionCurrency": "Devise de transaction de consultation", "@sage/xtrem-finance/nodes__journal_entry_line__property__isBalanceLine": "Ligne d'équilibrage", "@sage/xtrem-finance/nodes__journal_entry_line__property__itemAttribute": "Attribut article", "@sage/xtrem-finance/nodes__journal_entry_line__property__journalEntry": "Écriture", "@sage/xtrem-finance/nodes__journal_entry_line__property__journalEntryTypeLine": "Ligne de type d'écriture", "@sage/xtrem-finance/nodes__journal_entry_line__property__manufacturingSiteAttribute": "Attribut site de production", "@sage/xtrem-finance/nodes__journal_entry_line__property__numericSign": "Signe numérique", "@sage/xtrem-finance/nodes__journal_entry_line__property__projectAttribute": "Attribut de projet", "@sage/xtrem-finance/nodes__journal_entry_line__property__rateDescription": "Description du taux", "@sage/xtrem-finance/nodes__journal_entry_line__property__sign": "<PERSON>s", "@sage/xtrem-finance/nodes__journal_entry_line__property__signedTransactionAmount": "Montant de transaction signé", "@sage/xtrem-finance/nodes__journal_entry_line__property__stockSiteAttribute": "Attribut du site de stock", "@sage/xtrem-finance/nodes__journal_entry_line__property__supplierAttribute": "Attribut four<PERSON><PERSON>ur", "@sage/xtrem-finance/nodes__journal_entry_line__property__supplierDocumentNumber": "Numéro du document fournisseur", "@sage/xtrem-finance/nodes__journal_entry_line__property__taskAttribute": "Attribut de tâche", "@sage/xtrem-finance/nodes__journal_entry_line__property__tax": "Taxe", "@sage/xtrem-finance/nodes__journal_entry_line__property__taxDate": "Date de taxe", "@sage/xtrem-finance/nodes__journal_entry_line__property__taxExternalReference": "Référence externe de taxe", "@sage/xtrem-finance/nodes__journal_entry_line__property__taxRate": "Taux taxe", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionAmount": "Montant transaction", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionCredit": "Crédit transaction", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionDebit": "Débit transaction", "@sage/xtrem-finance/nodes__journal_entry_line__property__validationDate": "Date de validation", "@sage/xtrem-finance/nodes__journal_entry_line__tax_date_empty": "Laisser la date de taxe vide.", "@sage/xtrem-finance/nodes__journal_entry_line__tax_date_mandatory": "Renseignez une date de taxe.", "@sage/xtrem-finance/nodes__journal_entry_line__tax_ref_empty": "Laisser la référence de taxe vide.", "@sage/xtrem-finance/nodes__journal_entry_line__tax_ref_mandatory": "Renseignez une référence externe", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__node_name": "Section de ligne d'écriture", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__analyticalData": "Données analytiques", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__businessSite": "Site commercial", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__companyAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__companyCurrency": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__customer": "Client", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension01": "Section 01", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension02": "Section 02", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension03": "Section 03", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension04": "Section 04", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension05": "Section 05", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension06": "Section 06", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension07": "Section 07", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension08": "Section 08", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension09": "Section 09", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension10": "Section 10", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension11": "Section 11", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension12": "Section 12", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension13": "Section 13", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension14": "Section 14", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension15": "Section 15", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension16": "Section 16", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension17": "Section 17", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension18": "Section 18", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension19": "Section 19", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension20": "Section 20", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__employee": "Collaborateur", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__financialSite": "Site financier", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__financialSiteAmount": "Montant du site financier", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__financialSiteCurrency": "Devise du site financier", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__hasAttributesOrDimenionsChanged": "Attributs ou sections modifiés", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__item": "Article", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__journalEntryLine": "Ligne d'écriture", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__manufacturingSite": "Site de production", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__project": "<PERSON>e", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__stockSite": "Site de stock", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__storedAttributes": "Attributs stockés", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__storedComputedAttributes": "Attributs calculés stockés", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__storedDimensions": "Sections stockées", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__supplier": "Fournisseur", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__task": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__transactionAmount": "Montant transaction", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-finance/nodes__journal_entry_line_staging__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__journal_entry_line_staging__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__journal_entry_line_staging__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__journal_entry_line_staging__node_name": "Préparation de lignes d'écriture", "@sage/xtrem-finance/nodes__journal_entry_line_staging__property__accountingStaging": "Préparation comptable", "@sage/xtrem-finance/nodes__journal_entry_line_staging__property__journalEntryLine": "Ligne d'écriture", "@sage/xtrem-finance/nodes__journal-entry__cant_post_journal_entry_when_status_is_not_draft_nor_error": "Le statut de comptabilisation n'est pas {{draft}} ou {{error}}. La facture liée à l'écriture ne peut pas être comptabilisée.", "@sage/xtrem-finance/nodes__journal-entry__posted": "L'écriture a été comptabilisée.", "@sage/xtrem-finance/nodes__payment__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__payment__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__payment__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment": "Annuler règlement", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__failed": "Échec d'annulation de règlement.", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__parameter__payment": "Règlement", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__parameter__voidDate": "Date d'annulation", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__parameter__voidText": "Texte d'annulation", "@sage/xtrem-finance/nodes__payment__node_name": "Règlement", "@sage/xtrem-finance/nodes__payment_document_line__ap_invoice_reference_mandatory": "<PERSON><PERSON> devez ajouter une référence à la facture fournisseur.", "@sage/xtrem-finance/nodes__payment_document_line__ap_invoice_reference_not_allowed": "Vous ne pouvez pas faire référence à la facture fournisseur.", "@sage/xtrem-finance/nodes__payment_document_line__ar_invoice_reference_mandatory": "<PERSON><PERSON> devez ajouter une référence à la facture client.", "@sage/xtrem-finance/nodes__payment_document_line__ar_invoice_reference_not_allowed": "Vous ne pouvez pas faire référence à la facture client.", "@sage/xtrem-finance/nodes__payment_document_line__wrong_ap_invoice_posting_status": "Le statut de comptabilisation de la facture fournisseur doit être Comptabilisée.", "@sage/xtrem-finance/nodes__payment_document_line__wrong_ar_invoice_posting_status": "Le statut de comptabilisation de la facture client doit être Comptabilisée.", "@sage/xtrem-finance/nodes__receipt__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-finance/nodes__receipt__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-finance/nodes__receipt__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-finance/nodes__receipt__mutation__createReceipt": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__receipt__mutation__createReceipt__parameter__receiptHeader": "En-tête ré<PERSON>s", "@sage/xtrem-finance/nodes__receipt__mutation__createReceipt__parameter__receiptLines": "Lignes <PERSON>", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment": "Annuler règlement", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__failed": "Échec d'annulation de règlement.", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__parameter__payment": "Règlement", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__parameter__voidDate": "Date d'annulation", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__parameter__voidText": "Texte d'annulation", "@sage/xtrem-finance/nodes__receipt__node_name": "Ré<PERSON>", "@sage/xtrem-finance/nodes__receipt__payment_amount_discrepancy": "Le montant de règlement du document : {{amount}}, doit être le même que le montant total des règlements de toutes les lignes : {{total}}.", "@sage/xtrem-finance/notification_bulk_open_item_payment__description_error": "Le règlement forcé pour tous les documents a échoué. Vérifiez les traces de tâches batch pour plus d'informations.", "@sage/xtrem-finance/notification_bulk_open_item_payment__description_success": "Un règlement forcé a été utilisé pour les échéances.", "@sage/xtrem-finance/package__name": "Finance", "@sage/xtrem-finance/page__void_date__mandatory": "La date est obligatoire.", "@sage/xtrem-finance/page_fragments__base_payment__date_issued": "Date d'émission", "@sage/xtrem-finance/page_fragments__base_payment__date_received": "Date de réception", "@sage/xtrem-finance/page_fragments__payment_information__amount_in_bank_currency_must_be_positive": "Le montant en devise de banque doit être supérieur à 0.", "@sage/xtrem-finance/page_fragments__payment_information__date_issued": "Date d'émission", "@sage/xtrem-finance/page_fragments__payment_information__date_received": "Date de réception", "@sage/xtrem-finance/page_fragments__payment_information__payment_amount_can_not_be_negative": "Le montant de règlement doit être supérieur ou égal à 0.", "@sage/xtrem-finance/page_fragments__payment_information__transaction_information_can_not_exceed_100_characters": "Les informations de la transaction ne doivent pas dépasser 100 caractères.", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionCriteriaBlock____title": "Critères", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionDocumentType____title": "Type de document", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___createStamp": "Envoyée", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___id": "Code", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__batchId": "Code batch", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__message": "Message", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__originDocumentNumber": "N° doc", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__originDocumentType": "Type doc.", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__postingStatus": "Statut", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__status": "Statut de notification", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__targetDocumentNumber": "Numéro du document cible", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__targetDocumentType": "Type document cible", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__wasResent": "Renvoy<PERSON>", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____dropdownActions__title": "Modifier", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____dropdownActions__title__2": "Modifier les sections des documents source", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____title": "Résultats", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionSection____title": "Finance", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionStatus____title": "Statut", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocuments____columns__title__sourceDocumentNumber": "N°", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocuments____columns__title__sourceDocumentType": "Document", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocuments____title": "Documents d'origine", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocumentsBlock____title": "Documents d'origine", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__adjustmentAmount": "Mnt régularisation", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__amount": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__discountAmount": "Mnt escompte", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__bankAccount__name": "Compte bancaire", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__isVoided": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__number": "N° règlement", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__paymentDate": "Date règlement", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__paymentMethod": "Mode règlement", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__reference": "Informations de transaction", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__penaltyAmount": "Mnt pénalité", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____title": "Règlements", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__adjustmentAmount": "Mnt régularisation", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__amount": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__discountAmount": "<PERSON>ant escompte", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__bankAccount__name": "Compte bancaire", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__isVoided": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__number": "N° règlement reçu", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__paymentDate": "Date réception", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__paymentMethod": "Mode de règlement", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__reference": "Informations de transaction", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__penaltyAmount": "Mnt pénalité", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____title": "Règlements reçus", "@sage/xtrem-finance/page-fragments__base_payment__amount____title": "Montant de règlement", "@sage/xtrem-finance/page-fragments__base_payment__amountBankCurrency____title": "Montant en devise de banque", "@sage/xtrem-finance/page-fragments__base_payment__bankAccount____title": "Compte bancaire", "@sage/xtrem-finance/page-fragments__base_payment__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-finance/page-fragments__base_payment__currency____title": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment__customer____title": "Client", "@sage/xtrem-finance/page-fragments__base_payment__financialSite____title": "Site financier", "@sage/xtrem-finance/page-fragments__base_payment__isVoided____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment__paymentDate____title": "Date de réception", "@sage/xtrem-finance/page-fragments__base_payment__paymentMethod____title": "Mode de règlement", "@sage/xtrem-finance/page-fragments__base_payment__reference____title": "Informations de transaction", "@sage/xtrem-finance/page-fragments__base_payment__supplier____title": "Fournisseur", "@sage/xtrem-finance/page-fragments__base_payment__voidDate____title": "<PERSON><PERSON><PERSON> le", "@sage/xtrem-finance/page-fragments__base_payment__voidText____title": "Texte d'annulation", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__adjustmentAmount": "Mnt régularisation", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__dueDate": "Date échéance", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__postingDate": "Date comptabilisation", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__purchaseDocumentNumber": "N° doc", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__reference": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__dueDate": "Date échéance", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__postingDate": "Date comptabilisation", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__reference": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__salesDocumentNumber": "N° doc", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__currency__id": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__discountAmount": "Mnt escompte", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__origin": "Origine", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__penaltyAmount": "Mnt pénalité", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__signedAmount": "Mnt", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__signedAmountBankCurrency": "Mnt devise banque", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__open_item_discount__discountDate____title": "Date escompte", "@sage/xtrem-finance/page-fragments__open_item_discount__discountType____title": "Type d'escompte", "@sage/xtrem-finance/page-fragments__open_item_discount__displayDiscountPaymentDate____title": "Règlement d'escompte avant le", "@sage/xtrem-finance/page-fragments__open_item_general__businessRelation____title": "Relation commerciale", "@sage/xtrem-finance/page-fragments__open_item_general__companyAmountDueSigned____title": "<PERSON><PERSON> soci<PERSON> dû", "@sage/xtrem-finance/page-fragments__open_item_general__documentNumberLink____title": "Numéro de document", "@sage/xtrem-finance/page-fragments__open_item_general__documentType____title": "Type de document", "@sage/xtrem-finance/page-fragments__open_item_general__dueDate____title": "Date d'échéance", "@sage/xtrem-finance/page-fragments__open_item_general__financialSite____title": "Site financier", "@sage/xtrem-finance/page-fragments__open_item_general__transactionAmountDueSigned____title": "Montant de transaction dû", "@sage/xtrem-finance/page-fragments__open_item_penalty__penaltyPaymentType____title": "Type de pénalité", "@sage/xtrem-finance/page-fragments__payment_information__amount____title": "Montant de règlement", "@sage/xtrem-finance/page-fragments__payment_information__amountBankCurrency____title": "Montant en devise de banque", "@sage/xtrem-finance/page-fragments__payment_information__bankAccount____title": "Compte bancaire", "@sage/xtrem-finance/page-fragments__payment_information__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-finance/page-fragments__payment_information__currency____title": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__country__name": "Pays", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__id": "Code", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__name": "Nom", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__taxIdNumber": "N° TVA", "@sage/xtrem-finance/page-fragments__payment_information__customer____title": "Client", "@sage/xtrem-finance/page-fragments__payment_information__date____title": "Date de réception", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____columns__title__legalCompany__name": "Société", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____placeholder": "Site financier", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____title": "Site financier", "@sage/xtrem-finance/page-fragments__payment_information__paymentMethod____title": "Mode paiement", "@sage/xtrem-finance/page-fragments__payment_information__reference____title": "Informations de transaction", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__country__name": "Pays", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__id": "Code", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__name": "Nom", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__taxIdNumber": "N° TVA", "@sage/xtrem-finance/page-fragments__payment_information__supplier____title": "Fournisseur", "@sage/xtrem-finance/page-fragments__payment_information__type____title": "Type", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__number": "N° facture", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__origin": "Origine", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__postingDate": "Date comptabilisation", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__reference": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__number": "N° facture", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__origin": "Origine", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__postingDate": "Date comptabilisation", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__reference": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__adjustmentAmount": "Mnt régularisation", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__amountDue": "Mnt dû", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__creditAmount": "<PERSON>nt crédit", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__currency__id": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__discountPaymentAmount": "Mnt escompte", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__dueDate": "Date échéance", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__paymentAmount": "Mnt règlement", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__penaltyPaymentAmount": "Mnt pénalité", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__remainingCompanyAmount": "Mnt société dû", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__totalAmount": "Mnt total", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__totalCompanyAmount": "Mnt société total", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__totalCompanyAmountPaid": "Mnt société réglé", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__id__title": "Code fournisseur facturant", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line_4__title": "Type", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line_5__title": "Date facture", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line10__title": "Total HT", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line11__title": "Taxe totale régularisée", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line12__title": "Total taxes", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line13__title": "Date de la facture fournisseur", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line14__title": "Numéro du document fournisseur", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line15__title": "Date d'échéance", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line16__title": "Condition de paiement", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line2__title": "Fournisseur facturant", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line4__title": "Type", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line5__title": "Date de facture", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line6__title": "Site financier", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line7__title": "Total TTC", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line8__title": "Référence", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line9__title": "Origine", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__titleRight__title": "Statut de comptabilisation", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__2": "Brouillon", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__3": "Comptabilisée", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__4": "En cours", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__5": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice____objectTypePlural": "Factures comptables fournisseurs", "@sage/xtrem-finance/pages__accounts_payable_invoice____objectTypeSingular": "Facture comptable fournisseur", "@sage/xtrem-finance/pages__accounts_payable_invoice____subtitle": "Facture auxiliaire fournisseur", "@sage/xtrem-finance/pages__accounts_payable_invoice____title": "Facture comptable fournisseur", "@sage/xtrem-finance/pages__accounts_payable_invoice__billBySupplier____title": "Fournisseur facturant", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____columns__title__decimalDigits": "Décimales", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____columns__title__id": "ISO 4217", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____columns__title__name": "Nom", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____columns__title__symbol": "Symbole", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__dueDate____title": "Date d'échéance", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationApp____title": "Application", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationAppRecordId____title": "Référence intégration comptable", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationAppRecordIdLink____title": "Référence intégration comptable", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationAppUrl____title": "URL intégration finance", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationStatus____title": "Statut intégration comptable", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__columns__title___id": "Code", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__columns__title__id__2": "Code", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__title__5": "Moteur taxe", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__title__6": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__title__legalCompany__name": "Société", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____title": "Site financier", "@sage/xtrem-finance/pages__accounts_payable_invoice__generalSection____title": "Général", "@sage/xtrem-finance/pages__accounts_payable_invoice__goToSysNotificationPage____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__internalFinanceIntegrationStatus____title": "Statut interne", "@sage/xtrem-finance/pages__accounts_payable_invoice__invoiceDate____title": "Date de facture", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__account__composedDescription__title__3": "Description composée", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__currency__id__title": "Nom", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__currency__id__title__2": "ISO 4217", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__currency__id__title__3": "Symbole", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__currency__id__title__4": "Décimales", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__originLine___id__title": "Code", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__account__composedDescription": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__amount": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__amountExcludingTax": "Montant HT", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__amountIncludingTax": "Montant TTC", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__description": "Description", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__financialSite__name": "Site financier", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__lineAmountExcludingTax": "Montant HT", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__lineAmountIncludingTax": "Montant TTC", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__lineType": "Type ligne", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__originLine___id": "Ligne facture", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__recipientSite__name": "Site réception", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__taxAmount": "Montant taxe", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__taxAmountAdjusted": "Montant taxe", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__taxDate": "Date taxe", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__uiTaxes": "Taxes UI", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__dropdownActions__title": "Détails taxe", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__origin____title": "Origine", "@sage/xtrem-finance/pages__accounts_payable_invoice__paymentStatus____title": "Statut de règlement", "@sage/xtrem-finance/pages__accounts_payable_invoice__paymentTerm____title": "Condition de paiement", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDate____title": "Date de comptabilisation", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__financeIntegrationAppRecordId": "Réf. intégration comptable", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__postingStatus": "Statut", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__targetDocumentNumber": "N° doc", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__targetDocumentType": "Type doc", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____title": "Comptabilisation", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingMessageBlock____title": "<PERSON>é<PERSON> de l'erreur", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingSection____title": "Comptabilisation", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingStatus____title": "Statut de comptabilisation", "@sage/xtrem-finance/pages__accounts_payable_invoice__rateDescription____title": "Cours", "@sage/xtrem-finance/pages__accounts_payable_invoice__reference____title": "Référence", "@sage/xtrem-finance/pages__accounts_payable_invoice__supplierDocumentDate____title": "Date facture fournisseur", "@sage/xtrem-finance/pages__accounts_payable_invoice__supplierDocumentNumber____title": "N° facture fournisseur", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__columns__account__composedDescription__title__3": "Description composée", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__columns__originLine___id__title": "Code", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__account__composedDescription": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__amount": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__financialSite__name": "Site financier", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__recipientSite__name": "Site consommation", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__taxDetail": "Détails de taxe", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__taxLineTaxAmount": "Montant taxe", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__dropdownActions__title": "Détails de taxe", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____title": "Lignes des détails de taxe", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__postfix__taxRate": "%", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title___sortValue": "<PERSON><PERSON> de tri", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__tax": "Taxe", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxableAmount": "Base imposable", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxAmountAdjusted": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxCategory": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxRate": "Cours", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalAmountExcludingTax____title": "Total calculé HT", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalAmountIncludingTax____title": "Total calculé TTC", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalsSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalsSectionTaxTotalsBlock____title": "", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalTaxAmount____title": "Taxe totale calculée", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalTaxAmountAdjusted____title": "Taxe totale calculée régularisée", "@sage/xtrem-finance/pages__accounts_payable_invoice__type____title": "Type", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__businessEntityId__title": "Code fournisseur", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__closeReason__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__closeText__title": "Texte de solde", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__companyAmountDueSigned__title": "<PERSON><PERSON> soci<PERSON> dû", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__companyAmountPaid__title": "<PERSON><PERSON> soci<PERSON> ré<PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__companyCurrency__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__currency__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__documentType__title": "Type de document", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__financialSite__title": "Site financier", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__forcedAmountPaid__title": "<PERSON><PERSON> <PERSON><PERSON> r<PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__line2__title": "Nom fournisseur", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__line2Right__title": "Date d'échéance", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__remainingCompanyAmount__title": "<PERSON><PERSON> soci<PERSON> restant", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__remainingTransactionAmount__title": "Montant de transaction restant", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__title__title": "Numéro de document", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__titleRight__title": "Statut", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__transactionAmountDueSigned__title": "Montant de transaction dû", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__transactionAmountPaid__title": "Montant de transaction réglé", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__optionsMenu__title": "Toutes", "@sage/xtrem-finance/pages__accounts_payable_open_item____objectTypePlural": "Échéance comptable fournisseur", "@sage/xtrem-finance/pages__accounts_payable_open_item____objectTypeSingular": "Échéance comptable fournisseur", "@sage/xtrem-finance/pages__accounts_payable_open_item____title": "Éch<PERSON><PERSON> fournis<PERSON>ur", "@sage/xtrem-finance/pages__accounts_payable_open_item__companyAmountDueSigned____title": "<PERSON><PERSON> soci<PERSON> dû", "@sage/xtrem-finance/pages__accounts_payable_open_item__documentNumberLink____title": "Numéro de document", "@sage/xtrem-finance/pages__accounts_payable_open_item__documentType____title": "Type de document", "@sage/xtrem-finance/pages__accounts_payable_open_item__dueDate____title": "Date d'échéance", "@sage/xtrem-finance/pages__accounts_payable_open_item__financialSite____title": "Site financier", "@sage/xtrem-finance/pages__accounts_payable_open_item__forcedAmountPaidSigned____title": "<PERSON><PERSON> <PERSON><PERSON> r<PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item__generalSection____title": "Général", "@sage/xtrem-finance/pages__accounts_payable_open_item__status____title": "Statut", "@sage/xtrem-finance/pages__accounts_payable_open_item__supplier____title": "Fournisseur", "@sage/xtrem-finance/pages__accounts_payable_open_item__transactionAmountDueSigned____title": "Montant de transaction dû", "@sage/xtrem-finance/pages__accounts_payable_open_item__transactionAmountPaidSigned____title": "Montant total réglé", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__bulkActions__title": "Solder échéances", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__businessEntityId__title": "Code fournisseur", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__closeReason__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__closeText__title": "Texte de solde", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__companyAmountDueSigned__title": "<PERSON><PERSON> soci<PERSON> dû", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__companyAmountPaid__title": "<PERSON><PERSON> soci<PERSON> ré<PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__companyCurrency__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__currency__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__displayDiscountPaymentDate__title": "Règlement d'escompte avant le", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__documentType__title": "Type de document", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__financialSite__title": "Site financier", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__forcedAmountPaid__title": "<PERSON><PERSON> <PERSON><PERSON> r<PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__line2__title": "Nom du fournisseur", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__line2Right__title": "Date d'échéance", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__remainingCompanyAmount__title": "<PERSON><PERSON> soci<PERSON> restant", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__remainingTransactionAmount__title": "Montant transaction restant", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__title__title": "Numéro de document", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__titleRight__title": "Statut", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__transactionAmountDueSigned__title": "Montant de transaction dû", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__transactionAmountPaid__title": "Montant de transaction réglé", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title": "Pas entièrement réglée", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__2": "Tous les statuts", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__3": "Non réglée", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__4": "Partiellement rég<PERSON>e", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__5": "Totalement réglée", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____objectTypePlural": "Initialiser règlements fournisseurs", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____objectTypeSingular": "Initialiser règlement fournisseur", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____title": "Initialiser règlement fournisseur", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__closeReason____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__closeText____title": "Texte de solde", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__forcedAmountPaidSigned____title": "<PERSON><PERSON> <PERSON><PERSON> r<PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__generalSection____title": "Général", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__save____title": "Enregistrer", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__transactionAmountPaidSigned____title": "Montant total réglé", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line_4__title": "Site financier", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line13__title": "Date de règlement", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line2__title": "Description", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line2Right__title": "Date de comptabilisation", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line4__title": "Site financier", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line7__title": "Client", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line8__title": "<PERSON><PERSON> d'a<PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line9__title": "Référence", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__titleRight__title": "Statut de comptabilisation", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title": "Toutes", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__2": "Brouillon", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__3": "Comptabilisée", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__4": "En cours", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__5": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance____objectTypePlural": "Avances clients", "@sage/xtrem-finance/pages__accounts_receivable_advance____objectTypeSingular": "Avance client", "@sage/xtrem-finance/pages__accounts_receivable_advance____title": "Avance client", "@sage/xtrem-finance/pages__accounts_receivable_advance__advanceAmount____title": "<PERSON><PERSON> d'a<PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance__bankAccount____title": "Banque", "@sage/xtrem-finance/pages__accounts_receivable_advance__description____title": "Description", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationApp____title": "Application", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationAppRecordId____title": "Référence intégration comptable", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationAppRecordIdLink____title": "Référence intégration comptable", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationAppUrl____title": "URL intégration finance", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationStatus____title": "Statut d'intégration comptable", "@sage/xtrem-finance/pages__accounts_receivable_advance__financialSite____columns__title__legalCompany__name": "Société", "@sage/xtrem-finance/pages__accounts_receivable_advance__financialSite____title": "Site financier", "@sage/xtrem-finance/pages__accounts_receivable_advance__generalSection____title": "Général", "@sage/xtrem-finance/pages__accounts_receivable_advance__internalFinanceIntegrationStatus____title": "Statut interne", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____columns__title__account__composedDescription": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____columns__title__advanceAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____columns__title__description": "Description", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance__paymentDate____title": "Date de règlement", "@sage/xtrem-finance/pages__accounts_receivable_advance__payToCustomerName____title": "Client", "@sage/xtrem-finance/pages__accounts_receivable_advance__postingDate____title": "Date de réception", "@sage/xtrem-finance/pages__accounts_receivable_advance__postingStatus____title": "Statut de comptabilisation", "@sage/xtrem-finance/pages__accounts_receivable_advance__reference____title": "Référence", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__billToCustomerId__title": "Code du client facturé", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line_4__title": "Type", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line_5__title": "Date facture", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line10__title": "Origine", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line11__title": "Total HT", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line12__title": "Taxe totale régularisée", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line13__title": "Date d'échéance", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line14__title": "Condition de paiement", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line2__title": "Description", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line2Right__title": "Date de comptabilisation", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line4__title": "Type", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line5__title": "Date de facture", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line6__title": "Site financier", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line7__title": "Client facturé", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line8__title": "Total TTC", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line9__title": "Référence", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__titleRight__title": "Statut de comptabilisation", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__2": "Brouillon", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__3": "Comptabilisée", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__4": "En cours", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__5": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice____objectTypePlural": "Factures clients", "@sage/xtrem-finance/pages__accounts_receivable_invoice____objectTypeSingular": "Facture client", "@sage/xtrem-finance/pages__accounts_receivable_invoice____subtitle": "Facture auxiliaire client", "@sage/xtrem-finance/pages__accounts_receivable_invoice____title": "Facture comptable client", "@sage/xtrem-finance/pages__accounts_receivable_invoice__billToCustomer____columns__title__businessEntity__id": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__billToCustomer____columns__title__businessEntity__name": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__billToCustomer____title": "Client facturé", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____columns__title__decimalDigits": "Décimales", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____columns__title__id": "ISO 4217", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____columns__title__name": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____columns__title__symbol": "Symbole", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__dueDate____title": "Date d'échéance", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationApp____title": "Application", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationAppRecordId____title": "Référence intégration comptable", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationAppRecordIdLink____title": "Référence intégration comptable", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationAppUrl____title": "URL intégration finance", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationStatus____title": "Statut intégration comptable", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__columns__title___id": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__columns__title__decimalDigits": "Décimales", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__columns__title__id__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__columns__title__symbol": "Symbole", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__title__5": "Moteur taxe", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__title__6": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__title__legalCompany__name": "Société", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____title": "Site financier", "@sage/xtrem-finance/pages__accounts_receivable_invoice__generalSection____title": "Général", "@sage/xtrem-finance/pages__accounts_receivable_invoice__goToSysNotificationPage____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__internalFinanceIntegrationStatus____title": "Statut interne", "@sage/xtrem-finance/pages__accounts_receivable_invoice__invoiceDate____title": "Date de facture", "@sage/xtrem-finance/pages__accounts_receivable_invoice__isPrinted____title": "Imprimée", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__account__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__account__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__account__composedDescription__title__3": "Description composée", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__currency__id__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__currency__id__title__2": "ISO 4217", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__currency__id__title__3": "Symbole", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__currency__id__title__4": "Décimales", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension01__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension01__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension02__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension02__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension03__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension03__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension04__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension04__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension05__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension05__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension06__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension06__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension07__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension07__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension08__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension08__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension09__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension09__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension10__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension10__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension11__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension11__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension12__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension12__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension13__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension13__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension14__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension14__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension15__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension15__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension16__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension16__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension17__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension17__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension18__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension18__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension19__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension19__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension20__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension20__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__employee__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__employee__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__originLine___id__title": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__project__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__project__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__task__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__task__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__account__composedDescription": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__amount": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__amountExcludingTax": "Montant HT", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__amountIncludingTax": "Montant TTC", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__description": "Description", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__financialSite__name": "Site financier", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__lineAmountExcludingTax": "Montant HT", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__lineAmountIncludingTax": "Montant TTC", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__lineType": "Type ligne", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__originLine___id": "Ligne facture", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__providerSite__name": "Site de fourniture", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__taxAmount": "Montant de taxe", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__taxDate": "Date taxe", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__dropdownActions__title": "Détails de taxe", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__origin____title": "Origine", "@sage/xtrem-finance/pages__accounts_receivable_invoice__paymentStatus____title": "Statut de règlement", "@sage/xtrem-finance/pages__accounts_receivable_invoice__paymentTerm____title": "Condition de paiement", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDate____title": "Date de comptabilisation", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__financeIntegrationAppRecordId": "Réf. intégration comptable", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__postingStatus": "Statut", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__targetDocumentNumber": "N° doc", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__targetDocumentType": "Type doc", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____title": "Comptabilisation", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingMessageBlock____title": "<PERSON>é<PERSON> de l'erreur", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingSection____title": "Comptabilisation", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingStatus____title": "Statut de comptabilisation", "@sage/xtrem-finance/pages__accounts_receivable_invoice__rateDescription____title": "Cours", "@sage/xtrem-finance/pages__accounts_receivable_invoice__reference____title": "Référence", "@sage/xtrem-finance/pages__accounts_receivable_invoice__status____title": "Statut", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__account__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__account__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__account__composedDescription__title__3": "Description composée", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension01__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension01__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension02__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension02__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension03__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension03__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension04__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension04__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension05__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension05__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension06__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension06__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension07__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension07__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension08__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension08__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension09__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension09__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension10__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension10__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension11__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension11__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension12__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension12__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension13__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension13__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension14__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension14__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension15__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension15__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension16__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension16__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension17__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension17__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension18__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension18__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension19__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension19__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension20__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension20__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__employee__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__employee__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__originLine___id__title": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__project__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__project__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__task__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__task__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__account__composedDescription": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__amount": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__financialSite__name": "Site financier", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__providerSite__name": "Site fourniture", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__taxDetail": "Détails taxe", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__taxLineTaxAmount": "Montant taxe", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____title": "Lignes des détails de taxe", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__postfix__taxRate": "%", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__tax": "Taxe", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxableAmount": "Base imposable", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxCategory": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxRate": "Cours", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalAmountExcludingTax____title": "Total HT", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalAmountIncludingTax____title": "Total TTC", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalsSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalsSectionTaxTotalsBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalTaxAmount____title": "Total taxes", "@sage/xtrem-finance/pages__accounts_receivable_invoice__type____title": "Type", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__businessEntityId__title": "Code client", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__closeReason__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__closeText__title": "Texte de fermeture", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__companyAmountDueSigned__title": "<PERSON><PERSON> soci<PERSON> dû", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__companyAmountPaid__title": "<PERSON><PERSON> soci<PERSON> ré<PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__companyCurrency__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__currency__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__displayDiscountPaymentDate__title": "Règlement d'escompte avant le", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__documentType__title": "Type de document", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__financialSite__title": "Site financier", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__forcedAmountPaid__title": "<PERSON><PERSON> <PERSON><PERSON> r<PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__line2__title": "Nom client", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__line2Right__title": "Date d'échéance", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__remainingCompanyAmount__title": "<PERSON><PERSON> soci<PERSON> restant", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__remainingTransactionAmount__title": "Montant transaction restant", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__title__title": "Numéro de document", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__titleRight__title": "Statut", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__transactionAmountDueSigned__title": "Montant de transaction dû", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__transactionAmountPaid__title": "Montant de transaction réglé", "@sage/xtrem-finance/pages__accounts_receivable_open_item____objectTypePlural": "Échéances comptables clients", "@sage/xtrem-finance/pages__accounts_receivable_open_item____objectTypeSingular": "Échéance client", "@sage/xtrem-finance/pages__accounts_receivable_open_item____title": "Échéance client", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeReason____columns__title__id": "Code", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeReason____columns__title__name": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeReason____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeText____title": "Texte de fermeture", "@sage/xtrem-finance/pages__accounts_receivable_open_item__customer____helperText": "Code", "@sage/xtrem-finance/pages__accounts_receivable_open_item__discount_amount": "<PERSON>ant escompte", "@sage/xtrem-finance/pages__accounts_receivable_open_item__discount_percentage": "Pourcentage escompte", "@sage/xtrem-finance/pages__accounts_receivable_open_item__financialSite____helperText": "Code", "@sage/xtrem-finance/pages__accounts_receivable_open_item__forcedAmountPaidSigned____title": "<PERSON><PERSON> <PERSON><PERSON> r<PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item__generalSection____title": "Général", "@sage/xtrem-finance/pages__accounts_receivable_open_item__penalty_amount": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item__penalty_percentage": "Pourcentage pénalité", "@sage/xtrem-finance/pages__accounts_receivable_open_item__transactionAmountPaidSigned____title": "Montant total réglé", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__bulkActions__title": "Solder échéances", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__businessEntityId__title": "Code client", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__closeReason__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__closeText__title": "Texte de fermeture", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__companyAmountDueSigned__title": "<PERSON><PERSON> soci<PERSON> dû", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__companyAmountPaid__title": "<PERSON><PERSON> soci<PERSON> ré<PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__companyCurrency__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__currency__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__displayDiscountPaymentDate__title": "Règlement d'escompte avant le", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__documentType__title": "Type document", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__financialSite__title": "Site financier", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__forcedAmountPaid__title": "<PERSON><PERSON> <PERSON><PERSON> r<PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__line2__title": "Nom client", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__line2Right__title": "Date d'échéance", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__remainingCompanyAmount__title": "<PERSON><PERSON> soci<PERSON> restant", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__remainingTransactionAmount__title": "Montant transaction restant", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__status__title": "Statut", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__title__title": "Numéro document", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__titleRight__title": "Statut", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__transactionAmountDueSigned__title": "Montant de transaction dû", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__transactionAmountPaid__title": "Montant de transaction réglé", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title": "Pas entièrement réglée", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__2": "Tous les statuts", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__3": "Non réglée", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__4": "Partiellement rég<PERSON>e", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__5": "Totalement réglée", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____objectTypePlural": "Initialiser règlements clients", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____objectTypeSingular": "Initialiser règlement client", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____title": "Initialiser règlement client", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeReason____columns__title__id": "Code", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeReason____columns__title__name": "Nom", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeReason____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeText____title": "Texte de fermeture", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__forced_amount_paid_wrongly_negative": "Le montant forcé réglé doit être positif.", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__forced_amount_paid_wrongly_positive": "Le montant forcé réglé doit être négatif.", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__forcedAmountPaidSigned____title": "<PERSON><PERSON> <PERSON><PERSON> r<PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__generalSection____title": "Général", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__save____title": "Enregistrer", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__transactionAmountPaidSigned____title": "Montant total réglé", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__wrong_forced_amount_paid": "Le montant forcé réglé doit être entre 0 et {{maxForcedAmount}}.", "@sage/xtrem-finance/pages__datev_export____navigationPanel__dropdownActions__title": "Extraire", "@sage/xtrem-finance/pages__datev_export____navigationPanel__dropdownActions__title__2": "Exporter", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__datevConsultantNumber__title": "Numéro consultant", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__datevcustomerNumber__title": "Numéro de client", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__doAccounts__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__doCustomersSuppliers__title": "Clients et fournisseurs", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__doJournalEntries__title": "Écritures", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__fiscalYearStart__title": "Début année fiscale", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__id__title": "Code", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__isLocked__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__line2__title": "Date de début", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__line2Right__title": "Date de fin", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__timeStamp__title": "Extrait le", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__title__title": "Société", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__titleRight__title": "Statut", "@sage/xtrem-finance/pages__datev_export____objectTypePlural": "Exports DATEV", "@sage/xtrem-finance/pages__datev_export____objectTypeSingular": "Export DATEV", "@sage/xtrem-finance/pages__datev_export____title": "Export DATEV", "@sage/xtrem-finance/pages__datev_export__accountsSection____title": "Comptes extraits", "@sage/xtrem-finance/pages__datev_export__accountsWithoutDatevId____columns__title__id": "Code", "@sage/xtrem-finance/pages__datev_export__accountsWithoutDatevId____columns__title__name": "Nom", "@sage/xtrem-finance/pages__datev_export__accountsWithoutDatevId____title": "Comptes sans code DATEV", "@sage/xtrem-finance/pages__datev_export__acountsWithoutDatevIdSection____title": "Comptes sans code DATEV", "@sage/xtrem-finance/pages__datev_export__businessRelationsSection____title": "Clients et fournisseurs extraits", "@sage/xtrem-finance/pages__datev_export__createDatevExport____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevId____columns__title__id": "Code", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevId____columns__title__name": "Nom", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevId____title": "Clients sans code DATEV", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevIdSection____title": "Clients sans code DATEV", "@sage/xtrem-finance/pages__datev_export__datevConsultantNumber____title": "Numéro du consultant DATEV", "@sage/xtrem-finance/pages__datev_export__datevCustomerNumber____title": "Numéro client DATEV", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____columns__title__account__id": "Code", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____columns__title__datevId": "Code DATEV", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____columns__title__name": "Nom", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____title": "Comptes extraits", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__businessRelation__id": "Code", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__city": "Ville", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__country__name": "Pays", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__datevId": "Code DATEV", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__name": "Nom", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__postcode": "Code postal", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__street": "Rue", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__taxIdNumber": "N° TVA", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____title": "Clients et fournisseurs extraits", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__businessEntityTaxIdNumber": "Code taxe entité commerciale", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__companyFxRate": "Cours", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__companyValue": "Montant en devise société", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__datevAccountId": "ID compte DATEV", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__datevContraAccountId": "Code de compte de contrepartie DATEV", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__datevSign": "<PERSON>s", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__description": "Description", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__dimension1": "Section 1", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__dimension2": "Section 2", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__locked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__number": "N°", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__postingDate": "Date comptabilisation", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__postingKey": "Clé comptabilisation", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__siteTaxIdNumber": "SIRET", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__supplierDocumentNumber": "N° doc. fournisseur", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__transactionValue": "Mnt transaction", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____title": "Lignes d'écriture extraites", "@sage/xtrem-finance/pages__datev_export__dimensionTypeText1____title": "Axe 1", "@sage/xtrem-finance/pages__datev_export__dimensionTypeText2____title": "Axe 2", "@sage/xtrem-finance/pages__datev_export__doAccounts____title": "Exporter comptes", "@sage/xtrem-finance/pages__datev_export__doCustomersSuppliers____title": "Exporter clients et fournisseurs", "@sage/xtrem-finance/pages__datev_export__doJournalEntries____title": "Exporter écritures", "@sage/xtrem-finance/pages__datev_export__endDate____title": "Date fin", "@sage/xtrem-finance/pages__datev_export__export____title": "Exporter", "@sage/xtrem-finance/pages__datev_export__extract____title": "Extraire", "@sage/xtrem-finance/pages__datev_export__fiscalYearStart____title": "Début année fiscale", "@sage/xtrem-finance/pages__datev_export__headerSection____title": "Section d'en-tête", "@sage/xtrem-finance/pages__datev_export__id____title": "Code", "@sage/xtrem-finance/pages__datev_export__isLocked____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export__journalEntryLineSection____title": "Lignes d'écriture extraites", "@sage/xtrem-finance/pages__datev_export__notification_export_sent": "Demande d'export DATEV envoyée.", "@sage/xtrem-finance/pages__datev_export__notification_success": "Demande extraction DATEV envoyée", "@sage/xtrem-finance/pages__datev_export__skrCoaString____title": "SKR", "@sage/xtrem-finance/pages__datev_export__startDate____title": "Date début", "@sage/xtrem-finance/pages__datev_export__status____title": "Statut", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevId____columns__title__id": "Code", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevId____columns__title__name": "Nom", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevId____title": "Fournisseurs sans code DATEV", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevIdSection____title": "Fournisseurs sans code DATEV", "@sage/xtrem-finance/pages__datev_export__timeStamp____title": "Date dernière extraction", "@sage/xtrem-finance/pages__datev_export_parameters__datev_configuration": "Configuration DATEV", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_date_range": "La date de fin doit être postérieure à la date de début.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_dimension_types": "L'axe 2 doit être différente de l'axe 1.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_end_date": "La date de fin ne doit pas dépasser un an après le début de l'année fiscale.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_end_for_begin_of_fiscal_year": "La date de fin doit postérieure au début de l'année fiscale.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_start_for_begin_of_fiscal_year": "La  date de début doit être postérieure au début de l'année fiscale.", "@sage/xtrem-finance/pages__datev_export_settings____title": "Paramétrage export DATEV", "@sage/xtrem-finance/pages__datev_export_settings__company____columns__title__id": "Code", "@sage/xtrem-finance/pages__datev_export_settings__company____columns__title__name": "Nom", "@sage/xtrem-finance/pages__datev_export_settings__criteriaBlock____title": "Critères", "@sage/xtrem-finance/pages__datev_export_settings__dateRange____title": "Borne dates", "@sage/xtrem-finance/pages__datev_export_settings__dimensionTypeOrAttribute1____title": "Axe 1", "@sage/xtrem-finance/pages__datev_export_settings__dimensionTypeOrAttribute2____title": "Axe 2", "@sage/xtrem-finance/pages__datev_export_settings__doAccounts____title": "Exporter comptes", "@sage/xtrem-finance/pages__datev_export_settings__doCustomersSuppliers____title": "Exporter clients et fournisseurs", "@sage/xtrem-finance/pages__datev_export_settings__doJournalEntries____title": "Exporter écritures", "@sage/xtrem-finance/pages__datev_export_settings__endDate____title": "Date fin", "@sage/xtrem-finance/pages__datev_export_settings__fiscalYearStart____title": "Début année fiscale", "@sage/xtrem-finance/pages__datev_export_settings__id____title": "Code", "@sage/xtrem-finance/pages__datev_export_settings__isLocked____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export_settings__mainSection____title": "Général", "@sage/xtrem-finance/pages__datev_export_settings__startDate____title": "Date début", "@sage/xtrem-finance/pages__generate_journal_entries____title": "G<PERSON><PERSON>rer écritures", "@sage/xtrem-finance/pages__generate_journal_entries__create_button_text": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__generate_journal_entries__dateFrom____title": "Date de début", "@sage/xtrem-finance/pages__generate_journal_entries__dateTo____title": "Date de fin", "@sage/xtrem-finance/pages__generate_journal_entries__documentType____title": "Type de document", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____columns__title__legalCompany__name": "Société", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____lookupDialogTitle": "Sélectionner le site financier", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____placeholder": "Sélectionner...", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____title": "Site financier", "@sage/xtrem-finance/pages__generate_journal_entries__generalBlock____title": "Critères", "@sage/xtrem-finance/pages__generate_journal_entries__generalSection____title": "Général", "@sage/xtrem-finance/pages__generate_journal_entries__recurring": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__generate_journal_entries__run_once": "Exécuter une fois", "@sage/xtrem-finance/pages__generate_journal_entries__schedule_button_text": "Planifier", "@sage/xtrem-finance/pages__generate_pre_journal_entries____title": "Générer pré-écritures", "@sage/xtrem-finance/pages__generate_pre_journal_entries__dateFrom____title": "Date de début", "@sage/xtrem-finance/pages__generate_pre_journal_entries__dateTo____title": "Date de fin", "@sage/xtrem-finance/pages__generate_pre_journal_entries__documentType____title": "Type de pièce", "@sage/xtrem-finance/pages__generate_pre_journal_entries__financialSite____columns__title__id": "ID", "@sage/xtrem-finance/pages__generate_pre_journal_entries__financialSite____columns__title__name": "Nom", "@sage/xtrem-finance/pages__generate_pre_journal_entries__financialSite____placeholder": "Sélectionner...", "@sage/xtrem-finance/pages__generate_pre_journal_entries__financialSite____title": "Site", "@sage/xtrem-finance/pages__generate_pre_journal_entries__generalBlock____title": "Critères", "@sage/xtrem-finance/pages__generate_pre_journal_entries__generalSection____title": "Général", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line_4__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line_5__title": "Type de document", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line2Right__title": "Date de comptabilisation", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line3__title": "Site financier", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line4__title": "Journal", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line5__title": "Type de document", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line6__title": "Référence", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line7__title": "Origine", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__titleRight__title": "Statut de comptabilisation", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__optionsMenu__title__2": "En attente", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__optionsMenu__title__3": "Comptabilisée", "@sage/xtrem-finance/pages__journal_entry____objectTypePlural": "Écritures", "@sage/xtrem-finance/pages__journal_entry____objectTypeSingular": "Écriture", "@sage/xtrem-finance/pages__journal_entry____title": "Écriture", "@sage/xtrem-finance/pages__journal_entry___id____title": "Code", "@sage/xtrem-finance/pages__journal_entry__accountingIntegrationBlock____title": "Intégration comptable", "@sage/xtrem-finance/pages__journal_entry__description____title": "Description", "@sage/xtrem-finance/pages__journal_entry__documentType____title": "Type de document", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationApp____title": "Application", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationAppRecordId____title": "Référence intégration comptable", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationAppRecordIdLink____title": "Référence intégration comptable", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationAppUrl____title": "URL intégration finance", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationStatus____title": "Statut intégration comptable", "@sage/xtrem-finance/pages__journal_entry__financialSite____columns__lookupDialogTitle__legalCompany__name": "Sélectionner la société", "@sage/xtrem-finance/pages__journal_entry__financialSite____columns__title__legalCompany__name": "Société", "@sage/xtrem-finance/pages__journal_entry__financialSite____title": "Site financier", "@sage/xtrem-finance/pages__journal_entry__generalSection____title": "Général", "@sage/xtrem-finance/pages__journal_entry__internalFinanceIntegrationStatus____title": "Statut interne", "@sage/xtrem-finance/pages__journal_entry__journal____title": "Journal", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__account__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__account__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension01__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension01__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension02__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension02__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension03__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension03__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension04__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension04__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension05__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension05__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension06__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension06__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension07__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension07__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension08__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension08__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension09__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension09__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension10__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension10__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension11__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension11__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension12__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension12__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension13__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension13__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension14__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension14__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension15__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension15__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension16__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension16__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension17__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension17__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension18__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension18__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension19__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension19__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension20__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension20__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__employee__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__employee__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__project__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__project__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__task__composedDescription__title": "Nom", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__task__composedDescription__title__2": "Code", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__account__composedDescription": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__businessEntity__name": "Entité commerciale", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__commonReference": "Référence commune", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__companyAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__companyCredit": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__companyDebit": "<PERSON><PERSON><PERSON> soci<PERSON>", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__contraAccount": "Compte  con<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__description": "Description", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__dueDate": "Date d'échéance", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__financialSite__name": "Site financier", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__fxRateDate": "Date du cours de change", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__rateDescription": "Cours de change", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__tax__name": "Taxe", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__taxDate": "Date taxe", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionAmount": "Montant transaction", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionCredit": "Crédit transaction", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionCurrency__id": "Devise transaction", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionDebit": "Débit transaction", "@sage/xtrem-finance/pages__journal_entry__lines____levels__dropdownActions__title": "Détails de taxe", "@sage/xtrem-finance/pages__journal_entry__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__origin____title": "Origine", "@sage/xtrem-finance/pages__journal_entry__postingDate____title": "Date de comptabilisation", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title___id": "Code", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__documentNumber": "Numéro de document", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__documentSysId": "Code document", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__documentType": "Type de document", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__financeIntegrationAppRecordId": "Réf. intégration comptable", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__financeIntegrationAppRecordId__2": "Réf. intégration comptable", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__message": "Message", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__postingStatus": "Statut", "@sage/xtrem-finance/pages__journal_entry__postingDetails____title": "Comptabilisation", "@sage/xtrem-finance/pages__journal_entry__postingMessageBlock____title": "<PERSON><PERSON><PERSON> de l'erreur", "@sage/xtrem-finance/pages__journal_entry__postingSection____title": "Comptabilisation", "@sage/xtrem-finance/pages__journal_entry__postingSectionBlock____title": "Comptabilisation", "@sage/xtrem-finance/pages__journal_entry__postingStatus____title": "Statut de comptabilisation", "@sage/xtrem-finance/pages__journal_entry__reference____title": "Référence", "@sage/xtrem-finance/pages__journal_entry_inquiry____title": "Consultation d'écriture", "@sage/xtrem-finance/pages__journal_entry_inquiry__companyFilter____lookupDialogTitle": "Sélectionner les sociétés", "@sage/xtrem-finance/pages__journal_entry_inquiry__companyFilter____title": "Sociétés", "@sage/xtrem-finance/pages__journal_entry_inquiry__dateFrom____title": "Date de début", "@sage/xtrem-finance/pages__journal_entry_inquiry__dateTo____title": "Date de fin", "@sage/xtrem-finance/pages__journal_entry_inquiry__filtersBlock____title": "Critères", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__account__id": "Code comptable", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__account__name": "Nom compte", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__blank": "Rapprochement", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__blank__2": "Date rappro", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__businessEntity__id": "Code entité com.", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__businessEntity__name": "Nom entité com.", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__commonReference": "Référence commune", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__companyAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__contraAccount": "Compte con<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__dueDate": "Date échéance", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__financialSite__id": "Code site", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__financialSite__legalCompany__name": "Nom société", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__inquiryDescription": "Description", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__inquiryTransactionCurrency__id": "Devise transaction", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__journal__id": "Code journal", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__journal__name": "Nom journal", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__number": "N°", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__postingDate": "Date comptabilisation", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__postingDate__2": "Date doc réf", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__reference": "Référence", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__numericSign": "<PERSON>s", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__signedTransactionAmount": "Mnt transaction", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__tax__name": "Taxe", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__taxDate": "Date taxe", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__validationDate": "Date validation", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalFilter____lookupDialogTitle": "Sélectionner les journaux", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalFilter____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__amount__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__amountBankCurrency__title": "Montant en devise de banque", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__amountCompanyCurrency__title": "Montant en devise société", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__bankAccount__title": "Compte bancaire", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__bankCurrency__title": "Devise de compte bancaire", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__businessRelationId__title": "Code entité commerciale", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__companyCurrency__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__currency__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__financialSite__title": "Site financier", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__isVoided__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__line2__title": "Nom entité commerciale", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__line2Right__title": "Date de règlement", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__paymentMethod__title": "Mode de règlement", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__reference__title": "Référence", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__titleRight__title": "Statut", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__type__title": "Type", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__voidDate__title": "<PERSON><PERSON><PERSON> le", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__voidText__title": "Texte d'annulation", "@sage/xtrem-finance/pages__payment____navigationPanel__optionsMenu__title": "Règlements comptabilisés", "@sage/xtrem-finance/pages__payment____navigationPanel__optionsMenu__title__2": "Règlements annulés", "@sage/xtrem-finance/pages__payment____navigationPanel__optionsMenu__title__3": "Tous les règlements", "@sage/xtrem-finance/pages__payment____objectTypePlural": "Règlements", "@sage/xtrem-finance/pages__payment____objectTypeSingular": "Règlement", "@sage/xtrem-finance/pages__payment____title": "Règlement", "@sage/xtrem-finance/pages__payment__cancel_void_button": "Annuler", "@sage/xtrem-finance/pages__payment__cancelPayment____title": "Annuler", "@sage/xtrem-finance/pages__payment__confirm_void_button": "Confirmer", "@sage/xtrem-finance/pages__payment__createPayment____title": "Confirmer", "@sage/xtrem-finance/pages__payment__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__payment__mainSection____title": "Général", "@sage/xtrem-finance/pages__payment__paymentInformationBlock____title": "Informations de règlement", "@sage/xtrem-finance/pages__payment__voidPayment____title": "Annuler", "@sage/xtrem-finance/pages__payment__voidPaymentDate____helperText": "Ann<PERSON>r le règlement en date du", "@sage/xtrem-finance/pages__payment__voidPaymentDate____title": "Date", "@sage/xtrem-finance/pages__payment__voidPaymentText____title": "Texte", "@sage/xtrem-finance/pages__payment__voidSection____title": "Annuler règlement", "@sage/xtrem-finance/pages__pre_journal____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-finance/pages__pre_journal____navigationPanel__optionsMenu__title__2": "En attente", "@sage/xtrem-finance/pages__pre_journal____navigationPanel__optionsMenu__title__3": "Comptabilisée", "@sage/xtrem-finance/pages__pre_journal____subtitle": "Pré-écriture", "@sage/xtrem-finance/pages__pre_journal____title": "Pré-écriture", "@sage/xtrem-finance/pages__pre_journal__description____title": "Description", "@sage/xtrem-finance/pages__pre_journal__documentType____title": "Type de document", "@sage/xtrem-finance/pages__pre_journal__financialSite____columns__columns__legalCompany__name__title": "Législation", "@sage/xtrem-finance/pages__pre_journal__financialSite____columns__title__legalCompany__name": "Société", "@sage/xtrem-finance/pages__pre_journal__financialSite____title": "Site financier", "@sage/xtrem-finance/pages__pre_journal__generalSection____title": "Général", "@sage/xtrem-finance/pages__pre_journal__journal____title": "Journal", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__account__composedDescription__title": "ID", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__account__composedDescription__title__2": "Nom", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__account__composedDescription__title__3": "Description composée", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title": "Nom", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__2": "Code ISO 4217", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__3": "Symbole", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__4": "Décimales", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__5": "Nom", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__6": "ISO 4217", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__7": "Symbole", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__8": "Décimales", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title": "Nom", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__2": "Code ISO 4217", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__3": "Symbole", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__4": "Décimales", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__5": "Nom", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__6": "ISO 4217", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__7": "Symbole", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__8": "Décimales", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__account__composedDescription": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__businessEntity__name": "Entité commerciale", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__commonReference": "Référence commune", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyCredit": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyCurrency__id": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyCurrency__id__2": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyDebit": "<PERSON><PERSON><PERSON> soci<PERSON>", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__description": "Description", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__dueDate": "Date d'échéance", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__financialSite__name": "Site financier", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__fxRateDate": "Date cours de change", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__rateDescription": "Cours de change", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__tax__name": "Taxe", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__taxDate": "Date taxe", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionAmount": "Montant transaction", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionCredit": "Crédit transaction", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionCurrency__id": "Devise transaction", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionCurrency__id__2": "Devise transaction", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionDebit": "Débit transaction", "@sage/xtrem-finance/pages__pre_journal__linesBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__origin____title": "Origine", "@sage/xtrem-finance/pages__pre_journal__postingDate____title": "Date de comptabilisation", "@sage/xtrem-finance/pages__pre_journal__reference____title": "Référence", "@sage/xtrem-finance/pages__pre_journal__status____title": "Statut", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__amount__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__amountBankCurrency__title": "Montant en devise de banque", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__amountCompanyCurrency__title": "Montant en devise société", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__bankAccount__title": "Compte bancaire", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__bankCurrency__title": "Devise de compte bancaire", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__businessRelationId__title": "Code entité commerciale", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__companyCurrency__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__currency__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__financialSite__title": "Site financier", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__isVoided__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__line2__title": "Nom entité com", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__line2Right__title": "Date de règlement", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__paymentMethod__title": "Mode de règlement", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__reference__title": "Référence", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__titleRight__title": "Statut", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__type__title": "Type", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__voidDate__title": "<PERSON><PERSON><PERSON> le", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__voidText__title": "Texte d'annulation", "@sage/xtrem-finance/pages__receipt____navigationPanel__optionsMenu__title": "Règlements reçus comptabilisés", "@sage/xtrem-finance/pages__receipt____navigationPanel__optionsMenu__title__2": "Règlements reçus annulés", "@sage/xtrem-finance/pages__receipt____navigationPanel__optionsMenu__title__3": "Tous les règlements reçus", "@sage/xtrem-finance/pages__receipt____objectTypePlural": "Règlements reçus", "@sage/xtrem-finance/pages__receipt____objectTypeSingular": "Règlement reçu", "@sage/xtrem-finance/pages__receipt____title": "Règlement reçu", "@sage/xtrem-finance/pages__receipt__amount____title": "Montant de règlement", "@sage/xtrem-finance/pages__receipt__amountBankCurrency____title": "Montant en devise de banque", "@sage/xtrem-finance/pages__receipt__bankAccount____title": "Compte bancaire", "@sage/xtrem-finance/pages__receipt__cancel_void": "Annuler", "@sage/xtrem-finance/pages__receipt__cancelReceipt____title": "Annuler", "@sage/xtrem-finance/pages__receipt__confirm_void": "Confirmer", "@sage/xtrem-finance/pages__receipt__createReceipt____title": "Confirmer", "@sage/xtrem-finance/pages__receipt__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-finance/pages__receipt__currency____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__customer____title": "Client", "@sage/xtrem-finance/pages__receipt__financialSite____title": "Site financier", "@sage/xtrem-finance/pages__receipt__isVoided____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__lines____columns__title__apOpenItem__accountsPayableInvoice__purchaseDocumentNumber": "N° doc", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__accountsReceivableInvoice__postingDate": "Date comptabilisation", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__accountsReceivableInvoice__reference": "<PERSON><PERSON><PERSON>.", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__accountsReceivableInvoice__salesDocumentNumber": "N° doc", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__dueDate": "Date échéance", "@sage/xtrem-finance/pages__receipt__lines____columns__title__currency__id": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__lines____columns__title__origin": "Origine", "@sage/xtrem-finance/pages__receipt__lines____columns__title__signedAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__lines____columns__title__signedAmountBankCurrency": "Mnt devise banque", "@sage/xtrem-finance/pages__receipt__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__mainSection____title": "Général", "@sage/xtrem-finance/pages__receipt__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__paymentDate____title": "Date de réception", "@sage/xtrem-finance/pages__receipt__paymentInformationBlock____title": "Informations de règlement", "@sage/xtrem-finance/pages__receipt__paymentMethod____title": "Mode de règlement", "@sage/xtrem-finance/pages__receipt__reference____title": "Informations de transaction", "@sage/xtrem-finance/pages__receipt__supplier____title": "Fournisseur", "@sage/xtrem-finance/pages__receipt__voidPaymentDate____helperText": "Ann<PERSON>r le règlement en date du", "@sage/xtrem-finance/pages__receipt__voidPaymentDate____title": "Date", "@sage/xtrem-finance/pages__receipt__voidPaymentText____title": "Texte", "@sage/xtrem-finance/pages__receipt__voidReceipt____title": "Annuler", "@sage/xtrem-finance/pages__receipt__voidSection____title": "Annuler règlement reçu", "@sage/xtrem-finance/pages__record_payment____title": "Émission de règlements", "@sage/xtrem-finance/pages__record_payment__amount____title": "Montant de règlement", "@sage/xtrem-finance/pages__record_payment__amount_in_bank_currency_must_be_positive": "Le montant en devise de banque doit être supérieur à 0.", "@sage/xtrem-finance/pages__record_payment__amountAvailableToApply____title": "<PERSON>ant disponible à appliquer", "@sage/xtrem-finance/pages__record_payment__amountBankCurrency____title": "Montant en devise de banque", "@sage/xtrem-finance/pages__record_payment__bankAccount____title": "Compte bancaire", "@sage/xtrem-finance/pages__record_payment__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-finance/pages__record_payment__currency____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__country__name": "Pays", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__id": "Code", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__name": "Nom", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__taxIdNumber": "N° taxe", "@sage/xtrem-finance/pages__record_payment__customer____title": "Client", "@sage/xtrem-finance/pages__record_payment__date____title": "Date d'émission", "@sage/xtrem-finance/pages__record_payment__financialSite____columns__title__legalCompany__name": "Société", "@sage/xtrem-finance/pages__record_payment__financialSite____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-finance/pages__record_payment__financialSite____placeholder": "Site financier", "@sage/xtrem-finance/pages__record_payment__financialSite____title": "Site financier", "@sage/xtrem-finance/pages__record_payment__generateButton____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__number": "N° facture", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__origin": "Origine", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__postingDate": "Date comptabilisation", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__reference": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__amountDue": "Mnt dû", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__creditAmount": "<PERSON>nt crédit", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__currency__id": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__dueDate": "Date échéance", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__paymentAmount": "Mnt règlement", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__remainingCompanyAmount": "Mnt société dû", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__totalAmount": "Mnt total", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__totalCompanyAmount": "Mnt société total", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__totalCompanyAmountPaid": "Mnt société réglé", "@sage/xtrem-finance/pages__record_payment__lines____title": "Résultats", "@sage/xtrem-finance/pages__record_payment__linesBlock____title": "Résultats", "@sage/xtrem-finance/pages__record_payment__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__mainSection____title": "Général", "@sage/xtrem-finance/pages__record_payment__payment_amount_can_not_be_negative": "Le montant de règlement doit être supérieur ou égal à 0.", "@sage/xtrem-finance/pages__record_payment__payment_created": "Règlement créé : {{paymentNumber}}.", "@sage/xtrem-finance/pages__record_payment__paymentInformationBlock____title": "Informations de règlement", "@sage/xtrem-finance/pages__record_payment__paymentMethod____title": "Mode de règlement", "@sage/xtrem-finance/pages__record_payment__reference____title": "Informations de transaction", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__country__name": "Pays", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__id": "Code", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__name": "Nom", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__taxIdNumber": "N° taxe", "@sage/xtrem-finance/pages__record_payment__supplier____title": "Fournisseur", "@sage/xtrem-finance/pages__record_payment__totalPaymentApplied____title": "Règlement total appliqué", "@sage/xtrem-finance/pages__record_payment__transaction_information_can_not_exceed_100_characters": "Les informations de transaction ne doivent pas dépasser 100 caractères.", "@sage/xtrem-finance/pages__record_payment__type____title": "Type", "@sage/xtrem-finance/pages__record_receipt____title": "Réception de règlements", "@sage/xtrem-finance/pages__record_receipt__amount____title": "Montant de règlement", "@sage/xtrem-finance/pages__record_receipt__amount_in_bank_currency_must_be_positive": "Le montant en devise de banque doit être supérieur à 0.", "@sage/xtrem-finance/pages__record_receipt__amountAvailableToApply____title": "<PERSON>ant disponible à appliquer", "@sage/xtrem-finance/pages__record_receipt__amountBankCurrency____title": "Montant en devise de banque", "@sage/xtrem-finance/pages__record_receipt__bank_amount_must_be_positive": "Renseignez un montant en devise de banque supérieur à 0.", "@sage/xtrem-finance/pages__record_receipt__bankAccount____title": "Compte bancaire", "@sage/xtrem-finance/pages__record_receipt__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-finance/pages__record_receipt__currency____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt__customer____columns__title__country__name": "Pays", "@sage/xtrem-finance/pages__record_receipt__customer____columns__title__id": "Code", "@sage/xtrem-finance/pages__record_receipt__customer____columns__title__name": "Nom", "@sage/xtrem-finance/pages__record_receipt__customer____columns__title__taxIdNumber": "N° TVA", "@sage/xtrem-finance/pages__record_receipt__customer____title": "Client", "@sage/xtrem-finance/pages__record_receipt__date____title": "Date de réception", "@sage/xtrem-finance/pages__record_receipt__financialSite____columns__title__legalCompany__name": "Société", "@sage/xtrem-finance/pages__record_receipt__financialSite____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-finance/pages__record_receipt__financialSite____placeholder": "Site financier", "@sage/xtrem-finance/pages__record_receipt__financialSite____title": "Site financier", "@sage/xtrem-finance/pages__record_receipt__generateButton____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__accountsReceivableInvoice__number": "N° facture", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__accountsReceivableInvoice__origin": "Origine", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__accountsReceivableInvoice__postingDate": "Date comptabilisation", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__accountsReceivableInvoice__reference": "Référence", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__amountDue": "Mnt dû", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__creditAmount": "<PERSON>nt crédit", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__currency__id": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__dueDate": "Date échéance", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__paymentAmount": "Mnt règlement", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__remainingCompanyAmount": "<PERSON><PERSON> soci<PERSON> dû", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__totalAmount": "Mnt total", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__totalCompanyAmount": "Total montant société", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__totalCompanyAmountPaid": "<PERSON><PERSON> soci<PERSON> ré<PERSON>", "@sage/xtrem-finance/pages__record_receipt__lines____title": "Résultats", "@sage/xtrem-finance/pages__record_receipt__linesBlock____title": "Résultats", "@sage/xtrem-finance/pages__record_receipt__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt__mainSection____title": "Général", "@sage/xtrem-finance/pages__record_receipt__payment_amount_can_not_be_negative": "Le montant de règlement doit être supérieur ou égal à 0.", "@sage/xtrem-finance/pages__record_receipt__payment_amount_must_be_positive": "Renseignez un montant de règlement supérieur à 0.", "@sage/xtrem-finance/pages__record_receipt__payment_amount_must_be_total_payment_applied": "Le montant du règlement doit être identique au règlement total appliqué.", "@sage/xtrem-finance/pages__record_receipt__paymentInformationBlock____title": "Informations de règlement", "@sage/xtrem-finance/pages__record_receipt__paymentMethod____title": "Mode de règlement", "@sage/xtrem-finance/pages__record_receipt__receipt_created": "La réception de règlement suivante a été créée : {{receiptNumber}}.", "@sage/xtrem-finance/pages__record_receipt__reference____title": "Informations de transaction", "@sage/xtrem-finance/pages__record_receipt__supplier____columns__title__country__name": "Pays", "@sage/xtrem-finance/pages__record_receipt__supplier____columns__title__id": "Code", "@sage/xtrem-finance/pages__record_receipt__supplier____columns__title__name": "Nom", "@sage/xtrem-finance/pages__record_receipt__supplier____columns__title__taxIdNumber": "N° TVA", "@sage/xtrem-finance/pages__record_receipt__supplier____title": "Fournisseur", "@sage/xtrem-finance/pages__record_receipt__totalPaymentApplied____title": "Règlement total appliqué", "@sage/xtrem-finance/pages__record_receipt__transaction_information_can_not_exceed_100_characters": "Les informations de la transaction ne doivent pas dépasser 100 caractères.", "@sage/xtrem-finance/pages__record_receipt__type____title": "Type", "@sage/xtrem-finance/pages__unbilled_account_payable_inquiry__companyReference____columns__title__description": "Description", "@sage/xtrem-finance/pages__unbilled_account_payable_inquiry__fromSupplier____helperText": "Code", "@sage/xtrem-finance/pages__unbilled_account_payable_inquiry__toSupplier____helperText": "Code", "@sage/xtrem-finance/permission__accounting_integration__name": "Intégration comptable", "@sage/xtrem-finance/permission__create_journals_from_accounting_staging__name": "<PERSON><PERSON>er des journaux à partir de la préparation comptable", "@sage/xtrem-finance/permission__create_journals_from_accounting_staging_job__name": "Créer des journaux à partir de la tâche de préparation comptable", "@sage/xtrem-finance/permission__delete__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/permission__initialize_paid_amount__name": "Initialiser montant rég<PERSON>", "@sage/xtrem-finance/permission__manage__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/permission__post__name": "Comptabi<PERSON>er", "@sage/xtrem-finance/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-finance/permission__retry_finance_document__name": "Renvoyer le document financier", "@sage/xtrem-finance/permission__single_record__name": "Enregistrement unique", "@sage/xtrem-finance/permission__void__name": "Annuler", "@sage/xtrem-finance/search": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/source_document_type_not_supported": "Ce type de document d'origine n'est pas pris en charge : {{sourceDocumentType}}.", "@sage/xtrem-finance/status_not_supported": "Statut non pris en charge : {{status}}.", "@sage/xtrem-finance/success_notification__bulk_open_item_payment_title_error": "Forcer règlement échéances", "@sage/xtrem-finance/success_notification__bulk_open_item_payment_title_success": "Forcer règlement échéances", "@sage/xtrem-finance/success_notification_description__ar_open_items_link": "Échéances", "@sage/xtrem-finance/sys__notification_history__search": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/target_document_not_found": "{{sysId}} {{type}} : document cible introuvable.", "@sage/xtrem-finance/target_document_type_not_supported": "{{targetDocumentType}} : ce type de document cible n'est pas pris en charge.", "@sage/xtrem-finance/unexpected_error": "Erreur inattendue : {{retryFinanceDocumentResult}}", "@sage/xtrem-finance/widgets__finance_integration_health____callToActions__seeAll__title": "Tout voir", "@sage/xtrem-finance/widgets__finance_integration_health____dataDropdownMenu__orderBy__documentNumber__title": "Trier par numéro de document", "@sage/xtrem-finance/widgets__finance_integration_health____dataDropdownMenu__orderBy__status__title": "Trier par statut", "@sage/xtrem-finance/widgets__finance_integration_health____dataDropdownMenu__orderBy__updateStamp__title": "Trier par dernière mise à jour de statut", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__line2__title": "Type de document", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__line2Right__title": "Dernière mise à jour du statut", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__line3__title": "Message", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__targetNumber__title": "Numéro du document cible", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__targetType__title": "Type de document cible", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__title__title": "Statut", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__titleRight__title": "Numéro de document", "@sage/xtrem-finance/widgets__finance_integration_health____title": "Statut de transaction de finance"}