{"@sage/xtrem-finance/accounting_engine__no_records_to_process_on_accounting_staging_table": "No hay registros que procesar en el tipo de documento {{documentType}} número {{documentNumber}} para el tipo de documento de destino {{targetDocumentType}}.", "@sage/xtrem-finance/accounting_engine__there_are_records_already_processed_on_accounting_staging_table": "Ya se han procesado registros en el tipo de documento {{documentType}} número {{documentNumber}} para el tipo de documento de destino {{targetDocumentType}}.", "@sage/xtrem-finance/accounting_engine__there_are_records_to_be_processed_on_accounting_staging_table_to_documents_that_are_not_target_document_type": "Hay registros que procesar en el tipo de documento {{documentType}} número {{documentNumber}} que no son de tipo {{targetDocumentType}}.", "@sage/xtrem-finance/accounting_engine__wrong_number_of_records": "Number of records on the accounting staging: {{recordCount}}; expected value: {{batchSize}}", "@sage/xtrem-finance/activity__accounting_interface_listener__name": "Proceso de escucha de interfaz contable", "@sage/xtrem-finance/activity__accounting_staging__name": "Contabilidad provisional", "@sage/xtrem-finance/activity__accounts_payable_invoice__name": "Factura contable de proveedor", "@sage/xtrem-finance/activity__accounts_payable_open_item__name": "Vencimiento de proveedor", "@sage/xtrem-finance/activity__accounts_receivable_advance__name": "Anticipo de cliente", "@sage/xtrem-finance/activity__accounts_receivable_invoice__name": "Factura contable de cliente", "@sage/xtrem-finance/activity__accounts_receivable_open_item__name": "Vencimiento de cliente", "@sage/xtrem-finance/activity__accounts_receivable_payment__name": "Pago de cliente", "@sage/xtrem-finance/activity__datev_export__name": "Exportación de DATEV", "@sage/xtrem-finance/activity__generate_journal_entries__name": "Generación de asientos", "@sage/xtrem-finance/activity__journal_entry__name": "Asiento", "@sage/xtrem-finance/activity__journal_entry_inquiry__name": "Consulta de asiento", "@sage/xtrem-finance/activity__payment__name": "Pago", "@sage/xtrem-finance/activity__receipt__name": "Cobro", "@sage/xtrem-finance/cant_post_ap_invoice_when_status_is_not_pending": "La factura contable de proveedor no se puede contabilizar porque el estado no es {{pending}}.", "@sage/xtrem-finance/cant_post_ar_invoice_when_status_is_not_pending": "La factura contable de cliente no se puede contabilizar porque el estado no es {{pending}}.", "@sage/xtrem-finance/check-date-range": "La fecha de inicio no puede ser posterior a la fecha de fin.", "@sage/xtrem-finance/classes__journal__cant_read_accounting_staging_amount": "No se ha encontrado el importe de preparación contable para el documento número {{documentNumber}} {{documentNumberId}}.", "@sage/xtrem-finance/classes__journal__cant_read_journal_entry_type": "No se ha encontrado el tipo de asiento para el documento {{documentType}}, número {{documentNumber}} {{documentId}}.", "@sage/xtrem-finance/classes__journal__cant_read_posting_class_pre_journal": "No se ha encontrado la clase contable para el documento {{documentType}}, número {{documentNumber}}. El tipo de clase contable es {{postingClassType}}.", "@sage/xtrem-finance/classes__journal__journal_to_be_created_has_no_lines": "El asiento que se va a crear no tiene apuntes.", "@sage/xtrem-finance/classes__journal__no_documents_to_process": "No hay ningún documento que procesar para el documento {{documentType}}, el tipo de documento de destino {{targetDocumentType}} y el número de documento {{documentNumber}}.", "@sage/xtrem-finance/classes__journal__target_document_type_not_supported": "El tipo de documento de destino ({{targetDocumentType}}) no es compatible.", "@sage/xtrem-finance/classes__localized-messages__cant_read_account": "No se ha encontrado la clase {{account}}.", "@sage/xtrem-finance/classes__localized-messages__cant_read_accounting_staging_amount": "No se ha encontrado el importe de preparación contable para el documento número {{documentNumber}} {{documentNumberId}}.", "@sage/xtrem-finance/classes__localized-messages__cant_read_base_document_line": "No se ha encontrado la línea de documento de origen {{baseDocumentLine}}.", "@sage/xtrem-finance/classes__localized-messages__cant_read_base_tax": "No se ha encontrado la línea del impuesto {{baseTax}}.", "@sage/xtrem-finance/classes__localized-messages__cant_read_journal_entry_type": "No se ha encontrado el tipo de asiento para el documento {{documentType}}, número {{documentNumber}} {{documentId}}.", "@sage/xtrem-finance/classes__localized-messages__cant_read_stock_journal": "No se ha encontrado el diario de stock {{stockJournal}}.", "@sage/xtrem-finance/classes__localized-messages__cant_read_taxPostingClass": "No se ha encontrado la clase contable de impuesto {{taxPostingClass}}.", "@sage/xtrem-finance/classes__localized-messages__journal_to_be_created_has_no_lines": "El asiento que se va a crear no tiene apuntes.", "@sage/xtrem-finance/classes__localized-messages__no_documents_to_process": "No hay ningún documento que procesar que tenga el tipo de documento {{documentType}}, el tipo de documento de destino {{targetDocumentType}} y el número de documento {{documentNumber}}.", "@sage/xtrem-finance/classes__localized-messages__no_invoice": "No se ha encontrado el número de factura {{documentNumber}}.", "@sage/xtrem-finance/classes__localized-messages__target_document_type_not_supported": "El tipo de documento de destino {{targetDocumentType}} no es compatible.", "@sage/xtrem-finance/classes__localized-messages__unable_to_get_account": "La cuenta no se ha podido determinar.", "@sage/xtrem-finance/client_functions__record_common__different_currencies": "La divisa seleccionada es diferente de la divisa asociada a la cuenta bancaria.", "@sage/xtrem-finance/client_functions__record_common__invalid_date": "Selecciona una fecha pasada.", "@sage/xtrem-finance/client_functions__record_common__negative_credit_amount_error": "Introduce un importe de abono superior a 0.", "@sage/xtrem-finance/client_functions__record_common__negative_payment_amount_error": "Introduce un importe de pago superior a 0.", "@sage/xtrem-finance/client_functions__record_common__wrong_amount_in_bank_currency": "El importe en la divisa bancaria ({{bankAmount}} {{currencySymbol}}) es diferente del importe calculado ({{amount}} {{currencySymbol}}).", "@sage/xtrem-finance/client_functions__record_common__wrong_credit_amount_error": "El importe abonado debe ser inferior al importe pendiente.", "@sage/xtrem-finance/client_functions__record_common__wrong_payment_amount_error": "El importe de pago de la factura debe ser inferior al importe pendiente.", "@sage/xtrem-finance/client_functions__void_record__void_date_should_be_after_payment_date": "La fecha de anulación debe ser posterior a la de pago.", "@sage/xtrem-finance/data_types__datev_export_status_enum__name": "Datev export status enum", "@sage/xtrem-finance/document_type_not_supported": "El tipo de documento {{documentType}} no es compatible.", "@sage/xtrem-finance/enums__datev_export_status__draft": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/enums__datev_export_status__error": "Error", "@sage/xtrem-finance/enums__datev_export_status__exported": "Exportado", "@sage/xtrem-finance/enums__datev_export_status__exportInProgress": "Exportación en curso", "@sage/xtrem-finance/enums__datev_export_status__extracted": "Extraído", "@sage/xtrem-finance/enums__datev_export_status__extractionInProgress": "Extracción en curso", "@sage/xtrem-finance/fail__bulk_open_item_payment_notification_description__view_link": "Trazas de tareas por lotes", "@sage/xtrem-finance/functions__accounting_engine__cant_read_account": "No se ha encontrado la clase {{account}}.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_base_document_line": "No se ha encontrado la línea de documento de origen {{baseDocumentLine}}.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_base_tax": "No se ha encontrado la línea del impuesto {{baseTax}}.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_customer": "No se ha encontrado el cliente {{customer}}.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_financial_site": "No se ha encontrado la planta financiera {{financialSite}}.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_item": "No se ha encontrado el artículo {{item}}.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_journal_entry_type": "No se ha encontrado el tipo de asiento para el documento {{documentType}} número {{documentNumber}} {{documentId}}.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_stock_journal": "No se ha encontrado el diario de stock {{stockJournal}}.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_supplier": "No se ha encontrado el proveedor {{supplier}}.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_tax": "No se ha encontrado el impuesto {{tax}}.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_taxPostingClass": "No se ha encontrado la clase contable de impuesto {{taxPostingClass}}.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_transaction_currency": "No se ha encontrado la divisa de transacción {{transactionCurrency}}.", "@sage/xtrem-finance/functions__accounting_engine__customer_posting_class_missing_on_customer": "Introduce una clase contable para el cliente {{customer}}.", "@sage/xtrem-finance/functions__accounting_engine__item_posting_class_missing_on_item": "No se ha encontrado la clase contable del artículo {{item}}.", "@sage/xtrem-finance/functions__accounting_engine__no_documents_to_be_processed": "No hay ningún documento que procesar.", "@sage/xtrem-finance/functions__accounting_engine__no_documents_to_process": "No hay ningún documento que procesar que tenga estas características: tipo de documento {{documentType}}; tipo de documento de destino {{targetDocumentType}}; número de documento {{documentNumber}}.", "@sage/xtrem-finance/functions__accounting_engine__no_invoice": "No se ha encontrado el número de factura {{documentNumber}}.", "@sage/xtrem-finance/functions__accounting_engine__processing": "{{i}}/{{numberToBeProcess}} procesando {{documents}}", "@sage/xtrem-finance/functions__accounting_engine__supplier_posting_class_missing_on_supplier": "Introduce una clase contable para el proveedor {{supplier}}.", "@sage/xtrem-finance/functions__accounting_engine__target_document_type_not_supported": "El tipo de documento de destino ({{targetDocumentType}}) no es compatible.", "@sage/xtrem-finance/functions__accounting_engine__tax_posting_class_missing_on_tax": "Introduce una clase contable para el impuesto {{tax}}.", "@sage/xtrem-finance/functions__accounting_engine__to_be_process": "Número de documentos por procesar: {{numberToBeProcess}}", "@sage/xtrem-finance/generate-journal-entry-date": "Introduce una fecha de inicio anterior a la fecha de fin.", "@sage/xtrem-finance/generate-pre-journal-date": "La fecha de inicio no puede ser posterior a la fecha de fin.", "@sage/xtrem-finance/menu_item__datev-interface": "Interfaz de DATEV", "@sage/xtrem-finance/menu_item__payment-tracking": "Seguimiento de pagos", "@sage/xtrem-finance/node__accounting_staging__finish_create_journal": "Accounting staging: finish {{messageJournalCreated}}", "@sage/xtrem-finance/node__accounting_staging__start_create_journal": "Accounting staging start ", "@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_description": " Asientos generados: {{journalsCreated}}", "@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_title": "Los asientos se han generado.", "@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_title_fail": "Los asientos no se han generado.", "@sage/xtrem-finance/node__accounting-interface-listener__finance_document_notification_resent": "El documento se está procesando de nuevo.", "@sage/xtrem-finance/node__accounts_payable_invoice__resend_notification_for_finance": "Reenvío de la notificación de contabilidad para la factura contable de proveedor {{apInvoiceNumber}}", "@sage/xtrem-finance/node__accounts_receivable_invoice__resend_notification_for_finance": "Reenvío de la notificación de contabilidad para la factura contable de cliente {{arInvoiceNumber}}", "@sage/xtrem-finance/node-extensions__base-payment-document-extension__voided": "El pago se ha anulado.", "@sage/xtrem-finance/node-extensions__business_entity_extension__property__composedDescription": "Descripción compuesta", "@sage/xtrem-finance/node-extensions__item_extension__property__composedDescription": "Descripción compuesta", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__apOpenItem": "Vencimiento de proveedor", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__arOpenItem": "Vencimiento de cliente", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__companyAmount": "Importe de sociedad", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__origin": "Origen", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__originalNodeFactory": "<PERSON><PERSON> original", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__originalOpenItem": "Vencimiento original", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__signedAmount": "Importe firmado", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__signedAmountBankCurrency": "Importe firmado en divisa bancaria", "@sage/xtrem-finance/node-extensions__site_extension__property__composedDescription": "Descripción compuesta", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob": "Crear diarios desde tarea de contabilidad provisional", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__failed": "Error al crear los diarios desde la tarea de contabilidad provisional", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__parameter__batchTrackingId": "Id. de seguimiento por lotes", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__parameter__journalsCreatedData": "Datos de asientos creados", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging": "Crear diarios desde contabilidad provisional", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging__failed": "Error al crear los diarios desde la contabilidad provisional", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging__parameter__journalsCreatedData": "Datos de asientos creados", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__retryFinanceDocument": "Reintentar documento contable", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__retryFinanceDocument__failed": "Error al reintentar el documento contable", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__retryFinanceDocument__parameter__financeTransaction": "Transacción contable", "@sage/xtrem-finance/nodes__accounting_interface_listener__node_name": "Proceso de escucha de interfaz contable", "@sage/xtrem-finance/nodes__accounts_open_item__node_name": "Vencimiento", "@sage/xtrem-finance/nodes__accounts_payable_invoice__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_payable_invoice__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_payable_invoice__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__cant_post_ap_invoice_when_status_is_not_draft": "La factura contable de proveedor no se puede contabilizar porque el estado no es \"{{pending}}\".", "@sage/xtrem-finance/nodes__accounts_payable_invoice__cant_post_ap_invoice_when_status_is_not_draft_nor_error": "La factura contable de proveedor no se puede contabilizar porque el estado de contabilización no es \"{{draft}}\" o \"{{error}}\".", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__post": "Contabilizar", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__post__failed": "Error de contabilización", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__post__parameter__apInvoice": "Factura contable de proveedor", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__resendNotificationForFinance": "Reenviar notificación para contabilidad", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__resendNotificationForFinance__failed": "Error al reenviar la notificación para la contabilidad", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__resendNotificationForFinance__parameter__apInvoice": "Factura contable de proveedor", "@sage/xtrem-finance/nodes__accounts_payable_invoice__node_name": "Factura contable de proveedor", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__account": "C<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__billBySupplier": "Proveedor <PERSON>dor", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__billBySupplierName": "Nombre de proveedor facturador", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__billBySupplierTaxIdNumber": "NIF-IVA de proveedor facturador", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__companyFxRate": "Tipo de cambio de sociedad", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__companyFxRateDivisor": "Divisor de tipo de cambio de sociedad", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__currency": "Divisa", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__description": "Descripción", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__documentDate": "Fecha de documento", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationApp": "Aplicación de integración contable", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationAppRecordId": "Id. de registro de aplicación de integración contable", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationAppUrl": "URL de aplicación de integración contable", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationStatus": "Estado de integración contable", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financialSiteName": "Nombre de planta financiera", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financialSiteTaxIdNumber": "N.º id. fiscal de planta financiera", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__fxRateDate": "Fecha de tipo de cambio", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__internalFinanceIntegrationStatus": "Estado de integración de contabilidad interna", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__invoiceDate": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__lines": "Líneas", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__number": "Número", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__openItems": "Vencimientos", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__origin": "Origen", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__paymentTerm": "Condiciones de pago", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__paymentTracking": "Seguimiento de pagos", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__payToSupplier": "<PERSON><PERSON><PERSON><PERSON> pagado", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__payToSupplierLinkedAddress": "Dirección de proveedor pagado", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__postingDetails": "Detalles de contabilización", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__postingStatus": "Estado de contabilización", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__purchaseDocumentNumber": "Número de documento de compra", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__purchaseDocumentSysId": "Id. de sistema de documento de compra", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__rateDescription": "Descripción de tipo impositivo", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__reference": "Referencia", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__returnLinkedAddress": "Dirección de devolución", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__supplierDocumentDate": "<PERSON><PERSON> de documento de proveedor", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__supplierDocumentNumber": "Número de documento de proveedor", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__taxCalculationStatus": "Estado de cálculo de impuestos", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__taxes": "Impuestos", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalAmountExcludingTax": "Importe total sin impuestos", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalAmountIncludingTax": "Importe total con impuestos", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalCompanyAmountExcludingTax": "Importe total sin impuestos de sociedad", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalCompanyAmountIncludingTax": "Importe total con impuestos de sociedad", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalCompanyTaxAmount": "Total impuestos de sociedad", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalExemptAmount": "Importe exento total", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalTaxableAmount": "Importe imponible total", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalTaxAmount": "Total impuestos", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalTaxAmountAdjusted": "Importe total de impuestos regularizado", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__type": "Tipo", "@sage/xtrem-finance/nodes__accounts_payable_invoice__type_invalid": "Selecciona una factura o una factura rectificativa de compra como tipo de factura contable de proveedor.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__update_not_allowed_status_posted": "No puedes actualizar una factura contable de proveedor contabilizada.", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__node_name": "Línea de factura contable de proveedor", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__account": "C<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__accountingStagingLines": "Líneas contables provisionales", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__amountExcludingTax": "Importe sin impuestos", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__amountIncludingTax": "Importe con impuestos", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__attributesAndDimensions": "Atributos y secciones", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__currency": "Divisa", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__description": "Descripción", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__document": "Documento", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__documentId": "Id. de documento", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__documentLineType": "Tipo de línea de documento", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__documentNumber": "Número de documento", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__exemptAmount": "Importe exento", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__lineAmountExcludingTax": "Importe sin impuestos de línea", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__lineAmountIncludingTax": "Importe con impuestos de línea", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__lineType": "Tipo de línea", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__recipientSite": "Planta de destino", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__sourceDocumentNumber": "Número de documento de origen", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__sourceDocumentType": "Tipo de documento de origen", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxableAmount": "Importe imponible", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxAmount": "Importe de impuesto", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxAmountAdjusted": "Importe de impuesto regularizado", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxDate": "Fecha de impuesto", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxDetail": "Detalles de impuesto", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxes": "Impuestos", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxLineTaxAmount": "Importe de impuesto de línea de impuesto", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__uiTaxes": "Impuestos de seguro de desempleo", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__node_name": "Sección de línea de factura contable de proveedor", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__amount": "Importe", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__businessSite": "Planta empresarial", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__customer": "Cliente", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension01": "Sección 01", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension02": "Sección 02", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension03": "Sección 03", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension04": "Sección 04", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension05": "Sección 05", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension06": "Sección 06", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension07": "Sección 07", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension08": "Sección 08", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension09": "Sección 09", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension10": "Sección 10", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension11": "Sección 11", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension12": "Sección 12", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension13": "Sección 13", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension14": "Sección 14", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension15": "Sección 15", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension16": "Sección 16", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension17": "Sección 17", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension18": "Sección 18", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension19": "Sección 19", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension20": "Sección 20", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__employee": "Trabajador", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__hasAttributesOrDimenionsChanged": "Atributos o secciones cambiados", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__manufacturingSite": "Planta de producción", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__originLine": "<PERSON><PERSON>ea de origen", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__project": "Proyecto", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__stockSite": "Planta de stock", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__task": "Tarea", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__node_name": "Impuesto de línea de factura contable de proveedor", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__property__currency": "Divisa", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__property__document": "Documento", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__node_name": "Impuesto de factura contable de proveedor", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__property__currency": "Divisa", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__property__document": "Documento", "@sage/xtrem-finance/nodes__accounts_payable_open_item__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_payable_open_item__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_payable_open_item__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_payable_open_item__bulkMutation__bulkOpenItemUpdate": "Actualizar vencimientos en masa", "@sage/xtrem-finance/nodes__accounts_payable_open_item__bulkMutation__bulkOpenItemUpdate__failed": "Error al actualizar los vencimientos en masa", "@sage/xtrem-finance/nodes__accounts_payable_open_item__node_name": "Vencimiento de proveedor", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__accountsPayableInvoice": "Factura contable de proveedor", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__amountDue": "Importe pendiente", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__businessRelation": "Relación de negocio", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__paymentTracking": "Seguimiento de pagos", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__remainingCompanyAmount": "Importe restante de sociedad", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__totalAmount": "Importe total", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__totalCompanyAmount": "Importe total de sociedad", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__totalCompanyAmountPaid": "Importe pagado de sociedad", "@sage/xtrem-finance/nodes__accounts_payable_open_item__skipped": "El documento {{number}} se ha omitido porque ya está pagado por completo.", "@sage/xtrem-finance/nodes__accounts_receivable_advance__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_receivable_advance__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_receivable_advance__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_receivable_advance__cant_post_ar_advance_when_status_is_not_draft_nor_error": "El estado de contabilización debe ser \"Borrador\" o \"Error\". El anticipo del cliente no se puede contabilizar.", "@sage/xtrem-finance/nodes__accounts_receivable_advance__mutation__post": "Contabilizar", "@sage/xtrem-finance/nodes__accounts_receivable_advance__mutation__post__failed": "Error de contabilización", "@sage/xtrem-finance/nodes__accounts_receivable_advance__mutation__post__parameter__arAdvance": "Anticipo de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_advance__node_name": "Anticipo de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__advanceAmount": "Importe de anticipo", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__advanceCompanyAmount": "Importe de anticipo de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__bankAccount": "Cuenta bancaria", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__companyCurrency": "Divisa de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__companyFxRate": "Tipo de cambio de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__companyFxRateDivisor": "Divisor de tipo de cambio de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__currency": "Divisa", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__description": "Descripción", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationApp": "Aplicación de integración contable", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationAppRecordId": "Id. de registro de aplicación de integración contable", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationAppUrl": "URL de aplicación de integración contable", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationStatus": "Estado de integración contable", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financialSiteName": "Nombre de planta financiera", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__fxRateDate": "Fecha de tipo de cambio", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__internalFinanceIntegrationStatus": "Estado de integración de contabilidad interna", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__lines": "Líneas", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__number": "Número", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__paymentDate": "Fecha de pago", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__payToCustomerId": "Id. de cliente pagador", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__payToCustomerName": "Nombre de cliente pagador", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__postingStatus": "Estado de contabilización", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__reference": "Referencia", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-finance/nodes__accounts_receivable_advance__update_not_allowed_status_posted": "Solo puedes actualizar los anticipos de clientes que estén en borrador.", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__node_name": "Línea de anticipo de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__account": "C<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__advanceAmount": "Importe de anticipo", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__advanceCompanyAmount": "Importe de anticipo de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__businessSite": "Planta empresarial", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__companyCurrency": "Divisa de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__computedAttributes": "Atributos calculados", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__currency": "Divisa", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__customer": "Cliente", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__description": "Descripción", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension01": "Sección 01", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension02": "Sección 02", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension03": "Sección 03", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension04": "Sección 04", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension05": "Sección 05", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension06": "Sección 06", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension07": "Sección 07", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension08": "Sección 08", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension09": "Sección 09", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension10": "Sección 10", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension11": "Sección 11", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension12": "Sección 12", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension13": "Sección 13", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension14": "Sección 14", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension15": "Sección 15", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension16": "Sección 16", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension17": "Sección 17", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension18": "Sección 18", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension19": "Sección 19", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension20": "Sección 20", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__document": "Documento", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__documentId": "Id. de documento", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__documentNumber": "Número de documento", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__employee": "Trabajador", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__hasAttributesOrDimenionsChanged": "Atributos o secciones cambiados", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__manufacturingSite": "Planta de producción", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__project": "Proyecto", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__stockSite": "Planta de stock", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__task": "Tarea", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__cant_post_ar_invoice_when_status_is_not_draft": "La factura contable de cliente no se puede contabilizar porque el estado no es \"{{pending}}\".", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__cant_post_ar_invoice_when_status_is_not_draft_nor_error": "La factura contable de cliente no se puede contabilizar porque el estado de contabilización no es \"{{draft}}\" o \"{{error}}\".", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__information_payment_tracking": "Actualiza los vencimientos creados según la antigüedad de los saldos pendientes de pago y de cobro.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__post": "Contabilizar", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__post__failed": "Error de contabilización", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__post__parameter__arInvoice": "Factura contable de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__resendNotificationForFinance": "Reenviar notificación para contabilidad", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__resendNotificationForFinance__failed": "Error al reenviar la notificación para la contabilidad", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__resendNotificationForFinance__parameter__arInvoice": "Factura contable de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__node_name": "Factura contable de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__payment_tracking": "Opción de servicio de seguimiento de pagos activa", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__account": "C<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__billToCustomer": "Cliente facturado", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__billToCustomerName": "Nombre de cliente facturado", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__billToCustomerTaxIdNumber": "NIF-IVA de cliente facturado", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__companyFxRate": "Tipo de cambio de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__companyFxRateDivisor": "Divisor de tipo de cambio de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__currency": "Divisa", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__description": "Descripción", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__documentDate": "Fecha de documento", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationApp": "Aplicación de integración contable", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationAppRecordId": "Id. de registro de aplicación de integración contable", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationAppUrl": "URL de aplicación de integración contable", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationStatus": "Estado de integración contable", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financialSiteName": "Nombre de planta financiera", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financialSiteTaxIdNumber": "N.º id. fiscal de planta financiera", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__fxRateDate": "Fecha de tipo de cambio", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__internalFinanceIntegrationStatus": "Estado de integración de contabilidad interna", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__invoiceDate": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__isPrinted": "Impresa", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__lines": "Líneas", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__number": "Número", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__openItems": "Vencimientos", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__origin": "Origen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__paymentStatus": "Estado de pago", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__paymentTerm": "Condiciones de pago", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__postingDetails": "Detalles de contabilización", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__postingStatus": "Estado de contabilización", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__rateDescription": "Descripción de tipo impositivo", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__reference": "Referencia", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__salesDocumentNumber": "Número de documento de venta", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__salesDocumentSysId": "Id. de sistema de documento de venta", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__taxCalculationStatus": "Estado de cálculo de impuestos", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__taxEngine": "Motor de impuestos", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__taxes": "Impuestos", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalAmountExcludingTax": "Importe total sin impuestos", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalAmountIncludingTax": "Importe total con impuestos", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalCompanyAmountExcludingTax": "Importe total sin impuestos de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalCompanyAmountIncludingTax": "Importe total con impuestos de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalCompanyTaxAmount": "Total impuestos de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalExemptAmount": "Importe exento total", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalTaxableAmount": "Importe imponible total", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalTaxAmount": "Total impuestos", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalTaxAmountAdjusted": "Importe total de impuestos regularizado", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__type": "Tipo", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__type_invalid": "Selecciona una factura o una factura rectificativa de venta como tipo de factura contable de cliente.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__update_not_allowed_status_posted": "No puedes actualizar una factura contable de cliente contabilizada.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__node_name": "Línea de factura contable de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__account": "C<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__accountingStagingLines": "Líneas contables provisionales", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__amountExcludingTax": "Importe sin impuestos", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__amountIncludingTax": "Importe con impuestos", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__attributesAndDimensions": "Atributos y secciones", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__currency": "Divisa", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__description": "Descripción", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__document": "Documento", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__documentId": "Id. de documento", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__documentLineType": "Tipo de línea de documento", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__documentNumber": "Número de documento", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__exemptAmount": "Importe exento", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__lineAmountExcludingTax": "Importe sin impuestos de línea", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__lineAmountIncludingTax": "Importe con impuestos de línea", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__lineType": "Tipo de línea", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__netPriceIncludingTax": "Precio neto con impuestos", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__providerSite": "Planta de origen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__quantity": "Cantidad", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__quantityInSalesUnit": "Cantidad en unidad de venta", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__sourceDocumentNumber": "Número de documento de origen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__sourceDocumentType": "Tipo de documento de origen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxableAmount": "Importe imponible", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxAmount": "Importe de impuesto", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxAmountAdjusted": "Importe de impuesto regularizado", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxDate": "Fecha de impuesto", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxDetail": "Detalles de impuesto", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxes": "Impuestos", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxLineTaxAmount": "Importe de impuesto de línea de impuesto", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__uiTaxes": "Impuestos de seguro de desempleo", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__node_name": "Sección de línea de factura contable de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__amount": "Importe", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__businessSite": "Planta empresarial", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__customer": "Cliente", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension01": "Sección 01", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension02": "Sección 02", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension03": "Sección 03", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension04": "Sección 04", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension05": "Sección 05", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension06": "Sección 06", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension07": "Sección 07", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension08": "Sección 08", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension09": "Sección 09", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension10": "Sección 10", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension11": "Sección 11", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension12": "Sección 12", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension13": "Sección 13", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension14": "Sección 14", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension15": "Sección 15", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension16": "Sección 16", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension17": "Sección 17", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension18": "Sección 18", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension19": "Sección 19", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension20": "Sección 20", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__employee": "Trabajador", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__hasAttributesOrDimenionsChanged": "Atributos o secciones cambiados", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__manufacturingSite": "Planta de producción", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__originLine": "<PERSON><PERSON>ea de origen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__project": "Proyecto", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__stockSite": "Planta de stock", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__task": "Tarea", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__node_name": "Impuesto de línea de factura contable de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__property__currency": "Divisa", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__property__document": "Documento", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__node_name": "Impuesto de factura contable de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__property__currency": "Divisa", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__property__document": "Documento", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__bulkMutation__bulkOpenItemUpdate": "Actualizar vencimientos en masa", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__bulkMutation__bulkOpenItemUpdate__failed": "Error al actualizar los vencimientos en masa", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__node_name": "Vencimiento de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__accountsReceivableInvoice": "Factura contable de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__amountDue": "Importe pendiente", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__businessRelation": "Relación de negocio", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__receipts": "Co<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__remainingCompanyAmount": "Importe restante de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__totalAmount": "Importe total", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__totalCompanyAmount": "Importe total de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__totalCompanyAmountPaid": "Importe pagado de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__skipped": "El documento {{number}} se ha omitido porque ya está pagado por completo.", "@sage/xtrem-finance/nodes__accounts_receivable_payment__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_receivable_payment__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_receivable_payment__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_receivable_payment__cant_post_ar_payment_when_status_is_not_draft_nor_error": "El estado de contabilización debe ser \"Borrador\" o \"Error\". El pago del cliente no se puede contabilizar.", "@sage/xtrem-finance/nodes__accounts_receivable_payment__mutation__post": "Contabilizar", "@sage/xtrem-finance/nodes__accounts_receivable_payment__mutation__post__failed": "Error de contabilización", "@sage/xtrem-finance/nodes__accounts_receivable_payment__mutation__post__parameter__arPayment": "Pago de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_payment__node_name": "Pago de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__bankAccount": "Cuenta bancaria", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__companyCurrency": "Divisa de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__companyFxRate": "Tipo de cambio de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__companyFxRateDivisor": "Divisor de tipo de cambio de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__currency": "Divisa", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__description": "Descripción", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationApp": "Aplicación de integración contable", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationAppRecordId": "Id. de registro de aplicación de integración contable", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationAppUrl": "URL de aplicación de integración contable", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationStatus": "Estado de integración contable", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financialSiteName": "Nombre de planta financiera", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__fxRateDate": "Fecha de tipo de cambio", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__internalFinanceIntegrationStatus": "Estado de integración de contabilidad interna", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__lines": "Líneas", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__number": "Número", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__paymentAmount": "Importe de pago", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__paymentCompanyAmount": "Importe de pago de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__paymentDate": "Fecha de pago", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__payToCustomerId": "Id. de cliente pagador", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__payToCustomerName": "Nombre de cliente pagador", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__postingStatus": "Estado de contabilización", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__reference": "Referencia", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-finance/nodes__accounts_receivable_payment__update_not_allowed_status_posted": "No puedes actualizar un pago de cliente en estado \"{{status}}\".", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__node_name": "Línea de pago de cliente", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__companyCurrency": "Divisa de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__currency": "Divisa", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__document": "Documento", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__documentId": "Id. de documento", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__documentNumber": "Número de documento", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__paymentAmount": "Importe de pago", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__paymentCompanyAmount": "Importe de pago de sociedad", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__type": "Tipo", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__type_invalid": "El tipo de línea del pago de cliente debe ser \"Factura de venta\" o \"Factura rectificativa de venta\".", "@sage/xtrem-finance/nodes__ap_invoice__posted": "La factura contable de proveedor se ha contabilizado.", "@sage/xtrem-finance/nodes__ar_advance__posted": "El anticipo del cliente se ha contabilizado.", "@sage/xtrem-finance/nodes__ar_invoice__posted": "La factura contable de cliente se ha contabilizado.", "@sage/xtrem-finance/nodes__ar_payment__posted": "El pago del cliente se ha contabilizado.", "@sage/xtrem-finance/nodes__base_open_item__text_forced_close": "<PERSON><PERSON><PERSON> forzado por \"{{user}}\": {{date}}", "@sage/xtrem-finance/nodes__base_payment_document_payment_already_voided": "El pago ya está anulado.", "@sage/xtrem-finance/nodes__datev_export__account_without_datev_id": "Cuenta sin id. de DATEV {{account}}", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExport": "Exportación de DATEV", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExport__failed": "Error de exportación de DATEV", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExtraction": "Extraer DATEV", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExtraction__failed": "Error al extraer DATEV", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExtraction__parameter__id": "Id.", "@sage/xtrem-finance/nodes__datev_export__business_relation_without_datev_id": "Entidad empresarial sin id. de DATEV {{businessEntity}}", "@sage/xtrem-finance/nodes__datev_export__complete_account_export": "La cuenta de DATEV se ha exportado.", "@sage/xtrem-finance/nodes__datev_export__complete_business_relation_export": "La relación de negocio de DATEV se ha exportado.", "@sage/xtrem-finance/nodes__datev_export__complete_export": "La exportación de DATEV se ha realizado correctamente.", "@sage/xtrem-finance/nodes__datev_export__complete_journal_entry_lines_export": "La exportación de apuntes de DATEV se ha realizado correctamente.", "@sage/xtrem-finance/nodes__datev_export__datev_file": "Archivos de exportación de DATEV", "@sage/xtrem-finance/nodes__datev_export__datev_file_ready": "Archivos creados: {{nbOfFilesCreated}}\n", "@sage/xtrem-finance/nodes__datev_export__datev_number_of_warnings": "Avisos: {{nbOfWarnings}}.", "@sage/xtrem-finance/nodes__datev_export__end_extraction": "Fin de extracción de DATEV", "@sage/xtrem-finance/nodes__datev_export__failure_description": "Ha habido un error en la extracción de DATEV. {{error}}", "@sage/xtrem-finance/nodes__datev_export__failure_description_export": "Ha habido un error en la exportación de DATEV. {{error}}", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_account_without_datev_id": "Asiento {{journalEntryNumber}}; cuenta {{account}}: apunte para cuenta sin id. en DATEV.", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_contra_account_without_datev_id": "Asiento {{journalEntryNumber}}; cuenta de contrapartida {{contraAccount}}: apunte para cuenta de contrapartida sin id. en DATEV.", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_no_tax_line_for_taxable_amount": "Asiento {{journalEntryNumber}}: no se ha encontrado ninguna línea de impuesto para el importe imponible.", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_with_different_tax": "Asiento {{journalEntryNumber}}; cuenta {{account}}: apunte con cuenta automática que contiene otro código de impuesto.", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_without_posting_key": "Asiento {{journalEntryNumber}}; cuenta {{account}}: apunte sin cuenta automática ni clave contable.", "@sage/xtrem-finance/nodes__datev_export__node_name": "Exportación de DATEV", "@sage/xtrem-finance/nodes__datev_export__number_extracted_accounts": "Cuentas de DATEV extraídas: {{numberExtractedAccounts}}", "@sage/xtrem-finance/nodes__datev_export__number_extracted_customers_suppliers": "Clientes y proveedores de DATEV extraídos: {{numberExtractedBusinessRelations}}", "@sage/xtrem-finance/nodes__datev_export__number_extracted_journal_entry_lines": "Apuntes de DATEV extraídos: {{numberExtractedJournalEntryLines}}.", "@sage/xtrem-finance/nodes__datev_export__property__accountsWithoutDatevId": "Cuentas sin id. de DATEV", "@sage/xtrem-finance/nodes__datev_export__property__attributeType1": "Tipo de atributo 1", "@sage/xtrem-finance/nodes__datev_export__property__attributeType2": "Tipo de atributo 2", "@sage/xtrem-finance/nodes__datev_export__property__company": "Sociedad", "@sage/xtrem-finance/nodes__datev_export__property__customersWithoutDatevId": "Clientes sin id. de DATEV", "@sage/xtrem-finance/nodes__datev_export__property__dateRange": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__datev_export__property__datevConsultantNumber": "Número de asesor de DATEV", "@sage/xtrem-finance/nodes__datev_export__property__datevCustomerNumber": "Número de cliente de DATEV", "@sage/xtrem-finance/nodes__datev_export__property__datevExportAccounts": "Cuentas de exportación de DATEV", "@sage/xtrem-finance/nodes__datev_export__property__datevExportBusinessRelations": "Relaciones de negocio de exportación de DATEV", "@sage/xtrem-finance/nodes__datev_export__property__datevExportJournalEntryLines": "Apuntes de exportación de DATEV", "@sage/xtrem-finance/nodes__datev_export__property__dimensionType1": "Eje 1", "@sage/xtrem-finance/nodes__datev_export__property__dimensionType2": "Eje 2", "@sage/xtrem-finance/nodes__datev_export__property__doAccounts": "Do accounts", "@sage/xtrem-finance/nodes__datev_export__property__doCustomersSuppliers": "Do customers suppliers", "@sage/xtrem-finance/nodes__datev_export__property__doJournalEntries": "Do journal entries", "@sage/xtrem-finance/nodes__datev_export__property__endDate": "<PERSON><PERSON> de fin", "@sage/xtrem-finance/nodes__datev_export__property__fiscalYearStart": "In<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__datev_export__property__id": "Id.", "@sage/xtrem-finance/nodes__datev_export__property__isLocked": "Bloqueado", "@sage/xtrem-finance/nodes__datev_export__property__skrCoa": "SKR COA", "@sage/xtrem-finance/nodes__datev_export__property__startDate": "Fecha de inicio", "@sage/xtrem-finance/nodes__datev_export__property__status": "Estado", "@sage/xtrem-finance/nodes__datev_export__property__suppliersWithoutDatevId": "Proveedores sin id. de DATEV", "@sage/xtrem-finance/nodes__datev_export__property__timeStamp": "Marca de tiempo", "@sage/xtrem-finance/nodes__datev_export__start_export": "La exportación de DATEV se ha iniciado.", "@sage/xtrem-finance/nodes__datev_export__start_extract_accounts": "Inicio de extracción de cuenta de DATEV", "@sage/xtrem-finance/nodes__datev_export__start_extract_customers_suppliers": "Inicio de extracción de clientes y proveedores de DATEV", "@sage/xtrem-finance/nodes__datev_export__start_extract_journal_entry_lines": "Inicio de extracción de apuntes de DATEV", "@sage/xtrem-finance/nodes__datev_export__start_extraction": "Inicio de extracción de DATEV", "@sage/xtrem-finance/nodes__datev_export__stop_extraction": "Cancelación de la extracción de DATEV el {{stopDate}}", "@sage/xtrem-finance/nodes__datev_export__success_description": "La extracción de DATEV se ha realizado correctamente.", "@sage/xtrem-finance/nodes__datev_export__success_notification_title": "Extracción de DATEV finalizada", "@sage/xtrem-finance/nodes__datev_export__user_download_accounts": "<PERSON><PERSON><PERSON> cuenta<PERSON>", "@sage/xtrem-finance/nodes__datev_export__user_download_journal_entry_lines": "Descar<PERSON> a<PERSON>", "@sage/xtrem-finance/nodes__datev_export__user_notifications_download_customers_and_suppliers": "Descargar clientes y proveedores", "@sage/xtrem-finance/nodes__datev_export__user_notifications_history": "Historial", "@sage/xtrem-finance/nodes__datev_export_account__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__datev_export_account__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__datev_export_account__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__datev_export_account__node_name": "Cuenta de exportación de DATEV", "@sage/xtrem-finance/nodes__datev_export_account__property__account": "C<PERSON><PERSON>", "@sage/xtrem-finance/nodes__datev_export_account__property__datevExport": "Exportación de DATEV", "@sage/xtrem-finance/nodes__datev_export_account__property__datevId": "Id. de DATEV", "@sage/xtrem-finance/nodes__datev_export_account__property__name": "Nombre", "@sage/xtrem-finance/nodes__datev_export_business_relation__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__datev_export_business_relation__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__datev_export_business_relation__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__datev_export_business_relation__node_name": "Relación de negocio de exportación de DATEV", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__businessRelation": "Relación de negocio", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__city": "Ciudad", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__country": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__datevExport": "Exportación de DATEV", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__datevId": "Id. de DATEV", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__name": "Nombre", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__postcode": "Código postal", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__street": "Calle", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__taxIdNumber": "NIF-IVA", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__node_name": "Apunte de exportación de DATEV", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__businessEntityTaxIdNumber": "NIF-IVA de entidad empresarial", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__companyCurrency": "Divisa de sociedad", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__companyFxRate": "Tipo de cambio de sociedad", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__companyValue": "Valor de sociedad", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevAccountId": "Id. de cuenta de DATEV", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevContraAccountId": "Id. de cuenta de contrapartida de DATEV", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevExport": "Exportación de DATEV", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevSign": "Signo en DATEV", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__description": "Descripción", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__dimension1": "Sección 1", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__dimension2": "Sección 2", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__journalEntryLine": "Apunte", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__locked": "Bloqueado", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__number": "Número", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__postingKey": "<PERSON><PERSON><PERSON> contable", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__siteTaxIdNumber": "N.º id. fiscal de planta", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__supplierDocumentNumber": "Número de documento de proveedor", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__transactionValue": "Valor de transacción", "@sage/xtrem-finance/nodes__datev_export_listener__datev_file": "Archivo DATEV", "@sage/xtrem-finance/nodes__datev_export_listener__datev_file_ready": "Archivo listo para descargar", "@sage/xtrem-finance/nodes__datev_export_listener__node_name": "Proceso de escucha de exportación de DATEV", "@sage/xtrem-finance/nodes__initialize_open_item__node_name": "Inicialización de vencimiento", "@sage/xtrem-finance/nodes__intacct-bank-account-transaction_feed__site_legislation_and_coa_legislation_dont_match": "La legislación de la planta debe ser la misma que la legislación del plan de cuentas.", "@sage/xtrem-finance/nodes__journal_entry__ap_invoice_reference": "La referencia de la factura contable de proveedor solo se puede utilizar en asientos que procedan de una factura contable de proveedor.", "@sage/xtrem-finance/nodes__journal_entry__ar_invoice_reference": "La referencia de la factura contable de cliente solo se puede utilizar en asientos que procedan de una factura contable de cliente.", "@sage/xtrem-finance/nodes__journal_entry__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__journal_entry__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__journal_entry__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__journal_entry__contra_journal_entry_line_is_mandatory_on_new_documents": "Introduce un asiento de contrapartida en la línea.", "@sage/xtrem-finance/nodes__journal_entry__document_type_not_allowed": "El tipo de documento solo se puede definir para la legislación francesa.", "@sage/xtrem-finance/nodes__journal_entry__journal_entry_dimension_line_not_equal_to_journal_line": "El importe de asignación atributo/sección total es diferente al importe del apunte.", "@sage/xtrem-finance/nodes__journal_entry__mutation__post": "Contabilizar", "@sage/xtrem-finance/nodes__journal_entry__mutation__post__failed": "Error de contabilización", "@sage/xtrem-finance/nodes__journal_entry__mutation__post__parameter__journalEntry": "Asiento", "@sage/xtrem-finance/nodes__journal_entry__no_attribute_value_account": "Las secciones obligatorias ({{dimensions}}) de la cuenta no se han introducido en la cuenta {{lineAccount}} del apunte {{lineCount}}.", "@sage/xtrem-finance/nodes__journal_entry__no_attribute_value_company": "Las secciones obligatorias ({{dimensions}}) de la sociedad no se han introducido en la cuenta {{lineAccount}} del apunte {{lineCount}}.", "@sage/xtrem-finance/nodes__journal_entry__no_sequence_number": "El número de secuencia del asiento no se puede generar. Introduce un número de secuencia por defecto.", "@sage/xtrem-finance/nodes__journal_entry__node_name": "Asiento", "@sage/xtrem-finance/nodes__journal_entry__number_change_forbidden": "Solo puedes editar el número de asiento en los registros nuevos.", "@sage/xtrem-finance/nodes__journal_entry__property__apInvoice": "Factura contable de proveedor", "@sage/xtrem-finance/nodes__journal_entry__property__arInvoice": "Factura contable de cliente", "@sage/xtrem-finance/nodes__journal_entry__property__description": "Descripción", "@sage/xtrem-finance/nodes__journal_entry__property__documentType": "Tipo de documento", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationApp": "Aplicación de integración contable", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationAppRecordId": "Id. de registro de aplicación de integración contable", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationAppUrl": "URL de aplicación de integración contable", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationStatus": "Estado de integración contable", "@sage/xtrem-finance/nodes__journal_entry__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__journal_entry__property__internalFinanceIntegrationStatus": "Estado de integración de contabilidad interna", "@sage/xtrem-finance/nodes__journal_entry__property__journal": "Diario", "@sage/xtrem-finance/nodes__journal_entry__property__lines": "Líneas", "@sage/xtrem-finance/nodes__journal_entry__property__number": "Número", "@sage/xtrem-finance/nodes__journal_entry__property__origin": "Origen", "@sage/xtrem-finance/nodes__journal_entry__property__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/nodes__journal_entry__property__postingStatus": "Estado de contabilización", "@sage/xtrem-finance/nodes__journal_entry__property__reference": "Referencia", "@sage/xtrem-finance/nodes__journal_entry__query__areFinanceIntegrationPackagesActive": "Paquetes de integración contable activos", "@sage/xtrem-finance/nodes__journal_entry__query__areFinanceIntegrationPackagesActive__failed": "Error de paquetes de integración contable activos", "@sage/xtrem-finance/nodes__journal_entry__query__areFinanceIntegrationPackagesActive__parameter__dummy": "Simulación", "@sage/xtrem-finance/nodes__journal_entry__reconcile_journal_entry_by_key": "El asiento está descuadrado en la planta financiera {{financialSite}}.", "@sage/xtrem-finance/nodes__journal_entry__two_journal_entry_lines_mandatory": "Añade al menos dos apuntes al asiento.", "@sage/xtrem-finance/nodes__journal_entry_inquiry__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__journal_entry_inquiry__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__journal_entry_inquiry__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__journal_entry_inquiry__mutation__singleRecord": "Single record", "@sage/xtrem-finance/nodes__journal_entry_inquiry__mutation__singleRecord__failed": "Single record failed.", "@sage/xtrem-finance/nodes__journal_entry_inquiry__node_name": "Consulta de asiento", "@sage/xtrem-finance/nodes__journal_entry_inquiry__property__journalEntryLines": "Apuntes", "@sage/xtrem-finance/nodes__journal_entry_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__journal_entry_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__journal_entry_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__journal_entry_line__business_entity_reference_mandatory": "La cuenta {{account}} es de control. Introduce una entidad empresarial.", "@sage/xtrem-finance/nodes__journal_entry_line__business_entity_reference_not_possible": "La cuenta {{account}} no es de control. No puedes introducir una entidad empresarial.", "@sage/xtrem-finance/nodes__journal_entry_line__journal_entry_type_line_is_mandatory_on_new_documents": "Introduce el tipo de asiento en la línea.", "@sage/xtrem-finance/nodes__journal_entry_line__node_name": "Apunte", "@sage/xtrem-finance/nodes__journal_entry_line__property__account": "C<PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__accountingStagingLines": "Líneas contables provisionales", "@sage/xtrem-finance/nodes__journal_entry_line__property__attributesAndDimensions": "Atributos y secciones", "@sage/xtrem-finance/nodes__journal_entry_line__property__baseTax": "Impuesto base", "@sage/xtrem-finance/nodes__journal_entry_line__property__blank": "Vacía", "@sage/xtrem-finance/nodes__journal_entry_line__property__businessEntity": "Entidad empresarial", "@sage/xtrem-finance/nodes__journal_entry_line__property__businessSiteAttribute": "Atributo de planta empresarial", "@sage/xtrem-finance/nodes__journal_entry_line__property__chartOfAccount": "Plan de cuentas", "@sage/xtrem-finance/nodes__journal_entry_line__property__commonReference": "Referencia común", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyAmount": "Importe de sociedad", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyCredit": "Haber de sociedad", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyCurrency": "Divisa de sociedad", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyDebit": "Debe de sociedad", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyFxRate": "Tipo de cambio de sociedad", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyFxRateDivisor": "Divisor de tipo de cambio de sociedad", "@sage/xtrem-finance/nodes__journal_entry_line__property__contraAccount": "Cuenta de contrapartida", "@sage/xtrem-finance/nodes__journal_entry_line__property__contraJournalEntryLine": "Apunte de contrapartida", "@sage/xtrem-finance/nodes__journal_entry_line__property__customerAttribute": "Atributo de cliente", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevBusinessEntityTaxIdNumber": "NIF-IVA de entidad empresarial en DATEV", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevCompanyAmount": "Importe de sociedad en DATEV", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevContraAccountId": "Id. de cuenta de contrapartida en DATEV", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevPostingKey": "Clave contable en DATEV", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevTransactionAmount": "Importe de transacción en DATEV", "@sage/xtrem-finance/nodes__journal_entry_line__property__deductibleTaxRate": "Tipo impositivo deducible", "@sage/xtrem-finance/nodes__journal_entry_line__property__description": "Descripción", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension01": "Sección 01", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension02": "Sección 02", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension03": "Sección 03", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension04": "Sección 04", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension05": "Sección 05", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension06": "Sección 06", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension07": "Sección 07", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension08": "Sección 08", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension09": "Sección 09", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension10": "Sección 10", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension11": "Sección 11", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension12": "Sección 12", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension13": "Sección 13", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension14": "Sección 14", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension15": "Sección 15", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension16": "Sección 16", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension17": "Sección 17", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension18": "Sección 18", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension19": "Sección 19", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension20": "Sección 20", "@sage/xtrem-finance/nodes__journal_entry_line__property__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/nodes__journal_entry_line__property__employeeAttribute": "Atributo de trabajador", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteAmount": "Importe de planta financiera", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteAttribute": "Atributo de planta financiera", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteCredit": "Haber de planta financiera", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteCurrency": "Divisa de planta financiera", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteDebit": "Debe de planta financiera", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteFxRate": "Tipo de cambio de planta financiera", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteFxRateDivisor": "Divisor de tipo de cambio de planta financiera", "@sage/xtrem-finance/nodes__journal_entry_line__property__fxRateDate": "Fecha de tipo de cambio", "@sage/xtrem-finance/nodes__journal_entry_line__property__inquiryDescription": "Descripción de consulta", "@sage/xtrem-finance/nodes__journal_entry_line__property__inquiryTransactionCurrency": "Divisa de transacción de consulta", "@sage/xtrem-finance/nodes__journal_entry_line__property__isBalanceLine": "Is balance line", "@sage/xtrem-finance/nodes__journal_entry_line__property__itemAttribute": "Atributo de artículo", "@sage/xtrem-finance/nodes__journal_entry_line__property__journalEntry": "Asiento", "@sage/xtrem-finance/nodes__journal_entry_line__property__journalEntryTypeLine": "Línea de tipo de asiento", "@sage/xtrem-finance/nodes__journal_entry_line__property__manufacturingSiteAttribute": "Atributo de planta de producción", "@sage/xtrem-finance/nodes__journal_entry_line__property__numericSign": "Numeric sign", "@sage/xtrem-finance/nodes__journal_entry_line__property__projectAttribute": "Atributo de proyecto", "@sage/xtrem-finance/nodes__journal_entry_line__property__rateDescription": "Descripción de tipo impositivo", "@sage/xtrem-finance/nodes__journal_entry_line__property__sign": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__signedTransactionAmount": "Importe de transacción absoluto", "@sage/xtrem-finance/nodes__journal_entry_line__property__stockSiteAttribute": "Atributo de planta de stock", "@sage/xtrem-finance/nodes__journal_entry_line__property__supplierAttribute": "Atri<PERSON><PERSON> de <PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__supplierDocumentNumber": "Número de documento de proveedor", "@sage/xtrem-finance/nodes__journal_entry_line__property__taskAttribute": "Atributo de tarea", "@sage/xtrem-finance/nodes__journal_entry_line__property__tax": "Impuesto", "@sage/xtrem-finance/nodes__journal_entry_line__property__taxDate": "Fecha de impuesto", "@sage/xtrem-finance/nodes__journal_entry_line__property__taxExternalReference": "Referencia externa de impuesto", "@sage/xtrem-finance/nodes__journal_entry_line__property__taxRate": "Tipo impositivo", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionAmount": "Importe de transacción", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionCredit": "Haber de transacción", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionDebit": "Debe de transacción", "@sage/xtrem-finance/nodes__journal_entry_line__property__validationDate": "Fecha de validación", "@sage/xtrem-finance/nodes__journal_entry_line__tax_date_empty": "Deja la fecha del impuesto en blanco.", "@sage/xtrem-finance/nodes__journal_entry_line__tax_date_mandatory": "Introduce una fecha de impuesto.", "@sage/xtrem-finance/nodes__journal_entry_line__tax_ref_empty": "Deja la referencia del impuesto en blanco.", "@sage/xtrem-finance/nodes__journal_entry_line__tax_ref_mandatory": "Introduce una referencia para el impuesto.", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__node_name": "Sección de apunte", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__analyticalData": "Datos analític<PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__businessSite": "Planta empresarial", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__companyAmount": "Importe de sociedad", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__companyCurrency": "Divisa de sociedad", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__customer": "Cliente", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension01": "Sección 01", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension02": "Sección 02", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension03": "Sección 03", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension04": "Sección 04", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension05": "Sección 05", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension06": "Sección 06", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension07": "Sección 07", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension08": "Sección 08", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension09": "Sección 09", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension10": "Sección 10", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension11": "Sección 11", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension12": "Sección 12", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension13": "Sección 13", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension14": "Sección 14", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension15": "Sección 15", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension16": "Sección 16", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension17": "Sección 17", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension18": "Sección 18", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension19": "Sección 19", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension20": "Sección 20", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__employee": "Trabajador", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__financialSite": "Planta financiera", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__financialSiteAmount": "Importe de planta financiera", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__financialSiteCurrency": "Divisa de planta financiera", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__hasAttributesOrDimenionsChanged": "Atributos o secciones cambiados", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__journalEntryLine": "Apunte", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__manufacturingSite": "Planta de producción", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__project": "Proyecto", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__stockSite": "Planta de stock", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__storedAttributes": "Atributos almacenados", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__storedComputedAttributes": "Atributos calculados almacenados", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__storedDimensions": "Secciones almacenadas", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__task": "Tarea", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__transactionAmount": "Importe de transacción", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-finance/nodes__journal_entry_line_staging__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__journal_entry_line_staging__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__journal_entry_line_staging__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__journal_entry_line_staging__node_name": "Apunte provisional", "@sage/xtrem-finance/nodes__journal_entry_line_staging__property__accountingStaging": "Contabilidad provisional", "@sage/xtrem-finance/nodes__journal_entry_line_staging__property__journalEntryLine": "Apunte", "@sage/xtrem-finance/nodes__journal-entry__cant_post_journal_entry_when_status_is_not_draft_nor_error": "La factura del asiento no se puede contabilizar porque el estado de contabilización no es \"{{draft}}\" o \"{{error}}\".", "@sage/xtrem-finance/nodes__journal-entry__posted": "El asiento se ha contabilizado.", "@sage/xtrem-finance/nodes__payment__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__payment__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__payment__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment": "Anular pago", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__failed": "Error al anular el pago", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__parameter__payment": "Pago", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__parameter__voidDate": "Fecha de anulación", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__parameter__voidText": "Nota de anulación", "@sage/xtrem-finance/nodes__payment__node_name": "Pago", "@sage/xtrem-finance/nodes__payment_document_line__ap_invoice_reference_mandatory": "Añade la referencia a la factura contable de proveedor.", "@sage/xtrem-finance/nodes__payment_document_line__ap_invoice_reference_not_allowed": "No puedes hacer referencia a la factura contable de proveedor.", "@sage/xtrem-finance/nodes__payment_document_line__ar_invoice_reference_mandatory": "Añade la referencia a la factura contable de cliente.", "@sage/xtrem-finance/nodes__payment_document_line__ar_invoice_reference_not_allowed": "No puedes hacer referencia a la factura contable de cliente.", "@sage/xtrem-finance/nodes__payment_document_line__wrong_ap_invoice_posting_status": "La factura contable de proveedor debe estar contabilizada.", "@sage/xtrem-finance/nodes__payment_document_line__wrong_ar_invoice_posting_status": "La factura contable de cliente debe estar contabilizada.", "@sage/xtrem-finance/nodes__receipt__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-finance/nodes__receipt__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-finance/nodes__receipt__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-finance/nodes__receipt__mutation__createReceipt": "<PERSON><PERSON><PERSON> cobro", "@sage/xtrem-finance/nodes__receipt__mutation__createReceipt__parameter__receiptHeader": "Cabecera de cobro", "@sage/xtrem-finance/nodes__receipt__mutation__createReceipt__parameter__receiptLines": "Líneas de cobro", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment": "Anular pago", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__failed": "Error al anular el pago", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__parameter__payment": "Pago", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__parameter__voidDate": "Fecha de anulación", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__parameter__voidText": "Nota de anulación", "@sage/xtrem-finance/nodes__receipt__node_name": "Cobro", "@sage/xtrem-finance/nodes__receipt__payment_amount_discrepancy": "El importe de pago del documento ({{amount}}) debe coincidir con el importe de pago total de todas las líneas ({{total}}).", "@sage/xtrem-finance/notification_bulk_open_item_payment__description_error": "Ha habido un error al forzar el pago de los vencimientos. Para más información, revisa las trazas de las tareas por lotes.", "@sage/xtrem-finance/notification_bulk_open_item_payment__description_success": "Se ha forzado el pago de los vencimientos.", "@sage/xtrem-finance/package__name": "Contabilidad", "@sage/xtrem-finance/page__void_date__mandatory": "Introduce la fecha.", "@sage/xtrem-finance/page_fragments__base_payment__date_issued": "Fecha de pago", "@sage/xtrem-finance/page_fragments__base_payment__date_received": "<PERSON><PERSON>", "@sage/xtrem-finance/page_fragments__payment_information__amount_in_bank_currency_must_be_positive": "El importe en la divisa bancaria debe ser superior a 0.", "@sage/xtrem-finance/page_fragments__payment_information__date_issued": "Fecha de pago", "@sage/xtrem-finance/page_fragments__payment_information__date_received": "<PERSON><PERSON>", "@sage/xtrem-finance/page_fragments__payment_information__payment_amount_can_not_be_negative": "El importe del pago debe ser superior o igual a 0.", "@sage/xtrem-finance/page_fragments__payment_information__transaction_information_can_not_exceed_100_characters": "La información de la transacción no puede superar los 100 caracteres.", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionCriteriaBlock____title": "Criterios", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionDocumentType____title": "Tipo de documento", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___createStamp": "Envío", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___id": "Id.", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__batchId": "<PERSON><PERSON><PERSON> de tanda", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__message": "Men<PERSON><PERSON>", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__originDocumentNumber": "Número de documento", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__originDocumentType": "Tipo de documento", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__postingStatus": "Estado", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__status": "Estado de notificación", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__targetDocumentNumber": "Número de documento de destino", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__targetDocumentType": "Tipo de documento de destino", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__wasResent": "Reenviada", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____dropdownActions__title__2": "Editar secciones en documento de origen", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____dropdownActions__title__3": "Reintentar", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____title": "Resul<PERSON><PERSON>", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionSection____title": "Contabilidad", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionStatus____title": "Estado", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocuments____columns__title__sourceDocumentNumber": "Número", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocuments____columns__title__sourceDocumentType": "Documento", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocuments____title": "Documentos de origen", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocumentsBlock____title": "Documentos de origen", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__adjustmentAmount": "Importe de regularización", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__amount": "Importe", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__discountAmount": "Importe de descuento", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__bankAccount__name": "Cuenta bancaria", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__isVoided": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__number": "Número de pago", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__paymentDate": "Fecha de pago", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__paymentMethod": "Forma de pago", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__reference": "Información de transacción", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__penaltyAmount": "Importe de recargo", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____title": "Pagos", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__adjustmentAmount": "Importe de regularización", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__amount": "Importe", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__discountAmount": "Importe de descuento", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__bankAccount__name": "Cuenta bancaria", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__isVoided": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__number": "Número de co<PERSON>", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__paymentDate": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__paymentMethod": "Forma de pago", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__reference": "Información de transacción", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__penaltyAmount": "Importe de recargo", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____title": "Co<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment__amount____title": "Importe de pago", "@sage/xtrem-finance/page-fragments__base_payment__amountBankCurrency____title": "Importe en divisa bancaria", "@sage/xtrem-finance/page-fragments__base_payment__bankAccount____title": "Cuenta bancaria", "@sage/xtrem-finance/page-fragments__base_payment__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-finance/page-fragments__base_payment__currency____title": "Divisa", "@sage/xtrem-finance/page-fragments__base_payment__customer____title": "Cliente", "@sage/xtrem-finance/page-fragments__base_payment__financialSite____title": "Planta financiera", "@sage/xtrem-finance/page-fragments__base_payment__isVoided____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment__number____title": "Número", "@sage/xtrem-finance/page-fragments__base_payment__paymentDate____title": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment__paymentMethod____title": "Forma de pago", "@sage/xtrem-finance/page-fragments__base_payment__reference____title": "Información de transacción", "@sage/xtrem-finance/page-fragments__base_payment__supplier____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment__voidDate____title": "Anulación", "@sage/xtrem-finance/page-fragments__base_payment__voidText____title": "Nota de anulación", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__adjustmentAmount": "Importe de regularización", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__purchaseDocumentNumber": "Número de documento", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__reference": "Referencia", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__reference": "Referencia", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__salesDocumentNumber": "Número de documento", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__currency__id": "Divisa", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__discountAmount": "Importe de descuento", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__origin": "Origen", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__penaltyAmount": "Importe de recargo", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__signedAmount": "Importe", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__signedAmountBankCurrency": "Importe en divisa bancaria", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____title": "Líneas", "@sage/xtrem-finance/page-fragments__open_item_discount__discountDate____title": "Fecha de descuento", "@sage/xtrem-finance/page-fragments__open_item_discount__discountType____title": "Tipo de descuento", "@sage/xtrem-finance/page-fragments__open_item_discount__displayDiscountPaymentDate____title": "Fecha límite de pago de descuento", "@sage/xtrem-finance/page-fragments__open_item_general__businessRelation____title": "Relación de negocio", "@sage/xtrem-finance/page-fragments__open_item_general__companyAmountDueSigned____title": "Importe pendiente de sociedad", "@sage/xtrem-finance/page-fragments__open_item_general__documentNumberLink____title": "Número de documento", "@sage/xtrem-finance/page-fragments__open_item_general__documentType____title": "Tipo de documento", "@sage/xtrem-finance/page-fragments__open_item_general__dueDate____title": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/page-fragments__open_item_general__financialSite____title": "Planta financiera", "@sage/xtrem-finance/page-fragments__open_item_general__transactionAmountDueSigned____title": "Importe pendiente de transacción", "@sage/xtrem-finance/page-fragments__open_item_penalty__penaltyPaymentType____title": "Tipo de recargo", "@sage/xtrem-finance/page-fragments__payment_information__amount____title": "Importe de pago", "@sage/xtrem-finance/page-fragments__payment_information__amountBankCurrency____title": "Importe en divisa bancaria", "@sage/xtrem-finance/page-fragments__payment_information__bankAccount____title": "Cuenta bancaria", "@sage/xtrem-finance/page-fragments__payment_information__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-finance/page-fragments__payment_information__currency____title": "Divisa", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__country__name": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__id": "Id.", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__name": "Nombre", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__taxIdNumber": "NIF-IVA", "@sage/xtrem-finance/page-fragments__payment_information__customer____title": "Cliente", "@sage/xtrem-finance/page-fragments__payment_information__date____title": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____placeholder": "Planta financiera", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____title": "Planta financiera", "@sage/xtrem-finance/page-fragments__payment_information__paymentMethod____title": "Forma de pago", "@sage/xtrem-finance/page-fragments__payment_information__reference____title": "Información de transacción", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__country__name": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__id": "Id.", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__name": "Nombre", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__taxIdNumber": "NIF-IVA", "@sage/xtrem-finance/page-fragments__payment_information__supplier____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__payment_information__type____title": "Tipo", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__number": "Número de factura", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__origin": "Origen", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__reference": "Referencia", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__number": "Número de factura", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__origin": "Origen", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__reference": "Referencia", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__adjustmentAmount": "Importe de regularización", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__amountDue": "Importe pendiente", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__creditAmount": "Importe de abono", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__currency__id": "Divisa", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__discountPaymentAmount": "Importe de descuento", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__paymentAmount": "Importe de pago", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__penaltyPaymentAmount": "Importe de recargo", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__remainingCompanyAmount": "Importe pendiente de sociedad", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__totalAmount": "Importe total", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__totalCompanyAmount": "Importe total de sociedad", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__totalCompanyAmountPaid": "Importe pagado de sociedad", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__id__title": "Id. de proveedor facturador", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line_4__title": "Tipo", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line_5__title": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line10__title": "Total sin impuestos", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line11__title": "Impuestos regularizados", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line12__title": "Total impuestos", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line13__title": "<PERSON><PERSON> de factura de proveedor", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line14__title": "Número de documento de proveedor", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line15__title": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line16__title": "Condiciones de pago", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line2__title": "Proveedor <PERSON>dor", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line4__title": "Tipo", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line5__title": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line6__title": "Planta financiera", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line7__title": "Total con impuestos", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line8__title": "Referencia", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line9__title": "Origen", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__title__title": "Número", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__titleRight__title": "Estado de contabilización", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__2": "Borradores", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__3": "Contabilizadas", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__4": "En curso", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__5": "Error", "@sage/xtrem-finance/pages__accounts_payable_invoice____objectTypePlural": "Facturas contables de proveedor", "@sage/xtrem-finance/pages__accounts_payable_invoice____objectTypeSingular": "Factura contable de proveedor", "@sage/xtrem-finance/pages__accounts_payable_invoice____subtitle": "Factura contable de proveedor", "@sage/xtrem-finance/pages__accounts_payable_invoice____title": "Factura contable de proveedor", "@sage/xtrem-finance/pages__accounts_payable_invoice__billBySupplier____title": "Proveedor <PERSON>dor", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____columns__title__id": "Código ISO 4217", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____columns__title__symbol": "Símbolo", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____title": "Divisa", "@sage/xtrem-finance/pages__accounts_payable_invoice__dueDate____title": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationApp____title": "Aplicación", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationAppRecordId____title": "Referencia de integración contable", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationAppRecordIdLink____title": "Referencia de integración contable", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationAppUrl____title": "URL de integración contable", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationStatus____title": "Estado de integración contable", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__columns__title___id": "Id.", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__columns__title__id__2": "Id.", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__title": "Divisa", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__title__5": "Motor de impuestos", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__title__6": "Divisa", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____title": "Planta financiera", "@sage/xtrem-finance/pages__accounts_payable_invoice__generalSection____title": "General", "@sage/xtrem-finance/pages__accounts_payable_invoice__goToSysNotificationPage____title": "Reintentar", "@sage/xtrem-finance/pages__accounts_payable_invoice__internalFinanceIntegrationStatus____title": "Estado interno", "@sage/xtrem-finance/pages__accounts_payable_invoice__invoiceDate____title": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__account__composedDescription__title__3": "Descripción compuesta", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__currency__id__title": "Nombre", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__currency__id__title__2": "Código ISO 4217", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__currency__id__title__3": "Símbolo", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__currency__id__title__4": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__originLine___id__title": "Id.", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__account__composedDescription": "C<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__amount": "Importe", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__amountExcludingTax": "Importe sin impuestos", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__amountIncludingTax": "Importe con impuestos", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__description": "Descripción", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__financialSite__name": "Planta financiera", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__lineAmountExcludingTax": "Importe sin impuestos", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__lineAmountIncludingTax": "Importe con impuestos", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__lineType": "Tipo de línea", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__originLine___id": "Línea de factura", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__recipientSite__name": "Planta de recepción", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__taxAmount": "Importe de impuesto", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__taxAmountAdjusted": "Importe de impuesto", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__taxDate": "Fecha de impuesto", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__uiTaxes": "Impuestos de seguro de desempleo", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__dropdownActions__title": "Detalles de impuesto", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____title": "Líneas", "@sage/xtrem-finance/pages__accounts_payable_invoice__number____title": "Número", "@sage/xtrem-finance/pages__accounts_payable_invoice__origin____title": "Origen", "@sage/xtrem-finance/pages__accounts_payable_invoice__paymentStatus____title": "Estado de pago", "@sage/xtrem-finance/pages__accounts_payable_invoice__paymentTerm____title": "Condiciones de pago", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDate____title": "Fecha de contabilización", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__financeIntegrationAppRecordId": "Referencia de integración contable", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__postingStatus": "Estado", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__targetDocumentNumber": "Número de documento", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__targetDocumentType": "Tipo de documento", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____title": "Contabilización", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingMessageBlock____title": "Detalles de error", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingSection____title": "Contabilización", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingStatus____title": "Estado de contabilización", "@sage/xtrem-finance/pages__accounts_payable_invoice__rateDescription____title": "Tipo de cambio", "@sage/xtrem-finance/pages__accounts_payable_invoice__reference____title": "Referencia", "@sage/xtrem-finance/pages__accounts_payable_invoice__supplierDocumentDate____title": "<PERSON><PERSON> de factura de proveedor", "@sage/xtrem-finance/pages__accounts_payable_invoice__supplierDocumentNumber____title": "Número de factura de proveedor", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__columns__account__composedDescription__title__3": "Descripción compuesta", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__columns__originLine___id__title": "Id.", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__account__composedDescription": "C<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__amount": "Importe", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__financialSite__name": "Planta financiera", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__recipientSite__name": "Planta de destino", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__taxDetail": "Detalles de impuesto", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__taxLineTaxAmount": "Importe de impuesto", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__dropdownActions__title": "Detalles de impuesto", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____title": "Líneas de detalles de impuesto", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__postfix__taxRate": "%", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title___sortValue": "Número de línea", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__tax": "Impuesto", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxableAmount": "Base imponible", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxAmount": "Importe", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxAmountAdjusted": "Importe regularizado", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxCategory": "Categoría", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxRate": "Tipo impositivo", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____title": "Totales", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalAmountExcludingTax____title": "Calculado sin impuestos", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalAmountIncludingTax____title": "Calculado con impuestos", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalsSection____title": "Totales", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalsSectionTaxTotalsBlock____title": "", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalTaxAmount____title": "Impuestos calculados", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalTaxAmountAdjusted____title": "Impuestos calculados regularizados", "@sage/xtrem-finance/pages__accounts_payable_invoice__type____title": "Tipo", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__businessEntityId__title": "<PERSON><PERSON>. de <PERSON>edor", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__closeReason__title": "Motivo de cierre", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__closeText__title": "Nota de c<PERSON>re", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__companyAmountDueSigned__title": "Importe pendiente de sociedad", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__companyAmountPaid__title": "Importe pagado de sociedad", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__companyCurrency__title": "Divisa de sociedad", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__currency__title": "Divisa", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__documentType__title": "Tipo de documento", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__financialSite__title": "Planta financiera", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__forcedAmountPaid__title": "Importe pagado forzado", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__line2__title": "Nombre de proveedor", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__line2Right__title": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__remainingCompanyAmount__title": "Importe restante de sociedad", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__remainingTransactionAmount__title": "Importe restante de transacción", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__title__title": "Número de documento", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__titleRight__title": "Estado", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__transactionAmountDueSigned__title": "Importe pendiente de transacción", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__transactionAmountPaid__title": "Importe pagado de transacción", "@sage/xtrem-finance/pages__accounts_payable_open_item____objectTypePlural": "Vencimientos de proveedor", "@sage/xtrem-finance/pages__accounts_payable_open_item____objectTypeSingular": "Vencimiento de proveedor", "@sage/xtrem-finance/pages__accounts_payable_open_item____title": "Vencimiento de proveedor", "@sage/xtrem-finance/pages__accounts_payable_open_item__forcedAmountPaidSigned____title": "Importe pagado forzado", "@sage/xtrem-finance/pages__accounts_payable_open_item__generalSection____title": "General", "@sage/xtrem-finance/pages__accounts_payable_open_item__status____title": "Estado", "@sage/xtrem-finance/pages__accounts_payable_open_item__transactionAmountPaidSigned____title": "Total pagado", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__bulkActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__businessEntityId__title": "<PERSON><PERSON>. de <PERSON>edor", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__closeReason__title": "Motivo de cierre", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__closeText__title": "Nota de c<PERSON>re", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__companyAmountDueSigned__title": "Importe pendiente de sociedad", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__companyAmountPaid__title": "Importe pagado de sociedad", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__companyCurrency__title": "Divisa de sociedad", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__currency__title": "Divisa", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__displayDiscountPaymentDate__title": "Fecha límite de pago de descuento", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__documentType__title": "Tipo de documento", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__financialSite__title": "Planta financiera", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__forcedAmountPaid__title": "Importe pagado forzado", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__line2__title": "Nombre de proveedor", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__line2Right__title": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__remainingCompanyAmount__title": "Importe restante de sociedad", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__remainingTransactionAmount__title": "Importe restante de transacción", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__title__title": "Número de documento", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__titleRight__title": "Estado", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__transactionAmountDueSigned__title": "Importe pendiente de transacción", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__transactionAmountPaid__title": "Importe pagado de transacción", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title": "Sin pagar por completo", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__2": "Todos", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__3": "<PERSON> pagar", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__4": "Parcia<PERSON><PERSON> pagados", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__5": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____objectTypePlural": "Inicialización de pagos de proveedor", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____objectTypeSingular": "Inicialización de pago de proveedor", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____title": "Inicialización de pago de proveedor", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__closeReason____title": "Motivo de cierre", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__closeText____title": "Nota de c<PERSON>re", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__forcedAmountPaidSigned____title": "Importe pagado forzado", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__generalSection____title": "General", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__save____title": "Guardar", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__transactionAmountPaidSigned____title": "Total pagado", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line_4__title": "Planta financiera", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line13__title": "Fecha de pago", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line2__title": "Descripción", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line2Right__title": "Fecha de contabilización", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line4__title": "Planta financiera", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line7__title": "Cliente pagador", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line8__title": "Importe de anticipo", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line9__title": "Referencia", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__title__title": "Número", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__titleRight__title": "Estado de contabilización", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__2": "Borradores", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__3": "Contabilizados", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__4": "En curso", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__5": "Error", "@sage/xtrem-finance/pages__accounts_receivable_advance____objectTypePlural": "Anticipos de cliente", "@sage/xtrem-finance/pages__accounts_receivable_advance____objectTypeSingular": "Anticipo de cliente", "@sage/xtrem-finance/pages__accounts_receivable_advance____title": "Anticipo de cliente", "@sage/xtrem-finance/pages__accounts_receivable_advance__advanceAmount____title": "Importe de anticipo", "@sage/xtrem-finance/pages__accounts_receivable_advance__bankAccount____title": "Banco", "@sage/xtrem-finance/pages__accounts_receivable_advance__description____title": "Descripción", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationApp____title": "Aplicación", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationAppRecordId____title": "Referencia de integración contable", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationAppRecordIdLink____title": "Referencia de integración contable", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationAppUrl____title": "URL de integración contable", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationStatus____title": "Estado de integración contable", "@sage/xtrem-finance/pages__accounts_receivable_advance__financialSite____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-finance/pages__accounts_receivable_advance__financialSite____title": "Planta financiera", "@sage/xtrem-finance/pages__accounts_receivable_advance__generalSection____title": "General", "@sage/xtrem-finance/pages__accounts_receivable_advance__internalFinanceIntegrationStatus____title": "Estado interno", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____columns__title__account__composedDescription": "C<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____columns__title__advanceAmount": "Importe", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____columns__title__description": "Descripción", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____title": "Líneas", "@sage/xtrem-finance/pages__accounts_receivable_advance__number____title": "Número", "@sage/xtrem-finance/pages__accounts_receivable_advance__paymentDate____title": "Fecha de pago", "@sage/xtrem-finance/pages__accounts_receivable_advance__payToCustomerName____title": "Cliente pagador", "@sage/xtrem-finance/pages__accounts_receivable_advance__postingDate____title": "Fecha de recepción", "@sage/xtrem-finance/pages__accounts_receivable_advance__postingStatus____title": "Estado de contabilización", "@sage/xtrem-finance/pages__accounts_receivable_advance__reference____title": "Referencia", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__billToCustomerId__title": "Id. de cliente facturado", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line_4__title": "Tipo", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line_5__title": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line10__title": "Origen", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line11__title": "Total sin impuestos", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line12__title": "Impuestos regularizados", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line13__title": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line14__title": "Condiciones de pago", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line2__title": "Descripción", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line2Right__title": "Fecha de contabilización", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line4__title": "Tipo", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line5__title": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line6__title": "Planta financiera", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line7__title": "Cliente facturado", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line8__title": "Total con impuestos", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line9__title": "Referencia", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__title__title": "Número", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__titleRight__title": "Estado de contabilización", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__2": "Borradores", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__3": "Contabilizadas", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__4": "En curso", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__5": "Error", "@sage/xtrem-finance/pages__accounts_receivable_invoice____objectTypePlural": "Facturas contables de cliente", "@sage/xtrem-finance/pages__accounts_receivable_invoice____objectTypeSingular": "Factura contable de cliente", "@sage/xtrem-finance/pages__accounts_receivable_invoice____subtitle": "Factura contable de cliente", "@sage/xtrem-finance/pages__accounts_receivable_invoice____title": "Factura contable de cliente", "@sage/xtrem-finance/pages__accounts_receivable_invoice__billToCustomer____columns__title__businessEntity__id": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__billToCustomer____columns__title__businessEntity__name": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__billToCustomer____title": "Cliente facturado", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____columns__title__id": "Código ISO 4217", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____columns__title__symbol": "Símbolo", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____title": "Divisa", "@sage/xtrem-finance/pages__accounts_receivable_invoice__dueDate____title": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationApp____title": "Aplicación", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationAppRecordId____title": "Referencia de integración contable", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationAppRecordIdLink____title": "Referencia de integración contable", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationAppUrl____title": "URL de integración contable", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationStatus____title": "Estado de integración contable", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__columns__title___id": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__columns__title__id__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__columns__title__symbol": "Símbolo", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__title": "Divisa", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__title__5": "Motor de impuestos", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__title__6": "Divisa", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____title": "Planta financiera", "@sage/xtrem-finance/pages__accounts_receivable_invoice__generalSection____title": "General", "@sage/xtrem-finance/pages__accounts_receivable_invoice__goToSysNotificationPage____title": "Reintentar", "@sage/xtrem-finance/pages__accounts_receivable_invoice__internalFinanceIntegrationStatus____title": "Estado interno", "@sage/xtrem-finance/pages__accounts_receivable_invoice__invoiceDate____title": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__isPrinted____title": "Impresa", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__account__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__account__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__account__composedDescription__title__3": "Descripción compuesta", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__currency__id__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__currency__id__title__2": "Código ISO 4217", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__currency__id__title__3": "Símbolo", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__currency__id__title__4": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension01__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension01__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension02__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension02__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension03__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension03__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension04__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension04__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension05__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension05__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension06__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension06__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension07__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension07__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension08__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension08__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension09__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension09__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension10__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension10__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension11__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension11__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension12__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension12__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension13__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension13__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension14__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension14__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension15__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension15__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension16__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension16__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension17__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension17__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension18__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension18__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension19__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension19__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension20__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension20__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__employee__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__employee__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__originLine___id__title": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__project__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__project__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__task__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__task__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__account__composedDescription": "C<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__amount": "Importe", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__amountExcludingTax": "Importe sin impuestos", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__amountIncludingTax": "Importe con impuestos", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__description": "Descripción", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__financialSite__name": "Planta financiera", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__lineAmountExcludingTax": "Importe sin impuestos", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__lineAmountIncludingTax": "Importe con impuestos", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__lineType": "Tipo de línea", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__originLine___id": "Línea de factura", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__providerSite__name": "Planta de origen", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__taxAmount": "Importe de impuesto", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__taxDate": "Fecha de impuesto", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__dropdownActions__title": "Detalles de impuesto", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____title": "Líneas", "@sage/xtrem-finance/pages__accounts_receivable_invoice__number____title": "Número", "@sage/xtrem-finance/pages__accounts_receivable_invoice__origin____title": "Origen", "@sage/xtrem-finance/pages__accounts_receivable_invoice__paymentStatus____title": "Estado de pago", "@sage/xtrem-finance/pages__accounts_receivable_invoice__paymentTerm____title": "Condiciones de pago", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDate____title": "Fecha de contabilización", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__financeIntegrationAppRecordId": "Referencia de integración contable", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__postingStatus": "Estado", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__targetDocumentNumber": "Número de documento", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__targetDocumentType": "Tipo de documento", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____title": "Contabilización", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingMessageBlock____title": "Detalles de error", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingSection____title": "Contabilización", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingStatus____title": "Estado de contabilización", "@sage/xtrem-finance/pages__accounts_receivable_invoice__rateDescription____title": "Tipo de cambio", "@sage/xtrem-finance/pages__accounts_receivable_invoice__reference____title": "Referencia", "@sage/xtrem-finance/pages__accounts_receivable_invoice__status____title": "Estado", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__account__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__account__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__account__composedDescription__title__3": "Descripción compuesta", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension01__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension01__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension02__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension02__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension03__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension03__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension04__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension04__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension05__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension05__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension06__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension06__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension07__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension07__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension08__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension08__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension09__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension09__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension10__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension10__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension11__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension11__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension12__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension12__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension13__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension13__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension14__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension14__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension15__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension15__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension16__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension16__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension17__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension17__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension18__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension18__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension19__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension19__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension20__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension20__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__employee__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__employee__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__originLine___id__title": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__project__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__project__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__task__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__task__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__account__composedDescription": "C<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__amount": "Importe", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__financialSite__name": "Planta financiera", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__providerSite__name": "Planta de origen", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__taxDetail": "Detalles de impuesto", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__taxLineTaxAmount": "Importe de impuesto", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____title": "Líneas de detalles de impuesto", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__postfix__taxRate": "%", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__tax": "Impuesto", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxableAmount": "Base imponible", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxAmount": "Importe", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxCategory": "Categoría", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxRate": "Tipo impositivo", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____title": "Totales", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalAmountExcludingTax____title": "Total sin impuestos", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalAmountIncludingTax____title": "Total con impuestos", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalsSection____title": "Totales", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalsSectionTaxTotalsBlock____title": "Totales", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalTaxAmount____title": "Total impuestos", "@sage/xtrem-finance/pages__accounts_receivable_invoice__type____title": "Tipo", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__businessEntityId__title": "Id. de cliente", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__closeReason__title": "Motivo de cierre", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__closeText__title": "Nota de c<PERSON>re", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__companyAmountDueSigned__title": "Importe pendiente de sociedad", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__companyAmountPaid__title": "Importe pagado de sociedad", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__companyCurrency__title": "Divisa de sociedad", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__currency__title": "Divisa", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__displayDiscountPaymentDate__title": "Fecha límite de pago de descuento", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__documentType__title": "Tipo de documento", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__financialSite__title": "Planta financiera", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__forcedAmountPaid__title": "Importe pagado forzado", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__line2__title": "Nombre de cliente", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__line2Right__title": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__remainingCompanyAmount__title": "Importe restante de sociedad", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__remainingTransactionAmount__title": "Importe restante de transacción", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__title__title": "Número de documento", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__titleRight__title": "Estado", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__transactionAmountDueSigned__title": "Importe pendiente de transacción", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__transactionAmountPaid__title": "Importe pagado de transacción", "@sage/xtrem-finance/pages__accounts_receivable_open_item____objectTypePlural": "Vencimientos de cliente", "@sage/xtrem-finance/pages__accounts_receivable_open_item____objectTypeSingular": "Vencimiento de cliente", "@sage/xtrem-finance/pages__accounts_receivable_open_item____title": "Vencimiento de cliente", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeReason____columns__title__id": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeReason____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeReason____title": "Motivo de cierre", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeText____title": "Nota de c<PERSON>re", "@sage/xtrem-finance/pages__accounts_receivable_open_item__customer____helperText": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_open_item__discount_amount": "Importe de descuento", "@sage/xtrem-finance/pages__accounts_receivable_open_item__discount_percentage": "Porcentaje de descuento", "@sage/xtrem-finance/pages__accounts_receivable_open_item__financialSite____helperText": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_open_item__forcedAmountPaidSigned____title": "Importe pagado forzado", "@sage/xtrem-finance/pages__accounts_receivable_open_item__generalSection____title": "General", "@sage/xtrem-finance/pages__accounts_receivable_open_item__penalty_amount": "Importe de recargo", "@sage/xtrem-finance/pages__accounts_receivable_open_item__penalty_percentage": "Porcentaje de recargo", "@sage/xtrem-finance/pages__accounts_receivable_open_item__transactionAmountPaidSigned____title": "Total pagado", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__bulkActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__businessEntityId__title": "Id. de cliente", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__closeReason__title": "Motivo de cierre", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__closeText__title": "Nota de c<PERSON>re", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__companyAmountDueSigned__title": "Importe pendiente de sociedad", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__companyAmountPaid__title": "Importe pagado de sociedad", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__companyCurrency__title": "Divisa de sociedad", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__currency__title": "Divisa", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__displayDiscountPaymentDate__title": "Fecha límite de pago de descuento", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__documentType__title": "Tipo de documento", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__financialSite__title": "Planta financiera", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__forcedAmountPaid__title": "Importe pagado forzado", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__line2__title": "Nombre de cliente", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__line2Right__title": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__remainingCompanyAmount__title": "Importe restante de sociedad", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__remainingTransactionAmount__title": "Importe restante de transacción", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__status__title": "Estado", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__title__title": "Número de documento", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__titleRight__title": "Estado", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__transactionAmountDueSigned__title": "Importe pendiente de transacción", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__transactionAmountPaid__title": "Importe pagado de transacción", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title": "Sin pagar por completo", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__2": "Todos", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__3": "<PERSON> pagar", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__4": "Parcia<PERSON><PERSON> pagados", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__5": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____objectTypePlural": "Inicialización de pagos de cliente", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____objectTypeSingular": "Inicialización de pago de cliente", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____title": "Inicialización de pago de cliente", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeReason____columns__title__id": "Id.", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeReason____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeReason____title": "Motivo de cierre", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeText____title": "Nota de c<PERSON>re", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__forced_amount_paid_wrongly_negative": "El importe pagado forzado debe ser positivo.", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__forced_amount_paid_wrongly_positive": "El importe pagado forzado debe ser negativo.", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__forcedAmountPaidSigned____title": "Importe pagado forzado", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__generalSection____title": "General", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__save____title": "Guardar", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__transactionAmountPaidSigned____title": "Total pagado", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__wrong_forced_amount_paid": "El importe pagado forzado debe estar entre 0 y {{maxForcedAmount}}.", "@sage/xtrem-finance/pages__datev_export____navigationPanel__dropdownActions__title": "Extraer", "@sage/xtrem-finance/pages__datev_export____navigationPanel__dropdownActions__title__2": "Exportar", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__datevConsultantNumber__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__datevcustomerNumber__title": "Número de cliente", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__doAccounts__title": "Cuentas", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__doCustomersSuppliers__title": "Clientes y proveedores", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__doJournalEntries__title": "Asientos", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__fiscalYearStart__title": "In<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__id__title": "Id.", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__isLocked__title": "Bloqueado", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__line2__title": "Fecha de inicio", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__line2Right__title": "<PERSON><PERSON> de fin", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__timeStamp__title": "Extracción", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__title__title": "Sociedad", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__titleRight__title": "Estado", "@sage/xtrem-finance/pages__datev_export____objectTypePlural": "Exportaciones de DATEV", "@sage/xtrem-finance/pages__datev_export____objectTypeSingular": "Exportación de DATEV", "@sage/xtrem-finance/pages__datev_export____title": "Exportación de DATEV", "@sage/xtrem-finance/pages__datev_export__accountsSection____title": "Cuentas extraídas", "@sage/xtrem-finance/pages__datev_export__accountsWithoutDatevId____columns__title__id": "Id.", "@sage/xtrem-finance/pages__datev_export__accountsWithoutDatevId____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__datev_export__accountsWithoutDatevId____title": "Cuentas sin id. de DATEV", "@sage/xtrem-finance/pages__datev_export__acountsWithoutDatevIdSection____title": "Cuentas sin id. de DATEV", "@sage/xtrem-finance/pages__datev_export__businessRelationsSection____title": "Clientes y proveedores extraídos", "@sage/xtrem-finance/pages__datev_export__createDatevExport____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevId____columns__title__id": "Id.", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevId____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevId____title": "Clientes sin id. de DATEV", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevIdSection____title": "Clientes sin id. de DATEV", "@sage/xtrem-finance/pages__datev_export__datevConsultantNumber____title": "Número de asesor de DATEV", "@sage/xtrem-finance/pages__datev_export__datevCustomerNumber____title": "Número de cliente de DATEV", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____columns__title__account__id": "Id.", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____columns__title__datevId": "Id. de DATEV", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____title": "Cuentas extraídas", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__businessRelation__id": "Id.", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__city": "Ciudad", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__country__name": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__datevId": "Id. de DATEV", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__postcode": "Código postal", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__street": "Calle", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__taxIdNumber": "NIF-IVA", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____title": "Clientes y proveedores extraídos", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__businessEntityTaxIdNumber": "NIF-IVA de entidad empresarial", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__companyFxRate": "Tipo de cambio", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__companyValue": "Importe en divisa de sociedad", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__datevAccountId": "Id. de cuenta en DATEV", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__datevContraAccountId": "Id. de cuenta de contrapartida en DATEV", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__datevSign": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__description": "Descripción", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__dimension1": "Sección 1", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__dimension2": "Sección 2", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__locked": "Bloqueado", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__number": "Número", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__postingKey": "<PERSON><PERSON><PERSON> contable", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__siteTaxIdNumber": "N.º id. fiscal de planta", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__supplierDocumentNumber": "Número de documento de proveedor", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__transactionValue": "Importe de transacción", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____title": "Apuntes extraídos", "@sage/xtrem-finance/pages__datev_export__dimensionTypeText1____title": "Eje 1", "@sage/xtrem-finance/pages__datev_export__dimensionTypeText2____title": "Eje 2", "@sage/xtrem-finance/pages__datev_export__doAccounts____title": "Exportar cuentas", "@sage/xtrem-finance/pages__datev_export__doCustomersSuppliers____title": "Exportar clientes y proveedores", "@sage/xtrem-finance/pages__datev_export__doJournalEntries____title": "Exportar asientos", "@sage/xtrem-finance/pages__datev_export__endDate____title": "<PERSON><PERSON> de fin", "@sage/xtrem-finance/pages__datev_export__export____title": "Exportar", "@sage/xtrem-finance/pages__datev_export__extract____title": "Extraer", "@sage/xtrem-finance/pages__datev_export__fiscalYearStart____title": "In<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export__headerSection____title": "Sección de cabecera", "@sage/xtrem-finance/pages__datev_export__id____title": "Id.", "@sage/xtrem-finance/pages__datev_export__isLocked____title": "Bloqueado", "@sage/xtrem-finance/pages__datev_export__journalEntryLineSection____title": "Apuntes extraídos", "@sage/xtrem-finance/pages__datev_export__notification_export_sent": "La solicitud de exportación de DATEV se ha enviado.", "@sage/xtrem-finance/pages__datev_export__notification_success": "La solicitud de extracción de DATEV se ha enviado.", "@sage/xtrem-finance/pages__datev_export__skrCoaString____title": "SKR", "@sage/xtrem-finance/pages__datev_export__startDate____title": "Fecha de inicio", "@sage/xtrem-finance/pages__datev_export__status____title": "Estado", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevId____columns__title__id": "Id.", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevId____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevId____title": "Proveedores sin id. de DATEV", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevIdSection____title": "Proveedores sin id. de DATEV", "@sage/xtrem-finance/pages__datev_export__timeStamp____title": "Última fecha de extracción", "@sage/xtrem-finance/pages__datev_export_parameters__datev_configuration": "Configuración de DATEV", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_date_range": "La fecha de fin debe ser posterior a la fecha de inicio.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_dimension_types": "El eje 2 debe ser diferente del eje 1.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_end_date": "La fecha de fin debe situarse en el ejercicio.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_end_for_begin_of_fiscal_year": "La fecha de fin debe ser posterior al inicio del ejercicio.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_start_for_begin_of_fiscal_year": "La fecha de inicio debe ser posterior al inicio del ejercicio.", "@sage/xtrem-finance/pages__datev_export_settings____title": "Configuración de exportación de DATEV", "@sage/xtrem-finance/pages__datev_export_settings__company____columns__title__id": "Id.", "@sage/xtrem-finance/pages__datev_export_settings__company____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__datev_export_settings__criteriaBlock____title": "Criterios", "@sage/xtrem-finance/pages__datev_export_settings__dateRange____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export_settings__dimensionTypeOrAttribute1____title": "Eje 1", "@sage/xtrem-finance/pages__datev_export_settings__dimensionTypeOrAttribute2____title": "Eje 2", "@sage/xtrem-finance/pages__datev_export_settings__doAccounts____title": "Exportar cuentas", "@sage/xtrem-finance/pages__datev_export_settings__doCustomersSuppliers____title": "Exportar clientes y proveedores", "@sage/xtrem-finance/pages__datev_export_settings__doJournalEntries____title": "Exportar asientos", "@sage/xtrem-finance/pages__datev_export_settings__endDate____title": "<PERSON><PERSON> de fin", "@sage/xtrem-finance/pages__datev_export_settings__fiscalYearStart____title": "In<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export_settings__id____title": "Id.", "@sage/xtrem-finance/pages__datev_export_settings__isLocked____title": "Bloqueado", "@sage/xtrem-finance/pages__datev_export_settings__mainSection____title": "General", "@sage/xtrem-finance/pages__datev_export_settings__startDate____title": "Fecha de inicio", "@sage/xtrem-finance/pages__generate_journal_entries____title": "Generación de asientos", "@sage/xtrem-finance/pages__generate_journal_entries__create_button_text": "Generar", "@sage/xtrem-finance/pages__generate_journal_entries__dateFrom____title": "Fecha de inicio", "@sage/xtrem-finance/pages__generate_journal_entries__dateTo____title": "<PERSON><PERSON> de fin", "@sage/xtrem-finance/pages__generate_journal_entries__documentType____title": "Tipo de documento", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____lookupDialogTitle": "Seleccionar planta financiera", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____placeholder": "Seleccionar...", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____title": "Planta financiera", "@sage/xtrem-finance/pages__generate_journal_entries__generalBlock____title": "Criterios", "@sage/xtrem-finance/pages__generate_journal_entries__generalSection____title": "General", "@sage/xtrem-finance/pages__generate_journal_entries__recurring": "Periódica", "@sage/xtrem-finance/pages__generate_journal_entries__run_once": "Ejecutar una vez", "@sage/xtrem-finance/pages__generate_journal_entries__schedule_button_text": "Programar", "@sage/xtrem-finance/pages__generate_pre_journal_entries____title": "Generación de asientos", "@sage/xtrem-finance/pages__generate_pre_journal_entries__dateFrom____title": "Fecha de inicio", "@sage/xtrem-finance/pages__generate_pre_journal_entries__dateTo____title": "<PERSON><PERSON> de fin", "@sage/xtrem-finance/pages__generate_pre_journal_entries__documentType____title": "Tipo de documento", "@sage/xtrem-finance/pages__generate_pre_journal_entries__financialSite____columns__title__id": "Id.", "@sage/xtrem-finance/pages__generate_pre_journal_entries__financialSite____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__generate_pre_journal_entries__financialSite____placeholder": "Seleccionar...", "@sage/xtrem-finance/pages__generate_pre_journal_entries__financialSite____title": "Planta", "@sage/xtrem-finance/pages__generate_pre_journal_entries__generalBlock____title": "Criterios", "@sage/xtrem-finance/pages__generate_pre_journal_entries__generalSection____title": "General", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line_4__title": "Diario", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line_5__title": "Tipo de documento", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line2Right__title": "Fecha de contabilización", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line3__title": "Planta financiera", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line4__title": "Diario", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line5__title": "Tipo de documento", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line6__title": "Referencia", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line7__title": "Origen", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__title__title": "Número", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__titleRight__title": "Estado de contabilización", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__optionsMenu__title__2": "Pendientes", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__optionsMenu__title__3": "Contabilizados", "@sage/xtrem-finance/pages__journal_entry____objectTypePlural": "Asientos", "@sage/xtrem-finance/pages__journal_entry____objectTypeSingular": "Asiento", "@sage/xtrem-finance/pages__journal_entry____title": "Asiento", "@sage/xtrem-finance/pages__journal_entry___id____title": "Id.", "@sage/xtrem-finance/pages__journal_entry__accountingIntegrationBlock____title": "Integración contable", "@sage/xtrem-finance/pages__journal_entry__description____title": "Descripción", "@sage/xtrem-finance/pages__journal_entry__documentType____title": "Tipo de documento", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationApp____title": "Aplicación", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationAppRecordId____title": "Referencia de integración contable", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationAppRecordIdLink____title": "Referencia de integración contable", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationAppUrl____title": "URL de integración contable", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationStatus____title": "Estado de integración contable", "@sage/xtrem-finance/pages__journal_entry__financialSite____columns__lookupDialogTitle__legalCompany__name": "Seleccionar sociedad", "@sage/xtrem-finance/pages__journal_entry__financialSite____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-finance/pages__journal_entry__financialSite____title": "Planta financiera", "@sage/xtrem-finance/pages__journal_entry__generalSection____title": "General", "@sage/xtrem-finance/pages__journal_entry__internalFinanceIntegrationStatus____title": "Estado interno", "@sage/xtrem-finance/pages__journal_entry__journal____title": "Diario", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__account__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__account__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension01__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension01__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension02__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension02__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension03__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension03__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension04__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension04__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension05__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension05__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension06__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension06__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension07__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension07__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension08__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension08__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension09__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension09__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension10__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension10__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension11__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension11__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension12__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension12__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension13__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension13__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension14__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension14__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension15__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension15__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension16__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension16__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension17__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension17__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension18__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension18__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension19__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension19__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension20__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension20__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__employee__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__employee__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__project__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__project__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__task__composedDescription__title": "Nombre", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__task__composedDescription__title__2": "Id.", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__account__composedDescription": "C<PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__businessEntity__name": "Entidad empresarial", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__commonReference": "Referencia común", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__companyAmount": "Importe de sociedad", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__companyCredit": "Haber de sociedad", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__companyDebit": "Debe de sociedad", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__contraAccount": "Cuenta de contrapartida", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__description": "Descripción", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__financialSite__name": "Planta financiera", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__fxRateDate": "Fecha de tipo de cambio", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__rateDescription": "Tipo de cambio", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__tax__name": "Impuesto", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__taxDate": "Fecha de impuesto", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionAmount": "Importe de transacción", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionCredit": "Haber de transacción", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionCurrency__id": "Divisa de transacción", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionDebit": "Debe de transacción", "@sage/xtrem-finance/pages__journal_entry__lines____levels__dropdownActions__title": "Detalles de impuestos", "@sage/xtrem-finance/pages__journal_entry__lines____title": "Líneas", "@sage/xtrem-finance/pages__journal_entry__number____title": "Número", "@sage/xtrem-finance/pages__journal_entry__origin____title": "Origen", "@sage/xtrem-finance/pages__journal_entry__postingDate____title": "Fecha de contabilización", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title___id": "Id.", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__documentNumber": "Número de documento", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__documentSysId": "Id. de documento", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__documentType": "Tipo de documento", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__financeIntegrationAppRecordId": "Referencia de integración contable", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__financeIntegrationAppRecordId__2": "Referencia de integración contable", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__message": "Men<PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__postingStatus": "Estado", "@sage/xtrem-finance/pages__journal_entry__postingDetails____title": "Contabilización", "@sage/xtrem-finance/pages__journal_entry__postingMessageBlock____title": "Detalles de error", "@sage/xtrem-finance/pages__journal_entry__postingSection____title": "Contabilización", "@sage/xtrem-finance/pages__journal_entry__postingSectionBlock____title": "Contabilización", "@sage/xtrem-finance/pages__journal_entry__postingStatus____title": "Estado de contabilización", "@sage/xtrem-finance/pages__journal_entry__reference____title": "Referencia", "@sage/xtrem-finance/pages__journal_entry_inquiry____title": "Consulta de asiento", "@sage/xtrem-finance/pages__journal_entry_inquiry__companyFilter____lookupDialogTitle": "Seleccionar sociedades", "@sage/xtrem-finance/pages__journal_entry_inquiry__companyFilter____title": "Sociedades", "@sage/xtrem-finance/pages__journal_entry_inquiry__dateFrom____title": "Fecha de inicio", "@sage/xtrem-finance/pages__journal_entry_inquiry__dateTo____title": "<PERSON><PERSON> de fin", "@sage/xtrem-finance/pages__journal_entry_inquiry__filtersBlock____title": "Criterios", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__account__id": "Código de cuenta", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__account__name": "Nombre de cuenta", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__blank": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__blank__2": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__businessEntity__id": "Código de entidad empresarial", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__businessEntity__name": "Nombre de entidad empresarial", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__commonReference": "Referencia común", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__companyAmount": "Importe", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__contraAccount": "Cuenta de contrapartida", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__financialSite__id": "Código de planta", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__financialSite__legalCompany__name": "Razón social", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__inquiryDescription": "Descripción", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__inquiryTransactionCurrency__id": "Divisa de transacción", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__journal__id": "Código de diario", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__journal__name": "Número de diario", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__number": "Número", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__postingDate__2": "Fecha de documento de referencia", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__reference": "Referencia", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__numericSign": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__signedTransactionAmount": "Importe de transacción", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__tax__name": "Impuesto", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__taxDate": "Fecha de impuesto", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__validationDate": "Fecha de validación", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____title": "Líneas", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalFilter____lookupDialogTitle": "Seleccionar diarios", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalFilter____title": "Diarios", "@sage/xtrem-finance/pages__journal_entry_inquiry__linesSection____title": "Líneas", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__amount__title": "Importe", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__amountBankCurrency__title": "Importe en divisa bancaria", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__amountCompanyCurrency__title": "Importe en divisa de sociedad", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__bankAccount__title": "Cuenta bancaria", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__bankCurrency__title": "Divisa de cuenta bancaria", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__businessRelationId__title": "Id. de entidad empresarial", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__companyCurrency__title": "Divisa de sociedad", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__currency__title": "Divisa", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__financialSite__title": "Planta financiera", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__isVoided__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__line2__title": "Nombre de entidad empresarial", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__line2Right__title": "Fecha de pago", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__paymentMethod__title": "Forma de pago", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__reference__title": "Referencia", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__title__title": "Número", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__titleRight__title": "Estado", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__type__title": "Tipo", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__voidDate__title": "Anulación", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__voidText__title": "Nota de anulación", "@sage/xtrem-finance/pages__payment____navigationPanel__optionsMenu__title": "Contabilizados", "@sage/xtrem-finance/pages__payment____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__payment____navigationPanel__optionsMenu__title__3": "Todos", "@sage/xtrem-finance/pages__payment____objectTypePlural": "Pagos", "@sage/xtrem-finance/pages__payment____objectTypeSingular": "Pago", "@sage/xtrem-finance/pages__payment____title": "Pago", "@sage/xtrem-finance/pages__payment__cancel_void_button": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__payment__cancelPayment____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__payment__confirm_void_button": "Confirmar", "@sage/xtrem-finance/pages__payment__createPayment____title": "Confirmar", "@sage/xtrem-finance/pages__payment__linesSection____title": "Líneas", "@sage/xtrem-finance/pages__payment__mainSection____title": "General", "@sage/xtrem-finance/pages__payment__paymentInformationBlock____title": "Información de pago", "@sage/xtrem-finance/pages__payment__voidPayment____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__payment__voidPaymentDate____helperText": "Fecha de anulación del pago", "@sage/xtrem-finance/pages__payment__voidPaymentDate____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__payment__voidPaymentText____title": "<PERSON>a", "@sage/xtrem-finance/pages__payment__voidSection____title": "Anular pago", "@sage/xtrem-finance/pages__pre_journal____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-finance/pages__pre_journal____navigationPanel__optionsMenu__title__2": "Pendientes", "@sage/xtrem-finance/pages__pre_journal____navigationPanel__optionsMenu__title__3": "Contabilizados", "@sage/xtrem-finance/pages__pre_journal____subtitle": "Asiento", "@sage/xtrem-finance/pages__pre_journal____title": "Asiento", "@sage/xtrem-finance/pages__pre_journal__description____title": "Descripción", "@sage/xtrem-finance/pages__pre_journal__documentType____title": "Tipo de documento", "@sage/xtrem-finance/pages__pre_journal__financialSite____columns__columns__legalCompany__name__title": "Legislación", "@sage/xtrem-finance/pages__pre_journal__financialSite____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-finance/pages__pre_journal__financialSite____title": "Planta financiera", "@sage/xtrem-finance/pages__pre_journal__generalSection____title": "General", "@sage/xtrem-finance/pages__pre_journal__journal____title": "Diario", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__account__composedDescription__title": "Id.", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__account__composedDescription__title__2": "Nombre", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__account__composedDescription__title__3": "Descripción compuesta", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title": "Nombre", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__2": "Código ISO 4217", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__3": "Símbolo", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__4": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__5": "Nombre", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__6": "ISO 4217", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__7": "Símbolo", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__8": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title": "Nombre", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__2": "Código ISO 4217", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__3": "Símbolo", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__4": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__5": "Nombre", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__6": "ISO 4217", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__7": "Símbolo", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__8": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__account__composedDescription": "C<PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__businessEntity__name": "Entidad empresarial", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__commonReference": "Referencia común", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyAmount": "Importe de sociedad", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyCredit": "Haber de sociedad", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyCurrency__id": "Divisa de sociedad", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyCurrency__id__2": "Divisa de sociedad", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyDebit": "Debe de sociedad", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__description": "Descripción", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__financialSite__name": "Planta financiera", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__fxRateDate": "Fecha de tipo de cambio", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__rateDescription": "Tipo de cambio", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__tax__name": "Impuesto", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__taxDate": "Fecha de impuesto", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionAmount": "Importe de transacción", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionCredit": "Haber de transacción", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionCurrency__id": "Divisa de transacción", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionCurrency__id__2": "Divisa de transacción", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionDebit": "Debe de transacción", "@sage/xtrem-finance/pages__pre_journal__linesBlock____title": "Líneas", "@sage/xtrem-finance/pages__pre_journal__number____title": "Número", "@sage/xtrem-finance/pages__pre_journal__origin____title": "Origen", "@sage/xtrem-finance/pages__pre_journal__postingDate____title": "Fecha de contabilización", "@sage/xtrem-finance/pages__pre_journal__reference____title": "Referencia", "@sage/xtrem-finance/pages__pre_journal__status____title": "Estado", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__amount__title": "Importe", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__amountBankCurrency__title": "Importe en divisa bancaria", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__amountCompanyCurrency__title": "Importe en divisa de sociedad", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__bankAccount__title": "Cuenta bancaria", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__bankCurrency__title": "Divisa de cuenta bancaria", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__businessRelationId__title": "Id. de entidad empresarial", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__companyCurrency__title": "Divisa de sociedad", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__currency__title": "Divisa", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__financialSite__title": "Planta financiera", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__isVoided__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__line2__title": "Nombre de entidad empresarial", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__line2Right__title": "Fecha de pago", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__paymentMethod__title": "Forma de pago", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__reference__title": "Referencia", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__title__title": "Número", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__titleRight__title": "Estado", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__type__title": "Tipo", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__voidDate__title": "Anulación", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__voidText__title": "Nota de anulación", "@sage/xtrem-finance/pages__receipt____navigationPanel__optionsMenu__title": "Contabilizados", "@sage/xtrem-finance/pages__receipt____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt____navigationPanel__optionsMenu__title__3": "Todos", "@sage/xtrem-finance/pages__receipt____objectTypePlural": "Co<PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt____objectTypeSingular": "Cobro", "@sage/xtrem-finance/pages__receipt____title": "Cobro", "@sage/xtrem-finance/pages__receipt__amount____title": "Importe de pago", "@sage/xtrem-finance/pages__receipt__amountBankCurrency____title": "Importe en divisa bancaria", "@sage/xtrem-finance/pages__receipt__bankAccount____title": "Cuenta bancaria", "@sage/xtrem-finance/pages__receipt__cancel_void": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__cancelReceipt____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__confirm_void": "Confirmar", "@sage/xtrem-finance/pages__receipt__createReceipt____title": "Confirmar", "@sage/xtrem-finance/pages__receipt__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-finance/pages__receipt__currency____title": "Divisa", "@sage/xtrem-finance/pages__receipt__customer____title": "Cliente", "@sage/xtrem-finance/pages__receipt__financialSite____title": "Planta financiera", "@sage/xtrem-finance/pages__receipt__isVoided____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__lines____columns__title__apOpenItem__accountsPayableInvoice__purchaseDocumentNumber": "Número de documento", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__accountsReceivableInvoice__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__accountsReceivableInvoice__reference": "Referencia", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__accountsReceivableInvoice__salesDocumentNumber": "Número de documento", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/pages__receipt__lines____columns__title__currency__id": "Divisa", "@sage/xtrem-finance/pages__receipt__lines____columns__title__origin": "Origen", "@sage/xtrem-finance/pages__receipt__lines____columns__title__signedAmount": "Importe", "@sage/xtrem-finance/pages__receipt__lines____columns__title__signedAmountBankCurrency": "Importe en divisa bancaria", "@sage/xtrem-finance/pages__receipt__lines____title": "Líneas", "@sage/xtrem-finance/pages__receipt__linesSection____title": "Líneas", "@sage/xtrem-finance/pages__receipt__mainSection____title": "General", "@sage/xtrem-finance/pages__receipt__number____title": "Número", "@sage/xtrem-finance/pages__receipt__paymentDate____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__paymentInformationBlock____title": "Información de pago", "@sage/xtrem-finance/pages__receipt__paymentMethod____title": "Forma de pago", "@sage/xtrem-finance/pages__receipt__reference____title": "Información de transacción", "@sage/xtrem-finance/pages__receipt__supplier____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__voidPaymentDate____helperText": "Fecha de anulación del pago", "@sage/xtrem-finance/pages__receipt__voidPaymentDate____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__voidPaymentText____title": "<PERSON>a", "@sage/xtrem-finance/pages__receipt__voidReceipt____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__voidSection____title": "Anular cobro", "@sage/xtrem-finance/pages__record_payment____title": "Registro de pago", "@sage/xtrem-finance/pages__record_payment__amount____title": "Importe de pago", "@sage/xtrem-finance/pages__record_payment__amount_in_bank_currency_must_be_positive": "El importe en la divisa bancaria debe ser superior a 0.", "@sage/xtrem-finance/pages__record_payment__amountAvailableToApply____title": "Importe disponible", "@sage/xtrem-finance/pages__record_payment__amountBankCurrency____title": "Importe en divisa bancaria", "@sage/xtrem-finance/pages__record_payment__bankAccount____title": "Cuenta bancaria", "@sage/xtrem-finance/pages__record_payment__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-finance/pages__record_payment__currency____title": "Divisa", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__country__name": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__id": "Id.", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__taxIdNumber": "NIF-IVA", "@sage/xtrem-finance/pages__record_payment__customer____title": "Cliente", "@sage/xtrem-finance/pages__record_payment__date____title": "Fecha de pago", "@sage/xtrem-finance/pages__record_payment__financialSite____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-finance/pages__record_payment__financialSite____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-finance/pages__record_payment__financialSite____placeholder": "Planta financiera", "@sage/xtrem-finance/pages__record_payment__financialSite____title": "Planta financiera", "@sage/xtrem-finance/pages__record_payment__generateButton____title": "Generar", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__number": "Número de factura", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__origin": "Origen", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__reference": "Referencia", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__amountDue": "Importe pendiente", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__creditAmount": "Importe de abono", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__currency__id": "Divisa", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__paymentAmount": "Importe de pago", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__remainingCompanyAmount": "Importe pendiente de sociedad", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__totalAmount": "Importe total", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__totalCompanyAmount": "Importe total de sociedad", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__totalCompanyAmountPaid": "Importe pagado de sociedad", "@sage/xtrem-finance/pages__record_payment__lines____title": "Resul<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__linesBlock____title": "Resul<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__linesSection____title": "Líneas", "@sage/xtrem-finance/pages__record_payment__mainSection____title": "General", "@sage/xtrem-finance/pages__record_payment__payment_amount_can_not_be_negative": "El importe del pago debe ser superior o igual a 0.", "@sage/xtrem-finance/pages__record_payment__payment_created": "El pago {{paymentNumber}} se ha creado.", "@sage/xtrem-finance/pages__record_payment__paymentInformationBlock____title": "Información de pago", "@sage/xtrem-finance/pages__record_payment__paymentMethod____title": "Forma de pago", "@sage/xtrem-finance/pages__record_payment__reference____title": "Información de transacción", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__country__name": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__id": "Id.", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__taxIdNumber": "NIF-IVA", "@sage/xtrem-finance/pages__record_payment__supplier____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__totalPaymentApplied____title": "Total liquidado", "@sage/xtrem-finance/pages__record_payment__transaction_information_can_not_exceed_100_characters": "La información de la transacción no puede superar los 100 caracteres.", "@sage/xtrem-finance/pages__record_payment__type____title": "Tipo", "@sage/xtrem-finance/pages__record_receipt____title": "Registro de cobro", "@sage/xtrem-finance/pages__record_receipt__amount____title": "Importe de pago", "@sage/xtrem-finance/pages__record_receipt__amount_in_bank_currency_must_be_positive": "El importe en la divisa bancaria debe ser superior a 0.", "@sage/xtrem-finance/pages__record_receipt__amountAvailableToApply____title": "Importe disponible", "@sage/xtrem-finance/pages__record_receipt__amountBankCurrency____title": "Importe en divisa bancaria", "@sage/xtrem-finance/pages__record_receipt__bank_amount_must_be_positive": "Introduce un importe en divisa bancaria superior a 0.", "@sage/xtrem-finance/pages__record_receipt__bankAccount____title": "Cuenta bancaria", "@sage/xtrem-finance/pages__record_receipt__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-finance/pages__record_receipt__currency____title": "Divisa", "@sage/xtrem-finance/pages__record_receipt__customer____columns__title__country__name": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt__customer____columns__title__id": "Id.", "@sage/xtrem-finance/pages__record_receipt__customer____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__record_receipt__customer____columns__title__taxIdNumber": "NIF-IVA", "@sage/xtrem-finance/pages__record_receipt__customer____title": "Cliente", "@sage/xtrem-finance/pages__record_receipt__date____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt__financialSite____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-finance/pages__record_receipt__financialSite____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-finance/pages__record_receipt__financialSite____placeholder": "Planta financiera", "@sage/xtrem-finance/pages__record_receipt__financialSite____title": "Planta financiera", "@sage/xtrem-finance/pages__record_receipt__generateButton____title": "Generar", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__accountsReceivableInvoice__number": "Número de factura", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__accountsReceivableInvoice__origin": "Origen", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__accountsReceivableInvoice__postingDate": "Fecha de contabilización", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__accountsReceivableInvoice__reference": "Referencia", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__amountDue": "Importe pendiente", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__creditAmount": "Importe de abono", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__currency__id": "Divisa", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__dueDate": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__paymentAmount": "Importe de pago", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__remainingCompanyAmount": "Importe pendiente de sociedad", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__totalAmount": "Importe total", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__totalCompanyAmount": "Importe total de sociedad", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__totalCompanyAmountPaid": "Importe pagado de sociedad", "@sage/xtrem-finance/pages__record_receipt__lines____title": "Resul<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt__linesBlock____title": "Resul<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt__linesSection____title": "Líneas", "@sage/xtrem-finance/pages__record_receipt__mainSection____title": "General", "@sage/xtrem-finance/pages__record_receipt__payment_amount_can_not_be_negative": "El importe del pago debe ser superior o igual a 0.", "@sage/xtrem-finance/pages__record_receipt__payment_amount_must_be_positive": "Introduce un importe de pago superior a 0.", "@sage/xtrem-finance/pages__record_receipt__payment_amount_must_be_total_payment_applied": "El importe del pago debe coincidir con el total liquidado.", "@sage/xtrem-finance/pages__record_receipt__paymentInformationBlock____title": "Información de pago", "@sage/xtrem-finance/pages__record_receipt__paymentMethod____title": "Forma de pago", "@sage/xtrem-finance/pages__record_receipt__receipt_created": "Cobro {{receiptNumber}} creado", "@sage/xtrem-finance/pages__record_receipt__reference____title": "Información de transacción", "@sage/xtrem-finance/pages__record_receipt__supplier____columns__title__country__name": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt__supplier____columns__title__id": "Id.", "@sage/xtrem-finance/pages__record_receipt__supplier____columns__title__name": "Nombre", "@sage/xtrem-finance/pages__record_receipt__supplier____columns__title__taxIdNumber": "NIF-IVA", "@sage/xtrem-finance/pages__record_receipt__supplier____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt__totalPaymentApplied____title": "Total liquidado", "@sage/xtrem-finance/pages__record_receipt__transaction_information_can_not_exceed_100_characters": "La información de la transacción no puede superar los 100 caracteres.", "@sage/xtrem-finance/pages__record_receipt__type____title": "Tipo", "@sage/xtrem-finance/pages__unbilled_account_payable_inquiry__companyReference____columns__title__description": "Descripción", "@sage/xtrem-finance/pages__unbilled_account_payable_inquiry__fromSupplier____helperText": "Id.", "@sage/xtrem-finance/pages__unbilled_account_payable_inquiry__toSupplier____helperText": "Id.", "@sage/xtrem-finance/permission__accounting_integration__name": "Integración contable", "@sage/xtrem-finance/permission__create_journals_from_accounting_staging__name": "Create journals from accounting staging", "@sage/xtrem-finance/permission__create_journals_from_accounting_staging_job__name": "Create journals from accounting staging job", "@sage/xtrem-finance/permission__initialize_paid_amount__name": "Inicializar importe pagado", "@sage/xtrem-finance/permission__manage__name": "Gestionar", "@sage/xtrem-finance/permission__post__name": "Contabilizar", "@sage/xtrem-finance/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-finance/permission__retry_finance_document__name": "Reintentar documento contable", "@sage/xtrem-finance/permission__void__name": "<PERSON><PERSON>", "@sage/xtrem-finance/search": "Buscar", "@sage/xtrem-finance/source_document_type_not_supported": "El tipo de documento de origen \"{{sourceDocumentType}}\" no es compatible.", "@sage/xtrem-finance/status_not_supported": "Estado no permitido: {{status}}", "@sage/xtrem-finance/success_notification__bulk_open_item_payment_title_error": "<PERSON><PERSON>r de cierre de vencimientos", "@sage/xtrem-finance/success_notification__bulk_open_item_payment_title_success": "Vencimientos cerrados", "@sage/xtrem-finance/success_notification_description__ar_open_items_link": "Vencimientos", "@sage/xtrem-finance/sys__notification_history__search": "Buscar", "@sage/xtrem-finance/target_document_not_found": "No se ha encontrado el documento de destino de tipo {{type}} con id. {{sysId}}.", "@sage/xtrem-finance/target_document_type_not_supported": "El tipo de documento de destino {{targetDocumentType}} no es compatible.", "@sage/xtrem-finance/unexpected_error": "Error imprevisto: {{retryFinanceDocumentResult}}", "@sage/xtrem-finance/widgets__finance_integration_health____callToActions__seeAll__title": "Ver todo", "@sage/xtrem-finance/widgets__finance_integration_health____dataDropdownMenu__orderBy__documentNumber__title": "Ordenar por número de documento", "@sage/xtrem-finance/widgets__finance_integration_health____dataDropdownMenu__orderBy__status__title": "Ordenar por estado", "@sage/xtrem-finance/widgets__finance_integration_health____dataDropdownMenu__orderBy__updateStamp__title": "Ordenar por última actualización de estado", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__line2__title": "Tipo de documento", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__line2Right__title": "Última actualización de estado", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__line3__title": "Men<PERSON><PERSON>", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__targetNumber__title": "Número de documento de destino", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__targetType__title": "Tipo de documento de destino", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__title__title": "Estado", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__titleRight__title": "Número de documento", "@sage/xtrem-finance/widgets__finance_integration_health____title": "Estado de transacción contable"}