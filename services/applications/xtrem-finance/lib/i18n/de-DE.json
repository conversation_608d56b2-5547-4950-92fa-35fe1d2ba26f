{"@sage/xtrem-finance/accounting_engine__no_records_to_process_on_accounting_staging_table": "Es gibt keine Datensätze zur Verarbeitung für {{documentType}} {{documentNumber}} nach {{targetDocumentType}}", "@sage/xtrem-finance/accounting_engine__there_are_records_already_processed_on_accounting_staging_table": "Es gibt bereits verarbeitete Datensätze in {{documentType}} {{documentNumber}} nach {{targetDocumentType}}", "@sage/xtrem-finance/accounting_engine__there_are_records_to_be_processed_on_accounting_staging_table_to_documents_that_are_not_target_document_type": "Es gibt Datensätze zur Verarbeitung in {{documentType}} {{documentNumber}}, die nicht vom Typ {{targetDocumentType}} sind.", "@sage/xtrem-finance/accounting_engine__wrong_number_of_records": "<PERSON><PERSON><PERSON> in Staging Buchhaltung: {{recordCount}}; erwarteter Wert: {{batchSize}}", "@sage/xtrem-finance/activity__accounting_interface_listener__name": "Listener Buchhaltungsschnittstelle", "@sage/xtrem-finance/activity__accounting_staging__name": "Staging Buchhaltung", "@sage/xtrem-finance/activity__accounts_payable_invoice__name": "Eingangsrechnung", "@sage/xtrem-finance/activity__accounts_payable_open_item__name": "Offener Posten Verbindlichkeiten", "@sage/xtrem-finance/activity__accounts_receivable_advance__name": "Anzahlung Ausgangsrechnung", "@sage/xtrem-finance/activity__accounts_receivable_invoice__name": "Ausgangsrechnung", "@sage/xtrem-finance/activity__accounts_receivable_open_item__name": "Offener Posten Forderungen", "@sage/xtrem-finance/activity__accounts_receivable_payment__name": "Zahlung Ausgangsrechnung", "@sage/xtrem-finance/activity__datev_export__name": "DATEV-Export", "@sage/xtrem-finance/activity__generate_journal_entries__name": "Buchungen generieren", "@sage/xtrem-finance/activity__journal_entry__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/activity__journal_entry_inquiry__name": "Buchungsabfrage", "@sage/xtrem-finance/activity__payment__name": "Zahlung", "@sage/xtrem-finance/activity__receipt__name": "Eingang", "@sage/xtrem-finance/cant_post_ap_invoice_when_status_is_not_pending": "Der Status ist nicht {{pending}}. Die Eingangsrechnung kann nicht gebucht werden.", "@sage/xtrem-finance/cant_post_ar_invoice_when_status_is_not_pending": "Der Status ist nicht {{pending}}. Die Ausgangsrechnung kann nicht gebucht werden.", "@sage/xtrem-finance/check-date-range": "Das Startdatum kann nicht nach dem Enddatum liegen.", "@sage/xtrem-finance/classes__journal__cant_read_accounting_staging_amount": "Der bereitgestellte Buchungsbetrag für die Dokumentnummer {{documentNumber}} {{documentNumberId}} wurde nicht gefunden.", "@sage/xtrem-finance/classes__journal__cant_read_journal_entry_type": "Der Buchungstyp für das Dokument{{documentType}} mit der Dokumentnummer {{documentNumber}} {{documentId}} wurde nicht gefunden.", "@sage/xtrem-finance/classes__journal__cant_read_posting_class_pre_journal": "Die Buchungsklasse für das Dokument {{documentType}}, <PERSON><PERSON>mer {{documentNumber}}, wurde nicht gefunden. Der Buchungsklassentyp ist: {{postingClassType}}.", "@sage/xtrem-finance/classes__journal__journal_to_be_created_has_no_lines": "Das zu erstellende Journal enthält keine Zeilen.", "@sage/xtrem-finance/classes__journal__no_documents_to_process": "<PERSON><PERSON> zu verarbeitenden Dokuemnte für das Dokument {{documentType}}, den Zieldokumenttyp {{targetDocumentType}} und die Dokumentnummer {{documentNumber}}.", "@sage/xtrem-finance/classes__journal__target_document_type_not_supported": "Der Zieldokumenttyp {{targetDocumentType}} wird nicht unterstützt.", "@sage/xtrem-finance/classes__localized-messages__cant_read_account": "Das Konto {{account}} wurde nicht gefunden.", "@sage/xtrem-finance/classes__localized-messages__cant_read_accounting_staging_amount": "Der bereitgestellte Buchungsbetrag für die Dokumentnummer {{documentNumber}} {{documentNumberId}} wurde nicht gefunden.", "@sage/xtrem-finance/classes__localized-messages__cant_read_base_document_line": "Die Basisdokumentzeile {{baseDocumentLine}} wurde nicht gefunden.", "@sage/xtrem-finance/classes__localized-messages__cant_read_base_tax": "Die Steuerzeile {{baseTax}} wurde nicht gefunden.", "@sage/xtrem-finance/classes__localized-messages__cant_read_journal_entry_type": "Der Buchungstyp für {{documentType}} mit der Dokumentnummer {{documentNumber}} {{documentId}} wurde nicht gefunden.", "@sage/xtrem-finance/classes__localized-messages__cant_read_stock_journal": "Das Bestandsjournal {{stockJournal}} wurde nicht gefunden.", "@sage/xtrem-finance/classes__localized-messages__cant_read_taxPostingClass": "Die Steuerbuchungsklasse {{taxPostingClass}} wurde nicht gefunden.", "@sage/xtrem-finance/classes__localized-messages__journal_to_be_created_has_no_lines": "Die zu erstellende Buchung enthält keine Zeilen.", "@sage/xtrem-finance/classes__localized-messages__no_documents_to_process": "<PERSON>ür den Dokumenttyp {{documentType}}, den Zieldokumenttyp {{targetDocumentType}} und die Dokumentnummer {{documentNumber}} gibt es keine zu verarbeitenden Dokumente.", "@sage/xtrem-finance/classes__localized-messages__no_invoice": "Die Rechnungsnummer {{documentNumber}} wurde nicht gefunden.", "@sage/xtrem-finance/classes__localized-messages__target_document_type_not_supported": "Der Zieldokumenttyp {{targetDocumentType}} wird nicht unterstützt.", "@sage/xtrem-finance/classes__localized-messages__unable_to_get_account": "Das Konto kann nicht ermittelt werden.", "@sage/xtrem-finance/client_functions__record_common__different_currencies": "Die gewählte Währung unterscheidet sich von der Bankwährung.", "@sage/xtrem-finance/client_functions__record_common__invalid_date": "<PERSON><PERSON> müssen ein Datum in der Vergangenheit auswählen.", "@sage/xtrem-finance/client_functions__record_common__negative_credit_amount_error": "Erfassen Sie einen Gutschriftbetrag größer als 0.", "@sage/xtrem-finance/client_functions__record_common__negative_payment_amount_error": "Erfassen Sie einen Zahlungsbetrag größer als 0.", "@sage/xtrem-finance/client_functions__record_common__wrong_amount_in_bank_currency": "Die Banwährung und berechneten Beträge unterscheiden sich. Bankbetrag: {{currencySymbol}}{{bankAmount}}, berechneter Betrag: ({{currencySymbol}}{{amount}}).", "@sage/xtrem-finance/client_functions__record_common__wrong_credit_amount_error": "Der gutgeschriebene Betrag muss kleiner als der fällige Betrag sein.", "@sage/xtrem-finance/client_functions__record_common__wrong_payment_amount_error": "Der Zahlungsbetrag der Rechnung muss kleiner als der fällige Betrag sein.", "@sage/xtrem-finance/client_functions__void_record__void_date_should_be_after_payment_date": "Das Stornierungsdatum muss nach dem Zahlungsdatum liegen.", "@sage/xtrem-finance/data_types__datev_export_status_enum__name": "Enum Status DATEV-Export", "@sage/xtrem-finance/document_type_not_supported": "Der Dokumenttyp wird nicht unterstützt: {{documentType}}", "@sage/xtrem-finance/enums__datev_export_status__draft": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/enums__datev_export_status__error": "<PERSON><PERSON>", "@sage/xtrem-finance/enums__datev_export_status__exported": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/enums__datev_export_status__exportInProgress": "Export in Bearbeitung", "@sage/xtrem-finance/enums__datev_export_status__extracted": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/enums__datev_export_status__extractionInProgress": "Extraktion in Bearbeitung", "@sage/xtrem-finance/fail__bulk_open_item_payment_notification_description__view_link": "Protokolle <PERSON>ufgaben", "@sage/xtrem-finance/functions__accounting_engine__cant_read_account": "Das Konto {{account}} wurde nicht gefunden.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_base_document_line": "Basisdokumentzeile {{baseDocumentLine}} nicht gefunden,", "@sage/xtrem-finance/functions__accounting_engine__cant_read_base_tax": "Steuerzeile {{baseTax}} nicht gefunden.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_customer": "Kunde {{customer}} nicht gefunden.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_financial_site": "Buchhaltungsstandort {{financialSite}} nicht gefunden.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_item": "Artikel {{item}} nicht gefunden.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_journal_entry_type": "Buchungstyp für das Dokument {{documentType}}, Nummer {{documentNumber}} {{documentId}} nicht gefunden.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_stock_journal": "Das Bestandsjournal {{stockJournal}} wurde nicht gefunden.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_supplier": "Lieferant {{supplier}} nicht gefunden.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_tax": "Steuer {{tax}} nicht gefunden.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_taxPostingClass": "Steuerbuchungsklasse {{taxPostingClass}} nicht gefunden.", "@sage/xtrem-finance/functions__accounting_engine__cant_read_transaction_currency": "Transaktionswährung {{transactionCurrency}} nicht gefunden.", "@sage/xtrem-finance/functions__accounting_engine__customer_posting_class_missing_on_customer": "Erfassen Sie eine Buchungsklasse für den Kunden {{customer}}.", "@sage/xtrem-finance/functions__accounting_engine__item_posting_class_missing_on_item": "Die Buchungsklasse wurde für den Artikel {{item}} nicht gefunden.", "@sage/xtrem-finance/functions__accounting_engine__no_documents_to_be_processed": "<PERSON><PERSON> Dokument<PERSON> zu verarbeiten.", "@sage/xtrem-finance/functions__accounting_engine__no_documents_to_process": "<PERSON>ür den Dokumenttyp {{documentType}}, den Zieldokumenttyp {{targetDocumentType}} und die Dokumentnummer {{documentNumber}} gibt es keine zu verarbeitenden Dokumente.", "@sage/xtrem-finance/functions__accounting_engine__no_invoice": "Rechnungsnummer {{documentNumber}} nicht gefunden.", "@sage/xtrem-finance/functions__accounting_engine__processing": "{{i}}/{{numberToBeProcess}} verarbeitet {{documents}}", "@sage/xtrem-finance/functions__accounting_engine__supplier_posting_class_missing_on_supplier": "Erfassen Sie eine Buchungsklasse für den Lieferanten {{supplier}}.", "@sage/xtrem-finance/functions__accounting_engine__target_document_type_not_supported": "Der Zieldokumenttyp {{targetDocumentType}} wird nicht unterstützt.", "@sage/xtrem-finance/functions__accounting_engine__tax_posting_class_missing_on_tax": "Erfassen Sie eine Buchungsklasse für die Steuer {{tax}}.", "@sage/xtrem-finance/functions__accounting_engine__to_be_process": "<PERSON><PERSON><PERSON> zu verarbeitende Dokumente: {{numberToBeProcess}}", "@sage/xtrem-finance/generate-journal-entry-date": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein Startdatum, das vor dem Enddatum liegt.", "@sage/xtrem-finance/generate-pre-journal-date": "Das Startdatum kann nicht nach dem Enddatum liegen.", "@sage/xtrem-finance/menu_item__datev-interface": "DATEV-Schnittstelle", "@sage/xtrem-finance/menu_item__payment-tracking": "Zahlungsverfolgung", "@sage/xtrem-finance/node__accounting_staging__finish_create_journal": "Staging Buchhaltung: beendet {{messageJournalCreated}}", "@sage/xtrem-finance/node__accounting_staging__start_create_journal": "Beginn Staging Buchhaltung ", "@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_description": " Buchungen generiert: {{journalsCreated}}", "@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_title": "Batchaufgabe Generierung Buchung erfolgreich", "@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_title_fail": "Batchaufgabe Generierung Buchung nicht erfolgreich", "@sage/xtrem-finance/node__accounting-interface-listener__finance_document_notification_resent": "Das Dokument wird erneut verarbeitet.", "@sage/xtrem-finance/node__accounts_payable_invoice__resend_notification_for_finance": "Finanzbenachrichtigung für Eingangsrechnung {{apInvoiceNumber}} erneut senden.", "@sage/xtrem-finance/node__accounts_receivable_invoice__resend_notification_for_finance": "Finanzbenachrichtigung für Ausgangsrechnung {{arInvoiceNumber}} erneut senden.", "@sage/xtrem-finance/node-extensions__base-payment-document-extension__voided": "Die Zahlung wurde storniert.", "@sage/xtrem-finance/node-extensions__business_entity_extension__property__composedDescription": "Zusammengesetzte Bezeichnung", "@sage/xtrem-finance/node-extensions__item_extension__property__composedDescription": "Zusammengesetzte Bezeichnung", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__apOpenItem": "Offener Posten Verbindlichkeiten", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__arOpenItem": "Offener Posten Forderungen", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__companyAmount": "Unternehmensbetrag", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__origin": "Ursprung", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__originalNodeFactory": "Ursprünglicher Node-Standard", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__originalOpenItem": "Ursprünglicher offener Posten", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__signedAmount": "Vorzeichen Betrag", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__signedAmountBankCurrency": "Vorzeichen Betrag Bankwährung", "@sage/xtrem-finance/node-extensions__site_extension__property__composedDescription": "Zusammengesetzte Bezeichnung", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob": "Journale aus Staging-Job Buchhaltung erstellen", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__failed": "Journale aus Staging-Job Buchhaltung erstellen fehlgeschlagen.", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__parameter__batchTrackingId": "ID Batchrückmeldung", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__parameter__journalsCreatedData": "Daten Journale erstellt", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging": "Journale aus Staging Buchhaltung erstellen", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging__failed": "Journale aus Staging Buchhaltung erstellen fehlgeschlagen.", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging__parameter__journalsCreatedData": "Daten Journale erstellt", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__retryFinanceDocument": "Finanzdokument wiederholen", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__retryFinanceDocument__failed": "Finanzdokument wiederholen fehlgeschlagen.", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__retryFinanceDocument__parameter__financeTransaction": "Finanztransaktion", "@sage/xtrem-finance/nodes__accounting_interface_listener__node_name": "Listener Buchhaltungsschnittstelle", "@sage/xtrem-finance/nodes__accounts_open_item__node_name": "Offener Posten Konten", "@sage/xtrem-finance/nodes__accounts_payable_invoice__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_payable_invoice__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_payable_invoice__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_payable_invoice__cant_post_ap_invoice_when_status_is_not_draft": "Der Status ist nicht {{draft}}. Die Eingangsrechnung kann nicht gebucht werden.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__cant_post_ap_invoice_when_status_is_not_draft_nor_error": "Der Buchungsstatus ist nicht {{draft}} oder {{error}}. Die Eingangsrechnung kann nicht gebucht werden.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__post": "Buchen", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__post__failed": "Buchen fehlgeschlagen.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__post__parameter__apInvoice": "Eingangsrechnung", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__resendNotificationForFinance": "Benachrichtigung für Finanzen erneut senden", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__resendNotificationForFinance__failed": "Benachrichtigung für Finanzen erneut senden fehlgeschlagen.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__resendNotificationForFinance__parameter__apInvoice": "Eingangsrechnung", "@sage/xtrem-finance/nodes__accounts_payable_invoice__node_name": "Eingangsrechnung", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__account": "Ko<PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__billBySupplier": "Rechnungssteller", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__billBySupplierName": "Name Rechnungssteller", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__billBySupplierTaxIdNumber": "Steueridentifikationsnummer Rechnungssteller", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__companyFxRate": "Wechselkurs Unternehmen", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__companyFxRateDivisor": "Wechselkursdivisor Unternehmen", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__currency": "Währung", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__description": "Bezeichnung", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__documentDate": "Dokumentdatum", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__dueDate": "Fälligkeitsdatum", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationApp": "Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationAppRecordId": "Datensatz-ID Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationAppUrl": "URL Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationStatus": "Status Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financialSiteName": "Name Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financialSiteTaxIdNumber": "Steueridentifikationsnummer Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__fxRateDate": "Wechselkursdatum", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__internalFinanceIntegrationStatus": "Interner Finanzintegrationsstatus", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__invoiceDate": "Rechnungsdatum", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__openItems": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__origin": "Ursprung", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__paymentTerm": "Zahlungsbedingung", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__paymentTracking": "Zahlungsverfolgung", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__payToSupplier": "Zahlungsempfänger", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__payToSupplierLinkedAddress": "Verknüpfte Zahlungsempfängeradresse", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__postingDate": "Buchungsdatum", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__postingDetails": "Buchungsdetails", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__postingStatus": "Buchungsstatus", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__purchaseDocumentNumber": "Einkaufsdokumentnummer", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__purchaseDocumentSysId": "System-ID Einkaufsdokument", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__rateDescription": "Bezeichnung Kurs", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__returnLinkedAddress": "Verknüpfte Retouradresse", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__supplierDocumentDate": "Datum Lieferantendokument", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__supplierDocumentNumber": "Lieferantendokumentnummer", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__taxCalculationStatus": "Status Steuerberechnung", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__taxes": "Steuern", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalAmountExcludingTax": "Gesamtbetrag exkl. Steuern", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalAmountIncludingTax": "Gesamtbetrag inkl. Steuern", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalCompanyAmountExcludingTax": "Gesamtbetrag exkl. Steuern Unternehmen", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalCompanyAmountIncludingTax": "Gesamtbetrag inkl. Steuern Unternehmen", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalCompanyTaxAmount": "Gesamtsteuerbetrag Unternehmen", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalExemptAmount": "Gesamtbetrag Befreiung", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalTaxableAmount": "Gesamtsteuerbetrag", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalTaxAmount": "Gesamtsteuerbetrag", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalTaxAmountAdjusted": "Gesamtsteuerbetrag korrigiert", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__type": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice__type_invalid": "Wählen Sie eine Einkaufsrechnung oder eine Einkaufsgutschrift als Eingangsrechnungstyp aus.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__update_not_allowed_status_posted": "Die Eingangsrechnung ist gebucht. Aktualisierung nicht möglich.", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__node_name": "Eingangsrechnungszeile", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__account": "Ko<PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__accountingStagingLines": "Zeilen Staging Buchhaltung", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__amountExcludingTax": "Betrag exkl. Steuern", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__amountIncludingTax": "Betrag inkl. Steuern", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__attributesAndDimensions": "Attribute und Sektoren", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__currency": "Währung", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__description": "Bezeichnung", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__document": "Dokument", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__documentId": "Dokument-ID", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__documentLineType": "Dokumentzeilentyp", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__exemptAmount": "Befreiter Betrag", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__lineType": "Zeilentyp", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__recipientSite": "Empfängerstandort", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__sourceDocumentNumber": "<PERSON><PERSON>mer Ursprungsdokument", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__sourceDocumentType": "Typ Ursprungsdokument", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxableAmount": "Steuerpflichtiger Betrag", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxAmount": "Steuerbetrag", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxAmountAdjusted": "Steuerbetrag angepasst", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxDate": "Steuerdatum", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxDetail": "Steuerdetails", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxes": "Steuern", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxLineTaxAmount": "Steuerbetrag Steuerzeile", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__uiTaxes": "Steuern Arbeitslosenversicherung", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__node_name": "Sektor Zeile Eingangsrechnung", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__amount": "Betrag", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__analyticalData": "Analytische Daten", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__businessSite": "Geschäftsstandort", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__customer": "Kunde", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension01": "Sektor 01", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension02": "Sektor 02", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension03": "Sektor 03", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension04": "Sektor 04", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension05": "Sektor 05", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension06": "Sektor 06", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension07": "Sektor 07", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension08": "Sektor 08", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension09": "Sektor 09", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension10": "Sektor 10", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension11": "Sektor 11", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension12": "Sektor 12", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension13": "Sektor 13", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension14": "Sektor 14", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension15": "Sektor 15", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension16": "Sektor 16", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension17": "Sektor 17", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension18": "Sektor 18", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension19": "Sektor 19", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension20": "Sektor 20", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__employee": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__hasAttributesOrDimenionsChanged": "Hat geänderte Attribute oder Sektoren", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__item": "Artikel", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__manufacturingSite": "Fertigungsstandort", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__originLine": "Ursprüngliche Zeile", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__project": "Projekt", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__stockSite": "Lagerstandort", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__supplier": "Lieferant", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__task": "Aufgabe", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__node_name": "Steuer Eingangsrechnungszeile", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__property__currency": "Währung", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__property__document": "Dokument", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__node_name": "Steuer Eingangsrechnung", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__property__currency": "Währung", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__property__document": "Dokument", "@sage/xtrem-finance/nodes__accounts_payable_open_item__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_payable_open_item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_payable_open_item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_payable_open_item__bulkMutation__bulkOpenItemUpdate": "Massenaktualisierung offene Posten", "@sage/xtrem-finance/nodes__accounts_payable_open_item__bulkMutation__bulkOpenItemUpdate__failed": "Massenaktualisierung offene Posten fehlgeschlagen.", "@sage/xtrem-finance/nodes__accounts_payable_open_item__node_name": "Offener Posten Verbindlichkeiten", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__accountsPayableInvoice": "Eingangsrechnung", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__amountDue": "<PERSON><PERSON><PERSON><PERSON> Betrag", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__businessRelation": "Geschäftsbeziehung", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__paymentTracking": "Zahlungsverfolgung", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__remainingCompanyAmount": "Unternehmensbetrag verbleibend", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__totalAmount": "Gesamtbetrag", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__totalCompanyAmount": "Unternehmensbetrag gesamt", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__totalCompanyAmountPaid": "Unternehmensbetrag gesamt bezahlt", "@sage/xtrem-finance/nodes__accounts_payable_open_item__skipped": "Dokument übersprungen, da bereits vollständig bezahlt: {{number}}.", "@sage/xtrem-finance/nodes__accounts_receivable_advance__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_advance__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_advance__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_advance__cant_post_ar_advance_when_status_is_not_draft_nor_error": "Der Buchungsstatus muss '<PERSON><PERSON><PERSON><PERSON>' oder '<PERSON><PERSON>' sein. Die Kundenanzahlung kann nicht gebucht werden.", "@sage/xtrem-finance/nodes__accounts_receivable_advance__mutation__post": "Buchen", "@sage/xtrem-finance/nodes__accounts_receivable_advance__mutation__post__failed": "Buchen fehlgeschlagen.", "@sage/xtrem-finance/nodes__accounts_receivable_advance__mutation__post__parameter__arAdvance": "Anzahlung Ausgangsrechnung", "@sage/xtrem-finance/nodes__accounts_receivable_advance__node_name": "Anzahlung Ausgangsrechnung", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__advanceAmount": "Anzahlungsbetrag", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__advanceCompanyAmount": "Anzahlungsbetrag Unternehmen", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__bankAccount": "Bankkonto", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__companyCurrency": "Unternehmenswährung", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__companyFxRate": "Wechselkurs Unternehmen", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__companyFxRateDivisor": "Wechselkursdivisor Unternehmen", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__currency": "Währung", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__description": "Bezeichnung", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationApp": "Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationAppRecordId": "Datensatz-ID Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationAppUrl": "URL Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationStatus": "Status Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financialSiteName": "Name Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__fxRateDate": "Wechselkursdatum", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__internalFinanceIntegrationStatus": "Interner Finanzintegrationsstatus", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__paymentDate": "Zahlungsdatum", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__payToCustomerId": "ID Zahlungsempfänger", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__payToCustomerName": "Name Zahlungsempfänger", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__postingDate": "Buchungsdatum", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__postingStatus": "Buchungsstatus", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-finance/nodes__accounts_receivable_advance__update_not_allowed_status_posted": "Sie können eine Kundenanzahlung nur aktualisieren, wenn es sich um einen Entwurf handelt.", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__node_name": "Zeile Ku<PERSON>anzahlung", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__account": "Ko<PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__advanceAmount": "Anzahlungsbetrag", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__advanceCompanyAmount": "Anzahlungsbetrag Unternehmen", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__analyticalData": "Analytische Daten", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__businessSite": "Geschäftsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__companyCurrency": "Unternehmenswährung", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__computedAttributes": "Berechnete Attribute", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__currency": "Währung", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__customer": "Kunde", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__description": "Bezeichnung", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension01": "Sektor 01", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension02": "Sektor 02", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension03": "Sektor 03", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension04": "Sektor 04", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension05": "Sektor 05", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension06": "Sektor 06", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension07": "Sektor 07", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension08": "Sektor 08", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension09": "Sektor 09", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension10": "Sektor 10", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension11": "Sektor 11", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension12": "Sektor 12", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension13": "Sektor 13", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension14": "Sektor 14", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension15": "Sektor 15", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension16": "Sektor 16", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension17": "Sektor 17", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension18": "Sektor 18", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension19": "Sektor 19", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension20": "Sektor 20", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__document": "Dokument", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__documentId": "Dokument-ID", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__employee": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__hasAttributesOrDimenionsChanged": "Hat geänderte Attribute oder Sektoren", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__item": "Artikel", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__manufacturingSite": "Fertigungsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__project": "Projekt", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__stockSite": "Lagerstandort", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__supplier": "Lieferant", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__task": "Aufgabe", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__cant_post_ar_invoice_when_status_is_not_draft": "Der Status ist nicht {{draft}}. Die Ausgangsrechnung kann nicht gebucht werden.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__cant_post_ar_invoice_when_status_is_not_draft_nor_error": "Der Buchungsstatus ist nicht {{draft}} oder {{error}}. Die Ausgangsrechnung kann nicht gebucht werden.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__information_payment_tracking": "Aktualisieren Sie die zuvor erstellten offenen Posten entsprechend des Fälligkeitssaldos.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__post": "Buchen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__post__failed": "Buchen fehlgeschlagen.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__post__parameter__arInvoice": "Ausgangsrechnung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__resendNotificationForFinance": "Benachrichtigung für Finanzen erneut senden", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__resendNotificationForFinance__failed": "Benachrichtigung für Finanzen erneut senden fehlgeschlagen.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__resendNotificationForFinance__parameter__arInvoice": "Ausgangsrechnung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__node_name": "Ausgangsrechnung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__payment_tracking": "Dienstoption Zahlungsverfolgung aktiviert", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__account": "Ko<PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__billToCustomer": "Rechnungsempfänger", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__billToCustomerName": "Name Rechnungsempfänger", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__billToCustomerTaxIdNumber": "Steueridentifikationsnummer Rechnungsempfänger", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__companyFxRate": "Wechselkurs Unternehmen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__companyFxRateDivisor": "Wechselkursdivisor Unternehmen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__currency": "Währung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__description": "Bezeichnung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__documentDate": "Dokumentdatum", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__dueDate": "Fälligkeitsdatum", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationApp": "Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationAppRecordId": "Datensatz-ID Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationAppUrl": "URL Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationStatus": "Status Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financialSiteName": "Name Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financialSiteTaxIdNumber": "Steueridentifikationsnummer Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__fxRateDate": "Wechselkursdatum", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__internalFinanceIntegrationStatus": "Interner Finanzintegrationsstatus", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__invoiceDate": "Rechnungsdatum", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__isPrinted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__openItems": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__origin": "Ursprung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__paymentStatus": "Zahlungsstatus", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__paymentTerm": "Zahlungsbedingung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__postingDate": "Buchungsdatum", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__postingDetails": "Buchungsdetails", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__postingStatus": "Buchungsstatus", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__rateDescription": "Bezeichnung Kurs", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__salesDocumentNumber": "Verkaufsdokumentnummer", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__salesDocumentSysId": "System-ID Verkaufsdokument", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__taxCalculationStatus": "Status Steuerberechnung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__taxEngine": "Steuermodul", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__taxes": "Steuern", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalAmountExcludingTax": "Gesamtbetrag exkl. Steuern", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalAmountIncludingTax": "Gesamtbetrag inkl. Steuern", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalCompanyAmountExcludingTax": "Gesamtbetrag exkl. Steuern Unternehmen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalCompanyAmountIncludingTax": "Gesamtbetrag inkl. Steuern Unternehmen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalCompanyTaxAmount": "Gesamtsteuerbetrag Unternehmen", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalExemptAmount": "Gesamtbetrag Befreiung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalTaxableAmount": "Gesamtsteuerbetrag", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalTaxAmount": "Gesamtsteuerbetrag", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalTaxAmountAdjusted": "Gesamtsteuerbetrag angepasst", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__type": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__type_invalid": "Wählen Sie eine Verkaufsrechnung oder eine Verkaufsgutschrift als Ausgangsrechnungstyp aus.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__update_not_allowed_status_posted": "Die Ausgangsrechnung ist gebucht. Aktualisierung nicht möglich.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__node_name": "Ausgangsrechnungszeile", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__account": "Ko<PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__accountingStagingLines": "Zeilen Staging Buchhaltung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__amountExcludingTax": "Betrag exkl. Steuern", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__amountIncludingTax": "Betrag inkl. Steuern", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__attributesAndDimensions": "Attribute und Sektoren", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__currency": "Währung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__description": "Bezeichnung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__document": "Dokument", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__documentId": "Dokument-ID", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__documentLineType": "Dokumentzeilentyp", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__exemptAmount": "Befreiter Betrag", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__lineType": "Zeilentyp", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__netPriceIncludingTax": "Nettopreis inkl. Steuern", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__providerSite": "Anbieterstandort", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__quantity": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__quantityInSalesUnit": "Menge in Verkaufseinheit", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__sourceDocumentNumber": "<PERSON><PERSON>mer Ursprungsdokument", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__sourceDocumentType": "Typ Ursprungsdokument", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxableAmount": "Steuerpflichtiger Betrag", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxAmount": "Steuerbetrag", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxAmountAdjusted": "Steuerbetrag korrigiert", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxDate": "Steuerdatum", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxDetail": "Steuerdetails", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxes": "Steuern", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxLineTaxAmount": "Steuerbetrag Steuerzeile", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__uiTaxes": "Steuern Arbeitslosenversicherung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__node_name": "Sektor Zeile Ausgangsrechnung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__amount": "Betrag", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__analyticalData": "Analytische Daten", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__businessSite": "Geschäftsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__customer": "Kunde", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension01": "Sektor 01", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension02": "Sektor 02", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension03": "Sektor 03", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension04": "Sektor 04", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension05": "Sektor 05", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension06": "Sektor 06", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension07": "Sektor 07", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension08": "Sektor 08", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension09": "Sektor 09", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension10": "Sektor 10", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension11": "Sektor 11", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension12": "Sektor 12", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension13": "Sektor 13", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension14": "Sektor 14", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension15": "Sektor 15", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension16": "Sektor 16", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension17": "Sektor 17", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension18": "Sektor 18", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension19": "Sektor 19", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension20": "Sektor 20", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__employee": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__hasAttributesOrDimenionsChanged": "Hat geänderte Attribute oder Sektoren", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__item": "Artikel", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__manufacturingSite": "Fertigungsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__originLine": "Ursprüngliche Zeile", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__project": "Projekt", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__stockSite": "Lagerstandort", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__supplier": "Lieferant", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__task": "Aufgabe", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__node_name": "Steuer Ausgangsrechnungszeile", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__property__currency": "Währung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__property__document": "Dokument", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__node_name": "Steuer Ausgangsrechnungszeile", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__property__currency": "Währung", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__property__document": "Dokument", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__bulkMutation__bulkOpenItemUpdate": "Massenaktualisierung offene Posten", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__bulkMutation__bulkOpenItemUpdate__failed": "Massenaktualisierung offene Posten fehlgeschlagen.", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__node_name": "Offener Posten Forderungen", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__accountsReceivableInvoice": "Ausgangsrechnung", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__amountDue": "<PERSON><PERSON><PERSON><PERSON> Betrag", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__businessRelation": "Geschäftsbeziehung", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__receipts": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__remainingCompanyAmount": "Unternehmensbetrag verbleibend", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__totalAmount": "Gesamtbetrag", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__totalCompanyAmount": "Unternehmensbetrag gesamt", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__totalCompanyAmountPaid": "Unternehmensbetrag gesamt bezahlt", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__skipped": "Dokument übersprungen, da bereits vollständig bezahlt: {{number}}.", "@sage/xtrem-finance/nodes__accounts_receivable_payment__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_payment__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_payment__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_payment__cant_post_ar_payment_when_status_is_not_draft_nor_error": "Der Buchungsstatus muss '<PERSON><PERSON><PERSON><PERSON>' oder '<PERSON><PERSON>' sein. Die Zahlung zur Ausgangsrechnung kann nicht gebucht werden.", "@sage/xtrem-finance/nodes__accounts_receivable_payment__mutation__post": "Buchen", "@sage/xtrem-finance/nodes__accounts_receivable_payment__mutation__post__failed": "Buchen fehlgeschlagen.", "@sage/xtrem-finance/nodes__accounts_receivable_payment__mutation__post__parameter__arPayment": "Zahlung Ausgangsrechnung", "@sage/xtrem-finance/nodes__accounts_receivable_payment__node_name": "Zahlung Ausgangsrechnung", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__bankAccount": "Bankkonto", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__companyCurrency": "Unternehmenswährung", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__companyFxRate": "Wechselkurs Unternehmen", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__companyFxRateDivisor": "Wechselkursdivisor Unternehmen", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__currency": "Währung", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__description": "Bezeichnung", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationApp": "Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationAppRecordId": "Datensatz-ID Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationAppUrl": "URL Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationStatus": "Status Integration Finanzen", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financialSiteName": "Name Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__fxRateDate": "Wechselkursdatum", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__internalFinanceIntegrationStatus": "Interner Finanzintegrationsstatus", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__paymentAmount": "Zahlungsbetrag", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__paymentCompanyAmount": "Zahlungsbetrag Unternehmen", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__paymentDate": "Zahlungsdatum", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__payToCustomerId": "ID Zahlungsempfänger", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__payToCustomerName": "Name Zahlungsempfänger", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__postingDate": "Buchungsdatum", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__postingStatus": "Buchungsstatus", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-finance/nodes__accounts_receivable_payment__update_not_allowed_status_posted": "Der Zahlungsstatus der Ausgangsrechnung ist {{status}}. Aktualisierung nicht möglich.", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__node_name": "Zahlungszeile Ausgangsrechnung", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__companyCurrency": "Unternehmenswährung", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__currency": "Währung", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__document": "Dokument", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__documentId": "Dokument-ID", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__documentNumber": "Dokumentnummer", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__paymentAmount": "Zahlungsbetrag", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__paymentCompanyAmount": "Zahlungsbetrag Unternehmen", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__type": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__type_invalid": "Der Zeilentyp der Ausgangsrechnungszahlung muss 'Verkaufsrechnung' oder 'Verkaufsgutschrift' sein.", "@sage/xtrem-finance/nodes__ap_invoice__posted": "Die Eingangsrechnung wurde gebucht.", "@sage/xtrem-finance/nodes__ar_advance__posted": "Die Kundenanzahlung wurde gebucht.", "@sage/xtrem-finance/nodes__ar_invoice__posted": "Die Ausgangsrechnung wurde gebucht.", "@sage/xtrem-finance/nodes__ar_payment__posted": "Die Zahlung zur Ausgangsrechnung wurde gebucht.", "@sage/xtrem-finance/nodes__base_open_item__text_forced_close": "A<PERSON>chluss erzwungen am {{date}} durch {{user}}", "@sage/xtrem-finance/nodes__base_payment_document_payment_already_voided": "Die Zahlung wurde bereits storniert.", "@sage/xtrem-finance/nodes__datev_export__account_without_datev_id": "Konto ohne DATEV-ID: {{account}}.", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExport": "DATEV-Export", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExport__failed": "DATEV-Export fehlgeschlagen.", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExtraction": "DATEV-Extraktion", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExtraction__failed": "DATEV-Extraktion fehlgeschlagen.", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExtraction__parameter__id": "ID", "@sage/xtrem-finance/nodes__datev_export__business_relation_without_datev_id": "Geschäftsentität ohne DATEV-ID: {{businessEntity}}.", "@sage/xtrem-finance/nodes__datev_export__complete_account_export": "Der Export des DATEV-Kontos wurde erfolgreich abgeschlossen.", "@sage/xtrem-finance/nodes__datev_export__complete_business_relation_export": "Der Export der DATEV-Geschäftsbeziehung wurde erfolgreich abgeschlossen.", "@sage/xtrem-finance/nodes__datev_export__complete_export": "DATEV-Export erfolgreich abgeschlossen.", "@sage/xtrem-finance/nodes__datev_export__complete_journal_entry_lines_export": "Der Export der DATEV-Buchungszeile wurde erfolgreich abgeschlossen.", "@sage/xtrem-finance/nodes__datev_export__datev_file": "DATEV-Exportdateien", "@sage/xtrem-finance/nodes__datev_export__datev_file_ready": "Erstellte Dateien: {{nbOfFilesCreated}}.\n", "@sage/xtrem-finance/nodes__datev_export__datev_number_of_warnings": "Warnungen: {{nbOfWarnings}}.", "@sage/xtrem-finance/nodes__datev_export__end_extraction": "DATEV-Extraktion abgeschlossen.", "@sage/xtrem-finance/nodes__datev_export__failure_description": "Die DATEV-Extraktion wurde nicht erfolgreich durchgeführt. Fehler: {{error}}", "@sage/xtrem-finance/nodes__datev_export__failure_description_export": "DATEV-Export fehlgeschlagen. Fehler: {{error}}", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_account_without_datev_id": "Buchungszeile für ein Konto ohne DATEV-ID. Buchung {{journalEntryNumber}}, Konto {{account}}.", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_contra_account_without_datev_id": "Buchungszeile für ein Gegenkonto ohne DATEV-ID. Buchung {{journalEntryNumber}}, Gegenkonto {{contraAccount}}.", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_no_tax_line_for_taxable_amount": "<PERSON><PERSON><PERSON> den steuerpflichtigen Betrag wurde keine Steuerzeile gefunden. Buchung {{journalEntryNumber}}.", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_with_different_tax": "Buchungszeile mit einem Automatikkonto mit einem anderen Steuercode. Buchung {{journalEntryNumber}}, Konto {{account}}.", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_without_posting_key": "Buchungszeile mit einem Nicht-Automatikkonto und ohne Buchungsschlüssel. Buchung {{journalEntryNumber}}, Konto {{account}}.", "@sage/xtrem-finance/nodes__datev_export__node_name": "DATEV-Export", "@sage/xtrem-finance/nodes__datev_export__number_extracted_accounts": "Extrahierte DATEV-Konten: {{numberExtractedAccounts}}.", "@sage/xtrem-finance/nodes__datev_export__number_extracted_customers_suppliers": "Extrahierte DATEV-Kunden und Lieferanten: {{numberExtractedBusinessRelations}}.", "@sage/xtrem-finance/nodes__datev_export__number_extracted_journal_entry_lines": "DATEV-Buchungszeilen extrahiert: {{numberExtractedJournalEntryLines}}.", "@sage/xtrem-finance/nodes__datev_export__property__accountsWithoutDatevId": "Konten ohne DATEV-ID", "@sage/xtrem-finance/nodes__datev_export__property__attributeType1": "Attributtyp 1", "@sage/xtrem-finance/nodes__datev_export__property__attributeType2": "Attributtyp 2", "@sage/xtrem-finance/nodes__datev_export__property__company": "Unternehmen", "@sage/xtrem-finance/nodes__datev_export__property__customersWithoutDatevId": "Kunden ohne DATEV-ID", "@sage/xtrem-finance/nodes__datev_export__property__dateRange": "Datumsbereich", "@sage/xtrem-finance/nodes__datev_export__property__datevConsultantNumber": "DATEV-Beraternummer", "@sage/xtrem-finance/nodes__datev_export__property__datevCustomerNumber": "DATEV-Kundennummer", "@sage/xtrem-finance/nodes__datev_export__property__datevExportAccounts": "Konten DATEV-Export", "@sage/xtrem-finance/nodes__datev_export__property__datevExportBusinessRelations": "Geschäftsbeziehungen DATEV-Export", "@sage/xtrem-finance/nodes__datev_export__property__datevExportJournalEntryLines": "Buchungszeilen DATEV-Export", "@sage/xtrem-finance/nodes__datev_export__property__dimensionType1": "Dimension 1", "@sage/xtrem-finance/nodes__datev_export__property__dimensionType2": "Dimension 2", "@sage/xtrem-finance/nodes__datev_export__property__doAccounts": "Konten", "@sage/xtrem-finance/nodes__datev_export__property__doCustomersSuppliers": "Kunden und Lieferanten", "@sage/xtrem-finance/nodes__datev_export__property__doJournalEntries": "Buchungen", "@sage/xtrem-finance/nodes__datev_export__property__endDate": "Enddatum", "@sage/xtrem-finance/nodes__datev_export__property__fiscalYearStart": "Geschäftsjahresbeginn", "@sage/xtrem-finance/nodes__datev_export__property__id": "ID", "@sage/xtrem-finance/nodes__datev_export__property__isLocked": "Ist Festschreibung", "@sage/xtrem-finance/nodes__datev_export__property__skrCoa": "SKR COA", "@sage/xtrem-finance/nodes__datev_export__property__startDate": "Startdatum", "@sage/xtrem-finance/nodes__datev_export__property__status": "Status", "@sage/xtrem-finance/nodes__datev_export__property__suppliersWithoutDatevId": "Lieferanten ohne DATEV-ID", "@sage/xtrem-finance/nodes__datev_export__property__timeStamp": "Zeitstempel", "@sage/xtrem-finance/nodes__datev_export__start_export": "DATEV-Export gestartet.", "@sage/xtrem-finance/nodes__datev_export__start_extract_accounts": "Start der DATEV-Konto-Extraktion.", "@sage/xtrem-finance/nodes__datev_export__start_extract_customers_suppliers": "Start der DATEV-Kunden- und Lieferanten-Extraktion.", "@sage/xtrem-finance/nodes__datev_export__start_extract_journal_entry_lines": "Start der DATEV-Buchungszeilen-Extraktion.", "@sage/xtrem-finance/nodes__datev_export__start_extraction": "DATEV-Extraktion gestartet.", "@sage/xtrem-finance/nodes__datev_export__stop_extraction": "DATEV-Extraktion anhalten am {{stopDate}}.", "@sage/xtrem-finance/nodes__datev_export__success_description": "DATEV-Extraktion erfolgreich abgeschlossen.", "@sage/xtrem-finance/nodes__datev_export__success_notification_title": "DATEV-Extraktion abgeschlossen", "@sage/xtrem-finance/nodes__datev_export__user_download_accounts": "<PERSON><PERSON><PERSON> herunt<PERSON>n", "@sage/xtrem-finance/nodes__datev_export__user_download_journal_entry_lines": "Buchungszeilen herunterladen", "@sage/xtrem-finance/nodes__datev_export__user_notifications_download_customers_and_suppliers": "Kunden und Lieferanten herunterladen", "@sage/xtrem-finance/nodes__datev_export__user_notifications_history": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__datev_export_account__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__datev_export_account__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__datev_export_account__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__datev_export_account__node_name": "Konto DATEV-Export", "@sage/xtrem-finance/nodes__datev_export_account__property__account": "Ko<PERSON>", "@sage/xtrem-finance/nodes__datev_export_account__property__datevExport": "DATEV-Export", "@sage/xtrem-finance/nodes__datev_export_account__property__datevId": "DATEV-ID", "@sage/xtrem-finance/nodes__datev_export_account__property__name": "Name", "@sage/xtrem-finance/nodes__datev_export_business_relation__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__datev_export_business_relation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__datev_export_business_relation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__datev_export_business_relation__node_name": "Geschäftsbeziehung DATEV-Export", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__businessRelation": "Geschäftsbeziehung", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__city": "Stadt", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__country": "Land", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__datevExport": "DATEV-Export", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__datevId": "DATEV-ID", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__name": "Name", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__postcode": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__street": "Straße", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__taxIdNumber": "Steueridentifikationsnummer", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__node_name": "Buchungszeile DATEV-Export", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__businessEntityTaxIdNumber": "Steueridentifikationsnummer Geschäftsentität", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__companyCurrency": "Unternehmenswährung", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__companyFxRate": "Wechselkurs Unternehmen", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__companyValue": "Unternehmenswert", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevAccountId": "DATEV-Konto-ID", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevContraAccountId": "DATEV-Gegenkonto-ID", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevExport": "DATEV-Export", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevSign": "DATEV-Vorzeichen", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__description": "Bezeichnung", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__dimension1": "Sektor 1", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__dimension2": "Sektor 2", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__journalEntryLine": "Buchungszeile", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__locked": "Festschreibung", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__postingDate": "Buchungsdatum", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__postingKey": "Buchungsschlüssel", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__siteTaxIdNumber": "Steueridentifikationsnummer Standort", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__supplierDocumentNumber": "Lieferantendokumentnummer", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__transactionValue": "Transaktionswert", "@sage/xtrem-finance/nodes__datev_export_listener__datev_file": "DATEV-Datei", "@sage/xtrem-finance/nodes__datev_export_listener__datev_file_ready": "Die Datei kann heruntergeladen werden", "@sage/xtrem-finance/nodes__datev_export_listener__node_name": "Listener DATEV-Export", "@sage/xtrem-finance/nodes__initialize_open_item__node_name": "Initialisierung offener Posten", "@sage/xtrem-finance/nodes__intacct-bank-account-transaction_feed__site_legislation_and_coa_legislation_dont_match": "Die Rechtsordung des Standorts muss die gleiche wie die Rechtsordnung des Kontenrahmens sein.", "@sage/xtrem-finance/nodes__journal_entry__ap_invoice_reference": "Die Eingangsrechnungsreferenz kann nur in vorläufigen Buchungen aus einer Eingangsrechnung verwendet werden.", "@sage/xtrem-finance/nodes__journal_entry__ar_invoice_reference": "Die Ausgangsrechnungsreferenz kann nur in vorläufigen Buchungen aus einer Ausgangsrechnung verwendet werden.", "@sage/xtrem-finance/nodes__journal_entry__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__journal_entry__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__journal_entry__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__journal_entry__contra_journal_entry_line_is_mandatory_on_new_documents": "<PERSON>e müssen eine Gegenbuchung in der Zeile erfassen.", "@sage/xtrem-finance/nodes__journal_entry__document_type_not_allowed": "Der Dokumenttyp kann nur für die französische Rechtsordnung gesetzt werden.", "@sage/xtrem-finance/nodes__journal_entry__journal_entry_dimension_line_not_equal_to_journal_line": "Der Gesamtbetrag der Kostenaufteilung Attribut/Sektor ist nicht gleich dem Zeilenbetrag.", "@sage/xtrem-finance/nodes__journal_entry__mutation__post": "Buchen", "@sage/xtrem-finance/nodes__journal_entry__mutation__post__failed": "Buchen fehlgeschlagen.", "@sage/xtrem-finance/nodes__journal_entry__mutation__post__parameter__journalEntry": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry__mutation__updateDocumentStatusFromIntacct": "Update Document Status From Intacct", "@sage/xtrem-finance/nodes__journal_entry__no_attribute_value_account": "<PERSON><PERSON><PERSON> Konto {{lineAccount}} in der Journalzeile {{lineCount}} wurden keine erforderlichen Kontosektoren [{{dimensions}}] ausgewiesen.", "@sage/xtrem-finance/nodes__journal_entry__no_attribute_value_company": "<PERSON><PERSON><PERSON> Konto {{lineAccount}} in der Journalzeile {{lineCount}} wurden keine erforderlichen Unternehmenssektoren [{{dimensions}}] ausgewiesen.", "@sage/xtrem-finance/nodes__journal_entry__no_sequence_number": "Der Buchungsnummernkreis kann nicht generiert werden. Erfassen Sie zu<PERSON>t einen Standardnummernkreis.", "@sage/xtrem-finance/nodes__journal_entry__node_name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry__number_change_forbidden": "Sie können nur die Buchungsnummer in neuen Datensätzen bearbeiten.", "@sage/xtrem-finance/nodes__journal_entry__property__apInvoice": "Eingangsrechnung", "@sage/xtrem-finance/nodes__journal_entry__property__arInvoice": "Ausgangsrechnung", "@sage/xtrem-finance/nodes__journal_entry__property__description": "Bezeichnung", "@sage/xtrem-finance/nodes__journal_entry__property__documentType": "Dokumenttyp", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationApp": "Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationAppRecordId": "Datensatz-ID Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationAppUrl": "URL Anwendung Integration Finanzen", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationStatus": "Status Integration Finanzen", "@sage/xtrem-finance/nodes__journal_entry__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__journal_entry__property__internalFinanceIntegrationStatus": "Interner Finanzintegrationsstatus", "@sage/xtrem-finance/nodes__journal_entry__property__journal": "Journal", "@sage/xtrem-finance/nodes__journal_entry__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry__property__origin": "Ursprung", "@sage/xtrem-finance/nodes__journal_entry__property__postingDate": "Buchungsdatum", "@sage/xtrem-finance/nodes__journal_entry__property__postingStatus": "Buchungsstatus", "@sage/xtrem-finance/nodes__journal_entry__property__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry__query__areFinanceIntegrationPackagesActive": "Pakete für Finanzintegration aktiv", "@sage/xtrem-finance/nodes__journal_entry__query__areFinanceIntegrationPackagesActive__failed": "Sind Pakete für Finanzintegration aktiv fehlgeschlagen.", "@sage/xtrem-finance/nodes__journal_entry__query__areFinanceIntegrationPackagesActive__parameter__dummy": "Platzhalter", "@sage/xtrem-finance/nodes__journal_entry__reconcile_journal_entry_by_key": "Die Buchung ist für den Buchhaltungsstandort {{financialSite}} nicht saldiert.", "@sage/xtrem-finance/nodes__journal_entry__two_journal_entry_lines_mandatory": "Erfassen Sie mindestens zwei Zeilen in der Buchung.", "@sage/xtrem-finance/nodes__journal_entry_inquiry__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__journal_entry_inquiry__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__journal_entry_inquiry__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__journal_entry_inquiry__mutation__singleRecord": "Einzelner Datensatz", "@sage/xtrem-finance/nodes__journal_entry_inquiry__mutation__singleRecord__failed": "Einzelner Datensatz fehlgeschlagen.", "@sage/xtrem-finance/nodes__journal_entry_inquiry__node_name": "Buchungsabfrage", "@sage/xtrem-finance/nodes__journal_entry_inquiry__property__journalEntryLines": "Buchungszeilen", "@sage/xtrem-finance/nodes__journal_entry_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__journal_entry_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__journal_entry_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__journal_entry_line__business_entity_reference_mandatory": "Das Konto {{account}} ist ein Sammelkonto. Erfassen Sie eine Geschäftsentität.", "@sage/xtrem-finance/nodes__journal_entry_line__business_entity_reference_not_possible": "Das Konto {{account}} ist kein Sammelkonto. Sie können keine Geschäftsentität erfassen.", "@sage/xtrem-finance/nodes__journal_entry_line__journal_entry_type_line_is_mandatory_on_new_documents": "Sie müssen den Buchungstyp in der Zeile erfassen.", "@sage/xtrem-finance/nodes__journal_entry_line__node_name": "Buchungszeile", "@sage/xtrem-finance/nodes__journal_entry_line__property__account": "Ko<PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__accountingStagingLines": "Zeilen Staging Buchhaltung", "@sage/xtrem-finance/nodes__journal_entry_line__property__attributesAndDimensions": "Attribute und Sektoren", "@sage/xtrem-finance/nodes__journal_entry_line__property__baseTax": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__blank": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__businessEntity": "Geschäftsentität", "@sage/xtrem-finance/nodes__journal_entry_line__property__businessSiteAttribute": "Attribut Geschäftsstandort", "@sage/xtrem-finance/nodes__journal_entry_line__property__chartOfAccount": "Kontenrahmen", "@sage/xtrem-finance/nodes__journal_entry_line__property__commonReference": "Allgemeine Referenz", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyAmount": "Unternehmensbetrag", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyCredit": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyCurrency": "Unternehmenswährung", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyDebit": "Soll Unternehmen", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyFxRate": "Wechselkurs Unternehmen", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyFxRateDivisor": "Wechselkursdivisor Unternehmen", "@sage/xtrem-finance/nodes__journal_entry_line__property__contraAccount": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__contraJournalEntryLine": "Gegenbuchungszeile", "@sage/xtrem-finance/nodes__journal_entry_line__property__customerAttribute": "Attribut Kunde", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevBusinessEntityTaxIdNumber": "Steueridentifikationsnummer Geschäftsentität DATEV", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevCompanyAmount": "DATEV-Unternehmenskonto", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevContraAccountId": "DATEV-Gegenkonto-ID", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevPostingKey": "DATEV-Buchungsschlüssel", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevTransactionAmount": "DATEV-Transaktionsbetrag", "@sage/xtrem-finance/nodes__journal_entry_line__property__deductibleTaxRate": "Abzugsfähiger Steuersatz", "@sage/xtrem-finance/nodes__journal_entry_line__property__description": "Bezeichnung", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension01": "Sektor 01", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension02": "Sektor 02", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension03": "Sektor 03", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension04": "Sektor 04", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension05": "Sektor 05", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension06": "Sektor 06", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension07": "Sektor 07", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension08": "Sektor 08", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension09": "Sektor 09", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension10": "Sektor 10", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension11": "Sektor 11", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension12": "Sektor 12", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension13": "Sektor 13", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension14": "Sektor 14", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension15": "Sektor 15", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension16": "Sektor 16", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension17": "Sektor 17", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension18": "Sektor 18", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension19": "Sektor 19", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension20": "Sektor 20", "@sage/xtrem-finance/nodes__journal_entry_line__property__dueDate": "Fälligkeitsdatum", "@sage/xtrem-finance/nodes__journal_entry_line__property__employeeAttribute": "Attribut <PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteAmount": "Betrag Buchhaltungsstandort", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteAttribute": "Attribut Buchhaltungsstandort", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteCredit": "Haben Buchhaltungsstandort", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteCurrency": "Währung Buchhaltungsstandort", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteDebit": "Soll Buchhaltungsstandort", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteFxRate": "Wechselkurs Buchhaltungsstandort", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteFxRateDivisor": "Wechselkursdivisor Buchhaltungsstandort", "@sage/xtrem-finance/nodes__journal_entry_line__property__fxRateDate": "Wechselkursdatum", "@sage/xtrem-finance/nodes__journal_entry_line__property__inquiryDescription": "Bezeichnung Abfrage", "@sage/xtrem-finance/nodes__journal_entry_line__property__inquiryTransactionCurrency": "Transaktionswährung Abfrage", "@sage/xtrem-finance/nodes__journal_entry_line__property__isBalanceLine": "Ist Ausgleich Zeile", "@sage/xtrem-finance/nodes__journal_entry_line__property__itemAttribute": "Attribut Artikel", "@sage/xtrem-finance/nodes__journal_entry_line__property__journalEntry": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__journalEntryTypeLine": "Buchungstypzeile", "@sage/xtrem-finance/nodes__journal_entry_line__property__manufacturingSiteAttribute": "Attribut Fertigungsstandort", "@sage/xtrem-finance/nodes__journal_entry_line__property__numericSign": "Numerisches Zeichen", "@sage/xtrem-finance/nodes__journal_entry_line__property__projectAttribute": "Attribut Projekt", "@sage/xtrem-finance/nodes__journal_entry_line__property__rateDescription": "Bezeichnung Kurs", "@sage/xtrem-finance/nodes__journal_entry_line__property__sign": "Vorzeichen", "@sage/xtrem-finance/nodes__journal_entry_line__property__signedTransactionAmount": "Vorzeichen Transaktionsbetrag", "@sage/xtrem-finance/nodes__journal_entry_line__property__stockSiteAttribute": "Attribut Lagerstandort", "@sage/xtrem-finance/nodes__journal_entry_line__property__supplierAttribute": "Attribut Lieferant", "@sage/xtrem-finance/nodes__journal_entry_line__property__supplierDocumentNumber": "Lieferantendokumentnummer", "@sage/xtrem-finance/nodes__journal_entry_line__property__taskAttribute": "Aufgabenattribut", "@sage/xtrem-finance/nodes__journal_entry_line__property__tax": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line__property__taxDate": "Steuerdatum", "@sage/xtrem-finance/nodes__journal_entry_line__property__taxExternalReference": "Steuer externe Referenz", "@sage/xtrem-finance/nodes__journal_entry_line__property__taxRate": "Steuersatz", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionAmount": "Transaktionsbetrag", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionCredit": "Haben Transaktion", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionDebit": "Soll Transaktion", "@sage/xtrem-finance/nodes__journal_entry_line__property__validationDate": "Freigabedatum", "@sage/xtrem-finance/nodes__journal_entry_line__tax_date_empty": "<PERSON>sen Sie das Steuerdatum leer.", "@sage/xtrem-finance/nodes__journal_entry_line__tax_date_mandatory": "Erfassen Sie ein Steuerdatum.", "@sage/xtrem-finance/nodes__journal_entry_line__tax_ref_empty": "<PERSON>sen Sie die Steuerreferenz leer.", "@sage/xtrem-finance/nodes__journal_entry_line__tax_ref_mandatory": "Erfassen Sie eine Steuerreferenz.", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__node_name": "Sektor Buchungszeile", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__analyticalData": "Analytische Daten", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__businessSite": "Geschäftsstandort", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__companyAmount": "Unternehmensbetrag", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__companyCurrency": "Unternehmenswährung", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__customer": "Kunde", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension01": "Sektor 01", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension02": "Sektor 02", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension03": "Sektor 03", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension04": "Sektor 04", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension05": "Sektor 05", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension06": "Sektor 06", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension07": "Sektor 07", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension08": "Sektor 08", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension09": "Sektor 09", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension10": "Sektor 10", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension11": "Sektor 11", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension12": "Sektor 12", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension13": "Sektor 13", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension14": "Sektor 14", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension15": "Sektor 15", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension16": "Sektor 16", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension17": "Sektor 17", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension18": "Sektor 18", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension19": "Sektor 19", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension20": "Sektor 20", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__employee": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__financialSite": "Buchhaltungsstandort", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__financialSiteAmount": "Betrag Buchhaltungsstandort", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__financialSiteCurrency": "Währung Buchhaltungsstandort", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__hasAttributesOrDimenionsChanged": "Hat geänderte Attribute oder Sektoren", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__item": "Artikel", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__journalEntryLine": "Buchungszeile", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__manufacturingSite": "Fertigungsstandort", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__project": "Projekt", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__stockSite": "Lagerstandort", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__storedAttributes": "Gespeicherte Attribute", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__storedComputedAttributes": "Gespeicherte berechnete Attribute", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__storedDimensions": "Gespeicherte Sektoren", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__supplier": "Lieferant", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__task": "Aufgabe", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__transactionAmount": "Transaktionsbetrag", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__transactionCurrency": "Transaktionswährung", "@sage/xtrem-finance/nodes__journal_entry_line_staging__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__journal_entry_line_staging__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__journal_entry_line_staging__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__journal_entry_line_staging__node_name": "Staging Buchungszeile", "@sage/xtrem-finance/nodes__journal_entry_line_staging__property__accountingStaging": "Staging Buchhaltung", "@sage/xtrem-finance/nodes__journal_entry_line_staging__property__journalEntryLine": "Buchungszeile", "@sage/xtrem-finance/nodes__journal-entry__cant_post_journal_entry_when_status_is_not_draft_nor_error": "Der Buchungsstatus ist nicht {{draft}} oder {{error}}. Die Buchungsrechnung kann nicht gebucht werden.", "@sage/xtrem-finance/nodes__journal-entry__posted": "Die Buchung wurde gebucht.", "@sage/xtrem-finance/nodes__payment__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__payment__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__payment__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment": "Zahlung stornieren", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__failed": "Zahlung stornieren fehlgeschlagen.", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__parameter__payment": "Zahlung", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__parameter__voidDate": "Stornierungsdatum", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__parameter__voidText": "Stornierungstext", "@sage/xtrem-finance/nodes__payment__node_name": "Zahlung", "@sage/xtrem-finance/nodes__payment_document_line__ap_invoice_reference_mandatory": "Sie müssen eine Referenz zur Eingangsrechnung hinzufügen.", "@sage/xtrem-finance/nodes__payment_document_line__ap_invoice_reference_not_allowed": "Sie können die Eingangsrechnung nicht referenzieren.", "@sage/xtrem-finance/nodes__payment_document_line__ar_invoice_reference_mandatory": "Sie müssen eine Referenz zur Ausgangsrechnung hinzufügen.", "@sage/xtrem-finance/nodes__payment_document_line__ar_invoice_reference_not_allowed": "Sie können die Ausgangsrechnung nicht referenzieren.", "@sage/xtrem-finance/nodes__payment_document_line__wrong_ap_invoice_posting_status": "Der Buchungsstatus der Eingangsrechnung muss gebucht sein.", "@sage/xtrem-finance/nodes__payment_document_line__wrong_ar_invoice_posting_status": "Der Buchungsstatus der Ausgangsrechnung muss gebucht sein.", "@sage/xtrem-finance/nodes__receipt__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__receipt__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__receipt__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__receipt__mutation__createReceipt": "<PERSON><PERSON><PERSON> er<PERSON>llen", "@sage/xtrem-finance/nodes__receipt__mutation__createReceipt__parameter__receiptHeader": "Kopfzeile Eingang", "@sage/xtrem-finance/nodes__receipt__mutation__createReceipt__parameter__receiptLines": "Eingangszeilen", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment": "Zahlung stornieren", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__failed": "Zahlung stornieren fehlgeschlagen.", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__parameter__payment": "Zahlung", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__parameter__voidDate": "Stornierungsdatum", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__parameter__voidText": "Stornierungstext", "@sage/xtrem-finance/nodes__receipt__node_name": "Eingang", "@sage/xtrem-finance/nodes__receipt__payment_amount_discrepancy": "Der Zahlungsbetrag des Dokuments {{amount}} muss der Summe der Zahlungsbeträge aller Zeilen {{total}} entsprechen.", "@sage/xtrem-finance/notification_bulk_open_item_payment__description_error": "Die erzwungene Zahlung für alle offenen Posten ist fehlgeschlagen. Überprüfen Sie die Protokolle der Batchaufgaben, um weitere Informationen zu erhalten.", "@sage/xtrem-finance/notification_bulk_open_item_payment__description_success": "Die Zahlung für die offenen Posten wurde erzwungen.", "@sage/xtrem-finance/package__name": "Fin<PERSON>zen", "@sage/xtrem-finance/page__void_date__mandatory": "Das Datum ist erforderlich.", "@sage/xtrem-finance/page_fragments__base_payment__date_issued": "<PERSON><PERSON>", "@sage/xtrem-finance/page_fragments__base_payment__date_received": "<PERSON><PERSON>", "@sage/xtrem-finance/page_fragments__payment_information__amount_in_bank_currency_must_be_positive": "Der Betrag in Bankwährung muss größer als 0 sein.", "@sage/xtrem-finance/page_fragments__payment_information__date_issued": "<PERSON><PERSON>", "@sage/xtrem-finance/page_fragments__payment_information__date_received": "<PERSON><PERSON>", "@sage/xtrem-finance/page_fragments__payment_information__payment_amount_can_not_be_negative": "Der Zahlungsbetrag muss größer als oder gleich 0 sein.", "@sage/xtrem-finance/page_fragments__payment_information__transaction_information_can_not_exceed_100_characters": "Die Transaktionsinformationen dürfen nicht mehr als 100 Zeichen enthalten.", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionCriteriaBlock____title": "Kriterien", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionDocumentType____title": "Dokumenttyp", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___createStamp": "Gesendet", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___id": "ID", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__batchId": "Batch-ID", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__message": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__originDocumentNumber": "Dokumentnummer", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__originDocumentType": "Dokumenttyp", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__postingStatus": "Status", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__status": "Benachrichtigungssta<PERSON>", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__targetDocumentNumber": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__targetDocumentType": "<PERSON><PERSON>", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__wasResent": "<PERSON><PERSON><PERSON> gesendet", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____dropdownActions__title__2": "Sektoren Ursprungsdokument bearbeiten", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____title": "Ergebnisse", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionSection____title": "Fin<PERSON>zen", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionStatus____title": "Status", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocuments____columns__title__sourceDocumentNumber": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocuments____columns__title__sourceDocumentType": "Dokument", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocuments____title": "Ursprungsdokumente", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocumentsBlock____title": "Ursprungsdokumente", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__adjustmentAmount": "Korrekturbetrag", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__amount": "Betrag", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__discountAmount": "Skontobetrag", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__bankAccount__name": "Bankkonto", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__isVoided": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__number": "Zahlungsnummer", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__paymentDate": "Zahlungsdatum", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__paymentMethod": "Zahlungsart", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__reference": "Transaktionsinformationen", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__penaltyAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____title": "Zahlungen", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__adjustmentAmount": "Korrekturbetrag", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__amount": "Betrag", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__discountAmount": "Skontobetrag", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__bankAccount__name": "Bankkonto", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__isVoided": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__number": "Eingangsnummer", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__paymentDate": "Eingangsdatum", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__paymentMethod": "Zahlungsart", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__reference": "Transaktionsinformationen", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__penaltyAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment__amount____title": "Zahlungsbetrag", "@sage/xtrem-finance/page-fragments__base_payment__amountBankCurrency____title": "Betrag in Bankwährung", "@sage/xtrem-finance/page-fragments__base_payment__bankAccount____title": "Bankkonto", "@sage/xtrem-finance/page-fragments__base_payment__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-finance/page-fragments__base_payment__currency____title": "Währung", "@sage/xtrem-finance/page-fragments__base_payment__customer____title": "Kunde", "@sage/xtrem-finance/page-fragments__base_payment__financialSite____title": "Buchhaltungsstandort", "@sage/xtrem-finance/page-fragments__base_payment__isVoided____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment__paymentDate____title": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment__paymentMethod____title": "Zahlungsart", "@sage/xtrem-finance/page-fragments__base_payment__reference____title": "Transaktionsinformationen", "@sage/xtrem-finance/page-fragments__base_payment__supplier____title": "Lieferant", "@sage/xtrem-finance/page-fragments__base_payment__voidDate____title": "Storniert am", "@sage/xtrem-finance/page-fragments__base_payment__voidText____title": "Stornierungstext", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__adjustmentAmount": "Korrekturbetrag", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__dueDate": "Fälligkeitsdatum", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__postingDate": "Buchungsdatum", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__purchaseDocumentNumber": "Dokumentnummer", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__dueDate": "Fälligkeitsdatum", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__postingDate": "Buchungsdatum", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__salesDocumentNumber": "Dokumentnummer", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__currency__id": "Währung", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__discountAmount": "Skontobetrag", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__origin": "Ursprung", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__penaltyAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__signedAmount": "Betrag", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__signedAmountBankCurrency": "Betrag in Bankwährung", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__open_item_discount__discountDate____title": "Skontodatum", "@sage/xtrem-finance/page-fragments__open_item_discount__discountType____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__open_item_discount__displayDiscountPaymentDate____title": "Skonto Zahlung vor Datum", "@sage/xtrem-finance/page-fragments__open_item_general__businessRelation____title": "Geschäftsbeziehung", "@sage/xtrem-finance/page-fragments__open_item_general__companyAmountDueSigned____title": "Unternehmensbetrag fällig", "@sage/xtrem-finance/page-fragments__open_item_general__documentNumberLink____title": "Dokumentnummer", "@sage/xtrem-finance/page-fragments__open_item_general__documentType____title": "Dokumenttyp", "@sage/xtrem-finance/page-fragments__open_item_general__dueDate____title": "Fälligkeitsdatum", "@sage/xtrem-finance/page-fragments__open_item_general__financialSite____title": "Buchhaltungsstandort", "@sage/xtrem-finance/page-fragments__open_item_general__transactionAmountDueSigned____title": "Transaktionsbetrag fällig", "@sage/xtrem-finance/page-fragments__open_item_penalty__penaltyPaymentType____title": "Ma<PERSON>typ", "@sage/xtrem-finance/page-fragments__payment_information__amount____title": "Zahlungsbetrag", "@sage/xtrem-finance/page-fragments__payment_information__amountBankCurrency____title": "Betrag in Bankwährung", "@sage/xtrem-finance/page-fragments__payment_information__bankAccount____title": "Bankkonto", "@sage/xtrem-finance/page-fragments__payment_information__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-finance/page-fragments__payment_information__currency____title": "Währung", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__country__name": "Land", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__id": "ID", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__name": "Name", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__taxIdNumber": "Steuer-ID", "@sage/xtrem-finance/page-fragments__payment_information__customer____title": "Kunde", "@sage/xtrem-finance/page-fragments__payment_information__date____title": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____placeholder": "Buchhaltungsstandort", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____title": "Buchhaltungsstandort", "@sage/xtrem-finance/page-fragments__payment_information__paymentMethod____title": "Zahlungsart", "@sage/xtrem-finance/page-fragments__payment_information__reference____title": "Transaktionsinformationen", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__country__name": "Land", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__id": "ID", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__name": "Name", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__taxIdNumber": "Steuer-ID", "@sage/xtrem-finance/page-fragments__payment_information__supplier____title": "Lieferant", "@sage/xtrem-finance/page-fragments__payment_information__type____title": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__number": "Rechnungsnummer", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__origin": "Ursprung", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__postingDate": "Buchungsdatum", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__number": "Rechnungsnummer", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__origin": "Ursprung", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__postingDate": "Buchungsdatum", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__adjustmentAmount": "Korrekturbetrag", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__amountDue": "<PERSON><PERSON><PERSON><PERSON> Betrag", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__creditAmount": "Gutschriftbetrag", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__currency__id": "Währung", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__discountPaymentAmount": "Skontobetrag", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__dueDate": "Fälligkeitsdatum", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__paymentAmount": "Zahlungsbetrag", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__penaltyPaymentAmount": "<PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__remainingCompanyAmount": "Unternehmensbetrag fällig", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__totalAmount": "Gesamtbetrag", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__totalCompanyAmount": "Unternehmensbetrag gesamt", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__totalCompanyAmountPaid": "Unternehmensbetrag bezahlt", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__id__title": "ID Rechnungssteller", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line_4__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line_5__title": "Rechnungsdatum", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line10__title": "Summe exkl. Steuern", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line11__title": "Korrigierte Gesamtsteuer", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line12__title": "Steuer gesamt", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line13__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line14__title": "Lieferantendokumentnummer", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line15__title": "Fälligkeitsdatum", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line16__title": "Zahlungsbedingung", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line2__title": "Rechnungssteller", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line4__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line5__title": "Rechnungsdatum", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line6__title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line7__title": "Summe inkl. Steu<PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line8__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line9__title": "Ursprung", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__titleRight__title": "Buchungsstatus", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__3": "Gebucht", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__4": "In Bearbeitung", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__5": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice____objectTypePlural": "Eingangsrechnungen", "@sage/xtrem-finance/pages__accounts_payable_invoice____objectTypeSingular": "Eingangsrechnung", "@sage/xtrem-finance/pages__accounts_payable_invoice____subtitle": "Eingangsrechnung", "@sage/xtrem-finance/pages__accounts_payable_invoice____title": "Eingangsrechnung", "@sage/xtrem-finance/pages__accounts_payable_invoice__billBySupplier____title": "Rechnungssteller", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____columns__title__id": "ISO 4217", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____columns__title__name": "Name", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____columns__title__symbol": "Symbol", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-finance/pages__accounts_payable_invoice__currency____title": "Währung", "@sage/xtrem-finance/pages__accounts_payable_invoice__dueDate____title": "Fälligkeitsdatum", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationApp____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationAppRecordId____title": "Referenz Integration Buchhaltung", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationAppRecordIdLink____title": "Referenz Integration Buchhaltung", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationAppUrl____title": "URL Integration Finanzen", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationStatus____title": "Status Integration Buchhaltung", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__columns__title___id": "ID", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__columns__title__id__2": "ID", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__title": "Währung", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__title__5": "Steuermodul", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__columns__legalCompany__name__title__6": "Währung", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_payable_invoice__generalSection____title": "Allgemein", "@sage/xtrem-finance/pages__accounts_payable_invoice__goToSysNotificationPage____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__internalFinanceIntegrationStatus____title": "Interner Status", "@sage/xtrem-finance/pages__accounts_payable_invoice__invoiceDate____title": "Rechnungsdatum", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__account__composedDescription__title__3": "Zusammengesetzte Bezeichnung", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__currency__id__title": "Name", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__currency__id__title__2": "ISO 4217", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__currency__id__title__3": "Symbol", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__currency__id__title__4": "Dezimalstellen", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__columns__originLine___id__title": "ID", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__account__composedDescription": "Ko<PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__amount": "Betrag", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__amountExcludingTax": "Betrag exkl. Steuern", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__amountIncludingTax": "Betrag inkl. Steuern", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__description": "Bezeichnung", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__financialSite__name": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__lineType": "Zeilentyp", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__originLine___id": "Rechnungszeile", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__recipientSite__name": "Eingangsstandort", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__taxAmount": "Steuerbetrag", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__taxAmountAdjusted": "Steuerbetrag", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__taxDate": "Steuerdatum", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__uiTaxes": "Steuern Arbeitslosenversicherung", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__dropdownActions__title": "Steuerdetails", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__origin____title": "Ursprung", "@sage/xtrem-finance/pages__accounts_payable_invoice__paymentStatus____title": "Zahlungsstatus", "@sage/xtrem-finance/pages__accounts_payable_invoice__paymentTerm____title": "Zahlungsbedingung", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDate____title": "Buchungsdatum", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__financeIntegrationAppRecordId": "Referenz Integration Buchhaltung", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__targetDocumentNumber": "Dokumentnummer", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__targetDocumentType": "Dokumenttyp", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingMessageBlock____title": "Fehlerdetails", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingStatus____title": "Buchungsstatus", "@sage/xtrem-finance/pages__accounts_payable_invoice__rateDescription____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__reference____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__supplierDocumentDate____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__supplierDocumentNumber____title": "<PERSON><PERSON><PERSON>rechnung", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__columns__account__composedDescription__title__3": "Zusammengesetzte Bezeichnung", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__columns__originLine___id__title": "ID", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__account__composedDescription": "Ko<PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__amount": "Betrag", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__financialSite__name": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__recipientSite__name": "Empfängerstandort", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__taxDetail": "Steuerdetails", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__taxLineTaxAmount": "Steuerbetrag", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__dropdownActions__title": "Steuerdetails", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____title": "Steuerdetailzeilen", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__postfix__taxRate": "%", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title___sortValue": "Sortierwert", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__tax": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxableAmount": "Bemessungsgrundlage", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxAmount": "Betrag", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxAmountAdjusted": "Korrigierter Betrag", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxCategory": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxRate": "Satz", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalAmountExcludingTax____title": "Berechnete Summe exkl. Steuern", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalAmountIncludingTax____title": "Berechnete Summe inkl. Steuern", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalsSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalsSectionTaxTotalsBlock____title": "", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalTaxAmount____title": "Berechnete Gesamtsteuer", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalTaxAmountAdjusted____title": "Korrigierte berechnete Gesamtsteuer", "@sage/xtrem-finance/pages__accounts_payable_invoice__type____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__businessEntityId__title": "ID Lieferant", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__closeReason__title": "Grund für Abschluss", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__closeText__title": "Text Abschluss", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__companyAmountDueSigned__title": "Unternehmensbetrag fällig", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__companyAmountPaid__title": "Unternehmensbetrag bezahlt", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__companyCurrency__title": "Unternehmenswährung", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__currency__title": "Währung", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__documentType__title": "Dokumenttyp", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__financialSite__title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__forcedAmountPaid__title": "Bezahlter Betrag erzwungen", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__line2__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__line2Right__title": "Fälligkeitsdatum", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__remainingCompanyAmount__title": "Unternehmensbetrag verbleibend", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__remainingTransactionAmount__title": "Transaktionsbetrag verbleibend", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__title__title": "Dokumentnummer", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__transactionAmountDueSigned__title": "Transaktionsbetrag fällig", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__transactionAmountPaid__title": "Transaktionsbetrag bezahlt", "@sage/xtrem-finance/pages__accounts_payable_open_item____objectTypePlural": "Offene Posten Verbindlichkeiten", "@sage/xtrem-finance/pages__accounts_payable_open_item____objectTypeSingular": "Offener Posten Verbindlichkeiten", "@sage/xtrem-finance/pages__accounts_payable_open_item____title": "Offener Posten Verbindlichkeiten", "@sage/xtrem-finance/pages__accounts_payable_open_item__forcedAmountPaidSigned____title": "Bezahlter Betrag erzwungen", "@sage/xtrem-finance/pages__accounts_payable_open_item__generalSection____title": "Allgemein", "@sage/xtrem-finance/pages__accounts_payable_open_item__status____title": "Status", "@sage/xtrem-finance/pages__accounts_payable_open_item__transactionAmountPaidSigned____title": "Bezahlter Betrag gesamt", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__bulkActions__title": "Offene Posten schließen", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__businessEntityId__title": "ID Lieferant", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__closeReason__title": "Grund für Abschluss", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__closeText__title": "Text Abschluss", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__companyAmountDueSigned__title": "Unternehmensbetrag fällig", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__companyAmountPaid__title": "Unternehmensbetrag bezahlt", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__companyCurrency__title": "Unternehmenswährung", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__currency__title": "Währung", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__displayDiscountPaymentDate__title": "Skonto Zahlung vor Datum", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__documentType__title": "Dokumenttyp", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__financialSite__title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__forcedAmountPaid__title": "Bezahlter Betrag erzwungen", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__line2__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__line2Right__title": "Fälligkeitsdatum", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__remainingCompanyAmount__title": "Unternehmensbetrag verbleibend", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__remainingTransactionAmount__title": "Transaktionsbetrag verbleibend", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__title__title": "Dokumentnummer", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__transactionAmountDueSigned__title": "Transaktionsbetrag fällig", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__transactionAmountPaid__title": "Transaktionsbetrag bezahlt", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title": "Nicht vollständig bezahlt", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__2": "Alle Statuswerte", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__3": "<PERSON><PERSON> bezahlt", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__4": "Teilweise bezahlt", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__5": "Be<PERSON>hlt", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____objectTypePlural": "Initialisierung Zahlungsausgänge", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____objectTypeSingular": "Initialisierung Zahlungsausgang", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____title": "Initialisierung Zahlungsausgang", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__closeReason____title": "Grund für Abschluss", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__closeText____title": "Text Abschluss", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__forcedAmountPaidSigned____title": "Bezahlter Betrag erzwungen", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__generalSection____title": "Allgemein", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__save____title": "Speichern", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__transactionAmountPaidSigned____title": "Bezahlter Betrag gesamt", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line_4__title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line13__title": "Zahlungsdatum", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line2__title": "Bezeichnung", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line2Right__title": "Buchungsdatum", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line4__title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line7__title": "Zahlungsempfänger", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line8__title": "Anzahlungsbetrag", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line9__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__titleRight__title": "Buchungsstatus", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__3": "Gebucht", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__4": "In Bearbeitung", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__5": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance____objectTypePlural": "Anzahlungen Ausgangsrechnung", "@sage/xtrem-finance/pages__accounts_receivable_advance____objectTypeSingular": "Anzahlung Ausgangsrechnung", "@sage/xtrem-finance/pages__accounts_receivable_advance____title": "Anzahlung Ausgangsrechnung", "@sage/xtrem-finance/pages__accounts_receivable_advance__advanceAmount____title": "Anzahlungsbetrag", "@sage/xtrem-finance/pages__accounts_receivable_advance__bankAccount____title": "Bank", "@sage/xtrem-finance/pages__accounts_receivable_advance__description____title": "Bezeichnung", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationApp____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationAppRecordId____title": "Referenz Integration Buchhaltung", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationAppRecordIdLink____title": "Referenz Integration Buchhaltung", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationAppUrl____title": "URL Integration Finanzen", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationStatus____title": "Status Integration Buchhaltung", "@sage/xtrem-finance/pages__accounts_receivable_advance__financialSite____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-finance/pages__accounts_receivable_advance__financialSite____title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_receivable_advance__generalSection____title": "Allgemein", "@sage/xtrem-finance/pages__accounts_receivable_advance__internalFinanceIntegrationStatus____title": "Interner Status", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____columns__title__account__composedDescription": "Ko<PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____columns__title__advanceAmount": "Betrag", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____columns__title__description": "Bezeichnung", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_advance__paymentDate____title": "Zahlungsdatum", "@sage/xtrem-finance/pages__accounts_receivable_advance__payToCustomerName____title": "Zahlungsempfänger", "@sage/xtrem-finance/pages__accounts_receivable_advance__postingDate____title": "Eingangsdatum", "@sage/xtrem-finance/pages__accounts_receivable_advance__postingStatus____title": "Buchungsstatus", "@sage/xtrem-finance/pages__accounts_receivable_advance__reference____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__billToCustomerId__title": "ID Rechnungsempfänger", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line_4__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line_5__title": "Rechnungsdatum", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line10__title": "Ursprung", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line11__title": "Summe exkl. Steuern", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line12__title": "Korrigierte Gesamtsteuer", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line13__title": "Fälligkeitsdatum", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line14__title": "Zahlungsbedingung", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line2__title": "Bezeichnung", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line2Right__title": "Buchungsdatum", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line4__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line5__title": "Rechnungsdatum", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line6__title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line7__title": "Rechnungsempfänger", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line8__title": "Summe inkl. Steu<PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line9__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__titleRight__title": "Buchungsstatus", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__3": "Gebucht", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__4": "In Bearbeitung", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__5": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice____objectTypePlural": "Ausgangsrechnungen", "@sage/xtrem-finance/pages__accounts_receivable_invoice____objectTypeSingular": "Ausgangsrechnung", "@sage/xtrem-finance/pages__accounts_receivable_invoice____subtitle": "Ausgangsrechnung", "@sage/xtrem-finance/pages__accounts_receivable_invoice____title": "Ausgangsrechnung", "@sage/xtrem-finance/pages__accounts_receivable_invoice__billToCustomer____columns__title__businessEntity__id": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__billToCustomer____columns__title__businessEntity__name": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__billToCustomer____title": "Rechnungsempfänger", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____columns__title__id": "ISO 4217", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____columns__title__name": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____columns__title__symbol": "Symbol", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-finance/pages__accounts_receivable_invoice__currency____title": "Währung", "@sage/xtrem-finance/pages__accounts_receivable_invoice__dueDate____title": "Fälligkeitsdatum", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationApp____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationAppRecordId____title": "Referenz Integration Buchhaltung", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationAppRecordIdLink____title": "Referenz Integration Buchhaltung", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationAppUrl____title": "URL Integration Finanzen", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationStatus____title": "Status Integration Buchhaltung", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__columns__title___id": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__columns__title__id__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__columns__title__symbol": "Symbol", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__title": "Währung", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__title__5": "Steuermodul", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__columns__legalCompany__name__title__6": "Währung", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_receivable_invoice__generalSection____title": "Allgemein", "@sage/xtrem-finance/pages__accounts_receivable_invoice__goToSysNotificationPage____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__internalFinanceIntegrationStatus____title": "Interner Status", "@sage/xtrem-finance/pages__accounts_receivable_invoice__invoiceDate____title": "Rechnungsdatum", "@sage/xtrem-finance/pages__accounts_receivable_invoice__isPrinted____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__account__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__account__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__account__composedDescription__title__3": "Zusammengesetzte Bezeichnung", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__currency__id__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__currency__id__title__2": "ISO 4217", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__currency__id__title__3": "Symbol", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__currency__id__title__4": "Dezimalstellen", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension01__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension01__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension02__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension02__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension03__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension03__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension04__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension04__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension05__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension05__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension06__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension06__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension07__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension07__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension08__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension08__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension09__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension09__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension10__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension10__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension11__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension11__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension12__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension12__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension13__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension13__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension14__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension14__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension15__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension15__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension16__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension16__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension17__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension17__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension18__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension18__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension19__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension19__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension20__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__dimension20__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__employee__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__employee__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__originLine___id__title": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__project__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__project__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__task__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__columns__task__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__account__composedDescription": "Ko<PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__amount": "Betrag", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__amountExcludingTax": "Betrag exkl. Steuern", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__amountIncludingTax": "Betrag inkl. Steuern", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__description": "Bezeichnung", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__financialSite__name": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__lineType": "Zeilentyp", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__originLine___id": "Rechnungszeile", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__providerSite__name": "Anbieterstandort", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__taxAmount": "Steuerbetrag", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__taxDate": "Steuerdatum", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__dropdownActions__title": "Steuerdetails", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__origin____title": "Ursprung", "@sage/xtrem-finance/pages__accounts_receivable_invoice__paymentStatus____title": "Zahlungsstatus", "@sage/xtrem-finance/pages__accounts_receivable_invoice__paymentTerm____title": "Zahlungsbedingung", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDate____title": "Buchungsdatum", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__financeIntegrationAppRecordId": "Referenz Integration Buchhaltung", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__targetDocumentNumber": "Dokumentnummer", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__targetDocumentType": "Dokumenttyp", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingMessageBlock____title": "Fehlerdetails", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingStatus____title": "Buchungsstatus", "@sage/xtrem-finance/pages__accounts_receivable_invoice__rateDescription____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__reference____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__status____title": "Status", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__account__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__account__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__account__composedDescription__title__3": "Zusammengesetzte Bezeichnung", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension01__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension01__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension02__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension02__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension03__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension03__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension04__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension04__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension05__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension05__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension06__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension06__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension07__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension07__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension08__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension08__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension09__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension09__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension10__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension10__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension11__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension11__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension12__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension12__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension13__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension13__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension14__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension14__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension15__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension15__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension16__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension16__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension17__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension17__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension18__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension18__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension19__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension19__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension20__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__dimension20__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__employee__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__employee__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__originLine___id__title": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__project__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__project__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__task__composedDescription__title": "Name", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__columns__task__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__account__composedDescription": "Ko<PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__amount": "Betrag", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__financialSite__name": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__providerSite__name": "Anbieterstandort", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__taxDetail": "Steuerdetails", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__taxLineTaxAmount": "Steuerbetrag", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____title": "Steuerdetailzeilen", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__postfix__taxRate": "%", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__tax": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxableAmount": "Bemessungsgrundlage", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxAmount": "Betrag", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxCategory": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxRate": "Satz", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalAmountExcludingTax____title": "Summe exkl. Steuern", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalAmountIncludingTax____title": "Summe inkl. Steu<PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalsSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalsSectionTaxTotalsBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalTaxAmount____title": "Steuer gesamt", "@sage/xtrem-finance/pages__accounts_receivable_invoice__type____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__businessEntityId__title": "Kunden-ID", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__closeReason__title": "Grund für Abschluss", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__closeText__title": "Text Abschluss", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__companyAmountDueSigned__title": "Unternehmensbetrag fällig", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__companyAmountPaid__title": "Unternehmensbetrag bezahlt", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__companyCurrency__title": "Unternehmenswährung", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__currency__title": "Währung", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__displayDiscountPaymentDate__title": "Skonto Zahlung vor Datum", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__documentType__title": "Dokumenttyp", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__financialSite__title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__forcedAmountPaid__title": "Bezahlter Betrag erzwungen", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__line2__title": "Kundenname", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__line2Right__title": "Fälligkeitsdatum", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__remainingCompanyAmount__title": "Unternehmensbetrag verbleibend", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__remainingTransactionAmount__title": "Transaktionsbetrag verbleibend", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__title__title": "Dokumentnummer", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__transactionAmountDueSigned__title": "Transaktionsbetrag fällig", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__transactionAmountPaid__title": "Transaktionsbetrag bezahlt", "@sage/xtrem-finance/pages__accounts_receivable_open_item____objectTypePlural": "Offene Posten Forderungen", "@sage/xtrem-finance/pages__accounts_receivable_open_item____objectTypeSingular": "Offener Posten Forderungen", "@sage/xtrem-finance/pages__accounts_receivable_open_item____title": "Offener Posten Forderungen", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeReason____columns__title__id": "ID", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeReason____columns__title__name": "Name", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeReason____title": "Grund für Abschluss", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeText____title": "Text Abschluss", "@sage/xtrem-finance/pages__accounts_receivable_open_item__customer____helperText": "ID", "@sage/xtrem-finance/pages__accounts_receivable_open_item__discount_amount": "Skontobetrag", "@sage/xtrem-finance/pages__accounts_receivable_open_item__discount_percentage": "Skonto in Prozent", "@sage/xtrem-finance/pages__accounts_receivable_open_item__financialSite____helperText": "ID", "@sage/xtrem-finance/pages__accounts_receivable_open_item__forcedAmountPaidSigned____title": "Bezahlter Betrag erzwungen", "@sage/xtrem-finance/pages__accounts_receivable_open_item__generalSection____title": "Allgemein", "@sage/xtrem-finance/pages__accounts_receivable_open_item__penalty_amount": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item__penalty_percentage": "Mahngebühr in Prozent", "@sage/xtrem-finance/pages__accounts_receivable_open_item__transactionAmountPaidSigned____title": "Bezahlter Betrag gesamt", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__bulkActions__title": "Offene Posten schließen", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__businessEntityId__title": "Kunden-ID", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__closeReason__title": "Grund für Abschluss", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__closeText__title": "Text Abschluss", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__companyAmountDueSigned__title": "Unternehmensbetrag fällig", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__companyAmountPaid__title": "Unternehmensbetrag bezahlt", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__companyCurrency__title": "Unternehmenswährung", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__currency__title": "Währung", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__displayDiscountPaymentDate__title": "Skonto Zahlung vor Datum", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__documentType__title": "Dokumenttyp", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__financialSite__title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__forcedAmountPaid__title": "Bezahlter Betrag erzwungen", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__line2__title": "Kundenname", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__line2Right__title": "Fälligkeitsdatum", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__remainingCompanyAmount__title": "Unternehmensbetrag verbleibend", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__remainingTransactionAmount__title": "Transaktionsbetrag verbleibend", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__status__title": "Status", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__title__title": "Dokumentnummer", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__transactionAmountDueSigned__title": "Transaktionsbetrag fällig", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__transactionAmountPaid__title": "Transaktionsbetrag bezahlt", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title": "Nicht vollständig bezahlt", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__2": "Alle Statuswerte", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__3": "<PERSON><PERSON> bezahlt", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__4": "Teilweise bezahlt", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__5": "Be<PERSON>hlt", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____objectTypePlural": "Initialisierung Zahlungseingänge", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____objectTypeSingular": "Initialisierung Zahlungseingang", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____title": "Initialisierung Zahlungseingang", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeReason____columns__title__id": "ID", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeReason____columns__title__name": "Name", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeReason____title": "Grund für Abschluss", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeText____title": "Text Abschluss", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__forced_amount_paid_wrongly_negative": "Der erzwungene bezahlte Betrag muss positiv sein.", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__forced_amount_paid_wrongly_positive": "Der erzwungene bezahlte Betrag muss negativ sein.", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__forcedAmountPaidSigned____title": "Bezahlter Betrag erzwungen", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__generalSection____title": "Allgemein", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__save____title": "Speichern", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__transactionAmountPaidSigned____title": "Bezahlter Betrag gesamt", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__wrong_forced_amount_paid": "Der erzwungene bezahlte Betrag muss zwischen 0 und {{maxForcedAmount}} liegen.", "@sage/xtrem-finance/pages__datev_export____navigationPanel__dropdownActions__title": "Extraktion", "@sage/xtrem-finance/pages__datev_export____navigationPanel__dropdownActions__title__2": "Export", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__datevConsultantNumber__title": "Beraternummer", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__datevcustomerNumber__title": "Kundennummer", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__doAccounts__title": "Konten", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__doCustomersSuppliers__title": "Kunden und Lieferanten", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__doJournalEntries__title": "Buchungen", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__fiscalYearStart__title": "Geschäftsjahresbeginn", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__id__title": "ID", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__isLocked__title": "Festschreibung", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__line2__title": "Startdatum", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__line2Right__title": "Enddatum", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__timeStamp__title": "Extrahiert am", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__title__title": "Unternehmen", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-finance/pages__datev_export____objectTypePlural": "DATEV-Exporte", "@sage/xtrem-finance/pages__datev_export____objectTypeSingular": "DATEV-Export", "@sage/xtrem-finance/pages__datev_export____title": "DATEV-Export", "@sage/xtrem-finance/pages__datev_export__accountsSection____title": "Extrahierte Konten", "@sage/xtrem-finance/pages__datev_export__accountsWithoutDatevId____columns__title__id": "ID", "@sage/xtrem-finance/pages__datev_export__accountsWithoutDatevId____columns__title__name": "Name", "@sage/xtrem-finance/pages__datev_export__accountsWithoutDatevId____title": "Konten ohne DATEV-ID", "@sage/xtrem-finance/pages__datev_export__acountsWithoutDatevIdSection____title": "Konten ohne DATEV-ID", "@sage/xtrem-finance/pages__datev_export__businessRelationsSection____title": "Extrahierte Kunden und Lieferanten", "@sage/xtrem-finance/pages__datev_export__createDatevExport____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevId____columns__title__id": "ID", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevId____columns__title__name": "Name", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevId____title": "Kunden ohne DATEV-ID", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevIdSection____title": "Kunden ohne DATEV-ID", "@sage/xtrem-finance/pages__datev_export__datevConsultantNumber____title": "DATEV-Beraternummer", "@sage/xtrem-finance/pages__datev_export__datevCustomerNumber____title": "DATEV-Kundennummer", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____columns__title__account__id": "ID", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____columns__title__datevId": "DATEV-ID", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____columns__title__name": "Name", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____title": "Extrahierte Konten", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__businessRelation__id": "ID", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__city": "Stadt", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__country__name": "Land", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__datevId": "DATEV-ID", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__name": "Name", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__postcode": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__street": "Straße", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__taxIdNumber": "Steuer-ID", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____title": "Extrahierte Kunden und Lieferanten", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__businessEntityTaxIdNumber": "Steuer-ID Geschäftsentität", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__companyFxRate": "Satz", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__companyValue": "Betrag in Unternehmenswährung", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__datevAccountId": "DATEV-Konto-ID", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__datevContraAccountId": "DATEV-Gegenkonto-ID", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__datevSign": "Vorzeichen", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__description": "Bezeichnung", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__dimension1": "Sektor 1", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__dimension2": "Sektor 2", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__locked": "Festschreibung", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__postingDate": "Buchungsdatum", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__postingKey": "Buchungsschlüssel", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__siteTaxIdNumber": "Steuer-ID Standort", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__supplierDocumentNumber": "Lieferantendokumentnummer", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__transactionValue": "Transaktionsbetrag", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____title": "Extrahierte Buchungszeilen", "@sage/xtrem-finance/pages__datev_export__dimensionTypeText1____title": "Dimension 1", "@sage/xtrem-finance/pages__datev_export__dimensionTypeText2____title": "Dimension 2", "@sage/xtrem-finance/pages__datev_export__doAccounts____title": "Konten exportieren", "@sage/xtrem-finance/pages__datev_export__doCustomersSuppliers____title": "Kunden und Lieferanten exportieren", "@sage/xtrem-finance/pages__datev_export__doJournalEntries____title": "Buchungen exportieren", "@sage/xtrem-finance/pages__datev_export__endDate____title": "Enddatum", "@sage/xtrem-finance/pages__datev_export__export____title": "Export", "@sage/xtrem-finance/pages__datev_export__extract____title": "Extraktion", "@sage/xtrem-finance/pages__datev_export__fiscalYearStart____title": "Geschäftsjahresbeginn", "@sage/xtrem-finance/pages__datev_export__headerSection____title": "Kopfberei<PERSON>", "@sage/xtrem-finance/pages__datev_export__id____title": "ID", "@sage/xtrem-finance/pages__datev_export__isLocked____title": "Festschreibung", "@sage/xtrem-finance/pages__datev_export__journalEntryLineSection____title": "Extrahierte Buchungszeilen", "@sage/xtrem-finance/pages__datev_export__notification_export_sent": "Anforderung DATEV-Export gesendet.", "@sage/xtrem-finance/pages__datev_export__notification_success": "Anforderung DATEV-Extraktion gesendet.", "@sage/xtrem-finance/pages__datev_export__skrCoaString____title": "SKR", "@sage/xtrem-finance/pages__datev_export__startDate____title": "Startdatum", "@sage/xtrem-finance/pages__datev_export__status____title": "Status", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevId____columns__title__id": "ID", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevId____columns__title__name": "Name", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevId____title": "Lieferanten ohne DATEV-ID", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevIdSection____title": "Lieferanten ohne DATEV-ID", "@sage/xtrem-finance/pages__datev_export__timeStamp____title": "Letztes Extraktionsdatum", "@sage/xtrem-finance/pages__datev_export_parameters__datev_configuration": "DATEV-Konfiguration", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_date_range": "Das Enddatum muss nach dem Startdatum liegen.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_dimension_types": "Die Dimension 2 muss eine andere als die Dimension 1 sein.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_end_date": "Das Enddatum muss vor dem Geschäftsjahresende liegen.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_end_for_begin_of_fiscal_year": "Das Enddatum muss nach dem Geschäftsjahresbeginn liegen.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_start_for_begin_of_fiscal_year": "Das Startdatum muss nach dem Geschäftsjahresbeginn liegen.", "@sage/xtrem-finance/pages__datev_export_settings____title": "Einstellungen DATEV-Export", "@sage/xtrem-finance/pages__datev_export_settings__company____columns__title__id": "ID", "@sage/xtrem-finance/pages__datev_export_settings__company____columns__title__name": "Name", "@sage/xtrem-finance/pages__datev_export_settings__criteriaBlock____title": "Kriterien", "@sage/xtrem-finance/pages__datev_export_settings__dateRange____title": "Datumsbereich", "@sage/xtrem-finance/pages__datev_export_settings__dimensionTypeOrAttribute1____title": "Dimension 1", "@sage/xtrem-finance/pages__datev_export_settings__dimensionTypeOrAttribute2____title": "Dimension 2", "@sage/xtrem-finance/pages__datev_export_settings__doAccounts____title": "Konten exportieren", "@sage/xtrem-finance/pages__datev_export_settings__doCustomersSuppliers____title": "Kunden und Lieferanten exportieren", "@sage/xtrem-finance/pages__datev_export_settings__doJournalEntries____title": "Buchungen exportieren", "@sage/xtrem-finance/pages__datev_export_settings__endDate____title": "Enddatum", "@sage/xtrem-finance/pages__datev_export_settings__fiscalYearStart____title": "Geschäftsjahresbeginn", "@sage/xtrem-finance/pages__datev_export_settings__id____title": "ID", "@sage/xtrem-finance/pages__datev_export_settings__isLocked____title": "Festschreibung", "@sage/xtrem-finance/pages__datev_export_settings__mainSection____title": "Allgemein", "@sage/xtrem-finance/pages__datev_export_settings__startDate____title": "Startdatum", "@sage/xtrem-finance/pages__generate_journal_entries____title": "Buchungen generieren", "@sage/xtrem-finance/pages__generate_journal_entries__create_button_text": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__generate_journal_entries__dateFrom____title": "Startdatum", "@sage/xtrem-finance/pages__generate_journal_entries__dateTo____title": "Enddatum", "@sage/xtrem-finance/pages__generate_journal_entries__documentType____title": "Dokumenttyp", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____lookupDialogTitle": "Buchhaltungsstandort auswählen", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____placeholder": "Auswählen ...", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__generate_journal_entries__generalBlock____title": "Kriterien", "@sage/xtrem-finance/pages__generate_journal_entries__generalSection____title": "Allgemein", "@sage/xtrem-finance/pages__generate_journal_entries__recurring": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__generate_journal_entries__run_once": "Einmalig ausführen", "@sage/xtrem-finance/pages__generate_journal_entries__schedule_button_text": "Planen", "@sage/xtrem-finance/pages__generate_pre_journal_entries____title": "Vorläufige Buchungen generieren", "@sage/xtrem-finance/pages__generate_pre_journal_entries__dateFrom____title": "Startdatum", "@sage/xtrem-finance/pages__generate_pre_journal_entries__dateTo____title": "Enddatum", "@sage/xtrem-finance/pages__generate_pre_journal_entries__documentType____title": "Dokumententyp", "@sage/xtrem-finance/pages__generate_pre_journal_entries__financialSite____columns__title__id": "ID", "@sage/xtrem-finance/pages__generate_pre_journal_entries__financialSite____columns__title__name": "Name", "@sage/xtrem-finance/pages__generate_pre_journal_entries__financialSite____placeholder": "Auswählen ...", "@sage/xtrem-finance/pages__generate_pre_journal_entries__financialSite____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__generate_pre_journal_entries__generalBlock____title": "Kriterien", "@sage/xtrem-finance/pages__generate_pre_journal_entries__generalSection____title": "Allgemein", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line_4__title": "Journal", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line_5__title": "Dokumenttyp", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line2Right__title": "Buchungsdatum", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line3__title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line4__title": "Journal", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line5__title": "Dokumenttyp", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line6__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line7__title": "Ursprung", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__titleRight__title": "Buchungsstatus", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__optionsMenu__title__3": "Gebucht", "@sage/xtrem-finance/pages__journal_entry____objectTypePlural": "Buchungen", "@sage/xtrem-finance/pages__journal_entry____objectTypeSingular": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry___id____title": "ID", "@sage/xtrem-finance/pages__journal_entry__accountingIntegrationBlock____title": "Integration Buchhaltung", "@sage/xtrem-finance/pages__journal_entry__description____title": "Bezeichnung", "@sage/xtrem-finance/pages__journal_entry__documentType____title": "Dokumenttyp", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationApp____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationAppRecordId____title": "Referenz Integration Buchhaltung", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationAppRecordIdLink____title": "Referenz Integration Buchhaltung", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationAppUrl____title": "URL Integration Finanzen", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationStatus____title": "Status Integration Buchhaltung", "@sage/xtrem-finance/pages__journal_entry__financialSite____columns__lookupDialogTitle__legalCompany__name": "Unternehmen auswählen", "@sage/xtrem-finance/pages__journal_entry__financialSite____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-finance/pages__journal_entry__financialSite____title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__journal_entry__generalSection____title": "Allgemein", "@sage/xtrem-finance/pages__journal_entry__internalFinanceIntegrationStatus____title": "Interner Status", "@sage/xtrem-finance/pages__journal_entry__journal____title": "Journal", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__account__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__account__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension01__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension01__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension02__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension02__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension03__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension03__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension04__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension04__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension05__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension05__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension06__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension06__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension07__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension07__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension08__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension08__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension09__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension09__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension10__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension10__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension11__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension11__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension12__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension12__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension13__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension13__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension14__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension14__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension15__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension15__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension16__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension16__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension17__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension17__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension18__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension18__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension19__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension19__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension20__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension20__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__employee__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__employee__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__project__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__project__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__task__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__task__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__account__composedDescription": "Ko<PERSON>", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__businessEntity__name": "Geschäftsentität", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__commonReference": "Allgemeine Referenz", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__companyAmount": "Unternehmensbetrag", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__companyCredit": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__companyDebit": "Soll Unternehmen", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__contraAccount": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__description": "Bezeichnung", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__dueDate": "Fälligkeitsdatum", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__financialSite__name": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__fxRateDate": "Wechselkursdatum", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__rateDescription": "Wechselkurs", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__tax__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__taxDate": "Steuerdatum", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionAmount": "Transaktionsbetrag", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionCredit": "Haben Transaktion", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionCurrency__id": "Transaktionswährung", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionDebit": "Soll Transaktion", "@sage/xtrem-finance/pages__journal_entry__lines____levels__dropdownActions__title": "Steuerdetails", "@sage/xtrem-finance/pages__journal_entry__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__origin____title": "Ursprung", "@sage/xtrem-finance/pages__journal_entry__postingDate____title": "Buchungsdatum", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title___id": "ID", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__documentNumber": "Dokumentnummer", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__documentSysId": "Dokument-ID", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__documentType": "Dokumenttyp", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__financeIntegrationAppRecordId": "Referenz Integration Buchhaltung", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__financeIntegrationAppRecordId__2": "Referenz Integration Buchhaltung", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__message": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-finance/pages__journal_entry__postingDetails____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__postingMessageBlock____title": "Fehlerdetails", "@sage/xtrem-finance/pages__journal_entry__postingSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__postingSectionBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry__postingStatus____title": "Buchungsstatus", "@sage/xtrem-finance/pages__journal_entry__reference____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry____title": "Buchungsabfrage", "@sage/xtrem-finance/pages__journal_entry_inquiry__companyFilter____lookupDialogTitle": "Unternehmen auswählen", "@sage/xtrem-finance/pages__journal_entry_inquiry__companyFilter____title": "Unternehmen", "@sage/xtrem-finance/pages__journal_entry_inquiry__dateFrom____title": "Startdatum", "@sage/xtrem-finance/pages__journal_entry_inquiry__dateTo____title": "Enddatum", "@sage/xtrem-finance/pages__journal_entry_inquiry__filtersBlock____title": "Kriterien", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__account__id": "Kontocode", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__account__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__blank": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__blank__2": "Zuordnungsdatum", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__businessEntity__id": "Code Geschäftsentität", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__businessEntity__name": "Name Geschäftsentität", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__commonReference": "Allgemeine Referenz", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__companyAmount": "Betrag", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__contraAccount": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__dueDate": "Fälligkeitsdatum", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__financialSite__id": "Standortcode", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__financialSite__legalCompany__name": "Unternehmensname", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__inquiryDescription": "Bezeichnung", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__inquiryTransactionCurrency__id": "Transaktionswährung", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__journal__id": "Journalcode", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__journal__name": "Journal-Name", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__postingDate": "Buchungsdatum", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__postingDate__2": "Datum Referenzdokument", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__numericSign": "Vorzeichen", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__signedTransactionAmount": "Transaktionsbetrag", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__tax__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__taxDate": "Steuerdatum", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__validationDate": "Freigabedatum", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalFilter____lookupDialogTitle": "Journale auswählen", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalFilter____title": "Journale", "@sage/xtrem-finance/pages__journal_entry_inquiry__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__amount__title": "Betrag", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__amountBankCurrency__title": "Betrag in Bankwährung", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__amountCompanyCurrency__title": "Betrag in Unternehmenswährung", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__bankAccount__title": "Bankkonto", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__bankCurrency__title": "Bankwährung", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__businessRelationId__title": "ID Geschäftsentität", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__companyCurrency__title": "Unternehmenswährung", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__currency__title": "Währung", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__financialSite__title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__isVoided__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__line2__title": "Name Geschäftsentität", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__line2Right__title": "Zahlungsdatum", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__paymentMethod__title": "Zahlungsart", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__reference__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__type__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__voidDate__title": "Storniert am", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__voidText__title": "Stornierungstext", "@sage/xtrem-finance/pages__payment____navigationPanel__optionsMenu__title": "Gebuchte Zahlungen", "@sage/xtrem-finance/pages__payment____navigationPanel__optionsMenu__title__2": "Stornierte Zahlungen", "@sage/xtrem-finance/pages__payment____navigationPanel__optionsMenu__title__3": "Alle Zahlungen", "@sage/xtrem-finance/pages__payment____objectTypePlural": "Zahlungsausgänge", "@sage/xtrem-finance/pages__payment____objectTypeSingular": "Zahlungsausgang", "@sage/xtrem-finance/pages__payment____title": "Zahlungsausgang", "@sage/xtrem-finance/pages__payment__cancel_void_button": "Abbrechen", "@sage/xtrem-finance/pages__payment__cancelPayment____title": "Abbrechen", "@sage/xtrem-finance/pages__payment__confirm_void_button": "Bestätigen", "@sage/xtrem-finance/pages__payment__createPayment____title": "Bestätigen", "@sage/xtrem-finance/pages__payment__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__payment__mainSection____title": "Allgemein", "@sage/xtrem-finance/pages__payment__paymentInformationBlock____title": "Zahlungsinformationen", "@sage/xtrem-finance/pages__payment__voidPayment____title": "Stornieren", "@sage/xtrem-finance/pages__payment__voidPaymentDate____helperText": "Die Zahlung am Datum stornieren", "@sage/xtrem-finance/pages__payment__voidPaymentDate____title": "Datum", "@sage/xtrem-finance/pages__payment__voidPaymentText____title": "Text", "@sage/xtrem-finance/pages__payment__voidSection____title": "Zahlung stornieren", "@sage/xtrem-finance/pages__pre_journal____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-finance/pages__pre_journal____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal____navigationPanel__optionsMenu__title__3": "Gebucht", "@sage/xtrem-finance/pages__pre_journal____subtitle": "Vorläufige Buchung", "@sage/xtrem-finance/pages__pre_journal____title": "Vorläufige Buchung", "@sage/xtrem-finance/pages__pre_journal__description____title": "Bezeichnung", "@sage/xtrem-finance/pages__pre_journal__documentType____title": "Dokumententyp", "@sage/xtrem-finance/pages__pre_journal__financialSite____columns__columns__legalCompany__name__title": "Rechtsordnung", "@sage/xtrem-finance/pages__pre_journal__financialSite____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-finance/pages__pre_journal__financialSite____title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__pre_journal__generalSection____title": "Allgemein", "@sage/xtrem-finance/pages__pre_journal__journal____title": "Journal", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__account__composedDescription__title": "ID", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__account__composedDescription__title__2": "Name", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__account__composedDescription__title__3": "Zusammengesetzte Bezeichnung", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title": "Name", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__2": "ISO 4217-Code", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__3": "Symbol", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__4": "Dezimalstellen", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__5": "Name", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__6": "ID", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__7": "Symbol", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__companyCurrency__id__title__8": "Dezimalstellen", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title": "Name", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__2": "ISO 4217-Code", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__3": "Symbol", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__4": "Dezimalstellen", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__5": "Name", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__6": "ID", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__7": "Symbol", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__columns__transactionCurrency__id__title__8": "Dezimalstellen", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__account__composedDescription": "Ko<PERSON>", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__businessEntity__name": "Geschäftsentität", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__commonReference": "Allgemeine Referenz", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyAmount": "Unternehmensbetrag", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyCredit": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyCurrency__id": "Unternehmenswährung", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyCurrency__id__2": "Unternehmenswährung", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__companyDebit": "Soll Unternehmen", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__description": "Bezeichnung", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__dueDate": "Fälligkeitsdatum", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__financialSite__name": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__fxRateDate": "Wechselkursdatum", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__rateDescription": "Wechselkurs", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__tax__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__taxDate": "Steuerdatum", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionAmount": "Transaktionsbetrag", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionCredit": "Haben Transaktion", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionCurrency__id": "Transaktionswährung", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionCurrency__id__2": "Transaktionswährung", "@sage/xtrem-finance/pages__pre_journal__lines____levels__columns__title__transactionDebit": "Soll Transaktion", "@sage/xtrem-finance/pages__pre_journal__linesBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__origin____title": "Ursprung", "@sage/xtrem-finance/pages__pre_journal__postingDate____title": "Buchungsdatum", "@sage/xtrem-finance/pages__pre_journal__reference____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__pre_journal__status____title": "Status", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__amount__title": "Betrag", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__amountBankCurrency__title": "Betrag in Bankwährung", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__amountCompanyCurrency__title": "Betrag in Unternehmenswährung", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__bankAccount__title": "Bankkonto", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__bankCurrency__title": "Bankwährung", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__businessRelationId__title": "ID Geschäftsentität", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__companyCurrency__title": "Unternehmenswährung", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__currency__title": "Währung", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__financialSite__title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__isVoided__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__line2__title": "Name Geschäftsentität", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__line2Right__title": "Zahlungsdatum", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__paymentMethod__title": "Zahlungsart", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__reference__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__type__title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__voidDate__title": "Storniert am", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__voidText__title": "Stornierungstext", "@sage/xtrem-finance/pages__receipt____navigationPanel__optionsMenu__title": "Gebuchte Eingänge", "@sage/xtrem-finance/pages__receipt____navigationPanel__optionsMenu__title__2": "Stornierte Eingänge", "@sage/xtrem-finance/pages__receipt____navigationPanel__optionsMenu__title__3": "Alle Eingänge", "@sage/xtrem-finance/pages__receipt____objectTypePlural": "Zahlungseingänge", "@sage/xtrem-finance/pages__receipt____objectTypeSingular": "Zahlungseingang", "@sage/xtrem-finance/pages__receipt____title": "Zahlungseingang", "@sage/xtrem-finance/pages__receipt__amount____title": "Zahlungsbetrag", "@sage/xtrem-finance/pages__receipt__amountBankCurrency____title": "Betrag in Bankwährung", "@sage/xtrem-finance/pages__receipt__bankAccount____title": "Bankkonto", "@sage/xtrem-finance/pages__receipt__cancel_void": "Abbrechen", "@sage/xtrem-finance/pages__receipt__cancelReceipt____title": "Abbrechen", "@sage/xtrem-finance/pages__receipt__confirm_void": "Bestätigen", "@sage/xtrem-finance/pages__receipt__createReceipt____title": "Bestätigen", "@sage/xtrem-finance/pages__receipt__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-finance/pages__receipt__currency____title": "Währung", "@sage/xtrem-finance/pages__receipt__customer____title": "Kunde", "@sage/xtrem-finance/pages__receipt__financialSite____title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__receipt__isVoided____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__lines____columns__title__apOpenItem__accountsPayableInvoice__purchaseDocumentNumber": "Dokumentnummer", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__accountsReceivableInvoice__postingDate": "Buchungsdatum", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__accountsReceivableInvoice__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__accountsReceivableInvoice__salesDocumentNumber": "Dokumentnummer", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__dueDate": "Fälligkeitsdatum", "@sage/xtrem-finance/pages__receipt__lines____columns__title__currency__id": "Währung", "@sage/xtrem-finance/pages__receipt__lines____columns__title__origin": "Ursprung", "@sage/xtrem-finance/pages__receipt__lines____columns__title__signedAmount": "Betrag", "@sage/xtrem-finance/pages__receipt__lines____columns__title__signedAmountBankCurrency": "Betrag in Bankwährung", "@sage/xtrem-finance/pages__receipt__lines____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__mainSection____title": "Allgemein", "@sage/xtrem-finance/pages__receipt__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__paymentDate____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__paymentInformationBlock____title": "Zahlungsinformationen", "@sage/xtrem-finance/pages__receipt__paymentMethod____title": "Zahlungsart", "@sage/xtrem-finance/pages__receipt__reference____title": "Transaktionsinformationen", "@sage/xtrem-finance/pages__receipt__supplier____title": "Lieferant", "@sage/xtrem-finance/pages__receipt__voidPaymentDate____helperText": "Die Zahlung am Datum stornieren", "@sage/xtrem-finance/pages__receipt__voidPaymentDate____title": "Datum", "@sage/xtrem-finance/pages__receipt__voidPaymentText____title": "Text", "@sage/xtrem-finance/pages__receipt__voidReceipt____title": "Stornieren", "@sage/xtrem-finance/pages__receipt__voidSection____title": "Eingang stornieren", "@sage/xtrem-finance/pages__record_payment____title": "Erfassung Zahlungsausgänge", "@sage/xtrem-finance/pages__record_payment__amount____title": "Zahlungsbetrag", "@sage/xtrem-finance/pages__record_payment__amount_in_bank_currency_must_be_positive": "Der Betrag in Bankwährung muss größer als 0 sein.", "@sage/xtrem-finance/pages__record_payment__amountAvailableToApply____title": "<PERSON><PERSON> verfügbar", "@sage/xtrem-finance/pages__record_payment__amountBankCurrency____title": "Betrag in Bankwährung", "@sage/xtrem-finance/pages__record_payment__bankAccount____title": "Bankkonto", "@sage/xtrem-finance/pages__record_payment__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-finance/pages__record_payment__currency____title": "Währung", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__country__name": "Land", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__id": "ID", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__name": "Name", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__taxIdNumber": "Steuer-ID", "@sage/xtrem-finance/pages__record_payment__customer____title": "Kunde", "@sage/xtrem-finance/pages__record_payment__date____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__financialSite____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-finance/pages__record_payment__financialSite____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-finance/pages__record_payment__financialSite____placeholder": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__record_payment__financialSite____title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__record_payment__generateButton____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__number": "Rechnungsnummer", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__origin": "Ursprung", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__postingDate": "Buchungsdatum", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__amountDue": "<PERSON><PERSON><PERSON><PERSON> Betrag", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__creditAmount": "Gutschriftbetrag", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__currency__id": "Währung", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__dueDate": "Fälligkeitsdatum", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__paymentAmount": "Zahlungsbetrag", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__remainingCompanyAmount": "Unternehmensbetrag fällig", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__totalAmount": "Gesamtbetrag", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__totalCompanyAmount": "Unternehmensbetrag gesamt", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__totalCompanyAmountPaid": "Unternehmensbetrag bezahlt", "@sage/xtrem-finance/pages__record_payment__lines____title": "Ergebnisse", "@sage/xtrem-finance/pages__record_payment__linesBlock____title": "Ergebnisse", "@sage/xtrem-finance/pages__record_payment__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__mainSection____title": "Allgemein", "@sage/xtrem-finance/pages__record_payment__payment_amount_can_not_be_negative": "Der Zahlungsbetrag muss größer als oder gleich 0 sein.", "@sage/xtrem-finance/pages__record_payment__payment_created": "Zahlung erstellt: {{paymentNumber}}.", "@sage/xtrem-finance/pages__record_payment__paymentInformationBlock____title": "Zahlungsinformationen", "@sage/xtrem-finance/pages__record_payment__paymentMethod____title": "Zahlungsart", "@sage/xtrem-finance/pages__record_payment__reference____title": "Transaktionsinformationen", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__country__name": "Land", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__id": "ID", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__name": "Name", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__taxIdNumber": "Steuer-ID", "@sage/xtrem-finance/pages__record_payment__supplier____title": "Lieferant", "@sage/xtrem-finance/pages__record_payment__totalPaymentApplied____title": "Zahlung gesamt zugeordnet", "@sage/xtrem-finance/pages__record_payment__transaction_information_can_not_exceed_100_characters": "Die Transaktionsinformationen dürfen nicht mehr als 100 Zeichen enthalten.", "@sage/xtrem-finance/pages__record_payment__type____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt____title": "Erfassung Zahlungseingänge", "@sage/xtrem-finance/pages__record_receipt__amount____title": "Zahlungsbetrag", "@sage/xtrem-finance/pages__record_receipt__amount_in_bank_currency_must_be_positive": "Der Betrag in Bankwährung muss größer als 0 sein.", "@sage/xtrem-finance/pages__record_receipt__amountAvailableToApply____title": "<PERSON><PERSON> verfügbar", "@sage/xtrem-finance/pages__record_receipt__amountBankCurrency____title": "Betrag in Bankwährung", "@sage/xtrem-finance/pages__record_receipt__bank_amount_must_be_positive": "Erfassen Sie einen Betrag in Bankwährung größer als 0.", "@sage/xtrem-finance/pages__record_receipt__bankAccount____title": "Bankkonto", "@sage/xtrem-finance/pages__record_receipt__currency____lookupDialogTitle": "Währung auswählen", "@sage/xtrem-finance/pages__record_receipt__currency____title": "Währung", "@sage/xtrem-finance/pages__record_receipt__customer____columns__title__country__name": "Land", "@sage/xtrem-finance/pages__record_receipt__customer____columns__title__id": "ID", "@sage/xtrem-finance/pages__record_receipt__customer____columns__title__name": "Name", "@sage/xtrem-finance/pages__record_receipt__customer____columns__title__taxIdNumber": "Steuer-ID", "@sage/xtrem-finance/pages__record_receipt__customer____title": "Kunde", "@sage/xtrem-finance/pages__record_receipt__date____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt__financialSite____columns__title__legalCompany__name": "Unternehmen", "@sage/xtrem-finance/pages__record_receipt__financialSite____lookupDialogTitle": "Standort auswählen", "@sage/xtrem-finance/pages__record_receipt__financialSite____placeholder": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__record_receipt__financialSite____title": "Buchhaltungsstandort", "@sage/xtrem-finance/pages__record_receipt__generateButton____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__accountsReceivableInvoice__number": "Rechnungsnummer", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__accountsReceivableInvoice__origin": "Ursprung", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__accountsReceivableInvoice__postingDate": "Buchungsdatum", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__accountsReceivableInvoice__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__amountDue": "<PERSON><PERSON><PERSON><PERSON> Betrag", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__creditAmount": "Gutschriftbetrag", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__currency__id": "Währung", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__dueDate": "Fälligkeitsdatum", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__paymentAmount": "Zahlungsbetrag", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__remainingCompanyAmount": "Unternehmensbetrag fällig", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__totalAmount": "Gesamtbetrag", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__totalCompanyAmount": "Unternehmensbetrag gesamt", "@sage/xtrem-finance/pages__record_receipt__lines____columns__title__totalCompanyAmountPaid": "Unternehmensbetrag bezahlt", "@sage/xtrem-finance/pages__record_receipt__lines____title": "Ergebnisse", "@sage/xtrem-finance/pages__record_receipt__linesBlock____title": "Ergebnisse", "@sage/xtrem-finance/pages__record_receipt__linesSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_receipt__mainSection____title": "Allgemein", "@sage/xtrem-finance/pages__record_receipt__payment_amount_can_not_be_negative": "Der Zahlungsbetrag muss größer als oder gleich 0 sein.", "@sage/xtrem-finance/pages__record_receipt__payment_amount_must_be_positive": "Erfassen Sie einen Zahlungsbetrag größer als 0.", "@sage/xtrem-finance/pages__record_receipt__payment_amount_must_be_total_payment_applied": "Der Zahlungsbetrag muss der gesamt zugeordneten Zahlung entsprechen.", "@sage/xtrem-finance/pages__record_receipt__paymentInformationBlock____title": "Zahlungsinformationen", "@sage/xtrem-finance/pages__record_receipt__paymentMethod____title": "Zahlungsart", "@sage/xtrem-finance/pages__record_receipt__receipt_created": "Der folgende Eingang wurde erstellt: {{receiptNumber}}.", "@sage/xtrem-finance/pages__record_receipt__reference____title": "Transaktionsinformationen", "@sage/xtrem-finance/pages__record_receipt__supplier____columns__title__country__name": "Land", "@sage/xtrem-finance/pages__record_receipt__supplier____columns__title__id": "ID", "@sage/xtrem-finance/pages__record_receipt__supplier____columns__title__name": "Name", "@sage/xtrem-finance/pages__record_receipt__supplier____columns__title__taxIdNumber": "Steuer-ID", "@sage/xtrem-finance/pages__record_receipt__supplier____title": "Lieferant", "@sage/xtrem-finance/pages__record_receipt__totalPaymentApplied____title": "Zahlung gesamt zugeordnet", "@sage/xtrem-finance/pages__record_receipt__transaction_information_can_not_exceed_100_characters": "Die Transaktionsinformationen dürfen nicht mehr als 100 Zeichen enthalten.", "@sage/xtrem-finance/pages__record_receipt__type____title": "<PERSON><PERSON>", "@sage/xtrem-finance/pages__unbilled_account_payable_inquiry__companyReference____columns__title__description": "Bezeichnung", "@sage/xtrem-finance/pages__unbilled_account_payable_inquiry__fromSupplier____helperText": "ID", "@sage/xtrem-finance/pages__unbilled_account_payable_inquiry__toSupplier____helperText": "ID", "@sage/xtrem-finance/permission__accounting_integration__name": "Integration Buchhaltung", "@sage/xtrem-finance/permission__create_journals_from_accounting_staging__name": "Journale aus Staging Buchhaltung erstellen", "@sage/xtrem-finance/permission__create_journals_from_accounting_staging_job__name": "Journale aus Staging-Job Buchhaltung erstellen", "@sage/xtrem-finance/permission__initialize_paid_amount__name": "Initialisierung bezahlter Betrag", "@sage/xtrem-finance/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/permission__post__name": "Buchen", "@sage/xtrem-finance/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-finance/permission__retry_finance_document__name": "Finanzdokument wiederholen", "@sage/xtrem-finance/permission__void__name": "Stornieren", "@sage/xtrem-finance/search": "<PERSON><PERSON>", "@sage/xtrem-finance/source_document_type_not_supported": "Der Ursprungsdokumenttyp wird nicht unterstützt: {{documentType}}", "@sage/xtrem-finance/status_not_supported": "Status nicht unterstützt: {{status}}.", "@sage/xtrem-finance/success_notification__bulk_open_item_payment_title_error": "Zahlung offener Posten erzwingen", "@sage/xtrem-finance/success_notification__bulk_open_item_payment_title_success": "Zahlung offener Posten erzwingen", "@sage/xtrem-finance/success_notification_description__ar_open_items_link": "<PERSON><PERSON>", "@sage/xtrem-finance/sys__notification_history__search": "<PERSON><PERSON>", "@sage/xtrem-finance/target_document_not_found": "{{sysId}} {{type}}: Zieldokument nicht gefunden.", "@sage/xtrem-finance/target_document_type_not_supported": "{{targetDocumentType}}: <PERSON> Zieldokumenttyp wird nicht unterstützt.", "@sage/xtrem-finance/unexpected_error": "<PERSON><PERSON><PERSON><PERSON>: {{retryFinanceDocumentResult}}", "@sage/xtrem-finance/widgets__finance_integration_health____callToActions__seeAll__title": "Alles anzeigen", "@sage/xtrem-finance/widgets__finance_integration_health____dataDropdownMenu__orderBy__documentNumber__title": "Nach Dokumentnummer sortieren", "@sage/xtrem-finance/widgets__finance_integration_health____dataDropdownMenu__orderBy__status__title": "Nach Status sortieren", "@sage/xtrem-finance/widgets__finance_integration_health____dataDropdownMenu__orderBy__updateStamp__title": "Nach letzter Statusaktualisierung sortieren", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__line2__title": "Dokumenttyp", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__line2Right__title": "Letzte Statusaktualisierung", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__line3__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__targetNumber__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__targetType__title": "<PERSON><PERSON>", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__title__title": "Status", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__titleRight__title": "Dokumentnummer", "@sage/xtrem-finance/widgets__finance_integration_health____title": "Status Finanztransaktion"}