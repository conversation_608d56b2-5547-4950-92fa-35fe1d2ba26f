import type { Context, ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinance from '../index';

type FinanceDocumentType =
    | xtremFinance.nodes.AccountsPayableInvoice
    | xtremFinance.nodes.AccountsReceivableInvoice
    | xtremFinance.nodes.AccountsReceivableAdvance
    | xtremFinance.nodes.JournalEntry;

export async function computeAttributes(
    context: Context,
    site: xtremSystem.nodes.Site,
    item?: xtremMasterData.nodes.Item,
    customer?: xtremMasterData.nodes.Customer,
    supplier?: xtremMasterData.nodes.Supplier,
): Promise<xtremFinanceData.interfaces.ComputedAttributes> {
    const result = await xtremFinanceData.functions.computeGenericAttributes(context, {
        financialSite: site,
        item,
    });

    result.businessSite = await xtremFinanceData.functions.checkAttributeTypeActive(
        context,
        xtremFinanceData.nodes.AttributeType,
        'businessSite',
        await site.id,
    );
    if (customer) {
        result.customer = await xtremFinanceData.functions.checkAttributeTypeActive(
            context,
            xtremFinanceData.nodes.AttributeType,
            'customer',
            await (
                await customer.businessEntity
            ).id,
        );
    }
    if (supplier) {
        result.customer = await xtremFinanceData.functions.checkAttributeTypeActive(
            context,
            xtremFinanceData.nodes.AttributeType,
            'supplier',
            await (
                await supplier.businessEntity
            ).id,
        );
    }

    return result;
}

export async function controlMandatoryAttributesAndDimensions(
    context: Context,
    validationContext: ValidationContext,
    financeDocument: FinanceDocumentType,
) {
    // do the control only if the document is added or modified and not posted before the change
    if (
        financeDocument.$.status === NodeStatus.added ||
        (financeDocument.$.status === NodeStatus.modified &&
            (await (await financeDocument.$.old).postingStatus) !== 'posted')
    ) {
        let lineCount = 0;
        // Mandatory company dimensions control
        const mandatoryCompanyAttributes = await context
            .query(xtremFinanceData.nodes.CompanyAttributeType, {
                filter: { company: (await (await financeDocument.financialSite).legalCompany)._id, isRequired: true },
            })
            .toArray();

        const mandatoryCompanyDimensions = await context
            .query(xtremFinanceData.nodes.CompanyDimensionType, {
                filter: { company: (await (await financeDocument.financialSite).legalCompany)._id, isRequired: true },
            })
            .toArray();

        // Mandatory account dimensions control
        await financeDocument.lines.forEach(async line => {
            lineCount += 1;
            const lineAccount = await (await line.account)?.id;
            const lineAccountSysId = (await line.account)?._id;
            if (lineAccount) {
                const mandatoryAccountAttribute = await context
                    .query(xtremFinanceData.nodes.AccountAttributeType, {
                        filter: { account: lineAccountSysId, isRequired: true },
                    })
                    .toArray();

                const mandatoryAccountDimension = await context
                    .query(xtremFinanceData.nodes.AccountDimensionType, {
                        filter: { account: lineAccountSysId, isRequired: true },
                    })
                    .toArray();

                const attributesAndDimensionLines =
                    financeDocument instanceof xtremFinance.nodes.AccountsReceivableAdvance
                        ? financeDocument.lines
                        : (
                              line as
                                  | xtremFinance.nodes.JournalEntryLine
                                  | xtremFinance.nodes.AccountsPayableInvoiceLine
                                  | xtremFinance.nodes.AccountsReceivableInvoiceLine
                          ).attributesAndDimensions;

                await attributesAndDimensionLines.forEach(async dimensionLine => {
                    // Attribute and dimensions check - company level
                    const undefinedCompanyAttributes = await xtremFinanceData.functions.mandatoryAttributeControl(
                        (await dimensionLine.storedAttributes) || {},
                        mandatoryCompanyAttributes,
                    );
                    const undefinedCompanyDimensions = await xtremFinanceData.functions.mandatoryDimensionControl(
                        (await dimensionLine.storedDimensions) || {},
                        mandatoryCompanyDimensions,
                    );
                    const undefinedCompanyAttributeDimension = [
                        ...undefinedCompanyAttributes,
                        ...undefinedCompanyDimensions,
                    ];

                    const arrayCompanyAsString = undefinedCompanyAttributeDimension.map(String).join(', ');
                    if (undefinedCompanyAttributeDimension.length > 0) {
                        validationContext.error.addLocalized(
                            '@sage/xtrem-finance/nodes__journal_entry__no_attribute_value_company',
                            'Required company dimensions [{{dimensions}}] have not been declared for account {{lineAccount}} on journal line {{lineCount}}.',
                            {
                                dimensions: arrayCompanyAsString,
                                lineAccount,
                                lineCount,
                            },
                        );
                    }

                    // Attribute and dimensions check - account level
                    const undefinedAccountAttributes = await xtremFinanceData.functions.mandatoryAttributeControl(
                        (await dimensionLine.storedAttributes) || {},
                        mandatoryAccountAttribute,
                    );
                    const undefinedAccountDimensions = await xtremFinanceData.functions.mandatoryDimensionControl(
                        (await dimensionLine.storedDimensions) || {},
                        mandatoryAccountDimension,
                    );
                    const undefinedAccountAttributeDimension = [
                        ...undefinedAccountAttributes,
                        ...undefinedAccountDimensions,
                    ];

                    const arrayAccountAsString = undefinedAccountAttributeDimension.map(String).join(', ');
                    if (undefinedAccountAttributeDimension.length > 0) {
                        validationContext.error.addLocalized(
                            '@sage/xtrem-finance/nodes__journal_entry__no_attribute_value_account',
                            'Required account dimensions [{{dimensions}}] have not been declared for account {{lineAccount}} on journal line {{lineCount}}.',
                            {
                                dimensions: arrayAccountAsString,
                                lineCount,
                                lineAccount,
                            },
                        );
                    }
                });

                // Control that the journal line value equals the value acclocated to attributes and dumensnions
                if (financeDocument instanceof xtremFinance.nodes.JournalEntry) {
                    const totalDimensionLineValue = await (
                        line as xtremFinance.nodes.JournalEntryLine
                    ).attributesAndDimensions.sum(
                        async current => (await current.companyAmount) + (await current.transactionAmount),
                    );
                    if (totalDimensionLineValue !== 0) {
                        if (
                            totalDimensionLineValue !==
                            (await (line as xtremFinance.nodes.JournalEntryLine).companyAmount) +
                                (await (line as xtremFinance.nodes.JournalEntryLine).transactionAmount)
                        ) {
                            validationContext.error.addLocalized(
                                '@sage/xtrem-finance/nodes__journal_entry__journal_entry_dimension_line_not_equal_to_journal_line',
                                "The total attribute/dimension allocation amount isn't equal to the line amount",
                            );
                        }
                    }
                }
            }
        });
    }
}
