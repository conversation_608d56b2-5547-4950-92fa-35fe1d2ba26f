import { registerSqlFunction } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';

export function signedAmount(document: {
    isReceipt: boolean;
    isSupplier: boolean;
    origin: xtremFinanceData.enums.AccountsPayableReceivableInvoiceOrigin;
    amount: number;
}): number {
    if ((document.isSupplier && document.isReceipt) || (!document.isSupplier && !document.isReceipt)) {
        return document.origin === 'creditMemo' ? document.amount : document.amount * -1;
    }
    return document.origin === 'invoice' ? document.amount : document.amount * -1;
}

registerSqlFunction('xtremFinance.functions.signedAmount', signedAmount);
