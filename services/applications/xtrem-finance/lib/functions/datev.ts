import type { Context, NodeCreateData, NodePayloadData, NodeUpdateData } from '@sage/xtrem-core';
import { datetime, Logger } from '@sage/xtrem-core';
import { FileTimeToLive } from '@sage/xtrem-file-storage';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremInfrastructureAdapter from '@sage/xtrem-infrastructure-adapter';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type { InitialNotificationAction, integer } from '@sage/xtrem-shared';
import * as xtremUpload from '@sage/xtrem-upload';
import * as xtremFinance from '..';

const logger = Logger.getLogger(__filename, 'datev-export');

// at the moment, this function is only used for the user notifications related with the extraction,
// not the export
export async function sendDatevUserNotification(parameters: {
    context: Context;
    datevExportId: string;
    success: boolean;
    msgDescription: string;
}) {
    const actions: InitialNotificationAction[] = [
        {
            link: parameters.context.batch.notificationStateLink,
            title: 'History',
            icon: 'link',
            style: 'tertiary',
        },
    ];
    if (parameters.datevExportId) {
        actions.push({
            link: `@sage/xtrem-finance/DatevExport/${parameters.datevExportId}`,
            title: 'Results',
            icon: 'link',
            style: 'tertiary',
        });
    }
    await parameters.context.notifyUser({
        title: parameters.context.localize(
            '@sage/xtrem-finance/nodes__datev_export__success_notification_title',
            'DATEV extraction complete',
        ),
        description: parameters.msgDescription,
        icon: parameters.success ? 'tick' : 'error',
        level: parameters.success ? 'success' : 'error',
        shouldDisplayToast: true,
        actions,
    });
}

export async function datevExportRecordUpdate(parameters: {
    context: Context;
    datevExportId: string;
    data: NodeUpdateData<xtremFinance.nodes.DatevExport>;
}) {
    const datevExportUpdate = await parameters.context.read(
        xtremFinance.nodes.DatevExport,
        { id: parameters.datevExportId },
        { forUpdate: true },
    );
    logger.verbose(() => `DatevExportRecord - update ${JSON.stringify(parameters.data)}`);

    await datevExportUpdate.$.set(parameters.data);
    await datevExportUpdate.$.save();
}

export async function stopRequested(context: Context, datevId: string) {
    logger.debug(() => `stopRequested - check DATEV id ${datevId || ''}`);
    if ((await context.batch.isStopRequested()) || context.isAborted) {
        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-finance/nodes__datev_export__stop_extraction',
                'Stop DATEV extraction on {{stopDate}}.',
                {
                    stopDate: datetime.now(),
                },
            ),
        );

        await context.runInIsolatedContext(async newContext => {
            await datevExportRecordUpdate({
                context: newContext,
                datevExportId: datevId,
                data: { status: 'error', timeStamp: datetime.now() },
            });
        });

        await context.batch.confirmStop();
        return true;
    }
    return false;
}

/**
 * Executes the journal entry line controls for every datev journal entry line.
 * Writes the warnings on the log. Returns the number of warnings.
 * @param context Context
 * @param journalEntryLine JournalEntryLine from the DatevJournalEntryLine
 * @return number or warnings
 */
async function journalEntryLineControls(
    context: Context,
    journalEntryLine: xtremFinance.nodes.JournalEntryLine,
): Promise<integer> {
    let warnings = 0;
    const account = await journalEntryLine.account;
    if ((await account.isAutomaticAccount) && (await account.tax)?._id !== (await journalEntryLine.tax)?._id) {
        await xtremFinance.functions.datevWarningLogs.logJournalEntryLineWithDifferentTax(context, journalEntryLine);
        warnings += 1;
    }
    if (
        !(await account.isAutomaticAccount) &&
        !(await journalEntryLine.datevPostingKey) &&
        ((await account.taxManagement) !== 'other' ||
            (await (await (await journalEntryLine.contraJournalEntryLine)?.account)?.taxManagement) !== 'other')
    ) {
        await xtremFinance.functions.datevWarningLogs.logJournalEntryLineWithoutPostingKey(context, journalEntryLine);
        warnings += 1;
    }
    if (!(await account.datevId)) {
        await xtremFinance.functions.datevWarningLogs.logJournalEntryLineWithoutAccountDatevId(
            context,
            journalEntryLine,
        );
        warnings += 1;
    }
    if (!(await journalEntryLine.datevContraAccountId)) {
        await xtremFinance.functions.datevWarningLogs.logJournalEntryLineWithoutDatevContraAccountId(
            context,
            journalEntryLine,
        );
        warnings += 1;
    }
    if (
        (await account.taxManagement) === 'excludingTax' &&
        (await journalEntryLine.taxRate) !== 0 &&
        !(await (
            await journalEntryLine.journalEntry
        ).lines.some(
            async line =>
                line._id !== journalEntryLine._id &&
                (await line.baseTax)?._id === (await journalEntryLine.baseTax)?._id,
        ))
    ) {
        await xtremFinance.functions.datevWarningLogs.logJournalEntryLineWithoutTaxLineForTaxableAmount(
            context,
            journalEntryLine,
        );
        warnings += 1;
    }
    return warnings;
}

// -------------------------------------------------------- //
// ------------        DATA EXTRACTION         ------------ //
// -------------------------------------------------------- //
export async function executeAccountExtraction(
    context: Context,
    datevExport: xtremFinance.nodes.DatevExport,
): Promise<NodeCreateData<xtremFinance.nodes.DatevExportAccount>[]> {
    if (await datevExport.doAccounts) {
        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-finance/nodes__datev_export__start_extract_accounts',
                'DATEV account extraction start.',
            ),
        );
        logger.verbose(() => 'Start of DATEV account extraction.');

        const accounts = context.query(xtremFinanceData.nodes.Account, {
            filter: {
                chartOfAccount: { legislation: { id: 'DE' } },
                isActive: true,
            },
        });
        const datevExportAccounts: NodeCreateData<xtremFinance.nodes.DatevExportAccount>[] = [];
        await accounts.every(async account => {
            if (await stopRequested(context, await datevExport.id)) {
                return false;
            }
            const datevId = (await account.datevId) ?? 0;
            datevExportAccounts.push({
                account,
                datevId,
                name: await account.name,
            });
            if (!datevId) {
                await xtremFinance.functions.datevWarningLogs.logAccountWithoutDatevId(context, account);
            }
            return true;
        });
        const numberExtractedAccounts = await accounts.length;
        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-finance/nodes__datev_export__number_extracted_accounts',
                'DATEV accounts extracted: {{numberExtractedAccounts}}.',
                { numberExtractedAccounts },
            ),
        );
        logger.verbose(() => `DATEV accounts extracted:  ${numberExtractedAccounts.toString()}`);

        return datevExportAccounts;
    }
    return [];
}

export async function executeBusinessRelationExtraction(
    context: Context,
    datevExport: xtremFinance.nodes.DatevExport,
): Promise<NodeCreateData<xtremFinance.nodes.DatevExportBusinessRelation>[]> {
    if (await datevExport.doCustomersSuppliers) {
        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-finance/nodes__datev_export__start_extract_customers_suppliers',
                'DATEV customer and supplier extraction start.',
            ),
        );
        logger.verbose(() => 'Start of DATEV customer and supplier extraction.');

        const businessRelations = context.query(xtremMasterData.nodes.BaseBusinessRelation, {
            filter: {
                isActive: true,
            },
        });
        const datevExportBusinessRelations: NodeCreateData<xtremFinance.nodes.DatevExportBusinessRelation>[] = [];
        await businessRelations.every(async businessRelation => {
            if (await stopRequested(context, await datevExport.id)) {
                return false;
            }

            const primaryAddress = await businessRelation.primaryAddress;
            const datevId = (await businessRelation.datevId) ?? 0;

            datevExportBusinessRelations.push({
                businessRelation,
                datevId,
                name: await (await businessRelation.businessEntity).name,
                country: await primaryAddress.country,
                taxIdNumber: (await (
                    await (
                        await businessRelation.businessEntity
                    ).country
                ).isEuMember)
                    ? await (
                          await businessRelation.businessEntity
                      ).taxIdNumber
                    : undefined,
                street: await primaryAddress.addressLine1,
                city: await primaryAddress.city,
                postcode: await primaryAddress.postcode,
            });
            if (!datevId) {
                await xtremFinance.functions.datevWarningLogs.logBusinessEntityWithoutDatevId(
                    context,
                    await businessRelation.businessEntity,
                );
            }
            return true;
        });

        const numberExtractedBusinessRelations = await businessRelations.length;
        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-finance/nodes__datev_export__number_extracted_customers_suppliers',
                'DATEV customers and suppliers extracted: {{numberExtractedBusinessRelations}}.',
                { numberExtractedBusinessRelations },
            ),
        );
        logger.verbose(
            () => `DATEV customers and suppliers extracted:  ${numberExtractedBusinessRelations.toString()}`,
        );

        return datevExportBusinessRelations;
    }
    return [];
}

async function getDimensionId(
    journalEntryLine: xtremFinance.nodes.JournalEntryLine,
    datevDimensionType: xtremFinanceData.nodes.DimensionType,
): Promise<string> {
    const journalEntryLinePropertyName = ((await datevDimensionType?.docProperty)?.replace('Type', '') ??
        '') as keyof typeof journalEntryLine;

    return (
        (await ((await journalEntryLine[journalEntryLinePropertyName]) as xtremFinanceData.nodes.Dimension | null)
            ?.id) ?? ''
    );
}

async function getAttributeId(
    journalEntryLine: xtremFinance.nodes.JournalEntryLine,
    datevAttributeTypeId: string,
): Promise<string> {
    if (datevAttributeTypeId === '') return '';
    const journalEntryLinePropertyName = datevAttributeTypeId.concat('Attribute') as keyof typeof journalEntryLine;

    return (
        (await ((await journalEntryLine[journalEntryLinePropertyName]) as xtremFinanceData.nodes.Attribute | null)
            ?.id) ?? ''
    );
}

async function getDimensions(
    journalEntryLine: xtremFinance.nodes.JournalEntryLine,
    datevExport: xtremFinance.nodes.DatevExport,
): Promise<{ dimension1: string; dimension2: string }> {
    let dimensionType = await datevExport.dimensionType1;
    const dimension1 =
        dimensionType !== null
            ? await getDimensionId(journalEntryLine, dimensionType)
            : await getAttributeId(journalEntryLine, (await (await datevExport.attributeType1)?.id) ?? '');
    dimensionType = await datevExport.dimensionType2;
    const dimension2 =
        dimensionType !== null
            ? await getDimensionId(journalEntryLine, dimensionType)
            : await getAttributeId(journalEntryLine, (await (await datevExport.attributeType2)?.id) ?? '');
    return { dimension1, dimension2 };
}

async function getJournalEntryLine(
    journalEntryLine: xtremFinance.nodes.JournalEntryLine,
    datevExport: xtremFinance.nodes.DatevExport,
): Promise<NodeCreateData<xtremFinance.nodes.DatevExportJournalEntryLine>> {
    const { dimension1, dimension2 } = await getDimensions(journalEntryLine, datevExport);

    return {
        journalEntryLine,
        transactionValue: await journalEntryLine.datevTransactionAmount,
        datevSign: (await journalEntryLine.sign) === 'C' ? 'H' : 'S',
        transactionCurrency: await journalEntryLine.transactionCurrency,
        companyFxRate:
            Math.round(
                ((await journalEntryLine.companyFxRate) / (await journalEntryLine.companyFxRateDivisor)) * 10 ** 10,
            ) /
            10 ** 10,
        companyValue: await journalEntryLine.datevCompanyAmount,
        companyCurrency: await journalEntryLine.companyCurrency,
        datevAccountId: await (await journalEntryLine.account).datevId,
        datevContraAccountId: await journalEntryLine.datevContraAccountId,
        postingKey: await journalEntryLine.datevPostingKey,
        postingDate: await (await journalEntryLine.journalEntry).postingDate,
        number: await (await journalEntryLine.journalEntry).number,
        supplierDocumentNumber: await journalEntryLine.supplierDocumentNumber,
        description: await journalEntryLine.description,
        dimension1,
        dimension2,
        businessEntityTaxIdNumber: await journalEntryLine.datevBusinessEntityTaxIdNumber,
        locked: (await datevExport.isLocked) ? 1 : 0,
        siteTaxIdNumber: await (await journalEntryLine.financialSite).taxIdNumber,
    };
}

export async function executeJournalEntryLineExtraction(
    context: Context,
    datevExport: xtremFinance.nodes.DatevExport,
): Promise<NodeCreateData<xtremFinance.nodes.DatevExportJournalEntryLine>[]> {
    if (!(await datevExport.doJournalEntries)) {
        return [];
    }
    await context.batch.logMessage(
        'info',
        context.localize(
            '@sage/xtrem-finance/nodes__datev_export__start_extract_journal_entry_lines',
            'DATEV journal entry lines extraction start.',
        ),
    );
    logger.verbose(() => 'Start of DATEV journal entry lines extraction.');

    const datevExportJournalEntryLines: NodeCreateData<xtremFinance.nodes.DatevExportJournalEntryLine>[] = [];

    const journalEntries = context.query(xtremFinance.nodes.JournalEntry, {
        filter: {
            financialSite: { legalCompany: { _id: (await datevExport.company)._id } },
            postingDate: { _gte: await datevExport.startDate, _lte: await datevExport.endDate },
        },
    });

    let numberExtractedJournalEntryLines = 0;
    await journalEntries.every(async journalEntry => {
        if (await stopRequested(context, await datevExport.id)) {
            return false;
        }

        const linesToSkip: number[] = [];

        return journalEntry.lines
            .filter(async journalEntryLine =>
                ['other', 'excludingTax'].includes(await (await journalEntryLine.account).taxManagement),
            )
            .every(async journalEntryLine => {
                if (await stopRequested(context, await datevExport.id)) {
                    return false;
                }
                if ((await (await journalEntryLine.account).taxManagement) === 'other') {
                    linesToSkip.push((await journalEntryLine.contraJournalEntryLine)?._id ?? 0);
                }
                if (!linesToSkip.includes(journalEntryLine._id)) {
                    await journalEntryLineControls(context, journalEntryLine);

                    datevExportJournalEntryLines.push(await getJournalEntryLine(journalEntryLine, datevExport));
                    numberExtractedJournalEntryLines += 1;
                }
                return true;
            });
    });

    await context.batch.logMessage(
        'info',
        context.localize(
            '@sage/xtrem-finance/nodes__datev_export__number_extracted_journal_entry_lines',
            'DATEV journal entry lines extracted: {{numberExtractedJournalEntryLines}}.',
            { numberExtractedJournalEntryLines },
        ),
    );
    logger.verbose(() => `DATEV journal entry lines extracted:  ${numberExtractedJournalEntryLines.toString()}`);

    return datevExportJournalEntryLines;
}

// -------------------------------------------------------- //
// ------------          DATA EXPORT           ------------ //
// -------------------------------------------------------- //
async function createAndUploadFile(
    context: Context,
    parameters: {
        header: string;
        headLine: string;
        data: string[];
        filename: string;
        timeStamp: datetime;
    },
): Promise<NodePayloadData<xtremUpload.nodes.UploadedFile>> {
    const { header, headLine, data, filename, timeStamp } = parameters;
    const filenameExtension = 'csv';
    const objectKeyPrefix = 'datev';
    const mimeType = 'text/csv';

    // get file content
    const fileContent = `${header}\n${headLine}\n${data.join('\n')}`;

    const formattedTimeStamp = timeStamp.format(undefined, 'YYYY-MM-DD-HH-mm-ss');

    const uploadedFile = await xtremUpload.functions.createUploadedFile(context, {
        kind: 'upload',
        filename,
        filenameExtension,
        objectKeyPrefix,
        timestamp: formattedTimeStamp,
        mimeType,
    });

    await xtremInfrastructureAdapter.InfrastructureHelper.createFile(
        context,
        uploadedFile.objectKey,
        `Generated file - ${filename}`,
        fileContent,
        FileTimeToLive.Expire10Days,
    );

    return uploadedFile.result;
}

/**
 * Function used to create the data of a datev header. It can be used for the 3 types of file (accounts, business partners and journal entry lines)
 */
export async function getDatevHeader(parameters: {
    formatCategory: number;
    formatName: string;
    formatVersion: number;
    timeStamp: datetime;
    datevExport: xtremFinance.nodes.DatevExport;
    datevConfiguration: xtremFinanceData.nodes.DatevConfiguration;
}): Promise<string> {
    const locked = (await parameters.datevExport.isLocked) ? '1' : '0';
    return [
        '"EXTF"',
        '700',
        parameters.formatCategory.toString(),
        `"${parameters.formatName}"`,
        parameters.formatVersion.toString(),
        `${parameters.timeStamp.format(undefined, 'YYYYMMDDHHmmss')}000`,
        ...Array(4).fill(''),
        (await parameters.datevExport.datevConsultantNumber).toString(),
        (await parameters.datevExport.datevCustomerNumber).toString(),
        `${(await parameters.datevExport.fiscalYearStart).format('YYYYMMDD')}`,
        (await parameters.datevConfiguration.accountLength).toString(),
        parameters.formatCategory === 21 ? `${(await parameters.datevExport.startDate)?.format('YYYYMMDD')}` : '',
        parameters.formatCategory === 21 ? `${(await parameters.datevExport.endDate)?.format('YYYYMMDD')}` : '',
        parameters.formatCategory === 21
            ? `Rechnungsausgang ${(await parameters.datevExport.startDate)?.format('MM/YYYY')}`
            : '',
        ...Array(2).fill(''),
        parameters.formatCategory === 21 ? '0' : '',
        parameters.formatCategory === 21 ? locked : '',
        parameters.formatCategory === 21 ? await (await (await parameters.datevExport.company).currency).id : '',
        ...Array(4).fill(''),
        `"${(await parameters.datevExport.skrCoa).toString().padStart(2, '0')}"`,
        ...Array(4).fill(''),
    ].join(';');
}

function getDatevAccountHeadLine(): string {
    return ['Konto', 'Kontenbeschriftung', 'Sprach-ID', 'Kontenbeschriftung lang'].join(';');
}

function getDatevBusinessRelationHeadLineBloc(strIdx: number, endIdx: number): string[] {
    const bloc: string[] = [];

    for (let i = strIdx; i <= endIdx; i += 1) {
        bloc.push(`Bankleitzahl ${i}`);
        bloc.push(`Bankbezeichung ${i}`);
        bloc.push(`Bankkonto-Nummer ${i}`);
        bloc.push(`Länderkennzeichen ${i}`);
        bloc.push(`IBAN ${i}`);
        bloc.push(`Leerfeld`);
        bloc.push(`SWIFT-Code ${i}`);
        bloc.push(`Abw. Kontoinhaber ${i}`);
        bloc.push(`Kennz. Hauptbankverb. ${i}`);
        bloc.push(`Bankverb. ${i} Gültig von`);
        bloc.push(`Bankverb. ${i} Gültig bis`);
    }

    return bloc;
}

function getDatevJournalEntryLineHeadLineBloc(strPrefix: string, strIdx: number, endIdx: number): string[] {
    const bloc: string[] = [];

    for (let i = strIdx; i <= endIdx; i += 1) {
        bloc.push(`${strPrefix} - Art ${i}`);
        bloc.push(`${strPrefix} - Inhalt ${i}`);
    }

    return bloc;
}

function getDatevHeadLineRepeatString(entryString: string, strIdx: number, endIdx: number): string[] {
    const bloc: string[] = [];

    for (let i = strIdx; i <= endIdx; i += 1) {
        bloc.push(`${entryString}${i}`);
    }

    return bloc;
}

function getDatevJournalEntryLinesHeadLine(): string {
    return [
        'Umsatz (ohne Soll/Haben-Kz)',
        'Soll/Haben-Kennzeichen',
        'WKZ Umsatz',
        'Kurs',
        'Basisumsatz',
        'WKZ Basisumsatz',
        'Konto',
        'Gegenkonto (ohne BU-Schlüssel)',
        'BU-Schlüssel',
        'Belegdatum',
        ...getDatevHeadLineRepeatString('Belegfeld ', 1, 2),
        'Skonto',
        'Buchungstext',
        'Postensperre',
        'Diverse Adressnummer',
        'Geschäftspartnerbank',
        'Sachverhalt',
        'Zinssperre',
        'Beleglink',
        ...getDatevJournalEntryLineHeadLineBloc('Beleginfo', 1, 8),
        'KOST1 - Kostenstelle',
        'KOST2 - Kostenstelle',
        'Kost Menge',
        'EU-Land u. USt-IdNr. (Bestimmung)',
        'EU-Steuersatz (Bestimmung)',
        'Abw. Versteuerungsart',
        'Sachverhalt L+L',
        'Funktionsergänzung L+L',
        'BU 49 Hauptfunktionstyp',
        'BU 49 Hauptfunktionsnummer',
        'BU 49 Funktionsergänzung',
        ...getDatevJournalEntryLineHeadLineBloc('Zusatzinformation', 1, 20),
        'Stück',
        'Gewicht',
        'Zahlweise',
        'Forderungsart',
        'Veranlagungsjahr',
        'Zugeordnete Fälligkeit',
        'Skontotyp',
        'Auftragsnummer',
        'Buchungstyp',
        'USt-Schlüssel (Anzahlungen)',
        'EU-Mitgliedstaat (Anzahlungen)',
        'Sachverhalt L+L (Anzahlungen)',
        'EU-Steuersatz (Anzahlungen)',
        'Erlöskonto (Anzahlungen)',
        'Herkunft-Kz',
        'Leerfeld',
        'KOST-Datum',
        'SEPA-Mandatsreferenz',
        'Skontosperre',
        'Gesellschaftername',
        'Beteiligtennummer',
        'Identifikationsnummer',
        'Zeichnernummer',
        'Postensperre bis',
        'Bezeichnung SoBil-Sachverhalt',
        'Kennzeichen SoBil-Buchung',
        'Festschreibung',
        'Leistungsdatum',
        'Datum Zuord. Steuerperiode',
        'Fälligkeit',
        'Generalumkehr',
        'Steuersatz',
        'Land',
        'Abrechnungsreferenz',
        'BVV-Position',
        'EU-Land u. USt-IdNr. (Ursprung)',
        'EU-Steuersatz (Ursprung)',
        'Abw. Skontokonto',
    ].join(';');
}

function getDatevBusinessRelationHeadLine(): string {
    return [
        'Konto',
        'Name (Adressatentyp Unternehmen)',
        'Unternehmensgegenstand',
        'Name (Adressatentyp natürl. Person)',
        'Vorname (Adressatentyp natürl. Person)',
        'Name (Adressatentyp keine Angabe)',
        'Adressatentyp',
        'Kurzbezeichnung',
        'EU-Land',
        'EU-USt-IdNr.',
        'Anrede',
        'Titel/Akad. Grad',
        'Adelstitel',
        'Namensvorsatz',
        'Adressart',
        'Straße',
        'Postfach',
        'Postleitzahl',
        'Ort',
        'Land',
        'Versandzusatz',
        'Adresszusatz',
        'Abweichende Anrede',
        ...getDatevHeadLineRepeatString('Abw. Zustellbezeichnung ', 1, 2),
        'Kennz. Korrespondenzadresse',
        'Adresse Gültig von',
        'Adresse Gültig bis',
        'Telefon',
        'Bemerkung (Telefon)',
        'Telefon Geschäftsleitung',
        'Bemerkung (Telefon GL)',
        'E-Mail',
        'Bemerkung (E-Mail)',
        'Internet',
        'Bemerkung (Internet)',
        'Fax',
        'Bemerkung (Fax)',
        'Sonstige',
        'Bemerkung (Sonstige)',
        ...getDatevBusinessRelationHeadLineBloc(1, 5),
        'Leerfeld',
        'Briefanrede',
        'Grußformel',
        'Kundennummer',
        'Steuernummer',
        'Sprache',
        'Ansprechpartner',
        'Vertreter',
        'Sachbearbeiter',
        'Diverse-Konto',
        'Ausgabeziel',
        'Währungssteuerung',
        'Kreditlimit (Debitor)',
        'Zahlungsbedingung',
        'Fälligkeit in Tagen (Debitor)',
        'Skonto in Prozent (Debitor)',
        'Kreditoren-Ziel 1 (Tage)',
        'Kreditoren-Skonto 1 (%)',
        'Kreditoren-Ziel 2 (Tage)',
        'Kreditoren-Skonto 2 (%)',
        'Kreditoren-Ziel 3 Brutto (Tage)',
        'Kreditoren-Ziel 4 (Tage)',
        'Kreditoren-Skonto 4 (%)',
        'Kreditoren-Ziel 5 (Tage)',
        'Kreditoren-Skonto 5 (%)',
        'Mahnung',
        'Kontoauszug',
        ...getDatevHeadLineRepeatString('Mahntext ', 1, 3),
        'Kontoauszugstext',
        'Mahnlimit Betrag',
        'Mahnlimit %',
        'Zinsberechnung',
        ...getDatevHeadLineRepeatString('Mahnzinssatz ', 1, 3),
        'Lastschrift',
        'Leerfeld',
        'Mandantenbank',
        'Zahlungsträger',
        ...getDatevHeadLineRepeatString('Indiv. Feld ', 1, 15),
        'Abweichende Anrede (Rechnungsadresse)',
        'Adressart (Rechnungsadresse)',
        'Straße (Rechnungsadresse)',
        'Postfach (Rechnungsadresse)',
        'Postleitzahl (Rechnungsadresse)',
        'Ort (Rechnungsadresse)',
        'Land (Rechnungsadresse)',
        'Versandzusatz (Rechnungsadresse)',
        'Adresszusatz (Rechnungsadresse)',
        'Abw. Zustellbezeichung 1 (Rechnungsadresse)',
        'Abw. Zustellbezeichung 2 (Rechnungsadresse)',
        'Adresse Gültig von (Rechnungsadresse)',
        'Adresse Gültig bis (Rechnungsadresse)',
        ...getDatevBusinessRelationHeadLineBloc(6, 10),
        'Nummer Fremdsystem',
        'Insolvent',
        ...getDatevHeadLineRepeatString('SEPA-Mandatsreferenz ', 1, 10),
        'Verknüpftes OPOS-Konto',
        'Mahnsperre bis',
        'Lastschriftsperre bis',
        'Zahlungssperre bis',
        'Gebührenberechnung',
        ...getDatevHeadLineRepeatString('Mahngebühr ', 1, 3),
        'Pauschalenberechnung',
        ...getDatevHeadLineRepeatString('Verzugspauschale ', 1, 3),
        'Alternativer Suchname',
        'Status',
        'Anschrift manuell geändert (Korrespondenzadresse)',
        'Anschrift individuell (Korrespondenzadresse)',
        'Anschrift manuell geändert (Rechnungsadresse)',
        'Anschrift individuell (Rechnungsadresse)',
        'Fristberechnung bei Debitor',
        ...getDatevHeadLineRepeatString('Mahnfrist ', 1, 3),
        'Letzte Frist',
    ].join(';');
}

async function getDatevAccountElement(
    context: Context,
    datevExportAccount: xtremFinance.nodes.DatevExportAccount,
): Promise<{ accountLine: string; warning: boolean }> {
    const accountName = await datevExportAccount.name;
    const accountDatevId = await datevExportAccount.datevId;
    const warning = !accountDatevId;

    if (warning) {
        await xtremFinance.functions.datevWarningLogs.logAccountWithoutDatevId(
            context,
            await datevExportAccount.account,
        );
    }

    return {
        accountLine: [
            `${await datevExportAccount.datevId}`,
            `"${accountName.substring(0, 40)}"`,
            '"de-DE"',
            `"${accountName}"`,
        ].join(';'),
        warning,
    };
}

async function getDatevBusinessRelationElement(
    context: Context,
    datevExportBusinessRelation: xtremFinance.nodes.DatevExportBusinessRelation,
): Promise<{ businessRelationLine: string; warning: boolean }> {
    const taxIdNumber = await datevExportBusinessRelation.taxIdNumber;
    const businessRelationDatevId = await datevExportBusinessRelation.datevId;
    const warning = !businessRelationDatevId;
    if (warning) {
        await xtremFinance.functions.datevWarningLogs.logBusinessEntityWithoutDatevId(
            context,
            await (
                await datevExportBusinessRelation.businessRelation
            ).businessEntity,
        );
    }

    return {
        businessRelationLine: [
            `${await datevExportBusinessRelation.datevId}`,
            `"${(await datevExportBusinessRelation.name).substring(0, 50)}"`,
            ...Array(6).fill('""'),
            `"${taxIdNumber.substring(0, 2)}"`,
            `"${taxIdNumber.substring(2, 13)}"`,
            ...Array(5).fill('""'),
            `"${(await datevExportBusinessRelation.street).substring(0, 36)}"`,
            ...Array(1).fill('""'),
            `"${(await datevExportBusinessRelation.postcode).substring(0, 10)}"`,
            `"${(await datevExportBusinessRelation.city).substring(0, 30)}"`,
            `"${await (await datevExportBusinessRelation.country).id}"`,
            ...Array(5).fill('""'),
            ...Array(3).fill(''),
            ...Array(21).fill('""'),
            ...Array(2).fill(''),
            ...Array(9).fill('""'),
            ...Array(2).fill(''),
            ...Array(9).fill('""'),
            ...Array(2).fill(''),
            ...Array(9).fill('""'),
            ...Array(2).fill(''),
            ...Array(9).fill('""'),
            ...Array(2).fill(''),
            ...Array(5).fill('""'),
            ...Array(1).fill(''),
            ...Array(3).fill('""'),
            ...Array(2).fill(''),
            ...Array(1).fill('""'),
            ...Array(25).fill(''),
            ...Array(2).fill('""'),
            ...Array(1).fill(''),
            ...Array(27).fill('""'),
            ...Array(2).fill(''),
            ...Array(9).fill('""'),
            ...Array(2).fill(''),
            ...Array(9).fill('""'),
            ...Array(2).fill(''),
            ...Array(9).fill('""'),
            ...Array(2).fill(''),
            ...Array(9).fill('""'),
            ...Array(2).fill(''),
            ...Array(9).fill('""'),
            ...Array(2).fill(''),
            ...Array(1).fill('""'),
            ...Array(1).fill(''),
            ...Array(10).fill('""'),
            ...Array(12).fill(''),
            ...Array(1).fill('""'),
            ...Array(2).fill(''),
            ...Array(1).fill('""'),
            ...Array(1).fill(''),
            ...Array(1).fill('""'),
            ...Array(5).fill(''),
        ].join(';'),
        warning,
    };
}

async function getDatevJournalEntryLinesElement(
    context: Context,
    datevExportJournalEntryLine: xtremFinance.nodes.DatevExportJournalEntryLine,
): Promise<{ journalEntryLine: string; warnings: integer }> {
    const warnings = await journalEntryLineControls(context, await datevExportJournalEntryLine.journalEntryLine);

    return {
        journalEntryLine: [
            `${(await datevExportJournalEntryLine.transactionValue)?.toString().replaceAll('.', ',')}`,
            `"${await datevExportJournalEntryLine.datevSign}"`,
            `"${await (await datevExportJournalEntryLine.transactionCurrency).id}"`,
            `${(await datevExportJournalEntryLine.companyFxRate)?.toFixed(6).replaceAll('.', ',')}`,
            `${(await datevExportJournalEntryLine.companyValue)?.toString().replaceAll('.', ',')}`,
            `"${await (await datevExportJournalEntryLine.companyCurrency).id}"`,
            `${await datevExportJournalEntryLine.datevAccountId}`,
            `${await datevExportJournalEntryLine.datevContraAccountId}`,
            `"${(await datevExportJournalEntryLine.postingKey) ?? ''}"`,
            `${(await datevExportJournalEntryLine.postingDate).format('DDMM')}`,
            `"${await datevExportJournalEntryLine.number}"`,
            `"${(await datevExportJournalEntryLine.supplierDocumentNumber).substring(0, 11)}"`,
            ...Array(1).fill(''),
            `"${await datevExportJournalEntryLine.description}"`,
            `0`,
            ...Array(1).fill('""'),
            ...Array(3).fill(''),
            ...Array(17).fill('""'),
            `"${await datevExportJournalEntryLine.dimension1}"`,
            `"${await datevExportJournalEntryLine.dimension2}"`,
            ...Array(1).fill(''),
            `"${await datevExportJournalEntryLine.businessEntityTaxIdNumber}"`,
            ...Array(1).fill(''),
            ...Array(1).fill('""'),
            ...Array(5).fill(''),
            ...Array(40).fill('""'),
            ...Array(3).fill(''),
            ...Array(1).fill('""'),
            ...Array(3).fill(''),
            ...Array(2).fill('""'),
            ...Array(1).fill(''),
            ...Array(1).fill('""'),
            ...Array(3).fill(''),
            ...Array(2).fill('""'),
            ...Array(1).fill(''),
            ...Array(1).fill('""'),
            ...Array(1).fill(''),
            ...Array(1).fill('""'),
            ...Array(1).fill(''),
            ...Array(2).fill('""'),
            ...Array(1).fill(''),
            ...Array(1).fill('""'),
            ...Array(1).fill(''),
            `${await datevExportJournalEntryLine.locked}`,
            ...Array(3).fill(''),
            ...Array(1).fill('""'),
            ...Array(1).fill(''),
            ...Array(2).fill('""'),
            ...Array(1).fill(''),
            `"${await datevExportJournalEntryLine.businessEntityTaxIdNumber}"`,
            ...Array(2).fill(''),
        ].join(';'),
        warnings,
    };
}

export async function getDatevAccountLines(
    context: Context,
    datevExportId: string,
): Promise<{ datevAccountLines: string[]; warnings: number }> {
    const accountLines = await context
        .query(xtremFinance.nodes.DatevExportAccount, { filter: { datevExport: { id: datevExportId } } })
        .map(datevAccount => {
            return getDatevAccountElement(context, datevAccount);
        })
        .toArray();

    return {
        datevAccountLines: accountLines.map(line => line.accountLine),
        warnings: accountLines.reduce((acc, val) => acc + Number(val.warning), 0),
    };
}

export async function getDatevBusinessRelationLines(
    context: Context,
    datevExportId: string,
): Promise<{ datevBusinessRelationLines: string[]; warnings: number }> {
    const businessRelationLines = await context
        .query(xtremFinance.nodes.DatevExportBusinessRelation, {
            filter: { datevExport: { id: datevExportId } },
        })
        .map(datevBusinessRelation => {
            return getDatevBusinessRelationElement(context, datevBusinessRelation);
        })
        .toArray();

    return {
        datevBusinessRelationLines: businessRelationLines.map(line => line.businessRelationLine),
        warnings: businessRelationLines.reduce((acc, val) => acc + Number(val.warning), 0),
    };
}

export async function getDatevJournalEntryLines(
    context: Context,
    datevExportId: string,
): Promise<{ datevJournalEntryLines: string[]; warnings: number }> {
    const journalEntryLines = await context
        .query(xtremFinance.nodes.DatevExportJournalEntryLine, {
            filter: { datevExport: { id: datevExportId } },
        })
        .map(datevJournalEntryLine => {
            return getDatevJournalEntryLinesElement(context, datevJournalEntryLine);
        })
        .toArray();

    return {
        datevJournalEntryLines: journalEntryLines.map(line => line.journalEntryLine),
        warnings: journalEntryLines.reduce((acc, val) => acc + val.warnings, 0),
    };
}

export async function executeAccountExport(
    context: Context,
    datevExport: xtremFinance.nodes.DatevExport,
    datevConfiguration: xtremFinanceData.nodes.DatevConfiguration,
): Promise<{ uploadedFile: NodePayloadData<xtremUpload.nodes.UploadedFile>; warnings: number }> {
    const timeStamp = datetime.now();

    const { datevAccountLines, warnings } = await getDatevAccountLines(context, await datevExport.id);

    const datevAccountsFile = await createAndUploadFile(context, {
        header: await getDatevHeader({
            formatCategory: 20,
            formatName: 'Kontenbeschriftungen',
            formatVersion: 3,
            timeStamp,
            datevExport,
            datevConfiguration,
        }),
        headLine: getDatevAccountHeadLine(),
        data: datevAccountLines,
        filename: 'EXTF_datev-accounts',
        timeStamp,
    });

    await context.batch.logMessage(
        'result',
        context.localize(
            '@sage/xtrem-finance/nodes__datev_export__complete_account_export',
            'DATEV account export completed successfully.',
        ),
        { data: datevAccountsFile },
    );
    logger.verbose(() => 'DATEV account export completed successfully.');

    return { uploadedFile: datevAccountsFile, warnings };
}

export async function executeBusinessRelationExport(
    context: Context,
    datevExport: xtremFinance.nodes.DatevExport,
    datevConfiguration: xtremFinanceData.nodes.DatevConfiguration,
): Promise<{ uploadedFile: NodePayloadData<xtremUpload.nodes.UploadedFile>; warnings: number }> {
    const timeStamp = datetime.now();

    const { datevBusinessRelationLines, warnings } = await getDatevBusinessRelationLines(context, await datevExport.id);

    const datevBusinessRelationsFile = await createAndUploadFile(context, {
        header: await getDatevHeader({
            formatCategory: 16,
            formatName: 'Debitoren/Kreditoren',
            formatVersion: 5,
            timeStamp,
            datevExport,
            datevConfiguration,
        }),
        headLine: getDatevBusinessRelationHeadLine(),
        data: datevBusinessRelationLines,
        filename: 'EXTF_datev-business-relations',
        timeStamp,
    });

    await context.batch.logMessage(
        'result',
        context.localize(
            '@sage/xtrem-finance/nodes__datev_export__complete_business_relation_export',
            'DATEV business relation export completed successfully.',
        ),
        { data: datevBusinessRelationsFile },
    );
    logger.verbose(() => 'DATEV business relation export completed successfully.');

    return { uploadedFile: datevBusinessRelationsFile, warnings };
}

export async function executeJournalEntryLinesExport(
    context: Context,
    datevExport: xtremFinance.nodes.DatevExport,
    datevConfiguration: xtremFinanceData.nodes.DatevConfiguration,
): Promise<{ uploadedFile: NodePayloadData<xtremUpload.nodes.UploadedFile>; warnings: number }> {
    const timeStamp = datetime.now();

    const { datevJournalEntryLines, warnings } = await getDatevJournalEntryLines(context, await datevExport.id);

    const datevJournalEntryLinesFile = await createAndUploadFile(context, {
        header: await getDatevHeader({
            formatCategory: 21,
            formatName: 'Buchungsstapel',
            formatVersion: 13,
            timeStamp,
            datevExport,
            datevConfiguration,
        }),
        headLine: getDatevJournalEntryLinesHeadLine(),
        data: datevJournalEntryLines,
        filename: 'EXTF_datev_journal_entry_lines',
        timeStamp,
    });

    await context.batch.logMessage(
        'result',
        context.localize(
            '@sage/xtrem-finance/nodes__datev_export__complete_journal_entry_lines_export',
            'DATEV journal entry line export completed successfully.',
        ),
        { data: datevJournalEntryLinesFile },
    );
    logger.verbose(() => 'DATEV journal entry line export completed successfully.');

    return { uploadedFile: datevJournalEntryLinesFile, warnings };
}

function getNotificationAction(
    title: string,
    uploadedFile: NodePayloadData<xtremUpload.nodes.UploadedFile>,
): InitialNotificationAction {
    return {
        icon: 'link',
        title,
        style: 'link',
        link: uploadedFile.downloadUrl ?? '',
    };
}

export async function executeDatevExport(context: Context, datevExport: xtremFinance.nodes.DatevExport): Promise<void> {
    const datevConfiguration = await xtremFinanceData.functions.datev.getDatevConfiguration(context);

    let nbOfFilesCreated = 0;
    let nbOfWarnings = 0;

    const actions: InitialNotificationAction[] = [
        {
            icon: 'link',
            title: context.localize('@sage/xtrem-finance/nodes__datev_export__user_notifications_history', 'History'),
            style: 'tertiary',
            link: context.batch.notificationStateLink,
        },
    ];

    if (await datevExport.doAccounts) {
        const datevAccountsFile = await executeAccountExport(context, datevExport, datevConfiguration);
        actions.push(
            getNotificationAction(
                context.localize(
                    '@sage/xtrem-finance/nodes__datev_export__user_download_accounts',
                    'Download accounts',
                ),
                datevAccountsFile.uploadedFile,
            ),
        );
        nbOfFilesCreated += 1;
        nbOfWarnings += datevAccountsFile.warnings;
    }

    if (await datevExport.doCustomersSuppliers) {
        const datevBusinessRelationFile = await executeBusinessRelationExport(context, datevExport, datevConfiguration);
        actions.push(
            getNotificationAction(
                context.localize(
                    '@sage/xtrem-finance/nodes__datev_export__user_notifications_download_customers_and_suppliers',
                    'Download customers and suppliers',
                ),
                datevBusinessRelationFile.uploadedFile,
            ),
        );
        nbOfFilesCreated += 1;
        nbOfWarnings += datevBusinessRelationFile.warnings;
    }

    if (await datevExport.doJournalEntries) {
        const datevJournalEntryLinesFile = await executeJournalEntryLinesExport(
            context,
            datevExport,
            datevConfiguration,
        );
        actions.push(
            getNotificationAction(
                context.localize(
                    '@sage/xtrem-finance/nodes__datev_export__user_download_journal_entry_lines',
                    'Download journal entry lines',
                ),
                datevJournalEntryLinesFile.uploadedFile,
            ),
        );
        nbOfFilesCreated += 1;
        nbOfWarnings += datevJournalEntryLinesFile.warnings;
    }

    await context.notifyUser({
        title: context.localize('@sage/xtrem-finance/nodes__datev_export__datev_file', 'DATEV export files'),
        description: context
            .localize(
                '@sage/xtrem-finance/nodes__datev_export__datev_file_ready',
                'Files created: {{nbOfFilesCreated}}.\n',
                { nbOfFilesCreated },
            )
            .concat(
                nbOfWarnings
                    ? context.localize(
                          '@sage/xtrem-finance/nodes__datev_export__datev_number_of_warnings',
                          'Warnings: {{nbOfWarnings}}.',
                          { nbOfWarnings },
                      )
                    : '',
            ),
        icon: 'download',
        level: 'success',
        shouldDisplayToast: true,
        actions,
    });
}
