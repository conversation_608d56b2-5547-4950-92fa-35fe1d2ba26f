import type { Context } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremFinance from '..';

export async function logAccountWithoutDatevId(context: Context, account: xtremFinanceData.nodes.Account) {
    await context.batch.logMessage(
        'warning',
        context.localize(
            '@sage/xtrem-finance/nodes__datev_export__account_without_datev_id',
            'Account without a DATEV ID: {{account}}.',
            { account: await account.id },
        ),
    );
}

export async function logBusinessEntityWithoutDatevId(
    context: Context,
    businessEntity: xtremMasterData.nodes.BusinessEntity,
) {
    await context.batch.logMessage(
        'warning',
        context.localize(
            '@sage/xtrem-finance/nodes__datev_export__business_relation_without_datev_id',
            'Business entity without a DATEV ID: {{businessEntity}}.',
            { businessEntity: await businessEntity.id },
        ),
    );
}

export async function logJournalEntryLineWithDifferentTax(
    context: Context,
    journalEntryLine: xtremFinance.nodes.JournalEntryLine,
) {
    await context.batch.logMessage(
        'warning',
        context.localize(
            '@sage/xtrem-finance/nodes__datev_export__journal_entry_line_with_different_tax',
            'Journal entry line with an automatic account using a different tax code. Journal entry {{journalEntryNumber}}, account {{account}}.',
            {
                journalEntryNumber: await (await journalEntryLine.journalEntry).number,
                account: await (await journalEntryLine.account).id,
            },
        ),
    );
}

export async function logJournalEntryLineWithoutPostingKey(
    context: Context,
    journalEntryLine: xtremFinance.nodes.JournalEntryLine,
) {
    await context.batch.logMessage(
        'warning',
        context.localize(
            '@sage/xtrem-finance/nodes__datev_export__journal_entry_line_without_posting_key',
            'Journal entry line with a non-automatic account and no posting key. Journal entry {{journalEntryNumber}}, account {{account}}.',
            {
                journalEntryNumber: await (await journalEntryLine.journalEntry).number,
                account: await (await journalEntryLine.account).id,
            },
        ),
    );
}

export async function logJournalEntryLineWithoutAccountDatevId(
    context: Context,
    journalEntryLine: xtremFinance.nodes.JournalEntryLine,
) {
    await context.batch.logMessage(
        'warning',
        context.localize(
            '@sage/xtrem-finance/nodes__datev_export__journal_entry_line_account_without_datev_id',
            'Journal entry line for an account without a DATEV ID. Journal entry {{journalEntryNumber}}, account {{account}}.',
            {
                journalEntryNumber: await (await journalEntryLine.journalEntry).number,
                account: await (await journalEntryLine.account).id,
            },
        ),
    );
}

export async function logJournalEntryLineWithoutDatevContraAccountId(
    context: Context,
    journalEntryLine: xtremFinance.nodes.JournalEntryLine,
) {
    await context.batch.logMessage(
        'warning',
        context.localize(
            '@sage/xtrem-finance/nodes__datev_export__journal_entry_line_contra_account_without_datev_id',
            'Journal entry line for a contra account without a DATEV ID. Journal entry {{journalEntryNumber}}, contra account {{contraAccount}}.',
            {
                journalEntryNumber: await (await journalEntryLine.journalEntry).number,
                contraAccount: await journalEntryLine.contraAccount,
            },
        ),
    );
}

export async function logJournalEntryLineWithoutTaxLineForTaxableAmount(
    context: Context,
    journalEntryLine: xtremFinance.nodes.JournalEntryLine,
) {
    await context.batch.logMessage(
        'warning',
        context.localize(
            '@sage/xtrem-finance/nodes__datev_export__journal_entry_line_no_tax_line_for_taxable_amount',
            'A tax line cannot be found for the taxable amount. Journal entry {{journalEntryNumber}}.',
            {
                journalEntryNumber: await (await journalEntryLine.journalEntry).number,
            },
        ),
    );
}
