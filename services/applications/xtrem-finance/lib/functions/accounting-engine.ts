import type { Context, NodeQueryFilter } from '@sage/xtrem-core';
import { Logger, ValidationSeverity, asyncArray } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type { InitialNotificationAction } from '@sage/xtrem-shared';
import { DataInputError } from '@sage/xtrem-shared';
import type { BaseFinanceDocument } from '../classes/base-finance-document';
import * as xtremFinance from '../index';

const logger = Logger.getLogger(__filename, 'accounting-engine');

/** -------------------------------------------------------------------------------------
 * This script is dedicated to the finance documents generation.
 * On a first stage, it is used to create records on the accounting staging table from
 * the business documents based on notifications.
 * On a second stage, it generates journal entries, accounts receivable (ar) invoices and
 * accounts payable (ap) invoices from the records stored on the accounting staging table.
 * It is also responsible for replying back to the business documents that generate the
 * notifications.
 ------------------------------------------------------------------------------------- */

/** -------------------------------------------------------------------------------------
 * Callback functions provided to finance-data package to process an amount line
 ------------------------------------------------------------------------------------- */
/**
 * Adds or updates a finance document line to a finance document being created
 * @param journalEntryClassInstance: The journal entry class instance that originally called a function providing this function as a parameter
 * @param journalEntryTypeLine: A journal entry type line for the new finance document line generation
 * @param postingClassLine: A journal entry type line for the new finance document line generation
 */
export async function addOrUpdateFinanceDocumentLineCallback(
    context: Context,
    financeDocumentClassInstance: BaseFinanceDocument,
    journalEntryTypeLine: xtremFinanceData.nodes.JournalEntryTypeLine,
    postingClassLine: xtremFinanceData.nodes.PostingClassLine | null,
    stagingLineData: xtremFinanceData.interfaces.StagingLineData,
    createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
): Promise<void> {
    const account = await xtremFinanceData.functions.AccountingEngineCommon.getJournalEntryLineAccount({
        postingClassLine,
        accountingStagingLineAccount: stagingLineData.account,
        tax: stagingLineData.tax,
    });
    if (account) {
        financeDocumentClassInstance.addOrUpdateLine(
            await financeDocumentClassInstance.getNewFinanceDocumentLine(
                account,
                await financeDocumentClassInstance.currentAccountingStagingAmount.accountingStaging,
                journalEntryTypeLine,
                financeDocumentClassInstance.currentAccountingStagingAmount,
            ),
        );
    } else {
        createFinanceDocumentsReturn.validationMessages.push({
            type: ValidationSeverity.error,
            message: xtremFinance.classes.LocalizedMessages.unableToGetAccount(context),
        });
    }
}

/** -------------------------------------------------------------------------------------
 * Creating journal entries and ap/ar invoices
 ------------------------------------------------------------------------------------- */
/**
 *  Reply to the original notification that created the document on the accounting staging table
 * @param context context
 * @param financeTransactionRecord a set of data that identifies the documents concerned
 * @param status the status of the integration
 * @param createFinanceDocumentsReturn error messages
 * @param replyTopic the topic to reply to
 * @param replyId original notification id
 */
export async function replyToOriginalDocument(
    context: Context,
    financeTransactionRecord: xtremFinanceData.interfaces.FinanceTransactionRecord,
    status: xtremFinanceData.enums.FinanceIntegrationStatus,
    createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
    replyTopic: string,
    replyId: string,
    batchTrackingId?: string,
): Promise<void> {
    const financeTransactionData: xtremFinanceData.interfaces.FinanceTransactionData = {
        ...financeTransactionRecord,
        targetDocumentNumber: createFinanceDocumentsReturn.documentsCreated?.length
            ? createFinanceDocumentsReturn.documentsCreated[0].documentNumber
            : '',
        targetDocumentSysId: createFinanceDocumentsReturn.documentsCreated?.length
            ? createFinanceDocumentsReturn.documentsCreated[0].documentSysId
            : 0,
        validationMessages: createFinanceDocumentsReturn.validationMessages,
        status,
        isJustForStatus: true, // this will tell the listener static function that update of finance transaction node was already done
        batchTrackingId,
    };
    // Finance reply:
    // 1 - we update the finance transaction node
    // 2 - if the originated we send a reply to the accounting staging node
    await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, financeTransactionData);
    if (
        financeTransactionRecord.targetDocumentType !== 'journalEntry' ||
        ['apInvoice', 'arInvoice', 'salesInvoice', 'salesCreditMemo', 'purchaseInvoice', 'purchaseCreditMemo'].includes(
            financeTransactionRecord.documentType,
        )
    ) {
        context.logger.info(
            `Finance reply for document ${financeTransactionRecord.documentType} ${financeTransactionRecord.documentNumber}`,
        );
        await context.reply(replyTopic, financeTransactionData, { replyId });
    }
}

/** -------------------------------------------------------------------------------------
 * Journal entry creation
 ------------------------------------------------------------------------------------- */
/**
 * Creates a journal entry from a set of data on the accounting staging table related with a functional document
 * @param context context
 * @param targetDocumentType target document to create, accounts receivable or accounts payable invoice
 * @param documentType document type on the accounting staging table
 * @param documentNumber document number type on the accounting staging table
 * @param batchId batchId on the accounting staging table
 */
export async function createOrUpdateJournal(
    context: Context,
    targetDocumentType: xtremFinanceData.enums.TargetDocumentType,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    documentNumber: string,
    batchId: string,
    replyTopic: string,
    isUpdate = false,
    batchTrackingId?: string,
): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn> {
    const financeIntegrationRecord: xtremFinanceData.interfaces.FinanceTransactionRecord = {
        batchId,
        documentNumber,
        documentType,
        targetDocumentType,
    };

    let createOrUpdateJournalResult: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn = {
        documentsCreated: [],
        validationMessages: [] as xtremFinanceData.interfaces.ValidationMessage[],
    };

    const existingFinanceIntegrationRecord = await context.tryRead(xtremFinanceData.nodes.FinanceTransaction, {
        ...financeIntegrationRecord,
    });

    if (isUpdate && existingFinanceIntegrationRecord && (await existingFinanceIntegrationRecord.targetDocumentSysId)) {
        createOrUpdateJournalResult = await new xtremFinance.classes.JournalEntry(
            context,
            financeIntegrationRecord,
            replyTopic,
        ).update(
            context,
            financeIntegrationRecord,
            await existingFinanceIntegrationRecord.targetDocumentSysId,
            replyTopic,
        );
    } else {
        createOrUpdateJournalResult = await new xtremFinance.classes.JournalEntry(
            context,
            financeIntegrationRecord,
            replyTopic,
        ).create(context, financeIntegrationRecord, replyTopic, isUpdate, batchTrackingId);
    }

    if (createOrUpdateJournalResult.validationMessages?.length) {
        logger.error(
            () =>
                `Accounting engine, ${
                    isUpdate ? 'update' : 'create'
                } journal entry for document type ${documentType}, document number ${documentNumber},
                target document type ${targetDocumentType} and batch Id ${batchId}: ${JSON.stringify(
                    createOrUpdateJournalResult,
                )}`,
        );
        throw new DataInputError(
            createOrUpdateJournalResult.validationMessages
                .reduce((previous, current) => `${previous} \n ${current.message}`, '')
                .trimStart(),
        );
    }

    return createOrUpdateJournalResult;
}

/**
 * Creates an accounts payable or accounts receivable invoice from a set of data on the accounting staging table related with a business document
 * @param context context
 * @param targetDocumentType target document to create, accounts receivable or accounts payable invoice
 * @param documentType document type on the accounting staging table
 * @param documentNumber document number type on the accounting staging table
 * @param batchId batchId on the accounting staging table
 */
export async function createOrUpdateAPARInvoice(
    context: Context,
    targetDocumentType: xtremFinanceData.enums.TargetDocumentType,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    documentNumber: string,
    batchId: string,
    replyTopic: string,
    isUpdate = false,
    batchTrackingId?: string,
): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn> {
    let financeIntegrationRecord: xtremFinanceData.interfaces.FinanceTransactionRecord = {
        batchId,
        documentNumber,
        documentType,
        targetDocumentType,
    };

    let createOrUpdateApArInvoiceResult: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn = {
        documentsCreated: [],
        validationMessages: [] as xtremFinanceData.interfaces.ValidationMessage[],
    };

    const existingFinanceIntegrationRecord = await context.tryRead(
        xtremFinanceData.nodes.FinanceTransaction,
        financeIntegrationRecord,
    );
    if (existingFinanceIntegrationRecord) {
        financeIntegrationRecord = {
            ...financeIntegrationRecord,
            paymentTracking: await existingFinanceIntegrationRecord.paymentTracking,
        };
    }

    if (isUpdate && existingFinanceIntegrationRecord && (await existingFinanceIntegrationRecord.targetDocumentSysId)) {
        createOrUpdateApArInvoiceResult = await xtremFinance.classes.AccountsPayableReceivableInvoice.update(
            context,
            financeIntegrationRecord,
            await existingFinanceIntegrationRecord.targetDocumentSysId,
            replyTopic,
        );
    } else {
        createOrUpdateApArInvoiceResult = await xtremFinance.classes.AccountsPayableReceivableInvoice.create(
            context,
            financeIntegrationRecord,
            replyTopic,
            isUpdate,
            batchTrackingId,
        );
    }
    if (createOrUpdateApArInvoiceResult.validationMessages?.length) {
        logger.error(
            () =>
                `Accounting engine, ${
                    isUpdate ? 'update' : 'create'
                } ap-ar invoice for document type ${documentType}, document number ${documentNumber},
                target document type ${targetDocumentType} and batch Id ${batchId}: ${JSON.stringify(
                    createOrUpdateApArInvoiceResult,
                )}`,
        );
        throw new DataInputError(JSON.stringify(createOrUpdateApArInvoiceResult.validationMessages));
    }

    return createOrUpdateApArInvoiceResult;
}

/**
 * Creates the finance journal entries and ap\ar invoices for a given batchId
 * If one document fails, all should fail, for the same batchId
 * @param context context
 * @param groupedAccountingRecords the accounting staging records to process, all with the same batchId
 */
export async function createOrUpdateFinanceDocuments(
    context: Context,
    groupedAccountingRecords: xtremFinanceData.interfaces.GroupedAccountingRecord[],
    isUpdate = false,
    batchTrackingId?: string,
): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn[]> {
    let groupedAccountingRecordIndex = 0;
    const createdJournalsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn[] = [];
    try {
        await context.runInWritableContext(writableContext =>
            asyncArray(groupedAccountingRecords).forEach(async groupedAccountingRecord => {
                if (groupedAccountingRecord.targetDocumentType === 'journalEntry') {
                    // creates or updates a journal entry
                    createdJournalsReturn.push(
                        await createOrUpdateJournal(
                            writableContext,
                            groupedAccountingRecord.targetDocumentType,
                            groupedAccountingRecord.documentType,
                            groupedAccountingRecord.documentNumber,
                            groupedAccountingRecord.batchId,
                            groupedAccountingRecord.replyTopic,
                            isUpdate,
                            batchTrackingId,
                        ),
                    );
                } else {
                    // creates or updates an acccounts receivable invoice
                    createdJournalsReturn.push(
                        await createOrUpdateAPARInvoice(
                            writableContext,
                            groupedAccountingRecord.targetDocumentType,
                            groupedAccountingRecord.documentType,
                            groupedAccountingRecord.documentNumber,
                            groupedAccountingRecord.batchId,
                            groupedAccountingRecord.replyTopic,
                            isUpdate,
                            batchTrackingId,
                        ),
                    );
                }
                groupedAccountingRecordIndex += 1;
            }),
        );
    } catch (error) {
        logger.error(() => JSON.stringify(error));
        const financeIntegrationResultRecord: xtremFinanceData.interfaces.FinanceTransactionRecord = {
            batchId: groupedAccountingRecords[groupedAccountingRecordIndex].batchId,
            documentNumber: groupedAccountingRecords[groupedAccountingRecordIndex].documentNumber,
            documentType: groupedAccountingRecords[groupedAccountingRecordIndex].documentType,
            targetDocumentType: groupedAccountingRecords[groupedAccountingRecordIndex].targetDocumentType,
        };
        createdJournalsReturn.push({
            documentsCreated: [],
            validationMessages: [{ type: ValidationSeverity.error, message: error.message ?? '' }],
        });
        // in case of error we only reply if it's not an update
        // if we want to reply on the update error we need to review the status to be used
        if (!isUpdate) {
            await context.runInWritableContext(writableContext =>
                replyToOriginalDocument(
                    writableContext,
                    financeIntegrationResultRecord,
                    'notRecorded',
                    createdJournalsReturn[createdJournalsReturn.length - 1],
                    groupedAccountingRecords[groupedAccountingRecordIndex].replyTopic,
                    '',
                    batchTrackingId,
                ),
            );
        }
    }
    return createdJournalsReturn;
}

/**
 * Notify the user when the bulk journal entry creation is done.
 * @param context context
 * @param journalsCreated number of journals created
 * @return void
 */
export async function journalEntryClientNotification(context: Context, journalsCreated: number, hasError: boolean) {
    const title = hasError
        ? context.localize(
              '@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_title_fail',
              'Journal entry generation batch task unsuccessful',
          )
        : context.localize(
              '@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_title',
              'Journal entry generation batch task successful',
          );

    const description = context.localize(
        '@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_description',
        ' Journal entries generated: {{journalsCreated}}',
        { journalsCreated },
    );

    const actions: InitialNotificationAction[] = [
        {
            link: '@sage/xtrem-communication/SysNotificationHistory',
            title: 'Results',
            icon: 'link',
            style: 'tertiary',
        },
        {
            link: context.batch.notificationStateLink,
            title: 'History',
            icon: 'link',
            style: 'tertiary',
        },
    ];

    await context.notifyUser({
        title,
        icon: hasError ? 'error' : 'tick',
        description,
        level: hasError ? 'error' : 'success',
        shouldDisplayToast: true,
        actions,
    });
}

/**
 * Main entry point for the accounting engine. Processes all the non processed records from the accounting staging table
 * @param context context
 * @param paramFilter filter object to filter the accounting staging table
 * @return string ' xx journal created  '
 */
export async function createJournalsFromAccountingStaging(
    context: Context,
    paramFilter: NodeQueryFilter<xtremFinanceData.nodes.AccountingStaging> & { toBeReprocessed?: boolean } = {
        isProcessed: false,
    },
    isUpdate = false,
    batchTrackingId?: string,
): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn[]> {
    logger.debug(() => `Create journals using filter ${JSON.stringify(paramFilter)}`);

    const accountingStagingToBeProcess = context.queryAggregate(xtremFinanceData.nodes.AccountingStaging, {
        orderBy: { batchId: 1, documentType: 1, documentNumber: 1 },
        filter: paramFilter,
        group: {
            documentType: { _by: 'value' },
            targetDocumentType: { _by: 'value' },
            documentNumber: { _by: 'value' },
            batchId: { _by: 'value' },
            batchSize: { _by: 'value' },
            replyTopic: { _by: 'value' },
        },
        values: { _id: { distinctCount: true } },
    });

    const numberToBeProcess = await accountingStagingToBeProcess.length;
    if (!numberToBeProcess) {
        await context.batch.updateProgress({
            successCount: 1,
            totalCount: 1,
            phase: 'success',
        });
        await journalEntryClientNotification(context, 0, false);

        return [
            {
                documentsCreated: [],
                validationMessages: [
                    {
                        type: ValidationSeverity.info,
                        message: context.localize(
                            '@sage/xtrem-finance/functions__accounting_engine__no_documents_to_be_processed',
                            'No documents to be processed.',
                        ),
                    },
                ],
            },
        ];
    }

    await context.batch.logMessage(
        'info',
        context.localize(
            '@sage/xtrem-finance/functions__accounting_engine__to_be_process',
            'Number of documents to process: {{numberToBeProcess}}',
            { numberToBeProcess },
        ),
    );

    // generates the journal entries
    let batchId = '';
    let batchSize = 0;
    const createdJournalsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn[] = [];
    let accountingStagingBatchRecords: xtremFinanceData.interfaces.GroupedAccountingRecord[] = [];

    // progress bar
    let successCount = 0;
    let errorCount = 0;
    await accountingStagingToBeProcess
        // groups the records by batchId and creates the finance document by batchId
        .forEach(async (groupedAccountingRecord, i) => {
            if (batchId === groupedAccountingRecord.group.batchId) {
                batchSize += groupedAccountingRecord.values._id.distinctCount;
                accountingStagingBatchRecords.push(groupedAccountingRecord.group);
            } else {
                accountingStagingBatchRecords = [];
                batchSize = groupedAccountingRecord.values._id.distinctCount;
                accountingStagingBatchRecords.push(groupedAccountingRecord.group);
            }

            // If we are updating we might not be updating all the target documents so batchsize will not match
            if (batchSize === groupedAccountingRecord.group.batchSize || isUpdate) {
                await context.batch.logMessage(
                    'info',
                    context.localize(
                        '@sage/xtrem-finance/functions__accounting_engine__processing',
                        '{{i}}/{{numberToBeProcess}} processing {{documents}}',
                        {
                            numberToBeProcess,
                            i: i + 1,
                            documents: accountingStagingBatchRecords.map(record => record.documentNumber).join('-'),
                        },
                    ),
                );

                createdJournalsReturn.push(
                    ...(await createOrUpdateFinanceDocuments(
                        context,
                        accountingStagingBatchRecords,
                        isUpdate,
                        batchTrackingId,
                    )),
                );

                // Progress bar management
                const isError = createdJournalsReturn.some(line =>
                    line.validationMessages.some(message =>
                        [ValidationSeverity.error, ValidationSeverity.exception].includes(message.type),
                    ),
                );

                if (isError) {
                    errorCount += 1;
                } else {
                    successCount += 1;
                }

                if (((successCount || 0) + (errorCount || 0)) % 10) {
                    await context.batch.updateProgress({
                        successCount,
                        phase: 'processing',
                        errorCount,
                        totalCount: numberToBeProcess,
                    });
                }
            }

            batchId = groupedAccountingRecord.group.batchId;
        });

    // Update progress at the end
    await context.batch.updateProgress({
        successCount,
        phase: 'success',
        errorCount,
        totalCount: numberToBeProcess,
    });

    await journalEntryClientNotification(
        context,
        createdJournalsReturn.reduce((count, element) => count + element.documentsCreated.length, 0),
        errorCount > 0,
    );

    return createdJournalsReturn;
}

/**
 * Sometimes when we have more then one document to be generated on the same batchId, one succeeds
 * and another fails. This should not happen, since they should run on the same writableContext, but
 * it happens. Since one is successfully created, the failling will never run again because the not
 * processed documents for that batchId will never match the batchSize. This function allows to generate
 * the failed document by checking if the missing records correspond to an existing successfully posted
 * document.
 * @param context context
 * @param documentType The document type of the document that we want to rerun
 * @param documentNumber: The document number of the document that we want to rerun,
 * @param targetDocumentType: The target document type of the document that we want to rerun,
 * @return string
 */
export async function createOneOfManyFinanceDocumentsFromAccountingStaging(
    context: Context,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    documentNumber: string,
    targetDocumentType: xtremFinanceData.enums.TargetDocumentType,
    batchId: string,
): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn[]> {
    logger.info(
        () =>
            `Create some finance documents using for ${documentType} ${documentNumber} creating a ${targetDocumentType} with batch id ${batchId}`,
    );

    // gets the accounting staging records for the source document and batch id, grouped by relevant data
    const accountingStaging = await context
        .queryAggregate(xtremFinanceData.nodes.AccountingStaging, {
            filter: { documentNumber, documentType, batchId },
            group: {
                batchId: { _by: 'value' },
                batchSize: { _by: 'value' },
                documentType: { _by: 'value' },
                documentNumber: { _by: 'value' },
                targetDocumentType: { _by: 'value' },
                isProcessed: { _by: 'value' },
                replyTopic: { _by: 'value' },
            },
            values: { _id: { distinctCount: true } },
        })
        .toArray();

    const accountingRecordsToBeProcessed = accountingStaging.find(
        record => !record.group.isProcessed && record.group.targetDocumentType === targetDocumentType,
    );
    // we need to have records to be processed for the given target document type
    if (!accountingRecordsToBeProcessed) {
        const message = context.localize(
            '@sage/xtrem-finance/accounting_engine__no_records_to_process_on_accounting_staging_table',
            'There are no records to process on {{documentType}} {{documentNumber}} to {{targetDocumentType}}',
            { targetDocumentType, documentType, documentNumber },
        );
        logger.info(() => message);
        return [
            {
                documentsCreated: [],
                validationMessages: [
                    {
                        type: ValidationSeverity.info,
                        message,
                    },
                ],
            },
        ];
    }

    // There should be no record already processed for the given target document type
    if (
        accountingStaging.find(
            record => record.group.isProcessed && record.group.targetDocumentType === targetDocumentType,
        )
    ) {
        const message = context.localize(
            '@sage/xtrem-finance/accounting_engine__there_are_records_already_processed_on_accounting_staging_table',
            'There are records already processed on {{documentType}} {{documentNumber}} to {{targetDocumentType}}',
            { targetDocumentType, documentType, documentNumber },
        );
        logger.info(() => message);
        return [
            {
                documentsCreated: [],
                validationMessages: [
                    {
                        type: ValidationSeverity.info,
                        message,
                    },
                ],
            },
        ];
    }

    // All records for targetDocumentType that are not the given one must be already processed
    if (
        accountingStaging.find(
            record => !record.group.isProcessed && record.group.targetDocumentType !== targetDocumentType,
        )
    ) {
        const message = context.localize(
            '@sage/xtrem-finance/accounting_engine__there_are_records_to_be_processed_on_accounting_staging_table_to_documents_that_are_not_target_document_type',
            'There are records to be processed on {{documentType}} {{documentNumber}} that are not {{targetDocumentType}}',
            { targetDocumentType, documentType, documentNumber },
        );
        logger.info(() => message);
        return [
            {
                documentsCreated: [],
                validationMessages: [
                    {
                        type: ValidationSeverity.info,
                        message,
                    },
                ],
            },
        ];
    }

    // The batchsize is ok, meaning that no records were loss
    const batchSize = accountingStaging.at(0)?.group?.batchSize;
    const recordCount = accountingStaging.reduce((prev, record) => record.values._id.distinctCount + prev, 0);
    if (recordCount !== batchSize) {
        const message = context.localize(
            '@sage/xtrem-finance/accounting_engine__wrong_number_of_records',
            'Number of records on the accounting staging: {{recordCount}}; expected value: {{batchSize}}',
            { batchSize, recordCount },
        );
        logger.info(() => message);
        return [
            {
                documentsCreated: [],
                validationMessages: [
                    {
                        type: ValidationSeverity.info,
                        message,
                    },
                ],
            },
        ];
    }

    const createdJournalsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn[] = [];
    const accountingStagingBatchRecords: xtremFinanceData.interfaces.GroupedAccountingRecord[] = [];
    accountingStagingBatchRecords.push(accountingRecordsToBeProcessed.group);

    createdJournalsReturn.push(
        ...(await createOrUpdateFinanceDocuments(context, accountingStagingBatchRecords, false)),
    );

    return createdJournalsReturn;
}
