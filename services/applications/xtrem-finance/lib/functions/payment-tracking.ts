import type { Context, date } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type { decimal, InitialNotificationAction } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinance from '../index';
import type { AccountsReceivableOpenItem } from '../nodes/accounts-receivable-open-item';

// update the payment status on the accounts receivable invoice or accounts payable invoice
export async function updateApArInvoicePaymentStatus(parameters: {
    context: Context;
    openItem: xtremFinanceData.nodes.BaseOpenItem;
    status: xtremFinanceData.enums.OpenItemStatus;
}): Promise<boolean> {
    const isArOpenItem = ['salesInvoice', 'salesCreditMemo'].includes(await parameters.openItem.documentType);
    if (isArOpenItem) {
        const sysId = (await (parameters.openItem as AccountsReceivableOpenItem).accountsReceivableInvoice)._id;
        const arInvoice = await parameters.context.read(
            xtremFinance.nodes.AccountsReceivableInvoice,
            { _id: sysId },
            { forUpdate: true },
        );
        arInvoice.pCanUpdateFromExternalIntegration = true; // make sure the update of the ar invoice will not be refused by the control in saveBegin
        await arInvoice.$.set({ paymentStatus: parameters.status });
        await arInvoice.$.save();
    }
    return isArOpenItem;
}

// Updates an open item with the given line and company amounts.
// If the transaction amount due is equal to the new transaction amount paid, the open item is marked as paid.
// If the open item is fully paid in transaction currency, the company amount paid is set to the company amount due
// to avoid rounding issues and the close reason is set to 'payment'.
export async function updateOpenItem(parameters: {
    context: Context;
    openItemSysId: number;
    lineAmount: number;
    companyAmount: number;
    discountAmount?: number;
    adjustmentAmount?: number;
}): Promise<void> {
    const openItem = await parameters.context.read(
        xtremFinanceData.nodes.BaseOpenItem,
        { _id: parameters.openItemSysId },
        { forUpdate: true },
    );

    const transactionAmountDue = (await openItem.transactionAmountDue) ?? 0;
    const transactionAmountPaid = (await openItem.transactionAmountPaid) ?? 0;
    const newTransactionAmountPaid =
        transactionAmountPaid +
        (parameters.lineAmount ?? 0) +
        (parameters.discountAmount ?? 0) +
        (parameters.adjustmentAmount ?? 0);
    const companyAmountDue = (await openItem.companyAmountDue) ?? 0;
    const companyAmountPaid = (await openItem.companyAmountPaid) ?? 0;
    const newCompanyAmountPaid =
        transactionAmountDue === newTransactionAmountPaid
            ? companyAmountDue
            : companyAmountPaid + (parameters.companyAmount ?? 0);
    const closeReason =
        transactionAmountDue === newTransactionAmountPaid
            ? await parameters.context.read(xtremFinanceData.nodes.CloseReason, { id: 'Payment' })
            : null;
    await openItem.$.set({
        transactionAmountPaid: newTransactionAmountPaid,
        companyAmountPaid: newCompanyAmountPaid,
        financialSiteAmountPaid: newCompanyAmountPaid,
        closeReason,
    });
    await openItem.$.save();

    const status = await openItem.status;
    // update the payment status on the accounts receivable invoice or accounts payable invoice
    const isArOpenItem = await updateApArInvoicePaymentStatus({ context: parameters.context, openItem, status });

    // send notification to update the sales or purchasing document status
    if (isArOpenItem) {
        await parameters.context.notify(
            (await openItem.documentType) === 'salesInvoice'
                ? 'SalesInvoice/updatePaymentStatus'
                : 'SalesCreditMemo/updatePaymentStatus',
            { _id: await openItem.documentSysId, paymentStatus: status },
        );
    } else {
        await parameters.context.notify(
            (await openItem.documentType) === 'purchaseInvoice'
                ? 'PurchaseInvoice/updatePaymentStatus'
                : 'PurchaseCreditMemo/updatePaymentStatus',
            { _id: await openItem.documentSysId, paymentStatus: status },
        );
    }
}

// Forces an open item to be paid. Called from the bulk mutation on the open item edit page.
export async function forceOpenItemPayment(
    context: Context,
    openItem: xtremFinanceData.nodes.BaseOpenItem,
): Promise<void> {
    const transactionAmountDue = (await openItem.transactionAmountDue) ?? 0;
    const transactionAmountPaid = (await openItem.transactionAmountPaid) ?? 0;
    const forcedAmountPaid = (await openItem.forcedAmountPaid) ?? 0;
    const newForcedAmountPaid = transactionAmountDue - (transactionAmountPaid - forcedAmountPaid);
    const closeReason = await context.read(xtremFinanceData.nodes.CloseReason, { id: 'Forced close' });
    const closeText = context.localize(
        '@sage/xtrem-finance/nodes__base_open_item__text_forced_close',
        'Forced close on {{date}} by {{user}}',
        {
            date: new Date().toISOString().substring(0, 10),
            user: (await openItem.$.context.user)?.email,
        },
    );
    // we set the forcedAmountPaid here, the ...AmountPaid are all set in the saveBegin of the open item
    await openItem.$.set({
        status: 'paid',
        forcedAmountPaid: newForcedAmountPaid,
        closeReason,
        closeText,
    });
    await openItem.$.save();

    // update the payment status on the accounts receivable invoice or accounts payable invoice
    const isArOpenItem = await updateApArInvoicePaymentStatus({ context, openItem, status: 'paid' });

    // send notification to update the sales or purchasing document status
    if (isArOpenItem) {
        await context.notify(
            (await openItem.documentType) === 'salesInvoice'
                ? 'SalesInvoice/updatePaymentStatus'
                : 'SalesCreditMemo/updatePaymentStatus',
            { _id: await openItem.documentSysId, paymentStatus: 'paid' },
        );
    } else {
        await context.notify(
            (await openItem.documentType) === 'purchaseInvoice'
                ? 'PurchaseInvoice/updatePaymentStatus'
                : 'PurchaseCreditMemo/updatePaymentStatus',
            { _id: await openItem.documentSysId, paymentStatus: 'paid' },
        );
    }
}

/**
 * Notify the user that the bulk open item payment has been completed.
 * @param context
 * @param documents - Array of documentNumbers of the paid open items
 * @param openItemPage - page to be linked to notification
 */
export async function notifyUserBulkOpenItemPayment(
    context: Context,
    documents: string[],
    openItemPage: string,
): Promise<void> {
    const batchAction: InitialNotificationAction = {
        link: `@sage/xtrem-communication/SysNotificationState/`,
        title: context.localize(
            '@sage/xtrem-finance/fail__bulk_open_item_payment_notification_description__view_link',
            'Batch task logs',
        ),
        icon: 'link',
        style: 'tertiary',
    };

    if (documents.length === 0) {
        await context.notifyUser({
            title: context.localize(
                '@sage/xtrem-finance/success_notification__bulk_open_item_payment_title_error',
                'Force open item payment',
            ),
            description: context.localize(
                '@sage/xtrem-finance/notification_bulk_open_item_payment__description_error',
                `The forced payment for all open items failed. Review batch task logs for more information.`,
            ),
            icon: 'cross',
            level: 'error',
            shouldDisplayToast: true,
            actions: [batchAction],
        });

        return;
    }

    const actions: InitialNotificationAction[] = [];

    actions.push({
        link: xtremSystem.functions.linkToFilteredMainList<xtremFinance.nodes.AccountsReceivableOpenItem>(
            openItemPage,
            {
                documentNumber: { _in: documents },
            },
        ),
        title: context.localize(
            '@sage/xtrem-finance/success_notification_description__ar_open_items_link',
            'Open items',
        ),
        icon: 'link',
        style: 'tertiary',
    });

    actions.push(batchAction);

    await context.notifyUser({
        title: context.localize(
            '@sage/xtrem-finance/success_notification__bulk_open_item_payment_title_success',
            'Force open item payment',
        ),
        description: context.localize(
            '@sage/xtrem-finance/notification_bulk_open_item_payment__description_success',
            `The open items were forced to be paid.`,
        ),
        icon: 'tick',
        level: 'success',
        shouldDisplayToast: true,
        actions: [...actions],
    });
}

/**
 * Voids a payment or a receipt. called from the voidPayment mutation on the payment and receipt nodes.
 * @param context
 * @param payment - either a payment or a receipt
 * @param voidDate - the date of the void
 * @param voidText - the reason for the void
 */
export async function voidPayment(params: {
    context: Context;
    payment: xtremFinance.nodes.Payment | xtremFinance.nodes.Receipt;
    voidDate: date;
    voidText: string;
}): Promise<string> {
    if (await params.payment.isVoided) {
        return params.context.localize(
            '@sage/xtrem-finance/nodes__base_payment_document_payment_already_voided',
            'The payment was already voided.',
        );
    }

    await params.payment.lines.forEach(async paymentLine => {
        await xtremFinance.functions.updateOpenItem({
            context: params.context,
            openItemSysId: (await paymentLine.originalOpenItem)?._id ?? 0,
            lineAmount: (await paymentLine.amount) * -1,
            companyAmount: (await paymentLine.companyAmount) * -1,
            discountAmount: (await paymentLine.discountAmount) * -1,
            adjustmentAmount: (await paymentLine.adjustmentAmount) * -1,
        });
    });

    await params.payment.$.set({ isVoided: true, voidDate: params.voidDate, voidText: params.voidText });
    await params.payment.$.save();

    return params.context.localize(
        '@sage/xtrem-finance/node-extensions__base-payment-document-extension__voided',
        'The payment was voided.',
    );
}

// Called by the defaultValues of the companyAmount property of the payment document line extension.
// Calculates the company amount of a payment document line extension based on the
// "amount + discountAmount + adjustmentAmount" of the payment document line.
export async function defaultPaymentLineCompanyAmount(
    paymentDocumentLine: xtremFinanceData.nodes.PaymentDocumentLine,
): Promise<decimal> {
    const amount =
        (await paymentDocumentLine.amount) +
        (await paymentDocumentLine.discountAmount) +
        (await paymentDocumentLine.adjustmentAmount);
    const currency = await paymentDocumentLine.currency;
    const companyCurrency = await paymentDocumentLine.companyCurrency;
    if (currency !== companyCurrency) {
        const arOpenItem = await paymentDocumentLine.arOpenItem;
        if (arOpenItem) {
            return (await arOpenItem.companyAmountDue) * (amount / (await arOpenItem.transactionAmountDue));
        }
        const apOpenItem = await paymentDocumentLine.apOpenItem;
        if (apOpenItem) {
            return (await apOpenItem.companyAmountDue) * (amount / (await apOpenItem.transactionAmountDue));
        }
    }
    return amount;
}
