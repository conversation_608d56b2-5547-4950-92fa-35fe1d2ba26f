import type { Context, StaticThis, integer } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremFinance from '..';
import type { FinanceTypeNode } from './finance-types';

export function getFinanceDocumentNodeType(
    context: Context,
    targetDocumentType: xtremFinanceData.enums.TargetDocumentType,
) {
    switch (targetDocumentType) {
        case 'journalEntry':
            return context.application.getFactoryByName('JournalEntry').nodeConstructor;
        case 'accountsPayableInvoice':
            return context.application.getFactoryByName('AccountsPayableInvoice').nodeConstructor;
        case 'accountsReceivableInvoice':
            return context.application.getFactoryByName('AccountsReceivableInvoice').nodeConstructor;
        case 'accountsReceivableAdvance':
            return context.application.getFactoryByName('AccountsReceivableAdvance').nodeConstructor;
        case 'accountsReceivablePayment':
            return context.application.getFactoryByName('AccountsReceivablePayment').nodeConstructor;
        default:
            context.logger.info(`${targetDocumentType}: Target document type not supported.`);
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-finance/target_document_type_not_supported',
                    '{{targetDocumentType}}: Target document type not supported.',
                    { targetDocumentType },
                ),
            );
    }
}

export async function getTargetFinanceDocument(
    context: Context,
    document: { type: xtremFinanceData.enums.TargetDocumentType; sysId: integer; forUpdate?: boolean },
): Promise<FinanceTypeNode> {
    const forUpdate = !!document.forUpdate;
    const financeDocument = (await context.tryRead(
        getFinanceDocumentNodeType(context, document.type),
        { _id: document.sysId },
        { forUpdate },
    )) as FinanceTypeNode;
    if (!financeDocument) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-finance/target_document_not_found',
                '{{sysId}} {{type}}: Target document not found.',
                document,
            ),
        );
    }
    return financeDocument;
}

/**
 * Reads a finance document (Journal entry, AccountsReceivableInvoice or AccountsPayableInvoice).
 * @param context context
 * @param financeDocumentClass type of document to read (Journal entry, AccountsReceivableInvoice or AccountsPayableInvoice)
 * @return Journal entry, AccountsReceivableInvoice or AccountsPayableInvoice
 */
// eslint-disable-next-line require-await
export async function getFinanceDocumentBySysId(
    context: Context,
    financeDocumentClass: StaticThis<
        | xtremFinance.nodes.JournalEntry
        | xtremFinance.nodes.AccountsPayableInvoice
        | xtremFinance.nodes.AccountsReceivableInvoice
    >,
    targetDocumentSysId: integer,
    forUpdate: boolean,
): Promise<
    | xtremFinance.nodes.JournalEntry
    | xtremFinance.nodes.AccountsPayableInvoice
    | xtremFinance.nodes.AccountsReceivableInvoice
> {
    return context.read(financeDocumentClass, { _id: targetDocumentSysId }, { forUpdate });
}

/**
 * Tries to find a finance document (Journal entry, AccountsReceivableInvoice or AccountsPayableInvoice)
 * for a given finance transaction record by looking at the finance document line accounting staging records.
 * @param context context
 * @param financeTransaction the finance transaction
 * @param financeDocumentClass type of finance document line accounting staging to query
 * @return Journal entry, AccountsReceivableInvoice, AccountsPayableInvoice or null
 */
async function getFinanceDocumentFromStagingLines(
    context: Context,
    financeTransaction: xtremFinanceData.nodes.FinanceTransaction,
    financeDocumentLineStagingClass: StaticThis<
        | xtremFinance.nodes.JournalEntryLineStaging
        | xtremFinanceData.nodes.AccountsPayableInvoiceLineStaging
        | xtremFinanceData.nodes.AccountsReceivableInvoiceLineStaging
    >,
): Promise<xtremFinance.dataTypes.JournalEntryOrApArInvoiceLineStagingType | null> {
    return (
        (await context
            .query(financeDocumentLineStagingClass, {
                filter: {
                    accountingStaging: {
                        documentNumber: await financeTransaction.documentNumber,
                        documentSysId: await financeTransaction.documentSysId,
                        documentType: await financeTransaction.documentType,
                        targetDocumentType: await financeTransaction.targetDocumentType,
                        batchId: await financeTransaction.batchId,
                    },
                },
                first: 1,
            })
            .at(0)) || null
    );
}

/**
 * Searches for a journal entry, based on the finance transaction record.
 * It will first try to read the journal entry node directly.
 * If it does not find it, it will search for a journal entry that has any line
 * linked with an accounting staging record that originated from this finance transaction record.
 * @param context context
 * @param financeTransaction the finance transaction record
 * @return Journal entry or null
 */
async function getJournalEntry(
    context: Context,
    financeTransaction: xtremFinanceData.nodes.FinanceTransaction,
): Promise<xtremFinance.nodes.JournalEntry | null> {
    if (await financeTransaction.targetDocumentSysId) {
        return (await getFinanceDocumentBySysId(
            context,
            xtremFinance.nodes.JournalEntry,
            await financeTransaction.targetDocumentSysId,
            false,
        )) as xtremFinance.nodes.JournalEntry;
    }

    const journalEntryLineStaging = await getFinanceDocumentFromStagingLines(
        context,
        financeTransaction,
        xtremFinance.nodes.JournalEntryLineStaging,
    );

    context.logger.info(
        `retryFinanceDocument.getJournalEntry(): journalEntryLineStaging: ${
            journalEntryLineStaging ? journalEntryLineStaging._id : 'not found'
        }`,
    );

    return journalEntryLineStaging
        ? (await (journalEntryLineStaging as xtremFinance.nodes.JournalEntryLineStaging).journalEntryLine).journalEntry
        : null;
}

/**
 * Searches for an accounts payable invoice, based on the finance transaction record.
 * It will first try to read the ap invoice node directly.
 * If it does not find it, it will search for an ap invoice that has any line
 * linked with an accounting staging record that originated from this finance transaction record.
 * @param context context
 * @param financeTransaction the finance transaction record
 * @return AP invoice or null
 */
async function getAccountsPayableInvoice(
    context: Context,
    financeTransaction: xtremFinanceData.nodes.FinanceTransaction,
): Promise<xtremFinance.nodes.AccountsPayableInvoice | null> {
    const targetDocumentSysId =
        (await financeTransaction.targetDocumentSysId) ??
        (
            await getFinanceDocumentFromStagingLines(
                context,
                financeTransaction,
                xtremFinanceData.nodes.AccountsPayableInvoiceLineStaging,
            )
        )?._id;

    return targetDocumentSysId
        ? ((await getFinanceDocumentBySysId(
              context,
              xtremFinance.nodes.AccountsPayableInvoice,
              targetDocumentSysId,
              false,
          )) as xtremFinance.nodes.AccountsPayableInvoice)
        : null;
}

/**
 * Searches for an accounts receivable invoice, based on the finance transaction record.
 * It will first try to read the AR invoice node directly.
 * If it does not find it, it will search for an AR invoice that has any line
 * linked with an accounting staging record that originated from this finance transaction record.
 * @param context context
 * @param financeTransaction the finance transaction record
 * @return AR invoice or null
 */
async function getAccountsReceivableInvoice(
    context: Context,
    financeTransaction: xtremFinanceData.nodes.FinanceTransaction,
): Promise<xtremFinance.nodes.AccountsReceivableInvoice | null> {
    const targetDocumentSysId =
        (await financeTransaction.targetDocumentSysId) ??
        (
            await getFinanceDocumentFromStagingLines(
                context,
                financeTransaction,
                xtremFinanceData.nodes.AccountsReceivableInvoiceLineStaging,
            )
        )?._id;

    return targetDocumentSysId
        ? ((await getFinanceDocumentBySysId(
              context,
              xtremFinance.nodes.AccountsReceivableInvoice,
              targetDocumentSysId,
              false,
          )) as xtremFinance.nodes.AccountsReceivableInvoice)
        : null;
}

export async function getFinanceDocument(
    context: Context,
    financeTransaction: xtremFinanceData.nodes.FinanceTransaction,
): Promise<xtremFinance.dataTypes.JournalEntryOrApArInvoiceType | null> {
    switch (await financeTransaction.targetDocumentType) {
        case 'journalEntry':
            return getJournalEntry(context, financeTransaction);
        case 'accountsPayableInvoice':
            return getAccountsPayableInvoice(context, financeTransaction);
        case 'accountsReceivableInvoice':
            return getAccountsReceivableInvoice(context, financeTransaction);
        default:
            context.logger.info(`retryFinanceDocument.getFinanceDocument(): finance document not found.`);
            return null;
    }
}

function getTopicForWorkInProgress(
    context: Context,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    sourceDocumentType: xtremFinanceData.enums.SourceDocumentType,
): string {
    switch (sourceDocumentType) {
        case 'materialTracking':
            return 'MaterialTracking/resendNotificationForFinance';
        case 'operationTracking':
            return 'OperationTracking/resendNotificationForFinance';
        case 'productionTracking':
            return 'ProductionTracking/resendNotificationForFinance';
        case 'workOrderClose':
            return 'WorkOrder/resendNotificationForFinance';
        default:
            context.logger.info(
                `${sourceDocumentType}: Source document type not supported for document type ${documentType}`,
            );
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-finance/source_document_type_not_supported',
                    'Source document type not supported: {{sourceDocumentType}}.',
                    { sourceDocumentType },
                ),
            );
    }
}

export function getTopicByDocumentType(
    context: Context,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    sourceDocumentType: xtremFinanceData.enums.SourceDocumentType,
): string {
    switch (documentType) {
        case 'miscellaneousStockReceipt':
            return 'StockReceipt/resendNotificationForFinance';
        case 'miscellaneousStockIssue':
            return 'StockIssue/resendNotificationForFinance';
        case 'stockCount':
            return 'StockCount/resendNotificationForFinance';
        case 'purchaseReceipt':
            return 'PurchaseReceipt/resendNotificationForFinance';
        case 'purchaseInvoice':
            return 'PurchaseInvoice/resendNotificationForFinance';
        case 'purchaseCreditMemo':
            return 'PurchaseCreditMemo/resendNotificationForFinance';
        case 'salesInvoice':
            return 'SalesInvoice/resendNotificationForFinance';
        case 'salesCreditMemo':
            return 'SalesCreditMemo/resendNotificationForFinance';
        case 'stockAdjustment':
            return 'StockAdjustment/resendNotificationForFinance';
        case 'salesShipment':
            return 'SalesShipment/resendNotificationForFinance';
        case 'apInvoice':
            return 'AccountsPayableInvoice/resendNotificationForFinance';
        case 'arInvoice':
            return 'AccountsReceivableInvoice/resendNotificationForFinance';
        case 'purchaseReturn':
            return 'PurchaseReturn/resendNotificationForFinance';
        case 'salesReturnReceipt':
            return 'SalesReturnReceipt/resendNotificationForFinance';
        case 'stockTransferShipment':
            return 'StockTransferShipment/resendNotificationForFinance';
        case 'stockValueChange':
            return 'StockValueChange/resendNotificationForFinance';
        case 'stockTransferReceipt':
            return 'StockTransferReceipt/resendNotificationForFinance';
        case 'workInProgress':
            return getTopicForWorkInProgress(context, documentType, sourceDocumentType);
        default:
            context.logger.info(`${documentType}: Document type not supported.`);
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-finance/document_type_not_supported',
                    'Document type not supported: {{documentType}}',
                    { documentType },
                ),
            );
    }
}
