import type { date, decimal, integer } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';

export interface PaymentHeader {
    bankAccount: xtremFinanceData.nodes.BankAccount;
    financialSite: xtremSystem.nodes.Site;
    businessRelation: xtremMasterData.nodes.BaseBusinessRelation;
    type: xtremMasterData.enums.BusinessRelationType;
    paymentMethod: xtremMasterData.enums.PaymentMethod;
    reference: string;
    paymentDate: date;
    currency: xtremMasterData.nodes.Currency;
    amount: decimal;
    amountBankCurrency: decimal;
}

export interface PaymentCreationLine {
    openItemSysId: integer;
    paymentAmount?: decimal;
    creditAmount?: decimal;
}
