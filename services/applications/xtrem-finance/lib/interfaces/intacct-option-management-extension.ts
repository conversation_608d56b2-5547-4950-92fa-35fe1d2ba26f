import type { Context } from '@sage/xtrem-core';

export interface UpdateTaxSolutionLinesAndAccountsByLegislationParameters {
    context: Context;
    legislationIds: string[];
    isSubjectToGlTaxExcludedAmount: boolean;
    updateAccounts?: boolean;
}

export interface UpdateTaxSolutionLinesParameters {
    context: Context;
    legislationId: string;
    isSubjectToGlTaxExcludedAmount: boolean;
}

export interface UpdateAccountsTaxManagementParameters {
    context: Context;
    legislationIds: string[];
}
