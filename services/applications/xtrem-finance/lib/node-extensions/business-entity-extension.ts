import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';

@decorators.nodeExtension<BusinessEntityExtension>({
    extends: () => xtremMasterData.nodes.BusinessEntity,
})
export class BusinessEntityExtension extends NodeExtension<xtremMasterData.nodes.BusinessEntity> {
    @decorators.stringProperty<BusinessEntityExtension, 'composedDescription'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            return `${await this.id} -- ${await this.name}`;
        },
    })
    readonly composedDescription: Promise<string>;
}

declare module '@sage/xtrem-master-data/lib/nodes/business-entity' {
    export interface BusinessEntity extends BusinessEntityExtension {}
}
