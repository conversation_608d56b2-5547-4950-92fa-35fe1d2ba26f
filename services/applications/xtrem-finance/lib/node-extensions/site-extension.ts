import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.nodeExtension<SiteExtension>({
    extends: () => xtremSystem.nodes.Site,
})
export class SiteExtension extends NodeExtension<xtremSystem.nodes.Site> {
    @decorators.stringProperty<SiteExtension, 'composedDescription'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            return `${await this.id} -- ${await this.name}`;
        },
    })
    readonly composedDescription: Promise<string>;
}

declare module '@sage/xtrem-system/lib/nodes/site' {
    export interface Site extends SiteExtension {}
}
