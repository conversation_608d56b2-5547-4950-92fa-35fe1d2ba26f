import type { Reference, decimal } from '@sage/xtrem-core';
import { SubNodeExtension1, decorators } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as _ from 'lodash';
import {
    controlApOpenItemReference,
    controlArOpenItemReference,
} from '../events/control/payment-document-line-extension';
import * as xtremFinance from '../index';

@decorators.subNodeExtension1<PaymentDocumentLineExtension>({
    extends: () => xtremFinanceData.nodes.PaymentDocumentLine,

    async controlBegin(cx) {
        await xtremFinance.events.controlBegin.PaymentDocumentLineExtension.checkApArInvoiceStatus(this, cx);
    },

    async saveBegin() {
        const openItemSysId = (await this.arOpenItem)?._id ?? (await this.apOpenItem)?._id;
        await xtremFinance.functions.updateOpenItem({
            context: this.$.context,
            openItemSysId: openItemSysId ?? 0,
            lineAmount: await this.amount,
            companyAmount: await this.companyAmount,
            discountAmount: await this.discountAmount,
            adjustmentAmount: await this.adjustmentAmount,
        });
    },
})
export class PaymentDocumentLineExtension extends SubNodeExtension1<xtremFinanceData.nodes.PaymentDocumentLine> {
    @decorators.referenceProperty<PaymentDocumentLineExtension, 'arOpenItem'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dependsOn: [{ document: ['businessRelation'] }],
        async control(cx, val) {
            await controlArOpenItemReference({ line: this, val, cx });
        },
        node: () => xtremFinance.nodes.AccountsReceivableOpenItem,
    })
    readonly arOpenItem: Reference<xtremFinance.nodes.AccountsReceivableOpenItem | null>;

    @decorators.referenceProperty<PaymentDocumentLineExtension, 'apOpenItem'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dependsOn: [{ document: ['businessRelation'] }],
        async control(cx, val) {
            await controlApOpenItemReference({ line: this, val, cx });
        },
        node: () => xtremFinance.nodes.AccountsPayableOpenItem,
    })
    readonly apOpenItem: Reference<xtremFinance.nodes.AccountsPayableOpenItem | null>;

    @decorators.enumPropertyOverride<PaymentDocumentLineExtension, 'origin'>({
        dependsOn: ['arOpenItem', 'apOpenItem'],
        async defaultValue() {
            const arOpenItem = await this.arOpenItem;
            if (arOpenItem) {
                const arInvoice = await arOpenItem.accountsReceivableInvoice;
                if (arInvoice) {
                    return arInvoice.origin;
                }
            }
            const apOpenItem = await this.apOpenItem;
            if (apOpenItem) {
                const apInvoice = await apOpenItem.accountsPayableInvoice;
                if (apInvoice) {
                    return apInvoice.origin;
                }
            }
            return null;
        },
    })
    readonly origin: Promise<xtremFinanceData.enums.AccountsPayableReceivableInvoiceOrigin>;

    @decorators.referencePropertyOverride<PaymentDocumentLineExtension, 'originalOpenItem'>({
        node: () => xtremFinanceData.nodes.BaseOpenItem,
        dependsOn: ['arOpenItem', 'apOpenItem'],
        async defaultValue() {
            return (await this.arOpenItem) || this.apOpenItem;
        },
    })
    readonly originalOpenItem: Reference<xtremFinanceData.nodes.BaseOpenItem | null>;

    @decorators.referencePropertyOverride<PaymentDocumentLineExtension, 'originalNodeFactory'>({
        node: () => xtremMetadata.nodes.MetaNodeFactory,
        dependsOn: ['originalOpenItem'],
        async defaultValue() {
            const name = _.upperFirst(await (await this.originalOpenItem)?.documentType);
            return this.$.context.read(xtremMetadata.nodes.MetaNodeFactory, { name });
        },
    })
    readonly originalNodeFactory: Reference<xtremMetadata.nodes.MetaNodeFactory>;

    @decorators.decimalProperty<PaymentDocumentLineExtension, 'companyAmount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        dependsOn: ['amount', 'discountAmount', 'penaltyAmount', 'adjustmentAmount', 'currency', 'companyCurrency'],
        defaultValue() {
            return xtremFinance.functions.defaultPaymentLineCompanyAmount(this);
        },
    })
    readonly companyAmount: Promise<decimal>;

    @decorators.decimalProperty<PaymentDocumentLineExtension, 'signedAmount'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            return xtremFinance.functions.signedAmount({
                isReceipt: (await this.document)?.$.factory.name === 'Receipt',
                isSupplier: (await this.arOpenItem) === null,
                origin: await this.origin,
                amount: await this.amount,
            });
        },
    })
    readonly signedAmount: Promise<decimal>;

    @decorators.decimalProperty<PaymentDocumentLineExtension, 'signedAmountBankCurrency'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            return xtremFinance.functions.signedAmount({
                isReceipt: (await this.document)?.$.factory.name === 'Receipt',
                isSupplier: (await this.arOpenItem) === null,
                origin: await this.origin,
                amount: await this.amountBankCurrency,
            });
        },
    })
    readonly signedAmountBankCurrency: Promise<decimal>;
}

declare module '@sage/xtrem-finance-data/lib/nodes/payment-document-line' {
    export interface PaymentDocumentLine extends PaymentDocumentLineExtension {}
}
