import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';

@decorators.nodeExtension<ItemExtension>({
    extends: () => xtremMasterData.nodes.Item,
})
export class ItemExtension extends NodeExtension<xtremMasterData.nodes.Item> {
    @decorators.stringProperty<ItemExtension, 'composedDescription'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            return `${await this.id} -- ${await this.name}`;
        },
    })
    readonly composedDescription: Promise<string>;
}

declare module '@sage/xtrem-master-data/lib/nodes/item' {
    export interface Item extends ItemExtension {}
}
