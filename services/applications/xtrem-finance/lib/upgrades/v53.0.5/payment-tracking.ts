import { CustomSqlAction } from '@sage/xtrem-system';

export const paymentTracking = new CustomSqlAction({
    description:
        'Create payment tracking record for purchase invoices and credit memos and link it to finance transaction and AP invoice',
    fixes: {
        tables: ['finance_transaction'],
    },
    body: async helper => {
        await helper.executeSql(`
            DO $$ DECLARE
                ftRecord RECORD;
                existing_payment_tracking_id ${helper.schemaName}.payment_tracking._id%TYPE :=1;
                payment_tracking_id ${helper.schemaName}.payment_tracking._id%TYPE :=1;
                base_id ${helper.schemaName}.base_document._id%TYPE :=1;
                tenant varchar(255):= '';
                documentType varchar(255):= '';
                documentNumber varchar(255):= '';
                documentSysId ${helper.schemaName}.finance_transaction.document_sys_id%TYPE :=0;
                BEGIN
                    FOR ftRecord IN (
                        SELECT _id, _tenant_id, _create_user, _create_stamp, _update_user, _update_stamp,
                            document_sys_id, target_document_sys_id, document_number, document_type
                        FROM ${helper.schemaName}.finance_transaction
                        WHERE target_document_type = 'accountsPayableInvoice'
                    )
                    LOOP
                        -- Check if payment tracking record already exists for the document
                        SELECT pt._id
                        FROM ${helper.schemaName}.payment_tracking pt
                        WHERE pt._tenant_id=ftRecord._tenant_id
                        AND pt.document=ftRecord.document_sys_id
                        INTO existing_payment_tracking_id;

                        -- Check if base document record exists for ftRecord.document_sys_id
                        base_id := 0;
                        SELECT bd._id
                        FROM ${helper.schemaName}.base_document bd
                        WHERE bd._tenant_id=ftRecord._tenant_id
                        AND bd._id=ftRecord.document_sys_id
                        INTO base_id;

                        -- Warning if base document not found
                        if base_id IS NULL OR base_id <= 0 THEN
                            documentType := ftRecord.document_type;
                            documentNumber := ftRecord.document_number;
                            documentSysId := ftRecord.document_sys_id;
                            tenant := ftRecord._tenant_id;
                            RAISE WARNING 'Base document not found: tenant: %, document type: %, document number: %, document _id: %', tenant, documentType, documentNumber, documentSysId;
                        END IF;

                        -- If payment tracking record does not exist, create a new one and link it to finance transaction
                        IF base_id IS NOT NULL AND existing_payment_tracking_id IS NULL THEN
                            INSERT INTO ${helper.schemaName}.payment_tracking(_tenant_id, _create_user, _create_stamp, _update_user, _update_stamp, document)
                            VALUES (ftRecord._tenant_id, ftRecord._create_user, ftRecord._create_stamp, ftRecord._update_user,
                                ftRecord._update_stamp, ftRecord.document_sys_id)
                            RETURNING _id INTO payment_tracking_id;

                            IF ftRecord.target_document_sys_id <> 0 THEN
                                UPDATE ${helper.schemaName}.accounts_payable_invoice SET payment_tracking = payment_tracking_id
                                WHERE _id = ftRecord.target_document_sys_id AND _tenant_id = ftRecord._tenant_id;
                            END IF;

                            UPDATE ${helper.schemaName}.finance_transaction SET payment_tracking = payment_tracking_id
                            WHERE _id = ftRecord._id AND _tenant_id = ftRecord._tenant_id;
                        END IF;
                    END LOOP;
            END $$;
            `);
    },
});
