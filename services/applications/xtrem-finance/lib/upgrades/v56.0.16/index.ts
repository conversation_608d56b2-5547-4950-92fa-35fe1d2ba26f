import { UpgradeSuite } from '@sage/xtrem-system';
import {
    renamePropertyAccountsReceivableInvoiceLineForLineAmountExcludingTax,
    renamePropertyAccountsReceivableInvoiceLineForLineAmountIncludingTax,
} from './rename-accounts-receivable-invoice-line-property';

import {
    renamePropertyAccountsPayableInvoiceLineForLineAmountExcludingTax,
    renamePropertyAccountsPayableInvoiceLineForLineAmountIncludingTax,
} from './rename-accounts-payable-invoice-line-property';

export const upgradeSuite = new UpgradeSuite({
    actions: [
        renamePropertyAccountsReceivableInvoiceLineForLineAmountIncludingTax,
        renamePropertyAccountsReceivableInvoiceLineForLineAmountExcludingTax,
        renamePropertyAccountsPayableInvoiceLineForLineAmountExcludingTax,
        renamePropertyAccountsPayableInvoiceLineForLineAmountIncludingTax,
    ],
});
