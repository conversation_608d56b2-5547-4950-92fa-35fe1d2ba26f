import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import { AccountsPayableInvoiceLine } from '../../nodes/_index';

export const renamePropertyAccountsPayableInvoiceLineForLineAmountIncludingTax = new SchemaRenamePropertyAction({
    node: () => AccountsPayableInvoiceLine,
    oldPropertyName: 'lineAmountIncludingTax',
    newPropertyName: 'amountIncludingTax',
});

export const renamePropertyAccountsPayableInvoiceLineForLineAmountExcludingTax = new SchemaRenamePropertyAction({
    node: () => AccountsPayableInvoiceLine,
    oldPropertyName: 'lineAmountExcludingTax',
    newPropertyName: 'amountExcludingTax',
});
