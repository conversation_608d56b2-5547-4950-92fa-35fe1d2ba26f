import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import { AccountsReceivableInvoiceLine } from '../../nodes/_index';

export const renamePropertyAccountsReceivableInvoiceLineForLineAmountIncludingTax = new SchemaRenamePropertyAction({
    node: () => AccountsReceivableInvoiceLine,
    oldPropertyName: 'lineAmountIncludingTax',
    newPropertyName: 'amountIncludingTax',
});

export const renamePropertyAccountsReceivableInvoiceLineForLineAmountExcludingTax = new SchemaRenamePropertyAction({
    node: () => AccountsReceivableInvoiceLine,
    oldPropertyName: 'lineAmountExcludingTax',
    newPropertyName: 'amountExcludingTax',
});
