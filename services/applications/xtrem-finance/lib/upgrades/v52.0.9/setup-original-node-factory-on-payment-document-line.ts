import { CustomSqlAction } from '@sage/xtrem-system';

export const setupOriginalNodeFactoryOnPaymentDocumentLine = new CustomSqlAction({
    description: 'Set originalNodeFactory on PaymentDocumentLine',
    fixes: { notNullableColumns: [{ table: 'payment_document_line', column: 'original_node_factory' }] },
    body: async helper => {
        await helper.executeSql(`DO $$
        DECLARE
            tenant_node_factory_invoice_id BIGINT;
            tenant_node_factory_credit_memo_id BIGINT;
        BEGIN
            SELECT mnfi._id
            FROM ${helper.schemaName}.meta_node_factory mnfi
            WHERE mnfi.name='SalesInvoice' AND mnfi.is_active
            INTO tenant_node_factory_invoice_id;

            SELECT mnfc._id
            FROM ${helper.schemaName}.meta_node_factory mnfc
            WHERE mnfc.name='SalesCreditMemo' AND mnfc.is_active
            INTO tenant_node_factory_credit_memo_id;

            UPDATE ${helper.schemaName}.payment_document_line payment_line
            SET original_node_factory =
            ( CASE
                WHEN (line.origin = 'invoice') THEN tenant_node_factory_invoice_id
                ELSE tenant_node_factory_credit_memo_id
            END)
            FROM ${helper.schemaName}.payment_document_line line
            WHERE line._id = payment_line._id AND line._tenant_id = payment_line._tenant_id;
        END $$;`);
    },
});
