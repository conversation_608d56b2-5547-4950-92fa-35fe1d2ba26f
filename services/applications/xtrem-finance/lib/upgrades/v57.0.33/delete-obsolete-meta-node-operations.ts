import { CustomSqlAction } from '@sage/xtrem-system';

export const deleteObsoleteMetaNodeOperations = new CustomSqlAction({
    description: 'Delete obsolete createJournalsFromAccountingStagingJob Meta Node Operations',
    body: async helper => {
        await helper.executeSql(`
            DO $$
            DECLARE old_operation RECORD;
            BEGIN FOR old_operation IN
                SELECT mno._id FROM ${helper.schemaName}.meta_node_operation mno
                    INNER JOIN ${helper.schemaName}.meta_node_factory node_factory
                    ON mno.factory = node_factory._id
                    WHERE mno.name = 'createJournalsFromAccountingStagingJob'
                    AND node_factory.name = 'AccountingStaging'
                LOOP
                    DECLARE old_schedule RECORD;
                    BEGIN FOR old_schedule IN
                        SELECT sjs._id FROM ${helper.schemaName}.sys_job_schedule sjs
                            WHERE sjs.operation = old_operation._id
                        LOOP
                            DELETE FROM ${helper.schemaName}.sys_notification_state sns
                                WHERE sns.schedule = old_schedule._id;
                        END LOOP;
                    END;
                    DELETE FROM ${helper.schemaName}.sys_notification_state sns
                        WHERE sns.operation = old_operation._id;
                    DELETE FROM ${helper.schemaName}.sys_job_schedule sjs
                        WHERE sjs.operation = old_operation._id;
                    DELETE FROM ${helper.schemaName}.meta_node_operation mno
                        WHERE mno._id = old_operation._id;
                END LOOP;
            END $$
            `);
    },
});
