import type { Filter, OrderBy } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { SysNotificationHistory as SysNotificationHistoryPage } from '@sage/xtrem-communication/build/lib/pages/sys-notification-history';
import type { GraphApi } from '@sage/xtrem-finance-api';
import type {
    FinanceDocumentType,
    FinanceTransaction,
    FinanceTransactionLine,
    SourceDocumentType,
} from '@sage/xtrem-finance-data-api';
import * as PillColorIntegrationStatus from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import {
    getDocumentPageNameFinance,
    getTargetDocumentPageNameFinance,
} from '@sage/xtrem-finance-data/build/lib/shared-functions';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import * as ui from '@sage/xtrem-ui';
import { isFinanceTransaction, isFinanceTransactionLine, isFinanceTransactionTable } from '../client-functions/common';

export type FinanceTransactionTable = FinanceTransaction & {
    originDocumentNumber: string;
    originDocumentType: string;
    wasResent: boolean;
};

export type SelectFinanceTransactionLine = {
    _id: string;
    sourceDocumentType: SourceDocumentType;
    sourceDocumentNumber: string;
    sourceDocumentSysId: number;
    financeTransactionSysId: string;
};

@ui.decorators.pageExtension<SysNotificationHistoryExtension>({
    extends: '@sage/xtrem-communication/SysNotificationHistory',
    async onLoad() {
        if (this.$.queryParameters && this.$.queryParameters.recordFilter) {
            this.additionalRecordFilter = JSON.parse(
                this.$.queryParameters.recordFilter.toString(),
            ) as Filter<FinanceTransaction>;
            this.financeTransactionDocumentType.isDisabled = true;
            this.financeTransactionStatus.isDisabled = true;
            this.financeTransactionDocumentType.value = [String(this.additionalRecordFilter.documentType)];
        }

        this.financeTransactionStatus.value = ['toBeRecorded', 'recording', 'error', 'notRecorded', 'failed'];
        await this.searchFinanceData();

        this.$.setPageClean();
    },
})
export class SysNotificationHistoryExtension extends ui.PageExtension<SysNotificationHistoryPage, GraphApi> {
    additionalRecordFilter: Filter<FinanceTransaction> | null = null;

    @ui.decorators.section<SysNotificationHistoryExtension>({
        isTitleHidden: true,
        title: 'Finance',
        insertBefore() {
            return this.section;
        },
    })
    financeTransactionSection: ui.containers.Section;

    @ui.decorators.block<SysNotificationHistoryExtension>({
        parent() {
            return this.financeTransactionSection;
        },
        title: 'Criteria',
    })
    financeTransactionCriteriaBlock: ui.containers.Block;

    @ui.decorators.multiDropdownField<SysNotificationHistoryExtension>({
        parent() {
            return this.financeTransactionCriteriaBlock;
        },
        title: 'Document type',
        bind: 'documentType',
        optionType: '@sage/xtrem-finance-data/FinanceDocumentType',
    })
    financeTransactionDocumentType: ui.fields.MultiDropdown;

    @ui.decorators.multiDropdownField<SysNotificationHistoryExtension>({
        parent() {
            return this.financeTransactionCriteriaBlock;
        },
        title: 'Status',
        bind: 'status',
        optionType: '@sage/xtrem-finance-data/FinanceIntegrationStatus',
    })
    financeTransactionStatus: ui.fields.MultiDropdown;

    @ui.decorators.buttonField<SysNotificationHistoryExtension>({
        parent() {
            return this.financeTransactionCriteriaBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-finance/sys__notification_history__search', 'Search');
        },
        width: 'small',
        async onClick() {
            this.$.loader.isHidden = false;
            await this.searchFinanceData();
            this.$.setPageClean();
            this.$.loader.isHidden = true;
        },
    })
    searchFinance: ui.fields.Button;

    @ui.decorators.tableField<SysNotificationHistoryExtension, FinanceTransactionTable>({
        isTransient: true,
        isChangeIndicatorDisabled: true,
        canSelect: false,
        canFilter: false,
        title: 'Results',
        canResizeColumns: true,
        canExport: true,
        parent() {
            return this.financeTransactionSection;
        },
        orderBy: {
            _createStamp: -1,
        },
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHidden: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'batchId',
                title: 'Batch id',
                isReadOnly: true,
                isHidden: true,
            }),
            ui.nestedFields.numeric({
                bind: 'documentSysId',
                isReadOnly: true,
                isHidden: true,
            }),
            ui.nestedFields.label<SysNotificationHistoryExtension, FinanceTransactionTable>({
                bind: 'postingStatus',
                title: 'Status',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowValue) =>
                    PillColorIntegrationStatus.getLabelColorByStatus(
                        'FinanceDocumentPostingStatus',
                        rowValue?.postingStatus,
                    ),
            }),
            ui.nestedFields.label<SysNotificationHistoryExtension, FinanceTransactionTable>({
                bind: 'status',
                title: 'Notification status',
                optionType: '@sage/xtrem-finance-data/FinanceIntegrationStatus',
                style: (_id, rowValue) =>
                    PillColorIntegrationStatus.getLabelColorByStatus('FinanceIntegrationStatus', rowValue?.status),
            }),
            ui.nestedFields.date({ title: 'Sent', bind: '_createStamp', isReadOnly: true }),
            ui.nestedFields.text({ bind: 'message', title: 'Message', isReadOnly: true, isFullWidth: true }),
            ui.nestedFields.link({
                bind: 'originDocumentNumber',
                isTransient: true,
                title: 'Document number',
                isFullWidth: true,
                onClick(_id, rowData) {
                    if (isFinanceTransactionTable(rowData)) {
                        const page = getDocumentPageNameFinance({ type: rowData.documentType });

                        return this.$.dialog.page(
                            page,
                            { _id: rowData.documentSysId },
                            { fullScreen: true, resolveOnCancel: true },
                        );
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.text({
                bind: 'originDocumentType',
                isTransient: true,
                title: 'Document type',
                isReadOnly: true,
            }),
            ui.nestedFields.text({ bind: 'documentNumber', isHidden: true }),
            ui.nestedFields.select({
                optionType: '@sage/xtrem-finance-data/FinanceDocumentType',
                bind: 'documentType',
                isHidden: true,
            }),
            ui.nestedFields.numeric({ bind: 'sourceDocumentSysId', isHidden: true }),
            ui.nestedFields.text({ bind: 'sourceDocumentNumber', isHidden: true }),
            ui.nestedFields.select({
                optionType: '@sage/xtrem-finance-data/SourceDocumentType',
                bind: 'sourceDocumentType',
                isHidden: true,
            }),
            ui.nestedFields.link({
                bind: 'targetDocumentNumber',
                title: 'Target document number',
                isFullWidth: true,
                onClick(_id, rowData) {
                    if (isFinanceTransactionTable(rowData)) {
                        const page = getTargetDocumentPageNameFinance(rowData.targetDocumentType);
                        if (page && rowData.targetDocumentSysId) {
                            return this.$.dialog.page(
                                page,
                                { _id: rowData.targetDocumentSysId },
                                { fullScreen: true, resolveOnCancel: true },
                            );
                        }
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.select({
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
                bind: 'targetDocumentType',
                title: 'Target document type',
                isReadOnly: true,
            }),
            ui.nestedFields.checkbox({ bind: 'wasResent', title: 'Resent', isHidden: true }),
            ui.nestedFields.technical({ bind: 'hasSourceForDimensionLines' }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Edit',
                isDisabled(_rowId, rowData) {
                    if (rowData.wasResent) return true;
                    if (isFinanceTransaction(rowData)) {
                        if (!['notRecorded', 'failed'].includes(rowData.status)) return true;
                        return ![
                            'journalEntry',
                            'accountsPayableInvoice',
                            'accountsReceivableInvoice',
                            'accountsReceivableAdvance',
                        ].includes(rowData.targetDocumentType);
                    }
                    return false;
                },
                async onClick(rowId, rowData) {
                    if (rowData.targetDocumentType) {
                        const dimensionsOnSourceDocumentTypes: FinanceDocumentType[] = [
                            'miscellaneousStockReceipt',
                            'salesInvoice',
                            'salesReturnReceipt',
                            'salesShipment',
                            'purchaseReceipt',
                            'purchaseInvoice',
                            'purchaseCreditMemo',
                            'purchaseReturn',
                            'stockAdjustment',
                            'stockCount',
                            'miscellaneousStockIssue',
                            'salesCreditMemo',
                            'salesShipment',
                            'stockValueChange',
                            'workInProgress',
                            'stockTransferShipment',
                            'stockTransferReceipt',
                        ];

                        if (rowData.documentType) {
                            const recordSysId = dimensionsOnSourceDocumentTypes.includes(rowData.documentType)
                                ? rowData.documentSysId
                                : rowData.targetDocumentSysId?.toString();

                            if (dimensionsOnSourceDocumentTypes.includes(rowData.documentType)) {
                                rowData.wasResent = true;
                                this.financeTransactionHistory.addOrUpdateRecordValue(rowData);
                                await this.financeTransactionHistory.redraw();
                            }
                            if (recordSysId) {
                                return this.$.dialog.page(
                                    getDocumentPageNameFinance({ type: rowData.documentType }),
                                    {
                                        _id: recordSysId,
                                        fromNotificationHistory: 'true',
                                        financeTransactionSysId: rowId,
                                        wipSourceDocumentType: rowData.sourceDocumentType?.toString() || '',
                                    },
                                    { fullScreen: true, resolveOnCancel: true },
                                );
                            }
                        }
                    }
                    return undefined;
                },
            },
            {
                icon: 'edit',
                title: 'Edit source document dimensions',
                isDisabled(_rowId, rowData) {
                    if (isFinanceTransaction(rowData)) {
                        return (
                            !['notRecorded', 'failed'].includes(rowData.status) ||
                            ![
                                'journalEntry',
                                'accountsPayableInvoice',
                                'accountsReceivableInvoice',
                                'accountsReceivableAdvance',
                            ].includes(rowData.targetDocumentType) ||
                            rowData.wasResent ||
                            !rowData.hasSourceForDimensionLines
                        );
                    }
                    return false;
                },
                async onClick(_rowId, rowData) {
                    this.sourceDocuments.value = [];

                    await this.loadSourceDocuments(rowData._id);

                    rowData.wasResent = true;
                    this.financeTransactionHistory.addOrUpdateRecordValue(rowData);
                    await this.financeTransactionHistory.redraw();

                    const selectedFinanceTransactionLine = await this.selectFinanceTransactionLine();

                    if (selectedFinanceTransactionLine && rowData.documentType) {
                        await this.$.dialog.page(
                            getDocumentPageNameFinance({
                                type: rowData.documentType,
                                sourceType: selectedFinanceTransactionLine.sourceDocumentType,
                            }),
                            {
                                _id: selectedFinanceTransactionLine.sourceDocumentSysId || 0,
                                fromNotificationHistory: 'true',
                                sourceDocumentData: JSON.stringify({
                                    financeTransactionSysId: rowData._id,
                                    numberOfSourceDocuments: this.sourceDocuments.value.length,
                                }),
                            },
                            {
                                fullScreen: true,
                                resolveOnCancel: true,
                            },
                        );
                    }
                },
            },
            {
                icon: 'play',
                title: 'Retry',
                isDisabled(_rowId, rowItem) {
                    if (rowItem.status) {
                        return (
                            !['pending', 'submitted', 'failed', 'notRecorded', 'error'].includes(rowItem.status) ||
                            !!rowItem.wasResent
                        );
                    }
                    return false;
                },
                async onClick(_rowId, rowItem) {
                    rowItem.wasResent = true;
                    this.financeTransactionHistory.addOrUpdateRecordValue(rowItem);
                    await this.financeTransactionHistory.redraw();

                    this.$.showToast(
                        (
                            await this.$.graph
                                .node('@sage/xtrem-finance/AccountingInterfaceListener')
                                .mutations.retryFinanceDocument(
                                    { wasSuccessful: true, message: true },
                                    { financeTransaction: rowItem._id },
                                )
                                .execute()
                        ).message,
                    );
                },
            },
        ],
    })
    financeTransactionHistory: ui.fields.Table<FinanceTransactionTable>;

    @ui.decorators.block<SysNotificationHistoryExtension>({
        title: 'Source documents',
    })
    sourceDocumentsBlock: ui.containers.Block;

    @ui.decorators.tableField<SysNotificationHistoryExtension, SelectFinanceTransactionLine>({
        parent() {
            return this.sourceDocumentsBlock;
        },
        canSelect: false,
        canFilter: false,
        canUserHideColumns: false,
        title: 'Source documents',
        isTitleHidden: true,
        pageSize: 10,
        isReadOnly: true,
        columns: [
            ui.nestedFields.dropdownList({ title: 'Document', bind: 'sourceDocumentType' }),
            ui.nestedFields.link({
                bind: 'sourceDocumentNumber',
                title: 'Number',
                isFullWidth: true,
                onClick(_id, rowData) {
                    if (isFinanceTransactionLine(rowData)) {
                        return this.$.dialog.page(
                            getDocumentPageNameFinance({ type: rowData.sourceDocumentType }),
                            {
                                _id: rowData.sourceDocumentSysId,
                                fromNotificationHistory: 'true',
                                sourceDocumentData: JSON.stringify({
                                    financeTransactionSysId: rowData.financeTransactionSysId,
                                    numberOfSourceDocuments: this.sourceDocuments.value.length,
                                }),
                            },
                            { fullScreen: true, resolveOnCancel: true },
                        );
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.technical({ bind: 'sourceDocumentSysId' }),
            ui.nestedFields.technical({ bind: 'financeTransactionSysId' }),
        ],
    })
    sourceDocuments: ui.fields.Table<SelectFinanceTransactionLine>;

    async searchFinanceData() {
        this.financeTransactionHistory.value = [];

        const getOrderBy = (): OrderBy<FinanceTransaction> => ({ _createStamp: -1 });

        const financeTransactions = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-finance-data/FinanceTransaction')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            batchId: true,
                            documentNumber: true,
                            documentType: true,
                            documentSysId: true,
                            sourceDocumentNumber: true,
                            sourceDocumentType: true,
                            sourceDocumentSysId: true,
                            targetDocumentNumber: true,
                            targetDocumentType: true,
                            targetDocumentSysId: true,
                            status: true,
                            postingStatus: true,
                            message: true,
                            financeIntegrationApp: true,
                            financeIntegrationAppRecordId: true,
                            financeIntegrationAppUrl: true,
                            hasSourceForDimensionLines: true,
                            _createStamp: true,
                        },
                        { filter: this.getFilterFinance(), first: 500, orderBy: getOrderBy() },
                    ),
                )
                .execute(),
        );

        financeTransactions.forEach(financeTransactionHistoryLine => {
            this.financeTransactionHistory.addOrUpdateRecordValue({
                ...financeTransactionHistoryLine,
                originDocumentNumber:
                    financeTransactionHistoryLine.documentType === 'workInProgress'
                        ? financeTransactionHistoryLine.sourceDocumentNumber
                        : financeTransactionHistoryLine.documentNumber,
                originDocumentType:
                    financeTransactionHistoryLine.documentType === 'workInProgress'
                        ? ui.localizeEnumMember(
                              '@sage/xtrem-finance-data/SourceDocumentType',
                              financeTransactionHistoryLine.sourceDocumentType,
                          )
                        : ui.localizeEnumMember(
                              '@sage/xtrem-finance-data/FinanceDocumentType',
                              financeTransactionHistoryLine.documentType,
                          ),
                wasResent: false,
            });
        });
    }

    getFilterFinance(): Filter<FinanceTransaction> {
        const filter = { _and: [{}] };

        if (this.financeTransactionStatus.value && this.financeTransactionStatus.value.length > 0) {
            filter._and.push({ status: { _in: this.financeTransactionStatus.value } });
        }

        if (this.financeTransactionDocumentType.value && this.financeTransactionDocumentType.value.length > 0) {
            filter._and.push({ documentType: { _in: this.financeTransactionDocumentType.value } });
        }

        if (this.additionalRecordFilter) {
            filter._and.push(this.additionalRecordFilter);
        }

        return filter._and.length ? filter : {};
    }

    async loadSourceDocuments(financeTransactionSysId: string) {
        const documents = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-finance-data/FinanceTransactionLine')
                .query(
                    ui.queryUtils.edgesSelector<FinanceTransactionLine>(
                        {
                            _id: true,
                            sourceDocumentNumber: true,
                            sourceDocumentType: true,
                            sourceDocumentSysId: true,
                        },
                        { filter: { financeTransaction: financeTransactionSysId, isSourceForDimension: true } },
                    ),
                )
                .execute(),
        );

        documents.forEach(line => {
            this.sourceDocuments.addOrUpdateRecordValue({
                _id: line._id,
                sourceDocumentNumber: line.sourceDocumentNumber,
                sourceDocumentType: line.sourceDocumentType,
                sourceDocumentSysId: line.sourceDocumentSysId,
                financeTransactionSysId,
            });
        });
    }

    async selectFinanceTransactionLine(): Promise<Partial<SelectFinanceTransactionLine> | null> {
        if (this.sourceDocuments.value.length === 1) {
            return this.sourceDocuments.value[0];
        }

        await MasterDataUtils.confirmDialogToBoolean(
            this.$.dialog.custom('info', this.sourceDocumentsBlock, { cancelButton: { isHidden: true } }),
        );
        return null;
    }
}

declare module '@sage/xtrem-communication/build/lib/pages/sys-notification-history' {
    interface SysNotificationHistory extends SysNotificationHistoryExtension {}
}
