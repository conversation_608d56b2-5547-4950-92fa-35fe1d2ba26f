import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremCommunication from '@sage/xtrem-communication';
import * as xtremFinanceData from '@sage/xtrem-finance-data';

export const sysNotificationHistoryExtension = new ActivityExtension({
    extends: xtremCommunication.activities.sysNotificationHistory,
    __filename,
    permissions: [],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [() => xtremFinanceData.nodes.FinanceTransaction],
            },
            {
                operations: ['lookup'],
                on: [() => xtremFinanceData.nodes.FinanceTransactionLine],
            },
        ],
    },
});
