import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';

export async function saveBegin(
    basePaymentDocument: xtremFinanceData.nodes.BasePaymentDocument,
    typeToCheck: xtremMasterData.enums.BusinessRelationType,
): Promise<void> {
    const isTypeToCheck = (await basePaymentDocument.type) === typeToCheck;
    const totalCompanyAmount = await basePaymentDocument.lines.sum(async line => {
        const companyAmount = await line.companyAmount;
        const origin = await line.origin;
        return (origin === 'invoice' && isTypeToCheck) || (origin === 'creditMemo' && !isTypeToCheck)
            ? companyAmount
            : companyAmount * -1;
    });
    await basePaymentDocument.$.set({ companyAmount: totalCompanyAmount });
}
