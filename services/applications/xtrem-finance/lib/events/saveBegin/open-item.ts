import { NodeStatus } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { updateApArInvoicePaymentStatus } from '../../functions/payment-tracking';

// if the forced amount paid has changed, update the open item, the AP/AR invoice and the sales or purchasing document
export async function saveBegin(openItem: xtremFinanceData.nodes.BaseOpenItem): Promise<void> {
    if (
        (await openItem.$.context.isServiceOptionEnabled(xtremFinanceData.serviceOptions.paymentTrackingOption)) &&
        openItem.$.status !== NodeStatus.added
    ) {
        const oldForcedAmountPaid = (await (await openItem.$.old)?.forcedAmountPaid) ?? 0;
        const newForcedAmountPaid = (await openItem.forcedAmountPaid) ?? 0;
        if (oldForcedAmountPaid !== newForcedAmountPaid) {
            const deltaForcedAmount = newForcedAmountPaid - oldForcedAmountPaid;
            const transactionAmountDue = (await openItem.transactionAmountDue) ?? 0;
            const transactionAmountPaid = (await openItem.transactionAmountPaid) ?? 0;
            const newTransactionAmountPaid = transactionAmountPaid + deltaForcedAmount;
            const companyAmountDue = (await openItem.companyAmountDue) ?? 0;
            const companyAmountPaid = (await openItem.companyAmountPaid) ?? 0;
            const deltaCompanyAmount =
                transactionAmountDue !== companyAmountDue
                    ? companyAmountDue * (deltaForcedAmount / transactionAmountDue)
                    : deltaForcedAmount;
            const newCompanyAmountPaid =
                transactionAmountDue === newTransactionAmountPaid
                    ? companyAmountDue
                    : companyAmountPaid + deltaCompanyAmount;

            await openItem.$.set({
                transactionAmountPaid: newTransactionAmountPaid,
                companyAmountPaid: newCompanyAmountPaid,
                financialSiteAmountPaid: newCompanyAmountPaid,
            });

            // update the payment status on the accounts receivable invoice or accounts payable invoice
            const status = await openItem.status;
            const isArOpenItem = await updateApArInvoicePaymentStatus({
                context: openItem.$.context,
                openItem,
                status,
            });

            // send notification to update the sales or purchasing document status
            if (isArOpenItem) {
                await openItem.$.context.notify(
                    (await openItem.documentType) === 'salesInvoice'
                        ? 'SalesInvoice/updatePaymentStatus'
                        : 'SalesCreditMemo/updatePaymentStatus',
                    { _id: await openItem.documentSysId, paymentStatus: status },
                );
            } else {
                await openItem.$.context.notify(
                    (await openItem.documentType) === 'purchaseInvoice'
                        ? 'PurchaseInvoice/updatePaymentStatus'
                        : 'PurchaseCreditMemo/updatePaymentStatus',
                    { _id: await openItem.documentSysId, paymentStatus: status },
                );
            }
        }
    }
}
