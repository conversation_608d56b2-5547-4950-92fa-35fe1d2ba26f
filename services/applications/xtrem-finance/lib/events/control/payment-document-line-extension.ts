import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremFinance from '../../index';

/** Check if reference to AR open item is mandatory or not allowed. */
export async function controlArOpenItemReference(parameters: {
    line: xtremFinanceData.nodes.PaymentDocumentLine;
    val: xtremFinance.nodes.AccountsReceivableOpenItem | null;
    cx: ValidationContext;
}) {
    const document = await parameters.line.document;
    const businesRelationType = (await document.businessRelation).$.factory.name;
    if (parameters.val && businesRelationType !== 'Customer') {
        parameters.cx.error.addLocalized(
            '@sage/xtrem-finance/nodes__payment_document_line__ar_invoice_reference_not_allowed',
            'You cannot reference the accounts receivable invoice.',
        );
    }
    if (!parameters.val && businesRelationType === 'Customer') {
        parameters.cx.error.addLocalized(
            '@sage/xtrem-finance/nodes__payment_document_line__ar_invoice_reference_mandatory',
            'You need to add a reference to the accounts receivable invoice.',
        );
    }
}

/** Check if reference to AP open item is mandatory or not allowed. */
export async function controlApOpenItemReference(parameters: {
    line: xtremFinanceData.nodes.PaymentDocumentLine;
    val: xtremFinance.nodes.AccountsPayableOpenItem | null;
    cx: ValidationContext;
}) {
    const document = await parameters.line.document;
    const businesRelationType = (await document.businessRelation).$.factory.name;
    if (parameters.val && businesRelationType !== 'Supplier') {
        parameters.cx.error.addLocalized(
            '@sage/xtrem-finance/nodes__payment_document_line__ap_invoice_reference_not_allowed',
            'You cannot reference the accounts payable invoice.',
        );
    }
    if (!parameters.val && businesRelationType === 'Supplier') {
        parameters.cx.error.addLocalized(
            '@sage/xtrem-finance/nodes__payment_document_line__ap_invoice_reference_mandatory',
            'You need to add a reference to the accounts payable invoice.',
        );
    }
}
