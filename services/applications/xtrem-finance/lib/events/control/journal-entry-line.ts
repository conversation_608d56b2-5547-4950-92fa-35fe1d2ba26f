import type { ValidationContext, date } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremTax from '@sage/xtrem-tax';
import type * as xtremFinance from '../../index';

/** You need to enter the journal entry type on the line. */
export async function journalEntryTypeLineMandatoryOnNewDocuments(parameters: {
    line: xtremFinance.nodes.JournalEntryLine;
    entryTypeLine: xtremFinanceData.nodes.JournalEntryTypeLine | null;
    cx: ValidationContext;
}) {
    if (parameters.line.$.status === NodeStatus.added && !(await parameters.line.isBalanceLine)) {
        if (!parameters.entryTypeLine) {
            parameters.cx.error.addLocalized(
                '@sage/xtrem-finance/nodes__journal_entry_line__journal_entry_type_line_is_mandatory_on_new_documents',
                'You need to enter the journal entry type on the line.',
            );
        }
    }
}

/** Tax reference is mandatory. or Tax reference must be empty. */
export async function taxReferenceControls(parameters: {
    line: xtremFinance.nodes.JournalEntryLine;
    tax: xtremTax.nodes.Tax | null;
    cx: ValidationContext;
}) {
    if (await parameters.line.isMandatoryByTaxManagement()) {
        if (!parameters.tax) {
            parameters.cx.error.addLocalized(
                '@sage/xtrem-finance/nodes__journal_entry_line__tax_ref_mandatory',
                'Tax reference is mandatory.',
            );
        }
    } else if (parameters.tax) {
        parameters.cx.error.addLocalized(
            '@sage/xtrem-finance/nodes__journal_entry_line__tax_ref_empty',
            'Tax reference must be empty.',
            { errors: parameters.line.$.context.diagnoses },
        );
    }
}

/** Tax date is mandatory. or Tax date must be empty. */
export async function taxDateControls(parameters: {
    line: xtremFinance.nodes.JournalEntryLine;
    taxDate: date | null;
    cx: ValidationContext;
}) {
    if (await parameters.line.isMandatoryByTaxManagement()) {
        if (!parameters.taxDate) {
            parameters.cx.error.addLocalized(
                '@sage/xtrem-finance/nodes__journal_entry_line__tax_date_mandatory',
                'Tax date is mandatory.',
            );
        }
    } else if (parameters.taxDate) {
        parameters.cx.error.addLocalized(
            '@sage/xtrem-finance/nodes__journal_entry_line__tax_date_empty',
            'Tax date must be empty.',
        );
    }
}

/** The account {{account}} is not a control account. You cannot enter a business entity.
 * or
 * The account {{account}} is a control account. Enter a business entity. */
export async function businessEntityReferenceControls(parameters: {
    line: xtremFinance.nodes.JournalEntryLine;
    businessEntity: xtremMasterData.nodes.BusinessEntity | null;
    cx: ValidationContext;
}) {
    if (parameters.businessEntity && !(await (await parameters.line.account).isControl)) {
        parameters.cx.error.addLocalized(
            '@sage/xtrem-finance/nodes__journal_entry_line__business_entity_reference_not_possible',
            'The account {{account}} is not a control account. You cannot enter a business entity.',
            { account: await (await parameters.line.account).id },
        );
    }
    if (!parameters.businessEntity && (await (await parameters.line.account).isControl)) {
        parameters.cx.error.addLocalized(
            '@sage/xtrem-finance/nodes__journal_entry_line__business_entity_reference_mandatory',
            'The account {{account}} is a control account. Enter a business entity.',
            { account: await (await parameters.line.account).id },
        );
    }
}
