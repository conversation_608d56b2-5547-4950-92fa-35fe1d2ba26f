import type { ValidationContext } from '@sage/xtrem-core';
import { asyncArray } from '@sage/xtrem-core';
import * as xtremFinance from '../../index';

export async function controlEnd(journalEntry: xtremFinance.nodes.JournalEntry, cx: ValidationContext) {
    await xtremFinance.functions.controlMandatoryAttributesAndDimensions(journalEntry.$.context, cx, journalEntry);

    await cx.error
        .withMessage(
            '@sage/xtrem-finance/nodes__journal_entry__two_journal_entry_lines_mandatory',
            'The journal entry must contain at least two lines.',
        )
        .if(await journalEntry.lines.length)
        .is.less.than(2);

    await asyncArray(await journalEntry.financialSiteTransactionCurrencyUniqKey()).forEach(async line => {
        const financialSiteTransactionCurrencyLineBalance =
            await journalEntry.financialSiteTransactionCurrencyLineBalance(
                line.financialSite,
                line.transactionCurrency,
            );
        await journalEntry.$.context.logger.debugAsync(
            async () => `Financial site ${await line.financialSite.id} - Currency ${await line.transactionCurrency.id}`,
        );
        await cx.error
            .withMessage(
                '@sage/xtrem-finance/nodes__journal_entry__reconcile_journal_entry_by_key',
                'The journal is unbalanced for financial site {{financialSite}}',
                async () => ({ financialSite: await line.financialSite.id }),
            )
            .if(financialSiteTransactionCurrencyLineBalance)
            .is.not.zero();
    });
}
