import type { Collection } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import type * as xtremFinance from '../../index';

async function getContraJournalEntryLineFromBalanceLines(
    journalEntry: xtremFinance.nodes.JournalEntry,
): Promise<xtremFinance.nodes.JournalEntryLine | undefined> {
    const journalEntryLineIncludingTax = await journalEntry.lines.find(
        async line => (await (await line.account).taxManagement) === 'includingTax',
    );
    return journalEntryLineIncludingTax ?? journalEntry.lines.at(0);
}

async function getContraJournalEntryLineFromOtherLines(
    currentLine: xtremFinance.nodes.JournalEntryLine,
    otherLines: Collection<xtremFinance.nodes.JournalEntryLine>,
): Promise<xtremFinance.nodes.JournalEntryLine | undefined> {
    const lineAccountingStagingData = await (await currentLine.accountingStagingLines.at(0))?.accountingStaging;

    const contraJournalEntryLine = await otherLines.find(
        async otherLine =>
            (await (await currentLine.journalEntryTypeLine)?.contraJournalEntryTypeLine) ===
                (await otherLine.journalEntryTypeLine) &&
            (await lineAccountingStagingData?.baseDocumentLine)?._id ===
                (await (await (await otherLine.accountingStagingLines.at(0))?.accountingStaging)?.baseDocumentLine)
                    ?._id,
    );
    return (
        contraJournalEntryLine ??
        otherLines.find(async otherLine => {
            const otherLineAccountingStagingData = await (
                await otherLine.accountingStagingLines.at(0)
            )?.accountingStaging;
            return (
                (await (await currentLine.journalEntryTypeLine)?.contraJournalEntryTypeLine) ===
                    (await otherLine.journalEntryTypeLine) &&
                ((await lineAccountingStagingData?.baseDocumentLine)?._id ===
                    (await otherLineAccountingStagingData?.sourceBaseDocumentLine)?._id ||
                    (await lineAccountingStagingData?.sourceBaseDocumentLine)?._id ===
                        (await otherLineAccountingStagingData?.baseDocumentLine)?._id)
            );
        })
    );
}

export async function saveEnd(journalEntry: xtremFinance.nodes.JournalEntry) {
    await journalEntry.lines
        .filter(async line => !(await line.contraJournalEntryLine))
        .forEach(async line => {
            const contraJournalEntryLine = (await line.isBalanceLine)
                ? await getContraJournalEntryLineFromBalanceLines(journalEntry)
                : await getContraJournalEntryLineFromOtherLines(line, journalEntry.lines);
            if (!contraJournalEntryLine) {
                throw new BusinessRuleError(
                    journalEntry.$.context.localize(
                        '@sage/xtrem-finance/nodes__journal_entry__contra_journal_entry_line_is_mandatory_on_new_documents',
                        'You need to enter a contra journal entry on the line.',
                    ),
                );
            }
            await line.$.set({ contraJournalEntryLine });
        });
}
