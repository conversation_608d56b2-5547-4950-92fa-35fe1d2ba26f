import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';

// Check if the amount of the payment document is equal to the sum of the amounts of the lines
export async function controlBegin(options: {
    basePaymentDocument: xtremFinanceData.nodes.BasePaymentDocument;
    typeToCheck: xtremMasterData.enums.BusinessRelationType;
    cx: ValidationContext;
}): Promise<void> {
    const isTypeToCheck = (await options.basePaymentDocument.type) === options.typeToCheck;
    const totalAmount = await options.basePaymentDocument.lines.sum(async line => {
        const amount = await line.amount;
        const penaltyAmount = await line.penaltyAmount;
        const origin = await line.origin;
        return (origin === 'invoice' && isTypeToCheck) || (origin === 'creditMemo' && !isTypeToCheck)
            ? amount + penaltyAmount
            : (amount + penaltyAmount) * -1;
    });
    // error if the payment amount of the base payment document is different from total payment amount of the lines
    const basePaymentDocumentAmount = await options.basePaymentDocument.amount;
    if (basePaymentDocumentAmount !== totalAmount) {
        options.cx.error.addLocalized(
            '@sage/xtrem-finance/nodes__receipt__payment_amount_discrepancy',
            'The payment amount of the document: {{amount}}, needs to be the same as the total of payment amounts of all lines: {{total}}.',
            { amount: basePaymentDocumentAmount, total: totalAmount },
        );
    }
}
