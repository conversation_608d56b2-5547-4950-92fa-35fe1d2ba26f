import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremFinance from '../../index';

// Check if the linked AP or AR invoice is in status 'posted'
export async function checkApArInvoiceStatus(
    paymentDocumentLine: xtremFinance.nodeExtensions.PaymentDocumentLineExtension,
    cx: ValidationContext,
): Promise<void> {
    const isArPayment = await paymentDocumentLine.arOpenItem;
    const postingStatus = isArPayment
        ? await (
              await (
                  await paymentDocumentLine.arOpenItem
              )?.accountsReceivableInvoice
          )?.postingStatus
        : await (
              await (
                  await paymentDocumentLine.apOpenItem
              )?.accountsPayableInvoice
          )?.postingStatus;
    if (postingStatus !== 'posted') {
        if (isArPayment) {
            cx.error.addLocalized(
                '@sage/xtrem-finance/nodes__payment_document_line__wrong_ar_invoice_posting_status',
                'The posting status of the accounts receivable invoice needs to be posted.',
            );
        } else {
            cx.error.addLocalized(
                '@sage/xtrem-finance/nodes__payment_document_line__wrong_ap_invoice_posting_status',
                'The posting status of the accounts payable invoice needs to be posted.',
            );
        }
    }
}
