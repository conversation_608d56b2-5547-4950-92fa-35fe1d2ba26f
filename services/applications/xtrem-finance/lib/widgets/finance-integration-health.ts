import {
    getDocumentPageNameFinance,
    getTargetDocumentPageNameFinance,
} from '@sage/xtrem-finance-data/build/lib/shared-functions';
import * as ui from '@sage/xtrem-ui';

interface FinanceTransactionQueryResponse {
    cursor: string;
    node: {
        _id: string;
        status: string;
        documentNumber: string;
        documentType: string;
        _updateStamp: string;
        targetDocumentType: string;
        targetDocumentNumber: string;
        targetDocumentSysId: string;
        message: string;
        documentSysId: string;
    };
}

interface FinanceIntegrationStatus {
    _id: string;
    index: string;
    title: string;
    titleRight: string;
    line2: string;
    targetNumber: string;
    targetType: string;
    line2Right: string;
    line3: string;
    cursor: string;
}

@ui.widgets.table<FinanceIntegrationHealth>({
    title: 'Finance transaction status',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    category: 'Finance transactions',
    content() {
        const numberOfTransactions = this.$.data?.xtremFinanceData?.financeTransaction?.query?.edges?.length ?? 0;
        if (numberOfTransactions > 0) {
            return this.$.data.xtremFinanceData?.financeTransaction.query.edges.map(
                ({ node, cursor }: FinanceTransactionQueryResponse, index: number): FinanceIntegrationStatus => ({
                    _id: node._id ?? '',
                    index: `${index}`,
                    title: ui.localizeEnumMember('@sage/xtrem-finance-data/FinanceIntegrationStatus', node.status),
                    titleRight: node.documentNumber,
                    line2: ui.localizeEnumMember('@sage/xtrem-finance-data/FinanceDocumentType', node.documentType),
                    targetNumber: node.targetDocumentNumber,
                    targetType: ui.localizeEnumMember(
                        '@sage/xtrem-finance-data/TargetDocumentType',
                        node.targetDocumentType,
                    ),
                    line2Right: ui.formatDateToCurrentLocale(node._updateStamp),
                    line3: node.message,
                    cursor,
                }),
            );
        }
        return [];
    },
    callToActions: {
        seeAll: {
            title: 'See all',
            onClick() {
                this.$.router.goTo('@sage/xtrem-communication/SysNotificationHistory');
            },
        },
    },
    canSwitchViewMode: true,
    displayMode: 'table',
    canSelect: false,
    dataDropdownMenu: {
        orderBy: {
            status: { title: 'Sort by status' },
            documentNumber: { title: 'Sort by document number' },
            updateStamp: { title: 'Sort by last status update' },
        },
    },
    rowDefinition: {
        title: {
            title: 'Status',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const { status } = this.getDataFromLineIndex(_id);
                    switch (status) {
                        case 'failed':
                            return 'warning';
                        case 'error':
                            return 'negative';
                        default:
                            return 'neutral';
                    }
                },
            },
        },
        titleRight: {
            title: 'Document number',
            renderedAs: 'link',
            onClick(_id: string) {
                const { documentType, documentSysId } = this.getDataFromLineIndex(_id);
                const documentPageName = getDocumentPageNameFinance({ type: documentType });
                this.$.router.goTo(documentPageName, { _id: documentSysId });
            },
        },
        line2: { title: 'Document type' },
        targetNumber: {
            title: 'Target document number',
            renderedAs: 'link',
            onClick(_id: string) {
                const { targetDocumentType, targetDocumentSysId } = this.getDataFromLineIndex(_id);
                const documentPageName = getTargetDocumentPageNameFinance(targetDocumentType);
                this.$.router.goTo(documentPageName, { _id: targetDocumentSysId });
            },
        },
        targetType: { title: 'Target document type' },
        line2Right: { title: 'Last status update' },
        line3: {
            title: 'Message',
        },
    },
    getQuery(args) {
        const filter = { status: { _in: ['error', 'failed', 'notRecorded', 'toBeRecorded', 'recording'] } };

        const orderBy: { status?: number; documentNumber?: number; _updateStamp?: number } = {};
        if (!this.$.options.dataOptions?.orderBy || this.$.options.dataOptions?.orderBy === 'status') {
            orderBy.status = 1;
        }

        if (this.$.options.dataOptions?.orderBy === 'documentNumber') {
            orderBy.documentNumber = 1;
        }

        if (this.$.options.dataOptions?.orderBy === 'updateStamp') {
            orderBy._updateStamp = 1;
        }

        return {
            xtremFinanceData: {
                financeTransaction: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify(orderBy),
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            cursor: true,
                            node: {
                                _id: true,
                                status: true,
                                documentNumber: true,
                                documentType: true,
                                _updateStamp: true,
                                targetDocumentType: true,
                                targetDocumentNumber: true,
                                targetDocumentSysId: true,
                                message: true,
                                documentSysId: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class FinanceIntegrationHealth extends ui.widgets.TableWidget {
    private getDataFromLineIndex(_id: string) {
        return this.$.data.xtremFinanceData?.financeTransaction.query.edges.find(
            (edge: FinanceTransactionQueryResponse) => edge.node._id === _id,
        ).node;
    }
}
