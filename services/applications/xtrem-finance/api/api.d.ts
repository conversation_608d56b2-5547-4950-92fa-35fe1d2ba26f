declare module '@sage/xtrem-finance-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type {
        GroupRoleSite,
        Package as SageXtremAuthorization$Package,
        SiteGroup,
    } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type {
        Account,
        AccountingStaging,
        AccountsPayableInvoiceLineStaging,
        AccountsPayableInvoiceLineStagingBinding,
        AccountsPayableInvoiceLineStagingInput,
        AccountsReceivableInvoiceLineStaging,
        AccountsReceivableInvoiceLineStagingBinding,
        AccountsReceivableInvoiceLineStagingInput,
        AnalyticalData,
        Attribute,
        AttributeType,
        BankAccount,
        BaseOpenItem,
        BasePaymentDocument,
        CloseReason,
        Dimension,
        DimensionType,
        FinanceTransaction,
        Journal,
        JournalEntryTypeLine,
        Package as SageXtremFinanceData$Package,
        PaymentDocumentLine,
        PaymentDocumentLineBinding,
        PaymentDocumentLineInput,
        PaymentTracking,
        PostingClass,
    } from '@sage/xtrem-finance-data-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type {
        LandedCostItem,
        LandedCostItemBinding,
        LandedCostItemInput,
        Package as SageXtremLandedCost$Package,
    } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        BaseBusinessRelation,
        BomRevisionSequence,
        BusinessEntity,
        BusinessEntityAddress,
        BusinessEntityAddressBinding,
        BusinessEntityAddressInput,
        BusinessEntityContact,
        BusinessEntityContactBinding,
        BusinessEntityContactInput,
        BusinessEntityInput,
        Currency,
        Customer,
        CustomerBinding,
        CustomerInput,
        Item,
        ItemAllergen,
        ItemAllergenBinding,
        ItemAllergenInput,
        ItemCategory,
        ItemClassifications,
        ItemClassificationsBinding,
        ItemClassificationsInput,
        ItemCustomer,
        ItemCustomerInput,
        ItemCustomerPrice,
        ItemCustomerPriceBinding,
        ItemCustomerPriceInput,
        ItemSite,
        ItemSiteBinding,
        ItemSiteInput,
        ItemSupplier,
        ItemSupplierInput,
        ItemSupplierPrice,
        ItemSupplierPriceBinding,
        ItemSupplierPriceInput,
        Location,
        Package as SageXtremMasterData$Package,
        PaymentTerm,
        SequenceNumber,
        Supplier,
        SupplierBinding,
        SupplierInput,
        UnitOfMeasure,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type { Package as SageXtremStockData$Package, StockStatus } from '@sage/xtrem-stock-data-api';
    import type { ChartOfAccount, Country, Package as SageXtremStructure$Package } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type {
        Company,
        Package as SageXtremSystem$Package,
        Site,
        SiteBinding,
        SiteInput,
        User,
    } from '@sage/xtrem-system-api';
    import type { BaseTax, ItemTaxGroup, Package as SageXtremTax$Package, Tax, TaxCategory } from '@sage/xtrem-tax-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface DatevExportStatus$Enum {
        draft: 0;
        extracted: 1;
        exported: 2;
        error: 3;
        extractionInProgress: 4;
        exportInProgress: 5;
    }
    export type DatevExportStatus = keyof DatevExportStatus$Enum;
    export interface AccountingInterfaceListener extends ClientNode {}
    export interface AccountingInterfaceListenerInput extends ClientNodeInput {}
    export interface AccountingInterfaceListenerBinding extends ClientNode {}
    export interface AccountingInterfaceListener$Mutations {
        retryFinanceDocument: Node$Operation<
            {
                financeTransaction?: string;
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        createJournalsFromAccountingStaging: Node$Operation<
            {
                journalsCreatedData: boolean | string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountingInterfaceListener$AsyncOperations {
        createJournalsFromAccountingStagingJob: AsyncOperation<
            {
                journalsCreatedData: boolean | string;
                filter?: string;
                batchTrackingId?: string;
            },
            string
        >;
    }
    export interface AccountingInterfaceListener$Operations {
        mutations: AccountingInterfaceListener$Mutations;
        asyncOperations: AccountingInterfaceListener$AsyncOperations;
        getDefaults: GetDefaultsOperation<AccountingInterfaceListener>;
    }
    export interface AccountsOpenItem extends ClientNode {}
    export interface AccountsOpenItemInput extends ClientNodeInput {}
    export interface AccountsOpenItemBinding extends ClientNode {}
    export interface AccountsOpenItem$Operations {
        create: CreateOperation<AccountsOpenItemInput, AccountsOpenItem>;
        getDuplicate: GetDuplicateOperation<AccountsOpenItem>;
        update: UpdateOperation<AccountsOpenItemInput, AccountsOpenItem>;
        updateById: UpdateByIdOperation<AccountsOpenItemInput, AccountsOpenItem>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        getDefaults: GetDefaultsOperation<AccountsOpenItem>;
    }
    export interface AccountsPayableInvoice extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        financialSite: Site;
        financialSiteName: string;
        financialSiteTaxIdNumber: string;
        type: FinanceDocumentType;
        number: string;
        invoiceDate: string;
        postingDate: string;
        billBySupplier: Supplier;
        billBySupplierName: string;
        billBySupplierTaxIdNumber: string;
        payToSupplier: Supplier;
        payToSupplierLinkedAddress: BusinessEntityAddress;
        returnLinkedAddress: BusinessEntityAddress;
        currency: Currency;
        paymentTerm: PaymentTerm;
        postingStatus: JournalStatus;
        origin: AccountsPayableReceivableInvoiceOrigin;
        supplierDocumentNumber: string;
        supplierDocumentDate: string;
        reference: string;
        description: string;
        account: Account;
        dueDate: string;
        fxRateDate: string;
        totalCompanyAmountExcludingTax: string;
        totalCompanyAmountIncludingTax: string;
        totalCompanyTaxAmount: string;
        rateDescription: string;
        taxCalculationStatus: TaxCalculationStatus;
        purchaseDocumentNumber: string;
        purchaseDocumentSysId: integer;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsPayableInvoiceLine>;
        openItems: ClientCollection<AccountsPayableOpenItem>;
        taxes: ClientCollection<AccountsPayableInvoiceTax>;
        transactionCurrency: Currency;
        documentDate: string;
        postingDetails: ClientCollection<FinanceTransaction>;
        paymentTracking: PaymentTracking;
        totalAmountExcludingTax: string;
        totalTaxAmount: string;
        totalTaxAmountAdjusted: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountIncludingTax: string;
    }
    export interface AccountsPayableInvoiceInput extends ClientNodeInput {
        financialSite?: integer | string;
        financialSiteName?: string;
        financialSiteTaxIdNumber?: string;
        type?: FinanceDocumentType;
        number?: string;
        invoiceDate?: string;
        postingDate?: string;
        billBySupplier?: integer | string;
        billBySupplierName?: string;
        billBySupplierTaxIdNumber?: string;
        payToSupplier?: integer | string;
        payToSupplierLinkedAddress?: integer | string;
        returnLinkedAddress?: integer | string;
        currency?: integer | string;
        paymentTerm?: integer | string;
        postingStatus?: JournalStatus;
        origin?: AccountsPayableReceivableInvoiceOrigin;
        supplierDocumentNumber?: string;
        supplierDocumentDate?: string;
        reference?: string;
        description?: string;
        account?: integer | string;
        dueDate?: string;
        fxRateDate?: string;
        taxCalculationStatus?: TaxCalculationStatus;
        purchaseDocumentNumber?: string;
        purchaseDocumentSysId?: integer | string;
        lines?: Partial<AccountsPayableInvoiceLineInput>[];
        taxes?: Partial<AccountsPayableInvoiceTaxInput>[];
        paymentTracking?: integer | string;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
    }
    export interface AccountsPayableInvoiceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        financialSite: Site;
        financialSiteName: string;
        financialSiteTaxIdNumber: string;
        type: FinanceDocumentType;
        number: string;
        invoiceDate: string;
        postingDate: string;
        billBySupplier: Supplier;
        billBySupplierName: string;
        billBySupplierTaxIdNumber: string;
        payToSupplier: Supplier;
        payToSupplierLinkedAddress: BusinessEntityAddress;
        returnLinkedAddress: BusinessEntityAddress;
        currency: Currency;
        paymentTerm: PaymentTerm;
        postingStatus: JournalStatus;
        origin: AccountsPayableReceivableInvoiceOrigin;
        supplierDocumentNumber: string;
        supplierDocumentDate: string;
        reference: string;
        description: string;
        account: Account;
        dueDate: string;
        fxRateDate: string;
        totalCompanyAmountExcludingTax: string;
        totalCompanyAmountIncludingTax: string;
        totalCompanyTaxAmount: string;
        rateDescription: string;
        taxCalculationStatus: TaxCalculationStatus;
        purchaseDocumentNumber: string;
        purchaseDocumentSysId: integer;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsPayableInvoiceLineBinding>;
        openItems: ClientCollection<AccountsPayableOpenItem>;
        taxes: ClientCollection<AccountsPayableInvoiceTaxBinding>;
        transactionCurrency: Currency;
        documentDate: string;
        postingDetails: ClientCollection<FinanceTransaction>;
        paymentTracking: PaymentTracking;
        totalAmountExcludingTax: string;
        totalTaxAmount: string;
        totalTaxAmountAdjusted: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountIncludingTax: string;
    }
    export interface AccountsPayableInvoice$Mutations {
        post: Node$Operation<
            {
                apInvoice: string;
            },
            string
        >;
        resendNotificationForFinance: Node$Operation<
            {
                apInvoice: string;
            },
            boolean
        >;
    }
    export interface AccountsPayableInvoice$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsPayableInvoice$Lookups {
        financialSite: QueryOperation<Site>;
        billBySupplier: QueryOperation<Supplier>;
        payToSupplier: QueryOperation<Supplier>;
        payToSupplierLinkedAddress: QueryOperation<BusinessEntityAddress>;
        returnLinkedAddress: QueryOperation<BusinessEntityAddress>;
        currency: QueryOperation<Currency>;
        paymentTerm: QueryOperation<PaymentTerm>;
        account: QueryOperation<Account>;
        transactionCurrency: QueryOperation<Currency>;
        paymentTracking: QueryOperation<PaymentTracking>;
    }
    export interface AccountsPayableInvoice$Operations {
        query: QueryOperation<AccountsPayableInvoice>;
        read: ReadOperation<AccountsPayableInvoice>;
        aggregate: {
            read: AggregateReadOperation<AccountsPayableInvoice>;
            query: AggregateQueryOperation<AccountsPayableInvoice>;
        };
        mutations: AccountsPayableInvoice$Mutations;
        asyncOperations: AccountsPayableInvoice$AsyncOperations;
        lookups(dataOrId: string | { data: AccountsPayableInvoiceInput }): AccountsPayableInvoice$Lookups;
        getDefaults: GetDefaultsOperation<AccountsPayableInvoice>;
    }
    export interface AccountsPayableInvoiceLineDimension extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        originLine: AccountsPayableInvoiceLine;
        storedAttributes: string;
        storedDimensions: string;
        analyticalData: AnalyticalData;
        hasAttributesOrDimenionsChanged: boolean;
        amount: string;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        financialSite: Site;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: Customer;
        supplier: Supplier;
        project: Attribute;
        employee: Attribute;
        task: Attribute;
        item: Item;
    }
    export interface AccountsPayableInvoiceLineDimensionInput extends VitalClientNodeInput {
        storedAttributes?: string;
        storedDimensions?: string;
        analyticalData?: integer | string;
        hasAttributesOrDimenionsChanged?: boolean | string;
        amount?: decimal | string;
    }
    export interface AccountsPayableInvoiceLineDimensionBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        originLine: AccountsPayableInvoiceLine;
        storedAttributes: any;
        storedDimensions: any;
        analyticalData: AnalyticalData;
        hasAttributesOrDimenionsChanged: boolean;
        amount: string;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        financialSite: Site;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: Customer;
        supplier: Supplier;
        project: Attribute;
        employee: Attribute;
        task: Attribute;
        item: Item;
    }
    export interface AccountsPayableInvoiceLineDimension$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsPayableInvoiceLineDimension$Lookups {
        analyticalData: QueryOperation<AnalyticalData>;
        dimension01: QueryOperation<Dimension>;
        dimension02: QueryOperation<Dimension>;
        dimension03: QueryOperation<Dimension>;
        dimension04: QueryOperation<Dimension>;
        dimension05: QueryOperation<Dimension>;
        dimension06: QueryOperation<Dimension>;
        dimension07: QueryOperation<Dimension>;
        dimension08: QueryOperation<Dimension>;
        dimension09: QueryOperation<Dimension>;
        dimension10: QueryOperation<Dimension>;
        dimension11: QueryOperation<Dimension>;
        dimension12: QueryOperation<Dimension>;
        dimension13: QueryOperation<Dimension>;
        dimension14: QueryOperation<Dimension>;
        dimension15: QueryOperation<Dimension>;
        dimension16: QueryOperation<Dimension>;
        dimension17: QueryOperation<Dimension>;
        dimension18: QueryOperation<Dimension>;
        dimension19: QueryOperation<Dimension>;
        dimension20: QueryOperation<Dimension>;
        financialSite: QueryOperation<Site>;
        businessSite: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        manufacturingSite: QueryOperation<Site>;
        customer: QueryOperation<Customer>;
        supplier: QueryOperation<Supplier>;
        project: QueryOperation<Attribute>;
        employee: QueryOperation<Attribute>;
        task: QueryOperation<Attribute>;
        item: QueryOperation<Item>;
    }
    export interface AccountsPayableInvoiceLineDimension$Operations {
        query: QueryOperation<AccountsPayableInvoiceLineDimension>;
        read: ReadOperation<AccountsPayableInvoiceLineDimension>;
        aggregate: {
            read: AggregateReadOperation<AccountsPayableInvoiceLineDimension>;
            query: AggregateQueryOperation<AccountsPayableInvoiceLineDimension>;
        };
        asyncOperations: AccountsPayableInvoiceLineDimension$AsyncOperations;
        lookups(
            dataOrId: string | { data: AccountsPayableInvoiceLineDimensionInput },
        ): AccountsPayableInvoiceLineDimension$Lookups;
        getDefaults: GetDefaultsOperation<AccountsPayableInvoiceLineDimension>;
    }
    export interface AccountsReceivableAdvance extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        bankAccount: BankAccount;
        financialSite: Site;
        financialSiteName: string;
        payToCustomerId: string;
        payToCustomerName: string;
        reference: string;
        description: string;
        postingDate: string;
        paymentDate: string;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        fxRateDate: string;
        advanceAmount: string;
        advanceCompanyAmount: string;
        postingStatus: JournalStatus;
        financeIntegrationStatus: PostingStatus;
        financeIntegrationApp: FinanceIntegrationApp;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsReceivableAdvanceLine>;
    }
    export interface AccountsReceivableAdvanceInput extends ClientNodeInput {
        number?: string;
        bankAccount?: integer | string;
        financialSite?: integer | string;
        financialSiteName?: string;
        payToCustomerId?: string;
        payToCustomerName?: string;
        reference?: string;
        description?: string;
        postingDate?: string;
        paymentDate?: string;
        currency?: integer | string;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        fxRateDate?: string;
        advanceAmount?: decimal | string;
        advanceCompanyAmount?: decimal | string;
        postingStatus?: JournalStatus;
        lines?: Partial<AccountsReceivableAdvanceLineInput>[];
    }
    export interface AccountsReceivableAdvanceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        bankAccount: BankAccount;
        financialSite: Site;
        financialSiteName: string;
        payToCustomerId: string;
        payToCustomerName: string;
        reference: string;
        description: string;
        postingDate: string;
        paymentDate: string;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        fxRateDate: string;
        advanceAmount: string;
        advanceCompanyAmount: string;
        postingStatus: JournalStatus;
        financeIntegrationStatus: PostingStatus;
        financeIntegrationApp: FinanceIntegrationApp;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsReceivableAdvanceLineBinding>;
    }
    export interface AccountsReceivableAdvance$Mutations {
        post: Node$Operation<
            {
                arAdvance: string;
            },
            string
        >;
    }
    export interface AccountsReceivableAdvance$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsReceivableAdvance$Lookups {
        bankAccount: QueryOperation<BankAccount>;
        financialSite: QueryOperation<Site>;
        currency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface AccountsReceivableAdvance$Operations {
        query: QueryOperation<AccountsReceivableAdvance>;
        read: ReadOperation<AccountsReceivableAdvance>;
        aggregate: {
            read: AggregateReadOperation<AccountsReceivableAdvance>;
            query: AggregateQueryOperation<AccountsReceivableAdvance>;
        };
        update: UpdateOperation<AccountsReceivableAdvanceInput, AccountsReceivableAdvance>;
        updateById: UpdateByIdOperation<AccountsReceivableAdvanceInput, AccountsReceivableAdvance>;
        mutations: AccountsReceivableAdvance$Mutations;
        asyncOperations: AccountsReceivableAdvance$AsyncOperations;
        lookups(dataOrId: string | { data: AccountsReceivableAdvanceInput }): AccountsReceivableAdvance$Lookups;
        getDefaults: GetDefaultsOperation<AccountsReceivableAdvance>;
    }
    export interface AccountsReceivableInvoice extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        financialSite: Site;
        taxEngine: TaxEngine;
        financialSiteName: string;
        financialSiteTaxIdNumber: string;
        type: FinanceDocumentType;
        number: string;
        invoiceDate: string;
        postingDate: string;
        billToCustomer: Customer;
        billToCustomerName: string;
        billToCustomerTaxIdNumber: string;
        currency: Currency;
        paymentTerm: PaymentTerm;
        postingStatus: JournalStatus;
        paymentStatus: OpenItemStatus;
        isPrinted: boolean;
        origin: AccountsPayableReceivableInvoiceOrigin;
        reference: string;
        description: string;
        account: Account;
        dueDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        totalTaxAmountAdjusted: string;
        fxRateDate: string;
        totalCompanyAmountExcludingTax: string;
        totalCompanyAmountIncludingTax: string;
        totalCompanyTaxAmount: string;
        rateDescription: string;
        salesDocumentNumber: string;
        salesDocumentSysId: integer;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsReceivableInvoiceLine>;
        openItems: ClientCollection<AccountsReceivableOpenItem>;
        taxes: ClientCollection<AccountsReceivableInvoiceTax>;
        transactionCurrency: Currency;
        documentDate: string;
        postingDetails: ClientCollection<FinanceTransaction>;
        totalAmountExcludingTax: string;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountIncludingTax: string;
    }
    export interface AccountsReceivableInvoiceInput extends ClientNodeInput {
        financialSite?: integer | string;
        financialSiteName?: string;
        financialSiteTaxIdNumber?: string;
        type?: FinanceDocumentType;
        number?: string;
        invoiceDate?: string;
        postingDate?: string;
        billToCustomer?: integer | string;
        billToCustomerName?: string;
        billToCustomerTaxIdNumber?: string;
        currency?: integer | string;
        paymentTerm?: integer | string;
        postingStatus?: JournalStatus;
        paymentStatus?: OpenItemStatus;
        isPrinted?: boolean | string;
        origin?: AccountsPayableReceivableInvoiceOrigin;
        reference?: string;
        description?: string;
        account?: integer | string;
        dueDate?: string;
        taxCalculationStatus?: TaxCalculationStatus;
        totalTaxAmountAdjusted?: decimal | string;
        fxRateDate?: string;
        salesDocumentNumber?: string;
        salesDocumentSysId?: integer | string;
        lines?: Partial<AccountsReceivableInvoiceLineInput>[];
        taxes?: Partial<AccountsReceivableInvoiceTaxInput>[];
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
    }
    export interface AccountsReceivableInvoiceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        financialSite: Site;
        taxEngine: TaxEngine;
        financialSiteName: string;
        financialSiteTaxIdNumber: string;
        type: FinanceDocumentType;
        number: string;
        invoiceDate: string;
        postingDate: string;
        billToCustomer: Customer;
        billToCustomerName: string;
        billToCustomerTaxIdNumber: string;
        currency: Currency;
        paymentTerm: PaymentTerm;
        postingStatus: JournalStatus;
        paymentStatus: OpenItemStatus;
        isPrinted: boolean;
        origin: AccountsPayableReceivableInvoiceOrigin;
        reference: string;
        description: string;
        account: Account;
        dueDate: string;
        taxCalculationStatus: TaxCalculationStatus;
        totalTaxAmountAdjusted: string;
        fxRateDate: string;
        totalCompanyAmountExcludingTax: string;
        totalCompanyAmountIncludingTax: string;
        totalCompanyTaxAmount: string;
        rateDescription: string;
        salesDocumentNumber: string;
        salesDocumentSysId: integer;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsReceivableInvoiceLineBinding>;
        openItems: ClientCollection<AccountsReceivableOpenItem>;
        taxes: ClientCollection<AccountsReceivableInvoiceTaxBinding>;
        transactionCurrency: Currency;
        documentDate: string;
        postingDetails: ClientCollection<FinanceTransaction>;
        totalAmountExcludingTax: string;
        totalTaxAmount: string;
        totalTaxableAmount: string;
        totalExemptAmount: string;
        companyFxRate: string;
        companyFxRateDivisor: string;
        totalAmountIncludingTax: string;
    }
    export interface AccountsReceivableInvoice$Mutations {
        post: Node$Operation<
            {
                arInvoice: string;
            },
            string
        >;
        resendNotificationForFinance: Node$Operation<
            {
                arInvoice: string;
            },
            boolean
        >;
    }
    export interface AccountsReceivableInvoice$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsReceivableInvoice$Lookups {
        financialSite: QueryOperation<Site>;
        billToCustomer: QueryOperation<Customer>;
        currency: QueryOperation<Currency>;
        paymentTerm: QueryOperation<PaymentTerm>;
        account: QueryOperation<Account>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface AccountsReceivableInvoice$Operations {
        query: QueryOperation<AccountsReceivableInvoice>;
        read: ReadOperation<AccountsReceivableInvoice>;
        aggregate: {
            read: AggregateReadOperation<AccountsReceivableInvoice>;
            query: AggregateQueryOperation<AccountsReceivableInvoice>;
        };
        mutations: AccountsReceivableInvoice$Mutations;
        asyncOperations: AccountsReceivableInvoice$AsyncOperations;
        lookups(dataOrId: string | { data: AccountsReceivableInvoiceInput }): AccountsReceivableInvoice$Lookups;
        getDefaults: GetDefaultsOperation<AccountsReceivableInvoice>;
    }
    export interface AccountsReceivableInvoiceLineDimension extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        originLine: AccountsReceivableInvoiceLine;
        storedAttributes: string;
        storedDimensions: string;
        analyticalData: AnalyticalData;
        hasAttributesOrDimenionsChanged: boolean;
        amount: string;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        financialSite: Site;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: Customer;
        supplier: Supplier;
        project: Attribute;
        employee: Attribute;
        task: Attribute;
        item: Item;
    }
    export interface AccountsReceivableInvoiceLineDimensionInput extends VitalClientNodeInput {
        storedAttributes?: string;
        storedDimensions?: string;
        analyticalData?: integer | string;
        hasAttributesOrDimenionsChanged?: boolean | string;
        amount?: decimal | string;
    }
    export interface AccountsReceivableInvoiceLineDimensionBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        originLine: AccountsReceivableInvoiceLine;
        storedAttributes: any;
        storedDimensions: any;
        analyticalData: AnalyticalData;
        hasAttributesOrDimenionsChanged: boolean;
        amount: string;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        financialSite: Site;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: Customer;
        supplier: Supplier;
        project: Attribute;
        employee: Attribute;
        task: Attribute;
        item: Item;
    }
    export interface AccountsReceivableInvoiceLineDimension$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsReceivableInvoiceLineDimension$Lookups {
        analyticalData: QueryOperation<AnalyticalData>;
        dimension01: QueryOperation<Dimension>;
        dimension02: QueryOperation<Dimension>;
        dimension03: QueryOperation<Dimension>;
        dimension04: QueryOperation<Dimension>;
        dimension05: QueryOperation<Dimension>;
        dimension06: QueryOperation<Dimension>;
        dimension07: QueryOperation<Dimension>;
        dimension08: QueryOperation<Dimension>;
        dimension09: QueryOperation<Dimension>;
        dimension10: QueryOperation<Dimension>;
        dimension11: QueryOperation<Dimension>;
        dimension12: QueryOperation<Dimension>;
        dimension13: QueryOperation<Dimension>;
        dimension14: QueryOperation<Dimension>;
        dimension15: QueryOperation<Dimension>;
        dimension16: QueryOperation<Dimension>;
        dimension17: QueryOperation<Dimension>;
        dimension18: QueryOperation<Dimension>;
        dimension19: QueryOperation<Dimension>;
        dimension20: QueryOperation<Dimension>;
        financialSite: QueryOperation<Site>;
        businessSite: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        manufacturingSite: QueryOperation<Site>;
        customer: QueryOperation<Customer>;
        supplier: QueryOperation<Supplier>;
        project: QueryOperation<Attribute>;
        employee: QueryOperation<Attribute>;
        task: QueryOperation<Attribute>;
        item: QueryOperation<Item>;
    }
    export interface AccountsReceivableInvoiceLineDimension$Operations {
        query: QueryOperation<AccountsReceivableInvoiceLineDimension>;
        read: ReadOperation<AccountsReceivableInvoiceLineDimension>;
        aggregate: {
            read: AggregateReadOperation<AccountsReceivableInvoiceLineDimension>;
            query: AggregateQueryOperation<AccountsReceivableInvoiceLineDimension>;
        };
        asyncOperations: AccountsReceivableInvoiceLineDimension$AsyncOperations;
        lookups(
            dataOrId: string | { data: AccountsReceivableInvoiceLineDimensionInput },
        ): AccountsReceivableInvoiceLineDimension$Lookups;
        getDefaults: GetDefaultsOperation<AccountsReceivableInvoiceLineDimension>;
    }
    export interface AccountsReceivablePayment extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        bankAccount: BankAccount;
        financialSite: Site;
        financialSiteName: string;
        payToCustomerId: string;
        payToCustomerName: string;
        reference: string;
        description: string;
        postingDate: string;
        paymentDate: string;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        fxRateDate: string;
        paymentAmount: string;
        paymentCompanyAmount: string;
        postingStatus: JournalStatus;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsReceivablePaymentLine>;
    }
    export interface AccountsReceivablePaymentInput extends ClientNodeInput {
        number?: string;
        bankAccount?: integer | string;
        financialSite?: integer | string;
        financialSiteName?: string;
        payToCustomerId?: string;
        payToCustomerName?: string;
        reference?: string;
        description?: string;
        postingDate?: string;
        paymentDate?: string;
        currency?: integer | string;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        fxRateDate?: string;
        paymentAmount?: decimal | string;
        paymentCompanyAmount?: decimal | string;
        postingStatus?: JournalStatus;
        lines?: Partial<AccountsReceivablePaymentLineInput>[];
    }
    export interface AccountsReceivablePaymentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        bankAccount: BankAccount;
        financialSite: Site;
        financialSiteName: string;
        payToCustomerId: string;
        payToCustomerName: string;
        reference: string;
        description: string;
        postingDate: string;
        paymentDate: string;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        fxRateDate: string;
        paymentAmount: string;
        paymentCompanyAmount: string;
        postingStatus: JournalStatus;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<AccountsReceivablePaymentLineBinding>;
    }
    export interface AccountsReceivablePayment$Mutations {
        post: Node$Operation<
            {
                arPayment: string;
            },
            string
        >;
    }
    export interface AccountsReceivablePayment$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsReceivablePayment$Lookups {
        bankAccount: QueryOperation<BankAccount>;
        financialSite: QueryOperation<Site>;
        currency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface AccountsReceivablePayment$Operations {
        query: QueryOperation<AccountsReceivablePayment>;
        read: ReadOperation<AccountsReceivablePayment>;
        aggregate: {
            read: AggregateReadOperation<AccountsReceivablePayment>;
            query: AggregateQueryOperation<AccountsReceivablePayment>;
        };
        mutations: AccountsReceivablePayment$Mutations;
        asyncOperations: AccountsReceivablePayment$AsyncOperations;
        lookups(dataOrId: string | { data: AccountsReceivablePaymentInput }): AccountsReceivablePayment$Lookups;
        getDefaults: GetDefaultsOperation<AccountsReceivablePayment>;
    }
    export interface DatevExport extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        company: Company;
        status: DatevExportStatus;
        timeStamp: string;
        datevConsultantNumber: integer;
        datevCustomerNumber: integer;
        skrCoa: integer;
        dateRange: string;
        startDate: string;
        endDate: string;
        fiscalYearStart: string;
        isLocked: boolean;
        doAccounts: boolean;
        doCustomersSuppliers: boolean;
        doJournalEntries: boolean;
        dimensionType1: DimensionType;
        attributeType1: AttributeType;
        dimensionType2: DimensionType;
        attributeType2: AttributeType;
        datevExportAccounts: ClientCollection<DatevExportAccount>;
        datevExportBusinessRelations: ClientCollection<DatevExportBusinessRelation>;
        datevExportJournalEntryLines: ClientCollection<DatevExportJournalEntryLine>;
        accountsWithoutDatevId: ClientCollection<Account>;
        customersWithoutDatevId: ClientCollection<Customer>;
        suppliersWithoutDatevId: ClientCollection<Supplier>;
    }
    export interface DatevExportInput extends ClientNodeInput {
        id?: string;
        company?: integer | string;
        status?: DatevExportStatus;
        timeStamp?: string;
        datevConsultantNumber?: integer | string;
        datevCustomerNumber?: integer | string;
        skrCoa?: integer | string;
        dateRange?: string;
        fiscalYearStart?: string;
        isLocked?: boolean | string;
        doAccounts?: boolean | string;
        doCustomersSuppliers?: boolean | string;
        doJournalEntries?: boolean | string;
        dimensionType1?: integer | string;
        attributeType1?: integer | string;
        dimensionType2?: integer | string;
        attributeType2?: integer | string;
        datevExportAccounts?: Partial<DatevExportAccountInput>[];
        datevExportBusinessRelations?: Partial<DatevExportBusinessRelationInput>[];
        datevExportJournalEntryLines?: Partial<DatevExportJournalEntryLineInput>[];
    }
    export interface DatevExportBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        company: Company;
        status: DatevExportStatus;
        timeStamp: string;
        datevConsultantNumber: integer;
        datevCustomerNumber: integer;
        skrCoa: integer;
        dateRange: string;
        startDate: string;
        endDate: string;
        fiscalYearStart: string;
        isLocked: boolean;
        doAccounts: boolean;
        doCustomersSuppliers: boolean;
        doJournalEntries: boolean;
        dimensionType1: DimensionType;
        attributeType1: AttributeType;
        dimensionType2: DimensionType;
        attributeType2: AttributeType;
        datevExportAccounts: ClientCollection<DatevExportAccountBinding>;
        datevExportBusinessRelations: ClientCollection<DatevExportBusinessRelationBinding>;
        datevExportJournalEntryLines: ClientCollection<DatevExportJournalEntryLineBinding>;
        accountsWithoutDatevId: ClientCollection<Account>;
        customersWithoutDatevId: ClientCollection<Customer>;
        suppliersWithoutDatevId: ClientCollection<Supplier>;
    }
    export interface DatevExport$AsyncOperations {
        datevExtraction: AsyncOperation<
            {
                id?: string;
            },
            boolean
        >;
        datevExport: AsyncOperation<
            {
                id?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface DatevExport$Lookups {
        company: QueryOperation<Company>;
        dimensionType1: QueryOperation<DimensionType>;
        attributeType1: QueryOperation<AttributeType>;
        dimensionType2: QueryOperation<DimensionType>;
        attributeType2: QueryOperation<AttributeType>;
    }
    export interface DatevExport$Operations {
        query: QueryOperation<DatevExport>;
        read: ReadOperation<DatevExport>;
        aggregate: {
            read: AggregateReadOperation<DatevExport>;
            query: AggregateQueryOperation<DatevExport>;
        };
        create: CreateOperation<DatevExportInput, DatevExport>;
        getDuplicate: GetDuplicateOperation<DatevExport>;
        update: UpdateOperation<DatevExportInput, DatevExport>;
        updateById: UpdateByIdOperation<DatevExportInput, DatevExport>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: DatevExport$AsyncOperations;
        lookups(dataOrId: string | { data: DatevExportInput }): DatevExport$Lookups;
        getDefaults: GetDefaultsOperation<DatevExport>;
    }
    export interface DatevExportAccount extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        datevExport: DatevExport;
        account: Account;
        datevId: integer;
        name: string;
    }
    export interface DatevExportAccountInput extends VitalClientNodeInput {
        account?: integer | string;
        datevId?: integer | string;
        name?: string;
    }
    export interface DatevExportAccountBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        datevExport: DatevExport;
        account: Account;
        datevId: integer;
        name: string;
    }
    export interface DatevExportAccount$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface DatevExportAccount$Lookups {
        account: QueryOperation<Account>;
    }
    export interface DatevExportAccount$Operations {
        query: QueryOperation<DatevExportAccount>;
        read: ReadOperation<DatevExportAccount>;
        aggregate: {
            read: AggregateReadOperation<DatevExportAccount>;
            query: AggregateQueryOperation<DatevExportAccount>;
        };
        asyncOperations: DatevExportAccount$AsyncOperations;
        lookups(dataOrId: string | { data: DatevExportAccountInput }): DatevExportAccount$Lookups;
        getDefaults: GetDefaultsOperation<DatevExportAccount>;
    }
    export interface DatevExportBusinessRelation extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        datevExport: DatevExport;
        businessRelation: BaseBusinessRelation;
        datevId: integer;
        name: string;
        taxIdNumber: string;
        country: Country;
        street: string;
        postcode: string;
        city: string;
    }
    export interface DatevExportBusinessRelationInput extends VitalClientNodeInput {
        businessRelation?: integer | string;
        datevId?: integer | string;
        name?: string;
        taxIdNumber?: string;
        country?: integer | string;
        street?: string;
        postcode?: string;
        city?: string;
    }
    export interface DatevExportBusinessRelationBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        datevExport: DatevExport;
        businessRelation: BaseBusinessRelation;
        datevId: integer;
        name: string;
        taxIdNumber: string;
        country: Country;
        street: string;
        postcode: string;
        city: string;
    }
    export interface DatevExportBusinessRelation$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface DatevExportBusinessRelation$Lookups {
        businessRelation: QueryOperation<BaseBusinessRelation>;
        country: QueryOperation<Country>;
    }
    export interface DatevExportBusinessRelation$Operations {
        query: QueryOperation<DatevExportBusinessRelation>;
        read: ReadOperation<DatevExportBusinessRelation>;
        aggregate: {
            read: AggregateReadOperation<DatevExportBusinessRelation>;
            query: AggregateQueryOperation<DatevExportBusinessRelation>;
        };
        asyncOperations: DatevExportBusinessRelation$AsyncOperations;
        lookups(dataOrId: string | { data: DatevExportBusinessRelationInput }): DatevExportBusinessRelation$Lookups;
        getDefaults: GetDefaultsOperation<DatevExportBusinessRelation>;
    }
    export interface DatevExportJournalEntryLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        datevExport: DatevExport;
        journalEntryLine: JournalEntryLine;
        datevSign: string;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyCurrency: Currency;
        datevAccountId: integer;
        datevContraAccountId: integer;
        postingKey: integer;
        postingDate: string;
        number: string;
        supplierDocumentNumber: string;
        description: string;
        dimension1: string;
        dimension2: string;
        businessEntityTaxIdNumber: string;
        locked: integer;
        siteTaxIdNumber: string;
        transactionValue: string;
        companyValue: string;
    }
    export interface DatevExportJournalEntryLineInput extends VitalClientNodeInput {
        journalEntryLine?: integer | string;
        datevSign?: string;
        transactionCurrency?: integer | string;
        companyFxRate?: decimal | string;
        companyCurrency?: integer | string;
        datevAccountId?: integer | string;
        datevContraAccountId?: integer | string;
        postingKey?: integer | string;
        postingDate?: string;
        number?: string;
        supplierDocumentNumber?: string;
        description?: string;
        dimension1?: string;
        dimension2?: string;
        businessEntityTaxIdNumber?: string;
        locked?: integer | string;
        siteTaxIdNumber?: string;
        transactionValue?: decimal | string;
        companyValue?: decimal | string;
    }
    export interface DatevExportJournalEntryLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        datevExport: DatevExport;
        journalEntryLine: JournalEntryLine;
        datevSign: string;
        transactionCurrency: Currency;
        companyFxRate: string;
        companyCurrency: Currency;
        datevAccountId: integer;
        datevContraAccountId: integer;
        postingKey: integer;
        postingDate: string;
        number: string;
        supplierDocumentNumber: string;
        description: string;
        dimension1: string;
        dimension2: string;
        businessEntityTaxIdNumber: string;
        locked: integer;
        siteTaxIdNumber: string;
        transactionValue: string;
        companyValue: string;
    }
    export interface DatevExportJournalEntryLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface DatevExportJournalEntryLine$Lookups {
        journalEntryLine: QueryOperation<JournalEntryLine>;
        transactionCurrency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
    }
    export interface DatevExportJournalEntryLine$Operations {
        query: QueryOperation<DatevExportJournalEntryLine>;
        read: ReadOperation<DatevExportJournalEntryLine>;
        aggregate: {
            read: AggregateReadOperation<DatevExportJournalEntryLine>;
            query: AggregateQueryOperation<DatevExportJournalEntryLine>;
        };
        asyncOperations: DatevExportJournalEntryLine$AsyncOperations;
        lookups(dataOrId: string | { data: DatevExportJournalEntryLineInput }): DatevExportJournalEntryLine$Lookups;
        getDefaults: GetDefaultsOperation<DatevExportJournalEntryLine>;
    }
    export interface DatevExportListener extends ClientNode {}
    export interface DatevExportListenerInput extends ClientNodeInput {}
    export interface DatevExportListenerBinding extends ClientNode {}
    export interface InitializeOpenItem extends ClientNode {}
    export interface InitializeOpenItemInput extends ClientNodeInput {}
    export interface InitializeOpenItemBinding extends ClientNode {}
    export interface InitializeOpenItem$Operations {
        create: CreateOperation<InitializeOpenItemInput, InitializeOpenItem>;
        getDuplicate: GetDuplicateOperation<InitializeOpenItem>;
        update: UpdateOperation<InitializeOpenItemInput, InitializeOpenItem>;
        updateById: UpdateByIdOperation<InitializeOpenItemInput, InitializeOpenItem>;
        getDefaults: GetDefaultsOperation<InitializeOpenItem>;
    }
    export interface JournalEntry extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        financialSite: Site;
        arInvoice: AccountsReceivableInvoice;
        apInvoice: AccountsPayableInvoice;
        postingStatus: JournalStatus;
        journal: Journal;
        postingDate: string;
        description: string;
        reference: string;
        origin: JournalOrigin;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<JournalEntryLine>;
        documentType: string;
    }
    export interface JournalEntryInput extends ClientNodeInput {
        number?: string;
        financialSite?: integer | string;
        arInvoice?: integer | string;
        apInvoice?: integer | string;
        postingStatus?: JournalStatus;
        journal?: integer | string;
        postingDate?: string;
        description?: string;
        reference?: string;
        origin?: JournalOrigin;
        lines?: Partial<JournalEntryLineInput>[];
        documentType?: string;
    }
    export interface JournalEntryBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        financialSite: Site;
        arInvoice: AccountsReceivableInvoice;
        apInvoice: AccountsPayableInvoice;
        postingStatus: JournalStatus;
        journal: Journal;
        postingDate: string;
        description: string;
        reference: string;
        origin: JournalOrigin;
        financeIntegrationApp: FinanceIntegrationApp;
        financeIntegrationStatus: PostingStatus;
        internalFinanceIntegrationStatus: FinanceIntegrationStatus;
        financeIntegrationAppRecordId: string;
        financeIntegrationAppUrl: string;
        lines: ClientCollection<JournalEntryLineBinding>;
        documentType: string;
    }
    export interface JournalEntry$Queries {
        areFinanceIntegrationPackagesActive: Node$Operation<
            {
                dummy?: string;
            },
            boolean
        >;
    }
    export interface JournalEntry$Mutations {
        post: Node$Operation<
            {
                journalEntry: string;
            },
            string
        >;
    }
    export interface JournalEntry$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface JournalEntry$Lookups {
        financialSite: QueryOperation<Site>;
        arInvoice: QueryOperation<AccountsReceivableInvoice>;
        apInvoice: QueryOperation<AccountsPayableInvoice>;
        journal: QueryOperation<Journal>;
    }
    export interface JournalEntry$Operations {
        query: QueryOperation<JournalEntry>;
        read: ReadOperation<JournalEntry>;
        aggregate: {
            read: AggregateReadOperation<JournalEntry>;
            query: AggregateQueryOperation<JournalEntry>;
        };
        queries: JournalEntry$Queries;
        update: UpdateOperation<JournalEntryInput, JournalEntry>;
        updateById: UpdateByIdOperation<JournalEntryInput, JournalEntry>;
        mutations: JournalEntry$Mutations;
        asyncOperations: JournalEntry$AsyncOperations;
        lookups(dataOrId: string | { data: JournalEntryInput }): JournalEntry$Lookups;
        getDefaults: GetDefaultsOperation<JournalEntry>;
    }
    export interface JournalEntryInquiry extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        journalEntryLines: ClientCollection<JournalEntryLine>;
    }
    export interface JournalEntryInquiryInput extends ClientNodeInput {}
    export interface JournalEntryInquiryBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        journalEntryLines: ClientCollection<JournalEntryLine>;
    }
    export interface JournalEntryInquiry$Mutations {
        singleRecord: Node$Operation<{}, string>;
    }
    export interface JournalEntryInquiry$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface JournalEntryInquiry$Operations {
        query: QueryOperation<JournalEntryInquiry>;
        read: ReadOperation<JournalEntryInquiry>;
        aggregate: {
            read: AggregateReadOperation<JournalEntryInquiry>;
            query: AggregateQueryOperation<JournalEntryInquiry>;
        };
        mutations: JournalEntryInquiry$Mutations;
        asyncOperations: JournalEntryInquiry$AsyncOperations;
        getDefaults: GetDefaultsOperation<JournalEntryInquiry>;
    }
    export interface JournalEntryLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        journalEntry: JournalEntry;
        financialSite: Site;
        chartOfAccount: ChartOfAccount;
        tax: Tax;
        taxDate: string;
        taxRate: string;
        deductibleTaxRate: string;
        taxExternalReference: string;
        transactionCurrency: Currency;
        inquiryTransactionCurrency: Currency;
        companyCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        financialSiteCurrency: Currency;
        financialSiteFxRate: string;
        financialSiteFxRateDivisor: string;
        fxRateDate: string;
        rateDescription: string;
        account: Account;
        businessEntity: BusinessEntity;
        sign: Sign;
        numericSign: integer;
        blank: string;
        transactionAmount: string;
        transactionCredit: string;
        transactionDebit: string;
        signedTransactionAmount: string;
        companyAmount: string;
        companyCredit: string;
        companyDebit: string;
        financialSiteAmount: string;
        financialSiteCredit: string;
        financialSiteDebit: string;
        description: string;
        inquiryDescription: string;
        commonReference: string;
        attributesAndDimensions: ClientCollection<JournalEntryLineDimension>;
        dueDate: string;
        validationDate: string;
        accountingStagingLines: ClientCollection<JournalEntryLineStaging>;
        financialSiteAttribute: Site;
        businessSiteAttribute: Site;
        stockSiteAttribute: Site;
        manufacturingSiteAttribute: Site;
        customerAttribute: Customer;
        supplierAttribute: Supplier;
        projectAttribute: Attribute;
        employeeAttribute: Attribute;
        taskAttribute: Attribute;
        itemAttribute: Item;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        journalEntryTypeLine: JournalEntryTypeLine;
        contraJournalEntryLine: JournalEntryLine;
        contraAccount: string;
        baseTax: BaseTax;
        datevContraAccountId: integer;
        datevPostingKey: integer;
        datevTransactionAmount: string;
        datevCompanyAmount: string;
        supplierDocumentNumber: string;
        datevBusinessEntityTaxIdNumber: string;
        isBalanceLine: boolean;
    }
    export interface JournalEntryLineInput extends VitalClientNodeInput {
        financialSite?: integer | string;
        chartOfAccount?: integer | string;
        tax?: integer | string;
        taxDate?: string;
        taxRate?: decimal | string;
        deductibleTaxRate?: decimal | string;
        transactionCurrency?: integer | string;
        companyCurrency?: integer | string;
        companyFxRate?: decimal | string;
        companyFxRateDivisor?: decimal | string;
        financialSiteCurrency?: integer | string;
        financialSiteFxRate?: decimal | string;
        financialSiteFxRateDivisor?: decimal | string;
        fxRateDate?: string;
        account?: integer | string;
        businessEntity?: integer | string;
        sign?: Sign;
        transactionAmount?: decimal | string;
        companyAmount?: decimal | string;
        financialSiteAmount?: decimal | string;
        description?: string;
        commonReference?: string;
        attributesAndDimensions?: Partial<JournalEntryLineDimensionInput>[];
        accountingStagingLines?: Partial<JournalEntryLineStagingInput>[];
        journalEntryTypeLine?: integer | string;
        contraJournalEntryLine?: integer | string;
        baseTax?: integer | string;
        isBalanceLine?: boolean | string;
    }
    export interface JournalEntryLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        journalEntry: JournalEntry;
        financialSite: Site;
        chartOfAccount: ChartOfAccount;
        tax: Tax;
        taxDate: string;
        taxRate: string;
        deductibleTaxRate: string;
        taxExternalReference: string;
        transactionCurrency: Currency;
        inquiryTransactionCurrency: Currency;
        companyCurrency: Currency;
        companyFxRate: string;
        companyFxRateDivisor: string;
        financialSiteCurrency: Currency;
        financialSiteFxRate: string;
        financialSiteFxRateDivisor: string;
        fxRateDate: string;
        rateDescription: string;
        account: Account;
        businessEntity: BusinessEntity;
        sign: Sign;
        numericSign: integer;
        blank: string;
        transactionAmount: string;
        transactionCredit: string;
        transactionDebit: string;
        signedTransactionAmount: string;
        companyAmount: string;
        companyCredit: string;
        companyDebit: string;
        financialSiteAmount: string;
        financialSiteCredit: string;
        financialSiteDebit: string;
        description: string;
        inquiryDescription: string;
        commonReference: string;
        attributesAndDimensions: ClientCollection<JournalEntryLineDimensionBinding>;
        dueDate: string;
        validationDate: string;
        accountingStagingLines: ClientCollection<JournalEntryLineStagingBinding>;
        financialSiteAttribute: Site;
        businessSiteAttribute: Site;
        stockSiteAttribute: Site;
        manufacturingSiteAttribute: Site;
        customerAttribute: Customer;
        supplierAttribute: Supplier;
        projectAttribute: Attribute;
        employeeAttribute: Attribute;
        taskAttribute: Attribute;
        itemAttribute: Item;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        journalEntryTypeLine: JournalEntryTypeLine;
        contraJournalEntryLine: JournalEntryLine;
        contraAccount: string;
        baseTax: BaseTax;
        datevContraAccountId: integer;
        datevPostingKey: integer;
        datevTransactionAmount: string;
        datevCompanyAmount: string;
        supplierDocumentNumber: string;
        datevBusinessEntityTaxIdNumber: string;
        isBalanceLine: boolean;
    }
    export interface JournalEntryLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface JournalEntryLine$Lookups {
        financialSite: QueryOperation<Site>;
        chartOfAccount: QueryOperation<ChartOfAccount>;
        tax: QueryOperation<Tax>;
        transactionCurrency: QueryOperation<Currency>;
        inquiryTransactionCurrency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        financialSiteCurrency: QueryOperation<Currency>;
        account: QueryOperation<Account>;
        businessEntity: QueryOperation<BusinessEntity>;
        financialSiteAttribute: QueryOperation<Site>;
        businessSiteAttribute: QueryOperation<Site>;
        stockSiteAttribute: QueryOperation<Site>;
        manufacturingSiteAttribute: QueryOperation<Site>;
        customerAttribute: QueryOperation<Customer>;
        supplierAttribute: QueryOperation<Supplier>;
        projectAttribute: QueryOperation<Attribute>;
        employeeAttribute: QueryOperation<Attribute>;
        taskAttribute: QueryOperation<Attribute>;
        itemAttribute: QueryOperation<Item>;
        dimension01: QueryOperation<Dimension>;
        dimension02: QueryOperation<Dimension>;
        dimension03: QueryOperation<Dimension>;
        dimension04: QueryOperation<Dimension>;
        dimension05: QueryOperation<Dimension>;
        dimension06: QueryOperation<Dimension>;
        dimension07: QueryOperation<Dimension>;
        dimension08: QueryOperation<Dimension>;
        dimension09: QueryOperation<Dimension>;
        dimension10: QueryOperation<Dimension>;
        dimension11: QueryOperation<Dimension>;
        dimension12: QueryOperation<Dimension>;
        dimension13: QueryOperation<Dimension>;
        dimension14: QueryOperation<Dimension>;
        dimension15: QueryOperation<Dimension>;
        dimension16: QueryOperation<Dimension>;
        dimension17: QueryOperation<Dimension>;
        dimension18: QueryOperation<Dimension>;
        dimension19: QueryOperation<Dimension>;
        dimension20: QueryOperation<Dimension>;
        journalEntryTypeLine: QueryOperation<JournalEntryTypeLine>;
        contraJournalEntryLine: QueryOperation<JournalEntryLine>;
        baseTax: QueryOperation<BaseTax>;
    }
    export interface JournalEntryLine$Operations {
        query: QueryOperation<JournalEntryLine>;
        read: ReadOperation<JournalEntryLine>;
        aggregate: {
            read: AggregateReadOperation<JournalEntryLine>;
            query: AggregateQueryOperation<JournalEntryLine>;
        };
        asyncOperations: JournalEntryLine$AsyncOperations;
        lookups(dataOrId: string | { data: JournalEntryLineInput }): JournalEntryLine$Lookups;
        getDefaults: GetDefaultsOperation<JournalEntryLine>;
    }
    export interface JournalEntryLineDimension extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        journalEntryLine: JournalEntryLine;
        storedAttributes: string;
        storedDimensions: string;
        analyticalData: AnalyticalData;
        hasAttributesOrDimenionsChanged: boolean;
        transactionCurrency: Currency;
        companyCurrency: Currency;
        financialSiteCurrency: Currency;
        transactionAmount: string;
        companyAmount: string;
        financialSiteAmount: string;
        storedComputedAttributes: string;
        financialSite: Site;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: Customer;
        supplier: Supplier;
        project: Attribute;
        employee: Attribute;
        task: Attribute;
        item: Item;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
    }
    export interface JournalEntryLineDimensionInput extends VitalClientNodeInput {
        storedAttributes?: string;
        storedDimensions?: string;
        analyticalData?: integer | string;
        hasAttributesOrDimenionsChanged?: boolean | string;
        transactionAmount?: decimal | string;
        companyAmount?: decimal | string;
        financialSiteAmount?: decimal | string;
        storedComputedAttributes?: string;
    }
    export interface JournalEntryLineDimensionBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        journalEntryLine: JournalEntryLine;
        storedAttributes: any;
        storedDimensions: any;
        analyticalData: AnalyticalData;
        hasAttributesOrDimenionsChanged: boolean;
        transactionCurrency: Currency;
        companyCurrency: Currency;
        financialSiteCurrency: Currency;
        transactionAmount: string;
        companyAmount: string;
        financialSiteAmount: string;
        storedComputedAttributes: any;
        financialSite: Site;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: Customer;
        supplier: Supplier;
        project: Attribute;
        employee: Attribute;
        task: Attribute;
        item: Item;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
    }
    export interface JournalEntryLineDimension$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface JournalEntryLineDimension$Lookups {
        analyticalData: QueryOperation<AnalyticalData>;
        transactionCurrency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        financialSiteCurrency: QueryOperation<Currency>;
        financialSite: QueryOperation<Site>;
        businessSite: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        manufacturingSite: QueryOperation<Site>;
        customer: QueryOperation<Customer>;
        supplier: QueryOperation<Supplier>;
        project: QueryOperation<Attribute>;
        employee: QueryOperation<Attribute>;
        task: QueryOperation<Attribute>;
        item: QueryOperation<Item>;
        dimension01: QueryOperation<Dimension>;
        dimension02: QueryOperation<Dimension>;
        dimension03: QueryOperation<Dimension>;
        dimension04: QueryOperation<Dimension>;
        dimension05: QueryOperation<Dimension>;
        dimension06: QueryOperation<Dimension>;
        dimension07: QueryOperation<Dimension>;
        dimension08: QueryOperation<Dimension>;
        dimension09: QueryOperation<Dimension>;
        dimension10: QueryOperation<Dimension>;
        dimension11: QueryOperation<Dimension>;
        dimension12: QueryOperation<Dimension>;
        dimension13: QueryOperation<Dimension>;
        dimension14: QueryOperation<Dimension>;
        dimension15: QueryOperation<Dimension>;
        dimension16: QueryOperation<Dimension>;
        dimension17: QueryOperation<Dimension>;
        dimension18: QueryOperation<Dimension>;
        dimension19: QueryOperation<Dimension>;
        dimension20: QueryOperation<Dimension>;
    }
    export interface JournalEntryLineDimension$Operations {
        query: QueryOperation<JournalEntryLineDimension>;
        read: ReadOperation<JournalEntryLineDimension>;
        aggregate: {
            read: AggregateReadOperation<JournalEntryLineDimension>;
            query: AggregateQueryOperation<JournalEntryLineDimension>;
        };
        asyncOperations: JournalEntryLineDimension$AsyncOperations;
        lookups(dataOrId: string | { data: JournalEntryLineDimensionInput }): JournalEntryLineDimension$Lookups;
        getDefaults: GetDefaultsOperation<JournalEntryLineDimension>;
    }
    export interface JournalEntryLineStaging extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        journalEntryLine: JournalEntryLine;
        accountingStaging: AccountingStaging;
    }
    export interface JournalEntryLineStagingInput extends VitalClientNodeInput {
        accountingStaging?: integer | string;
    }
    export interface JournalEntryLineStagingBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        journalEntryLine: JournalEntryLine;
        accountingStaging: AccountingStaging;
    }
    export interface JournalEntryLineStaging$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface JournalEntryLineStaging$Lookups {
        accountingStaging: QueryOperation<AccountingStaging>;
    }
    export interface JournalEntryLineStaging$Operations {
        query: QueryOperation<JournalEntryLineStaging>;
        read: ReadOperation<JournalEntryLineStaging>;
        aggregate: {
            read: AggregateReadOperation<JournalEntryLineStaging>;
            query: AggregateQueryOperation<JournalEntryLineStaging>;
        };
        asyncOperations: JournalEntryLineStaging$AsyncOperations;
        lookups(dataOrId: string | { data: JournalEntryLineStagingInput }): JournalEntryLineStaging$Lookups;
        getDefaults: GetDefaultsOperation<JournalEntryLineStaging>;
    }
    export interface AccountsPayableInvoiceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsPayableInvoice;
        financialSite: Site;
        taxDate: string;
        recipientSite: Site;
        account: Account;
        documentLineType: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType: AccountsPayableReceivableInvoiceLineType;
        currency: Currency;
        amountExcludingTax: string;
        taxLineTaxAmount: string;
        taxDetail: string;
        description: string;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        attributesAndDimensions: ClientCollection<AccountsPayableInvoiceLineDimension>;
        accountingStagingLines: ClientCollection<AccountsPayableInvoiceLineStaging>;
        taxes: ClientCollection<AccountsPayableInvoiceLineTax>;
        uiTaxes: string;
        storedDimensions: string;
        storedAttributes: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxAmountAdjusted: string;
        amountIncludingTax: string;
    }
    export interface AccountsPayableInvoiceLineInput extends VitalClientNodeInput {
        financialSite?: integer | string;
        taxDate?: string;
        recipientSite?: integer | string;
        account?: integer | string;
        documentLineType?: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType?: AccountsPayableReceivableInvoiceLineType;
        currency?: integer | string;
        amountExcludingTax?: decimal | string;
        taxLineTaxAmount?: decimal | string;
        taxDetail?: string;
        description?: string;
        sourceDocumentNumber?: string;
        attributesAndDimensions?: Partial<AccountsPayableInvoiceLineDimensionInput>[];
        accountingStagingLines?: Partial<AccountsPayableInvoiceLineStagingInput>[];
        taxes?: Partial<AccountsPayableInvoiceLineTaxInput>[];
        uiTaxes?: string;
        taxableAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        amountIncludingTax?: decimal | string;
    }
    export interface AccountsPayableInvoiceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsPayableInvoice;
        financialSite: Site;
        taxDate: string;
        recipientSite: Site;
        account: Account;
        documentLineType: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType: AccountsPayableReceivableInvoiceLineType;
        currency: Currency;
        amountExcludingTax: string;
        taxLineTaxAmount: string;
        taxDetail: string;
        description: string;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        attributesAndDimensions: ClientCollection<AccountsPayableInvoiceLineDimensionBinding>;
        accountingStagingLines: ClientCollection<AccountsPayableInvoiceLineStagingBinding>;
        taxes: ClientCollection<AccountsPayableInvoiceLineTaxBinding>;
        uiTaxes: any;
        storedDimensions: any;
        storedAttributes: any;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxAmountAdjusted: string;
        amountIncludingTax: string;
    }
    export interface AccountsPayableInvoiceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsPayableInvoiceLine$Lookups {
        financialSite: QueryOperation<Site>;
        recipientSite: QueryOperation<Site>;
        account: QueryOperation<Account>;
        currency: QueryOperation<Currency>;
    }
    export interface AccountsPayableInvoiceLine$Operations {
        query: QueryOperation<AccountsPayableInvoiceLine>;
        read: ReadOperation<AccountsPayableInvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<AccountsPayableInvoiceLine>;
            query: AggregateQueryOperation<AccountsPayableInvoiceLine>;
        };
        asyncOperations: AccountsPayableInvoiceLine$AsyncOperations;
        lookups(dataOrId: string | { data: AccountsPayableInvoiceLineInput }): AccountsPayableInvoiceLine$Lookups;
        getDefaults: GetDefaultsOperation<AccountsPayableInvoiceLine>;
    }
    export interface AccountsPayableInvoiceLineTax extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: AccountsPayableInvoiceLine;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface AccountsPayableInvoiceLineTaxInput extends VitalClientNodeInput {
        taxRate?: decimal | string;
        taxCategoryReference?: integer | string;
        taxReference?: integer | string;
        deductibleTaxRate?: decimal | string;
        isReverseCharge?: boolean | string;
        isTaxMandatory?: boolean | string;
        isSubjectToGlTaxExcludedAmount?: boolean | string;
        jurisdictionName?: string;
        taxCategory?: string;
        tax?: string;
        deductibleTaxAmount?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        nonTaxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxableAmount?: decimal | string;
    }
    export interface AccountsPayableInvoiceLineTaxBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: AccountsPayableInvoiceLine;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface AccountsPayableInvoiceLineTax$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsPayableInvoiceLineTax$Lookups {
        taxCategoryReference: QueryOperation<TaxCategory>;
        taxReference: QueryOperation<Tax>;
        currency: QueryOperation<Currency>;
    }
    export interface AccountsPayableInvoiceLineTax$Operations {
        query: QueryOperation<AccountsPayableInvoiceLineTax>;
        read: ReadOperation<AccountsPayableInvoiceLineTax>;
        aggregate: {
            read: AggregateReadOperation<AccountsPayableInvoiceLineTax>;
            query: AggregateQueryOperation<AccountsPayableInvoiceLineTax>;
        };
        asyncOperations: AccountsPayableInvoiceLineTax$AsyncOperations;
        lookups(dataOrId: string | { data: AccountsPayableInvoiceLineTaxInput }): AccountsPayableInvoiceLineTax$Lookups;
        getDefaults: GetDefaultsOperation<AccountsPayableInvoiceLineTax>;
    }
    export interface AccountsPayableInvoiceTax extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: AccountsPayableInvoice;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface AccountsPayableInvoiceTaxInput extends VitalClientNodeInput {
        taxRate?: decimal | string;
        taxCategoryReference?: integer | string;
        taxReference?: integer | string;
        deductibleTaxRate?: decimal | string;
        isReverseCharge?: boolean | string;
        isTaxMandatory?: boolean | string;
        isSubjectToGlTaxExcludedAmount?: boolean | string;
        jurisdictionName?: string;
        taxCategory?: string;
        tax?: string;
        deductibleTaxAmount?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        nonTaxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxableAmount?: decimal | string;
    }
    export interface AccountsPayableInvoiceTaxBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: AccountsPayableInvoice;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface AccountsPayableInvoiceTax$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsPayableInvoiceTax$Lookups {
        taxCategoryReference: QueryOperation<TaxCategory>;
        taxReference: QueryOperation<Tax>;
        currency: QueryOperation<Currency>;
    }
    export interface AccountsPayableInvoiceTax$Operations {
        query: QueryOperation<AccountsPayableInvoiceTax>;
        read: ReadOperation<AccountsPayableInvoiceTax>;
        aggregate: {
            read: AggregateReadOperation<AccountsPayableInvoiceTax>;
            query: AggregateQueryOperation<AccountsPayableInvoiceTax>;
        };
        asyncOperations: AccountsPayableInvoiceTax$AsyncOperations;
        lookups(dataOrId: string | { data: AccountsPayableInvoiceTaxInput }): AccountsPayableInvoiceTax$Lookups;
        getDefaults: GetDefaultsOperation<AccountsPayableInvoiceTax>;
    }
    export interface AccountsPayableOpenItem extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        dueDate: string;
        businessEntity: BusinessEntity;
        businessRelation: Supplier;
        businessEntityPayment: BusinessEntity;
        type: BusinessEntityType;
        currency: Currency;
        transactionAmountDue: string;
        transactionAmountDueSigned: string;
        transactionAmountPaid: string;
        transactionAmountPaidSigned: string;
        remainingTransactionAmountSigned: string;
        companyAmountDue: string;
        companyAmountDueSigned: string;
        companyAmountPaid: string;
        companyAmountPaidSigned: string;
        remainingCompanyAmountSigned: string;
        financialSiteAmountDue: string;
        financialSiteAmountPaid: string;
        status: OpenItemStatus;
        documentType: FinanceDocumentType;
        documentNumber: string;
        documentSysId: integer;
        discountFrom: DueDateType;
        discountDate: integer;
        discountType: PaymentTermDiscountOrPenaltyType;
        discountAmount: string;
        discountPaymentBeforeDate: string;
        displayDiscountPaymentDate: string;
        penaltyPaymentType: DiscountOrPenaltyType;
        penaltyAmount: string;
        closeReason: CloseReason;
        closeText: string;
        forcedAmountPaid: string;
        forcedAmountPaidSigned: string;
        accountsPayableInvoice: AccountsPayableInvoice;
        financialSite: Site;
        totalAmount: string;
        amountDue: string;
        totalCompanyAmount: string;
        totalCompanyAmountPaid: string;
        remainingCompanyAmount: string;
        paymentTracking: PaymentTracking;
    }
    export interface AccountsPayableOpenItemInput extends ClientNodeInput {
        dueDate?: string;
        businessEntity?: integer | string;
        businessEntityPayment?: integer | string;
        type?: BusinessEntityType;
        currency?: integer | string;
        transactionAmountDue?: decimal | string;
        transactionAmountPaid?: decimal | string;
        companyAmountDue?: decimal | string;
        companyAmountPaid?: decimal | string;
        financialSiteAmountDue?: decimal | string;
        financialSiteAmountPaid?: decimal | string;
        documentType?: FinanceDocumentType;
        documentNumber?: string;
        documentSysId?: integer | string;
        discountFrom?: DueDateType;
        discountDate?: integer | string;
        discountType?: PaymentTermDiscountOrPenaltyType;
        discountAmount?: decimal | string;
        discountPaymentBeforeDate?: string;
        penaltyPaymentType?: DiscountOrPenaltyType;
        penaltyAmount?: decimal | string;
        closeReason?: integer | string;
        closeText?: string;
        forcedAmountPaid?: decimal | string;
        accountsPayableInvoice?: integer | string;
    }
    export interface AccountsPayableOpenItemBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        dueDate: string;
        businessEntity: BusinessEntity;
        businessRelation: Supplier;
        businessEntityPayment: BusinessEntity;
        type: BusinessEntityType;
        currency: Currency;
        transactionAmountDue: string;
        transactionAmountDueSigned: string;
        transactionAmountPaid: string;
        transactionAmountPaidSigned: string;
        remainingTransactionAmountSigned: string;
        companyAmountDue: string;
        companyAmountDueSigned: string;
        companyAmountPaid: string;
        companyAmountPaidSigned: string;
        remainingCompanyAmountSigned: string;
        financialSiteAmountDue: string;
        financialSiteAmountPaid: string;
        status: OpenItemStatus;
        documentType: FinanceDocumentType;
        documentNumber: string;
        documentSysId: integer;
        discountFrom: DueDateType;
        discountDate: integer;
        discountType: PaymentTermDiscountOrPenaltyType;
        discountAmount: string;
        discountPaymentBeforeDate: string;
        displayDiscountPaymentDate: string;
        penaltyPaymentType: DiscountOrPenaltyType;
        penaltyAmount: string;
        closeReason: CloseReason;
        closeText: string;
        forcedAmountPaid: string;
        forcedAmountPaidSigned: string;
        accountsPayableInvoice: AccountsPayableInvoice;
        financialSite: Site;
        totalAmount: string;
        amountDue: string;
        totalCompanyAmount: string;
        totalCompanyAmountPaid: string;
        remainingCompanyAmount: string;
        paymentTracking: PaymentTracking;
    }
    export interface AccountsPayableOpenItem$AsyncOperations {
        bulkOpenItemUpdate: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsPayableOpenItem$Lookups {
        businessEntity: QueryOperation<BusinessEntity>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        businessEntityPayment: QueryOperation<BusinessEntity>;
        currency: QueryOperation<Currency>;
        closeReason: QueryOperation<CloseReason>;
        accountsPayableInvoice: QueryOperation<AccountsPayableInvoice>;
        financialSite: QueryOperation<Site>;
        paymentTracking: QueryOperation<PaymentTracking>;
    }
    export interface AccountsPayableOpenItem$Operations {
        query: QueryOperation<AccountsPayableOpenItem>;
        read: ReadOperation<AccountsPayableOpenItem>;
        aggregate: {
            read: AggregateReadOperation<AccountsPayableOpenItem>;
            query: AggregateQueryOperation<AccountsPayableOpenItem>;
        };
        update: UpdateOperation<AccountsPayableOpenItemInput, AccountsPayableOpenItem>;
        updateById: UpdateByIdOperation<AccountsPayableOpenItemInput, AccountsPayableOpenItem>;
        asyncOperations: AccountsPayableOpenItem$AsyncOperations;
        lookups(dataOrId: string | { data: AccountsPayableOpenItemInput }): AccountsPayableOpenItem$Lookups;
        getDefaults: GetDefaultsOperation<AccountsPayableOpenItem>;
    }
    export interface AccountsReceivableAdvanceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsReceivableAdvance;
        financialSite: Site;
        account: Account;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        advanceAmount: string;
        advanceCompanyAmount: string;
        description: string;
        storedAttributes: string;
        computedAttributes: string;
        storedDimensions: string;
        analyticalData: AnalyticalData;
        hasAttributesOrDimenionsChanged: boolean;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: string;
        supplier: Supplier;
        project: Attribute;
        employee: Attribute;
        task: Attribute;
        item: Item;
    }
    export interface AccountsReceivableAdvanceLineInput extends VitalClientNodeInput {
        financialSite?: integer | string;
        account?: integer | string;
        currency?: integer | string;
        advanceAmount?: decimal | string;
        advanceCompanyAmount?: decimal | string;
        description?: string;
        storedAttributes?: string;
        storedDimensions?: string;
        analyticalData?: integer | string;
        hasAttributesOrDimenionsChanged?: boolean | string;
    }
    export interface AccountsReceivableAdvanceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsReceivableAdvance;
        financialSite: Site;
        account: Account;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        advanceAmount: string;
        advanceCompanyAmount: string;
        description: string;
        storedAttributes: any;
        computedAttributes: any;
        storedDimensions: any;
        analyticalData: AnalyticalData;
        hasAttributesOrDimenionsChanged: boolean;
        dimension01: Dimension;
        dimension02: Dimension;
        dimension03: Dimension;
        dimension04: Dimension;
        dimension05: Dimension;
        dimension06: Dimension;
        dimension07: Dimension;
        dimension08: Dimension;
        dimension09: Dimension;
        dimension10: Dimension;
        dimension11: Dimension;
        dimension12: Dimension;
        dimension13: Dimension;
        dimension14: Dimension;
        dimension15: Dimension;
        dimension16: Dimension;
        dimension17: Dimension;
        dimension18: Dimension;
        dimension19: Dimension;
        dimension20: Dimension;
        businessSite: Site;
        stockSite: Site;
        manufacturingSite: Site;
        customer: string;
        supplier: Supplier;
        project: Attribute;
        employee: Attribute;
        task: Attribute;
        item: Item;
    }
    export interface AccountsReceivableAdvanceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsReceivableAdvanceLine$Lookups {
        financialSite: QueryOperation<Site>;
        account: QueryOperation<Account>;
        currency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
        analyticalData: QueryOperation<AnalyticalData>;
        dimension01: QueryOperation<Dimension>;
        dimension02: QueryOperation<Dimension>;
        dimension03: QueryOperation<Dimension>;
        dimension04: QueryOperation<Dimension>;
        dimension05: QueryOperation<Dimension>;
        dimension06: QueryOperation<Dimension>;
        dimension07: QueryOperation<Dimension>;
        dimension08: QueryOperation<Dimension>;
        dimension09: QueryOperation<Dimension>;
        dimension10: QueryOperation<Dimension>;
        dimension11: QueryOperation<Dimension>;
        dimension12: QueryOperation<Dimension>;
        dimension13: QueryOperation<Dimension>;
        dimension14: QueryOperation<Dimension>;
        dimension15: QueryOperation<Dimension>;
        dimension16: QueryOperation<Dimension>;
        dimension17: QueryOperation<Dimension>;
        dimension18: QueryOperation<Dimension>;
        dimension19: QueryOperation<Dimension>;
        dimension20: QueryOperation<Dimension>;
        businessSite: QueryOperation<Site>;
        stockSite: QueryOperation<Site>;
        manufacturingSite: QueryOperation<Site>;
        supplier: QueryOperation<Supplier>;
        project: QueryOperation<Attribute>;
        employee: QueryOperation<Attribute>;
        task: QueryOperation<Attribute>;
        item: QueryOperation<Item>;
    }
    export interface AccountsReceivableAdvanceLine$Operations {
        query: QueryOperation<AccountsReceivableAdvanceLine>;
        read: ReadOperation<AccountsReceivableAdvanceLine>;
        aggregate: {
            read: AggregateReadOperation<AccountsReceivableAdvanceLine>;
            query: AggregateQueryOperation<AccountsReceivableAdvanceLine>;
        };
        asyncOperations: AccountsReceivableAdvanceLine$AsyncOperations;
        lookups(dataOrId: string | { data: AccountsReceivableAdvanceLineInput }): AccountsReceivableAdvanceLine$Lookups;
        getDefaults: GetDefaultsOperation<AccountsReceivableAdvanceLine>;
    }
    export interface AccountsReceivableInvoiceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsReceivableInvoice;
        financialSite: Site;
        taxDate: string;
        providerSite: Site;
        account: Account;
        documentLineType: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType: AccountsPayableReceivableInvoiceLineType;
        currency: Currency;
        amountExcludingTax: string;
        taxLineTaxAmount: string;
        taxDetail: string;
        quantity: string;
        quantityInSalesUnit: string;
        description: string;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        attributesAndDimensions: ClientCollection<AccountsReceivableInvoiceLineDimension>;
        accountingStagingLines: ClientCollection<AccountsReceivableInvoiceLineStaging>;
        taxes: ClientCollection<AccountsReceivableInvoiceLineTax>;
        uiTaxes: string;
        storedDimensions: string;
        storedAttributes: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxAmountAdjusted: string;
        amountIncludingTax: string;
        netPriceIncludingTax: string;
    }
    export interface AccountsReceivableInvoiceLineInput extends VitalClientNodeInput {
        financialSite?: integer | string;
        taxDate?: string;
        providerSite?: integer | string;
        account?: integer | string;
        documentLineType?: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType?: AccountsPayableReceivableInvoiceLineType;
        currency?: integer | string;
        amountExcludingTax?: decimal | string;
        taxLineTaxAmount?: decimal | string;
        taxDetail?: string;
        quantity?: decimal | string;
        description?: string;
        sourceDocumentNumber?: string;
        attributesAndDimensions?: Partial<AccountsReceivableInvoiceLineDimensionInput>[];
        accountingStagingLines?: Partial<AccountsReceivableInvoiceLineStagingInput>[];
        taxes?: Partial<AccountsReceivableInvoiceLineTaxInput>[];
        uiTaxes?: string;
        netPriceIncludingTax?: decimal | string;
    }
    export interface AccountsReceivableInvoiceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsReceivableInvoice;
        financialSite: Site;
        taxDate: string;
        providerSite: Site;
        account: Account;
        documentLineType: AccountsPayableReceivableInvoiceDocumentLineType;
        lineType: AccountsPayableReceivableInvoiceLineType;
        currency: Currency;
        amountExcludingTax: string;
        taxLineTaxAmount: string;
        taxDetail: string;
        quantity: string;
        quantityInSalesUnit: string;
        description: string;
        sourceDocumentNumber: string;
        sourceDocumentType: SourceDocumentType;
        attributesAndDimensions: ClientCollection<AccountsReceivableInvoiceLineDimensionBinding>;
        accountingStagingLines: ClientCollection<AccountsReceivableInvoiceLineStagingBinding>;
        taxes: ClientCollection<AccountsReceivableInvoiceLineTaxBinding>;
        uiTaxes: any;
        storedDimensions: any;
        storedAttributes: any;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxAmountAdjusted: string;
        amountIncludingTax: string;
        netPriceIncludingTax: string;
    }
    export interface AccountsReceivableInvoiceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsReceivableInvoiceLine$Lookups {
        financialSite: QueryOperation<Site>;
        providerSite: QueryOperation<Site>;
        account: QueryOperation<Account>;
        currency: QueryOperation<Currency>;
    }
    export interface AccountsReceivableInvoiceLine$Operations {
        query: QueryOperation<AccountsReceivableInvoiceLine>;
        read: ReadOperation<AccountsReceivableInvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<AccountsReceivableInvoiceLine>;
            query: AggregateQueryOperation<AccountsReceivableInvoiceLine>;
        };
        asyncOperations: AccountsReceivableInvoiceLine$AsyncOperations;
        lookups(dataOrId: string | { data: AccountsReceivableInvoiceLineInput }): AccountsReceivableInvoiceLine$Lookups;
        getDefaults: GetDefaultsOperation<AccountsReceivableInvoiceLine>;
    }
    export interface AccountsReceivableInvoiceLineTax extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: AccountsReceivableInvoiceLine;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface AccountsReceivableInvoiceLineTaxInput extends VitalClientNodeInput {
        taxRate?: decimal | string;
        taxCategoryReference?: integer | string;
        taxReference?: integer | string;
        deductibleTaxRate?: decimal | string;
        isReverseCharge?: boolean | string;
        isTaxMandatory?: boolean | string;
        isSubjectToGlTaxExcludedAmount?: boolean | string;
        jurisdictionName?: string;
        taxCategory?: string;
        tax?: string;
        deductibleTaxAmount?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        nonTaxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxableAmount?: decimal | string;
    }
    export interface AccountsReceivableInvoiceLineTaxBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: AccountsReceivableInvoiceLine;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface AccountsReceivableInvoiceLineTax$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsReceivableInvoiceLineTax$Lookups {
        taxCategoryReference: QueryOperation<TaxCategory>;
        taxReference: QueryOperation<Tax>;
        currency: QueryOperation<Currency>;
    }
    export interface AccountsReceivableInvoiceLineTax$Operations {
        query: QueryOperation<AccountsReceivableInvoiceLineTax>;
        read: ReadOperation<AccountsReceivableInvoiceLineTax>;
        aggregate: {
            read: AggregateReadOperation<AccountsReceivableInvoiceLineTax>;
            query: AggregateQueryOperation<AccountsReceivableInvoiceLineTax>;
        };
        asyncOperations: AccountsReceivableInvoiceLineTax$AsyncOperations;
        lookups(
            dataOrId: string | { data: AccountsReceivableInvoiceLineTaxInput },
        ): AccountsReceivableInvoiceLineTax$Lookups;
        getDefaults: GetDefaultsOperation<AccountsReceivableInvoiceLineTax>;
    }
    export interface AccountsReceivableInvoiceTax extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: AccountsReceivableInvoice;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface AccountsReceivableInvoiceTaxInput extends VitalClientNodeInput {
        taxRate?: decimal | string;
        taxCategoryReference?: integer | string;
        taxReference?: integer | string;
        deductibleTaxRate?: decimal | string;
        isReverseCharge?: boolean | string;
        isTaxMandatory?: boolean | string;
        isSubjectToGlTaxExcludedAmount?: boolean | string;
        jurisdictionName?: string;
        taxCategory?: string;
        tax?: string;
        deductibleTaxAmount?: decimal | string;
        taxAmountAdjusted?: decimal | string;
        nonTaxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxableAmount?: decimal | string;
    }
    export interface AccountsReceivableInvoiceTaxBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        taxRate: string;
        taxCategoryReference: TaxCategory;
        taxReference: Tax;
        deductibleTaxRate: string;
        isReverseCharge: boolean;
        isTaxMandatory: boolean;
        isSubjectToGlTaxExcludedAmount: boolean;
        jurisdictionName: string;
        document: AccountsReceivableInvoice;
        taxCategory: string;
        tax: string;
        currency: Currency;
        deductibleTaxAmount: string;
        taxAmountAdjusted: string;
        nonTaxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxableAmount: string;
    }
    export interface AccountsReceivableInvoiceTax$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsReceivableInvoiceTax$Lookups {
        taxCategoryReference: QueryOperation<TaxCategory>;
        taxReference: QueryOperation<Tax>;
        currency: QueryOperation<Currency>;
    }
    export interface AccountsReceivableInvoiceTax$Operations {
        query: QueryOperation<AccountsReceivableInvoiceTax>;
        read: ReadOperation<AccountsReceivableInvoiceTax>;
        aggregate: {
            read: AggregateReadOperation<AccountsReceivableInvoiceTax>;
            query: AggregateQueryOperation<AccountsReceivableInvoiceTax>;
        };
        asyncOperations: AccountsReceivableInvoiceTax$AsyncOperations;
        lookups(dataOrId: string | { data: AccountsReceivableInvoiceTaxInput }): AccountsReceivableInvoiceTax$Lookups;
        getDefaults: GetDefaultsOperation<AccountsReceivableInvoiceTax>;
    }
    export interface AccountsReceivableOpenItem extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        dueDate: string;
        businessEntity: BusinessEntity;
        businessRelation: Customer;
        businessEntityPayment: BusinessEntity;
        type: BusinessEntityType;
        currency: Currency;
        transactionAmountDue: string;
        transactionAmountDueSigned: string;
        transactionAmountPaid: string;
        transactionAmountPaidSigned: string;
        remainingTransactionAmountSigned: string;
        companyAmountDue: string;
        companyAmountDueSigned: string;
        companyAmountPaid: string;
        companyAmountPaidSigned: string;
        remainingCompanyAmountSigned: string;
        financialSiteAmountDue: string;
        financialSiteAmountPaid: string;
        status: OpenItemStatus;
        documentType: FinanceDocumentType;
        documentNumber: string;
        documentSysId: integer;
        discountFrom: DueDateType;
        discountDate: integer;
        discountType: PaymentTermDiscountOrPenaltyType;
        discountAmount: string;
        discountPaymentBeforeDate: string;
        displayDiscountPaymentDate: string;
        penaltyPaymentType: DiscountOrPenaltyType;
        penaltyAmount: string;
        closeReason: CloseReason;
        closeText: string;
        forcedAmountPaid: string;
        forcedAmountPaidSigned: string;
        accountsReceivableInvoice: AccountsReceivableInvoice;
        financialSite: Site;
        totalAmount: string;
        amountDue: string;
        totalCompanyAmount: string;
        totalCompanyAmountPaid: string;
        remainingCompanyAmount: string;
        receipts: ClientCollection<PaymentDocumentLine>;
    }
    export interface AccountsReceivableOpenItemInput extends ClientNodeInput {
        dueDate?: string;
        businessEntity?: integer | string;
        businessEntityPayment?: integer | string;
        type?: BusinessEntityType;
        currency?: integer | string;
        transactionAmountDue?: decimal | string;
        transactionAmountPaid?: decimal | string;
        companyAmountDue?: decimal | string;
        companyAmountPaid?: decimal | string;
        financialSiteAmountDue?: decimal | string;
        financialSiteAmountPaid?: decimal | string;
        documentType?: FinanceDocumentType;
        documentNumber?: string;
        documentSysId?: integer | string;
        discountFrom?: DueDateType;
        discountDate?: integer | string;
        discountType?: PaymentTermDiscountOrPenaltyType;
        discountAmount?: decimal | string;
        discountPaymentBeforeDate?: string;
        penaltyPaymentType?: DiscountOrPenaltyType;
        penaltyAmount?: decimal | string;
        closeReason?: integer | string;
        closeText?: string;
        forcedAmountPaid?: decimal | string;
        accountsReceivableInvoice?: integer | string;
    }
    export interface AccountsReceivableOpenItemBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        dueDate: string;
        businessEntity: BusinessEntity;
        businessRelation: Customer;
        businessEntityPayment: BusinessEntity;
        type: BusinessEntityType;
        currency: Currency;
        transactionAmountDue: string;
        transactionAmountDueSigned: string;
        transactionAmountPaid: string;
        transactionAmountPaidSigned: string;
        remainingTransactionAmountSigned: string;
        companyAmountDue: string;
        companyAmountDueSigned: string;
        companyAmountPaid: string;
        companyAmountPaidSigned: string;
        remainingCompanyAmountSigned: string;
        financialSiteAmountDue: string;
        financialSiteAmountPaid: string;
        status: OpenItemStatus;
        documentType: FinanceDocumentType;
        documentNumber: string;
        documentSysId: integer;
        discountFrom: DueDateType;
        discountDate: integer;
        discountType: PaymentTermDiscountOrPenaltyType;
        discountAmount: string;
        discountPaymentBeforeDate: string;
        displayDiscountPaymentDate: string;
        penaltyPaymentType: DiscountOrPenaltyType;
        penaltyAmount: string;
        closeReason: CloseReason;
        closeText: string;
        forcedAmountPaid: string;
        forcedAmountPaidSigned: string;
        accountsReceivableInvoice: AccountsReceivableInvoice;
        financialSite: Site;
        totalAmount: string;
        amountDue: string;
        totalCompanyAmount: string;
        totalCompanyAmountPaid: string;
        remainingCompanyAmount: string;
        receipts: ClientCollection<PaymentDocumentLine>;
    }
    export interface AccountsReceivableOpenItem$AsyncOperations {
        bulkOpenItemUpdate: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsReceivableOpenItem$Lookups {
        businessEntity: QueryOperation<BusinessEntity>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        businessEntityPayment: QueryOperation<BusinessEntity>;
        currency: QueryOperation<Currency>;
        closeReason: QueryOperation<CloseReason>;
        accountsReceivableInvoice: QueryOperation<AccountsReceivableInvoice>;
        financialSite: QueryOperation<Site>;
    }
    export interface AccountsReceivableOpenItem$Operations {
        query: QueryOperation<AccountsReceivableOpenItem>;
        read: ReadOperation<AccountsReceivableOpenItem>;
        aggregate: {
            read: AggregateReadOperation<AccountsReceivableOpenItem>;
            query: AggregateQueryOperation<AccountsReceivableOpenItem>;
        };
        update: UpdateOperation<AccountsReceivableOpenItemInput, AccountsReceivableOpenItem>;
        updateById: UpdateByIdOperation<AccountsReceivableOpenItemInput, AccountsReceivableOpenItem>;
        asyncOperations: AccountsReceivableOpenItem$AsyncOperations;
        lookups(dataOrId: string | { data: AccountsReceivableOpenItemInput }): AccountsReceivableOpenItem$Lookups;
        getDefaults: GetDefaultsOperation<AccountsReceivableOpenItem>;
    }
    export interface AccountsReceivablePaymentLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsReceivablePayment;
        type: FinanceDocumentType;
        financialSite: Site;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        paymentAmount: string;
        paymentCompanyAmount: string;
    }
    export interface AccountsReceivablePaymentLineInput extends VitalClientNodeInput {
        type?: FinanceDocumentType;
        financialSite?: integer | string;
        currency?: integer | string;
        paymentAmount?: decimal | string;
        paymentCompanyAmount?: decimal | string;
    }
    export interface AccountsReceivablePaymentLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: AccountsReceivablePayment;
        type: FinanceDocumentType;
        financialSite: Site;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        paymentAmount: string;
        paymentCompanyAmount: string;
    }
    export interface AccountsReceivablePaymentLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AccountsReceivablePaymentLine$Lookups {
        financialSite: QueryOperation<Site>;
        currency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface AccountsReceivablePaymentLine$Operations {
        query: QueryOperation<AccountsReceivablePaymentLine>;
        read: ReadOperation<AccountsReceivablePaymentLine>;
        aggregate: {
            read: AggregateReadOperation<AccountsReceivablePaymentLine>;
            query: AggregateQueryOperation<AccountsReceivablePaymentLine>;
        };
        asyncOperations: AccountsReceivablePaymentLine$AsyncOperations;
        lookups(dataOrId: string | { data: AccountsReceivablePaymentLineInput }): AccountsReceivablePaymentLine$Lookups;
        getDefaults: GetDefaultsOperation<AccountsReceivablePaymentLine>;
    }
    export interface Payment extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        bankAccount: BankAccount;
        financialSite: Site;
        financialSiteName: string;
        businessRelation: BaseBusinessRelation;
        businessRelationName: string;
        customer: BaseBusinessRelation;
        supplier: BaseBusinessRelation;
        type: BusinessRelationType;
        paymentMethod: PaymentMethod;
        reference: string;
        paymentDate: string;
        postingDate: string;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        exchangeRateDate: string;
        companyExchangeRate: string;
        companyExchangeRateDivisor: string;
        postingStatus: JournalStatus;
        amount: string;
        companyAmount: string;
        amountBankCurrency: string;
        isVoided: boolean;
        voidText: string;
        voidDate: string;
        lines: ClientCollection<PaymentDocumentLine>;
    }
    export interface PaymentInput extends ClientNodeInput {
        number?: string;
        bankAccount?: integer | string;
        financialSite?: integer | string;
        financialSiteName?: string;
        businessRelation?: integer | string;
        businessRelationName?: string;
        type?: BusinessRelationType;
        paymentMethod?: PaymentMethod;
        reference?: string;
        paymentDate?: string;
        postingDate?: string;
        currency?: integer | string;
        exchangeRateDate?: string;
        companyExchangeRate?: decimal | string;
        companyExchangeRateDivisor?: decimal | string;
        postingStatus?: JournalStatus;
        amount?: decimal | string;
        companyAmount?: decimal | string;
        amountBankCurrency?: decimal | string;
        isVoided?: boolean | string;
        voidText?: string;
        voidDate?: string;
        lines?: Partial<PaymentDocumentLineInput>[];
    }
    export interface PaymentBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        bankAccount: BankAccount;
        financialSite: Site;
        financialSiteName: string;
        businessRelation: BaseBusinessRelation;
        businessRelationName: string;
        customer: BaseBusinessRelation;
        supplier: BaseBusinessRelation;
        type: BusinessRelationType;
        paymentMethod: PaymentMethod;
        reference: string;
        paymentDate: string;
        postingDate: string;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        exchangeRateDate: string;
        companyExchangeRate: string;
        companyExchangeRateDivisor: string;
        postingStatus: JournalStatus;
        amount: string;
        companyAmount: string;
        amountBankCurrency: string;
        isVoided: boolean;
        voidText: string;
        voidDate: string;
        lines: ClientCollection<PaymentDocumentLineBinding>;
    }
    export interface Payment$Mutations {
        voidPayment: Node$Operation<
            {
                payment: string;
                voidDate: string;
                voidText?: string;
            },
            string
        >;
    }
    export interface Payment$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Payment$Lookups {
        bankAccount: QueryOperation<BankAccount>;
        financialSite: QueryOperation<Site>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        customer: QueryOperation<BaseBusinessRelation>;
        supplier: QueryOperation<BaseBusinessRelation>;
        currency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface Payment$Operations {
        query: QueryOperation<Payment>;
        read: ReadOperation<Payment>;
        aggregate: {
            read: AggregateReadOperation<Payment>;
            query: AggregateQueryOperation<Payment>;
        };
        create: CreateOperation<PaymentInput, Payment>;
        getDuplicate: GetDuplicateOperation<Payment>;
        mutations: Payment$Mutations;
        asyncOperations: Payment$AsyncOperations;
        lookups(dataOrId: string | { data: PaymentInput }): Payment$Lookups;
        getDefaults: GetDefaultsOperation<Payment>;
    }
    export interface Receipt extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        bankAccount: BankAccount;
        financialSite: Site;
        financialSiteName: string;
        businessRelation: BaseBusinessRelation;
        businessRelationName: string;
        customer: BaseBusinessRelation;
        supplier: BaseBusinessRelation;
        type: BusinessRelationType;
        paymentMethod: PaymentMethod;
        reference: string;
        paymentDate: string;
        postingDate: string;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        exchangeRateDate: string;
        companyExchangeRate: string;
        companyExchangeRateDivisor: string;
        postingStatus: JournalStatus;
        amount: string;
        companyAmount: string;
        amountBankCurrency: string;
        isVoided: boolean;
        voidText: string;
        voidDate: string;
        lines: ClientCollection<PaymentDocumentLine>;
    }
    export interface ReceiptInput extends ClientNodeInput {
        number?: string;
        bankAccount?: integer | string;
        financialSite?: integer | string;
        financialSiteName?: string;
        businessRelation?: integer | string;
        businessRelationName?: string;
        type?: BusinessRelationType;
        paymentMethod?: PaymentMethod;
        reference?: string;
        paymentDate?: string;
        postingDate?: string;
        currency?: integer | string;
        exchangeRateDate?: string;
        companyExchangeRate?: decimal | string;
        companyExchangeRateDivisor?: decimal | string;
        postingStatus?: JournalStatus;
        amount?: decimal | string;
        companyAmount?: decimal | string;
        amountBankCurrency?: decimal | string;
        isVoided?: boolean | string;
        voidText?: string;
        voidDate?: string;
        lines?: Partial<PaymentDocumentLineInput>[];
    }
    export interface ReceiptBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        bankAccount: BankAccount;
        financialSite: Site;
        financialSiteName: string;
        businessRelation: BaseBusinessRelation;
        businessRelationName: string;
        customer: BaseBusinessRelation;
        supplier: BaseBusinessRelation;
        type: BusinessRelationType;
        paymentMethod: PaymentMethod;
        reference: string;
        paymentDate: string;
        postingDate: string;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        exchangeRateDate: string;
        companyExchangeRate: string;
        companyExchangeRateDivisor: string;
        postingStatus: JournalStatus;
        amount: string;
        companyAmount: string;
        amountBankCurrency: string;
        isVoided: boolean;
        voidText: string;
        voidDate: string;
        lines: ClientCollection<PaymentDocumentLineBinding>;
    }
    export interface Receipt$Mutations {
        voidPayment: Node$Operation<
            {
                payment: string;
                voidDate: string;
                voidText?: string;
            },
            string
        >;
    }
    export interface Receipt$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Receipt$Lookups {
        bankAccount: QueryOperation<BankAccount>;
        financialSite: QueryOperation<Site>;
        businessRelation: QueryOperation<BaseBusinessRelation>;
        customer: QueryOperation<BaseBusinessRelation>;
        supplier: QueryOperation<BaseBusinessRelation>;
        currency: QueryOperation<Currency>;
        companyCurrency: QueryOperation<Currency>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface Receipt$Operations {
        query: QueryOperation<Receipt>;
        read: ReadOperation<Receipt>;
        aggregate: {
            read: AggregateReadOperation<Receipt>;
            query: AggregateQueryOperation<Receipt>;
        };
        create: CreateOperation<ReceiptInput, Receipt>;
        getDuplicate: GetDuplicateOperation<Receipt>;
        mutations: Receipt$Mutations;
        asyncOperations: Receipt$AsyncOperations;
        lookups(dataOrId: string | { data: ReceiptInput }): Receipt$Lookups;
        getDefaults: GetDefaultsOperation<Receipt>;
    }
    export interface BusinessEntityExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        legalEntity: LegalEntity;
        name: string;
        country: Country;
        currency: Currency;
        taxIdNumber: string;
        siret: string;
        addresses: ClientCollection<BusinessEntityAddress>;
        contacts: ClientCollection<BusinessEntityContact>;
        primaryAddress: BusinessEntityAddress;
        primaryContact: BusinessEntityContact;
        image: BinaryStream;
        website: string;
        isSite: boolean;
        parent: BusinessEntity;
        site: Site;
        customer: Customer;
        supplier: Supplier;
        composedDescription: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        isSupplier: boolean;
        isCustomer: boolean;
    }
    export interface BusinessEntityInputExtension {
        id?: string;
        isActive?: boolean | string;
        legalEntity?: LegalEntity;
        name?: string;
        country?: integer | string;
        currency?: integer | string;
        taxIdNumber?: string;
        siret?: string;
        addresses?: Partial<BusinessEntityAddressInput>[];
        contacts?: Partial<BusinessEntityContactInput>[];
        image?: BinaryStream;
        website?: string;
        parent?: integer | string;
        site?: SiteInput;
        customer?: CustomerInput;
        supplier?: SupplierInput;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface BusinessEntityBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        legalEntity: LegalEntity;
        name: string;
        country: Country;
        currency: Currency;
        taxIdNumber: string;
        siret: string;
        addresses: ClientCollection<BusinessEntityAddressBinding>;
        contacts: ClientCollection<BusinessEntityContactBinding>;
        primaryAddress: BusinessEntityAddress;
        primaryContact: BusinessEntityContact;
        image: BinaryStream;
        website: string;
        isSite: boolean;
        parent: BusinessEntity;
        site: SiteBinding;
        customer: CustomerBinding;
        supplier: SupplierBinding;
        composedDescription: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        isSupplier: boolean;
        isCustomer: boolean;
    }
    export interface ItemExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergen>;
        classifications: ClientCollection<ItemClassifications>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPrice>;
        itemSites: ClientCollection<ItemSite>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPrice>;
        capacity: integer;
        landedCostItem: LandedCostItem;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        inStockQuantity: string;
        inTransitStockQuantity: string;
        itemTaxGroup: ItemTaxGroup;
        postingClass: PostingClass;
        storedDimensions: string;
        storedAttributes: string;
        composedDescription: string;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
        analyticalData: AnalyticalData;
    }
    export interface ItemInputExtension {
        name?: string;
        description?: string;
        status?: ItemStatus;
        type?: ItemType;
        isBought?: boolean | string;
        isManufactured?: boolean | string;
        isSold?: boolean | string;
        eanNumber?: string;
        commodityCode?: string;
        stockUnit?: integer | string;
        volumeUnit?: integer | string;
        volume?: decimal | string;
        weightUnit?: integer | string;
        weight?: decimal | string;
        density?: decimal | string;
        image?: BinaryStream;
        allergens?: Partial<ItemAllergenInput>[];
        classifications?: Partial<ItemClassificationsInput>[];
        category?: integer | string;
        isStockManaged?: boolean | string;
        isPhantom?: boolean | string;
        isBomRevisionManaged?: boolean | string;
        bomRevisionSequenceNumber?: integer | string;
        isPotencyManagement?: boolean | string;
        isTraceabilityManagement?: boolean | string;
        supplierPrices?: Partial<ItemSupplierPriceInput>[];
        itemSites?: Partial<ItemSiteInput>[];
        suppliers?: Partial<ItemSupplierInput>[];
        customers?: Partial<ItemCustomerInput>[];
        purchaseUnit?: integer | string;
        purchaseUnitToStockUnitConversion?: decimal | string;
        serialNumberManagement?: SerialNumberManagement;
        serialNumberSequenceNumber?: integer | string;
        serialNumberUsage?: SerialNumberUsage;
        salesUnit?: integer | string;
        salesUnitToStockUnitConversion?: decimal | string;
        minimumSalesQuantity?: decimal | string;
        maximumSalesQuantity?: decimal | string;
        currency?: integer | string;
        basePrice?: decimal | string;
        minimumPrice?: decimal | string;
        customerPrices?: Partial<ItemCustomerPriceInput>[];
        capacity?: integer | string;
        landedCostItem?: LandedCostItemInput;
        itemTaxGroup?: integer | string;
        postingClass?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        id?: string;
        lotManagement?: LotManagement;
        lotSequenceNumber?: integer | string;
        isExpiryManaged?: boolean | string;
        analyticalData?: integer | string;
    }
    export interface ItemBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergenBinding>;
        classifications: ClientCollection<ItemClassificationsBinding>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPriceBinding>;
        itemSites: ClientCollection<ItemSiteBinding>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversion: string;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversion: string;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPriceBinding>;
        capacity: integer;
        landedCostItem: LandedCostItemBinding;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        inStockQuantity: string;
        inTransitStockQuantity: string;
        itemTaxGroup: ItemTaxGroup;
        postingClass: PostingClass;
        storedDimensions: any;
        storedAttributes: any;
        composedDescription: string;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
        analyticalData: AnalyticalData;
    }
    export interface PaymentDocumentLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: BasePaymentDocument;
        financialSite: Site;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        amount: string;
        amountBankCurrency: string;
        discountAmount: string;
        penaltyAmount: string;
        adjustmentAmount: string;
        paymentTracking: PaymentTracking;
        arOpenItem: AccountsReceivableOpenItem;
        apOpenItem: AccountsPayableOpenItem;
        companyAmount: string;
        signedAmount: string;
        signedAmountBankCurrency: string;
        origin: AccountsPayableReceivableInvoiceOrigin;
        originalOpenItem: BaseOpenItem;
        originalNodeFactory: MetaNodeFactory;
    }
    export interface PaymentDocumentLineInputExtension {
        financialSite?: integer | string;
        currency?: integer | string;
        amount?: decimal | string;
        amountBankCurrency?: decimal | string;
        discountAmount?: decimal | string;
        penaltyAmount?: decimal | string;
        adjustmentAmount?: decimal | string;
        paymentTracking?: integer | string;
        arOpenItem?: integer | string;
        apOpenItem?: integer | string;
        companyAmount?: decimal | string;
        origin?: AccountsPayableReceivableInvoiceOrigin;
        originalOpenItem?: integer | string;
        originalNodeFactory?: integer | string;
    }
    export interface PaymentDocumentLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: BasePaymentDocument;
        financialSite: Site;
        currency: Currency;
        companyCurrency: Currency;
        transactionCurrency: Currency;
        amount: string;
        amountBankCurrency: string;
        discountAmount: string;
        penaltyAmount: string;
        adjustmentAmount: string;
        paymentTracking: PaymentTracking;
        arOpenItem: AccountsReceivableOpenItem;
        apOpenItem: AccountsPayableOpenItem;
        companyAmount: string;
        signedAmount: string;
        signedAmountBankCurrency: string;
        origin: AccountsPayableReceivableInvoiceOrigin;
        originalOpenItem: BaseOpenItem;
        originalNodeFactory: MetaNodeFactory;
    }
    export interface PaymentDocumentLineExtension$Lookups {
        arOpenItem: QueryOperation<AccountsReceivableOpenItem>;
        apOpenItem: QueryOperation<AccountsPayableOpenItem>;
    }
    export interface PaymentDocumentLineExtension$Operations {
        lookups(dataOrId: string | { data: PaymentDocumentLineInput }): PaymentDocumentLineExtension$Lookups;
    }
    export interface SiteExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
        siteGroups: ClientCollection<SiteGroup>;
        groupRoleSites: ClientCollection<GroupRoleSite>;
        hierarchyChartContent: TextStream;
        businessEntity: BusinessEntity;
        financialCurrency: Currency;
        isFinance: boolean;
        isPurchase: boolean;
        isInventory: boolean;
        isSales: boolean;
        isManufacturing: boolean;
        isProjectManagement: boolean;
        stockSite: Site;
        itemSites: ClientCollection<ItemSite>;
        primaryAddress: BusinessEntityAddress;
        financialSite: Site;
        isLocationManaged: boolean;
        defaultLocation: Location;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        timeZone: string;
        country: Country;
        taxIdNumber: string;
        siret: string;
        currency: Currency;
        defaultStockStatus: StockStatus;
        storedDimensions: string;
        storedAttributes: string;
        composedDescription: string;
        _syncTick: string;
        analyticalData: AnalyticalData;
    }
    export interface SiteInputExtension {
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        legalCompany?: integer | string;
        businessEntity?: BusinessEntityInput;
        isFinance?: boolean | string;
        isPurchase?: boolean | string;
        isInventory?: boolean | string;
        isSales?: boolean | string;
        isManufacturing?: boolean | string;
        isProjectManagement?: boolean | string;
        primaryAddress?: integer | string;
        financialSite?: integer | string;
        isLocationManaged?: boolean | string;
        defaultLocation?: integer | string;
        sequenceNumberId?: string;
        timeZone?: string;
        defaultStockStatus?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        _syncTick?: decimal | string;
        analyticalData?: integer | string;
    }
    export interface SiteBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
        siteGroups: ClientCollection<SiteGroup>;
        groupRoleSites: ClientCollection<GroupRoleSite>;
        hierarchyChartContent: TextStream;
        businessEntity: BusinessEntity;
        financialCurrency: Currency;
        isFinance: boolean;
        isPurchase: boolean;
        isInventory: boolean;
        isSales: boolean;
        isManufacturing: boolean;
        isProjectManagement: boolean;
        stockSite: Site;
        itemSites: ClientCollection<ItemSite>;
        primaryAddress: BusinessEntityAddress;
        financialSite: Site;
        isLocationManaged: boolean;
        defaultLocation: Location;
        sequenceNumberId: string;
        isSequenceNumberIdUsed: boolean;
        timeZone: string;
        country: Country;
        taxIdNumber: string;
        siret: string;
        currency: Currency;
        defaultStockStatus: StockStatus;
        storedDimensions: any;
        storedAttributes: any;
        composedDescription: string;
        _syncTick: string;
        analyticalData: AnalyticalData;
    }
    export interface Package {
        '@sage/xtrem-finance/AccountingInterfaceListener': AccountingInterfaceListener$Operations;
        '@sage/xtrem-finance/AccountsOpenItem': AccountsOpenItem$Operations;
        '@sage/xtrem-finance/AccountsPayableInvoice': AccountsPayableInvoice$Operations;
        '@sage/xtrem-finance/AccountsPayableInvoiceLineDimension': AccountsPayableInvoiceLineDimension$Operations;
        '@sage/xtrem-finance/AccountsReceivableAdvance': AccountsReceivableAdvance$Operations;
        '@sage/xtrem-finance/AccountsReceivableInvoice': AccountsReceivableInvoice$Operations;
        '@sage/xtrem-finance/AccountsReceivableInvoiceLineDimension': AccountsReceivableInvoiceLineDimension$Operations;
        '@sage/xtrem-finance/AccountsReceivablePayment': AccountsReceivablePayment$Operations;
        '@sage/xtrem-finance/DatevExport': DatevExport$Operations;
        '@sage/xtrem-finance/DatevExportAccount': DatevExportAccount$Operations;
        '@sage/xtrem-finance/DatevExportBusinessRelation': DatevExportBusinessRelation$Operations;
        '@sage/xtrem-finance/DatevExportJournalEntryLine': DatevExportJournalEntryLine$Operations;
        '@sage/xtrem-finance/InitializeOpenItem': InitializeOpenItem$Operations;
        '@sage/xtrem-finance/JournalEntry': JournalEntry$Operations;
        '@sage/xtrem-finance/JournalEntryInquiry': JournalEntryInquiry$Operations;
        '@sage/xtrem-finance/JournalEntryLine': JournalEntryLine$Operations;
        '@sage/xtrem-finance/JournalEntryLineDimension': JournalEntryLineDimension$Operations;
        '@sage/xtrem-finance/JournalEntryLineStaging': JournalEntryLineStaging$Operations;
        '@sage/xtrem-finance/AccountsPayableInvoiceLine': AccountsPayableInvoiceLine$Operations;
        '@sage/xtrem-finance/AccountsPayableInvoiceLineTax': AccountsPayableInvoiceLineTax$Operations;
        '@sage/xtrem-finance/AccountsPayableInvoiceTax': AccountsPayableInvoiceTax$Operations;
        '@sage/xtrem-finance/AccountsPayableOpenItem': AccountsPayableOpenItem$Operations;
        '@sage/xtrem-finance/AccountsReceivableAdvanceLine': AccountsReceivableAdvanceLine$Operations;
        '@sage/xtrem-finance/AccountsReceivableInvoiceLine': AccountsReceivableInvoiceLine$Operations;
        '@sage/xtrem-finance/AccountsReceivableInvoiceLineTax': AccountsReceivableInvoiceLineTax$Operations;
        '@sage/xtrem-finance/AccountsReceivableInvoiceTax': AccountsReceivableInvoiceTax$Operations;
        '@sage/xtrem-finance/AccountsReceivableOpenItem': AccountsReceivableOpenItem$Operations;
        '@sage/xtrem-finance/AccountsReceivablePaymentLine': AccountsReceivablePaymentLine$Operations;
        '@sage/xtrem-finance/Payment': Payment$Operations;
        '@sage/xtrem-finance/Receipt': Receipt$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremFinanceData$Package,
            SageXtremImportExport$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-finance-api' {
    export type * from '@sage/xtrem-finance-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-finance-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type {
        BusinessEntityBindingExtension,
        BusinessEntityExtension,
        BusinessEntityInputExtension,
        ItemBindingExtension,
        ItemExtension,
        ItemInputExtension,
    } from '@sage/xtrem-finance-api';
    export interface BusinessEntity extends BusinessEntityExtension {}
    export interface BusinessEntityBinding extends BusinessEntityBindingExtension {}
    export interface BusinessEntityInput extends BusinessEntityInputExtension {}
    export interface Item extends ItemExtension {}
    export interface ItemBinding extends ItemBindingExtension {}
    export interface ItemInput extends ItemInputExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type {
        PaymentDocumentLineBindingExtension,
        PaymentDocumentLineExtension,
        PaymentDocumentLineExtension$Lookups,
        PaymentDocumentLineExtension$Operations,
        PaymentDocumentLineInputExtension,
    } from '@sage/xtrem-finance-api';
    export interface PaymentDocumentLine extends PaymentDocumentLineExtension {}
    export interface PaymentDocumentLineBinding extends PaymentDocumentLineBindingExtension {}
    export interface PaymentDocumentLineInput extends PaymentDocumentLineInputExtension {}
    export interface PaymentDocumentLine$Lookups extends PaymentDocumentLineExtension$Lookups {}
    export interface PaymentDocumentLine$Operations extends PaymentDocumentLineExtension$Operations {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { SiteBindingExtension, SiteExtension, SiteInputExtension } from '@sage/xtrem-finance-api';
    export interface Site extends SiteExtension {}
    export interface SiteBinding extends SiteBindingExtension {}
    export interface SiteInput extends SiteInputExtension {}
}
