import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type { GraphApi, WorkOrderStatus } from '@sage/xtrem-manufacturing-api';
import type { StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import * as ui from '@sage/xtrem-ui';

/**
 * Control to check if routing exists and is available to use
 * @param page
 */
export async function categoryControlRouting(
    page: ui.Page<GraphApi>,
    workOrderItemId: string,
    workOrderSiteId: string,
) {
    page.$.loader.isHidden = false;
    const routing = extractEdges(
        await page.$.graph
            .node('@sage/xtrem-technical-data/Routing')
            .aggregate.query(
                ui.queryUtils.edgesSelector(
                    {
                        group: {
                            _id: { _by: 'value' },
                        },
                    },
                    {
                        filter: {
                            item: { _eq: workOrderItemId },
                            site: { _eq: workOrderSiteId },
                            status: { _eq: 'availableToUse' },
                        },
                    },
                ),
            )
            .execute()
            .finally(() => {
                page.$.loader.isHidden = true;
            }),
    );
    if (!routing.length) {
        return ui.localize(
            '@sage/xtrem-manufacturing/pages__work_order_panel__functions_routing_not_found',
            'No routing available to use found.',
        );
    }

    return undefined;
}

/**
 * Control to check if bill of material exists and is available to use
 * @param page
 */
export async function categoryControlBillOfMaterial(
    page: ui.Page<GraphApi>,
    workOrderItemId: string,
    workOrderSiteId: string,
) {
    page.$.loader.isHidden = false;
    const billOfMaterial = extractEdges(
        await page.$.graph
            .node('@sage/xtrem-technical-data/BillOfMaterial')
            .aggregate.query(
                ui.queryUtils.edgesSelector(
                    {
                        group: {
                            _id: { _by: 'value' },
                        },
                    },
                    {
                        filter: {
                            item: { _eq: workOrderItemId },
                            site: { _eq: workOrderSiteId },
                        },
                    },
                ),
            )
            .execute()
            .finally(() => {
                page.$.loader.isHidden = true;
            }),
    );
    if (!billOfMaterial.length) {
        return ui.localize(
            '@sage/xtrem-manufacturing/pages__work_order_panel__functions__bill_of_material_not_found',
            'No bill of material available to use found.',
        );
    }

    return undefined;
}

export function convertStringToJsDate(date: string): DateValue {
    return DateValue.fromJsDate(new Date(date));
}
/**
 * Warns the user that it's introducing a date in the past
 */
export function requestDateBeforeTodayWarning(startDate: DateValue): string {
    return startDate.compare(DateValue.today()) < 0
        ? ui.localize(
              '@sage/xtrem-manufacturing/pages__work_order_client_functions__date_is_in_the_past',
              'This date is in the past.',
          )
        : '';
}

export function incompleteWorkOrderCloseDialog(page: ui.Page<GraphApi>) {
    const continueText = ui.localize('@sage/xtrem-manufacturing/close-dialog-continue', 'Continue');
    const cancelText = ui.localize('@sage/xtrem-manufacturing/close-dialog-cancel', 'Cancel');

    return page.$.dialog.confirmation(
        'warn',
        ui.localize('@sage/xtrem-manufacturing/close-dialog-title', 'Confirm closing'),
        ui.localize(
            '@sage/xtrem-manufacturing/close-dialog-content',
            'You are about to close an incomplete work order.',
        ),
        { acceptButton: { text: continueText }, cancelButton: { text: cancelText }, resolveOnCancel: true },
    );
}

export async function confirmDialogWithAcceptButtonText(
    page: ui.Page,
    title: string,
    message: string,
    acceptButtonText: string,
    cancelButtonText?: string,
) {
    const options = {
        acceptButton: { text: acceptButtonText },
        cancelButton: {
            text: cancelButtonText || ui.localize('@sage/xtrem-manufacturing/pages-confirm-cancel', 'Cancel'),
        },
    };
    try {
        await page.$.dialog.confirmation('warn', title, message, options);
        return true;
    } catch {
        return false;
    }
}

export async function correctStockTransactionStatusInProgress(
    pageInstance: ui.Page,
    workOrder: string,
    status: WorkOrderStatus,
    stockTransactionStatus: StockDocumentTransactionStatus,
    closingDate: string,
) {
    if (status === 'completed' && stockTransactionStatus === 'inProgress' && closingDate !== '') {
        if (
            await confirmDialogWithAcceptButtonText(
                pageInstance,
                ui.localize(
                    '@sage/xtrem-manufacturing/warning__stock_transaction_status_in_progress',
                    'Close work order',
                ),
                ui.localize(
                    '@sage/xtrem-manufacturing/warning__stock_transaction_status_in_progress_message',
                    'You are about to change the status of this work order to Closed.',
                ),
                ui.localize(
                    '@sage/xtrem-manufacturing/warning__stock_transaction_status_in_progress_continue',
                    'Continue',
                ),
            )
        ) {
            const statusRefreshed = await pageInstance.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrder')
                .mutations.resynchronizeStatus(true, { workOrder })
                .execute();
            if (statusRefreshed) {
                await pageInstance.$.router.refresh();
            }
        }
    }
}

export async function resynchronizeStatus(page: ui.Page<GraphApi>, workOrderId: string) {
    if (
        await confirmDialogWithAcceptButtonText(
            page,
            ui.localize('@sage/xtrem-manufacturing/client_functions____resync_status_title', 'Check and update status'),
            ui.localize(
                '@sage/xtrem-manufacturing/client_functions__resync_status_message',
                'You are about to update the status.',
            ),
            ui.localize('@sage/xtrem-manufacturing/client_functions_resync_status_continue', 'Continue'),
        )
    ) {
        const { oldStatus, newStatus } = await page.$.graph
            .node('@sage/xtrem-manufacturing/WorkOrder')
            .asyncOperations.resynchronizeStatus.runToCompletion(
                { oldStatus: true, newStatus: true },
                { workOrder: workOrderId },
            )
            .execute();

        if (oldStatus !== newStatus) {
            await page.$.router.refresh();
        }
    }
}

export function getTrackingPage(sourceDocumentType: string): string {
    switch (sourceDocumentType) {
        case 'productionTracking':
            return `@sage/xtrem-manufacturing/ProductionTrackingInquiry`;
        case 'operationTracking':
            return `@sage/xtrem-manufacturing/TimeTrackingInquiry`;
        case 'materialTracking':
        default:
            return `@sage/xtrem-manufacturing/MaterialTrackingInquiry`;
    }
}
