import * as ui from '@sage/xtrem-ui';
import { extractEdges } from '@sage/xtrem-client';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { DateValue } from '@sage/xtrem-date-time';
import type { GraphApi } from '@sage/xtrem-manufacturing-api';
import type {
    BillOfMaterialParameters,
    BillOfMaterialWithExtensionParameters,
    CheckStockParameters,
} from './interfaces/stock-details';
import * as ManufacturingStockDetailHelper from './stock-details-helper';

export async function billOfMaterialResult(billOfMaterialParameters: BillOfMaterialParameters) {
    const billOfMaterial = extractEdges(
        await billOfMaterialParameters.billOfMaterialPage.$.graph
            .node('@sage/xtrem-technical-data/BillOfMaterial')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        item: {
                            name: true,
                        },
                        baseQuantity: true,
                    },
                    {
                        filter: { site: billOfMaterialParameters.siteId, item: billOfMaterialParameters.itemId },
                    },
                ),
            )
            .execute(),
    );
    return MasterDataUtils.removeExtractEdgesPartial(billOfMaterial);
}

export async function loadCheckComponentStock(checkStockParameters: CheckStockParameters) {
    checkStockParameters.stockCheck.isDisabled = true;
    const shortages = await checkStockParameters.pageInstance.$.graph
        .node('@sage/xtrem-manufacturing/WorkOrderComponent')
        .queries.checkComponentStock(
            {
                _id: true,
                itemId: true,
                itemName: true,
                itemDescription: true,
                componentName: true,
                available: true,
                required: true,
                shortage: true,
                unit: true,
            },
            {
                orderQuantity: checkStockParameters.quantity,
                bomId: checkStockParameters.documentLine ?? '',
            },
        )
        .execute();
    shortages.forEach(line =>
        checkStockParameters.componentShortage.addRecord({
            ...line,
            available: +(line.available ?? 0),
            required: +(line.required ?? 0),
            shortage: +(line.shortage ?? 0),
        }),
    );
    checkStockParameters.stockCheck.isDisabled = false;
    checkStockParameters.componentShortage.isHidden = false;
}

export function returnedStockDetails(billOfMaterialParameters: BillOfMaterialWithExtensionParameters) {
    return MasterDataUtils.confirmDialogToObject(
        ManufacturingStockDetailHelper.editStockDetails(
            billOfMaterialParameters.billOfMaterialPage as ui.Page<GraphApi>,
            billOfMaterialParameters.line[0],
            {
                movementType: 'receipt',
                data: {
                    isEditable: true,
                    fieldCustomizations: {
                        supplierLot: {
                            isHidden: true,
                        },
                        quantity: { isEditable: true },
                    },
                    returnCustomization: {
                        date: true,
                        quantity: true,
                    },
                    effectiveDate: DateValue.today().toString(),
                    documentLineSortValue: 0,
                    documentLine: billOfMaterialParameters.documentLine,
                    jsonStockDetails: '',
                    item: billOfMaterialParameters?.itemId,
                    stockSite: billOfMaterialParameters.siteId,
                    quantity: billOfMaterialParameters.baseQuantity,
                    unit: billOfMaterialParameters.itemStockUnitId,
                    stockStatus: billOfMaterialParameters.siteDefaultStockStatusId,
                    location: billOfMaterialParameters.siteDefaultLocationId,
                    stockTransactionStatus: 'draft',
                    trackCheckStock: billOfMaterialParameters.trackCheckStock,
                    additionalParameters: {
                        hasRouting:
                            !!billOfMaterialParameters.routingCode &&
                            billOfMaterialParameters.routingCodeStatus === 'availableToUse',
                        fieldCustomizations: {
                            category: {
                                isVisible: true,
                            },
                            componentShortage: {
                                isVisible: true,
                            },
                            stockCheck: {
                                isVisible: true,
                            },
                            okFromBom: {
                                isVisible: true,
                            },
                        },
                    },
                },
            },
        ),
    );
}
