import type * as StockDataInterfaces from '@sage/xtrem-stock-data/lib/client-functions/interfaces';
import type { BillOfMaterial } from '@sage/xtrem-technical-data/build/lib/pages/bill-of-material';
import type * as ui from '@sage/xtrem-ui';
import type { GraphApi } from '@sage/xtrem-manufacturing-api';
import type { ExtensionMembers } from '@sage/xtrem-ui/build/lib/service/page-extension';
import type { ScreenBase } from '@sage/xtrem-ui/build/lib/service/screen-base';
import type { decimal, integer } from '@sage/xtrem-client';
import type { BillOfMaterialExtension } from '../../page-extensions/bill-of-material-extension';

interface StockDetailPanelExtensionFieldCustomizationParam
    extends StockDataInterfaces.StockDetailPanelFieldCustomizationParam {
    isHidden?: undefined;
    isVisible?: boolean;
}

type StockReceiptDetailPanelManufacturingCustomizableFields = Partial<{
    category: StockDetailPanelExtensionFieldCustomizationParam;
    componentShortage: StockDetailPanelExtensionFieldCustomizationParam;
    stockCheck: StockDetailPanelExtensionFieldCustomizationParam;
    okFromBom: StockDetailPanelExtensionFieldCustomizationParam;
}>;

export interface StockReceiptDetailPanelManufacturingAdditionalParameters {
    hasRouting?: boolean;
    fieldCustomizations?: StockReceiptDetailPanelManufacturingCustomizableFields;
}

export interface StockReceiptDetailPanelParameters extends StockDataInterfaces.StockReceiptDetailPanelParameters {
    trackCheckStock: 'track' | 'checkStock';
    additionalParameters?: StockReceiptDetailPanelManufacturingAdditionalParameters;
    returnCustomization?: StockDataInterfaces.StockReceiptDetailPanelParameters['returnCustomization'] & {
        category?: boolean;
        stockDetailStatus?: boolean;
    };
}

export interface StockReceiptDetailPanelManufacturingAdditionalData {
    bom: Node;
    jsonStockDetails?: string;
    category: { categoryId: string; categoryRouting: boolean; categoryBillOfMaterial: boolean };
    quantity: string;
    date: string;
    stockDetailStatus?: string;
}

export interface BillOfMaterialParameters {
    billOfMaterialPage: ExtensionMembers<BillOfMaterial | BillOfMaterialExtension>;
    siteId: string | undefined;
    itemId: string | undefined;
}

export interface BillOfMaterialWithExtensionParameters extends BillOfMaterialParameters {
    billOfMaterialWithExtentionPage: ExtensionMembers<BillOfMaterial | BillOfMaterialExtension> | ui.Page<GraphApi>;
    line: {
        item: {
            name: string;
        };
        baseQuantity: string;
        _id: string;
    }[];
    documentLine: string | undefined;
    baseQuantity: number | undefined;
    itemStockUnitId: string | undefined;
    siteDefaultStockStatusId: string | undefined;
    siteDefaultLocationId: string | undefined;
    trackCheckStock: 'track' | 'checkStock';
    routingCode: string | undefined;
    routingCodeStatus: string | undefined;
}

export interface OperationResultType {
    _id: string;
    itemId: string;
    itemDescription: string;
    itemName: string;
    componentName: string;
    available: decimal;
    required: decimal;
    shortage?: decimal;
    unit: string;
    componentNumber: integer;
}

export interface CheckStockParameters {
    pageInstance: ui.Page<GraphApi>;
    componentShortage: ui.fields.TableControlObject<OperationResultType, ScreenBase>;
    documentLine: string;
    quantity: number;
    stockCheck: ui.fields.Button;
}
