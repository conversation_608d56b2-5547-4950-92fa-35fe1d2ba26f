import type { MaterialTracking, OperationTracking, ProductionTracking } from '@sage/xtrem-manufacturing-api';
import type { StockTransactionStatus } from '@sage/xtrem-stock-data-api';
import type { Site } from '@sage/xtrem-system-api';

export interface WorkOrderTrackingView {
    _id: string;
    id: string;
    number: string;
    status: StockTransactionStatus | null;
    type: string;
    site: Site;
    date: string;
}

export type TrackingDocument = MaterialTracking | (ProductionTracking & OperationTracking);
