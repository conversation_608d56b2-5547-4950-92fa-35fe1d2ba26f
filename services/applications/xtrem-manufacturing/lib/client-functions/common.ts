import type {
    MaterialTrackingLineBinding,
    OperationTrackingLine,
    OperationTrackingLineBinding,
    ProductionTrackingLineBinding,
} from '@sage/xtrem-manufacturing-api';

type BaseTrackingLine = OperationTrackingLine | ProductionTrackingLineBinding | MaterialTrackingLineBinding;

export function isBaseTrackingLine(rowItem: any): rowItem is BaseTrackingLine {
    if (rowItem.document) {
        return !!rowItem;
    }
    return false;
}

export function isOperationTrackingLineBinding(rowItem: any): rowItem is OperationTrackingLineBinding {
    if (rowItem.workOrderOperation) {
        return !!rowItem;
    }
    return false;
}

export function isProductionTrackingLineBinding(rowItem: any): rowItem is ProductionTrackingLineBinding {
    if (rowItem.workOrderLine.document) {
        return !!rowItem;
    }
    return false;
}

export function isMaterialTrackingLineBinding(rowItem: any): rowItem is MaterialTrackingLineBinding {
    if (rowItem.workOrderLine) {
        return !!rowItem;
    }
    return false;
}
