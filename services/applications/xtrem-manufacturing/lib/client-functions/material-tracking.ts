import { extractEdges, type decimal } from '@sage/xtrem-client';
import type { GraphApi } from '@sage/xtrem-manufacturing-api';
import type { StockAllocationStatus } from '@sage/xtrem-stock-data-api';
import * as ui from '@sage/xtrem-ui';

export async function getLineAllocatedQuantity(page: ui.Page<GraphApi>, lineId: string): Promise<number> {
    return Number(
        extractEdges(
            await page.$.graph
                .node('@sage/xtrem-stock-data/StockAllocation')
                .aggregate.query(
                    ui.queryUtils.edgesSelector(
                        {
                            group: { documentLine: { _id: { _by: 'value' } } },
                            values: { quantityInStockUnit: { sum: true } },
                        },
                        { filter: { documentLine: { _id: lineId } } },
                    ),
                )
                .execute(),
        ).at(0)?.values.quantityInStockUnit?.sum ?? 0,
    );
}

export async function setAllocationStatus(
    page: ui.Page,
    lineId: string,
    jsonStockDetails: any, // no type defined for this field yet
    remainingQuantity: decimal | undefined,
): Promise<StockAllocationStatus | undefined> {
    const allocatedQuantity = jsonStockDetails
        ? JSON.parse(jsonStockDetails).reduce((prev: number, detail: any) => {
              if (detail.action === 'transfer') return prev + +detail.quantityToTransfer;
              if (detail.action !== 'reject') return prev + +detail.quantity;
              return prev;
          }, 0)
        : await getLineAllocatedQuantity(page, lineId);

    if (allocatedQuantity === 0) {
        return 'notAllocated';
    }
    if (allocatedQuantity !== 0 && remainingQuantity && +remainingQuantity === +allocatedQuantity) {
        return 'allocated';
    }
    return 'partiallyAllocated';
}

export function showGeneratedTrackingsMessage(page: ui.Page, numberOfGeneratedTrackings: number) {
    page.$.showToast(
        ui.localize(
            '@sage/xtrem-manufacturing/pages__material__tracking__creation__success',
            'Tracking records generated: {{num}}',
            { num: numberOfGeneratedTrackings },
        ),
        { timeout: 10000, type: 'success' },
    );
}
