import type { MonthComparison } from './interfaces/widget-interfaces';

export function percentageCalculation(quantityProduced: MonthComparison) {
    if (quantityProduced && (quantityProduced.previousMonth > 0 || quantityProduced.currentMonth > 0)) {
        return (
            Math.round(
                (Number(quantityProduced.previousMonth)
                    ? (Number(quantityProduced.currentMonth) / Number(quantityProduced.previousMonth)) * 100
                    : 100) * 100,
            ) / 100
        );
    }
    return 0;
}
