import type {
    ComponentStatus,
    OperationStatus,
    ReleasedItemStatus,
    WorkInProgressInquiryStatus,
    WorkInProgressStatus,
    WorkOrderSchedulingStatus,
    WorkOrderStatus,
    WorkOrderType,
} from '@sage/xtrem-manufacturing-api';
import type { ColoredElement } from '@sage/xtrem-system-api';
import {
    colorfulPillPattern,
    colorfulPillPatternDefaulted,
} from '@sage/xtrem-system/build/lib/client-functions/color-pattern';

function workOrderStatusColor(status: WorkOrderStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'pending':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'completed':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'closed':
            return colorfulPillPattern.filledClosing[coloredElement];
        case 'costCalculated':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'printed':
            return colorfulPillPattern.filledInformation[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function workInProgressStatusColor(status: WorkInProgressStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'pending':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'posted':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function workInProgressStatusInquiryColor(status: WorkInProgressInquiryStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'completed':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function operationOrComponentStatusColor(status: OperationStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'pending':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'completed':
            return colorfulPillPattern.filledClosing[coloredElement];
        case 'excluded':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'included':
            return colorfulPillPattern.filledInformation[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

// Adding here the values that are not on the Enum to keep current code working
export function workOrderTypeColor(type: WorkOrderType | 'suggested' | 'closed', coloredElement: ColoredElement) {
    switch (type) {
        case 'firm':
            return colorfulPillPattern.outlinedPositive[coloredElement];
        case 'planned':
            return colorfulPillPattern.outlinedInformation[coloredElement];
        case 'suggested':
            return colorfulPillPattern.outlinedFocus[coloredElement];
        case 'closed':
            return colorfulPillPattern.outlinedCaution[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function workOrderSchedulingStatusColor(status: WorkOrderSchedulingStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'notScheduled':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'scheduled':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'notManaged':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'toReschedule':
            return colorfulPillPattern.filledCaution[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

type WorkOrderDataEnumNames = 'ComponentStatus' | 'OperationStatus' | 'ReleasedItemStatus';
type WorkOrderEnumNames = 'WorkOrderStatus' | 'WorkOrderType' | 'WorkOrderSchedulingStatus';

type WorkOrderEnumValues = WorkOrderStatus | WorkOrderType | WorkOrderSchedulingStatus;
type WorkOrderDataEnumValues = ComponentStatus | OperationStatus | ReleasedItemStatus;

export function getLabelColorByStatus(
    enumEntry:
        | WorkOrderEnumNames
        | WorkOrderDataEnumNames
        | 'StockDetailStatus'
        | 'WorkInProgressStatus'
        | 'WorkInProgressInquiryStatus',
    status?: WorkOrderEnumValues | WorkOrderDataEnumValues | WorkInProgressStatus | WorkInProgressInquiryStatus | null,
): {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
} {
    const getColor = (coloredElement: ColoredElement) => {
        switch (enumEntry) {
            case 'ReleasedItemStatus':
            case 'WorkOrderStatus':
                return workOrderStatusColor(status as WorkOrderStatus, coloredElement);
            case 'WorkInProgressInquiryStatus':
                return workInProgressStatusInquiryColor(status as WorkInProgressInquiryStatus, coloredElement);
            case 'WorkInProgressStatus':
                return workInProgressStatusColor(status as WorkInProgressStatus, coloredElement);
            case 'OperationStatus':
            case 'ComponentStatus':
                return operationOrComponentStatusColor(status as OperationStatus, coloredElement);
            case 'WorkOrderType':
                return workOrderTypeColor(status as WorkOrderType, coloredElement);
            case 'WorkOrderSchedulingStatus':
                return workOrderSchedulingStatusColor(status as WorkOrderSchedulingStatus, coloredElement);
            default:
                return colorfulPillPatternDefaulted(coloredElement);
        }
    };

    return {
        backgroundColor: getColor('backgroundColor'),
        borderColor: getColor('borderColor'),
        textColor: getColor('textColor'),
    };
}

export function getTransparentStatusStyle() {
    return colorfulPillPattern.transparent;
}
