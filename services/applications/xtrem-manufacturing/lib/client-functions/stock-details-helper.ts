import type { GraphApi } from '@sage/xtrem-manufacturing-api';
import type {
    StockAllocationDetailPanelParameters,
    StockIssueDetailPanelParameters,
} from '@sage/xtrem-stock-data/build/lib/client-functions/interfaces/stock-detail';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import type * as ui from '@sage/xtrem-ui';
import type { StockReceiptDetailPanelParameters } from './interfaces/stock-details';

export function editStockDetails<Node extends ui.PartialNodeWithId<any>>(
    pageInstance: ui.Page<GraphApi>,
    line: Node,
    args:
        | {
              movementType: 'allocation';
              data: Omit<StockAllocationDetailPanelParameters, 'stockAllocationStockRecordsFound'>;
          }
        | {
              movementType: 'receipt';
              data: Omit<StockReceiptDetailPanelParameters, 'jsonStockDetails' | 'lotCreateData'> & {
                  jsonStockDetails: string;
                  lotCreateData?: string;
              };
          }
        | {
              movementType: 'issue';
              data: Omit<StockIssueDetailPanelParameters, 'jsonStockDetails' | 'stockIssusStockRecordsFound'> & {
                  jsonStockDetails?: string;
              };
          },
) {
    return StockDetailHelper.editStockDetails(
        pageInstance,
        line,
        args as Parameters<typeof StockDetailHelper.editStockDetails>[2],
    );
}
