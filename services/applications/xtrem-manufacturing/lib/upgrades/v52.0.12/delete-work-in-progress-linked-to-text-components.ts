import { CustomSqlAction } from '@sage/xtrem-system';

export const workInProgressWorkOrderComponent = new CustomSqlAction({
    description: 'Work in progress work order components - delete work in progress linked to text components',
    fixes: { tables: ['work_in_progress_work_order_component'] },
    body: async helper => {
        await helper.executeSql(`
        DO $$
        DECLARE
            sRecord RECORD;
        BEGIN
            FOR sRecord IN (
              	select wip."_id",woc."_tenant_id" ,  woc._id as component from ${helper.schemaName}.work_in_progress_work_order_component wip
				inner join ${helper.schemaName}.work_order_component woc on woc."_id" = wip.work_order_component and wip."_tenant_id" = woc."_tenant_id"
				where woc.line_type = 'text'
            )
            LOOP
                delete from ${helper.schemaName}.work_in_progress_work_order_component
                where _id = sRecord._id and _tenant_id = sRecord._tenant_id and work_order_component = sRecord.component;
            END LOOP;
        END $$;
        `);
    },
});
