import { ReferenceDataType } from '@sage/xtrem-core';
import { WorkOrderReleasedItem } from '../nodes/work-order-released-item';

// q delete any once  https://jira.sage.com/browse/XT-75281
export const workOrderReleasedItem = new ReferenceDataType({
    reference: () => WorkOrderReleasedItem,
    lookup: {
        valuePath: 'item.name',
        helperTextPath: 'item.id',
        columnPaths: ['item.name', 'item.id', 'item.description'],
        tunnelPage: '@sage/xtrem-master-data/Item',
        tunnelPageIdPath: 'item._id',
    },
});
