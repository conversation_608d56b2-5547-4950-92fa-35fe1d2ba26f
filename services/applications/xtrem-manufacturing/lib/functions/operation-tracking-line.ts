import type { Context, NodeCreateData } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import * as xtremManufacturing from '../index';

async function createSetupCost(
    context: Context,
    costPayload: NodeCreateData<xtremManufacturing.nodes.WorkInProgressCost>,
    actualResource: xtremMasterData.nodes.DetailedResource,
) {
    if (!costPayload.quantity || !costPayload.unit) return 0;
    const setupTimeCost = await xtremTechnicalData.functions.operationFunctions.operationSetupCost(
        costPayload.quantity,
        actualResource,
        costPayload.unit as xtremMasterData.nodes.UnitOfMeasure,
    );
    const wip = await context.create(xtremManufacturing.nodes.WorkInProgressCost, {
        ...costPayload,
        type: 'setupTimeTracking',
        amount: setupTimeCost,
        cost: costPayload.quantity === 0 ? 0 : setupTimeCost / costPayload.quantity,
    });
    await wip.$.save();

    return setupTimeCost;
}

async function createOperationCost(
    context: Context,
    costPayload: NodeCreateData<xtremManufacturing.nodes.WorkInProgressCost>,
    actualResource: xtremMasterData.nodes.DetailedResource,
) {
    if (!costPayload.quantity || !costPayload.unit) return 0;
    const runTimeCost = await xtremTechnicalData.functions.operationFunctions.operationRunCost(
        costPayload.quantity,
        actualResource,
        costPayload.unit as xtremMasterData.nodes.UnitOfMeasure,
    );
    const wip = await context.create(xtremManufacturing.nodes.WorkInProgressCost, {
        ...costPayload,
        type: 'runTimeTracking',
        amount: runTimeCost,
        cost: costPayload.quantity === 0 ? 0 : runTimeCost / costPayload.quantity,
    });
    await wip.$.save();

    return runTimeCost;
}

export async function onLineAdded(trackingLine: xtremManufacturing.nodes.OperationTrackingLine) {
    const operation = await trackingLine.workOrderOperation;
    const document = await trackingLine.document;
    const currency = await (await (await (await trackingLine.actualResource).site).businessEntity).currency;
    const actualSetupTime = await trackingLine.actualSetupTime;
    const actualRunTime = await trackingLine.actualRunTime;
    const actualResource = await trackingLine.actualResource;
    const setupTimeUnit = await trackingLine.setupTimeUnit;
    const runTimeUnit = await trackingLine.runTimeUnit;

    const costBase: NodeCreateData<xtremManufacturing.nodes.WorkInProgressCost> = {
        originatingLine: trackingLine,
        workOrder: await operation.workOrder,
        effectiveDate: await document.entryDate,
        currency,
        status: 'pending',
    };

    const setupTimeCost = await createSetupCost(
        trackingLine.$.context,
        { ...costBase, quantity: actualSetupTime, unit: setupTimeUnit },
        actualResource,
    );

    const runTimeCost = await createOperationCost(
        trackingLine.$.context,
        { ...costBase, quantity: actualRunTime, unit: runTimeUnit },
        actualResource,
    );

    const setupTime =
        setupTimeUnit === (await operation.setupTimeUnit)
            ? actualSetupTime
            : (await xtremMasterData.functions.getConvertCoefficient(setupTimeUnit, await operation.setupTimeUnit)) *
              actualSetupTime;
    const runTime =
        runTimeUnit === (await operation.runTimeUnit)
            ? actualRunTime
            : (await xtremMasterData.functions.getConvertCoefficient(runTimeUnit, await operation.runTimeUnit)) *
              actualRunTime;

    await xtremManufacturing.nodes.WorkOrderOperation.updateOperation(trackingLine.$.context, {
        workOrder: await operation.workOrder,
        operation,
        quantity: await trackingLine.completedQuantity,
        setupTime,
        runTime,
        setupTimeCost,
        runTimeCost,
        actualResource,
        completed: await trackingLine.completed,
    });
}
