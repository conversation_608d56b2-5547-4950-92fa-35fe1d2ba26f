import type { AsyncArrayReader, Collection, Context } from '@sage/xtrem-core';
import * as xtremManufacturing from '../../index';

class MaterialTrackingLib {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private constructor() {}

    static readonly instance = new MaterialTrackingLib();

    // eslint-disable-next-line class-methods-use-this
    async updateAfterStockSuccess(
        writableContext: Context,
        params: {
            tracking: xtremManufacturing.nodes.MaterialTracking;
            lines:
                | Collection<xtremManufacturing.nodes.MaterialTrackingLine>
                | AsyncArrayReader<xtremManufacturing.nodes.MaterialTrackingLine>;
            isStockDocumentCompleted: boolean;
        },
    ) {
        await params.lines.forEach(line =>
            xtremManufacturing.nodes.MaterialTrackingLine.afterLineSaving(writableContext, line),
        );

        // Update the bill of material tracking
        const workOrder = await writableContext.read(xtremManufacturing.nodes.WorkOrder, {
            _id: (await params.tracking.workOrder)._id,
        });

        await xtremManufacturing.functions.updateBomTrackingStatuses(writableContext, {
            number: await workOrder.number,
            stockStatus: await params.tracking.stockTransactionStatus,
            workOrderStatus: await workOrder.status,
        });

        if (params.isStockDocumentCompleted) {
            await xtremManufacturing.nodes.MaterialTracking.onceStockCompleted(writableContext, params.tracking);
        }
    }
}

export const materialTrackingLib = MaterialTrackingLib.instance;
