import type { Collection, Context, NodeCreateData, decimal } from '@sage/xtrem-core';
import { BusinessRuleError, Logger, LogicError, ValidationSeverity, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type { integer } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import * as xtremManufacturing from '../index';

const logger = Logger.getLogger(__filename, 'work-order');

export async function validateWorkOrderParameters(
    context: Context,
    parameters: {
        workOrderNumber: string;
        siteId: string;
        releasedItem: string;
        releasedQuantity: decimal;
        hasBillOfMaterial: boolean;
        hasRouting: boolean;
        startDate?: date;
        bom?: string;
        route?: string;
    },
): Promise<void> {
    const messages = [];
    const numberAlreadyInUse = await context.exists(xtremManufacturing.nodes.WorkOrder, {
        number: parameters.workOrderNumber,
    });
    if (numberAlreadyInUse) {
        messages.push(
            context.localize(
                '@sage/xtrem-manufacturing/functions__create_work_order__number_already_in_use',
                'The work order number is already in use. Enter a unique work order reference.',
            ),
        );
    }
    if (!parameters.siteId?.trim()) {
        messages.push(
            context.localize(
                '@sage/xtrem-manufacturing/functions__create_work_order__site_id_missing',
                'Site ID missing',
            ),
        );
    }
    if (!parameters.releasedItem?.trim()) {
        messages.push(
            context.localize(
                '@sage/xtrem-manufacturing/functions__create_work_order__released_item_id_missing',
                'ReleasedItem ID missing',
            ),
        );
    }
    if (parameters.startDate && parameters.startDate < date.today()) {
        context.diagnoses.push({
            message: context.localize(
                '@sage/xtrem-manufacturing/functions__create_work_order__date_before_today',
                `The date '{{startDate}}' is before the current date.`,
                { startDate: parameters.startDate },
            ),
            severity: ValidationSeverity.warn,
            path: [],
        });
    }
    if (parameters.releasedQuantity <= 0) {
        messages.push(
            context.localize(
                '@sage/xtrem-manufacturing/functions__create_work_order__release_quantity_invalid',
                `Release quantity '{{quantity}}' is invalid`,
                { quantity: parameters.releasedQuantity },
            ),
        );
    }
    if ((!parameters.bom || parameters.bom === '') && (!parameters.route || parameters.route === '')) {
        messages.push(
            context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__routing_or_bom_required',
                'Assign a routing or bill of material to this work order.',
            ),
        );
    }
    // Need to check the bom and routing against the work order category
    if (parameters.hasBillOfMaterial && (!parameters.bom || parameters.bom === '')) {
        messages.push(
            context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__bom_required',
                'Bill of material is missing.',
            ),
        );
    }
    if (parameters.hasRouting && (!parameters.route || parameters.route === '')) {
        messages.push(
            context.localize('@sage/xtrem-manufacturing/nodes__work_order__routing_required', 'Routing is missing.'),
        );
    }
    if (messages.length > 0) {
        throw new BusinessRuleError(messages.join(', '));
    }
}

export async function convertRoutingOperationToWorkOrderOperation(
    context: Context,
    operations: Collection<xtremManufacturing.nodes.WorkOrderOperation>,
    componentOperationId: number,
): Promise<xtremManufacturing.nodes.WorkOrderOperation | null> {
    let operation = null;
    const componentOperation = await context.read(xtremTechnicalData.nodes.Operation, { _id: componentOperationId });
    const workOrderOperations = await operations
        .filter(async workOrderOperation => {
            return (await componentOperation.operationNumber) === (await workOrderOperation.operationNumber);
        })
        .toArray();
    if (workOrderOperations.length > 0) {
        [operation] = workOrderOperations;
    }
    return operation;
}

/**
 * Generator function to get the component number if not component number from BOM
 */
function* getComponentNumber(): Generator<integer> {
    const bomNumberIncrement = 10;

    let nextComponentNumber = bomNumberIncrement;
    while (true) {
        yield nextComponentNumber;
        nextComponentNumber += bomNumberIncrement;
    }
}

type AddComponentsCallbackParameters = {
    workOrder: xtremManufacturing.nodes.WorkOrder;
    billOfMaterial: xtremTechnicalData.nodes.BillOfMaterial;
    bomRevision: xtremTechnicalData.nodes.BillOfMaterialRevision | null;
    requiredQuantity: decimal;
    level: integer;
    operation?: xtremTechnicalData.nodes.Operation | null;
    levelQuantityRatio?: decimal;
    componentNumberGenerator?: ReturnType<typeof getComponentNumber>;
};

type AddComponentsCallback = (context: Context, args: AddComponentsCallbackParameters) => Promise<void>;

async function addPhantomComponents(
    context: Context,
    args: AddComponentsCallbackParameters & {
        component: xtremTechnicalData.nodes.Component;
        componentItem: xtremMasterData.nodes.Item;
        addComponentsCallBack: AddComponentsCallback;
    },
) {
    await logger.debugAsync(
        async () =>
            `Adding phantom components for item ${await args.componentItem.id} with level ${args.level} and required quantity ${args.requiredQuantity}`,
    );
    // Only 1 level of phantom is supported
    if (args.level === 0) {
        const bomItem =
            (await args.component.bomItem) ??
            (await context.tryRead(xtremTechnicalData.nodes.BillOfMaterial, {
                item: args.componentItem,
                site: await args.billOfMaterial.site,
            }));

        if (!bomItem) {
            logger.warn(
                context.localize(
                    '@sage/xtrem-manufacturing/functions__create_work_order__phantom_bom_missing',
                    'You need to create a BOM for the {{item}} phantom item.',
                    { item: await args.componentItem.id },
                ),
            );
            return;
        }

        const bomRevision = await xtremTechnicalData.functions.bomRevisionLib.getRevisionByDate(
            bomItem,
            await args.workOrder.startDate,
        );
        await logger.debugAsync(
            async () =>
                `Adding phantom components for item ${await args.componentItem.id} & bomRevision ${(await bomRevision?.revision) ?? 'null'} `,
        );

        await args.addComponentsCallBack(context, {
            workOrder: args.workOrder,
            billOfMaterial: bomItem,
            bomRevision,
            requiredQuantity: args.requiredQuantity,
            level: args.level + 1,
            operation: await args.component.operation,
            levelQuantityRatio: (await bomItem.baseQuantity)
                ? (await args.component.linkQuantity) / (await bomItem.baseQuantity)
                : 0,
            componentNumberGenerator: args.componentNumberGenerator,
        });
    }
}

/**
 * Returns the corresponding work order operation for the component operation
 * @param context
 * @param args.componentOperation The component operation to find in the work order operations
 * @param args.workOrderOperations The work order operations to search in
 * @returns a WorkOrderOperation or null if the component operation is not found
 */
function getComponentOperation(
    context: Context,
    args: {
        componentOperation: Awaited<xtremTechnicalData.nodes.Component['operation']>;
        workOrderOperations: xtremManufacturing.nodes.WorkOrder['productionOperations'];
    },
): Promise<xtremManufacturing.nodes.WorkOrderOperation | null> | null {
    if (!args.componentOperation) return null;

    return convertRoutingOperationToWorkOrderOperation(context, args.workOrderOperations, args.componentOperation._id);
}

const addComponent = async (
    context: Context,
    args: AddComponentsCallbackParameters & {
        component: xtremTechnicalData.nodes.Component;
        addComponentsCallBack: AddComponentsCallback;
    },
) => {
    const lineType = await args.component.lineType;
    const componentItem = await args.component.item;

    let woComponentData: NodeCreateData<xtremManufacturing.nodes.WorkOrderComponent> = {
        lineType,
        lineStatus: lineType === 'text' ? 'completed' : undefined,
        name: await args.component.name,
        instruction: await args.component.instruction,
    };

    if (lineType === 'normal') {
        if (!componentItem) {
            // A normal component should always have an item
            throw new LogicError('Item is missing in a normal component.');
        }

        const requiredQuantity = xtremTechnicalData.functions.componentFunctions.setRequiredQuantity(
            args.requiredQuantity,
            await args.billOfMaterial.baseQuantity,
            await args.component.linkQuantity,
            await args.component.isFixedLinkQuantity,
            await args.component.scrapFactor,
            await (
                await args.component.unit
            )?.decimalDigits,
        );

        await logger.debugAsync(
            async () =>
                `Adding ${(await componentItem.isPhantom) ? 'phantom ' : ''}component ${await componentItem.id} with level ${args.level} and required quantity ${requiredQuantity}`,
        );

        if (await componentItem?.isPhantom) {
            await addPhantomComponents(context, {
                ...args,
                requiredQuantity,
                componentItem,
                componentNumberGenerator: args.componentNumberGenerator,
            });
            return;
        }

        woComponentData = {
            ...woComponentData,
            item: componentItem,
            unit: await args.component.unit,
            isFixedLinkQuantity: await args.component.isFixedLinkQuantity,
            // The link quantity is calculated for the level 0
            linkQuantity: (await args.component.linkQuantity) * (args.levelQuantityRatio ?? 1),
            scrapFactor: await args.component.scrapFactor,
            operation: await getComponentOperation(context, {
                // if the operation is given in parameter, use it, otherwise get it from the component
                // A phantom component gives its operation to its children
                componentOperation: args.operation ?? (await args.component.operation),
                workOrderOperations: args.workOrder.productionOperations,
            }),
            requiredQuantity,
            requiredDate: await args.workOrder.startDate,
        };
    }

    await args.workOrder.productionComponents.append({
        ...woComponentData,
        componentNumber: args.componentNumberGenerator?.next().value ?? (await args.component.componentNumber),
    });
};

const addComponents: AddComponentsCallback = async (context: Context, args: AddComponentsCallbackParameters) => {
    // if components have been inserted, they are not sorted by component number. That's why it must be done here
    await (
        await xtremTechnicalData.functions.componentFunctions.getBomRevisionComponents(
            args.billOfMaterial,
            args.bomRevision,
            { sorted: true },
        )
    ).forEach(async component => {
        await addComponent(context, { ...args, component, addComponentsCallBack: addComponents });
    });
};

export async function addComponentsToWorkOrder(
    context: Context,
    workOrder: xtremManufacturing.nodes.WorkOrder,
    billOfMaterial: xtremTechnicalData.nodes.BillOfMaterial,
    bomRevision: xtremTechnicalData.nodes.BillOfMaterialRevision | null,
): Promise<void> {
    if ((await workOrder.productionItems.length) === 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/functions__create_work_order__released_item_missing',
                'Cannot create work order operations with no work order production items',
            ),
        );
    }

    const hasPhantom = await billOfMaterial.components.some(async component => !!(await component.item)?.isPhantom);

    await addComponents(context, {
        workOrder,
        billOfMaterial,
        bomRevision,
        requiredQuantity: await (await workOrder.productionItems.elementAt(0)).releasedQuantity,
        level: 0,
        componentNumberGenerator: hasPhantom ? getComponentNumber() : undefined,
    });
}

export async function addOperationsToWorkOrder(
    context: Context,
    workOrder: xtremManufacturing.nodes.WorkOrder,
    routing: xtremTechnicalData.nodes.Routing,
): Promise<decimal> {
    if ((await workOrder.productionItems.length) === 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/functions__create_work_order__released_item_missing',
                'Cannot create work order operations with no work order production items',
            ),
        );
    }
    let operationsHours = 0;
    await routing.operations.forEach(async (operation, i) => {
        const timeFactor =
            (await (await workOrder.productionItems.elementAt(0)).releasedQuantity) /
            ((await routing.batchQuantity) || 1);
        const runTime = (await operation.runTime) * timeFactor;
        const operationHours =
            (await xtremMasterData.functions.convertToHours(await operation.runTimeUnit, runTime)) +
            (await xtremMasterData.functions.convertToHours(await operation.setupTimeUnit, await operation.setupTime));
        operationsHours += operationHours;
        await workOrder.productionOperations.append({
            operationNumber: await operation.operationNumber,
            name: await operation.name,
            plannedQuantity: await (await workOrder.productionItems.elementAt(0)).releasedQuantity,
            startDate: null,
            endDate: null,
            isProductionStep: await operation.isProductionStep,
            minCapabilityLevel: await operation.minCapabilityLevel,
            setupTimeUnit: await operation.setupTimeUnit,
            runTimeUnit: await operation.runTimeUnit,
        });
        await operation.resourceGroups.forEach(async (resource, z) => {
            await (
                await workOrder.productionOperations.elementAt(i)
            ).resources.append({
                resource: await resource.resource,
                efficiency: await resource.efficiency,
                expectedSetupTime: await resource.setupTime,
                expectedRunTime: (await resource.runTime) * timeFactor,
                isResourceQuantity: await resource.isResourceQuantity,
                expectedRunCost: await xtremTechnicalData.functions.operationFunctions.operationRunCost(
                    (await resource.runTime) * timeFactor,
                    await resource.resource,
                    await operation.runTimeUnit,
                ),
                expectedSetupCost: await xtremTechnicalData.functions.operationFunctions.operationSetupCost(
                    await resource.setupTime,
                    await resource.resource,
                    await operation.setupTimeUnit,
                ),
            });
            await resource.resources.forEach(async resourceDetail =>
                (await (await workOrder.productionOperations.elementAt(i)).resources.elementAt(z)).resources.append({
                    resource: await resourceDetail.resource,
                }),
            );
        });
    });
    return operationsHours;
}

async function setWorkOrderDates(workOrder: xtremManufacturing.nodes.WorkOrder, hours: decimal) {
    let days = 0;
    if ((await workOrder.productionOperations.length) > 0) {
        if (hours >= 24) {
            days = Math.floor(hours / 24);
        }
    } else {
        days = (await (await (await workOrder.productionItem)?.itemSite)?.prodLeadTime) || 0;
    }
    const dat1 = await workOrder.requestedDate;
    const dat2 = dat1.addDays(days);
    await workOrder.$.set({ startDate: dat1, endDate: dat2 });
}

export async function recalculateWorkOrderDates(workOrder: xtremManufacturing.nodes.WorkOrder): Promise<void> {
    const operationsHours = await workOrder.productionOperations
        .filter(async ope => !['excluded', 'included'].includes(await ope.status))
        .sum(
            async (operation: xtremManufacturing.nodes.WorkOrderOperation) =>
                (await xtremMasterData.functions.convertToHours(
                    await operation.runTimeUnit,
                    await operation.expectedRunTime,
                )) +
                (await xtremMasterData.functions.convertToHours(
                    await operation.setupTimeUnit,
                    await operation.expectedSetupTime,
                )),
        );

    await setWorkOrderDates(workOrder, operationsHours);
}

export async function createWorkOrder(
    context: Context,
    data: {
        siteId: string;
        releasedItem: string;
        releasedQuantity: decimal;
        name: string;
        type: xtremManufacturing.enums.WorkOrderType;
        workOrderCategory: xtremManufacturing.nodes.WorkOrderCategory;
        workOrderNumber: string;
        startDate?: date;
        bom?: string;
        route?: string;
        storedDimensions?: object;
        storedAttributes?: xtremMasterData.interfaces.StoredAttributes;
        _customData?: object;
    },
): Promise<xtremManufacturing.nodes.WorkOrder> {
    await xtremManufacturing.functions.validateWorkOrderParameters(context, {
        ...data,
        hasBillOfMaterial: !!(await data.workOrderCategory.billOfMaterial),
        hasRouting: !!(await data.workOrderCategory.routing),
        bom: data.bom,
        route: data.route,
    });

    const site = await context.read(xtremSystem.nodes.Site, { id: data.siteId });
    const item = await context.read(xtremMasterData.nodes.Item, { id: data.releasedItem });
    const requestedDate = data.startDate || date.today();
    let bomItem = null;
    let routeItem = null;
    let routing = null;
    let billOfMaterial = null;
    let bomRevision = null;
    if (data.bom) {
        if (data.bom !== data.releasedItem) {
            bomItem = await context.read(xtremMasterData.nodes.Item, { id: data.bom });
        }
        billOfMaterial = await context.read(xtremTechnicalData.nodes.BillOfMaterial, {
            site: site._id,
            item: bomItem ? bomItem._id : item._id,
        });
        bomRevision = await xtremTechnicalData.functions.bomRevisionLib.getRevisionByDate(
            billOfMaterial,
            requestedDate,
        );
    }
    if (data.route) {
        if (data.route !== data.releasedItem) {
            routeItem = await context.read(xtremMasterData.nodes.Item, { id: data.route });
        }
        routing = await context.read(xtremTechnicalData.nodes.Routing, {
            site: site._id,
            item: routeItem ? routeItem._id : item._id,
        });
    }

    const workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
        number: data.workOrderNumber || '',
        name: data.name,
        requestedDate,
        category: data.workOrderCategory,
        type: data.type,
        site,
        routingCode: routing,
        bomCode: billOfMaterial,
        bomRevision,
        timeUnit: (await routing?.timeUnit) || undefined,
        productionItems: [
            {
                releasedItem: item,
                releasedQuantity: data.releasedQuantity,
                storedAttributes: data.storedAttributes,
                storedDimensions: data.storedDimensions,
            },
        ],
        _customData: data._customData || {},
    });

    const operationsHours = routing
        ? await xtremManufacturing.functions.addOperationsToWorkOrder(context, workOrder, routing)
        : 0;
    await setWorkOrderDates(workOrder, operationsHours);

    if (billOfMaterial) {
        await xtremManufacturing.functions.addComponentsToWorkOrder(context, workOrder, billOfMaterial, bomRevision);
    }

    const releasedItem = await (await workOrder.productionItem)?.releasedItem;

    await workOrder.$.set({
        plannedOverheadCost:
            ((await workOrder.plannedMaterialCost) + (await workOrder.plannedProcessCost)) *
            (await xtremManufacturing.functions.workOrderLib.getIndirectCost(
                context,
                (await workOrder.site)._id,
                releasedItem?._id,
            )),
    });

    await workOrder.$.save();

    return workOrder;
}

export async function trackBillOfMaterial(
    readOnlyContext: Context,
    data: {
        bomId: string;
        siteId: string;
        itemId: string;
        itemName: string;
        quantity: decimal;
        workOrderCategoryId: string;
        stockDetails: string;
        date?: date;
        route?: string;
    },
): Promise<xtremTechnicalData.nodes.BillOfMaterialTracking | null> {
    let bomTracking: xtremTechnicalData.nodes.BillOfMaterialTracking | null = null;
    let workOrder: xtremManufacturing.nodes.WorkOrder | null = null;
    let workOrderTracking: xtremManufacturing.interfaces.WorkOrderTrackingReturnType | null = null;
    let message = '';
    const bom = await readOnlyContext.read(xtremTechnicalData.nodes.BillOfMaterial, { _id: data.bomId });
    let billOfMaterialTrackingId = 0;

    if (
        (await (await bom.item).isPhantom) ||
        (await bom.components.some(async component => !!(await (await component.item)?.isPhantom)))
    ) {
        throw new BusinessRuleError(
            readOnlyContext.localize(
                '@sage/xtrem-manufacturing/functions__create_work_order__cannot_track_phantom_bill_of_material',
                'You can only track a work order for an item that does not have a phantom component.',
            ),
        );
    }

    await readOnlyContext.runInWritableContext(async (writableContext: Context) => {
        bomTracking = await writableContext.create(xtremTechnicalData.nodes.BillOfMaterialTracking, {
            billOfMaterial: bom,
            stockDetails: data.stockDetails,
            quantity: data.quantity,
            status: 'inProgress',
        });
        await bomTracking.$.save();
        billOfMaterialTrackingId = bomTracking._id;
    });

    const category = await readOnlyContext.read(xtremManufacturing.nodes.WorkOrderCategory, {
        _id: data.workOrderCategoryId,
    });

    let workOrderId = -1;
    await readOnlyContext.runInWritableContext(async (writableContext: Context) => {
        try {
            workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(writableContext, {
                workOrderNumber: '',
                releasedQuantity: data.quantity,
                siteId: data.siteId,
                releasedItem: data.itemId,
                workOrderCategory: category,
                type: 'firm',
                name: data.itemName,
                bom: data.itemId,
                route: data.route ? data.itemId : '',
                startDate: data.date,
            });
            workOrderId = workOrder._id;
            await xtremManufacturing.functions.forceUpdateForScheduling(workOrder);
            await workOrder.$.set({
                isSchedulingSkipped: true,
                schedulingStatus: 'notManaged',
            });
            await workOrder.$.save();
        } catch (error) {
            message = error.message;
        }
    });
    workOrder = await readOnlyContext.read(xtremManufacturing.nodes.WorkOrder, { _id: workOrderId });

    const workOrderNumber = await workOrder?.number;
    const status = message.length > 0 ? 'error' : 'inProgress';

    await readOnlyContext.runInWritableContext(async (writableContext: Context) => {
        const trackingToUpdate = await writableContext.read(
            xtremTechnicalData.nodes.BillOfMaterialTracking,
            { _id: billOfMaterialTrackingId },
            { forUpdate: true },
        );
        const workOrderStatus = (await workOrder?.status)?.toString();
        const stockStatus = (await workOrder?.stockTransactionStatus)?.toString();
        await trackingToUpdate.$.set({
            workOrderNumber,
            workOrderId: workOrder?._id,
            workOrderStatus,
            stockStatus,
            message,
            status,
        });
        await trackingToUpdate.$.save();
    });

    try {
        if (workOrder) {
            workOrderTracking = await xtremManufacturing.nodes.ProductionTracking.createWorkOrderTracking(
                readOnlyContext,
                {
                    itemCode: data.itemId,
                    workOrderNumber,
                    workOrderTrackingNumber: '',
                    trackingQuantity: data.quantity,
                    trackingDate: await workOrder.startDate,
                    materialTracking: true,
                    timeTracking: true,
                    stockDetails: data.stockDetails ? JSON.parse(data.stockDetails) : null,
                },
            );
            if (workOrderTracking.message.length) {
                message = workOrderTracking.message;
            }
        }
    } catch (error) {
        logger.error(
            () => `Error creating work order tracking: ${error.message} - ${error.stack} - ${JSON.stringify(error)}`,
        );
        message = error.message;
    }

    await readOnlyContext.runInWritableContext(async (writableContext: Context) => {
        if (workOrderTracking !== null) {
            const trackingToUpdate = await writableContext.read(
                xtremTechnicalData.nodes.BillOfMaterialTracking,
                { _id: billOfMaterialTrackingId },
                { forUpdate: true },
            );

            const mat = workOrderTracking.materialTracking;
            const tim = workOrderTracking.timeTracking;
            const prod = workOrderTracking.productionTracking;

            const materialTrackingId = Number(mat?._id);
            const stockStatus = await mat?.stockTransactionStatus;
            const productionTrackingId = Number(prod?._id);
            const timeTrackingId = Number(tim?._id);
            const workOrderStatus = await (await mat?.workOrder)?.status;

            await trackingToUpdate.$.set({
                message,
                materialTrackingId,
                materialTrackingNumber: await mat?.number,
                productionTrackingId,
                productionTrackingNumber: prod?.number as string,
                timeTrackingId,
                timeTrackingNumber: tim?.number as string,
                stockStatus,
                workOrderStatus,
            });
            await trackingToUpdate.$.save();
        } else {
            const trackingToUpdate = await writableContext.read(
                xtremTechnicalData.nodes.BillOfMaterialTracking,
                { _id: billOfMaterialTrackingId },
                { forUpdate: true },
            );

            await trackingToUpdate.$.set({ message, status: 'error' });
            await trackingToUpdate.$.save();
        }
    });

    return bomTracking;
}
