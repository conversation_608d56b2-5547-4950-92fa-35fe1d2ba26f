import type { Context, NodeQueryFilter, PropertyQueryFilter } from '@sage/xtrem-core';
import { BusinessRuleError, date } from '@sage/xtrem-core';
import * as xtremManufacturing from '../index';
import type { WorkOrder } from '../nodes/index';

type Tracking =
    | xtremManufacturing.nodes.MaterialTracking
    | xtremManufacturing.nodes.ProductionTracking
    | xtremManufacturing.nodes.OperationTracking;

export async function controlClosingDate(
    context: Context,
    workOrder: { closingDate: date | null; filter?: PropertyQueryFilter<Tracking, WorkOrder> },
) {
    const { closingDate, filter: workOrderFilter } = workOrder;

    const values = { effectiveDate: { max: true } };

    // If "closed" then a closing date is required.
    if (!closingDate) {
        return context.localize(
            '@sage/xtrem-manufacturing/nodes__work_order__closing_date_required_if_closed',
            'A closing date is required.',
        );
    }

    // Closing date must not before today.
    if (closingDate.compare(date.today()) > 0) {
        return context.localize(
            '@sage/xtrem-manufacturing/nodes__work_order__closing_date_greater_today',
            'The closing date must not be in the future.',
        );
    }
    if (!workOrderFilter) return ''; // Success (no workOrder to check)
    const filter: NodeQueryFilter<Tracking> = { workOrder: workOrderFilter };

    // Closing date cannot be less than any tracking effective date
    const maxMaterialTrackingEffectiveDate = (
        await context.queryAggregate(xtremManufacturing.nodes.MaterialTracking, { filter, group: {}, values }).at(0)
    )?.values.effectiveDate.max;
    if (maxMaterialTrackingEffectiveDate && closingDate.compare(maxMaterialTrackingEffectiveDate) < 0) {
        return context.localize(
            '@sage/xtrem-manufacturing/nodes__work_order__closing_date_less_than_material_tracking',
            'The closing date must not be before any material tracking effective date. ({{date}})',
            { date: maxMaterialTrackingEffectiveDate },
        );
    }

    const maxWorkOrderTrackingEffectiveDate = (
        await context.queryAggregate(xtremManufacturing.nodes.ProductionTracking, { filter, group: {}, values }).at(0)
    )?.values.effectiveDate.max;
    if (maxWorkOrderTrackingEffectiveDate && closingDate.compare(maxWorkOrderTrackingEffectiveDate) < 0) {
        return context.localize(
            '@sage/xtrem-manufacturing/nodes__work_order__closing_date_less_than_production_tracking',
            'The closing date must not be before any production tracking effective date. ({{date}})',
            { date: maxWorkOrderTrackingEffectiveDate },
        );
    }

    const maxOperationTrackingEffectiveDate = (
        await context.queryAggregate(xtremManufacturing.nodes.OperationTracking, { filter, group: {}, values }).at(0)
    )?.values.effectiveDate.max;
    if (maxOperationTrackingEffectiveDate && closingDate.compare(maxOperationTrackingEffectiveDate) < 0) {
        return context.localize(
            '@sage/xtrem-manufacturing/nodes__work_order__closing_date_less_than_operation_tracking',
            'The closing date must not be before any operation tracking effective date. ({{date}})',
            { date: maxOperationTrackingEffectiveDate },
        );
    }

    return ''; // Success
}

/** Throw errors if we can't close the workOrder  */
export async function beforeCloseWorkOrder(context: Context, workOrder: WorkOrder, closingDate: date | null) {
    if ((await workOrder.status) === 'closed') {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__work_order_already_closed',
                'The work order {{workOrderNumber}} is already closed.',
                { workOrderNumber: await workOrder.number },
            ),
        );
    }

    if (
        (await (await workOrder.bomCode)?.status) === 'inDevelopment' ||
        (await (await workOrder.routingCode)?.status) === 'inDevelopment'
    ) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__cannot_close_with_incorrect_bom_or_routing',
                'You cannot close a work order for a routing or BOM that is in development.',
                { workOrderNumber: await workOrder.number },
            ),
        );
    }

    if ((await workOrder.status) === 'pending') {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__cannot_close_pending_work_order',
                'Try to delete the pending work order {{workOrderNumber}} instead. You cannot close it.',
                { workOrderNumber: await workOrder.number },
            ),
        );
    }

    if (['allocated', 'partiallyAllocated'].includes(await workOrder.allocationStatus)) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__cannot_close_allocated_work_order',
                'Remove the stock allocation before closing the work order {{workOrderNumber}}.',
                { workOrderNumber: await workOrder.number },
            ),
        );
    }

    if (
        await workOrder.productionComponents.some(
            async component => (await component.allocationRequestStatus) === 'inProgress',
        )
    ) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__cannot_close_allocated_request_in_progress',
                'You can only close the work order after the allocation request is complete for the component.',
            ),
        );
    }

    if (
        await workOrder.materialTrackings.some(
            async materialTracking => (await materialTracking.stockTransactionStatus) !== 'completed',
        )
    ) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__cannot_close_with_pending_material_tracking',
                'You can only close the work order after all material trackings are posted.',
            ),
        );
    }

    if (
        await workOrder.productionTrackings.some(
            async productionTracking => (await productionTracking.stockTransactionStatus) !== 'completed',
        )
    ) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__cannot_close_with_pending_production_tracking',
                'You can only close the work order after all production trackings are posted.',
            ),
        );
    }

    const closingDateMessage = await controlClosingDate(context, {
        filter: { _id: workOrder._id },
        closingDate,
    });

    if (closingDateMessage) {
        throw new BusinessRuleError(closingDateMessage);
    }
}
