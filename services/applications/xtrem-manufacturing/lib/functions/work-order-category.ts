import type { Context } from '@sage/xtrem-core';
import * as xtremManufacturing from '../index';

export async function disableOthersDefault(
    context: Context,
    workOrderCategory: xtremManufacturing.nodes.WorkOrderCategory,
) {
    await context.bulkUpdate(xtremManufacturing.nodes.WorkOrderCategory, {
        set: { isDefault: false },
        where: {
            isDefault: true,
            _id: { _ne: workOrderCategory._id },
        },
    });
}

export function getDefaultCategory(context: Context) {
    return context
        .query(xtremManufacturing.nodes.WorkOrderCategory, {
            filter: {
                isDefault: true,
            },
        })
        .elementAt(0);
}
