import type { Context, Dict, decimal } from '@sage/xtrem-core';
import { BusinessRuleError, asyncArray, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremManufacturing from '../index';

// TODO: validate stock details?
export function validateWorkOrderTrackingParameters(
    context: Context,
    itemCode: string,
    quantity: decimal,
    workOrderNumber: string,
): void {
    const messages = [];
    if (itemCode === '') {
        messages.push(
            context.localize(
                '@sage/xtrem-manufacturing/functions__production_tracking__released_item_missing',
                'Released item ID missing.',
            ),
        );
    }
    if (workOrderNumber === '') {
        messages.push(
            context.localize(
                '@sage/xtrem-manufacturing/functions__tracking__work_order_number_missing',
                'Work order number missing.',
            ),
        );
    }
    if (quantity <= 0) {
        messages.push(
            context.localize(
                '@sage/xtrem-manufacturing/functions__tracking__release_quantity_invalid',
                "Release quantity '{{quantity}}' is invalid.",
                { quantity },
            ),
        );
    }
    if (messages.length > 0) {
        throw new BusinessRuleError(messages.join(' '));
    }
}

export async function createTestProductionTracking(context: Context, orderNumberRoot = '', quantityRatio: decimal = 1) {
    let numberTrackings = 0;

    const defaultLocations: Dict<string> = {};
    const workOrders = await context.select(
        xtremManufacturing.nodes.WorkOrderReleasedItem,
        {
            document: {
                number: true,
                site: {
                    id: true,
                    isLocationManaged: true,
                    defaultLocation: { id: true, locationZone: { id: true, site: { id: true } } },
                },
            },
            item: { id: true, stockUnit: { decimalDigits: true } },
            quantityInStockUnit: true,
            releasedItem: { stockUnit: true },
        },
        {
            filter: {
                document: { type: 'firm', number: { _gte: `${orderNumberRoot}`, _lte: `${orderNumberRoot}ZZZZZZZZZ` } },
            },
        },
    );

    await context.batch.updateProgress({
        phase: 'start',
        totalCount: workOrders.length,
        errorCount: 0,
        detail: 'started',
        successCount: 0,
    });

    await asyncArray(workOrders).forEach(async (workOrder, index) => {
        if (!defaultLocations[workOrder.document.site.id]) {
            if (workOrder.document.site.defaultLocation)
                defaultLocations[workOrder.document.site.id] =
                    `${workOrder.document.site.defaultLocation.id}|${workOrder.document.site.defaultLocation.locationZone.site.id}|${workOrder.document.site.defaultLocation.locationZone.id}`;
            else {
                const location = (
                    await context.select(
                        xtremMasterData.nodes.Location,
                        { id: true, locationZone: { id: true, site: { id: true } } },
                        { filter: { site: workOrder.document.site.id, isActive: true }, first: 1 },
                    )
                )[0];
                defaultLocations[workOrder.document.site.id] =
                    `${location.id}|${location.locationZone.site.id}|${location.locationZone.id}`;
            }
        }
        let message = `Work order ${workOrder.document.number}`;
        const quantityToProduce =
            Math.round(
                (workOrder.quantityInStockUnit ?? 0) * quantityRatio * 10 ** workOrder.item.stockUnit.decimalDigits,
            ) /
                10 ** workOrder.item.stockUnit.decimalDigits || 1;

        const result = await xtremManufacturing.nodes.ProductionTracking.createWorkOrderTracking(context, {
            itemCode: workOrder.item.id ?? '',
            workOrderNumber: workOrder.document.number,
            workOrderTrackingNumber: `WOT-${workOrder.document.site.id}-${String(index).padStart(4, '0')}`,
            trackingQuantity: quantityToProduce,
            trackingDate: date.today(),
            materialTracking: false,
            timeTracking: false,
            stockDetails: [
                {
                    site: `#${workOrder.document.site.id}`,
                    item: `#${workOrder.item.id}`,
                    stockUnit: workOrder.releasedItem.stockUnit,
                    quantityInStockUnit: quantityToProduce,
                    status: '#A',
                    location: defaultLocations[workOrder.document.site.id],
                },
            ],
        });
        if (result.productionTracking?.number) {
            message += `${result.productionTracking?.number} created!\n`;
        }
        if (result.message.length) {
            message += `${result.message}\n`;
        }
        numberTrackings += 1;
        await context.batch.updateProgress({ phase: 'processing', successCount: index });
        await context.batch.logMessage('info', message);
    });

    await context.batch.updateProgress({
        phase: 'complete',
        totalCount: numberTrackings,
        errorCount: 0,
        detail: 'finished',
        successCount: numberTrackings,
    });
    return `Number of trackings created - ${numberTrackings}`;
}
