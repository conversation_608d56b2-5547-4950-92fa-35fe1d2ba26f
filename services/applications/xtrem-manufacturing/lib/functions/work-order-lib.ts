import type { AsyncArray, Context, decimal, integer } from '@sage/xtrem-core';
import { Logger, LogicError, asyncArray, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import * as xtremManufacturing from '../../index';

const logger = Logger.getLogger(__filename, 'work-order');

// It resets WO status value only when it is 'pending' or 'in progress'.
// Its new value is
// - 'completed', if all lines of its (released items + components + operations) are 'completed'
// - 'in progress', if at least one line of its (released items + components + operations) is 'in progress'
// - 'pending' otherwise

class WorkerOrderLib {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private constructor() {}

    static readonly instance: WorkerOrderLib = new WorkerOrderLib();

    // eslint-disable-next-line class-methods-use-this
    async getWorkOrderStatus(
        workOrder: xtremManufacturing.nodes.WorkOrder,
    ): Promise<xtremManufacturing.enums.WorkOrderStatus> {
        let releasedItemStatus = 'inProgress';
        let operationStatus = 'inProgress';
        let componentStatus = 'inProgress';

        const productionOperationsFiltered = workOrder.productionOperations.filter(
            async (workOrderProductionOperation: xtremManufacturing.nodes.WorkOrderOperation) =>
                !['excluded', 'included'].includes(await workOrderProductionOperation.status),
        );

        const productionComponentsFiltered = workOrder.productionComponents.filter(
            async (workOrderProductionComponent: xtremManufacturing.nodes.WorkOrderComponent) =>
                !['excluded', 'included'].includes(await workOrderProductionComponent.lineStatus),
        );

        if (['pending', 'inProgress'].includes(await workOrder.status)) {
            // all released items pending       => releasedItemsStatus = pending
            // all  released items completed    => releasedItemsStatus = completed
            // otherwise (*)                    => releasedItemsStatus = inProgress
            // (*  released items inProgress or a mix of  released items completed and pending)
            if (
                await workOrder.productionItems.every(
                    async (workOrderReleasedItem: xtremManufacturing.nodes.WorkOrderReleasedItem) =>
                        (await workOrderReleasedItem.lineStatus) === 'completed',
                )
            )
                releasedItemStatus = 'completed';
            else if (
                await workOrder.productionItems.every(
                    async (workOrderReleasedItem: xtremManufacturing.nodes.WorkOrderReleasedItem) =>
                        (await workOrderReleasedItem.lineStatus) === 'pending',
                )
            )
                releasedItemStatus = 'pending';

            // all components pending                   => componentStatus = pending
            // all components completed or no component => componentStatus = completed
            // otherwise (*)                            => componentStatus = inProgress
            // (* some components inProgress or a mix of components completed and pending)
            if (
                (await productionComponentsFiltered.length) === 0 ||
                (await productionComponentsFiltered.every(
                    async (workOrderProductionComponent: xtremManufacturing.nodes.WorkOrderComponent) =>
                        (await workOrderProductionComponent.lineStatus) === 'completed',
                ))
            )
                componentStatus = 'completed';
            else if (
                await productionComponentsFiltered.every(
                    async (workOrderProductionComponent: xtremManufacturing.nodes.WorkOrderComponent) =>
                        (await workOrderProductionComponent.lineStatus) === 'pending',
                )
            )
                componentStatus = 'pending';

            // all operations pending                   => operationStatus = pending
            // all operations completed or no component => operationStatus = completed
            // otherwise (*)                            => operationStatus = inProgress
            // (* some operations inProgress or a mix of operations completed and pending)
            if (
                (await productionOperationsFiltered.length) === 0 ||
                (await productionOperationsFiltered.every(
                    async (workOrderProductionOperation: xtremManufacturing.nodes.WorkOrderOperation) =>
                        (await workOrderProductionOperation.status) === 'completed',
                ))
            ) {
                operationStatus = 'completed';
            } else if (
                await productionOperationsFiltered.every(
                    async (workOrderProductionOperation: xtremManufacturing.nodes.WorkOrderOperation) =>
                        (await workOrderProductionOperation.status) === 'pending',
                )
            ) {
                operationStatus = 'pending';
            }
        } else {
            return workOrder.status;
        }

        if (releasedItemStatus === 'pending' && componentStatus === 'pending' && operationStatus === 'pending') {
            return 'pending';
        }

        if (releasedItemStatus === 'completed' && componentStatus === 'completed' && operationStatus === 'completed') {
            return 'completed';
        }

        return 'inProgress';
    }

    // eslint-disable-next-line class-methods-use-this
    costTypeOperations(status: xtremManufacturing.enums.OperationStatus): boolean {
        return status !== 'included' && status !== 'excluded';
    }

    // eslint-disable-next-line class-methods-use-this
    costTypeComponents(status: xtremManufacturing.enums.ComponentStatus): boolean {
        return status !== 'included' && status !== 'excluded';
    }

    // eslint-disable-next-line class-methods-use-this
    async getIndirectCost(context: Context, siteId: number, itemId?: number): Promise<decimal> {
        if (itemId) {
            const itemSite = await context.tryRead(xtremMasterData.nodes.ItemSite, {
                item: itemId,
                site: siteId,
            });
            let indirectCostPercent = 0;
            switch (await (await itemSite?.indirectCostSection)?.calculationMethod) {
                case 'cumulate':
                    {
                        const lines = (await itemSite?.indirectCostSection)?.lines;
                        indirectCostPercent = lines ? await lines.sum(line => line.percentage) : 0;
                    }
                    break;
                case 'compound':
                    {
                        const lines = (await itemSite?.indirectCostSection)?.lines;
                        indirectCostPercent = lines
                            ? (await lines.reduce(
                                  async (prev, line) => (prev * (100 + (await line.percentage))) / 100,
                                  100,
                              )) - 100
                            : 0;
                    }
                    break;
                default:
                    break;
            }
            return indirectCostPercent / 100;
        }
        return 0;
    }

    async updateActualOverhead(workOrder: xtremManufacturing.nodes.WorkOrder): Promise<decimal> {
        const releasedItem = await (await workOrder.productionItem)?.releasedItem;
        const newActualOverhead =
            ((await workOrder.actualMaterialCost) + (await workOrder.actualProcessCost)) *
            (await this.getIndirectCost(workOrder.$.context, (await workOrder.site)._id, releasedItem?._id));
        const overheadUpdate = newActualOverhead - (await workOrder.actualOverheadCost);
        await workOrder.$.set({ actualOverheadCost: newActualOverhead });
        return overheadUpdate;
    }

    // eslint-disable-next-line class-methods-use-this
    async getCurrentWipVariance(
        workOrder: xtremManufacturing.nodes.WorkOrder,
    ): Promise<{ absorbedWipAmount: decimal; nonAbsorbedWipAmount: decimal }> {
        // Query the WIP amounts absorbed in the stock value and the non absorbed amounts
        const sumWip = await workOrder.$.context
            .queryAggregate(xtremManufacturing.nodes.WorkInProgressCost, {
                group: { type: { _by: 'value' } },
                filter: {
                    workOrder: workOrder._id,
                    type: {
                        _in: xtremManufacturing.nodes.WorkInProgressCost.workInProgressVarianceTypes
                            .concat(xtremManufacturing.nodes.WorkInProgressCost.workInProgressNonAbsorbedVarianceTypes)
                            .concat('productionTracking'),
                    },
                },
                values: { amount: { sum: true } },
            })
            .toArray();

        // Now build the full total by multiplying with the correct sign.
        return sumWip.reduce(
            (sum, sumWipVariance) => {
                const signedVariance =
                    (sumWipVariance.values.amount.sum ?? 0) *
                    // The sum of amounts will be compared with StockJournal values which are positive
                    // => we need to reverse the sign of the variance to have the correct result
                    (xtremManufacturing.nodes.WorkInProgressCost.workInProgressNegativeTypes.includes(
                        sumWipVariance.group.type,
                    )
                        ? 1
                        : -1);
                const isNonAbsorbedAmount =
                    xtremManufacturing.nodes.WorkInProgressCost.workInProgressNonAbsorbedVarianceTypes.includes(
                        sumWipVariance.group.type,
                    );

                return {
                    absorbedWipAmount: sum.absorbedWipAmount + (isNonAbsorbedAmount ? 0 : signedVariance),
                    nonAbsorbedWipAmount: sum.nonAbsorbedWipAmount + (isNonAbsorbedAmount ? signedVariance : 0),
                };
            },
            { absorbedWipAmount: 0, nonAbsorbedWipAmount: 0 },
        );
    }

    // eslint-disable-next-line class-methods-use-this
    async getStockJournalValue(
        workOrder: xtremManufacturing.nodes.WorkOrder,
    ): Promise<{ orderAmountTotal: decimal; movementAmountTotal: decimal; nonAbsorbedTotal: decimal }> {
        logger.verbose(() => `calculateStockJournalValues workOrder=${workOrder._id}`);
        const trackingIds = (
            await workOrder.productionTrackings
                .map(tracking => tracking.lines.map(line => line._id).toArray())
                .toArray()
        ).flat();
        const costs = (
            await workOrder.$.context
                .queryAggregate(xtremStockData.nodes.StockJournal, {
                    filter: {
                        documentLine: {
                            _in: trackingIds,
                        },
                    },
                    group: {},
                    values: {
                        orderAmount: { sum: true },
                        movementAmount: { sum: true },
                        nonAbsorbedAmount: { sum: true },
                    },
                })
                .toArray()
        )[0]?.values;
        logger.verbose(() => `costs.movementAmount.sum = ${costs.orderAmount.sum}`);
        logger.verbose(() => `costs.nonAbsorbedAmount.sum = ${costs.nonAbsorbedAmount.sum}`);
        return {
            orderAmountTotal: costs.orderAmount.sum || 0,
            movementAmountTotal: costs.movementAmount.sum || 0,
            nonAbsorbedTotal: costs.nonAbsorbedAmount.sum || 0,
        };
    }

    /**
     * Update the WIP of overheads
     * This functions is expected to be called before the stock correction
     * @param context
     * @param workOrderToUpdate
     */
    async updateWipCost(context: Context, workOrderToUpdate: xtremManufacturing.nodes.WorkOrder) {
        // Calculate wip variance - Calculate new actual overhead, compare with planned
        const unit = await (await (await workOrderToUpdate.productionItem)?.releasedItem)?.stockUnit;
        const currency = await (await (await workOrderToUpdate.site).legalCompany).currency;

        const overheadUpdate = await this.updateActualOverhead(workOrderToUpdate);
        if (overheadUpdate !== 0) {
            const wip = await context.create(xtremManufacturing.nodes.WorkInProgressCost, {
                originatingLine: await workOrderToUpdate.productionItems.elementAt(0),
                workOrder: workOrderToUpdate,
                quantity: 1,
                cost: overheadUpdate,
                amount: overheadUpdate,
                status: 'pending',
                type: 'workOrderIndirectCost',
                unit,
                currency,
                effectiveDate: date.today(),
            });
            await wip.$.save();
        }
    }

    // eslint-disable-next-line class-methods-use-this
    async createWipVariance(
        writableContext: Context,
        args: {
            workOrderToUpdate: xtremManufacturing.nodes.WorkOrder;
            amount: decimal;
            type: xtremManufacturing.enums.WorkInProgressType;
            unit: xtremMasterData.nodes.UnitOfMeasure;
            currency: xtremMasterData.nodes.Currency;
        },
    ) {
        const wip = await writableContext.create(xtremManufacturing.nodes.WorkInProgressCost, {
            originatingLine: await args.workOrderToUpdate.productionItems.elementAt(0),
            workOrder: args.workOrderToUpdate,
            quantity: 1,
            cost: Math.abs(args.amount),
            amount: Math.abs(args.amount),
            status: 'pending',
            type: args.type,
            unit: args.unit,
            currency: args.currency,
        });
        await wip.$.save();
    }

    /**
     * Update the WIP variances
     * As it uses the result of the stock correction to update the WIP variances
     * it must be called after the stock service
     * @param writableContext
     * @param workOrderToUpdate
     */
    async updateVariances(writableContext: Context, workOrderToUpdate: xtremManufacturing.nodes.WorkOrder) {
        // Calculate wip variance - Calculate new actual overhead, compare with planned
        const unit = await (await (await workOrderToUpdate.productionItem)?.releasedItem)?.stockUnit;
        if (!unit) {
            // This should never happen as a released item should always exist and have a stock unit
            // when closing a work order
            throw new LogicError('Production item is not found or does not have a stock unit.');
        }
        const currency = await (await (await workOrderToUpdate.site).legalCompany).currency;

        const { movementAmountTotal, nonAbsorbedTotal } = await this.getStockJournalValue(workOrderToUpdate);

        // Create wip cost variance
        const { absorbedWipAmount, nonAbsorbedWipAmount } = await this.getCurrentWipVariance(workOrderToUpdate);

        const newWipVariance = movementAmountTotal - absorbedWipAmount;
        const newWipNonAbsorbedVariance = nonAbsorbedTotal - nonAbsorbedWipAmount;

        const variancesToCreate: { amount: decimal; type: xtremManufacturing.enums.WorkInProgressType }[] = [];

        if ((await (await (await workOrderToUpdate.productionItem)?.itemSite)?.valuationMethod) === 'standardCost') {
            variancesToCreate.push({
                amount: newWipNonAbsorbedVariance,
                type: newWipNonAbsorbedVariance >= 0 ? 'workOrderVariance' : 'workOrderNegativeVariance',
            });
        } else {
            variancesToCreate.push({
                amount: newWipNonAbsorbedVariance,
                type:
                    newWipNonAbsorbedVariance >= 0
                        ? 'workOrderActualCostAdjustmentNonAbsorbed'
                        : 'workOrderNegativeActualCostAdjustmentNonAbsorbed',
            });
            variancesToCreate.push({
                amount: newWipVariance,
                type: newWipVariance >= 0 ? 'workOrderActualCostAdjustment' : 'workOrderNegativeActualCostAdjustment',
            });
        }

        await asyncArray(variancesToCreate)
            .filter(variance => variance.amount !== 0)
            .forEach(async variance => {
                await this.createWipVariance(writableContext, {
                    workOrderToUpdate,
                    amount: variance.amount,
                    type: variance.type,
                    unit,
                    currency,
                });
            });
    }

    // eslint-disable-next-line class-methods-use-this
    async operationDatesNeedUpdating(
        newWorkOrder: xtremManufacturing.nodes.WorkOrder,
        oldWorkOrder: Readonly<xtremManufacturing.nodes.WorkOrder>,
    ): Promise<boolean> {
        if ((await oldWorkOrder.requestedDate) !== (await newWorkOrder.requestedDate)) {
            return true;
        }
        if (
            (await newWorkOrder.productionOperations.length) !== 0 &&
            (await (await oldWorkOrder.productionItem)?.releasedQuantity) !==
                (await (
                    await newWorkOrder.productionItem
                )?.releasedQuantity)
        ) {
            return true;
        }
        if (newWorkOrder.isOperationDeleted) {
            return true;
        }
        if (
            (await newWorkOrder.schedulingStatus) === 'scheduled' &&
            (await oldWorkOrder.isForwardScheduling) !== (await newWorkOrder.isForwardScheduling)
        ) {
            return true;
        }
        if (
            await newWorkOrder.productionOperations.find(async (operation, index1) => {
                if (index1 >= (await oldWorkOrder.productionOperations.length)) {
                    return true;
                }
                const oldOperation = await oldWorkOrder.productionOperations.elementAt(index1);

                if (
                    (await operation.expectedRunTime) !== (await oldOperation.expectedRunTime) ||
                    (await operation.expectedSetupTime) !== (await oldOperation.expectedSetupTime) ||
                    (await operation.setupTimeUnit) !== (await oldOperation.setupTimeUnit) ||
                    (await operation.runTimeUnit) !== (await oldOperation.runTimeUnit) ||
                    ((await operation.status) !== (await oldOperation.status) &&
                        ((await operation.status) === 'excluded' || (await oldOperation.status) === 'excluded'))
                ) {
                    return true;
                }

                if ((await newWorkOrder.schedulingStatus) === 'scheduled') {
                    if (
                        await operation.resources.find(async (resource, index2) => {
                            if (index2 >= (await oldOperation.resources.length)) {
                                return true;
                            }
                            const oldResource = await oldOperation.resources.elementAt(index2);

                            if (
                                (await resource.expectedRunTime) !== (await oldResource.expectedRunTime) ||
                                (await resource.expectedSetupTime) !== (await oldResource.expectedSetupTime) ||
                                ((await resource.status) !== (await oldResource.status) &&
                                    ((await resource.status) === 'excluded' ||
                                        (await oldResource.status) === 'excluded'))
                            ) {
                                return true;
                            }

                            return false;
                        })
                    ) {
                        return true;
                    }
                }

                return false;
            })
        ) {
            return true;
        }
        return false;
    }

    /**
     * Indicates if the WO component (line) can be automatically allocated
     * @param line work order component to check
     * @returns true if this component should be considered in the automatic allocation
     */
    // eslint-disable-next-line class-methods-use-this
    async isComponentAllocable(line: xtremManufacturing.nodes.WorkOrderComponent): Promise<boolean> {
        return (
            ['inProgress', 'pending'].includes(await line.lineStatus) && (await line.allocationStatus) !== 'notManaged'
        );
    }

    /**
     * Indicates if the WO component (line) should be taken into account for computing the header allocation status
     * @param line work order component to check
     * @returns true if this work order component should be considered in the allocation status determination
     */
    // eslint-disable-next-line class-methods-use-this
    async isComponentConsideredForAllocationStatus(
        line: xtremManufacturing.nodes.WorkOrderComponent,
    ): Promise<boolean> {
        return ['inProgress', 'pending'].includes(await line.lineStatus);
    }

    async updateReleasedItemPlannedCosts(
        workOrder: xtremManufacturing.nodes.WorkOrder,
        options?: {
            shouldSaveChanges?: true;
            canEventLinkRoutingOrBOM?: true;
            hasLinkedBOM?: boolean;
            hasLinkedRouting?: boolean;
        } & (
            | {
                  canEventLinkRoutingOrBOM: true;
                  hasLinkedBOM: boolean;
                  hasLinkedRouting: boolean;
              }
            | {}
        ),
    ) {
        const costOrigins: (
            | {
                  type: 'component';
                  component: xtremManufacturing.nodes.WorkOrderComponent;
              }
            | {
                  type: 'operation';
                  operation: xtremManufacturing.nodes.WorkOrderOperation;
              }
        )[] = [];

        const isUpdatePlannedCostEvent = !options?.canEventLinkRoutingOrBOM;
        const canUpdateProcessCosts = isUpdatePlannedCostEvent || options.hasLinkedRouting;
        const canUpdateMaterialCost = isUpdatePlannedCostEvent || options.hasLinkedBOM;

        if (canUpdateProcessCosts) {
            await workOrder.productionOperations.forEach(async operation => {
                if ((await operation.status) !== 'excluded') {
                    await operation.resources.forEach(async resource => {
                        if ((await resource.status) !== 'excluded') {
                            // do not modify the planned cost of an excluded resource
                            await resource.$.set({
                                expectedSetupCost: await resource.getExpectedCost(true),
                                expectedRunCost: await resource.getExpectedCost(false),
                            });
                        }
                    });
                }

                // TODO: make sure the cost are splitted when the time comes to have multiple released-items
                if (this.costTypeOperations(await operation.status)) {
                    costOrigins.push({
                        type: 'operation',
                        operation,
                    });
                }
            });
        }

        if (canUpdateMaterialCost) {
            await workOrder.productionComponents.forEach(async component => {
                if ((await component.lineStatus) !== 'excluded') {
                    // do not modify the planned cost of an excluded component
                    await component.$.set({ plannedCost: await component.getPlannedCost() });
                }
                // TODO: make sure the cost are splitted when the time comes to have multiple released-items
                if (this.costTypeComponents(await component.lineStatus)) {
                    costOrigins.push({
                        type: 'component',
                        component,
                    });
                }
            });
        }

        // TODO: make sure the cost are splitted when the time comes to have multiple released-items
        const workOrderReleasedItemUpdateData: {
            plannedLaborCost?: number;
            plannedToolCost?: number;
            plannedMachineCost?: number;
            plannedMaterialCost?: number;
        } = await asyncArray(costOrigins).reduce(
            async (accumulator, costOrigin) => {
                if (costOrigin.type === 'operation') {
                    const { operation } = costOrigin;
                    accumulator.plannedLaborCost +=
                        Number(await operation.expectedLaborCost) + (await operation.expectedLaborSetupCost);
                    accumulator.plannedToolCost +=
                        Number(await operation.expectedToolCost) + (await operation.expectedToolSetupCost);
                    accumulator.plannedMachineCost +=
                        (await operation.expectedMachineCost) + (await operation.expectedMachineSetupCost);
                } else {
                    accumulator.plannedMaterialCost += await costOrigin.component.plannedCost;
                }

                return accumulator;
            },
            {
                plannedLaborCost: 0,
                plannedToolCost: 0,
                plannedMachineCost: 0,
                plannedMaterialCost: 0,
            },
        );

        if (!canUpdateProcessCosts) {
            delete workOrderReleasedItemUpdateData.plannedLaborCost;
            delete workOrderReleasedItemUpdateData.plannedToolCost;
            delete workOrderReleasedItemUpdateData.plannedMachineCost;
        }

        if (!canUpdateMaterialCost) {
            delete workOrderReleasedItemUpdateData.plannedMaterialCost;
        }

        const workOrderReleasedItem = await workOrder.productionItems.elementAt(0);
        workOrderReleasedItem.allowPlannedCostUpdate = true;

        // TODO: make sure the cost are splitted when the time comes to have multiple released-items
        await workOrderReleasedItem.$.set({
            _action: 'update',
            _id: workOrderReleasedItem._id,
            ...workOrderReleasedItemUpdateData,
        });

        if (options?.shouldSaveChanges) {
            await workOrder.$.save();
        }
    }

    // eslint-disable-next-line class-methods-use-this
    async createTestWorkOrders(
        context: Context,
        orderQuantity: integer,
        bomId?: { itemId: string; siteId: string },
        orderNumberRoot = '',
    ) {
        let singleBom: xtremManufacturing.interfaces.TestBomRoutingObject | null = null;
        let boms: xtremManufacturing.interfaces.TestBomRoutingObject[] = [];
        let routings: xtremManufacturing.interfaces.TestBomRoutingObject[] = [];
        if (bomId) {
            [singleBom] = await context.select(
                xtremTechnicalData.nodes.BillOfMaterial,
                {
                    _id: true,
                    item: {
                        _id: true,
                        id: true,
                        name: true,
                    },
                    site: {
                        _id: true,
                        id: true,
                        name: true,
                    },
                },
                { filter: { item: { id: bomId.itemId }, site: { id: bomId.siteId } } },
            );
        } else {
            boms = await context.select(
                xtremTechnicalData.nodes.BillOfMaterial,
                {
                    _id: true,
                    item: {
                        _id: true,
                        id: true,
                        name: true,
                    },
                    site: {
                        _id: true,
                        id: true,
                        name: true,
                    },
                },
                { filter: {} },
            );
        }
        routings = await context.select(
            xtremTechnicalData.nodes.Routing,
            {
                _id: true,
                item: {
                    _id: true,
                    id: true,
                    name: true,
                },
                site: {
                    _id: true,
                    id: true,
                    name: true,
                },
            },
            { filter: {} },
        );
        const [normalCategory] = await context.select(
            xtremManufacturing.nodes.WorkOrderCategory,
            {
                _id: true,
            },
            { first: 1, filter: { routing: true, billOfMaterial: true } },
        );
        const [bomOnlyCategory] = await context.select(
            xtremManufacturing.nodes.WorkOrderCategory,
            {
                _id: true,
            },
            { first: 1, filter: { routing: false, billOfMaterial: true } },
        );

        await context.batch.updateProgress({
            phase: 'start',
            totalCount: orderQuantity,
            errorCount: 0,
            detail: 'started',
            successCount: 0,
        });

        for (let i = 0; i < orderQuantity; i += 1) {
            const itemQuantity = xtremMasterData.functions.randomInteger(1, 200);
            const bomNumber = xtremMasterData.functions.randomInteger(0, boms.length - 1);
            logger.debug(() => `Bom number ${bomNumber}`);
            const woType = i % 2 === 0 ? 'planned' : 'firm';
            const bom = singleBom || boms[bomNumber];
            const startDate = date.today().addDays(xtremMasterData.functions.randomInteger(0, 100));
            const routing: xtremManufacturing.interfaces.TestBomRoutingObject | undefined = routings.find(
                (route: xtremManufacturing.interfaces.TestBomRoutingObject) => {
                    logger.debug(
                        () => `Find routing route item ${JSON.stringify(route)} bom item ${JSON.stringify(bom)}`,
                    );
                    return route.item._id === bom.item._id && route.site._id === bom.site._id;
                },
            );
            const orderNumber = orderNumberRoot || 'WOTEST';
            const woCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                _id: routing ? `${normalCategory._id}` : `${bomOnlyCategory._id}`,
            });
            const newOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                siteId: bom.site.id,
                releasedItem: bom.item.id,
                releasedQuantity: itemQuantity,
                name: bom.item.name,
                type: woType,
                workOrderCategory: woCategory,
                startDate,
                workOrderNumber: `${orderNumber}-${i}`,
                bom: bom.item.id,
                route: routing ? routing.item.id : undefined,
            });
            const message = `Work order ${await newOrder.number}`;
            await context.batch.updateProgress({ phase: 'processing', successCount: i });
            await context.batch.logMessage('info', message);
            logger.debug(() => message);
        }

        await context.batch.updateProgress({
            phase: 'complete',
            totalCount: orderQuantity,
            errorCount: 0,
            detail: 'finished',
            successCount: orderQuantity,
        });
        return `Number of work orders created - ${orderQuantity}`;
    }

    // eslint-disable-next-line class-methods-use-this
    async getMaterialPlannedCost(
        context: Context,
        searchCriteria: {
            itemSite:
                | xtremMasterData.nodes.ItemSite
                | { item: xtremMasterData.nodes.Item; site: xtremSystem.nodes.Site };
            lineStatus: xtremManufacturing.enums.ComponentStatus;
            requiredDate: date;
            requiredQuantity: decimal;
        },
    ) {
        await logger.verboseAsync(
            async () =>
                `getMaterialPlannedCost(item=${await (await searchCriteria.itemSite.item).id}, site=${await (
                    await searchCriteria.itemSite.site
                ).id}, lineStatus=${searchCriteria.lineStatus}, requiredDate=${
                    searchCriteria.requiredDate
                } , requiredQuantity=${searchCriteria.requiredQuantity} )`,
        );
        let plannedCost = 0;
        const itemSite =
            searchCriteria.itemSite instanceof xtremMasterData.nodes.ItemSite
                ? searchCriteria.itemSite
                : await context.read(xtremMasterData.nodes.ItemSite, {
                      item: searchCriteria.itemSite.item,
                      site: searchCriteria.itemSite.site,
                  });
        if (searchCriteria.lineStatus !== 'included') {
            plannedCost =
                (
                    await xtremStockData.nodeExtensions.ItemSiteExtension.getItemSiteValuationCost(context, itemSite, {
                        quantity: 0, // set quantity=0 to only get a price in FIFO case
                        fifoCriteria: {
                            valuationType: 'issue',
                            dateOfValuation: date.make(1900, 1, 1), // to get the price of the oldest available tier
                        },
                        standardCostCriteria: {
                            standardCostDateOfValuation: searchCriteria.requiredDate ?? date.today(),
                        },
                    })
                ).unitCost * searchCriteria.requiredQuantity;
        }
        logger.verbose(() => ` -> plannedCost = ${plannedCost}`);
        return plannedCost;
    }

    // eslint-disable-next-line class-methods-use-this
    async inheritDimensionsFromReleasedItems(context: Context, workOrder: xtremManufacturing.nodes.WorkOrder) {
        await context.bulkUpdate(xtremManufacturing.nodes.WorkOrderComponent, {
            set: {
                storedAttributes: await (await workOrder.productionItem)?.storedAttributes,
                storedDimensions: await (await workOrder.productionItem)?.storedDimensions,
            },
            where: { document: workOrder._id },
        });

        await context.bulkUpdate(xtremManufacturing.nodes.WorkOrderOperationResource, {
            set: {
                storedAttributes: await (await workOrder.productionItem)?.storedAttributes,
                storedDimensions: await (await workOrder.productionItem)?.storedDimensions,
            },
            where: { workOrderOperation: { workOrder: workOrder._id } },
        });
    }

    // eslint-disable-next-line class-methods-use-this
    async updateProductionTrackingsStatus(
        context: Context,
        workOrder: xtremManufacturing.nodes.WorkOrder,
    ): Promise<void> {
        await workOrder.productionTrackings.forEach(async productionTracking => {
            const productionTrackingRecord = await context.read(
                xtremManufacturing.nodes.ProductionTracking,
                {
                    _id: productionTracking._id,
                },
                { forUpdate: true },
            );

            await productionTrackingRecord.lines.forEach(async line => {
                await line.$.set({ stockTransactionStatus: 'completed' });

                await context.bulkUpdate(xtremStockData.nodes.StockTransaction, {
                    set: { status: 'succeeded', resultAction: 'noChange' },
                    where: { documentLine: line._id, status: 'inProgress' },
                });
            });

            await productionTrackingRecord.$.save();
        });
    }

    // eslint-disable-next-line class-methods-use-this
    async resynchronizeTrackings(
        context: Context,
        trackings:
            | AsyncArray<xtremManufacturing.nodes.MaterialTracking>
            | AsyncArray<xtremManufacturing.nodes.ProductionTracking>,
    ) {
        const phase = (await trackings.elementAt(0)).$.factory.getLocalizedTitle(context);
        await context.batch.logMessage('info', phase);
        let { successCount } = await context.batch.getProgress();

        await trackings.forEach(async (tracking, index) => {
            if (await context.batch.isStopRequested()) {
                const stopMessage = context.localize(
                    '@sage/xtrem-manufacturing/work_order_lib__resync__stop_requested',
                    'Work order resync: Stop requested at {{stopDate}}.',
                    {
                        stopDate: new Date().toISOString(),
                    },
                );
                await context.batch.logMessage('info', stopMessage);

                await context.batch.confirmStop();
                throw Error(stopMessage);
            }
            await context.batch.logMessage('info', await tracking.number);
            await tracking.resynchronizeStatus();
            successCount += 1;

            if (index % 10 === 0) {
                await context.batch.updateProgress({
                    detail: await tracking.number,
                    successCount,
                    phase,
                });
            }
        });
        await context.batch.updateProgress({
            detail: '',
            successCount,
            phase,
        });
    }

    async resynchronizeWorkOrderTrackings(context: Context, workOrder: xtremManufacturing.nodes.WorkOrder) {
        if ((await workOrder.materialTrackings.length) > 0) {
            const materialTrackings = context.query(xtremManufacturing.nodes.MaterialTracking, {
                filter: { workOrder },
                forUpdate: true,
            });

            await this.resynchronizeTrackings(context, materialTrackings);
        }
        if ((await workOrder.productionTrackings.length) > 0) {
            const productionTrackings = context.query(xtremManufacturing.nodes.ProductionTracking, {
                filter: { workOrder },
                forUpdate: true,
            });
            await this.resynchronizeTrackings(context, productionTrackings);
        }
    }

    async resynchronizeStatus(
        context: Context,
        workOrder: xtremManufacturing.nodes.WorkOrder,
    ): Promise<{
        oldStatus: xtremManufacturing.enums.WorkOrderStatus;
        newStatus: xtremManufacturing.enums.WorkOrderStatus;
    }> {
        const numberOfMaterialTrackings = await workOrder.materialTrackings.length;
        const numberOfProductionTrackings = await workOrder.productionTrackings.length;
        const numberOfTrackings = numberOfMaterialTrackings + numberOfProductionTrackings;

        await context.batch.updateProgress({
            detail: await workOrder.number,
            errorCount: 0,
            successCount: 0,
            totalCount: numberOfTrackings,
            phase: 'start',
        });

        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-manufacturing/work_order_lib__resync__start',
                'Work order statuses sync start',
            ),
        );

        const oldStatus = await workOrder.status;

        await this.resynchronizeWorkOrderTrackings(context, workOrder);

        const updatedWorkOrder = await context.read(
            xtremManufacturing.nodes.WorkOrder,
            { _id: workOrder._id },
            { forUpdate: true },
        );
        let newStatus = await updatedWorkOrder.status;

        if (
            (await updatedWorkOrder.status) === 'completed' &&
            (await updatedWorkOrder.stockTransactionStatus) === 'inProgress' &&
            (await updatedWorkOrder.closingDate)
        ) {
            await xtremManufacturing.functions.workOrderLib.updateProductionTrackingsStatus(context, workOrder);
            await updatedWorkOrder.setCompletedStockTransactionStatus();

            newStatus = 'closed';
            await updatedWorkOrder.$.set({ status: newStatus });
            await updatedWorkOrder.$.save();
        }

        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-manufacturing/work_order_lib__resync__end',
                'Work order statuses sync finished',
            ),
        );
        await context.batch.updateProgress({ phase: 'finish', successCount: 1, totalCount: 1 });

        return { oldStatus, newStatus };
    }
}

export const workOrderLib = WorkerOrderLib.instance;
