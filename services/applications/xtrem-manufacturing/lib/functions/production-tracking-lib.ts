import type { AsyncArrayReader, Collection, Context } from '@sage/xtrem-core';
import * as xtremManufacturing from '../../index';

class ProductionTrackingLib {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private constructor() {}

    static readonly instance = new ProductionTrackingLib();

    // eslint-disable-next-line class-methods-use-this
    async updateAfterStockSuccess(
        writableContext: Context,
        params: {
            tracking: xtremManufacturing.nodes.ProductionTracking;
            lines:
                | Collection<xtremManufacturing.nodes.ProductionTrackingLine>
                | AsyncArrayReader<xtremManufacturing.nodes.ProductionTrackingLine>;
            isStockDocumentCompleted: boolean;
        },
    ) {
        await params.lines.forEach((line: xtremManufacturing.nodes.ProductionTrackingLine) =>
            xtremManufacturing.nodes.ProductionTrackingLine.afterLineSaving(writableContext, line),
        );

        // Update the bill of material tracking
        const workOrder = await params.tracking.workOrder;
        const existsMaterialTrackingInError =
            (await writableContext.queryCount(xtremManufacturing.nodes.MaterialTracking, {
                filter: {
                    workOrder,
                    lines: { _atLeast: 1, stockTransactionStatus: 'error' },
                },
            })) > 0;

        await xtremManufacturing.functions.updateBomTrackingStatuses(writableContext, {
            number: await workOrder.number,
            stockStatus: existsMaterialTrackingInError ? 'error' : await params.tracking.stockTransactionStatus,
            workOrderStatus: await workOrder.status,
        });

        if (params.isStockDocumentCompleted) {
            await xtremManufacturing.nodes.ProductionTracking.onceStockCompleted(writableContext, params.tracking);
        }
    }
}

export const productionTrackingLib = ProductionTrackingLib.instance;
