import type { Node, StaticThis } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremManufacturing from '../index';

class AllocationLib {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private constructor() {}

    static instance = new AllocationLib();

    // eslint-disable-next-line class-methods-use-this
    getAllocationUpdatesGraphQLDescriptor<NodeType extends StaticThis<Node> = typeof Node>(
        isMandatory = false,
    ): ReturnType<typeof xtremStockData.functions.allocationLib.getAllocationUpdatesGraphQLDescriptor> {
        const stockGraphQLDescriptor =
            xtremStockData.functions.allocationLib.getAllocationUpdatesGraphQLDescriptor<NodeType>(isMandatory);
        return {
            allocationUpdates: {
                ...stockGraphQLDescriptor.allocationUpdates,
                item: {
                    type: 'object',
                    properties: {
                        ...stockGraphQLDescriptor.allocationUpdates.item.properties,
                        action: {
                            ...stockGraphQLDescriptor.allocationUpdates.item.properties.action,
                            dataType: () => xtremManufacturing.enums.ManufacturingAllocationUpdateActionDataType,
                        },
                    },
                },
            },
        };
    }

    // eslint-disable-next-line class-methods-use-this
    manufacturingConvertFilterToMassProcessCriteria(filter: {
        workOrder: {
            site: {
                id: string;
            };
            startDate?: {
                _lte: string;
            };
            number?: {
                _gte?: string;
                _lte?: string;
            };
            productionItems?: {
                releasedItem: {
                    name: {
                        _gte?: string;
                        _lte?: string;
                    };
                };
            };
        };
        operation?: {
            operationNumber: {
                _gte?: string;
                _lte?: string;
            };
        };
    }) {
        return {
            stockSite: filter.workOrder.site.id,
            ...(filter.workOrder?.number?._gte ? { fromOrderNumber: filter.workOrder.number._gte } : {}),
            ...(filter.workOrder?.number?._lte ? { toOrderNumber: filter.workOrder.number._lte } : {}),

            ...(filter.workOrder?.productionItems?.releasedItem?.name._gte
                ? { fromReleasedItem: filter.workOrder.productionItems.releasedItem.name._gte }
                : {}),
            ...(filter.workOrder?.productionItems?.releasedItem?.name._lte
                ? { toReleasedItem: filter.workOrder.productionItems.releasedItem.name._lte }
                : {}),
            ...(filter.operation?.operationNumber?._gte
                ? { fromOperation: filter.operation.operationNumber._gte }
                : {}),
            ...(filter.operation?.operationNumber?._lte ? { toOperation: filter.operation.operationNumber._lte } : {}),

            ...(filter.workOrder?.startDate?._lte ? { maximumStartDate: filter.workOrder.startDate._lte } : {}),
        } as xtremManufacturing.sharedFunctions.allocation.ManufacturingAllocationMassProcessCriteria;
    }
}

export const allocationLib = AllocationLib.instance;
