import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremManufacturingInterfaces from '../interfaces/index';
import { MaterialTracking } from '../nodes/material-tracking';
import type { MaterialTrackingLine } from '../nodes/material-tracking-line';
import { OperationTracking } from '../nodes/operation-tracking';
import type { OperationTrackingLine } from '../nodes/operation-tracking-line';
import { ProductionTracking } from '../nodes/production-tracking';
import type { ProductionTrackingLine } from '../nodes/production-tracking-line';
import type { WorkOrder } from '../nodes/work-order';
import { WorkOrderOperationResourceDetail } from '../nodes/work-order-operation-resource-detail';
/**
 * Repost a material tracking from a work order after the journal creation or the sending to integrated finance
 * solution (e.g. Intacct) failed because of missing dimensions
 * @param options.context Context
 * @param options.financeTransaction the financeTransaction record for which the reposting has to be done
 * @param options.workOrder the work order on which the dimensions were added
 * @param options.updateLines an array of workOrderLine links with corresponding attributes and dimensions
 * @return wasSuccessful
 */
export async function repostMaterialTracking(options: {
    context: Context;
    financeTransaction: xtremFinanceData.nodes.FinanceTransaction;
    workOrder: WorkOrder;
    updateLines: {
        _id: number;
        storedAttributes: xtremMasterData.interfaces.StoredAttributes;
        storedDimensions: Object;
    }[];
}): Promise<xtremFinanceData.interfaces.MutationResult> {
    // read corresponding material tracking
    const materialTracking = await options.context.read(
        MaterialTracking,
        { _id: await options.financeTransaction.sourceDocumentSysId },
        { forUpdate: true },
    );

    // update attributes and dimensions on material tracking lines from work order components
    const updateMaterialLines = await materialTracking.lines
        .map(async (materialLine: MaterialTrackingLine) => {
            const workOrderLineId = (await materialLine?.workOrderLine)?._id || 0;
            const updateLine = options.updateLines.filter(line => line._id === workOrderLineId)[0];
            return {
                _id: materialLine?._id || 0,
                storedAttributes: updateLine.storedAttributes,
                storedDimensions: updateLine.storedDimensions,
            };
        })
        .toArray();
    await materialTracking.$.set({ forceUpdateForFinance: true, lines: updateMaterialLines });
    await materialTracking.$.save();

    // send notification in order to update a staging table entry for the accounting engine
    await xtremFinanceData.functions.financeDocumentUpdateNotification(options.context, {
        document: options.workOrder as xtremFinanceData.interfaces.FinanceOriginDocument,
        lines: (await materialTracking.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLineBase[],
        documentType: 'workInProgress',
        replyTopic: 'MaterialTracking/accountingInterface',
        doNotPostOnUpdate: false,
        financeTransactionRecord: options.financeTransaction,
    });
    return {
        wasSuccessful: true,
        message: options.context.localize(
            '@sage/xtrem-manufacturing/nodes__work_order__material_tracking_was_reposted',
            'The material tracking was reposted.',
        ),
    };
}

/**
 * Repost a production tracking from a work order after the journal creation or the sending to integrated finance
 * solution (e.g. Intacct) failed because of missing dimensions
 * @param options.context Context
 * @param options.financeTransaction the financeTransaction record for which the reposting has to be done
 * @param options.workOrder the work order on which the dimensions were added
 * @param options.updateLines an array of workOrderReleasedItem links with corresponding attributes and dimensions
 * @return wasSuccessful
 */
export async function repostProductionTracking(options: {
    context: Context;
    financeTransaction: xtremFinanceData.nodes.FinanceTransaction;
    workOrder: WorkOrder;
    updateLines: {
        _id: number;
        storedAttributes: xtremMasterData.interfaces.StoredAttributes;
        storedDimensions: Object;
    }[];
}): Promise<xtremFinanceData.interfaces.MutationResult> {
    // read corresponding production tracking
    const productionTracking = await options.context.read(
        ProductionTracking,
        { _id: await options.financeTransaction.sourceDocumentSysId },
        { forUpdate: true },
    );

    // update attributes and dimensions on production tracking lines from work order released item
    const updateProductionLines = await productionTracking.lines
        .map(async (productionLine: ProductionTrackingLine) => {
            const workOrderLineId = (await productionLine?.workOrderLine)?._id || 0;
            const updateLine = options.updateLines.filter(line => line._id === workOrderLineId)[0];
            return {
                _id: productionLine?._id || 0,
                storedAttributes: updateLine.storedAttributes,
                storedDimensions: updateLine.storedDimensions,
            };
        })
        .toArray();
    await productionTracking.$.set({ forceUpdateForFinance: true, lines: updateProductionLines });
    await productionTracking.$.save();

    // send notification in order to update a staging table entry for the accounting engine
    await xtremFinanceData.functions.financeDocumentUpdateNotification(options.context, {
        document: options.workOrder as xtremFinanceData.interfaces.FinanceOriginDocument,
        lines: (await productionTracking.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLineBase[],
        documentType: 'workInProgress',
        replyTopic: 'ProductionTracking/accountingInterface',
        doNotPostOnUpdate: false,
        financeTransactionRecord: options.financeTransaction,
    });
    return {
        wasSuccessful: true,
        message: options.context.localize(
            '@sage/xtrem-manufacturing/nodes__work_order__production_tracking_was_reposted',
            'The production tracking was reposted.',
        ),
    };
}

/**
 * Repost a work order closing after the journal creation or the sending to integrated finance
 * solution (e.g. Intacct) failed because of missing dimensions
 * @param options.context Context
 * @param options.financeTransaction the financeTransaction record for which the reposting has to be done
 * @param options.workOrder the work order on which the dimensions were added
 * @return wasSuccessful
 */
export async function repostWorkOrderClosing(options: {
    context: Context;
    financeTransaction: xtremFinanceData.nodes.FinanceTransaction;
    workOrder: WorkOrder;
}): Promise<xtremFinanceData.interfaces.MutationResult> {
    // send notification in order to update a staging table entry for the accounting engine
    await xtremFinanceData.functions.financeDocumentUpdateNotification(options.context, {
        document: options.workOrder as xtremFinanceData.interfaces.FinanceOriginDocument,
        lines: (await options.workOrder.productionItems.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLineBase[],
        documentType: 'workInProgress',
        replyTopic: 'WorkOrderClosing/accountingInterface',
        doNotPostOnUpdate: false,
        financeTransactionRecord: options.financeTransaction,
    });
    return {
        wasSuccessful: true,
        message: options.context.localize(
            '@sage/xtrem-manufacturing/nodes__work_order__closing_was_reposted',
            'The work order closing was reposted.',
        ),
    };
}

/**
 * Repost an operation tracking from a work order after the journal creation or the sending to integrated finance
 * solution (e.g. Intacct) failed because of missing dimensions
 * @param options.context Context
 * @param options.financeTransaction the financeTransaction record for which the reposting has to be done
 * @param options.workOrder the work order on which the dimensions were added
 * @param options.updateLines an array of operation links with corresponding attributes and dimensions
 * @return wasSuccessful
 */
export async function repostOperationTracking(options: {
    context: Context;
    financeTransaction: xtremFinanceData.nodes.FinanceTransaction;
    workOrder: WorkOrder;
    updateLines: {
        _id: number;
        storedAttributes: xtremMasterData.interfaces.StoredAttributes;
        storedDimensions: Object;
        operationSysId: number;
        resourceSysId: number;
    }[];
}): Promise<xtremFinanceData.interfaces.MutationResult> {
    // read corresponding operation tracking
    const operationTracking = await options.context.read(
        OperationTracking,
        { _id: await options.financeTransaction.sourceDocumentSysId },
        { forUpdate: true },
    );

    // check for each WorkOrderResource in options.updateLines if it is a group resource (means there exist linked
    // WorkOrderResourceDetail records). If yes, we get the list of resource _ids to find the correct operation
    // tracking line (in the tracking line there is a link to a single resource not to the group resource)
    const updateLinesWithDetailResources = await Promise.all(
        options.updateLines.map(async updateLine => {
            // search for WorkOrderOperationResourceDetail records
            const resourceDetails = options.context.query(WorkOrderOperationResourceDetail, {
                filter: { workOrderOperationResource: updateLine._id },
            });
            if (await resourceDetails.length) {
                const resourceSysIds: number[] = [];
                await resourceDetails.forEach(async detail => {
                    resourceSysIds.push((await detail.resource)._id);
                });
                return {
                    ...updateLine,
                    resourceSysIds,
                };
            }
            // if no WorkOrderOperationResourceDetail records found => use resourceSysId from WorkOrderOperationResource
            return {
                ...updateLine,
                resourceSysIds: [updateLine.resourceSysId],
            };
        }),
    );

    // update attributes and dimensions on operation tracking lines from work order operation
    const updateOperationLines = await operationTracking.lines
        .map(async (operationLine: OperationTrackingLine) => {
            const operationSysId = (await operationLine?.workOrderOperation)?._id || 0;
            const actualResourceSysId = (await operationLine?.actualResource)?._id || 0;
            const updateLine = updateLinesWithDetailResources.filter(line => {
                return line.operationSysId === operationSysId && line.resourceSysIds.includes(actualResourceSysId);
            })[0];
            return {
                _id: operationLine?._id || 0,
                storedAttributes: updateLine.storedAttributes,
                storedDimensions: updateLine.storedDimensions,
            };
        })
        .toArray();
    await operationTracking.$.set({ lines: updateOperationLines });
    await operationTracking.$.save();

    // send notification in order to update a staging table entry for the accounting engine
    await xtremFinanceData.functions.financeDocumentUpdateNotification(options.context, {
        document: options.workOrder as xtremFinanceData.interfaces.FinanceOriginDocument,
        lines: (await operationTracking.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLineBase[],
        documentType: 'workInProgress',
        replyTopic: 'OperationTracking/accountingInterface',
        doNotPostOnUpdate: false,
        financeTransactionRecord: options.financeTransaction,
    });
    return {
        wasSuccessful: true,
        message: options.context.localize(
            '@sage/xtrem-manufacturing/nodes__work_order__operation_tracking_was_reposted',
            'The operation tracking was reposted.',
        ),
    };
}

/**
 * Handles the repost of all kinds of posting originating from a work order (material tracking, time tracking, production
 * tracking, work order closing)
 * @param options.context Context
 * @param options.workOrder the work order on which the dimensions were added
 * @param options.financeTransaction the financeTransaction record for which the reposting has to be done
 * @param options.components the components of the work order with added dimensions
 * @param options.releasedItems the releasedItems of the work order with added dimensions
 * @param options.operationResources the operationResources of the work order with added dimensions
 * @return wasSuccessful
 */
export async function repost(options: {
    context: Context;
    workOrder: WorkOrder;
    financeTransactionSysId: string;
    components: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[];
    releasedItems: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[];
    operationResources: xtremManufacturingInterfaces.FinanceOperationResource[];
}): Promise<xtremFinanceData.interfaces.MutationResult> {
    if (!['failed', 'notRecorded', 'error'].includes(await options.workOrder.financeIntegrationStatus)) {
        throw new BusinessRuleError(
            options.context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__cant_repost_tracking_when_status_is_not_failed',
                "You can only repost a work order tracking if the status is 'Failed'.",
            ),
        );
    }
    const financeTransaction: xtremFinanceData.nodes.FinanceTransaction = await options.context.read(
        xtremFinanceData.nodes.FinanceTransaction,
        { _id: options.financeTransactionSysId },
    );
    if (financeTransaction) {
        if ((await financeTransaction.sourceDocumentType) === 'materialTracking') {
            const updateLines = options.components.map(component => {
                return {
                    _id: component.baseDocumentLineSysId,
                    storedAttributes: component.storedAttributes,
                    storedDimensions: component.storedDimensions,
                };
            });
            await options.workOrder.$.set({ productionComponents: updateLines });
            await options.workOrder.$.save();

            // repost the material tracking
            return repostMaterialTracking({
                context: options.context,
                financeTransaction,
                workOrder: options.workOrder,
                updateLines,
            });
        }
        if (['productionTracking', 'workOrderClose'].includes(await financeTransaction.sourceDocumentType)) {
            const updateLines = options.releasedItems.map(releasedItem => {
                return {
                    _id: releasedItem.baseDocumentLineSysId,
                    storedAttributes: releasedItem.storedAttributes,
                    storedDimensions: releasedItem.storedDimensions,
                };
            });
            await options.workOrder.$.set({ productionItems: updateLines });
            await options.workOrder.$.save();

            if ((await financeTransaction.sourceDocumentType) === 'productionTracking') {
                // repost the production tracking
                return repostProductionTracking({
                    context: options.context,
                    financeTransaction,
                    workOrder: options.workOrder,
                    updateLines,
                });
            }
            // repost the work order closing
            return repostWorkOrderClosing({
                context: options.context,
                financeTransaction,
                workOrder: options.workOrder,
            });
        }
        if ((await financeTransaction.sourceDocumentType) === 'operationTracking') {
            const updateLines = options.operationResources.map(operation => {
                return {
                    _id: operation.baseDocumentLineSysId,
                    storedAttributes: operation.storedAttributes,
                    storedDimensions: operation.storedDimensions,
                    operationSysId: operation.workOrderOperationSysId,
                    resourceSysId: operation.resourceSysId,
                };
            });
            await options.workOrder.productionOperations.forEach(async operation => {
                const resources = updateLines
                    .filter(line => line.operationSysId === operation._id)
                    .map(line => {
                        return {
                            _id: line._id,
                            storedAttributes: line.storedAttributes,
                            storedDimensions: line.storedDimensions,
                        };
                    });
                await operation.$.set({ resources });
                await operation.$.save();
            });

            // repost the operation tracking
            return repostOperationTracking({
                context: options.context,
                financeTransaction,
                workOrder: options.workOrder,
                updateLines,
            });
        }
    }
    return {
        wasSuccessful: true,
        message: options.context.localize(
            '@sage/xtrem-manufacturing/nodes__work_order__no_document_was_posted',
            'Nothing was reposted.',
        ),
    };
}
