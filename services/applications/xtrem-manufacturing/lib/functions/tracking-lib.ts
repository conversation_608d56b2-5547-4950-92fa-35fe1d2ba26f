import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremManufacturing from '../../index';
import { loggers } from './loggers';

const logger = loggers.trackingLib;

class TrackingLib {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private constructor() {}

    static readonly instance = new TrackingLib();

    /**
     * Resynchronize the stock status of a tracking document.
     * @returns true if resynchronization can continue, false if resynchronization must be stopped for this tracking
     */
    // eslint-disable-next-line class-methods-use-this
    async resynchronizeStockStatus(
        context: Context,
        tracking: xtremManufacturing.nodes.ProductionTracking | xtremManufacturing.nodes.MaterialTracking,
    ): Promise<boolean> {
        const { countInErrorLines } = await xtremStockData.functions.stockTransactionLib.resynchronizeStockStatus(
            context,
            {
                document: tracking,
                ...(tracking instanceof xtremManufacturing.nodes.MaterialTracking
                    ? { mustDeleteAllocationIfError: (await (await tracking.workOrder).status) === 'closed' }
                    : {}),
                logger,
            },
        );

        if (countInErrorLines > 0) {
            await context.batch.logMessage(
                'warning',
                context.localize(
                    '@sage/xtrem-manufacturing/tracking_lib__resync__need_to_post_again',
                    'The tracking needs to be posted to stock again: {{number}}.',
                    { number: await tracking.number },
                ),
            );
            return false;
        }

        return true;
    }

    // eslint-disable-next-line class-methods-use-this
    async validateStockUpdateCompletion(
        context: Context,
        tracking: xtremManufacturing.nodes.ProductionTracking | xtremManufacturing.nodes.MaterialTracking,
    ): Promise<void> {
        const isStockUpdateFinished = await tracking.lines.every(
            async line => (await line.stockTransactionStatus) === 'completed',
        );
        if (!isStockUpdateFinished) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-manufacturing/nodes__tracking_lib__stock_update_not_finished',
                    'The stock update is still in progress. You can resync after it has completed.',
                ),
            );
        }
    }

    // eslint-disable-next-line class-methods-use-this
    async getNumberOfExistingWipCosts(
        context: Context,
        tracking: xtremManufacturing.nodes.ProductionTracking | xtremManufacturing.nodes.MaterialTracking,
    ): Promise<number> {
        const type =
            tracking instanceof xtremManufacturing.nodes.MaterialTracking ? 'materialTracking' : 'productionTracking';
        return context.queryCount(xtremManufacturing.nodes.WorkInProgressCost, {
            filter: {
                workOrder: { number: await (await tracking.workOrder).number },
                type,
                originatingLine: { _id: { _in: await tracking.lines.map(line => line._id).toArray() } },
            },
        });
    }

    async resynchronizeStatus<
        Tracking extends xtremManufacturing.nodes.ProductionTracking | xtremManufacturing.nodes.MaterialTracking,
    >(
        context: Context,
        tracking: Tracking,
        onceStockCompleted: (
            writableContext: Context,
            params: {
                tracking: Tracking;
                lines: Tracking['lines'];
                isStockDocumentCompleted: boolean;
            },
        ) => Promise<void>,
    ): Promise<void> {
        const trackingNumber = await tracking.number;
        logger.verbose(() => `Resynchronizing ${tracking.$.factory.name}: ${trackingNumber}`);

        // ensure stockTransactionStatus is correct
        if (!(await this.resynchronizeStockStatus(context, tracking))) return;

        // nothing else can be done with a Closed work order
        // we cannot create WorkInProgressCost nor trigger the finance notification
        if ((await (await tracking.workOrder).status) === 'closed') {
            return;
        }

        // Check if StockJournal have been created. If not -> not managed yet
        await this.validateStockUpdateCompletion(context, tracking);

        // Check if workInProgressCost has already been created
        let wipCostNumber = await this.getNumberOfExistingWipCosts(context, tracking);
        if (wipCostNumber) {
            logger.info(() => 'Work in progress cost has been created. Resynchronization is not needed.');
            return;
        }

        await onceStockCompleted(context, {
            tracking,
            lines: tracking.lines,
            isStockDocumentCompleted: true,
        });

        wipCostNumber = await this.getNumberOfExistingWipCosts(context, tracking);

        logger.verbose(() => `WIP cost created: ${wipCostNumber}`);
    }
}

export const trackingLib = TrackingLib.instance;
