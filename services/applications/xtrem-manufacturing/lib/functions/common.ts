import type { OperationGrant } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import * as xtremUpload from '@sage/xtrem-upload';
import * as xtremManufacturing from '../index';

export const commonResourcesOperations: OperationGrant[] = [
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.GroupResource] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.MachineResource] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.GroupResource] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.LaborResource] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.CapabilityLevel] },
];

export const commonWorkOrderOperations: OperationGrant[] = [
    { operations: ['lookup', 'getUnitConversionFactor'], on: [() => xtremMasterData.nodes.UnitOfMeasure] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.ItemSite] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Item] },
    { operations: ['lookup'], on: [() => xtremTechnicalData.nodes.BillOfMaterial] },
    { operations: ['lookup'], on: [() => xtremSystem.nodes.Site] },
    { operations: ['lookup'], on: [() => xtremSystem.nodes.Company] },
    {
        operations: ['generateReportPdf', 'generateReportZip', 'generateReports'],
        on: [() => xtremReporting.nodes.Report],
    },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.CapabilityLevel] },
    { operations: ['lookup'], on: [() => xtremManufacturing.nodes.WorkOrderOperationResource] },
    ...commonResourcesOperations,
    ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
    { operations: ['lookup'], on: [() => xtremUpload.nodes.AttachmentAssociation] },
];

export const commonTrackingOperations: OperationGrant[] = [
    { operations: ['lookup'], on: [() => xtremManufacturing.nodes.WorkOrder] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.Item] },
    { operations: ['lookup'], on: [() => xtremSystem.nodes.Site] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.BusinessEntity] },
    { operations: ['lookup'], on: [() => xtremMasterData.nodes.SequenceNumber] },
    ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
    ...xtremStockData.functions.allocationLib.allocationOperation,
];
