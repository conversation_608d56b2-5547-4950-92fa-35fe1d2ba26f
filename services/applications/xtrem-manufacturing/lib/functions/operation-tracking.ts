import type { Context, NodeCreateData, decimal } from '@sage/xtrem-core';
import { asyncArray, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { isNumber } from 'lodash';
import * as xtremManufacturing from '../index';
import { loggers } from './loggers';

const { timeTrackingLog } = loggers;

async function processRessource(
    operationResource: xtremManufacturing.nodes.WorkOrderOperationResource,
    trackingQuantity: decimal,
): Promise<NodeCreateData<xtremManufacturing.nodes.OperationTrackingLine>> {
    const storedAttributes = await operationResource.storedAttributes;
    const storedDimensions = await operationResource.storedDimensions;
    const expectedRunTime = await operationResource.expectedRunTime;
    const workOrderOperation = await operationResource.workOrderOperation;
    const plannedQuantity = await workOrderOperation.plannedQuantity;

    let runTime = (trackingQuantity / plannedQuantity) * expectedRunTime;
    const setupTime =
        (await workOrderOperation.completedQuantity) === 0 ? await operationResource.expectedSetupTime : 0;

    if ((await (await workOrderOperation.workOrder).timeUnit) === 'seconds') {
        runTime = Math.round(runTime);
    }
    // actualSetupTime(trackingSetupTime) actualRunTime(trackingRunTime)
    const baseLine = { actualSetupTime: setupTime, actualRunTime: runTime, storedAttributes, storedDimensions };

    const operationResourceResource = await operationResource.resource;
    if (operationResourceResource instanceof xtremMasterData.nodes.DetailedResource) {
        return { actualResource: operationResourceResource, ...baseLine };
    }
    const { resources } = operationResource;
    return { actualResource: await (await resources.at(0))?.resource, ...baseLine };
}

export async function createOperationTrackings(
    context: Context,
    tracking: {
        workOrder: xtremManufacturing.nodes.WorkOrder;
        trackingNumber: string;
        quantity: decimal;
        operationNumber?: number;
        date: date;
    },
): Promise<xtremManufacturing.nodes.OperationTracking | null> {
    const workOrderOperations = tracking.workOrder.productionOperations
        .filter(async operationFilter => !['excluded', 'included'].includes(await operationFilter.status))
        .filter(async operation =>
            tracking.operationNumber ? (await operation.operationNumber) === tracking.operationNumber : true,
        );

    if (!(await workOrderOperations.length)) {
        timeTrackingLog.debug(() => `No operation found for work order ${tracking.workOrder.number}`);
        return null;
    }

    const trackingPayload: NodeCreateData<xtremManufacturing.nodes.OperationTracking> = {
        workOrder: tracking.workOrder,
        number: tracking.trackingNumber,
        entryDate: tracking.date || date.today(),
        lines: [],
    };
    let lineNumber = 0;

    trackingPayload.lines = (
        await workOrderOperations
            .filter(async operation => (await operation.resources.length) > 0)
            .map(operation => {
                return operation.resources
                    .map(async operationResource => {
                        const completedQuantity = (await operationResource.isResourceQuantity) ? tracking.quantity : 0;
                        lineNumber += 1;
                        return {
                            workOrderOperation: operation,
                            line: lineNumber,
                            completedQuantity,
                            ...(await processRessource(operationResource, tracking.quantity)),
                        };
                    })
                    .toArray();
            })
            .toArray()
    ).flat();
    if (!trackingPayload.lines.length) {
        timeTrackingLog.debug(() => `No operation resources found for work order ${tracking.workOrder.number}`);
        return null;
    }

    const createTracking = async (writableContext: Context) => {
        const operationTracking = await writableContext.create(
            xtremManufacturing.nodes.OperationTracking,
            trackingPayload,
        );
        await operationTracking.$.save();
        return operationTracking;
    };

    if (context.isWritable) {
        return createTracking(context);
    }
    return context.runInWritableContext(createTracking);
}

export interface TimeTracking
    extends Partial<Omit<xtremManufacturing.nodes.WorkOrderOperationResource, 'workOrderOperation'>> {
    workOrderOperation: {
        workOrder: xtremManufacturing.nodes.WorkOrder;
        operationNumber?: number;
        name: string;
        minCapabilityLevel?: xtremMasterData.nodes.CapabilityLevel;
        remainingQuantity: number; // is it needed ?
    };
    expectedResource: xtremMasterData.nodes.DetailedResource;
    completedRunTime?: string;
    plannedQuantity?: string;
    completedQuantity?: string;
    completedSetupTime?: string;
    actualQuantity?: number;
    isCompleted?: boolean;
}

async function addOperationToWorkOrder(
    context: Context,
    tracking: TimeTracking,
): Promise<xtremManufacturing.nodes.WorkOrderOperation> {
    const { workOrderOperation } = tracking;
    if (!workOrderOperation?.workOrder?._id) {
        throw new Error('Work order is required');
    }
    if (!(isNumber(tracking.expectedResource) || tracking.expectedResource?._id)) {
        throw new Error('Detail resource is required');
    }
    const { minCapabilityLevel } = workOrderOperation;
    if (!(isNumber(minCapabilityLevel) || minCapabilityLevel?._id)) {
        throw new Error('Min capability level is required');
    }
    const { workOrder, operationNumber } = workOrderOperation;

    const updateWorkOrder = await context.read(
        xtremManufacturing.nodes.WorkOrder,
        { _id: workOrder?._id },
        { forUpdate: true },
    );
    const actualResource = await context.read(xtremMasterData.nodes.DetailedResource, {
        _id: tracking.expectedResource._id,
    });

    await updateWorkOrder.productionOperations.append({
        ...(operationNumber ? { operationNumber } : {}),
        name: workOrderOperation.name,
        plannedQuantity: tracking.actualQuantity,
        minCapabilityLevel,
        setupTimeUnit: await tracking.setupTimeUnit,
        runTimeUnit: await tracking.runTimeUnit,
        status: 'included',
        resources: [
            {
                resource: actualResource,
                efficiency: await actualResource.efficiency,
                expectedSetupTime: await tracking.actualSetupTime,
                expectedRunTime: await tracking.actualRunTime,
                isResourceQuantity: true,
                status: 'included',
            },
        ],
    });
    await xtremManufacturing.functions.forceUpdateForScheduling(updateWorkOrder);
    await updateWorkOrder.$.save();
    return updateWorkOrder.productionOperations.elementAt(-1);
}

async function getOrCreateProductionOperation(
    context: Context,
    tracking: TimeTracking,
): Promise<xtremManufacturing.nodes.WorkOrderOperation> {
    const { workOrderOperation } = tracking;

    if (!workOrderOperation?.workOrder) {
        throw new Error('Work order is required');
    }

    if (workOrderOperation?.operationNumber) {
        const workOrderOperationKey = {
            workOrder: workOrderOperation.workOrder,
            operationNumber: workOrderOperation.operationNumber,
        };
        timeTrackingLog.debug(() => `Work order operation key: ${JSON.stringify(workOrderOperationKey)}`);
        const operation = await context.tryRead(xtremManufacturing.nodes.WorkOrderOperation, workOrderOperationKey, {
            forUpdate: true,
        });
        if (operation) {
            return operation;
        }
    }
    return addOperationToWorkOrder(context, tracking);
}

function createAllTrackings(
    context: Context,
    trackingPayloads: NodeCreateData<xtremManufacturing.nodes.OperationTracking>[],
): Promise<number[]> {
    const createTrackings = (writableContext: Context) => {
        return asyncArray(trackingPayloads)
            .map(async trackingPayload => {
                timeTrackingLog.debug(() => JSON.stringify(trackingPayload, null, 2));

                const operationTracking = await writableContext.create(
                    xtremManufacturing.nodes.OperationTracking,
                    trackingPayload,
                );
                await operationTracking.$.save();
                await context.batch.logMessage(
                    'info',
                    `Time tracking created for work order ${await (await operationTracking.workOrder).number}
                    with tracking number ${await operationTracking.number}`,
                );
                return operationTracking._id;
            })
            .toArray();
    };

    if (context.isWritable) {
        return createTrackings(context);
    }
    return context.runInWritableContext(createTrackings);
}

async function notifyUserForTrackings(context: Context, sysIds: number[]): Promise<void> {
    timeTrackingLog.debug(() => `Trackings created: ${sysIds.join(', ')}`);
    const link = xtremSystem.functions.linkToFilteredMainList('@sage/xtrem-manufacturing/TimeTrackingLineInquiry/', {
        _id: { _in: sysIds },
    });
    const operationTrackingCreated = context.localize(
        '@sage/xtrem-manufacturing/node-operation-tracking-created',
        'Time trackings created',
    );
    await context.batch.logMessage('result', `${operationTrackingCreated}`, { data: { url: link } });
    await context.notifyUser({
        title: operationTrackingCreated,
        icon: 'basket',
        description: operationTrackingCreated,
        level: 'success',
        shouldDisplayToast: true,
        actions: [
            { link: context.batch.notificationStateLink, title: 'History', icon: 'link', style: 'tertiary' },
            { link, title: 'Time trackings', icon: 'link', style: 'tertiary' },
        ],
    });
}

export async function createMultipleOperationalTrackingsFromTimeTracking(
    context: Context,
    allTrackings: TimeTracking[],
): Promise<number[]> {
    const trackingPayloads: NodeCreateData<xtremManufacturing.nodes.OperationTracking>[] = [];
    await context.batch.logMessage('info', `Creating ${allTrackings.length} trackings`);
    await asyncArray(allTrackings).forEach(async tracking => {
        const operation = await getOrCreateProductionOperation(context, tracking);
        const workOrder = await operation.workOrder;

        const currentTracking = trackingPayloads.find(payload => payload.workOrder === workOrder._id);
        const line = (currentTracking?.lines?.length || 0) + 1;

        const runTimeUnit = await tracking.runTimeUnit;
        const setupTimeUnit = await tracking.setupTimeUnit;

        const trackingLinePayload: NodeCreateData<xtremManufacturing.nodes.OperationTrackingLine> = {
            workOrderOperation: operation._id,
            line,
            completedQuantity: tracking.actualQuantity,
            actualResource: await context.read(xtremMasterData.nodes.DetailedResource, {
                _id: tracking.expectedResource._id,
            }),
            actualSetupTime: await tracking.actualSetupTime,
            actualRunTime: await tracking.actualRunTime,
            runTimeUnit,
            setupTimeUnit,
            completed: tracking.isCompleted,
            storedAttributes: (await tracking.storedAttributes) || ({} as any),
            storedDimensions: (await tracking.storedDimensions) || {},
        };

        if (!currentTracking) {
            trackingPayloads.push({
                workOrder: tracking.workOrderOperation?.workOrder?._id,
                lines: [trackingLinePayload],
            });
        } else {
            currentTracking.lines?.push(trackingLinePayload);
        }
    });
    const trackingsIds = await createAllTrackings(context, trackingPayloads);
    await notifyUserForTrackings(context, trackingsIds);

    return trackingsIds;
}

export function createMultipleOperationalTrackings(context: Context, trackings: string): Promise<number[]> {
    const allTrackings: TimeTracking[] = JSON.parse(trackings);
    return createMultipleOperationalTrackingsFromTimeTracking(context, allTrackings);
}
