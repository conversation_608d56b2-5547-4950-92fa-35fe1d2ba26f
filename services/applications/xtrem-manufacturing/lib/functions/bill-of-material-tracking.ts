import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import type { UpdateBomTrackingStatusesArgs } from '../interfaces/index';

export async function updateBomTrackingStatuses(context: Context, input: UpdateBomTrackingStatusesArgs): Promise<void> {
    const { number, workOrderStatus, stockStatus } = input;
    const billOfMaterialTrackings = await context.select(
        xtremTechnicalData.nodes.BillOfMaterialTracking,
        {
            _id: true,
            stockStatus: true,
            workOrderStatus: true,
            message: true,
        },
        {
            filter: { workOrderNumber: number },
        },
    );

    if (billOfMaterialTrackings.length > 1) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/functions__bom__tracking_more_than_one_bom_found',
                'There is more than one bill of material linked to the work order.',
            ),
        );
    }

    if (billOfMaterialTrackings.length === 1) {
        const bomTracking = billOfMaterialTrackings[0];
        const bomId = bomTracking._id;

        const trackingToUpdate = await context.read(
            xtremTechnicalData.nodes.BillOfMaterialTracking,
            {
                _id: bomId,
            },
            { forUpdate: true },
        );

        const currentStockStatus = bomTracking.stockStatus;
        const currentWorkOrderStatus = bomTracking.workOrderStatus;
        if (currentWorkOrderStatus !== workOrderStatus) {
            await trackingToUpdate?.$.set({
                workOrderStatus,
            });
        }
        if (currentStockStatus !== stockStatus) {
            await trackingToUpdate?.$.set({
                stockStatus,
            });
        }

        if (stockStatus === 'completed' && workOrderStatus === 'completed') {
            await trackingToUpdate?.$.set({
                status: 'completed',
            });
        } else {
            await trackingToUpdate?.$.set({
                status: 'error',
            });
        }

        await trackingToUpdate?.$.save();
    }
}
