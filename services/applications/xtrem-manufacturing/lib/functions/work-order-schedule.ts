import type { Context, decimal } from '@sage/xtrem-core';
import { BusinessRuleError, date, datetime, Logger } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremManufacturing from '../../index';

const logger = Logger.getLogger(__filename, 'scheduling');

async function checkWorkOrder(context: Context, workOrder: xtremManufacturing.nodes.WorkOrder, skip = false) {
    if ((await workOrder.status) === 'closed') {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__work_order_already_closed',
                'The work order {{workOrderNumber}} is already closed.',
                {
                    workOrderNumber: await workOrder.number,
                },
            ),
        );
    }
    if ((await workOrder.schedulingStatus) === 'inProgress') {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__wait_scheduling_is_done',
                'The work order {{workOrderNumber}} is currently in scheduling. Try again in a few minutes.',
                {
                    workOrderNumber: await workOrder.number,
                },
            ),
        );
    }
    if (!skip && (await workOrder.productionOperations.length) === 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/functions__work_order_scheduling__work_order_with_no_operation',
                'You can only schedule a work order that has at least one operation.',
            ),
        );
    }
    if (!skip && !(await (await workOrder.site).timeZone)) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/functions__work_order_scheduling__site_time_zone_not_defined',
                'You need to add a time zone to the {{site}} site before you can schedule work orders.',
                {
                    site: await (await workOrder.site).id,
                },
            ),
        );
    }
}

export async function forceUpdateForScheduling(workOrder: xtremManufacturing.nodes.WorkOrder) {
    await workOrder.$.set({
        forceUpdateForScheduling: true,
    });
}

function mapDate(dt: date | null, fmt?: string): string {
    return dt ? date.parse(dt.toString()).format(fmt || 'DD-MM-YYYY', undefined) : 'null';
}

export function mapDatetimeTZ(dt: datetime | null, tz?: string, fmt?: string): string {
    return dt
        ? datetime
              .parse(dt.toString(), undefined, undefined, tz || dt.timeZone)
              .format(undefined, fmt || 'DD-MM-YYYY HH:mm:ss')
        : 'null';
}

export function mapDatetimeUTC(dt: datetime | null): string {
    return dt ? dt.inTimeZone('UTC').format(undefined, 'DD-MM-YYYY HH:mm:ss') : 'null';
}

function mapDatetime(dt: datetime | null): string {
    return `${mapDatetimeTZ(dt)}`;
}

export function mapDatetimeForDebug(dt: datetime | null): string {
    return `${mapDatetimeTZ(dt)}  TZ:${dt?.timeZone} UTC:${mapDatetimeUTC(dt)}`;
}

function mapDay(dt: datetime | date): string {
    switch (dt.weekDay) {
        case 0:
            return 'Sunday';
        case 1:
            return 'Monday';
        case 2:
            return 'Tuesday';
        case 3:
            return 'Wednesday';
        case 4:
            return 'Thursday';
        case 5:
            return 'Friday';
        case 6:
            return 'Saturday';
        default:
            return '';
    }
}

function getDatetime(dat: date, time: string, tz?: string): datetime {
    return datetime.make(
        dat.year,
        dat.month,
        dat.day,
        Number(time.substring(0, 2)),
        Number(time.substring(3, 5)),
        0,
        0,
        tz,
    );
}

// return the number of seconds between 2 datetime
function secondsDiff(dt1: datetime, dt2: datetime): number {
    return dt2.millisDiff(dt1) / 1000;
}

function getDailyShift(
    weeklyShift: xtremMasterData.nodes.WeeklyShift,
    dat: date,
): Promise<xtremMasterData.nodes.DailyShift | null> | null {
    switch (dat.weekDay) {
        case 0:
            return weeklyShift.sundayShift;
        case 1:
            return weeklyShift.mondayShift;
        case 2:
            return weeklyShift.tuesdayShift;
        case 3:
            return weeklyShift.wednesdayShift;
        case 4:
            return weeklyShift.thursdayShift;
        case 5:
            return weeklyShift.fridayShift;
        case 6:
            return weeklyShift.saturdayShift;
        default:
            return null;
    }
}

// consider dailyShift for the day before refDatetime.date
// to search if its last shiftDetail overlap the night and include refDatetime
async function searchFromPreviousDailyShift(
    weeklyShift: xtremMasterData.nodes.WeeklyShift,
    refDatetime: datetime,
    duration: decimal,
): Promise<{
    start: datetime;
    end: datetime;
    duration: number;
}> {
    const start = refDatetime;
    let end = refDatetime;
    let dur = duration;

    if (!(await weeklyShift.isFullWeek)) {
        const previousDay = refDatetime.date.addDays(-1);
        const dailyShift = await getDailyShift(weeklyShift, previousDay);
        if (dailyShift && !(await dailyShift.isFullDay)) {
            const detail = await dailyShift.shiftDetails
                .sort(async (line1, line2) =>
                    (await (await line1.shiftDetail).shiftStart) > (await (await line2.shiftDetail).shiftStart)
                        ? -1
                        : 1,
                )
                .elementAt(0);
            const shiftStart = await (await detail.shiftDetail).shiftStart;
            const shiftEnd = await (await detail.shiftDetail).shiftEnd;
            if (shiftEnd < shiftStart) {
                const dt2 = getDatetime(refDatetime.date, shiftEnd, refDatetime.timeZone);
                if (refDatetime < dt2) {
                    const capacity = secondsDiff(refDatetime, dt2);
                    if (dur > capacity) {
                        end = dt2;
                        dur -= capacity;
                    } else {
                        end = refDatetime.addSeconds(dur);
                        dur = 0;
                    }
                    // logger
                    await logger.verboseAsync(
                        async () =>
                            `CC1 previousDay(${mapDay(previousDay)})=${mapDate(
                                previousDay,
                            )} dailyShift=${await dailyShift.id} => start=${mapDatetime(start)} end=${mapDatetime(
                                end,
                            )} dur=${dur}`,
                    );
                    //
                }
            }
        }
    }

    if (dur === duration) {
        // logger
        logger.verbose(() => `CC2 start=${mapDatetime(start)} end=${mapDatetime(end)} dur=${dur}`);
        //
    }
    return { start, end, duration: dur };
}

// for the input dailyShift
// - search first shiftDetail that includes refDatetime or that starts after it
// - search the end datetime reached from refDatetime till sum of its shiftDetails duration equals to the input duration
//   (shiftDetails may be considered partially)
// - return start/end datetime and remaining duration
async function searchFromDailyShift(
    dailyShift: xtremMasterData.nodes.DailyShift | null,
    refDatetime: datetime,
    duration: number,
): Promise<{ start: datetime | null; end: datetime | null; dur: number }> {
    let start: datetime | null = null;
    let end: datetime | null = null;
    let capacity = 0;
    let dur: number = duration;

    if (dailyShift) {
        await dailyShift.shiftDetails
            .sort(async (line1, line2) =>
                (await (await line1.shiftDetail).shiftStart) > (await (await line2.shiftDetail).shiftStart) ? 1 : -1,
            )
            .some(async detail => {
                const shiftStart = await (await detail.shiftDetail).shiftStart;
                const shiftEnd = await (await detail.shiftDetail).shiftEnd;
                const nextDay = shiftEnd < shiftStart ? 1 : 0;
                const dt2 = getDatetime(refDatetime.date.addDays(nextDay), shiftEnd, refDatetime.timeZone);
                if (refDatetime < dt2) {
                    const dt1 = getDatetime(refDatetime.date, shiftStart, refDatetime.timeZone);
                    const dt0 = dt1 < refDatetime ? refDatetime : dt1;
                    if (!start) {
                        start = dt0;
                    }
                    capacity = secondsDiff(dt0, dt2);
                    if (dur > capacity) {
                        end = dt2;
                        dur -= capacity;
                    } else {
                        end = dt0.addSeconds(dur);
                        dur = 0;
                        return true;
                    }
                }
                return false;
            });
    } else {
        // full day
        start = refDatetime;
        capacity = secondsDiff(refDatetime, getDatetime(refDatetime.date.addDays(1), '00:00', refDatetime.timeZone));
        if (dur > capacity) {
            dur -= capacity;
        } else {
            end = start.addSeconds(dur);
            dur = 0;
        }
    }

    // logger
    await logger.verboseAsync(
        async () =>
            `AA1 refDatetime=${mapDatetime(refDatetime)} dailyShift=${await dailyShift?.id}  start=${mapDatetime(
                start,
            )} end=${mapDatetime(end)} dur=${dur}`,
    );
    //
    return { start, end, dur };
}

// returns a start/end datetime for the weekShift of a resource from refDatetime till sum of its shiftDetails duration equals to the input duration
// (shiftDetails may be considered partially)
async function schedule(
    weeklyShift: xtremMasterData.nodes.WeeklyShift,
    refDatetime: datetime,
    duration: decimal,
): Promise<{
    start: datetime;
    end: datetime;
}> {
    // logger
    await logger.verboseAsync(
        async () =>
            `DD1 weeklyShift=${await weeklyShift.id} refDatetime(${mapDay(refDatetime)})=${mapDatetime(
                refDatetime,
            )} duration=${duration}`,
    );
    //

    let { start, end, duration: dur } = await searchFromPreviousDailyShift(weeklyShift, refDatetime, duration);
    let capacity: number;
    let dailyShift;
    let startFound = dur !== duration;
    //
    logger.verbose(
        () => `DD2 startFound=${startFound} start=${mapDatetime(start)} -> end=${mapDatetime(end)} dur=${dur}`,
    );
    //

    while (dur > 0) {
        // capacity // 2 full day // 1 working day // 0 day off
        if (await weeklyShift.isFullWeek) {
            dailyShift = null;
            capacity = 2;
        } else {
            dailyShift = await getDailyShift(weeklyShift, end.date);
            capacity = dailyShift ? 1 : 0;
        }
        // logger
        const loggerDailyShift = await dailyShift?.id;
        const loggerDate = end.date;
        const loggerCapacity = capacity;
        let loggerDur = dur;
        logger.verbose(
            () =>
                `DD3 refDate(${mapDay(loggerDate)})=${mapDate(
                    loggerDate,
                )} dailyShift=${loggerDailyShift} capacity=${loggerCapacity} dur=${loggerDur}`,
        );
        //

        if (capacity !== 0) {
            const result = await searchFromDailyShift(dailyShift, end, dur);
            if (result.start && result.end) {
                if (!startFound) {
                    startFound = true;
                    start = result.start;
                    //
                    const loggerStart = start;
                    logger.verbose(() => `DD4 startFound start=${mapDatetime(loggerStart)}`);
                    //
                }
                dur = result.dur;
                if (dur === 0) end = result.end;
                // logger
                loggerDur = dur;
                logger.verbose(() => `DD5 dur=${loggerDur}`);
                //
            }
        }

        if (dur > 0) {
            end = getDatetime(end.date.addDays(1), '00:00', end.timeZone);
            if (!startFound) start = end;
        }
    }

    return { start, end };
}

// schedule a resource from input refDatetime
async function scheduleResource(
    workOrderResource: xtremManufacturing.nodes.WorkOrderOperationResource,
    refDatetime: datetime,
): Promise<{
    start: datetime | null;
    end: datetime | null;
}> {
    let period: { start: datetime | null; end: datetime | null } = { start: null, end: null };
    // logger
    await logger.verboseAsync(
        async () =>
            `EE1 WOOR (start) ${await (
                await workOrderResource.resource
            ).name}: ${await workOrderResource.status} refDatetime=${mapDatetime(refDatetime)}`,
    );
    //
    if ((await workOrderResource.status) !== 'excluded') {
        const duration =
            (await xtremMasterData.functions.convertToSeconds(
                await workOrderResource.runTimeUnit,
                await workOrderResource.expectedRunTime,
            )) +
            (await xtremMasterData.functions.convertToSeconds(
                await workOrderResource.setupTimeUnit,
                await workOrderResource.expectedSetupTime,
            ));

        period = await schedule(await (await workOrderResource.resource).weeklyShift, refDatetime, duration);
    }

    await workOrderResource.$.set({ startDatetime: period.start, endDatetime: period.end });

    // logger
    await logger.verboseAsync(
        async () =>
            `EE2 WOOR (end) ${await (await workOrderResource.resource).name}: ${mapDatetime(
                period.start,
            )} -> ${mapDatetime(period.end)}`,
    );
    //
    return { start: period.start, end: period.end };
}

// schedule an operation from input refDatetime
// its resources are scheduled in parallel from input refDatetime
async function scheduleOperation(
    operation: xtremManufacturing.nodes.WorkOrderOperation,
    refDatetime: datetime,
): Promise<{
    start: datetime | null;
    end: datetime | null;
}> {
    let opeStart = null as datetime | null;
    let opeEnd = null as datetime | null;
    // logger
    await logger.verboseAsync(
        async () => `FF1 WOO (start) ${await operation.operationNumber}: refDatetime=${mapDatetime(refDatetime)}`,
    );
    //
    await operation.resources.forEach(async resource => {
        const result: { start: datetime | null; end: datetime | null } = await scheduleResource(resource, refDatetime);
        if (result.start && (!opeStart || result.start.compare(opeStart) < 0)) {
            opeStart = result.start;
        }
        if (result.end && (!opeEnd || result.end.compare(opeEnd) > 0)) {
            opeEnd = result.end;
        }
    });

    await operation.$.set({
        startDatetime: opeStart,
        endDatetime: opeEnd,
        startDate: opeStart?.date || null,
        endDate: opeEnd?.date || null,
    });
    // logger
    await logger.verboseAsync(
        async () =>
            `FF2 WO (end) ${await operation.operationNumber}: ${mapDatetime(opeStart)} -> ${mapDatetime(opeEnd)}`,
    );
    //
    return { start: opeStart, end: opeEnd };
}

// schedule a work order with all operations successive to one another
export async function scheduleWorkOrder(
    context: Context,
    workOrder: xtremManufacturing.nodes.WorkOrder,
): Promise<xtremManufacturing.nodes.WorkOrder> {
    await checkWorkOrder(context, workOrder);

    await forceUpdateForScheduling(workOrder);
    const refDate = await workOrder.requestedDate;
    const timeZone = await (await workOrder.site).timeZone;
    const refDatetime = getDatetime(refDate, '00:00', timeZone);
    let woStart: datetime | null = null;
    let woEnd: datetime | null = null;
    // logger
    await logger.verboseAsync(
        async () =>
            `============= ${
                (await workOrder.isForwardScheduling) ? 'Forward' : 'Backward'
            } scheduling (start) ===============`,
    );
    await logger.verboseAsync(
        async () =>
            `GG1 WO (start) ${await workOrder.number}: TZ=${refDatetime.timeZone} refDate=${mapDate(
                await workOrder.requestedDate,
            )} ${mapDate(await workOrder.startDate)} -> ${mapDate(await workOrder.endDate)}`,
    );
    //
    await workOrder.productionOperations
        .filter(async ope => (await ope.status) !== 'included')
        .sort(async (ope1, ope2) => ((await ope1.operationNumber) > (await ope2.operationNumber) ? 1 : -1))
        .forEach(async operation => {
            const result: { start: datetime | null; end: datetime | null } = await scheduleOperation(
                operation,
                woEnd || refDatetime,
            );
            if (result.start && (!woStart || result.start.compare(woStart) < 0)) {
                woStart = result.start;
            }
            if (result.end && (!woEnd || result.end.compare(woEnd) > 0)) {
                woEnd = result.end;
            }
        });
    // logger
    await logger.verboseAsync(
        async () =>
            `GG2 WO (end) ${await workOrder.number}: scheduled ${mapDatetime(woStart || refDatetime)} -> ${mapDatetime(
                woEnd || refDatetime,
            )}`,
    );
    //
    if (!woStart) woStart = refDatetime;
    if (!woEnd) woEnd = refDatetime;
    await workOrder.$.set({
        startDatetime: woStart,
        endDatetime: woEnd,
        startDate: woStart.date,
        endDate: woEnd.date,
        timeZone,
        schedulingStatus: 'scheduled',
    });
    await workOrder.$.save();
    // logger
    logger.verbose(() => `============= scheduling (end) ===============`);
    //
    return workOrder;
}

export async function skipSchedulingWorkOrder(
    context: Context,
    workOrder: xtremManufacturing.nodes.WorkOrder,
    skip = true,
): Promise<xtremManufacturing.nodes.WorkOrder> {
    await checkWorkOrder(context, workOrder, skip);

    if (!(!skip && ['scheduled', 'toReschedule'].includes(await workOrder.schedulingStatus))) {
        await forceUpdateForScheduling(workOrder);
        if (skip) {
            // not reset work order to notScheduled
            const woStart = await workOrder.startDate;
            const woEnd = await workOrder.endDate;
            const woId = workOrder._id;
            await context.bulkUpdate(xtremManufacturing.nodes.WorkOrderOperation, {
                set: { startDatetime: null, startDate: null, endDatetime: null, endDate: null },
                where: { workOrder: woId },
            });
            await context.bulkUpdate(xtremManufacturing.nodes.WorkOrderOperationResource, {
                set: { startDatetime: null, endDatetime: null },
                where: { workOrderOperation: { workOrder: woId } },
            });
            await workOrder.$.set({ startDatetime: null, startDate: woStart, endDatetime: null, endDate: woEnd });
        }
        await workOrder.$.set({
            isSchedulingSkipped: skip,
            schedulingStatus: skip ? 'notManaged' : 'notScheduled',
            timeZone: '',
        });
        await workOrder.$.save();
    }
    return workOrder;
}
