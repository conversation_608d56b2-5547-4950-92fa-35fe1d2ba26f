import type { Collection, decimal } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremManufacturing from '../index';

export function calculateCost(
    expected: boolean,
    setup: boolean,
    resourceType: xtremMasterData.enums.ResourceGroupType,
    resourceGroups: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>,
): Promise<decimal> {
    return resourceGroups
        .filter(async operationResource => {
            let result = false;
            if ((await operationResource.resource) instanceof xtremMasterData.nodes.GroupResource) {
                const groupResource = (await operationResource.resource) as xtremMasterData.nodes.GroupResource;
                result = (await groupResource.type) === resourceType;
            } else if (resourceType === 'labor') {
                result = (await operationResource.resource) instanceof xtremMasterData.nodes.LaborResource;
            } else if (resourceType === 'machine') {
                result = (await operationResource.resource) instanceof xtremMasterData.nodes.MachineResource;
            } else {
                result = (await operationResource.resource) instanceof xtremMasterData.nodes.ToolResource;
            }
            return result;
        })
        .sum(operationResource => {
            if (expected) {
                return setup ? operationResource.expectedSetupCost : operationResource.expectedRunCost;
            }
            return setup ? operationResource.actualSetupCost : operationResource.actualRunCost;
        });
}

export function calculateActualMachineRunCost(
    resourceGroups: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>,
): Promise<decimal> {
    return calculateCost(false, false, 'machine', resourceGroups);
}

export function calculateActualLaborRunCost(
    resourceGroups: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>,
): Promise<decimal> {
    return calculateCost(false, false, 'labor', resourceGroups);
}

export function calculateActualToolRunCost(
    resourceGroups: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>,
): Promise<decimal> {
    return calculateCost(false, false, 'tool', resourceGroups);
}

export function calculateActualMachineSetupCost(
    resourceGroups: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>,
): Promise<decimal> {
    return calculateCost(false, true, 'machine', resourceGroups);
}

export function calculateActualLaborSetupCost(
    resourceGroups: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>,
): Promise<decimal> {
    return calculateCost(false, true, 'labor', resourceGroups);
}

export function calculateActualToolSetupCost(
    resourceGroups: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>,
): Promise<decimal> {
    return calculateCost(false, true, 'tool', resourceGroups);
}

export function calculateExpectedMachineRunCost(
    resourceGroups: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>,
): Promise<decimal> {
    return calculateCost(true, false, 'machine', resourceGroups);
}

export function calculateExpectedLaborRunCost(
    resourceGroups: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>,
): Promise<decimal> {
    return calculateCost(true, false, 'labor', resourceGroups);
}

export function calculateExpectedToolRunCost(
    resourceGroups: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>,
): Promise<decimal> {
    return calculateCost(true, false, 'tool', resourceGroups);
}

export function calculateExpectedMachineSetupCost(
    resourceGroups: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>,
): Promise<decimal> {
    return calculateCost(true, true, 'machine', resourceGroups);
}

export function calculateExpectedLaborSetupCost(
    resourceGroups: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>,
): Promise<decimal> {
    return calculateCost(true, true, 'labor', resourceGroups);
}

export function calculateExpectedToolSetupCost(
    resourceGroups: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>,
): Promise<decimal> {
    return calculateCost(true, true, 'tool', resourceGroups);
}

export async function actualResourceType(
    actualResource: xtremMasterData.nodes.DetailedResource,
    groupResource: xtremMasterData.nodes.GroupResource | null,
): Promise<string> {
    if (groupResource && (await groupResource.type)) {
        if (['labor', 'machine', 'tool'].includes(await groupResource.type)) {
            return groupResource.type;
        }
    }

    switch (actualResource.constructor.name) {
        case 'LaborResource':
            return 'labor';
        case 'MachineResource':
            return 'machine';
        case 'ToolResource':
            return 'tool';
        default: {
            break;
        }
    }

    return '';
}
