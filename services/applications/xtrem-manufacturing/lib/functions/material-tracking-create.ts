import type { Context, Logger, decimal, integer } from '@sage/xtrem-core';
import { BusinessRuleError, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremManufacturing from '../../index';

/**
 *  Check
 * - workOrderTrackingNumber empty
 * - quantity less or equal than zero
 * - componentNumber, less than zero  & if it exist in the workOrder
 * @param context
 * @param workOrder
 * @param quantity
 * @param workOrderTrackingNumber
 * @param componentNumber
 */
export async function validateMaterialTrackingParameters(
    context: Context,
    workOrder: xtremManufacturing.nodes.WorkOrder,
    quantity: decimal,
    workOrderTrackingNumber: string,
    componentNumber?: integer,
): Promise<void> {
    if (!workOrder) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/functions__tracking__work_order_missing',
                'You need to add a work order.',
            ),
        );
    }
    const messages = [];
    if (workOrderTrackingNumber === '') {
        messages.push(
            context.localize(
                '@sage/xtrem-manufacturing/functions__tracking__tracking_number_missing',
                'Work order tracking number missing.',
            ),
        );
    }
    if (quantity <= 0) {
        messages.push(
            context.localize(
                '@sage/xtrem-manufacturing/functions__tracking__release_quantity_invalid',
                "Release quantity '{{quantity}}' is invalid.",
                { quantity },
            ),
        );
    }
    if (componentNumber) {
        if (componentNumber <= 0) {
            messages.push(
                context.localize(
                    '@sage/xtrem-manufacturing/functions__material_tracking__component_number_invalid',
                    "Component number '{{componentNumber}}' is invalid.",
                    { componentNumber },
                ),
            );
        } else if (
            !(await workOrder.productionComponents.some(
                async (component: xtremManufacturing.nodes.WorkOrderComponent) => {
                    return (await component.componentNumber) === componentNumber;
                },
            ))
        ) {
            messages.push(
                context.localize(
                    '@sage/xtrem-manufacturing/functions__material_tracking__component_not_on_work_order',
                    "Component number '{{componentNumber}}' does not exist on work order.",
                    { componentNumber },
                ),
            );
        }
    }
    if (messages.length > 0) {
        throw new BusinessRuleError(messages.join('\n'));
    }
}

/**
 *  validate :
 *  - data.length more than 0
 * @param context
 * @param data to validate
 */
export function validateMultipleMaterialTrackingParameters(
    context: Context,
    data: xtremManufacturing.interfaces.MultipleMaterialTracking[],
): void {
    if (!data.length) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-manufacturing/no_line_in_the_material_tracking',
                'There is no line in this material tracking.',
            ),
        );
    }
}
/**
 * From the tracking add a new component if no componentNumber
 * @param tracking
 * @param workOrder
 * @returns WorkOrderComponents array
 */
export async function getWorkOrderComponentArray(
    context: Context,
    tracking: xtremManufacturing.interfaces.MultipleMaterialTracking,
    logger: Logger,
): Promise<xtremManufacturing.nodes.WorkOrderComponent[]> {
    const workOrder = await context.read(
        xtremManufacturing.nodes.WorkOrder,
        {
            _id: tracking.workOrder._id,
        },
        // No need to update if we have already a componentNumber
        { forUpdate: !tracking.componentNumber },
    );

    let componentNumber = 0;
    if (!tracking.componentNumber) {
        // If no componentNumber (WorkOrderComponent) we add it to the workOrder
        componentNumber =
            (await workOrder.productionComponents.length) > 0
                ? 10 + Math.max(...(await workOrder.productionComponents.map(c => c.componentNumber).toArray()))
                : 10;
        await workOrder.productionComponents.append({
            componentNumber,
            item: tracking.item._id,
            lineStatus: 'included',
            lineType: 'normal',
            linkQuantity: tracking.remainingQuantity,
            name: tracking.name,
            requiredQuantity: tracking.remainingQuantity,
            requiredDate: tracking.date || date.today(),
            unit: (await tracking.item.stockUnit)._id,
            isFixedLinkQuantity: true,
        });
        await workOrder.$.save();
    } else {
        componentNumber = tracking.componentNumber;
    }

    const workOrderComponentArray = await workOrder.productionComponents
        .filter(async component => (await component.componentNumber) === componentNumber)
        .toArray();

    if (workOrderComponentArray.length > 1) {
        // As we use only the first of the  component workOrder : add a warning
        logger.warn(
            () => `getWorkOrderComponentArray : ${workOrderComponentArray.length} lines for productionComponents `,
        );
    }
    return workOrderComponentArray;
}

export async function controlCreateMaterialTracking(
    context: Context,
    data: xtremManufacturing.interfaces.SingleMaterialTracking,
): Promise<string> {
    const { component } = data;
    const componentItem = await component.item;

    if ((await (await (await component.workOrder).bomCode)?.status) === 'inDevelopment') {
        return context.localize(
            '@sage/xtrem-manufacturing/nodes__material_tracking__work_order_with_incorrect_bom',
            'Work order {{workOrderNumber}}: No material tracking generated for a BOM that is in development.',
            {
                workOrderNumber: await (await component.workOrder).number,
            },
        );
    }

    if (await context.isServiceOptionEnabled(xtremMasterData.serviceOptions.phantomItemOption)) {
        if (componentItem && (await componentItem.isPhantom) && (await component.lineStatus) !== 'excluded') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-manufacturing/nodes__material_tracking__phantom_item_not_allowed',
                    'Phantom components are not allowed in material tracking.',
                ),
            );
        }
    }

    // XT-33347 Material tracking creation cannot be created automatically for serial number managed items
    //          coming from production tracking using automatic material tracking.
    if (
        data.isMaterialTrackingCreationAllowedForSerialNumberManagedItems !== undefined &&
        (await context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption)) &&
        (await componentItem?.serialNumberManagement) === 'managed'
    ) {
        return context.localize(
            '@sage/xtrem-manufacturing/nodes__manufacturing_tracking__no_automatic_material_tracking_for_serialized_item',
            'No automatic material tracking generated for work order {{workOrderNumber}} serialized item {{itemId}} {{itemName}}.',
            {
                workOrderNumber: await (await component.workOrder).number,
                itemId: await componentItem?.id,
                itemName: await componentItem?.name,
            },
        );
    }

    return '';
}
