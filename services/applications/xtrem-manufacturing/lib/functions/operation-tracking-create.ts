import type { Context, decimal, integer } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import type * as xtremManufacturing from '../../index';

export async function validateOperationTrackingParameters(
    context: Context,
    workOrder: xtremManufacturing.nodes.WorkOrder,
    quantity: decimal,
    workOrderTrackingNumber: string,
    operationNumber?: integer,
): Promise<void> {
    const messages = [];
    if (workOrderTrackingNumber === '') {
        messages.push(
            context.localize(
                '@sage/xtrem-manufacturing/functions__tracking__tracking_number_missing',
                'Work order tracking number missing.',
            ),
        );
    }
    if (quantity <= 0) {
        messages.push(
            context.localize(
                '@sage/xtrem-manufacturing/functions__tracking__release_quantity_invalid',
                "Release quantity '{{quantity}}' is invalid.",
                { quantity },
            ),
        );
    }
    if (operationNumber) {
        if (operationNumber <= 0) {
            messages.push(
                context.localize(
                    '@sage/xtrem-manufacturing/functions__operation_tracking__operation_number_invalid',
                    "Operation number '{{operationNumber}}' is invalid.",
                    { operationNumber },
                ),
            );
        } else if (
            !(await workOrder.productionOperations.find(
                async (operation: xtremManufacturing.nodes.WorkOrderOperation) => {
                    return (await operation.operationNumber) === operationNumber;
                },
            ))
        ) {
            messages.push(
                context.localize(
                    '@sage/xtrem-manufacturing/functions__operation_tracking__operation_not_on_work_order',
                    "Operation number '{{operationNumber}}' does not exist on work order.",
                    { operationNumber },
                ),
            );
        }
    }
    if (messages.length > 0) {
        throw new BusinessRuleError(messages.join(' '));
    }
}
