import { EnumDataType } from '@sage/xtrem-core';
import { WorkOrderFilteredTypeEnum } from './index';

export enum WorkOrderTypeEnum {
    planned = WorkOrderFilteredTypeEnum.planned,
    firm = WorkOrderFilteredTypeEnum.firm,
}

export type WorkOrderType = keyof typeof WorkOrderTypeEnum;

export const workOrderTypeDataType = new EnumDataType<WorkOrderType>({ enum: WorkOrderTypeEnum, filename: __filename });
