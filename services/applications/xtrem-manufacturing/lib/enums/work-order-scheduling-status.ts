import { EnumDataType } from '@sage/xtrem-core';

export enum WorkOrderSchedulingStatusEnum {
    notScheduled = 1,
    inProgress = 2,
    scheduled = 3,
    toReschedule = 4,
    notManaged = 5,
}

export type WorkOrderSchedulingStatus = keyof typeof WorkOrderSchedulingStatusEnum;

export const WorkOrderSchedulingStatusDataType = new EnumDataType<WorkOrderSchedulingStatus>({
    enum: WorkOrderSchedulingStatusEnum,
    filename: __filename,
});
