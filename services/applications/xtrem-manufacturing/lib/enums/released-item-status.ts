import { EnumDataType } from '@sage/xtrem-core';
import { WorkOrderLineStatusFilteredEnum } from './work-order-line-status-filtered';
import { WorkOrderLineStatusOtherEnum } from './work-order-line-status-other';

export enum ReleasedItemStatusEnum {
    pending = WorkOrderLineStatusFilteredEnum.pending,
    inProgress = WorkOrderLineStatusFilteredEnum.inProgress,
    completed = WorkOrderLineStatusOtherEnum.completed,
    excluded = WorkOrderLineStatusOtherEnum.excluded,
    included = WorkOrderLineStatusFilteredEnum.included,
}

export type ReleasedItemStatus = keyof typeof ReleasedItemStatusEnum;

export const releasedItemStatusDataType = new EnumDataType<ReleasedItemStatus>({
    enum: ReleasedItemStatusEnum,
    filename: __filename,
});
