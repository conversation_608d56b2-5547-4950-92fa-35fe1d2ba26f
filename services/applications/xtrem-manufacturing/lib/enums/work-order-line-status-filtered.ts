import { EnumDataType } from '@sage/xtrem-core';

export enum WorkOrderLineStatusFilteredEnum {
    pending = 1,
    inProgress = 2,
    included = 5,
}

export type WorkOrderLineStatusFiltered = keyof typeof WorkOrderLineStatusFilteredEnum;

export const workOrderLineStatusFilteredDataType = new EnumDataType<WorkOrderLineStatusFiltered>({
    enum: WorkOrderLineStatusFilteredEnum,
    filename: __filename,
});
