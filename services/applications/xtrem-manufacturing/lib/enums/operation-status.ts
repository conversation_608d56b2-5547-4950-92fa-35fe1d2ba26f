import { EnumDataType } from '@sage/xtrem-core';
import { WorkOrderLineStatusFilteredEnum } from './work-order-line-status-filtered';
import { WorkOrderLineStatusOtherEnum } from './work-order-line-status-other';

export enum OperationStatusEnum {
    pending = WorkOrderLineStatusFilteredEnum.pending,
    inProgress = WorkOrderLineStatusFilteredEnum.inProgress,
    completed = WorkOrderLineStatusOtherEnum.completed,
    excluded = WorkOrderLineStatusOtherEnum.excluded,
    included = WorkOrderLineStatusFilteredEnum.included,
}

export type OperationStatus = keyof typeof OperationStatusEnum;

export const operationStatusDataType = new EnumDataType<OperationStatus>({
    enum: OperationStatusEnum,
    filename: __filename,
});
