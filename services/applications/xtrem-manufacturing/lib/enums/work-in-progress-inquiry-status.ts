import { EnumDataType } from '@sage/xtrem-core';

export enum WorkInProgressInquiryStatusEnum {
    inProgress,
    completed,
    draft,
    error,
}

export type WorkInProgressInquiryStatus = keyof typeof WorkInProgressInquiryStatusEnum;

export const workInProgressInquiryStatusDataType = new EnumDataType<WorkInProgressInquiryStatus>({
    enum: WorkInProgressInquiryStatusEnum,
    filename: __filename,
});
