import { EnumDataType } from '@sage/xtrem-core';

export enum WorkInProgressTypeEnum {
    materialTracking = 1,
    setupTimeTracking,
    runTimeTracking,
    productionTracking,
    workOrderIndirectCost,
    workOrderVariance,
    workOrderNegativeVariance,
    workOrderActualCostAdjustment,
    workOrderNegativeActualCostAdjustment,
    workOrderActualCostAdjustmentNonAbsorbed,
    workOrderNegativeActualCostAdjustmentNonAbsorbed,
}

export type WorkInProgressType = keyof typeof WorkInProgressTypeEnum;

export const WorkInProgressTypeDataType = new EnumDataType<WorkInProgressType>({
    enum: WorkInProgressTypeEnum,
    filename: __filename,
});
