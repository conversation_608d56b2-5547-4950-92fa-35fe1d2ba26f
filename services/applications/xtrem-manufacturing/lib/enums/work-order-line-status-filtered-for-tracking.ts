import { EnumDataType } from '@sage/xtrem-core';
import { WorkOrderLineStatusFilteredEnum } from './work-order-line-status-filtered';
import { WorkOrderLineStatusOtherEnum } from './work-order-line-status-other';

export enum WorkOrderLineStatusFilteredForTrackingEnum {
    pending = WorkOrderLineStatusFilteredEnum.pending,
    inProgress = WorkOrderLineStatusFilteredEnum.inProgress,
    completed = WorkOrderLineStatusOtherEnum.completed,
}

export type WorkOrderLineStatusFilteredForTracking = keyof typeof WorkOrderLineStatusFilteredForTrackingEnum;

export const workOrderLineStatusFilteredForTrackingDataType = new EnumDataType<WorkOrderLineStatusFilteredForTracking>({
    enum: WorkOrderLineStatusFilteredForTrackingEnum,
    filename: __filename,
});
