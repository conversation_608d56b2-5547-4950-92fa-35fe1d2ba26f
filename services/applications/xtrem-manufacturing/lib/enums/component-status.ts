import { EnumDataType } from '@sage/xtrem-core';
import { WorkOrderLineStatusFilteredEnum } from './work-order-line-status-filtered';
import { WorkOrderLineStatusOtherEnum } from './work-order-line-status-other';

export enum ComponentStatusEnum {
    pending = WorkOrderLineStatusFilteredEnum.pending,
    inProgress = WorkOrderLineStatusFilteredEnum.inProgress,
    completed = WorkOrderLineStatusOtherEnum.completed,
    excluded = WorkOrderLineStatusOtherEnum.excluded,
    included = WorkOrderLineStatusFilteredEnum.included,
}

export type ComponentStatus = keyof typeof ComponentStatusEnum;

export const componentStatusDataType = new EnumDataType<ComponentStatus>({
    enum: ComponentStatusEnum,
    filename: __filename,
});
