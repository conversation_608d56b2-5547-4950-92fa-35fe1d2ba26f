import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import * as xtremManufacturing from '../../index';

@decorators.nodeExtension<BillOfMaterialExtension>({
    extends: () => xtremTechnicalData.nodes.BillOfMaterial,
})
export class BillOfMaterialExtension extends NodeExtension<xtremTechnicalData.nodes.BillOfMaterial> {
    @decorators.referenceProperty<BillOfMaterialExtension, 'workOrderCategory'>({
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrderCategory,
    })
    readonly workOrderCategory: Promise<xtremManufacturing.nodes.WorkOrderCategory>;
}

declare module '@sage/xtrem-technical-data/lib/nodes/bill-of-material' {
    export interface BillOfMaterial extends BillOfMaterialExtension {}
}
