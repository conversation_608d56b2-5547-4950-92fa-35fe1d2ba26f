import type { Collection } from '@sage/xtrem-core';
import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremManufacturing from '../../index';

@decorators.nodeExtension<BaseResourceExtension>({
    extends: () => xtremMasterData.nodes.BaseResource,
})
export class BaseResourceExtension extends NodeExtension<xtremMasterData.nodes.BaseResource> {
    @decorators.collectionProperty<BaseResourceExtension, 'operationResources'>({
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrderOperationResource,
        reverseReference: 'resource',
    })
    readonly operationResources: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>;
}

declare module '@sage/xtrem-master-data/lib/nodes/base-resource' {
    export interface BaseResource extends BaseResourceExtension {}
}
