import type { Reference } from '@sage/xtrem-core';
import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremManufacturing from '../../index';

@decorators.nodeExtension<StockJournalExtension>({
    extends: () => xtremStockData.nodes.StockJournal,
})
export class StockJournalExtension extends NodeExtension<xtremStockData.nodes.StockJournal> {
    @decorators.referenceProperty<StockJournalExtension, 'materialTrackingLine'>({
        isPublished: true,
        dependsOn: ['documentLine'],
        node: () => xtremManufacturing.nodes.MaterialTrackingLine,
        isNullable: true,
        join: {
            async _id() {
                return (await this.documentLine)._id;
            },
        },
    })
    readonly materialTrackingLine: Reference<xtremManufacturing.nodes.MaterialTrackingLine> | null;

    @decorators.referenceProperty<StockJournalExtension, 'productionTrackingLine'>({
        isPublished: true,
        dependsOn: ['documentLine'],
        node: () => xtremManufacturing.nodes.ProductionTrackingLine,
        isNullable: true,
        join: {
            async _id() {
                return (await this.documentLine)._id;
            },
        },
    })
    readonly productionTrackingLine: Reference<xtremManufacturing.nodes.ProductionTrackingLine> | null;

    @decorators.referenceProperty<StockJournalExtension, 'workOrder'>({
        isPublished: true,
        dependsOn: ['materialTrackingLine', 'productionTrackingLine'],
        node: () => xtremManufacturing.nodes.WorkOrder,
        isNullable: true,
        async getValue() {
            return (
                (await (await this.materialTrackingLine)?.workOrderLine)?.workOrder ??
                (await (await this.productionTrackingLine)?.workOrderLine)?.document ??
                null
            );
        },
    })
    readonly workOrder: Reference<xtremManufacturing.nodes.WorkOrder> | null;
}

declare module '@sage/xtrem-stock-data/lib/nodes/stock-journal' {
    export interface StockJournal extends StockJournalExtension {}
}
