import type { decimal, ValidationContext } from '@sage/xtrem-core';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import type * as xtremManufacturing from '../../index';

/**
 * Validates the value passed is a positive number given the component line type (normal or text)
 * @param cx - validation context
 * @param lineType - component line type (normal or text)
 * @param val - object being validated
 */
export async function componentLinePositiveNumberRequired(
    cx: ValidationContext,
    lineType: xtremTechnicalData.enums.BomLineType,
    val: decimal,
): Promise<void> {
    if (lineType === 'normal') {
        await cx.error.if(val).is.less.than(0);
    } else {
        await cx.error.if(val).is.not.zero();
    }
}

export async function workOrderComponentItemControl(
    cx: ValidationContext,
    args: {
        lineType: Awaited<xtremManufacturing.nodes.WorkOrderComponent['lineType']>;
        lineStatus: Awaited<xtremManufacturing.nodes.WorkOrderComponent['lineStatus']>;
        item: Awaited<xtremManufacturing.nodes.WorkOrderComponent['item']>;
    },
) {
    xtremTechnicalData.events.control.componentLineControls.componentLinePropertyRequired(
        cx,
        args.lineType,
        args.item,
        'Item',
    );
    // In a first step, we allow phantom if they are excluded. It will easy the migration for customers.
    // isPhantom filter should be added in the filter of WorkOrderComponent.item and the lineStatus should no longer be tested.
    // It has to be confirmed with the PM
    if (args.item && (await args.item.isPhantom) && args.lineStatus !== 'excluded') {
        cx.error.addLocalized(
            '@sage/xtrem-manufacturing/events/control__component__item_cannot_be_a_phantom',
            'Phantom items cannot be added to a work order.',
        );
    }
}
