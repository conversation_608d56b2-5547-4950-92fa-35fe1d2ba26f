import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremManufacturing from '../../index';

/**
 * Validates the item for a work order released item
 * @param cx - validation context
 * @param item - item being validated
 */
export async function workOrderReleasedItemControl(
    cx: ValidationContext,
    item: Awaited<xtremManufacturing.nodes.WorkOrderReleasedItem['item']>,
): Promise<void> {
    if (!(await item.isStockManaged) || !(await item.isManufactured)) {
        cx.error.addLocalized(
            '@sage/xtrem-manufacturing/nodes__work_order_released_item__released_item_must_be_stock_managed_and_manufactured',
            'The released item needs to be stock managed and manufactured.',
        );
    }
    if (await item.isPhantom) {
        cx.error.addLocalized(
            '@sage/xtrem-manufacturing/nodes__work_order_released_item__phantom_item_not_allowed',
            'You can only add a stock item to a work order, not a phantom item.',
        );
    }
}
