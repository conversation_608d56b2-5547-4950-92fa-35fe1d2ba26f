import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremManufacturing from '../../index';

/**
 * Check the BOM revision is available to use. If no revision, the BOM code must be available to use.
 */
async function bomOrRevisionStatusControl(
    cx: ValidationContext,
    args: {
        bomCode: Awaited<xtremManufacturing.nodes.WorkOrder['bomCode']>;
        bomRevision: Awaited<xtremManufacturing.nodes.WorkOrder['bomRevision']>;
        workOrder: xtremManufacturing.nodes.WorkOrder;
    },
) {
    const { bomCode, bomRevision, workOrder } = args;
    if (!bomCode) return;

    // Only proceed for newly added work orders or modified ones with no previous BOM code
    const isNewWorkOrder = workOrder.$.status === NodeStatus.added;
    const isModifiedWithoutPreviousBom =
        workOrder.$.status === NodeStatus.modified && !(await (await workOrder.$.old).bomCode);

    if (!isNewWorkOrder && !isModifiedWithoutPreviousBom) {
        return;
    }

    if (bomRevision) {
        await cx.error
            .withMessage(
                '@sage/xtrem-manufacturing/nodes__work-order__bom_revision_invalid',
                'You can only add a BOM revision that is available to use to a work order.',
            )
            .if((await bomRevision.status) !== 'availableToUse')
            .is.true();
    } else {
        await cx.error
            .withMessage(
                '@sage/xtrem-manufacturing/nodes__work-order__bom_code_invalid',
                'You can only add a BOM code that is available to use to a work order.',
            )
            .if((await bomCode.status) !== 'availableToUse')
            .is.true();
    }
}

/**
 * Validates the item for a work order released item
 * @param cx - validation context
 * @param args
 * @param args.bomCode - bom code being validated
 * @param args.workOrder - work order getting the value of bomCode
 */
export async function workOrderBomCodeControl(
    cx: ValidationContext,
    args: {
        bomCode: Awaited<xtremManufacturing.nodes.WorkOrder['bomCode']>;
        workOrder: xtremManufacturing.nodes.WorkOrder;
    },
): Promise<void> {
    // If the service option for BOM revision is enabled, the control must be performed at bomRevision level
    if (
        !(await args.workOrder.$.context.isServiceOptionEnabled(
            xtremMasterData.serviceOptions.billOfMaterialRevisionServiceOption,
        ))
    ) {
        await bomOrRevisionStatusControl(cx, { ...args, bomRevision: null });
    }

    if (await (await args.bomCode?.item)?.isPhantom) {
        cx.error.addLocalized(
            '@sage/xtrem-manufacturing/nodes__work_order__phantom_item_not_allowed',
            'You can only create a work order for a standard BOM. You cannot create a work order for a phantom BOM.',
        );
    }
}

/** Check the BOM and and the revision are consistent */
async function bomRevisionConsistencyControl(
    cx: ValidationContext,
    args: {
        bomCode: Awaited<xtremManufacturing.nodes.WorkOrder['bomCode']>;
        bomRevision: Awaited<xtremManufacturing.nodes.WorkOrder['bomRevision']>;
    },
): Promise<void> {
    const { bomCode, bomRevision } = args;

    if (!bomRevision) return;

    // If a revision is set it must be the one of the bomCode
    await cx.error
        .withMessage('@sage/xtrem-manufacturing/nodes__work-order__bom_code_not_set', 'You need to select a BOM code.')
        .if(bomCode === null && bomRevision !== null)
        .is.true();

    await cx.error
        .withMessage(
            '@sage/xtrem-manufacturing/nodes__work-order__bom_revision_bom_code_mismatch',
            'The BOM revision needs to be for the BOM code. Item: {{itemId}}, Site: {{siteId}}.',
            async () => ({ itemId: await (await bomCode?.item)?.id, siteId: await (await bomCode?.site)?.id }),
        )
        .if((await bomRevision.billOfMaterial)._id !== bomCode?._id)
        .is.true();
}

/** Check the BOM revision is available at the work order start date */
async function revisionStartDateControl(
    cx: ValidationContext,
    args: {
        bomRevision: Awaited<xtremManufacturing.nodes.WorkOrder['bomRevision']>;
        workOrder: xtremManufacturing.nodes.WorkOrder;
    },
): Promise<void> {
    const { bomRevision, workOrder } = args;

    if (!bomRevision) return;

    // the BOM revision must start before the work order start date
    const woStartDate = await workOrder.startDate;
    await cx.error
        .withMessage(
            '@sage/xtrem-manufacturing/nodes__work-order__bom_revision_start_date_invalid',
            'The BOM revision needs to be available to use on the work order start date.',
            async () => ({ woStartDate: await workOrder.startDate }),
        )
        .if((await bomRevision.startDate).compare(woStartDate) > 0)
        .is.true();
}

export async function workOrderBomRevisionControl(
    cx: ValidationContext,
    args: {
        bomCode: Awaited<xtremManufacturing.nodes.WorkOrder['bomCode']>;
        bomRevision: Awaited<xtremManufacturing.nodes.WorkOrder['bomRevision']>;
        workOrder: xtremManufacturing.nodes.WorkOrder;
    },
): Promise<void> {
    await bomRevisionConsistencyControl(cx, args);

    await bomOrRevisionStatusControl(cx, args);

    await revisionStartDateControl(cx, args);
}
