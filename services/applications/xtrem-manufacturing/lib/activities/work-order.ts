import { Activity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { commonWorkOrderOperations } from '../functions/common';
import * as xtremManufacturing from '../index';

export const workOrder = new Activity({
    description: 'Work order',
    node: () => xtremManufacturing.nodes.WorkOrder,
    __filename,
    permissions: ['read', 'manage', 'close', 'manageCost', 'repost', 'tracking'],
    operationGrants: {
        read: [...commonWorkOrderOperations],
        manage: [
            ...commonWorkOrderOperations,
            ...xtremStockData.functions.allocationLib.allocationOperation,
            {
                operations: [
                    'create',
                    'update',
                    'delete',
                    'createWorkOrder',
                    'addOperationsToWorkOrder',
                    'preGenerateSerialNumbers',
                    'requestAutoAllocation',
                    'addComponentsToWorkOrder',
                    'scheduleWorkOrder',
                    'skipSchedulingWorkOrder',
                    'printBulk',
                    'bulkPrintPickList',
                    'resynchronizeStatus',
                    'getProductionCost',
                    'beforePrintWorkOrderPickList',
                ],
                on: [() => xtremManufacturing.nodes.WorkOrder],
            },
            {
                operations: [
                    'massAutoAllocation',
                    'updateComponent',
                    'setRequiredQuantity',
                    'checkComponentStock',
                    'getMaterialPlannedCost',
                ],
                on: [() => xtremManufacturing.nodes.WorkOrderComponent],
            },
            {
                operations: ['updateReleasedItem'],
                on: [() => xtremManufacturing.nodes.WorkOrderReleasedItem],
            },
            {
                operations: ['updateOperation'],
                on: [() => xtremManufacturing.nodes.WorkOrderOperation],
            },
            { operations: ['getProjectedStock'], on: [() => xtremMasterData.nodes.ItemSite] },
        ],
        close: [
            {
                operations: ['read', 'closeWorkOrder', 'controlClosingDate', 'resynchronizeStatus'],
                on: [() => xtremManufacturing.nodes.WorkOrder],
            },
        ],
        manageCost: [{ operations: ['read', 'updatePlannedCosts'], on: [() => xtremManufacturing.nodes.WorkOrder] }],
        repost: [
            { operations: ['read', 'resendNotificationForFinance'], on: [() => xtremManufacturing.nodes.WorkOrder] },
        ],
        tracking: [{ operations: ['read', 'trackBillOfMaterial'], on: [() => xtremManufacturing.nodes.WorkOrder] }],
    },
});
