import { Activity } from '@sage/xtrem-core';
import { commonWorkOrderOperations } from '../functions/common';
import { WorkOrderCategory } from '../nodes/work-order-category';

export const workOrderCategory = new Activity({
    description: 'Work order category',
    node: () => WorkOrderCategory,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [...commonWorkOrderOperations],
        manage: [
            ...commonWorkOrderOperations,
            {
                operations: ['create', 'update', 'delete'],
                on: [() => WorkOrderCategory],
            },
        ],
    },
});
