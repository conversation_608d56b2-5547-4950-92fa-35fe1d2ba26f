import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import { commonResourcesOperations, commonTrackingOperations } from '../functions/common';
import * as xtremManufacturing from '../index';

export const operationTracking = new Activity({
    description: 'Time tracking',
    node: () => xtremManufacturing.nodes.OperationTracking,
    __filename,
    permissions: ['read', 'create'],
    operationGrants: {
        read: [
            ...commonTrackingOperations,
            ...commonResourcesOperations,
            ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
            {
                operations: ['lookup'],
                on: [() => xtremTechnicalData.nodes.BillOfMaterial],
            },
            {
                operations: ['lookup'],
                on: [() => xtremTechnicalData.nodes.Routing],
            },
        ],
        create: [
            ...commonTrackingOperations,
            ...commonResourcesOperations,
            {
                operations: [
                    'createOperationTrackings',
                    'createMultipleOperationalTrackings',
                    'resendNotificationForFinance',
                ],
                on: [() => xtremManufacturing.nodes.OperationTracking],
            },
            {
                operations: ['lookup'],
                on: [() => xtremTechnicalData.nodes.BillOfMaterial],
            },
            {
                operations: ['lookup'],
                on: [() => xtremTechnicalData.nodes.Routing],
            },

            ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
        ],
    },
});
