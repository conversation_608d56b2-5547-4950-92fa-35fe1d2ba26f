import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { commonTrackingOperations } from '../functions/common';
import * as xtremManufacturing from '../index';

export const productionTracking = new Activity({
    description: 'Production tracking',
    node: () => xtremManufacturing.nodes.ProductionTracking,
    __filename,
    permissions: ['read', 'manage', 'postToStock'],
    operationGrants: {
        read: [
            ...commonTrackingOperations,
            ...xtremStockData.functions.stockDetailLib.stockReceiptOperations,
            ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
        ],
        manage: [
            ...commonTrackingOperations,
            ...xtremStockData.functions.stockDetailLib.stockReceiptOperations,
            ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
            {
                operations: [
                    'read',
                    'update',
                    'create',
                    'createWorkOrderTracking',
                    'createSingleProductionTracking',
                    'createMultipleWorkOrderTrackings',
                ],
                on: [() => xtremManufacturing.nodes.ProductionTracking],
            },
            {
                operations: ['getQuantityProduced'],
                on: [() => xtremManufacturing.nodes.ProductionTrackingLine],
            },
        ],
        postToStock: [
            {
                operations: ['read', 'resendNotificationForFinance'],
                on: [() => xtremManufacturing.nodes.ProductionTracking],
            },
            ...commonTrackingOperations,
            ...xtremStockData.functions.stockDetailLib.stockReceiptOperations,
            ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
        ],
    },
});
