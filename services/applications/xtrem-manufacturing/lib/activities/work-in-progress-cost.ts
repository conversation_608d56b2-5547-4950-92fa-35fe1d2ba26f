import { Activity } from '@sage/xtrem-core';
import { commonWorkOrderOperations } from '../functions/common';
import * as xtremManufacturing from '../index';

export const workInProgressCost = new Activity({
    description: 'Work in progress inquiry',
    node: () => xtremManufacturing.nodes.WorkInProgressCost,
    __filename,
    permissions: ['read', 'create', 'update', 'delete'],
    operationGrants: {
        read: [
            ...commonWorkOrderOperations,
            { operations: ['getCostProduced'], on: [() => xtremManufacturing.nodes.WorkInProgressCost] },
            { operations: ['massAutoAllocation'], on: [() => xtremManufacturing.nodes.WorkOrderComponent] },
        ],
        create: [
            ...commonWorkOrderOperations,
            { operations: ['massAutoAllocation'], on: [() => xtremManufacturing.nodes.WorkOrderComponent] },
        ],
        update: [
            ...commonWorkOrderOperations,
            { operations: ['massAutoAllocation'], on: [() => xtremManufacturing.nodes.WorkOrderComponent] },
        ],
        delete: [
            ...commonWorkOrderOperations,
            { operations: ['massAutoAllocation'], on: [() => xtremManufacturing.nodes.WorkOrderComponent] },
            {
                operations: ['read', 'create', 'update', 'wipInquiry'],
                on: [() => xtremManufacturing.nodes.WorkInProgressInputSet],
            },
        ],
    },
});
