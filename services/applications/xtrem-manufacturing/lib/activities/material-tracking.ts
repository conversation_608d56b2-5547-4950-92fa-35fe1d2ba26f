import { Activity } from '@sage/xtrem-core';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import { commonTrackingOperations } from '../functions/common';
import * as xtremManufacturing from '../index';

export const materialTracking = new Activity({
    description: 'Material tracking',
    node: () => xtremManufacturing.nodes.MaterialTracking,
    __filename,
    permissions: ['read', 'manage', 'postToStock'],
    operationGrants: {
        read: [
            ...commonTrackingOperations,
            {
                operations: ['lookup'],
                on: [() => xtremTechnicalData.nodes.BillOfMaterial],
            },
        ],
        manage: [
            ...commonTrackingOperations,
            {
                operations: [
                    'create',
                    'update',
                    'createMaterialTrackings',
                    'createSingleMaterialTracking',
                    'createMultipleMaterialTrackings',
                ],
                on: [() => xtremManufacturing.nodes.MaterialTracking],
            },
            {
                operations: ['lookup'],
                on: [() => xtremTechnicalData.nodes.BillOfMaterial],
            },
        ],
        postToStock: [
            ...commonTrackingOperations,
            {
                operations: ['read', 'resendNotificationForFinance'],
                on: [() => xtremManufacturing.nodes.MaterialTracking],
            },
            {
                operations: ['lookup'],
                on: [() => xtremTechnicalData.nodes.BillOfMaterial],
            },
        ],
    },
});
