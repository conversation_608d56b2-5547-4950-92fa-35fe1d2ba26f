import type { Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { GraphApi, WorkOrderCategory } from '@sage/xtrem-manufacturing-api';
import type { StockReceiptDetailsPanel } from '@sage/xtrem-stock-data/build/lib/pages/stock-receipt-details-panel';
import * as ui from '@sage/xtrem-ui';
import type { SiteBinding } from '@sage/xtrem-system-api';
import type { ExtensionMembers } from '@sage/xtrem-ui/build/lib/service/page-extension';
import type { Item } from '@sage/xtrem-master-data-api';
import type {
    StockReceiptDetailPanelParameters,
    OperationResultType,
} from '../client-functions/interfaces/stock-details';
import { loadCheckComponentStock } from '../client-functions/track-stock-functions';

@ui.decorators.pageExtension<StockReceiptDetailsPanelExtension>({
    extends: '@sage/xtrem-stock-data/StockReceiptDetailsPanel',
    businessActions() {
        return [this.okFromBom];
    },
    extensionAccessBinding: {
        node: '@sage/xtrem-manufacturing/WorkOrderCategory',
        bind: 'lookup',
    },
    async onLoad() {
        if (this.queryParams.trackCheckStock === 'checkStock') {
            this.$.page.title = ui.localize(
                '@sage/xtrem-manufacturing/page_extension_stock_receipt_details_panel__page_title_checkStock',
                'Check stock',
            );

            await loadCheckComponentStock({
                pageInstance: this,
                quantity: Number(this.quantity.value),
                documentLine: this.queryParams.documentLine ?? '',
                componentShortage: this.componentShortage,
                stockCheck: this.stockCheck,
            });
        } else if (!this.okFromBom.isHidden) {
            this.$.page.title = ui.localize(
                '@sage/xtrem-manufacturing/page_extension_stock_receipt_details_panel__page_title',
                'Production tracking',
            );
            this.stockDetails.title = ui.localize(
                '@sage/xtrem-manufacturing/page_extension_stock_receipt_details_panel__stockDetails_title',
                'Stock receipt details',
            );
            this.ok.isHidden = !this.okFromBom.isHidden;
            this.componentShortage.isHidden = true;
        }

        if (!this.category.isHidden) {
            this.categoryFilter = this.queryParams?.additionalParameters?.hasRouting
                ? { billOfMaterial: true }
                : { billOfMaterial: true, routing: false };

            const categories = extractEdges(
                await this.$.graph
                    .node('@sage/xtrem-manufacturing/WorkOrderCategory')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                _id: true,
                                name: true,
                                id: true,
                                routing: true,
                                billOfMaterial: true,
                            },
                            {
                                filter: this.categoryFilter,
                                orderBy: { routing: -1 },
                            },
                        ),
                    )
                    .execute(),
            );
            if (categories.length > 0) {
                [this.category.value] = categories;
            }
        }

        if (this.queryParams.trackCheckStock === 'checkStock') {
            this.hideCheckStockFields();
        }

        await this.$.commitValueAndPropertyChanges();
        this.$.setPageClean();
        await this.$.page.validate();
    },
})
export class StockReceiptDetailsPanelExtension extends ui.PageExtension<StockReceiptDetailsPanel, GraphApi> {
    queryParams: StockReceiptDetailPanelParameters;

    categoryFilter: Filter<WorkOrderCategory>;

    @ui.decorators.referenceField<StockReceiptDetailsPanelExtension, WorkOrderCategory>({
        parent() {
            return this.mainBlock;
        },
        title: 'Category',
        width: 'small',
        node: '@sage/xtrem-manufacturing/WorkOrderCategory',
        lookupDialogTitle: 'Select category',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'routing', title: 'Routing' }),
            ui.nestedFields.checkbox({ bind: 'billOfMaterial', title: 'Bill of material' }),
        ],
        isAutoSelectEnabled: true,
        shouldSuggestionsIncludeColumns: true,
        isHidden() {
            return (
                !this.queryParams?.additionalParameters?.fieldCustomizations?.category?.isVisible ||
                this.queryParams.trackCheckStock === 'checkStock'
            );
        },
        filter() {
            return this.categoryFilter;
        },
    })
    category: ui.fields.Reference<WorkOrderCategory>;

    @ui.decorators.referenceFieldOverride<StockReceiptDetailsPanelExtension>({
        isHidden() {
            return this.queryParams.trackCheckStock === 'checkStock';
        },
    })
    item: ui.fields.Reference<Item>;

    @ui.decorators.dateFieldOverride<StockReceiptDetailsPanelExtension>({
        isHidden() {
            return this.queryParams.trackCheckStock === 'checkStock';
        },
    })
    date: ui.fields.Date;

    @ui.decorators.referenceFieldOverride<StockReceiptDetailsPanelExtension>({
        isHidden() {
            return this.queryParams.trackCheckStock === 'checkStock';
        },
    })
    stockSite: ui.fields.Reference<SiteBinding>;

    @ui.decorators.numericFieldOverride<StockReceiptDetailsPanelExtension>({
        title: 'Quantity to produce',
        onChangeAfter() {
            if (this.componentShortage.value.length) {
                this.componentShortage.value.forEach(line => this.componentShortage.removeRecord(line._id));
            }
        },
    })
    quantity: ui.fields.Numeric;

    @ui.decorators.tableField<StockReceiptDetailsPanelExtension, OperationResultType>({
        title: 'Component shortage',
        bind: 'components',
        insertBefore() {
            return this.stockDetails;
        },
        orderBy: {
            componentNumber: +1,
        },
        canUserHideColumns: false,
        canSelect: false,
        node: '@sage/xtrem-stock-data/Stock',
        pageSize: 10,
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                title: 'Component description',
                bind: 'componentName',
            }),
            ui.nestedFields.numeric({
                title: 'Available quantity',
                bind: 'available',
                postfix(_rowId: string, rowItem: OperationResultType) {
                    return rowItem.available ? rowItem.unit : '';
                },
                scale() {
                    return this.unit.value?.decimalDigits || 0;
                },
            }),
            ui.nestedFields.numeric({
                title: 'Required quantity',
                bind: 'required',
                postfix(_rowId: string, rowItem: OperationResultType) {
                    return rowItem.required ? rowItem.unit : '';
                },
                scale() {
                    return this.unit.value?.decimalDigits || 0;
                },
            }),
            ui.nestedFields.numeric({
                title: 'Shortage',
                bind: 'shortage',
                isTransient: true,
                postfix(_rowId: string, rowItem: OperationResultType) {
                    return rowItem.shortage ? rowItem.unit : '';
                },
                scale() {
                    return this.unit.value?.decimalDigits || 0;
                },
            }),
            ui.nestedFields.text({
                bind: 'itemName',
                title: 'Item',
            }),
            ui.nestedFields.text({
                bind: 'itemId',
                title: 'Item ID',
            }),
            ui.nestedFields.text({
                bind: 'itemDescription',
                title: 'Item description',
            }),
        ],
        isHidden() {
            return !this.queryParams?.additionalParameters?.fieldCustomizations?.componentShortage?.isVisible;
        },
        parent() {
            return this.mainSection;
        },
    })
    componentShortage: ui.fields.Table<OperationResultType>;

    @ui.decorators.buttonField<StockReceiptDetailsPanelExtension>({
        parent() {
            return this.mainBlock;
        },
        async onClick() {
            await loadCheckComponentStock({
                pageInstance: this,
                quantity: Number(this.quantity.value),
                documentLine: this.queryParams.documentLine ?? '',
                componentShortage: this.componentShortage,
                stockCheck: this.stockCheck,
            });
        },
        size: 'small',
        map() {
            if (this.queryParams.trackCheckStock === 'checkStock') {
                return ui.localize(
                    '@sage/xtrem-manufacturing/page_extension_stock_receipt_details_panel_recalculate',
                    'Recalculate',
                );
            }
            return ui.localize(
                '@sage/xtrem-manufacturing/page_extension_stock_receipt_details_panel_checkStock',
                'Check stock',
            );
        },
        isHidden() {
            return !this.queryParams?.additionalParameters?.fieldCustomizations?.stockCheck?.isVisible;
        },
    })
    stockCheck: ui.fields.Button;

    @ui.decorators.pageAction<StockReceiptDetailsPanelExtension>({
        title: 'OK',
        isHidden() {
            return (
                !this.queryParams?.additionalParameters?.fieldCustomizations?.okFromBom?.isVisible ||
                this.queryParams.trackCheckStock === 'checkStock'
            );
        },
        async onClick() {
            await this.onClickOk({
                ...(this.queryParams?.returnCustomization?.quantity
                    ? {
                          category: {
                              categoryId: this.category.value?._id,
                              categoryRouting: this.category.value?.routing,
                              categoryBillOfMaterial: this.category.value?.billOfMaterial,
                          },
                      }
                    : {}),
                ...(this.queryParams?.returnCustomization?.quantity
                    ? {
                          stockDetailStatus: this.stockDetailStatus.value,
                      }
                    : {}),
            });
        },
        isDisabled() {
            return this.stockDetails.value.length < 1 || this.stockCheck.isDisabled;
        },
    })
    okFromBom: ui.PageAction;

    public hideCheckStockFields(
        this: ExtensionMembers<StockReceiptDetailsPanelExtension & StockReceiptDetailsPanel> &
            StockReceiptDetailsPanelExtension,
    ) {
        this.stockDetails.isHidden = true;
        this.okFromBom.isHidden = true;
        this.ok.isHidden = true;
        this.cancel.title = 'Close';
        this.cancel.buttonType = 'primary';
    }
}

declare module '@sage/xtrem-stock-data/build/lib/pages/stock-receipt-details-panel' {
    export interface StockReceiptDetailsPanel extends StockReceiptDetailsPanelExtension {}
}
