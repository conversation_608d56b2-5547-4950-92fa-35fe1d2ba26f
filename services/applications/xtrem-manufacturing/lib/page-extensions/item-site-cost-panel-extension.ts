import { extractEdges } from '@sage/xtrem-client';
import type { GraphApi } from '@sage/xtrem-manufacturing-api';
import type { ItemSiteCostPanel } from '@sage/xtrem-master-data/build/lib/pages/item-site-cost-panel';
import * as ui from '@sage/xtrem-ui';
import type { ExtensionMembers } from '@sage/xtrem-ui/build/lib/service/page-extension';

@ui.decorators.pageExtension<ItemSiteCostPanelExtension>({
    extends: '@sage/xtrem-master-data/ItemSiteCostPanel',

    onLoad() {
        this.calculate.isHidden = !this.itemSite.value?.item?.isManufactured;
        this.isCalculated.isHidden = !this.itemSite.value?.item?.isManufactured;
        if (this.itemSite.value?.item?.isManufactured) {
            this.setIsCalculated(this.isCalculated.value ?? false);
        }
        this.isCalculated.isDisabled = this.isCostFrozen();
        this.calculate.isDisabled = this.isCostFrozen();
    },
})
export class ItemSiteCostPanelExtension extends ui.PageExtension<ItemSiteCostPanel, GraphApi> {
    @ui.decorators.switchField<ItemSiteCostPanelExtension>({
        parent() {
            return this.mainBlock;
        },
        title: 'Calculated',
        insertBefore() {
            return this.materialCost;
        },
        onChange() {
            this.setIsCalculated(this.isCalculated.value ?? false);
        },
    })
    isCalculated: ui.fields.Switch;

    @ui.decorators.buttonField<ItemSiteCostPanelExtension>({
        isTransient: true,
        size: 'small',
        insertBefore() {
            return this.materialCost;
        },
        parent() {
            return this.mainBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-manufacturing/page_extension_item_site_cost_calculate', 'Calculate');
        },
        async onClick() {
            await this.calculateManufacturedCost();
        },
    })
    calculate: ui.fields.Button;

    async calculateManufacturedCost() {
        const page = this as unknown as ExtensionMembers<ItemSiteCostPanel & ItemSiteCostPanelExtension>;
        const messages: string[] = [];
        const filter = { item: page.itemSite.value?.item?._id, site: page.itemSite.value?.site?._id };
        const [billOfMaterialCosts] = extractEdges(
            await page.$.graph
                .node('@sage/xtrem-technical-data/BillOfMaterial')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true, standardCost: true, baseQuantity: true, status: true },
                        { filter },
                    ),
                )
                .execute(),
        );
        const [routingCosts] = extractEdges(
            await page.$.graph
                .node('@sage/xtrem-technical-data/Routing')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            machineCost: true,
                            machineSetupCost: true,
                            laborCost: true,
                            laborSetupCost: true,
                            toolCost: true,
                            toolSetupCost: true,
                            batchQuantity: true,
                            status: true,
                        },
                        { filter },
                    ),
                )
                .execute(),
        );
        if (billOfMaterialCosts) {
            const baseQuantity = billOfMaterialCosts.baseQuantity
                ? parseFloat(billOfMaterialCosts.baseQuantity.toString())
                : 1;
            const quantityFactor = (page.forQuantity.value ?? 0) / baseQuantity;
            const standardCost = billOfMaterialCosts.standardCost
                ? parseFloat(billOfMaterialCosts.standardCost.toString())
                : 0;
            const materialCost = standardCost * quantityFactor;
            page.materialCost.value = +materialCost.toFixed(page.materialCost.scale);
            if (['inDevelopment', 'suspended'].includes(billOfMaterialCosts.status)) {
                messages.push(
                    ui.localize(
                        '@sage/xtrem-manufacturing/page_extension_item_site_cost_bill_of_suspended_in_development',
                        'The bill of material is in development or suspended. When it is available to use, you might need to recalculate the item-site cost.',
                    ),
                );
            }
        } else {
            messages.push(
                ui.localize(
                    '@sage/xtrem-manufacturing/page_extension_item_site_cost_bill_of_material_missing',
                    'No bill of material found for this item. Check the costing calculation.',
                ),
            );
        }
        if (routingCosts) {
            const batchQuantity = routingCosts.batchQuantity ? parseFloat(routingCosts.batchQuantity.toString()) : 1;
            const quantityFactor = (page.forQuantity.value ?? 0) / batchQuantity;

            const machineRunCost = routingCosts.machineCost ? parseFloat(routingCosts.machineCost.toString()) : 0;
            const machineSetupCost = routingCosts.machineSetupCost
                ? parseFloat(routingCosts.machineSetupCost.toString())
                : 0;
            const laborRunCost = routingCosts.laborCost ? parseFloat(routingCosts.laborCost.toString()) : 0;
            const laborSetupCost = routingCosts.laborSetupCost ? parseFloat(routingCosts.laborSetupCost.toString()) : 0;
            const toolRunCost = routingCosts.toolCost ? parseFloat(routingCosts.toolCost.toString()) : 0;
            const toolSetupCost = routingCosts.toolSetupCost ? parseFloat(routingCosts.toolSetupCost.toString()) : 0;

            const machineCost = (machineRunCost + machineSetupCost) * quantityFactor;
            const laborCost = (laborRunCost + laborSetupCost) * quantityFactor;
            const toolCost = (toolRunCost + toolSetupCost) * quantityFactor;

            page.machineCost.value = +machineCost.toFixed(page.machineCost.scale);
            page.laborCost.value = +laborCost.toFixed(page.laborCost.scale);
            page.toolCost.value = +toolCost.toFixed(page.toolCost.scale);

            if (['inDevelopment', 'suspended'].includes(routingCosts.status)) {
                messages.push(
                    ui.localize(
                        '@sage/xtrem-manufacturing/page_extension_item_site_cost_routing_of_suspended_in_development',
                        'The routing is in development or suspended. When it is available to use, you might need to recalculate the item-site cost.',
                    ),
                );
            }
        } else {
            messages.push(
                ui.localize(
                    '@sage/xtrem-manufacturing/page_extension_item_site_cost_routing_missing',
                    'No routing found for this item. Check the costing calculation.',
                ),
            );
        }
        if (messages.length) {
            page.$.showToast(messages.join(','), { timeout: 2000, type: 'warning' });
        }
        //  await page.calculateIndirectCost();  TODO: Indirect cost removal until refactor
        page.calculateTotalCost();
        this.displayChart();
        page.isCalculated.value = true;
        page.isCalculated.isReadOnly = false;
        page.setIsCalculated(true);
    }

    private displayChart(): void {
        const page = this as unknown as ExtensionMembers<ItemSiteCostPanel & ItemSiteCostPanelExtension>;
        if (!page.materialCost.value) {
            page.materialCost.value = 0;
        }
        if (!page.machineCost.value) {
            page.machineCost.value = 0;
        }
        if (!page.laborCost.value) {
            page.laborCost.value = 0;
        }
        if (!page.toolCost.value) {
            page.toolCost.value = 0;
        }

        // TODO: Indirect cost removal until refactor
        // if (!page.indirectCost.value) {
        //     page.indirectCost.value = 0;
        // }
        const costs = [
            // { _id: '1', name: page.indirectCost.title, cost: page.indirectCost.value },
            { _id: '1', name: page.materialCost.title, cost: page.materialCost.value },
            { _id: '2', name: page.machineCost.title, cost: page.machineCost.value },
            { _id: '3', name: page.laborCost.title, cost: page.laborCost.value },
            { _id: '4', name: page.toolCost.title, cost: page.toolCost.value },
        ];
        page.costChart.value = costs;
    }
}

declare module '@sage/xtrem-master-data/build/lib/pages/item-site-cost-panel' {
    export interface ItemSiteCostPanel extends ItemSiteCostPanelExtension {}
}
