import type { Graph<PERSON>pi } from '@sage/xtrem-manufacturing-api';
import type { AllocationResult } from '@sage/xtrem-stock-data/build/lib/pages/allocation-result';
import * as ui from '@sage/xtrem-ui';
import type { ManufacturingAllocationMassProcessCriteria } from '../shared-functions/allocation';

@ui.decorators.pageExtension<AllocationResultExtension>({
    extends: '@sage/xtrem-stock-data/AllocationResult',
    onLoad() {
        if (this.documentType.value !== 'workOrder') {
            this.latestStartDate.isHidden = true;
            this.fromReleasedItem.isHidden = true;
            this.toReleasedItem.isHidden = true;
            this.fromOperation.isHidden = true;
            this.toOperation.isHidden = true;
        } else {
            this.fromOrderNumber.title = ui.localize(
                '@sage/xtrem-manufacturing/pages__allocation_result__fromWorkOrderNumberTitle',
                'From work order',
            );
            this.toOrderNumber.title = ui.localize(
                '@sage/xtrem-manufacturing/pages__allocation_result__toWorkOrderNumberTitle',
                'To work order',
            );
        }

        const criteria = this.massProcessCriteria.value
            ? (JSON.parse(this.massProcessCriteria.value) as ManufacturingAllocationMassProcessCriteria)
            : null;

        if (criteria) {
            this.stockSiteName.value = criteria.stockSite ?? '';
            this.fromItemText.value = criteria.fromItem ?? '';
            this.toItemText.value = criteria.toItem ?? '';
            this.fromOrderNumber.value = criteria.fromOrderNumber ?? '';
            this.toOrderNumber.value = criteria.toOrderNumber ?? '';

            this.fromReleasedItem.value = criteria.fromReleasedItem ?? '';
            this.toReleasedItem.value = criteria.toReleasedItem ?? '';
            this.fromOperation.value = criteria.fromOperation ?? '';
            this.toOperation.value = criteria.toOperation ?? '';
            this.latestStartDate.value = criteria.maximumStartDate ?? '';
        }
    },
})
export class AllocationResultExtension extends ui.PageExtension<AllocationResult, GraphApi> {
    @ui.decorators.dateField<AllocationResultExtension>({
        title: 'Latest start date',
        isTransient: true,
        width: 'medium',
        isReadOnly: true,
        parent() {
            return this.criteriaBlock;
        },
        insertAfter() {
            return this.processType;
        },
    })
    latestStartDate: ui.fields.Date;

    @ui.decorators.textField<AllocationResultExtension>({
        title: 'From released item',
        isTransient: true,
        width: 'medium',
        isReadOnly: true,
        parent() {
            return this.criteriaBlock;
        },
        insertAfter() {
            return this.toOrderNumber;
        },
    })
    fromReleasedItem: ui.fields.Text;

    @ui.decorators.textField<AllocationResultExtension>({
        title: 'To released item',
        isTransient: true,
        width: 'medium',
        isReadOnly: true,
        parent() {
            return this.criteriaBlock;
        },
        insertAfter() {
            return this.fromReleasedItem;
        },
    })
    toReleasedItem: ui.fields.Text;

    @ui.decorators.textField<AllocationResultExtension>({
        title: 'From operation',
        isTransient: true,
        width: 'medium',
        isReadOnly: true,
        parent() {
            return this.criteriaBlock;
        },
        insertAfter() {
            return this.toReleasedItem;
        },
    })
    fromOperation: ui.fields.Text;

    @ui.decorators.textField<AllocationResultExtension>({
        title: 'To operation',
        isTransient: true,
        width: 'medium',
        isReadOnly: true,
        parent() {
            return this.criteriaBlock;
        },
        insertAfter() {
            return this.fromOperation;
        },
    })
    toOperation: ui.fields.Text;
}

declare module '@sage/xtrem-stock-data/build/lib/pages/allocation-result' {
    export interface Site extends AllocationResultExtension {}
}
