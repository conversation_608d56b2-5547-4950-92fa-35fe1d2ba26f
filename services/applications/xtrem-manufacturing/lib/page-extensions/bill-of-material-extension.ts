import { querySelector } from '@sage/xtrem-client';
import type {
    GraphApi,
    MaterialTracking,
    OperationTracking,
    ProductionTracking,
    WorkOrder,
} from '@sage/xtrem-manufacturing-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import type { BillOfMaterialTracking } from '@sage/xtrem-technical-data-api';
import type { BillOfMaterial } from '@sage/xtrem-technical-data/build/lib/pages/bill-of-material';
import * as ui from '@sage/xtrem-ui';
import * as PillColor from '../client-functions/pill-color';
import * as trackStockFunctions from '../client-functions/track-stock-functions';

interface TrackingBillOfMaterialResult {
    bom: Node;
    quantity: string;
    date: string;
    jsonStockDetails?: string;
    stockDetailStatus?: string;
    additionalData: {
        category?: {
            categoryId?: string;
            categoryRouting?: boolean;
            categoryBillOfMaterial?: boolean;
        };
        date: string;
        quantity: string;
    };
}

@ui.decorators.pageExtension<BillOfMaterialExtension>({
    extends: '@sage/xtrem-technical-data/BillOfMaterial',
    businessActions(this) {
        return [this.track];
    },

    headerDropDownActions() {
        return [this.checkStock];
    },
})
export class BillOfMaterialExtension extends ui.PageExtension<BillOfMaterial, GraphApi> {
    public trackCheckStock: 'track' | 'checkStock';

    @ui.decorators.pageAction<BillOfMaterialExtension>({
        title: 'Check stock',
        icon: 'three_boxes',
        insertBefore() {
            return this.$standardOpenCustomizationPageWizardAction;
        },
        async onClick() {
            this.trackCheckStock = 'checkStock';
            const line = await trackStockFunctions.billOfMaterialResult({
                billOfMaterialPage: this,
                itemId: this.item.value?._id,
                siteId: this.site.value?._id,
            });

            await trackStockFunctions.returnedStockDetails({
                billOfMaterialWithExtentionPage: this,
                baseQuantity: this.baseQuantity.value || undefined,
                line,
                documentLine: this.$.recordId,
                itemStockUnitId: this.item.value?.stockUnit?._id,
                siteDefaultStockStatusId: this.site.value?.defaultStockStatus?._id,
                siteDefaultLocationId: this.site.value?.defaultLocation?._id,
                trackCheckStock: this.trackCheckStock,
                routingCode: this.routingCode.value?.name || undefined,
                routingCodeStatus: this.routingCode.value?.status,
                billOfMaterialPage: this,
                siteId: this.site.value?._id,
                itemId: this.item.value?._id,
            });
        },
    })
    checkStock: ui.PageAction;

    @ui.decorators.pageAction<BillOfMaterialExtension>({
        title: 'Track',
        isHidden() {
            return (
                this.status.value !== 'availableToUse' ||
                this.$.isDirty ||
                this.item.value?.serialNumberManagement === 'managed' ||
                this.item.value?.isPhantom ||
                this.components.value.some(
                    component => component.item?.serialNumberManagement === 'managed' || component.item?.isPhantom,
                )
            );
        },
        async onClick() {
            this.trackCheckStock = 'track';
            const line = await trackStockFunctions.billOfMaterialResult({
                billOfMaterialPage: this,
                itemId: this.item.value?._id,
                siteId: this.site.value?._id,
            });

            const returnedStockDetails: Node & TrackingBillOfMaterialResult =
                await trackStockFunctions.returnedStockDetails({
                    billOfMaterialWithExtentionPage: this,
                    baseQuantity: this.baseQuantity.value || undefined,
                    line,
                    documentLine: this.$.recordId,
                    itemStockUnitId: this.item.value?.stockUnit?._id,
                    siteDefaultStockStatusId: this.site.value?.defaultStockStatus?._id,
                    siteDefaultLocationId: this.site.value?.defaultLocation?._id,
                    trackCheckStock: this.trackCheckStock,
                    routingCode: this.routingCode.value?.name || undefined,
                    routingCodeStatus: this.routingCode.value?.status,
                    billOfMaterialPage: this,
                    siteId: this.site.value?._id,
                    itemId: this.item.value?._id,
                });

            if (returnedStockDetails && returnedStockDetails.additionalData) {
                const { category, date, quantity } = returnedStockDetails.additionalData;
                const { jsonStockDetails } = returnedStockDetails;
                const dataToPost = {
                    bomId: line[0]?._id,
                    siteId: this.site.value?.id ?? '',
                    itemId: this.item.value?.id ?? '',
                    itemName: this.item.value?.name ?? '',
                    quantity: Number(quantity),
                    workOrderCategoryId: category?.categoryId ?? '',
                    date,
                    route:
                        category?.categoryRouting && this.routingCode.value?.status === 'availableToUse'
                            ? category?.categoryId
                            : '',
                    stockDetails: jsonStockDetails ?? '',
                };

                await this.$.graph
                    .node('@sage/xtrem-manufacturing/WorkOrder')
                    .asyncOperations.trackBillOfMaterial.start(
                        {
                            trackingId: true,
                        },
                        {
                            data: dataToPost,
                        },
                    )
                    .execute();

                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-manufacturing/page-extensions__billOfMaterialExtension__trackingSuccess',
                        'Bill of material tracking in progress',
                    ),
                    {
                        type: 'success',
                    },
                );

                await this.$.router.hardRefresh();
            }
        },
    })
    track: ui.PageAction;

    @ui.decorators.tableField<BillOfMaterialExtension, BillOfMaterialTracking>({
        title: 'Trackings',
        isTitleHidden: true,
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-technical-data/BillOfMaterialTracking',
        parent() {
            return this.trackingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical({
                bind: 'billOfMaterial',
                node: '@sage/xtrem-technical-data/BillOfMaterial',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({
                        bind: 'item',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.date({
                title: 'Date',
                bind: 'date',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Quantity',
                bind: 'quantity',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Unit',
                bind: { billOfMaterial: { item: { stockUnit: true } } },
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                isReadOnly: true,
                columns: [ui.nestedFields.text({ title: 'Name', bind: 'name' })],
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'status',
                optionType: '@sage/xtrem-technical-data/BomTrackingStatus',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('WorkOrderStatus', rowData?.status),
            }),
            ui.nestedFields.technical({ bind: 'workOrderId' }),
            ui.nestedFields.technical({ bind: 'materialTrackingId' }),
            ui.nestedFields.technical({ bind: 'productionTrackingId' }),
            ui.nestedFields.technical({ bind: 'timeTrackingId' }),
            ui.nestedFields.technical({ bind: 'message' }),
        ],
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId: string) {
                    this.trackings.openSidebar(rowId);
                },
            },
        ],
        sidebar: {
            title(_id, recordValue) {
                return `Bill of material ${recordValue?.billOfMaterial.name}`;
            },
            headerDropdownActions: [],
            headerQuickActions: [],
            async onRecordOpened(_id, recordValue) {
                if (recordValue) {
                    if (recordValue.status === 'error' && recordValue.message.length !== 0) {
                        this.$.showToast(
                            ui.localize(
                                '@sage/xtrem-manufacturing/pages__bill_of_material_extension__tracking__creation__error',
                                `${recordValue.message}`,
                            ),
                            { timeout: 10000, type: 'error' },
                        );
                    }
                    await this.populateTrackings(recordValue);
                }
            },
            onRecordConfirmed() {
                this.$.setPageClean();
            },
            onRecordDiscarded() {
                this.$.setPageClean();
            },
            layout() {
                return {
                    workOrder: {
                        title: 'Work order',
                        blocks: {
                            orderBlock: {
                                fields: [this.workOrder],
                            },
                        },
                    },
                    materialTracking: {
                        title: 'Material tracking',
                        blocks: {
                            materialBlock: {
                                fields: [this.materialTracking],
                            },
                        },
                    },
                    productionTracking: {
                        title: 'Production tracking',
                        blocks: {
                            productionBlock: {
                                fields: [this.productionTracking],
                            },
                        },
                    },
                    timeTracking: {
                        title: 'Time tracking',
                        blocks: {
                            timeBlock: {
                                fields: [this.timeTracking],
                            },
                        },
                    },
                };
            },
        },
    })
    trackings: ui.fields.Table<BillOfMaterialTracking>;

    async populateTrackings(tracking: BillOfMaterialTracking) {
        this.workOrder.value = [];
        this.materialTracking.value = [];
        this.productionTracking.value = [];
        this.timeTracking.value = [];
        if (Number(tracking.workOrderId) > 0) {
            const workOrderDoc = await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrder')
                .read(
                    {
                        _id: true,
                        number: true,
                        status: true,
                        startDate: true,
                        site: {
                            _id: true,
                            id: true,
                            name: true,
                            isLocationManaged: true,
                        },
                        materialTrackings: querySelector(
                            {
                                _id: true,
                                number: true,
                                entryDate: true,
                                site: {
                                    _id: true,
                                    id: true,
                                    name: true,
                                    isLocationManaged: true,
                                },
                                stockTransactionStatus: true,
                            },
                            {
                                filter: { workOrder: { _id: tracking.workOrderId } },
                            },
                        ),
                        productionTrackings: querySelector(
                            {
                                _id: true,
                                number: true,
                                entryDate: true,
                                site: {
                                    _id: true,
                                    id: true,
                                    name: true,
                                    isLocationManaged: true,
                                },
                                stockTransactionStatus: true,
                            },
                            {
                                filter: { workOrder: { _id: tracking.workOrderId } },
                            },
                        ),
                        timeTrackings: querySelector(
                            {
                                _id: true,
                                number: true,
                                entryDate: true,
                                site: {
                                    _id: true,
                                    id: true,
                                    name: true,
                                    isLocationManaged: true,
                                },
                            },
                            {
                                filter: { workOrder: { _id: tracking.workOrderId } },
                            },
                        ),
                    },
                    `${tracking.workOrderId}`,
                )
                .execute();

            const workOrderDocument = {
                _id: workOrderDoc._id,
                number: workOrderDoc.number,
                status: workOrderDoc.status,
                startDate: workOrderDoc.startDate,
                site: workOrderDoc.site,
            };
            this.workOrder.addOrUpdateRecordValue(workOrderDocument);

            workOrderDoc.materialTrackings.query.edges.forEach(edge => {
                this.materialTracking.addOrUpdateRecordValue(edge.node);
            });
            workOrderDoc.productionTrackings.query.edges.forEach(edge => {
                this.productionTracking.addOrUpdateRecordValue(edge.node);
            });
            workOrderDoc.timeTrackings.query.edges.forEach(edge => {
                this.timeTracking.addOrUpdateRecordValue(edge.node);
            });
        }

        this.$.setPageClean();
    }

    @ui.decorators.tableField<BillOfMaterialExtension, WorkOrder>({
        title: 'Work order',
        isTitleHidden: true,
        canSelect: false,
        pageSize: 10,
        isTransient: true,
        node: '@sage/xtrem-manufacturing/WorkOrder',
        width: 'large',
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.link({
                title: 'Number',
                bind: 'number',
                onClick(_id) {
                    this.$.router.goTo(`@sage/xtrem-manufacturing/WorkOrder`, { _id });
                },
            }),
            ui.nestedFields.date({
                title: 'Date',
                bind: 'startDate',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Site',
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'id',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({
                        title: 'ID',
                        bind: 'id',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text({
                        title: 'Name',
                        bind: 'name',
                        isReadOnly: true,
                    }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'status',
                optionType: '@sage/xtrem-stock-data/WorkOrderStatus',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('WorkOrderStatus', rowData?.status),
            }),
        ],
    })
    workOrder: ui.fields.Table<WorkOrder>;

    @ui.decorators.tableField<BillOfMaterialExtension, MaterialTracking>({
        title: 'Material tracking',
        isTitleHidden: true,
        canSelect: false,
        pageSize: 10,
        isTransient: true,
        node: '@sage/xtrem-manufacturing/MaterialTracking',
        width: 'large',
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.link({
                title: 'Number',
                bind: 'number',
                onClick(_id) {
                    this.$.router.goTo(`@sage/xtrem-manufacturing/MaterialTrackingInquiry`, { _id });
                },
            }),
            ui.nestedFields.date({
                title: 'Date',
                bind: 'entryDate',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Site',
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'id',
                isReadOnly: true,
                columns: [ui.nestedFields.text({ bind: 'id' }), ui.nestedFields.text({ bind: 'name' })],
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData?.stockTransactionStatus,
                    ),
            }),
        ],
    })
    materialTracking: ui.fields.Table<MaterialTracking>;

    @ui.decorators.tableField<BillOfMaterialExtension, ProductionTracking>({
        title: 'Production tracking',
        isTitleHidden: true,
        canSelect: false,
        pageSize: 10,
        isTransient: true,
        node: '@sage/xtrem-manufacturing/ProductionTracking',
        width: 'large',
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.link({
                title: 'Number',
                bind: 'number',
                onClick(_id) {
                    this.$.router.goTo(`@sage/xtrem-manufacturing/ProductionTrackingInquiry`, { _id });
                },
            }),
            ui.nestedFields.date({
                title: 'Date',
                bind: 'entryDate',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Site',
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'id',
                isReadOnly: true,
                columns: [ui.nestedFields.text({ bind: 'id' }), ui.nestedFields.text({ bind: 'name' })],
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData?.stockTransactionStatus,
                    ),
            }),
        ],
    })
    productionTracking: ui.fields.Table<ProductionTracking>;

    @ui.decorators.tableField<BillOfMaterialExtension, OperationTracking>({
        title: 'Time tracking',
        isTitleHidden: true,
        canSelect: false,
        pageSize: 10,
        isTransient: true,
        node: '@sage/xtrem-manufacturing/OperationTracking',
        width: 'large',
        columns: [
            ui.nestedFields.technical({
                bind: '_id',
            }),
            ui.nestedFields.link({
                title: 'Number',
                bind: 'number',
                onClick(_id) {
                    this.$.router.goTo(`@sage/xtrem-manufacturing/TimeTrackingInquiry`, { _id });
                },
            }),
            ui.nestedFields.date({
                title: 'Date',
                bind: 'entryDate',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Site',
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'id',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({
                        title: 'ID',
                        bind: 'id',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text({
                        title: 'Name',
                        bind: 'name',
                        isReadOnly: true,
                    }),
                ],
            }),
        ],
    })
    timeTracking: ui.fields.Table<OperationTracking>;
}

declare module '@sage/xtrem-technical-data/build/lib/pages/bill-of-material' {
    export interface BillOfMaterial extends BillOfMaterialExtension {}
}
