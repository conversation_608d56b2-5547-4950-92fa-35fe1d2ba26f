import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';

export const billOFMaterialExtension = new ActivityExtension({
    extends: xtremTechnicalData.activities.billOfMaterial,
    __filename,
    permissions: [],
    operationGrants: {
        manage: [...xtremStockData.functions.stockDetailLib.stockReceiptOperations],
    },
});
