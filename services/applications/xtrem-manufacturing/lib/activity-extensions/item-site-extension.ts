import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';

export const itemSiteExtension = new ActivityExtension({
    extends: xtremMasterData.activities.itemSite,
    __filename,
    permissions: [],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [() => xtremTechnicalData.nodes.BillOfMaterial, () => xtremTechnicalData.nodes.Routing],
            },
        ],
        create: [
            {
                operations: ['lookup'],
                on: [() => xtremTechnicalData.nodes.BillOfMaterial, () => xtremTechnicalData.nodes.Routing],
            },
        ],
        update: [
            {
                operations: ['lookup'],
                on: [() => xtremTechnicalData.nodes.BillOfMaterial, () => xtremTechnicalData.nodes.Routing],
            },
        ],
        delete: [
            {
                operations: ['lookup'],
                on: [() => xtremTechnicalData.nodes.BillOfMaterial, () => xtremTechnicalData.nodes.Routing],
            },
        ],
    },
});
