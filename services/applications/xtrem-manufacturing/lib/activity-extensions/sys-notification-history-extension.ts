import * as xtremCommunication from '@sage/xtrem-communication';
import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremManufacturing from '../index';

export const sysNotificationHistoryExtension = new ActivityExtension({
    extends: xtremCommunication.activities.sysNotificationHistory,
    __filename,
    permissions: [],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [
                    () => xtremManufacturing.nodes.MaterialTracking,
                    () => xtremManufacturing.nodes.ProductionTracking,
                ],
            },
        ],
    },
});
