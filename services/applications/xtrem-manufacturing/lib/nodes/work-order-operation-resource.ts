import type { Collection, datetime, decimal, integer, Reference } from '@sage/xtrem-core';
import { asyncArray, decorators, Node, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import * as xtremManufacturing from '../../index';

@decorators.node<WorkOrderOperationResource>({
    package: 'xtrem-manufacturing',
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
    isCustomizable: true,
    indexes: [{ orderBy: { workOrderOperation: +1, resource: +1 }, isUnique: true }],
    async controlEnd(cx) {
        if (this.$.status === NodeStatus.modified && (await this.isAdded) !== (await (await this.$.old).isAdded)) {
            cx.error.addLocalized(
                '@sage/xtrem-manufacturing/nodes__work_order_operation_resource__isAdded_not_changeable',
                'The isAdded property cannot be changed.',
            );
        }

        // something is changed for a resource of an already and still excluded operation
        const operation = await this.workOrderOperation;
        if (
            this.$.status === NodeStatus.modified &&
            (await (await operation.$.old).status) === 'excluded' &&
            (await operation.status) === (await (await operation.$.old).status) &&
            !(await (
                await operation.workOrder
            ).forceUpdateForScheduling)
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-manufacturing/nodes__work_order_operation_resource__excluded_resource_not_changeable',
                'The resources for the excluded operation with number {{operationNumber}} cannot be modified.',
                { operationNumber: await operation.operationNumber },
            );
        }
        if ((await this.resource) instanceof xtremMasterData.nodes.GroupResource) {
            if ((await this.resources.length) === 0) {
                cx.error.addLocalized(
                    '@sage/xtrem-manufacturing/nodes__work_order_operation_resource__empty_group_resource',
                    'Enter the resources for the {{group}} group.',
                    { group: await (await this.resource).name },
                );
            } else if (
                await asyncArray(await this.resources.toArray()).some(
                    async woOperationResourceDetail =>
                        (await (await woOperationResourceDetail.resource).resourceGroup)?._id !==
                        (await this.resource)._id,
                )
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-manufacturing/nodes__work_order_operation_resource__incoherent_group_resource',
                    'The detailed resource does not belong to the {{group}} group.',
                    { group: await (await this.resource).name },
                );
            }
        }

        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
    async saveBegin() {
        if (this.$.status === NodeStatus.added) {
            await this.$.set({
                isAdded: (await (await this.workOrderOperation).workOrder).$.status !== NodeStatus.added,
            });
        }

        // default dimension and attributes on resource from released item if dimension and attributes null
        const storedAttributes = (await this.storedAttributes) ? await this.storedAttributes : {};
        const storedDimensions = (await this.storedDimensions) ? await this.storedDimensions : {};
        const currentReleasedItemAttribute = await (
            await (await (await this.workOrderOperation).workOrder).productionItems.elementAt(0)
        ).storedAttributes;
        const currentReleasedItemDimension = await (
            await (await (await this.workOrderOperation).workOrder).productionItems.elementAt(0)
        ).storedDimensions;

        if (
            storedAttributes !== null &&
            Object.keys(storedAttributes).length === 0 &&
            storedDimensions !== null &&
            Object.keys(storedDimensions).length === 0 &&
            this.$.status === NodeStatus.added
        ) {
            await this.$.set({
                storedAttributes: currentReleasedItemAttribute,
                storedDimensions: currentReleasedItemDimension,
            });
        }
    },
    async deleteBegin() {
        (await (await this.workOrderOperation).workOrder).isOperationDeleted = true;
    },
})
export class WorkOrderOperationResource extends Node {
    @decorators.referenceProperty<WorkOrderOperationResource, 'workOrderOperation'>({
        isStored: true,
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrderOperation,
        isVitalParent: true,
        lookupAccess: true,
    })
    readonly workOrderOperation: Reference<xtremManufacturing.nodes.WorkOrderOperation>;

    @decorators.referenceProperty<WorkOrderOperationResource, 'workOrder'>({
        isPublished: true,
        dependsOn: ['workOrderOperation'],
        node: () => xtremManufacturing.nodes.WorkOrder,
        lookupAccess: true,
        async getValue() {
            return (await this.workOrderOperation).workOrder;
        },
    })
    readonly workOrder: Reference<xtremManufacturing.nodes.WorkOrder>;

    @decorators.referenceProperty<WorkOrderOperationResource, 'resource'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.BaseResource,
        async control(cx, val) {
            await xtremTechnicalData.events.control.operationResourceControls.operationResourceGroupValidation(
                await (
                    await (
                        await (
                            await this.workOrderOperation
                        ).workOrder
                    ).site
                ).id,
                cx,
                val,
            );
        },
    })
    readonly resource: Reference<xtremMasterData.nodes.BaseResource>;

    @decorators.decimalProperty<WorkOrderOperationResource, 'efficiency'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['resource'],
        dataType: () => xtremMasterData.dataTypes.efficiencyPercentage,
        async defaultValue() {
            return (await this.resource).efficiency;
        },
    })
    readonly efficiency: Promise<decimal>;

    @decorators.referenceProperty<WorkOrderOperationResource, 'setupTimeUnit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: ['workOrderOperation'],
        async getValue() {
            return (await this.workOrderOperation).setupTimeUnit;
        },
    })
    readonly setupTimeUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referenceProperty<WorkOrderOperationResource, 'runTimeUnit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: ['workOrderOperation'],
        async getValue() {
            return (await this.workOrderOperation).runTimeUnit;
        },
        lookupAccess: true,
    })
    readonly runTimeUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<WorkOrderOperationResource, 'expectedRunTime'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.runTimeDataType,
        async control(cx, val: decimal) {
            await cx.error.if(val).is.negative();
        },
        lookupAccess: true,
    })
    readonly expectedRunTime: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperationResource, 'expectedSetupTime'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.setupTimeDataType,
        async control(cx, val: decimal) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly expectedSetupTime: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperationResource, 'actualRunTime'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.runTimeDataType,
        async control(cx, val: decimal) {
            await cx.error.if(val).is.negative();
        },
        duplicatedValue: 0,
    })
    readonly actualRunTime: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperationResource, 'actualSetupTime'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.setupTimeDataType,
        async control(cx, val: decimal) {
            await cx.error.if(val).is.negative();
        },
        duplicatedValue: 0,
    })
    readonly actualSetupTime: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperationResource, 'expectedCost'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['expectedRunCost', 'expectedSetupCost'],
        async getValue() {
            return (await this.expectedRunCost) + (await this.expectedSetupCost);
        },
    })
    readonly expectedCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperationResource, 'expectedRunCost'>({
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['resource', 'expectedRunTime', 'status', 'runTimeUnit'],

        defaultValue() {
            return this.getExpectedCost(false);
        },
        // The expectedRunCost must be updated only on demand
    })
    readonly expectedRunCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperationResource, 'expectedSetupCost'>({
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['resource', 'expectedSetupTime', 'status', 'setupTimeUnit'],

        defaultValue() {
            return this.getExpectedCost(true);
        },
        // The expectedSetupCost must be updated only on demand
    })
    readonly expectedSetupCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperationResource, 'actualCost'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['actualRunCost', 'actualSetupCost'],
        async getValue() {
            return (await this.actualRunCost) + (await this.actualSetupCost);
        },
    })
    readonly actualCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperationResource, 'actualRunCost'>({
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        duplicatedValue: 0,
    })
    readonly actualRunCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperationResource, 'actualSetupCost'>({
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        duplicatedValue: 0,
    })
    readonly actualSetupCost: Promise<decimal>;

    @decorators.integerProperty<WorkOrderOperationResource, 'resourceNumber'>({
        isPublished: true,
        lookupAccess: true,
        getValue() {
            return this.resources.length;
        },
    })
    readonly resourceNumber: Promise<integer>;

    @decorators.collectionProperty<WorkOrderOperationResource, 'resources'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremManufacturing.nodes.WorkOrderOperationResourceDetail,
        isVital: true,
        reverseReference: 'workOrderOperationResource',
    })
    readonly resources: Collection<xtremManufacturing.nodes.WorkOrderOperationResourceDetail>;

    @decorators.enumProperty<WorkOrderOperationResource, 'status'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremManufacturing.enums.operationStatusDataType,
        defaultValue: 'pending',
        async duplicatedValue() {
            return (await this.status) !== 'excluded' ? 'pending' : this.status;
        },
    })
    readonly status: Promise<xtremManufacturing.enums.OperationStatus>;

    // technical property to allow filter on status
    @decorators.enumProperty<WorkOrderOperationResource, 'uStatus'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremManufacturing.enums.workOrderLineStatusFilteredDataType,
        async getValue() {
            if (['included', 'inProgress', 'pending'].includes(await this.status)) {
                return (await this.status) as xtremManufacturing.enums.WorkOrderLineStatusFiltered;
            }
            return null;
        },
    })
    readonly uStatus: Promise<xtremManufacturing.enums.WorkOrderLineStatusFiltered | null>;

    @decorators.booleanProperty<WorkOrderOperationResource, 'isAdded'>({
        isStored: true,
        lookupAccess: true,
        isPublished: true,
    })
    readonly isAdded: Promise<boolean>;

    @decorators.decimalProperty<WorkOrderOperationResource, 'completedTimePercentage'>({
        async computeValue() {
            return (
                (100 *
                    ((await xtremMasterData.functions.convertToSeconds(
                        await this.setupTimeUnit,
                        await this.actualSetupTime,
                    )) +
                        (await xtremMasterData.functions.convertToSeconds(
                            await this.runTimeUnit,
                            await this.actualRunTime,
                        )))) /
                ((await xtremMasterData.functions.convertToSeconds(
                    await this.setupTimeUnit,
                    await this.expectedSetupTime,
                )) +
                    (await xtremMasterData.functions.convertToSeconds(
                        await this.runTimeUnit,
                        await this.expectedRunTime,
                    )))
            );
        },
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.percentageWorkOrderDataType,
        lookupAccess: true,
    })
    readonly completedTimePercentage: Promise<decimal>;

    @decorators.booleanProperty<WorkOrderOperationResource, 'isResourceQuantity'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: false,
        async control(cx, val) {
            if ((await this.expectedRunTime) === 0 && val === true) {
                cx.warn.add(
                    cx.localize(
                        '@sage/xtrem-manufacturing/nodes__work_order_operation_resource__run_time_different_than_0_to_flag',
                        'Run Time should be greater than 0 if isResourceQuantity is true.',
                    ),
                );
            }
        },
    })
    readonly isResourceQuantity: Promise<boolean>;

    @decorators.jsonProperty<WorkOrderOperationResource, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<WorkOrderOperationResource, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<WorkOrderOperationResource, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<WorkOrderOperationResource, 'computedAttributes'>({
        isPublished: true,
        dependsOn: ['resource'],
        lookupAccess: true,
        async computeValue() {
            return xtremFinanceData.functions.computeGenericAttributes(this.$.context, {
                financialSite: await (await this.resource).site,
                stockSite: await (await this.resource).site,
                returnBusinessSite: false,
            });
        },
    })
    readonly computedAttributes: Promise<object>;

    @decorators.datetimeProperty<WorkOrderOperationResource, 'startDatetime'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        duplicatedValue: null,
    })
    readonly startDatetime: Promise<datetime | null>;

    @decorators.datetimeProperty<WorkOrderOperationResource, 'endDatetime'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        async control(cx, val) {
            xtremMasterData.events.control.timeControls.checkDatetimeRange(cx, await this.startDatetime, val);
        },
        duplicatedValue: null,
    })
    readonly endDatetime: Promise<datetime | null>;

    async getExpectedCost(setupCost: boolean) {
        if ((await this.status) === 'included') {
            return 0;
        }

        const time = setupCost ? await this.expectedSetupTime : await this.expectedRunTime;
        const unit = setupCost ? await this.setupTimeUnit : await this.runTimeUnit;
        const operationCost = setupCost
            ? xtremTechnicalData.functions.operationFunctions.operationSetupCost
            : xtremTechnicalData.functions.operationFunctions.operationRunCost;

        return operationCost(time, await this.resource, unit);
    }
}
