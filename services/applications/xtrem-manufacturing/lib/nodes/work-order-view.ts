import type { Collection, Context } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremManufacturing from '../../index';

@decorators.node<WorkOrderView>({
    isClearedByReset: true,
    package: 'xtrem-manufacturing',
    storage: 'sql',
    isPublished: true,
    canRead: true,
})
export class WorkOrderView extends Node {
    @decorators.collectionProperty<WorkOrderView, 'workOrders'>({
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrder,
        getFilter() {
            return {};
        },
    })
    readonly workOrders: Collection<xtremManufacturing.nodes.WorkOrder>;

    // TODO: review this function to avoid the need of a record
    // XT-38194 create default work order view if not existing yet
    @decorators.mutation<typeof WorkOrderView, 'defaultRecord'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'string',
            name: '_id',
        },
    })
    static async defaultRecord(context: Context): Promise<string> {
        let workOrderView = (await context.query(WorkOrderView).toArray())[0];
        if (!workOrderView) {
            workOrderView = await context.create(WorkOrderView, {});
            await workOrderView.$.save();
        }
        return workOrderView._id.toString();
    }
}
