import type { Collection, Context, decimal, integer, JsonType, NodeCreateData, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremManufacturing from '../../index';

@decorators.subNode<MaterialTrackingLine>({
    isPublished: true,
    canRead: true,
    isVitalCollectionChild: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    async controlDelete(cx) {
        if (!(await this.canBeUpdated()))
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize(
                    '@sage/xtrem-manufacturing/nodes__material_tracking_line__deletion_forbidden',
                    'This material tracking line cannot be deleted.',
                ),
            );
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class MaterialTrackingLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements
        xtremFinanceData.interfaces.MaterialTrackingFinanceDocumentLine,
        xtremStockData.interfaces.DocumentLineWithStockAllocation
{
    /**
     * Indicates if a property can be updated
     * @param propertyName The property name or nothing for the node itself
     * @returns
     */
    async canBeUpdated(propertyName?: string): Promise<boolean> {
        if ([undefined, 'stockTransactionStatus'].includes(propertyName)) {
            return (await this.stockTransactionStatus) !== 'completed';
        }

        if (['completed', 'quantityInStockUnit'].includes(propertyName || '')) {
            return ['draft'].includes(await this.stockTransactionStatus);
        }

        if (['storedAttributes', 'storedDimensions'].includes(propertyName || '')) {
            return ['draft'].includes(await this.stockTransactionStatus) || (await this.document).forceUpdateForFinance;
        }

        return false;
    }

    async getWorkOrderDocumentLine(): Promise<xtremStockData.interfaces.DocumentLineWithStockAllocation | null> {
        return (await this.workOrderLine) || null;
    }

    @decorators.referenceProperty<MaterialTrackingLine, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        isFrozen: true,
        node: () => xtremManufacturing.nodes.MaterialTracking,
    })
    override readonly document: Reference<xtremManufacturing.nodes.MaterialTracking>;

    @decorators.stringPropertyOverride<MaterialTrackingLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<MaterialTrackingLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumProperty<MaterialTrackingLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        async isFrozen() {
            return !(await this.canBeUpdated('stockTransactionStatus'));
        },
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.enumProperty<MaterialTrackingLine, 'allocationStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        dependsOn: ['remainingQuantityToAllocate', 'quantityAllocated', 'item'],
        async getValue() {
            return xtremStockData.functions.allocationLib.getLineAllocationStatus(
                await this.remainingQuantityToAllocate,
                await this.quantityAllocated,
                (await (await this.item)?.isStockManaged) ?? false,
            );
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    @decorators.integerProperty<MaterialTrackingLine, 'line'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        isFrozen: true,
        dependsOn: ['workOrderLine'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        async defaultValue() {
            return (await this.workOrderLine).componentNumber;
        },
    })
    readonly line: Promise<integer>;

    @decorators.referenceProperty<MaterialTrackingLine, 'workOrderLine'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremManufacturing.nodes.WorkOrderComponent,
        async control(cx) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-manufacturing/nodes__material_tracking__work_order_type_must_comply',
                    'The work order component status must be different from Excluded.',
                )
                .if(await (await this.workOrderLine).lineStatus)
                .is.equal.to('excluded');

            await cx.error
                .withMessage(
                    '@sage/xtrem-manufacturing/nodes__material_tracking__allocation_request_in_progress',
                    'You can only track the component {{itemName}} after the allocation request is complete.',
                    async () => ({ itemName: await (await (await this.workOrderLine).item)?.name }),
                )
                .if(await (await this.workOrderLine).allocationRequestStatus)
                .is.equal.to('inProgress');
        },
    })
    readonly workOrderLine: Reference<xtremManufacturing.nodes.WorkOrderComponent>;

    @decorators.stringProperty<MaterialTrackingLine, 'materialType'>({
        isStored: true,
        isPublished: true,
        defaultValue: 'WO',
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.code,
    })
    readonly materialType: Promise<string>;

    @decorators.decimalProperty<MaterialTrackingLine, 'quantityInStockUnit'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        async isFrozen() {
            return !(await this.canBeUpdated('quantityInStockUnit'));
        },
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.booleanProperty<MaterialTrackingLine, 'isActiveQuantity'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
    })
    readonly isActiveQuantity: Promise<boolean>;

    /** To be deleted // replaced by stock allocations   */
    @decorators.collectionProperty<MaterialTrackingLine, 'stockDetails'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        dependsOn: ['itemSite'],
        node: () => xtremStockData.nodes.StockIssueDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockIssueDetail>;

    @decorators.collectionProperty<MaterialTrackingLine, 'stockMovements'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockJournal,
    })
    readonly stockMovements: Collection<xtremStockData.nodes.StockJournal>;

    @decorators.collectionProperty<MaterialTrackingLine, 'stockAllocations'>({
        isPublished: true,
        reverseReference: 'documentLine',
        dependsOn: ['itemSite'],
        node: () => xtremStockData.nodes.StockAllocation,
    })
    readonly stockAllocations: Collection<xtremStockData.nodes.StockAllocation>;

    @decorators.decimalProperty<MaterialTrackingLine, 'quantityAllocated'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['stockAllocations'],
        getValue() {
            return this.stockAllocations.where().sum(line => line.quantityInStockUnit);
        },
    })
    readonly quantityAllocated: Promise<decimal>;

    @decorators.decimalProperty<MaterialTrackingLine, 'remainingQuantityToAllocate'>({
        isPublished: true,
        dependsOn: ['quantityInStockUnit', 'quantityAllocated'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async getValue() {
            return (await this.quantityInStockUnit) - (await this.quantityAllocated);
        },
    })
    readonly remainingQuantityToAllocate: Promise<decimal>;

    @decorators.jsonProperty<MaterialTrackingLine, 'jsonStockDetails'>({
        isPublished: true,
        dependsOn: ['stockDetails'],
        async setValue(details) {
            if (details && details.length > 0) {
                await this.$.set({
                    stockDetails: xtremStockData.functions.stockDetailLib.parseDetails(details),
                });
            }
        },
        async computeValue() {
            if (this.stockDetails && (await this.stockDetails.length) > 0) {
                return this.stockDetails.map(detail =>
                    xtremStockData.functions.stockDetailLib.filterStockDetailProperties(
                        detail,
                        xtremStockData.nodes.StockIssueDetail,
                        {
                            onlyIds: true,
                        },
                    ),
                );
            }
            return {} as any; // forcing typing to accept an empty object
        },
    })
    readonly jsonStockDetails: Promise<Array<JsonType<NodeCreateData<xtremStockData.nodes.StockIssueDetail>>>>;

    @decorators.collectionProperty<MaterialTrackingLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    @decorators.booleanProperty<MaterialTrackingLine, 'completed'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        async isFrozen() {
            return !(await this.canBeUpdated('completed'));
        },
    })
    readonly completed: Promise<boolean>;

    @decorators.jsonProperty<MaterialTrackingLine, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        async isFrozen() {
            return !(await this.canBeUpdated('storedDimensions'));
        },
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<MaterialTrackingLine, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<MaterialTrackingLine, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        async isFrozen() {
            return !(await this.canBeUpdated('storedAttributes'));
        },
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<MaterialTrackingLine, 'computedAttributes'>({
        isPublished: true,
        dependsOn: ['workOrderLine'],
        async computeValue() {
            const workOrder = await (await this.workOrderLine).workOrder;
            return xtremFinanceData.functions.computeGenericAttributes(this.$.context, {
                item: await (await this.workOrderLine).item,
                stockSite: await workOrder.site,
                financialSite: await workOrder.site,
                returnBusinessSite: false,
            });
        },
    })
    readonly computedAttributes: Promise<object>;

    @decorators.collectionProperty<MaterialTrackingLine, 'workInProgressCosts'>({
        isPublished: true,
        reverseReference: 'originatingLine',
        node: () => xtremManufacturing.nodes.WorkInProgressCost,
    })
    readonly workInProgressCosts: Collection<xtremManufacturing.nodes.WorkInProgressCost>;

    @decorators.referenceProperty<MaterialTrackingLine, 'workInProgressCost'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremManufacturing.nodes.WorkInProgressCost,
        getValue() {
            return this.workInProgressCosts.takeOne(wipCost => wipCost != null);
        },
    })
    readonly workInProgressCost: Reference<xtremManufacturing.nodes.WorkInProgressCost | null>;

    /**  reference to wip cost of line for the accounting interface */
    @decorators.decimalProperty<MaterialTrackingLine, 'workInProgressCostAmount'>({
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return (await (await this.workInProgressCost)?.amount) ?? 0;
        },
    })
    readonly workInProgressCostAmount: Promise<decimal>;

    /**  reference to item for the accounting interface */
    @decorators.referenceProperty<MaterialTrackingLine, 'item'>({
        isPublished: true,
        async getValue() {
            return (await this.workOrderLine).item;
        },
        node: () => xtremMasterData.nodes.Item,
        isNullable: true,
    })
    readonly item: Reference<xtremMasterData.nodes.Item | null>;

    // work order number for the accounting interface
    @decorators.stringProperty<MaterialTrackingLine, 'workInProgressWorkOrderNumber'>({
        isPublished: true,
        async getValue() {
            return (await (await this.workOrderLine).workOrder).number;
        },
    })
    readonly workInProgressWorkOrderNumber: Promise<string>;

    // work order _id for the accounting interface
    @decorators.integerProperty<MaterialTrackingLine, 'workInProgressWorkOrderSysId'>({
        isPublished: true,
        async getValue() {
            return (await (await this.workOrderLine).document)._id;
        },
    })
    readonly workInProgressWorkOrderSysId: Promise<integer>;

    /** reference to item-site for the stock service */
    @decorators.referenceProperty<MaterialTrackingLine, 'itemSite'>({
        isPublished: true,
        isNullable: true,
        dependsOn: ['item', { document: ['site'] }],
        node: () => xtremMasterData.nodes.ItemSite,
        join: {
            item() {
                return this.item;
            },
            async site() {
                return (await this.document).site;
            },
        },
    })
    readonly itemSite: Reference<xtremMasterData.nodes.ItemSite | null>;

    getOrderCost(): Promise<decimal> {
        return xtremStockData.functions.stockDocumentLib.defaultDocumentLineCost(
            (this as xtremMasterData.nodes.BaseDocumentLine).$.context,
            this,
            {
                valuationType: 'issue',
            },
        );
    }

    getValuedCost(): Promise<decimal> {
        return this.getOrderCost();
    }

    // eslint-disable-next-line class-methods-use-this
    getValuationParameters(): xtremStockData.interfaces.ValuationParameter {
        return { valuationType: 'issue' };
    }

    async getItem(): Promise<xtremMasterData.nodes.Item | null> {
        return (await this.workOrderLine).item;
    }

    /**
     * Update linked component in the work order
     * Create WorkInProgressCost record
     * @param context
     * @param documentLine {MaterialTrackingLine}
     */
    static async afterLineSaving(context: Context, documentLine: MaterialTrackingLine): Promise<void> {
        // We issue material so the value is negative.
        const issuedOrderAmount = -(
            await xtremStockData.functions.stockValuationLib.getDocumentLineStockValue(context, documentLine._id)
        ).orderAmount;
        const issuedCost = issuedOrderAmount / (await documentLine.quantityInStockUnit);

        const workOrderLine = await documentLine.workOrderLine;
        await xtremManufacturing.nodes.WorkOrderComponent.updateComponent(
            context,
            await (
                await workOrderLine.workOrder
            ).number,
            await workOrderLine.componentNumber,
            await documentLine.quantityInStockUnit,
            issuedCost,
            await documentLine.completed,
        );

        const wip = await context.create(xtremManufacturing.nodes.WorkInProgressCost, {
            originatingLine: documentLine,
            workOrder: await workOrderLine.workOrder,
            quantity: await documentLine.quantityInStockUnit,
            cost: issuedCost,
            amount: issuedOrderAmount,
            status: 'pending',
            type: 'materialTracking',
            unit: await (await workOrderLine.item)!.stockUnit,
            currency: await (await (await (await workOrderLine.workOrder).site).legalCompany).currency,
            effectiveDate: await (await documentLine.document).entryDate,
        });
        await wip.$.save();
    }
}
