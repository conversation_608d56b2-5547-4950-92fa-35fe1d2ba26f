import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremManufacturing from '../../index';

@decorators.node<WorkOrderOperationResourceDetail>({
    package: 'xtrem-manufacturing',
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isCustomizable: true,
    indexes: [{ orderBy: { workOrderOperationResource: +1, resource: +1 }, isUnique: true }],
})
export class WorkOrderOperationResourceDetail extends Node {
    @decorators.referenceProperty<WorkOrderOperationResourceDetail, 'workOrderOperationResource'>({
        isStored: true,
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrderOperationResource,
        isVitalParent: true,
        lookupAccess: true,
    })
    readonly workOrderOperationResource: Reference<xtremManufacturing.nodes.WorkOrderOperationResource>;

    @decorators.referenceProperty<WorkOrderOperationResourceDetail, 'resource'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.DetailedResource,
        lookupAccess: true,
    })
    readonly resource: Reference<xtremMasterData.nodes.DetailedResource>;
}
