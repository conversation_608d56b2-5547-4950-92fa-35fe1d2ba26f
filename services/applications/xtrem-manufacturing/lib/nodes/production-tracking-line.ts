import type { Collection, Context, decimal, integer, JsonType, NodeCreateData, Reference } from '@sage/xtrem-core';
import { date, decorators, Logger, useDefaultValue, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremManufacturing from '../index';

const logger = Logger.getLogger(__filename, 'production-tracking');

interface QuantityProducedResult {
    group: {
        document: {
            effectiveDate: date;
        };
    };
    values: {
        releasedQuantity: {
            sum: decimal;
        };
    };
}

@decorators.subNode<ProductionTrackingLine>({
    isPublished: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    canRead: true,
    isVitalCollectionChild: true,
    async controlDelete(cx) {
        if (!(await this.canBeUpdated())) {
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize(
                    '@sage/xtrem-manufacturing/nodes__production_tracking_line__deletion_forbidden',
                    'This production tracking line cannot be deleted.',
                ),
            );
        }
    },
    async controlEnd(cx) {
        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class ProductionTrackingLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements xtremFinanceData.interfaces.WorkInProgressFinanceDocumentLine
{
    /**
     * Indicates if a property can be updated
     * @param propertyName The property name or nothing for the node itself
     * @returns
     */
    async canBeUpdated(propertyName?: string): Promise<boolean> {
        if (!propertyName) {
            return (await this.stockTransactionStatus) !== 'completed';
        }

        if (['valuedCost', 'orderCost', 'completed', 'releasedQuantity'].includes(propertyName)) {
            return ['draft'].includes(await this.stockTransactionStatus);
        }

        if (['storedAttributes', 'storedDimensions'].includes(propertyName || '')) {
            return ['draft'].includes(await this.stockTransactionStatus) || (await this.document).forceUpdateForFinance;
        }

        return false;
    }

    getOrderCost(): Promise<decimal> {
        return this.orderCost;
    }

    getValuedCost(): Promise<decimal> {
        return this.valuedCost;
    }

    // eslint-disable-next-line class-methods-use-this
    getValuationParameters(): xtremStockData.interfaces.ValuationParameter {
        // change valuation is done like issues for the issue and the receipt
        return { valuationType: 'receipt' };
    }

    async getItem(): Promise<xtremMasterData.nodes.Item> {
        return (await this.workOrderLine).releasedItem;
    }

    get quantityInStockUnit(): Promise<decimal> {
        return this.releasedQuantity;
    }

    async getWorkOrderDocumentLine(): Promise<xtremManufacturing.nodes.WorkOrderReleasedItem | null> {
        return (await this.workOrderLine) || null;
    }

    @decorators.referenceProperty<ProductionTrackingLine, 'document'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremManufacturing.nodes.ProductionTracking,
        isVitalParent: true,
    })
    override readonly document: Reference<xtremManufacturing.nodes.ProductionTracking>;

    @decorators.stringPropertyOverride<ProductionTrackingLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<ProductionTrackingLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumProperty<ProductionTrackingLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.integerProperty<ProductionTrackingLine, 'line'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
    })
    readonly line: Promise<integer>;

    // TODO: ENUM
    @decorators.stringProperty<ProductionTrackingLine, 'type'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.code,
        defaultValue: 'WO',
    })
    readonly type: Promise<string>;

    @decorators.referenceProperty<ProductionTrackingLine, 'workOrderLine'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremManufacturing.nodes.WorkOrderReleasedItem,
    })
    readonly workOrderLine: Reference<xtremManufacturing.nodes.WorkOrderReleasedItem>;

    @decorators.decimalProperty<ProductionTrackingLine, 'releasedQuantity'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        async isFrozen() {
            return !(await this.canBeUpdated('releasedQuantity'));
        },
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        async control(cx, val) {
            await cx.error.if(val).is.not.greater.than(0);
        },
    })
    readonly releasedQuantity: Promise<decimal>;

    @decorators.booleanProperty<ProductionTrackingLine, 'completed'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        async isFrozen() {
            return !(await this.canBeUpdated('completed'));
        },
    })
    readonly completed: Promise<boolean>;

    @decorators.decimalProperty<ProductionTrackingLine, 'orderCost'>({
        isStored: true,
        isPublished: true,
        async isFrozen() {
            return !(await this.canBeUpdated('orderCost'));
        },
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['workOrderLine', 'releasedQuantity'],
        async defaultValue() {
            return (await (await this.workOrderLine).getTotalPlannedCost()).unitCost;
        },
    })
    readonly orderCost: Promise<decimal>;

    @decorators.decimalProperty<ProductionTrackingLine, 'valuedCost'>({
        isStored: true,
        isPublished: true,
        async isFrozen() {
            return !(await this.canBeUpdated('valuedCost'));
        },
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['workOrderLine'],

        async defaultValue() {
            const workOrderLine = await this.workOrderLine;
            const itemSite: xtremMasterData.nodes.ItemSite = await this.$.context.read(xtremMasterData.nodes.ItemSite, {
                item: await workOrderLine.releasedItem,
                site: await (await workOrderLine.document).site,
            });
            return (await itemSite?.currentCost) || 0;
        },
    })
    readonly valuedCost: Promise<decimal>;

    @decorators.jsonProperty<ProductionTrackingLine, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        async isFrozen() {
            return !(await this.canBeUpdated('storedDimensions'));
        },
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<ProductionTrackingLine, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<ProductionTrackingLine, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        async isFrozen() {
            return !(await this.canBeUpdated('storedAttributes'));
        },
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<ProductionTrackingLine, 'computedAttributes'>({
        isPublished: true,
        async isFrozen() {
            return !(await this.canBeUpdated('computedAttributes'));
        },
        async computeValue() {
            const workOrderLine = await this.workOrderLine;
            const workOrderLineDocument = await workOrderLine.document;
            return xtremFinanceData.functions.computeGenericAttributes(this.$.context, {
                item: await workOrderLine.releasedItem,
                stockSite: await workOrderLineDocument.site,
                financialSite: await workOrderLineDocument.site,
                returnBusinessSite: false,
            });
        },
    })
    readonly computedAttributes: Promise<object>;

    @decorators.collectionProperty<ProductionTrackingLine, 'workInProgressCosts'>({
        isPublished: true,
        reverseReference: 'originatingLine',
        node: () => xtremManufacturing.nodes.WorkInProgressCost,
    })
    readonly workInProgressCosts: Collection<xtremManufacturing.nodes.WorkInProgressCost>;

    @decorators.referenceProperty<ProductionTrackingLine, 'workInProgressCost'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremManufacturing.nodes.WorkInProgressCost,
        getValue() {
            return this.workInProgressCosts.takeOne(wipCost => wipCost != null);
        },
    })
    readonly workInProgressCost: Reference<xtremManufacturing.nodes.WorkInProgressCost | null>;

    /** reference to wip cost of line for the accounting interface */
    @decorators.decimalProperty<ProductionTrackingLine, 'workInProgressCostAmount'>({
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return (await (await this.workInProgressCost)?.amount) ?? 0;
        },
    })
    readonly workInProgressCostAmount: Promise<decimal>;

    // reference to item for the accounting interface
    @decorators.referenceProperty<ProductionTrackingLine, 'item'>({
        isPublished: true,
        async getValue() {
            return (await this.workOrderLine).releasedItem;
        },
        node: () => xtremMasterData.nodes.Item,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    // work order number for the accounting interface
    @decorators.stringProperty<ProductionTrackingLine, 'workInProgressWorkOrderNumber'>({
        isPublished: true,
        async getValue() {
            return (await (await this.workOrderLine).document).number;
        },
    })
    readonly workInProgressWorkOrderNumber: Promise<string>;

    // work order _id for the accounting interface
    @decorators.integerProperty<ProductionTrackingLine, 'workInProgressWorkOrderSysId'>({
        isPublished: true,
        async getValue() {
            return (await (await this.workOrderLine).document)._id;
        },
    })
    readonly workInProgressWorkOrderSysId: Promise<integer>;

    @decorators.collectionProperty<ProductionTrackingLine, 'stockDetails'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        dependsOn: ['orderCost', 'valuedCost'],
        node: () => xtremStockData.nodes.StockReceiptDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockReceiptDetail>;

    @decorators.jsonProperty<ProductionTrackingLine, 'jsonStockDetails'>({
        isPublished: true,
        async setValue(details) {
            if (details && details.length > 0) {
                await this.$.set({
                    stockDetails: xtremStockData.functions.stockDetailLib.parseDetails(
                        details,
                    ) as NodeCreateData<xtremStockData.nodes.StockReceiptDetail>[],
                });
            }
        },
        async computeValue() {
            if ((await this.stockDetails.length) > 0) {
                return this.stockDetails
                    .map(detail =>
                        xtremStockData.functions.stockDetailLib.filterStockDetailProperties(
                            detail,
                            xtremStockData.nodes.StockReceiptDetail,
                            {
                                onlyIds: true,
                            },
                        ),
                    )
                    .toArray();
            }
            // forcing typing to accept an empty object
            return [] as any[];
        },
    })
    readonly jsonStockDetails: Promise<Array<JsonType<NodeCreateData<xtremStockData.nodes.StockReceiptDetail>>>>;

    @decorators.collectionProperty<ProductionTrackingLine, 'stockTransactions'>({
        isPublished: true,
        reverseReference: 'documentLine',
        isVital: true,
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    /**
     * Update linked released item in the work order
     * Create WorkInProgressCost record
     * @param writableContext
     * @param documentLine {ProductionTrackingLine}
     */
    static async afterLineSaving(writableContext: Context, documentLine: ProductionTrackingLine): Promise<void> {
        const workOrderLine = await documentLine.workOrderLine;
        const workOrderLineDocument = await workOrderLine.document;
        const workOrder = await workOrderLine.document;
        const releasedQuantity = await documentLine.releasedQuantity;
        const releasedItem = await workOrderLine.releasedItem;

        await xtremManufacturing.nodes.WorkOrderReleasedItem.updateReleasedItem(
            writableContext,
            await workOrder.number,
            await releasedItem.id,
            releasedQuantity,
            await documentLine.completed,
        );

        const itemSite: xtremMasterData.nodes.ItemSite = await writableContext.read(xtremMasterData.nodes.ItemSite, {
            item: releasedItem,
            site: await workOrder.site,
        });
        const valuationMethod = await itemSite.valuationMethod;

        const cost =
            valuationMethod === 'standardCost'
                ? await documentLine.valuedCost
                : (await workOrderLine.getTotalPlannedCost()).unitCost;

        const wip = await writableContext.create(xtremManufacturing.nodes.WorkInProgressCost, {
            originatingLine: documentLine,
            workOrder,
            quantity: releasedQuantity,
            cost,
            amount: releasedQuantity * cost,
            status: 'pending',
            type: 'productionTracking',
            unit: await (await workOrderLine.releasedItem).stockUnit,
            currency: await (await (await workOrderLineDocument.site).businessEntity).currency,
            effectiveDate: await (await documentLine.document).entryDate,
        });
        await wip.$.save();

        if (await writableContext.isServiceOptionEnabled(xtremMasterData.serviceOptions.orderToOrderOption)) {
            await xtremStockData.functions.orderAssignment.allocateDemandOrderLine(
                writableContext,
                documentLine,
                workOrderLine,
            );
        }
    }

    @decorators.query<typeof ProductionTrackingLine, 'getQuantityProduced'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'object',
            properties: {
                currentMonth: 'decimal',
                previousMonth: 'decimal',
            },
        },
    })
    static async getQuantityProduced(context: Context): Promise<object> {
        let currentMonth = 0;
        let previousMonth = 0;
        const today = date.today();
        const { month } = today;
        logger.debug(() => `getQuantityProduced currentMonth=${month}`);
        let firstDayOfPreviousMonth = new Date();
        if (month === 1) {
            firstDayOfPreviousMonth = new Date(today.year - 1, 11, 1, 23);
        } else {
            // Need to minus 2 as Date has months from 0 - 11 whereas today().month has 1 - 12
            firstDayOfPreviousMonth = new Date(today.year, month - 2, 1, 23);
        }
        logger.debug(
            () =>
                `getQuantityProduced First date of previous month=${firstDayOfPreviousMonth}, parameter=${firstDayOfPreviousMonth
                    .toISOString()
                    .slice(0, 10)
                    .replace(/-/g, '')}`,
        );
        const results = context.queryAggregate(ProductionTrackingLine, {
            filter: {
                document: {
                    effectiveDate: { _gte: firstDayOfPreviousMonth.toISOString().slice(0, 10).replace(/-/g, '') },
                },
            },
            group: {
                document: { effectiveDate: { _by: 'month' } },
            },
            values: {
                releasedQuantity: { sum: true },
            },
        });
        logger.debug(() => `getQuantityProduced results=${JSON.stringify(results)}`);
        const resultsArray = await results.toArray();
        logger.debug(() => `getQuantityProduced salesResults=${JSON.stringify(resultsArray)}`);

        if (resultsArray.length) {
            const currentMonthGroup = ProductionTrackingLine.filterResults(
                resultsArray,
                new Date(today.year, today.month - 1, today.day),
            );
            const previousMonthGroup = ProductionTrackingLine.filterResults(resultsArray, firstDayOfPreviousMonth);
            logger.debug(
                () =>
                    `getQuantityProduced currentMonthGroup = ${JSON.stringify(
                        currentMonthGroup,
                    )} previousMonthGroup=${JSON.stringify(previousMonthGroup)}`,
            );
            currentMonth = currentMonthGroup?.reduce(
                (amount: number, item: any) => amount + item.values.releasedQuantity.sum,
                0,
            );
            previousMonth = previousMonthGroup?.reduce(
                (amount: number, item: any) => amount + item.values.releasedQuantity.sum,
                0,
            );
            return { currentMonth, previousMonth };
        }

        return { previousMonth: 0, currentMonth: 0 };
    }

    private static filterResults(
        results: Array<QuantityProducedResult>,
        filterDate: Date,
    ): QuantityProducedResult[] | null {
        const passedDate = filterDate.toISOString().slice(0, 7);
        return results.filter((result: QuantityProducedResult) => {
            logger.debug(() => `getQuantityProduced result = ${JSON.stringify(result)}`);
            const { effectiveDate } = result.group.document;
            logger.debug(() => `getQuantityProduced effectiveDate = ${effectiveDate}`);
            const resultDate = result.group.document.effectiveDate.toString().slice(0, 7);
            logger.debug(() => `getQuantityProduced resultDate = ${resultDate} fdocm=${passedDate}`);
            if (result.group.document.effectiveDate.toString().slice(0, 7) === passedDate) {
                return result;
            }
            return null;
        });
    }
}
