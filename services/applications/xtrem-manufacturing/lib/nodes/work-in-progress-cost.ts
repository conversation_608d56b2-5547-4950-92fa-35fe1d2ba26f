import type { Context, decimal, integer, Reference } from '@sage/xtrem-core';
import { asyncArray, date, decorators, Logger, Node } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremManufacturing from '../../index';

interface CostProducedResult {
    group: {
        effectiveDate: date;
        currency: {
            id: string;
        };
    };
    values: {
        amount: {
            sum: decimal;
        };
    };
}

interface MonthlyProductionCost {
    month: integer;
    cost: decimal;
}
const logger = Logger.getLogger(__filename, 'work-in-progress-cost');

@decorators.node<WorkInProgressCost>({
    isClearedByReset: true,
    package: 'xtrem-manufacturing',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
})
export class WorkInProgressCost
    extends Node
    implements xtremFinanceData.interfaces.WorkOrderClosingFinanceDocumentLine
{
    static readonly workInProgressNegativeTypes: xtremManufacturing.enums.WorkInProgressType[] = [
        'workOrderNegativeVariance',
        'workOrderNegativeActualCostAdjustment',
        'workOrderNegativeActualCostAdjustmentNonAbsorbed',
        'productionTracking',
    ];

    static readonly workInProgressVarianceTypes: xtremManufacturing.enums.WorkInProgressType[] = [
        'workOrderActualCostAdjustment',
        'workOrderNegativeActualCostAdjustment',
    ];

    static readonly workInProgressNonAbsorbedVarianceTypes: xtremManufacturing.enums.WorkInProgressType[] = [
        'workOrderVariance',
        'workOrderNegativeVariance',
        'workOrderActualCostAdjustmentNonAbsorbed',
        'workOrderNegativeActualCostAdjustmentNonAbsorbed',
    ];

    @decorators.referenceProperty<WorkInProgressCost, 'originatingLine'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremMasterData.nodes.BaseDocumentLine,
    })
    readonly originatingLine: Reference<xtremMasterData.nodes.BaseDocumentLine>;

    @decorators.referenceProperty<WorkInProgressCost, 'operationTrackingLine'>({
        isPublished: true,
        node: () => xtremManufacturing.nodes.OperationTrackingLine,
        join: {
            async _id() {
                return (await this.originatingLine)._id;
            },
        },
        isNullable: true,
    })
    readonly operationTrackingLine: Reference<xtremManufacturing.nodes.OperationTrackingLine> | null;

    @decorators.referenceProperty<WorkInProgressCost, 'actualResource'>({
        isPublished: true,
        dependsOn: ['operationTrackingLine'],
        node: () => xtremMasterData.nodes.DetailedResource,
        isNullable: true,
        async getValue() {
            return (await this.operationTrackingLine)?.actualResource ?? null;
        },
    })
    readonly actualResource: Reference<xtremMasterData.nodes.DetailedResource> | null;

    @decorators.referenceProperty<WorkInProgressCost, 'workOrder'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremManufacturing.nodes.WorkOrder,
    })
    readonly workOrder: Reference<xtremManufacturing.nodes.WorkOrder>;

    @decorators.decimalProperty<WorkInProgressCost, 'quantity'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
    })
    readonly quantity: Promise<decimal>;

    @decorators.referenceProperty<WorkInProgressCost, 'unit'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
    })
    readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<WorkInProgressCost, 'cost'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
    })
    readonly cost: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressCost, 'amount'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dependsOn: ['quantity', 'cost'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async defaultValue() {
            return (await this.quantity) * (await this.cost);
        },
    })
    readonly amount: Promise<decimal>;

    @decorators.referenceProperty<WorkInProgressCost, 'currency'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremMasterData.nodes.Currency,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.enumProperty<WorkInProgressCost, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremManufacturing.enums.workInProgressStatusDataType,
        defaultValue: () => 'pending',
    })
    readonly status: Promise<xtremManufacturing.enums.WorkInProgressStatus>;

    @decorators.dateProperty<WorkInProgressCost, 'effectiveDate'>({
        isStored: true,
        isPublished: true,
    })
    readonly effectiveDate: Promise<date>;

    @decorators.dateProperty<WorkInProgressCost, 'postingDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly postingDate: Promise<date | null>;

    @decorators.enumProperty<WorkInProgressCost, 'type'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dataType: () => xtremManufacturing.enums.WorkInProgressTypeDataType,
    })
    readonly type: Promise<xtremManufacturing.enums.WorkInProgressType>;

    // reference to wip cost of line for the accounting interface
    @decorators.jsonProperty<WorkInProgressCost, 'storedDimensions'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        async computeValue() {
            return (await (await this.workOrder).productionItems.elementAt(0)).storedDimensions;
        },
    })
    readonly storedDimensions: Promise<object | null>;

    // reference to wip cost of line for the accounting interface
    @decorators.jsonProperty<WorkInProgressCost, 'storedAttributes'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        async computeValue() {
            return (await (await this.workOrder).productionItems.elementAt(0)).storedAttributes;
        },
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    // reference to wip cost of line for the accounting interface
    @decorators.jsonProperty<WorkInProgressCost, 'computedAttributes'>({
        isPublished: true,
        async computeValue() {
            return xtremFinanceData.functions.computeGenericAttributes(this.$.context, {
                stockSite: await (await this.workOrder).site,
                financialSite: await (await this.workOrder).site,
                item: await this.item,
                returnBusinessSite: false,
            });
        },
    })
    readonly computedAttributes: Promise<object>;

    // reference to item for the accounting interface
    @decorators.referenceProperty<WorkInProgressCost, 'item'>({
        isPublished: true,
        async getValue() {
            return (await (await this.workOrder).productionItem).releasedItem;
        },
        node: () => xtremMasterData.nodes.Item,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    // reference to wip cost type of line for the accounting interface
    @decorators.stringProperty<WorkInProgressCost, 'workInProgressCostType'>({
        async computeValue() {
            switch (await this.type) {
                case 'workOrderVariance':
                    return 'workOrderVariance';
                case 'workOrderNegativeVariance':
                    return 'workOrderNegativeVariance';
                case 'workOrderActualCostAdjustment':
                    return 'workOrderActualCostAdjustment';
                case 'workOrderNegativeActualCostAdjustment':
                    return 'workOrderNegativeActualCostAdjustment';
                case 'workOrderActualCostAdjustmentNonAbsorbed':
                    return 'workOrderActualCostAdjustmentNonAbsorbed';
                case 'workOrderNegativeActualCostAdjustmentNonAbsorbed':
                    return 'workOrderNegativeActualCostAdjustmentNonAbsorbed';
                case 'setupTimeTracking':
                    return 'setupTimeTracking';
                case 'runTimeTracking':
                    return 'runTimeTracking';
                case 'materialTracking':
                    return 'materialTracking';
                case 'productionTracking':
                    return 'productionTracking';
                default:
                    return 'workOrderIndirectCost';
            }
        },
    })
    readonly workInProgressCostType: Promise<string>;

    @decorators.query<typeof WorkInProgressCost, 'getCostProduced'>({
        isPublished: true,
        parameters: [
            { name: 'currency', type: 'reference', node: () => xtremMasterData.nodes.Currency, isMandatory: false },
            { name: 'reportMonths', type: 'integer', isMandatory: false },
        ],
        return: {
            name: 'months',
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    month: 'string',
                    cost: 'decimal',
                },
            },
        },
    })
    static async getCostProduced(
        context: Context,
        currency: xtremMasterData.nodes.Currency | null,
        reportMonths?: integer,
    ): Promise<Array<MonthlyProductionCost>> {
        const today = date.today();
        // Months are held 0 -> 11
        const todayMonth = today.month;
        const months = reportMonths ? Math.min(reportMonths, 12) : 2;
        const costs: Array<MonthlyProductionCost> = [];
        for (let i = months - 1; i >= 0; i -= 1) {
            costs.push({
                month: today.month <= i ? todayMonth + 12 - i : todayMonth - i,
                cost: 0,
            });
        }
        if (currency) {
            const { month } = today;
            logger.debug(() => `getQuantityProduced currentMonth=${month}`);
            let firstDayOfStartMonth: Date;
            if (month - months < 0) {
                firstDayOfStartMonth = new Date(today.year - 1, 11 + month - months, 1, 23);
            } else {
                firstDayOfStartMonth = new Date(today.year, month - months, 1, 23);
            }
            logger.debug(
                () =>
                    `getQuantityProduced First date of previous month=${firstDayOfStartMonth}, parameter=${firstDayOfStartMonth
                        .toISOString()
                        .slice(0, 10)
                        .replace(/-/g, '')}`,
            );
            const results = context.queryAggregate(WorkInProgressCost, {
                filter: {
                    effectiveDate: { _gte: firstDayOfStartMonth.toISOString() },
                    type: {
                        _in: ['materialTracking', 'setupTimeTracking', 'runTimeTracking', 'workOrderIndirectCost'],
                    },
                },
                group: {
                    effectiveDate: { _by: 'month' },
                    currency: { id: { _by: 'value' } },
                },
                values: {
                    amount: { sum: true },
                },
            });
            logger.debug(() => `getQuantityProduced results=${JSON.stringify(results)}`);
            const resultsArray = await results.toArray();
            logger.debug(() => `getQuantityProduced salesResults=${JSON.stringify(resultsArray)}`);

            await asyncArray(resultsArray).forEach(async result => {
                const monthGroup = WorkInProgressCost.filterResults(
                    resultsArray,
                    result.group.effectiveDate.begOfMonth(),
                );
                logger.debug(
                    () =>
                        `getMtdSales currentMonthGroup = ${JSON.stringify(
                            monthGroup,
                        )} previousMonthGroup=${JSON.stringify(monthGroup)}`,
                );
                const index = costs.findIndex(cost => cost.month === result.group.effectiveDate.month);
                if (index >= 0) {
                    costs[index].cost = await WorkInProgressCost.calculateMonthlyTotalInRequiredCurrency(
                        context,
                        monthGroup,
                        currency,
                    );
                }
            });
        }
        return costs;
    }

    private static filterResults(results: Array<CostProducedResult>, filterDate: date) {
        const passedDate = `${filterDate.year}-${filterDate.month}`;
        return results.filter((result: CostProducedResult) => {
            logger.debug(() => `getCostProduced result = ${JSON.stringify(result)}`);
            const { effectiveDate } = result.group;
            logger.debug(() => `getCostProduced effectiveDate = ${effectiveDate}`);
            const resultDate = `${result.group.effectiveDate.year}-${result.group.effectiveDate.month}`;
            logger.debug(() => `getCostProduced resultDate = ${resultDate} fdocm=${passedDate}`);
            if (
                filterDate.year === result.group.effectiveDate.year &&
                filterDate.month === result.group.effectiveDate.month
            ) {
                return result;
            }
            return null;
        });
    }

    private static async calculateMonthlyTotalInRequiredCurrency(
        context: Context,
        group: Array<CostProducedResult>,
        currency: xtremMasterData.nodes.Currency,
    ): Promise<decimal> {
        try {
            return await asyncArray(group).sum(async (result: CostProducedResult) => {
                if (result.group) {
                    if (result.group.currency.id === (await currency.id)) {
                        return result.values.amount.sum;
                    }
                    return xtremMasterData.functions.convertCurrency(
                        context,
                        result.values.amount.sum,
                        result.group.currency.id,
                        await currency.id,
                        date.today(),
                    );
                }
                return 0;
            });
        } catch (error) {
            // No exchange rate available
            return 0;
        }
    }
}
