import type { date, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremManufacturing from '../index';

@decorators.subNode<WorkInProgressWorkOrderComponent>({
    extends: () => xtremMasterData.nodes.WorkInProgress,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isVitalReferenceChild: true,
})
export class WorkInProgressWorkOrderComponent extends xtremMasterData.nodes.WorkInProgress {
    @decorators.referenceProperty<WorkInProgressWorkOrderComponent, 'workOrderComponent'>({
        isStored: true,
        isVitalParent: true,
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrderComponent,
    })
    readonly workOrderComponent: Reference<xtremManufacturing.nodes.WorkOrderComponent>;

    /*
    get adapters() {
        return {
            parametersWorkInProgress: {
                item: this.workOrderComponent.item,
                site: this.workOrderComponent.workOrder.site,
                status:
                    this.workOrderComponent.lineStatus === 'excluded' ||
                    this.workOrderComponent.lineStatus === 'completed'
                        ? 'closed'
                        : this.workOrderComponent.workOrder.type,
                startDate: this.workOrderComponent.requiredDate,
                endDate: this.workOrderComponent.requiredDate,

                expectedQuantity: this.workOrderComponent.requiredQuantity,
                actualQuantity: this.workOrderComponent.consumedQuantity,

                documentType: xtremMasterData.enums.DocumentType.materialNeed,
                documentNumber: this.workOrderComponent.workOrder.number,
                documentLine: this.workOrderComponent.componentNumber,

                originItem: this.workOrderComponent.workOrder.productionItem,
                originDocumentType: xtremMasterData.enums.DocumentType.workOrder,
                originDocumentNumber: this.workOrderComponent.workOrder.number,
            } as xtremMasterData.interfaces.WorkInProgressMappingInterface,
        };
    }
    */
    @decorators.referencePropertyOverride<WorkInProgressWorkOrderComponent, 'item'>({
        dependsOn: [{ workOrderComponent: ['item'] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        // getValue() {
        //     return this.workOrderComponent.item;
        // },
        async defaultValue() {
            return (await this.workOrderComponent).item;
        },
        updatedValue: useDefaultValue,
    })
    override readonly item: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referencePropertyOverride<WorkInProgressWorkOrderComponent, 'site'>({
        dependsOn: [{ workOrderComponent: [{ workOrder: ['site'] }] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        // getValue() {
        //     return this.workOrderComponent.workOrder.site;
        // },
        async defaultValue() {
            return (await (await this.workOrderComponent).workOrder).site;
        },
        updatedValue: useDefaultValue,
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    private async _setStatus(): Promise<xtremMasterData.enums.OrderType> {
        // TODO: confirm this by Xtrem team
        switch (await (await this.workOrderComponent).lineStatus) {
            case 'included':
            case 'pending': {
                return 'planned';
            }
            case 'inProgress': {
                return 'firm';
            }
            case 'excluded':
            case 'completed': {
                return 'closed';
            }
            default:
                return 'closed';
        }
    }

    @decorators.enumPropertyOverride<WorkInProgressWorkOrderComponent, 'status'>({
        dependsOn: [{ workOrderComponent: ['lineStatus'] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        // getValue() {
        //     return this._setStatus();
        // },
        defaultValue() {
            return this._setStatus();
        },
        updatedValue: useDefaultValue,
    })
    override readonly status: Promise<xtremMasterData.enums.OrderType>;

    @decorators.datePropertyOverride<WorkInProgressWorkOrderComponent, 'startDate'>({
        dependsOn: [{ workOrderComponent: ['requiredDate'] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        // getValue() {
        //     return this.workOrderComponent.requiredDate;
        // },
        async defaultValue() {
            return (await this.workOrderComponent).requiredDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly startDate: Promise<date | null>;

    @decorators.datePropertyOverride<WorkInProgressWorkOrderComponent, 'endDate'>({
        dependsOn: [{ workOrderComponent: ['requiredDate'] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        // getValue() {
        //     return this.workOrderComponent.requiredDate;
        // },
        async defaultValue() {
            return (await this.workOrderComponent).requiredDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly endDate: Promise<date | null>;

    /* quantities are expressed in item.stockUnit */
    @decorators.decimalPropertyOverride<WorkInProgressWorkOrderComponent, 'expectedQuantity'>({
        dependsOn: [{ workOrderComponent: ['requiredQuantity'] }],
        async defaultValue() {
            return (await this.workOrderComponent).requiredQuantity;
        },
        updatedValue: useDefaultValue,
    })
    override readonly expectedQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressWorkOrderComponent, 'actualQuantity'>({
        dependsOn: [{ workOrderComponent: ['consumedQuantity'] }],
        async defaultValue() {
            return (await this.workOrderComponent).consumedQuantity;
        },
        updatedValue: useDefaultValue,
    })
    override readonly actualQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressWorkOrderComponent, 'outstandingQuantity'>({
        dependsOn: ['expectedQuantity', 'actualQuantity'],
        async defaultValue() {
            return Math.max((await this.expectedQuantity) - (await this.actualQuantity), 0);
        },
        updatedValue: useDefaultValue,
    })
    override readonly outstandingQuantity: Promise<decimal>;

    @decorators.enumPropertyOverride<WorkInProgressWorkOrderComponent, 'documentType'>({
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        // getValue() {
        //     return 'materialNeed';
        // },
        defaultValue() {
            return 'materialNeed';
        },
    })
    override readonly documentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType>;

    @decorators.stringPropertyOverride<WorkInProgressWorkOrderComponent, 'documentNumber'>({
        dependsOn: [{ workOrderComponent: [{ workOrder: ['number'] }] }],
        async getValue() {
            return (await (await this.workOrderComponent).workOrder).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<WorkInProgressWorkOrderComponent, 'documentLine'>({
        dependsOn: [{ workOrderComponent: ['componentNumber'] }],
        async getValue() {
            return (await this.workOrderComponent).componentNumber;
        },
    })
    override readonly documentLine: Promise<integer>;

    @decorators.integerPropertyOverride<WorkInProgressWorkOrderComponent, 'documentId'>({
        async getValue() {
            return (await (await this.workOrderComponent).workOrder)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumPropertyOverride<WorkInProgressWorkOrderComponent, 'originDocumentType'>({
        dependsOn: [{ workOrderComponent: [{ workOrder: ['productionItem'] }] }],
        getValue() {
            return 'materialNeed';
        },
    })
    override readonly originDocumentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType | null>;
}
