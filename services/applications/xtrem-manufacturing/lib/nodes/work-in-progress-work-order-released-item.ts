import type { date, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremManufacturing from '../index';

@decorators.subNode<WorkInProgressWorkOrderReleasedItem>({
    extends: () => xtremMasterData.nodes.WorkInProgress,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isVitalReferenceChild: true,
})
export class WorkInProgressWorkOrderReleasedItem extends xtremMasterData.nodes.WorkInProgress {
    @decorators.referenceProperty<WorkInProgressWorkOrderReleasedItem, 'workOrderReleasedItem'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremManufacturing.nodes.WorkOrderReleasedItem,
    })
    readonly workOrderReleasedItem: Reference<xtremManufacturing.nodes.WorkOrderReleasedItem>;

    /*
    get adapters() {
        return {
            parametersWorkInProgress: {
                item: this.workOrderReleasedItem.releasedItem,
                site: this.workOrderReleasedItem.document.site,
                status: this.workOrderReleasedItem.document.type,
                startDate: this.workOrderReleasedItem.document.startDate,
                endDate: this.workOrderReleasedItem.document.endDate,
                expectedQuantity: this.workOrderReleasedItem.releasedQuantity,
                actualQuantity: this.workOrderReleasedItem.totalActualQuantity,
                documentType: xtremMasterData.enums.DocumentType.workOrder,
                documentNumber: this.workOrderReleasedItem.document.number,
            } as xtremMasterData.interfaces.WorkInProgressMappingInterface,
        };
    }
    */
    @decorators.referencePropertyOverride<WorkInProgressWorkOrderReleasedItem, 'item'>({
        dependsOn: [{ workOrderReleasedItem: ['releasedItem'] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        // getValue() {
        //     return this.workOrderReleasedItem.releasedItem;
        // },
        async defaultValue() {
            return (await this.workOrderReleasedItem).releasedItem;
        },
        updatedValue: useDefaultValue,
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.referencePropertyOverride<WorkInProgressWorkOrderReleasedItem, 'site'>({
        dependsOn: [{ workOrderReleasedItem: [{ document: ['site'] }] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        // getValue() {
        //     return this.workOrderReleasedItem.document.site;
        // },
        async defaultValue() {
            return (await (await this.workOrderReleasedItem).document).site;
        },
        updatedValue: useDefaultValue,
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    private async _setStatus(): Promise<xtremMasterData.enums.OrderType> {
        // TODO: confirm this by Xtrem team
        switch (await (await (await this.workOrderReleasedItem).document).status) {
            case 'pending': {
                return 'planned';
            }
            case 'inProgress': {
                return 'firm';
            }
            case 'completed':
            case 'closed':
            case 'costCalculated': {
                return 'closed';
            }
            default:
                return 'closed';
        }
    }

    @decorators.enumPropertyOverride<WorkInProgressWorkOrderReleasedItem, 'status'>({
        dependsOn: [{ workOrderReleasedItem: [{ document: ['status'] }] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        // getValue() {
        //     return this._setStatus();
        // },
        defaultValue() {
            return this._setStatus();
        },
        updatedValue: useDefaultValue,
    })
    override readonly status: Promise<xtremMasterData.enums.OrderType>;

    @decorators.datePropertyOverride<WorkInProgressWorkOrderReleasedItem, 'startDate'>({
        dependsOn: [{ workOrderReleasedItem: [{ document: ['startDate'] }] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        // getValue() {
        //     return this.workOrderReleasedItem.document.startDate;
        // },
        async defaultValue() {
            return (await (await this.workOrderReleasedItem).document).startDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly startDate: Promise<date>;

    @decorators.datePropertyOverride<WorkInProgressWorkOrderReleasedItem, 'endDate'>({
        dependsOn: [{ workOrderReleasedItem: [{ document: ['endDate'] }] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        // getValue() {
        //     return this.workOrderReleasedItem.document.endDate;
        // },
        async defaultValue() {
            return (await (await this.workOrderReleasedItem).document).endDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly endDate: Promise<date>;

    /* quantities are expressed in item.stockUnit */
    @decorators.decimalPropertyOverride<WorkInProgressWorkOrderReleasedItem, 'expectedQuantity'>({
        dependsOn: [{ workOrderReleasedItem: ['releasedQuantity'] }],
        async defaultValue() {
            return (await this.workOrderReleasedItem).releasedQuantity;
        },
        updatedValue: useDefaultValue,
    })
    override readonly expectedQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressWorkOrderReleasedItem, 'actualQuantity'>({
        dependsOn: [{ workOrderReleasedItem: ['totalActualQuantity'] }],
        async defaultValue() {
            return (await this.workOrderReleasedItem).totalActualQuantity;
        },
        updatedValue: useDefaultValue,
    })
    override readonly actualQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressWorkOrderReleasedItem, 'outstandingQuantity'>({
        dependsOn: ['expectedQuantity', 'actualQuantity'],
        async defaultValue() {
            return Math.max((await this.expectedQuantity) - (await this.actualQuantity), 0);
        },
        updatedValue: useDefaultValue,
    })
    override readonly outstandingQuantity: Promise<decimal>;

    @decorators.enumPropertyOverride<WorkInProgressWorkOrderReleasedItem, 'documentType'>({
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        // getValue() {
        //     return xtremMasterData.enums.DocumentType.workOrder;
        // },
        defaultValue() {
            return 'workOrder';
        },
    })
    override readonly documentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType>;

    @decorators.stringPropertyOverride<WorkInProgressWorkOrderReleasedItem, 'documentNumber'>({
        dependsOn: [{ workOrderReleasedItem: [{ document: ['number'] }] }],
        async getValue() {
            return (await (await this.workOrderReleasedItem).document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<WorkInProgressWorkOrderReleasedItem, 'documentLine'>({
        async getValue() {
            return (await this.workOrderReleasedItem)._sortValue;
        },
    })
    override readonly documentLine: Promise<integer>;

    @decorators.integerPropertyOverride<WorkInProgressWorkOrderReleasedItem, 'documentId'>({
        async getValue() {
            return (await (await this.workOrderReleasedItem).document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumPropertyOverride<WorkInProgressWorkOrderReleasedItem, 'originDocumentType'>({
        // dependsOn: [{workOrderReleasedItem : ['workOrderReleasedItem']}],
        getValue() {
            return null;
        },
    })
    override readonly originDocumentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType | null>;
}
