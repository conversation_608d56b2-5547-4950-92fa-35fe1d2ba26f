import type { Collection, Context, date, datetime, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, Logger, Node, NodeStatus, TextStream } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import * as xtremManufacturing from '../../index';

const logger = Logger.getLogger(__filename, 'work-order-operation');
@decorators.node<WorkOrderOperation>({
    package: 'xtrem-manufacturing',
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isCustomizable: true,
    canDuplicate: true,
    indexes: [{ orderBy: { workOrder: +1, operationNumber: +1 }, isUnique: true, isNaturalKey: true }],
    async controlEnd(cx) {
        if (this.$.status === NodeStatus.modified && (await this.isAdded) !== (await (await this.$.old).isAdded)) {
            cx.error.addLocalized(
                '@sage/xtrem-manufacturing/nodes__work_order_operation__isAdded_not_changeable',
                'The isAdded property cannot be changed.',
            );
        }

        // something else than status is changed for an already excluded operation
        if (
            this.$.status === NodeStatus.modified &&
            (await this.status) === (await (await this.$.old).status) &&
            (await (await this.$.old).status) === 'excluded' &&
            !(await (
                await this.workOrder
            ).forceUpdateForScheduling)
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-manufacturing/nodes__work_order_operation__excluded_operation_not_changeable',
                'The excluded operation with number {{operationNumber}} cannot be modified.',
                { operationNumber: await this.operationNumber },
            );
        }
    },
    async deleteBegin() {
        (await this.workOrder).isOperationDeleted = true;
    },
    async saveBegin() {
        logger.debug(() => `saveBegin`);
        if (this.$.status === NodeStatus.added) {
            await this.$.set({ isAdded: (await this.workOrder).$.status !== NodeStatus.added });
        }

        if (
            this.$.status === NodeStatus.modified &&
            (await (await this.$.old).status) !== (await this.status) &&
            (await this.status) === 'excluded'
        ) {
            // propagates excluded status from an operation to its resources
            await this.resources
                .filter(async opr => (await opr.status) !== 'excluded')
                .forEach(opr => opr.$.set({ status: 'excluded' }));
        } else if (
            this.$.status === NodeStatus.modified &&
            (await (await this.$.old).status) === 'excluded' &&
            (await this.status) === 'pending' &&
            (await this.resources.length) > 0 &&
            !(await this.resources.filter(async opr => (await opr.status) !== 'excluded').length)
        ) {
            // propagates removal of excluded status from an operation to its resources
            await this.resources
                .filter(async opr => (await opr.status) !== 'pending')
                .forEach(opr => opr.$.set({ status: 'pending' }));
        }

        if (this.$.status === NodeStatus.modified && (await (await this.$.old).status) === (await this.status)) {
            if (
                (await this.completedQuantity) > 0 &&
                ((await this.status) === 'pending' || (await this.status) === 'inProgress')
            ) {
                await this.$.set({
                    status: (await this.completedQuantity) >= (await this.plannedQuantity) ? 'completed' : 'inProgress',
                });
            }
        }
    },
})
export class WorkOrderOperation extends Node {
    @decorators.referenceProperty<WorkOrderOperation, 'workOrder'>({
        isStored: true,
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrder,
        isVitalParent: true,
        lookupAccess: true,
    })
    readonly workOrder: Reference<xtremManufacturing.nodes.WorkOrder>;

    @decorators.integerProperty<WorkOrderOperation, 'operationNumber'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        async defaultValue() {
            if ((await (await this.workOrder).productionOperations.length) === 0) {
                return 10;
            }
            const operationNumbers = await (await this.workOrder).productionOperations
                .map(operation => operation.operationNumber)
                .toArray();
            return Math.max(...operationNumbers) + 10;
        },
        lookupAccess: true,
    })
    readonly operationNumber: Promise<integer>;

    @decorators.stringProperty<WorkOrderOperation, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<WorkOrderOperation, 'setupTimeUnit'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: ['workOrder', { workOrder: ['routingTimeUnit'] }],
        async defaultValue() {
            return (await this.workOrder).routingTimeUnit;
        },
        filters: { control: { type: { _eq: 'time' } } },
    })
    readonly setupTimeUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referenceProperty<WorkOrderOperation, 'runTimeUnit'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: ['workOrder', { workOrder: ['routingTimeUnit'] }],
        async defaultValue() {
            return (await this.workOrder).routingTimeUnit;
        },
        filters: { control: { type: { _eq: 'time' } } },
    })
    readonly runTimeUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<WorkOrderOperation, 'expectedSetupTime'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.durationDataType,
        async computeValue() {
            // Find the maximum time as all operation resources are assumed to be performed at the same time.
            return Math.max(
                ...(await this.resources
                    .map(
                        (operationResource: xtremManufacturing.nodes.WorkOrderOperationResource) =>
                            operationResource.expectedSetupTime,
                    )
                    .toArray()),
                0,
            );
        },
    })
    readonly expectedSetupTime: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'expectedRunTime'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.durationDataType,
        async computeValue() {
            // Find the maximum time as all operation resources are assumed to be performed at the same time.
            return Math.max(
                ...(await this.resources
                    .map(
                        (operationResource: xtremManufacturing.nodes.WorkOrderOperationResource) =>
                            operationResource.expectedRunTime,
                    )
                    .toArray()),
                0,
            );
        },
    })
    readonly expectedRunTime: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'expectedBatchCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return (await this.expectedBatchRunCost) + (await this.expectedBatchSetupCost);
        },
    })
    readonly expectedBatchCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'expectedBatchRunCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        getValue() {
            return this.resources.sum(operationResourceGroup => operationResourceGroup.expectedRunCost);
        },
    })
    readonly expectedBatchRunCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'expectedMachineCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        computeValue() {
            return xtremManufacturing.functions.calculateExpectedMachineRunCost(this.resources);
        },
    })
    readonly expectedMachineCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'expectedLaborCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        computeValue() {
            return xtremManufacturing.functions.calculateExpectedLaborRunCost(this.resources);
        },
    })
    readonly expectedLaborCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'expectedToolCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        computeValue() {
            return xtremManufacturing.functions.calculateExpectedToolRunCost(this.resources);
        },
    })
    readonly expectedToolCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'expectedBatchSetupCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        getValue() {
            return this.resources.sum(operationResourceGroup => operationResourceGroup.expectedSetupCost);
        },
    })
    readonly expectedBatchSetupCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'expectedMachineSetupCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        computeValue() {
            return xtremManufacturing.functions.calculateExpectedMachineSetupCost(this.resources);
        },
    })
    readonly expectedMachineSetupCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'expectedLaborSetupCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        computeValue() {
            return xtremManufacturing.functions.calculateExpectedLaborSetupCost(this.resources);
        },
    })
    readonly expectedLaborSetupCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'expectedToolSetupCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        computeValue() {
            return xtremManufacturing.functions.calculateExpectedToolSetupCost(this.resources);
        },
    })
    readonly expectedToolSetupCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'actualSetupTime'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.durationDataType,
        async computeValue() {
            // Find the maximum time as all operation resources are assumed to be performed at the same time.
            return Math.max(
                ...(await this.resources
                    .map(
                        (operationResource: xtremManufacturing.nodes.WorkOrderOperationResource) =>
                            operationResource.actualSetupTime,
                    )
                    .toArray()),
                0,
            );
        },
    })
    readonly actualSetupTime: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'actualRunTime'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.durationDataType,
        async computeValue() {
            // Find the maximum time as all operation resources are assumed to be performed at the same time.
            return Math.max(
                ...(await this.resources
                    .map(
                        (operationResource: xtremManufacturing.nodes.WorkOrderOperationResource) =>
                            operationResource.actualRunTime,
                    )
                    .toArray()),
                0,
            );
        },
    })
    readonly actualRunTime: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'actualBatchCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return (await this.actualBatchSetupCost) + (await this.actualBatchRunCost);
        },
    })
    readonly actualBatchCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'actualBatchRunCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        getValue() {
            return this.resources.sum(operationResourceGroup => operationResourceGroup.actualRunCost);
        },
    })
    readonly actualBatchRunCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'actualMachineCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        computeValue() {
            return xtremManufacturing.functions.calculateActualMachineRunCost(this.resources);
        },
    })
    readonly actualMachineCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'actualLaborCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        computeValue() {
            return xtremManufacturing.functions.calculateActualLaborRunCost(this.resources);
        },
    })
    readonly actualLaborCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'actualToolCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        computeValue() {
            return xtremManufacturing.functions.calculateActualToolRunCost(this.resources);
        },
    })
    readonly actualToolCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'actualBatchSetupCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        getValue() {
            return this.resources.sum(operationResource => operationResource.actualSetupCost);
        },
    })
    readonly actualBatchSetupCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'actualMachineSetupCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        computeValue() {
            return xtremManufacturing.functions.calculateActualMachineSetupCost(this.resources);
        },
    })
    readonly actualMachineSetupCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'actualLaborSetupCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        computeValue() {
            return xtremManufacturing.functions.calculateActualLaborSetupCost(this.resources);
        },
    })
    readonly actualLaborSetupCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'actualToolSetupCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        computeValue() {
            return xtremManufacturing.functions.calculateActualToolSetupCost(this.resources);
        },
    })
    readonly actualToolSetupCost: Promise<decimal>;

    @decorators.integerProperty<WorkOrderOperation, 'resourceGroupNumber'>({
        isPublished: true,
        getValue() {
            return this.resources.length;
        },
    })
    readonly resourceGroupNumber: Promise<integer>;

    @decorators.referenceProperty<WorkOrderOperation, 'minCapabilityLevel'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.CapabilityLevel,
        lookupAccess: true,
    })
    readonly minCapabilityLevel: Reference<xtremMasterData.nodes.CapabilityLevel>;

    @decorators.collectionProperty<WorkOrderOperation, 'resources'>({
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrderOperationResource,
        isVital: true,
        reverseReference: 'workOrderOperation',
        async controlEnd(cx) {
            if ((await this.resources.length) && (await this.status) !== 'excluded') {
                // special case when an operation is changed from excluded to pending with all its resources still excluded
                // anticpate that pending status for operation will be propagated to all its resources
                const resourcesAsPending =
                    this.$.status === NodeStatus.modified &&
                    (await (await this.$.old).status) === 'excluded' &&
                    !(await this.resources.find(async resource => (await resource.status) !== 'excluded'));
                const resourceQuantityCount = await this.resources.filter(
                    async (resource: xtremManufacturing.nodes.WorkOrderOperationResource) => {
                        return (
                            (await resource.expectedRunTime) !== 0 &&
                            (await resource.isResourceQuantity) === true &&
                            ((await resource.status) !== 'excluded' || resourcesAsPending)
                        );
                    },
                ).length;

                if (resourceQuantityCount === 0) {
                    cx.error.addLocalized(
                        '@sage/xtrem-manufacturing/nodes_work_order_operation_no_quantity_produced_resource',
                        'At least one quantity produced resource with runtime is required on the operation number {{operationNumber}}',
                        { operationNumber: await this.operationNumber },
                    );
                }
                if (resourceQuantityCount > 1) {
                    cx.error.addLocalized(
                        '@sage/xtrem-manufacturing/nodes_work_order_operation__is_resource_quantity_operation_already_flagged',
                        'No more than one quantity produced resource with runtime on the operation number {{operationNumber}}',
                        { operationNumber: await this.operationNumber },
                    );
                }
            }
        },
    })
    readonly resources: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>;

    @decorators.booleanProperty<WorkOrderOperation, 'isProductionStep'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isProductionStep: Promise<boolean>;

    @decorators.enumProperty<WorkOrderOperation, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremManufacturing.enums.operationStatusDataType,
        defaultValue: 'pending',
        dependsOn: [{ resources: ['status'] }],
        async updatedValue() {
            logger.debug(() => `status updateValue`);
            if (['inProgress', 'pending'].includes(await this.status)) {
                if (await this.resources.some(async resource => (await resource.status) === 'pending')) {
                    return 'pending';
                }
                if (await this.resources.every(async resource => (await resource.status) === 'excluded')) {
                    return 'excluded';
                }
                if (await this.resources.some(async resource => (await resource.status) === 'inProgress')) {
                    return 'inProgress';
                }
                return 'completed';
            }
            return this.status;
        },
        async duplicatedValue() {
            return (await this.status) !== 'excluded' ? 'pending' : this.status;
        },
    })
    readonly status: Promise<xtremManufacturing.enums.OperationStatus>;

    // technical property to allow filter on status
    @decorators.enumProperty<WorkOrderOperation, 'uStatus'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremManufacturing.enums.workOrderLineStatusFilteredDataType,
        async getValue() {
            if (['included', 'inProgress', 'pending'].includes(await this.status)) {
                return (await this.status) as xtremManufacturing.enums.WorkOrderLineStatusFiltered;
            }
            return null;
        },
    })
    readonly uStatus: Promise<xtremManufacturing.enums.WorkOrderLineStatusFiltered | null>;

    @decorators.booleanProperty<WorkOrderOperation, 'isAdded'>({ isStored: true, isPublished: true })
    readonly isAdded: Promise<boolean>;

    @decorators.datetimeProperty<WorkOrderOperation, 'startDatetime'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        duplicatedValue: null,
    })
    readonly startDatetime: Promise<datetime | null>;

    @decorators.dateProperty<WorkOrderOperation, 'startDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dependsOn: ['startDatetime'],
        async defaultValue() {
            return (await this.startDatetime)?.date || null;
        },
        duplicatedValue: null,
    })
    readonly startDate: Promise<date | null>;

    @decorators.datetimeProperty<WorkOrderOperation, 'endDatetime'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        async control(cx, val) {
            xtremMasterData.events.control.timeControls.checkDatetimeRange(cx, await this.startDatetime, val);
        },
        duplicatedValue: null,
    })
    readonly endDatetime: Promise<datetime | null>;

    @decorators.dateProperty<WorkOrderOperation, 'endDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dependsOn: ['endDatetime'],
        async defaultValue() {
            return (await this.endDatetime)?.date || null;
        },
        async control(cx, val) {
            xtremMasterData.events.control.timeControls.checkDateRange(cx, await this.startDate, val);
        },
        duplicatedValue: null,
    })
    readonly endDate: Promise<date | null>;

    @decorators.decimalProperty<WorkOrderOperation, 'plannedQuantity'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly plannedQuantity: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'completedQuantity'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        duplicatedValue: 0,
    })
    readonly completedQuantity: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'completedQuantityPercentage'>({
        async getValue() {
            return ((await this.completedQuantity) / (await this.plannedQuantity)) * 100;
        },
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.durationDataType,
    })
    readonly completedQuantityPercentage: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'completedTimePercentage'>({
        async computeValue() {
            return (
                (100 *
                    ((await xtremMasterData.functions.convertToSeconds(
                        await this.setupTimeUnit,
                        await this.actualSetupTime,
                    )) +
                        (await xtremMasterData.functions.convertToSeconds(
                            await this.runTimeUnit,
                            await this.actualRunTime,
                        )))) /
                ((await xtremMasterData.functions.convertToSeconds(
                    await this.setupTimeUnit,
                    await this.expectedSetupTime,
                )) +
                    (await xtremMasterData.functions.convertToSeconds(
                        await this.runTimeUnit,
                        await this.expectedRunTime,
                    )))
            );
        },
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.percentageWorkOrderDataType,
    })
    readonly completedTimePercentage: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderOperation, 'remainingQuantity'>({
        async getValue() {
            return (await this.plannedQuantity) - (await this.completedQuantity);
        },
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.durationDataType,
    })
    readonly remainingQuantity: Promise<decimal>;

    /** Linked operation  */
    @decorators.referenceProperty<WorkOrderOperation, 'operation'>({
        isPublished: true,
        isNullable: true,
        dependsOn: ['workOrder', { workOrder: ['routingCode'] }],
        node: () => xtremTechnicalData.nodes.Operation,
        async computeValue() {
            const routing = await (await this.workOrder).routingCode;
            const operationNumber = await this.operationNumber;
            return routing
                ? (await this.$.context.tryRead(xtremTechnicalData.nodes.Operation, {
                      routing,
                      operationNumber,
                  })) || null
                : null;
        },
    })
    readonly operation: Reference<xtremTechnicalData.nodes.Operation | null>;

    /** additional instructions for the shop floor operator */
    @decorators.textStreamProperty<WorkOrderOperation, 'instruction'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['operation'],
        dataType: () => xtremTechnicalData.dataTypes.textStreamType,
        async defaultValue() {
            return (await this.operation)?.instruction || TextStream.empty;
        },
    })
    readonly instruction: Promise<TextStream>;

    @decorators.mutation<typeof WorkOrderOperation, 'updateOperation'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    workOrder: { type: 'reference', node: () => xtremManufacturing.nodes.WorkOrder },
                    operation: { type: 'reference', node: () => xtremManufacturing.nodes.WorkOrderOperation },
                    quantity: 'decimal',
                    setupTime: 'decimal',
                    runTime: 'decimal',
                    actualResource: { type: 'reference', node: () => xtremMasterData.nodes.DetailedResource },
                    completed: 'boolean',
                },
            },
        ],
        return: { type: 'reference', node: () => xtremManufacturing.nodes.WorkOrderOperation },
    })
    static async updateOperation(
        context: Context,
        data: {
            workOrder: xtremManufacturing.nodes.WorkOrder;
            operation: xtremManufacturing.nodes.WorkOrderOperation;
            quantity: decimal;
            setupTime: decimal;
            runTime: decimal;
            setupTimeCost: decimal;
            runTimeCost: decimal;
            actualResource: xtremMasterData.nodes.DetailedResource;
            completed: boolean;
        },
    ): Promise<xtremManufacturing.nodes.WorkOrderOperation | null> {
        const operation = await context.read(
            xtremManufacturing.nodes.WorkOrderOperation,
            { workOrder: data.workOrder, operationNumber: await data.operation.operationNumber },
            { forUpdate: true },
        );
        logger.debug(() => `operation=${JSON.stringify(operation)}`);

        await operation.$.set({ completedQuantity: data.quantity + (await operation.completedQuantity) });

        await logger.debugAsync(
            async () => `quantity=${data.quantity} completedQuantity=${await operation.completedQuantity}`,
        );

        logger.debug(() => `actualResource=${JSON.stringify(data.actualResource)}`);

        const filteredResources = operation.resources.filter(async operationResource => {
            logger.debug(() => `match resources resource=${JSON.stringify(operationResource)}`);
            const resourceId = await (await operationResource.resource).id;
            const actualResourceId = await data.actualResource.id;
            if (resourceId === actualResourceId) {
                return true;
            }
            if ((await operationResource.resource) instanceof xtremMasterData.nodes.GroupResource) {
                await logger.debugAsync(
                    async () => `resource=${JSON.stringify(await data.actualResource.resourceGroup)}`,
                );
                if (resourceId === (await (await data.actualResource.resourceGroup)?.id)) {
                    return true;
                }
            }
            logger.debug(() => `updateOperation - no match`);
            return false;
        });

        if ((await filteredResources.length) > 0) {
            await filteredResources.forEach(async operationResource => {
                logger.debug(
                    () =>
                        `updateOperation matched actualResource=${data.actualResource} resource=${JSON.stringify(
                            operationResource,
                        )}`,
                );
                await operationResource.$.set({
                    actualSetupTime: data.setupTime + (await operationResource.actualSetupTime),
                    actualRunTime: data.runTime + (await operationResource.actualRunTime),
                    actualSetupCost: data.setupTimeCost + (await operationResource.actualSetupCost),
                    actualRunCost: data.runTimeCost + (await operationResource.actualRunCost),
                });
                if (
                    (await operationResource.status) !== 'completed' &&
                    (await operationResource.status) !== 'included'
                ) {
                    await operationResource.$.set({
                        status:
                            (await operationResource.actualRunTime) >= (await operationResource.expectedRunTime) ||
                            data.completed
                                ? 'completed'
                                : 'inProgress',
                    });
                }
                logger.debug(() => `updateOperation matched updated resource=${JSON.stringify(operationResource)}`);
            });
        } else {
            logger.debug(() => `updateOperation not matched actualResource=${data.actualResource}`);
            await operation.resources.append({
                resource: data.actualResource,
                status: 'included',
                isAdded: true,
                actualSetupTime: data.setupTime,
                actualRunTime: data.runTime,
                actualSetupCost: data.setupTimeCost,
                actualRunCost: data.runTimeCost,
            });
            logger.debug(() => `updateOperation not matched updated data=${JSON.stringify(data)}`);
        }

        await operation.$.save();

        return operation;
    }
}
