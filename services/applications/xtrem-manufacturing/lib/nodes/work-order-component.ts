import type { Collection, Context, Reference, date, decimal, integer } from '@sage/xtrem-core';
import { NodeStatus, TextStream, ValidationSeverity, asyncArray, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import * as xtremManufacturing from '../index';

@decorators.subNode<WorkOrderComponent>({
    isPublished: true,
    isVitalCollectionChild: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    canDuplicate: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    indexes: [{ orderBy: { workOrder: 1, componentNumber: 1 }, isUnique: true, isNaturalKey: true }],
    async controlEnd(cx) {
        if (this.$.status === NodeStatus.modified && (await this.isAdded) !== (await (await this.$.old).isAdded)) {
            cx.error.addLocalized(
                '@sage/xtrem-manufacturing/nodes__work_order_component__isAdded_not_changeable',
                "The 'isAdded' property cannot be updated.",
            );
        }

        if (
            this.$.status === NodeStatus.modified &&
            ['allocated', 'partiallyAllocated'].includes(await this.allocationStatus) &&
            (await this.lineStatus) === 'excluded'
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-manufacturing/nodes__work_order_component__cannot_exclude_line_quantity_allocated',
                'Remove the stock allocation before excluding the line.',
            );
        }

        if (
            this.$.status === NodeStatus.modified &&
            (await this.requiredQuantity) < (await (await this.$.old).requiredQuantity) &&
            (await this.requiredQuantity) < (await this.quantityAllocated)
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-manufacturing/nodes__work_order_component__cannot_reduce_required_quantity_less_than_allocated',
                'The allocated quantity on the work order component cannot be more than the required quantity.',
            );
        }

        // something else than status is changed for an already excluded component
        if (
            this.$.status === NodeStatus.modified &&
            (await this.lineStatus) === (await (await this.$.old).lineStatus) &&
            (await (await this.$.old).lineStatus) === 'excluded'
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-manufacturing/nodes__work_order_component__excluded_component_not_changeable',
                'The component number {{componentNumber}} is excluded and cannot be modified.',
                { componentNumber: await this.componentNumber },
            );
        }

        // Automatic allocation in progress : we can't decrease the requiredQuantity
        if (
            this.$.status === NodeStatus.modified &&
            (await this.requiredQuantity) < (await (await this.$.old).requiredQuantity) &&
            (await this.allocationRequestStatus) === 'inProgress'
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-manufacturing/nodes__work_order_component__auto_allocation_cannot_decrease_quantity',
                'You can only reduce the required quantity of the component after the allocation request is complete.',
            );
        }

        // Automatic allocation in progress : we can't exclude the component
        if (
            this.$.status === NodeStatus.modified &&
            (await this.lineStatus) !== (await (await this.$.old).lineStatus) &&
            (await this.lineStatus) === 'excluded' &&
            (await this.allocationRequestStatus) === 'inProgress'
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-manufacturing/nodes__work_order_component__auto_allocation_cannot_exclude',
                'You can only exclude the component after the allocation request is complete.',
            );
        }

        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
    async saveBegin() {
        if (this.$.status === NodeStatus.added) {
            await this.$.set({ isAdded: (await this.workOrder).$.status !== NodeStatus.added });
        }

        if (
            this.$.status === NodeStatus.modified &&
            (await (await this.$.old).lineStatus) === (await this.lineStatus)
        ) {
            if (
                (await this.consumedQuantity) > 0 &&
                ((await this.lineStatus) === 'pending' || (await this.lineStatus) === 'inProgress')
            ) {
                await this.$.set({
                    lineStatus:
                        (await this.consumedQuantity) >= (await this.requiredQuantity) ? 'completed' : 'inProgress',
                });
            }
        }

        // default dimension and attributes on component from released item if dimension and attributes null
        const storedAttributes = (await this.storedAttributes) ? await this.storedAttributes : {};
        const storedDimensions = (await this.storedDimensions) ? await this.storedDimensions : {};

        const currentReleasedItemAttribute = await (
            await (await this.document).productionItems.elementAt(0)
        ).storedAttributes;

        const currentReleasedItemDimension = await (
            await (await this.document).productionItems.elementAt(0)
        ).storedDimensions;

        if (
            storedAttributes !== null &&
            Object.keys(storedAttributes).length === 0 &&
            storedDimensions !== null &&
            Object.keys(storedDimensions).length === 0 &&
            this.$.status === NodeStatus.added
        ) {
            await this.$.set({
                storedAttributes: currentReleasedItemAttribute,
                storedDimensions: currentReleasedItemDimension,
            });
        }
    },
    async createEnd() {
        if ((await this.lineType) !== 'text') {
            await this.$.set({ workInProgress: {} });
        }
    },
    async controlDelete(cx) {
        if (['allocated', 'partiallyAllocated'].includes(await this.allocationStatus)) {
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize(
                    '@sage/xtrem-manufacturing/nodes__work_order_component__cannot_delete_line_quantity_allocated',
                    'Remove the stock allocation before deleting the line.',
                ),
            );
        }
        if ((await this.allocationRequestStatus) === 'inProgress') {
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize(
                    '@sage/xtrem-manufacturing/nodes__work_order_component__auto_allocation_cannot_delete',
                    'The component cannot be deleted while an automatic allocation is in progress.',
                ),
            );
        }
    },
})
export class WorkOrderComponent
    extends xtremMasterData.nodes.BaseDocumentLine
    implements xtremStockData.interfaces.DocumentLineWithStockAllocation
{
    getItem() {
        return this.item;
    }

    @decorators.referenceProperty<WorkOrderComponent, 'document'>({
        dependsOn: ['workOrder'],
        node: () => xtremManufacturing.nodes.WorkOrder,
        getValue() {
            return this.workOrder;
        },
    })
    override readonly document: Reference<xtremManufacturing.nodes.WorkOrder>;

    @decorators.stringPropertyOverride<WorkOrderComponent, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<WorkOrderComponent, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.referenceProperty<WorkOrderComponent, 'workOrder'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremManufacturing.nodes.WorkOrder,
        isVitalParent: true,
    })
    readonly workOrder: Reference<xtremManufacturing.nodes.WorkOrder>;

    @decorators.integerProperty<WorkOrderComponent, 'componentNumber'>({
        isStored: true,
        isPublished: true,
        async control(cx, val) {
            await cx.error.if(val).is.not.greater.than(0);
        },
        async defaultValue() {
            return xtremTechnicalData.functions.common.getComponentNumber((await this.workOrder).productionComponents);
        },
    })
    readonly componentNumber: Promise<integer>;

    @decorators.enumProperty<WorkOrderComponent, 'lineType'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremTechnicalData.enums.bomLineTypeDataType,
        defaultValue: 'normal',
    })
    readonly lineType: Promise<xtremTechnicalData.enums.BomLineType>;

    @decorators.referenceProperty<WorkOrderComponent, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Item,
        isNullable: true,
        // In a first step, we allow phantom if they are excluded. See workOrderComponentItemControl for more details
        // filters: { control: { isStockManaged: true, isPhantom: false } },
        filters: { control: { isStockManaged: true } },
        async control(cx, item) {
            if ((await (await this.document).status) !== 'closed' && !(await (await this.document).closingDate)) {
                await xtremMasterData.functions.controls.item.inactiveItemControl(cx, item);
            }
            await xtremManufacturing.events.control.componentLineControls.workOrderComponentItemControl(cx, {
                lineType: await this.lineType,
                lineStatus: await this.lineStatus,
                item,
            });
        },
    })
    readonly item: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.stringProperty<WorkOrderComponent, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
        dependsOn: ['item', 'lineType'],
        async defaultValue() {
            const item = await this.item;
            return item && (await this.lineType) === 'normal' ? item.description : '';
        },
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<WorkOrderComponent, 'unit'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        isNullable: true,
        async control(cx, val) {
            xtremTechnicalData.events.control.componentLineControls.componentLinePropertyRequired(
                cx,
                await this.lineType,
                val,
                'Unit',
            );
        },
    })
    readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure | null>;

    @decorators.referenceProperty<WorkOrderComponent, 'itemSite'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.ItemSite,
        isNullable: true,
        join: {
            item() {
                return this.item;
            },
            async site() {
                return (await this.workOrder).site;
            },
        },
    })
    readonly itemSite: Reference<xtremMasterData.nodes.ItemSite | null>;

    @decorators.booleanProperty<WorkOrderComponent, 'isFixedLinkQuantity'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isFixedLinkQuantity: Promise<boolean>;

    @decorators.decimalProperty<WorkOrderComponent, 'linkQuantity'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        async control(cx, val) {
            await xtremTechnicalData.events.control.componentLineControls.componentLineNotZeroNumberRequired(
                cx,
                await this.lineType,
                val,
            );
        },
    })
    readonly linkQuantity: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderComponent, 'scrapFactor'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.scrapFactorPercentage,
        dependsOn: ['lineType'],
        async control(cx, val) {
            if ((await this.lineType) !== 'normal') {
                await cx.error.if(val).is.not.zero();
            }
        },
    })
    readonly scrapFactor: Promise<decimal>;

    @decorators.referenceProperty<WorkOrderComponent, 'operation'>({
        isStored: true,
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrderOperation,
        isNullable: true,
        async control(cx, val) {
            if ((await this.lineType) === 'text' && val) {
                cx.error.addLocalized(
                    '@sage/xtrem-manufacturing/nodes__work_order_component__no_operation_for_text_line',
                    'Operation must be null for text line type.',
                );
            }
            if ((await this.lineStatus) !== 'excluded' && (await val?.status) === 'excluded') {
                cx.error.addLocalized(
                    '@sage/xtrem-manufacturing/nodes__work_order_component__operation_excluded',
                    'The {{cmpNumber}} component cannot be linked to the {{opeNumber}} excluded operation.',
                    { cmpNumber: await this.componentNumber, opeNumber: await val?.operationNumber },
                );
            }
        },
    })
    readonly operation: Reference<xtremManufacturing.nodes.WorkOrderOperation | null>;

    @decorators.decimalProperty<WorkOrderComponent, 'requiredQuantity'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        dependsOn: ['linkQuantity'],
        defaultValue() {
            return this.linkQuantity;
        },
        async control(cx, val) {
            await xtremTechnicalData.events.control.componentLineControls.componentLineNotZeroNumberRequired(
                cx,
                await this.lineType,
                val,
            );
        },
    })
    readonly requiredQuantity: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderComponent, 'consumedQuantity'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        async control(cx, val) {
            await xtremManufacturing.events.control.componentLineControls.componentLinePositiveNumberRequired(
                cx,
                await this.lineType,
                val,
            );
        },
        duplicatedValue: 0,
    })
    readonly consumedQuantity: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderComponent, 'quantityInStockUnit'>({
        dependsOn: ['requiredQuantity', 'consumedQuantity'],
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        async getValue() {
            return (await this.requiredQuantity) - (await this.consumedQuantity);
        },
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.dateProperty<WorkOrderComponent, 'requiredDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        async control(cx, val) {
            xtremTechnicalData.events.control.componentLineControls.componentLinePropertyRequired(
                cx,
                await this.lineType,
                val,
                'Date',
            );
        },
        async duplicatedValue() {
            return (await this.lineType) === 'text' ? null : (await this.workOrder).startDate;
        },
    })
    readonly requiredDate: Promise<date | null>;

    // lineStatus is 'completed' if Consumed quantity >= Required quantity
    //                           or it is with a material tracking completed
    // lineStatus is 'pending' if Consumed quantity = 0
    // lineStatus is 'in progress' if Consumed quantity < Required quantity
    @decorators.enumProperty<WorkOrderComponent, 'lineStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremManufacturing.enums.componentStatusDataType,
        defaultValue: () => 'pending',
        lookupAccess: true,
        async control(cx, val) {
            if ((await this.lineType) === 'text' && ['pending', 'inProgress', 'included'].includes(val)) {
                cx.error.addLocalized(
                    '@sage/xtrem-manufacturing/nodes__work_order_component__text_work_order_component_not_completed_or_excluded',
                    'The work order component status needs to be completed or excluded if the type is Text.',
                );
            }
        },
        async duplicatedValue() {
            if ((await this.lineType) === 'text') {
                return 'completed';
            }
            return (await this.lineStatus) !== 'excluded' ? 'pending' : this.lineStatus;
        },
    })
    readonly lineStatus: Promise<xtremManufacturing.enums.ComponentStatus>;

    // technical property to allow filter on status
    @decorators.enumProperty<WorkOrderComponent, 'uLineStatus'>({
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        dataType: () => xtremManufacturing.enums.workOrderLineStatusFilteredDataType,
        async getValue() {
            if (['included', 'inProgress', 'pending'].includes(await this.lineStatus)) {
                return (await this.lineStatus) as xtremManufacturing.enums.WorkOrderLineStatusFiltered;
            }
            return null;
        },
    })
    readonly uLineStatus: Promise<xtremManufacturing.enums.WorkOrderLineStatusFiltered | null>;

    @decorators.booleanProperty<WorkOrderComponent, 'isAdded'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        lookupAccess: true,
    })
    readonly isAdded: Promise<boolean>;

    @decorators.decimalProperty<WorkOrderComponent, 'plannedCost'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['item', 'requiredQuantity', 'requiredDate', 'lineStatus', 'workOrder'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue() {
            return this.getPlannedCost();
        },
        // The plannedCost must be updated only on demand
        lookupAccess: true,
    })
    readonly plannedCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderComponent, 'actualCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        isStored: true,
        duplicatedValue: 0,
        lookupAccess: true,
    })
    readonly actualCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderComponent, 'completedQuantityPercentage'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.percentageWorkOrderDataType,
        lookupAccess: true,
        async getValue() {
            return (
                ((await this.lineStatus) === 'completed'
                    ? 1
                    : (await this.consumedQuantity) / (await this.requiredQuantity)) * 100
            );
        },
    })
    readonly completedQuantityPercentage: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderComponent, 'remainingQuantityToReport'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        async getValue() {
            return (await this.requiredQuantity) - (await this.consumedQuantity);
        },
        lookupAccess: true,
    })
    readonly remainingQuantityToReport: Promise<decimal>;

    @decorators.referenceProperty<WorkOrderComponent, 'workInProgress'>({
        isPublished: true,
        isVital: true,
        isNullable: true,
        reverseReference: 'workOrderComponent',
        node: () => xtremManufacturing.nodes.WorkInProgressWorkOrderComponent,
        // FIXME: move instance create from createEnd here once the platform fixes usage of defaultValue on vital
        // defaultValue() {
        //     // TODO: create entry according to document line criteria
        //     this.$.set({workInProgress: {}});
        // },
        async control(cx, val) {
            if ((await this.lineType) === 'text' && val) {
                cx.error.addLocalized(
                    '@sage/xtrem-manufacturing/nodes__work_order_component__work_in_progress_forbidden',
                    'Work in progress is not needed for a text component.',
                );
            }
        },
    })
    readonly workInProgress: Reference<xtremManufacturing.nodes.WorkInProgressWorkOrderComponent | null>;

    @decorators.jsonProperty<WorkOrderComponent, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        lookupAccess: true,
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<WorkOrderComponent, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<WorkOrderComponent, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        lookupAccess: true,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<WorkOrderComponent, 'computedAttributes'>({
        isPublished: true,
        dependsOn: [{ workOrder: ['site'] }],
        lookupAccess: true,
        async computeValue() {
            const workOrder = await this.workOrder;
            return xtremFinanceData.functions.computeGenericAttributes(this.$.context, {
                item: await this.item,
                stockSite: await workOrder.site,
                financialSite: await workOrder.site,
                returnBusinessSite: false,
            });
        },
    })
    readonly computedAttributes: Promise<object>;

    @decorators.collectionProperty<WorkOrderComponent, 'stockAllocations'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockAllocation,
        lookupAccess: true,
    })
    readonly stockAllocations: Collection<xtremStockData.nodes.StockAllocation>;

    @decorators.decimalProperty<WorkOrderComponent, 'quantityAllocated'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        getValue() {
            // TODO: this logic should be moved to function that update/create the stock allocation.
            //       This field will be stored. It will be updated during update/create the stock allocation.
            return this.stockAllocations.sum(allocation => allocation.quantityInStockUnit);
        },
    })
    readonly quantityAllocated: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderComponent, 'remainingQuantityToAllocate'>({
        isPublished: true,
        dependsOn: ['requiredQuantity', 'quantityAllocated', 'consumedQuantity'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async getValue() {
            return (await this.requiredQuantity) - (await this.quantityAllocated) - (await this.consumedQuantity);
        },
        lookupAccess: true,
    })
    readonly remainingQuantityToAllocate: Promise<decimal>;

    @decorators.enumProperty<WorkOrderComponent, 'allocationStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        dependsOn: ['remainingQuantityToAllocate', 'quantityAllocated', 'item'],
        async getValue() {
            return xtremStockData.functions.allocationLib.getLineAllocationStatus(
                await this.remainingQuantityToAllocate,
                await this.quantityAllocated,
                (await (await this.item)?.isStockManaged) ?? false,
            );
        },
        lookupAccess: true,
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    @decorators.enumProperty<WorkOrderComponent, 'allocationRequestStatus'>({
        isPublished: true,
        isStored: true,
        defaultValue: 'noRequest',
        duplicatedValue: 'noRequest',
        dataType: () => xtremStockData.enums.allocationRequestStatusDataType,
        lookupAccess: true,
    })
    readonly allocationRequestStatus: Promise<xtremStockData.enums.AllocationRequestStatus>;

    /** Linked component  */
    @decorators.referenceProperty<WorkOrderComponent, 'component'>({
        isPublished: true,
        isNullable: true,
        dependsOn: ['workOrder', { workOrder: ['bomCode'] }],
        node: () => xtremTechnicalData.nodes.Component,
        // Because of phantoms, this computation is no more correct and it's not possible to store it as it will block the modification of the BOM
        // => in the 1st phase of development of phantom, we just return null
        //
        // async computeValue() {
        //     const billOfMaterial = await (await this.workOrder).bomCode;
        //     const componentNumber = await this.componentNumber;
        //     return billOfMaterial
        //         ? (await this.$.context.tryRead(xtremTechnicalData.nodes.Component, {
        //               billOfMaterial,
        //               componentNumber,
        //           })) ?? null
        //         : null;
        // },
        getValue() {
            return null;
        },
    })
    readonly component: Reference<xtremTechnicalData.nodes.Component | null>;

    /** additional instructions for the shop floor operator */
    @decorators.textStreamProperty<WorkOrderComponent, 'instruction'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['component'],
        dataType: () => xtremTechnicalData.dataTypes.textStreamType,
        async defaultValue() {
            return (await this.component)?.instruction || TextStream.empty;
        },
        lookupAccess: true,
    })
    readonly instruction: Promise<TextStream>;

    @decorators.decimalProperty<WorkOrderComponent, 'stockOnHand'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['itemSite'],
        async getValue() {
            const itemSite = await this.itemSite;
            if (itemSite) {
                return itemSite.inStockQuantity;
            }
            return 0;
        },
        lookupAccess: true,
    })
    readonly stockOnHand: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderComponent, 'availableQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['unit', 'item', { workOrder: ['site'] }],
        lookupAccess: true,
        async getValue() {
            const itemSite = await this.itemSite;
            if (itemSite) {
                return (await itemSite.acceptedStockQuantity) - (await itemSite.allocatedQuantity);
            }
            return 0;
        },
    })
    readonly availableQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderComponent, 'stockShortageInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['unit', 'quantityInStockUnit', 'availableQuantityInStockUnit', 'quantityAllocated'],
        async getValue() {
            const availableQuantityInStockUnit = await this.availableQuantityInStockUnit;
            const quantityInStockUnit = await this.quantityInStockUnit;
            const quantityAllocated = await this.quantityAllocated;

            if (availableQuantityInStockUnit + quantityAllocated < quantityInStockUnit) {
                return quantityInStockUnit - (availableQuantityInStockUnit + quantityAllocated);
            }

            return 0;
        },
        lookupAccess: true,
    })
    readonly stockShortageInStockUnit: Promise<decimal>;

    @decorators.booleanProperty<WorkOrderComponent, 'stockShortageStatus'>({
        isPublished: true,
        dependsOn: ['stockShortageInStockUnit'],
        async getValue() {
            return (await this.stockShortageInStockUnit) > 0;
        },
        lookupAccess: true,
    })
    readonly stockShortageStatus: Promise<boolean>;

    @decorators.mutation<typeof WorkOrderComponent, 'updateComponent'>({
        isPublished: true,
        parameters: [
            { name: 'workOrderNumber', type: 'string' },
            { name: 'componentNumber', type: 'integer' },
            { name: 'quantity', type: 'decimal' },
            { name: 'cost', type: 'decimal' },
            { name: 'completed', type: 'boolean' },
        ],
        return: {
            type: 'reference',
            node: () => xtremManufacturing.nodes.WorkOrderComponent,
        },
    })
    static async updateComponent(
        context: Context,
        workOrderNumber: string,
        componentNumber: integer,
        quantity: decimal,
        cost: decimal,
        completed: boolean,
    ): Promise<xtremManufacturing.nodes.WorkOrderComponent | null> {
        const workOrder = await context.read(
            xtremManufacturing.nodes.WorkOrder,
            { number: workOrderNumber },
            { forUpdate: true },
        );
        const component = await workOrder.productionComponents.find(
            async cpn => (await cpn.componentNumber) === componentNumber,
        );
        if (component) {
            if (completed) {
                await component.$.set({ lineStatus: 'completed' });
            }
            await component.$.set({
                consumedQuantity: (await component.consumedQuantity) + quantity,
                actualCost: (await component.actualCost) + cost * quantity,
            });
            await workOrder.$.save();

            // FIXME: it works but it could be enhanced
            await workOrder.$.set({
                status: await xtremManufacturing.functions.workOrderLib.getWorkOrderStatus(workOrder),
            });
            await workOrder.$.save();

            return component;
        }
        return null;
    }

    @decorators.query<typeof WorkOrderComponent, 'setRequiredQuantity'>({
        isPublished: true,
        parameters: [
            { name: 'releasedQuantity', type: 'decimal' },
            { name: 'baseQuantity', type: 'decimal' },
            { name: 'linkQuantity', type: 'decimal' },
            { name: 'isFixed', type: 'boolean' },
            { name: 'scrapFactor', type: 'decimal' },
            { name: 'decimalPlaces', type: 'decimal' },
        ],
        return: 'decimal',
    })
    static setRequiredQuantity(
        _context: Context,
        releasedQuantity: decimal,
        baseQuantity: decimal,
        linkQuantity: decimal,
        isFixed: boolean,
        scrapFactor: decimal,
        decimalPlaces: decimal,
    ): decimal {
        return xtremTechnicalData.functions.componentFunctions.setRequiredQuantity(
            releasedQuantity,
            baseQuantity,
            linkQuantity,
            isFixed,
            scrapFactor,
            decimalPlaces,
        );
    }

    @decorators.query<typeof WorkOrderComponent, 'checkComponentStock'>({
        isPublished: true,
        parameters: [
            { name: 'orderQuantity', type: 'decimal', isMandatory: true },
            { name: 'bomId', type: 'string', isMandatory: true },
        ],
        return: {
            type: 'array',
            item: {
                name: 'stockShortage',
                type: 'object',
                properties: {
                    _id: { type: 'string', isMandatory: true },
                    itemId: { type: 'string', isMandatory: true },
                    itemDescription: { type: 'string', isMandatory: true },
                    itemName: { type: 'string', isMandatory: true },
                    componentName: { type: 'string', isMandatory: true },
                    available: { type: 'decimal', isMandatory: true },
                    required: { type: 'decimal', isMandatory: true },
                    shortage: { type: 'decimal', isMandatory: true },
                    unit: { type: 'string', isMandatory: true },
                    componentNumber: { type: 'integer', isMandatory: true },
                },
            },
        },
    })
    static async checkComponentStock(
        context: Context,
        orderQuantity: decimal,
        bomId: string,
    ): Promise<xtremManufacturing.interfaces.ComponentShortageInfo[]> {
        const billOfMaterial = await context.read(xtremTechnicalData.nodes.BillOfMaterial, { _id: bomId });

        const bomComponents = await billOfMaterial.components.toArray();
        const stockShortage: xtremManufacturing.interfaces.ComponentShortageInfo[] = [];

        await asyncArray(bomComponents).forEach(async (component: xtremTechnicalData.nodes.Component) => {
            if ((await component.lineType) === 'text') {
                return;
            }

            const requiredQuantity = xtremTechnicalData.functions.componentFunctions.setRequiredQuantity(
                orderQuantity,
                await billOfMaterial.baseQuantity,
                await component.linkQuantity,
                await component.isFixedLinkQuantity,
                await component.scrapFactor,
                await (
                    await component.unit
                )?.decimalDigits,
            );

            const availableStock = await xtremStockData.functions.stockLib.searchStock(context, {
                item: (await component.item) || (await billOfMaterial.item),
                site: await billOfMaterial.site,
                activeQuantityInStockUnit: 0,
                stockCharacteristics: [{ statusList: [{ statusType: 'accepted' }] }],
                ignoreExpirationDate: false,
            });

            let totalAvailableStock = 0;

            availableStock.forEach(stock => {
                totalAvailableStock += stock.quantityInStockUnit;
            });

            const componentItem = await component.item;
            const newShortage: xtremManufacturing.interfaces.ComponentShortageInfo = {
                _id: component._id.toString(),
                itemId: (await componentItem?.id) ?? '',
                itemDescription: (await componentItem?.description) ?? '',
                itemName: (await componentItem?.name) ?? '',
                componentName: await component.name,
                available: Math.round(totalAvailableStock),
                required: Math.round(Number(requiredQuantity)),
                unit:
                    (await (await componentItem?.stockUnit)?.symbol) || (await (await billOfMaterial.stockUnit).symbol),
                componentNumber: await component.componentNumber,
            };

            const remainingQuantity = newShortage.available - newShortage.required;

            newShortage.shortage = remainingQuantity >= 0 ? undefined : Math.abs(remainingQuantity);

            stockShortage.push(newShortage);
        });

        return stockShortage;
    }

    @decorators.bulkMutation<typeof WorkOrderComponent, 'massAutoAllocation'>({
        isPublished: true,
        isSchedulable: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    requestType: {
                        isMandatory: true,
                        type: 'enum',
                        dataType: () => xtremStockData.enums.allocationRequestTypeDataType,
                    },
                    requestDescription: 'string',
                    userEntries: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                lineId: { isMandatory: true, type: 'integer' },
                                quantityToProcess: { isMandatory: true, type: 'decimal' },
                            },
                        },
                    },
                },
            },
            // make sure that the stockAllocationParameters is matched with the output of getStockAllocationParameterGraphQLDescriptor function
            {
                name: 'stockAllocationParameters',
                type: 'object',
                properties: {
                    cannotOverAllocate: 'boolean',
                    shouldControlAllocationInProgress: 'boolean',
                },
            },
        ],
        onComplete(context: Context, workOrderComponentUpdateData) {
            return xtremStockData.functions.automaticAllocationLib.massAllocationOnCompleteHandler<xtremManufacturing.nodes.WorkOrder>(
                context,
                {
                    documentClass: xtremManufacturing.nodes.WorkOrder,
                    documentType: 'workOrder',
                    allocableLinesCollectionName: 'productionComponents',
                    documentLineUpdateData: workOrderComponentUpdateData,
                    convertFilterToMassProcessCriteriaCallBack:
                        xtremManufacturing.functions.allocationLib.manufacturingConvertFilterToMassProcessCriteria,
                },
            );
        },
    })
    static massAutoAllocation(
        context: Context,
        workOrderComponent: WorkOrderComponent,
        data: {
            requestType: xtremStockData.enums.AllocationRequestType;
            requestDescription: string;
            userEntries?: {
                lineId: integer;
                quantityToProcess: decimal;
            }[];
        },
        stockAllocationParameters?: xtremStockData.interfaces.StockAllocationParameters,
    ) {
        return xtremStockData.functions.automaticAllocationLib.massAllocationLineHandler<WorkOrderComponent>(
            context,
            {
                documentLine: workOrderComponent,
                requestType: data.requestType,
                documentType: 'workOrder',
                requestDescription: data.requestDescription,
                userEntries: data.userEntries,
                lineFilterCallback: xtremManufacturing.functions.workOrderLib.isComponentAllocable,
            },
            stockAllocationParameters,
        );
    }

    @decorators.query<typeof WorkOrderComponent, 'getMaterialPlannedCost'>({
        isPublished: true,
        parameters: [
            {
                name: 'searchCriteria',
                type: 'object',
                isMandatory: true,
                properties: {
                    requiredQuantity: { type: 'decimal', isMandatory: true },
                    requiredDate: { type: 'date', isMandatory: true },
                    item: { type: 'reference', node: () => xtremMasterData.nodes.Item, isMandatory: true },
                    site: { type: 'reference', node: () => xtremSystem.nodes.Site, isMandatory: true },
                    lineStatus: {
                        type: 'enum',
                        dataType: () => xtremManufacturing.enums.componentStatusDataType,
                        isMandatory: true,
                    },
                },
            },
        ],
        return: 'decimal',
    })
    static getMaterialPlannedCost(
        context: Context,
        searchCriteria: {
            requiredQuantity: decimal;
            requiredDate: date;
            item: xtremMasterData.nodes.Item;
            site: xtremSystem.nodes.Site;
            lineStatus: xtremManufacturing.enums.ComponentStatus;
        },
    ): Promise<decimal> {
        return xtremManufacturing.functions.workOrderLib.getMaterialPlannedCost(context, {
            requiredDate: searchCriteria.requiredDate,
            requiredQuantity: searchCriteria.requiredQuantity,
            itemSite: { item: searchCriteria.item, site: searchCriteria.site },
            lineStatus: searchCriteria.lineStatus,
        });
    }

    async getPlannedCost() {
        if ((await this.lineStatus) === 'included') {
            return 0;
        }
        const requiredDate = await this.requiredDate;
        if (!((await this.item) && requiredDate)) {
            return 0;
        }
        const itemSite = await this.itemSite;
        if (!itemSite) {
            return 0;
        }
        return xtremManufacturing.functions.workOrderLib.getMaterialPlannedCost(this.$.context, {
            requiredDate,
            requiredQuantity: await this.requiredQuantity,
            itemSite,
            lineStatus: await this.lineStatus,
        });
    }
}
