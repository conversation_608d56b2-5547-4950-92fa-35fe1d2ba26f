import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremManufacturing from '../index';

@decorators.node<WorkOrderSerialNumber>({
    serviceOptions: () => [xtremMasterData.serviceOptions.serialNumberOption],
    package: 'xtrem-manufacturing',
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    canSearch: true,
    isCustomizable: true,
})
export class WorkOrderSerialNumber extends Node {
    @decorators.referenceProperty<WorkOrderSerialNumber, 'workOrder'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremManufacturing.nodes.WorkOrder,
    })
    readonly workOrder: Reference<xtremManufacturing.nodes.WorkOrder>;

    @decorators.referenceProperty<WorkOrderSerialNumber, 'serialNumber'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremStockData.nodes.SerialNumber,
    })
    readonly serialNumber: Reference<xtremStockData.nodes.SerialNumber | null>;
}
