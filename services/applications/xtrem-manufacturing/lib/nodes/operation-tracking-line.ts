import type { Collection, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import { onLineAdded } from '../functions/operation-tracking-line';
import * as xtremManufacturing from '../index';

@decorators.subNode<OperationTrackingLine>({
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    isPublished: true,
    canRead: true,
    isVitalCollectionChild: true,
    controlDelete(cx) {
        cx.addDiagnose(
            ValidationSeverity.error,
            cx.localize(
                '@sage/xtrem-manufacturing/nodes__operation_tracking_line__deletion_forbidden',
                'This operation tracking line cannot be deleted.',
            ),
        );
    },
    async controlBegin(cx) {
        await cx.error
            .withMessage(
                '@sage/xtrem-manufacturing/nodes__operation_tracking__work_order_operation_must_comply',
                'The work order operation status must be different from Excluded.',
            )
            .if(await (await this.workOrderOperation).status)
            .is.equal.to('excluded');
    },
    async saveEnd() {
        if (this.$.status === 'added') {
            await onLineAdded(this);
        }
    },
})
export class OperationTrackingLine
    extends xtremMasterData.nodes.BaseDocumentLine
    implements xtremFinanceData.interfaces.OperationTrackingFinanceDocumentLine
{
    @decorators.referenceProperty<OperationTrackingLine, 'document'>({
        isStored: true,
        isPublished: true,
        node: () => xtremManufacturing.nodes.OperationTracking,
        isVitalParent: true,
    })
    override readonly document: Reference<xtremManufacturing.nodes.OperationTracking>;

    @decorators.stringPropertyOverride<OperationTrackingLine, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<OperationTrackingLine, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.integerProperty<OperationTrackingLine, 'line'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly line: Promise<integer>;

    @decorators.referenceProperty<OperationTrackingLine, 'workOrderOperation'>({
        isStored: true,
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrderOperation,
        filters: {
            control: {
                async workOrder() {
                    return (await this.document).workOrder;
                },
            },
        },
    })
    readonly workOrderOperation: Reference<xtremManufacturing.nodes.WorkOrderOperation>;

    @decorators.referenceProperty<OperationTrackingLine, 'operationResource'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremManufacturing.nodes.WorkOrderOperationResource,
        dependsOn: ['workOrderOperation', 'actualResource'],
        join: {
            workOrderOperation: 'workOrderOperation',
            resource() {
                return this.actualResource;
            },
        },
    })
    readonly operationResource: Reference<xtremManufacturing.nodes.WorkOrderOperationResource | null>;

    @decorators.stringProperty<OperationTrackingLine, 'trackingType'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.code,
        defaultValue: 'WO',
    })
    readonly trackingType: Promise<string>;

    @decorators.decimalProperty<OperationTrackingLine, 'completedQuantity'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        async control(cx, quantity) {
            await cx.error.if(quantity).is.negative();
            const resource = await this.operationResource;
            if (quantity && resource) {
                await cx.warn
                    .withMessage(
                        '@sage/xtrem-manufacturing/nodes__operation_tracking__line__completed_quantity_must_comply',
                        'You need to add an operation quantity for the operation resource.',
                    )
                    .if(await resource.isResourceQuantity)
                    .is.false();
            }
        },
    })
    readonly completedQuantity: Promise<decimal>;

    @decorators.referenceProperty<OperationTrackingLine, 'actualResource'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.DetailedResource,
        async control(cx, val) {
            const siteId = await (await (await (await this.workOrderOperation).workOrder).site).id;
            await xtremTechnicalData.events.control.operationResourceControls.operationResourceGroupValidation(
                siteId,
                cx,
                val,
            );
        },
    })
    readonly actualResource: Reference<xtremMasterData.nodes.DetailedResource>;

    @decorators.referenceProperty<OperationTrackingLine, 'setupTimeUnit'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: ['workOrderOperation'],
        async defaultValue() {
            return (await this.workOrderOperation).setupTimeUnit;
        },
        filters: { control: { type: { _eq: 'time' } } },
    })
    readonly setupTimeUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referenceProperty<OperationTrackingLine, 'runTimeUnit'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: ['workOrderOperation'],
        async defaultValue() {
            return (await this.workOrderOperation).runTimeUnit;
        },
        filters: { control: { type: { _eq: 'time' } } },
    })
    readonly runTimeUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<OperationTrackingLine, 'actualSetupTime'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.setupTimeDataType,
        async control(cx, val: decimal) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly actualSetupTime: Promise<decimal>;

    @decorators.decimalProperty<OperationTrackingLine, 'actualRunTime'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.runTimeDataType,
        async control(cx, val: decimal) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly actualRunTime: Promise<decimal>;

    @decorators.booleanProperty<OperationTrackingLine, 'completed'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly completed: Promise<boolean>;

    @decorators.jsonProperty<OperationTrackingLine, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<OperationTrackingLine, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<OperationTrackingLine, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<OperationTrackingLine, 'computedAttributes'>({
        isPublished: true,
        async computeValue() {
            return xtremFinanceData.functions.computeGenericAttributes(this.$.context, {
                stockSite: await (await this.actualResource).site,
                financialSite: await (await this.actualResource).site,
                returnBusinessSite: false,
            });
        },
    })
    readonly computedAttributes: Promise<object>;

    // reference to wip cost of line for the accounting interface
    @decorators.decimalProperty<OperationTrackingLine, 'workInProgressCostAmount'>({
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            // we are forced to use a query here because there is no index on WorkInProgressCost node
            return (
                (await (
                    await this.$.context
                        .query(xtremManufacturing.nodes.WorkInProgressCost, {
                            filter: {
                                originatingLine: this._id,
                                type: { _in: ['setupTimeTracking', 'runTimeTracking'] },
                            },
                            last: 1,
                        })
                        .at(0)
                )?.amount) || 0
            );
        },
    })
    readonly workInProgressCostAmount: Promise<decimal>;

    // reference to item for the accounting interface
    @decorators.referenceProperty<OperationTrackingLine, 'item'>({
        isPublished: true,
        async getValue() {
            return (await (
                await (
                    await (
                        await this.workOrderOperation
                    ).workOrder
                ).productionItem
            ).releasedItem) as xtremMasterData.nodes.Item;
        },
        node: () => xtremMasterData.nodes.Item,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    // reference to wip cost type of line for the accounting interface
    @decorators.stringProperty<OperationTrackingLine, 'workInProgressCostType'>({
        async computeValue() {
            // we are forced to use a query here because there is no index on WorkInProgressCost node
            const type = await (
                await this.$.context
                    .query(xtremManufacturing.nodes.WorkInProgressCost, {
                        filter: {
                            originatingLine: this._id,
                            type: { _in: ['setupTimeTracking', 'runTimeTracking'] },
                        },
                        last: 1,
                    })
                    .at(0)
            )?.type;
            return type === 'setupTimeTracking' ? 'setupTimeTracking' : 'runTimeTracking';
        },
    })
    readonly workInProgressCostType: Promise<string>;

    // reference to wip cost type of line for the accounting interface
    @decorators.stringProperty<OperationTrackingLine, 'workInProgressActualResourceType'>({
        async computeValue() {
            const actualResource = await this.actualResource;
            return xtremManufacturing.functions.actualResourceType(actualResource, await actualResource.resourceGroup);
        },
    })
    readonly workInProgressActualResourceType: Promise<string>;

    // work order number for the accounting interface
    @decorators.stringProperty<OperationTrackingLine, 'workInProgressWorkOrderNumber'>({
        isPublished: true,
        async getValue() {
            return (await (await this.workOrderOperation).workOrder).number;
        },
    })
    readonly workInProgressWorkOrderNumber: Promise<string>;

    // work order _id for the accounting interface
    @decorators.integerProperty<OperationTrackingLine, 'workInProgressWorkOrderSysId'>({
        isPublished: true,
        async getValue() {
            return (await (await this.workOrderOperation).workOrder)._id;
        },
    })
    readonly workInProgressWorkOrderSysId: Promise<integer>;

    @decorators.collectionProperty<OperationTrackingLine, 'workInProgressCosts'>({
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkInProgressCost,
        getFilter() {
            return {
                originatingLine: this._id,
                type: { _in: ['setupTimeTracking', 'runTimeTracking'] },
            };
        },
    })
    readonly workInProgressCosts: Collection<xtremManufacturing.nodes.WorkInProgressCost>;
}
