import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, datetime, decimal, integer, Reference } from '@sage/xtrem-core';
import {
    BusinessRuleError,
    date,
    decorators,
    Logger,
    LogicError,
    Node,
    NodeStatus,
    TextStream,
    useDefaultValue,
} from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import * as xtremManufacturing from '../../index';
import { workOrderLib } from '../functions/index';
import { beforeCloseWorkOrder, controlClosingDate } from '../functions/work-order-close';

const logger = Logger.getLogger(__filename, 'work-order');

@decorators.node<WorkOrder>({
    isClearedByReset: true,
    package: 'xtrem-manufacturing',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isCustomizable: true,
    canDuplicate: true,
    hasAttachments: true,
    notifies: ['created', 'updated', 'deleted'],
    indexes: [{ orderBy: { site: +1, number: +1 }, isUnique: true, isNaturalKey: true }],
    async prepare() {
        // TODO: make sure the cost are splitted when the time comes to have multiple released-items
        if (xtremMasterData.functions.nodeIsBeingCreated(this)) {
            await xtremManufacturing.functions.workOrderLib.updateReleasedItemPlannedCosts(this, {
                canEventLinkRoutingOrBOM: true,
                hasLinkedBOM: !!(await this.bomCode)?._id,
                hasLinkedRouting: !!(await this.routingCode)?._id,
            });
        }

        // Operations included to WO by time tracking are not WO planned operations
        // Consequently they are not added to WO planned costs and not concerned by scheduling
        let status = await this.schedulingStatus;
        if (
            (await this.isSchedulingSkipped) ||
            (await this.productionOperations.filter(async operation => (await operation.status) !== 'included')
                .length) === 0
        ) {
            status = 'notManaged';
        } else if (status === 'notManaged') {
            status = 'notScheduled';
        }
        if (status !== (await this.schedulingStatus)) {
            await this.$.set({
                schedulingStatus: status,
            });
        }

        if (await this.isDuplication) {
            await xtremManufacturing.functions.recalculateWorkOrderDates(this);
            await this.$.set({
                isDuplication: false,
            });
        }
        if (
            xtremMasterData.functions.nodeIsBeingModified(this) &&
            !(await this.forceUpdateForScheduling) &&
            !['inProgress', 'toReschedule'].includes(await this.schedulingStatus) &&
            (await xtremManufacturing.functions.workOrderLib.operationDatesNeedUpdating(this, await this.$.old))
        ) {
            switch (await this.schedulingStatus) {
                case 'notManaged':
                case 'notScheduled':
                    await xtremManufacturing.functions.recalculateWorkOrderDates(this);
                    break;
                case 'scheduled':
                    await this.$.set({ schedulingStatus: 'toReschedule' });
                    break;
                default:
                    break;
            }
        }

        if (
            xtremMasterData.functions.nodeIsBeingModified(this) &&
            (await (await this.$.old).startDate) !== (await this.startDate)
        ) {
            // If a component is not modified => change its required date
            // Otherwise consider that the required date has been passed and then mustn't be managed here
            await this.productionComponents
                .filter(
                    async (productionComponent: xtremManufacturing.nodes.WorkOrderComponent) =>
                        (await productionComponent.lineStatus) !== 'excluded' &&
                        (await productionComponent.lineType) !== 'text',
                )
                .forEach(async (productionComponent: xtremManufacturing.nodes.WorkOrderComponent) => {
                    await productionComponent.$.set({ requiredDate: await this.startDate });
                });
        }
    },
    async controlEnd(cx) {
        if ((await this.productionOperations.length) > 0) {
            const productionStepCount = await this.productionOperations?.filter(
                async workOrderOperation =>
                    (await workOrderOperation.isProductionStep) && (await workOrderOperation.status) !== 'excluded',
            ).length;

            if (productionStepCount === 0) {
                cx.error.addLocalized(
                    '@sage/xtrem-manufacturing/nodes__work_order__no_production_step',
                    'At least one operation with a production step is required.',
                );
            } else if (productionStepCount > 1) {
                cx.error.addLocalized(
                    '@sage/xtrem-manufacturing/nodes__work_order__multiple_production_step',
                    'Only one operation with a production step is allowed.',
                );
            }
        }
        if (
            xtremMasterData.functions.nodeIsBeingModified(this) &&
            !(await this.forceUpdateForScheduling) &&
            (await this.schedulingStatus) === 'inProgress' &&
            (await xtremManufacturing.functions.workOrderLib.operationDatesNeedUpdating(this, await this.$.old))
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-manufacturing/nodes__work_order__forbid_changes_impacting_scheduling',
                'You can save your changes when this work order is scheduled. Try again in a few minutes.',
            );
        }
    },
    async saveBegin() {
        // XT-35758
        // Default the closing date with the end date if closed.
        if (!(await this.closingDate) && (await this.status) === 'closed') {
            await this.$.set({ closingDate: await this.endDate });
        }

        if (!(await this.number)) {
            await this.$.set({
                number: await (
                    await xtremMasterData.classes.DocumentNumberGenerator.create(this.$.context, {
                        currentDate: date.today(),
                        nodeInstance: this,
                    })
                ).allocate(),
            });
        }
        if (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption)) {
            if (await (await this.routingCode)?.doSerialNumberPreGeneration)
                await this.handlePreGenerateSerialNumbers(false);
        }
    },
    async saveEnd() {
        if (await this.doInheritDimensions) {
            await workOrderLib.inheritDimensionsFromReleasedItems(this.$.context, this);
        }
    },
    async deleteBegin() {
        if (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption)) {
            if (await (await this.routingCode)?.doSerialNumberPreGeneration)
                await this.handlePreGenerateSerialNumbers(true);
        }
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class WorkOrder
    extends Node
    implements
        xtremMasterData.interfaces.Document,
        xtremFinanceData.interfaces.FinanceOriginDocument,
        xtremStockData.interfaces.DocumentHeaderWithStockAllocation
{
    async handlePreGenerateSerialNumbers(deleteAll: boolean) {
        const serialNumbers = await this.$.context
            .query(xtremStockData.nodes.SerialNumber, {
                filter: {
                    item: await (await this.productionItem)?.releasedItem,
                    site: await this.site,
                    baseDocumentLine: (await this.productionItems.elementAt(0))._id,
                    isUsable: false,
                    isInStock: false,
                },
                orderBy: { _id: -1 },
            })
            .toArray();
        if (serialNumbers.length > 0) {
            const diff = deleteAll
                ? serialNumbers.length
                : (await (await (await this.$.old).productionItems.elementAt(0)).releasedQuantity) -
                  (await (
                      await this.productionItems.elementAt(0)
                  ).releasedQuantity);
            if (diff >= 0) {
                for (let i = 0; i < diff; i += 1) {
                    await this.$.context.delete(xtremManufacturing.nodes.WorkOrderSerialNumber, {
                        serialNumber: { _id: serialNumbers[i]._id },
                    });
                    await this.$.context.delete(xtremStockData.nodes.SerialNumber, { _id: serialNumbers[i]._id });
                }
            } else {
                const relItem = await (await this.productionItem)?.releasedItem;
                if (relItem) {
                    await xtremStockData.nodes.SerialNumber.generateSerialNumbers(this.$.context, diff * -1, {
                        item: relItem,
                        site: await this.site,
                        baseDocumentLine: (await this.productionItems.elementAt(0))._id,
                        createDate: await this.endDate,
                    });
                }
            }
        }
    }

    getEffectiveDate(): Promise<date> {
        return this.endDate;
    }

    getStockSite() {
        return this.site;
    }

    isOperationDeleted: boolean;

    @decorators.booleanProperty<WorkOrder, 'isDuplication'>({
        isStored: true,
        defaultValue: false,
        duplicatedValue: true,
    })
    readonly isDuplication: Promise<boolean>;

    @decorators.referenceProperty<WorkOrder, 'site'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
        async control(cx, val) {
            await xtremSystem.events.control.isActive(
                this.$.status,
                cx,
                this.$.status === NodeStatus.modified ? (await this.$.old)._id : null,
                val,
            );
        },
        lookupAccess: true,
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.stringProperty<WorkOrder, 'number'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        provides: ['sequenceNumber'],
        lookupAccess: true,
        duplicatedValue: '',
        duplicateRequiresPrompt: true,
    })
    readonly number: Promise<string>;

    @decorators.enumProperty<WorkOrder, 'type'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremManufacturing.enums.workOrderTypeDataType,
        async isFrozen() {
            return (
                (await this.$.context.queryCount(xtremStockData.nodes.SerialNumber, {
                    filter: {
                        baseDocumentLine: (await this.productionItems.elementAt(0))._id,
                    },
                })) > 0
            );
        },
        async control(cx, val) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-manufacturing/nodes__work_order__type_must_be_firm',
                    'A work order in progress must be firm.',
                )
                .if(val === 'planned' && (await this.status) !== 'pending')
                .is.true();
        },
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly type: Promise<xtremManufacturing.enums.WorkOrderType>;

    @decorators.enumProperty<WorkOrder, 'typeFiltered'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremManufacturing.enums.workOrderFilteredTypeDataType,
    })
    readonly typeFiltered: Promise<xtremManufacturing.enums.WorkOrderFilteredType>;

    @decorators.enumProperty<WorkOrder, 'status'>({
        isStored: true,
        isPublished: true,
        defaultValue: 'pending',
        dataType: () => xtremManufacturing.enums.workOrderStatusDataType,
        lookupAccess: true,
        duplicatedValue: useDefaultValue,
    })
    readonly status: Promise<xtremManufacturing.enums.WorkOrderStatus>;

    @decorators.booleanProperty<WorkOrder, 'doInheritDimensions'>({
        isPublished: true,
        isTransientInput: true,
    })
    readonly doInheritDimensions: Promise<boolean>;

    /**
     * gives the status of the price correction launched by the mutation closeWorkOrder
     */
    @decorators.enumProperty<WorkOrder, 'stockTransactionStatus'>({
        isPublished: true,
        dependsOn: [{ productionItems: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.stringProperty<WorkOrder, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
        dependsOn: ['bomCode'],
        async defaultValue() {
            return (await this.bomCode)?.name ?? '';
        },
        async duplicatedValue() {
            return (await this.bomCode)?.name ?? '';
        },
    })
    readonly name: Promise<string>;

    @decorators.booleanProperty<WorkOrder, 'forceUpdateForScheduling'>({
        defaultValue: false,
    })
    readonly forceUpdateForScheduling: Promise<boolean>;

    @decorators.booleanProperty<WorkOrder, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    readonly forceUpdateForStock: Promise<boolean>;

    @decorators.booleanProperty<WorkOrder, 'isForwardScheduling'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        async control(cx, val) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-manufacturing/nodes__work_order__no_backward_scheduling',
                    'Backward scheduling is not available.',
                )
                .if(val)
                .is.false();
        },
    })
    readonly isForwardScheduling: Promise<boolean>;

    @decorators.booleanProperty<WorkOrder, 'isSchedulingSkipped'>({
        async isFrozen() {
            return !(await this.forceUpdateForScheduling);
        },
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isSchedulingSkipped: Promise<boolean>;

    // it is timeZone of site set when work order is scheduled
    @decorators.stringProperty<WorkOrder, 'timeZone'>({
        async isFrozen() {
            return !(await this.forceUpdateForScheduling);
        },
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.timeZone,
    })
    readonly timeZone: Promise<string>;

    @decorators.enumProperty<WorkOrder, 'schedulingStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremManufacturing.enums.WorkOrderSchedulingStatusDataType,
        duplicatedValue: 'notScheduled',
    })
    readonly schedulingStatus: Promise<xtremManufacturing.enums.WorkOrderSchedulingStatus>;

    // null for records created before this field was added
    @decorators.dateProperty<WorkOrder, 'creationDate'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        isNullable: true,
        defaultValue() {
            return date.today();
        },
    })
    readonly creationDate: Promise<date | null>;

    @decorators.dateProperty<WorkOrder, 'requestedDate'>({
        isStored: true,
        isPublished: true,
        async isFrozen() {
            return (await this.status) === 'completed';
        },
        duplicatedValue: date.today(),
        duplicateRequiresPrompt: true,
        lookupAccess: true,
    })
    readonly requestedDate: Promise<date>;

    @decorators.datetimeProperty<WorkOrder, 'startDatetime'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        duplicatedValue: null,
    })
    readonly startDatetime: Promise<datetime | null>;

    @decorators.dateProperty<WorkOrder, 'startDate'>({
        isStored: true,
        isPublished: true,
        async isFrozen() {
            return (await this.status) === 'completed';
        },
        dependsOn: ['startDatetime', 'requestedDate'],
        async defaultValue() {
            return (await this.startDatetime)?.date || this.requestedDate;
        },
        lookupAccess: true,
    })
    readonly startDate: Promise<date>;

    @decorators.datetimeProperty<WorkOrder, 'endDatetime'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        async control(cx, val) {
            xtremMasterData.events.control.timeControls.checkDatetimeRange(cx, await this.startDatetime, val);
        },
        duplicatedValue: null,
    })
    readonly endDatetime: Promise<datetime | null>;

    @decorators.dateProperty<WorkOrder, 'endDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['endDatetime', 'requestedDate'],
        async defaultValue() {
            return (await this.endDatetime)?.date || this.requestedDate;
        },
        async control(cx, val) {
            xtremMasterData.events.control.timeControls.checkDateRange(cx, await this.startDate, val);
        },
    })
    readonly endDate: Promise<date>;

    // XT-35758
    @decorators.dateProperty<WorkOrder, 'closingDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        duplicatedValue: null,
        async control(cx, closingDate) {
            // As long the work order is not closed everything is fine.
            if ((await this.status) !== 'closed') return;

            // Check if closing date is valid (no message).
            const message = await controlClosingDate(this.$.context, {
                filter: { _id: this._id },
                closingDate,
            });
            if (message.length) {
                cx.error.add(message);
            }
        },
    })
    readonly closingDate: Promise<date | null>;

    @decorators.referenceProperty<WorkOrder, 'category'>({
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        defaultValue() {
            return xtremManufacturing.functions.getDefaultCategory(this.$.context);
        },
        node: () => xtremManufacturing.nodes.WorkOrderCategory,
    })
    readonly category: Reference<xtremManufacturing.nodes.WorkOrderCategory>;

    @decorators.referenceProperty<WorkOrder, 'bomCode'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        lookupAccess: true,
        async isFrozen() {
            return (await this.bomCode) !== null;
        },
        node: () => xtremTechnicalData.nodes.BillOfMaterial,
        filters: { control: { item: { isPhantom: false } } },

        async control(cx, bomCode) {
            await xtremManufacturing.events.control.workOrderControls.workOrderBomCodeControl(cx, {
                bomCode,
                workOrder: this,
            });
        },
    })
    readonly bomCode: Reference<xtremTechnicalData.nodes.BillOfMaterial | null>;

    @decorators.referenceProperty<WorkOrder, 'bomRevision'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        lookupAccess: true,
        dependsOn: ['bomCode'],
        serviceOptions: () => [xtremMasterData.serviceOptions.billOfMaterialRevisionServiceOption],
        node: () => xtremTechnicalData.nodes.BillOfMaterialRevision,
        async isFrozen() {
            return (await this.bomCode) !== null;
        },
        async control(cx, bomRevision) {
            await xtremManufacturing.events.control.workOrderControls.workOrderBomRevisionControl(cx, {
                bomCode: await this.bomCode,
                bomRevision,
                workOrder: this,
            });
        },
    })
    readonly bomRevision: Reference<xtremTechnicalData.nodes.BillOfMaterialRevision | null>;

    @decorators.referenceProperty<WorkOrder, 'routingCode'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        lookupAccess: true,
        async isFrozen() {
            return (await this.routingCode) !== null;
        },
        node: () => xtremTechnicalData.nodes.Routing,
        async control(cx, val) {
            if (
                val &&
                (await val?.status) !== 'availableToUse' &&
                (this.$.status === NodeStatus.added ||
                    (this.$.status === NodeStatus.modified && !(await (await this.$.old).routingCode)))
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-manufacturing/nodes__work-order__routing_invalid',
                    'You can only add a routing that is available to use to work order.',
                );
            }
        },
    })
    readonly routingCode: Reference<xtremTechnicalData.nodes.Routing | null>;

    @decorators.referenceProperty<WorkOrder, 'routingTimeUnit'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        filters: { control: { type: { _eq: 'time' } } },
        isNullable: true,
    })
    readonly routingTimeUnit: Reference<xtremMasterData.nodes.UnitOfMeasure | null>;

    @decorators.decimalProperty<WorkOrder, 'baseQuantity'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        dependsOn: ['bomCode'],
        async defaultValue() {
            return (await this.bomCode)?.baseQuantity ?? 0;
        },
        async control(cx, val) {
            if (await this.bomCode) {
                await cx.error.if(val).is.not.greater.than(0);
            }
        },
    })
    readonly baseQuantity: Promise<decimal>;

    @decorators.decimalProperty<WorkOrder, 'baseRoutingQuantity'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        dependsOn: ['routingCode'],
        lookupAccess: true,
        async defaultValue() {
            return (await this.routingCode)?.batchQuantity ?? 0;
        },
        async control(cx, val) {
            if (await this.routingCode) {
                await cx.error.if(val).is.not.greater.than(0);
            }
        },
    })
    readonly baseRoutingQuantity: Promise<decimal>;

    @decorators.decimalProperty<WorkOrder, 'plannedMaterialCost'>({
        isPublished: true,
        dependsOn: ['productionItems'],
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        getValue() {
            return this.productionItems.sum(item => item.plannedMaterialCost);
        },
    })
    readonly plannedMaterialCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrder, 'plannedMachineCost'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['productionItems'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        getValue() {
            return this.productionItems.sum(item => item.plannedMachineCost);
        },
    })
    readonly plannedMachineCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrder, 'plannedLaborCost'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['productionItems'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        getValue() {
            return this.productionItems.sum(item => item.plannedLaborCost);
        },
    })
    readonly plannedLaborCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrder, 'plannedToolCost'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['productionItems'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        getValue() {
            return this.productionItems.sum(item => item.plannedToolCost);
        },
    })
    readonly plannedToolCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrder, 'plannedProcessCost'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['productionItems'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        getValue() {
            return this.productionItems.sum(item => item.plannedProcessCost);
        },
    })
    readonly plannedProcessCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrder, 'plannedOverheadCost'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: [
            'site',
            'bomCode',
            'plannedMaterialCost',
            'plannedProcessCost',
            'productionOperations',
            { productionOperations: ['expectedBatchCost'] },
            'productionComponents',
            { productionComponents: ['plannedCost'] },
            // 'productionItem', the build ask to add this one but it provoke a circular dependencies
        ],
        defaultValue: 0,
        async updatedValue() {
            const releasedItem = await (await this.productionItem)?.releasedItem;
            if (releasedItem) {
                return (
                    ((await this.plannedMaterialCost) + (await this.plannedProcessCost)) *
                    (await xtremManufacturing.functions.workOrderLib.getIndirectCost(
                        this.$.context,
                        (await this.site)._id,
                        releasedItem?._id,
                    ))
                );
            }
            return 0;
        },
    })
    readonly plannedOverheadCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrder, 'actualProcessCost'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        getValue() {
            return this.productionOperations.sum(operation => operation.actualBatchCost);
        },
    })
    readonly actualProcessCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrder, 'actualMaterialCost'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        getValue() {
            return this.productionComponents.sum(component => component.actualCost);
        },
    })
    readonly actualMaterialCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrder, 'actualOverheadCost'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue: 0,
        duplicatedValue: useDefaultValue,
    })
    readonly actualOverheadCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrder, 'wipVariance'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async computeValue() {
            const sumWipVariances = await this.$.context
                .queryAggregate(xtremManufacturing.nodes.WorkInProgressCost, {
                    group: { type: { _by: 'value' } },
                    filter: {
                        workOrder: this._id,
                        type: {
                            _in: xtremManufacturing.nodes.WorkInProgressCost.workInProgressVarianceTypes,
                        },
                    },
                    values: { amount: { sum: true } },
                })
                .toArray();
            return sumWipVariances.reduce(
                (sum, sumWipVariance) =>
                    sum +
                    (sumWipVariance.values.amount.sum ?? 0) *
                        (xtremManufacturing.nodes.WorkInProgressCost.workInProgressNegativeTypes.includes(
                            sumWipVariance.group.type,
                        )
                            ? -1
                            : 1),
                0,
            );
        },
    })
    readonly wipVariance: Promise<decimal>;

    @decorators.decimalProperty<WorkOrder, 'processCompletionPercentage'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.percentageWorkOrderDataType,
        async computeValue() {
            const expectedTime = await this.productionOperations
                .filter(async op => xtremManufacturing.functions.workOrderLib.costTypeOperations(await op.status))
                .sum(async operation => {
                    return (
                        (await xtremMasterData.functions.convertToSeconds(
                            await operation.setupTimeUnit,
                            await operation.expectedSetupTime,
                        )) +
                        (await xtremMasterData.functions.convertToSeconds(
                            await operation.runTimeUnit,
                            await operation.expectedRunTime,
                        ))
                    );
                });
            const completedTime = await this.productionOperations.sum(async operation => {
                return (
                    (await xtremMasterData.functions.convertToSeconds(
                        await operation.setupTimeUnit,
                        await operation.actualSetupTime,
                    )) +
                    (await xtremMasterData.functions.convertToSeconds(
                        await operation.runTimeUnit,
                        await operation.actualRunTime,
                    ))
                );
            });
            if (expectedTime === 0) {
                return 0;
            }
            return (completedTime / expectedTime) * 100;
        },
    })
    readonly processCompletionPercentage: Promise<decimal>;

    @decorators.decimalProperty<WorkOrder, 'materialCompletionPercentage'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.percentageWorkOrderDataType,
        async computeValue() {
            const requiredQuantity = await this.productionComponents
                .filter(async cmp => xtremManufacturing.functions.workOrderLib.costTypeComponents(await cmp.lineStatus))
                .sum(component => component.requiredQuantity);
            const completedQuantity = await this.productionComponents.sum(component => component.consumedQuantity);
            if (requiredQuantity === 0) {
                return 0;
            }
            return (completedQuantity / requiredQuantity) * 100;
        },
    })
    readonly materialCompletionPercentage: Promise<decimal>;

    @decorators.decimalProperty<WorkOrder, 'productionCompletionPercentage'>({
        lookupAccess: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.percentageWorkOrderDataType,
        async getValue() {
            const releasedQuantity = await this.productionItems.sum(workOrderItem => workOrderItem.releasedQuantity);
            const completedQuantity = await this.productionItems.sum(
                workOrderItem => workOrderItem.totalActualQuantity,
            );
            return (completedQuantity / releasedQuantity) * 100;
        },
    })
    readonly productionCompletionPercentage: Promise<decimal>;

    // property needed for the accounting interface
    @decorators.referenceProperty<WorkOrder, 'financialSite'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.Site,
        async getValue() {
            return xtremFinanceData.functions.getFinancialSite(await this.site);
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    // property needed for the accounting interface
    @decorators.referenceProperty<WorkOrder, 'transactionCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    // property needed for the accounting interface
    @decorators.dateProperty<WorkOrder, 'documentDate'>({
        isPublished: true,
        async getValue() {
            return (await this.closingDate) ?? this.startDate;
        },
        lookupAccess: true,
    })
    readonly documentDate: Promise<date>;

    /** items created by the workOrder the first is the releasedItem */
    @decorators.collectionProperty<WorkOrder, 'productionItems'>({
        isPublished: true,
        isVital: true,
        node: () => xtremManufacturing.nodes.WorkOrderReleasedItem,
        reverseReference: 'document',
        isRequired: true,
        dependsOn: ['startDate'],
    })
    readonly productionItems: Collection<xtremManufacturing.nodes.WorkOrderReleasedItem>;

    // needed for computeHeaderStatus
    get lines() {
        return this.productionItems;
    }

    readonly linesWithStockTransactionStatusCollectionName = 'productionItems';

    @decorators.collectionProperty<WorkOrder, 'productionOperations'>({
        isPublished: true,
        isVital: true,
        forceFullSave: true,
        node: () => xtremManufacturing.nodes.WorkOrderOperation,
        reverseReference: 'workOrder',
    })
    readonly productionOperations: Collection<xtremManufacturing.nodes.WorkOrderOperation>;

    @decorators.collectionProperty<WorkOrder, 'productionOperationResources'>({
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrderOperationResource,
        reverseReference: 'workOrder',
    })
    readonly productionOperationResources: Collection<xtremManufacturing.nodes.WorkOrderOperationResource>;

    @decorators.collectionProperty<WorkOrder, 'productionComponents'>({
        isPublished: true,
        isVital: true,
        forceFullSave: true,
        dependsOn: [{ productionOperations: ['status'] }],
        node: () => xtremManufacturing.nodes.WorkOrderComponent,
        reverseReference: 'workOrder',
    })
    readonly productionComponents: Collection<xtremManufacturing.nodes.WorkOrderComponent>;

    readonly allocableLinesCollectionName = 'productionComponents';

    @decorators.referenceProperty<WorkOrder, 'productionItem'>({
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrderReleasedItem,
        getValue() {
            // Note: At least one item exists because it is required by the collection property definition of
            //       productionItems (required: true). So we can safely assume that takeOne() does not return null.
            //       That's why we override the type from Reference<WorkOrderReleasedItem | null> to
            //       Reference<WorkOrderReleasedItem>.
            return this.productionItems.takeOne(
                item => item._id != null,
            ) as Reference<xtremManufacturing.nodes.WorkOrderReleasedItem>;
        },
        lookupAccess: true,
    })
    readonly productionItem: Reference<xtremManufacturing.nodes.WorkOrderReleasedItem>;

    @decorators.referenceProperty<WorkOrder, 'productionStepOperation'>({
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrderOperation,
        isNullable: true,
        lookupAccess: true,
        async computeValue() {
            const productionSteps = await this.productionOperations
                ?.filter(
                    async workOrderOperation =>
                        (await workOrderOperation.isProductionStep) && (await workOrderOperation.status) !== 'excluded',
                )
                .toArray();
            return productionSteps?.length > 0 ? productionSteps[0] : null;
        },
    })
    readonly productionStepOperation: Reference<xtremManufacturing.nodes.WorkOrderOperation | null>;

    @decorators.collectionProperty<WorkOrder, 'materialTrackings'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremManufacturing.nodes.MaterialTracking,
        getFilter() {
            return { workOrder: { _id: this._id } };
        },
    })
    readonly materialTrackings: Collection<xtremManufacturing.nodes.MaterialTracking>;

    @decorators.collectionProperty<WorkOrder, 'productionTrackings'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremManufacturing.nodes.ProductionTracking,
        getFilter() {
            return { workOrder: { _id: this._id } };
        },
    })
    readonly productionTrackings: Collection<xtremManufacturing.nodes.ProductionTracking>;

    @decorators.collectionProperty<WorkOrder, 'timeTrackings'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremManufacturing.nodes.OperationTracking,
        getFilter() {
            return { workOrder: { _id: this._id } };
        },
    })
    readonly timeTrackings: Collection<xtremManufacturing.nodes.OperationTracking>;

    @decorators.enumProperty<WorkOrder, 'timeUnit'>({
        isStored: true,
        lookupAccess: true,
        isPublished: true,
        dataType: () => xtremTechnicalData.enums.timeUnitDataType,
        defaultValue: 'hours',
    })
    readonly timeUnit: Promise<xtremTechnicalData.enums.TimeUnit>;

    @decorators.enumProperty<WorkOrder, 'allocationStatus'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        dependsOn: [{ productionComponents: ['allocationStatus'] }],
        computeValue() {
            return xtremStockData.functions.allocationLib.computeHeaderAllocationStatus(
                this,
                xtremManufacturing.functions.workOrderLib.isComponentConsideredForAllocationStatus,
            );
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    @decorators.enumProperty<WorkOrder, 'allocationRequestStatus'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremStockData.enums.allocationRequestStatusDataType,
        dependsOn: [{ productionComponents: ['allocationRequestStatus'] }],
        computeValue() {
            return xtremStockData.functions.automaticAllocationLib.computeHeaderAllocationRequestStatus(this);
        },
    })
    readonly allocationRequestStatus: Promise<xtremStockData.enums.AllocationRequestStatus>;

    @decorators.booleanProperty<WorkOrder, 'isServiceOptionsSerialNumberActive'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.serialNumberOption],
        isPublished: true,
        lookupAccess: true,
        computeValue() {
            return this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption);
        },
    })
    readonly isServiceOptionsSerialNumberActive: Promise<boolean>;

    @decorators.collectionProperty<WorkOrder, 'workOrderSerialNumbers'>({
        isPublished: true,
        isVital: true,
        node: () => xtremManufacturing.nodes.WorkOrderSerialNumber,
        reverseReference: 'workOrder',
    })
    readonly workOrderSerialNumbers: Collection<xtremManufacturing.nodes.WorkOrderSerialNumber>;

    // property needed for the finance integration
    @decorators.enumProperty<WorkOrder, 'financeIntegrationStatus'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
        async computeValue() {
            return xtremFinanceData.functions.getDocumentIntegrationStatus(
                this.$.context,
                'workInProgress',
                await this.number,
            );
        },
    })
    readonly financeIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.textStreamProperty<WorkOrder, 'note'>({
        isPublished: true,
        isStored: true,
        dataSensitivityLevel: 'businessSensitive',
        anonymizeMethod: 'fixed',
        anonymizeValue: TextStream.fromString('*'.repeat(15)),
        dataType: () => xtremMasterData.dataTypes.note,
        async isFrozen() {
            return (await this.status) === 'closed';
        },
        duplicatedValue() {
            return TextStream.empty;
        },
    })
    readonly note: Promise<TextStream>;

    @decorators.collectionProperty<WorkOrder, 'postingDetails'>({
        isPublished: true,
        join: {
            documentSysId() {
                return this._id;
            },
            documentType() {
                return 'workInProgress';
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    @decorators.bulkMutation<typeof WorkOrder, 'printBulk'>({
        isPublished: true,
        startsReadOnly: true,
        queue: 'reporting',
        async onComplete(context, reports) {
            const reportName = context.localize(
                '@sage/xtrem-manufacturing/node_work_order_bulk_print_report_name',
                'Work order',
            );
            await xtremMasterData.functions.bulkPrintOnCompleteHandler(context, {
                documentType: 'workOrder',
                documents: reports,
                reportName,
            });
        },
    })
    static printBulk(context: Context, document: WorkOrder) {
        return xtremReporting.nodes.Report.generateReportPdf(context, 'sageJobTraveler', '', {
            variables: JSON.stringify({ order: document._id }),
            isBulk: true,
        });
    }

    @decorators.bulkMutation<typeof WorkOrder, 'bulkPrintPickList'>({
        isPublished: true,
        startsReadOnly: true,
        queue: 'reporting',
        async onComplete(context, reports) {
            await xtremMasterData.functions.bulkPrintOnCompleteHandler(context, {
                documentType: 'workOrderPickList',
                documents: reports,
                reportName: context.localize(
                    '@sage/xtrem-manufacturing/node_work_order_bulk_print_pick_list_report_name',
                    'Work order pick list',
                ),
            });
        },
    })
    static bulkPrintPickList(context: Context, document: WorkOrder) {
        return xtremReporting.nodes.Report.generateReportPdf(context, 'workOrderPickList', '', {
            variables: JSON.stringify({ order: document._id }),
            isBulk: true,
        });
    }

    @decorators.mutation<typeof WorkOrder, 'createWorkOrder'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    siteId: { type: 'string', isMandatory: true },
                    releasedItem: { type: 'string', isMandatory: true },
                    releasedQuantity: { type: 'decimal', isMandatory: true },
                    name: { type: 'string', isMandatory: true },
                    type: {
                        type: 'enum',
                        dataType: () => xtremManufacturing.enums.workOrderTypeDataType,
                        isMandatory: true,
                    },
                    workOrderCategory: {
                        type: 'reference',
                        isMandatory: true,
                        node: () => xtremManufacturing.nodes.WorkOrderCategory,
                    },
                    workOrderNumber: { type: 'string', isMandatory: false },
                    startDate: { type: 'date', isMandatory: false },
                    bom: { type: 'string', isMandatory: false },
                    route: { type: 'string', isMandatory: false },
                    storedDimensions: { isNullable: true, type: 'string' },
                    storedAttributes: { isNullable: true, type: 'string' },
                    _customData: { isNullable: true, type: 'string' },
                },
                isMandatory: true,
            },
        ],
        return: { type: 'reference', node: () => xtremManufacturing.nodes.WorkOrder },
    })
    static createWorkOrder(
        context: Context,
        data: {
            siteId: string;
            releasedItem: string;
            releasedQuantity: decimal;
            name: string;
            type: xtremManufacturing.enums.WorkOrderType;
            workOrderCategory: xtremManufacturing.nodes.WorkOrderCategory;
            workOrderNumber: string;
            startDate?: date;
            bom?: string;
            route?: string;
            storedDimensions?: object;
            storedAttributes?: xtremMasterData.interfaces.StoredAttributes;
            _customData?: object;
        },
    ): Promise<xtremManufacturing.nodes.WorkOrder> {
        return xtremManufacturing.functions.createWorkOrder(context, data);
    }

    @decorators.mutation<typeof WorkOrder, 'scheduleWorkOrder'>({
        isPublished: true,
        parameters: [
            {
                name: 'workOrder',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => xtremManufacturing.nodes.WorkOrder,
            },
        ],
        return: {
            type: 'reference',
            node: () => xtremManufacturing.nodes.WorkOrder,
        },
    })
    static scheduleWorkOrder(
        context: Context,
        workOrder: xtremManufacturing.nodes.WorkOrder,
    ): Promise<xtremManufacturing.nodes.WorkOrder> {
        return xtremManufacturing.functions.scheduleWorkOrder(context, workOrder);
    }

    @decorators.mutation<typeof WorkOrder, 'skipSchedulingWorkOrder'>({
        isPublished: true,
        parameters: [
            {
                name: 'workOrder',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => xtremManufacturing.nodes.WorkOrder,
            },
            {
                name: 'skip',
                type: 'boolean',
                isMandatory: false,
            },
        ],
        return: {
            type: 'reference',
            node: () => xtremManufacturing.nodes.WorkOrder,
        },
    })
    static skipSchedulingWorkOrder(
        context: Context,
        workOrder: xtremManufacturing.nodes.WorkOrder,
        skip = true,
    ): Promise<xtremManufacturing.nodes.WorkOrder> {
        return xtremManufacturing.functions.skipSchedulingWorkOrder(context, workOrder, skip);
    }

    @decorators.mutation<typeof WorkOrder, 'updatePlannedCosts'>({
        isPublished: true,
        parameters: [
            {
                name: 'workOrder',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => xtremManufacturing.nodes.WorkOrder,
            },
        ],
        return: { type: 'reference', node: () => xtremManufacturing.nodes.WorkOrder },
    })
    static async updatePlannedCosts(
        _context: Context,
        workOrder: xtremManufacturing.nodes.WorkOrder,
    ): Promise<xtremManufacturing.nodes.WorkOrder> {
        // TODO: make sure the cost are splitted when the time comes to have multiple released-items
        await xtremManufacturing.functions.workOrderLib.updateReleasedItemPlannedCosts(workOrder, {
            shouldSaveChanges: true,
            canEventLinkRoutingOrBOM: true,
            hasLinkedBOM: true,
            hasLinkedRouting: true,
        });

        return workOrder;
    }

    @decorators.notificationListener<typeof WorkOrder>({
        topic: 'WorkOrder/close',
        startsReadOnly: true,
    })
    static async setWorkOrderClosed(readOnlyContext: Context, payload: { _id: number; trackingId: string }) {
        const stockPosting = await WorkOrder.postToStock(readOnlyContext, [payload._id]);
        (readOnlyContext as any)._contextValues.notificationId = payload.trackingId;

        if (stockPosting.length) {
            await readOnlyContext.batch.logMessage('warning', `${stockPosting}`);
            return stockPosting;
        }

        await readOnlyContext.runInWritableContext(async context => {
            await readOnlyContext.batch.logMessage('warning', `${stockPosting}`);

            const workOrderToUpdate = await context.read(WorkOrder, { _id: payload._id }, { forUpdate: true });
            const number = await workOrderToUpdate.number;

            const workOrderClosing = context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__work_order_closing',
                'Closing work order {{number}}',
                { number },
            );
            await readOnlyContext.batch.logMessage('info', workOrderClosing);

            await workOrderToUpdate.$.set({ status: 'closed' });
            await workOrderToUpdate.deleteUnusedPreGenerateSerialNumbers();
            await workOrderToUpdate.$.save();

            const workOrderClosed = context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__work_order_closed',
                'Work order {{number}} closed',
                { number },
            );
            await readOnlyContext.batch.logMessage('info', workOrderClosed);
            // send notification in order to create staging table entries for the accounting engine
            await workOrderToUpdate.createAccountingNotification();
        });
        return '';
    }

    @decorators.bulkMutation<typeof WorkOrder, 'closeWorkOrder'>({
        isPublished: true,
        parameters: [{ name: 'closingDate', type: 'date', isMandatory: true }],
        async onComplete(context, workOrders) {
            const workOrderCompleted = context.localize(
                '@sage/xtrem-manufacturing/nodes__work_order__work_orders_completed',
                'Work orders completed',
            );
            const numbers = workOrders.map(wo => wo.number).join(', ');
            const filter = btoa(
                JSON.stringify({ _filter: JSON.stringify({ _id: { _in: workOrders.map(wo => wo._id) } }) }),
            );
            await context.batch.logMessage('info', `workOrderCompleted : ${numbers}`);

            await context.notifyUser({
                title: workOrderCompleted,
                icon: 'close',
                description: numbers,
                level: 'success',
                shouldDisplayToast: true,
                actions: [
                    {
                        link: context.batch.notificationStateLink,
                        title: 'History',
                        icon: 'link',
                        style: 'tertiary',
                    },
                    {
                        // TODO use linkToFilteredMainList once it is merged
                        // ( https://github.com/Sage-ERP-X3/xtrem/pull/19101 )
                        link: `@sage/xtrem-manufacturing/WorkOrder/${filter}`,
                        title: 'Work orders',
                        icon: 'link',
                        style: 'tertiary',
                    },
                ],
            });
        },
    })
    static async closeWorkOrder(context: Context, workOrder: WorkOrder, closingDate: date) {
        await beforeCloseWorkOrder(context, workOrder, closingDate);

        if (!(await workOrder.productionItem)) {
            throw new LogicError(`The WO ${workOrder._id} has no productionItem`);
        }

        if ((await workOrder.status) !== 'completed') {
            await workOrder.setCompletedProductionItem();
            await workOrder.setCompletedProductionOperationsAndResources();
            await workOrder.setCompletedProductionComponents();
        }

        await xtremManufacturing.functions.workOrderLib.updateWipCost(context, workOrder);

        await workOrder.$.set({ closingDate, status: 'completed' });
        await workOrder.$.save();

        await context.notify('WorkOrder/close', { _id: workOrder._id, trackingId: context.batch.trackingId });

        return { number: await workOrder.number, _id: workOrder._id };
    }

    async setCompletedProductionItem() {
        await this.productionItems.forEach(item => item.$.set({ lineStatus: 'completed' }));
    }

    async setCompletedStockTransactionStatus() {
        await this.productionItems.forEach(item => item.$.set({ stockTransactionStatus: 'completed' }));
    }

    async setCompletedProductionOperationsAndResources() {
        await this.productionOperations
            .filter(async operation => ['pending', 'inProgress'].includes(await operation.status))
            .forEach(async operation => {
                await operation.$.set({ status: 'completed' });
                await operation.resources
                    .filter(async resource => ['pending', 'inProgress'].includes(await resource.status))
                    .forEach(resource => resource.$.set({ status: 'completed' }));
            });
    }

    async setCompletedProductionComponents() {
        await this.productionComponents
            .filter(async component => ['pending', 'inProgress'].includes(await component.lineStatus))
            .forEach(component => component.$.set({ lineStatus: 'completed' }));
    }

    @decorators.query<typeof WorkOrder, 'getProductionCost'>({
        isPublished: true,
        parameters: [
            {
                name: 'releasedItem',
                type: 'reference',
                node: () => xtremManufacturing.nodes.WorkOrderReleasedItem,
                isMandatory: true,
            },
        ],
        return: {
            name: 'productionCost',
            type: 'object',
            properties: { quantityProduced: { type: 'decimal' }, amount: { type: 'decimal' } },
        },
    })
    static async getProductionCost(
        context: Context,
        releasedItem: xtremManufacturing.nodes.WorkOrderReleasedItem,
    ): Promise<{ quantityProduced: decimal; amount: decimal }> {
        // For the moment, as there is only 1 released item per WO,
        // the cost is only the sum of all WorkInProgressCost of the WO
        const amount =
            (
                await context
                    .queryAggregate(xtremManufacturing.nodes.WorkInProgressCost, {
                        filter: {
                            _and: [
                                {
                                    workOrder: { _id: (await releasedItem.document)._id },
                                    type: {
                                        _in: [
                                            'materialTracking',
                                            'setupTimeTracking',
                                            'runTimeTracking',
                                            'workOrderIndirectCost',
                                        ],
                                    },
                                },
                            ],
                        },
                        group: {},
                        values: { amount: { sum: true } },
                    })
                    .toArray()
            )[0]?.values.amount.sum || 0;
        const result = { quantityProduced: await releasedItem.totalActualQuantity, amount };
        logger.verbose(
            () =>
                `getProductionCost for WorkOrderReleasedItem._id=${releasedItem._id} --> ${JSON.stringify({ result })}`,
        );
        return result;
    }

    /* ********************************
    Notification for the allocation engine
    ******************************** */
    @decorators.mutation<typeof WorkOrder, 'requestAutoAllocation'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    document: {
                        type: 'reference',
                        isWritable: true,
                        node: () => xtremManufacturing.nodes.WorkOrder,
                    },
                    requestType: 'string',
                },
            },
        ],
        return: { type: 'string' },
    })
    static async requestAutoAllocation(
        context: Context,
        data: {
            document: Reference<xtremManufacturing.nodes.WorkOrder>;
            requestType: xtremStockData.enums.AllocationRequestType;
        },
    ): Promise<string> {
        return xtremStockData.functions.notificationLib.requestAutoStockAllocation(
            context,
            {
                documentType: 'workOrder',
                processType: 'document',
                requestType: data.requestType,
                document: await data.document,
            },
            xtremManufacturing.functions.workOrderLib.isComponentAllocable,
        );
    }

    @decorators.notificationListener<typeof WorkOrder>({
        startsReadOnly: true,
        topic: 'WorkOrder/allocation/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<xtremStockData.interfaces.AllocationReplyPayload>,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandlerForAutomaticAllocation(
                context,
                envelope,
                error,
                WorkOrder,
            );
        },
    })
    static async onAllocationReply(
        context: Context,
        payload: xtremStockData.interfaces.AllocationReplyPayload,
    ): Promise<void> {
        await xtremStockData.functions.notificationLib.reactToAutomaticAllocationReply(
            context,
            payload,
            WorkOrder,
            xtremManufacturing.functions.workOrderLib.isComponentAllocable,
        );
    }

    static readonly _getLinesToCorrect: xtremStockData.interfaces.FunctionToGetLinesToCorrect = async (
        context: Context,
        line: xtremManufacturing.nodes.WorkOrderReleasedItem,
    ) => {
        const workOrderProductionCost = await WorkOrder.getProductionCost(context, line);
        const trackings = context.query(xtremManufacturing.nodes.ProductionTrackingLine, {
            filter: { workOrderLine: line._id },
        });
        logger.verbose(
            () =>
                `WorkOrderReleasedItem._id=${line._id} --> workOrderProductionCost=${JSON.stringify({
                    workOrderProductionCost,
                })}`,
        );
        // all production tracking lines will have the same unit production cost
        return {
            linesToCorrect: await trackings
                .map(async trackingLine => {
                    const previousAmount =
                        (
                            await context
                                .queryAggregate(xtremStockData.nodes.StockJournal, {
                                    filter: {
                                        stockDetail: {
                                            _in: await trackingLine.stockDetails
                                                .map(stockDetail => stockDetail._id)
                                                .toArray(),
                                        },
                                    },
                                    group: {},
                                    values: { orderAmount: { sum: true } },
                                })
                                .elementAt(0)
                        ).values.orderAmount.sum || 0;

                    logger.verbose(
                        () => `ProductionTrackingLine._id=${trackingLine._id} --> previousAmount=${previousAmount})}`,
                    );

                    return {
                        correctedDocumentLine: trackingLine,
                        impactedQuantity: await trackingLine.quantityInStockUnit,
                        amountToAbsorb:
                            workOrderProductionCost.quantityProduced > 0
                                ? ((await trackingLine.quantityInStockUnit) * workOrderProductionCost.amount) /
                                      workOrderProductionCost.quantityProduced -
                                  previousAmount
                                : 0,
                    };
                })
                .toArray(),
        };
    };

    /* ********************************
    Notification for the stock engine
    ******************************** */
    static postToStock(context: Context, workOrderIDs: integer[]): Promise<string> {
        return xtremStockData.functions.stockValuationLib.postToStockReceiptCostValueCorrection(context, {
            documentIds: workOrderIDs,
            correctorDocumentNode: WorkOrder,
            correctedDocumentNode: xtremManufacturing.nodes.ProductionTracking,
            functionToManageLinesWithoutStockUpdate: null,
            functionToGetLinesToCorrect: WorkOrder._getLinesToCorrect,
            replyTopic: 'WorkOrder/stock/correction/reply',
        });
    }

    @decorators.notificationListener<typeof WorkOrder>({
        startsReadOnly: true,
        topic: 'WorkOrder/stock/correction/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.correction>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(context, envelope, error, WorkOrder);
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.correction>,
    ): Promise<void> {
        // as the reply concerns production tracking we need to adapt the reply payload to the work order
        const correctionPayload = (
            await xtremStockData.functions.notificationLib.stockCorrectionReplyToOrigin(readOnlyContext, payload, [
                {
                    lineClass: xtremManufacturing.nodes.WorkOrderReleasedItem,
                    headerClass: xtremManufacturing.nodes.WorkOrder,
                },
            ])
        )[0];
        if (!correctionPayload) return;

        await WorkOrder.manageStockReply(readOnlyContext, correctionPayload, payload);
    }

    static async manageStockReply(
        readOnlyContext: Context,
        correctionPayload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.correction>,
        stockReplyPayload?: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.correction>,
    ) {
        const correctionUpdateResult = await readOnlyContext.runInWritableContext(async writableContext => {
            if (stockReplyPayload) {
                await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                    writableContext,
                    stockReplyPayload,
                    xtremManufacturing.nodes.ProductionTracking,
                );
            }
            return (
                await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                    writableContext,
                    correctionPayload,
                    WorkOrder,
                )
            ).correction;
        });
        if (!correctionUpdateResult) return;

        if (!correctionUpdateResult.transactionHadAnError) {
            await correctionUpdateResult.documents.forEach(async document => {
                await readOnlyContext.runInWritableContext(async writableContext => {
                    const workOrder = await xtremMasterData.functions.getWritableNode(
                        writableContext,
                        WorkOrder,
                        document.id,
                    );
                    if (document.isStockDocumentCompleted) {
                        await WorkOrder.onceStockCompleted(writableContext, workOrder);
                    } else if (await workOrder.closingDate) {
                        // XT-35758 As the closing date was set outside before the posting we have to reset it to null.
                        await workOrder.$.set({ closingDate: null });
                        await workOrder.$.save();
                    }

                    // Update the bill of material tracking
                    await xtremManufacturing.functions.updateBomTrackingStatuses(writableContext, {
                        number: await workOrder.number,
                        stockStatus: await workOrder.stockTransactionStatus,
                        workOrderStatus: await workOrder.status,
                    });
                });
            });
        }
    }

    @decorators.mutation<typeof WorkOrder, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [{ name: 'workOrder', type: 'reference', isMandatory: true, node: () => WorkOrder }],
        return: { type: 'boolean' },
    })
    static async resendNotificationForFinance(context: Context, workOrder: WorkOrder): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-manufacturing/node__work_order__resend_notification_for_finance',
                'Resending finance notification for work order: {{workOrder}}.',
                { workOrderNumber: await workOrder.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await workOrder.number,
                documentType: 'workInProgress',
                sourceDocumentNumber: await workOrder.number,
                sourceDocumentType: 'workOrderClose',
            })
        ) {
            await workOrder.createAccountingNotification();
        }

        return true;
    }

    @decorators.notificationListener<typeof WorkOrder>({
        topic: 'WorkOrder/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const workOrder = await context.read(WorkOrder, { number: document.number });

        await WorkOrder.resendNotificationForFinance(context, workOrder);
    }

    // waiting for finance notification
    static async onceStockCompleted(writableContext: Context, workOrder: WorkOrder): Promise<void> {
        await xtremManufacturing.functions.workOrderLib.updateVariances(writableContext, workOrder);

        // send notification in order to create staging table entries for the accounting engine
        await workOrder.createAccountingNotification();

        await workOrder.$.set({ status: 'closed' });
        await workOrder.$.save();
    }

    /**
     * Send notification in order to create staging table entries for the accounting engine
     * The workOrder and the previously created workInProgressCost record are sent to finance
     * @param context
     * @param work order for which the notification has to be sent
     */
    async createAccountingNotification(): Promise<void> {
        if (await (await (await this?.site)?.legalCompany)?.doWipPosting) {
            // read previously created WorkInProgressCost record of type
            // 'workOrderVariance', 'workOrderNegativeVariance', 'workOrderActualCostAdjustment' or 'workOrderNegativeActualCostAdjustment'
            // Only 1 record exists, type depending on the sign of the variance
            const wipCost = await this.$.context
                .query(xtremManufacturing.nodes.WorkInProgressCost, {
                    filter: {
                        workOrder: this._id,
                        type: {
                            _in: xtremManufacturing.nodes.WorkInProgressCost.workInProgressVarianceTypes.concat(
                                xtremManufacturing.nodes.WorkInProgressCost.workInProgressNonAbsorbedVarianceTypes,
                            ),
                        },
                    },
                })
                .toArray();

            // send notification only if a wipCost record was found and if the amount not zero
            if (wipCost.length > 0 && (await wipCost[0].amount) !== 0) {
                await xtremFinanceData.functions.ManufacturingNotificationLib.workOrderClosingNotification(
                    this.$.context,
                    this as xtremFinanceData.interfaces.FinanceOriginDocument,
                    wipCost as xtremFinanceData.interfaces.WorkOrderClosingFinanceDocumentLine[],
                );
            }
        }
    }

    @decorators.notificationListener<typeof WorkOrder>({
        topic: 'WorkOrderClosing/accountingInterface',
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }

    @decorators.mutation<typeof WorkOrder, 'addComponentsToWorkOrder'>({
        isPublished: true,
        parameters: [
            {
                name: 'workOrder',
                type: 'reference',
                node: () => xtremManufacturing.nodes.WorkOrder,
                isMandatory: true,
                isWritable: true,
            },
            {
                name: 'item',
                type: 'reference',
                node: () => xtremMasterData.nodes.Item,
                isMandatory: true,
            },
            {
                name: 'site',
                type: 'reference',
                node: () => xtremSystem.nodes.Site,
                isMandatory: true,
            },
        ],
        return: { type: 'reference', node: () => xtremManufacturing.nodes.WorkOrder },
    })
    static async addComponentsToWorkOrder(
        context: Context,
        workOrder: xtremManufacturing.nodes.WorkOrder,
        item: xtremMasterData.nodes.Item,
        site: xtremSystem.nodes.Site,
    ): Promise<xtremManufacturing.nodes.WorkOrder> {
        const billOfMaterial = await context.tryRead(xtremTechnicalData.nodes.BillOfMaterial, {
            _id: `#${await item.id}|${await site.id}`,
        });

        const bomRevision = billOfMaterial
            ? await xtremTechnicalData.functions.bomRevisionLib.getRevisionByDate(
                  billOfMaterial,
                  await workOrder.startDate,
              )
            : null;

        if (!billOfMaterial || (!bomRevision && (await billOfMaterial.status) !== 'availableToUse')) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-manufacturing/pages__work_order_panel__functions__bill_of_material_not_found',
                    'No bill of material available to use found.',
                ),
            );
        }

        await xtremManufacturing.functions.addComponentsToWorkOrder(context, workOrder, billOfMaterial, bomRevision);

        await workOrder.$.set({
            bomCode: billOfMaterial,
            bomRevision,
            baseQuantity: await billOfMaterial.baseQuantity,
        });

        // TODO: make sure the cost are splitted when the time comes to have multiple released-items
        await xtremManufacturing.functions.workOrderLib.updateReleasedItemPlannedCosts(workOrder, {
            canEventLinkRoutingOrBOM: true,
            hasLinkedBOM: true,
        });

        await workOrder.$.save();

        return workOrder;
    }

    @decorators.mutation<typeof WorkOrder, 'addOperationsToWorkOrder'>({
        isPublished: true,
        parameters: [
            {
                name: 'workOrder',
                type: 'reference',
                node: () => xtremManufacturing.nodes.WorkOrder,
                isMandatory: true,
                isWritable: true,
            },
            {
                name: 'item',
                type: 'reference',
                node: () => xtremMasterData.nodes.Item,
                isMandatory: true,
            },
            {
                name: 'site',
                type: 'reference',
                node: () => xtremSystem.nodes.Site,
                isMandatory: true,
            },
        ],
        return: { type: 'reference', node: () => xtremManufacturing.nodes.WorkOrder },
    })
    static async addOperationsToWorkOrder(
        context: Context,
        workOrder: xtremManufacturing.nodes.WorkOrder,
        item: xtremMasterData.nodes.Item,
        site: xtremSystem.nodes.Site,
    ): Promise<xtremManufacturing.nodes.WorkOrder> {
        const routing = await context
            .query(xtremTechnicalData.nodes.Routing, {
                filter: {
                    site: site._id,
                    item: item._id,
                    status: { _eq: 'availableToUse' },
                },
            })
            .at(0);
        if (!routing) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-manufacturing/pages__work_order_panel__functions_routing_not_found',
                    'No routing available to use found.',
                ),
            );
        }
        await xtremManufacturing.functions.addOperationsToWorkOrder(context, workOrder, routing);
        await workOrder.$.set({
            routingCode: routing,
            baseRoutingQuantity: await routing.batchQuantity,
        });

        // TODO: make sure the cost are splitted when the time comes to have multiple released-items
        await xtremManufacturing.functions.workOrderLib.updateReleasedItemPlannedCosts(workOrder, {
            canEventLinkRoutingOrBOM: true,
            hasLinkedRouting: true,
        });
        await workOrder.$.save();
        return workOrder;
    }

    @decorators.mutation<typeof WorkOrder, 'controlClosingDate'>({
        isPublished: true,
        parameters: [
            { name: 'closingDate', type: 'date', isMandatory: true },
            {
                name: 'workOrder',
                type: 'reference',
                node: () => xtremManufacturing.nodes.WorkOrder,
                isMandatory: false,
            },
        ],
        return: { type: 'string' },
    })
    static controlClosingDate(
        context: Context,
        closingDate: date | null,
        workOrder?: xtremManufacturing.nodes.WorkOrder,
    ): Promise<string> {
        return controlClosingDate(context, {
            ...(workOrder ? { filter: { _id: workOrder._id } } : {}),
            closingDate,
        });
    }

    @decorators.mutation<typeof WorkOrder, 'preGenerateSerialNumbers'>({
        isPublished: true,
        parameters: [
            {
                name: 'releasedItem',
                type: 'reference',
                node: () => xtremMasterData.nodes.Item,
                isMandatory: true,
            },
            { name: 'releasedQuantity', type: 'decimal', isMandatory: true },
            {
                name: 'site',
                type: 'reference',
                node: () => xtremSystem.nodes.Site,
                isMandatory: true,
            },
            {
                name: 'productionItem',
                type: 'reference',
                node: () => xtremManufacturing.nodes.WorkOrderReleasedItem,
                isMandatory: true,
            },
            { name: 'checkOnly', type: 'boolean', isMandatory: true },
        ],
        return: { type: 'string' },
    })
    static async preGenerateSerialNumbers(
        context: Context,
        releasedItem: xtremMasterData.nodes.Item,
        releasedQuantity: number,
        site: xtremSystem.nodes.Site,
        productionItem: xtremManufacturing.nodes.WorkOrderReleasedItem,
        checkOnly: boolean,
    ): Promise<string> {
        const serialNumToCreate =
            releasedQuantity -
            (await context.query(xtremStockData.nodes.SerialNumber, {
                filter: { item: releasedItem, site, baseDocumentLine: productionItem },
            }).length);
        if (serialNumToCreate === 0 || checkOnly) {
            return serialNumToCreate.toString();
        }
        const existingSerials = await context
            .query(xtremStockData.nodes.SerialNumber, {
                filter: { item: releasedItem, site, baseDocumentLine: productionItem },
            })
            .toArray();

        const workOrder = await context.read(
            xtremManufacturing.nodes.WorkOrder,
            { _id: (await productionItem.document)._id },
            { forUpdate: true },
        );

        const serials = await xtremStockData.nodes.SerialNumber.generateSerialNumbers(context, serialNumToCreate, {
            item: releasedItem,
            site,
            baseDocumentLine: productionItem,
            createDate: await workOrder.endDate,
        });

        const allSerials = existingSerials.concat(serials);

        const workOrderSerialNumbers = allSerials.map(serial => {
            return { serialNumber: serial._id };
        });
        await workOrder.$.set({ workOrderSerialNumbers });
        await workOrder.$.save();

        return serials.length.toString();
    }

    @decorators.collectionProperty<WorkOrder, 'serialsNumbers'>({
        node: () => xtremStockData.nodes.SerialNumber,
        async getFilter() {
            const documentLine = await this.productionItems.at(0);
            const item = (await documentLine?.item)?._id;
            const site = (await this.site)._id;
            return { item, site, baseDocumentLine: documentLine?._id };
        },
    })
    serialsNumbers: Collection<xtremStockData.nodes.SerialNumber>;

    async deleteUnusedPreGenerateSerialNumbers() {
        const serialNumToDelete = this.serialsNumbers.filter(async serialNumber => {
            return !(await serialNumber.isUsable);
        });

        await serialNumToDelete.forEach(async serialToDelete => {
            await this.$.context.delete(xtremManufacturing.nodes.WorkOrderSerialNumber, {
                serialNumber: { _id: serialToDelete._id },
            });
            await this.$.context.delete(xtremStockData.nodes.SerialNumber, { _id: serialToDelete._id });
        });
        return serialNumToDelete.length;
    }

    @decorators.asyncMutation<typeof WorkOrder, 'trackBillOfMaterial'>({
        startsReadOnly: true,
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    bomId: { type: 'string', isMandatory: true },
                    siteId: { type: 'string', isMandatory: true },
                    itemId: { type: 'string', isMandatory: true },
                    itemName: { type: 'string', isMandatory: true },
                    quantity: { type: 'decimal', isMandatory: true },
                    workOrderCategoryId: { type: 'string', isMandatory: true },
                    stockDetails: { type: 'string', isMandatory: true },
                    date: { type: 'date', isMandatory: false },
                    route: { type: 'string', isMandatory: false },
                },
                isMandatory: true,
            },
        ],
        return: {
            name: 'BillOfMaterialTracking',
            type: 'reference',
            node: () => xtremTechnicalData.nodes.BillOfMaterialTracking,
        },
    })
    static trackBillOfMaterial(
        context: Context,
        data: {
            bomId: string;
            siteId: string;
            itemId: string;
            itemName: string;
            quantity: decimal;
            workOrderCategoryId: string;
            stockDetails: string;
            date?: date;
            route?: string;
        },
    ): Promise<xtremTechnicalData.nodes.BillOfMaterialTracking | null> {
        return xtremManufacturing.functions.trackBillOfMaterial(context, data);
    }

    @decorators.asyncMutation<typeof WorkOrder, 'createTestWorkOrders'>({
        isPublished: true,
        startsReadOnly: false,
        serviceOptions: () => [xtremSystem.serviceOptions.DevTools],
        parameters: [
            { name: 'orderQuantity', type: 'integer', isMandatory: true },
            {
                name: 'bom',
                type: 'object',
                isMandatory: false,
                properties: {
                    itemId: { type: 'string', isMandatory: true },
                    siteId: { type: 'string', isMandatory: true },
                },
            },
            { name: 'orderNumberRoot', type: 'string', isMandatory: false },
        ],
        return: { type: 'string' },
    })
    static createTestWorkOrders(
        context: Context,
        orderQuantity: integer,
        bom?: { itemId: string; siteId: string },
        orderNumberRoot = '',
    ) {
        return xtremManufacturing.functions.workOrderLib.createTestWorkOrders(
            context,
            orderQuantity,
            bom,
            orderNumberRoot,
        );
    }

    // Repost creates an update notification to the acc engine in order to update attributes and dimenions.
    // In fact, the existing finance document headers are preserved while the lines are all rebuilt (old ones deleted and new ones calculated)
    @decorators.mutation<typeof WorkOrder, 'repost'>({
        isPublished: true,
        parameters: [
            { name: 'workOrder', type: 'reference', isMandatory: true, isWritable: true, node: () => WorkOrder },
            { name: 'financeTransactionSysId', type: 'string', isMandatory: true },
            {
                name: 'components',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
            {
                name: 'releasedItems',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
            {
                name: 'operationResources',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        workOrderOperationSysId: 'integer',
                        resourceSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
        ],
        return: { type: 'object', properties: { wasSuccessful: 'boolean', message: 'string' } },
    })
    static repost(
        context: Context,
        workOrder: WorkOrder,
        financeTransactionSysId: string,
        components: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
        releasedItems: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
        operationResources: xtremManufacturing.interfaces.FinanceOperationResource[],
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        return xtremManufacturing.functions.repost({
            context,
            workOrder,
            financeTransactionSysId,
            components,
            releasedItems,
            operationResources,
        });
    }

    resynchronizeStatus(): Promise<{
        oldStatus: xtremManufacturing.enums.WorkOrderStatus;
        newStatus: xtremManufacturing.enums.WorkOrderStatus;
    }> {
        return xtremManufacturing.functions.workOrderLib.resynchronizeStatus(this.$.context, this);
    }

    @decorators.asyncMutation<typeof WorkOrder, 'resynchronizeStatus'>({
        isPublished: true,
        parameters: [
            {
                name: 'workOrder',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => WorkOrder,
            },
        ],
        return: {
            type: 'object',
            properties: {
                oldStatus: {
                    type: 'enum',
                    dataType: () => xtremManufacturing.enums.workOrderStatusDataType,
                },
                newStatus: {
                    type: 'enum',
                    dataType: () => xtremManufacturing.enums.workOrderStatusDataType,
                },
            },
        },
    })
    static resynchronizeStatus(
        _context: Context,
        workOrder: WorkOrder,
    ): Promise<{
        oldStatus: xtremManufacturing.enums.WorkOrderStatus;
        newStatus: xtremManufacturing.enums.WorkOrderStatus;
    }> {
        return workOrder.resynchronizeStatus();
    }

    /**
     * Checks if the workOrderPickList can be printed
     * @returns true if it can be printed
     * @throws {BusinessRuleError} if cannot be printed
     */
    @decorators.mutation<typeof WorkOrder, 'beforePrintWorkOrderPickList'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'order',
                type: 'reference',
                isMandatory: true,
                node: () => WorkOrder,
            },
        ],
        return: { type: 'boolean' },
    })
    static async beforePrintWorkOrderPickList(context: Context, order: WorkOrder): Promise<boolean> {
        if (!((await order.type) === 'firm' && ['pending', 'inProgress'].includes(await order.status))) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-manufacturing/node_work_order_print_pick_list_error',
                    'This work order needs to be firm and in progress or pending to print the pick list:  {{workOrderNumber}}.',
                    { workOrderNumber: await order.number },
                ),
            );
        }
        return true;
    }
}
