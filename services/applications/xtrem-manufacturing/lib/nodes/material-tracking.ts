import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, decimal, integer, Reference } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, date, decorators, Logger, Node, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import * as _ from 'lodash';
import * as xtremManufacturing from '../../index';
import { allocationLib } from '../functions/allocation-lib';
import { getWorkOrderComponentArray } from '../functions/material-tracking-create';

const logger = Logger.getLogger(__filename, 'material-tracking');

@decorators.node<MaterialTracking>({
    isClearedByReset: true,
    package: 'xtrem-manufacturing',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    isCustomizable: true,
    indexes: [
        {
            orderBy: { number: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    async controlDelete(cx) {
        if (!(await this.canBeUpdated())) {
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize(
                    '@sage/xtrem-manufacturing/nodes__material_tracking__deletion_forbidden',
                    'This material tracking cannot be deleted.',
                ),
            );
        }
    },
    async saveBegin() {
        if (await this.number) {
            logger.warn(() => 'The ID already exists, and no sequence number will be allocated');
            return;
        }
        await this.$.set({
            number: await (
                await xtremMasterData.classes.DocumentNumberGenerator.create(this.$.context, {
                    nodeInstance: this,
                    currentDate: date.today(),
                })
            ).allocate(),
        });
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class MaterialTracking
    extends Node
    implements
        xtremFinanceData.interfaces.FinanceOriginDocument,
        xtremStockData.interfaces.DocumentHeaderWithStockAllocation
{
    // To allow update on repost from work order
    @decorators.booleanProperty<MaterialTracking, 'forceUpdateForFinance'>({
        defaultValue: false,
    })
    readonly forceUpdateForFinance: Promise<boolean>;

    getEffectiveDate(): Promise<date> {
        return this.effectiveDate;
    }

    getStockSite(): Promise<xtremSystem.nodes.Site> {
        return this.site;
    }

    /**
     * Indicates if a property can be updated
     * @param propertyName The property name or nothing for the node itself
     * @returns
     */
    async canBeUpdated(propertyName?: string): Promise<boolean> {
        if (!propertyName) {
            return (await this.stockTransactionStatus) !== 'completed';
        }

        if (['effectiveDate', 'entryDate'].includes(propertyName)) {
            return ['draft'].includes(await this.stockTransactionStatus);
        }

        return false;
    }

    @decorators.stringProperty<MaterialTracking, 'number'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        provides: ['sequenceNumber'],
        lookupAccess: true,
    })
    readonly number: Promise<string>;

    @decorators.referenceProperty<MaterialTracking, 'workOrder'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        lookupAccess: true,
        node: () => xtremManufacturing.nodes.WorkOrder,
        async control(cx) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-manufacturing/nodes__manufacturing_tracking__work_order_type_must_be_firm',
                    'Only firm work orders can be tracked.',
                )
                .if(await (await this.workOrder).type)
                .is.not.equal.to('firm');
        },
    })
    readonly workOrder: Reference<xtremManufacturing.nodes.WorkOrder>;

    @decorators.booleanProperty<MaterialTracking, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    readonly forceUpdateForStock: Promise<boolean>;

    @decorators.referenceProperty<MaterialTracking, 'site'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dependsOn: ['workOrder'],
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
        async defaultValue() {
            return (await this.workOrder).site;
        },
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.dateProperty<MaterialTracking, 'entryDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isNullable: false,
        async isFrozen() {
            return !(await this.canBeUpdated('entryDate'));
        },
        defaultValue() {
            return date.today();
        },
    })
    readonly entryDate: Promise<date>;

    @decorators.dateProperty<MaterialTracking, 'effectiveDate'>({
        isStored: true,
        isPublished: true,
        async isFrozen() {
            return !(await this.canBeUpdated('effectiveDate'));
        },
        dependsOn: ['entryDate'],
        defaultValue() {
            return this.entryDate;
        },
    })
    readonly effectiveDate: Promise<date>;

    // property needed for the accounting interface
    @decorators.referenceProperty<MaterialTracking, 'financialSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['site'],
        async getValue() {
            return xtremFinanceData.functions.getFinancialSite(await this.site);
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    // property needed for the accounting interface
    @decorators.referenceProperty<MaterialTracking, 'transactionCurrency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['financialSite'],
        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    // property needed for the accounting interface
    @decorators.dateProperty<MaterialTracking, 'documentDate'>({
        isPublished: true,
        dependsOn: ['effectiveDate'],
        getValue() {
            return this.effectiveDate;
        },
    })
    readonly documentDate: Promise<date>;

    @decorators.collectionProperty<MaterialTracking, 'lines'>({
        isPublished: true,
        isVital: true,
        isRequired: true,
        node: () => xtremManufacturing.nodes.MaterialTrackingLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<xtremManufacturing.nodes.MaterialTrackingLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    @decorators.enumProperty<MaterialTracking, 'allocationStatus'>({
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        dependsOn: [{ lines: ['allocationStatus'] }],
        computeValue() {
            return xtremStockData.functions.allocationLib.computeHeaderAllocationStatus(this);
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    readonly allocableLinesCollectionName = 'lines';

    @decorators.enumProperty<MaterialTracking, 'stockTransactionStatus'>({
        isPublished: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.mutation<typeof MaterialTracking, 'createMaterialTrackings'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'workOrder',
                type: 'reference',
                node: () => xtremManufacturing.nodes.WorkOrder,
                isMandatory: true,
            },
            { name: 'workOrderTrackingNumber', type: 'string', isMandatory: true },
            { name: 'trackingQuantity', type: 'decimal', isMandatory: true },
            { name: 'trackingDate', type: 'date' },
            { name: 'componentNumber', type: 'integer' },
            { name: 'isAutomated', type: 'boolean' },
            { name: 'isMaterialTrackingCreationAllowedForSerialNumberManagedItems', type: 'boolean' },
        ],
        return: {
            type: 'object',
            properties: {
                materialTracking: {
                    type: 'reference',
                    node: () => xtremManufacturing.nodes.MaterialTracking,
                },
                concatenatedMessages: { type: 'string' },
            },
        },
    })
    static async createMaterialTrackings(
        readOnlyContext: Context,
        workOrder: xtremManufacturing.nodes.WorkOrder,
        workOrderTrackingNumber: string,
        trackingQuantity: decimal,
        trackingDate: date = date.today(),
        componentNumber?: integer,
        isAutomated?: true,
        isMaterialTrackingCreationAllowedForSerialNumberManagedItems?: false,
    ): Promise<{ materialTracking: MaterialTracking | null; concatenatedMessages: string }> {
        await xtremManufacturing.functions.validateMaterialTrackingParameters(
            readOnlyContext,
            workOrder,
            trackingQuantity,
            workOrderTrackingNumber,
            componentNumber,
        );

        const workOrderItem = await workOrder.productionItems.elementAt(0);
        let concatenatedMessages = '';

        const createMaterialTracking = async (writableContext: Context) => {
            let materialTrackingHeader: MaterialTracking | null = null;
            const releasedQuantity =
                (await workOrderItem.releasedQuantity) !== 0 ? await workOrderItem.releasedQuantity : 1;
            if (componentNumber) {
                const workOrderComponent = (await workOrder.productionComponents.find(
                    async component => (await component.componentNumber) === componentNumber,
                ))!;
                let quantity = await workOrderComponent.requiredQuantity;
                if (trackingQuantity !== releasedQuantity) {
                    const scrapFactor = await workOrderComponent.scrapFactor;
                    const componentQuantity = MaterialTracking.getOriginalComponentRequirement(
                        scrapFactor,
                        await workOrderComponent.requiredQuantity,
                    );
                    quantity = xtremTechnicalData.functions.componentFunctions.setRequiredQuantity(
                        trackingQuantity,
                        releasedQuantity,
                        componentQuantity,
                        await workOrderComponent.isFixedLinkQuantity,
                        scrapFactor,
                        await (
                            await workOrderComponent.unit
                        )?.decimalDigits,
                    );
                }
                const { materialTracking, message } = await MaterialTracking.createSingleMaterialTracking(
                    writableContext,
                    {
                        component: workOrderComponent,
                        workOrderTrackingNumber,
                        quantity,
                        date: trackingDate,
                        isCompleted: false,
                        storedAttributes: (await workOrderComponent.storedAttributes)
                            ? JSON.stringify(await workOrderComponent.storedAttributes)
                            : undefined,
                        storedDimensions: (await workOrderComponent.storedDimensions)
                            ? JSON.stringify(await workOrderComponent.storedDimensions)
                            : undefined,
                        isStandaloneLine: false,
                        isAutomated,
                        isMaterialTrackingCreationAllowedForSerialNumberManagedItems,
                    },
                );
                if (materialTracking) {
                    // In case everything went well we got a material tracking and update the material tracking header.
                    materialTrackingHeader = materialTracking;
                }
                if (message.length) {
                    // In case of a returned message we add it to the list.
                    concatenatedMessages += `${message}\n`;
                }
            } else {
                await workOrder.productionComponents
                    .filter(
                        async componentFilter =>
                            (await componentFilter.lineType) === 'normal' &&
                            (await componentFilter.lineStatus) !== 'excluded' &&
                            (await componentFilter.lineStatus) !== 'included',
                    )
                    .forEach(async component => {
                        let materialQuantity = await component.requiredQuantity;
                        logger.debug(
                            () =>
                                `createMaterialTrackings: trackingQuantity:${trackingQuantity} releasedQuantity: ${releasedQuantity}`,
                        );
                        await logger.debugAsync(
                            async () =>
                                `createMaterialTrackings: linkQuantity:${await component.linkQuantity} requiredQuantity: ${await component.requiredQuantity}`,
                        );
                        if (trackingQuantity !== releasedQuantity) {
                            const scrapFactor = await component.scrapFactor;
                            const componentQuantity = MaterialTracking.getOriginalComponentRequirement(
                                scrapFactor,
                                await component.requiredQuantity,
                            );
                            materialQuantity = xtremTechnicalData.functions.componentFunctions.setRequiredQuantity(
                                trackingQuantity,
                                releasedQuantity,
                                componentQuantity,
                                await component.isFixedLinkQuantity,
                                scrapFactor,
                                await (
                                    await component.unit
                                )?.decimalDigits,
                            );
                        }
                        const { materialTracking, message } = await MaterialTracking.createSingleMaterialTracking(
                            writableContext,
                            {
                                component,
                                workOrderTrackingNumber,
                                quantity: materialQuantity,
                                date: trackingDate,
                                isCompleted: false,
                                storedAttributes: (await component.storedAttributes)
                                    ? JSON.stringify(await component.storedAttributes)
                                    : undefined,
                                storedDimensions: (await component.storedDimensions)
                                    ? JSON.stringify(await component.storedDimensions)
                                    : undefined,
                                isStandaloneLine: false,
                                isAutomated,
                                isMaterialTrackingCreationAllowedForSerialNumberManagedItems,
                            },
                        );
                        if (materialTracking) {
                            // In case everything went well we got a material tracking and update the material tracking header.
                            materialTrackingHeader = materialTracking;
                        }
                        if (message.length) {
                            // In case of a returned message we add it to the list.
                            concatenatedMessages += `${message}\n`;
                        }
                    });
            }
            return materialTrackingHeader;
        };

        const newMaterialTrackingHeader = await readOnlyContext.runInWritableContext(createMaterialTracking);
        // Create last material tracking notification for stock & accounting engines
        if (newMaterialTrackingHeader) {
            await MaterialTracking.materialTrackingCreationEnd(readOnlyContext, newMaterialTrackingHeader._id);
        }
        return { materialTracking: newMaterialTrackingHeader, concatenatedMessages };
    }

    /**
     * create a single material tracking or add à line to an existing one
     * @param context
     * @param data for material tracking
     * @returns
     */
    @decorators.mutation<typeof MaterialTracking, 'createSingleMaterialTracking'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                isMandatory: true,
                properties: {
                    component: {
                        type: 'reference',
                        node: () => xtremManufacturing.nodes.WorkOrderComponent,
                    },
                    workOrderTrackingNumber: 'string',
                    quantity: 'decimal',
                    date: 'date',
                    storedDimensions: 'string',
                    storedAttributes: 'string',
                    isCompleted: 'boolean',
                    // TODO: review the usage of standalone. It should be forced to true when the mutation is called
                    isStandaloneLine: 'boolean',
                    isAutomated: 'boolean',
                    isMaterialTrackingCreationAllowedForSerialNumberManagedItem: 'boolean',
                    ...allocationLib.getAllocationUpdatesGraphQLDescriptor<typeof MaterialTracking>(),
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                materialTracking: { type: 'reference', node: () => MaterialTracking },
                message: 'string',
            },
        },
    })
    static async createSingleMaterialTracking(
        context: Context,
        data: xtremManufacturing.interfaces.SingleMaterialTracking,
    ): Promise<{ materialTracking: MaterialTracking | null; message: string }> {
        const automaticStockDetails: Array<xtremStockData.interfaces.StockSearchResult> = [];
        const allocationsToBeTransferred: Array<xtremManufacturing.interfaces.AllocationUpdate> = [];
        const allocationsToBeCreated: Array<xtremManufacturing.interfaces.AllocationUpdate> = [];

        let materialTracking: MaterialTracking | null = null;

        const message = await xtremManufacturing.functions.controlCreateMaterialTracking(context, data);
        if (message.length) {
            return { materialTracking, message };
        }

        const { component } = data;
        const componentItem = await component.item;
        const quantityInStockUnit = parseFloat(data.quantity.toFixed(xtremMasterData.dataTypes.stockQuantity.scale));
        const quantityToAllocate = MaterialTracking.computeNewMaterialTrackingLineQuantityToAllocate({
            quantityInStockUnit,
            userEnteredStockAllocations: data.allocationUpdates || [],
            allocationsToBeCreated,
            allocationsToBeTransferred,
            useUserEntries: true,
        });

        const knownAllocations = await context
            .query(xtremStockData.nodes.StockAllocation, {
                filter: { documentLine: component._id },
            })
            .map(async (allocation: xtremStockData.nodes.StockAllocation) => {
                return {
                    _id: allocation._id,
                    stockRecordID: (await allocation.stockRecord)._id,
                    quantityInStockUnit: await allocation.quantityInStockUnit,
                };
            })
            .toArray();

        if (data.isAutomated && componentItem && quantityToAllocate > 0) {
            automaticStockDetails.push(
                ...(await xtremStockData.functions.stockLib.searchStock(context, {
                    activeQuantityInStockUnit: 0,
                    site: await (await component.workOrder).site,
                    // /!\  an item of a component can be null
                    item: componentItem,
                    knownAllocations,
                })),
            );

            if (context.diagnoses.length) {
                throw new BusinessRuleError(context.diagnoses[0].message);
            }

            // XT-60431 Check if we have enough stock.
            const quantityInStock = automaticStockDetails.reduce(
                (sum, stockDetail) => sum + stockDetail.quantityInStockUnit,
                0,
            );

            if (quantityInStock < quantityInStockUnit) {
                logger.debug(
                    () =>
                        `Not enough stock quantityInStock: ${quantityInStock} quantityInStockUnit: ${quantityInStockUnit}`,
                );
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-manufacturing/nodes__manufacturing_tracking__not_enough_stock',
                        'Not enough stock on Site: {{site}} Item: {{item}}.',
                        {
                            site: await (await (await component.workOrder).site).id,
                            item: await componentItem?.id,
                        },
                    ),
                );
            }
        }

        const createMaterialTracking = async (writableContext: Context) => {
            /** Search for the materialTracking Number  */
            if (data.workOrderTrackingNumber) {
                materialTracking = await writableContext.tryRead(
                    MaterialTracking,
                    { number: data.workOrderTrackingNumber },
                    { forUpdate: true },
                );
            }

            if (!materialTracking) {
                materialTracking = await writableContext.create(MaterialTracking, {
                    number: data.workOrderTrackingNumber,
                    workOrder: await component.workOrder,
                    entryDate: data.date,
                });
            }

            const preparedAllocationData = await MaterialTracking.prepareAllocationForNewMaterialTrackingLine(
                writableContext,
                {
                    component: data.component,
                    quantityInStockUnit,
                    userEnteredAllocationData: data.allocationUpdates,
                    automaticStockDetails,
                },
            );

            allocationsToBeCreated.push(...preparedAllocationData.allocationsToBeCreated);
            allocationsToBeTransferred.push(...preparedAllocationData.allocationsToBeTransferred);

            await materialTracking.lines.append({
                workOrderLine: component,

                completed: data.isCompleted,
                quantityInStockUnit,

                storedAttributes: data.storedAttributes ? JSON.parse(data.storedAttributes) : null,
                storedDimensions: data.storedDimensions ? JSON.parse(data.storedDimensions) : null,
            });

            await materialTracking.$.save();

            if (allocationsToBeCreated.length > 0 || allocationsToBeTransferred.length > 0) {
                await MaterialTracking.allocateStockForNewMaterialTrackingLine(writableContext, {
                    materialTrackingLine: materialTracking.lines.elementAt(-1),
                    allocationsToBeCreated,
                    allocationsToBeTransferred,
                });
            }

            return materialTracking;
        };

        if (data.isStandaloneLine) {
            materialTracking = await context.runInWritableContext(createMaterialTracking);
            await MaterialTracking.materialTrackingCreationEnd(context, materialTracking._id);
        } else {
            materialTracking = await createMaterialTracking(context);
        }

        return { materialTracking, message: '' };
    }

    @decorators.mutation<typeof MaterialTracking, 'createMultipleMaterialTrackings'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'data',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        /** WorkOrder of the tracking  */
                        workOrder: {
                            type: 'reference',
                            node: () => xtremManufacturing.nodes.WorkOrder,
                        },
                        componentNumber: {
                            isNullable: true,
                            type: 'integer',
                        },
                        item: {
                            type: 'reference',
                            node: () => xtremMasterData.nodes.Item,
                        },
                        /** Tracking date */
                        date: 'date',
                        remainingQuantity: 'decimal',
                        storedDimensions: {
                            isNullable: true,
                            type: 'string',
                        },
                        storedAttributes: {
                            isNullable: true,
                            type: 'string',
                        },
                        isCompleted: 'boolean',
                        isAutomated: 'boolean',
                        ...allocationLib.getAllocationUpdatesGraphQLDescriptor<typeof MaterialTracking>(),
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                numberTrackings: { type: 'integer' },
                concatenatedMessages: { type: 'string' },
            },
        },
    })
    static async createMultipleMaterialTrackings(
        readOnlyContext: Context,
        data: xtremManufacturing.interfaces.MultipleMaterialTracking[],
    ): Promise<{ numberTrackings: integer; concatenatedMessages: string }> {
        /** Current trackingNumber we want to create only one materialTracking per workOrder */
        let trackingNumber = '';

        let totalNumberOfTrackings = 0;
        let materialTrackingId = 0;
        let concatenatedMessages = '';

        xtremManufacturing.functions.validateMultipleMaterialTrackingParameters(readOnlyContext, data);

        const trackingByWorkOrderNumber = _.groupBy(data, tracking => tracking.workOrder._id);

        await asyncArray(Object.keys(trackingByWorkOrderNumber)).forEach(async workOrderNumber => {
            /** Count number of createSingleMaterialTracking */
            let numberOfTrackings = 0;
            await readOnlyContext.runInWritableContext(async writableContext => {
                /** WorkOrderTracking seq number */
                trackingNumber = await (
                    await xtremMasterData.classes.DocumentNumberGenerator.create(writableContext, {
                        sequenceNumber: 'WorkOrderTracking',
                        currentDate: date.today(),
                        site: await trackingByWorkOrderNumber[workOrderNumber][0].workOrder.site,
                        company: await (
                            await trackingByWorkOrderNumber[workOrderNumber][0].workOrder.site
                        )?.legalCompany,
                    })
                ).allocate();

                await asyncArray(trackingByWorkOrderNumber[workOrderNumber]).forEach(async tracking => {
                    const [workOrderComponent] = await getWorkOrderComponentArray(writableContext, tracking, logger);

                    const { materialTracking, message } =
                        await xtremManufacturing.nodes.MaterialTracking.createSingleMaterialTracking(writableContext, {
                            component: workOrderComponent,
                            workOrderTrackingNumber: trackingNumber,
                            quantity: tracking.remainingQuantity,
                            date: tracking.date,
                            isCompleted: tracking.isCompleted,
                            storedAttributes: tracking.storedAttributes,
                            storedDimensions: tracking.storedDimensions,
                            isStandaloneLine: false,
                            allocationUpdates: tracking.allocationUpdates,
                            isAutomated: tracking.isAutomated,
                        });
                    if (message.length) {
                        concatenatedMessages += `${message}\n`;
                    }
                    if (materialTracking) {
                        materialTrackingId = materialTracking._id;
                        numberOfTrackings += 1;
                    }
                });
            });

            totalNumberOfTrackings += numberOfTrackings;
            // Create last material tracking notification for stock & accounting engines
            if (numberOfTrackings > 0) {
                await MaterialTracking.materialTrackingCreationEnd(readOnlyContext, materialTrackingId);
            }
        });

        return { numberTrackings: totalNumberOfTrackings, concatenatedMessages };
    }

    /**
     * Actions run once a material tracking is created with all lines
     * -> Create notification for stock
     * @param readOnlyContext
     * @param trackingId {MaterialTracking}
     */
    static async materialTrackingCreationEnd(
        readOnlyContext: Context,
        trackingId: MaterialTracking['_id'],
    ): Promise<void> {
        // create notification for stock engine
        await MaterialTracking.postToStock(readOnlyContext, [trackingId]);
    }

    /**
     * Send notification in order to create staging table entries for the accounting engine
     * @param context
     * @param tracking number of tracking or tracking header itself
     */
    async createAccountingNotification(): Promise<void> {
        // technical: materialTrackingHeader can contain null
        const company = await (await this.site).legalCompany;

        if ((await company.doStockPosting) && (await company.doWipPosting) && (await this.lines.toArray())) {
            // loop over the lines and create a notification for each work order
            let lines: xtremFinanceData.interfaces.MaterialTrackingFinanceDocumentLine[] = [];
            let workOrderNumber = '';
            await this.lines.forEach(async line => {
                // if we have a change of work order => send notification and empty the lines array for the next notification
                if (workOrderNumber !== '' && workOrderNumber !== (await line.workInProgressWorkOrderNumber)) {
                    await xtremFinanceData.functions.ManufacturingNotificationLib.materialTrackingNotification(
                        this.$.context,
                        this as xtremFinanceData.interfaces.FinanceOriginDocument,
                        lines,
                    );
                    lines = [];
                }
                workOrderNumber = await line.workInProgressWorkOrderNumber; // remember the work order number
                lines.push(line as xtremFinanceData.interfaces.MaterialTrackingFinanceDocumentLine);
            });
            // send last notification
            if (lines.length > 0) {
                await xtremFinanceData.functions.ManufacturingNotificationLib.materialTrackingNotification(
                    this.$.context,
                    this as xtremFinanceData.interfaces.FinanceOriginDocument,
                    lines,
                );
            }
        }
    }

    @decorators.notificationListener<typeof MaterialTracking>({
        topic: 'MaterialTracking/accountingInterface',
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }

    /**
     * Send notification to perform stock issue of a material tracking
     * @param readOnlyContext
     * @param documentID the _id of the document
     * @returns list of notification IDs
     */
    @decorators.mutation<typeof MaterialTracking, 'postToStock'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async postToStock(readOnlyContext: Context, documentIds: integer[]): Promise<string> {
        await asyncArray(documentIds).forEach(async documentId => {
            const document = await readOnlyContext.read(MaterialTracking, { _id: documentId });

            if ((await (await document.workOrder).status) === 'closed') {
                throw new BusinessRuleError(
                    readOnlyContext.localize(
                        '@sage/xtrem-manufacturing/nodes__material_tracking__cant_create_tracking_when_work_order_is_closed',
                        'The work order is Closed, the material tracking cannot be posted.',
                    ),
                );
            }

            if (!(await document.lines.length)) {
                throw new BusinessRuleError(
                    readOnlyContext.localize(
                        '@sage/xtrem-manufacturing/nodes__material_tracking__no_line_in_the_material_tracking',
                        'There is no line in this material tracking {{trackingNumber}}',
                        { trackingNumber: await document.number },
                    ),
                );
            }

            if ((await document.effectiveDate) > date.today()) {
                throw new BusinessRuleError(
                    readOnlyContext.localize(
                        '@sage/xtrem-manufacturing/nodes__material_tracking__cant_create_tracking_when_effective_date_is_in_the_future',
                        'The effective date is in the future, the material tracking cannot be created.',
                    ),
                );
            }
        });

        return readOnlyContext.runInWritableContext(writableContext =>
            xtremStockData.functions.notificationLib.stockIssueRequestNotification(writableContext, {
                documentClass: MaterialTracking,
                documentIds,
                stockUpdateParameters: {
                    stockDetailData: {
                        isDocumentWithoutStockDetails: true,
                    },
                    allocationData: {
                        isUsingAllocations: true,
                    },
                },
            }),
        );
    }

    /**
     * WO component line is updated (consumed quantity increased, status changed, ...)
     * when tracking is posted (not when it is created)
     */
    @decorators.notificationListener<typeof MaterialTracking>({
        startsReadOnly: true,
        topic: 'MaterialTracking/stock/issue/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.issue>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(context, envelope, error, MaterialTracking);
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.issue>,
    ): Promise<void> {
        const issueUpdateResult = await readOnlyContext.runInWritableContext(
            async writableContext =>
                (
                    await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                        writableContext,
                        payload,
                        MaterialTracking,
                    )
                ).issue,
        );
        if (!issueUpdateResult) return;

        if (!issueUpdateResult.transactionHadAnError) {
            await issueUpdateResult.documents.forEach(async document => {
                await readOnlyContext.runInWritableContext(async writableContext => {
                    const materialTracking = await writableContext.read(xtremManufacturing.nodes.MaterialTracking, {
                        _id: document.instance._id,
                    });

                    await xtremManufacturing.functions.materialTrackingLib.updateAfterStockSuccess(writableContext, {
                        tracking: materialTracking,
                        // As all lines are completed, all lines must have a WIPcost.
                        // In some cases where synchronization has been processed, the WIPcosts records
                        // were not created => here we have to consider them
                        lines: materialTracking.lines.filter(
                            async materialTrackingLine => (await materialTrackingLine.workInProgressCosts.length) === 0,
                        ),
                        isStockDocumentCompleted: document.isStockDocumentCompleted,
                    });
                });
            });
        }
    }

    @decorators.mutation<typeof MaterialTracking, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [{ name: 'materialTracking', type: 'reference', isMandatory: true, node: () => MaterialTracking }],
        return: { type: 'boolean' },
    })
    static async resendNotificationForFinance(context: Context, materialTracking: MaterialTracking): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-manufacturing/node__material_tracking__resend_notification_for_finance',
                'Resending finance notification for material tracking: {{materialTracking}}.',
                { materialTrackingNumber: await materialTracking.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await (await materialTracking.workOrder).number,
                documentType: 'workInProgress',
                sourceDocumentNumber: await materialTracking.number,
                sourceDocumentType: 'materialTracking',
            })
        ) {
            await materialTracking.createAccountingNotification();
        }

        return true;
    }

    @decorators.notificationListener<typeof MaterialTracking>({
        topic: 'MaterialTracking/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const materialTracking = await context.read(MaterialTracking, { number: document.number });

        await MaterialTracking.resendNotificationForFinance(context, materialTracking);
    }

    static async onceStockCompleted(_context: Context, materialTracking: MaterialTracking): Promise<void> {
        // Send notification in order to create staging table entries for the accounting engine
        await materialTracking.createAccountingNotification();
    }

    static computeNewMaterialTrackingLineQuantityToAllocate(args: {
        quantityInStockUnit: decimal;
        userEnteredStockAllocations?: Array<xtremManufacturing.interfaces.AllocationUpdate>;
        allocationsToBeCreated: Array<xtremManufacturing.interfaces.AllocationUpdate>;
        allocationsToBeTransferred: Array<xtremManufacturing.interfaces.AllocationUpdate>;
        useUserEntries?: boolean;
    }) {
        return (
            args.quantityInStockUnit -
            (args?.useUserEntries ? args.userEnteredStockAllocations || [] : args.allocationsToBeCreated).reduce(
                (sum, allocation) => {
                    let quantity = 0;
                    if (allocation.action === 'transfer') {
                        quantity = allocation.quantityToTransfer || 0;
                    } else if (allocation.action !== 'reject') {
                        quantity = allocation.quantity || 0;
                    }
                    return sum + quantity;
                },
                0,
            ) -
            args.allocationsToBeTransferred.reduce((sum, allocation) => sum + (allocation.quantityToTransfer || 0), 0)
        );
    }

    static async rejectAllocation(
        context: Context,
        allocationUpdate: xtremManufacturing.interfaces.AllocationUpdate,
    ): Promise<void> {
        if (!allocationUpdate.allocationRecord) {
            return;
        }

        if (allocationUpdate.quantity === (await (await allocationUpdate.allocationRecord).quantityInStockUnit)) {
            // delete
            await xtremStockData.functions.allocationLib.deleteAllocation(context, {
                allocationRecord: allocationUpdate.allocationRecord as Reference<xtremStockData.nodes.StockAllocation>,
            });
        } else {
            // decrease
            await xtremStockData.functions.allocationLib.decreaseAllocation(context, {
                allocationRecord: allocationUpdate.allocationRecord as Reference<xtremStockData.nodes.StockAllocation>,
                quantity: allocationUpdate.quantity || 0,
                serialNumbers: allocationUpdate.serialNumbers,
            });
        }
    }

    static async prepareAllocationForNewMaterialTrackingLine(
        context: Context,
        allocationData: {
            component: xtremManufacturing.interfaces.SingleMaterialTracking['component'];
            quantityInStockUnit: decimal;
            userEnteredAllocationData?: Array<xtremManufacturing.interfaces.AllocationUpdate>;
            automaticStockDetails: Array<xtremStockData.interfaces.StockSearchResult>;
        },
    ): Promise<
        Omit<Parameters<typeof MaterialTracking.allocateStockForNewMaterialTrackingLine>[1], 'materialTrackingLine'>
    > {
        let allocationsToBeCreated: Array<xtremManufacturing.interfaces.AllocationUpdate> =
            allocationData.userEnteredAllocationData || [];
        const allocationsToBeTransferred: Array<xtremManufacturing.interfaces.AllocationUpdate> = [];
        const allocationsToBeRejected: Array<number> = [];
        const filteredUserEntry: Array<xtremManufacturing.interfaces.AllocationUpdate> = [];

        await asyncArray(allocationData.userEnteredAllocationData || []).forEach(async allocationUpdate => {
            if (allocationUpdate.action === 'reject') {
                if (allocationUpdate.allocationRecord) {
                    allocationsToBeRejected.push((await allocationUpdate.allocationRecord)._id);
                    await MaterialTracking.rejectAllocation(context, allocationUpdate);
                }
            } else {
                filteredUserEntry.push(allocationUpdate);
            }
        });

        let quantityToAllocate = MaterialTracking.computeNewMaterialTrackingLineQuantityToAllocate({
            quantityInStockUnit: allocationData.quantityInStockUnit,
            userEnteredStockAllocations: filteredUserEntry,
            allocationsToBeCreated,
            allocationsToBeTransferred,
        });

        if (quantityToAllocate > 0) {
            await asyncArray(
                await xtremStockData.functions.allocationLib.proposeAllocationsToTransfer(context, {
                    orderDocumentLine: allocationData.component,
                    requiredQuantity: quantityToAllocate,
                    hasAllAllocationsReturned: false,
                }),
            ).forEach(proposition => {
                if (!allocationsToBeRejected.includes(proposition.allocationRecord._id)) {
                    allocationsToBeTransferred.push({
                        ...proposition,
                        action: 'transfer',
                    } as xtremManufacturing.interfaces.AllocationUpdate);
                }
            });
        }

        quantityToAllocate = MaterialTracking.computeNewMaterialTrackingLineQuantityToAllocate({
            quantityInStockUnit: allocationData.quantityInStockUnit,
            userEnteredStockAllocations: filteredUserEntry,
            allocationsToBeCreated,
            allocationsToBeTransferred,
        });

        if (quantityToAllocate > 0) {
            await asyncArray(allocationData.automaticStockDetails).forEach(async detail => {
                if (quantityToAllocate > 0) {
                    const quantityAvailable = (await detail.stockRecord.quantityInStockUnit) || 0;
                    const quantity = quantityToAllocate > quantityAvailable ? quantityAvailable : quantityToAllocate;

                    const stockRecord = await context.read(xtremStockData.nodes.Stock, { _id: detail.stockRecord._id });
                    allocationsToBeCreated.push({
                        action: 'create',
                        stockRecord,
                        quantity,
                        serialNumbers: detail.serialNumbers,
                    });
                }
                quantityToAllocate = MaterialTracking.computeNewMaterialTrackingLineQuantityToAllocate({
                    quantityInStockUnit: allocationData.quantityInStockUnit,
                    userEnteredStockAllocations: filteredUserEntry,
                    allocationsToBeCreated,
                    allocationsToBeTransferred,
                });
            });
        }

        const tempAllocations = [...allocationsToBeCreated];
        allocationsToBeCreated = [];
        tempAllocations.forEach(allocation => {
            if (allocation.action === 'transfer') {
                allocationsToBeTransferred.push(allocation);
            } else if (allocation.action !== 'reject') {
                allocationsToBeCreated.push(allocation);
            }
        });

        return {
            allocationsToBeCreated,
            allocationsToBeTransferred,
        };
    }

    static async allocateStockForNewMaterialTrackingLine(
        context: Context,
        allocationData: {
            materialTrackingLine: Reference<xtremManufacturing.nodes.MaterialTrackingLine>;
            allocationsToBeTransferred: Array<xtremManufacturing.interfaces.AllocationUpdate>;
            allocationsToBeCreated: Array<xtremManufacturing.interfaces.AllocationUpdate>;
        },
    ): Promise<void> {
        await xtremStockData.nodes.Stock.updateAllocations(context, {
            documentLine: allocationData.materialTrackingLine,
            allocationUpdates: [
                ...allocationData.allocationsToBeCreated,
                ...allocationData.allocationsToBeTransferred,
            ] as Array<xtremStockData.interfaces.DataForUpdateAllocationMutationActions>,
        });
    }

    static getOriginalComponentRequirement(scrapFactor: decimal, linkQuantity: decimal): decimal {
        return scrapFactor > 0 ? linkQuantity / (1 + scrapFactor / 100) : linkQuantity;
    }

    /**
     * If the stock reply has been interrupted, the stock has been updated but the WIP cost has not been created.
     * This function will update the work order, create the WIP costs and call the finance notification
     * @returns true if the resynchronization has been successful
     */
    async resynchronizeStatus() {
        await xtremManufacturing.functions.trackingLib.resynchronizeStatus(
            this.$.context,
            this,
            xtremManufacturing.functions.materialTrackingLib.updateAfterStockSuccess,
        );
    }
}
