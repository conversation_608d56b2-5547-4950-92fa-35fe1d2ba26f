import { decorators, Node } from '@sage/xtrem-core';
import { dataTypes } from '@sage/xtrem-system';
import { disableOthersDefault } from '../functions/work-order-category';

@decorators.node<WorkOrderCategory>({
    package: 'xtrem-manufacturing',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    indexes: [
        {
            orderBy: { id: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isSetupNode: true,
    async saveEnd() {
        if (await this.isDefault) {
            await disableOthersDefault(this.$.context, this);
        }
    },
})
export class WorkOrderCategory extends Node {
    @decorators.stringProperty<WorkOrderCategory, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => dataTypes.id,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<WorkOrderCategory, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.localizedDescription,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<WorkOrderCategory, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.localizedName,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly name: Promise<string>;

    @decorators.booleanProperty<WorkOrderCategory, 'routing'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        async control(cx, val) {
            if (val === false && (await this.billOfMaterial) === false) {
                cx.error.addLocalized(
                    '@sage/xtrem-manufacturing/nodes__work_order_category__routing_or_bom_required',
                    'Assign a routing or bill of material to this work order category.',
                );
            }
        },
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly routing: Promise<boolean>;

    @decorators.booleanProperty<WorkOrderCategory, 'billOfMaterial'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly billOfMaterial: Promise<boolean>;

    @decorators.booleanProperty<WorkOrderCategory, 'isDefault'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: false,
        isOwnedByCustomer: true,
    })
    readonly isDefault: Promise<boolean>;
}
