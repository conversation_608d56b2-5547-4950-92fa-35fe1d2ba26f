import type { Collection, Context, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremManufacturing from '../../index';

@decorators.subNode<WorkOrderReleasedItem>({
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    extends: () => xtremMasterData.nodes.BaseDocumentLine,
    indexes: [{ orderBy: { document: 1, releasedItem: 1 }, isUnique: true, isNaturalKey: true }],
    async createEnd() {
        await this.$.set({ workInProgress: {} });
    },
    async saveBegin() {
        if (
            xtremMasterData.functions.nodeIsBeingModified(this) &&
            (await (await this.$.old).lineStatus) === (await this.lineStatus) &&
            (await this.lineStatus) !== 'completed'
        ) {
            if ((await this.totalActualQuantity) > 0) {
                await this.$.set({
                    lineStatus:
                        (await this.totalActualQuantity) >= (await this.releasedQuantity) ? 'completed' : 'inProgress',
                });
            }
        }
    },
    async deleteBegin() {
        await this.checkAndDeleteAssignment();
    },

    async saveEnd() {
        await this.checkAndUpdateQuantityOnAssignment();
    },
    async controlEnd(cx) {
        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class WorkOrderReleasedItem extends xtremMasterData.nodes.BaseDocumentLine {
    getItem() {
        return this.item;
    }

    @decorators.referenceProperty<WorkOrderReleasedItem, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremManufacturing.nodes.WorkOrder,
    })
    override readonly document: Reference<xtremManufacturing.nodes.WorkOrder>;

    @decorators.stringPropertyOverride<WorkOrderReleasedItem, 'documentNumber'>({
        async getValue() {
            return (await this.document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<WorkOrderReleasedItem, 'documentId'>({
        async getValue() {
            return (await this.document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.referenceProperty<WorkOrderReleasedItem, 'releasedItem'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Item,
        lookupAccess: true,
        filters: {
            control: {
                isPhantom: false,
            },
        },
        async control(cx, item) {
            if ((await (await this.document).status) !== 'closed' && !(await (await this.document).closingDate)) {
                if (item && (await item?.status) !== 'active') {
                    cx.error.addLocalized(
                        '@sage/xtrem-manufacturing/nodes__work_order_released_item__header_item_not_active',
                        'You need to remove the inactive items before you change the document status.',
                    );
                }
            }
            await xtremManufacturing.events.control.releasedItemControls.workOrderReleasedItemControl(cx, item);
        },
    })
    readonly releasedItem: Reference<xtremMasterData.nodes.Item>;

    // needed for the finance integration
    @decorators.referenceProperty<WorkOrderReleasedItem, 'item'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Item,
        lookupAccess: true,
        getValue() {
            return this.releasedItem;
        },
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.stringProperty<WorkOrderReleasedItem, 'releasedItemName'>({
        isPublished: true,
        async getValue() {
            return (await this.releasedItem).name;
        },
        lookupAccess: true,
    })
    readonly releasedItemName: Promise<string>;

    @decorators.stringProperty<WorkOrderReleasedItem, 'releasedItemId'>({
        isPublished: true,
        async getValue() {
            return (await this.releasedItem).id;
        },
    })
    readonly releasedItemId: Promise<string>;

    @decorators.referenceProperty<WorkOrderReleasedItem, 'stockUnit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        async getValue() {
            return (await this.releasedItem).stockUnit;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<WorkOrderReleasedItem, 'releasedQuantity'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['totalActualQuantity'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async control(cx, val) {
            if (
                this.$.status === NodeStatus.modified &&
                (await (await this.$.old).releasedQuantity) !== (await this.releasedQuantity) &&
                (await this.totalActualQuantity) > (await this.releasedQuantity)
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-manufacturing/nodes__work_order_released_item__released_quantity_less_than_completed_quantity',
                    'The released quantity is less than the completed quantity.',
                );
            }
            await cx.error.if(val).is.not.greater.than(0);
        },
        duplicateRequiresPrompt: true,
    })
    readonly releasedQuantity: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderReleasedItem, 'quantityInStockUnit'>({
        isPublished: true,
        dependsOn: ['releasedQuantity'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        getValue() {
            return this.releasedQuantity;
        },
    })
    readonly quantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderReleasedItem, 'totalActualQuantity'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async control(cx, val) {
            await cx.error.if(val).is.less.than(0);
        },
        duplicatedValue: 0,
    })
    readonly totalActualQuantity: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderReleasedItem, 'remainingQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async getValue() {
            return Math.max((await this.releasedQuantity) - (await this.totalActualQuantity), 0);
        },
    })
    readonly remainingQuantity: Promise<decimal>;

    @decorators.referenceProperty<WorkOrderReleasedItem, 'workInProgress'>({
        isPublished: true,
        isVital: true,
        isNullable: true,
        reverseReference: 'workOrderReleasedItem',
        node: () => xtremManufacturing.nodes.WorkInProgressWorkOrderReleasedItem,
        // FIXME: move instance create from createEnd here once the platform fixes usage of defaultValue on vital
        // defaultValue() {
        //     // TODO: create entry according to document line criteria
        //     this.$.set({workInProgress: {}});
        // },
    })
    readonly workInProgress: Reference<xtremManufacturing.nodes.WorkInProgressWorkOrderReleasedItem | null>;

    @decorators.enumProperty<WorkOrderReleasedItem, 'lineStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremManufacturing.enums.releasedItemStatusDataType,
        defaultValue: () => 'pending',
        duplicatedValue: useDefaultValue,
    })
    readonly lineStatus: Promise<xtremManufacturing.enums.ReleasedItemStatus>;

    // technical property to allow filter on status
    @decorators.enumProperty<WorkOrderReleasedItem, 'uLineStatus'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremManufacturing.enums.workOrderLineStatusFilteredDataType,
        async getValue() {
            if (['included', 'inProgress', 'pending'].includes(await this.lineStatus)) {
                return (await this.lineStatus) as xtremManufacturing.enums.WorkOrderLineStatusFiltered;
            }
            return null;
        },
    })
    readonly uLineStatus: Promise<xtremManufacturing.enums.WorkOrderLineStatusFiltered | null>;

    // technical property to allow filter on status in tracking (released item, material and time)
    @decorators.enumProperty<WorkOrderReleasedItem, 'uLineStatusForTracking'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremManufacturing.enums.workOrderLineStatusFilteredForTrackingDataType,
        async getValue() {
            if (['included', 'inProgress', 'pending'].includes(await this.lineStatus)) {
                return (await this.lineStatus) as xtremManufacturing.enums.WorkOrderLineStatusFilteredForTracking;
            }
            return null;
        },
    })
    readonly uLineStatusForTracking: Promise<xtremManufacturing.enums.WorkOrderLineStatusFilteredForTracking | null>;

    /**
     * gives the status of the price correction launched by the mutation WorkOrder.closeWorkOrder
     */
    @decorators.enumProperty<WorkOrderReleasedItem, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        defaultValue: () => 'draft',
        duplicatedValue: useDefaultValue,
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.collectionProperty<WorkOrderReleasedItem, 'stockTransactions'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    @decorators.jsonProperty<WorkOrderReleasedItem, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'manufacturingDirect',
                companyId: (await site.legalCompany)._id,
                site,
                item: await this.releasedItem,
            });
        },
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<WorkOrderReleasedItem, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<WorkOrderReleasedItem, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'manufacturingDirect',
                companyId: (await site.legalCompany)._id,
                site,
                item: await this.releasedItem,
            });
        },
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<WorkOrderReleasedItem, 'computedAttributes'>({
        isPublished: true,
        dependsOn: ['releasedItem', { document: ['site'] }],
        async computeValue() {
            const document = await this.document;
            return xtremFinanceData.functions.computeGenericAttributes(this.$.context, {
                item: await this.releasedItem,
                stockSite: await document.site,
                financialSite: await document.site,
                returnBusinessSite: false,
            });
        },
    })
    readonly computedAttributes: Promise<object>;

    @decorators.referenceProperty<WorkOrderReleasedItem, 'itemSite'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.ItemSite,
        dependsOn: ['releasedItem', { document: ['site'] }],
        join: {
            item() {
                return this.releasedItem;
            },
            async site() {
                return (await this.document).site;
            },
        },
    })
    readonly itemSite: Reference<xtremMasterData.nodes.ItemSite | null>;

    allowPlannedCostUpdate = false;

    @decorators.decimalProperty<WorkOrderReleasedItem, 'plannedMaterialCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        isFrozen() {
            return !this.allowPlannedCostUpdate;
        },
    })
    readonly plannedMaterialCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderReleasedItem, 'plannedMachineCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        isFrozen() {
            return !this.allowPlannedCostUpdate;
        },
    })
    readonly plannedMachineCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderReleasedItem, 'plannedLaborCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        isFrozen() {
            return !this.allowPlannedCostUpdate;
        },
    })
    readonly plannedLaborCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderReleasedItem, 'plannedToolCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        isFrozen() {
            return !this.allowPlannedCostUpdate;
        },
    })
    readonly plannedToolCost: Promise<decimal>;

    @decorators.decimalProperty<WorkOrderReleasedItem, 'plannedProcessCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async getValue() {
            return (await this.plannedMachineCost) + (await this.plannedLaborCost) + (await this.plannedToolCost);
        },
    })
    readonly plannedProcessCost: Promise<decimal>;

    async getTotalPlannedCost(): Promise<{ amount: decimal; unitCost: decimal }> {
        const amount = (await this.plannedProcessCost) + (await this.plannedMaterialCost);
        const releasedQuantity = await this.releasedQuantity;
        const unitCost = releasedQuantity ? amount / releasedQuantity : 0;
        return { amount, unitCost };
    }

    @decorators.mutation<typeof WorkOrderReleasedItem, 'updateReleasedItem'>({
        isPublished: true,
        parameters: [
            { name: 'workOrderNumber', type: 'string' },
            { name: 'releasedItem', type: 'string' },
            { name: 'quantity', type: 'decimal' },
            { name: 'completed', type: 'boolean' },
        ],
        return: { type: 'reference', node: () => xtremManufacturing.nodes.WorkOrderReleasedItem },
    })
    static async updateReleasedItem(
        context: Context,
        workOrderNumber: string,
        releasedItem: string,
        quantity: decimal,
        completed: boolean,
    ): Promise<xtremManufacturing.nodes.WorkOrderReleasedItem | null> {
        const workOrder = await context.read(
            xtremManufacturing.nodes.WorkOrder,
            { number: workOrderNumber },
            { forUpdate: true },
        );
        const workOrderItem = await workOrder.productionItems.find(
            async workOrderReleasedItem => (await workOrderReleasedItem.releasedItemId) === releasedItem,
        );
        if (workOrderItem) {
            if (completed) {
                await workOrderItem.$.set({ lineStatus: 'completed' });
            }
            await workOrderItem.$.set({
                totalActualQuantity: (await workOrderItem.totalActualQuantity) + quantity,
            });
            await workOrder.$.save();

            // FIXME: it works but it could be enhanced
            await workOrder.$.set({
                status: await xtremManufacturing.functions.workOrderLib.getWorkOrderStatus(workOrder),
            });
            await workOrder.$.save();

            return workOrderItem;
        }
        return null;
    }

    @decorators.referenceArrayProperty<WorkOrderReleasedItem, 'serialNumbers'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.serialNumberOption],
        isPublished: true,
        isStored: true,
        isNullable: true,
        onDelete: 'restrict',
        node: () => xtremStockData.nodes.SerialNumber,
    })
    readonly serialNumbers: Promise<xtremStockData.nodes.SerialNumber[] | null>;

    async checkAndUpdateQuantityOnAssignment(): Promise<void> {
        if (
            this.$.status === NodeStatus.modified &&
            (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.orderToOrderOption))
        ) {
            const workOrderAssignment = await xtremStockData.nodes.OrderAssignment.getSupplyAssignment(
                this.$.context,
                'salesOrderLine',
                this,
            );
            if (workOrderAssignment?._id) {
                const orderAssignment = await this.$.context.read(
                    xtremStockData.nodes.OrderAssignment,
                    { _id: workOrderAssignment._id },
                    { forUpdate: true },
                );
                if ((await this.quantityInStockUnit) < (await orderAssignment.quantityInStockUnit)) {
                    await orderAssignment.$.set({ quantityInStockUnit: await this.quantityInStockUnit });
                    await orderAssignment.$.save();
                }
            }
        }
    }

    async checkAndDeleteAssignment(): Promise<void> {
        if (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.orderToOrderOption)) {
            const result = await xtremStockData.nodes.OrderAssignment.getSupplyAssignment(
                this.$.context,
                'salesOrderLine',
                this,
            );
            if (result && result._id) {
                await this.$.context.delete(xtremStockData.nodes.OrderAssignment, { _id: result._id });
            }
        }
    }
}
