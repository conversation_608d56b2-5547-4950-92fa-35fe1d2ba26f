import type { decimal, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremManufacturing from '..';

@decorators.node<WorkInProgressResultLine>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class WorkInProgressResultLine extends Node {
    @decorators.referenceProperty<WorkInProgressResultLine, 'inputSet'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremManufacturing.nodes.WorkInProgressInputSet,
    })
    readonly inputSet: Reference<xtremManufacturing.nodes.WorkInProgressInputSet>;

    @decorators.referenceProperty<WorkInProgressResultLine, 'workOrder'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        node: () => xtremManufacturing.nodes.WorkOrder,
    })
    readonly workOrder: Reference<xtremManufacturing.nodes.WorkOrder>;

    @decorators.referenceProperty<WorkInProgressResultLine, 'item'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        node: () => xtremManufacturing.nodes.WorkOrderReleasedItem,
        dependsOn: [{ workOrder: ['productionItem'] }],
        async defaultValue() {
            return (await this.workOrder).productionItem;
        },
    })
    readonly item: Reference<xtremManufacturing.nodes.WorkOrderReleasedItem | null>;

    @decorators.enumProperty<WorkInProgressResultLine, 'status'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremManufacturing.enums.workOrderStatusDataType,
        dependsOn: [{ workOrder: ['status'] }],
        async defaultValue() {
            return (await this.workOrder).status;
        },
    })
    readonly status: Promise<xtremManufacturing.enums.WorkOrderStatus>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'productionCost'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue: 0,
    })
    readonly productionCost: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'plannedMaterialCost'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue: 0,
    })
    readonly plannedMaterialCost: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'actualMaterialCost'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue: 0,
    })
    readonly actualMaterialCost: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'materialCostVariance'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['actualMaterialCost', 'actualMaterialCost'],
        async getValue() {
            return (await this.actualMaterialCost) - (await this.plannedMaterialCost);
        },
    })
    readonly materialCostVariance: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'materialCostVariancePercentage'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['plannedMaterialCost', 'materialCostVariance'],
        async getValue() {
            return (await this.plannedMaterialCost) === 0
                ? 0
                : (100 * (await this.materialCostVariance)) / (await this.plannedMaterialCost);
        },
    })
    readonly materialCostVariancePercentage: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'plannedProcessCost'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue: 0,
    })
    readonly plannedProcessCost: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'actualProcessCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['actualMachineCost', 'actualLaborCost', 'actualToolCost'],
        async getValue() {
            return (await this.actualMachineCost) + (await this.actualLaborCost) + (await this.actualToolCost);
        },
    })
    readonly actualProcessCost: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'processCostVariance'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['actualProcessCost', 'plannedProcessCost'],
        async getValue() {
            return (await this.actualProcessCost) - (await this.plannedProcessCost);
        },
    })
    readonly processCostVariance: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'processCostVariancePercentage'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['processCostVariance', 'plannedProcessCost'],
        async getValue() {
            return (await this.plannedProcessCost) === 0
                ? 0
                : (100 * (await this.processCostVariance)) / (await this.plannedProcessCost);
        },
    })
    readonly processCostVariancePercentage: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'plannedMachineCost'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue: 0,
    })
    readonly plannedMachineCost: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'actualMachineCost'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue: 0,
    })
    readonly actualMachineCost: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'machineCostVariance'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['actualMachineCost', 'plannedMachineCost'],
        async getValue() {
            return (await this.actualMachineCost) - (await this.plannedMachineCost);
        },
    })
    readonly machineCostVariance: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'machineCostVariancePercentage'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['plannedMachineCost', 'machineCostVariance'],
        async getValue() {
            return (await this.plannedMachineCost) === 0
                ? 0
                : (100 * (await this.machineCostVariance)) / (await this.plannedMachineCost);
        },
    })
    readonly machineCostVariancePercentage: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'plannedLaborCost'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue: 0,
    })
    readonly plannedLaborCost: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'actualLaborCost'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue: 0,
    })
    readonly actualLaborCost: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'laborCostVariance'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['actualLaborCost', 'plannedLaborCost'],
        async getValue() {
            return (await this.actualLaborCost) - (await this.plannedLaborCost);
        },
    })
    readonly laborCostVariance: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'laborCostVariancePercentage'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['laborCostVariance', 'plannedLaborCost'],
        async getValue() {
            return (await this.plannedLaborCost) === 0
                ? 0
                : (100 * (await this.laborCostVariance)) / (await this.plannedLaborCost);
        },
    })
    readonly laborCostVariancePercentage: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'plannedToolCost'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue: 0,
    })
    readonly plannedToolCost: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'actualToolCost'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue: 0,
    })
    readonly actualToolCost: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'toolCostVariance'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['actualToolCost', 'plannedToolCost'],
        async getValue() {
            return (await this.actualToolCost) - (await this.plannedToolCost);
        },
    })
    readonly toolCostVariance: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'toolCostVariancePercentage'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['plannedToolCost', 'toolCostVariance'],
        async getValue() {
            return (await this.plannedToolCost) === 0
                ? 0
                : (100 * (await this.toolCostVariance)) / (await this.plannedToolCost);
        },
    })
    readonly toolCostVariancePercentage: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'totalPlannedCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['plannedMaterialCost', 'plannedProcessCost'],
        async getValue() {
            return (await this.plannedMaterialCost) + (await this.plannedProcessCost);
        },
    })
    readonly totalPlannedCost: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'totalActualCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['actualMaterialCost', 'actualProcessCost'],
        async getValue() {
            return (await this.actualMaterialCost) + (await this.actualProcessCost);
        },
    })
    readonly totalActualCost: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'totalCostVariance'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['totalActualCost', 'totalPlannedCost'],
        async getValue() {
            return (await this.totalActualCost) - (await this.totalPlannedCost);
        },
    })
    readonly totalCostVariance: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'totalCostVariancePercentage'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['totalCostVariance', 'totalPlannedCost'],
        async getValue() {
            return (await this.totalPlannedCost) === 0
                ? 0
                : (100 * (await this.totalCostVariance)) / (await this.totalPlannedCost);
        },
    })
    readonly totalCostVariancePercentage: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'positiveClosingVariance'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue: 0,
    })
    readonly positiveClosingVariance: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'negativeClosingVariance'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        defaultValue: 0,
    })
    readonly negativeClosingVariance: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'closingVariance'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['positiveClosingVariance', 'negativeClosingVariance'],
        async getValue() {
            return (await this.positiveClosingVariance) - (await this.negativeClosingVariance);
        },
    })
    readonly closingVariance: Promise<decimal>;

    @decorators.decimalProperty<WorkInProgressResultLine, 'workInProgressTotal'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: ['totalActualCost', 'productionCost', 'closingVariance'],
        async getValue() {
            return (await this.totalActualCost) - (await this.productionCost) - (await this.closingVariance);
        },
    })
    readonly workInProgressTotal: Promise<decimal>;

    @decorators.booleanProperty<WorkInProgressResultLine, 'isStockJournalAvailable'>({
        isPublished: true,
        isStored: true,
        defaultValue: false,
    })
    isStockJournalAvailable: boolean;

    @decorators.booleanProperty<WorkInProgressResultLine, 'isJournalEntryAvailable'>({
        isPublished: true,
        isStored: true,
        defaultValue: false,
    })
    isJournalEntryAvailable: boolean;
}
