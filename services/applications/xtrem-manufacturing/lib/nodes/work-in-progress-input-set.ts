import type { Collection, Context, decimal, NodeQueryFilter, Reference } from '@sage/xtrem-core';
import { BusinessRuleError, date, datetime, decorators, Logger, Node, SystemError } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { LocalizedError, type InitialNotificationAction } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremManufacturing from '..';

const logger = Logger.getLogger(__filename, 'work-in-progress-input-set');

@decorators.node<WorkInProgressInputSet>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: true,
    canSearch: true,
    canUpdate: true,
    canDelete: true,
    indexes: [
        {
            orderBy: { user: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class WorkInProgressInputSet extends Node {
    @decorators.referenceProperty<WorkInProgressInputSet, 'user'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremSystem.nodes.User,
        async defaultValue() {
            const userInfo = await this.$.context.user;
            if (!userInfo) {
                throw new SystemError('Current context does not have a valid user.');
            }
            return this.$.context.read(xtremSystem.nodes.User, { email: userInfo.email });
        },
    })
    readonly user: Reference<xtremSystem.nodes.User>;

    @decorators.referenceProperty<WorkInProgressInputSet, 'site'>({
        isStored: true,
        isPublished: true,
        provides: ['site'],
        node: () => xtremSystem.nodes.Site,
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<WorkInProgressInputSet, 'fromWorkOrder'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremManufacturing.nodes.WorkOrder,
    })
    readonly fromWorkOrder: Reference<xtremManufacturing.nodes.WorkOrder | null>;

    @decorators.referenceProperty<WorkInProgressInputSet, 'toWorkOrder'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremManufacturing.nodes.WorkOrder,
    })
    readonly toWorkOrder: Reference<xtremManufacturing.nodes.WorkOrder | null>;

    @decorators.dateProperty<WorkInProgressInputSet, 'asOfDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: () => date.today(),
    })
    readonly asOfDate: Promise<date>;

    @decorators.enumProperty<WorkInProgressInputSet, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremManufacturing.enums.workInProgressInquiryStatusDataType,
        defaultValue: 'draft',
    })
    readonly status: Promise<xtremManufacturing.enums.WorkInProgressInquiryStatus>;

    @decorators.datetimeProperty<WorkInProgressInputSet, 'executionDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly executionDate: Promise<datetime> | null;

    @decorators.referenceProperty<WorkInProgressInputSet, 'currency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: [{ site: ['currency'] }],
        async getValue() {
            return (await this.site).currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<WorkInProgressInputSet, 'workInProgressTotalAmount'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        getValue() {
            return this.lines.sum(line => line.workInProgressTotal);
        },
    })
    readonly workInProgressTotalAmount: Promise<decimal>;

    @decorators.collectionProperty<WorkInProgressInputSet, 'lines'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'inputSet',
        node: () => xtremManufacturing.nodes.WorkInProgressResultLine,
    })
    readonly lines: Collection<xtremManufacturing.nodes.WorkInProgressResultLine>;

    @decorators.asyncMutation<typeof WorkInProgressInputSet, 'wipInquiry'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [{ name: 'userId', type: 'string' }],
        return: 'boolean',
    })
    static async wipInquiry(context: Context, userId: string): Promise<boolean> {
        let message = context.localize(
            '@sage/xtrem-manufacturing/nodes__work-in-progress-input-set__success_message',
            'Success',
        );

        await context.runInWritableContext(async writableContext => {
            const inputSet = await writableContext.read(WorkInProgressInputSet, { user: userId }, { forUpdate: true });

            // Remove all lines, set status and last execution date
            await inputSet.$.set({ lines: [], status: 'inProgress', executionDate: datetime.now() });
            await inputSet.$.save();
        });

        const result = await context.runInWritableContext(async writableContext => {
            const inputSet = await writableContext.read(WorkInProgressInputSet, { user: userId }, { forUpdate: true });

            // Do calculation.
            let newStatus: xtremManufacturing.enums.WorkInProgressInquiryStatus;
            try {
                await WorkInProgressInputSet.executeInquiry(writableContext, inputSet);
                if (await inputSet.$.control()) {
                    newStatus = 'completed';
                } else {
                    newStatus = 'error';
                    message = 'error';
                }
            } catch (error) {
                logger.error(() => JSON.stringify(error));
                if (!(error instanceof LocalizedError)) throw error;

                newStatus = 'error';
                message = error.getMessageAndDiagnosesText(writableContext.diagnoses);
            }
            newStatus = 'completed' as xtremManufacturing.enums.WorkInProgressInquiryStatus;

            // Signal completion.
            await inputSet.$.set({ status: newStatus, executionDate: datetime.now() });
            await inputSet.$.save();

            return { sysId: inputSet._id, status: newStatus, message };
        });

        // Notify user.
        await WorkInProgressInputSet.sendNotification(context, {
            sysId: result.sysId.toString(),
            success: result.status !== 'error',
            message,
        });

        if (result.status === 'error') {
            throw new BusinessRuleError(result.message);
        }
        return true;
    }

    static async executeInquiry(context: Context, inputSet: WorkInProgressInputSet) {
        const asOfDate = await inputSet.asOfDate;

        const filter: NodeQueryFilter<xtremManufacturing.nodes.WorkOrder> = {
            _and: [
                { site: await inputSet.site },
                { startDate: { _lte: asOfDate } },
                { _or: [{ closingDate: { _eq: null } }, { closingDate: { _gte: asOfDate } }] },
            ],
        };

        const fromWorkOrderNumber = await (await inputSet.fromWorkOrder)?.number;
        if (fromWorkOrderNumber) {
            filter._and?.push({ number: { _gte: fromWorkOrderNumber } });
        }

        const toWorkOrderNumber = await (await inputSet.toWorkOrder)?.number;
        if (toWorkOrderNumber) {
            filter._and?.push({ number: { _lte: toWorkOrderNumber } });
        }

        const workOrders = context.query(xtremManufacturing.nodes.WorkOrder, { filter });

        await workOrders.forEach(async workOrder => {
            let actualMaterialCost = 0;
            let actualMachineCost = 0;
            let actualLaborCost = 0;
            let actualToolCost = 0;
            let productionCost = 0;
            let positiveClosingVariance = 0;
            let negativeClosingVariance = 0;

            // Request on WorkInProgressCost to get actual costs relative to material and work order variance
            const costs = WorkInProgressInputSet.searchActualOtherCosts(context, workOrder, asOfDate);
            await costs.forEach(cost => {
                const costAmount = cost.values.amount?.sum ?? 0;

                switch (cost.group.type) {
                    case 'materialTracking':
                        actualMaterialCost += costAmount;
                        break;
                    case 'productionTracking':
                        productionCost += costAmount;
                        break;
                    case 'workOrderVariance':
                    case 'workOrderActualCostAdjustment':
                    case 'workOrderActualCostAdjustmentNonAbsorbed':
                        positiveClosingVariance += costAmount;
                        break;
                    case 'workOrderNegativeVariance':
                    case 'workOrderNegativeActualCostAdjustment':
                    case 'workOrderNegativeActualCostAdjustmentNonAbsorbed':
                        negativeClosingVariance = costAmount;
                        break;
                    default:
                        break;
                }
            });

            // Request on OperationTrackingLine to get actual time costs
            const timeTrackings = context.query(xtremManufacturing.nodes.OperationTrackingLine, {
                filter: {
                    workOrderOperation: { workOrder },
                },
            });
            await timeTrackings.forEach(async tracking => {
                const timeCostAmount = await tracking.workInProgressCosts
                    .filter(async wipCost => (await wipCost.effectiveDate).compare(asOfDate) <= 0)
                    .reduce(async (prev, wipCost) => prev + (await wipCost.amount), 0);

                const detailedResource = await tracking.actualResource;
                if (detailedResource instanceof xtremMasterData.nodes.MachineResource) {
                    actualMachineCost += timeCostAmount;
                } else if (detailedResource instanceof xtremMasterData.nodes.LaborResource) {
                    actualLaborCost += timeCostAmount;
                } else if (detailedResource instanceof xtremMasterData.nodes.ToolResource) {
                    actualToolCost += timeCostAmount;
                }
            });

            // Add a (work in progress result) line
            await inputSet.lines.append({
                workOrder,
                item: await workOrder.productionItem,
                status: await workOrder.status,
                productionCost,
                plannedMaterialCost: await workOrder.plannedMaterialCost,
                actualMaterialCost,
                plannedProcessCost: await workOrder.plannedProcessCost,
                plannedMachineCost: await workOrder.plannedMachineCost,
                actualMachineCost,
                plannedLaborCost: await workOrder.plannedLaborCost,
                actualLaborCost,
                plannedToolCost: await workOrder.plannedToolCost,
                actualToolCost,
                positiveClosingVariance,
                negativeClosingVariance,
                isStockJournalAvailable: await WorkInProgressInputSet.hasStockJournal(
                    context,
                    (await inputSet.site)._id,
                    workOrder._id,
                ),
                isJournalEntryAvailable: await WorkInProgressInputSet.hasJournalEntry(context, workOrder._id),
            });
        });
    }

    static searchActualOtherCosts(context: Context, workOrder: xtremManufacturing.nodes.WorkOrder, asOfDate: date) {
        const filter: NodeQueryFilter<xtremManufacturing.nodes.WorkInProgressCost> = {
            _and: [
                {
                    type: {
                        _in: [
                            'materialTracking',
                            'workOrderVariance',
                            'workOrderNegativeVariance',
                            'workOrderActualCostAdjustment',
                            'workOrderNegativeActualCostAdjustment',
                            'workOrderActualCostAdjustmentNonAbsorbed',
                            'workOrderNegativeActualCostAdjustmentNonAbsorbed',
                            'productionTracking',
                        ],
                    },
                },
                { workOrder },
                { effectiveDate: { _lte: asOfDate } },
            ],
        };

        return context.queryAggregate(xtremManufacturing.nodes.WorkInProgressCost, {
            filter,
            group: { type: { _by: 'value' } },
            values: { amount: { sum: true } },
        });
    }

    static async hasStockJournal(context: Context, siteSysId: number, workOrderSysId: number) {
        return (
            (await context.query(xtremStockData.nodes.StockJournal, {
                filter: {
                    site: siteSysId,
                    workOrder: { _id: workOrderSysId },
                },
                first: 1,
            }).length) > 0
        );
    }

    static async hasJournalEntry(context: Context, workOrderSysId: number) {
        return (
            (await context.query(xtremFinanceData.nodes.FinanceTransaction, {
                filter: {
                    targetDocumentType: 'journalEntry',
                    documentType: 'workInProgress',
                    documentSysId: workOrderSysId,
                    targetDocumentSysId: { _gt: 0 },
                },
                first: 1,
            }).length) > 0
        );
    }

    static async sendNotification(context: Context, param: { sysId: string; success: boolean; message: string }) {
        const actions: InitialNotificationAction[] = [
            {
                link: context.batch.notificationStateLink,
                title: 'History',
                icon: 'link',
                style: 'tertiary',
            },
        ];

        if (param.sysId) {
            actions.push({
                link: `@sage/xtrem-manufacturing/WipInquiry/${param.sysId}`,
                title: 'Results',
                icon: 'link',
                style: 'tertiary',
            });
        }

        await context.notifyUser({
            title: context.localize(
                '@sage/xtrem-manufacturing/nodes__work-in-progress-input-set__success_notification_title',
                'Work in progress inquiry calculation complete',
            ),
            description: param.message,
            icon: param.success ? 'tick' : 'error',
            level: param.success ? 'success' : 'error',
            shouldDisplayToast: true,
            actions,
        });
    }
}
