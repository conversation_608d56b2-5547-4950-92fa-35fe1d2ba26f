import type { Collection, Context, decimal, integer, Reference } from '@sage/xtrem-core';
import { date, decorators, Logger, Node, NodeStatus, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { validateMaterialTrackingParameters } from '../functions/index';
import type { TimeTracking } from '../functions/operation-tracking';
import {
    createMultipleOperationalTrackingsFromTimeTracking,
    createOperationTrackings,
} from '../functions/operation-tracking';
import * as xtremManufacturing from '../index';

const logger = Logger.getLogger(__filename, 'operation-tracking');

@decorators.node<OperationTracking>({
    isClearedByReset: true,
    package: 'xtrem-manufacturing',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    isCustomizable: true,
    indexes: [{ orderBy: { number: +1 }, isUnique: true, isNaturalKey: true }],
    controlDelete(cx) {
        cx.addDiagnose(
            ValidationSeverity.error,
            cx.localize(
                '@sage/xtrem-manufacturing/nodes__operation_tracking__deletion_forbidden',
                'This operation tracking cannot be deleted.',
            ),
        );
    },
    async controlBegin(cx) {
        const workOrder = await this.workOrder;
        await cx.error
            .withMessage(
                '@sage/xtrem-manufacturing/nodes__manufacturing_tracking__work_order_type_must_be_firm',
                'Only firm work orders can be tracked.',
            )
            .if(await workOrder.type)
            .is.not.equal.to('firm');

        await cx.error
            .withMessage(
                '@sage/xtrem-manufacturing/nodes__operation_tracking__work_order_with_incorrect_routing',
                'Work order {{workOrderNumber}}: No time tracking generated for a routing that is in development.',
                async () => {
                    return { workOrderNumber: await workOrder.number };
                },
            )
            .if(await (await workOrder.routingCode)?.status)
            .is.equal.to('inDevelopment');
    },
    async saveBegin() {
        if (await this.number) {
            logger.warn(() => 'The ID already exists, and no sequence number will be allocated');
            return;
        }
        await this.$.set({
            number: await (
                await xtremMasterData.classes.DocumentNumberGenerator.create(this.$.context, {
                    currentDate: date.today(),
                    nodeInstance: this,
                })
            ).allocate(),
        });
    },
    async saveEnd() {
        const workOrder = await this.$.context.read(
            xtremManufacturing.nodes.WorkOrder,
            { _id: (await this.workOrder)._id },
            { forUpdate: true },
        );
        await workOrder.$.set({
            status: await xtremManufacturing.functions.workOrderLib.getWorkOrderStatus(workOrder),
        });

        await workOrder.$.save();

        // Because of the import tool, we need to create the accounting notification on creation in the saveEnd,
        // but we should not do it when the node is being updated, which is the case in a repost mutation
        if (this.$.status === NodeStatus.added) {
            await this.createAccountingNotification();
        }
    },
})
export class OperationTracking extends Node implements xtremFinanceData.interfaces.FinanceOriginDocument {
    @decorators.stringProperty<OperationTracking, 'number'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        provides: ['sequenceNumber'],
        lookupAccess: true,
    })
    readonly number: Promise<string>;

    @decorators.referenceProperty<OperationTracking, 'workOrder'>({
        isStored: true,
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrder,
        lookupAccess: true,
    })
    readonly workOrder: Reference<xtremManufacturing.nodes.WorkOrder>;

    @decorators.referenceProperty<OperationTracking, 'site'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['workOrder'],
        provides: ['site'],
        node: () => xtremSystem.nodes.Site,
        async defaultValue() {
            return (await this.workOrder).site;
        },
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.dateProperty<OperationTracking, 'entryDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isNullable: false,
        defaultValue() {
            return date.today();
        },
    })
    readonly entryDate: Promise<date>;

    @decorators.dateProperty<OperationTracking, 'effectiveDate'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['entryDate'],
        defaultValue() {
            return this.entryDate;
        },
    })
    readonly effectiveDate: Promise<date>;

    // property needed for the accounting interface
    @decorators.referenceProperty<OperationTracking, 'financialSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        async getValue() {
            return xtremFinanceData.functions.getFinancialSite(await this.site);
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    // property needed for the accounting interface
    @decorators.referenceProperty<OperationTracking, 'transactionCurrency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    // property needed for the accounting interface
    @decorators.dateProperty<OperationTracking, 'documentDate'>({
        isPublished: true,
        getValue() {
            return this.effectiveDate;
        },
    })
    readonly documentDate: Promise<date>;

    @decorators.collectionProperty<OperationTracking, 'lines'>({
        isPublished: true,
        isVital: true,
        isRequired: true,
        node: () => xtremManufacturing.nodes.OperationTrackingLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<xtremManufacturing.nodes.OperationTrackingLine>;

    /** Only call from  createWorkOrderTracking method of productionTracking  decorator have to be deleted */
    @decorators.mutation<typeof OperationTracking, 'createOperationTrackings'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'workOrder',
                type: 'reference',
                node: () => xtremManufacturing.nodes.WorkOrder,
                isMandatory: true,
            },
            { name: 'workOrderTrackingNumber', type: 'string', isMandatory: true },
            { name: 'trackingQuantity', type: 'decimal', isMandatory: true },
            { name: 'trackingDate', type: 'date' },
            { name: 'operationNumber', type: 'integer' },
        ],
        return: {
            type: 'reference',
            node: () => xtremManufacturing.nodes.OperationTracking,
        },
    })
    static async createOperationTrackings(
        readOnlyContext: Context,
        workOrder: xtremManufacturing.nodes.WorkOrder,
        workOrderTrackingNumber: string,
        trackingQuantity: decimal,
        trackingDate: date = date.today(),
        operationNumber?: integer,
    ): Promise<xtremManufacturing.nodes.OperationTracking | null> {
        await validateMaterialTrackingParameters(
            readOnlyContext,
            workOrder,
            trackingQuantity,
            workOrderTrackingNumber,
            operationNumber,
        );
        return createOperationTrackings(readOnlyContext, {
            workOrder,
            trackingNumber: workOrderTrackingNumber,
            quantity: trackingQuantity,
            date: trackingDate,
            operationNumber,
        });
    }

    @decorators.mutation<typeof OperationTracking, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [
            { name: 'operationTracking', type: 'reference', isMandatory: true, node: () => OperationTracking },
        ],
        return: { type: 'boolean' },
    })
    static async resendNotificationForFinance(
        context: Context,
        operationTracking: OperationTracking,
    ): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-manufacturing/node__operation_tracking__resend_notification_for_finance',
                'Resending finance notification for Operation tracking: {{operationTracking}}.',
                { operationTrackingNumber: await operationTracking.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await (await operationTracking.workOrder).number,
                documentType: 'workInProgress',
                sourceDocumentNumber: await operationTracking.number,
                sourceDocumentType: 'operationTracking',
            })
        ) {
            await operationTracking.createAccountingNotification();
        }

        return true;
    }

    @decorators.notificationListener<typeof OperationTracking>({
        topic: 'OperationTracking/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const operationTracking = await context.read(OperationTracking, { number: document.number });

        await OperationTracking.resendNotificationForFinance(context, operationTracking);
    }

    /**
     * Send notification in order to create staging table entries for the accounting engine
     * @param context
     * @param tracking number of tracking or tracking header itself
     */
    async createAccountingNotification(): Promise<void> {
        // technical: operationTrackingHeader can contain null
        if ((await (await (await this.site).legalCompany).doWipPosting) && (await this.lines.length) > 0) {
            await xtremFinanceData.functions.ManufacturingNotificationLib.operationTrackingNotification(
                this.$.context,
                this as xtremFinanceData.interfaces.FinanceOriginDocument,
                (await this.lines.toArray()) as xtremFinanceData.interfaces.OperationTrackingFinanceDocumentLine[],
            );
        }
    }

    @decorators.asyncMutation<typeof OperationTracking, 'createMultipleOperationalTrackings'>({
        isPublished: true,
        parameters: [
            {
                name: 'trackings',
                type: 'array',
                isMandatory: true,
                item: {
                    type: 'object',
                    properties: {
                        workOrderOperation: {
                            type: 'object',
                            properties: {
                                _id: 'string',
                                workOrder: { type: 'reference', node: () => xtremManufacturing.nodes.WorkOrder },
                                operationNumber: 'integer',
                                name: 'string',
                                remainingQuantity: 'decimal',
                                minCapabilityLevel: {
                                    type: 'reference',
                                    node: () => xtremMasterData.nodes.CapabilityLevel,
                                },
                            },
                        },
                        resource: { type: 'reference', node: () => xtremMasterData.nodes.BaseResource },
                        expectedResource: { type: 'reference', node: () => xtremMasterData.nodes.DetailedResource },
                        setupTimeUnit: { type: 'reference', node: () => xtremMasterData.nodes.UnitOfMeasure },
                        runTimeUnit: { type: 'reference', node: () => xtremMasterData.nodes.UnitOfMeasure },
                        status: { type: 'enum', dataType: () => xtremManufacturing.enums.operationStatusDataType },
                        completedRunTime: 'string',
                        plannedQuantity: 'decimal',
                        completedQuantity: 'string',
                        completedSetupTime: 'decimal',
                        actualQuantity: 'decimal',
                        actualSetupTime: 'decimal',
                        actualRunTime: 'decimal',
                        isCompleted: 'boolean',
                        storedAttributes: 'string',
                        storedDimensions: 'string',
                    },
                },
            },
        ],
        return: { type: 'array', item: 'integer' },
    })
    static createMultipleOperationalTrackings(context: Context, trackings: TimeTracking[]): Promise<integer[]> {
        return createMultipleOperationalTrackingsFromTimeTracking(context, trackings);
    }

    @decorators.notificationListener<typeof OperationTracking>({ topic: 'OperationTracking/accountingInterface' })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }
}
