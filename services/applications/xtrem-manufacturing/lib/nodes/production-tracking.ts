import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, decimal, integer, NodeCreateData, Reference } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, date, decorators, Logger, Node, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremManufacturing from '../index';
import type { IdAndNumberType, WorkOrderTrackingReturnType } from '../interfaces/index';

const logger = Logger.getLogger(__filename, 'production-tracking');
@decorators.node<ProductionTracking>({
    isClearedByReset: true,
    package: 'xtrem-manufacturing',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    isCustomizable: true,
    indexes: [
        {
            orderBy: { number: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    async controlDelete(cx) {
        if (!(await this.canBeUpdated())) {
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize(
                    '@sage/xtrem-manufacturing/nodes__production_tracking__deletion_forbidden',
                    'This production tracking cannot be deleted.',
                ),
            );
        }
    },
    async saveBegin() {
        if (await this.number) {
            logger.warn(() => 'The ID already exists, and no sequence number will be allocated');
            return;
        }
        await this.$.set({
            number: await (
                await xtremMasterData.classes.DocumentNumberGenerator.create(this.$.context, {
                    currentDate: date.today(),
                    nodeInstance: this,
                })
            ).allocate(),
        });
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class ProductionTracking extends Node implements xtremFinanceData.interfaces.FinanceOriginDocument {
    // To allow update on repost from work order
    @decorators.booleanProperty<ProductionTracking, 'forceUpdateForFinance'>({
        defaultValue: false,
    })
    readonly forceUpdateForFinance: Promise<boolean>;

    /**
     * Indicates if a property can be updated
     * @param propertyName The property name or nothing for the node itself
     * @returns
     */
    async canBeUpdated(propertyName?: string): Promise<boolean> {
        if ([undefined, 'stockTransactionStatus'].includes(propertyName)) {
            return (await this.stockTransactionStatus) !== 'completed';
        }

        if (['effectiveDate', 'entryDate'].includes(propertyName || '')) {
            return ['draft'].includes(await this.stockTransactionStatus);
        }

        return false;
    }

    getEffectiveDate(): Promise<date> {
        return this.effectiveDate;
    }

    getStockSite(): Promise<xtremSystem.nodes.Site> {
        return this.site;
    }

    @decorators.stringProperty<ProductionTracking, 'number'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isFrozen: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        provides: ['sequenceNumber'],
        lookupAccess: true,
    })
    readonly number: Promise<string>;

    @decorators.referenceProperty<ProductionTracking, 'workOrder'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremManufacturing.nodes.WorkOrder,
        lookupAccess: true,
    })
    readonly workOrder: Reference<xtremManufacturing.nodes.WorkOrder>;

    @decorators.booleanProperty<ProductionTracking, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    readonly forceUpdateForStock: Promise<boolean>;

    @decorators.enumProperty<ProductionTracking, 'stockTransactionStatus'>({
        isPublished: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        async isFrozen() {
            return !(await this.canBeUpdated('stockTransactionStatus'));
        },
        computeValue() {
            logger.debug(() => 'stockTransactionStatus update');
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.referenceProperty<ProductionTracking, 'site'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['workOrder'],
        isFrozen: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
        async defaultValue() {
            return (await this.workOrder).site;
        },
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.dateProperty<ProductionTracking, 'entryDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isNullable: false,
        async isFrozen() {
            return !(await this.canBeUpdated('entryDate'));
        },
        defaultValue() {
            return date.today();
        },
    })
    readonly entryDate: Promise<date>;

    @decorators.dateProperty<ProductionTracking, 'effectiveDate'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['entryDate'],
        async isFrozen() {
            return !(await this.canBeUpdated('effectiveDate'));
        },
        defaultValue() {
            return this.entryDate;
        },
    })
    readonly effectiveDate: Promise<date>;

    // property needed for the accounting interface
    @decorators.referenceProperty<ProductionTracking, 'financialSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        dependsOn: ['site'],
        async getValue() {
            return xtremFinanceData.functions.getFinancialSite(await this.site);
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    // property needed for the accounting interface
    @decorators.referenceProperty<ProductionTracking, 'transactionCurrency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['financialSite'],
        async getValue() {
            return (await (await this.financialSite).legalCompany).currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    // property needed for the accounting interface
    @decorators.dateProperty<ProductionTracking, 'documentDate'>({
        isPublished: true,
        dependsOn: ['effectiveDate'],
        getValue() {
            return this.effectiveDate;
        },
    })
    readonly documentDate: Promise<date>;

    @decorators.collectionProperty<ProductionTracking, 'lines'>({
        isPublished: true,
        isVital: true,
        isRequired: true,
        node: () => xtremManufacturing.nodes.ProductionTrackingLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<xtremManufacturing.nodes.ProductionTrackingLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    @decorators.mutation<typeof ProductionTracking, 'createWorkOrderTracking'>({
        startsReadOnly: true,
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    itemCode: {
                        type: 'string',
                        isMandatory: true,
                    },
                    workOrderNumber: {
                        type: 'string',
                        isMandatory: true,
                    },
                    workOrderTrackingNumber: {
                        type: 'string',
                        isMandatory: true,
                    },
                    trackingQuantity: {
                        type: 'decimal',
                        isMandatory: true,
                    },
                    trackingDate: {
                        type: 'date',
                        isMandatory: false,
                    },
                    materialTracking: {
                        type: 'boolean',
                        isMandatory: false,
                    },
                    timeTracking: {
                        type: 'boolean',
                        isMandatory: false,
                    },
                    completed: {
                        type: 'boolean',
                        isMandatory: false,
                    },
                    stockDetails: {
                        type: 'array',
                        item: {
                            type: 'object',
                            isNullable: false,
                            properties: {
                                effectiveDate: {
                                    type: 'string',
                                    isMandatory: false,
                                },
                                site: {
                                    type: 'reference',
                                    isMandatory: true,
                                    node: () => xtremSystem.nodes.Site,
                                },
                                item: {
                                    type: 'reference',
                                    isMandatory: true,
                                    node: () => xtremMasterData.nodes.Item,
                                },
                                stockUnit: {
                                    type: 'reference',
                                    isMandatory: true,
                                    node: () => xtremMasterData.nodes.UnitOfMeasure,
                                },
                                status: {
                                    type: 'reference',
                                    isMandatory: true,
                                    node: () => xtremStockData.nodes.StockStatus,
                                },
                                location: {
                                    type: 'reference',
                                    isMandatory: false,
                                    node: () => xtremMasterData.nodes.Location,
                                },
                                lotCreateData: {
                                    type: 'string',
                                    isMandatory: false,
                                },
                                existingLot: {
                                    type: 'reference',
                                    isMandatory: false,
                                    node: () => xtremStockData.nodes.Lot,
                                },
                                owner: {
                                    type: 'string',
                                    isMandatory: false,
                                },
                                quantityInStockUnit: {
                                    type: 'decimal',
                                    isMandatory: true,
                                },
                            },
                        },
                    },
                    storedDimensions: {
                        type: 'string',
                        isMandatory: false,
                    },
                    storedAttributes: {
                        type: 'string',
                        isMandatory: false,
                    },
                },
                isMandatory: true,
            },
        ],
        return: {
            type: 'object',
            properties: {
                workOrder: {
                    type: 'reference',
                    node: () => xtremManufacturing.nodes.WorkOrder,
                },
                message: { type: 'string' },
                materialTracking: {
                    type: 'reference',
                    node: () => xtremManufacturing.nodes.MaterialTracking,
                },
                timeTracking: {
                    type: 'object',
                    properties: {
                        _id: {
                            type: 'decimal',
                        },
                        number: {
                            type: 'string',
                        },
                    },
                },
                productionTracking: {
                    type: 'object',
                    properties: {
                        _id: {
                            type: 'decimal',
                        },
                        number: {
                            type: 'string',
                        },
                    },
                },
            },
        },
    })
    static async createWorkOrderTracking(
        readOnlyContext: Context,
        data: {
            itemCode: string;
            workOrderNumber: string;
            workOrderTrackingNumber: string;
            trackingQuantity: decimal;
            trackingDate?: date;
            materialTracking?: boolean;
            timeTracking?: boolean;
            completed?: boolean;
            stockDetails: Array<NodeCreateData<xtremStockData.nodes.StockReceiptDetail>>;
            storedDimensions?: string;
            storedAttributes?: string;
        },
    ): Promise<WorkOrderTrackingReturnType> {
        xtremManufacturing.functions.validateWorkOrderTrackingParameters(
            readOnlyContext,
            data.itemCode,
            data.trackingQuantity,
            data.workOrderNumber,
        );

        const workOrder = await readOnlyContext.read(xtremManufacturing.nodes.WorkOrder, {
            number: data.workOrderNumber,
        });

        if (await readOnlyContext.isServiceOptionEnabled(xtremMasterData.serviceOptions.phantomItemOption)) {
            const somePhantomItems = await workOrder.productionComponents.some(
                async (component: xtremManufacturing.nodes.WorkOrderComponent) => {
                    const item = await component.item;
                    const isPhantom = item && (await item.isPhantom) && (await component.lineStatus) !== 'excluded';
                    return isPhantom ?? false;
                },
            );
            if (somePhantomItems) {
                throw new BusinessRuleError(
                    readOnlyContext.localize(
                        '@sage/xtrem-manufacturing/nodes__production_tracking__phantom_item_not_allowed',
                        'Phantom components are not allowed in production tracking.',
                    ),
                );
            }
        }

        const workOrderItem = await workOrder.productionItems.elementAt(0);
        const result: WorkOrderTrackingReturnType = {
            message: '',
            workOrder: null,
            materialTracking: null,
            productionTracking: null,
            timeTracking: null,
        };

        let trackingNumber = data.workOrderTrackingNumber;

        if (!trackingNumber) {
            const releasedItemDocumentSite = await (await workOrderItem.document).site;
            trackingNumber = await readOnlyContext.runInWritableContext(async writableContext => {
                return (
                    await xtremMasterData.classes.DocumentNumberGenerator.create(writableContext, {
                        sequenceNumber: 'WorkOrderTracking',
                        currentDate: date.today(),
                        site: releasedItemDocumentSite,
                        company: await releasedItemDocumentSite?.legalCompany,
                    })
                ).allocate();
            });
        }

        if (
            data.materialTracking &&
            (await workOrder.bomCode) !== null &&
            (await (await workOrder.bomCode)?.status) !== 'inDevelopment'
        ) {
            const { concatenatedMessages, materialTracking } =
                await xtremManufacturing.nodes.MaterialTracking.createMaterialTrackings(
                    readOnlyContext,
                    workOrder,
                    trackingNumber,
                    data.trackingQuantity,
                    data.trackingDate || date.today(),
                    undefined,
                    true,
                    false,
                );
            result.message += concatenatedMessages;
            result.materialTracking = materialTracking;
        }
        if (data.timeTracking && (await (await workOrder.bomCode)?.status) !== 'inDevelopment') {
            await xtremManufacturing.nodes.OperationTracking.createOperationTrackings(
                readOnlyContext,
                workOrder,
                trackingNumber,
                data.trackingQuantity,
                data.trackingDate || date.today(),
            );

            const timeTracked = await readOnlyContext
                .query(xtremManufacturing.nodes.OperationTracking, {
                    filter: { workOrder: { _id: workOrder._id } },
                })
                .toArray();

            if (timeTracked.length > 0) {
                const time: IdAndNumberType = {
                    _id: timeTracked[0]?._id,
                    number: await timeTracked[0]?.number,
                };
                result.workOrder = await timeTracked[0].workOrder;
                result.timeTracking = time;
            }
        }

        const productionTracking = await ProductionTracking.createSingleProductionTracking(
            readOnlyContext,
            workOrderItem,
            trackingNumber,
            data.trackingQuantity,
            data.trackingDate || date.today(),
            data.completed || false,
            data.stockDetails,
            data.storedDimensions,
            data.storedAttributes,
        );

        const productionTrackingDoc = await productionTracking?.document;
        if (productionTrackingDoc) {
            const production: IdAndNumberType = {
                _id: productionTrackingDoc?._id,
                number: await productionTrackingDoc?.number,
            };
            result.productionTracking = production;
        }

        const finalWorkOrder = await readOnlyContext.read(xtremManufacturing.nodes.WorkOrder, { _id: workOrder._id });
        result.workOrder = finalWorkOrder;

        return result;
    }

    @decorators.mutation<typeof ProductionTracking, 'createSingleProductionTracking'>({
        isPublished: true,
        parameters: [
            {
                name: 'releasedItem',
                type: 'reference',
                node: () => xtremManufacturing.nodes.WorkOrderReleasedItem,
                isMandatory: true,
            },
            { name: 'workOrderTrackingNumber', type: 'string', isMandatory: true },
            { name: 'trackingQuantity', type: 'decimal', isMandatory: true },
            { name: 'trackingDate', type: 'date', isMandatory: false },
            { name: 'completed', type: 'boolean', isMandatory: false },
            {
                name: 'stockDetails',
                type: 'array',
                item: {
                    type: 'object',
                    isNullable: false,
                    properties: {
                        effectiveDate: {
                            type: 'string',
                            isMandatory: false,
                        },
                        site: {
                            type: 'reference',
                            isMandatory: true,
                            node: () => xtremSystem.nodes.Site,
                        },
                        item: {
                            type: 'reference',
                            isMandatory: true,
                            node: () => xtremMasterData.nodes.Item,
                        },
                        stockUnit: {
                            type: 'reference',
                            isMandatory: true,
                            node: () => xtremMasterData.nodes.UnitOfMeasure,
                        },
                        status: {
                            type: 'reference',
                            isMandatory: true,
                            node: () => xtremStockData.nodes.StockStatus,
                        },
                        location: {
                            type: 'reference',
                            isMandatory: false,
                            node: () => xtremMasterData.nodes.Location,
                        },
                        existingLot: {
                            type: 'reference',
                            isMandatory: false,
                            node: () => xtremStockData.nodes.Lot,
                        },
                        lotCreateData: {
                            type: 'string',
                            isMandatory: false,
                        },
                        owner: {
                            type: 'string',
                            isMandatory: false,
                        },
                        quantityInStockUnit: {
                            type: 'decimal',
                            isMandatory: true,
                        },
                    },
                },
            },
            { name: 'storedDimensions', type: 'string', isMandatory: false },
            { name: 'storedAttributes', type: 'string', isMandatory: false },
        ],
        return: {
            type: 'reference',
            node: () => xtremManufacturing.nodes.ProductionTrackingLine,
        },
    })
    static async createSingleProductionTracking(
        context: Context,
        releasedItem: xtremManufacturing.nodes.WorkOrderReleasedItem,
        workOrderTrackingNumber: string,
        trackingQuantity: decimal,
        // eslint-disable-next-line @typescript-eslint/default-param-last
        trackingDate: date = date.today(),
        completed: boolean,
        stockDetails: Array<NodeCreateData<xtremStockData.nodes.StockReceiptDetail>>,
        storedDimensions?: string,
        storedAttributes?: string,
    ): Promise<xtremManufacturing.nodes.ProductionTrackingLine | null> {
        if ((await (await releasedItem.document).type) !== 'firm') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-manufacturing/nodes__manufacturing_tracking__work_order_type_must_be_firm',
                    'Only firm work orders can be tracked.',
                ),
            );
        }

        if (!workOrderTrackingNumber) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-manufacturing/nodes__manufacturing_tracking__work-order-tracking-number-is-empty',
                    'Enter the work order tracking number.',
                ),
            );
        }

        // Once we declare that trackings collection is a vital one, we need to refactor this part as saving operation
        // should start at the header level
        const productionTracking = await context.runInWritableContext(async writableContext => {
            let productionTrackingHeader = await writableContext.tryRead(
                ProductionTracking,
                {
                    number: workOrderTrackingNumber,
                },
                { forUpdate: true },
            );

            if (!productionTrackingHeader) {
                productionTrackingHeader = await writableContext.create(ProductionTracking, {
                    number: workOrderTrackingNumber,
                    workOrder: await releasedItem.document,
                    entryDate: trackingDate,
                });
            }

            await productionTrackingHeader.lines.append({
                document: productionTrackingHeader,
                line: 1,
                releasedQuantity: trackingQuantity,
                workOrderLine: releasedItem,
                completed,
                // stockDetails: xtremStockData.functions.stockDetailLib.parseDetails(stockDetails),
                stockDetails: stockDetails.map(detail => {
                    return {
                        ...detail,
                        existingLot: detail.existingLot ? JSON.parse(`${detail.existingLot}`) : undefined,
                        lotCreateData: detail.lotCreateData ? JSON.parse(`${detail.lotCreateData}`) : undefined,
                    } as NodeCreateData<xtremStockData.nodes.StockReceiptDetail>;
                }),
                storedAttributes: storedAttributes ? JSON.parse(storedAttributes) : null,
                storedDimensions: storedDimensions ? JSON.parse(storedDimensions) : null,
            });
            await productionTrackingHeader.$.save();
            return productionTrackingHeader;
        });

        await ProductionTracking.postToStock(context, [productionTracking._id]);

        return productionTracking.lines.elementAt(-1);
    }

    @decorators.mutation<typeof ProductionTracking, 'createMultipleWorkOrderTrackings'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            { name: 'trackings', type: 'string', isMandatory: true },
            { name: 'trackingDate', type: 'date', isMandatory: false },
        ],
        return: {
            type: 'object',
            properties: {
                numberTrackings: 'integer',
                message: { type: 'string' },
            },
        },
    })
    static async createMultipleWorkOrderTrackings(
        readOnlyContext: Context,
        trackings: string,
        trackingDate: date,
    ): Promise<{ numberTrackings: integer; message: string }> {
        let numberTrackings = 0;
        let message = '';

        const lines: any[] = JSON.parse(trackings);
        await asyncArray(lines).forEach(async tracking => {
            const result = await ProductionTracking.createWorkOrderTracking(readOnlyContext, {
                ...tracking,
                stockDetails: JSON.parse(tracking.stockDetails),
                trackingDate,
            });
            if (result.message.length) {
                message += `${result.message}\n`;
            }
            numberTrackings += 1;
        });

        return { numberTrackings, message };
    }

    @decorators.notificationListener<typeof ProductionTracking>({
        topic: 'ProductionTracking/accountingInterface',
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }

    @decorators.mutation<typeof ProductionTracking, 'postToStock'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: {
            type: 'string',
        },
    })
    static postToStock(context: Context, documentIds: integer[]): Promise<string> {
        return ProductionTracking.sendStockNotification(context, documentIds, false);
    }

    static postCorrectionsToStock(context: Context, documentIds: integer[]): Promise<string> {
        return ProductionTracking.sendStockNotification(context, documentIds, true);
    }

    private static sendStockNotification(
        context: Context,
        documentIds: integer[],
        forceNotification: boolean,
    ): Promise<string> {
        return context.runInWritableContext(writableContext =>
            xtremStockData.functions.notificationLib.stockReceiptRequestNotification(writableContext, {
                documentClass: ProductionTracking,
                documentIds,
                additionalSteps: [{ movementType: 'correction' as xtremStockData.enums.StockMovementType }],
                forceNotificationEvenIfCompleted: forceNotification,
            }),
        );
    }

    @decorators.notificationListener<typeof ProductionTracking>({
        startsReadOnly: true,
        topic: 'ProductionTracking/stock/receipt/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.receipt>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(
                context,
                envelope,
                error,
                xtremManufacturing.nodes.ProductionTracking,
            );
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.receipt>,
    ): Promise<void> {
        const receiptUpdateResult = await readOnlyContext.runInWritableContext(
            async writableContext =>
                (
                    await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                        writableContext,
                        payload,
                        ProductionTracking,
                    )
                ).receipt,
        );
        if (receiptUpdateResult && !receiptUpdateResult.transactionHadAnError) {
            await receiptUpdateResult.documents.forEach(async document => {
                await readOnlyContext.runInWritableContext(async writableContext => {
                    const productionTracking = await writableContext.read(ProductionTracking, { _id: document.id });
                    await xtremManufacturing.functions.productionTrackingLib.updateAfterStockSuccess(writableContext, {
                        tracking: productionTracking,
                        // As all lines are completed, all lines must have a WIPcost.
                        // In some cases where synchronization has been processed, the WIPcosts records
                        // were not created => here we have to consider them
                        lines: productionTracking.lines.filter(
                            async productionTrackingLine =>
                                (await productionTrackingLine.workInProgressCosts.length) === 0,
                        ),
                        isStockDocumentCompleted: document.isStockDocumentCompleted,
                    });
                });
            });
        }
    }

    static async onceStockCompleted(_context: Context, productionTracking: ProductionTracking): Promise<void> {
        await productionTracking.sendFinanceNotification();
    }

    @decorators.mutation<typeof ProductionTracking, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [
            { name: 'productionTracking', type: 'reference', isMandatory: true, node: () => ProductionTracking },
        ],
        return: { type: 'boolean' },
    })
    static async resendNotificationForFinance(
        context: Context,
        productionTracking: ProductionTracking,
    ): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-manufacturing/node__production_tracking__resend_notification_for_finance',
                'Resending finance notification for production tracking: {{productionTracking}}.',
                { productionTrackingNumber: await productionTracking.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await (await productionTracking.workOrder).number,
                documentType: 'workInProgress',
                sourceDocumentNumber: await productionTracking.number,
                sourceDocumentType: 'productionTracking',
            })
        ) {
            await productionTracking.sendFinanceNotification();
        }

        return true;
    }

    @decorators.notificationListener<typeof ProductionTracking>({
        topic: 'ProductionTracking/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const productionTracking = await context.read(ProductionTracking, { number: document.number });

        await ProductionTracking.resendNotificationForFinance(context, productionTracking);
    }

    async sendFinanceNotification(): Promise<void> {
        // Send notification in order to create staging table entries for the accounting engine
        // This notification must be done only once when the production tracking is posted.
        // When the production tracking is posted again for corrections, the notification is sent by the WO (via stockCorrectionReplyToOrigin).
        const financeTransactions = await this.$.context.select(
            xtremFinanceData.nodes.FinanceTransaction,
            { _id: true },
            {
                filter: {
                    documentNumber: await this.number,
                    documentType: 'purchaseReceipt' as xtremFinanceData.enums.FinanceDocumentType, // TODO: update document type (source document type)
                },
            },
        );
        if (financeTransactions.length === 0 && (await (await (await this.site).legalCompany).doWipPosting)) {
            // loop over the lines and create a notification for each work order
            let lines: xtremFinanceData.interfaces.WorkInProgressFinanceDocumentLine[] = [];
            let workOrderNumber = '';
            await asyncArray(await this.lines.toArray()).forEach(
                async (line: xtremManufacturing.nodes.ProductionTrackingLine) => {
                    // if we have a change of work order => send notification and empty the lines array for the next notification
                    if (workOrderNumber !== '' && workOrderNumber !== (await line.workInProgressWorkOrderNumber)) {
                        await xtremFinanceData.functions.ManufacturingNotificationLib.productionTrackingNotification(
                            this.$.context,
                            this as xtremFinanceData.interfaces.FinanceOriginDocument,
                            lines,
                        );
                        lines = [];
                    }
                    workOrderNumber = await line.workInProgressWorkOrderNumber; // remember the work order number
                    lines.push(line as xtremFinanceData.interfaces.WorkInProgressFinanceDocumentLine);
                },
            );
            // send last notification
            if (lines.length > 0) {
                await xtremFinanceData.functions.ManufacturingNotificationLib.productionTrackingNotification(
                    this.$.context,
                    this as xtremFinanceData.interfaces.FinanceOriginDocument,
                    lines,
                );
            }
        }
    }

    /**
     * If the stock reply has been interrupted, the stock has been updated but the WIP cost has not been created.
     * This function will update the work order, create the WIP costs and call the finance notification
     */
    async resynchronizeStatus() {
        await xtremManufacturing.functions.trackingLib.resynchronizeStatus(
            this.$.context,
            this,
            xtremManufacturing.functions.productionTrackingLib.updateAfterStockSuccess,
        );
    }

    @decorators.asyncMutation<typeof ProductionTracking, 'createTestTracking'>({
        isPublished: true,
        startsReadOnly: false,
        serviceOptions: () => [xtremSystem.serviceOptions.DevTools],
        parameters: [
            {
                name: 'orderNumberRoot',
                type: 'string',
                isMandatory: false,
            },
            {
                name: 'quantityRatio',
                type: 'decimal',
            },
        ],
        return: {
            type: 'string',
        },
    })
    static createTestTracking(context: Context, orderNumberRoot = '', quantityRatio: decimal = 1) {
        return xtremManufacturing.functions.createTestProductionTracking(context, orderNumberRoot, quantityRatio);
    }
}
