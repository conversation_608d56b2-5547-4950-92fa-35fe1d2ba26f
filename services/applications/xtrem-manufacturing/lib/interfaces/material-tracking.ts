import type { date, decimal, Reference } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremManufacturing from '../../index';

export type AllocationUpdate = Omit<
    xtremStockData.interfaces.DataForUpdateAllocationMutationActions,
    'action' | 'stockRecord' | 'allocationRecord'
> & {
    action: xtremManufacturing.enums.ManufacturingAllocationUpdateAction;
    stockRecord?: Reference<xtremStockData.nodes.Stock> | xtremStockData.nodes.Stock;
    allocationRecord?: Reference<xtremStockData.nodes.StockAllocation> | xtremStockData.nodes.StockAllocation;
};

type AllocationUpdates = Array<AllocationUpdate>;

export interface SingleMaterialTracking {
    /** component to track */
    component: xtremManufacturing.nodes.WorkOrderComponent;
    /** tracking number, if not define a new tracking number will be created */
    workOrderTrackingNumber?: string; // MaterialTracking.number

    quantity: decimal;
    date: date;

    /** Finance  */
    storedDimensions?: string;
    /** Finance  */
    storedAttributes?: string;

    isCompleted: boolean;
    /** Launch the post if true  */
    isStandaloneLine: boolean;

    /** Allocation updates */
    allocationUpdates?: AllocationUpdates;

    /** Will allow all available stock if automated */
    isAutomated?: true;

    /** Will allow the creation even for serial number managed items */
    isMaterialTrackingCreationAllowedForSerialNumberManagedItems?: false;
}

export interface MultipleMaterialTracking {
    /** WorkOrder of the tracking  */
    workOrder: xtremManufacturing.nodes.WorkOrder;
    /** Component to track, if no component the item is added on the workOrder.components collection */
    componentNumber?: number;

    item: xtremMasterData.nodes.Item;
    name: string;
    /** Tracking date */
    date: date;
    unit: xtremMasterData.nodes.UnitOfMeasure;

    remainingQuantity: decimal;

    storedDimensions?: string;
    storedAttributes?: string;

    isCompleted: boolean;

    /** Allocation updates */
    allocationUpdates?: AllocationUpdates;

    /** Will allow all available stock if automated */
    isAutomated?: true;
}
