import type { decimal } from '@sage/xtrem-client';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type { integer } from '@sage/xtrem-shared';
import type * as xtremManufacturing from '../index';

export interface TestBomRoutingObject {
    _id: number;
    item: {
        _id: number;
        id: string;
        name: string;
    };
    site: {
        _id: number;
        id: string;
        name: string;
    };
}

export interface IdAndNumberType {
    _id: decimal | null;
    number: string | null;
}

export interface WorkOrderTrackingReturnType {
    message: string;
    workOrder: xtremManufacturing.nodes.WorkOrder | null;
    materialTracking: xtremManufacturing.nodes.MaterialTracking | null;
    productionTracking: IdAndNumberType | null;
    timeTracking: IdAndNumberType | null;
}

export interface ComponentShortageInfo {
    _id: string;
    itemId: string;
    itemDescription: string;
    itemName: string;
    componentName: string;
    available: decimal;
    required: decimal;
    shortage?: decimal;
    unit: string;
    componentNumber: integer;
}

export interface UpdateBomTrackingStatusesArgs {
    number: string;
    workOrderStatus: string;
    stockStatus: string;
}

export interface FinanceOperationResource {
    baseDocumentLineSysId: number;
    workOrderOperationSysId: number;
    resourceSysId: number;
    storedAttributes: xtremMasterData.interfaces.StoredAttributes;
    storedDimensions: object;
}
