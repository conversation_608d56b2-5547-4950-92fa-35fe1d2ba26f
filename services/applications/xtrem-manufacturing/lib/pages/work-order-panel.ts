import { extractEdges, type Filter, type OperationParamType } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import { initDefaultDimensionsOrderToOrder } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import type { GraphApi, WorkOrder$Operations, WorkOrderCategory, WorkOrderType } from '@sage/xtrem-manufacturing-api';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import { transformServerError } from '@sage/xtrem-structure/build/lib/client-functions/error-message-format';
import type { Site } from '@sage/xtrem-system-api';
import type { BillOfMaterial } from '@sage/xtrem-technical-data-api';
import * as ui from '@sage/xtrem-ui';
import * as WorkOrderFunctions from '../client-functions/work-order';

@ui.decorators.page<WorkOrderPanel>({
    title: 'New work order',
    mode: 'default',
    node: '@sage/xtrem-manufacturing/WorkOrder',
    module: 'xtrem-manufacturing',

    businessActions() {
        return [this.cancelWorkOrder, this.createWorkOrder];
    },
    async onLoad() {
        if (this.$.queryParameters.recommendation) {
            const recommendation = JSON.parse(this.$.queryParameters.recommendation as string);
            this.reorder = true;
            this.bomCode.value = recommendation.item;
            this.workOrderSite.value = recommendation.site;
            this.workOrderQuantity.value = recommendation.quantity;
            this.workOrderStartDate.value = recommendation.startDate;
            this.workOrderEndDate.value = recommendation.endDate;
            this.workOrderName.value = recommendation.item.name;
            this.workOrderType.value = 'planned';
            this.isFromSalesOrder = recommendation.isFromSalesOrder ? recommendation.isFromSalesOrder : false;
            if (
                this.isFromSalesOrder === true &&
                (recommendation.storedDimensions || recommendation.storedAttributes)
            ) {
                this.storedDimensions = recommendation.storedDimensions ?? '{}';
                this.storedAttributes = recommendation.storedAttributes ?? '{}';
            }
            if (!this.category.isHidden) {
                const categoryFilter: Filter<WorkOrderCategory> = { isDefault: true };

                const categories = extractEdges(
                    await this.$.graph
                        .node('@sage/xtrem-manufacturing/WorkOrderCategory')
                        .query(
                            ui.queryUtils.edgesSelector(
                                {
                                    _id: true,
                                    name: true,
                                    id: true,
                                    routing: true,
                                    billOfMaterial: true,
                                },
                                {
                                    filter: categoryFilter,
                                    orderBy: { routing: -1 },
                                },
                            ),
                        )
                        .execute(),
                );
                if (categories.length > 0) {
                    [this.category.value] = categories;
                }
            }
        } else {
            this.reorder = false;
            this.workOrderStartDate.value = DateValue.today().toString();
            await setReferenceIfSingleValue([this.workOrderSite, this.category]);
        }
    },
    onError(error) {
        this.$.loader.isHidden = true;
        const errorMessage = transformServerError(error);
        this.$.showToast(errorMessage, { timeout: 0, type: 'error' });
    },
})
export class WorkOrderPanel extends ui.Page<GraphApi> {
    reorder = false;

    isFromSalesOrder = false;

    storedDimensions = '{}';

    storedAttributes = '{}';

    @ui.decorators.pageAction<WorkOrderPanel>({
        title: 'Create',
        async onClick() {
            const message = await this.$.page.validate();
            if (message.length > 0) {
                this.$.showToast(message.join(', '), { timeout: 0, type: 'error' });
                return;
            }
            this.$.loader.isHidden = false;
            // if we come from a sales order, we need to get the correct attributes and dimensions for the WO
            // following the rules defined on the company for manufacturingOrderToOrder
            let { storedAttributes } = this;
            let { storedDimensions } = this;
            if (this.isFromSalesOrder) {
                const attributesDimensions = await initDefaultDimensionsOrderToOrder({
                    page: this,
                    dimensionDefinitionLevel: 'manufacturingOrderToOrder',
                    site: this.workOrderSite.value,
                    item: this.bomCode.value?.item,
                    storedAttributes,
                    storedDimensions,
                });
                storedAttributes = attributesDimensions.attributes;
                storedDimensions = attributesDimensions.dimensions;
            }

            const createParams: OperationParamType<WorkOrder$Operations['mutations']['createWorkOrder']> = {
                data: {
                    workOrderNumber: this.workOrderNumber.value ?? '',
                    startDate: this.workOrderStartDate.value ?? '',
                    releasedQuantity: Number(this.workOrderQuantity.value ?? 0),
                    siteId: this.workOrderSite.value?.id ?? '',
                    releasedItem: this.bomCode.value?.item?.id ?? '',
                    workOrderCategory: this.category.value?._id ?? '',
                    type: this.workOrderType.value as WorkOrderType,
                    name: this.workOrderName.value ?? '',
                    bom: this.category.value?.billOfMaterial ? this.bomCode.value?.item?.id : '',
                    route: this.category.value?.routing ? this.bomCode.value?.item?.id : '',
                    storedDimensions,
                    storedAttributes,
                    _customData: this.$.values._customData ?? '{}', // since we are not using the default create mutation on the work order panel, we have to pass this information, if it exists
                },
            };
            // if we do not come from a sales order, we may not pass any dimensions, because they should be defaulted on WO node
            if (!this.isFromSalesOrder) {
                delete createParams.data.storedAttributes;
                delete createParams.data.storedDimensions;
            }
            const workOrderCreated = await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrder')
                .mutations.createWorkOrder(
                    {
                        _id: true,
                        number: true,
                        name: true,
                        productionItems: {
                            query: {
                                edges: { node: { _id: true, releasedQuantity: true, workInProgress: { _id: true } } },
                            },
                        },
                    },
                    createParams,
                )
                .execute();
            this.$.loader.isHidden = true;
            this.$.finish(workOrderCreated);
        },
    })
    createWorkOrder: ui.PageAction;

    @ui.decorators.pageAction<WorkOrderPanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancelWorkOrder: ui.PageAction;

    @ui.decorators.section<WorkOrderPanel>({})
    workOrderSection: ui.containers.Section;

    @ui.decorators.block<WorkOrderPanel>({
        parent() {
            return this.workOrderSection;
        },
    })
    workOrderBlock: ui.containers.Block;

    @ui.decorators.referenceField<WorkOrderPanel, Site>({
        parent() {
            return this.workOrderBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        fetchesDefaults: true,
        helperTextField: 'id',
        isMandatory: true,
        bind: 'site',
        isDisabled() {
            return this.reorder;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'isManufacturing', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
        ],
        filter: { isManufacturing: true, isActive: true },
    })
    workOrderSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<WorkOrderPanel, BillOfMaterial>({
        parent() {
            return this.workOrderBlock;
        },
        title: 'Released item',
        bind: 'bomCode',
        tunnelPage: '@sage/xtrem-technical-data/BillOfMaterial',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        fetchesDefaults: true,
        helperTextField: { item: { id: true } },
        isMandatory: true,
        isDisabled() {
            return this.reorder;
        },
        columns: [
            ui.nestedFields.technical({ bind: 'status' }),
            ui.nestedFields.reference<WorkOrderPanel, BillOfMaterial, BillOfMaterial['item']>({
                title: 'Name',
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                columns: [
                    ui.nestedFields.technical({
                        bind: 'stockUnit',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.text({ bind: { item: { id: true } } }),
            ui.nestedFields.text({ bind: { item: { description: true } } }),
            ui.nestedFields.text({ title: 'Category', bind: { item: { category: { name: true } } } }),
        ],
        filter() {
            return {
                ...(this.workOrderSite.value ? { site: { _id: { _eq: this.workOrderSite.value._id } } } : {}),
                ...(this.$.isServiceOptionEnabled('phantomItemOption') ? { item: { isPhantom: false } } : {}),
            };
        },
        onChange() {
            if (this.bomCode.value?.item) {
                this.workOrderQuantity.scale = this.bomCode.value.item.stockUnit?.decimalDigits;
                this.workOrderQuantity.postfix = this.bomCode.value.item.stockUnit?.symbol;
                this.workOrderName.value = this.bomCode.value.name ?? '';
            }
        },
    })
    bomCode: ui.fields.Reference<BillOfMaterial>;

    @ui.decorators.referenceField<WorkOrderPanel, WorkOrderCategory>({
        parent() {
            return this.workOrderBlock;
        },
        node: '@sage/xtrem-manufacturing/WorkOrderCategory',
        title: 'Category',
        fetchesDefaults: true,
        isAutoSelectEnabled: true,
        lookupDialogTitle: 'Select category',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        bind: 'category',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'routing' }),
            ui.nestedFields.checkbox({ bind: 'billOfMaterial' }),
        ],
        async validation(category) {
            let control;
            if (category && this.bomCode.value && this.workOrderSite.value) {
                if (category.routing) {
                    control = await WorkOrderFunctions.categoryControlRouting(
                        this,
                        this.bomCode.value?.item?._id ?? '',
                        this.workOrderSite.value?._id ?? '',
                    );
                }
                if (category.billOfMaterial) {
                    control = await WorkOrderFunctions.categoryControlBillOfMaterial(
                        this,
                        this.bomCode.value?.item?._id ?? '',
                        this.workOrderSite.value?._id ?? '',
                    );
                }
            }
            return control;
        },
    })
    category: ui.fields.Reference<WorkOrderCategory>;

    @ui.decorators.dropdownListField<WorkOrderPanel>({
        title: 'Type',
        bind: 'type',
        isTransientInput: true, // => the value is not initialized and must be entered by the user
        isMandatory: true,
        parent() {
            return this.workOrderBlock;
        },
        optionType: '@sage/xtrem-manufacturing/WorkOrderType',
    })
    workOrderType: ui.fields.DropdownList;

    @ui.decorators.numericField<WorkOrderPanel>({
        title: 'Quantity',
        isTransient: true,
        isMandatory: true,
        parent() {
            return this.workOrderBlock;
        },
    })
    workOrderQuantity: ui.fields.Numeric;

    @ui.decorators.dateField<WorkOrderPanel>({
        title: 'Requested start date',
        bind: 'startDate',
        isMandatory: true,
        parent() {
            return this.workOrderBlock;
        },
        warningMessage() {
            return this.workOrderStartDate.value
                ? WorkOrderFunctions.requestDateBeforeTodayWarning(
                      WorkOrderFunctions.convertStringToJsDate(this.workOrderStartDate.value),
                  )
                : '';
        },
    })
    workOrderStartDate: ui.fields.Date;

    @ui.decorators.dateField<WorkOrderPanel>({
        title: 'End date',
        isTransient: true,
        isDisabled() {
            return this.reorder;
        },
        isHidden() {
            return !this.reorder;
        },
        parent() {
            return this.workOrderBlock;
        },
        minDate() {
            return this.workOrderStartDate.value || undefined;
        },
    })
    workOrderEndDate: ui.fields.Date;

    @ui.decorators.textField<WorkOrderPanel>({
        title: 'Name',
        bind: 'name',
        parent() {
            return this.workOrderBlock;
        },
    })
    workOrderName: ui.fields.Text;

    @ui.decorators.textField<WorkOrderPanel>({
        title: 'Work order number',
        bind: 'number',
        parent() {
            return this.workOrderBlock;
        },
    })
    workOrderNumber: ui.fields.Text;

    @ui.decorators.separatorField<WorkOrderPanel>({
        parent() {
            return this.workOrderBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    statusSeparator: ui.fields.Separator;

    @ui.decorators.buttonField<WorkOrderPanel>({
        parent() {
            return this.workOrderBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-manufacturing/pages__work_order_panel__projected_stock_button_text',
                'Projected stock',
            );
        },
        width: 'small',
        isTransient: true,
        async onClick() {
            if (this.bomCode.value) {
                await this.$.dialog.page(
                    '@sage/xtrem-stock-data/ProjectedStockDialog',
                    {
                        item: JSON.stringify(this.bomCode.value.item),
                        site: JSON.stringify(this.workOrderSite.value),
                        shipmentDate: this.workOrderEndDate.value ?? '',
                    },
                    {
                        rightAligned: false,
                        size: 'extra-large',
                        resolveOnCancel: true,
                    },
                );
            }
        },
        isHidden() {
            return !this.isFromSalesOrder;
        },
    })
    projectedStock: ui.fields.Button;
}
