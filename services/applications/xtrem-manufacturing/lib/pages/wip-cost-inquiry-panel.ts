import { extractEdges } from '@sage/xtrem-client';
import type { GraphApi, WorkInProgressCost } from '@sage/xtrem-manufacturing-api';
import * as DocumentLink from '@sage/xtrem-stock-data/build/lib/client-functions/document-link';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<WipCostInquiryPanel>({
    title: 'Tracking costs',
    module: 'manufacturing',
    mode: 'default',
    isTransient: true,
    access: { node: '@sage/xtrem-manufacturing/WorkInProgressCost' },
    async onLoad() {
        if (this.$.queryParameters) {
            await this.loadCosts((this.$.queryParameters.trackingLineId as string) || '');
        }
    },
})
export class WipCostInquiryPanel extends ui.Page<GraphApi> {
    @ui.decorators.section<WipCostInquiryPanel>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WipCostInquiryPanel>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Tracking costs',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.tableField<WipCostInquiryPanel>({
        title: 'Work in progress costs',
        isHelperTextHidden: true,
        isTransient: true,
        isReadOnly: true,
        canSelect: false,
        canExport: true,
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.reference({
                node: '@sage/xtrem-manufacturing/WorkOrder',
                bind: 'workOrder',
                title: 'Work order',
                valueField: 'number',
                size: 'small',
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'Number', bind: 'number' }),
                    ui.nestedFields.reference({
                        bind: 'site',
                        valueField: 'id',
                        isHidden: true,
                        node: '@sage/xtrem-system/Site',
                        columns: [
                            ui.nestedFields.text({
                                bind: 'name',
                            }),
                            ui.nestedFields.text({
                                bind: 'id',
                            }),
                            ui.nestedFields.checkbox({
                                bind: 'isLocationManaged',
                            }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                size: 'large',
            }),
            ui.nestedFields.text({
                bind: 'releasedItemId',
                title: 'Released item ID',
                size: 'small',
            }),
            ui.nestedFields.select({
                bind: 'type',
                title: 'Type',
                optionType: '@sage/xtrem-manufacturing/WorkInProgressType',
                size: 'small',
            }),
            ui.nestedFields.link({
                isFullWidth: true,
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                title: 'Document',
                map(_value, rowValue) {
                    return `${rowValue?.originatingLine?.documentNumber}`;
                },
                onClick(_id, data: ui.PartialCollectionValue<WorkInProgressCost>) {
                    const page = DocumentLink.getDocumentPageName(data?.originatingLine?._constructor || '');
                    return this.$.dialog.page(
                        page,
                        { _id: data?.originatingLine?.documentId || '' },
                        {
                            fullScreen: true,
                            resolveOnCancel: true,
                        },
                    );
                },
            }),
            ui.nestedFields.date({
                bind: 'effectiveDate',
                title: 'Date',
                size: 'small',
            }),
            ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Quantity',
                size: 'small',
                unit: (_val, rowData) => rowData?.unit,
                unitMode: 'unitOfMeasure',
                scale: null,
            }),
            ui.nestedFields.numeric({
                bind: 'cost',
                title: 'Cost',
                size: 'small',
                scale: 4,
                unit: (_val, rowData) => rowData?.currency,
            }),
            ui.nestedFields.numeric({
                bind: 'amount',
                title: 'Amount',
                size: 'small',
                unit: (_val, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'unit',
                title: 'Unit',
                valueField: 'symbol',
                isHiddenOnMainField: true,
                size: 'small',
                columns: [
                    ui.nestedFields.text({ bind: 'name', isHidden: true }),
                    ui.nestedFields.text({ bind: 'id', isHidden: true }),
                    ui.nestedFields.text({ bind: 'symbol', isHidden: true }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits', isHidden: true }),
                ],
            }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-master-data/Currency',
                bind: 'currency',
                title: 'Currency',
                valueField: 'id',
                isHiddenOnMainField: true,
                minLookupCharacters: 1,
                size: 'small',
                columns: [
                    ui.nestedFields.text({ bind: 'name', isHidden: true }),
                    ui.nestedFields.text({ bind: 'id', isHidden: true }),
                    ui.nestedFields.text({ bind: 'symbol', isHidden: true }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits', isHidden: true }),
                ],
            }),
        ],
    })
    costs: ui.fields.Table;

    async loadCosts(trackingLineId: string) {
        this.costs.value = [];

        const results = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkInProgressCost')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            type: true,
                            quantity: true,
                            cost: true,
                            amount: true,
                            status: true,
                            effectiveDate: true,
                            workOrder: {
                                _id: true,
                                number: true,
                                name: true,
                                startDate: true,
                                site: {
                                    _id: true,
                                    id: true,
                                    name: true,
                                    isLocationManaged: true,
                                },
                                productionItem: {
                                    releasedItem: {
                                        id: true,
                                    },
                                },
                            },
                            unit: {
                                _id: true,
                                id: true,
                                symbol: true,
                                decimalDigits: true,
                                name: true,
                            },
                            currency: {
                                _id: true,
                                id: true,
                                symbol: true,
                                name: true,
                                decimalDigits: true,
                            },
                            originatingLine: {
                                _constructor: true,
                                documentNumber: true,
                                documentId: true,
                            },
                        },
                        {
                            filter: {
                                originatingLine: { _id: trackingLineId },
                            },
                            first: 500,
                        },
                    ),
                )
                .execute(),
        );

        this.costs.value = results.map(value => ({
            ...value,
            number: value.workOrder.number,
            name: value.workOrder.name,
            releasedItemId: value.workOrder.productionItem.releasedItem.id,
            quantity: value.quantity,
            cost: value.cost,
            amount: value.amount,
            unitName: value.unit.name,
            type: value.type,
            unit: value.unit,
            currency: value.currency,
        }));

        this.$.setPageClean();
        this.$.loader.isHidden = true;
    }
}
