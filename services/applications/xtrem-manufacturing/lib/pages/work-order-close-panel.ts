import { date } from '@sage/xtrem-date-time';
import type { GraphApi } from '@sage/xtrem-technical-data-api';
import * as ui from '@sage/xtrem-ui';
import { isEmpty } from 'lodash';

/** Panel for closing a work order.
 * /!\ Can be call from main list & from the page of a work order.
 *  When called from main list
 *      this.$.queryParameters
 *  To be able to control the closing date before we need to know the work order filter
 * When called from the page of a work order
 *  We return the closing date
 */

@ui.decorators.page<WorkOrderClosePanel>({
    title: 'Work order closing',
    mode: 'default',
    businessActions() {
        return [this.$standardCancelAction, this.$standardDialogConfirmationAction, this.generate];
    },
    onLoad() {
        this.sysId = this.$.queryParameters._id?.toString() || '';
        this.closingDate.value = date.today().toString();
        this.generate.isHidden = this.sysId === '';
        this.$standardDialogConfirmationAction.isHidden = this.sysId !== '';
        this.$standardDialogConfirmationAction.title = ui.localize(
            '@sage/xtrem-manufacturing/generate-close-work-order',
            'Generate',
        );
    },
})
export class WorkOrderClosePanel extends ui.Page<GraphApi> {
    sysId: string;

    message: string;

    @ui.decorators.section<WorkOrderClosePanel>({ isTitleHidden: true })
    section: ui.containers.Section;

    @ui.decorators.block<WorkOrderClosePanel>({
        isTitleHidden: true,
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.dateField<WorkOrderClosePanel>({
        parent() {
            return this.block;
        },
        title: 'Closing date',
        width: 'small',
        onChange() {
            this.$standardDialogConfirmationAction.isDisabled = false;
            this.generate.isDisabled = false;
        },
        validation(this, value) {
            return this.validateClosingDate(value);
        },
    })
    closingDate: ui.fields.Date;

    @ui.decorators.pageAction<WorkOrderClosePanel>({
        title: 'Generate',
        async onClick() {
            const validation = await this.$.page.validate();
            if (isEmpty(validation)) {
                this.$.finish(this.closingDate.value);
            }
        },
    })
    generate: ui.PageAction;

    async validateClosingDate(closingDate: string) {
        const workOrder = this.sysId ? { workOrder: this.sysId } : undefined;
        const message = await this.$.graph
            .node('@sage/xtrem-manufacturing/WorkOrder')
            .mutations.controlClosingDate(true, { ...workOrder, closingDate })
            .execute();
        if (message.length) {
            this.$standardDialogConfirmationAction.isDisabled = true;
            this.generate.isDisabled = true;
            return message;
        }
        this.$standardDialogConfirmationAction.isDisabled = false;
        this.generate.isDisabled = false;
        return message.length ? message : undefined;
    }
}
