import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<WorkOrderDelaySettings>({
    title: 'Work order delay settings',
    isTransient: true,
    onLoad() {
        if (this.$.queryParameters.delay) {
            this.delay.value = JSON.parse(this.$.queryParameters.delay as string);
        }
    },
    businessActions() {
        return [this.save];
    },
})
export class WorkOrderDelaySettings extends ui.Page {
    @ui.decorators.section<WorkOrderDelaySettings>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<WorkOrderDelaySettings>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.numericField<WorkOrderDelaySettings>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Delay days',
        isMandatory: true,
        min: 0,
        max: 21,
        scale: 0,
    })
    delay: ui.fields.Numeric;

    @ui.decorators.pageAction<WorkOrderDelaySettings>({
        title: 'Save settings',
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                this.$.finish({
                    delay: JSON.stringify(this.delay.value),
                });
            }
        },
    })
    save: ui.PageAction;
}
