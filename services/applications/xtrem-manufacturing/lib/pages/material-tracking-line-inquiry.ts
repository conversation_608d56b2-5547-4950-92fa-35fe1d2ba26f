import type {
    GraphApi,
    MaterialTracking,
    MaterialTrackingLine as MaterialTrackingLineNode,
} from '@sage/xtrem-manufacturing-api';
import type { Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as PillColor from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { manufacturingInquiries } from '../menu-items/manufacturing-inquiries';

@ui.decorators.page<MaterialTrackingLineInquiry, MaterialTrackingLineNode>({
    menuItem: manufacturingInquiries,
    priority: 105,
    title: 'Material tracking line inquiry',
    module: 'manufacturing',
    mode: 'default',
    node: '@sage/xtrem-manufacturing/MaterialTrackingLine',
    access: { node: '@sage/xtrem-manufacturing/MaterialTracking', bind: '$read' },
    navigationPanel: {
        onSelect() {
            return true;
        },
        dropdownActions: [
            {
                icon: 'coins',
                title: 'Costs',
                async onClick(rowID) {
                    await this.$.dialog.page(
                        '@sage/xtrem-manufacturing/WipCostInquiryPanel',
                        {
                            trackingLineId: rowID,
                        },
                        { size: 'extra-large' },
                    );
                },
            },
        ],
        listItem: {
            title: ui.nestedFields.text({
                title: 'Stored dimensions',
                isHidden: true,
                bind: 'storedDimensions',
            }),
            trackingNumberLink: ui.nestedFields.reference<
                MaterialTrackingLineInquiry,
                MaterialTrackingLineNode,
                MaterialTracking
            >({
                title: 'Material tracking link',
                bind: 'document',
                valueField: 'number',
                tunnelPage: '@sage/xtrem-manufacturing/MaterialTrackingInquiry',
            }),
            trackingDate: ui.nestedFields.date({
                title: 'Tracking date',
                bind: { document: { effectiveDate: true } },
                isHiddenOnMainField: true,
            }),
            trackingNumber: ui.nestedFields.reference<
                MaterialTrackingLineInquiry,
                MaterialTrackingLineNode,
                MaterialTracking
            >({
                title: 'Material tracking',
                valueField: 'number',
                bind: 'document',
                node: '@sage/xtrem-manufacturing/MaterialTracking',
                width: 'medium',
                isHiddenOnMainField: true,
            }),
            workOrderStatus: ui.nestedFields.label({
                title: 'Work order status',
                optionType: '@sage/xtrem-manufacturing/WorkOrderStatus',
                bind: { workOrderLine: { workOrder: { status: true } } },
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColor.getLabelColorByStatus('WorkOrderStatus', rowData.workOrderLine?.workOrder?.status as any),
            }),
            workOrder: ui.nestedFields.reference<
                MaterialTrackingLineInquiry,
                MaterialTrackingLineNode,
                MaterialTracking
            >({
                title: 'Work order',
                valueField: 'number',
                bind: { workOrderLine: { workOrder: true } },
                node: '@sage/xtrem-manufacturing/WorkOrder',
                tunnelPage: '@sage/xtrem-manufacturing/WorkOrder',
            }),
            bomId: ui.nestedFields.text({
                title: 'BOM ID',
                bind: { workOrderLine: { workOrder: { bomCode: { item: { id: true } } } } },
                width: 'medium',
            }),
            bom: ui.nestedFields.reference({
                bind: { workOrderLine: { workOrder: { bomCode: true } } },
                title: 'Bill of material',
                width: 'large',
                node: '@sage/xtrem-technical-data/BillOfMaterial',
                tunnelPage: '@sage/xtrem-technical-data/BillOfMaterial',
                lookupDialogTitle: 'Select bill of material',
                valueField: 'name',
                helperTextField: { item: { name: true } },
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.reference({
                        title: 'Item',
                        bind: 'item',
                        valueField: 'name',
                        node: '@sage/xtrem-master-data/Item',
                        tunnelPage: '@sage/xtrem-master-data/Item',
                    }),
                ],
            }),
            site: ui.nestedFields.reference<MaterialTrackingLineInquiry, MaterialTrackingLineNode, Site>({
                bind: { document: { site: true } },
                title: 'Site name',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
            }),
            siteId: ui.nestedFields.text({
                title: 'Site ID',
                bind: { document: { site: { id: true } } },
                isHiddenOnMainField: true,
            }),
            componentNumber: ui.nestedFields.numeric({
                title: 'Component number',
                bind: { workOrderLine: { componentNumber: true } },
                width: 'medium',
            }),
            itemName: ui.nestedFields.reference<MaterialTrackingLineInquiry, MaterialTrackingLineNode, Item>({
                title: 'Item',
                valueField: 'name',
                bind: { workOrderLine: { item: true } },
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                width: 'medium',
            }),
            itemId: ui.nestedFields.text({
                title: 'Item ID',
                bind: { workOrderLine: { item: { id: true } } },
                width: 'medium',
            }),
            itemDescription: ui.nestedFields.text({
                title: 'Item description',
                bind: { workOrderLine: { item: { description: true } } },
                width: 'medium',
                isHiddenOnMainField: true,
            }),
            componentStatus: ui.nestedFields.label({
                title: 'Status',
                bind: { workOrderLine: { lineStatus: true } },
                optionType: '@sage/xtrem-manufacturing/ComponentStatus',
                style: (_id, rowValue) =>
                    PillColor.getLabelColorByStatus('ComponentStatus', rowValue.workOrderLine?.lineStatus as any),
            }),
            stockStatus: ui.nestedFields.label({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowValue) =>
                    PillColor.getLabelColorByStatus('StockDocumentTransactionStatus', rowValue.stockTransactionStatus),
                async onClick(_id, data) {
                    await StockDataUtils.onStockTransactionStatusClick(this, data.stockTransactionStatus, {
                        origin: 'line',
                        _id,
                    });
                },
            }),
            quantity: ui.nestedFields.numeric({
                title: 'Quantity',
                bind: 'quantityInStockUnit',
                scale(_val, rowData) {
                    return rowData?.workOrderLine?.item?.stockUnit?.decimalDigits || 0;
                },
                postfix(_val, rowData) {
                    return rowData?.workOrderLine?.item?.stockUnit?.symbol;
                },
            }),
            completed: ui.nestedFields.checkbox({
                title: 'Completed',
                bind: 'completed',
            }),
            stockUnit: ui.nestedFields.reference<MaterialTrackingLineInquiry, MaterialTrackingLineNode, UnitOfMeasure>({
                title: 'Unit',
                valueField: 'name',
                bind: { workOrderLine: { item: { stockUnit: true } } },
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            computedAttributes: ui.nestedFields.technical({
                bind: 'computedAttributes',
            }),
            storedAttributes: ui.nestedFields.technical({
                bind: 'storedAttributes',
            }),
            workOrderLine: ui.nestedFields.reference({
                valueField: '_id',
                bind: 'workOrderLine',
                node: '@sage/xtrem-manufacturing/WorkOrderComponent',
                isHidden: true,
                columns: [
                    ui.nestedFields.reference({
                        valueField: 'id',
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'description' }),
                            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                            ui.nestedFields.reference<MaterialTrackingLineInquiry, Item, UnitOfMeasure>({
                                valueField: 'name',
                                bind: 'stockUnit',
                                node: '@sage/xtrem-master-data/UnitOfMeasure',
                                columns: [
                                    ui.nestedFields.text({ bind: 'name' }),
                                    ui.nestedFields.text({ bind: 'id' }),
                                    ui.nestedFields.text({ bind: 'symbol' }),
                                    ui.nestedFields.technical({
                                        bind: 'decimalDigits',
                                    }),
                                ],
                            }),
                        ],
                    }),
                ],
            }),
        },
    },
})
export class MaterialTrackingLineInquiry extends ui.Page<GraphApi> {}
