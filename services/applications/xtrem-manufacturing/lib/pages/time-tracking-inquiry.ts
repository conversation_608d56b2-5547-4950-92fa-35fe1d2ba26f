import { DateValue } from '@sage/xtrem-date-time';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type {
    GraphApi,
    OperationTrackingLine,
    OperationTrackingLineBinding,
    OperationTracking as OperationTrackingNode,
    WorkOrder,
} from '@sage/xtrem-manufacturing-api';
import type { DetailedResource, Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { manufacturingInquiries } from '../menu-items/manufacturing-inquiries';

@ui.decorators.page<TimeTrackingInquiry, OperationTrackingNode>({
    menuItem: manufacturingInquiries,
    priority: 150,
    title: 'Time tracking',
    objectTypeSingular: 'Time tracking',
    objectTypePlural: 'Time trackings',
    idField() {
        return this.number;
    },
    mode: 'default',
    node: '@sage/xtrem-manufacturing/OperationTracking',
    navigationPanel: {
        orderBy: { number: -1, effectiveDate: -1 },
        listItem: {
            title: ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            line2: ui.nestedFields.reference<TimeTrackingInquiry, OperationTrackingNode, WorkOrder>({
                node: '@sage/xtrem-manufacturing/WorkOrder',
                tunnelPage: '@sage/xtrem-manufacturing/WorkOrder',
                bind: 'workOrder',
                valueField: 'number',
                title: 'Work order',
            }),
            workOrderName: ui.nestedFields.text({ bind: { workOrder: { name: true } }, title: 'Work order name' }),
            line2Right: ui.nestedFields.date({ bind: 'effectiveDate', title: 'Date' }),
            line3Right: ui.nestedFields.reference<TimeTrackingInquiry, OperationTrackingNode, Site>({
                title: 'Site name',
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
            }),
            siteID: ui.nestedFields.text({
                bind: { site: { id: true } },
                title: 'Site ID',
                isHiddenOnMainField: true,
            }),
            line3: ui.nestedFields.reference<TimeTrackingInquiry, OperationTrackingNode, Item>({
                bind: { workOrder: { productionItem: { releasedItem: true } } },
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            itemID: ui.nestedFields.text({
                bind: { workOrder: { productionItem: { releasedItem: { id: true } } } },
                title: 'Item ID',
            }),
            itemDescription: ui.nestedFields.text({
                bind: { workOrder: { productionItem: { releasedItem: { description: true } } } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
        },
    },
})
export class TimeTrackingInquiry extends ui.Page<GraphApi> {
    @ui.decorators.section<TimeTrackingInquiry>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<TimeTrackingInquiry>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
        isDisabled: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<TimeTrackingInquiry>({
        title: 'Number',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
    })
    number: ui.fields.Text;

    @ui.decorators.referenceField<TimeTrackingInquiry, WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Work order',
        node: '@sage/xtrem-manufacturing/WorkOrder',
        lookupDialogTitle: 'Select work order',
        valueField: 'number',
        helperTextField: 'name',
        tunnelPage: '@sage/xtrem-manufacturing/WorkOrder',
        width: 'medium',
    })
    workOrder: ui.fields.Reference<WorkOrder>;

    @ui.decorators.referenceField<TimeTrackingInquiry, Site>({
        parent() {
            return this.mainBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'isLocationManaged', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
        ],
        filter() {
            return { isActive: { _eq: true } };
        },
        width: 'medium',
    })
    site: ui.fields.Reference;

    @ui.decorators.dateField<TimeTrackingInquiry>({
        title: 'Date',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isMandatory: true,
        maxDate: DateValue.today().toString(),
    })
    effectiveDate: ui.fields.Date;

    @ui.decorators.referenceField<TimeTrackingInquiry, Item>({
        parent() {
            return this.mainBlock;
        },
        title: 'Item',
        bind: { workOrder: { productionItem: { releasedItem: true } } },
        valueField: 'name',
        columns: [
            ui.nestedFields.technical<TimeTrackingInquiry, Item, UnitOfMeasure>({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
    })
    releasedItem: ui.fields.Reference<Item>;

    @ui.decorators.tableField<TimeTrackingInquiry, OperationTrackingLineBinding>({
        title: 'Operations',
        bind: 'lines',
        canSelect: false,
        pageSize: 10,
        isReadOnly: true,
        node: '@sage/xtrem-manufacturing/OperationTrackingLine',
        orderBy: {
            _sortValue: +1,
        },
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.text({
                title: 'Operation number',
                bind: { workOrderOperation: { operationNumber: true } },
            }),
            ui.nestedFields.reference<TimeTrackingInquiry, OperationTrackingLineBinding, DetailedResource>({
                title: 'Resource name',
                valueField: 'name',
                bind: 'actualResource',
                node: '@sage/xtrem-master-data/DetailedResource',
                width: 'medium',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                ],
            }),
            ui.nestedFields.text({
                title: 'Resource ID',
                bind: { actualResource: { id: true } },
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text({
                bind: { actualResource: { _factory: { title: true } } },
                title: 'Resource type',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                title: 'Quantity',
                bind: 'completedQuantity',
                scale() {
                    return this.releasedItem.value?.stockUnit?.decimalDigits || 0;
                },
                postfix() {
                    return this.releasedItem.value?.stockUnit?.symbol ?? '';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Setup time',
                bind: 'actualSetupTime',
                scale(_val, rowData) {
                    return rowData?.setupTimeUnit?.decimalDigits || 0;
                },
                postfix(_val, rowData) {
                    return rowData?.setupTimeUnit?.symbol;
                },
            }),
            ui.nestedFields.numeric({
                title: 'Run time',
                bind: 'actualRunTime',
                scale(_val, rowData) {
                    return rowData?.runTimeUnit?.decimalDigits || 0;
                },
                postfix(_val, rowData) {
                    return rowData?.runTimeUnit?.symbol;
                },
            }),
            ui.nestedFields.checkbox({
                bind: 'completed',
            }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.reference<TimeTrackingInquiry, OperationTrackingLineBinding, UnitOfMeasure>({
                valueField: 'name',
                bind: 'setupTimeUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                isHidden: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.reference<TimeTrackingInquiry, OperationTrackingLineBinding, UnitOfMeasure>({
                valueField: 'name',
                bind: 'runTimeUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                isHidden: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
        dropdownActions: [
            {
                icon: 'view',
                title: 'Dimensions',
                async onClick(_rowId: any, rowItem: ui.PartialCollectionValue<OperationTrackingLine>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await utils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable: false,
                            },
                        ),
                    );
                },
            },
        ],
    })
    lines: ui.fields.Table<OperationTrackingLine>;
}
