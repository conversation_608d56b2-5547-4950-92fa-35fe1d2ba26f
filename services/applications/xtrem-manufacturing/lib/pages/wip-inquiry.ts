import { withoutEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type { FinanceTransaction } from '@sage/xtrem-finance-data-api';
import type {
    GraphApi,
    WorkInProgressInquiryStatus,
    WorkInProgressResultLine,
    WorkOrder,
} from '@sage/xtrem-manufacturing-api';
import type { Site, User } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import * as PillColor from '../client-functions/pill-color';
import { manufacturingInquiries } from '../menu-items/manufacturing-inquiries';

@ui.decorators.page<WipInquiry>({
    menuItem: manufacturingInquiries,
    priority: 301,
    title: 'Work in progress inquiry',
    module: 'manufacturing',
    mode: 'tabs',
    skipDirtyCheck: true,
    access: { node: '@sage/xtrem-manufacturing/WorkInProgressCost' },
    node: '@sage/xtrem-manufacturing/WorkInProgressInputSet',
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return [this.runButton];
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    async defaultEntry() {
        // As there is only one record per user try to load this record.
        //
        // NOTE (for local Cucumber script tests on local development instances): Please make sure that the user
        // e-mail address in the top field is set to the e-mail address of the user configured in xtrem-config.yml!
        const inputSet = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkInProgressInputSet')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true },
                        { filter: { user: { email: this.$.username } }, first: 1 },
                    ),
                )
                .execute(),
        );

        // If there is such record open it. Otherwise open work with an empty page in creation mode.
        return inputSet.at(0)?._id || '$new';
    },
    async onLoad() {
        if (!this.$.recordId) {
            // In case we have no record for this user pre-set the calculation user field.
            await this.$.fetchDefaults(['user']);
        }
        if (!this.status.value || this.status.value === 'draft') {
            this.asOfDate.value = DateValue.today().toString();
        }
        this.enableRunButton();
    },
    navigationPanel: undefined,
    headerSection() {
        return this.mainSection;
    },
})
export class WipInquiry extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<WipInquiry>({
        title: 'Run',
        isDisabled() {
            return this.status.value === 'inProgress';
        },
        async onClick() {
            this.$.loader.isHidden = false;
            this.status.value = 'inProgress';
            if (await this.saveInquiry()) {
                await this.runInquiry();
                await this.$.router.refresh();
            } else {
                this.status.value = 'error';
            }
            this.$.loader.isHidden = true;
        },
        buttonType: 'primary',
    })
    runButton: ui.PageAction;

    @ui.decorators.section<WipInquiry>({ isOpen: true, isTitleHidden: true, title: 'General' })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WipInquiry>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.tile<WipInquiry>({
        parent() {
            return this.mainSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.referenceField<WipInquiry, User>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'User',
        isReadOnly: true,
        width: 'small',
        fetchesDefaults: true,
        valueField: 'displayName',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
    })
    user: ui.fields.Reference<User>;

    @ui.decorators.referenceField<WipInquiry, Site>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        tunnelPage: '@sage/xtrem-master-data/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
            ui.nestedFields.reference({
                bind: 'legalCompany',
                title: 'Company',
                node: '@sage/xtrem-system/Company',
                valueField: 'name',
                columns: [
                    ui.nestedFields.technical({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [ui.nestedFields.technical({ bind: 'symbol' })],
                    }),
                ],
            }),
        ],
        filter: { isActive: { _eq: true }, isManufacturing: { _eq: true } },
        orderBy: { name: 1 },
        onChange() {
            this.enableRunButton();
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<WipInquiry, WorkOrder>({
        title: 'From work order',
        node: '@sage/xtrem-manufacturing/WorkOrder',
        tunnelPage: '@sage/xtrem-manufacturing/WorkOrder',
        valueField: 'number',
        helperTextField: 'number',
        columns: [
            ui.nestedFields.text({ title: 'Number', bind: 'number' }),
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
        ],
        parent() {
            return this.criteriaBlock;
        },
    })
    fromWorkOrder: ui.fields.Reference<WorkOrder>;

    @ui.decorators.referenceField<WipInquiry, WorkOrder>({
        title: 'To work order',
        node: '@sage/xtrem-manufacturing/WorkOrder',
        tunnelPage: '@sage/xtrem-manufacturing/WorkOrder',
        valueField: 'number',
        helperTextField: 'number',
        columns: [
            ui.nestedFields.text({ title: 'Number', bind: 'number' }),
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
        ],
        parent() {
            return this.criteriaBlock;
        },
    })
    toWorkOrder: ui.fields.Reference<WorkOrder>;

    @ui.decorators.dateField<WipInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'As of date',
        isMandatory: true,
        onChange() {
            this.enableRunButton();
        },
    })
    asOfDate: ui.fields.Date;

    @ui.decorators.relativeDateField<WipInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Last run date',
    })
    executionDate: ui.fields.RelativeDate;

    @ui.decorators.labelField<WipInquiry>({
        title: 'Status',
        optionType: '@sage/xtrem-manufacturing/WorkInProgressInquiryStatus',
        parent() {
            return this.criteriaBlock;
        },
        style() {
            return PillColor.getLabelColorByStatus('WorkInProgressInquiryStatus', this.status.value);
        },
    })
    status: ui.fields.Label<WorkInProgressInquiryStatus>;

    @ui.decorators.numericField<WipInquiry>({
        parent() {
            return this.tileContainer;
        },
        title: 'Work in progress total',
        prefix() {
            return this.getCurrencySymbol();
        },
        scale: 4,
        width: 'medium',
    })
    workInProgressTotalAmount: ui.fields.Numeric;

    @ui.decorators.section({ title: 'Results' }) resultsSection: ui.containers.Section;

    @ui.decorators.tableField<WipInquiry, WorkInProgressResultLine>({
        node: '@sage/xtrem-manufacturing/WorkInProgressResultLine',
        canResizeColumns: true,
        canSelect: false,
        isHelperTextHidden: true,
        canExport: true,
        title: 'Results',
        isReadOnly: true,
        orderBy: {
            workOrder: { number: -1 },
        },
        parent() {
            return this.resultsSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: 'isStockJournalAvailable' }),
            ui.nestedFields.technical({ bind: 'isJournalEntryAvailable' }),
            ui.nestedFields.reference({
                bind: 'workOrder',
                title: 'Work order',
                node: '@sage/xtrem-manufacturing/WorkOrder',
                tunnelPage: '@sage/xtrem-manufacturing/WorkOrder',
                valueField: 'number',
                size: 'small',
                isMandatory: true,
            }),
            ui.nestedFields.reference({
                bind: { item: { releasedItem: true } },
                title: 'Item',
                size: 'small',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                tunnelPage: '@sage/xtrem-master-data/Item',
            }),
            ui.nestedFields.text({
                bind: { item: { releasedItem: { id: true } } },
                title: 'Item ID',
            }),
            ui.nestedFields.text({
                bind: { item: { releasedItem: { description: true } } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-manufacturing/WorkOrderStatus',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('WorkOrderStatus', rowData.status),
            }),
            ui.nestedFields.numeric({
                bind: 'workInProgressTotal',
                title: 'Work in progress total',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
            }),
            ui.nestedFields.numeric({
                bind: 'plannedMaterialCost',
                title: 'Planned material cost',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
            }),
            ui.nestedFields.numeric({
                bind: 'actualMaterialCost',
                title: 'Actual material cost',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
            }),
            ui.nestedFields.numeric({
                bind: 'materialCostVariance',
                title: 'Material cost variance',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
            }),
            ui.nestedFields.numeric({
                bind: 'materialCostVariancePercentage',
                title: 'Material cost variance %',
                size: 'small',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'plannedProcessCost',
                title: 'Planned process cost',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
            }),
            ui.nestedFields.numeric({
                bind: 'actualProcessCost',
                title: 'Actual process cost',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
            }),
            ui.nestedFields.numeric({
                bind: 'processCostVariance',
                title: 'Process cost variance',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
            }),
            ui.nestedFields.numeric({
                bind: 'processCostVariancePercentage',
                title: 'Process cost variance %',
                size: 'small',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'plannedMachineCost',
                title: 'Planned machine cost',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'actualMachineCost',
                title: 'Actual machine cost',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'machineCostVariance',
                title: 'Machine cost variance',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'machineCostVariancePercentage',
                title: 'Machine cost variance %',
                size: 'small',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'plannedLaborCost',
                title: 'Planned labor cost',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'actualLaborCost',
                title: 'Actual labor cost',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'laborCostVariance',
                title: 'Labor cost variance',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'laborCostVariancePercentage',
                title: 'Labor cost variance %',
                size: 'small',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'plannedToolCost',
                title: 'Planned tool cost',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'actualToolCost',
                title: 'Actual tool cost',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'toolCostVariance',
                title: 'Tool cost variance',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'toolCostVariancePercentage',
                title: 'Tool cost variance %',
                size: 'small',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'totalPlannedCost',
                title: 'Total planned cost',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
            }),
            ui.nestedFields.numeric({
                bind: 'totalActualCost',
                title: 'Total actual cost',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
            }),
            ui.nestedFields.numeric({
                bind: 'totalCostVariance',
                title: 'Total cost variance',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
            }),
            ui.nestedFields.numeric({
                bind: 'totalCostVariancePercentage',
                title: 'Total cost variance %',
                size: 'small',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'closingVariance',
                title: 'Closing variance',
                size: 'small',
                prefix() {
                    return this.getCurrencySymbol();
                },
                scale: 4,
                isHiddenOnMainField: true,
            }),
        ],
        dropdownActions: [
            {
                title: 'Stock journal',
                icon: 'document_right_align',
                isHidden(_recordId: string, rowItem: ui.PartialNodeWithId<WorkInProgressResultLine>) {
                    return !rowItem.isStockJournalAvailable;
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<WorkInProgressResultLine>) {
                    await this.$.dialog.page(
                        '@sage/xtrem-stock/StockJournalInquiry',
                        {
                            [ui.QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]: JSON.stringify({
                                site: { _id: this.site.value?._id },
                                workOrder: { number: rowItem.workOrder?.number },
                            }),
                        },
                        {
                            isMainListDisplayedInDialog: true,
                            fullScreen: true,
                            resolveOnCancel: true,
                        },
                    );
                },
            },
            {
                title: 'Journal entry',
                icon: 'document_right_align',
                isHidden(_recordId: string, rowItem: ui.PartialNodeWithId<WorkInProgressResultLine>) {
                    return !rowItem.isJournalEntryAvailable;
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<WorkInProgressResultLine>) {
                    const journalEntrySysIds = await this.getJournalEntrySysIds(rowItem.workOrder?._id ?? '');
                    await this.$.dialog.page(
                        '@sage/xtrem-finance/JournalEntry',
                        {
                            [ui.QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]: JSON.stringify({
                                _id: { _in: journalEntrySysIds },
                            }),
                        },
                        {
                            isMainListDisplayedInDialog: true,
                            fullScreen: true,
                            resolveOnCancel: true,
                        },
                    );
                },
            },
        ],
    })
    lines: ui.fields.Table<WorkInProgressResultLine>;

    // ================================================

    enableRunButton() {
        this.runButton.isDisabled =
            this.site.value === null || this.asOfDate.value === null || this.status.value === 'inProgress';
    }

    getCurrencySymbol() {
        return this.site.value?.legalCompany?.currency?.symbol ?? '';
    }

    // ================================================

    async saveInquiry() {
        const validation = await this.$.page.validate();
        if (validation.length !== 0) {
            this.$.showToast(validation.join('\n'), { type: 'error' });
            return false;
        }
        await this.$standardSaveAction.execute(true);
        // To be sure that the user is refreshed before we continue
        return true;
    }

    async runInquiry() {
        if (!this.user.value?._id) {
            return;
        }

        // Notify the user that a calculation request is on the way.
        this.$.showToast(
            ui.localize(
                '@sage/xtrem-manufacturing/pages__wip_inquiry__notification_request_sent',
                'Work in progress inquiry request sent.',
            ),
            { type: 'success' },
        );

        // Request calculation in an asynchronous mutation.
        const isFinished = await this.$.graph
            .node('@sage/xtrem-manufacturing/WorkInProgressInputSet')
            .asyncOperations.wipInquiry.runToCompletion(true, {
                userId: this.user.value._id,
            })
            .execute();

        // Notify the user that the calculation is finished.
        if (isFinished) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__wip_inquiry__notification_inquiry_finished',
                    'Work in progress inquiry finished.',
                ),
                { type: 'success' },
            );
        }
    }

    // ================================================

    async getJournalEntrySysIds(workOrderSysId: string): Promise<string[]> {
        return withoutEdges<Partial<FinanceTransaction>>(
            await this.$.graph
                .node('@sage/xtrem-finance-data/FinanceTransaction')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            targetDocumentSysId: true,
                        },
                        {
                            filter: {
                                targetDocumentType: 'journalEntry',
                                documentType: 'workInProgress',
                                documentSysId: workOrderSysId,
                            },
                        },
                    ),
                )
                .execute(),
        ).map(transaction => (transaction.targetDocumentSysId ?? 0).toString());
    }
}
