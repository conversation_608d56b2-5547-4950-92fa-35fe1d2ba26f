import { asyncArray } from '@sage/xtrem-async-helper';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type { GraphApi } from '@sage/xtrem-manufacturing-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<WorkOrderSuggestion>({
    title: 'Work order suggestion',
    // Remove menu item as this page should not be callable from menu
    // menuItem: manufacturing,
    // priority: 350,
    module: 'manufacturing',
    mode: 'default',
    isTransient: true,
    businessActions() {
        return [this.updateWorkOrder];
    },
})
export class WorkOrderSuggestion extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkOrderSuggestion>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkOrderSuggestion>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Selection criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.pageAction<WorkOrderSuggestion>({
        title: 'Create',
        isDisabled: true,
        async onClick() {
            if (this.results.selectedRecords.length) {
                const validation = await this.$.page.validate();
                if (validation.length) {
                    this.$.showToast(validation.join('\n'), { type: 'error' });
                } else {
                    try {
                        this.$.loader.isHidden = false;
                        await asyncArray(this.results.selectedRecords).forEach(async rowId => {
                            const item = this.results.getRecordValue(rowId);
                            await this.$.graph
                                .node('@sage/xtrem-manufacturing/WorkOrder')
                                .update({ _id: true }, { data: { _id: item?._id, type: item?.type } })
                                .execute();
                        });
                        this.$.loader.isHidden = true;
                        this.$.showToast(
                            this.results.selectedRecords.length > 1
                                ? ui.localize(
                                      '@sage/xtrem-manufacturing/pages__work_order_suggestion__creation__success_multi',
                                      '{{num}} work orders updated',
                                      { num: this.results.selectedRecords.length },
                                  )
                                : ui.localize(
                                      '@sage/xtrem-manufacturing/pages__work_order_suggestion__creation__success',
                                      '{{num}} work order updated',
                                      { num: this.results.selectedRecords.length },
                                  ),
                            { timeout: 10000, type: 'success' },
                        );
                        this.$.finish();
                    } catch (error) {
                        this.$.showToast(error.message, { timeout: 10000, type: 'error' });
                    }
                }
                this.$.router.goTo('@sage/xtrem-manufacturing/WorkOrderSuggestion/');
            }
        },
        buttonType: 'primary',
    })
    updateWorkOrder: ui.PageAction;

    @ui.decorators.referenceField<WorkOrderSuggestion>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'isLocationManaged', isHidden: true }),
        ],
        isMandatory: true,
        placeholder: 'Select site',
        width: 'small',
    })
    site: ui.fields.Reference;

    @ui.decorators.dateField<WorkOrderSuggestion>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Order date from',
    })
    issueDateFrom: ui.fields.Date;

    @ui.decorators.dateField<WorkOrderSuggestion>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Order date to',
    })
    issueDateTo: ui.fields.Date;

    @ui.decorators.referenceField<WorkOrderSuggestion>({
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        title: 'Item',
        valueField: 'name',
        helperTextField: 'id',
    })
    item: ui.fields.Reference;

    @ui.decorators.buttonField<WorkOrderSuggestion>({
        parent() {
            return this.criteriaBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-manufacturing/search', 'Search');
        },
        width: 'small',
        async onClick() {
            this.$.loader.isHidden = false;
            await this.search();
            this.$.loader.isHidden = true;
        },
    })
    searchButton: ui.fields.Button;

    @ui.decorators.tableField<WorkOrderSuggestion>({
        title: 'Purchase order suggestions',
        canUserHideColumns: false,
        canSelect: true,
        isHelperTextHidden: true,
        isTransient: true,
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.text({
                title: 'Work order number',
                bind: 'number',
                isReadOnly: true,
            }),
            ui.nestedFields.dropdownList({
                title: 'Convert to',
                bind: 'type',
                optionType: '@sage/xtrem-manufacturing/WorkOrderFilteredType',
            }),
            ui.nestedFields.reference({
                bind: 'productionItem',
                title: 'Item name',
                node: '@sage/xtrem-manufacturing/WorkOrderReleasedItem',
                valueField: 'releasedItemName',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'productionItem',
                title: 'Item ID',
                node: '@sage/xtrem-manufacturing/WorkOrderReleasedItem',
                valueField: 'releasedItemId',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'productionItem',
                title: 'Quantity',
                node: '@sage/xtrem-manufacturing/WorkOrderReleasedItem',
                valueField: 'releasedQuantity',
                isReadOnly: true,
            }),
            ui.nestedFields.checkbox({
                bind: 'isForwardScheduling',
                title: 'Forward scheduling',
                isReadOnly: true,
            }),

            ui.nestedFields.date({
                title: 'Start',
                bind: 'endDate',
                isReadOnly: true,
            }),

            ui.nestedFields.date({
                title: 'End',
                bind: 'endDate',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                bind: '_id',
                isHidden: true,
            }),
        ],
        onRowSelected() {
            this.updateWorkOrder.isDisabled = false;
        },
        onRowUnselected() {
            if (!this.results.selectedRecords.length) {
                this.updateWorkOrder.isDisabled = true;
            }
        },
    })
    results: ui.fields.Table;

    async search() {
        const validation = await this.$.page.validate();
        if (validation.length) {
            this.$.showToast(validation.join('\n'), { type: 'error' });
        } else {
            this.results.value = [];

            this.$.loader.isHidden = false;

            const results = extractEdges(
                await this.$.graph
                    .node('@sage/xtrem-manufacturing/WorkOrder')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                _id: true,
                                number: true,
                                productionItem: {
                                    releasedQuantity: true,
                                    releasedItemName: true,
                                    releasedItemId: true,
                                },
                                startDate: true,
                                endDate: true,
                                isForwardScheduling: true,
                                type: true,
                            },
                            {
                                filter: this.getFilter(),
                            },
                        ),
                    )
                    .execute()
                    .finally(() => {
                        this.$.loader.isHidden = true;
                    }),
            );
            results.forEach(element => {
                element.type = 'planned';
            });

            this.results.value = results;

            this.$.loader.isHidden = true;
        }
    }

    getFilter(): any {
        const filter = { _and: [{}] };
        // Set default toDate to today
        let dateTo = DateValue.today().toString();
        if (this.issueDateTo.value) {
            dateTo = this.issueDateTo.value;
        } else {
            this.issueDateTo.value = dateTo;
        }
        let dateFrom = DateValue.today().addYears(-1).toString();
        if (this.issueDateFrom.value) {
            dateFrom = this.issueDateFrom.value;
        } else {
            this.issueDateFrom.value = dateFrom;
        }

        filter._and.push({
            type: { _eq: 'suggested' },
        });

        filter._and.push({
            startDate: {
                _gte: dateFrom,
            },
        });

        filter._and.push({
            endDate: {
                _lte: dateTo,
            },
        });

        if (this.item.value) {
            filter._and.push({
                productionItems: {
                    _atLeast: 1,
                    releasedItem: {
                        id: this.item.value.id.valueOf(),
                    },
                },
            });
        }

        if (this.site.value) {
            filter._and.push({
                site: {
                    id: {
                        _eq: this.site.value.id.valueOf(),
                    },
                },
            });
        }
        return filter;
    }
}
