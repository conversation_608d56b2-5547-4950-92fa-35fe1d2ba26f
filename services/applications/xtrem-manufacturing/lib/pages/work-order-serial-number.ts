import { extractEdges } from '@sage/xtrem-client';
import type { GraphApi } from '@sage/xtrem-manufacturing-api';
import type { WorkOrderSerialNumber as WorkOrderSerialNumberNode } from '@sage/xtrem-manufacturing-api-partial';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<WorkOrderSerialNumber>({
    title: 'Pregenerated serial number',
    mode: 'default',
    isTransient: true,
    module: 'xtrem-manufacturing',
    businessActions() {
        return [this.ok];
    },

    async onLoad() {
        const filter = {
            workOrder: { _id: { _eq: `${this.$.queryParameters.workOrderId}` } },
        };

        const serialNumbersResult = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrderSerialNumber')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            workOrder: {
                                _id: true,
                            },
                            serialNumber: {
                                _id: true,
                                id: true,
                                isUsable: true,
                                isInStock: true,
                                isAllocated: true,
                            },
                        },
                        {
                            filter,
                            orderBy: {
                                _id: 1,
                            },
                            first: 1000,
                        },
                    ),
                )
                .execute(),
        );

        this.serialNumbers.value = serialNumbersResult.map(serialNumberLine => ({
            serialNumber: {
                id: serialNumberLine.serialNumber.id,
                _id: serialNumberLine.serialNumber._id,
            },
            isInStock: serialNumberLine.serialNumber.isInStock,
            _id: serialNumberLine._id,
        }));

        this.$.setPageClean();
    },
})
export class WorkOrderSerialNumber extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<WorkOrderSerialNumber>({
        title: 'OK',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    ok: ui.PageAction;

    @ui.decorators.section<WorkOrderSerialNumber>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkOrderSerialNumber>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    gridBlock: ui.containers.Block;

    @ui.decorators.tableField<WorkOrderSerialNumber>({
        parent() {
            return this.gridBlock;
        },
        title: 'Serial numbers',
        isTransient: true,
        canSelect: false,
        canExport: true,
        orderBy: { _id: 1 },
        pageSize: 10,
        node: '@sage/xtrem-manufacturing/WorkOrderSerialNumber',
        columns: [
            ui.nestedFields.reference<WorkOrderSerialNumber>({
                title: 'Serial number',
                lookupDialogTitle: 'Select serial number',
                node: '@sage/xtrem-stock-data/SerialNumber',
                valueField: 'id',
                isMandatory: true,
                bind: 'serialNumber',
                isDisabled: true,
                columns: [],
            }),
            ui.nestedFields.checkbox<WorkOrderSerialNumber>({
                bind: 'isInStock',
                title: 'In stock',
                isDisabled: true,
            }),
        ],
    })
    serialNumbers: ui.fields.Table<WorkOrderSerialNumberNode>;
}
