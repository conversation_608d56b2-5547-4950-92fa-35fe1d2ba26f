import type {
    GraphApi,
    ProductionTracking,
    ProductionTrackingLineBinding,
    WorkOrderCategory,
} from '@sage/xtrem-manufacturing-api';
import type { Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as PillColor from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { BillOfMaterial, Routing } from '@sage/xtrem-technical-data-api';
import * as ui from '@sage/xtrem-ui';
import { isBaseTrackingLine, isProductionTrackingLineBinding } from '../client-functions/common';
import { manufacturingInquiries } from '../menu-items/manufacturing-inquiries';

@ui.decorators.page<ProductionTrackingLineInquiry, ProductionTrackingLineBinding>({
    menuItem: manufacturingInquiries,
    priority: 55,
    title: 'Production tracking line inquiry',
    module: 'inventory',
    mode: 'default',
    node: '@sage/xtrem-manufacturing/ProductionTrackingLine',
    access: { node: '@sage/xtrem-manufacturing/ProductionTracking', bind: '$read' },
    navigationPanel: {
        onSelect() {
            return true;
        },
        dropdownActions: [
            {
                icon: 'coins',
                title: 'Costs',
                async onClick(rowID) {
                    await this.$.dialog.page(
                        '@sage/xtrem-manufacturing/WipCostInquiryPanel',
                        { trackingLineId: rowID },
                        { size: 'extra-large' },
                    );
                },
            },
        ],
        orderBy: { document: { number: -1, effectiveDate: -1 } },
        listItem: {
            title: ui.nestedFields.text({
                isHidden: true,
                bind: 'storedDimensions',
            }),
            trackingNumberLink: ui.nestedFields.link({
                title: 'Production tracking link',
                bind: { document: { number: true } },
                width: 'medium',
                async onClick(_id, rowItem) {
                    if (isBaseTrackingLine(rowItem)) {
                        await this.$.dialog.page(
                            `@sage/xtrem-manufacturing/ProductionTrackingInquiry`,
                            { _id: rowItem.document._id },
                            { fullScreen: true, resolveOnCancel: true },
                        );
                    }
                },
            }),
            trackingDate: ui.nestedFields.date({
                title: 'Tracking date',
                bind: { document: { effectiveDate: true } },
                isHiddenOnMainField: true,
            }),
            trackingNumber: ui.nestedFields.reference<
                ProductionTrackingLineInquiry,
                ProductionTrackingLineBinding,
                ProductionTracking
            >({
                title: 'Production tracking',
                valueField: 'number',
                bind: 'document',
                node: '@sage/xtrem-manufacturing/ProductionTracking',
                width: 'medium',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.technical({
                        bind: 'financialSite',
                        node: '@sage/xtrem-system/Site',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'isActive' }),
                            ui.nestedFields.technical({ bind: 'isLocationManaged' }),
                            ui.nestedFields.technical({
                                bind: 'legalCompany',
                                node: '@sage/xtrem-system/Company',
                                nestedFields: [
                                    ui.nestedFields.technical({
                                        bind: 'currency',
                                        node: '@sage/xtrem-master-data/Currency',
                                        nestedFields: [
                                            ui.nestedFields.technical({ bind: 'id' }),
                                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                            ui.nestedFields.technical({ bind: 'symbol' }),
                                        ],
                                    }),
                                ],
                            }),
                        ],
                    }),
                ],
            }),
            workOrderNumber: ui.nestedFields.link({
                title: 'Work order link',
                bind: { workOrderLine: { document: { number: true } } },
                width: 'medium',
                async onClick(_id, rowItem) {
                    if (isProductionTrackingLineBinding(rowItem)) {
                        await this.$.dialog.page(
                            `@sage/xtrem-manufacturing/WorkOrder`,
                            { _id: rowItem.workOrderLine.document._id },
                            { fullScreen: true, resolveOnCancel: true },
                        );
                    }
                },
            }),
            workOrder: ui.nestedFields.reference({
                title: 'Work order',
                valueField: 'number',
                bind: { workOrderLine: { document: true } },
                node: '@sage/xtrem-manufacturing/WorkOrder',
                isHiddenOnMainField: true,
                columns: [ui.nestedFields.text({ bind: 'number', title: 'Number' })],
            }),
            itemName: ui.nestedFields.reference<ProductionTrackingLineInquiry, ProductionTrackingLineBinding, Item>({
                title: 'Item',
                valueField: 'name',
                bind: { workOrderLine: { releasedItem: true } },
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                width: 'medium',
            }),
            itemId: ui.nestedFields.text({
                title: 'Item ID',
                bind: { workOrderLine: { releasedItem: { id: true } } },
                width: 'medium',
            }),
            itemDescription: ui.nestedFields.text({
                title: 'Item description',
                bind: { workOrderLine: { releasedItem: { description: true } } },
                width: 'medium',
                isHiddenOnMainField: true,
            }),
            workOrderStatus: ui.nestedFields.label({
                title: 'Work order status',
                optionType: '@sage/xtrem-manufacturing/WorkOrderStatus',
                bind: { workOrderLine: { document: { status: true } } },
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColor.getLabelColorByStatus('WorkOrderStatus', rowData.workOrderLine?.document?.status as any),
            }),
            workOrderCategory: ui.nestedFields.reference<
                ProductionTrackingLineInquiry,
                ProductionTrackingLineBinding,
                WorkOrderCategory
            >({
                title: 'Work order category',
                valueField: 'name',
                bind: { workOrderLine: { document: { category: true } } },
                node: '@sage/xtrem-manufacturing/WorkOrderCategory',
                width: 'medium',
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'name' })],
            }),
            bomLink: ui.nestedFields.link({
                title: 'Bill of material link',
                bind: { workOrderLine: { document: { bomCode: { name: true } } } },
                width: 'large',
                async onClick(_id, rowItem) {
                    if (isProductionTrackingLineBinding(rowItem)) {
                        await this.$.dialog.page(
                            `@sage/xtrem-technical-data/BillOfMaterial`,
                            { _id: rowItem.workOrderLine.document.bomCode._id },
                            { fullScreen: true, resolveOnCancel: true },
                        );
                    }
                },
            }),
            bomId: ui.nestedFields.text({
                title: 'BOM ID',
                bind: { workOrderLine: { document: { bomCode: { item: { id: true } } } } },
                width: 'medium',
            }),
            bom: ui.nestedFields.reference<
                ProductionTrackingLineInquiry,
                ProductionTrackingLineBinding,
                BillOfMaterial
            >({
                bind: { workOrderLine: { document: { bomCode: true } } },
                title: 'Bill of material',
                width: 'large',
                node: '@sage/xtrem-technical-data/BillOfMaterial',
                tunnelPage: '@sage/xtrem-technical-data/BillOfMaterial',
                lookupDialogTitle: 'Select bill of material',
                valueField: 'name',
                helperTextField: { item: { name: true } },
                isReadOnly: true,
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
                    }),
                ],
            }),
            routingLink: ui.nestedFields.link({
                title: 'Routing link',
                bind: { workOrderLine: { document: { routingCode: { name: true } } } },
                width: 'large',
                async onClick(_id, rowItem) {
                    if (isProductionTrackingLineBinding(rowItem)) {
                        await this.$.dialog.page(
                            `@sage/xtrem-technical-data/Routing`,
                            { _id: rowItem.workOrderLine.document.routingCode._id },
                            {
                                fullScreen: true,
                                resolveOnCancel: true,
                            },
                        );
                    }
                },
            }),
            routingId: ui.nestedFields.text({
                title: 'Routing ID',
                bind: { workOrderLine: { document: { routingCode: { item: { id: true } } } } },
                width: 'medium',
            }),
            routing: ui.nestedFields.reference<ProductionTrackingLineInquiry, ProductionTrackingLineBinding, Routing>({
                bind: { workOrderLine: { document: { routingCode: true } } },
                title: 'Routing',
                width: 'large',
                node: '@sage/xtrem-technical-data/Routing',
                tunnelPage: '@sage/xtrem-technical-data/Routing',
                valueField: 'name',
                isHiddenOnMainField: true,
            }),
            site: ui.nestedFields.reference({
                title: 'Site name',
                bind: { document: { site: true } },
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
            siteId: ui.nestedFields.text({
                title: 'Site ID',
                bind: { document: { site: { id: true } } },
                isHiddenOnMainField: true,
            }),
            stockStatus: ui.nestedFields.label<ProductionTrackingLineInquiry, ProductionTrackingLineBinding>({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowData) =>
                    PillColor.getLabelColorByStatus('StockDocumentTransactionStatus', rowData.stockTransactionStatus),
                async onClick(_id, data) {
                    await StockDataUtils.onStockTransactionStatusClick(this, data.stockTransactionStatus, {
                        origin: 'line',
                        _id,
                    });
                },
            }),
            quantity: ui.nestedFields.numeric({
                title: 'Quantity',
                bind: 'releasedQuantity',
                scale: (_val, rowData) => rowData?.workOrderLine.releasedItem.stockUnit.decimalDigits || 0,
                postfix: (_val, rowData) => rowData?.workOrderLine.releasedItem.stockUnit.symbol,
            }),
            orderCost: ui.nestedFields.numeric({
                title: 'Order cost',
                bind: 'orderCost',
                scale: (_val, rowData) => rowData?.document.financialSite.currency.decimalDigits || 0,
                prefix: (_val, rowData) => rowData?.document.financialSite.currency.symbol,
            }),
            valuedCost: ui.nestedFields.numeric({
                title: 'Valued cost',
                bind: 'valuedCost',
                scale: (_val, rowData) => rowData?.document.financialSite.currency.decimalDigits || 0,
                prefix: (_val, rowData) => rowData?.document.financialSite.currency.symbol,
            }),
            completed: ui.nestedFields.checkbox({ title: 'Completed', bind: 'completed' }),
            stockUnit: ui.nestedFields.reference<
                ProductionTrackingLineInquiry,
                ProductionTrackingLineBinding,
                UnitOfMeasure
            >({
                title: 'Unit',
                isHiddenOnMainField: true,
                valueField: 'name',
                bind: { workOrderLine: { releasedItem: { stockUnit: true } } },
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            currency: ui.nestedFields.reference({
                title: 'Currency',
                bind: { document: { financialSite: { legalCompany: { currency: true } } } },
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                ],
            }),
            computedAttributes: ui.nestedFields.technical({ bind: 'computedAttributes' }),
            storedAttributes: ui.nestedFields.technical({ bind: 'storedAttributes' }),
            workOrderLine: ui.nestedFields.technical({
                bind: 'workOrderLine',
                node: '@sage/xtrem-manufacturing/WorkOrderReleasedItem',
                nestedFields: [
                    ui.nestedFields.technical({
                        bind: 'releasedItem',
                        node: '@sage/xtrem-master-data/Item',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({
                                bind: 'stockUnit',
                                node: '@sage/xtrem-master-data/UnitOfMeasure',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'symbol' }),
                                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                ],
                            }),
                        ],
                    }),
                ],
            }),
        },
    },
})
export class ProductionTrackingLineInquiry extends ui.Page<GraphApi> {}
