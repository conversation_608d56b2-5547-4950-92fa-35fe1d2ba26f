import type { GraphApi } from '@sage/xtrem-manufacturing-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { featuresManufacturing } from '../menu-items/features-manufacturing';

@ui.decorators.page<WorkOrderCategory>({
    title: 'Work order category',
    objectTypeSingular: 'Work order category',
    objectTypePlural: 'Work order categories',
    idField() {
        return this.id;
    },
    mode: 'default',
    module: 'manufacturing',
    menuItem: featuresManufacturing,
    priority: 250,
    node: '@sage/xtrem-manufacturing/WorkOrderCategory',
    navigationPanel: {
        listItem: {
            line2: ui.nestedFields.text({ bind: 'id' }),
            title: ui.nestedFields.text({ bind: 'name' }),
            description: ui.nestedFields.text({ bind: 'description' }),
            routing: ui.nestedFields.checkbox({ bind: 'routing', title: 'Routing' }),
            billOfMaterial: ui.nestedFields.checkbox({ bind: 'billOfMaterial', title: 'Bill of material' }),
            isDefault: ui.nestedFields.checkbox({ bind: 'isDefault', title: 'Default' }),
        },
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class WorkOrderCategory extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkOrderCategory>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkOrderCategory>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<WorkOrderCategory>({})
    _id: ui.fields.Text;

    @ui.decorators.textField<WorkOrderCategory>({
        isMandatory: true,
        parent() {
            return this.mainBlock;
        },
        title: 'ID',
    })
    id: ui.fields.Text;

    @ui.decorators.textField<WorkOrderCategory>({
        isMandatory: true,
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
    })
    name: ui.fields.Text;

    @ui.decorators.textField<WorkOrderCategory>({
        parent() {
            return this.mainBlock;
        },
        title: 'Description',
    })
    description: ui.fields.Text;

    @ui.decorators.checkboxField<WorkOrderCategory>({
        parent() {
            return this.mainBlock;
        },
        title: 'Routing',
        validation(value) {
            if (value === false && this.billOfMaterial.value === false) {
                return ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order_panel__routing_bom_error',
                    'Assign a routing or bill of material to this work order category.',
                );
            }
            return undefined;
        },
    })
    routing: ui.fields.Checkbox;

    @ui.decorators.checkboxField<WorkOrderCategory>({
        parent() {
            return this.mainBlock;
        },
        title: 'Bill of material',
    })
    billOfMaterial: ui.fields.Checkbox;

    @ui.decorators.checkboxField<WorkOrderCategory>({
        parent() {
            return this.mainBlock;
        },
        title: 'Default',
    })
    isDefault: ui.fields.Checkbox;
}
