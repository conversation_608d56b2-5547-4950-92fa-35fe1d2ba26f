import type { GraphApi, WorkOrder } from '@sage/xtrem-manufacturing-api';
import type { CapabilityLevel, DetailedResource, UnitOfMeasure, WeeklyShift } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<TimeTrackingWorkOrderOperation>({
    title: 'New work order operation',
    module: 'manufacturing',
    isTransient: true,
    businessActions() {
        return [this.cancel, this.save];
    },
})
export class TimeTrackingWorkOrderOperation extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<TimeTrackingWorkOrderOperation>({
        title: 'OK',
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                this.$.finish({
                    workOrderOperation: {
                        _id: null,
                        workOrder: this.workOrder.value,
                        name: this.operationName.value,
                        minCapabilityLevel: this.minCapabilityLevel.value,
                    },
                    status: 'pending',
                    setupTimeUnit: this.setupTimeUnit.value,
                    runTimeUnit: this.runTimeUnit.value,
                    expectedResource: this.expectedResource.value,
                    actualQuantity: this.actualQuantity.value,
                    actualSetupTime: this.actualSetupTime.value,
                    actualRunTime: this.actualRunTime.value,
                });
            } else {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            }
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<TimeTrackingWorkOrderOperation>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.section<TimeTrackingWorkOrderOperation>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<TimeTrackingWorkOrderOperation>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.referenceField<TimeTrackingWorkOrderOperation, WorkOrder>({
        node: '@sage/xtrem-manufacturing/WorkOrder',
        lookupDialogTitle: 'Select work order',
        valueField: 'number',
        helperTextField: 'name',
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'Number', bind: 'number' }),
            ui.nestedFields.technical({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                nestedFields: [ui.nestedFields.text({ bind: '_id' })],
            }),
            ui.nestedFields.technical({
                bind: 'routingCode',
                node: '@sage/xtrem-technical-data/Routing',
                nestedFields: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical({
                        node: '@sage/xtrem-master-data/Item',
                        bind: 'item',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],
        filter() {
            return {
                _and: [
                    {
                        type: {
                            _eq: 'firm',
                        },
                    },
                    {
                        status: {
                            _in: ['pending', 'inProgress', 'completed'],
                        },
                    },
                    { _or: [{ routingCode: null }, { routingCode: { status: { _ne: 'inDevelopment' } } }] },
                ],
            };
        },
        parent() {
            return this.block;
        },
        title: 'Work order number',
    })
    workOrder: ui.fields.Reference<WorkOrder>;

    @ui.decorators.textField<TimeTrackingWorkOrderOperation>({
        title: 'Operation description',
        isMandatory: true,
        parent() {
            return this.block;
        },
    })
    operationName: ui.fields.Text;

    @ui.decorators.referenceField<TimeTrackingWorkOrderOperation, CapabilityLevel>({
        parent() {
            return this.block;
        },
        title: 'Capability level',
        isMandatory: true,
        node: '@sage/xtrem-master-data/CapabilityLevel',
        lookupDialogTitle: 'Select capability level',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        width: 'small',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'Capability level', bind: 'id' }),
            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
            ui.nestedFields.text({ title: 'Level', bind: 'level' }),
        ],
    })
    minCapabilityLevel: ui.fields.Reference<CapabilityLevel>;

    @ui.decorators.referenceField<TimeTrackingWorkOrderOperation, UnitOfMeasure>({
        title: 'Setup time unit',
        bind: 'setupTimeUnit',
        valueField: 'name',
        helperTextField: 'symbol',
        isMandatory: true,
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
        lookupDialogTitle: 'Select unit of measure',
        minLookupCharacters: 1,
        parent() {
            return this.block;
        },

        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.technical({ bind: 'type' }),
        ],
        filter() {
            return { type: { _eq: 'time' } };
        },
        onChange() {
            this.actualSetupTime.postfix = this.setupTimeUnit.value?.symbol;
            this.actualSetupTime.scale = this.setupTimeUnit.value?.decimalDigits;
        },
    })
    setupTimeUnit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.referenceField<TimeTrackingWorkOrderOperation, UnitOfMeasure>({
        isMandatory: true,
        title: 'Run time unit',
        bind: 'runTimeUnit',
        valueField: 'name',
        helperTextField: 'symbol',
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
        lookupDialogTitle: 'Select unit of measure',
        minLookupCharacters: 1,
        parent() {
            return this.block;
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.technical({ bind: 'type' }),
        ],
        filter() {
            return { type: { _eq: 'time' } };
        },
        onChange() {
            this.actualRunTime.postfix = this.runTimeUnit.value?.symbol;
            this.actualRunTime.scale = this.runTimeUnit.value?.decimalDigits;
        },
    })
    runTimeUnit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.referenceField<TimeTrackingWorkOrderOperation, DetailedResource>({
        parent() {
            return this.block;
        },
        node: '@sage/xtrem-master-data/DetailedResource',
        title: 'Resource',
        valueField: 'id',
        helperTextField: 'name',
        minLookupCharacters: 1,
        isMandatory: true,
        lookupDialogTitle: 'Select resource',
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
            }),
            ui.nestedFields.text({
                bind: 'id',
                title: 'ID',
            }),
            ui.nestedFields.reference<TimeTrackingWorkOrderOperation, DetailedResource, WeeklyShift>({
                bind: 'weeklyShift',
                title: 'Weekly shift',
                valueField: 'id',
                node: '@sage/xtrem-master-data/WeeklyShift',
            }),

            ui.nestedFields.technical({
                bind: '_id',
            }),

            ui.nestedFields.technical({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                nestedFields: [ui.nestedFields.text({ bind: '_id' })],
            }),
        ],
        filter() {
            return {
                _and: [{ site: { _id: { _eq: this.workOrder.value.site._id } } }],
            };
        },
    })
    expectedResource: ui.fields.Reference<DetailedResource>;

    @ui.decorators.numericField<TimeTrackingWorkOrderOperation>({
        parent() {
            return this.block;
        },
        title: 'Actual quantity',
        isMandatory: true,
    })
    actualQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<TimeTrackingWorkOrderOperation>({
        parent() {
            return this.block;
        },
        title: 'Actual setup time',
        isMandatory: true,
    })
    actualSetupTime: ui.fields.Numeric;

    @ui.decorators.numericField<TimeTrackingWorkOrderOperation>({
        parent() {
            return this.block;
        },
        title: 'Actual run time',
        isMandatory: true,
    })
    actualRunTime: ui.fields.Numeric;
}
