import type {
    Graph<PERSON>pi,
    OperationTracking,
    OperationTrackingLine as OperationTrackingLineNode,
    WorkOrder,
    WorkOrderOperation,
    WorkOrderReleasedItem,
} from '@sage/xtrem-manufacturing-api';
import type { DetailedResource, Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';
import type { Routing } from '@sage/xtrem-technical-data-api';
import * as ui from '@sage/xtrem-ui';
import { isBaseTrackingLine } from '../client-functions/common';
import * as PillColor from '../client-functions/pill-color';
import { manufacturingInquiries } from '../menu-items/manufacturing-inquiries';

@ui.decorators.page<TimeTrackingLineInquiry, OperationTrackingLineNode>({
    menuItem: manufacturingInquiries,
    priority: 205,
    title: 'Time tracking line inquiry',
    module: 'manufacturing',
    mode: 'default',
    node: '@sage/xtrem-manufacturing/OperationTrackingLine',
    access: { node: '@sage/xtrem-manufacturing/OperationTracking', bind: '$read' },
    navigationPanel: {
        orderBy: { documentNumber: -1, workOrderOperation: { workOrder: { number: -1 } } },
        onSelect: () => true,
        dropdownActions: [
            {
                icon: 'coins',
                title: 'Costs',
                async onClick(rowID) {
                    await this.$.dialog.page(
                        '@sage/xtrem-manufacturing/WipCostInquiryPanel',
                        { trackingLineId: rowID },
                        { size: 'extra-large' },
                    );
                },
            },
        ],
        listItem: {
            storedDimensions: ui.nestedFields.technical({ bind: 'storedDimensions' }),
            trackingNumberLink: ui.nestedFields.link({
                title: 'Time tracking',
                bind: 'documentNumber',
                width: 'medium',
                async onClick(_id, rowItem) {
                    if (isBaseTrackingLine(rowItem)) {
                        await this.$.dialog.page(
                            `@sage/xtrem-manufacturing/TimeTrackingInquiry`,
                            { _id: rowItem.document._id },
                            { fullScreen: true, resolveOnCancel: true },
                        );
                    }
                },
            }),
            title: ui.nestedFields.date({
                title: 'Tracking date',
                bind: { document: { effectiveDate: true } },
                isHiddenOnMainField: true,
            }),
            trackingNumber: ui.nestedFields.reference<
                TimeTrackingLineInquiry,
                OperationTrackingLineNode,
                OperationTracking
            >({
                title: 'Time tracking',
                valueField: 'number',
                bind: 'document',
                node: '@sage/xtrem-manufacturing/OperationTracking',
                width: 'medium',
                isHiddenOnMainField: true,
            }),
            workOrder: ui.nestedFields.reference<TimeTrackingLineInquiry, OperationTrackingLineNode, WorkOrder>({
                title: 'Work order',
                valueField: 'number',
                tunnelPage: '@sage/xtrem-manufacturing/WorkOrder',
                bind: { workOrderOperation: { workOrder: true } },
                node: '@sage/xtrem-manufacturing/WorkOrder',
                columns: [
                    ui.nestedFields.technical({ bind: 'number' }),
                    ui.nestedFields.reference<TimeTrackingLineInquiry, WorkOrder, WorkOrderReleasedItem>({
                        title: 'Released item',
                        valueField: '_id',
                        bind: 'productionItem',
                        node: '@sage/xtrem-manufacturing/WorkOrderReleasedItem',
                        columns: [
                            ui.nestedFields.reference<TimeTrackingLineInquiry, WorkOrderReleasedItem, Item>({
                                title: 'Released item',
                                valueField: 'id',
                                bind: 'releasedItem',
                                node: '@sage/xtrem-master-data/Item',
                                tunnelPage: '@sage/xtrem-master-data/Item',
                                columns: [
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.reference<TimeTrackingLineInquiry, Item, UnitOfMeasure>({
                                        title: 'Unit',
                                        valueField: 'name',
                                        bind: 'stockUnit',
                                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                                        columns: [
                                            ui.nestedFields.technical({ bind: 'symbol' }),
                                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                        ],
                                    }),
                                ],
                            }),
                        ],
                    }),
                ],
            }),
            workOrderStatus: ui.nestedFields.label({
                title: 'Work order status',
                optionType: '@sage/xtrem-manufacturing/WorkOrderStatus',
                bind: { workOrderOperation: { workOrder: { status: true } } },
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColor.getLabelColorByStatus('WorkOrderStatus', rowData.workOrderOperation?.workOrder?.status),
            }),
            routing: ui.nestedFields.reference<TimeTrackingLineInquiry, OperationTrackingLineNode, Routing>({
                bind: { workOrderOperation: { workOrder: { routingCode: true } } },
                title: 'Routing name',
                width: 'large',
                node: '@sage/xtrem-technical-data/Routing',
                tunnelPage: '@sage/xtrem-technical-data/Routing',
                valueField: 'name',
            }),
            routingId: ui.nestedFields.text({
                title: 'Routing ID',
                bind: { workOrderOperation: { workOrder: { routingCode: { item: { id: true } } } } },
                width: 'medium',
                isHiddenOnMainField: true,
            }),
            site: ui.nestedFields.reference<TimeTrackingLineInquiry, OperationTrackingLineNode, Site>({
                title: 'Site name',
                bind: { document: { site: true } },
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
            }),
            siteId: ui.nestedFields.text({
                title: 'Site ID',
                bind: { document: { site: { id: true } } },
                isHiddenOnMainField: true,
            }),
            operationNumber: ui.nestedFields.numeric({
                title: 'Operation number',
                bind: { workOrderOperation: { operationNumber: true } },
                width: 'medium',
            }),
            operationStatus: ui.nestedFields.label({
                title: 'Operation status',
                bind: { workOrderOperation: { status: true } },
                optionType: '@sage/xtrem-manufacturing/OperationStatus',
                style: (_id, rowData) =>
                    PillColor.getLabelColorByStatus('OperationStatus', rowData.workOrderOperation?.status),
            }),
            resource: ui.nestedFields.reference<TimeTrackingLineInquiry, OperationTrackingLineNode, DetailedResource>({
                title: 'Resource name',
                valueField: 'name',
                bind: 'actualResource',
                isHiddenOnMainField: true,
                width: 'large',
                node: '@sage/xtrem-master-data/DetailedResource',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                ],
            }),
            resourceId: ui.nestedFields.text({ title: 'Resource ID', bind: { actualResource: { id: true } } }),
            resourceType: ui.nestedFields.text({
                bind: { actualResource: { _factory: { title: true } } },
                title: 'Resource type',
            }),
            quantity: ui.nestedFields.numeric({
                title: 'Quantity',
                bind: 'completedQuantity',
                scale: (_val, rowData) =>
                    rowData?.workOrderOperation?.workOrder?.productionItem?.releasedItem?.stockUnit?.decimalDigits ?? 0,
                postfix: (_val, rowData) =>
                    rowData?.workOrderOperation?.workOrder?.productionItem?.releasedItem?.stockUnit?.symbol ?? '',
            }),
            setupTime: ui.nestedFields.numeric({
                title: 'Setup time',
                bind: 'actualSetupTime',
                scale: (_val, rowData) => rowData?.setupTimeUnit?.decimalDigits ?? 0,
                postfix: (_val, rowData) => rowData?.setupTimeUnit?.symbol ?? '',
            }),
            runTime: ui.nestedFields.numeric({
                title: 'Run time',
                bind: 'actualRunTime',
                scale: (_val, rowData) => rowData?.runTimeUnit?.decimalDigits ?? 0,
                postfix: (_val, rowData) => rowData?.runTimeUnit?.symbol ?? '',
            }),
            completed: ui.nestedFields.checkbox({ title: 'Completed', bind: 'completed' }),
            computedAttributes: ui.nestedFields.technical({ bind: 'computedAttributes' }),
            storedAttributes: ui.nestedFields.technical({ bind: 'storedAttributes' }),
            setupTimeUnit: ui.nestedFields.reference<TimeTrackingLineInquiry, OperationTrackingLineNode, UnitOfMeasure>(
                {
                    title: 'Setup time unit',
                    valueField: 'name',
                    bind: 'setupTimeUnit',
                    node: '@sage/xtrem-master-data/UnitOfMeasure',
                    tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                    isHiddenOnMainField: true,
                    columns: [
                        ui.nestedFields.technical({ bind: 'symbol' }),
                        ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ],
                },
            ),
            runTimeUnit: ui.nestedFields.reference<TimeTrackingLineInquiry, OperationTrackingLineNode, UnitOfMeasure>({
                title: 'Run time unit',
                valueField: 'name',
                bind: 'runTimeUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            stockUnit: ui.nestedFields.reference<TimeTrackingLineInquiry, OperationTrackingLineNode, UnitOfMeasure>({
                title: 'Unit',
                valueField: 'name',
                bind: { workOrderOperation: { workOrder: { productionItem: { releasedItem: { stockUnit: true } } } } },
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            workOrderOperation: ui.nestedFields.reference<
                TimeTrackingLineInquiry,
                OperationTrackingLineNode,
                WorkOrderOperation
            >({
                bind: 'workOrderOperation',
                node: '@sage/xtrem-manufacturing/WorkOrderOperation',
                isHidden: true,
                columns: [ui.nestedFields.numeric({ bind: 'operationNumber' })],
            }),
        },
    },
})
export class TimeTrackingLineInquiry extends ui.Page<GraphApi> {}
