import type { Filter, decimal, integer } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import type { GraphApi, WorkOrder, WorkOrderReleasedItem, WorkOrderStatus } from '@sage/xtrem-manufacturing-api';
import type { Item, Location } from '@sage/xtrem-master-data-api';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { manufacturing } from '@sage/xtrem-master-data/build/lib/menu-items/manufacturing';
import type {
    Lot,
    SerialNumber,
    StockDetailStatus,
    StockDocumentTransactionStatus,
    StockStatus,
} from '@sage/xtrem-stock-data-api';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import * as StockDocumentHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-document-helper';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import { transformServerError } from '@sage/xtrem-structure/build/lib/client-functions/error-message-format';
import type { Site } from '@sage/xtrem-system-api';
import type { BillOfMaterial, Routing } from '@sage/xtrem-technical-data-api';
import * as ui from '@sage/xtrem-ui';
import { showGeneratedTrackingsMessage } from '../client-functions/material-tracking';
import * as PillColor from '../client-functions/pill-color';

interface ProductionTrackingEntry {
    number: string;
    name: string;
    status: WorkOrderStatus;
    itemId: string;
    itemName: string;
    itemDescription: string;
    releasedQuantity: decimal;
    totalActualQuantity: decimal;
    actualQuantity: decimal;
    quantityInStockUnit: decimal;
    stockDetailStatus: StockDetailStatus;
    stockStatus: StockStatus;
    location?: Location;
    lot?: Lot;
    lotId: string;
    sublot: string;
    expirationDate: DateValue;
    completed: boolean;
    materialTracking: boolean;
    timeTracking: boolean;
    site: Site;
    item: Item;
    productionItem: WorkOrderReleasedItem;
    routingCode: Routing;
    bomCode: BillOfMaterial;
    workOrder: WorkOrder;
    expirationReferenceDate: DateValue;
    potency: decimal;
    shelfLife: decimal;
    jsonStockDetails: string;
    computedAttributes: string;
    storedAttributes: string;
    storedDimensions: string;
    _id: string;
    _sortValue: string;
    stockTransactionStatus: StockDocumentTransactionStatus;
}

@ui.decorators.page<ProductionTracking>({
    menuItem: manufacturing,
    priority: 100,
    title: 'Production tracking',
    module: 'manufacturing',
    mode: 'default',
    isTransient: true,
    access: { node: '@sage/xtrem-manufacturing/ProductionTracking', bind: 'createMultipleWorkOrderTrackings' },
    businessActions() {
        return [this.createProductionTrackings];
    },
    headerDropDownActions() {
        return [this.$standardOpenCustomizationPageWizardAction];
    },
    async onLoad() {
        if (this.$.queryParameters.searchCriteria) {
            await this.setPageValues(this.$.queryParameters.searchCriteria as string);
        } else {
            await setReferenceIfSingleValue([this.woSite]);
        }
        this.$.setPageClean();
    },
    onError(error) {
        this.$.loader.isHidden = true;
        const errorMessage = transformServerError(error);
        this.$.showToast(errorMessage, { timeout: 10000, type: 'error' });
    },
})
export class ProductionTracking extends ui.Page<GraphApi> {
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    @ui.decorators.section<ProductionTracking>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    // DIALOG
    @ui.decorators.section<ProductionTracking>({
        title: 'Edit row',
        isHidden: true,
    })
    lotDialogSection: ui.containers.Section;

    // DIALOG
    @ui.decorators.gridRowBlock<ProductionTracking>({
        parent() {
            return this.lotDialogSection;
        },
        boundTo() {
            return this.workOrders;
        },
        fieldFilter(columnId: string) {
            return ['lotId', 'sublot', 'expirationDate', 'expirationReferenceDate', 'potency', 'shelfLife'].includes(
                columnId,
            );
        },
    })
    lotDialogGridRowBlock: ui.containers.GridRowBlock;

    @ui.decorators.block<ProductionTracking>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Work order criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.referenceField<ProductionTracking>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'isLocationManaged', isHidden: true }),
            ui.nestedFields.technical<ProductionTracking, Site, Location>({
                bind: 'defaultLocation',
                node: '@sage/xtrem-master-data/Location',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.technical<ProductionTracking, Site, Location>({
                bind: 'defaultStockStatus',
                node: '@sage/xtrem-stock-data/StockStatus',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
        ],
        width: 'small',
    })
    woSite: ui.fields.Reference;

    @ui.decorators.referenceField<ProductionTracking, BillOfMaterial>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From released item',
        node: '@sage/xtrem-technical-data/BillOfMaterial',
        tunnelPage: '@sage/xtrem-technical-data/BillOfMaterial',
        lookupDialogTitle: 'Select item',
        valueField: { item: { name: true } },
        helperTextField: { item: { id: true } },
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
            ui.nestedFields.reference({
                title: 'Name',
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.checkbox({ bind: 'isManufactured', isHidden: true }),
                ],
            }),
            ui.nestedFields.text({ bind: { item: { id: true } } }),
            ui.nestedFields.text({ bind: { item: { description: true } } }),
            ui.nestedFields.text({ title: 'Category', bind: { item: { category: { name: true } } } }),
            ui.nestedFields.reference<ProductionTracking, BillOfMaterial, BillOfMaterial['site']>({
                bind: 'site',
                valueField: 'name',
                node: '@sage/xtrem-system/Site',
            }),
        ],
        filter() {
            if (this.woSite.value) {
                return {
                    site: { _id: { _eq: this.woSite.value._id } },
                    item: { isManufactured: true },
                    isActive: true,
                };
            }
            return { isActive: true, item: { isManufactured: true } };
        },
    })
    itemFrom: ui.fields.Reference<BillOfMaterial>;

    @ui.decorators.referenceField<ProductionTracking, BillOfMaterial>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To released item',
        node: '@sage/xtrem-technical-data/BillOfMaterial',
        tunnelPage: '@sage/xtrem-technical-data/BillOfMaterial',
        lookupDialogTitle: 'Select item',
        valueField: { item: { name: true } },
        helperTextField: { item: { id: true } },
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
            ui.nestedFields.reference({
                title: 'Name',
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.checkbox({ bind: 'isManufactured', isHidden: true }),
                ],
            }),
            ui.nestedFields.text({ bind: { item: { id: true } } }),
            ui.nestedFields.text({ bind: { item: { description: true } } }),
            ui.nestedFields.text({ title: 'Category', bind: { item: { category: { name: true } } } }),
            ui.nestedFields.reference<ProductionTracking, BillOfMaterial, BillOfMaterial['site']>({
                bind: 'site',
                valueField: 'name',
                node: '@sage/xtrem-system/Site',
            }),
        ],
        filter() {
            if (this.woSite.value) {
                return {
                    site: { _id: { _eq: this.woSite.value._id } },
                    item: { isManufactured: true },
                    isActive: true,
                };
            }
            return { isActive: true, item: { isManufactured: true } };
        },
    })
    itemTo: ui.fields.Reference<BillOfMaterial>;

    @ui.decorators.referenceField<ProductionTracking, WorkOrder>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Work order from',
        node: '@sage/xtrem-manufacturing/WorkOrder',
        lookupDialogTitle: 'Select work order',
        valueField: 'number',
        helperTextField: 'name',
        columns: [
            ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({
                bind: 'type',
                title: 'Type',
            }),
            ui.nestedFields.reference<ProductionTracking, WorkOrder, Site>({
                bind: 'site',
                title: 'Site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({
                        bind: 'name',
                        title: 'Name',
                    }),
                    ui.nestedFields.text({
                        bind: 'id',
                        title: 'ID',
                    }),
                ],
            }),
        ],
        filter() {
            const filter = { _and: [{}] };
            if (this.woSite.value) {
                filter._and.push({ site: { _id: { _eq: this.woSite.value._id } } });
            }
            if (this.woNumberTo.value) {
                filter._and.push({ number: { _lte: this.woNumberTo.value.number } });
            }
            return filter;
        },
    })
    woNumberFrom: ui.fields.Reference;

    @ui.decorators.referenceField<ProductionTracking, WorkOrder>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Work order to',
        node: '@sage/xtrem-manufacturing/WorkOrder',
        lookupDialogTitle: 'Select work order',
        valueField: 'number',
        helperTextField: 'name',
        columns: [
            ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({
                bind: 'type',
                title: 'Type',
            }),
            ui.nestedFields.reference<ProductionTracking, WorkOrder, Site>({
                bind: 'site',
                title: 'Site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({
                        bind: 'name',
                        title: 'Name',
                    }),
                    ui.nestedFields.text({
                        bind: 'id',
                        title: 'ID',
                    }),
                ],
            }),
        ],
        filter() {
            const filter = { _and: [{}] };
            if (this.woSite.value) {
                filter._and.push({ site: { _id: { _eq: this.woSite.value._id } } });
            }
            if (this.woNumberFrom.value) {
                filter._and.push({ number: { _gte: this.woNumberFrom.value.number } });
            }
            return filter;
        },
    })
    woNumberTo: ui.fields.Reference;

    @ui.decorators.dropdownListField<ProductionTracking>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Status',
        placeholder: 'Select status',
        optionType: '@sage/xtrem-manufacturing/WorkOrderLineStatusFilteredForTracking',
        width: 'small',
        hasEmptyValue: true,
    })
    woStatus: ui.fields.DropdownList;

    @ui.decorators.dateField<ProductionTracking>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Start date from',
    })
    woStartDateFrom: ui.fields.Date;

    @ui.decorators.dateField<ProductionTracking>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Start date to',
    })
    woStartDateTo: ui.fields.Date;

    @ui.decorators.dateField<ProductionTracking>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'End date from',
    })
    woEndDateFrom: ui.fields.Date;

    @ui.decorators.dateField<ProductionTracking>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'End date to',
    })
    woEndDateTo: ui.fields.Date;

    @ui.decorators.buttonField<ProductionTracking>({
        parent() {
            return this.criteriaBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-manufacturing/search', 'Search');
        },
        width: 'small',
        async onClick() {
            this.$.loader.isHidden = false;
            await this.search();
            this.$.setPageClean();
            this.$.loader.isHidden = true;
        },
    })
    searchButton: ui.fields.Button;

    @ui.decorators.tableField<ProductionTracking>({
        title: 'Work orders',
        canSelect: true,
        canExport: true,
        isHelperTextHidden: true,
        isTransient: true,
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.text({
                bind: 'number',
                title: 'Number',
                isReadOnly: true,
                size: 'small',
            }),
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isReadOnly: true,
                size: 'medium',
            }),
            ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-manufacturing/WorkOrderStatus',
                size: 'small',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('WorkOrderStatus', rowData.status),
            }),
            ui.nestedFields.text({
                bind: 'itemName',
                title: 'Released item',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'itemId',
                title: 'Released item ID',
                isReadOnly: true,
                size: 'small',
            }),
            ui.nestedFields.text({
                bind: 'itemDescription',
                title: 'Released item description',
                isReadOnly: true,
                size: 'medium',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'releasedQuantity',
                title: 'Released qty',
                isReadOnly: true,
                size: 'small',
                scale(val, rowData) {
                    return rowData?.item?.stockUnit?.decimalDigits || 0;
                },
                postfix(val, rowData) {
                    return rowData?.item?.stockUnit?.symbol || '';
                },
            }),
            ui.nestedFields.numeric({
                bind: 'totalActualQuantity',
                title: 'Completed qty',
                isReadOnly: true,
                size: 'small',
                scale(val, rowData) {
                    return rowData?.item?.stockUnit?.decimalDigits || 0;
                },
                postfix(val, rowData) {
                    return rowData?.item?.stockUnit?.symbol || '';
                },
            }),
            ui.nestedFields.numeric({
                bind: 'actualQuantity',
                title: 'Actual qty',
                size: 'small',
                scale(val, rowData) {
                    return rowData?.item?.stockUnit?.decimalDigits || 0;
                },
                postfix(val, rowData) {
                    return rowData?.item?.stockUnit?.symbol || '';
                },
                async validation(val: number, rowData) {
                    if (await rowData.routingCode?.doSerialNumberPreGeneration) {
                        const serialDelta =
                            val - (await this.getNumberOfPredefinedSerialNumbers(await rowData.productionItem._id));
                        if (serialDelta > 0) {
                            await this.$.dialog.message(
                                'error',
                                ui.localize(
                                    '@sage/xtrem-manufacturing/pages__production_tracking__on_actual_quantity_changed_warn_title',
                                    'Actual quantity',
                                ),
                                ProductionTracking.getPredefinedSerialNumbersErrorMessage(serialDelta),
                            );
                            return ProductionTracking.getPredefinedSerialNumbersErrorMessage(serialDelta);
                        }
                    }
                    return undefined;
                },
                async onChange(_id, rowData) {
                    await this.verifyCompletedQuantity(
                        rowData.releasedQuantity,
                        rowData.totalActualQuantity,
                        rowData.actualQuantity || 0,
                    );
                    rowData.quantityInStockUnit = rowData.actualQuantity;
                    this.onRowChange(true, rowData);
                },
            }),
            ui.nestedFields.numeric({
                bind: 'quantityInStockUnit',
                isHidden: true,
                isTransient: true,
            }),
            ui.nestedFields.label({
                title: 'Stock detail status',
                bind: 'stockDetailStatus',
                isTransient: true,
                optionType: '@sage/xtrem-stock-data/StockDetailStatus',
                style: (_id, rowData) =>
                    PillColor.getLabelColorByStatus('StockDetailStatus', rowData.stockDetailStatus),
            }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-stock-data/StockStatus',
                valueField: 'name',
                bind: 'stockStatus',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({
                        bind: 'name',
                    }),
                    ui.nestedFields.text({
                        bind: 'id',
                    }),
                ],
            }),
            ui.nestedFields.reference({
                bind: 'location',
                isHidden: true,
                node: '@sage/xtrem-master-data/Location',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.reference({
                        bind: 'site',
                        node: '@sage/xtrem-system/Site',
                        valueField: 'name',
                        columns: [
                            ui.nestedFields.text({
                                bind: 'name',
                            }),
                            ui.nestedFields.text({
                                bind: 'id',
                            }),
                        ],
                    }),
                ],
                filter(rowData) {
                    return {
                        locationZone: { site: { _id: { _eq: rowData.site._id } } },
                        locationType: { locationCategory: { _ne: 'virtual' } },
                    };
                },
                isDisabled(val, rowData) {
                    return !rowData?.site.isLocationManaged;
                },
            }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-stock-data/Lot',
                valueField: 'id',
                isHidden: true,
                bind: 'lot',
                columns: [
                    ui.nestedFields.text({
                        bind: 'id',
                    }),
                    ui.nestedFields.text({
                        bind: 'sublot',
                    }),
                    ui.nestedFields.date({
                        bind: 'expirationDate',
                    }),
                ],
                filter(rowData) {
                    return { item: { _id: { _eq: rowData.item._id } } };
                },
                isDisabled(val, rowData) {
                    return rowData?.item.lotManagement === 'notManaged';
                },
                onChange(rowID, rowData) {
                    if (rowData.lot) {
                        rowData.expirationDate = rowData.lot.expirationDate;
                        this.workOrders.setRecordValue(rowData);
                    }
                },
            }),
            ui.nestedFields.text({
                bind: 'lotId',
                title: 'ID',
                size: 'small',
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.text({
                bind: 'sublot',
                title: 'Sublot',
                size: 'small',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.date({
                bind: 'expirationDate',
                isHidden: true,
            }),
            ui.nestedFields.checkbox({
                bind: 'completed',
                title: 'Completed',
                onChange(_id, rowData) {
                    if (rowData.completed) {
                        this.onRowChange(true, rowData);
                    }
                },
            }),
            ui.nestedFields.checkbox({
                bind: 'materialTracking',
                title: 'Automatic material tracking',
                isDisabled: (_value, row) => row?.bomCode === null || row?.bomCode.status === 'inDevelopment',
            }),
            ui.nestedFields.checkbox({
                bind: 'timeTracking',
                title: 'Automatic time tracking',
                isReadOnly: (_value, row) => row?.routingCode === null || row?.routingCode.status === 'inDevelopment',
            }),
            ui.nestedFields.reference({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                isHidden: true,
            }),
            ui.nestedFields.reference({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.reference({
                        bind: 'stockUnit',
                        valueField: 'symbol',
                        helperTextField: 'name',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        columns: [ui.nestedFields.text({ bind: 'id' }), ui.nestedFields.text({ bind: 'symbol' })],
                    }),
                    ui.nestedFields.text({
                        bind: 'isStockManaged',
                    }),
                    ui.nestedFields.text({
                        bind: 'lotManagement',
                    }),
                    ui.nestedFields.text({
                        bind: 'isExpiryManaged',
                    }),
                ],
            }),
            ui.nestedFields.reference({
                bind: 'workOrder',
                title: 'Work order',
                node: '@sage/xtrem-manufacturing/WorkOrder',
                valueField: 'name',
                isHidden: true,
                columns: [
                    ui.nestedFields.reference({
                        bind: 'routingCode',
                        valueField: '_id',
                        node: '@sage/xtrem-technical-data/Routing',
                        columns: [
                            ui.nestedFields.technical({
                                bind: 'status',
                            }),
                        ],
                    }),
                    ui.nestedFields.reference({
                        bind: 'bomCode',
                        valueField: '_id',
                        node: '@sage/xtrem-technical-data/BillOfMaterial',
                        columns: [
                            ui.nestedFields.technical({
                                bind: 'status',
                            }),
                        ],
                    }),
                    ui.nestedFields.technical({
                        bind: { productionItem: { _id: true } },
                    }),
                ],
            }),
            ui.nestedFields.date({
                title: 'Expiration reference date',
                bind: 'expirationReferenceDate',
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.numeric({
                bind: 'potency',
                title: 'Potency',
                size: 'small',
                isExcludedFromMainField: true,
                scale: 0,
            }),

            ui.nestedFields.numeric({
                bind: 'shelfLife',
                title: 'Shelf life',
                size: 'small',
                isExcludedFromMainField: true,
                scale: 0,
            }),

            ui.nestedFields.text({
                bind: 'jsonStockDetails',
                isHidden: true,
            }),
            ui.nestedFields.text({
                isHidden: true,
                bind: 'computedAttributes',
            }),
            ui.nestedFields.text({
                isHidden: true,
                bind: 'storedAttributes',
            }),

            ui.nestedFields.text({
                isHidden: true,
                bind: 'storedDimensions',
            }),
            ui.nestedFields.text({
                isHidden: true,
                bind: '_id',
            }),
            ui.nestedFields.technical({
                bind: 'stockTransactionStatus',
            }),
            ui.nestedFields.technical({
                bind: '_sortValue',
            }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: `Stock details`,
                async onClick(rowId: string, rowItem: ProductionTrackingEntry) {
                    await this.editStockDetails(rowId, rowItem);
                },
                isDisabled(rowId, rowData) {
                    return !rowData.actualQuantity;
                },
            },
            {
                icon: 'edit',
                title: 'Dimensions',
                async onClick(_rowId, rowItem) {
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.workOrders,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowItem as financeInterfaces.BaseDocumentLineWithAnalytical,
                            },
                            {
                                editable: ['pending', 'inProgress'].includes(rowItem.status),
                            },
                        ),
                    );
                },
            },
        ],
        onRowSelected(_id, rowData) {
            this.onRowChange(true, rowData);
        },
        onRowUnselected(_id, rowData) {
            this.onRowChange(false, rowData);
        },
    })
    workOrders: ui.fields.Table;

    private static getPredefinedSerialNumbersErrorMessage(serialDelta: integer) {
        return serialDelta > 1
            ? ui.localize(
                  '@sage/xtrem-manufacturing/pages__production_tracking__not_enough_predefined_serials_plural_error',
                  'Pre-generate {{numberSerials}} serial numbers on the work order first.',
                  { numberSerials: serialDelta },
              )
            : ui.localize(
                  '@sage/xtrem-manufacturing/pages__production_tracking__not_enough_predefined_serials_singular_error',
                  'Pre-generate {{numberSerials}} serial number on the work order first.',
                  { numberSerials: serialDelta },
              );
    }

    // check in the database how many pre-defined serial numbers exist for the WorkOrderReleasedItem linked to the
    // given productionItemId
    private async getNumberOfPredefinedSerialNumbers(productionItemId: string): Promise<integer> {
        const filter: Filter<SerialNumber> = {
            isUsable: false,
            isInStock: false,
            baseDocumentLine: { _id: { _eq: productionItemId } },
        };

        const serialNumbers = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-stock-data/SerialNumber')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true, id: true },
                        {
                            filter,
                            first: 1000,
                        },
                    ),
                )
                .execute(),
        );
        return serialNumbers.length;
    }

    async search() {
        this.workOrders.value = [];

        const results = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrder')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            number: true,
                            type: true,
                            status: true,
                            name: true,
                            startDate: true,
                            endDate: true,
                            routingCode: {
                                _id: true,
                                status: true,
                                doSerialNumberPreGeneration: true,
                            },
                            bomCode: {
                                _id: true,
                                status: true,
                            },
                            site: {
                                _id: true,
                                id: true,
                                name: true,
                                isLocationManaged: true,
                            },
                            productionItem: {
                                _id: true,
                                releasedItem: {
                                    _id: true,
                                    id: true,
                                    description: true,
                                    name: true,
                                    stockUnit: {
                                        _id: true,
                                        id: true,
                                        symbol: true,
                                        description: true,
                                        decimalDigits: true,
                                        name: true,
                                    },
                                    isStockManaged: true,
                                    lotManagement: true,
                                    isExpiryManaged: true,
                                },
                                releasedQuantity: true,
                                totalActualQuantity: true,
                                lineStatus: true,
                                storedAttributes: true,
                                storedDimensions: true,
                                computedAttributes: true,
                            },
                            productionStepOperation: {
                                completedQuantity: true,
                            },
                        },
                        {
                            filter: this.getFilter(),
                            first: 500,
                        },
                    ),
                )
                .execute(),
        );

        this.workOrders.value = results.map(values => ({
            ...values,
            itemId: values.productionItem.releasedItem.id,
            itemName: values.productionItem.releasedItem.name,
            itemDescription: values.productionItem.releasedItem.description,
            releasedQuantity: values.productionItem.releasedQuantity,
            totalActualQuantity: values.productionItem.totalActualQuantity, // Completed quantity
            actualQuantity: 0,
            quantityInStockUnit: 0,
            stockUnitDescription: values.productionItem.releasedItem.stockUnit.description,
            item: values.productionItem.releasedItem,
            completed: values.productionItem.lineStatus === 'completed',
            materialTracking: false,
            timeTracking: false,
            storedAttributes: values.productionItem.storedAttributes,
            storedDimensions: values.productionItem.storedDimensions,
            computedAttributes: values.productionItem.computedAttributes,
            stockDetailStatus: 'notRequired' as StockDetailStatus,
        }));

        this._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();

        this.$.loader.isHidden = true;
    }

    private getFilter(): Filter<WorkOrder> {
        const filter: Filter<WorkOrder> = {
            _and: [
                {
                    type: {
                        _eq: 'firm',
                    },
                },
            ],
        };

        if (this.$.isServiceOptionEnabled('phantomItemOption')) {
            filter._and?.push({
                productionComponents: { _none: true, item: { isPhantom: true }, lineStatus: { _ne: 'excluded' } },
            });
        }

        if (this.woStatus.value) {
            filter._and?.push({
                status: { _in: [this.woStatus.value] },
            });
        } else {
            filter._and?.push({
                status: { _in: ['pending', 'inProgress', 'completed'] },
            });
        }

        if (this.woSite.value) {
            filter._and?.push({
                site: {
                    _id: {
                        _eq: this.woSite.value._id,
                    },
                },
            });
        }

        if (this.woNumberFrom.value) {
            filter._and?.push({
                number: {
                    _gte: this.woNumberFrom.value.number,
                },
            });
        }

        if (this.woNumberTo.value) {
            filter._and?.push({
                number: {
                    _lte: this.woNumberTo.value.number,
                },
            });
        }

        if (this.woStartDateFrom.value) {
            filter._and?.push({
                startDate: {
                    _gte: this.woStartDateFrom.value,
                },
            });
        }
        if (this.woStartDateTo.value) {
            filter._and?.push({
                startDate: {
                    _lte: this.woStartDateTo.value,
                },
            });
        }
        if (this.woEndDateFrom.value) {
            filter._and?.push({
                endDate: {
                    _gte: this.woEndDateFrom.value,
                },
            });
        }
        if (this.woEndDateTo.value) {
            filter._and?.push({
                endDate: {
                    _lte: this.woEndDateTo.value,
                },
            });
        }

        const routingFilter: Filter<WorkOrder> = { _and: [{}] };
        const bomFilter: Filter<WorkOrder> = { _and: [{}] };
        if (this.itemFrom.value?.item) {
            routingFilter._and?.push({
                routingCode: {
                    item: {
                        id: {
                            _gte: this.itemFrom.value.item.id,
                        },
                    },
                },
            });
            bomFilter._and?.push({
                bomCode: {
                    item: {
                        id: {
                            _gte: this.itemFrom.value.item.id,
                        },
                    },
                },
            });
        }
        if (this.itemTo.value?.item) {
            routingFilter._and?.push({
                routingCode: {
                    item: {
                        id: {
                            _lte: this.itemTo.value.item.id,
                        },
                    },
                },
            });
            bomFilter._and?.push({
                bomCode: {
                    item: {
                        id: {
                            _lte: this.itemTo.value.item.id,
                        },
                    },
                },
            });
        }

        if (bomFilter._and?.length && bomFilter._and.length > 1) {
            filter._and?.push({
                _or: [bomFilter, routingFilter],
            });
        }
        return filter._and?.length ? filter : {};
    }

    onRowChange(select: boolean, rowData: ProductionTrackingEntry) {
        if (select) {
            this.workOrders.selectRecord(rowData._id);
            rowData.stockDetailStatus = StockDocumentHelper.getStockDetailStatus(
                rowData.quantityInStockUnit,
                rowData.jsonStockDetails && JSON.parse(rowData.jsonStockDetails),
                !!rowData.routingCode?.doSerialNumberPreGeneration,
            ) as StockDetailStatus;
        } else {
            this.workOrders.unselectRecord(rowData._id);
            rowData.stockDetailStatus = 'notRequired' as StockDetailStatus;
        }
        this.workOrders.setRecordValue(rowData);

        this.createProductionTrackings.isDisabled =
            !this.workOrders.selectedRecords.length ||
            this.workOrders.value.some(line => ['required'].includes(line.stockDetailStatus));
    }

    @ui.decorators.pageAction<ProductionTracking>({
        title: 'Generate',
        isDisabled: true,
        async onClick() {
            if (this.workOrders.selectedRecords.length) {
                const validation = await this.validate();
                if (validation.length) {
                    this.$.showToast(validation.join('\n'), { type: 'error' });
                } else {
                    this.$.loader.isHidden = false;
                    const getStockLineDetails = (line: ProductionTrackingEntry): string => {
                        if (line.jsonStockDetails?.length) {
                            return line.jsonStockDetails;
                        }
                        return JSON.stringify([
                            {
                                status: line.stockStatus?._id,
                                location: line.location?._id,
                                stockUnit: line.item.stockUnit._id,
                                existingLot: undefined,
                                lotCreateData: undefined,
                                quantityInStockUnit: line.actualQuantity,
                                site: line.site._id,
                                item: line.item._id,
                            },
                        ]);
                    };
                    const productionTrackings = JSON.stringify(
                        this.workOrders.value
                            .filter(v => this.workOrders.selectedRecords.indexOf(v._id) !== -1)
                            .map(workOrder => ({
                                itemCode: workOrder.item.id,
                                workOrderNumber: workOrder.number,
                                workOrderTrackingNumber: null as any,
                                trackingQuantity: workOrder.actualQuantity,
                                materialTracking: workOrder.materialTracking || false,
                                timeTracking: workOrder.timeTracking || false,
                                completed: workOrder.completed || false,
                                stockDetails: getStockLineDetails(workOrder as ProductionTrackingEntry),
                                storedAttributes: workOrder.storedAttributes,
                                storedDimensions: workOrder.storedDimensions,
                            })),
                    );
                    const result = await this.$.graph
                        .node('@sage/xtrem-manufacturing/ProductionTracking')
                        .mutations.createMultipleWorkOrderTrackings(
                            { numberTrackings: true, message: true },
                            {
                                trackings: productionTrackings,
                                trackingDate: DateValue.today().toString(),
                            },
                        )
                        .execute();
                    this.$.loader.isHidden = true;
                    if (result.message.length) {
                        this.$.showToast(result.message, { timeout: 10000, type: 'warning' });
                    }
                    if (result.numberTrackings) {
                        showGeneratedTrackingsMessage(this, result.numberTrackings);
                        this.$.finish();
                        this.$.setPageClean();
                        const searchCriteria = {
                            woSite: this.woSite.value,
                            itemFrom: this.itemFrom.value,
                            itemTo: this.itemTo.value,
                            woNumberFrom: this.woNumberFrom.value,
                            woNumberTo: this.woNumberTo.value,
                            woStatus: this.woStatus.value,
                            woStartDateFrom: this.woStartDateFrom.value,
                            woStartDateTo: this.woStartDateTo.value,
                            woEndDateFrom: this.woEndDateFrom.value,
                            woEndDateTo: this.woEndDateTo.value,
                        };
                        this.$.router.goTo('@sage/xtrem-manufacturing/ProductionTracking', {
                            searchCriteria: JSON.stringify(searchCriteria),
                        });
                    }
                }
            }
        },
        buttonType: 'primary',
    })
    createProductionTrackings: ui.PageAction;

    private async setPageValues(searchValues: string) {
        const parsedSearchValues = JSON.parse(searchValues);
        this.woSite.value = parsedSearchValues.woSite;
        this.itemFrom.value = parsedSearchValues.itemFrom;
        this.itemTo.value = parsedSearchValues.itemTo;
        this.woNumberFrom.value = parsedSearchValues.woNumberFrom;
        this.woNumberTo.value = parsedSearchValues.woNumberTo;
        this.woStatus.value = parsedSearchValues.woStatus;
        this.woStartDateFrom.value = parsedSearchValues.woStartDateFrom;
        this.woStartDateTo.value = parsedSearchValues.woStartDateTo;
        this.woEndDateFrom.value = parsedSearchValues.woEndDateFrom;
        this.woEndDateTo.value = parsedSearchValues.woEndDateTo;

        this.$.loader.isHidden = false;
        await this.search();
        this.$.loader.isHidden = true;
    }

    /**
     * Stock detail management.
     * Calls the StockReceiptDetailsPanel.
     * For the user to be able to enter the stock detail for a document line
     * Updates the stockDetails line property
     * @param lineData
     */
    private async editStockDetails(rowId: string, rowItem: ProductionTrackingEntry) {
        // if we need to have pre-generated serial numbers and the actual quantity entered exceeds the number of
        // pre-generated serial numbers => we show an error and do not open the stock receipt detail panel
        let editAllowed = true;
        if (rowItem.routingCode?.doSerialNumberPreGeneration) {
            const serialDelta =
                rowItem.actualQuantity - (await this.getNumberOfPredefinedSerialNumbers(rowItem.productionItem._id));
            if (serialDelta > 0) {
                editAllowed = false;
                await this.$.dialog.message(
                    'error',
                    ui.localize(
                        '@sage/xtrem-manufacturing/pages__production_tracking__on_actual_quantity_changed_warn_title',
                        'Actual quantity',
                    ),
                    ProductionTracking.getPredefinedSerialNumbersErrorMessage(serialDelta),
                );
            }
        }
        if (editAllowed) {
            const line = rowItem;

            // When serial numbers have to be pregenerated > add parameter 'baseDocumentSysId' to edit call
            const newLine = (await MasterDataUtils.applyPanelToLineIfChanged(
                this.workOrders,
                StockDetailHelper.editStockDetails(this, line, {
                    movementType: 'receipt',
                    data: {
                        isEditable: true,
                        fieldCustomizations: {
                            supplierLot: {
                                isHidden: true,
                            },
                        },
                        effectiveDate: DateValue.today().toString(),
                        documentLineSortValue: Number(rowItem._sortValue),
                        documentLine: `${Number(rowId) * -1}`,
                        jsonStockDetails: rowItem.jsonStockDetails,
                        item: rowItem.item._id,
                        stockSite: rowItem.site._id,
                        quantity: +rowItem.actualQuantity,
                        unit: rowItem.item.stockUnit?._id,
                        trackCheckStock: 'track',
                        stockStatus:
                            rowItem.stockStatus?._id ||
                            (await StockDataUtils.getDefaultQuality({
                                defaultQualityType: 'completedProductDefaultQuality',
                                page: this,
                                itemId: rowItem.item._id || '',
                                site: this.woSite.value,
                            })),
                        location:
                            rowItem.location?._id ||
                            (await StockDataUtils.getDefaultLocation({
                                defaultLocationType: 'completedProductDefaultLocation',
                                page: this,
                                itemId: line.item?._id || '',
                                site: this.woSite.value,
                            })),
                        stockTransactionStatus: rowItem.stockTransactionStatus,
                        preGeneratedSerialNumbers: rowItem.routingCode?.doSerialNumberPreGeneration,
                        baseDocumentLineSysId: Number(rowItem.productionItem._id),
                        serialNumberParameters: {
                            isNotUsableAllowed: true,
                        },
                    },
                }) as Promise<ProductionTrackingEntry>,
            )) as ProductionTrackingEntry;

            if (newLine) this.onRowChange(true, newLine);
        }
    }

    /**
     * Validates the page (including mandatory stock fields on the table)
     * @returns string array (promise of) with all the error messages
     */
    async validate(): Promise<string[]> {
        // TODO: remove quantity, status and location check when isMandatory on tables is working
        const validation = await this.$.page.validate();

        this.workOrders.selectedRecords.forEach(selectedWorkOrderId => {
            const workOrder = this.workOrders.getRecordValue(selectedWorkOrderId);
            if (!workOrder) return;
            if (!workOrder.jsonStockDetails?.length) {
                if (!workOrder.stockStatus) {
                    validation.push(
                        ui.localize(
                            '@sage/xtrem-manufacturing/pages__production_tracking__notification__The_stock_status_is_mandatory',
                            'The stock status is mandatory.',
                        ),
                    );
                }

                if (workOrder.site.isLocationManaged && !workOrder.location) {
                    validation.push(
                        ui.localize(
                            '@sage/xtrem-manufacturing/pages__production_tracking__notification__The_location_is_mandatory',
                            'The location is mandatory.',
                        ),
                    );
                }

                if (workOrder.item.isExpiryManaged) {
                    if (workOrder.expirationDate) {
                        if (Date.parse(workOrder.expirationDate) < Date.now()) {
                            validation.push(
                                ui.localize(
                                    '@sage/xtrem-manufacturing/pages__production_tracking__expiration_date_cannot_be_past',
                                    'The expiration date cannot be earlier than today.',
                                ),
                            );
                        }
                    } else {
                        validation.push(
                            ui.localize(
                                '@sage/xtrem-manufacturing/pages__production_tracking__expiration_date_is_mandatory',
                                'The expiration date is mandatory',
                            ),
                        );
                    }
                }
            }
        });

        return validation;
    }

    private async verifyCompletedQuantity(
        releasedQuantity: number,
        totalActualQuantity: number,
        actualQuantity: number,
    ) {
        if (totalActualQuantity + actualQuantity > releasedQuantity) {
            await this.$.dialog.message(
                'warn',
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__production_tracking__on_actual_quantity_changed_warn_title',
                    'Actual quantity',
                ),
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__production_tracking__on_actual_quantity_changed_warn',
                    'The total actual quantity will be greater than the released quantity.',
                ),
            );
        }
    }
}
