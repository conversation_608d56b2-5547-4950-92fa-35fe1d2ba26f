import { asyncArray } from '@sage/xtrem-async-helper';
import type { Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import type { GraphApi, WorkOrder, WorkOrderComponent } from '@sage/xtrem-manufacturing-api';
import type { Item, Location } from '@sage/xtrem-master-data-api';
import * as common from '@sage/xtrem-master-data/build/lib/client-functions/common';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { manufacturing } from '@sage/xtrem-master-data/build/lib/menu-items/manufacturing';
import type { decimal } from '@sage/xtrem-shared';
import type { AllocationRequestStatus, Lot, StockAllocationStatus, StockStatus } from '@sage/xtrem-stock-data-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import type { Site } from '@sage/xtrem-system-api';
import type { BillOfMaterial } from '@sage/xtrem-technical-data-api';
import * as ui from '@sage/xtrem-ui';
import { setAllocationStatus, showGeneratedTrackingsMessage } from '../client-functions/material-tracking';
import * as PillColor from '../client-functions/pill-color';
import type { tracking } from './shared';

/** To be able to type the component table request from WorkOrderComponent & others properties  */
type WorkOrderComponentTable = WorkOrderComponent & {
    /** Math.max(requiredQuantity - consumedQuantity , +0) */
    remainingQuantity: number;
    /** allocation details */
    jsonStockDetails: any;
    allocationStatus: StockAllocationStatus;
    stockStatus: StockStatus;
    site: Site;
    location: Location;
    lot: Lot;
    /** Unit  */
    unitName: string;
    quantityInStockUnit: decimal;
    expectedQuantity: decimal;
    itemDescription: string;
    /** WorkOrder status  */
    status: string;
    allocationRequestStatus: AllocationRequestStatus;
    initialAllocationStatus: StockAllocationStatus;
};
@ui.decorators.page<MaterialTracking>({
    menuItem: manufacturing,
    priority: 150,
    title: 'Material tracking',
    module: 'manufacturing',
    mode: 'default',
    isTransient: true,
    access: { node: '@sage/xtrem-manufacturing/MaterialTracking', bind: 'createMultipleMaterialTrackings' },
    businessActions() {
        return [this.createTrackings];
    },
    headerDropDownActions() {
        return [this.$standardOpenCustomizationPageWizardAction];
    },
    async onLoad() {
        this.addComponent.isDisabled = true;
        if (this.$.queryParameters.searchCriteria) {
            await this.setPageValues(JSON.parse(this.$.queryParameters.searchCriteria as string));
        } else {
            await setReferenceIfSingleValue([this.woSite]);
        }
        this.createTrackings.isDisabled = true; // for the case where the page is reloading after saving
    },
    onError(error) {
        this.$.showToast(error.message, { timeout: 10000, type: 'error' });
    },
})
export class MaterialTracking extends ui.Page<GraphApi> implements financeInterfaces.PageWithDefaultDimensions {
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    get trackingType(): tracking.TrackingTypes | null {
        const type = this.$.queryParameters.trackingType as string;
        return type as tracking.TrackingTypes;
    }

    @ui.decorators.section<MaterialTracking>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<MaterialTracking>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Material criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.block<MaterialTracking>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Work order components',
    })
    mainBlock: ui.containers.Block;

    /**
     * perform some updates when a change occurs in the line of the component grid:
     * - tick or untick the row
     * - enable or disable the "Save" button
     * @param isSelected indicate if the row must be selected or unselected
     * @param rowData the values of the row
     */
    async onRowChange(
        isSelected: boolean,
        rowData: WorkOrderComponentTable | ui.PartialNodeWithId<WorkOrderComponentTable>,
    ) {
        // allocation status pill is only displayed for selected lines
        let allocationStatus: StockAllocationStatus | undefined;
        if (isSelected) {
            this.components.selectRecord(rowData._id);
            allocationStatus = await setAllocationStatus(
                this,
                rowData._id,
                rowData.jsonStockDetails,
                rowData.remainingQuantity,
            );
        } else {
            this.components.unselectRecord(rowData._id);
            allocationStatus = undefined;
        }
        this.components.setRecordValue({
            _id: rowData._id,
            allocationStatus,
            allocationRequestStatus: rowData.allocationRequestStatus !== 'inProgress' ? 'noRequest' : 'inProgress',
        });

        /** Page action save  */
        this.createTrackings.isDisabled =
            !this.components.selectedRecords.length ||
            this.components.value.some(
                line =>
                    this.components.selectedRecords.includes(line._id) &&
                    !['allocated'].includes(line.allocationStatus),
            );
    }

    @ui.decorators.pageAction<MaterialTracking>({
        title: 'Generate',
        onError(error: string | (Error & { errors: Array<any> })) {
            return MasterDataUtils.formatError(this, error);
        },
        async onClick() {
            if (this.components.selectedRecords.length) {
                const validation = await this.$.page.validate();
                if (validation.length) {
                    this.$.showToast(validation.join('\n'), { type: 'error' });
                } else {
                    this.$.loader.isHidden = false;

                    const trackings = this.components.value
                        .filter(component => this.components.selectedRecords.indexOf(component._id) !== -1)
                        .map(line => {
                            const allocationUpdates = line.jsonStockDetails ? JSON.parse(line.jsonStockDetails) : null;
                            return {
                                workOrder: line.workOrder?._id,
                                componentNumber: line.componentNumber || null,
                                item: line.item?._id,
                                remainingQuantity: line.remainingQuantity,
                                storedDimensions: line.storedDimensions || null,
                                storedAttributes: line.storedAttributes || null,
                                isCompleted: false,
                                allocationUpdates,
                            };
                        });

                    const result = await this.$.graph
                        .node('@sage/xtrem-manufacturing/MaterialTracking')
                        .mutations.createMultipleMaterialTrackings(
                            { numberTrackings: true, concatenatedMessages: true },
                            { data: trackings },
                        )
                        .execute();

                    if (result.concatenatedMessages.length) {
                        this.$.showToast(
                            result.concatenatedMessages
                                .split('\n')
                                .slice(0, -1)
                                .map((e: string) => '- '.concat(e, '\n\n'))
                                .join(''),
                            { type: 'warning', timeout: 10000 },
                        );
                    }

                    if (result.numberTrackings) {
                        showGeneratedTrackingsMessage(this, result.numberTrackings);
                        this.$.finish();
                        this.$.setPageClean();
                        const searchCriteria = JSON.stringify({
                            woSite: this.woSite.value,
                            itemFrom: this.itemFrom.value,
                            itemTo: this.itemTo.value,
                            woFrom: this.woFrom.value,
                            woTo: this.woTo.value,
                            woStartDateFrom: this.woStartDateFrom.value,
                            woStartDateTo: this.woStartDateTo.value,
                            status: this.materialStatus.value,
                        });

                        this.$.router.goTo('@sage/xtrem-manufacturing/MaterialTracking', { searchCriteria });
                    }

                    this.$.loader.isHidden = true;
                }
            }
        },
        buttonType: 'primary',
    })
    createTrackings: ui.PageAction;

    @ui.decorators.pageAction<MaterialTracking>({
        icon: 'add',
        title: 'Add component',
        onClick() {
            const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                { _id: this.components.generateRecordId() },
                this._defaultDimensionsAttributes,
            );
            this.components.value = this.components.value.concat(line);
        },
    })
    addComponent: ui.PageAction;

    @ui.decorators.referenceField<MaterialTracking, Site>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isLocationManaged' }),
        ],
        width: 'small',
    })
    woSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<MaterialTracking, WorkOrder>({
        node: '@sage/xtrem-manufacturing/WorkOrder',
        lookupDialogTitle: 'Select work order',
        valueField: 'number',
        helperTextField: 'name',
        title: 'Work order from',
        columns: [
            ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'type', title: 'Type' }),
            ui.nestedFields.reference<MaterialTracking, WorkOrder, Site>({
                bind: 'site',
                title: 'Site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                ],
            }),
        ],
        filter() {
            return this.woSite.value ? { site: { _id: { _eq: this.woSite.value._id } } } : {};
        },
        parent() {
            return this.criteriaBlock;
        },
    })
    woFrom: ui.fields.Reference<WorkOrder>;

    @ui.decorators.referenceField<MaterialTracking, WorkOrder>({
        node: '@sage/xtrem-manufacturing/WorkOrder',
        title: 'Work order to',
        lookupDialogTitle: 'Select work order',
        valueField: 'number',
        helperTextField: 'name',
        columns: [
            ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'type', title: 'Type' }),
            ui.nestedFields.reference<MaterialTracking, WorkOrder, Site>({
                bind: 'site',
                title: 'Site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                ],
            }),
        ],
        filter() {
            return {
                ...(this.woSite.value?._id && { site: { _id: { _eq: this.woSite.value._id } } }),
                ...(this.woFrom.value?.number && { number: { _gte: this.woFrom.value.number } }),
            };
        },
        parent() {
            return this.criteriaBlock;
        },
    })
    woTo: ui.fields.Reference<WorkOrder>;

    @ui.decorators.dateField<MaterialTracking>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Start date from',
    })
    woStartDateFrom: ui.fields.Date;

    @ui.decorators.dateField<MaterialTracking>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Start date to',
    })
    woStartDateTo: ui.fields.Date;

    @ui.decorators.referenceField<MaterialTracking, BillOfMaterial>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From released item',
        node: '@sage/xtrem-technical-data/BillOfMaterial',
        tunnelPage: '@sage/xtrem-technical-data/BillOfMaterial',
        lookupDialogTitle: 'Select item',
        valueField: { item: { name: true } },
        helperTextField: { item: { id: true } },
        columns: [
            ui.nestedFields.reference<MaterialTracking, BillOfMaterial, BillOfMaterial['item']>({
                title: 'Name',
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'isManufactured' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.text({ bind: { item: { id: true } } }),
            ui.nestedFields.text({ bind: { item: { description: true } } }),
            ui.nestedFields.text({ title: 'Category', bind: { item: { category: { name: true } } } }),
            ui.nestedFields.reference<MaterialTracking, BillOfMaterial, BillOfMaterial['site']>({
                bind: 'site',
                valueField: 'name',
                node: '@sage/xtrem-system/Site',
            }),
        ],
        filter() {
            return this.itemFilter();
        },
    })
    itemFrom: ui.fields.Reference<BillOfMaterial>;

    itemFilter() {
        return {
            ...(this.woSite.value ? { site: { _id: { _eq: this.woSite.value._id } } } : {}),
            item: { isManufactured: true },
            isActive: true,
        };
    }

    @ui.decorators.referenceField<MaterialTracking, BillOfMaterial>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To released item',
        node: '@sage/xtrem-technical-data/BillOfMaterial',
        tunnelPage: '@sage/xtrem-technical-data/BillOfMaterial',
        lookupDialogTitle: 'Select item',
        valueField: { item: { name: true } },
        helperTextField: { item: { id: true } },
        columns: [
            ui.nestedFields.reference<MaterialTracking, BillOfMaterial, BillOfMaterial['item']>({
                title: 'Name',
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'isManufactured' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.text({ bind: { item: { id: true } } }),
            ui.nestedFields.text({ bind: { item: { description: true } } }),
            ui.nestedFields.text({ title: 'Category', bind: { item: { category: { name: true } } } }),
            ui.nestedFields.reference<MaterialTracking, BillOfMaterial, BillOfMaterial['site']>({
                bind: 'site',
                valueField: 'name',
                node: '@sage/xtrem-system/Site',
            }),
        ],
        filter() {
            return this.itemFilter();
        },
    })
    itemTo: ui.fields.Reference<BillOfMaterial>;

    @ui.decorators.dropdownListField<MaterialTracking>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Status',
        placeholder: 'Select status',
        optionType: '@sage/xtrem-manufacturing/WorkOrderLineStatusFilteredForTracking',
        width: 'small',
        hasEmptyValue: true,
    })
    materialStatus: ui.fields.DropdownList;

    @ui.decorators.buttonField<MaterialTracking>({
        parent() {
            return this.criteriaBlock;
        },
        map: () => ui.localize('@sage/xtrem-manufacturing/search', 'Search'),
        width: 'small',
        async onClick() {
            this.$.loader.isHidden = false;
            await this.search();
            this.$.loader.isHidden = true;
        },
    })
    searchButton: ui.fields.Button;

    @ui.decorators.checkboxField<MaterialTracking>({
        parent() {
            return this.mainBlock;
        },
        title: 'Select all',
        async onChange() {
            this.$.loader.isHidden = false;
            await this.selectUnselectCheckboxes();
            this.$.loader.isHidden = true;
        },
    })
    selectAllCheckbox: ui.fields.Checkbox;

    @ui.decorators.tableField<MaterialTracking, WorkOrderComponentTable>({
        title: 'Work order components',
        isTitleHidden: true,
        canExport: true,
        isHelperTextHidden: true,
        parent() {
            return this.mainBlock;
        },
        columns: [
            ui.nestedFields.technical<MaterialTracking, WorkOrderComponentTable, Site>({
                node: '@sage/xtrem-system/Site',
                bind: 'site',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
            }),
            ui.nestedFields.reference<MaterialTracking, WorkOrderComponentTable, WorkOrder>({
                node: '@sage/xtrem-manufacturing/WorkOrder',
                bind: 'workOrder',
                title: 'Number',
                valueField: 'number',
                isReadOnly: (_value, row) => row?.componentNumber > 0,
                size: 'small',
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'Number', bind: 'number' }),
                    ui.nestedFields.technical({
                        bind: 'site',
                        node: '@sage/xtrem-system/Site',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'isLocationManaged' }),
                        ],
                    }),
                ],
                /** why do we have to force the type filter there ?  */
                filter(): Filter<WorkOrder> {
                    return { _or: [{ bomCode: undefined }, { bomCode: { status: { _ne: 'inDevelopment' } } }] };
                },
                async onChange(_id, row) {
                    if (row.workOrder) {
                        row.name = row.workOrder.name;
                        row.site = row.workOrder.site;
                        row.lineStatus = 'pending';
                        this.components.addOrUpdateRecordValue(row);
                    }
                    await this.onRowChange(true, row);
                },
            }),
            ui.nestedFields.text<MaterialTracking, WorkOrderComponentTable>({
                bind: 'name',
                title: 'Name',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric<MaterialTracking, WorkOrderComponentTable>({
                bind: 'componentNumber',
                title: 'Line',
                isReadOnly: true,
                size: 'small',
            }),
            ui.nestedFields.label<MaterialTracking, WorkOrderComponentTable>({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-manufacturing/WorkOrderStatus',
                size: 'small',
                style: (_id, rowData) =>
                    rowData?.status
                        ? PillColor.getLabelColorByStatus('WorkOrderStatus', rowData?.status as any)
                        : PillColor.getTransparentStatusStyle(),
            }),
            ui.nestedFields.reference<MaterialTracking, WorkOrderComponentTable, Item>({
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                bind: 'item',
                title: 'Component',
                isReadOnly: (_value, row) => row?.componentNumber > 0,
                size: 'small',
                valueField: 'id',
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.technical({ bind: 'isExpiryManaged' }),
                    ui.nestedFields.reference({
                        title: 'Unit',
                        bind: 'stockUnit',
                        valueField: 'symbol',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                        columns: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.numeric({ title: 'Decimal digits', bind: 'decimalDigits' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                ],
                filter() {
                    return { isStockManaged: true } as Filter<Item>;
                },
                async onChange(_id, row) {
                    if (row.item) {
                        row.itemDescription = row.item.name;
                        row.unitName = row.item.stockUnit.id;
                        row.jsonStockDetails = null;
                        this.components.addOrUpdateRecordValue(row);
                    }
                    await this.onRowChange(true, row);
                },
            }),
            ui.nestedFields.text<MaterialTracking, WorkOrderComponentTable>({
                bind: 'itemDescription',
                title: 'Component description',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric<MaterialTracking, WorkOrderComponentTable>({
                bind: 'expectedQuantity',
                title: 'Expected',
                isReadOnly: true,
                size: 'small',
                scale: (_val, rowData) => rowData?.item?.stockUnit?.decimalDigits || 0,
                postfix: (_val, rowData) => rowData?.item?.stockUnit?.symbol || '',
            }),
            ui.nestedFields.numeric<MaterialTracking, WorkOrderComponentTable>({
                bind: 'consumedQuantity',
                title: 'Consumed',
                isReadOnly: true,
                size: 'small',
                scale: (_val, rowData) => rowData?.item?.stockUnit?.decimalDigits || 0,
                postfix: (_val, rowData) => rowData?.item?.stockUnit?.symbol || '',
            }),
            ui.nestedFields.technical<MaterialTracking, WorkOrderComponentTable>({
                bind: 'remainingQuantityToAllocate',
            }),
            ui.nestedFields.numeric<MaterialTracking, WorkOrderComponentTable>({
                bind: 'remainingQuantity',
                title: 'Actual quantity',
                size: 'small',
                scale: (_val, rowData) => rowData?.item?.stockUnit?.decimalDigits || 0,
                postfix: (_val, rowData) => rowData?.item?.stockUnit?.symbol || '',
                validation: val => common.valueMustBePositive(val),
                async onChange(_id, rowData) {
                    rowData.quantityInStockUnit = rowData.remainingQuantity;
                    this.components.setRecordValue(rowData);
                    await this.onRowChange(true, rowData);
                },
            }),
            ui.nestedFields.technical<MaterialTracking, WorkOrderComponentTable>({
                bind: 'quantityInStockUnit',
                isTransient: true,
            }),
            ui.nestedFields.label({
                title: 'Allocation status',
                bind: 'allocationStatus',
                optionType: '@sage/xtrem-stock-data/StockAllocationStatus',
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus('StockAllocationStatus', rowData.allocationStatus),
            }),
            ui.nestedFields.technical<MaterialTracking, WorkOrderComponentTable, StockStatus>({
                node: '@sage/xtrem-stock-data/StockStatus',
                bind: 'stockStatus',
                nestedFields: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
            ui.nestedFields.technical<MaterialTracking, WorkOrderComponentTable, Location>({
                bind: 'location',
                node: '@sage/xtrem-master-data/Location',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({
                        bind: 'site',
                        node: '@sage/xtrem-system/Site',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<MaterialTracking, WorkOrderComponentTable, Lot>({
                node: '@sage/xtrem-stock-data/Lot',
                bind: 'lot',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'sublot' }),
                    ui.nestedFields.technical({ bind: 'expirationDate' }),
                ],
            }),
            ui.nestedFields.technical<MaterialTracking, WorkOrderComponentTable>({ bind: 'unitName' }),
            ui.nestedFields.technical<MaterialTracking, WorkOrderComponentTable>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'isLocationManaged' }),
                ],
            }),
            ui.nestedFields.technical<MaterialTracking, WorkOrderComponentTable>({ bind: 'computedAttributes' }),
            ui.nestedFields.technical<MaterialTracking, WorkOrderComponentTable>({ bind: 'storedAttributes' }),
            ui.nestedFields.technical<MaterialTracking, WorkOrderComponentTable>({ bind: 'storedDimensions' }),
            ui.nestedFields.technical<MaterialTracking, WorkOrderComponentTable>({
                isTransientInput: true,
                bind: 'jsonStockDetails',
            }),
            ui.nestedFields.technical<MaterialTracking, WorkOrderComponentTable>({
                isTransientInput: true,
                bind: 'allocationRequestStatus',
            }),
            ui.nestedFields.technical<MaterialTracking, WorkOrderComponentTable>({
                isTransient: true,
                bind: 'initialAllocationStatus',
            }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Allocate stock',
                isHidden(_rowId, rowItem) {
                    return (
                        !(rowItem.site && rowItem.item && rowItem.remainingQuantity) ||
                        rowItem.allocationRequestStatus === 'inProgress'
                    );
                },
                async onClick(rowId: string, rowItem) {
                    await this.editAllocations(rowId, rowItem);
                },
            },
            {
                icon: 'edit',
                title: 'Dimensions',
                async onClick(rowId, rowItem) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.components,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            { documentLine: rowData },
                            {
                                editable: ['pending', 'inProgress'].includes(rowItem.status),
                            },
                        ),
                    );
                },
            },
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isHidden: rowId => Number(rowId) > 0,
                async onClick(rowId, rowItem) {
                    await this.onRowChange(false, rowItem);
                    this.components.removeRecord(rowId);
                },
            },
        ],
        fieldActions() {
            return [this.addComponent, this.defaultDimension];
        },
        async onRowSelected(_id, rowData) {
            await this.onRowChange(true, rowData);
        },
        async onRowUnselected(_id, rowData) {
            await this.onRowChange(false, rowData);
        },
    })
    components: ui.fields.Table<WorkOrderComponentTable>;

    private async selectUnselectCheckboxes() {
        if (this.components.value.length > 0) {
            await asyncArray(this.components.value).forEach(async row => {
                if (this.selectAllCheckbox.value) {
                    this.components.selectRecord(row._id);
                    await this.onRowChange(true, row);
                } else {
                    this.components.unselectRecord(row._id);
                    await this.onRowChange(false, row);
                }
            });
        }
    }

    @ui.decorators.pageAction<MaterialTracking>({
        icon: 'arrow_right',
        title: 'Set default dimensions',
        async onClick() {
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.components,
                this._defaultDimensionsAttributes,
            );
        },
    })
    defaultDimension: ui.PageAction;

    private async setPageValues(searchValues: any) {
        this.woSite.value = searchValues.woSite;
        this.itemFrom.value = searchValues.itemFrom;
        this.itemTo.value = searchValues.itemTo;
        this.woFrom.value = searchValues.woFrom;
        this.woTo.value = searchValues.woTo;
        this.woStartDateFrom.value = searchValues.woStartDateFrom;
        this.woStartDateTo.value = searchValues.woStartDateTo;
        if (searchValues.status) {
            this.materialStatus.value = searchValues.status;
        }
        this.$.loader.isHidden = false;
        await this.search();
        this.$.loader.isHidden = true;
    }

    async search() {
        this.components.value = [];

        const results = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrderComponent')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            lineType: true,
                            lineStatus: true,
                            allocationStatus: true,
                            componentNumber: true,
                            name: true,
                            requiredQuantity: true,
                            consumedQuantity: true,
                            remainingQuantityToAllocate: true,
                            allocationRequestStatus: true,
                            workOrder: {
                                _id: true,
                                number: true,
                                name: true,
                                startDate: true,
                                site: { _id: true, id: true, name: true, isLocationManaged: true },
                            },
                            item: {
                                _id: true,
                                id: true,
                                name: true,
                                lotManagement: true,
                                isExpiryManaged: true,
                                stockUnit: {
                                    _id: true,
                                    id: true,
                                    symbol: true,
                                    description: true,
                                    decimalDigits: true,
                                    name: true,
                                },
                                isStockManaged: true,
                            },
                            unit: { _id: true, id: true, symbol: true, decimalDigits: true, name: true },
                            storedAttributes: true,
                            storedDimensions: true,
                            computedAttributes: true,
                        },
                        { filter: this.getFilter(), first: 500 },
                    ),
                )
                .execute(),
        );

        this.components.value = results.map(value => {
            const remaining =
                Number.parseFloat(value.requiredQuantity.toString()) -
                Number.parseFloat(value.consumedQuantity.toString());
            const quantityToTrack = Math.max(remaining, +0);
            return {
                ...MasterDataUtils.removeExtractEdgesPartial(value),
                workOrder: MasterDataUtils.removeExtractEdgesPartial({
                    ...value.workOrder,
                    site: MasterDataUtils.removeExtractEdgesPartial(value.workOrder.site),
                }),
                stockUnit: MasterDataUtils.removeExtractEdgesPartial({
                    ...value.item,
                    stockUnit: MasterDataUtils.removeExtractEdgesPartial(value.item.stockUnit),
                }),
                unit: MasterDataUtils.removeExtractEdgesPartial(value.unit),
                lineNumber: value.componentNumber,
                name: value.workOrder.name,
                itemDescription: value.item.name,
                expectedQuantity: Number.parseFloat(value.requiredQuantity) as number,
                consumedQuantity: value.consumedQuantity,
                remainingQuantity: quantityToTrack,
                quantityInStockUnit: quantityToTrack,
                remainingQtyToAllocate: value.remainingQuantityToAllocate,
                allocationStatus: undefined as any,
                initialAllocationStatus: value.allocationStatus,
                unitName: value.unit.name,
                status: value.lineStatus,
                site: MasterDataUtils.removeExtractEdgesPartial(value.workOrder.site),
                storedAttributes: value.storedAttributes,
                storedDimensions: value.storedDimensions,
                computedAttributes: value.computedAttributes,
            };
        }) as ui.PartialNodeWithId<WorkOrderComponentTable>[];

        this._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();
        this.selectAllCheckbox.value = false;
        this.components.unselectAllRecords();
        this.addComponent.isDisabled = false;

        this.$.loader.isHidden = true;
    }

    getFilter(): Filter<WorkOrderComponent> {
        const workOrderNumber = {
            ...(this.woFrom.value?.number && { _gte: this.woFrom.value.number.valueOf() }),
            ...(this.woTo.value?.number && { _lte: this.woTo.value.number.valueOf() }),
        };
        const workOrderDate = {
            ...(this.woStartDateFrom.value && { _gte: this.woStartDateFrom.value }),
            ...(this.woStartDateTo.value && { _lte: this.woStartDateTo.value }),
        };
        const itemId = {
            ...(this.itemFrom.value?.item && { _gte: this.itemFrom.value.item.id }),
            ...(this.itemTo.value?.item && { _lte: this.itemTo.value.item.id }),
        };
        const filter: Filter<WorkOrderComponent> = { _and: [{}] };

        if (this.$.isServiceOptionEnabled('phantomItemOption')) {
            filter._and?.push({
                item: { isPhantom: false },
            });
        }
        /**
         * need to fix this => https://jira.sage.com/browse/XT-67917
         * { _ne: null } as any
         *  */
        filter._and?.push({
            workOrder: {
                type: { _eq: 'firm' },
                status: { _in: ['pending', 'inProgress', 'completed'] },

                bomCode: { _or: [{ status: { _ne: 'inDevelopment' } }, { _ne: null } as any] },
                ...(this.woSite.value && { site: { _id: { _eq: this.woSite.value._id } } }),
                number: workOrderNumber,
                startDate: workOrderDate,
                routingCode: { item: { id: itemId } },
            },
            allocationRequestStatus: { _ne: 'inProgress' },
            lineStatus: {
                _in: this.materialStatus.value ? [this.materialStatus.value] : ['pending', 'inProgress', 'completed'],
            },
            lineType: { _eq: 'normal' },
            item: { _ne: null } as any,
        });
        return filter;
    }

    /**
     * Allocation management.
     * Calls the StockAllocationDetailsPanel.
     * Updates the jsonStockDetails (to update allocations)
     * @param lineData
     */
    async editAllocations(rowId: string, rowItem: ui.PartialNodeWithId<WorkOrderComponentTable>) {
        // In this context:
        // - document line is material tracking line
        // - order document line is work order component
        // Work order component does not exist yet for a new tracking line => no allocation proposal in this case
        // For an existing tracking line, allocation proposal is done when allocation status is empty or not allocated
        const modifiedLine = await MasterDataUtils.applyPanelToLineIfChanged(
            this.components,
            StockDetailHelper.editStockDetails(this, rowItem, {
                movementType: 'allocation',
                data: {
                    isEditable: true,
                    isReturningDetails: true,
                    needFullAllocation: false,
                    needAllocationProposal:
                        Number(rowId) < 0 ? false : !rowItem.jsonStockDetails || rowItem.jsonStockDetails === '[]',
                    cannotOverAllocate: true,
                    jsonStockDetails: rowItem.jsonStockDetails,
                    documentLineHasAlreadySomeAllocations: Number(rowItem.quantityAllocated) > 0,
                    documentLineSortValue: rowItem._sortValue,
                    documentLine: '-1',
                    orderDocumentLine: Number(rowId) < 0 ? undefined : rowId,
                    item: rowItem.item?._id,
                    stockSite: rowItem.site?._id,
                    quantity: rowItem.remainingQuantity || 0,
                    unit: rowItem.item!.stockUnit?._id,
                    orderRemainingQuantityToAllocate: Number(rowItem.remainingQuantityToAllocate) || 0,
                    searchCriteria: {
                        activeQuantityInStockUnit: rowItem.remainingQuantity || 0,
                        item: rowItem.item?._id || '',
                        site: rowItem.site?._id || '',
                        stockUnit: rowItem.item!.stockUnit?._id,
                        statusList: [{ statusType: 'accepted' }],
                        ignoreExpirationDate: true,
                    },
                },
            }) as Promise<ui.PartialNodeWithId<WorkOrderComponentTable>>,
        );
        if (modifiedLine) {
            await this.onRowChange(true, modifiedLine);
        }
    }
}
