import type { GraphApi, WorkOrderOperation } from '@sage/xtrem-manufacturing-api';
import type { WorkOrderOperationInput } from '@sage/xtrem-manufacturing-api-partial';
import type { CapabilityLevel, Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<WorkOrderOperationPanel>({
    title: 'New operation',
    node: '@sage/xtrem-manufacturing/WorkOrderOperation',
    module: 'manufacturing',
    isTransient: true,
    businessActions() {
        return [this.cancel, this.save];
    },
    onLoad() {
        this.site.value = JSON.parse(this.$.queryParameters.site as string);
        this.item.value = JSON.parse(this.$.queryParameters.item as string);
        this.operations = JSON.parse(this.$.queryParameters.operations as string);
        this.plannedQuantity.value = JSON.parse(this.$.queryParameters.plannedQuantity as string);
        this.setPageValues(
            this.$.queryParameters.id as number,
            this.$.queryParameters.operation as string,
            this.$.queryParameters.timeUnit as string,
        );
        if (this._id.value) {
            this.$.page.title = `${ui.localize(
                '@sage/xtrem-manufacturing/pages__work_order_operation_panel__edit____title',
                'Edit work order operation',
            )}`;
        } else {
            this.$.page.title = `${ui.localize(
                '@sage/xtrem-manufacturing/pages__work_order_operation_panel__new____title',
                'New work order operation',
            )}`;
        }
    },
})
export class WorkOrderOperationPanel extends ui.Page<GraphApi> {
    /** array of _id of operation */
    private operations: number[];

    @ui.decorators.pageAction<WorkOrderOperationPanel>({
        title: 'OK',
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                this.$.finish({
                    _id: this._id.value?.toString(),
                    operationNumber: this.operationNumber.value,
                    name: this.name.value || '',
                    isProductionStep: this.isProductionStep.value || false,
                    minCapabilityLevel: this.minCapabilityLevel.value || null,
                    plannedQuantity: this.plannedQuantity.value || 0,
                    setupTimeUnit: this.setupTimeUnit.value || null,
                    runTimeUnit: this.runTimeUnit.value || null,
                    instruction: { value: this.instruction.value || '' },
                } as WorkOrderOperationInput);
            } else {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            }
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<WorkOrderOperationPanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.section<WorkOrderOperationPanel>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<WorkOrderOperationPanel>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.numericField<WorkOrderOperationPanel>({
        parent() {
            return this.block;
        },
        isReadOnly: true,
        isHidden: true,
        isDisabled: true,
    })
    _id: ui.fields.Numeric;

    @ui.decorators.numericField<WorkOrderOperationPanel>({
        title: 'Operation number',
        isMandatory: true,
        parent() {
            return this.block;
        },
        validation(newValue: number) {
            if (!this.operationNumber.isReadOnly && this.operations.includes(newValue)) {
                return ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order_operation_panel__work_order_operation_already_exists',
                    'Operation number {{operationNumber}} already exists',
                    { operationNumber: newValue },
                );
            }
            return undefined;
        },
    })
    operationNumber: ui.fields.Numeric;

    @ui.decorators.textField<WorkOrderOperationPanel>({
        title: 'Name',
        isMandatory: true,
        parent() {
            return this.block;
        },
    })
    name: ui.fields.Text;

    @ui.decorators.referenceField<WorkOrderOperationPanel, CapabilityLevel>({
        parent() {
            return this.block;
        },
        title: 'Capability level',
        isMandatory: true,
        node: '@sage/xtrem-master-data/CapabilityLevel',
        lookupDialogTitle: 'Select capability level',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        width: 'small',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'Capability level', bind: 'id' }),
            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
            ui.nestedFields.text({ title: 'Level', bind: 'level' }),
        ],
    })
    minCapabilityLevel: ui.fields.Reference<CapabilityLevel>;

    @ui.decorators.referenceField<WorkOrderOperationPanel, UnitOfMeasure>({
        title: 'Setup time unit',
        bind: 'setupTimeUnit',
        valueField: 'name',
        helperTextField: 'symbol',
        isMandatory: true,
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
        lookupDialogTitle: 'Select unit of measure',
        minLookupCharacters: 1,
        parent() {
            return this.block;
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', isHidden: true }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.numeric({ bind: 'decimalDigits', isHidden: true }),
            ui.nestedFields.text({ bind: 'type', isHidden: true }),
        ],
        filter() {
            return { type: { _eq: 'time' } };
        },
    })
    setupTimeUnit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.referenceField<WorkOrderOperationPanel, UnitOfMeasure>({
        isMandatory: true,
        title: 'Run time unit',
        bind: 'runTimeUnit',
        valueField: 'name',
        helperTextField: 'symbol',
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
        lookupDialogTitle: 'Select unit of measure',
        minLookupCharacters: 1,
        parent() {
            return this.block;
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', isHidden: true }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.numeric({ bind: 'decimalDigits', isHidden: true }),
            ui.nestedFields.text({ bind: 'type', isHidden: true }),
        ],
        filter() {
            return { type: { _eq: 'time' } };
        },
    })
    runTimeUnit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.referenceField<WorkOrderOperationPanel, Site>({
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'id',
        isDisabled: true,
        isHidden: true,
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<WorkOrderOperationPanel, Item>({
        title: 'Item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',

        lookupDialogTitle: 'Select item',
        valueField: 'id',
        isDisabled: true,
        isHidden: true,
    })
    item: ui.fields.Reference<Item>;

    @ui.decorators.switchField<WorkOrderOperationPanel>({
        parent() {
            return this.block;
        },
        title: 'Production step',
    })
    isProductionStep: ui.fields.Switch;

    @ui.decorators.numericField<WorkOrderOperationPanel>({
        parent() {
            return this.block;
        },
        isReadOnly: true,
        isHidden: true,
        isDisabled: true,
    })
    plannedQuantity: ui.fields.Numeric;

    @ui.decorators.richTextField<WorkOrderOperationPanel>({
        parent() {
            return this.block;
        },
        width: 'large',
        title: 'Instructions',
        capabilities: validCapabilities,
    })
    instruction: ui.fields.RichText;

    private setPageValues(id: number, currentOperation: string, timeUnit: string) {
        if (currentOperation) {
            const operation = JSON.parse(currentOperation) as ui.PartialNodeWithId<WorkOrderOperation>;
            this.operationNumber.isReadOnly = true;

            this.operationNumber.value = operation.operationNumber ? +operation.operationNumber : null;
            this.name.value = operation.name || '';
            this.isProductionStep.value = operation.isProductionStep || false;
            this.minCapabilityLevel.value = operation.minCapabilityLevel || null;
            this.setupTimeUnit.value = operation.setupTimeUnit || null;
            this.runTimeUnit.value = operation.runTimeUnit || null;
            this._id.value = id;
            this.instruction.value = operation.instruction?.value || '';
            if (operation.status !== 'pending') {
                this.section.isDisabled = true;
                this.save.isDisabled = true;
                this.isProductionStep.isDisabled = true;
            }
        } else {
            const timeUnitObject = JSON.parse(timeUnit) as ui.PartialNodeWithId<UnitOfMeasure>;
            this.operationNumber.value = null;
            this.name.value = '';
            this.isProductionStep.value = false;
            this.minCapabilityLevel.value = null;
            this.setupTimeUnit.value = timeUnitObject;
            this.runTimeUnit.value = timeUnitObject;
        }
    }
}
