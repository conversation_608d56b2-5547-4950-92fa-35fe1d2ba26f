import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<ReturnAnalysisStartDateSetting>({
    title: 'Start date settings',
    isTransient: true,
    onLoad() {
        if (this.$.queryParameters.startDate) {
            this.startDate.value = JSON.parse(this.$.queryParameters.startDate as string);
        }
    },
    businessActions() {
        return [this.save];
    },
})
export class ReturnAnalysisStartDateSetting extends ui.Page {
    @ui.decorators.section<ReturnAnalysisStartDateSetting>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<ReturnAnalysisStartDateSetting>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.dateField<ReturnAnalysisStartDateSetting>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Start date',
        isMandatory: true,
        fetchesDefaults: true,
    })
    startDate: ui.fields.Date;

    @ui.decorators.pageAction<ReturnAnalysisStartDateSetting>({
        title: 'Save settings',
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                this.$.finish({
                    startDate: JSON.stringify(this.startDate.value),
                });
            }
        },
    })
    save: ui.PageAction;
}
