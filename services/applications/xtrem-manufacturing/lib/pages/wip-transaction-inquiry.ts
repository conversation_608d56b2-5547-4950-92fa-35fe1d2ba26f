import type { GraphApi, WorkInProgressCost } from '@sage/xtrem-manufacturing-api';
import * as DocumentLink from '@sage/xtrem-stock-data/build/lib/client-functions/document-link';
import * as ui from '@sage/xtrem-ui';
import * as PillColor from '../client-functions/pill-color';
import { manufacturingInquiries } from '../menu-items/manufacturing-inquiries';

@ui.decorators.page<WipTransactionInquiry, WorkInProgressCost>({
    menuItem: manufacturingInquiries,
    priority: 305,
    title: 'Work in progress transaction inquiry',
    module: 'manufacturing',
    mode: 'default',
    node: '@sage/xtrem-manufacturing/WorkInProgressCost',
    navigationPanel: {
        orderBy: { effectiveDate: -1, _id: -1 },
        onSelect() {
            // We have to prevent selecting a line to avoid opening the "normal" page
            // (which is not even defined).
            return true;
        },
        listItem: {
            siteName: ui.nestedFields.reference({
                bind: { workOrder: { site: true } },
                title: 'Site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
            }),
            siteId: ui.nestedFields.text({
                bind: { workOrder: { site: { id: true } } },
                title: 'Site ID',
                isHiddenOnMainField: true,
            }),
            workOrder: ui.nestedFields.reference({
                bind: 'workOrder',
                title: 'Work order',
                node: '@sage/xtrem-manufacturing/WorkOrder',
                tunnelPage: '@sage/xtrem-manufacturing/WorkOrder',
                valueField: 'number',
                size: 'small',
            }),
            releasedItem: ui.nestedFields.reference({
                bind: { workOrder: { productionItem: { releasedItem: true } } },
                title: 'Item',
                size: 'small',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                tunnelPage: '@sage/xtrem-master-data/Item',
            }),
            releasedItemId: ui.nestedFields.text({
                bind: { workOrder: { productionItem: { releasedItem: { id: true } } } },
                title: 'Item ID',
                size: 'small',
            }),
            releasedItemDescription: ui.nestedFields.text({
                bind: { workOrder: { productionItem: { releasedItem: { description: true } } } },
                title: 'Item description',
                size: 'small',
                isHiddenOnMainField: true,
            }),
            type: ui.nestedFields.select({
                bind: 'type',
                title: 'Type',
                optionType: '@sage/xtrem-manufacturing/WorkInProgressType',
                size: 'small',
            }),
            document: ui.nestedFields.link({
                isFullWidth: true,
                bind: { originatingLine: { documentNumber: true } },
                title: 'Document',
                onClick(_id, data: ui.PartialCollectionValue<WorkInProgressCost>) {
                    return this.$.dialog.page(
                        DocumentLink.getDocumentPageName(data?.originatingLine?._constructor || ''),
                        { _id: data?.originatingLine?.documentId || '' },
                        { fullScreen: true, resolveOnCancel: true },
                    );
                },
            }),
            effectiveDate: ui.nestedFields.date({
                bind: 'effectiveDate',
                title: 'Date',
                size: 'small',
            }),
            status: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-manufacturing/WorkInProgressStatus',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('WorkInProgressStatus', rowData.status),
            }),
            quantity: ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Quantity',
                size: 'small',
                unit: (_rowId, rowData) => rowData?.unit,
                unitMode: 'unitOfMeasure',
                groupAggregationMethod: 'sum',
            }),
            unit: ui.nestedFields.reference({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'unit',
                title: 'Unit',
                valueField: 'name',
                minLookupCharacters: 1,
                size: 'small',
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            cost: ui.nestedFields.numeric({
                bind: 'cost',
                title: 'Cost',
                size: 'small',
                scale: 4,
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            amount: ui.nestedFields.numeric({
                bind: 'amount',
                title: 'Amount',
                size: 'small',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
                groupAggregationMethod: 'sum',
            }),
            currencyName: ui.nestedFields.reference({
                bind: 'currency',
                title: 'Currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                minLookupCharacters: 1,
                size: 'small',
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            currencyId: ui.nestedFields.text({
                bind: { currency: { id: true } },
                title: 'Currency ID',
                size: 'small',
                isHiddenOnMainField: true,
            }),
            // TODO: DNE Still in discussion: Which alternative should we keep?
            // processResourceType: ui.nestedFields.label({
            //     bind: { actualResource: { resourceGroup: { type: true } } },
            //     optionType: '@sage/xtrem-master-data/ResourceGroupType',
            //     title: 'Process resource type',
            //     backgroundColor: colorfulPillPattern.textBox.backgroundColor,
            //     borderColor: colorfulPillPattern.textBox.borderColor,
            //     isHiddenOnMainField: true,
            // }),
            processResourceType: ui.nestedFields.text({
                bind: { actualResource: { _factory: { title: true } } },
                title: 'Process resource type',
                isHiddenOnMainField: true,
            }),
            title: ui.nestedFields.reference({
                node: '@sage/xtrem-master-data/BaseDocumentLine',
                bind: 'originatingLine',
                title: 'Tracking line',
                tunnelPage: undefined,
                valueField: '_id',
                isHidden: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'documentNumber' }),
                    ui.nestedFields.technical({ bind: 'documentId' }),
                    ui.nestedFields.technical({ bind: '_constructor' }),
                ],
            }),
        },
    },
})
export class WipTransactionInquiry extends ui.Page<GraphApi, WorkInProgressCost> {}
