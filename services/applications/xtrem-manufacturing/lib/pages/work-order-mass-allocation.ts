import type { Filter } from '@sage/xtrem-client';
import type {
    <PERSON><PERSON>h<PERSON><PERSON>,
    WorkOrder,
    WorkOrderComponent,
    WorkOrderOperation,
    WorkOrderReleasedItem,
} from '@sage/xtrem-manufacturing-api';
import type { Item } from '@sage/xtrem-master-data-api';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { manufacturing } from '@sage/xtrem-master-data/build/lib/menu-items/manufacturing';
import type { GraphApi as SchedulerGraphApi } from '@sage/xtrem-scheduler-api';
import { once, recurring } from '@sage/xtrem-scheduler/build/lib/client-functions/job-execution';
import { scheduleWizard } from '@sage/xtrem-scheduler/build/lib/client-functions/job-schedule';
import type { Company, Site } from '@sage/xtrem-system-api';
import type { Operation } from '@sage/xtrem-technical-data-api';
import * as ui from '@sage/xtrem-ui';
import * as _ from 'lodash';
import * as PillColor from '../client-functions/pill-color';

@ui.decorators.page<WorkOrderMassAllocation>({
    isTransient: true,
    title: 'Work order mass allocation',
    menuItem: manufacturing,
    priority: 450,
    access: { node: '@sage/xtrem-manufacturing/WorkOrder', bind: '$create' },
    skipDirtyCheck: true,
    async onLoad() {
        this.action.value = 'allocation';
        this.actionButtonDefaultTitle = this.allocate.title as string;
        await setReferenceIfSingleValue([this.stockSite, this.company]);
        this.allocate.isDisabled = true;
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        this.$.loader.isHidden = true;
        return MasterDataUtils.formatError(this, error);
    },
    businessActions() {
        return [this.allocate, this.schedule];
    },
    onDirtyStateUpdated(isDirty) {
        if (isDirty) {
            this.allocate.isDisabled = false;
        }
    },
})
export class WorkOrderMassAllocation extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkOrderMassAllocation>({
        title: 'General',
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkOrderMassAllocation>({
        parent() {
            return this.mainSection;
        },
    })
    criteriaBlock: ui.containers.Block;

    actionButtonDefaultTitle: string;

    @ui.decorators.selectField<WorkOrderMassAllocation>({
        title: 'Action',
        optionType: '@sage/xtrem-stock-data/AllocationRequestType',
        isMandatory: true,
        parent() {
            return this.criteriaBlock;
        },
        onChange() {
            if (this.action.value === 'deallocation') {
                this.allocate.title = ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order_mass_allocation__deallocate____title',
                    'Deallocate',
                );
            } else {
                this.allocate.title = this.actionButtonDefaultTitle;
            }
        },
    })
    action: ui.fields.Select;

    @ui.decorators.textField<WorkOrderMassAllocation>({
        title: 'Description',
        parent() {
            return this.criteriaBlock;
        },
    })
    description: ui.fields.Text;

    @ui.decorators.referenceField<WorkOrderMassAllocation, Company>({
        title: 'Company',
        lookupDialogTitle: 'Select company',
        isMandatory: true,
        node: '@sage/xtrem-system/Company',
        tunnelPage: '@sage/xtrem-master-data/Company',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        onChange() {
            if (
                this.company.value &&
                this.stockSite.value &&
                this.stockSite.value.legalCompany?.id !== this.company.value.id
            ) {
                this.stockSite.value = null;
            }
        },
    })
    company: ui.fields.Reference<Company>;

    @ui.decorators.referenceField<WorkOrderMassAllocation, Site>({
        title: 'Site',
        lookupDialogTitle: 'Select site',
        isMandatory: true,
        node: '@sage/xtrem-system/Site',
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.reference<WorkOrderMassAllocation, Site, Company>({
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                ],
                isHidden() {
                    return !!this.company.value;
                },
            }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            return {
                isInventory: { _eq: true },
                ...(this.company.value ? { legalCompany: { _id: this.company.value._id } } : {}),
            };
        },
        async onChange() {
            if (!this.company.value && this.stockSite.value?.legalCompany) {
                this.company.value = this.stockSite.value.legalCompany;
                await this.$.commitValueAndPropertyChanges();
                await this.company.validate();
            }
        },
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.dateField<WorkOrderMassAllocation>({
        title: 'Latest start date',
        parent() {
            return this.criteriaBlock;
        },
    })
    latestStartDate: ui.fields.Date;

    @ui.decorators.referenceField<WorkOrderMassAllocation, Item>({
        title: 'From item',
        lookupDialogTitle: 'Select from item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        helperTextField: 'id',
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            return {
                isStockManaged: true,
                ...(this.stockSite.value ? { itemSites: { _atLeast: 1, site: this.stockSite.value._id } } : {}),
                ...(this.toItem.value ? { name: { _lte: this.toItem.value.name } } : {}),
            };
        },
    })
    fromItem: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<WorkOrderMassAllocation, Item>({
        title: 'To item',
        lookupDialogTitle: 'Select to item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        shouldSuggestionsIncludeColumns: true,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            return {
                isStockManaged: true,
                ...(this.stockSite.value ? { itemSites: { _atLeast: 1, site: this.stockSite.value._id } } : {}),
                ...(this.fromItem.value ? { name: { _gte: this.fromItem.value.name } } : {}),
            };
        },
    })
    toItem: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<WorkOrderMassAllocation, WorkOrder>({
        title: 'From work order',
        lookupDialogTitle: 'Select from work order',
        node: '@sage/xtrem-manufacturing/WorkOrder',
        valueField: 'number',
        columns: [
            ui.nestedFields.text({ title: 'Number', bind: 'number' }),
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            return {
                status: { _nin: ['completed', 'closed'] },
                ...(this.stockSite.value ? { site: { _id: this.stockSite.value._id } } : {}),
                ...(this.toWorkOrder.value ? { number: { _lte: this.toWorkOrder.value.number } } : {}),
            };
        },
    })
    fromWorkOrder: ui.fields.Reference<WorkOrder>;

    @ui.decorators.referenceField<WorkOrderMassAllocation, WorkOrder>({
        title: 'To work order',
        lookupDialogTitle: 'Select to work order',
        node: '@sage/xtrem-manufacturing/WorkOrder',
        valueField: 'number',
        columns: [
            ui.nestedFields.text({ title: 'Number', bind: 'number' }),
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            return {
                status: { _nin: ['completed', 'closed'] },
                ...(this.stockSite.value ? { site: { _id: this.stockSite.value._id } } : {}),
                ...(this.fromWorkOrder.value ? { number: { _gte: this.fromWorkOrder.value.number } } : {}),
            };
        },
    })
    toWorkOrder: ui.fields.Reference<WorkOrder>;

    @ui.decorators.referenceField<WorkOrderMassAllocation, WorkOrderReleasedItem>({
        title: 'From released item',
        lookupDialogTitle: 'Select from released item',
        node: '@sage/xtrem-manufacturing/WorkOrderReleasedItem',
        valueField: 'releasedItemName',
        columns: [
            ui.nestedFields.text({ title: 'Work order', bind: { document: { number: true } } }),
            ui.nestedFields.text({ title: 'Site', bind: { document: { site: { name: true } } } }),
            ui.nestedFields.technical({ bind: { document: { site: { _id: true } } } }),
            ui.nestedFields.text({
                title: 'Item',
                bind: { releasedItem: { name: true } },
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'lineStatus',
                optionType: '@sage/xtrem-manufacturing/ReleasedItemStatus',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('ReleasedItemStatus', rowData.lineStatus),
            }),
            ui.nestedFields.numeric({
                title: 'Required quantity',
                bind: 'releasedQuantity',
                postfix(_unused, rowData) {
                    return rowData?.stockUnit?.symbol ?? '';
                },
                scale(_unused, rowData) {
                    return MasterDataUtils.getScaleValue(2, rowData?.stockUnit?.decimalDigits);
                },
                min: 0,
            }),
            ui.nestedFields.numeric({
                title: 'Remaining quantity',
                bind: 'remainingQuantity',
                min: 0,
                postfix(_unused, rowData) {
                    return rowData?.stockUnit?.symbol ?? '';
                },
                scale(_unused, rowData) {
                    return MasterDataUtils.getScaleValue(2, rowData?.stockUnit?.decimalDigits);
                },
            }),
            ui.nestedFields.reference({
                bind: 'stockUnit',
                valueField: 'symbol',
                helperTextField: 'decimalDigits',
                isHidden: true,
            }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            const itemFilter = {
                ...(this.toReleasedItem.value ? { name: { _lte: this.toReleasedItem.value.releasedItem?.name } } : {}),
            };
            const filter: Filter<WorkOrderReleasedItem> = {
                lineStatus: { _ne: 'completed' },
                itemSite: { site: { _id: { _eq: this.stockSite.value?._id } } },
                ...(!_.isEmpty(itemFilter) ? { releasedItem: itemFilter } : {}),
            };

            return filter;
        },
    })
    fromReleasedItem: ui.fields.Reference<WorkOrderReleasedItem>;

    @ui.decorators.referenceField<WorkOrderMassAllocation, WorkOrderReleasedItem>({
        title: 'To released item',
        lookupDialogTitle: 'Select to released item',
        node: '@sage/xtrem-manufacturing/WorkOrderReleasedItem',
        valueField: 'releasedItemName',
        columns: [
            ui.nestedFields.text({ title: 'Work order', bind: { document: { number: true } } }),
            ui.nestedFields.text({ title: 'Site', bind: { document: { site: { name: true } } } }),
            ui.nestedFields.text({ title: 'Item', bind: { releasedItem: { name: true } } }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'lineStatus',
                optionType: '@sage/xtrem-manufacturing/ReleasedItemStatus',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('ReleasedItemStatus', rowData.lineStatus),
            }),
            ui.nestedFields.numeric({
                title: 'Required quantity',
                bind: 'releasedQuantity',
                postfix(_unused, rowData) {
                    return rowData?.stockUnit?.symbol ?? '';
                },
                scale(_unused, rowData) {
                    return MasterDataUtils.getScaleValue(2, rowData?.stockUnit?.decimalDigits);
                },

                min: 0,
            }),
            ui.nestedFields.numeric({
                title: 'Remaining quantity',
                bind: 'remainingQuantity',
                min: 0,
                postfix(_unused, rowData) {
                    return rowData?.stockUnit?.symbol ?? '';
                },
                scale(_unused, rowData) {
                    return MasterDataUtils.getScaleValue(2, rowData?.stockUnit?.decimalDigits);
                },
            }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            const itemFilter = {
                ...(this.fromReleasedItem.value
                    ? { name: { _gte: this.fromReleasedItem.value.releasedItem?.name } }
                    : {}),
            };
            return {
                lineStatus: { _ne: 'completed' },
                itemSite: { site: { _id: { _eq: this.stockSite.value?._id } } },
                ...(!_.isEmpty(itemFilter) ? { releasedItem: itemFilter } : {}),
            };
        },
    })
    toReleasedItem: ui.fields.Reference<WorkOrderReleasedItem>;

    @ui.decorators.referenceField<WorkOrderMassAllocation, Operation>({
        title: 'From operation',
        lookupDialogTitle: 'Select from operation',
        node: '@sage/xtrem-technical-data/Operation',
        valueField: 'name',
        columns: [
            ui.nestedFields.text({ title: 'Routing', bind: { routing: { name: true } } }),
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'Operation number', bind: 'operationNumber' }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            return {
                ...(this.toOperation.value ? { name: { _lte: this.toOperation.value.name } } : {}),
            };
        },
    })
    fromOperation: ui.fields.Reference<Operation>;

    @ui.decorators.referenceField<WorkOrderMassAllocation, Operation>({
        title: 'To operation',
        lookupDialogTitle: 'Select to operation',
        node: '@sage/xtrem-technical-data/Operation',
        valueField: 'name',
        columns: [
            ui.nestedFields.text({
                title: 'Routing',
                bind: { routing: { name: true } },
            }),
            ui.nestedFields.text({
                title: 'Name',
                bind: 'name',
            }),
            ui.nestedFields.text({
                title: 'Operation number',
                bind: 'operationNumber',
            }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            return {
                ...(this.fromOperation.value ? { name: { _gte: this.fromOperation.value.name } } : {}),
            };
        },
    })
    toOperation: ui.fields.Reference<Operation>;

    @ui.decorators.pageAction<WorkOrderMassAllocation>({
        title: 'Allocate',
        async onClick() {
            const validationError = await this.$.page.validate();

            if (validationError.length) {
                return;
            }
            this.allocate.isDisabled = true; // Disable the button to prevent multiple clicks
            this.$.setPageClean();

            this.$.loader.isHidden = false;

            const { massAllocationFilter, massAllocationData } = this.getMassAllocationParameters();

            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrderComponent')
                .asyncOperations.massAutoAllocation.start(
                    { trackingId: true },
                    {
                        filter: `${JSON.stringify(massAllocationFilter)}`,
                        data: massAllocationData,
                    },
                )
                .execute();
            this.$.loader.isHidden = true;

            await this.$.dialog.message(
                'info',
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order_mass_allocation__message_title',
                    'Allocation request submitted',
                ),
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order_mass_allocation__message',
                    'The allocation request was submitted.',
                ),
            );
        },
    })
    allocate: ui.PageAction;

    @ui.decorators.pageAction<WorkOrderMassAllocation>({
        title: 'Schedule',
        async onClick() {
            const validationError = await this.$.page.validate();

            if (validationError.length) {
                return;
            }

            const { massAllocationFilter, massAllocationData } = this.getMassAllocationParameters();

            /** as unknown as graphApi ... because actually we are not able to import api of the package only  */
            await scheduleWizard(this as unknown as ui.Page<SchedulerGraphApi>, {
                jobSchedule: [once, recurring],
                filter: JSON.stringify(massAllocationFilter),
                operationKey: 'WorkOrderComponent|massAutoAllocation|start',
                additionalParameters: {
                    data: JSON.stringify(massAllocationData),
                },
                isParametersHidden: true,
            });
        },
    })
    schedule: ui.PageAction;

    getItemFilter(): Filter<Item> {
        const itemFilter: {
            name?: {
                _gte?: string;
                _lte?: string;
            };
            itemSites?: {
                _atLeast: 1;
                site: { id: string };
            };
        } = {};

        if (this.fromItem.value) {
            itemFilter.name = {
                _gte: this.fromItem.value.name as string,
            };
        }

        if (this.toItem.value) {
            itemFilter.name = {
                ...itemFilter.name,
                _lte: this.toItem.value.name as string,
            };
        }

        return itemFilter;
    }

    getWorkOrderFilter(): Filter<WorkOrder> {
        let workOrderFilter: {
            number?: {
                _gte?: string;
                _lte?: string;
            };
            productionItems?: {
                _atLeast: number;
                releasedItem: {
                    name: {
                        _gte?: string;
                        _lte?: string;
                    };
                };
            };
            site?: {
                id: string;
            };
            startDate?: {
                _lte: string;
            };
        } = {};

        if (this.stockSite.value) {
            workOrderFilter = {
                site: {
                    id: this.stockSite.value.id as string,
                },
            };
        }

        if (this.latestStartDate.value) {
            workOrderFilter = {
                ...workOrderFilter,
                startDate: { _lte: this.latestStartDate.value },
            };
        }

        if (this.fromWorkOrder.value) {
            workOrderFilter.number = {
                _gte: this.fromWorkOrder.value.number as string,
            };
        }

        if (this.toWorkOrder.value) {
            workOrderFilter.number = {
                ...workOrderFilter.number,
                _lte: this.toWorkOrder.value.number as string,
            };
        }

        if (this.fromReleasedItem.value) {
            workOrderFilter.productionItems = {
                _atLeast: 1,
                releasedItem: {
                    name: { _gte: this.fromReleasedItem.value.releasedItem?.name as string },
                },
            };
        }

        if (this.toReleasedItem.value) {
            workOrderFilter.productionItems = {
                _atLeast: 1,
                releasedItem: {
                    name: {
                        ...workOrderFilter.productionItems?.releasedItem?.name,
                        _lte: this.toReleasedItem.value.releasedItem?.name as string,
                    },
                },
            };
        }

        return workOrderFilter;
    }

    getWorkOrderOperationFilter(): Filter<WorkOrderOperation> {
        const workOrderOperationFilter: {
            operation?: {
                operationNumber: {
                    _gte?: number;
                    _lte?: number;
                };
            };
        } = {};

        if (this.fromOperation.value) {
            workOrderOperationFilter.operation = {
                operationNumber: { _gte: this.fromOperation.value.operationNumber as number },
            };
        }

        if (this.toOperation.value) {
            workOrderOperationFilter.operation = {
                operationNumber: {
                    ...workOrderOperationFilter.operation?.operationNumber,
                    _lte: this.toOperation.value.operationNumber as number,
                },
            };
        }

        return workOrderOperationFilter;
    }

    getMassAllocationParameters() {
        const itemFilter = this.getItemFilter();
        const workOrderFilter = this.getWorkOrderFilter();
        const workOrderOperationFilter = this.getWorkOrderOperationFilter();

        const massAllocationFilter: Filter<WorkOrderComponent> = {
            ...(!_.isEmpty(itemFilter) ? { item: itemFilter } : {}),
            ...(!_.isEmpty(workOrderFilter) ? { workOrder: workOrderFilter } : {}),
            ...(!_.isEmpty(workOrderOperationFilter) ? workOrderOperationFilter : {}),
        };

        const massAllocationData = {
            requestType: this.action.value,
            ...(this.description.value && { requestDescription: this.description.value }),
        };

        return { massAllocationFilter, massAllocationData };
    }
}
