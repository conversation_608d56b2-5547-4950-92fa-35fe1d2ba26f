import type * as ui from '@sage/xtrem-ui';

export enum TrackingTypes {
    MaterialTracking = 'Material',
    ProductionTracking = 'Production',
}

export function onLoad<T extends ui.Page>(pageInstance: T, trackingType: TrackingTypes, callBack: () => any) {
    if (pageInstance.$.queryParameters.workOrder) {
        callBack();
    } else {
        // FIXME: Hack to enable navigation at this stage
        setTimeout(() =>
            pageInstance.$.router.goTo('@sage/xtrem-manufacturing/TrackingSearchWorkOrder', {
                trackingType,
            }),
        );
    }
}
