import type { ExtractEdges } from '@sage/xtrem-client';
import { extractEdges, withoutEdges } from '@sage/xtrem-client';
import type { PreferredProcess } from '@sage/xtrem-master-data-api';
import type { OrderAssignment, OrderToOrderDemandType, OrderToOrderSupplyType } from '@sage/xtrem-stock-data-api';
import * as ui from '@sage/xtrem-ui';
import type { OrderAssignmentLine } from '../../client-functions/interfaces/order-assignment';
import type { WorkOrder as WorkOrderPage } from '../work-order';
import type { WorkOrderAssignmentDetailsPanel as WorkOrderAssignmentDetailsPanelPage } from '../work-order-assignment-details-panel';

export function isOrderToOrderServiceOptionActivated(workOrder: WorkOrderPage, preferredProcess?: PreferredProcess) {
    return (
        workOrder.productionItems.value[0]?._id &&
        workOrder.productionItems.value[0].itemSite &&
        workOrder.productionItems.value[0].itemSite.isOrderToOrder === true &&
        (preferredProcess === undefined ||
            (preferredProcess && workOrder.productionItems.value[0].itemSite.preferredProcess === preferredProcess))
    );
}

async function getOrderAssignments(
    page: WorkOrderPage,
    demandType: OrderToOrderDemandType,
    supplyType: OrderToOrderSupplyType,
) {
    return extractEdges<Partial<OrderAssignment>>(
        await page.$.graph
            .node('@sage/xtrem-stock-data/OrderAssignment')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        quantityInStockUnit: true,
                    },
                    {
                        filter: {
                            supplyDocumentLine: { _eq: page.productionItems.value[0]._id },
                            demandType: { _eq: demandType },
                            supplyType: { _eq: supplyType },
                        },
                    },
                ),
            )
            .execute(),
    );
}

async function openAssignmentDialog(page: WorkOrderPage, dialogTitle: string, dialogText: string) {
    if (
        await page.$.dialog
            .confirmation('warn', dialogTitle, dialogText, {
                acceptButton: {
                    text: ui.localize(
                        '@sage/xtrem-manufacturing/pages-work-order-dialog-assignment-continue',
                        'Continue',
                    ),
                },
                cancelButton: {
                    text: ui.localize('@sage/xtrem-manufacturing/pages-work-order-dialog-assignment-cancel', 'Cancel'),
                },
            })
            .then(() => true)
            .catch(() => false)
    ) {
        return true;
    }
    return false;
}

async function assignedOrderAssignments(
    page: WorkOrderPage,
    demandType: OrderToOrderDemandType,
    supplyType: OrderToOrderSupplyType,
    preferredProcess?: PreferredProcess,
): Promise<ExtractEdges<Partial<OrderAssignment>>[] | null> {
    if (isOrderToOrderServiceOptionActivated(page, preferredProcess)) {
        const orderAssignments = await getOrderAssignments(page, demandType, supplyType);
        if (orderAssignments.length) {
            return orderAssignments;
        }
    }
    return null;
}

export async function isWorkOrderAssignmentCheckedOnWorkOrderQuantityChanged(
    page: WorkOrderPage,
    quantityInStockUnit: { new: number; old: number },
) {
    const assignedOrders = await assignedOrderAssignments(page, 'salesOrderLine', 'workOrder');
    if (
        assignedOrders &&
        assignedOrders.length &&
        quantityInStockUnit.new < Number(assignedOrders[0].quantityInStockUnit) &&
        quantityInStockUnit.new < quantityInStockUnit.old
    ) {
        return openAssignmentDialog(
            page,
            ui.localize(
                '@sage/xtrem-manufacturing/pages-work-order_confirm_assignment_decreasing_quantity_dialog_title',
                'Decrease work order quantity',
            ),
            ui.localize(
                '@sage/xtrem-manufacturing/pages-work-order_confirm_assignment_decreasing_quantity_dialog_content',
                'When you decrease the work order quantity, the quantity decreases on the linked original order.',
            ),
        );
    }
    return true;
}

export async function isWorkOrderAssignmentCheckedOnWorkOrderClose(page: WorkOrderPage) {
    const assignedOrders = await assignedOrderAssignments(page, 'salesOrderLine', 'workOrder');
    if (assignedOrders && assignedOrders.length) {
        return openAssignmentDialog(
            page,
            ui.localize(
                '@sage/xtrem-manufacturing/pages-work-order_confirm_assignment_closing_dialog_title',
                'Close work order',
            ),
            ui.localize(
                '@sage/xtrem-manufacturing/pages-work-order_confirm_assignment_closing_dialog_content',
                'When you close this work order, the link to the original order remains.',
            ),
        );
    }
    return true;
}

export async function isWorkOrderAssignmentCheckedOnWorkOrderDelete(page: WorkOrderPage) {
    const assignedOrders = await assignedOrderAssignments(page, 'salesOrderLine', 'workOrder');
    if (assignedOrders && assignedOrders.length) {
        return openAssignmentDialog(
            page,
            ui.localize(
                '@sage/xtrem-manufacturing/pages-work-order_confirm_assignment_deletion_dialog_title',
                'Delete work order',
            ),
            ui.localize(
                '@sage/xtrem-manufacturing/pages-work-order_confirm_assignment_deletion_dialog_content',
                'When you delete this work order, the link to the original order is also deleted.',
            ),
        );
    }
    return true;
}

export function openOrderAssignments(page: WorkOrderPage): Promise<OrderAssignment | null> {
    return page.$.dialog.page(
        '@sage/xtrem-manufacturing/WorkOrderAssignmentDetailsPanel',
        {
            data: JSON.stringify({
                productionItemsId: page.productionItems.value[0]._id,
                releasedQuantity: page.productionItems.value[0].releasedQuantity,
                itemStockUnit: page.productionItems.value[0].stockUnit,
                status: page.status.value,
            }),
        },
        {
            rightAligned: false,
            size: 'large',
            resolveOnCancel: true,
        },
    );
}

export async function queryOrderAssignment(
    page: WorkOrderAssignmentDetailsPanelPage,
    supplyDocumentLine: string,
): Promise<OrderAssignmentLine[]> {
    return withoutEdges(
        await page.$.graph
            .node('@sage/xtrem-stock-data/OrderAssignment')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        demandType: true,
                        demandDocumentLine: {
                            _id: true,
                            documentId: true,
                            documentNumber: true,
                            _constructor: true,
                        },
                        demandWorkInProgress: {
                            expectedQuantity: true,
                        },
                        quantityInStockUnit: true,
                    },
                    {
                        filter: {
                            supplyDocumentLine: { _eq: supplyDocumentLine },
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function checkAssignmentLinkExist({
    page,
    supplyDocumentLine,
    supplyType,
}: {
    page: WorkOrderPage;
    supplyDocumentLine: string;
    supplyType: OrderToOrderSupplyType;
}): Promise<Partial<OrderAssignment>[]> {
    if (isOrderToOrderServiceOptionActivated(page)) {
        return withoutEdges(
            await page.$.graph
                .node('@sage/xtrem-stock-data/OrderAssignment')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true },
                        {
                            filter: {
                                supplyDocumentLine: { _eq: supplyDocumentLine },
                                supplyType: { _eq: supplyType },
                            },
                        },
                    ),
                )
                .execute(),
        );
    }
    return [];
}
