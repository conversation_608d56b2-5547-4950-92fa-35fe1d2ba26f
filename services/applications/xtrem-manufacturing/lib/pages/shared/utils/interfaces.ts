import type { decimal, integer } from '@sage/xtrem-client';
import type {
    BaseDocumentLine,
    BaseDocumentLineBinding,
    Item,
    Location,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import type { Lot, SerialNumber, Stock, StockStatus } from '@sage/xtrem-stock-data-api';
import type { Site } from '@sage/xtrem-system-api';

// TODO: duplicated from interfaces (server side) - use a common when possible by platform
export type LotSearchData = {
    item: Item;
    id: string;
    sublot?: string;
};

// TODO: duplicated from interfaces (server side) - use a common when possible by platform
export interface LotCreateData extends Omit<LotSearchData, 'id'> {
    id?: string;
    supplierLot?: string | null;
    expirationDate?: string;
    potency?: number;
    documentLine?: BaseDocumentLineBinding['_id'];
    creationDate?: string;
    expirationReferenceDate?: string;
    shelfLife?: number;
}

// TODO: duplicated from interfaces (server side) - use a common when possible by platform
export interface LotData extends LotCreateData {
    _id?: integer;
}

// TODO: duplicated from interfaces (server side) - use a common when possible by platform
export type StockDetail = {
    stockDetailQuantityInStockUnit: decimal;
};

export interface StockIssueDetail extends StockDetail {
    stockRecord: Stock;
    serialNumbers?: Array<SerialNumber>;
}

// TODO: [RM] duplicated from interfaces (server side) - use a common when possible by platform
export type StockLineDetailData = {
    stockSite: Site;
    item: Item;
    stockLineDetailQuantityInStockUnit: decimal;
    stockLineDetailQuantity: decimal;
    stockUnit: UnitOfMeasure;
    unit: UnitOfMeasure;
    stockStatus: StockStatus;
    owner: string;
    effectiveDate: Date;
    orderCost: decimal;
    valuedCost: decimal;
    location?: Location | null;
    lot?: Lot | null;
    lotCreationData?: LotCreateData;
};

// TODO: [RM] duplicated from interfaces (server side) - use a common when possible by platform
export type StockLineDetail = StockLineDetailData & {
    baseDocumentLine: BaseDocumentLine;
};

export interface WorkOrderReturned {
    _id: string;
    number: string;
    name: string;
    productionItems: {
        query: {
            edges: {
                node: {
                    _id: string | null;
                    releasedQuantity: number | null;
                    workInProgress: {
                        _id: string | null;
                    };
                };
            }[];
        };
    };
}
