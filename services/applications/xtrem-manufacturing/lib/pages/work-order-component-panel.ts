import { extractEdges } from '@sage/xtrem-client';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import type { GraphApi, WorkOrderComponent, WorkOrderOperation } from '@sage/xtrem-manufacturing-api';
import type { ComponentStatus, WorkOrderComponentInput } from '@sage/xtrem-manufacturing-api-partial';
import type { Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { AllocationRequestStatus, StockAllocationStatus } from '@sage/xtrem-stock-data-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import type { BomLineType } from '@sage/xtrem-technical-data-api';
import * as ui from '@sage/xtrem-ui';
import { noop } from 'lodash';

@ui.decorators.page<WorkOrderComponentPanel>({
    title: 'Work order component',
    mode: 'tabs',
    node: '@sage/xtrem-manufacturing/WorkOrderComponent',
    module: 'xtrem-manufacturing',
    isTransient: true,

    businessActions() {
        return [this.cancelComponent, this.createComponent];
    },
    async onLoad() {
        this.workOrder = this.$.queryParameters.workOrder;
        this.baseQuantity = this.$.queryParameters.baseQuantity as number;
        this.releasedQuantity = this.$.queryParameters.releasedQuantity as number;
        this.site = this.$.queryParameters.site;
        this.components = JSON.parse(this.$.queryParameters.components as string);
        this.lineData = JSON.parse(this.$.queryParameters.component as string);
        this._defaultDimensions = JSON.parse(this.$.queryParameters._defaultDimensions as string);
        this.setPageValues(
            this.$.queryParameters.id as string,
            this.lineData,
            this.$.queryParameters.highestNumber as number,
        );
        await this.getComponentItems();
        this.$.loader.isHidden = true;
        if (this._id.value && this._id.value !== '0') {
            this.$.page.title = `${ui.localize(
                '@sage/xtrem-manufacturing/pages__work_order_component_panel__edit____title',
                'Edit work order component',
            )}`;
        } else {
            this.$.page.title = `${ui.localize(
                '@sage/xtrem-manufacturing/pages__work_order_component_panel__new____title',
                'New work order component',
            )}`;
        }
    },
})
export class WorkOrderComponentPanel extends ui.Page<GraphApi> {
    private components: number[];

    private workOrder: string | number | boolean;

    private site: string | number | boolean;

    private consumedQuantity: number;

    private lineStatus: ComponentStatus;

    private isAdded: boolean;

    private plannedCost: string;

    private componentItems: any[] = []; // Not any but itemSite

    private componentItemIds: number[] = [];

    private componentIndex: number | null = null;

    private baseQuantity: number;

    private releasedQuantity: number;

    private lineData: ui.PartialNodeWithId<WorkOrderComponent>;

    private _defaultDimensions: financeInterfaces.DefaultDimensions;

    @ui.decorators.pageAction<WorkOrderComponentPanel>({
        title: 'OK',
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                this.$.finish({
                    ...this.lineData,
                    _id: this._id.value,
                    componentNumber: this.componentNumber.value || '',
                    lineType: this.lineType.value as BomLineType,
                    item: this.item.value || null,
                    name: this.name.value || null,
                    unit: this.unit.value || null,
                    operation: this.operation.value || null,
                    isFixedLinkQuantity: this.isFixedLinkQuantity.value || false,
                    scrapFactor: this.scrapFactor.value || 0,
                    linkQuantity: this.linkQuantity.value || 0,
                    requiredQuantity: this.requiredQuantity.value || 0,
                    consumedQuantity: this.consumedQuantity,
                    lineStatus: this.lineStatus as ComponentStatus,
                    isAdded: this.isAdded,
                    instruction: { value: this.instruction.value || '' },
                } as WorkOrderComponentInput);
            } else {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            }
        },
    })
    createComponent: ui.PageAction;

    @ui.decorators.pageAction<WorkOrderComponentPanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancelComponent: ui.PageAction;

    @ui.decorators.section<WorkOrderComponentPanel>({
        title: 'Information',
    })
    componentSection: ui.containers.Section;

    @ui.decorators.block<WorkOrderComponentPanel>({
        parent() {
            return this.componentSection;
        },
    })
    componentBlock: ui.containers.Block;

    @ui.decorators.block<WorkOrderComponentPanel>({
        parent() {
            return this.componentSection;
        },
    })
    detailPanelDimensionsBlock: ui.containers.Block;

    @ui.decorators.textField<WorkOrderComponentPanel>({
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.labelField<WorkOrderComponentPanel>({
        parent() {
            return this.componentBlock;
        },
        title: 'Allocation request status',
        optionType: '@sage/xtrem-stock-data/AllocationRequestStatus',
        isFullWidth: true,
        isHidden() {
            return ['noRequest', 'completed'].includes(this.allocationRequestStatus.value || '');
        },
        style() {
            return PillColorStock.getLabelColorByStatus('AllocationRequestStatus', this.allocationRequestStatus.value);
        },
    })
    allocationRequestStatus: ui.fields.Label<AllocationRequestStatus>;

    @ui.decorators.numericField<WorkOrderComponentPanel>({
        title: 'Component number',
        isMandatory: true,
        parent() {
            return this.componentBlock;
        },
        validation(newValue: number) {
            if (!this.componentNumber.isReadOnly && this.components.includes(newValue)) {
                return `Component number ${newValue} already exists`;
            }
            return undefined;
        },
    })
    componentNumber: ui.fields.Numeric;

    @ui.decorators.dropdownListField<WorkOrderComponentPanel>({
        title: 'Type',
        isMandatory: true,
        optionType: '@sage/xtrem-technical-data/BomLineType',
        parent() {
            return this.componentBlock;
        },
        onChange() {
            if (this.lineType.value === 'text') {
                this.item.value = null;
                this.isStockManaged.value = false;
                this.unit.value = null;
                this.isFixedLinkQuantity.value = false;
                this.linkQuantity.value = 0;
                this.scrapFactor.value = 0;
                this.requiredQuantity.value = 0;
                this.lineStatus = 'completed';
            }
            this.setValuesToEnter(this.lineType.value === 'text');
        },
    })
    lineType: ui.fields.DropdownList;

    @ui.decorators.referenceField<WorkOrderComponentPanel, Item>({
        parent() {
            return this.componentBlock;
        },
        title: 'Item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.checkbox({ bind: 'isStockManaged', title: 'Stock management' }),
            ui.nestedFields.reference<WorkOrderComponentPanel, Item, Item['stockUnit']>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'description',
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name', width: 'large', isReadOnly: true }),
                    ui.nestedFields.text({ bind: 'id', isHidden: true }),
                    ui.nestedFields.text({ bind: 'symbol', isHidden: true }),
                    ui.nestedFields.text({ bind: 'decimalDigits', isHidden: true }),
                ],
            }),
        ],
        onChange() {
            if (this.item.value) {
                this.name.value = this.item.value.name || null;
                this.unit.value = this.item.value.stockUnit || null;
                this.isStockManaged.value = this.item.value.isStockManaged || null;
                this.componentIndex = null;
                this.linkQuantity.scale = this.item?.value?.stockUnit?.decimalDigits;
                this.requiredQuantity.scale = this.item?.value?.stockUnit?.decimalDigits;
            }
        },
        filter() {
            return {
                isStockManaged: true,
                itemSites: { _atLeast: 1, site: `${this.site}` },
                ...(this.$.isServiceOptionEnabled('phantomItemOption') ? { isPhantom: false } : {}),
            };
        },
    })
    item: ui.fields.Reference<Item>;

    @ui.decorators.checkboxField<WorkOrderComponentPanel>({
        title: 'Stock management',
        parent() {
            return this.componentBlock;
        },
        isReadOnly: true,
    })
    isStockManaged: ui.fields.Checkbox;

    @ui.decorators.textField<WorkOrderComponentPanel>({
        title: 'Component description',
        parent() {
            return this.componentBlock;
        },
    })
    name: ui.fields.Text;

    @ui.decorators.referenceField<WorkOrderComponentPanel, WorkOrderOperation>({
        parent() {
            return this.componentBlock;
        },
        title: 'Operation',
        node: '@sage/xtrem-manufacturing/WorkOrderOperation',
        lookupDialogTitle: 'Select work order operation',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'operationNumber',
        columns: [
            ui.nestedFields.text({
                title: 'Operation number',
                bind: 'operationNumber',
            }),
            ui.nestedFields.text({
                title: 'Name',
                bind: 'name',
            }),
            ui.nestedFields.reference<WorkOrderComponentPanel, WorkOrderOperation, WorkOrderOperation['workOrder']>({
                bind: 'workOrder',
                node: '@sage/xtrem-manufacturing/WorkOrder',
                valueField: 'number',
                isHidden: true,
            }),
        ],
        filter() {
            return { workOrder: { number: `${this.workOrder}` } };
        },
    })
    operation: ui.fields.Reference<WorkOrderOperation>;

    @ui.decorators.referenceField<WorkOrderComponentPanel, UnitOfMeasure>({
        parent() {
            return this.componentBlock;
        },
        title: 'Unit of measure',
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
        lookupDialogTitle: 'Select unit of measure',
        minLookupCharacters: 1,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', isHidden: true }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.text({ bind: 'description' }),
        ],
        valueField: 'name',
        helperTextField: 'symbol',
        isReadOnly: true,
    })
    unit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.checkboxField<WorkOrderComponentPanel>({
        title: 'Fixed quantity',
        parent() {
            return this.componentBlock;
        },
        async onChange() {
            await this.calculateRequiredQuantity();
        },
    })
    isFixedLinkQuantity: ui.fields.Checkbox;

    @ui.decorators.numericField<WorkOrderComponentPanel>({
        title: 'Link quantity',
        isMandatory: true,
        isNotZero: true,
        scale() {
            return this.item?.value?.stockUnit?.decimalDigits || 0;
        },
        postfix() {
            return this.item?.value?.stockUnit?.symbol || '';
        },
        parent() {
            return this.componentBlock;
        },
        async onChange() {
            await this.calculateRequiredQuantity();
        },
    })
    linkQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<WorkOrderComponentPanel>({
        title: 'Scrap factor %',
        min: 0.0,
        max: 99.99,
        scale: 2,
        parent() {
            return this.componentBlock;
        },
        async onChange() {
            await this.calculateRequiredQuantity();
        },
    })
    scrapFactor: ui.fields.Numeric;

    @ui.decorators.numericField<WorkOrderComponentPanel>({
        title: 'Required quantity',
        isMandatory: true,
        isNotZero: true,
        scale() {
            return this.item?.value?.stockUnit?.decimalDigits || 0;
        },
        postfix() {
            return this.item?.value?.stockUnit?.symbol || '';
        },
        parent() {
            return this.componentBlock;
        },
        validation() {
            return this.updateComponentFields() || this.checkAllocationRequestStatus() || undefined;
        },
    })
    requiredQuantity: ui.fields.Numeric;

    @ui.decorators.richTextField<WorkOrderComponentPanel>({
        parent() {
            return this.componentBlock;
        },
        width: 'large',
        title: 'Instructions',
        capabilities: validCapabilities,
    })
    instruction: ui.fields.RichText;

    @ui.decorators.buttonField<WorkOrderComponentPanel>({
        parent() {
            return this.detailPanelDimensionsBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-manufacturing/pages__work_order_component_panel__dimensions_button_text',
                'Dimensions',
            );
        },
        onError(error) {
            if (!(error instanceof Error) && !(typeof error === 'string')) {
                return noop();
            }
            return utils.formatError(this, error as any);
        },
        async onClick() {
            if (!this.lineData) {
                this.lineData = { _id: '-1' };
            }
            this.lineData = await dimensionPanelHelpers.editDisplayDimensions(
                this,
                {
                    documentLine: this.lineData as financeInterfaces.BaseDocumentLineWithAnalytical,
                },
                {
                    editable: ['pending', 'inProgress'].includes(this.lineData.lineStatus || ''),
                },
            );
        },
        isDisabled() {
            return this.lineType.value === 'text';
        },
    })
    dimensionsButton: ui.fields.Button;

    @ui.decorators.section<WorkOrderComponentPanel>({
        title: 'Stock',
        isHidden() {
            return (
                !['pending', 'inProgress'].includes(this.lineData.lineStatus || '') ||
                this.isStockManaged.value === false
            );
        },
    })
    stockSection: ui.containers.Section;

    @ui.decorators.block<WorkOrderComponentPanel>({
        parent() {
            return this.stockSection;
        },
    })
    stockBlock: ui.containers.Block;

    @ui.decorators.labelField<WorkOrderComponentPanel>({
        title: 'Stock shortage status',
        parent() {
            return this.stockBlock;
        },
        backgroundColor: value =>
            PillColorStock.setBooleanStatusColors('stockShortageStatus', value, 'backgroundColor'),
        borderColor: value => PillColorStock.setBooleanStatusColors('stockShortageStatus', value, 'borderColor'),
        color: value => PillColorStock.setBooleanStatusColors('stockShortageStatus', value, 'textColor'),
        map: value =>
            value === 'true'
                ? ui.localize(
                      '@sage/xtrem-manufacturing/pages__work_order_component_panel__display_shortage_status_true',
                      'Stock shortage',
                  )
                : ui.localize(
                      '@sage/xtrem-manufacturing/pages__work_order_component_panel__display_shortage_status_false',
                      'Available stock',
                  ),
    })
    stockShortageStatus: ui.fields.Label;

    @ui.decorators.separatorField<WorkOrderComponentPanel>({
        parent() {
            return this.stockBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorShortage: ui.fields.Separator;

    @ui.decorators.numericField<WorkOrderComponentPanel>({
        title: 'Stock on hand',
        parent() {
            return this.stockBlock;
        },
        isReadOnly: true,
    })
    stockOnHand: ui.fields.Numeric;

    @ui.decorators.numericField<WorkOrderComponentPanel>({
        title: 'Available stock',
        parent() {
            return this.stockBlock;
        },
        isReadOnly: true,
    })
    stockAvailable: ui.fields.Numeric;

    @ui.decorators.numericField<WorkOrderComponentPanel>({
        title: 'Stock shortage',
        parent() {
            return this.stockBlock;
        },
        isReadOnly: true,
    })
    stockShortage: ui.fields.Numeric;

    @ui.decorators.block<WorkOrderComponentPanel>({
        parent() {
            return this.stockSection;
        },
    })
    allocationBlock: ui.containers.Block;

    @ui.decorators.labelField<WorkOrderComponentPanel>({
        title: 'Allocation status',
        parent() {
            return this.allocationBlock;
        },
        optionType: '@sage/xtrem-stock-data/StockAllocationStatus',
        style() {
            return PillColorStock.getLabelColorByStatus('StockAllocationStatus', this.allocationStatus.value);
        },
    })
    allocationStatus: ui.fields.Label<StockAllocationStatus>;

    @ui.decorators.separatorField<WorkOrderComponentPanel>({
        parent() {
            return this.allocationBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorAllocation: ui.fields.Separator;

    @ui.decorators.numericField<WorkOrderComponentPanel>({
        title: 'Quantity to allocate',
        isReadOnly: true,
        scale() {
            return this.item?.value?.stockUnit?.decimalDigits || 0;
        },
        postfix() {
            return this.item?.value?.stockUnit?.symbol || '';
        },
        parent() {
            return this.allocationBlock;
        },
    })
    quantityToAllocate: ui.fields.Numeric;

    @ui.decorators.numericField<WorkOrderComponentPanel>({
        title: 'Allocated quantity',
        parent() {
            return this.allocationBlock;
        },
        isReadOnly: true,
    })
    quantityAllocated: ui.fields.Numeric;

    @ui.decorators.numericField<WorkOrderComponentPanel>({
        title: 'Remaining quantity',
        parent() {
            return this.allocationBlock;
        },
        isReadOnly: true,
    })
    remainingQuantityToAllocate: ui.fields.Numeric;

    private setPageValues(id: string, component: ui.PartialNodeWithId<WorkOrderComponent>, highestNumber: number) {
        if (component._id) {
            this.allocationRequestStatus.value = component.allocationRequestStatus || 'noRequest';
            this.componentNumber.value = component.componentNumber || null;
            this.lineType.value = component.lineType || 'normal';
            this.item.value = component.item || null;
            this.isStockManaged.value = component.item?.isStockManaged || false;
            this.name.value = component.name || '';
            this.unit.value = component.unit || null;
            this.operation.value = component.operation || null;
            this.isFixedLinkQuantity.value = component.isFixedLinkQuantity || false;
            this.scrapFactor.value = Number(component.scrapFactor);
            this.linkQuantity.value = Number(component.linkQuantity);
            this.requiredQuantity.value = Number(component.requiredQuantity);
            this.consumedQuantity = Number(component.consumedQuantity);
            this.lineStatus = component.lineStatus || 'pending';
            this.isAdded = component.isAdded || false;
            this.plannedCost = component.plannedCost || '0';
            this.instruction.value = component.instruction?.value || '';
            this._id.value = id;
            this.stockOnHand.value = Number(component.stockOnHand);
            this.stockAvailable.value = Number(component.availableQuantityInStockUnit);
            this.stockShortage.value = Number(component.stockShortageInStockUnit);
            this.stockShortageStatus.value = String(component.stockShortageStatus);
            this.quantityToAllocate.value = Number(component.requiredQuantity);
            this.quantityAllocated.value = Number(component.quantityAllocated);
            this.remainingQuantityToAllocate.value = Number(component.remainingQuantityToAllocate);
            this.allocationStatus.value = component.allocationStatus;

            if (this.lineStatus !== 'pending') {
                this.setFieldStatus(true);
                this.setValuesToEnter(true);
            } else {
                this.setFieldStatus(false);
                this.setValuesToEnter(this.lineType.value === 'text');
            }
            this.componentNumber.isReadOnly = true;
            this.lineType.isReadOnly = true;
            this.item.isReadOnly = true;
        } else {
            this.allocationRequestStatus.value = 'noRequest';
            this.componentNumber.value = highestNumber + (10 - (highestNumber % 10));
            this.lineType.value = 'normal';
            this.item.value = null;
            this.isStockManaged.value = false;
            this.name.value = '';
            this.unit.value = null;
            this.operation.value = null;
            this.isFixedLinkQuantity.value = false;
            this.scrapFactor.value = 0;
            this.linkQuantity.value = 0;
            this.requiredQuantity.value = 0;
            this.consumedQuantity = 0;
            this.lineStatus = 'pending';
            this.isAdded = true;
            this.plannedCost = '0';

            this.setFieldStatus(false);
            this.setValuesToEnter(false);
            this.componentNumber.isReadOnly = false;
            this.lineType.isReadOnly = false;

            this.lineData.lineStatus = this.lineStatus as ComponentStatus;

            // using as any cause partial type is used in the function and not for linedata
            this.lineData = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                this.lineData as any,
                this._defaultDimensions,
            ) as any;
            this.stockSection.isHidden = true;
        }
    }

    private setFieldStatus(status: boolean) {
        // status is false for pending line, and true otherwise
        this.lineType.isReadOnly = status;
        this.item.isReadOnly = status;
        this.operation.isReadOnly = status;
        this.name.isReadOnly = status;
        this.isFixedLinkQuantity.isReadOnly = status;
        this.linkQuantity.isReadOnly = status;
        this.scrapFactor.isReadOnly = status;
        this.requiredQuantity.isReadOnly = status;
    }

    private setValuesToEnter(status: boolean) {
        // status is false for normal line, and true for text
        this.item.isReadOnly = status;
        this.item.isMandatory = !status;
        this.isFixedLinkQuantity.isReadOnly = status;
        this.linkQuantity.isReadOnly = status;
        this.linkQuantity.isMandatory = !status;
        this.linkQuantity.isNotZero = !status;
        this.scrapFactor.isReadOnly = status;
        this.requiredQuantity.isReadOnly = status;
        this.requiredQuantity.isMandatory = !status;
        this.requiredQuantity.isNotZero = !status;
        this.dimensionsButton.isDisabled = status;
    }

    private updateComponentFields() {
        if (this.componentItems) {
            if (this.componentIndex === null) {
                this.componentIndex = this.componentItems.findIndex(
                    comp => +comp.item._id === Number(this.item.value?._id),
                );
            }
            return this.updateComponentDetails();
        }
        return undefined;
    }

    private updateComponentDetails() {
        const consumedQuantity = +this.consumedQuantity;
        const requiredQuantity = this.requiredQuantity.value ? +this.requiredQuantity.value : 0;
        if (consumedQuantity <= requiredQuantity && requiredQuantity >= 0) {
            if (consumedQuantity > 0) {
                if (consumedQuantity === requiredQuantity) {
                    this.lineStatus = 'completed';
                } else {
                    this.lineStatus = 'inProgress';
                }
            }
        } else {
            return ui.localize(
                '@sage/xtrem-manufacturing/required-quantity-lesser',
                'The required quantity must be more than or equal to the quantity already consumed',
            );
        }
        return undefined;
    }

    private async getComponentItems() {
        this.componentItems = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-master-data/ItemSite')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            item: { _id: true },
                            batchQuantity: true,
                        },
                        {
                            filter: { site: `${this.site}`, item: { isStockManaged: true } },
                            first: 500,
                        },
                    ),
                )
                .execute(),
        );

        if (this.componentItems?.length > 0) {
            if (this.componentItemIds) {
                this.componentItemIds = [];
            }
            this.componentItemIds.push(...this.componentItems.map(data => data.item._id));
        }
    }

    private async calculateRequiredQuantity() {
        if (this.linkQuantity.value && this.linkQuantity.value > 0) {
            this.requiredQuantity.value = await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrderComponent')
                .queries.setRequiredQuantity(true, {
                    releasedQuantity: this.releasedQuantity,
                    baseQuantity: this.baseQuantity,
                    linkQuantity: this.linkQuantity.value,
                    isFixed: this.isFixedLinkQuantity.value,
                    scrapFactor: this.scrapFactor.value,
                    decimalPlaces: this.unit.value?.decimalDigits,
                })
                .execute();
            this.updateComponentFields();
        }
    }

    private checkAllocationRequestStatus() {
        // Automatic allocation in progress : we can't decrease the requiredQuantity
        if (
            this.requiredQuantity.isDirty &&
            Number(this.requiredQuantity.value || 0) < Number(this.lineData.requiredQuantity || 0) &&
            this.lineData.allocationRequestStatus === 'inProgress'
        ) {
            return ui.localize(
                '@sage/xtrem-manufacturing/page__work_order_component__auto_allocation_cannot_decrease_quantity',
                'You can only reduce the required quantity of the component after the allocation request is complete.',
            );
        }
        return undefined;
    }
}
