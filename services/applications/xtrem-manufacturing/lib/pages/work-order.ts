import { asyncArray } from '@sage/xtrem-async-helper';
import type { Dict, ExtractEdgesPartial, OperationResultType } from '@sage/xtrem-client';
import { aggregateEdgesSelector, extractEdges, querySelector, withoutEdges } from '@sage/xtrem-client';
import { datetime } from '@sage/xtrem-date-time';
import type { FinanceTransactionBinding } from '@sage/xtrem-finance-data-api';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type {
    G<PERSON>h<PERSON><PERSON>,
    WorkOrder$Operations,
    WorkOrderCategory,
    WorkOrderComponent,
    WorkOrder as WorkOrderNode,
    WorkOrderOperation,
    WorkOrderOperationResource,
    WorkOrderOperationResourceDetail,
    WorkOrderOperationResourceInput,
    WorkOrderReleasedItem,
    WorkOrderStatus,
} from '@sage/xtrem-manufacturing-api';
import type { BaseResource, CapabilityLevel, DetailedResource, Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { manufacturing } from '@sage/xtrem-master-data/build/lib/menu-items/manufacturing';
import type {
    AllocationRequestStatus,
    StockAllocationStatus,
    StockDocumentTransactionStatus,
} from '@sage/xtrem-stock-data-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Site } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import type { BillOfMaterial, BillOfMaterialRevision, Routing } from '@sage/xtrem-technical-data-api';
import * as ui from '@sage/xtrem-ui';
import { isEmpty, isObject } from 'lodash';
import type { TrackingDocument, WorkOrderTrackingView } from '../client-functions/interfaces/work-order';
import * as PillColor from '../client-functions/pill-color';
import * as WorkOrderFunctions from '../client-functions/work-order';
import {
    convertStringToJsDate,
    incompleteWorkOrderCloseDialog,
    requestDateBeforeTodayWarning,
} from '../client-functions/work-order';
import {
    checkAssignmentLinkExist,
    isOrderToOrderServiceOptionActivated,
    isWorkOrderAssignmentCheckedOnWorkOrderClose,
    isWorkOrderAssignmentCheckedOnWorkOrderDelete,
    isWorkOrderAssignmentCheckedOnWorkOrderQuantityChanged,
    openOrderAssignments,
} from './shared/work-order-assignment';

@ui.decorators.page<WorkOrder, WorkOrderNode>({
    title: 'Work order',
    objectTypeSingular: 'Work order',
    objectTypePlural: 'Work orders',
    idField() {
        return this.number;
    },
    hasAttachmentsSection: true,
    mode: 'tabs',
    menuItem: manufacturing,
    priority: 50,
    node: '@sage/xtrem-manufacturing/WorkOrder',
    module: 'manufacturing',
    async onLoad() {
        if (this.$.recordId) {
            this.initPosting();
            this.manageDisplayButtonGoToSysNotificationPageAction();
            this.setBomAndRoutingFlags();
            this.updateReleasedItem(this.$.recordId);
            await this.getTrackings(this.$.recordId);
            this.updateCostValues();
            this.setPageReadonly();
            this.allocationStatus.isHidden = ['closed', 'completed'].includes(this.status.value || '');
            this.closingDate.isHidden = this.status.value !== 'closed';
            if (
                this.isServiceOptionsSerialNumberActive.value === true &&
                this.checkPreGenerateSerialNumbers() === true
            ) {
                await this.pregeneratedBusinessRules();
            }
            this.oldReleasedItemQuantity = this.releasedItemQuantity.value ? this.releasedItemQuantity.value : 0;
        }

        // Set the current quantity so we can recalculate after subsequent changes
        this.currentQuantity = this.releasedItemQuantity.value || 0;

        this.inheritDimensions = false; // default value false => dimensions are not inherited from released item to operations and components

        const initialDefaultDimensionsObject = await attributesAndDimensions.initDefaultDimensions({
            page: this,
            dimensionDefinitionLevel: 'manufacturingDirect',
            site: this.site.value,
        });
        this._defaultDimensionsAttributesArray = [
            // first index of this array is for the production operation resources collection
            { ...initialDefaultDimensionsObject },
            // second index of this array is for the production components collection
            { ...initialDefaultDimensionsObject },
        ];
        this.defaultDimensionActionArray = [
            // first index of this array is for the production operation resources collection
            this.productionOperationsDefaultDimension,
            // first index of this array is for the production components collection
            this.productionComponentsDefaultDimension,
        ];
        this.hideShowDefaultDimensionActions();
        this.$.setPageClean();
        setApplicativePageCrudActions({
            page: this,
            isDirty: this.$.isDirty,
            save: this.saveWorkOrder,
            cancel: this.$standardCancelAction,
            remove: this.deleteWorkOrder,
            duplicate: this.$standardDuplicateAction,
            actions: [this.$standardOpenCustomizationPageWizardAction],
        });
        this.setDeleteAction();
        this.stockTransactionStatus.isHidden = ['draft', 'completed'].includes(
            this.stockTransactionStatus.value as StockDocumentTransactionStatus,
        );
        await this.hideShowAssignOrder();
        this.initFromNotificationHistoryParameters();
    },
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'printBulk',
                title: 'Print job traveler',
                icon: 'print',
                buttonType: 'primary',
            },
            {
                mutation: 'closeWorkOrder',
                title: 'Close orders',
                icon: 'close',
                buttonType: 'primary',
                configurationPage: '@sage/xtrem-manufacturing/WorkOrderClosePanel',
            },
            {
                mutation: 'bulkPrintPickList',
                title: 'Print pick list',
                icon: 'print',
                buttonType: 'primary',
            },
        ],
        orderBy: { startDate: -1, number: -1 },
        startDateField: 'startDate',
        endDateField: 'endDate',
        cardColor(row) {
            switch (row?.type) {
                case 'firm':
                    return ui.tokens.colorsSemanticPositive500;
                case 'planned':
                    return ui.tokens.colorsSemanticCaution500;
                default:
                    return '';
            }
        },
        isEventMovable: false,
        listItem: {
            image: ui.nestedFields.image({
                bind: { productionItem: { releasedItem: { image: true } } },
                title: 'Image',
            }),
            title: ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            titleRight: ui.nestedFields.text({ bind: 'name' }),
            itemName: ui.nestedFields.text({
                title: 'Item',
                bind: { bomCode: { item: { name: true } } },
                isDisabled: true,
            }),
            line2: ui.nestedFields.text({
                title: 'Item ID',
                bind: { bomCode: { item: { id: true } } },
                isDisabled: true,
            }),
            itemDescription: ui.nestedFields.text({
                title: 'Item Description',
                bind: { bomCode: { item: { description: true } } },
                isDisabled: true,
                isHiddenOnMainField: true,
            }),
            type: ui.nestedFields.label({
                optionType: '@sage/xtrem-manufacturing/WorkOrderType',
                bind: 'type',
                title: 'Type',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('WorkOrderType', rowData.type),
            }),
            releasedQuantity: ui.nestedFields.reference({
                bind: 'productionItem',
                valueField: 'releasedQuantity',
                node: '@sage/xtrem-manufacturing/WorkOrderReleasedItem',
                title: 'Released quantity',
                tunnelPage: undefined,
            }),
            totalActualQuantity: ui.nestedFields.reference({
                bind: 'productionItem',
                valueField: 'totalActualQuantity',
                node: '@sage/xtrem-manufacturing/WorkOrderReleasedItem',
                title: 'Completed quantity',
                tunnelPage: undefined,
            }),
            category: ui.nestedFields.reference({
                bind: 'category',
                valueField: 'name',
                node: '@sage/xtrem-manufacturing/WorkOrderCategory',
                title: 'Category',
                tunnelPage: undefined,
            }),
            startDate: ui.nestedFields.date({ bind: 'startDate', title: 'Start date' }),
            endDate: ui.nestedFields.date({ bind: 'endDate', title: 'End date' }),
            site: ui.nestedFields.reference({
                bind: 'site',
                valueField: 'id',
                node: '@sage/xtrem-system/Site',
                title: 'Site',
                tunnelPage: undefined,
            }),
            line2Right: ui.nestedFields.label({
                optionType: '@sage/xtrem-manufacturing/WorkOrderStatus',
                bind: 'status',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('WorkOrderStatus', rowData.status),
                title: 'Status',
            }),
        },
        optionsMenu: [
            { title: 'All', id: 'all', graphQLFilter: {} },
            { title: 'Pending', id: 'pending', graphQLFilter: { status: { _eq: 'pending' } } },
            { title: 'In progress', id: 'inProgress', graphQLFilter: { status: { _eq: 'inProgress' } } },
            { title: 'Completed', id: 'completed', graphQLFilter: { status: { _eq: 'completed' } } },
            { title: 'Closed', id: 'closed', graphQLFilter: { status: { _eq: 'closed' } } },
            { title: 'Cost calculated', id: 'costCalculated', graphQLFilter: { status: { _eq: 'costCalculated' } } },
            { title: 'Printed', id: 'printed', graphQLFilter: { status: { _eq: 'printed' } } },
        ],
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty: isDirty && !this.fromNotificationHistory,
            save: this.saveWorkOrder,
            cancel: this.$standardCancelAction,
            remove: this.deleteWorkOrder,
            duplicate: this.$standardDuplicateAction,
            actions: [this.$standardOpenCustomizationPageWizardAction],
        });

        this.setDeleteAction();
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.deleteWorkOrder],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.deallocate,
                this.allocate,
                ui.menuSeparator(),
                this.printPickList,
                this.print,
            ],
        });
    },
    createAction() {
        return this.createWorkOrder;
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction, this.orderToOrderIcon];
    },
    businessActions() {
        return [
            this.$standardCancelAction,
            this.editWorkOrderReleasedItemDimensions,
            this.preGenerateSerialNumbers,
            this.preGenerateAdditionalSerialNumbers,
            this.viewSerialNumbers,
            this.scheduleWorkOrder,
            this.closeWorkOrder,
            this.saveWorkOrder,
            this.repost,
        ];
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
})
export class WorkOrder
    extends ui.Page<GraphApi, WorkOrderNode>
    implements financeInterfaces.PageWithMultipleDefaultDimensions
{
    _defaultDimensionsAttributesArray: financeInterfaces.DefaultDimensions[];

    defaultDimensionActionArray: ui.PageAction[];

    workOrderReleasedItemDimensions: Omit<financeInterfaces.DefaultDimensions, 'action'>;

    oldReleasedItemQuantity?: number = 0;

    fromNotificationHistory: boolean;

    wipSourceDocumentType: string | number | boolean;

    financeTransactionSysId: string | number | boolean;

    inheritDimensions: boolean;

    @ui.decorators.section<WorkOrder>({ title: 'General', isTitleHidden: true })
    generalSection: ui.containers.Section;

    @ui.decorators.section<WorkOrder>({ title: 'Components' })
    componentSection: ui.containers.Section;

    @ui.decorators.section<WorkOrder>({ title: 'Operations' })
    operationSection: ui.containers.Section;

    @ui.decorators.section<WorkOrder>({
        title: 'Trackings',
    })
    trackingsSection: ui.containers.Section;

    @ui.decorators.section<WorkOrder>({
        title: 'Posting',
        isHidden() {
            return !this.site.value?.legalCompany?.doWipPosting || !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<WorkOrder>({
        parent() {
            return this.postingSection;
        },
        title: 'Error detail',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.section<WorkOrder>({
        title: 'Notes',
    })
    noteSection: ui.containers.Section;

    @ui.decorators.block<WorkOrder>({
        parent() {
            return this.generalSection;
        },
        title: 'Technical',
        isHidden: true,
    })
    technicalBlock: ui.containers.Block;

    @ui.decorators.block<WorkOrder>({
        parent() {
            return this.generalSection;
        },
        title: 'General information',
        width: 'large',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.block<WorkOrder>({
        parent() {
            return this.noteSection;
        },
        title: 'Notes',
        isTitleHidden: true,
    })
    noteBlock: ui.containers.Block;

    @ui.decorators.tableField<WorkOrder, FinanceTransactionBinding>({
        title: 'Results',
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        isReadOnly: true,
        pageSize: 10,
        displayMode: ui.fields.TableDisplayMode.compact,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page: '@sage/xtrem-finance/JournalEntry',
                queryParameters(_value, rowData: FinanceTransactionBinding) {
                    return {
                        _id: rowData.targetDocumentSysId,
                        number: rowData.targetDocumentNumber,
                    };
                },
            }),
            ui.nestedFields.link({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                onClick(_value, rowData) {
                    if (rowData.financeIntegrationAppUrl) {
                        this.$.router.goToExternal(rowData.financeIntegrationAppUrl);
                    }
                },
                isHidden() {
                    return this.postingDetails.value.at(0)?.financeIntegrationApp !== 'intacct';
                },
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData.postingStatus),
            }),
            ui.nestedFields.select({
                title: 'Tracking type',
                bind: 'sourceDocumentType',
                optionType: '@sage/xtrem-finance-data/SourceDocumentType',
            }),
            ui.nestedFields.link({
                title: 'Tracking number',
                bind: 'sourceDocumentNumber',
                map(_value, rowData: FinanceTransactionBinding) {
                    if (rowData.sourceDocumentType !== 'workOrderClose') return rowData.sourceDocumentNumber;
                    return '';
                },
                onClick(_value, rowData) {
                    return this.$.dialog.page(
                        WorkOrderFunctions.getTrackingPage(rowData.sourceDocumentType),
                        { _id: rowData.sourceDocumentNumber },
                        { fullScreen: true, resolveOnCancel: true },
                    );
                },
            }),
            ui.nestedFields.technical({ bind: 'message' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table<FinanceTransactionBinding>;

    @ui.decorators.textAreaField<WorkOrder>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<WorkOrder>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'workInProgress',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.status ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.block<WorkOrder>({
        parent() {
            return this.generalSection;
        },
        title: 'Planned vs. actual',
        width: 'small',
    })
    additionalInfoBlock: ui.containers.Block;

    @ui.decorators.textField<WorkOrder>({
        parent() {
            return this.technicalBlock;
        },
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<WorkOrder>({
        parent() {
            return this.technicalBlock;
        },
        title: 'Work order number',
    })
    number: ui.fields.Text;

    @ui.decorators.textField<WorkOrder>({
        parent() {
            return this.technicalBlock;
        },
        title: 'Name',
    })
    name: ui.fields.Text;

    @ui.decorators.referenceField<WorkOrder, Site>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly() {
            return !!this.$.recordId;
        },
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-system/Company',
                bind: 'legalCompany',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [ui.nestedFields.technical({ bind: 'symbol' })],
                    }),
                    ui.nestedFields.technical({ bind: 'doWipPosting' }),
                ],
            }),
        ],
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.dropdownListField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Type',
        optionType: '@sage/xtrem-manufacturing/WorkOrderType',
        isMandatory: true,
        width: 'small',
        isDisabled() {
            return this.status.value !== 'pending';
        },
        onChange() {
            if (this.isServiceOptionsSerialNumberActive.value === true && this.type.value === 'planned') {
                this.preGenerateSerialNumbers.isHidden = true;
            }
        },
    })
    type: ui.fields.DropdownList;

    @ui.decorators.referenceField<WorkOrder, Item>({
        parent() {
            return this.mainBlock;
        },
        title: 'Item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item',
        valueField: 'name',
        helperTextField: 'id',
        bind: { productionItem: { releasedItem: true } },
        isReadOnly: true,
        columns: [ui.nestedFields.technical({ bind: 'isBomRevisionManaged' })],
    })
    releasedItem: ui.fields.Reference<Item>;

    @ui.decorators.labelField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Status',
        optionType: '@sage/xtrem-manufacturing/WorkOrderStatus',
        style() {
            return PillColor.getLabelColorByStatus('WorkOrderStatus', this.status.value);
        },
        width: 'small',
        onError(error) {
            return MasterDataUtils.formatError(this, error);
        },
        onClick() {
            return WorkOrderFunctions.resynchronizeStatus(this, this._id.value ?? '');
        },
    })
    status: ui.fields.Label<WorkOrderStatus>;

    @ui.decorators.labelField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Allocation status',
        optionType: '@sage/xtrem-stock-data/StockAllocationStatus',
        style() {
            return PillColorStock.getLabelColorByStatus('StockAllocationStatus', this.allocationStatus.value);
        },
    })
    allocationStatus: ui.fields.Label<StockAllocationStatus>;

    @ui.decorators.labelField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Allocation request status',
        optionType: '@sage/xtrem-stock-data/AllocationRequestStatus',
        isHidden() {
            return ['noRequest', 'completed'].includes(this.allocationRequestStatus.value || '');
        },
        style() {
            return PillColorStock.getLabelColorByStatus('AllocationRequestStatus', this.allocationRequestStatus.value);
        },
    })
    allocationRequestStatus: ui.fields.Label<AllocationRequestStatus>;

    releasedItemId: string;

    @ui.decorators.numericField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Quantity released',
        width: 'small',
        isReadOnly: true,
        bind: { productionItem: { releasedQuantity: true } },
        validation(this, value) {
            if (this.releasedItemQuantity.isDirty && value < (this.totalActualQuantity.value || 0)) {
                return ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order__released_quantity_less_than_completed_quantity',
                    'The released quantity is less than the completed quantity.',
                );
            }
            return undefined;
        },
        async onChange() {
            if (this.releasedItemQuantity.value) {
                const isAssignmentChecked = await isWorkOrderAssignmentCheckedOnWorkOrderQuantityChanged(this, {
                    new: this.releasedItemQuantity.value ? this.releasedItemQuantity.value : 0,
                    old: this.oldReleasedItemQuantity ? this.oldReleasedItemQuantity : 0,
                });
                if (isAssignmentChecked) {
                    this.oldReleasedItemQuantity = this.releasedItemQuantity.value
                        ? this.releasedItemQuantity.value
                        : 0;
                    await this.updateComponents(this.releasedItemQuantity.value);
                    await this.updateOperations(this.releasedItemQuantity.value, this.currentQuantity);
                    const productionItem = this.productionItems.value[0];
                    productionItem.releasedQuantity = this.releasedItemQuantity.value.toString();
                    this.productionItems.setRecordValue(productionItem);
                    this.currentQuantity = this.releasedItemQuantity.value;
                } else {
                    this.releasedItemQuantity.value = this.oldReleasedItemQuantity ? this.oldReleasedItemQuantity : 0;
                }
            }
        },
    })
    releasedItemQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Quantity completed',
        width: 'small',
        isReadOnly: true,
        bind: { productionItem: { totalActualQuantity: true } },
    })
    totalActualQuantity: ui.fields.Numeric;

    currentQuantity = 0;

    @ui.decorators.separatorField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isHidden: true,
        isDisabled: true,
    })
    separatorLine02: ui.fields.Separator;

    @ui.decorators.tableSummaryField<WorkOrder>({
        title: 'Planned vs. actual',
        isTitleHidden: true,
        isDisabled: true,
        parent() {
            return this.additionalInfoBlock;
        },
        isTransient: true,
        canSelect: false,
        isHelperTextHidden: true,
        pageSize: 4,
        columns: [
            ui.nestedFields.text({ title: 'Cost', bind: 'costTitle' }),
            ui.nestedFields.numeric({
                title: 'Planned',
                prefix(_value, rowData) {
                    return rowData?.cost ? this.site.value?.legalCompany?.currency?.symbol || '' : '';
                },
                scale: 4,
                bind: 'cost',
            }),
            ui.nestedFields.numeric({
                title: 'Actual',
                prefix(_value, rowData) {
                    return rowData?.actualCost ? this.site.value?.legalCompany?.currency?.symbol || '' : '';
                },
                scale: 4,
                bind: 'actualCost',
            }),
        ],
    })
    costs: ui.fields.TableSummary;

    @ui.decorators.numericField<WorkOrder>({
        parent() {
            return this.additionalInfoBlock;
        },
        title: 'Planned process cost',
        width: 'small',
        isReadOnly: true,
        isHidden: true,
    })
    plannedProcessCost: ui.fields.Numeric;

    @ui.decorators.numericField<WorkOrder>({
        parent() {
            return this.additionalInfoBlock;
        },
        title: 'Planned material cost',
        width: 'small',
        isReadOnly: true,
        isHidden: true,
    })
    plannedMaterialCost: ui.fields.Numeric;

    @ui.decorators.numericField<WorkOrder>({
        parent() {
            return this.additionalInfoBlock;
        },
        title: 'Planned overhead cost',
        width: 'small',
        isReadOnly: true,
        isHidden: true,
    })
    plannedOverheadCost: ui.fields.Numeric;

    @ui.decorators.numericField<WorkOrder>({
        parent() {
            return this.additionalInfoBlock;
        },
        title: 'Actual process cost',
        width: 'small',
        isReadOnly: true,
        isHidden: true,
    })
    actualProcessCost: ui.fields.Numeric;

    @ui.decorators.numericField<WorkOrder>({
        parent() {
            return this.additionalInfoBlock;
        },
        title: 'Actual material cost',
        width: 'small',
        isReadOnly: true,
        isHidden: true,
    })
    actualMaterialCost: ui.fields.Numeric;

    @ui.decorators.numericField<WorkOrder>({
        parent() {
            return this.additionalInfoBlock;
        },
        title: 'Actual overhead cost',
        width: 'small',
        isReadOnly: true,
        isHidden: true,
    })
    actualOverheadCost: ui.fields.Numeric;

    @ui.decorators.progressField<WorkOrder>({
        parent() {
            return this.additionalInfoBlock;
        },
        title: 'Production',
        width: 'small',
    })
    productionCompletionPercentage: ui.fields.Progress;

    @ui.decorators.progressField<WorkOrder>({
        parent() {
            return this.additionalInfoBlock;
        },
        title: '% process completion',
        width: 'small',
        isHidden() {
            return this.hasRouting.value === false && this.productionOperations.value.length === 0;
        },
    })
    processCompletionPercentage: ui.fields.Progress;

    @ui.decorators.progressField<WorkOrder>({
        parent() {
            return this.additionalInfoBlock;
        },
        title: '% material completion',
        width: 'small',
        isHidden() {
            return this.hasBillOfMaterial.value === false && this.productionComponents.value.length === 0;
        },
    })
    materialCompletionPercentage: ui.fields.Progress;

    @ui.decorators.buttonField<WorkOrder>({
        parent() {
            return this.additionalInfoBlock;
        },
        map: () => ui.localize('@sage/xtrem-manufacturing/updatePlannedCosts', 'Update planned costs'),
        width: 'small',
        async onClick() {
            this.$.loader.isHidden = false;
            await this.updatePlannedCosts();
            this.$.router.goTo(`@sage/xtrem-manufacturing/${this.$.page.id}`, { _id: this._id.value ?? '' });
            this.$.loader.isHidden = true;
        },
        // TODO: XT-66524 - Change back to isDisabled() once the issue XT-68914 is fixed.
        // isDisabled() {
        isHidden() {
            return (
                !this.$.recordId ||
                this.status.value === 'closed' ||
                (this.totalActualQuantity?.value ? this.totalActualQuantity.value > 0 : false)
            );
        },
        isTransient: true,
    })
    updateCostsButton: ui.fields.Button;

    @ui.decorators.separatorField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isHidden: true,
    })
    separatorLine03: ui.fields.Separator;

    @ui.decorators.checkboxField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Forward scheduling',
        isHidden: true,
    })
    isForwardScheduling: ui.fields.Checkbox;

    @ui.decorators.labelField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Scheduling status',
        optionType: '@sage/xtrem-manufacturing/WorkOrderSchedulingStatus',
        style() {
            return PillColor.getLabelColorByStatus('WorkOrderSchedulingStatus', this.schedulingStatus.value as any);
        },
        width: 'small',
        async onClick() {
            if (
                !this.$.isDirty &&
                this.status.value !== 'closed' &&
                this.schedulingStatus.value !== 'inProgress' &&
                this.productionOperations.value.length > 0
            ) {
                const userResponse = await MasterDataUtils.confirmDialogToBoolean(
                    this.$.dialog.confirmation(
                        'info',
                        ui.localize('@sage/xtrem-manufacturing/skip_scheduling_title', 'Change scheduling work flow'),
                        ui.localize(
                            '@sage/xtrem-manufacturing/skip_scheduling_question',
                            'Skip scheduling for this work order?',
                        ),
                        {
                            acceptButton: {
                                text:
                                    this.schedulingStatus.value === 'notManaged'
                                        ? ui.localize('@sage/xtrem-manufacturing/dialog_button_revert', 'Revert')
                                        : ui.localize('@sage/xtrem-manufacturing/dialog_button_confirm', 'Confirm'),
                            },
                            cancelButton: {
                                text: ui.localize('@sage/xtrem-manufacturing/dialog_button_cancel', 'Cancel'),
                            },
                            size: 'medium',
                        },
                    ),
                );
                if (userResponse) {
                    this.$.loader.isHidden = false;
                    await this.$.graph
                        .node('@sage/xtrem-manufacturing/WorkOrder')
                        .mutations.skipSchedulingWorkOrder(
                            { _id: true },
                            {
                                workOrder: this.$.recordId,
                                skip: this.schedulingStatus.value !== 'notManaged',
                            },
                        )
                        .execute();
                    this.$.loader.isHidden = true;
                    await this.$.router.refresh();
                }
            }
        },
    })
    schedulingStatus: ui.fields.Label;

    @ui.decorators.dateField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Date closed',
        width: 'small',
        isReadOnly: true,
    })
    closingDate: ui.fields.Date;

    @ui.decorators.textField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Time zone',
        isHidden: true,
    })
    timeZone: ui.fields.Text;

    @ui.decorators.dateField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Order created date',
        width: 'small',
        isHidden: true,
    })
    creationDate: ui.fields.Date;

    @ui.decorators.dateField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Requested start date',
        width: 'small',
        isMandatory: true,
        warningMessage() {
            return this.status.value === 'pending' && this.requestedDate.value
                ? requestDateBeforeTodayWarning(convertStringToJsDate(this.requestedDate.value))
                : '';
        },
    })
    requestedDate: ui.fields.Date;

    @ui.decorators.dateField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Start',
        width: 'small',
        isMandatory: true,
    })
    startDate: ui.fields.Date;

    @ui.decorators.dateField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'End',
        width: 'small',
        isMandatory: true,
        validation() {
            if (
                this.endDate.value !== null &&
                this.startDate.value !== null &&
                this.endDate.value < this.startDate.value
            ) {
                return ui.localize(
                    '@sage/xtrem-manufacturing/pages__end_date_cannot_be_earlier_than_start_date',
                    'The end date cannot be earlier than the start date.',
                );
            }
            return undefined;
        },
    })
    endDate: ui.fields.Date;

    @ui.decorators.referenceField<WorkOrder, BillOfMaterialRevision>({
        parent() {
            return this.mainBlock;
        },
        node: '@sage/xtrem-technical-data/BillOfMaterialRevision',
        title: 'Revision',
        width: 'small',
        isReadOnly: true,
        isHidden() {
            return !this.releasedItem.value?.isBomRevisionManaged;
        },
    })
    bomRevision: ui.fields.Reference<BillOfMaterialRevision>;

    @ui.decorators.labelField<WorkOrder>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        isHidden() {
            return ['draft', 'completed'].includes(this.stockTransactionStatus.value as StockDocumentTransactionStatus);
        },
        parent() {
            return this.mainBlock;
        },
        style() {
            return PillColorStock.getLabelColorByStatus(
                'StockDocumentTransactionStatus',
                this.stockTransactionStatus.value as any,
            );
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                {
                    origin: 'header',
                    tableField: this.productionItems,
                },
            );

            await WorkOrderFunctions.correctStockTransactionStatusInProgress(
                this,
                this._id.value ?? '',
                this.status.value as WorkOrderStatus,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                this.closingDate.value ?? '',
            );
        },
    })
    stockTransactionStatus: ui.fields.Label;

    @ui.decorators.referenceField<WorkOrder, Routing>({
        parent() {
            return this.mainBlock;
        },
        title: 'Routing',
        width: 'large',
        node: '@sage/xtrem-technical-data/Routing',
        tunnelPage: '@sage/xtrem-technical-data/Routing',
        valueField: 'name',
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({ title: 'Pregenerate serial numbers', bind: 'doSerialNumberPreGeneration' }),
        ],
    })
    routingCode: ui.fields.Reference<Routing>;

    @ui.decorators.referenceField<WorkOrder, WorkOrderCategory>({
        parent() {
            return this.mainBlock;
        },
        title: 'Category',
        width: 'medium',
        node: '@sage/xtrem-manufacturing/WorkOrderCategory',
        lookupDialogTitle: 'Select category',
        valueField: 'name',
        helperTextField: 'id',
        isReadOnly() {
            return !!this.$.recordId;
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.checkbox({ title: 'Routing', bind: 'routing' }),
            ui.nestedFields.checkbox({ title: 'Bill of material', bind: 'billOfMaterial' }),
        ],
    })
    category: ui.fields.Reference<WorkOrderCategory>;

    @ui.decorators.checkboxField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Uses routing',
        isTransient: true,
        isDisabled() {
            return this.disableUsesBOMorRouting(
                this.routingCode.value !== null,
                this.productionOperations.value.length,
            );
        },
        onError(e) {
            this.$.loader.isHidden = true;
            this.hasRouting.value = false;
            this.hasRouting.isDirty = false;
            return MasterDataUtils.formatError(this, e);
        },
        onChange() {
            if (
                this.isServiceOptionsSerialNumberActive.value === true &&
                this.checkPreGenerateSerialNumbers() === true
            ) {
                this.preGenerateSerialNumbers.isHidden = this.pregeneratedSerialNumbersPageAction().pregenerated;
            }
        },
        async onClick() {
            const addOperations = await this.confirmAddingComponentsOrOperations(
                ui.localize('@sage/xtrem-manufacturing/add-operations-title', 'Add operations'),
                ui.localize(
                    '@sage/xtrem-manufacturing/add-operations-content',
                    'You are about to add operations to this work order.',
                ),
            );
            if (addOperations) {
                await this.addComponentsOrOperations(
                    false,
                    this._id.value ?? '',
                    this.releasedItem.value?._id ?? '',
                    this.site.value?._id ?? '',
                );
            }
            this.hasRouting.value = addOperations;
            this.hasRouting.isDirty = false;
        },
    })
    hasRouting: ui.fields.Checkbox;

    @ui.decorators.checkboxField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Uses bill of material',
        isTransient: true,
        isDisabled() {
            return this.disableUsesBOMorRouting(this.bomCode.value !== null, this.productionComponents.value.length);
        },
        onError(e) {
            this.$.loader.isHidden = true;
            this.hasBillOfMaterial.value = false;
            this.hasBillOfMaterial.isDirty = false;
            return MasterDataUtils.formatError(this, e);
        },
        async onClick() {
            const addComponents = await this.confirmAddingComponentsOrOperations(
                ui.localize('@sage/xtrem-manufacturing/add-components-title', 'Add components'),
                ui.localize(
                    '@sage/xtrem-manufacturing/add-components-content',
                    'You are about to add components to this work order.',
                ),
            );
            if (addComponents) {
                await this.addComponentsOrOperations(
                    true,
                    this._id.value ?? '',
                    this.releasedItem.value?._id ?? '',
                    this.site.value?._id ?? '',
                );
            }
            this.hasBillOfMaterial.value = addComponents;
            this.hasBillOfMaterial.isDirty = false;
        },
    })
    hasBillOfMaterial: ui.fields.Checkbox;

    @ui.decorators.checkboxField<WorkOrder>({
        access: {
            node: '@sage/xtrem-technical-data/Routing',
            bind: 'doSerialNumberPreGeneration',
        },
        parent() {
            return this.mainBlock;
        },
        title: 'Serial number service option active',
        isHidden: true,
        async onChange() {
            if (
                this.isServiceOptionsSerialNumberActive.value === true &&
                this.checkPreGenerateSerialNumbers() === true
            ) {
                await this.updatePreGeneratedSerialNumbersQuantity();
                const result = this.pregeneratedSerialNumbersPageAction();
                this.preGenerateSerialNumbers.isHidden = result.pregenerated;
                this.viewSerialNumbers.isHidden = !result.view;
                this.preGenerateAdditionalSerialNumbers.isHidden = !result.additional;
                this.type.isReadOnly = result.view;
            }
        },
    })
    isServiceOptionsSerialNumberActive: ui.fields.Checkbox;

    @ui.decorators.numericField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Pregenerated serial number quantity',
        isHidden: true,
        isReadOnly: true,
        isTransient: true,
    })
    preGeneratedSerialNumbersQuantity: ui.fields.Numeric;

    @ui.decorators.referenceField<WorkOrder, BillOfMaterial>({
        parent() {
            return this.mainBlock;
        },
        title: 'BOM',
        width: 'large',
        node: '@sage/xtrem-technical-data/BillOfMaterial',
        tunnelPage: '@sage/xtrem-technical-data/BillOfMaterial',
        lookupDialogTitle: 'Select bill of material',
        valueField: 'name',
        helperTextField: { item: { name: true } },
        isReadOnly: true,
        isHidden: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.reference({
                title: 'Item',
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
            }),
        ],
    })
    bomCode: ui.fields.Reference<BillOfMaterial>;

    @ui.decorators.referenceField<WorkOrder, UnitOfMeasure>({
        title: 'Time unit',
        bind: 'routingTimeUnit',
        isHidden: true,
        parent() {
            return this.mainBlock;
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
        ],
        lookupDialogTitle: 'Select unit of measure',
        minLookupCharacters: 1,
    })
    routingTimeUnit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.numericField<WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        isHidden: true,
    })
    baseQuantity: ui.fields.Numeric;

    @ui.decorators.nestedGridField<
        WorkOrder,
        [WorkOrderOperation, WorkOrderOperationResource, WorkOrderOperationResourceDetail]
    >({
        parent() {
            return this.operationSection;
        },
        title: 'Operations',
        isTitleHidden: true,
        bind: 'productionOperations',
        canSelect: false,
        fieldActions() {
            return [this.addNewOperationLine, this.productionOperationsDefaultDimension];
        },
        levels: [
            {
                node: '@sage/xtrem-manufacturing/WorkOrderOperation',
                childProperty: 'resources',

                orderBy: { operationNumber: 1 },
                columns: [
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperation>({
                        title: 'Operation number',
                        bind: 'operationNumber',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text<WorkOrder, WorkOrderOperation>({
                        title: 'Name',
                        bind: 'name',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.label<WorkOrder, WorkOrderOperation>({
                        title: 'Status',
                        bind: 'status',
                        optionType: '@sage/xtrem-manufacturing/OperationStatus',
                        style: (_id, rowData) => PillColor.getLabelColorByStatus('OperationStatus', rowData.status),
                    }),
                    ui.nestedFields.checkbox({ title: 'Added', bind: 'isAdded', isReadOnly: true }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperation>({
                        title: 'Planned quantity',
                        bind: 'plannedQuantity',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperation>({
                        title: 'Completed quantity',
                        bind: 'completedQuantity',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperation>({
                        title: 'Planned setup time',
                        bind: 'expectedSetupTime',
                        isReadOnly: true,
                        postfix: (_rowId, rowData) => rowData?.setupTimeUnit.symbol,
                        scale: (_rowId, rowData) => rowData?.setupTimeUnit.decimalDigits,
                    }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperation>({
                        title: 'Planned run time',
                        bind: 'expectedRunTime',
                        postfix: (_rowId, rowData) => rowData?.runTimeUnit.symbol,
                        scale: (_rowId, rowData) => rowData?.runTimeUnit.decimalDigits,
                    }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperation>({
                        title: 'Actual setup time',
                        bind: 'actualSetupTime',
                        isReadOnly: true,
                        postfix: (_rowId, rowData) => rowData?.setupTimeUnit.symbol,
                        scale: (_rowId, rowData) => rowData?.setupTimeUnit.decimalDigits,
                    }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperation>({
                        title: 'Actual run time',
                        bind: 'actualRunTime',
                        isReadOnly: true,
                        postfix: (_rowId, rowData) => rowData?.runTimeUnit.symbol,
                        scale: (_rowId, rowData) => rowData?.runTimeUnit.decimalDigits,
                    }),
                    ui.nestedFields.technical<WorkOrder, WorkOrderOperation>({
                        bind: 'startDatetime',
                    }),
                    ui.nestedFields.label<WorkOrder, WorkOrderOperation>({
                        title: 'Start time',
                        bind: '_id',
                        isHidden() {
                            return ['notScheduled', 'notManaged', ''].includes(this.schedulingStatus.value || '');
                        },
                        map(_id, row) {
                            return this.mapDatetime(row.startDatetime);
                        },
                        backgroundColor: ui.tokens.colorsTransparent,
                        borderColor: ui.tokens.colorsTransparent,
                    }),
                    ui.nestedFields.technical<WorkOrder, WorkOrderOperation>({
                        bind: 'endDatetime',
                    }),
                    ui.nestedFields.label<WorkOrder, WorkOrderOperation>({
                        title: 'End time',
                        bind: '_id',
                        isHidden() {
                            return ['notScheduled', 'notManaged', ''].includes(this.schedulingStatus.value || '');
                        },
                        map(_id, row) {
                            return this.mapDatetime(row.endDatetime);
                        },
                        backgroundColor: ui.tokens.colorsTransparent,
                        borderColor: ui.tokens.colorsTransparent,
                    }),
                    ui.nestedFields.progress<WorkOrder, WorkOrderOperation>({
                        title: 'Progress',
                        bind: 'completedTimePercentage',
                    }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperation>({
                        title: 'Planned cost',
                        bind: 'expectedBatchCost',
                        isReadOnly: true,
                        scale: 4,
                        prefix() {
                            return this.site.value?.legalCompany?.currency?.symbol || '';
                        },
                    }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperation>({
                        title: 'Actual cost',
                        bind: 'actualBatchCost',
                        isReadOnly: true,
                        scale: 4,
                        prefix() {
                            return this.site.value?.legalCompany?.currency?.symbol || '';
                        },
                    }),
                    ui.nestedFields.checkbox({ title: 'Production step', bind: 'isProductionStep', isReadOnly: true }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperation>({
                        title: 'Resource group number',
                        bind: 'resourceGroupNumber',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.technical<WorkOrder, WorkOrderOperation, CapabilityLevel>({
                        bind: 'minCapabilityLevel',
                        node: '@sage/xtrem-master-data/CapabilityLevel',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'description' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'level' }),
                        ],
                    }),
                    ui.nestedFields.technical<WorkOrder, WorkOrderOperation>({ bind: '_id' }),
                    /** temporary hack to get the value of instruction ( textStream )
                     *  Type 'FieldKey.TextArea' is not assignable to type 'GridNestedFieldTypes'.
                     *  Jira bug XT-38752
                     */
                    ui.nestedFields.image<WorkOrder, WorkOrderOperation>({ bind: 'instruction', isHidden: true }),
                    ui.nestedFields.reference<WorkOrder, WorkOrderOperation, UnitOfMeasure>({
                        title: 'Setup time unit',
                        bind: 'setupTimeUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                        valueField: 'symbol',
                        isExcludedFromMainField: true,
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.reference<WorkOrder, WorkOrderOperation, UnitOfMeasure>({
                        title: 'Run time unit',
                        bind: 'runTimeUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                        valueField: 'symbol',
                        isExcludedFromMainField: true,
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'bin',
                        title: 'Delete',
                        id: 'delete',
                        isDestructive: true,
                        isHidden(_rowId, row) {
                            return !row.isAdded || row.status !== 'pending' || this.status.value === 'closed';
                        },
                        onClick(rowId, _rowData, level) {
                            if (level !== undefined) {
                                this.productionOperations.removeRecord(rowId, level);
                            }
                        },
                    },
                    {
                        icon: 'bin',
                        title: 'Exclude',
                        id: 'exclude',
                        isHidden(_rowId, row) {
                            return row.isAdded || row.status !== 'pending' || this.status.value === 'closed';
                        },
                        onClick(rowId, data, level) {
                            if (level !== undefined) {
                                this.updateOperationRowStatus(rowId, data, level, 'excluded');
                            }
                        },
                    },
                    {
                        icon: 'tick',
                        title: 'Include',
                        id: 'include',
                        isHidden(_rowId, row) {
                            return row.status !== 'excluded' || this.status.value === 'closed';
                        },
                        onClick(rowId, data, level) {
                            if (level !== undefined) {
                                this.updateOperationRowStatus(rowId, data, level, 'pending');
                            }
                        },
                    },
                    {
                        icon: 'edit',
                        title: 'Edit',
                        id: 'edit',
                        async onClick(rowId, operation, level) {
                            if (level !== undefined) {
                                await this.addNewOperation(level, rowId, operation);
                            }
                        },
                    },
                    {
                        icon: 'add',
                        title: 'Add',
                        id: 'add',
                        isDisabled(_rowId, row) {
                            return row.status !== 'pending' || this.status.value === 'closed';
                        },
                        async onClick(_rowId, operation, level) {
                            if (level !== undefined) {
                                await this.addNewResourceGroup(level + 1, operation._id, operation);
                            }
                        },
                    },
                ],
            },
            {
                node: '@sage/xtrem-manufacturing/WorkOrderOperationResource',
                childProperty: 'resources',
                columns: [
                    ui.nestedFields.reference<WorkOrder, WorkOrderOperationResource, BaseResource>({
                        title: 'ID',
                        bind: 'resource',
                        node: '@sage/xtrem-master-data/BaseResource',
                        valueField: 'id',
                        columns: [
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                        ],
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<WorkOrder, WorkOrderOperationResource, BaseResource>({
                        title: 'Name',
                        bind: 'resource',
                        node: '@sage/xtrem-master-data/BaseResource',
                        valueField: 'name',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.label<WorkOrder, WorkOrderOperationResource>({
                        title: 'Status',
                        bind: 'status',
                        optionType: '@sage/xtrem-manufacturing/OperationStatus',
                        style: (_id, rowData) => PillColor.getLabelColorByStatus('OperationStatus', rowData.status),
                    }),
                    ui.nestedFields.checkbox({ title: 'Added', bind: 'isAdded', isReadOnly: true }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperationResource>({
                        title: 'Planned setup time',
                        bind: 'expectedSetupTime',
                        isReadOnly: true,
                        postfix: (_rowId, rowData) => rowData?.setupTimeUnit.symbol,
                        scale: (_rowId, rowData) => rowData?.setupTimeUnit.decimalDigits,
                    }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperationResource>({
                        title: 'Planned run time',
                        bind: 'expectedRunTime',
                        isReadOnly: true,
                        postfix: (_rowId, rowData) => rowData?.runTimeUnit.symbol,
                        scale: (_rowId, rowData) => rowData?.runTimeUnit.decimalDigits,
                    }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperationResource>({
                        title: 'Actual setup time',
                        bind: 'actualSetupTime',
                        isReadOnly: true,
                        postfix: (_rowId, rowData) => rowData?.setupTimeUnit.symbol,
                        scale: (_rowId, rowData) => rowData?.setupTimeUnit.decimalDigits,
                    }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperationResource>({
                        title: 'Actual run time',
                        bind: 'actualRunTime',
                        isReadOnly: true,
                        postfix: (_rowId, rowData) => rowData?.runTimeUnit.symbol,
                        scale: (_rowId, rowData) => rowData?.runTimeUnit.decimalDigits,
                    }),
                    ui.nestedFields.technical<WorkOrder, WorkOrderOperationResource>({
                        bind: 'startDatetime',
                    }),
                    ui.nestedFields.label<WorkOrder, WorkOrderOperationResource>({
                        title: 'Start time',
                        bind: '_id',
                        isHidden() {
                            return ['notScheduled', 'notManaged', ''].includes(this.schedulingStatus.value || '');
                        },
                        map(_id, row) {
                            return this.mapDatetime(row.startDatetime);
                        },
                        backgroundColor: ui.tokens.colorsTransparent,
                        borderColor: ui.tokens.colorsTransparent,
                    }),
                    ui.nestedFields.technical<WorkOrder, WorkOrderOperationResource>({
                        bind: 'endDatetime',
                    }),
                    ui.nestedFields.label<WorkOrder, WorkOrderOperationResource>({
                        title: 'End time',
                        bind: '_id',
                        isHidden() {
                            return ['notScheduled', 'notManaged', ''].includes(this.schedulingStatus.value || '');
                        },
                        map(_id, row) {
                            return this.mapDatetime(row.endDatetime);
                        },
                        backgroundColor: ui.tokens.colorsTransparent,
                        borderColor: ui.tokens.colorsTransparent,
                    }),
                    ui.nestedFields.progress<WorkOrder, WorkOrderOperationResource>({
                        title: 'Progress',
                        bind: 'completedTimePercentage',
                    }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperationResource>({
                        title: 'Planned cost',
                        bind: 'expectedCost',
                        isReadOnly: true,
                        unit() {
                            return this.site.value?.legalCompany?.currency;
                        },
                    }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperationResource>({
                        title: 'Actual cost',
                        bind: 'actualCost',
                        unit() {
                            return this.site.value?.legalCompany?.currency;
                        },
                    }),
                    ui.nestedFields.numeric<WorkOrder, WorkOrderOperationResource>({
                        title: 'Resource number',
                        bind: 'resourceNumber',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.checkbox<WorkOrder, WorkOrderOperationResource>({
                        title: 'Is resource quantity',
                        bind: 'isResourceQuantity',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.technical<WorkOrder, WorkOrderOperationResource>({ bind: '_id' }),
                    ui.nestedFields.technical<WorkOrder, WorkOrderOperationResource, BaseResource>({
                        bind: 'resource',
                        node: '@sage/xtrem-master-data/BaseResource',
                        nestedFields: [ui.nestedFields.technical({ bind: '_constructor' })],
                    }),
                    ui.nestedFields.technical({ bind: 'computedAttributes' }),
                    ui.nestedFields.technical({ bind: 'storedAttributes' }),
                    ui.nestedFields.technical({ bind: 'storedDimensions' }),
                    ui.nestedFields.reference<WorkOrder, WorkOrderOperationResource, UnitOfMeasure>({
                        title: 'Setup time unit',
                        bind: 'setupTimeUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                        valueField: 'symbol',
                        isExcludedFromMainField: true,
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.reference<WorkOrder, WorkOrderOperationResource, UnitOfMeasure>({
                        title: 'Run time unit',
                        bind: 'runTimeUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                        valueField: 'symbol',
                        isExcludedFromMainField: true,
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                            ui.nestedFields.text({ title: 'Description', bind: 'description' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'bin',
                        title: 'Delete',
                        id: 'childDelete',
                        isDestructive: true,
                        isHidden(rowId, row, level, parentID) {
                            if (row.status === 'excluded') {
                                return true;
                            }
                            const operation = this.productionOperations.getRecordValue(parentID[0], level - 1);
                            const resource = this.productionOperations.getRecordValue(rowId, level);
                            return (
                                !resource.isAdded ||
                                resource.status !== 'pending' ||
                                operation.status !== 'pending' ||
                                this.status.value === 'closed'
                            );
                        },
                        onClick(rowId, _row, level) {
                            this.productionOperations.removeRecord(rowId, level);
                        },
                    },
                    {
                        icon: 'bin',
                        title: 'Exclude',
                        id: 'childExclude',
                        isHidden(_rowId, row, level, parentID) {
                            if (row.status === 'excluded') {
                                return true;
                            }
                            const operation = this.productionOperations.getRecordValue(parentID[0], level - 1);
                            return (
                                row.isAdded ||
                                row.status !== 'pending' ||
                                operation.status !== 'pending' ||
                                this.status.value === 'closed'
                            );
                        },

                        onClick(rowId, data, level, parentID) {
                            this.updateResourceRowStatus(rowId, data, level, 'excluded', parentID);
                        },
                    },
                    {
                        icon: 'tick',
                        title: 'Include',
                        id: 'childInclude',
                        isHidden(_rowId, row, level, parentID) {
                            if (row.status === 'excluded') {
                                return true;
                            }
                            const operation = this.productionOperations.getRecordValue(parentID[0], level - 1);
                            return operation.status !== 'pending' || this.status.value === 'closed';
                        },
                        onClick(rowId, data, level, parentID) {
                            this.updateResourceRowStatus(rowId, data, level, 'pending', parentID);
                            // if all resources of an operation is excluded, operation status will become excluded when saving work order
                        },
                    },
                    {
                        icon: 'edit',
                        title: 'Edit',
                        id: 'childEdit',
                        // Only allow edit of saved records
                        isDisabled(rowId) {
                            return parseInt(rowId, 10) < 0;
                        },
                        async onClick(rowId: any, operationResourceEdit, level, parentID) {
                            if (parentID && level) {
                                const operation = this.productionOperations.getRecordValue(parentID[0], level - 1);
                                await this.addNewResourceGroup(
                                    level,
                                    parentID[0],
                                    operation as any,
                                    operationResourceEdit,
                                    rowId,
                                );
                            }
                        },
                    },
                    {
                        icon: 'edit',
                        title: 'Dimensions',
                        id: 'childDimension',
                        async onClick(_rowId, rowItem: WorkOrderOperationResource, level) {
                            if (this.inheritDimensions) {
                                rowItem.storedAttributes = this.productionItems.value[0]?.storedAttributes ?? '';
                                rowItem.storedDimensions = this.productionItems.value[0].storedDimensions ?? '';
                            }
                            await MasterDataUtils.applyPanelToLineIfChanged(
                                this.productionOperations,
                                dimensionPanelHelpers.editDisplayDimensions(
                                    this,
                                    {
                                        documentLine: rowItem,
                                    },
                                    {
                                        editable: !this.fromNotificationHistory
                                            ? ['pending', 'inProgress'].includes(rowItem.status) &&
                                              !this.inheritDimensions
                                            : this.wipSourceDocumentType === 'operationTracking',
                                    },
                                ),
                                level,
                            );
                        },
                    },
                ],
            },
            {
                node: '@sage/xtrem-manufacturing/WorkOrderOperationResource',
                columns: [
                    ui.nestedFields.reference<WorkOrder, WorkOrderOperationResourceDetail, DetailedResource>({
                        title: 'ID',
                        bind: 'resource',
                        node: '@sage/xtrem-master-data/DetailedResource',
                        valueField: 'id',
                        isReadOnly: true,
                        columns: [
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'description' }),
                            ui.nestedFields.text({ bind: 'name' }),
                        ],
                    }),
                    ui.nestedFields.reference<WorkOrder, WorkOrderOperationResourceDetail, DetailedResource>({
                        title: 'Name',
                        bind: 'resource',
                        node: '@sage/xtrem-master-data/DetailedResource',
                        valueField: 'name',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<WorkOrder, WorkOrderOperationResourceDetail, DetailedResource>({
                        title: 'Type',
                        bind: 'resource',
                        node: '@sage/xtrem-master-data/DetailedResource',
                        valueField: { resourceGroup: { type: true } },
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<WorkOrder, WorkOrderOperationResourceDetail, DetailedResource>({
                        title: 'Description',
                        bind: 'resource',
                        node: '@sage/xtrem-master-data/DetailedResource',
                        valueField: 'description',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<WorkOrder, WorkOrderOperationResourceDetail, DetailedResource>({
                        title: 'Active from',
                        bind: 'resource',
                        node: '@sage/xtrem-master-data/BaseResource',
                        valueField: 'activeFrom',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<WorkOrder, WorkOrderOperationResourceDetail, DetailedResource>({
                        title: 'Active to',
                        bind: 'resource',
                        node: '@sage/xtrem-master-data/BaseResource',
                        valueField: 'activeTo',
                        isReadOnly: true,
                    }),
                ],
            },
        ],
    })
    productionOperations: ui.fields.NestedGrid;
    // Must be ui.fields.NestedGrid<[WorkOrderOperation, WorkOrderOperationResource, WorkOrderOperationResourceDetail]>
    // When adding the right typing for the nestedGrid we fail with the following error: isAdded & status doesnot exist

    @ui.decorators.pageAction<WorkOrder>({
        icon: 'add',
        title: 'Add',
        isDisabled() {
            return this.status.value === 'completed' || this.status.value === 'closed';
        },
        async onClick() {
            await this.addNewOperation(0);
        },
    })
    addNewOperationLine: ui.PageAction;

    private async addNewOperation(level: number, id?: string, operation?: any) {
        const changedOperation = await this.$.dialog.page(
            '@sage/xtrem-manufacturing/WorkOrderOperationPanel',
            {
                id,
                operation: operation ? JSON.stringify(operation) : '',
                item: JSON.stringify(this.releasedItem.value),
                site: JSON.stringify(this.site.value),
                plannedQuantity: JSON.stringify(this.releasedItemQuantity.value),
                operations: JSON.stringify(this.productionOperations.value.map(o => o.operationNumber)),
                timeUnit: JSON.stringify(this.routingTimeUnit.value),
            },
            { resolveOnCancel: true, rightAligned: true, size: 'large' },
        );
        if (changedOperation && !isEmpty(changedOperation)) {
            if (!operation) {
                // when a new operation is added
                changedOperation.isAdded = true;
                changedOperation.status = 'pending';
                changedOperation.resourceGroupNumber = 0;
            }
            this.productionOperations.addOrUpdateRecordValue(changedOperation, level, undefined);
        }
    }

    updateOperationRowStatus(rowId: string, data: any, level: number, status: string) {
        data.status = status;
        this.productionOperations.setRecordValue(data, level);
        // FIXME: XT-3840 extend excluded/pending status from operation to its resources
        //            and if param(status)='excluded', warn + delete added resources of operation if confirmed
        // const operation = this.productionOperations.getRecordValue(rowId, level);
        // operation.resources = []
    }

    private async addNewResourceGroup(
        level: number,
        parentID: string,
        operation?: WorkOrderOperation,
        operationResource?: any,
        id?: string,
    ) {
        // Work out if there is already a resource flagged as quantity managed
        const quantityFlagged =
            this.productionOperations
                .getRecordValue(parentID, level - 1)
                ?.resources.some(
                    (woOperationResource: WorkOrderOperationResource) => woOperationResource.isResourceQuantity,
                ) ?? false;
        const currentOperationResource = id
            ? operationResource
            : dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                  operationResource as any,
                  this._defaultDimensionsAttributesArray[0],
              );

        const changedResource = await MasterDataUtils.catchPanelCrossQuitButtonAsNoop(
            this.$.dialog.page(
                '@sage/xtrem-manufacturing/WorkOrderOperationResourcePanel',
                {
                    _id: id ?? '',
                    id: id ?? '',
                    operationResource: currentOperationResource ? JSON.stringify(currentOperationResource) : '',
                    operation: JSON.stringify(operation),
                    site: JSON.stringify(this.site.value),
                    quantityFlagged: JSON.stringify(quantityFlagged),
                },
                {
                    rightAligned: true,
                    size: 'large',
                },
            ),
        );
        if (changedResource) {
            if (id && currentOperationResource?.resource?._id === changedResource.resource._id) {
                delete changedResource.resources;
            } else {
                // new resource
                if (currentOperationResource._id) {
                    // when a new resource replaces old one, delete the old one first
                    this.productionOperations.removeRecord(currentOperationResource._id, level);
                    changedResource._id = undefined;
                }

                changedResource.isAdded = true;
                changedResource.status = 'pending';
                changedResource.resourceGroupNumber = 0;
            }

            this.productionOperations.addOrUpdateRecordValue(changedResource, level, parentID);
        }
    }

    updateResourceRowStatus(rowId: string, data: any, level: number, status: string, parentID: any) {
        data.status = status;
        this.productionOperations.addOrUpdateRecordValue(data, level, parentID);
    }

    @ui.decorators.tableField<WorkOrder, WorkOrderComponent>({
        parent() {
            return this.componentSection;
        },
        title: 'Components',
        isTitleHidden: true,
        node: '@sage/xtrem-manufacturing/WorkOrderComponent',
        bind: 'productionComponents',
        canSelect: false,
        isReadOnly: true,
        orderBy: { componentNumber: 1 },
        columns: [
            ui.nestedFields.numeric({ title: 'Component number', bind: 'componentNumber', isReadOnly: true }),
            ui.nestedFields.reference<WorkOrder, WorkOrderComponent, Item>({
                node: '@sage/xtrem-master-data/Item',
                bind: 'item',
                valueField: 'id',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.checkbox({ bind: 'isStockManaged' }),
                    ui.nestedFields.reference<WorkOrder, Item, Item['stockUnit']>({
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        valueField: 'description',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.text({ title: 'Component description', bind: 'name', width: 'large' }),
            ui.nestedFields.text({
                title: 'Item',
                bind: { item: { name: true } },
                isDisabled: true,
                isMandatory: true,
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: { item: { description: true } },
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'lineStatus',
                optionType: '@sage/xtrem-manufacturing/ComponentStatus',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('ComponentStatus', rowData.lineStatus),
            }),
            ui.nestedFields.checkbox({ title: 'Added', bind: 'isAdded' }),
            ui.nestedFields.technical({ bind: 'lineType' }),
            ui.nestedFields.numeric({
                title: 'Required quantity',
                bind: 'requiredQuantity',
                scale: (_val, rowData) => rowData?.item?.stockUnit?.decimalDigits || 0,
                postfix: (_val, rowData) => rowData?.item?.stockUnit?.symbol || '',
            }),
            ui.nestedFields.numeric({
                title: 'Quantity consumed',
                bind: 'consumedQuantity',
                scale: (_val, rowData) => rowData?.item?.stockUnit?.decimalDigits || 0,
                postfix: (_val, rowData) => rowData?.item?.stockUnit?.symbol || '',
            }),
            ui.nestedFields.numeric({
                title: 'Available stock quantity',
                bind: 'availableQuantityInStockUnit',
                isHiddenOnMainField: true,
                scale: (_val, rowData) => rowData?.item?.stockUnit?.decimalDigits || 0,
                postfix: (_val, rowData) => rowData?.item?.stockUnit?.symbol || '',
            }),
            ui.nestedFields.numeric({
                title: 'Stock shortage',
                bind: 'stockShortageInStockUnit',
                isHiddenOnMainField: true,
                scale: (_val, rowData) => rowData?.item?.stockUnit?.decimalDigits || 0,
                postfix: (_val, rowData) => rowData?.item?.stockUnit?.symbol || '',
            }),
            ui.nestedFields.technical({ bind: 'quantityAllocated' }),
            ui.nestedFields.reference<WorkOrder, WorkOrderComponent, WorkOrderOperation>({
                node: '@sage/xtrem-manufacturing/WorkOrderOperation',
                title: 'Operation',
                bind: 'operation',
                valueField: 'operationNumber',
                columns: [
                    ui.nestedFields.text({ title: 'Operation number', bind: 'operationNumber' }),
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical<WorkOrder, WorkOrderOperation, WorkOrderOperation['workOrder']>({
                        bind: 'workOrder',
                        node: '@sage/xtrem-manufacturing/WorkOrder',
                        nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
                    }),
                ],
            }),
            ui.nestedFields.numeric({ title: 'Scrap factor %', bind: 'scrapFactor', scale: 4 }),
            ui.nestedFields.progress({ title: 'Progress', bind: 'completedQuantityPercentage' }),
            ui.nestedFields.numeric({
                title: 'Planned cost',
                bind: 'plannedCost',
                scale: 4,
                prefix() {
                    return this.site.value?.legalCompany?.currency?.symbol || '';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Actual cost',
                bind: 'actualCost',
                scale: 4,
                prefix() {
                    return this.site.value?.legalCompany?.currency?.symbol || '';
                },
            }),
            ui.nestedFields.technical({ bind: 'linkQuantity' }),
            ui.nestedFields.technical<WorkOrder, WorkOrderComponent, UnitOfMeasure>({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isFixedLinkQuantity' }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            /** temporary hack to get the value of instruction ( textStream )
             *  Type 'FieldKey.TextArea' is not assignable to type 'GridNestedFieldTypes'.
             *  Jira bug XT-38752
             */
            ui.nestedFields.image({ isHidden: true, bind: 'instruction' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.label({
                title: 'Allocation status',
                bind: 'allocationStatus',
                optionType: '@sage/xtrem-stock-data/StockAllocationStatus',
                isHidden() {
                    return this.status.value === 'closed';
                },
                map: (_value, rowData) =>
                    ['excluded', 'completed', 'included'].includes(rowData.lineStatus)
                        ? undefined
                        : rowData.allocationStatus,
                style: (_id, rowData) =>
                    rowData.allocationStatus
                        ? PillColorStock.getLabelColorByStatus('OperationStatus', rowData.allocationStatus)
                        : PillColorStock.getTransparentStatusStyle(),
            }),
            ui.nestedFields.label({
                title: 'Allocation request status',
                bind: 'allocationRequestStatus',
                optionType: '@sage/xtrem-stock-data/AllocationRequestStatus',
                isHidden() {
                    return ['noRequest', 'completed'].includes(this.allocationRequestStatus.value || '');
                },
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus('AllocationRequestStatus', rowData.allocationRequestStatus),
            }),
            ui.nestedFields.technical({ bind: 'stockOnHand' }),
            ui.nestedFields.technical({ bind: 'availableQuantityInStockUnit' }),
            ui.nestedFields.technical({ bind: 'stockShortageStatus' }),
            ui.nestedFields.technical({ bind: 'stockShortageInStockUnit' }),
            ui.nestedFields.technical({ bind: 'remainingQuantityToAllocate' }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Edit',
                id: 'edit',
                onClick(rowId: string, rowData: ui.PartialCollectionValue<WorkOrderComponent>) {
                    return this.addEditComponent(rowId, rowData);
                },
            },
            {
                icon: 'cross_circle',
                title: 'Exclude',
                id: 'exclude',
                isHidden(_rowId, row: ui.PartialCollectionValue<WorkOrderComponent>) {
                    if (row.lineStatus === 'excluded') {
                        return true;
                    }
                    if (row.lineType !== 'text' && (row.isAdded || row.lineStatus !== 'pending')) {
                        return true;
                    }
                    if (this.status.value === 'closed') {
                        return true;
                    }
                    if (['allocated', 'partiallyAllocated'].includes(row.allocationStatus)) {
                        return true;
                    }
                    if (row.allocationRequestStatus === 'inProgress') {
                        return true;
                    }
                    return false;
                },
                onClick(rowId, data: ui.PartialCollectionValue<WorkOrderComponent>) {
                    this.updateComponentRowStatus(rowId, data, 'excluded');
                },
            },
            {
                icon: 'tick_circle',
                title: 'Include',
                id: 'tick',
                isHidden(_rowId, row: ui.PartialCollectionValue<WorkOrderComponent>) {
                    return row.lineStatus !== 'excluded' || this.status.value === 'closed';
                },
                onClick(rowId: string, data: ui.PartialCollectionValue<WorkOrderComponent>) {
                    const lineType = this.productionComponents?.getRecordValue(rowId)?.lineType;
                    if (lineType === 'text') {
                        this.updateComponentRowStatus(rowId, data, 'completed');
                        return;
                    }
                    this.updateComponentRowStatus(rowId, data, 'pending');
                },
            },
            ui.menuSeparator(),
            {
                icon: 'three_boxes',
                title: 'Allocate stock',
                id: 'allocateStock',
                async onClick(rowId: string, rowItem) {
                    const allocationChanged = await this.allocateStock(rowId, rowItem);
                    if (allocationChanged) {
                        const result = extractEdges(
                            await this.$.graph
                                .node('@sage/xtrem-manufacturing/WorkOrderComponent')
                                .query(
                                    ui.queryUtils.edgesSelector(
                                        { _id: true, allocationStatus: true },
                                        {
                                            filter: {
                                                _id: { _eq: rowItem._id },
                                                workOrder: { _id: { _eq: this._id.value } },
                                                componentNumber: { _eq: rowItem.componentNumber },
                                            },
                                        },
                                    ),
                                )
                                .execute(),
                        );
                        rowItem.allocationStatus = result[0].allocationStatus;
                        if (rowItem.allocationRequestStatus !== 'inProgress') {
                            rowItem.allocationRequestStatus = 'noRequest';
                        }
                        await this.productionComponents.refresh();

                        await this.$.router.refresh();

                        if (
                            this.isServiceOptionsSerialNumberActive.value === true &&
                            this.checkPreGenerateSerialNumbers() === true
                        ) {
                            await this.pregeneratedBusinessRules();
                        }
                    }
                },
                isHidden(_rowId, rowItem: ui.PartialCollectionValue<WorkOrderComponent>) {
                    return (
                        rowItem &&
                        (!(rowItem.item && rowItem.requiredQuantity && rowItem.item.isStockManaged) ||
                            !rowItem.lineStatus ||
                            !['pending', 'inProgress'].includes(rowItem.lineStatus) ||
                            rowItem.allocationRequestStatus === 'inProgress')
                    );
                },
            },
            {
                icon: 'three_boxes',
                title: 'Projected stock',
                id: 'projectedStock',
                async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<WorkOrderComponent>) {
                    await this.$.dialog.page(
                        '@sage/xtrem-stock-data/ProjectedStockDialog',
                        {
                            item: JSON.stringify(rowItem.item),
                            site: JSON.stringify(this.site.value),
                        },
                        {
                            rightAligned: false,
                            size: 'extra-large',
                            resolveOnCancel: true,
                        },
                    );
                },
                isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<WorkOrderComponent>) {
                    return !rowItem?.item?.isStockManaged;
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                id: 'dimensions',
                onClick(_rowId, rowItem: ui.PartialCollectionValue<WorkOrderComponent>) {
                    if (this.inheritDimensions) {
                        rowItem.storedAttributes = this.productionItems.value[0].storedAttributes;
                        rowItem.storedDimensions = this.productionItems.value[0].storedDimensions;
                    }
                    return MasterDataUtils.applyPanelToLineIfChanged(
                        this.productionComponents,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            { documentLine: rowItem as any },
                            {
                                editable: !this.fromNotificationHistory
                                    ? ['pending', 'inProgress'].includes(rowItem.lineStatus || '') &&
                                      !this.inheritDimensions
                                    : this.wipSourceDocumentType === 'materialTracking',
                            },
                        ),
                    );
                },
                isDisabled(_rowId, rowItem: ui.PartialCollectionValue<WorkOrderComponent>) {
                    return rowItem.lineType === 'text';
                },
            },

            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                id: 'delete',
                isDestructive: true,
                isHidden(_rowId, row: ui.PartialCollectionValue<WorkOrderComponent>) {
                    if (
                        (row.lineType !== 'text' && (!row.isAdded || row.lineStatus !== 'pending')) ||
                        this.status.value === 'closed' ||
                        row.allocationRequestStatus === 'inProgress'
                    ) {
                        return true;
                    }
                    return false;
                },
                onClick(rowId) {
                    this.productionComponents.removeRecord(rowId);
                },
            },
        ],
        fieldActions() {
            return [this.addComponentLine, this.productionComponentsDefaultDimension];
        },
    })
    productionComponents: ui.fields.Table<WorkOrderComponent>;

    @ui.decorators.tableField<WorkOrder, WorkOrderTrackingView>({
        title: 'Trackings',
        isTransient: true,
        canSelect: false,
        isReadOnly: true,
        pageSize: 10,
        parent() {
            return this.trackingsSection;
        },
        orderBy: { number: +1, date: +1 },
        columns: [
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.dropdownList({ title: 'Document type', bind: 'type' }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'number',
                onClick(_rowId, rowItem) {
                    let page = '';

                    switch (rowItem.type) {
                        case 'Production tracking':
                            page = `@sage/xtrem-manufacturing/ProductionTrackingInquiry`;
                            break;
                        case 'Time tracking':
                            page = `@sage/xtrem-manufacturing/TimeTrackingInquiry`;
                            break;
                        case 'Material tracking':
                        default:
                            page = `@sage/xtrem-manufacturing/MaterialTrackingInquiry`;
                            break;
                    }
                    return this.$.dialog.page(page, { _id: rowItem.id }, { fullScreen: true, resolveOnCancel: true });
                },
            }),
            ui.nestedFields.date<WorkOrder, WorkOrderTrackingView>({ title: 'Date', bind: 'date' }),
            ui.nestedFields.reference({
                title: 'Site',
                node: '@sage/xtrem-system/Site',
                bind: 'site',
                valueField: 'id',
            }),
            ui.nestedFields.label({
                title: 'Stock status',
                bind: 'status',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowData) =>
                    rowData.status
                        ? PillColorStock.getLabelColorByStatus('StockDocumentTransactionStatus', rowData.status)
                        : PillColorStock.getTransparentStatusStyle(),
            }),
        ],
    })
    trackingView: ui.fields.Table<WorkOrderTrackingView>;

    @ui.decorators.richTextField<WorkOrder>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Notes',
        capabilities: validCapabilities,
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    note: ui.fields.RichText;

    @ui.decorators.tableField<WorkOrder, WorkOrderReleasedItem>({
        parent() {
            return this.generalSection;
        },
        title: 'Items',
        isTitleHidden: true,
        node: '@sage/xtrem-manufacturing/WorkOrderReleasedItem',
        bind: 'productionItems',
        isReadOnly: true,
        isHidden: true,
        columns: [
            ui.nestedFields.reference<WorkOrder, WorkOrderReleasedItem, Item>({
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                title: 'Item',
                bind: 'releasedItem',
                valueField: 'id',
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name', width: 'large', isReadOnly: true }),
                    ui.nestedFields.dropdownList({
                        bind: 'serialNumberManagement',
                        optionType: '@sage/xtrem-master-data/SerialNumberManagement',
                    }),
                ],
            }),
            ui.nestedFields.reference<WorkOrder, WorkOrderReleasedItem, UnitOfMeasure>({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                title: 'Unit',
                bind: 'stockUnit',
                valueField: 'symbol',
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name', width: 'large', isReadOnly: true }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Released quantity',
                bind: 'releasedQuantity',
                scale: (_val, rowData) => rowData?.stockUnit?.decimalDigits || 0,
                postfix: (_val, rowData) => rowData?.stockUnit?.symbol || '',
            }),
            ui.nestedFields.numeric({
                title: 'Completed quantity',
                bind: 'totalActualQuantity',
                scale: (_val, rowData) => rowData?.stockUnit?.decimalDigits || 0,
                postfix: (_val, rowData) => rowData?.stockUnit?.symbol || '',
            }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({ bind: 'lineStatus' }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-master-data/ItemSite',
                bind: 'itemSite',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'prodLeadTime' }),
                    ui.nestedFields.technical({ bind: 'isOrderToOrder' }),
                    ui.nestedFields.technical({ bind: 'preferredProcess' }),
                ],
            }),
        ],
    })
    productionItems: ui.fields.Table<WorkOrderReleasedItem>;

    @ui.decorators.pageAction<WorkOrder>({
        title: 'Create',
        icon: 'add',
        buttonType: 'primary',
        async onClick() {
            const workOrderPanelReturn: OperationResultType<WorkOrder$Operations['mutations']['createWorkOrder']> =
                await this.$.dialog.page(
                    '@sage/xtrem-manufacturing/WorkOrderPanel',
                    {},
                    {
                        rightAligned: true,
                        size: 'large',
                        resolveOnCancel: true,
                    },
                );
            if (workOrderPanelReturn && !isEmpty(workOrderPanelReturn)) {
                await this.$.router.selectRecord(workOrderPanelReturn._id);
                await this.$.refreshNavigationPanel();
                await this.$.router.refresh();
                if (
                    this.isServiceOptionsSerialNumberActive.value === true &&
                    this.checkPreGenerateSerialNumbers() === true
                ) {
                    await this.pregeneratedBusinessRules();
                }
            }
        },
    })
    createWorkOrder: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        access: {
            bind: 'scheduleWorkOrder',
        },
        title: 'Schedule',
        isHidden() {
            return this.schedulingStatus.value === 'notManaged' || this.status.value === 'closed';
        },
        isDisabled() {
            return this.$.isDirty || this.schedulingStatus.value === 'inProgress';
        },
        async onClick() {
            this.$.loader.isHidden = false;
            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrder')
                .mutations.scheduleWorkOrder({ _id: true }, { workOrder: this._id.value })
                .execute();
            this.$.loader.isHidden = true;
            await this.$.router.refresh();
        },
    })
    scheduleWorkOrder: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        title: 'Close order',
        isHidden() {
            return this.status.value === 'closed' || !!this.closingDate.value;
        },
        isDisabled() {
            return (
                this.status.value === 'pending' ||
                this.$.isDirty ||
                ['allocated', 'partiallyAllocated'].includes(this.allocationStatus.value ?? '')
            );
        },
        async onClick() {
            const isAssignmentChecked = await isWorkOrderAssignmentCheckedOnWorkOrderClose(this);
            const isStatusCompleted = this.status.value === 'completed';
            const isRecordIdValid = !!this.$.recordId;

            if (
                !isRecordIdValid ||
                !isAssignmentChecked ||
                (!isStatusCompleted && !(await incompleteWorkOrderCloseDialog(this)))
            )
                return;
            const closingDate = await this.$.dialog.page(
                '@sage/xtrem-manufacturing/WorkOrderClosePanel',
                { _id: this.$.recordId },
                { resolveOnCancel: true },
            );

            if (!this.validateAndSetClosingDate(closingDate)) return;

            this.$.loader.isHidden = false;
            try {
                if (
                    await this.$.graph
                        .node('@sage/xtrem-manufacturing/WorkOrder')
                        .asyncOperations.closeWorkOrder.runToCompletion(true, {
                            filter: JSON.stringify({ _id: { _eq: this._id.value } }),
                            closingDate,
                        })
                        .execute()
                ) {
                    this.status.value = 'closed';
                }
            } finally {
                this.$.loader.isHidden = true;
                await this.$.router.refresh();
                await this.$.refreshNavigationPanel();
            }
        },
    })
    closeWorkOrder: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        title: 'Save',
        access: { bind: '$update' },
        async onClick() {
            const isDialog = this.$.isInDialog;
            await this.$standardSaveAction.execute(true);
            if (isDialog) return;
            this.hasBillOfMaterial.isDisabled = this.disableUsesBOMorRouting(
                this.bomCode.value !== null,
                this.productionComponents.value.length,
            );
            this.hasRouting.isDisabled = this.disableUsesBOMorRouting(
                this.routingCode.value !== null,
                this.productionOperations.value.length,
            );
            if (
                this.isServiceOptionsSerialNumberActive.value === true &&
                this.checkPreGenerateSerialNumbers() === true
            ) {
                await this.pregeneratedBusinessRules();
            }
            this.costs.value = [];
            this.updateCostValues();
            this.inheritDimensions = false;
            this.$.setPageClean();
        },
    })
    saveWorkOrder: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        icon: 'add',
        title: 'Add',
        isDisabled() {
            return this.status.value === 'completed' || this.status.value === 'closed';
        },
        async onClick() {
            await this.addEditComponent('', null);
        },
    })
    addComponentLine: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        title: 'Dimensions',
        isDisabled() {
            return !this.$.recordId;
        },
        async onClick() {
            await MasterDataUtils.applyPanelToLineIfChanged(
                this.productionItems,
                dimensionPanelHelpers.editDimensionsToInherit(
                    this,
                    {
                        documentLine: dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(
                            this.productionItems.value[0],
                        ) as any,
                    },
                    {
                        editable: !this.fromNotificationHistory
                            ? ['pending', 'inProgress'].includes(this.status.value || '')
                            : ['productionTracking', 'workOrderClose'].includes(this.wipSourceDocumentType as string),
                    },
                ),
            );
        },
    })
    editWorkOrderReleasedItemDimensions: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        title: 'Pregenerate serial numbers',
        isHidden: true,
        isDisabled() {
            return !this.$.recordId;
        },
        access: {
            node: '@sage/xtrem-technical-data/Routing',
            bind: 'doSerialNumberPreGeneration',
        },
        async onError(error) {
            this.$.loader.isHidden = true;
            await this.$.dialog.message(
                'info',
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order_pregenerate_serial_number',
                    'Pregenerate serial numbers',
                ),
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__pages__work_order_pregenerate_serial_number_error',
                    'Pregenerate serial numbers: {{error}}',
                    { error },
                ),
            );
        },
        async onClick() {
            const qtyToCreate = await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrder')
                .mutations.preGenerateSerialNumbers(true, {
                    releasedItem: this.releasedItem.value?._id,
                    releasedQuantity: this.releasedItemQuantity.value,
                    site: this.site.value?._id,
                    productionItem: this.productionItems.value[0]._id,
                    checkOnly: true,
                })
                .execute();

            const options: ui.dialogs.DialogOptions = {
                acceptButton: {
                    text: ui.localize(
                        '@sage/xtrem-manufacturing/pages_work_order_pregenerate_serial_numbers__confirmationButton',
                        'Confirm pregeneration',
                    ),
                },
                cancelButton: {
                    text: 'Cancel',
                },
            };
            if (
                await this.$.dialog
                    .confirmation(
                        'warn',
                        ui.localize(
                            '@sage/xtrem-manufacturing/pages_work_order_pregenerate_serial_numbers__confirmation',
                            'Confirm pregeneration of serial numbers',
                        ),
                        this.releasedItemQuantity.value && this.releasedItemQuantity.value > 1
                            ? ui.localize(
                                  '@sage/xtrem-manufacturing/pages_work_order_pregenerate_serial_numbers__message',
                                  'You are about to generate {{qty}} serial numbers. Confirm generation?',
                                  { qty: qtyToCreate },
                              )
                            : ui.localize(
                                  '@sage/xtrem-manufacturing/pages_work_order_pregenerate_serial_number__message',
                                  'You are about to generate {{qty}} serial number. Confirm generation?',
                                  { qty: qtyToCreate },
                              ),
                        options,
                    )
                    .then(() => true)
                    .catch(() => false)
            ) {
                this.$.loader.display();
                const message = await this.$.graph
                    .node('@sage/xtrem-manufacturing/WorkOrder')
                    .mutations.preGenerateSerialNumbers(true, {
                        releasedItem: this.releasedItem.value?._id,
                        releasedQuantity: this.releasedItemQuantity.value,
                        site: this.site.value?._id,
                        productionItem: this.productionItems.value[0]._id,
                        checkOnly: false,
                    })
                    .execute();
                if (message.length) {
                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-manufacturing/pre_generate_serial_number_confirmation',
                            `${message} serial numbers generated`,
                            { type: 'success' },
                        ),
                    );
                }
                this.$.loader.hide();

                if (
                    this.isServiceOptionsSerialNumberActive.value === true &&
                    this.checkPreGenerateSerialNumbers() === true
                ) {
                    await this.pregeneratedBusinessRules();
                }
            }
        },
    })
    preGenerateSerialNumbers: ui.PageAction;

    @ui.decorators.section<WorkOrder>({
        access: {
            node: '@sage/xtrem-technical-data/Routing',
            bind: 'doSerialNumberPreGeneration',
        },
        title: 'Pregenerate additional serial numbers',
        isTitleHidden: true,
        isHidden: true,
    })
    preGenerateAdditionalSerialNumbersSection: ui.containers.Section;

    @ui.decorators.block<WorkOrder>({
        access: {
            node: '@sage/xtrem-technical-data/Routing',
            bind: 'doSerialNumberPreGeneration',
        },
        parent() {
            return this.preGenerateAdditionalSerialNumbersSection;
        },
        title: 'Additional serial numbers',
        isTitleHidden: true,
    })
    preGenerateAdditionalSerialNumbersBlock: ui.containers.Block;

    @ui.decorators.numericField<WorkOrder>({
        access: {
            node: '@sage/xtrem-technical-data/Routing',
            bind: 'doSerialNumberPreGeneration',
        },
        parent() {
            return this.preGenerateAdditionalSerialNumbersBlock;
        },
        title: 'Enter the quantity of additional serial numbers to pre-generate',
        width: 'small',
        isTransient: true,
        onChange() {
            this.okButton.isDisabled = !!(
                this.additionalSerialNumberQuantity.value === null ||
                this.additionalSerialNumberQuantity.value === undefined ||
                this.additionalSerialNumberQuantity.value <= 0
            );
        },
    })
    additionalSerialNumberQuantity: ui.fields.Numeric;

    @ui.decorators.separatorField<WorkOrder>({
        parent() {
            return this.preGenerateAdditionalSerialNumbersBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorFromAdditionalSerialNumberQuantity: ui.fields.Separator;

    @ui.decorators.buttonField<WorkOrder>({
        isTransient: true,
        parent() {
            return this.preGenerateAdditionalSerialNumbersBlock;
        },
        map: () => ui.localize('@sage/xtrem-manufacturing/pages__work_order__cancel_button_text', 'Cancel'),
        onClick() {
            this.$.setPageClean();
            this.$.router.goTo(`@sage/xtrem-manufacturing/WorkOrder`, { _id: this._id.value });
        },
    })
    cancelButton: ui.fields.Button;

    @ui.decorators.buttonField<WorkOrder>({
        isTransient: true,
        parent() {
            return this.preGenerateAdditionalSerialNumbersBlock;
        },
        map: () => ui.localize('@sage/xtrem-manufacturing/pages__work_order__generate_button_text', 'Generate'),
        isDisabled() {
            return !!(
                this.additionalSerialNumberQuantity.value === null ||
                this.additionalSerialNumberQuantity.value === undefined ||
                this.additionalSerialNumberQuantity.value <= 0
            );
        },
        async onClick() {
            const sumSerialNumberQty = (additional: number, pregenerated: number): number =>
                +additional + +pregenerated;
            const result = sumSerialNumberQty(
                this.additionalSerialNumberQuantity.value ? this.additionalSerialNumberQuantity.value : 0,
                this.preGeneratedSerialNumbersQuantity.value ? this.preGeneratedSerialNumbersQuantity.value : 0,
            );

            const message = await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrder')
                .mutations.preGenerateSerialNumbers(true, {
                    releasedItem: this.releasedItem.value?._id,
                    releasedQuantity: result,
                    site: this.site.value?._id,
                    productionItem: this.productionItems.value[0]._id,
                    checkOnly: false,
                })
                .execute();

            if (message.length) {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-manufacturing/pre_generate_serial_number_confirmation',
                        `${message} serial numbers generated`,
                        { type: 'success' },
                    ),
                );
            }
            this.$.setPageClean();
            this.$.router.goTo(`@sage/xtrem-manufacturing/WorkOrder`, { _id: this._id.value });
        },
    })
    okButton: ui.fields.Button;

    @ui.decorators.pageAction<WorkOrder>({
        access: {
            node: '@sage/xtrem-technical-data/Routing',
            bind: 'doSerialNumberPreGeneration',
        },
        title: 'Pregenerate additional serial numbers',
        isHidden: true,
        isDisabled() {
            return !this.$.recordId;
        },
        async onError(error) {
            await this.$.dialog.message(
                'info',
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order_pregenerate_additional_serial_number',
                    'Pregenerate additional serial numbers',
                ),
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__pages__work_order_pregenerate_additional_serial_number_error',
                    'Pregenerate serial numbers: {{error}}',
                    { error },
                ),
            );
        },
        async onClick() {
            this.preGenerateAdditionalSerialNumbersSection.isHidden = false;
            await MasterDataUtils.confirmDialogToBoolean(
                this.$.dialog.custom('info', this.preGenerateAdditionalSerialNumbersSection, {
                    resolveOnCancel: true,
                    cancelButton: { isHidden: true },
                    acceptButton: { isHidden: true },
                }),
            );

            this.preGenerateAdditionalSerialNumbersSection.isHidden = true;

            if (
                this.isServiceOptionsSerialNumberActive.value === true &&
                this.checkPreGenerateSerialNumbers() === true
            ) {
                await this.updatePreGeneratedSerialNumbersQuantity();
                this.preGenerateSerialNumbers.isHidden = this.pregeneratedSerialNumbersPageAction().pregenerated;
                this.viewSerialNumbers.isHidden = !this.pregeneratedSerialNumbersPageAction().view;
                this.preGenerateAdditionalSerialNumbers.isHidden =
                    !this.pregeneratedSerialNumbersPageAction().additional;
            }
            this.$.setPageClean();
            this.$.loader.hide();
        },
    })
    preGenerateAdditionalSerialNumbers: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        access: {
            node: '@sage/xtrem-technical-data/Routing',
            bind: 'doSerialNumberPreGeneration',
        },
        title: 'View serial numbers',
        isHidden() {
            return true;
        },
        isDisabled() {
            return !this.$.recordId;
        },
        async onClick() {
            await this.$.dialog.page(
                '@sage/xtrem-manufacturing/WorkOrderSerialNumber',
                { workOrderId: this._id.value },
                { rightAligned: true, size: 'large' },
            );
        },
    })
    viewSerialNumbers: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        icon: 'arrow_right',
        title: 'Set default dimensions',
        isDisabled() {
            return !dimensionPanelHelpers.isDefaultDimensionActionActive(this.$.recordId, this.$.isDirty);
        },
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'manufacturingDirect',
                companyId: Number(this.site?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributesArray[0] =
                await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                    this,
                    this.productionOperations,
                    this._defaultDimensionsAttributesArray[0],
                    () => true,
                    defaultedFromItem,
                );
        },
    })
    productionOperationsDefaultDimension: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        icon: 'arrow_right',
        title: 'Set default dimensions',
        isDisabled() {
            return !dimensionPanelHelpers.isDefaultDimensionActionActive(this.$.recordId, this.$.isDirty);
        },
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'manufacturingDirect',
                companyId: Number(this.site?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributesArray[1] =
                await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                    this,
                    this.productionComponents,
                    this._defaultDimensionsAttributesArray[1],
                    () => true,
                    defaultedFromItem,
                );
        },
    })
    productionComponentsDefaultDimension: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        title: 'Job traveler',
        icon: 'print',
        isDisabled() {
            return this.$.isDirty;
        },
        async onClick() {
            await this.$.dialog.page(
                '@sage/xtrem-reporting/PrintDocument',
                { reportName: 'sageJobTraveler', order: this._id.value || '' },
                { size: 'extra-large' },
            );
        },
    })
    print: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        title: 'Pick list',
        icon: 'print',
        isDisabled() {
            return (
                this.$.isDirty ||
                !['pending', 'inProgress'].includes(this.status.value ?? '') ||
                this.type.value !== 'firm'
            );
        },
        async onClick() {
            await this.$.dialog.page(
                '@sage/xtrem-reporting/PrintDocument',
                { reportName: 'workOrderPickList', order: this._id.value ?? '' },
                { size: 'extra-large' },
            );
        },
    })
    printPickList: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        title: 'Assigned to',
        icon: 'file_generic',
        async onClick() {
            await this.assignOrderDetailPanel();
        },
    })
    orderToOrderIcon: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        title: 'Delete',
        icon: 'bin',
        isDestructive: true,
        onError() {
            this.$.loader.isHidden = true;
        },
        async onClick() {
            const isAssignmentChecked = await isWorkOrderAssignmentCheckedOnWorkOrderDelete(this);
            if (isAssignmentChecked) {
                await this.$standardDeleteAction.execute(true);
            }
        },
    })
    deleteWorkOrder: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        title: 'Allocate stock',
        isHidden() {
            return (
                this.allocationRequestStatus.value === 'inProgress' ||
                !['pending', 'inProgress'].includes(this.status.value || '') ||
                !['notAllocated', 'partiallyAllocated'].includes(this.allocationStatus.value || '')
            );
        },
        isDisabled() {
            return this.$.isDirty;
        },
        onError(error: string | (Error & { errors: Array<any> })) {
            this.$.loader.isHidden = true;
            return MasterDataUtils.formatError(this, error);
        },
        async onClick() {
            this.$.loader.isHidden = false;
            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrder')
                .mutations.requestAutoAllocation(true, {
                    data: {
                        document: this._id.value,
                        requestType: 'allocation',
                    },
                })
                .execute();
            this.$.loader.isHidden = true;

            await this.$.dialog.message(
                'info',
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order_auto_allocate_message_title',
                    'Allocation request submitted',
                ),
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order_auto_allocate_message',
                    'The allocation request was submitted.',
                ),
            );

            await this.$.router.refresh();
            await this.$.refreshNavigationPanel();
        },
    })
    allocate: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        title: 'Deallocate stock',
        isHidden() {
            return (
                this.allocationRequestStatus.value === 'inProgress' ||
                !['pending', 'inProgress'].includes(this.status.value || '') ||
                !['allocated', 'partiallyAllocated'].includes(this.allocationStatus.value || '')
            );
        },
        isDisabled() {
            return this.$.isDirty;
        },
        onError(error: string | (Error & { errors: Array<any> })) {
            this.$.loader.isHidden = true;
            return MasterDataUtils.formatError(this, error);
        },
        async onClick() {
            this.$.loader.isHidden = false;
            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrder')
                .mutations.requestAutoAllocation(true, {
                    data: {
                        document: this._id.value,
                        requestType: 'deallocation',
                    },
                })
                .execute();
            this.$.loader.isHidden = true;

            await this.$.dialog.message(
                'info',
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order_auto_deallocate_message_title',
                    'Deallocation request submitted',
                ),
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order_auto_deallocate_message',
                    'The deallocation request was submitted.',
                ),
            );

            await this.$.router.refresh();
            await this.$.refreshNavigationPanel();
        },
    })
    deallocate: ui.PageAction;

    @ui.decorators.pageAction<WorkOrder>({
        title: 'Repost',
        isHidden() {
            return !this.fromNotificationHistory;
        },
        isDisabled() {
            return !this.$.isDirty;
        },
        async onClick() {
            await this.trackingRepost();
            this.$.finish();
        },
    })
    repost: ui.PageAction;

    async trackingRepost() {
        const components = this.productionComponents.value
            .filter(line => Number(line._id) > 0)
            .map(line => ({
                baseDocumentLineSysId: line._id,
                storedAttributes: line.storedAttributes,
                storedDimensions: line.storedDimensions,
            }));

        const releasedItems = this.productionItems.value
            .filter(item => Number(item._id) > 0)
            .map(item => ({
                baseDocumentLineSysId: item._id,
                storedAttributes: item.storedAttributes,
                storedDimensions: item.storedDimensions,
            }));

        const operationResources: {
            baseDocumentLineSysId: number;
            workOrderOperationSysId: number;
            resourceSysId: number;
            storedAttributes: string;
            storedDimensions: string;
        }[] = [];
        await asyncArray(this.productionOperations.value).forEach(
            async (operation: ui.PartialNodeWithId<WorkOrderOperation>) => {
                operation.resources = await this.productionOperations.loadChildRecords({
                    childLevel: 1,
                    parentRecordId: operation._id,
                    openLevel: false,
                });
                if (operation.resources) {
                    await asyncArray(operation.resources).forEach(
                        (resource: ui.PartialCollectionValue<WorkOrderOperationResource>) => {
                            operationResources.push({
                                baseDocumentLineSysId: Number(resource._id) || 0,
                                workOrderOperationSysId: Number(operation._id) || 0,
                                resourceSysId: Number(resource.resource?._id) || 0,
                                storedAttributes: resource.storedAttributes || '{}',
                                storedDimensions: resource.storedDimensions || '{}',
                            });
                        },
                    );
                }
            },
        );

        this.$.loader.isHidden = false;

        const postResult = await this.$.graph
            .node('@sage/xtrem-manufacturing/WorkOrder')
            .mutations.repost(
                { wasSuccessful: true, message: true },
                {
                    workOrder: this.$.recordId || '',
                    financeTransactionSysId: this.financeTransactionSysId.toString(),
                    components,
                    releasedItems,
                    operationResources,
                },
            )
            .execute();

        this.$.loader.isHidden = true;

        if (!postResult.wasSuccessful) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order__repost_errors',
                    'Errors while reposting: {{errors}}',
                    { errors: postResult.message },
                ),
                { type: 'error', timeout: 20000 },
            );
        } else {
            this.$.showToast(postResult.message, { type: 'success' });
        }

        return postResult;
    }

    updateReleasedItem(workOrderId: string) {
        if (workOrderId) {
            const productionItem = this.productionItems.value.at(0);

            this.releasedItemId = productionItem ? productionItem._id : '0';
            this.releasedItemQuantity.scale = MasterDataUtils.getScaleValue(
                2,
                productionItem?.stockUnit?.decimalDigits,
            );
            this.releasedItemQuantity.postfix = productionItem?.stockUnit?.symbol;
            this.totalActualQuantity.value = productionItem?.totalActualQuantity
                ? +productionItem.totalActualQuantity
                : 0;
            this.totalActualQuantity.scale = MasterDataUtils.getScaleValue(2, productionItem?.stockUnit?.decimalDigits);
            this.totalActualQuantity.postfix = productionItem?.stockUnit?.symbol;
        } else {
            this.releasedItemId = '0';
            this.releasedItemQuantity.value = 0;
            this.releasedItemQuantity.postfix = '';
            this.totalActualQuantity.value = 0;
        }
    }

    getSerializedValues() {
        let pageValuesObj = this.$.values;
        if (pageValuesObj.productionOperations) {
            WorkOrder.getProductionOperationsPayload(pageValuesObj.productionOperations);
        }
        if (pageValuesObj.productionComponents) {
            this.getProductionComponentsPayload(pageValuesObj.productionComponents);
        }
        // if the user changed the dimensions of the released item and clicked on "Apply to operations and components"
        // we indicate this to the node (transient property doInheritDimensions) so that in the saveEnd the inheritance
        // to operation resources and components can be done
        if (this.inheritDimensions) {
            pageValuesObj = {
                ...pageValuesObj,
                doInheritDimensions: true,
            };
        }
        delete pageValuesObj.plannedProcessCost;
        delete pageValuesObj.plannedMaterialCost;
        delete pageValuesObj.actualMaterialCost;
        delete pageValuesObj.actualProcessCost;
        delete pageValuesObj.processCompletionPercentage;
        delete pageValuesObj.materialCompletionPercentage;
        delete pageValuesObj.creationDate;
        delete pageValuesObj.schedulingStatus;
        delete pageValuesObj.timeZone;
        return pageValuesObj;
    }

    updateCostValues(): void {
        this.costs.value = this.costs.value.concat([
            {
                _id: this.costs.generateRecordId(),
                costTitle: ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order__costs____row__title__total',
                    'Total',
                ),
                actualCost: this.sumWorkOrderCosts(
                    this.actualProcessCost.value ?? 0,
                    this.actualMaterialCost.value ?? 0,
                    this.actualOverheadCost.value ?? 0,
                ),
                cost: this.sumWorkOrderCosts(
                    this.plannedProcessCost.value ?? 0,
                    this.plannedMaterialCost.value ?? 0,
                    this.plannedOverheadCost.value ?? 0,
                ),
            },
            // XT-38417: deactivate (temporarily) indirect cost on item-sites
            // overhead of operations are included in "process cost"
            // {
            //     _id: this.costs.generateRecordId(),
            //     costTitle: ui.localize(
            //        '@sage/xtrem-manufacturing/pages__work_order__costs____row__title__overhead_cost',
            //        'Overhead cost'),
            //     actualCost: this.actualOverheadCost.value,
            //     cost: this.plannedOverheadCost.value,
            // },
        ]);
        if (this.hasBillOfMaterial.value || this.productionComponents.value.length > 0) {
            this.costs.addRecord({
                _id: this.costs.generateRecordId(),
                costTitle: ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order__costs____row__title__material_cost',
                    'Material cost',
                ),
                actualCost: this.actualMaterialCost.value,
                cost: this.plannedMaterialCost.value,
            });
        }
        if (this.hasRouting.value || this.productionOperations.value.length > 0) {
            this.costs.addRecord({
                _id: this.costs.generateRecordId(),
                costTitle: ui.localize(
                    '@sage/xtrem-manufacturing/pages__work_order__costs____row__title__process_cost',
                    'Process cost',
                ),
                actualCost: this.actualProcessCost.value,
                cost: this.plannedProcessCost.value,
            });
        }
    }

    // eslint-disable-next-line class-methods-use-this
    sumWorkOrderCosts(processCost: number, materialCost: number, overheadCost: number): number {
        return processCost + materialCost + overheadCost;
    }

    async updateComponents(quantity: number) {
        await asyncArray(this.productionComponents.value)
            .filter(
                component => !component.isFixedLinkQuantity && !!+(component.linkQuantity ?? 0), // filter text lines
            )
            .forEach(async component => {
                component.requiredQuantity = (
                    await this.$.graph
                        .node('@sage/xtrem-manufacturing/WorkOrderComponent')
                        .queries.setRequiredQuantity(true, {
                            releasedQuantity: quantity,
                            baseQuantity: this.baseQuantity.value ?? 0,
                            linkQuantity: component.linkQuantity,
                            isFixed: component.isFixedLinkQuantity,
                            scrapFactor: component.scrapFactor ? component.scrapFactor : 0,
                            decimalPlaces: component.unit?.decimalDigits,
                        })
                        .execute()
                ).toString();

                if (Number(component.consumedQuantity) > 0) {
                    component.lineStatus =
                        Number(component.consumedQuantity) >= +(component.requiredQuantity ?? 0)
                            ? 'completed'
                            : 'inProgress';
                }
                this.productionComponents.setRecordValue(component);
            });
    }

    private async updateOperations(quantity: number, previousQuantity: number): Promise<void> {
        await asyncArray(this.productionOperations.value).forEach(
            async (operation: ui.PartialNodeWithId<WorkOrderOperation>) => {
                operation.resources = await this.productionOperations.loadChildRecords({
                    childLevel: 1,
                    parentRecordId: operation._id,
                    openLevel: false,
                });
                if (operation.resources) {
                    operation.resources.forEach((resource: ui.PartialCollectionValue<WorkOrderOperationResource>) => {
                        resource.expectedRunTime = (
                            ((resource.expectedRunTime ? +resource.expectedRunTime : 0) * +quantity) /
                            +previousQuantity
                        ).toString();
                        if (resource.actualRunTime && +resource.actualRunTime > 0) {
                            resource.status =
                                +resource.actualRunTime >= +resource.expectedRunTime ? 'completed' : 'inProgress';
                        }
                        this.productionOperations.addOrUpdateRecordValue(resource, 1, operation._id);
                    });
                    operation.expectedRunTime = (
                        ((operation.expectedRunTime ? +operation.expectedRunTime : 0) * +quantity) /
                        +previousQuantity
                    ).toString();
                    if (operation.actualRunTime && +operation.actualRunTime > 0) {
                        operation.status =
                            +operation.actualRunTime >= +operation.expectedRunTime ? 'completed' : 'inProgress';
                    }
                    operation.plannedQuantity = quantity.toString();
                    this.productionOperations.addOrUpdateRecordValue(operation, 0, undefined);
                }
            },
        );
    }

    async updateReleasedItemChanges() {
        const data = {
            _id: this.releasedItemId,
            releasedQuantity: this.releasedItemQuantity.value ?? undefined,
            document: this._id.value,
            storedAttributes: this.workOrderReleasedItemDimensions?.attributes || '{}',
            storedDimensions: this.workOrderReleasedItemDimensions?.dimensions || '{}',
        };
        await this.$.graph
            .node('@sage/xtrem-manufacturing/WorkOrderReleasedItem')
            .update({ _id: true, releasedItem: { _id: true, id: true, name: true }, releasedQuantity: true }, { data })
            .execute();
    }

    getProductionComponentsPayload(components: Partial<Dict<any>>[]) {
        components.forEach(c => {
            delete c.plannedCost;
            delete c.actualCost;
            delete c.completedQuantityPercentage;
            delete c.allocationRequestStatus;
            if (c.lineType !== 'text') {
                c.requiredDate = this.startDate.value;
            } else {
                delete c.operation;
            }
        });
    }

    static getProductionOperationsPayload(operations: Partial<Dict<any>>[]) {
        operations.forEach(o => {
            // TODO: need to refactor when operations is restructured
            // Original operation is not available any more
            // if (o.originalOperation.routing.timeUnit === 'seconds') {
            //     o.setupTime = Math.round(o.setupTime);
            //     o.runTime = Math.round(o.runTime);
            //     o.actualSetupTime = Math.round(o.actualSetupTime);
            //     o.actualRunTime = Math.round(o.actualRunTime);
            // }
            // delete o.batchCost;
            // delete o.wipCost;
            // delete o.completedTimePercentage;
            delete o.startDatetime;
            delete o.endDatetime;
            if (o.resources) {
                o.resources.forEach((r: WorkOrderOperationResourceInput) => {
                    delete r.startDatetime;
                    delete r.endDatetime;
                });
            }
        });
    }

    updateComponentRowStatus(rowId: any, data: any, status: string) {
        data.lineStatus = status;
        this.productionComponents.setRecordValue(data);
    }

    async addEditComponent(id: string, component: ui.PartialCollectionValue<WorkOrderComponent> | null) {
        const highestNumber = this.productionComponents.value.reduce(
            (prev, cmp) => Math.max(prev, cmp.componentNumber ?? 0),
            0,
        );
        // The loader is needed to avoid double and more click error.
        this.$.loader.isHidden = false;
        const changedComponent = (await this.$.dialog.page(
            '@sage/xtrem-manufacturing/WorkOrderComponentPanel',
            {
                id,
                component: JSON.stringify(component || {}),
                components: JSON.stringify(this.productionComponents.value.map(c => c.componentNumber)),
                workOrder: this.number.value || '',
                site: this.site.value?._id || '',
                releasedQuantity: this.releasedItemQuantity.value || 0,
                baseQuantity: this.baseQuantity.value || 1,
                _defaultDimensions: JSON.stringify(this._defaultDimensionsAttributesArray[1]),
                highestNumber,
            },
            { resolveOnCancel: true, rightAligned: true, size: 'large' },
        )) as ui.PartialNodeWithId<WorkOrderComponent>;

        if (changedComponent && !isEmpty(changedComponent)) {
            if (changedComponent.allocationRequestStatus !== 'inProgress') {
                changedComponent.allocationRequestStatus = 'noRequest';
            }
            if (changedComponent.lineType !== 'text' && (!changedComponent._id || Number(changedComponent._id) <= 0)) {
                // We get the planned cost only for added components and not saved yet
                const requiredQuantity = changedComponent.requiredQuantity || 0;
                changedComponent.plannedCost = (
                    await this.$.graph
                        .node('@sage/xtrem-manufacturing/WorkOrderComponent')
                        .queries.getMaterialPlannedCost(true, {
                            searchCriteria: {
                                requiredQuantity,
                                requiredDate: this.startDate.value ?? '',
                                item: changedComponent.item?._id ?? 0,
                                site: this.site.value?._id ?? 0,
                                lineStatus: 'pending',
                            },
                        })
                        .execute()
                ).toString();
            }
            this.productionComponents.addOrUpdateRecordValue(changedComponent);
            if (changedComponent._id === '0') {
                this.productionComponents.value = this.productionComponents.value.sort(
                    (a, b) => (a.componentNumber || 0) - (b.componentNumber || 0),
                );
            }
        }
    }

    setPageReadonly() {
        const readonly = this.status.value === 'completed' || this.status.value === 'closed';
        this.type.isReadOnly = readonly;
        this.releasedItemQuantity.isReadOnly = readonly;
        this.startDate.isReadOnly = readonly;
        this.endDate.isReadOnly = this.status.value === 'closed';
        this.isForwardScheduling.isReadOnly = readonly;
        this.requestedDate.isReadOnly = readonly;
    }

    setBomAndRoutingFlags() {
        this.hasBillOfMaterial.value = this.bomCode.value !== null;
        this.hasRouting.value = this.routingCode.value !== null;
    }

    async getTrackings(id: string) {
        let nextRow = 0;

        const workOrder = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrder')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            materialTrackings: querySelector(
                                {
                                    _id: true,
                                    number: true,
                                    entryDate: true,
                                    site: { _id: true, id: true, name: true, isLocationManaged: true },
                                    stockTransactionStatus: true,
                                },
                                { filter: { workOrder: { _id: id } } },
                            ),
                            productionTrackings: querySelector(
                                {
                                    _id: true,
                                    number: true,
                                    entryDate: true,
                                    site: { _id: true, id: true, name: true, isLocationManaged: true },
                                    stockTransactionStatus: true,
                                },
                                { filter: { workOrder: { _id: id } } },
                            ),
                            timeTrackings: querySelector(
                                {
                                    _id: true,
                                    number: true,
                                    entryDate: true,
                                    site: { _id: true, id: true, name: true, isLocationManaged: true },
                                },
                                { filter: { workOrder: { _id: id } } },
                            ),
                        },
                        { filter: { _id: id } },
                    ),
                )
                .execute(),
        );

        const materialTrackings: TrackingDocument[] = workOrder[0].materialTrackings as TrackingDocument[];
        const productionTrackings: TrackingDocument[] = workOrder[0].productionTrackings as TrackingDocument[];
        const timeTrackings: TrackingDocument[] = workOrder[0].timeTrackings as TrackingDocument[];

        this.setTrackingView('Material tracking', materialTrackings, nextRow);
        nextRow += materialTrackings.length;

        this.setTrackingView('Production tracking', productionTrackings, nextRow);

        nextRow += productionTrackings.length;

        this.setTrackingView('Time tracking', timeTrackings, nextRow);

        this.$.setPageClean();
    }

    setTrackingView(type: string, documents: TrackingDocument[], nextRow: number) {
        let rowId = nextRow;
        documents.forEach((document: TrackingDocument) => {
            rowId += 1;
            const tracking: ui.PartialNodeWithId<WorkOrderTrackingView> = {
                _id: rowId.toString(),
                id: document._id,
                date: document.entryDate,
                number: document.number,
                site: document.site as unknown as ExtractEdgesPartial<Site>,
                status: document.stockTransactionStatus ? document.stockTransactionStatus : '',
                type,
            };
            this.trackingView.addOrUpdateRecordValue(tracking);
        });
    }

    setDeleteAction() {
        if (!this.deleteWorkOrder.isDisabled && this.status.value !== 'pending') {
            this.deleteWorkOrder.isDisabled = true;
        }
    }

    hideShowDefaultDimensionActions() {
        this.productionOperationsDefaultDimension.isHidden =
            !['pending', 'inProgress'].includes(this.status.value || '') && !this.inheritDimensions;
        this.productionComponentsDefaultDimension.isHidden =
            !['pending', 'inProgress'].includes(this.status.value || '') && !this.inheritDimensions;
    }

    async updatePlannedCosts() {
        await this.$.graph
            .node('@sage/xtrem-manufacturing/WorkOrder')
            .mutations.updatePlannedCosts({ _id: true }, { workOrder: this._id.value })
            .execute();
    }

    allocateStock(rowId: string, rowItem: ui.PartialNodeWithId<WorkOrderComponent>) {
        const line = rowItem;
        const requiredQuantity = Number(line.requiredQuantity) - Number(line.consumedQuantity);
        return MasterDataUtils.confirmDialogToBoolean(
            StockDetailHelper.editStockDetails(this, line, {
                movementType: 'allocation',
                data: {
                    isEditable: ['pending', 'inProgress'].includes(line.lineStatus!),
                    needFullAllocation: false,
                    cannotOverAllocate: true,
                    shouldControlAllocationInProgress: true,
                    documentLineHasAlreadySomeAllocations: Number(line.quantityAllocated) > 0,
                    documentLineSortValue: line._sortValue,
                    documentLine: rowId,
                    item: line.item?._id,
                    stockSite: this.site.value?._id,
                    quantity: Number(requiredQuantity),
                    unit: line.item!.stockUnit?._id,
                    searchCriteria: {
                        activeQuantityInStockUnit: line.requiredQuantity ? +line.requiredQuantity : 0,
                        item: line.item?._id ?? '',
                        site: this.site.value?._id ?? '',
                        stockUnit: line.item!.stockUnit?._id,
                        statusList: [{ statusType: 'accepted' }],
                        ignoreExpirationDate: true,
                    },
                },
            }),
        );
    }

    disableUsesBOMorRouting(code: boolean, numberOfLines: number): boolean {
        return this.$.isDirty || code || numberOfLines > 0 || ['completed', 'closed'].includes(this.status.value || '');
    }

    confirmAddingComponentsOrOperations(title: string, message: string): Promise<boolean> {
        const options: ui.dialogs.DialogOptions = {
            acceptButton: { text: 'Continue' },
            cancelButton: { text: 'Cancel' },
        };
        return MasterDataUtils.confirmDialogToBoolean(this.$.dialog.confirmation('warn', title, message, options));
    }

    async addComponentsOrOperations(addComponents: boolean, workOrderId: string, itemId: string, siteId: string) {
        this.$.loader.isHidden = false;
        const returnValues = { _id: true, number: true, name: true };
        const parameters = { workOrder: workOrderId, item: itemId, site: siteId };
        const result = addComponents
            ? await this.$.graph
                  .node('@sage/xtrem-manufacturing/WorkOrder')
                  .mutations.addComponentsToWorkOrder(returnValues, parameters)
                  .execute()
            : await this.$.graph
                  .node('@sage/xtrem-manufacturing/WorkOrder')
                  .mutations.addOperationsToWorkOrder(returnValues, parameters)
                  .execute();
        this.$.loader.isHidden = true;
        await this.$.router.refresh(true);
        const successMessage = addComponents
            ? ui.localize(
                  '@sage/xtrem-manufacturing/pages__work_order__components_added_to_work_order',
                  'Components added to work order {{workOrderNumber}}',
                  { workOrderNumber: result.number },
              )
            : ui.localize(
                  '@sage/xtrem-manufacturing/pages__work_order__operations_added_to_work_order',
                  'Operations added to work order {{workOrderNumber}}',
                  { workOrderNumber: result.number },
              );
        this.$.showToast(successMessage, { timeout: 0, type: 'success' });
    }

    checkPreGenerateSerialNumbers() {
        if (this.isServiceOptionsSerialNumberActive.value === true) {
            if (
                this.hasRouting.value === true &&
                this.routingCode.value?.doSerialNumberPreGeneration &&
                this.type.value === 'firm' &&
                !['closed', 'completed'].includes(this.status.value || '') &&
                this.productionItems.value[0]?.releasedItem?.serialNumberManagement === 'managed'
            ) {
                return true;
            }
        }
        return false;
    }

    async checkAlreadyPregeneratedSerialNumbers() {
        if (
            (await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrder')
                .mutations.preGenerateSerialNumbers(true, {
                    releasedItem: this.releasedItem.value?._id,
                    releasedQuantity: this.releasedItemQuantity.value,
                    site: this.site.value?._id,
                    productionItem: this.productionItems.value[0]?._id,
                    checkOnly: true,
                })
                .execute()) >= '0'
        ) {
            return false;
        }
        return true;
    }

    async pregeneratedBusinessRules() {
        await this.updatePreGeneratedSerialNumbersQuantity();
        const result = this.pregeneratedSerialNumbersPageAction();
        this.preGenerateSerialNumbers.isHidden = !result.pregenerated;
        this.viewSerialNumbers.isHidden = !result.view;
        this.type.isReadOnly = result.view;
        if (this.status.value === 'inProgress') {
            this.preGenerateAdditionalSerialNumbers.isHidden = !result.additional;
        }
    }

    pregeneratedSerialNumbersPageAction(): {
        pregenerated: boolean;
        additional: boolean;
        view: boolean;
    } {
        const releasedQty = this.releasedItemQuantity.value ? this.releasedItemQuantity.value : 0;
        const serialNumberQty = this.preGeneratedSerialNumbersQuantity.value
            ? this.preGeneratedSerialNumbersQuantity.value
            : 0;
        const pregenerated = releasedQty > serialNumberQty && this.type.value === 'firm';
        const additional = serialNumberQty >= releasedQty && this.type.value === 'firm';
        const view = serialNumberQty > 0 && this.type.value === 'firm';
        return { pregenerated, additional, view };
    }

    async updatePreGeneratedSerialNumbersQuantity(): Promise<void> {
        const serialNumbers = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-stock-data/SerialNumber')
                .aggregate.query(
                    aggregateEdgesSelector(
                        {
                            group: { baseDocumentLine: { _id: { _by: 'value' } } },
                            values: { id: { distinctCount: true } },
                        },
                        { filter: { baseDocumentLine: this.productionItems.value[0]._id } },
                    ),
                )
                .execute(),
        );

        if (serialNumbers.length) {
            this.preGeneratedSerialNumbersQuantity.value = serialNumbers[0].values.id?.distinctCount
                ? serialNumbers[0].values.id?.distinctCount
                : 0;
        }
    }

    async hideShowAssignOrder() {
        const isAssignOrderHidden =
            (
                await checkAssignmentLinkExist({
                    page: this,
                    supplyDocumentLine: this.releasedItemId,
                    supplyType: 'workOrder',
                })
            ).length === 0;
        this.orderToOrderIcon.isHidden = isAssignOrderHidden;
    }

    async assignOrderDetailPanel() {
        const isAssignOrderHidden = isOrderToOrderServiceOptionActivated(this)
            ? !(await openOrderAssignments(this))
            : false;
        this.orderToOrderIcon.isHidden = isAssignOrderHidden;
    }

    initFromNotificationHistoryParameters() {
        this.fromNotificationHistory = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        this.financeTransactionSysId = this.$.queryParameters.financeTransactionSysId || '';
        this.wipSourceDocumentType = this.$.queryParameters.wipSourceDocumentType || '';
    }

    mapDatetime(datetimeValue: string | null): string {
        return datetimeValue
            ? datetime
                  .parse(datetimeValue, undefined, undefined, this.timeZone.value || undefined)
                  .format(undefined, 'YYYY-MM-DD HH:mm:ss')
            : '';
    }

    validateAndSetClosingDate(closingDate: string | null): boolean {
        if (
            isObject(closingDate) ||
            closingDate === null ||
            typeof closingDate !== 'string' ||
            Number.isNaN(Date.parse(closingDate))
        ) {
            return false;
        }
        this.closingDate.value = closingDate;
        return true;
    }

    initPosting() {
        this.postingMessageBlock.isHidden = true;
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value[0].message ?? '';
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus || '',
                ),
            },
            recordId: this.$.recordId,
        });
    }
}
