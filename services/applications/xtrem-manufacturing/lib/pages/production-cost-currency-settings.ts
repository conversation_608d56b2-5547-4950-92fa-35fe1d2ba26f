import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<ProductionCostCurrencySettings>({
    title: 'Production cost currency settings',
    isTransient: true,
    onLoad() {
        if (this.$.queryParameters.productionCostCurrency) {
            this.productionCostCurrency.value = JSON.parse(this.$.queryParameters.productionCostCurrency as string);
        }
    },
    businessActions() {
        return [this.save];
    },
})
export class ProductionCostCurrencySettings extends ui.Page {
    @ui.decorators.section<ProductionCostCurrencySettings>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<ProductionCostCurrencySettings>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.referenceField<ProductionCostCurrencySettings>({
        parent() {
            return this.fieldBlock;
        },
        node: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        helperTextField: 'id',
        title: 'Currency for cost values',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        isMandatory: true,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.numeric({ bind: 'decimalDigits', isHidden: true }),
        ],
    })
    productionCostCurrency: ui.fields.Reference;

    @ui.decorators.pageAction<ProductionCostCurrencySettings>({
        title: 'Save settings',
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                this.$.finish({
                    productionCostCurrency: JSON.stringify(this.productionCostCurrency.value),
                });
            }
        },
    })
    save: ui.PageAction;
}
