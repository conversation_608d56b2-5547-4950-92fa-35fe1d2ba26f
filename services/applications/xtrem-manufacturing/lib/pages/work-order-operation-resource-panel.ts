import { extractEdges } from '@sage/xtrem-client';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type {
    GraphApi,
    WorkOrderOperation,
    WorkOrderOperationResource,
    WorkOrderOperationResourceDetail,
} from '@sage/xtrem-manufacturing-api';
import type { BaseResource } from '@sage/xtrem-master-data-api';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { noop } from 'lodash';

@ui.decorators.page<WorkOrderOperationResourcePanel>({
    title: 'Operation resource',
    node: '@sage/xtrem-manufacturing/WorkOrderOperationResource',
    module: 'manufacturing',
    businessActions() {
        return [this.cancel, this.save];
    },
    async onLoad() {
        await this.setPageValues(
            this.$.queryParameters.operationResource,
            this.$.queryParameters.site,
            this.$.queryParameters.operation,
            this.$.queryParameters.id as number,
        );
        if (this._id.value) {
            await this.displayResources(this.resource.value);
            this.$.page.title = `${ui.localize(
                '@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__edit____title',
                'Edit work order operation resource',
            )}`;
        } else {
            this.$.page.title = `${ui.localize(
                '@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__new____title',
                'New work order operation resource',
            )}`;
        }
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return utils.formatError(this, error);
    },
})
export class WorkOrderOperationResourcePanel extends ui.Page<GraphApi> {
    private lineData: WorkOrderOperationResource;

    @ui.decorators.pageAction<WorkOrderOperationResourcePanel>({
        title: 'OK',

        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                this.$.finish({
                    ...this.lineData,
                    _id: this._id.value,
                    workOrderOperation: this.operation.value,
                    resource: this.resource.value,
                    expectedSetupTime: this.expectedSetupTime.value,
                    expectedRunTime: this.expectedRunTime.value,
                    isResourceQuantity: this.isResourceQuantity.value,
                    resources: this.resources.value,
                });
            } else {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            }
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<WorkOrderOperationResourcePanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.section<WorkOrderOperationResourcePanel>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<WorkOrderOperationResourcePanel>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.block<WorkOrderOperationResourcePanel>({
        parent() {
            return this.section;
        },
    })
    dimensionsBlock: ui.containers.Block;

    @ui.decorators.referenceField<WorkOrderOperationResourcePanel, WorkOrderOperation>({
        parent() {
            return this.block;
        },
        title: 'Operation',
        node: '@sage/xtrem-manufacturing/WorkOrderOperation',
        lookupDialogTitle: 'Select work order operation',
        valueField: 'name',
        helperTextField: 'operationNumber',
        isTransient: true,
        columns: [
            ui.nestedFields.text({
                title: 'Operation number',
                bind: 'operationNumber',
            }),
            ui.nestedFields.text({
                title: 'Name',
                bind: 'name',
            }),
        ],
    })
    operation: ui.fields.Reference<WorkOrderOperation>;

    @ui.decorators.referenceField<WorkOrderOperationResourcePanel, BaseResource>({
        parent() {
            return this.block;
        },
        title: 'Resource group',
        node: '@sage/xtrem-master-data/BaseResource',
        lookupDialogTitle: 'Select resource group',
        minLookupCharacters: 1,
        valueField: 'name',
        helperTextField: 'id',
        width: 'small',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.text({ title: 'Type', bind: '_constructor' }),
            ui.nestedFields.reference<WorkOrderOperationResourcePanel, BaseResource, BaseResource['site']>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: '_id',
                isHidden: true,
            }),
        ],
        filter() {
            return {
                site: { _id: { _eq: `${this.site.value._id}` } },
            };
        },
        async onChange() {
            if (this.resource.value) {
                await this.copyResources(this.resource.value);
            }
        },
    })
    resource: ui.fields.Reference<BaseResource>;

    @ui.decorators.numericField<WorkOrderOperationResourcePanel>({
        parent() {
            return this.block;
        },
        title: 'Setup time',
        postfix() {
            return this.setupTimeUnit.value.symbol;
        },
        scale() {
            return this.setupTimeUnit.value.decimalDigits;
        },
    })
    expectedSetupTime: ui.fields.Numeric;

    @ui.decorators.numericField<WorkOrderOperationResourcePanel>({
        parent() {
            return this.block;
        },
        title: 'Run time',
        postfix() {
            return this.runTimeUnit.value.symbol;
        },
        scale() {
            return this.runTimeUnit.value.decimalDigits;
        },
        onChange() {
            this.isResourceQuantity.isReadOnly = !this.expectedRunTime.value;
            if (this.isResourceQuantity.isReadOnly) {
                this.isResourceQuantity.value = false;
            } else if (!(this.$.queryParameters.id as number)) {
                // If we don't have any other resources tracking quantity default this resource to track quantity
                this.isResourceQuantity.value = !JSON.parse(this.$.queryParameters.quantityFlagged as string);
            }
        },
    })
    expectedRunTime: ui.fields.Numeric;

    @ui.decorators.checkboxField<WorkOrderOperationResourcePanel>({
        parent() {
            return this.block;
        },
        title: 'Is resource quantity',
        isReadOnly() {
            return !this.expectedRunTime.value;
        },
    })
    isResourceQuantity: ui.fields.Checkbox;

    @ui.decorators.referenceField<WorkOrderOperationResourcePanel, Site>({
        parent() {
            return this.block;
        },
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'isLocationManaged', isHidden: true }),
        ],
        isTransient: true,
        isHidden: true,
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<WorkOrderOperationResourcePanel>({
        title: 'Setup time unit',
        bind: 'setupTimeUnit',
        valueField: 'name',
        helperTextField: 'symbol',
        isHidden: true,
        parent() {
            return this.block;
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', isHidden: true }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.text({ bind: 'decimalDigits', isHidden: true }),
        ],
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
        lookupDialogTitle: 'Select unit of measure',
    })
    setupTimeUnit: ui.fields.Reference;

    @ui.decorators.referenceField<WorkOrderOperationResourcePanel>({
        title: 'Time unit',
        bind: 'runTimeUnit',
        valueField: 'name',
        helperTextField: 'symbol',
        isHidden: true,
        parent() {
            return this.block;
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', isHidden: true }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.text({ bind: 'decimalDigits', isHidden: true }),
        ],
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
        lookupDialogTitle: 'Select unit of measure',
        minLookupCharacters: 1,
    })
    runTimeUnit: ui.fields.Reference;

    @ui.decorators.textField<WorkOrderOperationResourcePanel>({
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.tableField<WorkOrderOperationResourcePanel, WorkOrderOperationResourceDetail>({
        title: 'Resources',
        bind: 'resources',
        canSelect: false,
        isTransient: true,
        pageSize: 10,
        node: '@sage/xtrem-manufacturing/WorkOrderOperationResourceDetail',
        orderBy: {
            resource: 1,
        },
        parent() {
            return this.section;
        },
        columns: [
            ui.nestedFields.reference<WorkOrderOperationResourcePanel, WorkOrderOperationResourceDetail, BaseResource>({
                title: 'ID',
                bind: 'resource',
                node: '@sage/xtrem-master-data/BaseResource',
                valueField: 'id',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                    ui.nestedFields.text({ title: 'Description', bind: 'description' }),
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                ],
            }),
            ui.nestedFields.reference<WorkOrderOperationResourcePanel, WorkOrderOperationResourceDetail, BaseResource>({
                title: 'Name',
                bind: 'resource',
                node: '@sage/xtrem-master-data/BaseResource',
                valueField: 'name',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                    ui.nestedFields.text({ title: 'Description', bind: 'description' }),
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                ],
            }),
            ui.nestedFields.reference<WorkOrderOperationResourcePanel, WorkOrderOperationResourceDetail, BaseResource>({
                title: 'Description',
                bind: 'resource',
                node: '@sage/xtrem-master-data/BaseResource',
                valueField: 'description',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ title: 'ID', bind: 'id', canFilter: false }),
                    ui.nestedFields.text({ title: 'Description', bind: 'description' }),
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                ],
            }),
            ui.nestedFields.reference<WorkOrderOperationResourcePanel, WorkOrderOperationResourceDetail, BaseResource>({
                title: 'Active from',
                bind: 'resource',
                node: '@sage/xtrem-master-data/BaseResource',
                valueField: 'activeFrom',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                    ui.nestedFields.text({ title: 'Description', bind: 'description' }),
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                ],
            }),

            ui.nestedFields.reference<WorkOrderOperationResourcePanel, WorkOrderOperationResourceDetail, BaseResource>({
                title: 'Active to',
                bind: 'resource',
                node: '@sage/xtrem-master-data/BaseResource',
                valueField: 'activeTo',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                    ui.nestedFields.text({ title: 'Description', bind: 'description' }),
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                ],
            }),
        ],
    })
    resources: ui.fields.Table;

    @ui.decorators.buttonField<WorkOrderOperationResourcePanel>({
        parent() {
            return this.dimensionsBlock;
        },
        isTransient: true,
        map() {
            return ui.localize(
                '@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__dimensions_button_text',
                'Dimensions',
            );
        },
        onError(error) {
            if (!(error instanceof Error) && !(typeof error === 'string')) {
                return noop();
            }
            return utils.formatError(this, error as any);
        },
        async onClick() {
            if (!this.lineData) {
                this.lineData = {} as any;
            }
            const oldLineData = this.lineData;
            this.lineData = await dimensionPanelHelpers.editDisplayDimensions(
                this,
                {
                    documentLine: this.lineData,
                },
                {
                    editable: ['pending', 'inProgress'].includes(this.lineData.status),
                },
            );
            if (
                this.save.isDisabled &&
                (oldLineData.storedAttributes !== this.lineData.storedAttributes ||
                    oldLineData.storedDimensions !== this.lineData.storedDimensions)
            ) {
                this.save.isDisabled = false;
            }
        },
    })
    dimensionsButton: ui.fields.Button;

    private async setPageValues(operationResource: any, currentSite: any, operation: any, id: number) {
        this.operation.value = JSON.parse(operation);
        this.operation.isReadOnly = true;
        const site = JSON.parse(currentSite);
        this.site.value = site;

        if (operationResource) {
            const selectedOperationResource = JSON.parse(operationResource);

            if (!id) {
                selectedOperationResource.status = 'pending';
            }

            this.resource.value = selectedOperationResource.resource;
            this.expectedSetupTime.value = selectedOperationResource.expectedSetupTime;
            this.expectedRunTime.value = selectedOperationResource.expectedRunTime;
            this.isResourceQuantity.value = selectedOperationResource.isResourceQuantity;
            this.setupTimeUnit.value = selectedOperationResource.setupTimeUnit
                ? selectedOperationResource.setupTimeUnit
                : this.operation.value.setupTimeUnit;
            this.runTimeUnit.value = selectedOperationResource.runTimeUnit
                ? selectedOperationResource.runTimeUnit
                : this.operation.value.runTimeUnit;
            await this.displayResources(selectedOperationResource.resource);
            if (selectedOperationResource.status !== 'pending') {
                // FIXME: XT-8240 cannot disable a block/section
                // this.section.isDisabled = true;
                this.expectedSetupTime.isDisabled = true;
                this.expectedRunTime.isDisabled = true;
                this.isResourceQuantity.isDisabled = true;
                this.resource.isDisabled = true;
                this.save.isDisabled = true;
            }
            this.lineData = selectedOperationResource;
        }
    }

    private async displayResources(resource: any) {
        if (resource) {
            const result = extractEdges(
                await this.$.graph
                    .node('@sage/xtrem-manufacturing/WorkOrderOperationResourceDetail')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                _id: true,
                                workOrderOperationResource: { expectedSetupTime: true, expectedRunTime: true },
                                resource: {
                                    _id: true,
                                    id: true,
                                    name: true,
                                    description: true,
                                    activeFrom: true,
                                    activeTo: true,
                                },
                            },
                            {
                                filter: {
                                    workOrderOperationResource: {
                                        workOrderOperation: { _id: this.operation.value._id },
                                        resource: { _id: resource._id },
                                    },
                                },
                            },
                        ),
                    )
                    .execute(),
            );
            this.resources.value = [];
            if (result.length > 0) {
                result.forEach(operationResourceDetail => {
                    this.resources.value = this.resources.value.concat({
                        resource: operationResourceDetail.resource,
                        _id: operationResourceDetail._id,
                    });
                });
            }
        }
    }

    private async copyResources(resource: any) {
        this.save.isDisabled = false;
        this.resources.value = [];
        if (resource && resource._constructor === 'GroupResource') {
            const result = extractEdges(
                await this.$.graph
                    .node('@sage/xtrem-master-data/GroupResource')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                _id: true,
                                resources: {
                                    query: {
                                        edges: {
                                            node: {
                                                _id: true,
                                                id: true,
                                                name: true,
                                                description: true,
                                                activeFrom: true,
                                                activeTo: true,
                                            },
                                        },
                                    },
                                },
                            },
                            {
                                filter: {
                                    _id: `${resource._id}`,
                                },
                            },
                        ),
                    )
                    .execute(),
            );
            if (result.length > 0 && result[0].resources.length > 0) {
                result[0].resources.forEach(detailResource => {
                    this.resources.addRecord({
                        resource: detailResource,
                    });
                });
            } else {
                this.save.isDisabled = true;
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__empty_resource_group',
                        'The resource group must not be empty of resource. Please choose another group or add resource to the group.',
                    ),
                );
            }
        }
    }
}
