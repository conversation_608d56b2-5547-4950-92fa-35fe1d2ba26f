import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<ProductionCostTrendSettings>({
    title: 'Production cost trend settings',
    isTransient: true,
    onLoad() {
        if (this.$.queryParameters.productionCostCurrency) {
            this.productionCostCurrency.value = JSON.parse(this.$.queryParameters.productionCostCurrency as string);
        }
        if (this.$.queryParameters.reportMonths) {
            this.reportMonths.value = this.$.queryParameters.reportMonths as number;
        }
    },
    businessActions() {
        return [this.save];
    },
})
export class ProductionCostTrendSettings extends ui.Page {
    @ui.decorators.section<ProductionCostTrendSettings>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<ProductionCostTrendSettings>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.referenceField<ProductionCostTrendSettings>({
        parent() {
            return this.fieldBlock;
        },
        node: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        helperTextField: 'id',
        title: 'Currency for cost values',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        isMandatory: true,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.numeric({ bind: 'decimalDigits', isHidden: true }),
        ],
    })
    productionCostCurrency: ui.fields.Reference;

    @ui.decorators.numericField<ProductionCostTrendSettings>({
        title: 'Number of months',
        isMandatory: true,
        scale: 0,
        max: 12,
        min: 2,
        parent() {
            return this.fieldBlock;
        },
    })
    reportMonths: ui.fields.Numeric;

    @ui.decorators.pageAction<ProductionCostTrendSettings>({
        title: 'Save settings',
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                this.$.finish({
                    productionCostCurrency: JSON.stringify(this.productionCostCurrency.value),
                    reportMonths: this.reportMonths.value,
                });
            }
        },
    })
    save: ui.PageAction;
}
