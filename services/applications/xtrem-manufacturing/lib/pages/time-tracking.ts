import type { ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import type { GraphApi, WorkOrder, WorkOrderOperationResource } from '@sage/xtrem-manufacturing-api';
import type { CapabilityLevel, DetailedResource, Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as common from '@sage/xtrem-master-data/build/lib/client-functions/common';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { manufacturing } from '@sage/xtrem-master-data/build/lib/menu-items/manufacturing';
import type { Site } from '@sage/xtrem-system-api';
import type { BillOfMaterial } from '@sage/xtrem-technical-data-api';
import * as ui from '@sage/xtrem-ui';
import { merge } from 'lodash';
import { showGeneratedTrackingsMessage } from '../client-functions/material-tracking';
import * as PillColor from '../client-functions/pill-color';

type CustomWorkOrderOperationResource = WorkOrderOperationResource & {
    expectedResource: DetailedResource;
    completedRunTime: string;
    plannedQuantity: string;
    completedQuantity: string;
    completedSetupTime: string;
    actualQuantity: string;
    isCompleted: boolean;
};

@ui.decorators.page<TimeTracking>({
    title: 'Time tracking',
    menuItem: manufacturing,
    priority: 200,
    module: 'manufacturing',
    mode: 'default',
    isTransient: true,
    access: { node: '@sage/xtrem-manufacturing/OperationTracking', bind: 'createMultipleOperationalTrackings' },
    businessActions() {
        return [this.createTrackings];
    },
    headerDropDownActions() {
        return [this.$standardOpenCustomizationPageWizardAction];
    },
    async onLoad() {
        if (this.$.queryParameters.searchCriteria) {
            await this.setPageValues(JSON.parse(this.$.queryParameters.searchCriteria as string));
        } else {
            await setReferenceIfSingleValue([this.site]);
        }
    },
})
export class TimeTracking extends ui.Page<GraphApi> {
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    @ui.decorators.section<TimeTracking>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<TimeTracking>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.pageAction<TimeTracking>({
        title: 'Generate',
        isDisabled: true,
        async onClick() {
            if (this.workOrderOperations.selectedRecords.length) {
                const validation = await this.$.page.validate();
                if (validation.length) {
                    this.$.showToast(validation.join('\n'), { type: 'error' });
                    return;
                }
                this.$.loader.isHidden = false;
                const trackings = this.workOrderOperations.value.filter(
                    v => this.workOrderOperations.selectedRecords.indexOf(v._id) !== -1,
                );

                const result = await this.$.graph
                    .node('@sage/xtrem-manufacturing/OperationTracking')
                    .asyncOperations.createMultipleOperationalTrackings.runToCompletion(true, {
                        trackings: trackings.map(tracking => ({
                            workOrderOperation: {
                                _id: tracking.workOrderOperation?._id,
                                operationNumber: tracking.workOrderOperation?.operationNumber ?? '',
                                name: tracking.workOrderOperation?.name ?? '',
                                workOrder: tracking.workOrderOperation?.workOrder?._id,
                                minCapabilityLevel: tracking.workOrderOperation?.minCapabilityLevel?._id,
                            },
                            resource: tracking.expectedResource?._id,
                            expectedResource: tracking.expectedResource?._id,
                            setupTimeUnit: tracking.setupTimeUnit?._id,
                            runTimeUnit: tracking.runTimeUnit?._id,
                            status: tracking.status,
                            actualQuantity: tracking.actualQuantity ?? '0',
                            actualSetupTime: tracking.actualSetupTime ?? '0',
                            actualRunTime: tracking.actualRunTime ?? '0',
                            isCompleted: !!tracking.isCompleted,
                            storedAttributes: tracking.storedAttributes,
                            storedDimensions: tracking.storedDimensions,
                        })),
                    })
                    .execute();

                if (result) {
                    showGeneratedTrackingsMessage(this, result.length);
                    this.$.finish();
                    this.$.setPageClean();
                    const searchCriteria = {
                        site: this.site.value,
                        itemFrom: this.itemFrom.value,
                        itemTo: this.itemTo.value,
                        woNumberFrom: this.woNumberFrom.value,
                        woNumberTo: this.woNumberTo.value,
                        woStartFrom: this.woStartFrom.value,
                        woStartTo: this.woStartTo.value,
                        woStatus: this.operationResourceStatus.value,
                    };
                    this.$.router.goTo('@sage/xtrem-manufacturing/TimeTracking', {
                        searchCriteria: JSON.stringify(searchCriteria),
                    });
                }

                this.$.loader.isHidden = true;
            }
        },
        buttonType: 'primary',
    })
    createTrackings: ui.PageAction;

    @ui.decorators.pageAction<TimeTracking>({
        icon: 'add',
        title: 'Add operation',
        async onClick() {
            const workOrderOperationTimeTracking = await this.$.dialog.page(
                '@sage/xtrem-manufacturing/TimeTrackingWorkOrderOperation',
                {},
                { resolveOnCancel: true, rightAligned: true, size: 'large' },
            );
            if (workOrderOperationTimeTracking) {
                const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                    { _id: this.workOrderOperations.generateRecordId() },
                    this._defaultDimensionsAttributes,
                );

                workOrderOperationTimeTracking.storedAttributes = line.storedAttributes;
                workOrderOperationTimeTracking.storedDimensions = line.storedDimensions;

                this.workOrderOperations.addRecord(workOrderOperationTimeTracking);
            }
        },
    })
    addOperation: ui.PageAction;

    @ui.decorators.referenceField<TimeTracking, Site>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
        placeholder: 'Select site',
        width: 'small',
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<TimeTracking, BillOfMaterial>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From released item',
        node: '@sage/xtrem-technical-data/BillOfMaterial',
        tunnelPage: '@sage/xtrem-technical-data/BillOfMaterial',
        lookupDialogTitle: 'Select item',
        valueField: { item: { name: true } },
        helperTextField: { item: { id: true } },
        columns: [
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<TimeTracking, BillOfMaterial, Item>({
                title: 'Name',
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'isManufactured' }),
                ],
            }),
            ui.nestedFields.text({ bind: { item: { id: true } } }),
            ui.nestedFields.text({ bind: { item: { description: true } } }),
            ui.nestedFields.text({ title: 'Category', bind: { item: { category: { name: true } } } }),
            ui.nestedFields.reference<TimeTracking, BillOfMaterial, BillOfMaterial['site']>({
                bind: 'site',
                valueField: 'name',
                node: '@sage/xtrem-system/Site',
            }),
        ],
        filter() {
            return {
                isActive: true,
                item: { isManufactured: true },
                ...(this.site.value ? { site: { _id: { _eq: this.site.value._id } } } : {}),
            };
        },
    })
    itemFrom: ui.fields.Reference<BillOfMaterial>;

    @ui.decorators.referenceField<TimeTracking, BillOfMaterial>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To released item',
        node: '@sage/xtrem-technical-data/BillOfMaterial',
        tunnelPage: '@sage/xtrem-technical-data/BillOfMaterial',
        lookupDialogTitle: 'Select item',
        valueField: { item: { name: true } },
        helperTextField: { item: { id: true } },
        columns: [
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference({
                title: 'Name',
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'isManufactured' }),
                ],
            }),
            ui.nestedFields.text({ bind: { item: { id: true } } }),
            ui.nestedFields.text({ bind: { item: { description: true } } }),
            ui.nestedFields.text({ title: 'Category', bind: { item: { category: { name: true } } } }),
            ui.nestedFields.reference<TimeTracking, BillOfMaterial, BillOfMaterial['site']>({
                bind: 'site',
                valueField: 'name',
                node: '@sage/xtrem-system/Site',
            }),
        ],
        filter() {
            return {
                isActive: true,
                item: { isManufactured: true },
                ...(this.site.value ? { site: { _id: { _eq: this.site.value._id } } } : {}),
            };
        },
    })
    itemTo: ui.fields.Reference<BillOfMaterial>;

    @ui.decorators.referenceField<TimeTracking, WorkOrder>({
        node: '@sage/xtrem-manufacturing/WorkOrder',
        lookupDialogTitle: 'Select work order',
        valueField: 'number',
        helperTextField: 'name',
        columns: [ui.nestedFields.text({ bind: 'number', title: 'Number' }), ui.nestedFields.text({ bind: 'name' })],
        parent() {
            return this.criteriaBlock;
        },
        title: 'Work order from',
        filter() {
            return this.site.value ? { site: { _id: { _eq: this.site.value._id } } } : {};
        },
    })
    woNumberFrom: ui.fields.Reference<WorkOrder>;

    @ui.decorators.referenceField<TimeTracking, WorkOrder>({
        node: '@sage/xtrem-manufacturing/WorkOrder',
        lookupDialogTitle: 'Select work order',
        valueField: 'number',
        helperTextField: 'name',
        columns: [ui.nestedFields.text({ bind: 'number', title: 'Number' }), ui.nestedFields.text({ bind: 'name' })],
        parent() {
            return this.criteriaBlock;
        },
        title: 'Work order to',
        filter() {
            return {
                ...(this.site.value ? { site: { _id: { _eq: this.site.value._id } } } : {}),
                ...(this.woNumberFrom.value?.number ? { number: { _gte: this.woNumberFrom.value.number } } : {}),
            };
        },
    })
    woNumberTo: ui.fields.Reference<WorkOrder>;

    @ui.decorators.dateField<TimeTracking>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Work order start date from',
        width: 'small',
    })
    woStartFrom: ui.fields.Date;

    @ui.decorators.dateField<TimeTracking>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Work order start date to',
        width: 'small',
    })
    woStartTo: ui.fields.Date;

    @ui.decorators.dropdownListField<TimeTracking>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Status',
        placeholder: 'Select status',
        optionType: '@sage/xtrem-manufacturing/WorkOrderLineStatusFilteredForTracking',
        width: 'small',
        hasEmptyValue: true,
    })
    operationResourceStatus: ui.fields.DropdownList;

    @ui.decorators.buttonField<TimeTracking>({
        parent() {
            return this.criteriaBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-manufacturing/search', 'Search');
        },
        width: 'small',
        async onClick() {
            this.$.loader.isHidden = false;
            await this.search();
            this.$.loader.isHidden = true;
        },
    })
    searchButton: ui.fields.Button;

    getWorkOrderFilter(): Filter<WorkOrder> {
        // XT-67917 force to use as any there to have a clean type
        const filter: Filter<WorkOrder> = {
            type: { _eq: 'firm' },
            status: { _in: ['pending', 'inProgress', 'completed'] },
            _or: [{ routingCode: null } as any, { routingCode: { status: { _ne: 'inDevelopment' } } }],
        };
        if (this.site.value) {
            merge(filter, { site: { _id: { _eq: this.site.value._id } } });
        }
        if (this.woNumberFrom.value?.number) {
            merge(filter, { number: { _gte: this.woNumberFrom.value.number.valueOf() } });
        }
        if (this.woNumberTo.value?.number) {
            merge(filter, { number: { _lte: this.woNumberTo.value.number.valueOf() } });
        }
        if (this.woStartFrom.value) {
            merge(filter, { startDate: { _gte: this.woStartFrom.value } });
        }
        if (this.woStartTo.value) {
            merge(filter, { startDate: { _lte: this.woStartTo.value } });
        }
        if (this.itemFrom.value?.item?.id) {
            merge(filter, { routingCode: { item: { id: { _gte: this.itemFrom.value.item.id } } } });
        }
        if (this.itemTo.value?.item?.id) {
            merge(filter, { routingCode: { item: { id: { _lte: this.itemTo.value.item.id } } } });
        }
        return filter;
    }

    @ui.decorators.tableField<TimeTracking, CustomWorkOrderOperationResource>({
        title: 'Work order operations',
        canResizeColumns: true,
        canExport: true,
        isHelperTextHidden: true,
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.reference<TimeTracking, WorkOrderOperationResource, WorkOrder>({
                node: '@sage/xtrem-manufacturing/WorkOrder',
                bind: { workOrderOperation: { workOrder: true } },
                title: 'Number',
                valueField: 'number',
                size: 'small',
                isReadOnly: (_value, row) => (row?.workOrderOperation?.operationNumber ?? 0) > 0,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'Number', bind: 'number' }),
                    ui.nestedFields.technical({
                        bind: 'routingTimeUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol' }),
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'description' }),
                        ],
                    }),
                ],
                filter() {
                    return this.getWorkOrderFilter();
                },
                onChange(_id, rowData) {
                    if (rowData.workOrder) {
                        this.selectRow(rowData._id);
                        rowData.setupTimeUnit = rowData.workOrder.routingTimeUnit;
                        rowData.runTimeUnit = rowData.workOrder.routingTimeUnit;
                        this.workOrderOperations.setRecordValue(rowData);
                    }
                },
            }),
            ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-manufacturing/OperationStatus',
                size: 'small',
                style: (_id, rowData) =>
                    rowData.status
                        ? PillColor.getLabelColorByStatus('OperationStatus', rowData.status)
                        : PillColor.getTransparentStatusStyle(),
            }),
            ui.nestedFields.text<TimeTracking, WorkOrderOperationResource>({
                bind: { workOrderOperation: { operationNumber: true } },
                title: 'Operation number',
                isReadOnly: true,
                size: 'small',
            }),
            ui.nestedFields.text<TimeTracking, WorkOrderOperationResource>({
                bind: { workOrderOperation: { name: true } },
                title: 'Description',
                isReadOnly: (_value, row) => (row?.workOrderOperation?.operationNumber ?? 0) > 0,
                size: 'small',
            }),
            ui.nestedFields.text<TimeTracking, WorkOrderOperationResource>({
                bind: { workOrderOperation: { workOrder: { routingCode: { item: { name: true } } } } },
                title: 'Released item',
                isReadOnly: true,
                isMandatory: true,
            }),
            ui.nestedFields.text<TimeTracking, WorkOrderOperationResource>({
                bind: { workOrderOperation: { workOrder: { routingCode: { item: { id: true } } } } },
                title: 'Released item ID',
                isReadOnly: true,
                size: 'small',
            }),
            ui.nestedFields.text<TimeTracking, WorkOrderOperationResource>({
                bind: { workOrderOperation: { workOrder: { routingCode: { item: { description: true } } } } },
                title: 'Released item description',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric<TimeTracking, CustomWorkOrderOperationResource>({
                bind: 'plannedQuantity',
                title: 'Planned quantity',
                isReadOnly: true,
                size: 'small',
                scale: 3,
            }),
            ui.nestedFields.numeric<TimeTracking, CustomWorkOrderOperationResource>({
                bind: 'completedQuantity',
                title: 'Completed quantity',
                isReadOnly: true,
                size: 'small',
                scale: 3,
            }),
            ui.nestedFields.technical<TimeTracking, CustomWorkOrderOperationResource>({
                bind: 'completedSetupTime',
            }),
            ui.nestedFields.technical<TimeTracking, CustomWorkOrderOperationResource>({
                bind: 'completedRunTime',
            }),
            ui.nestedFields.reference<TimeTracking, CustomWorkOrderOperationResource, DetailedResource>({
                bind: 'expectedResource',
                title: 'Actual resource',
                node: '@sage/xtrem-master-data/DetailedResource',
                size: 'small',
                valueField: 'id',
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ title: 'Code', bind: 'id' }),
                    ui.nestedFields.text({ title: 'Description', bind: 'description' }),
                    ui.nestedFields.text({ title: 'Type', bind: '_constructor' }),
                ],
                filter(rowData) {
                    return { site: { _id: { _eq: rowData.workOrderOperation.workOrder.site._id } } };
                },
                onChange(_id, rowData) {
                    this.selectRow(rowData._id);
                    this.workOrderOperations.setRecordValue(rowData);
                },
            }),
            ui.nestedFields.numeric<TimeTracking, CustomWorkOrderOperationResource>({
                bind: 'actualQuantity',
                title: 'Actual quantity',
                size: 'small',
                scale: 3,
                async onChange(_id, rowData) {
                    this.selectRow(rowData._id);
                    await this.verifyCompletedQuantity(
                        rowData.workOrderOperation.workOrder._id,
                        rowData.workOrderOperation.operationNumber,
                        rowData.actualQuantity,
                    );
                },
                isReadOnly: (_value, rowData) =>
                    !rowData?.isResourceQuantity && !!rowData?.workOrderOperation?.operationNumber,
            }),

            ui.nestedFields.numeric({
                bind: 'expectedSetupTime',
                title: 'Planned setup time',
                size: 'small',
                isReadOnly: true,
                postfix: (_rowId, rowData) => rowData?.setupTimeUnit?.symbol ?? '',
                scale: (_rowId, rowData) => rowData?.setupTimeUnit?.decimalDigits ?? 0,
            }),

            ui.nestedFields.numeric({
                bind: 'actualSetupTime',
                title: 'Actual setup time',
                size: 'small',
                validation: val => common.valueMustBePositive(val),
                onChange(_id, rowData) {
                    this.selectRow(rowData._id);
                },
                postfix: (_rowId, rowData) => rowData?.setupTimeUnit?.symbol ?? '',
                scale: (_rowId, rowData) => rowData?.setupTimeUnit?.decimalDigits ?? 2,
            }),
            ui.nestedFields.numeric({
                bind: 'expectedRunTime',
                title: 'Planned run time',
                size: 'small',
                isReadOnly: true,
                postfix: (_rowId, rowData) => rowData?.runTimeUnit?.symbol ?? '',
                scale: (_rowId, rowData) => rowData?.runTimeUnit?.decimalDigits ?? 0,
            }),
            ui.nestedFields.numeric({
                bind: 'actualRunTime',
                title: 'Actual run time',
                size: 'small',
                validation: val => common.valueMustBePositive(val),
                onChange(_id, rowData) {
                    this.selectRow(rowData._id);
                },
                postfix: (_rowId, rowData) => rowData?.runTimeUnit?.symbol ?? '',
                scale: (_rowId, rowData) => rowData?.runTimeUnit?.decimalDigits ?? 0,
            }),
            ui.nestedFields.checkbox({
                bind: 'isCompleted',
                title: 'Completed',
                onChange(_id, rowData) {
                    this.selectRow(rowData._id);
                },
            }),
            ui.nestedFields.reference<TimeTracking, WorkOrderOperationResource, CapabilityLevel>({
                bind: { workOrderOperation: { minCapabilityLevel: true } },
                title: 'Capability level',
                node: '@sage/xtrem-master-data/CapabilityLevel',
                isReadOnly: (_value, row) => (row?.workOrderOperation?.operationNumber ?? 0) > 0,
                size: 'small',
                valueField: 'id',
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ title: 'Code', bind: 'id' }),
                    ui.nestedFields.text({ title: 'Description', bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'level' }),
                ],
                onChange(_id, rowData) {
                    this.selectRow(rowData._id);
                },
            }),
            ui.nestedFields.technical({ bind: { workOrderOperation: { workOrder: { site: { _id: true } } } } }),
            ui.nestedFields.technical({ bind: 'isResourceQuantity' }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.reference<TimeTracking, WorkOrderOperationResource, UnitOfMeasure>({
                title: 'Setup time unit',
                bind: 'setupTimeUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                isExcludedFromMainField: true,
                minLookupCharacters: 1,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ title: 'Description', bind: 'description' }),
                ],
            }),
            ui.nestedFields.reference<TimeTracking, WorkOrderOperationResource, UnitOfMeasure>({
                title: 'Run time unit',
                bind: 'runTimeUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ title: 'Description', bind: 'description' }),
                ],
            }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Dimensions',
                async onClick(_rowId, rowItem) {
                    await utils.applyPanelToLineIfChanged(
                        this.workOrderOperations,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            { documentLine: rowItem as financeInterfaces.BaseDocumentLineWithAnalytical },
                            { editable: ['pending', 'inProgress'].includes(rowItem?.status ?? '') },
                        ),
                    );
                },
            },
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isHidden: rowId => Number(rowId) > 0,
                onClick(rowId: string) {
                    this.workOrderOperations.removeRecord(rowId);
                },
            },
        ],
        fieldActions() {
            return [this.addOperation, this.defaultDimension];
        },
        onChange() {
            this.createTrackings.isDisabled = !this.workOrderOperations.selectedRecords.length;
        },
        onRowSelected() {
            this.createTrackings.isDisabled = false;
        },
        onRowUnselected() {
            if (!this.workOrderOperations.selectedRecords.length) {
                this.createTrackings.isDisabled = true;
            }
        },
    })
    workOrderOperations: ui.fields.Table<CustomWorkOrderOperationResource>;

    @ui.decorators.pageAction<TimeTracking>({
        icon: 'arrow_right',
        title: 'Set default dimensions',
        // isDisabled() {
        //     return !dimensionPanelHelpers.isDefaultDimensionActionActive(this.$.recordId, this.$.isDirty);
        // },
        async onClick() {
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.workOrderOperations,
                this._defaultDimensionsAttributes,
            );
        },
    })
    defaultDimension: ui.PageAction;

    async search() {
        const results = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrderOperationResource')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            status: true,
                            actualSetupTime: true,
                            actualRunTime: true,
                            expectedRunTime: true,
                            expectedSetupTime: true,
                            setupTimeUnit: {
                                _id: true,
                                id: true,
                                symbol: true,
                                name: true,
                                description: true,
                                decimalDigits: true,
                            },
                            runTimeUnit: {
                                _id: true,
                                id: true,
                                symbol: true,
                                name: true,
                                description: true,
                                decimalDigits: true,
                            },
                            isResourceQuantity: true,
                            workOrderOperation: {
                                _id: true,
                                operationNumber: true,
                                name: true,
                                status: true,
                                plannedQuantity: true,
                                completedQuantity: true,
                                workOrder: {
                                    _id: true,
                                    number: true,
                                    site: { _id: true, id: true },
                                    routingCode: { item: { id: true, name: true, description: true } },
                                },
                                minCapabilityLevel: {
                                    _id: true,
                                    id: true,
                                    name: true,
                                    description: true,
                                    level: true,
                                },
                            },
                            resource: {
                                _id: true,
                                id: true,
                                name: true,
                                description: true,
                                site: { _id: true, id: true },
                            },
                            resources: {
                                query: {
                                    edges: {
                                        node: {
                                            resource: {
                                                _id: true,
                                                id: true,
                                                name: true,
                                                description: true,
                                                site: { _id: true, id: true },
                                            },
                                        },
                                    },
                                },
                            },
                            storedAttributes: true,
                            storedDimensions: true,
                            computedAttributes: true,
                        },
                        { filter: this.getFilter(), first: 500 },
                    ),
                )
                .execute(),
        );
        this.workOrderOperations.value = results.map(values => {
            const resource = values.resources.length ? values.resources[0].resource : values.resource;
            return {
                ...values,
                expectedResource: resource,
                plannedQuantity: values.workOrderOperation.plannedQuantity,
                completedQuantity: values.workOrderOperation.completedQuantity,
                completedSetupTime: values.actualSetupTime,
                completedRunTime: values.actualRunTime,
                actualSetupTime: '0', // can't this cause a inversion ?
                actualRunTime: '0',
                isCompleted: values.workOrderOperation.status === 'completed',
            };
        });
        this._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();

        this.$.loader.isHidden = true;
    }

    getFilter(): Filter<WorkOrderOperationResource> {
        const filter = {
            workOrderOperation: { status: { _nin: ['excluded', 'included'] }, workOrder: this.getWorkOrderFilter() },
        };
        if (this.operationResourceStatus.value) {
            merge(filter, { status: { _eq: this.operationResourceStatus.value } });
        } else {
            merge(filter, { status: { _in: ['pending', 'inProgress', 'completed'] } });
        }
        return filter;
    }

    selectRow(recordId: string) {
        this.workOrderOperations.selectRecord(recordId);
    }

    private async setPageValues(searchValues: {
        site: ExtractEdgesPartial<Site> | null;
        itemFrom: ExtractEdgesPartial<BillOfMaterial>;
        itemTo: ExtractEdgesPartial<BillOfMaterial>;
        woNumberFrom: ExtractEdgesPartial<WorkOrder>;
        woNumberTo: ExtractEdgesPartial<WorkOrder>;
        woStartFrom: string;
        woStartTo: string;
        woStatus: string;
    }) {
        this.site.value = searchValues.site;
        this.itemFrom.value = searchValues.itemFrom;
        this.itemTo.value = searchValues.itemTo;
        this.woNumberFrom.value = searchValues.woNumberFrom;
        this.woNumberTo.value = searchValues.woNumberTo;
        this.woStartFrom.value = searchValues.woStartFrom;
        this.woStartTo.value = searchValues.woStartTo;
        this.operationResourceStatus.value = searchValues.woStatus;
        this.$.loader.isHidden = false;
        await this.search();
        this.$.loader.isHidden = true;
    }

    private async verifyCompletedQuantity(
        workOrder: number,
        operationNumber: number,
        completedQuantity: number,
    ): Promise<void> {
        const previousCompletedQuantity = await this.previouslyCompleted(workOrder, operationNumber);

        if (previousCompletedQuantity && completedQuantity > previousCompletedQuantity) {
            await this.$.dialog.message(
                'warn',
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__time_tracking__on_completed_quantity_changed_warn_title',
                    'Completed quantity',
                ),
                ui.localize(
                    '@sage/xtrem-manufacturing/pages__time_tracking__on_completed_quantity_changed_warn',
                    'Quantity completed is greater than that of the previous operation',
                ),
            );
        }
    }

    async previouslyCompleted(workOrderSysId: number, operationNumber: number): Promise<number> {
        const [lastOperation] = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkOrderOperation')
                .query(
                    ui.queryUtils.edgesSelector(
                        { completedQuantity: true },
                        {
                            filter: { workOrder: { _id: workOrderSysId }, operationNumber: { _lt: operationNumber } },
                            last: 1,
                        },
                    ),
                )
                .execute(),
        );
        return +(lastOperation?.completedQuantity ?? 0);
    }
}
