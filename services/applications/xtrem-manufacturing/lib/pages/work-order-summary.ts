import type { ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import { withoutEdges } from '@sage/xtrem-client';
import { date } from '@sage/xtrem-date-time';
import type {
    GraphApi,
    WorkInProgressCost,
    WorkOrderReleasedItem,
    WorkOrderStatus,
} from '@sage/xtrem-manufacturing-api';
import type { Currency, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import type { decimal } from '@sage/xtrem-shared';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import * as PillColor from '../client-functions/pill-color';

interface WorkOrderSummaryLine extends WorkOrderReleasedItem {
    currentMonthQuantity: decimal;
    previousMonthQuantity: decimal;
    currentMonthCost: decimal;
    previousMonthCost: decimal;
    itemId: string;
    workOrderId: string;
    itemName: string;
    workOrderNumber: string;
    status: WorkOrderStatus;
    siteName: string;
    currency: Currency;
    site: Site;
    unit: UnitOfMeasure;
}
@ui.decorators.page<WorkOrderSummary>({
    title: 'Work order summary',
    mode: 'default',
    node: '@sage/xtrem-manufacturing/WorkOrder',
    module: 'xtrem-manufacturing',
    isTransient: true,
    headerSection() {
        return this.headerSection;
    },
    async onLoad() {
        if (this.$.queryParameters.currentMonth) {
            await this.populateWorkOrderTable(this.$.queryParameters.currentMonth as number);
        }
        this.isCostView = JSON.parse(this.$.queryParameters.isCostView as string);
        if (this.$.queryParameters.currency) {
            const currency = withoutEdges(
                await this.$.graph
                    .node('@sage/xtrem-master-data/Currency')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                _id: true,
                                id: true,
                                name: true,
                                symbol: true,
                                decimalDigits: true,
                            },
                            {
                                filter: { _id: this.$.queryParameters.currency },
                                first: 1,
                            },
                        ),
                    )
                    .execute(),
            );
            if (currency.length) {
                this.currency.value = currency.at(0) ?? null;
            }
        }
        if (this.$.queryParameters.currentMonthCost) {
            this.currentCostProduced.value = this.$.queryParameters.currentMonthCost as number;
        }
        if (this.$.queryParameters.previousMonthCost) {
            this.previousCostProduced.value = this.$.queryParameters.previousMonthCost as number;
        }
    },
})
export class WorkOrderSummary extends ui.Page<GraphApi> {
    workOrderFilter: Filter<WorkInProgressCost> = { _and: [{}] };

    isCostView = true;

    @ui.decorators.section<WorkOrderSummary>({
        title: 'Header section',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<WorkOrderSummary>({
        isTitleHidden: true,
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.tile<WorkOrderSummary>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.textField<WorkOrderSummary>({
        parent() {
            return this.headerBlock;
        },
        title: 'ID',
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.aggregateField<WorkOrderSummary, WorkOrderSummaryLine>({
        parent() {
            return this.tileContainer;
        },
        title: 'Work order count',
        bind: 'releasedItems',
        aggregateOn: 'currentMonthQuantity',
        aggregationMethod: 'sum',
        isTransient: true,
    })
    workOrderCount: ui.fields.Aggregate;

    @ui.decorators.aggregateField<WorkOrderSummary, WorkOrderSummaryLine>({
        parent() {
            return this.tileContainer;
        },
        isTransient: true,
        aggregateOn: 'currentMonthQuantity',
        aggregationMethod: 'sum',
        title: 'Current month quantity',
        bind: 'releasedItems',
        isHidden() {
            return this.isCostView;
        },
    })
    currentQuantityProduced: ui.fields.Aggregate;

    @ui.decorators.aggregateField<WorkOrderSummary, WorkOrderSummaryLine>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'previousMonthQuantity',
        aggregationMethod: 'sum',
        title: 'Previous month quantity',
        bind: 'releasedItems',
        isHidden() {
            return this.isCostView;
        },
    })
    previousQuantityProduced: ui.fields.Aggregate;

    @ui.decorators.numericField<WorkOrderSummary>({
        parent() {
            return this.tileContainer;
        },
        title: 'Quantity change',
        isHidden() {
            return this.isCostView;
        },
    })
    quantityProducedDifference: ui.fields.Numeric;

    @ui.decorators.aggregateField<WorkOrderSummary, WorkOrderSummaryLine>({
        parent() {
            return this.tileContainer;
        },
        isTransient: true,
        aggregateOn: 'currentMonthCost',
        aggregationMethod: 'sum',
        title: 'Current month cost',
        bind: 'releasedItems',
        isHidden() {
            return !this.isCostView;
        },
        unit() {
            return this.currency.value;
        },
    })
    currentCostProduced: ui.fields.Aggregate;

    @ui.decorators.aggregateField<WorkOrderSummary, WorkOrderSummaryLine>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'previousMonthCost',
        aggregationMethod: 'sum',
        title: 'Previous month cost',
        bind: 'releasedItems',
        isHidden() {
            return !this.isCostView;
        },
        unit() {
            return this.currency.value;
        },
    })
    previousCostProduced: ui.fields.Aggregate;

    @ui.decorators.numericField<WorkOrderSummary>({
        parent() {
            return this.tileContainer;
        },
        title: 'Cost change',
        isHidden() {
            return !this.isCostView;
        },
        unit() {
            return this.currency.value;
        },
    })
    costProducedDifference: ui.fields.Numeric;

    @ui.decorators.section<WorkOrderSummary>({
        title: 'Work orders',
    })
    workOrderSection: ui.containers.Section;

    @ui.decorators.referenceField<WorkOrderSummary>({
        parent() {
            return this.headerBlock;
        },
        node: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        helperTextField: 'id',
        title: 'Currency for cost values',
        minLookupCharacters: 1,
        isHidden: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.numeric({ bind: 'decimalDigits', isHidden: true }),
        ],
    })
    currency: ui.fields.Reference;

    @ui.decorators.tableField<WorkOrderSummary, WorkOrderSummaryLine>({
        node: '@sage/xtrem-manufacturing/WorkOrderReleasedItem',
        parent() {
            return this.workOrderSection;
        },
        isTitleHidden: true,
        isChangeIndicatorDisabled: true,
        isTransient: true,
        canFilter: false,
        orderBy: { _id: 1 },
        canSelect: false,
        columns: [
            ui.nestedFields.text({ bind: 'workOrderId', isHidden: true }),
            ui.nestedFields.text({ bind: 'itemId', isHidden: true }),
            ui.nestedFields.link({
                bind: 'workOrderNumber',
                isTransient: true,
                title: 'Work order',
                page: '@sage/xtrem-manufacturing/WorkOrder',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData.workOrderId,
                    };
                },
            }),
            ui.nestedFields.text({ bind: 'siteName', title: 'Site' }),
            ui.nestedFields.link({
                bind: 'itemName',
                isTransient: true,
                title: 'Item',
                page: '@sage/xtrem-master-data/Item',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData.itemId,
                    };
                },
            }),
            ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-manufacturing/ReleasedItemStatus',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('ReleasedItemStatus', rowData.status),
            }),
            ui.nestedFields.numeric({
                bind: 'releasedQuantity',
                title: 'Required quantity',
                unit: (_id, rowData) => rowData?.unit,
                unitMode: 'unitOfMeasure',
                min: 0,
            }),
            ui.nestedFields.numeric({
                bind: 'remainingQuantity',
                isTransient: true,
                title: 'Remaining quantity',
                unit: (_id, rowData) => rowData?.unit,
                unitMode: 'unitOfMeasure',
                min: 0,
            }),
            ui.nestedFields.numeric({
                bind: 'currentMonthQuantity',
                title: 'Current month quantity',
                unit: (_id, rowData) => rowData?.unit,
                unitMode: 'unitOfMeasure',
                min: 0,
                isHidden() {
                    return this.isCostView;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'previousMonthQuantity',
                title: 'Previous month quantity',
                unit: (_id, rowData) => rowData?.unit,
                unitMode: 'unitOfMeasure',
                min: 0,
                isHidden() {
                    return this.isCostView;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'currentMonthCost',
                title: 'Current month cost',
                unit: (_id, rowData) => rowData?.currency,
                min: 0,
                isHidden() {
                    return !this.isCostView;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'previousMonthCost',
                isTransient: true,
                title: 'Previous month cost',
                unit: (_id, rowData) => rowData?.currency,
                min: 0,
                isHidden() {
                    return !this.isCostView;
                },
            }),
            ui.nestedFields.reference({
                bind: 'document',
                isHidden: true,
                valueField: 'number',
                node: '@sage/xtrem-manufacturing/WorkOrder',
            }),
            ui.nestedFields.reference({
                bind: 'releasedItem',
                isHidden: true,
                valueField: 'id',
                node: '@sage/xtrem-master-data/Item',
            }),
            ui.nestedFields.reference({
                bind: 'currency',
                isHidden: true,
                valueField: 'id',
                node: '@sage/xtrem-master-data/Currency',
                columns: [ui.nestedFields.numeric({ bind: 'decimalDigits' }), ui.nestedFields.text({ bind: 'symbol' })],
            }),
            ui.nestedFields.reference({
                bind: 'unit',
                isHidden: true,
                valueField: 'symbol',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                columns: [
                    ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.text({ bind: 'id' }),
                ],
            }),
        ],
    })
    releasedItems: ui.fields.Table<WorkOrderSummaryLine>;

    private async populateWorkOrderTable(currentMonth: number) {
        const today = date.today();
        const monthCurrent = currentMonth ?? today.month;
        const firstDayOfStartMonth =
            monthCurrent < 2 ? new Date(today.year - 1, 11, 1, 23) : new Date(today.year, monthCurrent - 2, 1, 23);
        const endDayOfCurrentMonth = new Date(today.year, monthCurrent, 0, 23);

        const dateFilter: Filter<WorkInProgressCost> = {
            _and: [
                {
                    effectiveDate: {
                        _gte: firstDayOfStartMonth.toISOString().substring(0, 10),
                    },
                },
                {
                    effectiveDate: {
                        _lte: endDayOfCurrentMonth.toISOString().substring(0, 10),
                    },
                },
            ],
        };
        const costFilter: Filter<WorkInProgressCost> = {
            _and: [
                {
                    type: {
                        _in: ['materialTracking', 'setupTimeTracking', 'runTimeTracking', 'workOrderIndirectCost'],
                    },
                },
            ],
        };

        const quantityFilter: Filter<WorkInProgressCost> = {
            _and: [
                {
                    type: {
                        _in: ['materialTracking', 'setupTimeTracking', 'runTimeTracking', 'workOrderIndirectCost'],
                    },
                },
            ],
        };
        if (dateFilter._and) {
            costFilter._and?.push(...dateFilter._and);
            quantityFilter._and?.push(...dateFilter._and);
        }

        const quantities = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkInProgressCost')
                .aggregate.query(
                    ui.queryUtils.edgesSelector(
                        {
                            group: {
                                workOrder: {
                                    number: { _by: 'value' },
                                },
                                effectiveDate: { _by: 'month' },
                            },
                            values: {
                                quantity: {
                                    sum: true,
                                },
                            },
                        },
                        { filter: quantityFilter },
                    ),
                )
                .execute(),
        );

        const costs = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkInProgressCost')
                .aggregate.query(
                    ui.queryUtils.edgesSelector(
                        {
                            group: {
                                workOrder: {
                                    number: { _by: 'value' },
                                },
                                effectiveDate: { _by: 'month' },
                            },
                            values: {
                                quantity: {
                                    sum: true,
                                },
                                amount: {
                                    sum: true,
                                },
                            },
                        },
                        { filter: costFilter },
                    ),
                )
                .execute(),
        );

        this.workOrderFilter._and.push(...dateFilter._and);
        this.workOrderFilter._and.push({
            type: {
                _in: [
                    'productionTracking',
                    'materialTracking',
                    'setupTimeTracking',
                    'runTimeTracking',
                    'workOrderIndirectCost',
                ],
            },
        });

        const workOrders = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-manufacturing/WorkInProgressCost')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            workOrder: {
                                _id: true,
                                number: true,
                                status: true,
                                productionItem: {
                                    releasedItem: {
                                        _id: true,
                                        id: true,
                                        name: true,
                                        stockUnit: {
                                            _id: true,
                                            id: true,
                                            symbol: true,
                                            decimalDigits: true,
                                        },
                                    },
                                    releasedQuantity: true,
                                    remainingQuantity: true,
                                    lineStatus: true,
                                },
                                site: {
                                    _id: true,
                                    id: true,
                                    name: true,
                                    legalCompany: {
                                        id: true,
                                        currency: {
                                            _id: true,
                                            id: true,
                                            symbol: true,
                                            decimalDigits: true,
                                        },
                                    },
                                },
                            },
                            effectiveDate: true,
                            quantity: true,
                            amount: true,
                        },
                        { filter: this.workOrderFilter },
                    ),
                )
                .execute(),
        );

        if (costs || quantities) {
            let currentCost = 0;
            let previousCost = 0;
            let currentQuantity = 0;
            let previousQuantity = 0;
            this.releasedItems.value = [];
            costs.forEach(cost => {
                const workOrder = workOrders.find(order => order.workOrder.number === cost.group.workOrder.number);

                const row: ExtractEdgesPartial<WorkOrderSummaryLine> =
                    this.releasedItems.value.find(line => {
                        if (line.document) {
                            return line.document.number === cost.group.workOrder.number;
                        }
                        return false;
                    }) || {};

                let { currentMonthCost } = row;
                let { previousMonthCost } = row;
                if (Number(cost.group.effectiveDate.substring(5, 7)) === monthCurrent) {
                    currentMonthCost = Number(cost.values.amount?.sum);
                    currentCost += currentMonthCost || 0;
                } else {
                    previousMonthCost = Number(cost.values.amount?.sum);
                    previousCost += previousMonthCost || 0;
                }
                if (workOrder) {
                    row.itemId = workOrder.workOrder.productionItem.releasedItem._id;
                    row.workOrderId = workOrder.workOrder._id;
                    row.workOrderNumber = workOrder.workOrder.number;
                    row.siteName = workOrder.workOrder.site.name;
                    row.itemName = workOrder.workOrder.productionItem.releasedItem.name;
                    row.status = workOrder.workOrder.status;
                    row.releasedQuantity = workOrder.workOrder.productionItem.releasedQuantity;
                    row.remainingQuantity = workOrder.workOrder.productionItem.remainingQuantity;
                    row.currentMonthCost = currentMonthCost;
                    row.previousMonthCost = previousMonthCost;
                    row.document = workOrder.workOrder;
                    row.releasedItem = workOrder.workOrder.productionItem.releasedItem;
                    row.currency = workOrder.workOrder.site.legalCompany.currency;
                    row.unit = workOrder.workOrder.productionItem.releasedItem.stockUnit;

                    this.releasedItems.addOrUpdateRecordValue(row);
                }
            });
            quantities.forEach(quantity => {
                const workOrder = workOrders.find(order => order.workOrder.number === quantity.group.workOrder.number);
                const row: ExtractEdgesPartial<WorkOrderSummaryLine> =
                    this.releasedItems.value.find(line => {
                        if (line.document) {
                            return line.document.number === quantity.group.workOrder.number;
                        }
                        return false;
                    }) || {};

                let { currentMonthQuantity } = row;
                let { previousMonthQuantity } = row;
                if (workOrder) {
                    if (Number(quantity.group.effectiveDate.substring(5, 7)) === monthCurrent) {
                        currentMonthQuantity = Number(quantity.values.quantity?.sum);
                        currentQuantity += currentMonthQuantity || 0;
                    } else {
                        previousMonthQuantity = Number(quantity.values.quantity?.sum);
                        previousQuantity += previousMonthQuantity || 0;
                    }

                    row.itemId = workOrder.workOrder.productionItem.releasedItem._id;
                    row.workOrderId = workOrder.workOrder._id;
                    row.workOrderNumber = workOrder.workOrder.number;
                    row.itemName = workOrder.workOrder.productionItem.releasedItem.name;
                    row.status = workOrder.workOrder.status;
                    row.releasedQuantity = workOrder.workOrder.productionItem.releasedQuantity;
                    row.remainingQuantity = workOrder.workOrder.productionItem.remainingQuantity;
                    row.currentMonthQuantity = currentMonthQuantity;
                    row.previousMonthQuantity = previousMonthQuantity;
                    row.document = workOrder.workOrder;
                    row.releasedItem = workOrder.workOrder.productionItem.releasedItem;
                    row.currency = workOrder.workOrder.site.legalCompany.currency;
                    row.unit = workOrder.workOrder.productionItem.releasedItem.stockUnit;

                    this.releasedItems.addOrUpdateRecordValue(row);
                }
            });
            // this.currentCostProduced.value = currentCost;
            // this.previousCostProduced.value = previousCost;
            this.currentQuantityProduced.value = currentQuantity;
            this.previousQuantityProduced.value = previousQuantity;
            this.workOrderCount.value = this.releasedItems.value.length;
            this.costProducedDifference.value = currentCost - previousCost;
            this.quantityProducedDifference.value = currentQuantity - previousQuantity;
        }
        this.$.setPageClean();
    }
}
