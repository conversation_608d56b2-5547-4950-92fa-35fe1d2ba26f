import { date } from '@sage/xtrem-date-time';
import * as ui from '@sage/xtrem-ui';

@ui.widgets.lineChart<ProductionCostTrend>({
    title: 'Production cost trend',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    settingsPage: '@sage/xtrem-manufacturing/ProductionCostTrendSettings',
    content() {
        const entries = this.$.data.xtremManufacturing.workInProgressCost.getCostProduced || [];
        return entries.map((month: any) => {
            // Month is 0 - 11 so need to minus 1
            const nameDate = new Date(date.today().year, month.month - 1, 1);
            const monthName = nameDate.toLocaleString('default', { month: 'long' });
            return {
                cost: Number(month.cost),
                month: monthName,
            };
        });
    },
    primaryAxis: {
        bind: 'month',
        title: 'Month',
    },
    secondaryAxes: [
        {
            bind: 'cost',
            title: 'Cost',
            tooltipContent(primaryValue) {
                return `${ui.localize(
                    '@sage/xtrem-manufacturing/widgets__production_cost_trend__cost_tooltip_title',
                    'Cost',
                )}: ${primaryValue.cost}`;
            },
            onClick(record) {
                const queryDate = date.parse(`01 ${record.month} ${date.today().year}`, undefined, 'DD MMMM YYYY');
                const filter = {
                    _filter: JSON.stringify({
                        _and: [
                            { effectiveDate: { _gte: queryDate.begOfMonth() } },
                            { effectiveDate: { _lte: queryDate.endOfMonth() } },
                        ],
                    }),
                };
                this.$.router.goTo('@sage/xtrem-manufacturing/WipTransactionInquiry', filter);
            },
        },
    ],
    getQuery() {
        return {
            xtremManufacturing: {
                workInProgressCost: {
                    getCostProduced: {
                        __args: {
                            currency: this.$.settings.productionCostCurrency ?? null,
                            reportMonths: this.$.settings.reportMonths ?? 2,
                        },
                        month: true,
                        cost: true,
                    },
                },
            },
        };
    },
})
export class ProductionCostTrend extends ui.widgets.AbstractWidget {}
