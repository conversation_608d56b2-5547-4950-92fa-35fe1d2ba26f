import * as ui from '@sage/xtrem-ui';

@ui.widgets.table<MadeItemReturnReasonAnalysis>({
    title: 'Production return reason analysis',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    settingsPage: '@sage/xtrem-manufacturing/ReturnAnalysisStartDateSetting',
    content() {
        if (!this.$.data?.xtremSales?.salesReturnRequestLine?.queryAggregate) {
            return [];
        }
        return this.$.data.xtremSales.salesReturnRequestLine.queryAggregate.edges.map(
            ({ node }: any, index: number) => ({
                _id: `${index}`,
                title: node.group.reason.name ?? 'No reason',
                titleRight: `${node.values._id.distinctCount}`,
                line2Right: `${Math.round((node.values._id.distinctCount / this.getTotalCount()) * 100)}%`,
            }),
        );
    },
    canSwitchViewMode: false,
    displayMode: 'card',
    canSelect: false,
    rowDefinition: {
        title: {
            title: 'Reason name',
            renderedAs: 'link',
            onClick(_id: string) {
                const node = this.getDataFromLineIndex(_id);
                this.$.router.goTo(
                    `@sage/xtrem-sales/SalesReturnRequest/${btoa(
                        JSON.stringify({
                            [ui.QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]: JSON.stringify({
                                lines: { _atLeast: 1, reason: { _id: node.group.reason._id } },
                            }),
                        }),
                    )}`,
                );
            },
        },
        titleRight: {
            title: 'Returns',
        },
        line2Right: {
            title: '',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const node = this.getDataFromLineIndex(_id);
                    const value = Math.round((node.values._id.distinctCount / this.getTotalCount()) * 100);
                    if (+value > 29) {
                        return 'warning';
                    }
                    if (+value > 19) {
                        return 'neutral';
                    }
                    if (+value > 9) {
                        return 'positive';
                    }
                    return 'negative';
                },
            },
        },
    },
    getQuery() {
        const filter: any = {};
        filter.item = { isManufactured: true };
        if (this.$.settings.startDate) {
            filter.document = { date: { _gte: JSON.parse(this.$.settings.startDate) } };
        }

        return {
            xtremSales: {
                salesReturnRequestLine: {
                    readAggregate: {
                        _id: { distinctCount: true },
                    },
                    queryAggregate: {
                        __args: {
                            filter: JSON.stringify(filter),
                        },
                        edges: {
                            node: {
                                group: {
                                    reason: {
                                        name: true,
                                        _id: true,
                                    },
                                },
                                values: {
                                    _id: {
                                        distinctCount: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        };
    },
})
export class MadeItemReturnReasonAnalysis extends ui.widgets.TableWidget {
    private getDataFromLineIndex(index: string): any {
        return this.$.data.xtremSales.salesReturnRequestLine.queryAggregate.edges[index].node;
    }

    private getTotalCount(): number {
        return this.$.data.xtremSales.salesReturnRequestLine.readAggregate._id.distinctCount;
    }
}
