import { date } from '@sage/xtrem-date-time';
import * as ui from '@sage/xtrem-ui';

@ui.widgets.table<WorkOrderDelay>({
    title: 'Work order delay',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    settingsPage: '@sage/xtrem-manufacturing/WorkOrderDelaySettings',
    content() {
        if (!this.$.data?.xtremManufacturing?.workOrder?.query?.edges) {
            return [];
        }
        return this.$.data.xtremManufacturing.workOrder.query.edges.map(({ node, cursor }: any, index: number) => ({
            _id: `${node._id}`,
            index: `${index}`,
            title: node.number,
            line2: ui.localizeEnumMember('@sage/xtrem-manufacturing/WorkOrderStatus', node.status),
            titleRight: node.productionItem.releasedItem.name,
            line2Right: `${this.getWorkOrderDelayValue(node._id)}`,
            cursor,
        }));
    },
    canSwitchViewMode: true,
    displayMode: 'card',
    canSelect: false,
    dataDropdownMenu: {
        orderBy: {
            number: { title: 'Sort by number' },
            endDate: { title: 'Sort by end date' },
            status: { title: 'Sort by status' },
        },
    },
    rowDefinition: {
        title: {
            title: 'Work order',
            renderedAs: 'link',
            onClick(_id: string) {
                this.$.router.goTo('@sage/xtrem-manufacturing/WorkOrder', { _id });
            },
        },
        titleRight: { title: 'Released item' },
        line2: {
            title: 'Status',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const node = this.getDataFromLineIndex(_id);
                    if (node.status === 'completed') {
                        return 'positive';
                    }
                    if (node.status === 'inProgress') {
                        return 'warning';
                    }
                    if (node.status === 'pending') {
                        return 'negative';
                    }
                    return 'neutral';
                },
            },
        },
        line2Right: {
            title: 'Delay',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const delay = JSON.parse(this.$.settings.delay || '0');
                    const value = this.getWorkOrderDelayValue(_id);
                    if (+value > delay) {
                        return 'positive';
                    }
                    if (+value > 0 && value <= delay) {
                        return 'warning';
                    }
                    if (+value < 0) {
                        return 'negative';
                    }
                    return 'neutral';
                },
            },
        },
    },
    getQuery(args) {
        const filter: any = {};
        if (this.$.settings.statusList) {
            filter.status = { _in: JSON.parse(this.$.settings.statusList) };
        } else {
            filter.status = { _in: ['pending', 'inProgress'] };
        }
        if (this.$.settings.delay) {
            const delay = JSON.parse(this.$.settings.delay || '0');
            const end = date.today().addDays(delay);
            filter.endDate = { _lte: `${end}` };
        } else {
            filter.endDate = { _lte: `${date.today()}` };
        }

        const orderBy: any = {};
        const { dataOptions } = this.$.options;
        if (!dataOptions?.orderBy || dataOptions?.orderBy === 'endDate') {
            orderBy.endDate = 1;
        }

        if (dataOptions?.orderBy === 'number') {
            orderBy.number = 1;
        }

        if (dataOptions?.orderBy === 'status') {
            orderBy.status = 1;
        }

        return {
            xtremManufacturing: {
                workOrder: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify(orderBy),
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            cursor: true,
                            node: {
                                _id: true,
                                number: true,
                                endDate: true,
                                status: true,
                                productionItem: {
                                    releasedItem: {
                                        id: true,
                                        name: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        };
    },
})
export class WorkOrderDelay extends ui.widgets.TableWidget {
    private getWorkOrderDelayValue(_id: string) {
        const node = this.getDataFromLineIndex(_id);
        return date.fromJsDate(new Date(node.endDate)).daysDiff(date.today());
    }

    private getDataFromLineIndex(_id: string): any {
        return this.$.data.xtremManufacturing.workOrder.query.edges.find((edge: any) => edge.node._id === _id).node;
    }
}
