import { date } from '@sage/xtrem-date-time';
import * as ui from '@sage/xtrem-ui';

interface CostProduced {
    month: number;
    cost: number;
}

@ui.widgets.indicatorTile<CostProducedPercentage>({
    title: 'Production cost percentage',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    settingsPage: '@sage/xtrem-manufacturing/ProductionCostCurrencySettings',

    color() {
        const percentage = CostProducedPercentage.percentageCalculation(
            this.$.data?.xtremManufacturing?.workInProgressCost?.getCostProduced ?? [],
        );
        return percentage < 100 ? ui.tokens.colorsSemanticPositive500 : ui.tokens.colorsSemanticNegative500;
    },
    icon() {
        const percentage = CostProducedPercentage.percentageCalculation(
            this.$.data?.xtremManufacturing?.workInProgressCost?.getCostProduced ?? [],
        );
        return percentage > 100 ? 'incline' : 'decline';
    },
    value() {
        const percentage = CostProducedPercentage.percentageCalculation(
            this.$.data?.xtremManufacturing?.workInProgressCost?.getCostProduced ?? [],
        );
        return `${String(percentage)}%`;
    },
    getQuery() {
        return {
            xtremManufacturing: {
                workInProgressCost: {
                    getCostProduced: {
                        __args: { currency: this.$.settings.productionCostCurrency ?? null, reportMonths: 2 },
                        month: true,
                        cost: true,
                    },
                },
            },
        };
    },
    onClick() {
        const { currentMonth, previousMonth } = CostProducedPercentage.getCurentAndPreviousMonthsCosts(
            this.$.data?.xtremManufacturing?.workInProgressCost?.getCostProduced ?? [],
        );
        if (this.$.settings.productionCostCurrency) {
            const { _id } = JSON.parse(this.$.settings.productionCostCurrency as string);
            this.$.router.goTo('@sage/xtrem-manufacturing/WorkOrderSummary', {
                currentMonth: date.today().month,
                isCostView: 'true',
                currency: _id ?? null,
                currentMonthCost: currentMonth,
                previousMonthCost: previousMonth,
            });
        }
    },
})
export class CostProducedPercentage extends ui.widgets.AbstractWidget {
    private static getCurentAndPreviousMonthsCosts(costProduced: Array<CostProduced>) {
        let previousMonth = 0;
        let currentMonth = 0;
        const todayMonth = date.today().month;
        const previousMonthObject = costProduced.find((month: CostProduced) => Number(month.month) === todayMonth - 1);
        if (previousMonthObject) {
            previousMonth = previousMonthObject.cost;
        }
        const currentMonthObject = costProduced.find((month: CostProduced) => Number(month.month) === todayMonth);
        if (currentMonthObject) {
            currentMonth = currentMonthObject.cost;
        }
        return { currentMonth, previousMonth };
    }

    private static percentageCalculation(costProduced: Array<CostProduced>) {
        const { currentMonth, previousMonth } = this.getCurentAndPreviousMonthsCosts(costProduced);
        if (previousMonth > 0 || currentMonth > 0) {
            const percentage =
                Math.round((Number(previousMonth) ? (Number(currentMonth) / Number(previousMonth)) * 100 : 100) * 100) /
                100;
            return percentage;
        }
        return 0;
    }
}
