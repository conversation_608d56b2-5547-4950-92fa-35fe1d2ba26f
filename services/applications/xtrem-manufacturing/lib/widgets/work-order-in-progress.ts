import { toDecimal } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';

interface WorkOrderQueryResponseNode {
    _id: string;
    number: string;
    name: string;
    status: string;
    startDate: string;
    endDate: string;
    stockTransactionStatus: string;
    processCompletionPercentage: string;
    materialCompletionPercentage: string;
    productionCompletionPercentage: string;
    wipVariance: string;
}

interface WorkOrderQueryResponse {
    cursor: string;
    node: WorkOrderQueryResponseNode;
}

interface WorkOrderInProgressObject {
    _id: string;
    index: string;
    title: string;
    line2: string;
    titleRight: string;
    line2Right: string;
    line3: string;
    line3Right: string;
    endDate: string;
    processCompletionPercentage: string;
    materialCompletionPercentage: string;
    productionCompletionPercentage: string;
    cursor: string;
}
@ui.widgets.table<WorkOrderInProgress>({
    title: 'Work orders in progress',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    category: 'Period close validations',
    content() {
        const numberOfWorkOrders = this.$.data?.xtremManufacturing?.workOrder?.query?.edges?.length ?? 0;
        if (numberOfWorkOrders > 0) {
            const results = this.$.data.xtremManufacturing.workOrder.query.edges.map(
                ({ node, cursor }: WorkOrderQueryResponse, index: number): WorkOrderInProgressObject => {
                    return {
                        _id: node._id,
                        index: `${index}`,
                        title: node.number,
                        line2: ui.localizeEnumMember('@sage/xtrem-manufacturing/WorkOrderStatus', node.status),
                        titleRight: node.name,
                        line2Right: node.wipVariance,
                        line3: ui.localizeEnumMember(
                            '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                            node.stockTransactionStatus,
                        ),
                        line3Right: ui.formatDateToCurrentLocale(node.startDate),
                        endDate: ui.formatDateToCurrentLocale(node.endDate),
                        processCompletionPercentage: toDecimal(node.processCompletionPercentage).toFixed(4),
                        materialCompletionPercentage: toDecimal(node.materialCompletionPercentage).toFixed(4),
                        productionCompletionPercentage: toDecimal(node.productionCompletionPercentage).toFixed(4),
                        cursor,
                    };
                },
            );
            return results;
        }
        return [];
    },
    callToActions: {
        seeAll: {
            title: 'See all',
            onClick() {
                this.$.router.goTo('@sage/xtrem-manufacturing/WorkOrder');
            },
        },
    },
    canSwitchViewMode: true,
    displayMode: 'table',
    canSelect: false,
    dataDropdownMenu: {
        orderBy: {
            number: { title: 'Sort by number' },
            endDate: { title: 'Sort by end date' },
            status: { title: 'Sort by status' },
        },
    },
    rowDefinition: {
        title: {
            title: 'Work order',
            renderedAs: 'link',
            onClick(_id: string) {
                this.$.router.goTo('@sage/xtrem-manufacturing/WorkOrder', { _id });
            },
        },
        titleRight: { title: 'Released item' },
        line2: {
            title: 'Status',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const { status } = this.getDataFromLineIndex(_id);
                    switch (status) {
                        case 'completed':
                            return 'positive';
                        case 'inProgress':
                            return 'warning';
                        default:
                            return 'neutral';
                    }
                },
            },
        },
        line2Right: {
            title: 'WIP variance',
        },
        line3: {
            title: 'Stock transaction status',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const { stockTransactionStatus } = this.getDataFromLineIndex(_id);
                    switch (stockTransactionStatus) {
                        case 'completed':
                            return 'positive';
                        case 'inProgress':
                            return 'warning';
                        case 'error':
                            return 'negative';
                        default:
                            return 'neutral';
                    }
                },
            },
        },
        line3Right: { title: 'Start date' },
        endDate: { title: 'End date' },
        processCompletionPercentage: { title: 'Process completion percentage' },
        materialCompletionPercentage: { title: 'Material completion percentage' },
        productionCompletionPercentage: { title: 'Production completion percentage' },
    },
    getQuery(args) {
        const filter = { status: { _ne: 'closed' } };

        const orderBy: { endDate?: number; number?: number; status?: number } = {};
        switch (this.$.options.dataOptions?.orderBy) {
            case 'number':
                orderBy.number = -1;
                break;
            case 'endDate':
                orderBy.endDate = 1;
                break;
            case 'status':
                orderBy.status = 1;
                break;
            default:
                orderBy.endDate = 1;
        }

        return {
            xtremManufacturing: {
                workOrder: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify(orderBy),
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            cursor: true,
                            node: {
                                _id: true,
                                number: true,
                                name: true,
                                status: true,
                                startDate: true,
                                endDate: true,
                                stockTransactionStatus: true,
                                processCompletionPercentage: true,
                                materialCompletionPercentage: true,
                                productionCompletionPercentage: true,
                                wipVariance: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class WorkOrderInProgress extends ui.widgets.TableWidget {
    private getDataFromLineIndex(_id: string): WorkOrderQueryResponseNode {
        return this.$.data.xtremManufacturing.workOrder.query.edges.find(
            (edge: WorkOrderQueryResponse) => edge.node._id === _id,
        ).node;
    }
}
