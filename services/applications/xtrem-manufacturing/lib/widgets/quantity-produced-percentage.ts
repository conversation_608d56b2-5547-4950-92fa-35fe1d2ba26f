import { date } from '@sage/xtrem-date-time';
import * as ui from '@sage/xtrem-ui';

interface QuantityProduced {
    currentMonth: number;
    previousMonth: number;
}
@ui.widgets.indicatorTile<QuantityProducedPercentage>({
    title: 'Quantity produced percentage',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    color() {
        const percentage = QuantityProducedPercentage.percentageCalculation(
            this.$.data.xtremManufacturing.productionTrackingLine.getQuantityProduced,
        );
        return percentage > 100 ? ui.tokens.colorsSemanticPositive500 : ui.tokens.colorsSemanticNegative500;
    },
    icon() {
        const percentage = QuantityProducedPercentage.percentageCalculation(
            this.$.data.xtremManufacturing.productionTrackingLine.getQuantityProduced,
        );
        return percentage > 100 ? 'arrow_up' : 'arrow_down';
    },
    value() {
        const percentage = QuantityProducedPercentage.percentageCalculation(
            this.$.data.xtremManufacturing.productionTrackingLine.getQuantityProduced,
        );
        return `${String(percentage)}%`;
    },
    getQuery() {
        return {
            xtremManufacturing: {
                productionTrackingLine: {
                    getQuantityProduced: {
                        currentMonth: true,
                        previousMonth: true,
                    },
                },
            },
        };
    },
    onClick() {
        this.$.router.goTo('@sage/xtrem-manufacturing/WorkOrderSummary', {
            currentMonth: date.today().month,
            isCostView: 'false',
        });
    },
})
export class QuantityProducedPercentage extends ui.widgets.AbstractWidget {
    private static percentageCalculation(quantityProduced: QuantityProduced) {
        if (quantityProduced && (quantityProduced.previousMonth > 0 || quantityProduced.currentMonth > 0)) {
            const percentage =
                Math.round(
                    (Number(quantityProduced.previousMonth)
                        ? (Number(quantityProduced.currentMonth) / Number(quantityProduced.previousMonth)) * 100
                        : 100) * 100,
                ) / 100;
            return percentage;
        }
        return 0;
    }
}
