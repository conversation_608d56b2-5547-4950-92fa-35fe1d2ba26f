{"@sage/xtrem-manufacturing/activity__material_tracking__name": "Material tracking", "@sage/xtrem-manufacturing/activity__operation_tracking__name": "Operation tracking", "@sage/xtrem-manufacturing/activity__production_tracking__name": "Production tracking", "@sage/xtrem-manufacturing/activity__work_in_progress_cost__name": "Work in progress cost", "@sage/xtrem-manufacturing/activity__work_order__name": "Work order", "@sage/xtrem-manufacturing/activity__work_order_category__name": "Work order category", "@sage/xtrem-manufacturing/add-components-content": "You are about to add components to this work order.", "@sage/xtrem-manufacturing/add-components-title": "Add components", "@sage/xtrem-manufacturing/add-operations-content": "You are about to add operations to this work order.", "@sage/xtrem-manufacturing/add-operations-title": "Add operations", "@sage/xtrem-manufacturing/client_functions____resync_status_title": "Check and update status", "@sage/xtrem-manufacturing/client_functions__resync_status_message": "You are about to update the status.", "@sage/xtrem-manufacturing/client_functions_resync_status_continue": "Continue", "@sage/xtrem-manufacturing/close-dialog-cancel": "Cancel", "@sage/xtrem-manufacturing/close-dialog-content": "You are about to close an incomplete work order.", "@sage/xtrem-manufacturing/close-dialog-continue": "Continue", "@sage/xtrem-manufacturing/close-dialog-title": "Confirm closing", "@sage/xtrem-manufacturing/data_types__component_status_enum__name": "Component status enum", "@sage/xtrem-manufacturing/data_types__manufacturing_allocation_update_action_enum__name": "Manufacturing allocation update action enum", "@sage/xtrem-manufacturing/data_types__operation_status_enum__name": "Operation status enum", "@sage/xtrem-manufacturing/data_types__released_item_status_enum__name": "Released item status enum", "@sage/xtrem-manufacturing/data_types__work_in_progress_inquiry_status_enum__name": "Work in progress inquiry status enum", "@sage/xtrem-manufacturing/data_types__work_in_progress_status_enum__name": "Work in progress status enum", "@sage/xtrem-manufacturing/data_types__work_in_progress_type_enum__name": "Work in progress type enum", "@sage/xtrem-manufacturing/data_types__work_order_filtered_type_enum__name": "Work order filtered type enum", "@sage/xtrem-manufacturing/data_types__work_order_line_status_filtered_enum__name": "Work order line status filtered enum", "@sage/xtrem-manufacturing/data_types__work_order_line_status_filtered_for_tracking_enum__name": "Work order line status filtered for tracking enum", "@sage/xtrem-manufacturing/data_types__work_order_line_status_other_enum__name": "Work order line status other enum", "@sage/xtrem-manufacturing/data_types__work_order_released_item__name": "Work order released item", "@sage/xtrem-manufacturing/data_types__work_order_scheduling_status_enum__name": "Work order scheduling status enum", "@sage/xtrem-manufacturing/data_types__work_order_status_enum__name": "Work order status enum", "@sage/xtrem-manufacturing/data_types__work_order_type_enum__name": "Work order type enum", "@sage/xtrem-manufacturing/dialog_button_cancel": "Cancel", "@sage/xtrem-manufacturing/dialog_button_confirm": "Confirm", "@sage/xtrem-manufacturing/dialog_button_revert": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/enums__component_status__completed": "Completed", "@sage/xtrem-manufacturing/enums__component_status__excluded": "Excluded", "@sage/xtrem-manufacturing/enums__component_status__included": "Included", "@sage/xtrem-manufacturing/enums__component_status__inProgress": "In progress", "@sage/xtrem-manufacturing/enums__component_status__pending": "Pending", "@sage/xtrem-manufacturing/enums__manufacturing_allocation_update_action__create": "Create", "@sage/xtrem-manufacturing/enums__manufacturing_allocation_update_action__decrease": "Decrease", "@sage/xtrem-manufacturing/enums__manufacturing_allocation_update_action__delete": "Delete", "@sage/xtrem-manufacturing/enums__manufacturing_allocation_update_action__increase": "Increase", "@sage/xtrem-manufacturing/enums__manufacturing_allocation_update_action__reject": "Reject", "@sage/xtrem-manufacturing/enums__manufacturing_allocation_update_action__transfer": "Transfer", "@sage/xtrem-manufacturing/enums__operation_status__completed": "Completed", "@sage/xtrem-manufacturing/enums__operation_status__excluded": "Excluded", "@sage/xtrem-manufacturing/enums__operation_status__included": "Included", "@sage/xtrem-manufacturing/enums__operation_status__inProgress": "In progress", "@sage/xtrem-manufacturing/enums__operation_status__pending": "Pending", "@sage/xtrem-manufacturing/enums__released_item_status__completed": "Completed", "@sage/xtrem-manufacturing/enums__released_item_status__excluded": "Excluded", "@sage/xtrem-manufacturing/enums__released_item_status__included": "Included", "@sage/xtrem-manufacturing/enums__released_item_status__inProgress": "In progress", "@sage/xtrem-manufacturing/enums__released_item_status__pending": "Pending", "@sage/xtrem-manufacturing/enums__work_in_progress_inquiry_status__completed": "Completed", "@sage/xtrem-manufacturing/enums__work_in_progress_inquiry_status__draft": "Draft", "@sage/xtrem-manufacturing/enums__work_in_progress_inquiry_status__error": "Error", "@sage/xtrem-manufacturing/enums__work_in_progress_inquiry_status__inProgress": "In progress", "@sage/xtrem-manufacturing/enums__work_in_progress_status__error": "Error", "@sage/xtrem-manufacturing/enums__work_in_progress_status__pending": "Pending", "@sage/xtrem-manufacturing/enums__work_in_progress_status__posted": "Posted", "@sage/xtrem-manufacturing/enums__work_in_progress_type__materialTracking": "Material tracking", "@sage/xtrem-manufacturing/enums__work_in_progress_type__productionTracking": "Production tracking", "@sage/xtrem-manufacturing/enums__work_in_progress_type__runTimeTracking": "Run time tracking", "@sage/xtrem-manufacturing/enums__work_in_progress_type__setupTimeTracking": "Setup time tracking", "@sage/xtrem-manufacturing/enums__work_in_progress_type__workOrderActualCostAdjustment": "Work order actual cost adjustment", "@sage/xtrem-manufacturing/enums__work_in_progress_type__workOrderActualCostAdjustmentNonAbsorbed": "Work order actual cost adjustment non absorbed", "@sage/xtrem-manufacturing/enums__work_in_progress_type__workOrderIndirectCost": "Work order indirect cost", "@sage/xtrem-manufacturing/enums__work_in_progress_type__workOrderNegativeActualCostAdjustment": "Work order negative actual cost adjustment", "@sage/xtrem-manufacturing/enums__work_in_progress_type__workOrderNegativeActualCostAdjustmentNonAbsorbed": "Work order negative actual cost adjustment non absorbed", "@sage/xtrem-manufacturing/enums__work_in_progress_type__workOrderNegativeVariance": "Work order negative variance", "@sage/xtrem-manufacturing/enums__work_in_progress_type__workOrderVariance": "Work order variance", "@sage/xtrem-manufacturing/enums__work_order_filtered_type__firm": "Firm", "@sage/xtrem-manufacturing/enums__work_order_filtered_type__planned": "Planned", "@sage/xtrem-manufacturing/enums__work_order_line_status_filtered__included": "Included", "@sage/xtrem-manufacturing/enums__work_order_line_status_filtered__inProgress": "In progress", "@sage/xtrem-manufacturing/enums__work_order_line_status_filtered__pending": "Pending", "@sage/xtrem-manufacturing/enums__work_order_line_status_filtered_for_tracking__completed": "Completed", "@sage/xtrem-manufacturing/enums__work_order_line_status_filtered_for_tracking__inProgress": "In progress", "@sage/xtrem-manufacturing/enums__work_order_line_status_filtered_for_tracking__pending": "Pending", "@sage/xtrem-manufacturing/enums__work_order_line_status_other__completed": "Completed", "@sage/xtrem-manufacturing/enums__work_order_line_status_other__excluded": "Excluded", "@sage/xtrem-manufacturing/enums__work_order_scheduling_status__inProgress": "In progress", "@sage/xtrem-manufacturing/enums__work_order_scheduling_status__notManaged": "Not managed", "@sage/xtrem-manufacturing/enums__work_order_scheduling_status__notScheduled": "Not scheduled", "@sage/xtrem-manufacturing/enums__work_order_scheduling_status__scheduled": "Scheduled", "@sage/xtrem-manufacturing/enums__work_order_scheduling_status__toReschedule": "To reschedule", "@sage/xtrem-manufacturing/enums__work_order_status__closed": "Closed", "@sage/xtrem-manufacturing/enums__work_order_status__completed": "Completed", "@sage/xtrem-manufacturing/enums__work_order_status__costCalculated": "Cost calculated", "@sage/xtrem-manufacturing/enums__work_order_status__inProgress": "In progress", "@sage/xtrem-manufacturing/enums__work_order_status__pending": "Pending", "@sage/xtrem-manufacturing/enums__work_order_status__printed": "Printed", "@sage/xtrem-manufacturing/enums__work_order_type__firm": "Firm", "@sage/xtrem-manufacturing/enums__work_order_type__planned": "Planned", "@sage/xtrem-manufacturing/events/control__component__item_cannot_be_a_phantom": "Phantom items cannot be added to a work order.", "@sage/xtrem-manufacturing/functions__bom__tracking_more_than_one_bom_found": "There is more than one bill of material linked to the work order.", "@sage/xtrem-manufacturing/functions__create_work_order__cannot_track_phantom_bill_of_material": "You can only track a work order for an item that does not have a phantom component.", "@sage/xtrem-manufacturing/functions__create_work_order__date_before_today": "The date '{{startDate}}' is before the current date.", "@sage/xtrem-manufacturing/functions__create_work_order__number_already_in_use": "The work order number is already in use. Enter a unique work order reference.", "@sage/xtrem-manufacturing/functions__create_work_order__phantom_bom_missing": "You need to create a BOM for the {{item}} phantom item.", "@sage/xtrem-manufacturing/functions__create_work_order__release_quantity_invalid": "Release quantity '{{quantity}}' is invalid", "@sage/xtrem-manufacturing/functions__create_work_order__released_item_id_missing": "ReleasedItem ID missing", "@sage/xtrem-manufacturing/functions__create_work_order__released_item_missing": "Cannot create work order operations with no work order production items", "@sage/xtrem-manufacturing/functions__create_work_order__site_id_missing": "Site ID missing", "@sage/xtrem-manufacturing/functions__material_tracking__component_not_on_work_order": "Component number '{{componentNumber}}' does not exist on work order.", "@sage/xtrem-manufacturing/functions__material_tracking__component_number_invalid": "Component number '{{componentNumber}}' is invalid.", "@sage/xtrem-manufacturing/functions__operation_tracking__operation_not_on_work_order": "Operation number '{{operationNumber}}' does not exist on work order.", "@sage/xtrem-manufacturing/functions__operation_tracking__operation_number_invalid": "Operation number '{{operationNumber}}' is invalid.", "@sage/xtrem-manufacturing/functions__production_tracking__released_item_missing": "Released item ID missing.", "@sage/xtrem-manufacturing/functions__tracking__release_quantity_invalid": "Release quantity '{{quantity}}' is invalid.", "@sage/xtrem-manufacturing/functions__tracking__tracking_number_missing": "Work order tracking number missing.", "@sage/xtrem-manufacturing/functions__tracking__work_order_missing": "You need to add a work order.", "@sage/xtrem-manufacturing/functions__tracking__work_order_number_missing": "Work order number missing.", "@sage/xtrem-manufacturing/functions__work_order_scheduling__site_time_zone_not_defined": "You need to add a time zone to the {{site}} site before you can schedule work orders.", "@sage/xtrem-manufacturing/functions__work_order_scheduling__work_order_with_no_operation": "You can only schedule a work order that has at least one operation.", "@sage/xtrem-manufacturing/generate-close-work-order": "Generate", "@sage/xtrem-manufacturing/menu_item__features-manufacturing": "Manufacturing", "@sage/xtrem-manufacturing/menu_item__manufacturing-inquiries": "Manufacturing inquiries", "@sage/xtrem-manufacturing/no_line_in_the_material_tracking": "There is no line in this material tracking.", "@sage/xtrem-manufacturing/node__material_tracking__resend_notification_for_finance": "Resending finance notification for material tracking: {{materialTracking}}.", "@sage/xtrem-manufacturing/node__operation_tracking__resend_notification_for_finance": "Resending finance notification for Operation tracking: {{operationTracking}}.", "@sage/xtrem-manufacturing/node__production_tracking__resend_notification_for_finance": "Resending finance notification for production tracking: {{productionTracking}}.", "@sage/xtrem-manufacturing/node__work_order__resend_notification_for_finance": "Resending finance notification for work order: {{workOrder}}.", "@sage/xtrem-manufacturing/node_work_order_bulk_print_pick_list_report_name": "Work order pick list", "@sage/xtrem-manufacturing/node_work_order_bulk_print_report_name": "Work order", "@sage/xtrem-manufacturing/node_work_order_print_pick_list_error": "This work order needs to be firm and in progress or pending to print the pick list:  {{workOrderNumber}}.", "@sage/xtrem-manufacturing/node-extensions__base_resource_extension__property__operationResources": "Operation resources", "@sage/xtrem-manufacturing/node-extensions__bill_of_material_extension__property__workOrderCategory": "Work order category", "@sage/xtrem-manufacturing/node-extensions__stock_journal_extension__property__materialTrackingLine": "Material tracking line", "@sage/xtrem-manufacturing/node-extensions__stock_journal_extension__property__productionTrackingLine": "Production tracking line", "@sage/xtrem-manufacturing/node-extensions__stock_journal_extension__property__workOrder": "Work order", "@sage/xtrem-manufacturing/node-operation-tracking-created": "Time trackings created", "@sage/xtrem-manufacturing/nodes__manufacturing_tracking__no_automatic_material_tracking_for_serialized_item": "No automatic material tracking generated for work order {{workOrderNumber}} serialized item {{itemId}} {{itemName}}.", "@sage/xtrem-manufacturing/nodes__manufacturing_tracking__not_enough_stock": "Not enough stock on Site: {{site}} Item: {{item}}.", "@sage/xtrem-manufacturing/nodes__manufacturing_tracking__work_order_type_must_be_firm": "Only firm work orders can be tracked.", "@sage/xtrem-manufacturing/nodes__manufacturing_tracking__work-order-tracking-number-is-empty": "Enter the work order tracking number.", "@sage/xtrem-manufacturing/nodes__material_tracking__allocation_request_in_progress": "You can only track the component {{itemName}} after the allocation request is complete.", "@sage/xtrem-manufacturing/nodes__material_tracking__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__material_tracking__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__material_tracking__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__material_tracking__cant_create_tracking_when_effective_date_is_in_the_future": "The effective date is in the future, the material tracking cannot be created.", "@sage/xtrem-manufacturing/nodes__material_tracking__cant_create_tracking_when_work_order_is_closed": "The work order is Closed, the material tracking cannot be posted.", "@sage/xtrem-manufacturing/nodes__material_tracking__deletion_forbidden": "This material tracking cannot be deleted.", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createMaterialTrackings": "Create material trackings", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createMaterialTrackings__failed": "Create material trackings failed.", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createMaterialTrackings__parameter__componentNumber": "Component number", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createMaterialTrackings__parameter__isAutomated": "Is automated", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createMaterialTrackings__parameter__isMaterialTrackingCreationAllowedForSerialNumberManagedItems": "Is material tracking creation allowed for serial number managed items", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createMaterialTrackings__parameter__trackingDate": "Tracking date", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createMaterialTrackings__parameter__trackingQuantity": "Tracking quantity", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createMaterialTrackings__parameter__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createMaterialTrackings__parameter__workOrderTrackingNumber": "Work order tracking number", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createMultipleMaterialTrackings": "Create multiple material trackings", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createMultipleMaterialTrackings__failed": "Create multiple material trackings failed.", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createMultipleMaterialTrackings__parameter__data": "Data", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createSingleMaterialTracking": "Create single material tracking", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createSingleMaterialTracking__failed": "Create single material tracking failed.", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__createSingleMaterialTracking__parameter__data": "Data", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__postToStock": "Post to stock", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__postToStock__failed": "Post to stock failed.", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__postToStock__parameter__documentIds": "Document ids", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-manufacturing/nodes__material_tracking__mutation__resendNotificationForFinance__parameter__materialTracking": "Material tracking", "@sage/xtrem-manufacturing/nodes__material_tracking__no_line_in_the_material_tracking": "There is no line in this material tracking {{trackingNumber}}", "@sage/xtrem-manufacturing/nodes__material_tracking__node_name": "Material tracking", "@sage/xtrem-manufacturing/nodes__material_tracking__phantom_item_not_allowed": "Phantom components are not allowed in material tracking.", "@sage/xtrem-manufacturing/nodes__material_tracking__property__allocationStatus": "Allocation status", "@sage/xtrem-manufacturing/nodes__material_tracking__property__documentDate": "Document date", "@sage/xtrem-manufacturing/nodes__material_tracking__property__effectiveDate": "Effective date", "@sage/xtrem-manufacturing/nodes__material_tracking__property__entryDate": "Entry date", "@sage/xtrem-manufacturing/nodes__material_tracking__property__financialSite": "Financial site", "@sage/xtrem-manufacturing/nodes__material_tracking__property__forceUpdateForFinance": "Force update for finance", "@sage/xtrem-manufacturing/nodes__material_tracking__property__forceUpdateForStock": "Force update for stock", "@sage/xtrem-manufacturing/nodes__material_tracking__property__lines": "Lines", "@sage/xtrem-manufacturing/nodes__material_tracking__property__number": "Number", "@sage/xtrem-manufacturing/nodes__material_tracking__property__site": "Site", "@sage/xtrem-manufacturing/nodes__material_tracking__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-manufacturing/nodes__material_tracking__property__transactionCurrency": "Transaction currency", "@sage/xtrem-manufacturing/nodes__material_tracking__property__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__material_tracking__work_order_type_must_comply": "The work order component status must be different from Excluded.", "@sage/xtrem-manufacturing/nodes__material_tracking__work_order_with_incorrect_bom": "Work order {{workOrderNumber}}: No material tracking generated for a BOM that is in development.", "@sage/xtrem-manufacturing/nodes__material_tracking_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__material_tracking_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__material_tracking_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__material_tracking_line__deletion_forbidden": "This material tracking line cannot be deleted.", "@sage/xtrem-manufacturing/nodes__material_tracking_line__node_name": "Material tracking line", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__allocationStatus": "Allocation status", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__analyticalData": "Analytical data", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__completed": "Completed", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__document": "Document", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__documentId": "Document id", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__documentNumber": "Document number", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__isActiveQuantity": "Is active quantity", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__itemSite": "Item site", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__jsonStockDetails": "Json stock details", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__line": "Line", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__materialType": "Material type", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__quantityAllocated": "Quantity allocated", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__remainingQuantityToAllocate": "Remaining quantity to allocate", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__stockAllocations": "Stock allocations", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__stockDetails": "Stock details", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__stockMovements": "Stock movements", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__stockTransactions": "Stock transactions", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__workInProgressCost": "Work in progress cost", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__workInProgressCostAmount": "Work in progress cost amount", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__workInProgressCosts": "Work in progress costs", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__workInProgressWorkOrderNumber": "Work in progress work order number", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__workInProgressWorkOrderSysId": "Work in progress work order sys id", "@sage/xtrem-manufacturing/nodes__material_tracking_line__property__workOrderLine": "Work order line", "@sage/xtrem-manufacturing/nodes__operation_tracking__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__operation_tracking__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__operation_tracking__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__operation_tracking__asyncMutation__createMultipleOperationalTrackings": "Create multiple operational trackings", "@sage/xtrem-manufacturing/nodes__operation_tracking__asyncMutation__createMultipleOperationalTrackings__failed": "Create multiple operational trackings failed.", "@sage/xtrem-manufacturing/nodes__operation_tracking__asyncMutation__createMultipleOperationalTrackings__parameter__trackings": "Trackings", "@sage/xtrem-manufacturing/nodes__operation_tracking__deletion_forbidden": "This operation tracking cannot be deleted.", "@sage/xtrem-manufacturing/nodes__operation_tracking__line__completed_quantity_must_comply": "You need to add an operation quantity for the operation resource.", "@sage/xtrem-manufacturing/nodes__operation_tracking__mutation__createOperationTrackings": "Create operation trackings", "@sage/xtrem-manufacturing/nodes__operation_tracking__mutation__createOperationTrackings__failed": "Create operation trackings failed.", "@sage/xtrem-manufacturing/nodes__operation_tracking__mutation__createOperationTrackings__parameter__operationNumber": "Operation number", "@sage/xtrem-manufacturing/nodes__operation_tracking__mutation__createOperationTrackings__parameter__trackingDate": "Tracking date", "@sage/xtrem-manufacturing/nodes__operation_tracking__mutation__createOperationTrackings__parameter__trackingQuantity": "Tracking quantity", "@sage/xtrem-manufacturing/nodes__operation_tracking__mutation__createOperationTrackings__parameter__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__operation_tracking__mutation__createOperationTrackings__parameter__workOrderTrackingNumber": "Work order tracking number", "@sage/xtrem-manufacturing/nodes__operation_tracking__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-manufacturing/nodes__operation_tracking__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-manufacturing/nodes__operation_tracking__mutation__resendNotificationForFinance__parameter__operationTracking": "Operation tracking", "@sage/xtrem-manufacturing/nodes__operation_tracking__node_name": "Operation tracking", "@sage/xtrem-manufacturing/nodes__operation_tracking__property__documentDate": "Document date", "@sage/xtrem-manufacturing/nodes__operation_tracking__property__effectiveDate": "Effective date", "@sage/xtrem-manufacturing/nodes__operation_tracking__property__entryDate": "Entry date", "@sage/xtrem-manufacturing/nodes__operation_tracking__property__financialSite": "Financial site", "@sage/xtrem-manufacturing/nodes__operation_tracking__property__lines": "Lines", "@sage/xtrem-manufacturing/nodes__operation_tracking__property__number": "Number", "@sage/xtrem-manufacturing/nodes__operation_tracking__property__site": "Site", "@sage/xtrem-manufacturing/nodes__operation_tracking__property__transactionCurrency": "Transaction currency", "@sage/xtrem-manufacturing/nodes__operation_tracking__property__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__operation_tracking__work_order_operation_must_comply": "The work order operation status must be different from Excluded.", "@sage/xtrem-manufacturing/nodes__operation_tracking__work_order_with_incorrect_routing": "Work order {{workOrderNumber}}: No time tracking generated for a routing that is in development.", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__deletion_forbidden": "This operation tracking line cannot be deleted.", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__node_name": "Operation tracking line", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__actualResource": "Actual resource", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__actualRunTime": "Actual run time", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__actualSetupTime": "Actual setup time", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__analyticalData": "Analytical data", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__completed": "Completed", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__completedQuantity": "Completed quantity", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__document": "Document", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__documentId": "Document id", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__documentNumber": "Document number", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__line": "Line", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__operationResource": "Operation resource", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__runTimeUnit": "Run time unit", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__setupTimeUnit": "Setup time unit", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__trackingType": "Tracking type", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__workInProgressActualResourceType": "Work in progress actual resource type", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__workInProgressCostAmount": "Work in progress cost amount", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__workInProgressCosts": "Work in progress costs", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__workInProgressCostType": "Work in progress cost type", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__workInProgressWorkOrderNumber": "Work in progress work order number", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__workInProgressWorkOrderSysId": "Work in progress work order sys id", "@sage/xtrem-manufacturing/nodes__operation_tracking_line__property__workOrderOperation": "Work order operation", "@sage/xtrem-manufacturing/nodes__production_tracking__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__production_tracking__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__production_tracking__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__production_tracking__asyncMutation__createTestTracking": "Create test tracking", "@sage/xtrem-manufacturing/nodes__production_tracking__asyncMutation__createTestTracking__failed": "Create test tracking failed.", "@sage/xtrem-manufacturing/nodes__production_tracking__asyncMutation__createTestTracking__parameter__orderNumberRoot": "Order number root", "@sage/xtrem-manufacturing/nodes__production_tracking__asyncMutation__createTestTracking__parameter__quantityRatio": "Quantity ratio", "@sage/xtrem-manufacturing/nodes__production_tracking__deletion_forbidden": "This production tracking cannot be deleted.", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createMultipleWorkOrderTrackings": "Create multiple work order trackings", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createMultipleWorkOrderTrackings__failed": "Create multiple work order trackings failed.", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createMultipleWorkOrderTrackings__parameter__trackingDate": "Tracking date", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createMultipleWorkOrderTrackings__parameter__trackings": "Trackings", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createSingleProductionTracking": "Create single production tracking", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createSingleProductionTracking__failed": "Create single production tracking failed.", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createSingleProductionTracking__parameter__completed": "Completed", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createSingleProductionTracking__parameter__releasedItem": "Released item", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createSingleProductionTracking__parameter__stockDetails": "Stock details", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createSingleProductionTracking__parameter__storedAttributes": "Stored attributes", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createSingleProductionTracking__parameter__storedDimensions": "Stored dimensions", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createSingleProductionTracking__parameter__trackingDate": "Tracking date", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createSingleProductionTracking__parameter__trackingQuantity": "Tracking quantity", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createSingleProductionTracking__parameter__workOrderTrackingNumber": "Work order tracking number", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createWorkOrderTracking": "Create work order tracking", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createWorkOrderTracking__failed": "Create work order tracking failed.", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__createWorkOrderTracking__parameter__data": "Data", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__postToStock": "Post to stock", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__postToStock__failed": "Post to stock failed.", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__postToStock__parameter__documentIds": "Document ids", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-manufacturing/nodes__production_tracking__mutation__resendNotificationForFinance__parameter__productionTracking": "Production tracking", "@sage/xtrem-manufacturing/nodes__production_tracking__node_name": "Production tracking", "@sage/xtrem-manufacturing/nodes__production_tracking__phantom_item_not_allowed": "Phantom components are not allowed in production tracking.", "@sage/xtrem-manufacturing/nodes__production_tracking__property__documentDate": "Document date", "@sage/xtrem-manufacturing/nodes__production_tracking__property__effectiveDate": "Effective date", "@sage/xtrem-manufacturing/nodes__production_tracking__property__entryDate": "Entry date", "@sage/xtrem-manufacturing/nodes__production_tracking__property__financialSite": "Financial site", "@sage/xtrem-manufacturing/nodes__production_tracking__property__forceUpdateForFinance": "Force update for finance", "@sage/xtrem-manufacturing/nodes__production_tracking__property__forceUpdateForStock": "Force update for stock", "@sage/xtrem-manufacturing/nodes__production_tracking__property__lines": "Lines", "@sage/xtrem-manufacturing/nodes__production_tracking__property__number": "Number", "@sage/xtrem-manufacturing/nodes__production_tracking__property__site": "Site", "@sage/xtrem-manufacturing/nodes__production_tracking__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-manufacturing/nodes__production_tracking__property__transactionCurrency": "Transaction currency", "@sage/xtrem-manufacturing/nodes__production_tracking__property__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__production_tracking_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__production_tracking_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__production_tracking_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__production_tracking_line__deletion_forbidden": "This production tracking line cannot be deleted.", "@sage/xtrem-manufacturing/nodes__production_tracking_line__node_name": "Production tracking line", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__analyticalData": "Analytical data", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__completed": "Completed", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__document": "Document", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__documentId": "Document id", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__documentNumber": "Document number", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__jsonStockDetails": "Json stock details", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__line": "Line", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__orderCost": "Order cost", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__releasedQuantity": "Released quantity", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__stockDetails": "Stock details", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__stockTransactions": "Stock transactions", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__type": "Type", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__valuedCost": "Valued cost", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__workInProgressCost": "Work in progress cost", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__workInProgressCostAmount": "Work in progress cost amount", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__workInProgressCosts": "Work in progress costs", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__workInProgressWorkOrderNumber": "Work in progress work order number", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__workInProgressWorkOrderSysId": "Work in progress work order sys id", "@sage/xtrem-manufacturing/nodes__production_tracking_line__property__workOrderLine": "Work order line", "@sage/xtrem-manufacturing/nodes__production_tracking_line__query__getQuantityProduced": "Get quantity produced", "@sage/xtrem-manufacturing/nodes__production_tracking_line__query__getQuantityProduced__failed": "Get quantity produced failed.", "@sage/xtrem-manufacturing/nodes__tracking_lib__stock_update_not_finished": "The stock update is still in progress. You can resync after it has completed.", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__node_name": "Work in progress cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__actualResource": "Actual resource", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__amount": "Amount", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__computedAttributes": "Computed attributes", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__cost": "Cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__effectiveDate": "Effective date", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__item": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__operationTrackingLine": "Operation tracking line", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__originatingLine": "Originating line", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__postingDate": "Posting date", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__quantity": "Quantity", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__status": "Status", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__storedAttributes": "Stored attributes", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__storedDimensions": "Stored dimensions", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__type": "Type", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__unit": "Unit", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__workInProgressCostType": "Work in progress cost type", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__property__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__query__getCostProduced": "Get cost produced", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__query__getCostProduced__failed": "Get cost produced failed.", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__query__getCostProduced__parameter__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__work_in_progress_cost__query__getCostProduced__parameter__reportMonths": "Report months", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__asyncMutation__wipInquiry": "Wip inquiry", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__asyncMutation__wipInquiry__failed": "Wip inquiry failed.", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__asyncMutation__wipInquiry__parameter__userId": "User id", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__node_name": "Work in progress input set", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__property__asOfDate": "As of date", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__property__executionDate": "Execution date", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__property__fromWorkOrder": "From work order", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__property__lines": "Lines", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__property__site": "Site", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__property__status": "Status", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__property__toWorkOrder": "To work order", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__property__user": "User", "@sage/xtrem-manufacturing/nodes__work_in_progress_input_set__property__workInProgressTotalAmount": "Work in progress total amount", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__node_name": "Work in progress result line", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__actualLaborCost": "Actual labor cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__actualMachineCost": "Actual machine cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__actualMaterialCost": "Actual material cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__actualProcessCost": "Actual process cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__actualToolCost": "Actual tool cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__closingVariance": "Closing variance", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__inputSet": "Input set", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__isJournalEntryAvailable": "Is journal entry available", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__isStockJournalAvailable": "Is stock journal available", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__laborCostVariance": "Labor cost variance", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__laborCostVariancePercentage": "Labor cost variance percentage", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__machineCostVariance": "Machine cost variance", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__machineCostVariancePercentage": "Machine cost variance percentage", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__materialCostVariance": "Material cost variance", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__materialCostVariancePercentage": "Material cost variance percentage", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__negativeClosingVariance": "Negative closing variance", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__plannedLaborCost": "Planned labor cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__plannedMachineCost": "Planned machine cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__plannedMaterialCost": "Planned material cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__plannedProcessCost": "Planned process cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__plannedToolCost": "Planned tool cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__positiveClosingVariance": "Positive closing variance", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__processCostVariance": "Process cost variance", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__processCostVariancePercentage": "Process cost variance percentage", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__productionCost": "Production cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__status": "Status", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__toolCostVariance": "Tool cost variance", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__toolCostVariancePercentage": "Tool cost variance percentage", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__totalActualCost": "Total actual cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__totalCostVariance": "Total cost variance", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__totalCostVariancePercentage": "Total cost variance percentage", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__totalPlannedCost": "Total planned cost", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__workInProgressTotal": "Work in progress total", "@sage/xtrem-manufacturing/nodes__work_in_progress_result_line__property__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__node_name": "Work in progress work order component", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__property__actualQuantity": "Actual quantity", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__property__documentId": "Document id", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__property__documentLine": "Document line", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__property__documentNumber": "Document number", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__property__documentType": "Document type", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__property__endDate": "End date", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__property__expectedQuantity": "Expected quantity", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__property__item": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__property__originDocumentType": "Origin document type", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__property__outstandingQuantity": "Outstanding quantity", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__property__site": "Site", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__property__startDate": "Start date", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__property__status": "Status", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_component__property__workOrderComponent": "Work order component", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__node_name": "Work in progress work order released item", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__property__actualQuantity": "Actual quantity", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__property__documentId": "Document id", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__property__documentLine": "Document line", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__property__documentNumber": "Document number", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__property__documentType": "Document type", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__property__endDate": "End date", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__property__expectedQuantity": "Expected quantity", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__property__item": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__property__originDocumentType": "Origin document type", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__property__outstandingQuantity": "Outstanding quantity", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__property__site": "Site", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__property__startDate": "Start date", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__property__status": "Status", "@sage/xtrem-manufacturing/nodes__work_in_progress_work_order_released_item__property__workOrderReleasedItem": "Work order released item", "@sage/xtrem-manufacturing/nodes__work_order__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__work_order__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__work_order__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__work_order__asyncMutation__createTestWorkOrders": "Create test work orders", "@sage/xtrem-manufacturing/nodes__work_order__asyncMutation__createTestWorkOrders__failed": "Create test work orders failed.", "@sage/xtrem-manufacturing/nodes__work_order__asyncMutation__createTestWorkOrders__parameter__bom": "Bo<PERSON>", "@sage/xtrem-manufacturing/nodes__work_order__asyncMutation__createTestWorkOrders__parameter__orderNumberRoot": "Order number root", "@sage/xtrem-manufacturing/nodes__work_order__asyncMutation__createTestWorkOrders__parameter__orderQuantity": "Order quantity", "@sage/xtrem-manufacturing/nodes__work_order__asyncMutation__resynchronizeStatus": "Resynchronize status", "@sage/xtrem-manufacturing/nodes__work_order__asyncMutation__resynchronizeStatus__failed": "Resynchronize status failed.", "@sage/xtrem-manufacturing/nodes__work_order__asyncMutation__resynchronizeStatus__parameter__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_order__asyncMutation__trackBillOfMaterial": "Track bill of material", "@sage/xtrem-manufacturing/nodes__work_order__asyncMutation__trackBillOfMaterial__failed": "Track bill of material failed.", "@sage/xtrem-manufacturing/nodes__work_order__asyncMutation__trackBillOfMaterial__parameter__data": "Data", "@sage/xtrem-manufacturing/nodes__work_order__bom_required": "Bill of material is missing.", "@sage/xtrem-manufacturing/nodes__work_order__bulkMutation__bulkPrintPickList": "Bulk print pick list", "@sage/xtrem-manufacturing/nodes__work_order__bulkMutation__bulkPrintPickList__failed": "Bulk print pick list failed.", "@sage/xtrem-manufacturing/nodes__work_order__bulkMutation__closeWorkOrder": "Close work order", "@sage/xtrem-manufacturing/nodes__work_order__bulkMutation__closeWorkOrder__failed": "Close work order failed.", "@sage/xtrem-manufacturing/nodes__work_order__bulkMutation__closeWorkOrder__parameter__closingDate": "Closing date", "@sage/xtrem-manufacturing/nodes__work_order__bulkMutation__printBulk": "Print bulk", "@sage/xtrem-manufacturing/nodes__work_order__bulkMutation__printBulk__failed": "Print bulk failed.", "@sage/xtrem-manufacturing/nodes__work_order__cannot_close_allocated_request_in_progress": "You can only close the work order after the allocation request is complete for the component.", "@sage/xtrem-manufacturing/nodes__work_order__cannot_close_allocated_work_order": "Remove the stock allocation before closing the work order {{workOrderNumber}}.", "@sage/xtrem-manufacturing/nodes__work_order__cannot_close_pending_work_order": "Try to delete the pending work order {{workOrderNumber}} instead. You cannot close it.", "@sage/xtrem-manufacturing/nodes__work_order__cannot_close_with_incorrect_bom_or_routing": "You cannot close a work order for a routing or BOM that is in development.", "@sage/xtrem-manufacturing/nodes__work_order__cannot_close_with_pending_material_tracking": "You can only close the work order after all material trackings are posted.", "@sage/xtrem-manufacturing/nodes__work_order__cannot_close_with_pending_production_tracking": "You can only close the work order after all production trackings are posted.", "@sage/xtrem-manufacturing/nodes__work_order__cant_repost_tracking_when_status_is_not_failed": "You can only repost a work order tracking if the status is 'Failed'.", "@sage/xtrem-manufacturing/nodes__work_order__closing_date_greater_today": "The closing date must not be in the future.", "@sage/xtrem-manufacturing/nodes__work_order__closing_date_less_than_material_tracking": "The closing date must not be before any material tracking effective date. ({{date}})", "@sage/xtrem-manufacturing/nodes__work_order__closing_date_less_than_operation_tracking": "The closing date must not be before any operation tracking effective date. ({{date}})", "@sage/xtrem-manufacturing/nodes__work_order__closing_date_less_than_production_tracking": "The closing date must not be before any production tracking effective date. ({{date}})", "@sage/xtrem-manufacturing/nodes__work_order__closing_date_required_if_closed": "A closing date is required.", "@sage/xtrem-manufacturing/nodes__work_order__closing_was_reposted": "The work order closing was reposted.", "@sage/xtrem-manufacturing/nodes__work_order__forbid_changes_impacting_scheduling": "You can save your changes when this work order is scheduled. Try again in a few minutes.", "@sage/xtrem-manufacturing/nodes__work_order__material_tracking_was_reposted": "The material tracking was reposted.", "@sage/xtrem-manufacturing/nodes__work_order__multiple_production_step": "Only one operation with a production step is allowed.", "@sage/xtrem-manufacturing/nodes__work_order__mutation__addComponentsToWorkOrder": "Add components to work order", "@sage/xtrem-manufacturing/nodes__work_order__mutation__addComponentsToWorkOrder__failed": "Add components to work order failed.", "@sage/xtrem-manufacturing/nodes__work_order__mutation__addComponentsToWorkOrder__parameter__item": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__work_order__mutation__addComponentsToWorkOrder__parameter__site": "Site", "@sage/xtrem-manufacturing/nodes__work_order__mutation__addComponentsToWorkOrder__parameter__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_order__mutation__addOperationsToWorkOrder": "Add operations to work order", "@sage/xtrem-manufacturing/nodes__work_order__mutation__addOperationsToWorkOrder__failed": "Add operations to work order failed.", "@sage/xtrem-manufacturing/nodes__work_order__mutation__addOperationsToWorkOrder__parameter__item": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__work_order__mutation__addOperationsToWorkOrder__parameter__site": "Site", "@sage/xtrem-manufacturing/nodes__work_order__mutation__addOperationsToWorkOrder__parameter__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_order__mutation__beforePrintWorkOrderPickList": "Before print work order pick list", "@sage/xtrem-manufacturing/nodes__work_order__mutation__beforePrintWorkOrderPickList__failed": "Before print work order pick list failed.", "@sage/xtrem-manufacturing/nodes__work_order__mutation__beforePrintWorkOrderPickList__parameter__order": "Order", "@sage/xtrem-manufacturing/nodes__work_order__mutation__controlClosingDate": "Control closing date", "@sage/xtrem-manufacturing/nodes__work_order__mutation__controlClosingDate__failed": "Control closing date failed.", "@sage/xtrem-manufacturing/nodes__work_order__mutation__controlClosingDate__parameter__closingDate": "Closing date", "@sage/xtrem-manufacturing/nodes__work_order__mutation__controlClosingDate__parameter__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_order__mutation__createWorkOrder": "Create work order", "@sage/xtrem-manufacturing/nodes__work_order__mutation__createWorkOrder__failed": "Create work order failed.", "@sage/xtrem-manufacturing/nodes__work_order__mutation__createWorkOrder__parameter__data": "Data", "@sage/xtrem-manufacturing/nodes__work_order__mutation__preGenerateSerialNumbers": "Pre generate serial numbers", "@sage/xtrem-manufacturing/nodes__work_order__mutation__preGenerateSerialNumbers__failed": "Pre generate serial numbers failed.", "@sage/xtrem-manufacturing/nodes__work_order__mutation__preGenerateSerialNumbers__parameter__checkOnly": "Check only", "@sage/xtrem-manufacturing/nodes__work_order__mutation__preGenerateSerialNumbers__parameter__productionItem": "Production item", "@sage/xtrem-manufacturing/nodes__work_order__mutation__preGenerateSerialNumbers__parameter__releasedItem": "Released item", "@sage/xtrem-manufacturing/nodes__work_order__mutation__preGenerateSerialNumbers__parameter__releasedQuantity": "Released quantity", "@sage/xtrem-manufacturing/nodes__work_order__mutation__preGenerateSerialNumbers__parameter__site": "Site", "@sage/xtrem-manufacturing/nodes__work_order__mutation__repost": "Repost", "@sage/xtrem-manufacturing/nodes__work_order__mutation__repost__failed": "Repost failed.", "@sage/xtrem-manufacturing/nodes__work_order__mutation__repost__parameter__components": "Components", "@sage/xtrem-manufacturing/nodes__work_order__mutation__repost__parameter__financeTransactionSysId": "Finance transaction sys id", "@sage/xtrem-manufacturing/nodes__work_order__mutation__repost__parameter__operationResources": "Operation resources", "@sage/xtrem-manufacturing/nodes__work_order__mutation__repost__parameter__releasedItems": "Released items", "@sage/xtrem-manufacturing/nodes__work_order__mutation__repost__parameter__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_order__mutation__requestAutoAllocation": "Request auto allocation", "@sage/xtrem-manufacturing/nodes__work_order__mutation__requestAutoAllocation__failed": "Request auto allocation failed.", "@sage/xtrem-manufacturing/nodes__work_order__mutation__requestAutoAllocation__parameter__data": "Data", "@sage/xtrem-manufacturing/nodes__work_order__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-manufacturing/nodes__work_order__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-manufacturing/nodes__work_order__mutation__resendNotificationForFinance__parameter__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_order__mutation__scheduleWorkOrder": "Schedule work order", "@sage/xtrem-manufacturing/nodes__work_order__mutation__scheduleWorkOrder__failed": "Schedule work order failed.", "@sage/xtrem-manufacturing/nodes__work_order__mutation__scheduleWorkOrder__parameter__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_order__mutation__skipSchedulingWorkOrder": "Skip scheduling work order", "@sage/xtrem-manufacturing/nodes__work_order__mutation__skipSchedulingWorkOrder__failed": "Skip scheduling work order failed.", "@sage/xtrem-manufacturing/nodes__work_order__mutation__skipSchedulingWorkOrder__parameter__skip": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__work_order__mutation__skipSchedulingWorkOrder__parameter__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_order__mutation__updatePlannedCosts": "Update planned costs", "@sage/xtrem-manufacturing/nodes__work_order__mutation__updatePlannedCosts__failed": "Update planned costs failed.", "@sage/xtrem-manufacturing/nodes__work_order__mutation__updatePlannedCosts__parameter__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_order__no_backward_scheduling": "Backward scheduling is not available.", "@sage/xtrem-manufacturing/nodes__work_order__no_document_was_posted": "Nothing was reposted.", "@sage/xtrem-manufacturing/nodes__work_order__no_production_step": "At least one operation with a production step is required.", "@sage/xtrem-manufacturing/nodes__work_order__node_name": "Work order", "@sage/xtrem-manufacturing/nodes__work_order__operation_tracking_was_reposted": "The operation tracking was reposted.", "@sage/xtrem-manufacturing/nodes__work_order__phantom_item_not_allowed": "You can only create a work order for a standard BOM. You cannot create a work order for a phantom BOM.", "@sage/xtrem-manufacturing/nodes__work_order__production_tracking_was_reposted": "The production tracking was reposted.", "@sage/xtrem-manufacturing/nodes__work_order__property__actualMaterialCost": "Actual material cost", "@sage/xtrem-manufacturing/nodes__work_order__property__actualOverheadCost": "Actual overhead cost", "@sage/xtrem-manufacturing/nodes__work_order__property__actualProcessCost": "Actual process cost", "@sage/xtrem-manufacturing/nodes__work_order__property__allocationRequestStatus": "Allocation request status", "@sage/xtrem-manufacturing/nodes__work_order__property__allocationStatus": "Allocation status", "@sage/xtrem-manufacturing/nodes__work_order__property__baseQuantity": "Base quantity", "@sage/xtrem-manufacturing/nodes__work_order__property__baseRoutingQuantity": "Base routing quantity", "@sage/xtrem-manufacturing/nodes__work_order__property__bomCode": "Bom code", "@sage/xtrem-manufacturing/nodes__work_order__property__bomRevision": "Bom revision", "@sage/xtrem-manufacturing/nodes__work_order__property__category": "Category", "@sage/xtrem-manufacturing/nodes__work_order__property__closingDate": "Closing date", "@sage/xtrem-manufacturing/nodes__work_order__property__creationDate": "Creation date", "@sage/xtrem-manufacturing/nodes__work_order__property__documentDate": "Document date", "@sage/xtrem-manufacturing/nodes__work_order__property__doInheritDimensions": "Do inherit dimensions", "@sage/xtrem-manufacturing/nodes__work_order__property__endDate": "End date", "@sage/xtrem-manufacturing/nodes__work_order__property__endDatetime": "End datetime", "@sage/xtrem-manufacturing/nodes__work_order__property__financeIntegrationStatus": "Finance integration status", "@sage/xtrem-manufacturing/nodes__work_order__property__financialSite": "Financial site", "@sage/xtrem-manufacturing/nodes__work_order__property__forceUpdateForScheduling": "Force update for scheduling", "@sage/xtrem-manufacturing/nodes__work_order__property__forceUpdateForStock": "Force update for stock", "@sage/xtrem-manufacturing/nodes__work_order__property__isDuplication": "Is duplication", "@sage/xtrem-manufacturing/nodes__work_order__property__isForwardScheduling": "Is forward scheduling", "@sage/xtrem-manufacturing/nodes__work_order__property__isSchedulingSkipped": "Is scheduling skipped", "@sage/xtrem-manufacturing/nodes__work_order__property__isServiceOptionsSerialNumberActive": "Is service options serial number active", "@sage/xtrem-manufacturing/nodes__work_order__property__materialCompletionPercentage": "Material completion percentage", "@sage/xtrem-manufacturing/nodes__work_order__property__materialTrackings": "Material trackings", "@sage/xtrem-manufacturing/nodes__work_order__property__name": "Name", "@sage/xtrem-manufacturing/nodes__work_order__property__note": "Note", "@sage/xtrem-manufacturing/nodes__work_order__property__number": "Number", "@sage/xtrem-manufacturing/nodes__work_order__property__plannedLaborCost": "Planned labor cost", "@sage/xtrem-manufacturing/nodes__work_order__property__plannedMachineCost": "Planned machine cost", "@sage/xtrem-manufacturing/nodes__work_order__property__plannedMaterialCost": "Planned material cost", "@sage/xtrem-manufacturing/nodes__work_order__property__plannedOverheadCost": "Planned overhead cost", "@sage/xtrem-manufacturing/nodes__work_order__property__plannedProcessCost": "Planned process cost", "@sage/xtrem-manufacturing/nodes__work_order__property__plannedToolCost": "Planned tool cost", "@sage/xtrem-manufacturing/nodes__work_order__property__postingDetails": "Posting details", "@sage/xtrem-manufacturing/nodes__work_order__property__processCompletionPercentage": "Process completion percentage", "@sage/xtrem-manufacturing/nodes__work_order__property__productionCompletionPercentage": "Production completion percentage", "@sage/xtrem-manufacturing/nodes__work_order__property__productionComponents": "Production components", "@sage/xtrem-manufacturing/nodes__work_order__property__productionItem": "Production item", "@sage/xtrem-manufacturing/nodes__work_order__property__productionItems": "Production items", "@sage/xtrem-manufacturing/nodes__work_order__property__productionOperationResources": "Production operation resources", "@sage/xtrem-manufacturing/nodes__work_order__property__productionOperations": "Production operations", "@sage/xtrem-manufacturing/nodes__work_order__property__productionStepOperation": "Production step operation", "@sage/xtrem-manufacturing/nodes__work_order__property__productionTrackings": "Production trackings", "@sage/xtrem-manufacturing/nodes__work_order__property__requestedDate": "Requested date", "@sage/xtrem-manufacturing/nodes__work_order__property__routingCode": "Routing code", "@sage/xtrem-manufacturing/nodes__work_order__property__routingTimeUnit": "Routing time unit", "@sage/xtrem-manufacturing/nodes__work_order__property__schedulingStatus": "Scheduling status", "@sage/xtrem-manufacturing/nodes__work_order__property__serialsNumbers": "Serials numbers", "@sage/xtrem-manufacturing/nodes__work_order__property__site": "Site", "@sage/xtrem-manufacturing/nodes__work_order__property__startDate": "Start date", "@sage/xtrem-manufacturing/nodes__work_order__property__startDatetime": "Start datetime", "@sage/xtrem-manufacturing/nodes__work_order__property__status": "Status", "@sage/xtrem-manufacturing/nodes__work_order__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-manufacturing/nodes__work_order__property__timeTrackings": "Time trackings", "@sage/xtrem-manufacturing/nodes__work_order__property__timeUnit": "Time unit", "@sage/xtrem-manufacturing/nodes__work_order__property__timeZone": "Time zone", "@sage/xtrem-manufacturing/nodes__work_order__property__transactionCurrency": "Transaction currency", "@sage/xtrem-manufacturing/nodes__work_order__property__type": "Type", "@sage/xtrem-manufacturing/nodes__work_order__property__typeFiltered": "Type filtered", "@sage/xtrem-manufacturing/nodes__work_order__property__wipVariance": "Wip variance", "@sage/xtrem-manufacturing/nodes__work_order__property__workOrderSerialNumbers": "Work order serial numbers", "@sage/xtrem-manufacturing/nodes__work_order__query__getProductionCost": "Get production cost", "@sage/xtrem-manufacturing/nodes__work_order__query__getProductionCost__failed": "Get production cost failed.", "@sage/xtrem-manufacturing/nodes__work_order__query__getProductionCost__parameter__releasedItem": "Released item", "@sage/xtrem-manufacturing/nodes__work_order__routing_or_bom_required": "Assign a routing or bill of material to this work order.", "@sage/xtrem-manufacturing/nodes__work_order__routing_required": "Routing is missing.", "@sage/xtrem-manufacturing/nodes__work_order__type_must_be_firm": "A work order in progress must be firm.", "@sage/xtrem-manufacturing/nodes__work_order__wait_scheduling_is_done": "The work order {{workOrderNumber}} is currently in scheduling. Try again in a few minutes.", "@sage/xtrem-manufacturing/nodes__work_order__work_order_already_closed": "The work order {{workOrderNumber}} is already closed.", "@sage/xtrem-manufacturing/nodes__work_order__work_order_closed": "Work order {{number}} closed", "@sage/xtrem-manufacturing/nodes__work_order__work_order_closing": "Closing work order {{number}}", "@sage/xtrem-manufacturing/nodes__work_order__work_orders_completed": "Work orders completed", "@sage/xtrem-manufacturing/nodes__work_order_category__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__work_order_category__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__work_order_category__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__work_order_category__node_name": "Work order category", "@sage/xtrem-manufacturing/nodes__work_order_category__property__billOfMaterial": "Bill of material", "@sage/xtrem-manufacturing/nodes__work_order_category__property__description": "Description", "@sage/xtrem-manufacturing/nodes__work_order_category__property__id": "Id", "@sage/xtrem-manufacturing/nodes__work_order_category__property__isDefault": "Is default", "@sage/xtrem-manufacturing/nodes__work_order_category__property__name": "Name", "@sage/xtrem-manufacturing/nodes__work_order_category__property__routing": "Routing", "@sage/xtrem-manufacturing/nodes__work_order_category__routing_or_bom_required": "Assign a routing or bill of material to this work order category.", "@sage/xtrem-manufacturing/nodes__work_order_component__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__work_order_component__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__work_order_component__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__work_order_component__auto_allocation_cannot_decrease_quantity": "You can only reduce the required quantity of the component after the allocation request is complete.", "@sage/xtrem-manufacturing/nodes__work_order_component__auto_allocation_cannot_delete": "The component cannot be deleted while an automatic allocation is in progress.", "@sage/xtrem-manufacturing/nodes__work_order_component__auto_allocation_cannot_exclude": "You can only exclude the component after the allocation request is complete.", "@sage/xtrem-manufacturing/nodes__work_order_component__bulkMutation__massAutoAllocation": "Mass auto allocation", "@sage/xtrem-manufacturing/nodes__work_order_component__bulkMutation__massAutoAllocation__failed": "Mass auto allocation failed.", "@sage/xtrem-manufacturing/nodes__work_order_component__bulkMutation__massAutoAllocation__parameter__data": "Data", "@sage/xtrem-manufacturing/nodes__work_order_component__bulkMutation__massAutoAllocation__parameter__stockAllocationParameters": "Stock allocation parameters", "@sage/xtrem-manufacturing/nodes__work_order_component__cannot_delete_line_quantity_allocated": "Remove the stock allocation before deleting the line.", "@sage/xtrem-manufacturing/nodes__work_order_component__cannot_exclude_line_quantity_allocated": "Remove the stock allocation before excluding the line.", "@sage/xtrem-manufacturing/nodes__work_order_component__cannot_reduce_required_quantity_less_than_allocated": "The allocated quantity on the work order component cannot be more than the required quantity.", "@sage/xtrem-manufacturing/nodes__work_order_component__excluded_component_not_changeable": "The component number {{componentNumber}} is excluded and cannot be modified.", "@sage/xtrem-manufacturing/nodes__work_order_component__isAdded_not_changeable": "The 'isAdded' property cannot be updated.", "@sage/xtrem-manufacturing/nodes__work_order_component__mutation__updateComponent": "Update component", "@sage/xtrem-manufacturing/nodes__work_order_component__mutation__updateComponent__failed": "Update component failed.", "@sage/xtrem-manufacturing/nodes__work_order_component__mutation__updateComponent__parameter__completed": "Completed", "@sage/xtrem-manufacturing/nodes__work_order_component__mutation__updateComponent__parameter__componentNumber": "Component number", "@sage/xtrem-manufacturing/nodes__work_order_component__mutation__updateComponent__parameter__cost": "Cost", "@sage/xtrem-manufacturing/nodes__work_order_component__mutation__updateComponent__parameter__quantity": "Quantity", "@sage/xtrem-manufacturing/nodes__work_order_component__mutation__updateComponent__parameter__workOrderNumber": "Work order number", "@sage/xtrem-manufacturing/nodes__work_order_component__no_operation_for_text_line": "Operation must be null for text line type.", "@sage/xtrem-manufacturing/nodes__work_order_component__node_name": "Work order component", "@sage/xtrem-manufacturing/nodes__work_order_component__operation_excluded": "The {{cmpNumber}} component cannot be linked to the {{opeNumber}} excluded operation.", "@sage/xtrem-manufacturing/nodes__work_order_component__property__actualCost": "Actual cost", "@sage/xtrem-manufacturing/nodes__work_order_component__property__allocationRequestStatus": "Allocation request status", "@sage/xtrem-manufacturing/nodes__work_order_component__property__allocationStatus": "Allocation status", "@sage/xtrem-manufacturing/nodes__work_order_component__property__analyticalData": "Analytical data", "@sage/xtrem-manufacturing/nodes__work_order_component__property__availableQuantityInStockUnit": "Available quantity in stock unit", "@sage/xtrem-manufacturing/nodes__work_order_component__property__completedQuantityPercentage": "Completed quantity percentage", "@sage/xtrem-manufacturing/nodes__work_order_component__property__component": "Component", "@sage/xtrem-manufacturing/nodes__work_order_component__property__componentNumber": "Component number", "@sage/xtrem-manufacturing/nodes__work_order_component__property__computedAttributes": "Computed attributes", "@sage/xtrem-manufacturing/nodes__work_order_component__property__consumedQuantity": "Consumed quantity", "@sage/xtrem-manufacturing/nodes__work_order_component__property__document": "Document", "@sage/xtrem-manufacturing/nodes__work_order_component__property__documentId": "Document id", "@sage/xtrem-manufacturing/nodes__work_order_component__property__documentNumber": "Document number", "@sage/xtrem-manufacturing/nodes__work_order_component__property__instruction": "Instruction", "@sage/xtrem-manufacturing/nodes__work_order_component__property__isAdded": "Is added", "@sage/xtrem-manufacturing/nodes__work_order_component__property__isFixedLinkQuantity": "Is fixed link quantity", "@sage/xtrem-manufacturing/nodes__work_order_component__property__item": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__work_order_component__property__itemSite": "Item site", "@sage/xtrem-manufacturing/nodes__work_order_component__property__lineStatus": "Line status", "@sage/xtrem-manufacturing/nodes__work_order_component__property__lineType": "Line type", "@sage/xtrem-manufacturing/nodes__work_order_component__property__linkQuantity": "Link quantity", "@sage/xtrem-manufacturing/nodes__work_order_component__property__name": "Name", "@sage/xtrem-manufacturing/nodes__work_order_component__property__operation": "Operation", "@sage/xtrem-manufacturing/nodes__work_order_component__property__plannedCost": "Planned cost", "@sage/xtrem-manufacturing/nodes__work_order_component__property__quantityAllocated": "Quantity allocated", "@sage/xtrem-manufacturing/nodes__work_order_component__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-manufacturing/nodes__work_order_component__property__remainingQuantityToAllocate": "Remaining quantity to allocate", "@sage/xtrem-manufacturing/nodes__work_order_component__property__remainingQuantityToReport": "Remaining quantity to report", "@sage/xtrem-manufacturing/nodes__work_order_component__property__requiredDate": "Required date", "@sage/xtrem-manufacturing/nodes__work_order_component__property__requiredQuantity": "Required quantity", "@sage/xtrem-manufacturing/nodes__work_order_component__property__scrapFactor": "Scrap factor", "@sage/xtrem-manufacturing/nodes__work_order_component__property__stockAllocations": "Stock allocations", "@sage/xtrem-manufacturing/nodes__work_order_component__property__stockOnHand": "Stock on hand", "@sage/xtrem-manufacturing/nodes__work_order_component__property__stockShortageInStockUnit": "Stock shortage in stock unit", "@sage/xtrem-manufacturing/nodes__work_order_component__property__stockShortageStatus": "Stock shortage status", "@sage/xtrem-manufacturing/nodes__work_order_component__property__storedAttributes": "Stored attributes", "@sage/xtrem-manufacturing/nodes__work_order_component__property__storedDimensions": "Stored dimensions", "@sage/xtrem-manufacturing/nodes__work_order_component__property__uLineStatus": "U line status", "@sage/xtrem-manufacturing/nodes__work_order_component__property__unit": "Unit", "@sage/xtrem-manufacturing/nodes__work_order_component__property__workInProgress": "Work in progress", "@sage/xtrem-manufacturing/nodes__work_order_component__property__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_order_component__query__checkComponentStock": "Check component stock", "@sage/xtrem-manufacturing/nodes__work_order_component__query__checkComponentStock__failed": "Check component stock failed.", "@sage/xtrem-manufacturing/nodes__work_order_component__query__checkComponentStock__parameter__bomId": "Bom id", "@sage/xtrem-manufacturing/nodes__work_order_component__query__checkComponentStock__parameter__orderQuantity": "Order quantity", "@sage/xtrem-manufacturing/nodes__work_order_component__query__getMaterialPlannedCost": "Get material planned cost", "@sage/xtrem-manufacturing/nodes__work_order_component__query__getMaterialPlannedCost__failed": "Get material planned cost failed.", "@sage/xtrem-manufacturing/nodes__work_order_component__query__getMaterialPlannedCost__parameter__searchCriteria": "Search criteria", "@sage/xtrem-manufacturing/nodes__work_order_component__query__setRequiredQuantity": "Set required quantity", "@sage/xtrem-manufacturing/nodes__work_order_component__query__setRequiredQuantity__failed": "Set required quantity failed.", "@sage/xtrem-manufacturing/nodes__work_order_component__query__setRequiredQuantity__parameter__baseQuantity": "Base quantity", "@sage/xtrem-manufacturing/nodes__work_order_component__query__setRequiredQuantity__parameter__decimalPlaces": "Decimal places", "@sage/xtrem-manufacturing/nodes__work_order_component__query__setRequiredQuantity__parameter__isFixed": "Is fixed", "@sage/xtrem-manufacturing/nodes__work_order_component__query__setRequiredQuantity__parameter__linkQuantity": "Link quantity", "@sage/xtrem-manufacturing/nodes__work_order_component__query__setRequiredQuantity__parameter__releasedQuantity": "Released quantity", "@sage/xtrem-manufacturing/nodes__work_order_component__query__setRequiredQuantity__parameter__scrapFactor": "Scrap factor", "@sage/xtrem-manufacturing/nodes__work_order_component__text_work_order_component_not_completed_or_excluded": "The work order component status needs to be completed or excluded if the type is Text.", "@sage/xtrem-manufacturing/nodes__work_order_component__work_in_progress_forbidden": "Work in progress is not needed for a text component.", "@sage/xtrem-manufacturing/nodes__work_order_operation__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__work_order_operation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__work_order_operation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__work_order_operation__excluded_operation_not_changeable": "The excluded operation with number {{operationN<PERSON>ber}} cannot be modified.", "@sage/xtrem-manufacturing/nodes__work_order_operation__isAdded_not_changeable": "The isAdded property cannot be changed.", "@sage/xtrem-manufacturing/nodes__work_order_operation__mutation__updateOperation": "Update operation", "@sage/xtrem-manufacturing/nodes__work_order_operation__mutation__updateOperation__failed": "Update operation failed.", "@sage/xtrem-manufacturing/nodes__work_order_operation__mutation__updateOperation__parameter__data": "Data", "@sage/xtrem-manufacturing/nodes__work_order_operation__node_name": "Work order operation", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__actualBatchCost": "Actual batch cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__actualBatchRunCost": "Actual batch run cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__actualBatchSetupCost": "Actual batch setup cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__actualLaborCost": "Actual labor cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__actualLaborSetupCost": "Actual labor setup cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__actualMachineCost": "Actual machine cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__actualMachineSetupCost": "Actual machine setup cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__actualRunTime": "Actual run time", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__actualSetupTime": "Actual setup time", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__actualToolCost": "Actual tool cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__actualToolSetupCost": "Actual tool setup cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__completedQuantity": "Completed quantity", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__completedQuantityPercentage": "Completed quantity percentage", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__completedTimePercentage": "Completed time percentage", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__endDate": "End date", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__endDatetime": "End datetime", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__expectedBatchCost": "Expected batch cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__expectedBatchRunCost": "Expected batch run cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__expectedBatchSetupCost": "Expected batch setup cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__expectedLaborCost": "Expected labor cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__expectedLaborSetupCost": "Expected labor setup cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__expectedMachineCost": "Expected machine cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__expectedMachineSetupCost": "Expected machine setup cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__expectedRunTime": "Expected run time", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__expectedSetupTime": "Expected setup time", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__expectedToolCost": "Expected tool cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__expectedToolSetupCost": "Expected tool setup cost", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__instruction": "Instruction", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__isAdded": "Is added", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__isProductionStep": "Is production step", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__minCapabilityLevel": "Min capability level", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__name": "Name", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__operation": "Operation", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__operationNumber": "Operation number", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__plannedQuantity": "Planned quantity", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__remainingQuantity": "Remaining quantity", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__resourceGroupNumber": "Resource group number", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__resources": "Resources", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__runTimeUnit": "Run time unit", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__setupTimeUnit": "Setup time unit", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__startDate": "Start date", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__startDatetime": "Start datetime", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__status": "Status", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__uStatus": "U status", "@sage/xtrem-manufacturing/nodes__work_order_operation__property__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__empty_group_resource": "Enter the resources for the {{group}} group.", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__excluded_resource_not_changeable": "The resources for the excluded operation with number {{operationNumber}} cannot be modified.", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__incoherent_group_resource": "The detailed resource does not belong to the {{group}} group.", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__isAdded_not_changeable": "The isAdded property cannot be changed.", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__node_name": "Work order operation resource", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__actualCost": "Actual cost", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__actualRunCost": "Actual run cost", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__actualRunTime": "Actual run time", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__actualSetupCost": "Actual setup cost", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__actualSetupTime": "Actual setup time", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__analyticalData": "Analytical data", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__completedTimePercentage": "Completed time percentage", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__computedAttributes": "Computed attributes", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__efficiency": "Efficiency", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__endDatetime": "End datetime", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__expectedCost": "Expected cost", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__expectedRunCost": "Expected run cost", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__expectedRunTime": "Expected run time", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__expectedSetupCost": "Expected setup cost", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__expectedSetupTime": "Expected setup time", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__isAdded": "Is added", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__isResourceQuantity": "Is resource quantity", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__resource": "Resource", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__resourceNumber": "Resource number", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__resources": "Resources", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__runTimeUnit": "Run time unit", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__setupTimeUnit": "Setup time unit", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__startDatetime": "Start datetime", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__status": "Status", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__storedAttributes": "Stored attributes", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__storedDimensions": "Stored dimensions", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__uStatus": "U status", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__property__workOrderOperation": "Work order operation", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource__run_time_different_than_0_to_flag": "Run Time should be greater than 0 if isResourceQuantity is true.", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource_detail__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource_detail__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource_detail__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource_detail__node_name": "Work order operation resource detail", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource_detail__property__resource": "Resource", "@sage/xtrem-manufacturing/nodes__work_order_operation_resource_detail__property__workOrderOperationResource": "Work order operation resource", "@sage/xtrem-manufacturing/nodes__work_order_released_item__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__work_order_released_item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__work_order_released_item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__work_order_released_item__header_item_not_active": "You need to remove the inactive items before you change the document status.", "@sage/xtrem-manufacturing/nodes__work_order_released_item__mutation__updateReleasedItem": "Update released item", "@sage/xtrem-manufacturing/nodes__work_order_released_item__mutation__updateReleasedItem__failed": "Update released item failed.", "@sage/xtrem-manufacturing/nodes__work_order_released_item__mutation__updateReleasedItem__parameter__completed": "Completed", "@sage/xtrem-manufacturing/nodes__work_order_released_item__mutation__updateReleasedItem__parameter__quantity": "Quantity", "@sage/xtrem-manufacturing/nodes__work_order_released_item__mutation__updateReleasedItem__parameter__releasedItem": "Released item", "@sage/xtrem-manufacturing/nodes__work_order_released_item__mutation__updateReleasedItem__parameter__workOrderNumber": "Work order number", "@sage/xtrem-manufacturing/nodes__work_order_released_item__node_name": "Work order released item", "@sage/xtrem-manufacturing/nodes__work_order_released_item__phantom_item_not_allowed": "You can only add a stock item to a work order, not a phantom item.", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__analyticalData": "Analytical data", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__computedAttributes": "Computed attributes", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__document": "Document", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__documentId": "Document id", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__documentNumber": "Document number", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__item": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__itemSite": "Item site", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__lineStatus": "Line status", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__plannedLaborCost": "Planned labor cost", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__plannedMachineCost": "Planned machine cost", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__plannedMaterialCost": "Planned material cost", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__plannedProcessCost": "Planned process cost", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__plannedToolCost": "Planned tool cost", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__releasedItem": "Released item", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__releasedItemId": "Released item id", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__releasedItemName": "Released item name", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__releasedQuantity": "Released quantity", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__remainingQuantity": "Remaining quantity", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__serialNumbers": "Serial numbers", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__stockTransactions": "Stock transactions", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__stockUnit": "Stock unit", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__storedAttributes": "Stored attributes", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__storedDimensions": "Stored dimensions", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__totalActualQuantity": "Total actual quantity", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__uLineStatus": "U line status", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__uLineStatusForTracking": "U line status for tracking", "@sage/xtrem-manufacturing/nodes__work_order_released_item__property__workInProgress": "Work in progress", "@sage/xtrem-manufacturing/nodes__work_order_released_item__released_item_must_be_stock_managed_and_manufactured": "The released item needs to be stock managed and manufactured.", "@sage/xtrem-manufacturing/nodes__work_order_released_item__released_quantity_less_than_completed_quantity": "The released quantity is less than the completed quantity.", "@sage/xtrem-manufacturing/nodes__work_order_serial_number__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__work_order_serial_number__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__work_order_serial_number__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__work_order_serial_number__node_name": "Work order serial number", "@sage/xtrem-manufacturing/nodes__work_order_serial_number__property__serialNumber": "Serial number", "@sage/xtrem-manufacturing/nodes__work_order_serial_number__property__workOrder": "Work order", "@sage/xtrem-manufacturing/nodes__work_order_view__asyncMutation__asyncExport": "Export", "@sage/xtrem-manufacturing/nodes__work_order_view__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-manufacturing/nodes__work_order_view__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-manufacturing/nodes__work_order_view__mutation__defaultRecord": "Default record", "@sage/xtrem-manufacturing/nodes__work_order_view__mutation__defaultRecord__failed": "Default record failed.", "@sage/xtrem-manufacturing/nodes__work_order_view__node_name": "Work order view", "@sage/xtrem-manufacturing/nodes__work_order_view__property__workOrders": "Work orders", "@sage/xtrem-manufacturing/nodes__work-in-progress-input-set__success_message": "Success", "@sage/xtrem-manufacturing/nodes__work-in-progress-input-set__success_notification_title": "Work in progress inquiry calculation complete", "@sage/xtrem-manufacturing/nodes__work-order__bom_code_invalid": "You can only add a BOM code that is available to use to a work order.", "@sage/xtrem-manufacturing/nodes__work-order__bom_code_not_set": "You need to select a BOM code.", "@sage/xtrem-manufacturing/nodes__work-order__bom_revision_bom_code_mismatch": "The BOM revision needs to be for the BOM code. Item: {{itemId}}, Site: {{siteId}}.", "@sage/xtrem-manufacturing/nodes__work-order__bom_revision_invalid": "You can only add a BOM revision that is available to use to a work order.", "@sage/xtrem-manufacturing/nodes__work-order__bom_revision_start_date_invalid": "The BOM revision needs to be available to use on the work order start date.", "@sage/xtrem-manufacturing/nodes__work-order__routing_invalid": "You can only add a routing that is available to use to work order.", "@sage/xtrem-manufacturing/nodes_work_order_operation__is_resource_quantity_operation_already_flagged": "No more than one quantity produced resource with runtime on the operation number {{operationNumber}}", "@sage/xtrem-manufacturing/nodes_work_order_operation_no_quantity_produced_resource": "At least one quantity produced resource with runtime is required on the operation number {{operationNumber}}", "@sage/xtrem-manufacturing/package__name": "Sage xtrem manufacturing", "@sage/xtrem-manufacturing/page__work_order_component__auto_allocation_cannot_decrease_quantity": "You can only reduce the required quantity of the component after the allocation request is complete.", "@sage/xtrem-manufacturing/page_extension_item_site_cost_bill_of_material_missing": "No bill of material found for this item. Check the costing calculation.", "@sage/xtrem-manufacturing/page_extension_item_site_cost_bill_of_suspended_in_development": "The bill of material is in development or suspended. When it is available to use, you might need to recalculate the item-site cost.", "@sage/xtrem-manufacturing/page_extension_item_site_cost_calculate": "Calculate", "@sage/xtrem-manufacturing/page_extension_item_site_cost_routing_missing": "No routing found for this item. Check the costing calculation.", "@sage/xtrem-manufacturing/page_extension_item_site_cost_routing_of_suspended_in_development": "The routing is in development or suspended. When it is available to use, you might need to recalculate the item-site cost.", "@sage/xtrem-manufacturing/page_extension_stock_receipt_details_panel__page_title": "Production tracking", "@sage/xtrem-manufacturing/page_extension_stock_receipt_details_panel__page_title_checkStock": "Check stock", "@sage/xtrem-manufacturing/page_extension_stock_receipt_details_panel__stockDetails_title": "Stock receipt details", "@sage/xtrem-manufacturing/page_extension_stock_receipt_details_panel_checkStock": "Check stock", "@sage/xtrem-manufacturing/page_extension_stock_receipt_details_panel_recalculate": "Recalculate", "@sage/xtrem-manufacturing/page-extensions__allocation_result_extension__fromOperation____title": "From operation", "@sage/xtrem-manufacturing/page-extensions__allocation_result_extension__fromReleasedItem____title": "From released item", "@sage/xtrem-manufacturing/page-extensions__allocation_result_extension__latestStartDate____title": "Latest start date", "@sage/xtrem-manufacturing/page-extensions__allocation_result_extension__toOperation____title": "To operation", "@sage/xtrem-manufacturing/page-extensions__allocation_result_extension__toReleasedItem____title": "To released item", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__checkStock____title": "Check stock", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__materialTracking____columns__title__entryDate": "Date", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__materialTracking____columns__title__number": "Number", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__materialTracking____columns__title__site__id": "Site", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__materialTracking____columns__title__stockTransactionStatus": "Status", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__materialTracking____title": "Material tracking", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__productionTracking____columns__title__entryDate": "Date", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__productionTracking____columns__title__number": "Number", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__productionTracking____columns__title__site__id": "Site", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__productionTracking____columns__title__stockTransactionStatus": "Status", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__productionTracking____title": "Production tracking", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__timeTracking____columns__columns__site__id__title": "ID", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__timeTracking____columns__columns__site__id__title__2": "Name", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__timeTracking____columns__title__entryDate": "Date", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__timeTracking____columns__title__number": "Number", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__timeTracking____columns__title__site__id": "Site", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__timeTracking____title": "Time tracking", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__track____title": "Track", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__trackings____columns__columns__billOfMaterial__item__stockUnit__name__title": "Name", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__trackings____columns__title__billOfMaterial__item__stockUnit__name": "Unit", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__trackings____columns__title__date": "Date", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__trackings____columns__title__quantity": "Quantity", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__trackings____columns__title__status": "Status", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__trackings____inlineActions__title": "Open line panel", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__trackings____title": "Trackings", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__workOrder____columns__columns__site__id__title": "ID", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__workOrder____columns__columns__site__id__title__2": "Name", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__workOrder____columns__title__number": "Number", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__workOrder____columns__title__site__id": "Site", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__workOrder____columns__title__startDate": "Date", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__workOrder____columns__title__status": "Status", "@sage/xtrem-manufacturing/page-extensions__bill_of_material_extension__workOrder____title": "Work order", "@sage/xtrem-manufacturing/page-extensions__billOfMaterialExtension__trackingSuccess": "Bill of material tracking in progress", "@sage/xtrem-manufacturing/page-extensions__item_site_cost_panel_extension__isCalculated____title": "Calculated", "@sage/xtrem-manufacturing/page-extensions__stock_receipt_details_panel_extension__category____columns__title__billOfMaterial": "Bill of material", "@sage/xtrem-manufacturing/page-extensions__stock_receipt_details_panel_extension__category____columns__title__routing": "Routing", "@sage/xtrem-manufacturing/page-extensions__stock_receipt_details_panel_extension__category____lookupDialogTitle": "Select category", "@sage/xtrem-manufacturing/page-extensions__stock_receipt_details_panel_extension__category____title": "Category", "@sage/xtrem-manufacturing/page-extensions__stock_receipt_details_panel_extension__componentShortage____columns__title__available": "Available quantity", "@sage/xtrem-manufacturing/page-extensions__stock_receipt_details_panel_extension__componentShortage____columns__title__componentName": "Component description", "@sage/xtrem-manufacturing/page-extensions__stock_receipt_details_panel_extension__componentShortage____columns__title__itemDescription": "Item description", "@sage/xtrem-manufacturing/page-extensions__stock_receipt_details_panel_extension__componentShortage____columns__title__itemId": "Item ID", "@sage/xtrem-manufacturing/page-extensions__stock_receipt_details_panel_extension__componentShortage____columns__title__itemName": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/page-extensions__stock_receipt_details_panel_extension__componentShortage____columns__title__required": "Required quantity", "@sage/xtrem-manufacturing/page-extensions__stock_receipt_details_panel_extension__componentShortage____columns__title__shortage": "Shortage", "@sage/xtrem-manufacturing/page-extensions__stock_receipt_details_panel_extension__componentShortage____title": "Component shortage", "@sage/xtrem-manufacturing/page-extensions__stock_receipt_details_panel_extension__okFromBom____title": "OK", "@sage/xtrem-manufacturing/page-extensions__stock_receipt_details_panel_extension__quantity____title": "Quantity to produce", "@sage/xtrem-manufacturing/pages__allocation_result__fromWorkOrderNumberTitle": "From work order", "@sage/xtrem-manufacturing/pages__allocation_result__toWorkOrderNumberTitle": "To work order", "@sage/xtrem-manufacturing/pages__end_date_cannot_be_earlier_than_start_date": "The end date cannot be earlier than the start date.", "@sage/xtrem-manufacturing/pages__material__tracking__creation__success": "Tracking records generated: {{num}}", "@sage/xtrem-manufacturing/pages__material_tracking____title": "Material tracking", "@sage/xtrem-manufacturing/pages__material_tracking__addComponent____title": "Add component", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__columns__item__id__columns__title__decimalDigits": "Decimal digits", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__columns__item__id__columns__title__symbol": "Symbol", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__columns__item__id__title": "Name", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__columns__item__id__title__2": "ID", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__columns__item__id__title__3": "Unit", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__columns__workOrder__number__title": "Name", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__columns__workOrder__number__title__2": "Number", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__title__allocationStatus": "Allocation status", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__title__componentNumber": "Line", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__title__consumedQuantity": "Consumed", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__title__expectedQuantity": "Expected", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__title__item__id": "Component", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__title__itemDescription": "Component description", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__title__remainingQuantity": "Actual quantity", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__title__status": "Status", "@sage/xtrem-manufacturing/pages__material_tracking__components____columns__title__workOrder__number": "Number", "@sage/xtrem-manufacturing/pages__material_tracking__components____dropdownActions__title": "Allocate stock", "@sage/xtrem-manufacturing/pages__material_tracking__components____dropdownActions__title__2": "Dimensions", "@sage/xtrem-manufacturing/pages__material_tracking__components____dropdownActions__title__3": "Delete", "@sage/xtrem-manufacturing/pages__material_tracking__components____title": "Work order components", "@sage/xtrem-manufacturing/pages__material_tracking__createTrackings____title": "Generate", "@sage/xtrem-manufacturing/pages__material_tracking__criteriaBlock____title": "Material criteria", "@sage/xtrem-manufacturing/pages__material_tracking__defaultDimension____title": "Set default dimensions", "@sage/xtrem-manufacturing/pages__material_tracking__itemFrom____columns__title__item__category__name": "Category", "@sage/xtrem-manufacturing/pages__material_tracking__itemFrom____columns__title__item__name": "Name", "@sage/xtrem-manufacturing/pages__material_tracking__itemFrom____lookupDialogTitle": "Select item", "@sage/xtrem-manufacturing/pages__material_tracking__itemFrom____title": "From released item", "@sage/xtrem-manufacturing/pages__material_tracking__itemTo____columns__title__item__category__name": "Category", "@sage/xtrem-manufacturing/pages__material_tracking__itemTo____columns__title__item__name": "Name", "@sage/xtrem-manufacturing/pages__material_tracking__itemTo____lookupDialogTitle": "Select item", "@sage/xtrem-manufacturing/pages__material_tracking__itemTo____title": "To released item", "@sage/xtrem-manufacturing/pages__material_tracking__mainBlock____title": "Work order components", "@sage/xtrem-manufacturing/pages__material_tracking__mainSection____title": "General", "@sage/xtrem-manufacturing/pages__material_tracking__materialStatus____placeholder": "Select status", "@sage/xtrem-manufacturing/pages__material_tracking__materialStatus____title": "Status", "@sage/xtrem-manufacturing/pages__material_tracking__selectAllCheckbox____title": "Select all", "@sage/xtrem-manufacturing/pages__material_tracking__woFrom____columns__columns__site__name__title": "Name", "@sage/xtrem-manufacturing/pages__material_tracking__woFrom____columns__columns__site__name__title__2": "ID", "@sage/xtrem-manufacturing/pages__material_tracking__woFrom____columns__title__number": "Number", "@sage/xtrem-manufacturing/pages__material_tracking__woFrom____columns__title__site__name": "Site", "@sage/xtrem-manufacturing/pages__material_tracking__woFrom____columns__title__type": "Type", "@sage/xtrem-manufacturing/pages__material_tracking__woFrom____lookupDialogTitle": "Select work order", "@sage/xtrem-manufacturing/pages__material_tracking__woFrom____title": "Work order from", "@sage/xtrem-manufacturing/pages__material_tracking__woSite____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__material_tracking__woSite____title": "Site", "@sage/xtrem-manufacturing/pages__material_tracking__woStartDateFrom____title": "Start date from", "@sage/xtrem-manufacturing/pages__material_tracking__woStartDateTo____title": "Start date to", "@sage/xtrem-manufacturing/pages__material_tracking__woTo____columns__columns__site__name__title": "Name", "@sage/xtrem-manufacturing/pages__material_tracking__woTo____columns__columns__site__name__title__2": "ID", "@sage/xtrem-manufacturing/pages__material_tracking__woTo____columns__title__number": "Number", "@sage/xtrem-manufacturing/pages__material_tracking__woTo____columns__title__site__name": "Site", "@sage/xtrem-manufacturing/pages__material_tracking__woTo____columns__title__type": "Type", "@sage/xtrem-manufacturing/pages__material_tracking__woTo____lookupDialogTitle": "Select work order", "@sage/xtrem-manufacturing/pages__material_tracking__woTo____title": "Work order to", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry____navigationPanel__listItem__itemID__title": "Item ID", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry____navigationPanel__listItem__line2__title": "Work order", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry____navigationPanel__listItem__line3__title": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry____navigationPanel__listItem__line3Right__title": "Site name", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry____navigationPanel__listItem__siteID__title": "Site ID", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry____navigationPanel__listItem__titleRight__title": "Stock status", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry____navigationPanel__listItem__workOrderName__title": "Work order name", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry____objectTypePlural": "Material issues", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry____objectTypeSingular": "Material issue", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry____title": "Material issue", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__effectiveDate____title": "Date", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__financialSite____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__financialSite____placeholder": "Select ...", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__financialSite____title": "Financial site", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__lines____columns__columns__item__name__title": "Name", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__lines____columns__columns__item__name__title__2": "ID", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__lines____columns__columns__item__name__title__3": "Unit", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__lines____columns__columns__item__name__title__4": "Lot management", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__lines____columns__title__item__description": "Item description", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__lines____columns__title__item__id": "Item ID", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__lines____columns__title__quantityInStockUnit": "Quantity", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__lines____columns__title__workOrderLine__componentNumber": "Component number", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__lines____dropdownActions__title": "Stock details", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__lines____dropdownActions__title__2": "Allocate stock", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__lines____dropdownActions__title__3": "Dimensions", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__lines____title": "Components", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__mainSection____title": "General", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__number____title": "Number", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__post____title": "Post", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__releasedItem____title": "Item name", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__saveStockDetail____title": "Save", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__site____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__site____title": "Site", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__stockTransactionStatus____title": "Stock status", "@sage/xtrem-manufacturing/pages__material_tracking_inquiry__workOrder____title": "Work order", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__dropdownActions__title": "Costs", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__bom__columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__bom__columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__bom__lookupDialogTitle": "Select bill of material", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__bom__title": "Bill of material", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__bomId__title": "BOM ID", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__completed__title": "Completed", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__componentNumber__title": "Component number", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__componentStatus__title": "Status", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__itemId__title": "Item ID", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__itemName__title": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__quantity__title": "Quantity", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__site__title": "Site name", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__siteId__title": "Site ID", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__stockStatus__title": "Stock status", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__stockUnit__title": "Unit", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__title__title": "Stored dimensions", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__trackingDate__title": "Tracking date", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__trackingNumber__title": "Material tracking", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__trackingNumberLink__title": "Material tracking link", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__workOrder__title": "Work order", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__workOrderLine__columns__columns__item__id__title": "Category", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____navigationPanel__listItem__workOrderStatus__title": "Work order status", "@sage/xtrem-manufacturing/pages__material_tracking_line_inquiry____title": "Material tracking line inquiry", "@sage/xtrem-manufacturing/pages__pages__work_order_pregenerate_additional_serial_number_error": "Pregenerate serial numbers: {{error}}", "@sage/xtrem-manufacturing/pages__pages__work_order_pregenerate_serial_number_error": "Pregenerate serial numbers: {{error}}", "@sage/xtrem-manufacturing/pages__production_cost_currency_settings____title": "Production cost currency settings", "@sage/xtrem-manufacturing/pages__production_cost_currency_settings__productionCostCurrency____columns__title__id": "ISO 4217 code", "@sage/xtrem-manufacturing/pages__production_cost_currency_settings__productionCostCurrency____columns__title__symbol": "Symbol", "@sage/xtrem-manufacturing/pages__production_cost_currency_settings__productionCostCurrency____title": "Currency for cost values", "@sage/xtrem-manufacturing/pages__production_cost_currency_settings__save____title": "Save settings", "@sage/xtrem-manufacturing/pages__production_cost_trend_settings____title": "Production cost trend settings", "@sage/xtrem-manufacturing/pages__production_cost_trend_settings__productionCostCurrency____columns__title__id": "ISO 4217 code", "@sage/xtrem-manufacturing/pages__production_cost_trend_settings__productionCostCurrency____columns__title__symbol": "Symbol", "@sage/xtrem-manufacturing/pages__production_cost_trend_settings__productionCostCurrency____title": "Currency for cost values", "@sage/xtrem-manufacturing/pages__production_cost_trend_settings__reportMonths____title": "Number of months", "@sage/xtrem-manufacturing/pages__production_cost_trend_settings__save____title": "Save settings", "@sage/xtrem-manufacturing/pages__production_tracking____title": "Production tracking", "@sage/xtrem-manufacturing/pages__production_tracking__createProductionTrackings____title": "Generate", "@sage/xtrem-manufacturing/pages__production_tracking__criteriaBlock____title": "Work order criteria", "@sage/xtrem-manufacturing/pages__production_tracking__expiration_date_cannot_be_past": "The expiration date cannot be earlier than today.", "@sage/xtrem-manufacturing/pages__production_tracking__expiration_date_is_mandatory": "The expiration date is mandatory", "@sage/xtrem-manufacturing/pages__production_tracking__itemFrom____columns__title__item__category__name": "Category", "@sage/xtrem-manufacturing/pages__production_tracking__itemFrom____columns__title__item__name": "Name", "@sage/xtrem-manufacturing/pages__production_tracking__itemFrom____lookupDialogTitle": "Select item", "@sage/xtrem-manufacturing/pages__production_tracking__itemFrom____title": "From released item", "@sage/xtrem-manufacturing/pages__production_tracking__itemTo____columns__title__item__category__name": "Category", "@sage/xtrem-manufacturing/pages__production_tracking__itemTo____columns__title__item__name": "Name", "@sage/xtrem-manufacturing/pages__production_tracking__itemTo____lookupDialogTitle": "Select item", "@sage/xtrem-manufacturing/pages__production_tracking__itemTo____title": "To released item", "@sage/xtrem-manufacturing/pages__production_tracking__lotDialogSection____title": "Edit row", "@sage/xtrem-manufacturing/pages__production_tracking__mainSection____title": "General", "@sage/xtrem-manufacturing/pages__production_tracking__not_enough_predefined_serials_plural_error": "Pre-generate {{numberSerials}} serial numbers on the work order first.", "@sage/xtrem-manufacturing/pages__production_tracking__not_enough_predefined_serials_singular_error": "Pre-generate {{numberSerials}} serial number on the work order first.", "@sage/xtrem-manufacturing/pages__production_tracking__notification__The_location_is_mandatory": "The location is mandatory.", "@sage/xtrem-manufacturing/pages__production_tracking__notification__The_stock_status_is_mandatory": "The stock status is mandatory.", "@sage/xtrem-manufacturing/pages__production_tracking__on_actual_quantity_changed_warn": "The total actual quantity will be greater than the released quantity.", "@sage/xtrem-manufacturing/pages__production_tracking__on_actual_quantity_changed_warn_title": "Actual quantity", "@sage/xtrem-manufacturing/pages__production_tracking__woEndDateFrom____title": "End date from", "@sage/xtrem-manufacturing/pages__production_tracking__woEndDateTo____title": "End date to", "@sage/xtrem-manufacturing/pages__production_tracking__woNumberFrom____columns__columns__site__name__title": "Name", "@sage/xtrem-manufacturing/pages__production_tracking__woNumberFrom____columns__columns__site__name__title__2": "ID", "@sage/xtrem-manufacturing/pages__production_tracking__woNumberFrom____columns__title__number": "Number", "@sage/xtrem-manufacturing/pages__production_tracking__woNumberFrom____columns__title__site__name": "Site", "@sage/xtrem-manufacturing/pages__production_tracking__woNumberFrom____columns__title__type": "Type", "@sage/xtrem-manufacturing/pages__production_tracking__woNumberFrom____lookupDialogTitle": "Select work order", "@sage/xtrem-manufacturing/pages__production_tracking__woNumberFrom____title": "Work order from", "@sage/xtrem-manufacturing/pages__production_tracking__woNumberTo____columns__columns__site__name__title": "Name", "@sage/xtrem-manufacturing/pages__production_tracking__woNumberTo____columns__columns__site__name__title__2": "ID", "@sage/xtrem-manufacturing/pages__production_tracking__woNumberTo____columns__title__number": "Number", "@sage/xtrem-manufacturing/pages__production_tracking__woNumberTo____columns__title__site__name": "Site", "@sage/xtrem-manufacturing/pages__production_tracking__woNumberTo____columns__title__type": "Type", "@sage/xtrem-manufacturing/pages__production_tracking__woNumberTo____lookupDialogTitle": "Select work order", "@sage/xtrem-manufacturing/pages__production_tracking__woNumberTo____title": "Work order to", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__actualQuantity": "Actual qty", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__completed": "Completed", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__expirationReferenceDate": "Expiration reference date", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__itemDescription": "Released item description", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__itemId": "Released item ID", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__itemName": "Released item", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__lotId": "ID", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__materialTracking": "Automatic material tracking", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__number": "Number", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__potency": "Potency", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__releasedQuantity": "Released qty", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__shelfLife": "Shelf life", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__status": "Status", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__stockDetailStatus": "Stock detail status", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__sublot": "Sublot", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__timeTracking": "Automatic time tracking", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__totalActualQuantity": "Completed qty", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____columns__title__workOrder__name": "Work order", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____dropdownActions__title": "Stock details", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____dropdownActions__title__2": "Dimensions", "@sage/xtrem-manufacturing/pages__production_tracking__workOrders____title": "Work orders", "@sage/xtrem-manufacturing/pages__production_tracking__woSite____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__production_tracking__woSite____title": "Site", "@sage/xtrem-manufacturing/pages__production_tracking__woStartDateFrom____title": "Start date from", "@sage/xtrem-manufacturing/pages__production_tracking__woStartDateTo____title": "Start date to", "@sage/xtrem-manufacturing/pages__production_tracking__woStatus____placeholder": "Select status", "@sage/xtrem-manufacturing/pages__production_tracking__woStatus____title": "Status", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry____navigationPanel__listItem__itemID__title": "Item ID", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry____navigationPanel__listItem__line2__title": "Work order", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry____navigationPanel__listItem__line3__title": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry____navigationPanel__listItem__line3Right__title": "Site name", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry____navigationPanel__listItem__siteID__title": "Site ID", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry____navigationPanel__listItem__titleRight__title": "Stock status", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry____navigationPanel__listItem__workOrderName__title": "Work order name", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry____objectTypePlural": "Production receipts", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry____objectTypeSingular": "Production receipt", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry____title": "Production receipt", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__effectiveDate____title": "Date", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__financialSite____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__financialSite____title": "Financial site", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____columns__columns__item__name__columns__title__decimalDigits": "Decimal digits", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____columns__columns__item__name__columns__title__symbol": "Symbol", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____columns__columns__item__name__title__2": "Unit", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____columns__columns__item__name__title__3": "Lot management", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____columns__title__item__description": "Item description", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____columns__title__item__id": "Item ID", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____columns__title__orderCost": "Order cost", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____columns__title__releasedQuantity": "Quantity", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____columns__title__valuedCost": "Valued cost", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____dropdownActions__title": "Stock details", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____dropdownActions__title__2": "Dimensions", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__lines____title": "Items", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__mainSection____title": "General", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__number____title": "Number", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__post____title": "Post", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__releasedItem____title": "Item name", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__saveStockDetail____title": "Save", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__site____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__site____title": "Site", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__stockTransactionStatus____title": "Stock status", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__workOrder____columns__title__number": "Number", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__workOrder____lookupDialogTitle": "Select work order", "@sage/xtrem-manufacturing/pages__production_tracking_inquiry__workOrder____title": "Work order", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__dropdownActions__title": "Costs", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__bom__lookupDialogTitle": "Select bill of material", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__bom__title": "Bill of material", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__bomId__title": "BOM ID", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__bomLink__title": "Bill of material link", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__completed__title": "Completed", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__currency__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__itemId__title": "Item ID", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__itemName__title": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__orderCost__title": "Order cost", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__quantity__title": "Quantity", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__routing__title": "Routing", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__routingId__title": "Routing ID", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__routingLink__title": "Routing link", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__site__title": "Site name", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__siteId__title": "Site ID", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__stockStatus__title": "Stock status", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__stockUnit__title": "Unit", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__trackingDate__title": "Tracking date", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__trackingNumber__title": "Production tracking", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__trackingNumberLink__title": "Production tracking link", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__valuedCost__title": "Valued cost", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__workOrder__columns__title__number": "Number", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__workOrder__title": "Work order", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__workOrderCategory__title": "Work order category", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__workOrderNumber__title": "Work order link", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____navigationPanel__listItem__workOrderStatus__title": "Work order status", "@sage/xtrem-manufacturing/pages__production_tracking_line_inquiry____title": "Production tracking line inquiry", "@sage/xtrem-manufacturing/pages__return_analysis_start_date_setting____title": "Start date settings", "@sage/xtrem-manufacturing/pages__return_analysis_start_date_setting__save____title": "Save settings", "@sage/xtrem-manufacturing/pages__return_analysis_start_date_setting__startDate____title": "Start date", "@sage/xtrem-manufacturing/pages__time_tracking____title": "Time tracking", "@sage/xtrem-manufacturing/pages__time_tracking__addOperation____title": "Add operation", "@sage/xtrem-manufacturing/pages__time_tracking__createTrackings____title": "Generate", "@sage/xtrem-manufacturing/pages__time_tracking__criteriaBlock____title": "Criteria", "@sage/xtrem-manufacturing/pages__time_tracking__defaultDimension____title": "Set default dimensions", "@sage/xtrem-manufacturing/pages__time_tracking__itemFrom____columns__title__item__category__name": "Category", "@sage/xtrem-manufacturing/pages__time_tracking__itemFrom____columns__title__item__name": "Name", "@sage/xtrem-manufacturing/pages__time_tracking__itemFrom____lookupDialogTitle": "Select item", "@sage/xtrem-manufacturing/pages__time_tracking__itemFrom____title": "From released item", "@sage/xtrem-manufacturing/pages__time_tracking__itemTo____columns__title__item__category__name": "Category", "@sage/xtrem-manufacturing/pages__time_tracking__itemTo____columns__title__item__name": "Name", "@sage/xtrem-manufacturing/pages__time_tracking__itemTo____lookupDialogTitle": "Select item", "@sage/xtrem-manufacturing/pages__time_tracking__itemTo____title": "To released item", "@sage/xtrem-manufacturing/pages__time_tracking__mainSection____title": "General", "@sage/xtrem-manufacturing/pages__time_tracking__on_completed_quantity_changed_warn": "Quantity completed is greater than that of the previous operation", "@sage/xtrem-manufacturing/pages__time_tracking__on_completed_quantity_changed_warn_title": "Completed quantity", "@sage/xtrem-manufacturing/pages__time_tracking__operationResourceStatus____placeholder": "Select status", "@sage/xtrem-manufacturing/pages__time_tracking__operationResourceStatus____title": "Status", "@sage/xtrem-manufacturing/pages__time_tracking__site____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__time_tracking__site____placeholder": "Select site", "@sage/xtrem-manufacturing/pages__time_tracking__site____title": "Site", "@sage/xtrem-manufacturing/pages__time_tracking__woNumberFrom____columns__title__number": "Number", "@sage/xtrem-manufacturing/pages__time_tracking__woNumberFrom____lookupDialogTitle": "Select work order", "@sage/xtrem-manufacturing/pages__time_tracking__woNumberFrom____title": "Work order from", "@sage/xtrem-manufacturing/pages__time_tracking__woNumberTo____columns__title__number": "Number", "@sage/xtrem-manufacturing/pages__time_tracking__woNumberTo____lookupDialogTitle": "Select work order", "@sage/xtrem-manufacturing/pages__time_tracking__woNumberTo____title": "Work order to", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__columns__expectedResource__id__title": "Code", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__columns__expectedResource__id__title__2": "Description", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__columns__expectedResource__id__title__3": "Type", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__columns__runTimeUnit__symbol__title": "Name", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__columns__runTimeUnit__symbol__title__2": "Description", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__columns__setupTimeUnit__symbol__title": "Name", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__columns__setupTimeUnit__symbol__title__2": "Description", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__columns__workOrderOperation__minCapabilityLevel__id__title": "Code", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__columns__workOrderOperation__minCapabilityLevel__id__title__2": "Description", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__columns__workOrderOperation__workOrder__number__title": "Name", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__columns__workOrderOperation__workOrder__number__title__2": "Number", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__actualQuantity": "Actual quantity", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__actualRunTime": "Actual run time", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__actualSetupTime": "Actual setup time", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__completedQuantity": "Completed quantity", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__expectedResource__id": "Actual resource", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__expectedRunTime": "Planned run time", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__expectedSetupTime": "Planned setup time", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__isCompleted": "Completed", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__plannedQuantity": "Planned quantity", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__runTimeUnit__symbol": "Run time unit", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__setupTimeUnit__symbol": "Setup time unit", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__status": "Status", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__workOrderOperation__minCapabilityLevel__id": "Capability level", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__workOrderOperation__name": "Description", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__workOrderOperation__operationNumber": "Operation number", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__workOrderOperation__workOrder__number": "Number", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__workOrderOperation__workOrder__routingCode__item__description": "Released item description", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__workOrderOperation__workOrder__routingCode__item__id": "Released item ID", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____columns__title__workOrderOperation__workOrder__routingCode__item__name": "Released item", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____dropdownActions__title": "Dimensions", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____dropdownActions__title__2": "Delete", "@sage/xtrem-manufacturing/pages__time_tracking__workOrderOperations____title": "Work order operations", "@sage/xtrem-manufacturing/pages__time_tracking__woStartFrom____title": "Work order start date from", "@sage/xtrem-manufacturing/pages__time_tracking__woStartTo____title": "Work order start date to", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry____navigationPanel__listItem__itemID__title": "Item ID", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry____navigationPanel__listItem__line2__title": "Work order", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry____navigationPanel__listItem__line2Right__title": "Date", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry____navigationPanel__listItem__line3__title": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry____navigationPanel__listItem__line3Right__title": "Site name", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry____navigationPanel__listItem__siteID__title": "Site ID", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry____navigationPanel__listItem__workOrderName__title": "Work order name", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry____objectTypePlural": "Time trackings", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry____objectTypeSingular": "Time tracking", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry____title": "Time tracking", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__effectiveDate____title": "Date", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__lines____columns__columns__actualResource__name__title": "Name", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__lines____columns__columns__actualResource__name__title__2": "ID", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__lines____columns__title__actualResource___factory__title": "Resource type", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__lines____columns__title__actualResource__id": "Resource ID", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__lines____columns__title__actualResource__name": "Resource name", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__lines____columns__title__actualRunTime": "Run time", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__lines____columns__title__actualSetupTime": "Setup time", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__lines____columns__title__completedQuantity": "Quantity", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__lines____columns__title__workOrderOperation__operationNumber": "Operation number", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__lines____dropdownActions__title": "Dimensions", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__lines____title": "Operations", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__mainSection____title": "General", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__number____title": "Number", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__releasedItem____title": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__site____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__site____title": "Site", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__workOrder____lookupDialogTitle": "Select work order", "@sage/xtrem-manufacturing/pages__time_tracking_inquiry__workOrder____title": "Work order", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__dropdownActions__title": "Costs", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__completed__title": "Completed", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__operationNumber__title": "Operation number", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__operationStatus__title": "Operation status", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__quantity__title": "Quantity", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__resource__columns__title__id": "ID", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__resource__columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__resource__title": "Resource name", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__resourceId__title": "Resource ID", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__resourceType__title": "Resource type", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__routing__title": "Routing name", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__routingId__title": "Routing ID", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__runTime__title": "Run time", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__runTimeUnit__title": "Run time unit", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__setupTime__title": "Setup time", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__setupTimeUnit__title": "Setup time unit", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__site__title": "Site name", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__siteId__title": "Site ID", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__stockUnit__title": "Unit", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__title__title": "Tracking date", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__trackingNumber__title": "Time tracking", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__trackingNumberLink__title": "Time tracking", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__workOrder__columns__columns__productionItem___id__columns__title__stockUnit__name": "Unit", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__workOrder__columns__columns__productionItem___id__title": "Released item", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__workOrder__columns__title__productionItem___id": "Released item", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__workOrder__title": "Work order", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____navigationPanel__listItem__workOrderStatus__title": "Work order status", "@sage/xtrem-manufacturing/pages__time_tracking_line_inquiry____title": "Time tracking line inquiry", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation____title": "New work order operation", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__actualQuantity____title": "Actual quantity", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__actualRunTime____title": "Actual run time", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__actualSetupTime____title": "Actual setup time", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__cancel____title": "Cancel", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__expectedResource____columns__title__id": "ID", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__expectedResource____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__expectedResource____columns__title__weeklyShift__id": "Weekly shift", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__expectedResource____lookupDialogTitle": "Select resource", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__expectedResource____title": "Resource", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__minCapabilityLevel____columns__title__description": "Description", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__minCapabilityLevel____columns__title__id": "Capability level", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__minCapabilityLevel____columns__title__level": "Level", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__minCapabilityLevel____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__minCapabilityLevel____lookupDialogTitle": "Select capability level", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__minCapabilityLevel____title": "Capability level", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__operationName____title": "Operation description", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__runTimeUnit____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__runTimeUnit____columns__title__symbol": "Symbol", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__runTimeUnit____lookupDialogTitle": "Select unit of measure", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__runTimeUnit____title": "Run time unit", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__save____title": "OK", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__setupTimeUnit____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__setupTimeUnit____columns__title__symbol": "Symbol", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__setupTimeUnit____lookupDialogTitle": "Select unit of measure", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__setupTimeUnit____title": "Setup time unit", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__workOrder____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__workOrder____columns__title__number": "Number", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__workOrder____lookupDialogTitle": "Select work order", "@sage/xtrem-manufacturing/pages__time_tracking_work_order_operation__workOrder____title": "Work order number", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel____title": "Tracking costs", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__costs____columns__columns__workOrder__number__title": "Name", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__costs____columns__columns__workOrder__number__title__2": "Number", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__costs____columns__title___id": "Document", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__costs____columns__title__amount": "Amount", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__costs____columns__title__cost": "Cost", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__costs____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__costs____columns__title__effectiveDate": "Date", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__costs____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__costs____columns__title__quantity": "Quantity", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__costs____columns__title__releasedItemId": "Released item ID", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__costs____columns__title__type": "Type", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__costs____columns__title__unit__symbol": "Unit", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__costs____columns__title__workOrder__number": "Work order", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__costs____title": "Work in progress costs", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__mainBlock____title": "Tracking costs", "@sage/xtrem-manufacturing/pages__wip_cost_inquiry_panel__mainSection____title": "General", "@sage/xtrem-manufacturing/pages__wip_inquiry____title": "Work in progress inquiry", "@sage/xtrem-manufacturing/pages__wip_inquiry__asOfDate____title": "As of date", "@sage/xtrem-manufacturing/pages__wip_inquiry__criteriaBlock____title": "Criteria", "@sage/xtrem-manufacturing/pages__wip_inquiry__executionDate____title": "Last run date", "@sage/xtrem-manufacturing/pages__wip_inquiry__fromWorkOrder____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__wip_inquiry__fromWorkOrder____columns__title__number": "Number", "@sage/xtrem-manufacturing/pages__wip_inquiry__fromWorkOrder____title": "From work order", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__postfix__laborCostVariancePercentage": "%", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__postfix__machineCostVariancePercentage": "%", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__postfix__materialCostVariancePercentage": "%", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__postfix__processCostVariancePercentage": "%", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__postfix__toolCostVariancePercentage": "%", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__postfix__totalCostVariancePercentage": "%", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__actualLaborCost": "Actual labor cost", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__actualMachineCost": "Actual machine cost", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__actualMaterialCost": "Actual material cost", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__actualProcessCost": "Actual process cost", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__actualToolCost": "Actual tool cost", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__closingVariance": "Closing variance", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__item__releasedItem__description": "Item description", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__item__releasedItem__id": "Item ID", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__item__releasedItem__name": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__laborCostVariance": "Labor cost variance", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__laborCostVariancePercentage": "Labor cost variance %", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__machineCostVariance": "Machine cost variance", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__machineCostVariancePercentage": "Machine cost variance %", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__materialCostVariance": "Material cost variance", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__materialCostVariancePercentage": "Material cost variance %", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__plannedLaborCost": "Planned labor cost", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__plannedMachineCost": "Planned machine cost", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__plannedMaterialCost": "Planned material cost", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__plannedProcessCost": "Planned process cost", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__plannedToolCost": "Planned tool cost", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__processCostVariance": "Process cost variance", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__processCostVariancePercentage": "Process cost variance %", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__status": "Status", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__toolCostVariance": "Tool cost variance", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__toolCostVariancePercentage": "Tool cost variance %", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__totalActualCost": "Total actual cost", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__totalCostVariance": "Total cost variance", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__totalCostVariancePercentage": "Total cost variance %", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__totalPlannedCost": "Total planned cost", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__workInProgressTotal": "Work in progress total", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____columns__title__workOrder__number": "Work order", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____dropdownActions__title": "Stock journal", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____dropdownActions__title__2": "Journal entry", "@sage/xtrem-manufacturing/pages__wip_inquiry__lines____title": "Results", "@sage/xtrem-manufacturing/pages__wip_inquiry__mainSection____title": "General", "@sage/xtrem-manufacturing/pages__wip_inquiry__notification_inquiry_finished": "Work in progress inquiry finished.", "@sage/xtrem-manufacturing/pages__wip_inquiry__notification_request_sent": "Work in progress inquiry request sent.", "@sage/xtrem-manufacturing/pages__wip_inquiry__resultsSection____title": "Results", "@sage/xtrem-manufacturing/pages__wip_inquiry__runButton____title": "Run", "@sage/xtrem-manufacturing/pages__wip_inquiry__site____columns__title__id": "ID", "@sage/xtrem-manufacturing/pages__wip_inquiry__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-manufacturing/pages__wip_inquiry__site____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__wip_inquiry__site____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__wip_inquiry__site____title": "Site", "@sage/xtrem-manufacturing/pages__wip_inquiry__status____title": "Status", "@sage/xtrem-manufacturing/pages__wip_inquiry__toWorkOrder____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__wip_inquiry__toWorkOrder____columns__title__number": "Number", "@sage/xtrem-manufacturing/pages__wip_inquiry__toWorkOrder____title": "To work order", "@sage/xtrem-manufacturing/pages__wip_inquiry__user____title": "User", "@sage/xtrem-manufacturing/pages__wip_inquiry__workInProgressTotalAmount____title": "Work in progress total", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__amount__title": "Amount", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__cost__title": "Cost", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__currencyId__title": "Currency ID", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__currencyName__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__document__title": "Document", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__effectiveDate__title": "Date", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__processResourceType__title": "Process resource type", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__quantity__title": "Quantity", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__releasedItem__title": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__releasedItemDescription__title": "Item description", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__releasedItemId__title": "Item ID", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__siteId__title": "Site ID", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__siteName__title": "Site", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__status__title": "Status", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__title__title": "Tracking line", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__type__title": "Type", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__unit__title": "Unit", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____navigationPanel__listItem__workOrder__title": "Work order", "@sage/xtrem-manufacturing/pages__wip_transaction_inquiry____title": "Work in progress transaction inquiry", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__bulkActions__title": "Print job traveler", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__bulkActions__title__2": "Close orders", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__bulkActions__title__3": "Print pick list", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__listItem__category__title": "Category", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__listItem__endDate__title": "End date", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__listItem__image__title": "Image", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__listItem__itemDescription__title": "Item Description", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__listItem__itemName__title": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__listItem__line2__title": "Item ID", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__listItem__line2Right__title": "Status", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__listItem__releasedQuantity__title": "Released quantity", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__listItem__site__title": "Site", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__listItem__startDate__title": "Start date", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__listItem__totalActualQuantity__title": "Completed quantity", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__listItem__type__title": "Type", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__optionsMenu__title__2": "Pending", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__optionsMenu__title__3": "In progress", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__optionsMenu__title__4": "Completed", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__optionsMenu__title__5": "Closed", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__optionsMenu__title__6": "Cost calculated", "@sage/xtrem-manufacturing/pages__work_order____navigationPanel__optionsMenu__title__7": "Printed", "@sage/xtrem-manufacturing/pages__work_order____objectTypePlural": "Work orders", "@sage/xtrem-manufacturing/pages__work_order____objectTypeSingular": "Work order", "@sage/xtrem-manufacturing/pages__work_order____title": "Work order", "@sage/xtrem-manufacturing/pages__work_order__actualMaterialCost____title": "Actual material cost", "@sage/xtrem-manufacturing/pages__work_order__actualOverheadCost____title": "Actual overhead cost", "@sage/xtrem-manufacturing/pages__work_order__actualProcessCost____title": "Actual process cost", "@sage/xtrem-manufacturing/pages__work_order__addComponentLine____title": "Add", "@sage/xtrem-manufacturing/pages__work_order__additionalInfoBlock____title": "Planned vs. actual", "@sage/xtrem-manufacturing/pages__work_order__additionalSerialNumberQuantity____title": "Enter the quantity of additional serial numbers to pre-generate", "@sage/xtrem-manufacturing/pages__work_order__addNewOperationLine____title": "Add", "@sage/xtrem-manufacturing/pages__work_order__allocate____title": "Allocate stock", "@sage/xtrem-manufacturing/pages__work_order__allocationRequestStatus____title": "Allocation request status", "@sage/xtrem-manufacturing/pages__work_order__allocationStatus____title": "Allocation status", "@sage/xtrem-manufacturing/pages__work_order__bomCode____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__work_order__bomCode____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order__bomCode____lookupDialogTitle": "Select bill of material", "@sage/xtrem-manufacturing/pages__work_order__bomCode____title": "BOM", "@sage/xtrem-manufacturing/pages__work_order__bomRevision____title": "Revision", "@sage/xtrem-manufacturing/pages__work_order__cancel_button_text": "Cancel", "@sage/xtrem-manufacturing/pages__work_order__category____columns__title__billOfMaterial": "Bill of material", "@sage/xtrem-manufacturing/pages__work_order__category____columns__title__id": "ID", "@sage/xtrem-manufacturing/pages__work_order__category____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order__category____columns__title__routing": "Routing", "@sage/xtrem-manufacturing/pages__work_order__category____lookupDialogTitle": "Select category", "@sage/xtrem-manufacturing/pages__work_order__category____title": "Category", "@sage/xtrem-manufacturing/pages__work_order__closeWorkOrder____title": "Close order", "@sage/xtrem-manufacturing/pages__work_order__closingDate____title": "Date closed", "@sage/xtrem-manufacturing/pages__work_order__components_added_to_work_order": "Components added to work order {{workOrderNumber}}", "@sage/xtrem-manufacturing/pages__work_order__componentSection____title": "Components", "@sage/xtrem-manufacturing/pages__work_order__costs____columns__title__actualCost": "Actual", "@sage/xtrem-manufacturing/pages__work_order__costs____columns__title__cost": "Planned", "@sage/xtrem-manufacturing/pages__work_order__costs____columns__title__costTitle": "Cost", "@sage/xtrem-manufacturing/pages__work_order__costs____row__title__material_cost": "Material cost", "@sage/xtrem-manufacturing/pages__work_order__costs____row__title__process_cost": "Process cost", "@sage/xtrem-manufacturing/pages__work_order__costs____row__title__total": "Total", "@sage/xtrem-manufacturing/pages__work_order__costs____title": "Planned vs. actual", "@sage/xtrem-manufacturing/pages__work_order__createWorkOrder____title": "Create", "@sage/xtrem-manufacturing/pages__work_order__creationDate____title": "Order created date", "@sage/xtrem-manufacturing/pages__work_order__deallocate____title": "Deallocate stock", "@sage/xtrem-manufacturing/pages__work_order__deleteWorkOrder____title": "Delete", "@sage/xtrem-manufacturing/pages__work_order__editWorkOrderReleasedItemDimensions____title": "Dimensions", "@sage/xtrem-manufacturing/pages__work_order__endDate____title": "End", "@sage/xtrem-manufacturing/pages__work_order__generalSection____title": "General", "@sage/xtrem-manufacturing/pages__work_order__generate_button_text": "Generate", "@sage/xtrem-manufacturing/pages__work_order__goToSysNotificationPage____title": "Retry", "@sage/xtrem-manufacturing/pages__work_order__hasBillOfMaterial____title": "Uses bill of material", "@sage/xtrem-manufacturing/pages__work_order__hasRouting____title": "Uses routing", "@sage/xtrem-manufacturing/pages__work_order__isForwardScheduling____title": "Forward scheduling", "@sage/xtrem-manufacturing/pages__work_order__isServiceOptionsSerialNumberActive____title": "Serial number service option active", "@sage/xtrem-manufacturing/pages__work_order__mainBlock____title": "General information", "@sage/xtrem-manufacturing/pages__work_order__materialCompletionPercentage____title": "% material completion", "@sage/xtrem-manufacturing/pages__work_order__name____title": "Name", "@sage/xtrem-manufacturing/pages__work_order__note____title": "Notes", "@sage/xtrem-manufacturing/pages__work_order__noteBlock____title": "Notes", "@sage/xtrem-manufacturing/pages__work_order__noteSection____title": "Notes", "@sage/xtrem-manufacturing/pages__work_order__number____title": "Work order number", "@sage/xtrem-manufacturing/pages__work_order__operations_added_to_work_order": "Operations added to work order {{workOrderNumber}}", "@sage/xtrem-manufacturing/pages__work_order__operationSection____title": "Operations", "@sage/xtrem-manufacturing/pages__work_order__orderToOrderIcon____title": "Assigned to", "@sage/xtrem-manufacturing/pages__work_order__plannedMaterialCost____title": "Planned material cost", "@sage/xtrem-manufacturing/pages__work_order__plannedOverheadCost____title": "Planned overhead cost", "@sage/xtrem-manufacturing/pages__work_order__plannedProcessCost____title": "Planned process cost", "@sage/xtrem-manufacturing/pages__work_order__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-manufacturing/pages__work_order__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-manufacturing/pages__work_order__postingDetails____columns__title__sourceDocumentNumber": "Tracking number", "@sage/xtrem-manufacturing/pages__work_order__postingDetails____columns__title__sourceDocumentType": "Tracking type", "@sage/xtrem-manufacturing/pages__work_order__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-manufacturing/pages__work_order__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-manufacturing/pages__work_order__postingDetails____title": "Results", "@sage/xtrem-manufacturing/pages__work_order__postingMessageBlock____title": "Error detail", "@sage/xtrem-manufacturing/pages__work_order__postingSection____title": "Posting", "@sage/xtrem-manufacturing/pages__work_order__preGenerateAdditionalSerialNumbers____title": "Pregenerate additional serial numbers", "@sage/xtrem-manufacturing/pages__work_order__preGenerateAdditionalSerialNumbersBlock____title": "Additional serial numbers", "@sage/xtrem-manufacturing/pages__work_order__preGenerateAdditionalSerialNumbersSection____title": "Pregenerate additional serial numbers", "@sage/xtrem-manufacturing/pages__work_order__preGeneratedSerialNumbersQuantity____title": "Pregenerated serial number quantity", "@sage/xtrem-manufacturing/pages__work_order__preGenerateSerialNumbers____title": "Pregenerate serial numbers", "@sage/xtrem-manufacturing/pages__work_order__print____title": "Job traveler", "@sage/xtrem-manufacturing/pages__work_order__printPickList____title": "Pick list", "@sage/xtrem-manufacturing/pages__work_order__processCompletionPercentage____title": "% process completion", "@sage/xtrem-manufacturing/pages__work_order__productionCompletionPercentage____title": "Production", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__columns__operation__operationNumber__title": "Operation number", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__columns__operation__operationNumber__title__2": "Name", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__actualCost": "Actual cost", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__allocationRequestStatus": "Allocation request status", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__allocationStatus": "Allocation status", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__availableQuantityInStockUnit": "Available stock quantity", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__completedQuantityPercentage": "Progress", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__componentNumber": "Component number", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__consumedQuantity": "Quantity consumed", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__isAdded": "Added", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__item__description": "Item description", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__item__id": "Item ID", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__lineStatus": "Status", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__name": "Component description", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__operation__operationNumber": "Operation", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__plannedCost": "Planned cost", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__requiredQuantity": "Required quantity", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__scrapFactor": "Scrap factor %", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____columns__title__stockShortageInStockUnit": "Stock shortage", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____dropdownActions__title__allocateStock": "Allocate stock", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____dropdownActions__title__delete": "Delete", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____dropdownActions__title__dimensions": "Dimensions", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____dropdownActions__title__edit": "Edit", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____dropdownActions__title__exclude": "Exclude", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____dropdownActions__title__projectedStock": "Projected stock", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____dropdownActions__title__tick": "Include", "@sage/xtrem-manufacturing/pages__work_order__productionComponents____title": "Components", "@sage/xtrem-manufacturing/pages__work_order__productionComponentsDefaultDimension____title": "Set default dimensions", "@sage/xtrem-manufacturing/pages__work_order__productionItems____columns__columns__releasedItem__id__title": "Name", "@sage/xtrem-manufacturing/pages__work_order__productionItems____columns__columns__stockUnit__symbol__title": "Name", "@sage/xtrem-manufacturing/pages__work_order__productionItems____columns__title__releasedItem__id": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__work_order__productionItems____columns__title__releasedQuantity": "Released quantity", "@sage/xtrem-manufacturing/pages__work_order__productionItems____columns__title__stockUnit__symbol": "Unit", "@sage/xtrem-manufacturing/pages__work_order__productionItems____columns__title__totalActualQuantity": "Completed quantity", "@sage/xtrem-manufacturing/pages__work_order__productionItems____title": "Items", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__resource__id__title": "ID", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__resource__id__title__2": "Description", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__resource__id__title__3": "Name", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__runTimeUnit__symbol__title": "Name", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__runTimeUnit__symbol__title__2": "Symbol", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__runTimeUnit__symbol__title__3": "Description", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__runTimeUnit__symbol__title__4": "Name", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__runTimeUnit__symbol__title__5": "Symbol", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__runTimeUnit__symbol__title__6": "Description", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__setupTimeUnit__symbol__title": "Name", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__setupTimeUnit__symbol__title__2": "Symbol", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__setupTimeUnit__symbol__title__3": "Description", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__setupTimeUnit__symbol__title__4": "Name", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__setupTimeUnit__symbol__title__5": "Symbol", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__columns__setupTimeUnit__symbol__title__6": "Description", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title___id": "Start time", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title___id__2": "End time", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title___id__3": "Start time", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title___id__4": "End time", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__actualBatchCost": "Actual cost", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__actualCost": "Actual cost", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__actualRunTime": "Actual run time", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__actualRunTime__2": "Actual run time", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__actualSetupTime": "Actual setup time", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__actualSetupTime__2": "Actual setup time", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__completedQuantity": "Completed quantity", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__completedTimePercentage": "Progress", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__completedTimePercentage__2": "Progress", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__expectedBatchCost": "Planned cost", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__expectedCost": "Planned cost", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__expectedRunTime": "Planned run time", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__expectedRunTime__2": "Planned run time", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__expectedSetupTime": "Planned setup time", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__expectedSetupTime__2": "Planned setup time", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__isAdded": "Added", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__isAdded__2": "Added", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__isProductionStep": "Production step", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__isResourceQuantity": "Is resource quantity", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__operationNumber": "Operation number", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__plannedQuantity": "Planned quantity", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__resource__activeFrom": "Active from", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__resource__activeTo": "Active to", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__resource__description": "Description", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__resource__id": "ID", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__resource__id__2": "ID", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__resource__name": "Name", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__resource__name__2": "Name", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__resource__resourceGroup__type": "Type", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__resourceGroupNumber": "Resource group number", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__resourceNumber": "Resource number", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__runTimeUnit__symbol": "Run time unit", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__runTimeUnit__symbol__2": "Run time unit", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__setupTimeUnit__symbol": "Setup time unit", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__setupTimeUnit__symbol__2": "Setup time unit", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__status": "Status", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__columns__title__status__2": "Status", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__dropdownActions__title__add": "Add", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__dropdownActions__title__childDelete": "Delete", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__dropdownActions__title__childDimension": "Dimensions", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__dropdownActions__title__childEdit": "Edit", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__dropdownActions__title__childExclude": "Exclude", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__dropdownActions__title__childInclude": "Include", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__dropdownActions__title__delete": "Delete", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__dropdownActions__title__edit": "Edit", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__dropdownActions__title__exclude": "Exclude", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____levels__dropdownActions__title__include": "Include", "@sage/xtrem-manufacturing/pages__work_order__productionOperations____title": "Operations", "@sage/xtrem-manufacturing/pages__work_order__productionOperationsDefaultDimension____title": "Set default dimensions", "@sage/xtrem-manufacturing/pages__work_order__released_quantity_less_than_completed_quantity": "The released quantity is less than the completed quantity.", "@sage/xtrem-manufacturing/pages__work_order__releasedItem____lookupDialogTitle": "Select item", "@sage/xtrem-manufacturing/pages__work_order__releasedItem____title": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__work_order__releasedItemQuantity____title": "Quantity released", "@sage/xtrem-manufacturing/pages__work_order__repost____title": "Repost", "@sage/xtrem-manufacturing/pages__work_order__repost_errors": "Errors while reposting: {{errors}}", "@sage/xtrem-manufacturing/pages__work_order__requestedDate____title": "Requested start date", "@sage/xtrem-manufacturing/pages__work_order__routingCode____columns__title__doSerialNumberPreGeneration": "Pregenerate serial numbers", "@sage/xtrem-manufacturing/pages__work_order__routingCode____title": "Routing", "@sage/xtrem-manufacturing/pages__work_order__routingTimeUnit____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order__routingTimeUnit____columns__title__symbol": "Symbol", "@sage/xtrem-manufacturing/pages__work_order__routingTimeUnit____lookupDialogTitle": "Select unit of measure", "@sage/xtrem-manufacturing/pages__work_order__routingTimeUnit____title": "Time unit", "@sage/xtrem-manufacturing/pages__work_order__saveWorkOrder____title": "Save", "@sage/xtrem-manufacturing/pages__work_order__scheduleWorkOrder____title": "Schedule", "@sage/xtrem-manufacturing/pages__work_order__schedulingStatus____title": "Scheduling status", "@sage/xtrem-manufacturing/pages__work_order__site____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__work_order__startDate____title": "Start", "@sage/xtrem-manufacturing/pages__work_order__status____title": "Status", "@sage/xtrem-manufacturing/pages__work_order__stockTransactionStatus____title": "Stock status", "@sage/xtrem-manufacturing/pages__work_order__technicalBlock____title": "Technical", "@sage/xtrem-manufacturing/pages__work_order__timeZone____title": "Time zone", "@sage/xtrem-manufacturing/pages__work_order__totalActualQuantity____title": "Quantity completed", "@sage/xtrem-manufacturing/pages__work_order__trackingsSection____title": "Trackings", "@sage/xtrem-manufacturing/pages__work_order__trackingView____columns__title__date": "Date", "@sage/xtrem-manufacturing/pages__work_order__trackingView____columns__title__number": "Document number", "@sage/xtrem-manufacturing/pages__work_order__trackingView____columns__title__site__id": "Site", "@sage/xtrem-manufacturing/pages__work_order__trackingView____columns__title__status": "Stock status", "@sage/xtrem-manufacturing/pages__work_order__trackingView____columns__title__type": "Document type", "@sage/xtrem-manufacturing/pages__work_order__trackingView____title": "Trackings", "@sage/xtrem-manufacturing/pages__work_order__type____title": "Type", "@sage/xtrem-manufacturing/pages__work_order__viewSerialNumbers____title": "View serial numbers", "@sage/xtrem-manufacturing/pages__work_order_assignment_details_panel____title": "Assigned orders", "@sage/xtrem-manufacturing/pages__work_order_assignment_details_panel__assignedQuantity____title": "Assigned quantity", "@sage/xtrem-manufacturing/pages__work_order_assignment_details_panel__assignmentLines____columns__title__demandDocumentLine__documentNumber": "Order", "@sage/xtrem-manufacturing/pages__work_order_assignment_details_panel__assignmentLines____columns__title__demandType": "Assigned to", "@sage/xtrem-manufacturing/pages__work_order_assignment_details_panel__assignmentLines____columns__title__demandWorkInProgress__expectedQuantity": "Quantity on order", "@sage/xtrem-manufacturing/pages__work_order_assignment_details_panel__assignmentLines____columns__title__quantityInStockUnit": "Assigned quantity", "@sage/xtrem-manufacturing/pages__work_order_assignment_details_panel__assignmentLines____columns__title__quantityNotAssigned": "Quantity not assigned", "@sage/xtrem-manufacturing/pages__work_order_assignment_details_panel__assignmentLines____dropdownActions__title": "Delete", "@sage/xtrem-manufacturing/pages__work_order_assignment_details_panel__assignmentLines____title": "Assigned orders", "@sage/xtrem-manufacturing/pages__work_order_assignment_details_panel__cancel____title": "Cancel", "@sage/xtrem-manufacturing/pages__work_order_assignment_details_panel__componentBlock____title": "General", "@sage/xtrem-manufacturing/pages__work_order_assignment_details_panel__confirm____title": "Save", "@sage/xtrem-manufacturing/pages__work_order_assignment_details_panel__remainingQuantity____title": "Remaining quantity", "@sage/xtrem-manufacturing/pages__work_order_assignment_details_panel__requiredQuantity____title": "Required quantity", "@sage/xtrem-manufacturing/pages__work_order_auto_allocate_message": "The allocation request was submitted.", "@sage/xtrem-manufacturing/pages__work_order_auto_allocate_message_title": "Allocation request submitted", "@sage/xtrem-manufacturing/pages__work_order_auto_deallocate_message": "The deallocation request was submitted.", "@sage/xtrem-manufacturing/pages__work_order_auto_deallocate_message_title": "Deallocation request submitted", "@sage/xtrem-manufacturing/pages__work_order_category____navigationPanel__listItem__billOfMaterial__title": "Bill of material", "@sage/xtrem-manufacturing/pages__work_order_category____navigationPanel__listItem__isDefault__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__work_order_category____navigationPanel__listItem__routing__title": "Routing", "@sage/xtrem-manufacturing/pages__work_order_category____objectTypePlural": "Work order categories", "@sage/xtrem-manufacturing/pages__work_order_category____objectTypeSingular": "Work order category", "@sage/xtrem-manufacturing/pages__work_order_category____title": "Work order category", "@sage/xtrem-manufacturing/pages__work_order_category__billOfMaterial____title": "Bill of material", "@sage/xtrem-manufacturing/pages__work_order_category__description____title": "Description", "@sage/xtrem-manufacturing/pages__work_order_category__id____title": "ID", "@sage/xtrem-manufacturing/pages__work_order_category__isDefault____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__work_order_category__mainSection____title": "General", "@sage/xtrem-manufacturing/pages__work_order_category__name____title": "Name", "@sage/xtrem-manufacturing/pages__work_order_category__routing____title": "Routing", "@sage/xtrem-manufacturing/pages__work_order_client_functions__date_is_in_the_past": "This date is in the past.", "@sage/xtrem-manufacturing/pages__work_order_close_panel____title": "Work order closing", "@sage/xtrem-manufacturing/pages__work_order_close_panel__closingDate____title": "Closing date", "@sage/xtrem-manufacturing/pages__work_order_close_panel__generate____title": "Generate", "@sage/xtrem-manufacturing/pages__work_order_component_panel____title": "Work order component", "@sage/xtrem-manufacturing/pages__work_order_component_panel__allocationRequestStatus____title": "Allocation request status", "@sage/xtrem-manufacturing/pages__work_order_component_panel__allocationStatus____title": "Allocation status", "@sage/xtrem-manufacturing/pages__work_order_component_panel__cancelComponent____title": "Cancel", "@sage/xtrem-manufacturing/pages__work_order_component_panel__componentNumber____title": "Component number", "@sage/xtrem-manufacturing/pages__work_order_component_panel__componentSection____title": "Information", "@sage/xtrem-manufacturing/pages__work_order_component_panel__createComponent____title": "OK", "@sage/xtrem-manufacturing/pages__work_order_component_panel__dimensions_button_text": "Dimensions", "@sage/xtrem-manufacturing/pages__work_order_component_panel__display_shortage_status_false": "Available stock", "@sage/xtrem-manufacturing/pages__work_order_component_panel__display_shortage_status_true": "Stock shortage", "@sage/xtrem-manufacturing/pages__work_order_component_panel__edit____title": "Edit work order component", "@sage/xtrem-manufacturing/pages__work_order_component_panel__instruction____title": "Instructions", "@sage/xtrem-manufacturing/pages__work_order_component_panel__isFixedLinkQuantity____title": "Fixed quantity", "@sage/xtrem-manufacturing/pages__work_order_component_panel__isStockManaged____title": "Stock management", "@sage/xtrem-manufacturing/pages__work_order_component_panel__item____columns__columns__stockUnit__description__title": "Name", "@sage/xtrem-manufacturing/pages__work_order_component_panel__item____columns__title__category__name": "Category", "@sage/xtrem-manufacturing/pages__work_order_component_panel__item____columns__title__isStockManaged": "Stock management", "@sage/xtrem-manufacturing/pages__work_order_component_panel__item____columns__title__stockUnit__description": "Stock unit", "@sage/xtrem-manufacturing/pages__work_order_component_panel__item____lookupDialogTitle": "Select item", "@sage/xtrem-manufacturing/pages__work_order_component_panel__item____title": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__work_order_component_panel__lineType____title": "Type", "@sage/xtrem-manufacturing/pages__work_order_component_panel__linkQuantity____title": "Link quantity", "@sage/xtrem-manufacturing/pages__work_order_component_panel__name____title": "Component description", "@sage/xtrem-manufacturing/pages__work_order_component_panel__new____title": "New work order component", "@sage/xtrem-manufacturing/pages__work_order_component_panel__operation____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_component_panel__operation____columns__title__operationNumber": "Operation number", "@sage/xtrem-manufacturing/pages__work_order_component_panel__operation____lookupDialogTitle": "Select work order operation", "@sage/xtrem-manufacturing/pages__work_order_component_panel__operation____title": "Operation", "@sage/xtrem-manufacturing/pages__work_order_component_panel__quantityAllocated____title": "Allocated quantity", "@sage/xtrem-manufacturing/pages__work_order_component_panel__quantityToAllocate____title": "Quantity to allocate", "@sage/xtrem-manufacturing/pages__work_order_component_panel__remainingQuantityToAllocate____title": "Remaining quantity", "@sage/xtrem-manufacturing/pages__work_order_component_panel__requiredQuantity____title": "Required quantity", "@sage/xtrem-manufacturing/pages__work_order_component_panel__scrapFactor____title": "Scrap factor %", "@sage/xtrem-manufacturing/pages__work_order_component_panel__stockAvailable____title": "Available stock", "@sage/xtrem-manufacturing/pages__work_order_component_panel__stockOnHand____title": "Stock on hand", "@sage/xtrem-manufacturing/pages__work_order_component_panel__stockSection____title": "Stock", "@sage/xtrem-manufacturing/pages__work_order_component_panel__stockShortage____title": "Stock shortage", "@sage/xtrem-manufacturing/pages__work_order_component_panel__stockShortageStatus____title": "Stock shortage status", "@sage/xtrem-manufacturing/pages__work_order_component_panel__unit____columns__title__symbol": "Symbol", "@sage/xtrem-manufacturing/pages__work_order_component_panel__unit____lookupDialogTitle": "Select unit of measure", "@sage/xtrem-manufacturing/pages__work_order_component_panel__unit____title": "Unit of measure", "@sage/xtrem-manufacturing/pages__work_order_delay_settings____title": "Work order delay settings", "@sage/xtrem-manufacturing/pages__work_order_delay_settings__delay____title": "Delay days", "@sage/xtrem-manufacturing/pages__work_order_delay_settings__save____title": "Save settings", "@sage/xtrem-manufacturing/pages__work_order_line_assignment_details_panel__assigned_quantity_higher_than_quantity_on_order": "You have assigned more than the quantity on order. You need to reduce the assigned quantity.", "@sage/xtrem-manufacturing/pages__work_order_line_assignment_details_panel__assigned_quantity_higher_than_work_order_release_quantity": "You have assigned more than the released quantity. You need to reduce the assigned quantity.", "@sage/xtrem-manufacturing/pages__work_order_line_assignment_details_panel__line_deletion_dialog_content": "You are about to delete the link to the order.", "@sage/xtrem-manufacturing/pages__work_order_line_assignment_details_panel__line_deletion_dialog_title": "Delete assigned line", "@sage/xtrem-manufacturing/pages__work_order_line_assignment_details_panel__line-cancel": "Cancel", "@sage/xtrem-manufacturing/pages__work_order_line_assignment_details_panel__line-delete": "Delete", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation____title": "Work order mass allocation", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__action____title": "Action", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__allocate____title": "Allocate", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__company____columns__title__id": "ID", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__company____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__company____lookupDialogTitle": "Select company", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__company____title": "Company", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__deallocate____title": "Deallocate", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__description____title": "Description", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromItem____columns__title__category__name": "Category", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromItem____lookupDialogTitle": "Select from item", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromItem____title": "From item", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromOperation____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromOperation____columns__title__operationNumber": "Operation number", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromOperation____columns__title__routing__name": "Routing", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromOperation____lookupDialogTitle": "Select from operation", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromOperation____title": "From operation", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromReleasedItem____columns__title__document__number": "Work order", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromReleasedItem____columns__title__document__site__name": "Site", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromReleasedItem____columns__title__lineStatus": "Status", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromReleasedItem____columns__title__releasedItem__name": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromReleasedItem____columns__title__releasedQuantity": "Required quantity", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromReleasedItem____columns__title__remainingQuantity": "Remaining quantity", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromReleasedItem____lookupDialogTitle": "Select from released item", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromReleasedItem____title": "From released item", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromWorkOrder____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromWorkOrder____columns__title__number": "Number", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromWorkOrder____lookupDialogTitle": "Select from work order", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__fromWorkOrder____title": "From work order", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__latestStartDate____title": "Latest start date", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__mainSection____title": "General", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__message": "The allocation request was submitted.", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__message_title": "Allocation request submitted", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__schedule____title": "Schedule", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__stockSite____columns__columns__legalCompany__name__title": "Name", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__stockSite____columns__columns__legalCompany__name__title__2": "ID", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__stockSite____columns__title__id": "ID", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__stockSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__stockSite____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__stockSite____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__stockSite____title": "Site", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toItem____columns__title__category__name": "Category", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toItem____columns__title__description": "Description", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toItem____columns__title__id": "ID", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toItem____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toItem____lookupDialogTitle": "Select to item", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toItem____title": "To item", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toOperation____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toOperation____columns__title__operationNumber": "Operation number", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toOperation____columns__title__routing__name": "Routing", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toOperation____lookupDialogTitle": "Select to operation", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toOperation____title": "To operation", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toReleasedItem____columns__title__document__number": "Work order", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toReleasedItem____columns__title__document__site__name": "Site", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toReleasedItem____columns__title__lineStatus": "Status", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toReleasedItem____columns__title__releasedItem__name": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toReleasedItem____columns__title__releasedQuantity": "Required quantity", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toReleasedItem____columns__title__remainingQuantity": "Remaining quantity", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toReleasedItem____lookupDialogTitle": "Select to released item", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toReleasedItem____title": "To released item", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toWorkOrder____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toWorkOrder____columns__title__number": "Number", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toWorkOrder____lookupDialogTitle": "Select to work order", "@sage/xtrem-manufacturing/pages__work_order_mass_allocation__toWorkOrder____title": "To work order", "@sage/xtrem-manufacturing/pages__work_order_operation_panel____title": "New operation", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__cancel____title": "Cancel", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__edit____title": "Edit work order operation", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__instruction____title": "Instructions", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__isProductionStep____title": "Production step", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__item____lookupDialogTitle": "Select item", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__item____title": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__minCapabilityLevel____columns__title__description": "Description", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__minCapabilityLevel____columns__title__id": "Capability level", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__minCapabilityLevel____columns__title__level": "Level", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__minCapabilityLevel____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__minCapabilityLevel____lookupDialogTitle": "Select capability level", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__minCapabilityLevel____title": "Capability level", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__name____title": "Name", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__new____title": "New work order operation", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__operationNumber____title": "Operation number", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__runTimeUnit____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__runTimeUnit____columns__title__symbol": "Symbol", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__runTimeUnit____lookupDialogTitle": "Select unit of measure", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__runTimeUnit____title": "Run time unit", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__save____title": "OK", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__setupTimeUnit____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__setupTimeUnit____columns__title__symbol": "Symbol", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__setupTimeUnit____lookupDialogTitle": "Select unit of measure", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__setupTimeUnit____title": "Setup time unit", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__site____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__site____title": "Site", "@sage/xtrem-manufacturing/pages__work_order_operation_panel__work_order_operation_already_exists": "Operation number {{operationNumber}} already exists", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel____title": "Operation resource", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__cancel____title": "Cancel", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__dimensions_button_text": "Dimensions", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__edit____title": "Edit work order operation resource", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__empty_resource_group": "The resource group must not be empty of resource. Please choose another group or add resource to the group.", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__expectedRunTime____title": "Run time", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__expectedSetupTime____title": "Setup time", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__isResourceQuantity____title": "Is resource quantity", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__new____title": "New work order operation resource", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__operation____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__operation____columns__title__operationNumber": "Operation number", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__operation____lookupDialogTitle": "Select work order operation", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__operation____title": "Operation", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resource____columns__title___constructor": "Type", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resource____columns__title__id": "ID", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resource____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resource____lookupDialogTitle": "Select resource group", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resource____title": "Resource group", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__activeFrom__title": "ID", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__activeFrom__title__2": "Description", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__activeFrom__title__3": "Name", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__activeTo__title": "ID", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__activeTo__title__2": "Description", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__activeTo__title__3": "Name", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__description__title": "ID", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__description__title__2": "Description", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__description__title__3": "Name", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__id__title": "ID", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__id__title__2": "Description", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__id__title__3": "Name", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__name__title": "ID", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__name__title__2": "Description", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__columns__resource__name__title__3": "Name", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__title__resource__activeFrom": "Active from", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__title__resource__activeTo": "Active to", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__title__resource__description": "Description", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__title__resource__id": "ID", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____columns__title__resource__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__resources____title": "Resources", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__runTimeUnit____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__runTimeUnit____columns__title__symbol": "Symbol", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__runTimeUnit____lookupDialogTitle": "Select unit of measure", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__runTimeUnit____title": "Time unit", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__save____title": "OK", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__setupTimeUnit____columns__title__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__setupTimeUnit____columns__title__symbol": "Symbol", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__setupTimeUnit____lookupDialogTitle": "Select unit of measure", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__setupTimeUnit____title": "Setup time unit", "@sage/xtrem-manufacturing/pages__work_order_operation_resource_panel__site____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__work_order_panel____title": "New work order", "@sage/xtrem-manufacturing/pages__work_order_panel__bomCode____columns__title__item__category__name": "Category", "@sage/xtrem-manufacturing/pages__work_order_panel__bomCode____columns__title__item__name": "Name", "@sage/xtrem-manufacturing/pages__work_order_panel__bomCode____lookupDialogTitle": "Select item", "@sage/xtrem-manufacturing/pages__work_order_panel__bomCode____title": "Released item", "@sage/xtrem-manufacturing/pages__work_order_panel__cancelWorkOrder____title": "Cancel", "@sage/xtrem-manufacturing/pages__work_order_panel__category____lookupDialogTitle": "Select category", "@sage/xtrem-manufacturing/pages__work_order_panel__category____title": "Category", "@sage/xtrem-manufacturing/pages__work_order_panel__createWorkOrder____title": "Create", "@sage/xtrem-manufacturing/pages__work_order_panel__functions__bill_of_material_not_found": "No bill of material available to use found.", "@sage/xtrem-manufacturing/pages__work_order_panel__functions_routing_not_found": "No routing available to use found.", "@sage/xtrem-manufacturing/pages__work_order_panel__projected_stock_button_text": "Projected stock", "@sage/xtrem-manufacturing/pages__work_order_panel__routing_bom_error": "Assign a routing or bill of material to this work order category.", "@sage/xtrem-manufacturing/pages__work_order_panel__workOrderEndDate____title": "End date", "@sage/xtrem-manufacturing/pages__work_order_panel__workOrderName____title": "Name", "@sage/xtrem-manufacturing/pages__work_order_panel__workOrderNumber____title": "Work order number", "@sage/xtrem-manufacturing/pages__work_order_panel__workOrderQuantity____title": "Quantity", "@sage/xtrem-manufacturing/pages__work_order_panel__workOrderSite____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__work_order_panel__workOrderSite____title": "Site", "@sage/xtrem-manufacturing/pages__work_order_panel__workOrderStartDate____title": "Requested start date", "@sage/xtrem-manufacturing/pages__work_order_panel__workOrderType____title": "Type", "@sage/xtrem-manufacturing/pages__work_order_pregenerate_additional_serial_number": "Pregenerate additional serial numbers", "@sage/xtrem-manufacturing/pages__work_order_pregenerate_serial_number": "Pregenerate serial numbers", "@sage/xtrem-manufacturing/pages__work_order_serial_number____title": "Pregenerated serial number", "@sage/xtrem-manufacturing/pages__work_order_serial_number__mainSection____title": "General", "@sage/xtrem-manufacturing/pages__work_order_serial_number__ok____title": "OK", "@sage/xtrem-manufacturing/pages__work_order_serial_number__serialNumbers____columns__lookupDialogTitle__serialNumber__id": "Select serial number", "@sage/xtrem-manufacturing/pages__work_order_serial_number__serialNumbers____columns__title__isInStock": "In stock", "@sage/xtrem-manufacturing/pages__work_order_serial_number__serialNumbers____columns__title__serialNumber__id": "Serial number", "@sage/xtrem-manufacturing/pages__work_order_serial_number__serialNumbers____title": "Serial numbers", "@sage/xtrem-manufacturing/pages__work_order_suggestion____title": "Work order suggestion", "@sage/xtrem-manufacturing/pages__work_order_suggestion__creation__success": "{{num}} work order updated", "@sage/xtrem-manufacturing/pages__work_order_suggestion__creation__success_multi": "{{num}} work orders updated", "@sage/xtrem-manufacturing/pages__work_order_suggestion__criteriaBlock____title": "Selection criteria", "@sage/xtrem-manufacturing/pages__work_order_suggestion__issueDateFrom____title": "Order date from", "@sage/xtrem-manufacturing/pages__work_order_suggestion__issueDateTo____title": "Order date to", "@sage/xtrem-manufacturing/pages__work_order_suggestion__item____columns__title__category__name": "Category", "@sage/xtrem-manufacturing/pages__work_order_suggestion__item____lookupDialogTitle": "Select item", "@sage/xtrem-manufacturing/pages__work_order_suggestion__item____title": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__work_order_suggestion__mainSection____title": "General", "@sage/xtrem-manufacturing/pages__work_order_suggestion__results____columns__title__endDate": "Start", "@sage/xtrem-manufacturing/pages__work_order_suggestion__results____columns__title__endDate__2": "End", "@sage/xtrem-manufacturing/pages__work_order_suggestion__results____columns__title__isForwardScheduling": "Forward scheduling", "@sage/xtrem-manufacturing/pages__work_order_suggestion__results____columns__title__number": "Work order number", "@sage/xtrem-manufacturing/pages__work_order_suggestion__results____columns__title__productionItem__releasedItemId": "Item ID", "@sage/xtrem-manufacturing/pages__work_order_suggestion__results____columns__title__productionItem__releasedItemName": "Item name", "@sage/xtrem-manufacturing/pages__work_order_suggestion__results____columns__title__productionItem__releasedQuantity": "Quantity", "@sage/xtrem-manufacturing/pages__work_order_suggestion__results____columns__title__type": "Convert to", "@sage/xtrem-manufacturing/pages__work_order_suggestion__results____title": "Purchase order suggestions", "@sage/xtrem-manufacturing/pages__work_order_suggestion__site____lookupDialogTitle": "Select site", "@sage/xtrem-manufacturing/pages__work_order_suggestion__site____placeholder": "Select site", "@sage/xtrem-manufacturing/pages__work_order_suggestion__site____title": "Site", "@sage/xtrem-manufacturing/pages__work_order_suggestion__updateWorkOrder____title": "Create", "@sage/xtrem-manufacturing/pages__work_order_summary____title": "Work order summary", "@sage/xtrem-manufacturing/pages__work_order_summary___id____title": "ID", "@sage/xtrem-manufacturing/pages__work_order_summary__costProducedDifference____title": "Cost change", "@sage/xtrem-manufacturing/pages__work_order_summary__currency____columns__title__id": "ISO 4217 code", "@sage/xtrem-manufacturing/pages__work_order_summary__currency____columns__title__symbol": "Symbol", "@sage/xtrem-manufacturing/pages__work_order_summary__currency____title": "Currency for cost values", "@sage/xtrem-manufacturing/pages__work_order_summary__currentCostProduced____title": "Current month cost", "@sage/xtrem-manufacturing/pages__work_order_summary__currentQuantityProduced____title": "Current month quantity", "@sage/xtrem-manufacturing/pages__work_order_summary__headerSection____title": "Header section", "@sage/xtrem-manufacturing/pages__work_order_summary__previousCostProduced____title": "Previous month cost", "@sage/xtrem-manufacturing/pages__work_order_summary__previousQuantityProduced____title": "Previous month quantity", "@sage/xtrem-manufacturing/pages__work_order_summary__quantityProducedDifference____title": "Quantity change", "@sage/xtrem-manufacturing/pages__work_order_summary__releasedItems____columns__title__currentMonthCost": "Current month cost", "@sage/xtrem-manufacturing/pages__work_order_summary__releasedItems____columns__title__currentMonthQuantity": "Current month quantity", "@sage/xtrem-manufacturing/pages__work_order_summary__releasedItems____columns__title__itemName": "<PERSON><PERSON>", "@sage/xtrem-manufacturing/pages__work_order_summary__releasedItems____columns__title__previousMonthCost": "Previous month cost", "@sage/xtrem-manufacturing/pages__work_order_summary__releasedItems____columns__title__previousMonthQuantity": "Previous month quantity", "@sage/xtrem-manufacturing/pages__work_order_summary__releasedItems____columns__title__releasedQuantity": "Required quantity", "@sage/xtrem-manufacturing/pages__work_order_summary__releasedItems____columns__title__remainingQuantity": "Remaining quantity", "@sage/xtrem-manufacturing/pages__work_order_summary__releasedItems____columns__title__siteName": "Site", "@sage/xtrem-manufacturing/pages__work_order_summary__releasedItems____columns__title__status": "Status", "@sage/xtrem-manufacturing/pages__work_order_summary__releasedItems____columns__title__workOrderNumber": "Work order", "@sage/xtrem-manufacturing/pages__work_order_summary__workOrderCount____title": "Work order count", "@sage/xtrem-manufacturing/pages__work_order_summary__workOrderSection____title": "Work orders", "@sage/xtrem-manufacturing/pages_work_order_pregenerate_serial_number__message": "You are about to generate {{qty}} serial number. Confirm generation?", "@sage/xtrem-manufacturing/pages_work_order_pregenerate_serial_numbers__confirmation": "Confirm pregeneration of serial numbers", "@sage/xtrem-manufacturing/pages_work_order_pregenerate_serial_numbers__confirmationButton": "Confirm pregeneration", "@sage/xtrem-manufacturing/pages_work_order_pregenerate_serial_numbers__message": "You are about to generate {{qty}} serial numbers. Confirm generation?", "@sage/xtrem-manufacturing/pages-confirm-cancel": "Cancel", "@sage/xtrem-manufacturing/pages-work-order_confirm_assignment_closing_dialog_content": "When you close this work order, the link to the original order remains.", "@sage/xtrem-manufacturing/pages-work-order_confirm_assignment_closing_dialog_title": "Close work order", "@sage/xtrem-manufacturing/pages-work-order_confirm_assignment_decreasing_quantity_dialog_content": "When you decrease the work order quantity, the quantity decreases on the linked original order.", "@sage/xtrem-manufacturing/pages-work-order_confirm_assignment_decreasing_quantity_dialog_title": "Decrease work order quantity", "@sage/xtrem-manufacturing/pages-work-order_confirm_assignment_deletion_dialog_content": "When you delete this work order, the link to the original order is also deleted.", "@sage/xtrem-manufacturing/pages-work-order_confirm_assignment_deletion_dialog_title": "Delete work order", "@sage/xtrem-manufacturing/pages-work-order-dialog-assignment-cancel": "Cancel", "@sage/xtrem-manufacturing/pages-work-order-dialog-assignment-continue": "Continue", "@sage/xtrem-manufacturing/permission__close__name": "Close", "@sage/xtrem-manufacturing/permission__create__name": "Create", "@sage/xtrem-manufacturing/permission__delete__name": "Delete", "@sage/xtrem-manufacturing/permission__manage__name": "Manage", "@sage/xtrem-manufacturing/permission__manage_cost__name": "Manage cost", "@sage/xtrem-manufacturing/permission__post_to_stock__name": "Post to stock", "@sage/xtrem-manufacturing/permission__read__name": "Read", "@sage/xtrem-manufacturing/permission__repost__name": "Repost", "@sage/xtrem-manufacturing/permission__tracking__name": "Tracking", "@sage/xtrem-manufacturing/permission__update__name": "Update", "@sage/xtrem-manufacturing/required-quantity-lesser": "The required quantity must be more than or equal to the quantity already consumed", "@sage/xtrem-manufacturing/search": "Search", "@sage/xtrem-manufacturing/skip_scheduling_question": "Skip scheduling for this work order?", "@sage/xtrem-manufacturing/skip_scheduling_title": "Change scheduling work flow", "@sage/xtrem-manufacturing/tracking_lib__resync__need_to_post_again": "The tracking needs to be posted to stock again: {{number}}.", "@sage/xtrem-manufacturing/updatePlannedCosts": "Update planned costs", "@sage/xtrem-manufacturing/warning__stock_transaction_status_in_progress": "Close work order", "@sage/xtrem-manufacturing/warning__stock_transaction_status_in_progress_continue": "Continue", "@sage/xtrem-manufacturing/warning__stock_transaction_status_in_progress_message": "You are about to change the status of this work order to Closed.", "@sage/xtrem-manufacturing/widgets__cost_produced_percentage____title": "Production cost percentage", "@sage/xtrem-manufacturing/widgets__made_item_return_reason_analysis____rowDefinition__line2Right__title": "", "@sage/xtrem-manufacturing/widgets__made_item_return_reason_analysis____rowDefinition__title__title": "Reason name", "@sage/xtrem-manufacturing/widgets__made_item_return_reason_analysis____rowDefinition__titleRight__title": "Returns", "@sage/xtrem-manufacturing/widgets__made_item_return_reason_analysis____title": "Production return reason analysis", "@sage/xtrem-manufacturing/widgets__production_cost_trend____primaryAxis__title": "Month", "@sage/xtrem-manufacturing/widgets__production_cost_trend____secondaryAxes__title": "Cost", "@sage/xtrem-manufacturing/widgets__production_cost_trend____title": "Production cost trend", "@sage/xtrem-manufacturing/widgets__production_cost_trend__cost_tooltip_title": "Cost", "@sage/xtrem-manufacturing/widgets__quantity_produced_percentage____title": "Quantity produced percentage", "@sage/xtrem-manufacturing/widgets__work_order_delay____dataDropdownMenu__orderBy__endDate__title": "Sort by end date", "@sage/xtrem-manufacturing/widgets__work_order_delay____dataDropdownMenu__orderBy__number__title": "Sort by number", "@sage/xtrem-manufacturing/widgets__work_order_delay____dataDropdownMenu__orderBy__status__title": "Sort by status", "@sage/xtrem-manufacturing/widgets__work_order_delay____rowDefinition__line2__title": "Status", "@sage/xtrem-manufacturing/widgets__work_order_delay____rowDefinition__line2Right__title": "Delay", "@sage/xtrem-manufacturing/widgets__work_order_delay____rowDefinition__title__title": "Work order", "@sage/xtrem-manufacturing/widgets__work_order_delay____rowDefinition__titleRight__title": "Released item", "@sage/xtrem-manufacturing/widgets__work_order_delay____title": "Work order delay", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____callToActions__seeAll__title": "See all", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____dataDropdownMenu__orderBy__endDate__title": "Sort by end date", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____dataDropdownMenu__orderBy__number__title": "Sort by number", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____dataDropdownMenu__orderBy__status__title": "Sort by status", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____rowDefinition__endDate__title": "End date", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____rowDefinition__line2__title": "Status", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____rowDefinition__line2Right__title": "WIP variance", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____rowDefinition__line3__title": "Stock transaction status", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____rowDefinition__line3Right__title": "Start date", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____rowDefinition__materialCompletionPercentage__title": "Material completion percentage", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____rowDefinition__processCompletionPercentage__title": "Process completion percentage", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____rowDefinition__productionCompletionPercentage__title": "Production completion percentage", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____rowDefinition__title__title": "Work order", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____rowDefinition__titleRight__title": "Released item", "@sage/xtrem-manufacturing/widgets__work_order_in_progress____title": "Work orders in progress", "@sage/xtrem-manufacturing/work_order_lib__resync__end": "Work order statuses sync finished", "@sage/xtrem-manufacturing/work_order_lib__resync__start": "Work order statuses sync start", "@sage/xtrem-manufacturing/work_order_lib__resync__stop_requested": "Work order resync: Stop requested at {{stopDate}}."}