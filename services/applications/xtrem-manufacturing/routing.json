{"@sage/xtrem-manufacturing": [{"topic": "MaterialTracking/accountingInterface", "queue": "manufacturing", "sourceFileName": "material-tracking.ts"}, {"topic": "MaterialTracking/asyncExport/start", "queue": "import-export", "sourceFileName": "material-tracking.ts"}, {"topic": "MaterialTracking/resendNotificationForFinance", "queue": "manufacturing", "sourceFileName": "material-tracking.ts"}, {"topic": "MaterialTracking/stock/issue/reply", "queue": "manufacturing", "sourceFileName": "material-tracking.ts"}, {"topic": "MaterialTrackingLine/asyncExport/start", "queue": "import-export", "sourceFileName": "material-tracking-line.ts"}, {"topic": "OperationTracking/accountingInterface", "queue": "manufacturing", "sourceFileName": "operation-tracking.ts"}, {"topic": "OperationTracking/asyncExport/start", "queue": "import-export", "sourceFileName": "operation-tracking.ts"}, {"topic": "OperationTracking/createMultipleOperationalTrackings/start", "queue": "manufacturing", "sourceFileName": "operation-tracking.ts"}, {"topic": "OperationTracking/resendNotificationForFinance", "queue": "manufacturing", "sourceFileName": "operation-tracking.ts"}, {"topic": "OperationTrackingLine/asyncExport/start", "queue": "import-export", "sourceFileName": "operation-tracking-line.ts"}, {"topic": "ProductionTracking/accountingInterface", "queue": "manufacturing", "sourceFileName": "production-tracking.ts"}, {"topic": "ProductionTracking/asyncExport/start", "queue": "import-export", "sourceFileName": "production-tracking.ts"}, {"topic": "ProductionTracking/createTestTracking/start", "queue": "manufacturing", "sourceFileName": "production-tracking.ts"}, {"topic": "ProductionTracking/resendNotificationForFinance", "queue": "manufacturing", "sourceFileName": "production-tracking.ts"}, {"topic": "ProductionTracking/stock/receipt/reply", "queue": "manufacturing", "sourceFileName": "production-tracking.ts"}, {"topic": "ProductionTrackingLine/asyncExport/start", "queue": "import-export", "sourceFileName": "production-tracking-line.ts"}, {"topic": "WorkInProgressCost/asyncExport/start", "queue": "import-export", "sourceFileName": "work-in-progress-cost.ts"}, {"topic": "WorkInProgressInputSet/asyncExport/start", "queue": "import-export", "sourceFileName": "work-in-progress-input-set.ts"}, {"topic": "WorkInProgressInputSet/wipInquiry/start", "queue": "manufacturing", "sourceFileName": "work-in-progress-input-set.ts"}, {"topic": "WorkInProgressResultLine/asyncExport/start", "queue": "import-export", "sourceFileName": "work-in-progress-result-line.ts"}, {"topic": "WorkInProgressWorkOrderComponent/asyncExport/start", "queue": "import-export", "sourceFileName": "work-in-progress-work-order-component.ts"}, {"topic": "WorkInProgressWorkOrderReleasedItem/asyncExport/start", "queue": "import-export", "sourceFileName": "work-in-progress-work-order-released-item.ts"}, {"topic": "WorkOrder/allocation/reply", "queue": "manufacturing", "sourceFileName": "work-order.ts"}, {"topic": "WorkOrder/asyncExport/start", "queue": "import-export", "sourceFileName": "work-order.ts"}, {"topic": "WorkOrder/bulkPrintPickList/start", "queue": "reporting", "sourceFileName": "work-order.ts"}, {"topic": "WorkOrder/close", "queue": "manufacturing", "sourceFileName": "work-order.ts"}, {"topic": "WorkOrder/closeWorkOrder/start", "queue": "manufacturing", "sourceFileName": "work-order.ts"}, {"topic": "WorkOrder/createTestWorkOrders/start", "queue": "manufacturing", "sourceFileName": "work-order.ts"}, {"topic": "WorkOrder/printBulk/start", "queue": "reporting", "sourceFileName": "work-order.ts"}, {"topic": "WorkOrder/resendNotificationForFinance", "queue": "manufacturing", "sourceFileName": "work-order.ts"}, {"topic": "WorkOrder/resynchronizeStatus/start", "queue": "manufacturing", "sourceFileName": "work-order.ts"}, {"topic": "WorkOrder/stock/correction/reply", "queue": "manufacturing", "sourceFileName": "work-order.ts"}, {"topic": "WorkOrder/trackBillOfMaterial/start", "queue": "manufacturing", "sourceFileName": "work-order.ts"}, {"topic": "WorkOrderCategory/asyncExport/start", "queue": "import-export", "sourceFileName": "work-order-category.ts"}, {"topic": "WorkOrderClosing/accountingInterface", "queue": "manufacturing", "sourceFileName": "work-order.ts"}, {"topic": "WorkOrderComponent/asyncExport/start", "queue": "import-export", "sourceFileName": "work-order-component.ts"}, {"topic": "WorkOrderComponent/massAutoAllocation/start", "queue": "manufacturing", "sourceFileName": "work-order-component.ts"}, {"topic": "WorkOrderOperation/asyncExport/start", "queue": "import-export", "sourceFileName": "work-order-operation.ts"}, {"topic": "WorkOrderOperationResource/asyncExport/start", "queue": "import-export", "sourceFileName": "work-order-operation-resource.ts"}, {"topic": "WorkOrderOperationResourceDetail/asyncExport/start", "queue": "import-export", "sourceFileName": "work-order-operation-resource-detail.ts"}, {"topic": "WorkOrderReleasedItem/asyncExport/start", "queue": "import-export", "sourceFileName": "work-order-released-item.ts"}, {"topic": "WorkOrderSerialNumber/asyncExport/start", "queue": "import-export", "sourceFileName": "work-order-serial-number.ts"}, {"topic": "WorkOrderView/asyncExport/start", "queue": "import-export", "sourceFileName": "work-order-view.ts"}]}