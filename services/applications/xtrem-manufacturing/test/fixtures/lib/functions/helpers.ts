import type * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremManufacturing from '../../../../lib';

type MaterialTrackingLineStatus = {
    trackingNumber: string;
    lineSortValue: number;
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus;
};

const getMaterialTrackingLineStatus = async (
    materialTrackingLine: xtremManufacturing.nodes.MaterialTrackingLine,
): Promise<MaterialTrackingLineStatus> => ({
    trackingNumber: await (await materialTrackingLine.document).number,
    lineSortValue: await materialTrackingLine._sortValue,
    stockTransactionStatus: await materialTrackingLine.stockTransactionStatus,
});

/**
 * returns the stockTransactionStatus of all material tracking lines of a work order
 */
export async function getMaterialTrackingLineStatuses(
    workOrder: xtremManufacturing.nodes.WorkOrder,
): Promise<MaterialTrackingLineStatus[]> {
    return (
        await workOrder.materialTrackings
            .map(materialTracking => materialTracking.lines.map(getMaterialTrackingLineStatus).toArray())
            .toArray()
    ).flat();
}
