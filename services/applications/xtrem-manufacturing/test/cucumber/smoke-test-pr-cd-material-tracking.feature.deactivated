@xtrem_manufacturing
Feature: smoke-test-pr-cd-material-tracking

    Scenario: Create material tracking
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/MaterialTracking"
        #No navigation panel full width
        Then the "Material tracking" titled page is displayed
        #Fill in the criteria
        When the user selects the "status" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Pending" in the dropdown-list field
        And the user selects the "from work order" labelled reference field on the main page
        And the user writes "WOS-SM-FIRM" in the reference field
        And the user selects "WOS-SM-FIRM" in the reference field
        And the user selects the "to work order" labelled reference field on the main page
        And the user writes "WOS-SM-FIRM" in the reference field
        And the user selects "WOS-SM-FIRM" in the reference field
        # Click the search button
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "components" bound table field on the main page
        And the user clicks the "remainingQuantity" bound nested field of row 1 in the table field
        And the user presses Backspace
        # Enter time tracking information
        And the user selects the "components" bound table field on the main page
        And the user writes "1" in the "actual quantity" labelled nested reference field of row 1 in the table field
        # Add the stock allocation
        And the user selects the "components" bound table field on the main page
        And the user clicks the "Allocate stock" dropdown action of row 1 of the table field
        And the user selects the "allocations" bound table field on a modal
        And the user clicks the "Quantity to allocate" labelled nested field of row 1 in the table field
        And the user presses Delete
        And the user selects the "allocations" bound table field on a modal
        And the user writes "1" in the "Quantity to allocate" labelled nested text field of row 1 in the table field
        And the user presses Enter
        And the user selects the "Allocate" labelled business action button on a modal
        # Click the Generate button
        And the user selects the "Generate" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Tracking records generated: 1" is displayed
