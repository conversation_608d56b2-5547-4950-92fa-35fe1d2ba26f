@xtrem_manufacturing
Feature: smoke-test-pr-cd-wip-transaction-inquiry

    Scenario: Search WIP transaction inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WipTransactionInquiry"
        Then the "Work in progress transaction inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page

        # Set filter for type
        And the user filters the "Type" labelled column in the table field with value "Martial tracking"

        # Set filter for work order
        Then the user opens the filter of the "Work order" labelled column in the table field
        And the user searches "WO2021US0030001" in the filter of the table field
        And the user ticks the item with text "WO2021US0030001" in the filter of the table field
        Then the user closes the filter of the "Work order" labelled column in the table field

        # Check result
        When the user selects the row with text "Pending" in the "Status" labelled column header of the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "10.00 L"
        And the value of the "Amount" labelled nested numeric field of the selected row in the table field is "£ 100.00"

        # Click on a document number link (to prevent not noticing when <PERSON><PERSON><PERSON> undoes a link hack - XT-86124)
        When the user clicks the "Document" labelled nested field of the selected row in the table field
        Then the dialog title is "Material issue WOTUS00300003" on a full width modal
