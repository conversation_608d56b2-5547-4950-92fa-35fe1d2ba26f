@xtrem_manufacturing
Feature: smoke-test-data

    #Case without navigation panel full width
    Scenario Outline: std \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed
        Examples:
            | Page                                                 | Title                      |
            | @sage/xtrem-manufacturing/WorkOrder/eyJfaWQiOiIxIn0= | Work order WO2021US0030001 |

    Scenario: std \ xtrem-manufacturing \ desktop \ check i can change a work order and save without any problems
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder/eyJfaWQiOiIyIn0="
        Then the "Work order WO2021US0030002" titled page is displayed
        When the user selects the "Released quantity" labelled numeric field on the main page
        And the user writes "200" in the numeric field
        And the user blurs the numeric field
        Then the user clicks the "Save" labelled business action button on the main page
