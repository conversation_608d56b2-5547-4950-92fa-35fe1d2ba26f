@xtrem_manufacturing
Feature: smoke-test-pr-cd-time-tracking

    Scenario: Create time tracking
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/TimeTracking"
        #No navigation panel full width
        Then the "Time tracking" titled page is displayed
        #Fill in the criteria
        And the user selects the "from work order number" labelled reference field on the main page
        And the user writes "WOS-FIRM" in the reference field
        And the user selects "WOS-FIRM" in the reference field
        And the user selects the "to work order number" labelled reference field on the main page
        And the user writes "WOS-FIRM" in the reference field
        And the user selects "WOS-FIRM" in the reference field
        When the user selects the "status" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Pending" in the dropdown-list field
        # Click the search button
        When the user clicks in the "searchButton" bound button field on the main page
        # Enter time tracking information
        And the user selects the "workOrderOperations" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "actual resource" labelled nested field of the selected row in the table field
        And the user writes "<PERSON>" in the "actual resource" labelled nested reference field of the selected row in the table field
        And the user selects "<PERSON>" in the "actual resource" labelled nested field of the selected row in the table field
        And the user writes "10" in the "actual setup time" labelled nested numeric field of the selected row in the table field
        And the user writes "20" in the "actual run time" labelled nested numeric field of the selected row in the table field

        # Click the Generate button
        When the user clicks the "Generate" labelled business action button on the main page

        #Verify Creation
        And a toast containing text "Tracking records generated: 1" is displayed
