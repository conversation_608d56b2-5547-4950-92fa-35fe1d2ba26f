@xtrem_manufacturing
Feature: smoke-test-static

    #Case with navigation panel full width without create business action
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                                    | NavigationPanelTitle                 | Title                                |
            | @sage/xtrem-manufacturing/MaterialTrackingInquiry       | Material issues                      | Material issues                      |
            | @sage/xtrem-manufacturing/TimeTrackingInquiry           | Time tracking inquiries              | Time tracking inquiries              |
            | @sage/xtrem-manufacturing/ProductionTrackingInquiry     | Production receipts                  | Production receipts                  |
            | @sage/xtrem-manufacturing/WipInquiry                    | Work in progress inquiry             | Work in progress inquiry             |
            | @sage/xtrem-manufacturing/MaterialTrackingLineInquiry   | Material tracking line inquiry       | Material tracking line inquiry       |
            | @sage/xtrem-manufacturing/TimeTrackingLineInquiry       | Time tracking line inquiry           | Time tracking line inquiry           |
            | @sage/xtrem-manufacturing/ProductionTrackingLineInquiry | Production tracking line inquiry     | Production tracking line inquiry     |
            | @sage/xtrem-manufacturing/WipTransactionInquiry         | Work in progress transaction inquiry | Work in progress transaction inquiry |
            | @sage/xtrem-manufacturing/WorkOrderCategory             | Work order categories                | Work order categories                |
            | @sage/xtrem-manufacturing/WorkOrderMassAllocation       | Work order mass allocation           | Work order mass allocation           |
            | @sage/xtrem-manufacturing/TimeTracking                  | Time tracking                        | Time tracking                        |
            | @sage/xtrem-manufacturing/MaterialTracking              | Material tracking                    | Material tracking                    |
            | @sage/xtrem-manufacturing/ProductionTracking            | Production tracking                  | Production tracking                  |
            | @sage/xtrem-manufacturing/WorkOrder                     | Work orders                          | Work orders                          |

    # Positive test to ensure that the error dialog is definitely exist and open
    Scenario: sts \ xtrem-manufacturing \ desktop \ check an error dialog appears when the page does not load
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Wazup"
        Then an error dialog appears on the main page
