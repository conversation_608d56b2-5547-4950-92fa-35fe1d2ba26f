@xtrem_master_data
Feature: smoke-test-pr-cd-work-order-category

    Scenario: Create Work order category
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrderCategory"
        #No navigation panel full width
        Then the "Work order categories" titled page is displayed
        #Click Add <PERSON><PERSON>
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Then the detail panel is displayed
        #Fill in the mandatory fields
        And the user selects the "id" labelled text field on the main page
        And the user writes "TESTCAT" in the text field
        And the user selects the "name" labelled text field on the main page
        And the user writes "TESTCAT" in the text field
        And the user selects the "description" labelled text field on the main page
        And the user writes "TESTCAT" in the text field
        #Click Save Crud Button
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then the "Work order category TESTCAT" titled page is displayed

    Scenario: Work order category delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrderCategory"
        Then the "Work order categories" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "id" bound column in the table field with value "TESTCAT"
        And the user selects the row 1 of the table field
        And the user clicks the "id" labelled nested field of the selected row in the table field
        #Click Delete Crud Button
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
