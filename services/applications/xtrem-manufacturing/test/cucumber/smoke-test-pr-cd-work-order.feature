@xtrem_master_data
Feature: smoke-test-pr-cd-work-order

    Scenario: Create Work order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        #No navigation panel full width
        Then the "Work orders" titled page is displayed
        #Click Add <PERSON><PERSON>
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Then the detail panel is displayed
        #Fill in the mandatory fields
        And the user selects the "site" labelled reference field on the sidebar
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        And the user selects the "Released item" labelled reference field on the sidebar
        And the user writes "Chair" in the reference field
        And the user selects "Chair" in the reference field
        And the user selects the "category" labelled reference field on the sidebar
        And the user writes "Normal" in the reference field
        And the user selects "Normal" in the reference field
        And the user selects the "Type" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Firm" in the dropdown-list field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "2" in the numeric field
        And the user selects the "Work order number" labelled text field on the sidebar
        And the user writes "W<PERSON><PERSON><PERSON><PERSON>" in the text field
        #Click Save Crud Button
        And the user clicks the "Create" labelled business action button on the sidebar
        #Verify Creation
        Then the "Work order WOSTATIC" titled page is displayed

    Scenario: Add components to work order
        #Reload the just created work order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "WOSTATIC"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "WOSTATIC"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Work order WOSTATIC" titled page is displayed
        #Add a stock managed item as component
        And selects the "Components" labelled navigation anchor on the main page
        And the user selects the "productionComponents" bound table field on the main page
        And the user clicks the "addComponentLine" bound action of the table field
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        And the user writes "45" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Simple component" in the reference field
        And the user selects "Simple component" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user blurs the numeric field
        And the user clicks the "OK" labelled business action button on the sidebar
        #Verify Save
        Then a toast containing text "Record updated" is displayed

    Scenario: Work order delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "WOSTATIC"
        Then the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is "WOSTATIC"
        And the user selects the row 1 of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Work order WOSTATIC" titled page is displayed
        #Click Delete Crud Button
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
