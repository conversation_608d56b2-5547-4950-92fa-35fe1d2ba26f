@xtrem_manufacturing
Feature: smoke-test-pr-cd-wip-inquiry

    Scenario: Search WIP detailed inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WipInquiry"
        #No navigation panel full width
        Then the "Work in progress inquiry" titled page is displayed
        #Fill in site reference field
        When the user selects the "Site *" labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        #Fill in date field
        And the user selects the "As of date *" labelled date field on the main page
        And the user writes "03/18/2021" in the date field
        #Fill in the work order intervall
        And the user selects the "From work order" labelled reference field on the main page
        And the user writes "WOS-SM-FIRM-NOALLREQ" in the reference field
        And the user selects the "To work order" labelled reference field on the main page
        And the user writes "WOS-SM-FIRM-NOALLREQ" in the reference field
        #Click the run button
        And the user clicks the "Run" labelled business action button on the main page
        And the user waits for 10 seconds
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "Simple made item"
        And the value of the "plannedProcessCost" labelled nested numeric field of the selected row in the table field is "$ 1.1200"
