@xtrem_manufacturing
Feature: smoke-test-pr-cd-production-tracking

    Scenario: Create production tracking
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTracking"
        #No navigation panel full width
        Then the "Production tracking" titled page is displayed
        #Fill in the criteria
        When the user selects the "from work order" labelled reference field on the main page
        And the user writes "WOS-SM-FIRM" in the reference field
        And the user selects "WOS-SM-FIRM" in the reference field
        And the user selects the "to work order" labelled reference field on the main page
        And the user writes "WOS-SM-FIRM" in the reference field
        And the user selects "WOS-SM-FIRM" in the reference field
        And the user selects the "status" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Pending" in the dropdown-list field
        # Click the search button
        And the user clicks in the "searchButton" bound button field on the main page
        # Enter production tracking information
        And the user selects the "workOrders" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "10" in the "actual qty." labelled nested numeric field of the selected row in the table field
        # Add the stock details
        And the user selects the "workOrders" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row 1 of the table field
        And the user writes "location 1" in the "location" labelled nested reference field of the selected row in the table field
        And the user selects "location 1" in the "location" labelled nested field of the selected row in the table field
        And the user writes "Quality control" in the "Quality control" labelled nested reference field of the selected row in the table field
        And the user selects "Quality control" in the "Quality control" labelled nested field of the selected row in the table field
        And the user clicks the "ok" labelled business action button on a modal
        # Click the Generate button
        And the user clicks the "Generate" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Tracking records generated: 1" is displayed
