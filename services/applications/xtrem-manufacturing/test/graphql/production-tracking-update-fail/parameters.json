{"Production tracking update for completed tracking fails": {"input": {"properties": {"_id": "5", "effectiveDate": "2022-11-28"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "ProductionTracking.effectiveDate: cannot set value on frozen property", "path": ["effectiveDate"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "productionTracking", "update"]}]}}, "Production tracking update for effectiveDate fails": {"input": {"properties": {"_id": "7", "effectiveDate": "2022-12-25"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "ProductionTracking.effectiveDate: cannot set value on frozen property", "path": ["effectiveDate"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "productionTracking", "update"]}]}}, "Production tracking update for entryDate fails": {"input": {"properties": {"_id": "7", "entryDate": "2022-12-25"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "ProductionTracking.entryDate: cannot set value on frozen property", "path": ["entryDate"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "productionTracking", "update"]}]}}}