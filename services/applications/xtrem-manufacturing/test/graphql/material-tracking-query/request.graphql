{
    xtremManufacturing {
        materialTracking {
            query(first: 1) {
                edges {
                    node {
                        number
                        workOrder {
                            number
                            type
                            typeFiltered
                            status
                            name
                            timeUnit
                        }
                        site {
                            id
                        }
                        entryDate
                        effectiveDate
                        stockTransactionStatus
                        lines {
                            query {
                                edges {
                                    node {
                                        document {
                                            number
                                            entryDate
                                            effectiveDate
                                        }
                                        line
                                        workOrderLine {
                                            componentNumber
                                            lineType
                                            name
                                            isFixedLinkQuantity
                                            linkQuantity
                                            scrapFactor
                                            requiredQuantity
                                            consumedQuantity
                                            requiredDate
                                            lineStatus
                                            isAdded
                                            plannedCost
                                            actualCost
                                            completedQuantityPercentage
                                        }
                                        materialType
                                        quantityInStockUnit
                                        isActiveQuantity
                                        completed
                                        stockTransactionStatus
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
