{"Create a production-tracking insert fail work order line": {"input": {"properties": {"number": "004", "entryDate": "2020-06-02", "effectiveDate": "2020-06-02", "workOrder": "#US003|WO2021US0030002", "lines": {"document": 1, "line": "23", "type": "WO", "releasedQuantity": "38.76"}}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremManufacturing", "productionTracking", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "orderCost"], "message": "ProductionTrackingLine.workOrderLine: property is required"}]}}], "data": {"xtremManufacturing": {"productionTracking": {"create": null}}}, "extensions": {"diagnoses": [{"severity": 4, "path": ["lines", "", "orderCost"], "message": "ProductionTrackingLine.workOrderLine: property is required"}]}}}, "Create a production-tracking insert fail released quantity": {"input": {"properties": {"number": "004", "entryDate": "2020-06-02", "effectiveDate": "2020-06-02", "workOrder": "#US003|WO2021US0030002", "lines": {"document": 1, "line": "23", "type": "WO", "workOrderLine": "#US003|WO2021US0030001|17890-B"}}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremManufacturing", "productionTracking", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "-1000000002", "releasedQuantity"], "message": "property cannot be equal to 0"}]}}], "data": {"xtremManufacturing": {"productionTracking": {"create": null}}}, "extensions": {"diagnoses": [{"severity": 3, "path": ["lines", "-2", "releasedQuantity"], "message": "property cannot be equal to 0"}]}}}, "Create a production-tracking insert fail released quantity negative": {"input": {"properties": {"number": "004", "entryDate": "2020-06-02", "effectiveDate": "2020-06-02", "workOrder": "#US003|WO2021US0030002", "lines": {"document": 1, "line": "23", "type": "WO", "workOrderLine": "#US003|WO2021US0030001|17890-B", "releasedQuantity": "-38.76"}}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremManufacturing", "productionTracking", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "-1000000002", "releasedQuantity"], "message": "value must be greater than 0"}]}}], "data": {"xtremManufacturing": {"productionTracking": {"create": null}}}, "extensions": {"diagnoses": [{"severity": 3, "path": ["lines", "-1000000002", "releasedQuantity"], "message": "value must be greater than 0"}]}}}}