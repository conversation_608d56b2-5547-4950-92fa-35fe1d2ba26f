{
    xtremManufacturing {
        workOrderComponent {
            read(_id: "3016") {
                _id
                requiredQuantity
                consumedQuantity
                requiredDate
                lineStatus
                componentNumber
                lineType
                name
                isFixedLinkQuantity
                linkQuantity
                plannedCost
                actualCost
                completedQuantityPercentage
                stockAllocations {
                    query {
                        totalCount
                        edges {
                            node {
                                _sourceId
                                _id
                                quantityInStockUnit
                                stockRecord {
                                    _id
                                }
                                serialNumbers {
                                    query {
                                        edges {
                                            node {
                                                _id
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
