{"Create a work order component with missing work order error": {"input": {"properties": {"workOrder": "", "name": "Component 11", "componentNumber": 40, "lineType": "normal", "item": "#1324", "unit": "#EACH", "linkQuantity": 20, "requiredQuantity": 20, "plannedCost": 10, "requiredDate": "2020-06-10", "operation": 4}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "WorkOrderComponent.workOrder: property is required", "path": ["workInProgress", "site"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderComponent", "create"]}]}}, "Create a work order component with missing unit error": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Component 11", "componentNumber": 70, "lineType": "normal", "item": "#1324", "unit": "", "linkQuantity": 20, "requiredQuantity": 20, "requiredDate": "2020-06-10"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Unit mandatory for normal line type.", "path": ["unit"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderComponent", "create"]}]}}, "Create a work order component with missing item error": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Component 11", "componentNumber": 70, "lineType": "normal", "unit": "#LITER", "linkQuantity": 20, "requiredQuantity": 20, "requiredDate": "2020-06-10"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Item mandatory for normal line type.", "path": ["item"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderComponent", "create"]}]}}, "Create a work order component with negative line quantity error": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Component 11", "componentNumber": 70, "lineType": "normal", "item": "#1324", "unit": "#LITER", "linkQuantity": -98, "requiredQuantity": 20, "requiredDate": "2020-06-10"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be greater than 0", "path": ["linkQuantity"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderComponent", "create"]}]}}, "Create a work order component with missing link quantity error": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Component 11", "componentNumber": 70, "lineType": "normal", "item": "#1324", "unit": "#LITER", "requiredQuantity": 20, "requiredDate": "2020-06-10"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be greater than 0", "path": ["linkQuantity"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderComponent", "create"]}]}}, "Create a work order component with invalid link quantity error": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Component 11", "componentNumber": 70, "lineType": "text", "lineStatus": "completed", "item": "#1324", "unit": "#LITER", "linkQuantity": 10}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Item must be null for text line type.", "path": ["item"], "severity": 3}, {"message": "Unit must be null for text line type.", "path": ["unit"], "severity": 3}, {"message": "value must be zero", "path": ["linkQuantity"], "severity": 3}, {"message": "value must be zero", "path": ["requiredQuantity"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderComponent", "create"]}]}}, "Create a work order component with invalid operation error": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Component 11", "componentNumber": 70, "lineType": "text", "lineStatus": "completed", "operation": 4}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Operation must be null for text line type.", "path": ["operation"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderComponent", "create"]}]}}, "Create a work order component for text line type and scrap factor 10%": {"executionMode": "normal", "input": {"properties": {"workOrder": "#US003|WO2021US0030002", "componentNumber": 70, "lineType": "text", "lineStatus": "completed", "scrapFactor": 10}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be zero", "path": ["scrapFactor"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderComponent", "create"]}]}}, "Create a work order component with a phantom item error": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["phantomItemOption"]}, "input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Component 60", "componentNumber": 60, "lineType": "normal", "item": "#Phantom", "unit": "#EACH", "linkQuantity": 20, "requiredQuantity": 20, "requiredDate": "2021-03-18"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Phantom items cannot be added to a work order.", "path": ["item"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderComponent", "create"]}]}}}