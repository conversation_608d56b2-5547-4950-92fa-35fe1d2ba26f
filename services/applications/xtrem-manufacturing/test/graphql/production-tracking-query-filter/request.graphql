{
    xtremManufacturing {
        productionTracking {
            query(filter: "{_id:'3'}") {
                edges {
                    node {
                        _id
                        number
                        entryDate
                        lines {
                            query {
                                edges {
                                    node {
                                        line
                                        type
                                        releasedQuantity
                                        workOrderLine {
                                            document {
                                                number
                                                type
                                                status
                                                name
                                                isForwardScheduling
                                                startDate
                                                endDate
                                            }
                                            releasedItem {
                                                id
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
