{"Production tracking update for completed tracking fails": {"input": {"properties": {"_id": "5", "lines": [{"_id": "2204", "releasedQuantity": 123, "_action": "update"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "ProductionTrackingLine.releasedQuantity: cannot set value on frozen property", "path": ["lines", "2204", "releasedQuantity"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "productionTracking", "update"]}]}}, "Production tracking line update for releasedQuantity fails": {"input": {"properties": {"_id": "7", "lines": [{"_id": "2208", "releasedQuantity": 123, "_action": "update"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "ProductionTrackingLine.releasedQuantity: cannot set value on frozen property", "path": ["lines", "2208", "releasedQuantity"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "productionTracking", "update"]}]}}, "Production tracking line update for completed fails": {"input": {"properties": {"_id": "7", "lines": [{"_id": "2208", "completed": true, "_action": "update"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "ProductionTrackingLine.completed: cannot set value on frozen property", "path": ["lines", "2208", "completed"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "productionTracking", "update"]}]}}, "Production tracking line update for orderCost fails": {"input": {"properties": {"_id": "7", "lines": [{"_id": "2208", "orderCost": 12.34, "_action": "update"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "ProductionTrackingLine.orderCost: cannot set value on frozen property", "path": ["lines", "2208", "orderCost"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "productionTracking", "update"]}]}}, "Production tracking line update for valuedCost fails": {"input": {"properties": {"_id": "7", "lines": [{"_id": "2208", "valuedCost": 12.34, "_action": "update"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "ProductionTrackingLine.valuedCost: cannot set value on frozen property", "path": ["lines", "2208", "valuedCost"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "productionTracking", "update"]}]}}, "Production tracking line update for storedDimensions fails": {"input": {"properties": {"_id": "7", "lines": [{"_id": "2208", "storedDimensions": "{}", "_action": "update"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "ProductionTrackingLine.storedDimensions: cannot set value on frozen property", "path": ["lines", "2208", "storedDimensions"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "productionTracking", "update"]}]}}, "Production tracking line update for storedAttributes fails": {"input": {"properties": {"_id": "7", "lines": [{"_id": "2208", "storedAttributes": "{}", "_action": "update"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "ProductionTrackingLine.storedAttributes: cannot set value on frozen property", "path": ["lines", "2208", "storedAttributes"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "productionTracking", "update"]}]}}}