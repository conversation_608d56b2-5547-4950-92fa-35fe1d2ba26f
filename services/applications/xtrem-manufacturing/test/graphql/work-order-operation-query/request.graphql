{
    xtremManufacturing {
        workOrderOperation {
            query(filter: "{_id: { _in: ['#US003|WO2021US0030001|5','#US003|WO2021US0030001|10']}}") {
                edges {
                    node {
                        operationNumber
                        name
                        status
                        startDate
                        endDate
                        plannedQuantity
                        completedQuantity
                        completedQuantityPercentage
                        completedTimePercentage
                        expectedSetupTime
                        expectedRunTime
                        expectedBatchCost
                        expectedBatchSetupCost
                        isProductionStep
                        resources {
                            query {
                                edges {
                                    node {
                                        resource {
                                            name
                                        }
                                        expectedRunTime
                                        expectedSetupTime
                                        resourceNumber
                                        resources {
                                            query {
                                                edges {
                                                    node {
                                                        resource {
                                                            name
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
