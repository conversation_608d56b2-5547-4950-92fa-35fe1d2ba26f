{"Create a work order with createWorkOrder mutation": {"input": {"properties": {"workOrderNumber": "", "startDate": "2023-01-05", "releasedQuantity": 1000, "siteId": "US001", "releasedItem": "Chair", "workOrderCategory": "#Normal", "type": "firm", "name": "Chair", "bom": "Chair", "route": "Chair"}}, "output": {"createWorkOrder": {"number": "WO230001", "name": "Chair", "bomCode": {"name": "Chair"}, "productionItems": {"query": {"edges": [{"node": {"item": {"id": "Chair"}, "quantityInStockUnit": "1000", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}, "productionComponents": {"query": {"edges": [{"node": {"componentNumber": 10, "item": {"id": "ChairLeg"}, "linkQuantity": "4", "requiredQuantity": "4000", "operation": {"operationNumber": 10}, "instruction": {"value": "Start with the legs"}}}, {"node": {"componentNumber": 20, "item": {"id": "ChairSeat"}, "linkQuantity": "1", "operation": null, "requiredQuantity": "1050", "instruction": {"value": ""}}}, {"node": {"componentNumber": 30, "item": {"id": "ChairBack"}, "linkQuantity": "1", "operation": null, "requiredQuantity": "1000", "instruction": {"value": "Finish with the back"}}}]}}, "routingCode": {"name": "Chair"}, "productionOperations": {"query": {"edges": [{"node": {"instruction": {"value": "Put the 3 parts together"}, "operationNumber": 10}}]}}, "_customData": "{}"}}, "executionMode": "normal", "envConfigs": {"today": "2023-01-05"}}, "Create a work order with BOM having a phantom as component but phantomItemOption service option deactivated": {"commentLine1": "This test is just to check data before the test with phantomItemOption activated", "executionMode": "normal", "envConfigs": {"today": "2024-07-16"}, "input": {"properties": {"workOrderNumber": "", "startDate": "2024-07-16", "releasedQuantity": 1000, "siteId": "US001", "releasedItem": "CARROT-BOX", "workOrderCategory": "#Assembly", "type": "firm", "name": "Carrot box", "bom": "CARROT-BOX"}}, "output": {"createWorkOrder": {"number": "WO240004", "name": "Carrot box", "routingCode": null, "productionItems": {"query": {"edges": [{"node": {"item": {"id": "CARROT-BOX"}, "quantityInStockUnit": "1000", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}, "productionComponents": {"query": {"edges": [{"node": {"componentNumber": 10, "item": {"id": "CARROT"}, "linkQuantity": "0.5", "operation": null, "requiredQuantity": "500", "instruction": {"value": ""}}}, {"node": {"componentNumber": 20, "item": {"id": "DRESSING"}, "linkQuantity": "0.01", "operation": null, "requiredQuantity": "10", "instruction": {"value": ""}}}, {"node": {"componentNumber": 30, "item": null, "linkQuantity": "0", "operation": null, "requiredQuantity": "0", "instruction": {"value": "<p>Don't forget to rinse the box</p>"}}}, {"node": {"componentNumber": 40, "item": {"id": "BOX500"}, "linkQuantity": "1", "operation": null, "requiredQuantity": "1000", "instruction": {"value": ""}}}, {"node": {"componentNumber": 50, "item": {"id": "Aqua"}, "linkQuantity": "5", "operation": null, "requiredQuantity": "5", "instruction": {"value": ""}}}]}}, "bomCode": {"name": "Carrot box 500g"}, "productionOperations": {"query": {"edges": []}}, "_customData": "{}"}}}, "Create a work order with BOM having a phantom as component": {"executionMode": "normal", "envConfigs": {"today": "2024-07-16", "testActiveServiceOptions": ["phantomItemOption"]}, "input": {"properties": {"workOrderNumber": "", "startDate": "2024-07-16", "releasedQuantity": 1000, "siteId": "US001", "releasedItem": "CARROT-BOX", "workOrderCategory": "#Normal", "type": "firm", "name": "Carrot box", "bom": "CARROT-BOX", "route": "CARROT-BOX"}}, "output": {"createWorkOrder": {"number": "WO240004", "name": "Carrot box", "routingCode": {"name": "Carrot box"}, "productionItems": {"query": {"edges": [{"node": {"item": {"id": "CARROT-BOX"}, "quantityInStockUnit": "1000", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}, "productionComponents": {"query": {"edges": [{"node": {"componentNumber": 10, "item": {"id": "CARROT"}, "requiredQuantity": "500", "linkQuantity": "0.5", "operation": null, "instruction": {"value": ""}}}, {"node": {"componentNumber": 20, "item": {"id": "MUSTARD"}, "requiredQuantity": "2", "linkQuantity": "0.002", "operation": {"operationNumber": 10}, "instruction": {"value": ""}}}, {"node": {"componentNumber": 30, "item": {"id": "OLIVE-OIL"}, "requiredQuantity": "6", "linkQuantity": "0.006", "operation": {"operationNumber": 10}, "instruction": {"value": ""}}}, {"node": {"componentNumber": 40, "item": {"id": "WINE-VINEGAR"}, "requiredQuantity": "4", "linkQuantity": "0.004", "operation": {"operationNumber": 10}, "instruction": {"value": ""}}}, {"node": {"componentNumber": 50, "item": null, "requiredQuantity": "0", "linkQuantity": "0", "operation": null, "instruction": {"value": "<p>Don't forget to rinse the box</p>"}}}, {"node": {"componentNumber": 60, "item": {"id": "BOX500"}, "requiredQuantity": "1000", "linkQuantity": "1", "operation": {"operationNumber": 20}, "instruction": {"value": ""}}}, {"node": {"componentNumber": 70, "item": {"id": "Aqua"}, "requiredQuantity": "5", "linkQuantity": "5", "operation": null, "instruction": {"value": ""}}}]}}, "bomCode": {"name": "Carrot box 500g"}, "productionOperations": {"query": {"edges": [{"node": {"operationNumber": 20, "instruction": {"value": ""}}}, {"node": {"operationNumber": 10, "instruction": {"value": ""}}}]}}, "_customData": "{}"}}}, "Create a work order with createWorkOrder mutation and empty customData": {"input": {"properties": {"workOrderNumber": "", "startDate": "2023-01-05", "releasedQuantity": 1000, "siteId": "US001", "releasedItem": "Chair", "workOrderCategory": "#Normal", "type": "firm", "name": "Chair", "bom": "Chair", "route": "Chair", "_customData": "{}"}}, "output": {"createWorkOrder": {"number": "WO230001", "name": "Chair", "bomCode": {"name": "Chair"}, "productionItems": {"query": {"edges": [{"node": {"item": {"id": "Chair"}, "quantityInStockUnit": "1000", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}, "productionComponents": {"query": {"edges": [{"node": {"componentNumber": 10, "item": {"id": "ChairLeg"}, "linkQuantity": "4", "requiredQuantity": "4000", "operation": {"operationNumber": 10}, "instruction": {"value": "Start with the legs"}}}, {"node": {"componentNumber": 20, "item": {"id": "ChairSeat"}, "linkQuantity": "1", "operation": null, "requiredQuantity": "1050", "instruction": {"value": ""}}}, {"node": {"componentNumber": 30, "item": {"id": "ChairBack"}, "linkQuantity": "1", "operation": null, "requiredQuantity": "1000", "instruction": {"value": "Finish with the back"}}}]}}, "routingCode": {"name": "Chair"}, "productionOperations": {"query": {"edges": [{"node": {"instruction": {"value": "Put the 3 parts together"}, "operationNumber": 10}}]}}, "_customData": "{}"}}, "executionMode": "normal", "envConfigs": {"today": "2023-01-05"}}, "Create a work order with createWorkOrder mutation and having customData": {"input": {"properties": {"workOrderNumber": "", "startDate": "2023-01-05", "releasedQuantity": 1000, "siteId": "US001", "releasedItem": "Chair", "workOrderCategory": "#Normal", "type": "firm", "name": "Chair", "bom": "Chair", "route": "Chair", "_customData": "{\"my_custom_field\":\"i will be back\"}"}}, "output": {"createWorkOrder": {"number": "WO230001", "name": "Chair", "bomCode": {"name": "Chair"}, "productionItems": {"query": {"edges": [{"node": {"item": {"id": "Chair"}, "quantityInStockUnit": "1000", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}, "productionComponents": {"query": {"edges": [{"node": {"componentNumber": 10, "item": {"id": "ChairLeg"}, "linkQuantity": "4", "requiredQuantity": "4000", "operation": {"operationNumber": 10}, "instruction": {"value": "Start with the legs"}}}, {"node": {"componentNumber": 20, "item": {"id": "ChairSeat"}, "linkQuantity": "1", "operation": null, "requiredQuantity": "1050", "instruction": {"value": ""}}}, {"node": {"componentNumber": 30, "item": {"id": "ChairBack"}, "linkQuantity": "1", "operation": null, "requiredQuantity": "1000", "instruction": {"value": "Finish with the back"}}}]}}, "routingCode": {"name": "Chair"}, "productionOperations": {"query": {"edges": [{"node": {"instruction": {"value": "Put the 3 parts together"}, "operationNumber": 10}}]}}, "_customData": "{\"my_custom_field\":\"i will be back\"}"}}, "executionMode": "normal", "envConfigs": {"today": "2023-01-05"}}}