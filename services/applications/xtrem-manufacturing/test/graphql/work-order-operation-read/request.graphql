{
    xtremManufacturing {
        workOrderOperation {
            read(_id: "#US003|WO2021US0030001|15") {
                operationNumber
                name
                status
                startDate
                endDate
                plannedQuantity
                completedQuantity
                completedQuantityPercentage
                completedTimePercentage
                expectedSetupTime
                expectedRunTime
                expectedBatchCost
                expectedBatchSetupCost
                expectedMachineCost
                expectedMachineSetupCost
                expectedLaborCost
                expectedLaborSetupCost
                expectedToolCost
                expectedToolSetupCost
                actualMachineCost
                actualMachineSetupCost
                actualLaborCost
                actualLaborSetupCost
                actualToolCost
                actualToolSetupCost
                isProductionStep
                resources {
                    query {
                        edges {
                            node {
                                workOrder {
                                    number
                                }
                                resource {
                                    name
                                }
                                expectedRunTime
                                expectedSetupTime
                                resourceNumber
                                resources {
                                    query {
                                        edges {
                                            node {
                                                resource {
                                                    name
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
