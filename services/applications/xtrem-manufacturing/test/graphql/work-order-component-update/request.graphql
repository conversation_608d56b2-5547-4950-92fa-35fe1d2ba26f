# TODO: check that this test is relevant because with the UI, it's not allowed

# By modifying the quantity of an existing component, check the planned cost is not updated

mutation {
    xtremManufacturing {
        workOrderComponent {
            update(data: { _id: "3002", _action: "update", linkQuantity: 60, requiredQuantity: 60 }) {
                workOrder {
                    number
                }
                componentNumber
                linkQuantity
                plannedCost
            }
        }
    }
}
