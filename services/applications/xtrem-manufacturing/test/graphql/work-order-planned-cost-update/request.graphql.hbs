{{#if isQuery}}
query readPlannedCost {
  xtremManufacturing {
    workOrder {
      query(first: 1, filter: "{ number: 'WO2021US0030004' }") {
        edges {
          node {
{{else}}
mutation updatePlannedCost {
  xtremManufacturing {
    workOrder {
      updatePlannedCosts(workOrder: "#US003|WO2021US0030004") {
{{/if}}
            number
            productionItems {
              query {
                edges {
                  node {
                    _sortValue
                    releasedItem {
                      name
                    }
                    plannedToolCost
                    plannedLaborCost
                    plannedMachineCost
                    plannedProcessCost
                    plannedMaterialCost
                  }
                }
              }
            }
{{#if isQuery}}
          }
        }
      }
    }
  }
}
{{else}}
      }
    }
  }
}
{{/if}}
