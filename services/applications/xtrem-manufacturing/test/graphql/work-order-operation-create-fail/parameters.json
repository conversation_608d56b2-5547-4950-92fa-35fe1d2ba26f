{"Create a work order operation with missing work order error": {"input": {"properties": {"workOrder": "", "name": "Operation 11", "operationNumber": 40, "plannedQuantity": 20, "startDate": "2020-06-10", "endDate": "2020-06-10", "minCapabilityLevel": "1", "runTimeUnit": "#HOUR", "setupTimeUnit": "#HOUR", "resources": [{"resource": "#AFILM100L-G|US003", "expectedSetupTime": 10, "expectedRunTime": 60, "expectedRunCost": 10, "expectedSetupCost": 10, "isResourceQuantity": true, "resources": [{"resource": "#AFILM100L|US003"}]}, {"resource": "#AFILM100L|US003", "expectedSetupTime": 20, "expectedRunTime": 55, "expectedRunCost": 10, "expectedSetupCost": 10}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "WorkOrderOperation.workOrder: property is required", "path": ["instruction"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderOperation", "create"]}]}}, "Create a work order operation with missing capability level": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Operation 11", "operationNumber": 40, "plannedQuantity": 20, "startDate": "2020-06-10", "endDate": "2020-06-10", "runTimeUnit": "#HOUR", "setupTimeUnit": "#HOUR"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "WorkOrderOperation.minCapabilityLevel: property is required", "path": ["minCapabilityLevel"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderOperation", "create"]}]}}, "Create a work order operation with negative planned quantity error": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Operation 11", "operationNumber": 40, "plannedQuantity": -20, "startDate": "2020-06-10", "endDate": "2020-06-10", "runTimeUnit": "#HOUR", "setupTimeUnit": "#HOUR", "minCapabilityLevel": "1", "resources": [{"resource": "#AFILM100L-G|US003", "expectedSetupTime": 10, "expectedRunTime": 60, "isResourceQuantity": true, "resources": [{"resource": "#AFILM100L|US003"}]}, {"resource": "#AFILM100L|US003", "expectedSetupTime": 20, "expectedRunTime": 55}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must not be negative", "path": ["plannedQuantity"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderOperation", "create"]}]}}, "Create a work order operation with empty resource group error": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Operation 11", "operationNumber": 40, "plannedQuantity": 20, "startDate": "2020-06-10", "endDate": "2020-06-10", "runTimeUnit": "#HOUR", "setupTimeUnit": "#HOUR", "minCapabilityLevel": "1", "resources": [{"resource": "#AFILM100L-G|US003", "expectedSetupTime": 10, "expectedRunTime": 60, "isResourceQuantity": true, "resources": [{"resource": "#AFILM100L|US003"}]}, {"resource": "#WIMCA-G|US003", "expectedSetupTime": 20, "expectedRunTime": 55}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Enter the resources for the Reactor controler group.", "path": ["resources", "-1000000005"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderOperation", "create"]}]}}, "Create a work order operation with resource not belong to resource group error": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Operation 11", "operationNumber": 40, "plannedQuantity": 20, "startDate": "2020-06-10", "endDate": "2020-06-10", "runTimeUnit": "#HOUR", "setupTimeUnit": "#HOUR", "minCapabilityLevel": "1", "resources": [{"resource": "#AFILM100L-G|US003", "expectedSetupTime": 10, "expectedRunTime": 60, "isResourceQuantity": true, "resources": [{"resource": "#AFILM100L|US003"}, {"resource": "#<PERSON>|US003"}]}, {"resource": "#AFILM100L|US003", "expectedSetupTime": 20, "expectedRunTime": 55}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The detailed resource does not belong to the Automatic Filling Machine 100L group.", "path": ["resources", "-1000000002"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderOperation", "create"]}]}}, "Create a work order operation with negative actual quantity error": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Operation 11", "operationNumber": 40, "plannedQuantity": 20, "completedQuantity": -2, "startDate": "2020-06-10", "endDate": "2020-06-10", "runTimeUnit": "#HOUR", "setupTimeUnit": "#HOUR", "minCapabilityLevel": "1", "resources": [{"resource": "#AFILM100L-G|US003", "expectedSetupTime": 10, "expectedRunTime": 60, "isResourceQuantity": true, "resources": [{"resource": "#AFILM100L|US003"}]}, {"resource": "#AFILM100L|US003", "expectedSetupTime": 20, "expectedRunTime": 55}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must not be negative", "path": ["completedQuantity"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderOperation", "create"]}]}}, "Create a work order operation with missing planned quantity error": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Operation 11", "operationNumber": 40, "startDate": "2020-06-10", "endDate": "2020-06-10", "runTimeUnit": "#HOUR", "setupTimeUnit": "#HOUR", "minCapabilityLevel": "1", "resources": [{"resource": "#AFILM100L-G|US003", "expectedSetupTime": 10, "expectedRunTime": 60, "isResourceQuantity": true, "resources": [{"resource": "#AFILM100L|US003"}]}, {"resource": "#AFILM100L|US003", "expectedSetupTime": 20, "expectedRunTime": 55}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "property cannot be equal to 0", "path": ["plannedQuantity"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderOperation", "create"]}]}}, "Create a work order operation resource with no isResourceQuantity set": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Operation 11", "operationNumber": 40, "plannedQuantity": 20, "startDate": "2020-06-10", "endDate": "2020-06-10", "runTimeUnit": "#HOUR", "setupTimeUnit": "#HOUR", "minCapabilityLevel": "1", "resources": [{"resource": "#AFILM100L-G|US003", "expectedSetupTime": 10, "expectedRunTime": 60, "resources": [{"resource": "#AFILM100L|US003"}]}, {"resource": "#AFILM100L|US003", "expectedSetupTime": 20, "expectedRunTime": 55}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "At least one quantity produced resource with runtime is required on the operation number 40", "path": ["resources"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderOperation", "create"]}]}}, "Create a work order operation resource with more than 1 isResourceQuantity set": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Operation 11", "operationNumber": 40, "plannedQuantity": 20, "startDate": "2020-06-10", "endDate": "2020-06-10", "runTimeUnit": "#HOUR", "setupTimeUnit": "#HOUR", "minCapabilityLevel": "1", "resources": [{"resource": "#AFILM100L-G|US003", "expectedSetupTime": 10, "expectedRunTime": 60, "isResourceQuantity": true, "resources": [{"resource": "#AFILM100L|US003"}]}, {"resource": "#AFILM100L|US003", "expectedSetupTime": 20, "expectedRunTime": 55, "isResourceQuantity": true}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "No more than one quantity produced resource with runtime on the operation number 40", "path": ["resources"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderOperation", "create"]}]}}, "Create a work order operation resource with more isResourceQuantity set and no runtime": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Operation 11", "operationNumber": 40, "plannedQuantity": 20, "startDate": "2020-06-10", "endDate": "2020-06-10", "runTimeUnit": "#HOUR", "setupTimeUnit": "#HOUR", "minCapabilityLevel": "1", "resources": [{"resource": "#AFILM100L-G|US003", "expectedSetupTime": 10, "expectedRunTime": 0, "isResourceQuantity": true, "resources": [{"resource": "#AFILM100L|US003"}]}, {"resource": "#AFILM100L|US003", "expectedSetupTime": 20, "expectedRunTime": 55}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Run Time should be greater than 0 if isResourceQuantity is true.", "path": ["resources", "-1000000002", "isResourceQuantity"], "severity": 2}, {"message": "At least one quantity produced resource with runtime is required on the operation number 40", "path": ["resources"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrderOperation", "create"]}]}}}