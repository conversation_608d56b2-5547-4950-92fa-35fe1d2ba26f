{"Create an operation tracking with minimum information": {"envConfigs": {"today": "2020-06-02"}, "input": {"properties": {"workOrder": "#US003|WO2021US0030003", "lines": [{"line": 5, "workOrderOperation": "#US003|WO2021US0030003|5", "actualResource": "#MLS-C912|US003", "actualRunTime": 20, "completedQuantity": 10}]}}, "output": {"create": {"number": "WT20000001", "lines": {"query": {"edges": [{"node": {"line": 5}}]}}}}}, "Create an operation tracking with full information": {"input": {"properties": {"number": "WOTUS0010001", "workOrder": "#US003|WO2021US0030003", "entryDate": "2020-06-02", "lines": [{"line": 6, "workOrderOperation": "#US003|WO2021US0030003|5", "trackingType": "WO", "completedQuantity": 1, "actualResource": "#MLS-C912|US003", "actualSetupTime": 10, "actualRunTime": 20}]}}, "output": {"create": {"number": "WOTUS0010001", "lines": {"query": {"edges": [{"node": {"line": 6}}]}}}}}}