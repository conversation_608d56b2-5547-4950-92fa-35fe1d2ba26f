{"Get cost for a planned FIFO component": {"envConfigs": {"today": "2024-01-31"}, "executionMode": "normal", "input": {"properties": {"requiredQuantity": "2", "requiredDate": "2024-02-27", "item": "#STOFIFO-MANUF", "site": "#US001", "lineStatus": "pending"}}, "output": "228.912"}, "Get cost for a planned average cost component": {"envConfigs": {"today": "2024-01-31"}, "executionMode": "normal", "input": {"properties": {"requiredQuantity": "2", "requiredDate": "2024-02-27", "item": "#STOAVC", "site": "#US001", "lineStatus": "pending"}}, "output": "240.44990291262135922"}, "Get cost for a planned standard cost component in future": {"envConfigs": {"today": "2024-01-31"}, "executionMode": "normal", "input": {"properties": {"requiredQuantity": "2", "requiredDate": "2024-02-27", "item": "#STOSTD", "site": "#US001", "lineStatus": "pending"}}, "output": "228.888"}, "Get cost for a planned standard cost component in past": {"envConfigs": {"today": "2024-01-31"}, "executionMode": "normal", "input": {"properties": {"requiredQuantity": "2", "requiredDate": "2023-02-27", "item": "#STOSTD", "site": "#US001", "lineStatus": "pending"}}, "output": "226.666"}}