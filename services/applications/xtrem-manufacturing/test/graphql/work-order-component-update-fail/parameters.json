{"Update a work order component fail : record not found": {"input": {"properties": {"_id": "99", "linkQuantity": 60}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "WorkOrderComponent: record not found: {\"_id\":99}", "path": [], "severity": 4}]}, "locations": [{"column": 12, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "workOrderComponent", "update"]}]}}, "Update a work order component fail : with scrap factor -10% ": {"input": {"properties": {"_id": "3001", "scrapFactor": -10}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Percentage value (-10) must be between 0 and 99.99.", "path": ["scrapFactor"], "severity": 3}]}, "locations": [{"column": 12, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "workOrderComponent", "update"]}]}}, "Update a work order component fail : with scrap factor 100% ": {"input": {"properties": {"_id": "3001", "scrapFactor": 100}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Percentage value (100) must be between 0 and 99.99.", "path": ["scrapFactor"], "severity": 3}]}, "locations": [{"column": 12, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "workOrderComponent", "update"]}]}}, "Update a work order component fail : forbidden decrease of quantity when allocation request in progress ": {"input": {"properties": {"_id": "3053", "requiredQuantity": 150}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You can only reduce the required quantity of the component after the allocation request is complete.", "path": [], "severity": 3}]}, "locations": [{"column": 12, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "workOrderComponent", "update"]}]}}, "Update a work order component fail : cannot exclude when allocation request in progress ": {"input": {"properties": {"_id": "3053", "lineStatus": "excluded"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You can only exclude the component after the allocation request is complete.", "path": [], "severity": 3}]}, "locations": [{"column": 12, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "workOrderComponent", "update"]}]}}}