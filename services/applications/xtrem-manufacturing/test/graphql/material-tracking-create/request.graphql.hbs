mutation {
    xtremManufacturing {
        materialTracking {
            createSingleMaterialTracking( data:{{inputParameters}} ) { 
                materialTracking{
                    number
                    workOrder {
                        name
                        number
                        status
                        timeUnit
                        type
                    }
                    site {
                        id
                    }
                    number
                    entryDate
                    effectiveDate
                    documentDate
                    stockTransactionStatus
                    lines {
                        query {
                            edges {
                                node {
                                    document {
                                        effectiveDate
                                        entryDate
                                        number
                                    }
                                    line
                                    workOrderLine {
                                        componentNumber
                                    }
                                    item {
                                        name
                                    }
                                    quantityInStockUnit
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
