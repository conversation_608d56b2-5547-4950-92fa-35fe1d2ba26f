{
    xtremManufacturing {
        workOrderComponent {
            query(filter: "{_id: { _in: [3000,3001]}}") {
                edges {
                    node {
                        _id
                        requiredQuantity
                        consumedQuantity
                        requiredDate
                        lineStatus
                        componentNumber
                        lineType
                        name
                        isFixedLinkQuantity
                        linkQuantity
                        plannedCost
                        actualCost
                        completedQuantityPercentage
                        item {
                            id
                        }
                        unit {
                            id
                        }
                        operation {
                            name
                        }
                    }
                }
            }
        }
    }
}
