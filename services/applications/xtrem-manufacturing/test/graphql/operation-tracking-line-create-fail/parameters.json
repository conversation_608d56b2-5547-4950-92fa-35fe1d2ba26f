{"Create an operation tracking with missing work order operation error": {"input": {"properties": {"number": "WOTUS0010004", "entryDate": "2020-06-02", "effectiveDate": "2020-06-02", "workOrder": "#US003|WO2021US0030002", "lines": {"line": 1, "workOrderOperation": "", "actualResource": "#RES1|US001", "setupTimeUnit": "#SECOND", "actualSetupTime": 0, "runTimeUnit": "#SECOND", "actualRunTime": 20, "completedQuantity": 20}}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "OperationTrackingLine.workOrderOperation: property is required", "path": ["lines", "-1000000002"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "operationTracking", "create"]}]}}, "Create an operation tracking with missing resource error": {"executionMode": "normal", "input": {"properties": {"number": "WOTUS0010004", "entryDate": "2020-06-02", "effectiveDate": "2020-06-02", "workOrder": "#US003|WO2021US0030002", "lines": {"line": 1, "workOrderOperation": "#US003|WO2021US0030002|5", "actualSetupTime": 0, "actualRunTime": 20, "completedQuantity": 20}}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "OperationTrackingLine.actualResource: property is required", "path": ["lines", "-1000000002", "completedQuantity"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "operationTracking", "create"]}]}}, "Create an operation tracking with negative completed quantity error": {"input": {"properties": {"number": "WOTUS0010004", "entryDate": "2020-06-02", "effectiveDate": "2020-06-02", "workOrder": "#US003|WO2021US0030002", "lines": {"line": 1, "workOrderOperation": "#US003|WO2021US0030002|5", "actualResource": "#MLS-C912|US003", "actualSetupTime": 0, "actualRunTime": 20, "completedQuantity": -20}}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must not be negative", "path": ["lines", "-1000000002", "completedQuantity"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "operationTracking", "create"]}]}}}