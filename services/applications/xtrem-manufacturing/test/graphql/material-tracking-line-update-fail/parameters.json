{"Material tracking update for completed tracking fails": {"input": {"properties": {"_id": "#WT-MAT-WO-COMPLETED-2", "lines": [{"_id": "2009", "quantityInStockUnit": 123, "_action": "update"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "MaterialTrackingLine.quantityInStockUnit: cannot set value on frozen property", "path": ["lines", "2009", "quantityInStockUnit"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "materialTracking", "update"]}]}}, "Material tracking line update for quantityInStockUnit fails": {"input": {"properties": {"_id": "#WOTUS00300008", "lines": [{"_id": "2014", "quantityInStockUnit": 123, "_action": "update"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "MaterialTrackingLine.quantityInStockUnit: cannot set value on frozen property", "path": ["lines", "2014", "quantityInStockUnit"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "materialTracking", "update"]}]}}, "Material tracking line update for completed fails": {"input": {"properties": {"_id": "#WOTUS00300008", "lines": [{"_id": "2014", "completed": true, "_action": "update"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "MaterialTrackingLine.completed: cannot set value on frozen property", "path": ["lines", "2014", "completed"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "materialTracking", "update"]}]}}, "Material tracking line update for isActiveQuantity fails": {"input": {"properties": {"_id": "#WOTUS00300005", "lines": [{"_id": "2003", "isActiveQuantity": true, "_action": "update"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "MaterialTrackingLine.isActiveQuantity: cannot set value on frozen property", "path": ["lines", "2003", "isActiveQuantity"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "materialTracking", "update"]}]}}, "Material tracking line update for storedDimensions fails": {"input": {"properties": {"_id": "#WOTUS00300008", "lines": [{"_id": "2014", "storedDimensions": "{}", "_action": "update"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "MaterialTrackingLine.storedDimensions: cannot set value on frozen property", "path": ["lines", "2014", "storedDimensions"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "materialTracking", "update"]}]}}, "Material tracking line update for storedAttributes fails": {"input": {"properties": {"_id": "#WOTUS00300008", "lines": [{"_id": "2014", "storedAttributes": "{}", "_action": "update"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "MaterialTrackingLine.storedAttributes: cannot set value on frozen property", "path": ["lines", "2014", "storedAttributes"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremManufacturing", "materialTracking", "update"]}]}}}