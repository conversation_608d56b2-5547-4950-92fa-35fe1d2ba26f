{"Update a work order operation resource with new times": {"input": {"properties": {"_id": "#US003|WO2021US0030001|10", "workOrder": "#US003|WO2021US0030001", "name": "Operation 11", "operationNumber": 10, "plannedQuantity": 2000, "startDate": "2020-06-10", "endDate": "2020-06-10", "isProductionStep": true, "minCapabilityLevel": "1", "resources": [{"_id": "#US003|WO2021US0030001|10|900", "_action": "update", "resource": "#MLS-C912-G|US003", "expectedSetupTime": 20, "expectedRunTime": 90, "isResourceQuantity": true}, {"_id": "#US003|WO2021US0030001|10|1000", "_action": "update", "resource": "#GEKIN-G|US003", "expectedSetupTime": 30, "expectedRunTime": 70}]}}, "output": {"update": {"name": "Operation 11", "resources": {"query": {"edges": [{"node": {"resource": {"id": "MLS-C912-G", "name": "Machine for laying screw caps"}, "expectedSetupTime": "20", "expectedRunTime": "90", "expectedSetupCost": "0", "expectedRunCost": "0.0353"}}, {"node": {"resource": {"id": "GEKIN-G", "name": "Technician"}, "expectedSetupTime": "30", "expectedRunTime": "70", "expectedSetupCost": "0", "expectedRunCost": "0.353"}}]}}}}}}