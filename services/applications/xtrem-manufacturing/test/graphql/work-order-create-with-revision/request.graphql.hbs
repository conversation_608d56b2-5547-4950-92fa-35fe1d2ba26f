mutation {
  xtremManufacturing {
    workOrder {
      createWorkOrder(
        data: {{inputParameters}}) {
        number
        name
        routingCode {
          name
        }
        productionItems {
          query {
            edges {
              node {
                item{
                  id
                }
                quantityInStockUnit
                storedDimensions
              }
            }
          }
        }
        productionComponents {
          query {
            edges {
              node {
                componentNumber
                item {
                  id
                }
                requiredQuantity
                linkQuantity
                operation {
                  operationNumber
                }
                instruction {
                  value
                }
              }
            }
          }
        }
        bomCode {
          name
        }
        bomRevision {
          revision
        }
        productionOperations {
          query {
            edges {
              node {
                operationNumber
                instruction {
                  value
                }
              }
            }
          }
        }
      }
    }
  }
}
