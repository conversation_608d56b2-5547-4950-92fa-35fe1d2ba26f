{"Create a work order for a product using phantom managed by revision, prd rev=null & cmp rev=null": {"comment0": "Recap of the revisions and their statuses used in the next tests", "comment1": "PRD_REVISION_USING_REV_PHANTOM : -------------------|-------REV-01 (suspended)--------------|--------REV-02 (availableToUse)------------", "comment2": "CMP_REV_PHANTOM : -------------------|----------REV-A (suspended)----------|------------REV-B (availableToUse)--------------", "comment3": "                                     |              |                      |                |", "comment4": "                                2025-06-01     2025-07-01             2025-08-01       2025-09-01", "comment5": "=> WO start date used for PRD_USING_REV_PHANTOM: 2025-05-15, 2025-06-15 & 2025-08-15", "comment6": "=> WO start date used for PRD_REVISION_USING_REV_PHANTOM: 2025-05-15, 2025-06-15, 2025-07-15, 2025-08-15 & 2025-09-15", "executionMode": "normal", "envConfigs": {"today": "2025-01-01", "testActiveServiceOptions": ["phantomItemOption", "billOfMaterialRevisionServiceOption"]}, "input": {"properties": {"workOrderNumber": "TEST_REV_PHANTOM", "startDate": "2025-05-15", "releasedQuantity": 10, "siteId": "US001", "releasedItem": "PRD_USING_REV_PHANTOM", "workOrderCategory": "#Assembly", "type": "firm", "name": "prd with phantom managed by revision", "bom": "PRD_USING_REV_PHANTOM"}}, "output": {"createWorkOrder": {"bomCode": {"name": "Product using phantom"}, "bomRevision": null, "name": "prd with phantom managed by revision", "number": "TEST_REV_PHANTOM", "productionComponents": {"query": {"edges": [{"node": {"componentNumber": 10, "instruction": {"value": ""}, "item": {"id": "MUSTARD"}, "linkQuantity": "0.002", "operation": null, "requiredQuantity": "0.02"}}, {"node": {"componentNumber": 20, "instruction": {"value": ""}, "item": {"id": "OLIVE-OIL"}, "linkQuantity": "0.006", "operation": null, "requiredQuantity": "0.06"}}, {"node": {"componentNumber": 30, "instruction": {"value": ""}, "item": {"id": "WINE-VINEGAR"}, "linkQuantity": "0.004", "operation": null, "requiredQuantity": "0.04"}}, {"node": {"componentNumber": 40, "instruction": {"value": ""}, "item": {"id": "BOX500"}, "linkQuantity": "1", "operation": null, "requiredQuantity": "10"}}]}}, "productionItems": {"query": {"edges": [{"node": {"item": {"id": "PRD_USING_REV_PHANTOM"}, "quantityInStockUnit": "10", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}, "productionOperations": {"query": {"edges": []}}, "routingCode": null}}}, "Create a work order for a product using phantom managed by revision, prd without rev + cmp rev=suspended -> rev=REV-A": {"comment1": "The suspended revision of the phantom component is kept", "comment2": "This behavior is expected today but doesn't make sense => should be revised in the future with phantom not managed by revision too", "executionMode": "normal", "envConfigs": {"today": "2025-01-01", "testActiveServiceOptions": ["phantomItemOption", "billOfMaterialRevisionServiceOption"]}, "input": {"properties": {"workOrderNumber": "TEST_REV_PHANTOM", "startDate": "2025-06-15", "releasedQuantity": 10, "siteId": "US001", "releasedItem": "PRD_USING_REV_PHANTOM", "workOrderCategory": "#Assembly", "type": "firm", "name": "prd with phantom managed by revision", "bom": "PRD_USING_REV_PHANTOM"}}, "output": {"createWorkOrder": {"bomCode": {"name": "Product using phantom"}, "bomRevision": null, "name": "prd with phantom managed by revision", "number": "TEST_REV_PHANTOM", "productionComponents": {"query": {"edges": [{"node": {"componentNumber": 10, "instruction": {"value": ""}, "item": {"id": "MUSTARD"}, "linkQuantity": "0.003", "operation": null, "requiredQuantity": "0.03"}}, {"node": {"componentNumber": 20, "instruction": {"value": ""}, "item": {"id": "OLIVE-OIL"}, "linkQuantity": "0.007", "operation": null, "requiredQuantity": "0.07"}}, {"node": {"componentNumber": 30, "instruction": {"value": ""}, "item": {"id": "BOX500"}, "linkQuantity": "1", "operation": null, "requiredQuantity": "10"}}]}}, "productionItems": {"query": {"edges": [{"node": {"item": {"id": "PRD_USING_REV_PHANTOM"}, "quantityInStockUnit": "10", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}, "productionOperations": {"query": {"edges": []}}, "routingCode": null}}}, "Create a work order for a product using phantom managed by revision, prd without rev -> rev=null + cmp rev=REV-B": {"executionMode": "normal", "envConfigs": {"today": "2025-01-01", "testActiveServiceOptions": ["phantomItemOption", "billOfMaterialRevisionServiceOption"]}, "input": {"properties": {"workOrderNumber": "TEST_REV_PHANTOM", "startDate": "2025-08-15", "releasedQuantity": 10, "siteId": "US001", "releasedItem": "PRD_USING_REV_PHANTOM", "workOrderCategory": "#Assembly", "type": "firm", "name": "prd with phantom managed by revision", "bom": "PRD_USING_REV_PHANTOM"}}, "output": {"createWorkOrder": {"bomCode": {"name": "Product using phantom"}, "bomRevision": null, "name": "prd with phantom managed by revision", "number": "TEST_REV_PHANTOM", "productionComponents": {"query": {"edges": [{"node": {"componentNumber": 10, "instruction": {"value": ""}, "item": {"id": "MUSTARD"}, "linkQuantity": "0.005", "operation": null, "requiredQuantity": "0.5"}}, {"node": {"componentNumber": 20, "instruction": {"value": ""}, "item": {"id": "OLIVE-OIL"}, "linkQuantity": "0.008", "operation": null, "requiredQuantity": "0.08"}}, {"node": {"componentNumber": 30, "instruction": {"value": ""}, "item": {"id": "WINE-VINEGAR"}, "linkQuantity": "0.006", "operation": null, "requiredQuantity": "0.06"}}, {"node": {"componentNumber": 40, "instruction": {"value": ""}, "item": {"id": "BOX500"}, "linkQuantity": "1", "operation": null, "requiredQuantity": "10"}}]}}, "productionItems": {"query": {"edges": [{"node": {"item": {"id": "PRD_USING_REV_PHANTOM"}, "quantityInStockUnit": "10", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}, "productionOperations": {"query": {"edges": []}}, "routingCode": null}}}, "Create a work order for a product managed by revision using phantom managed by revision, prd rev=null & cmp rev=null": {"executionMode": "normal", "envConfigs": {"today": "2025-01-01", "testActiveServiceOptions": ["phantomItemOption", "billOfMaterialRevisionServiceOption"]}, "input": {"properties": {"workOrderNumber": "TEST_REV_PHANTOM", "startDate": "2025-05-15", "releasedQuantity": 10, "siteId": "US001", "releasedItem": "PRD_REVISION_USING_REV_PHANTOM", "workOrderCategory": "#Assembly", "type": "firm", "name": "prd with phantom managed by revision", "bom": "PRD_REVISION_USING_REV_PHANTOM"}}, "output": {"createWorkOrder": {"bomCode": {"name": "Product revision using phantom"}, "bomRevision": null, "name": "prd with phantom managed by revision", "number": "TEST_REV_PHANTOM", "productionComponents": {"query": {"edges": [{"node": {"componentNumber": 10, "instruction": {"value": ""}, "item": {"id": "MUSTARD"}, "linkQuantity": "0.04", "operation": null, "requiredQuantity": "0.4"}}, {"node": {"componentNumber": 20, "instruction": {"value": ""}, "item": {"id": "OLIVE-OIL"}, "linkQuantity": "0.12", "operation": null, "requiredQuantity": "1.2"}}, {"node": {"componentNumber": 30, "instruction": {"value": ""}, "item": {"id": "WINE-VINEGAR"}, "linkQuantity": "0.08", "operation": null, "requiredQuantity": "0.8"}}, {"node": {"componentNumber": 40, "instruction": {"value": ""}, "item": {"id": "BOX500"}, "linkQuantity": "3", "operation": null, "requiredQuantity": "30"}}]}}, "productionItems": {"query": {"edges": [{"node": {"item": {"id": "PRD_REVISION_USING_REV_PHANTOM"}, "quantityInStockUnit": "10", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}, "productionOperations": {"query": {"edges": []}}, "routingCode": null}}}, "Create a work order for a product managed by revision using phantom managed by revision, prd rev=null & cmp rev suspended -> revision=rev-A": {"executionMode": "normal", "envConfigs": {"today": "2025-01-01", "testActiveServiceOptions": ["phantomItemOption", "billOfMaterialRevisionServiceOption"]}, "input": {"properties": {"workOrderNumber": "TEST_REV_PHANTOM", "startDate": "2025-06-15", "releasedQuantity": 10, "siteId": "US001", "releasedItem": "PRD_REVISION_USING_REV_PHANTOM", "workOrderCategory": "#Assembly", "type": "firm", "name": "prd with phantom managed by revision", "bom": "PRD_REVISION_USING_REV_PHANTOM"}}, "output": {"createWorkOrder": {"bomCode": {"name": "Product revision using phantom"}, "bomRevision": null, "name": "prd with phantom managed by revision", "number": "TEST_REV_PHANTOM", "productionComponents": {"query": {"edges": [{"node": {"componentNumber": 10, "instruction": {"value": ""}, "item": {"id": "MUSTARD"}, "linkQuantity": "0.06", "operation": null, "requiredQuantity": "0.6"}}, {"node": {"componentNumber": 20, "instruction": {"value": ""}, "item": {"id": "OLIVE-OIL"}, "linkQuantity": "0.14", "operation": null, "requiredQuantity": "1.4"}}, {"node": {"componentNumber": 30, "instruction": {"value": ""}, "item": {"id": "BOX500"}, "linkQuantity": "3", "operation": null, "requiredQuantity": "30"}}]}}, "productionItems": {"query": {"edges": [{"node": {"item": {"id": "PRD_REVISION_USING_REV_PHANTOM"}, "quantityInStockUnit": "10", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}, "productionOperations": {"query": {"edges": []}}, "routingCode": null}}}, "Create a work order for a product managed by revision using phantom managed by revision, prd rev=rev-02 & cmp rev=rev-B": {"executionMode": "normal", "envConfigs": {"today": "2025-01-01", "testActiveServiceOptions": ["phantomItemOption", "billOfMaterialRevisionServiceOption"]}, "input": {"properties": {"workOrderNumber": "TEST_REV_PHANTOM", "startDate": "2025-09-15", "releasedQuantity": 10, "siteId": "US001", "releasedItem": "PRD_REVISION_USING_REV_PHANTOM", "workOrderCategory": "#Assembly", "type": "firm", "name": "prd with phantom managed by revision", "bom": "PRD_REVISION_USING_REV_PHANTOM"}}, "output": {"createWorkOrder": {"bomCode": {"name": "Product revision using phantom"}, "bomRevision": {"revision": "REV-02"}, "name": "prd with phantom managed by revision", "number": "TEST_REV_PHANTOM", "productionComponents": {"query": {"edges": [{"node": {"componentNumber": 10, "instruction": {"value": ""}, "item": {"id": "MUSTARD"}, "linkQuantity": "0.2", "operation": null, "requiredQuantity": "0.5"}}, {"node": {"componentNumber": 20, "instruction": {"value": ""}, "item": {"id": "OLIVE-OIL"}, "linkQuantity": "0.32", "operation": null, "requiredQuantity": "3.2"}}, {"node": {"componentNumber": 30, "instruction": {"value": ""}, "item": {"id": "WINE-VINEGAR"}, "linkQuantity": "0.24", "operation": null, "requiredQuantity": "2.4"}}, {"node": {"componentNumber": 40, "instruction": {"value": ""}, "item": {"id": "BOX500"}, "linkQuantity": "5", "operation": null, "requiredQuantity": "5"}}]}}, "productionItems": {"query": {"edges": [{"node": {"item": {"id": "PRD_REVISION_USING_REV_PHANTOM"}, "quantityInStockUnit": "10", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}, "productionOperations": {"query": {"edges": []}}, "routingCode": null}}}}