{"Create a work order operation with minimum information": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Operation 11", "operationNumber": 40, "plannedQuantity": 10, "startDate": "2020-06-10", "endDate": "2020-06-10", "isProductionStep": true, "runTimeUnit": "#HOUR", "setupTimeUnit": "#HOUR", "minCapabilityLevel": "1", "resources": [{"resource": "#GEKIN-G|US003", "expectedSetupTime": 10, "expectedRunTime": 60, "isResourceQuantity": true, "resources": [{"resource": "#<PERSON>|US003"}]}, {"resource": "#AFILM100L|US003", "expectedSetupTime": 20, "expectedRunTime": 55}]}}, "output": {"values": {"name": "Operation 11"}}}, "Create a work order operation with actual information": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Operation 11", "operationNumber": 40, "plannedQuantity": 10, "completedQuantity": 4, "startDate": "2020-06-10", "endDate": "2020-06-10", "isProductionStep": true, "runTimeUnit": "#HOUR", "setupTimeUnit": "#HOUR", "minCapabilityLevel": "1", "resources": [{"resource": "#GEKIN-G|US003", "expectedSetupTime": 10, "expectedRunTime": 60, "isResourceQuantity": true, "resources": [{"resource": "#<PERSON>|US003"}]}, {"resource": "#AFILM100L|US003", "expectedSetupTime": 20, "expectedRunTime": 55}]}}, "output": {"values": {"name": "Operation 11"}}}}