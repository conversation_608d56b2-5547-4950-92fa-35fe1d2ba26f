{"Create a work order for a product managed by revision using phantom managed by revision, prd rev suspended -> take previous revision=null & cmp rev suspended -> take previous revision=null": {"comment0": "Recap of the revisions and their statuses used in the next tests", "comment1": "PRD_REVISION_USING_REV_PHANTOM : -------------------|-------REV-01 (suspended)--------------|--------REV-02 (availableToUse)------------", "comment2": "CMP_REV_PHANTOM : -------------------|----------REV-A (suspended)----------|------------REV-B (availableToUse)--------------", "comment3": "                                     |              |                      |                |", "comment4": "                                2025-06-01     2025-07-01             2025-08-01       2025-09-01", "comment6": "=> WO start date used for PRD_REVISION_USING_REV_PHANTOM: 2025-07-15 & 2025-08-15", "executionMode": "normal", "envConfigs": {"today": "2025-01-01", "testActiveServiceOptions": ["phantomItemOption", "billOfMaterialRevisionServiceOption"]}, "input": {"properties": {"workOrderNumber": "TEST_REV_PHANTOM", "startDate": "2025-07-15", "releasedQuantity": 10, "siteId": "US001", "releasedItem": "PRD_REVISION_USING_REV_PHANTOM", "workOrderCategory": "#Assembly", "type": "firm", "name": "prd with phantom managed by revision", "bom": "PRD_REVISION_USING_REV_PHANTOM"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You can only add a BOM revision that is available to use to a work order.", "path": ["bomRevision"], "severity": 3}]}, "locations": [{"column": 7, "line": 4}], "message": "Create work order failed.", "path": ["xtremManufacturing", "workOrder", "createWorkOrder"]}]}}, "Create a work order for a product managed by revision using phantom managed by revision, prd rev suspended -> take previous revision=null & cmp rev=rev-B": {"executionMode": "normal", "envConfigs": {"today": "2025-01-01", "testActiveServiceOptions": ["phantomItemOption", "billOfMaterialRevisionServiceOption"]}, "input": {"properties": {"workOrderNumber": "TEST_REV_PHANTOM", "startDate": "2025-08-15", "releasedQuantity": 10, "siteId": "US001", "releasedItem": "PRD_REVISION_USING_REV_PHANTOM", "workOrderCategory": "#Assembly", "type": "firm", "name": "prd with phantom managed by revision", "bom": "PRD_REVISION_USING_REV_PHANTOM"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You can only add a BOM revision that is available to use to a work order.", "path": ["bomRevision"], "severity": 3}]}, "locations": [{"column": 7, "line": 4}], "message": "Create work order failed.", "path": ["xtremManufacturing", "workOrder", "createWorkOrder"]}]}}}