{
    xtremManufacturing {
        materialTrackingLine {
            query(first: 2) {
                edges {
                    node {
                        document {
                            number
                        }
                        line
                        workOrderLine {
                            componentNumber
                        }
                        materialType
                        quantityInStockUnit
                        isActiveQuantity
                        computedAttributes
                        stockTransactionStatus
                    }
                }
            }
        }
    }
}
