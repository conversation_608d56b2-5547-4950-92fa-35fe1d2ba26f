{"Create a work order component for normal line type": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Component 60", "componentNumber": 60, "lineType": "normal", "item": "#1324", "unit": "#EACH", "linkQuantity": 20, "requiredQuantity": 20, "requiredDate": "2021-03-18"}}, "output": {"name": "Component 60", "workInProgress": {"documentType": "materialNeed", "status": "planned", "startDate": "2021-03-18", "endDate": "2021-03-18", "expectedQuantity": "20", "actualQuantity": "0", "outstandingQuantity": "20"}, "scrapFactor": "0"}}, "Create a work order component for normal line type with operation": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Component 60", "componentNumber": 60, "lineType": "normal", "item": "#1324", "unit": "#EACH", "linkQuantity": 20, "requiredQuantity": 20, "requiredDate": "2021-03-18", "operation": 4}}, "output": {"name": "Component 60", "workInProgress": {"documentType": "materialNeed", "status": "planned", "startDate": "2021-03-18", "endDate": "2021-03-18", "expectedQuantity": "20", "actualQuantity": "0", "outstandingQuantity": "20"}, "scrapFactor": "0"}}, "Create a work order component for text line type": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "componentNumber": 70, "lineType": "text", "lineStatus": "completed"}}, "output": {"name": "", "workInProgress": null, "scrapFactor": "0"}}, "Create a work order component with scrap factor 15.50%": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Component 60", "componentNumber": 60, "lineType": "normal", "item": "#1324", "unit": "#EACH", "linkQuantity": 20, "requiredQuantity": 20, "requiredDate": "2020-06-10", "scrapFactor": "15.55"}}, "output": {"name": "Component 60", "workInProgress": {"actualQuantity": "0", "documentType": "materialNeed", "endDate": "2020-06-10", "expectedQuantity": "20", "outstandingQuantity": "20", "startDate": "2020-06-10", "status": "planned"}, "scrapFactor": "15.55"}}, "Create a work order component with scrap factor 15.552%": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Component 60", "componentNumber": 60, "lineType": "normal", "item": "#1324", "unit": "#EACH", "linkQuantity": 20, "requiredQuantity": 20, "requiredDate": "2020-06-10", "scrapFactor": 15.552}}, "output": {"name": "Component 60", "workInProgress": {"actualQuantity": "0", "documentType": "materialNeed", "endDate": "2020-06-10", "expectedQuantity": "20", "outstandingQuantity": "20", "startDate": "2020-06-10", "status": "planned"}, "scrapFactor": "15.56"}}, "Create a work order component with scrap factor 0.0002%": {"input": {"properties": {"workOrder": "#US003|WO2021US0030002", "name": "Component 60", "componentNumber": 60, "lineType": "normal", "item": "#1324", "unit": "#EACH", "linkQuantity": 20, "requiredQuantity": 20, "requiredDate": "2020-06-10", "scrapFactor": 0.0002}}, "output": {"name": "Component 60", "workInProgress": {"actualQuantity": "0", "documentType": "materialNeed", "endDate": "2020-06-10", "expectedQuantity": "20", "outstandingQuantity": "20", "startDate": "2020-06-10", "status": "planned"}, "scrapFactor": "0.01"}}}