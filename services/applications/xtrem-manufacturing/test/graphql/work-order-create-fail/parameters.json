{"Create a work order with baseQuantity=0 fails": {"executionMode": "normal", "envConfigs": {"today": "2020-06-02"}, "input": {"properties": {"number": "5", "site": "5", "type": "planned", "name": "WO 5", "startDate": "2020-06-03", "endDate": "2020-06-05", "category": "2", "bomCode": "1", "routingCode": "1", "baseQuantity": 0, "productionItems": [{"releasedItem": "#17890", "releasedQuantity": 10}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "value must be greater than 0", "path": ["baseQuantity"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrder", "create"]}]}}, "Create a work order with a phantom as released item fails": {"executionMode": "normal", "envConfigs": {"today": "2024-07-16", "testActiveServiceOptions": ["phantomItemOption"]}, "input": {"properties": {"number": "5", "site": "5", "type": "planned", "name": "WO 5", "startDate": "2024-07-16", "endDate": "2024-07-16", "category": "2", "bomCode": "#DRESSING|US001", "baseQuantity": 1, "productionItems": [{"releasedItem": "#DRESSING", "releasedQuantity": 10}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The record is not valid. You need to select a different record.", "path": ["bomCode"], "severity": 3}, {"message": "You can only create a work order for a standard BOM. You cannot create a work order for a phantom BOM.", "path": ["bomCode"], "severity": 3}, {"message": "The record is not valid. You need to select a different record.", "path": ["productionItems", "-1000000002", "releasedItem"], "severity": 3}, {"message": "You can only add a stock item to a work order, not a phantom item.", "path": ["productionItems", "-1000000002", "releasedItem"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrder", "create"]}]}}, "Create a work order with wrong revision": {"executionMode": "normal", "envConfigs": {"today": "2025-06-27", "testActiveServiceOptions": ["billOfMaterialRevisionServiceOption"]}, "input": {"properties": {"number": "test with revision", "site": "#US001", "type": "planned", "name": "WO with revision", "startDate": "2025-06-27", "endDate": "2025-06-29", "category": "2", "bomCode": "#COST_REV_C|US001", "bomRevision": "#BomRevision|US003|REV-B", "baseQuantity": 15, "productionItems": [{"releasedItem": "#COST_REV_C", "releasedQuantity": 10}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The BOM revision needs to be for the BOM code. Item: COST_REV_C, Site: US001.", "path": ["bomRevision"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrder", "create"]}]}}, "Create a work order with suspended revision": {"executionMode": "normal", "envConfigs": {"today": "2025-06-27", "testActiveServiceOptions": ["billOfMaterialRevisionServiceOption"]}, "input": {"properties": {"number": "test with revision", "site": "#US001", "type": "planned", "name": "WO with revision", "startDate": "2025-06-27", "endDate": "2025-06-29", "category": "2", "bomCode": "#COST_REV_C|US001", "bomRevision": "#COST_REV_C|US001|REV-D", "baseQuantity": 15, "productionItems": [{"releasedItem": "#COST_REV_C", "releasedQuantity": 10}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You can only add a BOM revision that is available to use to a work order.", "path": ["bomRevision"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrder", "create"]}]}}, "Create a work order with not started revision": {"executionMode": "normal", "envConfigs": {"today": "2025-01-01", "testActiveServiceOptions": ["billOfMaterialRevisionServiceOption"]}, "input": {"properties": {"number": "test with revision", "site": "#US001", "type": "planned", "name": "WO with revision", "startDate": "2025-01-01", "endDate": "2025-01-02", "category": "2", "bomCode": "#COST_REV_C|US001", "bomRevision": "#COST_REV_C|US001|REV-B", "baseQuantity": 15, "productionItems": [{"releasedItem": "#COST_REV_C", "releasedQuantity": 10}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The BOM revision needs to be available to use on the work order start date.", "path": ["bomRevision"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremManufacturing", "workOrder", "create"]}]}}}