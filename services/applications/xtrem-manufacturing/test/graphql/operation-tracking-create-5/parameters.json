{"Mutation createOperationTrackings with a specific operation number": {"executionMode": "normal", "input": {"properties": {"workOrder": "#US003|WO2021US0030002", "workOrderTrackingNumber": "WOT1", "trackingDate": "2021-03-26", "trackingQuantity": 1, "operationNumber": 10}}, "output": {"createOperationTrackings": {"number": "WOT1", "lines": {"query": {"edges": [{"node": {"workOrderOperation": {"operationNumber": 10}, "actualResource": {"id": "MLS-C912"}, "actualSetupTime": "0", "setupTimeUnit": {"id": "SECOND"}, "actualRunTime": "1", "runTimeUnit": {"id": "SECOND"}, "completedQuantity": "1", "completed": false}}, {"node": {"workOrderOperation": {"operationNumber": 10}, "actualResource": {"id": "<PERSON>"}, "actualSetupTime": "0", "setupTimeUnit": {"id": "SECOND"}, "actualRunTime": "1", "runTimeUnit": {"id": "SECOND"}, "completedQuantity": "0", "completed": false}}]}}}}}, "Mutation createOperationTrackings without operation number": {"executionMode": "normal", "input": {"properties": {"workOrder": "#US003|WO2021US0030002", "workOrderTrackingNumber": "TRK2", "trackingDate": "2021-03-26", "trackingQuantity": 1}}, "output": {"createOperationTrackings": {"number": "TRK2", "lines": {"query": {"edges": [{"node": {"workOrderOperation": {"operationNumber": 5}, "actualResource": {"id": "AFILM100L"}, "actualSetupTime": "0", "setupTimeUnit": {"id": "SECOND"}, "actualRunTime": "1", "runTimeUnit": {"id": "SECOND"}, "completedQuantity": "1", "completed": false}}, {"node": {"workOrderOperation": {"operationNumber": 5}, "actualResource": {"id": "<PERSON>"}, "actualSetupTime": "0", "setupTimeUnit": {"id": "SECOND"}, "actualRunTime": "1", "runTimeUnit": {"id": "SECOND"}, "completedQuantity": "0", "completed": false}}, {"node": {"workOrderOperation": {"operationNumber": 10}, "actualResource": {"id": "MLS-C912"}, "actualSetupTime": "0", "setupTimeUnit": {"id": "SECOND"}, "actualRunTime": "1", "runTimeUnit": {"id": "SECOND"}, "completedQuantity": "1", "completed": false}}, {"node": {"workOrderOperation": {"operationNumber": 10}, "actualResource": {"id": "<PERSON>"}, "actualSetupTime": "0", "setupTimeUnit": {"id": "SECOND"}, "actualRunTime": "1", "runTimeUnit": {"id": "SECOND"}, "completedQuantity": "0", "completed": false}}, {"node": {"workOrderOperation": {"operationNumber": 15}, "actualResource": {"id": "AUTLAB"}, "actualSetupTime": "0", "setupTimeUnit": {"id": "SECOND"}, "actualRunTime": "0", "runTimeUnit": {"id": "SECOND"}, "completedQuantity": "1", "completed": false}}, {"node": {"workOrderOperation": {"operationNumber": 15}, "actualResource": {"id": "<PERSON>"}, "actualSetupTime": "0", "setupTimeUnit": {"id": "SECOND"}, "actualRunTime": "0", "runTimeUnit": {"id": "SECOND"}, "completedQuantity": "0", "completed": false}}, {"node": {"workOrderOperation": {"operationNumber": 20}, "actualResource": {"id": "<PERSON>"}, "actualSetupTime": "0", "setupTimeUnit": {"id": "SECOND"}, "actualRunTime": "2", "runTimeUnit": {"id": "SECOND"}, "completedQuantity": "1", "completed": false}}]}}}}}}