import type { Context, NodeSelectOptions, decimal, integer } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib';

async function assertionMaterialCost(
    workOrder: xtremManufacturing.nodes.WorkOrder,
    expectedCost: {
        componentNumber: integer;
        plannedCost: decimal;
    }[],
) {
    const materialCost = await workOrder.productionComponents
        .map(async component => ({
            componentNumber: await component.componentNumber,
            plannedCost: await component.plannedCost,
        }))
        .toArray();
    assert.deepEqual(materialCost, expectedCost);
}

async function assertionProcessCost(
    workOrder: xtremManufacturing.nodes.WorkOrder,
    expectedCost: {
        workOrderOperation: { operationNumber: integer };
        resource: { id: string };
        expectedSetupCost: decimal;
        expectedRunCost: decimal;
    }[],
) {
    const processCost = await workOrder.productionOperations.reduce(
        async (cum, operation) => {
            return cum.concat(
                await operation.resources
                    .map(async operationResource => ({
                        workOrderOperation: {
                            operationNumber: await (await operationResource.workOrderOperation).operationNumber,
                        },
                        resource: {
                            id: await (await operationResource.resource).id,
                        },
                        expectedSetupCost: await operationResource.expectedSetupCost,
                        expectedRunCost: await operationResource.expectedRunCost,
                    }))
                    .toArray(),
            );
        },
        [] as {
            workOrderOperation: { operationNumber: integer };
            resource: { id: string };
            expectedSetupCost: decimal;
            expectedRunCost: decimal;
        }[],
    );

    assert.deepEqual(processCost, expectedCost);
}

export async function assertionPlannedCost(
    context: Context,
    workOrderNumber: string,
    expectedCost: {
        totalCost: { plannedMaterialCost: decimal; plannedProcessCost: decimal; plannedOverheadCost: decimal };
        productionComponents?: {
            componentNumber: integer;
            plannedCost: decimal;
        }[];
        productionOperations?: {
            workOrderOperation: { operationNumber: integer };
            resource: { id: string };
            expectedSetupCost: decimal;
            expectedRunCost: decimal;
        }[];
    },
) {
    const workOrder = await context.read(
        xtremManufacturing.nodes.WorkOrder,

        { number: workOrderNumber },
    );

    if (expectedCost.productionComponents) {
        await assertionMaterialCost(workOrder, expectedCost.productionComponents);
    }

    if (expectedCost.productionOperations) {
        await assertionProcessCost(workOrder, expectedCost.productionOperations);
    }

    const totalCost = {
        plannedMaterialCost: await workOrder.plannedMaterialCost,
        plannedProcessCost: await workOrder.plannedProcessCost,
        plannedOverheadCost: await workOrder.plannedOverheadCost,
    };

    assert.deepEqual(totalCost, expectedCost.totalCost);
}

export async function assertionWipCosts(
    context: Context,
    queryOptions: NodeSelectOptions<xtremManufacturing.nodes.WorkInProgressCost>,
    expectedResults: {
        type: Awaited<xtremManufacturing.nodes.WorkInProgressCost['type']>;
        cost: Awaited<xtremManufacturing.nodes.WorkInProgressCost['cost']>;
        amount: Awaited<xtremManufacturing.nodes.WorkInProgressCost['amount']>;
        quantity: Awaited<xtremManufacturing.nodes.WorkInProgressCost['quantity']>;
    }[],
) {
    const wipCosts = await context.select(
        xtremManufacturing.nodes.WorkInProgressCost,
        { type: true, cost: true, amount: true, quantity: true },
        queryOptions,
    );
    assert.deepEqual(wipCosts, expectedResults);
}
