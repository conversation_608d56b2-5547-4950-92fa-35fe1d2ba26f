import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib/index';
import type { UpdateBomTrackingStatusesArgs } from '../../../lib/interfaces';

describe('validateWorkOrderTrackingParameters function', () => {
    it('check validation of parameters all blank, zero - Check error responses', () =>
        Test.withContext(context => {
            assert.throws(
                () => xtremManufacturing.functions.validateWorkOrderTrackingParameters(context, '', 0, ''),
                "Released item ID missing. Work order number missing. Release quantity '0' is invalid.",
            );
        }));

    it('check validation of parameters invalid item, work order, quantity - Check error responses', () =>
        Test.withContext(context => {
            assert.throws(
                () => xtremManufacturing.functions.validateWorkOrderTrackingParameters(context, 'XXX', -10, 'YYY'),
                "Release quantity '-10' is invalid.",
            );
        }));
});

describe('updateBomTrackingStatuses function', () => {
    it('check that not more than one bom record linked to a work order - Check error responses', () =>
        Test.withContext(async context => {
            const statusUpdateInput: UpdateBomTrackingStatusesArgs = {
                number: 'WO2023US0010001',
                stockStatus: 'completed',
                workOrderStatus: 'completed',
            };

            await assert.isRejected(
                xtremManufacturing.functions.updateBomTrackingStatuses(context, statusUpdateInput),
                'There is more than one bill of material linked to the work order.',
            );
        }));
});
