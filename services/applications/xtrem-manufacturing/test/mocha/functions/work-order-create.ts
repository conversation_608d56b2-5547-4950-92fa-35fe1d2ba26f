import type { LocalizeLocale } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import { assert, expect } from 'chai';
import * as xtremManufacturing from '../../../lib/index';

describe('convertRoutingOperationToWorkOrderOperation function', () => {
    it('Get work order operation from bill of material component operation - Check properties', () =>
        Test.withContext(async context => {
            const workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
                number: 'TEST WO',
                name: 'New work order',
                startDate: date.today(),
                type: 'firm',
                baseQuantity: 100,
                routingCode: 1,
                category: 1,
            });
            const component = await context.create(xtremTechnicalData.nodes.Component, {
                billOfMaterial: 1,
                componentNumber: 100,
                item: '#Chemical A',
                linkQuantity: 10,
                operation: 7,
            });
            await workOrder.productionOperations.append({
                workOrder: 1,
                operationNumber: 10,
            });
            await workOrder.productionOperations.append({
                workOrder: 1,
                operationNumber: 20,
            });
            const componentOperation = await component.operation;
            if (componentOperation) {
                const operation = await xtremManufacturing.functions.convertRoutingOperationToWorkOrderOperation(
                    context,
                    workOrder.productionOperations,
                    componentOperation._id,
                );
                assert.equal(await operation?.operationNumber, 20);
                assert.equal(await operation?.expectedRunTime, 0);
            }
            await component.$.set({
                operation: await context.read(xtremTechnicalData.nodes.Operation, { _id: 10 }),
            });
            const operation2 = await xtremManufacturing.functions.convertRoutingOperationToWorkOrderOperation(
                context,
                workOrder.productionOperations,
                (await component.operation)?._id!,
            );
            assert.equal(operation2, null);
        }));
});

describe('validateWorkOrderParameters function', () => {
    it('Validate the mandatory parameters provided for work order creation', () =>
        Test.withContext(async context => {
            await expect(
                xtremManufacturing.functions.validateWorkOrderParameters(context, {
                    workOrderNumber: '',
                    siteId: '',
                    releasedItem: '',
                    releasedQuantity: -10,
                    hasBillOfMaterial: true,
                    hasRouting: true,
                }),
            ).to.be.rejected;
        }));

    it('Validate work order number', () =>
        Test.withContext(async context => {
            await expect(
                xtremManufacturing.functions.validateWorkOrderParameters(context, {
                    workOrderNumber: 'WO2021US0030001',
                    siteId: 'US003',
                    releasedItem: '17890',
                    releasedQuantity: 100,
                    hasBillOfMaterial: true,
                    hasRouting: false,
                    bom: '17890',
                }),
            ).to.be.rejectedWith('The work order number is already in use. Enter a unique work order reference.');
        }));

    it('Validate site id', () =>
        Test.withContext(async context => {
            await expect(
                xtremManufacturing.functions.validateWorkOrderParameters(context, {
                    workOrderNumber: '',
                    siteId: '',
                    releasedItem: '17890',
                    releasedQuantity: 100,
                    hasBillOfMaterial: true,
                    hasRouting: false,
                    bom: '17890',
                }),
            ).to.be.rejectedWith('Site ID missing');
        }));

    it('Validate released item id', () =>
        Test.withContext(async context => {
            await expect(
                xtremManufacturing.functions.validateWorkOrderParameters(context, {
                    workOrderNumber: '',
                    siteId: 'US003',
                    releasedItem: '',
                    releasedQuantity: 100,
                    hasBillOfMaterial: true,
                    hasRouting: false,
                    bom: '17890',
                }),
            ).to.be.rejectedWith('ReleasedItem ID missing');
        }));

    it('Validate the optional date parameter provided for work order creation', () =>
        Test.withContext(async context => {
            await xtremManufacturing.functions.validateWorkOrderParameters(context, {
                workOrderNumber: '',
                siteId: 'US003',
                releasedItem: '17890',
                releasedQuantity: 100,
                hasBillOfMaterial: true,
                hasRouting: false,
                startDate: date.parse('2020-06-01', context.currentLocale as LocalizeLocale),
                bom: '17890',
            });
            const { diagnoses } = context;
            assert.equal(diagnoses.at(0)?.message, "The date '2020-06-01' is before the current date.");
        }));
});

describe('addComponentsToWorkOrder function', () => {
    it('Add components to work order from bill of material components - Check properties', () =>
        Test.withContext(async context => {
            const workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
                number: 'TEST WO',
                name: 'New work order',
                startDate: date.today(),
                type: 'firm',
                baseQuantity: 100,
                routingCode: 1,
                category: 1,
            });
            const billOfMaterial = await context.read(xtremTechnicalData.nodes.BillOfMaterial, {
                site: '#US001',
                item: '#Chair',
            });
            await assert.isRejected(
                xtremManufacturing.functions.addComponentsToWorkOrder(context, workOrder, billOfMaterial, null),
                'Cannot create work order operations with no work order production items',
            );
        }));

    it('Add components to work order from bill of material components - Check required quantity', () =>
        Test.withContext(async context => {
            const billOfMaterial = await context.read(xtremTechnicalData.nodes.BillOfMaterial, {
                site: '#US001',
                item: '#Chair',
            });
            const workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
                site: '#US001',
                number: 'TEST WO',
                name: 'New work order',
                startDate: date.today(),
                type: 'firm',
                baseQuantity: 5,
                productionItems: [
                    {
                        releasedItem: '#Chair',
                        releasedQuantity: 5,
                    },
                ],
                bomCode: billOfMaterial,
                routingCode: 1,
                category: 1,
            });
            await xtremManufacturing.functions.addComponentsToWorkOrder(context, workOrder, billOfMaterial, null);
            await workOrder.productionComponents.forEach(async component => {
                // eslint-disable-next-line default-case
                switch (await component.name) {
                    case 'Chair leg':
                        assert.equal(await component.requiredQuantity, 20);
                        break;
                    case 'Chair seat':
                        assert.equal(await component.requiredQuantity, 6);
                        break;
                    case 'Chair back':
                        assert.equal(await component.requiredQuantity, 5);
                        break;
                }
            });
        }));
});

describe('addOperationsToWorkOrder function', () => {
    it('Add operations to work order from routing operations - Check properties', () =>
        Test.withContext(async context => {
            const workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
                number: 'TEST WO',
                name: 'New work order',
                startDate: date.today(),
                type: 'firm',
                baseQuantity: 100,
                routingCode: 1,
                category: 1,
            });
            const item = await context.read(xtremMasterData.nodes.Item, { id: '17890-B' });
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US003' });
            const routing = await context.read(xtremTechnicalData.nodes.Routing, {
                site,
                item,
            });
            await assert.isRejected(
                xtremManufacturing.functions.addOperationsToWorkOrder(context, workOrder, routing),
                'Cannot create work order operations with no work order production items',
            );
        }));
});

describe('trackBillOfMaterial function', () => {
    it('Track phantom bill of material - fails', () =>
        Test.withContext(
            async context => {
                const phantomBom = await context.read(xtremTechnicalData.nodes.BillOfMaterial, {
                    item: '#DRESSING',
                    site: '#US001',
                });
                const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                    id: 'Assembly',
                });
                await assert.isRejected(
                    xtremManufacturing.functions.trackBillOfMaterial(context, {
                        bomId: `${phantomBom._id}`,
                        siteId: 'US001',
                        itemId: 'DRESSING',
                        itemName: 'Dressing',
                        quantity: 10,
                        workOrderCategoryId: `${workOrderCategory._id}`,
                        stockDetails: '',
                    }),
                    'You can only track a work order for an item that does not have a phantom component.',
                );
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.phantomItemOption],
            },
        ));
    it('Track bill of material with Phantom component - fails', () =>
        Test.withContext(
            async context => {
                const phantomBom = await context.read(xtremTechnicalData.nodes.BillOfMaterial, {
                    item: '#CARROT-BOX',
                    site: '#US001',
                });
                const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                    id: 'Assembly',
                });
                await assert.isRejected(
                    xtremManufacturing.functions.trackBillOfMaterial(context, {
                        bomId: `${phantomBom._id}`,
                        siteId: 'US001',
                        itemId: 'CARROT-BOX',
                        itemName: 'Carrot box',
                        quantity: 10,
                        workOrderCategoryId: `${workOrderCategory._id}`,
                        stockDetails: '',
                    }),
                    'You can only track a work order for an item that does not have a phantom component.',
                );
            },
            {
                testActiveServiceOptions: [xtremMasterData.serviceOptions.phantomItemOption],
            },
        ));
});
