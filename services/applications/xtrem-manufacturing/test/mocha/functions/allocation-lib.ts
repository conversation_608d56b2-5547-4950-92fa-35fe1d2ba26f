// tslint:disable:no-duplicate-string
import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../index';

describe('AllocationLib', () => {
    before(() => {});
    it('manufacturingConvertFilterToMassProcessCriteria return the expected object - All criteria given', () =>
        Test.withContext(() => {
            const filterInput = {
                item: {
                    name: {
                        _gte: 'A bottle of milk',
                        _lte: 'Triisopropanolamine',
                    },
                },
                workOrder: {
                    site: {
                        id: 'US001',
                    },
                    startDate: {
                        _lte: '2023-10-25',
                    },
                    number: {
                        _gte: 'WO2023US0010001',
                        _lte: 'WOS-SM-FIRM',
                    },
                    productionItems: {
                        releasedItem: {
                            name: {
                                _gte: 'Bottle 100 ml Hydro-alcoholic gel for hand antisepsis',
                                _lte: 'Hydro-alcoholic 4 gel for hand antisepsis',
                            },
                        },
                    },
                },
                operation: {
                    operationNumber: {
                        _gte: '10',
                        _lte: '10',
                    },
                },
            };
            const criteriaOutput = {
                stockSite: 'US001',
                fromOrderNumber: 'WO2023US0010001',
                toOrderNumber: 'WOS-SM-FIRM',
                fromReleasedItem: 'Bottle 100 ml Hydro-alcoholic gel for hand antisepsis',
                toReleasedItem: 'Hydro-alcoholic 4 gel for hand antisepsis',
                maximumStartDate: '2023-10-25',
                toOperation: '10',
                fromOperation: '10',
            };

            const result =
                xtremManufacturing.functions.allocationLib.manufacturingConvertFilterToMassProcessCriteria(filterInput);

            assert.deepEqual(result, criteriaOutput);
        }));

    it('manufacturingConvertFilterToMassProcessCriteria return the expected object - Only site criteria given', () =>
        Test.withContext(() => {
            const filterInput = {
                workOrder: {
                    site: {
                        id: 'US001',
                    },
                },
            };

            const criteriaOutput = { stockSite: 'US001' };

            const result =
                xtremManufacturing.functions.allocationLib.manufacturingConvertFilterToMassProcessCriteria(filterInput);

            assert.deepEqual(result, criteriaOutput);
        }));

    it('manufacturingConvertFilterToMassProcessCriteria return the expected object - Mixed criteria given', () =>
        Test.withContext(() => {
            const filterInput = {
                workOrder: {
                    site: {
                        id: 'US001',
                    },
                    startDate: {
                        _lte: '2023-10-25',
                    },
                    number: {
                        _gte: 'WO2023US0010001',
                        _lte: 'WOS-SM-FIRM',
                    },
                    productionItems: {
                        releasedItem: {
                            name: {
                                _gte: 'Bottle 100 ml Hydro-alcoholic gel for hand antisepsis',
                            },
                        },
                    },
                },
            };

            const criteriaOutput = {
                stockSite: 'US001',
                fromOrderNumber: 'WO2023US0010001',
                toOrderNumber: 'WOS-SM-FIRM',
                fromReleasedItem: 'Bottle 100 ml Hydro-alcoholic gel for hand antisepsis',
                maximumStartDate: '2023-10-25',
            };

            const result =
                xtremManufacturing.functions.allocationLib.manufacturingConvertFilterToMassProcessCriteria(filterInput);

            assert.deepEqual(result, criteriaOutput);
        }));
});
