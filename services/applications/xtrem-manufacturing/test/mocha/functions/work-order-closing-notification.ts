import type { Context } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib/index';

describe('Work order closing finance notification', () => {
    before(() => {});

    async function createWorkOrderClosingDocument(context: Context): Promise<{
        document: xtremFinanceData.interfaces.FinanceOriginDocument;
        documentLines: xtremFinanceData.interfaces.WorkOrderClosingFinanceDocumentLine[];
    }> {
        const financialSite = await context.read(xtremSystem.nodes.Site, {
            id: 'US001',
        });
        const transactionCurrency = await context.read(xtremMasterData.nodes.Currency, {
            id: 'USD',
        });
        const item = await context.read(xtremMasterData.nodes.Item, {
            id: 'ChairLeg',
        });

        const originatingLine = await context.read(xtremManufacturing.nodes.WorkOrderReleasedItem, {
            _id: '#US003|WO2021US0030001|17890-B',
        });

        const document: xtremFinanceData.interfaces.FinanceOriginDocument = {
            _id: 14,
            number: 'WIPDOC04',
            documentDate: date.today(),
            financialSite,
            transactionCurrency,
            fxRateDate: date.today(),
            companyFxRate: 2,
            companyFxRateDivisor: 4,
        } as any; // TODO: convert to node instance
        const documentLine = {
            _id: 134,
            item,
            computedAttributes: null,
            storedAttributes: null,
            storedDimensions: null,
            amount: 17.53,
            workInProgressCostType: 'workOrderVariance',
            originatingLine,
        } as unknown as xtremFinanceData.interfaces.WorkOrderClosingFinanceDocumentLine;
        const documentLines: xtremFinanceData.interfaces.WorkOrderClosingFinanceDocumentLine[] = [];
        documentLines.push(documentLine);

        return { document, documentLines };
    }

    it('Create notifications from a work order closing', () =>
        Test.withContext(
            async context => {
                const { document, documentLines } = await createWorkOrderClosingDocument(context);
                const notifications =
                    await xtremFinanceData.functions.ManufacturingNotificationLib.workOrderClosingNotification(
                        context,
                        document,
                        documentLines,
                    );
                assert.equal(notifications.batchSize, 1);

                const financeTransactionRecord: xtremFinanceData.interfaces.FinanceTransactionRecord = {
                    batchId: notifications.batchId,
                    documentNumber: await document.number,
                    documentType: 'workInProgress',
                    targetDocumentType: 'journalEntry',
                };

                const financeIntegrationStatus = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                    ...financeTransactionRecord,
                });

                assert.equal(await financeIntegrationStatus.message, '');

                const replyPayload: xtremFinanceData.interfaces.FinanceTransactionData = {
                    ...financeTransactionRecord,
                    validationMessages: [],
                    targetDocumentNumber: '',
                    targetDocumentSysId: 0,
                    status: 'recorded',
                };

                // hack to make the state of the context when been triggered by a notification
                (context as any)._contextValues.notificationId = '4445';
                (context as any)._contextValues.replyTopic = 'WorkOrderClosing/accountingInterface';
                await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, replyPayload);

                const newFinanceIntegrationStatus = await context.read(xtremFinanceData.nodes.FinanceTransaction, {
                    ...financeTransactionRecord,
                });

                assert.equal(await newFinanceIntegrationStatus.status, 'recorded');
            },
            {
                today: '2021-08-23',
            },
        ));
});
