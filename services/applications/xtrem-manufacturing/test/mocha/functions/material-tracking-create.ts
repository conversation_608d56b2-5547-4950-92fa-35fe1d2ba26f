import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib/index';

describe('validateMaterialTrackingParameters function', () => {
    it('check validation of parameters all blank, zero - Check error responses', () =>
        Test.withContext(async context => {
            const workOrder = await context.tryRead(xtremManufacturing.nodes.WorkOrder, { number: 'WO2021US0030001' });
            if (workOrder) {
                await assert.isRejected(
                    xtremManufacturing.functions.validateMaterialTrackingParameters(context, workOrder, 0, '', -100),
                    `Work order tracking number missing.
Release quantity '0' is invalid.
Component number '-100' is invalid.`,
                );
            }
        }));
    it('check validation of parameters invalid component number - Check error responses', () =>
        Test.withContext(async context => {
            const workOrder = await context.tryRead(xtremManufacturing.nodes.WorkOrder, { _id: 1 });
            if (workOrder) {
                await assert.isRejected(
                    xtremManufacturing.functions.validateMaterialTrackingParameters(
                        context,
                        workOrder,
                        -10,
                        'WOT0001',
                        99,
                    ),
                    `Release quantity '-10' is invalid.
Component number '99' does not exist on work order.`,
                );
            }
        }));
});
