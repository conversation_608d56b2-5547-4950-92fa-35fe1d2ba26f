import type { Context } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib';

function getSecondUnitOfMeasure(context: Context) {
    return context.read(xtremMasterData.nodes.UnitOfMeasure, { _id: '#SECOND' });
}

async function getTracking1(context: Context): Promise<xtremManufacturing.functions.operationTracking.TimeTracking> {
    const unitSecond = getSecondUnitOfMeasure(context);
    return {
        workOrderOperation: {
            workOrder: await context.read(xtremManufacturing.nodes.WorkOrder, { _id: '#US003|WO2021US0030001' }),
            minCapabilityLevel: await context.read(xtremMasterData.nodes.CapabilityLevel, { _id: '#Master' }),
            name: '<PERSON><PERSON> filling',
            remainingQuantity: 10,
        },
        expectedResource: await context.read(xtremMasterData.nodes.DetailedResource, { _id: '#AFILM100L|US003' }),
        actualQuantity: 1,
        setupTimeUnit: unitSecond,
        runTimeUnit: unitSecond,
        actualSetupTime: Promise.resolve(10),
        actualRunTime: Promise.resolve(50),
        status: Promise.resolve('pending'),
        isCompleted: false,
        // Where is this data ?
        completedQuantity: '0',
        completedSetupTime: '0',
        plannedQuantity: '1',
        completedRunTime: '0',
    };
}
async function getTracking2(context: Context): Promise<xtremManufacturing.functions.operationTracking.TimeTracking> {
    const unitSecond = getSecondUnitOfMeasure(context);
    return Promise.resolve({
        workOrderOperation: {
            workOrder: await context.read(xtremManufacturing.nodes.WorkOrder, { _id: '#US003|WO2021US0030002' }),
            operationNumber: 10,
            name: 'Laying screw cap',
            // operationName: 'Bottle filling',
            remainingQuantity: 10,
        },
        expectedResource: await context.read(xtremMasterData.nodes.DetailedResource, { _id: '#AFILM100L|US003' }),

        actualQuantity: 1,
        setupTimeUnit: unitSecond,
        runTimeUnit: unitSecond,
        actualSetupTime: Promise.resolve(10),
        actualRunTime: Promise.resolve(50),
        status: Promise.resolve('pending'),
        isCompleted: false,
        completedQuantity: '1',
        completedSetupTime: '0',
        plannedQuantity: '1',
        completedRunTime: '0',
    });
}

async function getTracking3(context: Context): Promise<xtremManufacturing.functions.operationTracking.TimeTracking> {
    const unitSecond = getSecondUnitOfMeasure(context);
    return Promise.resolve({
        workOrderOperation: {
            workOrder: await context.read(xtremManufacturing.nodes.WorkOrder, { _id: '#US003|WO2021US0030003' }),
            operationNumber: 20,
            name: 'Laying screw cap',
            // operationName: 'Bottle filling',
            remainingQuantity: 10,
        },
        expectedResource: await context.read(xtremMasterData.nodes.DetailedResource, {
            _id: '#AFILM100L|US003' as any,
        }),
        actualQuantity: 1,
        setupTimeUnit: unitSecond,
        runTimeUnit: unitSecond,
        actualSetupTime: Promise.resolve(10),
        actualRunTime: Promise.resolve(50),
        status: Promise.resolve('pending'),
        isCompleted: false,
        completedQuantity: '1',
        completedSetupTime: '0',
        plannedQuantity: '1',
        completedRunTime: '0',
    });
}

describe('Create multiple operational trackings ', () => {
    it('createMultipleOperationalTrackingsFromTimeTracking ', () =>
        Test.withContext(async context => {
            const tracking1 = await getTracking1(context);
            const tracking2 = await getTracking2(context);
            const tracking3 = await getTracking3(context);
            const numberOfTrackings1 =
                await xtremManufacturing.functions.operationTracking.createMultipleOperationalTrackingsFromTimeTracking(
                    context,
                    [tracking1],
                );
            assert.equal(numberOfTrackings1.length, 1);
            const numberOfTrackings2 =
                await xtremManufacturing.functions.operationTracking.createMultipleOperationalTrackingsFromTimeTracking(
                    context,
                    [tracking1, tracking2],
                );
            assert.equal(numberOfTrackings2.length, 2);
            const numberOfTrackings3 =
                await xtremManufacturing.functions.operationTracking.createMultipleOperationalTrackingsFromTimeTracking(
                    context,
                    [tracking1, tracking2, tracking3],
                );
            assert.equal(numberOfTrackings3.length, 3);
        }));
    it('tracking that fail ', () =>
        Test.withContext(async context => {
            await xtremManufacturing.functions.operationTracking.createMultipleOperationalTrackingsFromTimeTracking(
                context,
                [await getTracking1(context)],
            );
        }));
});
