import type { Context, decimal } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as Sinon from 'sinon';
import * as xtremManufacturing from '../../../lib';

describe('getCurrentWipVariance & getStockJournalValue', () => {
    async function createWipVariance(
        writableContext: Context,
        args: {
            workOrderToUpdate: xtremManufacturing.nodes.WorkOrder;
            amount: decimal;
            type: xtremManufacturing.enums.WorkInProgressType;
        },
    ) {
        const wip = await writableContext.create(xtremManufacturing.nodes.WorkInProgressCost, {
            originatingLine: await args.workOrderToUpdate.productionItems.elementAt(0),
            workOrder: args.workOrderToUpdate,
            quantity: 1,
            cost: Math.abs(args.amount),
            amount: Math.abs(args.amount),
            status: 'pending',
            type: args.type,
            unit: await (await (await args.workOrderToUpdate.productionItem)?.releasedItem)?.stockUnit,
            currency: await (await (await args.workOrderToUpdate.site).legalCompany).currency,
        });
        await wip.$.save();
    }

    it('getStockJournalValue', () =>
        Test.withContext(async context => {
            const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: 'WIP Variance test 1' });
            const result = await xtremManufacturing.functions.workOrderLib.getStockJournalValue(workOrder);
            assert.deepEqual(result, {
                movementAmountTotal: 493.8,
                nonAbsorbedTotal: 1139.69,
                orderAmountTotal: 1633.49,
            });
        }));

    it('getCurrentWipVariance', () =>
        Test.withContext(async context => {
            // The 'WIP Variance test 1' work order is closed, stock journal of production trackings updated
            // but WIP cost variance is still missing
            const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: 'WIP Variance test 1' });
            const result = await xtremManufacturing.functions.workOrderLib.getCurrentWipVariance(workOrder);
            assert.deepEqual(result, {
                absorbedWipAmount: 493.8,
                nonAbsorbedWipAmount: 0,
            });
        }));

    it('getCurrentWipVariance', () =>
        // The 'WIP Variance test 1' work order is closed, stock journal of production trackings updated
        // but WIP cost variance is still missing
        Test.withContext(async context => {
            const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: 'WIP Variance test 1' });

            // Create some other WIP records to test the sum
            await createWipVariance(context, {
                workOrderToUpdate: workOrder,
                amount: 100,
                type: 'workOrderActualCostAdjustmentNonAbsorbed',
            });
            await createWipVariance(context, {
                workOrderToUpdate: workOrder,
                amount: 30,
                type: 'workOrderNegativeActualCostAdjustmentNonAbsorbed',
            });

            const result = await xtremManufacturing.functions.workOrderLib.getCurrentWipVariance(workOrder);
            assert.deepEqual(result, {
                absorbedWipAmount: 493.8,
                nonAbsorbedWipAmount: -70.0,
            });
        }));
});

describe('update of WIP costs', () => {
    let context: any;
    /** Not a real WorkOrder */
    let workOrderToUpdate: any;

    beforeEach(() => {
        context = {
            create: Sinon.stub().resolves({
                $: {
                    save: Sinon.stub().resolves(),
                },
            }),
        };
    });

    /** Used to mock a workOrder */
    function initWorkOrderToUpdate(valuationMethod: string) {
        return {
            productionItems: {
                elementAt: Sinon.stub().resolves(),
            },
            site: Promise.resolve({ legalCompany: { currency: 'USD' } }),
            productionItem: Promise.resolve({
                releasedItem: Promise.resolve({ stockUnit: 'unit' }),
                itemSite: Promise.resolve({ valuationMethod }),
            }),
            wipVariance: Promise.resolve(150),
        };
    }

    it('should create workOrderIndirectCost WIP', async () => {
        const overheadUpdate = 100;
        workOrderToUpdate = initWorkOrderToUpdate('standardCost');

        xtremSystem.TestHelpers.Sinon.registerMocks([
            {
                type: 'stub',
                reference: xtremManufacturing.functions.workOrderLib,
                name: 'updateActualOverhead',
                returns: Promise.resolve(overheadUpdate),
            },
        ]);

        await xtremManufacturing.functions.workOrderLib.updateWipCost(context, workOrderToUpdate);

        Sinon.assert.calledOnce(context.create);
        assert.equal(context.create.getCall(0).args[0], xtremManufacturing.nodes.WorkInProgressCost);
        assert.deepEqual(
            {
                type: context.create.getCall(0).args[1].type as xtremManufacturing.enums.WorkInProgressType,
                amount: Number(context.create.getCall(0).args[1].amount),
            },
            { type: 'workOrderIndirectCost', amount: overheadUpdate },
        );

        xtremSystem.TestHelpers.Sinon.removeMocks();
    });

    async function testUpdateWipCostWithoutOverhead(
        args: {
            stockJournalValue: { movementAmountTotal: decimal; nonAbsorbedTotal: decimal };
            currentWipVariance: { absorbedWipAmount: decimal; nonAbsorbedWipAmount: decimal };
            valuationMethod: string;
        },
        expectedValues: { type: xtremManufacturing.enums.WorkInProgressType; amount: decimal }[],
    ) {
        workOrderToUpdate = initWorkOrderToUpdate(args.valuationMethod);

        xtremSystem.TestHelpers.Sinon.registerMocks([
            {
                type: 'stub',
                reference: xtremManufacturing.functions.workOrderLib,
                name: 'getStockJournalValue',
                returns: Promise.resolve(args.stockJournalValue),
            },
            {
                type: 'stub',
                reference: xtremManufacturing.functions.workOrderLib,
                name: 'getCurrentWipVariance',
                returns: Promise.resolve(args.currentWipVariance),
            },
        ]);

        await xtremManufacturing.functions.workOrderLib.updateVariances(context, workOrderToUpdate);

        Sinon.assert.callCount(context.create, expectedValues.length);

        const unit = (await (await workOrderToUpdate.productionItem).releasedItem).stockUnit;
        const { currency } = (await workOrderToUpdate.site).legalCompany;
        expectedValues.forEach((expectedValue, index) => {
            assert.equal(context.create.getCall(index).args[0], xtremManufacturing.nodes.WorkInProgressCost);
            const callArg = context.create.getCall(index).args[1];
            assert.deepEqual(
                {
                    workOrder: callArg.workOrder,
                    quantity: callArg.quantity,
                    cost: callArg.cost,
                    status: callArg.status,
                    unit: callArg.unit,
                    currency: callArg.currency,
                    type: callArg.type,
                    amount: Number(callArg.amount),
                },
                {
                    workOrder: workOrderToUpdate,
                    quantity: 1,
                    status: 'pending',
                    unit,
                    currency,
                    cost: expectedValue.amount,
                    type: expectedValue.type,
                    amount: expectedValue.amount,
                },
            );
        });
        xtremSystem.TestHelpers.Sinon.removeMocks();
    }

    it('should only create a new WorkInProgressCost with type workOrderNegativeVariance for standardCost', async () => {
        await testUpdateWipCostWithoutOverhead(
            {
                stockJournalValue: { movementAmountTotal: 100, nonAbsorbedTotal: -111 },
                currentWipVariance: { absorbedWipAmount: 100, nonAbsorbedWipAmount: 0 },
                valuationMethod: 'standardCost',
            },
            [{ type: 'workOrderNegativeVariance', amount: 111 }],
        );
    });

    it('should only create a new WorkInProgressCost with type workOrderVariance for standardCost', async () => {
        await testUpdateWipCostWithoutOverhead(
            {
                stockJournalValue: { movementAmountTotal: 200, nonAbsorbedTotal: 222 },
                currentWipVariance: { absorbedWipAmount: 200, nonAbsorbedWipAmount: 0 },
                valuationMethod: 'standardCost',
            },
            [{ type: 'workOrderVariance', amount: 222 }],
        );
    });

    describe('Average unit cost & FIFO', () => {
        // Test for all possible combinations for averageCost and fifo knowing these methods
        // should get the same results
        ['averageCost', 'fifo'].forEach(valuationMethod => {
            it(`should only create a new WorkInProgressCost with type workOrderNegativeActualCostAdjustment for ${valuationMethod}`, async () => {
                await testUpdateWipCostWithoutOverhead(
                    {
                        stockJournalValue: { movementAmountTotal: 100, nonAbsorbedTotal: 0 },
                        currentWipVariance: { absorbedWipAmount: 110, nonAbsorbedWipAmount: 0 },
                        valuationMethod,
                    },
                    [
                        {
                            type: 'workOrderNegativeActualCostAdjustment',
                            amount: 10,
                        },
                    ],
                );
            });

            it(`should only create a new WorkInProgressCost with type workOrderActualCostAdjustment for ${valuationMethod}`, async () => {
                await testUpdateWipCostWithoutOverhead(
                    {
                        stockJournalValue: { movementAmountTotal: 100, nonAbsorbedTotal: 0 },
                        currentWipVariance: { absorbedWipAmount: 89, nonAbsorbedWipAmount: 0 },
                        valuationMethod,
                    },
                    [
                        {
                            type: 'workOrderActualCostAdjustment',
                            amount: 11,
                        },
                    ],
                );
            });

            it(`should only create a new WorkInProgressCost with types workOrderNegativeActualCostAdjustmentNonAbsorbed and workOrderNegativeActualCostAdjustment for ${valuationMethod}`, async () => {
                await testUpdateWipCostWithoutOverhead(
                    {
                        stockJournalValue: { movementAmountTotal: 100, nonAbsorbedTotal: -5 },
                        currentWipVariance: { absorbedWipAmount: 110, nonAbsorbedWipAmount: 0 },
                        valuationMethod,
                    },
                    [
                        {
                            type: 'workOrderNegativeActualCostAdjustmentNonAbsorbed' as xtremManufacturing.enums.WorkInProgressType,
                            amount: 5,
                        },
                        {
                            type: 'workOrderNegativeActualCostAdjustment' as xtremManufacturing.enums.WorkInProgressType,
                            amount: 10,
                        },
                    ],
                );
            });

            it(`should only create a new WorkInProgressCost with types workOrderNegativeActualCostAdjustmentNonAbsorbed and workOrderActualCostAdjustment for ${valuationMethod}`, async () => {
                await testUpdateWipCostWithoutOverhead(
                    {
                        stockJournalValue: { movementAmountTotal: 100, nonAbsorbedTotal: -6 },
                        currentWipVariance: { absorbedWipAmount: 89, nonAbsorbedWipAmount: 0 },
                        valuationMethod,
                    },
                    [
                        {
                            type: 'workOrderNegativeActualCostAdjustmentNonAbsorbed' as xtremManufacturing.enums.WorkInProgressType,
                            amount: 6,
                        },
                        {
                            type: 'workOrderActualCostAdjustment',
                            amount: 11,
                        },
                    ],
                );
            });

            it(`should only create a new WorkInProgressCost with types workOrderActualCostAdjustmentNonAbsorbed and workOrderNegativeActualCostAdjustment for ${valuationMethod}`, async () => {
                await testUpdateWipCostWithoutOverhead(
                    {
                        stockJournalValue: { movementAmountTotal: 100, nonAbsorbedTotal: 7 },
                        currentWipVariance: { absorbedWipAmount: 110, nonAbsorbedWipAmount: 0 },
                        valuationMethod,
                    },
                    [
                        {
                            type: 'workOrderActualCostAdjustmentNonAbsorbed' as xtremManufacturing.enums.WorkInProgressType,
                            amount: 7,
                        },
                        {
                            type: 'workOrderNegativeActualCostAdjustment' as xtremManufacturing.enums.WorkInProgressType,
                            amount: 10,
                        },
                    ],
                );
            });

            it(`should only create a new WorkInProgressCost with types workOrderActualCostAdjustmentNonAbsorbed and workOrderActualCostAdjustment for ${valuationMethod}`, async () => {
                await testUpdateWipCostWithoutOverhead(
                    {
                        stockJournalValue: { movementAmountTotal: 100, nonAbsorbedTotal: 8 },
                        currentWipVariance: { absorbedWipAmount: 89, nonAbsorbedWipAmount: 0 },
                        valuationMethod,
                    },
                    [
                        {
                            type: 'workOrderActualCostAdjustmentNonAbsorbed' as xtremManufacturing.enums.WorkInProgressType,
                            amount: 8,
                        },
                        {
                            type: 'workOrderActualCostAdjustment',
                            amount: 11,
                        },
                    ],
                );
            });
        });
    });
});
