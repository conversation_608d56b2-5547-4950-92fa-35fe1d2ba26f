// import { Test } from '@sage/xtrem-core';
// import { assert } from 'chai';
// import * as xtremManufacturing from '../../../lib';
// import { TimeTracking } from '../../../lib/functions/operation-tracking';

// describe('createMultipleOperationalTrackings function', () => {
//     it('Create multiple operational trackings', async () => {
//         const trackings: TimeTracking[] = [
//             {
//                 workOrderOperation: {
//                     workOrder: {
//                         site: { id: 'US003', _id: '#US003' as any },
//                         _id: '#US003|WO2021US0030002' as any,
//                         number: 'WO2021US0030002',
//                     },
//                     operationNumber: 15,
//                     name: 'assembling',
//                 },
//                 expectedResource: {
//                     description: 'Automatic Filling Machine 100L',
//                     _id: '#AFILM100L|US003' as any,
//                     // code: 'AFILM100L',
//                 },
//                 actualQuantity: 2,
//                 setupTimeUnit: { id: 'SECOND' },
//                 actualSetupTime: 2,
//                 runTimeUnit: { id: 'SECOND' },
//                 actualRunTime: 2,
//             },
//             {
//                 workOrderOperation: {
//                     workOrder: {
//                         site: { id: 'US003', _id: '#US003' as any },
//                         _id: '#US003|WO2021US0030002' as any,
//                         number: 'WO2021US0030002',
//                     },
//                     operationNumber: 20,
//                     name: 'painting',
//                 },
//                 expectedResource: {
//                     description: 'Thomas Campbell',
//                     _id: '#Thomas Campbell|US003' as any,
//                     // code: 'Thomas Campbell',
//                 },
//                 actualQuantity: 1,
//                 setupTimeUnit: { id: 'SECOND' },
//                 actualSetupTime: 1,
//                 runTimeUnit: { id: 'SECOND' },
//                 actualRunTime: 1,
//             },
//         ];
//         await Test.withContext(
//             async context => {
//                 const numberOfTrackingCreated =
//                     await xtremManufacturing.nodes.OperationTracking.createMultipleOperationalTrackings(
//                         context,
//                         JSON.stringify(trackings),
//                     );
//                 assert.equal(numberOfTrackingCreated, 2);
//             },
//             { today: '2020-05-09' },
//         );
//     });
// });

// describe('createMultipleOperationalTrackings function', () => {
//     it('Create new operation/work order operation/trackings', () =>
//         Test.withContext(
//             async context => {
//                 const tracking: TimeTracking[] = [
//                     {
//                         workOrderOperation: {
//                             workOrder: {
//                                 name: 'Bottle 100 ml Hydro-alcoholic gel for hand antisepsis',
//                                 number: 'WO2021US0030002',
//                                 _id: '#US003|WO2021US0030002' as any,
//                                 site: { id: 'US003', _id: '#US003' as any },
//                             },
//                             minCapabilityLevel: { _id: '#Master' as any },
//                             name: 'Bottle filling',
//                         },
//                         expectedResource: {
//                             description: 'Automatic Filling Machine 100L',
//                             _id: '#AFILM100L|US003' as any,
//                             // code: 'AFILM100L',
//                         },
//                         actualQuantity: 3,
//                         actualSetupTime: 3,
//                         actualRunTime: 3,

//                         setupTimeUnit: { id: 'SECOND' },
//                         runTimeUnit: { id: 'SECOND' },
//                     },
//                 ];
//                 const result = await xtremManufacturing.nodes.OperationTracking.createMultipleOperationalTrackings(
//                     context,
//                     JSON.stringify(tracking),
//                 );
//                 assert.equal(result, 1);
//             },
//             { today: '2020-05-09' },
//         ));
//     it('Create new operation/work order operation/trackings with unexpected resource', async () => {
//         const tracking: TimeTracking = {
//             workOrderOperation: {
//                 workOrder: {
//                     name: 'Bottle 100 ml Hydro-alcoholic gel for hand antisepsis',
//                     number: 'WO2021US0030002',
//                     _id: '#US003|WO2021US0030002' as any,
//                     site: { id: 'US003', _id: '#US003' as any },
//                 },
//                 operationNumber: 10,
//             },
//             expectedResource: {
//                 description: 'Automatic Filling Machine 100L',
//                 _id: '#AFILM100L|US003' as any,
//                 // code: 'AFILM100L',
//             },
//             actualQuantity: 3,
//             setupTimeUnit: { id: 'SECOND' },
//             runTimeUnit: { id: 'SECOND' },
//         };

//         await Test.withContext(
//             async context => {
//                 const numberOfTrackingCreated =
//                     await xtremManufacturing.nodes.OperationTracking.createMultipleOperationalTrackings(
//                         context,
//                         JSON.stringify([tracking]),
//                     );
//                 assert.equal(numberOfTrackingCreated, 1);
//             },
//             { today: '2020-05-09' },
//         );
//     });
// });
