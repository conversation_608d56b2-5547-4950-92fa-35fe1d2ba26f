import { Test, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib';
import { WorkInProgressInputSet } from '../../../lib/nodes';

const userEmail = '<EMAIL>';
const siteId = 'US001'; /** Chem. Austin */
const workOrderKey = `#${siteId}|WO2024US0010006`;
const itemId = 'STOSTD-MANUF';

describe('Test work in progress input settings', () => {
    it('Create work in progress input settings before running the selection', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { _id: siteId });
                const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    _id: workOrder<PERSON>ey,
                });

                // Create work in progress input settings
                let inputSet = await context.create(xtremManufacturing.nodes.WorkInProgressInputSet, {});

                assert.deepEqual(await (await inputSet.user).email, userEmail);
                assert.deepEqual(await inputSet.asOfDate, date.today());
                assert.deepEqual(await inputSet.workInProgressTotalAmount, 0);
                assert.deepEqual(await inputSet.status, 'draft');
                await assert.isRejected(inputSet.$.save(), 'The record was not created.');
                await inputSet.$.set({ site });
                await inputSet.$.save();
                await inputSet.$.set({ fromWorkOrder: workOrder, toWorkOrder: workOrder, status: 'inProgress' });
                await inputSet.$.save();

                // Read work in progress input settings and compare with input.
                inputSet = await context.read(xtremManufacturing.nodes.WorkInProgressInputSet, {
                    _id: inputSet._id,
                });

                assert.deepEqual(await inputSet.asOfDate, date.today());
                assert.deepEqual(await inputSet.workInProgressTotalAmount, 0);
                assert.deepEqual(await inputSet.status, 'inProgress');
                assert.deepEqual(await (await inputSet.currency).id, 'USD');
            },
            { today: '2024-12-20', user: { email: userEmail } },
        ));

    it('Update work in progress input settings with lines', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { _id: siteId });
                const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    _id: workOrderKey,
                });

                // Create work in progress input setting with line.
                let inputSet = await context.create(xtremManufacturing.nodes.WorkInProgressInputSet, {
                    site,
                    status: 'completed',
                    lines: [
                        {
                            workOrder,
                            plannedMaterialCost: 1,
                            actualMaterialCost: 2,
                            plannedProcessCost: 2,
                            actualProcessCost: 4,
                            plannedMachineCost: 3,
                            actualMachineCost: 6,
                            plannedLaborCost: 4,
                            actualLaborCost: 8,
                            plannedToolCost: 0.1,
                            actualToolCost: 0.2,
                        },
                    ],
                });

                await inputSet.$.save();

                // Read work in progress input settings and compare with input.
                inputSet = await context.read(xtremManufacturing.nodes.WorkInProgressInputSet, {
                    _id: inputSet._id,
                });

                // Check correctness.
                assert.deepEqual(await inputSet.lines.length, 1);

                const WorkInProgressResultLine = await inputSet.lines.at(0);

                assert.deepEqual(await (await WorkInProgressResultLine?.workOrder)?.number, await workOrder.number);
                assert.deepEqual(await WorkInProgressResultLine?.status, 'inProgress');

                assert.deepEqual(await WorkInProgressResultLine?.materialCostVariance, 1.0);
                assert.deepEqual(await WorkInProgressResultLine?.materialCostVariancePercentage, 100.0);
                assert.deepEqual(await WorkInProgressResultLine?.processCostVariance, 12.2);
                assert.deepEqual(await WorkInProgressResultLine?.processCostVariancePercentage, 610.0);
                assert.deepEqual(await WorkInProgressResultLine?.machineCostVariance, 3.0);
                assert.deepEqual(await WorkInProgressResultLine?.machineCostVariancePercentage, 100.0);
                assert.deepEqual(await WorkInProgressResultLine?.laborCostVariance, 4.0);
                assert.deepEqual(await WorkInProgressResultLine?.laborCostVariancePercentage, 100.0);
                assert.deepEqual(await WorkInProgressResultLine?.toolCostVariance, 0.1);
                assert.deepEqual(await WorkInProgressResultLine?.toolCostVariancePercentage, 100.0);
                assert.deepEqual(await WorkInProgressResultLine?.totalPlannedCost, 3.0);
                assert.deepEqual(await WorkInProgressResultLine?.totalActualCost, 16.2);
                assert.deepEqual(await WorkInProgressResultLine?.totalCostVariance, 13.2);
                assert.deepEqual(await WorkInProgressResultLine?.totalCostVariancePercentage, 440.0);
                assert.deepEqual(await WorkInProgressResultLine?.positiveClosingVariance, 0.0);
                assert.deepEqual(await WorkInProgressResultLine?.negativeClosingVariance, 0.0);
                assert.deepEqual(await WorkInProgressResultLine?.closingVariance, 0.0);
                assert.deepEqual(await WorkInProgressResultLine?.workInProgressTotal, 16.2);

                // Note: The linter does not want the await. But mocha recognizes "line?.is..."" as promises.
                // eslint-disable-next-line @typescript-eslint/await-thenable
                assert.deepEqual(await WorkInProgressResultLine?.isStockJournalAvailable, false);
                // eslint-disable-next-line @typescript-eslint/await-thenable
                assert.deepEqual(await WorkInProgressResultLine?.isJournalEntryAvailable, false);
            },
            { today: '2024-12-20', user: { email: userEmail } },
        ));

    it('Test data selection - functionality of async operation wipInquiry()', () =>
        Test.withContext(
            async context => {
                const user = await context.read(xtremSystem.nodes.User, { email: userEmail });
                const userId = user._id.toString();
                const site = await context.read(xtremSystem.nodes.Site, { _id: siteId });
                const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    _id: workOrderKey,
                });
                const item = await context.read(xtremMasterData.nodes.Item, {
                    id: itemId,
                });

                // == Fill a work in progress input setting and run selection. ==
                let inputSet = await context.create(xtremManufacturing.nodes.WorkInProgressInputSet, { site });
                await inputSet.$.save();

                // Run selection.
                assert.isTrue(await WorkInProgressInputSet.wipInquiry(context, userId));

                // Check the result.
                inputSet = await context.read(xtremManufacturing.nodes.WorkInProgressInputSet, {
                    _id: inputSet._id,
                });
                assert.deepEqual(await inputSet.lines.length, 13);

                // == Narrow the selection down to a specific work order and deep check the result. ==
                await inputSet.$.set({ fromWorkOrder: workOrder, toWorkOrder: workOrder });
                await inputSet.$.save();

                // Run selection.
                assert.isTrue(await WorkInProgressInputSet.wipInquiry(context, userId));

                // Check the result.
                inputSet = await context.read(xtremManufacturing.nodes.WorkInProgressInputSet, {
                    _id: inputSet._id,
                });
                assert.deepEqual(await inputSet.status, 'completed');
                assert.deepEqual(await inputSet.lines.length, 1);

                // WorkInProgressResultLine
                const line = await inputSet.lines.at(0);

                assert.deepEqual(await (await line?.workOrder)?.number, await workOrder.number);
                assert.deepEqual(await (await line?.item)?.releasedItem, item);
                assert.deepEqual(await line?.status, 'inProgress');
                assert.deepEqual(await line?.workInProgressTotal, 1863.6337);
                assert.deepEqual(await line?.plannedMaterialCost, 7152.372);
                assert.deepEqual(await line?.actualMaterialCost, 1572.11);
                assert.deepEqual(await line?.materialCostVariance, -5580.262);
                assert.deepEqual((await line?.materialCostVariancePercentage)?.toFixed(2), '-78.02');

                assert.deepEqual(await line?.plannedProcessCost, 2048.8876);
                assert.deepEqual(await line?.actualProcessCost, 661.8737);
                assert.deepEqual(await line?.processCostVariance, -1387.0139);
                assert.deepEqual((await line?.processCostVariancePercentage)?.toFixed(2), '-67.70');

                assert.deepEqual(await line?.plannedMachineCost, 25.96);
                assert.deepEqual(await line?.actualMachineCost, 8.576);
                assert.deepEqual(await line?.machineCostVariance, -17.384);
                assert.deepEqual((await line?.machineCostVariancePercentage)?.toFixed(2), '-66.96');

                assert.deepEqual(await line?.plannedLaborCost, 2010.0);
                assert.deepEqual(await line?.actualLaborCost, 647.6);
                assert.deepEqual(await line?.laborCostVariance, -1362.4);
                assert.deepEqual((await line?.laborCostVariancePercentage)?.toFixed(2), '-67.78');

                assert.deepEqual(await line?.plannedToolCost, 12.9276);
                assert.deepEqual(await line?.actualToolCost, 5.6977);
                assert.deepEqual(await line?.toolCostVariance, -7.2299);
                assert.deepEqual((await line?.toolCostVariancePercentage)?.toFixed(2), '-55.93');

                assert.deepEqual(await line?.totalPlannedCost, 9201.2596);
                assert.deepEqual(await line?.totalActualCost, 2233.9837);
                assert.deepEqual(await line?.totalCostVariance, -6967.2759);
                assert.deepEqual((await line?.totalCostVariancePercentage)?.toFixed(2), '-75.72');

                assert.deepEqual(await line?.positiveClosingVariance, 0.0);
                assert.deepEqual(await line?.negativeClosingVariance, 0.0);
                assert.deepEqual(await line?.closingVariance, 0.0);

                // Note: The linter does not want the await. But mocha recognises line?.is... as promises.
                // eslint-disable-next-line @typescript-eslint/await-thenable
                assert.deepEqual(await line?.isStockJournalAvailable, true);
                // eslint-disable-next-line @typescript-eslint/await-thenable
                assert.deepEqual(await line?.isJournalEntryAvailable, false);
            },
            { today: '2025-01-10', user: { email: userEmail } },
        ));
});
