import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremManufacturing from '../../../lib';

describe('OperationTracking  Standard create ', () => {
    it('Create OperationTracking with a labor resource ', () =>
        Test.withContext(async context => {
            const wosFirm = await context.read(xtremManufacturing.nodes.WorkOrder, { _id: '#US003|WOS-FIRM' });
            const workOrderOperation = await wosFirm.productionOperations.elementAt(0);
            const operator = await context.read(xtremMasterData.nodes.LaborResource, { _id: '#Thomas <PERSON>|US003' });

            const newTracking = await context.create(xtremManufacturing.nodes.OperationTracking, {
                workOrder: wosFirm,
                lines: [
                    {
                        workOrderOperation,
                        line: 1,
                        completedQuantity: 1,
                        actualResource: operator,
                    },
                ],
            });

            await newTracking.$.save();
        }));
    it('Create OperationTracking with a detail resource ', () =>
        Test.withContext(async context => {
            const wosFirm = await context.read(xtremManufacturing.nodes.WorkOrder, { _id: '#US003|WO2021US0030001' });
            const workOrderOperation = await wosFirm.productionOperations.find(
                async operation => (await operation.operationNumber) === 20,
            );
            const operator = await context.read(xtremMasterData.nodes.DetailedResource, { _id: '#MLS-C912|US003' });

            const newTracking = await context.create(xtremManufacturing.nodes.OperationTracking, {
                number: 'TRK1',
                workOrder: wosFirm,
                lines: [
                    {
                        workOrderOperation,
                        line: 1,
                        actualRunTime: 1,
                        completedQuantity: 1,
                        actualResource: operator,
                    },
                ],
            });

            await newTracking.$.save();
        }));

    it('should correctly set runTimeUnit from tracking data instead of setupTimeUnit', () =>
        Test.withContext(async context => {
            // Create test data with different setup and run time units
            const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, {
                _id: '#US003|WO2021US0030001',
            });

            const setupTimeUnit = await context.read(xtremMasterData.nodes.UnitOfMeasure, {
                _id: '#MINUTE',
            });

            const runTimeUnit = await context.read(xtremMasterData.nodes.UnitOfMeasure, {
                _id: '#HOUR',
            });

            const expectedResource = await context.read(xtremMasterData.nodes.DetailedResource, {
                _id: '#AFILM100L|US003',
            });

            const minCapabilityLevel = await context.read(xtremMasterData.nodes.CapabilityLevel, {
                _id: '#Master',
            });

            // Create tracking data with different setup and run time units
            const tracking: xtremManufacturing.functions.operationTracking.TimeTracking = {
                workOrderOperation: {
                    workOrder,
                    name: 'Test operation with different time units',
                    remainingQuantity: 10,
                    minCapabilityLevel,
                },
                expectedResource,
                actualQuantity: 5,
                setupTimeUnit: Promise.resolve(setupTimeUnit),
                runTimeUnit: Promise.resolve(runTimeUnit), // This should be different from setupTimeUnit
                actualSetupTime: Promise.resolve(15),
                actualRunTime: Promise.resolve(2.5),
                status: Promise.resolve('pending'),
                isCompleted: false,
            };

            // Call the public function that internally calls addOperationToWorkOrder
            const trackingIds =
                await xtremManufacturing.functions.operationTracking.createMultipleOperationalTrackingsFromTimeTracking(
                    context,
                    [tracking],
                );

            // Verify that tracking was created
            assert.equal(trackingIds.length, 1);

            // Get the created operation tracking
            const operationTracking = await context.read(xtremManufacturing.nodes.OperationTracking, {
                _id: trackingIds[0],
            });

            // Verify that the operation was created with correct time units
            assert.equal(await operationTracking.lines.length, 1);
            const trackingLine = await operationTracking.lines.elementAt(0);

            // Verify that setupTimeUnit and runTimeUnit are correctly set from tracking data
            const trackingLineSetupTimeUnit = await trackingLine.setupTimeUnit;
            const trackingLineRunTimeUnit = await trackingLine.runTimeUnit;

            assert.equal(
                trackingLineSetupTimeUnit._id,
                setupTimeUnit._id,
                'Setup time unit should match tracking data',
            );
            assert.equal(trackingLineRunTimeUnit._id, runTimeUnit._id, 'Run time unit should match tracking data');
            assert.notEqual(
                trackingLineSetupTimeUnit._id,
                trackingLineRunTimeUnit._id,
                'Setup and run time units should be different',
            );

            // Verify that the operation was created with correct time units
            const workOrderOperation = await trackingLine.workOrderOperation;
            const operationSetupTimeUnit = await workOrderOperation.setupTimeUnit;
            const operationRunTimeUnit = await workOrderOperation.runTimeUnit;

            assert.equal(
                operationSetupTimeUnit._id,
                setupTimeUnit._id,
                'Operation setup time unit should match tracking data',
            );
            assert.equal(
                operationRunTimeUnit._id,
                runTimeUnit._id,
                'Operation run time unit should match tracking data',
            );
            assert.notEqual(
                operationSetupTimeUnit._id,
                operationRunTimeUnit._id,
                'Operation setup and run time units should be different',
            );
        }));
});
