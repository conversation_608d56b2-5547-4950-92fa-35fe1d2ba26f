import { Test, date } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib';

describe('createMultipleOperationalTrackings function', () => {
    it('Create new operation/work order operation/tracking with operation', () =>
        Test.withContext(async context => {
            const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { _id: '#US003|WO2021US0030002' });
            const tracking = {
                workOrder,
                trackingNumber: 'TRK1',
                operationNumber: 10,
                quantity: 3,
                date: date.parse('2020-05-09'),
            };
            const trackingOperation = await workOrder.productionOperations.find(
                async operation => (await operation.operationNumber) === 10,
            );
            if (!trackingOperation) {
                throw new Error('productionOperation not find for operation number 10');
            }
            assert.equal(await trackingOperation.plannedQuantity, 100);

            assert.equal(await trackingOperation.resources.length, 2);

            const resource1 = await trackingOperation.resources.elementAt(0);
            const resource2 = await trackingOperation.resources.elementAt(1);

            assert.equal(await resource1.expectedRunTime, 60);
            assert.equal(await resource2.expectedRunTime, 60);

            const operationtracking = await xtremManufacturing.functions.operationTracking.createOperationTrackings(
                context,
                tracking,
            );
            if (!operationtracking) {
                throw new Error('Operation tracking not created');
            }

            assert.equal(await operationtracking.lines.length, 2);

            const trackingLine1 = await operationtracking.lines.elementAt(0);
            const trackingLine2 = await operationtracking.lines.elementAt(1);

            assert.equal(await operationtracking.number, 'TRK1');
            assert.equal(await trackingLine1.workInProgressActualResourceType, 'machine');

            assert.equal(await trackingLine1.completedQuantity, 3);
            assert.equal(await trackingLine2.completedQuantity, 0);
            // 2 because 1.8 round to up ( 3*60 / 100 = 1.8)
            assert.equal(await trackingLine2.actualRunTime, 2);
            assert.equal(await trackingLine1.actualRunTime, 2);
        }));

    it('Create new operation/work order operation/tracking with operation with resource that does not belong to resource group', () =>
        Test.withContext(async context => {
            const workOrder = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { _id: '#US001|WOS-SM-FIRM' },
                { forUpdate: true },
            );
            assert.equal(await workOrder.productionOperations.length, 1);
            const operation = await workOrder.productionOperations.elementAt(0);
            assert.equal(await operation.operationNumber, 10);
            await xtremManufacturing.nodes.WorkOrder.skipSchedulingWorkOrder(context, workOrder);
            const operationtracking = await xtremManufacturing.nodes.OperationTracking.createOperationTrackings(
                context,
                workOrder,
                'TRK1',
                3,
                undefined,
                10,
            );
            const trackings = await context
                .query(xtremManufacturing.nodes.OperationTrackingLine, {
                    filter: { document: { number: 'TRK1' } },
                })
                .toArray();
            assert.isNotNull(operationtracking);
            assert.equal(trackings.length, 1);
            assert.equal(await (await trackings[0].document).number, 'TRK1');
            assert.equal(await trackings[0].workInProgressActualResourceType, 'labor');
        }));
    it('Create new operation/work order operation/tracking with operation', () =>
        Test.withContext(async context => {
            const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { _id: '#US003|WO2021US0030002' });
            const tracking = { workOrder, trackingNumber: 'TRK1', quantity: 1, date: date.parse('2020-05-09') };

            assert.equal(await workOrder.productionOperations.length, 4);
            assert.equal(
                await workOrder.productionOperations.reduce(async (numberOfResource, operation) => {
                    return numberOfResource + (await operation.resources.length);
                }, 0),
                7,
            );

            const operationtracking = await xtremManufacturing.functions.operationTracking.createOperationTrackings(
                context,
                tracking,
            );
            if (!operationtracking) {
                throw new Error('Operation tracking not created');
            }
            // 7  trackings created
            assert.equal(await operationtracking.lines.length, 7);
            const trackingLinesOperation5 = operationtracking.lines.filter(
                async line => (await (await line.workOrderOperation).operationNumber) === 5,
            );

            const trackingLine1 = await trackingLinesOperation5.elementAt(0);
            const trackingLine2 = await trackingLinesOperation5.elementAt(1);

            assert.equal(await operationtracking.number, 'TRK1');
            assert.equal(await trackingLine1.workInProgressActualResourceType, 'machine');

            assert.equal(await (await trackingLine1.actualResource).id, 'AFILM100L');
            assert.equal(await trackingLine1.completedQuantity, 1);
            assert.equal(await (await trackingLine2.actualResource).id, 'Thomas Campbell');
            assert.equal(await trackingLine2.completedQuantity, 0);
            assert.equal(await trackingLine2.actualRunTime, 1);
            assert.equal(await trackingLine1.actualRunTime, 1);
        }));
});
