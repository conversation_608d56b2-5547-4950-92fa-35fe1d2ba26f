import { Test, date } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib';

describe('WorkOrder node', () => {
    it('Close work order twice', () =>
        Test.withContext(
            async context => {
                const workOrder = await context.read(
                    xtremManufacturing.nodes.WorkOrder,
                    { number: 'WIP Variance test 1' },
                    { forUpdate: true },
                );

                assert.equal(await workOrder.status, 'closed');

                await assert.isRejected(
                    xtremManufacturing.nodes.WorkOrder.closeWorkOrder(context, workOrder, date.today()),
                    'The work order WIP Variance test 1 is already closed.',
                );
            },
            { today: '2022-07-27' },
        ));
    it('Close pending work order', () =>
        Test.withContext(
            async context => {
                const workOrder1 = await context.read(
                    xtremManufacturing.nodes.WorkOrder,
                    { number: 'WOS-SM-FIRM' },
                    { forUpdate: true },
                );

                await assert.isRejected(
                    xtremManufacturing.nodes.WorkOrder.closeWorkOrder(context, workOrder1, date.today()),
                    'Try to delete the pending work order WOS-SM-FIRM instead. You cannot close it.',
                );
            },
            { today: '2023-04-18' },
        ));
});
