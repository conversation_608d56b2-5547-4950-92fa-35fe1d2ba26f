import { Test } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib';

describe('Finance posting for manufacturing documents', () => {
    it('Finance posting for a material tracking', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'WT25000003';
                const materialTracking = await context.read(
                    xtremManufacturing.nodes.MaterialTracking,
                    {
                        number: documentNumber,
                    },
                    { forUpdate: true },
                );

                await xtremManufacturing.nodes.MaterialTracking.onceStockCompleted(context, materialTracking);

                const documentFilter = {
                    sourceDocumentNumber: documentNumber,
                    documentType: 'workInProgress',
                    sourceDocumentType: 'materialTracking',
                    targetDocumentType: 'journalEntry',
                };

                const financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                const accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 2);
            },
            {
                today: '2025-03-31',
            },
        ));

    it('Finance posting for a production tracking', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'WT25000002';
                const productionTracking = await context.read(
                    xtremManufacturing.nodes.ProductionTracking,
                    {
                        number: documentNumber,
                    },
                    { forUpdate: true },
                );

                await xtremManufacturing.nodes.ProductionTracking.onceStockCompleted(context, productionTracking);

                const documentFilter = {
                    sourceDocumentNumber: documentNumber,
                    documentType: 'workInProgress',
                    sourceDocumentType: 'productionTracking',
                    targetDocumentType: 'journalEntry',
                };

                const financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                const accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 1);
            },
            {
                today: '2025-03-31',
            },
        ));

    it('Finance posting for a work order close', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'WO250002';
                const workOrder = await context.read(
                    xtremManufacturing.nodes.WorkOrder,
                    {
                        number: documentNumber,
                    },
                    { forUpdate: true },
                );

                await xtremManufacturing.nodes.WorkOrder.onceStockCompleted(context, workOrder);

                const documentFilter = {
                    sourceDocumentNumber: documentNumber,
                    documentType: 'workInProgress',
                    sourceDocumentType: 'workOrderClose',
                    targetDocumentType: 'journalEntry',
                };

                const financeTransactions = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(financeTransactions.length, 1);
                assert.equal(await financeTransactions.at(0)?.message, '');
                assert.equal(await financeTransactions.at(0)?.status, 'pending');

                const accountingStagingRecords = await context
                    .query(xtremFinanceData.nodes.AccountingStaging, { filter: { ...documentFilter } })
                    .toArray();

                assert.equal(accountingStagingRecords.length, 1);
            },
            {
                today: '2025-03-31',
            },
        ));
});

describe('Finance posting resend for manufacturing documents', () => {
    it('Material tracking - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'WT25000001';

                const materialTracking: xtremManufacturing.nodes.MaterialTracking = await context.read(
                    xtremManufacturing.nodes.MaterialTracking,
                    {
                        number: documentNumber,
                    },
                );

                const filter = {
                    sourceDocumentNumber: documentNumber,
                    documentType: 'workInProgress',
                    sourceDocumentType: 'materialTracking',
                    targetDocumentType: 'journalEntry',
                };

                let storedAttributes = await (
                    await context
                        .query(xtremFinanceData.nodes.AccountingStaging, {
                            filter,
                        })
                        .at(0)
                )?.storedAttributes;

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal((storedAttributes as any).item, 'RM_ITEM_002');
                assert.equal(financeTransactionStatus, 'notRecorded');
                assert.notEqual(financeTransactionMessage, '');

                await xtremManufacturing.nodes.MaterialTracking.resendNotificationForFinance(context, materialTracking);

                storedAttributes = await (
                    await context
                        .query(xtremFinanceData.nodes.AccountingStaging, {
                            filter,
                        })
                        .at(0)
                )?.storedAttributes;

                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);

                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal((storedAttributes as any).item, 'RM_ITEM_003');
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            {
                today: '2025-02-13',
            },
        ));

    it('Operation tracking - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'WT25000001';

                const operationTracking: xtremManufacturing.nodes.OperationTracking = await context.read(
                    xtremManufacturing.nodes.OperationTracking,
                    { number: documentNumber },
                );

                const filter = {
                    sourceDocumentNumber: documentNumber,
                    documentType: 'workInProgress',
                    sourceDocumentType: 'operationTracking',
                    targetDocumentType: 'journalEntry',
                };

                let storedAttributes = await (
                    await context
                        .query(xtremFinanceData.nodes.AccountingStaging, {
                            filter,
                        })
                        .at(0)
                )?.storedAttributes;

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal((storedAttributes as any).item, 'RM_ITEM_002');
                assert.equal(financeTransactionStatus, 'notRecorded');
                assert.notEqual(financeTransactionMessage, '');

                await xtremManufacturing.nodes.OperationTracking.resendNotificationForFinance(
                    context,
                    operationTracking,
                );

                storedAttributes = await (
                    await context
                        .query(xtremFinanceData.nodes.AccountingStaging, {
                            filter,
                        })
                        .at(0)
                )?.storedAttributes;

                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);

                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal((storedAttributes as any).item, undefined);
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            { today: '2025-01-31' },
        ));

    it('Production tracking - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'WT25000001';

                const productionTracking: xtremManufacturing.nodes.ProductionTracking = await context.read(
                    xtremManufacturing.nodes.ProductionTracking,
                    {
                        number: documentNumber,
                    },
                );

                const filter = {
                    sourceDocumentNumber: documentNumber,
                    documentType: 'workInProgress',
                    sourceDocumentType: 'productionTracking',
                    targetDocumentType: 'journalEntry',
                };

                let storedAttributes = await (
                    await context
                        .query(xtremFinanceData.nodes.AccountingStaging, {
                            filter,
                        })
                        .at(0)
                )?.storedAttributes;

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal((storedAttributes as any).item, 'RM_ITEM_002');
                assert.equal(financeTransactionStatus, 'notRecorded');
                assert.notEqual(financeTransactionMessage, '');

                await xtremManufacturing.nodes.ProductionTracking.resendNotificationForFinance(
                    context,
                    productionTracking,
                );

                storedAttributes = await (
                    await context
                        .query(xtremFinanceData.nodes.AccountingStaging, {
                            filter,
                        })
                        .at(0)
                )?.storedAttributes;

                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);

                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal((storedAttributes as any).item, 'RM_ITEM_005');
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            {
                today: '2025-02-13',
            },
        ));

    it('Work order close - Successfully resend notification for finance', () =>
        Test.withContext(
            async context => {
                const documentNumber = 'WO250001';

                const workOrder: xtremManufacturing.nodes.WorkOrder = await context.read(
                    xtremManufacturing.nodes.WorkOrder,
                    { number: documentNumber },
                );

                const filter = {
                    sourceDocumentNumber: documentNumber,
                    documentType: 'workInProgress',
                    sourceDocumentType: 'workOrderClose',
                    targetDocumentType: 'journalEntry',
                };

                let storedAttributes = await (
                    await context
                        .query(xtremFinanceData.nodes.AccountingStaging, {
                            filter,
                        })
                        .at(0)
                )?.storedAttributes;

                let financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);
                let financeTransactionStatus = await financeTransactionRecord?.status;
                let financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal((storedAttributes as any).item, 'RM_ITEM_002');
                assert.equal(financeTransactionStatus, 'notRecorded');
                assert.notEqual(financeTransactionMessage, '');

                await xtremManufacturing.nodes.WorkOrder.resendNotificationForFinance(context, workOrder);

                storedAttributes = await (
                    await context
                        .query(xtremFinanceData.nodes.AccountingStaging, {
                            filter,
                        })
                        .at(0)
                )?.storedAttributes;

                financeTransactionRecord = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, { filter })
                    .at(0);

                financeTransactionStatus = await financeTransactionRecord?.status;
                financeTransactionMessage = await financeTransactionRecord?.message;

                assert.equal((storedAttributes as any).item, 'RM_ITEM_005');
                assert.equal(financeTransactionStatus, 'pending');
                assert.equal(financeTransactionMessage, '');
            },
            { today: '2025-01-31' },
        ));
});
