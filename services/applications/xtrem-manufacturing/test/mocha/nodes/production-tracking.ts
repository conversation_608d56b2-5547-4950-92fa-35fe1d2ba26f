import type { Context, LocalizeLocale, NodeCreateData } from '@sage/xtrem-core';
import { Test, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib';

async function createWorkOrderWithCommittedContext(context: Context, workOrderNumber: string) {
    if (await context.tryRead(xtremManufacturing.nodes.WorkOrder, { number: workOrderNumber })) return;

    const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
        id: 'Normal',
    });

    await context.runInIsolatedContext(async (committedContext: Context) => {
        await xtremManufacturing.nodes.WorkOrder.createWorkOrder(committedContext, {
            siteId: 'US003',
            releasedItem: '17890-B',
            releasedQuantity: 100,
            name: 'New work order',
            type: 'firm',
            workOrderNumber,
            startDate: date.today(),
            workOrderCategory,
            bom: '17890-B',
            route: '17890-B',
        });
    });
}

describe('createProductionsTracking function', () => {
    it('Create production trackings with associated material and time tracking', () =>
        Test.withContext(
            async context => {
                const workOrderNumber = 'WOUS003900001';
                const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: workOrderNumber });
                const tracking = await xtremManufacturing.nodes.ProductionTracking.createWorkOrderTracking(context, {
                    itemCode: '17890-B',
                    workOrderNumber,
                    workOrderTrackingNumber: 'WOTUS00320200001',
                    trackingQuantity: 50,
                    trackingDate: date.parse('2020-05-09', context.currentLocale as LocalizeLocale),
                    materialTracking: true,
                    timeTracking: true,
                    stockDetails: [
                        {
                            site: '#US003',
                            item: '#17890-B',
                            stockUnit: await (await (await workOrder.productionItem)!.releasedItem).stockUnit,
                            quantityInStockUnit: 50,
                            status: '#A',
                            location: '11',
                        },
                    ],
                });
                assert.equal(await tracking.workOrder?.number, workOrderNumber);
            },
            { today: '2020-05-09' },
        ));

    // TODO: For the moment we cannot run this test because switching on the serial number service option in the currnct
    //       testing context does not affect the committed context inside createWorkOrderTracking() creating material
    //       trackings. So, while material trackings are created the service option is always seen as deactivated and
    //       and the error to test cannot be provoked.
    it.skip('Creation of production trackings with material tracking for a serial number managed item should fail.', () =>
        Test.withContext(
            async context => {
                assert.deepEqual(
                    await context.isServiceOptionEnabled(xtremMasterData.serviceOptions.serialNumberOption),
                    true,
                );

                const workOrderNumber = 'WOUS003900002';

                // Note: We need to create the work order in a committed context to be sure it can be read later
                //       inside a committedContext.
                await createWorkOrderWithCommittedContext(context, workOrderNumber);

                const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: workOrderNumber });

                const result = await xtremManufacturing.nodes.ProductionTracking.createWorkOrderTracking(context, {
                    itemCode: 'SM2',
                    workOrderNumber,
                    workOrderTrackingNumber: 'WOTUS00320200002',
                    trackingQuantity: 50,
                    trackingDate: date.parse('2020-05-09', context.currentLocale as LocalizeLocale),
                    materialTracking: true,
                    timeTracking: true,
                    stockDetails: [
                        {
                            site: '#US001',
                            item: '#SM2',
                            stockUnit: await (await (await workOrder.productionItem)!.releasedItem).stockUnit,
                            quantityInStockUnit: 50,
                            status: '#A',
                            location: '#LOC3|US001|Loading dock',
                        },
                    ],
                });
                assert.deepEqual(
                    result.message,
                    `No automatic material tracking generated for work order ${workOrderNumber} serialized item SN2 Serial number component.\n`,
                );
            },
            {
                today: '2020-05-09',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption],
            },
        ));

    it('Create production trackings without tracking number or date and with lotId', () =>
        Test.withContext(
            async context => {
                const workOrderNumber = 'WOUS003900001';
                const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: workOrderNumber });
                const existingLot = await context.read(xtremStockData.nodes.Lot, { _id: '#17890-B|10|' });
                const result = await xtremManufacturing.nodes.ProductionTracking.createWorkOrderTracking(context, {
                    itemCode: '17890-B',
                    workOrderNumber,
                    workOrderTrackingNumber: '',
                    trackingQuantity: 1,
                    stockDetails: [
                        {
                            site: '#US003',
                            item: '#17890-B',
                            stockUnit: await (await (await workOrder.productionItem)!.releasedItem).stockUnit,
                            quantityInStockUnit: 1,
                            status: '#A',
                            location: '#LOC1|US003|Production line',
                            existingLot: existingLot._id,
                        },
                    ],
                });
                assert.equal(await result.workOrder?.number, workOrderNumber);
            },
            { today: '2020-05-09' },
        ));

    it('Create multiple production trackings', () =>
        Test.withContext(
            async context => {
                const expectedQuantity = await (
                    await context.read(xtremMasterData.nodes.ItemSite, { item: '#17891', site: '#US003' })
                ).expectedQuantity;

                // WO2021US0030007 with 1000 l to produce
                const workOrder8 = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US003|WO2021US0030007',
                });
                // WO2021US0030008 with 1000 l to produce as well
                const workOrder9 = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US003|WO2021US0030008',
                });

                const trackings = JSON.stringify([
                    {
                        itemCode: await (await (await workOrder8.productionItem)!.releasedItem).id,
                        workOrderNumber: await workOrder8.number,
                        workOrderTrackingNumber: 'TRK8',
                        trackingQuantity: 2,
                        materialTracking: false,
                        timeTracking: false,
                        completed: true,
                        stockDetails: JSON.stringify([
                            {
                                site: '#US003',
                                item: '#17890-B',
                                stockUnit: await (await (await workOrder8.productionItem)!.releasedItem).stockUnit,
                                quantityInStockUnit: 2,
                                status: '#A',
                                location: '#LOC5|US003|Production line',
                                _action: 'create',
                            },
                        ]),
                    },
                    {
                        itemCode: await (await (await workOrder9.productionItem)!.releasedItem).id,
                        workOrderNumber: await workOrder9.number,
                        workOrderTrackingNumber: 'TRK9',
                        trackingQuantity: 20,
                        materialTracking: false,
                        timeTracking: false,
                        completed: false,
                        stockDetails: JSON.stringify([
                            {
                                site: '#US003',
                                item: '#17890-B',
                                stockUnit: (await (await (await workOrder9.productionItem)!.releasedItem).stockUnit)
                                    ._id,
                                quantityInStockUnit: 20,
                                status: '#A1',
                                location: '#LOC1|US003|Production line',
                                _action: 'create',
                            },
                        ]),
                    },
                ]);

                const trackingDate = date.today();
                const result = await xtremManufacturing.nodes.ProductionTracking.createMultipleWorkOrderTrackings(
                    context,
                    trackings,
                    trackingDate,
                );
                assert.equal(result.numberTrackings, 2);

                const tracking8 = await context.tryRead(xtremManufacturing.nodes.ProductionTracking, {
                    number: 'TRK8',
                });
                assert.isNotNull(tracking8);

                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    tracking8._id,
                    xtremManufacturing.nodes.ProductionTracking,
                    'receipt',
                    xtremManufacturing.nodes.ProductionTracking.onStockReply,
                );

                const tracking9 = await context.tryRead(xtremManufacturing.nodes.ProductionTracking, {
                    number: 'TRK9',
                });
                assert.isNotNull(tracking9);

                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    tracking9._id,
                    xtremManufacturing.nodes.ProductionTracking,
                    'receipt',
                    xtremManufacturing.nodes.ProductionTracking.onStockReply,
                );

                const workOrder8Updated = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US003|WO2021US0030007',
                });
                const workOrder9Updated = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US003|WO2021US0030008',
                });

                assert.equal(await (await workOrder8Updated.productionItem)?.lineStatus, 'completed');
                assert.equal(await (await workOrder9Updated.productionItem)?.lineStatus, 'inProgress');

                const newExpectedQuantity = await (
                    await context.read(xtremMasterData.nodes.ItemSite, { item: '#17891', site: '#US003' })
                ).expectedQuantity;

                assert.deepEqual(Number(newExpectedQuantity - expectedQuantity), -22);

                const workOrder9ForUpdate = await context.read(
                    xtremManufacturing.nodes.WorkOrder,
                    { _id: '#US003|WO2021US0030008' },
                    { forUpdate: true },
                );
                await workOrder9ForUpdate.$.set({ endDate: (await workOrder9ForUpdate.endDate).addDays(1) });
                await (await workOrder9ForUpdate.productionItem)!.$.set({ releasedQuantity: 19 });
                await assert.isRejected(workOrder9ForUpdate.$.save());

                const productionItemId = (await workOrder9ForUpdate.productionItem)?._id;
                assert.deepEqual(workOrder9ForUpdate.$.context.diagnoses, [
                    {
                        message: 'The released quantity is less than the completed quantity.',
                        path: ['productionItems', `${productionItemId}`, 'releasedQuantity'],
                        severity: 3,
                    },
                ]);
            },
            { today: '2021-03-23' },
        ));

    it('Create a production tracking with time and material', () =>
        Test.withContext(
            async context => {
                const itemId = 'SM1';
                const siteId = 'US001';
                const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                    id: 'Normal',
                });

                const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId,
                    releasedItem: itemId,
                    releasedQuantity: 10,
                    name: 'New work order',
                    type: 'firm',
                    workOrderNumber: 'WOUS001000001',
                    workOrderCategory,
                    bom: itemId,
                    route: itemId,
                });
                const releasedItem = await (await workOrder.productionItem)?.releasedItem;
                assert.isDefined(releasedItem);
                const trackings = JSON.stringify([
                    {
                        itemCode: itemId,
                        workOrderNumber: await workOrder.number,
                        workOrderTrackingNumber: null,
                        trackingQuantity: 10,
                        materialTracking: true,
                        timeTracking: true,
                        completed: false,
                        stockDetails: JSON.stringify([
                            {
                                site: `#${siteId}`,
                                item: `#${itemId}`,
                                status: '#A',
                                location: '#LOC1|US001|Loading dock',
                                stockUnit: (await releasedItem.stockUnit)._id,
                                owner: '',
                                stockDetailQuantity: 2,
                                stockDetailQuantityInStockUnit: 2,
                            },
                        ]),
                    },
                ]);

                const trackingDate = date.today();

                const result = await xtremManufacturing.nodes.ProductionTracking.createMultipleWorkOrderTrackings(
                    context,
                    trackings,
                    trackingDate,
                );

                const workOrderUpdated = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    number: 'WOUS001000001',
                });

                assert.equal(await workOrderUpdated.productionTrackings.length, 1);
                assert.equal(await workOrderUpdated.materialTrackings.length, 1);
                assert.equal(await workOrderUpdated.timeTrackings.length, 1);
                assert.equal(result.numberTrackings, 1);
                assert.equal(await workOrderUpdated.status, 'inProgress');

                const productionTrackingId = (await workOrderUpdated.productionTrackings.elementAt(0))._id;
                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    productionTrackingId,
                    xtremManufacturing.nodes.ProductionTracking,
                    'receipt',
                    xtremManufacturing.nodes.ProductionTracking.onStockReply,
                );

                const productionTracking = await context.read(xtremManufacturing.nodes.ProductionTracking, {
                    _id: productionTrackingId,
                });
                assert.equal(await productionTracking.stockTransactionStatus, 'completed');
            },
            { today: '2021-03-23' },
        ));

    it('Create production trackings with dimensions and attributes', () =>
        Test.withContext(
            async context => {
                const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US003|WO2021US0030010',
                });
                assert.equal(await workOrder.productionTrackings.length, 0);
                assert.equal(await workOrder.materialTrackings.length, 1);
                const materialTrackingLines = (await workOrder.materialTrackings.elementAt(0)).lines;
                assert.equal(await materialTrackingLines.length, 3);
                assert.sameMembers(
                    await materialTrackingLines
                        .map(async materialTrackingLine => (await materialTrackingLine.item)?.id)
                        .toArray(),
                    ['3426', '7047', '5467'],
                );

                const releasedItem = await (await workOrder.productionItem)?.releasedItem;
                assert.isDefined(releasedItem);
                const trackings = JSON.stringify([
                    {
                        itemCode: await releasedItem.id,
                        workOrderNumber: await workOrder.number,
                        workOrderTrackingNumber: 'TRK8',
                        trackingQuantity: 2,
                        materialTracking: true,
                        timeTracking: false,
                        completed: false,
                        stockDetails: JSON.stringify([
                            {
                                site: '#US003',
                                item: '#17890-B',
                                stockUnit: await releasedItem.stockUnit,
                                quantityInStockUnit: 2,
                                status: '#A',
                                location: '#LOC1|US003|Production line',
                                _action: 'create',
                            } as NodeCreateData<xtremStockData.nodes.StockReceiptDetail>,
                        ]),
                        storedDimensions: JSON.stringify({ dimensionType03: '100' }),
                        storedAttributes: JSON.stringify({ project: 'AttPROJ2' }),
                    },
                ]);

                const trackingDate = date.today();
                const result = await xtremManufacturing.nodes.ProductionTracking.createMultipleWorkOrderTrackings(
                    context,
                    trackings,
                    trackingDate,
                );
                assert.equal(result.numberTrackings, 1);

                const productionTracking = await context.read(xtremManufacturing.nodes.ProductionTracking, {
                    number: 'TRK8',
                });
                const materialTracking = await context.read(xtremManufacturing.nodes.MaterialTracking, {
                    number: 'TRK8',
                });

                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    productionTracking._id,
                    xtremManufacturing.nodes.ProductionTracking,
                    'receipt',
                    xtremManufacturing.nodes.ProductionTracking.onStockReply,
                );

                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    materialTracking._id,
                    xtremManufacturing.nodes.MaterialTracking,
                    'issue',
                    xtremManufacturing.nodes.MaterialTracking.onStockReply,
                    { stockUpdateResultStatus: 'decreased' },
                );

                const workOrderUpdated = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US003|WO2021US0030010',
                });
                // only 2/1000 tracked => inProgress
                assert.equal(await (await workOrderUpdated.productionItem)?.lineStatus, 'inProgress');
                assert.equal(await workOrderUpdated.productionTrackings.length, 1);
                assert.equal(await workOrderUpdated.materialTrackings.length, 2);

                const productionTrackingLines = (await workOrderUpdated.productionTrackings.elementAt(0)).lines;
                assert.equal(await productionTrackingLines.length, 1);
                const productionTrackingLine = await productionTrackingLines.elementAt(0);
                assert.equal(await productionTrackingLine.quantityInStockUnit, 2);
                assert.equal(await productionTrackingLine.stockTransactionStatus, 'completed');
                assert.deepEqual(await productionTrackingLine.storedAttributes, { project: 'AttPROJ2' } as any);
                assert.deepEqual(await productionTrackingLine.storedDimensions, { dimensionType03: '100' });

                const newMaterialTrackingLines = (
                    await workOrderUpdated.materialTrackings.find(async mt => (await mt.number) === 'TRK8')
                )?.lines;
                assert.isDefined(newMaterialTrackingLines);
                assert.equal(await newMaterialTrackingLines.length, 4);

                type LineResult = {
                    componentNumber: Awaited<
                        Awaited<xtremManufacturing.nodes.MaterialTrackingLine['workOrderLine']>['componentNumber']
                    >;
                    stockTransactionStatus: Awaited<
                        xtremManufacturing.nodes.MaterialTrackingLine['stockTransactionStatus']
                    >;
                    storedAttributes: Awaited<xtremManufacturing.nodes.MaterialTrackingLine['storedAttributes']>;
                    storedDimensions: Awaited<xtremManufacturing.nodes.MaterialTrackingLine['storedDimensions']>;
                };
                const actualLines: LineResult[] = await newMaterialTrackingLines
                    .map(async line => ({
                        componentNumber: await (await line.workOrderLine).componentNumber,
                        stockTransactionStatus: await line.stockTransactionStatus,
                        storedAttributes: await line.storedAttributes,
                        storedDimensions: await line.storedDimensions,
                    }))
                    .toArray();

                const expectedLines: LineResult[] = [
                    {
                        componentNumber: 10,
                        stockTransactionStatus: 'completed',
                        storedAttributes: null,
                        storedDimensions: null,
                    },
                    {
                        componentNumber: 20,
                        stockTransactionStatus: 'completed',
                        storedAttributes: { project: 'AttPROJ' } as any,
                        storedDimensions: { dimensionType01: '300' },
                    },
                    {
                        componentNumber: 30,
                        stockTransactionStatus: 'completed',
                        storedAttributes: { project: 'AttPROJ' } as any,
                        storedDimensions: { dimensionType01: '300' },
                    },
                    {
                        componentNumber: 40,
                        stockTransactionStatus: 'completed',
                        storedAttributes: { project: 'AttPROJ' } as any,
                        storedDimensions: { dimensionType01: '300' },
                    },
                ];

                assert.sameDeepMembers(
                    actualLines,
                    expectedLines,
                    `Actual result lines:${JSON.stringify(actualLines, null, 4)}`,
                );
            },
            { today: '2021-03-23' },
        ));

    it('Create production tracking for work order with excluded phantom BOM as component', () =>
        Test.withContext(
            async context => {
                const workOrderNumber = 'WO240003';
                const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: workOrderNumber });
                const tracking = await xtremManufacturing.nodes.ProductionTracking.createWorkOrderTracking(context, {
                    itemCode: 'Chair',
                    workOrderNumber,
                    workOrderTrackingNumber: 'WOTUS00120240001',
                    trackingQuantity: 1,
                    trackingDate: date.parse('2024-08-06', context.currentLocale as LocalizeLocale),
                    materialTracking: true,
                    timeTracking: false,
                    stockDetails: [
                        {
                            site: '#US001',
                            item: '#Chair',
                            stockUnit: await (await (await workOrder.productionItem)!.releasedItem).stockUnit,
                            quantityInStockUnit: 1,
                            status: '#A',
                            location: '#LOC1|US001|Loading dock',
                        },
                    ],
                });
                assert.equal(await tracking.workOrder?.number, workOrderNumber);
            },
            { today: '2024-08-06', testActiveServiceOptions: [xtremMasterData.serviceOptions.phantomItemOption] },
        ));

    it('Create production tracking for work order with included phantom BOM as component', () =>
        Test.withContext(
            async context => {
                const workOrderNumber = 'WO240002';
                const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: workOrderNumber });
                await assert.isRejected(
                    xtremManufacturing.nodes.ProductionTracking.createWorkOrderTracking(context, {
                        itemCode: 'Chair',
                        workOrderNumber,
                        workOrderTrackingNumber: 'WOTUS00120240002',
                        trackingQuantity: 1,
                        trackingDate: date.parse('2024-08-06', context.currentLocale as LocalizeLocale),
                        materialTracking: true,
                        timeTracking: false,
                        stockDetails: [
                            {
                                site: '#US001',
                                item: '#Chair',
                                stockUnit: await (await (await workOrder.productionItem)!.releasedItem).stockUnit,
                                quantityInStockUnit: 1,
                                status: '#A',
                                location: '#LOC1|US001|Loading dock',
                            },
                        ],
                    }),
                    'Phantom components are not allowed in production tracking.',
                );
            },
            { today: '2024-08-06', testActiveServiceOptions: [xtremMasterData.serviceOptions.phantomItemOption] },
        ));
});

describe('production tracking posting to stock', () => {
    it('Check onceStockCompleted is called only when the document is completed', () =>
        Test.withContext(async context => {
            const result = await xtremStockData.functions.testLib.testOnceStockCompleted(context, {
                clas: xtremManufacturing.nodes.ProductionTracking,
                movementType: 'receipt',
                documents: [
                    { key: { number: 'WOTUS00300007' }, isCompleted: false },
                    { key: { number: 'WOTUS00300008' }, isCompleted: true },
                    { key: { number: 'WOTUS00300009' }, isCompleted: false },
                    { key: { number: 'WOTUS00300010' }, isCompleted: true },
                ],
                returnedProperty: 'number',
            });

            assert.deepEqual(result, ['WOTUS00300008', 'WOTUS00300010']);
        }));
});
