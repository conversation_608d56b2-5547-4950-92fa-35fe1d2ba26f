import { asyncArray, date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as Sinon from 'sinon';
import * as xtremManufacturing from '../../../lib';

describe('createMultipleMaterialTracking function', () => {
    it('Create multiple material trackings', () =>
        Test.withContext(
            async context => {
                /** 49 : item LBHYDR100  50 US003
                 *  47 : item BPEC100ML 50 US003 */
                const stockLine = [
                    await context.tryRead(xtremStockData.nodes.Stock, { _id: 49 }),
                    await context.tryRead(xtremStockData.nodes.Stock, { _id: 47 }),
                ];
                assert.isNotNull(stockLine[0]);
                assert.isNotNull(stockLine[1]);

                /**  WO2021US0030001 firm in progress */
                const workOrder1 = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US003|WO2021US0030001',
                });
                /**  WO2021US0030004 firm in progress */
                const workOrder2 = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US003|WO2021US0030004',
                });
                /** item LBHYDR100 */
                const item1 = await context.read(xtremMasterData.nodes.Item, { _id: '#LBHYDR100' });
                /** BPEC100ML */
                const item2 = await context.read(xtremMasterData.nodes.Item, { _id: '#BPEC100ML' });

                /** EACH */ const unit = await context.read(xtremMasterData.nodes.UnitOfMeasure, { id: 'EACH' });

                const trackings: xtremManufacturing.interfaces.MultipleMaterialTracking[] = [
                    {
                        workOrder: workOrder1,
                        item: item1,
                        unit,
                        componentNumber: 40,
                        name: 'Label Bottle Hydro-alcoholic gel for hand antisepsis 100 ml',
                        remainingQuantity: 10,
                        date: date.today(),
                        isCompleted: true,
                        isAutomated: true,
                    },
                    {
                        workOrder: workOrder2,
                        item: item2,
                        name: 'PEC bottle 100 ml',
                        unit,
                        remainingQuantity: 5,
                        isCompleted: true,
                        date: date.today(),
                        isAutomated: true,
                    },
                ];

                const result = await xtremManufacturing.nodes.MaterialTracking.createMultipleMaterialTrackings(
                    context,
                    trackings,
                );
                assert.equal(result.numberTrackings, 2);
            },
            { today: '2020-05-09' },
        ));

    it('Create multiple material trackings -fail ', () =>
        Test.withContext(
            async context => {
                /** 50 :  */
                const stockLine = [await context.tryRead(xtremStockData.nodes.Stock, { _id: 50 })];
                assert.isNotNull(stockLine[0]);
                assert.isNotNull(stockLine[1]);

                /**  WO2021US0030001 firm in progress */
                const workOrder1 = await context.read(xtremManufacturing.nodes.WorkOrder, { _id: '#US003|WOS-FIRM' });
                const workOrder2 = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US003|WOS-PLANNED',
                });
                const workOrder3 = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US001|WOS-SM-FIRM',
                });

                /** item LBHYDR100 */
                const item1 = await context.read(xtremMasterData.nodes.Item, { _id: '#5467' });
                const item3 = await context.read(xtremMasterData.nodes.Item, { _id: '#SC1' });

                /** unit EACH */
                const unit = await context.read(xtremMasterData.nodes.UnitOfMeasure, { id: 'EACH' });

                const trackings: xtremManufacturing.interfaces.MultipleMaterialTracking[] = [
                    {
                        workOrder: workOrder1,
                        item: item1,
                        unit,
                        componentNumber: 40,
                        name: 'Label Bottle Hydro-alcoholic gel for hand antisepsis 100 ml',
                        remainingQuantity: 10,
                        date: date.today(),
                        isCompleted: true,
                    },
                ];

                await assert.isRejected(
                    xtremManufacturing.nodes.MaterialTracking.createMultipleMaterialTrackings(context, trackings),
                    'The record was not created.',
                );
                assert.deepEqual(context.diagnoses, [
                    {
                        path: ['lines', '-1000000003', 'workOrderLine'],
                        severity: 3,
                        message: 'The work order component status must be different from Excluded.',
                    },
                ]);

                trackings[0].workOrder = workOrder2;
                await assert.isRejected(
                    xtremManufacturing.nodes.MaterialTracking.createMultipleMaterialTrackings(context, trackings),
                    'The record was not created.',
                );
                assert.deepEqual(context.diagnoses, [
                    {
                        path: ['workOrder'],
                        severity: 3,
                        message: 'Only firm work orders can be tracked.',
                    },
                ]);

                trackings[0].workOrder = workOrder3;
                trackings[0].item = item3;
                trackings[0].remainingQuantity = 10;
                trackings[0].componentNumber = 10;
                await assert.isRejected(
                    xtremManufacturing.nodes.MaterialTracking.createMultipleMaterialTrackings(context, trackings),
                    'The record was not created.',
                );
                assert.deepEqual(context.diagnoses, [
                    {
                        path: ['lines', '-1000000009', 'workOrderLine'],
                        severity: 3,
                        message:
                            'You can only track the component Simple component after the allocation request is complete.',
                    },
                ]);
            },
            { today: '2020-09-20' },
        ));

    it('Create material trackings', () =>
        Test.withContext(
            async context => {
                const postToStockSpy = Sinon.spy(xtremManufacturing.nodes.MaterialTracking, 'postToStock');
                const reactToStockReplySpy = Sinon.spy(
                    xtremStockData.functions.notificationLib,
                    'reactToStockMovementReply',
                );

                /** Item site 17890 (Hydro-alcoholic gel for hand antisepsis) on site US003  */
                const itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item: '#17890', site: '#US003' });
                const requiredQuantity = await itemSite.requiredQuantity;

                /** WO2021US0030002 firm & pending - Bottle 100 ml Hydro-alcoholic gel for hand antisepsis */
                const workOrder = await context.tryRead(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US003|WO2021US0030002',
                });

                assert.isNotNull(workOrder);

                const result = await xtremManufacturing.nodes.MaterialTracking.createMaterialTrackings(
                    context,
                    workOrder,
                    'WOTUS00320200001',
                    50,
                    undefined,
                    undefined,
                    true,
                );

                assert.isTrue(postToStockSpy.calledOnce, 'MaterialTracking.postToStock was not called');
                const postToStockResult = await postToStockSpy.returnValues[0];
                assert.isNotEmpty(postToStockResult);

                assert.deepEqual(JSON.parse(postToStockResult).result, 'requested');

                const trackings = await context
                    .query(xtremManufacturing.nodes.MaterialTracking, {
                        filter: { number: 'WOTUS00320200001' },
                    })
                    .toArray();
                assert.isNotNull(result);
                assert.equal(await trackings[0].lines.length, 5);
                assert.equal(await trackings[0].number, 'WOTUS00320200001');

                const trackingLineIds = await trackings[0].lines.map(line => line._id).toArray();
                assert.deepEqual(trackingLineIds.length, 5);

                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    trackings[0]._id,
                    xtremManufacturing.nodes.MaterialTracking,
                    'issue',
                    xtremManufacturing.nodes.MaterialTracking.onStockReply,
                    { stockUpdateResultStatus: 'decreased' },
                );

                assert.isTrue(reactToStockReplySpy.calledOnce, 'MaterialTracking.onStockReply was not called');
                const reactToStockReplyResult = await reactToStockReplySpy.returnValues[0];

                assert.isDefined(reactToStockReplyResult.issue);
                assert.isFalse(reactToStockReplyResult.issue.transactionHadAnError);
                assert.deepEqual(Object.keys(reactToStockReplyResult).length, 1);
                assert.strictEqual(await reactToStockReplyResult.issue.documents.length, 1);
                const document = await reactToStockReplyResult.issue.documents.elementAt(0);
                assert.strictEqual(document.id, trackings[0]._id);
                assert.isTrue(document.isStockDocumentCompleted);
                assert.strictEqual(await document.linesInError.length, 0);
                assert.deepEqual(await document.restOfTheLines.map(line => line.id).toArray(), trackingLineIds);

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                const newRequiredQuantity = await (
                    await context.read(xtremMasterData.nodes.ItemSite, { item: '#17890', site: '#US003' })
                ).requiredQuantity;

                assert.equal(
                    Number(newRequiredQuantity - requiredQuantity),
                    -5.5,
                    `newRequiredQuantity=${newRequiredQuantity} requiredQuantity=${requiredQuantity}`,
                );

                postToStockSpy.restore();
                reactToStockReplySpy.restore();
            },
            { today: '2020-05-09' },
        ));

    it('Create material trackings - quantity to produce without stock', () =>
        Test.withContext(
            async context => {
                const workOrder = await context.tryRead(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US003|WO2021US0030002',
                });
                assert.isNotNull(workOrder);

                await assert.isRejected(
                    xtremManufacturing.nodes.MaterialTracking.createMaterialTrackings(
                        context,
                        workOrder!,
                        'WOTUS00320200001',
                        1,
                        undefined,
                        undefined,
                        true,
                    ),
                    'No stock available on Site: US003 Item: 17890.\nThe expiration date should be greater than or equal to 2022-03-11.',
                );
            },
            { today: '2022-03-11' },
        ));

    // FIXME: refine this test as it does not do what it claims to do.
    it.skip('Create material trackings - quantity to produce incompatible with stock unit for material to consume', () =>
        Test.withContext(
            async context => {
                const workOrder = await context.tryRead(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US003|WO2021US0030002',
                });
                assert.isNotNull(workOrder);

                await assert.isRejected(
                    xtremManufacturing.nodes.MaterialTracking.createMaterialTrackings(
                        context,
                        workOrder!,
                        'WOTUS00320200001',
                        1,
                    ),
                    'The sum of the stock-detail or allocation quantity must be equal to the line quantity.',
                );
            },
            { today: '2020-05-09' },
        ));

    it('Create material trackings - including date and component', () =>
        Test.withContext(
            async context => {
                const workOrder = await context.tryRead(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US003|WO2021US0030001',
                });
                assert.isNotNull(workOrder);
                const result = await xtremManufacturing.nodes.MaterialTracking.createMaterialTrackings(
                    context,
                    workOrder!,
                    'WOTUS00320200001',
                    50,
                    date.today(),
                    50,
                    true,
                );
                const trackings = await context
                    .query(xtremManufacturing.nodes.MaterialTrackingLine, {
                        filter: { document: { number: 'WOTUS00320200001' } },
                    })
                    .toArray();
                assert.isNotNull(result);
                assert.equal(trackings.length, 1);
                assert.equal(await (await trackings[0].document).number, 'WOTUS00320200001');
            },
            { today: '2020-05-09' },
        ));

    it('Create material trackings - complete line under required', () =>
        Test.withContext(
            async context => {
                const workOrderComponent = await context.tryRead(xtremManufacturing.nodes.WorkOrderComponent, {
                    _id: 3004,
                });

                assert(workOrderComponent);

                assert.equal(await workOrderComponent.requiredQuantity, 20);
                assert.equal(await workOrderComponent.consumedQuantity, 3);
                assert.equal(await workOrderComponent.completedQuantityPercentage, 15);

                const workOrderRequiredQuantity = await (await workOrderComponent.workOrder).productionComponents
                    .filter(async cmp =>
                        xtremManufacturing.functions.workOrderLib.costTypeComponents(await cmp.lineStatus),
                    )
                    .reduce(async (prev, component) => prev + +(await component.requiredQuantity), 0);
                assert.equal(workOrderRequiredQuantity, 3130);

                const workOrderCompletedQuantity = await (
                    await workOrderComponent.workOrder
                ).productionComponents.reduce(async (prev, component) => prev + +(await component.consumedQuantity), 0);
                assert.equal(workOrderCompletedQuantity, 148);

                const tracking = await xtremManufacturing.nodes.MaterialTracking.createSingleMaterialTracking(context, {
                    component: workOrderComponent,
                    workOrderTrackingNumber: 'WOTUS00320200001',
                    quantity: 20,
                    date: date.today(),
                    isCompleted: true,
                    isStandaloneLine: true,
                    isAutomated: true,
                });
                assert.isNotNull(tracking);

                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    tracking.materialTracking!._id,
                    xtremManufacturing.nodes.MaterialTracking,
                    'issue',
                    xtremManufacturing.nodes.MaterialTracking.onStockReply,
                );

                const workOrder = await context.tryRead(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US003|WO2021US0030001',
                });
                assert.isNotNull(workOrder);

                // 100 * (workOrderConsumedQuantity+20/ workOrderRequiredQuantity) = 100 * (148+20) / 313 = 5.3674121405750798722
                // eslint-disable-next-line @typescript-eslint/no-loss-of-precision
                assert.equal((await workOrder?.materialCompletionPercentage)?.valueOf(), 5.3674121405750798722);
                const component = await workOrder?.productionComponents.find(
                    async item => (await item.componentNumber) === (await workOrderComponent!.componentNumber),
                );
                assert.equal(await component?.consumedQuantity, 23);
                assert.equal(await component?.lineStatus, 'completed');
                assert.equal(await component?.completedQuantityPercentage, 100);
            },
            { today: '2020-05-09' },
        ));
});

describe('createSingleMaterialTracking function', () => {
    it('Track totally one component of a not yet tracked work order', () =>
        Test.withContext(
            async context => {
                let workOrder = await context.tryRead(xtremManufacturing.nodes.WorkOrder, {
                    number: 'WO2021US0030003',
                });
                assert.isNotNull(workOrder);

                const component = (await workOrder!.productionComponents.toArray())[0] as any;
                assert.equal(await component.lineStatus, 'pending');
                assert.equal(await workOrder!.status, 'pending');

                const tracking = await xtremManufacturing.nodes.MaterialTracking.createSingleMaterialTracking(context, {
                    component,
                    workOrderTrackingNumber: 'TRK1',
                    quantity: 11,
                    date: date.today(),
                    isCompleted: true,
                    isStandaloneLine: true,
                    isAutomated: true,
                });
                assert.isNotNull(tracking);

                await xtremStockData.functions.testLib.simulateStockTransaction(
                    context,
                    tracking.materialTracking!._id,
                    xtremManufacturing.nodes.MaterialTracking,
                    'issue',
                    xtremManufacturing.nodes.MaterialTracking.onStockReply,
                );

                workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    number: 'WO2021US0030003',
                });
                assert.isNotNull(workOrder);
                assert.equal(await (await workOrder!.productionComponents.toArray())[0].lineStatus, 'completed');
                assert.equal(await workOrder!.status, 'inProgress');
            },
            { today: '2020-07-10' },
        ));

    it('Material tracking creation should fail because of insufficient material', () =>
        Test.withContext(
            async context => {
                const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    _id: '#US001|WOS-SM-FIRM-NOALLREQ',
                });
                const component = await workOrder.productionComponents.at(0);

                assert(component);
                await assert.isRejected(
                    xtremManufacturing.nodes.MaterialTracking.createSingleMaterialTracking(context, {
                        component,
                        workOrderTrackingNumber: 'TRK2',
                        quantity: 1001,
                        date: date.today(),
                        isCompleted: true,
                        isStandaloneLine: true,
                        isAutomated: true,
                    }),
                    'Not enough stock on Site: US001 Item: SC1.',
                );
            },
            { today: '2020-07-10' },
        ));
});

describe('createSingleMaterialTracking function (allocation focus)', () => {
    it('Create material tracking - using work-oder allocation', () =>
        Test.withContext(
            async context => {
                const workOrderComponent = await context.read(xtremManufacturing.nodes.WorkOrderComponent, {
                    _id: 3004,
                });
                const item = (await workOrderComponent.item)!;
                const site = await (await workOrderComponent.workOrder).site;
                const stockSearchData = {
                    item,
                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC5' }),
                    site,
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    stockUnit: await item.stockUnit,
                    owner: await site.id,
                    lot: null,
                };
                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

                if (!stockRecord) {
                    assert.isNotNull(stockRecord);
                    return;
                }

                const allocationResult = await xtremStockData.functions.allocationLib.createAllocation(context, {
                    documentLine: workOrderComponent as any,
                    quantity: 10,
                    stockRecord: stockRecord as any,
                });

                xtremStockData.functions.testLib.registerAllocationMocks({
                    context,
                    allocationResult,
                    isMockActivated: true,
                });

                const result = await xtremManufacturing.nodes.MaterialTracking.createSingleMaterialTracking(context, {
                    isCompleted: true,
                    isStandaloneLine: false,
                    workOrderTrackingNumber: 'TEST',
                    component: workOrderComponent,
                    date: date.today(),
                    quantity: 10,
                });
                let { materialTracking } = result;

                if (!materialTracking) {
                    assert.isNotNull(materialTracking);
                    return;
                }

                const materialTrackingId = materialTracking._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                materialTracking = await context.read(xtremManufacturing.nodes.MaterialTracking, {
                    _id: materialTrackingId,
                });

                const allocations = await (await materialTracking.lines.elementAt(0)).stockAllocations.toArray();

                assert.isNotEmpty(allocations);
                assert.equal(allocations.length, 1);
                assert.equal(await allocations[0].quantityInStockUnit, 10);

                xtremStockData.functions.testLib.unregisterAllocationMocks();
            },
            { today: '2020-05-09' },
        ));
    it('Create material tracking - using given allocation data', () =>
        Test.withContext(
            async context => {
                const workOrderComponent = await context.read(xtremManufacturing.nodes.WorkOrderComponent, {
                    _id: 3004,
                });
                const item = (await workOrderComponent.item)!;
                const site = await (await workOrderComponent.workOrder).site;
                const stockSearchData = {
                    item,
                    location: await context.read(xtremMasterData.nodes.Location, { id: 'LOC5' }),
                    site,
                    status: await context.read(xtremStockData.nodes.StockStatus, { id: 'A' }),
                    stockUnit: await item.stockUnit,
                    owner: await site.id,
                    lot: null,
                };
                const stockRecord = await xtremStockData.functions.stockLib.getStockRecord(context, stockSearchData);

                if (!stockRecord) {
                    assert.isNotNull(stockRecord);
                    return;
                }

                xtremStockData.functions.testLib.registerAllocationMocks({
                    context,
                    allocationResult: {} as any,
                    isMockActivated: false,
                });

                const result = await xtremManufacturing.nodes.MaterialTracking.createSingleMaterialTracking(context, {
                    isCompleted: true,
                    isStandaloneLine: false,
                    workOrderTrackingNumber: 'TEST',
                    component: workOrderComponent,
                    date: date.today(),
                    quantity: 10,
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: 10,
                            stockRecord: stockRecord as any,
                        },
                    ],
                });
                let { materialTracking } = result;

                if (!materialTracking) {
                    assert.isNotNull(materialTracking);
                    return;
                }

                const materialTrackingId = materialTracking._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                materialTracking = await context.read(xtremManufacturing.nodes.MaterialTracking, {
                    _id: materialTrackingId,
                });

                const allocations = await (await materialTracking.lines.elementAt(0)).stockAllocations.toArray();

                assert.isNotEmpty(allocations);
                assert.equal(allocations.length, 1);
                assert.equal(await allocations[0].quantityInStockUnit, 10);
                assert.equal(
                    JSON.stringify(
                        await asyncArray(allocations)
                            .map(async alloc => (await alloc.stockRecord)._id)
                            .toArray(),
                    ),
                    JSON.stringify([50]),
                );

                xtremStockData.functions.testLib.unregisterAllocationMocks();
            },
            { today: '2020-05-09' },
        ));
    it('Create material tracking - without allocation data', () =>
        Test.withContext(
            async context => {
                const workOrderComponent = await context.read(xtremManufacturing.nodes.WorkOrderComponent, {
                    _id: 3004,
                });

                xtremStockData.functions.testLib.registerAllocationMocks({
                    context,
                    allocationResult: {} as any,
                    isMockActivated: false,
                });

                const result = await xtremManufacturing.nodes.MaterialTracking.createSingleMaterialTracking(context, {
                    isCompleted: true,
                    isStandaloneLine: false,
                    workOrderTrackingNumber: 'TEST',
                    component: workOrderComponent,
                    date: date.today(),
                    quantity: 10,
                    isAutomated: true,
                });
                let { materialTracking } = result;

                if (!materialTracking) {
                    assert.isNotNull(materialTracking);
                    return;
                }

                const materialTrackingId = materialTracking._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                materialTracking = await context.read(xtremManufacturing.nodes.MaterialTracking, {
                    _id: materialTrackingId,
                });

                const allocations = await (await materialTracking.lines.elementAt(0)).stockAllocations.toArray();

                assert.isNotEmpty(allocations);
                assert.equal(allocations.length, 1);
                assert.equal(await allocations[0].quantityInStockUnit, 10);
                assert.equal(
                    JSON.stringify(
                        await asyncArray(allocations)
                            .map(async alloc => (await alloc.stockRecord)._id)
                            .toArray(),
                    ),
                    JSON.stringify([50]),
                );

                xtremStockData.functions.testLib.unregisterAllocationMocks();
            },
            { today: '2020-05-09' },
        ));
    it('Create material tracking - using work-oder allocation + allocation data + automatic creation of missing allocation', () =>
        Test.withContext(
            async context => {
                const workOrderComponent = await context.read(xtremManufacturing.nodes.WorkOrderComponent, {
                    _id: '#US003|WO2021US0030001|50',
                });
                const item = await workOrderComponent.item; // BOX 50BT
                assert.isNotNull(item);
                const site = await (await workOrderComponent.workOrder).site;
                const location1 = await context.read(xtremMasterData.nodes.Location, {
                    _id: '#LOC1|US003|Production line',
                });
                const location5 = await context.read(xtremMasterData.nodes.Location, {
                    _id: '#LOC5|US003|Production line',
                });
                const statusA = await context.read(xtremStockData.nodes.StockStatus, { id: 'A' });
                const statusA1 = await context.read(xtremStockData.nodes.StockStatus, { id: 'A1' });
                const stockSearchData = {
                    item,
                    location: location5,
                    site,
                    status: statusA1,
                    stockUnit: await item.stockUnit,
                    owner: await site.id,
                    lot: null,
                };

                // stock _id 73
                const stockRecordForWorkOrderAllocation = await xtremStockData.functions.stockLib.getStockRecord(
                    context,
                    stockSearchData,
                );
                assert.isNotNull(stockRecordForWorkOrderAllocation);

                // stock _id 74
                stockSearchData.location = location1;
                stockSearchData.status = statusA;
                const stockRecordForMaterialTrackingAllocation = await xtremStockData.functions.stockLib.getStockRecord(
                    context,
                    stockSearchData,
                );
                assert.isNotNull(stockRecordForMaterialTrackingAllocation);

                const allocationResult = await xtremStockData.functions.allocationLib.createAllocation(context, {
                    documentLine: workOrderComponent as any,
                    quantity: 3,
                    stockRecord: stockRecordForWorkOrderAllocation as any,
                });

                xtremStockData.functions.testLib.registerAllocationMocks({
                    context,
                    allocationResult,
                    isMockActivated: true,
                });

                const result = await xtremManufacturing.nodes.MaterialTracking.createSingleMaterialTracking(context, {
                    isCompleted: true,
                    isStandaloneLine: false,
                    workOrderTrackingNumber: 'TEST',
                    component: workOrderComponent,
                    date: date.today(),
                    quantity: 10,
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: 3,
                            stockRecord: stockRecordForMaterialTrackingAllocation,
                        },
                    ],
                    isAutomated: true,
                });
                let { materialTracking } = result;

                assert.isNotNull(materialTracking);

                const materialTrackingId = materialTracking._id;

                // rollbackCache function will flush deferred actions and saves
                await Test.rollbackCache(context);

                materialTracking = await context.read(xtremManufacturing.nodes.MaterialTracking, {
                    _id: materialTrackingId,
                });

                const allocations = await (await materialTracking.lines.elementAt(0)).stockAllocations.toArray();

                assert.isNotEmpty(allocations);
                assert.equal(allocations.length, 3);
                assert.equal(
                    (
                        await asyncArray(allocations).reduce(
                            async (sum, alloc) => sum + (await alloc.quantityInStockUnit),
                            0,
                        )
                    ).valueOf(),
                    10,
                );
                assert.equal(
                    JSON.stringify(
                        await asyncArray(allocations)
                            .map(async alloc => (await alloc.stockRecord)._id)
                            .toArray(),
                    ),
                    JSON.stringify([74, 50, 73]),
                );
                assert.equal(
                    JSON.stringify(
                        await asyncArray(allocations)
                            .map(async alloc => (await alloc.quantityInStockUnit).valueOf())
                            .toArray(),
                    ),
                    JSON.stringify(['3', '4', '3']),
                );

                xtremStockData.functions.testLib.unregisterAllocationMocks();
            },
            { today: '2020-05-09' },
        ));

    after(() => {
        Sinon.restore();
    });
});

describe('material tracking posting to stock', () => {
    it('Check onceStockCompleted is called only when the document is completed', () =>
        Test.withContext(async context => {
            const result = await xtremStockData.functions.testLib.testOnceStockCompleted(context, {
                clas: xtremManufacturing.nodes.MaterialTracking,
                movementType: 'issue',
                documents: [
                    { key: { number: 'WOTUS00300003' }, isCompleted: false },
                    { key: { number: 'WOTUS00300004' }, isCompleted: true },
                    { key: { number: 'WOTUS00300005' }, isCompleted: false },
                    { key: { number: 'WOTUS00300006' }, isCompleted: true },
                ],
                returnedProperty: 'number',
            });

            assert.deepEqual(result, ['WOTUS00300004', 'WOTUS00300006']);
        }));
});

describe('material tracking fail on phantomBom component', () => {
    it('Create material tracking for work order component that is a phantom BOM', () =>
        Test.withContext(
            async context => {
                const workOrderComponent = await context.read(xtremManufacturing.nodes.WorkOrderComponent, {
                    _id: '#US001|WO240002|30',
                });
                await assert.isRejected(
                    xtremManufacturing.nodes.MaterialTracking.createSingleMaterialTracking(context, {
                        isCompleted: true,
                        isStandaloneLine: false,
                        workOrderTrackingNumber: 'TEST',
                        component: workOrderComponent,
                        date: date.today(),
                        quantity: 10,
                        isAutomated: true,
                    }),
                    'Phantom components are not allowed in material tracking.',
                );
            },
            { today: '2024-08-06', testActiveServiceOptions: [xtremMasterData.serviceOptions.phantomItemOption] },
        ));
});
