import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib';

describe('Try to change isAdded for a work order operation', () => {
    it('Try to change isAdded for an operation', () =>
        Test.withContext(async context => {
            const WO = 'WO2021US0030004';

            const workOrder1 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const operationLine1 = await workOrder1.productionOperations.elementAt(1);
            assert.equal(await operationLine1.isAdded, false);

            await operationLine1.$.set({ isAdded: true });
            await assert.isRejected(workOrder1.$.save());
            assert.deepEqual(workOrder1.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['productionOperations', operationLine1._id.toString()],
                    message: 'The isAdded property cannot be changed.',
                },
            ]);
        }));

    it('Try to change isAdded for a resource', () =>
        Test.withContext(async context => {
            const WO = 'WO2021US0030004';

            const workOrder1 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const operationLine1 = await workOrder1.productionOperations.elementAt(1);
            assert.equal(await operationLine1.isAdded, false);
            const resource1 = await operationLine1.resources.elementAt(0);
            await resource1.$.set({ isAdded: true });
            await assert.isRejected(workOrder1.$.save());
            assert.deepEqual(workOrder1.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [
                        'productionOperations',
                        operationLine1._id.toString(),
                        'resources',
                        resource1._id.toString(),
                    ],
                    message: 'The isAdded property cannot be changed.',
                },
            ]);
        }));
});

describe('Propagate a work order operation exclusion', () => {
    it('Propagate excluded status from an operation to its resources', () =>
        Test.withContext(async context => {
            const WO = 'WO2021US0030004';
            const operation = 10;

            // Propagate excluded status from a pending operation to its resources
            const workOrder1 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const operationLine1 = await workOrder1.productionOperations.elementAt(1);
            assert.equal(await operationLine1.operationNumber, operation);
            assert.equal(await operationLine1.status, 'pending');
            assert.equal(await operationLine1.resources.length, 2);
            assert.equal(await (await operationLine1.resources.elementAt(0)).status, 'pending');
            assert.equal(await (await operationLine1.resources.elementAt(1)).status, 'pending');

            await (await workOrder1.productionOperations.elementAt(1)).$.set({ status: 'excluded' });
            await workOrder1.$.save();

            const workOrder2 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const operationLine2 = await workOrder2.productionOperations.elementAt(1);
            assert.equal(await operationLine2.operationNumber, operation);
            assert.equal(await operationLine2.status, 'excluded');
            assert.equal(await operationLine2.resources.length, 2);
            assert.equal(await (await operationLine2.resources.elementAt(0)).status, 'excluded');
            assert.equal(await (await operationLine2.resources.elementAt(1)).status, 'excluded');

            // Propagate pending status from an excluded operation to its resources
            await (await workOrder2.productionOperations.elementAt(1)).$.set({ status: 'pending' });
            await workOrder2.$.save();

            const workOrder3 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const operationLine3 = await workOrder3.productionOperations.elementAt(1);
            assert.equal(await operationLine3.operationNumber, operation);
            assert.equal(await operationLine3.status, 'pending');
            assert.equal(await operationLine3.resources.length, 2);
            assert.equal(await (await operationLine3.resources.elementAt(0)).status, 'pending');
            assert.equal(await (await operationLine3.resources.elementAt(1)).status, 'pending');
        }));

    it('Exclude all resources for an operation', () =>
        Test.withContext(async context => {
            const WO = 'WO2021US0030004';
            const operation = 10;

            // Exclude one of the 2 pending resources for a pending operation => operation remains pending
            const workOrder1 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const operationLine1 = await workOrder1.productionOperations.elementAt(1);
            assert.equal(await operationLine1.operationNumber, operation);
            assert.equal(await operationLine1.isProductionStep, false);
            assert.equal(await operationLine1.status, 'pending');
            assert.equal(await operationLine1.resources.length, 2);
            assert.equal(await (await operationLine1.resources.elementAt(0)).status, 'pending');
            assert.equal(await (await operationLine1.resources.elementAt(0)).isResourceQuantity, true);
            assert.equal(await (await operationLine1.resources.elementAt(1)).status, 'pending');
            assert.equal(await (await operationLine1.resources.elementAt(1)).isResourceQuantity, false);

            await (
                await (await workOrder1.productionOperations.elementAt(1)).resources.elementAt(1)
            ).$.set({
                status: 'excluded',
            });
            await workOrder1.$.save();

            const workOrder2 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const operationLine2 = await workOrder2.productionOperations.elementAt(1);
            assert.equal(await operationLine2.operationNumber, operation);
            assert.equal(await operationLine2.resources.length, 2);
            assert.equal(await (await operationLine2.resources.elementAt(0)).status, 'pending');
            assert.equal(await (await operationLine2.resources.elementAt(1)).status, 'excluded');
            assert.equal(await operationLine2.status, 'pending');

            // Exclude the last pending resource of a pending operation, operation becomes excluded
            await (
                await (await workOrder2.productionOperations.elementAt(1)).resources.elementAt(0)
            ).$.set({
                status: 'excluded',
            });
            await workOrder2.$.save();

            const workOrder3 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const operationLine3 = await workOrder3.productionOperations.elementAt(1);
            assert.equal(await operationLine3.operationNumber, operation);
            assert.equal(await operationLine3.resources.length, 2);
            assert.equal(await (await operationLine3.resources.elementAt(0)).status, 'excluded');
            assert.equal(await (await operationLine3.resources.elementAt(1)).status, 'excluded');
            assert.equal(await operationLine3.status, 'excluded');
        }));
});

describe('Try to change data for a work order excluded operation', () => {
    it('Try to change name of an excluded operation', () =>
        Test.withContext(async context => {
            const WO = 'WO2021US0030004';

            // exclude 2nd operation
            const workOrder1 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            await (await workOrder1.productionOperations.elementAt(1)).$.set({ status: 'excluded' });
            await workOrder1.$.save();

            // change name of the excluded operation
            const workOrder2 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const operationLine2 = await workOrder2.productionOperations.elementAt(1);
            await operationLine2.$.set({ name: 'new name' });
            await assert.isRejected(workOrder2.$.save());

            assert.deepEqual(workOrder2.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['productionOperations', operationLine2._id.toString()],
                    message: 'The excluded operation with number 10 cannot be modified.',
                },
            ]);
        }));

    it('Try to change expected time of an excluded resource', () =>
        Test.withContext(async context => {
            const WO = 'WO2021US0030004';

            // exclude 2nd operation
            const workOrder1 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            await (await workOrder1.productionOperations.elementAt(1)).$.set({ status: 'excluded' });
            await workOrder1.$.save();

            // change resource of the excluded operation
            const workOrder2 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const operationLine = await workOrder2.productionOperations.elementAt(1);
            const resourceLine = await operationLine.resources.elementAt(1);
            await resourceLine.$.set({ expectedSetupTime: 12 });
            await assert.isRejected(workOrder2.$.save());

            assert.deepEqual(workOrder2.$.context.diagnoses, [
                {
                    message: 'The resources for the excluded operation with number 10 cannot be modified.',
                    path: [
                        'productionOperations',
                        operationLine._id.toString(),
                        'resources',
                        resourceLine._id.toString(),
                    ],
                    severity: 3,
                },
            ]);
        }));

    it('Try to remove exclusion of resource for an excluded operation', () =>
        Test.withContext(async context => {
            const WO = 'WO2021US0030004';

            // exclude 2nd operation
            const workOrder1 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            await (await workOrder1.productionOperations.elementAt(1)).$.set({ status: 'excluded' });
            await workOrder1.$.save();

            // reset resource not excluded for the excluded operation
            const workOrder2 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const operationLine = await workOrder2.productionOperations.elementAt(1);
            const resourceLine = await operationLine.resources.elementAt(0);
            await resourceLine.$.set({ status: 'pending' });
            await assert.isRejected(workOrder2.$.save());

            assert.deepEqual(workOrder2.$.context.diagnoses, [
                {
                    message: 'The resources for the excluded operation with number 10 cannot be modified.',
                    path: [
                        'productionOperations',
                        operationLine._id.toString(),
                        'resources',
                        resourceLine._id.toString(),
                    ],
                    severity: 3,
                },
            ]);
        }));

    it('Resources attribute type restricted to', () =>
        Test.withContext(async context => {
            const WO = 'WO2021US0030004';

            // Exclude one of the 2 pending resources for a pending operation => operation remains pending
            const workOrder1 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const operationLine1 = await workOrder1.productionOperations.elementAt(1);
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            operationLine1.resources.forEach(async resource => {
                await resource.$.set({
                    storedAttributes: { project: '', employee: '', task: 'TASK1' },
                });
            });
            await assert.isRejected(operationLine1.$.save());

            const resource1 = await operationLine1.resources.elementAt(0);
            const resource2 = await operationLine1.resources.elementAt(1);

            assert.deepEqual(operationLine1.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['resources', resource1._id.toString()],
                    message: 'The Project attribute needs to be filled in.',
                },
                {
                    severity: 3,
                    path: ['resources', resource2._id.toString()],
                    message: 'The Project attribute needs to be filled in.',
                },
            ]);
        }));
});

describe('Computed values calculation', () => {
    it('Completed time percentage', () =>
        Test.withContext(async context => {
            const workOrder1 = await context.read(xtremManufacturing.nodes.WorkOrder, {
                number: 'WIP Variance test 1',
            });
            const operation10 = await workOrder1.productionOperations.elementAt(0);
            assert.equal(await operation10.operationNumber, 10);
            assert.equal(await (await operation10.setupTimeUnit).id, 'MINUTE');
            assert.equal(await operation10.expectedSetupTime, 15);
            assert.equal(await operation10.actualSetupTime, 20);
            assert.equal(await (await operation10.runTimeUnit).id, 'HOUR');
            assert.equal(await operation10.expectedRunTime, 12);
            assert.equal(await operation10.actualRunTime, 3);
            assert.equal((await operation10.completedTimePercentage).toFixed(2), '27.21');

            const resource1 = await operation10.resources.elementAt(1); // AFILM100L
            assert.equal(await (await resource1.setupTimeUnit).id, 'MINUTE');
            assert.equal(await resource1.expectedSetupTime, 10);
            assert.equal(await resource1.actualSetupTime, 15);
            assert.equal(await (await resource1.runTimeUnit).id, 'HOUR');
            assert.equal(await resource1.expectedRunTime, 12);
            assert.equal(await resource1.actualRunTime, 2);
            assert.equal((await resource1.completedTimePercentage).toFixed(2), '18.49');

            const resource2 = await operation10.resources.elementAt(0); // COST_LAB_01
            assert.equal(await (await resource2.setupTimeUnit).id, 'MINUTE');
            assert.equal(await resource2.expectedSetupTime, 15);
            assert.equal(await resource2.actualSetupTime, 20);
            assert.equal(await (await resource2.runTimeUnit).id, 'HOUR');
            assert.equal(await resource2.expectedRunTime, 11);
            assert.equal(await resource2.actualRunTime, 3);
            assert.equal((await resource2.completedTimePercentage).toFixed(2), '29.63');

            const operation20 = await workOrder1.productionOperations.elementAt(1);
            assert.equal(await operation20.operationNumber, 20);
            assert.equal(await (await operation20.setupTimeUnit).id, 'MINUTE');
            assert.equal(await operation20.expectedSetupTime, 20);
            assert.equal(await operation20.actualSetupTime, 10);
            assert.equal(await (await operation20.runTimeUnit).id, 'HOUR');
            assert.equal(await operation20.expectedRunTime, 2);
            assert.equal(await operation20.actualRunTime, 1);
            assert.equal((await operation20.completedTimePercentage).toFixed(2), '50.00');

            assert.equal((await workOrder1.processCompletionPercentage).toFixed(2), '30.86');
        }));
});
