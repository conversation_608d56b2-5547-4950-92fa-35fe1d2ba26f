import { date, Test, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib';

function compareData(
    actual: xtremStockData.interfaces.ProjectedStockRecord[] | null,
    expected: xtremStockData.interfaces.ProjectedStockRecord[] | null,
): boolean {
    if (actual && expected) {
        assert.equal(actual.length, expected.length);

        for (let i: number = 0; i < actual.length; i += 1) {
            assert.equal(actual[i].day.toString(), expected[i].day.toString());
            assert.equal(actual[i].demand, expected[i].demand);
            assert.equal(actual[i].supply, expected[i].supply, `index=${i}`);
            assert.equal(actual[i].stockLevel, expected[i].stockLevel);
        }
        return true;
    }
    return actual === expected;
}

describe('Work order component node', () => {
    before(() => {});

    it('Create test', () =>
        Test.withContext(async context => {
            const woComponent = await context.create(xtremManufacturing.nodes.WorkOrderComponent, {
                workOrder: 4,
                componentNumber: 100,
                item: '#Chemical A',
                linkQuantity: 100,
                requiredQuantity: 100,
                requiredDate: date.today(),
                consumedQuantity: 0,
                unit: { id: 'METER' },
            });

            await woComponent.$.save();

            assert.deepEqual(woComponent.$.context.diagnoses, []);
            assert.equal(await woComponent.componentNumber, 100);
            assert.equal(await woComponent.lineStatus, 'pending');
            assert.equal(await woComponent.stockOnHand, 0);
            assert.equal(await woComponent.availableQuantityInStockUnit, 0);
            assert.equal(await woComponent.stockShortageInStockUnit, 100);
            assert.equal(await woComponent.stockShortageStatus, true);
        }));

    it('Try to change isAdded for a component', () =>
        Test.withContext(async context => {
            const WO = 'WO2021US0030004';
            const component = 20;

            const workOrder1 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const componentLine1 = await workOrder1.productionComponents.elementAt(1);
            assert.equal(await componentLine1.componentNumber, component);
            assert.equal(await componentLine1.isAdded, false);

            await (await workOrder1.productionComponents.elementAt(1)).$.set({ isAdded: true });
            await assert.isRejected(workOrder1.$.save());
            assert.deepEqual(workOrder1.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['productionComponents', '3016'],
                    message: "The 'isAdded' property cannot be updated.",
                },
            ]);
        }));

    it('Try to scrap factor of an excluded component', () =>
        Test.withContext(async context => {
            const WO = 'WO2021US0030004';
            const component = 20;

            // exclude 2nd component
            const workOrder1 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const componentLine1 = await workOrder1.productionComponents.elementAt(1);
            assert.equal(await componentLine1.componentNumber, component);
            assert.equal(await componentLine1.lineStatus, 'pending');
            assert.equal(await componentLine1.linkQuantity, 1000);
            assert.equal(await componentLine1.scrapFactor, 0);
            assert.equal(await componentLine1.requiredQuantity, 100);

            await (await workOrder1.productionComponents.elementAt(1)).$.set({ lineStatus: 'excluded' });
            await workOrder1.$.save();

            // change scrap factor of the excluded component
            const workOrder2 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            await (await workOrder2.productionComponents.elementAt(1)).$.set({ scrapFactor: 5 });
            await assert.isRejected(workOrder2.$.save());

            assert.deepEqual(workOrder2.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['productionComponents', '3016'],
                    message: 'The component number 20 is excluded and cannot be modified.',
                },
            ]);
        }));

    it('Try to refer to an excluded operation', () =>
        Test.withContext(async context => {
            const WO = 'WO2021US0030004';
            const operation = 10;
            const component = 20;

            // change operation from 5 to 10 for component 20
            const workOrder1 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );

            const componentLine1 = await workOrder1.productionComponents.elementAt(1);

            assert.equal(await componentLine1.componentNumber, component);
            assert.equal(await componentLine1.lineStatus, 'pending');
            assert.equal(
                await (
                    await componentLine1.operation
                )?.operationNumber,
                await (
                    await workOrder1.productionOperations.elementAt(0)
                ).operationNumber,
            );

            await (
                await workOrder1.productionComponents.elementAt(1)
            ).$.set({
                operation: await workOrder1.productionOperations.elementAt(1),
            });
            await workOrder1.$.save();

            // exclude operation 10
            const workOrder2 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );

            const componentLine2 = await workOrder2.productionComponents.elementAt(1);

            assert.equal(await componentLine2.componentNumber, component);
            assert.equal(await componentLine2.lineStatus, 'pending');
            assert.equal(
                await (
                    await componentLine2.operation
                )?.operationNumber,
                await (
                    await workOrder2.productionOperations.elementAt(1)
                ).operationNumber,
            );
            assert.equal(await (await componentLine2.operation)?.operationNumber, operation);

            const operationLine2 = await workOrder2.productionOperations.at(1);
            assert.equal(await operationLine2?.operationNumber, operation);
            assert.equal(await operationLine2?.status, 'pending');

            await (await workOrder2.productionOperations.elementAt(1)).$.set({ status: 'excluded' });

            await assert.isRejected(workOrder2.$.save(), 'The record was not updated.');
            assert.deepEqual(workOrder2.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['productionComponents', '3016', 'operation'],
                    message: 'The 20 component cannot be linked to the 10 excluded operation.',
                },
            ]);
        }));

    it('Get projected stock - Create workOrder node - with finished goods and components', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const item = await context.read(xtremMasterData.nodes.Item, { _id: '#Muesli' });
                const itemComponent = await context.read(xtremMasterData.nodes.Item, { _id: '#ITME_LOT' });

                let result = await xtremStockData.nodeExtensions.ItemSiteExtension.getProjectedStock(
                    context,
                    item,
                    site,
                    5,
                );
                assert.isNotNull(result);
                compareData(result, [
                    {
                        day: date.today(),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(1),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(2),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(3),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(4),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                ]);

                result = await xtremStockData.nodeExtensions.ItemSiteExtension.getProjectedStock(
                    context,
                    itemComponent,
                    site,
                    5,
                );
                assert.isNotNull(result);
                compareData(result, [
                    {
                        day: date.today(),
                        demand: 0,
                        supply: 0,
                        stockLevel: 5000,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                    {
                        day: date.today().addDays(1),
                        demand: 0,
                        supply: 0,
                        stockLevel: 5000,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                    {
                        day: date.today().addDays(2),
                        demand: 0,
                        supply: 0,
                        stockLevel: 5000,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                    {
                        day: date.today().addDays(3),
                        demand: 0,
                        supply: 0,
                        stockLevel: 5000,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                    {
                        day: date.today().addDays(4),
                        demand: 0,
                        supply: 0,
                        stockLevel: 5000,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                ]);

                const workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
                    site: '#US001',
                    name: 'Another cake',
                    startDate: date.today(),
                    endDate: date.today().addDays(3),
                    type: 'firm',
                    category: 2,
                    bomCode: 1,
                    routingCode: 1,
                    productionItems: [
                        {
                            releasedItem: '#Muesli',
                            releasedQuantity: 10,
                        },
                    ],
                    productionComponents: [
                        {
                            componentNumber: 10,
                            item: '#ITME_LOT',
                            linkQuantity: 100,
                            requiredQuantity: 100,
                            requiredDate: date.today().addDays(3),
                            unit: { id: 'BOX' },
                        },
                    ],
                });
                await workOrder.$.save();
                assert.deepEqual(workOrder.$.context.diagnoses, []);

                result = await xtremStockData.nodeExtensions.ItemSiteExtension.getProjectedStock(
                    context,
                    item,
                    site,
                    5,
                );
                assert.isNotNull(result);
                compareData(result, [
                    {
                        day: date.today(),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(1),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(2),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(3),
                        demand: 0,
                        supply: 10,
                        stockLevel: 100210,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(4),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100210,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                ]);

                result = await xtremStockData.nodeExtensions.ItemSiteExtension.getProjectedStock(
                    context,
                    itemComponent,
                    site,
                    5,
                );
                assert.isNotNull(result);
                compareData(result, [
                    {
                        day: date.today(),
                        demand: 0,
                        supply: 0,
                        stockLevel: 5000,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                    {
                        day: date.today().addDays(1),
                        demand: 0,
                        supply: 0,
                        stockLevel: 5000,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                    {
                        day: date.today().addDays(2),
                        demand: 0,
                        supply: 0,
                        stockLevel: 5000,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                    {
                        day: date.today().addDays(3),
                        demand: 100,
                        supply: 0,
                        stockLevel: 4900,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                    {
                        day: date.today().addDays(4),
                        demand: 0,
                        supply: 0,
                        stockLevel: 4900,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                ]);

                // Check that the WIP is no more considered if the status is closed
                await (await workOrder.productionItem)!.$.set({ lineStatus: 'completed' });
                await (await workOrder.productionComponents!.elementAt(0)).$.set({ lineStatus: 'completed' });
                await workOrder.$.set({ status: 'completed' });
                await workOrder.$.save();

                await xtremManufacturing.nodes.WorkOrder.closeWorkOrder(context, workOrder, date.today());

                result = await xtremStockData.nodeExtensions.ItemSiteExtension.getProjectedStock(
                    context,
                    item,
                    site,
                    5,
                );
                assert.isNotNull(result);
                compareData(result, [
                    {
                        day: date.today(),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(1),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(2),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(3),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                    {
                        day: date.today().addDays(4),
                        demand: 0,
                        supply: 0,
                        stockLevel: 100200,
                        reorderLevel: 10,
                        safetyLevel: 220,
                    },
                ]);

                result = await xtremStockData.nodeExtensions.ItemSiteExtension.getProjectedStock(
                    context,
                    itemComponent,
                    site,
                    5,
                );
                assert.isNotNull(result);
                compareData(result, [
                    {
                        day: date.today(),
                        demand: 0,
                        supply: 0,
                        stockLevel: 5000,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                    {
                        day: date.today().addDays(1),
                        demand: 0,
                        supply: 0,
                        stockLevel: 5000,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                    {
                        day: date.today().addDays(2),
                        demand: 0,
                        supply: 0,
                        stockLevel: 5000,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                    {
                        day: date.today().addDays(3),
                        demand: 0,
                        supply: 0,
                        stockLevel: 5000,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                    {
                        day: date.today().addDays(4),
                        demand: 0,
                        supply: 0,
                        stockLevel: 5000,
                        reorderLevel: 10,
                        safetyLevel: 100,
                    },
                ]);
            },
            {
                today: '2020-06-10',
            },
        ));
    it('WIP quantities - Create workOrder node - with finished goods and components', () =>
        Test.withContext(
            async context => {
                const requiredQuantity = await (
                    await context.read(xtremMasterData.nodes.ItemSite, { item: '#ITME_LOT', site: '#US001' })
                ).requiredQuantity;
                const expectedQuantity = await (
                    await context.read(xtremMasterData.nodes.ItemSite, { item: '#Muesli', site: '#US001' })
                ).expectedQuantity;
                const releasedQuantity = 10;
                const componentQuantity = 100;

                const workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
                    site: '#US001',
                    name: 'Another cake',
                    startDate: date.today(),
                    endDate: date.today().addDays(3),
                    type: 'firm',
                    category: 2,
                    bomCode: 1,
                    routingCode: 1,
                    productionItems: [
                        {
                            releasedItem: '#Muesli',
                            releasedQuantity,
                        },
                    ],
                    productionComponents: [
                        {
                            componentNumber: 10,
                            item: '#ITME_LOT',
                            linkQuantity: componentQuantity,
                            requiredQuantity: componentQuantity,
                            requiredDate: date.today().addDays(3),
                            unit: { id: 'BOX' },
                        },
                    ],
                });
                await workOrder.$.save();
                assert.deepEqual(workOrder.$.context.diagnoses, []);

                const newRequiredQuantity = await (
                    await context.read(xtremMasterData.nodes.ItemSite, { item: '#ITME_LOT', site: '#US001' })
                ).requiredQuantity;
                const newExpectedQuantity = await (
                    await context.read(xtremMasterData.nodes.ItemSite, { item: '#Muesli', site: '#US001' })
                ).expectedQuantity;
                assert.deepEqual(Number(newRequiredQuantity - requiredQuantity), componentQuantity);
                assert.deepEqual(Number(newExpectedQuantity - expectedQuantity), releasedQuantity);
            },
            {
                today: '2020-06-10',
            },
        ));

    it('Ensure work order components of type text have status completed', () =>
        Test.withContext(async context => {
            const workOrderComponent = await context.create(xtremManufacturing.nodes.WorkOrderComponent, {
                workOrder: 1,
                componentNumber: 1,
                lineType: 'text',
                // Default status (lineStatus): pending.
            });

            await assert.isRejected(workOrderComponent.$.save());

            assert.deepEqual(workOrderComponent.$.context.diagnoses, [
                {
                    message: 'The work order component status needs to be completed or excluded if the type is Text.',
                    path: ['lineStatus'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('Create work order component with inactive item', () =>
        Test.withContext(async context => {
            const woComponent = await context.create(xtremManufacturing.nodes.WorkOrderComponent, {
                workOrder: '#US003|WO2021US0030004',
                componentNumber: 110,
                item: '#Chemical B',
                linkQuantity: 110,
                requiredQuantity: 110,
                requiredDate: date.today(),
                consumedQuantity: 0,
                unit: '#METER',
                storedAttributes: { project: '', task: 'TASK1', employee: '' },
            });

            await assert.isRejected(woComponent.$.save());
            assert.deepEqual(woComponent.$.context.diagnoses, [
                {
                    severity: 3,
                    path: ['item'],
                    message: 'The record cannot be referenced because it is inactive.',
                },
                {
                    severity: 3,
                    path: ['item'],
                    message: 'You need to remove the inactive items before you change the document status.',
                },
                {
                    severity: 3,
                    path: ['workInProgress', 'item'],
                    message: 'The record cannot be referenced because it is inactive.',
                },
                {
                    severity: 3,
                    path: [],
                    message: 'The Project attribute needs to be filled in.',
                },
            ]);
        }));

    it('Create work order component test attribute type restricted to', () =>
        Test.withContext(async context => {
            const woComponent = await context.create(xtremManufacturing.nodes.WorkOrderComponent, {
                workOrder: '#US003|WO2021US0030004',
                componentNumber: 100,
                item: '#Chemical A',
                linkQuantity: 100,
                requiredQuantity: 100,
                requiredDate: date.today(),
                consumedQuantity: 0,
                unit: '#METER',
                storedAttributes: { project: '', task: 'TASK1', employee: '' },
            });

            await assert.isRejected(woComponent.$.save());
            assert.deepEqual(woComponent.$.context.diagnoses, [
                {
                    severity: 3,
                    path: [],
                    message: 'The Project attribute needs to be filled in.',
                },
            ]);
        }));

    it('Ensure Create test for line type text does not create Work-order', () =>
        Test.withContext(async context => {
            const woComponent = await context.create(xtremManufacturing.nodes.WorkOrderComponent, {
                workOrder: 1,
                componentNumber: 1,
                lineType: 'text',
                lineStatus: 'completed',
            });

            await woComponent.$.save();
            assert.isNull(await woComponent.workInProgress);
        }));

    it('Ensure work order components of type text should throw Error', () =>
        Test.withContext(async context => {
            const woComponent = await context.create(xtremManufacturing.nodes.WorkOrderComponent, {
                workOrder: 4,
                componentNumber: 100,
                lineStatus: 'completed',
                lineType: 'text',
                workInProgress: {},
            });

            await assert.isRejected(woComponent.$.save());
            assert.deepEqual(woComponent.$.context.diagnoses, [
                {
                    severity: 3,
                    message: 'Work in progress is not needed for a text component.',
                    path: ['workInProgress'],
                },
            ]);
        }));
});
