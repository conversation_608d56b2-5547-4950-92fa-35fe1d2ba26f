import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib';

describe('Work order category node', () => {
    it('Get the default category', () =>
        Test.withContext(async context => {
            const defaultCategory = await xtremManufacturing.functions.getDefaultCategory(context);

            // Assert that the default category is the "Normal" one
            assert.equal(await defaultCategory.id, 'Normal');
            assert.equal(await defaultCategory.name, 'Normal');
            assert.equal(await defaultCategory.description, 'Normal work order');
            assert.equal(await defaultCategory.routing, true);
            assert.equal(await defaultCategory.billOfMaterial, true);
            assert.equal(await defaultCategory.isDefault, true);
        }));

    it('Change the default category', () =>
        Test.withContext(async context => {
            let normalCategory = await context.read(
                xtremManufacturing.nodes.WorkOrderCategory,
                { id: 'Normal' },
                { forUpdate: true },
            );

            let reworkCategory = await context.read(
                xtremManufacturing.nodes.WorkOrderCategory,
                { id: 'Rework' },
                { forUpdate: true },
            );

            // Assert that the normal category is the default one
            assert.equal(await normalCategory.isDefault, true);

            // Assert that the rework category is not the default one
            assert.equal(await reworkCategory.isDefault, false);

            // Change the default category
            await reworkCategory.$.set({ isDefault: true });
            await reworkCategory.$.save();
            await Test.rollbackCache(context);

            // Reload the categories
            normalCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, { id: 'Normal' });
            reworkCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, { id: 'Rework' });

            // Assert that the normal category is not the default anymore
            assert.equal(await normalCategory.isDefault, false);

            // Assert that the rework category is now the default one
            assert.equal(await reworkCategory.isDefault, true);
        }));
});
