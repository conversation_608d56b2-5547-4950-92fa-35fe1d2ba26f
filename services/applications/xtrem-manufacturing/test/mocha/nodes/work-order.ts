import type { Context, LocalizeLocale, decimal } from '@sage/xtrem-core';
import { Test, assertDeepPartialMatch, date } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTechnicalData from '@sage/xtrem-technical-data';
import { assert } from 'chai';
import * as Sinon from 'sinon';
import * as xtremManufacturing from '../../../lib';
import * as testHelper from '../../fixtures/lib';
import { assertionPlannedCost, assertionWipCosts } from '../fixtures/common';

describe('WorkOrder node', () => {
    it('Read workOrder node - Check properties', () =>
        Test.withContext(async context => {
            const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: 'WO2021US0030002' });
            assert.equal(await workOrder.name, 'Bottle 100 ml Hydro-alcoholic gel for hand antisepsis');
            assert.equal(await workOrder.isForwardScheduling, true);
            assert.equal(await (await workOrder.site).id, 'US003');
            assert.equal(
                await (
                    await workOrder.bomCode
                )?.name,
                'Bottle 100 ml Hydro-alcoholic gel for hand antisepsis',
            );
        }));

    it('Create workOrder node - without finished goods', () =>
        Test.withContext(
            async context => {
                const workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
                    site: 2,
                    name: 'Another cake',
                    number: 'WOUS00200001',
                    startDate: date.parse('2020-06-10', context.currentLocale as LocalizeLocale),
                    endDate: date.parse('2020-06-10', context.currentLocale as LocalizeLocale),
                    type: 'planned',
                    category: 2,
                    bomCode: 1,
                    routingCode: 1,
                    productionItems: [
                        {
                            releasedItem: '#Chemical A',
                            releasedQuantity: 1,
                        },
                    ],
                });
                await workOrder.$.save();
                assert.deepEqual(workOrder.$.context.diagnoses, []);
                assert.equal(await workOrder.status, 'pending');
            },
            { today: '2020-06-09' },
        ));

    it('Create workOrder node using sequence counter - without finished goods', () =>
        Test.withContext(
            async context => {
                let workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
                    site: 2,
                    name: 'Another cake',
                    startDate: date.parse('2020-06-10', context.currentLocale as any),
                    endDate: date.parse('2020-06-10', context.currentLocale as any),
                    type: 'planned',
                    category: 2,
                    bomCode: 1,
                    routingCode: 1,
                    productionItems: [
                        {
                            releasedItem: '#Chemical A',
                            releasedQuantity: 1,
                        },
                    ],
                });
                await workOrder.$.save();
                assert.deepEqual(workOrder.$.context.diagnoses, []);
                assert.equal(await workOrder.number, 'WO200001');
                assert.equal(await workOrder.status, 'pending');

                workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
                    site: 2,
                    name: 'Yet another cake',
                    startDate: date.parse('2020-06-10', context.currentLocale as any),
                    endDate: date.parse('2020-06-10', context.currentLocale as any),
                    type: 'planned',
                    category: 2,
                    bomCode: 1,
                    routingCode: 1,
                    productionItems: [
                        {
                            releasedItem: '#Chemical A',
                            releasedQuantity: 1,
                        },
                    ],
                });
                await workOrder.$.save();
                assert.deepEqual(workOrder.$.context.diagnoses, []);
                assert.equal(await workOrder.number, 'WO200002');
                assert.equal(await workOrder.status, 'pending');
            },
            { today: '2020-06-10' },
        ));

    it('Create workOrder node - without finished goods date error', () =>
        Test.withContext(
            async context => {
                const workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
                    site: 2,
                    name: 'Another cake',
                    number: 'WOUS00200001',
                    startDate: date.parse('2020-06-12', context.currentLocale as any),
                    endDate: date.parse('2020-06-10', context.currentLocale as any),
                    type: 'planned',
                    category: 2,
                    bomCode: 1,
                    routingCode: 1,
                    productionItems: [
                        {
                            releasedItem: '#Chemical A',
                            releasedQuantity: 1,
                        },
                    ],
                });
                await assert.isRejected(workOrder.$.save());
                assert.deepEqual(workOrder.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['endDate'],
                        message: 'The date range from 2020-06-12 to 2020-06-10 is not valid.',
                    },
                ]);
            },
            { today: '2020-06-09' },
        ));

    it('Create workOrder node - with finished goods', () =>
        Test.withContext(
            async context => {
                const workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
                    site: 2,
                    name: 'Another cake',
                    number: 'WOUS00200001',
                    startDate: date.parse('2020-06-10', context.currentLocale as any),
                    endDate: date.parse('2020-06-10', context.currentLocale as any),
                    type: 'planned',
                    category: 2,
                    bomCode: 1,
                    routingCode: 1,
                    productionItems: [
                        {
                            releasedItem: '#Chemical A',
                            releasedQuantity: 1,
                        },
                    ],
                });
                await workOrder.$.save();
                assert.deepEqual(workOrder.$.context.diagnoses, []);
                assert.equal(await workOrder.productionItems.length, 1);
                assert.equal((await (await workOrder.productionItems.elementAt(0)).remainingQuantity).valueOf(), 1);
            },
            { today: '2020-06-09' },
        ));

    it('Create workOrder node - with finished goods and components', () =>
        Test.withContext(
            async context => {
                const workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
                    site: 2,
                    name: 'Another cake',
                    number: 'WOUS00200001',
                    startDate: date.parse('2020-06-10', context.currentLocale as any),
                    endDate: date.parse('2020-06-10', context.currentLocale as any),
                    type: 'planned',
                    category: 2,
                    bomCode: 1,
                    routingCode: 1,
                    productionItems: [
                        {
                            releasedItem: '#Chemical A',
                            releasedQuantity: 1,
                        },
                    ],
                    productionComponents: [
                        {
                            componentNumber: 10,
                            item: '#Chemical A',
                            linkQuantity: 100,
                            requiredQuantity: 100,
                            requiredDate: date.today(),
                            unit: { id: 'METER' },
                        },
                    ],
                });
                await workOrder.$.save();
                assert.deepEqual(workOrder.$.context.diagnoses, []);
                assert.equal(await workOrder.productionItems.length, 1);
                assert.equal((await (await workOrder.productionItems.elementAt(0)).remainingQuantity).valueOf(), 1);
                assert.equal(await workOrder.productionComponents.length, 1);
                assert.equal(
                    (await (await workOrder.productionComponents.elementAt(0)).requiredQuantity).valueOf(),
                    100,
                );
            },
            { today: '2020-06-09' },
        ));

    it('Create workOrder node - check material planned cost', () =>
        Test.withContext(
            async context => {
                const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, { _id: 2 });

                const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: 'US001',
                    releasedItem: 'STOBOM',
                    releasedQuantity: 4.5,
                    name: 'multi valuation method',
                    type: 'firm',
                    startDate: date.parse('2024-01-26', context.currentLocale as LocalizeLocale),
                    workOrderCategory,
                    workOrderNumber: 'MULTIVALWO',
                    bom: 'STOBOM',
                });

                assert.deepEqual(workOrder.$.context.diagnoses, []);
                assert.equal(await workOrder.status, 'pending');

                await assertionPlannedCost(context, 'MULTIVALWO', {
                    totalCost: { plannedMaterialCost: 3142.0886, plannedOverheadCost: 0.0, plannedProcessCost: 0.0 },
                    productionComponents: [
                        { componentNumber: 10, plannedCost: 686.736 },
                        { componentNumber: 20, plannedCost: 1082.0246 },
                        { componentNumber: 30, plannedCost: 1373.328 },
                    ],
                    productionOperations: [],
                });
            },
            {
                today: '2023-06-09',
            },
        ));
});

describe('Work order create function', () => {
    const futureDate = date.today().addDays(1);
    it('Create work order with minimum properties - Check properties', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Normal',
            });

            const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                siteId: 'US003',
                releasedItem: '17890-B',
                releasedQuantity: 100,
                name: 'New work order',
                type: 'firm',
                workOrderNumber: 'WOUS003000001',
                workOrderCategory,
                bom: '17890-B',
                route: '17890-B',
            });
            assert.equal(await workOrder?.name, 'New work order');
            assert.equal(await workOrder?.isForwardScheduling, true);
            assert.equal((await workOrder?.startDate)?.toString(), date.today().toString());
            assert.equal(await (await workOrder?.site)?.id, 'US003');
            assert.equal(
                await (
                    await workOrder?.bomCode
                )?.name,
                'Bottle 100 ml Hydro-alcoholic gel for hand antisepsis',
            );
        }));

    it('Create work order with all properties - Check properties', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Normal',
            });

            const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                siteId: 'US003',
                releasedItem: '17890-B',
                releasedQuantity: 100,
                name: 'New work order',
                type: 'firm',
                workOrderNumber: 'WOUS003000001',
                workOrderCategory,
                startDate: futureDate,
                bom: '17890-B',
                route: '17890-B',
            });
            assert.equal(await workOrder.name, 'New work order');
            assert.equal((await workOrder.startDate).toString(), futureDate.toString());
            assert.equal((await workOrder.endDate).toString(), futureDate.toString());
            assert.equal(await workOrder.productionItems.length, 1);
            assert.equal(await workOrder.productionComponents.length, 5);
            assert.equal(await workOrder.productionOperations.length, 4);
            assert.equal(await (await workOrder.site).id, 'US003');
            assert.equal(
                await (
                    await workOrder.bomCode
                )?.name,
                'Bottle 100 ml Hydro-alcoholic gel for hand antisepsis',
            );

            assert.equal((await workOrder.plannedProcessCost).valueOf(), 0.318);
            assert.equal((await workOrder.plannedLaborCost).valueOf(), 0.212);
            assert.equal((await workOrder.plannedMachineCost).valueOf(), 0.106);
            assert.equal((await workOrder.plannedToolCost).valueOf(), 0);
            assert.equal((await workOrder.plannedMaterialCost).valueOf(), 44);

            assert.equal(await (await workOrder.productionComponents.elementAt(0)).isFixedLinkQuantity, false);
            assert.equal(await (await workOrder.productionComponents.elementAt(0)).linkQuantity, 100);
            assert.equal(await (await workOrder.productionComponents.elementAt(0)).scrapFactor, 10);
            assert.equal(await (await workOrder.productionComponents.elementAt(0)).requiredQuantity, 11);

            assert.equal((await (await workOrder.productionItems.elementAt(0)).plannedProcessCost).valueOf(), 0.318);
            assert.equal((await (await workOrder.productionItems.elementAt(0)).plannedLaborCost).valueOf(), 0.212);
            assert.equal((await (await workOrder.productionItems.elementAt(0)).plannedMachineCost).valueOf(), 0.106);
            assert.equal((await (await workOrder.productionItems.elementAt(0)).plannedToolCost).valueOf(), 0);
            assert.equal((await (await workOrder.productionItems.elementAt(0)).plannedMaterialCost).valueOf(), 44);

            const billOfMaterial = (
                await context.query(xtremTechnicalData.nodes.BillOfMaterial, { filter: { item: '#17890-B' } }).toArray()
            )[0];

            assert.deepEqual(await billOfMaterial.components.length, 5);

            await billOfMaterial.components.forEach(async component => {
                const workOrderComponent = await workOrder.productionComponents.find(
                    async woComponent => (await woComponent.componentNumber) === (await component.componentNumber),
                );
                assert.equal(
                    await (
                        await workOrderComponent?.operation
                    )?.operationNumber,
                    await (
                        await component.operation
                    )?.operationNumber,
                );
            });
        }));

    it('Create work order with planned overhead cost - Check properties ', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Normal',
            });

            const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                siteId: 'US003',
                releasedItem: '17891',
                releasedQuantity: 100,
                name: 'New work order',
                type: 'firm',
                workOrderNumber: '',
                workOrderCategory,
                startDate: futureDate,
                bom: '17891',
                route: '17891',
            });
            assert.equal(await workOrder?.name, 'New work order');
            assert.equal((await workOrder?.startDate)?.toString(), futureDate.toString());
            assert.equal((await workOrder?.endDate)?.toString(), futureDate.toString());
            assert.equal(await workOrder?.productionItems.length, 1);
            assert.equal(await workOrder?.productionComponents.length, 4);
            assert.equal(await workOrder?.productionOperations.length, 6);
            assert.equal(await (await workOrder?.site)?.id, 'US003');
            assert.equal(await (await workOrder?.bomCode)?.name, 'Hydro-alcoholic gel 2 for hand antisepsis');
            assert.equal((await workOrder?.plannedOverheadCost)?.valueOf(), 0);
        }));

    it('Create work order with BOM revision not null - Check properties', () =>
        Test.withContext(
            async context => {
                const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                    id: 'Assembly',
                });

                const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: 'US001',
                    releasedItem: 'COST_REV_C',
                    releasedQuantity: 7,
                    name: 'New work order with bom revision',
                    type: 'firm',
                    workOrderNumber: 'TEST WITH BOM REVISION',
                    workOrderCategory,
                    bom: 'COST_REV_C',
                });
                assert.equal(await workOrder.name, 'New work order with bom revision');
                assert.equal(await workOrder.isForwardScheduling, true);
                assert.equal((await workOrder.startDate)?.toString(), date.today().toString());
                assert.equal(await (await workOrder.site)?.id, 'US001');
                assert.equal(await (await (await workOrder.bomCode)?.item)?.id, 'COST_REV_C');
                assert.equal(await (await workOrder.bomRevision)?.revision, 'REV-B');
                assert.deepEqual(await workOrder.productionComponents.length, 3);
                assert.deepEqual(
                    await workOrder.productionComponents
                        .map(async component => ({
                            itemId: await (await component.item)?.id,
                            quantity: await component.requiredQuantity,
                        }))
                        .toArray(),
                    [
                        { itemId: 'COST_REV_F', quantity: 11.5 }, // = fixed qty (10) + scrap (15%)
                        { itemId: 'COST_REV_H', quantity: 28.0 }, // = linkQty (20) / baseQty (5) * releaseQty (7)
                        { itemId: 'COST_REV_I', quantity: 42.0 }, // = linkQty (30) / baseQty (5) * releaseQty (7)
                    ],
                );
            },
            {
                today: '2025-03-28',
                testActiveServiceOptions: [xtremMasterData.serviceOptions.billOfMaterialRevisionServiceOption],
            },
        ));

    it('Create work order with BOM revision = null - Check properties', () =>
        Test.withContext(
            async context => {
                const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                    id: 'Assembly',
                });

                const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: 'US001',
                    releasedItem: 'COST_REV_C',
                    releasedQuantity: 7,
                    name: 'New work order with bom revision',
                    type: 'firm',
                    workOrderNumber: 'TEST WITH BOM REVISION',
                    workOrderCategory,
                    bom: 'COST_REV_C',
                });
                assert.equal(await workOrder.name, 'New work order with bom revision');
                assert.equal(await workOrder.isForwardScheduling, true);
                assert.equal((await workOrder.startDate)?.toString(), date.today().toString());
                assert.equal(await (await workOrder.site)?.id, 'US001');
                assert.equal(await (await (await workOrder.bomCode)?.item)?.id, 'COST_REV_C');
                assert.isNull(await workOrder.bomRevision);
                assert.deepEqual(await workOrder.productionComponents.length, 3);
                assert.deepEqual(
                    await workOrder.productionComponents
                        .map(async component => ({
                            itemId: await (await component.item)?.id,
                            quantity: await component.requiredQuantity,
                        }))
                        .toArray(),
                    [
                        { itemId: 'COST_REV_F', quantity: 1.1 }, // fixed qty (10) + scrap (10%)
                        { itemId: 'COST_REV_H', quantity: 2.8 }, // = linkQty (2) / baseQty (5) * releaseQty (7)
                        { itemId: 'COST_REV_I', quantity: 4.2 }, // = linkQty (3) / baseQty (5) * releaseQty (7)
                    ],
                );
            },
            {
                today: '2025-03-26', // first revision starts on 2025-03-27
                testActiveServiceOptions: [xtremMasterData.serviceOptions.billOfMaterialRevisionServiceOption],
            },
        ));

    it('Create work order with invalid mandatory properties - Check properties', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Normal',
            });

            await assert.isRejected(
                xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: '',
                    releasedItem: '',
                    releasedQuantity: -10,
                    name: 'New work order',
                    type: 'firm',
                    workOrderNumber: '',
                    workOrderCategory,
                    startDate: futureDate,
                    bom: 'BOM',
                    route: 'ROUTING',
                }),
                "Site ID missing, ReleasedItem ID missing, Release quantity '-10' is invalid",
            );
        }));

    it('Create work order with invalid BOM item ID - Check properties', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Assembly',
            });

            await assert.isRejected(
                xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: 'US003',
                    releasedItem: '17890-B',
                    releasedQuantity: 100,
                    name: 'New work order',
                    type: 'planned',
                    workOrderNumber: 'WOUS003000001',
                    workOrderCategory,
                    bom: 'BOM',
                }),
                'Item: record not found: {"id":"BOM"}',
            );
        }));

    it('Create work order with invalid routing item id - Check properties', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Rework',
            });

            await assert.isRejected(
                xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: 'US003',
                    releasedItem: '17890-B',
                    releasedQuantity: 100,
                    name: 'New work order',
                    type: 'planned',
                    workOrderNumber: 'WOUS003000001',
                    workOrderCategory,
                    route: 'ROUTING',
                }),
                'Item: record not found: {"id":"ROUTING"}',
            );
        }));

    it('Create work order with invalid BOM ID - Check properties', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Assembly',
            });
            const item_id = (await context.read(xtremMasterData.nodes.Item, { id: 'SC100' }))._id;
            await assert.isRejected(
                xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: 'US003',
                    releasedItem: '17890-B',
                    releasedQuantity: 100,
                    name: 'New work order',
                    type: 'planned',
                    workOrderNumber: 'WOUS003000001',
                    workOrderCategory,
                    bom: 'SC100',
                }),
                `BillOfMaterial: record not found: {"site":5,"item":${item_id}}`,
            );
        }));

    it('Create work order with invalid routing ID - Check properties', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Rework',
            });
            const item_id = (await context.read(xtremMasterData.nodes.Item, { id: 'SC100' }))._id;
            await assert.isRejected(
                xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: 'US003',
                    releasedItem: '17890-B',
                    releasedQuantity: 100,
                    name: 'New work order',
                    type: 'planned',
                    workOrderNumber: 'WOUS003000001',
                    workOrderCategory,
                    route: 'SC100',
                }),
                `Routing: record not found: {"site":5,"item":${item_id}}`,
            );
        }));
});

describe('Control production step of a work order', () => {
    it('Exclude unique resource of production step operation', () =>
        Test.withContext(async context => {
            const WO = 'WO2021US0030005';
            const operation = 20;

            const workOrder1 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const operationLine1 = await workOrder1.productionOperations.elementAt(3);
            assert.equal(await operationLine1.operationNumber, operation);
            assert.equal(await operationLine1.isProductionStep, true);
            assert.equal(await operationLine1.status, 'pending');
            assert.equal(await operationLine1.resources.length, 1);
            assert.equal(await (await operationLine1.resources.elementAt(0)).status, 'pending');
            assert.equal(await (await operationLine1.resources.elementAt(0)).isResourceQuantity, true);

            await (
                await (await workOrder1.productionOperations.elementAt(3)).resources.elementAt(0)
            ).$.set({
                status: 'excluded',
            });
            await assert.isRejected(workOrder1.$.save());

            assert.deepEqual(workOrder1.$.context.diagnoses, [
                {
                    message: 'At least one operation with a production step is required.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));

    it('Forbid multiple production step operations', () =>
        Test.withContext(async context => {
            const WO = 'WO2021US0030005';
            const operation = 10;

            const workOrder1 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const operationLine1 = await workOrder1.productionOperations.elementAt(1);
            assert.equal(await operationLine1.operationNumber, operation);
            assert.equal(await operationLine1.isProductionStep, false);
            assert.equal(await operationLine1.status, 'pending');

            await (await workOrder1.productionOperations.elementAt(1)).$.set({ isProductionStep: true });
            await assert.isRejected(workOrder1.$.save());

            assert.deepEqual(workOrder1.$.context.diagnoses, [
                {
                    message: 'Only one operation with a production step is allowed.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));

    it('Close completed work order with average cost product', () =>
        Test.withContext(async context => {
            const WO = 'WO-COMPLETED-AVC';
            const initialWipCosts: Parameters<typeof assertionWipCosts>[2] = [
                { type: 'productionTracking', cost: 86.4, amount: 86.4, quantity: 1.0 },
                { type: 'materialTracking', cost: 14.4, amount: 172.8, quantity: 12.0 },
                { type: 'materialTracking', cost: 14.4, amount: 43.2, quantity: 3.0 },
                { type: 'materialTracking', cost: 14.4, amount: 43.2, quantity: 3.0 },
                { type: 'productionTracking', cost: 86.4, amount: 172.8, quantity: 2.0 },
                { type: 'materialTracking', cost: 14.4, amount: 14.4, quantity: 1.0 },
                { type: 'materialTracking', cost: 14.4, amount: 14.4, quantity: 1.0 },
                { type: 'materialTracking', cost: 14.4, amount: 14.4, quantity: 1.0 },
            ];

            const workOrder = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const productionTrackingLines = await context
                .query(xtremManufacturing.nodes.ProductionTrackingLine, {
                    filter: { workOrderLine: { document: { number: WO } } },
                })
                .toArray();
            assert.deepEqual(productionTrackingLines.length, 2);

            await assertionWipCosts(context, { filter: { workOrder } }, initialWipCosts);

            await xtremManufacturing.nodes.WorkOrder.closeWorkOrder(context, workOrder, date.today());
            // Test that the notify method is called
            const closeResult = await xtremManufacturing.nodes.WorkOrder.setWorkOrderClosed(context, {
                _id: workOrder._id,
                trackingId: '1234',
            });

            assert.equal(await workOrder.status, 'completed');
            assert.equal(await (await workOrder.productionItem)?.lineStatus, 'completed');

            const releaseItemPostResult = JSON.parse(
                closeResult,
            ) as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum.correction>;

            assert.deepEqual(releaseItemPostResult.result, 'requested');
            if (releaseItemPostResult.result !== 'requested') return;
            assert.deepEqual(releaseItemPostResult.documents.length, 2);
            assert.deepEqual(releaseItemPostResult.documents[0].lines.length, 1);
            assert.deepEqual(releaseItemPostResult.documents[1].lines.length, 1);

            const stockCorrectionDetails = await context
                .query(xtremStockData.nodes.StockCorrectionDetail, {
                    filter: { documentLine: { _in: productionTrackingLines.map(line => line._id) } },
                    orderBy: { _id: 1 },
                })
                .toArray();

            assert.equal(stockCorrectionDetails.length, 2);

            assert.deepEqual(await stockCorrectionDetails[0].amountToAbsorb, 14.4);
            assert.deepEqual(await stockCorrectionDetails[0].impactedQuantity, 1.0);
            assert.deepEqual(await (await stockCorrectionDetails[0].reasonCode).id, 'Stock value correction');
            assert.deepEqual(await stockCorrectionDetails[1].amountToAbsorb, 28.8);
            assert.deepEqual(await stockCorrectionDetails[1].impactedQuantity, 2.0);
            assert.deepEqual(await (await stockCorrectionDetails[1].reasonCode).id, 'Stock value correction');
        }));

    it('Close completed work order with FIFO cost product', () =>
        Test.withContext(async context => {
            const WO = 'WO2024US0010005';
            const initialWipCosts: Parameters<typeof assertionWipCosts>[2] = [
                { type: 'setupTimeTracking', cost: 1.4364, amount: 28.728, quantity: 20.0 },
                { type: 'runTimeTracking', cost: 6.2244, amount: 3.7346, quantity: 0.6 },
                { type: 'setupTimeTracking', cost: 2.0, amount: 30.0, quantity: 15.0 },
                { type: 'runTimeTracking', cost: 180.0, amount: 594.0, quantity: 3.3 },
                { type: 'setupTimeTracking', cost: 0.052, amount: 0.52, quantity: 10.0 },
                { type: 'runTimeTracking', cost: 2.12, amount: 7.632, quantity: 3.6 },
                { type: 'materialTracking', cost: 114.4433, amount: 343.33, quantity: 3.0 },
                { type: 'productionTracking', cost: 322.1577, amount: 966.47, quantity: 3.0 },
                { type: 'materialTracking', cost: 114.44, amount: 114.44, quantity: 1.0 },
                { type: 'setupTimeTracking', cost: 2.0, amount: 20.0, quantity: 10.0 },
                { type: 'runTimeTracking', cost: 180.0, amount: 36.0, quantity: 0.2 },
                { type: 'setupTimeTracking', cost: 0.052, amount: 0.52, quantity: 10.0 },
                { type: 'runTimeTracking', cost: 2.12, amount: 0.424, quantity: 0.2 },
                { type: 'setupTimeTracking', cost: 1.4364, amount: 0.1436, quantity: 0.1 },
            ];

            const workOrder = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const productionTrackingLines = await context
                .query(xtremManufacturing.nodes.ProductionTrackingLine, {
                    filter: { workOrderLine: { document: { number: WO } } },
                })
                .toArray();
            assert.deepEqual(productionTrackingLines.length, 1);

            await assertionWipCosts(context, { filter: { workOrder } }, initialWipCosts);

            await xtremManufacturing.nodes.WorkOrder.closeWorkOrder(context, workOrder, date.today());
            const closeResult = await xtremManufacturing.nodes.WorkOrder.setWorkOrderClosed(context, {
                _id: workOrder._id,
                trackingId: '1234',
            });

            assert.equal(await workOrder.status, 'completed');
            assert.equal(await (await workOrder.productionItem)?.lineStatus, 'completed');

            const releaseItemPostResult = JSON.parse(
                closeResult,
            ) as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum.correction>;

            assert.deepEqual(releaseItemPostResult.result, 'requested');
            if (releaseItemPostResult.result !== 'requested') return;
            assert.deepEqual(releaseItemPostResult.documents.length, 1);
            assert.deepEqual(releaseItemPostResult.documents[0].lines.length, 1);

            const stockCorrectionDetails = await context
                .query(xtremStockData.nodes.StockCorrectionDetail, {
                    filter: { documentLine: { _in: productionTrackingLines.map(line => line._id) } },
                    orderBy: { _id: 1 },
                })
                .toArray();

            assert.equal(stockCorrectionDetails.length, 1);

            assert.deepEqual(await stockCorrectionDetails[0].amountToAbsorb, 213.0);
            assert.deepEqual(await stockCorrectionDetails[0].impactedQuantity, 3.0);
            assert.deepEqual(await (await stockCorrectionDetails[0].reasonCode).id, 'Stock value correction');
        }));

    it('Close completed work order with standard cost product', () =>
        Test.withContext(async context => {
            const WO = 'WO2024US0010006';
            const initialWipCosts: Parameters<typeof assertionWipCosts>[2] = [
                { type: 'setupTimeTracking', cost: 2.0, amount: 30.0, quantity: 15.0 },
                { type: 'runTimeTracking', cost: 180.0, amount: 594.0, quantity: 3.3 },
                { type: 'runTimeTracking', cost: 2.12, amount: 7.632, quantity: 3.6 },
                { type: 'setupTimeTracking', cost: 0.0239, amount: 0.4788, quantity: 20.0 },
                { type: 'runTimeTracking', cost: 6.2244, amount: 3.7346, quantity: 0.6 },
                { type: 'materialTracking', cost: 114.7333, amount: 481.88, quantity: 4.2 },
                { type: 'materialTracking', cost: 123.0, amount: 738.0, quantity: 6.0 },
                { type: 'productionTracking', cost: 123.45, amount: 370.35, quantity: 3.0 },
                { type: 'materialTracking', cost: 114.79, amount: 114.79, quantity: 1.0 },
                { type: 'materialTracking', cost: 123.0, amount: 123.0, quantity: 1.0 },
                { type: 'materialTracking', cost: 114.44, amount: 114.44, quantity: 1.0 },
                { type: 'setupTimeTracking', cost: 2.0, amount: 20.0, quantity: 10.0 },
                { type: 'runTimeTracking', cost: 180.0, amount: 3.6, quantity: 0.02 },
                { type: 'setupTimeTracking', cost: 0.052, amount: 0.52, quantity: 10.0 },
                { type: 'runTimeTracking', cost: 2.12, amount: 0.424, quantity: 0.2 },
                { type: 'setupTimeTracking', cost: 0.0239, amount: 0.2394, quantity: 10.0 },
                { type: 'runTimeTracking', cost: 6.2244, amount: 1.2449, quantity: 0.2 },
            ];

            const workOrder = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            const productionTrackingLines = await context
                .query(xtremManufacturing.nodes.ProductionTrackingLine, {
                    filter: { workOrderLine: { document: { number: WO } } },
                })
                .toArray();
            assert.deepEqual(productionTrackingLines.length, 1);

            await assertionWipCosts(context, { filter: { workOrder } }, initialWipCosts);

            await xtremManufacturing.nodes.WorkOrder.closeWorkOrder(context, workOrder, date.today());
            const closeResult = await xtremManufacturing.nodes.WorkOrder.setWorkOrderClosed(context, {
                _id: workOrder._id,
                trackingId: '1234',
            });

            assert.equal(await workOrder.status, 'completed');
            assert.equal(await (await workOrder.productionItem)?.lineStatus, 'completed');

            const releaseItemPostResult = JSON.parse(
                closeResult,
            ) as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum.correction>;

            assert.deepEqual(releaseItemPostResult.result, 'requested');
            if (releaseItemPostResult.result !== 'requested') return;
            assert.deepEqual(releaseItemPostResult.documents.length, 1);
            assert.deepEqual(releaseItemPostResult.documents[0].lines.length, 1);

            const stockCorrectionDetails = await context
                .query(xtremStockData.nodes.StockCorrectionDetail, {
                    filter: { documentLine: { _in: productionTrackingLines.map(line => line._id) } },
                    orderBy: { _id: 1 },
                })
                .toArray();

            assert.equal(stockCorrectionDetails.length, 1);

            assert.deepEqual(await stockCorrectionDetails[0].amountToAbsorb, -526.4);
            assert.deepEqual(await stockCorrectionDetails[0].impactedQuantity, 3.0);
            assert.deepEqual(await (await stockCorrectionDetails[0].reasonCode).id, 'Stock value correction');
        }));

    it('Update work order planned costs', () =>
        Test.withContext(async context => {
            const WO = 'WO2021US0030005';

            const workOrder1 = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: WO },
                { forUpdate: true },
            );
            await assertionPlannedCost(context, WO, {
                totalCost: { plannedMaterialCost: 44.4, plannedOverheadCost: 0.0, plannedProcessCost: 0.318 },
            });

            await (await workOrder1.productionComponents.elementAt(0)).$.set({ requiredQuantity: 100 });
            await workOrder1.$.save();

            await xtremManufacturing.nodes.WorkOrder.updatePlannedCosts(context, workOrder1);
            await assertionPlannedCost(context, WO, {
                totalCost: { plannedMaterialCost: 84.0, plannedOverheadCost: 0.0, plannedProcessCost: 0.318 },
            });
        }));

    it('Update work order dimensions on released item and inherit on operations and components', () =>
        Test.withContext(async context => {
            const futureDate = date.today().addDays(1);
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Assembly',
            });
            const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                siteId: 'US003',
                releasedItem: '17890-B',
                releasedQuantity: 100,
                name: 'New work order',
                type: 'firm',
                workOrderNumber: 'WOUS003000001',
                workOrderCategory,
                startDate: futureDate,
                bom: '17890-B',
                route: '17890-B',
                storedAttributes: { project: 'AttPROJ', task: '', employee: '' },
                storedDimensions: {
                    dimensionType01: '300',
                    dimensionType02: 'CHANNELVALUE1',
                    dimensionType03: 'DIMTYPE1VALUE1',
                    dimensionType04: 'DIMTYPE2VALUE2',
                    dimensionType05: 'COMMERCIAL',
                },
            });

            await (
                await workOrder.productionItems.elementAt(0)
            ).$.set({
                storedAttributes: { project: 'AttPROJ2', task: '', employee: '' },
                storedDimensions: {
                    dimensionType03: 'WAREHOUSE',
                    dimensionType05: 'RETAIL',
                },
            });

            await workOrder.$.set({ doInheritDimensions: true });
            await workOrder.$.save();
            const workOrder1 = await context.read(xtremManufacturing.nodes.WorkOrder, {
                site: '#US003',
                number: 'WOUS003000001',
            });

            // components
            const componentAttributesLine1 = (await workOrder1.productionComponents.elementAt(0)).storedAttributes;
            const componentDimensionsLine1 = (await workOrder1.productionComponents.elementAt(0)).storedDimensions;

            // operations
            const operationAttributesLine1 = (
                await (await workOrder1.productionOperations.elementAt(0)).resources.elementAt(0)
            ).storedAttributes;

            const operationDimensionsLine1 = (
                await (await workOrder1.productionOperations.elementAt(0)).resources.elementAt(0)
            ).storedDimensions;

            const expectedAttributes = { task: '', project: 'AttPROJ2', employee: '' };

            const expectedDimensions = {
                dimensionType03: 'WAREHOUSE',
                dimensionType05: 'RETAIL',
            };

            assert.equal(JSON.stringify(await componentAttributesLine1), JSON.stringify(expectedAttributes));
            assert.equal(JSON.stringify(await componentDimensionsLine1), JSON.stringify(expectedDimensions));
            assert.equal(JSON.stringify(await operationAttributesLine1), JSON.stringify(expectedAttributes));
            assert.equal(JSON.stringify(await operationDimensionsLine1), JSON.stringify(expectedDimensions));
        }));

    it('Update work order dimensions released items and test inheritance on component', () =>
        Test.withContext(async context => {
            const futureDate = date.today().addDays(1);
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Assembly',
            });
            const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                siteId: 'US003',
                releasedItem: '17890-B',
                releasedQuantity: 100,
                name: 'New work order',
                type: 'firm',
                workOrderNumber: 'WOUS003000001',
                workOrderCategory,
                startDate: futureDate,
                bom: '17890-B',
                route: '17890-B',
                storedAttributes: { project: 'AttPROJ', task: '', employee: '' },
                storedDimensions: {
                    dimensionType01: '300',
                    dimensionType02: 'CHANNELVALUE1',
                    dimensionType03: 'DIMTYPE1VALUE1',
                    dimensionType04: 'DIMTYPE2VALUE2',
                    dimensionType05: 'COMMERCIAL',
                },
            });

            // components
            const componentAttributesLine1 = (await workOrder.productionComponents.elementAt(0)).storedAttributes;
            const componentDimensionsLine1 = (await workOrder.productionComponents.elementAt(0)).storedDimensions;

            // operations
            const operationAttributesLine1 = (
                await (await workOrder.productionOperations.elementAt(0)).resources.elementAt(0)
            ).storedAttributes;

            const operationDimensionsLine1 = (
                await (await workOrder.productionOperations.elementAt(0)).resources.elementAt(0)
            ).storedDimensions;

            const expectedAttributes = { project: 'AttPROJ', task: '', employee: '' };

            const expectedDimensions = {
                dimensionType01: '300',
                dimensionType02: 'CHANNELVALUE1',
                dimensionType03: 'DIMTYPE1VALUE1',
                dimensionType04: 'DIMTYPE2VALUE2',
                dimensionType05: 'COMMERCIAL',
            };

            assert.equal(JSON.stringify(await componentAttributesLine1), JSON.stringify(expectedAttributes));
            assert.equal(JSON.stringify(await componentDimensionsLine1), JSON.stringify(expectedDimensions));
            assert.equal(JSON.stringify(await operationAttributesLine1), JSON.stringify(expectedAttributes));
            assert.equal(JSON.stringify(await operationDimensionsLine1), JSON.stringify(expectedDimensions));
        }));

    it('Update work order planned costs with components using different valuation methods', () =>
        Test.withContext(
            async context => {
                const WO = 'WO2024US0010004';

                await assertionPlannedCost(context, WO, {
                    totalCost: { plannedMaterialCost: 483.444, plannedOverheadCost: 0.0, plannedProcessCost: 0.0 },
                    productionComponents: [
                        { componentNumber: 10, plannedCost: 228.912 },
                        { componentNumber: 20, plannedCost: 369.0 },
                        { componentNumber: 30, plannedCost: 114.444 },
                    ],
                    productionOperations: [],
                });

                const workOrder1 = await context.read(
                    xtremManufacturing.nodes.WorkOrder,
                    { number: WO },
                    { forUpdate: true },
                );

                // Add an operation + modify quantity of a component + add a component
                await workOrder1.$.set({
                    productionOperations: [
                        {
                            _action: 'create',
                            isAdded: true,
                            isProductionStep: true,
                            minCapabilityLevel: '#Master',
                            name: 'added operation',
                            operationNumber: 999,
                            plannedQuantity: 1.5,
                            // different unit between runTimeUnit and setupTimeUnit to check units are well considered
                            runTimeUnit: '#HOUR',
                            setupTimeUnit: '#MINUTE',
                            status: 'pending',
                            resources: [
                                {
                                    _action: 'create',
                                    expectedRunTime: 1.25,
                                    expectedSetupTime: 10,
                                    isAdded: true,
                                    isResourceQuantity: true,
                                    resource: '#AFILM100L|US001',
                                    status: 'pending',
                                },
                            ],
                        },
                    ],
                    productionComponents: [
                        {
                            _action: 'update',
                            componentNumber: 20,
                            requiredQuantity: 5, // 3 -> 5
                        },
                        {
                            _action: 'create',
                            allocationRequestStatus: 'noRequest',
                            componentNumber: 999,
                            consumedQuantity: 0,
                            isAdded: true,
                            isFixedLinkQuantity: false,
                            item: '#STOFIFO-MANUF',
                            lineStatus: 'pending',
                            lineType: 'normal',
                            linkQuantity: 2,
                            name: 'STOFIFO Manuf',
                            operation: null,
                            requiredQuantity: 2,
                            scrapFactor: 0,
                            unit: '#MINUTE',
                            requiredDate: date.today(),
                        },
                    ],
                });

                await workOrder1.$.save();

                // The planned cost is not modified when the WO is modified
                await assertionPlannedCost(context, WO, {
                    totalCost: { plannedMaterialCost: 483.444, plannedOverheadCost: 0.0, plannedProcessCost: 0.0 },
                    productionComponents: [
                        { componentNumber: 10, plannedCost: 228.912 },
                        { componentNumber: 20, plannedCost: 369.0 },
                        { componentNumber: 30, plannedCost: 114.444 },
                        { componentNumber: 999, plannedCost: 228.912 },
                    ],
                    productionOperations: [
                        {
                            workOrderOperation: { operationNumber: 999 },
                            resource: { id: 'AFILM100L' },
                            expectedSetupCost: 0.52,
                            expectedRunCost: 2.65,
                        },
                    ],
                });

                await xtremManufacturing.nodes.WorkOrder.updatePlannedCosts(context, workOrder1);

                await assertionPlannedCost(context, WO, {
                    totalCost: { plannedMaterialCost: 944.4808, plannedOverheadCost: 0.0, plannedProcessCost: 3.17 },
                    productionComponents: [
                        { componentNumber: 10, plannedCost: 228.912 },
                        { componentNumber: 20, plannedCost: 601.1248 },
                        { componentNumber: 30, plannedCost: 114.444 },
                        { componentNumber: 999, plannedCost: 228.912 },
                    ],
                    productionOperations: [
                        {
                            workOrderOperation: { operationNumber: 999 },
                            resource: { id: 'AFILM100L' },
                            expectedSetupCost: 0.52,
                            expectedRunCost: 2.65,
                        },
                    ],
                });

                // simulate stock value changes
                //  - component 10
                const fifoTier = await context.read(
                    xtremStockData.nodes.FifoValuationTier,
                    { _id: 220900 },
                    { forUpdate: true },
                );
                await fifoTier.$.set({ remainingQuantity: 0 });
                await fifoTier.$.save();

                //  - component 20
                const itemSite = await context.read(
                    xtremMasterData.nodes.ItemSite,
                    { item: '#STOAVC', site: '#US001' },
                    { forUpdate: true },
                );
                await itemSite.$.set({ stockValuationAtAverageCost: 5000 }); // 12300 --> 5000
                await itemSite.$.save();

                //  - component 30
                // cost will be modified by changing the required date

                //  - component 999
                await context.bulkUpdate(
                    xtremStockData.nodes.FifoValuationTier,

                    { set: { remainingQuantity: 0 }, where: { item: '#STOFIFO-MANUF' } },
                );
                await fifoTier.$.set({ remainingQuantity: 0 });
                await fifoTier.$.save();

                // Change requiredDate
                const newRequiredDate = date.make(2023, 12, 15);
                await workOrder1.$.set({ startDate: newRequiredDate });
                await workOrder1.$.save();
                // Check new date on components
                const workOrder2 = await context.read(
                    xtremManufacturing.nodes.WorkOrder,
                    { number: WO },
                    { forUpdate: true },
                );

                await workOrder2.productionComponents.forEach(async (component, index) => {
                    if (index === 0) {
                        // As the first component is excluded, update is forbidden => the required date is not modified
                        assert.deepEqual(await component.requiredDate, date.make(2024, 2, 2));
                    } else {
                        assert.deepEqual(await component.requiredDate, newRequiredDate);
                    }
                });

                // Check price has not been modified
                await assertionPlannedCost(context, WO, {
                    totalCost: { plannedMaterialCost: 944.4808, plannedOverheadCost: 0.0, plannedProcessCost: 3.17 },
                    productionComponents: [
                        { componentNumber: 10, plannedCost: 228.912 },
                        { componentNumber: 20, plannedCost: 601.1248 },
                        { componentNumber: 30, plannedCost: 114.444 },
                        { componentNumber: 999, plannedCost: 228.912 },
                    ],
                    productionOperations: [
                        {
                            workOrderOperation: { operationNumber: 999 },
                            resource: { id: 'AFILM100L' },
                            expectedSetupCost: 0.52,
                            expectedRunCost: 2.65,
                        },
                    ],
                });

                // update planned cost
                await xtremManufacturing.nodes.WorkOrder.updatePlannedCosts(context, workOrder1);

                await assertionPlannedCost(context, WO, {
                    totalCost: { plannedMaterialCost: 356.0514, plannedOverheadCost: 0.0, plannedProcessCost: 3.17 },
                    productionComponents: [
                        { componentNumber: 10, plannedCost: 228.912 },
                        { componentNumber: 20, plannedCost: 242.7184 },
                        { componentNumber: 30, plannedCost: 113.333 },
                        { componentNumber: 999, plannedCost: 0.0 },
                    ],
                    productionOperations: [
                        {
                            workOrderOperation: { operationNumber: 999 },
                            resource: { id: 'AFILM100L' },
                            expectedSetupCost: 0.52,
                            expectedRunCost: 2.65,
                        },
                    ],
                });
            },
            {
                today: '2023-12-01',
            },
        ));

    // TODO: Update the test to take account of the new stock service
    it.skip('Check the work order status functionality', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Normal',
            });

            const woNumber = 'WOTEST1';
            // Create the work order
            const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                siteId: 'US001',
                releasedItem: 'SM1',
                releasedQuantity: 20,
                name: 'Simple made item',
                type: 'firm',
                workOrderNumber: woNumber,
                workOrderCategory,
                bom: 'SM1',
                route: 'SM1',
            });
            assert.equal(await workOrder.status, 'pending');

            // Do a material tracking for some of the component
            const result = await xtremManufacturing.nodes.MaterialTracking.createMaterialTrackings(
                context,
                workOrder,
                'WOTTESTC1',
                10,
            );

            assert.isNull(result);
            const workOrder1 = await context.read(xtremManufacturing.nodes.WorkOrder, { number: woNumber });
            assert.equal(await (await workOrder1?.productionComponents?.elementAt(0))?.lineStatus, 'inProgress');
            assert.equal(await workOrder1.status, 'inProgress');

            // Do a time tracking for all of the time
            const result1 = await xtremManufacturing.nodes.OperationTracking.createOperationTrackings(
                context,
                workOrder,
                'WOTTESTO1',
                20,
                date.today(),
                10,
            );

            assert.isNull(result1);
            const workOrder2 = await context.read(xtremManufacturing.nodes.WorkOrder, { number: woNumber });
            assert.equal(await (await workOrder2?.productionOperations?.elementAt(0))?.status, 'completed');
            assert.equal(await workOrder2.status, 'inProgress');

            // Do the rest of the material tracking
            const result2 = await xtremManufacturing.nodes.MaterialTracking.createMaterialTrackings(
                context,
                workOrder,
                'WOTTESTC2',
                30,
            );

            assert.isNull(result2);
            const workOrder3 = await context.read(xtremManufacturing.nodes.WorkOrder, { number: woNumber });
            assert.equal(await (await workOrder3?.productionComponents.elementAt(0))?.lineStatus, 'completed');
            assert.equal(await workOrder3.status, 'inProgress');

            // Do a production tracking
            const status = await context.read(xtremStockData.nodes.StockStatus, { id: 'A' });
            const location = await context.read(xtremMasterData.nodes.Location, {
                id: 'LOC1',
                locationZone: '#US001|Loading dock',
            });

            const result4 = await xtremManufacturing.nodes.ProductionTracking.createWorkOrderTracking(context, {
                itemCode: await (await (await workOrder3.productionItem)!.releasedItem).id,
                workOrderNumber: await workOrder.number,
                workOrderTrackingNumber: 'WOTTESTP1',
                trackingQuantity: 10,
                stockDetails: [
                    {
                        quantityInStockUnit: 10,
                        stockUnit: await (await (await workOrder3.productionItem)!.releasedItem).stockUnit,
                        status,
                        location,
                        owner: '',
                    },
                ],
            });

            assert.isNotNull(result4);
            const workOrder4 = await context.read(xtremManufacturing.nodes.WorkOrder, { number: woNumber });
            assert.equal(await (await workOrder4?.productionItem)?.lineStatus, 'inProgress');
            assert.equal(await workOrder4.status, 'inProgress');

            // Do the final production tracking
            const result5 = await xtremManufacturing.nodes.ProductionTracking.createWorkOrderTracking(context, {
                itemCode: await (await (await workOrder3.productionItem)!.releasedItem).id,
                workOrderNumber: await workOrder.number,
                workOrderTrackingNumber: 'WOTTESTP2',
                trackingQuantity: 10,
                stockDetails: [
                    {
                        quantityInStockUnit: 10,
                        stockUnit: await (await (await workOrder3.productionItem)!.releasedItem).stockUnit,
                        status,
                        location,
                        owner: '',
                    },
                ],
            });

            assert.isNotNull(result5);
            const workOrder5 = await context.read(xtremManufacturing.nodes.WorkOrder, { number: woNumber });
            assert.equal(await (await workOrder5?.productionItem)?.lineStatus, 'completed');
            assert.equal(await workOrder5.status, 'completed');
        }));
});

describe('Work order create function - with only BOM or routing', () => {
    const futureDate = date.today().addDays(1);
    it('Create work order with only BOM - Check properties', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Assembly',
            });

            const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                siteId: 'US003',
                releasedItem: '17890-B',
                releasedQuantity: 100,
                name: 'New work order',
                type: 'firm',
                workOrderNumber: 'WOUS003000001',
                workOrderCategory,
                bom: '17890-B',
            });
            assert.equal(await workOrder?.name, 'New work order');
            assert.equal(await workOrder?.isForwardScheduling, true);
            assert.equal((await workOrder?.startDate)?.toString(), date.today().toString());
            assert.equal(await (await workOrder?.site)?.id, 'US003');
            assert.equal(
                await (
                    await workOrder?.bomCode
                )?.name,
                'Bottle 100 ml Hydro-alcoholic gel for hand antisepsis',
            );
            assert.isNull(await workOrder?.routingCode);
            assert.equal(
                await (
                    await (
                        await workOrder.productionComponents.elementAt(0)
                    ).operation
                )?.operationNumber,
                undefined,
            );
        }));

    it('Create work order with only routing - Check properties', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Rework',
            });

            const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                siteId: 'US003',
                releasedItem: '17890-B',
                releasedQuantity: 100,
                name: 'New work order',
                type: 'firm',
                workOrderNumber: 'WOUS003000001',
                workOrderCategory,
                startDate: futureDate,
                route: '17890-B',
            });
            assert.equal(await workOrder.name, 'New work order');
            assert.equal((await workOrder.startDate).toString(), futureDate.toString());
            assert.equal((await workOrder.endDate).toString(), futureDate.toString());
            assert.equal(await workOrder.productionItems.length, 1);
            assert.equal(await workOrder.productionComponents.length, 0);
            assert.equal(await workOrder.productionOperations.length, 4);
            assert.equal(await (await workOrder.site).id, 'US003');
            assert.equal(
                await (
                    await workOrder.routingCode
                )?.name,
                'Bottle 100 ml Hydro-alcoholic gel for hand antisepsis',
            );
            assert.equal(await (await workOrder.productionOperations.elementAt(0))?.operationNumber, 5);
        }));

    it('Create work order without BOM and routing - Check properties', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Normal',
            });

            await assert.isRejected(
                xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: 'US003',
                    releasedItem: '17890-B',
                    releasedQuantity: 100,
                    name: 'New work order',
                    type: 'planned',
                    workOrderNumber: 'WOUS003000001',
                    workOrderCategory,
                }),
                'Assign a routing or bill of material to this work order.',
            );
        }));

    it('Create work order with only BOM then add routing then fail to remove routing - Check properties', () =>
        Test.withContext(
            async context => {
                const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                    id: 'Assembly',
                });
                const date0 = date.parse('2020-06-10', context.currentLocale as LocalizeLocale);
                const date1 = date.parse('2020-06-11', context.currentLocale as LocalizeLocale);
                const date2 = date.parse('2020-06-12', context.currentLocale as LocalizeLocale);

                let workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: 'US003',
                    releasedItem: '17890-B',
                    releasedQuantity: 100,
                    startDate: date0,
                    name: 'New work order',
                    type: 'planned',
                    workOrderNumber: 'WOUS003000001',
                    workOrderCategory,
                    bom: '17890-B',
                });
                assert.equal(date0.compare(await workOrder.requestedDate), 0);
                assert.equal(date0.compare(await workOrder.startDate), 0);
                assert.equal(date0.addDays(2).compare(await workOrder.endDate), 0); // 2 days = prodLeadTime for released item
                assert.equal(await workOrder.schedulingStatus, 'notManaged');

                workOrder = await context.read(
                    xtremManufacturing.nodes.WorkOrder,
                    { _id: workOrder._id },
                    { forUpdate: true },
                );
                await workOrder.$.set({ startDate: date1, endDate: date2 });
                await workOrder.$.save();
                assert.deepEqual(workOrder.$.context.diagnoses, []);
                assert.equal(date0.compare(await workOrder.requestedDate), 0);
                assert.equal(date1.compare(await workOrder.startDate), 0);
                assert.equal(date2.compare(await workOrder.endDate), 0);
                assert.equal(await workOrder.schedulingStatus, 'notManaged');

                const item = await (await workOrder.bomCode)?.item;
                const site = await (await workOrder.bomCode)?.site;
                assert.isDefined(item);
                assert.isDefined(site);
                const workOrder2 = await xtremManufacturing.nodes.WorkOrder.addOperationsToWorkOrder(
                    context,
                    workOrder,
                    item,
                    site,
                );
                assert.equal(await (await workOrder2.productionOperations.elementAt(0))?.operationNumber, 5);
                assert.equal(date0.compare(await workOrder2.requestedDate), 0);
                assert.equal(date0.compare(await workOrder2.startDate), 0);
                assert.equal(date0.compare(await workOrder2.endDate), 0);
                assert.equal(await workOrder2.schedulingStatus, 'notScheduled');

                const workOrder3 = await context.tryRead(
                    xtremManufacturing.nodes.WorkOrder,
                    { _id: workOrder._id },
                    { forUpdate: true },
                );
                assert.isNotNull(workOrder3);
                await assert.isRejected(
                    workOrder3.$.set({ routingCode: null }),
                    'WorkOrder.routingCode: cannot set value on frozen property',
                );
            },
            { today: '2020-06-09' },
        ));

    it('Create work order with routing then add bom then fail to remove bom - Check properties', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Rework',
            });

            const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                siteId: 'US003',
                releasedItem: '17890-B',
                releasedQuantity: 100,
                name: 'New work order',
                type: 'planned',
                workOrderNumber: 'WOUS003000001',
                workOrderCategory,
                route: '17890-B',
            });

            const item = await (await workOrder.routingCode)?.item;
            const site = await (await workOrder.routingCode)?.site;
            assert.isDefined(item);
            assert.isDefined(site);
            const workOrder2 = await xtremManufacturing.nodes.WorkOrder.addComponentsToWorkOrder(
                context,
                workOrder,
                item,
                site,
            );
            assert.equal(
                await (
                    await (
                        await workOrder2.productionComponents.elementAt(0)
                    ).operation
                )?.operationNumber,
                5,
            );

            const workOrder3 = await context.tryRead(
                xtremManufacturing.nodes.WorkOrder,
                { _id: workOrder._id },
                { forUpdate: true },
            );
            assert.isNotNull(workOrder3);
            await assert.isRejected(
                workOrder3.$.set({ bomCode: null }),
                'WorkOrder.bomCode: cannot set value on frozen property',
            );
        }));

    it('Create work order with bill of material but wrong category - Check properties', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Normal',
            });

            await assert.isRejected(
                xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: 'US003',
                    releasedItem: '17890-B',
                    releasedQuantity: 100,
                    name: 'New work order',
                    type: 'planned',
                    workOrderNumber: 'WOUS003000001',
                    workOrderCategory,
                    bom: '17890-B',
                }),
                'Routing is missing.',
            );
        }));

    it('Create work order with routing but wrong category - Check properties', () =>
        Test.withContext(async context => {
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Normal',
            });

            await assert.isRejected(
                xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: 'US003',
                    releasedItem: '17890-B',
                    releasedQuantity: 100,
                    name: 'New work order',
                    type: 'planned',
                    workOrderNumber: 'WOUS003000001',
                    workOrderCategory,
                    route: '17890-B',
                }),
                'Bill of material is missing.',
            );
        }));
});

describe('Pregenerate serial numbers functions', () => {
    it('Pregenerate serial numbers', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                    id: 'Normal',
                });

                const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: 'US001',
                    releasedItem: 'SN1',
                    releasedQuantity: 1000,
                    name: 'New work order',
                    type: 'firm',
                    workOrderNumber: 'WOUS003000001',
                    startDate: date.today(),
                    workOrderCategory,
                    bom: 'SN1',
                    route: 'SN1',
                });

                const productionItem = await workOrder.productionItem;
                const releasedItem = await (await workOrder.productionItem)?.releasedItem;
                assert.isNotNull(productionItem);
                assert.isDefined(releasedItem);
                assert.equal(await productionItem?.quantityInStockUnit, 1000);
                assert.equal(await releasedItem?.id, 'SN1');
                const preGeneratedSerialNumbers = await xtremManufacturing.nodes.WorkOrder.preGenerateSerialNumbers(
                    context,
                    releasedItem,
                    10,
                    site,
                    productionItem,
                    true,
                );
                assert.equal(preGeneratedSerialNumbers, '10');
                const preGeneratedSerialNumbers2 = await xtremManufacturing.nodes.WorkOrder.preGenerateSerialNumbers(
                    context,
                    releasedItem,
                    10,
                    site,
                    productionItem,
                    false,
                );
                assert.equal(preGeneratedSerialNumbers2, '10');
                const preGeneratedSerialNumbers3 = await xtremManufacturing.nodes.WorkOrder.preGenerateSerialNumbers(
                    context,
                    releasedItem,
                    10,
                    site,
                    productionItem,
                    false,
                );
                assert.equal(preGeneratedSerialNumbers3, '0');

                const preGeneratedSerialNumbers4 = await xtremManufacturing.nodes.WorkOrder.preGenerateSerialNumbers(
                    context,
                    releasedItem,
                    10,
                    site,
                    productionItem,
                    true,
                );
                assert.equal(preGeneratedSerialNumbers4, '0');
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));

    it('Delete pregenerated serial numbers', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                    id: 'Normal',
                });

                const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: 'US001',
                    releasedItem: 'SN1',
                    releasedQuantity: 1000,
                    name: 'New work order',
                    type: 'firm',
                    workOrderNumber: 'WOUS003000001',
                    startDate: date.today(),
                    workOrderCategory,
                    bom: 'SN1',
                    route: 'SN1',
                });

                const productionItem = await workOrder.productionItem;
                const releasedItem = await (await workOrder.productionItem)?.releasedItem;
                assert.isNotNull(productionItem);
                assert.isDefined(releasedItem);
                assert.equal(await productionItem?.quantityInStockUnit, 1000);
                assert.equal(await releasedItem?.id, 'SN1');
                const preGeneratedSerialNumbers = await xtremManufacturing.nodes.WorkOrder.preGenerateSerialNumbers(
                    context,
                    releasedItem,
                    100,
                    site,
                    productionItem,
                    false,
                );
                await productionItem.$.set({ releasedQuantity: 100 });
                await productionItem.$.save();
                assert.equal(preGeneratedSerialNumbers, '100');
                await context.delete(xtremManufacturing.nodes.WorkOrder, { _id: workOrder._id });

                const serNumCount = (
                    await context
                        .query(xtremStockData.nodes.SerialNumber, {
                            filter: { baseDocumentLine: productionItem },
                        })
                        .toArray()
                ).length;
                assert.equal(serNumCount, 0);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));
    it('Delete unused pregenerated serial numbers', () =>
        Test.withContext(
            async context => {
                const site = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
                const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                    id: 'Normal',
                });

                const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                    siteId: 'US001',
                    releasedItem: 'SN1',
                    releasedQuantity: 10,
                    name: 'New work order',
                    type: 'firm',
                    workOrderNumber: 'WOUS003000001',
                    startDate: date.today(),
                    workOrderCategory,
                    bom: 'SN1',
                    route: 'SN1',
                });

                const productionItem = await workOrder.productionItem;
                const releasedItem = await (await workOrder.productionItem)?.releasedItem;
                assert.isNotNull(productionItem);
                assert.isDefined(releasedItem);
                assert.equal(await productionItem?.quantityInStockUnit, 10);
                assert.equal(await releasedItem?.id, 'SN1');
                const preGeneratedSerialNumbers = await xtremManufacturing.nodes.WorkOrder.preGenerateSerialNumbers(
                    context,
                    releasedItem,
                    10,
                    site,
                    productionItem,
                    false,
                );
                assert.equal(preGeneratedSerialNumbers, '10');

                const serials = await context
                    .query(xtremStockData.nodes.SerialNumber, {
                        forUpdate: true,
                        filter: { baseDocumentLine: productionItem },
                    })
                    .toArray();
                for (let i = 0; i < serials.length; i += 2) {
                    await serials[i].$.set({ isUsable: true });
                    await serials[i].$.save();
                }

                assert.equal(await workOrder.serialsNumbers.length, 10);

                const deletedSerials = await workOrder.deleteUnusedPreGenerateSerialNumbers();
                assert.equal(deletedSerials, 5);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.serialNumberOption] },
        ));
});

async function testWorkOrderBomOrRoutingStatus(context: Context, forBom: boolean) {
    const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
        id: forBom ? 'Assembly' : 'Rework',
    });
    if (forBom) {
        assert.equal(await workOrderCategory.billOfMaterial, true);
        assert.equal(await workOrderCategory.routing, false);
    } else {
        assert.equal(await workOrderCategory.billOfMaterial, false);
        assert.equal(await workOrderCategory.routing, true);
    }

    const item = await context.read(xtremMasterData.nodes.Item, { _id: 'COST_D' });
    const site = await context.read(xtremSystem.nodes.Site, { _id: 'US001' });

    const bomOrRouting = forBom
        ? await context.read(xtremTechnicalData.nodes.BillOfMaterial, { item, site }, { forUpdate: true })
        : await context.read(xtremTechnicalData.nodes.Routing, { item, site }, { forUpdate: true });

    const workOrderData = {
        siteId: 'US001',
        releasedItem: 'COST_D',
        releasedQuantity: 100,
        name: 'New work order',
        type: 'firm',
        workOrderNumber: 'WOUS003000001',
        workOrderCategory,
    } as {
        siteId: string;
        releasedItem: string;
        releasedQuantity: decimal;
        name: string;
        type: xtremManufacturing.enums.WorkOrderType;
        workOrderCategory: xtremManufacturing.nodes.WorkOrderCategory;
        workOrderNumber: string;
        startDate?: date;
        bom?: string;
        route?: string;
        storedDimensions?: object;
        storedAttributes?: xtremMasterData.interfaces.StoredAttributes;
    };

    if (!forBom) {
        workOrderData.route = 'COST_D';
    } else {
        workOrderData.bom = 'COST_D';
    }

    // Set the active BOM to 'In development'. A work order creation with that BOM should fail.
    await bomOrRouting?.$.set({ status: 'inDevelopment' });
    await bomOrRouting?.$.save();
    await assert.isRejected(xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, workOrderData));

    // Set the active BOM to 'Is available to use'. A work order creation with that BOM should succeed.
    await bomOrRouting?.$.set({ status: 'availableToUse' });
    await bomOrRouting?.$.save();
    const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, workOrderData);

    // Set the active BOM to 'suspended'. A work order creation with that BOM should fail.
    await bomOrRouting?.$.set({ status: 'suspended' });
    await bomOrRouting?.$.save();
    await assert.isRejected(xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, workOrderData));

    // Set the active BOM to 'availableToUse'. The work order should be saved.
    await bomOrRouting?.$.set({ status: 'availableToUse' });
    await bomOrRouting?.$.save();
    await workOrder.$.save();

    // Set the active BOM to 'Suspended'. The work order should be saved.
    await bomOrRouting?.$.set({ status: 'suspended' });
    await bomOrRouting?.$.save();
    await workOrder.$.save();
}

describe('Work order BOM and routing status', () => {
    it('Update work order with BOM - Check status', () =>
        Test.withContext(async context => {
            await testWorkOrderBomOrRoutingStatus(context, true);
        }));

    it('Update work order with routing - Check status', () =>
        Test.withContext(async context => {
            await testWorkOrderBomOrRoutingStatus(context, false);
        }));

    it('Create workOrder node - without finished goods attribute type restricted to', () =>
        Test.withContext(
            async context => {
                const workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
                    site: '#700',
                    name: 'Another cake',
                    number: 'WOUS00200001',
                    startDate: date.parse('2020-06-10', context.currentLocale as LocalizeLocale),
                    endDate: date.parse('2020-06-10', context.currentLocale as LocalizeLocale),
                    type: 'planned',
                    category: '#Assembly',
                    bomCode: '#17890-B|US003',
                    routingCode: '#17890-B|US003',
                    productionItems: [
                        {
                            releasedItem: '#Chemical A',
                            releasedQuantity: 1,
                            storedAttributes: { project: '', task: 'TASK1', employee: '' },
                        },
                    ],
                });
                await assert.isRejected(workOrder.$.save());
                assert.deepEqual(workOrder.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['productionItems', '-**********'],
                        message: 'The Project attribute needs to be filled in.',
                    },
                ]);
            },
            { today: '2020-06-09' },
        ));

    it('Work order default attributes', () =>
        Test.withContext(async context => {
            const futureDate = date.today().addDays(1);
            const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
                id: 'Assembly',
            });

            const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
                siteId: 'RX001',
                releasedItem: 'HC001',
                releasedQuantity: 100,
                name: 'New work order',
                type: 'planned',
                workOrderNumber: 'WOUS003000001',
                workOrderCategory,
                startDate: futureDate,
                bom: 'HC001',
            });

            assert.deepEqual(
                JSON.stringify(await (await workOrder.productionItems.elementAt(0)).storedAttributes),
                JSON.stringify({ project: 'AttPROJ', task: 'Task1' }),
            );
        }));
});

describe('Test data fixes resulting from bugs fixed', () => {
    afterEach(() => {
        xtremSystem.TestHelpers.Sinon.removeMocks();
    });
    it('Work order close created status in progress', () =>
        Test.withContext(async context => {
            let workOrder = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: 'WIP Variance test 1' },
                { forUpdate: true },
            );

            await workOrder.$.set({ status: 'completed' });

            await workOrder.productionItems.forEach(async productionItem => {
                await productionItem.$.set({ stockTransactionStatus: 'inProgress' });
            });
            await workOrder.$.save();

            await workOrder.productionTrackings.forEach(async productionTracking => {
                const productionTrackingRecord = await context.read(
                    xtremManufacturing.nodes.ProductionTracking,
                    {
                        _id: productionTracking._id,
                    },
                    { forUpdate: true },
                );

                await productionTrackingRecord.lines.forEach(async line => {
                    await line.$.set({ stockTransactionStatus: 'inProgress' });
                });

                await productionTrackingRecord.$.save();
            });

            workOrder = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: 'WIP Variance test 1' },
                { forUpdate: true },
            );

            // work order stuck status
            assert.equal(await workOrder.status, 'completed');
            assert.equal(await workOrder.stockTransactionStatus, 'inProgress');
            assert.isNotNull(await workOrder.closingDate);

            // Fix statuses
            await xtremManufacturing.nodes.WorkOrder.resynchronizeStatus(context, workOrder);

            workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: 'WIP Variance test 1' });

            assert.equal(await workOrder.status, 'closed');
            assert.equal(await workOrder.stockTransactionStatus, 'completed'); // work order stock transaction status all statuses on lines must be complete
        }));

    it('Material tracking interrupted after stock update', () =>
        Test.withContext(async context => {
            const woNumber = 'WO-Test-Resync';
            let workOrder = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: woNumber },
                { forUpdate: true },
            );
            assert.equal(await workOrder.status, 'inProgress');
            assert.equal(await workOrder.stockTransactionStatus, 'draft');
            assert.equal(await workOrder.productionTrackings.length, 1);
            assert.equal(await workOrder.timeTrackings.length, 1);
            assert.equal(await workOrder.materialTrackings.length, 1);
            await workOrder.materialTrackings.forEach(async materialTracking => {
                await materialTracking.lines.forEach(async line => {
                    assert.equal(await line.stockTransactionStatus, 'completed');
                    const wipCosts = context.query(xtremManufacturing.nodes.WorkInProgressCost, {
                        filter: { originatingLine: line },
                    });
                    assert.equal(await wipCosts.length, 0);
                });
            });

            const mocks = xtremSystem.TestHelpers.Sinon.registerMocks([
                {
                    type: 'spy',
                    isMethod: true,
                    class: xtremManufacturing.nodes.MaterialTracking,
                    name: 'resynchronizeStatus',
                },
                {
                    type: 'spy',
                    reference: xtremFinanceData.functions.ManufacturingNotificationLib,
                    name: 'materialTrackingNotification',
                },
                {
                    type: 'spy',
                    reference: xtremManufacturing.functions.materialTrackingLib,
                    name: 'updateAfterStockSuccess',
                },
            ]);
            const materialResynchronizeSpy = mocks[0].mock as Sinon.SinonStub;
            const createAccountingNotificationSpy = mocks[1].mock as Sinon.SinonStub;
            const updateAfterStockSuccessSpy = mocks[2].mock as Sinon.SinonStub;

            // Fix data: create WIP costs and send finance notification
            await xtremManufacturing.nodes.WorkOrder.resynchronizeStatus(context, workOrder);

            workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: woNumber });

            assert.equal(await workOrder.status, 'inProgress');
            assert.equal(await workOrder.stockTransactionStatus, 'draft');
            const expectedWipCosts = [
                { originatingLine: { documentNumber: 'WOTUS00100019', itemId: 'ChairBack' }, amount: 14.4 },
                { originatingLine: { documentNumber: 'WOTUS00100019', itemId: 'ChairSeat' }, amount: 28.8 },
                { originatingLine: { documentNumber: 'WOTUS00100019', itemId: 'ChairLeg' }, amount: 57.6 },
            ];

            // Check that WIP costs of material tracking have been created
            const actualWipCosts = await context
                .query(xtremManufacturing.nodes.WorkInProgressCost, {
                    filter: { workOrder, type: 'materialTracking' },
                    orderBy: { amount: 1 },
                })
                .map(async wipCost => {
                    const originatingLine = await wipCost.originatingLine;
                    assert.isNotNull(originatingLine);
                    return {
                        originatingLine: {
                            documentNumber: await originatingLine.documentNumber,
                            itemId: await (
                                await (originatingLine as xtremManufacturing.nodes.MaterialTrackingLine).item
                            )?.id,
                        },
                        amount: await wipCost.amount,
                    };
                })
                .toArray();

            assert.deepEqual(actualWipCosts, expectedWipCosts);

            Sinon.assert.calledOnce(materialResynchronizeSpy);
            Sinon.assert.calledOnce(updateAfterStockSuccessSpy);

            Sinon.assert.calledOnce(createAccountingNotificationSpy);
            assert.equal(
                await createAccountingNotificationSpy.getCall(0).args[1].number,
                await (
                    await workOrder.materialTrackings.elementAt(0)
                ).number,
            );

            await Test.rollbackCache(context);

            workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: woNumber });

            // The 2nd call should do nothing
            await xtremManufacturing.nodes.WorkOrder.resynchronizeStatus(context, workOrder);

            // The update has not been called a second time
            Sinon.assert.calledOnce(updateAfterStockSuccessSpy);
        }));

    it('Material tracking interrupted during stock update', () =>
        Test.withContext(async context => {
            // Resynchronize must raise an error if the stockTransactionStatus is not completed
            // The stockTransaction is still considered in progress because is was created less than 1h ago
            const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: 'WO-Test-Resync2' });
            await assert.isRejected(
                xtremManufacturing.nodes.WorkOrder.resynchronizeStatus(context, workOrder),
                'The stock update is still in progress. You can resync after it has completed.',
            );

            xtremSystem.TestHelpers.Sinon.registerMocks([
                {
                    type: 'stub',
                    reference: xtremStockData.functions.stockTransactionLib,
                    name: 'isTransactionStuckInProgress',
                    returns: Promise.resolve(true),
                },
            ]);
            await xtremManufacturing.nodes.WorkOrder.resynchronizeStatus(context, workOrder);

            const actualStatuses = await testHelper.functions.getMaterialTrackingLineStatuses(workOrder);

            assert.includeDeepMembers(actualStatuses, [
                {
                    trackingNumber: 'WOTUS00100020',
                    lineSortValue: 10,
                    stockTransactionStatus: 'completed',
                },
                {
                    trackingNumber: 'WOTUS00100020',
                    lineSortValue: 20,
                    stockTransactionStatus: 'completed',
                },
                {
                    trackingNumber: 'WOTUS00100020',
                    lineSortValue: 30,
                    stockTransactionStatus: 'error',
                },
            ]);
        }));

    it('Production trackings interrupted before or after stock update', () =>
        Test.withContext(async context => {
            const woNumber = 'WO-Test-Resync3';
            let workOrder = await context.read(
                xtremManufacturing.nodes.WorkOrder,
                { number: woNumber },
                { forUpdate: true },
            );
            assert.equal(await workOrder.status, 'inProgress');
            assert.equal(await workOrder.stockTransactionStatus, 'draft');
            assert.equal(await workOrder.productionTrackings.length, 4);
            assert.equal(await workOrder.timeTrackings.length, 0);
            assert.equal(await workOrder.materialTrackings.length, 0);
            type WipCost = { amount: number };
            type TrackingLine = { stockTransactionStatus: string; wipCosts: WipCost[] };
            type ProductionTracking = { number: string; lines: TrackingLine[] };

            async function mapWipCost(wipCost: xtremManufacturing.nodes.WorkInProgressCost): Promise<WipCost> {
                return { amount: await wipCost.amount };
            }

            async function mapTrackingLine(
                line: xtremManufacturing.nodes.ProductionTrackingLine,
            ): Promise<TrackingLine> {
                const wipCosts = await line.workInProgressCosts.map(mapWipCost).toArray();
                return {
                    stockTransactionStatus: await line.stockTransactionStatus,
                    wipCosts,
                };
            }

            async function mapProductionTracking(
                productionTracking: xtremManufacturing.nodes.ProductionTracking,
            ): Promise<ProductionTracking> {
                const lines = await productionTracking.lines.map(mapTrackingLine).toArray();
                return {
                    number: await productionTracking.number,
                    lines,
                };
            }

            let productionTrackings = await workOrder.productionTrackings.map(mapProductionTracking).toArray();
            assertDeepPartialMatch(productionTrackings, [
                {
                    number: 'WOTUS00100020', // stopped just after reactToStockMovementReply -> stock is finihed but WIP costs are not created yet
                    lines: [{ stockTransactionStatus: 'completed', wipCosts: [] }],
                },
                {
                    number: 'WOTUS00100021', // stopped just before the stock reply -> stock is finished, transaction still in progress
                    lines: [{ stockTransactionStatus: 'inProgress', wipCosts: [] }],
                },
                {
                    number: 'WOTUS00100022', // stopped before stock update -> stock transaction in progress
                    lines: [{ stockTransactionStatus: 'inProgress', wipCosts: [] }],
                },
                {
                    number: 'WOTUS00100023', // completed correctly
                    lines: [{ stockTransactionStatus: 'completed', wipCosts: [{ amount: '94.048' }] }],
                },
            ]);

            const mocks = xtremSystem.TestHelpers.Sinon.registerMocks([
                {
                    type: 'spy',
                    isMethod: true,
                    class: xtremManufacturing.nodes.ProductionTracking,
                    name: 'resynchronizeStatus',
                },
                {
                    type: 'spy',
                    reference: xtremFinanceData.functions.ManufacturingNotificationLib,
                    name: 'productionTrackingNotification',
                },
                {
                    type: 'spy',
                    reference: xtremManufacturing.functions.productionTrackingLib,
                    name: 'updateAfterStockSuccess',
                },
            ]);
            const productionResynchronizeSpy = mocks[0].mock as Sinon.SinonStub;
            const createAccountingNotificationSpy = mocks[1].mock as Sinon.SinonStub;
            const updateAfterStockSuccessSpy = mocks[2].mock as Sinon.SinonStub;

            // As WOTUS00100022 is still considered in progress, the resync should raise an error
            await assert.isRejected(
                xtremManufacturing.nodes.WorkOrder.resynchronizeStatus(context, workOrder),
                'The stock update is still in progress. You can resync after it has completed.',
            );
            Sinon.assert.callCount(productionResynchronizeSpy, 3);
            Sinon.assert.callCount(updateAfterStockSuccessSpy, 2);

            productionResynchronizeSpy.resetHistory();
            updateAfterStockSuccessSpy.resetHistory();

            // mock the stockTransactionLib.isTransactionStuckInProgress to consider that the transaction is stuck
            xtremSystem.TestHelpers.Sinon.registerMocks([
                {
                    type: 'stub',
                    reference: xtremStockData.functions.stockTransactionLib,
                    name: 'isTransactionStuckInProgress',
                    returns: Promise.resolve(true),
                },
            ]);
            // Fix data: create WIP costs and send finance notification
            await xtremManufacturing.nodes.WorkOrder.resynchronizeStatus(context, workOrder);

            workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: woNumber });

            assert.equal(await workOrder.status, 'inProgress');
            assert.equal(await workOrder.stockTransactionStatus, 'draft');

            productionTrackings = await workOrder.productionTrackings.map(mapProductionTracking).toArray();

            assertDeepPartialMatch(productionTrackings, [
                {
                    number: 'WOTUS00100020', // stopped just after reactToStockMovementReply -> resync has created WIP costs
                    lines: [{ stockTransactionStatus: 'completed', wipCosts: [{ amount: '188.096' }] }],
                },
                {
                    number: 'WOTUS00100021', // stopped just before the stock reply -> stock is set as completed and wip cost created
                    lines: [{ stockTransactionStatus: 'completed', wipCosts: [{ amount: '282.144' }] }],
                },
                {
                    number: 'WOTUS00100022', // stopped before stock update -> resync set the stock transaction in error
                    lines: [{ stockTransactionStatus: 'error', wipCosts: [] }],
                },
                {
                    number: 'WOTUS00100023', // completed correctly -> nothing done
                    lines: [{ stockTransactionStatus: 'completed', wipCosts: [{ amount: '94.048' }] }],
                },
            ]);

            Sinon.assert.callCount(productionResynchronizeSpy, 4);
            // The resync has already been called for the 2 first production trackings
            // + the 3rd can't be resynced because the stock transaction is in error
            // + the 4th is already completed
            Sinon.assert.notCalled(updateAfterStockSuccessSpy);

            Sinon.assert.callCount(createAccountingNotificationSpy, 2);
            assert.equal(
                await createAccountingNotificationSpy.getCall(0).args[1].number,
                await (
                    await workOrder.productionTrackings.elementAt(0)
                ).number,
            );
            assert.equal(
                await createAccountingNotificationSpy.getCall(1).args[1].number,
                await (
                    await workOrder.productionTrackings.elementAt(1)
                ).number,
            );

            await Test.rollbackCache(context);

            workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: woNumber });

            // The 2nd call should do nothing
            productionResynchronizeSpy.resetHistory();

            // As WOTUS00100022 is in error, the resync should raise an error
            await assert.isRejected(
                xtremManufacturing.nodes.WorkOrder.resynchronizeStatus(context, workOrder),
                'The stock update is still in progress. You can resync after it has completed.',
            );

            // The update has not been called again
            Sinon.assert.notCalled(updateAfterStockSuccessSpy);
        }));

    it('Create workOrder node - with finished goods and components - default sequencing', () =>
        Test.withContext(
            async context => {
                const workOrder = await context.create(xtremManufacturing.nodes.WorkOrder, {
                    site: '#700',
                    name: 'Another cake',
                    number: 'WOUS00200001',
                    startDate: date.parse('2020-06-10', context.currentLocale as any),
                    endDate: date.parse('2020-06-10', context.currentLocale as any),
                    type: 'planned',
                    category: '#Assembly',
                    bomCode: '#17890-B|US003',
                    routingCode: '#Chemical A|US001',
                    productionItems: [
                        {
                            releasedItem: '#Chemical A',
                            releasedQuantity: 1,
                        },
                    ],
                    productionComponents: [
                        {
                            item: '#Chemical A',
                            linkQuantity: 100,
                            requiredQuantity: 100,
                            requiredDate: date.today(),
                            unit: { id: 'METER' },
                        },
                        {
                            item: '#Chemical A',
                            componentNumber: 15,
                            linkQuantity: 100,
                            requiredQuantity: 100,
                            requiredDate: date.today(),
                            unit: { id: 'METER' },
                        },
                        {
                            item: '#Chemical A',
                            linkQuantity: 100,
                            requiredQuantity: 100,
                            requiredDate: date.today(),
                            unit: { id: 'METER' },
                        },
                    ],
                });
                await workOrder.$.save();
                assert.deepEqual(workOrder.$.context.diagnoses, []);
                assert.equal(await (await workOrder.productionComponents.elementAt(0)).componentNumber, 10);
                assert.equal(await (await workOrder.productionComponents.elementAt(1)).componentNumber, 15);
                assert.equal(await (await workOrder.productionComponents.elementAt(2)).componentNumber, 20);
            },
            { today: '2020-06-09' },
        ));
});

describe('Posting details', () => {
    it('Gets posting details successfully', () =>
        Test.withContext(async context => {
            const woDocumentNumber = 'WO-Test-Resync';
            const woDocument = await context.read(xtremManufacturing.nodes.WorkOrder, { number: woDocumentNumber });
            assert.equal(await woDocument.postingDetails.length, 2);
            const journalEntry = await woDocument.postingDetails.elementAt(0);
            assert.equal(await journalEntry.documentType, 'workInProgress');
            assert.equal(await journalEntry.documentNumber, woDocumentNumber);
            assert.equal(await journalEntry.documentSysId, woDocument._id);
            assert.equal(await journalEntry.targetDocumentType, 'journalEntry');
        }));
});

describe('Pre/Post printing mutations', () => {
    it('workOrderPickList - beforePrintWorkOrderPickList - checks the status of the work order before printing the work order pick list', () =>
        Test.withContext(async context => {
            const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { _id: '#US001|WO250001' });
            await assert.isRejected(
                xtremManufacturing.nodes.WorkOrder.beforePrintWorkOrderPickList(context, workOrder),
                'This work order needs to be firm and in progress or pending to print the pick list:  WO250001.',
            );
        }));
    it('workOrderPickList - beforePrintWorkOrderPickList - checks the status of the work order before printing the work order pick list successfully', () =>
        Test.withContext(async context => {
            const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { _id: '#US001|WO2024US0010006' });
            assert.isTrue(await xtremManufacturing.nodes.WorkOrder.beforePrintWorkOrderPickList(context, workOrder));
        }));
});
