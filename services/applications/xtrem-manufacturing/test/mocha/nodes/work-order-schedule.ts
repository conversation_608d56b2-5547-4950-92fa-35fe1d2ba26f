import type { Context, LocalizeLocale } from '@sage/xtrem-core';
import { date, datetime, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib';

describe('Daylight saving changes', () => {
    it('Common day with no time switch', () => {
        const dat1 = datetime.make(2024, 4, 1, 0, 0, 0, 0, 'Europe/Paris');
        const UTC1 = dat1.inTimeZone('UTC');
        const dat2 = datetime.make(2024, 4, 1, 4, 0, 0, 0, 'Europe/Paris');
        const UTC2 = dat2.inTimeZone('UTC');
        // 4h between 0:00 - 4:00 AM the 1st of April 2024
        assert.equal(dat2.millisDiff(dat1) / (3600 * 1000), 4);
        assert.equal(UTC2.millisDiff(UTC1) / (3600 * 1000), 4);
    });

    it('Switch day to summer time', () => {
        const dat1 = datetime.make(2024, 3, 31, 0, 0, 0, 0, 'Europe/Paris');
        const UTC1 = dat1.inTimeZone('UTC');
        const dat2 = datetime.make(2024, 3, 31, 4, 0, 0, 0, 'Europe/Paris');
        const UTC2 = dat2.inTimeZone('UTC');
        // 3h between 0:00 - 4:00 AM the 31st of March 2024 because one hour is skipped (At 2:00 AM, it will be 3:00 AM)
        assert.equal(dat2.millisDiff(dat1) / (3600 * 1000), 3);
        assert.equal(UTC2.millisDiff(UTC1) / (3600 * 1000), 3);
    });

    it('Switch day to winter time', () => {
        const dat1 = datetime.make(2024, 10, 27, 0, 0, 0, 0, 'Europe/Paris');
        const UTC1 = dat1.inTimeZone('UTC');
        const dat2 = datetime.make(2024, 10, 27, 4, 0, 0, 0, 'Europe/Paris');
        const UTC2 = dat2.inTimeZone('UTC');
        // 5h between 0:00 and 4:00 AM the 27th of October 2024 because one hour is added (At 3:00 AM, it will be 2:00 AM)
        assert.equal(dat2.millisDiff(dat1) / (3600 * 1000), 5);
        assert.equal(UTC2.millisDiff(UTC1) / (3600 * 1000), 5);
    });
});

describe('Forward scheduling', () => {
    function mapDatetime(dateToBeMapped: datetime | null, timeZone: string) {
        return xtremManufacturing.functions.mapDatetimeTZ(dateToBeMapped, timeZone, 'YYYY-MM-DD HH:mm:ss');
    }

    async function checkDatesForNotScheduledWorkOrder(
        opeRes: xtremManufacturing.nodes.WorkOrderOperation | xtremManufacturing.nodes.WorkOrderOperationResource,
    ) {
        let workOrder: xtremManufacturing.nodes.WorkOrder;
        if (opeRes instanceof xtremManufacturing.nodes.WorkOrderOperation) {
            workOrder = await opeRes.workOrder;
            assert.isNull(await opeRes.startDate);
            assert.isNull(await opeRes.endDate);
        } else {
            workOrder = await (await opeRes.workOrderOperation).workOrder;
        }
        assert.isNull(await workOrder.startDatetime);
        assert.isNull(await workOrder.endDatetime);
        assert.isNull(await opeRes.startDatetime);
        assert.isNull(await opeRes.endDatetime);
    }

    async function checkResults(
        workOrder: xtremManufacturing.nodes.WorkOrder,
        parameters: {
            requestedDate: string;
            startDateTime: string;
            endDateTime: string;
            timeZone: string;
            status?: string;
        },
    ) {
        if (!parameters.status) {
            parameters.status = 'scheduled';
        }
        assert.equal(await workOrder.schedulingStatus, parameters.status);
        assert.equal(await workOrder.timeZone, parameters.timeZone);
        assert.isTrue(await workOrder.isForwardScheduling);
        assert.equal((await workOrder.requestedDate).toString(), parameters.requestedDate);
        const operation1 = await workOrder.productionOperations.elementAt(0);
        const resource1 = await operation1.resources.elementAt(0);
        if (parameters.timeZone === '') {
            assert.equal((await workOrder.startDate).toString(), parameters.startDateTime);
            assert.equal((await workOrder.endDate).toString(), parameters.endDateTime);
            assert.isNull(await workOrder.startDatetime);
            assert.isNull(await workOrder.endDatetime);
            await checkDatesForNotScheduledWorkOrder(operation1);
            await checkDatesForNotScheduledWorkOrder(resource1);
        } else {
            assert.equal(mapDatetime(await workOrder.startDatetime, parameters.timeZone), parameters.startDateTime);
            assert.equal(mapDatetime(await workOrder.endDatetime, parameters.timeZone), parameters.endDateTime);

            const startDate = parameters.startDateTime.substring(0, 10);
            const endDate = parameters.endDateTime.substring(0, 10);
            assert.equal((await workOrder.startDate).toString(), startDate);
            assert.equal((await workOrder.endDate).toString(), endDate);
            assert.equal((await operation1.startDate)?.toString(), startDate);
            assert.equal((await operation1.endDate)?.toString(), endDate);

            const workOrderStartDate = (await workOrder.startDatetime)?.toString();
            const workOrderEndDate = (await workOrder.endDatetime)?.toString();
            assert.equal((await operation1.startDatetime)?.toString(), workOrderStartDate);
            assert.equal((await operation1.endDatetime)?.toString(), workOrderEndDate);
            assert.equal((await resource1.startDatetime)?.toString(), workOrderStartDate);
            assert.equal((await resource1.endDatetime)?.toString(), workOrderEndDate);
        }
    }

    async function createWorkOrderForTest(
        context: Context,
        number: string,
        date0: date,
        timeZone: string,
        qty: number,
    ): Promise<xtremManufacturing.nodes.WorkOrder> {
        const workOrderCategory = await context.read(xtremManufacturing.nodes.WorkOrderCategory, {
            id: 'Rework',
        });

        const site = await context.read(
            xtremSystem.nodes.Site,
            {
                id: 'US001',
            },
            { forUpdate: true },
        );
        await site.$.set({ timeZone });
        await site.$.save();
        assert.equal(await site.timeZone, timeZone);

        const workOrder = await xtremManufacturing.nodes.WorkOrder.createWorkOrder(context, {
            siteId: 'US001',
            releasedItem: 'Chair',
            releasedQuantity: qty,
            name: 'New work order',
            type: 'firm',
            workOrderNumber: number,
            startDate: date0,
            workOrderCategory,
            route: 'Chair',
        });
        assert.equal(await workOrder.schedulingStatus, 'notScheduled');
        assert.isTrue(await workOrder.isForwardScheduling);
        assert.equal(await workOrder.timeZone, '');
        assert.isNull(await workOrder.startDatetime);
        assert.isNull(await workOrder.endDatetime);
        assert.equal((await workOrder.requestedDate).toString(), date0.toString());
        assert.equal(await workOrder.productionOperations.length, 1);
        const operation1 = await workOrder.productionOperations.elementAt(0);
        const duration =
            (await xtremMasterData.functions.convertToSeconds(
                await operation1.runTimeUnit,
                await operation1.expectedRunTime,
            )) +
            (await xtremMasterData.functions.convertToSeconds(
                await operation1.setupTimeUnit,
                await operation1.expectedSetupTime,
            ));
        assert.equal(duration, (10 + 4 * qty) * 60);
        assert.equal(await operation1.resources.length, 1);
        await checkDatesForNotScheduledWorkOrder(operation1);

        const resource1 = await operation1.resources.elementAt(0);
        assert.equal(await (await (await resource1.resource).weeklyShift).id, 'FIVE_DAYS_EIGHT_HOURS'); // Monday-Friday 08:00-16:00 => 40 hours
        await checkDatesForNotScheduledWorkOrder(resource1);

        return workOrder;
    }

    async function changeRequestedDate(
        context: Context,
        number: string,
        requestedDate: string,
    ): Promise<xtremManufacturing.nodes.WorkOrder> {
        const requestedDateLocalized = date.parse(requestedDate, context.currentLocale as LocalizeLocale);
        const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number }, { forUpdate: true });
        await workOrder.$.set({ requestedDate: requestedDateLocalized });
        await workOrder.$.save();
        return workOrder;
    }

    it('Fails to schedule a work order with site timezone blank', () =>
        Test.withContext(
            async context => {
                // Work order WOTEST0 created  &  site timezone assigned to blank
                const workOrderDate = date.parse('2024-04-06', context.currentLocale as LocalizeLocale);
                const workOrder = await createWorkOrderForTest(context, 'WOTEST0', workOrderDate, '', 500);
                await assert.isRejected(
                    xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder),
                    'You need to add a time zone to the US001 site before you can schedule work orders.',
                );
            },
            { today: '2024-03-11' },
        ));

    it('Fails to schedule a work order with no operation', () =>
        Test.withContext(async context => {
            const workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number: 'WO2024US0010004' });
            await assert.isRejected(
                xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder),
                'You can only schedule a work order that has at least one operation.',
            );
        }));

    ['Europe/Paris', 'Europe/London', 'Atlantic/Cape_Verde'].forEach(timeZone =>
        it(`Work order WOTEST1 - ${timeZone} timezone`, () =>
            // Work order with 1 operation/1 resource
            // test the flow with change of required date change
            // test stability of results on different timezones
            Test.withContext(
                async context => {
                    // Work order WOTEST1 created with required date = Friday 19 January 2024 and total expected time = 2010 min = 33h 30 min
                    const workOrderDate = date.parse('2024-01-19', context.currentLocale as LocalizeLocale);
                    const number = 'WOTEST1';
                    let workOrder = await createWorkOrderForTest(context, number, workOrderDate, timeZone, 500);
                    assert.equal((await workOrder.startDate).toString(), '2024-01-19');
                    assert.equal((await workOrder.endDate).toString(), '2024-01-20');

                    // 1st work order scheduling with required date = Friday 19
                    // => Friday 19 (8h), skip 2 days (we),  Monday 22 - Wednesday 24 (3*8h = 24h) + Thursday 25 (1h30) => 09:30
                    await checkResults(await xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder), {
                        requestedDate: '2024-01-19',
                        startDateTime: '2024-01-19 08:00:00',
                        endDateTime: '2024-01-25 09:30:00',
                        timeZone,
                    });

                    // Change required date from Friday 19 to Saturday 20
                    // => work order start/end dates are unchanged
                    // => work order is 'to reschedule'
                    workOrder = await changeRequestedDate(context, number, '2024-01-20');
                    await checkResults(workOrder, {
                        requestedDate: '2024-01-20',
                        startDateTime: '2024-01-19 08:00:00',
                        endDateTime: '2024-01-25 09:30:00',
                        timeZone,
                        status: 'toReschedule',
                    });

                    // 2nd work order scheduling with required date = Saturday 20
                    // => skip 2 days (we), Monday 22 - Thursday 25 (4*8h = 32h) + Friday 26 (1h30) => 09:30
                    await checkResults(await xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder), {
                        requestedDate: '2024-01-20',
                        startDateTime: '2024-01-22 08:00:00',
                        endDateTime: '2024-01-26 09:30:00',
                        timeZone,
                    });

                    // 3rd work order scheduling with required date = Tuesday 23
                    // =>  Tuesday 23 - Friday 26 (4*8h = 32h), skip 2 days (we), Monday 29 (1h30) => 09:30
                    workOrder = await changeRequestedDate(context, number, '2024-01-23');
                    await checkResults(await xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder), {
                        requestedDate: '2024-01-23',
                        startDateTime: '2024-01-23 08:00:00',
                        endDateTime: '2024-01-29 09:30:00',
                        timeZone,
                    });

                    // skip work order scheduling
                    // => work order start/end dates are unchanged
                    // => remove start/end date from operation
                    // => remove start/end datetime from operation/resource
                    // => work order is with scheduling 'not Managed'
                    workOrder = await context.read(xtremManufacturing.nodes.WorkOrder, { number }, { forUpdate: true });
                    await checkResults(
                        await xtremManufacturing.nodes.WorkOrder.skipSchedulingWorkOrder(context, workOrder),
                        {
                            requestedDate: '2024-01-23',
                            startDateTime: '2024-01-23',
                            endDateTime: '2024-01-29',
                            timeZone: '',
                            status: 'notManaged',
                        },
                    );

                    // revert the skip of work order scheduling
                    // => work order is with scheduling 'not Scheduled'
                    await checkResults(
                        await xtremManufacturing.nodes.WorkOrder.skipSchedulingWorkOrder(context, workOrder, false),
                        {
                            requestedDate: '2024-01-23',
                            startDateTime: '2024-01-23',
                            endDateTime: '2024-01-29',
                            timeZone: '',
                            status: 'notScheduled',
                        },
                    );
                },
                { today: '2024-01-17' },
            )),
    );

    ['Europe/Paris', 'America/Los_Angeles'].forEach(timeZone =>
        it(`Work order WOTEST2 - daylight saving time change - ${timeZone} timezone`, () =>
            // Work order with 1 operation/1 resource
            // test daylight saving time change on Paris timezone
            Test.withContext(
                async context => {
                    // Work order WOTEST2 created with required date = Friday 22 March 2024 and total expected time = 360 min = 6h
                    // 1 operation/1 resource with weekly shift = Monday-Friday 08:00-12:00 / 14:00-18:00, Saturday-Sunday 22-00-06:00
                    const workOrderDate = date.parse('2024-03-22', context.currentLocale as LocalizeLocale);
                    const workOrderNumber = 'WOTEST2';
                    let workOrder = await createWorkOrderForTest(context, workOrderNumber, workOrderDate, timeZone, 90);
                    assert.equal((await workOrder.startDate).toString(), '2024-03-22');
                    assert.equal((await workOrder.endDate).toString(), '2024-03-22');

                    workOrder = await context.read(
                        xtremManufacturing.nodes.WorkOrder,
                        { number: workOrderNumber },
                        { forUpdate: true },
                    );
                    const operation1 = await workOrder.productionOperations.elementAt(0);
                    const resource1 = await operation1.resources.elementAt(0);
                    await workOrder.$.set({
                        productionOperations: [
                            {
                                _action: 'update',
                                _id: operation1._id,
                                resources: [
                                    {
                                        _action: 'update',
                                        _id: resource1._id,
                                        expectedRunTime: 340,
                                        expectedSetupTime: 20,
                                    },
                                ],
                            },
                        ],
                    });
                    await workOrder.$.save();
                    const weeklyShift = await context.read(
                        xtremMasterData.nodes.WeeklyShift,
                        {
                            id: await (await (await resource1.resource).weeklyShift).id,
                        },
                        { forUpdate: true },
                    );
                    await weeklyShift.$.set({
                        mondayShift: '#TEST_DUAL_EIGHT_HOURS_SHIFT',
                        tuesdayShift: '#TEST_DUAL_EIGHT_HOURS_SHIFT',
                        wednesdayShift: '#TEST_DUAL_EIGHT_HOURS_SHIFT',
                        thursdayShift: '#TEST_DUAL_EIGHT_HOURS_SHIFT',
                        fridayShift: '#TEST_DUAL_EIGHT_HOURS_SHIFT',
                        saturdayShift: '#NIGHT_EIGHT_HOURS',
                        sundayShift: '#NIGHT_EIGHT_HOURS',
                    });
                    await weeklyShift.$.save();
                    assert.equal(await weeklyShift.formattedCapacity, '56:00');

                    // 1st work order scheduling with required date = Friday 22 March 2024
                    await checkResults(await xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder), {
                        requestedDate: '2024-03-22',
                        startDateTime: '2024-03-22 08:00:00',
                        endDateTime: '2024-03-22 16:00:00',
                        timeZone,
                    });

                    // 2nd work order scheduling with required date = Saturday 23 March 2024
                    workOrder = await changeRequestedDate(context, workOrderNumber, '2024-03-23');
                    await checkResults(await xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder), {
                        requestedDate: '2024-03-23',
                        startDateTime: '2024-03-23 22:00:00',
                        endDateTime: '2024-03-24 04:00:00',
                        timeZone,
                    });

                    // 3rd work order scheduling with required date = Friday 29 March 2024
                    workOrder = await changeRequestedDate(context, workOrderNumber, '2024-03-29');
                    await checkResults(await xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder), {
                        requestedDate: '2024-03-29',
                        startDateTime: '2024-03-29 08:00:00',
                        endDateTime: '2024-03-29 16:00:00',
                        timeZone,
                    });

                    // 4th work order scheduling with required date = Saturday 30 March 2024
                    // Europe/Paris timezone impacted by summer time change
                    workOrder = await changeRequestedDate(context, workOrderNumber, '2024-03-30');
                    await checkResults(await xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder), {
                        requestedDate: '2024-03-30',
                        startDateTime: '2024-03-30 22:00:00',
                        endDateTime: timeZone === 'Europe/Paris' ? '2024-03-31 05:00:00' : '2024-03-31 04:00:00',
                        timeZone,
                    });

                    // 5th work order scheduling with required date = Friday 05 April 2024
                    workOrder = await changeRequestedDate(context, workOrderNumber, '2024-04-05');
                    await checkResults(await xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder), {
                        requestedDate: '2024-04-05',
                        startDateTime: '2024-04-05 08:00:00',
                        endDateTime: '2024-04-05 16:00:00',
                        timeZone,
                    });

                    // 6th work order scheduling with required date = Saturday 06 April 2024
                    workOrder = await changeRequestedDate(context, workOrderNumber, '2024-04-06');
                    await checkResults(await xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder), {
                        requestedDate: '2024-04-06',
                        startDateTime: '2024-04-06 22:00:00',
                        endDateTime: '2024-04-07 04:00:00',
                        timeZone,
                    });

                    // 7th work order scheduling with required date = Friday 25 October 2024
                    workOrder = await changeRequestedDate(context, workOrderNumber, '2024-10-25');
                    await checkResults(await xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder), {
                        requestedDate: '2024-10-25',
                        startDateTime: '2024-10-25 08:00:00',
                        endDateTime: '2024-10-25 16:00:00',
                        timeZone,
                    });

                    // 8th work order scheduling with required date = Saturday 26 October 2024
                    // Europe/Paris timezone impacted by summer time change
                    workOrder = await changeRequestedDate(context, workOrderNumber, '2024-10-26');
                    await checkResults(await xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder), {
                        requestedDate: '2024-10-26',
                        startDateTime: '2024-10-26 22:00:00',
                        endDateTime: timeZone === 'Europe/Paris' ? '2024-10-27 03:00:00' : '2024-10-27 04:00:00',
                        timeZone,
                    });

                    // 9th work order scheduling with required date = Friday 8 November 2024
                    workOrder = await changeRequestedDate(context, workOrderNumber, '2024-11-08');
                    await checkResults(await xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder), {
                        requestedDate: '2024-11-08',
                        startDateTime: '2024-11-08 08:00:00',
                        endDateTime: '2024-11-08 16:00:00',
                        timeZone,
                    });

                    // 10th work order scheduling with required date = Saturday 9 November 2024
                    workOrder = await changeRequestedDate(context, workOrderNumber, '2024-11-09');
                    await checkResults(await xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder), {
                        requestedDate: '2024-11-09',
                        startDateTime: '2024-11-09 22:00:00',
                        endDateTime: '2024-11-10 04:00:00',
                        timeZone,
                    });
                },
                { today: '2024-03-11', config: { 'xtrem-manufacturing': { 'time-zone': timeZone } } },
            )),
    );

    it('Work order WOTEST3', () =>
        // Work order with operation1/1 resource + operation2/ 2 resources
        Test.withContext(
            async context => {
                // Work order WOTEST3 created with required date = Saturday 06 April 2024 and expected time = 10 min + 2420 min = 2430 min = 40h 30 min
                // with operation 10 for resource Jennifer Dawson (Monday-Friday 08:00-16:00)
                const workOrderDate = date.parse('2024-04-06', context.currentLocale as LocalizeLocale);
                const workOrderNumber = 'WOTEST3';
                const timeZone = 'Europe/Paris';
                let workOrder = await createWorkOrderForTest(context, workOrderNumber, workOrderDate, timeZone, 605);
                assert.equal((await workOrder.startDate).toString(), '2024-04-06');
                assert.equal((await workOrder.endDate).toString(), '2024-04-07');

                // add operation 20 with 2 resources
                // - labor Franck american (Monday-Friday 08:00-12:00 14:00-18:00) expected time = 1 h+ 9h = 10h
                // - group STBON-G (Monday-Friday 22:00-06:00) expected time = 1h + 19h = 20h

                const groupResource = await context.read(
                    xtremMasterData.nodes.GroupResource,
                    {
                        id: 'STBON-G',
                        site: '#US001',
                    },
                    { forUpdate: true },
                );
                await groupResource.$.set({ weeklyShift: '#NIGHT_SHIFT' });
                await groupResource.$.save();

                workOrder = await context.read(
                    xtremManufacturing.nodes.WorkOrder,
                    { number: workOrderNumber },
                    { forUpdate: true },
                );
                await workOrder.$.set({
                    productionOperations: [
                        {
                            _action: 'create',
                            isAdded: true,
                            isProductionStep: false,
                            minCapabilityLevel: '#Master',
                            name: 'added operation',
                            operationNumber: 20,
                            plannedQuantity: 605,
                            runTimeUnit: '#HOUR',
                            setupTimeUnit: '#HOUR',
                            status: 'pending',
                            resources: [
                                {
                                    _action: 'create',
                                    expectedRunTime: 9,
                                    expectedSetupTime: 1,
                                    isAdded: true,
                                    isResourceQuantity: false,
                                    resource: '#Frank American|US001',
                                    status: 'pending',
                                },
                                {
                                    _action: 'create',
                                    expectedRunTime: 19,
                                    expectedSetupTime: 1,
                                    isAdded: true,
                                    isResourceQuantity: true,
                                    resource: '#STBON-G|US001',
                                    status: 'pending',
                                    resources: [{ resource: '#Elizabeth Greyson|US001' }],
                                },
                            ],
                        },
                    ],
                });
                await workOrder.$.save();

                workOrder = await xtremManufacturing.nodes.WorkOrder.scheduleWorkOrder(context, workOrder);
                assert.equal(await workOrder.schedulingStatus, 'scheduled');
                assert.equal(await workOrder.timeZone, timeZone);
                assert.isTrue(await workOrder.isForwardScheduling);
                assert.equal((await workOrder.requestedDate).toString(), '2024-04-06'); // Saturday
                assert.equal(mapDatetime(await workOrder.startDatetime, timeZone), '2024-04-08 08:00:00'); // Monday
                assert.equal(mapDatetime(await workOrder.endDatetime, timeZone), '2024-04-18 02:00:00'); // Thursday

                const operation1 = await workOrder.productionOperations.elementAt(0);
                assert.equal((await operation1.startDate)?.toString(), '2024-04-08');
                assert.equal((await operation1.endDate)?.toString(), '2024-04-15');
                assert.equal(mapDatetime(await operation1.startDatetime, timeZone), '2024-04-08 08:00:00'); // Monday
                assert.equal(mapDatetime(await operation1.endDatetime, timeZone), '2024-04-15 08:30:00'); // Monday
                const resource1 = await operation1.resources.elementAt(0);
                assert.equal(mapDatetime(await resource1.startDatetime, timeZone), '2024-04-08 08:00:00'); // Monday
                assert.equal(mapDatetime(await resource1.endDatetime, timeZone), '2024-04-15 08:30:00'); // Monday

                const operation2 = await workOrder.productionOperations.elementAt(1);
                assert.equal((await operation2.startDate)?.toString(), '2024-04-15');
                assert.equal((await operation2.endDate)?.toString(), '2024-04-18');
                assert.equal(mapDatetime(await operation2.startDatetime, timeZone), '2024-04-15 08:30:00'); // Monday
                assert.equal(mapDatetime(await operation2.endDatetime, timeZone), '2024-04-18 02:00:00'); // Thursday
                const resource2 = await operation2.resources.elementAt(0);
                assert.equal(mapDatetime(await resource2.startDatetime, timeZone), '2024-04-15 08:30:00'); // Monday
                assert.equal(mapDatetime(await resource2.endDatetime, timeZone), '2024-04-16 10:30:00'); // Tuesday
                const resource3 = await operation2.resources.elementAt(1);
                assert.equal(mapDatetime(await resource3.startDatetime, timeZone), '2024-04-15 22:00:00'); // Monday
                assert.equal(mapDatetime(await resource3.endDatetime, timeZone), '2024-04-18 02:00:00'); // Thursday

                workOrder = await context.read(
                    xtremManufacturing.nodes.WorkOrder,
                    { number: workOrderNumber },
                    { forUpdate: true },
                );
                await workOrder.$.set({
                    productionOperations: [
                        {
                            _action: 'update',
                            _id: operation2._id,
                            resources: [
                                {
                                    _action: 'update',
                                    _id: resource2._id,
                                    expectedSetupTime: 0,
                                },
                                {
                                    _action: 'update',
                                    _id: resource3._id,
                                    expectedSetupTime: 2,
                                },
                            ],
                        },
                    ],
                });
                await workOrder.$.save();
                assert.equal(await workOrder.schedulingStatus, 'toReschedule');
            },
            { today: '2024-03-11' },
        ));
});
