import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib';

describe('OperationTracking  Standard create ', () => {
    it('Create OperationTracking with a labor resource ', () =>
        Test.withContext(async context => {
            const wosFirm = await context.read(xtremManufacturing.nodes.WorkOrder, { _id: '#US003|WOS-FIRM' });

            const workOrderOperation15 = await wosFirm.productionOperations.find(
                async operation => (await operation.operationNumber) === 15,
            );
            if (!workOrderOperation15) {
                throw new Error('Operation not found');
            }

            assert.equal(await workOrderOperation15.status, 'excluded');
            const operator = await context.read(xtremMasterData.nodes.LaborResource, { _id: '#<PERSON>|US003' });

            const newTracking = await context.create(xtremManufacturing.nodes.OperationTracking, {
                workOrder: wosFirm,
                lines: [
                    {
                        workOrderOperation: workOrderOperation15,
                        line: 1,
                        completedQuantity: 1,
                        actualResource: operator,
                    },
                ],
            });

            assert.isFalse(await newTracking.$.control());
            assert.deepEqual(context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines', '-1000000002'],
                    message: 'The work order operation status must be different from Excluded.',
                },
            ]);
        }));
});
