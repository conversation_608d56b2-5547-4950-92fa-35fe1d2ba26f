import type { Reference } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as xtremManufacturing from '../../../lib';

describe('Work Order node - allocation', () => {
    it('Work order - all lines partially allocated', () =>
        Test.withContext(async context => {
            const workOrder1 = await context.read(xtremManufacturing.nodes.WorkOrder, {
                number: 'WO2021US0030002',
            });
            const workOrder1Component1 = await workOrder1.productionComponents.elementAt(0);
            assert.equal((await workOrder1Component1.requiredQuantity).toString(), '11');
            assert.equal((await workOrder1Component1.quantityAllocated).toString(), '0');
            assert.equal(await workOrder1Component1.allocationStatus, 'notAllocated');

            let stockRecord = await context.read(xtremStockData.nodes.Stock, {
                item: (await workOrder1Component1.item)!._id,
                site: (await workOrder1.site)._id,
            });

            await xtremStockData.nodes.Stock.updateAllocations(context, {
                documentLine:
                    workOrder1Component1 as unknown as Promise<xtremStockData.interfaces.DocumentLineWithStockAllocation>,
                allocationUpdates: [
                    {
                        action: 'create',
                        quantity: 5,
                        stockRecord: stockRecord as unknown as Reference<xtremStockData.nodes.Stock>,
                    },
                ],
            });

            const workOrder1Component2 = await workOrder1.productionComponents.elementAt(1);
            assert.equal((await workOrder1Component2.requiredQuantity).toString(), '100');
            assert.equal((await workOrder1Component2.quantityAllocated).toString(), '0');
            assert.equal(await workOrder1Component2.allocationStatus, 'notAllocated');

            stockRecord = await context.read(xtremStockData.nodes.Stock, {
                item: (await workOrder1Component2.item)!._id,
                site: (await workOrder1.site)._id,
            });

            await xtremStockData.nodes.Stock.updateAllocations(context, {
                documentLine:
                    workOrder1Component2 as unknown as Promise<xtremStockData.interfaces.DocumentLineWithStockAllocation>,
                allocationUpdates: [
                    {
                        action: 'create',
                        quantity: 5,
                        stockRecord: stockRecord as unknown as Reference<xtremStockData.nodes.Stock>,
                    },
                ],
            });

            const workOrder1Component3 = await workOrder1.productionComponents.elementAt(2);
            assert.equal((await workOrder1Component3.requiredQuantity).toString(), '100');
            assert.equal((await workOrder1Component3.quantityAllocated).toString(), '0');
            assert.equal(await workOrder1Component3.allocationStatus, 'notAllocated');

            stockRecord = await context.read(xtremStockData.nodes.Stock, {
                item: (await workOrder1Component3.item)!._id,
                site: (await workOrder1.site)._id,
            });

            await xtremStockData.nodes.Stock.updateAllocations(context, {
                documentLine:
                    workOrder1Component3 as unknown as Promise<xtremStockData.interfaces.DocumentLineWithStockAllocation>,
                allocationUpdates: [
                    {
                        action: 'create',
                        quantity: 5,
                        stockRecord: stockRecord as unknown as Reference<xtremStockData.nodes.Stock>,
                    },
                ],
            });

            const workOrder1Component4 = await workOrder1.productionComponents.elementAt(3);
            assert.equal((await workOrder1Component4.requiredQuantity).toString(), '100');
            assert.equal((await workOrder1Component4.quantityAllocated).toString(), '0');
            assert.equal(await workOrder1Component4.allocationStatus, 'notAllocated');

            stockRecord = await context.read(xtremStockData.nodes.Stock, {
                item: (await workOrder1Component4.item)!._id,
                site: (await workOrder1.site)._id,
            });

            await xtremStockData.nodes.Stock.updateAllocations(context, {
                documentLine:
                    workOrder1Component4 as unknown as Promise<xtremStockData.interfaces.DocumentLineWithStockAllocation>,
                allocationUpdates: [
                    {
                        action: 'create',
                        quantity: 5,
                        stockRecord: stockRecord as unknown as Reference<xtremStockData.nodes.Stock>,
                    },
                ],
            });

            const worOrder1Component5 = await workOrder1.productionComponents.elementAt(4);
            assert.equal((await worOrder1Component5.requiredQuantity).toString(), '2');
            assert.equal((await worOrder1Component5.quantityAllocated).toString(), '0');
            assert.equal(await worOrder1Component5.allocationStatus, 'notAllocated');

            await xtremStockData.nodes.Stock.updateAllocations(context, {
                documentLine:
                    worOrder1Component5 as unknown as Promise<xtremStockData.interfaces.DocumentLineWithStockAllocation>,
                allocationUpdates: [
                    {
                        action: 'create',
                        quantity: 5,
                        stockRecord: stockRecord as unknown as Reference<xtremStockData.nodes.Stock>,
                    },
                ],
            });

            const workOrder2 = await context.read(xtremManufacturing.nodes.WorkOrder, {
                number: 'WO2021US0030002',
            });
            const workOrder2Component1 = await workOrder1.productionComponents.elementAt(0);
            assert.equal((await workOrder2Component1.requiredQuantity).toString(), '11');
            assert.equal((await workOrder2Component1.quantityAllocated).toString(), '5');
            assert.equal(await workOrder2Component1.allocationStatus, 'partiallyAllocated');

            const worOrder2Component2 = await workOrder1.productionComponents.elementAt(1);
            assert.equal((await worOrder2Component2.requiredQuantity).toString(), '100');
            assert.equal((await worOrder2Component2.quantityAllocated).toString(), '5');
            assert.equal(await worOrder2Component2.allocationStatus, 'partiallyAllocated');
            assert.equal(await workOrder2.allocationStatus, 'partiallyAllocated');
        }));

    it('Work order - some lines partially allocated', () =>
        Test.withContext(
            async context => {
                const workOrder1 = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    number: 'WO2021US0030002',
                });
                const workOrder1Component1 = await workOrder1.productionComponents.elementAt(0);
                assert.equal((await workOrder1Component1.requiredQuantity).toString(), '11');
                assert.equal((await workOrder1Component1.quantityAllocated).toString(), '0');
                assert.equal(await workOrder1Component1.allocationStatus, 'notAllocated');
                const stockRecord = await context.read(xtremStockData.nodes.Stock, {
                    item: (await workOrder1Component1.item)!._id,
                    site: (await workOrder1.site)._id,
                });

                await xtremStockData.nodes.Stock.updateAllocations(context, {
                    documentLine:
                        workOrder1Component1 as unknown as Promise<xtremStockData.interfaces.DocumentLineWithStockAllocation>,
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: 5,
                            stockRecord: stockRecord as unknown as Reference<xtremStockData.nodes.Stock>,
                        },
                    ],
                });

                const worOrder1Component2 = await workOrder1.productionComponents.elementAt(1);
                assert.equal((await worOrder1Component2.requiredQuantity).toString(), '100');
                assert.equal((await worOrder1Component2.quantityAllocated).toString(), '0');
                assert.equal(await worOrder1Component2.allocationStatus, 'notAllocated');

                const workOrder2 = await context.read(
                    xtremManufacturing.nodes.WorkOrder,
                    {
                        number: 'WO2021US0030002',
                    },
                    { forUpdate: true },
                );
                const workOrder2Component1 = await workOrder2.productionComponents.elementAt(0);
                assert.equal((await workOrder2Component1.requiredQuantity).toString(), '11');
                assert.equal((await workOrder2Component1.quantityAllocated).toString(), '5');
                assert.equal(await workOrder2Component1.allocationStatus, 'partiallyAllocated');

                const worOrder2Component2 = await workOrder2.productionComponents.elementAt(1);
                assert.equal((await worOrder2Component2.requiredQuantity).toString(), '100');
                assert.equal((await worOrder2Component2.quantityAllocated).toString(), '0');
                assert.equal(await worOrder2Component2.allocationStatus, 'notAllocated');
                assert.equal(await workOrder2.allocationStatus, 'partiallyAllocated');

                await assert.isRejected(
                    xtremManufacturing.nodes.WorkOrder.closeWorkOrder(context, workOrder2, date.today()),
                    'Try to delete the pending work order WO2021US0030002 instead. You cannot close it.',
                );

                await assert.isRejected(workOrder2.$.delete(), 'The record was not deleted.');
                assert.deepEqual(context.diagnoses, [
                    {
                        severity: 3,
                        path: ['productionComponents', '3005'],
                        message: 'Remove the stock allocation before deleting the line.',
                    },
                ]);

                await workOrder2Component1.$.set({ requiredQuantity: 4 });
                await assert.isRejected(workOrder2.$.save());
                assert.deepEqual(workOrder2.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['productionComponents', '3005'],
                        message:
                            'The allocated quantity on the work order component cannot be more than the required quantity.',
                    },
                ]);

                await workOrder2Component1.$.set({ requiredQuantity: 5, lineStatus: 'excluded' });
                await assert.isRejected(workOrder2.$.save());
                assert.deepEqual(workOrder2.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['productionComponents', '3005'],
                        message: 'Remove the stock allocation before excluding the line.',
                    },
                ]);
            },
            { today: '2023-04-18' },
        ));

    it('Work order - all lines allocated', () =>
        Test.withContext(
            async context => {
                const workOrder1 = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    number: 'WOS-SM-FIRM',
                });
                const workOrder1Component1 = await workOrder1.productionComponents.elementAt(0);
                assert.equal((await workOrder1Component1.requiredQuantity).toString(), '200');
                assert.equal((await workOrder1Component1.quantityAllocated).toString(), '0');
                assert.equal(await workOrder1Component1.allocationStatus, 'notAllocated');

                const stockRecord = await context.read(xtremStockData.nodes.Stock, {
                    item: (await workOrder1Component1.item)!._id,
                    site: (await workOrder1.site)._id,
                });

                await xtremStockData.nodes.Stock.updateAllocations(context, {
                    documentLine:
                        workOrder1Component1 as unknown as Promise<xtremStockData.interfaces.DocumentLineWithStockAllocation>,
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: 200,
                            stockRecord: stockRecord as unknown as Reference<xtremStockData.nodes.Stock>,
                        },
                    ],
                });

                const workOrder2 = await context.read(
                    xtremManufacturing.nodes.WorkOrder,
                    {
                        number: 'WOS-SM-FIRM',
                    },
                    { forUpdate: true },
                );
                const workOrder2Component1 = await workOrder2.productionComponents.elementAt(0);
                assert.equal((await workOrder2Component1.requiredQuantity).toString(), '200');
                assert.equal((await workOrder2Component1.quantityAllocated).toString(), '200');
                assert.equal(await workOrder2Component1.allocationStatus, 'allocated');
                assert.equal(await workOrder2.allocationStatus, 'allocated');

                await assert.isRejected(
                    xtremManufacturing.nodes.WorkOrder.closeWorkOrder(context, workOrder2, date.today()),
                    'Try to delete the pending work order WOS-SM-FIRM instead. You cannot close it.',
                );

                await assert.isRejected(workOrder2.$.delete(), 'The record was not deleted.');
                assert.deepEqual(context.diagnoses, [
                    {
                        message: 'Remove the stock allocation before deleting the line.',
                        path: ['productionComponents', '3053'],
                        severity: 3,
                    },
                    {
                        message: 'The component cannot be deleted while an automatic allocation is in progress.',
                        path: ['productionComponents', '3053'],
                        severity: 3,
                    },
                ]);

                await workOrder2Component1.$.set({ lineStatus: 'excluded' });
                await assert.isRejected(workOrder2.$.save());
                assert.deepEqual(workOrder2.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['productionComponents', '3053'],
                        message: 'Remove the stock allocation before excluding the line.',
                    },
                    {
                        severity: 3,
                        path: ['productionComponents', '3053'],
                        message: 'You can only exclude the component after the allocation request is complete.',
                    },
                ]);
            },
            { today: '2023-04-18' },
        ));

    it('Work order in progress - some lines partially allocated, closing and deletion fail', () =>
        Test.withContext(
            async context => {
                const workOrder1 = await context.read(xtremManufacturing.nodes.WorkOrder, {
                    number: 'WO2021US0030001',
                });
                const workOrder1Component1 = await workOrder1.productionComponents.elementAt(4);
                assert.equal((await workOrder1Component1.requiredQuantity).toString(), '20');
                assert.equal((await workOrder1Component1.quantityAllocated).toString(), '0');
                assert.equal(await workOrder1Component1.allocationStatus, 'notAllocated');

                const stockRecord = await context.read(xtremStockData.nodes.Stock, {
                    _id: 50,
                });

                await xtremStockData.nodes.Stock.updateAllocations(context, {
                    documentLine:
                        workOrder1Component1 as unknown as Promise<xtremStockData.interfaces.DocumentLineWithStockAllocation>,
                    allocationUpdates: [
                        {
                            action: 'create',
                            quantity: 5,
                            stockRecord: stockRecord as unknown as Reference<xtremStockData.nodes.Stock>,
                        },
                    ],
                });

                const workOrder2 = await context.read(
                    xtremManufacturing.nodes.WorkOrder,
                    {
                        number: 'WO2021US0030001',
                    },
                    { forUpdate: true },
                );
                const worOrder2Component1 = await workOrder2.productionComponents.elementAt(4);
                assert.equal((await worOrder2Component1.requiredQuantity).toString(), '20');
                assert.equal((await worOrder2Component1.quantityAllocated).toString(), '5');
                assert.equal(await worOrder2Component1.allocationStatus, 'partiallyAllocated');
                assert.equal(await workOrder2.allocationStatus, 'partiallyAllocated');

                await assert.isRejected(
                    xtremManufacturing.nodes.WorkOrder.closeWorkOrder(context, workOrder2, date.today()),
                    'Remove the stock allocation before closing the work order WO2021US0030001.',
                );

                await assert.isRejected(workOrder2.$.delete(), 'The record was not deleted.');
                assert.deepEqual(context.diagnoses, [
                    {
                        severity: 3,
                        path: ['productionComponents', '3004'],
                        message: 'Remove the stock allocation before deleting the line.',
                    },
                ]);
            },
            { today: '2023-04-18' },
        ));

    it('Work order - allocation request in progress on component', () =>
        Test.withContext(
            async context => {
                const workOrder1 = await context.read(
                    xtremManufacturing.nodes.WorkOrder,
                    {
                        number: 'WOS-SM-FIRM',
                    },
                    { forUpdate: true },
                );

                await workOrder1.$.set({ status: 'inProgress' });

                await assert.isRejected(
                    xtremManufacturing.nodes.WorkOrder.closeWorkOrder(context, workOrder1, date.today()),
                    'You can only close the work order after the allocation request is complete for the component.',
                );
            },
            { today: '2023-04-18' },
        ));
});
