declare module '@sage/xtrem-manufacturing-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { Package as SageXtremDistribution$Package } from '@sage/xtrem-distribution-api';
    import type {
        AnalyticalData,
        FinanceTransaction,
        Package as SageXtremFinanceData$Package,
    } from '@sage/xtrem-finance-data-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type { Package as SageXtremLandedCost$Package } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        BaseDocumentLine,
        BaseResource,
        CapabilityLevel,
        Currency,
        DetailedResource,
        Item,
        ItemSite,
        Location,
        Package as SageXtremMasterData$Package,
        ReasonCode,
        ResourceCostCategory,
        ResourceCostCategoryBinding,
        ResourceCostCategoryInput,
        UnitOfMeasure,
        WeeklyShift,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremPurchasing$Package } from '@sage/xtrem-purchasing-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type {
        BaseStockDetail,
        Lot,
        Package as SageXtremStockData$Package,
        SerialNumber,
        StockAllocation,
        StockIssueDetail,
        StockIssueDetailBinding,
        StockIssueDetailInput,
        StockJournal,
        StockJournalInput,
        StockJournalSerialNumber,
        StockJournalSerialNumberBinding,
        StockJournalSerialNumberInput,
        StockReceiptDetail,
        StockReceiptDetailBinding,
        StockReceiptDetailInput,
        StockStatus,
        StockTransaction,
        StockTransactionBinding,
        StockTransactionInput,
    } from '@sage/xtrem-stock-data-api';
    import type { Package as SageXtremStructure$Package } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type { Package as SageXtremSystem$Package, Site, SysVendor, User } from '@sage/xtrem-system-api';
    import type { Package as SageXtremTax$Package } from '@sage/xtrem-tax-api';
    import type {
        BillOfMaterial,
        BillOfMaterialRevision,
        BillOfMaterialTracking,
        Component,
        Operation,
        Package as SageXtremTechnicalData$Package,
        Routing,
    } from '@sage/xtrem-technical-data-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface ComponentStatus$Enum {
        pending: 1;
        inProgress: 2;
        completed: 3;
        excluded: 4;
        included: 5;
    }
    export type ComponentStatus = keyof ComponentStatus$Enum;
    export interface ManufacturingAllocationUpdateAction$Enum {
        create: 1;
        increase: 2;
        decrease: 3;
        delete: 4;
        transfer: 5;
        reject: 6;
    }
    export type ManufacturingAllocationUpdateAction = keyof ManufacturingAllocationUpdateAction$Enum;
    export interface OperationStatus$Enum {
        pending: 1;
        inProgress: 2;
        completed: 3;
        excluded: 4;
        included: 5;
    }
    export type OperationStatus = keyof OperationStatus$Enum;
    export interface ReleasedItemStatus$Enum {
        pending: 1;
        inProgress: 2;
        completed: 3;
        excluded: 4;
        included: 5;
    }
    export type ReleasedItemStatus = keyof ReleasedItemStatus$Enum;
    export interface WorkInProgressInquiryStatus$Enum {
        inProgress: 0;
        completed: 1;
        draft: 2;
        error: 3;
    }
    export type WorkInProgressInquiryStatus = keyof WorkInProgressInquiryStatus$Enum;
    export interface WorkInProgressStatus$Enum {
        pending: 1;
        posted: 2;
        error: 3;
    }
    export type WorkInProgressStatus = keyof WorkInProgressStatus$Enum;
    export interface WorkInProgressType$Enum {
        materialTracking: 1;
        setupTimeTracking: 2;
        runTimeTracking: 3;
        productionTracking: 4;
        workOrderIndirectCost: 5;
        workOrderVariance: 6;
        workOrderNegativeVariance: 7;
        workOrderActualCostAdjustment: 8;
        workOrderNegativeActualCostAdjustment: 9;
        workOrderActualCostAdjustmentNonAbsorbed: 10;
        workOrderNegativeActualCostAdjustmentNonAbsorbed: 11;
    }
    export type WorkInProgressType = keyof WorkInProgressType$Enum;
    export interface WorkOrderFilteredType$Enum {
        firm: 1;
        planned: 2;
    }
    export type WorkOrderFilteredType = keyof WorkOrderFilteredType$Enum;
    export interface WorkOrderLineStatusFiltered$Enum {
        pending: 1;
        inProgress: 2;
        included: 5;
    }
    export type WorkOrderLineStatusFiltered = keyof WorkOrderLineStatusFiltered$Enum;
    export interface WorkOrderLineStatusFilteredForTracking$Enum {
        pending: 1;
        inProgress: 2;
        completed: 3;
    }
    export type WorkOrderLineStatusFilteredForTracking = keyof WorkOrderLineStatusFilteredForTracking$Enum;
    export interface WorkOrderLineStatusOther$Enum {
        completed: 3;
        excluded: 4;
    }
    export type WorkOrderLineStatusOther = keyof WorkOrderLineStatusOther$Enum;
    export interface WorkOrderSchedulingStatus$Enum {
        notScheduled: 1;
        inProgress: 2;
        scheduled: 3;
        toReschedule: 4;
        notManaged: 5;
    }
    export type WorkOrderSchedulingStatus = keyof WorkOrderSchedulingStatus$Enum;
    export interface WorkOrderStatus$Enum {
        pending: 1;
        inProgress: 2;
        completed: 3;
        closed: 4;
        costCalculated: 5;
        printed: 6;
    }
    export type WorkOrderStatus = keyof WorkOrderStatus$Enum;
    export interface WorkOrderType$Enum {
        planned: 2;
        firm: 1;
    }
    export type WorkOrderType = keyof WorkOrderType$Enum;
    export interface MaterialTracking extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        workOrder: WorkOrder;
        site: Site;
        entryDate: string;
        effectiveDate: string;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        lines: ClientCollection<MaterialTrackingLine>;
        stockTransactionStatus: StockDocumentTransactionStatus;
    }
    export interface MaterialTrackingInput extends ClientNodeInput {
        number?: string;
        workOrder?: integer | string;
        site?: integer | string;
        entryDate?: string;
        effectiveDate?: string;
        lines?: Partial<MaterialTrackingLineInput>[];
    }
    export interface MaterialTrackingBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        workOrder: WorkOrder;
        site: Site;
        entryDate: string;
        effectiveDate: string;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        lines: ClientCollection<MaterialTrackingLineBinding>;
        stockTransactionStatus: StockDocumentTransactionStatus;
    }
    export interface MaterialTracking$Mutations {
        createMaterialTrackings: Node$Operation<
            {
                workOrder: string;
                workOrderTrackingNumber: string;
                trackingQuantity: decimal | string;
                trackingDate?: string;
                componentNumber?: integer | string;
                isAutomated?: boolean | string;
                isMaterialTrackingCreationAllowedForSerialNumberManagedItems?: boolean | string;
            },
            {
                materialTracking: MaterialTracking;
                concatenatedMessages: string;
            }
        >;
        createSingleMaterialTracking: Node$Operation<
            {
                data: {
                    component?: integer | string;
                    workOrderTrackingNumber?: string;
                    quantity?: decimal | string;
                    date?: string;
                    storedDimensions?: string;
                    storedAttributes?: string;
                    isCompleted?: boolean | string;
                    isStandaloneLine?: boolean | string;
                    isAutomated?: boolean | string;
                    isMaterialTrackingCreationAllowedForSerialNumberManagedItem?: boolean | string;
                    allocationUpdates?: {
                        action: ManufacturingAllocationUpdateAction;
                        stockRecord?: integer | string;
                        allocationRecord?: integer | string;
                        quantity?: decimal | string;
                        quantityToTransfer?: decimal | string;
                        serialNumbers?: string[];
                    }[];
                };
            },
            {
                materialTracking: MaterialTracking;
                message: string;
            }
        >;
        createMultipleMaterialTrackings: Node$Operation<
            {
                data?: {
                    workOrder?: integer | string;
                    componentNumber?: (integer | string) | null;
                    item?: integer | string;
                    date?: string;
                    remainingQuantity?: decimal | string;
                    storedDimensions?: string | null;
                    storedAttributes?: string | null;
                    isCompleted?: boolean | string;
                    isAutomated?: boolean | string;
                    allocationUpdates?: {
                        action: ManufacturingAllocationUpdateAction;
                        stockRecord?: integer | string;
                        allocationRecord?: integer | string;
                        quantity?: decimal | string;
                        quantityToTransfer?: decimal | string;
                        serialNumbers?: string[];
                    }[];
                }[];
            },
            {
                numberTrackings: integer;
                concatenatedMessages: string;
            }
        >;
        postToStock: Node$Operation<
            {
                documentIds?: (integer | string)[];
            },
            string
        >;
        resendNotificationForFinance: Node$Operation<
            {
                materialTracking: string;
            },
            boolean
        >;
    }
    export interface MaterialTracking$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MaterialTracking$Lookups {
        workOrder: QueryOperation<WorkOrder>;
        site: QueryOperation<Site>;
        financialSite: QueryOperation<Site>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface MaterialTracking$Operations {
        query: QueryOperation<MaterialTracking>;
        read: ReadOperation<MaterialTracking>;
        aggregate: {
            read: AggregateReadOperation<MaterialTracking>;
            query: AggregateQueryOperation<MaterialTracking>;
        };
        create: CreateOperation<MaterialTrackingInput, MaterialTracking>;
        getDuplicate: GetDuplicateOperation<MaterialTracking>;
        update: UpdateOperation<MaterialTrackingInput, MaterialTracking>;
        updateById: UpdateByIdOperation<MaterialTrackingInput, MaterialTracking>;
        mutations: MaterialTracking$Mutations;
        asyncOperations: MaterialTracking$AsyncOperations;
        lookups(dataOrId: string | { data: MaterialTrackingInput }): MaterialTracking$Lookups;
        getDefaults: GetDefaultsOperation<MaterialTracking>;
    }
    export interface OperationTracking extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        workOrder: WorkOrder;
        site: Site;
        entryDate: string;
        effectiveDate: string;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        lines: ClientCollection<OperationTrackingLine>;
    }
    export interface OperationTrackingInput extends ClientNodeInput {
        number?: string;
        workOrder?: integer | string;
        site?: integer | string;
        entryDate?: string;
        effectiveDate?: string;
        lines?: Partial<OperationTrackingLineInput>[];
    }
    export interface OperationTrackingBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        workOrder: WorkOrder;
        site: Site;
        entryDate: string;
        effectiveDate: string;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        lines: ClientCollection<OperationTrackingLineBinding>;
    }
    export interface OperationTracking$Mutations {
        createOperationTrackings: Node$Operation<
            {
                workOrder: string;
                workOrderTrackingNumber: string;
                trackingQuantity: decimal | string;
                trackingDate?: string;
                operationNumber?: integer | string;
            },
            OperationTracking
        >;
        resendNotificationForFinance: Node$Operation<
            {
                operationTracking: string;
            },
            boolean
        >;
    }
    export interface OperationTracking$AsyncOperations {
        createMultipleOperationalTrackings: AsyncOperation<
            {
                trackings: {
                    workOrderOperation?: {
                        _id?: string;
                        workOrder?: integer | string;
                        operationNumber?: integer | string;
                        name?: string;
                        remainingQuantity?: decimal | string;
                        minCapabilityLevel?: integer | string;
                    };
                    resource?: integer | string;
                    expectedResource?: integer | string;
                    setupTimeUnit?: integer | string;
                    runTimeUnit?: integer | string;
                    status?: OperationStatus;
                    completedRunTime?: string;
                    plannedQuantity?: decimal | string;
                    completedQuantity?: string;
                    completedSetupTime?: decimal | string;
                    actualQuantity?: decimal | string;
                    actualSetupTime?: decimal | string;
                    actualRunTime?: decimal | string;
                    isCompleted?: boolean | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
            },
            integer[]
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface OperationTracking$Lookups {
        workOrder: QueryOperation<WorkOrder>;
        site: QueryOperation<Site>;
        financialSite: QueryOperation<Site>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface OperationTracking$Operations {
        query: QueryOperation<OperationTracking>;
        read: ReadOperation<OperationTracking>;
        aggregate: {
            read: AggregateReadOperation<OperationTracking>;
            query: AggregateQueryOperation<OperationTracking>;
        };
        create: CreateOperation<OperationTrackingInput, OperationTracking>;
        getDuplicate: GetDuplicateOperation<OperationTracking>;
        mutations: OperationTracking$Mutations;
        asyncOperations: OperationTracking$AsyncOperations;
        lookups(dataOrId: string | { data: OperationTrackingInput }): OperationTracking$Lookups;
        getDefaults: GetDefaultsOperation<OperationTracking>;
    }
    export interface ProductionTracking extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        workOrder: WorkOrder;
        site: Site;
        entryDate: string;
        effectiveDate: string;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        lines: ClientCollection<ProductionTrackingLine>;
        stockTransactionStatus: StockDocumentTransactionStatus;
    }
    export interface ProductionTrackingInput extends ClientNodeInput {
        number?: string;
        workOrder?: integer | string;
        site?: integer | string;
        entryDate?: string;
        effectiveDate?: string;
        lines?: Partial<ProductionTrackingLineInput>[];
    }
    export interface ProductionTrackingBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        number: string;
        workOrder: WorkOrder;
        site: Site;
        entryDate: string;
        effectiveDate: string;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        lines: ClientCollection<ProductionTrackingLineBinding>;
        stockTransactionStatus: StockDocumentTransactionStatus;
    }
    export interface ProductionTracking$Mutations {
        createWorkOrderTracking: Node$Operation<
            {
                data: {
                    itemCode: string;
                    workOrderNumber: string;
                    workOrderTrackingNumber: string;
                    trackingQuantity: decimal | string;
                    trackingDate?: string;
                    materialTracking?: boolean | string;
                    timeTracking?: boolean | string;
                    completed?: boolean | string;
                    stockDetails?: {
                        effectiveDate?: string;
                        site: integer | string;
                        item: integer | string;
                        stockUnit: integer | string;
                        status: integer | string;
                        location?: integer | string;
                        lotCreateData?: string;
                        existingLot?: integer | string;
                        owner?: string;
                        quantityInStockUnit: decimal | string;
                    }[];
                    storedDimensions?: string;
                    storedAttributes?: string;
                };
            },
            {
                workOrder: WorkOrder;
                message: string;
                materialTracking: MaterialTracking;
                timeTracking: {
                    _id: string;
                    number: string;
                };
                productionTracking: {
                    _id: string;
                    number: string;
                };
            }
        >;
        createSingleProductionTracking: Node$Operation<
            {
                releasedItem: string;
                workOrderTrackingNumber: string;
                trackingQuantity: decimal | string;
                trackingDate?: string;
                completed?: boolean | string;
                stockDetails?: {
                    effectiveDate?: string;
                    site: integer | string;
                    item: integer | string;
                    stockUnit: integer | string;
                    status: integer | string;
                    location?: integer | string;
                    existingLot?: integer | string;
                    lotCreateData?: string;
                    owner?: string;
                    quantityInStockUnit: decimal | string;
                }[];
                storedDimensions?: string;
                storedAttributes?: string;
            },
            ProductionTrackingLine
        >;
        createMultipleWorkOrderTrackings: Node$Operation<
            {
                trackings: string;
                trackingDate?: string;
            },
            {
                numberTrackings: integer;
                message: string;
            }
        >;
        postToStock: Node$Operation<
            {
                documentIds?: (integer | string)[];
            },
            string
        >;
        resendNotificationForFinance: Node$Operation<
            {
                productionTracking: string;
            },
            boolean
        >;
    }
    export interface ProductionTracking$AsyncOperations {
        createTestTracking: AsyncOperation<
            {
                orderNumberRoot?: string;
                quantityRatio?: decimal | string;
            },
            string
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ProductionTracking$Lookups {
        workOrder: QueryOperation<WorkOrder>;
        site: QueryOperation<Site>;
        financialSite: QueryOperation<Site>;
        transactionCurrency: QueryOperation<Currency>;
    }
    export interface ProductionTracking$Operations {
        query: QueryOperation<ProductionTracking>;
        read: ReadOperation<ProductionTracking>;
        aggregate: {
            read: AggregateReadOperation<ProductionTracking>;
            query: AggregateQueryOperation<ProductionTracking>;
        };
        create: CreateOperation<ProductionTrackingInput, ProductionTracking>;
        getDuplicate: GetDuplicateOperation<ProductionTracking>;
        update: UpdateOperation<ProductionTrackingInput, ProductionTracking>;
        updateById: UpdateByIdOperation<ProductionTrackingInput, ProductionTracking>;
        mutations: ProductionTracking$Mutations;
        asyncOperations: ProductionTracking$AsyncOperations;
        lookups(dataOrId: string | { data: ProductionTrackingInput }): ProductionTracking$Lookups;
        getDefaults: GetDefaultsOperation<ProductionTracking>;
    }
    export interface WorkInProgressCost extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        originatingLine: BaseDocumentLine;
        operationTrackingLine: OperationTrackingLine;
        actualResource: DetailedResource;
        workOrder: WorkOrder;
        quantity: string;
        unit: UnitOfMeasure;
        cost: string;
        amount: string;
        currency: Currency;
        status: WorkInProgressStatus;
        effectiveDate: string;
        postingDate: string;
        type: WorkInProgressType;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        item: Item;
    }
    export interface WorkInProgressCostInput extends ClientNodeInput {
        originatingLine?: integer | string;
        workOrder?: integer | string;
        quantity?: decimal | string;
        unit?: integer | string;
        cost?: decimal | string;
        amount?: decimal | string;
        currency?: integer | string;
        status?: WorkInProgressStatus;
        effectiveDate?: string;
        postingDate?: string;
        type?: WorkInProgressType;
    }
    export interface WorkInProgressCostBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        originatingLine: BaseDocumentLine;
        operationTrackingLine: OperationTrackingLine;
        actualResource: DetailedResource;
        workOrder: WorkOrder;
        quantity: string;
        unit: UnitOfMeasure;
        cost: string;
        amount: string;
        currency: Currency;
        status: WorkInProgressStatus;
        effectiveDate: string;
        postingDate: string;
        type: WorkInProgressType;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        item: Item;
    }
    export interface WorkInProgressCost$Queries {
        getCostProduced: Node$Operation<
            {
                currency?: string;
                reportMonths?: integer | string;
            },
            {
                month: string;
                cost: string;
            }[]
        >;
    }
    export interface WorkInProgressCost$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkInProgressCost$Lookups {
        originatingLine: QueryOperation<BaseDocumentLine>;
        operationTrackingLine: QueryOperation<OperationTrackingLine>;
        actualResource: QueryOperation<DetailedResource>;
        workOrder: QueryOperation<WorkOrder>;
        unit: QueryOperation<UnitOfMeasure>;
        currency: QueryOperation<Currency>;
        item: QueryOperation<Item>;
    }
    export interface WorkInProgressCost$Operations {
        query: QueryOperation<WorkInProgressCost>;
        read: ReadOperation<WorkInProgressCost>;
        aggregate: {
            read: AggregateReadOperation<WorkInProgressCost>;
            query: AggregateQueryOperation<WorkInProgressCost>;
        };
        queries: WorkInProgressCost$Queries;
        create: CreateOperation<WorkInProgressCostInput, WorkInProgressCost>;
        getDuplicate: GetDuplicateOperation<WorkInProgressCost>;
        update: UpdateOperation<WorkInProgressCostInput, WorkInProgressCost>;
        updateById: UpdateByIdOperation<WorkInProgressCostInput, WorkInProgressCost>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: WorkInProgressCost$AsyncOperations;
        lookups(dataOrId: string | { data: WorkInProgressCostInput }): WorkInProgressCost$Lookups;
        getDefaults: GetDefaultsOperation<WorkInProgressCost>;
    }
    export interface WorkInProgressInputSet extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        user: User;
        site: Site;
        fromWorkOrder: WorkOrder;
        toWorkOrder: WorkOrder;
        asOfDate: string;
        status: WorkInProgressInquiryStatus;
        executionDate: string;
        currency: Currency;
        workInProgressTotalAmount: string;
        lines: ClientCollection<WorkInProgressResultLine>;
    }
    export interface WorkInProgressInputSetInput extends ClientNodeInput {
        user?: integer | string;
        site?: integer | string;
        fromWorkOrder?: integer | string;
        toWorkOrder?: integer | string;
        asOfDate?: string;
        status?: WorkInProgressInquiryStatus;
        executionDate?: string;
        lines?: Partial<WorkInProgressResultLineInput>[];
    }
    export interface WorkInProgressInputSetBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        user: User;
        site: Site;
        fromWorkOrder: WorkOrder;
        toWorkOrder: WorkOrder;
        asOfDate: string;
        status: WorkInProgressInquiryStatus;
        executionDate: string;
        currency: Currency;
        workInProgressTotalAmount: string;
        lines: ClientCollection<WorkInProgressResultLineBinding>;
    }
    export interface WorkInProgressInputSet$AsyncOperations {
        wipInquiry: AsyncOperation<
            {
                userId?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkInProgressInputSet$Lookups {
        user: QueryOperation<User>;
        site: QueryOperation<Site>;
        fromWorkOrder: QueryOperation<WorkOrder>;
        toWorkOrder: QueryOperation<WorkOrder>;
        currency: QueryOperation<Currency>;
    }
    export interface WorkInProgressInputSet$Operations {
        query: QueryOperation<WorkInProgressInputSet>;
        read: ReadOperation<WorkInProgressInputSet>;
        aggregate: {
            read: AggregateReadOperation<WorkInProgressInputSet>;
            query: AggregateQueryOperation<WorkInProgressInputSet>;
        };
        create: CreateOperation<WorkInProgressInputSetInput, WorkInProgressInputSet>;
        getDuplicate: GetDuplicateOperation<WorkInProgressInputSet>;
        update: UpdateOperation<WorkInProgressInputSetInput, WorkInProgressInputSet>;
        updateById: UpdateByIdOperation<WorkInProgressInputSetInput, WorkInProgressInputSet>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: WorkInProgressInputSet$AsyncOperations;
        lookups(dataOrId: string | { data: WorkInProgressInputSetInput }): WorkInProgressInputSet$Lookups;
        getDefaults: GetDefaultsOperation<WorkInProgressInputSet>;
    }
    export interface WorkInProgressResultLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        inputSet: WorkInProgressInputSet;
        workOrder: WorkOrder;
        item: WorkOrderReleasedItem;
        status: WorkOrderStatus;
        productionCost: string;
        plannedMaterialCost: string;
        actualMaterialCost: string;
        materialCostVariance: string;
        materialCostVariancePercentage: string;
        plannedProcessCost: string;
        plannedMachineCost: string;
        actualMachineCost: string;
        machineCostVariance: string;
        machineCostVariancePercentage: string;
        plannedLaborCost: string;
        actualLaborCost: string;
        laborCostVariance: string;
        laborCostVariancePercentage: string;
        plannedToolCost: string;
        actualToolCost: string;
        toolCostVariance: string;
        toolCostVariancePercentage: string;
        totalPlannedCost: string;
        positiveClosingVariance: string;
        negativeClosingVariance: string;
        closingVariance: string;
        isStockJournalAvailable: boolean;
        isJournalEntryAvailable: boolean;
        actualProcessCost: string;
        processCostVariance: string;
        processCostVariancePercentage: string;
        totalActualCost: string;
        totalCostVariance: string;
        totalCostVariancePercentage: string;
        workInProgressTotal: string;
    }
    export interface WorkInProgressResultLineInput extends VitalClientNodeInput {
        workOrder?: integer | string;
        item?: integer | string;
        status?: WorkOrderStatus;
        productionCost?: decimal | string;
        plannedMaterialCost?: decimal | string;
        actualMaterialCost?: decimal | string;
        plannedProcessCost?: decimal | string;
        plannedMachineCost?: decimal | string;
        actualMachineCost?: decimal | string;
        plannedLaborCost?: decimal | string;
        actualLaborCost?: decimal | string;
        plannedToolCost?: decimal | string;
        actualToolCost?: decimal | string;
        positiveClosingVariance?: decimal | string;
        negativeClosingVariance?: decimal | string;
        isStockJournalAvailable?: boolean | string;
        isJournalEntryAvailable?: boolean | string;
    }
    export interface WorkInProgressResultLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        inputSet: WorkInProgressInputSet;
        workOrder: WorkOrder;
        item: WorkOrderReleasedItem;
        status: WorkOrderStatus;
        productionCost: string;
        plannedMaterialCost: string;
        actualMaterialCost: string;
        materialCostVariance: string;
        materialCostVariancePercentage: string;
        plannedProcessCost: string;
        plannedMachineCost: string;
        actualMachineCost: string;
        machineCostVariance: string;
        machineCostVariancePercentage: string;
        plannedLaborCost: string;
        actualLaborCost: string;
        laborCostVariance: string;
        laborCostVariancePercentage: string;
        plannedToolCost: string;
        actualToolCost: string;
        toolCostVariance: string;
        toolCostVariancePercentage: string;
        totalPlannedCost: string;
        positiveClosingVariance: string;
        negativeClosingVariance: string;
        closingVariance: string;
        isStockJournalAvailable: boolean;
        isJournalEntryAvailable: boolean;
        actualProcessCost: string;
        processCostVariance: string;
        processCostVariancePercentage: string;
        totalActualCost: string;
        totalCostVariance: string;
        totalCostVariancePercentage: string;
        workInProgressTotal: string;
    }
    export interface WorkInProgressResultLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkInProgressResultLine$Lookups {
        workOrder: QueryOperation<WorkOrder>;
        item: QueryOperation<WorkOrderReleasedItem>;
    }
    export interface WorkInProgressResultLine$Operations {
        query: QueryOperation<WorkInProgressResultLine>;
        read: ReadOperation<WorkInProgressResultLine>;
        aggregate: {
            read: AggregateReadOperation<WorkInProgressResultLine>;
            query: AggregateQueryOperation<WorkInProgressResultLine>;
        };
        asyncOperations: WorkInProgressResultLine$AsyncOperations;
        lookups(dataOrId: string | { data: WorkInProgressResultLineInput }): WorkInProgressResultLine$Lookups;
        getDefaults: GetDefaultsOperation<WorkInProgressResultLine>;
    }
    export interface WorkOrder extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        site: Site;
        number: string;
        type: WorkOrderType;
        typeFiltered: WorkOrderFilteredType;
        status: WorkOrderStatus;
        isForwardScheduling: boolean;
        isSchedulingSkipped: boolean;
        timeZone: string;
        schedulingStatus: WorkOrderSchedulingStatus;
        creationDate: string;
        requestedDate: string;
        startDatetime: string;
        startDate: string;
        endDatetime: string;
        endDate: string;
        closingDate: string;
        category: WorkOrderCategory;
        bomCode: BillOfMaterial;
        bomRevision: BillOfMaterialRevision;
        routingCode: Routing;
        routingTimeUnit: UnitOfMeasure;
        baseQuantity: string;
        baseRoutingQuantity: string;
        actualProcessCost: string;
        actualMaterialCost: string;
        actualOverheadCost: string;
        wipVariance: string;
        processCompletionPercentage: string;
        materialCompletionPercentage: string;
        productionCompletionPercentage: string;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        productionItems: ClientCollection<WorkOrderReleasedItem>;
        productionOperations: ClientCollection<WorkOrderOperation>;
        productionOperationResources: ClientCollection<WorkOrderOperationResource>;
        productionComponents: ClientCollection<WorkOrderComponent>;
        productionItem: WorkOrderReleasedItem;
        productionStepOperation: WorkOrderOperation;
        materialTrackings: ClientCollection<MaterialTracking>;
        productionTrackings: ClientCollection<ProductionTracking>;
        timeTrackings: ClientCollection<OperationTracking>;
        timeUnit: TimeUnit;
        allocationStatus: StockAllocationStatus;
        allocationRequestStatus: AllocationRequestStatus;
        isServiceOptionsSerialNumberActive: boolean;
        workOrderSerialNumbers: ClientCollection<WorkOrderSerialNumber>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        note: TextStream;
        postingDetails: ClientCollection<FinanceTransaction>;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        name: string;
        plannedMaterialCost: string;
        plannedMachineCost: string;
        plannedLaborCost: string;
        plannedToolCost: string;
        plannedProcessCost: string;
        plannedOverheadCost: string;
    }
    export interface WorkOrderInput extends ClientNodeInput {
        site?: integer | string;
        number?: string;
        type?: WorkOrderType;
        typeFiltered?: WorkOrderFilteredType;
        status?: WorkOrderStatus;
        doInheritDimensions?: boolean | string;
        isForwardScheduling?: boolean | string;
        isSchedulingSkipped?: boolean | string;
        timeZone?: string;
        schedulingStatus?: WorkOrderSchedulingStatus;
        creationDate?: string;
        requestedDate?: string;
        startDatetime?: string;
        startDate?: string;
        endDatetime?: string;
        endDate?: string;
        closingDate?: string;
        category?: integer | string;
        bomCode?: integer | string;
        bomRevision?: integer | string;
        routingCode?: integer | string;
        routingTimeUnit?: integer | string;
        baseQuantity?: decimal | string;
        baseRoutingQuantity?: decimal | string;
        actualOverheadCost?: decimal | string;
        productionItems?: Partial<WorkOrderReleasedItemInput>[];
        productionOperations?: Partial<WorkOrderOperationInput>[];
        productionComponents?: Partial<WorkOrderComponentInput>[];
        timeUnit?: TimeUnit;
        workOrderSerialNumbers?: Partial<WorkOrderSerialNumberInput>[];
        note?: TextStream;
        _attachments?: Partial<AttachmentAssociationInput>[];
        name?: string;
        plannedOverheadCost?: decimal | string;
    }
    export interface WorkOrderBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        site: Site;
        number: string;
        type: WorkOrderType;
        typeFiltered: WorkOrderFilteredType;
        status: WorkOrderStatus;
        doInheritDimensions: boolean;
        isForwardScheduling: boolean;
        isSchedulingSkipped: boolean;
        timeZone: string;
        schedulingStatus: WorkOrderSchedulingStatus;
        creationDate: string;
        requestedDate: string;
        startDatetime: string;
        startDate: string;
        endDatetime: string;
        endDate: string;
        closingDate: string;
        category: WorkOrderCategory;
        bomCode: BillOfMaterial;
        bomRevision: BillOfMaterialRevision;
        routingCode: Routing;
        routingTimeUnit: UnitOfMeasure;
        baseQuantity: string;
        baseRoutingQuantity: string;
        actualProcessCost: string;
        actualMaterialCost: string;
        actualOverheadCost: string;
        wipVariance: string;
        processCompletionPercentage: string;
        materialCompletionPercentage: string;
        productionCompletionPercentage: string;
        financialSite: Site;
        transactionCurrency: Currency;
        documentDate: string;
        productionItems: ClientCollection<WorkOrderReleasedItemBinding>;
        productionOperations: ClientCollection<WorkOrderOperationBinding>;
        productionOperationResources: ClientCollection<WorkOrderOperationResource>;
        productionComponents: ClientCollection<WorkOrderComponentBinding>;
        productionItem: WorkOrderReleasedItem;
        productionStepOperation: WorkOrderOperation;
        materialTrackings: ClientCollection<MaterialTracking>;
        productionTrackings: ClientCollection<ProductionTracking>;
        timeTrackings: ClientCollection<OperationTracking>;
        timeUnit: TimeUnit;
        allocationStatus: StockAllocationStatus;
        allocationRequestStatus: AllocationRequestStatus;
        isServiceOptionsSerialNumberActive: boolean;
        workOrderSerialNumbers: ClientCollection<WorkOrderSerialNumberBinding>;
        financeIntegrationStatus: FinanceIntegrationStatus;
        note: TextStream;
        postingDetails: ClientCollection<FinanceTransaction>;
        _attachments: ClientCollection<AttachmentAssociation>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        name: string;
        plannedMaterialCost: string;
        plannedMachineCost: string;
        plannedLaborCost: string;
        plannedToolCost: string;
        plannedProcessCost: string;
        plannedOverheadCost: string;
    }
    export interface WorkOrder$Queries {
        getProductionCost: Node$Operation<
            {
                releasedItem: string;
            },
            {
                quantityProduced: string;
                amount: string;
            }
        >;
    }
    export interface WorkOrder$Mutations {
        createWorkOrder: Node$Operation<
            {
                data: {
                    siteId: string;
                    releasedItem: string;
                    releasedQuantity: decimal | string;
                    name: string;
                    type: WorkOrderType;
                    workOrderCategory: integer | string;
                    workOrderNumber?: string;
                    startDate?: string;
                    bom?: string;
                    route?: string;
                    storedDimensions?: string | null;
                    storedAttributes?: string | null;
                    _customData?: string | null;
                };
            },
            WorkOrder
        >;
        scheduleWorkOrder: Node$Operation<
            {
                workOrder: string;
            },
            WorkOrder
        >;
        skipSchedulingWorkOrder: Node$Operation<
            {
                workOrder: string;
                skip?: boolean | string;
            },
            WorkOrder
        >;
        updatePlannedCosts: Node$Operation<
            {
                workOrder: string;
            },
            WorkOrder
        >;
        requestAutoAllocation: Node$Operation<
            {
                data?: {
                    document?: integer | string;
                    requestType?: string;
                };
            },
            string
        >;
        resendNotificationForFinance: Node$Operation<
            {
                workOrder: string;
            },
            boolean
        >;
        addComponentsToWorkOrder: Node$Operation<
            {
                workOrder: string;
                item: string;
                site: string;
            },
            WorkOrder
        >;
        addOperationsToWorkOrder: Node$Operation<
            {
                workOrder: string;
                item: string;
                site: string;
            },
            WorkOrder
        >;
        controlClosingDate: Node$Operation<
            {
                closingDate: string;
                workOrder?: string;
            },
            string
        >;
        preGenerateSerialNumbers: Node$Operation<
            {
                releasedItem: string;
                releasedQuantity: decimal | string;
                site: string;
                productionItem: string;
                checkOnly: boolean | string;
            },
            string
        >;
        repost: Node$Operation<
            {
                workOrder: string;
                financeTransactionSysId: string;
                components?: {
                    baseDocumentLineSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
                releasedItems?: {
                    baseDocumentLineSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
                operationResources?: {
                    baseDocumentLineSysId?: integer | string;
                    workOrderOperationSysId?: integer | string;
                    resourceSysId?: integer | string;
                    storedAttributes?: string;
                    storedDimensions?: string;
                }[];
            },
            {
                wasSuccessful: boolean;
                message: string;
            }
        >;
        beforePrintWorkOrderPickList: Node$Operation<
            {
                order: string;
            },
            boolean
        >;
    }
    export interface WorkOrder$AsyncOperations {
        printBulk: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        bulkPrintPickList: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        closeWorkOrder: AsyncOperation<
            {
                filter?: string;
                closingDate: string;
            },
            boolean
        >;
        trackBillOfMaterial: AsyncOperation<
            {
                data: {
                    bomId: string;
                    siteId: string;
                    itemId: string;
                    itemName: string;
                    quantity: decimal | string;
                    workOrderCategoryId: string;
                    stockDetails: string;
                    date?: string;
                    route?: string;
                };
            },
            BillOfMaterialTracking
        >;
        createTestWorkOrders: AsyncOperation<
            {
                orderQuantity: integer | string;
                bom?: {
                    itemId: string;
                    siteId: string;
                };
                orderNumberRoot?: string;
            },
            string
        >;
        resynchronizeStatus: AsyncOperation<
            {
                workOrder: string;
            },
            {
                oldStatus: WorkOrderStatus;
                newStatus: WorkOrderStatus;
            }
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkOrder$Lookups {
        site: QueryOperation<Site>;
        category: QueryOperation<WorkOrderCategory>;
        bomCode: QueryOperation<BillOfMaterial>;
        bomRevision: QueryOperation<BillOfMaterialRevision>;
        routingCode: QueryOperation<Routing>;
        routingTimeUnit: QueryOperation<UnitOfMeasure>;
        financialSite: QueryOperation<Site>;
        transactionCurrency: QueryOperation<Currency>;
        productionItem: QueryOperation<WorkOrderReleasedItem>;
        productionStepOperation: QueryOperation<WorkOrderOperation>;
    }
    export interface WorkOrder$Operations {
        query: QueryOperation<WorkOrder>;
        read: ReadOperation<WorkOrder>;
        aggregate: {
            read: AggregateReadOperation<WorkOrder>;
            query: AggregateQueryOperation<WorkOrder>;
        };
        queries: WorkOrder$Queries;
        create: CreateOperation<WorkOrderInput, WorkOrder>;
        getDuplicate: GetDuplicateOperation<WorkOrder>;
        duplicate: DuplicateOperation<string, WorkOrderInput, WorkOrder>;
        update: UpdateOperation<WorkOrderInput, WorkOrder>;
        updateById: UpdateByIdOperation<WorkOrderInput, WorkOrder>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: WorkOrder$Mutations;
        asyncOperations: WorkOrder$AsyncOperations;
        lookups(dataOrId: string | { data: WorkOrderInput }): WorkOrder$Lookups;
        getDefaults: GetDefaultsOperation<WorkOrder>;
    }
    export interface WorkOrderCategory extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        description: string;
        name: string;
        routing: boolean;
        billOfMaterial: boolean;
        isDefault: boolean;
    }
    export interface WorkOrderCategoryInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        description?: string;
        name?: string;
        routing?: boolean | string;
        billOfMaterial?: boolean | string;
        isDefault?: boolean | string;
    }
    export interface WorkOrderCategoryBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        description: string;
        name: string;
        routing: boolean;
        billOfMaterial: boolean;
        isDefault: boolean;
    }
    export interface WorkOrderCategory$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkOrderCategory$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface WorkOrderCategory$Operations {
        query: QueryOperation<WorkOrderCategory>;
        read: ReadOperation<WorkOrderCategory>;
        aggregate: {
            read: AggregateReadOperation<WorkOrderCategory>;
            query: AggregateQueryOperation<WorkOrderCategory>;
        };
        create: CreateOperation<WorkOrderCategoryInput, WorkOrderCategory>;
        getDuplicate: GetDuplicateOperation<WorkOrderCategory>;
        duplicate: DuplicateOperation<string, WorkOrderCategoryInput, WorkOrderCategory>;
        update: UpdateOperation<WorkOrderCategoryInput, WorkOrderCategory>;
        updateById: UpdateByIdOperation<WorkOrderCategoryInput, WorkOrderCategory>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: WorkOrderCategory$AsyncOperations;
        lookups(dataOrId: string | { data: WorkOrderCategoryInput }): WorkOrderCategory$Lookups;
        getDefaults: GetDefaultsOperation<WorkOrderCategory>;
    }
    export interface WorkOrderOperation extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        workOrder: WorkOrder;
        operationNumber: integer;
        name: string;
        setupTimeUnit: UnitOfMeasure;
        runTimeUnit: UnitOfMeasure;
        expectedSetupTime: string;
        expectedRunTime: string;
        expectedBatchCost: string;
        expectedBatchRunCost: string;
        expectedMachineCost: string;
        expectedLaborCost: string;
        expectedToolCost: string;
        expectedBatchSetupCost: string;
        expectedMachineSetupCost: string;
        expectedLaborSetupCost: string;
        expectedToolSetupCost: string;
        actualSetupTime: string;
        actualRunTime: string;
        actualBatchCost: string;
        actualBatchRunCost: string;
        actualMachineCost: string;
        actualLaborCost: string;
        actualToolCost: string;
        actualBatchSetupCost: string;
        actualMachineSetupCost: string;
        actualLaborSetupCost: string;
        actualToolSetupCost: string;
        resourceGroupNumber: integer;
        minCapabilityLevel: CapabilityLevel;
        resources: ClientCollection<WorkOrderOperationResource>;
        isProductionStep: boolean;
        status: OperationStatus;
        uStatus: WorkOrderLineStatusFiltered;
        isAdded: boolean;
        startDatetime: string;
        startDate: string;
        endDatetime: string;
        endDate: string;
        plannedQuantity: string;
        completedQuantity: string;
        completedQuantityPercentage: string;
        completedTimePercentage: string;
        remainingQuantity: string;
        operation: Operation;
        instruction: TextStream;
    }
    export interface WorkOrderOperationInput extends VitalClientNodeInput {
        operationNumber?: integer | string;
        name?: string;
        setupTimeUnit?: integer | string;
        runTimeUnit?: integer | string;
        minCapabilityLevel?: integer | string;
        resources?: Partial<WorkOrderOperationResourceInput>[];
        isProductionStep?: boolean | string;
        status?: OperationStatus;
        isAdded?: boolean | string;
        startDatetime?: string;
        startDate?: string;
        endDatetime?: string;
        endDate?: string;
        plannedQuantity?: decimal | string;
        completedQuantity?: decimal | string;
        instruction?: TextStream;
    }
    export interface WorkOrderOperationBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        workOrder: WorkOrder;
        operationNumber: integer;
        name: string;
        setupTimeUnit: UnitOfMeasure;
        runTimeUnit: UnitOfMeasure;
        expectedSetupTime: string;
        expectedRunTime: string;
        expectedBatchCost: string;
        expectedBatchRunCost: string;
        expectedMachineCost: string;
        expectedLaborCost: string;
        expectedToolCost: string;
        expectedBatchSetupCost: string;
        expectedMachineSetupCost: string;
        expectedLaborSetupCost: string;
        expectedToolSetupCost: string;
        actualSetupTime: string;
        actualRunTime: string;
        actualBatchCost: string;
        actualBatchRunCost: string;
        actualMachineCost: string;
        actualLaborCost: string;
        actualToolCost: string;
        actualBatchSetupCost: string;
        actualMachineSetupCost: string;
        actualLaborSetupCost: string;
        actualToolSetupCost: string;
        resourceGroupNumber: integer;
        minCapabilityLevel: CapabilityLevel;
        resources: ClientCollection<WorkOrderOperationResourceBinding>;
        isProductionStep: boolean;
        status: OperationStatus;
        uStatus: WorkOrderLineStatusFiltered;
        isAdded: boolean;
        startDatetime: string;
        startDate: string;
        endDatetime: string;
        endDate: string;
        plannedQuantity: string;
        completedQuantity: string;
        completedQuantityPercentage: string;
        completedTimePercentage: string;
        remainingQuantity: string;
        operation: Operation;
        instruction: TextStream;
    }
    export interface WorkOrderOperation$Mutations {
        updateOperation: Node$Operation<
            {
                data?: {
                    workOrder?: integer | string;
                    operation?: integer | string;
                    quantity?: decimal | string;
                    setupTime?: decimal | string;
                    runTime?: decimal | string;
                    actualResource?: integer | string;
                    completed?: boolean | string;
                };
            },
            WorkOrderOperation
        >;
    }
    export interface WorkOrderOperation$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkOrderOperation$Lookups {
        setupTimeUnit: QueryOperation<UnitOfMeasure>;
        runTimeUnit: QueryOperation<UnitOfMeasure>;
        minCapabilityLevel: QueryOperation<CapabilityLevel>;
        operation: QueryOperation<Operation>;
    }
    export interface WorkOrderOperation$Operations {
        query: QueryOperation<WorkOrderOperation>;
        read: ReadOperation<WorkOrderOperation>;
        aggregate: {
            read: AggregateReadOperation<WorkOrderOperation>;
            query: AggregateQueryOperation<WorkOrderOperation>;
        };
        create: CreateOperation<WorkOrderOperationInput, WorkOrderOperation>;
        getDuplicate: GetDuplicateOperation<WorkOrderOperation>;
        duplicate: DuplicateOperation<string, WorkOrderOperationInput, WorkOrderOperation>;
        update: UpdateOperation<WorkOrderOperationInput, WorkOrderOperation>;
        updateById: UpdateByIdOperation<WorkOrderOperationInput, WorkOrderOperation>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: WorkOrderOperation$Mutations;
        asyncOperations: WorkOrderOperation$AsyncOperations;
        lookups(dataOrId: string | { data: WorkOrderOperationInput }): WorkOrderOperation$Lookups;
        getDefaults: GetDefaultsOperation<WorkOrderOperation>;
    }
    export interface WorkOrderOperationResource extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        workOrderOperation: WorkOrderOperation;
        workOrder: WorkOrder;
        resource: BaseResource;
        efficiency: string;
        setupTimeUnit: UnitOfMeasure;
        runTimeUnit: UnitOfMeasure;
        expectedRunTime: string;
        expectedSetupTime: string;
        actualRunTime: string;
        actualSetupTime: string;
        actualRunCost: string;
        actualSetupCost: string;
        resourceNumber: integer;
        resources: ClientCollection<WorkOrderOperationResourceDetail>;
        status: OperationStatus;
        uStatus: WorkOrderLineStatusFiltered;
        isAdded: boolean;
        completedTimePercentage: string;
        isResourceQuantity: boolean;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        startDatetime: string;
        endDatetime: string;
        expectedRunCost: string;
        expectedSetupCost: string;
        actualCost: string;
        analyticalData: AnalyticalData;
        expectedCost: string;
    }
    export interface WorkOrderOperationResourceInput extends VitalClientNodeInput {
        resource?: integer | string;
        efficiency?: decimal | string;
        expectedRunTime?: decimal | string;
        expectedSetupTime?: decimal | string;
        actualRunTime?: decimal | string;
        actualSetupTime?: decimal | string;
        actualRunCost?: decimal | string;
        actualSetupCost?: decimal | string;
        resources?: Partial<WorkOrderOperationResourceDetailInput>[];
        status?: OperationStatus;
        isAdded?: boolean | string;
        isResourceQuantity?: boolean | string;
        storedDimensions?: string;
        storedAttributes?: string;
        startDatetime?: string;
        endDatetime?: string;
        expectedRunCost?: decimal | string;
        expectedSetupCost?: decimal | string;
        analyticalData?: integer | string;
    }
    export interface WorkOrderOperationResourceBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        workOrderOperation: WorkOrderOperation;
        workOrder: WorkOrder;
        resource: BaseResource;
        efficiency: string;
        setupTimeUnit: UnitOfMeasure;
        runTimeUnit: UnitOfMeasure;
        expectedRunTime: string;
        expectedSetupTime: string;
        actualRunTime: string;
        actualSetupTime: string;
        actualRunCost: string;
        actualSetupCost: string;
        resourceNumber: integer;
        resources: ClientCollection<WorkOrderOperationResourceDetailBinding>;
        status: OperationStatus;
        uStatus: WorkOrderLineStatusFiltered;
        isAdded: boolean;
        completedTimePercentage: string;
        isResourceQuantity: boolean;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        startDatetime: string;
        endDatetime: string;
        expectedRunCost: string;
        expectedSetupCost: string;
        actualCost: string;
        analyticalData: AnalyticalData;
        expectedCost: string;
    }
    export interface WorkOrderOperationResource$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkOrderOperationResource$Lookups {
        workOrder: QueryOperation<WorkOrder>;
        resource: QueryOperation<BaseResource>;
        setupTimeUnit: QueryOperation<UnitOfMeasure>;
        runTimeUnit: QueryOperation<UnitOfMeasure>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface WorkOrderOperationResource$Operations {
        query: QueryOperation<WorkOrderOperationResource>;
        read: ReadOperation<WorkOrderOperationResource>;
        aggregate: {
            read: AggregateReadOperation<WorkOrderOperationResource>;
            query: AggregateQueryOperation<WorkOrderOperationResource>;
        };
        asyncOperations: WorkOrderOperationResource$AsyncOperations;
        lookups(dataOrId: string | { data: WorkOrderOperationResourceInput }): WorkOrderOperationResource$Lookups;
        getDefaults: GetDefaultsOperation<WorkOrderOperationResource>;
    }
    export interface WorkOrderOperationResourceDetail extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        workOrderOperationResource: WorkOrderOperationResource;
        resource: DetailedResource;
    }
    export interface WorkOrderOperationResourceDetailInput extends VitalClientNodeInput {
        resource?: integer | string;
    }
    export interface WorkOrderOperationResourceDetailBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        workOrderOperationResource: WorkOrderOperationResource;
        resource: DetailedResource;
    }
    export interface WorkOrderOperationResourceDetail$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkOrderOperationResourceDetail$Lookups {
        resource: QueryOperation<DetailedResource>;
    }
    export interface WorkOrderOperationResourceDetail$Operations {
        query: QueryOperation<WorkOrderOperationResourceDetail>;
        read: ReadOperation<WorkOrderOperationResourceDetail>;
        aggregate: {
            read: AggregateReadOperation<WorkOrderOperationResourceDetail>;
            query: AggregateQueryOperation<WorkOrderOperationResourceDetail>;
        };
        create: CreateOperation<WorkOrderOperationResourceDetailInput, WorkOrderOperationResourceDetail>;
        getDuplicate: GetDuplicateOperation<WorkOrderOperationResourceDetail>;
        duplicate: DuplicateOperation<string, WorkOrderOperationResourceDetailInput, WorkOrderOperationResourceDetail>;
        update: UpdateOperation<WorkOrderOperationResourceDetailInput, WorkOrderOperationResourceDetail>;
        updateById: UpdateByIdOperation<WorkOrderOperationResourceDetailInput, WorkOrderOperationResourceDetail>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: WorkOrderOperationResourceDetail$AsyncOperations;
        lookups(
            dataOrId: string | { data: WorkOrderOperationResourceDetailInput },
        ): WorkOrderOperationResourceDetail$Lookups;
        getDefaults: GetDefaultsOperation<WorkOrderOperationResourceDetail>;
    }
    export interface WorkOrderSerialNumber extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        workOrder: WorkOrder;
        serialNumber: SerialNumber;
    }
    export interface WorkOrderSerialNumberInput extends VitalClientNodeInput {
        serialNumber?: integer | string;
    }
    export interface WorkOrderSerialNumberBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        workOrder: WorkOrder;
        serialNumber: SerialNumber;
    }
    export interface WorkOrderSerialNumber$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkOrderSerialNumber$Lookups {
        serialNumber: QueryOperation<SerialNumber>;
    }
    export interface WorkOrderSerialNumber$Operations {
        query: QueryOperation<WorkOrderSerialNumber>;
        read: ReadOperation<WorkOrderSerialNumber>;
        aggregate: {
            read: AggregateReadOperation<WorkOrderSerialNumber>;
            query: AggregateQueryOperation<WorkOrderSerialNumber>;
        };
        asyncOperations: WorkOrderSerialNumber$AsyncOperations;
        lookups(dataOrId: string | { data: WorkOrderSerialNumberInput }): WorkOrderSerialNumber$Lookups;
        getDefaults: GetDefaultsOperation<WorkOrderSerialNumber>;
    }
    export interface WorkOrderView extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        workOrders: ClientCollection<WorkOrder>;
    }
    export interface WorkOrderViewInput extends ClientNodeInput {}
    export interface WorkOrderViewBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        workOrders: ClientCollection<WorkOrder>;
    }
    export interface WorkOrderView$Mutations {
        defaultRecord: Node$Operation<{}, string>;
    }
    export interface WorkOrderView$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkOrderView$Operations {
        query: QueryOperation<WorkOrderView>;
        read: ReadOperation<WorkOrderView>;
        aggregate: {
            read: AggregateReadOperation<WorkOrderView>;
            query: AggregateQueryOperation<WorkOrderView>;
        };
        mutations: WorkOrderView$Mutations;
        asyncOperations: WorkOrderView$AsyncOperations;
        getDefaults: GetDefaultsOperation<WorkOrderView>;
    }
    export interface MaterialTrackingLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: MaterialTracking;
        stockTransactionStatus: StockDocumentTransactionStatus;
        workOrderLine: WorkOrderComponent;
        materialType: string;
        quantityInStockUnit: string;
        isActiveQuantity: boolean;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransaction>;
        completed: boolean;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        workInProgressCosts: ClientCollection<WorkInProgressCost>;
        workInProgressCost: WorkInProgressCost;
        item: Item;
        workInProgressWorkOrderNumber: string;
        workInProgressWorkOrderSysId: integer;
        itemSite: ItemSite;
        line: integer;
        stockDetails: ClientCollection<StockIssueDetail>;
        stockAllocations: ClientCollection<StockAllocation>;
        quantityAllocated: string;
        remainingQuantityToAllocate: string;
        jsonStockDetails: string;
        analyticalData: AnalyticalData;
        allocationStatus: StockAllocationStatus;
    }
    export interface MaterialTrackingLineInput extends VitalClientNodeInput {
        stockTransactionStatus?: StockDocumentTransactionStatus;
        workOrderLine?: integer | string;
        materialType?: string;
        quantityInStockUnit?: decimal | string;
        isActiveQuantity?: boolean | string;
        stockTransactions?: Partial<StockTransactionInput>[];
        completed?: boolean | string;
        storedDimensions?: string;
        storedAttributes?: string;
        line?: integer | string;
        stockDetails?: Partial<StockIssueDetailInput>[];
        jsonStockDetails?: string;
        analyticalData?: integer | string;
    }
    export interface MaterialTrackingLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: MaterialTracking;
        stockTransactionStatus: StockDocumentTransactionStatus;
        workOrderLine: WorkOrderComponent;
        materialType: string;
        quantityInStockUnit: string;
        isActiveQuantity: boolean;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        completed: boolean;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        workInProgressCosts: ClientCollection<WorkInProgressCost>;
        workInProgressCost: WorkInProgressCost;
        item: Item;
        workInProgressWorkOrderNumber: string;
        workInProgressWorkOrderSysId: integer;
        itemSite: ItemSite;
        line: integer;
        stockDetails: ClientCollection<StockIssueDetailBinding>;
        stockAllocations: ClientCollection<StockAllocation>;
        quantityAllocated: string;
        remainingQuantityToAllocate: string;
        jsonStockDetails: any;
        analyticalData: AnalyticalData;
        allocationStatus: StockAllocationStatus;
    }
    export interface MaterialTrackingLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MaterialTrackingLine$Lookups {
        workOrderLine: QueryOperation<WorkOrderComponent>;
        workInProgressCost: QueryOperation<WorkInProgressCost>;
        item: QueryOperation<Item>;
        itemSite: QueryOperation<ItemSite>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface MaterialTrackingLine$Operations {
        query: QueryOperation<MaterialTrackingLine>;
        read: ReadOperation<MaterialTrackingLine>;
        aggregate: {
            read: AggregateReadOperation<MaterialTrackingLine>;
            query: AggregateQueryOperation<MaterialTrackingLine>;
        };
        asyncOperations: MaterialTrackingLine$AsyncOperations;
        lookups(dataOrId: string | { data: MaterialTrackingLineInput }): MaterialTrackingLine$Lookups;
        getDefaults: GetDefaultsOperation<MaterialTrackingLine>;
    }
    export interface OperationTrackingLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: OperationTracking;
        line: integer;
        workOrderOperation: WorkOrderOperation;
        trackingType: string;
        completedQuantity: string;
        actualResource: DetailedResource;
        setupTimeUnit: UnitOfMeasure;
        runTimeUnit: UnitOfMeasure;
        actualSetupTime: string;
        actualRunTime: string;
        completed: boolean;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        item: Item;
        workInProgressWorkOrderNumber: string;
        workInProgressWorkOrderSysId: integer;
        workInProgressCosts: ClientCollection<WorkInProgressCost>;
        operationResource: WorkOrderOperationResource;
        analyticalData: AnalyticalData;
    }
    export interface OperationTrackingLineInput extends VitalClientNodeInput {
        line?: integer | string;
        workOrderOperation?: integer | string;
        trackingType?: string;
        completedQuantity?: decimal | string;
        actualResource?: integer | string;
        setupTimeUnit?: integer | string;
        runTimeUnit?: integer | string;
        actualSetupTime?: decimal | string;
        actualRunTime?: decimal | string;
        completed?: boolean | string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
    }
    export interface OperationTrackingLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: OperationTracking;
        line: integer;
        workOrderOperation: WorkOrderOperation;
        trackingType: string;
        completedQuantity: string;
        actualResource: DetailedResource;
        setupTimeUnit: UnitOfMeasure;
        runTimeUnit: UnitOfMeasure;
        actualSetupTime: string;
        actualRunTime: string;
        completed: boolean;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        item: Item;
        workInProgressWorkOrderNumber: string;
        workInProgressWorkOrderSysId: integer;
        workInProgressCosts: ClientCollection<WorkInProgressCost>;
        operationResource: WorkOrderOperationResource;
        analyticalData: AnalyticalData;
    }
    export interface OperationTrackingLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface OperationTrackingLine$Lookups {
        workOrderOperation: QueryOperation<WorkOrderOperation>;
        actualResource: QueryOperation<DetailedResource>;
        setupTimeUnit: QueryOperation<UnitOfMeasure>;
        runTimeUnit: QueryOperation<UnitOfMeasure>;
        item: QueryOperation<Item>;
        operationResource: QueryOperation<WorkOrderOperationResource>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface OperationTrackingLine$Operations {
        query: QueryOperation<OperationTrackingLine>;
        read: ReadOperation<OperationTrackingLine>;
        aggregate: {
            read: AggregateReadOperation<OperationTrackingLine>;
            query: AggregateQueryOperation<OperationTrackingLine>;
        };
        asyncOperations: OperationTrackingLine$AsyncOperations;
        lookups(dataOrId: string | { data: OperationTrackingLineInput }): OperationTrackingLine$Lookups;
        getDefaults: GetDefaultsOperation<OperationTrackingLine>;
    }
    export interface ProductionTrackingLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: ProductionTracking;
        stockTransactionStatus: StockDocumentTransactionStatus;
        line: integer;
        type: string;
        workOrderLine: WorkOrderReleasedItem;
        releasedQuantity: string;
        completed: boolean;
        orderCost: string;
        valuedCost: string;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        workInProgressCosts: ClientCollection<WorkInProgressCost>;
        workInProgressCost: WorkInProgressCost;
        item: Item;
        workInProgressWorkOrderNumber: string;
        workInProgressWorkOrderSysId: integer;
        stockDetails: ClientCollection<StockReceiptDetail>;
        jsonStockDetails: string;
        stockTransactions: ClientCollection<StockTransaction>;
        analyticalData: AnalyticalData;
    }
    export interface ProductionTrackingLineInput extends VitalClientNodeInput {
        stockTransactionStatus?: StockDocumentTransactionStatus;
        line?: integer | string;
        type?: string;
        workOrderLine?: integer | string;
        releasedQuantity?: decimal | string;
        completed?: boolean | string;
        orderCost?: decimal | string;
        valuedCost?: decimal | string;
        storedDimensions?: string;
        storedAttributes?: string;
        stockDetails?: Partial<StockReceiptDetailInput>[];
        jsonStockDetails?: string;
        stockTransactions?: Partial<StockTransactionInput>[];
        analyticalData?: integer | string;
    }
    export interface ProductionTrackingLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: ProductionTracking;
        stockTransactionStatus: StockDocumentTransactionStatus;
        line: integer;
        type: string;
        workOrderLine: WorkOrderReleasedItem;
        releasedQuantity: string;
        completed: boolean;
        orderCost: string;
        valuedCost: string;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        workInProgressCosts: ClientCollection<WorkInProgressCost>;
        workInProgressCost: WorkInProgressCost;
        item: Item;
        workInProgressWorkOrderNumber: string;
        workInProgressWorkOrderSysId: integer;
        stockDetails: ClientCollection<StockReceiptDetailBinding>;
        jsonStockDetails: any;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        analyticalData: AnalyticalData;
    }
    export interface ProductionTrackingLine$Queries {
        getQuantityProduced: Node$Operation<
            {},
            {
                currentMonth: string;
                previousMonth: string;
            }
        >;
    }
    export interface ProductionTrackingLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ProductionTrackingLine$Lookups {
        workOrderLine: QueryOperation<WorkOrderReleasedItem>;
        workInProgressCost: QueryOperation<WorkInProgressCost>;
        item: QueryOperation<Item>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface ProductionTrackingLine$Operations {
        query: QueryOperation<ProductionTrackingLine>;
        read: ReadOperation<ProductionTrackingLine>;
        aggregate: {
            read: AggregateReadOperation<ProductionTrackingLine>;
            query: AggregateQueryOperation<ProductionTrackingLine>;
        };
        queries: ProductionTrackingLine$Queries;
        asyncOperations: ProductionTrackingLine$AsyncOperations;
        lookups(dataOrId: string | { data: ProductionTrackingLineInput }): ProductionTrackingLine$Lookups;
        getDefaults: GetDefaultsOperation<ProductionTrackingLine>;
    }
    export interface WorkInProgressWorkOrderComponent extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentType: WorkInProgressDocumentType;
        workOrderComponent: WorkOrderComponent;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
    }
    export interface WorkInProgressWorkOrderComponentInput extends VitalClientNodeInput {
        remainingQuantityToAllocate?: decimal | string;
        documentType?: WorkInProgressDocumentType;
        item?: integer | string;
        site?: integer | string;
        status?: OrderType;
        startDate?: string;
        endDate?: string;
        expectedQuantity?: decimal | string;
        actualQuantity?: decimal | string;
        outstandingQuantity?: decimal | string;
    }
    export interface WorkInProgressWorkOrderComponentBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        remainingQuantityToAllocate: string;
        documentType: WorkInProgressDocumentType;
        documentId: integer;
        originDocumentLine: integer;
        workOrderComponent: WorkOrderComponent;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
        documentNumber: string;
        documentLine: integer;
        originDocumentType: WorkInProgressDocumentType;
    }
    export interface WorkInProgressWorkOrderComponent$Queries {
        getWorkInProgressQuantityPerItemSite: Node$Operation<
            {
                currentItem: integer | string;
                currentSite: integer | string;
                currentDate?: string;
                currentStatus?: OrderType;
                originDocumentType?: WorkInProgressDocumentType;
            },
            {
                supply: string;
                demand: string;
            }
        >;
    }
    export interface WorkInProgressWorkOrderComponent$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkInProgressWorkOrderComponent$Lookups {
        item: QueryOperation<Item>;
        site: QueryOperation<Site>;
    }
    export interface WorkInProgressWorkOrderComponent$Operations {
        query: QueryOperation<WorkInProgressWorkOrderComponent>;
        read: ReadOperation<WorkInProgressWorkOrderComponent>;
        aggregate: {
            read: AggregateReadOperation<WorkInProgressWorkOrderComponent>;
            query: AggregateQueryOperation<WorkInProgressWorkOrderComponent>;
        };
        queries: WorkInProgressWorkOrderComponent$Queries;
        create: CreateOperation<WorkInProgressWorkOrderComponentInput, WorkInProgressWorkOrderComponent>;
        getDuplicate: GetDuplicateOperation<WorkInProgressWorkOrderComponent>;
        duplicate: DuplicateOperation<string, WorkInProgressWorkOrderComponentInput, WorkInProgressWorkOrderComponent>;
        update: UpdateOperation<WorkInProgressWorkOrderComponentInput, WorkInProgressWorkOrderComponent>;
        updateById: UpdateByIdOperation<WorkInProgressWorkOrderComponentInput, WorkInProgressWorkOrderComponent>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: WorkInProgressWorkOrderComponent$AsyncOperations;
        lookups(
            dataOrId: string | { data: WorkInProgressWorkOrderComponentInput },
        ): WorkInProgressWorkOrderComponent$Lookups;
        getDefaults: GetDefaultsOperation<WorkInProgressWorkOrderComponent>;
    }
    export interface WorkInProgressWorkOrderReleasedItem extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentType: WorkInProgressDocumentType;
        workOrderReleasedItem: WorkOrderReleasedItem;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
    }
    export interface WorkInProgressWorkOrderReleasedItemInput extends VitalClientNodeInput {
        remainingQuantityToAllocate?: decimal | string;
        documentType?: WorkInProgressDocumentType;
        item?: integer | string;
        site?: integer | string;
        status?: OrderType;
        startDate?: string;
        endDate?: string;
        expectedQuantity?: decimal | string;
        actualQuantity?: decimal | string;
        outstandingQuantity?: decimal | string;
    }
    export interface WorkInProgressWorkOrderReleasedItemBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        remainingQuantityToAllocate: string;
        documentType: WorkInProgressDocumentType;
        documentLine: integer;
        documentId: integer;
        originDocumentType: WorkInProgressDocumentType;
        originDocumentLine: integer;
        workOrderReleasedItem: WorkOrderReleasedItem;
        item: Item;
        site: Site;
        status: OrderType;
        startDate: string;
        endDate: string;
        expectedQuantity: string;
        actualQuantity: string;
        outstandingQuantity: string;
        documentNumber: string;
    }
    export interface WorkInProgressWorkOrderReleasedItem$Queries {
        getWorkInProgressQuantityPerItemSite: Node$Operation<
            {
                currentItem: integer | string;
                currentSite: integer | string;
                currentDate?: string;
                currentStatus?: OrderType;
                originDocumentType?: WorkInProgressDocumentType;
            },
            {
                supply: string;
                demand: string;
            }
        >;
    }
    export interface WorkInProgressWorkOrderReleasedItem$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkInProgressWorkOrderReleasedItem$Lookups {
        item: QueryOperation<Item>;
        site: QueryOperation<Site>;
    }
    export interface WorkInProgressWorkOrderReleasedItem$Operations {
        query: QueryOperation<WorkInProgressWorkOrderReleasedItem>;
        read: ReadOperation<WorkInProgressWorkOrderReleasedItem>;
        aggregate: {
            read: AggregateReadOperation<WorkInProgressWorkOrderReleasedItem>;
            query: AggregateQueryOperation<WorkInProgressWorkOrderReleasedItem>;
        };
        queries: WorkInProgressWorkOrderReleasedItem$Queries;
        create: CreateOperation<WorkInProgressWorkOrderReleasedItemInput, WorkInProgressWorkOrderReleasedItem>;
        getDuplicate: GetDuplicateOperation<WorkInProgressWorkOrderReleasedItem>;
        duplicate: DuplicateOperation<
            string,
            WorkInProgressWorkOrderReleasedItemInput,
            WorkInProgressWorkOrderReleasedItem
        >;
        update: UpdateOperation<WorkInProgressWorkOrderReleasedItemInput, WorkInProgressWorkOrderReleasedItem>;
        updateById: UpdateByIdOperation<WorkInProgressWorkOrderReleasedItemInput, WorkInProgressWorkOrderReleasedItem>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: WorkInProgressWorkOrderReleasedItem$AsyncOperations;
        lookups(
            dataOrId: string | { data: WorkInProgressWorkOrderReleasedItemInput },
        ): WorkInProgressWorkOrderReleasedItem$Lookups;
        getDefaults: GetDefaultsOperation<WorkInProgressWorkOrderReleasedItem>;
    }
    export interface WorkOrderComponent extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        workOrder: WorkOrder;
        componentNumber: integer;
        lineType: BomLineType;
        item: Item;
        name: string;
        unit: UnitOfMeasure;
        itemSite: ItemSite;
        isFixedLinkQuantity: boolean;
        linkQuantity: string;
        scrapFactor: string;
        operation: WorkOrderOperation;
        requiredQuantity: string;
        consumedQuantity: string;
        requiredDate: string;
        lineStatus: ComponentStatus;
        uLineStatus: WorkOrderLineStatusFiltered;
        isAdded: boolean;
        plannedCost: string;
        actualCost: string;
        completedQuantityPercentage: string;
        remainingQuantityToReport: string;
        workInProgress: WorkInProgressWorkOrderComponent;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        stockAllocations: ClientCollection<StockAllocation>;
        quantityAllocated: string;
        remainingQuantityToAllocate: string;
        allocationStatus: StockAllocationStatus;
        allocationRequestStatus: AllocationRequestStatus;
        component: Component;
        instruction: TextStream;
        stockOnHand: string;
        availableQuantityInStockUnit: string;
        stockShortageInStockUnit: string;
        stockShortageStatus: boolean;
        analyticalData: AnalyticalData;
    }
    export interface WorkOrderComponentInput extends VitalClientNodeInput {
        componentNumber?: integer | string;
        lineType?: BomLineType;
        item?: integer | string;
        name?: string;
        unit?: integer | string;
        isFixedLinkQuantity?: boolean | string;
        linkQuantity?: decimal | string;
        scrapFactor?: decimal | string;
        operation?: integer | string;
        requiredQuantity?: decimal | string;
        consumedQuantity?: decimal | string;
        requiredDate?: string;
        lineStatus?: ComponentStatus;
        isAdded?: boolean | string;
        plannedCost?: decimal | string;
        actualCost?: decimal | string;
        workInProgress?: WorkInProgressWorkOrderComponentInput;
        storedDimensions?: string;
        storedAttributes?: string;
        allocationRequestStatus?: AllocationRequestStatus;
        instruction?: TextStream;
        analyticalData?: integer | string;
    }
    export interface WorkOrderComponentBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        workOrder: WorkOrder;
        componentNumber: integer;
        lineType: BomLineType;
        item: Item;
        name: string;
        unit: UnitOfMeasure;
        itemSite: ItemSite;
        isFixedLinkQuantity: boolean;
        linkQuantity: string;
        scrapFactor: string;
        operation: WorkOrderOperation;
        requiredQuantity: string;
        consumedQuantity: string;
        requiredDate: string;
        lineStatus: ComponentStatus;
        uLineStatus: WorkOrderLineStatusFiltered;
        isAdded: boolean;
        plannedCost: string;
        actualCost: string;
        completedQuantityPercentage: string;
        remainingQuantityToReport: string;
        workInProgress: WorkInProgressWorkOrderComponentBinding;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        stockAllocations: ClientCollection<StockAllocation>;
        quantityAllocated: string;
        remainingQuantityToAllocate: string;
        allocationStatus: StockAllocationStatus;
        allocationRequestStatus: AllocationRequestStatus;
        component: Component;
        instruction: TextStream;
        stockOnHand: string;
        availableQuantityInStockUnit: string;
        stockShortageInStockUnit: string;
        stockShortageStatus: boolean;
        analyticalData: AnalyticalData;
    }
    export interface WorkOrderComponent$Queries {
        setRequiredQuantity: Node$Operation<
            {
                releasedQuantity?: decimal | string;
                baseQuantity?: decimal | string;
                linkQuantity?: decimal | string;
                isFixed?: boolean | string;
                scrapFactor?: decimal | string;
                decimalPlaces?: decimal | string;
            },
            decimal
        >;
        checkComponentStock: Node$Operation<
            {
                orderQuantity: decimal | string;
                bomId: string;
            },
            {
                _id: string;
                itemId: string;
                itemDescription: string;
                itemName: string;
                componentName: string;
                available: string;
                required: string;
                shortage: string;
                unit: string;
                componentNumber: integer;
            }[]
        >;
        getMaterialPlannedCost: Node$Operation<
            {
                searchCriteria: {
                    requiredQuantity: decimal | string;
                    requiredDate: string;
                    item: integer | string;
                    site: integer | string;
                    lineStatus: ComponentStatus;
                };
            },
            decimal
        >;
    }
    export interface WorkOrderComponent$Mutations {
        updateComponent: Node$Operation<
            {
                workOrderNumber?: string;
                componentNumber?: integer | string;
                quantity?: decimal | string;
                cost?: decimal | string;
                completed?: boolean | string;
            },
            WorkOrderComponent
        >;
    }
    export interface WorkOrderComponent$AsyncOperations {
        massAutoAllocation: AsyncOperation<
            {
                filter?: string;
                data?: {
                    requestType: AllocationRequestType;
                    requestDescription?: string;
                    userEntries?: {
                        lineId: integer | string;
                        quantityToProcess: decimal | string;
                    }[];
                };
                stockAllocationParameters?: {
                    cannotOverAllocate?: boolean | string;
                    shouldControlAllocationInProgress?: boolean | string;
                };
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkOrderComponent$Lookups {
        item: QueryOperation<Item>;
        unit: QueryOperation<UnitOfMeasure>;
        itemSite: QueryOperation<ItemSite>;
        operation: QueryOperation<WorkOrderOperation>;
        component: QueryOperation<Component>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface WorkOrderComponent$Operations {
        query: QueryOperation<WorkOrderComponent>;
        read: ReadOperation<WorkOrderComponent>;
        aggregate: {
            read: AggregateReadOperation<WorkOrderComponent>;
            query: AggregateQueryOperation<WorkOrderComponent>;
        };
        queries: WorkOrderComponent$Queries;
        create: CreateOperation<WorkOrderComponentInput, WorkOrderComponent>;
        getDuplicate: GetDuplicateOperation<WorkOrderComponent>;
        duplicate: DuplicateOperation<string, WorkOrderComponentInput, WorkOrderComponent>;
        update: UpdateOperation<WorkOrderComponentInput, WorkOrderComponent>;
        updateById: UpdateByIdOperation<WorkOrderComponentInput, WorkOrderComponent>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: WorkOrderComponent$Mutations;
        asyncOperations: WorkOrderComponent$AsyncOperations;
        lookups(dataOrId: string | { data: WorkOrderComponentInput }): WorkOrderComponent$Lookups;
        getDefaults: GetDefaultsOperation<WorkOrderComponent>;
    }
    export interface WorkOrderReleasedItem extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: WorkOrder;
        releasedItem: Item;
        item: Item;
        releasedItemName: string;
        releasedItemId: string;
        stockUnit: UnitOfMeasure;
        totalActualQuantity: string;
        remainingQuantity: string;
        workInProgress: WorkInProgressWorkOrderReleasedItem;
        lineStatus: ReleasedItemStatus;
        uLineStatus: WorkOrderLineStatusFiltered;
        uLineStatusForTracking: WorkOrderLineStatusFilteredForTracking;
        stockTransactionStatus: StockDocumentTransactionStatus;
        stockTransactions: ClientCollection<StockTransaction>;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        itemSite: ItemSite;
        plannedMaterialCost: string;
        plannedMachineCost: string;
        plannedLaborCost: string;
        plannedToolCost: string;
        plannedProcessCost: string;
        serialNumbers: SerialNumber[];
        releasedQuantity: string;
        quantityInStockUnit: string;
        analyticalData: AnalyticalData;
    }
    export interface WorkOrderReleasedItemInput extends VitalClientNodeInput {
        releasedItem?: integer | string;
        totalActualQuantity?: decimal | string;
        workInProgress?: WorkInProgressWorkOrderReleasedItemInput;
        lineStatus?: ReleasedItemStatus;
        stockTransactionStatus?: StockDocumentTransactionStatus;
        stockTransactions?: Partial<StockTransactionInput>[];
        storedDimensions?: string;
        storedAttributes?: string;
        plannedMaterialCost?: decimal | string;
        plannedMachineCost?: decimal | string;
        plannedLaborCost?: decimal | string;
        plannedToolCost?: decimal | string;
        serialNumbers?: (integer | string)[];
        releasedQuantity?: decimal | string;
        analyticalData?: integer | string;
    }
    export interface WorkOrderReleasedItemBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: WorkOrder;
        releasedItem: Item;
        item: Item;
        releasedItemName: string;
        releasedItemId: string;
        stockUnit: UnitOfMeasure;
        totalActualQuantity: string;
        remainingQuantity: string;
        workInProgress: WorkInProgressWorkOrderReleasedItemBinding;
        lineStatus: ReleasedItemStatus;
        uLineStatus: WorkOrderLineStatusFiltered;
        uLineStatusForTracking: WorkOrderLineStatusFilteredForTracking;
        stockTransactionStatus: StockDocumentTransactionStatus;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        itemSite: ItemSite;
        plannedMaterialCost: string;
        plannedMachineCost: string;
        plannedLaborCost: string;
        plannedToolCost: string;
        plannedProcessCost: string;
        serialNumbers: SerialNumber[];
        releasedQuantity: string;
        quantityInStockUnit: string;
        analyticalData: AnalyticalData;
    }
    export interface WorkOrderReleasedItem$Mutations {
        updateReleasedItem: Node$Operation<
            {
                workOrderNumber?: string;
                releasedItem?: string;
                quantity?: decimal | string;
                completed?: boolean | string;
            },
            WorkOrderReleasedItem
        >;
    }
    export interface WorkOrderReleasedItem$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkOrderReleasedItem$Lookups {
        releasedItem: QueryOperation<Item>;
        item: QueryOperation<Item>;
        stockUnit: QueryOperation<UnitOfMeasure>;
        itemSite: QueryOperation<ItemSite>;
        analyticalData: QueryOperation<AnalyticalData>;
    }
    export interface WorkOrderReleasedItem$Operations {
        query: QueryOperation<WorkOrderReleasedItem>;
        read: ReadOperation<WorkOrderReleasedItem>;
        aggregate: {
            read: AggregateReadOperation<WorkOrderReleasedItem>;
            query: AggregateQueryOperation<WorkOrderReleasedItem>;
        };
        create: CreateOperation<WorkOrderReleasedItemInput, WorkOrderReleasedItem>;
        getDuplicate: GetDuplicateOperation<WorkOrderReleasedItem>;
        duplicate: DuplicateOperation<string, WorkOrderReleasedItemInput, WorkOrderReleasedItem>;
        update: UpdateOperation<WorkOrderReleasedItemInput, WorkOrderReleasedItem>;
        updateById: UpdateByIdOperation<WorkOrderReleasedItemInput, WorkOrderReleasedItem>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: WorkOrderReleasedItem$Mutations;
        asyncOperations: WorkOrderReleasedItem$AsyncOperations;
        lookups(dataOrId: string | { data: WorkOrderReleasedItemInput }): WorkOrderReleasedItem$Lookups;
        getDefaults: GetDefaultsOperation<WorkOrderReleasedItem>;
    }
    export interface BaseResourceExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategory>;
        operationResources: ClientCollection<WorkOrderOperationResource>;
        _syncTick: string;
    }
    export interface BaseResourceInputExtension {
        _constructor?: string;
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        activeRange?: string;
        site?: integer | string;
        resourceImage?: BinaryStream;
        weeklyShift?: integer | string;
        efficiency?: decimal | string;
        location?: integer | string;
        resourceCostCategories?: Partial<ResourceCostCategoryInput>[];
        _syncTick?: decimal | string;
    }
    export interface BaseResourceBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        activeRange: string;
        activeFrom: string;
        activeTo: string;
        site: Site;
        resourceImage: BinaryStream;
        weeklyShift: WeeklyShift;
        efficiency: string;
        location: Location;
        resourceCostCategories: ClientCollection<ResourceCostCategoryBinding>;
        operationResources: ClientCollection<WorkOrderOperationResource>;
        _syncTick: string;
    }
    export interface StockJournalExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sequence: integer;
        site: Site;
        currency: Currency;
        item: Item;
        stockUnit: UnitOfMeasure;
        quantityInStockUnit: string;
        effectiveDate: string;
        owner: string;
        documentType: string;
        sourceDocumentLine: BaseDocumentLine;
        lot: Lot;
        status: StockStatus;
        location: Location;
        serialNumbers: ClientCollection<StockJournalSerialNumber>;
        orderCost: string;
        valuedCost: string;
        isAdjusted: boolean;
        reasonCode: ReasonCode;
        stockDetail: BaseStockDetail;
        nonAbsorbedAmount: string;
        movementType: StockMovementType;
        activeQuantityInStockUnit: string;
        documentLine: BaseDocumentLine;
        orderAmount: string;
        movementAmount: string;
        costVariance: string;
        amountVariance: string;
        materialTrackingLine: MaterialTrackingLine;
        productionTrackingLine: ProductionTrackingLine;
        workOrder: WorkOrder;
    }
    export interface StockJournalInputExtension {
        sequence?: integer | string;
        site?: integer | string;
        item?: integer | string;
        stockUnit?: integer | string;
        quantityInStockUnit?: decimal | string;
        effectiveDate?: string;
        owner?: string;
        sourceDocumentLine?: integer | string;
        lot?: integer | string;
        status?: integer | string;
        location?: integer | string;
        serialNumbers?: Partial<StockJournalSerialNumberInput>[];
        orderCost?: decimal | string;
        valuedCost?: decimal | string;
        isAdjusted?: boolean | string;
        reasonCode?: integer | string;
        stockDetail?: integer | string;
        nonAbsorbedAmount?: decimal | string;
        movementType?: StockMovementType;
        activeQuantityInStockUnit?: decimal | string;
        documentLine?: integer | string;
        orderAmount?: decimal | string;
        movementAmount?: decimal | string;
    }
    export interface StockJournalBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sequence: integer;
        site: Site;
        currency: Currency;
        item: Item;
        stockUnit: UnitOfMeasure;
        quantityInStockUnit: string;
        effectiveDate: string;
        owner: string;
        documentType: string;
        sourceDocumentLine: BaseDocumentLine;
        lot: Lot;
        status: StockStatus;
        location: Location;
        serialNumbers: ClientCollection<StockJournalSerialNumberBinding>;
        orderCost: string;
        valuedCost: string;
        isAdjusted: boolean;
        reasonCode: ReasonCode;
        stockDetail: BaseStockDetail;
        nonAbsorbedAmount: string;
        movementType: StockMovementType;
        activeQuantityInStockUnit: string;
        documentLine: BaseDocumentLine;
        orderAmount: string;
        movementAmount: string;
        costVariance: string;
        amountVariance: string;
        materialTrackingLine: MaterialTrackingLine;
        productionTrackingLine: ProductionTrackingLine;
        workOrder: WorkOrder;
    }
    export interface StockJournalExtension$Lookups {
        materialTrackingLine: QueryOperation<MaterialTrackingLine>;
        productionTrackingLine: QueryOperation<ProductionTrackingLine>;
        workOrder: QueryOperation<WorkOrder>;
    }
    export interface StockJournalExtension$Operations {
        lookups(dataOrId: string | { data: StockJournalInput }): StockJournalExtension$Lookups;
    }
    export interface Package {
        '@sage/xtrem-manufacturing/MaterialTracking': MaterialTracking$Operations;
        '@sage/xtrem-manufacturing/OperationTracking': OperationTracking$Operations;
        '@sage/xtrem-manufacturing/ProductionTracking': ProductionTracking$Operations;
        '@sage/xtrem-manufacturing/WorkInProgressCost': WorkInProgressCost$Operations;
        '@sage/xtrem-manufacturing/WorkInProgressInputSet': WorkInProgressInputSet$Operations;
        '@sage/xtrem-manufacturing/WorkInProgressResultLine': WorkInProgressResultLine$Operations;
        '@sage/xtrem-manufacturing/WorkOrder': WorkOrder$Operations;
        '@sage/xtrem-manufacturing/WorkOrderCategory': WorkOrderCategory$Operations;
        '@sage/xtrem-manufacturing/WorkOrderOperation': WorkOrderOperation$Operations;
        '@sage/xtrem-manufacturing/WorkOrderOperationResource': WorkOrderOperationResource$Operations;
        '@sage/xtrem-manufacturing/WorkOrderOperationResourceDetail': WorkOrderOperationResourceDetail$Operations;
        '@sage/xtrem-manufacturing/WorkOrderSerialNumber': WorkOrderSerialNumber$Operations;
        '@sage/xtrem-manufacturing/WorkOrderView': WorkOrderView$Operations;
        '@sage/xtrem-manufacturing/MaterialTrackingLine': MaterialTrackingLine$Operations;
        '@sage/xtrem-manufacturing/OperationTrackingLine': OperationTrackingLine$Operations;
        '@sage/xtrem-manufacturing/ProductionTrackingLine': ProductionTrackingLine$Operations;
        '@sage/xtrem-manufacturing/WorkInProgressWorkOrderComponent': WorkInProgressWorkOrderComponent$Operations;
        '@sage/xtrem-manufacturing/WorkInProgressWorkOrderReleasedItem': WorkInProgressWorkOrderReleasedItem$Operations;
        '@sage/xtrem-manufacturing/WorkOrderComponent': WorkOrderComponent$Operations;
        '@sage/xtrem-manufacturing/WorkOrderReleasedItem': WorkOrderReleasedItem$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremDistribution$Package,
            SageXtremFinanceData$Package,
            SageXtremImportExport$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremPurchasing$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremTechnicalData$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-manufacturing-api' {
    export type * from '@sage/xtrem-manufacturing-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-distribution-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-purchasing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-technical-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-manufacturing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type {
        BaseResourceBindingExtension,
        BaseResourceExtension,
        BaseResourceInputExtension,
    } from '@sage/xtrem-manufacturing-api';
    export interface BaseResource extends BaseResourceExtension {}
    export interface BaseResourceBinding extends BaseResourceBindingExtension {}
    export interface BaseResourceInput extends BaseResourceInputExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type {
        StockJournalBindingExtension,
        StockJournalExtension,
        StockJournalExtension$Lookups,
        StockJournalExtension$Operations,
        StockJournalInputExtension,
    } from '@sage/xtrem-manufacturing-api';
    export interface StockJournal extends StockJournalExtension {}
    export interface StockJournalBinding extends StockJournalBindingExtension {}
    export interface StockJournalInput extends StockJournalInputExtension {}
    export interface StockJournal$Lookups extends StockJournalExtension$Lookups {}
    export interface StockJournal$Operations extends StockJournalExtension$Operations {}
}
