query {
    rlPALjZt: xtremManufacturing {
        workOrder {
            query (filter: "{\"_id\":{\"_and\":[{\"_eq\":\"{{order}}\"}]}}", orderBy: "{}", first: 1) {
                edges {
                    node {
                        number
                        productionItem {
                            releasedItemName
                            remainingQuantity
                            releasedQuantity
                            stockUnit {
                                name
                            }
                        }
                        startDate
                        endDate
                        seOOSTde: productionComponents {
                            query (filter: "{}", orderBy: "{\"name\":1,\"item\":{\"id\":1},\"remainingQuantityToReport\":1,\"requiredDate\":1,\"operation\":{\"name\":1}}") {
                                edges {
                                    node {
                                        _id
                                        name
                                        item {
                                            id
                                        }
                                        remainingQuantityToReport
                                        unit {
                                            name
                                        }
                                        sjBoUnKz: stockAllocations {
                                            query (filter: "{}", orderBy: "{}", first: 1) {
                                                edges {
                                                    node {
                                                        quantityInStockUnit
                                                        stockRecord {
                                                            stockUnit {
                                                                name
                                                            }
                                                        }
                                                        _id
                                                    }
                                                }
                                            }
                                        }
                                        operation {
                                            name
                                        }
                                        oMecatnh: stockAllocations {
                                            query (filter: "{}", orderBy: "{\"stockRecord\":{\"location\":{\"name\":1},\"lot\":{\"id\":1},\"quantityInStockUnit\":1}}") {
                                                edges {
                                                    node {
                                                        _id
                                                        stockRecord {
                                                            location {
                                                                name
                                                            }
                                                            lot {
                                                                id
                                                            }
                                                            quantityInStockUnit
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        _id
                    }
                }
            }
        }
    }
}