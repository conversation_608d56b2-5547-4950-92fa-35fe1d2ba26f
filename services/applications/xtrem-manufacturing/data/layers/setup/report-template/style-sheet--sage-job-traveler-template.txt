@page {
    size: auto;
    margin: 15mm 15mm 15mm 15mm;
}

body {
    font-size: 12px;
    margin: 0px;
    color: var(--textAndLabels);
    -webkit-print-color-adjust: exact;
}

.main {
    font-family: Helvetica;
}

h1,h2,h3,h4,th{
    color: var(--themePrimary);
}

table {
  font-size: 12px;
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 16px;
}

table tbody tr {
  border-bottom: 1px solid var(--tableSeparator);
}

table tbody tr:nth-child(even) {
  background: #F2F5F6;
}

table th {
  border-bottom: 2px solid var(--themePrimary);
  border-right: 1px solid var(--tableSeparator);
  padding: 4px;
  font-size: 12px;
}

table th:last-child {
    border-right: none;
}

table th:first-child {
    padding-left: 0
}

table td {
  font-size: 12px;
  vertical-align: top;
  border-bottom: 1px solid var(--tableSeparator);
  border-right: 1px solid var(--tableSeparator);
  padding: 4px;
}

table td:last-child{
    border-right: none;
    padding-right: 0;
}
table td:first-child{
    padding-left: 0;
}

table .column-left {
  text-align: left;
}

table .column-right{
  text-align: right;
}

.header-table{
  width: 100%;
}

.header-table td{
  vertical-align: top;
}

@media print {
  .page-break {
    page-break-after: always;
  }
}

table.left {
   float:left;
   width:50%;
   border: none;
}

table.right {
   float:right;
   width:30%;
}

table.left tbody tr {
   border-right: none;
   border-bottom: none;
}

table.right tbody tr {
   border-right: none;
   border-bottom: none;
}

table.right td {
   border-right: none;
   border-bottom: none;
}

table.left td {
   border-right: none;
   border-bottom: none;
}

table.left tbody tr:nth-child(even) {
  background: none;
}

table.right tbody tr:nth-child(even) {
  background: none;
}
