<div class="main">
  <div>
    <div align="center">
      <div style="float: center; width: 22%">
        <h2>{{xtremManufacturing.workOrder.query.edges.0.node.site.legalCompany.name}}</h2>
        <h1>{{ translatedContent "d9706f49e7915eca39ca603684b4bced" }}</h1>
        <h2>{{ translatedContent "f074487b5882b4a50d6faf836db627c2" }} {{xtremManufacturing.workOrder.query.edges.0.node.number}}</h2>
      </div>
    </div>
    <div>
      <table class="column-left">
        <thead>
          <tr>
            <th class="column-left">{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</th>
             <th class="column-left">{{ translatedContent "a7d6475ec8993b7224d6facc8cb0ead6" }}</th>
             <th class="column-left">{{ translatedContent "4d34f1097f6c8b9cee28bca8b78bbee9" }}</th>
             <th class="column-left">{{ translatedContent "b7de7e4247d4ab279ef031b7a44c201d" }}</th>
          </tr>
        </thead>
        <tbody>
          <tr>
                <td class="column-left">{{xtremManufacturing.workOrder.query.edges.0.node.name}}</td>
                <td class="column-left">{{xtremManufacturing.workOrder.query.edges.0.node.site.name}}</td>
                <td class="column-left">{{xtremManufacturing.workOrder.query.edges.0.node.startDate}}</td>
                <td class="column-left">{{xtremManufacturing.workOrder.query.edges.0.node.endDate}}</td>
          </tr>
        </tbody>
      </table>
      <div style="clear: both"></div>
      <h3>{{ translatedContent "8809594200e60bc307f2963ad0d20ff3" }}</h3>
      <br>
      <table>
        <thead>
           <tr>
          <th class="column-left">{{ translatedContent "7d74f3b92b19da5e606d737d339a9679" }}</th>
          <th class="column-left">{{ translatedContent "3bdf0cc3bc997094901151b41ec4c9e4" }}</th>
          <th class="column-left">{{ translatedContent "694e8d1f2ee056f98ee488bdc4982d73" }}</th>
          <th class="column-left">{{ translatedContent "19c562a36aeb455d09534f93b4f5236f" }}</th>
        </tr>
        </thead>
        <tbody>
          <tr>
            <td class="column-left">{{xtremManufacturing.workOrder.query.edges.0.node.productionItem.releasedItem.id}}</td>
            <td class="column-left">{{xtremManufacturing.workOrder.query.edges.0.node.productionItem.releasedItem.name}}</td>
            <td class="column-left">{{xtremManufacturing.workOrder.query.edges.0.node.baseQuantity}}</td>
            <td class="column-left">{{xtremManufacturing.workOrder.query.edges.0.node.productionItem.releasedItem.stockUnit.name}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div style="clear: both"></div>
  <div>
    {{#if codeBlockResult.withoutOperationComponents}}
    <h3>{{ translatedContent "3b7009aea937d1b7536c60294807c880" }}</h3>
    <table class="lines-table">
      <thead>
        <tr>
          <th class="column-right">{{ translatedContent "b2ee912b91d69b435159c7c3f6df7f5f" }}</th>
          <th class="column-left">{{ translatedContent "2cb05e4bb7830be982f0922fed86b4cd" }}</th>
          <th class="column-left">{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</th>
          <th class="column-right">{{ translatedContent "b651efdb98a5d6bd2b3935d0c3f4a5e2" }}</th>
          <th class="column-left">{{ translatedContent "19c562a36aeb455d09534f93b4f5236f" }}</th>
          <th class="column-right">{{ translatedContent "00d67560278e758f42df7c0f21279120" }}</th>
        </tr>
      </thead>
      <tbody>
        {{#each codeBlockResult.withoutOperationComponents 'node.requiredQuantity' 'sum'}}
        <tr>
          <td class="column-right"><strong>{{node.componentNumber}}</strong></td>
          <td class="column-left">{{node.item.name}}</td>
          <td class="column-left">{{node.name}}</td>
          <td class="column-right">{{node.requiredQuantity}}</td>
          <td class="column-left">{{node.unit.name}}</td>
          <td class="column-right"></td>
        </tr>
        {{#if node.instruction.value}}
        <tr></tr>
        <tr>
          <td></td>
          <td class="column-left" colspan="5">{{{node.instruction.value}}}</td>
        </tr>
        {{/if}} {{/each}}
      </tbody>
    </table>
    {{/if}} {{#each codeBlockResult.newProductionOperations}}
    <h3>{{ translatedContent "64f77d1958d90fae641e7901efab99a6" }}</h3>
    <table class="header-table">
      <thead>
        <tr>
          <th class="column-left"></th>
          <th class="column-left"></th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>{{ translatedContent "3ff39d3acb327553070a64ef0cb321d5" }}</td>
          <td>{{node.operationNumber}}</td>
        </tr>
        <tr>
          <td>{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</td>
          <td>{{node.name}}</td>
        </tr>
      </tbody>
    </table>
    <table class="header-table">
      <thead>
        <tr>
          <th class="column-left"></th>
          <th class="column-left"></th>
          <th class="column-left"></th>
          <th class="column-left"></th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>{{ translatedContent "e02d2ae03de9d493df2b6b2d2813d302" }}</td>
          <td>{{ translatedContent "ad2376beebecdcf7846ba973fa1a005b" }}</td>
          <td>{{ translatedContent "c5301693c4e792bcd5a479ef38fb8f8d" }}</td>
          <td>{{ translatedContent "96b0141273eabab320119c467cdcaf17" }}</td>
        </tr>
        <tr>
          <td></td>
          <td>{{node.expectedSetupTime}} {{node.setupTimeUnit.symbol}}</td>
          <td>{{node.expectedRunTime}} {{node.runTimeUnit.symbol}}</td>
          <td>{{node.expectedTotalTime}} {{node.runTimeUnit.symbol}}</td>
        </tr>
      </tbody>
    </table>
    {{#if node.instruction.value}}
    <div style="width: 100%">{{{node.instruction.value}}}</div>
    {{/if}}
    <h4>{{ translatedContent "9cd735f9a90166bf8bbf48850c20599e" }}</h4>
    <table class="lines-table">
      <thead>
        <tr>
          <th class="column-left">{{ translatedContent "be8545ae7ab0276e15898aae7acfbd7a" }}</th>
          <th class="column-left">{{ translatedContent "ad2376beebecdcf7846ba973fa1a005b" }}</th>
          <th class="column-left">{{ translatedContent "c5301693c4e792bcd5a479ef38fb8f8d" }}</th>
          <th class="column-left">{{ translatedContent "b29e9799774caa0b2db0cbe5c91d6027" }}</th>
          <th class="column-left">{{ translatedContent "ac8c87fb367123974a14215aa5274312" }}</th>
          <th class="column-left">{{ translatedContent "c9da945ebe01b79486d7f65fb9148984" }}</th>
          <th class="column-left">{{ translatedContent "a2e7a312e731294c5f569d8d4b2b95c5" }}</th>
        </tr>
      </thead>
      <tbody>
        {{#each node.resources.query.edges 'node.expectedRunTime' 'sum' 'node.expectedSetupTime' 'count'}}
        <tr>
          <td class="column-left"><strong>{{node.resource.name}}</strong></td>
          <td class="column-right">{{node.expectedSetupTime}} {{node.setupTimeUnit.symbol}}</td>
          <td class="column-right">{{node.expectedRunTime}} {{node.runTimeUnit.symbol}}</td>
          <td class="column-right"></td>
          <td class="column-right"></td>
          <td class="column-right">{{aggregatedData.node.expectedRunTime.sum}} {{node.runTimeUnit.symbol}}</td>
          <td class="column-right"></td>
        </tr>
        {{/each}}
      </tbody>
    </table>
    {{#if node.newProductionComponents}}
    <h4>{{ translatedContent "3b7009aea937d1b7536c60294807c880" }}</h4>
    <table class="lines-table">
      <thead>
        <tr>
          <th class="column-right">{{ translatedContent "b2ee912b91d69b435159c7c3f6df7f5f" }}</th>
          <th class="column-left">{{ translatedContent "2cb05e4bb7830be982f0922fed86b4cd" }}</th>
          <th class="column-left">{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</th>
          <th class="column-right">{{ translatedContent "b651efdb98a5d6bd2b3935d0c3f4a5e2" }}</th>
          <th class="column-left">{{ translatedContent "19c562a36aeb455d09534f93b4f5236f" }}</th>
          <th class="column-right">{{ translatedContent "00d67560278e758f42df7c0f21279120" }}</th>
        </tr>
      </thead>
      <tbody>
        {{#each node.newProductionComponents 'node.requiredQuantity' 'sum'}}
        <tr>
          <td class="column-right"><strong>{{node.componentNumber}}</strong></td>
          <td class="column-left">{{node.item.name}}</td>
          <td class="column-left">{{node.name}}</td>
          <td class="column-right">{{node.requiredQuantity}}</td>
          <td class="column-left">{{node.unit.name}}</td>
          <td class="column-right"></td>
        </tr>
        {{#if node.instruction.value}}
        <tr></tr>
        <tr>
          <td></td>
          <td class="column-left" colspan="5">{{{node.instruction.value}}}</td>
        </tr>
        {{/if}} {{/each}}
      </tbody>
    </table>
    {{/if}} {{#printBreakIfPropertyWillChange 'node.operationNumber'}}
    <div class="page-break"></div>
    {{/printBreakIfPropertyWillChange}} {{/each}}
    <h1>{{ translatedContent "2a01d572b1447155c310cabafac3fae9" }}</h1>
    {{{xtremManufacturing.workOrder.query.edges.0.node.note.value}}}
  </div>
</div>
