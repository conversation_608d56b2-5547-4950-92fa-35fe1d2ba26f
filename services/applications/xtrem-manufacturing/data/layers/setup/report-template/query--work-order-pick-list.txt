{
  xtremManufacturing {
    workOrder {
      query(
        filter: "{type: 'firm', status: {_in:['pending','inProgress']}, _id:'{{order}}' }"
      ) {
        edges {
          node {
            number
            startDate
            endDate
            type
            status
            site {
              id
              name
            }
            productionItems {
              query(
                first: 500,
              ) {
                edges {
                  node {
                    releasedItem {
                      id
                      name
                    }
                    releasedItemName
                    releasedQuantity
                    stockUnit {
                      name
                    }
                  }
                }
              }
            }
            productionComponents {
              query(
                first: 500,
                filter: "{lineStatus: {_in:['pending','inProgress']}, lineType: {_ne: 'text'}}"
              ) {
                edges {
                  node {
                    name
                    remainingQuantityToReport
                    unit{
                      name
                    }
                    operation {
                      name
                    }
                    item{
                      id
                      lotManagement
                      serialNumberManagement
                    }
                    lineStatus
                    lineType
                    stockAllocations {
                      queryAggregate {
                        edges {
                          node {
                            group {
                              documentLine {
                                _id
                              }
                            }
                            values {
                              quantityInStockUnit {
                                sum
                              }
                            }
                          }
                        }
                      }
                    }
                    stockAllocations {
                      query {
                        edges {
                          node {
                            quantityInStockUnit
                            serialNumberRanges
                            stockRecord {
                              location {
                                name
                              }
                              lot {
                                id
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
