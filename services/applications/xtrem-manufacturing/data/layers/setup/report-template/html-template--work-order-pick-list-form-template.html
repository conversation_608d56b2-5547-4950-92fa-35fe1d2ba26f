<section class="unbreakable-block"><div class="unbreakable-block-body"><section class="record-context" data-context-object-type="WorkOrder" data-context-object-path="xtremManufacturing.workOrder.query.edges.0.node" data-context-filter="[{&quot;_id&quot;:&quot;1&quot;,&quot;label&quot;:&quot;_id&quot;,&quot;filterType&quot;:&quot;matches&quot;,&quot;filterValue&quot;:&quot;order&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;_id&quot;,&quot;title&quot;:&quot;ID&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;IntOrString&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isStored&quot;:true,&quot;isOnInputType&quot;:false,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;_id&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;_id&quot;,&quot;labelPath&quot;:&quot;_id&quot;,&quot;property&quot;:{&quot;name&quot;:&quot;_id&quot;,&quot;title&quot;:&quot;ID&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;IntOrString&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isStored&quot;:true,&quot;isOnInputType&quot;:false,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;_id&quot;,&quot;iconType&quot;:&quot;csv&quot;,&quot;id&quot;:&quot;_id&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;_id&quot;,&quot;title&quot;:&quot;ID&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;IntOrString&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isStored&quot;:true,&quot;isOnInputType&quot;:false,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;_id&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;key&quot;:&quot;_id&quot;,&quot;labelKey&quot;:&quot;_id&quot;,&quot;labelPath&quot;:&quot;_id&quot;},&quot;parameter&quot;:true}]" data-context-list-order="{}" data-alias="rlPALjZt"><!--{{#with rlPALjZt.workOrder.query.edges.0.node}}--><div class="report-context-body"><figure class="table" style="width:100%;"><table class="ck-table-resized"><colgroup><col style="width:47.56%;"><col style="width:52.44%;"></colgroup><tbody><tr><td style="background-color:#dfdfdf;border-color:#dfdfdf;">&nbsp;</td><td style="background-color:#dfdfdf;text-align:right;"><h2><span style="color:#198E59;font-family:'Sage UI', Geneva, sans-serif;">{{ translatedContent "d82f698ad6cae2e0fca7014967607d54" }}</span></h2><p><span style="color:#198E59;font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><strong>{{ translatedContent "f074487b5882b4a50d6faf836db627c2" }}</strong></span><strong><span class="property" data-property-display-label="Number" data-property-data-type="String" data-property-name="number" data-property-data-format="" data-property-parent-context="WorkOrder">{{number}}</span></strong></p></td></tr><tr><td style="border-color:#FFFFFF;"><section class="conditional-block" data-context-condition="[{&quot;conjunction&quot;:&quot;and&quot;,&quot;valueType1&quot;:&quot;property&quot;,&quot;valueType2&quot;:&quot;constant&quot;,&quot;_id&quot;:&quot;1&quot;,&quot;value1&quot;:{&quot;label&quot;:&quot;Released item name&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;releasedItemName&quot;,&quot;title&quot;:&quot;Released item name&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;String&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isStored&quot;:false,&quot;isOnInputType&quot;:false,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;Released item name&quot;,&quot;node&quot;:&quot;String&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;productionItem.releasedItemName&quot;,&quot;key&quot;:&quot;productionItem.releasedItemName&quot;,&quot;labelKey&quot;:&quot;Released item name&quot;,&quot;labelPath&quot;:&quot;Production item > Released item name&quot;},&quot;value2&quot;:null,&quot;key&quot;:&quot;1&quot;,&quot;operator&quot;:&quot;notEmpty&quot;}]"><!--{{#if ( not ( or ( eq productionItem.releasedItemName null ) ( eq productionItem.releasedItemName "" ) ( eq productionItem.releasedItemName undefined ) ) )}}--><div class="conditional-block-body"><span style="font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><strong>{{ translatedContent "d84b3dcc61dec56ba740e6da5a1e40ef" }}</strong><span class="property" data-property-display-label="Released item name" data-property-data-type="String" data-property-name="productionItem.releasedItemName" data-property-data-format="" data-property-parent-context="WorkOrder">{{productionItem.releasedItemName}}</span></span></div><!--{{/if}}--><div class="conditional-block-footer">&nbsp;</div></section><section class="conditional-block" data-context-condition="[{&quot;conjunction&quot;:&quot;and&quot;,&quot;valueType1&quot;:&quot;property&quot;,&quot;valueType2&quot;:&quot;constant&quot;,&quot;_id&quot;:&quot;1&quot;,&quot;value1&quot;:{&quot;label&quot;:&quot;Released item name&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;releasedItemName&quot;,&quot;title&quot;:&quot;Released item name&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;String&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isStored&quot;:false,&quot;isOnInputType&quot;:false,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;Released item name&quot;,&quot;node&quot;:&quot;String&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;productionItem.releasedItemName&quot;,&quot;key&quot;:&quot;productionItem.releasedItemName&quot;,&quot;labelKey&quot;:&quot;Released item name&quot;,&quot;labelPath&quot;:&quot;Production item > Released item name&quot;},&quot;value2&quot;:null,&quot;key&quot;:&quot;1&quot;,&quot;operator&quot;:&quot;notEmpty&quot;}]"><!--{{#if ( not ( or ( eq productionItem.releasedItemName null ) ( eq productionItem.releasedItemName "" ) ( eq productionItem.releasedItemName undefined ) ) )}}--><div class="conditional-block-body"><span style="font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><strong>{{ translatedContent "4edb4f408d28e240dc8a246030a32f40" }}</strong><span class="property" data-property-display-label="Released item name" data-property-data-type="String" data-property-name="productionItem.releasedItemName" data-property-data-format="" data-property-parent-context="WorkOrder">{{productionItem.releasedItemName}}</span></span></div><!--{{/if}}--><div class="conditional-block-footer">&nbsp;</div></section><section class="conditional-block" data-context-condition="[{&quot;conjunction&quot;:&quot;and&quot;,&quot;valueType1&quot;:&quot;property&quot;,&quot;valueType2&quot;:&quot;constant&quot;,&quot;_id&quot;:&quot;1&quot;,&quot;value1&quot;:{&quot;label&quot;:&quot;Remaining quantity&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;remainingQuantity&quot;,&quot;title&quot;:&quot;Remaining quantity&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;Decimal&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isStored&quot;:false,&quot;isOnInputType&quot;:false,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;quantityInStockUnit&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;Remaining quantity&quot;,&quot;node&quot;:&quot;Decimal&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;productionItem.remainingQuantity&quot;,&quot;key&quot;:&quot;productionItem.remainingQuantity&quot;,&quot;labelKey&quot;:&quot;Remaining quantity&quot;,&quot;labelPath&quot;:&quot;Production item > Remaining quantity&quot;},&quot;value2&quot;:null,&quot;key&quot;:&quot;1&quot;,&quot;operator&quot;:&quot;notEmpty&quot;}]"><!--{{#if ( not ( or ( eq productionItem.remainingQuantity null ) ( eq productionItem.remainingQuantity "" ) ( eq productionItem.remainingQuantity undefined ) ) )}}--><div class="conditional-block-body"><span style="font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><strong>{{ translatedContent "29373e0416c914aa8d9f1a62bd0dd5a6" }}</strong><span class="property" data-property-display-label="Released quantity" data-property-data-type="Decimal" data-property-name="productionItem.releasedQuantity" data-property-data-format="2" data-property-parent-context="WorkOrder">{{formatNumber productionItem.releasedQuantity 2}}</span><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="productionItem.stockUnit.name" data-property-data-format="" data-property-parent-context="WorkOrder">{{productionItem.stockUnit.name}}</span></span></div><!--{{/if}}--><div class="conditional-block-footer">&nbsp;</div></section></td><td style="text-align:right;"><p><span style="font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><strong>{{ translatedContent "4d34f1097f6c8b9cee28bca8b78bbee9" }}</strong>{{ translatedContent "853ae90f0351324bd73ea615e6487517" }}<span class="property" data-property-display-label="Start date" data-property-data-type="Date" data-property-name="startDate" data-property-data-format="FullDate" data-property-parent-context="WorkOrder">{{formatDate startDate 'FullDate'}}</span></span></p><p><span style="font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><strong>{{ translatedContent "823a38edcd60271ed5106469ce7de36a" }}</strong><span class="property" data-property-display-label="End date" data-property-data-type="Date" data-property-name="endDate" data-property-data-format="FullDate" data-property-parent-context="WorkOrder">{{formatDate endDate 'FullDate'}}</span></span></p></td></tr></tbody></table></figure><table class="query-table" data-context-object-type="WorkOrderComponent" data-context-object-path="productionComponents.query.edges" data-context-filter="[]" data-context-list-order="{&quot;name&quot;:&quot;ascending&quot;,&quot;item.id&quot;:&quot;ascending&quot;,&quot;remainingQuantityToReport&quot;:&quot;ascending&quot;,&quot;requiredDate&quot;:&quot;ascending&quot;,&quot;operation.name&quot;:&quot;ascending&quot;}" data-alias="seOOSTde"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #dfdfdf;padding:2px;vertical-align:top;"><p><span style="color:#198E59;font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><strong>{{ translatedContent "2cb05e4bb7830be982f0922fed86b4cd" }}&nbsp;</strong></span></p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #dfdfdf;padding:2px;vertical-align:top;"><p><span style="color:#198E59;font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><strong>{{ translatedContent "0740c98b63c78442aad2eb2bb3f2eb80" }}</strong></span></p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #dfdfdf;padding:2px;vertical-align:top;"><p><span style="color:#198E59;font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><strong>{{ translatedContent "91c5b39e297e86e55080174396d3b792" }}&nbsp;</strong></span></p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #dfdfdf;padding:2px;vertical-align:top;"><p><span style="color:#198E59;font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><strong>{{ translatedContent "465960461e87b78cdffeedd8d1930d69" }}</strong></span></p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #dfdfdf;padding:2px;vertical-align:top;"><p><span style="color:#198E59;font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><strong>{{ translatedContent "2a78ed76450c3cb42320882b3e055b31" }}</strong></span></p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #dfdfdf;padding:2px;vertical-align:top;"><p>&nbsp;</p></td></tr></thead><tbody class="query-table-body"><!--{{#each seOOSTde.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #dfdfdf;padding:2px;text-align:left;vertical-align:top;"><p><span style="font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="name" data-property-data-format="" data-property-parent-context="WorkOrderComponent">{{name}}</span></span></p></td><td class="query-table-cell" style="border:1px solid #dfdfdf;padding:2px;text-align:left;vertical-align:top;"><p><span style="font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><span class="property" data-property-display-label="ID" data-property-data-type="String" data-property-name="item.id" data-property-data-format="" data-property-parent-context="WorkOrderComponent">{{item.id}}</span></span></p></td><td class="query-table-cell e-right-align" style="border:1px solid #dfdfdf;padding:2px;text-align:left;vertical-align:top;"><p><span style="font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><span class="property" data-property-display-label="Remaining quantity to report" data-property-data-type="Decimal" data-property-name="remainingQuantityToReport" data-property-data-format="2" data-property-parent-context="WorkOrderComponent">{{formatNumber remainingQuantityToReport 2}}</span><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="unit.name" data-property-data-format="" data-property-parent-context="WorkOrderComponent">{{unit.name}}</span></span></p></td><td class="query-table-cell" style="border:1px solid #dfdfdf;padding:2px;text-align:left;vertical-align:top;"><section class="record-context" data-context-object-type="StockAllocation" data-context-object-path="stockAllocations.query.edges.0.node" data-context-filter="[]" data-context-list-order="{}" data-alias="sjBoUnKz"><!--{{#with sjBoUnKz.query.edges.0.node}}--><div class="report-context-body"><p><span style="font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><span class="property" data-property-display-label="Quantity in stock unit" data-property-data-type="Decimal" data-property-name="quantityInStockUnit" data-property-data-format="2" data-property-parent-context="StockAllocation">{{formatNumber quantityInStockUnit 2}}</span><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="stockRecord.stockUnit.name" data-property-data-format="" data-property-parent-context="StockAllocation">{{stockRecord.stockUnit.name}}</span></span></p></div><!--{{/with}}--><span class="report-context-footer">&nbsp;</span></section></td><td class="query-table-cell" style="border:1px solid #dfdfdf;padding:2px;text-align:left;vertical-align:top;"><p><span style="font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="operation.name" data-property-data-format="" data-property-parent-context="WorkOrderComponent">{{operation.name}}</span></span></p></td><td class="query-table-cell e-right-align" style="border:1px solid #dfdfdf;padding:2px;text-align:left;vertical-align:top;"><table class="query-table" data-context-object-type="StockAllocation" data-context-object-path="stockAllocations.query.edges" data-context-filter="[]" data-context-list-order="{&quot;stockRecord.location.name&quot;:&quot;ascending&quot;,&quot;stockRecord.lot.id&quot;:&quot;ascending&quot;,&quot;stockRecord.quantityInStockUnit&quot;:&quot;ascending&quot;}" data-alias="oMecatnh"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #dfdfdf;padding:2px;text-align:left;vertical-align:top;"><p><span style="color:#198E59;font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><strong>{{ translatedContent "ce5bf551379459c1c61d2a204061c455" }}</strong></span></p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #dfdfdf;padding:2px;text-align:left;vertical-align:top;"><p><span style="color:#198E59;font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><strong>{{ translatedContent "eeec6c7a9d2b475c23650b202208b892" }}</strong></span></p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #dfdfdf;padding:2px;text-align:left;vertical-align:top;"><p><span style="color:#198E59;font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><strong>{{ translatedContent "465960461e87b78cdffeedd8d1930d69" }}</strong></span></p></td></tr></thead><tbody class="query-table-body"><!--{{#each oMecatnh.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #dfdfdf;padding:2px;text-align:left;vertical-align:top;"><p><span style="font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="stockRecord.location.name" data-property-data-format="" data-property-parent-context="StockAllocation">{{stockRecord.location.name}}</span></span></p></td><td class="query-table-cell" style="border:1px solid #dfdfdf;padding:2px;text-align:left;vertical-align:top;"><p><span style="font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><span class="property" data-property-display-label="ID" data-property-data-type="String" data-property-name="stockRecord.lot.id" data-property-data-format="" data-property-parent-context="StockAllocation">{{stockRecord.lot.id}}</span></span></p></td><td class="query-table-cell e-right-align" style="border:1px solid #dfdfdf;padding:2px;text-align:left;vertical-align:top;"><p><span style="font-family:'Sage UI', Geneva, sans-serif;font-size:10pt;"><span class="property" data-property-display-label="Quantity in stock unit" data-property-data-type="Decimal" data-property-name="stockRecord.quantityInStockUnit" data-property-data-format="2" data-property-parent-context="StockAllocation">{{formatNumber stockRecord.quantityInStockUnit 2}}</span></span></p></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="3"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="3"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="6"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="6"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table></div><!--{{/with}}--><span class="report-context-footer">&nbsp;</span></section></div></section>