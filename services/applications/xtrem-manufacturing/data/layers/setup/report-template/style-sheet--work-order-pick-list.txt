@page {
    size: auto;
    margin: 15mm 15mm 15mm 15mm;
}

body {
    font-size: 12px;
    margin: 0px;
    color: var(--textAndLabels);
    -webkit-print-color-adjust: exact;
}

.frame {
  border-style:solid;
  border-color: var(--themePrimary);
  padding: 10px 15px;
}

.strong-theme {
  color: var(--themePrimary);
  font-weight: bold;
}

.po-title {
  padding: 65px 0px 10px 0px;
}

.main {
    font-family: Helvetica;
}

h1,h2,h3,h4,th{
    color: var(--themePrimary);
}

table {
  font-size: 12px;
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 8px;
}

table tbody tr {
  border-bottom: 1px solid var(--tableSeparator);
}

table th {
  border-bottom: 2px solid var(--themePrimary);
  border-right: 1px solid var(--tableSeparator);
  padding: 4px;
  font-size: 12px;
}

table th:last-child {
    border-right: none;
}

table th:first-child {
    padding-left: 0
}

table td {
  font-size: 12px;
  vertical-align: top;
  border-bottom: 1px solid var(--tableSeparator);
  border-right: 1px solid var(--tableSeparator);
  padding: 4px;
}


table td:last-child{
    border-right: none;
    padding-right: 0;
}
table td:first-child{
    padding-left: 0;
}

table .column-left {
  text-align: left;
}

table .column-right{
  text-align: right;
}

table .column-center{
  text-align: center;
}

.header-table{
  width: 100%;
  margin: 25px 0px 25px 0px;
}

.header-table td{
  vertical-align: top;
}

.lines-table {
  margin: 0px 0px 25px 0px;
}

.normal-black{
  font-weight: normal;
  color: black;
  font-family: Helvetica;
  border-bottom: none;
}

table.report-container {
    page-break-after:always;
}

thead.report-header {
    display:table-header-group;
}
tfoot.report-footer {
    display:table-footer-group;
}

table.report-container div.article {
    page-break-inside: avoid;
}
