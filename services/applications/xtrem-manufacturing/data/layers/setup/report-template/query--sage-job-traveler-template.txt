{
  xtremManufacturing {
    workOrder {
      query(filter: "{_id:'{{order}}'}") {
        edges {
          node {
            number
            type
            status
            name
            startDate
            endDate
            plannedProcessCost
            plannedMaterialCost
            actualProcessCost
            actualMaterialCost
            materialCompletionPercentage
            processCompletionPercentage
            productionCompletionPercentage
            baseQuantity
            note {
              value
            }
            site {
              name
              legalCompany {
                name
              }
            }
            productionItem {
              releasedItem {
                id
                name
                description
                stockUnit {
                  name
                }
              }
            }
            productionComponents {
              query(orderBy: "{componentNumber :1}") {
                edges {
                  node {
                    componentNumber
                    operation {
                      operationNumber
                      workOrder {
                        number
                      }
                    }
                    name
                    requiredQuantity
                    unit {
                      name
                    }
                    item{
                      name
                    }
                    consumedQuantity
                    instruction {
                      value
                    }
                  }
                }
              }
            }
            productionOperations {
              query(orderBy: "{operationNumber :1}") {
                edges {
                  node {
                    name
                    operationNumber
                    expectedSetupTime
                    expectedRunTime
                    setupTimeUnit{id, name, symbol}
                    runTimeUnit{id, name, symbol}
                    resources {
                      query {
                        edges {
                          node {
                            resource {
                              name
                            }
                            expectedRunTime
                            actualRunTime
                            actualSetupTime
                            expectedSetupTime
                            actualSetupTime
                            setupTimeUnit{id, name, symbol}
                            runTimeUnit{id, name, symbol}
                          }
                        }
                      }
                    }
                    completedQuantity
                    instruction {
                      value
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
