<div>
  {{#each xtremManufacturing.workOrder.query.edges }}
  <table class="report-container">
    <thead class="report-header">
      <tr>
        <th class="report-header-cell normal-black">
          <div class="header-info">
            <table>
              <tbody>
                <tr>
                  <td class="column-right">
                    <div>
                      <h1>{{ translatedContent "d82f698ad6cae2e0fca7014967607d54" }}</h1>
                      <br />
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="frame">
            <table style="border-style: none">
              <tbody>
                <tr>
                  <td class="column-left"><span class="strong-theme">{{ translatedContent "f074487b5882b4a50d6faf836db627c2" }}</span> {{node.number}}</td>
                  <td class="column-left">
                    <span class="strong-theme">{{ translatedContent "0d668bd70867851f4882d042b8438342" }}</span> {{node.startDate}}
                    <span class="strong-theme">{{ translatedContent "60293a4ec6b989d4fe4da57ab10dcd33" }}</span> {{node.endDate}}
                  </td>
                </tr>
                <tr>
                  <td class="column-left">
                    <span class="strong-theme">{{ translatedContent "b63c2138e69fa48f4d3a9653e1b2089b" }}</span>
                    {{node.productionItems.query.edges.0.node.releasedItemName}}
                  </td>
                  <td class="column-left">
                    <span class="strong-theme">{{ translatedContent "29373e0416c914aa8d9f1a62bd0dd5a6" }}</span>
                    {{node.productionItems.query.edges.0.node.releasedQuantity}} {{node.productionItems.query.edges.0.node.stockUnit.name}}
                  </td>
                </tr>
                <tr>
                  <td class="column-left" colspan="2">
                    <span class="strong-theme">{{ translatedContent "4edb4f408d28e240dc8a246030a32f40" }}</span>
                    {{node.productionItems.query.edges.0.node.releasedItemName}}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </th>
      </tr>
    </thead>

    <tfoot class="report-footer"></tfoot>

    <tbody class="report-content">
      <tr>
        <td class="report-content-cell">
          <div class="main">
            <table class="lines-table">
              <thead>
                <tr>
                  <th class="column-left" style="width: 30%">{{ translatedContent "2cb05e4bb7830be982f0922fed86b4cd" }}</th>
                  <th class="column-left">{{ translatedContent "0740c98b63c78442aad2eb2bb3f2eb80" }}</th>
                  <th class="column-left">{{ translatedContent "91c5b39e297e86e55080174396d3b792" }}</th>
                  <th class="column-left">{{ translatedContent "465960461e87b78cdffeedd8d1930d69" }}</th>
                  <th class="column-left">{{ translatedContent "2a78ed76450c3cb42320882b3e055b31" }}</th>
                </tr>
              </thead>
              <tbody>
                {{#each node.productionComponents.query.edges}}
                <tr>
                  <td class="column-left">{{node.name}}</td>
                  <td class="column-left">{{node.item.id}}</td>
                  <td class="column-right">{{node.remainingQuantityToReport}} {{node.unit.name}}</td>
                  <td class="column-right">
                    {{#if node.stockAllocations.queryAggregate.edges}} {{node.stockAllocations.queryAggregate.edges.0.node.values.quantityInStockUnit.sum}}
                    {{node.unit.name}} {{/if}}
                  </td>
                  <td class="column-left">{{node.operation.name}}</td>
                </tr>
                {{#if node.stockAllocations.queryAggregate.edges}}
                <tr>
                  <td></td>
                  <td colspan="4">
                    <table>
                      <thead>
                        <tr>
                          <th class="column-left">{{ translatedContent "ce5bf551379459c1c61d2a204061c455" }}</th>
                          <th class="column-left">{{ translatedContent "eeec6c7a9d2b475c23650b202208b892" }}</th>
                          <th class="column-left">{{ translatedContent "e79c08243e599c00a89a413b1f4a2833" }}</th>
                          <th class="column-left">{{ translatedContent "465960461e87b78cdffeedd8d1930d69" }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        {{#each node.stockAllocations.query.edges}}
                        <tr>
                          <td class="column-left" width="20%">{{node.stockRecord.location.name}}</td>
                          <td class="column-left" width="20%">{{node.stockRecord.lot.id}}</td>
                          <td class="column-left">
                            <span>{{#with (JSONparse node.serialNumberRanges)}}</span>
                            {{#each ranges}} {{this.from}}{{#eq this.from this.to}}{{else}} - {{this.to}} {{ translatedContent
                            "84c40473414caf2ed4a7b1283e48bbf4" }}{{this.quantity}}){{/eq}}<br />
                            {{/each}} {{/with}}
                          </td>
                          <td class="column-right" width="12%">{{node.quantityInStockUnit}} {{../node.stockUnit.name}}</td>
                        </tr>
                        {{/each}}
                      </tbody>
                    </table>
                  </td>
                </tr>
                {{/if}} {{/each}}
              </tbody>
            </table>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
{{/each}}
