const newProductionOperations =
    queryResponse.xtremManufacturing.workOrder.query.edges[0].node.productionOperations.query.edges.map(
        productionOperation => {
            const productionComponents =
                queryResponse.xtremManufacturing.workOrder.query.edges[0].node.productionComponents.query.edges.filter(
                    productionComponent =>
                        productionComponent.node.operation?.operationNumber ===
                        productionOperation.node.operationNumber,
                );
            productionOperation.node.newProductionComponents = productionComponents;
            productionOperation.node.expectedTotalTime =
                +productionOperation.node.expectedSetupTime + +productionOperation.node.expectedRunTime;
            return productionOperation;
        },
    );

const withoutOperationComponents = 
    queryResponse.xtremManufacturing.workOrder.query.edges[0].node.productionComponents.query.edges.filter(
        productionComponent =>
            !productionComponent.node.operation?.operationNumber
    );
return { newProductionOperations, withoutOperationComponents };