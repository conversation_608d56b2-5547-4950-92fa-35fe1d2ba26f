{"data": [{"_id": "0", "path": "isDuplication", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is duplication (false/true)"}, {"_id": "10", "path": "!site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "30", "path": "*type", "locale": "", "dataType": "enum(planned,firm)", "isCustom": false, "description": "type"}, {"_id": "40", "path": "typeFiltered", "locale": "", "dataType": "enum(firm,planned)", "isCustom": false, "description": "type filtered"}, {"_id": "50", "path": "status", "locale": "", "dataType": "enum(pending,inProgress,completed,closed,costCalculated,printed)", "isCustom": false, "description": "status"}, {"_id": "60", "path": "doInheritDimensions", "locale": "", "dataType": "boolean", "isCustom": false, "description": "do inherit dimensions (false/true)"}, {"_id": "70", "path": "is<PERSON>orward<PERSON><PERSON>uling", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is forward scheduling (true/false)"}, {"_id": "80", "path": "isSchedulingSkipped", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is scheduling skipped (false/true)"}, {"_id": "90", "path": "timeZone", "locale": "", "dataType": "string", "isCustom": false, "description": "time zone"}, {"_id": "100", "path": "schedulingStatus", "locale": "", "dataType": "enum(notScheduled,inProgress,scheduled,toReschedule,notManaged)", "isCustom": false, "description": "scheduling status"}, {"_id": "110", "path": "creationDate", "locale": "", "dataType": "date", "isCustom": false, "description": "creation date (yyyy-MM-dd)"}, {"_id": "120", "path": "requestedDate", "locale": "", "dataType": "date", "isCustom": false, "description": "requested date (yyyy-MM-dd)"}, {"_id": "130", "path": "startDatetime", "locale": "", "dataType": "datetime", "isCustom": false, "description": "start datetime (yyyy-MM-ddThh:mm:ssZ)"}, {"_id": "140", "path": "startDate", "locale": "", "dataType": "date", "isCustom": false, "description": "start date (yyyy-MM-dd)"}, {"_id": "150", "path": "endDatetime", "locale": "", "dataType": "datetime", "isCustom": false, "description": "end datetime (yyyy-MM-ddThh:mm:ssZ)"}, {"_id": "160", "path": "endDate", "locale": "", "dataType": "date", "isCustom": false, "description": "end date (yyyy-MM-dd)"}, {"_id": "170", "path": "closingDate", "locale": "", "dataType": "date", "isCustom": false, "description": "closing date (yyyy-MM-dd)"}, {"_id": "180", "path": "category", "locale": "", "dataType": "reference", "isCustom": false, "description": "category (#id)"}, {"_id": "190", "path": "bomCode", "locale": "", "dataType": "reference", "isCustom": false, "description": "bom code (#item|site)"}, {"_id": "200", "path": "routingCode", "locale": "", "dataType": "reference", "isCustom": false, "description": "routing code (#item|site)"}, {"_id": "210", "path": "routingTimeUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "routing time unit (#id)"}, {"_id": "220", "path": "baseQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "base quantity"}, {"_id": "230", "path": "baseRoutingQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "base routing quantity"}, {"_id": "240", "path": "actualOverheadCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "actual overhead cost"}, {"_id": "250", "path": "timeUnit", "locale": "", "dataType": "enum(hours,minutes,seconds)", "isCustom": false, "description": "time unit"}, {"_id": "260", "path": "note", "locale": "", "dataType": "textStream", "isCustom": false, "description": "note"}, {"_id": "270", "path": "name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "280", "path": "plannedOverheadCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "planned overhead cost"}, {"_id": "290", "path": "#productionItems", "locale": "", "dataType": "collection", "isCustom": false, "description": "production items"}, {"_id": "310", "path": "!releasedItem", "locale": "", "dataType": "reference", "isCustom": false, "description": "released item (#id)"}, {"_id": "320", "path": "totalActualQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total actual quantity"}, {"_id": "330", "path": "lineStatus", "locale": "", "dataType": "enum(pending,inProgress,completed,excluded,included)", "isCustom": false, "description": "line status"}, {"_id": "340", "path": "stockTransactionStatus", "locale": "", "dataType": "enum(draft,inProgress,completed,error)", "isCustom": false, "description": "stock transaction status"}, {"_id": "350", "path": "storedDimensions", "locale": "", "dataType": "json", "isCustom": false, "description": "stored dimensions"}, {"_id": "360", "path": "storedAttributes", "locale": "", "dataType": "json", "isCustom": false, "description": "stored attributes"}, {"_id": "370", "path": "plannedMaterialCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "planned material cost"}, {"_id": "380", "path": "plannedMachineCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "planned machine cost"}, {"_id": "390", "path": "plannedLaborCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "planned labor cost"}, {"_id": "400", "path": "plannedToolCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "planned tool cost"}, {"_id": "420", "path": "releasedQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "released quantity"}, {"_id": "430", "path": "//workInProgress", "locale": "", "dataType": "reference", "isCustom": false, "description": "work in progress (#workOrderReleasedItem)"}, {"_id": "440", "path": "remainingQuantityToAllocate", "locale": "", "dataType": "decimal", "isCustom": false, "description": "remaining quantity to allocate"}, {"_id": "450", "path": "documentType", "locale": "", "dataType": "enum(workOrder,materialNeed,purchaseOrder,purchaseReceipt,purchaseReturn,salesOrder,stockTransferOrder,stockTransferReceipt)", "isCustom": false, "description": "document type"}, {"_id": "460", "path": "item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "470", "path": "site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "480", "path": "status#1", "locale": "", "dataType": "enum(firm,planned,suggested,closed)", "isCustom": false, "description": "status"}, {"_id": "490", "path": "startDate#1", "locale": "", "dataType": "date", "isCustom": false, "description": "start date (yyyy-MM-dd)"}, {"_id": "500", "path": "endDate#1", "locale": "", "dataType": "date", "isCustom": false, "description": "end date (yyyy-MM-dd)"}, {"_id": "510", "path": "expectedQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "expected quantity"}, {"_id": "520", "path": "actualQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "actual quantity"}, {"_id": "530", "path": "outstandingQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "outstanding quantity"}, {"_id": "540", "path": "##stockTransactions", "locale": "", "dataType": "collection", "isCustom": false, "description": "stock transactions"}, {"_id": "560", "path": "notificationId", "locale": "", "dataType": "string", "isCustom": false, "description": "notification id"}, {"_id": "570", "path": "status#2", "locale": "", "dataType": "enum(inProgress,succeeded,error)", "isCustom": false, "description": "status"}, {"_id": "580", "path": "message", "locale": "", "dataType": "string", "isCustom": false, "description": "message"}, {"_id": "590", "path": "resultAction", "locale": "", "dataType": "enum(none,created,increased,decreased,deleted,adjusted,changed,allocated,deallocated,corrected,noChange,valueChange,transferred)", "isCustom": false, "description": "result action"}, {"_id": "600", "path": "//analyticalData", "locale": "", "dataType": "reference", "isCustom": false, "description": "analytical data"}, {"_id": "610", "path": "financialSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "financial site (#id)"}, {"_id": "620", "path": "businessSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "business site (#id)"}, {"_id": "630", "path": "stockSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site (#id)"}, {"_id": "640", "path": "manufacturingSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "manufacturing site (#id)"}, {"_id": "650", "path": "supplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier (#businessEntity)"}, {"_id": "660", "path": "customer", "locale": "", "dataType": "reference", "isCustom": false, "description": "customer (#businessEntity)"}, {"_id": "670", "path": "project", "locale": "", "dataType": "reference", "isCustom": false, "description": "project (#id|attributeType|attributeRestrictedToId)"}, {"_id": "680", "path": "task", "locale": "", "dataType": "reference", "isCustom": false, "description": "task (#id|attributeType|attributeRestrictedToId)"}, {"_id": "690", "path": "employee", "locale": "", "dataType": "reference", "isCustom": false, "description": "employee (#id|attributeType|attributeRestrictedToId)"}, {"_id": "700", "path": "item#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "710", "path": "dimension01", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 01 (#id|dimensionType)"}, {"_id": "720", "path": "dimension02", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 02 (#id|dimensionType)"}, {"_id": "730", "path": "dimension03", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 03 (#id|dimensionType)"}, {"_id": "740", "path": "dimension04", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 04 (#id|dimensionType)"}, {"_id": "750", "path": "dimension05", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 05 (#id|dimensionType)"}, {"_id": "760", "path": "dimension06", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 06 (#id|dimensionType)"}, {"_id": "770", "path": "dimension07", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 07 (#id|dimensionType)"}, {"_id": "780", "path": "dimension08", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 08 (#id|dimensionType)"}, {"_id": "790", "path": "dimension09", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 09 (#id|dimensionType)"}, {"_id": "800", "path": "dimension10", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 10 (#id|dimensionType)"}, {"_id": "810", "path": "dimension11", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 11 (#id|dimensionType)"}, {"_id": "820", "path": "dimension12", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 12 (#id|dimensionType)"}, {"_id": "830", "path": "dimension13", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 13 (#id|dimensionType)"}, {"_id": "840", "path": "dimension14", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 14 (#id|dimensionType)"}, {"_id": "850", "path": "dimension15", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 15 (#id|dimensionType)"}, {"_id": "860", "path": "dimension16", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 16 (#id|dimensionType)"}, {"_id": "870", "path": "dimension17", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 17 (#id|dimensionType)"}, {"_id": "880", "path": "dimension18", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 18 (#id|dimensionType)"}, {"_id": "890", "path": "dimension19", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 19 (#id|dimensionType)"}, {"_id": "900", "path": "dimension20", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 20 (#id|dimensionType)"}, {"_id": "910", "path": "#productionOperations", "locale": "", "dataType": "collection", "isCustom": false, "description": "production operations"}, {"_id": "930", "path": "!operationNumber", "locale": "", "dataType": "integer", "isCustom": false, "description": "operation number"}, {"_id": "940", "path": "name#1", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "950", "path": "setupTimeUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "setup time unit (#id)"}, {"_id": "960", "path": "runTimeUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "run time unit (#id)"}, {"_id": "970", "path": "*minCapabilityLevel", "locale": "", "dataType": "reference", "isCustom": false, "description": "min capability level (#id)"}, {"_id": "980", "path": "isProductionStep", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is production step (false/true)"}, {"_id": "990", "path": "status#3", "locale": "", "dataType": "enum(pending,inProgress,completed,excluded,included)", "isCustom": false, "description": "status"}, {"_id": "1000", "path": "isAdded", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is added (false/true)"}, {"_id": "1010", "path": "startDatetime#1", "locale": "", "dataType": "datetime", "isCustom": false, "description": "start datetime (yyyy-MM-ddThh:mm:ssZ)"}, {"_id": "1020", "path": "startDate#2", "locale": "", "dataType": "date", "isCustom": false, "description": "start date (yyyy-MM-dd)"}, {"_id": "1030", "path": "endDatetime#1", "locale": "", "dataType": "datetime", "isCustom": false, "description": "end datetime (yyyy-MM-ddThh:mm:ssZ)"}, {"_id": "1040", "path": "endDate#2", "locale": "", "dataType": "date", "isCustom": false, "description": "end date (yyyy-MM-dd)"}, {"_id": "1050", "path": "plannedQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "planned quantity"}, {"_id": "1060", "path": "completedQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "completed quantity"}, {"_id": "1070", "path": "instruction", "locale": "", "dataType": "textStream", "isCustom": false, "description": "instruction"}, {"_id": "1080", "path": "##resources", "locale": "", "dataType": "collection", "isCustom": false, "description": "resources"}, {"_id": "1090", "path": "!_sortValue", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": "1100", "path": "*resource", "locale": "", "dataType": "reference", "isCustom": false, "description": "resource (#id|site)"}, {"_id": "1110", "path": "efficiency", "locale": "", "dataType": "decimal", "isCustom": false, "description": "efficiency"}, {"_id": "1120", "path": "expectedRunTime", "locale": "", "dataType": "decimal", "isCustom": false, "description": "expected run time"}, {"_id": "1130", "path": "expectedSetupTime", "locale": "", "dataType": "decimal", "isCustom": false, "description": "expected setup time"}, {"_id": "1140", "path": "actualRunTime", "locale": "", "dataType": "decimal", "isCustom": false, "description": "actual run time"}, {"_id": "1150", "path": "actualSetupTime", "locale": "", "dataType": "decimal", "isCustom": false, "description": "actual setup time"}, {"_id": "1160", "path": "actualRunCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "actual run cost"}, {"_id": "1170", "path": "actualSetupCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "actual setup cost"}, {"_id": "1180", "path": "status#4", "locale": "", "dataType": "enum(pending,inProgress,completed,excluded,included)", "isCustom": false, "description": "status"}, {"_id": "1190", "path": "isAdded#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is added (false/true)"}, {"_id": "1200", "path": "isResourceQuantity", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is resource quantity (false/true)"}, {"_id": "1210", "path": "storedDimensions#1", "locale": "", "dataType": "json", "isCustom": false, "description": "stored dimensions"}, {"_id": "1220", "path": "storedAttributes#1", "locale": "", "dataType": "json", "isCustom": false, "description": "stored attributes"}, {"_id": "1230", "path": "startDatetime#2", "locale": "", "dataType": "datetime", "isCustom": false, "description": "start datetime (yyyy-MM-ddThh:mm:ssZ)"}, {"_id": "1240", "path": "endDatetime#2", "locale": "", "dataType": "datetime", "isCustom": false, "description": "end datetime (yyyy-MM-ddThh:mm:ssZ)"}, {"_id": "1250", "path": "expectedRunCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "expected run cost"}, {"_id": "1260", "path": "expectedSetupCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "expected setup cost"}, {"_id": "1270", "path": "###resources", "locale": "", "dataType": "collection", "isCustom": false, "description": "resources"}, {"_id": "1280", "path": "!_sortValue#1", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": "1290", "path": "*resource#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "resource (#id|site)"}, {"_id": "1300", "path": "///analyticalData", "locale": "", "dataType": "reference", "isCustom": false, "description": "analytical data"}, {"_id": "1310", "path": "financialSite#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "financial site (#id)"}, {"_id": "1320", "path": "businessSite#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "business site (#id)"}, {"_id": "1330", "path": "stockSite#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site (#id)"}, {"_id": "1340", "path": "manufacturingSite#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "manufacturing site (#id)"}, {"_id": "1350", "path": "supplier#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier (#businessEntity)"}, {"_id": "1360", "path": "customer#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "customer (#businessEntity)"}, {"_id": "1370", "path": "project#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "project (#id|attributeType|attributeRestrictedToId)"}, {"_id": "1380", "path": "task#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "task (#id|attributeType|attributeRestrictedToId)"}, {"_id": "1390", "path": "employee#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "employee (#id|attributeType|attributeRestrictedToId)"}, {"_id": "1400", "path": "item#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "1410", "path": "dimension01#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 01 (#id|dimensionType)"}, {"_id": "1420", "path": "dimension02#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 02 (#id|dimensionType)"}, {"_id": "1430", "path": "dimension03#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 03 (#id|dimensionType)"}, {"_id": "1440", "path": "dimension04#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 04 (#id|dimensionType)"}, {"_id": "1450", "path": "dimension05#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 05 (#id|dimensionType)"}, {"_id": "1460", "path": "dimension06#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 06 (#id|dimensionType)"}, {"_id": "1470", "path": "dimension07#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 07 (#id|dimensionType)"}, {"_id": "1480", "path": "dimension08#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 08 (#id|dimensionType)"}, {"_id": "1490", "path": "dimension09#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 09 (#id|dimensionType)"}, {"_id": "1500", "path": "dimension10#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 10 (#id|dimensionType)"}, {"_id": "1510", "path": "dimension11#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 11 (#id|dimensionType)"}, {"_id": "1520", "path": "dimension12#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 12 (#id|dimensionType)"}, {"_id": "1530", "path": "dimension13#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 13 (#id|dimensionType)"}, {"_id": "1540", "path": "dimension14#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 14 (#id|dimensionType)"}, {"_id": "1550", "path": "dimension15#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 15 (#id|dimensionType)"}, {"_id": "1560", "path": "dimension16#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 16 (#id|dimensionType)"}, {"_id": "1570", "path": "dimension17#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 17 (#id|dimensionType)"}, {"_id": "1580", "path": "dimension18#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 18 (#id|dimensionType)"}, {"_id": "1590", "path": "dimension19#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 19 (#id|dimensionType)"}, {"_id": "1600", "path": "dimension20#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 20 (#id|dimensionType)"}, {"_id": "1610", "path": "#productionComponents", "locale": "", "dataType": "collection", "isCustom": false, "description": "production components"}, {"_id": "1620", "path": "_sortValue#3", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": "1630", "path": "!componentNumber", "locale": "", "dataType": "integer", "isCustom": false, "description": "component number"}, {"_id": "1640", "path": "lineType", "locale": "", "dataType": "enum(normal,text)", "isCustom": false, "description": "line type"}, {"_id": "1650", "path": "item#3", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "1660", "path": "name#2", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "1670", "path": "unit", "locale": "", "dataType": "reference", "isCustom": false, "description": "unit (#id)"}, {"_id": "1680", "path": "isFixedLinkQuantity", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is fixed link quantity (false/true)"}, {"_id": "1690", "path": "linkQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "link quantity"}, {"_id": "1700", "path": "scrapFactor", "locale": "", "dataType": "decimal", "isCustom": false, "description": "scrap factor"}, {"_id": "1710", "path": "operation", "locale": "", "dataType": "reference", "isCustom": false, "description": "operation (#workOrder|operationNumber)"}, {"_id": "1720", "path": "requiredQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "required quantity"}, {"_id": "1730", "path": "consumedQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "consumed quantity"}, {"_id": "1740", "path": "requiredDate", "locale": "", "dataType": "date", "isCustom": false, "description": "required date (yyyy-MM-dd)"}, {"_id": "1750", "path": "lineStatus#1", "locale": "", "dataType": "enum(pending,inProgress,completed,excluded,included)", "isCustom": false, "description": "line status"}, {"_id": "1760", "path": "isAdded#2", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is added (false/true)"}, {"_id": "1770", "path": "plannedCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "planned cost"}, {"_id": "1780", "path": "actualCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "actual cost"}, {"_id": "1790", "path": "storedDimensions#2", "locale": "", "dataType": "json", "isCustom": false, "description": "stored dimensions"}, {"_id": "1800", "path": "storedAttributes#2", "locale": "", "dataType": "json", "isCustom": false, "description": "stored attributes"}, {"_id": "1810", "path": "allocationRequestStatus", "locale": "", "dataType": "enum(noRequest,inProgress,error,completed)", "isCustom": false, "description": "allocation request status"}, {"_id": "1820", "path": "instruction#1", "locale": "", "dataType": "textStream", "isCustom": false, "description": "instruction"}, {"_id": "1830", "path": "//workInProgress#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "work in progress (#workOrderComponent)"}, {"_id": "1840", "path": "remainingQuantityToAllocate#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "remaining quantity to allocate"}, {"_id": "1850", "path": "documentType#1", "locale": "", "dataType": "enum(workOrder,materialNeed,purchaseOrder,purchaseReceipt,purchaseReturn,salesOrder,stockTransferOrder,stockTransferReceipt)", "isCustom": false, "description": "document type"}, {"_id": "1860", "path": "item#4", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "1870", "path": "site#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "1880", "path": "status#5", "locale": "", "dataType": "enum(firm,planned,suggested,closed)", "isCustom": false, "description": "status"}, {"_id": "1890", "path": "startDate#3", "locale": "", "dataType": "date", "isCustom": false, "description": "start date (yyyy-MM-dd)"}, {"_id": "1900", "path": "endDate#3", "locale": "", "dataType": "date", "isCustom": false, "description": "end date (yyyy-MM-dd)"}, {"_id": "1910", "path": "expectedQuantity#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "expected quantity"}, {"_id": "1920", "path": "actualQuantity#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "actual quantity"}, {"_id": "1930", "path": "outstandingQuantity#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "outstanding quantity"}, {"_id": "1940", "path": "//analyticalData#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "analytical data"}, {"_id": "1950", "path": "financialSite#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "financial site (#id)"}, {"_id": "1960", "path": "businessSite#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "business site (#id)"}, {"_id": "1970", "path": "stockSite#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site (#id)"}, {"_id": "1980", "path": "manufacturingSite#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "manufacturing site (#id)"}, {"_id": "1990", "path": "supplier#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier (#businessEntity)"}, {"_id": "2000", "path": "customer#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "customer (#businessEntity)"}, {"_id": "2010", "path": "project#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "project (#id|attributeType|attributeRestrictedToId)"}, {"_id": "2020", "path": "task#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "task (#id|attributeType|attributeRestrictedToId)"}, {"_id": "2030", "path": "employee#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "employee (#id|attributeType|attributeRestrictedToId)"}, {"_id": "2040", "path": "item#5", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "2050", "path": "dimension01#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 01 (#id|dimensionType)"}, {"_id": "2060", "path": "dimension02#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 02 (#id|dimensionType)"}, {"_id": "2070", "path": "dimension03#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 03 (#id|dimensionType)"}, {"_id": "2080", "path": "dimension04#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 04 (#id|dimensionType)"}, {"_id": "2090", "path": "dimension05#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 05 (#id|dimensionType)"}, {"_id": "2100", "path": "dimension06#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 06 (#id|dimensionType)"}, {"_id": "2110", "path": "dimension07#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 07 (#id|dimensionType)"}, {"_id": "2120", "path": "dimension08#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 08 (#id|dimensionType)"}, {"_id": "2130", "path": "dimension09#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 09 (#id|dimensionType)"}, {"_id": "2140", "path": "dimension10#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 10 (#id|dimensionType)"}, {"_id": "2150", "path": "dimension11#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 11 (#id|dimensionType)"}, {"_id": "2160", "path": "dimension12#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 12 (#id|dimensionType)"}, {"_id": "2170", "path": "dimension13#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 13 (#id|dimensionType)"}, {"_id": "2180", "path": "dimension14#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 14 (#id|dimensionType)"}, {"_id": "2190", "path": "dimension15#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 15 (#id|dimensionType)"}, {"_id": "2200", "path": "dimension16#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 16 (#id|dimensionType)"}, {"_id": "2210", "path": "dimension17#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 17 (#id|dimensionType)"}, {"_id": "2220", "path": "dimension18#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 18 (#id|dimensionType)"}, {"_id": "2230", "path": "dimension19#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 19 (#id|dimensionType)"}, {"_id": "2240", "path": "dimension20#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 20 (#id|dimensionType)"}]}