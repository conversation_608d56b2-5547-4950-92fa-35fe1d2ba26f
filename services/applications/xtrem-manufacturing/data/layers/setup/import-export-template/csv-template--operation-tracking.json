{"data": [{"_id": "10", "path": "*workOrder", "locale": "", "dataType": "reference", "isCustom": false, "description": "work order (#site|number)"}, {"_id": "20", "path": "site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "30", "path": "entryDate", "locale": "", "dataType": "date", "isCustom": false, "description": "entry date (yyyy-MM-dd)"}, {"_id": "40", "path": "effectiveDate", "locale": "", "dataType": "date", "isCustom": false, "description": "effective date (yyyy-MM-dd)"}, {"_id": "50", "path": "#lines", "locale": "", "dataType": "collection", "isCustom": false, "description": "lines"}, {"_id": "70", "path": "line", "locale": "", "dataType": "integer", "isCustom": false, "description": "line"}, {"_id": "80", "path": "*workOrderOperation", "locale": "", "dataType": "reference", "isCustom": false, "description": "work order operation (#workOrder|operationNumber)"}, {"_id": "90", "path": "trackingType", "locale": "", "dataType": "string", "isCustom": false, "description": "tracking type"}, {"_id": "100", "path": "completedQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "completed quantity"}, {"_id": "110", "path": "*actualResource", "locale": "", "dataType": "reference", "isCustom": false, "description": "actual resource (#id|site)"}, {"_id": "120", "path": "setupTimeUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "setup time unit (#id)"}, {"_id": "130", "path": "runTimeUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "run time unit (#id)"}, {"_id": "140", "path": "actualSetupTime", "locale": "", "dataType": "decimal", "isCustom": false, "description": "actual setup time"}, {"_id": "150", "path": "actualRunTime", "locale": "", "dataType": "decimal", "isCustom": false, "description": "actual run time"}, {"_id": "160", "path": "completed", "locale": "", "dataType": "boolean", "isCustom": false, "description": "completed (false/true)"}, {"_id": "170", "path": "storedDimensions", "locale": "", "dataType": "json", "isCustom": false, "description": "stored dimensions"}, {"_id": "180", "path": "storedAttributes", "locale": "", "dataType": "json", "isCustom": false, "description": "stored attributes"}, {"_id": "190", "path": "//analyticalData", "locale": "", "dataType": "reference", "isCustom": false, "description": "analytical data"}, {"_id": "200", "path": "financialSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "financial site (#id)"}, {"_id": "210", "path": "businessSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "business site (#id)"}, {"_id": "220", "path": "stockSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site (#id)"}, {"_id": "230", "path": "manufacturingSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "manufacturing site (#id)"}, {"_id": "240", "path": "supplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier (#businessEntity)"}, {"_id": "250", "path": "customer", "locale": "", "dataType": "reference", "isCustom": false, "description": "customer (#businessEntity)"}, {"_id": "260", "path": "project", "locale": "", "dataType": "reference", "isCustom": false, "description": "project (#id|attributeType|attributeRestrictedToId)"}, {"_id": "270", "path": "task", "locale": "", "dataType": "reference", "isCustom": false, "description": "task (#id|attributeType|attributeRestrictedToId)"}, {"_id": "280", "path": "employee", "locale": "", "dataType": "reference", "isCustom": false, "description": "employee (#id|attributeType|attributeRestrictedToId)"}, {"_id": "290", "path": "item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "300", "path": "dimension01", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 01 (#id|dimensionType)"}, {"_id": "310", "path": "dimension02", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 02 (#id|dimensionType)"}, {"_id": "320", "path": "dimension03", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 03 (#id|dimensionType)"}, {"_id": "330", "path": "dimension04", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 04 (#id|dimensionType)"}, {"_id": "340", "path": "dimension05", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 05 (#id|dimensionType)"}, {"_id": "350", "path": "dimension06", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 06 (#id|dimensionType)"}, {"_id": "360", "path": "dimension07", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 07 (#id|dimensionType)"}, {"_id": "370", "path": "dimension08", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 08 (#id|dimensionType)"}, {"_id": "380", "path": "dimension09", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 09 (#id|dimensionType)"}, {"_id": "390", "path": "dimension10", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 10 (#id|dimensionType)"}, {"_id": "400", "path": "dimension11", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 11 (#id|dimensionType)"}, {"_id": "410", "path": "dimension12", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 12 (#id|dimensionType)"}, {"_id": "420", "path": "dimension13", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 13 (#id|dimensionType)"}, {"_id": "430", "path": "dimension14", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 14 (#id|dimensionType)"}, {"_id": "440", "path": "dimension15", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 15 (#id|dimensionType)"}, {"_id": "450", "path": "dimension16", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 16 (#id|dimensionType)"}, {"_id": "460", "path": "dimension17", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 17 (#id|dimensionType)"}, {"_id": "470", "path": "dimension18", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 18 (#id|dimensionType)"}, {"_id": "480", "path": "dimension19", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 19 (#id|dimensionType)"}, {"_id": "490", "path": "dimension20", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension 20 (#id|dimensionType)"}]}