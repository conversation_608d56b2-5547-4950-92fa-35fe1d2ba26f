{"data": [{"_id": "0", "path": "*workOrder", "locale": "", "dataType": "reference", "isCustom": false, "description": "work order (#site|number)"}, {"_id": "5", "path": "workOrder.number", "locale": "", "dataType": "string", "description": "number"}, {"_id": "810", "path": "workOrder.site", "locale": "", "dataType": "reference", "description": "site"}, {"_id": "820", "path": "workOrder.site.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "830", "path": "workOrder.site.name", "locale": "", "dataType": "string", "description": "name"}, {"_id": "910", "path": "workOrder.productionItem", "locale": "", "dataType": "reference", "description": "production item"}, {"_id": "920", "path": "workOrder.productionItem.releasedItem", "locale": "", "dataType": "reference", "isCustom": null, "description": "released item (#id)"}, {"_id": "930", "path": "workOrder.productionItem.releasedItem.id", "locale": "", "dataType": "string", "isCustom": null, "description": "id"}, {"_id": "940", "path": "workOrder.productionItem.releasedItem.name", "locale": "en", "dataType": "localized text", "isCustom": null, "description": "id"}, {"_id": "30", "path": "cost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "cost"}, {"_id": "40", "path": "amount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "amount"}, {"_id": "10", "path": "quantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity"}, {"_id": "200", "path": "*unit", "locale": "", "dataType": "reference", "isCustom": false, "description": "unit (#id)"}, {"_id": "210", "path": "unit.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "220", "path": "unit.name", "locale": "en", "dataType": "localized text", "description": "name"}, {"_id": "60", "path": "status", "locale": "", "dataType": "enum(pending,posted,error)", "isCustom": false, "description": "status"}, {"_id": "50", "path": "*currency", "locale": "", "dataType": "reference", "isCustom": false, "description": "currency (#id)"}, {"_id": "110", "path": "currency.name", "locale": "en", "dataType": "localized text", "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "120", "path": "currency.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "70", "path": "effectiveDate", "locale": "", "dataType": "date", "isCustom": false, "description": "effective date (yyyy-MM-dd)"}, {"_id": "90", "path": "type", "locale": "ên", "dataType": "enum(materialTracking,setupTimeTracking,runTimeTracking,productionTracking,workOrderIndirectCost,workOrderVariance,workOrderNegativeVariance,workOrderActualCostAdjustment,workOrderNegativeActualCostAdjustment,workOrderActualCostAdjustmentNonAbsorbed,workOrderNegativeActualCostAdjustmentNonAbsorbed)", "isCustom": false, "description": "type"}, {"_id": "600", "path": "*originatingLine", "locale": "", "dataType": "reference", "isCustom": false, "description": "Originating line"}, {"_id": "610", "path": "originatingLine.documentNumber", "locale": "", "dataType": "string", "description": "Document number"}]}