"id";"_vendor";"name";"description";"node_name";"is_active";"is_default";"template_use";"default_parameters";"csv_template"
"OperationTracking";"sage";"{""en"":""OperationTracking"",""base"":""OperationTracking"",""en-US"":""OperationTracking""}";"{""en-US"":""""}";"OperationTracking";"Y";"Y";"importAndExport";"file:default-parameters--operation-tracking.json";"file:csv-template--operation-tracking.json"
"WorkOrder";"sage";"{""en"":""WorkOrder"",""base"":""WorkOrder"",""en-US"":""WorkOrder""}";"{""en"":"""",""en-US"":""""}";"WorkOrder";"Y";"Y";"importAndExport";"file:default-parameters--work-order.json";"file:csv-template--work-order.json"
"WipTransactionInquiry";"sage";"{""en"":""Work in progress transaction inquiry"",""base"":""Work in progress transaction inquiry"",""en-US"":""Work in progress transaction inquiry""}";"{""en"":""Default export template for work in progress transaction inquiry"",""base"":""Default export template for work in progress transaction inquiry"",""en-US"":""""}";"WorkInProgressCost";"Y";"Y";"exportOnly";"file:default-parameters--wip-transaction-inquiry.json";"file:csv-template--wip-transaction-inquiry.json"
