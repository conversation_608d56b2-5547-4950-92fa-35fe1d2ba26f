import { Graph<PERSON><PERSON> } from '@sage/xtrem-declarations-api';
import { DeliveryMode } from '@sage/xtrem-master-data/build/lib/pages/delivery-mode';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<DeliveryModeExtension>({
    extends: '@sage/xtrem-master-data/DeliveryMode',
    navigationPanel: {
        listItem: {
            line3: ui.nestedFieldExtensions.dropdownList({
                title: 'Intrastat transport mode',
                optionType: '@sage/xtrem-declarations/IntrastatModeOfTransport',
                bind: 'intrastatModeOfTransport',
                map(this, value?, rowValue?) {
                    const formattedValue = value.replace(/(?!^)([A-Z]|\d+)/g, ' $1').toLowerCase();
                    return formattedValue.charAt(0).toUpperCase() + formattedValue.slice(1);
                },
                isHiddenOnMainField: true,
            }),
        },
    },
})
export class DeliveryModeExtension extends ui.PageExtension<DeliveryMode, GraphApi> {
    @ui.decorators.separatorField<DeliveryModeExtension>({
        parent() {
            return this.block;
        },
        insertAfter() {
            return this.description;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorStatus: ui.fields.Separator;

    @ui.decorators.dropdownListField<DeliveryModeExtension>({
        parent() {
            return this.block;
        },
        title: 'Intrastat transport mode',
        isMandatory: true,
        optionType: '@sage/xtrem-declarations/IntrastatModeOfTransport',
    })
    intrastatModeOfTransport: ui.fields.DropdownList;
}

declare module '@sage/xtrem-master-data/build/lib/pages/delivery-mode' {
    interface DeliveryMode extends DeliveryModeExtension {}
}
