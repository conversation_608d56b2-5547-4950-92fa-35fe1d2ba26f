import { Graph<PERSON><PERSON> } from '@sage/xtrem-declarations-api';
import { UnitOfMeasure } from '@sage/xtrem-master-data-api';
import { Item } from '@sage/xtrem-master-data/build/lib/pages/item';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<ItemExtension>({
    extends: '@sage/xtrem-master-data/Item',
    navigationPanel: {
        listItem: {
            intrastatAdditionalUnit: ui.nestedFieldExtensions.reference({
                bind: 'intrastatAdditionalUnit',
                insertBefore: 'weightUnit',
                isHiddenOnMainField: true,
            }),
        },
    },
})
export class ItemExtension extends ui.PageExtension<Item, GraphApi> {
    @ui.decorators.dropdownListFieldOverride<ItemExtension>({
        onChangeAfter() {
            if (this.type.value === 'service') {
                this.intrastatAdditionalUnit.value = null;
                this.intrastatAdditionalUnitToStockUnitConversion.value = 0;
            }
        },
    })
    type: ui.fields.DropdownList;

    @ui.decorators.referenceField<ItemExtension, UnitOfMeasure>({
        parent() {
            return this.customsUnitBlock;
        },
        title: 'Intrastat additional unit',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select intrastat additional unit',
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
            }),
            ui.nestedFields.text({
                bind: 'id',
                title: 'ID',
            }),
            ui.nestedFields.text({
                bind: 'type',
                isHidden: true,
                title: 'Type',
            }),
            ui.nestedFields.text({
                bind: 'decimalDigits',
                title: 'Decimal digits',
                isHidden: true,
            }),
        ],
        isHidden() {
            return this.type.value === 'service';
        },
        onChange() {
            if (this.intrastatAdditionalUnit.value?._id === this.stockUnit.value?._id) {
                this.intrastatAdditionalUnitToStockUnitConversion.value = 1;
            } else {
                this.intrastatAdditionalUnitToStockUnitConversion.value = 0;
            }
            this.intrastatAdditionalUnitToStockUnitConversion.isMandatory = !!this.intrastatAdditionalUnit.value;
            this.intrastatAdditionalUnitToStockUnitConversion.isDisabled =
                !this.intrastatAdditionalUnitToStockUnitConversion.isMandatory ||
                this.intrastatAdditionalUnit.value?._id === this.stockUnit.value?._id;
        },
    })
    intrastatAdditionalUnit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.numericField<ItemExtension>({
        parent() {
            return this.customsUnitBlock;
        },
        title: 'Stock unit conversion factor',
        isMandatory() {
            return !!this.intrastatAdditionalUnit.value;
        },
        isDisabled() {
            return this.intrastatAdditionalUnit.value ? false : true;
        },
        isHidden() {
            return this.type.value === 'service';
        },
        // to match with coefficientDataType on the backend side
        scale: 10,
    })
    intrastatAdditionalUnitToStockUnitConversion: ui.fields.Numeric;

    toggleItemPropertiesBasedOnTypeExtension(shouldDisable: boolean) {
        this.intrastatAdditionalUnit.isDisabled = shouldDisable;
        this.intrastatAdditionalUnit.isHidden = shouldDisable;
        this.intrastatAdditionalUnitToStockUnitConversion.isDisabled = shouldDisable;
        this.intrastatAdditionalUnitToStockUnitConversion.isHidden = shouldDisable;
    }
}

declare module '@sage/xtrem-master-data/build/lib/pages/item' {
    interface Item extends ItemExtension { }
}
