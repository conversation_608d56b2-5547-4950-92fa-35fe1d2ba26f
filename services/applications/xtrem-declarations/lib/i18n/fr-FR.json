{"@sage/xtrem-declarations/activity__intrastat_declaration__name": "Déclaration d'échange de biens", "@sage/xtrem-declarations/activity__movement_rule__name": "R<PERSON>gle de mouvement", "@sage/xtrem-declarations/activity__nature_of_transaction__name": "Nature de transaction", "@sage/xtrem-declarations/activity__statistical_procedure__name": "Procédure statistique", "@sage/xtrem-declarations/data_types__intrastat_declaration_period_enum__name": "Enum période déclaration DEB", "@sage/xtrem-declarations/data_types__intrastat_declaration_status_enum__name": "Enum statut déclaration DEB", "@sage/xtrem-declarations/data_types__intrastat_document_type_enum__name": "Enum type document DEB", "@sage/xtrem-declarations/data_types__intrastat_flow_enum__name": "Enum flux DEB", "@sage/xtrem-declarations/data_types__intrastat_mode_of_transport_enum__name": "Enum de mode de transport DEB", "@sage/xtrem-declarations/enums__intrastat_declaration_period__earliestMonth": "Mois le plus proche : par facture ou transaction +1", "@sage/xtrem-declarations/enums__intrastat_declaration_period__transactionMonth": "Mois de transaction", "@sage/xtrem-declarations/enums__intrastat_declaration_status__draft": "Brouillon", "@sage/xtrem-declarations/enums__intrastat_declaration_status__recorded": "Enregistrée", "@sage/xtrem-declarations/enums__intrastat_declaration_status__validated": "Valid<PERSON>", "@sage/xtrem-declarations/enums__intrastat_document_type__purchaseReceipt": "<PERSON><PERSON><PERSON> d'<PERSON>", "@sage/xtrem-declarations/enums__intrastat_document_type__purchaseReturn": "Retour d'achat", "@sage/xtrem-declarations/enums__intrastat_document_type__salesReturn": "<PERSON><PERSON> de vente", "@sage/xtrem-declarations/enums__intrastat_document_type__salesShipment": "Expédition de vente", "@sage/xtrem-declarations/enums__intrastat_flow__arrival": "Arrivée", "@sage/xtrem-declarations/enums__intrastat_flow__dispatch": "Expédition", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__airTransport": "Transport par air", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byAir": "Par air", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byInlandNavigation": "Inst. transport fixes", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byMail": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byRail": "Par chemin de fer", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byRoad": "Par route", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__bySea": "Maritime", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__fixedTransportInstallations": "Installations de transport fixes", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__inlandWaterwayTransport": "Transport par navigation intérieure", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__internalNavigation": "Par navigation intérieure", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__ownPropulsion": "Propulsion propre", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__postalConsignments": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__railwayTransport": "Transport par chemin de fer", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__roadTransport": "Transport par route", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__seaTransport": "Transport maritime", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__selfPropelled": "Propulsion propre", "@sage/xtrem-declarations/intrastat_declaration__date__not_end_of_month": "La fin de la période n'est pas la fin d'un mois.", "@sage/xtrem-declarations/menu_item__intrastat": "Déclaration d'échange de biens", "@sage/xtrem-declarations/node-extensions__delivery_mode_extension__property__intrastatModeOfTransport": "Mode de transport DEB", "@sage/xtrem-declarations/node-extensions__item_extension__intrastat_additional_unit_to_stock_unit_conversion_mandatory": "Renseignez le coefficient de conversion d'unité supplémentaire DEB.", "@sage/xtrem-declarations/node-extensions__item_extension__property__intrastatAdditionalUnit": "Unité supplémentaire DEB", "@sage/xtrem-declarations/node-extensions__item_extension__property__intrastatAdditionalUnitToStockUnitConversion": "Conversion unité supplémentaire Intrastat en unité de stock", "@sage/xtrem-declarations/node-extensions__purchase_receipt_line_extension__property__intrastatDeclarations": "Déclarations d'échanges de biens", "@sage/xtrem-declarations/node-extensions__purchase_return_line_extension__property__intrastatDeclarations": "Déclarations d'échanges de biens", "@sage/xtrem-declarations/node-extensions__sales_return_receipt_line_extension__property__intrastatDeclarations": "Déclarations d'échanges de biens", "@sage/xtrem-declarations/node-extensions__sales_shipment_line_extension__property__intrastatDeclarations": "Déclarations d'échanges de biens", "@sage/xtrem-declarations/nodes__intrastat_decalartioni__not-a-valid-tax-id": "Utilisez le format de numéro de TVA : {{format}}", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-declarations/nodes__intrastat_declaration__deletion_forbidden_validated": "La déclaration DEB courante ne peut pas être supprimée. Elle est déjà validée.", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines": "Extraire les lignes", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines__failed": "Échec ouverture du volet des lignes.", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines__parameter__intrastatDeclaration": "Déclaration d'échange de biens", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration": "Valider la déclaration", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration__failed": "Échec de validation de déclaration.", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration__parameter__intrastatDeclaration": "Déclaration d'échange de biens", "@sage/xtrem-declarations/nodes__intrastat_declaration__node_name": "Déclaration d'échange de biens", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__company": "Société", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__date": "Date", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__status": "Statut", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__commodity_code_format": "Utiliser le format de code marchandise : {{format}}", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__node_name": "Ligne de déclaration d'échange de biens", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__amount": "<PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__baseDocumentLine": "Ligne de document de base", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__beTaxIdNumber": "N° de TVA", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__businessEntity": "Entité commerciale", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__commodityCode": "Code marchandise", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__companyCurrency": "<PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedPurchaseCreditMemoLines": "Lignes d'avoirs d'achat calculées", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedPurchaseInvoiceLines": "Lignes de factures d'achat calculées", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedSalesCreditMemoLines": "Lignes d'avoirs de vente calculés", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedSalesInvoiceLines": "Lignes de factures de vente calculées", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__country": "Pays", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__countryOfOrigin": "Pays d'origine", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__currency": "<PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__documentDate": "Date document", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__documentType": "Type de document", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__flow": "Flux", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__geographicSubdivision": "Sous-division géographique", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__hasEmptyValue": "Valeurs vides", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__hasWarning": "Avertissement", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__intrastatAdditionalUnit": "Unité supplémentaire DEB", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__intrastatDeclaration": "Déclaration d'échange de biens", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__item": "Article", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__lineNumber": "Numéro de ligne", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__modeOfTransport": "Mode de transport", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__natureOfTransaction": "Nature de transaction", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__netPrice": "Prix net", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__netWeight": "Poids net", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseCreditMemoLines": "Lignes d'avoirs d'achat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseInvoiceLines": "Lignes de factures d'achat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseReceiptLine": "Ligne de réception d'achat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseReturnLine": "Ligne de retour d'achat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__quantity": "Quantité", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__quantityInIntrastatAdditionalUnit": "Quantité en unité supplémentaire DEB", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesCreditMemoLines": "Lignes d'avoirs de vente", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesPurchaseUnit": "<PERSON><PERSON> d'a<PERSON> ou de vente", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesReturnReceiptLine": "Ligne de réception de retour de vente", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesShipmentLine": "Ligne d'expédition de vente", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__statisticalProcedure": "Procédure statistique", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__toInvoiceLines": "Vers les lignes de factures", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__totalExcludingTax": "Total HT", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__totalExcludingTaxInCompanyCurrency": "Total HT devise société", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__node_name": "Ligne de déclaration d'échange de biens en ligne d'avoir d'achat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__property__intrastatDeclarationLine": "Ligne de déclaration d'échange de biens", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__property__purchaseCreditMemoLine": "Ligne d'avoir d'achat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__node_name": "Ligne de déclaration d'échange de biens en ligne de facture d'achat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__property__intrastatDeclarationLine": "Ligne de déclaration d'échange de biens", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__property__purchaseInvoiceLine": "Ligne de facture d'achat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__node_name": "Ligne de déclaration d'échange de biens en ligne d'avoir de vente", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__property__intrastatDeclarationLine": "Ligne de déclaration d'échange de biens", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__property__salesCreditMemoLine": "Ligne d'avoir de vente", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__node_name": "Ligne de déclaration d'échange de biens en ligne de facture de vente", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__property__intrastatDeclarationLine": "Ligne de déclaration d'échange de biens", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__property__salesInvoiceLine": "Ligne de facture de vente", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-declarations/nodes__movement_rule__node_name": "R<PERSON>gle de mouvement", "@sage/xtrem-declarations/nodes__movement_rule__property__declarationPeriod": "Période de déclaration", "@sage/xtrem-declarations/nodes__movement_rule__property__documentType": "Type de document", "@sage/xtrem-declarations/nodes__movement_rule__property__intrastatFlow": "Flux de Déclaration d'Échange de Biens", "@sage/xtrem-declarations/nodes__movement_rule__property__isActive": "Active", "@sage/xtrem-declarations/nodes__movement_rule__property__natureOfTransaction": "Nature de transaction", "@sage/xtrem-declarations/nodes__movement_rule__property__setupId": "Code paramétrage", "@sage/xtrem-declarations/nodes__movement_rule__property__statisticalProcedure": "Procédure statistique", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-declarations/nodes__nature_of_transaction__node_name": "Nature de transaction", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__id": "Code", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__isActive": "Active", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__name": "Nom", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-declarations/nodes__statistical_procedure__node_name": "Procédure statistique", "@sage/xtrem-declarations/nodes__statistical_procedure__property__id": "Code", "@sage/xtrem-declarations/nodes__statistical_procedure__property__intrastatFlow": "Flux de Déclaration d'Échange de Biens", "@sage/xtrem-declarations/nodes__statistical_procedure__property__isActive": "Active", "@sage/xtrem-declarations/nodes__statistical_procedure__property__name": "Nom", "@sage/xtrem-declarations/package__name": "Déclarations", "@sage/xtrem-declarations/page-extensions__delivery_mode_extension____navigationPanel__listItem__line3__title": "Mode de transport DEB", "@sage/xtrem-declarations/page-extensions__delivery_mode_extension__intrastatModeOfTransport____title": "Mode de transport DEB", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__decimalDigits": "Décimales", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__id": "Code", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__name": "Nom", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__type": "Type", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____lookupDialogTitle": "Sélectionner l'unité supplémentaire DEB", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____title": "Unité supplémentaire DEB", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnitToStockUnitConversion____title": "Coefficient conversion US", "@sage/xtrem-declarations/pages__confirm-cancel": "Annuler", "@sage/xtrem-declarations/pages__confirm-confirm": "Confirmer", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__line2__title": "Société", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__line2Right__title": "Période", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__titleRight__title": "Statut", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__optionsMenu__title": "Toutes", "@sage/xtrem-declarations/pages__intrastat_declaration____objectTypePlural": "Déclarations d'échanges de biens", "@sage/xtrem-declarations/pages__intrastat_declaration____objectTypeSingular": "Déclaration d'échange de biens", "@sage/xtrem-declarations/pages__intrastat_declaration____subtitle": "Déclaration d'échange de biens", "@sage/xtrem-declarations/pages__intrastat_declaration____title": "Déclaration d'échange de biens", "@sage/xtrem-declarations/pages__intrastat_declaration___id____title": "Code", "@sage/xtrem-declarations/pages__intrastat_declaration__addLine____title": "<PERSON><PERSON><PERSON> ligne", "@sage/xtrem-declarations/pages__intrastat_declaration__businessActions____title": "Actions métier", "@sage/xtrem-declarations/pages__intrastat_declaration__buttonBlock____title": "Actions métier", "@sage/xtrem-declarations/pages__intrastat_declaration__buttonGenerate____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__buttonValidate____title": "Valider", "@sage/xtrem-declarations/pages__intrastat_declaration__company____placeholder": "Sélectionner la société", "@sage/xtrem-declarations/pages__intrastat_declaration__company____title": "Société", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_deletion_dialog_text": "Vous êtes sur le point de supprimer cette déclaration avec toutes ses lignes.", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_deletion_dialog_title": "Confirmer la <PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_regeneration_dialog_text": "Vous êtes sur le point de réinitialiser les lignes de la déclaration avec tous les changements manuels.", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_regeneration_dialog_title": "Confirmer la regénération", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_validation_dialog_text": "Vous êtes sur le point de valider la déclaration. Vous ne pourrez plus la modifier ou la supprimer.", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_validation_dialog_title": "Confirmer la validation", "@sage/xtrem-declarations/pages__intrastat_declaration__date____title": "Période", "@sage/xtrem-declarations/pages__intrastat_declaration__deleteDeclaration____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentNumber____title": "Numéro de document", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsBlock____title": "Documents", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__amountExcludingTax": "Total HT devise société", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__documentDate": "Date d'avoir", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__documentNumber": "Avoir", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__lineAmountExcludingTax": "Total HT devise société", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__quantity": "Quantité avoir", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____title": "Lignes d'avoirs d'achat", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__amountExcludingTax": "Total HT devise société", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__documentDate": "Date facture", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__documentNumber": "Facture", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__lineAmountExcludingTax": "Total HT devise société", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__quantity": "Quantité facture", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____title": "Lignes de factures d'achat", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__amountExcludingTax": "Total HT devise société", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__companyCurrency__id": "<PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__documentDate": "Date d'avoir", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__documentNumber": "Avoir", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__lineAmountExcludingTax": "Total HT devise société", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__quantity": "Quantité avoir", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____title": "Lignes d'avoirs de vente", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__amountExcludingTax": "Total HT devise société", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__documentDate": "Date facture", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__documentNumber": "Facture", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__lineAmountExcludingTax": "Total HT devise société", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__quantity": "Quantité facture", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____title": "Lignes de factures de vente", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSection____title": "Documents", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelGeneralSection____title": "Général", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderbusinessEntityName____content": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderDate____content": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderSection____title": "Détails DEB", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelItem____title": "Article", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelQuantityAndNetPrice____title": "Quantité et prix net", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelTotalsExcludingTax____title": "Totaux HT", "@sage/xtrem-declarations/pages__intrastat_declaration__filterBlock____title": "Critères de filtres de données", "@sage/xtrem-declarations/pages__intrastat_declaration__generate_generate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__generate_regenerate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__headerBlock____title": "Critères d'extraction", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__businessEntity__name__title": "ISO 3166-1 alpha-2", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__name__title": "Nom", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__name__title__2": "Symbole", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__name__title__3": "Décimales", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__title": "Symbole", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__title__2": "Décimales", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__purchaseReceiptLine___id__title": "<PERSON><PERSON><PERSON> a<PERSON>t", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__purchaseReturnLine___id__title": "Retour achat", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__purchaseReturnLine___id__title__2": "Retour achat", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesPurchaseUnit__name__title": "Symbole", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesPurchaseUnit__name__title__2": "Décimales", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnReceiptLine___id__columns__title__number": "Numéro document", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnReceiptLine___id__title": "<PERSON>é<PERSON> de retour de vente", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnReceiptLine___id__title__2": "<PERSON>é<PERSON> de retour de vente", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnRequestLine___id__title": "Code", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnRequestLine___id__title__2": "ID document", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnRequestLine___id__title__3": "N° document", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesShipmentLine___id__columns__title__number": "Numéro document", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesShipmentLine___id__title": "Expédition vente", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesShipmentLine___id__title__2": "Expédition vente", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__statisticalRule__name__title": "Code", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__statisticalRule__name__title__2": "Nom", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__statisticalRule__name__title__3": "Code", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__amount": "<PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__beTaxIdNumber": "N° TVA client", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__businessEntity__name": "Entité commerciale", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__commodityCode": "Code marchandise", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__commodityCodeEU": "Code marchandise", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__companyCurrency__id": "<PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__country": "Pays de réception / d'émission", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__countryOfOrigin": "Pays d'origine", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__currency__id": "<PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__documentDate": "Date document", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__documentType": "Type document", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__flow": "Flux", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__geographicSubdivision": "Département", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__hasEmptyValue": "Valeurs vides", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__hasWarning": "Avertissement", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__intrastatAdditionalUnit": "Unité supplémentaire", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__intrastatAdditionalUnit__name": "Unité supplémentaire", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__item__name": "Article", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__lineNumber": "Ligne", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__linkToDocument": "Numéro document", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__modeOfTransport": "Mode de transport", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__natureOfTransaction__name": "Nature de transaction", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__netPrice": "Prix net", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__netWeight": "Poids (kg)", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__purchaseReceiptLine___id": "Ligne récept. achat", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__purchaseReturnLine___id": "Retour achat", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__quantity": "Quantité", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__quantityInIntrastatAdditionalUnit": "Quantité en unité supplémentaire DEB", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesPurchaseUnit__name": "Unité", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesReturnReceiptLine___id": "<PERSON>é<PERSON> de retour de vente", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesReturnRequestLine___id": "De<PERSON>e retour vente", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesShipmentLine___id": "Expédition vente", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__statisticalProcedure__name": "Procédure statistique", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__statisticalRule__name": "Régime statistique", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__totalExcludingTax": "Total HT", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__totalExcludingTaxInCompanyCurrency": "Total HT devise société", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____title": "Résultats", "@sage/xtrem-declarations/pages__intrastat_declaration__mainSection____title": "Général", "@sage/xtrem-declarations/pages__intrastat_declaration__maxAmount____title": "Montant maximum", "@sage/xtrem-declarations/pages__intrastat_declaration__minAmount____title": "Montant minimum", "@sage/xtrem-declarations/pages__intrastat_declaration__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__numberNew____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__status____title": "Statut", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__listItem__title__title": "Type de document", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__optionsMenu__title": "Toutes", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-declarations/pages__movement_rule____objectTypePlural": "Règles de mouvement", "@sage/xtrem-declarations/pages__movement_rule____objectTypeSingular": "R<PERSON>gle de mouvement", "@sage/xtrem-declarations/pages__movement_rule____title": "R<PERSON>gle de mouvement", "@sage/xtrem-declarations/pages__movement_rule__declarationPeriod____title": "Période de déclaration", "@sage/xtrem-declarations/pages__movement_rule__documentType____title": "Type de document", "@sage/xtrem-declarations/pages__movement_rule__generalSection____title": "Général", "@sage/xtrem-declarations/pages__movement_rule__intrastatFlow____title": "Flux", "@sage/xtrem-declarations/pages__movement_rule__isActive____title": "Active", "@sage/xtrem-declarations/pages__movement_rule__natureOfTransaction____title": "Nature de transaction", "@sage/xtrem-declarations/pages__movement_rule__statisticalProcedure____title": "Procédure statistique", "@sage/xtrem-declarations/pages__movement_rule_definition____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-declarations/pages__movement_rule_definition____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-declarations/pages__movement_rule_definition____subtitle": "R<PERSON>gle de mouvement", "@sage/xtrem-declarations/pages__movement_rule_definition____title": "R<PERSON>gle de mouvement", "@sage/xtrem-declarations/pages__movement_rule_definition__declarationPeriod____title": "Période de déclaration", "@sage/xtrem-declarations/pages__movement_rule_definition__documentType____title": "Type de document", "@sage/xtrem-declarations/pages__movement_rule_definition__generalSection____title": "Général", "@sage/xtrem-declarations/pages__movement_rule_definition__intrastatFlow____title": "Flux", "@sage/xtrem-declarations/pages__movement_rule_definition__isActive____title": "Active", "@sage/xtrem-declarations/pages__movement_rule_definition__natureOfTransaction____columns__title__id": "Code", "@sage/xtrem-declarations/pages__movement_rule_definition__natureOfTransaction____columns__title__name": "Nom", "@sage/xtrem-declarations/pages__movement_rule_definition__natureOfTransaction____title": "Nature de transaction", "@sage/xtrem-declarations/pages__movement_rule_definition__statisticalRule____columns__title__id": "Code", "@sage/xtrem-declarations/pages__movement_rule_definition__statisticalRule____columns__title__intrastatFlow": "Flux", "@sage/xtrem-declarations/pages__movement_rule_definition__statisticalRule____columns__title__name": "Nom", "@sage/xtrem-declarations/pages__movement_rule_definition__statisticalRule____title": "Régime statistique", "@sage/xtrem-declarations/pages__nature_of_transaction____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-declarations/pages__nature_of_transaction____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-declarations/pages__nature_of_transaction____objectTypePlural": "Natures de transaction", "@sage/xtrem-declarations/pages__nature_of_transaction____objectTypeSingular": "Nature de transaction", "@sage/xtrem-declarations/pages__nature_of_transaction____title": "Nature de transaction", "@sage/xtrem-declarations/pages__nature_of_transaction___id____title": "Code", "@sage/xtrem-declarations/pages__nature_of_transaction__generalSection____title": "Général", "@sage/xtrem-declarations/pages__nature_of_transaction__id____title": "Code", "@sage/xtrem-declarations/pages__nature_of_transaction__isActive____title": "Active", "@sage/xtrem-declarations/pages__nature_of_transaction__name____title": "Nom", "@sage/xtrem-declarations/pages__statistical_procedure____navigationPanel__optionsMenu__title": "Toutes", "@sage/xtrem-declarations/pages__statistical_procedure____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-declarations/pages__statistical_procedure____objectTypePlural": "Procédures statistique", "@sage/xtrem-declarations/pages__statistical_procedure____objectTypeSingular": "Procédure statistique", "@sage/xtrem-declarations/pages__statistical_procedure____title": "Procédure statistique", "@sage/xtrem-declarations/pages__statistical_procedure___id____title": "Code", "@sage/xtrem-declarations/pages__statistical_procedure__generalSection____title": "Général", "@sage/xtrem-declarations/pages__statistical_procedure__id____title": "Code", "@sage/xtrem-declarations/pages__statistical_procedure__intrastatFlow____title": "Flux", "@sage/xtrem-declarations/pages__statistical_procedure__isActive____title": "Active", "@sage/xtrem-declarations/pages__statistical_procedure__name____title": "Nom", "@sage/xtrem-declarations/pages__statistical_rule____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-declarations/pages__statistical_rule____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-declarations/pages__statistical_rule____subtitle": "Régime statistique", "@sage/xtrem-declarations/pages__statistical_rule____title": "Régime statistique", "@sage/xtrem-declarations/pages__statistical_rule___id____title": "Nom", "@sage/xtrem-declarations/pages__statistical_rule__generalSection____title": "Général", "@sage/xtrem-declarations/pages__statistical_rule__id____title": "Code", "@sage/xtrem-declarations/pages__statistical_rule__intrastatFlow____title": "Flux", "@sage/xtrem-declarations/pages__statistical_rule__isActive____title": "Actif", "@sage/xtrem-declarations/pages__statistical_rule__name____title": "Nom", "@sage/xtrem-declarations/permission__manage__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/permission__read__name": "<PERSON><PERSON>"}