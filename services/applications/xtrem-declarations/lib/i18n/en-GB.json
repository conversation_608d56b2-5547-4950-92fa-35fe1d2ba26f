{"@sage/xtrem-declarations/activity__intrastat_declaration__name": "Intrastat declaration", "@sage/xtrem-declarations/activity__movement_rule__name": "Movement rule", "@sage/xtrem-declarations/activity__nature_of_transaction__name": "Nature of transaction", "@sage/xtrem-declarations/activity__statistical_procedure__name": "Statistical procedure", "@sage/xtrem-declarations/data_types__intrastat_declaration_period_enum__name": "Intrastat declaration period enum", "@sage/xtrem-declarations/data_types__intrastat_declaration_status_enum__name": "Intrastat declaration status enum", "@sage/xtrem-declarations/data_types__intrastat_document_type_enum__name": "Intrastat document type enum", "@sage/xtrem-declarations/data_types__intrastat_flow_enum__name": "Intrastat flow enum", "@sage/xtrem-declarations/data_types__intrastat_mode_of_transport_enum__name": "Intrastat mode of transport enum", "@sage/xtrem-declarations/enums__intrastat_declaration_period__earliestMonth": "Earliest month: by invoice or transaction +1", "@sage/xtrem-declarations/enums__intrastat_declaration_period__transactionMonth": "Transaction month", "@sage/xtrem-declarations/enums__intrastat_declaration_status__draft": "Draft", "@sage/xtrem-declarations/enums__intrastat_declaration_status__recorded": "Recorded", "@sage/xtrem-declarations/enums__intrastat_declaration_status__validated": "Validated", "@sage/xtrem-declarations/enums__intrastat_document_type__purchaseReceipt": "Purchase receipt", "@sage/xtrem-declarations/enums__intrastat_document_type__purchaseReturn": "Purchase return", "@sage/xtrem-declarations/enums__intrastat_document_type__salesReturn": "Sales return", "@sage/xtrem-declarations/enums__intrastat_document_type__salesShipment": "Sales shipment", "@sage/xtrem-declarations/enums__intrastat_flow__arrival": "Arrival", "@sage/xtrem-declarations/enums__intrastat_flow__dispatch": "Dispatch", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__airTransport": "Air transport", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__fixedTransportInstallations": "Fixed transport installations", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__inlandWaterwayTransport": "Inland waterway transport", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__ownPropulsion": "Own propulsion", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__postalConsignments": "Postal consignments", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__railwayTransport": "Railway transport", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__roadTransport": "Road transport", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__seaTransport": "Sea transport", "@sage/xtrem-declarations/intrastat_declaration__date__not_end_of_month": "The end of the period is not the end of a month.", "@sage/xtrem-declarations/menu_item__intrastat": "Intrastat", "@sage/xtrem-declarations/node-extensions__delivery_mode_extension__property__intrastatModeOfTransport": "Intrastat mode of transport", "@sage/xtrem-declarations/node-extensions__item_extension__intrastat_additional_unit_to_stock_unit_conversion_mandatory": "Enter the Intrastat additional unit conversion factor.", "@sage/xtrem-declarations/node-extensions__item_extension__property__intrastatAdditionalUnit": "Intrastat additional unit", "@sage/xtrem-declarations/node-extensions__item_extension__property__intrastatAdditionalUnitToStockUnitConversion": "Intrastat additional unit to stock unit conversion", "@sage/xtrem-declarations/node-extensions__purchase_receipt_line_extension__property__intrastatDeclarations": "Intrastat declarations", "@sage/xtrem-declarations/node-extensions__purchase_return_line_extension__property__intrastatDeclarations": "Intrastat declarations", "@sage/xtrem-declarations/node-extensions__sales_return_receipt_line_extension__property__intrastatDeclarations": "Intrastat declarations", "@sage/xtrem-declarations/node-extensions__sales_shipment_line_extension__property__intrastatDeclarations": "Intrastat declarations", "@sage/xtrem-declarations/nodes__intrastat_decalartioni__not-a-valid-tax-id": "Use the tax ID format: {{format}}", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__intrastat_declaration__deletion_forbidden_validated": "The current Intrastat declaration cannot be deleted. It is already validated.", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines": "Extract lines", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines__failed": "Extract lines failed.", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines__parameter__intrastatDeclaration": "Intrastat declaration", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration": "Validate declaration", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration__failed": "Validate declaration failed.", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration__parameter__intrastatDeclaration": "Intrastat declaration", "@sage/xtrem-declarations/nodes__intrastat_declaration__node_name": "Intrastat declaration", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__company": "Company", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__date": "Date", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__lines": "Lines", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__number": "Number", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__status": "Status", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__commodity_code_format": "Use the commodity code format: {{format}}", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__node_name": "Intrastat declaration line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__amount": "Amount", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__baseDocumentLine": "Base document line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__beTaxIdNumber": "Be Tax ID number", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__businessEntity": "Business entity", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__commodityCode": "Commodity code", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__companyCurrency": "Company currency", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedPurchaseCreditMemoLines": "Computed purchase credit note lines", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedPurchaseInvoiceLines": "Computed purchase invoice lines", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedSalesCreditMemoLines": "Computed sales credit note lines", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedSalesInvoiceLines": "Computed sales invoice lines", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__country": "Country", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__countryOfOrigin": "Country of origin", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__documentDate": "Document date", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__documentType": "Document type", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__flow": "Flow", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__geographicSubdivision": "Geographic subdivision", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__hasEmptyValue": "Has empty values", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__hasWarning": "Has warning", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__intrastatAdditionalUnit": "Intrastat additional unit", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__intrastatDeclaration": "Intrastat declaration", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__lineNumber": "Line number", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__modeOfTransport": "Mode of transport", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__natureOfTransaction": "Nature of transaction", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__netPrice": "Net price", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__netWeight": "Net weight", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseCreditMemoLines": "Purchase credit note lines", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseInvoiceLines": "Purchase invoice lines", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseReceiptLine": "Purchase receipt line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseReturnLine": "Purchase return line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__quantity": "Quantity", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__quantityInIntrastatAdditionalUnit": "Quantity in Intrastat additional unit", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesCreditMemoLines": "Sales credit note lines", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesPurchaseUnit": "Sales or purchase unit", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesReturnReceiptLine": "Sales return receipt line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesShipmentLine": "Sales shipment Line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__statisticalProcedure": "Statistical procedure", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__toInvoiceLines": "To invoice lines", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__totalExcludingTax": "Total excluding VAT", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__totalExcludingTaxInCompanyCurrency": "Total excluding VAT in company currency", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__node_name": "Intrastat declaration line to purchase credit note line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__property__intrastatDeclarationLine": "Intrastat declaration line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__property__purchaseCreditMemoLine": "Purchase credit note line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__node_name": "Intrastat declaration line to purchase invoice line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__property__intrastatDeclarationLine": "Intrastat declaration line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__property__purchaseInvoiceLine": "Purchase invoice line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__node_name": "Intrastat declaration line to sales credit note line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__property__intrastatDeclarationLine": "Intrastat declaration line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__property__salesCreditMemoLine": "Sales credit note line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__node_name": "Intrastat declaration line to sales invoice line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__property__intrastatDeclarationLine": "Intrastat declaration line", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__property__salesInvoiceLine": "Sales invoice line", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__movement_rule__node_name": "Movement rule", "@sage/xtrem-declarations/nodes__movement_rule__property__declarationPeriod": "Declaration period", "@sage/xtrem-declarations/nodes__movement_rule__property__documentType": "Document type", "@sage/xtrem-declarations/nodes__movement_rule__property__intrastatFlow": "Intrastat flow", "@sage/xtrem-declarations/nodes__movement_rule__property__isActive": "Active", "@sage/xtrem-declarations/nodes__movement_rule__property__natureOfTransaction": "Nature of transaction", "@sage/xtrem-declarations/nodes__movement_rule__property__setupId": "Setup ID", "@sage/xtrem-declarations/nodes__movement_rule__property__statisticalProcedure": "Statistical procedure", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__nature_of_transaction__node_name": "Nature of transaction", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__id": "ID", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__isActive": "Active", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__name": "Name", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__statistical_procedure__node_name": "Statistical procedure", "@sage/xtrem-declarations/nodes__statistical_procedure__property__id": "ID", "@sage/xtrem-declarations/nodes__statistical_procedure__property__intrastatFlow": "Intrastat flow", "@sage/xtrem-declarations/nodes__statistical_procedure__property__isActive": "Active", "@sage/xtrem-declarations/nodes__statistical_procedure__property__name": "Name", "@sage/xtrem-declarations/package__name": "Declarations", "@sage/xtrem-declarations/page-extensions__delivery_mode_extension____navigationPanel__listItem__line3__title": "Intrastat transport mode", "@sage/xtrem-declarations/page-extensions__delivery_mode_extension__intrastatModeOfTransport____title": "Intrastat transport mode", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__decimalDigits": "Decimal places", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__id": "ID", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__name": "Name", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__type": "Type", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____lookupDialogTitle": "Select Intrastat additional unit", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____title": "Intrastat additional unit", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnitToStockUnitConversion____title": "Stock unit conversion factor", "@sage/xtrem-declarations/pages__confirm-cancel": "Cancel", "@sage/xtrem-declarations/pages__confirm-confirm": "Confirm", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__line2__title": "Company", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__line2Right__title": "Period", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-declarations/pages__intrastat_declaration____objectTypePlural": "Intrastat declarations", "@sage/xtrem-declarations/pages__intrastat_declaration____objectTypeSingular": "Intrastat declaration", "@sage/xtrem-declarations/pages__intrastat_declaration____subtitle": "Intrastat declaration", "@sage/xtrem-declarations/pages__intrastat_declaration____title": "Intrastat declaration", "@sage/xtrem-declarations/pages__intrastat_declaration___id____title": "ID", "@sage/xtrem-declarations/pages__intrastat_declaration__addLine____title": "Add line", "@sage/xtrem-declarations/pages__intrastat_declaration__businessActions____title": "Business actions", "@sage/xtrem-declarations/pages__intrastat_declaration__buttonGenerate____title": "Generate", "@sage/xtrem-declarations/pages__intrastat_declaration__buttonValidate____title": "Validate", "@sage/xtrem-declarations/pages__intrastat_declaration__company____placeholder": "Select company", "@sage/xtrem-declarations/pages__intrastat_declaration__company____title": "Company", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_deletion_dialog_text": "You are about to delete this declaration including all lines.", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_deletion_dialog_title": "Confirm deletion", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_regeneration_dialog_text": "You are about to reset the declaration lines including all manual changes.", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_regeneration_dialog_title": "Confirm regeneration", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_validation_dialog_text": "You are about to validate the declaration. You will no longer be able to edit or delete it.", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_validation_dialog_title": "Confirm validation", "@sage/xtrem-declarations/pages__intrastat_declaration__date____title": "Period", "@sage/xtrem-declarations/pages__intrastat_declaration__deleteDeclaration____title": "Delete", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentNumber____title": "Document number", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsBlock____title": "Documents", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__amountExcludingTax": "Total excluding VAT in company currency", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__documentDate": "Credit note date", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__documentNumber": "Credit note", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__lineAmountExcludingTax": "Total excluding VAT in company currency", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__quantity": "Credit note quantity", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____title": "Purchase credit note lines", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__amountExcludingTax": "Total excluding VAT in company currency", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__documentDate": "Invoice date", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__documentNumber": "Invoice", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__lineAmountExcludingTax": "Total excluding VAT in company currency", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__quantity": "Invoice quantity", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____title": "Purchase invoice lines", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__amountExcludingTax": "Total excluding VAT in company currency", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__companyCurrency__id": "Company currency", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__documentDate": "Credit note date", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__documentNumber": "Credit note", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__lineAmountExcludingTax": "Total excluding VAT in company currency", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__quantity": "Credit note quantity", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____title": "Sales credit note lines", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__amountExcludingTax": "Total excluding VAT in company currency", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__documentDate": "Invoice date", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__documentNumber": "Invoice", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__lineAmountExcludingTax": "Total excluding VAT in company currency", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__quantity": "Invoice quantity", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____title": "Sales invoice lines", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSection____title": "Documents", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelGeneralSection____title": "General", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderbusinessEntityName____content": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderDate____content": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderSection____title": "Intrastat details", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelItem____title": "<PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelQuantityAndNetPrice____title": "Quantity and net price", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelTotalsExcludingTax____title": "Totals excluding VAT", "@sage/xtrem-declarations/pages__intrastat_declaration__filterBlock____title": "Data filter criteria", "@sage/xtrem-declarations/pages__intrastat_declaration__generate_generate": "Generate", "@sage/xtrem-declarations/pages__intrastat_declaration__generate_regenerate": "Regenerate", "@sage/xtrem-declarations/pages__intrastat_declaration__headerBlock____title": "Extraction criteria", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__businessEntity__name__title": "ISO 3166-1 alpha-2", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__name__title": "Name", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__name__title__2": "Symbol", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__name__title__3": "Decimal places", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__title": "Symbol", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__title__2": "Decimal places", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__purchaseReceiptLine___id__title": "Purchase receipt", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__purchaseReturnLine___id__title": "Purchase return", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__purchaseReturnLine___id__title__2": "Purchase return", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesPurchaseUnit__name__title": "Symbol", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesPurchaseUnit__name__title__2": "Decimal places", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnReceiptLine___id__columns__title__number": "Document number", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnReceiptLine___id__title": "Sales return receipt", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnReceiptLine___id__title__2": "Sales return receipt", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnRequestLine___id__title": "ID", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnRequestLine___id__title__2": "Document ID", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnRequestLine___id__title__3": "Document number", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesShipmentLine___id__columns__title__number": "Document number", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesShipmentLine___id__title": "Sales shipment", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesShipmentLine___id__title__2": "Sales shipment", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__amount": "Value", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__beTaxIdNumber": "Customer tax ID", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__businessEntity__name": "Business entity", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__commodityCode": "Commodity code", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__commodityCodeEU": "Commodity code", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__companyCurrency__id": "Company currency", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__country": "Receiving/Issuing country", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__countryOfOrigin": "Country of origin", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__documentDate": "Document date", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__documentType": "Document type", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__flow": "Flow", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__geographicSubdivision": "Department", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__hasEmptyValue": "Has empty values", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__hasWarning": "Has warning", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__intrastatAdditionalUnit": "Additional unit", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__intrastatAdditionalUnit__name": "Additional unit", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__lineNumber": "Line", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__linkToDocument": "Document number", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__modeOfTransport": "Mode of transport", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__natureOfTransaction__name": "Nature of transaction", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__netPrice": "Net price", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__netWeight": "Weight (kg)", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__purchaseReceiptLine___id": "Purchase receipt line", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__purchaseReturnLine___id": "Purchase return", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__quantity": "Quantity", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__quantityInIntrastatAdditionalUnit": "Quantity in Intrastat additional unit", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesPurchaseUnit__name": "Unit", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesReturnReceiptLine___id": "Sales return receipt", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesReturnRequestLine___id": "Sales return request", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesShipmentLine___id": "Sales shipment", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__statisticalProcedure__name": "Statistical procedure", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__totalExcludingTax": "Total excluding VAT", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__totalExcludingTaxInCompanyCurrency": "Total excluding VAT in company currency", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____dropdownActions__title": "Delete", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____title": "Results", "@sage/xtrem-declarations/pages__intrastat_declaration__mainSection____title": "General", "@sage/xtrem-declarations/pages__intrastat_declaration__maxAmount____title": "Maximum amount", "@sage/xtrem-declarations/pages__intrastat_declaration__minAmount____title": "Minimum amount", "@sage/xtrem-declarations/pages__intrastat_declaration__number____title": "Number", "@sage/xtrem-declarations/pages__intrastat_declaration__numberNew____title": "Number", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__listItem__title__title": "Document type", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-declarations/pages__movement_rule____objectTypePlural": "Movement rules", "@sage/xtrem-declarations/pages__movement_rule____objectTypeSingular": "Movement rule", "@sage/xtrem-declarations/pages__movement_rule____title": "Movement rule", "@sage/xtrem-declarations/pages__movement_rule__declarationPeriod____title": "Declaration period", "@sage/xtrem-declarations/pages__movement_rule__documentType____title": "Document type", "@sage/xtrem-declarations/pages__movement_rule__generalSection____title": "General", "@sage/xtrem-declarations/pages__movement_rule__intrastatFlow____title": "Flow", "@sage/xtrem-declarations/pages__movement_rule__isActive____title": "Active", "@sage/xtrem-declarations/pages__movement_rule__natureOfTransaction____title": "Nature of transaction", "@sage/xtrem-declarations/pages__movement_rule__statisticalProcedure____title": "Statistical procedure", "@sage/xtrem-declarations/pages__nature_of_transaction____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-declarations/pages__nature_of_transaction____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-declarations/pages__nature_of_transaction____objectTypePlural": "Nature of transaction", "@sage/xtrem-declarations/pages__nature_of_transaction____objectTypeSingular": "Nature of transaction", "@sage/xtrem-declarations/pages__nature_of_transaction____title": "Nature of transaction", "@sage/xtrem-declarations/pages__nature_of_transaction___id____title": "ID", "@sage/xtrem-declarations/pages__nature_of_transaction__generalSection____title": "General", "@sage/xtrem-declarations/pages__nature_of_transaction__id____title": "ID", "@sage/xtrem-declarations/pages__nature_of_transaction__isActive____title": "Active", "@sage/xtrem-declarations/pages__nature_of_transaction__name____title": "Name", "@sage/xtrem-declarations/pages__statistical_procedure____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-declarations/pages__statistical_procedure____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-declarations/pages__statistical_procedure____objectTypePlural": "Statistical procedures", "@sage/xtrem-declarations/pages__statistical_procedure____objectTypeSingular": "Statistical procedure", "@sage/xtrem-declarations/pages__statistical_procedure____title": "Statistical procedure", "@sage/xtrem-declarations/pages__statistical_procedure___id____title": "ID", "@sage/xtrem-declarations/pages__statistical_procedure__generalSection____title": "General", "@sage/xtrem-declarations/pages__statistical_procedure__id____title": "ID", "@sage/xtrem-declarations/pages__statistical_procedure__intrastatFlow____title": "Flow", "@sage/xtrem-declarations/pages__statistical_procedure__isActive____title": "Active", "@sage/xtrem-declarations/pages__statistical_procedure__name____title": "Name", "@sage/xtrem-declarations/permission__manage__name": "Manage", "@sage/xtrem-declarations/permission__read__name": "Read"}