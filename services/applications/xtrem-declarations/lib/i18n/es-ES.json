{"@sage/xtrem-declarations/activity__intrastat_declaration__name": "Declaración Intrastat", "@sage/xtrem-declarations/activity__movement_rule__name": "Régimen de transacción", "@sage/xtrem-declarations/activity__nature_of_transaction__name": "Naturaleza de transacción", "@sage/xtrem-declarations/activity__statistical_procedure__name": "Régimen estadístico", "@sage/xtrem-declarations/data_types__intrastat_declaration_period_enum__name": "Intrastat declaration period enum", "@sage/xtrem-declarations/data_types__intrastat_declaration_status_enum__name": "Intrastat declaration status enum", "@sage/xtrem-declarations/data_types__intrastat_document_type_enum__name": "Intrastat document type enum", "@sage/xtrem-declarations/data_types__intrastat_flow_enum__name": "Intrastat flow enum", "@sage/xtrem-declarations/data_types__intrastat_mode_of_transport_enum__name": "Modo de transporte de Intrastat", "@sage/xtrem-declarations/enums__intrastat_declaration_period__earliestMonth": "Primer mes (por factura o transacción) + 1", "@sage/xtrem-declarations/enums__intrastat_declaration_period__transactionMonth": "Mes de transacción", "@sage/xtrem-declarations/enums__intrastat_declaration_status__draft": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/enums__intrastat_declaration_status__recorded": "Registrada", "@sage/xtrem-declarations/enums__intrastat_declaration_status__validated": "Validada", "@sage/xtrem-declarations/enums__intrastat_document_type__purchaseReceipt": "Recepción de compra", "@sage/xtrem-declarations/enums__intrastat_document_type__purchaseReturn": "Devolución de compra", "@sage/xtrem-declarations/enums__intrastat_document_type__salesReturn": "Devolución de venta", "@sage/xtrem-declarations/enums__intrastat_document_type__salesShipment": "Expedición de venta", "@sage/xtrem-declarations/enums__intrastat_flow__arrival": "Llegada", "@sage/xtrem-declarations/enums__intrastat_flow__dispatch": "Expedición", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__airTransport": "Aéreo", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byAir": "Aéreo", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byInlandNavigation": "Instalaciones fijas y de transporte", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byMail": "Envíos postales", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byRail": "Por ferrocarril", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byRoad": "<PERSON>r carretera", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__bySea": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__fixedTransportInstallations": "Instalaciones fijas", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__inlandWaterwayTransport": "Navegación interior", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__internalNavigation": "Navegación interior", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__ownPropulsion": "Autopropulsión", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__postalConsignments": "Envíos postales", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__railwayTransport": "Por ferrocarril", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__roadTransport": "<PERSON>r carretera", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__seaTransport": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__selfPropelled": "Autopropulsión", "@sage/xtrem-declarations/intrastat_declaration__date__not_end_of_month": "El fin del periodo no coincide con el fin del mes.", "@sage/xtrem-declarations/menu_item__intrastat": "Intrastat", "@sage/xtrem-declarations/node-extensions__delivery_mode_extension__property__intrastatModeOfTransport": "Modo de transporte de Intrastat", "@sage/xtrem-declarations/node-extensions__item_extension__intrastat_additional_unit_to_stock_unit_conversion_mandatory": "Introduce el coeficiente de conversión de unidad complementaria de Intrastat.", "@sage/xtrem-declarations/node-extensions__item_extension__property__intrastatAdditionalUnit": "Unidad complementaria de Intrastat", "@sage/xtrem-declarations/node-extensions__item_extension__property__intrastatAdditionalUnitToStockUnitConversion": "Conversión de unidad complementaria de Intrastat a stock", "@sage/xtrem-declarations/node-extensions__purchase_receipt_line_extension__property__intrastatDeclarations": "Declaraciones Intrastat", "@sage/xtrem-declarations/node-extensions__purchase_return_line_extension__property__intrastatDeclarations": "Declaraciones Intrastat", "@sage/xtrem-declarations/node-extensions__sales_return_receipt_line_extension__property__intrastatDeclarations": "Declaraciones Intrastat", "@sage/xtrem-declarations/node-extensions__sales_shipment_line_extension__property__intrastatDeclarations": "Declaraciones Intrastat", "@sage/xtrem-declarations/nodes__intrastat_decalartioni__not-a-valid-tax-id": "Utiliza el siguiente formato para el NIF-IVA: {{format}}", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-declarations/nodes__intrastat_declaration__deletion_forbidden_validated": "Esta declaración Intrastat no se puede eliminar porque ya se ha validado.", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines": "<PERSON>er líneas", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines__failed": "Error al extraer las líneas", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines__parameter__intrastatDeclaration": "Declaración Intrastat", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration": "Validar declaración", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration__failed": "Error al validar la declaración", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration__parameter__intrastatDeclaration": "Declaración Intrastat", "@sage/xtrem-declarations/nodes__intrastat_declaration__node_name": "Declaración Intrastat", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__company": "Sociedad", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__date": "<PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__lines": "Líneas", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__number": "Número", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__status": "Estado", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__commodity_code_format": "Utiliza el formato {{format}} para el código de mercancías.", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__node_name": "Línea de declaración Intrastat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__amount": "Importe", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__baseDocumentLine": "Línea de documento base", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__beTaxIdNumber": "Be Tax ID number", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__businessEntity": "Entidad empresarial", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__commodityCode": "Código de mercancías", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__companyCurrency": "Divisa de sociedad", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedPurchaseCreditMemoLines": "Líneas de factura rectificativa de compra calculadas", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedPurchaseInvoiceLines": "Líneas de factura de compra calculadas", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedSalesCreditMemoLines": "Líneas de factura rectificativa de venta calculadas", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedSalesInvoiceLines": "Líneas de factura de venta calculadas", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__country": "<PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__countryOfOrigin": "<PERSON><PERSON> de origen", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__currency": "Divisa", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__documentDate": "Fecha de documento", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__documentType": "Tipo de documento", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__flow": "F<PERSON>jo", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__geographicSubdivision": "Subdivisión geográfica", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__hasEmptyValue": "Valores vacíos", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__hasWarning": "Aviso", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__intrastatAdditionalUnit": "Unidad complementaria de Intrastat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__intrastatDeclaration": "Declaración Intrastat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__lineNumber": "Número de línea", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__modeOfTransport": "Modo de transporte", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__natureOfTransaction": "Naturaleza de transacción", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__netPrice": "<PERSON><PERSON> neto", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__netWeight": "Peso neto", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseCreditMemoLines": "Líneas de factura rectificativa de compra", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseInvoiceLines": "Líneas de factura de compra", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseReceiptLine": "Línea de recepción de compra", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseReturnLine": "Línea de devolución de compra", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__quantity": "Cantidad", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__quantityInIntrastatAdditionalUnit": "Cantidad en unidad complementaria de Intrastat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesCreditMemoLines": "Líneas de factura rectificativa de venta", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesPurchaseUnit": "Unidad de venta o compra", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesReturnReceiptLine": "Línea de recepción de devolución", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesShipmentLine": "Línea de expedición de venta", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__statisticalProcedure": "Régimen estadístico", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__toInvoiceLines": "Líneas de factura", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__totalExcludingTax": "Total sin impuestos", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__totalExcludingTaxInCompanyCurrency": "Total sin impuestos en divisa de sociedad", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__node_name": "Relación entre línea de declaración Intrastat y línea de factura rectificativa de compra", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__property__intrastatDeclarationLine": "Línea de declaración Intrastat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__property__purchaseCreditMemoLine": "Línea de factura rectificativa de compra", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__node_name": "Relación entre línea de declaración Intrastat y línea de factura de compra", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__property__intrastatDeclarationLine": "Línea de declaración Intrastat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__property__purchaseInvoiceLine": "Línea de factura de compra", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__node_name": "Relación entre línea de declaración Intrastat y línea de factura rectificativa de venta", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__property__intrastatDeclarationLine": "Línea de declaración Intrastat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__property__salesCreditMemoLine": "Línea de factura rectificativa de venta", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__node_name": "Relación entre línea de declaración Intrastat y línea de factura de venta", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__property__intrastatDeclarationLine": "Línea de declaración Intrastat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__property__salesInvoiceLine": "Línea de factura de venta", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-declarations/nodes__movement_rule__node_name": "Régimen de transacción", "@sage/xtrem-declarations/nodes__movement_rule__property__declarationPeriod": "Periodo de declaración", "@sage/xtrem-declarations/nodes__movement_rule__property__documentType": "Tipo de documento", "@sage/xtrem-declarations/nodes__movement_rule__property__intrastatFlow": "Flujo de Intrastat", "@sage/xtrem-declarations/nodes__movement_rule__property__isActive": "Activo", "@sage/xtrem-declarations/nodes__movement_rule__property__natureOfTransaction": "Naturaleza de transacción", "@sage/xtrem-declarations/nodes__movement_rule__property__setupId": "Id. de parametrización", "@sage/xtrem-declarations/nodes__movement_rule__property__statisticalProcedure": "Régimen estadístico", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-declarations/nodes__nature_of_transaction__node_name": "Naturaleza de transacción", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__id": "Id.", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__isActive": "Activa", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__name": "Nombre", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-declarations/nodes__statistical_procedure__node_name": "Régimen estadístico", "@sage/xtrem-declarations/nodes__statistical_procedure__property__id": "Id.", "@sage/xtrem-declarations/nodes__statistical_procedure__property__intrastatFlow": "Flujo de Intrastat", "@sage/xtrem-declarations/nodes__statistical_procedure__property__isActive": "Activo", "@sage/xtrem-declarations/nodes__statistical_procedure__property__name": "Nombre", "@sage/xtrem-declarations/package__name": "Declaraciones", "@sage/xtrem-declarations/page-extensions__delivery_mode_extension____navigationPanel__listItem__line3__title": "Modo de transporte de Intrastat", "@sage/xtrem-declarations/page-extensions__delivery_mode_extension__intrastatModeOfTransport____title": "Modo de transporte de Intrastat", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__id": "Id.", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__name": "Nombre", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__type": "Tipo", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____lookupDialogTitle": "Seleccionar unidad complementaria de Intrastat", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____title": "Unidad complementaria de Intrastat", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnitToStockUnitConversion____title": "Coeficiente de conversión de unidad de stock", "@sage/xtrem-declarations/pages__confirm-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__confirm-confirm": "Confirmar", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__line2__title": "Sociedad", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__line2Right__title": "Periodo", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__title__title": "Número", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__titleRight__title": "Estado", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration____objectTypePlural": "Declaraciones Intrastat", "@sage/xtrem-declarations/pages__intrastat_declaration____objectTypeSingular": "Declaración Intrastat", "@sage/xtrem-declarations/pages__intrastat_declaration____subtitle": "Declaración Intrastat", "@sage/xtrem-declarations/pages__intrastat_declaration____title": "Declaración Intrastat", "@sage/xtrem-declarations/pages__intrastat_declaration___id____title": "Id.", "@sage/xtrem-declarations/pages__intrastat_declaration__addLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__businessActions____title": "Acciones empresariales", "@sage/xtrem-declarations/pages__intrastat_declaration__buttonBlock____title": "Acciones empresariales", "@sage/xtrem-declarations/pages__intrastat_declaration__buttonGenerate____title": "Generar", "@sage/xtrem-declarations/pages__intrastat_declaration__buttonValidate____title": "Validar", "@sage/xtrem-declarations/pages__intrastat_declaration__company____placeholder": "Seleccionar sociedad", "@sage/xtrem-declarations/pages__intrastat_declaration__company____title": "Sociedad", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_deletion_dialog_text": "¿Quieres eliminar esta declaración y todas sus líneas?", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_deletion_dialog_title": "Confirmar eliminación", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_regeneration_dialog_text": "Vas a restablecer las líneas de esta declaración y todos los cambios manuales.", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_regeneration_dialog_title": "Confirmar regeneración", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_validation_dialog_text": "¿Quieres validar la declaración? No vas a poder volver a editarla ni eliminarla.", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_validation_dialog_title": "Confirmar validación", "@sage/xtrem-declarations/pages__intrastat_declaration__date____title": "Periodo", "@sage/xtrem-declarations/pages__intrastat_declaration__deleteDeclaration____title": "Eliminar", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentNumber____title": "Número de documento", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsBlock____title": "Documentos", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__amountExcludingTax": "Total sin impuestos en divisa de sociedad", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__documentDate": "Fecha de factura rectificativa", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__documentNumber": "Factura rectificativa", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__lineAmountExcludingTax": "Total sin impuestos en divisa de sociedad", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__quantity": "Cantidad de factura rectificativa", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____title": "Líneas de factura rectificativa de compra", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__amountExcludingTax": "Total sin impuestos en divisa de sociedad", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__documentDate": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__documentNumber": "Factura", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__lineAmountExcludingTax": "Total sin impuestos en divisa de sociedad", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__quantity": "Cantidad de factura", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____title": "Líneas de factura de compra", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__amountExcludingTax": "Total sin impuestos en divisa de sociedad", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__companyCurrency__id": "Divisa de sociedad", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__documentDate": "Fecha de factura rectificativa", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__documentNumber": "Factura rectificativa", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__lineAmountExcludingTax": "Total sin impuestos en divisa de sociedad", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__quantity": "Cantidad de factura rectificativa", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____title": "Líneas de factura rectificativa de venta", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__amountExcludingTax": "Total sin impuestos en divisa de sociedad", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__documentDate": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__documentNumber": "Factura", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__lineAmountExcludingTax": "Total sin impuestos en divisa de sociedad", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__quantity": "Cantidad de factura", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____title": "Líneas de factura de venta", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSection____title": "Documentos", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelGeneralSection____title": "General", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderbusinessEntityName____content": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderDate____content": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderSection____title": "Detalles de Intrastat", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelItem____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelQuantityAndNetPrice____title": "Cantidad y precio neto", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelTotalsExcludingTax____title": "Totales sin impuestos", "@sage/xtrem-declarations/pages__intrastat_declaration__filterBlock____title": "Criterios de filtro de datos", "@sage/xtrem-declarations/pages__intrastat_declaration__generate_generate": "Generar", "@sage/xtrem-declarations/pages__intrastat_declaration__generate_regenerate": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__headerBlock____title": "Criterios de extracción", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__businessEntity__name__title": "Código ISO 3166-1 alfa-2", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__name__title": "Nombre", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__name__title__2": "Símbolo", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__name__title__3": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__title": "Símbolo", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__purchaseReceiptLine___id__title": "Recepción de compra", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__purchaseReturnLine___id__title": "Devolución de compra", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__purchaseReturnLine___id__title__2": "Devolución de compra", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesPurchaseUnit__name__title": "Símbolo", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesPurchaseUnit__name__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnReceiptLine___id__columns__title__number": "Número de documento", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnReceiptLine___id__title": "Recepción de devolución de venta", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnReceiptLine___id__title__2": "Recepción de devolución de venta", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnRequestLine___id__title": "Id.", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnRequestLine___id__title__2": "Id. de documento", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnRequestLine___id__title__3": "Número de documento", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesShipmentLine___id__columns__title__number": "Número de documento", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesShipmentLine___id__title": "Expedición de venta", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesShipmentLine___id__title__2": "Expedición de venta", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__statisticalRule__name__title": "Id.", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__statisticalRule__name__title__2": "Nombre", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__statisticalRule__name__title__3": "Id.", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__amount": "Valor", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__beTaxIdNumber": "NIF-IVA de cliente", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__businessEntity__name": "Entidad empresarial", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__commodityCode": "Código de mercancías", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__commodityCodeEU": "Código de mercancías", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__companyCurrency__id": "Divisa de sociedad", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__country": "País de procedencia/destino", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__countryOfOrigin": "<PERSON><PERSON> de origen", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__currency__id": "Divisa", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__documentDate": "Fecha de documento", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__documentType": "Tipo de documento", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__flow": "F<PERSON>jo", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__geographicSubdivision": "Departamento", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__hasEmptyValue": "Valores vacíos", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__hasWarning": "Aviso", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__intrastatAdditionalUnit": "Unidad complementaria", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__intrastatAdditionalUnit__name": "Unidad adicional", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__lineNumber": "Lín<PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__linkToDocument": "Número de documento", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__modeOfTransport": "Modo de transporte", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__natureOfTransaction__name": "Naturaleza de transacción", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__netPrice": "<PERSON><PERSON> neto", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__netWeight": "Peso (kg)", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__purchaseReceiptLine___id": "Línea de recepción de compra", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__purchaseReturnLine___id": "Devolución de compra", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__quantity": "Cantidad", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__quantityInIntrastatAdditionalUnit": "Cantidad en unidad complementaria de Intrastat", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesPurchaseUnit__name": "Unidad", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesReturnReceiptLine___id": "Recepción de devolución de venta", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesReturnRequestLine___id": "Solicitud de devolución de venta", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesShipmentLine___id": "Expedición de venta", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__statisticalProcedure__name": "Régimen estadístico", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__statisticalRule__name": "Régimen estadístico", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__totalExcludingTax": "Total sin impuestos", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__totalExcludingTaxInCompanyCurrency": "Total sin impuestos en divisa de sociedad", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____dropdownActions__title": "Eliminar", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____title": "Resul<PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__mainSection____title": "General", "@sage/xtrem-declarations/pages__intrastat_declaration__maxAmount____title": "Importe máximo", "@sage/xtrem-declarations/pages__intrastat_declaration__minAmount____title": "Importe mínimo", "@sage/xtrem-declarations/pages__intrastat_declaration__number____title": "Número", "@sage/xtrem-declarations/pages__intrastat_declaration__numberNew____title": "Número", "@sage/xtrem-declarations/pages__intrastat_declaration__status____title": "Estado", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__listItem__title__title": "Tipo de documento", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-declarations/pages__movement_rule____objectTypePlural": "Regímenes de transacción", "@sage/xtrem-declarations/pages__movement_rule____objectTypeSingular": "Régimen de transacción", "@sage/xtrem-declarations/pages__movement_rule____title": "Régimen de transacción", "@sage/xtrem-declarations/pages__movement_rule__declarationPeriod____title": "Periodo de declaración", "@sage/xtrem-declarations/pages__movement_rule__documentType____title": "Tipo de documento", "@sage/xtrem-declarations/pages__movement_rule__generalSection____title": "General", "@sage/xtrem-declarations/pages__movement_rule__intrastatFlow____title": "F<PERSON>jo", "@sage/xtrem-declarations/pages__movement_rule__isActive____title": "Activo", "@sage/xtrem-declarations/pages__movement_rule__natureOfTransaction____title": "Naturaleza de transacción", "@sage/xtrem-declarations/pages__movement_rule__statisticalProcedure____title": "Régimen estadístico", "@sage/xtrem-declarations/pages__movement_rule_definition____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-declarations/pages__movement_rule_definition____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-declarations/pages__movement_rule_definition____subtitle": "Régimen de transacción", "@sage/xtrem-declarations/pages__movement_rule_definition____title": "Régimen de transacción", "@sage/xtrem-declarations/pages__movement_rule_definition__declarationPeriod____title": "Periodo de declaración", "@sage/xtrem-declarations/pages__movement_rule_definition__documentType____title": "Tipo de documento", "@sage/xtrem-declarations/pages__movement_rule_definition__generalSection____title": "General", "@sage/xtrem-declarations/pages__movement_rule_definition__intrastatFlow____title": "F<PERSON>jo", "@sage/xtrem-declarations/pages__movement_rule_definition__isActive____title": "Activo", "@sage/xtrem-declarations/pages__movement_rule_definition__natureOfTransaction____columns__title__id": "Id.", "@sage/xtrem-declarations/pages__movement_rule_definition__natureOfTransaction____columns__title__name": "Nombre", "@sage/xtrem-declarations/pages__movement_rule_definition__natureOfTransaction____title": "Naturaleza de transacción", "@sage/xtrem-declarations/pages__movement_rule_definition__statisticalRule____columns__title__id": "Id.", "@sage/xtrem-declarations/pages__movement_rule_definition__statisticalRule____columns__title__intrastatFlow": "F<PERSON>jo", "@sage/xtrem-declarations/pages__movement_rule_definition__statisticalRule____columns__title__name": "Nombre", "@sage/xtrem-declarations/pages__movement_rule_definition__statisticalRule____title": "Régimen estadístico", "@sage/xtrem-declarations/pages__nature_of_transaction____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-declarations/pages__nature_of_transaction____navigationPanel__optionsMenu__title__2": "Activas", "@sage/xtrem-declarations/pages__nature_of_transaction____objectTypePlural": "Naturalezas de transacción", "@sage/xtrem-declarations/pages__nature_of_transaction____objectTypeSingular": "Naturaleza de transacción", "@sage/xtrem-declarations/pages__nature_of_transaction____title": "Naturaleza de transacción", "@sage/xtrem-declarations/pages__nature_of_transaction___id____title": "Id.", "@sage/xtrem-declarations/pages__nature_of_transaction__generalSection____title": "General", "@sage/xtrem-declarations/pages__nature_of_transaction__id____title": "Id.", "@sage/xtrem-declarations/pages__nature_of_transaction__isActive____title": "Activa", "@sage/xtrem-declarations/pages__nature_of_transaction__name____title": "Nombre", "@sage/xtrem-declarations/pages__statistical_procedure____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-declarations/pages__statistical_procedure____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-declarations/pages__statistical_procedure____objectTypePlural": "Regímenes estadís<PERSON>", "@sage/xtrem-declarations/pages__statistical_procedure____objectTypeSingular": "Régimen estadístico", "@sage/xtrem-declarations/pages__statistical_procedure____title": "Régimen estadístico", "@sage/xtrem-declarations/pages__statistical_procedure___id____title": "Id.", "@sage/xtrem-declarations/pages__statistical_procedure__generalSection____title": "General", "@sage/xtrem-declarations/pages__statistical_procedure__id____title": "Id.", "@sage/xtrem-declarations/pages__statistical_procedure__intrastatFlow____title": "F<PERSON>jo", "@sage/xtrem-declarations/pages__statistical_procedure__isActive____title": "Activo", "@sage/xtrem-declarations/pages__statistical_procedure__name____title": "Nombre", "@sage/xtrem-declarations/pages__statistical_rule____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-declarations/pages__statistical_rule____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-declarations/pages__statistical_rule____subtitle": "Régimen estadístico", "@sage/xtrem-declarations/pages__statistical_rule____title": "Régimen estadístico", "@sage/xtrem-declarations/pages__statistical_rule___id____title": "_id", "@sage/xtrem-declarations/pages__statistical_rule__generalSection____title": "General", "@sage/xtrem-declarations/pages__statistical_rule__id____title": "Id.", "@sage/xtrem-declarations/pages__statistical_rule__intrastatFlow____title": "F<PERSON>jo", "@sage/xtrem-declarations/pages__statistical_rule__isActive____title": "Activo", "@sage/xtrem-declarations/pages__statistical_rule__name____title": "Nombre", "@sage/xtrem-declarations/permission__manage__name": "Gestionar", "@sage/xtrem-declarations/permission__read__name": "<PERSON><PERSON>"}