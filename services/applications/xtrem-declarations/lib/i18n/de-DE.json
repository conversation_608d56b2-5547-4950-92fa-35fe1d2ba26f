{"@sage/xtrem-declarations/activity__intrastat_declaration__name": "Intrastat-Meldung", "@sage/xtrem-declarations/activity__movement_rule__name": "Bewegungsregel", "@sage/xtrem-declarations/activity__nature_of_transaction__name": "Transaktionsart", "@sage/xtrem-declarations/activity__statistical_procedure__name": "Statistisches Verfahren", "@sage/xtrem-declarations/data_types__intrastat_declaration_period_enum__name": "Enum Zeitraum Intrastat-Meldung", "@sage/xtrem-declarations/data_types__intrastat_declaration_status_enum__name": "Enum Status Intrastat-Meldung", "@sage/xtrem-declarations/data_types__intrastat_document_type_enum__name": "Enum Dokumenttyp Intrastat", "@sage/xtrem-declarations/data_types__intrastat_flow_enum__name": "Enum Fluss Intrastat", "@sage/xtrem-declarations/data_types__intrastat_mode_of_transport_enum__name": "Enum Transportart Intrastat", "@sage/xtrem-declarations/enums__intrastat_declaration_period__earliestMonth": "Frühester Monat: nach Rechnung oder Transaktion +1", "@sage/xtrem-declarations/enums__intrastat_declaration_period__transactionMonth": "Transaktionsmonat", "@sage/xtrem-declarations/enums__intrastat_declaration_status__draft": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/enums__intrastat_declaration_status__recorded": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/enums__intrastat_declaration_status__validated": "Freigegeben", "@sage/xtrem-declarations/enums__intrastat_document_type__purchaseReceipt": "Wareneingang", "@sage/xtrem-declarations/enums__intrastat_document_type__purchaseReturn": "Einkaufsretoure", "@sage/xtrem-declarations/enums__intrastat_document_type__salesReturn": "Verkaufsretoure", "@sage/xtrem-declarations/enums__intrastat_document_type__salesShipment": "Warenausgang", "@sage/xtrem-declarations/enums__intrastat_flow__arrival": "Einfuhr", "@sage/xtrem-declarations/enums__intrastat_flow__dispatch": "Ausfuhr", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__airTransport": "Luftverkehr", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byAir": "Luftverkehr", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byInlandNavigation": "Binnenschifffahrt", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byMail": "Postsendungen", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byRail": "Eisenbahnverkehr", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__byRoad": "Straßenverkehr", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__bySea": "Seeverkehr", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__fixedTransportInstallations": "Festinstallierte Transporteinrichtungen", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__inlandWaterwayTransport": "Binnenschifffahrt", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__internalNavigation": "Festinstallierte Transporteinrichtungen", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__ownPropulsion": "<PERSON><PERSON><PERSON> An<PERSON>", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__postalConsignments": "Postsendungen", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__railwayTransport": "Eisenbahnverkehr", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__roadTransport": "Straßenverkehr", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__seaTransport": "Seeverkehr", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__selfPropelled": "<PERSON><PERSON><PERSON> An<PERSON>", "@sage/xtrem-declarations/intrastat_declaration__date__not_end_of_month": "Das Zeitraumende ist kein Monatsende.", "@sage/xtrem-declarations/menu_item__intrastat": "Intrastat", "@sage/xtrem-declarations/node-extensions__delivery_mode_extension__property__intrastatModeOfTransport": "Transportart Intrastat", "@sage/xtrem-declarations/node-extensions__item_extension__intrastat_additional_unit_to_stock_unit_conversion_mandatory": "Erfassen Sie den Umrechnungsfaktor für die zusätzliche Einheit für Intrastat.", "@sage/xtrem-declarations/node-extensions__item_extension__property__intrastatAdditionalUnit": "Zusätzliche Einheit Intrastat", "@sage/xtrem-declarations/node-extensions__item_extension__property__intrastatAdditionalUnitToStockUnitConversion": "Umrechnung zusätzliche Einheit für Intrastat in Lagereinheit", "@sage/xtrem-declarations/node-extensions__purchase_receipt_line_extension__property__intrastatDeclarations": "Intrastat-Meldungen", "@sage/xtrem-declarations/node-extensions__purchase_return_line_extension__property__intrastatDeclarations": "Intrastat-Meldungen", "@sage/xtrem-declarations/node-extensions__sales_return_receipt_line_extension__property__intrastatDeclarations": "Intrastat-Meldungen", "@sage/xtrem-declarations/node-extensions__sales_shipment_line_extension__property__intrastatDeclarations": "Intrastat-Meldungen", "@sage/xtrem-declarations/nodes__intrastat_decalartioni__not-a-valid-tax-id": "Verwenden Sie für die Steuer-ID das folgende Format: {{format}}", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__intrastat_declaration__deletion_forbidden_validated": "Die aktuelle Intrastat-Meldung kann nicht gelöscht werden. Sie wurde bereits freigegeben.", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines": "Zeilen ausgeben", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines__failed": "Zeilen ausgeben fehlgeschlagen.", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines__parameter__intrastatDeclaration": "Intrastat-Meldung", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration": "Meldung freigeben", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration__failed": "Meldung freigeben fehlgeschlagen.", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration__parameter__intrastatDeclaration": "Intrastat-Meldung", "@sage/xtrem-declarations/nodes__intrastat_declaration__node_name": "Intrastat-Meldung", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__company": "Unternehmen", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__date": "Datum", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__number": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__status": "Status", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__commodity_code_format": "Verwenden Sie für die Warennummer das folgende Format: {{format}}", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__node_name": "Zeile Intrastat-Meldung", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__amount": "Betrag", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__baseDocumentLine": "Basisdokumentzeilen", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__beTaxIdNumber": "Steueridentifikationsnummer", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__businessEntity": "Geschäftsentität", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__commodityCode": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__companyCurrency": "Unternehmenswährung", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedPurchaseCreditMemoLines": "Berechnete Einkaufsgutschriftzeilen", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedPurchaseInvoiceLines": "Berechnete Einkaufsrechnungszeilen", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedSalesCreditMemoLines": "Berechnete Verkaufsgutschriftzeilen", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedSalesInvoiceLines": "Berechnete Verkaufsrechnungszeilen", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__country": "Land", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__countryOfOrigin": "Ursprungsland", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__currency": "Währung", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__documentDate": "Dokumentdatum", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__documentType": "Dokumenttyp", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__flow": "Fluss", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__geographicSubdivision": "Geographische Untereinheit", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__hasEmptyValue": "Hat leere Werte", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__hasWarning": "Hat <PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__intrastatAdditionalUnit": "Zusätzliche Einheit Intrastat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__intrastatDeclaration": "Intrastat-Meldung", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__item": "Artikel", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__lineNumber": "Zeilennummer", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__modeOfTransport": "Transportart", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__natureOfTransaction": "Transaktionsart", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__netPrice": "Nettopreis", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__netWeight": "Nettogewicht", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseCreditMemoLines": "Einkaufsgutschriftzeilen", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseInvoiceLines": "Einkaufsrechnungszeilen", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseReceiptLine": "Wareneingangszeile", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseReturnLine": "Einkaufsretourzeile", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__quantity": "<PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__quantityInIntrastatAdditionalUnit": "Menge in zusätzlicher Einheit Intrastat", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesCreditMemoLines": "Verkaufsgutschriftzeilen", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesPurchaseUnit": "Verkaufseinheit oder Einkaufseinheit", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesReturnReceiptLine": "Verkaufsretoureneingangszeile", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesShipmentLine": "Warenausgangszeile", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__statisticalProcedure": "Statistisches Verfahren", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__toInvoiceLines": "<PERSON><PERSON>", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__totalExcludingTax": "Summe exkl. Steuern", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__totalExcludingTaxInCompanyCurrency": "Summe exkl. Steuern in Unternehmenswährung", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__node_name": "Intrastat-Meldungszeile zu Einkaufsgutschriftzeile", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__property__intrastatDeclarationLine": "Zeile Intrastat-Meldung", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__property__purchaseCreditMemoLine": "Einkaufsgutschriftzeile", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__node_name": "Intrastat-Meldungszeile zu Einkaufsrechnungszeile", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__property__intrastatDeclarationLine": "Zeile Intrastat-Meldung", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__property__purchaseInvoiceLine": "Einkaufsrechnungszeile", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__node_name": "Intrastat-Meldungszeile zu Verkaufsgutschriftzeile", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__property__intrastatDeclarationLine": "Zeile Intrastat-Meldung", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__property__salesCreditMemoLine": "Verkaufsgutschriftzeile", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__node_name": "Intrastat-Meldungszeile zu Verkaufsrechnungszeile", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__property__intrastatDeclarationLine": "Zeile Intrastat-Meldung", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__property__salesInvoiceLine": "Verkaufsrechnungszeile", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__movement_rule__node_name": "Bewegungsregel", "@sage/xtrem-declarations/nodes__movement_rule__property__declarationPeriod": "Meldungszeitraum", "@sage/xtrem-declarations/nodes__movement_rule__property__documentType": "Dokumenttyp", "@sage/xtrem-declarations/nodes__movement_rule__property__intrastatFlow": "Intrastat-Fluss", "@sage/xtrem-declarations/nodes__movement_rule__property__isActive": "Aktiv", "@sage/xtrem-declarations/nodes__movement_rule__property__natureOfTransaction": "Transaktionsart", "@sage/xtrem-declarations/nodes__movement_rule__property__setupId": "ID Einstellungen", "@sage/xtrem-declarations/nodes__movement_rule__property__statisticalProcedure": "Statistisches Verfahren", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__nature_of_transaction__node_name": "Transaktionsart", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__id": "ID", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__isActive": "Aktiv", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__name": "Name", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport": "Export", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-declarations/nodes__statistical_procedure__node_name": "Statistisches Verfahren", "@sage/xtrem-declarations/nodes__statistical_procedure__property__id": "ID", "@sage/xtrem-declarations/nodes__statistical_procedure__property__intrastatFlow": "Intrastat-Fluss", "@sage/xtrem-declarations/nodes__statistical_procedure__property__isActive": "Aktiv", "@sage/xtrem-declarations/nodes__statistical_procedure__property__name": "Name", "@sage/xtrem-declarations/package__name": "Meldungen", "@sage/xtrem-declarations/page-extensions__delivery_mode_extension____navigationPanel__listItem__line3__title": "Transportart Intrastat", "@sage/xtrem-declarations/page-extensions__delivery_mode_extension__intrastatModeOfTransport____title": "Transportart Intrastat", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__decimalDigits": "Dezimalstellen", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__id": "ID", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__name": "Name", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____lookupDialogTitle": "Zusätzliche Einheit Intrastat auswählen", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____title": "Zusätzliche Einheit Intrastat", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnitToStockUnitConversion____title": "Umrechnungsfaktor Lagereinheit", "@sage/xtrem-declarations/pages__confirm-cancel": "Abbrechen", "@sage/xtrem-declarations/pages__confirm-confirm": "Bestätigen", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__line2__title": "Unternehmen", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__line2Right__title": "Zeitraum", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-declarations/pages__intrastat_declaration____objectTypePlural": "Intrastat-Meldungen", "@sage/xtrem-declarations/pages__intrastat_declaration____objectTypeSingular": "Intrastat-Meldung", "@sage/xtrem-declarations/pages__intrastat_declaration____subtitle": "Intrastat-Meldung", "@sage/xtrem-declarations/pages__intrastat_declaration____title": "Intrastat-Meldung", "@sage/xtrem-declarations/pages__intrastat_declaration___id____title": "ID", "@sage/xtrem-declarations/pages__intrastat_declaration__addLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__businessActions____title": "Geschäftshandlungen", "@sage/xtrem-declarations/pages__intrastat_declaration__buttonBlock____title": "Geschäftshandlungen", "@sage/xtrem-declarations/pages__intrastat_declaration__buttonGenerate____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__buttonValidate____title": "Freigeben", "@sage/xtrem-declarations/pages__intrastat_declaration__company____placeholder": "Unternehmen auswählen", "@sage/xtrem-declarations/pages__intrastat_declaration__company____title": "Unternehmen", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_deletion_dialog_text": "<PERSON>e sind dabei, diese Meldung einschließlich aller Zeilen zu löschen.", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_deletion_dialog_title": "Löschen bestätigen", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_regeneration_dialog_text": "<PERSON><PERSON> sind dabei, die Meldungszeilen einschließlich aller manuellen Änderungen zurückzusetzen.", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_regeneration_dialog_title": "Erneute Generierung bestätigen", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_validation_dialog_text": "<PERSON><PERSON> sind dabei, die Meldung freizugeben. <PERSON><PERSON> ist das Bearbeiten oder Löschen nicht mehr möglich.", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_validation_dialog_title": "Freigabe bestätigen", "@sage/xtrem-declarations/pages__intrastat_declaration__date____title": "Zeitraum", "@sage/xtrem-declarations/pages__intrastat_declaration__deleteDeclaration____title": "Löschen", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentNumber____title": "Dokumentnummer", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsBlock____title": "Dokumente", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__amountExcludingTax": "Summe exkl. Steuern in Unternehmenswährung", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__documentDate": "Datum Gutschrift", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__documentNumber": "Gutschrift", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__quantity": "Gutschriftmenge", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____title": "Einkaufsgutschriftzeilen", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__amountExcludingTax": "Summe exkl. Steuern in Unternehmenswährung", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__documentDate": "Rechnungsdatum", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__documentNumber": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__quantity": "Re<PERSON><PERSON>ngsmeng<PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____title": "Einkaufsrechnungszeilen", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__amountExcludingTax": "Summe exkl. Steuern in Unternehmenswährung", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__companyCurrency__id": "Unternehmenswährung", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__documentDate": "Datum Gutschrift", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__documentNumber": "Gutschrift", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__quantity": "Gutschriftmenge", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____title": "Verkaufsgutschriftzeilen", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__amountExcludingTax": "Summe exkl. Steuern in Unternehmenswährung", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__documentDate": "Rechnungsdatum", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__documentNumber": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__quantity": "Re<PERSON><PERSON>ngsmeng<PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____title": "Verkaufsrechnungszeilen", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSection____title": "Dokumente", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelGeneralSection____title": "Allgemein", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderbusinessEntityName____content": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderDate____content": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderSection____title": "Details Intrastat", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelItem____title": "Artikel", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelQuantityAndNetPrice____title": "Menge und Nettopreis", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelTotalsExcludingTax____title": "Summen exkl. Steuern", "@sage/xtrem-declarations/pages__intrastat_declaration__filterBlock____title": "Datenfilterkriterien", "@sage/xtrem-declarations/pages__intrastat_declaration__generate_generate": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__generate_regenerate": "<PERSON><PERSON><PERSON> gene<PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__headerBlock____title": "Ausgabekriterien", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__businessEntity__name__title": "ISO 3166-1 Alpha-2", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__name__title": "Name", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__name__title__2": "Symbol", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__name__title__3": "Dezimalstellen", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__title": "Symbol", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__title__2": "Dezimalstellen", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__purchaseReceiptLine___id__title": "Wareneingang", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__purchaseReturnLine___id__title": "Einkaufsretoure", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__purchaseReturnLine___id__title__2": "Einkaufsretoure", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesPurchaseUnit__name__title": "Symbol", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesPurchaseUnit__name__title__2": "Dezimalstellen", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnReceiptLine___id__columns__title__number": "Dokumentnummer", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnReceiptLine___id__title": "Verkaufsretoureneingang", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnReceiptLine___id__title__2": "Verkaufsretoureneingang", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnRequestLine___id__title": "ID", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnRequestLine___id__title__2": "Dokument-ID", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnRequestLine___id__title__3": "Dokumentnummer", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesShipmentLine___id__columns__title__number": "Dokumentnummer", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesShipmentLine___id__title": "Warenausgang", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesShipmentLine___id__title__2": "Warenausgang", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__statisticalRule__name__title": "ID", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__statisticalRule__name__title__2": "Name", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__statisticalRule__name__title__3": "ID", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__amount": "Wert", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__beTaxIdNumber": "Steuer-ID Kunde", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__businessEntity__name": "Geschäftsentität", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__commodityCode": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__commodityCodeEU": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__companyCurrency__id": "Unternehmenswährung", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__country": "Einfuhr-/Ausfuhrland", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__countryOfOrigin": "Ursprungsland", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__currency__id": "Währung", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__documentDate": "Dokumentdatum", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__documentType": "Dokumenttyp", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__flow": "Fluss", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__geographicSubdivision": "Departement", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__hasEmptyValue": "Hat leere Werte", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__hasWarning": "Hat <PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__intrastatAdditionalUnit": "Zusätzliche Einheit", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__intrastatAdditionalUnit__name": "Zusätzliche Einheit", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__item__name": "Artikel", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__lineNumber": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__linkToDocument": "Dokumentnummer", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__modeOfTransport": "Transportart", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__natureOfTransaction__name": "Transaktionsart", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__netPrice": "Nettopreis", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__netWeight": "Gewicht (kg)", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__purchaseReceiptLine___id": "Wareneingangszeile", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__purchaseReturnLine___id": "Einkaufsretoure", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__quantity": "<PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__quantityInIntrastatAdditionalUnit": "Menge in zusätzlicher Einheit Intrastat", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesPurchaseUnit__name": "Einheit", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesReturnReceiptLine___id": "Verkaufsretoureneingang", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesReturnRequestLine___id": "Verkaufsretouranforderung", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesShipmentLine___id": "Warenausgang", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__statisticalProcedure__name": "Statistisches Verfahren", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__statisticalRule__name": "Statistisches Verfahren", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__totalExcludingTax": "Summe exkl. Steuern", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__totalExcludingTaxInCompanyCurrency": "Summe exkl. Steuern in Unternehmenswährung", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____dropdownActions__title": "Löschen", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____title": "Ergebnisse", "@sage/xtrem-declarations/pages__intrastat_declaration__mainSection____title": "Allgemein", "@sage/xtrem-declarations/pages__intrastat_declaration__maxAmount____title": "Höchstbetrag", "@sage/xtrem-declarations/pages__intrastat_declaration__minAmount____title": "Mindestbetrag", "@sage/xtrem-declarations/pages__intrastat_declaration__number____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__numberNew____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/pages__intrastat_declaration__status____title": "Status", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__listItem__title__title": "Dokumenttyp", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-declarations/pages__movement_rule____objectTypePlural": "Bewegungsregeln", "@sage/xtrem-declarations/pages__movement_rule____objectTypeSingular": "Bewegungsregel", "@sage/xtrem-declarations/pages__movement_rule____title": "Bewegungsregel", "@sage/xtrem-declarations/pages__movement_rule__declarationPeriod____title": "Meldungszeitraum", "@sage/xtrem-declarations/pages__movement_rule__documentType____title": "Dokumenttyp", "@sage/xtrem-declarations/pages__movement_rule__generalSection____title": "Allgemein", "@sage/xtrem-declarations/pages__movement_rule__intrastatFlow____title": "Fluss", "@sage/xtrem-declarations/pages__movement_rule__isActive____title": "Aktiv", "@sage/xtrem-declarations/pages__movement_rule__natureOfTransaction____title": "Transaktionsart", "@sage/xtrem-declarations/pages__movement_rule__statisticalProcedure____title": "Statistisches Verfahren", "@sage/xtrem-declarations/pages__movement_rule_definition____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-declarations/pages__movement_rule_definition____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-declarations/pages__movement_rule_definition____subtitle": "Definition Bewegungsregel", "@sage/xtrem-declarations/pages__movement_rule_definition____title": "Definition Bewegungsregel", "@sage/xtrem-declarations/pages__movement_rule_definition__declarationPeriod____title": "Meldungszeitraum", "@sage/xtrem-declarations/pages__movement_rule_definition__documentType____title": "Dokumententyp", "@sage/xtrem-declarations/pages__movement_rule_definition__generalSection____title": "Allgemein", "@sage/xtrem-declarations/pages__movement_rule_definition__intrastatFlow____title": "Fluss", "@sage/xtrem-declarations/pages__movement_rule_definition__isActive____title": "Aktiv", "@sage/xtrem-declarations/pages__movement_rule_definition__natureOfTransaction____columns__title__id": "ID", "@sage/xtrem-declarations/pages__movement_rule_definition__natureOfTransaction____columns__title__name": "Name", "@sage/xtrem-declarations/pages__movement_rule_definition__natureOfTransaction____title": "Transaktionsart", "@sage/xtrem-declarations/pages__movement_rule_definition__statisticalRule____columns__title__id": "ID", "@sage/xtrem-declarations/pages__movement_rule_definition__statisticalRule____columns__title__intrastatFlow": "Fluss", "@sage/xtrem-declarations/pages__movement_rule_definition__statisticalRule____columns__title__name": "Name", "@sage/xtrem-declarations/pages__movement_rule_definition__statisticalRule____title": "Statistisches Verfahren", "@sage/xtrem-declarations/pages__nature_of_transaction____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-declarations/pages__nature_of_transaction____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-declarations/pages__nature_of_transaction____objectTypePlural": "Transaktionsarten", "@sage/xtrem-declarations/pages__nature_of_transaction____objectTypeSingular": "Transaktionsart", "@sage/xtrem-declarations/pages__nature_of_transaction____title": "Transaktionsart", "@sage/xtrem-declarations/pages__nature_of_transaction___id____title": "ID", "@sage/xtrem-declarations/pages__nature_of_transaction__generalSection____title": "Allgemein", "@sage/xtrem-declarations/pages__nature_of_transaction__id____title": "ID", "@sage/xtrem-declarations/pages__nature_of_transaction__isActive____title": "Aktiv", "@sage/xtrem-declarations/pages__nature_of_transaction__name____title": "Name", "@sage/xtrem-declarations/pages__statistical_procedure____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-declarations/pages__statistical_procedure____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-declarations/pages__statistical_procedure____objectTypePlural": "Statistische Verfahren", "@sage/xtrem-declarations/pages__statistical_procedure____objectTypeSingular": "Statistisches Verfahren", "@sage/xtrem-declarations/pages__statistical_procedure____title": "Statistisches Verfahren", "@sage/xtrem-declarations/pages__statistical_procedure___id____title": "ID", "@sage/xtrem-declarations/pages__statistical_procedure__generalSection____title": "Allgemein", "@sage/xtrem-declarations/pages__statistical_procedure__id____title": "ID", "@sage/xtrem-declarations/pages__statistical_procedure__intrastatFlow____title": "Fluss", "@sage/xtrem-declarations/pages__statistical_procedure__isActive____title": "Aktiv", "@sage/xtrem-declarations/pages__statistical_procedure__name____title": "Name", "@sage/xtrem-declarations/pages__statistical_rule____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-declarations/pages__statistical_rule____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-declarations/pages__statistical_rule____subtitle": "Statistisches Verfahren", "@sage/xtrem-declarations/pages__statistical_rule____title": "Statistisches Verfahren", "@sage/xtrem-declarations/pages__statistical_rule___id____title": "_id", "@sage/xtrem-declarations/pages__statistical_rule__generalSection____title": "Allgemein", "@sage/xtrem-declarations/pages__statistical_rule__id____title": "ID", "@sage/xtrem-declarations/pages__statistical_rule__intrastatFlow____title": "Fluss", "@sage/xtrem-declarations/pages__statistical_rule__isActive____title": "Aktiv", "@sage/xtrem-declarations/pages__statistical_rule__name____title": "Name", "@sage/xtrem-declarations/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-declarations/permission__read__name": "<PERSON><PERSON>"}