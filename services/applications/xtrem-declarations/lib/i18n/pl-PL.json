{"@sage/xtrem-declarations/activity__intrastat_declaration__name": "", "@sage/xtrem-declarations/activity__movement_rule__name": "", "@sage/xtrem-declarations/activity__nature_of_transaction__name": "", "@sage/xtrem-declarations/activity__statistical_procedure__name": "", "@sage/xtrem-declarations/data_types__intrastat_declaration_period_enum__name": "", "@sage/xtrem-declarations/data_types__intrastat_declaration_status_enum__name": "", "@sage/xtrem-declarations/data_types__intrastat_document_type_enum__name": "", "@sage/xtrem-declarations/data_types__intrastat_flow_enum__name": "", "@sage/xtrem-declarations/data_types__intrastat_mode_of_transport_enum__name": "", "@sage/xtrem-declarations/enums__intrastat_declaration_period__earliestMonth": "", "@sage/xtrem-declarations/enums__intrastat_declaration_period__transactionMonth": "", "@sage/xtrem-declarations/enums__intrastat_declaration_status__draft": "", "@sage/xtrem-declarations/enums__intrastat_declaration_status__recorded": "", "@sage/xtrem-declarations/enums__intrastat_declaration_status__validated": "", "@sage/xtrem-declarations/enums__intrastat_document_type__purchaseReceipt": "", "@sage/xtrem-declarations/enums__intrastat_document_type__purchaseReturn": "", "@sage/xtrem-declarations/enums__intrastat_document_type__salesReturn": "", "@sage/xtrem-declarations/enums__intrastat_document_type__salesShipment": "", "@sage/xtrem-declarations/enums__intrastat_flow__arrival": "", "@sage/xtrem-declarations/enums__intrastat_flow__dispatch": "", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__airTransport": "", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__fixedTransportInstallations": "", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__inlandWaterwayTransport": "", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__ownPropulsion": "", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__postalConsignments": "", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__railwayTransport": "", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__roadTransport": "", "@sage/xtrem-declarations/enums__intrastat_mode_of_transport__seaTransport": "", "@sage/xtrem-declarations/intrastat_declaration__date__not_end_of_month": "", "@sage/xtrem-declarations/menu_item__intrastat": "", "@sage/xtrem-declarations/node-extensions__delivery_mode_extension__property__intrastatModeOfTransport": "", "@sage/xtrem-declarations/node-extensions__item_extension__intrastat_additional_unit_to_stock_unit_conversion_mandatory": "", "@sage/xtrem-declarations/node-extensions__item_extension__property__intrastatAdditionalUnit": "", "@sage/xtrem-declarations/node-extensions__item_extension__property__intrastatAdditionalUnitToStockUnitConversion": "", "@sage/xtrem-declarations/node-extensions__purchase_receipt_line_extension__property__intrastatDeclarations": "", "@sage/xtrem-declarations/node-extensions__purchase_return_line_extension__property__intrastatDeclarations": "", "@sage/xtrem-declarations/node-extensions__sales_return_receipt_line_extension__property__intrastatDeclarations": "", "@sage/xtrem-declarations/node-extensions__sales_shipment_line_extension__property__intrastatDeclarations": "", "@sage/xtrem-declarations/nodes__intrastat_decalartioni__not-a-valid-tax-id": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__deletion_forbidden_validated": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines__failed": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__extractLines__parameter__intrastatDeclaration": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration__failed": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__mutation__validateDeclaration__parameter__intrastatDeclaration": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__node_name": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__company": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__date": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__lines": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__number": "", "@sage/xtrem-declarations/nodes__intrastat_declaration__property__status": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__commodity_code_format": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__node_name": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__amount": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__baseDocumentLine": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__beTaxIdNumber": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__businessEntity": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__commodityCode": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__companyCurrency": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedPurchaseCreditMemoLines": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedPurchaseInvoiceLines": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedSalesCreditMemoLines": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__computedSalesInvoiceLines": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__country": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__countryOfOrigin": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__currency": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__documentDate": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__documentType": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__flow": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__geographicSubdivision": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__hasEmptyValue": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__hasWarning": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__intrastatAdditionalUnit": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__intrastatDeclaration": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__item": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__lineNumber": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__modeOfTransport": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__natureOfTransaction": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__netPrice": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__netWeight": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseCreditMemoLines": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseInvoiceLines": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseReceiptLine": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__purchaseReturnLine": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__quantity": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__quantityInIntrastatAdditionalUnit": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesCreditMemoLines": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesPurchaseUnit": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesReturnReceiptLine": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__salesShipmentLine": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__statisticalProcedure": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__toInvoiceLines": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__totalExcludingTax": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line__property__totalExcludingTaxInCompanyCurrency": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__node_name": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__property__intrastatDeclarationLine": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_credit_memo_line__property__purchaseCreditMemoLine": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__node_name": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__property__intrastatDeclarationLine": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_purchase_invoice_line__property__purchaseInvoiceLine": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__node_name": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__property__intrastatDeclarationLine": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_credit_memo_line__property__salesCreditMemoLine": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__node_name": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__property__intrastatDeclarationLine": "", "@sage/xtrem-declarations/nodes__intrastat_declaration_line_to_sales_invoice_line__property__salesInvoiceLine": "", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport": "", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-declarations/nodes__movement_rule__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-declarations/nodes__movement_rule__node_name": "", "@sage/xtrem-declarations/nodes__movement_rule__property__declarationPeriod": "", "@sage/xtrem-declarations/nodes__movement_rule__property__documentType": "", "@sage/xtrem-declarations/nodes__movement_rule__property__intrastatFlow": "", "@sage/xtrem-declarations/nodes__movement_rule__property__isActive": "", "@sage/xtrem-declarations/nodes__movement_rule__property__natureOfTransaction": "", "@sage/xtrem-declarations/nodes__movement_rule__property__statisticalProcedure": "", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport": "", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-declarations/nodes__nature_of_transaction__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-declarations/nodes__nature_of_transaction__node_name": "", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__id": "", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__isActive": "", "@sage/xtrem-declarations/nodes__nature_of_transaction__property__name": "", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport": "", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-declarations/nodes__statistical_procedure__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-declarations/nodes__statistical_procedure__node_name": "", "@sage/xtrem-declarations/nodes__statistical_procedure__property__id": "", "@sage/xtrem-declarations/nodes__statistical_procedure__property__intrastatFlow": "", "@sage/xtrem-declarations/nodes__statistical_procedure__property__isActive": "", "@sage/xtrem-declarations/nodes__statistical_procedure__property__name": "", "@sage/xtrem-declarations/package__name": "", "@sage/xtrem-declarations/page-extensions__delivery_mode_extension____navigationPanel__listItem__line3__title": "", "@sage/xtrem-declarations/page-extensions__delivery_mode_extension__intrastatModeOfTransport____title": "", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__decimalDigits": "", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__id": "", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__name": "", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____columns__title__type": "", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____lookupDialogTitle": "", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnit____title": "", "@sage/xtrem-declarations/page-extensions__item_extension__intrastatAdditionalUnitToStockUnitConversion____title": "", "@sage/xtrem-declarations/pages__confirm-cancel": "", "@sage/xtrem-declarations/pages__confirm-confirm": "", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__line2__title": "", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__line2Right__title": "", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__title__title": "", "@sage/xtrem-declarations/pages__intrastat_declaration____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-declarations/pages__intrastat_declaration____objectTypePlural": "", "@sage/xtrem-declarations/pages__intrastat_declaration____objectTypeSingular": "", "@sage/xtrem-declarations/pages__intrastat_declaration____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration___id____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__addLine____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__buttonGenerate____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__buttonValidate____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__company____placeholder": "", "@sage/xtrem-declarations/pages__intrastat_declaration__company____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_deletion_dialog_text": "", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_deletion_dialog_title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_regeneration_dialog_text": "", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_regeneration_dialog_title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_validation_dialog_text": "", "@sage/xtrem-declarations/pages__intrastat_declaration__confirm_validation_dialog_title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__date____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__deleteDeclaration____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentNumber____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsBlock____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__amountExcludingTax": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__documentDate": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__documentNumber": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____columns__title__quantity": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseCreditMemoLines____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__amountExcludingTax": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__documentDate": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__documentNumber": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____columns__title__quantity": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsPurchaseInvoiceLines____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__amountExcludingTax": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__companyCurrency__id": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__documentDate": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__documentNumber": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____columns__title__quantity": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesCreditMemoLine____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__amountExcludingTax": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__documentDate": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__documentNumber": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____columns__title__quantity": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSalesInvoiceLines____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelDocumentsSection____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelGeneralSection____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderbusinessEntityName____content": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderDate____content": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelHeaderSection____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelItem____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelQuantityAndNetPrice____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__detailPanelTotalsExcludingTax____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__generate_generate": "", "@sage/xtrem-declarations/pages__intrastat_declaration__generate_regenerate": "", "@sage/xtrem-declarations/pages__intrastat_declaration__headerBlock____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__businessEntity__name__title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__intrastatAdditionalUnit__title__2": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__purchaseReceiptLine___id__title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__purchaseReturnLine___id__title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesPurchaseUnit__name__title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesPurchaseUnit__name__title__2": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesReturnReceiptLine___id__title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__columns__salesShipmentLine___id__title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__amount": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__beTaxIdNumber": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__businessEntity__name": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__commodityCode": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__companyCurrency__id": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__country": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__countryOfOrigin": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__currency__id": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__documentDate": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__documentType": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__flow": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__geographicSubdivision": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__hasEmptyValue": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__hasWarning": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__intrastatAdditionalUnit": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__item__name": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__lineNumber": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__linkToDocument": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__modeOfTransport": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__natureOfTransaction__name": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__netPrice": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__netWeight": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__purchaseReceiptLine___id": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__purchaseReturnLine___id": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__quantity": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__quantityInIntrastatAdditionalUnit": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesPurchaseUnit__name": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesReturnReceiptLine___id": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__salesShipmentLine___id": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__statisticalProcedure__name": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__totalExcludingTax": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____columns__title__totalExcludingTaxInCompanyCurrency": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____dropdownActions__title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__lines____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__mainSection____title": "", "@sage/xtrem-declarations/pages__intrastat_declaration__number____title": "", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__listItem__title__title": "", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__optionsMenu__title": "", "@sage/xtrem-declarations/pages__movement_rule____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-declarations/pages__movement_rule____objectTypePlural": "", "@sage/xtrem-declarations/pages__movement_rule____objectTypeSingular": "", "@sage/xtrem-declarations/pages__movement_rule____title": "", "@sage/xtrem-declarations/pages__movement_rule__declarationPeriod____title": "", "@sage/xtrem-declarations/pages__movement_rule__documentType____title": "", "@sage/xtrem-declarations/pages__movement_rule__generalSection____title": "", "@sage/xtrem-declarations/pages__movement_rule__intrastatFlow____title": "", "@sage/xtrem-declarations/pages__movement_rule__isActive____title": "", "@sage/xtrem-declarations/pages__movement_rule__natureOfTransaction____title": "", "@sage/xtrem-declarations/pages__movement_rule__statisticalProcedure____title": "", "@sage/xtrem-declarations/pages__nature_of_transaction____navigationPanel__optionsMenu__title": "", "@sage/xtrem-declarations/pages__nature_of_transaction____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-declarations/pages__nature_of_transaction____objectTypePlural": "", "@sage/xtrem-declarations/pages__nature_of_transaction____objectTypeSingular": "", "@sage/xtrem-declarations/pages__nature_of_transaction____title": "", "@sage/xtrem-declarations/pages__nature_of_transaction___id____title": "", "@sage/xtrem-declarations/pages__nature_of_transaction__generalSection____title": "", "@sage/xtrem-declarations/pages__nature_of_transaction__id____title": "", "@sage/xtrem-declarations/pages__nature_of_transaction__isActive____title": "", "@sage/xtrem-declarations/pages__nature_of_transaction__name____title": "", "@sage/xtrem-declarations/pages__statistical_procedure____navigationPanel__optionsMenu__title": "", "@sage/xtrem-declarations/pages__statistical_procedure____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-declarations/pages__statistical_procedure____objectTypePlural": "", "@sage/xtrem-declarations/pages__statistical_procedure____objectTypeSingular": "", "@sage/xtrem-declarations/pages__statistical_procedure____title": "", "@sage/xtrem-declarations/pages__statistical_procedure___id____title": "", "@sage/xtrem-declarations/pages__statistical_procedure__generalSection____title": "", "@sage/xtrem-declarations/pages__statistical_procedure__id____title": "", "@sage/xtrem-declarations/pages__statistical_procedure__intrastatFlow____title": "", "@sage/xtrem-declarations/pages__statistical_procedure__isActive____title": "", "@sage/xtrem-declarations/pages__statistical_procedure__name____title": "", "@sage/xtrem-declarations/permission__manage__name": "", "@sage/xtrem-declarations/permission__read__name": ""}