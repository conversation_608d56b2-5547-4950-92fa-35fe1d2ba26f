import type { Collection } from '@sage/xtrem-core';
import { decorators, SubNodeExtension2 } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremDeclarations from '../index';

@decorators.subNodeExtension2<SalesShipmentLineExtension>({
    extends: () => xtremSales.nodes.SalesShipmentLine,
})
export class SalesShipmentLineExtension extends SubNodeExtension2<xtremSales.nodes.SalesShipmentLine> {
    @decorators.collectionProperty<SalesShipmentLineExtension, 'intrastatDeclarations'>({
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'baseDocumentLine',
        node: () => xtremDeclarations.nodes.IntrastatDeclarationLine,
    })
    readonly intrastatDeclarations: Collection<xtremDeclarations.nodes.IntrastatDeclarationLine>;
}

declare module '@sage/xtrem-sales/lib/nodes/sales-shipment-line' {
    export interface SalesShipmentLine extends SalesShipmentLineExtension {}
}
