import type { Reference, decimal } from '@sage/xtrem-core';
import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';

@decorators.nodeExtension<ItemExtension>({
    extends: () => xtremMasterData.nodes.Item,
})
export class ItemExtension extends NodeExtension<xtremMasterData.nodes.Item> {
    @decorators.referenceProperty<ItemExtension, 'intrastatAdditionalUnit'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
    })
    readonly intrastatAdditionalUnit: Reference<xtremMasterData.nodes.UnitOfMeasure | null>;

    @decorators.decimalProperty<ItemExtension, 'intrastatAdditionalUnitToStockUnitConversion'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.coefficientDataType,
        async control(cx, val) {
            if (val === 0 && (await this.intrastatAdditionalUnit)) {
                cx.error.addLocalized(
                    '@sage/xtrem-declarations/node-extensions__item_extension__intrastat_additional_unit_to_stock_unit_conversion_mandatory',
                    'The intrastat additional unit conversion factor is mandatory.',
                );
            }
        },
    })
    readonly intrastatAdditionalUnitToStockUnitConversion: Promise<decimal>;
}

declare module '@sage/xtrem-master-data/lib/nodes/item' {
    export interface Item extends ItemExtension {}
}
