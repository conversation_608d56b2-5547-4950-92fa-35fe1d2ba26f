import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremDeclarations from '../index';

@decorators.nodeExtension<DeliveryModeExtension>({
    extends: () => xtremMasterData.nodes.DeliveryMode,
})
export class DeliveryModeExtension extends NodeExtension<xtremMasterData.nodes.DeliveryMode> {
    @decorators.enumProperty<DeliveryModeExtension, 'intrastatModeOfTransport'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremDeclarations.enums.intrastatModeOfTransportDataType,
        duplicateRequiresPrompt: true,
    })
    readonly intrastatModeOfTransport: Promise<xtremDeclarations.enums.IntrastatModeOfTransport>;
}

declare module '@sage/xtrem-master-data/lib/nodes/delivery-mode' {
    export interface DeliveryMode extends DeliveryModeExtension {}
}
