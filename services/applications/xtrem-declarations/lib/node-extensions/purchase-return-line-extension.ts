import type { Collection } from '@sage/xtrem-core';
import { decorators, SubNodeExtension4 } from '@sage/xtrem-core';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremDeclarations from '../index';

@decorators.subNodeExtension4<xtremPurchasing.nodes.PurchaseReturnLine>({
    extends: () => xtremPurchasing.nodes.PurchaseReturnLine,
})
export class PurchaseReturnLineExtension extends SubNodeExtension4<xtremPurchasing.nodes.PurchaseReturnLine> {
    @decorators.collectionProperty<PurchaseReturnLineExtension, 'intrastatDeclarations'>({
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'baseDocumentLine',
        node: () => xtremDeclarations.nodes.IntrastatDeclarationLine,
    })
    readonly intrastatDeclarations: Collection<xtremDeclarations.nodes.IntrastatDeclarationLine>;
}

declare module '@sage/xtrem-purchasing/lib/nodes/purchase-return-line' {
    export interface PurchaseReturnLine extends PurchaseReturnLineExtension {}
}
