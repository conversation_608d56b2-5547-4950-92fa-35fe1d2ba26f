import type { Collection } from '@sage/xtrem-core';
import { decorators, SubNodeExtension5 } from '@sage/xtrem-core';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremDeclarations from '../index';

@decorators.subNodeExtension5<PurchaseReceiptLineExtension>({
    extends: () => xtremPurchasing.nodes.PurchaseReceiptLine,
})
export class PurchaseReceiptLineExtension extends SubNodeExtension5<xtremPurchasing.nodes.PurchaseReceiptLine> {
    @decorators.collectionProperty<PurchaseReceiptLineExtension, 'intrastatDeclarations'>({
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'baseDocumentLine',
        node: () => xtremDeclarations.nodes.IntrastatDeclarationLine,
    })
    readonly intrastatDeclarations: Collection<xtremDeclarations.nodes.IntrastatDeclarationLine>;
}

declare module '@sage/xtrem-purchasing/lib/nodes/purchase-receipt-line' {
    export interface PurchaseReceiptLine extends PurchaseReceiptLineExtension {}
}
