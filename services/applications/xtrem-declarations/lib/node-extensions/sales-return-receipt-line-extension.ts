import type { Collection } from '@sage/xtrem-core';
import { decorators, SubNodeExtension2 } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremDeclarations from '../index';

@decorators.subNodeExtension2<xtremSales.nodes.SalesReturnReceiptLine>({
    extends: () => xtremSales.nodes.SalesReturnReceiptLine,
})
export class SalesReturnReceiptLineExtension extends SubNodeExtension2<xtremSales.nodes.SalesReturnReceiptLine> {
    @decorators.collectionProperty<SalesReturnReceiptLineExtension, 'intrastatDeclarations'>({
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'baseDocumentLine',
        node: () => xtremDeclarations.nodes.IntrastatDeclarationLine,
    })
    readonly intrastatDeclarations: Collection<xtremDeclarations.nodes.IntrastatDeclarationLine>;
}

declare module '@sage/xtrem-sales/lib/nodes/sales-return-receipt-line' {
    export interface SalesReturnReceiptLine extends SalesReturnReceiptLineExtension {}
}
