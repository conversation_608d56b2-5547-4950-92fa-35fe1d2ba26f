import { EnumDataType } from '@sage/xtrem-core';

export enum IntrastatModeOfTransportEnum {
    seaTransport = 1,
    railwayTransport = 2,
    roadTransport = 3,
    airTransport = 4,
    postalConsignments = 5,
    fixedTransportInstallations = 7,
    inlandWaterwayTransport = 8,
    ownPropulsion = 9,
}

export type IntrastatModeOfTransport = keyof typeof IntrastatModeOfTransportEnum;

export const intrastatModeOfTransportDataType = new EnumDataType<IntrastatModeOfTransport>({
    enum: IntrastatModeOfTransportEnum,
    filename: __filename,
});
