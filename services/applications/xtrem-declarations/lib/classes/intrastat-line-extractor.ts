import type { NodeCreateData, NodeQueryFilter, decimal, integer } from '@sage/xtrem-core';
import { Logger } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremDeclarations from '../index';

const logger = Logger.getLogger(__filename, 'intrastat-class');

export class IntrastatLineExtractor {
    // The movement rule. It will change depending if we are extracting sales shipment sales return, purchase receipt or purchase return
    private movementRule: xtremDeclarations.nodes.MovementRule;

    // The current intrastat declaration line number. It will increase by 1 every time we add a line to the declaration
    private currentLineNumber = 0;

    // The unit of measure that represents kilograms in the app.
    private kilogramUnitOfMeasure: Promise<xtremMasterData.nodes.UnitOfMeasure> =
        this.intrastatDeclaration.$.context.read(xtremMasterData.nodes.UnitOfMeasure, { id: 'KILOGRAM' });

    // The main filter to apply when querying the sales shipment lines
    private get salesShipmentLinesFilter(): Promise<{
        filter: {
            _or?: NodeQueryFilter<xtremSales.nodes.SalesShipmentLine>[];
            _and: NodeQueryFilter<xtremSales.nodes.SalesShipmentLine>[];
        };
    }> {
        return (async () => {
            return {
                filter: {
                    _and: [
                        {
                            document: {
                                status: 'shipped',
                                site: { legalCompany: { _id: (await this.intrastatDeclaration.company)._id } },
                                shippingDate: { _lte: await this.intrastatDeclaration.date },
                                shipToCustomer: {
                                    businessEntity: {
                                        country: {
                                            _ne: (await (await this.intrastatDeclaration.company).country)._id,
                                            isEuMember: true,
                                        },
                                    },
                                },
                            },
                            item: { type: 'good' },
                            intrastatDeclarations: { _none: true },
                        },
                    ],
                },
            };
        })();
    }

    // The main filter to apply when querying the sales return receipt lines
    private get salesReturnReceiptLinesFilter(): Promise<{
        filter: {
            _or?: NodeQueryFilter<xtremSales.nodes.SalesReturnReceiptLine>[];
            _and: NodeQueryFilter<xtremSales.nodes.SalesReturnReceiptLine>[];
        };
    }> {
        return (async () => {
            return {
                filter: {
                    _and: [
                        {
                            document: {
                                status: 'closed',
                                stockSite: { legalCompany: { _id: (await this.intrastatDeclaration.company)._id } },
                                date: { _lte: await this.intrastatDeclaration.date },
                                shipToCustomer: {
                                    businessEntity: {
                                        country: {
                                            _ne: (await (await this.intrastatDeclaration.company).country)._id,
                                            isEuMember: true,
                                        },
                                    },
                                },
                            },
                            item: { type: 'good' },
                            intrastatDeclarations: { _none: true },
                        },
                    ],
                },
            };
        })();
    }

    // The main filter to apply when querying the purchase receipt lines
    private get purchaseReceiptLinesFilter(): Promise<{
        filter: {
            _or?: NodeQueryFilter<xtremPurchasing.nodes.PurchaseReceiptLine>[];
            _and: NodeQueryFilter<xtremPurchasing.nodes.PurchaseReceiptLine>[];
        };
    }> {
        return (async () => {
            return {
                filter: {
                    _and: [
                        {
                            document: {
                                status: { _ne: 'draft' },
                                site: { legalCompany: { _id: (await this.intrastatDeclaration.company)._id } },
                                receiptDate: { _lte: await this.intrastatDeclaration.date },
                                billBySupplier: {
                                    businessEntity: {
                                        country: {
                                            _ne: (await (await this.intrastatDeclaration.company).country)._id,
                                            isEuMember: true,
                                        },
                                    },
                                },
                            },
                            item: { type: 'good' },
                            intrastatDeclarations: { _none: true },
                        },
                    ],
                },
            };
        })();
    }

    // The main filter to apply when querying the purchase return lines
    private get purchaseReturnLinesFilter(): Promise<{
        filter: {
            _or?: NodeQueryFilter<xtremPurchasing.nodes.PurchaseReturnLine>[];
            _and: NodeQueryFilter<xtremPurchasing.nodes.PurchaseReturnLine>[];
        };
    }> {
        return (async () => {
            return {
                filter: {
                    _and: [
                        {
                            document: {
                                shippingStatus: 'shipped',
                                returnSite: { legalCompany: { _id: (await this.intrastatDeclaration.company)._id } },
                                returnRequestDate: { _lte: await this.intrastatDeclaration.date },
                                supplier: {
                                    businessEntity: {
                                        country: {
                                            _ne: (await (await this.intrastatDeclaration.company).country)._id,
                                            isEuMember: true,
                                        },
                                    },
                                },
                            },
                            item: { type: 'good' },
                            intrastatDeclarations: { _none: true },
                        },
                    ],
                },
            };
        })();
    }

    constructor(private readonly intrastatDeclaration: xtremDeclarations.nodes.IntrastatDeclaration) {}

    /**
     * Sets the movement rule accordingly to the document type
     * @param documentType: the document type that we are processing
     */
    private async setMovementRule(documentType: xtremDeclarations.enums.IntrastatDocumentType): Promise<void> {
        this.movementRule = await this.intrastatDeclaration.$.context.read(xtremDeclarations.nodes.MovementRule, {
            documentType,
        });
    }

    /**
     * Calculates the net weight
     * @param item: the item
     * @param quantity: the quantity
     */
    private async calculateNetWeight(item: xtremMasterData.nodes.Item, quantity: decimal): Promise<decimal> {
        let netWeight = quantity * (await item.weight);
        if ((await (await item.weightUnit)?.id) !== (await (await this.kilogramUnitOfMeasure).id)) {
            netWeight = Math.round(
                await xtremMasterData.functions.convertFromTo(
                    await item.weightUnit,
                    await this.kilogramUnitOfMeasure,
                    netWeight,
                ),
            );
        }
        netWeight = Math.round(netWeight);
        return netWeight;
    }

    /**
     * Calculates the quantityInIntrastatAdditionalUnit
     * @param item: the item
     * @param quantity: the quantity
     */
    private static async calculateQuantityInIntrastatAdditionalUnit(
        item: xtremMasterData.nodes.Item,
        quantity: decimal,
    ): Promise<decimal | undefined> {
        return (await item.intrastatAdditionalUnit)
            ? Math.round(quantity * (await item.intrastatAdditionalUnitToStockUnitConversion))
            : undefined;
    }

    /**
     * methods related with the data extraction from sales shipments
     */
    private async processSalesShipmentLineInvoiceCreditNotes(
        shipmentLineInvoiceLineCreditLine: xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine,
        salesCreditMemoLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToSalesCreditMemoLine>[],
    ): Promise<{ creditedQuantity: number; creditedAmount: number }> {
        // check if the credit memo is not linked to a sales return
        let creditedQuantity = 0;
        let creditedAmount = 0;

        salesCreditMemoLines.push({
            salesCreditMemoLine: await shipmentLineInvoiceLineCreditLine.document,
        });
        if (
            !(await this.intrastatDeclaration.$.context.query(
                xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine,
                {
                    filter: {
                        document: (await shipmentLineInvoiceLineCreditLine.document)._id,
                    },
                },
            ).length)
        ) {
            // update the invoiced quantity and the invoiced amount
            creditedQuantity += await (await shipmentLineInvoiceLineCreditLine.document).quantity;
            creditedAmount += await (
                await shipmentLineInvoiceLineCreditLine.document
            ).amountExcludingTaxInCompanyCurrency;
        }

        return { creditedQuantity, creditedAmount };
    }

    private async processSalesShipmentLineInvoices(
        shipmentLineInvoiceLine: xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine,
        toInvoiceLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToSalesInvoiceLine>[],
        salesCreditMemoLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToSalesCreditMemoLine>[],
    ): Promise<{ invoicedQuantity: number; invoicedAmount: number }> {
        // calculate the invoiced quantity and invoiced amount
        let invoicedQuantity = await (await shipmentLineInvoiceLine.document).quantity;
        let invoicedAmount = await (await shipmentLineInvoiceLine.document).amountExcludingTaxInCompanyCurrency;

        toInvoiceLines.push({ salesInvoiceLine: await shipmentLineInvoiceLine.document });
        // get the credit memos linked to this invoice line
        await this.intrastatDeclaration.$.context
            .query(xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine, {
                filter: {
                    linkedDocument: (await shipmentLineInvoiceLine.document)._id,
                    document: {
                        document: {
                            date: {
                                _lte: await this.intrastatDeclaration.date,
                            },
                            status: { _in: ['posted', 'inProgress', 'error'] },
                        },
                    },
                },
            })
            .forEach(async shipmentLineInvoiceLineCreditLine => {
                const { creditedQuantity, creditedAmount } = await this.processSalesShipmentLineInvoiceCreditNotes(
                    shipmentLineInvoiceLineCreditLine,
                    salesCreditMemoLines,
                );
                invoicedQuantity -= creditedQuantity;
                invoicedAmount -= creditedAmount;
            });
        return { invoicedQuantity, invoicedAmount };
    }

    private async extractFromSalesShipmentLines(): Promise<void> {
        // set the movement rule according to the document type
        await this.setMovementRule('salesShipment');

        // get the shipment lines according to the filter
        const salesShipmentLines = this.intrastatDeclaration.$.context.query(
            xtremSales.nodes.SalesShipmentLine,
            await this.salesShipmentLinesFilter,
        );
        await logger.debugAsync(async () => `Extract salesShipmentLines : ${await salesShipmentLines.length}`);

        await salesShipmentLines.forEach(async (shipmentLine: xtremSales.nodes.SalesShipmentLine) => {
            let shipmentLineInvoicedQuantity = 0;
            let shipmentLineInvoicedAmount = 0;

            const toInvoiceLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToSalesInvoiceLine>[] =
                [];
            const salesCreditMemoLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToSalesCreditMemoLine>[] =
                [];

            // get the invoice lines linked to this shipment line
            await this.intrastatDeclaration.$.context
                .query(xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine, {
                    filter: {
                        linkedDocument: shipmentLine._id,
                        document: {
                            document: {
                                invoiceDate: {
                                    _lte: await this.intrastatDeclaration.date,
                                },
                                status: { _in: ['posted', 'inProgress', 'error'] },
                            },
                        },
                    },
                })
                .forEach(async shipmentLineInvoiceLine => {
                    const { invoicedQuantity, invoicedAmount } = await this.processSalesShipmentLineInvoices(
                        shipmentLineInvoiceLine,
                        toInvoiceLines,
                        salesCreditMemoLines,
                    );
                    shipmentLineInvoicedQuantity += invoicedQuantity;
                    shipmentLineInvoicedAmount += invoicedAmount;
                });

            const invoiceStatus =
                shipmentLineInvoicedQuantity === (await shipmentLine.quantity) ? 'invoiced' : 'notInvoiced';

            // Add the line to the declaration, if it meets the conditions
            if (
                !(
                    (await this.movementRule.declarationPeriod) === 'earliestMonth' &&
                    (await this.intrastatDeclaration.date).month ===
                        (await (await shipmentLine.document).shippingDate).month &&
                    (await shipmentLine.quantity) < shipmentLineInvoicedQuantity
                )
            ) {
                this.currentLineNumber += 1;

                const salesSite = await (await shipmentLine.document).site;
                await this.intrastatDeclaration.lines.append({
                    lineNumber: this.currentLineNumber,
                    flow: await this.movementRule.intrastatFlow,
                    commodityCode: await (await shipmentLine.item).commodityCode,
                    country: await (await (await (await shipmentLine.document).shipToCustomer).businessEntity).country,
                    amount:
                        invoiceStatus === 'notInvoiced'
                            ? await shipmentLine.amountExcludingTaxInCompanyCurrency
                            : shipmentLineInvoicedAmount,
                    currency: await (await (await (await shipmentLine.document).site).legalCompany).currency,
                    quantityInIntrastatAdditionalUnit:
                        await xtremDeclarations.classes.IntrastatLineExtractor.calculateQuantityInIntrastatAdditionalUnit(
                            await shipmentLine.item,
                            await shipmentLine.quantityInStockUnit,
                        ),
                    intrastatAdditionalUnit: (await (await shipmentLine.item).intrastatAdditionalUnit) || null,
                    netWeight: await this.calculateNetWeight(
                        await shipmentLine.item,
                        await shipmentLine.quantityInStockUnit,
                    ),
                    statisticalProcedure: (await this.movementRule.statisticalProcedure)?._id || null,
                    natureOfTransaction: (await this.movementRule.natureOfTransaction)._id,
                    modeOfTransport: await (await (await shipmentLine.document).deliveryMode).intrastatModeOfTransport,
                    geographicSubdivision:
                        (await (await salesSite.primaryAddress)?.region)?.length === 2 &&
                        !Number.isNaN(+(await (await salesSite.primaryAddress)?.region)!)
                            ? await (
                                  await salesSite.primaryAddress
                              )?.region
                            : (await (await salesSite.primaryAddress)?.postcode)?.substring(0, 2),
                    countryOfOrigin: (
                        await (
                            await (
                                await (
                                    await shipmentLine.document
                                ).stockSite
                            ).businessEntity
                        ).country
                    )._id,
                    beTaxIdNumber: await (
                        await (
                            await (
                                await shipmentLine.document
                            ).shipToCustomer
                        ).businessEntity
                    ).taxIdNumber,
                    documentType: 'salesShipment',
                    baseDocumentLine: shipmentLine._id,
                    salesShipmentLine: shipmentLine._id,
                    toInvoiceLines,
                    salesCreditMemoLines,
                });
            }
        });
    }

    /**
     * methods related with the data extraction from sales return receipts
     */

    private static async processSalesReturnReceiptLineReturnCreditNotes(
        salesReturnReceiptLineReturnRequestLineCreditLine: xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine,
        salesCreditMemoLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToSalesCreditMemoLine>[],
    ): Promise<{ lineCreditedQuantity: number; lineCreditedAmount: number }> {
        let lineCreditedQuantity = 0;
        let lineCreditedAmount = 0;
        salesCreditMemoLines.push({
            salesCreditMemoLine: await salesReturnReceiptLineReturnRequestLineCreditLine.document,
        });
        // update the credited quantity and the credited amount
        lineCreditedQuantity += await (await salesReturnReceiptLineReturnRequestLineCreditLine.document).quantity;
        lineCreditedAmount += await (
            await salesReturnReceiptLineReturnRequestLineCreditLine.document
        ).amountExcludingTaxInCompanyCurrency;

        return { lineCreditedQuantity, lineCreditedAmount };
    }

    private async processSalesReturnReceiptLineReturnRequests(
        returnRequestLineReturnReceiptLine: xtremSales.nodes.SalesReturnRequestLineToSalesReturnReceiptLine,
        salesCreditMemoLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToSalesCreditMemoLine>[],
    ): Promise<{ creditedQuantity: number; creditedAmount: number }> {
        let creditedQuantity = 0;
        let creditedAmount = 0;

        // get the credit memos linked to this request line
        await this.intrastatDeclaration.$.context
            .query(xtremSales.nodes.SalesReturnRequestLineSalesCreditMemoLine, {
                filter: {
                    linkedDocument: (await returnRequestLineReturnReceiptLine.linkedDocument)._id, // request
                    document: {
                        document: {
                            date: {
                                _lte: await this.intrastatDeclaration.date,
                            },
                            status: { _in: ['posted', 'inProgress', 'error'] },
                        },
                    },
                },
            })
            .forEach(async salesReturnReceiptLineReturnRequestLineCreditLine => {
                const { lineCreditedQuantity, lineCreditedAmount } =
                    await xtremDeclarations.classes.IntrastatLineExtractor.processSalesReturnReceiptLineReturnCreditNotes(
                        salesReturnReceiptLineReturnRequestLineCreditLine,
                        salesCreditMemoLines,
                    );
                creditedQuantity += lineCreditedQuantity;
                creditedAmount += lineCreditedAmount;
            });
        return { creditedQuantity, creditedAmount };
    }

    private async extractFromSalesReturnReceiptLines(): Promise<void> {
        // set the movement rule definition according to the document type
        await this.setMovementRule('salesReturn');

        const filter = await this.salesReturnReceiptLinesFilter;

        // get the shipment lines according to the filter
        const salesReturnReceiptLines = this.intrastatDeclaration.$.context.query(
            xtremSales.nodes.SalesReturnReceiptLine,
            filter,
        );
        await logger.debugAsync(
            async () => `Extract salesReturnReceiptLines : ${await salesReturnReceiptLines.length}`,
        );

        await salesReturnReceiptLines.forEach(
            async (salesReturnReceiptLine: xtremSales.nodes.SalesReturnReceiptLine) => {
                let salesReturnReceiptLineCreditedQuantity = 0;
                let salesReturnReceiptLineCreditedAmount = 0;

                const salesCreditMemoLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToSalesCreditMemoLine>[] =
                    [];

                // get the credit memo lines linked to this return receipt line
                await salesReturnReceiptLine.toReturnRequestLines.forEach(
                    async salesReturnReceiptLineReturnRequestLine => {
                        const { creditedQuantity, creditedAmount } =
                            await this.processSalesReturnReceiptLineReturnRequests(
                                salesReturnReceiptLineReturnRequestLine,
                                salesCreditMemoLines,
                            );
                        salesReturnReceiptLineCreditedQuantity += creditedQuantity;
                        salesReturnReceiptLineCreditedAmount += creditedAmount;
                    },
                );

                const creditedStatus =
                    salesReturnReceiptLineCreditedQuantity === (await salesReturnReceiptLine.quantity)
                        ? 'credited'
                        : 'notCredited';

                // Add the line to the declaration, if it meets the conditions
                if (
                    !(
                        (await this.movementRule.declarationPeriod) === 'earliestMonth' &&
                        (await this.intrastatDeclaration.date).month ===
                            (await (await salesReturnReceiptLine.document).date).month &&
                        (await salesReturnReceiptLine.quantity) < salesReturnReceiptLineCreditedQuantity
                    )
                ) {
                    this.currentLineNumber += 1;

                    const salesShipmentLine = await (
                        await salesReturnReceiptLine.salesShipmentLines.at(0)
                    )?.linkedDocument;
                    const salesReturnLine = await (
                        await salesReturnReceiptLine.toReturnRequestLines.at(0)
                    )?.linkedDocument;

                    const salesSite = await (await salesShipmentLine?.document)?.site;

                    await this.intrastatDeclaration.lines.append({
                        lineNumber: this.currentLineNumber,
                        flow: await this.movementRule.intrastatFlow,
                        commodityCode: await (await salesReturnReceiptLine.item).commodityCode,
                        country: await (
                            await (
                                await (
                                    await salesReturnReceiptLine.document
                                ).shipToCustomer
                            ).businessEntity
                        ).country,
                        amount:
                            creditedStatus === 'notCredited' && salesShipmentLine
                                ? xtremMasterData.sharedFunctions.convertAmount(
                                      (await salesReturnReceiptLine.quantity) * (await salesShipmentLine.netPrice),
                                      await (
                                          await salesShipmentLine?.document
                                      )?.companyFxRate,
                                      await (
                                          await salesShipmentLine?.document
                                      )?.companyFxRateDivisor,
                                      await (
                                          await (
                                              await salesShipmentLine?.document
                                          )?.currency
                                      )?.decimalDigits,
                                      await (
                                          await (
                                              await (
                                                  await (
                                                      await salesShipmentLine?.document
                                                  )?.site
                                              )?.legalCompany
                                          )?.currency
                                      )?.decimalDigits,
                                  )
                                : salesReturnReceiptLineCreditedAmount,
                        currency: await (
                            await (
                                await (
                                    await salesShipmentLine?.document
                                )?.site
                            )?.legalCompany
                        )?.currency,
                        quantityInIntrastatAdditionalUnit:
                            await xtremDeclarations.classes.IntrastatLineExtractor.calculateQuantityInIntrastatAdditionalUnit(
                                await salesReturnReceiptLine.item,
                                await salesReturnReceiptLine.quantityInStockUnit,
                            ),
                        intrastatAdditionalUnit:
                            (await (await salesReturnReceiptLine.item).intrastatAdditionalUnit) || null,
                        netWeight: await this.calculateNetWeight(
                            await salesReturnReceiptLine.item,
                            await salesReturnReceiptLine.quantityInStockUnit,
                        ),
                        statisticalProcedure: (await this.movementRule.statisticalProcedure)?._id || null,
                        natureOfTransaction: (await this.movementRule.natureOfTransaction)._id,
                        modeOfTransport: await (await (
                            await salesReturnLine?.document
                        )?.deliveryMode)!.intrastatModeOfTransport,
                        geographicSubdivision:
                            (await (await salesSite?.primaryAddress)?.region)?.length === 2 &&
                            !Number.isNaN(+(await (await salesSite?.primaryAddress)?.region)!)
                                ? await (
                                      await (
                                          await (
                                              await salesShipmentLine?.document
                                          )?.site
                                      )?.primaryAddress
                                  )?.region
                                : (await (await salesSite?.primaryAddress)?.postcode)?.substring(0, 2),
                        countryOfOrigin: (
                            await (
                                await (
                                    await (
                                        await salesReturnReceiptLine.document
                                    )?.stockSite
                                )?.businessEntity
                            )?.country
                        )?._id,
                        beTaxIdNumber: await (
                            await (
                                await (
                                    await salesReturnReceiptLine.document
                                ).shipToCustomer
                            ).businessEntity
                        ).taxIdNumber,
                        documentType: 'salesReturn',
                        baseDocumentLine: salesReturnReceiptLine._id,
                        salesReturnReceiptLine: salesReturnReceiptLine._id,
                        salesCreditMemoLines,
                    });
                }
            },
        );
    }

    /**
     * methods related with the data extraction from purchase receipts
     */
    private async processPurchaseReceiptLineInvoices(
        receiptLineInvoiceLine: xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseInvoiceLine,
        purchaseInvoiceLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToPurchaseInvoiceLine>[],
        purchaseCreditMemoLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToPurchaseCreditMemoLine>[],
    ): Promise<{ invoicedQuantity: number; invoicedAmount: number }> {
        // calculate the invoiced quantity and invoiced amount
        const receiptInvoiceLine = await receiptLineInvoiceLine.purchaseInvoiceLine;
        let invoicedQuantity = await receiptInvoiceLine.quantity;
        let invoicedAmount = await receiptInvoiceLine.amountExcludingTaxInCompanyCurrency;

        purchaseInvoiceLines.push({ purchaseInvoiceLine: receiptInvoiceLine });
        // get the credit memos linked to this invoice line
        await this.intrastatDeclaration.$.context
            .query(xtremPurchasing.nodes.PurchaseInvoiceLineToPurchaseCreditMemoLine, {
                filter: {
                    purchaseInvoiceLine: receiptInvoiceLine._id,
                    purchaseCreditMemoLine: {
                        document: {
                            creditMemoDate: {
                                _lte: await this.intrastatDeclaration.date,
                            },
                            status: { _in: ['posted', 'inProgress', 'error'] },
                        },
                    },
                },
            })
            .forEach(async receiptLineInvoiceLineCreditLine => {
                const { creditedQuantity, creditedAmount } = await this.processPurchaseReceiptLineInvoiceCreditNotes(
                    receiptLineInvoiceLineCreditLine,
                    purchaseCreditMemoLines,
                );
                invoicedQuantity -= creditedQuantity;
                invoicedAmount -= creditedAmount;
            });
        return { invoicedQuantity, invoicedAmount };
    }

    private async processPurchaseReceiptLineInvoiceCreditNotes(
        receiptLineInvoiceLineCreditLine: xtremPurchasing.nodes.PurchaseInvoiceLineToPurchaseCreditMemoLine,
        purchaseCreditMemoLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToPurchaseCreditMemoLine>[],
    ): Promise<{ creditedQuantity: number; creditedAmount: number }> {
        // check if the credit memo is not linked to a purchase receipt
        let creditedQuantity = 0;
        let creditedAmount = 0;

        const receiptCreditMemoLine = await receiptLineInvoiceLineCreditLine.purchaseCreditMemoLine;
        purchaseCreditMemoLines.push({
            purchaseCreditMemoLine: receiptCreditMemoLine,
        });
        if (
            !(await this.intrastatDeclaration.$.context.query(
                xtremPurchasing.nodes.PurchaseReturnLineToPurchaseCreditMemoLine,
                {
                    filter: {
                        purchaseCreditMemoLine: receiptCreditMemoLine._id,
                    },
                },
            ).length)
        ) {
            // update the invoiced quantity and the invoiced amount
            creditedQuantity += await receiptCreditMemoLine.quantity;
            creditedAmount += await receiptCreditMemoLine.amountExcludingTaxInCompanyCurrency;
        }

        return { creditedQuantity, creditedAmount };
    }

    private async extractFromPurchaseReceiptLines(): Promise<void> {
        // set the movement rule definition according to the document type
        await this.setMovementRule('purchaseReceipt');

        // get the receipt lines according to the filter
        const purchaseReceiptLines = this.intrastatDeclaration.$.context.query(
            xtremPurchasing.nodes.PurchaseReceiptLine,
            await this.purchaseReceiptLinesFilter,
        );
        await logger.debugAsync(async () => `Extract purchaseReceiptLines : ${await purchaseReceiptLines.length}`);

        await purchaseReceiptLines.forEach(async (receiptLine: xtremPurchasing.nodes.PurchaseReceiptLine) => {
            let receiptLineInvoicedQuantity = 0;
            let receiptLineInvoicedAmount = 0;

            const purchaseInvoiceLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToPurchaseInvoiceLine>[] =
                [];
            const purchaseCreditMemoLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToPurchaseCreditMemoLine>[] =
                [];

            // get the invoice lines linked to this receipt line
            await receiptLine.purchaseInvoiceLines
                .filter(async purchaseReceiptLineToPurchaseInvoiceLine => {
                    const receiptInvoiceLineDocument = await (
                        await purchaseReceiptLineToPurchaseInvoiceLine.purchaseInvoiceLine
                    ).document;
                    return (
                        (await receiptInvoiceLineDocument.invoiceDate) <= (await this.intrastatDeclaration.date) &&
                        ['posted', 'inProgress', 'error'].includes(await receiptInvoiceLineDocument.status)
                    );
                })
                .forEach(async purchaseReceiptLineToPurchaseInvoiceLine => {
                    const { invoicedQuantity, invoicedAmount } = await this.processPurchaseReceiptLineInvoices(
                        purchaseReceiptLineToPurchaseInvoiceLine,
                        purchaseInvoiceLines,
                        purchaseCreditMemoLines,
                    );
                    receiptLineInvoicedQuantity += invoicedQuantity;
                    receiptLineInvoicedAmount += invoicedAmount;
                });

            const invoiceStatus =
                receiptLineInvoicedQuantity === (await receiptLine.quantity) ? 'invoiced' : 'notInvoiced';

            // Add the line to the declaration, if it meets the conditions
            const receiptLineDocument = await receiptLine.document;
            if (
                !(
                    (await this.movementRule.declarationPeriod) === 'earliestMonth' &&
                    (await this.intrastatDeclaration.date).month === (await receiptLineDocument.receiptDate).month &&
                    (await receiptLine.quantity) < receiptLineInvoicedQuantity
                )
            ) {
                this.currentLineNumber += 1;
                const receiptLineItem = await receiptLine.item;
                const { stockSite } = receiptLineDocument;
                const stockSitePrimaryAddress = await (await stockSite).primaryAddress;
                await this.intrastatDeclaration.lines.append({
                    lineNumber: this.currentLineNumber,
                    flow: await this.movementRule.intrastatFlow,
                    commodityCode: await receiptLineItem.commodityCode,
                    country: await (await (await receiptLineDocument.supplier).businessEntity).country,
                    amount:
                        invoiceStatus === 'notInvoiced'
                            ? await receiptLine.amountExcludingTaxInCompanyCurrency
                            : receiptLineInvoicedAmount,
                    currency: await (await (await receiptLineDocument.stockSite).legalCompany).currency,
                    quantityInIntrastatAdditionalUnit:
                        await xtremDeclarations.classes.IntrastatLineExtractor.calculateQuantityInIntrastatAdditionalUnit(
                            receiptLineItem,
                            await receiptLine.quantityInStockUnit,
                        ),
                    intrastatAdditionalUnit: (await receiptLineItem.intrastatAdditionalUnit) || null,
                    netWeight: await this.calculateNetWeight(receiptLineItem, await receiptLine.quantity),
                    statisticalProcedure: (await this.movementRule.statisticalProcedure)?._id || null,
                    natureOfTransaction: (await this.movementRule.natureOfTransaction)._id,
                    modeOfTransport: await (
                        await (
                            await receiptLineDocument.supplier
                        ).deliveryMode
                    )?.intrastatModeOfTransport,
                    geographicSubdivision:
                        (await stockSitePrimaryAddress?.region)?.length === 2 &&
                        !Number.isNaN(+(await stockSitePrimaryAddress?.region)!)
                            ? await stockSitePrimaryAddress?.region
                            : (await stockSitePrimaryAddress?.postcode)?.substring(0, 2),
                    countryOfOrigin: (await (await (await stockSite).businessEntity).country)._id,
                    beTaxIdNumber: await (await (await receiptLineDocument.supplier).businessEntity).taxIdNumber,
                    documentType: 'purchaseReceipt',
                    baseDocumentLine: receiptLine._id,
                    purchaseReceiptLine: receiptLine._id,
                    purchaseInvoiceLines,
                    purchaseCreditMemoLines,
                });
            }
        });
    }

    /**
     * methods related with the data extraction from purchase returns
     */
    private static async processPurchaseReturnLineCreditNotes(
        purchaseReturnLineCreditLine: xtremPurchasing.nodes.PurchaseReturnLineToPurchaseCreditMemoLine,
        purchaseCreditMemoLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToPurchaseCreditMemoLine>[],
    ): Promise<{ creditedQuantity: number; creditedAmount: number }> {
        let creditedQuantity = 0;
        let creditedAmount = 0;

        const returnCreditMemoLine = await purchaseReturnLineCreditLine.purchaseCreditMemoLine;
        purchaseCreditMemoLines.push({ purchaseCreditMemoLine: returnCreditMemoLine });
        // update the credited quantity and the credited amount
        creditedQuantity += await returnCreditMemoLine.quantity;
        creditedAmount += await returnCreditMemoLine.amountExcludingTaxInCompanyCurrency;

        return { creditedQuantity, creditedAmount };
    }

    private async extractFromPurchaseReturnLines(): Promise<void> {
        // set the movement rule definition according to the document type
        await this.setMovementRule('purchaseReturn');

        // get the shipment lines according to the filter
        const purchaseReturnLines = this.intrastatDeclaration.$.context.query(
            xtremPurchasing.nodes.PurchaseReturnLine,
            await this.purchaseReturnLinesFilter,
        );
        await logger.debugAsync(async () => `Extract purchaseReturnLines : ${await purchaseReturnLines.length}`);

        await purchaseReturnLines.forEach(async (purchaseReturnLine: xtremPurchasing.nodes.PurchaseReturnLine) => {
            let purchaseReturnLineCreditedQuantity = 0;
            let purchaseReturnLineCreditedAmount = 0;

            const purchaseCreditMemoLines: NodeCreateData<xtremDeclarations.nodes.IntrastatDeclarationLineToPurchaseCreditMemoLine>[] =
                [];

            // get the credit memo lines linked to this return line
            await purchaseReturnLine.purchaseCreditMemoLines.forEach(async purchaseCreditMemoLine => {
                const { creditedQuantity, creditedAmount } =
                    await xtremDeclarations.classes.IntrastatLineExtractor.processPurchaseReturnLineCreditNotes(
                        purchaseCreditMemoLine,
                        purchaseCreditMemoLines,
                    );
                purchaseReturnLineCreditedQuantity += creditedQuantity;
                purchaseReturnLineCreditedAmount += creditedAmount;
            });

            const creditedStatus =
                purchaseReturnLineCreditedQuantity === (await purchaseReturnLine.quantity) ? 'credited' : 'notCredited';

            // Add the line to the declaration, if it meets the conditions
            const returnLineDocument = await purchaseReturnLine.document;
            if (
                !(
                    (await this.movementRule.declarationPeriod) === 'earliestMonth' &&
                    (await this.intrastatDeclaration.date).month ===
                        (await returnLineDocument.returnRequestDate).month &&
                    (await purchaseReturnLine.quantity) < purchaseReturnLineCreditedQuantity
                )
            ) {
                this.currentLineNumber += 1;

                const { returnSite } = returnLineDocument;
                const returnLineItem = await purchaseReturnLine.item;
                const returnSitePrimaryAddress = await (await returnSite).primaryAddress;
                await this.intrastatDeclaration.lines.append({
                    lineNumber: this.currentLineNumber,
                    flow: await this.movementRule.intrastatFlow,
                    commodityCode: await returnLineItem.commodityCode,
                    country: await (await (await returnLineDocument.supplier).businessEntity).country,
                    amount:
                        creditedStatus === 'notCredited'
                            ? await purchaseReturnLine.amountExcludingTaxInCompanyCurrency
                            : purchaseReturnLineCreditedAmount,
                    currency: await (await (await returnLineDocument.returnSite).legalCompany).currency,
                    quantityInIntrastatAdditionalUnit:
                        await xtremDeclarations.classes.IntrastatLineExtractor.calculateQuantityInIntrastatAdditionalUnit(
                            returnLineItem,
                            await purchaseReturnLine.quantityInStockUnit,
                        ),
                    intrastatAdditionalUnit: (await returnLineItem.intrastatAdditionalUnit) || null,
                    netWeight: await this.calculateNetWeight(
                        returnLineItem,
                        await purchaseReturnLine.quantityInStockUnit,
                    ),
                    statisticalProcedure: (await this.movementRule.statisticalProcedure)?._id || null,
                    natureOfTransaction: (await this.movementRule.natureOfTransaction)._id,
                    modeOfTransport: await (
                        await (
                            await returnLineDocument.supplier
                        ).deliveryMode
                    )?.intrastatModeOfTransport,
                    geographicSubdivision:
                        (await returnSitePrimaryAddress?.region)?.length === 2 &&
                        !Number.isNaN(+(await returnSitePrimaryAddress?.region)!)
                            ? (await returnSitePrimaryAddress?.region)!
                            : (await returnSitePrimaryAddress?.postcode)?.substring(0, 2),
                    countryOfOrigin: (await (await (await returnSite).businessEntity).country)._id,
                    beTaxIdNumber: await (await (await returnLineDocument.supplier).businessEntity).taxIdNumber,
                    documentType: 'purchaseReturn',
                    baseDocumentLine: purchaseReturnLine._id,
                    purchaseReturnLine: purchaseReturnLine._id,
                    purchaseCreditMemoLines,
                });
            }
        });
    }

    public async extract(): Promise<integer> {
        await this.intrastatDeclaration.$.set({ lines: [] });
        await this.intrastatDeclaration.$.save(); // we are in the same transaction why are we saving there ?
        await this.extractFromSalesShipmentLines();
        logger.debug(() => `${this.currentLineNumber} SalesShipmentLines extracted`);
        await this.extractFromSalesReturnReceiptLines();
        logger.debug(() => `${this.currentLineNumber} SalesReturnReceiptLines extracted`);
        await this.extractFromPurchaseReceiptLines();
        logger.debug(() => `${this.currentLineNumber} PurchaseReceiptLines extracted`);
        await this.extractFromPurchaseReturnLines();
        logger.debug(() => `${this.currentLineNumber} PurchaseReturnLines extracted`);
        await this.intrastatDeclaration.$.set({ status: 'draft' });
        await this.intrastatDeclaration.$.save();
        logger.debug(
            () => `${this.currentLineNumber} Intrastat lines extracted
        line lenght : ${this.intrastatDeclaration.lines.length}`,
        );
        return this.intrastatDeclaration.lines.length;
    }
}
