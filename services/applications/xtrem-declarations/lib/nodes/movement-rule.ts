import type { Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremDeclarations from '../index';

@decorators.node<MovementRule>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    indexes: [{ orderBy: { documentType: +1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
export class MovementRule extends Node {
    @decorators.booleanProperty<MovementRule, 'isActive'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        defaultValue: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.enumProperty<MovementRule, 'documentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremDeclarations.enums.intrastatDocumentTypeDataType,
    })
    readonly documentType: Promise<xtremDeclarations.enums.IntrastatDocumentType>;

    @decorators.referenceProperty<MovementRule, 'statisticalProcedure'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremDeclarations.nodes.StatisticalProcedure,
    })
    readonly statisticalProcedure: Reference<xtremDeclarations.nodes.StatisticalProcedure | null>;

    @decorators.referenceProperty<MovementRule, 'natureOfTransaction'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremDeclarations.nodes.NatureOfTransaction,
    })
    readonly natureOfTransaction: Reference<xtremDeclarations.nodes.NatureOfTransaction>;

    @decorators.enumProperty<MovementRule, 'intrastatFlow'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremDeclarations.enums.intrastatFlowDataType,
    })
    readonly intrastatFlow: Promise<xtremDeclarations.enums.IntrastatFlow>;

    @decorators.enumProperty<MovementRule, 'declarationPeriod'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremDeclarations.enums.intrastatDeclarationPeriodDataType,
    })
    readonly declarationPeriod: Promise<xtremDeclarations.enums.IntrastatDeclarationPeriod>;
}
