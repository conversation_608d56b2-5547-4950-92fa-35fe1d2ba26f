import type { Reference } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremDeclarations from '../index';

@decorators.node<IntrastatDeclarationLineToPurchaseCreditMemoLine>({
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
})
export class IntrastatDeclarationLineToPurchaseCreditMemoLine extends Node {
    @decorators.referenceProperty<IntrastatDeclarationLineToPurchaseCreditMemoLine, 'intrastatDeclarationLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremDeclarations.nodes.IntrastatDeclarationLine,
    })
    readonly intrastatDeclarationLine: Reference<xtremDeclarations.nodes.IntrastatDeclarationLine>;

    @decorators.referenceProperty<IntrastatDeclarationLineToPurchaseCreditMemoLine, 'purchaseCreditMemoLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremPurchasing.nodes.PurchaseCreditMemoLine,
    })
    readonly purchaseCreditMemoLine: Reference<xtremPurchasing.nodes.PurchaseCreditMemoLine>;
}
