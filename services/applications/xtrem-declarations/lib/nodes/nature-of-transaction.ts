import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.node<NatureOfTransaction>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canCreate: true,
    indexes: [{ orderBy: { id: +1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
export class NatureOfTransaction extends Node {
    @decorators.stringProperty<NatureOfTransaction, 'id'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.id,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<NatureOfTransaction, 'name'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.booleanProperty<NatureOfTransaction, 'isActive'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        defaultValue: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
    })
    readonly isActive: Promise<boolean>;
}
