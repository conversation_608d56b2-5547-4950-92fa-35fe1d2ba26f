import type { Reference } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremDeclarations from '../index';

@decorators.node<IntrastatDeclarationLineToSalesCreditMemoLine>({
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
})
export class IntrastatDeclarationLineToSalesCreditMemoLine extends Node {
    @decorators.referenceProperty<IntrastatDeclarationLineToSalesCreditMemoLine, 'intrastatDeclarationLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremDeclarations.nodes.IntrastatDeclarationLine,
    })
    readonly intrastatDeclarationLine: Reference<xtremDeclarations.nodes.IntrastatDeclarationLine>;

    @decorators.referenceProperty<IntrastatDeclarationLineToSalesCreditMemoLine, 'salesCreditMemoLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSales.nodes.SalesCreditMemoLine,
    })
    readonly salesCreditMemoLine: Reference<xtremSales.nodes.SalesCreditMemoLine>;
}
