import type { Reference } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremDeclarations from '../index';

@decorators.node<IntrastatDeclarationLineToSalesInvoiceLine>({
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
})
export class IntrastatDeclarationLineToSalesInvoiceLine extends Node {
    @decorators.referenceProperty<IntrastatDeclarationLineToSalesInvoiceLine, 'intrastatDeclarationLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremDeclarations.nodes.IntrastatDeclarationLine,
    })
    readonly intrastatDeclarationLine: Reference<xtremDeclarations.nodes.IntrastatDeclarationLine>;

    @decorators.referenceProperty<IntrastatDeclarationLineToSalesInvoiceLine, 'salesInvoiceLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSales.nodes.SalesInvoiceLine,
    })
    readonly salesInvoiceLine: Reference<xtremSales.nodes.SalesInvoiceLine>;
}
