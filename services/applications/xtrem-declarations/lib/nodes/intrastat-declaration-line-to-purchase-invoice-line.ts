import type { Reference } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremDeclarations from '../index';

@decorators.node<IntrastatDeclarationLineToPurchaseInvoiceLine>({
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
})
export class IntrastatDeclarationLineToPurchaseInvoiceLine extends Node {
    @decorators.referenceProperty<IntrastatDeclarationLineToPurchaseInvoiceLine, 'intrastatDeclarationLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremDeclarations.nodes.IntrastatDeclarationLine,
    })
    readonly intrastatDeclarationLine: Reference<xtremDeclarations.nodes.IntrastatDeclarationLine>;

    @decorators.referenceProperty<IntrastatDeclarationLineToPurchaseInvoiceLine, 'purchaseInvoiceLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremPurchasing.nodes.PurchaseInvoiceLine,
    })
    readonly purchaseInvoiceLine: Reference<xtremPurchasing.nodes.PurchaseInvoiceLine>;
}
