import type { Collection, date, decimal, Reference } from '@sage/xtrem-core';
import { decorators, Node, StringDataType, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremDeclarations from '../index';

@decorators.node<IntrastatDeclarationLine>({
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    indexes: [{ orderBy: { baseDocumentLine: +1 } }],
})
export class IntrastatDeclarationLine extends Node {
    @decorators.referenceProperty<IntrastatDeclarationLine, 'intrastatDeclaration'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isVitalParent: true,
        node: () => xtremDeclarations.nodes.IntrastatDeclaration,
    })
    readonly intrastatDeclaration: Reference<xtremDeclarations.nodes.IntrastatDeclaration>;

    @decorators.decimalProperty<IntrastatDeclarationLine, 'lineNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
    })
    readonly lineNumber: Promise<decimal>;

    @decorators.enumProperty<IntrastatDeclarationLine, 'flow'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremDeclarations.enums.intrastatFlowDataType,
    })
    readonly flow: Promise<xtremDeclarations.enums.IntrastatFlow>;

    /** COPIED FROM ITEM:
     *
     *  6 Digit : HS Codes
     *  8 Digit : Intrastat commodity codes
     *  10 Digit : importation of goods from outside the EU
     */
    @decorators.stringProperty<IntrastatDeclarationLine, 'commodityCode'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.code,
        control(cx, val) {
            if (val && !xtremMasterData.sharedFunctions.validateCommodityCode(val)) {
                cx.error.addLocalized(
                    '@sage/xtrem-declarations/nodes__intrastat_declaration_line__commodity_code_format',
                    'Use the commodity code format: {{format}}',
                    {
                        format: xtremMasterData.sharedFunctions.commodityCodeFormat,
                    },
                );
            }
        },
    })
    readonly commodityCode: Promise<string>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'country'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremStructure.nodes.Country,
    })
    readonly country: Reference<xtremStructure.nodes.Country | null>;

    @decorators.decimalProperty<IntrastatDeclarationLine, 'amount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.amountDataType,
    })
    readonly amount: Promise<decimal>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'currency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Currency,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<IntrastatDeclarationLine, 'netWeight'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
    })
    readonly netWeight: Promise<decimal>;

    @decorators.decimalProperty<IntrastatDeclarationLine, 'quantityInIntrastatAdditionalUnit'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
    })
    readonly quantityInIntrastatAdditionalUnit: Promise<decimal>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'intrastatAdditionalUnit'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
    })
    readonly intrastatAdditionalUnit: Reference<xtremMasterData.nodes.UnitOfMeasure | null>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'statisticalProcedure'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremDeclarations.nodes.StatisticalProcedure,
    })
    readonly statisticalProcedure: Reference<xtremDeclarations.nodes.StatisticalProcedure | null>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'natureOfTransaction'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremDeclarations.nodes.NatureOfTransaction,
    })
    readonly natureOfTransaction: Reference<xtremDeclarations.nodes.NatureOfTransaction | null>;

    @decorators.enumProperty<IntrastatDeclarationLine, 'modeOfTransport'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremDeclarations.enums.intrastatModeOfTransportDataType,
    })
    readonly modeOfTransport: Promise<xtremDeclarations.enums.IntrastatModeOfTransport | null>;

    @decorators.stringProperty<IntrastatDeclarationLine, 'geographicSubdivision'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => new StringDataType({ maxLength: 2 }),
    })
    readonly geographicSubdivision: Promise<string>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'countryOfOrigin'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremStructure.nodes.Country,
    })
    readonly countryOfOrigin: Reference<xtremStructure.nodes.Country | null>;

    /**
     * Tax identification /  Intracom tax Id
     */
    @decorators.stringProperty<IntrastatDeclarationLine, 'beTaxIdNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'custom',
        anonymizeValue(value) {
            return xtremStructure.functions.anonymizeTaxId(value);
        },
        dataType: () => xtremStructure.dataTypes.taxIdentificationDataType,
        async control(cx, value) {
            if (value.length > 0) {
                const countryId = await (await this.country)?.id;
                if (countryId) {
                    // DNE (TODO): Check with PM if necessary and how to get legalEntity value.
                    // if (this.country?.id === 'FR' && this.legalEntity === 'corporation') {
                    //     xtremStructure.functions.frCountryControl(this.$.context, cx, value);
                    // }
                    if (value && !xtremMasterData.sharedFunctions.validTaxIdentification(countryId, value)) {
                        cx.error.addLocalized(
                            '@sage/xtrem-declarations/nodes__intrastat_decalartioni__not-a-valid-tax-id',
                            'Use the tax ID format: {{format}}',
                            {
                                format: xtremMasterData.sharedFunctions
                                    .exampleTaxIdentification(countryId)
                                    .join(` ${xtremMasterData.functions.orText(this.$.context)} `),
                            },
                        );
                    }
                }
            }
        },
    })
    readonly beTaxIdNumber: Promise<string>;

    @decorators.enumProperty<IntrastatDeclarationLine, 'documentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremDeclarations.enums.intrastatDocumentTypeDataType,
    })
    readonly documentType: Promise<xtremDeclarations.enums.IntrastatDocumentType>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'purchaseReceiptLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremPurchasing.nodes.PurchaseReceiptLine,
    })
    readonly purchaseReceiptLine: Reference<xtremPurchasing.nodes.PurchaseReceiptLine | null>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'purchaseReturnLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremPurchasing.nodes.PurchaseReturnLine,
    })
    readonly purchaseReturnLine: Reference<xtremPurchasing.nodes.PurchaseReturnLine | null>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'salesShipmentLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSales.nodes.SalesShipmentLine,
    })
    readonly salesShipmentLine: Reference<xtremSales.nodes.SalesShipmentLine | null>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'salesReturnReceiptLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremSales.nodes.SalesReturnReceiptLine,
    })
    readonly salesReturnReceiptLine: Reference<xtremSales.nodes.SalesReturnReceiptLine | null>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'baseDocumentLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        allowedInUniqueIndex: true,
        node: () => xtremMasterData.nodes.BaseDocumentLine,
    })
    readonly baseDocumentLine: Reference<xtremMasterData.nodes.BaseDocumentLine | null>;

    // =================================================
    // Computed columns

    @decorators.dateProperty<IntrastatDeclarationLine, 'documentDate'>({
        isPublished: true,
        lookupAccess: true,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            if (await this.purchaseReceiptLine) {
                return (await (await (await this.purchaseReceiptLine)?.document)?.receiptDate) || null;
            }
            if (await this.purchaseReturnLine) {
                return (await (await (await this.purchaseReturnLine)?.document)?.returnRequestDate) || null;
            }
            if (await this.salesShipmentLine) {
                return (await (await (await this.salesShipmentLine)?.document)?.shippingDate) || null;
            }
            if (await this.salesReturnReceiptLine) {
                return (await (await (await this.salesReturnReceiptLine)?.document)?.date) || null;
            }
            return null; // Fallback that should never be executed but without it the linter complains.
        },
    })
    readonly documentDate: Promise<date | null>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'businessEntity'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.BusinessEntity,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            if (await this.purchaseReceiptLine) {
                return (
                    (await (await (await (await this.purchaseReceiptLine)?.document)?.supplier)?.businessEntity) || null
                );
            }
            if (await this.purchaseReturnLine) {
                return (
                    (await (await (await (await this.purchaseReturnLine)?.document)?.supplier)?.businessEntity) || null
                );
            }
            if (await this.salesShipmentLine) {
                return (
                    (await (await (await (await this.salesShipmentLine)?.document)?.shipToCustomer)?.businessEntity) ||
                    null
                );
            }
            if (await this.salesReturnReceiptLine) {
                return (
                    (await (
                        await (
                            await (
                                await this.salesReturnReceiptLine
                            )?.document
                        )?.shipToCustomer
                    )?.businessEntity) || null
                );
            }
            return null;
        },
    })
    readonly businessEntity: Reference<xtremMasterData.nodes.BusinessEntity | null>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'item'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Item,
        async getValue() {
            if (await this.purchaseReceiptLine) {
                return (await (await this.purchaseReceiptLine)?.item) ?? null;
            }
            if (await this.purchaseReturnLine) {
                return (await (await this.purchaseReturnLine)?.item) ?? null;
            }
            if (await this.salesShipmentLine) {
                return (await (await this.salesShipmentLine)?.item) ?? null;
            }
            if (await this.salesReturnReceiptLine) {
                return (await (await this.salesReturnReceiptLine)?.item) ?? null;
            }
            return null;
        },
    })
    readonly item: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.decimalProperty<IntrastatDeclarationLine, 'quantity'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.quantityInUnit,
        async getValue() {
            if (await this.purchaseReceiptLine) {
                return (await (await this.purchaseReceiptLine)?.quantity) ?? null;
            }
            if (await this.purchaseReturnLine) {
                return (await (await this.purchaseReturnLine)?.quantity) ?? null;
            }
            if (await this.salesShipmentLine) {
                return (await (await this.salesShipmentLine)?.quantity) ?? null;
            }
            if (await this.salesReturnReceiptLine) {
                return (await (await this.salesReturnReceiptLine)?.quantity) ?? null;
            }
            return null;
        },
    })
    readonly quantity: Promise<decimal | null>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'salesPurchaseUnit'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        async getValue() {
            if (await this.purchaseReceiptLine) {
                return (await (await this.purchaseReceiptLine)?.unit) ?? null;
            }
            if (await this.purchaseReturnLine) {
                return (await (await this.purchaseReturnLine)?.unit) ?? null;
            }
            if (await this.salesShipmentLine) {
                return (await (await this.salesShipmentLine)?.unit) ?? null;
            }
            if (await this.salesReturnReceiptLine) {
                return (await (await this.salesReturnReceiptLine)?.unit) ?? null;
            }
            return null;
        },
    })
    readonly salesPurchaseUnit: Reference<xtremMasterData.nodes.UnitOfMeasure | null>;

    @decorators.decimalProperty<IntrastatDeclarationLine, 'netPrice'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
        async computeValue() {
            if (await this.purchaseReceiptLine) {
                return (await (await this.purchaseReceiptLine)?.netPrice) || null;
            }
            if (await this.purchaseReturnLine) {
                return (await (await this.purchaseReturnLine)?.netPrice) || null;
            }
            if (await this.salesShipmentLine) {
                return (await (await this.salesShipmentLine)?.netPrice) || null;
            }
            if (await this.salesReturnReceiptLine) {
                // For we do not have pricing information on a sales return receipt line we use it from the
                // linked sales shipment line.
                return (
                    (await (
                        await (
                            await (await this.salesReturnReceiptLine)?.salesShipmentLines?.at(0)
                        )?.linkedDocument
                    )?.netPrice) ?? null
                );
            }
            return null;
        },
    })
    readonly netPrice: Promise<decimal | null>;

    @decorators.decimalProperty<IntrastatDeclarationLine, 'totalExcludingTax'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            if (await this.purchaseReceiptLine) {
                return (await (await this.purchaseReceiptLine)?.amountExcludingTax) || null;
            }
            if (await this.purchaseReturnLine) {
                return (await (await this.purchaseReturnLine)?.amountExcludingTax) || null;
            }
            if (await this.salesShipmentLine) {
                return (await (await this.salesShipmentLine)?.amountExcludingTax) || null;
            }
            const salesReturnReceiptLine = await this.salesReturnReceiptLine;
            const netPrice = await this.netPrice;
            const quantity = await salesReturnReceiptLine?.quantity;
            if (salesReturnReceiptLine && netPrice && quantity) {
                // For we do not have pricing information on a sales return receipt line we have to calculate it
                // using the net price informaton from sales shipment line.
                return netPrice * quantity;
            }
            return null;
        },
    })
    readonly totalExcludingTax: Promise<decimal | null>;

    @decorators.decimalProperty<IntrastatDeclarationLine, 'totalExcludingTaxInCompanyCurrency'>({
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
        async computeValue() {
            if (await this.purchaseReceiptLine) {
                return (await (await this.purchaseReceiptLine)?.amountExcludingTaxInCompanyCurrency) || null;
            }
            if (await this.purchaseReturnLine) {
                return (await (await this.purchaseReturnLine)?.amountExcludingTaxInCompanyCurrency) || null;
            }
            if (await this.salesShipmentLine) {
                return (await (await this.salesShipmentLine)?.amountExcludingTaxInCompanyCurrency) || null;
            }
            const salesReturnReceiptLine = await this.salesReturnReceiptLine;
            const totalExcludingTax = await this.totalExcludingTax;
            if (salesReturnReceiptLine && totalExcludingTax) {
                // For we do not have pricing information on a sales return receipt line we have to calculate it
                // using the net price informaton from sales shipment line.
                const companyFxRate = await (
                    await (
                        await (
                            await salesReturnReceiptLine.salesShipmentLines?.at(0)
                        )?.linkedDocument
                    )?.document
                )?.companyFxRate;
                const companyFxRateDivisor = await (
                    await (
                        await (
                            await salesReturnReceiptLine.salesShipmentLines?.at(0)
                        )?.linkedDocument
                    )?.document
                )?.companyFxRateDivisor;

                if (companyFxRate && companyFxRateDivisor) {
                    return xtremMasterData.sharedFunctions.convertAmount(
                        totalExcludingTax,
                        companyFxRate,
                        companyFxRateDivisor,
                        await (
                            await this.currency
                        ).decimalDigits,
                        await (
                            await this.companyCurrency
                        ).decimalDigits,
                    );
                }
            }
            return null;
        },
    })
    readonly totalExcludingTaxInCompanyCurrency: Promise<decimal | null>;

    @decorators.referenceProperty<IntrastatDeclarationLine, 'companyCurrency'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await (await this.intrastatDeclaration).company).currency;
        },
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    // We pack the purchase invoice lines in an object property to be used as JSON on the side panel of the page.
    @decorators.jsonProperty<IntrastatDeclarationLine, 'computedPurchaseInvoiceLines'>({
        isPublished: true,
        lookupAccess: true,
        async computeValue() {
            return {
                purchaseInvoiceLines: await this.purchaseInvoiceLines
                    .map(async purchaseInvoiceLine => {
                        const invoiceLineDocument = await (await purchaseInvoiceLine.purchaseInvoiceLine).document;
                        return {
                            _id: (await purchaseInvoiceLine.purchaseInvoiceLine)._id,
                            document: invoiceLineDocument,
                            documentNumber: await invoiceLineDocument.number,
                            documentDate: await invoiceLineDocument.invoiceDate,
                            quantity: await (await purchaseInvoiceLine.purchaseInvoiceLine).quantity,
                            unit: await (await purchaseInvoiceLine.purchaseInvoiceLine).unit,
                            amountExcludingTax: await (
                                await purchaseInvoiceLine.purchaseInvoiceLine
                            ).amountExcludingTax,
                            companyCurrency: await this.companyCurrency,
                        };
                    })
                    .toArray(),
            };
        },
    })
    readonly computedPurchaseInvoiceLines: Promise<object>;

    // We pack the purchase invoice lines in an object property to be used as JSON on the side panel of the page.
    @decorators.jsonProperty<IntrastatDeclarationLine, 'computedPurchaseCreditMemoLines'>({
        isPublished: true,
        lookupAccess: true,
        async computeValue() {
            return {
                purchaseCreditMemoLines: await this.purchaseCreditMemoLines
                    .map(async purchaseCreditMemoLine => {
                        const creditMemoLineDocument = await (
                            await purchaseCreditMemoLine.purchaseCreditMemoLine
                        ).document;
                        return {
                            _id: (await purchaseCreditMemoLine.purchaseCreditMemoLine)._id,
                            document: creditMemoLineDocument,
                            documentNumber: await creditMemoLineDocument.number,
                            documentDate: await creditMemoLineDocument.creditMemoDate,
                            quantity: await (await purchaseCreditMemoLine.purchaseCreditMemoLine).quantity,
                            unit: await (await purchaseCreditMemoLine.purchaseCreditMemoLine).unit,
                            amountExcludingTax: await (
                                await purchaseCreditMemoLine.purchaseCreditMemoLine
                            ).amountExcludingTax,
                            companyCurrency: await this.companyCurrency,
                        };
                    })
                    .toArray(),
            };
        },
    })
    readonly computedPurchaseCreditMemoLines: Promise<object>;

    // We pack the purchase invoice lines in an object property to be used as JSON on the side panel of the page.
    @decorators.jsonProperty<IntrastatDeclarationLine, 'computedSalesInvoiceLines'>({
        isPublished: true,
        lookupAccess: true,
        async computeValue() {
            return {
                toInvoiceLines: await this.toInvoiceLines
                    .map(async salesInvoiceLine => {
                        return {
                            _id: (await salesInvoiceLine.salesInvoiceLine)._id,
                            document: await (await salesInvoiceLine.salesInvoiceLine).document,
                            documentNumber: await (await (await salesInvoiceLine.salesInvoiceLine).document).number,
                            documentDate: await (await (await salesInvoiceLine.salesInvoiceLine).document).invoiceDate,
                            quantity: await (await salesInvoiceLine.salesInvoiceLine).quantity,
                            unit: await (await salesInvoiceLine.salesInvoiceLine).unit,
                            amountExcludingTax: await (await salesInvoiceLine.salesInvoiceLine).amountExcludingTax,
                            companyCurrency: await this.companyCurrency,
                        };
                    })
                    .toArray(),
            };
        },
    })
    readonly computedSalesInvoiceLines: Promise<object>;

    // We pack the purchase invoice lines in an object property to be used as JSON on the side panel of the page.
    @decorators.jsonProperty<IntrastatDeclarationLine, 'computedSalesCreditMemoLines'>({
        isPublished: true,
        lookupAccess: true,
        async computeValue() {
            return {
                salesCreditMemoLines: await this.salesCreditMemoLines
                    .map(async salesCreditMemoLine => {
                        return {
                            _id: (await salesCreditMemoLine.salesCreditMemoLine)._id,
                            document: await (await salesCreditMemoLine.salesCreditMemoLine).document,
                            documentNumber: await (
                                await (
                                    await salesCreditMemoLine.salesCreditMemoLine
                                ).document
                            ).number,
                            documentDate: await (await (await salesCreditMemoLine.salesCreditMemoLine).document).date,
                            quantity: await (await salesCreditMemoLine.salesCreditMemoLine).quantity,
                            unit: await (await salesCreditMemoLine.salesCreditMemoLine).unit,
                            amountExcludingTax: await (
                                await salesCreditMemoLine.salesCreditMemoLine
                            ).amountExcludingTax,
                            companyCurrency: await this.companyCurrency,
                        };
                    })
                    .toArray(),
            };
        },
    })
    readonly computedSalesCreditMemoLines: Promise<object>;

    // =================================================
    // Computed booleans to detect problematic lines

    @decorators.booleanProperty<IntrastatDeclarationLine, 'hasEmptyValue'>({
        isPublished: true,
        lookupAccess: true,
        isStoredOutput: true,
        dependsOn: [
            'commodityCode',
            'country',
            'amount',
            'currency',
            'netWeight',
            'statisticalProcedure',
            'natureOfTransaction',
            'modeOfTransport',
            'geographicSubdivision',
            'countryOfOrigin',
            'beTaxIdNumber',
        ],

        defaultValue() {
            return this._lineContainsEmptyValues();
        },
        updatedValue: useDefaultValue,
    })
    readonly hasEmptyValue: Promise<boolean>;

    @decorators.booleanProperty<IntrastatDeclarationLine, 'hasWarning'>({
        isPublished: true,
        lookupAccess: true,
        isStoredOutput: true,
        dependsOn: ['purchaseReceiptLine', 'purchaseReturnLine', 'salesShipmentLine', 'salesReturnReceiptLine'],

        defaultValue() {
            return this._lineContainsWarnings();
        },
        updatedValue: useDefaultValue,
    })
    readonly hasWarning: Promise<boolean>;

    // =================================================
    // Collections to the involved document lines.

    @decorators.collectionProperty<IntrastatDeclarationLine, 'purchaseInvoiceLines'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremDeclarations.nodes.IntrastatDeclarationLineToPurchaseInvoiceLine,
        reverseReference: 'intrastatDeclarationLine',
    })
    readonly purchaseInvoiceLines: Collection<xtremDeclarations.nodes.IntrastatDeclarationLineToPurchaseInvoiceLine>;

    @decorators.collectionProperty<IntrastatDeclarationLine, 'purchaseCreditMemoLines'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremDeclarations.nodes.IntrastatDeclarationLineToPurchaseCreditMemoLine,
        reverseReference: 'intrastatDeclarationLine',
    })
    readonly purchaseCreditMemoLines: Collection<xtremDeclarations.nodes.IntrastatDeclarationLineToPurchaseCreditMemoLine>;

    @decorators.collectionProperty<IntrastatDeclarationLine, 'toInvoiceLines'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremDeclarations.nodes.IntrastatDeclarationLineToSalesInvoiceLine,
        reverseReference: 'intrastatDeclarationLine',
    })
    readonly toInvoiceLines: Collection<xtremDeclarations.nodes.IntrastatDeclarationLineToSalesInvoiceLine>;

    @decorators.collectionProperty<IntrastatDeclarationLine, 'salesCreditMemoLines'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremDeclarations.nodes.IntrastatDeclarationLineToSalesCreditMemoLine,
        reverseReference: 'intrastatDeclarationLine',
    })
    readonly salesCreditMemoLines: Collection<xtremDeclarations.nodes.IntrastatDeclarationLineToSalesCreditMemoLine>;

    // =================================================
    // Utility functions (internal use only)

    private async _lineContainsEmptyValues(): Promise<boolean> {
        return (
            !(await this.commodityCode) ||
            (await this.commodityCode) === '' ||
            !(await this.country) ||
            (await this.amount) === 0.0 ||
            !(await this.currency) ||
            (await this.netWeight) === 0.0 ||
            !(await this.statisticalProcedure) ||
            !(await this.natureOfTransaction) ||
            !(await this.modeOfTransport) ||
            (await this.geographicSubdivision) === '' ||
            !(await this.countryOfOrigin) ||
            (await this.beTaxIdNumber) === ''
        );
    }

    private async _lineContainsWarnings(): Promise<boolean> {
        return !(
            (await (await this.purchaseReceiptLine)?.lineInvoiceStatus) === 'invoiced' ||
            (await (await this.purchaseReturnLine)?.lineCreditStatus) === 'credited' ||
            (await (await this.salesShipmentLine)?.invoiceStatus) === 'invoiced' ||
            (await (
                await (
                    await (await this.salesReturnReceiptLine)?.toReturnRequestLines?.at(0)
                )?.linkedDocument
            )?.creditStatus) === 'credited'
        );
    }
}
