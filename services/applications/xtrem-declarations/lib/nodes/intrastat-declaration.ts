import type { Collection, Context, date, integer, Reference } from '@sage/xtrem-core';
import { decorators, Node, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremDeclarations from '../index';

@decorators.node<IntrastatDeclaration>({
    isClearedByReset: true,
    storage: 'sql',
    indexes: [{ orderBy: { number: 1, company: 1 }, isUnique: true }],
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    async saveBegin() {
        // Calculate and apply the new declaration number (by extracting the highest decl. number of a company + 1).
        if ((await this.number) <= 0) {
            const intrastatDeclarationRecords = await this.$.context
                .queryAggregate(IntrastatDeclaration, {
                    filter: {
                        company: (await this.company)._id,
                    },
                    group: {},
                    values: { number: { max: true } },
                })
                .toArray();

            await this.$.set({
                number: intrastatDeclarationRecords[0]?.values.number.max
                    ? intrastatDeclarationRecords[0].values.number.max + 1
                    : 1,
            });
        }
    },
    async controlDelete(cx): Promise<void> {
        if ((await this.status) === 'validated') {
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize(
                    '@sage/xtrem-declarations/nodes__intrastat_declaration__deletion_forbidden_validated',
                    'The current Intrastat declaration cannot be deleted. It is already validated.',
                ),
            );
        }
    },
})
export class IntrastatDeclaration extends Node {
    @decorators.referenceProperty<IntrastatDeclaration, 'company'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        provides: ['company'],
        node: () => xtremSystem.nodes.Company,
        filters: { control: { country: { isEuMember: true } } },
    })
    readonly company: Reference<xtremSystem.nodes.Company>;

    @decorators.integerProperty<IntrastatDeclaration, 'number'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
    })
    readonly number: Promise<integer>;

    @decorators.dateProperty<IntrastatDeclaration, 'date'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        isFrozen: true,
        control(cx, val) {
            if (val.toString() !== val.endOfMonth().toString()) {
                cx.error.addLocalized(
                    '@sage/xtrem-declarations/intrastat_declaration__date__not_end_of_month',
                    'The end of the period is not the end of a month.',
                );
            }
        },
    })
    readonly date: Promise<date>;

    @decorators.enumProperty<IntrastatDeclaration, 'status'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremDeclarations.enums.intrastatDeclarationStatusDataType,
        defaultValue: 'recorded',
    })
    readonly status: Promise<xtremDeclarations.enums.IntrastatDeclarationStatus>;

    @decorators.collectionProperty<IntrastatDeclaration, 'lines'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => xtremDeclarations.nodes.IntrastatDeclarationLine,
        reverseReference: 'intrastatDeclaration',
    })
    readonly lines: Collection<xtremDeclarations.nodes.IntrastatDeclarationLine>;

    @decorators.mutation<typeof IntrastatDeclaration, 'extractLines'>({
        isPublished: true,
        parameters: [
            {
                name: 'intrastatDeclaration',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => IntrastatDeclaration,
            },
        ],
        return: { type: 'integer' },
    })
    static extractLines(_context: Context, intrastatDeclaration: IntrastatDeclaration): Promise<integer> {
        return new xtremDeclarations.classes.IntrastatLineExtractor(intrastatDeclaration).extract();
    }

    @decorators.mutation<typeof IntrastatDeclaration, 'validateDeclaration'>({
        isPublished: true,
        parameters: [
            {
                name: 'intrastatDeclaration',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => IntrastatDeclaration,
            },
        ],
        return: { type: 'integer' },
    })
    static async validateDeclaration(_context: Context, intrastatDeclaration: IntrastatDeclaration): Promise<integer> {
        await intrastatDeclaration.$.set({ status: 'validated' });
        await intrastatDeclaration.$.save();
        return 0;
    }
}
