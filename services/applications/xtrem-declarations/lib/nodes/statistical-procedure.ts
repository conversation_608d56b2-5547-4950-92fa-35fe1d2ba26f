import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremDeclarations from '../index';

@decorators.node<StatisticalProcedure>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canCreate: true,
    indexes: [{ orderBy: { id: +1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
export class StatisticalProcedure extends Node {
    @decorators.stringProperty<StatisticalProcedure, 'id'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.id,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<StatisticalProcedure, 'name'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.booleanProperty<StatisticalProcedure, 'isActive'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        defaultValue: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.enumProperty<StatisticalProcedure, 'intrastatFlow'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremDeclarations.enums.intrastatFlowDataType,
        defaultValue() {
            return 'dispatch';
        },
    })
    readonly intrastatFlow: Promise<xtremDeclarations.enums.IntrastatFlow>;
}
