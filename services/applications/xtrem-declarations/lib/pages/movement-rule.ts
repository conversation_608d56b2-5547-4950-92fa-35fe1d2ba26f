import { GraphApi } from '@sage/xtrem-declarations-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { intrastat } from '../menu-items/intrastat';

@ui.decorators.page<MovementRule>({
    module: 'declarations',
    title: 'Movement rule',
    node: '@sage/xtrem-declarations/MovementRule',
    menuItem: intrastat,
    objectTypeSingular: 'Movement rule',
    objectTypePlural: 'Movement rules',
    idField() {
        return this.documentType;
    },
    priority: 300,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.select({
                bind: 'documentType',
                title: 'Document type',
                optionType: '@sage/xtrem-declarations/IntrastatDocumentType',
            }),
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'Active',
                graphQLFilter: { isActive: true },
            },
        ],
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
    },
})
export class MovementRule extends ui.Page<GraphApi> {
    @ui.decorators.section<MovementRule>({
        title: 'General',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<MovementRule>({
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.switchField<MovementRule>({
        parent() {
            return this.generalBlock;
        },
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<MovementRule>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.dropdownListField<MovementRule>({
        parent() {
            return this.generalBlock;
        },
        title: 'Document type',
        optionType: '@sage/xtrem-declarations/IntrastatDocumentType',
        isReadOnly: true,
    })
    documentType: ui.fields.DropdownList;

    @ui.decorators.separatorField<MovementRule>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    documentTypeSeparator: ui.fields.Separator;

    @ui.decorators.referenceField<MovementRule>({
        parent() {
            return this.generalBlock;
        },
        title: 'Statistical procedure',
        bind: 'statisticalProcedure',
        node: '@sage/xtrem-declarations/StatisticalProcedure',
        width: 'large',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 1,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.dropdownList({ bind: 'intrastatFlow', isHidden: true }),
        ],
    })
    statisticalProcedure: ui.fields.Reference;

    @ui.decorators.referenceField<MovementRule>({
        parent() {
            return this.generalBlock;
        },
        title: 'Nature of transaction',
        bind: 'natureOfTransaction',
        node: '@sage/xtrem-declarations/NatureOfTransaction',
        width: 'large',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 1,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
        ],
    })
    natureOfTransaction: ui.fields.Reference;

    @ui.decorators.separatorField<MovementRule>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    natureOfTransactionSeparator: ui.fields.Separator;

    @ui.decorators.dropdownListField<MovementRule>({
        parent() {
            return this.generalBlock;
        },
        title: 'Flow',
        optionType: '@sage/xtrem-declarations/IntrastatFlow',
    })
    intrastatFlow: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<MovementRule>({
        parent() {
            return this.generalBlock;
        },
        title: 'Declaration period',
        optionType: '@sage/xtrem-declarations/IntrastatDeclarationPeriod',
        width: 'large',
    })
    declarationPeriod: ui.fields.DropdownList;
}
