import { Dict } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';

export function statusColor(status: any) {
    switch (status) {
        case 'recorded':
            return ui.tokens.colorsSemanticInfo150;
        case 'draft':
            return ui.tokens.colorsSemanticInfo500;
        case 'inProgress':
            return ui.tokens.colorsSemanticCaution500;
        case 'pending':
            return ui.tokens.colorsSemanticPositive500;
        case 'closed':
            return ui.tokens.colorsUtilityMajor400;
        case 'validated':
        case 'shipped':
            return ui.tokens.colorsSemanticPositive500;
        default:
            return ui.tokens.colorsSemanticInfo500;
    }
}

export function statusTextColor(status: any) {
    return '#FFFFFF';
}

export function confirmDialog(page: Dict<any>, title: string, message: string) {
    const options: ui.dialogs.DialogOptions = {
        acceptButton: {
            text: ui.localize('@sage/xtrem-declarations/pages__confirm-confirm', 'Confirm'),
        },
        cancelButton: {
            text: ui.localize('@sage/xtrem-declarations/pages__confirm-cancel', 'Cancel'),
        },
    };
    return page.$.dialog
        .confirmation('warn', title, message, options)
        .then(() => true)
        .catch(() => false);
}
