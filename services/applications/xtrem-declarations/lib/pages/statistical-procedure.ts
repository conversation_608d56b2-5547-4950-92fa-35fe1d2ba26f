import { GraphApi } from '@sage/xtrem-declarations-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { intrastat } from '../menu-items/intrastat';

@ui.decorators.page<StatisticalProcedure>({
    module: 'declarations',
    title: 'Statistical procedure',
    objectTypeSingular: 'Statistical procedure',
    objectTypePlural: 'Statistical procedures',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-declarations/StatisticalProcedure',
    menuItem: intrastat,
    priority: 200,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'id' }),
            line2: ui.nestedFields.text({ bind: 'name' }),
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'Active',
                graphQLFilter: { isActive: true },
            },
        ],
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class StatisticalProcedure extends ui.Page<GraphApi> {
    @ui.decorators.section<StatisticalProcedure>({
        title: 'General',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<StatisticalProcedure>({
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.switchField<StatisticalProcedure>({
        parent() {
            return this.generalBlock;
        },
        title: 'Active',
        fetchesDefaults: true,
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<StatisticalProcedure>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.textField<StatisticalProcedure>({
        parent() {
            return this.generalBlock;
        },
        title: 'Name',
        width: 'large',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.textField<StatisticalProcedure>({
        parent() {
            return this.generalBlock;
        },
        title: 'ID',
        width: 'small',
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<StatisticalProcedure>({
        isHidden: true,
        title: '_id',
    })
    _id: ui.fields.Text;

    @ui.decorators.separatorField<StatisticalProcedure>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    flowSeparator: ui.fields.Separator;

    @ui.decorators.dropdownListField<StatisticalProcedure>({
        parent() {
            return this.generalBlock;
        },
        title: 'Flow',
        optionType: '@sage/xtrem-declarations/IntrastatFlow',
    })
    intrastatFlow: ui.fields.DropdownList;
}
