import { GraphApi } from '@sage/xtrem-declarations-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { intrastat } from '../menu-items/intrastat';
@ui.decorators.page<NatureOfTransaction>({
    module: 'declarations',
    title: 'Nature of transaction',
    objectTypeSingular: 'Nature of transaction',
    objectTypePlural: 'Natures of transaction',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-declarations/NatureOfTransaction',
    menuItem: intrastat,
    priority: 100,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'id' }),
            line2: ui.nestedFields.text({ bind: 'name' }),
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'Active',
                graphQLFilter: { isActive: true },
            },
        ],
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class NatureOfTransaction extends ui.Page<GraphApi> {
    @ui.decorators.section<NatureOfTransaction>({
        title: 'General',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<NatureOfTransaction>({
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.switchField<NatureOfTransaction>({
        parent() {
            return this.generalBlock;
        },
        title: 'Active',
        fetchesDefaults: true,
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<NatureOfTransaction>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.textField<NatureOfTransaction>({
        parent() {
            return this.generalBlock;
        },
        title: 'Name',
        width: 'large',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.textField<NatureOfTransaction>({
        parent() {
            return this.generalBlock;
        },
        title: 'ID',
        width: 'small',
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<NatureOfTransaction>({
        isHidden: true,
        title: '_id',
    })
    _id: ui.fields.Text;
}
