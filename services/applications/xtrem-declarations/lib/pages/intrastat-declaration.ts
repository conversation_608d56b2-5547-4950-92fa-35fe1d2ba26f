import {
    GraphApi,
    IntrastatDeclarationLine,
    IntrastatDeclarationStatus,
    NatureOfTransaction,
    StatisticalProcedure,
} from '@sage/xtrem-declarations-api';
import { BusinessEntity, Currency, Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import { PurchaseReceipt, PurchaseReceiptLine, PurchaseReturn, PurchaseReturnLine } from '@sage/xtrem-purchasing-api';
import { SalesReturnReceipt, SalesReturnReceiptLine, SalesShipment, SalesShipmentLine } from '@sage/xtrem-sales-api';
import { Country } from '@sage/xtrem-structure-api';
import { Company } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { intrastat } from '../menu-items/intrastat';
import { confirmDialog, statusColor, statusTextColor } from './shared/page-functions';

interface IntrastatDeclarationLineRow extends IntrastatDeclarationLine {
    linkToDocument: string;
}

@ui.decorators.page<IntrastatDeclaration>({
    title: 'Intrastat declaration',
    objectTypeSingular: 'Intrastat declaration',
    objectTypePlural: 'Intrastat declarations',
    idField() {
        return this.number;
    },
    module: 'finance',
    mode: 'default',
    node: '@sage/xtrem-declarations/IntrastatDeclaration',
    menuItem: intrastat,
    priority: 100,
    businessActions() {
        return [this.$standardCancelAction, this.buttonGenerate, this.buttonValidate, this.$standardSaveAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    createAction() {
        return this.$standardNewAction;
    },
    navigationPanel: {
        orderBy: { date: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.numeric({ bind: 'number', title: 'Number' }),
            line2: ui.nestedFields.reference<IntrastatDeclaration>({
                title: 'Company',
                bind: 'company',
                node: '@sage/xtrem-system/Company',
                tunnelPage: undefined,
                valueField: 'name',
            }),
            line2Right: ui.nestedFields.date({
                bind: 'date',
                title: 'Period',
            }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-declarations/IntrastatDeclarationStatus',
                style(_id, rowData) {
                    return {
                        backgroundColor: statusColor(rowData?.status),
                        borderColor: statusColor(rowData?.status),
                        textColor: statusTextColor(rowData?.status),
                    }
                },
            }),
        },
        // TODO (DNE): Filter options to be specified. For the moment it only occupies space on the screen.
        // optionsMenu: [
        //     {
        //         title: 'All',
        //         graphQLFilter: {},
        //     },
        // ],
    },
    detailPanel() {
        return {
            activeSection: 'detailPanelGeneralSection',
            header: this.detailPanelHeaderSection,
            sections: [this.detailPanelGeneralSection, this.detailPanelDocumentsSection /*this.detailPanelAllSection*/],
            footerActions: [],
            isTitleHidden: true,
        };
    },
    async onLoad() {
        this.$.detailPanel.isHidden = true;

        this.addLine.isDisabled = !this.$.recordId;
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
        this.manageBusinessButtons();
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
        this.manageBusinessButtons();
    },
})
export class IntrastatDeclaration extends ui.Page<GraphApi> {
    @ui.decorators.section<IntrastatDeclaration>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    // =================================================
    // Header block

    @ui.decorators.block<IntrastatDeclaration>({
        parent() {
            return this.mainSection;
        },
        title: 'Extraction criteria',
        width: 'extra-large',
        // REFACTOR (DNE): To be removed or re-actived. Waiting for XT-29161 to be decided.
        // isDisabled(): boolean {
        //     return this.status.value === 'validated';
        // },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.referenceField<IntrastatDeclaration, Company>({
        parent() {
            return this.headerBlock;
        },
        title: 'Company',
        node: '@sage/xtrem-system/Company',
        tunnelPage: '@sage/xtrem-master-data/Company',
        valueField: 'name',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
        ],
        minLookupCharacters: 1,
        placeholder: 'Select company',
        width: 'small',
        isMandatory: false,
    })
    company: ui.fields.Reference;

    @ui.decorators.textField<IntrastatDeclaration>({
        title: 'ID',
        isHidden: true,
        isTitleHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.numericField<IntrastatDeclaration>({
        parent() {
            return this.headerBlock;
        },
        title: 'Number',
        isReadOnly: true,
    })
    number: ui.fields.Numeric;

    @ui.decorators.dateField<IntrastatDeclaration>({
        parent() {
            return this.headerBlock;
        },
        title: 'Period',
    })
    date: ui.fields.Date;

    @ui.decorators.labelField<IntrastatDeclaration>({
        parent() {
            return this.headerBlock;
        },
        optionType: '@sage/xtrem-declarations/IntrastatDeclarationStatus',
        width: 'small',
        style() {
            return {
                backgroundColor: statusColor(this.status.value),
                borderColor: statusColor(this.status.value),
                textColor: statusTextColor(this.status.value),
            };
        },
    })
    status: ui.fields.Label<IntrastatDeclarationStatus>;

    // =================================================
    // Filter criteria block (to be removed?)

    // TODO (DNE): Column filter options to be specified. For the moment it only occupies space on the screen.
    // @ui.decorators.block<IntrastatDeclaration>({
    //     parent() {
    //         return this.mainSection;
    //     },
    //     width: 'extra-large',
    //     title: 'Data filter criteria',
    // })
    // filterBlock: ui.containers.Block;

    // // TODO / REFACTOR (DNE):
    // // Note that the next 2 fields and the setFilter() function are part of a mockup for the criteria section
    // // for at the moment it not yet clear whether we need this section nor which criteria should be available here.

    // @ui.decorators.numericField<IntrastatDeclaration>({
    //     parent() {
    //         return this.filterBlock;
    //     },
    //     title: 'Minimum amount',
    //     isTransient: true,
    //     onChange() {
    //         this.setLineFilter();
    //     },
    // })
    // minAmount: ui.fields.Numeric;

    // @ui.decorators.numericField<IntrastatDeclaration>({
    //     parent() {
    //         return this.filterBlock;
    //     },
    //     title: 'Maximum amount',
    //     isTransient: true,
    //     onChange() {
    //         this.setLineFilter();
    //     },
    // })
    // maxAmount: ui.fields.Numeric;

    // setLineFilter() {
    //     // TODO (DNE): Implement (see comment abvoe).
    // }

    // =================================================
    // Result table block

    @ui.decorators.tableField<IntrastatDeclaration, IntrastatDeclarationLineRow>({
        parent() {
            return this.mainSection;
        },
        title: 'Results',
        node: '@sage/xtrem-declarations/IntrastatDeclarationLine',
        canSelect: false,
        isHelperTextHidden: true,
        bind: 'lines',
        pageSize: 25,
        canExport: true,
        mobileCard: undefined,
        isReadOnly(): boolean {
            return this.status.value === 'validated';
        },

        orderBy: {
            lineNumber: 1,
        },
        columns: [
            ui.nestedFields.technical({
                bind: 'computedPurchaseInvoiceLines',
            }),

            ui.nestedFields.technical({
                bind: 'computedPurchaseCreditMemoLines',
            }),

            ui.nestedFields.technical({
                bind: 'computedSalesInvoiceLines',
            }),

            ui.nestedFields.technical({
                bind: 'computedSalesCreditMemoLines',
            }),

            ui.nestedFields.text({
                bind: '_sortValue',
                isHidden: true,
            }),

            ui.nestedFields.numeric({
                title: 'Line',
                bind: 'lineNumber',
            }),

            ui.nestedFields.dropdownList({
                title: 'Flow',
                bind: 'flow',
                optionType: '@sage/xtrem-declarations/IntrastatFlow',
                isReadOnly: true,
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.text({
                title: 'Commodity code',
                bind: 'commodityCode',
            }),

            ui.nestedFields.reference<IntrastatDeclaration, IntrastatDeclarationLineRow, Country>({
                bind: 'country',
                title: 'Receiving/Issuing country',
            }),

            ui.nestedFields.reference<IntrastatDeclaration, IntrastatDeclarationLineRow, Currency>({
                bind: 'currency',
                title: 'Currency',
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
                isReadOnly: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                ],
            }),

            ui.nestedFields.numeric({
                title: 'Value',
                bind: 'amount',
                unit: (_id, rowData) => rowData?.currency,
            }),

            ui.nestedFields.reference<IntrastatDeclaration, IntrastatDeclarationLineRow, StatisticalProcedure>({
                bind: 'statisticalProcedure',
                title: 'Statistical procedure',
                node: '@sage/xtrem-declarations/StatisticalProcedure',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                ],
            }),

            ui.nestedFields.numeric({
                title: 'Weight (kg)',
                bind: 'netWeight',
                postfix(value, rowData) {
                    return 'kg';
                },
                scale(value, rowData) {
                    return 3;
                },
            }),

            ui.nestedFields.numeric({
                title: 'Quantity in Intrastat additional unit',
                bind: 'quantityInIntrastatAdditionalUnit',
                postfix(value, rowData) {
                    return rowData?.intrastatAdditionalUnit?.symbol ?? '';
                },
                scale(value, rowData) {
                    return rowData?.intrastatAdditionalUnit?.decimalDigits ?? 2;
                },
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.reference<IntrastatDeclaration, IntrastatDeclarationLineRow, UnitOfMeasure>({
                title: 'Additional unit',
                bind: 'intrastatAdditionalUnit',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', isHidden: true }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.text({ bind: 'decimalDigits', title: 'Decimal digits' }),
                ],
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.reference<IntrastatDeclaration, IntrastatDeclarationLineRow, NatureOfTransaction>({
                bind: 'natureOfTransaction',
                title: 'Nature of transactiton',
                node: '@sage/xtrem-declarations/NatureOfTransaction',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'name' }),
                ],
            }),

            ui.nestedFields.dropdownList({
                title: 'Mode of transport',
                bind: 'modeOfTransport',
                optionType: '@sage/xtrem-declarations/IntrastatModeOfTransport',
            }),

            ui.nestedFields.text({
                title: 'Department',
                bind: 'geographicSubdivision',
            }),

            ui.nestedFields.reference<IntrastatDeclaration, IntrastatDeclarationLineRow, Country>({
                bind: 'countryOfOrigin',
                title: 'Country of origin',
            }),

            ui.nestedFields.text({
                title: 'Customer tax ID',
                bind: 'beTaxIdNumber',
            }),

            ui.nestedFields.dropdownList({
                title: 'Document type',
                bind: 'documentType',
                optionType: '@sage/xtrem-declarations/IntrastatDocumentType',
                isReadOnly: true,
            }),

            ui.nestedFields.reference<IntrastatDeclaration, IntrastatDeclarationLineRow, PurchaseReceiptLine>({
                title: 'Purchase receipt line',
                bind: 'purchaseReceiptLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
                valueField: '_id',
                columns: [
                    ui.nestedFields.reference<IntrastatDeclaration, PurchaseReceiptLine, PurchaseReceipt>({
                        title: 'Purchase receipt',
                        bind: 'document',
                        node: '@sage/xtrem-purchasing/PurchaseReceipt',
                        valueField: 'number',
                        columns: [
                            ui.nestedFields.text({ bind: 'number', isHidden: true }),
                        ],
                        isReadOnly: true,
                        isExcludedFromMainField: true,
                    }),
                ],
                isReadOnly: true,
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.reference<IntrastatDeclaration, IntrastatDeclarationLineRow, PurchaseReturnLine>({
                title: 'Purchase return',
                bind: 'purchaseReturnLine',
                node: '@sage/xtrem-purchasing/PurchaseReturnLine',
                valueField: '_id',
                columns: [
                    ui.nestedFields.reference<IntrastatDeclaration, PurchaseReturnLine, PurchaseReturn>({
                        title: 'Purchase return',
                        bind: 'document',
                        node: '@sage/xtrem-purchasing/PurchaseReturn',
                        valueField: 'number',
                        columns: [
                            ui.nestedFields.text({ bind: 'number', isHidden: true }),
                        ],
                        isReadOnly: true,
                        isExcludedFromMainField: true,
                    }),
                ],
                isReadOnly: true,
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.reference<IntrastatDeclaration, IntrastatDeclarationLineRow, SalesShipmentLine>({
                title: 'Sales shipment',
                bind: 'salesShipmentLine',
                node: '@sage/xtrem-sales/SalesShipmentLine',
                valueField: '_id',
                columns: [
                    ui.nestedFields.reference<IntrastatDeclaration, SalesShipmentLine, SalesShipment>({
                        title: 'Sales shipment',
                        bind: 'document',
                        node: '@sage/xtrem-sales/SalesShipment',
                        valueField: 'number',
                        columns: [
                            ui.nestedFields.text({ bind: 'number', isHidden: true }),
                        ],
                        isReadOnly: true,
                        isExcludedFromMainField: true,
                    }),
                ],
                isReadOnly: true,
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.reference<IntrastatDeclaration, IntrastatDeclarationLineRow, SalesReturnReceiptLine>({
                title: 'Sales return receipt',
                bind: 'salesReturnReceiptLine',
                node: '@sage/xtrem-sales/SalesReturnReceiptLine',
                valueField: '_id',
                columns: [
                    ui.nestedFields.reference<IntrastatDeclaration, SalesReturnReceiptLine, SalesReturnReceipt>({
                        title: 'Sales return receipt',
                        bind: 'document',
                        node: '@sage/xtrem-sales/SalesReturnReceipt',
                        valueField: 'number',
                        columns: [
                            ui.nestedFields.text({ bind: 'number' }),
                        ],
                        isReadOnly: true,
                        isExcludedFromMainField: true,
                    }),
                ],
                isReadOnly: true,
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.link<IntrastatDeclaration, IntrastatDeclarationLineRow>({
                title: 'Document number',
                bind: 'linkToDocument', // Dummy link; the value is set in the map() function for we cannot use valueField.
                width: 'large',
                isTransient: true,
                map(_fieldValue, rowData) {
                    if (rowData.purchaseReceiptLine?._id) {
                        return `${rowData.purchaseReceiptLine.document.number}`;
                    }
                    if (rowData.purchaseReturnLine?._id) {
                        return `${rowData.purchaseReturnLine.document.number}`;
                    }
                    if (rowData.salesShipmentLine?._id) {
                        return `${rowData.salesShipmentLine.document.number}`;
                    }
                    if (rowData.salesReturnReceiptLine?._id) {
                        return `${rowData.salesReturnReceiptLine.document.number}`;
                    }
                    return ''; // Fallback that should never be executed but without it the linter complains.
                },
                page(_value, rowData) {
                    if (rowData.purchaseReceiptLine?._id) {
                        return '@sage/xtrem-purchasing/PurchaseReceipt';
                    }
                    if (rowData.purchaseReturnLine?._id) {
                        return '@sage/xtrem-purchasing/PurchaseReturn';
                    }
                    if (rowData.salesShipmentLine?._id) {
                        return '@sage/xtrem-sales/SalesShipment';
                    }
                    if (rowData.salesReturnReceiptLine?._id) {
                        return '@sage/xtrem-sales/SalesReturnReceipt';
                    }
                },
                queryParameters(_value, rowData) {
                    if (rowData.purchaseReceiptLine?._id) {
                        return {
                            _id: rowData.purchaseReceiptLine.document._id,
                        };
                    }
                    if (rowData.purchaseReturnLine?._id) {
                        return {
                            _id: rowData.purchaseReturnLine.document._id,
                        };
                    }
                    if (rowData.salesShipmentLine?._id) {
                        return {
                            _id: rowData.salesShipmentLine.document._id,
                        };
                    }
                    if (rowData.salesReturnReceiptLine?._id) {
                        return {
                            _id: rowData.salesReturnReceiptLine.document._id,
                        };
                    }
                },
            }),

            // -------------------------------------------------
            // Invisible columns needed for the detail panel

            ui.nestedFields.reference<IntrastatDeclaration, IntrastatDeclarationLine, BusinessEntity>({
                title: 'Business entity',
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                tunnelPage: '@sage/xtrem-master-data/BusinessEntity',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                ],
                width: 'small',
                isReadOnly: true,
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.date({
                title: 'Document date',
                bind: 'documentDate',
                isReadOnly: true,
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.reference<IntrastatDeclaration, IntrastatDeclarationLine, Item>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                ],
                width: 'large',
                isReadOnly: true,
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.numeric({
                title: 'Quantity',
                bind: 'quantity',
                width: 'small',
                postfix(value, rowData) {
                    return rowData?.salesPurchaseUnit?.symbol ?? '';
                },
                scale(value, rowData) {
                    return rowData?.salesPurchaseUnit?.decimalDigits ?? 2;
                },
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.reference<IntrastatDeclaration, IntrastatDeclarationLine, UnitOfMeasure>({
                title: 'Unit',
                bind: 'salesPurchaseUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', isHidden: true }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.text({ bind: 'decimalDigits', title: 'Decimal digits' }),
                ],
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.numeric({
                title: 'Net price',
                bind: 'netPrice',
                width: 'small',
                unit: (_id, rowData) => rowData?.currency,
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.numeric({
                title: 'Total excluding tax',
                bind: 'totalExcludingTax',
                width: 'large',
                unit: (_id, rowData) => rowData?.currency,
                scale: null,
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.numeric({
                title: 'Total excluding tax in company currency',
                bind: 'totalExcludingTaxInCompanyCurrency',
                width: 'large',
                unit: (_id, rowData) => rowData?.currency,
                scale: null,
                isExcludedFromMainField: true,
            }),

            ui.nestedFields.reference<IntrastatDeclaration, IntrastatDeclarationLine, Currency>({
                title: 'Company currency',
                bind: 'companyCurrency',
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                ],
                isExcludedFromMainField: true,
            }),

            // -------------------------------------------------
            // 2 Checkboxes indicating problems on the line.

            ui.nestedFields.checkbox({
                bind: 'hasEmptyValue',
                title: 'Has empty values',
                isReadOnly: true,
            }),

            ui.nestedFields.checkbox({
                bind: 'hasWarning',
                title: 'Has warning',
                isReadOnly: true,
            }),
        ],
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isDisabled() {
                    return !!this.$.recordId;
                },
                onClick(rowID: any) {
                    this.lines.removeRecord(rowID);
                },
            },
        ],
        fieldActions() {
            return [this.addLine];
        },
        async onRowClick(_id: string, rowData) {
            await this.showHideCurrentLineDetailPanel(_id, rowData);
        },
    })
    lines: ui.fields.Table;

    // =================================================
    // Page actions

    @ui.decorators.pageAction<IntrastatDeclaration>({
        title: 'Delete',
        isDestructive: true,
        async onClick() {
            // Custom messages for the standard deletion dialog
            this.$standardDeletePromptTitle = ui.localize(
                '@sage/xtrem-declarations/pages__intrastat_declaration__confirm_deletion_dialog_title',
                'Confirm deletion',
            );
            this.$standardDeletePromptMessage = ui.localize(
                '@sage/xtrem-declarations/pages__intrastat_declaration__confirm_deletion_dialog_text',
                'You are about to delete this declaration including all lines.',
            );
            await this.$standardDeleteAction.execute();
        },
    })
    deleteDeclaration: ui.PageAction;

    @ui.decorators.pageAction<IntrastatDeclaration>({
        icon: 'add',
        title: 'Add line',
    })
    addLine: ui.PageAction;

    // =================================================
    // Business actions

    @ui.decorators.pageAction<IntrastatDeclaration>({
        title: 'Generate',
        access: {
            bind: '$create',
        },
        isDisabled() {
            return this.status.value === 'validated';
        },
        async onClick() {
            if (
                this.status.value === 'draft' &&
                !(await confirmDialog(
                    this,
                    ui.localize(
                        '@sage/xtrem-declarations/pages__intrastat_declaration__confirm_regeneration_dialog_title',
                        'Confirm regeneration',
                    ),
                    ui.localize(
                        '@sage/xtrem-declarations/pages__intrastat_declaration__confirm_regeneration_dialog_text',
                        'You are about to reset the declaration lines including all manual changes.',
                    ),
                ))
            ) {
                return;
            }
            await this.generateIntrastatDeclaration();
        },
    })
    buttonGenerate: ui.PageAction;

    @ui.decorators.pageAction<IntrastatDeclaration>({
        title: 'Validate',
        access: {
            bind: '$create',
        },
        isDisabled() {
            return this.status.value !== 'draft';
        },
        async onClick() {
            if (
                !(await confirmDialog(
                    this,
                    ui.localize(
                        '@sage/xtrem-declarations/pages__intrastat_declaration__confirm_validation_dialog_title',
                        'Confirm validation',
                    ),
                    ui.localize(
                        '@sage/xtrem-declarations/pages__intrastat_declaration__confirm_validation_dialog_text',
                        'You are about to validate the declaration. You will no longer be able to edit or delete it.',
                    ),
                ))
            ) {
                return;
            }
            await this.validateIntrastatDeclaration();
        },
    })
    buttonValidate: ui.PageAction;

    // =================================================
    // Helper functions

    private manageBusinessButtons() {
        this.buttonGenerate.title =
            this.status.value === 'recorded'
                ? ui.localize('@sage/xtrem-declarations/pages__intrastat_declaration__generate_generate', 'Generate')
                : ui.localize(
                    '@sage/xtrem-declarations/pages__intrastat_declaration__generate_regenerate',
                    'Regenerate',
                );
        this.buttonGenerate.isDisabled = this.$.isDirty || !this.number.value || this.status.value === 'validated';
        this.buttonValidate.isDisabled = this.status.value !== 'draft';
        this.deleteDeclaration.isDisabled = !this.number.value || this.status.value === 'validated';
        // FIXME (DNE): To be removed or re-actived. Waiting for XT-29161 to be decided.
        this.headerBlock.isDisabled = this.status.value === 'validated';
    }

    private async generateIntrastatDeclaration() {
        this.$.loader.isHidden = false;
        await this.$.graph
            .node('@sage/xtrem-declarations/IntrastatDeclaration')
            .mutations.extractLines(true, { intrastatDeclaration: this._id.value })
            .execute();
        this.$.router.refresh(true);
        this.$.refreshNavigationPanel();
        this.manageBusinessButtons();
        this.$.loader.isHidden = true;
    }

    private async validateIntrastatDeclaration() {
        this.$.loader.isHidden = false;
        await this.$.graph
            .node('@sage/xtrem-declarations/IntrastatDeclaration')
            .mutations.validateDeclaration(true, { intrastatDeclaration: this._id.value })
            .execute();
        this.$.router.refresh(true);
        this.$.refreshNavigationPanel();
        this.manageBusinessButtons();
        this.$.loader.isHidden = true;
    }

    /**
     * Show/Hide the selected line's detail panel
     * @param _id
     * @param rowData
     */
    private showHideCurrentLineDetailPanel(_id: string, rowData: Partial<IntrastatDeclarationLine>) {
        this.$.detailPanel.isHidden = false;

        // Header section
        this.detailPanelHeaderSection.title = ui.localizeEnumMember(
            '@sage/xtrem-declarations/IntrastatDocumentType',
            rowData.documentType,
        );
        this.detailPanelHeaderbusinessEntityName.value = rowData.businessEntity.name;
        this.detailPanelHeaderDate.value =
            ui.formatDateToCurrentLocale(rowData.documentDate) + '\n' + rowData.beTaxIdNumber;

        // General tab
        this.detailPanelDocumentNumber.selectedRecordId = _id;
        this.detailPanelItem.selectedRecordId = _id;
        this.detailPanelQuantityAndNetPrice.selectedRecordId = _id;
        this.detailPanelTotalsExcludingTax.selectedRecordId = _id;

        // Documents tab
        this.detailPanelDocumentsPurchaseInvoiceLines.value = JSON.parse(
            rowData.computedPurchaseInvoiceLines,
        ).purchaseInvoiceLines;
        this.detailPanelDocumentsPurchaseInvoiceLines.isHidden =
            this.detailPanelDocumentsPurchaseInvoiceLines.value.length <= 0;

        this.detailPanelDocumentsPurchaseCreditMemoLines.value = JSON.parse(
            rowData.computedPurchaseCreditMemoLines,
        ).purchaseCreditMemoLines;
        this.detailPanelDocumentsPurchaseCreditMemoLines.isHidden =
            this.detailPanelDocumentsPurchaseCreditMemoLines.value.length <= 0;

        this.detailPanelDocumentsSalesInvoiceLines.value = JSON.parse(
            rowData.computedSalesInvoiceLines,
        ).purchaseInvoiceLines;
        this.detailPanelDocumentsSalesInvoiceLines.isHidden =
            this.detailPanelDocumentsSalesInvoiceLines.value.length <= 0;

        this.detailPanelDocumentsSalesCreditMemoLine.value = JSON.parse(
            rowData.computedSalesCreditMemoLines,
        ).purchaseCreditMemoLines;
        this.detailPanelDocumentsSalesCreditMemoLine.isHidden =
            this.detailPanelDocumentsSalesCreditMemoLine.value.length <= 0;
    }

    // =================================================
    // Detail panel

    // detailPanelHeaderSection
    @ui.decorators.section<IntrastatDeclaration>({
        title: 'Intrastat declaration detail panel',
        isDisabled: true,
    })
    detailPanelHeaderSection: ui.containers.Section;

    @ui.decorators.block<IntrastatDeclaration>({
        isTitleHidden: true,
        parent() {
            return this.detailPanelHeaderSection;
        },
    })
    detailPanelHeaderBlock2: ui.containers.Block;

    @ui.decorators.staticContentField<IntrastatDeclaration>({
        content: '',
        isTitleHidden: true,
        isTransient: true,
        width: 'small',
        parent() {
            return this.detailPanelHeaderBlock2;
        },
    })
    detailPanelHeaderbusinessEntityName: ui.fields.StaticContent;

    @ui.decorators.staticContentField<IntrastatDeclaration>({
        content: '',
        isTitleHidden: true,
        isTransient: true,
        width: 'small',
        parent() {
            return this.detailPanelHeaderBlock2;
        },
    })
    detailPanelHeaderDate: ui.fields.StaticContent;

    // detailPanelGeneralSection
    @ui.decorators.section<IntrastatDeclaration>({
        title: 'General',
    })
    detailPanelGeneralSection: ui.containers.Section;

    @ui.decorators.gridRowBlock<IntrastatDeclaration>({
        title: 'Document number',
        isTitleHidden: true,
        boundTo() {
            return this.lines;
        },
        fieldFilter(columnId: string) {
            return ['linkToDocument'].includes(columnId);
        },
        parent() {
            return this.detailPanelGeneralSection;
        },
    })
    detailPanelDocumentNumber: ui.containers.GridRowBlock;

    @ui.decorators.gridRowBlock<IntrastatDeclaration>({
        title: 'Item',
        isTitleHidden: true,
        isDisabled: true,
        boundTo() {
            return this.lines;
        },
        fieldFilter(columnId: string) {
            return ['item'].includes(columnId);
        },
        parent() {
            return this.detailPanelGeneralSection;
        },
    })
    detailPanelItem: ui.containers.GridRowBlock;

    @ui.decorators.gridRowBlock<IntrastatDeclaration>({
        title: 'Quantity and net price',
        isTitleHidden: true,
        isDisabled: true,
        boundTo() {
            return this.lines;
        },
        fieldFilter(columnId: string) {
            return ['quantity', 'netPrice'].includes(columnId);
        },
        parent() {
            return this.detailPanelGeneralSection;
        },
    })
    detailPanelQuantityAndNetPrice: ui.containers.GridRowBlock;

    @ui.decorators.gridRowBlock<IntrastatDeclaration>({
        title: 'Totals excluding tax',
        isTitleHidden: true,
        isDisabled: true,
        boundTo() {
            return this.lines;
        },
        fieldFilter(columnId: string) {
            return ['totalExcludingTax', 'totalExcludingTaxInCompanyCurrency'].includes(columnId);
        },
        parent() {
            return this.detailPanelGeneralSection;
        },
    })
    detailPanelTotalsExcludingTax: ui.containers.GridRowBlock;

    // detailPanelDocumentsSection
    @ui.decorators.section<IntrastatDeclaration>({
        title: 'Documents',
    })
    detailPanelDocumentsSection: ui.containers.Section;

    @ui.decorators.block<IntrastatDeclaration>({
        title: 'Documents',
        isTitleHidden: true,
        parent() {
            return this.detailPanelDocumentsSection;
        },
    })
    detailPanelDocumentsBlock: ui.containers.Block;

    @ui.decorators.podCollectionField<IntrastatDeclaration, any>({
        parent() {
            return this.detailPanelDocumentsBlock;
        },
        title: 'Purchase invoice lines',
        isTransient: true,
        columns: [
            ui.nestedFields.text({
                bind: 'documentNumber',
                isHidden: true,
            }),
            ui.nestedFields.link({
                title: 'Invoice',
                bind: 'documentNumber', // Dummy link; the value is set in the map() function.
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `${rowData.documentNumber}`;
                },
                page: '@sage/xtrem-purchasing/PurchaseInvoice',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData.document._id,
                    };
                },
            }),
            ui.nestedFields.date({
                title: 'Invoice date',
                bind: 'documentDate',
                isDisabled: true,
            }),
            ui.nestedFields.reference({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.text({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Invoice quantity',
                bind: 'quantity',
                width: 'large',
                unit: (_id, rowData) => rowData?.unit,
                unitMode: 'unitOfMeasure',
                isDisabled: true,
            }),
            ui.nestedFields.reference({
                bind: 'companyCurrency',
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax in company currency',
                bind: 'amountExcludingTax',
                width: 'large',
                unit: (_id, rowData) => rowData?.companyCurrency,
                scale: null,
                isDisabled: true,
            }),
        ],
    })
    detailPanelDocumentsPurchaseInvoiceLines: ui.fields.PodCollection;

    @ui.decorators.podCollectionField<IntrastatDeclaration, any>({
        parent() {
            return this.detailPanelDocumentsBlock;
        },
        title: 'Purchase credit memo lines',
        isTransient: true,
        columns: [
            ui.nestedFields.text({
                bind: 'documentNumber',
                isHidden: true,
            }),
            ui.nestedFields.link({
                title: 'Credit memo',
                bind: 'documentNumber', // Dummy link; the value is set in the map() function.
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `${rowData.documentNumber}`;
                },
                page: '@sage/xtrem-purchasing/PurchaseCreditMemo',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData.document._id,
                    };
                },
            }),
            ui.nestedFields.date({
                title: 'Credit memo date',
                bind: 'documentDate',
                isDisabled: true,
            }),
            ui.nestedFields.reference({
                bind: 'unit',
                isHidden: true,
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.text({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Credit memo quantity',
                bind: 'quantity',
                width: 'large',
                unit: (_id, rowData) => rowData?.unit,
                unitMode: 'unitOfMeasure',
                isDisabled: true,
            }),
            ui.nestedFields.reference({
                bind: 'companyCurrency',
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax in company currency',
                bind: 'amountExcludingTax',
                width: 'large',
                isDisabled: true,
                unit: (_id, rowData) => rowData?.companyCurrency,
                scale: null,
            }),
        ],
    })
    detailPanelDocumentsPurchaseCreditMemoLines: ui.fields.PodCollection;

    @ui.decorators.podCollectionField<IntrastatDeclaration, any>({
        parent() {
            return this.detailPanelDocumentsBlock;
        },
        title: 'Sales invoice lines',
        isTransient: true,
        columns: [
            ui.nestedFields.text({
                bind: 'documentNumber',
                isHidden: true,
            }),
            ui.nestedFields.link({
                title: 'Invoice',
                bind: 'documentNumber', // Dummy link; the value is set in the map() function.
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `${rowData.documentNumber}`;
                },
                page: '@sage/xtrem-purchasing/SalesInvoice',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData.document._id,
                    };
                },
            }),
            ui.nestedFields.date({
                title: 'Invoice date',
                bind: 'documentDate',
                isDisabled: true,
            }),
            ui.nestedFields.reference({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.text({ bind: 'decimalDigits' }),
                ],
                isHidden: true,
            }),
            ui.nestedFields.numeric({
                title: 'Invoice quantity',
                bind: 'quantity',
                width: 'large',
                unit: (_id, rowData) => rowData?.unit,
                unitMode: 'unitOfMeasure',
                isDisabled: true,
            }),
            ui.nestedFields.reference({
                bind: 'companyCurrency',
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                ],
                isHidden: true,
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax in company currency',
                bind: 'amountExcludingTax',
                width: 'large',
                isDisabled: true,
                unit: (_id, rowData) => rowData?.companyCurrency,
                scale: null,
            }),
        ],
    })
    detailPanelDocumentsSalesInvoiceLines: ui.fields.PodCollection;

    @ui.decorators.podCollectionField<IntrastatDeclaration, any>({
        parent() {
            return this.detailPanelDocumentsBlock;
        },
        title: 'Sales credit memo lines',
        isTransient: true,
        columns: [
            ui.nestedFields.text({
                bind: 'documentNumber',
                isHidden: true,
            }),
            ui.nestedFields.link({
                title: 'Credit memo',
                bind: 'documentNumber', // Dummy link; the value is set in the map() function.
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `${rowData.documentNumber}`;
                },
                page: '@sage/xtrem-purchasing/SalesCreditMemo',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData.document._id,
                    };
                },
            }),
            ui.nestedFields.date({
                title: 'Credit memo date',
                bind: 'documentDate',
                isDisabled: true,
            }),
            ui.nestedFields.reference({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.text({ bind: 'decimalDigits' }),
                ],
                isHidden: true,
            }),
            ui.nestedFields.numeric({
                title: 'Credit memo quantity',
                bind: 'quantity',
                width: 'large',
                unit: (_id, rowData) => rowData?.unit,
                unitMode: 'unitOfMeasure',
                isDisabled: true,
            }),
            ui.nestedFields.reference({
                title: 'Company currency',
                bind: 'companyCurrency',
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                ],
                isHidden: true,
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax in company currency',
                bind: 'amountExcludingTax',
                width: 'large',
                unit: (_id, rowData) => rowData?.companyCurrency,
                isDisabled: true,
                scale: null,
            }),
        ],
    })
    detailPanelDocumentsSalesCreditMemoLine: ui.fields.PodCollection;
}
