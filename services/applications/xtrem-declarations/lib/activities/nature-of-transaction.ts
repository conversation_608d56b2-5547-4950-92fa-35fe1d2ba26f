import { Activity } from '@sage/xtrem-core';
import * as xtremDeclarations from '../index';

export const natureOfTransaction = new Activity({
    description: 'Nature of transaction',
    node: () => xtremDeclarations.nodes.NatureOfTransaction,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['create', 'update', 'delete'],
                on: [() => xtremDeclarations.nodes.NatureOfTransaction],
            },
        ],
    },
});
