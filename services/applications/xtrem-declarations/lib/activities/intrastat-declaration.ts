import { Activity } from '@sage/xtrem-core';
import * as xtremDeclarations from '../index';

export const intrastatDeclaration = new Activity({
    description: 'Intrastat declaration',
    node: () => xtremDeclarations.nodes.IntrastatDeclaration,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['create', 'update', 'delete', 'extractLines', 'validateDeclaration'],
                on: [() => xtremDeclarations.nodes.IntrastatDeclaration],
            },
            { operations: ['lookup', 'update'], on: [() => xtremDeclarations.nodes.MovementRule] },
            {
                operations: ['lookup', 'create', 'update', 'delete'],
                on: [() => xtremDeclarations.nodes.NatureOfTransaction],
            },
            {
                operations: ['lookup', 'create', 'update', 'delete'],
                on: [() => xtremDeclarations.nodes.StatisticalProcedure],
            },
        ],
    },
});
