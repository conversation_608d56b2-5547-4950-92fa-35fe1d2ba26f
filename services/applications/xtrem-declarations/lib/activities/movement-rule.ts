import { Activity } from '@sage/xtrem-core';
import * as xtremDeclarations from '../index';

export const movementRule = new Activity({
    description: 'Movement rule',
    node: () => xtremDeclarations.nodes.MovementRule,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['update'],
                on: [() => xtremDeclarations.nodes.MovementRule],
            },
        ],
    },
});
