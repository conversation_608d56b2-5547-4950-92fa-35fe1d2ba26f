import { Activity } from '@sage/xtrem-core';
import * as xtremDeclarations from '../index';

export const statisticalProcedure = new Activity({
    description: 'Statistical procedure',
    node: () => xtremDeclarations.nodes.StatisticalProcedure,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['create', 'update', 'delete'],
                on: [() => xtremDeclarations.nodes.StatisticalProcedure],
            },
        ],
    },
});
