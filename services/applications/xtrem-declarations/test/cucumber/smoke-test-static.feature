@xtrem_declarations
Feature: smoke-test-static

    #Case with navigation panel full width without create business action
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                          | Title                  |
            | @sage/xtrem-declarations/MovementRule         | Movement rules         |
            | @sage/xtrem-declarations/StatisticalProcedure | Statistical procedures |
            | @sage/xtrem-declarations/NatureOfTransaction  | Natures of transaction |
            | @sage/xtrem-declarations/IntrastatDeclaration | Intrastat declarations |


    #Case with navigation panel full width
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                          | NavigationPanelTitle   | Title                 |
            | @sage/xtrem-declarations/IntrastatDeclaration | Intrastat declarations | Intrastat declaration |
            | @sage/xtrem-declarations/NatureOfTransaction  | Natures of transaction | Nature of transaction |
            | @sage/xtrem-declarations/StatisticalProcedure | Statistical procedures | Statistical procedure |
            # Opening pages having extensions in the current package
            | @sage/xtrem-master-data/DeliveryMode          | Delivery modes         | Delivery mode         |
            | @sage/xtrem-master-data/Item                  | Items                  | Item                  |


    # Positive test to ensure that the error dialog is definitely exist and open
    Scenario: sts \ xtrem-declarations \ desktop \ check an error dialog appears when the page does not load
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Wazup"
        Then an error dialog appears on the main page
        Examples:
