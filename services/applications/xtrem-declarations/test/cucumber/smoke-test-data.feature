@xtrem_declarations
Feature: smoke-test-data

    #Case without navigation panel full width
    Scenario Outline: std \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed
        Examples:
            | Page                                                           | Title                      |
            | @sage/xtrem-declarations/MovementRule/eyJfaWQiOiI0In0=         | Movement rule Sales return |
            | @sage/xtrem-declarations/IntrastatDeclaration/eyJfaWQiOiIxIn0= | Intrastat declaration 1    |
