@xtrem_declarations
Feature:  smoke-test-pr-cd-statistical-procedure

    Scenario: Statistical procedure creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-declarations/StatisticalProcedure"
        Then the "Statistical procedures" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Set isActive to "On"
        And the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "ON"
        #Fill in name
        And the user selects the "name" labelled text field on the main page
        And the user writes "Statistical-procedure-Test" in the text field
        #Fill in ID
        And the user selects the "ID" labelled text field on the main page
        And the user writes "Statistical-procedure-Test" in the text field
        #Click Save Crud Button
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: Nature of transaction deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-declarations/StatisticalProcedure"
        Then the "Statistical procedures" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "Statistical-procedure-Test"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Then the "Statistical procedure Statistical-procedure-Test" titled page is displayed
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
