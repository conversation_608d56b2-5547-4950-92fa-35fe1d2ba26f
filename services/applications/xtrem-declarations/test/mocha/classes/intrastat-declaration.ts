import type { Collection, Context, decimal, NodeCreateData } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '@sage/xtrem-purchasing';
import * as xtremSales from '@sage/xtrem-sales';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import { assert } from 'chai';
import * as xtremDeclarations from '../../../lib/index';

let lastBaseDocumentLineId = 0; // store the highest _id of BaseDocumentLine before running the tests

describe('Intrastat declaration', () => {
    before(async () => {
        await Test.withContext(async context => {
            const maxIds = await context
                .queryAggregate(xtremMasterData.nodes.BaseDocumentLine, {
                    group: {},
                    values: { _id: { max: true } },
                })
                .toArray();
            lastBaseDocumentLineId = maxIds[0].values._id.max;
        });
    });

    beforeEach(async () => {
        // set company S1 to the intrastat-declaration test context
        await Test.withCommittedContext(async context => {
            const companyS1 = await context.read(xtremSystem.nodes.Company, { id: 'S1' }, { forUpdate: true });
            const lines = companyS1.dimensionTypes;
            await lines.forEach(async line => {
                await line.$.delete();
            });
        });
    });

    interface Document {
        _id: number;
        number: string;
        info: string;
        type: string;
    }

    type AllDocuments =
        | xtremSales.nodes.SalesShipment
        | xtremSales.nodes.SalesInvoice
        | xtremSales.nodes.SalesCreditMemo
        | xtremSales.nodes.SalesReturnRequest
        | xtremSales.nodes.SalesReturnReceipt
        | xtremPurchasing.nodes.PurchaseReceipt
        | xtremPurchasing.nodes.PurchaseInvoice
        | xtremPurchasing.nodes.PurchaseCreditMemo
        | xtremPurchasing.nodes.PurchaseReturn;

    async function addDocument(documents: Document[], document: AllDocuments, info: string): Promise<void> {
        await document.$.context.flushDeferredActions();
        documents.push({ _id: document._id, number: await document.number, info, type: document.$.factory.name });
    }

    // ----------------------------------------
    // Sales documents generation for the tests
    // ----------------------------------------
    function getSalesOrderData(currentDate: date): NodeCreateData<xtremSales.nodes.SalesOrder> {
        return {
            site: '#ETS2-S01',
            stockSite: '#ETS2-S01',
            soldToCustomer: '#Pastelaria moderna',
            date: currentDate,
            status: 'pending',
            requestedDeliveryDate: currentDate,
            lines: [
                {
                    item: '#Milk',
                    status: 'pending',
                    site: '#ETS2-S01',
                    stockSite: '#ETS2-S01',
                    quantity: 40,
                    grossPrice: 12,
                    storedAttributes: null,
                    storedDimensions: { dimensionType01: '300' },
                },
            ],
        };
    }

    async function createSalesShipment(context: Context): Promise<xtremSales.nodes.SalesShipment> {
        const salesOrder = await context.create(xtremSales.nodes.SalesOrder, getSalesOrderData(date.today()));
        await salesOrder.$.save({ flushDeferredActions: true });

        await xtremSales.nodes.SalesOrder.createSalesShipmentsFromOrderLines(context, [
            {
                salesDocumentLine: (await salesOrder.lines.toArray())[0],
                quantityToProcess: await (await salesOrder.lines.toArray())[0].quantity,
            },
        ]);
        await context.flushDeferredActions();
        const salesShipment = (
            await context.query(xtremSales.nodes.SalesShipment, { forUpdate: true, orderBy: { _id: 1 } }).toArray()
        ).pop();

        if (!salesShipment) {
            assert.fail('The sales shipment is empty.');
        }

        await salesShipment.$.set({
            status: 'shipped',
            lines: [{ _id: (await salesShipment.lines.elementAt(0))._id, status: 'shipped' }],
        });

        await salesShipment.$.save();

        return salesShipment;
    }

    async function invoiceShipment(
        context: Context,
        salesShipment: xtremSales.nodes.SalesShipment,
        quantityToProcess?: decimal,
    ): Promise<xtremSales.nodes.SalesInvoice> {
        const salesShipmentLines = (await salesShipment.lines
            .map(line => {
                return { salesDocumentLine: line, quantityToProcess };
            })
            .toArray()) as [{ salesDocumentLine: xtremSales.nodes.SalesShipmentLine; quantityToProcess?: decimal }];

        await xtremSales.nodes.SalesShipment.createSalesInvoicesFromShipmentLines(context, salesShipmentLines);
        await context.flushDeferredActions();

        const salesShipmentLineToSalesInvoiceLine = (
            await context
                .query(xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine, {
                    filter: { linkedDocument: salesShipmentLines[0].salesDocumentLine._id },
                    orderBy: { _id: 1 },
                })
                .toArray()
        ).pop();
        if (!salesShipmentLineToSalesInvoiceLine) {
            assert.fail('Sales shipment line to sales invoice line is empty.');
        }

        let salesInvoice = await context.read(xtremSales.nodes.SalesInvoice, {
            _id: (await (await salesShipmentLineToSalesInvoiceLine.document).document)._id,
        });
        await xtremSales.nodes.SalesInvoice.post(context, salesInvoice);
        // enforce the 'posted' status of the invoice (after post it is 'inProgress'). Normally the 'posted'
        // status is set asynchronously on the reply of the finance integration
        salesInvoice = await context.read(
            xtremSales.nodes.SalesInvoice,
            { _id: salesInvoice._id },
            { forUpdate: true },
        );
        await salesInvoice.$.set({ status: 'posted', forceUpdateForFinance: true });
        await salesInvoice.$.save();

        return salesInvoice;
    }

    async function creditInvoice(
        context: Context,
        salesInvoice: xtremSales.nodes.SalesInvoice,
        quantityToProcess?: decimal,
    ): Promise<xtremSales.nodes.SalesCreditMemo> {
        const toInvoiceLines = (await salesInvoice.lines
            .map(line => {
                return { salesDocumentLine: line, quantityToProcess };
            })
            .toArray()) as [{ salesDocumentLine: xtremSales.nodes.SalesInvoiceLine; quantityToProcess?: decimal }];

        await xtremSales.nodes.SalesInvoice.createSalesCreditMemosFromInvoiceLines(context, toInvoiceLines);
        await context.flushDeferredActions();

        const salesInvoiceLineToSalesCreditMemoLine = (
            await context
                .query(xtremSales.nodes.SalesInvoiceLineToSalesCreditMemoLine, {
                    filter: { linkedDocument: toInvoiceLines[0].salesDocumentLine._id },
                    orderBy: { _id: 1 },
                })
                .toArray()
        ).pop();

        if (!salesInvoiceLineToSalesCreditMemoLine) {
            assert.fail('The sales invoice line to sales credit memo line is empty.');
        }

        const salesCreditMemo = await context.read(xtremSales.nodes.SalesCreditMemo, {
            _id: (await (await salesInvoiceLineToSalesCreditMemoLine.document).document)._id,
        });
        await xtremSales.nodes.SalesCreditMemo.post(context, salesCreditMemo);

        return salesCreditMemo;
    }

    /** Generates 5 shipment lines - return created documents */
    async function createSalesShipments(context: Context): Promise<Document[]> {
        // Generates 5 shipment lines
        const documents: Document[] = [];

        // First sales shipment, shipped but not invoiced
        const saleShipment1 = await createSalesShipment(context);
        await addDocument(documents, saleShipment1, 'shipped but not invoiced');

        // Second sales shipment, shipped and fully invoiced
        const salesShipment2 = await createSalesShipment(context);
        const invoiceSalesShipment2 = await invoiceShipment(context, salesShipment2);
        await addDocument(documents, salesShipment2, 'shipped and fully invoiced');
        await addDocument(documents, invoiceSalesShipment2, 'shipped and fully invoiced');

        // Third sales shipment, shipped and partly invoiced with 2 invoices
        const salesShipment3 = await createSalesShipment(context);
        const invoice1SalesShipment3 = await invoiceShipment(context, salesShipment3, 14);
        const invoice2SalesShipment3 = await invoiceShipment(context, salesShipment3, 16);
        await addDocument(documents, salesShipment3, 'shipped and partly invoiced with 2 invoices');
        await addDocument(documents, invoice1SalesShipment3, 'shipped and partly invoiced with 2 invoices');
        await addDocument(documents, invoice2SalesShipment3, 'shipped and partly invoiced with 2 invoices');

        // Fourth sales shipment, shipped, fully invoiced and fully credited
        const salesShipment4 = await createSalesShipment(context);
        const salesShipment4Invoice = await invoiceShipment(context, salesShipment4);
        const salesShipMent4InvoiceCreditMemo = await creditInvoice(context, salesShipment4Invoice);
        await addDocument(documents, salesShipment4, 'shipped, fully invoiced and fully credited');
        await addDocument(documents, salesShipment4Invoice, 'shipped, fully invoiced and fully credited');
        await addDocument(documents, salesShipMent4InvoiceCreditMemo, 'shipped, fully invoiced and fully credited');

        // Fifth sales shipment, shipped, fully invoiced and partially credited
        const salesShipment5 = await createSalesShipment(context);
        const salesShipment5Invoice = await invoiceShipment(context, salesShipment5);
        const salesCreditMemo1 = await creditInvoice(context, salesShipment5Invoice, 30);
        await addDocument(documents, salesShipment5, 'shipped, fully invoiced and partially credited');
        await addDocument(documents, salesShipment5Invoice, 'shipped, fully invoiced and partially credited');
        await addDocument(documents, salesCreditMemo1, 'shipped, fully invoiced and partially credited');

        return documents;

        // TODO: [RM] create the case where we have credit memos linked to sales returns
    }

    async function createSalesReturnReceiptFromReturnRequest(
        context: Context,
        salesReturnRequest: xtremSales.nodes.SalesReturnRequest,
    ): Promise<xtremSales.nodes.SalesReturnReceipt> {
        const receipt = await context.create(xtremSales.nodes.SalesReturnReceipt, {
            effectiveDate: date.today(),
            site: '#ETS2-S01',
            shipToCustomer: '#Pastelaria moderna',
            lines: [
                {
                    item: '#Milk',
                    quantityInStockUnit: await (await salesReturnRequest.lines.at(0))?.quantityInStockUnit,
                    quantity: await (await salesReturnRequest.lines.at(0))?.quantity,
                    toReturnRequestLines: [
                        {
                            linkedDocument: await salesReturnRequest.lines.at(0),
                            quantityInStockUnit: 40,
                        },
                    ],
                },
            ],
        });

        await receipt.$.save({ flushDeferredActions: true });
        await receipt.$.set({
            lines: [{ _id: (await receipt.lines.toArray())[0]._id, stockTransactionStatus: 'completed' }],
        });
        await receipt.$.save();

        return receipt;
    }

    async function createSalesReturnRequestFromShipment(
        context: Context,
        salesShipment: xtremSales.nodes.SalesShipment,
        quantityToProcess?: decimal,
    ): Promise<xtremSales.nodes.SalesReturnRequest> {
        const salesShipmentLines = (await salesShipment.lines
            .map(line => {
                return { salesDocumentLine: line, quantityToProcess };
            })
            .toArray()) as [{ salesDocumentLine: xtremSales.nodes.SalesShipmentLine; quantityToProcess?: decimal }];

        await xtremSales.nodes.SalesShipment.createSalesReturnRequestFromShipmentLines(context, salesShipmentLines);
        await context.flushDeferredActions();

        const salesShipmentLineToSalesReturnRequestLine = (
            await context
                .query(xtremSales.nodes.SalesShipmentLineToSalesReturnRequestLine, {
                    filter: { linkedDocument: salesShipmentLines[0].salesDocumentLine._id },
                    orderBy: { _id: 1 },
                })
                .toArray()
        ).pop();

        if (!salesShipmentLineToSalesReturnRequestLine) {
            assert.fail('The sales shipment line to sales return request line is empty.');
        }
        const salesReturnRequest = await context.read(xtremSales.nodes.SalesReturnRequest, {
            _id: (await (await salesShipmentLineToSalesReturnRequestLine.document).document)._id,
        });

        return salesReturnRequest;
    }

    async function createSalesCreditMemoFromReturnRequest(
        context: Context,
        salesShipment: xtremSales.nodes.SalesShipment,
        quantityToProcess?: decimal,
    ): Promise<Document[]> {
        const documents: Document[] = [];
        // if we want to credit them, we first need to invoice them
        const invoice = await invoiceShipment(context, salesShipment);
        await addDocument(documents, invoice, 'shipped and invoiced');

        const returnRequest = await createSalesReturnRequestFromShipment(context, salesShipment);
        await returnRequest.$.set({ approvalStatus: 'pendingApproval' });
        await returnRequest.$.save({ flushDeferredActions: true });
        await xtremSales.nodes.SalesReturnRequest.approve(context, returnRequest);
        await addDocument(documents, returnRequest, 'shipped and returned');

        const returnReceipt = await createSalesReturnReceiptFromReturnRequest(context, returnRequest);
        await addDocument(documents, returnReceipt, 'shipped and returned');

        await returnRequest.$.set({
            approvalStatus: 'approved',
            lines: [
                {
                    _id: (await returnRequest.lines.elementAt(0))._id,
                    isCreditMemoExpected: true,
                    receiptStatus: 'received',
                    _action: 'update',
                },
            ],
        });
        await returnRequest.$.save({ flushDeferredActions: true });

        const salesRequestLinesToCredit = (await returnRequest.lines
            .map(line => {
                return { salesDocumentLine: line, quantityToProcess };
            })
            .toArray()) as [
            { salesDocumentLine: xtremSales.nodes.SalesReturnRequestLine; quantityToProcess?: decimal },
        ];

        const createSalesOutputDocumentsReturnValues =
            await xtremSales.nodes.SalesReturnRequest.createSalesCreditMemosFromReturnRequestLines(
                context,
                salesRequestLinesToCredit,
            );
        await context.flushDeferredActions();
        if (createSalesOutputDocumentsReturnValues.numberOfCreditMemos === 1) {
            const creditMemo = (
                await context
                    .query(xtremSales.nodes.SalesCreditMemo, {
                        orderBy: { _id: 1 },
                    })
                    .toArray()
            ).pop();

            if (!creditMemo) {
                assert.fail('The credit memo is empty.');
            }

            await xtremSales.nodes.SalesCreditMemo.post(context, creditMemo);
        }

        return documents;
    }

    /** Generates 3 shipment lines and 3 sales return lines  */
    async function createSalesReturns(context: Context): Promise<Document[]> {
        const documents: Document[] = [];

        // first sales receipt
        const salesShipment = await createSalesShipment(context);
        const returnRequest = await createSalesReturnRequestFromShipment(context, salesShipment);
        const salesReturnReceipt = await createSalesReturnReceiptFromReturnRequest(context, returnRequest);
        await addDocument(documents, salesShipment, 'shipped and returned');
        await addDocument(documents, returnRequest, 'shipped and returned');
        await addDocument(documents, salesReturnReceipt, 'shipped and returned');

        // second sales receipt, fully credited
        const salesShipment2 = await createSalesShipment(context);
        documents.push(...(await createSalesCreditMemoFromReturnRequest(context, salesShipment2)));

        await addDocument(documents, salesShipment2, 'shipped and fully credited');

        // third sales receipt, partially credited
        const salesShipment3 = await createSalesShipment(context);
        documents.push(...(await createSalesCreditMemoFromReturnRequest(context, salesShipment3, 14)));
        await addDocument(documents, salesShipment3, 'shipped and partially credited');

        return documents;
    }

    // -------------------------------------------
    // Purchase documents generation for the tests
    // -------------------------------------------
    function getPurchaseOrderData(currentDate: date): NodeCreateData<xtremPurchasing.nodes.PurchaseOrder> {
        return {
            site: '#ETS1-S01',
            businessRelation: '#The Golf Shack',
            currency: '#EUR',
            orderDate: currentDate,
            fxRateDate: currentDate,
            approvalStatus: 'pendingApproval',
            lines: [
                {
                    item: '#StockItem01',
                    stockSite: '#ETS1-S01',
                    quantity: 150,
                    unit: '#GRAM',
                    grossPrice: 100,
                },
                {
                    item: '#StockItem02',
                    stockSite: '#ETS1-S01',
                    quantity: 100,
                    unit: '#GRAM',
                    grossPrice: 100,
                },
            ],
        };
    }

    async function invoicePurchaseReceipt(
        context: Context,
        purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt,
    ): Promise<xtremPurchasing.nodes.PurchaseInvoice> {
        const [{ _id }] = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseInvoice(context, purchaseReceipt);
        await context.flushDeferredActions();

        const purchaseInvoice = await context.read(xtremPurchasing.nodes.PurchaseInvoice, { _id });

        await purchaseInvoice.$.set({
            status: 'posted',
            totalAmountExcludingTax: await purchaseInvoice.calculatedTotalAmountExcludingTax,
        });
        await purchaseInvoice.$.save();

        return context.read(xtremPurchasing.nodes.PurchaseInvoice, { _id: purchaseInvoice._id });
    }

    async function createPurchaseReceipt(context: Context): Promise<xtremPurchasing.nodes.PurchaseReceipt> {
        const purchaseOrder = await context.create(
            xtremPurchasing.nodes.PurchaseOrder,
            getPurchaseOrderData(date.today()),
        );
        await purchaseOrder.$.save({ flushDeferredActions: true });

        // Approve the purchase order
        await xtremPurchasing.nodes.PurchaseOrder.approve(context, purchaseOrder, true);

        // Create receipt from purchase order
        const [{ _id }] = await xtremPurchasing.nodes.PurchaseOrder.createPurchaseReceipt(context, purchaseOrder);

        // Read the purchase receipt created
        const purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { _id }, { forUpdate: true });

        assert.equal(await purchaseReceipt.lines.length, 2);

        // Update the stock details
        await purchaseReceipt.$.set({
            lines: [
                {
                    _sortValue: await (await purchaseReceipt.lines.elementAt(0))._sortValue,
                    _action: 'update',
                    stockDetails: [
                        {
                            effectiveDate: date.today(),
                            site: '#ETS1-S01',
                            item: '#StockItem01',
                            location: '#PTLOC01|ETS1-S01|Central',
                            stockUnit: '#GRAM',
                            status: '#A',
                            owner: '#ETS1-S01',
                            quantityInStockUnit: 150,
                        },
                    ],
                    stockTransactionStatus: 'completed',
                },
                {
                    _sortValue: await (await purchaseReceipt.lines.elementAt(1))._sortValue,
                    _action: 'update',
                    stockDetails: [
                        {
                            effectiveDate: date.today(),
                            site: '#ETS1-S01',
                            item: '#StockItem01',
                            location: '#PTLOC01|ETS1-S01|Central',
                            stockUnit: '#GRAM',
                            status: '#A',
                            owner: '#ETS1-S01',
                            quantityInStockUnit: 100,
                        },
                    ],
                    stockTransactionStatus: 'completed',
                },
            ],
        });
        await purchaseReceipt.$.save({ flushDeferredActions: true });

        // Post the purchase receipt
        await xtremPurchasing.nodes.PurchaseReceipt.post(context, purchaseReceipt);

        return context.read(xtremPurchasing.nodes.PurchaseReceipt, { _id });
    }

    async function createPurchaseReturn(
        context: Context,
        purchaseReceiptNumber: string,
    ): Promise<xtremPurchasing.nodes.PurchaseReturn | null> {
        const purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, {
            number: purchaseReceiptNumber,
        });
        const purchaseReturnResult = await xtremPurchasing.nodes.PurchaseReceipt.createPurchaseReturns(
            context,
            purchaseReceipt,
        );
        await context.flushDeferredActions();

        if (purchaseReturnResult) {
            const purchaseReturn = (
                await context
                    .query(xtremPurchasing.nodes.PurchaseReturn, {
                        forUpdate: true,
                        orderBy: { _id: 1 },
                    })
                    .toArray()
            ).pop();

            if (!purchaseReturn) {
                assert.fail('The purchase return is empty.');
            }

            await purchaseReturn.lines.forEach(purchaseReturnLine =>
                purchaseReturn.$.set({
                    lines: [{ _id: purchaseReturnLine._id, shippedStatus: 'shipped', _action: 'update' }],
                }),
            );
            await purchaseReturn.$.save({ flushDeferredActions: true });

            return purchaseReturn;
        }
        return null;
    }

    function getPurchaseInvoiceData(params: {
        orderDate: date;
        purchaseReceiptLine: xtremPurchasing.nodes.PurchaseReceiptLine;
    }): NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice> {
        return {
            payToSupplier: '#The Golf Shack',
            billBySupplier: '#The Golf Shack',
            site: '#ETS1-S01',
            invoiceDate: params.orderDate,
            supplierDocumentDate: params.orderDate,
            lines: [
                {
                    _action: 'create',
                    charge: 0,
                    discount: 0,
                    grossPrice: 100,
                    item: '#StockItem01',
                    amountExcludingTax: 5000,
                    amountExcludingTaxInCompanyCurrency: 5000,
                    amountIncludingTax: 5000,
                    amountIncludingTaxInCompanyCurrency: 5000,
                    netPrice: 100,
                    priceOrigin: 'manual',
                    purchaseReceiptLine: { purchaseReceiptLine: params.purchaseReceiptLine._id },
                    unit: '#GRAM',
                    unitToStockUnitConversionFactor: 1,
                    quantity: 50,
                    recipientSite: '#ETS1-S01',
                    stockUnit: '#GRAM',
                    taxAmount: 0,
                    taxAmountAdjusted: 0,
                },
            ],
            totalAmountExcludingTax: 5000,
        };
    }

    function getSecondPurchaseInvoiceData(params: {
        orderDate: date;
        purchaseReceiptLine: xtremPurchasing.nodes.PurchaseReceiptLine;
    }): NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice> {
        return {
            payToSupplier: '#The Golf Shack',
            billBySupplier: '#The Golf Shack',
            site: '#ETS1-S01',
            invoiceDate: params.orderDate,
            supplierDocumentDate: params.orderDate,
            lines: [
                {
                    _action: 'create',
                    charge: 0,
                    discount: 0,
                    grossPrice: 100,
                    item: '#StockItem01',
                    amountExcludingTax: 5000,
                    amountExcludingTaxInCompanyCurrency: 5000,
                    amountIncludingTax: 5000,
                    amountIncludingTaxInCompanyCurrency: 5000,
                    netPrice: 100,
                    priceOrigin: 'manual',
                    purchaseReceiptLine: { purchaseReceiptLine: params.purchaseReceiptLine._id },
                    unit: '#GRAM',
                    unitToStockUnitConversionFactor: 1,
                    quantity: 100,
                    recipientSite: '#ETS1-S01',
                    stockUnit: '#GRAM',
                    taxAmount: 0,
                    taxAmountAdjusted: 0,
                },
            ],
            totalAmountExcludingTax: 10000,
        };
    }

    async function getPurchaseCreditMemoData(params: {
        orderDate: date;
        purchaseInvoiceLines: Collection<xtremPurchasing.nodes.PurchaseInvoiceLine>;
    }): Promise<NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo>> {
        return {
            payToSupplier: '#The Golf Shack',
            lines: [
                {
                    _action: 'create',
                    charge: 0,
                    discount: 0,
                    grossPrice: 100,
                    item: '#StockItem01',
                    amountExcludingTax: 15000,
                    amountExcludingTaxInCompanyCurrency: 15000,
                    amountIncludingTax: 15000,
                    amountIncludingTaxInCompanyCurrency: 15000,
                    netPrice: 100,
                    priceOrigin: 'manual',
                    purchaseInvoiceLine: { purchaseInvoiceLine: (await params.purchaseInvoiceLines.elementAt(0))._id },
                    unit: '#GRAM',
                    unitToStockUnitConversionFactor: 1,
                    quantity: 150,
                    recipientSite: '#ETS1-S01',
                    stockUnit: '#GRAM',
                    storedAttributes: null,
                    storedDimensions: null,
                    taxAmount: 0,
                    taxAmountAdjusted: 0,
                },
                {
                    _action: 'create',
                    charge: 0,
                    discount: 0,
                    grossPrice: 100,
                    item: '#StockItem02',
                    amountExcludingTax: 10000,
                    amountExcludingTaxInCompanyCurrency: 10000,
                    amountIncludingTax: 10000,
                    amountIncludingTaxInCompanyCurrency: 10000,
                    netPrice: 100,
                    priceOrigin: 'manual',
                    purchaseInvoiceLine: { purchaseInvoiceLine: (await params.purchaseInvoiceLines.elementAt(1))._id },
                    unit: '#GRAM',
                    unitToStockUnitConversionFactor: 1,
                    quantity: 100,
                    recipientSite: '#ETS1-S01',
                    stockUnit: '#GRAM',
                    storedAttributes: null,
                    storedDimensions: null,
                    taxAmount: 0,
                    taxAmountAdjusted: 0,
                },
            ],
            reason: '#R1',
            paymentTerm: '#TEST_NET_30_SUPPLIER',
            currency: '#EUR',
            billBySupplier: '#The Golf Shack',
            site: '#ETS1-S01',
            fxRateDate: params.orderDate,
            companyFxRate: 1,
            companyFxRateDivisor: 1,
            creditMemoDate: params.orderDate,
            dueDate: params.orderDate,
            status: 'draft',
            taxCalculationStatus: 'notDone',
            supplierDocumentDate: params.orderDate,
            totalAmountExcludingTax: 25000,
            totalAmountIncludingTax: 25000,
            totalTaxAmount: 0,
            totalTaxAmountAdjusted: 0,
        };
    }

    /** return created documents */
    async function createPurchaseReceiptsAndReturns(context: Context): Promise<Document[]> {
        const orderDate: date = date.today();
        const documents: Document[] = [];

        // First purchase receipt, received but not invoiced
        const purchaseReceipt1 = await createPurchaseReceipt(context);
        const purchaseReturn = await createPurchaseReturn(context, await purchaseReceipt1.number);
        await addDocument(documents, purchaseReceipt1, 'received but not invoiced');
        if (purchaseReturn) await addDocument(documents, purchaseReturn, 'received but not invoiced');

        // Second purchase receipt, received and fully invoiced
        const purchaseReceipt2 = await createPurchaseReceipt(context);
        const purchaseInvoiceOfPurchaseReceipt = await invoicePurchaseReceipt(context, purchaseReceipt2);
        await addDocument(documents, purchaseReceipt2, 'received and fully invoiced');
        await addDocument(documents, purchaseInvoiceOfPurchaseReceipt, 'received and fully invoiced');

        // Third purchase receipt - Multiple invoices
        const purchaseReceipt3 = await createPurchaseReceipt(context);
        await addDocument(documents, purchaseReceipt3, 'received and multiple invoices');

        // Third purchase receipt - First purchase invoice
        const purchaseInvoiceData1 = getPurchaseInvoiceData({
            orderDate,
            purchaseReceiptLine: await purchaseReceipt3.lines.elementAt(0),
        });
        const purchaseInvoice1 = await context.create(xtremPurchasing.nodes.PurchaseInvoice, purchaseInvoiceData1);
        await purchaseInvoice1.$.save({ flushDeferredActions: true });
        await purchaseInvoice1.$.set({ status: 'posted' });
        await purchaseInvoice1.$.save({ flushDeferredActions: true });
        await addDocument(documents, purchaseInvoice1, 'received and multiple invoices');

        // Third Purchase receipt  - Second invoice
        const purchaseInvoiceData2 = getSecondPurchaseInvoiceData({
            orderDate,
            purchaseReceiptLine: await purchaseReceipt3.lines.elementAt(0),
        });
        const purchaseInvoice2 = await context.create(xtremPurchasing.nodes.PurchaseInvoice, purchaseInvoiceData2);
        await purchaseInvoice2.$.save({ flushDeferredActions: true });
        await purchaseInvoice2.$.set({ status: 'posted' });
        await purchaseInvoice2.$.save({ flushDeferredActions: true });
        await addDocument(documents, purchaseInvoice2, 'received and multiple invoices');

        const purchaseReturn4 = await createPurchaseReturn(context, await purchaseReceipt3.number);
        if (purchaseReturn4) await addDocument(documents, purchaseReturn4, 'received and multiple invoices');

        // Fourth purchase receipt - fully invoice and credited
        const purchaseReceipt4 = await createPurchaseReceipt(context);
        const purchaseInvoice4 = await invoicePurchaseReceipt(context, purchaseReceipt4);
        await addDocument(documents, purchaseReceipt4, 'received and fully invoiced and credited');
        await addDocument(documents, purchaseInvoice4, 'received and fully invoiced and credited');

        const purchaseCreditMemoData = await getPurchaseCreditMemoData({
            orderDate,
            purchaseInvoiceLines: purchaseInvoice4.lines,
        });

        const purchaseCreditNote4 = await context.create(
            xtremPurchasing.nodes.PurchaseCreditMemo,
            purchaseCreditMemoData,
        );
        await purchaseCreditNote4.$.save({ flushDeferredActions: true });
        await addDocument(documents, purchaseCreditNote4, 'received and fully invoiced and credited');

        await xtremPurchasing.nodes.PurchaseCreditMemo.post(context, purchaseCreditNote4);

        return documents;

        // TODO: It's not possible to create a credit memo for a purchase return. Add that to the tests when possible.
    }

    const createdDocuments = [
        { number: 'SH220001', type: 'SalesShipment' },
        { number: 'SH220002', type: 'SalesShipment' },
        { number: 'SIPEG220001', type: 'SalesInvoice' },
        { number: 'SH220003', type: 'SalesShipment' },
        { number: 'SIPEG220002', type: 'SalesInvoice' },
        { number: 'SIPEG220003', type: 'SalesInvoice' },
        { number: 'SH220004', type: 'SalesShipment' },
        { number: 'SIPEG220004', type: 'SalesInvoice' },
        { number: 'SCEG220001', type: 'SalesCreditMemo' },
        { number: 'SH220005', type: 'SalesShipment' },
        { number: 'SIPEG220005', type: 'SalesInvoice' },
        { number: 'SCEG220002', type: 'SalesCreditMemo' },
        { number: 'SH220006', type: 'SalesShipment' },
        { number: 'ST220001', type: 'SalesReturnRequest' },
        { number: 'SRR220001', type: 'SalesReturnReceipt' },
        { number: 'SIPEG220006', type: 'SalesInvoice' },
        { number: 'ST220002', type: 'SalesReturnRequest' },
        { number: 'SRR220002', type: 'SalesReturnReceipt' },
        { number: 'SH220007', type: 'SalesShipment' },
        { number: 'SIPEG220007', type: 'SalesInvoice' },
        { number: 'ST220003', type: 'SalesReturnRequest' },
        { number: 'SRR220003', type: 'SalesReturnReceipt' },
        { number: 'SH220008', type: 'SalesShipment' },
        { number: 'PR220001', type: 'PurchaseReceipt' },
        { number: 'PT220001', type: 'PurchaseReturn' },
        { number: 'PR220002', type: 'PurchaseReceipt' },
        { number: 'PI220001', type: 'PurchaseInvoice' },
        { number: 'PR220003', type: 'PurchaseReceipt' },
        { number: 'PI220002', type: 'PurchaseInvoice' },
        { number: 'PI220003', type: 'PurchaseInvoice' },
        { number: 'PT220002', type: 'PurchaseReturn' },
        { number: 'PR220004', type: 'PurchaseReceipt' },
        { number: 'PI220004', type: 'PurchaseInvoice' },
        { number: 'PC220001', type: 'PurchaseCreditMemo' },
    ];

    const returnReceiptFilterCompare = {
        filter: {
            _and: [
                {
                    document: {
                        status: 'closed',
                        stockSite: { legalCompany: { _id: 13 } },
                        date: { _lte: { _value: 20220630 } },
                        shipToCustomer: { businessEntity: { country: { _ne: 1, isEuMember: true } } },
                    },
                    item: { type: 'good' },
                    intrastatDeclarations: { _none: true },
                },
            ],
        },
    };

    it('Intrastat extraction for intrastat transactions', () =>
        Test.withContext(
            async context => {
                const taxSolution = await context.read(
                    xtremTax.nodes.TaxSolution,
                    { _id: '#FRSOL' },
                    { forUpdate: true },
                );
                if (await taxSolution.lines.at(1)) {
                    await taxSolution.$.set({
                        lines: [{ _action: 'delete', _id: (await taxSolution.lines.at(1))?._id }],
                    });
                    await taxSolution.$.save();
                }

                const documents: Document[] = [];

                documents.push(...(await createSalesShipments(context)));
                documents.push(...(await createSalesReturns(context)));
                documents.push(...(await createPurchaseReceiptsAndReturns(context)));

                await context.flushDeferredActions();

                assert.equal(documents.length, 34);
                assert.deepEqual(
                    documents.map(doc => {
                        return { number: doc.number, type: doc.type };
                    }),
                    createdDocuments,
                );

                const company = await context.read(xtremSystem.nodes.Company, { id: 'S1' });

                // First declaration
                const intrastatDeclaration = await context.create(xtremDeclarations.nodes.IntrastatDeclaration, {
                    number: 666,
                    company,
                    date: date.today().endOfMonth().addDays(1),
                });

                // Not the end of a month, so saving should not be possible.
                await assert.isRejected(intrastatDeclaration.$.save());

                // Now EOM, so the declaration should be saved without errors.
                await intrastatDeclaration.$.set({ date: date.today().endOfMonth() });
                await intrastatDeclaration.$.save();

                const extractor = new xtremDeclarations.classes.IntrastatLineExtractor(intrastatDeclaration);

                const returnReceiptFilter = await (extractor as any).salesReturnReceiptLinesFilter;
                assert.deepEqual(returnReceiptFilter, returnReceiptFilterCompare);

                const salesReturnReceiptLinesExpected = context.query(xtremSales.nodes.SalesReturnReceiptLine, {
                    filter: { document: { number: { _in: ['SRR220001', 'SRR220002', 'SRR220003'] } } },
                });
                const line1 = await salesReturnReceiptLinesExpected.elementAt(0);
                const document1 = await line1.document;
                const line2 = await salesReturnReceiptLinesExpected.elementAt(1);
                const document2 = await line2.document;
                const line3 = await salesReturnReceiptLinesExpected.elementAt(2);
                const document3 = await line3.document;

                //   stockSite: { legalCompany: { _id: 13 } },
                assert.equal((await (await document1?.stockSite)?.legalCompany)?._id, company._id);
                assert.equal((await (await document2?.stockSite)?.legalCompany)?._id, company._id);
                assert.equal((await (await document3?.stockSite)?.legalCompany)?._id, company._id);

                // shipToCustomer: { businessEntity: { country: { _ne: 1, isEuMember: true } } },
                assert.equal(await (await (await (await document1.shipToCustomer).businessEntity).country).id, 'PT');
                assert.equal(await (await (await (await document1.shipToCustomer).businessEntity).country).id, 'PT');
                assert.equal(await (await (await (await document1.shipToCustomer).businessEntity).country).id, 'PT');

                assert.equal(await document1.status, 'closed');
                assert.equal(await document2.status, 'closed');
                assert.equal(await document3.status, 'closed');

                assert.equal(await (await line1.item).type, 'good');
                assert.equal(await (await line2.item).type, 'good');
                assert.equal(await (await line3.item).type, 'good');

                assert.equal(await line1.intrastatDeclarations.length, 0);
                assert.equal(await line2.intrastatDeclarations.length, 0);
                assert.equal(await line3.intrastatDeclarations.length, 0);

                const salesReturnReceiptLines = context.query(
                    xtremSales.nodes.SalesReturnReceiptLine,
                    returnReceiptFilter,
                );

                assert.equal(await salesReturnReceiptLines.length, 3);

                const length = await extractor.extract();
                assert.equal(length, 23);

                // Was 23 before the businessEntity refactoring
                assert.equal(await intrastatDeclaration.lines.length, 23);
                assert.equal(await (await intrastatDeclaration.lines.at(0))?.amount, 480);
                assert.equal(await (await intrastatDeclaration.lines.at(0))?.toInvoiceLines.length, 0);
                assert.equal(await (await intrastatDeclaration.lines.at(0))?.salesCreditMemoLines.length, 0);
                assert.equal(await (await intrastatDeclaration.lines.at(1))?.amount, 480);
                assert.equal(await (await intrastatDeclaration.lines.at(1))?.toInvoiceLines.length, 1);
                assert.equal(await (await intrastatDeclaration.lines.at(1))?.salesCreditMemoLines.length, 0);
                assert.equal(await (await intrastatDeclaration.lines.at(2))?.amount, 480);
                assert.equal(await (await intrastatDeclaration.lines.at(2))?.toInvoiceLines.length, 2);
                assert.equal(await (await intrastatDeclaration.lines.at(2))?.salesCreditMemoLines.length, 0);
                assert.equal(await (await intrastatDeclaration.lines.at(3))?.amount, 480);
                assert.equal(await (await intrastatDeclaration.lines.at(3))?.toInvoiceLines.length, 1);
                assert.equal(await (await intrastatDeclaration.lines.at(3))?.salesCreditMemoLines.length, 1);
                assert.equal(await (await intrastatDeclaration.lines.at(4))?.amount, 480);
                assert.equal(await (await intrastatDeclaration.lines.at(4))?.toInvoiceLines.length, 1);
                assert.equal(await (await intrastatDeclaration.lines.at(4))?.salesCreditMemoLines.length, 1);

                // Sales shipments (3) + Sales Returns (3)
                assert.equal(await (await intrastatDeclaration.lines.at(5))?.amount, 480);
                assert.equal(await (await intrastatDeclaration.lines.at(5))?.salesCreditMemoLines.length, 0);
                assert.equal(await (await intrastatDeclaration.lines.at(6))?.amount, 480);
                assert.equal(await (await intrastatDeclaration.lines.at(6))?.salesCreditMemoLines.length, 1);
                assert.equal(await (await intrastatDeclaration.lines.at(7))?.amount, 480);
                assert.equal(await (await intrastatDeclaration.lines.at(7))?.salesCreditMemoLines.length, 1);
                assert.equal(await (await intrastatDeclaration.lines.at(8))?.salesCreditMemoLines.length, 0);
                assert.equal(
                    await (
                        await intrastatDeclaration.lines.at(9)
                    )?.salesCreditMemoLines.length,
                    1,
                    'Line 9 - salesCreditMemoLines length ',
                );
                assert.equal(await (await intrastatDeclaration.lines.at(10))?.salesCreditMemoLines.length, 1);

                // Purchase receipts
                assert.equal(await (await intrastatDeclaration.lines.at(11))?.purchaseInvoiceLines.length, 0);
                assert.equal(await (await intrastatDeclaration.lines.at(12))?.purchaseInvoiceLines.length, 0);
                assert.equal(await (await intrastatDeclaration.lines.at(13))?.purchaseInvoiceLines.length, 1);
                assert.equal(await (await intrastatDeclaration.lines.at(14))?.purchaseInvoiceLines.length, 1);
                assert.equal(await (await intrastatDeclaration.lines.at(15))?.purchaseInvoiceLines.length, 2);
                assert.equal(await (await intrastatDeclaration.lines.at(16))?.purchaseInvoiceLines.length, 0);
                assert.equal(await (await intrastatDeclaration.lines.at(17))?.purchaseCreditMemoLines.length, 1);
                assert.equal(await (await intrastatDeclaration.lines.at(18))?.purchaseCreditMemoLines.length, 1);

                // Purchase returns
                assert.equal(await (await intrastatDeclaration.lines.at(19))?.purchaseInvoiceLines.length, 0);
                assert.equal(await (await intrastatDeclaration.lines.at(20))?.purchaseInvoiceLines.length, 0);
                assert.equal(await (await intrastatDeclaration.lines.at(21))?.purchaseInvoiceLines.length, 0);

                // computedPurchaseInvoiceLines
                assert.isTrue(
                    JSON.stringify(
                        await (
                            await intrastatDeclaration.lines.at(13)
                        )?.computedPurchaseInvoiceLines,
                    ).startsWith(`{"purchaseInvoiceLines":[{"_id":${lastBaseDocumentLineId + 44}`),
                );

                // computedPurchaseCreditMemoLines
                assert.isTrue(
                    JSON.stringify(
                        await (
                            await intrastatDeclaration.lines.at(17)
                        )?.computedPurchaseCreditMemoLines,
                    ).startsWith(`{"purchaseCreditMemoLines":[{"_id":${lastBaseDocumentLineId + 60}`),
                );

                // computedSalesInvoiceLines
                assert.isTrue(
                    JSON.stringify(
                        await (
                            await intrastatDeclaration.lines.at(1)
                        )?.computedSalesInvoiceLines,
                    ).startsWith(`{"toInvoiceLines":[{"_id":${lastBaseDocumentLineId + 5}`),
                );

                // computedSalesCreditMemoLines
                assert.isTrue(
                    JSON.stringify(
                        await (
                            await intrastatDeclaration.lines.at(6)
                        )?.computedSalesCreditMemoLines,
                    ).startsWith(`{"salesCreditMemoLines":[{"_id":${lastBaseDocumentLineId + 27}`),
                );

                await createSalesShipments(context);

                // Second declaration - it should not include the lines already included on the first declaration
                const intrastatDeclaration2 = await context.create(xtremDeclarations.nodes.IntrastatDeclaration, {
                    number: 999,
                    company,
                    date: date.today().endOfMonth(),
                });

                await intrastatDeclaration2.$.save({ flushDeferredActions: true });

                await new xtremDeclarations.classes.IntrastatLineExtractor(intrastatDeclaration2).extract();
                assert.equal(await intrastatDeclaration2.lines.length, 5);
            },
            {
                today: '2022-06-03',
                user: { email: '<EMAIL>' },
            },
        ));

    it('Intrastat declaration - saveBegin() and controlDelete()', () =>
        Test.withContext(async context => {
            // Create declaration for company 'S1'.
            const company = await context.read(xtremSystem.nodes.Company, {
                id: 'S1',
            });
            const intrastatDeclaration = await context.create(xtremDeclarations.nodes.IntrastatDeclaration, {
                // number: 666,
                company,
                date: date.today().endOfMonth(),
            });

            // Saving should be possible.
            await intrastatDeclaration.$.save({ flushDeferredActions: true });

            // Check that we've got a new number (intrastatDeclaration.number > 0)
            assert.isAbove(await intrastatDeclaration.number, 0, 'Declaration number not set.');

            // Validate
            await intrastatDeclaration.$.set({ status: 'validated' });

            // Deleting of a validated statement should be not possible.
            await assert.isRejected(intrastatDeclaration.$.delete());
        }));

    it('Intrastat declaration - saveBegin() for 2 different companies', () =>
        Test.withContext(async context => {
            // Create declaration for company 'S1' and S4.
            const intrastatDeclarationS1 = await context.create(xtremDeclarations.nodes.IntrastatDeclaration, {
                company: '#S1',
                date: date.today().endOfMonth(),
            });
            const intrastatDeclarationS4 = await context.create(xtremDeclarations.nodes.IntrastatDeclaration, {
                company: '#S4',
                date: date.today().endOfMonth(),
            });

            // Saving should be possible.
            await intrastatDeclarationS1.$.save({ flushDeferredActions: true });
            await intrastatDeclarationS4.$.save({ flushDeferredActions: true });

            // Check that we've got a new number (intrastatDeclaration.number > 0)
            assert.isAbove(await intrastatDeclarationS1.number, 0, 'Declaration number not set.');
            assert.isAbove(await intrastatDeclarationS4.number, 0, 'Declaration number not set.');
        }));

    it('Intrastat declaration lines - Check comodity code, business entity tax ID and computed columns', () =>
        Test.withContext(async context => {
            const intrastatDeclaration = await context.read(xtremDeclarations.nodes.IntrastatDeclaration, {
                number: '1',
                company: '#S1',
            });

            // documentDate
            assert.equal((await (await intrastatDeclaration.lines.at(0))?.documentDate)?.toString(), '2020-08-10');
            assert.equal((await (await intrastatDeclaration.lines.at(1))?.documentDate)?.toString(), '2020-08-10');
            assert.equal((await (await intrastatDeclaration.lines.at(2))?.documentDate)?.toString(), '2020-11-20');
            assert.equal((await (await intrastatDeclaration.lines.at(3))?.documentDate)?.toString(), '2021-01-01');

            // businessEntity
            assert.equal(await (await (await intrastatDeclaration.lines.at(0))?.businessEntity)?.id, 'LECLERC');
            assert.equal(await (await (await intrastatDeclaration.lines.at(1))?.businessEntity)?.id, 'LECLERC');
            assert.equal(await (await (await intrastatDeclaration.lines.at(2))?.businessEntity)?.id, 'US020');
            // assert.equal(await (await (await intrastatDeclaration.lines.at(3))?.businessEntity)?.id, 'US019');

            // item
            assert.equal(await (await (await intrastatDeclaration.lines.at(0))?.item)?.id, 'Chemical C');
            assert.equal(await (await (await intrastatDeclaration.lines.at(1))?.item)?.id, 'Chemical C');
            assert.equal(await (await (await intrastatDeclaration.lines.at(2))?.item)?.id, 'Chemical D');
            // assert.equal(await (await (await intrastatDeclaration.lines.at(3))?.item)?.id, 'Chair');

            // quantity
            assert.equal(await (await intrastatDeclaration.lines.at(0))?.quantity, 10);
            assert.equal(await (await intrastatDeclaration.lines.at(1))?.quantity, 10);
            assert.equal(await (await intrastatDeclaration.lines.at(2))?.quantity, 10);
            // assert.equal(await (await intrastatDeclaration.lines.at(3))?.quantity, 10);

            // salesPurchaseUnit
            assert.equal(await (await (await intrastatDeclaration.lines.at(0))?.salesPurchaseUnit)?.id, 'KILOGRAM');
            assert.equal(await (await (await intrastatDeclaration.lines.at(1))?.salesPurchaseUnit)?.id, 'KILOGRAM');
            assert.equal(await (await (await intrastatDeclaration.lines.at(2))?.salesPurchaseUnit)?.id, 'OUNCE');
            // assert.equal(await (await (await intrastatDeclaration.lines.at(3))?.salesPurchaseUnit)?.id, 'EACH');

            // totalExcludingTax
            assert.equal(await (await intrastatDeclaration.lines.at(0))?.totalExcludingTax, 1000);
            assert.equal(await (await intrastatDeclaration.lines.at(1))?.totalExcludingTax, 1000);
            assert.equal(await (await intrastatDeclaration.lines.at(2))?.totalExcludingTax, 100);
            // assert.equal(await (await intrastatDeclaration.lines.at(3))?.totalExcludingTax, 0);

            // totalExcludingTaxInCompanyCurrency
            assert.equal(await (await intrastatDeclaration.lines.at(0))?.totalExcludingTaxInCompanyCurrency, 0);
            assert.equal(await (await intrastatDeclaration.lines.at(1))?.totalExcludingTaxInCompanyCurrency, 0);
            assert.equal(await (await intrastatDeclaration.lines.at(2))?.totalExcludingTaxInCompanyCurrency, 100);
            // assert.equal(await (await intrastatDeclaration.lines.at(3))?.totalExcludingTaxInCompanyCurrency, 0);

            // netPrice
            assert.equal(await (await intrastatDeclaration.lines.at(0))?.netPrice, 100);
            assert.equal(await (await intrastatDeclaration.lines.at(1))?.netPrice, 0);
            assert.equal(await (await intrastatDeclaration.lines.at(2))?.netPrice, 10);
            // assert.equal(await (await intrastatDeclaration.lines.at(3))?.netPrice, 0);

            await assert.isRejected(intrastatDeclaration.$.save({ flushDeferredActions: true }));
        }));
});
