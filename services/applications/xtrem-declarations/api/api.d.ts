declare module '@sage/xtrem-declarations-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type {
        DocumentLineDiscountCharge,
        DocumentLineDiscountChargeBinding,
        DocumentLineDiscountChargeInput,
        Package as SageXtremDistribution$Package,
    } from '@sage/xtrem-distribution-api';
    import type {
        AnalyticalData,
        Package as SageXtremFinanceData$Package,
        PostingClass,
    } from '@sage/xtrem-finance-data-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type {
        LandedCostDocumentLine,
        LandedCostDocumentLineBinding,
        LandedCostDocumentLineInput,
        LandedCostItem,
        LandedCostItemBinding,
        LandedCostItemInput,
        LandedCostLine,
        LandedCostLineBinding,
        LandedCostLineInput,
        Package as SageXtremLandedCost$Package,
    } from '@sage/xtrem-landed-cost-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type {
        Address,
        BaseDocumentLine,
        BomRevisionSequence,
        BusinessEntity,
        BusinessEntityAddress,
        Currency,
        CustomerPriceReason,
        Item,
        ItemAllergen,
        ItemAllergenBinding,
        ItemAllergenInput,
        ItemCategory,
        ItemClassifications,
        ItemClassificationsBinding,
        ItemClassificationsInput,
        ItemCustomer,
        ItemCustomerInput,
        ItemCustomerPrice,
        ItemCustomerPriceBinding,
        ItemCustomerPriceInput,
        ItemInput,
        ItemSite,
        ItemSiteBinding,
        ItemSiteInput,
        ItemSupplier,
        ItemSupplierInput,
        ItemSupplierPrice,
        ItemSupplierPriceBinding,
        ItemSupplierPriceInput,
        Location,
        Package as SageXtremMasterData$Package,
        ReasonCode,
        SequenceNumber,
        Supplier,
        UnitOfMeasure,
    } from '@sage/xtrem-master-data-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type {
        Package as SageXtremPurchasing$Package,
        PurchaseCreditMemoLine,
        PurchaseInvoiceLine,
        PurchaseOrderLine,
        PurchaseOrderLineToPurchaseReceiptLine,
        PurchaseOrderLineToPurchaseReceiptLineBinding,
        PurchaseOrderLineToPurchaseReceiptLineInput,
        PurchaseReceipt,
        PurchaseReceiptLine,
        PurchaseReceiptLineToPurchaseInvoiceLine,
        PurchaseReceiptLineToPurchaseReturnLine,
        PurchaseReceiptLineToPurchaseReturnLineBinding,
        PurchaseReceiptLineToPurchaseReturnLineInput,
        PurchaseReturn,
        PurchaseReturnLine,
        PurchaseReturnLineToPurchaseCreditMemoLine,
        PurchaseReturnLineToPurchaseInvoiceLine,
        WorkInProgressPurchaseReceiptLine,
        WorkInProgressPurchaseReceiptLineBinding,
        WorkInProgressPurchaseReceiptLineInput,
        WorkInProgressPurchaseReturnLine,
        WorkInProgressPurchaseReturnLineBinding,
        WorkInProgressPurchaseReturnLineInput,
    } from '@sage/xtrem-purchasing-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type {
        Package as SageXtremSales$Package,
        SalesCreditMemoLine,
        SalesInvoiceLine,
        SalesOrderLineToSalesShipmentLine,
        SalesOrderLineToSalesShipmentLineBinding,
        SalesOrderLineToSalesShipmentLineInput,
        SalesReturnReceipt,
        SalesReturnReceiptLine,
        SalesReturnRequestLineToSalesReturnReceiptLine,
        SalesReturnRequestLineToSalesReturnReceiptLineBinding,
        SalesReturnRequestLineToSalesReturnReceiptLineInput,
        SalesShipment,
        SalesShipmentLine,
        SalesShipmentLineDiscountCharge,
        SalesShipmentLineDiscountChargeBinding,
        SalesShipmentLineDiscountChargeInput,
        SalesShipmentLineToSalesReturnReceiptLine,
        SalesShipmentLineToSalesReturnReceiptLineBinding,
        SalesShipmentLineToSalesReturnReceiptLineInput,
    } from '@sage/xtrem-sales-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type {
        Lot,
        Package as SageXtremStockData$Package,
        StockAllocation,
        StockIssueDetail,
        StockIssueDetailBinding,
        StockIssueDetailInput,
        StockJournal,
        StockReceiptDetail,
        StockReceiptDetailBinding,
        StockReceiptDetailInput,
        StockStatus,
        StockTransaction,
        StockTransactionBinding,
        StockTransactionInput,
    } from '@sage/xtrem-stock-data-api';
    import type { Country, Package as SageXtremStructure$Package } from '@sage/xtrem-structure-api';
    import type { Package as SageXtremSynchronization$Package } from '@sage/xtrem-synchronization-api';
    import type { Company, Package as SageXtremSystem$Package, Site, SysVendor, User } from '@sage/xtrem-system-api';
    import type {
        DocumentLineTax,
        DocumentLineTaxBinding,
        DocumentLineTaxInput,
        ItemTaxGroup,
        Package as SageXtremTax$Package,
    } from '@sage/xtrem-tax-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface IntrastatDeclarationPeriod$Enum {
        transactionMonth: 0;
        earliestMonth: 1;
    }
    export type IntrastatDeclarationPeriod = keyof IntrastatDeclarationPeriod$Enum;
    export interface IntrastatDeclarationStatus$Enum {
        recorded: 0;
        draft: 1;
        validated: 2;
    }
    export type IntrastatDeclarationStatus = keyof IntrastatDeclarationStatus$Enum;
    export interface IntrastatDocumentType$Enum {
        salesShipment: 0;
        salesReturn: 1;
        purchaseReceipt: 2;
        purchaseReturn: 3;
    }
    export type IntrastatDocumentType = keyof IntrastatDocumentType$Enum;
    export interface IntrastatFlow$Enum {
        dispatch: 0;
        arrival: 1;
    }
    export type IntrastatFlow = keyof IntrastatFlow$Enum;
    export interface IntrastatModeOfTransport$Enum {
        seaTransport: 1;
        railwayTransport: 2;
        roadTransport: 3;
        airTransport: 4;
        postalConsignments: 5;
        fixedTransportInstallations: 7;
        inlandWaterwayTransport: 8;
        ownPropulsion: 9;
    }
    export type IntrastatModeOfTransport = keyof IntrastatModeOfTransport$Enum;
    export interface IntrastatDeclaration extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        company: Company;
        number: integer;
        date: string;
        status: IntrastatDeclarationStatus;
        lines: ClientCollection<IntrastatDeclarationLine>;
    }
    export interface IntrastatDeclarationInput extends ClientNodeInput {
        company?: integer | string;
        number?: integer | string;
        date?: string;
        status?: IntrastatDeclarationStatus;
        lines?: Partial<IntrastatDeclarationLineInput>[];
    }
    export interface IntrastatDeclarationBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        company: Company;
        number: integer;
        date: string;
        status: IntrastatDeclarationStatus;
        lines: ClientCollection<IntrastatDeclarationLineBinding>;
    }
    export interface IntrastatDeclaration$Mutations {
        extractLines: Node$Operation<
            {
                intrastatDeclaration: string;
            },
            integer
        >;
        validateDeclaration: Node$Operation<
            {
                intrastatDeclaration: string;
            },
            integer
        >;
    }
    export interface IntrastatDeclaration$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntrastatDeclaration$Lookups {
        company: QueryOperation<Company>;
    }
    export interface IntrastatDeclaration$Operations {
        query: QueryOperation<IntrastatDeclaration>;
        read: ReadOperation<IntrastatDeclaration>;
        aggregate: {
            read: AggregateReadOperation<IntrastatDeclaration>;
            query: AggregateQueryOperation<IntrastatDeclaration>;
        };
        create: CreateOperation<IntrastatDeclarationInput, IntrastatDeclaration>;
        getDuplicate: GetDuplicateOperation<IntrastatDeclaration>;
        update: UpdateOperation<IntrastatDeclarationInput, IntrastatDeclaration>;
        updateById: UpdateByIdOperation<IntrastatDeclarationInput, IntrastatDeclaration>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: IntrastatDeclaration$Mutations;
        asyncOperations: IntrastatDeclaration$AsyncOperations;
        lookups(dataOrId: string | { data: IntrastatDeclarationInput }): IntrastatDeclaration$Lookups;
        getDefaults: GetDefaultsOperation<IntrastatDeclaration>;
    }
    export interface IntrastatDeclarationLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        intrastatDeclaration: IntrastatDeclaration;
        lineNumber: string;
        flow: IntrastatFlow;
        commodityCode: string;
        country: Country;
        currency: Currency;
        netWeight: string;
        quantityInIntrastatAdditionalUnit: string;
        intrastatAdditionalUnit: UnitOfMeasure;
        statisticalProcedure: StatisticalProcedure;
        natureOfTransaction: NatureOfTransaction;
        modeOfTransport: IntrastatModeOfTransport;
        geographicSubdivision: string;
        countryOfOrigin: Country;
        beTaxIdNumber: string;
        documentType: IntrastatDocumentType;
        purchaseReceiptLine: PurchaseReceiptLine;
        purchaseReturnLine: PurchaseReturnLine;
        salesShipmentLine: SalesShipmentLine;
        salesReturnReceiptLine: SalesReturnReceiptLine;
        baseDocumentLine: BaseDocumentLine;
        documentDate: string;
        businessEntity: BusinessEntity;
        item: Item;
        quantity: string;
        salesPurchaseUnit: UnitOfMeasure;
        netPrice: string;
        totalExcludingTax: string;
        totalExcludingTaxInCompanyCurrency: string;
        companyCurrency: Currency;
        computedPurchaseInvoiceLines: string;
        computedPurchaseCreditMemoLines: string;
        computedSalesInvoiceLines: string;
        computedSalesCreditMemoLines: string;
        hasWarning: boolean;
        purchaseInvoiceLines: ClientCollection<IntrastatDeclarationLineToPurchaseInvoiceLine>;
        purchaseCreditMemoLines: ClientCollection<IntrastatDeclarationLineToPurchaseCreditMemoLine>;
        toInvoiceLines: ClientCollection<IntrastatDeclarationLineToSalesInvoiceLine>;
        salesCreditMemoLines: ClientCollection<IntrastatDeclarationLineToSalesCreditMemoLine>;
        amount: string;
        hasEmptyValue: boolean;
    }
    export interface IntrastatDeclarationLineInput extends VitalClientNodeInput {
        lineNumber?: decimal | string;
        flow?: IntrastatFlow;
        commodityCode?: string;
        country?: integer | string;
        currency?: integer | string;
        netWeight?: decimal | string;
        quantityInIntrastatAdditionalUnit?: decimal | string;
        intrastatAdditionalUnit?: integer | string;
        statisticalProcedure?: integer | string;
        natureOfTransaction?: integer | string;
        modeOfTransport?: IntrastatModeOfTransport;
        geographicSubdivision?: string;
        countryOfOrigin?: integer | string;
        beTaxIdNumber?: string;
        documentType?: IntrastatDocumentType;
        purchaseReceiptLine?: integer | string;
        purchaseReturnLine?: integer | string;
        salesShipmentLine?: integer | string;
        salesReturnReceiptLine?: integer | string;
        baseDocumentLine?: integer | string;
        purchaseInvoiceLines?: Partial<IntrastatDeclarationLineToPurchaseInvoiceLineInput>[];
        purchaseCreditMemoLines?: Partial<IntrastatDeclarationLineToPurchaseCreditMemoLineInput>[];
        toInvoiceLines?: Partial<IntrastatDeclarationLineToSalesInvoiceLineInput>[];
        salesCreditMemoLines?: Partial<IntrastatDeclarationLineToSalesCreditMemoLineInput>[];
        amount?: decimal | string;
    }
    export interface IntrastatDeclarationLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        intrastatDeclaration: IntrastatDeclaration;
        lineNumber: string;
        flow: IntrastatFlow;
        commodityCode: string;
        country: Country;
        currency: Currency;
        netWeight: string;
        quantityInIntrastatAdditionalUnit: string;
        intrastatAdditionalUnit: UnitOfMeasure;
        statisticalProcedure: StatisticalProcedure;
        natureOfTransaction: NatureOfTransaction;
        modeOfTransport: IntrastatModeOfTransport;
        geographicSubdivision: string;
        countryOfOrigin: Country;
        beTaxIdNumber: string;
        documentType: IntrastatDocumentType;
        purchaseReceiptLine: PurchaseReceiptLine;
        purchaseReturnLine: PurchaseReturnLine;
        salesShipmentLine: SalesShipmentLine;
        salesReturnReceiptLine: SalesReturnReceiptLine;
        baseDocumentLine: BaseDocumentLine;
        documentDate: string;
        businessEntity: BusinessEntity;
        item: Item;
        quantity: string;
        salesPurchaseUnit: UnitOfMeasure;
        netPrice: string;
        totalExcludingTax: string;
        totalExcludingTaxInCompanyCurrency: string;
        companyCurrency: Currency;
        computedPurchaseInvoiceLines: any;
        computedPurchaseCreditMemoLines: any;
        computedSalesInvoiceLines: any;
        computedSalesCreditMemoLines: any;
        hasWarning: boolean;
        purchaseInvoiceLines: ClientCollection<IntrastatDeclarationLineToPurchaseInvoiceLineBinding>;
        purchaseCreditMemoLines: ClientCollection<IntrastatDeclarationLineToPurchaseCreditMemoLineBinding>;
        toInvoiceLines: ClientCollection<IntrastatDeclarationLineToSalesInvoiceLineBinding>;
        salesCreditMemoLines: ClientCollection<IntrastatDeclarationLineToSalesCreditMemoLineBinding>;
        amount: string;
        hasEmptyValue: boolean;
    }
    export interface IntrastatDeclarationLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntrastatDeclarationLine$Lookups {
        country: QueryOperation<Country>;
        currency: QueryOperation<Currency>;
        intrastatAdditionalUnit: QueryOperation<UnitOfMeasure>;
        statisticalProcedure: QueryOperation<StatisticalProcedure>;
        natureOfTransaction: QueryOperation<NatureOfTransaction>;
        countryOfOrigin: QueryOperation<Country>;
        purchaseReceiptLine: QueryOperation<PurchaseReceiptLine>;
        purchaseReturnLine: QueryOperation<PurchaseReturnLine>;
        salesShipmentLine: QueryOperation<SalesShipmentLine>;
        salesReturnReceiptLine: QueryOperation<SalesReturnReceiptLine>;
        baseDocumentLine: QueryOperation<BaseDocumentLine>;
        businessEntity: QueryOperation<BusinessEntity>;
        item: QueryOperation<Item>;
        salesPurchaseUnit: QueryOperation<UnitOfMeasure>;
        companyCurrency: QueryOperation<Currency>;
    }
    export interface IntrastatDeclarationLine$Operations {
        query: QueryOperation<IntrastatDeclarationLine>;
        read: ReadOperation<IntrastatDeclarationLine>;
        aggregate: {
            read: AggregateReadOperation<IntrastatDeclarationLine>;
            query: AggregateQueryOperation<IntrastatDeclarationLine>;
        };
        asyncOperations: IntrastatDeclarationLine$AsyncOperations;
        lookups(dataOrId: string | { data: IntrastatDeclarationLineInput }): IntrastatDeclarationLine$Lookups;
        getDefaults: GetDefaultsOperation<IntrastatDeclarationLine>;
    }
    export interface IntrastatDeclarationLineToPurchaseCreditMemoLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        intrastatDeclarationLine: IntrastatDeclarationLine;
        purchaseCreditMemoLine: PurchaseCreditMemoLine;
    }
    export interface IntrastatDeclarationLineToPurchaseCreditMemoLineInput extends VitalClientNodeInput {
        purchaseCreditMemoLine?: integer | string;
    }
    export interface IntrastatDeclarationLineToPurchaseCreditMemoLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        intrastatDeclarationLine: IntrastatDeclarationLine;
        purchaseCreditMemoLine: PurchaseCreditMemoLine;
    }
    export interface IntrastatDeclarationLineToPurchaseCreditMemoLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntrastatDeclarationLineToPurchaseCreditMemoLine$Lookups {
        purchaseCreditMemoLine: QueryOperation<PurchaseCreditMemoLine>;
    }
    export interface IntrastatDeclarationLineToPurchaseCreditMemoLine$Operations {
        query: QueryOperation<IntrastatDeclarationLineToPurchaseCreditMemoLine>;
        read: ReadOperation<IntrastatDeclarationLineToPurchaseCreditMemoLine>;
        aggregate: {
            read: AggregateReadOperation<IntrastatDeclarationLineToPurchaseCreditMemoLine>;
            query: AggregateQueryOperation<IntrastatDeclarationLineToPurchaseCreditMemoLine>;
        };
        asyncOperations: IntrastatDeclarationLineToPurchaseCreditMemoLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: IntrastatDeclarationLineToPurchaseCreditMemoLineInput },
        ): IntrastatDeclarationLineToPurchaseCreditMemoLine$Lookups;
        getDefaults: GetDefaultsOperation<IntrastatDeclarationLineToPurchaseCreditMemoLine>;
    }
    export interface IntrastatDeclarationLineToPurchaseInvoiceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        intrastatDeclarationLine: IntrastatDeclarationLine;
        purchaseInvoiceLine: PurchaseInvoiceLine;
    }
    export interface IntrastatDeclarationLineToPurchaseInvoiceLineInput extends VitalClientNodeInput {
        purchaseInvoiceLine?: integer | string;
    }
    export interface IntrastatDeclarationLineToPurchaseInvoiceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        intrastatDeclarationLine: IntrastatDeclarationLine;
        purchaseInvoiceLine: PurchaseInvoiceLine;
    }
    export interface IntrastatDeclarationLineToPurchaseInvoiceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntrastatDeclarationLineToPurchaseInvoiceLine$Lookups {
        purchaseInvoiceLine: QueryOperation<PurchaseInvoiceLine>;
    }
    export interface IntrastatDeclarationLineToPurchaseInvoiceLine$Operations {
        query: QueryOperation<IntrastatDeclarationLineToPurchaseInvoiceLine>;
        read: ReadOperation<IntrastatDeclarationLineToPurchaseInvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<IntrastatDeclarationLineToPurchaseInvoiceLine>;
            query: AggregateQueryOperation<IntrastatDeclarationLineToPurchaseInvoiceLine>;
        };
        asyncOperations: IntrastatDeclarationLineToPurchaseInvoiceLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: IntrastatDeclarationLineToPurchaseInvoiceLineInput },
        ): IntrastatDeclarationLineToPurchaseInvoiceLine$Lookups;
        getDefaults: GetDefaultsOperation<IntrastatDeclarationLineToPurchaseInvoiceLine>;
    }
    export interface IntrastatDeclarationLineToSalesCreditMemoLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        intrastatDeclarationLine: IntrastatDeclarationLine;
        salesCreditMemoLine: SalesCreditMemoLine;
    }
    export interface IntrastatDeclarationLineToSalesCreditMemoLineInput extends VitalClientNodeInput {
        salesCreditMemoLine?: integer | string;
    }
    export interface IntrastatDeclarationLineToSalesCreditMemoLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        intrastatDeclarationLine: IntrastatDeclarationLine;
        salesCreditMemoLine: SalesCreditMemoLine;
    }
    export interface IntrastatDeclarationLineToSalesCreditMemoLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntrastatDeclarationLineToSalesCreditMemoLine$Lookups {
        salesCreditMemoLine: QueryOperation<SalesCreditMemoLine>;
    }
    export interface IntrastatDeclarationLineToSalesCreditMemoLine$Operations {
        query: QueryOperation<IntrastatDeclarationLineToSalesCreditMemoLine>;
        read: ReadOperation<IntrastatDeclarationLineToSalesCreditMemoLine>;
        aggregate: {
            read: AggregateReadOperation<IntrastatDeclarationLineToSalesCreditMemoLine>;
            query: AggregateQueryOperation<IntrastatDeclarationLineToSalesCreditMemoLine>;
        };
        asyncOperations: IntrastatDeclarationLineToSalesCreditMemoLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: IntrastatDeclarationLineToSalesCreditMemoLineInput },
        ): IntrastatDeclarationLineToSalesCreditMemoLine$Lookups;
        getDefaults: GetDefaultsOperation<IntrastatDeclarationLineToSalesCreditMemoLine>;
    }
    export interface IntrastatDeclarationLineToSalesInvoiceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        intrastatDeclarationLine: IntrastatDeclarationLine;
        salesInvoiceLine: SalesInvoiceLine;
    }
    export interface IntrastatDeclarationLineToSalesInvoiceLineInput extends VitalClientNodeInput {
        salesInvoiceLine?: integer | string;
    }
    export interface IntrastatDeclarationLineToSalesInvoiceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        intrastatDeclarationLine: IntrastatDeclarationLine;
        salesInvoiceLine: SalesInvoiceLine;
    }
    export interface IntrastatDeclarationLineToSalesInvoiceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface IntrastatDeclarationLineToSalesInvoiceLine$Lookups {
        salesInvoiceLine: QueryOperation<SalesInvoiceLine>;
    }
    export interface IntrastatDeclarationLineToSalesInvoiceLine$Operations {
        query: QueryOperation<IntrastatDeclarationLineToSalesInvoiceLine>;
        read: ReadOperation<IntrastatDeclarationLineToSalesInvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<IntrastatDeclarationLineToSalesInvoiceLine>;
            query: AggregateQueryOperation<IntrastatDeclarationLineToSalesInvoiceLine>;
        };
        asyncOperations: IntrastatDeclarationLineToSalesInvoiceLine$AsyncOperations;
        lookups(
            dataOrId: string | { data: IntrastatDeclarationLineToSalesInvoiceLineInput },
        ): IntrastatDeclarationLineToSalesInvoiceLine$Lookups;
        getDefaults: GetDefaultsOperation<IntrastatDeclarationLineToSalesInvoiceLine>;
    }
    export interface MovementRule extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        documentType: IntrastatDocumentType;
        statisticalProcedure: StatisticalProcedure;
        natureOfTransaction: NatureOfTransaction;
        intrastatFlow: IntrastatFlow;
        declarationPeriod: IntrastatDeclarationPeriod;
    }
    export interface MovementRuleInput extends ClientNodeInput {
        _vendor?: integer | string;
        isActive?: boolean | string;
        documentType?: IntrastatDocumentType;
        statisticalProcedure?: integer | string;
        natureOfTransaction?: integer | string;
        intrastatFlow?: IntrastatFlow;
        declarationPeriod?: IntrastatDeclarationPeriod;
    }
    export interface MovementRuleBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        documentType: IntrastatDocumentType;
        statisticalProcedure: StatisticalProcedure;
        natureOfTransaction: NatureOfTransaction;
        intrastatFlow: IntrastatFlow;
        declarationPeriod: IntrastatDeclarationPeriod;
    }
    export interface MovementRule$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MovementRule$Lookups {
        _vendor: QueryOperation<SysVendor>;
        statisticalProcedure: QueryOperation<StatisticalProcedure>;
        natureOfTransaction: QueryOperation<NatureOfTransaction>;
    }
    export interface MovementRule$Operations {
        query: QueryOperation<MovementRule>;
        read: ReadOperation<MovementRule>;
        aggregate: {
            read: AggregateReadOperation<MovementRule>;
            query: AggregateQueryOperation<MovementRule>;
        };
        update: UpdateOperation<MovementRuleInput, MovementRule>;
        updateById: UpdateByIdOperation<MovementRuleInput, MovementRule>;
        asyncOperations: MovementRule$AsyncOperations;
        lookups(dataOrId: string | { data: MovementRuleInput }): MovementRule$Lookups;
        getDefaults: GetDefaultsOperation<MovementRule>;
    }
    export interface NatureOfTransaction extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
    }
    export interface NatureOfTransactionInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        isActive?: boolean | string;
    }
    export interface NatureOfTransactionBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
    }
    export interface NatureOfTransaction$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface NatureOfTransaction$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface NatureOfTransaction$Operations {
        query: QueryOperation<NatureOfTransaction>;
        read: ReadOperation<NatureOfTransaction>;
        aggregate: {
            read: AggregateReadOperation<NatureOfTransaction>;
            query: AggregateQueryOperation<NatureOfTransaction>;
        };
        create: CreateOperation<NatureOfTransactionInput, NatureOfTransaction>;
        getDuplicate: GetDuplicateOperation<NatureOfTransaction>;
        update: UpdateOperation<NatureOfTransactionInput, NatureOfTransaction>;
        updateById: UpdateByIdOperation<NatureOfTransactionInput, NatureOfTransaction>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: NatureOfTransaction$AsyncOperations;
        lookups(dataOrId: string | { data: NatureOfTransactionInput }): NatureOfTransaction$Lookups;
        getDefaults: GetDefaultsOperation<NatureOfTransaction>;
    }
    export interface StatisticalProcedure extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        intrastatFlow: IntrastatFlow;
    }
    export interface StatisticalProcedureInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        isActive?: boolean | string;
        intrastatFlow?: IntrastatFlow;
    }
    export interface StatisticalProcedureBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        intrastatFlow: IntrastatFlow;
    }
    export interface StatisticalProcedure$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface StatisticalProcedure$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface StatisticalProcedure$Operations {
        query: QueryOperation<StatisticalProcedure>;
        read: ReadOperation<StatisticalProcedure>;
        aggregate: {
            read: AggregateReadOperation<StatisticalProcedure>;
            query: AggregateQueryOperation<StatisticalProcedure>;
        };
        create: CreateOperation<StatisticalProcedureInput, StatisticalProcedure>;
        getDuplicate: GetDuplicateOperation<StatisticalProcedure>;
        update: UpdateOperation<StatisticalProcedureInput, StatisticalProcedure>;
        updateById: UpdateByIdOperation<StatisticalProcedureInput, StatisticalProcedure>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: StatisticalProcedure$AsyncOperations;
        lookups(dataOrId: string | { data: StatisticalProcedureInput }): StatisticalProcedure$Lookups;
        getDefaults: GetDefaultsOperation<StatisticalProcedure>;
    }
    export interface DeliveryModeExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        description: string;
        intrastatModeOfTransport: IntrastatModeOfTransport;
    }
    export interface DeliveryModeInputExtension {
        _vendor?: integer | string;
        isActive?: boolean | string;
        id?: string;
        name?: string;
        description?: string;
        intrastatModeOfTransport?: IntrastatModeOfTransport;
    }
    export interface DeliveryModeBindingExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        id: string;
        name: string;
        description: string;
        intrastatModeOfTransport: IntrastatModeOfTransport;
    }
    export interface ItemExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergen>;
        classifications: ClientCollection<ItemClassifications>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPrice>;
        itemSites: ClientCollection<ItemSite>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPrice>;
        capacity: integer;
        landedCostItem: LandedCostItem;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        inStockQuantity: string;
        inTransitStockQuantity: string;
        itemTaxGroup: ItemTaxGroup;
        postingClass: PostingClass;
        storedDimensions: string;
        storedAttributes: string;
        purchaseDocuments: ClientCollection<PurchaseOrderLine>;
        intrastatAdditionalUnit: UnitOfMeasure;
        intrastatAdditionalUnitToStockUnitConversion: string;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
        analyticalData: AnalyticalData;
    }
    export interface ItemInputExtension {
        name?: string;
        description?: string;
        status?: ItemStatus;
        type?: ItemType;
        isBought?: boolean | string;
        isManufactured?: boolean | string;
        isSold?: boolean | string;
        eanNumber?: string;
        commodityCode?: string;
        stockUnit?: integer | string;
        volumeUnit?: integer | string;
        volume?: decimal | string;
        weightUnit?: integer | string;
        weight?: decimal | string;
        density?: decimal | string;
        image?: BinaryStream;
        allergens?: Partial<ItemAllergenInput>[];
        classifications?: Partial<ItemClassificationsInput>[];
        category?: integer | string;
        isStockManaged?: boolean | string;
        isPhantom?: boolean | string;
        isBomRevisionManaged?: boolean | string;
        bomRevisionSequenceNumber?: integer | string;
        isPotencyManagement?: boolean | string;
        isTraceabilityManagement?: boolean | string;
        supplierPrices?: Partial<ItemSupplierPriceInput>[];
        itemSites?: Partial<ItemSiteInput>[];
        suppliers?: Partial<ItemSupplierInput>[];
        customers?: Partial<ItemCustomerInput>[];
        purchaseUnit?: integer | string;
        purchaseUnitToStockUnitConversion?: decimal | string;
        serialNumberManagement?: SerialNumberManagement;
        serialNumberSequenceNumber?: integer | string;
        serialNumberUsage?: SerialNumberUsage;
        salesUnit?: integer | string;
        salesUnitToStockUnitConversion?: decimal | string;
        minimumSalesQuantity?: decimal | string;
        maximumSalesQuantity?: decimal | string;
        currency?: integer | string;
        basePrice?: decimal | string;
        minimumPrice?: decimal | string;
        customerPrices?: Partial<ItemCustomerPriceInput>[];
        capacity?: integer | string;
        landedCostItem?: LandedCostItemInput;
        itemTaxGroup?: integer | string;
        postingClass?: integer | string;
        storedDimensions?: string;
        storedAttributes?: string;
        intrastatAdditionalUnit?: integer | string;
        intrastatAdditionalUnitToStockUnitConversion?: decimal | string;
        _syncTick?: decimal | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        id?: string;
        lotManagement?: LotManagement;
        lotSequenceNumber?: integer | string;
        isExpiryManaged?: boolean | string;
        analyticalData?: integer | string;
    }
    export interface ItemBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        status: ItemStatus;
        isActive: boolean;
        type: ItemType;
        isBought: boolean;
        isManufactured: boolean;
        isSold: boolean;
        eanNumber: string;
        commodityCode: string;
        stockUnit: UnitOfMeasure;
        volumeUnit: UnitOfMeasure;
        volume: string;
        weightUnit: UnitOfMeasure;
        weight: string;
        density: string;
        image: BinaryStream;
        allergens: ClientCollection<ItemAllergenBinding>;
        classifications: ClientCollection<ItemClassificationsBinding>;
        category: ItemCategory;
        isStockManaged: boolean;
        isPhantom: boolean;
        isBomRevisionManaged: boolean;
        bomRevisionSequenceNumber: BomRevisionSequence;
        isPotencyManagement: boolean;
        isTraceabilityManagement: boolean;
        supplierPrices: ClientCollection<ItemSupplierPriceBinding>;
        itemSites: ClientCollection<ItemSiteBinding>;
        suppliers: ClientCollection<ItemSupplier>;
        customers: ClientCollection<ItemCustomer>;
        purchaseUnit: UnitOfMeasure;
        purchaseUnitToStockUnitConversion: string;
        purchaseUnitToStockUnitConversionDedicated: boolean;
        serialNumberManagement: SerialNumberManagement;
        serialNumberSequenceNumber: SequenceNumber;
        serialNumberUsage: SerialNumberUsage;
        useSupplierSerialNumbers: boolean;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversion: string;
        salesUnitToStockUnitConversionDedicated: boolean;
        minimumSalesQuantity: string;
        maximumSalesQuantity: string;
        currency: Currency;
        basePrice: string;
        minimumPrice: string;
        customerPrices: ClientCollection<ItemCustomerPriceBinding>;
        capacity: integer;
        landedCostItem: LandedCostItemBinding;
        landedCostAllocationRule: AllocationRule;
        landedCostAllocationRuleUnit: UnitOfMeasure;
        inStockQuantity: string;
        inTransitStockQuantity: string;
        itemTaxGroup: ItemTaxGroup;
        postingClass: PostingClass;
        storedDimensions: any;
        storedAttributes: any;
        purchaseDocuments: ClientCollection<PurchaseOrderLine>;
        intrastatAdditionalUnit: UnitOfMeasure;
        intrastatAdditionalUnitToStockUnitConversion: string;
        _syncTick: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        id: string;
        lotManagement: LotManagement;
        lotSequenceNumber: SequenceNumber;
        isExpiryManaged: boolean;
        analyticalData: AnalyticalData;
    }
    export interface ItemExtension$Lookups {
        intrastatAdditionalUnit: QueryOperation<UnitOfMeasure>;
    }
    export interface ItemExtension$Operations {
        lookups(dataOrId: string | { data: ItemInput }): ItemExtension$Lookups;
    }
    export interface PurchaseReceiptLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseReceipt;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountCharge>;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLine;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLine>;
        taxes: ClientCollection<DocumentLineTax>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        itemSupplier: ItemSupplier;
        stockTransactionStatus: StockDocumentTransactionStatus;
        supplier: Supplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        amountForLandedCostAllocation: string;
        stockTransactions: ClientCollection<StockTransaction>;
        uiTaxes: string;
        purchaseOrderLine: PurchaseOrderLineToPurchaseReceiptLine;
        purchaseReturnLines: ClientCollection<PurchaseReceiptLineToPurchaseReturnLine>;
        purchaseInvoiceLines: ClientCollection<PurchaseReceiptLineToPurchaseInvoiceLine>;
        numberOfPurchaseInvoiceLines: integer;
        workInProgress: WorkInProgressPurchaseReceiptLine;
        financialSite: Site;
        billBySupplier: Supplier;
        returnedQuantity: string;
        invoicedQuantity: string;
        remainingQuantityToInvoice: string;
        remainingReturnQuantity: string;
        returnedQuantityInStockUnit: string;
        invoicedQuantityInStockUnit: string;
        orderCost: string;
        valuedCost: string;
        returnAddress: Address;
        stockMovements: ClientCollection<StockJournal>;
        actualLandedCostInCompanyCurrency: string;
        discount: string;
        charge: string;
        lineStatus: PurchaseDocumentStatus;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        intrastatDeclarations: ClientCollection<IntrastatDeclarationLine>;
        origin: BaseOrigin;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        quantityInStockUnit: string;
        stockCostAmountInCompanyCurrency: string;
        stockCostAmount: string;
        stockCostUnit: string;
        lineReturnStatus: PurchaseReceiptReturnStatus;
        lineInvoiceStatus: PurchaseReceiptInvoiceStatus;
        remainingQuantityInStockUnit: string;
        stockDetails: ClientCollection<StockReceiptDetail>;
        stockDetailStatus: StockDetailStatus;
        jsonStockDetails: string;
        status: BaseStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        priceOrigin: BasePriceOrigin;
        completed: boolean;
    }
    export interface PurchaseReceiptLineInputExtension {
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        grossPrice?: decimal | string;
        discountCharges?: Partial<DocumentLineDiscountChargeInput>[];
        netPrice?: decimal | string;
        amountExcludingTax?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxDate?: string;
        text?: TextStream;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        landedCost?: LandedCostDocumentLineInput;
        landedCostLines?: Partial<LandedCostLineInput>[];
        taxes?: Partial<DocumentLineTaxInput>[];
        taxAmountAdjusted?: decimal | string;
        stockTransactionStatus?: StockDocumentTransactionStatus;
        purchaseUnit?: integer | string;
        stockTransactions?: Partial<StockTransactionInput>[];
        uiTaxes?: string;
        transientPurchaseOrderLine?: integer | string;
        purchaseOrderLine?: PurchaseOrderLineToPurchaseReceiptLineInput;
        workInProgress?: WorkInProgressPurchaseReceiptLineInput;
        orderCost?: decimal | string;
        valuedCost?: decimal | string;
        returnAddress?: integer | string;
        discount?: decimal | string;
        charge?: decimal | string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        origin?: BaseOrigin;
        unitToStockUnitConversionFactor?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
        quantityInStockUnit?: decimal | string;
        lineReturnStatus?: PurchaseReceiptReturnStatus;
        lineInvoiceStatus?: PurchaseReceiptInvoiceStatus;
        stockDetails?: Partial<StockReceiptDetailInput>[];
        stockDetailStatus?: StockDetailStatus;
        jsonStockDetails?: string;
        status?: BaseStatus;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        priceOrigin?: BasePriceOrigin;
        completed?: boolean | string;
    }
    export interface PurchaseReceiptLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseReceipt;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountChargeBinding>;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLineBinding;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLineBinding>;
        taxes: ClientCollection<DocumentLineTaxBinding>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        itemSupplier: ItemSupplier;
        stockTransactionStatus: StockDocumentTransactionStatus;
        supplier: Supplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        amountForLandedCostAllocation: string;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        uiTaxes: any;
        transientPurchaseOrderLine: integer;
        purchaseOrderLine: PurchaseOrderLineToPurchaseReceiptLineBinding;
        purchaseReturnLines: ClientCollection<PurchaseReceiptLineToPurchaseReturnLine>;
        purchaseInvoiceLines: ClientCollection<PurchaseReceiptLineToPurchaseInvoiceLine>;
        numberOfPurchaseInvoiceLines: integer;
        workInProgress: WorkInProgressPurchaseReceiptLineBinding;
        financialSite: Site;
        billBySupplier: Supplier;
        returnedQuantity: string;
        invoicedQuantity: string;
        remainingQuantityToInvoice: string;
        remainingReturnQuantity: string;
        returnedQuantityInStockUnit: string;
        invoicedQuantityInStockUnit: string;
        orderCost: string;
        valuedCost: string;
        returnAddress: Address;
        stockMovements: ClientCollection<StockJournal>;
        actualLandedCostInCompanyCurrency: string;
        discount: string;
        charge: string;
        lineStatus: PurchaseDocumentStatus;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        intrastatDeclarations: ClientCollection<IntrastatDeclarationLine>;
        origin: BaseOrigin;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        quantityInStockUnit: string;
        stockCostAmountInCompanyCurrency: string;
        stockCostAmount: string;
        stockCostUnit: string;
        lineReturnStatus: PurchaseReceiptReturnStatus;
        lineInvoiceStatus: PurchaseReceiptInvoiceStatus;
        remainingQuantityInStockUnit: string;
        stockDetails: ClientCollection<StockReceiptDetailBinding>;
        stockDetailStatus: StockDetailStatus;
        jsonStockDetails: any;
        status: BaseStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        priceOrigin: BasePriceOrigin;
        completed: boolean;
    }
    export interface PurchaseReturnLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseReturn;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountCharge>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLine;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLine>;
        taxes: ClientCollection<DocumentLineTax>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        lineStatus: PurchaseDocumentStatus;
        supplier: Supplier;
        supplierName: string;
        itemSupplier: ItemSupplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        purchaseUnitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        uiTaxes: string;
        expectedReturnDate: string;
        totalTaxExcludedAmount: string;
        calculatedTotalAmountExcludingTaxInCompanyCurrency: string;
        purchaseReceiptLine: PurchaseReceiptLineToPurchaseReturnLine;
        purchaseInvoiceLines: ClientCollection<PurchaseReturnLineToPurchaseInvoiceLine>;
        workInProgress: WorkInProgressPurchaseReturnLine;
        invoicedQuantity: string;
        invoicedQuantityInStockUnit: string;
        quantityToInvoice: string;
        shippedStatus: PurchaseReturnShippingStatus;
        approvalStatus: PurchaseDocumentApprovalStatus;
        reason: ReasonCode;
        creditedQuantity: string;
        lineCreditStatus: PurchaseReturnCreditStatus;
        purchaseCreditMemoLines: ClientCollection<PurchaseReturnLineToPurchaseCreditMemoLine>;
        remainingQuantityToCredit: string;
        stockAllocations: ClientCollection<StockAllocation>;
        quantityAllocated: string;
        stockTransactions: ClientCollection<StockTransaction>;
        stockDetails: ClientCollection<StockIssueDetail>;
        stockMovements: ClientCollection<StockJournal>;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        intrastatDeclarations: ClientCollection<IntrastatDeclarationLine>;
        origin: BaseOrigin;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        creditedQuantityInStockUnit: string;
        sourceDocumentType: SourceDocumentType;
        quantityInStockUnit: string;
        lineInvoiceStatus: PurchaseReturnInvoiceStatus;
        quantityToInvoiceInStockUnit: string;
        remainingQuantityToAllocate: string;
        status: BaseStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        allocationStatus: StockAllocationStatus;
    }
    export interface PurchaseReturnLineInputExtension {
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        grossPrice?: decimal | string;
        discountCharges?: Partial<DocumentLineDiscountChargeInput>[];
        priceOrigin?: BasePriceOrigin;
        netPrice?: decimal | string;
        amountExcludingTax?: decimal | string;
        taxableAmount?: decimal | string;
        taxAmount?: decimal | string;
        exemptAmount?: decimal | string;
        taxDate?: string;
        text?: TextStream;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        landedCost?: LandedCostDocumentLineInput;
        landedCostLines?: Partial<LandedCostLineInput>[];
        taxes?: Partial<DocumentLineTaxInput>[];
        taxAmountAdjusted?: decimal | string;
        purchaseUnit?: integer | string;
        purchaseUnitToStockUnitConversionFactor?: decimal | string;
        discount?: decimal | string;
        charge?: decimal | string;
        stockTransactionStatus?: StockDocumentTransactionStatus;
        uiTaxes?: string;
        expectedReturnDate?: string;
        totalTaxExcludedAmount?: decimal | string;
        calculatedTotalAmountExcludingTaxInCompanyCurrency?: decimal | string;
        purchaseReceiptLine?: PurchaseReceiptLineToPurchaseReturnLineInput;
        workInProgress?: WorkInProgressPurchaseReturnLineInput;
        shippedStatus?: PurchaseReturnShippingStatus;
        approvalStatus?: PurchaseDocumentApprovalStatus;
        reason?: integer | string;
        stockTransactions?: Partial<StockTransactionInput>[];
        stockDetails?: Partial<StockIssueDetailInput>[];
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        origin?: BaseOrigin;
        unitToStockUnitConversionFactor?: decimal | string;
        amountIncludingTax?: decimal | string;
        amountIncludingTaxInCompanyCurrency?: decimal | string;
        quantityInStockUnit?: decimal | string;
        lineInvoiceStatus?: PurchaseReturnInvoiceStatus;
        status?: BaseStatus;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
    }
    export interface PurchaseReturnLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: PurchaseReturn;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        currency: Currency;
        grossPrice: string;
        discountCharges: ClientCollection<DocumentLineDiscountChargeBinding>;
        priceOrigin: BasePriceOrigin;
        netPrice: string;
        signedQuantity: string;
        amountExcludingTax: string;
        taxableAmount: string;
        taxAmount: string;
        exemptAmount: string;
        taxDate: string;
        text: TextStream;
        signedAmountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        signedAmountExcludingTaxInCompanyCurrency: string;
        canHaveLandedCost: boolean;
        landedCost: LandedCostDocumentLineBinding;
        canHaveLandedCostLine: boolean;
        landedCostLines: ClientCollection<LandedCostLineBinding>;
        taxes: ClientCollection<DocumentLineTaxBinding>;
        taxCalculationStatus: TaxCalculationStatus;
        taxAmountAdjusted: string;
        lineStatus: PurchaseDocumentStatus;
        supplier: Supplier;
        supplierName: string;
        itemSupplier: ItemSupplier;
        purchaseUnit: UnitOfMeasure;
        companyCurrency: Currency;
        company: Company;
        purchaseUnitToStockUnitConversionFactor: string;
        discount: string;
        charge: string;
        stockTransactionStatus: StockDocumentTransactionStatus;
        uiTaxes: any;
        expectedReturnDate: string;
        totalTaxExcludedAmount: string;
        calculatedTotalAmountExcludingTaxInCompanyCurrency: string;
        purchaseReceiptLine: PurchaseReceiptLineToPurchaseReturnLineBinding;
        purchaseInvoiceLines: ClientCollection<PurchaseReturnLineToPurchaseInvoiceLine>;
        workInProgress: WorkInProgressPurchaseReturnLineBinding;
        invoicedQuantity: string;
        invoicedQuantityInStockUnit: string;
        quantityToInvoice: string;
        shippedStatus: PurchaseReturnShippingStatus;
        approvalStatus: PurchaseDocumentApprovalStatus;
        reason: ReasonCode;
        creditedQuantity: string;
        lineCreditStatus: PurchaseReturnCreditStatus;
        purchaseCreditMemoLines: ClientCollection<PurchaseReturnLineToPurchaseCreditMemoLine>;
        remainingQuantityToCredit: string;
        stockAllocations: ClientCollection<StockAllocation>;
        quantityAllocated: string;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        stockDetails: ClientCollection<StockIssueDetailBinding>;
        stockMovements: ClientCollection<StockJournal>;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        intrastatDeclarations: ClientCollection<IntrastatDeclarationLine>;
        origin: BaseOrigin;
        unitToStockUnitConversionFactor: string;
        amountIncludingTax: string;
        amountIncludingTaxInCompanyCurrency: string;
        creditedQuantityInStockUnit: string;
        sourceDocumentType: SourceDocumentType;
        quantityInStockUnit: string;
        lineInvoiceStatus: PurchaseReturnInvoiceStatus;
        quantityToInvoiceInStockUnit: string;
        remainingQuantityToAllocate: string;
        status: BaseStatus;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        allocationStatus: StockAllocationStatus;
    }
    export interface SalesReturnReceiptLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesReturnReceipt;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        originDocumentType: SalesOriginDocumentType;
        orderCost: string;
        valuedCost: string;
        toReturnRequestLines: ClientCollection<SalesReturnRequestLineToSalesReturnReceiptLine>;
        salesShipmentLines: ClientCollection<SalesShipmentLineToSalesReturnReceiptLine>;
        stockDetails: ClientCollection<StockReceiptDetail>;
        jsonStockDetails: string;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransaction>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        intrastatDeclarations: ClientCollection<IntrastatDeclarationLine>;
        origin: BaseOrigin;
        unitToStockUnitConversionFactor: string;
        isReceiptExpected: boolean;
        location: Location;
        stockStatus: StockStatus;
        lot: Lot;
        sourceDocumentType: SourceDocumentType;
        quantityInStockUnit: string;
        stockDetailStatus: StockDetailStatus;
    }
    export interface SalesReturnReceiptLineInputExtension {
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        orderCost?: decimal | string;
        valuedCost?: decimal | string;
        toReturnRequestLines?: Partial<SalesReturnRequestLineToSalesReturnReceiptLineInput>[];
        salesShipmentLines?: Partial<SalesShipmentLineToSalesReturnReceiptLineInput>[];
        stockDetails?: Partial<StockReceiptDetailInput>[];
        jsonStockDetails?: string;
        stockTransactions?: Partial<StockTransactionInput>[];
        stockTransactionStatus?: StockDocumentTransactionStatus;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        origin?: BaseOrigin;
        unitToStockUnitConversionFactor?: decimal | string;
        quantityInStockUnit?: decimal | string;
        stockDetailStatus?: StockDetailStatus;
    }
    export interface SalesReturnReceiptLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesReturnReceipt;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        originDocumentType: SalesOriginDocumentType;
        orderCost: string;
        valuedCost: string;
        toReturnRequestLines: ClientCollection<SalesReturnRequestLineToSalesReturnReceiptLineBinding>;
        salesShipmentLines: ClientCollection<SalesShipmentLineToSalesReturnReceiptLineBinding>;
        stockDetails: ClientCollection<StockReceiptDetailBinding>;
        jsonStockDetails: any;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        stockTransactionStatus: StockDocumentTransactionStatus;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        intrastatDeclarations: ClientCollection<IntrastatDeclarationLine>;
        origin: BaseOrigin;
        unitToStockUnitConversionFactor: string;
        isReceiptExpected: boolean;
        location: Location;
        stockStatus: StockStatus;
        lot: Lot;
        sourceDocumentType: SourceDocumentType;
        quantityInStockUnit: string;
        stockDetailStatus: StockDetailStatus;
    }
    export interface SalesShipmentLineExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesShipment;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        invoiceStatus: SalesDocumentInvoiceStatus;
        returnRequestStatus: SalesDocumentReturnStatus;
        returnReceiptStatus: SalesDocumentReceiptStatus;
        stockTransactionStatus: StockDocumentTransactionStatus;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        salesOrderLines: ClientCollection<SalesOrderLineToSalesShipmentLine>;
        grossPrice: string;
        quantityInvoicedInProgressInSalesUnit: string;
        quantityInvoicedPostedInSalesUnit: string;
        quantityRequestedInSalesUnit: string;
        quantityReceiptInSalesUnit: string;
        stockAllocations: ClientCollection<StockAllocation>;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        grossPriceDeterminated: string;
        stockDetails: ClientCollection<StockIssueDetail>;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransaction>;
        customerNumber: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: string;
        storedAttributes: string;
        computedAttributes: string;
        analyticalData: AnalyticalData;
        intrastatDeclarations: ClientCollection<IntrastatDeclarationLine>;
        unitToStockUnitConversionFactor: string;
        quantityAllocated: string;
        discountCharges: ClientCollection<SalesShipmentLineDiscountCharge>;
        discount: string;
        charge: string;
        netPrice: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        quantityInStockUnit: string;
        remainingQuantity: string;
        remainingQuantityToAllocate: string;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
        allocationStatus: StockAllocationStatus;
    }
    export interface SalesShipmentLineInputExtension {
        origin?: BaseOrigin;
        status?: BaseStatus;
        site?: integer | string;
        siteLinkedAddress?: integer | string;
        item?: integer | string;
        itemDescription?: string;
        stockSite?: integer | string;
        stockSiteLinkedAddress?: integer | string;
        stockUnit?: integer | string;
        unit?: integer | string;
        quantity?: decimal | string;
        internalNote?: TextStream;
        externalNote?: TextStream;
        isExternalNote?: boolean | string;
        invoiceStatus?: SalesDocumentInvoiceStatus;
        returnRequestStatus?: SalesDocumentReturnStatus;
        returnReceiptStatus?: SalesDocumentReceiptStatus;
        stockTransactionStatus?: StockDocumentTransactionStatus;
        salesOrderLines?: Partial<SalesOrderLineToSalesShipmentLineInput>[];
        grossPrice?: decimal | string;
        priceOrigin?: SalesPriceOrigin;
        priceReason?: integer | string;
        priceOriginDeterminated?: SalesPriceOrigin;
        priceReasonDeterminated?: integer | string;
        isPriceDeterminated?: boolean | string;
        grossPriceDeterminated?: decimal | string;
        stockDetails?: Partial<StockIssueDetailInput>[];
        stockTransactions?: Partial<StockTransactionInput>[];
        customerNumber?: string;
        storedDimensions?: string;
        storedAttributes?: string;
        analyticalData?: integer | string;
        unitToStockUnitConversionFactor?: decimal | string;
        discountCharges?: Partial<SalesShipmentLineDiscountChargeInput>[];
        discount?: decimal | string;
        charge?: decimal | string;
        netPrice?: decimal | string;
        amountExcludingTaxInCompanyCurrency?: decimal | string;
        quantityInStockUnit?: decimal | string;
    }
    export interface SalesShipmentLineBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        documentNumber: string;
        documentId: integer;
        document: SalesShipment;
        origin: BaseOrigin;
        status: BaseStatus;
        site: Site;
        siteLinkedAddress: BusinessEntityAddress;
        item: Item;
        itemDescription: string;
        stockSite: Site;
        stockSiteLinkedAddress: BusinessEntityAddress;
        itemSite: ItemSite;
        stockUnit: UnitOfMeasure;
        unit: UnitOfMeasure;
        quantity: string;
        internalNote: TextStream;
        externalNote: TextStream;
        isExternalNote: boolean;
        originDocumentType: SalesOriginDocumentType;
        invoiceStatus: SalesDocumentInvoiceStatus;
        returnRequestStatus: SalesDocumentReturnStatus;
        returnReceiptStatus: SalesDocumentReceiptStatus;
        stockTransactionStatus: StockDocumentTransactionStatus;
        quantityInSalesUnit: string;
        salesUnit: UnitOfMeasure;
        salesUnitToStockUnitConversionFactor: string;
        salesOrderLines: ClientCollection<SalesOrderLineToSalesShipmentLineBinding>;
        grossPrice: string;
        quantityInvoicedInProgressInSalesUnit: string;
        quantityInvoicedPostedInSalesUnit: string;
        quantityRequestedInSalesUnit: string;
        quantityReceiptInSalesUnit: string;
        stockAllocations: ClientCollection<StockAllocation>;
        priceOrigin: SalesPriceOrigin;
        priceReason: CustomerPriceReason;
        priceOriginDeterminated: SalesPriceOrigin;
        priceReasonDeterminated: CustomerPriceReason;
        isPriceDeterminated: boolean;
        grossPriceDeterminated: string;
        stockDetails: ClientCollection<StockIssueDetailBinding>;
        stockMovements: ClientCollection<StockJournal>;
        stockTransactions: ClientCollection<StockTransactionBinding>;
        customerNumber: string;
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        storedDimensions: any;
        storedAttributes: any;
        computedAttributes: any;
        analyticalData: AnalyticalData;
        intrastatDeclarations: ClientCollection<IntrastatDeclarationLine>;
        unitToStockUnitConversionFactor: string;
        quantityAllocated: string;
        discountCharges: ClientCollection<SalesShipmentLineDiscountChargeBinding>;
        discount: string;
        charge: string;
        netPrice: string;
        amountExcludingTax: string;
        amountExcludingTaxInCompanyCurrency: string;
        quantityInStockUnit: string;
        remainingQuantity: string;
        remainingQuantityToAllocate: string;
        stockCostAmountInCompanyCurrency: string;
        grossProfitAmountInCompanyCurrency: string;
        stockCostAmount: string;
        grossProfitAmount: string;
        stockCostUnit: string;
        grossProfit: string;
        allocationStatus: StockAllocationStatus;
    }
    export interface Package {
        '@sage/xtrem-declarations/IntrastatDeclaration': IntrastatDeclaration$Operations;
        '@sage/xtrem-declarations/IntrastatDeclarationLine': IntrastatDeclarationLine$Operations;
        '@sage/xtrem-declarations/IntrastatDeclarationLineToPurchaseCreditMemoLine': IntrastatDeclarationLineToPurchaseCreditMemoLine$Operations;
        '@sage/xtrem-declarations/IntrastatDeclarationLineToPurchaseInvoiceLine': IntrastatDeclarationLineToPurchaseInvoiceLine$Operations;
        '@sage/xtrem-declarations/IntrastatDeclarationLineToSalesCreditMemoLine': IntrastatDeclarationLineToSalesCreditMemoLine$Operations;
        '@sage/xtrem-declarations/IntrastatDeclarationLineToSalesInvoiceLine': IntrastatDeclarationLineToSalesInvoiceLine$Operations;
        '@sage/xtrem-declarations/MovementRule': MovementRule$Operations;
        '@sage/xtrem-declarations/NatureOfTransaction': NatureOfTransaction$Operations;
        '@sage/xtrem-declarations/StatisticalProcedure': StatisticalProcedure$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremDistribution$Package,
            SageXtremFinanceData$Package,
            SageXtremImportExport$Package,
            SageXtremLandedCost$Package,
            SageXtremMailer$Package,
            SageXtremMasterData$Package,
            SageXtremMetadata$Package,
            SageXtremPurchasing$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremSales$Package,
            SageXtremScheduler$Package,
            SageXtremStockData$Package,
            SageXtremStructure$Package,
            SageXtremSynchronization$Package,
            SageXtremSystem$Package,
            SageXtremTax$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-declarations-api' {
    export type * from '@sage/xtrem-declarations-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-distribution-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-finance-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-landed-cost-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-purchasing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-sales-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-stock-data-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-structure-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-synchronization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-tax-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-declarations-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-master-data-api-partial' {
    import type {
        DeliveryModeBindingExtension,
        DeliveryModeExtension,
        DeliveryModeInputExtension,
        ItemBindingExtension,
        ItemExtension,
        ItemExtension$Lookups,
        ItemExtension$Operations,
        ItemInputExtension,
    } from '@sage/xtrem-declarations-api';
    export interface DeliveryMode extends DeliveryModeExtension {}
    export interface DeliveryModeBinding extends DeliveryModeBindingExtension {}
    export interface DeliveryModeInput extends DeliveryModeInputExtension {}
    export interface Item extends ItemExtension {}
    export interface ItemBinding extends ItemBindingExtension {}
    export interface ItemInput extends ItemInputExtension {}
    export interface Item$Lookups extends ItemExtension$Lookups {}
    export interface Item$Operations extends ItemExtension$Operations {}
}
declare module '@sage/xtrem-purchasing-api-partial' {
    import type {
        PurchaseReceiptLineBindingExtension,
        PurchaseReceiptLineExtension,
        PurchaseReceiptLineInputExtension,
        PurchaseReturnLineBindingExtension,
        PurchaseReturnLineExtension,
        PurchaseReturnLineInputExtension,
    } from '@sage/xtrem-declarations-api';
    export interface PurchaseReceiptLine extends PurchaseReceiptLineExtension {}
    export interface PurchaseReceiptLineBinding extends PurchaseReceiptLineBindingExtension {}
    export interface PurchaseReceiptLineInput extends PurchaseReceiptLineInputExtension {}
    export interface PurchaseReturnLine extends PurchaseReturnLineExtension {}
    export interface PurchaseReturnLineBinding extends PurchaseReturnLineBindingExtension {}
    export interface PurchaseReturnLineInput extends PurchaseReturnLineInputExtension {}
}
declare module '@sage/xtrem-sales-api-partial' {
    import type {
        SalesReturnReceiptLineBindingExtension,
        SalesReturnReceiptLineExtension,
        SalesReturnReceiptLineInputExtension,
        SalesShipmentLineBindingExtension,
        SalesShipmentLineExtension,
        SalesShipmentLineInputExtension,
    } from '@sage/xtrem-declarations-api';
    export interface SalesReturnReceiptLine extends SalesReturnReceiptLineExtension {}
    export interface SalesReturnReceiptLineBinding extends SalesReturnReceiptLineBindingExtension {}
    export interface SalesReturnReceiptLineInput extends SalesReturnReceiptLineInputExtension {}
    export interface SalesShipmentLine extends SalesShipmentLineExtension {}
    export interface SalesShipmentLineBinding extends SalesShipmentLineBindingExtension {}
    export interface SalesShipmentLineInput extends SalesShipmentLineInputExtension {}
}
