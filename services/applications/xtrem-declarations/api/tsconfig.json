{"extends": "../../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": ".", "composite": true}, "include": ["api.d.ts"], "references": [{"path": "../../../../platform/system/xtrem-authorization/api"}, {"path": "../../../../platform/front-end/xtrem-client"}, {"path": "../../../shared/xtrem-master-data/api"}, {"path": "../../xtrem-purchasing/api"}, {"path": "../../xtrem-sales/api"}, {"path": "../../../shared/xtrem-structure/api"}, {"path": "../../../../platform/system/xtrem-system/api"}, {"path": "../../../shared/xtrem-tax/api"}]}