{"@sage/xtrem-declarations": [{"topic": "IntrastatDeclaration/asyncExport/start", "queue": "import-export", "sourceFileName": "intrastat-declaration.ts"}, {"topic": "IntrastatDeclarationLine/asyncExport/start", "queue": "import-export", "sourceFileName": "intrastat-declaration-line.ts"}, {"topic": "IntrastatDeclarationLineToPurchaseCreditMemoLine/asyncExport/start", "queue": "import-export", "sourceFileName": "intrastat-declaration-line-to-purchase-credit-memo-line.ts"}, {"topic": "IntrastatDeclarationLineToPurchaseInvoiceLine/asyncExport/start", "queue": "import-export", "sourceFileName": "intrastat-declaration-line-to-purchase-invoice-line.ts"}, {"topic": "IntrastatDeclarationLineToSalesCreditMemoLine/asyncExport/start", "queue": "import-export", "sourceFileName": "intrastat-declaration-line-to-sales-credit-memo-line.ts"}, {"topic": "IntrastatDeclarationLineToSalesInvoiceLine/asyncExport/start", "queue": "import-export", "sourceFileName": "intrastat-declaration-line-to-sales-invoice-line.ts"}, {"topic": "MovementRule/asyncExport/start", "queue": "import-export", "sourceFileName": "movement-rule.ts"}, {"topic": "NatureOfTransaction/asyncExport/start", "queue": "import-export", "sourceFileName": "nature-of-transaction.ts"}, {"topic": "StatisticalProcedure/asyncExport/start", "queue": "import-export", "sourceFileName": "statistical-procedure.ts"}]}