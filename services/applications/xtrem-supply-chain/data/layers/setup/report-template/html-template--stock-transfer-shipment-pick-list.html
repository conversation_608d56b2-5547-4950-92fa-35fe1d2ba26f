<div>
  {{#each xtremSupplyChain.stockTransferShipment.query.edges }}
  <table class="report-container">
    <thead class="report-header">
      <tr>
        <th class="report-header-cell normal-black">
          <div class="header-info">
            <table>
              <tbody>
                <tr>
                  <td class="column-right">
                    <div>
                      <h1>{{ translatedContent "86c7873d50dd888e68228a2f30d59419" }}</h1>
                      <br />
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="frame">
            <table style="border-style: none">
              <tbody>
                <tr>
                  <td class="column-left">
                    <span class="strong-theme">{{ translatedContent "17db2f1bf1aaeee084ae495c5ea362bb" }}</span>
                    {{node.number}}
                  </td>
                  <td class="column-left">
                    <span class="strong-theme">{{ translatedContent "a3a79b28bc45b7fd393effd056b917e1" }}</span>
                    {{formatDate node.date}}
                  </td>
                </tr>
                <tr style="border-bottom: none">
                  <td class="column-left" style="border-bottom: none">
                    <span class="strong-theme">{{ translatedContent "1549a8f3c91b29a6ab37605ced6eacb3" }}</span>
                  </td>
                  <td class="column-left">
                    <span class="strong-theme">{{ translatedContent "f7dd60c481d8519afc248df571fa88b7" }}</span>
                    {{node.receivingSite.id}}
                  </td>
                </tr>
                <tr>
                  <td class="column-left">
                    <strong>{{node.receivingSite.name}}</strong>
                    <br />
                    {{#with node.receivingSite.primaryAddress}} {{addressLine1}}<br />
                    {{#if addressLine2}}{{addressLine2}}<br />{{/if}} {{#if (is country.iso31661Alpha3 "GBR")}}{{city}}{{#if region}} {{region}}{{/if}}
                    {{postcode}}<br />
                    {{else}} {{#if (is country.iso31661Alpha3 "ZAF")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
                    {{else}} {{#if (is country.iso31661Alpha3 "USA")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
                    {{else}} {{postcode}} {{city}}<br />
                    {{/if}} {{/if}} {{/if}} {{country.name}} {{/with}}
                  </td>
                  <td class="column-left">
                    <span class="strong-theme">{{ translatedContent "0f55fedcee89c8d9407351716835247e" }}</span>
                    {{node.deliveryMode.description}}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </th>
      </tr>
    </thead>
    <tfoot class="report-footer"></tfoot>

    <tbody class="report-content">
      <tr>
        <td class="report-content-cell">
          <div class="main">
            <table class="lines-table">
              <thead>
                <tr>
                  <th class="column-left">{{ translatedContent "7d74f3b92b19da5e606d737d339a9679" }}</th>
                  <th class="column-left">{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</th>
                  <th class="column-left">{{ translatedContent "bb50048d8dc3944441110736f4bb3146" }}</th>
                  <th class="column-left">{{ translatedContent "551ac9c28499058b0a8af63d3b2a3a6d" }}</th>
                  <th class="column-left">{{ translatedContent "465960461e87b78cdffeedd8d1930d69" }}</th>
                </tr>
              </thead>
              <tbody>
                {{#each node.lines.query.edges}}
                <tr>
                  <td class="column-left" width="15%">{{node.item.id}}</td>
                  <td class="column-left" width="20%">{{node.itemDescription}}</td>
                  <td class="column-left">{{node.linkToStockTransferOrderLines.query.edges.0.node.to.document.number}}</td>
                  <td class="column-right">{{node.quantity}} {{node.unit.name}}</td>
                  <td class="column-right" width="10%">
                    {{#if node.stockAllocations.queryAggregate.edges}} {{node.stockAllocations.queryAggregate.edges.0.node.values.quantityInStockUnit.sum}}
                    {{node.stockUnit.name}} {{/if}}
                  </td>
                  <td class="column-left">{{node.operation.name}}</td>
                </tr>
                {{#if node.stockAllocations.queryAggregate.edges}}
                <tr></tr>
                <tr>
                  <td></td>
                  <td colspan="5">
                    <table>
                      <thead>
                        <tr>
                          <th class="column-left">{{ translatedContent "ce5bf551379459c1c61d2a204061c455" }}</th>
                          <th class="column-left">{{ translatedContent "eeec6c7a9d2b475c23650b202208b892" }}</th>
                          <th class="column-left">{{ translatedContent "e79c08243e599c00a89a413b1f4a2833" }}</th>
                          <th class="column-left">{{ translatedContent "465960461e87b78cdffeedd8d1930d69" }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        {{#each node.stockAllocations.query.edges}}
                        <tr>
                          <td class="column-left" width="20%">{{node.stockRecord.location.name}}</td>
                          <td class="column-left" width="20%">{{node.stockRecord.lot.id}}</td>
                          <td class="column-left">
                            <span>{{#with (JSONparse node.serialNumberRanges)}}</span>
                            {{#each ranges}} {{this.from}}{{#eq this.from this.to}}{{else}} - {{this.to}} ({{this.quantity}}){{/eq}}<br />
                            {{/each}} {{/with}}
                          </td>
                          <td class="column-right" width="12%">{{node.quantityInStockUnit}} {{../node.stockUnit.name}}</td>
                        </tr>
                        {{/each}}
                      </tbody>
                    </table>
                  </td>
                </tr>
                {{/if}} {{/each}}
              </tbody>
            </table>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
  {{/each}}
</div>
