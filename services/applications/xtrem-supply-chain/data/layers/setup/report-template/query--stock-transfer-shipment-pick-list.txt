{
  xtremSupplyChain {
    stockTransferShipment {
      query (filter: "{_id:'{{stockTransferShipment}}'}") {
        edges {
          node {
            number
            receivingSite{
              id
              name
              primaryAddress{
                name
                addressLine1
                addressLine2
                city
                region
                postcode
                country{
                  name
                  iso31661Alpha3
                }
              }
            }
            date #shippingDate
            deliveryMode {
              description
            }
            lines {
              query (first: 500) {
                edges {
                  node {
                    linkToStockTransferOrderLines{
                      query{
                        edges {
                          node {
                            to{
                              document {
                                number
                              }
                            }
                          }
                        }
                      }
                    }
                    item {
                      id
                    }
                    itemDescription
                    quantity
                    unit {
                      name
                    }
                    stockAllocations {
                      queryAggregate {
                        edges {
                          node {
                            group {
                              documentLine {
                                _id
                              }
                            }
                            values {
                              quantityInStockUnit {
                                sum
                              }
                            }
                          }
                        }
                      }
                    }
                    stockAllocations {
                      query {
                        edges {
                          node {
                            quantityInStockUnit
                            serialNumberRanges
                            stockRecord {
                              location {
                                name
                              }
                              lot {
                                id
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
