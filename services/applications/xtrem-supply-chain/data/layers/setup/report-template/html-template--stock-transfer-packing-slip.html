<div>
  {{#with xtremSupplyChain.stockTransferShipment.query.edges.0.node}}
  <table class="report-container">
    <thead class="report-header">
      <tr>
        <th class="normal-black">
          <div class="header-info" style="position: relative; top: -8px">
            <table>
              <tbody>
                <tr>
                  <td class="column-left">
                    <strong>{{stockSite.name}}</strong><br />
                    {{#with stockSite.primaryAddress}} {{addressLine1}}<br />
                    {{#if addressLine2}}{{addressLine2}}<br />{{/if}} {{#if (is country.iso31661Alpha3 "GBR")}}{{city}}{{#if region}} {{region}}{{/if}}
                    {{postcode}}<br />
                    {{else}} {{#if (is country.iso31661Alpha3 "ZAF")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
                    {{else}} {{#if (is country.iso31661Alpha3 "USA")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
                    {{else}} {{postcode}} {{city}}<br />
                    {{/if}} {{/if}} {{/if}} {{country.name}}<br />
                    {{/with}} {{#if stockSite.siret}}<span>{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}</span> {{stockSite.siret}}<br />{{/if}}
                    {{#if stockSite.taxIdNumber}}<span>{{ translatedContent "9484d9e54beb6cc09ea063b26a0b6aa7" }}</span>
                    {{stockSite.taxIdNumber}}{{/if}}
                  </td>
                  <td class="column-right">
                    <div style="position: relative; top: -20px">
                      <h1>{{ translatedContent "a38e0dae8230cf769d39fbbaea91e30f" }}</h1>
                      <br />
                    </div>
                    <div style="text-align: left; padding-left: 300px; position: relative; top: -15px">
                      {{ translatedContent "2a39f204d4eac05604c2059e5ac2f9f7" }}
                      <strong>{{number}}</strong><br />
                      {{ translatedContent "a3a79b28bc45b7fd393effd056b917e1" }}
                      <strong>{{formatDate date}}</strong>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </th>
      </tr>
    </thead>
    <tfoot class="report-footer">
      <tr>
        <td class="report-footer-cell">
          <div class="footer-info">
            <table class="header-table">
              <tbody>
                <tr>
                  <td class="column-center">
                    <strong
                      >{{#if stockSite.legalCompany.legalForm}}<span style="text-transform: uppercase">{{stockSite.legalCompany.legalForm}}</span>
                      {{/if}}{{stockSite.legalCompany.name}}</strong
                    >{{#if stockSite.legalCompany.rcs}} {{stockSite.legalCompany.rcs}}{{/if}}{{#if stockSite.legalCompany.naf}}
                    <span>{{ translatedContent "7137a85d9b8d9e6d09f47767d2ca31ee" }}</span>
                    {{stockSite.legalCompany.naf}}{{/if}}<br />
                    {{#with stockSite.legalCompany.addresses.query.edges.0.node}} {{addressLine1}} {{#if addressLine2}}{{addressLine2}}{{/if}} {{#if (is
                    country.iso31661Alpha3 "GBR")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}} {{else}} {{#if (is country.iso31661Alpha3
                    "ZAF")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}} {{else}} {{#if (is country.iso31661Alpha3 "USA")}}{{city}}{{#if region}}
                    {{region}}{{/if}} {{postcode}} {{else}} {{postcode}} {{city}} {{/if}} {{/if}} {{/if}} {{country.name}} {{/with}}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div>
            {{#if externalNote.value}}
            <table style="width: 100%">
              <tbody>
                <tr>
                  <td style="text-align: center; width: 5%; border: 0">
                    <p><b>{{ translatedContent "fc9d3df613063ee57906c461320744e2" }}</b></p>
                  </td>
                  <td style="text-align: left; border: 0">{{{externalNote.value}}}</td>
                </tr>
              </tbody>
            </table>
            {{/if}}
          </div>
        </td>
      </tr>
    </tfoot>

    <tbody class="report-content">
      <tr>
        <td class="report-content-cell">
          <div class="main">
            <div class="address-frame"></div>
            <div style="position: relative; top: -10px">
              <table>
                <thead>
                  <tr>
                    <th class="column-left"></th>
                    <th class="column-left">{{ translatedContent "1549a8f3c91b29a6ab37605ced6eacb3" }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td style="width: 60%"></td>
                    <td>
                      <strong>{{receivingSite.name}}</strong>
                      <br />
                      {{#with receivingSite.primaryAddress}} {{addressLine1}}<br />
                      {{#if addressLine2}}{{addressLine2}}<br />{{/if}} {{#if (is country.iso31661Alpha3 "GBR")}}{{city}}{{#if region}} {{region}}{{/if}}
                      {{postcode}}<br />
                      {{else}} {{#if (is country.iso31661Alpha3 "ZAF")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
                      {{else}} {{#if (is country.iso31661Alpha3 "USA")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
                      {{else}} {{postcode}} {{city}}<br />
                      {{/if}} {{/if}} {{/if}} {{country.name}} {{/with}}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div style="position: relative; top: 10px">
              <div>
                <p style="text-align: left"></p>
              </div>
            </div>

            <div style="position: relative; top: 5px"><br /></div>
            <table>
              <thead>
                <tr>
                  <th class="column-left">{{ translatedContent "0c4f82ac8c388cb72b1ba684f10fa098" }}</th>
                  <th class="column-left">{{ translatedContent "201bdb87f8e278b943d07ae924d5de6e" }}</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="column-left">{{formatDate deliveryDate}}</td>
                  <td class="column-left">{{deliveryMode.name}}</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div style="position: relative; top: 5px"><br /></div>
          <table class="lines-table">
            <thead>
              <tr>
                <th class="column-left">{{ translatedContent "4049d979b8e6b7d78194e96c3208a5a5" }}</th>
                <th class="column-left">{{ translatedContent "7d74f3b92b19da5e606d737d339a9679" }}</th>
                <th class="column-left">{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</th>
                <th class="column-left">{{ translatedContent "19c562a36aeb455d09534f93b4f5236f" }}</th>
                <th class="column-right">{{ translatedContent "694e8d1f2ee056f98ee488bdc4982d73" }}</th>
              </tr>
            </thead>
            <tbody>
              {{#each lines.query.edges}}
              <tr>
                <td class="column-left">{{node.linkToStockTransferOrderLines.query.edges.0.node.to.document.number}}</td>
                <td class="column-left">{{node.item.id}}</td>
                <td class="column-left">{{node.itemDescription}}</td>
                <td class="column-left">{{node.unit.name}}</td>
                <td class="column-right">{{formatNumber node.quantity 2}}</td>
              </tr>
              {{#if node.externalNote.value}}
              <tr>
                <td class="column-center" style="text-align: center; border: 0">
                  <p><b>{{ translatedContent "fc9d3df613063ee57906c461320744e2" }}</b></p>
                </td>
                <td class="column-left" colspan="9" style="text-align: left; border: 0">{{{node.externalNote.value}}}</td>
              </tr>
              {{/if}} {{/each}}
            </tbody>
          </table>
        </td>
      </tr>
    </tbody>
  </table>
  {{/with}}
</div>
