{
  xtremSupplyChain {
    stockTransferShipment {
      query (filter: "{_id:'{{stockTransferShipment}}'}") {
        edges {
          node {
            number
            status
            deliveryDate
            externalNote {
              value
            }
            stockSite{
              name
              primaryAddress{
                name
                addressLine1
                addressLine2
                city
                region
                postcode
                country{
                  name
                  iso31661Alpha3
                }
              }
              legalCompany{
                name
                addresses{
                  query (filter: "{isPrimary:true}") {
                    edges {
                      node {
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        country {
                          name
                          iso31661Alpha3
                        }
                      }
                    }
                  }
                }
                legalForm
                siren
                rcs
                naf
              }
              siret
              taxIdNumber
            }
            receivingSite{
              name
              primaryAddress{
                name
                addressLine1
                addressLine2
                city
                region
                postcode
                country{
                  name
                  iso31661Alpha3
                }
              }
            }
            date #shippingDate
            deliveryMode{
              name
            }
            lines{
              query(first:500){
                edges {
                  node {
										linkToStockTransferOrderLines{
                      query{
                        edges {
                          node {
                            to{
                              document {
                                number
                              }
                            }
                          }
                        }
                      }
                    }
                    item{
                      id
                    }
                    itemDescription
                    unit{
                       name
                    }
                    quantity
                    externalNote {
                      value
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
