@xtrem_supply_chain
Feature:  smoke-test-pr-cd-stock-transfer-order

    Scenario: Stock transfer order creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferOrder"
        Then the "Stock transfer orders" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in from site reference field
        And the user selects the "Shipping site" labelled reference field on the main page
        And the user writes "Canadian site" in the reference field
        And the user selects "Canadian site" in the reference field
        #Fill in to site  reference field
        And the user selects the "Receiving site" labelled reference field on the main page
        And the user writes "Canadian site 2" in the reference field
        And the user selects "Canadian site 2" in the reference field
        #Fill in a text field
        # Put back the following line when header block code reinstated
        # And selects the "general" labelled navigation anchor on the main page
        And the user selects the "Number" labelled text field on the main page
        And the user writes "TO-test" in the text field
        # Put back the following line when header block code reinstated
        # And selects the "lines" labelled navigation anchor on the main page
        #Add a line
        Then the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        #Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "STOFIFO" in the reference field
        And the user selects "STOFIFO" in the reference field
        #Fill in Quantity on sidebar
        And the user selects the "Quantity in stock unit" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        #Select Delivery tab on sidebar
        And selects the "delivery" labelled navigation anchor on the sidebar
        #Click Apply button
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 1 second
        #Click Save Crud Button on main page
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: Stock transfer order Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferOrder"
        Then the "Stock transfer orders" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "TO-test"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "TO-test"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Stock transfer order TO-test" titled page is displayed
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
