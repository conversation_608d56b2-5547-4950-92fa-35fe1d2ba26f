@xtrem_supply_chain
Feature:  smoke-test-pr-cd-stock-transfer-shipment

    Scenario: Stock transfer shipment creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferOrder"
        Then the "Stock transfer orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        # When the user filters the "number" bound column in the table field with value "TOTEST04"
        And the user selects the row with text "TOTEST04" in the "Number" labelled column header of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "TOTEST04"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        #cfreate shipment
        And the user clicks the "Create shipment" labelled business action button on the main page
        And the user waits for 2 seconds
        And the user clicks the "Create" button of the Confirm dialog
        #Verify Creation
        Then a toast containing text "Transfer shipment created" is displayed
        And the user waits for 2 seconds
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SCM_Number]"

    Scenario: Stock transfer shipment Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferShipment"
        #No navigation panel full width
        Then the "Stock transfer shipments" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the row with text "[ENV_SCM_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
