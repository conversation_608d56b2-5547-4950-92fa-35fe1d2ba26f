@xtrem_supply_chain
Feature:  smoke-test-pr-cd-stock-transfer-receipt

    Scenario: Stock transfer receipt update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferReceipt"
        Then the "Stock transfer receipts" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "TR240001" in the "Number" labelled column header of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "TR240001"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        #Select Notes Tab
        And selects the "Notes" labelled navigation anchor on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        When the user clears the rich text field
        When the user writes "some information ........and more" in the rich text field
        Then the value of the rich text field is "some information ........and more"
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
