@xtrem_supply_chain
Feature: smoke-test-static

    #Case without navigation panel full width
    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                                   | Title                    |
            | @sage/xtrem-supply-chain/StockTransferInTransitInquiry | Stock in transit inquiry |
            | @sage/xtrem-supply-chain/PurchaseOrderPlanning         | Purchase order planning  |
            | @sage/xtrem-supply-chain/WorkOrderPlanning             | Work order planning      |
            | @sage/xtrem-supply-chain/StockTransferShipment         | Stock transfer shipments |
            | @sage/xtrem-supply-chain/StockTransferReceipt          | Stock transfer receipts  |

    # Positive test to ensure that the error dialog is definitely exist and open
    Scenario: sts \ xtrem-intacct \ desktop \ check an error dialog appears when the page does not load
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Wazup"
        And the user waits 4 seconds
        Then an error dialog appears on the main page
    # And no dialogs are displayed

    Scenario Outline: sts \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<NavigationPanelTitle>" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                        | NavigationPanelTitle  | Title                |
            | @sage/xtrem-supply-chain/StockTransferOrder | Stock transfer orders | Stock transfer order |
