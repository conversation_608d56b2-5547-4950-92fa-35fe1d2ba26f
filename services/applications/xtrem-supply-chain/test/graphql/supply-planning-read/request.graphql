{
    xtremSupplyChain {
        supplyPlanning {
            query(filter: "{_id : 2}") {
                edges {
                    node {
                        purchaseRequisitionLine {
                            document {
                                number
                            }
                            _sortValue
                        }
                        mrpResultLine {
                            mrpCalculation {
                                description
                            }
                            _sortValue
                        }
                        type
                        itemSite {
                            site {
                                id
                                legalCompany {
                                    id
                                }
                            }
                            item {
                                id
                            }
                        }
                        purchaseSite {
                            id
                        }
                        supplier {
                            businessEntity {
                                id
                            }
                        }
                        itemSupplier {
                            id
                        }
                        stockQuantity
                        purchaseQuantity
                        purchaseUnit {
                            id
                        }
                        startDate
                        requirementDate
                        grossPrice
                        status
                        suggestionSource
                        suggestionDate
                        suggestionUser {
                            email
                        }
                        workOrderCategory {
                            id
                        }
                        workOrderType
                        workOrderNumber
                        workOrderName
                    }
                }
            }
        }
    }
}
