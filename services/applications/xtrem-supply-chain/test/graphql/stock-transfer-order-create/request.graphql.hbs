mutation {
  xtremSupplyChain {
    stockTransferOrder {
      create(data: {{ inputParameters }} )
        {
        site {
          name
        }
        receivingSite {
          name
        }
        stockSite {
          name
        }
        financialSite {
          name
        }
        number
        status
        displayStatus
        stockTransferOrderDocumentType
        stockTransferOrderFlowType
        approvalStatus
        date
        isPrinted
        isSent
        fxRateDate
        requestedDeliveryDate
        doNotShipBeforeDate
        doNotShipAfterDate
        shippingStatus
        deliveryLeadTime
        isExternalNote
        isTransferHeaderNote
        isTransferLineNote
        isCloseHidden
        companyFxRate
        companyFxRateDivisor
        workDays
        allocationStatus
        allocationRequestStatus
        rateDescription
        shippingDate
        expectedDeliveryDate
        lines {
          query {
            edges {
              node {
                documentNumber
                status
                shippingStatus
                itemDescription
                unitToStockUnitConversionFactor
                requestedDeliveryDate
                deliveryLeadTime
                doNotShipBeforeDate
                doNotShipAfterDate
                shippingDate
                expectedDeliveryDate
                storedDimensions
                storedAttributes
                computedAttributes
                quantityToShipInProgressInStockUnit
                shippedQuantityInStockUnit
                remainingQuantityToShipInStockUnit
                allocationRequestStatus
                isExternalNote
                quantityAllocatedInStockUnit
                remainingQuantityToAllocateInStockUnit
                quantity
                unit {
                  id
                }
                quantityInStockUnit
                stockUnit{
                  id
                }
                allocationStatus
                availableQuantityInStockUnit
                stockOnHand
                stockShortageInStockUnit
                stockShortageStatus
                stockCostAmountInCompanyCurrency
                stockCostAmount
                stockCostUnit
                workInProgress {
                  documentType
                  status
                  startDate
                  endDate
                  expectedQuantity
                  actualQuantity
                  outstandingQuantity
                }
              }
            }
          }
        }
      }
    }
  }
}
