mutation {
  xtremSupplyChain {
    stockTransferReceipt {
      create(data: {{ inputParameters }} ){
        site {
          name
        }
        supplier {
          name
        }
        stockSite {
          name
        }
        financialSite {
          name
        }
        number
        status
        displayStatus
        date
        isPrinted
        isSent
        fxRateDate
        isExternalNote
        isTransferHeaderNote
        isTransferLineNote
        companyFxRate
        companyFxRateDivisor
        rateDescription
        lines {
          query {
            edges {
              node {
                documentNumber
                status
                itemDescription
                unitToStockUnitConversionFactor
                storedDimensions
                storedAttributes
                computedAttributes
                isExternalNote
                quantity
                unit {
                  id
                }
                quantityInStockUnit
                stockUnit{
                  id
                }
                stockCostAmountInCompanyCurrency
                stockCostAmount
                stockCostUnit
              }
            }
          }
        }
      }
    }
  }
}
