{"Create stock transfer receipt - minimal payload": {"executionMode": "normal", "input": {"properties": {"site": "#CAS02", "supplier": "#CAS01", "shippingSite": "#CAS01", "date": "2024-07-08", "lines": [{"item": "#STOFIFO", "quantity": 1}]}}, "output": {"create": {"site": {"name": "Canadian site 2"}, "supplier": {"name": "Canadian site"}, "stockSite": {"name": "Canadian site 2"}, "financialSite": {"name": "Canadian site"}, "number": "TR240002", "status": "readyToProcess", "displayStatus": "readyToProcess", "date": "2024-07-08", "isPrinted": false, "isSent": false, "fxRateDate": "2024-07-08", "isExternalNote": false, "isTransferHeaderNote": false, "isTransferLineNote": false, "companyFxRate": "1", "companyFxRateDivisor": "1", "rateDescription": "1 CAD = 1 CAD", "lines": {"query": {"edges": [{"node": {"documentNumber": "TR240002", "status": "readyToProcess", "itemDescription": "FIFO cost valuation", "unitToStockUnitConversionFactor": "1", "storedDimensions": null, "storedAttributes": null, "computedAttributes": "{\"financialSite\":\"CAS01\",\"stockSite\":\"CAS02\",\"item\":\"STOFIFO\",\"supplier\":\"CAS01\"}", "isExternalNote": false, "quantity": "1", "unit": {"id": "EACH"}, "quantityInStockUnit": "1", "stockUnit": {"id": "EACH"}, "stockCostAmountInCompanyCurrency": "0", "stockCostAmount": "0", "stockCostUnit": "0"}}]}}}}, "envConfigs": {"today": "2024-27-03"}}}