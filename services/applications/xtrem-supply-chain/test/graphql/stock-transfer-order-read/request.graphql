{
    xtremSupplyChain {
        stockTransferOrder {
            query(filter: "{number:'TOTEST01'}") {
                edges {
                    node {
                        status
                        site {
                            name
                        }
                        receivingSite {
                            name
                        }
                        lines {
                            query {
                                edges {
                                    node {
                                        item {
                                            name
                                        }
                                        workInProgress {
                                            expectedQuantity
                                            actualQuantity
                                            outstandingQuantity
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
