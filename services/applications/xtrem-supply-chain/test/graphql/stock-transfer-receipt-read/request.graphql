{
    xtremSupplyChain {
        stockTransferReceipt {
            query(filter: "{number:'TRTEST01'}") {
                edges {
                    node {
                        status
                        site {
                            name
                        }
                        shippingSite {
                            name
                        }
                        lines {
                            query {
                                edges {
                                    node {
                                        item {
                                            name
                                        }
                                        workInProgress {
                                            expectedQuantity
                                            actualQuantity
                                            outstandingQuantity
                                        }
                                        jsonStockDetails
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
