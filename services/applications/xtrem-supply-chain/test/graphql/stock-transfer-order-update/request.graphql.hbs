mutation {
    xtremSupplyChain {
        stockTransferOrder {
            update(data: {{inputParameters}}) {
                number
                incoterm {
                    id
                }
                deliveryMode {
                    name
                }
                lines {
                    query {
                        edges {
                            node {
                                status
                                workInProgress {
                                  documentType
                                  status
                                  startDate
                                  endDate
                                  expectedQuantity
                                  actualQuantity
                                  outstandingQuantity
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
