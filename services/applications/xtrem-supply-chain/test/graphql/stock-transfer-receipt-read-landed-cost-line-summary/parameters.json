{"Read stock transfer receipt with landed costs. Get summary by landed cost types": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}, "input": {"number": "TR250002"}, "output": {"query": {"edges": [{"node": {"number": "TR250002", "jsonAggregateLandedCostTypes": "[{\"landedCostType\":\"freight\",\"actualCostAmountInCompanyCurrency\":\"14.93\",\"actualAllocatedCostAmountInCompanyCurrency\":\"0\"}]"}}]}}}}