import type { Context, NodeCreateData, date } from '@sage/xtrem-core';
import * as xtremSupply<PERSON>hain from '../../lib';

export async function readTestStockTransferShipment(context: Context, stockTransferShipmentNumber: string) {
    const stockTransferShipment = await context.read(
        xtremSupplyChain.nodes.StockTransferShipment,
        {
            number: stockTransferShipmentNumber,
        },
        { forUpdate: true },
    );
    return stockTransferShipment;
}

export function createStockTransferShipment(
    context: Context,
    skipLines = false,
    sites: {
        site: string | null;
        receivingSite: string | null;
    } = {
        site: '#CAS01',
        receivingSite: '#CAS02',
    },
    shipmentDate?: date | undefined,
    deliveryDate?: date | undefined,
    dataDict: {
        stockSite?: string;
        businessRelation?: string;
        status?: 'readyToProcess' | 'readyToShip' | 'shipped' | 'received' | undefined;
    } = {
        stockSite: sites.site ?? '#CAS01',
        businessRelation: '#CAS01',
        status: 'readyToProcess',
    },
    shipmentLines: NodeCreateData<xtremSupplyChain.nodes.StockTransferShipmentLine>[] = [],
): Promise<xtremSupplyChain.nodes.StockTransferShipment> {
    let lines: NodeCreateData<xtremSupplyChain.nodes.StockTransferShipmentLine>[] = [];

    if (!skipLines && shipmentLines.length === 0) {
        lines = [
            {
                item: '#STOFIFO',
                stockSite: dataDict.stockSite ?? '#CAS01',
                quantity: 1,
            },
        ];
    } else {
        lines = shipmentLines;
    }

    const baseData: NodeCreateData<xtremSupplyChain.nodes.StockTransferShipment> = {
        site: sites.site,
        receivingSite: sites.receivingSite,
        businessRelation: dataDict.businessRelation,
        status: dataDict.status,
        date: shipmentDate,
        deliveryDate,
        lines,
    };

    return context.create(xtremSupplyChain.nodes.StockTransferShipment, baseData);
}
